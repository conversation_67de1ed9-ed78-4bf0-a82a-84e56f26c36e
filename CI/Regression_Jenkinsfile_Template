@Library('eng-ci') _

env.notify_email = "<EMAIL>,<EMAIL>"
env.slack_channel = "all_qa"
env.notifyCulprits = false;

def RUN_FREQUENCY = '0 0 * * 6' //At 12:00 AM, only on Saturday

//Source Schema
def DB_SERVER = "dev30"
def SCHEMA = "gold_owner_01"

env.makeText = "true"

env.destSchema = "dmain_owner_01"
env.destSchemaId = "1001"
env.destdb_server = "dev26"



def String LOGIN_CFG = "/u02/home/<USER>/CFGS/login/login_cidev.cfg"
def String PODS_CFG = ""
def String UNITTEST_CFG = ""

def TARGET = "sanity,regression"
env.LEVEL = "0"
env.TestPlan = "IA-12293"          //Valid TestPlan ID in Jira ex: IA-4004

env.JOB_REVISION = 0;
env.JOB_NAME = env.JOB_NAME.replaceAll("%2F", "/")
env.BUILD_TAG = env.BUILD_TAG.replaceAll("%2F", "-")
env.WORKSPACE = "/u02/home/<USER>/workspace/${env.JOB_NAME}"
env.BRANCH_NAME = "main"

def PHP_VERSION = "8.2"

def checkoutGITSandbox(String workSpace) {
    timeout(time: 4, unit: 'HOURS') {
        env.WORKSPACE = "$workSpace"
        echo "\u001B[34mSTARTING GIT CHECKOUT \u001B[0m"
        dir(env.WORKSPACE) {
            try {

                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-app.git", branch: "main")
                env.JOB_REVISION = getGitRevision(env.WORKSPACE)
                currentBuild.displayName = "#${env.BUILD_NUMBER}-${env.BRANCH_NAME} rev=${env.JOB_REVISION}"

                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-externals.git", folderName: "externals", branch: "main")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwtester.git", folderName: "gwtester", branch: "main")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-quality.git", folderName: "jenkins", branch: "main", sparsepath: "tools/jenkins")

                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-controlfile.git", folderName: "gwdata/controlfile", branch: "${env.BRANCH_NAME}")

                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-sanity.git", folderName: "gwdata/sanity", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-apar.git", folderName: "gwdata/apar", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-api.git", folderName: "gwdata/api", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-gl.git", folderName: "gwdata/gl", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-scm.git", folderName: "gwdata/scm", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-contract.git", folderName: "gwdata/contract", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-cre.git", folderName: "gwdata/cre", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-regression.git", folderName: "gwdata/regression", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-sfdc.git", folderName: "gwdata/sfdc", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-appfoundation.git", folderName: "gwdata/appfoundation", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-cm.git", folderName: "gwdata/cm", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-igc.git", folderName: "gwdata/igc", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-vat.git", folderName: "gwdata/vat", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-project.git", folderName: "gwdata/project", branch: "${env.BRANCH_NAME}")
                checkoutSandbox(url: "ssh://******************:443/Intacct/ia-gwdata-platform.git", folderName: "gwdata/platform", branch: "${env.BRANCH_NAME}")

                echo "\u001B[32m GIT CHECKOUT SUCCEEDED \u001B[0m"
            } catch (Exception e) {
                echo "\u001B[31m GIT CHECKOUT FAILED \u001B[0m"
                println "Error:" + e
                currentBuild.result = 'FAILURE'
                notify("SLACK", currentBuild.result)
                notify("MAIL", currentBuild.result)
                sh 'exit 1'
            }
        }
    }
}

ansiColor('xterm') {
    timestamps {
        def Tests = [:]
        node('PHP82') {
            properties([
                    disableConcurrentBuilds(),
                    buildDiscarder(logRotator(artifactDaysToKeepStr: '3', artifactNumToKeepStr: '4', daysToKeepStr: '30', numToKeepStr: '30')), [$class: 'ScannerJobProperty', doNotScan: false],
                    pipelineTriggers([pollSCM(RUN_FREQUENCY)])
            ])

            try {
                stage("Checkout") {
                    checkoutGITSandbox(env.WORKSPACE)
                }
                stage("Build") {
                    buildSandbox(env.WORKSPACE, LOGIN_CFG, PODS_CFG, UNITTEST_CFG)
                }
               
    			stage("DB_Migration") {
				sh """ /u02/home/<USER>/scripts/autodb_migration.sh ${env.destSchema} ${env.destdb_server} """
	    		}

                stage("API Tests") {
                    runAPITests(PHP_VERSION, DB_SERVER, SCHEMA, TARGET)
                }
                stage("Push to JIRA") {
                    pushResultsToJira(env.TestPlan)
                }
                stage("Cleanup") {
                    doCleanup(env.WORKSPACE)
                }
            } catch (all) {
                currentBuild.result = 'FAILURE'
            }
        }
    }
}
