name: Jenkinsfile Branch Deletion
on:
   delete:
jobs:
  JenkinsfileBranch:
    if: "! startsWith(github.event.ref, 'Feb') && ! startsWith(github.event.ref, 'May') && ! startsWith(github.event.ref, 'Aug') && ! startsWith(github.event.ref, 'Nov') && ! startsWith(github.event.ref, 'release')"
    #runs-on: ubuntu-20.04
    runs-on: self-hosted

    steps:                   
      - name: Repository Dispatch
        uses: peter-evans/repository-dispatch@v3.0.0
        with:
          token: ${{ secrets.GITHUBS_TOKEN }}
          repository: Intacct/ia-ci
          event-type: delete-branch
          client-payload: '{"ref": "${{ github.event.ref }}"}'
