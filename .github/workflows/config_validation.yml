name: Validate configs

on:
  pull_request:
    types: [opened, synchronize, reopened, labeled]

jobs:
  check-files:
      runs-on: ubuntu-latest
      outputs:
        should-proceed: ${{ steps.determine-proceed.outputs.should-proceed }}
      steps:
        - name: Check for config changes
          id: determine-proceed
          shell: bash
          run: |
            PR_NUMBER=${{ github.event.pull_request.number }}
            echo "Fetching files changed in PR #$PR_NUMBER"
  
            # Use GitHub REST API to get the list of files in the PR
            FILES=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                "https://api.github.com/repos/intacct/ia-app/pulls/$PR_NUMBER/files" \
                | jq -r '.[].filename')
  
            echo "Changed files: $FILES"
  
            # Check if any file ends with api.yaml and has openapisec in its path
            FOUND=false
            for FILE in $FILES; do
              if [[ $FILE == *"common"* && $FILE == *".cfg" ]]; then
                FOUND=true
                break
              fi
            done
  
            if [ "$FOUND" = true ]; then
              echo "Relevant config file found."
              echo "::set-output name=should-proceed::true"
            else
              echo "No relevant config files found. Exiting gracefully."
              echo "::set-output name=should-proceed::false"
            fi

  build_and_run:
    needs: check-files
    runs-on: ubuntu-latest
    if: needs.check-files.outputs.should-proceed == 'true'
    steps:
      - name: Checkout ia-app repository
        uses: actions/checkout@v4
        with:
          path: ia-app
          ref: ${{ github.head_ref }}
      - name: Checkout ia-tools-eng repository
        id: git_clone
        env:
          GH_TOKEN: ${{ secrets.GITHUBS_TOKEN }}
        run: |
          echo "Cloning -> main ia-tools-eng"
          git clone https://jenkins-sageintacct:${{ secrets.GITHUBS_TOKEN }}@github.com/intacct/ia-tools-eng.git --branch main ia-tools-eng

      - name: Run config_validation.py
        id: validate
        run: |
          PR_NUMBER=${{ github.event.pull_request.number }}
          OUTPUT=$(python -W ignore ia-tools-eng/python/config_validation.py ia-app/app/source/common 2>&1)
          echo "OUTPUT<<EOF" >> $GITHUB_ENV
          echo "$OUTPUT" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

          if echo "$OUTPUT" | grep -q "has issues"; then
            echo "Configuration issues detected."
            exit 1
          fi

        continue-on-error: true
      
      - name: Add 'Invalid_config_changes' label
        if: steps.validate.outcome == 'failure' 
        uses: buildsville/add-remove-label@v2.0.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }} 
          label: Invalid_config_changes
          type: add

      - name: Remove 'Invalid_config_changes' label
        if: steps.validate.outcome == 'success' 
        uses: buildsville/add-remove-label@v2.0.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }} 
          label: Invalid_config_changes
          type: remove

      - name: Check for Valid Label
        if: steps.validate.outcome == 'failure' 
        uses: marocchino/sticky-pull-request-comment@v2.9.0
        with:
          header: Failed
          recreate: true
          message: |
            !! 🚨 PR contains Invalid config Changes 🚨  !!
            Check the details below:
    
            ```
            ${{ env.OUTPUT }}
            ```
      - name: Remove Invalid CONFIG Changes Message
        if: steps.validate.outcome == 'success' 
        uses: marocchino/sticky-pull-request-comment@v2.9.0
        with:
          header: Failed
          recreate: true
          message: |
            ✅ PR has been validated successfully.
