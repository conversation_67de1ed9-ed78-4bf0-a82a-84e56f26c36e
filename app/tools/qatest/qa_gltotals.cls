<?php
require_once 'Util_DataRecordFormatter.cls';

global $query1, $query2;
$query = &Request::$r->query;
$query = "
      SELECT b.cny#, 
bkj.bookid, 
b.account#, 
nvl(b.location#, 0) location#, 
nvl(b.dept#, 0) dept#,
                acct_utils.computedatetoperiod (b.cny#, b.entry_date) timeperiod,
                b.basecurr as currency,
                nvl(b.customerdimkey, 0) customerdimkey,
		nvl(b.projectdimkey, 0) projectdimkey,
		nvl(b.vendordimkey, 0) vendordimkey,
		nvl(b.employeedimkey, 0) employeedimkey,
		nvl(b.itemdimkey, 0) itemdimkey,
		nvl(b.classdimkey, 0) classdimkey,
               nvl(b.custdim1, 0) custdim1,
               nvl(b.custdim2, 0) custdim2,
                nvl(b.custdim3, 0) custdim3,
                nvl(b.custdim4, 0) custdim4,
                nvl(b.custdim5, 0) custdim5,
		SUM (b.tr_type * b.amount) amount,
                SUM (DECODE (b.adj, 'F', DECODE (b.tr_type, 1, b.amount, 0), 0) ) as debit,
                SUM (DECODE (b.adj, 'F', DECODE (b.tr_type, -1, b.amount, 0), 0) ) as credit,
                SUM (DECODE (b.adj, 'T', DECODE (b.tr_type, 1, b.amount, 0), 0) ) as adjdebit,
                SUM (DECODE (b.adj, 'T', DECODE (b.tr_type, -1, b.amount, 0), 0) ) as adjcredit
      FROM	glentry b, glbatch gb, bookjournals bkj
      WHERE	b.cny# = :1
		AND gb.cny# = :1
		AND gb.record# = b.batch#
		AND bkj.cny# = :1
		AND bkj.journalkey = gb.journal#
        AND " . GLBatchManager::getPostStateQryFilter('gb', true, 'b') . "
      GROUP BY	b.cny#,
		bkj.bookid,
		b.account#,
		NVL (b.location#, 0),
		NVL (b.dept#, 0),
		acct_utils.computedatetoperiod (b.cny#, b.entry_date),
		b.basecurr,
		nvl(b.customerdimkey, 0),
		nvl(b.projectdimkey, 0),
		nvl(b.vendordimkey, 0),
		nvl(b.employeedimkey, 0),
		nvl(b.itemdimkey, 0),
		nvl(b.classdimkey, 0),
                nvl(b.custdim1, 0),
                nvl(b.custdim2, 0),
                nvl(b.custdim3, 0),
                nvl(b.custdim4, 0),
                nvl(b.custdim5, 0)
      UNION ALL
      SELECT	b.cny#, 
 bkj.bookid, 
 b.account#, 
 NVL (b.location#, 0) location#,
                NVL (b.dept#, 0) dept#,
                acct_utils.computedatetoperiod (b.cny#, b.entry_date) as timeperiod,
                b.currency,
                nvl(b.customerdimkey, 0) customerdimkey,
		nvl(b.projectdimkey, 0) projectdimkey,
		nvl(b.vendordimkey, 0) vendordimkey,
		nvl(b.employeedimkey, 0) employeedimkey,
		nvl(b.itemdimkey, 0) itemdimkey,
		nvl(b.classdimkey, 0) classdimkey,
                nvl(b.custdim1, 0) custdim1,
                nvl(b.custdim2, 0) custdim2,
                nvl(b.custdim3, 0) custdim3,
                nvl(b.custdim4, 0) custdim4,
                nvl(b.custdim5, 0) custdim5,
		SUM (b.tr_type * b.trx_amount) amount,
                SUM (DECODE (b.adj, 'F', DECODE (b.tr_type, 1, b.trx_amount, 0), 0) ) as debit,
                SUM (DECODE (b.adj, 'F', DECODE (b.tr_type, -1, b.trx_amount, 0), 0) ) as credit,
                SUM (DECODE (b.adj, 'T', DECODE (b.tr_type, 1, b.trx_amount, 0), 0) ) as adjdebit,
                SUM (DECODE (b.adj, 'T', DECODE (b.tr_type, -1, b.trx_amount, 0), 0) ) as adjcredit
      FROM	glentry b, glbatch gb, bookjournals bkj
      WHERE	b.cny# = :1
		AND gb.cny# = :1
		AND gb.record# = b.batch#
		AND bkj.cny# = :1
		AND bkj.journalkey = gb.journal#
		AND b.currency <> b.basecurr
        AND " . GLBatchManager::getPostStateQryFilter('gb', true, 'b') . "
      GROUP BY	b.cny#,
		bkj.bookid,
		b.account#,
		NVL (b.location#, 0),
		NVL (b.dept#, 0),
		acct_utils.computedatetoperiod (b.cny#, b.entry_date),
		b.currency,
		nvl(b.customerdimkey, 0),
		nvl(b.projectdimkey, 0),
		nvl(b.vendordimkey, 0),
		nvl(b.employeedimkey, 0),
		nvl(b.itemdimkey, 0),
		nvl(b.classdimkey, 0),
                nvl(b.custdim1, 0),
                nvl(b.custdim2, 0),
                nvl(b.custdim3, 0),
                nvl(b.custdim4, 0),
                nvl(b.custdim5, 0)";

    $query1 = "select cny#, bookid, account#, location#, dept#, timeperiod, currency, customerdimkey, projectdimkey, vendordimkey, employeedimkey, itemdimkey, classdimkey, amount from basegltotals where cny# = :1 minus  
      SELECT cny#, bookid, account#, location#, dept#, timeperiod, currency, customerdimkey, projectdimkey, vendordimkey, employeedimkey, itemdimkey, classdimkey, amount from (" . $query . ")";

    $query2 = "select a.timeperiod, a.amount, g.acct_no, l.location_no, d.title, a.currency,
cu.customerid, p.projectid, v.vendorid, e.employeeid, i.itemid, cl.classid from (" . $query1 .") a,
company co, glaccount g, location l, department d, customer cu, project p, vendor v, employee e, icitem i, class cl
where a.cny# = co.record#
and a.cny# = g.cny# (+) and a.account# = g.record# (+)
and a.cny# = l.cny# (+) and a.location# = l.record# (+)
and a.cny# = d.cny# (+) and a.dept# = d.record# (+)
and a.cny# = cu.cny# (+) and a.customerdimkey = cu.record# (+)
and a.cny# = p.cny# (+) and a.projectdimkey = p.record# (+)
and a.cny# = v.cny# (+) and a.vendordimkey = v.record# (+)
and a.cny# = e.cny# (+) and a.employeedimkey = e.record# (+)
and a.cny# = i.cny# (+) and a.itemdimkey = i.record# (+)
and a.cny# = cl.cny# (+) and a.classdimkey = cl.record# (+)
order by timeperiod asc";

class qa_gltotals
{
  
    /**
     * Verify GLTotals integrity for a given cny or title
     *
     * @param string  $cny
     * @param string  $title
     */
    public static function verifyGltotals($cny, $title) 
    {

        $cny = qa_util::initDb($cny, $title);

        // What GLTOTALS are NOT correct?
        global $query2;
        $results = QueryResult(array($query2, $cny));
        if (count($results) == 0) {
            qa_logger::addEntry("$cny: OK"); 
        }
        else {
            qa_logger::addEntry("$cny: " . count($results) . " ERRORS");
            foreach($results as $key => $result) {
                $datePeriod = Period2Dates($result['TIMEPERIOD']);
                $results[$key]['TIMEPERIOD'] = date("Y-m", strtotime($datePeriod[0]));
            }
            qa_logger::addEntry(Util_DataRecordFormatter::phpToCsv($results));
        }
    }

}
