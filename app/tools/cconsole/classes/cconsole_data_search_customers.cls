<?php

require_once "cconsole_data_search_base.cls";

/**
 * Searches for customers and practice clients for
 * cconsole_page_search_results.cls.
 *
 * This is where the bulk of the searching takes place. When practice
 * clients are searched, company_id will be a cny#. Otherwise, company_id
 * is a search over the company.title field in the database.
 *
 * @see cconsole_page_search_results
 */
class cconsole_data_search_customers extends cconsole_data_search_base {
    /**
     * Marks whether the parent company should be included in the
     * practice client list.
     *
     * @var bool $include_parent
     */
    public $include_parent;

    /**
     * @var array $allresults
     */
    public $allresults;

    /**
     * @param string $search_by
     * @param bool   $exact
     * @param bool   $cpas
     * @param string $schema
     * @param bool   $include_parent
     *
     * @see cconsole_data_search_base::cconsole_data_search_base()
     */
    function __construct($search_by, $exact = false, $cpas = true, $schema = "", $include_parent = false) {
        parent::__construct($search_by, $exact, $cpas, $schema);

        $this->include_parent = $include_parent;

        $this->columns = array(
            "title"                 => "ID",
            "name"                  => "Name",
            "client_type"           => "Client Type",
            "created"               => "Date Created",
            "bu"                    => "Business Units",
            "funds"                 => "Funds",
            "B"                     => "Business Users",
            "D"                     => "Dashboard Users",
            "S"                     => "CRM Users",
            "E"                     => "Employee Users",
            "H"                     => "Warehouse Users",
            "P"                     => "Approver Users",
            "T"                     => "Platform Users",
            "A"                     => "Payment Users",
            "transstorage"          => "Transaction Storage",
            "docstorage"            => "Document Storage",
            "schema"                => "Schema",
            "type"                  => "Type",
            "tenant_type"           => "Tenant Type",
            "multicurrency_enabled" => "Multi-Currency",
            "platform_enabled"      => "Platform",
            "icrw_model"            => "Interactive CRW model",
            "dv_model"              => "Interactive IVE model",
            "udd_count"             => "UDD Count",
            "order_customer_id"      => "Order Customer ID",
            "contract_customer_id"   => "Contract Customer ID",
        );
    }

    /**
     * Search for a company using company_id.
     *
     * Get all data (relating to the search) in a specific schema.
     *
     * @see    cconsole_data_search_base::search()
     * @see    cconsole_data_search_base::$results
     * @param  string $company_id company.title or practice company cny#
     *
     * @return bool true if at least one company was found else false
     */
    function search_schema($company_id) {
        $this->results = array();

        // if we are searching by practice client then disregard this
        // because $company_id will be a cny# for the practice company
        if (!$this->exact and $this->search_by != PRACTICE_CLIENT) {
            $company_id = "%" . $company_id . "%";
        }

        if ($this->get_companies($company_id)) {
            // find schemas with companies
            $schemas = array();
            foreach ($this->results as $info) {
                $schemas[$info["dbid"]] = $info["dbid"];
            }

            // get additional data
            $this->get_additional_data($company_id, $schemas);
        }

        return (count($this->results) > 0);
    }

    /**
     * Gets the basic information about every company and determines if
     * the company is MED/MES/SE. Also loads name, title, and schema.
     *
     * @param  string $p_company company.title or practice company cny#
     *
     * @access private
     * @return bool true if at least one company was found else false
     */
    function get_companies($p_company) {
        if ($this->search_by === PRACTICE_CLIENT and $this->include_parent) {
            $this->company_sql = sprintf("(%s OR company.record#=:1)", $this->company_sql);
        }

        $sql = "SELECT
                        schemamap.databaseid as dbid,
						company.record# as record#,
						company.title as title,
						company.name as name,
						company2.transstorage as transstorage,
						company.created as created,
						company2.docstorage as docstorage,
						(
							CASE
								WHEN company.record# IN
								(
									SELECT
										cny#
									FROM
										practicecompany
									WHERE
										practicetype='M'
								) THEN 'MED'
								WHEN company.record# IN
								(
									SELECT
										cny#
									FROM
										module
									WHERE
										moduleid='37.ME'
								) THEN 'MES'
								ELSE 'SE'
							END
						) as type,
						company.type as tenant_type,
						NVL((SELECT value FROM modulepref WHERE CNY#= company.record# AND modulekey = '2.GL' AND property = 'ENABLEMULTICURRENCY'  AND LOCATIONKEY is null), 'F') AS multicurrency_enabled,
                        NVL((SELECT value FROM companypref WHERE CNY#= company.record# AND property = 'PLATFORMENABLED'), 'F') AS platform_enabled,
                        NVL((select decode(p.value, 'Y', 'Designer', 'User') from modulepref p, module m
                             where p.cny# = m.cny# and p.modulekey = m.moduleid and m.moduleid = '59.CRW'
                                   and property = 'AUTHOR' and  m.CNY#= company.record#), 'None') AS icrw_model,
                        NVL((select decode(p.value, 'Y', 'Designer', 'User') from modulepref p, module m
                             where p.cny# = m.cny# and p.modulekey = m.moduleid and m.moduleid = '66.DV'
                                   and property = 'AUTHOR' and  m.CNY#= company.record#), 'None') AS dv_model,
                        NVL((SELECT COUNT(1) FROM pt_obj_def WHERE CNY#= company.record# AND properties LIKE '%isGLDimension=1%'), 0) AS udd_count,
                        company.ordercustomerid as order_customerid,
                        company.contractcustomerid as contract_customerid
					FROM
						company,
						company2,
						schemamap
					WHERE
					    company.status = 'T' AND
						" . $this->company_sql . " AND
						company.record#=company2.cny# AND
						company.record# = schemamap.cny#
					ORDER BY
						LOWER(company.title)";

        if ($this->schema) {
            $company_results = DBRunner::runOnDB("QueryResult", array(array($sql, $p_company)), $this->schema);
        } else {
            $company_results = DBRunner::runOnAll("QueryResult", array(array($sql, $p_company)));
            $company_results = DBRunner::mergeRunOnSomeResults($company_results);
        }

        foreach ($company_results as $row) {
            $this->results[$row["RECORD#"]] = array(
                "record#"               => $row["RECORD#"],
                "title"                 => $row["TITLE"],
                "name"                  => $row["NAME"],
                "created"               => $row["CREATED"],
                "docstorage"            => $row["DOCSTORAGE"],
                "transstorage"          => $row["TRANSSTORAGE"],
                "client_type"           => "",
                "type"                  => $row["TYPE"],
                "bu"                    => 0,
                "funds"                 => 0,
                "B"                     => 0,
                "D"                     => 0,
                "S"                     => 0,
                "E"                     => 0,
                "H"                     => 0,
                "P"                     => 0,
                "T"                     => 0,
                "A"                     => 0,
                "AU"                    => 0,
                "schema"                => $this->schema_list[$row["DBID"]],
                "tenant_type"           => $row["TENANT_TYPE"],
                "multicurrency_enabled" => $row["MULTICURRENCY_ENABLED"],
                "platform_enabled"      => $row["PLATFORM_ENABLED"],
                "icrw_model"            => $row["ICRW_MODEL"],
                "dv_model"              => $row["DV_MODEL"],
                "udd_count"             => $row["UDD_COUNT"],
                "order_customer_id"     => $row["ORDER_CUSTOMERID"],
                "contract_customer_id"  => $row["CONTRACT_CUSTOMERID"],
                "dbid"                  => $row["DBID"],
            );
        }

        return (bool)$company_results;
    }

    /**
     * Loads the additional data information for all companies.
     *
     * @param string|int $p_company company.title or console cny#
     * @param array      $p_schemas schemas that contain useful companies
     */
    function get_additional_data($p_company, $p_schemas = array()) {
        $results = array(
            "offerings" => array(),
            "userinfo" => array(),
            "mes" => array(),
            "funds" => array()
        );

        // Loads the offerings information for all companies.
        $sql_offerings =
            "SELECT cny#, clienttype, company.type tenant_type
             FROM company, acctcompany
             WHERE " . $this->company_sql . "
               AND acctcompany.cny# = company.record#";

        // Loads the user count information for all companies.
        $sql_userinfo =
            "SELECT company.record# AS cny#, u.type AS type, COUNT(u.type) AS total
             FROM company, userinfo u
             WHERE " . $this->company_sql . "
               AND company.record# = u.cny#
               AND u.visible = 'T'
               AND u.status != 'F'
             GROUP BY company.record#, u.type";

        // Loads the business units for Multi-Entity Shared and Multi-Entity Distributed rows.
        $sql_business_units =
            "SELECT location.cny# AS cny#, SUM(CASE WHEN location.accountingtype IS NULL OR location.accountingtype != 'Fund' THEN 1 ELSE 0 END) AS bu, SUM(CASE WHEN location.accountingtype = 'Fund' THEN 1 ELSE 0 END) AS funds
             FROM location, company, module
             WHERE company.record# = location.cny#
               AND location.status = 'T'
               AND location.locationtype = 'E'
               AND module.cny# = location.cny#
               AND module.moduleid = '37.ME'
               AND " . $this->company_sql . "
             GROUP BY location.cny#";

        $queries = array($sql_offerings, $sql_userinfo, $sql_business_units);

        if ($this->schema) {
            $query_results = DBRunner::runOnDB("get_queries_results", array($queries, $p_company), $this->schema, true, "cconsole_util.inc");

            $results["offerings"] = array_merge($results["offerings"], $query_results[0]);
            $results["userinfo"] = array_merge($results["userinfo"], $query_results[1]);
            $results["mes"] = array_merge($results["mes"], $query_results[2]);
        } else {
            if (count($p_schemas) > 0) {
                $query_results = DBRunner::runOnSome("get_queries_results", array($queries, $p_company), $p_schemas, null, "cconsole_util.inc");
            } else {
                $query_results = DBRunner::runOnAll("get_queries_results", array($queries, $p_company), null, null, "cconsole_util.inc");
            }

            foreach ($query_results as $query_result) {
                $results["offerings"] = array_merge($results["offerings"], $query_result[0]);
                $results["userinfo"] = array_merge($results["userinfo"], $query_result[1]);
                $results["mes"] = array_merge($results["mes"], $query_result[2]);
            }
        }

        foreach ($results["offerings"] as $row) {
            if (isset($this->results[$row["CNY#"]])) {
                $this->results[$row["CNY#"]]["client_type"] = $GLOBALS["OFFERING_TYPES"][$row["CLIENTTYPE"]];
                $this->results[$row["CNY#"]]["tenant_type"] = $row["TENANT_TYPE"];
            }
        }

        foreach ($results["userinfo"] as $row) {
            if (isset($this->results[$row["CNY#"]])) {
                $this->results[$row["CNY#"]][$row["TYPE"]] = $row["TOTAL"];
            }
        }

        foreach ($results["mes"] as $row) {
            if (isset($this->results[$row["CNY#"]])) {
                $this->results[$row["CNY#"]]["bu"] = $row["BU"];
                $this->results[$row["CNY#"]]["funds"] = $row["FUNDS"];
            }
        }

        $med_search = cconsole_data_search_base::get_search_class(
            PRACTICE_CLIENT,
            $this->exact,
            $this->cpas,
            $this->schema
        );

        // get counts for all MED children and put them into an array
        foreach ($this->results as $cny => $row) {
            if ($row["type"] == "MED") {
                // recurse through all MED children
                $med_search->search($cny);

                $this->results[$cny]["bu"] += count($med_search->results);

                foreach ($med_search->results as $med_client) {
                    $this->results[$cny]["transstorage"] += $med_client["transstorage"];
                    $this->results[$cny]["docstorage"] += $med_client["docstorage"];

                    // sum up all client information for MED
                    // entities unless we are IN a practice
                    // client table (because in the pclient
                    // table we want JUST the MED's info without
                    // it's clients)
                    if ($this->search_by != PRACTICE_CLIENT) {
                        $this->results[$cny]["B"] += $med_client["B"];
                        $this->results[$cny]["D"] += $med_client["D"];
                        $this->results[$cny]["C"] += $med_client["C"];
                        $this->results[$cny]["E"] += $med_client["E"];
                        $this->results[$cny]["P"] += $med_client["P"];
                        $this->results[$cny]["S"] += $med_client["S"];
                    }

                    if ($med_client["type"] == "MED" || $med_client["type"] == "MES") {
                        $this->results[$cny]["bu"] += $med_client["bu"];
                        $this->results[$cny]["funds"] += $med_client["funds"];
                    }
                }
            }
        }
    }
}
