<?
/**
 * @package cconsole
 */

require_once "cconsole_data_details_table.cls";

/**
 * Generates the COUNTS table to be used in the data statistics page.
 *
 * @see cconsole_page_details_data_stats
 */
class cconsole_data_details_stats extends cconsole_data_details_table {
    /**
     * Handles simple count queries for customer, vendor, gl account,
     * gl account group, and dashboards.
     *
     * @param int $company
     */
    function __construct($company) {
        parent::__construct($company);

        $this->columns = array(
            "PROPERTY" => "Property",
            "COUNT"    => "Count"
        );
    }

    function load_glbatch_count()
    {
        SetDBSchema($this->company, "cny#");

        $glbatch_count = QueryResult(
            array(
                "SELECT
						COUNT(record#) AS count
					FROM
						glbatch
					WHERE
						cny#=:1",
                $this->company
            )
        );

        if (count($glbatch_count) > 0) {
            $this->results[] = [
                "PROPERTY" => "GLBatch",
                "COUNT"    => $glbatch_count[0]["COUNT"]
            ];
        }
    }

    function load_ave_glentry_count()
    {
        SetDBSchema($this->company, "cny#");

        $glentry_count = QueryResult(
            array(
                "SELECT
						glentry_count.count / glbatch_count.count AS
							average
					FROM
					(
						SELECT
							COUNT(glbatch.record#) AS count
						FROM
							glbatch
						WHERE
							glbatch.cny#=:1
					) glbatch_count,
					(
						SELECT
							COUNT(glentry.batch#) AS count
						FROM
							glentry
						WHERE
							glentry.cny#=:1
					) glentry_count
					WHERE
						-- to fix a div by 0 fault in select above
						glbatch_count.count > 0",
                $this->company
            )
        );

        if (count($glentry_count) > 0) {
            $average = number_format(
                $glentry_count[0]["AVERAGE"], 2
            );

            $this->results[] = [
                "PROPERTY" => "Average GLEntry Count Per GLBatch",
                "COUNT"    => $average
            ];
        }
    }

    function load_customer_count()
    {
        SetDBSchema($this->company, "cny#");

        $customer_count = QueryResult(
            array(
                "SELECT
						COUNT(customerid) AS count
					FROM
						customer
					WHERE
						cny#=:1",
                $this->company
            )
        );

        if (count($customer_count) > 0) {
            $this->results[] = [
                "PROPERTY" => "Customers",
                "COUNT"    => $customer_count[0]["COUNT"]
            ];
        }
    }

    function load_vendor_count()
    {
        SetDBSchema($this->company, "cny#");

        $vendor_count = QueryResult(
            array(
                "SELECT
						COUNT(vendorid) AS count
					FROM
						vendor
					WHERE
						cny#=:1",
                $this->company
            )
        );

        if (count($vendor_count) > 0) {
            $this->results[] = [
                "PROPERTY" => "Vendors",
                "COUNT"    => $vendor_count[0]["COUNT"]
            ];
        }
    }

    function load_glaccount_count()
    {
        SetDBSchema($this->company, "cny#");

        $glaccount_count = QueryResult(
            array(
                "SELECT
						COUNT(record#) AS count
					FROM
						glaccount
					WHERE
						cny#=:1",
                $this->company
            )
        );

        if (count($glaccount_count) > 0) {
            $this->results[] = [
                "PROPERTY" => "GL Accounts",
                "COUNT"    => $glaccount_count[0]["COUNT"]
            ];
        }
    }

    function load_glaccount_group_count()
    {
        SetDBSchema($this->company, "cny#");

        $glaccount_group_count = QueryResult(
            array(
                "SELECT
						COUNT(record#) AS count
					FROM
						glacctgrp
					WHERE
						cny#=:1",
                $this->company
            )
        );

        if (count($glaccount_group_count) > 0) {
            $this->results[] = [
                "PROPERTY" => "GL Account Groups",
                "COUNT"    => $glaccount_group_count[0]["COUNT"]
            ];
        }
    }

    function load_dashboard_count()
    {
        SetDBSchema($this->company, "cny#");

        $dashboard_count = QueryResult(
            array(
                "SELECT
						COUNT(record#) AS count
					FROM
						userdashboard
					WHERE
						cny#=:1",
                $this->company
            )
        );

        if (count($dashboard_count) > 0) {
            $this->results[] = [
                "PROPERTY" => "Dashboards",
                "COUNT"    => $dashboard_count[0]["COUNT"]
            ];
        }
    }

    function load()
    {
        $this->load_glbatch_count();
        $this->load_ave_glentry_count();
        $this->load_customer_count();
        $this->load_vendor_count();
        $this->load_glaccount_count();
        $this->load_glaccount_group_count();
        $this->load_dashboard_count();
    }
}

