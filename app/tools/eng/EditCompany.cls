<?php
//=============================================================================
//
//	FILE:			EditCompany.cls
//	AUTHOR:			<PERSON><PERSON><PERSON>
//	DESCRIPTION:	the definition for the EditCompany entity
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'show_listing.inc';
require_once 'country.inc';
require_once 'common_cs.js';
require_once 'backend_misc.inc';
require_once 'backend_company.inc';
require_once 'currency.inc';
// require_once 'EditCompanyUtils.cls';
require_once 'TenantLifeCycleManager.cls';
require_once 'crw_util.inc';

class EditCompanyObj
{
    /** @var array $_fieldInfo */
    private $_fieldInfo;
    /** @var string $_entityName */
    private $_entityName;
//    /** @var string $_entityType */
//    private $_entityType;
    /** @var array $_tabInfo */
    private $_tabInfo;
    /** @var string $_companyUpdated */
    private $_companyUpdated;
    // ovi - 24-01-21 - practicetype & affiliation seem unused
//    /** @var array $affiliationArray */
//    var $affiliationArray;
    /** @var  CompanyLogManager $companyManagerLog */
    var $companyManagerLog;
    /** @var IAIssueHandler $gErr */
    var $gErr;
    /** @var ManagerFactory $gManagerFactory */
    var $gManagerFactory;
    /** @var QueryManager $gQueryMgr */
    var $gQueryMgr;
    /** @var resource $dbConn */
    var $dbConn;
    /** @var int $companyId */
    var $companyId;
    /** @var array $co */
    var $co;
    /** @var array $companyInfo */
    var $companyInfo;
    /** @var array $logsInfo */
    var $logsInfo;
    /** @var array $companyInfoCache */
    var $companyInfoCache;
    // ovi - 24-01-21 - practicetype & affiliation seem unused
//    /** @var string $currentAffiliationValue */
//    var $currentAffiliationValue;
//    /** @var  string $practicetype */
//    var $practicetype;
    /** @var array $warnings */
    public $warnings = array();

    /**
     * initialize the variables for this entity
     */
    function __construct() {
        global $gManagerFactory, $_userid;

        // set context
        $_userid = "@" . Request::$r->_r;
        SetDBSchema(Request::$r->_r, "");

        // available admin users
        /** @var UserInfoManager $oUserInfoMgr */
        $oUserInfoMgr = $gManagerFactory->getManager("userinfo");
        $params = array(
            "filters" => array(
                array(
                    array("ADMIN", "=", UserInfoManager::FULL_ADMIN),
                    array("STATUS", "=", "active"),
                    array("UNRESTRICTED", "=", "true"),
                    array("CATEGORY", "NOT IN", array(UserInfoManager::CATEGORY_ANONYMOUS, UserInfoManager::CATEGORY_SUPPORT, UserInfoManager::CATEGORY_SYSTEM))
                ),
            ),
        );
        $users = $oUserInfoMgr->getList($params);
        $admins = array("" => "Select user");
        foreach ($users as $user) {
            $name = trim($user["CONTACTINFO.FIRSTNAME"] . " " . $user["CONTACTINFO.LASTNAME"]);
            $admins[$user["RECORD#"]] = $user["LOGINID"] . ($name !== "" ? " (" . $name . ")" : "");
        }

        // available obi instances
        $allowedInstances = SubsystemDefinition::getAllowedInstancesForCNY(Request::$r->_r);
        if (isset($allowedInstances['msg'])) {
            $this->warnings['ALLOCATE_BIID_INSTANCE'] = $allowedInstances['msg'];
            $allowedInstances = [];
        }

        $scheduledJobsExecutionOffsetValues = [0 => "None", 1 => "+1 hour"];
        for ($i = 2; $i <= 23; $i++) {
            $scheduledJobsExecutionOffsetValues[$i] = '+' . $i . ' hours';
        }

        $this->_fieldInfo = array(
            "Edit Company Info" => array(
                // the field information structure
                array(
                    // the fieldname for the field (used in the form, not required for this field but added as an example)
                    'fieldname' => 'CNY#',
                    // the type of the field
                    'type' => 'integer',
                    // max length for the field (not required for this field but added as an example)
                    'maxlength' => 40,
                    // the fieldlabel for the field to be displayed (will be used in all views)
                    'fieldlabel' => 'CNY#',
                    // 1 -> display in editview and uneditable, 2-> diaply in editview and editable,
                    // 3-> dont display in editview, 4-> display only if value (editable), 5-> display only if value (uneditable)
                    // 6-> don't display the field in view/editview if 'displayfieldcondition' is true (IT NEEDS A 'displayfieldcondition' FIELD!!!!)
                    // 'displayfieldcondition' => 'check something; return bool;'
                    'edit' => 1,
                    // the company eidt is a mix of different objects, so we need a map to figure what is this field a part of
                    'partof' => 'company',
                    // the columnname for the field (one in the database, will be used to fetch the fields' value like company['RECORDNO'])
                    'columnname' => 'RECORDNO',
                ),
                array(
                    "fieldname" => "address1",
                    'type' => 'text',
                    'fieldlabel' => 'Address 1',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'ADDRESS1',
                ),
                array(
                    "fieldname" => "status",
                    'type' => 'text',
                    'fieldlabel' => 'Status',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'STATUS'
                ),
                array(
                    "fieldname" => "address2",
                    'type' => 'text',
                    'fieldlabel' => 'Address 2',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'ADDRESS2',
                ),
                array(
                    'fieldname' => 'title',
                    'type' => 'text',
                    'fieldlabel' => 'Company Title',
                    'edit' => 2,
                    'mandatory' => true,
                    'partof' => 'company',
                    'columnname' => 'TITLE',
                ),
                array(
                    "fieldname" => "city",
                    'type' => 'text',
                    'fieldlabel' => 'City/Town',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'CITY',
                ),
                array(
                    "fieldname" => "name",
                    'type' => 'text',
                    'fieldlabel' => 'Company Name',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'NAME',
                ),
                array(
                    "fieldname" => "zipcode",
                    'type' => 'text',
                    'fieldlabel' => 'Zip/Postal Code',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'ZIPCODE',
                ),
                array(
                    "fieldname" => "legalname",
                    'type' => 'text',
                    'fieldlabel' => 'Legal Name',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALNAME',
                ),
                array(
                    "fieldname" => "stateforus",
                    'type' => 'text',
                    'fieldlabel' => 'State (for US only)',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'STATEFORUS',
                ),
                array(
                    'fieldname' => 'type',
                    'type' => 'pick',
                    'fieldlabel' => 'Type',
                    'values' => array(
                        "production" => COMPANY_TYPE_PRODUCTION_LABEL,
                        "implementation" => COMPANY_TYPE_IMPLEMENTATION_LABEL,
                        "sandbox" => COMPANY_TYPE_SANDBOX_LABEL,
                        "debug" => COMPANY_TYPE_DEBUG_LABEL,
                        "demo" => COMPANY_TYPE_DEMO_LABEL,
                        "sample" => COMPANY_TYPE_SAMPLE_LABEL,
                        "trial" => COMPANY_TYPE_TRIAL_LABEL,
                        "developer" => COMPANY_TYPE_DEVELOPER_LABEL,
                        "salesdemo" => COMPANY_TYPE_SALESDEMO_LABEL,
                        "test" => COMPANY_TYPE_TEST_LABEL,
                        "template" => COMPANY_TYPE_TEMPLATE_LABEL,
                        "archive" => COMPANY_TYPE_ARCHIVE_LABEL,
                        "training" => COMPANY_TYPE_TRAINING_LABEL,
                        "NULL" => COMPANY_TYPE_NULL_LABEL,
                    ),
                    'edit' => 2,
                    'mandatory' => true,
                    'partof' => 'company',
                    'columnname' => 'TYPE',
                ),
                array(
                    "fieldname" => "statefornonus",
                    'type' => 'text',
                    'fieldlabel' => 'State/Province (Non US Countries)',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'STATEFORNONUS',
                ),
                array(
                    "fieldname" => "admin_user_key",
                    "type" => "pick",
                    "values" => $admins,
                    "fieldlabel" => "Administrative User",
                    "edit" => 2,
                    "partof" => "company",
                    "columnname" => "ADMIN_USER_KEY",
                ),
                array(
                    "fieldname" => "country",
                    'type' => 'text',
                    'fieldlabel' => 'Country',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'COUNTRY',
                ),
                array(
                    "fieldname" => "contactname",
                    'type' => 'text',
                    'fieldlabel' => 'Contact Name',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'CONTACTNAME',
                ),
                array(
                    "fieldname" => "countrycode",
                    'type' => 'text',
                    'fieldlabel' => 'Country Code',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'COUNTRYCODE',
                ),
                array(
                    "fieldname" => "contactphone",
                    'type' => 'text',
                    'fieldlabel' => 'Contact Phone',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'CONTACTPHONE',
                ),
                array(
                    "fieldname" => "legaladdress1",
                    'type' => 'text',
                    'fieldlabel' => 'Legal Address1',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALADDRESS1',
                ),
                array(
                    'fieldname' => 'contactemail',
                    'columnname' => 'CONTACTEMAIL',
                    'type' => 'text',
                    'fieldlabel' => 'Contact E-Mail',
                    'edit' => 1,
                    'partof' => 'company',
                ),
                array(
                    "fieldname" => "legaladdress2",
                    'type' => 'text',
                    'fieldlabel' => 'Legal Address2',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALADDRESS2',
                ),
                array(
                    "fieldname" => "fax",
                    'type' => 'text',
                    'fieldlabel' => 'Fax',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'FAX',
                ),
                array(
                    "fieldname" => "legalcity",
                    'type' => 'text',
                    'fieldlabel' => 'Legal City/Town',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALCITY',
                ),
                array(
                    'fieldname' => 'contractcustomerid',
                    'type' => 'text',
                    'fieldlabel' => 'Contract Customer ID',
                    'edit' => 1,
                    'mandatory' => false,
                    'partof' => 'company',
                    'columnname' => 'CONTRACTCUSTOMERID',
                ),
                array(
                    "fieldname" => "legalzipcode",
                    'type' => 'text',
                    'fieldlabel' => 'Legal Zip/Postal Code',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALZIPCODE',
                ),
                array(
                    'fieldname' => 'ordercustomerid',
                    'type' => 'text',
                    'fieldlabel' => 'Order Customer ID',
                    'edit' => 1,
                    'mandatory' => false,
                    'partof' => 'company',
                    'columnname' => 'ORDERCUSTOMERID',
                ),
                array(
                    "fieldname" => "legalstate",
                    'type' => 'text',
                    'fieldlabel' => 'Legal State/Province',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALSTATE',
                ),
                array(
                    "fieldname" => "authcode",
                    'type' => 'text',
                    'fieldlabel' => 'Authorization Code',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'AUTHCODE',
                ),
                array(
                    "fieldname" => "legalcountry",
                    'type' => 'text',
                    'fieldlabel' => 'Legal Country',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALCOUNTRY',
                ),
                array(
                    'fieldname' => 'ipaddressfilter',
                    'type' => 'pick',
                    'fieldlabel' => 'Enforce IP Address Filters',
                    'values' => array(
                        "None" => "None",
                        "Enforce at company level" => "Enforce at company level",
                        "Enforce at company level and override at user level" => "Enforce at company level and override at user level",
                        "Enforce at user level" => "Enforce at user level"
                    ),
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'IPADDRESSFILTER',
                ),
                array(
                    "fieldname" => "legalcountrycode",
                    'type' => 'text',
                    'fieldlabel' => 'Legal Country Code',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LEGALCOUNTRYCODE',
                ),
                array(
                    'fieldname' => 'globalsearchenabled',
                    'type' => 'pick',
                    'values' => array('F' => 'No', 'T' => 'Yes'),
                    'fieldlabel' => 'Is Global Search Enabled',
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'GLOBALSEARCHENABLED',
                ),
                array(
                    "fieldname" => "federalid",
                    'type' => 'text',
                    'fieldlabel' => 'Federal ID',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'FEDERALID',
                ),
                array(
                    'fieldname' => 'emailpoweredbydisabled',
                    'type' => 'pick',
                    'fieldlabel' => 'Is Email Powered By Disabled',
                    'edit' => 2,
                    'values' => array('F' => 'No', 'T' => 'Yes'),
                    'partof' => 'company',
                    'columnname' => 'EMAILPOWEREDBYDISABLED',
                ),
                array(
                    "fieldname" => "notifycnt",
                    'type' => 'text',
                    'fieldlabel' => 'Notify Count',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'NOTIFYCNT',
                ),
                array(
                    'fieldname' => 'auditstorageenabled',
                    'type' => 'pick',
                    'fieldlabel' => 'View new audit trail from storage',
                    'edit' => 2,
                    'values' => array('F' => 'No', 'T' => 'Yes'),
                    'partof' => 'company',
                    'columnname' => 'AUDITSTORAGEENABLED',
                ),
                array(
                    "fieldname" => "failedauthcnt",
                    'type' => 'text',
                    'fieldlabel' => 'Failed Authentication Count',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'FAILEDAUTHCNT',
                ),
/*              array(
                    'fieldname' => 'platformenabled',
                    'type' => 'pick',
                    'fieldlabel' => 'Is Platform Enabled',
                    'edit' => 2,
                    'values' => array('F' => 'No', 'T' => 'Yes'),
                    'partof' => 'company',
                    'columnname' => 'PLATFORMENABLED',
                ),*/
                 array(
                    'fieldname' => 'disableemailvalidation',
                    'type' => 'pick',
                    'values' => array('T' => 'Yes', 'F' => 'No'),
                    'fieldlabel' => 'Email security',
                    'partof' => 'company',
                    'columnname' => 'DISABLEEMAILVALIDATION',
                    'edit' => 2,
                ),
                array(
                    "fieldname" => "moved",
                    'type' => 'text',
                    'fieldlabel' => 'Moved',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'MOVED',
                ),
                array(
                    'fieldname' => 'biid',
                    'type' => 'pick',
                    'values' => array_combine($allowedInstances, $allowedInstances),
                    'fieldlabel' => 'BI Instance Id',
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'BIID',
                ),
                array(
                    "fieldname" => "acctnoseparator",
                    'type' => 'text',
                    'fieldlabel' => 'Account Field Seperator',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'ACCTNOSEPERATOR',
                ),
                array(
                    'fieldname' => 'esid',
                    'type' => 'integer',
                    'fieldlabel' => 'ES Instance Id',
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'ESID',
                ),
                array(
                    "fieldname" => "primacctnolen",
                    'type' => 'text',
                    'fieldlabel' => 'Primary Account # Length',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'PRIMACCTNOLEN',
                ),
                array(
                    'fieldname' => 'logquerydata',
                    'type' => 'limitdate',
                    'values' => array('OFF', 'ON'),
                    'fieldlabel' => 'Log Query Data',
                    'edit' => 6,
                    'partof' => 'company',
                    'columnname' => 'LOGQUERYDATA',
                    'displayfieldcondition' => 'return GetValueForIACFGProperty("LOG_QUERY_DATA")["FEATURE_FLAG"] === "F";', //verify if flag for feature is off
                ),
                array(
                    "fieldname" => "subacctnolen",
                    'type' => 'text',
                    'fieldlabel' => 'SubAccount# Length ',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'SUBACCTNOLEN',
                ),
                array(
                    'fieldname' => 'disableapautomation',
                    'type' => 'checkbox',
                    'fieldlabel' => 'Disable AP Automation',
                    'partof' => 'company',
                    'columnname' => 'DISABLEAPAUTOMATION',
                    'edit' => 2,
                ),
                array(
                    'fieldname' => 'enablecashflowautomation',
                    'type' => 'checkbox',
                    'fieldlabel' => 'Enable Cash Flow Automation',
                    'partof' => 'company',
                    'columnname' => 'ENABLECASHFLOW',
                    'edit' => 2,
                ),
                array(
                    "fieldname" => "logo",
                    'type' => 'img',
                    'fieldlabel' => 'Company Logo',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LOGO',
                ),
                array(
                    'fieldname' => 'enabledigitalnetwork',
                    'type' => 'pick',
                    'values' => array(
                            'F' => 'No',
                            'T' => 'Yes'
                    ),
                    'fieldlabel' => 'Enable Digital Network',
                    'partof' => 'module',
                    'columnname' => 'ENABLEDIGITALSYNC',
                    'edit' => 1,
                ),
                array(
                    'fieldname' => 'enableautomatedtxn',
                    'type' => 'pick',
                    'values' => array(
                        'NONE' => 'Select',
                        'STX' => 'STX',
                        'SAIL' => 'SAIL'
                    ),
                    'fieldlabel' => 'AP Automation Endpoint',
                    'partof' => 'module',
                    'columnname' => 'ENABLEAUTOMATEDTXN',
                    'edit' => 1,
                ),
                array(
                    'fieldname' => 'verbositylevel',
                    'type' => 'limitverbositylevel',
                    'values' => array(LogManager::DEFAULT => 'Default', LogManager::ELEVATED => 'Elevated'),
                    'fieldlabel' => 'Verbosity level',
                    'partof' => 'company',
                    'columnname' => 'VERBOSITYLEVEL',
                    'edit' => 7,
                    'displayfieldcondition' => '$isSlideDisabled = IsSlideEnabled(' . Request::$r->_r . '); 
                                                    return ( isset($isSlideDisabled[0]["STATUS"]) && isset($isSlideDisabled[0]["ISEXPIRED"]) ) 
                                                             ? $isSlideDisabled[0]["STATUS"] === "F" || $isSlideDisabled[0]["ISEXPIRED"] < "0" 
                                                             : true;'
                ),
                array(
                    'fieldname' => 'enabledimdnsync',
                    'type' => 'pick',
                    'values' => array(
                        'F' => 'No',
                        'T' => 'Yes'
                    ),
                    'fieldlabel' => 'Enable Dimensions Digital Network Sync',
                    'partof' => 'module',
                    'columnname' => 'ENABLE_DIMENSIONS_DN_SYNC',
                    'edit' => 1,
                ),
                array(
                    "fieldname" => "ia_billing",
                    "type" => "pick",
                    "values" => array(
                        0 => "Off",
                        1 => "Company, entities & users",
                        2 => "Modules",
                        3 => "Module preferences",
                    ),
                    "fieldlabel" => "Intacct Billing",
                    "edit" => 2,
                    "partof" => "company",
                    "columnname" => "IA_BILLING",
                ),
                array(
                    'fieldname' => 'data_size',
                    'type' => 'text',
                    'fieldlabel' => 'Data Size',
                    'edit' => 1,
                    'mandatory' => false,
                    'partof' => 'company',
                    'columnname' => 'DATA_SIZE',
                ),
                array(
                    'fieldname' => 'attachment_max_upload_size',
                    'type' => 'integer',
                    'fieldlabel' => 'Attachment max upload size (MB) <br /> (Default:  ' . GetValueForIACFGProperty(SupportingDocumentsEditor::IA_ATTACHMENT_DEFAULT_UPLOAD_MAX_SIZE) . ' | No limit: -1)',
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'ATTACHMENT_MAX_UPLOAD_SIZE',
                ),
            ), // "Edit Company Info"
            "Scheduled Jobs" => array(
                array(
                    'fieldname' => 'scheduledjobs',
                    'type' => 'pick',
                    'values' => array('On', 'Off'),
                    'fieldlabel' => 'Scheduled Jobs',
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'SCHEDULEDJOBS',
                ),
                array(
                    'fieldname' => 'scheduledjobsexecutionoffset',
                    'type' => 'pick',
                    'values' =>  $scheduledJobsExecutionOffsetValues,
                    'fieldlabel' => 'Scheduled jobs execution offset',
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'SCHEDULEDJOBSEXECUTIONOFFSET',
                ),
            ),
            "Logs" => array(
                array(
                    "fieldname" => "logs",
                    'type' => 'text',
                    'fieldlabel' => 'Logs',
                    'edit' => 1,
                    'partof' => 'logsInfo',
                    'columnname' => 'LOGS',
                    'notspecial' => true,
                ),
            ),
            "Notes" => array(
                array(
                    'fieldname' => 'bugid',
                    'type' => 'text',
                    'fieldlabel' => 'BugID / SFDC ID',
                    'maxlength' => 30,
                    'edit' => 2,
                    "partof" => null,
                    'mandatory' => Globals::$g->islive,
                    'columnname' => 'BUGID',
                ),
                array(
                    'fieldname' => 'whowanted',
                    'type' => 'text',
                    'fieldlabel' => 'Who wanted the change',
                    'maxlength' => 30,
                    'edit' => 2,
                    "partof" => null,
                    'mandatory' => Globals::$g->islive,
                    'columnname' => 'WHOWANTED',
                ),
                array(
                    'fieldname' => 'notes',
                    'type' => 'textarea',
                    'fieldlabel' => 'Notes',
                    'maxlength' => 380,
                    'edit' => 2,
                    "partof" => null,
                    'mandatory' => Globals::$g->islive,
                    'columnname' => 'NOTES',
                ),
            ),
            "Dates" => array(
                array(
                    "fieldname" => "created",
                    'type' => 'text',
                    'fieldlabel' => 'Created',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'CREATED',
                ),
                array(
                    "fieldname" => "expirationdate",
                    'type' => 'text',
                    'fieldlabel' => 'Expiration Date for Slide in User',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'EXPIRATIONDATE',
                ),
                array(
                    'fieldname' => 'deletedate',
                    'type' => 'date',
                    'fieldlabel' => 'Expiration Date',
                    'edit' => 2,
                    'partof' => 'company',
                    'columnname' => 'DELETEDATE',
                ),
                array(
                    'fieldname' => 'lockdate',
                    'type' => 'date',
                    'fieldlabel' => 'Lock Date',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'LOCKDATE',
                ),
                array(
                    'fieldname' => 'optoutdate',
                    'type' => 'date',
                    'fieldlabel' => 'Opt out Date',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'OPTOUTDATE',
                ),
                array(
                    'fieldname' => 'storagedate',
                    'type' => 'date',
                    'fieldlabel' => 'Storage Date',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'STORAGEDATE',
                ),
                array(
                    'fieldname' => 'terminatedate',
                    'type' => 'date',
                    'fieldlabel' => 'Terminate Date',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'TERMINATEDATE',
                ),
                array(
                    'fieldname' => 'purgedate',
                    'type' => 'date',
                    'fieldlabel' => 'Purge Date',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'PURGEDATE',
                ),
                array(
                    'fieldname' => 'maxlife',
                    'type' => 'date',
                    'fieldlabel' => 'Max Life',
                    'edit' => 1,
                    'partof' => 'company',
                    'columnname' => 'MAXLIFE',
                ),
            ),
        );

        // this label will be printed at the top of the editor UI
        $this->_entityName = CSTOOLS_TOOLNAME_TENANT_EDIT;

        // the type of the entity :: this is the variable which will be passed internally to Tools lister and editor
//        $this->_entityType = 'EditCompany';

        // a little info about each tab
        $this->_tabInfo = array(
            "Edit Company Info" => array("tabid" => "CData", "viewType" => 1, "style" => "visibility: block;"),
            "Scheduled Jobs" => array("tabid" => "Jobs", "viewType" => 1, "style"=>"visibility: none;"),
            "Logs" => array("tabid" => "Logs", "viewType" => 1, "style" => "height: 400px; visibility: none; overflow: auto;"),
            "Dates" => array("tabid" => "Dates", "viewType" => 1, "style" => "visibility: none;"),
            "Notes" => array("tabid" => "Notes", "viewType" => 2, "style" => "visibility: none;"),
        );
    }

    /**
     * set the required global variables in one place :: might want to move to a factory later
     */
    public function setGlobals()
    {
        global $gErr, $gManagerFactory, $gQueryMgr, $conn;

        $this->gErr = $gErr;
        $this->gManagerFactory = $gManagerFactory;
        $this->gQueryMgr = $gQueryMgr;
        $this->dbConn = $conn;
        $this->companyId = Request::$r->_r;
    }

    /**
     * display the page components common for all pages (for e.g. the select dropdown)
     */
    public function showPageCommonLayout() {
        global $_NOJSG;
        $_NOJSG = true;

        Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000');
        $startOverPage = 'EditCompany.phtml';
        PrintCommonComponents("", $startOverPage);
    }

    /**
     * get external user expiration date based on the companyid
     * @return string $expirationDate - the expiration date from the database
     */
    public function getUserExpirationDate()
    {
        $resultSet = QueryResult(array("SELECT expirationdate FROM externassoc WHERE cny# = :1 AND type = :2", $this->companyId, ExternalAccessManager::DB_SUPPORT));
        $expirationDate = $resultSet[0]['EXPIRATIONDATE'];
        return $expirationDate;
    }

    /**
     * Load some of the dates from the company2 table
     */
    protected function loadCompany2Info()
    {
        // Load TenantLifeCycle dates
        $company2Data = QueryResult(array("select * from company2 where cny# = :1", $this->companyId));
        if ( isset($company2Data[0]) ) {
            $company2Data = $company2Data[0];
            $this->companyInfo['PURGEDATE']    = $company2Data['PURGEDATE'];
            $this->companyInfo['LOCKDATE']      = $company2Data['LOCKDATE'];
            $this->companyInfo['OPTOUTDATE']    = $company2Data['OPTOUTDATE'];
            $this->companyInfo['STORAGEDATE']   = $company2Data['STORAGEDATE'];
            $this->companyInfo['TERMINATEDATE'] = $company2Data['TERMINATEDATE'];
            $this->companyInfo['MAXLIFE']       = $company2Data['MAXLIFE'];
        }
    }

    /**
     * this function gets the delete date for the current companyid for the object; it is only called if the company type is demo
     * @return string $deleteDate - the deletion date for the company
     */
    public function getDeletionDate()
    {
        $resultSet = QueryResult(array("select deletedate from company2 where cny# = :1 ", $this->companyId));
        $deleteDate = $resultSet[0]['DELETEDATE'];
        return $deleteDate;
    }

    // ovi - 24-01-21 - practicetype & affiliation seem unused
//    /**
//     * this function sets the affiliation value for the current company
//     */
//    public function setAffiliation()
//    {
//        if($this->companyInfo['praticeType'] == 'C') {
//            $data = getAffiliationTypeValues("Accountant");
//            $this->affiliationArray = array('' => 'Not Defined', $data['type'] => $data['label']);
//            //this field needs to be displayed only for a console companies
//            $this->_fieldInfo['Edit Company Info'][] = array(
//            array('fieldname' => 'currentAffiliation',
//             'type' => 'pick',
//             'fieldlabel' => 'Console Type',
//             'values' => $this->affiliationArray,
//             'edit' => 2,
//             'partof' => 'company',
//             'columnname' => 'AFFILIATION',
//            ),
//            );
//            $this->companyInfo['AFFILIATION'] = $this->affiliationArray[$this->companyInfo['currentAffiliationValue']];
//        }
//    }

    /**
     * this function sets the company info for the current company id
     */
    public function setCompanyInfo() {
        global $gManagerFactory;

        if (Request::$r->_userid !== null) {
            $_userid = &Request::$r->_userid;
        } else {
            global $_userid;
        }

        // check if company has accounting
        $sql =
            "SELECT c.record#, a.cny# acct, p.practicetype, p.affiliation
             FROM company c
               LEFT JOIN acctcompany a ON a.cny# = c.record# AND a.cny# = :1
               LEFT JOIN practicecompany p ON p.cny# = c.record# AND p.cny# = :1
             WHERE c.record# = :1";
        $result = QueryResult(array($sql, $this->companyId));
        if ($result === false) {
            throw new IAException("Can't get accounting info", "BL01000004");
        }
        $company_acct = $result[0];

        // this has to be set before company_info get(), as it uses $_userid to decide what to load
        if ($company_acct["ACCT"] === null) {
            $_userid = "@" . $this->companyId;
        } else {
            $_userid = "@" . $this->companyId . "@A";
        }

        // company info entity data; loads company & company prefs data
        $companyInfoManager = $gManagerFactory->getManager('company_info');
        $this->companyInfo = $companyInfoManager->get('');

        // ovi - 24-01-21 - practicetype & affiliation seem unused
//        $this->companyInfo["practiceType"] = $company_acct["PRACTICETYPE"];
//        $this->companyInfo["currentAffiliationValue"] = $company_acct["AFFILIATION"];

        // entity license
        try {
            $this->companyInfo["LICENSE"] = getCompanyLicense($this->companyId);
        } catch (Exception $ex) {
            global $gErr;
            $gErr->addError($ex->getCode(), $ex->getFile() . '.' . $ex->getLine(), $ex->getMessage());
            include_once "../acct/popuperror.phtml";
            exit;
        }

        // we dont have a valid profile session so we need to handle the same way company info manager does
        if (!isset($this->companyInfo['OBJCURRENCYREC'])) {
            $oCurrMgr = $gManagerFactory->getManager('trxcurrencies');
            $base_currency = $oCurrMgr->GetBaseCurrencyInfo();
            $this->companyInfo['OBJCURRENCYREC'] = isset($base_currency) ? join("@", $base_currency) : null;
        }

        if (!isset($this->companyInfo['IPADDRESSFILTER'])) {
            $this->companyInfo['IPADDRESSFILTER'] = 'None';
        }

        // set type, expiration date, deletedate
        $this->companyInfo['EXPIRATIONDATE'] = $this->getUserExpirationDate();
        $this->companyInfo['DELETEDATE'] = $this->getDeletionDate();

        // company2 table data
        $this->loadCompany2Info();

        // company table data
        $oCompanyManager = $gManagerFactory->getManager("company");
        $this->co = $oCompanyManager->tGet($this->companyId);

        // get administrative user contact data
        if ($this->co["ADMIN_USER_KEY"] !== null) {
            $user = UserInfoManager::getRowByRecordNo($this->co["ADMIN_USER_KEY"], true);

            $oUserInfoManager = $gManagerFactory->getManager("userinfo");
            $user_info = $oUserInfoManager->get($user["LOGINID"]);

            $this->co["CONTACTNAME"] = $user_info["CONTACTINFO"]["CONTACTNAME"];
            $this->co["CONTACTPHONE"] = $user_info["CONTACTINFO"]["PHONE1"];
            $this->co["CONTACTEMAIL"] = $user_info["CONTACTINFO"]["EMAIL1"];
            $this->co["FAX"] = $user_info["CONTACTINFO"]["FAX"];
        }

        $this->companyInfo = array_merge($this->companyInfo, $this->co);
        // save old values for future usage
        $this->companyInfoCache = $this->companyInfo;
    }

    /**
     * this function displays the detail view for the edit company wizard
     */
    public function showDetailView() {
        if (isset($this->warnings['CHANGE_BIID_ERROR'])) { ?>
        <strong style='color: #ff0000'><?=$this->warnings['CHANGE_BIID_ERROR'];?></strong><br/>
        <? }
        if (isset($this->warnings['CHANGE_BIID_SUCCESS'])) { ?>
        <strong style='color: #11ab00'><?= $this->warnings['CHANGE_BIID_SUCCESS'];?></strong><br/>
        <? }
        if (isset($this->warnings["GLOBALSEARCH_INSTANCE_WARN"])) { ?>
        <strong style='color: #ff0000'>Warning - Global Search not enabled: <?=$this->warnings["GLOBALSEARCH_INSTANCE_WARN"];?></strong><br/>
        <? } ?>
        <style>
            .value_cell {
                font-weight: bold;
            }
            .value_cell:hover {
                background-color: #ffffce;
            }
        </style>
        <br/><br/>
        <form name="launch" method="post" action="EditCompany.phtml?mode=edit">
            <input type="hidden" name=".r" value="<?=$this->companyId;?>"/>
            <table width="1050">
                <tr>
                    <td>
                        <span class="header_title"><?=$this->_entityName;?></span>
                    </td>
                </tr>
                <tr>
                    <td class="value_cell1">
                        <? if (isset($this->_companyUpdated)) {
                            echo $this->_companyUpdated;
                        } else if (isset($this->companyInfo['AUTHCODE'])) {
                             //if the company hasn't been activated through activation code we should not edit it.
                            echo "<strong>Company has not been activated through an activation code and can not be edited</strong><br>";
                        } ?>
                    </td>
                </tr>
            </table>
            <table width="1050" class="field_list_multitab_data">
                <tr>
				    <? foreach($this->_tabInfo as $tabLabel => $tabInfo) {
                        if ($tabInfo['viewType'] == 2) {
                            // don't display the tab for detailview
                            continue;
                        }
                    ?>
					<td width='25%' id='<?=$tabInfo['tabid'];?>Tag'>
						<b><a href='#' onclick='enableTag("<?=$tabInfo['tabid'];?>");'><?=$tabLabel;?></a></b>
					</td>
				    <? } ?>
                </tr>
                <tr>
                    <td colspan="4" valign="top">
                        <? foreach($this->_fieldInfo as $tabLabel => $fields) {
                            $viewType = $this->_tabInfo[$tabLabel]['viewType'];
                            if ($viewType == 2) {
                                // do not display for detailview
                                continue;
                            }
                        ?>
                        <table id="<?=$this->_tabInfo[$tabLabel]['tabid'];?>" style="<?=$this->_tabInfo[$tabLabel]['style'];?>">
                            <tr>
                                <? $i = 0;
                                foreach ($fields as $field) {
                                    if ($this->_dontDisplay($field, 'view')) {
                                        continue;
                                    }
                                    if ($field['fieldname'] === "scheduledjobsexecutionoffset" && $fields[0]['fieldname'] === 'scheduledjobs' && $fields[0]['value'] === "Off") {
                                        continue;
                                    }
                                    if ($field['fieldname'] === "disableapautomation") {
                                        $field['value'] = ($field['value'] == 'T') ? 'true' : 'false';
                                    }
                                    if ($field['fieldname'] == 'verbositylevel' && $field['value'] != LogManager::DEFAULT ) {
                                        preg_match('/[0-9]{2}\/[0-9]{2}\/[0-9]{4}(\s)[0-9]{2}:[0-9]{2}:[0-9]{2}/', $field['value'], $timeStarted);
                                        preg_match('/[0-9]+h/', $field['value'], $activeHours);
                                        $activeHours = substr($activeHours[0], 0, -1);
                                        $elevatedActiveTime = date("m/d/Y H:i:s", strtotime("+$activeHours hours $timeStarted[0]"));
                                        // check if the time selected for elevated level has expired
                                        if (LocalToGMT(date("m/d/Y H:i:s")) <= $elevatedActiveTime) {
                                            // elevated level is active
                                            // replace GMT with local time
                                            $GMTDate = GMTToUserTZ($elevatedActiveTime);
                                            $field['value'] = str_replace($elevatedActiveTime, $GMTDate, $field['value']);
                                        } else {
                                            // default level is made active
                                            $field['value'] = 'Default';
                                            LogManager::$verbosityLevel = LogManager::DEFAULT;
                                            // update also the value in db
                                            $verbosityLevelQuery = "DECLARE PRAGMA AUTONOMOUS_TRANSACTION;BEGIN ";
                                            $verbosityLevelQuery .= "UPDATE companypref SET value =:1 WHERE property = 'VERBOSITYLEVEL' AND cny# =:2;";
                                            $verbosityLevelQuery .= "COMMIT;END;";
                                            ExecStmt([$verbosityLevelQuery, LogManager::DEFAULT, $this->companyId]);
                                        }
                                    }
                                    $this->_displayDetailField($field);
                                    if ($i++ % 2 != 0) { ?>
                            </tr>
                            <tr>
                                    <? }
                                } ?>
                            </tr>
                            <? if ($this->_tabInfo[$tabLabel]['tabid'] === 'CData') { ?>
                            <tr>
                                <td class="label_cell">Issue recorder</td>
                                <td class="value_cell"><?=$this->companyInfo["ISSUE_RECORDER"] === "F" ? "Off" : "On";?></td>
                                <td class="label_cell">Entity License</td>
                                <td class="value_cell"><?=$this->companyInfo["LICENSE"];?></td>
                            </tr>
                            <? } ?>
                        </table>
                        <? } ?>
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="3">
                        <? if ($this->companyInfo['TYPE'] != COMPANY_TYPE_TEMPLATE) { ?>
                        <input type="submit" name=".edit" value="edit" class="nosavehistory"
                           <? /* if the company hasn't been activated through activation code we should not edit it. */
                           echo isset($this->companyInfo['AUTHCODE']) ? "disabled" : "";?>>
                        <? } ?>
                        <input type="button" name=".cancel" value="cancel" onclick='window.history.back();' class="nosavehistory">
                    </td>
                </tr>
            </table>
        </form>
        <?
    }

    /**
     * render the fields based on the field information array
     *
     * @param array $field  the field information array
     */
    private function _displayDetailField($field) {
        $fontColor = "#000000";
        if (isset($field['mandatory']) && $field['mandatory'] === true) {
            $fontColor = "#ff0000";
        }

        $value = $field["value"] ?? "";
        if ($field['type'] == 'pick' && array_key_exists($value, $field['values'])) {
            $value = $field['values'][$value];
        }
        ?>
        <td width="200" class="label_cell" style="color:<?=$fontColor;?>">
            <?=$field['fieldlabel'];?>
        </td>
        <? if ($field['type'] === 'img') { ?>
        <td align="right" valign="top">
            <?
            if (isset($field['value']) && $field['value'] != '') {
                $filetype = isl_substr($field['value'], 0, isl_strrpos($field['value'], '.'));
                echo GetCnyImageTag($this->companyInfo['RECORDNO'], $filetype, '', '', '', '../acct/');
            }
            ?>
        </td>
        <? } else { ?>
        <td width="300" class="value_cell">
            <? if (isset($field['notspecial']) && $field['notspecial'] == true) {
                echo $value;
            } else { ?>
                <?=isl_htmlspecialchars($value);?>
            <? } ?>
        </td>
        <? }
    }

    public function setEnvironment()
    {
        // get company data
        $this->setCompanyInfo();
        $this->setLogsInfo();
    }

    public function setLogsInfo()
    {
        // set the logger object
        $this->companyManagerLog = $this->gManagerFactory->getManager('companylog');
        $logTable = $this->companyManagerLog->getCompanyLogs($this->companyId);
        $this->logsInfo['LOGS'] = '';

        foreach($logTable as $log) {
            $this->logsInfo['LOGS'] .= "<hr>";
            foreach($log as $columnname => $value) {
                $this->logsInfo['LOGS'] .= $columnname. " : ". isl_htmlspecialchars($value)."<br>";
            }
        }
    }

    /**
     * merge all values for field with the fieldInfo array, has to be hard-coded since different fields are coming from different places,
     * but atleast moves all hard-coding to one place
     */
    public function mergeValues() {
        foreach ($this->_fieldInfo as &$fields) {
            foreach ($fields as &$field) {
                switch ($field["partof"]) {
                    case "company":
                        $field["value"] = $this->companyInfo[$field["columnname"]];
                        break;
                    case "logsInfo":
                        $field["value"] = $this->logsInfo[$field["columnname"]];
                        break;
                    case "module":
                        if ($field["fieldname"] == "enableautomatedtxn") {
                            $field["value"] = $this->getAutomatedTxnStatus($field);
                        } else if ($field["fieldname"] == "enabledigitalnetwork" or $field["fieldname"] == "enabledimdnsync") {
                            $field["value"] = $this->getDigitalNetworkSyncStatus($field);
                        }
                        break;
                }
            }
        }
    }

    /**
     * this function displays the edit view
     */
    public function showEditView()
    {
        if (isset($this->warnings['ALLOCATE_BIID_INSTANCE'])) {
            echo "<strong style='color: #ff0000'> " . $this->warnings['ALLOCATE_BIID_INSTANCE'] . " </strong><br/>";
        }
        ?>
        <br/><br/>
        <style>
            .value_cell {
                font-weight: bold;
            }
            .value_cell:hover {
                background-color: #ffffce;
            }
        </style>
        <form name="launch" method="post" action="EditCompany.phtml?mode=save">
            <input type="hidden" name=".r" value="<?=$this->companyId;?>">
            <table width="1050">
                <tr>
                    <td>
                        <span class="header_title"><?=$this->_entityName;?></span>
                    </td>
                </tr>
                <tr>
                    <td class="value_cell1">
                        <?=$this->_companyUpdated;?>
                    </td>
                </tr>
            </table>
            <table align='center' width=1050 class='field_list_multitab_data' >
                <tr>
                    <? foreach ($this->_tabInfo as $tabLabel => $tabInfo) { ?>
                    <td width='25%' id='<?=$tabInfo['tabid'];?>Tag'>
                        <b><a href="#" onclick="enableTag('<?=$tabInfo["tabid"];?>');"><?=$tabLabel?></a></b>
                    </td>
                    <? } ?>
                </tr>
                <tr>
                    <td colspan="4" align="left" valign="top">
                        <? foreach ($this->_fieldInfo as $tabLabel => $fields) { ?>
                        <table id="<?=$this->_tabInfo[$tabLabel]["tabid"];?>" style="<?=$this->_tabInfo[$tabLabel]["style"];?>;">
                            <tr>
		        				<? $i = 0;
                                foreach ($fields as $field) {
                                    if ($this->_dontDisplay($field, 'edit')) {
                                        continue;
                                    }
                                    if ($field['edit'] == 1 || $field['edit'] == 4) {
                                        $this->_displayDetailField($field);
                                    } else {
                                        if ($field["fieldname"] === "type" and $this->companyInfo["TYPE"] === COMPANY_TYPE_IMAGE) {
                                            $this->_displayDetailField($field);
                                        } else {
                                            $this->_displayEditField($field);
                                        }
                                    }
                                    if ($i++ % 2 != 0) { ?>
                            </tr>
                            <tr>
                                    <? }
                                } ?>
                            </tr>
                            <? if ($this->_tabInfo[$tabLabel]['tabid'] === 'CData') { ?>
                            <tr>
                                <td class="label_cell">Issue recorder</td>
                                <td>
                                    <select name="issue_recorder">
                                        <option value="T" <?=$this->companyInfo["ISSUE_RECORDER"] === "T" ? "selected" : "";?>>On</option>
                                        <option value="F" <?=$this->companyInfo["ISSUE_RECORDER"] === "F" ? "selected" : "";?>>Off</option>
                                    </select>
                                </td>
                                <td class="label_cell">Entity License</td>
                                <td class="value_cell"><?=$this->companyInfo["LICENSE"];?></td>
                            </tr>
                            <? } ?>
                        </table>
                        <? } ?>
                    </td>
                </tr>
                <tr>
                    <? if ($this->companyInfo['TYPE'] != COMPANY_TYPE_TEMPLATE) { ?>
					<td align="left" colspan="4">
						<input type="button" name=".save" value="save" onclick="validateAll();" class="nosavehistory">
						<input type="button" name=".cancel" value="cancel" onclick='window.history.back();' class="nosavehistory">
					</td>
				    <? } ?>
                </tr>
            </table>
        </form>
        <?
    }

    /**
     * @param array  $field
     * @param string $mode
     *
     * @return bool
     */
    private function _dontDisplay($field, $mode)
    {
        if($field['edit'] == 6 && eval($field['displayfieldcondition']))
        {
          return true;
        }

        if($field['edit'] == 7 && eval($field['displayfieldcondition']))
        {
            return true;
        }

        if($field['edit'] == 5 && $mode == 'view') {
            if(empty($field['value'])) {
                return true;        // some fields are not displayed when there is no value for them; edit value 5 for them
            }
        }else if($field['edit'] == 3 && $mode == 'edit') {
            return true;        // some fields are not displayed in edit view
        }
        // show fields by default; don't display only if above conditions are met
        return false;
    }

    /**
     * @param array $field
     */
    private function _displayEditField($field)
    {
        $fontColor = "#000000";
        if (isset($field['mandatory']) && $field['mandatory'] === true) {
            $fontColor = "#ff0000";
        }
    ?>
    <td align="right" valign="top" class="label_cell" width="200" <?php if ($field["fieldname"] === "scheduledjobsexecutionoffset") { echo "id='.scheduleOffsetLabel'"; } ?>>
        <font color="<?=$fontColor;?>"><?= $field['fieldlabel']; ?></font>
    </td>
    <td width="300"  <?php if ($field["fieldname"] === "scheduledjobsexecutionoffset") { echo "id='.scheduleOffsetValue'"; } ?>>
        <?php
        $fieldName = $field["fieldname"];
        $fieldValue = "";
        switch ($field["partof"]) {
            case "company":
                $fieldValue = $this->companyInfo[$field["columnname"]];
                break;
            case "logsInfo":
                $fieldValue = $this->logsInfo[$field["columnname"]];
                break;
            case "module":
                if ($fieldName == "enableautomatedtxn") {
                    $fieldValue = $this->getAutomatedTxnStatus($field);
                } else if ($fieldName == "enabledigitalnetwork") {
                    $fieldValue = $this->getDigitalNetworkSyncStatus($field);
                }
                break;
            }
        switch (strtolower($field['type'])) {
            case 'integer':
            case 'text':
                echo "<input type='text' name='.{$fieldName}' id='.{$fieldName}' value='{$fieldValue}' size='20'>";
                break;

            case 'pick':
                if ( $fieldName === "scheduledjobs" ){
                    echo "<select name='.{$fieldName}' id='.{$fieldName}' onchange='toggleScheduleOffset()'>";
                } else if ( $fieldName === "enableautomatedtxn" ){
                    echo "<select name='.{$fieldName}' id='.{$fieldName}' onchange='checkAutomatedTxn(this)'>";
                } else {
                    echo "<select name='.{$fieldName}' id='.{$fieldName}'>";
                }
                if ($fieldName === "scheduledjobsexecutionoffset" or $fieldName === "ia_billing") {
                    ShowOptions($fieldValue, $field['values'], true);
                } else {
                    ShowOptions($fieldValue, $field['values']);
                }
                echo "</select>";
                break;

            case 'textarea':
                $maxLength = $field['maxlength'];
                echo "<textarea id='.{$fieldName}' name='.{$fieldName}' rows='10' cols='50' onkeyup='this.value=this.value.substring(0, {$maxLength});'></textarea>";
                break;

            case 'date':
                list($month, , $year) = explode("/", $fieldValue);
            ?>
           <div class="yui-skin-sam-calendar" id="body_cal__obj__<?=$fieldName;?>" style="visibility: hidden;">
          <div class="yui-calclose" onclick="hideCalendar(cal_openned);"></div>
          <div class="yui-h-slider-y" onclick="changeYear(event);" id="sliderbg_y_cal__obj__<?=$fieldName;?>">
								<div class="yui-slider-thumb-y" id="sliderthumb_y_cal__obj__<?=$fieldName;?>" style="position: relative;"><?=$year;?></div>
          </div>
          <div class="yui-h-slider-m" onclick="changeMonth(event);" id="sliderbg_m_cal__obj__<?=$fieldName;?>">
								<div class="yui-slider-thumb-m" id="sliderthumb_m_cal__obj__<?=$fieldName;?>" style="position: relative;"><?=$month;?></div>
          </div>
          <div id="cal__obj__<?=$fieldName;?>" class="yui-calcontainer single" style="display: none;">
          </div>
         </div>
         <input name="_obj__<?=$fieldName;?>" value="<?=$fieldValue;?>" size="12" maxlength="12" onchange="setDate(this);SetFormChangedFlag(this);" onfocus="" onblur="" type="text">
         <a class="Pick" id="fld_obj__<?=$fieldName;?>" tabindex="-1" accesskey="P"
          title="date" href="#Skip"
          onclick='initCalendar(event, "cal__obj__<?=$fieldName;?>", "/mdY", "<?=$fieldValue?:date('m/d/Y');?>", "launch");'
          onmouseover='window.status="Open the Calendar";'
          onmouseout='window.status="";'>
         <img src="../resources/images/ia-app/buttons/date.gif" alt="date" border="0">
         </a>
        <?
                break;

            case 'limitdate':
                $fieldValues = preg_split('/ /', $fieldValue, -1, PREG_SPLIT_NO_EMPTY);
                list($month, , $year) = explode("/", $fieldValues[1]);
                $dateVisibilityOnStart = $fieldValues[0] === 'ON' ? 'inline' : 'none';
                $currentDate = gmdate("m/d/Y");
                $inputInitialValue = $fieldValues[1] ?? $currentDate;
                echo "<select name='.{$fieldName}' id='.{$fieldName}' onchange='changeDateDisplay(this.value, \"data_{$fieldName}\")'>";
                ShowOptions($fieldValues[0], $field['values']);
                echo "</select>";
                echo "<div class='yui-skin-sam-calendar' id='body_cal__obj__{$fieldName}' style='visibility: hidden'>
                <div class='yui-calclose' onclick='hideCalendar(cal_openned);'></div>
                <div class='yui-h-slider-y' onclick='changeYear(event);' id='sliderbg_y_cal__obj__{$fieldName}'>
                    <div class='yui-slider-thumb-y' id='sliderthumb_y_cal__obj__{$fieldName}'
                         style='position: relative;'>{$year}</div>
                </div>
                <div class='yui-h-slider-m' onclick='changeMonth(event);' id='sliderbg_m_cal__obj__{$fieldName}'>
                    <div class='yui-slider-thumb-m' id='sliderthumb_m_cal__obj__{$fieldName}'
                         style='position: relative;'>{$month}</div>
                </div>
                <div id='cal__obj__{$fieldName}' class='yui-calcontainer single' style='display: none;'>
                </div>
            </div>
        <div id='data_{$fieldName}' style='display:{$dateVisibilityOnStart}'>
            until:
            <input id='_obj__{$fieldName}' name='_obj__{$fieldName}' value='{$fieldValues[1]}' size='12' maxlength='12'
                   onchange='validateDate(this, \"{$currentDate}\")'  onfocus='' onblur='' type='text'>
            <a class='Pick' id='fld_obj__{$fieldName}'  tabindex=-1' accesskey='P'
               title='date' href='#Skip'
               onclick='initCalendar(event, \"cal__obj__{$fieldName}\", \"/mdY\", \"{$inputInitialValue}\", \"launch\");'
               onmouseover='window.status=\"Open the Calendar\";'
               onmouseout='window.status=\"\";'>
                <img src='../resources/images/ia-app/buttons/date.gif' alt='date' border='0'>
            </a>
            </div>";
                break;

            case 'limitverbositylevel':
                list($verbosityLevel, $activeTime) = preg_split('/ for /', $fieldValue);
                preg_match('/[0-9]+h/', $activeTime, $match);
                $activeTime = substr($match[0], 0, -1);

                $checkedOptionDefaultField = '';
                $checkedOptionElevatedField = '';
                if ( $verbosityLevel === LogManager::ELEVATED ) {
                    $activeTimeVisibility = 'inline';
                    $checkedOptionElevatedField = 'checked';
                } else {
                    $activeTimeVisibility = 'none';
                    $checkedOptionDefaultField = 'checked';
                }

                echo "<span>Default</span>  <input type='checkbox' name='.vlDefault' id='.vlDefault' 
                                                onclick='changeVerbosityActiveLevelDisplay(this, \"default\", \".activeTime\")' $checkedOptionDefaultField >     ";
                echo "<span>Elevated</span>  <input type='checkbox' name='.vlElevated' id='.vlElevated' 
                                                onclick='changeVerbosityActiveLevelDisplay(this, \"elevated\", \".activeTime\")' $checkedOptionElevatedField >";

                echo "<div id='.activeTime' style='display:{$activeTimeVisibility}'
                       <span>  for: </span> 
                       <select name='.activeTime' id='.activeTime'>";
                ShowOptions($activeTime, [1, 2, 4, 12, 24, 36, 48]);

                echo "</select><span> h</span></div>";

                break;
            case "checkbox":
                echo "<input name=\"." . $fieldName . "\" id=\"." . $fieldName . "\" value=\"T\" type=\"checkbox\" " . ($fieldValue === "T" ? "checked" : "") . " />";
                break;
        }
        ?>
    </td>
    <?php
    }

    /**
     * this function saves the form values
     */
    public function save() {
        global $gManagerFactory, $gErr;

        // check required fields
        $source = __FUNCTION__ . " :: " . __FILE__;
        if (Globals::$g->islive and (isl_trim(Request::$r->_notes) === "" or isl_trim(Request::$r->_whowanted) === "" or isl_trim(Request::$r->_bugid) === "")) {
            $gErr->addError("**********", $source, "Please fill in Bugid, Who wanted the change, Notes fields, for logging purposes.");
        }
        if (!isl_trim(Request::$r->_title)) {
            $gErr->addError("**********", $source, "Title cannot be empty.");
        }
        if (Request::$r->_type == "NULL" and ($this->co["TYPE"] != "" or isset($this->co["TYPE"]))) {
            $gErr->addError("**********", $source, "New type cannot be NULL.");
        }
        // from cs tools, admin user is not mandatory; set this to be used in companyinfomanager->set()
        $this->companyInfo["REQUIRED_ADMIN_USER"] = false;
//        if ($this->companyInfo["ADMIN_USER_KEY"] === null or $this->companyInfo["ADMIN_USER_KEY"] === "") {
//            $gErr->addError("**********", $source, "Administrative user cannot be empty.");
//        }
        if (!isset($this->co)) {
            $gErr->addError("**********", $source, "Company could not be fetched, update failed.");
        }

        if (isset(Request::$r->_attachment_max_upload_size)) {
            $supportingDocumentsService = new SupportingDocumentsService();
            if (!$supportingDocumentsService->isValidAttachmentMaxUploadSize(floatval(Request::$r->_attachment_max_upload_size))) {
                $gErr->addError("**********", $source, "Maximum attachment upload size has an invalid value. Valid values are: -1 (for no limit) or a value greater than 0");
            }
        }

        if (HasErrors()) {
            include "../acct/popuperror.phtml";
            exit;
        }

        $oCompanyManager = $gManagerFactory->getManager("company");
        $oCompanyInfoManager = $gManagerFactory->getManager("company_info");

        // keep a copy of old company for comparison purpose before merging form values
        $oldCompany = $oCompanyManager->get($this->companyId);
        $oldCompanyInfo = $this->companyInfoCache;

        if ($oldCompanyInfo["TYPE"] != COMPANY_TYPE_DEMO and $this->co["TYPE"] == COMPANY_TYPE_DEMO) {
            $gErr->addError("**********", $source, "Cannot change tenant type to Demo because the type will be deprecated in R3 2025. Please select Sales demo or another type from the list.");
        }

        $deleteDate = &Request::$r->_obj__deletedate;
        if (in_array($this->co['TYPE'], array(COMPANY_TYPE_DEMO, COMPANY_TYPE_TRIAL, COMPANY_TYPE_SAMPLE))) {
            if (empty($deleteDate)) {
                // the company type is being changed to demo/trial, set the delete date(expiry date) as per TenantLifeCycle config
                $deleteDate = date('m/d/Y');
            }
        }
        $this->companyInfo['DELETEDATE'] = $deleteDate;

        $bCompanyChanged = false;
        foreach ($this->co as $field => $value) {
            if ($value != $oldCompany[$field]) {
                $bCompanyChanged = true;
                break;
            }
        }

        if ($bCompanyChanged and $this->co['TYPE'] != $oldCompany['TYPE']) {
            global $gDBServerId;

            if (!CheckAcceptedTypesforDB($gDBServerId, $this->co['TYPE'])) {
                $this->gErr->addError(
                    "BL02000012", __FILE__.':'.__LINE__,
                    "Cannot change type for this company because its database does not support the ".$this->co['TYPE']." type."
                );
            }
        }

        // reset contact data if the admin user was unset
        if (($oldCompany["ADMIN_USER_KEY"] ?? "") !== $this->companyInfo["ADMIN_USER_KEY"] and $this->companyInfo["ADMIN_USER_KEY"] === "") {
            $this->companyInfo["CONTACTNAME"] = "";
            $this->companyInfo["CONTACTPHONE"] = "";
            $this->companyInfo["CONTACTEMAIL"] = "";
            $this->companyInfo["FAX"] = "";
        }

        // ovi - 24-01-21 - practicetype & affiliation seem unused
//        $oldAffiliationValue = $this->currentAffiliationValue;
        /** @noinspection PhpUndefinedVariableInspection */
//        $this->currentAffiliationValue = $_affiliation;
//        $this->currentAffiliationValue = null;
//        $affiliationValueChanged = 0;
//        if ($this->currentAffiliationValue != $oldAffiliationValue) {
//            $affiliationValueChanged = 1;
//        }

        if ($this->companyInfo["GLOBALSEARCHENABLED"] === "T") {
            $currentESInstanceId = $oldCompanyInfo['ESID'] ?? null;
            if (!$this->companyInfo['ESID']) {
                $this->companyInfo['ESID'] = ElasticSearchInstance::allocateInstance(true);
            } elseif ((int)$currentESInstanceId !== $this->companyInfo['ESID']) {
                // the new ESID must be updated in company cache before we construct a SearchJob instance.
                // disable ES for a brief moment in order to remove the search progress aka the data from 'indexingstatus' from current instance
                // allocateInstance() also makes sure that the new ESID represents a valid ID
                $searchJob = new SearchJob($this->companyId); // this object connects to the current ESID
                $searchJob->searchDisable($this->companyId);
                unset($searchJob);
                $this->companyInfo['ESID'] = ElasticSearchInstance::allocateInstance(true, $this->companyInfo['ESID']);
            }
            $searchJob = new SearchJob($this->companyId); // this object connects to the new ESID (if it was changed)
            if ($this->companyInfo['ESID']) {
                $searchJob->searchEnable($this->companyId);
            } else {
                $this->companyInfo['GLOBALSEARCHENABLED'] = 'F';
                $gErr->AddWarning("Elastic Search instance not found. Global search will not be enabled");
                logToFileCritical(
                    ElasticSearchDriver::LOGGING_PREFIX
                    . "No ElasticSearch Index found. Global Search not enabled on $this->companyId"
                );
                $this->warnings["GLOBALSEARCH_INSTANCE_WARN"] = "Elastic Search Instance not available";
            }
        } else {
            $searchJob = new SearchJob($this->companyId);
            $this->companyInfo['ESID'] = null;
        }

        $bCompanyInfoChanged = false;
        foreach ($this->companyInfo as $field => $value) {
            if ($value != $oldCompanyInfo[$field]) {
                $bCompanyInfoChanged = true;
                break;
            }
        }

        // ovi - 24-01-21 - practicetype & affiliation seem unused
//        if (!$bCompanyChanged and !$bCompanyInfoChanged and ($affiliationValueChanged == 0 or $this->practicetype != 'C') and !$this->warnings["GLOBALSEARCH_INSTANCE_WARN"]) {
        if (!$bCompanyChanged and !$bCompanyInfoChanged and !isset($this->warnings["GLOBALSEARCH_INSTANCE_WARN"])) {
            $gErr->addError('PL0300003', $source, 'No Changes have been made, please check the form again');

            include "../acct/popuperror.phtml";
            exit;
        }

        $this->setGlobals();
        XACT_BEGIN('EDIT_COMPANY_DATA', $this->dbConn);
        // ovi - 24-01-21 - seems unused
//        if ($this->practicetype == 'C' && $oldAffiliationValue != $this->currentAffiliationValue) {
//            $updatecns = array('QUERY' => "update practicecompany set affiliation = ? where cny# =? ",'ARGTYPES' => array('text','integer'));
//            $updateResult = $this->gQueryMgr->DoCustomQuery($updatecns, array($this->currentAffiliationValue, $this->companyId));
//            if (!$updateResult) {
//                $gErr->addError('**********', $source, 'Update company console affiliation type - This is an invalid update statement');
//                XACT_ABORT('EDIT_COMPANY_DATA', $this->dbConn);
//
//                include "../acct/popuperror.phtml";
//                exit;
//            }
//        }

        // TODO ovi - eliminate this; use only company info, as it includes company too
        if ($bCompanyChanged) {
            $updateResult = $oCompanyManager->set($this->co);
            if (!$updateResult) {
                $gErr->addError('**********', $source, 'companyManager:Set - This is an invalid update statement');
            }

            if (!$gErr->hasErrors() and $this->co["TITLE"] !== $oldCompany["TITLE"]) {
                // If consolidation, update subsidiary to reflect correct company title
                $updatecns = array(
                    'QUERY' => "update subsidiary set subsidiaryid = ?, subsidiaryname = ?  where subsidiarykey = ? and subsidiaryid = ? and subsidiaryname = ?",
                    'ARGTYPES' => array('text','text','integer','text','text')
                );
                $updateResult = $this->gQueryMgr->DoCustomQuery($updatecns, array($this->co['TITLE'], Request::$r->_name, $this->co['RECORDNO'], $oldCompanyInfo['TYPE'], $oldCompanyInfo['TYPE']));
                if (!$updateResult) {
                    $gErr->addError('**********', $source, 'Update subsidiary - This is an invalid update statement');
                }
            }
        }

        if ($bCompanyInfoChanged) {
            // if MAXLOGINATTEMPT was not set before , companyinfomanager raises error , so set it to default value
            if (!isset($this->companyInfo['MAXLOGINATTEMPT']) || $this->companyInfo['MAXLOGINATTEMPT'] == '') {
                $this->companyInfo['MAXLOGINATTEMPT'] = $oCompanyInfoManager->GetFieldInfo('MAXLOGINATTEMPT')['default'];
            }
            if (isset($this->companyInfo['THEME']) && $this->companyInfo['THEME'] !== '') {
                list($custom, $navbgcolor, $navfontcolor) = explode(';', $this->companyInfo['THEME']);
                if ($custom == 'custom') {
                    // THEME is composed of NAVBGCOLOR and NAVFONTCOLOR.  The manager expects these values to be set
                    $this->companyInfo['NAVBGCOLOR'] = $navbgcolor;
                    $this->companyInfo['NAVFONTCOLOR'] = $navfontcolor;
                }
            }

            // Add missing password history config or data corruption, set to default
            if (!isset($this->companyInfo['ENFORCEPWDHISTORY'])
                || ($this->companyInfo['ENFORCEPWDHISTORY'] < IAPWD_HISTORY_DEFAULT)
            ) {
                $this->companyInfo['ENFORCEPWDHISTORY'] = IAPWD_HISTORY_DEFAULT;
            }

            $billing_level = 0;
            if (!$gErr->hasErrors()) {
                // for some companies companyinfo name and contactemail is empty so we must set it, otherwise companyinfo manager raises error
                if (!isl_trim($this->companyInfo['NAME'])) {
                    $this->companyInfo['NAME'] = $this->co['NAME'];
                }

                // set _mod variable to prevent company_info manager raise base currency not set error for my practice not set up companies
                // bug id : 23795
                $resultSet = QueryResult(array("select COUNT(*) AS RESULT from MODULE WHERE MODULEID = '13.PR' AND CNY# = :1 ", $this->companyId));
                if ($resultSet[0]['RESULT'] > 0) {
                    Request::$r->_mod = 'mp';
                }

                // ovi - ia_billing will be updated later, to generate a billing INSERT event
                if ($this->companyInfo["IA_BILLING"] !== $oldCompanyInfo["IA_BILLING"] and $this->companyInfo["IA_BILLING"] != 0) {
                    $billing_level = $this->companyInfo["IA_BILLING"];
                    $this->companyInfo["IA_BILLING"] = $oldCompanyInfo["IA_BILLING"];
                }

                $oCompanyInfoManager->setSkipValidation('TAXID');
                $updateResult = $oCompanyInfoManager->set($this->companyInfo);
                if (!$updateResult) {
                    $gErr->addError('**********', $source, 'SetCompanyPreferences - This is an invalid update statement');
                }
            }

            // init billing
            if (!$gErr->hasErrors() and $billing_level != 0) {
                $aws_config = GetValueForIACFGProperty("AWS_SNS");
                addLog("init billing for cny=" . $this->companyId, LogManager::INFO, false, $aws_config["LOG_FILE"]);

                require_once "backend_msg.inc";
                try {
                    $events = msg_init_billing($this->companyId, $billing_level);
                    addLog("init billing SUCCESS, " . $events . " events generated for cny=" . $this->companyId, LogManager::INFO, false, $aws_config["LOG_FILE"]);
                } catch (IAException $e) {
                    addLog("init billing FAILED", LogManager::ERROR, true, $aws_config["LOG_FILE"]);
                    addLog(print_r($e, true), LogManager::ERROR, false, $aws_config["LOG_FILE"]);
                    $gErr->addError("**********", $source, "init billing FAILED. " . $e->getMessage());
                }
            }
        }

        // save the company expiration date
        // do Tenant lifecycle specific changes
        // TODO: ovi - remove tenant lifecycle; deprecated
        try {
            $tenantLifeCycleManager = new TenantLifeCycleManager($this->co['RECORDNO']);
            if ( $tenantLifeCycleManager->updateOnExtendExpiryDate($deleteDate) == false ) {
                $this->gErr->addError('**********', $source, "Unable to extend the expiration date!");
            } else if ($tenantLifeCycleManager->updateOnChangeTenantType($this->co['TYPE'], $oldCompany['TYPE']) == false) {
                $this->gErr->addError('**********', $source, "Unable to change life cycle dates specific to tenant type!");
            }
        } catch (Exception $tenantException) {
            $this->gErr->addError('**********', $source, $tenantException->getMessage());
        }

        if (!$this->gErr->hasErrors()) {
            if (($this->companyInfoCache['TYPE'] != $this->companyInfo['TYPE']) or ($this->companyInfoCache['SCHEDULEDJOBS'] != $this->companyInfo['SCHEDULEDJOBS'])) {
                $activities = array('SCHEDULEDJOBS' => $this->companyInfo['SCHEDULEDJOBS']);
                // because we havent commited yet, new title is not valid
                $updateResult = SetProductionActivities($oldCompany['TITLE'], $this->companyInfo['TYPE'], $activities);
                if (!$updateResult) {
                    $this->gErr->addError('**********', $source, 'SetScheduledJobs - This is an invalid update statement');
                } else {
                    $this->companyInfoCache['SCHEDULEDJOBS'] = $updateResult;
                }
            }
        }

        // See if Global Search has been enabled or disabled, and if so perform that enable/disable.
        if ($this->companyInfo["GLOBALSEARCHENABLED"] !== $oldCompanyInfo['GLOBALSEARCHENABLED']) {
            if ($this->companyInfo['GLOBALSEARCHENABLED'] == 'T') {
                $searchJob->searchEnable($this->companyId);
            } else {
                $searchJob->searchDisable($this->companyId);
            }
        }

        // TODO: 24-01-21 ovi - deprecated; should be handled by auto-purging
        // on removal, look into EditCompanyUtils.cls too
        // check if the company needs to be reactivated and do it
//        if ($this->companyInfo['STATUS'] != 'active') {
//            $expirationPeriod = EditCompanyUtils::getExpirationPeriod();
//            $deleteDate = $this->getDeletionDate();
//            if (strtotime("+{$expirationPeriod} days", strtotime($deleteDate)) > strtotime(date('Y/m/d'))) {
//                $activateCompanyQuery = array(
//                    'QUERY' => "update company set status=? where record#=?",
//                    'ARGTYPES' => array('text')
//                );
//                $this->gQueryMgr->DoCustomQuery($activateCompanyQuery, array( 'T', $this->co['RECORDNO']));
//            }
//        }

        $companyType = GetMyCompanyType();

        // check if the digital network enable or not
        if (isSpecified(Request::$r->_enabledigitalnetwork) && Request::$r->_enabledigitalnetwork != 'F' ) {
            // Get the Module Pref
            GetModulePreferences(Globals::$g->kDNid, $modulePreferences);
            $isAdvAudit = AdvAuditTracking::isCnyTracked();

            //Block Sandbox and implementation company
            if ($companyType === COMPANY_TYPE_SANDBOX || $companyType === COMPANY_TYPE_IMPLEMENTATION) {
                Globals::$g->gErr->addError(
                    'BL03000106', __FILE__ . '.' . __LINE__,
                    'Digital Networrk Sync feature cannot be enabled for the company type ' . $companyType
                );
            } //Block the customer from Enabling DN synch whe the Audit column has null value in Location
            elseif ($this->isAuditColumnEmpty()) {
                Globals::$g->gErr->addError(
                    'BL03000106', __FILE__ . '.' . __LINE__,
                    'Digital Networrk Sync feature cannot be enabled for the a company with Audit column empty 
                    for Vendor/Location'
                );
            } else {
                /** @var DigitalNetworkSyncQueueManager $digitalNetworkQueueMgr */
                $digitalNetworkQueueMgr = Globals::$g->gManagerFactory->getManager('digitalnetworksyncqueue');
                // Module Not install, then insert into FiFo
                if ($modulePreferences["MODULE_CONFIGURED"] === null or $modulePreferences["MODULE_CONFIGURED"] === "") {
                    // If Feature configuration STOP_DIGITAL_NETWORK = T , then return true, otherwise false
                    if (!FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('STOP_DIGITAL_NETWORK')) {

                        // Check for the Event Queue
                        $hasSyncJob = $digitalNetworkQueueMgr->getDNSubscribeSyncJobStatus();

                        if (!$hasSyncJob) {
                            // Add the SUBSCRRIBE_ORRGANIZATION Event
                            $ok = $digitalNetworkQueueMgr->addDigitalNetworkSyncEvent();
                            // Check for the Event Queue
                            $hasSyncJob = $digitalNetworkQueueMgr->getDNGLDimJobStatus();
                            if (!$hasSyncJob) {
                                // Add the GL Dimensions Event
                                $ok = $ok && $digitalNetworkQueueMgr->addGLDimNetworkSyncEvents();
                            }
                            if (!$ok) {
                                Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Error - Digital Networrk Sync Enable");
                            }
                        }
                    }
                }
            }
        }

        // check if the automated transaction is enable or not
        if (isSpecified($this->companyInfo["ENABLEAUTOMATEDTXN"]) and $this->companyInfo["ENABLEAUTOMATEDTXN"] == 'SAIL' ) {

            $isAdvAudit = AdvAuditTracking::isCnyTracked();
            $syncStatus['columnname'] = 'ENABLEDIGITALSYNC';
            $dnSyncStatusResult = $this->getDigitalNetworkSyncStatus($syncStatus);
            $canSwitchAT = $this->canSwitchAT();
            //Block Sandbox and implementation company
            if ($companyType === COMPANY_TYPE_SANDBOX or $companyType === COMPANY_TYPE_IMPLEMENTATION) {
                Globals::$g->gErr->addError(
                    'BL03000106', __FILE__ . '.' . __LINE__,
                    'Automated transaction cannot be enabled for the company type ' . $companyType
                );
            } //Block the customer from Enabling DN synch whe the Audit column has null value in Location
            elseif ($this->isAuditColumnEmpty()) {
                Globals::$g->gErr->addError(
                    'BL03000106', __FILE__ . '.' . __LINE__,
                    'Automated transaction cannot be enabled for the a company with Audit column empty 
                    for Vendor/Location'
                );
            } elseif ($dnSyncStatusResult != 'T') {
                Globals::$g->gErr->addError(
                    'BL03000106', __FILE__ . '.' . __LINE__,
                    'Digital network sync has to be enabled to subscribe to SAIL'
                );
            } elseif (!$canSwitchAT) {
                Globals::$g->gErr->addError(
                    'BL03000106', __FILE__ . '.' . __LINE__,
                    'Cannot subscribe to SAIL as there are pending jobs in queue'
                );
            }
            else {
                $ok = true;
                $ok = $ok && AutomatedTransactionSetupManager::configureProvider(AutomatedTransactionSetupManager::PROVIDER_SAIL);
                GetModulePreferences(Globals::$g->kSTXid, $modulePreferences);
                if ( $modulePreferences[StxSetupManager::STXSUBSCRIBED] === 'T' ) {
                    // If stx is already subscribed it means dn sync subscribed company event has already finished
                    // This is a switch from STX to SAIL
                    $modulePreferences[StxSetupManager::STXSUBSCRIBED] = 'F';
                    $ok = $ok && SetModulePreferences(Globals::$g->kSTXid, $modulePreferences);
                }
                if (!$ok) {
                    Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Error - Automated transaction failed");
                }
            }
        }

        $queueMgr = Globals::$g->gManagerFactory->getManager('cashflowdigitalnetworksyncqueue');
        if ($this->companyInfo['ENABLECASHFLOW'] != null && $this->companyInfo["ENABLECASHFLOW"] != 'F' ) {
            GetModulePreferences(Globals::$g->kDNid, $modulePreferences);
            if ($modulePreferences["MODULE_CONFIGURED"] == null || $modulePreferences["MODULE_CONFIGURED"] === "" ) {
                Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Digital Network has to be enabled for Cash Flow automation.");
            }
             else if ($this->companyInfo["DISABLEAPAUTOMATION"] != null && $this->companyInfo["DISABLEAPAUTOMATION"] === 'T' && $this->companyInfoCache['DISABLEAPAUTOMATION'] == $this->companyInfo["DISABLEAPAUTOMATION"]) {
                Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "AP automation has to be enabled for Cash Flow automation.");
            }
            else {
                if ($this->companyInfoCache['ENABLECASHFLOW'] !== $this->companyInfo["ENABLECASHFLOW"]) {
                    $queueMgr = Globals::$g->gManagerFactory->getManager('cashflowdigitalnetworksyncqueue');
                    if ($this->companyInfo['ENABLECASHFLOW'] == 'T') {
                        $ok = $queueMgr->configSyncForCashFlowMatching(true);
                        $ok = $ok && $queueMgr->processBulkSyncForCashFlow();
                        if (!$ok) {
                            Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Error - Cash Flow Digital Networrk Sync Enable");
                        }
                    }
                }
            }
        } else {
            if ($this->companyInfo['ENABLECASHFLOW'] == null && $this->companyInfoCache['ENABLECASHFLOW'] === 'T') {
                $queueMgr = Globals::$g->gManagerFactory->getManager('cashflowdigitalnetworksyncqueue');
                $hasCashFlowJobsInQueue = $queueMgr->getCashFlowSyncJobStatus();
                $ok = false;
                if ($hasCashFlowJobsInQueue) {
                    $updateJobStatus = $queueMgr->failQueuedJobs();
                    if(!$updateJobStatus) {
                        Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Error - Failed to update the status of the jobs currently in the queue.");
                    }
                }
                $ok = $queueMgr->configSyncForCashFlowMatching(false);
                if (!$ok) {
                    Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Error - Cash Flow Digital Network Sync Disable");
                }
            }
        }

        if (isSpecified(Request::$r->_enabledimdnsync) && Request::$r->_enabledimdnsync != 'F') {
            /** @var DigitalNetworkSyncQueueManager $digitalNetworkQueueMgr */
            $digitalNetworkQueueMgr = Globals::$g->gManagerFactory->getManager('digitalnetworksyncqueue');

            // Check for the Event Queue
            $hasSyncJob = $digitalNetworkQueueMgr->getDNGLDimJobStatus();
            if (!$hasSyncJob) {
                // Add the GL Dimensions Event
                $ok = $digitalNetworkQueueMgr->addGLDimNetworkSyncEvents();
                if (!$ok) {
                    Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Error - Digital Networrk Sync Enable");
                }
            }
        }

        $this->updateAPAutomationStatus(Request::$r->_disableapautomation);

        $fieldValue = Request::$r->_disableemailvalidation ?? '';
        $this->updateAPAutomationEmailCheck($fieldValue);

        // assign variables for logging
        $notes = "<bugid>" . Request::$r->_bugid . "</bugid><whowanted>" . Request::$r->_whowanted . "</whowanted><notes>" . Request::$r->_notes . "</notes>";
        $_userip = &Globals::$g->REMOTE_ADDR;

        // assign all values to be logged to log array
        $coLog['TITLE']                             = $this->co['TITLE'];
        $coLogOld['TITLE']                          = $oldCompany['TITLE'];
        $coLog['TYPE']                              = $this->co['TYPE'];
        $coLogOld['TYPE']                           = $oldCompany['TYPE'];
        $coLog["ADMIN_USER_KEY"]                    = $this->co["ADMIN_USER_KEY"];
        $coLogOld["ADMIN_USER_KEY"]                 = $oldCompany["ADMIN_USER_KEY"];
        $coLog["IA_BILLING"]                        = $this->co["IA_BILLING"];
        $coLogOld["IA_BILLING"]                     = $oldCompany["IA_BILLING"];
        $coLog['IPADDRESSFILTER']                   = $this->companyInfo['IPADDRESSFILTER'];
        $coLogOld['IPADDRESSFILTER']                = $this->companyInfoCache['IPADDRESSFILTER'];
        $coLog['SCHEDULEDJOBS']                     = $this->companyInfo['SCHEDULEDJOBS'];
        $coLogOld['SCHEDULEDJOBS']                  = $oldCompanyInfo['SCHEDULEDJOBS'];
        $coLog['SCHEDULEDJOBSEXECUTIONOFFSET']      = $this->companyInfo['SCHEDULEDJOBSEXECUTIONOFFSET'];
        $coLogOld['SCHEDULEDJOBSEXECUTIONOFFSET']   = $oldCompanyInfo['SCHEDULEDJOBSEXECUTIONOFFSET'];
        // ovi - 24-01-21 - practicetype & affiliation seem unused
//        $coLog['AFFILIATION']                       = $this->currentAffiliationValue;
//        $coLogOld['AFFILIATION']                    = $oldAffiliationValue;
        $coLog['VERBOSITYLEVEL']                    = $this->companyInfo['VERBOSITYLEVEL'];
        $coLogOld['VERBOSITYLEVEL']                 = $oldCompanyInfo['VERBOSITYLEVEL'];
        $coLog['DISABLEAPAUTOMATION']               = $this->companyInfo['DISABLEAPAUTOMATION'];
        $coLogOld['DISABLEAPAUTOMATION']            = $oldCompanyInfo['DISABLEAPAUTOMATION'];
        $coLog['DISABLEEMAILVALIDATION']            = $this->companyInfo['DISABLEEMAILVALIDATION'];
        $coLogOld['DISABLEEMAILVALIDATION']         = $oldCompanyInfo['DISABLEEMAILVALIDATION'];
        $coLog['ATTACHMENT_MAX_UPLOAD_SIZE']        = $this->companyInfo['ATTACHMENT_MAX_UPLOAD_SIZE'];
        $coLogOld['ATTACHMENT_MAX_UPLOAD_SIZE']     = $oldCompanyInfo['ATTACHMENT_MAX_UPLOAD_SIZE'];
        $coLog['DATA_SIZE']                         = $this->co['DATA_SIZE'];
        $coLogOld['DATA_SIZE']                      = $oldCompany['DATA_SIZE'];
        $coLog['ENABLECASHFLOW']               = $this->companyInfo['ENABLECASHFLOW'];
        $coLogOld['ENABLECASHFLOW']            = $oldCompanyInfo['ENABLECASHFLOW'];

        $logArr = array(
            'COLOG'    => $coLog,
            'COLOGOLD' => $coLogOld,
            'USER'     => ToolsAuthentication::$t->getUserName(),
            'DATE'     => GetOraTimeStamp(),
            'IP'       => $_userip,
            'NOTES'    => $notes,
        );
        $this->companyManagerLog->insertCompanyLog($logArr);
        $this->companyInfo['DELETEDATE'] = $deleteDate;
        // Load the dates again to show them on the view screen
        $this->companyInfo['CREATED'] = $this->co['CREATED'];
        $this->companyInfo['EXPIRATIONDATE'] = $this->getUserExpirationDate();
        $this->loadCompany2Info();
        $this->setLogsInfo();

        // Log query data disable => delete all collections from company mongoDB database created by this feature
        if ($this->companyInfo['LOGQUERYDATA'] != $oldCompanyInfo['LOGQUERYDATA'] and $this->companyInfo['LOGQUERYDATA'] == 'OFF') {
            include_once 'QueryStorage.cls';
            $qStorage = new QueryStorage(MongoDBEnvManager::getInstance()->getHostsManager(), LogManager::getInstance());
            if ($qStorage->dropCollections($this->companyId) === false) {
                $this->gErr->addError('**********', $source, "Unable to delete stored data!");
            }
        }

        if ($oldCompanyInfo['VERBOSITYLEVEL'] != $this->companyInfo['VERBOSITYLEVEL']) {
            //update the verbosity level
            $updateCompanyVerbosityLevelQuery = [
                'QUERY' => "update companypref set value=? where cny#=? and property=?",
                'ARGTYPES' => ['text']
            ];
            /** @noinspection PhpUnusedLocalVariableInspection */
            $updateVerbosityLevelResult = $this->gQueryMgr->DoCustomQuery($updateCompanyVerbosityLevelQuery, [ $this->companyInfo['VERBOSITYLEVEL'], $this->companyId, 'VERBOSITYLEVEL' ]);
        }

        if ($this->gErr->hasErrors()) {
            XACT_ABORT('EDIT_COMPANY_DATA', $this->dbConn);

            include "../acct/popuperror.phtml";
            exit;
        }
        XACT_COMMIT("EDIT_COMPANY_DATA", $this->dbConn);

        if (CRWUtil::isCnySubcribed($this->companyId) and $oldCompany['TITLE'] !== Request::$r->_title) {
            require_once "crw_util.inc";
            renameOBICompanyTitle($this->companyId, $oldCompany['TITLE'], $this->co["TITLE"]);
        }

        if (CRWUtil::isCnySubcribed($this->companyId) and $oldCompanyInfo['BIID'] !== $this->companyInfo['BIID']) {
            $reporting = new Reporting();
            $today = date_create('2018-01-01');
            $backupFrom = $today->getTimestamp();
            $folders = [];
            $oldInstance = OBIEEInstance::getInstance($oldCompanyInfo['BIID']);
            $backupCny = false;
            if (($oldInstance !== null) && (!$oldInstance->isUnderMaintenance())) {
                $backupCny = $reporting->backupTenantCny('/shared/', $folders, $backupFrom, $output, $this->companyId, $oldCompanyInfo['BIID']);
            }
            if (!$backupCny) {
                // BIID is changed even though the backup process failed.
                $this->warnings['CHANGE_BIID_ERROR'] =  "Backup cny failed for tenant " . $this->companyId . " by changing BIID.";
                LogToFile("CRW_BACKUPCNY: Backup cny failed for tenant " . $this->companyId . " by changing BIID.");
            }
            $reporting = new Reporting(null, $this->companyInfo['BIID']);
            $restoreCny = $reporting->restoreCny($this->companyId, $this->companyInfo['BIID'], 'full', true, $output);
            if ($restoreCny) {
                 if (($oldInstance !== null) && (!$oldInstance->isUnderMaintenance())) {
                     $reporting = new Reporting($oldInstance->getAdminUser(), $oldInstance->getInstanceId());
                     $reporting->deleteFolder("/shared/".$this->co['TITLE']);
                     queueRegisterTenantJobs($oldCompanyInfo['BIID'], $this->companyInfo['BIID'], $this->companyId);
                     $this->warnings['CHANGE_BIID_SUCCESS'] = "Tenant Successfully moved to the new instance";
                 }
            } else {
                // roll back the BIID if can't restore tenant to new instance
                SetCompanyPreferences('@' . $this->companyId, ['BIID' => $oldCompanyInfo['BIID']]);
                $this->warnings['CHANGE_BIID_ERROR'] = "Restore cny failed for tenant " . $this->companyId . " by changing BIID.";
                LogToFile("CRW_RESTORECNY: Restore cny failed for tenant " . $this->companyId . " by changing BIID.");
            }
        }

        $this->_companyUpdated = isset($this->warnings["GLOBALSEARCH_INSTANCE_WARN"]) ? "Company updated with warnings!" : "Company updated!";
    }

    /**
     * Function to disable/enable the email security check in the DN Side
     * @param string $fieldValue
     *
     * @return void
     * @throws IAException
     * @throws SailException
     * @throws SimpleHttpResponseException
     */
    public function updateAPAutomationEmailCheck(string $fieldValue) {
        if (!isNullOrBlank($fieldValue) && $this->companyInfoCache['DISABLEEMAILVALIDATION'] != $fieldValue) {
            GetModulePreferences(Globals::$g->kDNid, $modulePreferences);

            // if not install, then install
            if (!isNullOrBlank($modulePreferences['MODULE_CONFIGURED'])) {
                // get the sync job status
                $hasDNSyncEnabled = true;
            } else {
                $digitalNetworkQueueMgr = Globals::$g->gManagerFactory->getManager('digitalnetworksyncqueue');
                $hasDNSyncEnabled = $digitalNetworkQueueMgr->getDNSubscribeSyncJobStatus();
            }

            if ($hasDNSyncEnabled === true ) {
                $emailCheck = true;
                if ($fieldValue === 'F') {
                    $emailCheck = false;
                }
                if (AutomatedTransactionInteractions::isAutomatedTransactionEnabled()) {
                    $apAutomation = $this->companyInfoCache['DISABLEAPAUTOMATION'];
                    $apAutomationActive = true;
                    if ($apAutomation === 'T') {
                        $apAutomationActive = false;
                    }
                    AutomatedTransactionSetupManager::getActiveProviderObject()
                        ->updateConfigs($apAutomationActive,$emailCheck);
                } else {
                    $companyGuid = \CompanyCacheHandler::getInstance()->getProperty('COMPANYPREF', 'GUID');
                    if (empty($companyGuid)) {
                        throw new SailException(0, "GUID is not set for cny# -");
                    }
                    $apConfigSailHttp = new APConfigSailHttpRequest();

                    $apConfigSailHttp->setCompanyGuid($companyGuid);
                    $endPoint = $apConfigSailHttp->getEndpoint() . APConfigSailHttpRequest::SETTINGS_END_POINT;

                    $bodyParam['network_id'] = APConfigSailHttpRequest::APPlICATION_ID . '+' . $companyGuid . '_0';
                    $bodyParam['email']['security'] = $emailCheck;

                    $payload = json_encode($bodyParam);

                    $slHttpResponse = $apConfigSailHttp->makeHttpRequest('APEMAILSERVICECONFIGSETUP', $endPoint, $payload, 'POST');

                    $msg = "file= " . GetFL() . " | httpStatusCode= " . $slHttpResponse->getResponseCode() . " 
                | response: " . $slHttpResponse->getResponse() . " 
                | responseHeader:" . json_encode($slHttpResponse->getResponseHeader()) . " \n";

                    if ($slHttpResponse->getResponseCode() >= SailException::RES_CODE_SERVER_ERROR
                        || $slHttpResponse->getResponseCode() == 0) {
                        $apConfigSailHttp->log($msg);
                        Globals::$g->gErr->addError(
                            'BL03000106', __FILE__ . '.' . __LINE__,
                            "Issue with the end point "
                        );
                    } else if ($slHttpResponse->getResponseCode() == SailException::RES_CODE_VALIDATION_ERROR) {
                        $apConfigSailHttp->log($msg);
                        Globals::$g->gErr->addError(
                            'BL03000106', __FILE__ . '.' . __LINE__,
                            "Issue with the end point res code"
                        );
                    }
                }
            } else {
                if( (!isNullOrBlank($this->companyInfoCache['DISABLEEMAILVALIDATION'])
                    && $fieldValue != $this->companyInfoCache['DISABLEEMAILVALIDATION'] )
                    || (isNullOrBlank($this->companyInfoCache['DISABLEEMAILVALIDATION'])
                    && $fieldValue == 'F')) {
                    Globals::$g->gErr->addError(
                        'BL03000106', __FILE__ . '.' . __LINE__,
                        "Digital Network Not enabled in this company. please enable first."
                    );
                }
            }
        }
    }


    /**
     * @param $p_disable_ap_automation
     *
     * @return void
     */
    public function updateAPAutomationStatus($p_disable_ap_automation)
    {
        $p_disable_ap_automation = $p_disable_ap_automation?? 'F';
        if ($this->companyInfoCache['DISABLEAPAUTOMATION'] != $p_disable_ap_automation) {
            GetModulePreferences(Globals::$g->kDNid, $modulePreferences);

            // if not install, then install

            if ($modulePreferences["MODULE_CONFIGURED"] !== null and $modulePreferences["MODULE_CONFIGURED"] !== "") {
                // get the sync job status
                $hasDNSyncEnabled = true;
            } else {
                $digitalNetworkQueueMgr = Globals::$g->gManagerFactory->getManager('digitalnetworksyncqueue');
                $hasDNSyncEnabled = $digitalNetworkQueueMgr->getDNSubscribeSyncJobStatus();
            }
            if ($hasDNSyncEnabled) {
                $apAutomationActive = true;
                if ($p_disable_ap_automation === 'T') {
                    $apAutomationActive = false;
                }

                if (!$apAutomationActive) {
                    // Disable PO Automation by default because AP automation is disabled
                    if (!POSetupManager::disablePOAutomationConfig()) {
                        Globals::$g->gErr->addError(
                            'BL03000106', __FILE__ . '.' . __LINE__,
                            "Issue with disbaling PO Automation"
                        );
                    }
                    if($this->companyInfo["ENABLECASHFLOW"] === "T") {
                        Globals::$g->gErr->addError(
                            'BL03000106', __FILE__ . '.' . __LINE__,
                            "Please disable Cash Flow Automation before disabling AP Automation"
                        );
                    }
                }

                if (AutomatedTransactionInteractions::isAutomatedTransactionEnabled()) {
                    $disableEmail = $this->companyInfoCache['DISABLEEMAILVALIDATION'];
                    $emailCheck = true;
                    if ($disableEmail === 'F') {
                        $emailCheck = false;
                    }
                    AutomatedTransactionSetupManager::getActiveProviderObject()
                        ->updateConfigs($apAutomationActive,$emailCheck);
                } else {
                    $companyGuid = \CompanyCacheHandler::getInstance()->getProperty('COMPANYPREF', 'GUID');
                    if (empty($companyGuid)) {
                        throw new SailException(0, "GUID is not set for cny# -");
                    }
                    $apConfigSailHttp = new APConfigSailHttpRequest();

                    $apConfigSailHttp->setCompanyGuid($companyGuid);
                    $endPoint = $apConfigSailHttp->getEndpoint() . APConfigSailHttpRequest::SETTINGS_END_POINT;

                    $bodyParam['network_id'] = APConfigSailHttpRequest::APPlICATION_ID . '+' . $companyGuid . '_0';

                    $bodyParam['suits']['ap_automation_active'] = $apAutomationActive;
                    $payload = json_encode($bodyParam);

                    $slHttpResponse = $apConfigSailHttp->makeHttpRequest('APEMAILSERVICECONFIGSETUP', $endPoint, $payload, 'POST');

                    $msg = "file= " . GetFL() . " | httpStatusCode= " . $slHttpResponse->getResponseCode() . " 
                    | response: " . $slHttpResponse->getResponse() . " 
                    | responseHeader:" . json_encode($slHttpResponse->getResponseHeader()) . " \n";

                    if ($slHttpResponse->getResponseCode() >= SailException::RES_CODE_SERVER_ERROR
                        || $slHttpResponse->getResponseCode() == 0) {
                        $apConfigSailHttp->log($msg);
                        Globals::$g->gErr->addError(
                            'BL03000106', __FILE__ . '.' . __LINE__,
                            "Issue with the end point "
                        );
                    } else if ($slHttpResponse->getResponseCode() == SailException::RES_CODE_VALIDATION_ERROR) {
                        $apConfigSailHttp->log($msg);
                        Globals::$g->gErr->addError(
                            'BL03000106', __FILE__ . '.' . __LINE__,
                            "Issue with the end point res code"
                        );
                    }
                }
            } else {
                Globals::$g->gErr->addError(
                    'BL03000106', __FILE__ . '.' . __LINE__,
                    "Digital Network Not enabled in this company. please enable first."
                );
            }
        }
    }

    /**
     * This function checks whether we should update the Verbosity level
     * In case new hour perios is different from old hour period- we should update it
     *
     * @param string $newActiveTime
     *
     * @return bool
     */
    public function checkIfVerbosityUpdateRequired(string $newActiveTime): bool {
        // we need to extract the previous number of hours set for verbosity level
        [, $activeTime] = preg_split('/ for /', $this->companyInfo["VERBOSITYLEVEL"]);
        preg_match('/[0-9]+h/', $activeTime, $match);
        $previousActiveTime = substr($match[0], 0, -1);

        return $previousActiveTime != $newActiveTime;
    }

    /**
     * @param array $field
     *
     * @return string
     */
    public function getDigitalNetworkSyncStatus(&$field) {
        /** @var DigitalNetworkSyncQueueManager $digitalNetworkQueueMgr */
        $digitalNetworkQueueMgr = Globals::$g->gManagerFactory->getManager('digitalnetworksyncqueue');
        $value = $digitalNetworkQueueMgr->getDigitalNetworkSyncStatus($field);
        $field['advAuditEnable'] = AdvAuditTracking::isCnyTracked();

        return $value;
    }

    /**
     * @return bool
     */
    public function canSwitchAT() : bool
    {
        $qry = "SELECT COUNT(1) CNT FROM fifodispatcherqueue WHERE cny# = :1 AND type in ('W', 'F', 'E') AND state = 'Q'";
        $cnt = QueryResult(array($qry, GetMyCompany()));
        $cnt = intval($cnt[0]['CNT']);
        if ($cnt !== 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param array $field
     *
     * @return string
     */
    public function getAutomatedTxnStatus(array &$field): string {
        $value = 'NONE';
        $field['edit'] = 2;
        // Get the Module Preferences
        $atrxSetup = EntityManager::GetListQuick('automatedtransactionsetup',['STATE'],
            ['MODULEKEY' => AutomatedTransactionInteractions::MODULEID, 'PROVIDER' => AutomatedTransactionSetupManager::PROVIDER_SAIL]);
        GetModulePreferences(Globals::$g->kSTXid, $stxModulePreferences);

        if (!empty($stxModulePreferences['MODULE_CONFIGURED']) && $stxModulePreferences['MODULE_CONFIGURED'] == 'T'
            && empty($atrxSetup)) {
            $value = 'STX';
        } else if ( !empty($atrxSetup[0]['STATE']) &&
            in_array($atrxSetup[0]['STATE'],
                [AutomatedTransactionSetupManager::STATE_INPROGRESS,
                    AutomatedTransactionSetupManager::STATE_SUBSCRIBED])){
            $value = 'SAIL';
            $field['edit'] = 1;
        } else {
            $value = 'NONE';
        }
        $field['advAuditEnable'] = AdvAuditTracking::isCnyTracked();

        return $value;
    }

    /**
     * Check if any of the whencreated value is empty in location/ vendor
     * @return bool
     */
    public function isAuditColumnEmpty() {
        /* Lets check if we any whencreated emoty in Location */
        $LocationMgr = Globals::$g->gManagerFactory->getManager('location');
        $filter = array(
            'selects' => array(
                'RECORDNO'
            ),
            'filters' => array(
                array(
                    array('WHENCREATED', 'IS NULL'),
                    array('RECORDNO', 'CASE_CONDITION', array('ROWNUM = 1'))
                )
            ),
            'usemst' => true
        );
        $locationEmptyAudit = $LocationMgr->getList($filter);
        if (!empty($locationEmptyAudit)) {
            return true;
        }

        /* Lets check if we any whencreated emoty in Vendor */
        $vendorMgr = Globals::$g->gManagerFactory->getManager('vendor');
        $filter = array(
            'selects' => array(
                'RECORDNO'
            ),
            'filters' => array(
                array(
                    array('WHENCREATED', 'IS NULL'),
                    array('RECORDNO', 'CASE_CONDITION', array('ROWNUM = 1'))
                )
            ),
            'usemst' => true
        );
        $vendorEmptyAudit = $vendorMgr->getList($filter);
        if (!empty($vendorEmptyAudit)) {
            return true;
        }

        return false;
    }
}
