<?php

try {
    //extract($_REQUEST, EXTR_PREFIX_ALL, "r");
    // need this, because inspections don't like extract()
    $r_page = Request::$r->page;
    
    // check requested page
    $pages_map = array(
        "underconstruction" => "cs_construction.inc",
        "list" => "sage_id_provisioning_list.inc",
    );
    if (isset($pages_map[$r_page])) {
        $page = $r_page;
    } else if ($r_page === null) {
        // default page
        $page = "underconstruction";
    } else {
        // invalid page; 404
        require "cs_not_found.inc";
        
        exit;
    }
    
    require_once "cs_common.inc";
    
    // just to keep inspections happy...
    global $g_login_id;
    // end inspections
    
    // frame template data
    $t["user"] = html_prepare($g_login_id);
    $t["tool_styles"] = array();
    $t["tool_scripts"] = array();
    
    // load page script
    require $pages_map[$page];
    
} catch (Exception $e) {
    ob_clean();
    
    $t_exception = $e;
    include_once "cs_exception.inc";
}

header("Content-Type: text/html; charset=utf-8");
ob_end_flush();
