<?php

/**
 *
 * class SagePeopleAnalyzerObj is for CStool
 */
abstract class SagePeopleAnalyzerObj extends PartnerAnalyzerObj
{
    /**
     *
     * @return array
     * @throws IAException
     */
    public static function objMapPartnerList(): array
    {
        /**
         * 'IAOBJECT' => 'PROJECT'                                                  //Intacct Object name
         * 'SUBOBJECT' => 'PROJECT',                                                //Object name in subscription table
         * 'SFOBJECT' => 'Project__c',                                                //Salesforce Object name
         * 'IAFIELDS' => array('RECORD#', 'PROJECTID', 'NAME', 'SFDCKEY'),            //Intacct fields to query and comparision
         * 'SFFIELDS' => array('Id', 'Project_ID__c', 'Name', 'Opportunity__c'),    //Salesforce field to query and comparision
         * 'NOTNULLSFFLD' => 'Project_ID__c',                                        //Mandatory field in salesforce
         * 'SUBVID' => 'RECORD#',                                                    //Primary key as Subscrption table
         * 'IAVID' => 'RECORD#',                                                    //Primary key as Intacct (It can be same as SUBVID) * In review
         * 'IAMAPKEY' => array('PROJECTID'),                                        //Intacct Primary key for mapping/matching, must be part of IAFIELDS
         * 'SFMAPKEY' => array('PROJECT_ID__C'),                                    //Salesfoce Primary key for mapping/matching, must be part of SFFIELDS
         * 'ALLOWSYNC' => true,                                                        //Allow sync option in output UI. eg: In the case of one way Sync (Intacct to SF)
         *
         * 'ADDONSUBOBJ' => 'PROJECTOPP',                                            //[O] Additional subscription object
         * 'ADDONIAFLD' => 'SFDCKEY',                                                //[O] Additional subscription object match field in Intacct
         * 'ADDONSFFLD' => 'OPPORTUNITY__C',                                        //[O] Additional subscription object match field in Salesforce
         *
         * 'IAPARENTFLD' => 'PROJECTID',                                            //Parent object field in Intacct eg: PROJECTID for Task
         * 'SFPARENTFLD' => 'Project__r.Project_ID__c',                                //Parent object field in Salesforce eg: Project__r.Project_ID__c for Task__c
         * 'ORDERBY' => 'NAME'                                                      //[O] Table coulumn for orderby the query result in Intacct
         */
        $SPnameSpace = SagePeopleNamespaceHandler::getPrefix();
        return array(
            'SGPPL' => array(
                'Class' => array(
                    'IAOBJECT' => 'CLASS',
                    'SUBOBJECT' => 'CLASS',
                    'SFOBJECT' => $SPnameSpace . 'Class__c',
                    'IAFIELDS' => array('RECORDNO', 'CLASSID', 'NAME'),
                    'SFFIELDS' => array('Id', $SPnameSpace . 'Class_ID__c', 'Name'),
                    'NOTNULLSFFLD' => $SPnameSpace . 'Class_ID__c',
                    'SUBVID' => 'CLASSID',
                    'IAVID' => 'CLASSID',
                    'IAMAPKEY' => array('CLASSID'),
                    'SFMAPKEY' => array($SPnameSpace . 'Class_ID__c'),
                    'ALLOWSYNC' => true,
                ),
                'Department' => array(
                    'IAOBJECT' => 'DEPARTMENT',
                    'SUBOBJECT' => 'DEPARTMENT',
                    'SFOBJECT' => $SPnameSpace . 'Department__c',
                    'IAFIELDS' => array('RECORDNO', 'DEPARTMENTID', 'TITLE'),
                    'SFFIELDS' => array('Id', 'Name'),
                    'NOTNULLSFFLD' => 'Name',
                    'SUBVID' => 'DEPARTMENTID',
                    'IAVID' => 'DEPARTMENTID',
                    'IAMAPKEY' => array('TITLE'),
                    'SFMAPKEY' => array('Name'),
                    'ALLOWSYNC' => true,
                ),
                'Employee' => array(
                    'IAOBJECT' => 'EMPLOYEE',
                    'SUBOBJECT' => 'EMPLOYEE',
                    'SFOBJECT' => 'fHCM2__Team_Member__c',
                    'IAFIELDS' => array('RECORDNO', 'EMPLOYEEID', 'TITLE'),
                    'SFFIELDS' => array('Id', $SPnameSpace . 'Intacct_Employee_Id__c', 'fHCM2__Job_Title__c'),
                    'NOTNULLSFFLD' => $SPnameSpace . 'Intacct_Employee_Id__c',
                    'SUBVID' => 'EMPLOYEEID',
                    'IAVID' => 'EMPLOYEEID',
                    'IAMAPKEY' => array('EMPLOYEEID'),
                    'SFMAPKEY' => array($SPnameSpace . 'Intacct_Employee_Id__c'),
                    'ALLOWSYNC' => false,
                ),
                'Employee Type' => array(
                    'IAOBJECT' => 'EMPLOYEETYPE',
                    'SUBOBJECT' => 'EMPLOYEETYPE',
                    'SFOBJECT' => $SPnameSpace . 'Employee_Type__c',
                    'IAFIELDS' => array('RECORDNO', 'NAME', 'STATUS'),
                    'SFFIELDS' => array('Id', 'Name', $SPnameSpace . 'Status__c'),
                    'NOTNULLSFFLD' => 'NAME',
                    'SUBVID' => 'NAME',
                    'IAVID' => 'NAME',
                    'IAMAPKEY' => array('NAME'),
                    'SFMAPKEY' => array('Name'),
                    'ALLOWSYNC' => true,
                    'ORDERBY' => 'NAME',
                ),
                'Earning Type' => array(
                    'IAOBJECT' => 'EARNINGTYPE',
                    'SUBOBJECT' => 'EARNINGTYPE',
                    'SFOBJECT' => $SPnameSpace . 'Earning_Type__c',
                    'IAFIELDS' => array('RECORDNO', 'NAME', 'METHOD'),
                    'SFFIELDS' => array('Id', 'Name', $SPnameSpace . 'Calculation_Method__c'),
                    'NOTNULLSFFLD' => 'NAME',
                    'SUBVID' => 'NAME',
                    'IAVID' => 'NAME',
                    'IAMAPKEY' => array('NAME'),
                    'SFMAPKEY' => array('Name'),
                    'ALLOWSYNC' => true,
                    'ORDERBY' => 'NAME',
                ),
//                'Intacct Entity' => array(
//                    'IAOBJECT' => 'LOCATIONENTITY',
//                    'SUBOBJECT' => 'LOCATIONENTITY',
//                    'SFOBJECT' => $SPnameSpace . 'Intacct_Entity__c',
//                    'IAFIELDS' => array('RECORDNO', 'LOCATIONID', 'NAME'),
//                    'SFFIELDS' => array('Id', $SPnameSpace . 'Intacct_Entity_ID__c', 'Name'),
//                    'NOTNULLSFFLD' => $SPnameSpace . 'Intacct_Entity_ID__c',
//                    'SUBVID' => 'LOCATIONID',
//                    'IAVID' => 'LOCATIONID',
//                    'IAMAPKEY' => array('LOCATIONID'),
//                    'SFMAPKEY' => array($SPnameSpace . 'Intacct_Entity_ID__c'),
//                    'ALLOWSYNC' => true,
//                ),
                'Location' => array(
                    'IAOBJECT' => 'LOCATION',
                    'SUBOBJECT' => 'LOCATIONENTITY',
                    'SFOBJECT' => $SPnameSpace . 'Intacct_Entity__c',
                    'IAFIELDS' => array('RECORDNO', 'LOCATIONID', 'NAME'),
                    'SFFIELDS' => array('Id', $SPnameSpace . 'Intacct_Entity_ID__c', 'Name'),
                    'NOTNULLSFFLD' => $SPnameSpace . 'Intacct_Entity_ID__c',
                    'SUBVID' => 'LOCATIONID',
                    'IAVID' => 'LOCATIONID',
                    'IAMAPKEY' => array('LOCATIONID'),
                    'SFMAPKEY' => array($SPnameSpace . 'Intacct_Entity_ID__c'),
                    'ALLOWSYNC' => true,
                ),),
        );

    }
}