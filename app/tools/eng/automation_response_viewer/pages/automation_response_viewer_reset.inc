<?php
//=============================================================================
//
//	FILE:        automation_response_viewer_reset.inc
//	AUTHOR:      <PERSON><PERSON><PERSON> R <pras<PERSON><PERSON>.<EMAIL>>
//
//	(C)2024, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================


$r_cny = Request::$r->cny;
$r_stxfile_id = Request::$r->stxfile_id;
$r_view = Request::$r->view;
$errors = [ALERT_TYPE_ERROR => []];

if ($r_stxfile_id) {
    $stxdetails_result = DBRunner::runOnCnyDB("get_stxfiledetails_data", array($r_cny, $r_stxfile_id), $r_cny, true, "automation_response_viewer_util.inc");
}

if ($stxdetails_result === false) {
    // DB Runner error
    addLog("Can not get company information because DBRunner call failed.", LogManager::ERROR);
    throw new IAException("Can not get company information.");
} else if ($stxdetails_result["result"] === false) {
    throw new IAException("Can not get file information. Error message: " . $stxdetails_result["message"]);
}

$stxfiledetails_info = $stxdetails_result["data"];

if (!empty($stxfiledetails_info)) {
    $stxfiledetails_info_data = generateListerForListOfStxfiledetails($stxfiledetails_info, 'stxfiledetails_info');
}

$data['STXFILEID'] = $stxfiledetails_info['STXFILEID'];
$data['FILENOTIFICATION'] = json_encode(json_decode($stxfiledetails_info['FILENOTIFICATION'], true), JSON_PRETTY_PRINT);
$data['DRAFTNOTIFICATION'] = json_encode(json_decode($stxfiledetails_info['DRAFTNOTIFICATION'], true), JSON_PRETTY_PRINT);
$data['FILEDETAILSRESPONSE'] = json_encode(json_decode($stxfiledetails_info['FILEDETAILSRESPONSE'], true), JSON_PRETTY_PRINT);

$data['DRAFTDETAILSRESPONSE'] = null;
if ( isset($stxfiledetails_info['DRAFTDETAILSRESPONSE']) ) {
    // Decode the JSON string to a PHP array
    $dataArray = json_decode($stxfiledetails_info['DRAFTDETAILSRESPONSE'], true);

    // Now decode the cleaned prediction string back to a proper array
    foreach ($dataArray['enhancements'] as &$enhancement) {
        if ( isset($enhancement['prediction']) ) {
            $enhancement['prediction'] = json_decode($enhancement['prediction'], true);
        }
    }

    // Beautify the JSON
    $data['DRAFTDETAILSRESPONSE'] = json_encode($dataArray, JSON_PRETTY_PRINT);
}



$t['result'] = $stxfiledetails_info_data;
$t['stxdata'] = $data;
$t['stxfile_id'] = $r_stxfile_id;
$t['view'] = $r_view ?? 0;

$t["main"] = "automation_response_viewer.tpl.inc";
$t["title"] = "Automation Response Viewer";


render('cs_frame.tpl.inc', $t);
