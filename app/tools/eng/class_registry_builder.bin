#!/usr/local/php/bin/php -q
<?php

chdir(trim(getenv("PWD")));
define("CLASS_EXTENSION", "cls");

$args = $_SERVER['argv'];
if (!in_array(sizeof($args), array(2))) {
    error_log("Usage: $args[0] registry_file \n");
    die(1);
}
$targetfile=$args[1];

$cmd = "/usr/bin/find . ../xml ../tools ../build -name '*.cls'";
exec($cmd, $filelist);

$map[0] = GenerateLowerToUpperClassMapping($filelist);
$map[1] = array_flip($map[0]);
$map[2] = GenerateClassToFileMapping($filelist);

ConvertStructureToText('lower_to_upper_map', $map[0], $template);
ConvertStructureToText('upper_to_lower_map', $map[1], $template);
ConvertStructureToText('class_to_file_map', $map[2], $template);

ReplaceIntoFile($targetfile, 'CLASS_REGISTRY_BUILDER', $template);

# exit()

/**
 * @param string $filename
 * @param string $marker
 * @param mixed $data
 */
function ReplaceIntoFile($filename, $marker, $data) {

    static $dsClassRegistryName = 'ds_class_registry.inc';

    $buffer = file_get_contents($filename);

    if ( false === $buffer || strpos($buffer, $dsClassRegistryName) === false ) {
	    $buffer = null;
    }

	if (!$buffer) {
		$buffer = "<?\n/* @BEGIN_${marker}@ */\n/* @END_${marker}@ */\n\n";
		$buffer .= "if ( stream_resolve_include_path('$dsClassRegistryName') ) {\n"
            . "    include '$dsClassRegistryName';\n"
	        . "}\n";
	}

	$regexp = "/(\/\* @BEGIN_${marker}@ \*\/).*(\/\* @END_${marker}@ \*\/)/s";
	if (is_array($data)) {
		$data = $data['outline']['start'].join("\n",$data['data']).$data['outline']['end'];
	}
	$replace_str = "\\1\n\n".$data."\n\n\\2";
	$buffer = preg_replace( $regexp, $replace_str, $buffer );

	$fd = @fopen($filename, "w+");
	if ($fd === false) {
        error_log("Could not open file for writing: " . $filename);
        die(1);
    }
	fwrite($fd, $buffer, strlen($buffer));
	fclose($fd);

}

/**
 * @param array $filelist
 *
 * @return array
 */
function GenerateLowerToUpperClassMapping($filelist) {
	$map = array();

	foreach($filelist as $file) {
        $fileElements =  explode('/', $file);
        preg_match("/(.*).".CLASS_EXTENSION."/", end($fileElements), $matches);
		if ($map[strtolower($matches[1])]) {
            echo 'Filename collision: ' . $map[strtolower($matches[1])] . "\n";
        }
		$map[strtolower($matches[1])] = $matches[1];
	}

	asort($map);
	return $map;
}

/**
 * @param array $filelist
 *
 * @return array
 */
function GenerateClassToFileMapping($filelist) {
	$map = array();

	foreach($filelist as $file) {
        $fileElements =  explode('/', $file);
        preg_match("/(.*).".CLASS_EXTENSION."/", end($fileElements), $matches);
		$map[$matches[1]] = $matches[0];
	}

	asort($map);
	return $map;
}

/**
 * @param string $key
 * @param array $value
 * @param array $template
 */
function ConvertStructureToText($key, $value, &$template) {

	if (!$template) {
		$template['outline']['start'] = "global \$class_registry;\n\$class_registry = array(\n";
		$template['outline']['end'] = "\n); ";
	}

	foreach($value as $lkey => $lvalue) {
        /** @noinspection PhpUndefinedVariableInspection */
        $valuestr .= "\n\t\t\t'${lkey}' => '${lvalue}',";
	}

    /** @noinspection PhpUndefinedVariableInspection */
    $template['data'][] = "\t'${key}' => array( ${valuestr}
	),";

}


