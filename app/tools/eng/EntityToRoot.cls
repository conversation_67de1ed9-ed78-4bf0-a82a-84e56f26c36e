<?php
/**
 * LICENSE:
 * (C)1999-2014 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 *
 * <AUTHOR> <<EMAIL>>"
 * @copyright 2016 Intacct Corporation
 */
require 'DataFixToolsManager.cls';

/**
 * Update the Location Key which provide all kind of functions
 *
 * @access public
 * <AUTHOR>
 */
class EntityToRoot extends DataFixToolsManager
{
    const ACTION_HOME = 'Home';
    const ACTION_ANALYZE = 'Analyze';
    const ACTION_COMMIT = 'Commit';
    const ACTION_CSTOOLHOME = 'Redirect';
    /** @var array $finalCsvData  */
    private $finalCsvData = array();
    /** @var array $diffIdsArr  */
    private $diffIdsArr = array();
    /** @var array $childDiffIds  */
    private $childDiffIds = array();
    /** @var bool $moveAllObjects  */
    private $moveAllObjects = false;
    /** @var bool $isAnalyzed  */
    private $isAnalyzed = false;
    /** @var int $totalRecCount  */
    private $totalRecCount = 0;

        /**
         * @param string $tool CS Tool name
         *
         * @access public
         */
        function __construct($tool)
        {
            parent::__construct($tool);
        }

        /**
         * Entire Tool action will be taken care from here
         *
         * @param string $action for the tool
         *
         * @access public
         */
        public function processAction($action)
        {
            switch ($action) {
            case self::ACTION_HOME:
                $this->home();
                break;
            case self::ACTION_ANALYZE:
                $this->analyze();
                break;
            case self::ACTION_COMMIT:
                $this->commit();
                break;
            case self::ACTION_CSTOOLHOME;
                $this->redirect();
                break;
            }
        }

            /**
         * Home screen for the tool after validating the Intacct user and Tool
         * Authorization
         *
         * @access private
         */
        private function home()
        {
            $this->deleteFileName();
            /** @noinspection PhpUnusedLocalVariableInspection */
            $_object = strtoupper(Request::$r->_object);
            ?>
            <form action="EntityToRoot.phtml?" method="post"
                  enctype="multipart/form-data" name="form1" id="form1">
                <center>
                <br/>
                <b>ENTITY TO ROOT</b>
                <br/>
                <hr/>
                <br/>
                <b>Company ID:</b>
                <label><?php echo $this->compinfo['TITLE']; ?></label>
                <b>&nbsp;&nbsp;Object Name</b>
                <select name=".object" onchange="checkObject(this.value)">
                    <option value="dummyObj">SELECT AN OBJECT</option>
                    <option value="Class">CLASS</option>
                    <option value="Icitem">ICITEM</option>
                    <option value="Bankaccount">BANKACCOUNT</option>
                    <option value="Employee">EMPLOYEE</option>
                   <!-- <option value="Memorizedreports">MEMORIZEDREPORTS</option> -->
                    <option value="Vendor">VENDOR</option>
                    <option value="Customer">CUSTOMER</option>
                    <option value="Project">PROJECT</option>
                    <option value="Baseaccount">GL ACCOUNT</option>
                </select>
                <br>
                <br>

                <b>Import from CSV</b> <input id="csv" type="file" name="csv"/>
                <font color='#ff8c00'><b>Match with record# </b></font>
                <input type="radio" name=".radioid" id="mrecord" value="matchrecord" onclick="getRadioAction
                ('matchrecord')"/>
                <font color='#ff8c00'><b>Match with ID </b></font>
                <input type="radio" name=".radioid" id="mid" value="matchid"
                onclick="getRadioAction('matchid')"/>
                <br/><br/>

                <b><?php echo "OR" ?></b>
                <br><br>
                <b>Move all the object records</b> <input type="checkbox" name=".checkbox" value="checkbox">
                <font color='red'>****</font>
                <br/><br/>
                <br/><br/>
                <input type="hidden" id="actionname" name=".action" value="">
                <input type="hidden" id="cny" name=".r" value="">
                <input type="submit" name=".debug" value="Analyze"
                onclick="setAction('Analyze','<?php echo Request::$r->_r; ?>');"/>
                <input type="submit" name=".save" value="Commit"
                onclick="setAction('Commit','<?php echo Request::$r->_r; ?>'); "/>
                <br/><br/>
                <hr/>
                <br/></CENTER>

            </form>
            <div align="left">
            <b> NOTE: <br/><br/> </b>
            <b>* It is mandatory to either </b><font color='red'><b>
                    (Upload a csv file or choose Move all the object records) </b></font> .<b><br/>
            <b>* <font color='red'>****</font> If you choose <font color='red'><b> Move all the object records </b>
                </font>,then all records of selected object will be moved to Root level<font
                 color='red'><b></b></font>.<b><br/><b>* It is mandatory to choose either </b><font color='red'><b>
                (Match with record# or Match with ID) </b></font> .<b><br/>
            <b>* If you choose <font color='red'><b> Match with record# </b></font>, then make sure in CSV First
                 Column always contains <font color='red'><b>'RECORD#' values</b></font>.<b><br/>
            <b>* If you choose <font color='red'><b> Match with ID </b></font>, then make sure in CSV First Column
                 always contains</b><font color='red'><b>'OBJECT_ID' values</b></font></b><br/>
            <b>* <font color='red'><b>'Analyze'</b></font><b> button will validate the CSV input without
                    committing any Result .</b><br/>
            <b>* After 'Analyze' if you don't get any <font color='red'><b>'ERROR'</b></font> then, for commit, Please
                Click  <font color='red'><b>'COMMIT'</b></font> button .<br/></b>
            <b>* CSV must not contain <font color='red'><b>'non-ASCII'</b></font> character in it.<br/></b>
            </div>
            <?php
        }


        /**
        * Analyzing of the csv to validate whether contents are correct or not
        *
        * @access private
        */
        private function analyze()
        {
            $this->isAnalyzed = true;
            if (isset(Request::$r->_checkbox)) {
                $this->moveAllObjects = true;
            }
            $this->isAnalyzed =  $this->isAnalyzed && $this->displayAnalyze();
        }

        /**
        * Dislaying the Ids need to be updated.
        *
        * @access public
        *
        * <AUTHOR> Singh<<EMAIL>>
        * @return bool
        */
        public function displayAnalyze()
        {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $cny = Request::$r->_r;
            $object = strtoupper(Request::$r->_object);
            $checked = Request::$r->_radioid;
            $isCommit = false;

            if (!($result = $this->validateCsvData($isCommit))) {
                return false;
            }

            $diffFlag = 0;

            if (!empty($this->diffIdsArr)) {
                       $diffFlag = 1;
                       $processedData = $this->diffIdsArr;
            } else {
                       $processedData = $this->finalCsvData;
            }

            if ($checked == 'matchrecord') {
                    $id = "RECORD#";
            } else {
                    $id = "ID";
            }

            ?>
            <form action="EntityToRoot.phtml?.r=<?php echo Request::$r->_r; ?>" method="post"
                  enctype="multipart/form-data" name="form1" id="form1">
                <br/>
                <?php
                // This will print all the valid IDs which needs to be updated
                echo "<hr/>";
                $error = false;
                if (($this->moveAllObjects)) {
                    if ($result > 0) {
                        echo "<br/>";
                        $this->out(
                                "<p>\n<font color='#1e90ff'><b>$result Ids will be updated. Press COMMIT if you want 
                         to proceed.</b></font></p>"
                        );
                    } else if ( $result == 'Zero') {
                        $this->out(
                            "<br><p>\n<font color='#1e90ff'><b>All records are moved already</b></font></p>"
                        );
                    }
                    $error = true;
                } else {
                    $this->out(
                        "<p>\n<font color='#1e90ff'><b>Please wait while we validate the ID from the CSV....</b>
                        </font></p>"
                    );
                    echo "<hr/>";
                    if ($diffFlag) {
                        $this->out(
                            "<b><font color='red'>Following is the list of object Ids that  either does not 
                             exists in the system / is already moved.</font></b>"
                        );
                        $error = true;
                    } else if (!empty($this->childDiffIds)) {
                        $processedData = $this->childDiffIds;
                        $this->out(
                            "<b><font color='red'>Following Objects records can not be moved to Root as their 
                            parentkeys are still at the entity level and not included in the attached csv.
                            </font></b>"
                        );
                        $error = true;
                    } else {
                        $this->out(
                            "<b><font color='green'>Following is the list of object Ids to be moved, If you
                             commit.</font></b>"
                        );
                    }

                    ?><br/>
                    <table border="1">
                        <tr>
                            <td style="width: 200px;"><?php $this->out("<b> $object $id</b>"); ?>
                            </td>
                        </tr>
                    </table>
                    <?php
                    foreach ( $processedData as $val) {
                        ?>
                        <table border="1">
                            <tr>
                                <td style="width: 200px;"> <?php echo $val; ?> </td>
                            </tr>
                        </table>
                        <?php
                    } ?>
                    <br/>
                    <?php
                }
                    echo "<hr/><br/>";
                ?>
                <input type="hidden" id="actionname" name=".action" value="">
                <input type="hidden" id="index" name=".index" value="">
                <input type="hidden" id="commit" name=".commit" value="">
                <input type="hidden" id="checked" name=".radioid" value="<?php echo Request::$r->_radioid; ?>">
                <input type="hidden" id="object" name=".object" value="<?php echo Request::$r->_object; ?>">
                <input type="hidden" id="checkbox" name=".checkbox" value="<?php echo Request::$r->_checkbox; ?>">
                <input type="hidden" id="csv" name=".csv" value="

                <?php
                if ( !$error ) {
                    $pushfilepath = INTACCTtempnam("/tmp", "EntityToRoot");
                    $filepath = base64_encode($pushfilepath);
                    if ( !move_uploaded_file($_FILES['csv']['tmp_name'], $pushfilepath)) {
                        echo("There is a problem with uploading a file");
                    }
                    echo $filepath;
                }
                ?>">
                <input type="submit" name=".back" value="Go Back" onclick="HomeScreen('')"/>
                <?php
                if ($diffFlag == 0 && $result != 0 && empty($this->childDiffIds)) {
                            ?>
                       <input type="submit" name=".save" value="Commit"
                       onclick="setAction('Commit','<?php echo Request::$r->_r; ?>'); "/><br/><br/>
                            <?php
                }
                ?>
                <input type="submit" name=".home" value="Home" onclick="HomeScreen('Redirect')"/>
            </form>
        <?php
            return true;
        }

        /**
        * Validating the given csv input
        * Reading csv data and validating Ids
        * @param bool $isCommit flag for deciding for commit
        * @access private
        * @return int
        */
        private function validateCsvData(/** @noinspection PhpUnusedParameterInspection */ $isCommit)
        {
            $_object = strtoupper(Request::$r->_object);
            $checked = Request::$r->_radioid;
            $dont_allow_object = array('CLASS','ICITEM','BASEACCOUNT');

            if ($_object == 'DUMMYOBJ') {
                $this->status = $this->printMessage("Please select An Object", false);
                return $this->status;
            }

            $file = $_FILES['csv']['tmp_name'];

            if (isset(Request::$r->_csv) && empty($file)) {
                $file = base64_decode(Request::$r->_csv);
            }

            if (empty($file) && !($this->moveAllObjects)) {
                $this->status = $this->printMessage("No selection made. Please either UPLOAD a csv file or
                          Choose MOVE ALL OBJECT RECORDS", false);
                return $this->status;
            }

            if ($this->moveAllObjects) {
                $querystament = "Select count(1) AS COUNT FROM $_object WHERE CNY# = :1 
                                      AND " . $this->getObjectDetails($_object)[$_object][0] . " IS NOT NULL";
                $result = QueryResult(array($querystament, Request::$r->_r));
                $this->totalRecCount = $result[0]['COUNT'];
                if ($this->totalRecCount == 0) {
                    $this->totalRecCount = 'Zero';
                }
                return $this->totalRecCount;
            } else if ($file != '' && !($this->moveAllObjects)) {
                $bit = $this->readCSV($file);
                $csvArray = $bit[1];
                if (empty($csvArray)) {
                    $this->status = $this->printMessage("Empty File Uploaded", false);
                    return $this->status;
                }

                if ($bit[0] == 0) {
                    $this->status = $this->printMessage("Please make sure that CSV does not contain more than
                                999 rows. <br/>If you have more than 999 rows, 
                                then please provide the CSV into parts", false);
                    return $this->status;
                }
            }

            if ($checked != 'matchrecord' && $checked != 'matchid') {
                $this->status = $this->printMessage("Please select either Match with record# or Match with ID.", false);
                return $this->status;
            }

            /** @noinspection PhpUndefinedVariableInspection */
            foreach ( $csvArray as $data) {
                $csvData[] = $data;
                $finaldata = implode("','", $csvData);
            }

            if ($checked == 'matchrecord') {
                $columnbit = $this->getObjectDetails($_object)['RECORD#'];
            } else {
                $columnbit = $this->getObjectDetails($_object)['ID'];
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $querystament = "Select " . $columnbit . " FROM $_object WHERE CNY# = :1 and 
                                " . $this->getObjectDetails($_object)[$_object][0] . " is not null 
                                and " . $columnbit . " in ('" . $finaldata . "')";

            $result = QueryResult(array($querystament, Request::$r->_r));


            $finalResult = array();
            foreach ( $result as $value) {
                $finalResult[] = $value[$columnbit];
            }

            if (empty($finalResult)) {
                /** @noinspection PhpUndefinedVariableInspection */
                $this->diffIdsArr = $csvData;
            } else {
                /** @noinspection PhpUndefinedVariableInspection */
                $this->diffIdsArr = array_diff($csvData, $finalResult);
            }
            $this->diffIdsArr = array_unique($this->diffIdsArr);
            $this->finalCsvData = array_unique($csvData);

            if (!in_array($_object, $dont_allow_object)) {
                $queryStat = " Select ob1.$columnbit as childkey, ob2.$columnbit as parentkey from $_object ob1, 
                                $_object ob2 where ob1.cny# = :1 and ob1.cny#=ob2.cny# and ob1.parentkey is not null 
                                and ob1." . $this->getObjectDetails($_object)[$_object][0] . " is not null and 
                                ob2." . $this->getObjectDetails($_object)[$_object][0] . " is not null 
                                and ob2.record# = ob1.parentkey AND ob1.$columnbit  IN ('" . $finaldata . "')";


                $parentKeys = QueryResult(array($queryStat, Request::$r->_r));

                $parentKeysArr = [];
                foreach ($parentKeys as $value) {
                    $parentKeysArr[$value['CHILDKEY']] = $value['PARENTKEY'];
                }

                $parentDiffIds = array_diff($parentKeysArr, $this->finalCsvData);

                if (!empty($parentDiffIds)) {
                    foreach ($parentDiffIds as $key => $val) {
                        $this->childDiffIds[] = $key;
                    }
                }
            }

            return $this->status;
        }

        /**
        * Reading from CSV
        * @param int $file
        * @access private
        * @return array    CSV Content
        */
        private function readCSV($file)
        {
            $row = 0;
            $bit = 1;
            $_handle = fopen($file, "rb");
            while ($data = fgetcsv($_handle, 50000, ",", '"', "\n")) {
                 if (count($data) > 1) {
                    $this->printMessage("Uploaded file format is not as expected", false );
                    exit();
                }

                if ($row == 999) {
                    $bit = 0;
                    break;
                }
                if ($data) {
                        $csvData[] = trim($data[0]);
                }
                $row++;

            }

            /** @noinspection PhpUndefinedVariableInspection */
            return array( $bit, $csvData);
        }

        /**
         * Get all the object details and their respective table and column
         *
         * @param string $obj Object name
         *
         * @access private
         * @return array for the object
         */
        private function getObjectDetails($obj)
        {
            $objectar = array(
                'CLASS' => array(
                    'CLASS' => array('LOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'CLASSID',
                ),
                'ICITEM' => array(
                    'ICITEM' => array('LOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'ITEMID',
                ),
                'BANKACCOUNT' => array(
                    'BANKACCOUNT' => array('LOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'ACCOUNTID',
                    'HASCHILD' => array(
                        'CHILDTABLE' => 'CONTACTHEAD',
                        'COLUMNNAME' => array('LOCATIONKEY'),
                        'RECORD#' => 'RECORD#',
                        'PARENTCOLNAME' => array('CHECKINFOCONTACTKEY'),
                    ),
                ),
                'EMPLOYEE' => array(
                    'EMPLOYEE' => array('MELOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'EMPLOYEEID',
                    'HASCHILD' => array(
                        'CHILDTABLE' => 'CONTACTHEAD',
                        'COLUMNNAME' => array('LOCATIONKEY'),
                        'RECORD#' => 'RECORD#',
                        'PARENTCOLNAME' => array('CONTACTKEY'),
                    ),
                ),
                'VENDOR' => array(
                    'VENDOR' => array('LOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'VENDORID',
                    'HASCHILD' => array(
                        'CHILDTABLE' => 'CONTACTHEAD',
                        'COLUMNNAME' => array('LOCATIONKEY'),
                        'RECORD#' => 'RECORD#',
                        'PARENTCOLNAME' => array('DISPLAYCONTACTKEY', 'PAYTOKEY', 'RETURNTOKEY'),
                    ),
                    'SUPDOC' => '1',
                ),
                'CUSTOMER' => array(
                    'CUSTOMER' => array('LOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'CUSTOMERID',
                    'HASCHILD' => array(
                        'CHILDTABLE' => 'CONTACTHEAD',
                        'COLUMNNAME' => array('LOCATIONKEY'),
                        'RECORD#' => 'RECORD#',
                        'PARENTCOLNAME' => array('DISPLAYCONTACTKEY'),
                    ),
                    'SUPDOC' => '1',
                ),
                'PROJECT' => array(
                    'PROJECT' => array('LOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'PROJECTID',
                    'HASCHILD' => array(
                        'CHILDTABLE' => 'CONTACTHEAD',
                        'COLUMNNAME' => array('LOCATIONKEY'),
                        'RECORD#' => 'RECORD#',
                        'PARENTCOLNAME' => array('DISPLAYCONTACTKEY'),
                    ),
                    'SUPDOC' => '1',
                ),
                'BASEACCOUNT' => array(
                    'BASEACCOUNT' => array('LOCATIONKEY'),
                    'RECORD#' => 'RECORD#',
                    'ID' => 'ACCT_NO',
                ),
    //                        'MEMORIZEDREPORTS' => array(
    //                            'MEMORIZEDREPORTS' => array('LOCATIONKEY'),
    //                             'RECORD#' => 'RECORD#',
    //                             'ID' => 'NAME',
    //                             'MULTIWHERECLAUSE' => " USERREPORTPFLAG = 'F'  AND MODULEKEY = '2.GL' ",
    //                        ),
            );
            return $objectar[$obj];
        }

        /**
         * Commiting to DB after successfull validation of Object ID
         *
         * @access private
         */
        private function commit()
        {
            $object = strtoupper(Request::$r->_object);
            $isCommit = true;

            if (!($this->moveAllObjects)) {
                $this->moveAllObjects = Request::$r->_checkbox;
            }

            if (!($this->isAnalyzed)) {
                $this->validateCsvData($isCommit);
            }

            $this->deleteFileName();
            if (empty($this->diffIdsArr)) {
                $result = $this->updateLocationKey();
                $ok = $result[0];
                $count = $result[1];

                if ($ok) {
                    $memo = addoraslashes(
                        "Customer support user " . $this->userName . " has invoked Engineering tool i.e.(Entity To 
                                    Root) to " .
                        "Update the " . strtoupper(Request::$r->_object) . "ID in " . getenv('IA_SERVER_NAME') . " server with the" .
                        " tracking id " . Globals::$g->perfdata->getSerialnbr() . " at " . date("m/d/Y h:i:s")
                    );
                    LogCSAction("CSTOOLS_TOOLNAME_ENG_ENT_TO_ROOT", $this->userName, $memo, Request::$r->_r);

                    $countArr = !empty($this->finalCsvData) && !($this->moveAllObjects) ? count($this->finalCsvData) :
                        $this->totalRecCount;

                    if ($countArr < $count) {
                        $message = "$countArr records has been updated for $object Object.";
                    } else {
                        $message = "$count out of $countArr records has been updated for $object Object.";
                    }
                    $this->printMessage($message, true);
                } else {
                    $this->printMessage('While updating $object Object. Please analyze before committing', false);
                }
            } else {
                $msg = "While updating $object Object. Some of the IDs doesnot exists in the system. Please analyze 
                    before committing";
                $this->printMessage($msg, false);
            }
        }

        /**
         * Core function afer validating each and every item from CSV to company
         * data, we are running this update migration to move object from Entity
         * To Root
         *
         * @access private
         * @return int[]    $ok
         **/
        private function updateLocationKey()
        {
            XACT_BEGIN('EntityToRoot');
            StartTimer('EntityToRoot');
            $cny = Request::$r->_r;
            $object = strtoupper(Request::$r->_object);
            $checked = Request::$r->_radioid;
            $objectData = $this->getObjectDetails($object);

            if ($checked == 'matchrecord') {
                $id = $objectData['RECORD#'];
            } else if ($checked == 'matchid') {
                $id = $objectData['ID'];
            }

            $finaldata = implode("','", $this->finalCsvData);
            /** @noinspection PhpUnusedLocalVariableInspection */
            $tableName = $object;
            $column = $objectData[$object][0];
            /** @noinspection PhpUnusedLocalVariableInspection */
            $qryArray = array();

            $qry[0] = "UPDATE " . $object . " SET " . $column . " = null WHERE cny# = :1 AND " . $column . " is
                         not null";

            if (!($this->moveAllObjects) && !empty($finaldata)) {
                /** @noinspection PhpUndefinedVariableInspection */
                $qry[0] .= " AND " . $id . " IN ('" . $finaldata . "')";
            }

    //                    if (isset($objectData['MULTIWHERECLAUSE'])) {
    //                        $qry[0] .= " AND " . $objectData['MULTIWHERECLAUSE'];
    //                    }

            $qry[] = $cny;
            $ok = ExecStmt($qry);

            $querystament = "SELECT count(1) AS COUNT FROM $object WHERE cny# = :1 AND " . $column . " is null";

            if (!($this->moveAllObjects) && !empty($finaldata)) {
                /** @noinspection PhpUndefinedVariableInspection */
                $querystament .= " AND " . $id . " IN ('" . $finaldata . "')";
            }

            $result = QueryResult(array($querystament, $cny));
            $count = $result[0]['COUNT'];


            if (isset($objectData['HASCHILD'])) {
                foreach ( $objectData['HASCHILD']['PARENTCOLNAME'] as $colName) {

                    $childTableQry[0] = "UPDATE " . $objectData['HASCHILD']['CHILDTABLE'] .
                        " SET " . $objectData['HASCHILD']['COLUMNNAME'][0] . " = null WHERE cny#
                             = :1 AND " . $objectData['HASCHILD']['COLUMNNAME'][0] . " is not null AND RECORD# IN ( SELECT "
                        . $colName . " FROM " . $object . " WHERE cny# = " . $objectData['HASCHILD']['CHILDTABLE'] . ".cny# ";

                    if (!($this->moveAllObjects) && !empty($finaldata)) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        $childTableQry[0] .= " AND " . $id . " IN ('" . $finaldata . "')";
                    }

                    $childTableQry[0] .= " ) ";

                    $childTableQry[1] = $cny;

                    $ok = $ok && ExecStmt($childTableQry);
                }

            }

            if (isset($objectData['SUPDOC'])) {

                $supDocQry[0] = "UPDATE SUPDOC  SET LOCATIONKEY = null WHERE cny# = :1 AND LOCATIONKEY is not 
                                            null AND RECORD# IN (SELECT DOCUMENTID FROM SUPDOCMAPS WHERE 
                                            cny# = SUPDOC.cny# AND TRANSACTIONTYPE = '" . $object . "' ";

                if (!($this->moveAllObjects) && !empty($finaldata)) {
                    $supDocQry[0] .= " AND RECORDID  IN ('" . $finaldata . "')";
                }

                $supDocQry[0] .= " ) ";

                $supDocQry[] = $cny;

                $ok = $ok && ExecStmt($supDocQry);

                $supDocDataQry[0] = "UPDATE supdocdata SET locationkey = NULL WHERE  cny# = :1  AND 
                            locationkey is not null AND documentkey IN (SELECT documentid FROM supdocmaps WHERE cny# = 
                            supdocdata.cny# and TRANSACTIONTYPE = '" . $object . "' ";

                if (!($this->moveAllObjects) && !empty($finaldata)) {
                    $supDocDataQry[0] .= " AND RECORDID  IN ('" . $finaldata . "')";
                }

                $supDocDataQry[0] .= " ) ";

                $supDocDataQry[] = $cny;


                $ok = $ok && ExecStmt($supDocDataQry);

                $supDocMapsQry[0] = "UPDATE supdocmaps SET locationkey = NULL WHERE  cny# = :1  and
                             LOCATIONKEY is not null AND TRANSACTIONTYPE = '" . $object . "' ";

                if (!($this->moveAllObjects) && !empty($finaldata)) {
                    $supDocMapsQry[0] .= " AND RECORDID  IN ('" . $finaldata . "')";
                }

                $supDocMapsQry[] = $cny;

                $ok = $ok && ExecStmt($supDocMapsQry);
            }

            $ok = $ok && XACT_COMMIT('EntityToRoot');


            if (!$ok) {
                XACT_ABORT('EntityToRoot');
            }

            StopTimer('EntityToRoot');

            return array($ok, $count);
        }
}
