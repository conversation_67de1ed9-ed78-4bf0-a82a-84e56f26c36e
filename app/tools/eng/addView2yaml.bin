#!/usr/local/php/bin/php -q
<?php
/**
 * Usage
 *
 * read api json file, create hash and output hash
 *   ./addView2yaml.bin object=<api object name>
 *
 * read api json file, create hash and update history file for object with hash
 *   ./addView2yaml.bin object=<api object name> module=<openapispec module path> revision=<revision>
 *
 *
 * Where:
 *   <api object name> = api object name, such as bill-summary
 *   <openapispec module path> = optional: folder under openapisec where the history file is located, such as ap
 *   <revision> is the revision number, i.e. s1, s2, etc.
 */

// this gives us the path to link, which is the app root.
$linkParent = wheresLinkLive();

define('INTACCTCONTEXT', "true");
require_once('util.inc');
InitGlobalConstants();

if (Globals::$g->islive) {
  die("ERROR: This tool is unavailable in Production");
}


//convert command lines to _REQUEST array;

if (isset($argv) && count($argv) > 1) {
  foreach ( $argv as $key => $value ) {
    $thisArg = explode("=", $value);
    if (count($thisArg) == 2) {
      $_REQUEST[$thisArg[0]] = $thisArg[1];
    }
  }
} else {
  showHelp("\nError: no arguments passed\n\n");
}

$object = $_REQUEST['object'] ?? '';
$module = $_REQUEST['module'] ?? '';
$revision = $_REQUEST['revision'] ?? '';

if ($object === "") {
  showHelp("\nError: missing object argument\n\n");
}

// Need both
if ($module === "" || $revision === "") {
  showHelp("\nError: both module and revision arguments are required\n\n");
}

// now add the path to the file
$schemaFile = $linkParent . 'build/components/' . $object . '.' . $revision . '.schema.json';
// make sure the file can be found.  no need to even start if not.
if (is_file($schemaFile)) {
  // file found, now do the magic
  $historyFile = $linkParent . 'source/openapispec/' . $module . '/history/' . $object . '.schema.history.yaml';
  if (is_file($historyFile)) {
    try {
      $parseLib = $linkParent . 'link/private/lib/symfony-yaml/';
      foreach (glob($parseLib . '*.php') as $incl) {
        require_once($incl);
      }
      $yaml = Symfony\Component\Yaml\Yaml::parseFile($historyFile);
      // check the version piece in the history file for the view info.
      // systemViews:
      //    systemfw1:
      //      revision: s1
      //      hash: '0'
      //  uiMetadataHash: '0'
      //  
      //           revision: s1
      //                 hash: '0'
      //                   uiMetadataHash: '0'
      // hash is for schema, uiMetadataHash is for uimeta, views are undersystemViews, then view name then hash:
      if (array_key_exists($revision, $yaml)) {
        // now check for systemViews
        if (array_key_exists('systemViews', $yaml[$revision])) {
	  // check for systemfw1 view, if not there, add it.
          if (!array_key_exists('systemfw1', $yaml[$revision]['systemViews'])) {
            $yaml[$revision]['systemViews']['systemfw1'] = ['revision' => 's1', 'hash' => '0'];
          }
	} else {
          $yaml[$revision]['systemViews']['systemfw1'] = ['revision' => 's1', 'hash' => '0'];
	}
        // now check for uiMetadataHash
        if (!array_key_exists('uiMetadataHash', $yaml[$revision])) {
          $yaml[$revision]['uiMetadataHash'] = '0';
        }
      } else {
        echo "Revision: $revision not found in history file, you need the revision to be in the history file for this to work\n";
      }
      $newHFC = Symfony\Component\Yaml\Yaml::dump($yaml, 256);
      if (file_get_contents($historyFile) === $newHFC) {
        echo "systemfw1 already exists in history file\n";
      } else {
        writeFile($historyFile, $newHFC);
        echo "File updated: " . $historyFile . "\n\n$newHFC\n";
      }
    } catch ( Exception $ex ) {
      echo "Failed \n";
      print( $ex->getMessage() );
    }
  } else {
      echo "Error: History file: $historyFile not found\n";
  }
} else {
  echo "Error: Schema file: $schemaFile not found";
}

/**
 * @param string $errorMsg
 */
function showHelp($errorMsg = '')
{
  echo $errorMsg;
  echo "\nvalid arguments (all are required):\n";
  echo "\tobject=<api object name> = name of the openAPI object such as bill-summary\n";
  echo "\tmodule=<openapispec module> = folder under openapisec where the history file is located, such as ap. Requires revision argument when used\n";
  echo "\trevision=<revision> is the revision number, i.e. s1, s2, etc. \n";
  exit;
}

/**
 * @param string $fname  file name to write
 * @param string $fdata  date to write to file
 */
function writeFile(string $fname, string $fdata)
{
  $h = fopen($fname, "w");
  if ($h !== false) {
    fwrite($h, $fdata);
    fclose($h);
  } else {
    echo "error opening file: " . getcwd() . $fname;
  }
}

/**
 * @return string  the relative path of the link directory's parent
 * taken from parse_metadata_for_schema
 */
function wheresLinkLive() {
  if (file_exists("./link")) {
    $indirection = "./";
  } elseif (file_exists("../../link")) {
    $indirection = "../../";
  } elseif (file_exists("../../../link")) {
    $indirection = "../../../";
  } else if (file_exists("../../../../link")) {
    $indirection = "../../../../";
  } else {
    die("ERROR: can't find common files in ../../ or ../../..");
  }
  ini_set('include_path', ini_get('include_path') . ":"
      . $indirection . "link/private/inc:"
      . $indirection . "source/*:"
      . $indirection . "link/private/lib/log4php:"
      . $indirection . "link/private/lib/php4:.:"
      . $indirection . "link/private/lib/misc:"
      . $indirection . "link/private/lib/nusoap:"
      . $indirection . "link/private/lib/sforcev2:"
  );
  return $indirection;
}

