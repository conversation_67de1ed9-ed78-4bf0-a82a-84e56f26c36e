/**
 * Created by an<PERSON><PERSON><PERSON><PERSON>.
 */
/*
 FILE:		EntityToRoot.js
 DESCRIPTION:	Functionality of Handling the screen in Entity To Root Tool

 @copyright 1999-2014 Intacct Corporation

 This document contains trade secret data that belongs to Intacct
 corporation and is protected by the copyright laws. Information
 herein may not be used, copied or disclosed in whole or part
 without prior written consent from Intacct Corporation.
 */

function HomeScreen(actionName) {
    var action = document.getElementById('action').value = actionName;
    var index = document.getElementById('index').value = actionName;
    var commit = document.getElementById('commit').value = actionName;
    return true;
}

function setAction(actionName, cny) {
    var action = document.getElementById('actionname').value = actionName;
    var r = document.getElementById('cny').value = cny;
    return true;
}

function getRadioAction(radioaction) {
    var matchrecord = document.getElementById('mid').value = radioaction;
    var matchid = document.getElementById('mrecord').value = radioaction;
    jq('input').click(function (e) {
        if (e.key) {
            jq(this).prop('checked', false);
        }
    });
    return true;
}


