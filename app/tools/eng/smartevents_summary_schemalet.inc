<?php

$kSEOSummaryQueries = [
    'QRY_SEO_GENERAL_STATUS' => [
        'QUERY' =>
            "select count(*) as queued 
                from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s, 
                 company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c                      
                        where s.state = 'Q'
                        and s.type = 'S'
	                    and (c.record# = s.cny# and c.status = 'T')",

        'COLUMNS' => [ 'QUEUED' => 11 ],
        'TITLE'   => 'Current status for all Smart Event jobs:',
    ],

    'QRY_SEO_TOPIC_QUEUED_CURRENT' => [
        'QUERY' =>
            "select topic, queued_current 
                from
                ( select topic, count(*) as queued_current 
                from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s, 
                     company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c                       
                            where s.state = 'Q'
                            and s.type = 'S'
                            and (c.record# = s.cny# and c.status = 'T')
                group by topic
                order by queued_current desc )
             where rownum < 10",

        'COLUMNS'   => [ 'TOPIC' => -45, 'QUEUED' => 1 ],
        'Aggregate' => [
            'gb'     => 'TOPIC',
            'totals' => [
                [ 'QUEUED_CURRENT', 'sum' ],
            ],
        ],
        'Top'       => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_CURRENT', 'DESC' ],
        ],
        'TITLE'     => 'Current topics queued on all Smart Event jobs:',
    ],

    'QRY_SEO_CNY_QUEUED_CURRENT' => [
        'QUERY'   =>
            "select cny#, title, queued_current from
                ( select s.cny#, c.title, count(*) as queued_current
                from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s,
                     company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
                            where s.state = 'Q'
                            and s.type = 'S'
                            and (c.record# = s.cny# and c.status = 'T')
                group by s.cny#, c.title
                order by queued_current desc )
             where rownum < 10",
        'COLUMNS' => [ 'CNY#' => 20, 'TITLE' => -45, 'IN QUEUE' => 1 ],
        'Top'     => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_CURRENT', 'DESC' ],
        ],
        'TITLE'   => 'All current companies with queued Smart Event jobs:',
    ],

    'QRY_SEO_GENERAL_STATUS_X' => [
        'QUERY'   => "           
            select decode(state, 
                        'E', 'Executed', 
                        'Q', 'Queued') status, 
                  count(*) as jobs 
            from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
            where whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) and type = 'S'
            group by state
            order by status desc
        ",
        'COLUMNS' => [ 'STATUS' => 11, 'JOBS' => 11 ],
        'TITLE'   => 'Current status for Smart Event jobs queued since {X} hours ago:',
    ],

    'QRY_SEO_TOPIC_QUEUED_CURRENT_X' => [
        'QUERY' =>
            "select topic, queued_current from
                ( select topic, count(*) as queued_current 
                from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')          
                     where state = 'Q' and type = 'S' and whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))                             
             group by topic
             order by queued_current desc)
             where rownum <= 10 ",

        'COLUMNS'   => [ 'TOPIC' => -45, 'QUEUED' => 9 ],
        'Aggregate' => [
            'gb'     => 'TOPIC',
            'totals' => [
                [ 'QUEUED_CURRENT', 'sum' ],
            ],
        ],
        'Top'       => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_CURRENT', 'DESC' ],
        ],
        'TITLE'     => 'Current topics queued on all Smart Event jobs since {X} hours ago:',
    ],

    'QRY_SEO_CNY_QUEUED_CURRENT_X' => [
        'QUERY'   =>
            "select cny#, title, queued_current from
                ( select s.cny#, c.title, count(*) as queued_current 
                from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s,
                     company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c                      
                            where s.state = 'Q'
                            and s.type = 'S'
                            and (c.record# = s.cny# and c.status = 'T')
                            and s.whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
                group by s.cny#, c.title
                order by queued_current desc )
             where rownum < 10",
        'COLUMNS' => [ 'CNY#' => 20, 'TITLE' => -45, 'IN QUEUE' => 1 ],
        'Top'     => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_CURRENT', 'DESC' ],
        ],
        'TITLE'   => 'Current companies queued on all Smart Event jobs:',
    ],

    'QRY_SEO_INQUEUE_BY_HOUR_QUEUED' => [
        'QUERY'     => "
           select hour, count(*) ct
            from 
            (
                SELECT TO_CHAR(whencreated, 'YYYY-MM-DD HH24:MI:SS') AS HOUR 
                  from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s,    
                  company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
                where s.state = 'Q'
                and s.type = 'S'
                and (c.record# = s.cny# and c.status = 'T')
            )
            group by hour
            order by hour desc            
        ",
        'COLUMNS'   => [ 'HOUR' => -25, 'IN QUEUE' => 9 ],
        'Aggregate' => [
            'gb'     => 'HOUR',
            'totals' => [
                [ 'CT', 'sum' ],
            ],
        ],
        'Top'       => [
            'order' => [ 'HOUR', 'DESC' ],
        ],
        'TITLE'     => 'Smart Event jobs in queue by hour queued:',
    ],

    'QRY_SEO_ACTIVITY_LAST_X' => [
        'QUERY'   => "
            with 
            QLH as (
                select count(*) as queued_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) and type = 'S'               
            ),            
            SLH as (
                select count(*) as started_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) and type = 'S'
            ),            
            FLH as (
                select count(*) as finished_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where timefinished > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) and type = 'S'
            )            
           select queued_LH, started_LH, finished_LH from QLH, SLH, FLH
        ",
        'COLUMNS' => [ 'QUEUED' => 8, 'STARTED' => 8, 'FINISHED' => 8 ],
        'TITLE'   => 'Activity in the last {X} hour(s):',
    ],

    'QRY_SEO_TOPIC_QUEUED_SINCE_X'                 => [
        'QUERY'     => "
             select topic, queued_since_x_hours_ago from
                ( select topic, count(*) as queued_since_x_hours_ago 
                  from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')          
                     where whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) and type = 'S'                                         
                  group by topic
                  order by queued_since_x_hours_ago desc
                )
        ",
        'COLUMNS'   => [ 'TOPIC' => -45, 'QUEUED' => 1 ],
        'Aggregate' => [
            'gb'     => 'TOPIC',
            'totals' => [
                [ 'QUEUED_SINCE_X_HOURS_AGO', 'sum' ],
            ],
        ],
        'Top'       => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_SINCE_X_HOURS_AGO', 'DESC' ],
        ],
        'TITLE'     => 'All Smart Event jobs topics queued since {X} hours ago:',
    ],

    // how many smart events (executed by smart event jobs) succeeded/failed per topic
    'QRY_SEO_TOPIC_EXECUTED_SMARTEVENTS_SINCE_X'   => [
        'QUERY'   => "
           select
                  topic,
                  NVL(succeeded, 0) succeeded,
                  NVL(failed, 0) failed
               from
                (
                select * from (
                      select sq.topic as topic, sh.executionstate
                        from                     
                        V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sq, 
                        smarteventjobdetails as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sh,
                        smartlink as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sl
                        where sq.cny# = sh.cny# and sq.cny# = sl.cny#
                        and sq.record# = sh.smarteventjob#
                        and sq.state = 'E'
                        and sq.type = 'S'
                        and sh.smarteventid = sl.smartlinkid and sq.topic like '%_'||UPPER(sl.ownerobject)
                        and sq.timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
                        ) pivot (sum(1) for executionstate in ('S' as succeeded, 'F' as failed))
                )        
        ",
        'COLUMNS' => [ 'TOPIC' => 20, 'SUCCEEDED' => 9, 'FAILED' => 9, ],
        'TITLE'   => 'Number of succeeded/failed smart events per topic since {X} hours ago:',
    ],

    // how many smart events (executed by smart event jobs) succeeded/failed per company
    'QRY_SEO_COMPANY_EXECUTED_SMARTEVENTS_SINCE_X' => [
        'QUERY'   => "
           select
                  cny#,
                  title,
                  NVL(succeeded, 0) succeeded,
                  NVL(failed, 0) failed
               from
                (
                    select * from (
                      select sq.cny#, c.title, sh.executionstate
                        from                     
                        V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sq, 
                        smarteventjobdetails as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sh,
                        smartlink as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sl,
                        company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
                        where sq.cny# = c.record# and sq.cny# = sh.cny# and sq.cny# = sl.cny#
                        and sq.record# = sh.smarteventjob#
                        and sq.state = 'E'
                        and sq.type = 'S'
                        and sh.smarteventid = sl.smartlinkid and sq.topic like '%_'||UPPER(sl.ownerobject)
                        and sq.timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
                        ) pivot (sum(1) for executionstate in ('S' as succeeded, 'F' as failed))
                )
        ",
        'COLUMNS' => [ 'CNY#' => 20, 'TITLE' => -45, 'SUCCEEDED' => 9, 'FAILED' => 9 ],
        'TITLE'   => 'Number of succeeded/failed smart events per company since {X} hours ago:',
    ],

    'QRY_SEO_CNY_QUEUED_SINCE_X' => [
        'QUERY'   => "
            select cny#, title, queued_since_x_hours_ago from
                ( select s.cny#, c.title, count(*) as queued_since_x_hours_ago 
                  from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s,
                     company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c                      
                            where (c.record# = s.cny# and c.status = 'T')
                            and s.type = 'S'
                            and s.whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
                
                  group by s.cny#, c.title
                  order by queued_since_x_hours_ago desc 
                )
             where rownum < 10 
        ",
        'COLUMNS' => [ 'CNY#' => 20, 'TITLE' => -45, 'QUEUED' => 1 ],
        'Top'     => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_SINCE_X_HOURS_AGO', 'DESC' ],
        ],
        'TITLE'   => 'All companies with Smart Event jobs queued since {X} hours ago:',
    ],

    'QRY_SEO_ACTIVITY_PREVIOUS_X' => [
        'QUERY'   => "
            with
            QLH as (
                select count(*) as queued_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2*2/24))
                and whencreated <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))    
                and type = 'S'            
            ),
            SLH as (
                select count(*) as started_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2*2/24)) 
                and timestarted <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) 
                and type = 'S'
            ),
            FLH as (
                select count(*) as finished_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where timefinished > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2*2/24))
                and timefinished <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24)) 
                and type = 'S'
            )
           select queued_LH, started_LH, finished_LH from QLH, SLH, FLH
        ",
        'COLUMNS' => [ 'QUEUED' => 8, 'STARTED' => 8, 'FINISHED' => 8 ],
        'TITLE'   => 'Smart Event jobs activity in the previous {X} hour(s): ',
    ],

    'QRY_SEO_TOPIC_QUEUED_PREVIOUS_X' => [
        'QUERY'     => "
               select topic, queued_since_x_hours_ago from
                ( select topic, count(*) as queued_since_x_hours_ago 
                  from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')          
                     where whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2*2/24))
                     and whencreated <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
                     and type = 'S'
                  group by topic
                  order by queued_since_x_hours_ago desc
                )
         ",
        'COLUMNS'   => [ 'TOPIC' => -45, 'QUEUED' => 1 ],
        'Aggregate' => [
            'gb'     => 'TOPIC',
            'totals' => [
                [ 'QUEUED_SINCE_X_HOURS_AGO', 'sum' ],
            ],
        ],
        'Top'       => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_SINCE_X_HOURS_AGO', 'DESC' ],
        ],
        'TITLE'     => 'Smart Event jobs queued topics in the previous {X} hour(s):',
    ],

    'QRY_SEO_CNY_QUEUED_PREVIOUS_X' => [
        'QUERY'   => "
             select cny#, title, queued_since_x_hours_ago from
                ( select s.cny#, c.title, count(*) as queued_since_x_hours_ago 
                  from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s,
                     company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c                      
                            where (c.record# = s.cny# and c.status = 'T')
                            and s.whencreated <= (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2/24))
                            and s.whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (:2*2/24))
                            and s.type = 'S'                
                  group by s.cny#, c.title
                  order by queued_since_x_hours_ago desc 
                )
             where rownum < 10 
        ",
        'COLUMNS' => [ 'CNY#' => 20, 'TITLE' => -45, 'QUEUED' => 1 ],
        'Top'     => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_SINCE_X_HOURS_AGO', 'DESC' ],
        ],
        'TITLE'   => 'Smart Event jobs queued companies in the previous {X} hour(s): ',
    ],

    'QRY_SEO_ACTIVITY_LAST_HOUR' => [
        'QUERY'   => "
            with
            QLH as (
                select count(*) as queued_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24)) and type = 'S'
            ),
            SLH as (
                select count(*) as started_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24)) and type = 'S'
            ),
            FLH as (
                select count(*) as finished_LH from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')
                where timefinished > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24)) and type = 'S'
            )
           select queued_LH, started_LH, finished_LH from QLH, SLH, FLH
        ",
        'COLUMNS' => [ 'QUEUED' => 8, 'STARTED' => 8, 'FINISHED' => 8 ],
        'TITLE'   => 'Smart Event jobs activity in the last hour:',
    ],

    'QRY_SEO_TOPIC_LAST_HOUR' => [
        'QUERY'     => "
             select topic, queued_since_x_hours_ago from
                ( select topic, count(*) as queued_since_x_hours_ago 
                  from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS')          
                     where whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24)) and type = 'S'                                         
                  group by topic
                  order by queued_since_x_hours_ago desc
                )
        ",
        'COLUMNS'   => [ 'TOPIC' => -45, 'QUEUED' => 1 ],
        'Aggregate' => [
            'gb'     => 'TOPIC',
            'totals' => [
                [ 'QUEUED_SINCE_X_HOURS_AGO', 'sum' ],
            ],
        ],
        'Top'       => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_SINCE_X_HOURS_AGO', 'DESC' ],
        ],
        'TITLE'     => 'Smart Event jobs topics in the last hour:',
    ],

    'QRY_SEO_CNY_LAST_HOUR' => [
        'QUERY'   => "
           select cny#, title, queued_since_x_hours_ago from
                ( select s.cny#, c.title, count(*) as queued_since_x_hours_ago 
                  from V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') s,
                     company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c                      
                            where (c.record# = s.cny# and c.status = 'T')
                            and s.type = 'S'
                            and s.whencreated > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
                
                  group by s.cny#, c.title
                  order by queued_since_x_hours_ago desc 
                )
             where rownum < 10 
        ",
        'COLUMNS' => [ 'CNY#' => 20, 'TITLE' => -45, 'QUEUED' => 1 ],
        'Top'     => [
            'cnt'   => 10,
            'order' => [ 'QUEUED_SINCE_X_HOURS_AGO', 'DESC' ],
        ],
        'TITLE'   => 'Companies with Smart Event jobs in the last hour:',
    ],

    // how many smart events (executed by smart event jobs) succeeded/failed per topic
    'QRY_SEO_TOPIC_EXECUTED_SMARTEVENTS_LAST_HOUR'   => [
        'QUERY'   => "
           select
                  topic,
                  NVL(succeeded, 0) succeeded,
                  NVL(failed, 0) failed
               from
                (
                    select * from (
                      select sq.topic as topic, sh.executionstate
                        from                     
                        V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sq, 
                        smarteventjobdetails as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sh,
                        smartlink as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sl
                        where sq.cny# = sh.cny# and sq.cny# = sl.cny#
                        and sq.record# = sh.smarteventjob#
                        and sq.state = 'E'
                        and sq.type = 'S'
                        and sh.smarteventid = sl.smartlinkid and sq.topic like '%_'||UPPER(sl.ownerobject)
                        and sq.timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
                        ) pivot (sum(1) for executionstate in ('S' as succeeded, 'F' as failed))
                )        
        ",
        'COLUMNS' => [ 'TOPIC' => 20, 'SUCCEEDED' => 9, 'FAILED' => 9, ],
        'TITLE'   => 'Number of succeeded/failed smart events per topic during the last hour',
    ],

    // how many smart events (executed by smart event jobs) succeeded/failed per company
    'QRY_SEO_COMPANY_EXECUTED_SMARTEVENTS_LAST_HOUR' => [
        'QUERY'   => "
           select
                  cny#,
                  title,
                  NVL(succeeded, 0) succeeded,
                  NVL(failed, 0) failed
               from
                (
                    select * from (
                      select sq.cny#, c.title, sh.executionstate
                        from                     
                        V_FIFODISPATCHERQUEUE as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sq, 
                        smarteventjobdetails as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sh,
                        smartlink as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') sl,
                        company as of timestamp to_date(:1, 'YYYY-MM-DD HH24:MI:SS') c
                        where sq.cny# = c.record# and sq.cny# = sh.cny# and sq.cny# = sl.cny#
                        and sq.record# = sh.smarteventjob#
                        and sq.type = 'S'
                        and sq.state = 'E'
                        and sh.smarteventid = sl.smartlinkid and sq.topic like '%_'||UPPER(sl.ownerobject)
                        and sq.timestarted > (to_date(:1, 'YYYY-MM-DD HH24:MI:SS') - (1/24))
                        ) pivot (sum(1) for executionstate in ('S' as succeeded, 'F' as failed))
                )
        ",
        'COLUMNS' => [ 'CNY#' => 20, 'TITLE' => -45, 'SUCCEEDED' => 9, 'FAILED' => 9 ],
        'TITLE'   => 'Number of succeeded/failed smart events per company during the last hour',
    ],
];

