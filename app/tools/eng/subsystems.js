var selSubsystemType = "";
var selSCLevel = "";

function getSubsystemsTable($subsystems) {


    var $table = $('<table>').addClass('table').addClass('table-condensed')
        .append($('<tr>')
            .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=create&.r=0&add=Add&_action=new&.it=subsystem&.done=subsystems_mgmnt_console.phtml","html":"Add","id":"id00","style":"color:blue","class":"qx-lister-editlink"})))
            .append($('<th>').text(''))
            .append($('<th>').text('Name'))
            .append($('<th>').text('Description'))
            .append($('<th>').text('Type'))
            .append($('<th>').addClass('center').text('Status'))
            .append($('<th>').text('Created'))
            .append($('<th>').text('Modified'))
            .append($('<th>').text(''))
    );

    $.each($subsystems, function(index, subsystem) {

        $table.append($('<tr>').attr('title', title)
            .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=edit&.r="+subsystem.RECORDNO+"&.it=subsystem&.done=subsystems_mgmnt_console.phtml","html":"Edit","id":"id"+subsystem.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
            .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=view&.r="+subsystem.RECORDNO+"&.it=subsystem&.done=subsystems_mgmnt_console.phtml","html":"View","id":"id"+subsystem.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
            .append($('<td>').text(subsystem.NAME))
            .append($('<td>').text(subsystem.DESCRIPTION))
            .append($('<td>').text(subsystem.TYPE))
            .append($('<td>').addClass(subsystem.STATUS == 'T'? "label-success" : "label-danger").text(subsystem.STATUS == 'T' ? "Active" : "Inactive"))
            .append($('<td>').text(subsystem.WHENCREATED))
            .append($('<td>').text(subsystem.WHENMODIFIED))
            .append($('<td>').append($("<a/>",{"href":"../cstools/submit_light.phtml?.do=delete&.r="+subsystem.RECORDNO+"&.it=subsystem&.done=subsystems_mgmnt_console.phtml","html":"Delete","id":"id"+subsystem.RECORDNO,"style":"color:blue","class":"qx-lister-editlink", "onclick":"return confirm('Are you sure you want to delete this item?');"})))
        );

    });

    return $table;
}

function getSubsystemInstancesTable($subsystem_instances) {


    var $table = $('<table>').addClass('table').addClass('table-condensed')
        .append($('<tr>')
            .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=create&.r=0&add=Add&_action=new&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Add","id":"id00","style":"color:blue","class":"qx-lister-editlink"})))
            .append($('<th>').text(''))
            .append($('<th>').text('Type'))
            .append($('<th>').text('Name'))
            .append($('<th>').text('Level'))
            .append($('<th>').text('Value'))
            .append($('<th>').addClass('center').text('Status'))
            .append($('<th>').text('Created'))
            .append($('<th>').text('Modified'))
            .append($('<th>').text(''))
        );

    $.each($subsystem_instances, function(index, subsystem_instance) {

        $table.append($('<tr>').attr('title', title)
            .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=edit&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Edit","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
            .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=view&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"View","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
            .append($('<td>').text(subsystem_instance.TYPE))
            .append($('<td>').text(subsystem_instance.NAME))
            .append($('<td>').text(subsystem_instance.SCLEVEL))
            .append($('<td>').text(subsystem_instance.VALUE))
            .append($('<td>').addClass(subsystem_instance.STATUS == 'T'? "label-success" : "label-danger").text(subsystem_instance.STATUS == 'T' ? "Active" : "Inactive"))
            .append($('<td>').text(subsystem_instance.WHENCREATED))
            .append($('<td>').text(subsystem_instance.WHENMODIFIED))
            .append($('<td>').append($("<a/>",{"href":"../cstools/submit_light.phtml?.do=delete&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Delete","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink", "onclick":"return confirm('Are you sure you want to delete this item?');"})))
        );

    });

    return $table;
}

function init() {
    $(document).ready(function() {
        $('#search').hide();
        $("form").submit(function(e){
            e.preventDefault(e);
        });

        $("#subsys_types.dropdown-menu li a").click(function(){
            selSubsystemType = $(this).text();
            $("#btnSelectType").html(selSubsystemType+' <span class="caret"></span>');
        });
        $("#sc_levels.dropdown-menu li a").click(function(){
            selSCLevel = $(this).text();
            if (!$(this).parent().hasClass("disabled"))
                $("#btnSelectLevel").html(selSCLevel+' <span class="caret"></span>');
            else
                return false;
        });

        $('.tree li:has(ul)').addClass('parent_li').find(' > span').attr('title', 'Collapse this branch');
        $('.tree li.parent_li > span').on('click', function (e) {
            var children = $(this).parent('li.parent_li').find(' > ul > li');
            if (children.is(":visible")) {
                children.hide('fast');
                $(this).attr('title', 'Expand this branch').find(' > i').addClass('icon-plus-sign').removeClass('icon-minus-sign');
            } else {
                children.show('fast');
                $(this).attr('title', 'Collapse this branch').find(' > i').addClass('icon-minus-sign').removeClass('icon-plus-sign');
            }
            e.stopPropagation();
        });
    });
}

function showSubsystemInstances() {

    var $results = $('#results');

    //unbindAll();
    $results.fadeOut(50, function () {
        $results.html('<div id="loading" class="loading"><i class="fa fa-gear fa-spin"></i></div>');
        $results.fadeIn(50, function() {
            var $subsystemInstancesTable = getSubsystemInstancesTable(subsystem_instances);
            $('#results .loading').fadeOut(50, function () {
                $results.children().remove();
                $results.append($subsystemInstancesTable);
                $results.fadeIn(50);
            })
        })
    });
}

function showSubsystems() {

    var $results = $('#results');

    $results.fadeOut(50, function () {
        $results.html('<div id="loading" class="loading"><i class="fa fa-gear fa-spin"></i></div>');
        $results.fadeIn(50, function() {
            var $subsystemsTable = getSubsystemsTable(subsystems);
            $('#results .loading').fadeOut(50, function () {
                $results.children().remove();
                $results.append($subsystemsTable);
                $results.fadeIn(50);
            })
        })
    });
     /*
    window.location.href = '../cstools/lister.phtml?.op=3230';*/
}

function showInspect() {

    $('#search').fadeToggle();
}

function showValidate() {

    showSubsystemInstances();
    //Find duplicates by level, mark as seen duplicate
    var txtValue;
    var seen = {};
    var duplicates = {};

    $('#results tbody tr').each(function() {
        txtValue = $(this).children("td:eq(5)").text();

        if (seen[txtValue]){
            duplicates[txtValue] = true;
        }
        else
            seen[txtValue] = true;
    });
    //highlight duplicates and info the rest
    jQuery.each(duplicates,function(index, item) {
        $('#results tbody tr').each(function() {
            txtValue = $(this).children("td:eq(5)").text();
            if ($(this).children("td:eq(5)").text() != index){
                $(this).addClass("info");
            } else
                $(this).addClass("warning");
        });
    });

}

function getObjects(list, key, val) {
    var objects = [];
    for (var instance in list) {
        if (!list.hasOwnProperty(instance)) continue;
        if (typeof list[instance] == 'object') {
            objects = objects.concat(getObjects(list[instance], key, val));
        } else if (instance == key && list[key] == val) {
            objects.push(list);
        }
    }
    return objects;
}

function getSubsystemInstancesFilteredTable(subsystem_instances, selSubsystemType, selSCLevel, searchValue) {

    var filtered_subsys_instances;
    var $table = $('<table>').addClass('table').addClass('table-condensed')
        .append($('<tr>')
                .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=create&.r=0&add=Add&_action=new&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Add","id":"id00","style":"color:blue","class":"qx-lister-editlink"})))
                .append($('<th>').text(''))
                .append($('<th>').text(''))
                .append($('<th>').text('Type'))
                .append($('<th>').text('Name'))
                .append($('<th>').text('Level'))
                .append($('<th>').text('Value'))
                .append($('<th>').addClass('center').text('Status'))
                .append($('<th>').text('Created'))
                .append($('<th>').text('Modified'))
                .append($('<th>').text(''))
        );

    if (selSCLevel == 'POD') {
        filtered_subsys_instances = getObjects(subsystem_instances, "SCLEVEL", selSCLevel);
        filtered_subsys_instances = getObjects(filtered_subsys_instances, "VALUE", searchValue);
        filtered_subsys_instances = getObjects(filtered_subsys_instances, "TYPE", selSubsystemType);

        $.each(filtered_subsys_instances, function(index, subsystem_instance) {

            $table.append($('<tr>').attr('title', title)
                .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=edit&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Edit","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
                .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=view&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"View","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
                .append($('<td>').text(subsystem_instance.TYPE))
                .append($('<td>').text(subsystem_instance.NAME))
                .append($('<td>').text(subsystem_instance.SCLEVEL))
                .append($('<td>').text(subsystem_instance.VALUE))
                .append($('<td>').addClass(subsystem_instance.STATUS == 'T'? "label-success" : "label-danger").text(subsystem_instance.STATUS == 'T' ? "Active" : "Inactive"))
                .append($('<td>').text(subsystem_instance.WHENCREATED))
                .append($('<td>').text(subsystem_instance.WHENMODIFIED))
                .append($('<td>').append($("<a/>",{"href":"../cstools/submit_light.phtml?.do=delete&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Delete","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink", "onclick":"return confirm('Are you sure you want to delete this item?');"})))
            );

        });
    } else if (selSCLevel == 'COMPANYNO') {
        filtered_subsys_instances = getObjects(subsystem_instances, "SCLEVEL", selSCLevel);
        filtered_subsys_instances = getObjects(filtered_subsys_instances, "VALUE", searchValue);
        filtered_subsys_instances = getObjects(filtered_subsys_instances, "TYPE", selSubsystemType);

        $.each(filtered_subsys_instances, function(index, subsystem_instance) {
            //mark as winner if companyno subsystem exists
            $table.append($('<tr class="bg-info">').attr('title', title)
                .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=edit&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Edit","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
                .append($('<td>').append($("<a/>",{"href":"../cstools/editor_light.phtml?.do=view&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"View","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink"})))
                .append($('<td>').text(subsystem_instance.TYPE))
                .append($('<td>').text(subsystem_instance.NAME))
                .append($('<td>').text(subsystem_instance.SCLEVEL))
                .append($('<td>').text(subsystem_instance.VALUE))
                .append($('<td>').addClass(subsystem_instance.STATUS == 'T'? "label-success" : "label-danger").text(subsystem_instance.STATUS == 'T' ? "Active" : "Inactive"))
                .append($('<td>').text(subsystem_instance.WHENCREATED))
                .append($('<td>').text(subsystem_instance.WHENMODIFIED))
                .append($('<td>').append($("<a/>",{"href":"../cstools/submit_light.phtml?.do=delete&.r="+subsystem_instance.RECORDNO+"&.it=subsystemconfiguration&.done=subsystems_mgmnt_console.phtml","html":"Delete","id":"id"+subsystem_instance.RECORDNO,"style":"color:blue","class":"qx-lister-editlink", "onclick":"return confirm('Are you sure you want to delete this item?');"})))
            );

        });

    }


    return $table;
}

function generatePageTree(dataset, parent, limit){
    if(limit > 4) return ''; // Make sure not to have an endless recursion, we have 4 SC levels
    var treeItems = '<ul>';
    $.each(dataset.data, function(key, element) {
        if(element['*parentId']==parent){
            treeItems += '<li><span><i class="icon-minus-sign"></i>';
            treeItems += element['*name'];
            treeItems += '</span>';
            treeItems += generatePageTree(dataset, element['*id'], limit++);
            treeItems += '</li>';
        }
    });
    treeItems += '</ul>';
    return treeItems;
}

function doSearch(searchValue) {

    var search_results = $('#search_results');
    var url = '';
    if (selSubsystemType == 'OBIEE') {
        switch(selSCLevel) {
            case "POD":
                url = 'subsystems_mgmnt_console.phtml?api=1&.operation=sbpod&.opval='+searchValue+'&.optype='+selSubsystemType;
                break;
            case "DBSERVER":
                url = 'subsystems_mgmnt_console.phtml?api=1&.operation=sbdbsrv&.opval='+searchValue+'&.optype='+selSubsystemType;
                break;
            case "DBSCHEMA":
                url = 'subsystems_mgmnt_console.phtml?api=1&.operation=sbdbsch&.opval='+searchValue+'&.optype='+selSubsystemType;
                break;
            case "COMPANYNO":
                url = 'subsystems_mgmnt_console.phtml?api=1&.operation=sbco&.opval='+searchValue+'&.optype='+selSubsystemType;
                break;
            default:
                url = 'subsystems_mgmnt_console.phtml?api=1&.operation=sbco&.opval='+searchValue+'&.optype='+selSubsystemType;
        }
    }
    $.ajax({
        //The URL to process the request
        'url' : url,
        //The type of request, also known as the "method" in HTML forms
        'type' : 'GET',
        'dataType' : 'json',
        //The response from the server
        'success' : function(data) {
            search_results.children().remove();
            if (data.message == 'ok') {
                var treeItems = '';
                search_results.append(generatePageTree(data, 0, 0));
            } else if (data.message == 'error') {
                alert(data.data);
            }
        }
    });

}

