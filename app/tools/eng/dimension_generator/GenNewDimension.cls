<?php

/**
 * Class to generate new dimension
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Intacct Corporation All, Rights Reserved
 */

/**
 * class to generate new dimension
 */
class GenNewDimension
{
    /** @var string $dimensionName */
    private $dimensionName;
    /** @var string $dimTableName */
    private $dimTableName;
    /** @var string $dimFieldID */
    private $dimFieldID;
    /** @var string $fkDimFieldKey */
    private $fkDimFieldKey;
    /** @var string $fkDimGroupKey */
    private $fkDimGroupKey;
    /** @var string $dimGroupTableName */
    private $dimGroupTableName;
    /** @var string $dimGrpMemTableName */
    private $dimGrpMemTableName;
    /** @var array|string[] $childDimTables */
    private array $childDimTables = [
        'BASEGLTOTALS', 'GLACCTGRP', 'REPORTINFO', 'HLTH_BASEGLTOTALS', 'UPDPRBCACHE', 'PROJECTTOTALS', 'GLENTRY', 'GLBUDGET',
        'REVRECSCHEDULEENTRY', 'PRENTRY', 'RECURPRENTRY', 'DOCENTRY', 'RECURDOCENTRY', 'TRANSTMPLENTRY',
        'RECURSUBTOTALS', 'DOCHDRSUBTOTALS', 'PRTAXENTRY', 'TIMESHEETENTRY', 'ALLOCATIONENTRY', 'RECURGLENTRY', 
        'DETOTALS', 'CONTRACT', 'CONTRACTDETAIL', 'CONTRACTEXPENSEDETAIL', 'GENINVOICELINE', 'GLACCTALLOCATIONBASIS',
        'GLACCTALLOCATIONREVERSE', 'GLACCTALLOCATIONSOURCE', 'GLACCTALLOCATIONTARGET', 'GLOB_TEMP_GLENTRY_TOTALS', 
        'GLOB_TEMP_PROJECTTOTALS', 'PJESTIMATEENTRY', 'PROJECTCONTRACTLINE', 'RATETABLEENTRY_AP', 'RATETABLEENTRY_CC',
        'RATETABLEENTRY_EE', 'RATETABLEENTRY_GL', 'RATETABLEENTRY_PO', 'RATETABLEENTRY_TS', 'CRDETAIL', 'CONSGLENTRY'
    ];
    
    /** @var array|string[] $childDimTablesNoFK */
    private array $childDimTablesNoFK = [
        'BASEGLTOTALS', 'HLTH_BASEGLTOTALS', 'UPDPRBCACHE', 'GLOB_TEMP_GLENTRY_TOTALS', 'GLOB_TEMP_PROJECTTOTALS', 
        'DETOTALS', 'PROJECTTOTALS', 'CONSGLENTRY'
    ];
    
    /** @var array|string[] $defaultZeroDimTables */
    private array $defaultZeroDimTables = [
        'BASEGLTOTALS', 'HLTH_BASEGLTOTALS', 'DETOTALS', 'CONSGLENTRY'
    ];
    
    /** @var array|string[] $dimViews */
    private array $dimViews = [
        'R__VIEW_BASEGLTOTALS_OWNER',
        'R__VIEW_DISPGLENTRY',
        'R__VIEW_DISPPRENTRY',
        'R__VIEW_DISPRECURGLENTRY',
        'R__VIEW_DISPRECURPRENTRY',
        'R__VIEW_GLACCOUNTBALANCE',
        'R__VIEW_GLBUDGET_OWNER',
        'R__VIEW_GLENTRY_OWNER',
        'R__VIEW_V_BASEGLTOT_DDS10P1',
        'R__VIEW_V_BASEGLTOT_DDS10P2',
        'R__VIEW_V_BASEGLTOT_DDS10P3',
        'R__VIEW_V_BILLABLEEXPENSE',
        'R__VIEW_V_DOCHDRSUBTOTALS',
        'R__VIEW_V_GENINVOICEPREBILLLINE',
        'R__VIEW_V_GLACCOUNTBAL_DDS10',
        'R__VIEW_V_GLACCOUNTBAL_DDS10L2',
        'R__VIEW_V_GLACCOUNTBAL_DDS10L3',
        'R__VIEW_V_GLBUDGETGLENTRY',
        'R__VIEW_V_GLDETAIL',
        'R__VIEW_V_GLDOCDETAIL',
        'R__VIEW_V_IETRANSACTIONS',
        'R__VIEW_V_KITCOSTING',
        'R__VIEW_V_RRSCHDLENTRY_DETAIL',
        'R__VIEW_V_RRSCHEDULEENTRY',
        'R__VIEW_V_CREREPORTING',
        'R__VIEW_PROJECTDETAIL',
    ];
    
    /** @var array|string[] $dimRequireFldViews */
    private array $dimRequireFldViews = [
        'R__VIEW_GLACCOUNT',
        'R__VIEW_STATACCOUNT',
    ];
    
    /** @var array|string[] $childDimGroupTables */
    private array $childDimGroupTables = [
        'REPORTINFO', 
        'GLACCTALLOCATIONBASIS',
        'GLACCTALLOCATIONSOURCE',
        'GLDIMGRPMEMBERS',
    ];
    
    /** @var array|string[] $dimPackages */
    private array $dimPackages = [
        'R__PKG_ACCT_UTILS',
        'R__PKG_PA_UTILS',
    ];
    
    /** @var array|string[] $dimPackageBodies */
    private array $dimPackageBodies = [
        'R__PKGBODY_ACCT_UTILS',
        'R__PKGBODY_HLTH_UTILS',
        'R__PKGBODY_INV_UTILS',
        'R__PKGBODY_PA_UTILS',
    ];
    
    /** @var array|string[] $dimProcedures */
    private array $dimProcedures = [
        'R__PROC_COMPUTEDEFERREDGLTOTALS',
    ];
    
    /** @var array|string[] $dimTriggers */
    private array $dimTriggers = [
        'R__TRIG_ARIU_DOCENTRY',
        'R__TRIG_ARIU_TIMESHEETENTRY',
        'R__TRIG_ARIUD_PRENTRY',
        'R__TRIG_BRDU_PRENTRY',
        'R__TRIG_BRUD_DOCENTRY',
        'R__TRIG_BRUD_TIMESHEETENTRY',
        'R__TRIG_GLENTRY_DO_GLTOTALS',
        'R__TRIG_BRIU_CONSGLENTRY',
    ];
    
    /** @var array|string[] $dimManualTriggers */
    private array $dimManualTriggers = [
        'R__TRIG_BRIU_BASEGLTOTALS',
        'R__TRIG_BRIU_GLBUDGET',
        'R__TRIG_BRIU_CONSGLENTRY',
    ];
    
    const NEWDIMTMPL = '--NEWDIMTOOL-';
    const UPPERNEWDIMFIELDTMPL = 'NEWSTDDIMKEY';
    const LOWERNEWDIMFIELDTMPL = 'newstddimkey';
    
    /**
     * @param string $dimensionName
     * @param string $dimTableName                     
     */
    public function __construct($dimensionName, $dimTableName)
    {
        $this->dimensionName = strtoupper($dimensionName);
        $this->dimTableName = strtoupper($dimTableName);
        $this->dimGroupTableName = $this->dimTableName . 'GROUP';
        $this->dimGrpMemTableName = $this->dimTableName . 'GRPMEMBERS';
        $this->dimFieldID = $this->dimensionName . "ID";
        $this->fkDimFieldKey = $this->dimensionName . "DIMKEY";
        $this->fkDimGroupKey = $this->dimensionName . "GROUPKEY";
    }

    /**
     * @return string
     */
    public function genScripts()
    {
        $script = $this->genDimensionTable();
        $script .= $this->genDimensionGrpTable();
        $script .= $this->genDimensionGrpMemTable();
        
        $script .= $this->genDimensionPickViews();
        
        $script .= $this->genDimChildTables();
        $script .= $this->genDimGroupChildTables();
        
        $script .= $this->genDimAll();
        
        $script .= $this->genPHPHelp();
        
        return $script;
    }

    /**
     * @return string
     */
    public function genReverseScripts()
    {
        $script = $this->revScript();
        
        return $script;
    }
    
    /**
     * @return string
     */
    private function revScript()
    {
        $script = "------ ***** Reversing Script ******* -----\n\n";
        $script .= "------ ***** Reversing ".$this->fkDimFieldKey ." ******* -----\n\n";        
        foreach ($this->childDimTables as $tableName) {
            if (!in_array($tableName, $this->childDimTablesNoFK)) {
                $script .= "ALTER TABLE $tableName DROP CONSTRAINT FK_" . $tableName . "_" . $this->fkDimFieldKey . "\n";
                $script .= "/\n\n";
            }
            $script .= "ALTER TABLE $tableName DROP (".$this->fkDimFieldKey . ")\n";
            $script .= "/\n\n";
        }
        
        $script .= "ALTER TABLE GLODCONFIG DROP (".$this->fkDimFieldKey.")\n";
        $script .= "/\n\n";
        
        $script .= "------ ***** Reversing REQUIRE".$this->dimensionName ." ******* -----\n\n";  
        
        $script .= "ALTER TABLE BASEACCOUNT DROP (REQUIRE".$this->dimensionName.")\n";
        $script .= "/\n\n";
        
        $script .= "------ ***** Reversing FILTER".$this->dimensionName ." ******* -----\n\n";  
        $script .= "ALTER TABLE GLACCTGRP DROP (FILTER".$this->dimensionName.")\n";
        $script .= "/\n\n";
        
        $script .= "---- ***** Reversing ".$this->dimensionName."DIMENSION  ******* -----\n\n";
        $script .= "ALTER TABLE ARSETUP DROP (".$this->dimensionName."DIMENSION)\n";
        $script .= "/\n\n";
        
        $script .= "------ ***** Reversing ".$this->fkDimGroupKey ." ******* -----\n\n";  
        //added for dropping the constraints for dimension group key
        foreach ( $this->childDimGroupTables as $tableName) {
            $script .= "ALTER TABLE $tableName DROP CONSTRAINT FK_" . $tableName . "_" . $this->fkDimGroupKey . "\n";
            $script .= "/\n\n";
            $script .= "ALTER TABLE $tableName DROP (".$this->fkDimGroupKey . ")\n";
            $script .= "/\n\n";
        }
        
        $tableName = 'V_'.$this->dimensionName.'PICK';
        $script .= "DROP VIEW $tableName\n";
        $script .= "/\n\n";
        
        $tableName = 'V_'.$this->dimensionName.'NGROUPPICK';
        $script .= "DROP VIEW $tableName\n";
        $script .= "/\n\n";
        
        $tableName = $this->dimGrpMemTableName;
        $script .= "DROP TABLE $tableName\n";
        $script .= "/\n\n";
        
        $tableName = $this->dimGroupTableName;
        $script .= "DROP TABLE $tableName\n";
        $script .= "/\n\n";
        
        $tableName = $this->dimTableName;
        $script .= "DROP TABLE $tableName\n";
        $script .= "/\n\n";
        
        return $script;
    }
    
    /**
     * @return string
     */
    private function genDimensionTable()
    {
        $tableName = $this->dimTableName;
        
        $script = "CREATE TABLE $tableName (\n  ";
        $script .= "CNY# NUMBER(15)," . "\n  ";
        $script .= "RECORD# NUMBER(15),\n  ";
        $script .= $this->dimFieldID . " VARCHAR2(100 CHAR),\n  ";
        $script .= "NAME VARCHAR2(200 CHAR),\n  ";
        $script .= "STATUS VARCHAR2(1 CHAR),\n  ";
        $script .= "LOCATIONKEY NUMBER(15) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),\n  ";
        $script .= "WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',\n  ";
        $script .= "WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',\n  ";
        $script .= "CREATEDBY NUMBER(15),\n  ";
        $script .= "MODIFIEDBY  NUMBER(15),\n  ";
        $script .= "SI_UUID VARCHAR2(36 CHAR),\n  ";
        $script .= "CONSTRAINT PK_$tableName PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,\n  ";
        $script .= "CONSTRAINT UQ_" . $tableName . "_" . $this->dimFieldID . " UNIQUE (CNY#, " . $this->dimFieldID
                   . ") USING INDEX TABLESPACE ACCTINDX  ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_LOCKEY FOREIGN KEY (CNY#, LOCATIONKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE\n";
        $script .= ") TABLESPACE ACCTDATA\n";
        $script .= "/\n\n";

        $script .= "CREATE INDEX IX_" . $tableName
                   . "_CREATEDBY ON $tableName (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        $script .= "CREATE INDEX IX_" . $tableName
                   . "_MODIFIEDBY ON $tableName (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        $script .= "CREATE INDEX IX_" . $tableName . "_LOCKEY ON $tableName (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX\n";
        $script .= "/\n\n";
        
        return $script;
    }
    
    /**
     * @return string
     */
    private function genDimensionGrpTable()
    {
        $tableName = $this->dimGroupTableName;
        $script = "CREATE TABLE $tableName (\n  ";
        $script .= "CNY# NUMBER(15)," . "\n  ";
        $script .= "RECORD# NUMBER(15),\n  ";
        $script .= "ID VARCHAR2(100 CHAR),\n  ";
        $script .= "NAME VARCHAR2(500 CHAR),\n  ";
        $script .= "DESCRIPTION VARCHAR2(500 CHAR),\n  ";
        $script .= "GROUPTYPE VARCHAR2(1 CHAR),\n  ";
        $script .= "MEMBERFILTERS VARCHAR2(4000 CHAR),\n  ";
        $script .= "LOCATIONKEY NUMBER(15) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),\n  ";
        $script .= "DEPTKEY NUMBER(15) DEFAULT sys_context('TMCtx', 'DEPTKEY'),\n  ";
        $script .= "WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',\n  ";
        $script .= "WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',\n  ";
        $script .= "CREATEDBY NUMBER(15),\n  ";
        $script .= "MODIFIEDBY  NUMBER(15),\n  ";
        $script .= "SI_UUID VARCHAR2(36 CHAR),\n  ";
        $script .= "CONSTRAINT PK_$tableName PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,\n  ";
        $script .= "CONSTRAINT UQ_" . $tableName . "_ID UNIQUE (CNY#, ID) USING INDEX TABLESPACE ACCTINDX  ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_LOCKEY FOREIGN KEY (CNY#, LOCATIONKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE\n";
        $script .= ") TABLESPACE ACCTDATA\n";
        $script .= "/\n\n";

        $script .= "CREATE INDEX IX_" . $tableName
                   . "_CREATEDBY ON $tableName (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        $script .= "CREATE INDEX IX_" . $tableName
                   . "_MODIFIEDBY ON $tableName (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        $script .= "CREATE INDEX IX_" . $tableName
                   . "_LOCKEY ON $tableName (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX\n";
        $script .= "/\n\n";

        return $script;
    }
    
    /**
     * @return string
     */
    private function genDimensionGrpMemTable()
    {
        $tableName = $this->dimGrpMemTableName;
        $script = "CREATE TABLE $tableName (\n  ";
        $script .= "CNY# NUMBER(15)," . "\n  ";
        $script .= "RECORD# NUMBER(15),\n  ";
        $script .= "GROUPKEY NUMBER(15) CONSTRAINT NN_".$tableName."_GROUPKEY NOT NULL ENABLE,\n  ";
	    $script .= $this->dimensionName."KEY NUMBER(15) CONSTRAINT NN_".$tableName."_".$this->dimensionName." NOT NULL ENABLE,\n  "; 
	    $script .= "SORTORD NUMBER(8) CONSTRAINT NN_".$tableName."_SORTORD NOT NULL ENABLE,\n  "; 
        $script .= "WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',\n  ";
        $script .= "WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',\n  ";
        $script .= "CREATEDBY NUMBER(15),\n  ";
        $script .= "MODIFIEDBY  NUMBER(15),\n  ";
        $script .= "SI_UUID VARCHAR2(36 CHAR),\n  ";
        $script .= "CONSTRAINT PK_$tableName PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,\n  ";
        $script .= "CONSTRAINT FK_".$tableName."_GROUP FOREIGN KEY (CNY#, GROUPKEY) REFERENCES ".$this->dimGroupTableName
                   ."(CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,\n  "; 
        $script .= "CONSTRAINT FK_".$tableName."_".$this->dimensionName." FOREIGN KEY (CNY#, ".$this->dimensionName
                   ."KEY) REFERENCES ".$this->dimTableName
                   ."(CNY#, RECORD#) DEFERRABLE ENABLE,\n  "; 
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,\n  ";
        $script .= "CONSTRAINT FK_" . $tableName
                   . "_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE\n";
        $script .= ") TABLESPACE ACCTDATA\n";
        $script .= "/\n\n";

        $script .= "CREATE INDEX IX_" . $tableName
                   . "_CREATEDBY ON $tableName (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        $script .= "CREATE INDEX IX_" . $tableName
                   . "_MODIFIEDBY ON $tableName (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        $script .= "CREATE INDEX IX_" . $tableName."_".$this->dimensionName
                   . " ON $tableName (CNY#, ".$this->dimensionName."KEY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        $script .= "CREATE INDEX IX_" . $tableName
                   . "_GROUP ON $tableName (CNY#, GROUPKEY) TABLESPACE ACCTINDX INVISIBLE\n";
        $script .= "/\n\n";
        
        return $script;
    }
    
    
    /**
     * @return string
     */
    private function genDimensionPickViews()
    {
        $tableName = 'V_'.$this->dimensionName.'PICK';
        
        $script = "CREATE OR REPLACE VIEW $tableName (";
        $script .= "CNY#, ".$this->dimensionName."PICK, STATUS ) AS \n  ";
        $script .= "SELECT A.CNY#, (A.".$this->dimFieldID." || '--' || A.NAME) ".$this->dimensionName."PICK, A.STATUS FROM ".$this->dimTableName." A  \n";
        $script .= "/\n\n";
        
        
        $tableName = 'V_'.$this->dimensionName.'NGROUPPICK';
        
        $script .= "CREATE OR REPLACE VIEW $tableName (";
        $script .= "CNY#, ".$this->dimensionName."PICK, STATUS, RECORDNO, TYPE ) AS \n  ";
        $script .= "SELECT A.CNY#, (A.".$this->dimFieldID." || '--' || A.NAME) ".$this->dimensionName."PICK, A.STATUS, A.RECORD#, '".$this->dimensionName."' type FROM ".$this->dimTableName." A  \n";
        $script .= "UNION ALL\n  ";
        $script .= "SELECT GRP.CNY#, (GRP.ID || '--' || GRP.NAME) ".$this->dimensionName."PICK, 'T' STATUS, GRP.RECORD#, 'GROUP' type FROM ".$this->dimGroupTableName." GRP  \n";
        $script .= "/\n\n";
        
        return $script;
    }
    /**
     * @return string
     */
    private function genDimChildTables()
    {
        $script = "\n---- ***** Adding ".$this->fkDimFieldKey." in the following tables\n\n";
        foreach ($this->childDimTables as $tableName) {
            if (!in_array($tableName, $this->defaultZeroDimTables)) {
                $script .= "ALTER TABLE $tableName ADD (\n  ";
                $script .= $this->fkDimFieldKey . " NUMBER(15)\n";
                $script .= ")\n";
                $script .= "/\n\n";
            } else {
                $script .= "ALTER TABLE $tableName ADD (\n  ";
                $script .= $this->fkDimFieldKey . " NUMBER(15) DEFAULT 0 CONSTRAINT NN_".$tableName."_".$this->fkDimFieldKey." NOT NULL ENABLE\n";
                $script .= ")\n";
                $script .= "/\n\n";
            }
            
            if (!in_array($tableName, $this->childDimTablesNoFK)) {
                $script .= "ALTER TABLE $tableName ADD CONSTRAINT FK_" . $tableName . "_" . $this->fkDimFieldKey
                           . " FOREIGN KEY (";
                $script .= "CNY#, " . $this->fkDimFieldKey . ")\n";
                $script .= " REFERENCES " . $this->dimTableName . " (CNY#, RECORD#) DEFERRABLE ENABLE\n";
                $script .= "/\n\n";

                $script .= "CREATE INDEX IX_" . $tableName . "_" . $this->fkDimFieldKey . " ON $tableName (CNY#, "
                           . $this->fkDimFieldKey . ") TABLESPACE ACCTINDX\n";
                $script .= "/\n\n";
            }

        }
        
        $script .= "ALTER TABLE GLODCONFIG ADD (".$this->fkDimFieldKey." CHAR(1 CHAR) DEFAULT 'F')\n";
        $script .= "/\n\n";
        
        $script .= "\n---- ***** Adding REQUIRE".$this->dimensionName." in the following tables\n\n";
        
        $script .= "ALTER TABLE BASEACCOUNT ADD (REQUIRE".$this->dimensionName.
                   " CHAR(1 CHAR) DEFAULT 'F' CONSTRAINT NN_BASEACCOUNT_REQ".$this->dimensionName." NOT NULL ENABLE)\n";
        $script .= "/\n\n";
        
        $script .= "\n---- ***** Adding FILTER".$this->dimensionName." in the following tables\n\n";
        
        $script .= "ALTER TABLE GLACCTGRP ADD (FILTER".$this->dimensionName." NUMBER(2) )\n";
        $script .= "/\n\n";

        $script .= "\n---- ***** Adding ".$this->dimensionName."DIMENSION in the following tables\n\n";

        $script .= "ALTER TABLE ARSETUP ADD (".$this->dimensionName."DIMENSION CHAR(1 CHAR) DEFAULT 'F')\n";
        $script .= "/\n\n";
        
        return $script;
    }
    
    /**
     * @return string
     */
    private function genDimGroupChildTables()
    {
        $script = "\n---- ***** Adding ".$this->fkDimGroupKey." in the following tables\n\n";
        foreach ( $this->childDimGroupTables as $tableName ) {
            $script .= "ALTER TABLE $tableName ADD (\n  ";
            $script .= $this->fkDimGroupKey . " NUMBER(15)\n";
            $script .= ")\n";
            $script .= "/\n\n";

            $script .= "ALTER TABLE $tableName ADD CONSTRAINT FK_" . $tableName . "_" . $this->fkDimGroupKey
                       . " FOREIGN KEY (";
            $script .= "CNY#, " . $this->fkDimGroupKey . ")\n";
            $script .= " REFERENCES " . $this->dimGroupTableName . " (CNY#, RECORD#) DEFERRABLE ENABLE\n";
            $script .= "/\n\n";

            $script .= "CREATE INDEX IX_" . $tableName . "_" . $this->fkDimGroupKey . " ON $tableName (CNY#, "
                       . $this->fkDimGroupKey . ") TABLESPACE ACCTINDX\n";
            $script .= "/\n\n";
        }
        return $script;
    }
    
    /**
     * @return string
     */
    private function genDimAll()
    {
        $basePath = strstr(getcwd(), 'app/', true);
        $script = "";
        
        $path = $basePath.'app/db/db_migration/objects/Views/';
        $this->checkPermission($path, $this->dimViews, $script);
        $this->checkPermission($path, $this->dimRequireFldViews, $script);
        
        $path = $basePath.'app/db/db_migration/objects/Packages/';
        $this->checkPermission($path, $this->dimPackages, $script);
        
        $path = $basePath.'app/db/db_migration/objects/PackageBodies/';
        $this->checkPermission($path, $this->dimPackageBodies, $script);
        
        $path = $basePath.'app/db/db_migration/objects/Procedures/';
        $this->checkPermission($path, $this->dimProcedures, $script);
        
        $path = $basePath.'app/db/db_migration/objects/Triggers/';
        $this->checkPermission($path, $this->dimTriggers, $script);
        
        if (!empty($script)) {
            $script = "****** PERMISSION ERROR: Execute following commands from 'app' folder\n\n$script";
            return $script;
        }
        
        $path = $basePath.'app/db/db_migration/objects/Views/';
        $script = "\n---- Updating ".$this->fkDimFieldKey." in the following views at ".$path."\n\n";
        $this->updateFiles($path, $this->dimViews, $this->fkDimFieldKey, $script);
        
        $script .= "\n---- Updating REQUIRE".$this->dimensionName." in the following views at ".$path."\n\n";
        $this->updateFiles($path, $this->dimRequireFldViews, "REQUIRE" . $this->dimensionName, $script);
        
        $path = $basePath.'app/db/db_migration/objects/Packages/';
        $script .= "\n---- Updating following packages at ".$path."\n\n";
        $this->updateFiles($path, $this->dimPackages, $this->fkDimFieldKey, $script);
        
        $path = $basePath.'app/db/db_migration/objects/PackageBodies/';
        $script .= "\n---- Updating following package bodies at ".$path."\n\n";
        $this->updateFiles($path, $this->dimPackageBodies, $this->fkDimFieldKey, $script);
        
        $path = $basePath.'app/db/db_migration/objects/Procedures/';
        $script .= "\n---- Updating following procedures at ".$path."\n\n";
        $this->updateFiles($path, $this->dimProcedures, $this->fkDimFieldKey, $script);
        
        $path = $basePath.'app/db/db_migration/objects/Triggers/';
        $script .= "\n---- Updating following triggers at ".$path."\n\n";
        $this->updateFiles($path, $this->dimTriggers, $this->fkDimFieldKey, $script);
        
        $script .= "\n---- ***** MANUALLY update following triggers\n\n";
        foreach ($this->dimManualTriggers as $name) {
            $script .= " -- $name\n\n";
        }
                
        return $script;
    }

    /**
     * @param string $path
     * @param array  $files
     * @param string $field
     * @param string $script
     *
     * @throws Exception
     */
    private function updateFiles($path, $files, $field, &$script)
    {
        foreach ( $files as $name ) {
            $script .= " -- $name\n\n";

            $fileName = $path . $name . '.sql';
            $fileText = file_get_contents($fileName);

            $this->appendNewRow($fileText, $field);

            if ( ! file_put_contents($fileName, $fileText) ) {
                $script .= "ERROR writing $fileName\n\n";
            }
        }
    }
    
        /**
     * @param string $path
     * @param array  $files
     * @param string $script
     *
     * @throws Exception
     */
    private function checkPermission($path, $files, &$script)
    {
        $errPath = strstr($path, 'db/');
        foreach ( $files as $name ) {
            $fileName = $path . $name . '.sql';
            if ( ! is_writable($fileName) ) {
                $script .= "chmod 777 $errPath$name.sql\n";
            }
        }
    }
    
    
    /**
     * @param $fileText
     */
    private function appendNewRow(&$fileText, $field)
    {
        $upperFKDimFieldKey = $field;
        $lowerFKDimFieldKey = strtolower($field);
        $matches = [];
        
        // skip if the new key exist already
        if (!strpos($fileText, $upperFKDimFieldKey) && !strpos($fileText, $lowerFKDimFieldKey)) {
            //get all template rows
            preg_match_all('/' . self::NEWDIMTMPL . '.*/', $fileText, $matches);
            $matches[0] = array_unique($matches[0]);
        }

        foreach ( $matches[0] as $match ) {
            //duplicate the template and replace with template new dimkey name
            $newDimStr = str_replace(self::UPPERNEWDIMFIELDTMPL, $upperFKDimFieldKey, $match);
            $newDimStr = str_replace(self::LOWERNEWDIMFIELDTMPL, $lowerFKDimFieldKey, $newDimStr);

            //remove the template in the new string
            $newDimStr = str_replace(self::NEWDIMTMPL, "", $newDimStr);

            //now insert the row with new dimkey name before the template row
            $fileText = str_replace($match, $newDimStr . "\n" . $match, $fileText);
        }
    }

    /**
     * @return string
     */
    private function genPHPHelp()
    {
        $script = "\n---- *** In PHP do the following *****\n\n";
        $script .= "-- Look other dimensions and create similar array for new dimension in IADimension.cls\n";
        $script .= "-- Search for FILTERASSET and add FILTER" . $this->dimensionName . " at all files\n";
        $script .= "-- Search for ASSET and do the same for new dimension at \n";
        $script .= "----- EM::dimensionFieldName2RecordNoFieldName\n";
        $script .= "----- dbschema.inc\n";
        $script .= "----- backend_nobjects.inc\n";
        $script .= "----- entitycombo.inc\n";
        $script .= "----- releasetopay.ent\n";
        $script .= "----- json.datapipeline.cfg & json.glod.cfg\n";
        $script .= "----- GLFinancialReport.cls\n";
        $script .= "----- std_reports.inc\n";
        $script .= "----- glacctgrp.ent\n";
        $script .= "----- glacctgrp_form.xml\n";
        $script .= "----- gldimgrpmember.ent\n";
        $script .= "----- GLAcctGrpManager.cls\n";
        $script .= "----- glsetup.ent\n";
        $script .= "----- GraphsManager.cls\n";

        return $script;
    }
    
}