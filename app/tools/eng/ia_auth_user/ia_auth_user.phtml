<?php
    //=============================================================================
    //
    //	FILE:        ia_auth_user.phtml
    //	AUTHOR:      <PERSON> <<EMAIL>>
    //	DESCRIPTION: IA Auth User tool - entry point
    //
    //	(C)2024, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    try {
        // need this, because inspections don't like extract()
        $r_page = Request::$r->page;

        // check requested page
        $pages = array("set_cookie");
        if (in_array($r_page, $pages, true)) {
            $page = $r_page;
        } else if ($r_page === null) {
            // default page
            $page = "set_cookie";
        } else {
            // invalid page; 404
            include "cs_not_found.inc";

            exit;
        }

        require_once "cs_common.inc";

        // unnecessary, but to keep inspections happy...
        global $g_login_id, $g_oMemCache;

        // messages passed from one page to the other
        $message = $g_oMemCache->get("cs_message_" . $g_login_id);
        if ($message !== false) {
            $t["message"] = array("type" => $message["type"], "text" => $message["text"]);

            $g_oMemCache->delete("cs_message_" . $g_login_id);
        } else if (isset($_COOKIE["IA_AUTH_USER"])) {
            $t["message"] = array("type" => ALERT_TYPE_WARNING, "text" => "Your current cookie value is '" . util_encode($_COOKIE['IA_AUTH_USER']) . "'.",);
        } else {
            $t["message"] = null;
        }

        // frame template data
        $t["tool_styles"] = array();
        $t["tool_scripts"] = array();

        // load page script
        ob_start();
        require "ia_auth_user_" . $page . ".inc";

    } catch (Exception $e) {
        ob_clean();

        $t_exception = $e;
        include_once "cs_exception.inc";
    }

    header("Content-Type: text/html; charset=utf-8");
    ob_end_flush();
