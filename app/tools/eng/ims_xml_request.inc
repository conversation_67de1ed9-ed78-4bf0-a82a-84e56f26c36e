<?  
//=============================================================================
//
//	FILE:			ims_xml_request.inc
//	AUTHOR:			
//	DESCRIPTION:	Include file for data needed by IMS_TOOLS_REQUEST
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================



// Example:
//
// $url = "http://hibiscus/~gomu/test.php3";
// $query = "data=hello";
// 
// $result = gatewaypostquery ($url, $query, &$response);
// 
// echo $response;
//

/**
 * @param array $postparams
 * @param string $content
 * @param string $response
 *
 * @return bool
 */
function imspostquery($postparams, $content, &$response) 
{
    include_once 'ims_package.cls';
    include_once 'ims_vehicle.cls';

    $ims_package = new ims_package();
    $ims_package->SetSender("XML_INTEGRATION_GATEWAY");
    $ims_package->SetAddress($postparams['PROTOCOL']."://".$postparams['ADDRESSURL']);
    $ims_package->SetOption('HTTP:USER', $postparams['USER']);
    $ims_package->SetOption('HTTP:PASSWORD', $postparams['PASSWORD']);
    $ims_package->SetOption('HTTP:URLENCODE', ($postparams['URLENCODE'] == 'T')? true : false);
    if ($postparams['CONTENT_TYPE']) {
        $ims_package->SetOption('HTTP:CONTENT_TYPE', $postparams['CONTENT_TYPE']);
    }
    if ($postparams['METHOD']) {
        $ims_package->SetOption('HTTP:METHOD', $postparams['METHOD']);
    }
    $ims_package->SetOption('CONTENT_PRIMARY_DELIMITER', $postparams['CONTENT_PRIMARY_DELIMITER']);
    $ims_package->SetOption('CONTENT_SECONDARY_DELIMITER', $postparams['CONTENT_SECONDARY_DELIMITER']);
    $ims_package->PushMessageVariable($content, $postparams['CONTENT_KEY']);

    $ims_vehicle = new ims_vehicle($ims_package);

    if (!$ims_vehicle->Transport($response)) {
        return false;
    }
    else {
        return true;
    }

}


