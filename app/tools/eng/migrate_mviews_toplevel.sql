
/* note: leave this blank line in migrate_mviews_toplevel.sql as it avoids an error when migrate_mviews_gold.sql does not end with a blank line.
------------------------------------------------------------------------------------------------------------------------
-- Following are the views written in migrate_mviews_gold.sql that needs to be overridden for top level
------------------------------------------------------------------------------------------------------------------------
*/


CREATE OR REPLACE FORCE VIEW MV_MEPRRECORD AS
  SELECT A.*
  FROM PRRECORD A
  WHERE   MEGA(LOC1)
/

CREATE OR REPLACE FORCE VIEW MV_MEPRRECORDLITE AS
  SELECT A.*
  FROM PRRECORD A
  WHERE  MEGA(LOC1)
/
      
CREATE OR REPLACE FORCE VIEW MV_MERECURPRRECORD AS
  SELECT A.*
  FROM RECURPRRECORD A
  WHERE  MEGA(LOC1)
/

CREATE OR REPLACE FORCE VIEW MV_MEDOCHDR AS
  SELECT A.*
  FROM DOCHDR A
  WHERE   MEGA(LOC5)
/

CREATE OR REPLACE FORCE VIEW MV_MEDOCHDRLITE AS
  SELECT *
  FROM DOCHDR A
  WHERE MEGA(LOC5)
/

CREATE OR REPLACE FORCE VIEW MV_MERECURDOCHDR AS
  SELECT A.*
  FROM RECURDOCHDR A
  WHERE   MEGA(LOC5)
/

CREATE OR REPLACE FORCE VIEW MV_MERECURDOCHDRLITE AS
  SELECT A.*
  FROM RECURDOCHDR A
  WHERE  MEGA(LOC5)
/

CREATE OR REPLACE FORCE VIEW MV_MEGLBATCH AS
  SELECT A.*
  FROM GLBATCH A
  WHERE   MEGA(LOC1)
/ 

CREATE OR REPLACE FORCE VIEW MV_MEREPORTSTORE AS
  SELECT    *
  FROM REPORTSTORE A
/

CREATE OR REPLACE FORCE VIEW MV_MEBASEACCOUNT AS
  SELECT A.*
  FROM BASEACCOUNT A
  WHERE MEGA(LOC1) 
/

CREATE OR REPLACE FORCE VIEW MV_MEGLACCOUNT AS
  SELECT A.*
  FROM GLACCOUNT A
  WHERE MEGA(LOC1)
/

CREATE OR REPLACE FORCE VIEW MV_MESTATACCOUNT AS
  SELECT A.*
  FROM STATACCOUNT A
  WHERE MEGA(LOC1)
/
