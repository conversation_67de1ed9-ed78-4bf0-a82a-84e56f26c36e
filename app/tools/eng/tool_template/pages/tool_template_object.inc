<?php
    //=============================================================================
    //
    //	FILE:        tool_template_object.inc
    //	AUTHOR:      <PERSON> <<EMAIL>>
    //	DESCRIPTION: cs tool template object page
    //
    //	(C)2024, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    //
    // READ ME
    //
    // example of an editor implementation
    //
    // NO output from this file; all the output is generated by "template/" directory or error files
    //
    // all pages (in "pages/" directory) should have 4 sections: init, ops, data, template
    // init: do checks on request params
    //       initialize minimum data needed to process the request; data for page rendering will be loaded later
    // ops: process requests like save, edit, delete
    //      at the end of the processing, redirect to the result page without executing the rest of the script
    // data: load data needed to render the page
    // template: initialize variables needed by the template to reader the page
    //           no data processing here; it should have been done by now in previous sections
    //

    //
    // init
    //

    // CS TEMPLATE INFO
    // set $r_* vars to map to request params
    // these vars must be immutable; they do not change value;
    //   you can always expect them to have what was requested; if you need to alter, define other vars

    $r_action = Request::$r->action;
    $r_record = Request::$r->record;
    $r_save = Request::$r->save;
    $r_name = Request::$r->name;
    $r_description = Request::$r->description;
    $r_status = Request::$r->status;

    // CS TEMPLATE INFO
    // $g_* global vars, defined at framework level
    // do not use globals at tool level; pass necessary values as parameters in functions

    // just to keep inspections happy...
    global $g_login_id, $g_oMemCache;
    // end inspections

    $alerts = array(
        ALERT_TYPE_ERROR => array(),
        ALERT_TYPE_WARNING => array(),
        ALERT_TYPE_CONFIRM => array(),
    );

    // check request params
    if (!in_array($r_action, array("add", "edit", "delete"), true)) {
        // invalid request; 404
        require "cs_not_found.inc";

        exit;
    }

    if ($r_action === "edit" or $r_action === "delete") {
        if ($r_record === null or $r_record === "") {
            // invalid request; 404
            require "cs_not_found.inc";

            exit;
        }

        $oObject = CSTemplateModel::Create($r_record);
        if ($oObject === null) {
            // invalid request; 404
            require "cs_not_found.inc";

            exit;
        }
    } else {
        // add a new object
        $oObject = new CSTemplateModel();
    }

    //
    // ops
    //

    // save record (insert or update)
    if (isset($r_save)) {
        // check required fields
        if (!isset($r_name) or trim($r_name) === "") {
            $alerts[ALERT_TYPE_ERROR]["name"] = "";
        }
        if (!isset($r_status) or trim($r_status) === "") {
            $alerts[ALERT_TYPE_ERROR]["status"] = "";
        }

        // set request values on the object
        $oObject->name = $r_name;
        $oObject->description = $r_description;
        $oObject->status = $r_status;

        if (count($alerts[ALERT_TYPE_ERROR]) === 0) {
            // save record
            $oObject->Save();

            // success message that will be displayed in the lister
            if ($r_action === "add") {
                $g_oMemCache->set("cs_message_" . $g_login_id, array("type" => ALERT_TYPE_SUCCESS, "text" => html_prepare("Record added successfully.")));
            } else if ($r_action === "edit") {
                $g_oMemCache->set("cs_message_" . $g_login_id, array("type" => ALERT_TYPE_SUCCESS, "text" => html_prepare("Record edited successfully.")));
            }

            // back to the lister
            header("Location: tool_template.phtml?page=list");
            exit;
        }
    }

    // delete record
    if ($r_action === "delete") {
        $oObject->Delete();

        // success message that will be displayed in the lister
        $g_oMemCache->set("cs_message_" . $g_login_id, array("type" => ALERT_TYPE_SUCCESS, "text" => html_prepare("Record deleted successfully.")));

        // back to the lister
        header("Location: tool_template.phtml?page=list");
        exit;
    }

    //
    // data
    //

    $header_buttons = array(
        array("type" => "submit", "name" => "save", "form" => "frm_tool_template", "label" => "Save"),
        array("type" => "link", "url" => "tool_template.phtml", "label" => "Cancel"),
    );

    //
    // template
    //

    // CS TEMPLATE INFO
    // vars needed for the frame part of the page, set by the framework
    // set how the main part of the page is generated: use "main_content" as HTML, or "main" as a "template/" file name

    // frame
    if ($r_action === "add") {
        $t["page_name"] = "Add new record";
    } else if ($r_action === "edit") {
        $t["page_name"] = "Edit record (" . html_prepare($oObject->name) . ")";
    }
    $t["header_buttons"] = $header_buttons;
    $t["main"] = "tool_template_object.tpl.inc";

    // CS TEMPLATE INFO
    // vars needed for your page, set in the "template/" file specified above ("main" key)

    // main
    $t["alerts"] = $alerts;
    $t["oObject"] = $oObject->HTMLPrepare();

    render("cs_frame.tpl.inc", $t);
