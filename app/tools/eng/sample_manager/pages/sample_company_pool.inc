<?php
    //=============================================================================
    //
    //	FILE:        sample_company_pool.inc
    //	AUTHOR:      <PERSON><PERSON><PERSON> <<EMAIL>>
    //	DESCRIPTION: sample company manager page
    //
    //	(C)2021, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    //
    // init data
    //

    $r_save = Request::$r->save;
    $r_action = Request::$r->action;
    $r_pool = Request::$r->pool;
    $r_error = Request::$r->error;

    $error_values = array(
        "0" => "Please choose one...",
        "1" => "Pool is now full",
        "2" => "Provisioning is now enabled",
        "3" => "Login is now enabled",
        "4" => "Reservation is now enabled",
    );

    $operations = array(
        "fill" => "Fill Sample Pool",
        "drain" => "Drain Sample Pool",
        "status" => "Get Pool Status",
        "notify" => "Send Notifications",
    );

    $pool = ($r_pool === "All") ? "" : $r_pool;

    // init lister
    $oLister = new WebCSLister("sample_manager");
    $oLister->hasOrdering = false;
    $oLister->template = "cs_lister_v2";

    if ($oLister->Init($_REQUEST) === WebCSLister::INVALID_REQUEST) {
        // invalid request; 404
        require "cs_not_found.inc";

        exit;
    }

    $need_lister = false;
    $alerts = array(ALERT_TYPE_ERROR => array(), ALERT_TYPE_WARNING => array(), ALERT_TYPE_CONFIRM => array());

    //
    // operations
    //

    // Fill Sample Pool
    if ($r_action === "fill") {
        $oLister->columns = array(
            "pool" => array("column" => "pool", "label" => "Pool name", "width" => "20rem"),
            "size" => array("column" => "size", "label" => "Size", "width" => "33%"),
            "available" => array("column" => "available", "label" => "Available", "width" => "33%"),
            "created" => array("column" => "created", "label" => "Created", "width" => "33%"),
        );

        $result = SampleCompanyManager::fillPools($pool);
        $rows = array();
        foreach ($result as $pool_name => $pool_info) {
            $row = array();
            $row["pool"] = $pool_name;
            $row["size"] = $pool_info["size"];
            $row["available"] = $pool_info["available"];
            $row["created"] = $pool_info["created"];
            $rows[] = $row;

            if (array_key_exists("error", $pool_info)) {
                $alerts[ALERT_TYPE_ERROR]["fill_pool"] = "Fill: " . $pool_info["error"];
            }
        }

        $oLister->data = $rows;
        $oLister->rowsPerPage = count($oLister->data);
        $need_lister = true;
    }

    // Drain Sample Pool
    if ($r_action === "drain") {
        $result = SampleCompanyManager::drainPools($pool);
        $drained = "";
        $errors = "";
        foreach ($result as $pool_name => $status) {
            if ($status === "drained") {
                $drained .= $pool_name . ", ";
            } else {
                $errors .= $pool_name . ", ";
            }
        }
        if ($drained !== "") {
            $drained = mb_substr($drained, 0, -2);
            $alerts[ALERT_TYPE_CONFIRM]["drained_pool"] = "Drained: " . $drained;
        }
        if ($errors !== "") {
            $errors = mb_substr($errors, 0, -2);
            $alerts[ALERT_TYPE_ERROR]["drained_pool"] = "Errors: " . $errors;
        }
    }

    // Get Pool Status
    $status_result = array();
    if ($r_action === "status") {
        $oLister->columns = array(
            "pool" => array("column" => "pool", "label" => "Pool name", "width" => "20rem"),
            "size" => array("column" => "size", "label" => "Size", "width" => "20%"),
            "provisioned" => array("column" => "provisioned", "label" => "Provisioned", "width" => "20%"),
            "to be deleted" => array("column" => "to be deleted", "label" => "To be deleted", "width" => "20%"),
            "other" => array("column" => "other", "label" => "Other", "width" => "20%"),
            "status" => array("column" => "status", "label" => "Status", "width" => "20%"),
        );

        $status = SampleCompanyManager::getReservationStats();
        $reserve_info = array();
        foreach ($status["status"] as $key => $value) {
            $reserve_info["Reservation " . $key] = $value;
        }
        if (is_array($status["errors"])) {
            foreach ($status["errors"] as $error => $count) {
                $reserve_info["Error " . $error] = $count;
            }
        }
        $status_result["reserve_info"] = $reserve_info;

        $status = SampleCompanyManager::getPoolStatus($pool);
        $rows = array();
        foreach ($status as $pool_name => $pool_info) {
            $row = array();
            $row["pool"] = $pool_name;
            $row["size"] = $pool_info["size"];
            $row["available"] = $pool_info["available"];
            $row["provisioned"] = $pool_info["provisioned"];
            $row["to be deleted"] = $pool_info["to be deleted"];
            $row["other"] = $pool_info["other"] ? $pool_info["otherother"] : "";
            $row["status"] = $pool_info["status"];
            $rows[] = $row;
        }

        $oLister->data = $rows;
        $oLister->rowsPerPage = count($oLister->data);
        $need_lister = true;
    }

    // Send Notifications
    if ($r_action === "notify") {
        $status = SampleCompanyManager::getPoolStatus($pool);
        $sent = SampleCompanyManager::notifyStatusErrorResolution($status, $r_error);
        if (!$sent) {
            $alerts[ALERT_TYPE_ERROR]["notifications"] = "No notifications sent.";
        } else {
            $messages = array();
            foreach ($sent as $pool_name => $sent_to) {
                $messages[] = $pool_name . ": " . $sent_to;
            }

            $alerts[ALERT_TYPE_CONFIRM]["notifications"] = $messages;
        }
    }

    //
    // data
    //

    $pool_names = SampleCompanyManager::getSamplePoolNames();

    //
    // template
    //

    // frame
    $t["page_name"] = "Sample company pool";

    // main
    $t["oLister"] = $oLister;
    $t["pools"] = html_prepare($pool_names);
    $t["main"] = "sample_company_pool.tpl.inc";
    $t["has_sec_header"] = false;
    $t["form_params"] = array(
        "action" => $r_action ?? "fill",
        "pool" => $r_pool ?? "All",
        "error" => $r_error ?? "0",
    );
    $t["error_values"] = $error_values;
    $t["operations"] = $operations;
    $t["need_lister"] = $need_lister;
    $t["alerts"] = html_prepare($alerts);
    $t["status_result"] = html_prepare($status_result);

    render("cs_frame.tpl.inc", $t);

