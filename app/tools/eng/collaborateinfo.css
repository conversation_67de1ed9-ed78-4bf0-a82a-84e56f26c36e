@charset "utf-8";
body {
    background-color: #fff;
    margin: 0px auto 20px;
    max-width: 1000px;
}
#page {
    display: none;
    margin: 0 20px;
}
#header {
    background-color: #fff;
    border: 1px solid #fff;
    border-radius: 4px;
    margin: 10px 0 15px;
    padding: 20px 0px;;
    width: 100%;

}
#brand {
    font-size: 32px;
    line-height: 50px;
    vertical-align: middle;
}
#title {
    margin-top: 10px;
    font-size:24px; font-weight:300;
}
#instructions {
    margin-top: 10px;
}
#form {
    margin-bottom: 0;
}
#response {
    font-size: 16px;
    height: 225px;
    line-height: 24px;
    width: 100%;
}
.loading {
    color: #000;
    font-size: 72px;
    margin: 150px auto 0;
    text-align: center;
    width: 100%;
}
#results, #error {
    color: #000;
    font-size: 28px;
    font-weight: 200;
    margin: 20px auto 0;
    text-align: center;
    width: 100%;
    display:none;
}
#results table {
    margin-top: 20px;
}
#results table th:not([scope=row]), #record table th:not([scope=row]){
    border-top: 0px;
}
#results td, #results th[scope=row], #record td, #record th[scope=row] {
    vertical-align: middle;
}
#results th:not([scope=row]), #record th:not([scope=row]) {
    vertical-align: bottom;
}
#results td {
    text-align: left;
}
#results i, #error i {
    font-size: 64px;
    margin-bottom: 20px;
}
td.right, #results td.right, #record td.right {
    text-align: right;
    padding-right: 12px;
}
th.center, #results th.center, #record th.center {
    text-align: center;
}
#overlay {
    background: #111 none repeat scroll 0 0;
    height: 100%;
    left: 0;
    margin: 0;
    opacity: 0.75;
    overflow: auto;
    padding: 0;
    position: fixed;
    top: 0;
    display: none;
    width: 100%;
    z-index: 100
}
#window {
    display: none;
    margin: auto;
    opacity: 1;
    padding: 20px;
    position: absolute;
    top: 100px;
    width: 1000px;
    z-index: 101;
}
#record {
    margin:auto;
    background-color:#fff;
    padding:20px;
    width:100%;
}

.center {
    margin: 0 auto;
    text-align: center;
}
.center i {
    margin-right: 15px;
}
table {
    font-size:12px;
}

.section { font-size:18px; font-weight:16px;}
.dashboard { margin-bottom:20px; margin-left: 70px;}

.card { border: 1px solid #bbb; border-radius: 4px; padding: 10px; cursor: pointer; background-color: #fff }
.card:hover { background-color:#E7FECD }
.card_data { font-size: 28px; text-align:center; padding:10px; font-weight:600; }
.card_label { font-size: 16px; text-align:center }
.red { color: #F00 }
.bold { font-weight:600 }



.form-control {
}
.form-control::-moz-placeholder, .form-control::-moz-placeholder {
    opacity: 0.6;
}
.form-control:-moz-placeholder, .form-control:-moz-placeholder {
    opacity: 0.6;
}
.formfieldred {
    background-color: #fff4f4;
    border: 1px solid #f00;
}
.formfieldred::-moz-placeholder, .formfieldred::-moz-placeholder {
    color: #f00;
}
.formfieldred:-moz-placeholder, .formfieldred:-moz-placeholder {
    color: #f00;
}

body.querypage {
    padding: 12px;
}

.querypage textarea {
    width:80%;
    height: 180px;
}

.querypage label {
    padding-right: 12px;
}

