<?php
require_once 'copy_cny2.inc';
require_once 'MoveTable.cls';

$moveTable = new MoveTable();
$moveTable->initializeParameters(Request::$r->tableName, Request::$r->tableCols, Request::$r->msg, Request::$r->options, Request::$r->opIdentifier,  Request::$r->ignoreList, Request::$r->allCompanySQL,
                                 Request::$r->srcSchema, Request::$r->dstSchema, Request::$r->srcDBLink, Request::$r->dstDBLink, Request::$r->clear, Request::$r->showSQL, Request::$r->timestamp);
global $conn;
$gErr = Globals::$g->gErr;
$conn = DatabaseConnection::getDirectConnection(Request::$r->username, Request::$r->adminpwd, Request::$r->server);
$response = ['success' => true, 'tableName' => Request::$r->tableName, 'errorMsg' => ''];
if($conn) {
    $disabled = TableOp::disableConstraintsAndTriggers(Request::$r->dstSchema, Request::$r->dstDBLink);
    if($disabled) {
        $moveTable->moveDisableFKsOn();
    }
    if ( HasErrors() ) {
        $gErr->GetErrList($errorMsgs);
        $response['success'] = false;
        foreach ( $errorMsgs as $err ) {
            $response['errorMsg'] .= $err['CDESCRIPTION'] . "\n";
        }
    }
} else {
    $response['success'] = false;
    $errorMsg = "Unable to connect to databse. Check adminid and adminpwd parameters.\n";
    $response['errorMsg'] = $errorMsg;
}
echo json_encode($response);