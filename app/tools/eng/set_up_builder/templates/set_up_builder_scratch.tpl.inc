<?php

// just to keep inspections happy
$t_page_name = $t_page_name ?? "";
$t_console_types = $t_console_types ?? array();
$t_console_company_type = $t_console_company_type ?? array();
$t_form_param = $t_form_param ?? array();
$t_pod_labels = $t_pod_labels ?? array();
$t_schema_labels = $t_schema_labels ?? array();
$t_client_company_type = $t_client_company_type ?? array();
$t_alerts = $t_alerts ?? array();
$t_module_labels = $t_module_labels ?? array();
// end inspections
?>
<div class="alerts">
    <div id="error_group" class="alert alert-error" style="display:<?=count($t_alerts[ALERT_TYPE_ERROR]) > 0 ? "block" : "none"; ?>;">
        <span id="email_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["email"]) ? "block" : "none";?>;">
        A value for <a href="#email">Email</a> is required.
        </span>
        <span id="create_console_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["create_console"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_ERROR]["create_console"] ?? "";?>
        </span>
        <span id="create_clients_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["create_clients"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_ERROR]["create_clients"] ?? "";?>
        </span>
        <span id="link_clients_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["link_clients"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_ERROR]["link_clients"] ?? "";?>
        </span>
        <span id="prefix_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["prefix"]) ? "block" : "none";?>;">
        A value for <a href="#prefix">Companies Prefix</a> is required.
        </span>
        <span id="console_title_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["console_title"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_ERROR]["console_title"] ?? "";?>
        </span>
        <span id="client_title_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["client_title"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_ERROR]["client_title"] ?? "";?>
        </span>
    </div>
    <div id="success_group" class="alert alert-confirm" style="display:<?=count($t_alerts[ALERT_TYPE_CONFIRM]) > 0 ? "block" : "none";?>;">
        <span id="create_console_success" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_CONFIRM]["create_console"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_CONFIRM]["create_console"] ?? "";?>
        </span>
        <span id="create_clients_success" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_CONFIRM]["create_clients"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_CONFIRM]["create_clients"] ?? "";?>
        </span>
        <span id="link_clients_success" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_CONFIRM]["link_clients"]) ? "block" : "none";?>;">
        <?=$t_alerts[ALERT_TYPE_CONFIRM]["link_clients"] ?? "";?>
        </span>
    </div>
</div>
<div class="page-row">
    <div class="page-col2">
        <form id="form" action="?page=scratch" method="post" class="box" onsubmit="return formValidation();">

            <div class="form-row">
                <label class="form-row-text">
                    <span>Console Type</span>
                    <span class="required">*</span>
                </label>
                <div class="select-group">
                    <button type="button" class="select-btn" onclick="showDropdown(event,'console_type');">
                        <span id="console_type_label" class="select-label"><?=$t_console_types[$t_form_param["console"]["type"]];?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="console_type" class="select-dropdown">
                        <? foreach ($t_console_types as $item => $value) { ?>
                        <input id="console_type_<?=strtolower($value);?>" name="console_type" type="radio" value="<?=$item;?>" <?=($t_form_param["console"]["type"] === $item) ? " checked" : "";?> class="select-option" onclick="setSelector('console_type_label','<?=$value;?>');"/>
                        <label for="console_type_<?=strtolower($value);?>" class="select-item"><?=$value;?></label>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label class="form-row-text">
                    <span>Console Company Type</span>
                    <span class="required">*</span>
                </label>
                <div class="select-group">
                    <button type="button" class="select-btn" onclick="showDropdown(event,'console_company_type');">
                        <span id="console_company_type_label" class="select-label"><?=$t_form_param["console"]["company_type"];?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="console_company_type" class="select-dropdown">
                        <?foreach ($t_console_company_type as $item => $value) { ?>
                        <input id="console_company_type_<?=$item;?>" name="console_company_type" type="radio" value="<?=$item;?>" <?=($t_form_param["console"]["company_type"] === $value) ? " checked" : "";?> class="select-option" onclick="setSelector('console_company_type_label','<?=$value;?>');"/>
                        <label for="console_company_type_<?=$item;?>" class="select-item"><?=$value;?></label>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label class="form-row-text">
                    <span>POD</span>
                    <span class="required">*</span>
                </label>
                <div class="select-group">
                    <button type="button" class="select-btn" onclick="showDropdown(event,'console_pod_selector');">
                        <span id="console_pod_selector_label" class="select-label"><?=$t_pod_labels[$t_form_param["console"]["pod"]];?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="console_pod_selector" class="select-dropdown">
                        <? foreach ($t_pod_labels as $item => $value) { ?>
                        <input id="console_pod_<?=$item;?>" name="console_pod" type="radio" value="<?=$item;?>" <?=($t_form_param["console"]["pod"] == $item) ? " checked" : "";?> class="select-option" onclick="setSelector('console_pod_selector_label','<?=$value;?>'); changeOption('<?=$item?>', 'console_schema', 'selector_option_generic')"/>
                        <label for="console_pod_<?=$item;?>" class="select-item"><?=$value;?></label>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label class="form-row-text">
                    <span>Schema</span>
                    <span class="required">*</span>
                </label>
                <div class="select-group">
                    <button type="button" class="select-btn" onclick="showDropdown(event,'console_schema_selector');">
                        <span id="console_schema_selector_label" class="select-label"><?=$t_schema_labels[$t_form_param["console"]["pod"]][$t_form_param["console"]["schema"]];?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="console_schema_selector" class="select-dropdown">
                        <? foreach ($t_schema_labels[$t_form_param["console"]["pod"]] as $schema_key => $value) { ?>
                        <input id="console_schema_<?=$schema_key;?>" name="console_schema" type="radio" value="<?=$schema_key;?>" <?=($t_form_param["console"]["schema"] == $schema_key) ? " checked" : "";?> class="select-option" onclick="setSelector('console_schema_selector_label','<?=$value;?>');"/>
                        <label for="console_schema_<?=$schema_key;?>" class="select-item"><?=$value;?></label>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label for="email" class="form-row-text">
                    <span>Email</span>
                    <span class="required">*</span>
                </label>
                <input id="email" name="email" type="text" class="form-input required-value" value="<?=$t_form_param["console"]["email"];?>"/>
            </div>

            <div class="form-row">
                <label for="prefix" class="form-row-text">
                    <span>Companies Prefix</span>
                    <span class="required">*</span>
                </label>
                <input id="prefix" name="prefix" type="text" class="form-input required-value" value="<?=$t_form_param["prefix"];?>"/>
            </div>

            <input id="clients_id" type="hidden" name="clients_id" value="<?=$t_form_param["clients_id"];?>">
            <div id="client_template_INDEX" class="expend-card" index="INDEX" style="display: none">
                <div class="expend-card-head" onclick="expandCard(event, 'client_INDEX', 'arrow_INDEX');">
                    <span id="arrow_INDEX" class="fal fa-angle-down"></span>
                    <label>Client #INDEX</label>
                    <button class="btn-icon minus-btn" type="button" onclick="removeClient('INDEX', event)">
                        <span class="far i-remove"></span>
                    </button>
                </div>

                <div id="client_INDEX" class="expend-card-body">
                    <div class="form-row">
                        <label class="form-row-text">
                            <span>Company type</span>
                            <span class="required">*</span>
                        </label>
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'client_INDEX_type');">
                                <span id="client_INDEX_type_label" class="select-label"><?=reset($t_client_company_type);?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="client_INDEX_type" class="select-dropdown">
                                <?foreach ($t_client_company_type as $item => $value) { ?>
                                <input id="client_INDEX_type_<?=$item;?>" name="client_INDEX_type" type="radio" value="<?=$item;?>" <?=(reset($t_client_company_type) === $value) ? " checked" : "";?> class="select-option" onclick="setSelector('client_INDEX_type_label','<?=$value;?>');"/>
                                <label for="client_INDEX_type_<?=$item;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-row-text">
                            <span>POD</span>
                            <span class="required">*</span>
                        </label>
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'client_INDEX_pods_selector');">
                                <span id="client_INDEX_pod_label" class="select-label"><?=reset($t_pod_labels);?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="client_INDEX_pods_selector" class="select-dropdown">
                                <? foreach ($t_pod_labels as $item => $value) { ?>
                                <input id="client_INDEX_pod_<?=$item;?>" name="client_INDEX_pod" type="radio" value="<?=$item;?>" <?=(reset($t_pod_labels) === $value) ? " checked" : "";?> class="select-option" onclick="setSelector('client_INDEX_pod_label','<?=$value;?>'); changeOption('<?=$item?>', 'client_INDEX', 'selector_option_generic')"/>
                                <label for="client_INDEX_pod_<?=$item;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-row-text">
                            <span>Schema</span>
                            <span class="required">*</span>
                        </label>
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'client_INDEX_selector');">
                                <span id="client_INDEX_selector_label" class="select-label"><?=reset($t_schema_labels[array_key_first($t_pod_labels)]);?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="client_INDEX_selector" class="select-dropdown">
                                <? foreach ($t_schema_labels[array_key_first($t_pod_labels)] as $schema_key => $value) { ?>
                                <input id="client_INDEX_<?=$schema_key;?>" name="client_INDEX_schema" type="radio" value="<?=$schema_key;?>" <?=(reset($t_schema_labels[array_key_first($t_pod_labels)]) === $value) ? " checked" : "";?> class="select-option" onclick="setSelector('client_INDEX_selector_label','<?=$value;?>');"/>
                                <label for="client_INDEX_<?=$schema_key;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-row-text">
                            <span>Modules</span>
                        </label>
                        <?$count = 0;?>
                        <? foreach ($t_module_labels as $module_key => $module) { ?>
                        <label class="checkbox">
                            <input name="client_INDEX_modules[app<?=$count?>]" value="<?=$module_key;?>" type="checkbox"/>
                            <span class="fal"></span>
                            <div class="form-row-text"><?=$module["NAME"];?></div>
                        </label>
                        <?$count++;?>
                        <? } ?>
                    </div>

                </div>
            </div>

            <? foreach ($t_form_param["clients"] as $key => $client) { ?>
            <div id="client_block_<?=$key;?>" class="expend-card" index="<?=$key;?>">
                <div class="expend-card-head" onclick="expandCard(event, 'client_<?=$key;?>', 'arrow_<?=$key;?>');">
                    <span id="arrow_<?=$key;?>" class="fal fa-angle-down"></span>
                    <label>Client <?=$key;?></label>
                    <button class="btn-icon minus-btn" type="button" onclick="removeClient('<?=$key;?>', event)">
                        <span class="far i-remove"></span>
                    </button>
                </div>

                <div id="client_<?=$key;?>" class="expend-card-body">
                    <div class="form-row">
                        <label class="form-row-text">
                            <span>Company type</span>
                            <span class="required">*</span>
                        </label>
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'client_<?=$key;?>_type');">
                                <span id="client_<?=$key;?>_type_label" class="select-label"><?=$t_client_company_type[$client["type"]];?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="client_<?=$key;?>_type" class="select-dropdown">
                                <?foreach ($t_client_company_type as $item => $value) { ?>
                                <input id="client_<?=$key;?>_type_<?=$item;?>" name="client_<?=$key;?>_type" type="radio" value="<?=$item;?>" <?=($client["type"] === $item) ? " checked" : "";?> class="select-option" onclick="setSelector('client_<?=$key;?>_type_label','<?=$value;?>');"/>
                                <label for="client_<?=$key;?>_type_<?=$item;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-row-text">
                            <span>POD</span>
                            <span class="required">*</span>
                        </label>
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'client_<?=$key;?>_pods_selector');">
                                <span id="client_<?=$key;?>_pod_label" class="select-label"><?=$t_pod_labels[$client["pod"]];?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="client_<?=$key;?>_pods_selector" class="select-dropdown">
                                <? foreach ($t_pod_labels as $item => $value) { ?>
                                <input id="client_<?=$key;?>_pod_<?=$item;?>" name="client_<?=$key;?>_pod" type="radio" value="<?=$item;?>" <?=($client["pod"] == $item) ? " checked" : "";?> class="select-option" onclick="setSelector('client_<?=$key;?>_pod_label','<?=$value;?>'); changeOption('<?=$item?>', 'client_<?=$key;?>', 'selector_option_generic')"/>
                                <label for="client_<?=$key;?>_pod_<?=$item;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-row-text">
                            <span>Schema</span>
                            <span class="required">*</span>
                        </label>
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'client_<?=$key;?>_selector');">
                                <span id="client_<?=$key;?>_selector_label" class="select-label"><?=$t_schema_labels[$client["pod"]][$client["schema"]];?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="client_<?=$key;?>_selector" class="select-dropdown">
                                <? foreach ($t_schema_labels[$client["pod"]] as $schema_key => $value) { ?>
                                <input id="client_<?=$key;?>_<?=$schema_key;?>" name="client_<?=$key;?>_schema" type="radio" value="<?=$schema_key;?>" <?=($client["schema"] == $schema_key) ? " checked" : "";?> class="select-option" onclick="setSelector('client_<?=$key;?>_schema_label','<?=$value;?>');"/>
                                <label for="client_<?=$key;?>_<?=$schema_key;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-row-text">
                            <span>Modules</span>
                        </label>
                        <?$count = 0;?>
                        <? foreach ($t_module_labels as $module_key => $module) { ?>
                        <label class="checkbox">
                            <input name="client_<?=$key;?>_modules[app<?=$count?>]" value="<?=$module_key;?>" <?=in_array($module_key, $t_form_param["clients"][$key]["modules"], true) ? " checked" : "";?> type="checkbox"/>
                            <span class="fal"></span>
                            <div class="form-row-text"><?=$module["NAME"];?></div>
                        </label>
                        <?$count++;?>
                        <? } ?>
                    </div>

                </div>
            </div>
            <? } ?>

            <div class="add-btn" onclick="addClient()">
                Add Client
                <button class="btn-icon" type="button">
                    <span id="" class="far i-add"></span>
                </button>
            </div>

            <div class="form-row">
                <input style="min-width: 5rem;" name="build" type="submit" value="Build"/>
                <a href="?page=scratch" class="button-link" onclick="showLoader();">Cancel</a>
            </div>
        </form>
    </div>
</div>

<template id="selector_option_generic">
    <input id="BLOCK_KEY" name="BLOCK" type="radio" value="KEY" class="select-option" onclick="setSelector('BLOCK_selector_label', 'VALUE');"/>
    <label for="BLOCK_KEY" title="VALUE" class="select-item" tabindex="-1">VALUE</label>
</template>

<? if (!empty($t_schema_labels)) { ?>
    <script>
        var option_labels = <?= json_encode($t_schema_labels, true); ?>;
    </script>
<? } ?>
<!-- keep inspections happy -->
