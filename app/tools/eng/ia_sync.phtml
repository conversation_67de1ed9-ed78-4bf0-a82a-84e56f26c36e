<?php

import('MultiPickControl');
require_once 'IAObjectsSync.cls';

if (isset($_GET['file'])) {
   /* echo file_get_contents("/tmp/quickstart_ia_backup/2022R3/iadefinition_ALL.json"); dieFL();
    $file = $_GET['file'];
    $filename = $_GET['filename'];

    if (file_exists($file) && is_readable($file) && preg_match('/\.json$/',$file)) {
        header('Content-Type: application/json');
        header("Content-Disposition: attachment; filename=\"$filename\"");
        echo $file;
    }*/

    // Create filename
    $file = $_GET['file'];
    $filename = $_GET['filename'];

    // Force download .json file with JSON in it
    header("Content-type: application/vnd.ms-excel");
    header("Content-Type: application/force-download");
    header("Content-Type: application/download");
    header("Content-disposition: " . $filename);
    header("Content-disposition: filename=" . $filename);

    print file_get_contents($file);
    exit;
}

$path = &Request::$r->path;
$_dstschema = Request::$r->_dstschema;
$_dump = Request::$r->_dump;
$_iaobjects = Request::$r->_iaobjects;
$_admindbid = Request::$r->_admindbid;
$_admindbpwd = Request::$r->_admindbpwd;
$_industrycode = Request::$r->_industrycode;
$path = ini_get("include_path");
ini_set("include_path", $path . ":../../www/acct:./");

if ( $_sync || $_dump) {
    $gErr = Globals::$g->gErr;
    $schemas = explode('#~#', $_dstschema);
    $iaobjects = explode('#~#', $_iaobjects);
    $ok = true;
    if ($_sync) {
        if (count($schemas) == 0 || $schemas[0] == '') {
            $gErr->addError('BL03000113', __FILE__ . '::' . __LINE__, "Please Select atleast one schema");
            $ok = false;
        }
        if (count($iaobjects) == 0 || $iaobjects[0] == '') {
            $gErr->addError('BL03000113', __FILE__ . '::' . __LINE__, "Please Select atleast one object");
            $ok = false;
        }
    }

    if ($ok) {
        $syncObj = new IAObjectsSync();
        $syncObj->SynchronizeIADef($iaobjects, $schemas, $_admindbid, $_admindbpwd, $_dump, $_industrycode);
    }
    if (HasErrors() && $gErr->ErrorCount) {
        include 'popuperror.phtml';
        exit;
    }
    return true;
}

$title = 'Synchronize';
global $kIAObjects, $kDBSchemas;
$syncObj = new IAObjectsSync();
$kDBSchemas = $syncObj->GetSchemas();

$iaObjs = array('ALLOBJECTS' => 'All System Objects');
$iaObjs = INTACCTarray_merge($iaObjs, $kIAObjects);

$schemas = array('ALLSCHEMAS' => 'All Schemas');
$schemas = INTACCTarray_merge($schemas, $kDBSchemas);
Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000');
?>

<form action=ia_sync.phtml method=post>
    <table border=0 cellpadding=2 cellspacing=0 width="90%">
        <tr>
            <td align=right valign=middle width="30%">
                <font face="verdana, arial" size="2">
                    Objects to be synchronized :
                </font>
            </td>
            <td valign=top>
                <? $mult = new MultiPickControl([]);
                $mult->_params['varname'] = ".iaobjects";
                $mult->_params['rowcount'] = 15;
                $mult->_params['type']['validlabels'] = array_values($iaObjs);
                $mult->_params['type']['validvalues'] = array_keys($iaObjs);
                $mult->_params['needhidden'] = true;
                $mult->Show();
                ?>
            </td>
        </tr>
        <!--<tr>
            <td align=right valign=middle width="30%">
                <font face="verdana, arial" size="2">
                    Schemas to be synchronized :
                </font>
            </td>
            <td valign=top>
                <?/* $mult = new MultiPickControl([]);
                $mult->_params['varname'] = ".dstschema";
                $mult->_params['rowcount'] = 15;
                $mult->_params['type']['validlabels'] = array_values($schemas);
                $mult->_params['type']['validvalues'] = array_keys($schemas);
                $mult->_params['needhidden'] = true;
                $mult->Show();
                */?>
            </td>
        </tr>-->
        <!--<tr>
            <td align=right valign=middle width="30%">
                <font face="verdana, arial" size="2">
                    (Optional - Dump only) Industry :
                </font>
            </td>
            <td valign=top>
                <input type="text" name=".industrycode">
            </td>
        </tr>-->
        <tr>
            <td align=right valign=middle width="30%">
                <font face="verdana, arial" size="2">
                    DB Admin ID :
                </font>
            </td>
            <td valign=top>
                <input type="text" name=".admindbid">
            </td>
        </tr>
        <tr>
            <td align=right valign=middle width="30%">
                <font face="verdana, arial" size="2">
                    DB Admin Password :
                </font>
            </td>
            <td valign=top>
                <input type="password" name=".admindbpwd">
            </td>
        </tr>
    </table>
    <p>
    <table width="90%" border="0" cellpadding=4 cellspacing=0 bgcolor="<? echo Globals::$g->color4 ?>">
        <tr>
            <td colspan=4 nowrap>
                <img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="25" height="1" border="0">
                <small><font face="verdana, arial">
                        Click Sync to Synchronize this to selected schemas
                    </font></small>
            </td>
            <td align=right>
<!--                <input class="nosavehistory" type="Submit" name=".sync" value="Sync">-->
                <input class="nosavehistory" type="Submit" name=".dump" value="Dump">
            </td>
        </tr>
    </table>

</form>
