<?php
///////////////////////////////////////////////////////////////////
// USE THIS SCRIPT TO UPDATE/DELETE THE PARTNER SUBSCRIPTION
///////////////////////////////////////////////////////////////////
require_once 'common_cs.js';
require_once 'cs_functions.inc';
Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000');
$_gotopage = 'check_partner_subscription.phtml';
PrintCommonComponents("", $_gotopage);

$_action = Request::$r->_action;
$action = &Request::$r->action;
$_object = Request::$r->_object;
$_r = Request::$r->_r;
$_companycny = Request::$r->_companycny;
$_username = Request::$r->_username;
$_partner = Request::$r->_partner;
$_intacctid = Request::$r->_intacctid;
$_externalid = Request::$r->_externalid;
$_eobject = Request::$r->_eobject;
$_eintacctid = Request::$r->_eintacctid;
$_epartner = Request::$r->_epartner;
$_eexternalid = Request::$r->_eexternalid;
$_edisplayid = Request::$r->_edisplayid;
$_updisplayid = Request::$r->_updisplayid;
$_upexternalid = Request::$r->_upexternalid;
$_upobject = Request::$r->_upobject;
$_changeid = Request::$r->_changeid;
$_uppartner = Request::$r->_uppartner;
$_upintacctid = Request::$r->_upintacctid;
$_newdisplayid = Request::$r->_newdisplayid;
$_newexternalid = Request::$r->_newexternalid;
$_actReason = Request::$r->_actReason;
$_deleteId = Request::$r->_deleteId;

Globals::$g->_dbg = 0;
ini_set("display_errors", Globals::$g->_dbg);

////////////////////////////////////////////////////////////
// INIT 
////////////////////////////////////////////////////////////
InitBase();
InitGlobals();

$_sess = Session::getKey();
$_companycny = (isset($_companycny) && $_companycny != '') ? $_companycny : $_r;
if ($_companycny == '') {
    header("Location: csservice.phtml?.gotopage=check_partner_subscription.phtml");
    exit;
}
if ($_sess == '') {
    setGlobal($_companycny);
}

////////////////////////////////////////////////////////////
// ACTIONS
////////////////////////////////////////////////////////////
switch ($_action) {
    case ('Check Subscription') :
        {

            $searchVals = array();
            $searchVals['CNY'] = $_companycny;
            $searchVals['OBJECT'] = $_object;
            $searchVals['INTACCTID'] = $_intacctid;
            $searchVals['SUBSCRIBER'] = $_partner;
            $searchVals['EXTERNALID'] = $_externalid;

            $results = GetSynchStatus($searchVals);
            $results = BuildSynchLinks($results, $searchVals['CNY'], $searchVals['SUBSCRIBER']);
            ShowSynchStatusAll($results, $searchVals['CNY'], $searchVals['SUBSCRIBER'], $searchVals['OBJECT']);
            break;
        }
    case ('openedit') :
        {

            $editVals = array();
            $obj = $_eobject;
            $partner = $_partner;
            $editVals['CNY'] = $_companycny;
            $editVals['OBJECT'] = $_eobject;
            $editVals['INTACCTID'] = $_eintacctid;
            $editVals['SUBSCRIBER'] = $_epartner;
            $editVals['EXTERNALID'] = $_eexternalid;
            $editVals['DISPLAYID'] = $_edisplayid;

            BuildEditor($editVals, 'edit');
            break;
        }
    case ('opendelete') :
        {

            $delVals = array();
            $delVals['CNY'] = $_companycny;
            $delVals['OBJECT'] = $_eobject;
            $delVals['INTACCTID'] = $_eintacctid;
            $delVals['SUBSCRIBER'] = $_epartner;
            $delVals['EXTERNALID'] = $_eexternalid;
            $delVals['DISPLAYID'] = $_edisplayid;

            BuildEditor($delVals, 'delete');
            break;
        }
    case ('Update Subscription'):

        if ((isset($_newdisplayid) && $_updisplayid != $_newdisplayid)
            && (isset($_newexternalid) && $_upexternalid != $_newexternalid)
        ) {
            Globals::$g->gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, 'Both Display Id & External Id have been modified.<br />Change either one of them and save again.');
            include_once '../acct/popuperror.phtml';
            break;
        }

        if (!isset($_actReason) || $_actReason == '') {
            Globals::$g->gErr->addError('Reason for this action is required', __FILE__ . '.' . __LINE__, 'Please add the reason for this action.');
            include_once '../acct/popuperror.phtml';
            break;
        }
        //Build data to update
        $updateVals['CNY'] = $_companycny;
        $updateVals['OBJECT'] = $_upobject;
        //$updateVals['INTACCTID'] = ($_changeid == 'intid') ? $_newintacctid : $_upintacctid;
        $updateVals['DISPLAYID'] = ($_changeid == 'intid') ? $_newdisplayid : $_updisplayid;
        $updateVals['SUBSCRIBER'] = $_uppartner;
        $updateVals['EXTERNALID'] = ($_changeid == 'extid') ? $_newexternalid : $_upexternalid;
        if ($_changeid == 'extid' && !isset($_newdisplayid)) {
            $updateVals['INTACCTID'] = $_upintacctid;
        }

        $ok = UpdateSubscription($updateVals, $_changeid);

        if ($ok) {
            $msg = "Partner subscription link update done by CS tool user -> " . ToolsAuthentication::$t->getUserName()
                . " :: Action Note -> " . $_actReason . " :: slide in as user ->" . $_userid;
            //assign variables for logging
            $notes = "<whymodified>" . $_actReason . "</whymodified>";
            if ($_changeid == 'intid') {
                $valLog = "Partnet : $_uppartner : Object : $_upobject : IntacctId updated from $_updisplayid to $_newdisplayid : ExternalId : $_upexternalid";
            } else {
                $valLog = "Partnet : $_uppartner : Object : $_upobject : ExternalId updated from $_upexternalid to $_newexternalid : IntacctId : $_updisplayid";
            }
            LogManager::getInstance()->log($msg, LOG_FILE);
            CreateLog(ToolsAuthentication::$t->getUserName(), $notes, $valLog);
        }

        break;
    case ('Delete Subscription'):

        if (!isset($_actReason) || $_actReason == '') {
            Globals::$g->gErr->addError('Reason for this action is required', __FILE__ . '.' . __LINE__, 'Please add the reason for this action.');
            include_once '../acct/popuperror.phtml';
            break;
        }
        //Build data to delete
        $deleteVals['CNY'] = $_companycny;
        $deleteVals['OBJECT'] = $_upobject;
        $deleteVals['INTACCTID'] = $_upintacctid;
        $deleteVals['SUBSCRIBER'] = $_uppartner;
        $deleteVals['EXTERNALID'] = $_upexternalid;

        if (isset($_deleteId) && $_deleteId == 'deleteId') {
            $ok = DeleteSubscription($deleteVals);
        }

        if ($ok) {
            $msg = "Partner subscription link deleted by CS tool user -> " . ToolsAuthentication::$t->getUserName()
                . " :: Action Note -> " . $_actReason . " :: slide in as user ->" . $_userid;
            //assign variables for logging
            $notes = "<whymodified>" . $_actReason . "</whymodified>";

            $valLog = "Action : Delete : Partnet : $_uppartner : Object : $_upobject : ExternalId : $_upexternalid : IntacctId : $_upintacctid";

            LogManager::getInstance()->log($msg, LOG_FILE);
            CreateLog(ToolsAuthentication::$t->getUserName(), $notes, $valLog);
        }

        break;
    default:
        DisplayPageOne($_companycny);
}

ShowNewSearchButton($_companycny, $_partner, $_object);

////////////////////////////////////////////////////////////
// list of objects
////////////////////////////////////////////////////////////

//  List of objcets to check
//	'ARACCOUNTLABEL', 'CONTACT', 'CUSTOMER', 'EXPENSEREPORT', 
//	'EXPENSETYPE', 'INVOICE', 'SERVICEITEM',
//	'USERINFO', 'VENDOR'

/**
 * This is an object map against partner
 * Partner Name => array(
 *      Display name for an object => array(
 *            'OBJECT => Intacct object,
 *            'ISMAP' => Is Id displayed in UI different from stored IntacctId in Subscription table,
 *            'TABLE' => IntacctId of Subscription table stored in table,
 *            'UID' => What user see in UI(Table column),
 *            'VID' => Actual column of TABLE = IntacctId in Subscription table,
 *
 * @return array
 */
function ObjMap()
{
    $objMap = array(
        'Salesforce' => array(
            'Customer' => array('OBJECT' => 'CUSTOMER'),
            'Item' => array('OBJECT' => 'ICITEM'),
            'Sales Document' => array('OBJECT' => 'SODOCUMENT',
                'ISMAP' => true,
                'TABLE' => 'DOCHDR',
                'UID' => 'DOCID',
                'VID' => 'RECORD#'),
            'Sales Document Opportunity' => array('OBJECT' => 'SODOCUMENTOPP',
                'ISMAP' => true,
                'TABLE' => 'DOCHDR',
                'UID' => 'DOCID',
                'VID' => 'RECORD#'),
            'Pricelist' => array('OBJECT' => 'PRICELIST'),
            'Pricelistentry' => array('OBJECT' => 'PRICELISTENTRY',
                'ISMAP' => true,
                'TABLE' => 'OEPRCLSTITEMS',
                'UID' => 'ITEMKEY',
                'VID' => 'RECORD#',
                'HASPARENT' => true),
            'Project' => array('OBJECT' => 'PROJECT',
                'ISMAP' => true,
                'TABLE' => 'PROJECT',
                'UID' => 'PROJECTID',
                'VID' => 'RECORD#'),
            'Project Opportunity' => array('OBJECT' => 'PROJECTOPP',
                'ISMAP' => true,
                'TABLE' => 'PROJECT',
                'UID' => 'PROJECTID',
                'VID' => 'RECORD#'),
            'Task' => array('OBJECT' => 'TASK',
                'ISMAP' => true,
                'TABLE' => 'TASK',
                'UID' => 'NAME',
                'VID' => 'RECORD#'),
        ),
        'SFDC V2' => array(
            'Customer' => array('OBJECT' => 'CUSTOMER'),
            'Item' => array('OBJECT' => 'ITEM'),
            'Sales Document' => array('OBJECT' => 'SODOCUMENT',
                'ISMAP' => true,
                'TABLE' => 'DOCHDR',
                'UID' => 'DOCID',
                'VID' => 'RECORD#'),
            'Sales Document Opportunity' => array('OBJECT' => 'SODOCUMENTOPP',
                'ISMAP' => true,
                'TABLE' => 'DOCHDR',
                'UID' => 'DOCID',
                'VID' => 'RECORD#'),
            'Pricelist' => array('OBJECT' => 'SOPRICELIST'),
            'Pricelistentry' => array('OBJECT' => 'SOPRICELISTENTRY',
                'ISMAP' => true,
                'TABLE' => 'OEPRCLSTITEMS',
                'UID' => 'ITEMKEY',
                'VID' => 'RECORD#',
                'HASPARENT' => true,
                'PARENTREL' => array('TABLE' => 'OEPRCLSTITEMS', 'FKEY' => 'PRCLSTKEY',
                    'INVFKEY' => 'RECORD#', 'FTABLE' => 'OEPRCLST',
                    'PARENTID' => 'OEPRCLST.NAME')
            ),
            'Project' => array('OBJECT' => 'PROJECT',
                'ISMAP' => true,
                'TABLE' => 'PROJECT',
                'UID' => 'PROJECTID',
                'VID' => 'PROJECTID'),
            'Project Opportunity' => array('OBJECT' => 'PROJECTOPP',
                'ISMAP' => true,
                'TABLE' => 'PROJECT',
                'UID' => 'PROJECTID',
                'VID' => 'PROJECTID'),
            'Task' => array('OBJECT' => 'TASK',
                'ISMAP' => true,
                'TABLE' => 'TASK',
                'UID' => 'NAME',
                'VID' => 'RECORD#'),
            'Salesforce Contract' => array('OBJECT' => 'SFCONTRACT',
                'ISMAP' => true,
                'TABLE' => 'CONTRACT',
                'UID' => 'CONTRACTID',
                'VID' => 'CONTRACTID'),
            'Intacct Contract' => array('OBJECT' => 'CONTRACT',
                'ISMAP' => true,
                'TABLE' => 'CONTRACT',
                'UID' => 'CONTRACTID',
                'VID' => 'CONTRACTID'),
            'Salesforce Contract Detail' => array('OBJECT' => 'SFCONTRACTDETAIL',
                'ISMAP' => true,
                'TABLE' => 'CONTRACTDETAIL',
                'UID' => 'LINENO',
                'VID' => 'RECORD#',
                'PARENTREL' => array('TABLE' => 'CONTRACTDETAIL', 'FKEY' => 'CONTRACTKEY',
                    'INVFKEY' => 'RECORD#', 'FTABLE' => 'CONTRACT',
                    'PARENTID' => 'CONTRACT.CONTRACTID')
            ),
            'Salesforce Contract Detail Opportunity' => array('OBJECT' => 'SFCONTRACTDETAILOPP',
                'ISMAP' => true,
                'TABLE' => 'CONTRACTDETAIL',
                'UID' => 'LINENO',
                'VID' => 'RECORD#',
                'PARENTREL' => array('TABLE' => 'CONTRACTDETAIL', 'FKEY' => 'CONTRACTKEY',
                    'INVFKEY' => 'RECORD#', 'FTABLE' => 'CONTRACT',
                    'PARENTID' => 'CONTRACT.CONTRACTID')
            ),
            'Intacct Contract Detail' => array('OBJECT' => 'CONTRACTDETAIL',
                'ISMAP' => true,
                'TABLE' => 'CONTRACTDETAIL',
                'UID' => 'LINENO',
                'VID' => 'RECORD#',
                'PARENTREL' => array('TABLE' => 'CONTRACTDETAIL', 'FKEY' => 'CONTRACTKEY',
                    'INVFKEY' => 'RECORD#', 'FTABLE' => 'CONTRACT',
                    'PARENTID' => 'CONTRACT.CONTRACTID')
            ),
            'AR Term' => array('OBJECT' => 'ARTERM'),
            'Billing Pricelist' => array('OBJECT' => 'CONTRACTPRICELIST'),
            'Billing Pricelistentry' => array('OBJECT' => 'CONTRACTITEMPRICELIST',
                'ISMAP' => true,
                'TABLE' => 'ALLOCITEMPRCLST',
                'UID' => 'ITEMKEY',
                'VID' => 'RECORD#',
                'PARENTREL' => array('TABLE' => 'ALLOCITEMPRCLST', 'FKEY' => 'ALLOCPRCLSTKEY',
                    'INVFKEY' => 'RECORD#', 'FTABLE' => 'ALLOCPRCLST',
                    'PARENTID' => 'ALLOCPRCLST.NAME')
            ),
            'Contract Billing Template' => array('OBJECT' => 'CONTRACTBILLINGTEMPLATE'),
            'Class' => array('OBJECT' => 'CLASS'),
            'Department' => array('OBJECT' => 'DEPARTMENT'),
            'Employee' => array('OBJECT' => 'EMPLOYEE'),
            'Exchange Rate Type' => array('OBJECT' => 'EXCHANGERATETYPES'),
            'Intacct Entity' => array('OBJECT' => 'LOCATIONENTITY'),
            'Project Status' => array('OBJECT' => 'PROJECTSTATUS'),
            'Project Type' => array('OBJECT' => 'PROJECTTYPE'),
            'Renewal Template' => array('OBJECT' => 'RENEWALMACRO'),
            'Territory' => array('OBJECT' => 'TERRITORY'),
            'Contact' => array('OBJECT' => 'CONTACT'),
            'User Defined Dimension' => array('OBJECT' => 'UDD',
                'TABLE' => 'PT_OBJ_DATA',
                'ISMAP' => true,
                'UID' => 'OBJ_NAME',
                'VID' => 'RECORD#'),
            'GL Account' => array('OBJECT' => 'GLACCOUNT',
                'TABLE' => 'GLACCOUNT',
                'ISMAP' => true,
                'UID' => 'TITLE',
                'VID' => 'acct_no'),
            'Statistical Account' => array('OBJECT' => 'GLACCOUNT',
                'TABLE' => 'STATACCOUNT',
                'ISMAP' => true,
                'UID' => 'TITLE',
                'VID' => 'acct_no'),
            'Journal' => array('OBJECT' => 'GLJOURNAL',
                'TABLE' => 'GLJOURNAL',
                'ISMAP' => true,
                'UID' => 'TITLE',
                'VID' => 'symbol'),
            'Statistical Journal' => array('OBJECT' => 'GLJOURNAL',
                'TABLE' => 'STATJOURNAL',
                'ISMAP' => true,
                'UID' => 'TITLE',
                'VID' => 'symbol'),
            'Journal Entry' => array('OBJECT' => 'GLBATCH',
                'TABLE' => 'GLBATCH',
                'ISMAP' => true,
                'UID' => 'batch_no',
                'VID' => 'RECORD#'),
            'Journal Entry Line' => array('OBJECT' => 'GLENTRY',
                'ISMAP' => true,
                'TABLE' => 'GLENTRY',
                'UID' => 'LINE_NO',
                'VID' => 'RECORD#',
                'PARENTREL' => array('TABLE' => 'GLENTRY', 'FKEY' => 'batch#',
                    'INVFKEY' => 'RECORD#', 'FTABLE' => 'GLBATCH',
                    'PARENTID' => 'GLBATCH.batch_no')),
        ),
        'Sage People' => array(
            'Class' => array('OBJECT' => 'CLASS'),
            'Department' => array('OBJECT' => 'DEPARTMENT'),
            'Employee' => array('OBJECT' => 'EMPLOYEE'),
            'Employee Type' => array('OBJECT' => 'EMPLOYEETYPE'),
            'Intacct Entity' => array('OBJECT' => 'LOCATIONENTITY'),
            'Earning Type' => array('OBJECT' => 'EARNINGTYPE'),
        ),
    );
    foreach ( $objMap as $key => $version){
        ksort($objMap[$key]);
    }
    return $objMap;
}

/**
 * This is map for partner name against subscriber in subscription table
 * 'Partner name' => 'SUBSCRIBER',
 *
 * @return array
 */
function partnerList()
{
    $partnerList = [
        'Salesforce' => 'SFORCE',
        'SFDC V2' => 'SFORCE',
        'Sage People' => 'SGPPL',
    ];

    return $partnerList;
}

//First page to search the subscription
/**
 * @param int    $cny
 */
function DisplayPageOne($cny)
{
    /** @noinspection PhpUnusedLocalVariableInspection */
    $objMap = ObjMap();
    BuildPageTitle("Partner Subscription Management Tool", $cny);
    ?>

    <form action="check_partner_subscription.phtml" method="post">
        <table border="0">
            <tr>
                <td align="right"><font color="red"><b>*</b></font></td>
                <td align="left"><b>Cny# :</b></td>
                <?php
                if (!isset($cny) || $cny == '') {
                    ?>
                    <td><input name=".companycny" type="text" size="15" value=""></td>
                    <?php
                } else {
                    ?>
                    <td>
                        <input type="hidden" name=".companycny" value="<?= html_prepare($cny); ?>"/>
                        <?= html_prepare($cny); ?>
                    </td>
                    <?php
                }
                ?>
            </tr>
            <tr>
                <td align="right"><font color="red"><b>*</b></font></td>
                <td align="left"><b>Partner :</b></td>
                <td><select name=".partner" id="partner" onchange="buildObjList(this.value)">
                        <?php
                        $partnerList = partnerList();
                        $select = '';
                        /** @-noinspection PhpUnusedLocalVariableInspection */
                        foreach ($partnerList as $key => $val) {
                            if ($key == 'Salesforce') {
                                $select = ' selected="selected"';
                            }
                            echo '<option value= "' . html_prepare($key) . '" ' . $select . '>' . html_prepare($key) . '</option>';
                        }
                        ?>
                    </select></td>
            </tr>
            <tr>
                <td align="right"><font color="red"><b>*</b></font></td>
                <td align="left"><b>Object :</b></td>
                <td><select name=".object" id="object"></select></td>
            </tr>
            <tr>
                <td align="left">&nbsp;</td>
                <td align="left"><b>Intacct Id :</b></td>
                <td><input name=".intacctid" type="text" size="80"/></td>
            </tr>
            <tr>
                <td align="left">&nbsp;</td>
                <td align="left"><b>External Id :</b></td>
                <td><input name=".externalid" type="text" size="80"/></td>
            </tr>
            <tr>
                <td colspan="3" align="center">&nbsp;</td>
            </tr>
            <tr>
                <td colspan="3" align="center"><input type="submit" name=".action" value="Check Subscription"/></td>
            </tr>
            <tr>
                <td colspan="3" align="center">&nbsp;</td>
            </tr>
            <tr>
                <td align="center">(<font color="red"><b>*</b></font>)</td>
                <td align="left">Mandatory field</td>
                <td align="left">&nbsp;</td>
            </tr>
        </table>
    </form>

    <script type="text/javascript">
        window.history.forward();
        var myObjects = new Array();

        <?php
        // Build dropdown list of Intacct object against partner
        $objMap = ObjMap();
        foreach($objMap as $partner => $inObjMap) {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $cnt = count($inObjMap);
        $i = 0;
        ?>
        myObjects['<?php echo $partner;?>'] = new Array();
        <?php
        foreach($inObjMap as $key => $val) { ?>

        myObjects['<?php echo $partner;?>']['<?php echo $i; ?>'] = '<?php echo $key; ?>';
        <?  $i++;
        }
        }
        ?>

        //Body onload
        //Keep partner Salesforce as default
        onbodyload();

        function onbodyload() {
            var partner = document.getElementById("partner").value = 'Salesforce';
            buildObjList('Salesforce');
        }

        function buildObjList(selectbox) {

            var combo = document.getElementById("object");
            if (selectbox == '') {
                var partner = document.getElementById("partner").value;
            } else {
                var partner = selectbox;
            }
            var cnt = myObjects[partner].length;
            document.getElementById("object").options.length = 0;

            for (var i = 0; i < cnt; ++i) {
                var optn = document.createElement("OPTION");
                optn.text = myObjects[partner][i];
                optn.value = myObjects[partner][i];
                combo.options.add(optn);
            }
        }

    </script>

    <?php
}

/**
 * Editor page for Edit/Delete of partnet susbcription link
 *
 * @param array  $editVals data set for update/delete
 * @param string $action   action
 */
function BuildEditor($editVals, $action)
{
    if ($action == 'edit') {
        $headertxt = 'Edit';
        $disabled = '';
    } else {
        $headertxt = 'Delete';
        $disabled = 'disabled="disabled"';
    }
    /** @noinspection PhpUnusedLocalVariableInspection */
    $partnerList = partnerList();
    BuildPageTitle("$headertxt Partner Subscription", $editVals['CNY']);

    ?>

    <form name="editdelform" id="editdelform" action="check_partner_subscription.phtml" method="post">
        <input type="hidden" name=".companycny" value="<?= html_prepare($editVals['CNY']); ?>"/>
        <input type="hidden" name=".upobject" value="<?= html_prepare($editVals['OBJECT']); ?>"/>
        <input type="hidden" name=".upintacctid" value="<?= html_prepare($editVals['INTACCTID']); ?>"/>
        <input type="hidden" name=".uppartner" value="<?= html_prepare($editVals['SUBSCRIBER']); ?>"/>
        <input type="hidden" name=".upexternalid" value="<?= html_prepare($editVals['EXTERNALID']); ?>"/>
        <input type="hidden" name=".updisplayid" value="<?= html_prepare($editVals['DISPLAYID']); ?>"/>
        <table border="0">
            <tr>
                <td><b>Object:</b></td>
                <td><?= html_prepare($editVals['OBJECT']); ?></td>
            </tr>

            <tr>
                <td><b>Intacct Internal Id:</b></td>
                <td><?= html_prepare($editVals['INTACCTID']); ?></td>
            </tr>

            <tr>
                <td><b>Intacct Display Id:</b></td>
                <td><input id=".newdisplayid" name=".newdisplayid" type="text" disabled="disabled"
                           value="<?= html_prepare($editVals['DISPLAYID']); ?>" size="80"/></td>
            </tr>

            <tr>
                <td><b>Partner:</b>
                <td><?= html_prepare($editVals['SUBSCRIBER']); ?></td>
                </td></tr>

            <tr>
                <td><b>External Id:</b></td>
                <td><input id=".newexternalid" name=".newexternalid" <?= $disabled; ?> type="text"
                           value="<?= html_prepare($editVals['EXTERNALID']); ?>" size="30"/></td>
            </tr>
            <tr>
                <td><b><font color='red'>Reason for this action:</font></b></td>
                <td><input id="actReason" name=".actReason" type="text" value="" size="100"/ title="This can be
                    ticket/case number or short description upto 100 characters">
                </td>
            </tr>
            <tr>
                <td colspan="2" align="center">&nbsp;</td>
            </tr>
            <?php if ($action == 'edit') { ?>
                <tr>
                    <td><input type="radio" name=".changeid" value="extid" id="extid" onclick="disableField()"
                               checked="checked"/><b>Update External Id</b></td>
                    <td><input type="radio" name=".changeid" value="intid" id="intid" onclick="disableField()"/><b>Update
                            Intacct Id</b></td>
                </tr>

                <tr>
                    <td colspan="2" align="center">&nbsp;</td>
                </tr>
                <input type="hidden" name=".action" id=".action" value="Update Subscription"/>
                <tr>
                    <td colspan="2" align="center"><input type="button" value="Update Subscription"
                                                          onclick="checkReason()"/></td>
                </tr>

                <?php
            } else { ?>
                <tr>
                    <td colspan="2" align="center"><b>Please select the below checkbox if you want to delete this
                            subscription entry permanently</b></td>
                <tr>
                    <td align="right"><input type="checkbox" name=".deleteId" value="deleteId" id="deleteId"/></td>
                    <td><b><font color='red'>Yes, I want to delete this subscription entry.</font></b></td>
                    <input type="hidden" name=".action" id=".action" value="Delete Subscription"/>
                <tr>
                    <td colspan="2" align="center"><input type="button" value="Delete Subscription"
                                                          onclick="checkform()"/></td>
                </tr>
                <?php
            } ?>

        </table>
    </form>

    <script type="text/javascript">
        function disableField() {
            var extid;
            var intid;
            extid = document.getElementById("extid").checked;
            intid = document.getElementById("intid").checked;

            if (extid) {
                //document.getElementById(".newintacctid").disabled = true;
                document.getElementById(".newdisplayid").disabled = true;
                document.getElementById(".newexternalid").disabled = false;
            }
            if (intid) {
                //document.getElementById(".newintacctid").disabled = false;
                document.getElementById(".newdisplayid").disabled = false;
                document.getElementById(".newexternalid").disabled = true;
            }
        }

        function checkReason() {
            var actReason = document.getElementById("actReason").value;
            if (actReason == '') {
                alert("Please fill the reason for this action.");
                return false;
            } else {
                document.getElementById("editdelform").submit();
                return false;
            }
        }

        function checkform() {
            var delAction = document.getElementById("deleteId").checked;
            if (delAction) {
                //document.getElementById("_action").value = "Delete Subscription";
                //document.getElementById("editdelform").submit();
                checkReason()
                //return false;
            } else {
                alert("Plese select checkbox 'Yes, I want to delete this subscription entry' to delete the subscription entry");
                return false;
            }
        }


    </script>

    <?php
}

/**
 * Message page
 *
 * @param string $header  header of page
 * @param string $message display message
 * @param int    $cny     compant record#
 * @param bool   $success shows success or failure message status
 */
function DisplayMessagePage($header, $message, $cny, $success = true)
{

    BuildPageTitle($header, $cny);
    ?>

    <table border="0" cellpadding="0" cellspacing="1" bgcolor="#999999">
        <tr>
            <td>
                <table border="0" cellpadding="15" cellspacing="0" bgcolor="#999999">
                    <tr>
                        <?php if ($success) { ?>
                            <td bgcolor="#bdfeb6" style="width:45px;">
                                <img src="../resources/images/ia-app/icons/success.png" alt="Success"/>
                            </td>
                            <td bgcolor="#bdfeb6" align="center"><font size="2"><b><?= html_prepare($message); ?></b></font>
                            </td>
                            <td bgcolor="#bdfeb6" style="width:45px;">&nbsp;</td>
                            <?php
                        } else { ?>
                            <td bgcolor="#bdfeb6" align="center"><font size="2"><b><?= html_prepare($message); ?></b></font>
                            </td>
                            <?php
                        } ?>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <?php
}

/**
 * Susbcription link set
 *
 * @param array $searchVals search parameter
 *
 * @return string[][]|bool
 */
function GetSynchStatus($searchVals)
{

    $gErr = Globals::$g->gErr;
    $objMap = ObjMap();
    $partnerList = partnerList();
    $subscriber = $searchVals['SUBSCRIBER'];
    $object = $objMap[$subscriber][$searchVals['OBJECT']]['OBJECT'];
    $ismap = $objMap[$subscriber][$searchVals['OBJECT']]['ISMAP'];
    $hasParent = $objMap[$subscriber][$searchVals['OBJECT']]['HASPARENT'];
    $parentRel = $objMap[$subscriber][$searchVals['OBJECT']]['PARENTREL'];
    $sqlquery = array();
    $i = 0;

    if (!isset($object) || $object == '') {
        $gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, 'Search object not found.');
    }

    $selectParent = '';
    if (isset($hasParent) && $hasParent) {
        $selectParent = ', SUBSCRIPTION.PARENTID';
    }
    $TABLE = "";
    $UID = "";
    $selectDisplayId = 'SUBSCRIPTION.INTACCTID';
    $whereClause = "SUBSCRIPTION.CNY# = :1 and SUBSCRIPTION.OBJECT = :2 and SUBSCRIPTION.SUBSCRIBER = :3";
    if (isset($ismap) && $ismap) {

        $TABLE = $objMap[$subscriber][$searchVals['OBJECT']]['TABLE'];
        $VID = $objMap[$subscriber][$searchVals['OBJECT']]['VID'];
        $UID = $objMap[$subscriber][$searchVals['OBJECT']]['UID'];

        $selectDisplayId = $TABLE . '.' . $UID;
        $whereClause .= " and $TABLE.CNY# = SUBSCRIPTION.CNY# and $TABLE.$VID = SUBSCRIPTION.INTACCTID";
    }

    $fromTables = 'SUBSCRIPTION';
    if (!empty($TABLE)) {
        $fromTables .= ' ,' . $TABLE;
    }

    if (!empty($parentRel)) {
        $selectParent = ',' . $parentRel['PARENTID'] . " as PARENTID";
        $fromTables .= ' ,' . $parentRel['FTABLE'];
        $whereClause .= " and " . $parentRel['FTABLE'] . ".CNY# = SUBSCRIPTION.CNY# and "
            . $parentRel['FTABLE'] . "." . $parentRel['INVFKEY'] . " = " . $parentRel['TABLE'] . "." . $parentRel['FKEY'];
    }

    $sql = "select CONCAT('','" . $searchVals['OBJECT'] . "') as OBJECT, $selectDisplayId as DISPLAYID, 
    SUBSCRIPTION.INTACCTID, SUBSCRIPTION.SUBSCRIBER, SUBSCRIPTION.EXTERNALID $selectParent
    from $fromTables
    where $whereClause";

    $sqlquery[$i++] = '';
    $sqlquery[$i++] = $searchVals['CNY'];
    $sqlquery[$i++] = $object;
    $sqlquery[$i++] = $partnerList[$subscriber];
    if (isset($searchVals['INTACCTID']) && $searchVals['INTACCTID'] != '') {
        if (isset($ismap) && $ismap) {
            $sql .= " and $TABLE.$UID like :$i";
            $sqlquery[$i++] = "%" . $searchVals['INTACCTID'] . "%";
        } else {
            $sql .= " and SUBSCRIPTION.INTACCTID like :$i";
            $sqlquery[$i++] = "%" . $searchVals['INTACCTID'] . "%";
        }
    }

    if (isset($searchVals['EXTERNALID']) && $searchVals['EXTERNALID'] != '') {
        $sql .= " and  SUBSCRIPTION.EXTERNALID like :$i";
        $sqlquery[$i] = "%" . $searchVals['EXTERNALID'] . "%";
    }
    $sqlquery[0] = $sql;

    $res = QueryResult($sqlquery);

    if ((HasErrors() && $gErr->ErrorCount)) {
        $gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, 'Unable to featch the data.');
        include_once '../acct/popuperror.phtml';
        exit;
    }

    return $res;
}

/**
 * Build edit/delete link
 *
 * @param array $results result set
 * @param int   $cny     company record#
 * @param int   $partner partner
 *
 * @return mixed
 */
function BuildSynchLinks($results, $cny, $partner)
{

    $_sess = Session::getKey();

    foreach ($results as $key => $result) {
        $linkinit = "check_partner_subscription.phtml?.action=";
        $linkend = '&.companycny=' . urlencode($cny) . '&.eobject=' . urlencode($result['OBJECT']) .
            '&.eintacctid=' . urlencode($result['INTACCTID']) . '&.epartner=' . urlencode($partner) . '&.eexternalid=' . urlencode($result['EXTERNALID']) . '&.sess=' . urlencode($_sess) . '&.edisplayid=' . urlencode($result['DISPLAYID']);
        $results[$key]['ACTION'] = getEditDeleteLink([$linkinit, $linkend]);
    }
    return $results;
}


/**
 * Prepares the edit and delete link
 * @param array $linkParts
 * @return string
 */
function getEditDeleteLink($linkParts)
{
    $editlink = $linkParts[0] . 'openedit' . $linkParts[1];
    $dellink = $linkParts[0] . 'opendelete' . $linkParts[1];
    // I18N: TODO
    $builtLink = '<a href =' . html_prepare($editlink) . '>Edit</a>' . ' | <a href =' . html_prepare($dellink) . '>Delete</a>';
    return $builtLink;
}

/**
 * Build table to display
 *
 * @param array $table   result set
 * @param int   $partner string
 */
function BuildSimpleTable($table, $partner)
{
    //$partnerList = partnerList();
    ?>
    <table border="0" cellpadding="1" cellspacing="0" bgcolor="#999999" width="95%">
        <tr>
            <td>
                <table border="0" cellpadding="3" cellspacing="1" bgcolor="#999999" width=100%>
                    <tr>
                        <?php
                        if (empty($table) || count($table) == 0) {
                            ?>
                            <td bgcolor="#EEEEEE" align="center"><b>Matching data not found.</b></td>
                            <?php
                        } else {
                        foreach ($table[0] as $colname => $rowval) {
                            //if($colname != 'INTACCTID') {
                            if ($colname == 'SUBSCRIBER') {
                                $colname = 'PARTNER';
                            }
                            ?>
                            <td bgcolor="#999999"><b><?= html_prepare($colname); ?></b></td>
                            <?php
                        }
                        ?>
                    </tr>
                    <?php
                    foreach ($table as $rowkey => $row) {
                        echo "<tr>";
                        $rowcolor = ($rowkey % 2 == 0) ? "#EEEEEE" : "#DEDEDE";
                        foreach ($row as $col => $rowval) {
                            if ($col == 'SUBSCRIBER') {
                                $rowval = $partner;
                            }
                            if ($col === 'ACTION') {
                                ?>
                                <td bgcolor="<?= $rowcolor; ?>"><?= $rowval ?></td>
                                <?php
                            } else {
                                ?>
                                <td bgcolor="<?= $rowcolor; ?>"><?= html_prepare($rowval); ?></td>
                                <?php
                            }
                        }
                        echo "</tr>";

                    }
                    }
                    ?>
                </table>
            </td>
        </tr>
    </table>
    <?php
}


/**
 * Build sync status
 *
 * @param array  $results result set
 * @param int    $cny     company record#
 * @param string $partner partner
 * @param string $obj     object
 */
function ShowSynchStatusAll($results, $cny, $partner, $obj)
{
    BuildPageTitle("Partner Subscription Map", $cny);
    ShowNewSearchButton($cny, $partner, $obj);
    BuildSimpleTable($results, $partner);
}


/**
 * Build page title
 *
 * @param string $title company name
 * @param int    $cny   company record#
 */
function BuildPageTitle($title, $cny)
{
    global $_destTitle;
    ?>
    <table border="0" width="95%">
        <tr>
            <td valign="center" align="center"><font size="2"><b><?= html_prepare($title); ?></b></font></td>
        <tr>
            <?php if ($_destTitle != '') { ?>
        <tr>
            <td align="center">
                <table border="0" cellpadding="0" cellspacing="0" bgcolor="#999999" width="95%">
                    <tr>
                        <td>
                            <table border="0" cellpadding="3" cellspacing="1" bgcolor="#999999" width=100%>
                                <tr>
                                    <td bgcolor="#DEDEDE" align="center"><b><?= html_prepare("$cny : $_destTitle"); ?></b></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <?php
        } ?>
        <tr>
            <td>&nbsp;</td>
        <tr>
    </table>
    <?php
}


/**
 * Build search button
 *
 * @param int    $cny     company record#
 * @param string $partner partner
 * @param string $obj     object
 */
function ShowNewSearchButton($cny, $partner, $obj)
{
    $_sess = Session::getKey();
    ?>
    <br/>
    <form action="check_partner_subscription.phtml" method="post">
        <input type="hidden" name=".r" value="<?= html_prepare($cny); ?>"/>
        <input type="hidden" name=".partner" value="<?= html_prepare($partner); ?>"/>
        <input type="hidden" name=".object" value="<?= html_prepare($obj); ?>"/>
        <input type="hidden" name=".sess" value="<?= html_prepare($_sess); ?>"/>
        <table border="0" cellpadding="1" cellspacing="0" width="95%">
            <tr>
                <td>
                    <table border="0" cellpadding="0" cellspacing="0" width=100%>
                        <tr>
                            <td align="center"><input type="submit" value="Search again"/></td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </form>
    <?php
}


/**
 * Build page footer
 *
 * @param int $_dest company record#
 */
function setGlobal($_dest)
{
    include_once 'slide.inc';

    //Slide in as Intacct CS User
    global $_userid, $_destTitle;
    $sess = "intacct";
    Request::$r->_sess = $sess;
    //$ok = FetchCSUserLink($_dest, $_destTitle, $_destUser, $_destPassword);

    //if ($ok) {
    $_userid = "1@$_dest";
    //$_userid = "$_destUser@$_dest";
    //set dbschema and variables to company schema
    SetDBSchema($_dest, 'cny#');
    $qrycompany = "select company.* from COMPANY company where company.record# = :1";
    $comp_data = QueryResult(array($qrycompany, $_dest));
    $_destTitle = $comp_data[0]['TITLE'];
    //}
}


/**
 * Update subscription
 *
 * @param array  $updateVals result set
 * @param string $_changeid  modified value
 *
 * @return bool
 */
function UpdateSubscription($updateVals, $_changeid)
{

    $gErr = Globals::$g->gErr;
    $objMap = ObjMap();
    $partnerList = partnerList();
    $subscriber = $updateVals['SUBSCRIBER'];
    $object = $objMap[$subscriber][$updateVals['OBJECT']]['OBJECT'];
    //$ismap = $objMap[$updateVals['OBJECT']]['ISMAP'];
    $ok = true;
    $setVal = "";

    if ($_changeid == 'intid') {

        $setVar = "set INTACCTID = :1";
        //$setVal = $updateVals['INTACCTID'];
        $whereVar = "EXTERNALID = :4";
        $whereVal = $updateVals['EXTERNALID'];

        //Since display Id is changed so get the internal Intacct Id
        $ismap = $objMap[$subscriber][$updateVals['OBJECT']]['ISMAP'];
        if (isset($ismap) && $ismap) {

            $TABLE = $objMap[$subscriber][$updateVals['OBJECT']]['TABLE'];
            $VID = $objMap[$subscriber][$updateVals['OBJECT']]['VID'];
            $UID = $objMap[$subscriber][$updateVals['OBJECT']]['UID'];

            $sql = "select $VID as INTACCTID from $TABLE where $UID = :1 and CNY# = :2";
            $res = QueryResult(array($sql, $updateVals['DISPLAYID'], $updateVals['CNY']));

            if (empty($res)) {
                $gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, "'" . $updateVals['DISPLAYID'] . "' doesnot exists for object '" . $updateVals['OBJECT'] . "'.");
                $ok = false;
            } else {
                $setVal = $res[0]['INTACCTID'];
            }

        } else {
            $setVal = $updateVals['INTACCTID'] ?: $updateVals['DISPLAYID'];
        }
    } else {
        //Since display Id is not changed so internal Intacct Id can be used in where condition
        $setVar = "set EXTERNALID = :1";
        $setVal = $updateVals['EXTERNALID'];
        $whereVar = "INTACCTID = :4";
        $whereVal = $updateVals['INTACCTID'];
    }

    $sql = "update SUBSCRIPTION " . $setVar . " where OBJECT = :2 and SUBSCRIBER = :3 and " . $whereVar . " and CNY# = :5";

    try {
        $ok = $ok && ExecStmt(array($sql, $setVal, $object, $partnerList[$subscriber], $whereVal, $updateVals['CNY']));
    } catch (Exception $e) {
        $gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, $e->getMessage());
        include_once 'popuperror.phtml';
        $ok = false;
    }

    //if ($gErr->hasErrors()){
    if ((HasErrors() && $gErr->ErrorCount) || !$ok) {
        $gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, 'Subscription update is failed');
        include_once '../acct/popuperror.phtml';
        $ok = false;
    } else {
        DisplayMessagePage("Success Message", "Subscription update is successful.", $updateVals['CNY']);
    }
    return $ok;
}

/**
 * Delete subscription
 *
 * @param array $deleteVals result set
 *
 * @return bool
 */
function DeleteSubscription($deleteVals)
{
    $gErr = Globals::$g->gErr;
    $partnerList = partnerList();
    $subscriber = $partnerList[$deleteVals['SUBSCRIBER']];
    $objMap = ObjMap();
    $object = $objMap[$deleteVals['SUBSCRIBER']][$deleteVals['OBJECT']]['OBJECT'];
    /** @noinspection PhpUnusedLocalVariableInspection */
    $ok = true;

    $sql = "delete SUBSCRIPTION where OBJECT = :1 and SUBSCRIBER = :2 and INTACCTID = :3 and EXTERNALID = :4 and CNY# = :5";
    try {
        $ok = ExecStmt(
            array($sql,
                $object, $subscriber, $deleteVals['INTACCTID'], $deleteVals['EXTERNALID'], $deleteVals['CNY'])
        );
    } catch (Exception $e) {
        //$gErr = Globals::$g->gErr;
        $gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, $e->getMessage());
        $ok = false;
    }

    if ((HasErrors() && $gErr->ErrorCount) || !$ok) {
        $gErr->addError('Internal Error:', __FILE__ . '.' . __LINE__, 'Subscription delete is failed');
        include_once '../acct/popuperror.phtml';
        $ok = false;
    } else {
        DisplayMessagePage("Success Message", "Subscription delete is successful.", $deleteVals['CNY']);
    }
    return $ok;
}


/**
 * Create company log
 *
 * @param string $user  userid
 * @param string $notes note to store
 * @param string $valLog
 *
 * @internal param int $_companycny company record#
 */
function CreateLog($user, $notes, $valLog)
{

    $gManagerFactory = Globals::$g->gManagerFactory;
    $companyLogManager = $gManagerFactory->getManager('companylog');

    $coinfo = array();
    $coinfoOld = array();
    //company is created from which tool
    $coinfoOld = array_merge($coinfoOld, array('CREATED_FROM_WHERE' => 'Partner subscription tool : ' . $valLog));
    $remoteIP = &Globals::$g->REMOTE_ADDR;
    $logarray = array(
        'COLOG' => $coinfo,
        'COLOGOLD' => $coinfoOld,
        'USER' => $user,
        'DATE' => GetOraTimeStamp(),
        'IP' => $remoteIP,
        'NOTES' => $notes,
    );
    $companyLogManager->insertCompanyLog($logarray);
}

