<?

require_once 'querytool_util.inc';
require_once 'querytool_metadata_util.inc';

$_cnytitle = Request::$r->_cnytitle;
$_entity = (int) Request::$r->_entity;
$_cmd = Request::$r->_cmd;
$_data = Request::$r->_data;
$_table = Request::$r->_table;
$_column = Request::$r->_column;
$_operator = Request::$r->_operator;
$_value = Request::$r->_value;
$_count = Request::$r->_count;
$_selects = Request::$r->_selects;
$_includeCFs = Request::$r->_includeCFs;
$_includeFKs = Request::$r->_includeFKs;
$_includeAuditCols = Request::$r->_includeAuditCols;
$_includeRelCols = Request::$r->_includeRelCols;
$_dataSet = Request::$r->_dataSet;
$_sortbySet = Request::$r->_sortbySet;
$_freeSql = Request::$r->_freeSql;
$_simulateProd = (Request::$r->_simulateProd == 'true');
$isFreeFormSQLEnabled = isFreeFormSQLEnabled();
$isProd = $_simulateProd || isProd();

//forcing to fetch maximum of 100 rows
if (!$_count || $_count > 100) {
    $_count = 100;
}

if (!empty($_data)) {
    $_data = json_decode($_data, true);
}

$arr = array();
if ($_cnytitle) {
    $cnyDetails = getCnyDetailsByTitle($_cnytitle, $errStatus, $_entity);
    if (!$cnyDetails) {
        $errMsg = getCnyErrorMessage($errStatus);

        $arr['STATUS'] = false;
        $arr['ERRMSG'] = $errMsg;

    } else if ($_cmd) {
        if ($_cmd == "getColumns" && $_table) {
            $arr = getColumns($cnyDetails, $_table);
        } else if ($_cmd == "getRows" && $_table) {
            $arr = getRowsServer($cnyDetails, $_table, $_column, $_operator, $_value, $_count, $_sortbySet,
                                 $_selects, $_includeCFs, $_includeFKs, $_dataSet, $_includeAuditCols,
                                 $_includeRelCols);
        } else if ($_cmd == "getRows" && $_freeSql && $isFreeFormSQLEnabled) {
            $arr = getFreeSqlRowsServer($cnyDetails, $_freeSql, $_count, $isProd);
        } else if ($_cmd == "getExplain" && $_freeSql && $isFreeFormSQLEnabled) {
            $arr = getExplainServer($cnyDetails, $_freeSql, $isProd);
        } else if ($_cmd == "getOwnedObjectCounts" && $_table && $_value) {
            $arr = getOwnedObjectCounts($cnyDetails, $_table, $_value);
        } else if ($_cmd == "rebuildCache") {
            rebuildCache($cnyDetails);
            $arr = null;
        }
    }
} else if ($_cmd == "getStats") {
    if (empty($_data)) {
        $arr = getStats();
    } else {
        $arr = getQueries($_data);
    }
}

echo(json_encode($arr));
exit();
