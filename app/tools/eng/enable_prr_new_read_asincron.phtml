<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2019 Intacct Corporation All, Rights Reserved
 *
 *  Intacct Corporation Proprietary Information.
 *  This document contains trade secret data that belongs to Intacct
 *  corporation and is protected by the copyright laws. Information herein
 *  may not be used, copied or disclosed in whole or part without prior
 *  written consent from Intacct Corporation.
 */
require_once 'util.inc';
include 'rel_common.inc';
$_cny = Request::$r->_cny;
$from = Request::$r->from ?? 'tool';
$timestamp = Request::$r->timestamp;
$requestTimeLimitMS = Request::$r->requestTimeLimitMS;
define("PRR_TIMELIMIT_MS", $requestTimeLimitMS);

// Check all required values
$gConn = GetGlobalConnection();
if ( !$gConn ) {
    echo requestAsincronToolResponse($from, 'Cannot get handle to global DB connection.', false);
    
    return null;
}

ob_start();
enablePRRNewRead($_cny, $from);
