<?php
/**
 * Update the Web Services Only flag of users
 *
 * @access public
 * <AUTHOR> <<EMAIL>>
 */

/**
 * Class WSOUserLister
 */
class WSOUserLister extends NLister
{

    /**
     * @var string $filterWebserviceOnlyUsers
     */
    private $filterWebserviceOnlyUsers;

    /**
     * @param array $params
     *
     * @throws Exception
     */
    public function __construct($params = [])
    {
        $this->filterWebserviceOnlyUsers = strtoupper($params['FILTER_WEBSERVICEONLY_USERS']);
        parent::__construct(
            [
                'entity'         => $params['entity'],
                'title'          => 'User',
                'fields'         => [ 'LOGINID', 'USERTYPE', 'RECORDNO', 'ADMIN', 'LOGINDISABLED' ],
                'helpfile'       => 'Viewing_and_Managing_User_Lists',
                //'nofilteronthesefields' => ['WSO', 'USERTYPE'],
                'nosort'         => [ 'USERPERM' => 1 ],
                'entitynostatus' => true,
                'edit'           => 'change_wso_flag.phtml?cny=' . $params['cny'],
                //'key' => 'UserCNY',
                'op'             => [
                    'edit' => true,
                ],
                'recordkey'      => $params['cny'],
            ]
        );
    }

    /**
     * @return array
     */
    public function BuildQuerySpec()
    {
        $querySpec = parent::BuildQuerySpec();

        //we override the status filter so that lockedout users show in active list
        $defaultFilter = [ "STATUS", "=", "active" ];
        foreach ( $querySpec['filters'][0] as $filterKey => $filterValue ) {
            if ( $filterValue == $defaultFilter ) {
                $querySpec['filters'][0][$filterKey] = [ "STATUS", "!=", 'inactive' ];
                break;
            }
        }

        //do not show hidden users and CRM
        $querySpec['filters'][0][] = [ 'VISIBLE', '=', 'true' ];
        if ( ! empty($this->filterWebserviceOnlyUsers) ) {
            $querySpec['filters'][0][] =
                [ 'LOGINDISABLED', '=', ( $this->filterWebserviceOnlyUsers === 'Y' ) ? 'true' : 'false' ];
        }

        $querySpec['filters'][0][] = [ 'USERTYPE', '!=', 'CRM user' ];

        return $querySpec;
    }

    /**
     * @return bool
     */
    public function BuildTable()
    {
        parent::BuildTable();
        $flds = [
            'LOGINID',
            'USERTYPE',
            'STATUS',
            'FILTER_WEBSERVICEONLY_USERS',
        ];
        $lbls = [
            _('User name '),
            _('User type'),
            _('Status'),
            _('Is WSO'),
        ];
        $fldOrder = [
            'LOGINID',
            'USERTYPE',
            'STATUS',
            'LOGINDISABLED',
        ];

        $table = &$this->table;
        $count = count($table);
        for ( $i = 0; $i < $count; $i++ ) {
            // uppercase the user types
            $table[$i]['USERTYPE'] = isl_ucwords($table[$i]['USERTYPE']);
            $table[$i]['FILTER_WEBSERVICEONLY_USERS'] =
                ( $table[$i]['LOGINDISABLED'] === 'true' && $table[$i]['USERTYPE'] !== 'CRM User' ) ? 'Y' : 'N';
        }
        $this->SetOutputFields($flds, $lbls, $fldOrder);

        return true;
    }

    /**
     * @param  string          $user
     * @param  int             $cny
     * @param  UserInfoManager $userInfo
     */
    public function wsoForm($user, $cny, $userInfo)
    {
        $data = $userInfo->get($user);
        ?>
        <form method="POST" action="change_wso_flag.phtml?.r=<?php echo $cny ?>">
            <table align="center" style="margin-top: 30px; width: 200px">
                <tr>
                    <td>User ID</td>
                    <td><?php echo $user ?></td>
                </tr>
                <tr>
                    <td>User Type</td>
                    <td><?php echo ucwords($data['USERTYPE']) ?></td>
                </tr>
                <tr>
                    <td>Web Service Only</td>
                    <td><input type="checkbox" value="T"
                               name="logindisabled" <?php echo ( $data['LOGINDISABLED'] === 'true' ) ? ' checked'
                            : '' ?>>
                    </td>
                </tr>
                <tr>
                    <td><a href="change_wso_flag.phtml?.r=<?php echo $cny ?>">Go Back</a></td>
                    <td>
                        <input type="hidden" name=".r" value="<?php echo $cny ?>">
                        <input type="hidden" name=".user" value="<?php echo $user ?>">
                        <input type="hidden" name=".do" value="save">
                        <input type="hidden" name=".done"
                               value="<?php echo insertDone('change_wso_flag.phtml?.r=' . $cny) ?>">
                        <input type="submit" value="Update" name="Update">
                    </td>
                </tr>
            </table>
        </form>
        <?php
    }
}