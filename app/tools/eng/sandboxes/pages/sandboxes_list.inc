<?php
    //=============================================================================
    //
    //	FILE:        sandboxes_list.inc
    //	AUTHOR:      <PERSON> <<EMAIL>>
    //	DESCRIPTION: sandbox list page (sandboxes cs tool)
    //
    //	(C)2022, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    //
    // init
    //

    // just to keep inspections happy...
    $r_cny = Request::$r->cny;
    // end inspections

    $company_info = null;
    if (!empty($r_cny)) {
        $company_info = get_company_general_info($r_cny, "cny#");
    }

    // init lister
    $oLister = new WebCSLister("sandbox");
    $oLister->columns = array(
        "ops" => array("column" => "ops", "label" => "", "width" => "7.5rem"),
        "productiontitle" => array("column" => "PRODUCTIONTITLE", "label" => "Production title", "width" => "20%", "filter" => ""),
        "productionname" => array("column" => "PRODUCTIONNAME", "label" => "Production name", "width" => "20%", "filter" => ""),
        "sandboxtitle" => array("column" => "SANDBOXTITLE", "label" => "Sandbox title", "width" => "20%", "filter" => ""),
        "state" => array("column" => "STATE", "label" => "State", "width" => "10%", "filter" => ""),
        "whencreated" => array("column" => "CREATED", "label" => "Created", "width" => "15%", "filter" => ""),
        "whenmodified" => array("column" => "MODIFIED", "label" => "Modified", "width" => "15%", "filter" => ""),
        "switch_to" => array("column" => "switch_to", "label" => "", "width" => "5.5rem"),
    );
    $oLister->orderBy = "whenmodified";
    $oLister->order = "desc";
    $oLister->baseURL = "sandboxes.phtml" . (empty($r_cny) ? "" : "?page=list&cny=" . urlencode($r_cny));
    $oLister->template = "cs_lister_v2";

    if ($oLister->Init($_REQUEST) === WebCSLister::INVALID_REQUEST) {
        // invalid request; 404
        require "cs_not_found.inc";

        exit;
    }

    //
    // ops
    //

    //
    // data
    //

    // get sandbox list
    if (!empty($r_cny)) {
        $oLister->filters["CNY"] = $r_cny;
    }
    $oLister->data = SandboxModel::ListDetails(
        $oLister->filters,
        $oLister->columns[$oLister->orderBy]["column"],
        $oLister->order,
        $oLister->rowsPerPage * ($oLister->page - 1),
        $oLister->rowsPerPage
    );

    foreach ($oLister->data as &$sandbox) {
        $sandbox_title = $sandbox["SANDBOXTITLE"];

        if ($sandbox["CNY#"] != "") {
            $sandbox["PRODUCTIONTITLE"] = array(
                "type" => "link",
                "links" => array(
                    array(
                        "label" => $sandbox["PRODUCTIONTITLE"],
                        "url" => "index.phtml?page=company&cny=" . $sandbox["CNY#"],
                        "target" => "_blank",
                    ),
                ),
            );
        }

        if ($sandbox["SANDBOXCNY#"] != "") {
            $sandbox["SANDBOXTITLE"] = array(
                "type" => "link",
                "links" => array(
                    array(
                        "label" => $sandbox_title,
                        "url" => "index.phtml?page=company&cny=" . $sandbox["SANDBOXCNY#"],
                        "target" => "_blank",
                    ),
                ),
            );
        }

        $sandbox["ops"] = array(
            "type" => "link",
            "links" => array(
                array(
                    "label" => "View",
                    "url" => "sandboxes.phtml?page=view&cny=" . urlencode($sandbox["CNY#"]) . "&record=" . urlencode($sandbox["RECORD#"]),
                ),
                array(
                    "label" => "History",
                    "url" => "sandboxes.phtml?page=history_list&cny=" . urlencode($sandbox["CNY#"]) . "&sandbox_key=" . urlencode($sandbox["RECORD#"]),
                ),
            ),
        );

        if ($sandbox["SANDBOXCNY#"] != "") {
            $sandbox["switch_to"] = array(
                "type" => "link",
                "links" => array(
                    array(
                        "label" => "Switch to",
                        "url" => "csslide.phtml?destCny=" . $sandbox["SANDBOXCNY#"] . "&destName=" . urlencode($sandbox_title),
                    ),
                ),
            );
        }
    }
    unset($sandbox);

    $header_buttons = array(
        array("type" => "link", "label" => "History search", "url" => "sandboxes.phtml?page=search",),
    );

    $company_name = ($company_info === null ? "" : (" " . html_prepare($company_info["NAME"]) . " (" . html_prepare($company_info["TITLE"] . ")")));

    //
    // template
    //

    // frame
    $t["page_name"] = "Sandboxes" . $company_name;
    $t["header_buttons"] = $header_buttons;
    $t["main_content"] = $oLister->Render();

    // main

    render("cs_frame.tpl.inc", $t);
