<?php
/**
 *    FILE:            fix_stuck_consolidation.phtml
 *    AUTHOR:          Girish D <<EMAIL>>
 *    DESCRIPTION:     A Tool to fix consolidation stuck in progress
 *
 *    (C) 2008, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class fixConsolidation
{
    /** @var string $cny */
    private $cny = '';

    /**
     * @param string $_cny
     * @param string $_id            book name or structure name
     * @param string $_cnySearchType whether cny# or title
     * @param string $_csnType       whether it is book consolidation or structure
     * @param array  $csnResults
     *
     * @return bool
     */
    public function validateAndSetCSNStatus($_cny, $_id, $_cnySearchType, $_csnType, &$csnResults = [])
    {
        // Set schema info
        if ($_cnySearchType == 'CNY#') {
            $_cny = SetDBSchema($_cny, 'cny');
        } else {
            $_cny = SetDBSchema($_cny);
        }

        // Login as Admin user into given company
        Globals::$g->_userid = "1@$_cny";

        if ($_cny == '') {
            $csnResults[] = 'Company not found';
            return false;
        }
        MyConnect();
        $this->cny = $_cny;

        // Check company
        $cnyqry = array("SELECT title FROM company WHERE record# = :1", $this->cny);
        $res = QueryResult($cnyqry);
        if ($res[0]['TITLE'] == '') {
            $csnResults[] = 'Company not found';
            return false;
        }

        $ok = true;
        if ($_csnType == 'BOOK') {
            $ok = $ok && $this->validateBookAndUpdateCSNStatus($_id, $csnResults);
        } elseif ($_csnType == 'STRUCTURE') {
            $ok = $ok && $this->validateStructureAndUpdateCSNStatus($_id, $csnResults);
        }
        return $ok;
    }

    /**
     * @param string $_id book name or structure name
     * @param int    $getRowCount
     *
     * @return bool
     */
    private function setConsolidationStatus($_id, &$getRowCount)
    {
        $consolidationHandler = new ConsolidationHandler($_id);
        $bookLockName = $consolidationHandler->getBookLockNameForTier($_id);
        $lock = new Lock();
        $isLockExists = $lock->initUsingExistingLock($bookLockName);
        // Release lock only if it exists
        return $consolidationHandler->setCSNStatusAndReleaseLock($lock, $_id, $isLockExists, $getRowCount);
    }

    /**
     * @param string $bookId
     * @param array  $csnResults
     * @param string $type
     *
     * @return bool
     */
    private function validateBookAndUpdateCSNStatus($bookId, &$csnResults, $type = 'C')
    {
        $bookQry = ["SELECT 1 FROM glbook WHERE cny# = :1 and bookid = :2 and type = :3", $this->cny, $bookId, $type];
        $book = QueryResult($bookQry);

        if (empty($book)) {
            $csnResults[] = 'Invalid consolidation book.';
            return false;
        }

        $ok = $this->setConsolidationStatus($bookId, $getRowCount);
        if (!$ok) {
            $csnResults[] = "Failed to update consolidation history of book "
                . isl_htmlspecialchars($bookId) . " to failed";
        } else {
            $csnResults[] = "Successfully updated " . isl_htmlspecialchars($getRowCount)
                . " consolidation history of book : " . isl_htmlspecialchars($bookId)
                . " to failed and released book lock.";
        }
        return $ok;
    }

    /**
     * @param string $structureName
     * @param array  $csnResults
     *
     * @return bool
     */
    private function validateStructureAndUpdateCSNStatus($structureName, &$csnResults)
    {
        // Validate the ownership structure
        // 1. Get all the books in given structure where status is not success
        // 2. Update the consolidation history of all the books to failed having status as In progress or multi run etc
        // 3. Release book level lock if exists
        // 4. Release structure lock if exists
        // call method validateBookAndUpdateCSNStatus for each book

        $ok = true;
        // Validate the ownership structure
        $gManagerFactory = Globals::$g->gManagerFactory;
        $ownershipMgr = $gManagerFactory->getManager('gcownershipstructure');
        $ownershipStructure = $ownershipMgr->GetList(
            [
                'filters' => [
                    [
                        ['STRUCTURENAME', '=', $structureName]
                    ]
                ],
            ]
        );

        if (empty($ownershipStructure)) {
            $csnResults[] = 'Invalid ownership structure.';
            return false;
        }

        $csnBooks = $this->getAllStuckBooksOfStructure($structureName);

        // 2. Update the consolidation history of all the books to failed having status as In progress or multi run etc
        // 3. Release book level lock if exists
        foreach ($csnBooks as $book) {
            $ok = $ok && $this->validateBookAndUpdateCSNStatus($book['BOOKKEY'], $csnResults, 'PC');
        }

        $ok = $ok && $this->releaseStructureLock($structureName, $csnResults);
        return $ok;
    }

    /**
     * @param string $structureName
     * @param array  $csnResults
     *
     * @return bool
     */
    private function releaseStructureLock($structureName, &$csnResults)
    {
        $tierConsolidationHandler = new TierConsolidationHandler($structureName);
        // First release structure lock
        // This method might error out if the structure lock doesn't exit but it's okay as the process won't be stopped
        $ok = $tierConsolidationHandler->releaseStructureLock();
        if ($ok) {
            $csnResults[] = "Successfully released Structure lock for structure : " . isl_htmlspecialchars(
                    $structureName
                );
        } else {
            $csnResults[] = $tierConsolidationHandler->_Err->__toString();
        }
        return $ok;
    }

    /**
     * @param string $structureId
     *
     * @return array[]
     */
    private function getAllStuckBooksOfStructure(string $structureId)
    {
        // 1. Get all the books in given structure where status is not success
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gcTierConsolidationMgr = $gManagerFactory->getManager('gctierconsolidation');
        $filter = [
            'selects' => [
                [
                    'fields' => ['BOOKKEY'],
                    'function' => 'DISTINCT(${1})'
                ]
            ],
            'filters' => [
                [
                    ['STRUCTURENAME', '=', $structureId],
                    [
                        'CSNSTATUS', 'NOT IN', [
                        AbstractConsolidationHandler::CSN_SUCCESS,
                        AbstractConsolidationHandler::CSN_FAIL
                    ]
                    ]
                ]
            ]
        ];

        $csnHistoryBooks = $gcTierConsolidationMgr->GetList($filter);
        return $csnHistoryBooks;
    }
}