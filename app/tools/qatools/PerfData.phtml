<?php	// extract and summarize the perf.log
//
//================================================================================
//	FILE:             PerfData.phtml
//	AUTHOR:           eroth
//	DESCRIPTION:      extract and summarize the perf.log
//	MODIFIED DATE:    08/30/2016
//
//	(C)2016, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information
//	herein may not be used, copied or disclosed in whole or part
//	without prior written consent from Intacct Corporation.
//================================================================================
//

/**
 * @param string $cny
 * @param string $title
 * @param string $starttime
 * @param string $stoptime
 * @param string $isapi
 * @return array
 */
function CollectSummaryResults($cny, $title, $starttime, $stoptime, $isapi)
{
	$title = str_replace(' ','+',trim($title));

	if ($isapi != '1') {
		$logFile = '/tmp/perf.log';
	}
	else {
		$logFile = '/tmp/perf_gw.log';
	}
	
	$handle = fopen($logFile, 'r');
	if ($handle == FALSE) {
		echo "cannot open $logFile";
		$err = error_get_last();
		print_r($err);
		return array();
	}
	
	if ($starttime == '') {
		// start at the beginning
		fseek($handle, 0, SEEK_SET);
	}
	else {
		fseek($handle, 0, SEEK_END);
		
		// lets go back in 5k chunks until we are just before the start time
		// echo "back\r\n";
		do {
			$dataChunk = fseek($handle, 1024 * -5, SEEK_CUR);
			
			fgets($handle);	// we may have a partial line, so read again the next full one
			$dataLine = fgets($handle);
			$dataArray = parseDataLine($dataLine, $isapi);
			if (-1 == compareDates($dataArray['date'], $starttime)) {
				break;
			}
		} while ($dataChunk != -1);
	
		if (feof($handle)) {
			echo "Cannot find start date.";
			fclose($handle);
			return array();
		}
	}
	// echo "forward\r\n";
	$summary = array();
	$lowerTitle = strtolower($title);
	while (!feof($handle))
	{
		$dataLine = fgets($handle);
		
		// check if the raw line contains the title or the cny#. Only then parse
		if ((strpos(strtolower($dataLine), $lowerTitle) === false) && (strpos($dataLine,$cny) === false)) {
			continue;
		}
		
		// Ignore the prepost_xmlgw
		if (($isapi == '1') && strpos($dataLine, 'PREPOST_XMLGW')) {
			continue;
		}
		
		$dataArray = parseDataLine($dataLine, $isapi);
		if ($dataArray['date'] == '') {
			continue;
		}
		if (-1 == compareDates($dataArray['date'], $starttime)) {
			continue;
		}
		if (1 == compareDates($dataArray['date'], $stoptime)) {
			break;
		}
		if (isset($cny) && ($cny != '')) {
			if ($dataArray['cny#'] == $cny) {
				$summary[] = $dataArray;
			}
		}
		else if (isset($title) && ($title != '')) {
			if (strtolower($dataArray['title']) == $lowerTitle) {
				$summary[] = $dataArray;
			}
		}
	}
	
	// we have all the data, so lets close the file
	fclose($handle);
	return $summary;
}

/**
 * @param string $fileName
 * @return array
 */
function ReadAllLines($fileName) {
	$data = array();
	$handle = fopen($fileName, "r");
	if ($handle !== false) {
		// Move to the end, then seek back 6k. So we dont have to read the file where the data is most likeley at end.
		fseek($handle, 0, SEEK_END);
		fseek($handle, 1024 * -6, SEEK_CUR);
		fgets($handle);
	
		while (!feof($handle)) {
			$row = fgets($handle);
			$data[] = $row;
		}
		fclose($handle);
	}
	return $data;
}

/**
 * @param string $serialnbr
 * @return int
 */
function GetCSPerf($serialnbr) {
	global $csPerfData;

	$csperflogfile = '/tmp/csperf.log';
	if (count($csPerfData) == 0) {		
		$csPerfRaw = ReadAllLines($csperflogfile);
		foreach ($csPerfRaw as $csPerfLine) {
			$data = explode(" ~ ", $csPerfLine);
			$csPerfData[$data[0]] = $data[1];
		}
	}
	$out = $csPerfData[$serialnbr];
	if (!isset($out)) {
		$out = 0;
	}
	return $out;
}

/**
 * @param string $dataLine
 * @param string $isapi
 * @return array
 */
function parseDataLine($dataLine, $isapi) {
	if ($isapi == '1') {
		return parseDataLineAPI($dataLine);
	}
	else {
		return parseDataLineUI($dataLine);
	}
}

/**
 * @param string $dataLine
 * @return array
 */
function parseDataLineUI($dataLine) {

    $dataLine = str_replace("\t", " ", $dataLine);
	$data = explode(" ", $dataLine);
	$outData = array(
			'pid' 		=> $data[1],
			'ticks'	=> $data[2],
			'user cpu time' => $data[3],
			'system cpu time' => $data[4],
			'userid' 	=> $data[5],
			'date' 		=> $data[6] . ' ' . $data[7],
			'client ip' => $data[8],
			'sql count' 	=> $data[9],
			'sql time'	=> $data[10],
			'op' => $data[11],
			'bytes in'	=> $data[14],
			'bytes out'	=> $data[15],
			'scriptname' => $data[16],
			'start date' => $data[17] . ' ' . $data[18],
			'cny#' 		=> $data[20],
			'serialnbr' => $data[21],
			'title'		=> $data[28],
			'acct.log count'	=> $data[30],
			'transaction xml count'		=> $data[31],
			'salesforce count'		=> $data[32],
			'salesforce time'		=> $data[33],
			'tomcat count'		=> $data[34],
			'tomcat time'		=> $data[35],
			'external count'		=> $data[36],
			'external time'		=> $data[37],
			'imsq count'		=> $data[38],
			'imsq time'		=> $data[39],
			'xslt count'		=> $data[40],
			'xslt time'		=> $data[41],
			'pt formula count'		=> $data[42],
			'pt formula time'		=> $data[43],
			'pt cache count'		=> $data[44],
			'pt cache time'		=> $data[45],
			'pt sql count'		=> $data[46],
			'pt sql time'		=> $data[47],
			'pt trigger count'		=> $data[48],
			'pt trigger time'		=> $data[49],
			'mod security time 1'		=> $data[50],
			'mod security time 2'		=> $data[51],
			'mod security time 3'		=> $data[52],
			'schemalet db count'		=> $data[53],
			'schemalet db time'		=> $data[54],
			'global db count'		=> $data[55],
			'global db time'		=> $data[56],
			'ims db count'		=> $data[57],
			'ims db time'		=> $data[58],
			'memcache read count' => $data[59],
			'memcache write count' => $data[60],
			'csperf' => 0,
			'module' => $data[22],
			'object' => $data[23],
			'action' => $data[24],
		);
	return $outData;
}

/**
 * @param string $dataLine
 * @return array
 */
function parseDataLineAPI($dataLine) {
	$dataLine = str_replace("\t", " ", $dataLine);
	$data = explode(" ", $dataLine);
	$outData = array(
			'pid' 		=> $data[1],
			'ticks'	=> $data[2],
			'user cpu time' => $data[3],
			'system cpu time' => $data[4],
			'userid' 	=> $data[5],
			'client ip' => $data[8],
			'sql count' 	=> $data[9],
			'sql time'	=> $data[10],
			'cny#' 		=> $data[17],
			'serialnbr' => $data[18],
			'partnerid' => $data[19],
			'dbschemalet' => $data[20],
			'title'		=> $data[21],
			'acct.log count'	=> $data[23],
			'transaction xml count'		=> $data[24],
			'salesforce count'		=> $data[25],
			'salesforce time'		=> $data[26],
			'tomcat count'		=> $data[27],
			'tomcat time'		=> $data[28],
			'external count'		=> $data[29],
			'external time'		=> $data[30],
			'imsq count'		=> $data[31],
			'imsq time'		=> $data[32],
			'xslt count'		=> $data[33],
			'xslt time'		=> $data[34],
			'pt formula count'		=> $data[35],
			'pt formula time'		=> $data[36],
			'pt cache count'		=> $data[37],
			'pt cache time'		=> $data[38],
			'pt sql count'		=> $data[39],
			'pt sql time'		=> $data[40],
			'pt trigger count'		=> $data[41],
			'pt trigger time'		=> $data[42],
			'schemalet db count'		=> $data[43],
			'schemalet db time'		=> $data[43],
			'global db count'		=> $data[44],
			'global db time'		=> $data[46],
			'ims db count'		=> $data[47],
			'ims db time'		=> $data[48],
			'dtdversion'		=> $data[49],
			'function'		=> $data[50],
			'object'		=> $data[51],
			'memcache read count' => $data[52],
			'memcache write count' => $data[53],
			'throttle wait time' => $data[54],
			'scriptname' => $data[13],
			'date' 		=> $data[6] . ' ' . $data[7],
			'start time' => $data[14] . ' ' . $data[15]
		);
	return $outData;
}

/**
 * @param string $date1
 * @param string $date2
 * @return int
 */
function compareDates($date1, $date2) {
	$time1 = strtotime($date1);
	$time2 = strtotime($date2);
// echo "Compare $date1 to $date2\r\n";
	if ($time1 < $time2) {
		 return -1;
	}
	if ($time1 > $time2) {
		return 1;
	}
	return 0;
}

/**
 * @param array $rawData
 * @param array $summaryFieldList
 * @param array $noSummaryList
 * @return array
 */
function summarizeData(&$rawData, $summaryFieldList, $noSummaryList) {
	$summaryData = array('requests' => 0,'csperf' => 0);
	foreach ($summaryFieldList as $fld) {
		if (!in_array($fld, $noSummaryList)) {
			$summaryData[$fld] = 0.0;
		}
	}
	foreach ($rawData as &$rawLine) {
		$summaryData['requests'] += 1;
		$rawLine['csperf'] = GetCSPerf($rawLine['serialnbr']);
		foreach ($summaryFieldList as $fld) {
			if (!in_array($fld, $noSummaryList)) {
				$summaryData[$fld] += $rawLine[$fld];
			}
		}
	}
	return $summaryData;
}
	
/**
 * @param array $summaryData
 * @param array $summaryFieldList
 * @param array $noSummaryList
 * @param bool $bHdr
 * @return string
 */
function formatHTML($summaryData, $summaryFieldList, $noSummaryList, $bHdr) {
	// format it nice for html
	$outHtml = '';
	if ($bHdr) {
		$outHtml = '<tr>';
		foreach ($summaryFieldList as $fld) {
			if (!in_array($fld, $noSummaryList)) {
				$outHtml = $outHtml . '<th class="table-header">' . $fld . '</th>';
			}
		}
		$outHtml = $outHtml . '</tr>';
	}
	$outHtml = $outHtml . '<tr>';
	foreach ($summaryFieldList as $fld) {
		if (!in_array($fld, $noSummaryList)) {
			$outHtml = $outHtml . '<td  class="table-data">' . $summaryData[$fld] . '</td>';
		}
	}
	$outHtml = $outHtml . '</tr>';
	return $outHtml;	
}		

/**
 * @param array $summaryData
 * @param array $summaryFieldList
 * @param bool $bHdr
 * @return string
 */
function formatCSV($summaryData, $summaryFieldList, $bHdr) {
	$outCSV = '';
	if ($bHdr) {
		$bFirstCol = true;
		foreach ($summaryFieldList as $fld) {
			if (!$bFirstCol) {
				$outCSV = $outCSV . ',';
			}
			$outCSV = $outCSV . '"' . $fld . '"';
			$bFirstCol = false;
		}
		$outCSV = $outCSV . "\n";
	}

	$bFirstCol = true;
	foreach ($summaryFieldList as $fld) {
		if (!$bFirstCol) {
			$outCSV = $outCSV . ',';
		}
		$outCSV = $outCSV . '"' . $summaryData[$fld] . '"';
		$bFirstCol = false;
	}
	$outCSV = $outCSV . "\n";
	return $outCSV;	
}

/**
 * Borrowed from product's register_globals code. Thanks, Rich!
 */
function RegisterTheGlobals() {
   foreach ( $_FILES as $file_key => $file_attr ) {
        $GLOBALS[$file_key . '_name'] = $file_attr['name'];
        $GLOBALS[$file_key . '_type'] = $file_attr['type'];
        $GLOBALS[$file_key . '_size'] = $file_attr['size'];
        $GLOBALS[$file_key] = $file_attr['tmp_name'];
   }
	
   $variablesOrder = ini_get("variables_order");
	 $aEPGCS = array ( 'E' => $_ENV, 
                      'P' => $_POST,
                      'G' => $_GET, 
                      'C' => $_COOKIE,
                      'S' => $_SERVER );

    // Loop over each super global type based on the "variables_order" directive
    for ( $i=0; $i<strlen($variablesOrder); $i++ ) { // DELIBERATE use of non-isl. 
        $aSuperGlobal = $aEPGCS[$variablesOrder[$i]];
        foreach ( $aSuperGlobal as $key => $value ) {
            // Assign the value into $GLOBALS
            $GLOBALS[$key] = $value;
        }
    }
  }

$csPerfData = array();
$ignoreFieldList = array('pid','userid','client ip','op','cny#','title','serialnbr','dbschemalet','partnerid');
$noSummaryList = array('scriptname','module','object','action','function','dtdversion','start date','date');
RegisterTheGlobals();
$_isapi = $_REQUEST['_isapi'];
$_starttime = $_REQUEST['_starttime'];
$_title = $_REQUEST['_title'];
$_cny = $_REQUEST['_cny'];
$htmlStartTime = htmlentities(htmlspecialchars($_starttime, ENT_QUOTES));
$htmlTitle = htmlentities(htmlspecialchars($_title, ENT_QUOTES));
$htmlCny = htmlentities(htmlspecialchars($_cny, ENT_QUOTES));

if ((!isset($_cny) || ($_cny == '')) && (!isset($_title) || ($_title == ''))) {
	if (isset($_start) || isset($_stop) || isset($_download)) {
		$collectMsg = "Company CNY# or ID is required!";
	}
}
else {
	if (isset($_start)) {
		$collectMsg = "Collecting..";
		$_starttime = date("Y-m-d H:i:s");
	}
	else if (isset($_stop) || isset($_download)) {
		$stoptime = date("Y-m-d H:i:s");
		if ($_starttime == '') {
			$collectMsg = "Summary Results between 'Beginning' and '$stoptime' ";
		}
		else {
			$collectMsg = "Summary Results between '$htmlStartTime' and '$stoptime' ";
		}
			
		if ($_title != '') {
			$collectMsg = $collectMsg . "for company: '$htmlTitle'.";
		}
		else {
			$collectMsg = $collectMsg . "for company CNY#: $htmlCny.";
		}
		
		$rawData = CollectSummaryResults($_cny, $_title, $_starttime, $stoptime, $_isapi);
		$summaryFieldList = array_diff(array_keys($rawData[0]),$ignoreFieldList);
		$summaryData = summarizeData($rawData, $summaryFieldList, $noSummaryList);
		$outputData = formatHTML($summaryData, $summaryFieldList, $noSummaryList, true);

		$bHdr = true;
		$outputDetail = '<br/>Detail Information<br/><table>';
		foreach ($rawData as $outRow) {
			$outputDetail = $outputDetail . formatHTML($outRow, $summaryFieldList, array(), $bHdr);
			$bHdr = false;
		}
		$outputDetail = $outputDetail . '</table>';
		$isCollectedData = true;
		
		if (isset($_download)) {
			$bHdr = true;
			$csvDetail = '';
			foreach ($rawData as $outRow) {
				$csvDetail = $csvDetail . formatCSV($outRow, $summaryFieldList, $bHdr);
				$bHdr = false;
			}
			
			header("Content-type: application/octet-stream");
			header("Content-Disposition: attachment; filename=\"PerfLogData.csv\"");
			echo $csvDetail;
			die();
		}
	}
}

?>
<html>
<head>
	<title>Performance Summary Collection</title>
	<style>
		body{
	background-color: #7CAFCB;
	text-align: center;
}
.caption2{
	background: transparent;
	font: bold 13px sans-serif;
	margin: 2px;
	padding: 3px;
}
.caption3{
	background: transparent;
	display: block;
	font: bold 29px Arial,Helvetica,sans-serif;
	margin: 12px 0;
	padding: 5px 3px;
}
.required-field {
	color: red;
	font: bold 12px sans-serif;
}
.table-data{
	background: #E5F3FF;
	font: 12px sans-serif;
	padding: 4px;
	text-align: right;
}

.table-header{
	background: #53A8FD;
	color: #FFFFF0;
	font: italic small-caps 11px sans-serif;
	padding: 4px;
	text-align: center;
}
}
	</style>
</head>
<form id="configform" action="PerfData.phtml" method="POST">
	<div id="configform-container">
		<span class="caption3">Performance Summary Information</span>
		<div class="config-display-line">
			<span class="caption"><span class="required-field">*Company CNY#</span></span>
			<span class="element">
				<input type="text" id="cny" name=".cny" value="<? echo $htmlCny; ?>" size="12"/>
			</span><b>OR</b>
			<span class="caption"><span class="required-field">*Title</span></span>
			<span class="element">
				<input type="text" id="title" name=".title" value="<? echo $htmlTitle; ?>" size="25"/>
			</span>
			<span class="caption">API Test</span>
			<span class="element">
				<input type="checkbox" id="isapi" name=".isapi" value="1" <? echo ($_isapi == '1') ? 'checked' : ''; ?>/>
			</span>
		</div>
	</div>
	<br/>
	<div id="configform-container">
		<div class="config-display-line">
			<span class="element2">
				Collection Start Time:&nbsp;<input type="text" name=".starttime" value="<? echo $htmlStartTime; ?>"/>
				<input type="submit" name=".start" value="Start Collection"/>
				<input type="submit" name=".stop" value="Stop and Show Log"/>
				<input type="submit" name=".download" value="Stop and Download Log"/>
			</span>
		</div>
	</div>
	<br/>
	<div id="configform-container">
		<div class="config-display-line">
			<span class="caption"><? echo $collectMsg; ?></span>
			<br/>
			<table>
				<? if ($isCollectedData) { echo $outputData; } ?>
			</table>
			<? if ($isCollectedData) { echo $outputDetail; } ?>
		</div>
	</div>
</form>
</html>
