<?php
    //=============================================================================
    //
    //	FILE:        company_builder_intro.tpl.inc
    //	AUTHOR:      <PERSON><PERSON> <<EMAIL>>
    //	DESCRIPTION: intro for Company Builder Tool
    //
    //	(C)2023, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

?>
<div class="page-row">
    <div class="page-col2">

        <div class="box">
            <div class="form-row">
                <p class="row-text">Create a net new instance of Intacct. Use this in the same way as the deprecated Create Company Wizard.</p>
                <a href="company_builder.phtml?page=create" class="button-link" onclick="showLoader();">Create company</a>
            </div>

            <div class="form-row">
                <p class="row-text">Create a new instance of Intacct by copying another. Use this in the same way as the deprecated Copy tenant tool.</p>
                <a href="company_builder.phtml?page=copy" class="button-link" onclick="showLoader();">Copy company</a>
            </div>

            <div class="form-row">
                <p class="row-text">Create a net new instance of Intacct. Use this in the same way as the deprecated Create Console.</p>
                <a href="company_builder.phtml?page=console" class="button-link" onclick="showLoader();">Create console</a>
            </div>

            <div class="form-row">
                <p class="row-text">Check provisioning status.</p>
                <a href="company_builder.phtml?page=list" class="button-link" onclick="showLoader();">Provisioning status</a>
            </div>

        </div>

    </div>
</div>
