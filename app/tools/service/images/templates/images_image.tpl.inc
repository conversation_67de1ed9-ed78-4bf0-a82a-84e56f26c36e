<?php
    //=============================================================================
    //
    //	FILE:        images_image.tpl.inc
    //	AUTHOR:      <PERSON> <<EMAIL>>
    //	DESCRIPTION: add/edit image template
    //
    //	(C)2022, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    // just to keep inspections happy
    $t_header_buttons = $t_header_buttons ?? array();
    $t_alerts = $t_alerts ?? array();
    $t_oEdition = $t_oEdition ?? new EditionModel();
    $t_company_title = $t_company_title ?? "";
    // end inspections
?>

<div class="alerts">
    <div id="error_group" class="alert alert-error" style="display:<?= count($t_alerts[ALERT_TYPE_ERROR]) > 0 ? "block" : "none"; ?>;">
        <span id="err_category" class="alert-message" style="display:<?= isset($t_alerts[ALERT_TYPE_ERROR]["category"]) ? "block" : "none"; ?>;">
            A value for <a href="#category">Category</a>is required. Please choose a valid category.
        </span>
        <span id="err_name" class="alert-message" style="display:<?= isset($t_alerts[ALERT_TYPE_ERROR]["name"]) ? "block" : "none"; ?>;">
            A value for <a href="#name">Name</a> is required.
        </span>
        <span id="err_name_validation" class="alert-message" style="display:<?= isset($t_alerts[ALERT_TYPE_ERROR]["name_validation"]) ? "block" : "none"; ?>;">
            <a href="#name">Name</a> is invalid. It can have max 100 chars.
        </span>
        <span id="err_description_validation" class="alert-message" style="display:<?= isset($t_alerts[ALERT_TYPE_ERROR]["description_validation"]) ? "block" : "none"; ?>;">
            <a href="#description">Description</a> is invalid. It can have max 100 chars.
        </span>
        <span id="err_vertical" class="alert-message" style="display:<?= isset($t_alerts[ALERT_TYPE_ERROR]["vertical"]) ? "block" : "none"; ?>;">
            A value for <a href="#vertical">Vertical</a> is required. Please choose a valid vertical.
        </span>
        <span id="err_subvertical" class="alert-message" style="display:<?= isset($t_alerts[ALERT_TYPE_ERROR]["subvertical"]) ? "block" : "none"; ?>;">
            A value for <a href="#subvertical">Subvertical</a> is required. Please choose a valid subvertical.
        </span>
    </div>
</div>

<div class="page-row">
    <div class="page-col2">
        <form id="frm_editions" method="post" class="box" onsubmit="return formValidation('frm_editions');">
            <div class="form-row">
                <label>Category
                    <span class="required">*</span>
                </label>
                <div class="radio-btn-group">
                    <? foreach (EditionModel::CATEGORIES as $category_key => $category) { ?>
                    <label class="radio">
                        <input name="category" type="radio" value="<?=$category_key;?>" <?=$t_oEdition->category === $category_key ? " checked" : "";?> class="radio-input" onchange="toggleRequiredItems('add');"/>
                        <div class="radio-circle"></div>
                        <?=$category["label"];?>
                    </label>
                    <? } ?>
                </div>
            </div>

            <div class="form-row">
                <label>Company title</label>
                <div class="form-row-data-text"><?=$t_company_title;?></div>
            </div>

            <div class="form-row">
                <label for="name">Image name
                    <span class="required">*</span>
                </label>
                <input id="name" name="name" type="text" maxlength="100" class="form-input<?=isset($t_alerts[ALERT_TYPE_ERROR]["name"]) ? " input-error" : ""; ?>" value="<?= $t_oEdition->name;?>">
            </div>

            <div class="form-row">
                <label for="vertical">Vertical
                    <span id="required_vertical" class="required" style="display:<?=EditionModel::CATEGORIES[$t_oEdition->category]["config"]["vertical"] ? " " : " none";?>">*</span>
                </label>
                <div class="select-group" id="select_vertical">
                    <button type="button" class="select-btn" id="vertical_button" onclick="showDropdown(event, 'vertical_selector');" <?=EditionModel::CATEGORIES[$t_oEdition->category]["config"]["vertical"] ? " " : " disabled";?>>
                        <span id="vertical_selector_label" class="select-label"><?= EditionModel::VERTICAL_TYPES[$t_oEdition->vertical] ?? ""; ?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="vertical_selector" class="select-dropdown">
                        <? foreach (EditionModel::VERTICAL_TYPES as $verticalKey => $verticalType) { ?>
                        <input id="vertical_<?= $verticalKey; ?>" name="vertical" type="radio" value="<?= $verticalKey; ?>" <?= $t_oEdition->vertical === $verticalKey ? " checked" : ""; ?> class="select-option" onclick="setSelector('vertical_selector_label', '<?= $verticalType; ?>'); changeOption('<?= $verticalKey; ?>', 'subvertical', 'selector_option_generic')"/>
                        <label for="vertical_<?= $verticalKey; ?>" title="<?= $verticalType; ?>" class="select-item" tabindex="-1"><?= $verticalType; ?></label>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label for="subvertical">Subvertical
                    <span id="required_subvertical" class="required" style="display:<?=EditionModel::CATEGORIES[$t_oEdition->category]["config"]["subvertical"] ? " " : " none";?>">*</span>
                </label>
                <div class="select-group" id="select_subvertical">
                    <button type="button" class="select-btn" id="subvertical_button" onclick="showDropdown(event, 'subvertical_selector');" <?=EditionModel::CATEGORIES[$t_oEdition->category]["config"]["subvertical"] ? " " : " disabled";?>>
                        <span id="subvertical_selector_label" class="select-label"><?= EditionModel::SUBVERTICAL_TYPES[$t_oEdition->vertical][$t_oEdition->subvertical] ?? ""; ?></span>
                        <span class="select-arrow-box"><span class="select-arrow"></span></span>
                    </button>
                    <div id="subvertical_selector" class="select-dropdown">
                        <? if (isset(EditionModel::SUBVERTICAL_TYPES[$t_oEdition->vertical])) { ?>
                        <? foreach (EditionModel::SUBVERTICAL_TYPES[$t_oEdition->vertical] as $subverticalKey => $subverticalType) { ?>
                        <input id="subvertical_<?= $subverticalKey; ?>" name="subvertical" type="radio" value="<?= $subverticalKey; ?>" <?= $t_oEdition->subvertical === $subverticalKey ? " checked" : ""; ?> class="select-option" onclick="setSelector('subvertical_selector_label', '<?= $subverticalType; ?>');"/>
                        <label for="subvertical_<?= $subverticalKey; ?>" title="<?= $subverticalType; ?>" class="select-item" tabindex="-1"><?= $subverticalType; ?></label>
                        <? } ?>
                        <? } ?>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label for="description">Description</label>
                <textarea id="description" name="description" maxlength="100"><?= $t_oEdition->description; ?></textarea>
            </div>

            <div class="form-buttons">
                <? foreach ($t_header_buttons as $button) { ?>
                <? if ($button["type"] === "submit") { ?>
                <input name="<?= $button["name"]; ?>" type="submit" form="<?= $button["form"]; ?>" value="<?= $button["label"]; ?>">
                <? } else if ($button["type"] === "link") { ?>
                <a href="<?= $button["url"]; ?>" class="button-link" onclick="showLoader();"><?= $button["label"]; ?></a>
                <? } ?>
                <? } ?>
            </div>
        </form>
    </div>
</div>

<template id="selector_option_generic">
    <input id="BLOCK_KEY" name="BLOCK" type="radio" value="KEY" class="select-option" onclick="setSelector('BLOCK_selector_label', 'VALUE');"/>
    <label for="BLOCK_KEY" title="VALUE" class="select-item" tabindex="-1">VALUE</label>
</template>

<script>
    cs.forms = cs.forms || {};
    cs.forms['frm_editions'] = {'required': ['name']};
    cs.data = cs.data || {};
    cs.data['categories'] = <?=json_encode(EditionModel::CATEGORIES, true);?>;

    var option_labels = <?= json_encode(EditionModel::SUBVERTICAL_TYPES, true); ?>;
</script>
