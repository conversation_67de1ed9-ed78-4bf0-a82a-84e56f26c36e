<?php
    //=============================================================================
    //
    //	FILE:        cs_user.phtml
    //	AUTHOR:      <PERSON><PERSON> <<EMAIL>>
    //	DESCRIPTION: entry point for tool to add/edit a user
    //
    //	(C)2021, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    try {
        //extract($_REQUEST, EXTR_PREFIX_ALL, "r");
        // need this, because inspections don't like extract()
        $r_page = Request::$r->page;

        // check requested page
        $pages = array("user_list", "user");
        if (in_array($r_page, $pages, true)) {
            $page = $r_page;
        } else if ($r_page === null) {
            // default page
            $page = "user_list";
        } else {
            // invalid page; 404
            include "cs_not_found.inc";

            exit;
        }

        require_once "cs_common.inc";
        require_once "cs_user_data.inc";
        require_once "backend_company.inc";

        // frame template data
        $t["tool_styles"] = array();
        $t["tool_scripts"] = array("cs_user.js");

        // load page script
        ob_start();
        require "cs_user_" . $page . ".inc";

    } catch (Exception $e) {
        ob_clean();

        $t_exception = $e;
        include_once "cs_exception.inc";
    }

    header("Content-Type: text/html; charset=utf-8");
    ob_end_flush();
