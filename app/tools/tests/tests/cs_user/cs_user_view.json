{"url": "csuser.phtml", "params": {"GET": {"page": "user", "action": "edit", "user_key": "********"}, "POST": {}}, "expected": {"http_code": 200, "content": {"alerts": {"error": []}, "action": "edit", "page_name": "Edit user (ovi2)", "oCSUser": {"record": ********, "username": "ovi2", "password": "AaZxEg/2iga8w", "realname": "", "acclevel": "", "string": "", "podwheremodified": 500, "whenmodified": "10/11/2021", "token": "", "tokenexpirytime": null, "email": "<EMAIL>"}, "user_roles": [{"RECORD#": "***************", "USERKEY": "********", "ROLEKEY": "2", "NAME": "Account Manager"}, {"RECORD#": "***************", "USERKEY": "********", "ROLEKEY": "1", "NAME": "Accounting"}, {"RECORD#": "***************", "USERKEY": "********", "ROLEKEY": "3", "NAME": "Consultant"}], "has_admin_rights": false}}}