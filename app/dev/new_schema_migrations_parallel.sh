#!/bin/bash
# Usage: new_schema_migrations_in_parallel.sh -h
# Purpose:
#        Use this script to apply migrations in parallel to all schemas following the new dev process & tools
#

# Example: ./new_schema_migrations_parallel.sh -T aug20 -d /chroot/apache/home/<USER>/p10/db -f migrate.xxx.mega_views -u DBUSR -Y -P -l IA_MIG_PROD

. /usr/local/intacct/etc/ia_servers

# set this to 0 for DBA mode and 1 for DEV mode
DEV_MODE=0

if [[ $DEV_MODE -eq 1 ]]; then
    # for dev/testing
    MIGRBASEDIR="$HOME/migrations"
else
    # for DBAs
    MIGRBASEDIR="/u02/db_migrations"
fi

LOGDIR="$MIGRBASEDIR/logs"
MIGDIR="$MIGRBASEDIR/migrations"
EXTRAMIGRLIST="/tmp/miglist"

# for dev/testing
if [[ $DEV_MODE -eq 1 ]]; then
    # overwrite the location of miglit file for dev
    EXTRAMIGRLIST="$MIGRBASEDIR/miglist"
fi

MIGGER="/usr/local/intacct/bin/iaTools/migger.sql"
script_name=$(basename $0)
SCRIPTDIR=`dirname $0`
SCRIPTDIR=`readlink -f $SCRIPTDIR`

if [[ -f "/u02/home/<USER>/opt/flyway/flyway" ]]
  then
    FLYWAYDIR="/u02/home/<USER>/opt/flyway"
  else
    FLYWAYDIR="/opt/flyway-ia/flyway"
fi


# production Pod 1:
#    the params -d & -P are new, the rest is as the old script

# CASE 1: run only "maintenance" migrations in quarterly release night:
#        ./new_schema_migrations_parallel.sh -d /chroot/apache/home/<USER>/p10/db -f migrate.xxx.mega_views -u DBUSR -Y -l IA_MIG_PROD
#
# CASE 2: run POST migrations in quarterly release night:
#        ./new_schema_migrations_parallel.sh -d /chroot/apache/home/<USER>/p10/db -f migrate.xxx.mega_views -u DBUSR -Y -P -l IA_MIG_PROD
#
# CASE 3: run OCR migrations (also used for DEV migrations):
#        ./new_schema_migrations_parallel.sh -d /chroot/apache/home/<USER>/p10/db -f migrate.xxx.mega_views -u DBUSR -Y -P -l IA_MIG_PROD
# (for case 3 we use the same command as for case 2)

usage="Usage:
	$script_name -T tag -d MIGDIR -f MIGFILE[,MIGFILE,...] -u USERNAME -Y -l LIST [-P] [-V] [-R] [-I] [-M] [-t] [-a] [-A]
	$script_name -T tag -d MIGDIR -f MIGFILE[,MIGFILE,...] -u USERNAME -N -l LIST [-P] [-V] [-R] [-I] [-M] [-t] [-a] [-A]
	$script_name -h
	 -T tag         : Tag/Label to use on the migration logs to easier identify the logs for specific migrations
	 -d MIGDIR      : Migration directory containing the flyway compatible files that needs to be applied
	 -f MIGFILE     : Migration file that needs to be applied, e.g. migrate.feb14.10
	 -u USERNAME    : DB user which will run the migration, e.g. dbusr
	 -t target      : The target version up to which Flyway should consider migrations, e.g. Aug22. Special values: current, latest
	 -a             : To include also archived migrations in flyway phase
	 -A             : To include only archived migrations in flyway phase
	 -D             : To include DDL migrations only in flyway phase
	 -M             : To include DML migrations only in flyway phase
	 -Y             : For full run (flyway + files + process_whole_schema)
	 -N             : For dry run of process_whole_schema; will run migrate for flyway
	 -V             : For running validate command in flyway, cannot be used with -Y, -N, -R, -I
	 -R             : For running repair command in flyway, cannot be used with -Y, -N, -V, -I
	 -I             : For running info command in flyway, cannot be used with -Y, -N, -V, -R
	 -P             : To include 'post' migrations in flyway phase; used only in combination with -d option
	 -m             : (DEV ONLY) Generate and apply mega views after FLYWAY section. It requires -d option
	 -l LIST        : List of schemas. LIST=LIST={IA_MIG_PROD|IA_MIG_DR|IA_MIG_SPECIAL_PROD|IA_MIG_SPECIAL_DR|IA_MIG_DEBUG|IA_MIG_EXTRA|IA_MIG_POD[0-9]{1,}|IA_MIG_DR_POD[0-9]{1,}|IA_MIG_SPECIAL_POD[0-9]{1,}|IA_MIG_SPECIAL_DR_POD[0-9]{1,}}
	 -h             : Print the usage message

Notes: Make sure "logs" & "migrations" directories exist under $MIGRBASEDIR. Copy the migration file under $MIGDIR. Log files for each schema will be created under $LOGDIR. This script calls another script named migger.sql. Make sure migger.sql exists at $MIGGER.If you use IA_MIG_EXTRA make sure to have $EXTRAMIGRLIST file with the schemas.
"

#
# Apply post migration.
#
apply_post_migration_on_schema() {
  local SCHEMA=$1;
  local SERVER=$2;
  local LOG_FILE="$LOGDIR/$SERVER.$SCHEMA.$FILETAG.$TAG.log"

  echo "Starting files migration on schema $SCHEMA:$SERVER at $NOW"

  echo -e "\n\nSTARTING FILE MIGRATION\n\n" >> $LOG_FILE
  echo "SCHEMA='$SCHEMA' SERVER='$SERVER' LOG_FILE='$LOG_FILE'";

  echo $PASSWORD | /usr/bin/nohup sqlplus $USERNAME@$SERVER @$MIGGER $SCHEMA $tmpfil $GO_AHEAD_AND_RUN >> $LOG_FILE 2>/dev/null &

}

# schemas for which post migration finished with success
postMigratedSchemas=()
# number of migrations ended with no ORA errors
noOfSuccessfulMig=0
# list of schemas for which information was written in the error log file
schemasWithErrorLogged=()

#
# Search for ORA Errors/Warnings.
#
check_ora_messages()
{
  local SCHEMA=$1;
  local SERVER=$2;
  local logFile=`echo "$SERVER.$SCHEMA.$FILETAG.$TAG.log"`

  echo "SCHEMA  --- Error Count"
  COUNT_SP2_ERRORS=`grep -i 'SP2-' $LOGDIR/$SERVER.$SCHEMA.$FILETAG.$TAG.log | wc -l`
  if [[ $COUNT_SP2_ERRORS>0 ]]
  then
    echo "$SCHEMA : ERROR    SERVER : $SERVER    LOG_FILE : $logFile    MESSAGE : SP2 ERROR - Fix errors/warnings and reapply the migration" >> $LOGDIR/migration_error_summary.$TAG.log
  else
    COUNT=`grep -i 'ORA-\|Warning:' $LOGDIR/$SERVER.$SCHEMA.$FILETAG.$TAG.log | grep -v outOfOrder | grep -v 'with compilation errors' | grep -v 'execution completed with warning' | grep -v 'Skipping filesystem location' | grep -v 'newer than the latest available migration' | grep -v 'Flyway upgrade recommended' | wc -l`
    if [[ $COUNT>0 ]]
    then
      echo -e "$SCHEMA\t : ERROR\t: Fix errors/warnings in $LOGDIR/$SERVER.$SCHEMA.$FILETAG.$TAG.log and reapply the migration"

      echo "$SCHEMA : ERROR    SERVER : $SERVER    LOG_FILE : $logFile    MESSAGE : ORA ERROR - Fix errors/warnings and reapply the migration" >> $LOGDIR/migration_error_summary.$TAG.log
    else
      echo -e "$SCHEMA\t : SUCCESS\t: `grep 'Invalid object count' $LOGDIR/$SERVER.$SCHEMA.$FILETAG.$TAG.log` There are no ORA errors"

      noOfSuccessfulMig=$(($noOfSuccessfulMig + 1))
      echo "$SCHEMA : SUCCESS    SERVER : $SERVER    MESSAGE : ORA SUCCESS - `grep 'Invalid object count' $LOGDIR/$SERVER.$SCHEMA.$FILETAG.$TAG.log` There are no ORA errors " >> $LOGDIR/migration_success_summary.$TAG.log
    fi
  fi
}

#
# Check if post migration was done.
#
verify_post_migration() {
  local SCHEMA=$1;
  local SERVER=$2;
  local logFile="$LOGDIR/$SERVER.$SCHEMA.$FILETAG.$TAG.log"

  # verify if post migration finished fo this schema
  # read content of the file and check if it ends with a message containing "Disconnected from Oracle Database" string
  local message=`cat $logFile | grep 'Disconnected from Oracle Database'`
  if [[ ! -z $message ]]
  then
    # file migration finished for this schema

    # add the schema in postMigratedSchemas
    postMigratedSchemas+=($SCHEMA:$SERVER)

    # print success message
    echo "####################################################"
    echo "Migration completed on schema $SCHEMA:$SERVER. Please check log files for errors and make sure there are ZERO invalids"
    echo "####################################################"

    # search for ORA Errors/Warnings.
    check_ora_messages $SCHEMA $SERVER
  else
    # print waiting message
    echo "Waiting on migration to be applied on $SCHEMA:$SERVER"
  fi
}

# schema' logs for which flyway finished
flywaySuccessLog=()

#
# Check if flyway finished for schemas.
#
# Check also if post migration finished for schemas where flyway finished.
#
check_flyway_result_and_apply_post_migration() {
  # get all log files that have the same $TAG
  local logFiles=`ls $LOGDIR | grep $TAG`

  for logFile in $logFiles
  do
    if [[ ! $logFile =~ "migration" ]]
    then
      local schema=`echo $logFile | cut -d "." -f2`
      local server=`echo $logFile | cut -d "." -f1`

      # check if action was already taken for the file in order to avoid repeated actions for the same schema
      if [[ ! " ${flywaySuccessLog[*]} " =~ " ${logFile} " ]]
      then
        # action was not taken for this schema

        # process the log
        # read content of the file and check if it ends with a message containing "Successfully applied" or "Schema .* is up to date. No migration necessary." string
        local successMessage=`cat $LOGDIR/$logFile | grep 'Flyway finished successfully'`
        if [[ ! -z $successMessage ]]
        then
          # flyway finished with success for this schema
          echo '####################################################'
          echo "Flyway migration completed on schema $schema:$server. Please check log files for errors"
          echo '####################################################'

          # mark the file as processed with success
          flywaySuccessLog+=($logFile)

          if [[ "$STOP_AFTER_FLYWAY" != "Y" ]]
          then
            # apply file migration for this schema
            apply_post_migration_on_schema $schema $server &
          fi
        else
          # check if flyway finished with error
          local errorMessage=`cat $LOGDIR/$logFile | grep '^ERROR'`
          if [[ ! -z $errorMessage ]]
          then
            # check if the message was already written in the log file
            if [[ ! " ${schemasWithErrorLogged[*]} " =~ " ${schema}:${server} " ]]
            then
              # the message wasn't written yet
              # add the information about the schema in the file
              local file=`echo "$server.$schema.$FILETAG.$TAG.log"`
              echo "$schema : ERROR    SERVER : $server    LOG_FILE : $file    MESSAGE : Flyway ERROR - Fix errors and reapply the migration" >> $LOGDIR/migration_error_summary.$TAG.log

              # add the schema in schemasWithErrorLogged
              schemasWithErrorLogged+=($schema:$server)
            fi
          fi
        fi
      else
        # flyway finished for this schema
        # we can check the next step - check if post migration already finished for this schema
        if [[ ! " ${postMigratedSchemas[*]} " =~ " ${schema}:${server} " ]]
        then
          # check if post migration finished or not on this schema
          verify_post_migration $schema $server
        fi
      fi
    fi
  done
}

#
# Validate if target has the expected format, e.g. 2208.9
#
validate_target() {
  if [[ ! $targetInfo =~ ^[0-9]{4}.9$ && ! $targetInfo == "current"  && ! $targetInfo == "latest" ]]
  then
    validTargetFormat="Valid target value is formed from:
        - release    : one of the values {Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec}
        - year       : last two digits from the year
        e.g. Aug22"
    echo "Error: Target $TARGET is incorrect."; echo "$validTargetFormat";
    exit 0;
  fi
}

#
# Returns target in the form accepted by flyway
#
get_target() {
  # convert target to the for accepted by flyway
  # if target =current|latest we leave the value as it is and inform the user about the option
  if [[ "$TARGET" == "current" ]]
  then
    targetInfo="$TARGET"
    echo "Target = $TARGET: no versioned migrations will be applied but repeatable migrations will be, together with any callbacks"
  elif [[ "$TARGET" == "latest" ]]
  then
    targetInfo="$TARGET"
    echo "Target = $TARGET: applying migrations up to the latest version of the schema, as defined by the migration with the highest
    version"
  else
    # declare months to be used in release validation
    declare -A months=([Jan]="01" [Feb]="02" [Mar]="03" [Apr]="04" [May]="05" [Jun]="06" [Jul]="07" [Aug]="08" [Sep]="09" [Oct]="10" [Nov]="11" [Dec]="12")

    year=`echo "$TARGET" | cut -c4-5`
    monthInfo=`echo "$TARGET" | cut -c1-3`
    ocr=".9"
    targetInfo=$year${months[$monthInfo]}$ocr
  fi
}

#
# Check if TNS_ADMIN variable is set
#
check_tns() {
  tns_var=`printenv TNS_ADMIN`

  if [[ -z "${tns_var+x}" ]]
  then
    if [[ -f "/etc/tnsnames.ora" ]]
    then
      export TNS_ADMIN='/etc'
    else
      echo "Error: TNS_ADMIN environment variable is not set. Please set the variable value to /etc"; exit 0
    fi
  fi
}

run_flyway_migrations() {
    local curDir=$PWD
    cd $INPUT_DIR
    local configDir="db_migration/conf"
    local configFiles_ddl="$configDir/flyway.conf,$configDir/flyway.history.ddl.conf,$configDir/flyway.ddl.conf"
    local configFiles_dml="$configDir/flyway.conf,$configDir/flyway.history.dml.conf,$configDir/flyway.dml.conf"

    # check if we need to include archived migrations
    if [[ "$INCLUDE_ARCHIVE" == 1 ]]
      then
        # include archived migrations
        configFiles_ddl="$configFiles_ddl,$configDir/flyway.archive.ddl.conf"
        configFiles_dml="$configFiles_dml,$configDir/flyway.archive.dml.conf"
    fi

    # check if we need to include archived migrations only
    if [[ "$INCLUDE_ARCHIVE_ONLY" == 1 ]]
      then
        # include archived migrations
        configFiles_ddl="$configDir/flyway.conf,$configDir/flyway.history.ddl.conf,$configDir/flyway.archive.only.ddl.conf"
        configFiles_dml="$configDir/flyway.conf,$configDir/flyway.history.dml.conf,$configDir/flyway.archive.only.dml.conf"
    fi

    if [[ "$INCLUDE_POST" == "Y" ]]
      then
        configFiles_ddl="$configFiles_ddl,$configDir/flyway.post.ddl.conf"
        configFiles_dml="$configFiles_dml,$configDir/flyway.post.dml.conf"

        # check if we need to include archived migrations
        if [[ "$INCLUDE_ARCHIVE" == 1 ]]
          then
          # include archived migrations
          configFiles_ddl="$configFiles_ddl,$configDir/flyway.post.archive.ddl.conf"
          configFiles_dml="$configFiles_dml,$configDir/flyway.post.archive.dml.conf"
        fi

        # check if we need to include archived migrations only
        if [[ "$INCLUDE_ARCHIVE_ONLY" == 1 ]]
          then
          # include archived migrations
          configFiles_ddl="$configDir/flyway.conf,$configDir/flyway.history.ddl.conf,$configDir/flyway.post.archive.only.ddl.conf"
          configFiles_dml="$configDir/flyway.conf,$configDir/flyway.history.dml.conf,$configDir/flyway.post.archive.only.dml.conf"
        fi

    fi

    JAVA_ARGS="-Doracle.net.tns_admin=/etc"

    echo "Starting Flyway migration ($FLYWAY_CMD) on following schemas at $NOW"
    #local javaCMD="$FLYWAYDIR/jre/bin/java"
    # get java executable path
    javaConfigFile="$configDir/javaConfig.conf"
    javaPathConfig=`grep -n "javaPath" $javaConfigFile`
    pathConfigStartPosition=$(expr index "$javaPathConfig" "=")
    javaCMD=${javaPathConfig:$pathConfigStartPosition}
    #local javaCMD=/usr/lib/jvm/java-17-openjdk-17.0.1.0.12-2.el8_5.x86_64/bin/java

    local CP=""
    CP="./intacct.flyway-parallel.jar"
    CP="$CP:$FLYWAYDIR/lib/*:$FLYWAYDIR/lib/community/*:$FLYWAYDIR/drivers/*"
    local params="flyway.baselineOnMigrate=true\nflyway.user=${USERNAME}\nflyway.password=${PASSWORD}"

    # check if we want to apply flyway action for only a type of migration (DDL or DML)
    if [[ "$INCLUDE_DDL_ONLY" == 1 ]]
    then
      # we include only DDL migrations in flyway phase
      configFiles_dml=""
    elif [[ "$INCLUDE_DML_ONLY" == 1 ]]
    then
      # we include only DML migrations in flyway phase
      configFiles_ddl=""
    fi

    # check if target is specified
    if [[ -z "$TARGET" ]]
    then
      # target is not specified; running flyway without target
      # run flyway in background
      if [[ "$INCLUDE_DDL_ONLY" == 0 && "$INCLUDE_DML_ONLY" == 0 ]]
      then
        (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles_ddl -configFiles_dml=$configFiles_dml -logDir=$LOGDIR -logTS=$TAG -logFN=$FILETAG "-list=$LIST" $FLYWAY_CMD) &
      elif [[ "$INCLUDE_DDL_ONLY" == 1 ]]
      then
        (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles_ddl -logDir=$LOGDIR -logTS=$TAG -logFN=$FILETAG "-list=$LIST" $FLYWAY_CMD) &
      elif [[ "$INCLUDE_DML_ONLY" == 1 ]]
      then
        (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_dml=$configFiles_dml -logDir=$LOGDIR -logTS=$TAG -logFN=$FILETAG "-list=$LIST" $FLYWAY_CMD) &
      fi
    else
      # running flyway with target

      # get target in the format accepted by flyway
      # default: target=latest - applying migrations up to the latest version of the schema, as defined by the migration with the highest
      # version
      targetInfo='latest'
      get_target

      # validate target
      validate_target

      # run flyway in background
      if [[ "$INCLUDE_DDL_ONLY" == 0 && "$INCLUDE_DML_ONLY" == 0 ]]
      then
        (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles_ddl -configFiles_dml=$configFiles_dml -logDir=$LOGDIR -logTS=$TAG -logFN=$FILETAG "-list=$LIST" -target="$targetInfo" $FLYWAY_CMD) &
      elif [[ "$INCLUDE_DDL_ONLY" == 1 ]]
      then
        (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_ddl=$configFiles_ddl -logDir=$LOGDIR -logTS=$TAG -logFN=$FILETAG "-list=$LIST" -target="$targetInfo" $FLYWAY_CMD) &
      elif [[ "$INCLUDE_DML_ONLY" == 1 ]]
      then
        (echo -e $params | $javaCMD $JAVA_ARGS -cp "$CP" com.intacct.db.Main -configFiles_dml=$configFiles_dml -logDir=$LOGDIR -logTS=$TAG -logFN=$FILETAG "-list=$LIST" -target="$targetInfo" $FLYWAY_CMD) &
      fi
    fi

    local flywayPid=$!

    # check for each schema flyway result
    while [[ `ps -eo pid | grep $flywayPid` -eq $flywayPid ]]
    do
      check_flyway_result_and_apply_post_migration

      sleep 30
    done
    # check once again to be sure we handled all cases
    check_flyway_result_and_apply_post_migration

    local exitCode=$?
    cd $curDir


    if [[ $exitCode -eq 1 ]]
      then
        exit 1
    fi

    if [[ "$STOP_AFTER_FLYWAY" == "Y" ]]
      then
        echo "Stopping after FLYWAY phase."
        exit 0
    fi
    echo -e "\n"

    # put an empty line in the temporary file for the "old" process to kick off the process_whole_schema part
    echo >> "$tmpfil"
    NUM_FILES=$((NUM_FILES+1))
}

gen_mega_views() {
    local curDir=$PWD
    cd $INPUT_DIR
    echo "Refreshing mega views"
    if [[ ! -d ../link/private/cstools ]]
      then
        echo "Cannot cd to link/private/cstools. Are you specifying a sandbox "app" folder for -d option?"
        exit 1
    fi
    cd ../link/private/cstools
    /usr/local/php/bin/php ./genMegaViewsDDL.phtml >> $tmpfil
    if [ "$?" -ne 0 ]; then
      cd $curDir
      echo "Error in generating mega views"
      exit 1
    fi
    echo >> "$tmpfil"
    NUM_FILES=$((NUM_FILES+1))
    echo "Done generating mega views in $tmpfil"
    cd $curDir
}

if [[ $# -lt 9 ]]
	then
		if [[ $@ == "-h" ]]
			then
				echo "$usage" ; exit 0
		else
				echo "Error: invalid number of arguments specified: $#" ; echo "$usage" ; exit 0
		fi
fi

declare -a MIGFILES
FLYWAY_CMD=info
STOP_AFTER_FLYWAY=N
EXCLUSIVE_COUNT=0
EXCLUSIVE_LIST=""
INCLUDE_ARCHIVE=0
INCLUDE_ARCHIVE_ONLY=0
INCLUDE_DDL_ONLY=0
INCLUDE_DML_ONLY=0

while getopts ":d:f:u:T:t:aADMYNPVRmIl:" opt ; do
        case $opt in
        T)  MIGR_TAG=$OPTARG ;;
        d)  INPUT_DIR=$OPTARG ;;
        f)  IFS=, read -a MIGFILES <<< "$OPTARG" ;;
        u)  USERNAME=$OPTARG ;;
        t)  TARGET=$OPTARG ;;
        a)  INCLUDE_ARCHIVE=1;;
        A)  INCLUDE_ARCHIVE_ONLY=1;;
        D)  INCLUDE_DDL_ONLY=1;;
        M)  INCLUDE_DML_ONLY=1;;
        Y)  GO_AHEAD_AND_RUN=Y FLYWAY_CMD=migrate EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        N)  GO_AHEAD_AND_RUN=N FLYWAY_CMD=migrate EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        P)  INCLUDE_POST=Y ;;
        V)  GO_AHEAD_AND_RUN=N FLYWAY_CMD=validate STOP_AFTER_FLYWAY=Y EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        R)  GO_AHEAD_AND_RUN=N FLYWAY_CMD=repair STOP_AFTER_FLYWAY=Y EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        I)  GO_AHEAD_AND_RUN=N FLYWAY_CMD=info STOP_AFTER_FLYWAY=Y EXCLUSIVE_COUNT=$((EXCLUSIVE_COUNT+1)) EXCLUSIVE_LIST="$EXCLUSIVE_LIST -$opt";;
        m)  GEN_MEGA_VIEWS=Y ;;
        l)  LIST=$OPTARG ;;
        h)  echo "$usage"; exit 0 ;;
        ?)  echo "Error: invalid arguments specified"; echo "$usage" ; exit 0 ;;
        esac
done

if [[ -z "$MIGR_TAG" ]]
then
  echo "Error: A migration tag/label must be specified using the -T parameter" ; echo "$usage" ; exit 0
fi

if [[ $EXCLUSIVE_COUNT -gt 1 ]]
then
  echo "Error: Mutually exclusive options cannot be specified together:$EXCLUSIVE_LIST" ; echo "$usage" ; exit 0
fi

NUM_FILES=${#MIGFILES[*]}
if [[ $NUM_FILES -eq 0 && -z "$INPUT_DIR" ]]
then
		echo "Error: A migration Flyway Directory should be specified with -d option or a migration File name should be specified with -f option" ; echo "$usage" ; exit 0
fi

if [[ ! -z "$INPUT_DIR" && ! -d $INPUT_DIR ]]
	then
		echo "Error: Flyway migration directory $INPUT_DIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d "$INPUT_DIR/db_migration" ]]
	then
		echo "Error: Flyway migration directory "$INPUT_DIR/db_migration" does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d $FLYWAYDIR ]]
	then
		echo "Error: Flyway runtime directory $FLYWAYDIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d $MIGDIR ]]
	then
		echo "Error: Migrations directory $MIGDIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -d $LOGDIR ]]
	then
		echo "Error: Log directory $LOGDIR does not exist"; echo "$usage" ; exit 0
fi

if [[ ! -w $LOGDIR ]]
	then
		echo "Error: User cannot write to $LOGDIR. Change permisssions."; echo "$usage" ; exit 0
fi

tmpfil=$( mktemp /tmp/schema_migrations_in_parallel.XXXXXX )
trap "rm '$tmpfil'" 0

if [[ $NUM_FILES -gt 0 ]]
  then
    for f in "${MIGFILES[@]}" ; do
    	if [[ ! -r "$MIGDIR/$f" ]]
    		then
    			echo "Error: $MIGDIR/$f does not exist"; echo "$usage" ; exit 0
    	fi
    	cat "$MIGDIR/$f" >> "$tmpfil"
    	echo >> "$tmpfil"
    done
fi

if [[ -z $USERNAME ]]
	then
		echo "Error: DB user which will run the migration should be specified with -u option" ; echo "$usage" ; exit 0
fi

if [[ -z $GO_AHEAD_AND_RUN ]]
	then
		echo "Error: Specify Y for real run or N for dry run" ; echo "$usage" ; exit 0
fi

if [[ ! -r $MIGGER ]]
	then
		echo "Error: $MIGGER does not exist in present working directory"; echo "$usage" ; exit 0
fi

if [[ INCLUDE_DDL_ONLY == 1 && INCLUDE_DML_ONLY == 1 ]]; then
  echo "Error: ddl-only (-D) and dml-only (-M) options are mutually exclusive"; echo "$usage" ; exit 0
fi

if [[ -z $LIST ]]
	then
		echo "Error: Specify the schema list with -l option"; echo "$usage" ; exit 0
fi

if [[ $LIST = 'IA_MIG_EXTRA' && ! -r $EXTRAMIGRLIST ]]
	then
		echo "Error: $EXTRAMIGRLIST with extra schemas does not exist"; echo "$usage" ; exit 0
fi

if [[ $LIST = 'IA_MIG_EXTRA' && -r $EXTRAMIGRLIST ]]
	then
		. $EXTRAMIGRLIST
fi

case $LIST in
    IA_MIG_PROD) LIST=$IA_MIG_PROD ;;
    IA_MIG_DR)	LIST=$IA_MIG_DR ;;
    IA_MIG_SPECIAL_PROD) LIST=$IA_MIG_SPECIAL_PROD ;;
    IA_MIG_SPECIAL_DR) LIST=$IA_MIG_SPECIAL_DR ;;
    IA_MIG_DEBUG) LIST=$IA_MIG_DEBUG ;;
    IA_MIG_EXTRA) LIST=$IA_MIG_EXTRA ;;
    IA_MIG_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    IA_MIG_SPECIAL_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    IA_MIG_DR_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    IA_MIG_SPECIAL_DR_POD[0123456789]*) listName=$LIST LIST=${!listName} ;;
    *) echo "Error: invalid schema list specified"; echo "$usage" ; exit 0 ;;
esac

echo Enter database user password for $USERNAME
read -s PASSWORD
if [[ -z $PASSWORD ]]
   then
         echo "Error: Password can't be null"; exit 0
fi

check_tns

NOW=$(date +%Y-%m-%d.%H:%M:%S)
TAG="$NOW.$MIGR_TAG"

if [[ -z "${MIGFILES[0]}" ]]
  then
    FILETAG="no_file"
  else
    FILETAG=${MIGFILES[0]}
  fi

if [[ ! -z "$INPUT_DIR" ]]
  then
    if [[ "$GEN_MEGA_VIEWS" == "Y" && $STOP_AFTER_FLYWAY != "Y" ]]
    then
      gen_mega_views
    fi
    run_flyway_migrations
fi

#
# Verify if post migration finished for the schemas where it was still running
#
verify_post_migration_on_schemas() {
  for entry in $LIST;
  do
    SCHEMA=${entry%%:*};
    SERVER=${entry##*:};
    SCHEMA=${SCHEMA^^}

    # verify if post migration is done for this schema
    if [[ ! " ${postMigratedSchemas[*]} " =~ " ${SCHEMA}:${SERVER} " ]]
    then
      verify_post_migration $SCHEMA $SERVER
    fi
  done
}

#
# Print a summary regarding the migrations at the end of the process
#
print_summary() {
  echo "####################################################"
  echo "Summary"
  echo "####################################################"

  # get the total number of schemas for which migration process is applied
  local logFiles=`ls $LOGDIR | grep $TAG`
  local noOfSchemas=0
  for logFile in $logFiles
  do
    if [[ ! $logFile =~ "migration" ]]
    then
      noOfSchemas=$((noOfSchemas+1))
    fi
  done

  # print the number of schemas for which migration ended successfully with no ORA error
  echo "Migration completed successfully on $noOfSuccessfulMig (out of $noOfSchemas) schemas"
  # print a summary for this schemas
  if [[ -e $LOGDIR/migration_success_summary.$TAG.log ]]
  then
    cat $LOGDIR/migration_success_summary.$TAG.log
  fi

  #get the number of schemas for which there where errors and migrations didn't apply
  local noOfSchemasWithErrors=$((noOfSchemas-noOfSuccessfulMig))
  if [[ $noOfSchemasWithErrors -gt 0 ]]
  then
    echo "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    # print the number of schemas for which migration ended with ORA or flyway errors
    echo "Migration failed on $noOfSchemasWithErrors (out of $noOfSchemas) schemas because of errors. Please check log files for errors."
    if [[ -e $LOGDIR/migration_error_summary.$TAG.log ]]
    then
      cat $LOGDIR/migration_error_summary.$TAG.log
    fi
  fi

  echo "####################################################"
  echo "Summary End"
  echo "####################################################"
 }

#
# Determine when file migration finishes for all schemas
#
if [[ $NUM_FILES -gt 0 ]]
  then

    # check the rest of the schemas for which post migration wasn't applied yet

    while [[ ${#postMigratedSchemas[@]} != ${#LIST[@]} && `ps -eaf | grep migger.sql | grep -v grep | awk '{ print $10 }'` ]]
    do

      MIGPIDCOUNT=`ps -eaf | grep migger | grep -v grep | awk '{ print $2 }' | wc -l`
      echo "####################################################"
      echo "Migration is running on $MIGPIDCOUNT schemas"
      echo "####################################################"

      # verify if post migration finished for schemas
      verify_post_migration_on_schemas

      sleep 30
    done

    # verify once again to be sure we handled all cases
    verify_post_migration_on_schemas

    MIGPIDCOUNT=`ps -eaf | grep migger | grep -v grep | awk '{ print $2 }' | wc -l`
    if [[ $MIGPIDCOUNT == 0 ]]
    then
      echo "####################################################"
      echo "Migration completed on all schemas. Please check log files for errors and make sure there are ZERO invalids"
      echo "####################################################"

      # print summary on the screen
      print_summary

    fi
fi

exit
