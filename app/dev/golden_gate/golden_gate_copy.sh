#!/bin/bash

script_name=$(basename $0)

help_function() {
  usage="Usage:
	$script_name --path path
	$script_name -h,--help
	 --path path    : path for the curl call. Has to be in the format https://server/users/user/sandbox/tools/golden_gate_copy_company.phtml
	 -h,--help      : show the usage
"

  echo "$usage"
}

parse_options_function() {
   if [ -z $1 ]
    then
      help_function
      exit 1
    fi 
    
  while [[ $# -gt 0 ]]
  do
    key="$1"
    case $key in
      -h|--help)
        help_function
        shift
        ;;
      --path)
        GG_PATH="$2"
        shift
        shift
        ;;
      *)
        echo -e "\nUnexpected parameter $key\n"
        help_function
        exit 1
        ;;
    esac
  done
}

call_golden_gate_copy() {
  #   --header 'X-Forwarded-For: ***********' \ - use for debug
  url="--header 'Content-Type: application/x-www-form-urlencoded' \
  --insecure \
  --silent ${GG_PATH} \
  "

  curl ${url}

}

parse_options_function $@

call_golden_gate_copy
