<!--     This DTD defines the common functions and company functions  	
         File:               intacct_co_functions.v2.0.dtd	
         Version:            2.0
         Last Modified:      09/05/2000  	
		
         (C)2000, Intacct Corporation, All Rights Reserved 

         Intacct Corporation Proprietary Information.
         This document contains trade secret data that belongs to Intacct 
         corporation and is protected by the copyright laws. Information herein 
         may not be used, copied or disclosed in whole or part without prior 
         written consent from Intacct Corporation.
-->

<!-- Filter structure -->
<!ELEMENT filter ((logical+) | (expression+))>
    <!ELEMENT expression (field, operator, value)>
    <!ELEMENT logical ((expression, expression, expression*) | 
                       (logical+, expression+) | 
                       (logical, logical, logical*))>
    <!ATTLIST logical
	    logical_operator (and | or) #REQUIRED>

<!-- init_session function -->
<!ELEMENT init_session (activity?) >

<!-- Get_List function -->
<!ELEMENT get_list (filter?, sorts?, fields?)>
<!ATTLIST get_list
	object (%LIST_OBJECTS;) #REQUIRED
	maxitems CDATA #IMPLIED>

    <!ELEMENT sorts (sortfield+)>
        <!ELEMENT sortfield (#PCDATA)>
        <!ATTLIST sortfield
            order (asc | desc) #REQUIRED>

    <!ELEMENT fields (field+)>
        <!ELEMENT field (#PCDATA)>

<!-- Create an employee -->
<!ELEMENT create_employee 
	(employeeid, ssn?, title?, locationid?, departmentid?,
	supervisorid?, birthdate?, startdate?, enddate?, status?, externalid?, personalinfo?)>
<!ATTLIST create_employee
		ignoreduplicates (true | false) "false">

<!-- Delete an employee -->
<!ELEMENT delete_employee EMPTY>
<!ATTLIST delete_employee
	employeeid CDATA #REQUIRED
	externalkey (true | false) "false">

<!-- Get an employee -->
<!ELEMENT get_employee EMPTY>
<!ATTLIST get_employee
	employeeid CDATA #REQUIRED
	externalkey (true | false) "false">

<!-- Update an employee -->
<!ELEMENT update_employee 
(ssn?, title?, locationid?, departmentid?, supervisorid?, birthdate?, startdate?, enddate?, status?, externalid?)>
<!ATTLIST update_employee
	employeeid CDATA #REQUIRED
	set_nulls (true | false) "false"
	externalkey (true | false) "false">

<!-- Create a contact -->
<!ELEMENT create_contact (contactname, printas, %CONTACT_OPTIONAL_FIELDS;, externalid?)>
<!ATTLIST create_contact
		ignoreduplicates (true | false) "false">

<!-- Update a contact -->
<!ELEMENT update_contact (printas, %CONTACT_OPTIONAL_FIELDS;, externalid?)>
<!ATTLIST update_contact
	contactname CDATA #REQUIRED
	set_nulls (true | false) "false"
	externalkey (true | false) "false">

<!-- Delete a contact -->
<!ELEMENT delete_contact EMPTY>
<!ATTLIST delete_contact
	contactname CDATA #REQUIRED
	externalkey (true | false) "false">

<!-- Get a contact -->
<!ELEMENT get_contact EMPTY>
<!ATTLIST get_contact
	contactname CDATA #REQUIRED
	externalkey (true | false) "false">

<!-- Create Department -->
<!ELEMENT create_department (departmentid, title?, parentid?, supervisorid?, externalid?, status?)>
<!ATTLIST create_department
	ignoreduplicates (true | false) "false">

<!-- Update Department -->
<!ELEMENT update_department (title?, parentid?, supervisorid?, externalid?, status?)>
<!ATTLIST update_department
	departmentid CDATA #REQUIRED
	set_nulls (true | false) "false"
	externalkey (true | false) "false">

<!-- Delete Department -->
<!ELEMENT delete_department EMPTY>
<!ATTLIST delete_department
	departmentid CDATA #REQUIRED
	externalkey (true | false) "false">

<!-- Create Location -->
<!ELEMENT create_location (locationid, name, parentid?, supervisorid?, startdate?, enddate?, usedingl?,
		status?, externalid?, contactinfo?, shipto?)>
<!ATTLIST create_department
	ignoreduplicates (true | false) "false">

<!-- Update Location -->
<!ELEMENT update_location (name?, parentid?, supervisorid?, startdate?, enddate?, usedingl?,
		status?, externalid?, contactinfo?, shipto?)>
<!ATTLIST update_location
	locationid CDATA #REQUIRED
	set_nulls (true | false) "false"
	externalkey (true | false) "false">

<!-- Delete Location -->
<!ELEMENT delete_location EMPTY>
<!ATTLIST delete_location
	locationid CDATA #REQUIRED
	externalkey (true | false) "false">


<!-- Create Journal -->
<!ELEMENT create_journal (symbol, title, externalid?, status?)>
<!ATTLIST create_journal
	ignoreduplicates (true | false) "false">

<!-- Update journal -->
<!ELEMENT update_journal (title?, externalid?, status?)>
<!ATTLIST update_journal
	symbol CDATA #REQUIRED
	externalkey (true | false) "false">

<!-- Delete Journal -->
<!ELEMENT delete_journal (title?, status?)>
<!ATTLIST delete_journal
	symbol CDATA #REQUIRED
	externalkey (true | false) "false">

<!-- Create Glaccount -->
<!ELEMENT create_glaccount (accountno, title, normalbalance, accounttype?, closingtype?, closingaccountno?, status?,
		requiredept?, requireloc?, taxable?, externalid?)>
<!ATTLIST create_glaccount
	ignoreduplicates (true | false) "false">

<!-- Update Glaccount -->
<!ELEMENT update_glaccount (title?, normalbalance?, accounttype?, closingtype?, closingaccountno?, status?, requiredept?,
		requireloc?, taxable?, externalid?)>
<!ATTLIST update_glaccount
	accountno CDATA #REQUIRED
	set_nulls (true | false) "false"
	externalkey (true | false) "false">

<!-- Delete Glaccount -->
<!ELEMENT delete_glaccount EMPTY>
<!ATTLIST delete_glaccount
	accountno CDATA #REQUIRED
	externalkey (true | false) "false">