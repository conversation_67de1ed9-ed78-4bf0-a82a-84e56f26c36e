<?php

/**
 * Class ReadReportUtil
 *
 * Utility class implementing static method for readReport API
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2020 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class ReadReportUtil
{

    // POLICY_PARAMETERS_MAP maps policy fields (key) to a field passed to a report generator
    const POLICY_PARAMETERS_MAP = [
        'MODEFLAG'   => 'POLICY_MODEFLAG',
        'PROTOCOL'   => 'POLICY_PROTOCOL',
        'ADDRESSURL' => 'POLICY_ADDRESSURL',
        'METHOD'     => 'POLICY_METHOD',
        'URLENCODE'  => 'POLICY_URLENCODE',
        'CONTENTKEY' => 'POLICY_CONTENTKEY' ];

    // COPIED_PARAMETERS_LIST list of parameters copied forward to the request
    const COPIED_PARAMETERS_LIST = [ 'STATUS', 'SE<PERSON>ER<PERSON>', 'CONTROLID', 'UNIQUE<PERSON>', 'DTDVERS<PERSON>',
                                     'USERID', 'COMPANYID', 'LOCATIONID', 'SESSIONTIMESTAMP', 'SESSIONTIMEOUT',
                                     'FUNCTION_CONTROL_ID'];

    /**
     * The method evaluates the readReport method input.
     * 1. No report id is provided - VALID input
     * 2. Report Id is NOT provided:
     *    - no policy / invalid policy - ERROR
     *    - policy is NOT HTTP / HTTPS - ERROR
     *    - policy is HTTP / HTTPT but not ASYNCHRONOUS - ERROR
     *
     * @param string $partnerId
     * @param string $policyId
     * @param string $reportId
     *
     * @return string
     */
    public static function evaluateReadReportInput(string $partnerId, string $reportId = '', string $policyId = '')
    {
        if (!$partnerId) {
            Globals::$g->gErr->addError("BL03002028", __FILE__ . '.' . __LINE__,
                                        _("Invalid partner Id."));
            throw new IAException('Valid partner Id is required');
        }

        $ret = 'VALID';
        $policy = null;
        if ( $policyId ) {
            $policy = self::getCachedPolicy($partnerId, $policyId);
        }

        if ( $reportId ) {
            // User specified report Id is provided. Continue with validating the policy

            if ( ! empty($policy) ) {
                if ( imsTransportPolicyManager::PROTOCOL_HTTP === $policy['PROTOCOL']
                     || imsTransportPolicyManager::PROTOCOL_HTTPS === $policy['PROTOCOL'] ) {
                    // Report id is provided in the request and the policy with the HTTP / HTTPS protocol is selected
                    if ( imsTransportPolicyManager::TRANSPORT_MODE_ASYNCHRONOUS === $policy['MODEFLAG'] ) {
                        // User input is Valid. Return the code indicating to suppress the notification
                        // with the report ID.
                        $ret = 'VALID_SUPPRESS_NOTIFICATION';
                    } else {
                        // ERROR condition. The transport mode is NOT ASYNCHRONOUS
                        Globals::$g->gErr->addError("BL03002028", __FILE__ . '.' . __LINE__,
                                                    _("You provided a report ID, which is used with asynchronous processing, but provided a synchronous transport policy ID. " .
                                                      "Provide an asynchronous transport policy ID and try again."));
                        $ret = 'INVALID';
                    }
                } else {
                    // If the report Id is provided the policy myst have the HTTP / HTTPS protocl specified
                    Globals::$g->gErr->addError("BL03002028", __FILE__ . '.' . __LINE__,
                                                _("The transport policy ID you provided is not allowed for this operation. " .
                                                  "Provide an asynchronous transport policy ID with an  HTTP/HTTPS protocol and try again."));
                    $ret = 'INVALID';
                }
            } else {
                // Report Id is provided but policy is NOT. Report an error
                Globals::$g->gErr->addError("BL03002028", __FILE__ . '.' . __LINE__,
                                            _("You provided a report ID, which is used with asynchronous processing, without providing an asynchronous transport policy ID. " .
                                              "Provide an asynchronous transport policy ID and try again."));
                $ret = 'INVALID';
            }
        }

        return $ret;
    }

    /**
     * Checks for the executed function and if that is readReport confirms that for the case when the reportId
     * is provided the correct policy Id is specified.
     *
     * @param xmlgw $xmlgw
     *
     * @return bool
     * @throws IAException
     */
    public static function checkReadReportCompliance(xmlgw $xmlgw)
    {
        $ret = true;
        $policyId = $xmlgw->_policyid;

        if ($policyId) {
            $policy = self::getCachedPolicy($xmlgw->_partnerid, $xmlgw->_policyid);

            if ( ! empty($policy) ) {
                // The valid policy is specified. Parse the full erquest to check for report Id
                if ( $xmlgw->_ParseRequest(false) ) {
                    // The policy with HTTP / HTTPS protocol is specified.
                    // Check for the function (readReport) with the provided report Id - ERROR condition
                    if ( isset($xmlgw->_domRequest['operation'][0]['content']) ) {
                        foreach ( $xmlgw->_domRequest['operation'][0]['content'] as $singleRecord ) {
                            if ( isset($singleRecord['function']) ) {
                                $functions = $singleRecord['function'];
                                foreach ( $functions as $function ) {
                                    if ( isset($function['readReport'][0]['reportId']) ) {
                                        $reportId = $function['readReport'][0]['reportId'][0]['cdata'];

                                        if ('INVALID' === self::evaluateReadReportInput($xmlgw->_partnerid, $reportId, $policyId)) {
                                            $ret = false;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $ret;
    }

    /**
     * Returns policy specified by the partner Id and policy Id
     *
     * @param string $partnerId
     * @param string $policyId
     *
     * @return array
     * @throws IAException
     */
    public static function getCachedPolicy(string $partnerId, string $policyId)
    {
        if ( $partnerId == '') {
            throw new IAException(__FILE__ . '.' . __LINE__ . "partnerId is invalid");
        }
        if ( $policyId == '') {
            throw new IAException(__FILE__ . '.' . __LINE__ . "policyId is invalid");
        }

        static $cachedPolicies = [];
        $key = $partnerId . ':' . $policyId;

        if ( ! isset($cachedPolicies[$key]) ) {
            $policyManager = new imsTransportPolicyManager($partnerId);
            $policy = $policyManager->Get($policyId);

            if ( ! empty($policy) ) {
                $cachedPolicies[$key] = $policy;
            } else {
                // the Get method might return false
                $cachedPolicies[$key] = [];
            }
        }

        return $cachedPolicies[$key];
    }

    /**
     * Reads the policy for the given partner and policy id and injects policy fields into the
     * passed properties array.
     *
     * @param array  $params
     * @param string $partnerId
     * @param string $policyId
     *
     * @throws IAException
     */
    public static function injectPolicyParameters(array &$params, string $partnerId, string $policyId)
    {
        $policy = self::getCachedPolicy($partnerId, $policyId);

        if ( ! empty($policy)) {
            $params['POLICYID'] = $policyId;
            $params['SUPPRESS_NOTIFICATION'] = true;

            foreach ( self::POLICY_PARAMETERS_MAP as $policyField => $reportField ) {
                $params[$reportField] = $policy[$policyField];
            }
        }

    }

    /**
     * Copies the previously collected request parameters into the array that will be used for
     * processing the report.
     *
     * @param array $reportParams
     * @param array $requestParams
     */
    public static function injectReportParameters(array &$reportParams, array $requestParams)
    {
        if ( $requestParams['POLICYID'] ) {
            $reportParams['POLICYID'] = $requestParams['POLICYID'];
            if ( $requestParams['SUPPRESS_NOTIFICATION'] ) {
                $reportParams['SUPPRESS_NOTIFICATION'] = $requestParams['SUPPRESS_NOTIFICATION'];
            }

            if ( $requestParams['SHOWSTATUSONLY'] ) {
                $reportParams['SHOWSTATUSONLY'] = $requestParams['SHOWSTATUSONLY'];
            }

            foreach ( self::POLICY_PARAMETERS_MAP as $reportField ) {
                if ( $requestParams[$reportField] ) {
                    $reportParams[$reportField] = $requestParams[$reportField];
                }
            }

            foreach ( self::COPIED_PARAMETERS_LIST as $reportField ) {
                if ( $requestParams[$reportField] ) {
                    $reportParams[$reportField] = $requestParams[$reportField];
                }
            }

        }
    }

    /**
     * Injects the response parameters into the passed properties array.
     *
     * @param array $params
     * @param array $responseParams
     */
    public static function injectResponseParameters(array &$params, array $responseParams)
    {
        foreach ( $responseParams as $key => $value) {
            $params[strtoupper($key)] = $value;
        }
    }

    /**
     * Build the xml string for readReport request. The output depends on the $showStatusOnly flag:
     * 1. $showStatusOnly is false - the default behavior. The method will call readMore method and build
     *    the first batch of data.
     * 2. $showStatusOnly is true - the user is only interested in summary information (like number of records)
     *    and will query the complete set of data by running readMore
     *
     * @param array $packageBody
     * @param bool  $showStatusOnly
     *
     * @return string
     */
    public static function buildReadReportResponse(array &$packageBody, bool $showStatusOnly = false)
    {
        $objectName = null;
        $viewName = null;
        $reportId = $packageBody['PROVIDED_REPORT_ID'];
        $resultId = null;

        $contentKey = $packageBody['POLICY_CONTENTKEY'] ?? 'data';

        // Data section
        $data = [];
        // report section
        $report = [];

        if ( $showStatusOnly ) {
            // No need to return any record. Just put the summary information and the status as DONE
            $count = 0;
            $totalcount = $packageBody['NUMBER_OF_RECORDS'] ?? 0;
            $numremaining = $totalcount;
            $resultId = null;

            $summary = [
                $contentKey => [ 'STATUS' => [ [ 'DONE' ] ] ],
            ];
            $report['report'][] = $summary;
        } else {
            // Need to return the first batch of data.
            // Run readMore and populate the report section with all the data and the summary information
            $readMoreOutParams = [];
            require_once 'xmlgw_router.3.0.cls';

            $values = api_readMore(Session::getKey(), $objectName, $viewName, $reportId, $resultId,
                                   $readMoreOutParams, false);
            $values = [ [ $contentKey => $values ] ];

            foreach ( ( $values ?? [] ) as $finalRow ) {
                $nextRow =
                    Util_DataRecordFormatter::phpToXmlArray($finalRow, xmlgw_router_3_0::$emptyArrayToStringConversion);

                //  If the row is complete empty, we still want an empty XML row, else it causes problems both for
                //   the code (malformed XML) and the caller (no indication of the null values in the row).
                //   The code below provides an empty row.
                if ( is_null($nextRow) ) {
                    $nextRow = [ '' ];
                }
                $report['report'][] = $nextRow;
            }

            $count = $readMoreOutParams['NUM_THIS_TIME'] == 0 ? '0' : $readMoreOutParams['NUM_THIS_TIME'];
            $totalcount = $readMoreOutParams['TOTAL_COUNT'] == 0 ? '0' : $readMoreOutParams['TOTAL_COUNT'];
            $numremaining = ( $readMoreOutParams['NUM_REMAINING'] <= 0 ) ? '0' : $readMoreOutParams['NUM_REMAINING'];
            if ( ! empty($readMoreOutParams['RESULTID']) ) {
                $resultId = $readMoreOutParams['RESULTID'];
            }
        }

        $report['report'][0]['reportId'] = $reportId;

        $data[] = $report;

        if (Globals::$g->gErr->hasErrors()) {
            $status = 'failure';
        } else {
            $status = $packageBody['STATUS'];

            $data[0]['listtype'] = 'report';
            $data[0]['count'] = $count;
            $data[0]['totalcount'] = $totalcount;
            $data[0]['numremaining'] = $numremaining;
            if ( $resultId !== null ) {
                $data[0]['resultId'] = $resultId;
            }
        }

        // authentication section
        $authentication = [];
        $authentication["status"][0]['cdata'] = $packageBody['STATUS'];
        $authentication["userid"][0]['cdata'] = $packageBody['USERID'];
        $authentication["companyid"][0]['cdata'] = $packageBody['COMPANYID'];
        $authentication["locationid"][0]['cdata'] = $packageBody['LOCATIONID'];
        $authentication["sessiontimestamp"] = $packageBody['SESSIONTIMESTAMP'];
        $authentication["sessiontimeout"] = $packageBody['SESSIONTIMEOUT'];

        // result section
        $result = [];

        $result["status"][0]['cdata'] = $status;
        $result["function"][0]['cdata'] = 'readReport';
        $result["controlid"][0]['cdata'] = $packageBody['FUNCTION_CONTROL_ID'];

        $result['data'] = $data;

        // operation section
        $operation = [];
        $operation['authentication'][] = $authentication;

        $operation['result'][] = $result;

        // body section
        $body = [];

        $control = initControlSection($packageBody['SENDERID'], $packageBody['CONTROLID'], 'success',
                                      $packageBody['UNIQUEID'], $packageBody['DTDVERSION'], $packageBody['POLICYID']);
        $body['control'] = $control;

        $body['operation'][] = $operation;

        $gxml = new genxml;
        $gxml->pretty = true;

        $xmlOut = $gxml->build("response", $body, 'none');


        if (isset($packageBody['POLICY_URLENCODE']) && 'T' === $packageBody['POLICY_URLENCODE']) {
            $xmlOut = urlencode($xmlOut);
        }

        return $xmlOut;
    }

}
