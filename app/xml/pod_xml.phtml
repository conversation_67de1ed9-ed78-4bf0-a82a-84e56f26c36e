<?php

require_once 'util.inc';
require_once 'backend_misc.inc';
require_once 'backend_error.inc';
require_once 'ims_include.inc';
require_once "PODManager.cls";

if ( $_POST['_is_gateway_request'] == 'true' ) {
    $xmlResponse = '';
    $requestXML = $_POST['_requestXML'];
    if ( $_POST['EXTERNAL_HOST'] != null ) {
        $_SERVER['HTTP_HOST'] = $_POST['EXTERNAL_HOST'];
    }
    $sender = $_POST['sender'];
    if ( $_POST['POD_ID'] != null ) {
        Globals::$g->gPODId = $_POST['POD_ID'];
    }
    $companyId = $_POST['company'];
    $sessionId = $_POST['session'];
    Globals::$g->gPODManager->executeXMLRequestOnPOD($xmlResponse, $requestXML, $sender, $companyId, $sessionId);

    echo $xmlResponse;
}
