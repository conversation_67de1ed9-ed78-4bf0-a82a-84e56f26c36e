[{"id": "IA.THE_REPORT_TYPE_YOU_RE_ADDING_DOESN_T_EXIST_OR", "value": "The Report type you're adding doesn't exist or isn't active."}, {"id": "IA.SELECT_AN_ACTIVE_REPORT_TYPE_CHANGE_THE_STATUS", "value": "Select an active Report type, change the status of the one to add, or create the new one."}, {"id": "IA.THE_REPORT_AUDIENCE_YOU_RE_ADDING_DOESN_T_EXIST", "value": "The Report audience you're adding doesn't exist or isn't active."}, {"id": "IA.SELECT_AN_ACTIVE_REPORT_AUDIENCE_CHANGE_THE_STA", "value": "Select an active Report audience, change the status of the one to add, or create the new one."}, {"id": "IA.PLEASE_MODIFY_THE_REPORT_TO_ACCESS_ONLY_OBJECTS", "value": "Please modify the report to access only objects you are authorized to access"}, {"id": "IA.COULD_NOT_READ_IMPORT_FILE", "value": "Could not read import file."}, {"id": "IA.PLEASE_MAKE_SURE_THE_FILE_IS_VALID", "value": "Please make sure the file is valid."}, {"id": "IA.INVALID_FILTER", "value": "Invalid filter"}, {"id": "IA.PLEASE_CHECK_YOU_FILTER_DEFINITION", "value": "Please check you filter definition"}, {"id": "IA.CAN_NOT_HANDLE_REPORT_REQUEST", "value": "Can not handle report request"}, {"id": "IA.YOU_ARE_NOT_AUTHORIZED_TO_RUN_THIS_REPORT", "value": "You are not authorized to run this report"}, {"id": "IA.PLEASE_REVISE_REPORT_DEFINITION", "value": "Please revise report definition"}, {"id": "IA.INVALID_FIELD", "value": "Invalid field"}, {"id": "IA.PLEASE_CHECK_FIELD_PATH", "value": "Please check field path"}, {"id": "IA.UNABLE_TO_CREATE_THE_DDS_AUTOMATIC_RECORD", "value": "Unable to create the Dds Automatic record."}, {"id": "IA.UNABLE_TO_UPDATE_THE_DDS_AUTOMATIC_RECORD", "value": "Unable to update the Dds Automatic record."}, {"id": "IA.COULD_NOT_ADD_DDS_SUBSCRIPTIONS", "value": "Could not add DDS subscriptions."}, {"id": "IA.ENTER_SUITABLE_VALUES_IN_THE_FIELD_S", "value": "Enter suitable values in the field(s)."}, {"id": "IA.ERROR_WHILE_RETRIEVING_DATA", "value": "Error while retrieving data."}, {"id": "IA.UNABLE_TO_RUN_DDS_JOB", "value": "Unable to Run Dds Job."}, {"id": "IA.THE_INVALID_DELETED_REFERENCES_ARE_REMOVED_FROM", "value": "The invalid/deleted references are removed from the report definition.${ADV_FILTER_MESSAGE} Please review the report definition."}, {"id": "IA.BR_THERE_ARE_REFERENCES_IN_THE_REPORT_DEFINITIO", "value": "<br>There are references in the report definition which are either invalid or deleted.", "markdown": true}, {"id": "IA.INVALID_TRANSACTION_DEFINITION", "value": "Invalid transaction definition"}, {"id": "IA.INVALID_TRANSACTION_DEFINITIONS_SPECIFIED_PLEAS", "value": "Invalid transaction definitions specified. Please remove following: ${DOC_TYPE}"}, {"id": "IA.PLEASE_SELECT_VALID_TRANSACTION_DEFINITION", "value": "Please select valid transaction definition"}, {"id": "IA.FIELD_INFORMATION_NOT_AVAILABLE_FOR", "value": "Field information not available for ${PATH}"}, {"id": "IA.MAXIMUM_RUN_TIME_PARAMETERS_CAN_BE", "value": "Maximum run-time parameters can be ${MAX}"}, {"id": "IA.INVALID_PERIOD_VALUE", "value": "Invalid PERIOD value '${PERIOD}'"}, {"id": "IA.INVALID_VALUE_NEXUS", "value": "Invalid value '${VAL}' found for '$NEXUS_PATH'"}, {"id": "IA.INVALID_REPORT_ARGUMENT", "value": "Invalid report argument '${NEXT_PATH}'"}, {"id": "IA.COULD_NOT_UPDATE_DDSJOB_STATUS", "value": "Could not update ddsjob status"}, {"id": "IA.GET_EXCEPTION_MESSAGE", "value": "${GET_MESSAGE}"}, {"id": "IA.COULD_NOT_DELETE_CONTRACT_USAGE_BILLING_RECORDS", "value": "Could not delete contract usage billing records with usagebillingid:${SCHEDULE_KEY}"}, {"id": "IA.REPORT_DOES_NOT_EXIST", "value": "Report '${REPORT_NAME}' does not exist"}, {"id": "IA.THE_REPORT_MUST_BE_RUN_IN_A_NON_ROOT_LOCATION", "value": "The report '${REPORT_NAME}' must be run in a non-root location"}, {"id": "IA.YOU_DO_NOT_HAVE_PERMISSION_TO_RUN_THE_REPORT", "value": "You do not have permission to run the report '${REPORT_NAME}'"}, {"id": "IA.COULD_NOT_FIND_REPORT_OBJECT_TYPE", "value": "Could not find report object type: ${QUERY_DEF_ROOT}"}, {"id": "IA.GET_EXCEPTION_ROOT_MESSAGE", "value": "${ROOT} ${QUERY_DEF_ROOT}"}, {"id": "IA.DOCUMENT_TYPE_IS_REQUIRED", "value": "Document type is required"}, {"id": "IA.GET_EXCEPTION_ROOT_MESSAGE_POINT", "value": "${ROOT} . ${QUERY_DEF_ROOT}"}, {"id": "IA.NO_COLUMNS_SELECTED_FOR_THE_RECORD_LIST_VIEW", "value": "No columns selected for the record list view"}, {"id": "IA.THE_VIEW_NAME_CANNOT_BE_A_SYSTEM_VIEW_NAME", "value": "The View name cannot be a system view name "}, {"id": "IA.DATABASE_ERROR", "value": "Database error."}, {"id": "IA.THE_REPORTS_PATH_CAN_NOT_BE_UPDATED_WITH_THE_NE", "value": "The reports path can not be updated with the new company title in CRW_DATA_MAP or CRW_BACKUP tables."}, {"id": "IA.SEE_ERROR_DETAILS", "value": "See error details."}, {"id": "IA.UNABLE_TO_CLOSE_THE_BOOKS_A_DATABASE_ERROR_OCCU", "value": "Unable to close the books; a database error occurred."}, {"id": "IA.UNABLE_TO_INSERT_SSO_STRUCTURE", "value": "Unable to insert SSO structure."}, {"id": "IA.SETTING_INTERACTIVE_REPORT_PERMISSIONS_FAILED", "value": "Setting interactive report permissions failed"}, {"id": "IA.INVALID_ROOT_OBJECT", "value": "Invalid root object"}, {"id": "IA.INVALID_ROOT_OBJECT_QUERY", "value": "Invalid root object: ${QUERY_DEF_ROOT}"}, {"id": "IA.PLEASE_SELECT_VALID_ROOT_OBJECT", "value": "Please select valid root object"}, {"id": "IA.INVALID_GROUP_BY", "value": "Invalid Group by"}, {"id": "IA.GROUP_IS_INVALID", "value": "${GROUP} is invalid"}, {"id": "IA.ALL_LEVELS_BELOW_FIRST_LEVEL_MUST_BE_SUBSET_OF", "value": "All levels below first level, must be subset of first level"}, {"id": "IA.INVALID_SUMMARY", "value": "<PERSON><PERSON>id Summary"}, {"id": "IA.IS_NOT_ALLOWED_ON", "value": "'${FUNC}' is not allowed on ${OBJECT}.${FIELD_ID}"}, {"id": "IA.PLEASE_PROVIDE_A_VALID_SUMMARY_OPERATION", "value": "Please provide a valid summary operation"}, {"id": "IA.PERMISSION_DENIED", "value": "Permission denied"}, {"id": "IA.YOU_DO_NOT_HAVE_PERMISSION_TO_VIEW_OBJECT_DATA", "value": "You do not have permission to view object data"}, {"id": "IA.MAXIMUM_SUMMARY_COLUMNS_CAN_BE", "value": "Maximum summary columns can be ${MAX}"}, {"id": "IA.MAXIMUM_GROUP_BY_COLUMNS_CAN_BE", "value": "Maximum group by columns can be ${MAX}"}, {"id": "IA.MAXIMUM_SORT_BY_COLUMNS_CAN_BE", "value": "Maximum sort by columns can be ${MAX}"}, {"id": "IA.MAXIMUM_REPORT_COLUMNS_CAN_BE", "value": "Maximum report columns can be ${MAX}"}, {"id": "IA.THE_VALUE_IS_NOT_A_VALID_NUMERIC_VALUE", "value": "The value '${VAL}' is not a valid numeric value"}, {"id": "IA.INVALID_NEXUS", "value": "Invalid Nexus"}, {"id": "IA.INVALID_NEXUS_OBJECT_PATH", "value": "Invalid Nexus ${LAST_OBJECT}.${PATH_VAL}"}, {"id": "IA.INVALID_NEXUS_OBJECT", "value": "Invalid Nexus Object ${OBJECT}"}, {"id": "IA.INVALID_LOCATION_ID", "value": "Invalid location ID."}, {"id": "IA.LOCATION_ID_IS_INVALID", "value": "Location ID '${LOC_NO}' is invalid."}, {"id": "IA.ENTER_A_LOCATION_ID_PRESENT_IN_YOUR_COMPANY", "value": "Enter a location ID present in your company."}, {"id": "IA.INVALID_DEPARTMENT_ID", "value": "Invalid department ID."}, {"id": "IA.DEPARTMENT_ID_IS_INVALID", "value": "Department ID '${DEPT_NO}' is invalid."}, {"id": "IA.ENTER_A_DEPARTMENT_ID_THAT_IS_VALID_FOR_THIS_CO", "value": "Enter a department ID that is valid for this company."}, {"id": "IA.FILTER_LOCATION_S_WERE_INVALID", "value": "Filter Location(s) were invalid."}, {"id": "IA.YOU_MUST_INCLUDE_AT_LEAST_ONE_OBJECT_SELECT_THE", "value": "You must include at least one object. Select the objects to include from the list of available objects."}, {"id": "IA.COULD_NOT_CREATE_DDS_HISTORY_RECORD", "value": "Could not create DDS History record!"}, {"id": "IA.REPORT_MENU_MODULE_AND_REPORT_MODULE_ARE_NOT_SA", "value": "Report menu module and Report module are not same."}, {"id": "IA.PLEASE_SELECT_THE_SAME_MODULE_FOR_REPORT_MENU_A", "value": "Please select the same module for Report menu also."}, {"id": "IA.COULD_NOT_COMPLETE_THE_CONFIGURATION", "value": "Could not complete the configuration."}, {"id": "IA.UNABLE_TO_ALLOCATE_REPORTING_RESOURCES", "value": "Unable to allocate reporting resources"}, {"id": "IA.INVALID_COMPANY", "value": "Invalid Company."}, {"id": "IA.COMPANY_NOT_FOUND", "value": "Company not found."}, {"id": "IA.ERROR_WHILE_DISABLING_DDS_FOR_TENANT", "value": "Error while disabling DDS for tenant: ${MSG}"}, {"id": "IA.THE_COMPANY_IS_NOT_ENABLED_FOR_DDS", "value": "The company is not enabled for DDS"}, {"id": "IA.COULD_NOT_CANCEL_THE_MODULE_SUBSCRIPTION", "value": "Could not cancel the module subscription."}]