[{"id": "IA.CANNOT_LOAD_CONSOL_SUBSIDIARY_QUERIES_ABORTING", "value": "Cannot load consol subsidiary queries - aborting"}, {"id": "IA.CONSOLIDATOR_LINK_GET_FAILED", "value": "Consolidator Link Get failed"}, {"id": "IA.CONSOLIDATOR_LINK_SET_FAILED", "value": "Consolidator Link Set failed"}, {"id": "IA.CONSOLIDATOR_UNLINK_GET_FAILED", "value": "Consolidator Unlink Get failed"}, {"id": "IA.CONSOLIDATOR_UNLINK_SET_FAILED", "value": "Consolidator Unlink Set failed"}, {"id": "IA.COMPANYPREF_QUERY_FAILED", "value": "companypref query failed"}, {"id": "IA.MODULE_QUERY_FAILED", "value": "module query failed"}, {"id": "IA.FAILED_TO_UNSERIALIZE_SUBSIDIARY_OBJECT", "value": "Failed to unserialize subsidiary object."}, {"id": "IA.COMPANYINFO_QUERY_FAILED", "value": "companyinfo query failed"}, {"id": "IA.NO_SUBPERIODKEY_WAS_PASSED_AS_A_PARAMETER", "value": "No Subperiodkey was passed as a parameter"}, {"id": "IA.FAILED_TO_OBTAIN_PERIODS_FROM_CUSTOM_QUERY", "value": "Failed to obtain periods from custom query"}, {"id": "IA.CANNOT_COMPLETE_REQUEST_ONE_OR_MORE_JOBS_ARE_ST", "value": "Cannot complete request. One or more jobs are still in progress - see the appropriate status screen."}, {"id": "IA.CANNOT_OBTAIN_SUBSIDIARY_MAPS_FOR_SUBSIDIARY_ID", "value": "Cannot obtain subsidiary maps for subsidiary ID: ${SUBSIDIARY_ID}"}, {"id": "IA.DBRUNNER_GETCONSOLIDATIONDATA_CALL_FAILED_FOR", "value": "DBRunner GetConsolidationData call failed for ${SUBSIDIARY_ID}"}, {"id": "IA.DEQUEUESESSION_FAILED_FOR_DOCONSOLIDATION", "value": "DequeueSession failed for DoConsolidation"}, {"id": "IA.DBRUNNER_PERIOD_GETCONSOLIDATIONDATA_CALL_FAILE", "value": "DBRunner Period-GetConsolidationData call failed for ${SUBSIDIARY_ID}"}, {"id": "IA.DEQUEUESESSION_FAILED_FOR_DOPERIODCONSOLIDATION", "value": "DequeueSession failed for DoPeriodConsolidation"}, {"id": "IA.DBRUNNER_COMPANY_GETCONSOL<PERSON>ATIONDATA_FAILED_FO", "value": "DBRunner Company-GetConsolidationData failed for ${SUBSIDIARY_ID}"}, {"id": "IA.DEQUEUESESSION_FAILED_FOR_DOCOMPANYCONSOLIDATIO", "value": "DequeueSession failed for DoCompanyConsolidation"}, {"id": "IA.DBRUNNER_CALL_FAILED_FOR_AND", "value": "DBRunner call failed for ${SUBSIDIARY_ID} and ${SUBSIDIARY_PERIOD_KEY}"}, {"id": "IA.DEQUEUESESSION_FAILED_FOR_DOCOMPANYPERIODCONSOL", "value": "DequeueSession failed for DoCompanyPeriodConsolidation"}, {"id": "IA.COULD_NOT_FIND_KEY_FOR_DATA_CONSOLIDATION_REQUE", "value": "Could not find key for Data Consolidation Request Status record for period '${PERIODKEY}' and subsidiary '${SUBSIDIARYKEY}'"}, {"id": "IA.COULD_NOT_FIND_DATA_CONSOLIDATION_REQUEST_STATU", "value": "Could not find Data Consolidation Request Status record '${PERIODKEY}' and subsidiary '${SUBSIDIARYKEY}'"}, {"id": "IA.COULD_NOT_SET_DATA_CONSOLIDATION_REQUEST_STATUS", "value": "Could not set Data Consolidation Request Status record"}, {"id": "IA.CANNOT_LOAD_CONSOL_MAP_QUERIES_ABORTING", "value": "Cannot load consol map queries - aborting"}, {"id": "IA.FAILED_TO_OBTAIN_PERIOD_MAPS", "value": "Failed to obtain period maps"}, {"id": "IA.FAILED_TO_OBTAIN_ACCOUNT_MAPS", "value": "Failed to obtain account maps"}, {"id": "IA.UNABLE_TO_CONTINUE_WITH", "value": "Unable to continue with ${ENTITY_DESC}."}, {"id": "IA.NO_SUBSIDIARIES_ARE_LINKED_LINK_A_SUBSIDIARY_BE", "value": "No subsidiaries are linked.  Link a subsidiary before mapping statistical accounts."}, {"id": "IA.ENTER_A_VALID_SUBSIDIARY_ID", "value": "Enter a valid Subsidiary ID."}, {"id": "IA.ENTER_A_VALID_REPORTING_PERIOD_NAME", "value": "Enter a valid Reporting Period name."}, {"id": "IA.DEQUEUESESSION_FAILED_FOR_PERIOD_REFRESH_LIST", "value": "DequeueSession failed for Period Refresh List"}, {"id": "IA.COULD_NOT_FIND_KEY_FOR_MAPPING_STATUS_RECORD_FO", "value": "Could not find key for '${MANAGER}' Mapping Status record for period '${PERIODKEY}' and subsidiary '${SUBSIDIARYKEY}'"}, {"id": "IA.COULD_NOT_RETRIEVE_MAPPING_STATUS_RECORD_FOR_PE", "value": "Could not retrieve '${MANAGER}' Mapping Status record for period '${PERIODKEY}' and subsidiary '${SUBSIDIARYKEY}'"}, {"id": "IA.COULD_NOT_SET_MAPPING_STATUS_RECORD", "value": "Could not set '${MANAGER}' Mapping Status record"}, {"id": "IA.CANNOT_LOAD_RECEIVE_QUERIES_ABORTING", "value": "Cannot load receive queries - aborting"}, {"id": "IA.CANNOT_FIND_PERIOD_BALANCE_SHEET_RATE_FOR_PERIO", "value": "Cannot find Period Balance Sheet Rate for period: ${SUBSIDIARY_ID}"}, {"id": "IA.CANNOT_FIND_PERIOD_WEIGHTED_AVERAGE_RATE_FOR_PE", "value": "Cannot find Period Weighted Average Rate for period: ${SUBSIDIARY_ID}"}, {"id": "IA.CANNOT_FIND_LOCATION_FOR_SUBSIDIARY_ID", "value": "Cannot find location for subsidiary id: ${SUBSIDIARY_ID}"}, {"id": "IA.CANNOT_FIND_CONSOLIDATION_LOCATION", "value": "Cannot find Consolidation Location"}, {"id": "IA.CANNOT_FIND_LIABILITY_MINORITY_INTEREST_ACCOUNT", "value": "Cannot find Liability Minority Interest account for subsidiary id: ${SUBSIDIARY_ID}"}, {"id": "IA.CANNOT_FIND_P_L_MINORITY_INTEREST_ACCOUNT_FOR_S", "value": "Cannot find P/L Minority Interest account for subsidiary id: ${SUBSIDIARY_ID}"}, {"id": "IA.CANNOT_FIND_CURRENCY_EXCHANGE_GAIN_LOSS_ACCOUNT", "value": "Cannot find Currency exchange Gain / Loss Account on Net Assets account for subsidiary id: ${SUBSIDIARY_ID}"}, {"id": "IA.CANNOT_FIND_CURRENCY_EXCHANGE_GAIN_LOSS_INCOME", "value": "Cannot find Currency exchange Gain / Loss Account on Net Income account for subsidiary id: ${SUBSIDIARY_ID}"}, {"id": "IA.FAILED_TO_PROCESS_INCOMING_DATA", "value": "Failed to Process Incoming Data"}, {"id": "IA.PROCESSBUDGET_FAILED", "value": "ProcessBudget failed"}, {"id": "IA.PROCESSCONSOLIDATIONENTRY_FAILED", "value": "ProcessConsolidationEntry failed"}, {"id": "IA.UNABLE_TO_DELETE_EXISTING_GL_BATCHES", "value": "Unable to delete existing GL batches"}, {"id": "IA.UNABLE_TO_DELETE_EXISTING_CONSOLIDATION_BUDGETS", "value": "Unable to delete existing Consolidation budgets"}, {"id": "IA.FAILED_TO_DELETE_BUDGET_FOR_PERIOD_SUBSIDIARY_A", "value": "Failed to delete budget for period ${PERIOD_KEY}, subsidiary ${SUBSIDIARY_REC}, and budget header ${BUDGET_HDR_KEY}"}, {"id": "IA.TRANSACTION_LINE_GL_ACCOUNT_IS_NOT_MAPPED", "value": "Transaction line gl account ${ACCOUNT_NO} is not mapped."}, {"id": "IA.BUDGET_LINE_GL_ACCOUNT_IS_NOT_MAPPED", "value": "Budget line gl account ${ACCOUNT_NO} is not mapped."}, {"id": "IA.FAILED_TO_UPDATE_BUDGET_FOR_PERIOD", "value": "Failed to update budget for period ${PERIOD_KEY}"}, {"id": "IA.FAILED_TO_INSERT_BUDGET_FOR_PERIOD_ACCOUNT_DEPA", "value": "Failed to insert budget for period ${PERIOD_KEY}, account ${BDGTLINE_ACCOUNT}, department ${BDGTLINE_DEPT}, and location ${BDGTLINE_LOCATION}"}, {"id": "IA.UNABLE_TO_CREATE_STATISTICAL_ACCOUNT_ACTIVITY_G", "value": "Unable to create Statistical Account Activity GLBatch"}, {"id": "IA.UNABLE_TO_CREATE_GAAP_ACQUISITION_BALANCE_SHEET", "value": "Unable to create GAAP Acquisition Balance Sheet GLBatch"}, {"id": "IA.UNABLE_TO_CREATE_TAX_ACQUISITION_BALANCE_SHEET", "value": "Unable to create Tax Acquisition Balance Sheet GLBatch"}, {"id": "IA.UNABLE_TO_CREATE_ACQUISITION_BALANCE_SHEET_GLBA", "value": "Unable to create Acquisition Balance Sheet GLBatch"}, {"id": "IA.UNABLE_TO_CREATE_STATISTICAL_ACQUISITION_BALANC", "value": "Unable to create Statistical Acquisition Balance Sheet GLBatch"}, {"id": "IA.UNABLE_TO_CREATE_ACTIVITY_GL_ENTRIES", "value": "Unable to create activity GL Entries"}, {"id": "IA.CONSOLIDATION_OF_SUBSIDIARY_FAILED", "value": "Consolidation of Subsidiary failed"}, {"id": "IA.UNABLE_TO_CREATE_MINORITY_INTEREST_EQUITY_P_L_G", "value": "Unable to create minority interest Equity P&L GL Batch"}, {"id": "IA.UNABLE_TO_CREATE_MINORITY_INTEREST_P_L_LIABILIT", "value": "Unable to create minority interest  P&L/Liabilities GL Entries"}, {"id": "IA.UNABLE_TO_COMMIT_GL_TRANSACTION", "value": "Unable to commit GL transaction"}, {"id": "IA.FAILED_TO_ADD_GLBATCH_FOR_AND_PERIOD_AND_BATCH", "value": "Failed to add glbatch for ${LOCATION_ID} and period ${PERIOD_NAME} and batch title '${BATCH_TITLE}'"}, {"id": "IA.FAILED_TO_DELETE_BATCHES", "value": "Failed to delete batches"}, {"id": "IA.UNABLE_TO_CREATE_ACCOUNT_ACTIVITY_GLBATCH", "value": "Unable to create Account Activity GLBatch"}, {"id": "IA.NO_ENTRY_AMOUNT_FOUND", "value": "No entry amount found"}, {"id": "IA.FAILED_TO_CREATE_GLENTRY_FOR_ACCOUNT_AND_SUBSID", "value": "Failed to create glentry for account ${ACCOUNT} and subsidiary ${LOCATION_ID} and date ${END_DATE} and the record is ${ENTRY_VALUES}"}, {"id": "IA.CREATE_GL_ENTRY_FAILED", "value": "Create GL entry failed"}, {"id": "IA.FAILED_TO_COMMIT_THE_TRANSACTION", "value": "Failed to commit the transaction"}, {"id": "IA.COULD_NOT_SET_GUID_DATA_CONSOLIDATION_RECEIVE_S", "value": "Could not set GUID Data Consolidation Receive Status record"}, {"id": "IA.COULD_NOT_FIND_KEY_FOR_DATA_CONSOLIDATION_RECEI", "value": "Could not find key for Data Consolidation Receive Status record for period '${PERIOD_KEY}' and subsidiary '${SUBSIDIARY_KEY}'"}, {"id": "IA.COULD_NOT_FIND_DATA_CONSOLIDATION_RECEIVE_STATU", "value": "Could not find Data Consolidation Receive Status record '${PERIOD_KEY}' and subsidiary '${SUBSIDIARY_KEY}' from recordkey '${RECORD_KEY}'"}, {"id": "IA.COULD_NOT_SET_DATA_CONSOLIDATION_RECEIVE_STATUS", "value": "Could not set Data Consolidation Receive Status record"}, {"id": "IA.FAILED_TO_ADD_GLBATCH_FOR_AND_PERIOD", "value": "Failed to add glbatch for ${LOCATION_ID} and period ${PERIOD_NAME}"}, {"id": "IA.NO_DEFAULT_JOURNAL_WAS_PROVIDED_DURING_THE_SUBS", "value": "No Default ${BATCH} Journal was provided during the subsidiary linking in the parent company ${GET_MY_COMPANY_NAME}"}, {"id": "IA.NO_SUBSIDIARIES_ARE_LINKED_LINK_A_SUBSIDIARY_MP", "value": "No subsidiaries are linked.  Link a subsidiary before mapping periods."}, {"id": "IA.NO_SUBSIDIARIES_ARE_LINKED_LINK_A_SUBSIDIARY_FI", "value": "No subsidiaries are linked.  Link a subsidiary first before mapping locations."}, {"id": "IA.NO_SUBSIDIARIES_ARE_LINKED_LINK_A_SUBSIDIARY_DE", "value": "No subsidiaries are linked.  Link a subsidiary first before mapping departments."}, {"id": "IA.NO_SUBSIDIARIES_ARE_LINKED_LINK_A_SUBSIDIARY_AC", "value": "No subsidiaries are linked.  Link a subsidiary first before mapping accounts."}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_ACCESS_RECORD_CO", "value": "Could not create Consolidation Access record - Company ID required"}, {"id": "IA.CANNOT_ENABLE_A_SUBSIDIARY_TO_BE_YOUR_PARENT_CO", "value": "Cannot enable a subsidiary to be your parent company."}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_ACCESS_RECORD_IN", "value": "Could not create Consolidation Access record - invalid Company ID!"}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_ACCESS_RECORD_TH", "value": "Could not create Consolidation Access record - the Company ID is not valid."}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATOR_DEFAULT_USER", "value": "Could not create Consolidator default user."}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATOR_RECORD", "value": "Could not create Consolidator record."}, {"id": "IA.COULD_NOT_FIND_CONSOLIDATOR_USER_S_DATA", "value": "Could not find consolidator user's data."}, {"id": "IA.COULD_NOT_SET_THE_CONSOLIDATOR_RECORD", "value": "Could not set the Consolidator record."}, {"id": "IA.CONSOLIDATION_PARENT_COMPANY_HAS_ALREADY_LINKED", "value": "Consolidation Parent company has already linked to this company.  Delete Subsidiary link in Parent company, or set Access to 'Disabled'."}, {"id": "IA.COULD_NOT_DELETE_CONSOLIDATOR_USER", "value": "Could not delete consolidator user."}, {"id": "IA.CANNOT_GET_THE_MAPPING_STATUS", "value": "Cannot get the mapping status"}, {"id": "IA.SELECT_THE_SUBSIDIARY_COMPANY_ID", "value": "Select the subsidiary company ID"}, {"id": "IA.SELECT_THE_PERIOD", "value": "Select the period "}, {"id": "IA.THE_CONSOLIDATOR_USER_IS_DISABLED_ACCESS_HAS_BE", "value": "The Consolidator user is disabled - access has been denied."}, {"id": "IA.VALIDATE_CREDENTIALS_CALL_FAILED_CNYTITLE_IS_AN", "value": "Validate Credentials call failed. cnytitle is ${INTACCT_CNY_TITLE} and login is ${INTACCT_LOGIN} and password is ${INTACCT_PASSWORD}"}, {"id": "IA.ACCESS_FAILED_CONSOLIDATION_ACCESS_HAS_BEEN_REM", "value": "Access Failed.  Consolidation Access has been removed by subsidiary."}, {"id": "IA.COULD_NOT_VALIDATE_CREDENTIALS_NONE_GIVEN", "value": "Could not validate credentials - none given!"}, {"id": "IA.FAILED_TO_ADD_SUBSIDIARY_PERIOD_TO_THE_REFRESH", "value": "Failed to add subsidiary '${SUBSIDIARY_KEY}' - period '${PERIOD_KEY_RECORD}' to the Refresh Status List"}, {"id": "IA.FAILED_TO_FIND_SUBSIDIARY_PERIOD_ENTRY_IN_THE_R", "value": "Failed to find subsidiary '${SUBSIDIARY_KEY}' - period '${PERIOD_KEY}' entry in the Refresh Status List"}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_STATISTICAL_ACCO", "value": "Could not create Consolidation Statistical Account Map record."}, {"id": "IA.COULD_NOT_FIND_SUBSIDIARY_DATA", "value": "Could not find subsidiary data."}, {"id": "IA.COULD_NOT_FIND_STATACCOUNT_DATA", "value": "Could not find stataccount data."}, {"id": "IA.COULD_NOT_SET_THE_CONSOLIDATION_STATISTICAL_ACC", "value": "Could not set the Consolidation Statistical Account Map record."}, {"id": "IA.FAILED_TO_ADD_CNSSTATACCOUNT_FOR_PERIOD", "value": "Failed to Add Cnsstataccount ${SUB_TITLE} for period ${CNS_PERIOD_KEY}"}, {"id": "IA.FAILED_TO_SET_CNSSTATACCOUNT_FOR_PERIOD", "value": "Failed to Set Cnsstataccount ${SUB_TITLE} for period ${CNS_PERIOD_KEY}"}, {"id": "IA.COULD_NOT_SET_GUID_STATISTICAL_ACCOUNT_MAPPING", "value": "Could not set GUID Statistical Account Mapping Status record"}, {"id": "IA.COULD_NOT_FIND_KEY_FOR_STATISTICAL_ACCOUNT_MAPP", "value": "Could not find key for Statistical Account Mapping Status record for period '${PERIOD_KEY}' and subsidiary '${SUBSIDIARY_KEY}'"}, {"id": "IA.COULD_NOT_RETRIEVE_ACCOUNT_MAPPING_STATUS_RECOR", "value": "Could not retrieve Account Mapping Status record"}, {"id": "IA.COULD_NOT_SET_STATISTICAL_ACCOUNT_MAPPING_STATU", "value": "Could not set Statistical Account Mapping Status record"}, {"id": "IA.NONE_OF_THE_SUBSIDIARY_COMPANY_IS_LINKED", "value": "None of the subsidiary company is linked"}, {"id": "IA.THE_COMPANY_IS_NOT_LINKED_TO_ANY_SUBSIDIARY_COM", "value": "The company is not linked to any subsidiary company"}, {"id": "IA.LINK_ANY_A<PERSON><PERSON>ABLE_SUBSIDIARY_COMPANY_BEFORE_CO", "value": "Link any available subsidiary company before consolidation process"}, {"id": "IA.CONSOLIDATION_PROCESS_FAILED", "value": "Consolidation process failed"}, {"id": "IA.THE_PERIOD_MAPPING_IS_NOT_DONE_WITH_THE_SUBSIDI", "value": "The period mapping is not done with the subsidiary company"}, {"id": "IA.MAP_THE_SUBSIDIARY_REPORTING_PERIOD_WITH_A_PARE", "value": "Map the subsidiary reporting period with a parent reporting period and try consolidation"}, {"id": "IA.MAP_THE_SELECTED_REPORTING_PERIOD_WITH_A_PARENT", "value": "Map the selected reporting period with a parent reporting period and try consolidation"}, {"id": "IA.THE_ACCOUNTS_MAPPING_IS_NOT_DONE_WITH_THE_SUBSI", "value": "The accounts mapping is not done with the subsidiary company for the chosen period"}, {"id": "IA.MAP_THE_ACCOUNTS_OF_SUBSIDIARY_WITH_THE_PARENT", "value": "Map the accounts of subsidiary with the parent accounts for the chosen reporting period and try consolidation"}, {"id": "IA.FAILED_TO_ADD_TO_THE_PERIOD_MAPPING_STATUS_LIST", "value": "Failed to add  ${SUBSIDIARY_RECORD} to the Period Mapping Status List"}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_PERIOD_MAP_RECOR", "value": "Could not create Consolidation Period Map record."}, {"id": "IA.COULD_NOT_FIND_REPORTING_PERIOD_DATA", "value": "Could not find reporting period data."}, {"id": "IA.COULD_NOT_SET_THE_CONSOLIDATION_PERIOD_MAP_RECO", "value": "Could not set the Consolidation Period Map record."}, {"id": "IA.FAILED_TO_SET_CNSPERIOD", "value": "Failed to Set Cnsperiod ${SUBPERIOD_SUBPERIODNAME}"}, {"id": "IA.COULD_NOT_SET_GUID_IN_PERIOD_MAPPING_STATUS_REC", "value": "Could not set GUID in Period Mapping Status record"}, {"id": "IA.COULD_NOT_FIND_LOCATION_MAPPING_STATUS_RECORD_A", "value": "Could not find Location Mapping Status record '${PERIODKEY}' and subsidiary '${SUBSIDIARYKEY}'"}, {"id": "IA.COULD_NOT_SET_PERIOD_MAPPING_STATUS_RECORD", "value": "Could not set Period Mapping Status record"}, {"id": "IA.FAILED_TO_ADD_CNSPERIOD", "value": "Failed to Add Cnsperiod ${SUB_PERIOD_NAME}"}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_LOCATION_MAP_REC", "value": "Could not create Consolidation Location Map record."}, {"id": "IA.COULD_NOT_FIND_LOCATION_DATA", "value": "Could not find location data."}, {"id": "IA.COULD_NOT_SET_THE_CONSOLIDATION_LOCATION_MAP_RE", "value": "Could not set the Consolidation Location Map record."}, {"id": "IA.FAILED_TO_ADD_CNSLOCATION_FOR_PERIOD", "value": "Failed to Add CnsLocation ${SUB_NAME} for period ${CNS_PERIOD_KEY}"}, {"id": "IA.FAILED_TO_SET_CNSLOCATION_FOR_PERIOD", "value": "Failed to Set CnsLocation ${SUBLOCATION_SUBNAME} for period ${CNS_PERIODKEY}"}, {"id": "IA.COULD_NOT_SET_LOCATION_MAPPING_STATUS_RECORD", "value": "Could not set Location Mapping Status record"}, {"id": "IA.COULD_NOT_FIND_KEY_FOR_LOCATION_MAPPING_STATUS", "value": "Could not find key for Location Mapping Status record for period '${PERIODKEY}' and subsidiary '${SUBSIDIARYKEY}'"}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_DEPARTMENT_MAP_R", "value": "Could not create Consolidation Department Map record."}, {"id": "IA.COULD_NOT_FIND_DEPARTMENT_DATA", "value": "Could not find department data."}, {"id": "IA.COULD_NOT_SET_THE_CONSOLIDATION_DEPARTMENT_MAP", "value": "Could not set the Consolidation Department Map record."}, {"id": "IA.FAILED_TO_ADD_CNSDEPARTMENT_FOR_PERIOD", "value": "Failed to Add Cnsdepartment ${SUB_TITLE} for period ${CNS_PERIOD_KEY}"}, {"id": "IA.FAILED_TO_SET_CNSDEPARTMENT_FOR_PERIOD", "value": "Failed to Set Cnsdepartment ${SUBDEPT_SUBTITLE} for period ${CNS_PERIOD_KEY}"}, {"id": "IA.COULD_NOT_SET_DEPARTMENT_MAPPING_STATUS_RECORD", "value": "Could not set Department Mapping Status record"}, {"id": "IA.COULD_NOT_FIND_KEY_FOR_DEPARTMENT_MAPPING_STATU", "value": "Could not find key for Department Mapping Status record for period '${PERIOD_KEY}' and subsidiary '${SUBSIDIARY_KEY}'"}, {"id": "IA.COULD_NOT_RETRIEVE_DEPARTMENT_MAPPING_STATUS_RE", "value": "Could not retrieve Department Mapping Status record"}, {"id": "IA.COULD_NOT_CREATE_CONSOLIDATION_GL_ACCOUNT_MAP_R", "value": "Could not create Consolidation GL Account Map record."}, {"id": "IA.COULD_NOT_FIND_GLACCOUNT_DATA", "value": "Could not find glaccount data."}, {"id": "IA.THIS_PARENT_ACCOUNT_IS_MAPPED_TO_BANK_ACCOUNT_C", "value": "This parent account is mapped to bank account: ${ACCOUNT_ID} - ${ACCOUNT_NAME}. Cannot map subsidiary accounts to a parent account that is linked to a bank account. Create another account in the parent for mapping this subsidiary account and use account groups to combine them for reporting."}, {"id": "IA.COULD_NOT_SET_THE_CONSOLIDATION_ACCOUNT_MAP_REC", "value": "Could not set the Consolidation Account Map record."}, {"id": "IA.FAILED_TO_ADD_CNSACCOUNT_FOR_PERIOD", "value": "Failed to Add Cnsaccount ${SUB_TITLE} for period ${CNS_PERIOD_KEY}"}, {"id": "IA.FAILED_TO_SET_CNSACCOUNT_FOR_PERIOD", "value": "Failed to Set Cnsaccount ${SUB_TITLE} for period ${CNS_PERIOD_KEY}"}, {"id": "IA.COULD_NOT_SET_GUID_ACCOUNT_MAPPING_STATUS_RECOR", "value": "Could not set GUID Account Mapping Status record"}, {"id": "IA.COULD_NOT_FIND_KEY_FOR_ACCOUNT_MAPPING_STATUS_R", "value": "Could not find key for Account Mapping Status record for period '${PERIODKEY}' and subsidiary '${SUBSIDIARYKEY}'"}, {"id": "IA.COULD_NOT_SET_ACCOUNT_MAPPING_STATUS_RECORD", "value": "Could not set Account Mapping Status record"}, {"id": "IA.SUBSIDIARY_ID_NOT_PICKED_ENTERED", "value": "Subsidiary ID Not Picked / Entered."}, {"id": "IA.SUBSIDIARY_ID_CANNOT_BE_BLANK", "value": "Subsidiary ID cannot be blank."}, {"id": "IA.PICK_ENTER_A_VALID_SUBSIDIARY_ID", "value": "Pick / Enter a valid subsidiary ID"}, {"id": "IA.THERE_WAS_A_PROBLEM_WHILE_CREATING_THE_SUBSIDIA", "value": "There was a problem while creating the subsidiary record with the data you have entered."}, {"id": "IA.UNABLE_TO_CREATE_SUBSIDIARY_LINK_A_LINK_FOR_THI", "value": "Unable to create subsidiary link. A link for this subsidiary already exists."}, {"id": "IA.PLEASE_CHECK_IF_ALL_THE_DATA_PROVIDED_IS_CORREC", "value": "Please check if all the data provided is correct."}, {"id": "IA.NO_CURRENCY_EXCHANGE_GAIN_LOSS_ACCOUNT_PROVIDED", "value": "No Currency exchange Gain/Loss account provided."}, {"id": "IA.SUBSIDIARY_LINKING_FAILED", "value": "Subsidiary Linking Failed."}, {"id": "IA.YOU_NEED_TO_PROVIDE_A_CURRENCY_EXCHANGE_GAIN_LO", "value": "You need to provide a Currency exchange Gain / Loss Account to link this subsidiary."}, {"id": "IA.AUTHENTICATION_WITH_SUBSIDIARY_COMPANY_FAILED", "value": "Authentication with Subsidiary company failed."}, {"id": "IA.SUBSIDIARY_HAS_ENABLED_MULTIPLE_BASE_CURRENCY_S", "value": "Subsidiary has enabled Multiple base currency. So please select a Consolidation book"}, {"id": "IA.THE_SUBSIDIARY_LINKAGE_FAILED", "value": "The subsidiary linkage failed"}, {"id": "IA.VERIFY_IF_THE_SUBSIDIARY_HAS_PROVIDED_EXTERNAL", "value": "Verify if the subsidiary has provided external access to the parent and try linking"}, {"id": "IA.THE_DEFAULT_PERCENTAGE_IS_NOT_IN_THE_RANGE_OF_2", "value": "The Default Percentage is not in the range of 20 % - 100 %"}, {"id": "IA.INVALID_OWNERSHIP_PERCENTAGE_ENTERED", "value": "Invalid Ownership Percentage Entered."}, {"id": "IA.ENTER_A_PERCENTAGE_OWNERSHIP_VALUE_OF_GREATER_T", "value": "Enter a percentage ownership value of greater than 20 and less than 100."}, {"id": "IA.CONSOLIDATION_ACCRUAL_QUERY_FAILED", "value": "consolidation accrual query failed"}, {"id": "IA.SUBSIDIARY_REPORTING_METHOD_DOES_NOT_MATCH_CONS", "value": "Subsidiary reporting method does not match consolidation company reporting method."}, {"id": "IA.COULD_NOT_FIND_ACQUISITION_PERIOD_DATA", "value": "Could not find acquisition period data."}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_JOURNAL", "value": "Could not find specified journal."}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_GAAP_ADJUSTMENT_JOURNA", "value": "Could not find specified gaap adjustment journal."}, {"id": "IA.SUBSIDIARY_COMPANY_HAS_NOT_ENABLED_GAAP_ADJUSTM", "value": "Subsidiary company has not enabled GAAP Adjustment Journals in the General Ledger configuration."}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_TAX_ADJUSTMENT_JOURNAL", "value": "Could not find specified tax adjustment journal."}, {"id": "IA.SUBSIDIARY_COMPANY_HAS_NOT_ENABLED_TAX_ADJUSTME", "value": "Subsidiary company has not enabled Tax Adjustment Journals in the General Ledger configuration."}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_SHARE_HOLDER_EQUITY_AC", "value": "Could not find specified share holder equity account group."}, {"id": "IA.INVALID_CURRENCY_EXCHANGE_GAIN_LOSS_ACCOUNT_PRO", "value": "Invalid Currency exchange Gain / Loss Account provided"}, {"id": "IA.PROVIDE_A_VALID_CURRENCY_EXCHANGE_GAIN_LOSS_ACC", "value": "Provide a valid Currency exchange Gain / Loss Account"}, {"id": "IA.INVALID_CURRENCY_EXCHANGE_GAIN_LOSS_ACCOUNT_ON", "value": "Invalid Currency exchange Gain / Loss Account on Net Income provided"}, {"id": "IA.PROVIDE_A_VALID_CURRENCY_EXCHANGE_GAIN_LOSS_INC", "value": "Provide a valid Currency exchange Gain / Loss Account on Net Income"}, {"id": "IA.COULD_NOT_CREATE_SUBSIDIARY_RECORD", "value": "Could not create Subsidiary record."}, {"id": "IA.SETTING_THE_CONSOLIDATOR_LINK_RECORD_FAILED", "value": "Setting the Consolidator Link record failed."}, {"id": "IA.DBRUNNER_CALL_FAILED_FOR", "value": "DBRunner call failed for ${SUBSIDIARY_ID}"}, {"id": "IA.THERE_WAS_A_PROBLEM_WHILE_UPDATING_THE_SUBSIDIA", "value": "There was a problem while updating the subsidiary record with the data you have entered."}, {"id": "IA.COULD_NOT_UPDATE_SUBSIDIARY_RECORD", "value": "Could not update Subsidiary record."}, {"id": "IA.PROVIDE_A_CORRECT_CURRENCY_EXCHANGE_GAIN_LOSS_A", "value": "Provide a correct Currency exchange Gain/Loss account."}, {"id": "IA.CURRENCY_CONVERSION_RATE_MISSING", "value": "Currency conversion rate missing."}, {"id": "IA.YOU_NEED_TO_FILL_IN_THE_CURRENCY_CONVERSION_RAT", "value": "You need to fill in the currency conversion rate and if you do not want any conversion put in a value 1"}, {"id": "IA.INVALID_CURRENCY_EXCHANGE_GAIN_LOSS_ACCOUNT_AS", "value": "Invalid Currency exchange Gain / Loss Account on Net Assets provided"}, {"id": "IA.PROVIDE_A_VALID_CURRENCY_EXCHANGE_GAIN_LOSS_AS", "value": "Provide a valid Currency exchange Gain / Loss Account on Net Assets"}, {"id": "IA.PROVIDE_A_VALID_CURRENCY_EXCHANGE_GAIN_LOSS_NET", "value": "Provide a valid Currency exchange Gain / Loss Account on Net Income"}, {"id": "IA.SUB_COULD_NOT_DELETE_RECORD_WITH_ID", "value": "Could not delete ${ENTITY} record with ID ${ID}!"}, {"id": "IA.FAILED_TO_DELETE_THE_SUBSIDIARY_ENTRY", "value": "Failed to delete the Subsidiary Entry."}, {"id": "IA.PERIODIC_CURRENCY_CONVERSION_RATE_S_NOT_PROVIDE", "value": "Periodic currency conversion rate(s) not provided"}, {"id": "IA.PROVIDE_PERIODIC_CURRENCY_CONVERSION_RATE_S", "value": "Provide periodic currency conversion rate(s)."}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_STATISTICAL_JOURNAL", "value": "Could not find specified statistical journal."}, {"id": "IA.COMPANY_ID_FIELD_IS_NOT_PROVIDED", "value": "Company ID field is not provided."}, {"id": "IA.COULD_NOT_UPDATE_DISTRIBUTED_CONSOLIDATION", "value": "Could not update distributed consolidation console preferences!"}, {"id": "IA.COULD_NOT_INSERT_DISTRIBUTED_CONSOLIDATION_CONS", "value": "Could not insert distributed consolidation console preferences!"}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_LOCATION", "value": "Could not find specified location"}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_LIABILITY_ACCOUNT", "value": "Could not find specified liability account"}, {"id": "IA.COULD_NOT_FIND_SPECIFIED_EQUITY_ACCOUNT", "value": "Could not find specified Equity account"}, {"id": "IA.OPERATION_NOT_ALLOWED_ON_DISTRIBUTED", "value": "This operation is not allowed on Distributed Consolidation Setup."}, {"id": "IA.USE_NEWLY_PUBLISHED_API_OR_NEXTGEN_API", "value": "Please use newly published API or NextGen API of cssetup object."}]