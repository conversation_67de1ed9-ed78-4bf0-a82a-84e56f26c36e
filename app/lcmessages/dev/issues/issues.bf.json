[{"id": "IA.REQUEST_CONTAIN_INVALID_PAYMENT_RECORD_NUMBER_S", "value": "The request contains payment record numbers that are not valid."}, {"id": "IA.COULD_NOT_CREATE_BANKFILE_RECORD", "value": "Could not create BankFile record."}, {"id": "IA.LINKING_PAYMENT_AND_BANK_FILE_FAILED", "value": "Linking Payment and Bank file failed"}, {"id": "IA.ERROR_CONFIRMING_PAYMENTS", "value": "Error Confirming Payments"}, {"id": "IA.YOUR_BANK_FILE_IS_BEING_GENERATED_CONFIRM_THE_F", "value": "Your bank file is being generated. Confirm the file after it's done being generated"}, {"id": "IA.CANNOT_CONFIRM_BANK_FILE_PAYMENT_IN_STATE", "value": "Cannot confirm, when the payment file state is in '${STATE}'"}, {"id": "IA.YOUR_BANK_FILE_IS_CONFIRMED_AND_CAN_T_BE_DELETE", "value": "Your bank file is confirmed, and cannot be deleted."}, {"id": "IA.VENDOR_DOES_NOT_EXISTS", "value": "[${VENDORID}]: Vendor does not exists."}, {"id": "IA.PLEASE_PROVIDE_PROPER_VENDORID", "value": "Provide a valid Vendorid."}, {"id": "IA.VENDOR_IS_ALREADY_ENABLED_FOR_ACH", "value": "[${VENDORID}]: Vendor is already enabled for ACH."}, {"id": "IA.PLEASE_DISABLE_ACH_IF_BANK_FILE_METHOD_NEED_TO_BE_ENABLED", "value": "Disable ACH if bank file method needs to be enabled."}, {"id": "IA.BANK_FILE_COUNTRY_CODE_REQUIRED", "value": "Enter a country code to enable the bank file."}, {"id": "IA.BANK_FILE_CURRENCY_REQUIRED", "value": "Select the currency that matches your bank file format."}, {"id": "IA.BANK_FILE_FORMAT_FOR_BANK", "value": "Select the bank file format for your bank."}, {"id": "IA.VALID_BANK_FILE_FORMAT_FOR_BANK", "value": "Select a valid bank file format for your bank."}, {"id": "IA.ENABLE_BANK_FILE_ON_SAGE_CLOUD_SERVICES", "value": "Bank file payments are not enabled. Enable bank file functionality on the Sage Cloud Services subscription."}, {"id": "IA.BANK_FILE_NOT_SUPPORTED_FOR_COUNTRY", "value": "Bank file payments are not currently supported for the selected country. Make sure to check the release notes for updates on when this country is supported."}, {"id": "IA.BANK_FILE_ERROR_DESCRIPTION", "value": "${ERR_DESC_0}"}, {"id": "IA.UI_LABEL_IS_REQUIRED_FOR_OBJECT", "value": "'${UI_LABEL}' is required for ${OBJECT_NAME}, '${OBJECTID}'"}, {"id": "IA.UI_LABEL_IS_REQUIRED", "value": "'${UI_LABEL}' is required "}, {"id": "IA.PLEASE_PROVIDE_A_VALID_VALUE_FOR_FIELD_OF_THE_OBJECT", "value": "Please provide a valid value for field '${LABEL}' of the ${OBJECT_NAME} '${OBJECTID}'"}, {"id": "IA.PLEASE_PROVIDE_A_VALID_VALUE_FOR_FIELD", "value": "Please provide a valid value for field '${LABEL}'"}, {"id": "IA.PLEASE_PROVIDE_A_VALID_VALUE_FOR_LABEL_TO_OBJECT", "value": "Please provide a valid value for '${UI_LABEL}' to ${OBJECT_NAME} '${OBJECTID}'"}, {"id": "IA.BANK_FILE_FIELD_META_ERROR", "value": "${FIELD_META_ERROR}"}, {"id": "IA.PLEASE_PROVIDE_A_VALID_VALUE_FOR_UI", "value": "Please provide a valid value for '${UI_LABEL}'"}, {"id": "IA.VALID_BENEFICIARY_TYPE_FOR_BANK", "value": "Select a valid beneficiary type for your bank."}, {"id": "IA.FOR_THE_ACH_BANK_FILE_PAYMENT_BILL_ACC_CURRENCY_TO_MATCH", "value": "For the ACH / Bank file payment method, the bill and checking account currencies must match. Choose a bank that matches the transaction currency"}, {"id": "IA.ACH_PAYMENT_IS_ONLY_ALLOWED_FOR_USD_TRANSACTION", "value": "ACH payment is only allowed for USD transaction currency"}, {"id": "IA.ENABLE_ACH_BANK_FILE_PAYMENT_FOR_THE_VENDORS", "value": "Enable ACH / Bank File payment for the vendor ${VENDORID}"}, {"id": "IA.FOR_ACH_PAYMENT_VENDORS_SEC_CODE_SHOULDNT_BE_IAT", "value": "For ACH payment vendor's SEC code shouldn't be IAT"}, {"id": "IA.PAYMENT_VALIDATION_FAILED", "value": "Payment validation failed."}, {"id": "IA.NUMBER_OF_PAYMENTS_IN_A_SINGLE_BANK_FILE_BATCH", "value": "You can make up to '${BANKFILE_INFO_MAXPYMTCOUNT}' payments in a single bank file using this format, '${BANK_FILE_FORMAT}'. Retry with fewer payments "}, {"id": "IA.INVALID_STRING_SIZE", "value": "Invalid string size. "}, {"id": "IA.INVALID_BANK_FILE_FORMAT", "value": "Invalid bank file format ${BANK_FILE_FORMAT}"}, {"id": "IA.COUNTRY_CODE_IS_MISSING", "value": "Country code is missing."}, {"id": "IA.EMPLOYEE_BANK_FILE_ALREADY_SETUP", "value": "Bank file setup for the employee ${EMPLOYEEID} already exists."}, {"id": "IA.VENDOR_BANK_FILE_ALREADY_SETUP", "value": "Bank file setup for the vendor ${VENDORID} already exists."}, {"id": "IA.CANNOT_PAY_BANK_FILE_ENABLED_VENDOR_OR_EMPLOYEE_USING_MANUAL_ACH_BANK", "value": "The bank account ${BANKACCOUNT} is set up for ACH, not bank file payments. Please select an account that supports bank file payments."}, {"id": "IA.EMPLOYEE_DOES_NOT_EXIST_CSV_BANK_FILE", "value": "[${EMPLOYEEID}]: Employee does not exists. Please provide a valid proper Employee Id."}, {"id": "IA.EMPLOYEE_ALREADY_ENABLED_FOR_MANUAL_ACH", "value": "[${EMPLOYEEID}]: Employee is already enabled for ACH. Please disable ACH if bank file method need to be enabled."}, {"id": "IA.CANNOT_PAY_MANUAL_ACH_VENDOR_OR_EMPLOYEE_USING_BANK_FILE_ENABLED_BANK", "value": "The bank account ${BANKACCOUNT} is set up for bank file payments, not ACH. Please select an account that supports ACH payments."}, {"id": "IA.CONCURRENT_REQUEST_IN_PROCESS_BANK_FILE", "value": "Bank file generation is in process for the bank account. Please wait for the system to finish processing the original request before generating the bank file."}, {"id": "IA.MAX_PSN_COUNT_EXCEEDED_FOR_NACHA_FILES", "value": "The payment sequence number for this file exceeds the maximum allowed number, which is ${PSN}. Update your checking account's bank file settings to restart the payment sequence numbering and regenerate the payment file."}, {"id": "IA.CANNOT_UPDATE_DOCNUMBER_FOR_PAYMENT_RECORD", "value": "Unable to update docnumber for one or more payment records."}]