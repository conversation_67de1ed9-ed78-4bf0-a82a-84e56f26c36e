<?php
/**
 * CompanyFetchResult.class.php
 */

/**
 * 
 *
 * <AUTHOR>
 * @copyright � 2004 - 2011 Avalara, Inc.  All rights reserved.
 * @package   Batch
 */
importAvaClass('BaseResult');
class CompanyFetchResult extends BaseResult {
  private $Batches; // ArrayOfBatch
  private $RecordCount; // int

  public function setBatches($value){$this->Batches=$value;} // ArrayOfBatch
  public function getBatches(){return $this->Batches;} // ArrayOfBatch

  public function setRecordCount($value){$this->RecordCount=$value;} // int
  public function getRecordCount(){return $this->RecordCount;} // int

}


