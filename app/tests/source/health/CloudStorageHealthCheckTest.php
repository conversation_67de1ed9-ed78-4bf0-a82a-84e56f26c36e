<?php
namespace tests\source\health;

use CloudStorageHealthCheck;
use HealthCheckResult;
use DeliveryServiceManager;
use IAException;
use PHPUnit\Framework\MockObject\MockObject;
use AwsHandler;
use AzureHandler;
use Box<PERSON><PERSON>ler;
use OneD<PERSON>Hand<PERSON>;
use GoogleDriveHandler;
use DropB<PERSON>Handler;
use HTTP<PERSON>andler;

/**
 * @covers <PERSON>wsHandler
 * @covers AzureHandler
 * @covers BoxHandler
 * @covers CloudStorageHealthCheck
 * @covers DeliveryServiceManager
 * @covers GoogleDriveHandler
 * @covers HTTPHandler
 * @covers OneDriveHandler
 * @group unit
 */
class CloudStorageHealthCheckTest extends \unitTest\core\UnitTestBase
{
    /**
     * @var array $decCredentials decrypted dummy credentials
     */
    protected $decCredentials = [
        'HEALTHCHECK_CS_AWS' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user',
        'HEALTHCHECK_CS_ONEDRIVE' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user',
        'HEALTHCHECK_CS_AZURESTORAGE' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user',
        'HEALTHCHECK_CS_GOOGLEDRIVE' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user',
        'HEALTHCHECK_CS_BOX' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user',
        'HEALTHCHECK_CS_DROPBOX' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user',
        'HEALTHCHECK_CS_HTTP' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user'
    ];
    
    /**
     * @var array $encCredentials encrypted dummy credentials
     */
     protected $encCredentials = [
        'HEALTHCHECK_CS_AWS' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_ONEDRIVE' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_AZURESTORAGE' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_GOOGLEDRIVE' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_BOX' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_DROPBOX' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_HTTP' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c='
    ];
    
    /**
     * @var array $mixedCredentials encrypted and unencrypted dummy credentials
     */
    protected $mixedCredentials = [
        'HEALTHCHECK_CS_AWS' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_ONEDRIVE' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_AZURESTORAGE' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_GOOGLEDRIVE' =>  'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_BOX' => 'fooBar-cloud-storage-target@fooBar-company@fooBar-user',
        'HEALTHCHECK_CS_DROPBOX' =>  'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=',
        'HEALTHCHECK_CS_HTTP' => 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c='
    ];
    
    /**
     * @var integer $countCredentials
     */
    protected $countCredentials = 0;
    
    /**
     * @var array[] $arrDataProviderCheck
     */
    protected $arrDataProviderCheck = [
        ['checkAws', 'Aws', 'HEALTHCHECK_CS_AWS'],
        ['checkOneDrive', 'OneDrive', 'HEALTHCHECK_CS_ONEDRIVE'],
        ['checkAzureStorage', 'AzureStorage', 'HEALTHCHECK_CS_AZURESTORAGE'],
        ['checkGoogleDrive', 'GoogleDrive', 'HEALTHCHECK_CS_GOOGLEDRIVE'],
        ['checkBox', 'Box', 'HEALTHCHECK_CS_BOX'],
        ['checkDropbox', 'Dropbox', 'HEALTHCHECK_CS_DROPBOX'],
        ['checkHTTP', 'HTTP', 'HEALTHCHECK_CS_HTTP']
    ];
    
    /**
     * @return void
     */
    protected function setUp() : void
    {
        $this->countCredentials = count($this->encCredentials);
    }
    
    /**
     * initialize failed with exception
     * @return void
     */
    public function testInitializeFailedWithException() : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getHlthCfgCredentials', 'setCsErrors',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setCsErrors')
            ->with('initialize ' . $cloudStorageHealthCheckMock::NAME . ' exception', 'fooBar');
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHlthCfgCredentials')
            ->willThrowException(new IAException('fooBar'));
        
        $this->invokePrivate($cloudStorageHealthCheckMock, 'initialize');
    }
    
    /**
     * @return array dataProviderInitializeCleanupDefaultUnsetOrSetDifferentThanZero
     */
    public function dataProviderInitializeCleanupDefaultUnsetOrSetDifferentThanZero() : array
    {
        return [
            [
                1,
                $this->decCredentials,
                $this->decCredentials,
            ],
            [
                null,
                $this->decCredentials,
                $this->decCredentials,
            ],
            [
                1,
                [],
                [],
            ],
            [
                null,
                [],
                [],
            ],
            
        ];
    }
    
    /**
     * initialize with cleanup enabled: by default or set as request parameter with value 1, so delivered file is deleted on completion;
     *
     * @dataProvider dataProviderInitializeCleanupDefaultUnsetOrSetDifferentThanZero()
     *
     * @param mixed $cleanup
     * @param array $credentialHlthCfg
     * @param array $expectedResult
     *
     * @return void
     *
     */
    public function testInitializeCleanupDefaultUnsetOrSetDifferentThanZero(mixed $cleanup, array $credentialHlthCfg, array
    $expectedResult) : void
    {
        
        if (isset($cleanup)) {
            $_REQUEST['_cleanup'] = $cleanup;
        } else {
            if(array_key_exists('_cleanup', $_REQUEST)) {
                unset($_REQUEST['_cleanup']);
            }
        }
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getHlthCfgCredentials',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->method('getHlthCfgCredentials')
            ->willReturn($credentialHlthCfg);
        
        $this->invokePrivate($cloudStorageHealthCheckMock, 'initialize');
        $hlthCfgCredentialsResult = self::getPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials');
        self::assertSame($expectedResult, $hlthCfgCredentialsResult);
        
        $cleanupResult = self::getPrivateProperty($cloudStorageHealthCheckMock, 'cleanup');
        // by default cleanup is true so delivered file is deleted on completion; to keep the file add to URL parameter &.cleanup=0
        self::assertTrue($cleanupResult);
    }
    
    /**
     * @return array dataProviderInitializeRequestCleanupIsZero
     */
    public function dataProviderInitializeRequestCleanupIsZero() : array
    {
        return [
            [
                $this->decCredentials,
                $this->decCredentials,
            ],
            [
                [],
                [],
            ],
        
        ];
    }
    
    /**
     * by default cleanup is true so delivered file is deleted on completion; to keep the file add to URL parameter &.cleanup=0
     *
     * @dataProvider dataProviderInitializeRequestCleanupIsZero()
     *
     * @param array $credentialHlthCfg
     * @param array $expectedResult
     *
     * @return void
     *
     */
    public function testInitializeRequestCleanupIsZero(array $credentialHlthCfg, array $expectedResult) : void
    {
        $_REQUEST['_cleanup'] = 0;
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getHlthCfgCredentials',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->method('getHlthCfgCredentials')
            ->willReturn($credentialHlthCfg);
        
        $this->invokePrivate($cloudStorageHealthCheckMock, 'initialize');
        $hlthCfgCredentialsResult = self::getPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials');
        self::assertSame($expectedResult, $hlthCfgCredentialsResult);
        
        $cleanupResult = self::getPrivateProperty($cloudStorageHealthCheckMock, 'cleanup');
        // by default cleanup is true so delivered file is deleted on completion; to keep the file add to URL parameter &.cleanup=0
        self::assertFalse($cleanupResult);
    }
    
    /**
     * @return array dataProviderGetHlthCfgNoCredentials
     */
    public function dataProviderGetHlthCfgNoCredentials() : array
    {
        return [
            [
                [],
                [],
            ]
        ];
    }
    
    /**
     * there are no credentials in the health check configuration
     * @dataProvider dataProviderGetHlthCfgNoCredentials()
     * @param array $csCredentialsPlainText
     * @param array $expectedResult
     *
     * @return void
     * @throws \Exception
     */
    public function testGetHlthCfgNoCredentials( array $csCredentialsPlainText, array $expectedResult) : void
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
        $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getValueForHLTHCFGPropertyWrapper', 'getHealthCheckResult',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getValueForHLTHCFGPropertyWrapper')
            ->with('HEALTHCHECK_CS_CREDENTIALS')
            ->willReturn($csCredentialsPlainText);
        $hlthCfgCredentialsResult = $this->invokePrivate($cloudStorageHealthCheckMock, 'getHlthCfgCredentials');
        self::assertSame($expectedResult, $hlthCfgCredentialsResult);
    }
    
    /**
     * @return array dataProviderGetHlthCfgCredentialsPlainText
     */
    public function dataProviderGetHlthCfgCredentialsPlainText() : array
    {
        return [
            [
                $this->decCredentials,
                $this->decCredentials,
            ],
        ];
    }
    /**
     *  the credentials are in plain text, unencrypted, in the health check configuration
     *
     * @dataProvider dataProviderGetHlthCfgCredentialsPlainText()
     *
     * @param array $csCredentialsPlainText
     * @param array $expectedResult
     *
     * @return void
     *
     */
    public function testGetHlthCfgCredentialsPlainText(array $csCredentialsPlainText, array $expectedResult) : void
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
        $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setStatus', 'setDetail',
            ])
            ->getMock();
        
        $healthCheckResultMock
            ->expects(self::exactly($this->countCredentials))
            ->method('setStatus')
            ->with(" Encryption issue CloudStorage");
        
        $healthCheckResultMock
            ->expects(self::exactly($this->countCredentials))
            ->method('setDetail')
            ->with(
                $this->callback(function ($value) {
                    self::assertStringContainsString('HEALTHCHECK_CS_CREDENTIALS', $value);
                    self::assertStringContainsString('Use instead the outcome of TwoWayEncryptWithKey as value:', $value);
                    return true;
                })
            );
        
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getValueForHLTHCFGPropertyWrapper', 'getHealthCheckResult', 'setCsErrors'
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getValueForHLTHCFGPropertyWrapper')
            ->with('HEALTHCHECK_CS_CREDENTIALS')
            ->willReturn($csCredentialsPlainText);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly($this->countCredentials))
            ->method('setCsErrors')
            ->with(
                $this->callback(function ($value) {
                    self::assertStringContainsString('HEALTHCHECK_CS_CREDENTIALS', $value);
                    return true;
                })
            );
        
        $hlthCfgCredentialsResult = $this->invokePrivate($cloudStorageHealthCheckMock, 'getHlthCfgCredentials');
        self::assertSame($expectedResult, $hlthCfgCredentialsResult);
    }
    
    /**
     * @return array dataProviderGetHlthCfgCredentialsEncrypted
     */
    public function dataProviderGetHlthCfgCredentialsEncrypted() : array
    {
        return [
            [
                $this->encCredentials,
                $this->decCredentials,
            ],
        ];
    }
    
    /**
     * the credentials are encrypted in the health check configuration
     *
     * @dataProvider dataProviderGetHlthCfgCredentialsEncrypted()
     *
     * @param array $csCredentialsEncrypted
     * @param array $expectedResult
     *
     * @return void
     *
     */
    public function testGetHlthCfgCredentialsEncrypted(array $csCredentialsEncrypted, array $expectedResult) : void
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
        $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setStatus', 'setDetail',
            ])
            ->getMock();
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $healthCheckResultMock->expects(self::never())->method('setStatus');
        $healthCheckResultMock->expects(self::never())->method('setDetail');
        
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getValueForHLTHCFGPropertyWrapper', 'getTwoWayDecryptWithKeyWrapper', 'getHealthCheckResult',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getValueForHLTHCFGPropertyWrapper')
            ->with('HEALTHCHECK_CS_CREDENTIALS')
            ->willReturn($csCredentialsEncrypted);
        $cloudStorageHealthCheckMock
            ->expects(self::exactly($this->countCredentials))
            ->method('getTwoWayDecryptWithKeyWrapper')
            ->willReturn('fooBar-cloud-storage-target@fooBar-company@fooBar-user');
        
        $hlthCfgCredentialsResult = $this->invokePrivate($cloudStorageHealthCheckMock, 'getHlthCfgCredentials');
        self::assertSame($expectedResult, $hlthCfgCredentialsResult);
    }
    
    /**
     * @return array dataProviderGetHlthCfgCredentials
     */
    public function dataProviderGetHlthCfgCredentialsMixed() : array
    {
        return [
            [
                $this->mixedCredentials,
                $this->decCredentials,
            ],
        ];
    }
    /**
     * only credential 'HEALTHCHECK_CS_BOX' is unencrypted,
     * the rest (HEALTHCHECK_CS_AWS,..) are encrypted in health check configuration, the file ia_init.local.cfg
     * the healthcheck detects the unencrypted credential and reports an error and suggest the encrypted 'HEALTHCHECK_CS_BOX'
     *
     * @dataProvider dataProviderGetHlthCfgCredentialsMixed()
     *
     * @param array $csCredentialsMixed
     * @param array $expectedResult
     *
     * @return void
     *
     */
    public function testGetHlthCfgCredentialsMixed(array $csCredentialsMixed, array $expectedResult) : void
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
       $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setStatus', 'setDetail',
            ])
            ->getMock();
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('setStatus')
            ->with(" Encryption issue CloudStorage");
        $key = 'HEALTHCHECK_CS_CREDENTIALS CloudStorage ';
        $msg = "HEALTHCHECK_CS_CREDENTIALS, key "
               . 'HEALTHCHECK_CS_BOX' . ", contains an unencrypted value!"
               ."\n Use instead the outcome of TwoWayEncryptWithKey as value:\n"
               . 'KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=';
        ;
        $healthCheckResultMock
            ->expects(self::once())
            ->method('setDetail')
            ->with(
                $this->callback(function ($value) {
                    self::assertStringContainsString('HEALTHCHECK_CS_CREDENTIALS', $value);
                    self::assertStringContainsString('Use instead the outcome of TwoWayEncryptWithKey as value:', $value);
                    return true;
                })
        );
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setCsErrors', 'getValueForHLTHCFGPropertyWrapper', 'getTwoWayEncryptWithKeyWrapper', 'getTwoWayDecryptWithKeyWrapper', 'getHealthCheckResult',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getValueForHLTHCFGPropertyWrapper')
            ->with('HEALTHCHECK_CS_CREDENTIALS')
            ->willReturn($csCredentialsMixed);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly($this->countCredentials))
            ->method('getTwoWayDecryptWithKeyWrapper')
            ->willReturn('fooBar-cloud-storage-target@fooBar-company@fooBar-user');
            
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getTwoWayEncryptWithKeyWrapper')
            ->with('fooBar-cloud-storage-target@fooBar-company@fooBar-user')
            ->willReturn('KEY_DEV_SECRETS:VSyBjU/58UnzkD5+o3Sc/IWNNe/u42iaai8H5T39U9jtSOj982SEYhWLw+3zSoDqWZ9c9a0is6c=');
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('setCsErrors')
            ->with($key, $msg);
        
        $hlthCfgCredentialsResult = $this->invokePrivate($cloudStorageHealthCheckMock, 'getHlthCfgCredentials');
        self::assertSame($expectedResult, $hlthCfgCredentialsResult);
    }
    
    /**
     * failed to find HEALTHCHECK_CS_CREDENTIALS in health check configuration, check the file ia_init.local.cfg
     * @return void
     */
    public function testDoCheckErrorEmptyHlthCfgCredentials() : void
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
        $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setStatus', 'setDetail', 'isStatusAtLeast',
            ])
            ->getMock();
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('setStatus')
            ->with("Cloud storages credentials not found");
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('setDetail')
            ->with("config healthcheck: failed to find HEALTHCHECK_CS_CREDENTIALS.");
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('isStatusAtLeast')
            ->with(HealthCheckResult::STATUS_ERROR)
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setCsErrors', 'getHealthCheckResult', 'recordResourceErrorWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('recordResourceErrorWrapper')
            ->with('HEALTHCHECK_CS_CREDENTIALS', "CloudStorageHealthCheck credentials error", HealthCheckResult::STATUS_ERROR);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', []);
        
        $this->invokePrivate($cloudStorageHealthCheckMock, 'doCheck', []);
    }
    
    /**
     * test the call of the health check's entrypoint with valid values of parameter rid, e.g: &rid=Box,Aws,OneDrive,AzureStorage
     * ia-app/hlth/health.phtml?hc=CloudStorage&rid=Box,Aws,OneDrive,AzureStorage&log=1
     * @return void
     */
    public function testDoCheckWithoutErrorsForParamRidStorage()
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
        $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setStatus', 'setDetail', 'isStatusAtLeast',
            ])
            ->getMock();
        
        $healthCheckResultMock
            ->expects(self::exactly(7))
            ->method('setDetail')
            ->withConsecutive(
                ['CloudStorage Box ok'],
                ['CloudStorage Aws ok'],
                ['CloudStorage OneDrive ok'],
                ['CloudStorage AzureStorage ok'],
                ['CloudStorage GoogleDrive ok'],
                ['CloudStorage Dropbox ok'],
                ['CloudStorage HTTP ok'],
            );
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('isStatusAtLeast')
            ->with(HealthCheckResult::STATUS_ERROR)
            ->willReturn(false);
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('setStatus')
            ->with("CloudStorage HealthCheck ok");
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getHealthCheckResult', 'checkBox', 'checkAws',
                'checkOneDrive', 'checkAzureStorage', 'checkGoogleDrive', 'checkDropbox', 'checkHTTP'
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('checkBox')
            ->willReturn(false);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('checkAws')
            ->willReturn(false);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('checkOneDrive')
            ->willReturn(false);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('checkAzureStorage')
            ->willReturn(false);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('checkGoogleDrive')
            ->willReturn(false);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('checkDropbox')
            ->willReturn(false);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('checkHTTP')
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        
        $this->invokePrivate($cloudStorageHealthCheckMock, 'doCheck', [
            'Box'=>0,
            'Aws'=>1,
            'OneDrive'=>2,
            'AzureStorage'=>3,
            'GoogleDrive'=>4,
            'Dropbox'=>5,
            'HTTP'=>6
        ]);
    }
    
    
    /**
     * test the call of the entrypoint URL with an UNKNOWN value of parameter rid, e.g.:
     * .ia-app/hlth/health.phtml?hc=CloudStorage&rid=UNKNOWN&log=1
     * @return void
     */
    public function testDoCheckWithErrorsForUnknownHealthcheck()
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
        $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'isStatusAtLeast', 'setFinalResult',
            ])
            ->getMock();
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('isStatusAtLeast')
            ->with(HealthCheckResult::STATUS_ERROR)
            ->willReturn(true);
        
        $healthCheckResultMock
            ->expects(self::exactly(1))
            ->method('setFinalResult')
            ->with(HealthCheckResult::STATUS_ERROR);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getHealthCheckResult', 'recordResourceErrorWrapper', 'setCsErrors',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('setCsErrors')
            ->with("UNKNOWN", "Healthcheck for 'UNKNOWN' is not implemented.");
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('recordResourceErrorWrapper')
            ->with('CloudStorage', "HealthCheck error", HealthCheckResult::STATUS_ERROR);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        
        $this->invokePrivate($cloudStorageHealthCheckMock, 'doCheck', []);
    }
    
    
    /**
     * @return array[] dataPoviderDoCheckUnimplemented
     */
    public function dataPoviderDoCheckUnimplemented() : array
    {
        return [
            ['UNIMPLEMENTED']
        ];
    }
    /**
     * @dataProvider dataPoviderDoCheckUnimplemented
     * test call of the entrypoint URL with an unimplemented (yet) cloud storage as value of the parameter rid, e.g.:
     * where the following health checks are not implemented yet: UNIMPLEMENTED
     * .ia-app/hlth/health.phtml?hc=CloudStorage&rid=UNIMPLEMENTED&log=1
     *
     * @return void
     */
    public function testDoCheckUnimplemented($csName) : void
    {
        /** @var MockObject|HealthCheckResult $healthCheckResultMock */
        $healthCheckResultMock = $this->getMockBuilder(HealthCheckResult::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
               'setStatus', 'setDetail', 'setInfo', 'mergeDetails', 'isStatusAtLeast', 'setFinalResult',
            ])
            ->getMock();
        
        $healthCheckResultMock
            ->expects(self::exactly(1))
            ->method('setStatus')
            ->with($csName);
        
        $healthCheckResultMock
            ->expects(self::exactly(1))
            ->method('setDetail')
            ->with('CloudStorage '.$csName.' error');
        
        $healthCheckResultMock
            ->expects(self::exactly(1))
            ->method('mergeDetails')
            ->with(["Healthcheck for '$csName' is not implemented."]);
        
        $healthCheckResultMock
            ->expects(self::exactly(1))
            ->method('setInfo')
            ->with('todo '.$csName);
        
        $healthCheckResultMock
            ->expects(self::once())
            ->method('isStatusAtLeast')
            ->with(HealthCheckResult::STATUS_ERROR)
            ->willReturn(true);
        
        $healthCheckResultMock
            ->expects(self::exactly(1))
            ->method('setFinalResult')
            ->with(HealthCheckResult::STATUS_ERROR);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getHealthCheckResult', 'recordResourceErrorWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getHealthCheckResult')
            ->willReturn($healthCheckResultMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('recordResourceErrorWrapper')
            ->with('CloudStorage', "HealthCheck error", HealthCheckResult::STATUS_ERROR);
        
        $tmp["todo $csName"][0] = "Healthcheck for '$csName' is not implemented.";
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'csErrors', $tmp);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        
        $this->invokePrivate($cloudStorageHealthCheckMock, 'doCheck', [$csName => 0]);
        
    }
    
    /**
     * @return array[] dataPoviderCheckCloudStorage
     */
     public function dataPoviderCheckCloudStorage() : array
     {
         return $this->arrDataProviderCheck;
     }
     
     /**
      * check cloud storage successfully
      * @dataProvider dataPoviderCheckCloudStorage
      * @param string $checkMethod
      * @param string $service
      * @param string $iaCsCredentialKey
      * check success
      *
      * @return void
      */
    public function testCheckCloudStorageSuccess(string $checkMethod, string $service, string $iaCsCredentialKey) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkDeliverFiles',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('checkDeliverFiles')
            ->with($service, $this->decCredentials[$iaCsCredentialKey])
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, success
        self::assertFalse($this->invokePrivate($cloudStorageHealthCheckMock, $checkMethod));
    }
    
    
    /**
     * check cloud storage fails deliver files
     * @dataProvider dataPoviderCheckCloudStorage
     *
     * @param string $checkMethod
     * @param string $service
     * @param string $iaCsCredentialKey
     * @return void
     */
    public function testCheckCloudStorageFail(string $checkMethod, string $service, string $iaCsCredentialKey) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkDeliverFiles',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('checkDeliverFiles')
            ->with($service, $this->decCredentials[$iaCsCredentialKey])
            ->willReturn(true);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        //there is an error
        self::assertTrue($this->invokePrivate($cloudStorageHealthCheckMock, $checkMethod));
    }
    
    
    /**
     * check fails because credentials' key does not exist in the configuration ia_init.local.cfg, section [HEALTHCHECK_CS_CREDENTIALS]
     * @dataProvider dataPoviderCheckCloudStorage
     *
     * @param string $checkMethod
     * @param string $service
     * @param string $iaCsCredentialKey
     *
     * @return void
     */
    public function testCheckCloudStorageFailCredentialKeyDoesNotExists(string $checkMethod, string $service, string $iaCsCredentialKey) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setCsErrors',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('setCsErrors')
            ->with($iaCsCredentialKey, "config healthcheck ". $service.": failed to find key ".$iaCsCredentialKey.".");
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', []);
        //there is an error
        self::assertTrue($this->invokePrivate($cloudStorageHealthCheckMock, $checkMethod));
    }
    
    /**
     * check fails because credentials' values are empty in the ia_init.local.cfg configuration, section [HEALTHCHECK_CS_CREDENTIALS]
     * @dataProvider dataPoviderCheckCloudStorage
     *
     * @param string $checkMethod
     * @param string $service
     * @param string $iaCsCredentialKey
     *
     * @return void
     */
    public function testCheckCloudStorageFailCredentialWithoutValue(string $checkMethod, string $service, string $iaCsCredentialKey) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setCsErrors',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('setCsErrors')
            ->with($iaCsCredentialKey, "config healthcheck ". $service.": failed to find value for ".$iaCsCredentialKey.".");
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', [$iaCsCredentialKey => '']);
        //there is an error
        self::assertTrue($this->invokePrivate($cloudStorageHealthCheckMock, $checkMethod));
    }
    
    /**
     * test check cloud storage fails with IAException for checkDeliverFiles
     * @dataProvider dataPoviderCheckCloudStorage
     * @param string $checkMethod
     * @param string $service
     * @param string $iaCsCredentialKey
     *
     * @return void
     */
    public function testCheckCloudStorageFailWithExceptionDeliverFiles(string $checkMethod, string $service, string $iaCsCredentialKey) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkDeliverFiles', 'setCsErrors',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('checkDeliverFiles')
            ->with($service, $this->decCredentials[$iaCsCredentialKey])
            ->willThrowException(new IAException('fooBar'));
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('setCsErrors')
            ->with('check' . $service . ' exception',
                'core exception ' . $service . ': fooBar');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        //there is an error
        self::assertTrue($this->invokePrivate($cloudStorageHealthCheckMock, $checkMethod));
    }
    
    /**
     * test checkDeliverFiles to Aws S3 storage with success
     * @return void
     * @throws \ReflectionException
     */
    public function testcheckDeliverFilesToAwsWithSuccess() : void
    {   // mock response from DB for Cloud storage information
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"Aws","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $path = $objBoxArr['PATH'];
        $filePrefix = "healthcheck".$service;
        $fileName = "healthcheck".$service.'fooBar-key';
        $filesReturn = [$filePrefix.'_timeAndUniqueId.txt' => '/webdirs/import/fooBar'];
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles'
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with($filesReturn,'fooBar-cloud-storage-target')
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'getObjectFullPath',
                'setFileOnDrive',
                'getDeliveryServiceHandlerErrors',
                'getDeliveryServiceHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        /** @var MockObject|AwsHandler $awsHandlerMock */
        $awsHandlerMock = $this->getMockBuilder(\AwsHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkUploadedFile',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceHandlerWrapper')
            ->with($service)
            ->willReturn($awsHandlerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, 'healthcheckAws');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', $filesReturn);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getObjectFullPath')
            ->with($service, $path)
            ->willReturn($path.'/');
        
        $awsHandlerMock
            ->expects(self::exactly(1))
            ->method('checkUploadedFile')
            ->with('fooBar-cloud-storage-target', $path.'/', 1, 1)
            ->willReturn(0);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceHandlerErrors')
            ->willReturn(false);
    
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, it should return bError false
        self::assertFalse(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                $service,
                $this->decCredentials['HEALTHCHECK_CS_AWS'])
        );
    }
    
    /**
     * test checkDeliverFiles to Azure storage with success
     * @return void
     * @throws \ReflectionException
     */
    public function testcheckDeliverFilesToAzureStorageWithSuccess() : void
    {   // mock response from DB for Cloud storage information
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"AzureStorage","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $path = $objBoxArr['PATH'];
        $fileName = "healthcheck".$service.'_timeAndUniqueId.txt';
        $filesReturn = [$fileName => '/webdirs/import/fooBar'];
        $addFileNameToPath = $path.'/'.$fileName;
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles'
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with($filesReturn,'fooBar-cloud-storage-target')
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'getObjectFullPath',
                'setFileOnDrive',
                'getDeliveryServiceHandlerErrors',
                'getDeliveryServiceHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        /** @var MockObject|AzureHandler $azureHandlerMock */
        $azureHandlerMock = $this->getMockBuilder(\AzureHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkUploadedFile',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceHandlerWrapper')
            ->with($service)
            ->willReturn($azureHandlerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, 'healthcheckAzureStorage');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', $filesReturn);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getObjectFullPath')
            ->with($service, $path)
            ->willReturn($addFileNameToPath);
        
        $azureHandlerMock
            ->expects(self::exactly(1))
            ->method('checkUploadedFile')
            ->with('fooBar-cloud-storage-target', $addFileNameToPath, 1, 1)
            ->willReturn(0);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceHandlerErrors')
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, it should return bError false
        self::assertFalse(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                $service,
                $this->decCredentials['HEALTHCHECK_CS_AZURESTORAGE'])
        );
    }
    
    /**
     * test checkDeliverFiles to Box storage with success
     * @return void
     * @throws \ReflectionException
     */
    public function testcheckDeliverFilesToBoxWithSuccess() : void
    {   // mock response from DB for Cloud storage information
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"Box","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $path = $objBoxArr['PATH'];
        $filePrefix = "healthcheck".$service;
        $fileName = "healthcheck".$service.'fooBar-key';
        $filesReturn = [$filePrefix.'_timeAndUniqueId.txt' => '/webdirs/import/fooBar'];
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles'
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with($filesReturn,'fooBar-cloud-storage-target')
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'getObjectFullPath',
                'setFileOnDrive',
                'getDeliveryServiceHandlerErrors',
                'getDeliveryServiceHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        /** @var MockObject|BoxHandler $boxHandlerMock */
        $boxHandlerMock = $this->getMockBuilder(\BoxHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkUploadedFile',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceHandlerWrapper')
            ->with($service)
            ->willReturn($boxHandlerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, 'healthcheckBox');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', $filesReturn);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getObjectFullPath')
            ->with($service, $path)
            ->willReturn($path.'/');
        
        $boxHandlerMock
            ->expects(self::exactly(1))
            ->method('checkUploadedFile')
            ->with('fooBar-cloud-storage-target', $path.'/', 1, 1)
            ->willReturn(0);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceHandlerErrors')
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, it should return bError false
        self::assertFalse(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                $service,
                $this->decCredentials['HEALTHCHECK_CS_BOX'])
        );
    }
    
    /**
     * test checkDeliverFiles to OneDrive storage with success
     * @return void
     * @throws \ReflectionException
     */
    public function testcheckDeliverFilesToOnedriveWithSuccess() : void
    {   // mock response from DB for Cloud storage information
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"OneDrive","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $path = $objBoxArr['PATH'];
        $fileName = "healthcheck".$service.'_timeAndUniqueId.txt';
        $filesReturn = [$fileName => '/webdirs/import/fooBar'];
        $addFileNameToPath = $path.'/'.$fileName;
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles'
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with($filesReturn,'fooBar-cloud-storage-target')
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'getObjectFullPath',
                'setFileOnDrive',
                'getDeliveryServiceHandlerErrors',
                'getDeliveryServiceHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        /** @var MockObject|OneDriveHandler $odHandlerMock */
        $odHandlerMock = $this->getMockBuilder(\OneDriveHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkUploadedFile',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceHandlerWrapper')
            ->with($service)
            ->willReturn($odHandlerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, 'healthcheckOneDrive');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', $filesReturn);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getObjectFullPath')
            ->with($service, $path)
            ->willReturn($addFileNameToPath);
        
        $odHandlerMock
            ->expects(self::exactly(1))
            ->method('checkUploadedFile')
            ->with('fooBar-cloud-storage-target', $addFileNameToPath, 1, 1)
            ->willReturn(0);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceHandlerErrors')
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, it should return bError false
        self::assertFalse(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                $service,
                $this->decCredentials['HEALTHCHECK_CS_ONEDRIVE'])
        );
    }
    
    /**
     * test checkDeliverFiles to GoogleDrive storage with success
     * @return void
     * @throws \ReflectionException
     */
    public function testcheckDeliverFilesToGoogleDriveWithSuccess() : void
    {   // mock response from DB for Cloud storage information
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"GoogleDrive","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $path = $objBoxArr['PATH'];
        $fileName = "healthcheck".$service.'_timeAndUniqueId.txt';
        $filesReturn = [$fileName => '/webdirs/import/fooBar'];
        $addFileNameToPath = $path.'/'.$fileName;
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles'
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with($filesReturn,'fooBar-cloud-storage-target')
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'getObjectFullPath',
                'setFileOnDrive',
                'getDeliveryServiceHandlerErrors',
                'getDeliveryServiceHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        /** @var MockObject|GoogleDriveHandler $odHandlerMock */
        $gdHandlerMock = $this->getMockBuilder(\GoogleDriveHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkUploadedFile',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceHandlerWrapper')
            ->with($service)
            ->willReturn($gdHandlerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, 'healthcheckGoogleDrive');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', $filesReturn);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getObjectFullPath')
            ->with($service, $path)
            ->willReturn($addFileNameToPath);
        
        $gdHandlerMock
            ->expects(self::exactly(1))
            ->method('checkUploadedFile')
            ->with('fooBar-cloud-storage-target', $addFileNameToPath, 1, 1)
            ->willReturn(0);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceHandlerErrors')
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, it should return bError false
        self::assertFalse(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                'GoogleDrive',
                $this->decCredentials['HEALTHCHECK_CS_GOOGLEDRIVE'])
        );
    }
    
    /**
     * test checkDeliverFiles to Dropbox storage with success
     * @return void
     * @throws \ReflectionException
     */
    public function testcheckDeliverFilesToDropboxWithSuccess() : void
    {   // mock response from DB for Cloud storage information
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"Dropbox","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $path = $objBoxArr['PATH'];
        $fileName = "healthcheck".$service.'_timeAndUniqueId.txt';
        $filesReturn = [$fileName => '/webdirs/import/fooBar'];
        $addFileNameToPath = $path.'/'.$fileName;
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles'
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with($filesReturn,'fooBar-cloud-storage-target')
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'getObjectFullPath',
                'setFileOnDrive',
                'getDeliveryServiceHandlerErrors',
                'getDeliveryServiceHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        /** @var MockObject|DropBoxHandler $odHandlerMock */
        $dbHandlerMock = $this->getMockBuilder(\DropBoxHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkUploadedFile',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceHandlerWrapper')
            ->with($service)
            ->willReturn($dbHandlerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, 'healthcheckDropbox');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', $filesReturn);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getObjectFullPath')
            ->with($service, $path)
            ->willReturn($addFileNameToPath);
        
        $dbHandlerMock
            ->expects(self::exactly(1))
            ->method('checkUploadedFile')
            ->with('fooBar-cloud-storage-target', $addFileNameToPath, 1, 1)
            ->willReturn(0);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceHandlerErrors')
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, it should return bError false
        self::assertFalse(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                'Dropbox',
                $this->decCredentials['HEALTHCHECK_CS_DROPBOX'])
        );
    }
    
    /**
     * test checkDeliverFiles to Aws S3 storage with success
     * @return void
     * @throws \ReflectionException
     */
    public function testcheckDeliverFilesToHTTPWithSuccess() : void
    {   // mock response from DB for Cloud storage information
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"HTTP","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $path = $objBoxArr['PATH'];
        $filePrefix = "healthcheck".$service;
        $fileName = "healthcheck".$service.'fooBar-key';
        $filesReturn = [$filePrefix.'_timeAndUniqueId.txt' => '/webdirs/import/fooBar'];
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles'
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with($filesReturn,'fooBar-cloud-storage-target')
            ->willReturn(true);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'getObjectFullPath',
                'setFileOnDrive',
                'getDeliveryServiceHandlerErrors',
                'getDeliveryServiceHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        /** @var MockObject|HTTPHandler $httpHandlerMock */
        $httpHandlerMock = $this->getMockBuilder(\HTTPHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'checkUploadedFile',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getDeliveryServiceHandlerWrapper')
            ->with($service)
            ->willReturn($httpHandlerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, 'healthcheckHTTP');
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', $filesReturn);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getObjectFullPath')
            ->with($service, $path)
            ->willReturn($path.'/');
        
        $httpHandlerMock
            ->expects(self::exactly(1))
            ->method('checkUploadedFile')
            ->with('fooBar-cloud-storage-target', $path.'/', 1, 1)
            ->willReturn(0);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceHandlerErrors')
            ->willReturn(false);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // no error, it should return bError false
        self::assertFalse(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                $service,
                $this->decCredentials['HEALTHCHECK_CS_HTTP'])
        );
    }
    
    /**
     * @return array dataProviderCheckDeliverFilesFailGetHandlerWrapper
     */
    public function dataProviderCheckDeliverFilesFailGetHandlerWrapper(): array
    {
        return [
            ['Aws', $this->decCredentials['HEALTHCHECK_CS_AWS']],
            ['AzureStorage', $this->decCredentials['HEALTHCHECK_CS_AZURESTORAGE']],
            ['Box', $this->decCredentials['HEALTHCHECK_CS_BOX']],
            ['OneDrive', $this->decCredentials['HEALTHCHECK_CS_ONEDRIVE']],
            ['HTTP', $this->decCredentials['HEALTHCHECK_CS_HTTP']]
        ];
    }
    /**
     * test checkDeliverFiles, it fails at getHandlerWrapper
     * @dataProvider dataProviderCheckDeliverFilesFailGetHandlerWrapper
     * @param string $service
     * @param string $decryptedCredential
     * @return void
     * @throws \ReflectionException
     */
    public function testCheckDeliverFilesFailGetHandlerWrapper(string $service, string $decryptedCredential) : void
    {
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn([]);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        // has error, it should return bError true
        self::assertTrue(
            $this->invokePrivate($cloudStorageHealthCheckMock, 'checkDeliverFiles', $service, $decryptedCredential)
        );
    }
    
    /**
     * @return array CheckDeliverFilesFailDeliveryServiceManagerDeliverFiles
     */
    public function dataProviderCheckDeliverFilesFailDeliveryServiceManagerDeliverFiles(): array
    {
        return [
            ['Aws', $this->decCredentials['HEALTHCHECK_CS_AWS']],
            ['AzureStorage', $this->decCredentials['HEALTHCHECK_CS_AZURESTORAGE']],
            ['Box', $this->decCredentials['HEALTHCHECK_CS_BOX']],
            ['OneDrive', $this->decCredentials['HEALTHCHECK_CS_ONEDRIVE']],
            ['HTTP', $this->decCredentials['HEALTHCHECK_CS_HTTP']]
        ];
    }
    /**
     * test checkDeliverFiles, it fails at DeliveryServiceManager's deliverFiles
     * @dataProvider dataProviderCheckDeliverFilesFailDeliveryServiceManagerDeliverFiles
     * @return void
     * @throws \ReflectionException
     */
    public function testCheckDeliverFilesFailDeliveryServiceManagerDeliverFiles(string $csApiName, string $hlthCredential) : void
    {
        $objBoxArr = json_decode(
            '{"RECORDNO":"123","STATUS":"active","TYPE":"'.$csApiName.'","NAME":"fooBar-cloud-storage-target","PATH":"fooBar-path"}',
            true
        );
        $service = $objBoxArr['TYPE'];
        $filePrefix = "healthcheck".$service;
        $fileName = "healthcheck".$service.'fooBar-key';
        $fileContent = $filePrefix . " test";
        $csName = $objBoxArr['NAME'];
        
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'deliverFiles',
            ])
            ->getMock();
        
        $deliveryServiceManagerMock
            ->expects(self::once())
            ->method('deliverFiles')
            ->with([$fileName=>$fileContent],'fooBar-cloud-storage-target')
            ->willReturn(false);
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getDeliveryServiceManagerWrapper',
                'getHandlerWrapper',
                'setFileOnDrive',
                'setCsErrors',
                'getGlobalErrorsWrapper',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getDeliveryServiceManagerWrapper')
            ->willReturn($deliveryServiceManagerMock);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('getHandlerWrapper')
            ->with($deliveryServiceManagerMock, 'fooBar-cloud-storage-target')
            ->willReturn($objBoxArr);
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('setFileOnDrive')
            ->with($deliveryServiceManagerMock, $filePrefix);
        
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileName', $fileName);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileOnDrive', [$fileName=>$fileContent]);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'fileSize', 1);
        self::setPrivateProperty($cloudStorageHealthCheckMock, 'hlthCfgCredentials', $this->decCredentials);
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('setCsErrors')
            ->with('checkDeliverFiles' . $service . ' error', "Issue to deliverFiles for $service target $csName");
        
        $cloudStorageHealthCheckMock
            ->expects(self::once())
            ->method('getGlobalErrorsWrapper')
            ->willReturn(true);
        // has error, it should return bError true
        self::assertTrue(
            $this->invokePrivate($cloudStorageHealthCheckMock,
                'checkDeliverFiles',
                $service,
                $hlthCredential)
        );
    }
    
    /**
     * @return array dataProviderGetDeliveryServiceHandlerWithErrors
     */
    public function dataProviderGetDeliveryServiceHandlerWithErrors(): array
    {
        return [
            [1,'fooBar', true],
            [2,'fooBar', true],
            [3,'fooBar', true],
            [4,'fooBar', true],
            [5,'fooBar', true],
            [6,'fooBar', true],
            [7,'fooBar', true],
            [8,'fooBar', true],
            [9,'fooBar', true],
            [123,'fooBar', true],
        ];
    }
    
    /**
     * test DeliveryServiceHandler with errors
     * @dataProvider dataProviderGetDeliveryServiceHandlerWithErrors()
     * @param int $errorCode
     * @param string $keyName
     * @param bool $expectedResult
     * @return void
     * @throws \Exception
     *
     */
    public function testGetDeliveryServiceHandlerWithErrors(int $errorCode, string $keyName, bool $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setCsErrors',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::exactly(1))
            ->method('setCsErrors')
            ->with(
                $this->callback(function ($key) {
                    self::assertStringContainsString('DeliveryServiceHandler error', $key);
                    return true;
                }));
           
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'getDeliveryServiceHandlerErrors', $errorCode, $keyName)
        );
    }
    
    /**
     * test DeliveryServiceHandler with no errors
     * @return void
     * @throws \Exception
     *
     */
    public function testGetDeliveryServiceHandlerNoErrors() : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'setCsErrors',
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock
            ->expects(self::never())
            ->method('setCsErrors');
        
        self::assertSame(
            false,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'getDeliveryServiceHandlerErrors', 0, 'fooBar')
        );
    }
    /**
     * @return array dataProviderGetObjectFullPath
     */
    public function dataProviderGetObjectFullPath(): array
    {
        return [
            ['Aws','', ''],
            ['Aws','/', ''],
            ['Aws','/fooBar', 'fooBar/'],
            ['Aws','fooBar/', 'fooBar/'],
            ['Aws','/foo Bar', 'foo Bar/'],
            ['Box','', ''],
            ['Box','/', ''],
            ['Box','/fooBar', 'fooBar/'],
            ['Box','fooBar/', 'fooBar/'],
            ['Box','/foo Bar', 'foo Bar/'],
            ['OneDrive','', ''],
            ['OneDrive','/', ''],
            ['OneDrive','/fooBar', 'fooBar/'],
            ['OneDrive','fooBar/', 'fooBar/'],
            ['OneDrive','/foo Bar', 'foo Bar/'],
            ['GoogleDrive','', ''],
            ['GoogleDrive','/', ''],
            ['GoogleDrive','/fooBar', 'fooBar/'],
            ['GoogleDrive','fooBar/', 'fooBar/'],
            ['GoogleDrive','/foo Bar', 'foo Bar/'],
            ['Dropbox','', '/'],
            ['Dropbox','/', '/'],
            ['Dropbox','/fooBar', '/fooBar/'],
            ['Dropbox','fooBar/', '/fooBar/'],
            ['Dropbox','/foo Bar', '/foo Bar/'],
            ['HTTP','', ''],
            ['HTTP','/', ''],
            ['HTTP','/fooBar', 'fooBar/'],
            ['HTTP','fooBar/', 'fooBar/'],
            ['HTTP','/foo Bar', 'foo Bar/']
        ];
    }
    
    /**
     * test getObjectFullPath
     * @dataProvider dataProviderGetObjectFullPath()
     *
     * @param string $service
     * @param string $objPath
     * @param string $expectedResult
     *
     * @return void
     */
    public function testGetObjectFullPath (string $service, string $objPath, string $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'getObjectFullPath', $service, $objPath)
        );
    
    }
    
    /**
     * @return array dataProviderNormalizePath
     */
    public function dataProviderNormalizePath(): array
    {
        return [
            ['', ''],
            ['/', ''],
            ['fooBar', 'fooBar/'],
            ['/fooBar', 'fooBar/'],
            ['fooBar/test', 'fooBar/test/'],
            ['/fooBar/test', 'fooBar/test/'],
            ['/fooBar/test/', 'fooBar/test/'],
            ['/foo Bar', 'foo Bar/'],
            ['/foo Bar', 'foo Bar/'],
        ];
    }
    
    /**
     * @return array dataProviderNormalizePathOneAndGoogleDrive
     */
    public function dataProviderNormalizePathOneAndGoogleDrive(): array
    {
        return [
            ['', ''],
            ['/', ''],
            ['fooBar', 'fooBar'],
            ['/fooBar', 'fooBar'],
            ['fooBar/test', 'fooBar/test'],
            ['/fooBar/test', 'fooBar/test'],
            ['/fooBar/test/', 'fooBar/test/'],
            ['/foo Bar', 'foo Bar'],
            ['/foo Bar', 'foo Bar'],
        ];
    }
    
    /**
     * test normalizePath for checkAws
     * @dataProvider dataProviderNormalizePath()
     *
     * @param string $objPath
     * @param string $expectedResult
     *
     * @return void
     */
    public function testNormalizePathForCheckAws (string $objPath, string $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'normalizePathAws', $objPath)
        );
    }
    /**
     * test normalizePath for checkBox
     * @dataProvider dataProviderNormalizePath()
     *
     * @param string $objPath
     * @param string $expectedResult
     *
     * @return void
     */
    public function testNormalizePathForCheckBox (string $objPath, string $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'normalizePathForCheckBox', $objPath)
        );
    }
    /**
     * test normalizePath for checkOneDrive
     * @dataProvider dataProviderNormalizePathOneAndGoogleDrive
     *
     * @param string $objPath
     * @param string $expectedResult
     *
     * @return void
     */
    public function testNormalizePathForCheckOneDrive (string $objPath, string $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'normalizePathForCheckOneDrive', $objPath)
        );
    }
    /**
     * test normalizePath for checkGoogleDrive
     * @dataProvider dataProviderNormalizePathOneAndGoogleDrive
     *
     * @param string $objPath
     * @param string $expectedResult
     *
     * @return void
     */
    public function testNormalizePathForCheckGoogleDrive (string $objPath, string $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'normalizePathForGoogleDrive', $objPath)
        );
    }
    
    /**
     * @return array dataProviderNormalizePathForDropbox
     */
    public function dataProviderNormalizePathForDropbox(): array
    {
        return [
            ['', '/'],
            ['/', '/'],
            ['fooBar', '/fooBar/'],
            ['/fooBar', '/fooBar/'],
            ['fooBar/test', '/fooBar/test/'],
            ['/fooBar/test', '/fooBar/test/'],
            ['/fooBar/test/', '/fooBar/test/'],
            ['/foo Bar', '/foo Bar/'],
            ['/foo Bar', '/foo Bar/'],
        ];
    }
    /**
     * test normalizePath for checkDropbox
     * @dataProvider dataProviderNormalizePathForDropbox
     *
     * @param string $objPath
     * @param string $expectedResult
     *
     * @return void
     */
    public function testNormalizePathForCheckDropbox (string $objPath, string $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'normalizePathForDropbox', $objPath)
        );
    }
    
    /**
     * test normalizePath for checkHTTP
     * @dataProvider dataProviderNormalizePath()
     *
     * @param string $objPath
     * @param string $expectedResult
     *
     * @return void
     */
    public function testNormalizePathForCheckHTTP (string $objPath, string $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'normalizePathForHTTP', $objPath)
        );
    }
    
    /**
     * @return array dataProviderSetFileOnDrive
     */
    public function dataProviderSetFileOnDrive() : array
    {
        return [
            ['', ['_timeAndUniqueId.txt' => '/webdirs/import/']],
            ['fooBar', ['fooBar_timeAndUniqueId.txt' => '/webdirs/import/fooBar']],
            ['foo Bar', ['foo Bar_timeAndUniqueId.txt' => '/webdirs/import/foo Bar']],
        ];
    }
    /**
     * test set (DUMMY) file on drive for healthcheck; it is a wrapper for DeliveryServiceManager's createLocalFilesFromData
     * @dataProvider dataProviderSetFileOnDrive
     * @param string $filePrefix
     * @param array $filesReturn
     * @return void
     */
    public function testSetFileOnDrive(string $filePrefix, array $filesReturn) : void
    {
        /** @var MockObject|DeliveryServiceManager $deliveryServiceManagerMock */
        $deliveryServiceManagerMock = $this->getMockBuilder(\DeliveryServiceManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'createLocalFilesFromData',
            ])
            ->getMock();
        
        $deliveryServiceManagerMock->expects(self::once())
            ->method('createLocalFilesFromData')
            ->with(
                $this->callback(function ($value) use ($filePrefix) {
                    self::assertStringContainsString(
                        $filePrefix.'_',
                        key($value)
                    );
                    return true;
                })
            )
            ->willReturn($filesReturn);
        
        
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(\CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getFileSizeWrapper', 'getFileNameWrapper'
            ])
            ->getMock();
        
        $cloudStorageHealthCheckMock->expects(self::once())
            ->method('getFileSizeWrapper')
            ->with(current($filesReturn))
            ->willReturn(1);
        
        $cloudStorageHealthCheckMock->expects(self::once())
            ->method('getFileNameWrapper')
            ->with($filePrefix)
            ->willReturn($filePrefix.'_timeAndUniqueId.txt');
        
        $this->invokePrivate($cloudStorageHealthCheckMock,'setFileOnDrive', $deliveryServiceManagerMock, $filePrefix);
    }
    
    /**
     * @return array dataProviderAddFileNameToNormalizePath
     */
    public function dataProviderAddFileNameToNormalizePath() : array
    {
        return [
            ['', 'healthcheckABC_timeAndUniqueId.txt', 'healthcheckABC_timeAndUniqueId.txt'],
            ['/', 'healthcheckABC_timeAndUniqueId.txt', '/healthcheckABC_timeAndUniqueId.txt'],
            ['fooBar', 'healthcheckABC_timeAndUniqueId.txt', 'fooBar/healthcheckABC_timeAndUniqueId.txt'],
            ['fooBar/', 'healthcheckABC_timeAndUniqueId.txt', 'fooBar/healthcheckABC_timeAndUniqueId.txt'],
            ['foo Bar/', 'healthcheckABC_timeAndUniqueId.txt', 'foo Bar/healthcheckABC_timeAndUniqueId.txt'],
            ['fooBar/', 'healthcheckABC_timeAndUniqueId.txt', 'fooBar/healthcheckABC_timeAndUniqueId.txt'],
            ['fooBar/test/', 'healthcheckABC_timeAndUniqueId.txt', 'fooBar/test/healthcheckABC_timeAndUniqueId.txt'],
            
        ];
    }
    /**
     * test add FileName to normalized path
     *
     * @dataProvider dataProviderAddFileNameToNormalizePath
     *
     * @param string $normalizedPath outcome of normalizePath()
     * @param string $fileName with the following pattern:  'healthcheck'.$service.'_timeAndUniqueId.txt'
     * @param string $expectedResult full path with fileName
     *
     * @return void
     */
    public function testAddFileNameToNormalizePath($normalizedPath, $fileName, $expectedResult) : void
    {
        /** @var MockObject|CloudStorageHealthCheck $cloudStorageHealthCheckMock */
        $cloudStorageHealthCheckMock = $this->getMockBuilder(\CloudStorageHealthCheck::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        self::assertSame(
            $expectedResult,
            $this->invokePrivate($cloudStorageHealthCheckMock, 'addFileNameToNormalizePath', $normalizedPath, $fileName)
        );
    }
}

