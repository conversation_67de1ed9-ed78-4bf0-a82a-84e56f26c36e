<?php

namespace tests\source\common\sso\provider;

use IdPMetadataProvider;
use IdPMetadataProviderInterface;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * @covers IdPMetadataProvider
 * @group unit
 */
class IdPMetadataProviderTest extends \PHPUnit\Framework\TestCase
{

    public function testConstruct()
    {
        $object = new IdPMetadataProvider();
        self::assertInstanceOf(IdPMetadataProviderInterface::class, $object);
    }

    public function testBuildMetadataReturnStringMetadata()
    {
        /** @var MockObject|IdPMetadataProvider $responseProvider */
        $responseProvider = $this->getMockBuilder(IdPMetadataProvider::class)
            ->onlyMethods(['get509XCert'])
            ->getMock();
        $responseProvider
            ->expects(self::once())
            ->method('get509XCert')
            ->with('fo bar certificate')
            ->willReturn('fo bar 509XCert');

        $documentXml = $responseProvider->buildMetadata(
            'fobar-idp-issuer',
            'fo bar certificate',
            'fo-bar-federation-strategy',
            'https://fobar-loginLocation',
            'https://fobar-logoutLocation',
            'fo bar contactPersonType',
            'fo bar contactPersonName',
            'fo bar contactPersonEmailAddress'
        );
        self::assertStringContainsString(
            '<md:EntityDescriptor'
            . ' xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata"'
            . ' xmlns:ds="http://www.w3.org/2000/09/xmldsig#"'
            . ' entityID="fobar-idp-issuer"',
            $documentXml
        );
        self::assertStringContainsString(
            '<md:IDPSSODescriptor'
            . ' protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">',
            $documentXml
        );
        self::assertStringContainsString(
            '<md:KeyDescriptor use="signing">'
                . '<ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">'
                    . '<ds:X509Data>'
                        . '<ds:X509Certificate>fo bar 509XCert</ds:X509Certificate>'
                    . '</ds:X509Data>'
                . '</ds:KeyInfo>'
            . '</md:KeyDescriptor>',
            $documentXml
        );

        self::assertStringContainsString(
            '<md:KeyDescriptor use="encryption">'
                . '<ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#">'
                    . '<ds:X509Data>'
                        . '<ds:X509Certificate>fo bar 509XCert</ds:X509Certificate>'
                    . '</ds:X509Data>'
                . '</ds:KeyInfo>'
            . '</md:KeyDescriptor>',
            $documentXml
        );
        self::assertStringContainsString(
            '<md:SingleLogoutService'
            . ' Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"'
            . ' Location="',
            $documentXml
        );
        self::assertStringContainsString(
            '<md:NameIDFormat>fo-bar-federation-strategy</md:NameIDFormat>',
            $documentXml
        );
        self::assertStringContainsString(
            '<md:SingleSignOnService'
            . ' Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"'
            . ' Location="',
            $documentXml
        );
        self::assertStringContainsString(
            '</md:IDPSSODescriptor>'
            . '<md:ContactPerson contactType="fo bar contactPersonType">'
                . '<md:GivenName>fo bar contactPersonName</md:GivenName>'
                . '<md:EmailAddress>fo bar contactPersonEmailAddress</md:EmailAddress>'
            . '</md:ContactPerson>'
            . '</md:EntityDescriptor',
            $documentXml
        );
    }
}
