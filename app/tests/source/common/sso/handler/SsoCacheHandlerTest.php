<?php

namespace tests\source\common\sso\handler;

use CompanyCacheHandler;
use PHPUnit\Framework\MockObject\MockObject;
use PODManager;
use SsoCacheHandler;
use UserCacheHandler;

/**
 * @covers SsoCacheHandler
 * @group unit
 */
class SsoCacheHandlerTest extends \PHPUnit\Framework\TestCase
{

    public function testGetCompanyCacheHandlerWithCompanyOnTheSamePod()
    {
        $cny = 112233;

        /** @var MockObject|PODManager $podManagerMock */
        $podManagerMock = $this->getMockBuilder(PODManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isCnyInCurrentPODGroup'])
            ->getMock();
        $podManagerMock
            ->expects(self::once())
            ->method('isCnyInCurrentPODGroup')
            ->willReturn(true);

        /** @var MockObject|CompanyCacheHandler $companyCacheHandlerMock */
        $companyCacheHandlerMock = $this->getMockBuilder(CompanyCacheHandler::class)
            ->disableOriginalConstructor()
            ->getMock();

        /** @var MockObject|SsoCacheHandler $cacheHandlerMock */
        $cacheHandlerMock = $this->getMockBuilder(SsoCacheHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getPodManager', 'setDBSchema', 'getCompanyCacheHandlerObjectFromCurrentPod'])
            ->getMock();
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getPodManager')
            ->willReturn($podManagerMock);
        $cacheHandlerMock
            ->expects(self::once())
            ->method('setDBSchema')
            ->with($cny, 'cny#')
            ->willReturn($podManagerMock);
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getCompanyCacheHandlerObjectFromCurrentPod')
            ->with($cny)
            ->willReturn($companyCacheHandlerMock);

        self::assertSame($companyCacheHandlerMock, $cacheHandlerMock->getCompanyCacheHandler($cny));
    }

    public function testGetCompanyCacheHandlerWithCompanyOnOtherPod()
    {
        $cny = 112233;

        /** @var MockObject|PODManager $podManagerMock */
        $podManagerMock = $this->getMockBuilder(PODManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isCnyInCurrentPODGroup'])
            ->getMock();
        $podManagerMock
            ->expects(self::once())
            ->method('isCnyInCurrentPODGroup')
            ->willReturn(false);

        /** @var MockObject|CompanyCacheHandler $companyCacheHandlerMock */
        $companyCacheHandlerMock = $this->getMockBuilder(CompanyCacheHandler::class)
            ->disableOriginalConstructor()
            ->getMock();

        /** @var MockObject|SsoCacheHandler $cacheHandlerMock */
        $cacheHandlerMock = $this->getMockBuilder(SsoCacheHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getPodManager', 'setDBSchema', 'runOnCnyDB'])
            ->getMock();
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getPodManager')
            ->willReturn($podManagerMock);
        $cacheHandlerMock
            ->expects(self::never())
            ->method('setDBSchema');
        $cacheHandlerMock
            ->expects(self::once())
            ->method('runOnCnyDB')
            ->with(
                ['CompanyCacheHandler', 'getInstance'],
                [$cny],
                $cny
            )
            ->willReturn($companyCacheHandlerMock);

        self::assertSame($companyCacheHandlerMock, $cacheHandlerMock->getCompanyCacheHandler($cny));
    }

    public function testGetUserCacheHandlerWithCompanyOnTheSamePod()
    {
        $cny = 112233;
        $userId = 223344;

        /** @var MockObject|PODManager $podManagerMock */
        $podManagerMock = $this->getMockBuilder(PODManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isCnyInCurrentPODGroup'])
            ->getMock();
        $podManagerMock
            ->expects(self::once())
            ->method('isCnyInCurrentPODGroup')
            ->willReturn(true);

        /** @var MockObject|UserCacheHandler $userCacheHandlerMock */
        $userCacheHandlerMock = $this->getMockBuilder(UserCacheHandler::class)
            ->disableOriginalConstructor()
            ->getMock();

        /** @var MockObject|SsoCacheHandler $cacheHandlerMock */
        $cacheHandlerMock = $this->getMockBuilder(SsoCacheHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getPodManager', 'setDBSchema', 'getUserCacheHandlerObjectFromCurrentPod'])
            ->getMock();
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getPodManager')
            ->willReturn($podManagerMock);
        $cacheHandlerMock
            ->expects(self::never())
            ->method('setDBSchema');
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getUserCacheHandlerObjectFromCurrentPod')
            ->with($cny, $userId)
            ->willReturn($userCacheHandlerMock);

        self::assertSame(
            $userCacheHandlerMock,
            $cacheHandlerMock->getUserCacheHandler($cny, $userId)
        );
    }

    public function testGetUserCacheHandlerWithCompanyOnOtherPod()
    {
        $cny = 112233;
        $userId = 223344;

        /** @var MockObject|PODManager $podManagerMock */
        $podManagerMock = $this->getMockBuilder(PODManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['isCnyInCurrentPODGroup'])
            ->getMock();
        $podManagerMock
            ->expects(self::once())
            ->method('isCnyInCurrentPODGroup')
            ->willReturn(false);

        /** @var MockObject|UserCacheHandler $userCacheHandlerMock */
        $userCacheHandlerMock = $this->getMockBuilder(UserCacheHandler::class)
            ->disableOriginalConstructor()
            ->getMock();

        /** @var MockObject|SsoCacheHandler $cacheHandlerMock */
        $cacheHandlerMock = $this->getMockBuilder(SsoCacheHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getPodManager', 'setDBSchema', 'runOnCnyDB'])
            ->getMock();
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getPodManager')
            ->willReturn($podManagerMock);
        $cacheHandlerMock
            ->expects(self::never())
            ->method('setDBSchema');
        $cacheHandlerMock
            ->expects(self::once())
            ->method('runOnCnyDB')
            ->with(
                ['UserCacheHandler', 'getInstance'],
                [$cny, $userId],
                $cny
            )
            ->willReturn($userCacheHandlerMock);

        self::assertSame(
            $userCacheHandlerMock,
            $cacheHandlerMock->getUserCacheHandler($cny, $userId)
        );
    }
}
