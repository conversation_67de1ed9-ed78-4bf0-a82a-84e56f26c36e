<?php

namespace tests\source\common\sso;

use AbstractIdP;
use FatalException;
use IdPEntityProvider;
use IdPLogger;
use IdPMetadataProvider;
use MetadataIdP;
use MetadataIdPInterface;
use PHPUnit\Framework\MockObject\MockObject;
use SPEntityProvider;
use SsoIdPMetricInterface;

/**
 * @covers MetadataIdP
 * @covers AbstractIdP
 * @group unit
 */
class MetadataIdPTest extends \PHPUnit\Framework\TestCase
{

    public function testConstruct() : void
    {
        /** @var MockObject|SsoIdPMetricInterface $metricMock */
        $metricMock = $this->getMockBuilder(SsoIdPMetricInterface::class)
            ->disableOriginalConstructor()
            ->getMockForAbstractClass();
        
        $object = new MetadataIdP(
            new IdPEntityProvider(),
            new SPEntityProvider([]),
            new IdPLogger($metricMock),
            new IdPMetadataProvider()
        );

        self::assertInstanceOf(AbstractIdP::class, $object);
        self::assertInstanceOf(MetadataIdPInterface::class, $object);
    }

    public function testGetMetadataThrowExceptionIfIdPCertificateIsNotSet() : void
    {
        $this->expectException(FatalException::class);
        $this->expectExceptionMessage('IdP certificate is not set');

        /** @var MockObject|IdPEntityProvider $idpEntityProvider */
        $idpEntityProvider = $this->getMockBuilder(IdPEntityProvider::class)
            ->disableOriginalConstructor()
            ->getMock();
    
        /** @var MockObject|SPEntityProvider $spEntityProvider */
        $spEntityProvider = $this->getMockBuilder(SPEntityProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getIdPPublicCertificate'])
            ->getMock();
        $spEntityProvider
            ->expects(self::once())
            ->method('getIdPPublicCertificate')
            ->willReturn('');
    
        /** @var MockObject|SsoIdPMetricInterface $metricMock */
        $metricMock = $this->getMockBuilder(SsoIdPMetricInterface::class)
            ->disableOriginalConstructor()
            ->getMockForAbstractClass();
        
        /** @var MockObject|IdPLogger $idPLogger */
        $idPLogger = $this->getMockBuilder(IdPLogger::class)
            ->setConstructorArgs([$metricMock])
            ->onlyMethods(['info'])
            ->getMock();
        $idPLogger
            ->expects(self::never())
            ->method('info');

        /** @var MockObject|IdPMetadataProvider $idPMetadataProvider */
        $idPMetadataProvider = $this->getMockBuilder(IdPMetadataProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['buildMetadata'])
            ->getMock();
        $idPMetadataProvider
            ->expects(self::never())
            ->method('buildMetadata');

        /** @var MockObject|MetadataIdP $metadataIdP */
        $metadataIdP = new MetadataIdP($idpEntityProvider, $spEntityProvider, $idPLogger, $idPMetadataProvider);
        $metadataIdP->getMetadata();
    }

    public function testGetMetadataReturnResponseAndLogIt() : void
    {
        /** @var MockObject|IdPEntityProvider $idpEntityProvider */
        $idpEntityProvider = $this->getMockBuilder(IdPEntityProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods([
                'getLoginUrl',
                'getLogoutUrl',
                'getContactPersonType',
                'getContactPersonName',
                'getContactPersonEmailAddress'
            ])
            ->getMock();
        $idpEntityProvider
            ->expects(self::once())
            ->method('getLoginUrl')
            ->willReturn('https://loginUrl');
        $idpEntityProvider
            ->expects(self::once())
            ->method('getLogoutUrl')
            ->willReturn('https://logoutUrl');
        $idpEntityProvider
            ->expects(self::once())
            ->method('getContactPersonType')
            ->willReturn('fo bar contact person type');
        $idpEntityProvider
            ->expects(self::once())
            ->method('getContactPersonName')
            ->willReturn('fo bar contact person name');
        $idpEntityProvider
            ->expects(self::once())
            ->method('getContactPersonEmailAddress')
            ->willReturn('fo bar contact person email address');
    
        /** @var MockObject|SPEntityProvider $spEntityProvider */
        $spEntityProvider = $this->getMockBuilder(SPEntityProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getEntityId', 'getIdPPublicCertificate', 'getFederationStrategy'])
            ->getMock();
        $spEntityProvider
            ->expects(self::once())
            ->method('getEntityId')
            ->willReturn('fo-bar-idp');
        $spEntityProvider
            ->expects(self::exactly(2))
            ->method('getIdPPublicCertificate')
            ->willReturn('fo bar certificate');
        $spEntityProvider
            ->expects(self::once())
            ->method('getFederationStrategy')
            ->willReturn('fo-bar-federation-strategy');
    
        /** @var MockObject|SsoIdPMetricInterface $metricMock */
        $metricMock = $this->getMockBuilder(SsoIdPMetricInterface::class)
            ->disableOriginalConstructor()
            ->getMockForAbstractClass();
        
        /** @var MockObject|IdPLogger $idPLogger */
        $idPLogger = $this->getMockBuilder(IdPLogger::class)
            ->setConstructorArgs([$metricMock])
            ->onlyMethods(['info'])
            ->getMock();
        $idPLogger
            ->expects(self::once())
            ->method('info')
            ->with('Response Metadata: fo bar response');

        /** @var MockObject|IdPMetadataProvider $idPMetadataProvider */
        $idPMetadataProvider = $this->getMockBuilder(IdPMetadataProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['buildMetadata'])
            ->getMock();
        $idPMetadataProvider
            ->expects(self::once())
            ->method('buildMetadata')
            ->with(
                'fo-bar-idp',
                'fo bar certificate',
                'fo-bar-federation-strategy',
                'https://loginUrl',
                'https://logoutUrl',
                'fo bar contact person type',
                'fo bar contact person name',
                'fo bar contact person email address'
            )
            ->willReturn('fo bar response');

        /** @var MockObject|MetadataIdP $metadataIdP */
        $metadataIdP = new MetadataIdP($idpEntityProvider, $spEntityProvider, $idPLogger, $idPMetadataProvider);
        $metadataIdP->getMetadata();
    }
}
