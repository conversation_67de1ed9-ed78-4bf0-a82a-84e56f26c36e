<?php

namespace tests\source\common\sso\validator;

use AbstractSSOValidator;
use PHPUnit\Framework\MockObject\MockObject;
use RegisterValidator;
use SPEntityProviderInterface;
use ValidCompanyTypeValidation;
use ValidUserAttributesValidation;
use ValidUserTypeValidation;

/**
 * @covers RegisterValidator
 * @covers AbstractSSOValidator
 * @group unit
 */
class RegisterValidatorTest extends \PHPUnit\Framework\TestCase
{

    /**
     * @return array
     */
    public function getAcceptedCompanyTypesDataProvider(): array
    {
        return [
            [[]],
            [['ACCEPT_TYPES' => 'foo#~#bar']],
        ];
    }

    /**
     * @dataProvider getAcceptedCompanyTypesDataProvider
     *
     * @param array $acceptedCompanyTypes
     */
    public function testConstructAddValidations(array $acceptedCompanyTypes)
    {
        /** @var MockObject|SPEntityProviderInterface $spEntityProviderMock */
        $spEntityProviderMock = $this->getMockBuilder(SPEntityProviderInterface::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getAcceptedCompanyTypes'])
            ->getMockForAbstractClass();
        $spEntityProviderMock
            ->expects(self::once())
            ->method('getAcceptedCompanyTypes')
            ->willReturn($acceptedCompanyTypes);

        $validator = new RegisterValidator($spEntityProviderMock);
        self::assertInstanceOf(AbstractSSOValidator::class, $validator);
        $validations = $validator->getValidations();
        self::assertArrayHasKey(0, $validations);
        self::assertCount(2, $validations);
        self::assertInstanceOf(ValidUserAttributesValidation::class, $validations[0]);
        self::assertInstanceOf(ValidCompanyTypeValidation::class, $validations[1]);
    }
}
