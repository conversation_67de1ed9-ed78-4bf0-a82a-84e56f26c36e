<?php

namespace tests\source\common\sso\generic_service;

use AbstractIdP;
use CompanyCacheHandler;
use FeatureFlagEnabledValidation;
use IASessionHandler;
use IdPCookieProvider;
use IdPEntityProvider;
use IdPLogger;
use LoginIdP;
use LoginValidator;
use PHPUnit\Framework\MockObject\MockObject;
use SamlAuthnResponseProvider;
use GenericServiceTemplateProvider;
use SsoCacheHandler;
use SsoInitiateIdpController;
use SSOValidatorInterface;
use SubsystemManager;
use UserCacheHandler;
use UserRegisteredValidation;
use ValidCompanyTypeValidation;

/**
 * @covers SsoInitiateIdpController
 * @covers AbstractSsoIdpController
 * @covers AbstractTemplateProvider
 * @covers GenericServiceTemplateProvider
 * @group unit
 */
class GenericServiceSsoInitiateControllerTest extends \PHPUnit\Framework\TestCase
{
    
    /**
     * @return array[]
     */
    public function validationFailedDataProvider(): array
    {
        return [
            [
                [FeatureFlagEnabledValidation::COMPANY_NOT_ENABLED],
                sprintf(
                    sprintf(
                        '<span class="page-subsection-title">%s</span>',
                        FeatureFlagEnabledValidation::COMPANY_NOT_ENABLED
                    ),
                    AbstractIdP::SP_GENERIC_SERVICE_TAG_LABEL
                )
            ],
            [
                [sprintf(ValidCompanyTypeValidation::COMPANY_TYPE_NOT_ALLOWED, 'fo-bar')],
                sprintf(
                    sprintf(
                        '<span class="page-subsection-title">%s</span>',
                        ValidCompanyTypeValidation::COMPANY_TYPE_NOT_ALLOWED
                    ),
                    'fo-bar'
                )
            ]
        ];
    }
    
    /**
     * @dataProvider validationFailedDataProvider
     *
     * @param array  $validationMessages
     * @param string $expectedErrorMessage
     */
    public function testRunReturnValidationFailedPage(array $validationMessages, string $expectedErrorMessage) : void
    {
        $cny = 222222;
        $userId = 444444;
        
        /** @var MockObject|IdPEntityProvider $idpEntityProvider */
        $idpEntityProvider = $this->getMockBuilder(IdPEntityProvider::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|IdPLogger $idPLogger */
        $idPLogger = $this->getMockBuilder(IdPLogger::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|SubsystemManager $subsystemManager */
        $subsystemManager = $this->getMockBuilder(SubsystemManager::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|IdPCookieProvider $cookieProvider */
        $cookieProvider = $this->getMockBuilder(IdPCookieProvider::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|SamlAuthnResponseProvider $samlAuthnResponseProvider */
        $samlAuthnResponseProvider = $this->getMockBuilder(SamlAuthnResponseProvider::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|LoginValidator $loginValidator */
        $loginValidator = $this->getMockBuilder(LoginValidator::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['addValidations'])
            ->getMock();
        $loginValidator
            ->expects(self::once())
            ->method('addValidations')
            ->with(
                $this->callback(function ($value) {
                    self::assertCount(3, $value);
                    self::assertInstanceOf(FeatureFlagEnabledValidation::class, $value[0]);
                    self::assertInstanceOf(ValidCompanyTypeValidation::class, $value[1]);
                    self::assertInstanceOf(UserRegisteredValidation::class, $value[2]);
                    return true;
                })
            );
        
        /** @var MockObject|LoginIdP $loginIdpMock */
        $loginIdpMock = $this->getMockBuilder(LoginIdP::class)
            ->setConstructorArgs(
                [
                    $idpEntityProvider,
                    $idPLogger,
                    $subsystemManager,
                    $cookieProvider,
                    $samlAuthnResponseProvider,
                    $loginValidator
                ]
            )
            ->onlyMethods([
                'isEnabled',
                'getSubsystemByTag',
                'getValidator'
            ])
            ->getMock();
        $loginIdpMock
            ->expects(self::once())
            ->method('isEnabled')
            ->willReturn(true);
        $loginIdpMock
            ->expects(self::once())
            ->method('getSubsystemByTag')
            ->with(AbstractIdP::SP_GENERIC_SERVICE_TAG)
            ->willReturn(
                [
                    'NAME' => 'foBarSPIssuer',
                    'PROPERTIES' => json_encode(
                        [
                            'spPublicCertificate' => 'fo-bar-certificate',
                            'tag' => AbstractIdP::SP_GENERIC_SERVICE_TAG
                        ]
                    )
                ]
            );
        
        /** @var MockObject|SSOValidatorInterface $validatorMock */
        $validatorMock = $this->getMockBuilder(SSOValidatorInterface::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['validate', 'getValidationMessages'])
            ->getMockForAbstractClass();
        $validatorMock
            ->expects(self::once())
            ->method('validate')
            ->willReturn(false);
        $validatorMock
            ->expects(self::once())
            ->method('getValidationMessages')
            ->willReturn($validationMessages);
        
        $loginIdpMock
            ->expects(self::exactly(2))
            ->method('getValidator')
            ->willReturn($validatorMock);
        
        /** @var MockObject|CompanyCacheHandler $companyCacheHandlerMock */
        $companyCacheHandlerMock = $this->getMockBuilder(CompanyCacheHandler::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|UserCacheHandler $userCacheHandlerMock */
        $userCacheHandlerMock = $this->getMockBuilder(UserCacheHandler::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|SsoCacheHandler $cacheHandlerMock */
        $cacheHandlerMock = $this->getMockBuilder(SsoCacheHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getCompanyCacheHandler', 'getUserCacheHandler', 'getPodManager'])
            ->getMock();
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getCompanyCacheHandler')
            ->with($cny)
            ->willReturn($companyCacheHandlerMock);
        $cacheHandlerMock
            ->expects(self::once())
            ->method('getUserCacheHandler')
            ->with($cny, $userId)
            ->willReturn($userCacheHandlerMock);
        
        /** @var MockObject|IASessionHandler $iaSessionHandlerMock */
        $iaSessionHandlerMock = $this->getMockBuilder(IASessionHandler::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getUserId'])
            ->getMock();
        $iaSessionHandlerMock
            ->expects(self::once())
            ->method('getUserId')
            ->willReturn(sprintf('%s@%s@%s', $userId, $cny, 'A'));
        
        /** @var MockObject|GenericServiceTemplateProvider $templateProviderMock */
        $templateProviderMock = $this->getMockBuilder(GenericServiceTemplateProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getLocalizedText', 'gtFromOneToken'])
            ->getMockForAbstractClass();
        $templateProviderMock
            ->expects(self::exactly(2))
            ->method('getLocalizedText')
            ->withConsecutive(
                [[['id' => 'IA.SAGE_INTACCT_ACCOUNT_CONNECT']]],
                [[['id' => 'IA.CLOSE']]],
            )
            ->willReturnOnConsecutiveCalls(
                [['IA.SAGE_INTACCT_ACCOUNT_CONNECT' => 'IA.SAGE_INTACCT_ACCOUNT_CONNECT']],
                [['IA.CLOSE' => 'IA.CLOSE']],
            );
        $templateProviderMock
            ->expects(self::once())
            ->method('gtFromOneToken')
            ->with(
                'IA.SAGE_INTACCT_WITH_PLACEHOLDER',
                [
                    ['name' => 'PLACEHOLDER', 'value' => AbstractIdP::SP_GENERIC_SERVICE_TAG_LABEL],
                ]
            )
            ->willReturn('IA.SAGE_INTACCT_WITH_PLACEHOLDER');
        
        $_REQUEST['_sso_sp'] = AbstractIdP::SP_GENERIC_SERVICE_TAG;
        $_REQUEST['_sso_identity'] = 'fo-bar-sess-2';
        
        /** @var MockObject|SsoInitiateIdpController $ssoInitiateIdpControllerMock */
        $ssoInitiateIdpControllerMock = $this->getMockBuilder(SsoInitiateIdpController::class)
            ->setConstructorArgs([$loginIdpMock, $cacheHandlerMock])
            ->onlyMethods(['getTemplateProvider', 'getIaSessionHandler', 'gtFromOneToken'])
            ->getMock();
        $ssoInitiateIdpControllerMock
            ->expects(self::once())
            ->method('getTemplateProvider')
            ->with(AbstractIdP::SP_GENERIC_SERVICE_TAG)
            ->willReturn($templateProviderMock);
        $ssoInitiateIdpControllerMock
            ->expects(self::once())
            ->method('getIaSessionHandler')
            ->with('fo-bar-sess-2')
            ->willReturn($iaSessionHandlerMock);
        $ssoInitiateIdpControllerMock
            ->expects(self::once())
            ->method('gtFromOneToken')
            ->with(
                $validationMessages[0],
                [
                    ['name' => 'PLACEHOLDER', 'value' => AbstractIdP::SP_GENERIC_SERVICE_TAG_LABEL],
                ]
            )
            ->willReturn($validationMessages[0]);
        
        ob_start();
        $ssoInitiateIdpControllerMock->run();
        $content = ob_get_contents();
        ob_end_clean();
        self::assertStringContainsString(
            '<title>IA.SAGE_INTACCT_ACCOUNT_CONNECT</title>',
            $content
        );
        self::assertStringContainsString(
            '<script src="../resources/js/sso.js" language="javascript"></script>',
            $content
        );
        self::assertStringContainsString(
            '<script src="../resources/js/sso.js" language="javascript"></script>',
            $content
        );
        self::assertStringContainsString(
            '<body onunload="" onload="" ',
            $content
        );
        self::assertStringContainsString(
            '<span title="IA.SAGE_INTACCT_WITH_PLACEHOLDER">IA.SAGE_INTACCT_WITH_PLACEHOLDER</span>',
            $content
        );
        self::assertStringContainsString($expectedErrorMessage, $content);
        self::assertStringContainsString(
            '<span class="page-subsection-title"></span>',
            $content
        );
        self::assertStringContainsString(
            '<button class="btn btn-secondary button-additional" onclick="ssoCloseWindow();">IA.CLOSE</button>',
            $content
        );
    }
}
