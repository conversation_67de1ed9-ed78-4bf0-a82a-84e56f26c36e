<?php

namespace tests\source\validation\library\rules\custom\vendor;

use PHPUnit\Framework\TestCase;
use unitTest\core\UnitTestTool;
use ReflectionClass;
use Validator;
use ValidatorResourcesBase;
use ContactUniqueRule;
use Rule;
use ValidationContext;

/**
 * @covers Vendors
 * @group  unit
 */
class ContactUniqueRuleTest extends UnitTestTool
{

    public ValidatorResourcesBase $resources;
    public Validator $validator;
    public Rule $rule;

    public function setUp() : void
    {
        $directoryPath = __DIR__ . '/testdata/' . (new ReflectionClass(new self()))->getShortName();
        parent::initializeDataObject($directoryPath);

        $this->validator = new Validator('OBJECT_TYPE', 'OBJECT_ALIAS');

        $this->resources = $this->createMyStub();
        $this->rule = new ContactUniqueRule($this->resources);
        $args = [ 'RETURNTO' ];
        $this->rule->fillParameters($args);

        $this->rules = [
            'RETURNTO.CONTACT_NAME' => [ $this->rule ],
        ];
    }

    public function createMyStub()
    {
        $stub = $this->createStub(ValidatorResourcesBase::class);

        $stubData = parent::getDataObj()['stub'];
    
        $entitiesObjects = [
            [ 'vendor', 'vendor_1', $stubData['getEntityObject']['vendor_1'] ],
            [ 'vendor', 'vendor_2', $stubData['getEntityObject']['vendor_2'] ],
            [ 'contact', 'contact_1', $stubData['getEntityObject']['contact_1'] ],
            [ 'contact', 'contact_2', $stubData['getEntityObject']['contact_2'] ],
            [ 'contact', 'contact_3', $stubData['getEntityObject']['contact_3'] ],
        ];
    
        $stub->method('getEntityObject')
             ->will($this->returnValueMap($entitiesObjects));

        return $stub;
    }

    public function testPasses()
    {
        $dataset = parent::getDataObj()[__FUNCTION__]['testcases'] ?? [];

        foreach ( $dataset as $request ) {
            $request = $request['request'];

            $validation = $this->validator->make($request, $this->rules);

            $updatedObject = $this->resources->getEntityObject('vendor', $request['VENDOR_ID']);
            $validationContext = new ValidationContext('vendor', !empty($updatedObject), $updatedObject);
            $validation->setContext($validationContext);

            $validation->validate();

            $this->assertFalse($validation->fails());
        }
    }

    public function testFails()
    {
        $dataset = parent::getDataObj()[__FUNCTION__]['testcases'] ?? [];

        foreach ( $dataset as $request ) {
            $request = $request['request'];

            $validation = $this->validator->make($request, $this->rules);

            $updatedObject = $this->resources->getEntityObject('vendor', $request['VENDOR_ID']);
            $validationContext = new ValidationContext('vendor', !empty($updatedObject), $updatedObject);
            $validation->setContext($validationContext);

            $validation->validate();

            $this->assertTrue($validation->fails());
        }
    }
}
