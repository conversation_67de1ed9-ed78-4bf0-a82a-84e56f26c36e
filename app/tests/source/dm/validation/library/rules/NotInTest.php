<?php

namespace tests\source\validation\library\rules;

use PHPUnit\Framework\TestCase;
use NotInRule;

class NotInTest extends TestCase
{

    public function setUp() : void
    {
        $this->rule = new NotInRule();
    }

    public function testValids()
    {
        $args = [ [ '2', '3', '4' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('1'));
        $args = [ [ 1, 2, 3 ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check(5));
    }

    public function testInvalids()
    {
        $args = [ [ 'bar', 'baz', 'qux' ] ];
        $this->assertFalse($this->rule->fillParameters($args)->check('bar'));
    }

    public function testStricts()
    {
        // Not strict
        $args = [ [ '1', '2', '3' ] ];
        $this->assertFalse($this->rule->fillParameters($args)->check(1));
        $args = [ [ '1', '2', '3' ] ];
        $this->assertFalse($this->rule->fillParameters($args)->check(true));

        // Strict
        $this->rule->strict();
        $args = [ [ '1', '2', '3' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check(1));
        $args = [ [ '1', '2', '3' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check(1));
    }
}
