<?php

namespace tests\source\validation\library\rules;

use PHPUnit\Framework\TestCase;
use RegexRule;

class RegexTest extends TestCase
{
    public function setUp() : void
    {
        $this->rule = new RegexRule();
    }

    public function testValids()
    {
        $args = [ "/^F/i" ];
        $this->assertTrue($this->rule->fillParameters($args)->check("foo"));
    }

    public function testInvalids()
    {
        $args = [ "/^F/i" ];
        $this->assertFalse($this->rule->fillParameters($args)->check("bar"));
    }
}
