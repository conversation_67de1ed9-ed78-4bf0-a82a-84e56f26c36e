<?php

namespace tests\source\validation\library\rules;

use PHPUnit\Framework\TestCase;
use UrlRule;

class UrlTest extends TestCase
{

    public function setUp() : void
    {
        $this->rule = new UrlRule();
    }

    public function testValids()
    {
        // Without specific schemes
        $this->assertTrue($this->rule->check('ftp://foobar.com'));
        $this->assertTrue($this->rule->check('any://foobar.com'));
        $this->assertTrue($this->rule->check('http://foobar.com'));
        $this->assertTrue($this->rule->check('https://foobar.com'));
        $this->assertTrue($this->rule->check('https://foobar.com/path?a=123&b=blah'));

        // Using specific schemes
        $args = [ [ 'ftp' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('ftp://foobar.com'));
        $args = [ [ 'any' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('any://foobar.com'));
        $args = [ [ 'http' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('http://foobar.com'));
        $args = [ [ 'https' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('https://foobar.com'));
        $args = [ [ 'http', 'https' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('https://foobar.com'));
        $args = [ [ 'foo', 'bar' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('bar://foobar.com'));
        $args = [ [ 'mailto' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('mailto:<EMAIL>'));
        $args = [ [ 'jdbc' ] ];
        $this->assertTrue($this->rule->fillParameters($args)->check('*****************************'));

        // Using forScheme
        $this->assertTrue($this->rule->forScheme('ftp')
                                     ->check('ftp://foobar.com'));
        $this->assertTrue($this->rule->forScheme('http')
                                     ->check('http://foobar.com'));
        $this->assertTrue($this->rule->forScheme('https')
                                     ->check('https://foobar.com'));
        $this->assertTrue($this->rule->forScheme([ 'http', 'https' ])
                                     ->check('https://foobar.com'));
        $this->assertTrue($this->rule->forScheme('mailto')
                                     ->check('mailto:<EMAIL>'));
        $this->assertTrue($this->rule->forScheme('jdbc')
                                     ->check('*****************************'));
    }

    public function testInvalids()
    {
        $this->assertFalse($this->rule->check('foo:'));
        $this->assertFalse($this->rule->check('mailto:<EMAIL>'));
        $this->assertFalse($this->rule->forScheme('mailto')
                                      ->check('http://www.foobar.com'));
        $this->assertFalse($this->rule->forScheme('ftp')
                                      ->check('http://www.foobar.com'));
        $this->assertFalse($this->rule->forScheme('jdbc')
                                      ->check('http://www.foobar.com'));
        $this->assertFalse($this->rule->forScheme([ 'http', 'https' ])
                                      ->check('any://www.foobar.com'));
    }
}
