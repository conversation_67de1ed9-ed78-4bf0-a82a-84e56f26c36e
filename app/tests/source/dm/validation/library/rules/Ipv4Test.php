<?php

namespace tests\source\validation\library\rules;

use PHPUnit\Framework\TestCase;
use Ipv4Rule;

class Ipv4Test extends TestCase
{

    public function setUp() : void
    {
        $this->rule = new Ipv4Rule();
    }

    public function testValids()
    {
        $this->assertTrue($this->rule->check('0.0.0.0'));
        $this->assertTrue($this->rule->check('*******'));
        $this->assertTrue($this->rule->check('***************'));
    }

    public function testInvalids()
    {
        $this->assertFalse($this->rule->check('hf02::2'));
        $this->assertFalse($this->rule->check('12345:0000:3238:DFE1:0063:0000:0000:FEFB'));
    }
}
