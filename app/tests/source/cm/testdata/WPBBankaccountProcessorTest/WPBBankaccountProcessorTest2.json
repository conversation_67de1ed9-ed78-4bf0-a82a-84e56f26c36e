{"testGetAllBankAccounts": {"testcases": [{"ID": "2", "response": {"BANKACCOUNTID": "1a95e2e9-796b-4534-aa4c-e0e333fa878f", "BANKID": "92c3264b-4835-44bf-a9df-0bfb29b6b327", "STATUS": "active", "ACCOUNTKEY": "NON-MFA", "ACCOUNTNAME": "Demo Bank", "DATAPROVIDER": null}, "request": {"total": 1, "items": [{"bankId": "92c3264b-4835-44bf-a9df-0bfb29b6b327", "users": [], "bankIdentifier": null, "accountIdentifier": "3b62796c-a130-4966-bb2e-38d375f276e7", "accountType": "Checking", "branchIdentifier": null, "accountKey": "NON-MFA", "defaultCurrency": null, "accountName": "Demo Bank", "dataProvider": "indirect", "authAlwaysRequired": false, "aggregatorId": null, "aggregatorName": "demo", "requestedStartDate": "2019-02-16T11:30:00.000Z", "company": {"companyId": "543d3876-a547-4476-862f-77f31d966802", "companyName": "MCPMEGA032712-AMEXWF_Feb19_AK1", "vatNumber": null}, "accountant": {"accountantManaged": "none", "accountantCompanyId": null}, "heldTransactions": 0, "clientAuthorisationToken": "AUTO", "status": "active", "statusModifiedDate": "2019-03-18T08:18:19.383Z", "lastTransactionId": 201, "lastHeldTransactionId": 0, "ledgerBalanceAmount": 0, "ledgerBalanceDate": "2019-03-18T08:18:19.391Z", "availableBalanceAmount": 0, "availableBalanceDate": "2019-03-18T08:18:19.391Z", "lastTransactionsReceived": null, "featureOptions": {"autoCreateRules": false, "applyTransactionNarrativeMatching": false, "disableRuleWildcards": false}, "bankAccountId": "1a95e2e9-796b-4534-aa4c-e0e333fa878f"}]}}]}}