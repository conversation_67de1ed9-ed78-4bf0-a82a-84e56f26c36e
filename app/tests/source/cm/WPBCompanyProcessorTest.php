<?php

namespace tests\source\cm;

use Exception;
use IaSbcRequestInterface;
use PHPUnit\Framework\MockObject\MockObject;
use SbcHttpResult;
use unitTest\core\UnitTestBase;
use unitTest\core\Utils;
use WPBCompanyProcessor;

/**
 * @covers WPBCompanyProcessor
 * @group unit
 */
class WPBCompanyProcessorTest extends UnitTestBase
{

    /** @var MockObject|IaSbcRequestInterface $IaSbcRequestMock */
    public $IaSbcRequestMock;

    /** @var string $orgID */
    private $orgID = 'fb7d1e37-90dc-4597-8149-81bd6ebca295';

    /** @var string $companyID */
    private $companyID = 'ccc4547-90dc-4597-8149-81bd6ebca295';


    public function setUp(): void
    {
        parent::setUp();
        $this->IaSbcRequestMock = $this->getMockBuilder(IaSbcRequestInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
    }


    public function testConstructorSuccess() : void
    {
        $wpbComppanyProcessor = new WPBCompanyProcessor($this->IaSbcRequestMock, $this->orgID);
        self::assertEquals($wpbComppanyProcessor->getOrgId(), $this->orgID);
    }


    public function testConstructorFailure(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Organization id is missing');
        $wpbComppanyProcessor = new WPBCompanyProcessor($this->IaSbcRequestMock, '');
        self::assertEquals($wpbComppanyProcessor->getOrgId(), '');
    }


    public function testGetCompanyEndPoint(): void
    {
        $wpbComppanyProcessor = new WPBCompanyProcessor($this->IaSbcRequestMock, $this->orgID);
        self::assertEquals(
            '/companies',
            $wpbComppanyProcessor->getCompanyEndPoint()
        );
    }


    public function testGetCompanyURL(): void
    {
        $wpbComppanyProcessor = new WPBCompanyProcessor($this->IaSbcRequestMock, $this->orgID);
        self::assertEquals(
            '/organisations/' . $this->orgID . '/companies/' . $this->companyID,
            $wpbComppanyProcessor->getCompanyURL($this->companyID)
        );
    }

    /**
     * @dataProvider dataProviderWPBCompanyProcessor
     * @param string $companyName
     * @param string $externalId
     * @param string $output
     */
    public function testGeneratePostPayload(
        string $companyName,
        string $externalId,
        string $output
    ): void {
        $wpbComppanyProcessor = new WPBCompanyProcessor($this->IaSbcRequestMock, $this->orgID);

        $method = Utils::getAccessibleReflectionMethod(
            'WPBCompanyProcessor', 'generatePostPayload');

        $res = $method->invokeArgs($wpbComppanyProcessor, [$companyName, $externalId]);
        self::assertEquals($res, $output);
    }


    public function testGetAllCompanies(): void
    {
        $output = [
            [ 'companyId' => '543d3876-a547-4476-862f-77f31d966802', 'name' => 'MCPMEGA032712-AMEXWF_Feb19_AK1', 'externalId' => '91' ],
            [ 'companyId' => 'b72a92ea-d950-4a16-af8a-5b157d86b4d1', 'name' => '33333335-44444445', 'externalId' => '81']
        ];

        $this->IaSbcRequestMock
            ->method('get')
            ->willReturn(
                new SbcHttpResult(
                    200,
                    '[{"companyId":"543d3876-a547-4476-862f-77f31d966802","organisationId":"eb4c59e6-5eef-42c6-9e86-da77fe812999","name":"MCPMEGA032712-AMEXWF_Feb19_AK1","externalId":"91","logoUrl":"","address":null,"taxNumber":null,"standardIndustrialCode":null,"contactTelNo":null,"contactEmail":null,"defaultMotoProvider":null},
{"companyId":"b72a92ea-d950-4a16-af8a-5b157d86b4d1","organisationId":"eb4c59e6-5eef-42c6-9e86-da77fe812999","name":"33333335-44444445","externalId":"81","logoUrl":"","address":null,"taxNumber":null,"standardIndustrialCode":null,"contactTelNo":null,"contactEmail":null,"defaultMotoProvider":null}]',
                    []
                )
            );

        $wpbComppanyProcessor = new WPBCompanyProcessor($this->IaSbcRequestMock, $this->orgID);
        $res = $wpbComppanyProcessor->getAllCompanies();
        self::assertEquals($output ,$res);
    }

    /**
     * @return array
     */
    public function dataProviderWPBCompanyProcessor(): array
    {
        return [
            ['Fo Bar', 'ABC', '{"name":"Fo Bar","externalId":"ABC"}'],
        ];
    }
}
