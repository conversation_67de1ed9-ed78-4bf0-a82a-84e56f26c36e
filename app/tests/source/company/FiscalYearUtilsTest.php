<?php

namespace test\source\company;

use DateTemplates;
use FiscalYearUtils;
use unitTest\core\UnitTestBaseContext;

class FiscalYearUtilsTest extends UnitTestBaseContext
{
    public function getFiscalYearDateRangeDataProvider(): array {
        return [
            [ // 0
                [
                    'gNonStandardPeriods' => 1,
                    'returnValue' => ['09/26/2023', '09/26/2023']
                ],
                [
                    'asOfDate' => '09/26/2023',
                    'lookUp' => 14
                ]
            ],
            [ // 1
                [
                    'gNonStandardPeriods' => 0,
                    'returnValue' => ['03/01/2023', '02/29/2024']
                ],
                [
                    'asOfDate' => '',
                    'lookUp' => 37
                ]
            ],
        ];
    }

    /**
     * @dataProvider getFiscalYearDateRangeDataProvider
     * @param array $values
     *
     * @return void
     * @throws \IAException
     */
    public function testGetFiscalYearDateRange(array $expected, array $values)
    {
        \Globals::$g->gNonStandardPeriods = $expected['gNonStandardPeriods'];
        $dateTemplatesMock = $this->getMockBuilder(DateTemplates::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['GetDateRange'])
            ->getMock();
        $dateTemplatesMock->expects($this->any())
            ->method('GetDateRange')
            ->with($values['lookUp'], $values['asOfDate'])
            ->willReturn($expected['returnValue']);
        FiscalYearUtils::setDateTemplates($dateTemplatesMock);
        $actual = FiscalYearUtils::getFiscalYearDateRange($values['asOfDate']);
        self::assertSame($expected['returnValue'], $actual);
    }

    public function getFiscalYearDataProvider(): array {
        return [
            [ // 0
                [
                    'gNonStandardPeriods' => 0,
                    'returnValue' => 2024
                ],
                [
                    'asOfDate' => '',
                    'lookUp' => 37,
                    'returnValueGetDateRange' => ['03/01/2023', '02/29/2024']
                ]
            ],
            [ // 1
                [
                    'gNonStandardPeriods' => 1,
                    'returnValue' => 2023
                ],
                [
                    'asOfDate' => '09/26/2023',
                    'lookUp' => 14,
                    'returnValueGetDateRange' => ['09/26/2023', '09/26/2023']
                ]
            ],
            [ // 2
                [
                    'gNonStandardPeriods' => 1,
                    'returnValue' => -1
                ],
                [
                    'asOfDate' => '09.26.2023',
                    'lookUp' => 14,
                    'returnValueGetDateRange' => []
                ]
            ],
        ];
    }

    /**
     * @dataProvider getFiscalYearDataProvider
     * @param array $values
     *
     * @return void
     * @throws \IAException
     */
    public function testGetFiscalYear(array $expected, array $values)
    {
        \Globals::$g->gNonStandardPeriods = $expected['gNonStandardPeriods'];
        $dateTemplatesMock = $this->getMockBuilder(DateTemplates::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['GetDateRange'])
            ->getMock();
        $dateTemplatesMock->expects($this->any())
            ->method('GetDateRange')
            ->with($values['lookUp'], $values['asOfDate'])
            ->willReturn($values['returnValueGetDateRange']);
        FiscalYearUtils::setDateTemplates($dateTemplatesMock);
        $actual = FiscalYearUtils::getFiscalYear($values['asOfDate']);
        self::assertSame($expected['returnValue'], $actual);
    }
}
