<?php

namespace tests\source\core\suites\Config;

use PHPUnit\Framework\MockObject\MockObject;
use SuitesAbstractConfigProvider;
use SuitesApplicationConfigProvider;
use SuitesConfigProvider;

/**
 * @covers SuitesAbstractConfigProvider
 * @covers SuitesConfigProvider
 * @group unit
 */
class SuitesConfigProviderTest extends \PHPUnit\Framework\TestCase
{

    public function testConstruct(): void
    {
        self::assertInstanceOf(SuitesAbstractConfigProvider::class, new SuitesConfigProvider());
    }
    
    /**
     * @return array
     */
    public function applicationsDataProvider(): array
    {
        return [
            [[], null],
            [
                ['APPLICATIONS' => ['SI_FINANCIALS', 'SI_PLANNING_US', 'SI_PLANNING_EU']],
                ['SI_FINANCIALS', 'SI_PLANNING_US', 'SI_PLANNING_EU']
            ],
            [
                ['APPLICATIONS' => ['SI_FINANCIALS', 'SI_PLANNING_US', 'SI_PLANNING_EU', 'SI_ANALYTICS', 'S_PAYROLL', 'S_HR']],
                ['SI_FINANCIALS', 'SI_PLANNING_US', 'SI_PLANNING_EU', 'SI_ANALYTICS', 'S_PAYROLL', 'S_HR']
            ]
        ];
    }
    
    /**
     * @dataProvider applicationsDataProvider
     * @param array      $config
     * @param array|null $expected
     */
    public function testGetApplications(array $config, ?array $expected): void
    {
        /** @var MockObject|SuitesConfigProvider $configProviderMock */
        $configProviderMock = $this->getMockBuilder(SuitesConfigProvider::class)
            ->onlyMethods(['getValueForIACFGProperty'])
            ->getMock();
        $configProviderMock
            ->expects(self::once())
            ->method('getValueForIACFGProperty')
            ->with(SuitesConfigProvider::CONFIG_KEY)
            ->willReturn($config);

        self::assertSame($expected, $configProviderMock->getApplications());
    }
    
    /**
     * @return array
     */
    public function allEnabledDataProvider(): array
    {
        return [
            [
                ['APPLICATIONS' => ['SI_FINANCIALS', 'SI_PLANNING_US']],
                [
                    ...($enabled = [
                        $this->getSuitesApplicationConfigProviderMock(['getEnabled'], ['expects' => self::once(), 'willReturn' => true]),
                        $this->getSuitesApplicationConfigProviderMock(['getEnabled'], ['expects' => self::once(), 'willReturn' => true])
                    ])
                ],
                ['SI_FINANCIALS' => $enabled[0], 'SI_PLANNING_US' => $enabled[1]]
            ],
            [
                ['APPLICATIONS' => ['SI_FINANCIALS', 'SI_PLANNING_US']],
                [
                    ($enabled = $this->getSuitesApplicationConfigProviderMock(
                        ['getEnabled'], ['expects' => self::once(), 'willReturn' => true]
                    )),
                    $this->getSuitesApplicationConfigProviderMock(['getEnabled'], ['expects' => self::once(), 'willReturn' => false])
                ],
                ['SI_FINANCIALS' => $enabled]
            ],
            [
                ['APPLICATIONS' => ['SI_FINANCIALS', 'SI_PLANNING_US']],
                [
                    ...([
                        $this->getSuitesApplicationConfigProviderMock(['getEnabled'], ['expects' => self::once(), 'willReturn' => false]),
                        $this->getSuitesApplicationConfigProviderMock(['getEnabled'], ['expects' => self::once(), 'willReturn' => false])
                    ])
                ],
                []
            ],
        ];
    }
    
    /**
     * @dataProvider allEnabledDataProvider
     * @param array $config
     * @param array $suitesApplicationConfigProvider
     * @param array $expected
     *
     * @return void
     */
    public function testGetAllEnabled(array $config, array $suitesApplicationConfigProvider, array $expected): void
    {
        
        /** @var MockObject|SuitesConfigProvider $configProviderMock */
        $configProviderMock = $this->getMockBuilder(SuitesConfigProvider::class)
            ->onlyMethods(['getValueForIACFGProperty', 'getApplicationConfig'])
            ->getMock();
        $configProviderMock
            ->expects(self::once())
            ->method('getValueForIACFGProperty')
            ->with(SuitesConfigProvider::CONFIG_KEY)
            ->willReturn($config);
        $configProviderMock
            ->expects(self::exactly(count($config['APPLICATIONS'] ?? 0)))
            ->method('getApplicationConfig')
            ->willReturnOnConsecutiveCalls(...$suitesApplicationConfigProvider);

        self::assertSame($expected, $configProviderMock->getAllEnabled());
    }
    
    /**
     * @return array
     */
    public function getAllDataProvider(): array
    {
        /** @var MockObject|SuitesConfigProvider $suitesApplicationConfigProvider */
        $suitesApplicationConfigProvider = $this->getMockBuilder(SuitesApplicationConfigProvider::class)
            ->disableOriginalConstructor()
            ->getMock();
        return [
            [
                ['APPLICATIONS' => ['SI_FINANCIALS', 'SI_PLANNING_US']],
                [...($all = [$suitesApplicationConfigProvider, $suitesApplicationConfigProvider])],
                ['SI_FINANCIALS' => $all[0], 'SI_PLANNING_US' => $all[1]]
            ],
            [
                ['APPLICATIONS' => ['SI_FINANCIALS', 'SI_PLANNING_EU']],
                [...($all = [$suitesApplicationConfigProvider, $suitesApplicationConfigProvider])],
                ['SI_FINANCIALS' => $all[0], 'SI_PLANNING_EU' => $all[1]]
            ],
        ];
    }
    
    /**
     * @dataProvider getAllDataProvider
     * @param array $config
     * @param array $suitesApplicationConfigProvider
     * @param array $expected
     *
     * @return void
     */
    public function testGetAll(array $config, array $suitesApplicationConfigProvider, array $expected): void
    {
        /** @var MockObject|SuitesConfigProvider $configProviderMock */
        $configProviderMock = $this->getMockBuilder(SuitesConfigProvider::class)
            ->onlyMethods(['getValueForIACFGProperty', 'getApplicationConfig'])
            ->getMock();
        $configProviderMock
            ->expects(self::once())
            ->method('getValueForIACFGProperty')
            ->with(SuitesConfigProvider::CONFIG_KEY)
            ->willReturn($config);
        $configProviderMock
            ->expects(self::exactly(count($config['APPLICATIONS'] ?? 0)))
            ->method('getApplicationConfig')
            ->willReturnOnConsecutiveCalls(...$suitesApplicationConfigProvider);

        self::assertSame($expected, $configProviderMock->getAll());
    }
    
    /**
     * @return void
     */
    public function testGetApplicationConfig(): void
    {
        $applicationId = 'SI_FINANCIALS';
        
        /** @var MockObject|SuitesApplicationConfigProvider $configProviderMock */
        $suiteApplicationMock = $this->getMockBuilder(SuitesApplicationConfigProvider::class)
            ->disableOriginalConstructor()
            ->getMock();
        
        /** @var MockObject|SuitesConfigProvider $configProviderMock */
        $configProviderMock = $this->getMockBuilder(SuitesConfigProvider::class)
            ->onlyMethods(['getSuitesApplicationConfigProvider'])
            ->getMock();
        $configProviderMock
            ->expects(self::once())
            ->method('getSuitesApplicationConfigProvider')
            ->with($applicationId)
            ->willReturn($suiteApplicationMock);
        
        self::assertInstanceOf(SuitesApplicationConfigProvider::class, $configProviderMock->getApplicationConfig($applicationId));
        self::assertSame($suiteApplicationMock, $configProviderMock->getApplicationConfig($applicationId));
    }
    
    /**
     * @param array $onlyMethods
     * @param array $methodOptions
     *
     * @return SuitesApplicationConfigProvider|MockObject
     */
    private function getSuitesApplicationConfigProviderMock(
        array $onlyMethods,
        array $methodOptions
    ) : SuitesApplicationConfigProvider|MockObject {
        /** @var MockObject|SuitesApplicationConfigProvider $configProviderMock */
        $suiteApplicationMock = $this->getMockBuilder(SuitesApplicationConfigProvider::class)
            ->disableOriginalConstructor()
            ->onlyMethods($onlyMethods)
            ->getMock();
        foreach ($onlyMethods as $method) {
            $suiteApplicationMock
                ->expects($methodOptions['expects'])
                ->method($method)
                ->willReturn($methodOptions['willReturn']);
        }
        
        return $suiteApplicationMock;
    }
}
