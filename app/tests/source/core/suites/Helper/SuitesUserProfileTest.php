<?php

namespace test\source\core\suites\Helper;

use SuitesUserProfile;

/**
 * @covers SuitesUserProfile
 * @group unit
 */
class SuitesUserProfileTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @return void
     */
    public function testDefaultValues() : void
    {
        $suiteUserProfile = new SuitesUserProfile();
        
        self::assertSame(false, $suiteUserProfile->isSuiteExperienceEnabled());
        self::assertSame(false, $suiteUserProfile->hasAccessToSuiteApps());
        self::assertSame(true, $suiteUserProfile->hasAccessToSageIntacct());
        self::assertSame(false, $suiteUserProfile->hasAccessToOnlyOneApplication());
        self::assertSame(false, $suiteUserProfile->isSuiteRestricted());
        self::assertSame('', $suiteUserProfile->onlyOneAppId());
    }
    
    /**
     * @return array
     */
    public function trueFalseDataProvider() : array
    {
        return [
            [true, true],
            [false, false]
        ];
    }
    
    /**
     * @dataProvider trueFalseDataProvider
     * @param bool $setValue
     * @param bool $expected
     *
     * @return void
     */
    public function testIsSuiteExperienceEnabled(bool $setValue, bool $expected) : void
    {
        $suiteUserProfile = new SuitesUserProfile();
        $suiteUserProfile->setIsSuiteExperienceEnabled($setValue);
        
        self::assertSame($expected, $suiteUserProfile->isSuiteExperienceEnabled());
    }
    
    /**
     * @dataProvider trueFalseDataProvider
     * @param bool $setValue
     * @param bool $expected
     *
     * @return void
     */
    public function testHasAccessToSuiteApps(bool $setValue, bool $expected) : void
    {
        $suiteUserProfile = new SuitesUserProfile();
        $suiteUserProfile->setHasAccessToSuiteApps($setValue);
        
        self::assertSame($expected, $suiteUserProfile->hasAccessToSuiteApps());
    }
    
    /**
     * @dataProvider trueFalseDataProvider
     * @param bool $setValue
     * @param bool $expected
     *
     * @return void
     */
    public function testHasAccessToSageIntacct(bool $setValue, bool $expected) : void
    {
        $suiteUserProfile = new SuitesUserProfile();
        $suiteUserProfile->setHasAccessToSageIntacct($setValue);
        
        self::assertSame($expected, $suiteUserProfile->hasAccessToSageIntacct());
    }
    
    /**
     * @dataProvider trueFalseDataProvider
     * @param bool $setValue
     * @param bool $expected
     *
     * @return void
     */
    public function testHasAccessToOnlyOneApplication(bool $setValue, bool $expected) : void
    {
        $suiteUserProfile = new SuitesUserProfile();
        $suiteUserProfile->setHasAccessToOnlyOneApplication($setValue);
        
        self::assertSame($expected, $suiteUserProfile->hasAccessToOnlyOneApplication());
    }
    
    /**
     * @dataProvider trueFalseDataProvider
     * @param bool $setValue
     * @param bool $expected
     *
     * @return void
     */
    public function testIsSuiteRestricted(bool $setValue, bool $expected) : void
    {
        $suiteUserProfile = new SuitesUserProfile();
        $suiteUserProfile->setIsSuiteRestricted($setValue);
        
        self::assertSame($expected, $suiteUserProfile->isSuiteRestricted());
    }
    
    
    
    /**
     * @return array
     */
    public function stringDataProvider() : array
    {
        return [
            ['', ''],
            ['fo', 'fo'],
            ['bar', 'bar']
        ];
    }
    
    /**
     * @dataProvider stringDataProvider
     * @param string $setValue
     * @param string $expected
     *
     * @return void
     */
    public function testOnlyOneAppId(string $setValue, string $expected) : void
    {
        $suiteUserProfile = new SuitesUserProfile();
        $suiteUserProfile->setOnlyOneAppId($setValue);
        
        self::assertSame($expected, $suiteUserProfile->onlyOneAppId());
    }
}
