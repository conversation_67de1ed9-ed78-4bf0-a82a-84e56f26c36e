<?php

namespace tests\source\core\filter;

use AbstractFilterViewDialect;
use EntityException;
use EntityManager;
use ExpressionFault;
use FilterViewDataType;
use FilterViewFault;
use FilterViewField;
use FIlterViewFieldType;
use FilterViewOperation;
use FilterViewService;
use FilterViewValidator;
use Globals;
use IAException;
use UIFilterViewDialect;
use unitTest\core\UnitTestBaseContext;

/**
 * Class FilterViewTest
 *
 * Unit tests for testing FilterViewService class methods.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2020 Intacct Corporation
 * @group unit
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class FilterViewServiceIntegrationTest extends UnitTestBaseContext
{

    const MAX_TEXT_LENGTH = 5;

    const TEST_OBJECT_COUNT = 10;

    /* @var array $BROKEN_ENTITIES */
    private $BROKEN_ENTITIES = [ 'apreverse', 'arinvoicereverse', 'arinvoicesubtotals', 'arreverse', 'advance',
                                 'cmreverse',
                                 'documentapproval', 'entitydefault', 'glgraph', 'iafinancialtool', 'iagraphstool',
                                 'ietreverse', 'imspackagememcache', 'image', 'invoicereverse', 'kpigraphs',
                                 'paybyentity',
                                 'qcheckpayment', 'qcheckreverse', 'qdepositreverse', 'reverse', 'sorecursalescontract',
                                 'setup', 'subtotalglresolve',
                                 'costtype', 'standardcosttype', 'productionunits', 'pjestimate',
                                 'pjestimateentry', 'tasktype',
                                 'iaglacctgrpmember', 'iaglcoacatmember', 'iaglcompgrpmember',
                                 // broken queries
                                 'userrights', 'invoicerun', 'pjestimatetype',
                                 // ignore these, take forever to run
                                 'imspackagedetail', 'audittrail',
    ];

    /**
     * Clean all global errors before running each test
     */
    public function setUp(): void
    {
        // Clean all global errors
        Globals::$g->gErr->Clear();
    }

    /**
     * @throws FilterViewFault
     * @throws IAException
     */
    public function testAllFromRegistry()
    {
        $validEntities = [];
        // $emptyEntities = [];
        $invalidEntities = [];
        $failedQueries = [];
        $standardObjectMap = $this->accessPrivate('Util_StandardObjectRegistry', 'standardObjectMap');

        $metadataTestCounter = 0;
        $readDataTestCounter = 0;
        $eq = 0;
        $neq = 0;
        $r = 0;
        $oth = 0;
        $noData = 0;
        foreach ( $standardObjectMap as $entity => $info ) {
            if ( in_array($entity, $this->BROKEN_ENTITIES) ) {
                continue;
            }

            $distribution = $this->dataDistribution($entity);
            if ( $distribution !== null ) {
                $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
                $service->getFilterViewValidator()->setService($service);

                $queryParamsList = $this->distribution2queryParamsList($distribution);

                if ( ! empty($distribution) ) {
                    // there are records in the database for the given entity
                    $validEntities[] = $entity;

                    $metadataTestCounter += $this->validateGetFilteringMetadata($service);

                    $testResults = $this->validateGetFilteredData(
                        $service,
                        $queryParamsList,
                        $distribution,
                        $failedQueries
                    );

                    $readDataTestCounter += $testResults['tests'];
                    $eq += $testResults['equals'];
                    $neq += $testResults['not-equals'];
                    $r += $testResults['range'];
                    $oth += $testResults['others'];
                    $noData += $testResults['no-data'];
                } else {
                    // no data in the database for the given entity
                    // $emptyEntities[] = $entity;
                }
            } else {
                $invalidEntities[] = $entity;
            }

            if ( count($validEntities) > self::TEST_OBJECT_COUNT ) {
                break;
            }
        }

        LogToFile("Valid entities " . var_export($validEntities, 1));
        if ( ! empty($failedQueries) ) {
            LogToFile("Failed queries " . var_export($failedQueries, 1));
        }
        if ( ! empty($invalidEntities) ) {
            LogToFile("Invalid entities " . var_export($invalidEntities, 1));
        }
        LogToFile("============================================================");
        LogToFile("Number of equals tests - $eq");
        LogToFile("Number of non-equals  - $neq");
        LogToFile("Number of range tests - $r");
        LogToFile("Number of other tests - $oth");
        LogToFile("Number of no-data tests - $noData");
        LogToFile("============================================================");
        LogToFile("Total number of successful read tests - $readDataTestCounter");
        LogToFile("Number of successful metadata tests - $metadataTestCounter");
        LogToFile("============================================================");
    }

    /**
     * @param FilterViewService $service
     * @param array             $queryParamsList
     * @param array             $distribution
     * @param array             $failedQueries
     *
     * @return array
     */
    private function validateGetFilteredData(FilterViewService $service, array $queryParamsList, array $distribution,
                                             array & $failedQueries)
    {
        $testCounter = 0;
        $eq = 0;
        $neq = 0;
        $r = 0;
        $oth = 0;
        $noData = 0;
        foreach ( $queryParamsList as $uiQueryParams ) {
            try {
                $response = $service->getFilteredData($uiQueryParams);

                if ( empty($response['data']) ) {
                    $testCounter++;
                    $noData++;
                } else {
                    if ( $response === false ) {
                        $failedQueries[$service->getEntityManager()->_entity][] = $uiQueryParams;
                        continue;
                    }

                    $queryResults = $response['data'];

                    $validation = $this->validateQueryResults($uiQueryParams, $distribution, $queryResults);

                    $success = $validation['isvalid'];
                    $eq += $validation['equals'];
                    $neq += $validation['not-equals'];
                    $r += $validation['range'];
                    $oth += $validation['others'];

                    self::assertTrue($success);
                    $testCounter++;
                }
            } catch ( \Exception $e ) {
                LogToFile($e->getMessage());
                if ( strpos($e->getMessage(), 'Missing type for field') !== false ) {
                    // throw $e;
                    continue;
                }
            }
        }

        return [ 'tests'      => $testCounter,
                 'equals'     => $eq,
                 'not-equals' => $neq,
                 'range'      => $r,
                 'others'     => $oth,
                 'no-data'    => $noData ];
    }

    /**
     * @param array $uiQueryParams
     * @param array $distribution
     * @param array $queryResults
     *
     * @return array
     * @throws IAException
     */
    private function validateQueryResults(array $uiQueryParams, array $distribution, array $queryResults)
    {
        static $equals = 0;
        static $nonEquals = 0;
        static $range = 0;
        static $others = 0;
        $ret = true;
        $eq = 0;
        $neq = 0;
        $r = 0;
        $oth = 0;
        if ( isset($uiQueryParams[FilterViewService::FILTERS_KEY][0]) ) {
            $filter = $uiQueryParams[FilterViewService::FILTERS_KEY][0];
            switch ($filter[UIFilterViewDialect::OPERATION_KEY]) {
                case FilterViewOperation::EQUALS:
                    $ret = $this->validateEquals($distribution, $filter, $queryResults);
                    self::assertTrue($ret);
                    $eq++;
                    $equals++;
                    break;
                case FilterViewOperation::NOT_EQUAL_TO:
                    $ret = $this->validateNonEquals($distribution, $filter, $queryResults);
                    self::assertTrue($ret);
                    $nonEquals++;
                    $neq++;
                    break;
                case FilterViewOperation::RANGE:
                    $ret = $this->validateRange($filter, $queryResults);
                    self::assertTrue($ret);
                    $r++;
                    $range++;
                    break;
                default:
                    $others++;
                    $oth++;
                    $ret = true;
            }
        }

        LogToFile(">> Equals - $equals\t Non-equals - $nonEquals\t Range - $range\t Others - $others");

        return [ 'isvalid' => $ret, 'equals' => $eq, 'not-equals' => $neq, 'range' => $r, 'others' => $oth ];
    }

    /**
     * @param array $filter
     * @param array $queryResults
     *
     * @return bool
     */
    private function validateRange(array $filter, array $queryResults)
    {
        $ret = ( count($queryResults) === $filter['range-expected-record-count'] );

        return $ret;
    }

    /**
     * @param array $distribution
     * @param array $filter
     * @param array $queryResults
     *
     * @return bool
     * @throws IAException
     */
    private function validateNonEquals(array $distribution, array $filter, array $queryResults)
    {
        $fieldPath = $filter[UIFilterViewDialect::PATH_KEY];
        $info = $this->retrieveFieldInfoFromDistribution($distribution, $fieldPath);

        $ret = true;

        $expectedTotalNumberOfRecords = $info['counter']['null'] + $info['counter']['not-null'];
        if ( $expectedTotalNumberOfRecords > 0 && $expectedTotalNumberOfRecords === count($queryResults) ) {
            $filterValue = $filter[UIFilterViewDialect::VALUE_KEY];
            $expectedRecords = 0;
            foreach ( $info['values'] as $value => $numberOfRecords ) {
                if ( $value !== $filterValue ) {
                    $expectedRecords += $numberOfRecords;
                }
            }

            foreach ( $queryResults as $singleRecord ) {
                if ( isset($singleRecord[$fieldPath]) && $singleRecord[$fieldPath] !== $filterValue ) {
                    $expectedRecords--;
                }
            }

            $ret = ( $expectedRecords === 0 );
        }

        return $ret;
    }

    /**
     * @param array $distribution
     * @param array $filter
     * @param array $queryResults
     *
     * @return bool
     * @throws IAException
     */
    private function validateEquals(array $distribution, array $filter, array $queryResults)
    {
        $fieldPath = $filter[UIFilterViewDialect::PATH_KEY];
        $info = $this->retrieveFieldInfoFromDistribution($distribution, $fieldPath);

        $ret = true;

        $expectedTotalNumberOfRecords = $info['counter']['null'] + $info['counter']['not-null'];
        if ( $expectedTotalNumberOfRecords > 0 && $expectedTotalNumberOfRecords === count($queryResults) ) {
            $filterValue = $filter[UIFilterViewDialect::VALUE_KEY];
            $expectedRecords = $info['values'][$filterValue];
            foreach ( $queryResults as $singleRecord ) {
                if ( isset($singleRecord[$fieldPath]) && $singleRecord[$fieldPath] === $filterValue ) {
                    $expectedRecords--;
                }
            }

            $ret = ( $expectedRecords === 0 );
        }

        return $ret;
    }

    /**
     * @param array  $distribution
     * @param string $fieldPath
     *
     * @return array
     * @throws IAException
     */
    private function retrieveFieldInfoFromDistribution(array $distribution, string $fieldPath)
    {
        foreach ( $distribution as $dataTypeInfo ) {
            foreach ( $dataTypeInfo as $aField => $fieldInfo ) {
                if ( $fieldPath === $aField ) {
                    return $fieldInfo;
                }
            }
        }
        throw new IAException("Field $fieldPath has no distribution");
    }

    /**
     * @param array $distribution
     *
     * @return array
     * @throws FilterViewFault
     */
    private function distribution2queryParamsList(array $distribution)
    {
        $ret = [];

        foreach ( $distribution as $dataType => $dataTypeDistribution ) {
            $dbOperations = FilterViewDataType::getDbOperations($dataType);

            foreach ( $dataTypeDistribution as $path => $info ) {
                $numberOfRecords = $info['counter']['null'] + $info['counter']['not-null'];
                if ( false === is_countable($info['values'] ?? false)
                    || count($info['values']) === $numberOfRecords
                ) {
                    continue;
                }

                $allSortedValues = array_keys($info['sorted_values']);
                $allSortedKeys = array_keys($info['sorted_keys']);

                foreach ( $dbOperations as $operation => $opInfo ) {
                    $filter = [];
                    $filter[UIFilterViewDialect::PATH_KEY] = $path;
                    $filter[UIFilterViewDialect::OPERATION_KEY] = $operation;
                    if ( ! $opInfo['unary'] ) {
                        if ( $opInfo['param-type'] === 'array' ) {
                            if ( $operation === 'range' ) {
                                if ( count($allSortedValues) == 0 ) {
                                    continue;
                                } else if ( count($allSortedValues) == 1 ) {
                                    $filter[UIFilterViewDialect::VALUE_KEY] = [ $allSortedKeys[0], $allSortedKeys[0] ];
                                    $rangeExpectedRecordCount = $info['sorted_keys'][$allSortedKeys[0]];
                                } else {
                                    // >= 2
                                    $filter[UIFilterViewDialect::VALUE_KEY] = [ $allSortedKeys[0], $allSortedKeys[1] ];
                                    $rangeExpectedRecordCount = $info['sorted_keys'][$allSortedKeys[0]]
                                                                + $info['sorted_keys'][$allSortedKeys[1]];
                                }
                                $filter['range-expected-record-count'] = $rangeExpectedRecordCount;
                            } else {
                                $filter[UIFilterViewDialect::VALUE_KEY] = [ $allSortedValues[0] ];
                            }
                        } else {
                            $validValue = $allSortedValues[0];
                            if ( $dataType == FilterViewDataType::TEXT ) {
                                if ( $operation === FilterViewOperation::STARTS_WITH ) {
                                    $validValue = substr($validValue, 0, self::MAX_TEXT_LENGTH);
                                } else if ( $operation === FilterViewOperation::ENDS_WITH ) {
                                    $validValue = substr(
                                        $validValue,
                                        strlen($validValue) - self::MAX_TEXT_LENGTH,
                                        self::MAX_TEXT_LENGTH
                                    );
                                } else if ( $operation === FilterViewOperation::CONTAINS ) {
                                    $start =
                                        strlen($validValue) >= self::MAX_TEXT_LENGTH ? self::MAX_TEXT_LENGTH - 3 : 0;
                                    $end = $start + self::MAX_TEXT_LENGTH;
                                    $validValue = substr($validValue, $start, $end);
                                }
                            }
                            $filter[UIFilterViewDialect::VALUE_KEY] = $validValue;
                        }
                    }

                    $filters[] = $filter;

                    $params = [
                        FilterViewService::SELECTS_KEY => [ $path ],
                        FilterViewService::FILTERS_KEY => $filters,
                    ];

                    unset($filters);

                    // $json = json_encode($params);
                    $ret[] = $params;
                }
            }
        }

        return $ret;
    }

    /**
     * @param string $entity
     *
     * @return array
     * @throws \Exception
     */
    private function dataDistribution(string $entity)
    {
        Globals::$g->gErr->Clear();

        try {
            $manager = Globals::$g->gManagerFactory->getManager($entity);
            $fields = \LookupService::getManagerQueryableFields($manager);
        } catch ( EntityException $e ) {
            logToFileWarning($e->getMessage(), true);

            return null;
        }

        if ( ( $key = array_search('RECORD_URL', $fields) ) !== false ) {
            // remove the RECORD_URL field
            unset($fields[$key]);

            if ( empty($fields) ) {
                return null;
            }
        }

        $fields = array_slice($fields, 0, 20);
        $list = $manager->strictGetList([ 'selects' => $fields ]);

        if ( Globals::$g->gErr->GetNextError() !== null ) {
            return null;
        }

        $dataDistribution = [];
        foreach ( $list as $record ) {
            foreach ( $record as $field => $value ) {
                $fieldInfo = $this->getFieldInfo($manager, $field);

                if ( $fieldInfo === null || ! isset($fieldInfo[FilterViewField::FIELD_INFO_TYPE]) ) {
                    continue;
                }

                $fieldDataType = FIlterViewFieldType::getFieldDataType($fieldInfo[FilterViewField::FIELD_INFO_TYPE]);

                if ( $fieldDataType === null || ! FilterViewDataType::isValidDataType($fieldDataType) ) {
                    continue;
                }

                if ( $value === '' ) {
                    $value = null;
                }

                if ( $value === null ) {
                    $counter = $dataDistribution[$fieldDataType][$field]['counter']['null'] ?? 0;
                    $dataDistribution[$fieldDataType][$field]['counter']['null'] = $counter + 1;
                } else {
                    $counter = $dataDistribution[$fieldDataType][$field]['counter']['not-null'] ?? 0;
                    $dataDistribution[$fieldDataType][$field]['counter']['not-null'] = $counter + 1;

                    $counter = $dataDistribution[$fieldDataType][$field]['values'][$value] ?? 0;
                    $dataDistribution[$fieldDataType][$field]['values'][$value] = $counter + 1;
                }
            }
        }

        foreach ( $dataDistribution as $dataType => &$distribution ) {
            foreach ( $distribution as &$info ) {
                $values = $info['values'];

                if (is_array($values ?? false)) {
                    // Sort the array of collected values by values
                    asort($values);
                    $info['sorted_values'] = $values;

                    if ( in_array($dataType, [ 'date', 'timestamp' ]) ) {
                        // Use a special date/timestamp comparator to sort the array of values
                        // by keys
                        uksort($values, [ $this, "compareByTimeStamp" ]);
                    } else {
                        // Sort array of values by keys
                        ksort($values);
                    }
                }

                $info['sorted_keys'] = $values;
            }
        }

        return $dataDistribution;
    }

    /**
     * Date / timestamp comparator. Compares two strings representing date / timestamp
     *
     * @param string $time1
     * @param string $time2
     *
     * @return int
     */
    private function compareByTimeStamp(string $time1, string $time2)
    {
        if ( strtotime($time1) > strtotime($time2) ) {
            return 1;
        } else if ( strtotime($time1) < strtotime($time2) ) {
            return -1;
        }

        return 0;
    }

    /**
     * @param EntityManager $manager
     * @param string        $path
     *
     * @return mixed
     */
    private function getFieldInfo(EntityManager $manager, string $path)
    {
        static $fieldInfos = [];
        $key = $manager->_entity . '_' . ( $manager->_docType ?? 'no-doc-type' ) . '_' . $path;
        if ( ! $fieldInfos[$key] ) {
            $fieldInfo = $manager->GetFieldInfo($path);
            $fieldInfos[$key] = $fieldInfo;
        }

        return $fieldInfos[$key];
    }

    /**
     * Confirms that reading all filter view records from database returns non-empty collection.
     *
     * @param FilterViewService $service
     *
     * @return int
     * @throws IAException
     */
    public function validateGetFilteringMetadata(FilterViewService $service)
    {
        /** @var \FilterViewMetadata $metadata */
        /** @noinspection PhpUndefinedMethodInspection */
        $metadata = $service->getFilterViewDialect()->getFilterMetadataFromEntity('__default');
        $counter = 0;

        self::assertNotNull($metadata);

        /** @noinspection PhpUndefinedMethodInspection */
        $allFields = $metadata->getFilterViewFields();
        foreach ( $allFields as $info ) {
            self::assertNotNull($info->getPath());
            self::assertNotNull($info->getLabel());

            self::assertNotEmpty($info->getFilteringOptions());
            foreach ( $info->getFilteringOptions() as $option ) {
                self::assertNotNull($option['component']);
                self::assertNotNull($option['operation']['value']);
                self::assertNotNull($option['operation']['label']);

                $counter++;
            }
        }

        return $counter;
    }

    // - - - - - - - Testing on manually provided test cases - - - - - - - -

    /**
     * @dataProvider expressionDataProvider
     *
     * @param array $testInput
     */
    public function testExpression($testInput)
    {
        LogToFile("testInput => " . var_export($testInput, 1));
        $this->singleQueryTest($testInput);
    }

    /**
     * @dataProvider queryParametersDataProvider
     *
     * @param array $testInput
     *
     * @throws IAException
     */
    public function _testQueryParamsValidator($testInput)
    {
        // $manager = Globals::$g->gManagerFactory->getManager('apbill');
        // $manager = Globals::$g->gManagerFactory->getManager('filterview');
        // $list = $manager->GetList();

        $service = new FilterViewService($testInput['input']['entity'], $testInput['input']['doctype']);
        $service->getFilterViewValidator()->setService($service);
        $uiQueryParams = $testInput['input']['query-params'];
        $validator = new FilterViewValidator($service->getEntityManager());
        $b = $validator->validateQueryParams($uiQueryParams);

        self::assertTrue($b);
    }

    /**
     * @dataProvider queryParametersDataProvider
     *
     * @param array $testInput
     */
    public function testManualQueries($testInput)
    {
        $this->singleQueryTest($testInput);
    }

    /**
     * @param array $testInput
     *
     * @throws IAException
     */
    private function singleQueryTest(array $testInput)
    {
        $service = new FilterViewService($testInput['input']['entity'],
            $testInput['input']['doctype'],
            AbstractFilterViewDialect::UI);
        $service->getEntityManager()
            ->_schemas[$testInput['input']['entity']]['filtercontexts']['system']['__default']['views'] = [];
        $service->getFilterViewValidator()->setService($service);

        $uiQueryParams = $testInput['input']['query-params'];
        $response = $service->getFilteredData($uiQueryParams);
        self::assertTrue($response['success']);
        $queryResults = $response['data'];

        $expectedNumberOfRecords = $testInput['validation']['total-records'];
        self::assertEquals($expectedNumberOfRecords, count($queryResults));

        if ( $expectedNumberOfRecords > 0 ) {
            $expectedRecords = $testInput['validation']['records'];
            if ( ! empty($expectedRecords) ) {
                foreach ( $expectedRecords as $index => $expectedRecord ) {
                    self::assertTrue(isset($queryResults[$index]));
                    $actualRecord = $queryResults[$index];
                    foreach ( $expectedRecord as $expectedColumn ) {
                        self::assertTrue(isset($actualRecord[$expectedColumn['field']]));
                        self::assertEquals($expectedColumn['value'], $actualRecord[$expectedColumn['field']]);
                    }
                }
            }
        }
    }

    /**
     * @return array
     */
    public function expressionDataProvider()
    {
        $queries = [
            [
                [
                    'input' =>
                        [ 'entity'       => 'userinfo', 'doctype' => null,
                          'query-params' => [
                              'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
                              'filters'    => [
                                  [ 'key' => 3, 'path'  => 'LOGINID', 'operation' => FilterViewOperation::EQUALS,
                                         'value' => 'Admin' ],
                                  [ 'key' => 2, 'path'  => 'STATUS', 'operation' => FilterViewOperation::EQUALS,
                                         'value' => 'active' ],
                                  [ 'key' => 1, 'path'  => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS,
                                         'value' => 'business user' ],
                                  [ 'key' => 4, 'path'  => 'RECORDNO', 'operation' => FilterViewOperation::RANGE,
                                         'value' => [ 1, 5 ] ],
                              ],
                              'expression' => '(1 or 2 or 4) and 3',
                          ],
                        ],

                    'validation' => [
                        'total-records' => 1,
                        'records'       => [
                            [ [ 'field' => 'RECORDNO', 'value' => '1' ], [ 'field' => 'LOGINID', 'value' => 'Admin' ] ],
                        ],
                    ],
                ],
            ],
            [
                [
                    'input' =>
                        [ 'entity'       => 'userinfo', 'doctype' => null,
                          'query-params' => [
                              'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
                              'filters'    => [
                                  [ 'key'   => 3, 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS,
                                    'value' => 'Admin' ],
                                  [ 'key'   => 2, 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS,
                                    'value' => 'active' ],
                                  [ 'key'   => 1, 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS,
                                    'value' => 'business user' ],
                                  [ 'key'   => 4, 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE,
                                    'value' => [ 1, 5 ] ],
                              ],
                              'expression' => '(1 or 2 or 4) and 3',
                          ],
                        ],

                    'validation' => [
                        'total-records' => 1,
                        'records'       => [
                            [ [ 'field' => 'RECORDNO', 'value' => '1' ], [ 'field' => 'LOGINID', 'value' => 'Admin' ] ],
                        ],
                    ],
                ],
            ],
            [
                [
                    'input' =>
                        [ 'entity'       => 'userinfo', 'doctype' => null,
                          'query-params' => [
                              'selects' => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
                              'filters' => [
                                  [ 'path'  => 'LOGINID', 'operation' => FilterViewOperation::EQUALS,
                                    'value' => 'Admin' ],
                                  [ 'path'  => 'STATUS', 'operation' => FilterViewOperation::EQUALS,
                                    'value' => 'active' ],
                                  [ 'path'  => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS,
                                    'value' => 'business user' ],
                                  [ 'path'  => 'RECORDNO', 'operation' => FilterViewOperation::RANGE,
                                    'value' => [ 1, 5 ] ],
                              ],
                          ],
                        ],

                    'validation' => [
                        'total-records' => 1,
                        'records'       => [
                            [ [ 'field' => 'RECORDNO', 'value' => '1' ], [ 'field' => 'LOGINID', 'value' => 'Admin' ] ],
                        ],
                    ],
                ],
            ],
        ];

        return $queries;
    }

    /**
     * @throws FilterViewFault
     */
    function testExpressionIgnoreableFilterRecords()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                [ 'key' => 3, 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                [ 'key' => 99, 'path' => '', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                [ 'key' => 6 ],
                [ 'key' => 2, 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                [ 'key'   => 1, 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS,
                  'value' => 'business user' ],
                [ 'key' => 4, 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
                [],
                [],
                [],
                [],
            ],
            'expression' => '(1 or 2 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);

        $response = $service->getFilteredData($uiQueryParams);
        self::assertTrue($response['success']);
        $queryResults = $response['data'];

        self::assertNotEmpty($queryResults);

        foreach ( $queryResults as $user ) {
            self::assertTrue($user['LOGINID'] === 'Admin'
                              && $user['STATUS'] === 'active'
                              && $user['USERTYPE'] === 'business user'
                              && $user['RECORDNO'] >= 1
                              && $user['RECORDNO'] <= 5);
        }
    }

    /**
     * Single test with the trivial or expression
     *
     * @throws FilterViewFault
     */
    function testSingleORExpression()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 2, 5 ] ],
            ],
            'expression' => 'or',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);

        $response = $service->getFilteredData($uiQueryParams);
        self::assertTrue($response['success']);
        $queryResults = $response['data'];

        self::assertNotEmpty($queryResults);

        foreach ( $queryResults as $user ) {
            self::assertTrue($user['LOGINID'] === 'Admin' || ( $user['RECORDNO'] >= 2 && $user['RECORDNO'] <= 5 ));
        }
    }

    /**
     * Single test with the trivial OR expression (with caps)
     *
     * @throws FilterViewFault
     */
    function testSingleORExpressionWithCaps()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 2, 5 ] ],
            ],
            'expression' => 'OR',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()
                ->setService($service);

        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_INVALID_EXPRESSION, $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::INVALID_EXPRESSION, $response['errors'][0]['code']);
    }

    /**
     * Missing operator (boolean), instead got opening brace.
     *
     * @throws IAException
     */
    function testIncorrectOpeningBraceExpectOperator()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3 => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                2 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1 => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4 => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '1(2 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_MESSAGE_INVALID_EXPECTING_OPERATOR,
            $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::INVALID_EXPECTING_OPERATOR, $response['errors'][0]['code']);
    }

    /**
     * Single test with complex expression with mixed cases for 'or' and 'and' operators
     *
     * the expression is valid expect for the fact that the 'or' and 'and' operators are NOT lower case
     *
     * @throws IAException
     */
    function testComplexExpressionWithMixedCases()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3 => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                2 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1 => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4 => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '1 OR (2 Or 4) AnD 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()
                ->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_INVALID_EXPRESSION,
                           $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::INVALID_EXPRESSION, $response['errors'][0]['code']);
    }

    /**
     * Missing operator (boolean), instead got opening brace.
     *
     * @throws IAException
     */
    function testInvalidExpression()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3     => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                'abc' => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1     => [ 'path'  => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS,
                           'value' => 'business user' ],
                4     => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '(abc or 2 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_INVALID_EXPRESSION, $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::INVALID_EXPRESSION, $response['errors'][0]['code']);
    }

    /**
     * Missing operator (boolean), instead got opening brace.
     *
     * @throws IAException
     */
    function testInvalidOperand()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3 => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                2 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1 => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4 => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '(1 or )2 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_MESSAGE_INVALID_OPERAND, $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::INVALID_OPERAND, $response['errors'][0]['code']);
    }

    /**
     * Negative test for the invalid single filter
     *
     * @throws IAException
     */
    function testInvalidFilterOperation()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                [ 'key' => 3, 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                [ 'key' => 2, 'path' => 'STATUS', 'operation' => FilterViewOperation::ON_OR_AFTER, 'value' => 'active' ],
                [ 'key' => 1, 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                [ 'key' => 4, 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '(1 or 2 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals('Enter a valid filtering operation in the filter STATUS.',
            $response['errors'][0]['message']);
        self::assertEquals(FilterViewValidator::INVALID_OPERATION, $response['errors'][0]['code']);
    }

    /**
     * Negative test for the invalid expression, got operator while expecting operand
     *
     * @throws IAException
     */
    function testExpressionInvalidOperator()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3 => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                2 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1 => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4 => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '(1 or and 2 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_MESSAGE_INVALID_OPERATOR, $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::INVALID_OPERATOR, $response['errors'][0]['code']);
    }

    /**
     * Negative test when the expression is invalid, opening and closing braces do not match
     *
     * @throws IAException
     */
    function testExpressionMismatchedParenthesis()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3 => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                2 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1 => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4 => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '((1 or 2 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_MESSAGE_MISMATCHED_PARENTHESIS, $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::MISMATCHED_PARENTHESIS, $response['errors'][0]['code']);
    }

    /**
     * Negative test when the expression is invalid, opening and closing braces do not match
     *
     * @throws IAException
     */
    function testExpressionMismatchedParenthesisTwo()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3 => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                2 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1 => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4 => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '(1 or 2) or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals(ExpressionFault::ERROR_MESSAGE_MISMATCHED_PARENTHESIS, $response['errors'][0]['message']);
        self::assertEquals(ExpressionFault::MISMATCHED_PARENTHESIS, $response['errors'][0]['code']);
    }

    /**
     * Negative test when the expression is referencing a filter that hasn't been specified
     *
     * @throws IAException
     */
    function testExpressionNonExistentFilter()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3  => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                99 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1  => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4  => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '(1 or 2 and 66 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals($response['errors'][0]['message'], 'Replace invalid filter references [2, 66] with valid filter references.');
        self::assertEquals(FilterViewValidator::NO_REFERENCE_FILTER, $response['errors'][0]['code']);
        self::assertNotEmpty($response['errors'][0]['keys']);
    }

    /**
     * Negative test when the expression is referencing a filter that hasn't been specified
     *
     * @throws IAException
     */
    function testExpressionExtraFilter()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                3  => [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                99 => [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                1  => [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                4  => [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => '(1 or 4) and 3',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($uiQueryParams);
        self::assertFalse($response['success']);

        self::assertEquals('Add missing filter expressions: [99].', $response['errors'][0]['message']);
        self::assertEquals(FilterViewValidator::MISSING_FILTER_IN_EXPRESSION, $response['errors'][0]['code']);
        self::assertNotEmpty($response['errors'][0]['keys']);
    }

    /**
     * Single test with the trivial AND expression
     *
     * @throws FilterViewFault
     */
    function testSingleANDExpression()
    {
        $uiQueryParams = [
            'selects'    => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters'    => [
                [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
            'expression' => 'and',
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);

        $response = $service->getFilteredData($uiQueryParams);
        self::assertTrue($response['success']);
        $queryResults = $response['data'];

        self::assertNotEmpty($queryResults);

        foreach ( $queryResults as $user ) {
            self::assertTrue($user['LOGINID'] === 'Admin'
                              && $user['STATUS'] === 'active'
                              && $user['USERTYPE'] === 'business user'
                              && $user['RECORDNO'] >= 1
                              && $user['RECORDNO'] <= 5);
        }
    }

    /**
     * Simple test with NO expression.
     *
     * @throws FilterViewFault
     */
    function testNoExpression()
    {
        $uiQueryParams = [
            'selects' => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION', 'ADMIN', 'USERTYPE', 'STATUS' ],
            'filters' => [
                [ 'path' => 'LOGINID', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Admin' ],
                [ 'path' => 'STATUS', 'operation' => FilterViewOperation::EQUALS, 'value' => 'active' ],
                [ 'path' => 'USERTYPE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'business user' ],
                [ 'path' => 'RECORDNO', 'operation' => FilterViewOperation::RANGE, 'value' => [ 1, 5 ] ],
            ],
        ];

        $entity = 'userinfo';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);

        $response = $service->getFilteredData($uiQueryParams);
        self::assertTrue($response['success']);
        $queryResults = $response['data'];

        self::assertNotEmpty($queryResults);
    }

    /**
     * @dataProvider getFiltersForAllowedOperations
     *
     * @param array  $queryParams
     * @param array  $allowedOperations
     * @param string $entity
     * @param bool   $expected
     *
     * @throws FilterViewFault
     * @throws IAException
     */
    public function testValidateAllowedOperations(
        array $queryParams,
        array $allowedOperations,
        string $entity,
        bool $expected
    )
    {
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $entityManager = $service->getEntityManager();
        // save the previous allowed operations as EM will be reused
        $prevAllowedOperations = $entityManager->_schemas[$entity]['filtercontexts']['system']['__default']['allowedOperations'];
        $entityManager->_schemas[$entity]['filtercontexts']['system']['__default']['allowedOperations'] =
            $allowedOperations;
        $entityManager->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];

        $service->setEntityManager($entityManager);
        $validator = new FilterViewValidator($service->getEntityManager());
        $validator->setService($service);
        self::assertEquals($expected, $validator->validateQueryParams($queryParams));
        // reset back
        $entityManager->_schemas[$entity]['filtercontexts']['system']['__default']['allowedOperations'] =
            $prevAllowedOperations;
    }

    /**
     * @dataProvider getFilters
     *
     * @param array  $queryParams
     * @param array  $views
     * @param array  $mandatoryFilters
     * @param string $entity
     * @param bool   $expected
     *
     * @throws FilterViewFault
     * @throws IAException
     */
    public function testValidateMandatoryFilters(
        array $queryParams,
        array $views,
        array $mandatoryFilters,
        string $entity,
        bool $expected
    )
    {
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $entityManager = $service->getEntityManager();
        $entityManager->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = $views;
        $entityManager->_schemas[$entity]['filtercontexts']['system']['__default'][FilterViewService::MANDATORY_FILTERS] =
            $mandatoryFilters;

        $service->setEntityManager($entityManager);
        $validator = new FilterViewValidator($service->getEntityManager());
        $validator->setService($service);
        self::assertEquals($expected, $validator->validateQueryParams($queryParams));
    }

    /**
     * @return array
     */
    public function queryParametersDataProvider()
    {
        // path - FilterViewService::PATH_KEY
        // operation - FilterViewService::OPERATION_KEY
        // value - FilterViewService::VALUE_KEY

        $queries = [
            [
                [
                    'input'      =>
                        [ 'entity'       => 'userinfo', 'doctype' => null,
                          'query-params' => [
                              'selects' => [ 'RECORDNO', 'LOGINID', 'DESCRIPTION' ],
                              'filters' => [
                                  [ 'path'  => 'LOGINID', 'operation' => FilterViewOperation::EQUALS,
                                    'value' => 'Admin' ],
                              ],
                          ],
                        ],
                    'validation' => [
                        'total-records' => 1,
                        'records'       => [
                            [ [ 'field' => 'RECORDNO', 'value' => '1' ] ],
                        ],
                    ],
                ],
            ],
            /*
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path'  => 'VENDORNAME', 'operation' => FilterViewOperation::EQUALS, 'value' => 'GL Posting' ]
                                ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 1,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '288' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path'  => 'STATE', 'operation' => FilterViewOperation::NOT_EQUAL_TO, 'value' => 'Posted' ]
                                ],
                                'orders' =>  [ [ 'RECORDNO', 'desc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 8,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '286' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '175' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '145' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '142' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '141' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '140' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '136' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '129' ] ],

                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path'  => 'TOTALENTERED', 'operation' => FilterViewOperation::LESS_OR_EQUAL, 'value' => '10' ]
                                ],
                                'orders' =>  [ [ 'RECORDNO', 'asc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 2,
                        'records' => [

                            [ [ 'field' => 'RECORDNO', 'value' => '228' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '286' ] ],
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path'  => 'TOTALENTERED', 'operation' => FilterViewOperation::GREATER_OR_EQUAL, 'value' => '20000' ]
                                ],
                                'orders' =>  [ [ 'WHENCREATED', 'asc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 5,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '139' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '171' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '170' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '173' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '167' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path'  => 'RECORDID', 'operation' => FilterViewOperation::IS_BLANK ]
                                ],
                                'orders' =>  [ [ 'WHENCREATED', 'desc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 4,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '288' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '230' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '228' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '232' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path'  => 'DESCRIPTION2', 'operation' => FilterViewOperation::IS_NOT_BLANK ]
                                ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 1,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '288' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO', 'VENDORID' ],
                                'filters' => [
                                    [ 'path'  => 'WHENCREATED', 'operation' => FilterViewOperation::ON_OR_BEFORE, 'value' => '08/31/2000' ]
                                ],
                                'orders' =>  [ [ 'VENDORID', 'asc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 11,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '140' ], [ 'field' => 'VENDORID', 'value' => '201' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '141' ], [ 'field' => 'VENDORID', 'value' => '202' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '137' ], [ 'field' => 'VENDORID', 'value' => '203' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '145' ], [ 'field' => 'VENDORID', 'value' => '204' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '155' ], [ 'field' => 'VENDORID', 'value' => '204' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '144' ], [ 'field' => 'VENDORID', 'value' => '205' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '139' ], [ 'field' => 'VENDORID', 'value' => '206' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '143' ], [ 'field' => 'VENDORID', 'value' => '207' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '142' ], [ 'field' => 'VENDORID', 'value' => '208' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '138' ], [ 'field' => 'VENDORID', 'value' => '210' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '136' ], [ 'field' => 'VENDORID', 'value' => '213' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO', 'VENDORID' ],
                                'filters' => [
                                    [ 'path'  => 'WHENCREATED', 'operation' => FilterViewOperation::ON_OR_AFTER, 'value' => '12/29/2011' ]
                                ],
                                'orders' =>  [ [ 'VENDORID', 'desc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 2,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '288' ], [ 'field' => 'VENDORID', 'value' => 'GLPOSTING' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '286' ], [ 'field' => 'VENDORID', 'value' => '204' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'VENDORNAME', 'RECORDID', 'TOTALENTERED' ],
                                'filters' => [
                                    [ 'path'  => 'VENDORNAME', 'operation' => FilterViewOperation::STARTS_WITH, 'value' => 'P' ]
                                ],
                                'orders' =>  [ [ 'VENDORNAME', 'desc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 11,
                        'records' => [
                            [ [ 'field' => 'VENDORNAME', 'value' => 'PG & E' ], [ 'field' => 'RECORDID', 'value' => 'Bill#6 #5' ], [ 'field' => 'TOTALENTERED', 'value' => '91.92' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'PG & E' ], [ 'field' => 'RECORDID', 'value' => 'Bill#4 #5' ], [ 'field' => 'TOTALENTERED', 'value' => '2273.17' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'PG & E' ], [ 'field' => 'RECORDID', 'value' => 'Bill#5 #5' ], [ 'field' => 'TOTALENTERED', 'value' => '22087.54' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'PG & E' ], [ 'field' => 'RECORDID', 'value' => 'Bill#3 #5' ], [ 'field' => 'TOTALENTERED', 'value' => '5469.69' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'PG & E' ], [ 'field' => 'RECORDID', 'value' => 'Bill#2 - #5' ], [ 'field' => 'TOTALENTERED', 'value' => '8550' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#5 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '27073.88' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#1-#6' ], [ 'field' => 'TOTALENTERED', 'value' => '301.56' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#6 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '1877.57' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#2 - #6' ], [ 'field' => 'TOTALENTERED', 'value' => '9000.89' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#3 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '7771.25' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#4 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '74.81' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'VENDORNAME', 'RECORDID', 'TOTALENTERED' ],
                                'filters' => [
                                    [ 'path'  => 'VENDORNAME', 'operation' => FilterViewOperation::ENDS_WITH, 'value' => 'll' ]
                                ],
                                'orders' =>  [ [ 'VENDORNAME', 'desc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 12,
                        'records' => [
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Quick and Easy Payroll' ], [ 'field' => 'RECORDID', 'value' => 'Bill#4 #7' ], [ 'field' => 'TOTALENTERED', 'value' => '585.05' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Quick and Easy Payroll' ], [ 'field' => 'RECORDID', 'value' => 'Bill#6 #7' ], [ 'field' => 'TOTALENTERED', 'value' => '585.05' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Quick and Easy Payroll' ], [ 'field' => 'RECORDID', 'value' => 'Bill#5 #7' ], [ 'field' => 'TOTALENTERED', 'value' => '534.69' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Quick and Easy Payroll' ], [ 'field' => 'RECORDID', 'value' => 'Bill#2 - #7' ], [ 'field' => 'TOTALENTERED', 'value' => '6591.89' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Quick and Easy Payroll' ], [ 'field' => 'RECORDID', 'value' => 'Bill#1-#7' ], [ 'field' => 'TOTALENTERED', 'value' => '7085.55' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Quick and Easy Payroll' ], [ 'field' => 'RECORDID', 'value' => 'Bill#3 #7' ], [ 'field' => 'TOTALENTERED', 'value' => '1534.69' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#6 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '1877.57' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#4 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '74.81' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#2 - #6' ], [ 'field' => 'TOTALENTERED', 'value' => '9000.89' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#5 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '27073.88' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#1-#6' ], [ 'field' => 'TOTALENTERED', 'value' => '301.56' ] ],
                            [ [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ], [ 'field' => 'RECORDID', 'value' => 'Bill#3 #6' ], [ 'field' => 'TOTALENTERED', 'value' => '7771.25' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO', 'VENDORNAME', 'RECORDID', 'TOTALENTERED' ],
                                'filters' => [
                                    [ 'path'  => 'RECORDID', 'operation' => FilterViewOperation::CONTAINS, 'value' => 'Reversed' ]
                                ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 1,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '286' ], [ 'field' => 'VENDORNAME', 'value' => 'Yamaguchi Garden Supply' ],
                                [ 'field' => 'RECORDID', 'value' => 'Reversed - Bill#2 - #10' ], [ 'field' => 'TOTALENTERED', 'value' => '-1025' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO', 'PRBATCH' ],
                                'filters' => [
                                    [ 'path'  => 'PRBATCH', 'operation' => FilterViewOperation::DOES_NOT_CONTAIN, 'value' => 'Bill' ]
                                ],
                                'orders' =>  [ [ 'TOTALENTERED', 'desc' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 4,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '230' ], [ 'field' => 'PRBATCH', 'value' => 'Cash Management Transactions: March 2002 Batch' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '232' ], [ 'field' => 'PRBATCH', 'value' => 'Cash Management Transactions: October 2001 Batch' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '288' ], [ 'field' => 'PRBATCH', 'value' => 'Vendor Invoice: 02/23/2012 Batch' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '228' ], [ 'field' => 'PRBATCH', 'value' => 'Cash Management Transactions: February 2002 Batch' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO', 'VENDORNAME' ],
                                'filters' => [
                                    [ 'path'  => 'VENDORNAME', 'operation' => FilterViewOperation::IS_ONE_OF, 'value' => ['Able and Sons, Accountants', 'GL Posting', 'Master Card Vendor'] ]
                                ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 4,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '124' ], [ 'field' => 'VENDORNAME', 'value' => 'Able and Sons, Accountants' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '232' ], [ 'field' => 'VENDORNAME', 'value' => 'Master Card Vendor' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '230' ], [ 'field' => 'VENDORNAME', 'value' => 'Master Card Vendor' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '288' ], [ 'field' => 'VENDORNAME', 'value' => 'GL Posting' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO', 'STATE' ],
                                'filters' => [
                                    [ 'path'  => 'STATE', 'operation' => FilterViewOperation::IS_NOT_ONE_OF, 'value' => ['Paid', 'Posted', 'Selected'] ]
                                ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 2,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '145' ], [ 'field' => 'STATE', 'value' => 'Reversed' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '286' ], [ 'field' => 'STATE', 'value' => 'Reversal' ] ]
                        ]
                    ]
                ]
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO', 'VENDORNAME' ],
                                'filters' => [
                                    [ 'path' => 'STATE', 'operation' => FilterViewOperation::IS_ONE_OF, 'value' => ['Posted', 'Paid'] ],
                                    [ 'path' => 'VENDORNAME', 'operation' => FilterViewOperation::IS_ONE_OF, 'value' => ['Pac Bell', 'PG & E', 'Ford Motor Credit', 'Office Supply and Copier Co.', 'NCS, Inc.'] ],
                                    [ 'path' => 'DOCNUMBER', 'operation' => FilterViewOperation::STARTS_WITH, 'value' => 'PO' ],
                                    [ 'path' => 'WHENCREATED', 'operation' => FilterViewOperation::GREATER_THAN, 'value' => '08/15/2001' ]
                                ],
                                'orders' =>  [ [ 'VENDORNAME' ] ]
                            ]
                        ],
                    'validation' => [
                        'total-records' => 4,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '147' ], [ 'field' => 'VENDORNAME', 'value' => 'NCS, Inc.' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '148' ], [ 'field' => 'VENDORNAME', 'value' => 'Office Supply and Copier Co.' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '151' ], [ 'field' => 'VENDORNAME', 'value' => 'Pac Bell' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '150' ], [ 'field' => 'VENDORNAME', 'value' => 'PG & E' ] ]
                        ]
                    ]
                ],
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'VENDORID' ],
                                'filters' => [
                                    [ 'path' => 'STATE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Posted' ],
                                    [ 'path' => 'VENDORNAME', 'operation' => FilterViewOperation::IS_NOT_BLANK ],
                                    [ 'path' => 'VENDORID', 'operation' => FilterViewOperation::CONTAINS, 'value' => 'POST' ]
                                ],
                            ],
                        ],
                    'validation' => [
                        'total-records' => 1,
                        'records' => [
                            [ [ 'field' => 'VENDORID', 'value' => 'GLPOSTING ' ] ]
                        ]
                    ],
                ],
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'apbill', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path' => 'STATE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Posted' ],
                                    [ 'path' => 'VENDORNAME', 'operation' => FilterViewOperation::EQUALS, 'value' => 'PG & E' ],
                                    [ 'path' => 'TOTALENTERED', 'operation' => FilterViewOperation::GREATER_OR_EQUAL, 'value' => '1000' ],
                                    [ 'path' => 'WHENCREATED', 'operation' => FilterViewOperation::GREATER_THAN, 'value' => '08/31/2001' ]
                                ],
                                'orders' =>  [ [ 'RECORDNO', 'desc' ] ]
                            ],
                        ],
                    'validation' => [
                        'total-records' => 2,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '170' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '160' ] ]
                        ]
                    ],
                ],
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'project', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path' => 'CUSTOMER.ARACCOUNT.TITLE', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Sales-Scrap' ]
                                ]
                            ],
                        ],
                    'validation' => [
                        'total-records' => 1,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '1' ] ]
                        ]
                    ],
                ],
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'project', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path' => 'NAME', 'operation' => FilterViewOperation::EQUALS, 'value' => 'Created by XML 3.0' ],
                                    [ 'path' => 'CUSTOMER.ARACCOUNT.TAXABLE', 'operation' => FilterViewOperation::IS_ONE_OF, 'value' => [false] ],
                                ],
                                'orders' =>  [ [ 'CUSTOMER.CUSTOMERID', 'desc' ] ]
                            ],
                        ],
                    'validation' => [
                        'total-records' => 2,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '4' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '1' ] ]
                        ]
                    ],
                ],
            ],
            [
                [
                    'input'  =>
                        [ 'entity'       => 'project', 'doctype' => null,
                            'query-params' => [
                                'selects' => [ 'RECORDNO' ],
                                'filters' => [
                                    [ 'path' => 'CUSTOMER.ARACCOUNT.TITLE', 'operation' => FilterViewOperation::IS_ONE_OF, 'value' => ['Sales-Scrap', 'Sales-Wholesale'] ],
                                    [ 'path' => 'SALESCONTACT.EMPLOYEEID', 'operation' => FilterViewOperation::IS_BLANK ],
                                    [ 'path' => 'BUDGETAMOUNT', 'operation' => FilterViewOperation::AFTER, 'value' => '10' ]
                                ],
                                'orders' =>  [ [ 'CUSTOMER.CUSTOMERID' ] ]
                            ],
                        ],
                    'validation' => [
                        'total-records' => 2,
                        'records' => [
                            [ [ 'field' => 'RECORDNO', 'value' => '1' ] ],
                            [ [ 'field' => 'RECORDNO', 'value' => '4' ] ]
                        ]
                    ],
                ],
            ],
            */
        ];

        return $queries;
    }

    /**
     * Evaluates all field types for all objects in the registry.
     *
     * @throws EntityException
     */
    public function _testReadDataTypes()
    {
        // $brokenEntities = [];
        // $noTypeEntities = [];
        $types = [];
        $ptypes = [];

        $standardObjectMap = $this->accessPrivate('Util_StandardObjectRegistry', 'standardObjectMap');
        foreach ( $standardObjectMap as $entity => $info ) {
            if ( in_array($entity, $this->BROKEN_ENTITIES) ) {
                continue;
            }

            LogToFile(">> entity: $entity");
            try {
                $manager = Globals::$g->gManagerFactory->getManager($entity, true);
            } catch ( \Exception $e ) {
                // Ignore
                continue;
                // $brokenEntities[$entity] = $entity;
            }
            $fields = \LookupService::getManagerQueryableFields($manager);

            foreach ( $fields as $field ) {
                $fieldInfo = $manager->GetFieldInfo($field);

                if ( isset($fieldInfo['type']) ) {
                    if ( isset($fieldInfo['type']['type']) ) {
                        $counter =
                            isset($types[$fieldInfo['type']['type']]) ? $types[$fieldInfo['type']['type']] + 1 : 1;
                        $types[$fieldInfo['type']['type']] = $counter;
                    }
                    if ( isset($fieldInfo['type']['ptype']) ) {
                        $counter =
                            isset($types[$fieldInfo['type']['ptype']]) ? $types[$fieldInfo['type']['ptype']] + 1 : 1;
                        $ptypes[$fieldInfo['type']['ptype']] = $counter;
                    }
                } else {
                    // Ignore
                    // $noTypeEntities[$entity] = $entity;
                }
            }
        }

        // LogToFile(var_export(array_keys($types), 1));
        // LogToFile(var_export(array_keys($ptypes), 1));

        self::assertNotEmpty($ptypes);
        self::assertNotEmpty($types);
    }

    /**
     * @return array[]
     */
    public function getFiltersForAllowedOperations() : array
    {
        return [
            'not allowed operations for specific filter' => [
                [
                    FilterViewService::SELECTS_KEY => [
                        'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                    ],
                    FilterViewService::FILTERS_KEY => [
                        [
                            UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                            UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::CONTAINS,
                            UIFilterViewDialect::VALUE_KEY     => '9',
                        ],
                    ],
                ],
                [
                    'RECORDID' => [ 'is not one of', 'is one of', 'ends with', ],
                ],
                'apbill',
                false
            ],
            'allowed operations for specific filter' => [
                [
                    FilterViewService::SELECTS_KEY => [
                        'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                    ],
                    FilterViewService::FILTERS_KEY => [
                        [
                            UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                            UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::CONTAINS,
                            UIFilterViewDialect::VALUE_KEY     => '9',
                        ],
                    ],
                ],
                [
                    'RECORDID' => [ 'contains', ],
                ],
                'apbill',
                true
            ],
        ];
    }

    /**
     * @return array[]
     */
    public function getFilters() : array
    {
        return [
            'validate mandatory operation failed for individual view' => [
                [
                    FilterViewService::SELECTS_KEY       => [
                        'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                    ],
                    FilterViewService::FILTERS_KEY       => [
                        [
                            UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                            UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::CONTAINS,
                            UIFilterViewDialect::VALUE_KEY     => '9',
                        ],
                    ],
                    UIFilterViewDialect::NAME_KEY        => 'test1',
                ],
                [
                    [
                        UIFilterViewDialect::NAME_KEY           => 'test1',
                        UIFilterViewDialect::IS_DEFAULT_KEY     => true,
                        UIFilterViewDialect::OWNER_KEY          => '1',
                        UIFilterViewDialect::FILTER_VIEW_ID_KEY => 'sytemfilterviewdefault',
                        FilterViewService::QUERY_PARAMETERS_KEY => [
                            FilterViewService::SELECTS_KEY => [
                                'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                            ],
                            FilterViewService::FILTERS_KEY => [
                                [
                                    UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                                    UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::CONTAINS,
                                    UIFilterViewDialect::VALUE_KEY     => '9',
                                ],
                            ],
                            FilterViewService::ORDERS_KEY  => [ [ 'VENDORNAME', 'ASC' ] ],
                        ],
                        FilterViewService::MANDATORY_FILTERS => [
                            [
                                UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                                UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                                UIFilterViewDialect::VALUE_KEY     => '87654',
                            ],
                        ],
                    ],
                ],
                [],
                'apbill',
                false
            ],
            'validate mandatory value failed for individual view' => [
                [
                    FilterViewService::SELECTS_KEY       => [
                        'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                    ],
                    FilterViewService::FILTERS_KEY       => [
                        [
                            UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                            UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                            UIFilterViewDialect::VALUE_KEY     => '9',
                        ],
                    ],
                    UIFilterViewDialect::NAME_KEY        => 'test1',
                ],
                [
                    [
                        UIFilterViewDialect::NAME_KEY           => 'test1',
                        UIFilterViewDialect::IS_DEFAULT_KEY     => true,
                        UIFilterViewDialect::OWNER_KEY          => '1',
                        UIFilterViewDialect::FILTER_VIEW_ID_KEY => 'sytemfilterviewdefault',
                        FilterViewService::QUERY_PARAMETERS_KEY => [
                            FilterViewService::SELECTS_KEY => [
                                'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                            ],
                            FilterViewService::FILTERS_KEY => [
                                [
                                    UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                                    UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                                    UIFilterViewDialect::VALUE_KEY     => '9',
                                ],
                            ],
                            FilterViewService::ORDERS_KEY  => [ [ 'VENDORNAME', 'ASC' ] ],
                        ],
                        FilterViewService::MANDATORY_FILTERS => [
                            [
                                UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                                UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                                UIFilterViewDialect::VALUE_KEY     => '99',
                            ],
                        ],
                    ],
                ],
                [],
                'apbill',
                false
            ],
            'validate mandatory fields failed for individual view' => [
                [
                    FilterViewService::SELECTS_KEY => [
                        'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                    ],
                    FilterViewService::FILTERS_KEY => [
                        [
                            UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                            UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::CONTAINS,
                            UIFilterViewDialect::VALUE_KEY     => '9',
                        ],
                    ],
                    UIFilterViewDialect::NAME_KEY  => 'test1',
                ],
                [
                    [
                        UIFilterViewDialect::NAME_KEY           => 'test1',
                        UIFilterViewDialect::IS_DEFAULT_KEY     => true,
                        UIFilterViewDialect::OWNER_KEY          => '1',
                        UIFilterViewDialect::FILTER_VIEW_ID_KEY => 'sytemfilterviewdefault',
                        FilterViewService::QUERY_PARAMETERS_KEY => [
                            FilterViewService::SELECTS_KEY => [
                                'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                            ],
                            FilterViewService::FILTERS_KEY => [
                                [
                                    UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                                    UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                                    UIFilterViewDialect::VALUE_KEY     => '9',
                                ],
                            ],
                            FilterViewService::ORDERS_KEY  => [ [ 'VENDORNAME', 'ASC' ] ],
                        ],
                        FilterViewService::MANDATORY_FILTERS => [
                            [
                                UIFilterViewDialect::PATH_KEY      => 'STATE',
                                UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                                UIFilterViewDialect::VALUE_KEY     => '99',
                            ],
                        ],
                    ],
                ],
                [],
                'apbill',
                false
            ],
            'validate mandatory filters for context views' => [
                [
                    FilterViewService::SELECTS_KEY => [
                        'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                    ],
                    FilterViewService::FILTERS_KEY => [
                        [
                            UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                            UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                            UIFilterViewDialect::VALUE_KEY     => '99',
                        ],
                    ],
                    UIFilterViewDialect::NAME_KEY        => 'test1',
                ],
                [
                    [
                        UIFilterViewDialect::NAME_KEY           => 'test1',
                        UIFilterViewDialect::IS_DEFAULT_KEY     => true,
                        UIFilterViewDialect::OWNER_KEY          => '1',
                        UIFilterViewDialect::FILTER_VIEW_ID_KEY => 'sytemfilterviewdefault',
                        FilterViewService::QUERY_PARAMETERS_KEY => [
                            FilterViewService::SELECTS_KEY => [
                                'RECORDID', 'VENDORNAME', 'WHENCREATED', 'WHENPOSTED', 'PAYMENTPRIORITY', 'STATE',
                            ],
                            FilterViewService::FILTERS_KEY => [
                                [
                                    UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                                    UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                                    UIFilterViewDialect::VALUE_KEY     => '9',
                                ],
                            ],
                            FilterViewService::ORDERS_KEY  => [ [ 'VENDORNAME', 'ASC' ] ],
                        ],
                    ],
                ],
                [
                    [
                        UIFilterViewDialect::PATH_KEY      => 'RECORDID',
                        UIFilterViewDialect::OPERATION_KEY => FilterViewOperation::EQUALS,
                        UIFilterViewDialect::VALUE_KEY     => '99',
                    ],
                ],
                'apbill',
                true
            ],
        ];
    }
}
