<?php

namespace tests\source\core\filter;

use AbstractFilterViewDialect;
use FilterViewDataType;
use FilterViewFault;
use FilterViewOperation;
use FilterViewService;
use Globals;
use QuickFilterHelper;
use UIFilterViewDialect;
use unitTest\core\UnitTestBaseContext;

/**
 * Class FilterViewTest
 *
 * Unit tests for testing quick filters on FilterViewService.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2020 Intacct Corporation
 * @group integration
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class FilterViewQuickFilterTest extends UnitTestBaseContext
{

    /* @var array $collectedResponces */
    static $collectedResponces = [];

    const PROJECT_ID_PREFIX = 'PRJ-UT-00';

    const DEFAULT_PROJECT_COPIES = 3;

    /** @var array $PROJECTS_QUERY_TEMPLATE */
    private static $PROJECTS_QUERY_TEMPLATE = [
        'selects' => [ 'RECORDNO', 'PROJECTID', 'NAME', 'DESCRIPTION', 'PROJECTCATEGORY', 'BEGINDATE', 'ENDDATE' , 'WHENCREATED' , 'WHENMODIFIED' ],
        'filters' => [
            [ 'path' => 'PROJECTID', 'operation' => FilterViewOperation::STARTS_WITH, 'value' => self::PROJECT_ID_PREFIX ],
        ],
    ];

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        self::createProjects();
    }

    public static function tearDownAfterClass(): void
    {
        self::dropProjects();

        parent::tearDownAfterClass();
    }

    /**
     * Create random projects before any test is executed
     */
    public static function createProjects(): void
    {
        LogToFile("============= createProjects =============");
        $manager = Globals::$g->gManagerFactory->getManager('project');

        $daysDistribution = [
            'week' => 7,
            'month' => 30,
            'quarter' => 90,
            'year' => 365
        ];
        foreach ($daysDistribution as $timeWindow => $days) {
            // default number of projects starting some time in the past and ending some time in the future
            for ( $i = 1; $i <= self::DEFAULT_PROJECT_COPIES; $i++ ) {
                $project = [];
                $project['PROJECTID'] = self::PROJECT_ID_PREFIX . "-$timeWindow-$i";
                $project['NAME'] = self::PROJECT_ID_PREFIX . "-$timeWindow-$i" . '-' . bin2hex(openssl_random_pseudo_bytes(2));
                $project['DESCRIPTION'] = self::PROJECT_ID_PREFIX . "-$timeWindow-$i" . '-' . bin2hex(openssl_random_pseudo_bytes(5));
                $project['PROJECTCATEGORY'] = 'Contract';
                $project['BEGINDATE'] = date("m/d/Y", strtotime('-' . rand(1, $days) . ' days'));
                $project['ENDDATE'] = date("m/d/Y", strtotime('+' . rand(1, $days) . ' days'));
                $project['WHENCREATED'] = date("m/d/Y", strtotime('-' . rand(1, $days) . ' days'));
                $project['WHENMODIFIED'] = date("m/d/Y", strtotime('-' . rand(1, $days) . ' days'));

                $ok = $manager->add($project);
                self::assertTrue($ok);
            }
        }

        $startDates = [
            date("m/d/Y"), //today
            date("m/d/Y", mktime(0, 0, 0, date("m"), date("d") - 1, date("Y"))), //yesterday
        ];

        $index = 1001;
        foreach ($startDates as $startDate) {
            $counter = 0;
            do  {
                $project = [];
                $project['PROJECTID'] = self::PROJECT_ID_PREFIX . $index;
                $project['NAME'] = self::PROJECT_ID_PREFIX . $index . '-' . bin2hex(openssl_random_pseudo_bytes(2));
                $project['DESCRIPTION'] = self::PROJECT_ID_PREFIX . $index . '-' . bin2hex(openssl_random_pseudo_bytes(5));
                $project['PROJECTCATEGORY'] = 'Contract';
                $project['BEGINDATE'] = $startDate;
                $project['ENDDATE'] = date("m/d/Y", strtotime('+' . rand(1,365) . ' days'));
                $project['WHENCREATED'] = $startDate;
                $project['WHENMODIFIED'] = $startDate;

                $ok = $manager->add($project);
                self::assertTrue($ok);
                $index++;
            } while (++$counter < 3);
        }
    }

    /**
     * Drop all projects created for testing after all the tests were executed
     */
    public static function dropProjects(): void
    {
        LogToFile("============= dropProjects =============");
        LogToFile(var_export(self::$collectedResponces, 1));

        /** @var \ProjectManager $manager */
        $manager = Globals::$g->gManagerFactory->getManager('project');
        $params = [
            'selects' => ['RECORDNO', 'PROJECTID', 'NAME', 'DESCRIPTION', 'PROJECTCATEGORY', 'BEGINDATE', 'ENDDATE'],
            'filters' => [
                [
                    ['PROJECTID', 'ILIKE', self::PROJECT_ID_PREFIX . '%']
                ]
            ]
        ];
        $projects = $manager->GetList($params);

        foreach ( $projects as $project ) {
            $ok = $manager->delete($project['PROJECTID']);
            self::assertTrue($ok);
        }
    }

    /**
     * Single test for timestamp type field
     *
     * @throws FilterViewFault
     */
    public function testTimestampField(): void
    {
        // Clean all global errors
        Globals::$g->gErr->Clear();

        LogToFile("============= testTimestampField =============");
        $manager = Globals::$g->gManagerFactory->getManager('project');
        $params = [
            'selects' => ['RECORDNO', 'WHENCREATED', 'WHENMODIFIED'],
            'filters' => [
                [
                    ['PROJECTID', 'ILIKE', self::PROJECT_ID_PREFIX . '%']
                ]
            ]
        ];
        $allProjects = $manager->GetList($params);
        $baseDate = $allProjects[0]['WHENCREATED'];

        // create a $dt object with the UTC timezone
        $timestamp = new \DateTime($baseDate, new \DateTimeZone('UTC'));

        // change the timezone of the object without changing it's time to the local timezone
        $localTimeZoneName = (new \DateTime())->getTimezone()->getName();
        $timestamp->setTimezone(new \DateTimeZone($localTimeZoneName));

        // The base date is the TIMESTAMP value from the database in UTC converted
        // to date format in LOCAL timezone
        $baseDate = $timestamp->format("m/d/Y");

        $params = self::$PROJECTS_QUERY_TEMPLATE;
        $params['filters'][] = ['path' => 'WHENCREATED', 'operation' => FilterViewOperation::EQUALS, 'value' => QuickFilterHelper::TODAY];
        $params[UIFilterViewDialect::ATTRIBUTES_KEY][FilterViewService::BASE_DATE_KEY] = $baseDate;

        $entity = 'project';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);

        LogToFile("============= calling getFilteredData =============");
        LogToFile("params -> " .var_export($params, 1));
        $response = $service->getFilteredData($params);
        if (!$response['success']) {
            $errlist = [];
            Globals::$g->gErr->GetErrList($errlist);
            if (sizeof($errlist) > 0) {
                print_r($errlist);
            }
        }
        self::assertTrue($response['success']);
    }

    /**
     * @dataProvider timestampTypeTestDataProvider
     *
     * @param array $testInput
     */
    public function testQuickFilterForTimestampFied(array $testInput): void
    {
        // Clean all global errors
        Globals::$g->gErr->Clear();

        static $baseDate = null;
        if ($baseDate === null) {
            $manager = Globals::$g->gManagerFactory->getManager('project');
            $params = [
                'selects' => ['RECORDNO', 'WHENCREATED', 'WHENMODIFIED'],
                'filters' => [
                    [
                        ['PROJECTID', 'ILIKE', self::PROJECT_ID_PREFIX . '%']
                    ]
                ]
            ];
            $allProjects = $manager->GetList($params);
            $baseDate = $allProjects[0]['WHENCREATED'];

            // create a $dt object with the UTC timezone
            $timestamp = new \DateTime($baseDate, new \DateTimeZone('UTC'));

            // change the timezone of the object without changing it's time to the local timezone
            $localTimeZoneName = (new \DateTime())->getTimezone()->getName();
            $timestamp->setTimezone(new \DateTimeZone($localTimeZoneName));

            // format the datetime
            $baseDate = $timestamp->format("m/d/Y H:i:s");
        }

        $this->singleQuickFilterTest($testInput, $baseDate);
    }

    /**
     * @dataProvider dateTypeTestDataProvider
     *
     * @param array $testInput
     */
    public function testQuickFilterForDateFied(array $testInput): void
    {
        // Clean all global errors
        Globals::$g->gErr->Clear();

        $this->singleQuickFilterTest($testInput);
    }

    /**
     * @param array       $testInput
     * @param string|null $baseDate
     *
     * @throws \IAException
     */
    private function singleQuickFilterTest(array $testInput, string $baseDate = null): void
    {
        static $noValueCounter = 0;

        $params = $testInput['query-params'];

        if ($baseDate !== null) {
            $params[UIFilterViewDialect::ATTRIBUTES_KEY][FilterViewService::BASE_DATE_KEY] = $baseDate;
        }

        $f = $params['filters'][1];
        $key = $f['path'] . '-' . $f['operation'] . '-' . ($f['value'] ?? ('-' . (++$noValueCounter)));

        $fieldPath = $f['path'];

        // LogToFile(var_export($params['filters'], 1));

        $entity = 'project';
        $service = new FilterViewService($entity, null, AbstractFilterViewDialect::UI);
        $em = $service->getEntityManager();
        $em->_schemas[$entity]['filtercontexts']['system']['__default']['views'] = [];
        $service->setEntityManager($em);
        $service->getFilterViewValidator()->setService($service);
        $response = $service->getFilteredData($params);

        self::assertTrue($response['success']);

        $projects = $response['data'];

        self::$collectedResponces[$key]['count'] = count($projects);

        LogToFile(">>> filter: " . var_export($params['filters'][1], 1));
        LogToFile(">>> count: " . count($projects));
        foreach ($projects as $project) {
            self::$collectedResponces[$key]['projects'][] = $project['PROJECTID'] . "      $fieldPath: " . $project[$fieldPath];
            LogToFile(">>> PROJECTID: " . $project['PROJECTID'] . "      $fieldPath: " . $project[$fieldPath]);
        }
    }

    /**
     * @return array
     * @throws FilterViewFault
     */
    public function timestampTypeTestDataProvider() : array
    {
        LogToFile("============= timestampTypeTestDataProvider =============");
        return $this->fieldTypeSpecificDataProvider(FilterViewDataType::TIMESTAMP, 'WHENCREATED');
    }

    /**
     * Generates input for all tests.
     *
     * @return array
     * @throws FilterViewFault
     */
    public function dateTypeTestDataProvider() : array
    {
        LogToFile("============= dateTypeTestDataProvider =============");
        return $this->fieldTypeSpecificDataProvider(FilterViewDataType::DATE, 'BEGINDATE');
    }

    /**
     * @param string $fieldViewDataType
     * @param string $fieldName
     *
     * @return array
     * @throws FilterViewFault
     */
    public function fieldTypeSpecificDataProvider(string $fieldViewDataType, string $fieldName) : array
    {
        $ret = [];
        $paramsList = [];

        $dbOperations = FilterViewDataType::getDbOperations($fieldViewDataType);

        $quickFilters = QuickFilterHelper::quickFilters();

        foreach ( $quickFilters as $quickFilter ) {
            foreach ( $dbOperations as $operation => $details ) {
                $params = self::$PROJECTS_QUERY_TEMPLATE;
                if ($details['unary'] === true) {
                    $filter = ['path' => $fieldName, 'operation' => $operation];
                    // continue;
                } else {
                    $date = date('m/d/Y', strtotime($quickFilter));
                    if ($operation === 'range') {
                        $quickFilter = [ $date, $date ];
                    }
                    $filter = ['path' => $fieldName, 'operation' => $operation, 'value' => $quickFilter];
                }
                $params['filters'][] = $filter;

                $paramsList[] = [
                    'query-params' => $params,
                    'validate' => false
                ];

                $ret[] = $paramsList;

                unset($paramsList);
            }
        }

        return $ret;
    }
}
