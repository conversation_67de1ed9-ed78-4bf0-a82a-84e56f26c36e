<?php
use unitTest\core\UnitTestBase;

class I18NTest extends UnitTestBase
{
    protected function setUp(): void
    {
        I18N::setLocale('I18NTest');
    }

    protected function tearDown(): void
    {
        // Ensure other unit tests that use I18N are not affected
        I18N::setJsonAdaptor();
        I18N::setLocale('en-US');
        I18N::setPlaceholderReturn(false);
    }

    public function testBatchWithoutPlaceHolders()
    {
        $testArray = array(
            ['id' => 'IA.TEST_TOKEN:12'],
            ['id' => 'IA.TEST_TOKEN2']
        );
        I18N::setNoOpAdaptor();
        I18N::addToken($testArray[0]['id']);
        I18N::addToken($testArray[1]['id']);
        $return = I18N::getTokensForArray();
        $this->assertContains($testArray[0]['id'], array_keys($return));
        $this->assertContains($testArray[1]['id'], array_keys($return));

        I18N::flush();
        $return = I18N::getTokensForArray($testArray);
        $this->assertContains($testArray[0]['id'], array_keys($return));
        $this->assertContains($testArray[1]['id'], array_keys($return));
    }

    public function testAddMultipleTokens()
    {
        $testArray = array(
            ['id' => 'IA.TEST_TOKEN:12'],
            ['id' => 'IA.TEST_TOKEN1',
                'placeHolders'=> [
                    ['name' => 'PLACEHOLDER1', 'value' => 'VALUE1'],
                    ['name'=> 'PLACEHOLDER2', 'value' => 'VALUE2']
                ]
            ],
            ['id' => 'IA.TEST_TOKEN2'],
            ['id' => 'IA.TEST_TOKEN3'],
            ['id' => 'IA.TEST_TOKEN4'],
        );
        I18N::addTokens($testArray);
	    I18N::setNoOpAdaptor();
        $return = array_keys(I18N::getText());
        foreach ($testArray as $testArrayItem) {
            $this->assertContains($testArrayItem['id'], $return);
        }

    }

    public function testTerminologyHandler()
    {
        //we cannot use real ids because the i18n is already preloaded because its called in the init scripts
        $testArray = array(['id' => 'IA.TOKEN_WITH_TERM']);
        $expected = 'TOKEN WITH TERM';
        I18N::setJsonAdaptor([
            'IA.TOKEN_WITH_TERM' => [
                'value' => [
                    'en-US' => '${IATERM_IN_TOKEN_WITH_TERM}',
                ],
                'markdown' => false,
            ],
            'IATERM_IN_TOKEN_WITH_TERM' => [
                'value' => [
                    'en-US' => 'token with term',
                ],
                'markdown' => false,
            ]
        ]);
        //ensure we have en-US locale because term replace works only for english like languages and we added tokens only for en-US
        I18N::setLocale('en-US');
        I18N::addTokens($testArray);
        $return = I18N::getText('', 'strtoupper');
        $this->assertEquals($expected, $return['IA.TOKEN_WITH_TERM']);
    }

    public function testPlaceholderConversion(){
        $placeholderObj = [
                    ['name' => 'DISPLAY_DATE', 'value' => "aDate"]
        ];
        $placeholderMap = [ 'DISPLAY_DATE' => 'aDate'];
        $result = I18N::mapToPlaceholderArray($placeholderMap );
        $this->assertEquals($placeholderObj, $result);
    }

    public function testArrayOfTokensToObject(){
        $expected = [
            'IA.EQUALS',
            'IA.LESS_THAN',
            'IA.GREATER_THAN',
            'IA.LESS_OR_EQUAL',
            'IA.GREATER_OR_EQUAL',
            'IA.NOT_EQUAL_TO',
            'IA.CONTAINS',
            'IA.DOES_NOT_CONTAIN',
            'IA.STARTS_WITH',
            'IA.ENDS_WITH',
            'IA.INCLUDES',
            'IA.EXCLUDES',
            'IA.FIELD_EQUALS_NULL',
            'IA.FIELD_NOT_EQUALS_NULL',
            'IA.MY_TOKEN'
        ];
        $tokens = [
            ['id' => 'IA.EQUALS'],
            ['id' => 'IA.LESS_THAN'],
            ['id' => 'IA.GREATER_THAN'],
            ['id' => 'IA.LESS_OR_EQUAL'],
            ['id' => 'IA.GREATER_OR_EQUAL'],
            ['id' => 'IA.NOT_EQUAL_TO'],
            ['id' => 'IA.CONTAINS'],
            ['id' => 'IA.DOES_NOT_CONTAIN'],
            ['id' => 'IA.STARTS_WITH'],
            ['id' => 'IA.ENDS_WITH'],
            ['id' => 'IA.INCLUDES'],
            ['id' => 'IA.EXCLUDES'],
            ['id' => 'IA.FIELD_EQUALS_NULL'],
            ['id' => 'IA.FIELD_NOT_EQUALS_NULL'],
            ['id' => 'IA.MY_TOKEN']
        ];
	    I18N::setNoOpAdaptor();
        $result = I18N::getTokensForArray($tokens);
        $this->assertEquals($expected,  array_keys($result));
        $this->assertEquals($expected,  array_values($result));
    }

    public function testBatchWithPlaceHolders()
    {
        $testArray = [
            [
                'id' => 'IA.TEST_TOKEN',
                'placeHolders'=> [
                    ['name' => 'PLACEHOLDER1', 'value' => 0.432],
                    ['name'=> 'PLACEHOLDER2', 'value' => null]
                ],
            ],
            [
                'id' => 'IA.TEST_TOKEN2',
                'placeHolders' => [
                    ['name' => 'PLACEHOLDER1', 'value' => 'VALUE1']
                ],
            ],
            [
                'id' => 'IA.TEST_TOKEN2:CONTEXT1',
                'placeHolders'=> [
                    ['name'=>'PLACEHOLDER2', 'value' => 12],
                ]
            ],
            [
                'id' => 'IA.TEST_TOKEN2:CONTEXT2',
                'placeHolders'=> [
                    ['name'=>'PLACEHOLDER2', 'value' => 'VALUE1']
                ]
            ],
            [
                'id' => 'IA.SDS'
            ],
        ];


        // get results with placeholders and value
        I18N::setPlaceholderReturn(true);
	    I18N::setNoOpAdaptor();
        $result = I18N::getTokensForArray($testArray);
        foreach ($testArray as $tokenObject) {
            $this->assertContains($tokenObject['id'], array_keys($result));
            $this->assertEquals(
                $this->_getExpectedResultValue($tokenObject['id'], $tokenObject['placeHolders']),
                $result[$tokenObject['id']]
            );
        }

        //get results without placeholders
        I18N::setPlaceholderReturn(false);
        $result = I18N::getTokensForArray($testArray);
        foreach ($testArray as $tokenObject) {
            $this->assertContains($tokenObject['id'], array_keys($result));
            $this->assertEquals(explode(':',$tokenObject['id'])[0], $result[$tokenObject['id']]);
        }

        // get results with placeholders and value
        I18N::setPlaceholderReturn(true);
        $result = I18N::getTokensForArray($testArray);
        foreach ($testArray as $tokenObject) {
            $this->assertContains($tokenObject['id'], array_keys($result));
            $this->assertEquals(
                $this->_getExpectedResultValue($tokenObject['id'], $tokenObject['placeHolders']),
                $result[$tokenObject['id']]
            );
        }
    }

    /**
     * @param $token
     * @param $placeholderArr
     *
     * @return string
     */
    private function _getExpectedResultValue($token, $placeholderArr): string
    {
        $return = explode(':',$token)[0];
        foreach ($placeholderArr as $placeholder) {
            $return .= '-{'.$placeholder['name'].'='.$placeholder['value'].'}';
        }
        return $return;
    }

    public function testGetWithoutForceWithoutPreload()
    {
	    I18N::setNoOpAdaptor();
        I18N::setPlaceholderReturn(true);
        $this->assertEquals('IA.TOKEN',I18N::getSingleToken('IA.TOKEN', [['name' => 'PLACEHOLDER1', 'value' => 'VALUE1']], false));
    }

    public function testGetWithoutForceWithPreload()
    {
	    I18N::setNoOpAdaptor();
        I18N::getTokensForArray([['id' => 'IA.TOKEN']]);
        I18N::setPlaceholderReturn(true);
        $this->assertEquals('IA.TOKEN-{PLACEHOLDER1=VALUE1}',I18N::getSingleToken('IA.TOKEN', [['name' => 'PLACEHOLDER1', 'value' => 'VALUE1']]));
    }

    public function testGetWithForce() {
	    I18N::setNoOpAdaptor();
        I18N::setPlaceholderReturn(true);
        $this->assertEquals('IA.TOKEN-{PLACEHOLDER1=VALUE1}',I18N::getSingleToken('IA.TOKEN', [['name' => 'PLACEHOLDER1', 'value' => 'VALUE1']], true));
    }

    public function testGetBogus() {
        I18N::setNoOpAdaptor();
        I18N::setPlaceholderReturn(true);
        $this->assertEquals('lemne de vara: pe iarna',I18N::getSingleToken('lemne de vara: pe iarna'));
    }
    public function testGetBogus2() {
        I18N::setNoOpAdaptor();
        I18N::setPlaceholderReturn(true);
        $this->assertEquals('lemne de vara:',I18N::getSingleToken('lemne de vara:'));
    }

    public function testGet()
    {
	    I18N::setNoOpAdaptor();
        $this->assertEquals('IA.TEST',I18N::getSingleToken('IA.TEST', [], true));
        $this->assertEquals('IA.TEST',I18N::getSingleToken('IA.TEST:CONTEXT'));
        I18N::setPlaceholderReturn(true);
        $this->assertEquals('IA.TEST-{PLACEHOLDER=VALUE}',
            I18N::getSingleToken('IA.TEST', [['name'=> 'PLACEHOLDER', 'value' =>'VALUE']])
        );

    }

    public function testBuckets() {
        I18N::setNoOpAdaptor();
        I18N::addToken('IA.TEST1');
        I18N::addToken('IA.TEST2');
        I18N::addToken('IA.TEST4', [], 'test');
        I18N::addToken('IA.TEST5', [], 'test');
        $default = I18N::getText();
        $bucket = I18N::getText('test');
        $this->assertContains('IA.TEST1', array_keys($default));
        $this->assertContains('IA.TEST2', array_keys($default));
        $this->assertCount(2, $default);
        $this->assertContains('IA.TEST4', array_keys($bucket));
        $this->assertContains('IA.TEST5', array_keys($bucket));
        $this->assertCount(2, $bucket);
    }

    private function _objectToMap($array) {
        $mapArray=[];
        foreach ($array as $tokenObject) {
            if (array_key_exists('placeHolders', $tokenObject)) {
                $mapArray[$tokenObject['id']] = $this->_convertToPlaceholderArrayToObject($tokenObject['placeHolders']);
            } else {
                $mapArray[] = $tokenObject['id'];
            }
        }
        return $mapArray;
    }
    private function _convertToPlaceholderArrayToObject(array $placeHolderObjectArray = array()): array {
        $return = [];
        foreach ($placeHolderObjectArray as $placeHolderObject) {
            if (!array_key_exists('name', $placeHolderObject) || !array_key_exists('value', $placeHolderObject)) {
                throw new I18NException('Invalid placeHolder: '.json_encode($placeHolderObject));
            }
            $return[$placeHolderObject['name']] = $placeHolderObject['value'];
        }
        return $return;
    }

}
