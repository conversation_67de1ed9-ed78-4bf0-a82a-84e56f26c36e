<?php

namespace tests\source\core;

require_once('Picker.cls');

use ComboPicker;
use LogManager;
use PHPUnit\Framework\MockObject\MockObject;
use Picker;
use unitTest\core\UnitTestBase;

/**
 * Class ComboPickerTest
 *
 * @coversDefaultClass ComboPicker
 */
class ComboPickerTest extends UnitTestBase
{
    /** @var LogManager|MockObject $logManagerMock */
    private $logManagerMock;
    /** @var Picker|MockObject $pickerMock */
    private $pickerMock;
    
    /**
     * This method is called before the first test of this test class is run.
     */
    protected function setUp(): void
    {
        $this->logManagerMock = $this->getMockBuilder(LogManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['logErrorMessage'])
            ->getMock();
        $this->pickerMock = $this->getMockBuilder(Picker::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getPickerFieldPaths'])
            ->getMock();
        $this->pickerMock->entity = 'testEntity';
    }
    
    /**
     * @return array
     */
    public function sanitizeQuerySelectsValidValuesDataProvider(): array
    {
        return [
            [ /* dataset 0 */
                [
                    'ajaxSelects' => ['col1'],
                    'querySelects' => ['col1'],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                ],
                ['col1'],
            ],
            [ /* dataset 1 */
                [
                    'ajaxSelects' => ['col1'],
                    'querySelects' => ['col1', 'select 1 as InternalData'],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                ],
                ['col1', 'select 1 as InternalData'],
            ],
            [ /* dataset 2 */
                [
                    'ajaxSelects' => [],
                    'querySelects' => ['col2'],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                ],
                ['col2'],
            ],
            [ /* dataset 3 */
                [
                    'ajaxSelects' => [],
                    'querySelects' => [],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                ],
                [],
            ],
        ];
    }
    
    /**
     * @dataProvider sanitizeQuerySelectsValidValuesDataProvider
     *
     * @param array $testInput
     * @param array $expected
     */
    public function testSanitizeQuerySelects(array $testInput, array $expected)
    {
        $comboMock = $this->createPartialMock(ComboPicker::class, []);
        $comboMock->picker = $this->pickerMock;
        $comboMock->logManager = $this->logManagerMock;
        $this->pickerMock->expects(self::once())
            ->method('getPickerFieldPaths')
            ->willReturn($testInput['allFieldPaths']);
        $this->logManagerMock->expects(self::never())->method('logErrorMessage');
        
        $comboMock->querySpec['selects'] = $testInput['querySelects'];
        $this->invokePrivate($comboMock, 'sanitizeQuerySelects', $testInput['ajaxSelects']);
        
        self::assertSame($expected, $comboMock->querySpec['selects']);
    }
    
    /**
     * @return array
     */
    public function sanitizeQuerySelectsInvalidValuesDataProvider(): array
    {
        return [
            [ /* dataset 0 */
                [
                    'ajaxSelects' => ['col3'],
                    'querySelects' => ['col3'],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                    'invalidCol' => 'col3',
                ],
                [],
            ],
            [ /* dataset 1 */
                [
                    'ajaxSelects' => ['col3', 'col4'],
                    'querySelects' => ['col3', 'col4'],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                    'invalidCol' => 'col3,col4',
                ],
                [],
            ],
            [ /* dataset 2 */
                [
                    'ajaxSelects' => ['col1', 'col3'],
                    'querySelects' => ['col1', 'col3', 'select 1 as InternalData'],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                    'invalidCol' => 'col3',
                ],
                ['col1', 'select 1 as InternalData'],
            ],
            [ /* dataset 3 */
                [
                    'ajaxSelects' => [null],
                    'querySelects' => [null],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                    'invalidCol' => '',
                ],
                [],
            ],
            [ /* dataset 4 */
                [
                    'ajaxSelects' => ['col1', 'attempted XSS'],
                    'querySelects' => ['col1', 'attempted XSS', 'select 1 as InternalData'],
                    'allFieldPaths' => ['COL1' => 1, 'COL2' => 1],
                    'invalidCol' => 'attempted XSS',
                ],
                ['col1', 'select 1 as InternalData'],
            ],
        ];
    }
    
    /**
     * @dataProvider sanitizeQuerySelectsInvalidValuesDataProvider
     *
     * @param array $testInput
     * @param array $expected
     */
    public function testSanitizeQuerySelectsInvalid(array $testInput, array $expected)
    {
        $comboMock = $this->createPartialMock(ComboPicker::class, []);
        $comboMock->picker = $this->pickerMock;
        $comboMock->logManager = $this->logManagerMock;
        $this->pickerMock->expects(self::once())
            ->method('getPickerFieldPaths')
            ->willReturn($testInput['allFieldPaths']);
        $expectedError = 'Invalid fields detected in AJAX call for entity testEntity -> ' . $testInput['invalidCol'];
        $this->logManagerMock->expects(self::once())
            ->method('logErrorMessage')
            ->with($expectedError);
        
        $comboMock->querySpec['selects'] = $testInput['querySelects'];
        $this->invokePrivate($comboMock, 'sanitizeQuerySelects', $testInput['ajaxSelects']);
        
        self::assertSame($expected, $comboMock->querySpec['selects']);
    }
}
