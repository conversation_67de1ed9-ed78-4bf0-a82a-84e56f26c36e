<?php
use unitTest\core\UnitTestBase;
use PHPUnit\Framework\MockObject\MockObject;

class CurrencyNumberFormatTest extends UnitTestBase
{
    protected $currencyNumberFormatMock = null;

    /**
     * @return string[]
     */
    protected function getUserData1()
    {
        $decimalSep1 = '*';
        $thousandsSep1 = '+';
        return [$decimalSep1, $thousandsSep1];
    }

    /**
     * @return string[]
     */
    protected function getUserData2()
    {
        $decimalSep1 = ',';
        $thousandsSep1 = ' ';
        return [$decimalSep1, $thousandsSep1];
    }

    /**
     * @return string[]
     */
    protected function getUserData3()
    {
        $decimalSep1 = ',';
        $thousandsSep1 = '.';
        return [$decimalSep1, $thousandsSep1];
    }

    /**
     * @return array
     */
    protected function getCnyData1(): array
    {
        $companySeparators = [
            'CURRENCYALIGNMENT' => CurrencyNumberFormat::CURRENCY_ALLIGNAMENT_US,
            'CURRENCYSYMBOL' => '$',
            'CURRENCYNUMSYS' => '1',
            'CURRENCYDECIMAL_SEP' => '.',
            'CURRENCYTHOUSAND_SEP' => ',',
        ];
        return $companySeparators;
    }


    protected function getCurrencyData1(): array
    {
        $currencyInfo = [];
        $currencyInfo['ALIGNMENT'] = CurrencyNumberFormat::CURRENCY_ALLIGNAMENT_US;
        $currencyInfo['SYMBOL'] = '€';
        $currencyInfo['NAME'] = 'Euro';
        $currencyInfo['SUBNAME'] = 'eurocent';
        $currencyInfo['FORMAT'] = 'Custom';
        $currencyInfo['CODE'] = 'EUR';
        $currencyInfo['NUMBERING_SYS'] = '1';

        return $currencyInfo;
    }


    protected function genMock($userSeparators, $cnySeparators)
    {
        $currencyNumberFormatMock = $this->getMockBuilder(CurrencyNumberFormat::class)
            ->disableOriginalConstructor()
            ->setMethods(['loadProfileSeparators', 'loadCompanySeparators', 'isFeatureEnabled'])
            ->getMock();
        $currencyNumberFormatMock->expects($this->any())
            ->method('loadProfileSeparators')
            ->withAnyParameters()
            ->will($this->returnValue($userSeparators));

        $currencyNumberFormatMock->expects($this->any())
            ->method('loadCompanySeparators')
            ->withAnyParameters()
            ->will($this->returnValue($cnySeparators));

        $currencyNumberFormatMock->expects($this->any())
            ->method('isFeatureEnabled')
            ->withAnyParameters()
            ->will($this->returnValue(true));

        $currencyNumberFormatMock->checkFeatureEnabled();

        return  $currencyNumberFormatMock;
    }
    protected function setUp(): void
    {
        $this->currencyNumberFormatMock = $this->genMock($this->getUserData1(), $this->getCnyData1());
        $this->currencyNumberFormatMock->loadSeparators();
    }

    protected function tearDown(): void
    {
    }


    /**
     * @return void
     */
    public function testSeparators()
    {
        $userData1 = $this->getUserData1();
        $cnf = $this->currencyNumberFormatMock;

        $decimalSep = $cnf->getDecimalSeparator();
        $thousandSep = $cnf->getThousandsSeparator();

        // check user data is prioritary
        $this->assertEquals($userData1[0], $decimalSep);
        $this->assertEquals($userData1[1], $thousandSep);
    }

    public function testNumberingSystem()
    {
        $cnf = $this->currencyNumberFormatMock;
        // test default alignament
        $this->assertEquals(CurrencyNumberFormat::CURRENCY_NUMBERING_SYSTEM_US, $cnf->getCurrencyNumberingSystem());

                // test set
        //$cnf->setCurrencyNumberingSystem(CurrencyNumberFormat::CURRENCY_NUMBERING_SYSTEM_INDIA_LEGACY);
      //  $this->assertEquals(CurrencyNumberFormat::CURRENCY_NUMBERING_SYSTEM_INDIA, $cnf->getCurrencyNumberingSystem());
    }

    /*public function testCurrency()
    {
        $currencyData = $this->getCurrencyData1();
        $currencyManagerMock = $this->getMockBuilder(TrxCurrenciesManager::class)
        ->disableOriginalConstructor()
        ->setMethods(['GetCurrencyInfo'])
        ->getMock();

        $currencyManagerMock->expects($this->any())
            ->method('GetCurrencyInfo')
            ->withAnyParameters()
            ->will($this->returnValue($currencyData));

        global $gManagerFactory;
        $managerFactoryMock = $this->getMockBuilder(ManagerFactory::class)
            ->disableOriginalConstructor()
            ->setMethods(['getManager'])
            ->getMock();
        $managerFactoryMock->expects($this->any())
            ->method('getManager')
            ->withAnyParameters()
            ->will($this->returnValue($currencyManagerMock));

        $gManagerFactory = $managerFactoryMock;
        $cnf = $this->currencyNumberFormatMock;

        $cnf->loadCurrency('EUR');
        $this->assertEquals($currencyData['CODE'], $cnf->getCurrencyCode());
        $this->assertEquals($currencyData['SYMBOL'], $cnf->getCurrencySymbol());
        $this->assertEquals($currencyData['NAME'], $cnf->getCurrencyName());
        $this->assertEquals($currencyData['SUBNAME'], $cnf->getCurrencySubName());
        $this->assertEquals($currencyData['NUMBERING_SYS'], $cnf->getCurrencyNumberingSystem());


    }*/

    /**
     * @dataProvider getRemoveNumberFormattingDataProvider
     */
    public function testRemoveNumberFormatting($userSeparators, $input, $output)
    {
        $numberFormat = $this->genMock($userSeparators, $this->getCnyData1());
        $numberFormat->loadSeparators();

        $this->assertEquals($numberFormat->removeNumberFormatting($input), $output);
    }

    /**
     * @return array[]
     */
    public function getRemoveNumberFormattingDataProvider()
    {
        return [
            "Integer value with user prefs [ decimals=',', thousand=' ' ]"         => [
                $this->getUserData2(),
                '1000',
                '1000',
            ],
            "Formatted value with user prefs [ decimals=',', thousand=' ' ]"       => [
                $this->getUserData2(),
                '1 000 000,89',
                '1000000.89',
            ],
            "Wrong order formatted value with user prefs [ decimals=',', thousand=' ' ]" => [
                $this->getUserData2(),
                '1,000 78',
                '1.000 78',
            ],
            "Wrong formatted value with user prefs [ decimals=',', thousand=' ' ]" => [
                $this->getUserData2(),
                '1,000,000.89',
                '1.000,000.89',
            ],
            "Integer value with user prefs [ decimals=',', thousand='.' ]"         => [
                $this->getUserData3(),
                '1000',
                '1000',
            ],
            "Formatted value with user prefs [ decimals=',', thousand='.' ]"       => [
                $this->getUserData3(),
                '1.000.000,89',
                '1000000.89',
            ],
            "Wrong order formatted value with user prefs [ decimals=',', thousand='.' ]" => [
                $this->getUserData3(),
                '1,000.78',
                '1.000.78',
            ],
            "Wrong formatted value with user prefs [ decimals=',', thousand='.' ]" => [
                $this->getUserData3(),
                '1 000 000.89',
                '1 000 00089',
            ],
        ];
    }

}
