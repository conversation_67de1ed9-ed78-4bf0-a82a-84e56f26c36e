openapi: 3.0.0
info:
  title: Vendor
  description: Vendor API
  version: '1.0'
  contact:
    email: <EMAIL>
    name: <PERSON><PERSON><PERSON>
tags:
  - name: vendor
servers:
  - url: 'https://dev01.intacct.com/users/anjali.israni/projects.nextgenapi-glbranch/api/v0'
    description: development
paths:
  '/objects/vendor/{key}':
    parameters:
      - schema:
          type: integer
          example: 153
        name: key
        in: path
        required: true
    get:
      summary: Get Vendor
      description: Get complete details for a particular vendor
      tags:
        - vendor
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/vendor'
        '400':
          description: Bad Request
      operationId: get-objects-vendor-key
    patch:
      summary: ''
      description: desc
      tags:
        - vendor
      operationId: patch-v1-objects-vendor-multiple
      responses:
        '200':
          description: OK
          content:
            application/json:
              example:
                - key: '36492'
                  id: '36492'
                  href: /objects/vendor/36492
                - key: '36493'
                  id: '36493'
                  href: /objects/vendor/36493
        '400':
          description: Bad Request
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/vendor'
    post:
      summary: ''
      tags:
        - vendor
      operationId: post-v1-objects-vendor
      responses:
        '201':
          description: Created
          content:
            application/json:
              examples:
                Single Vendor:
                  value:
                    key: '36493'
                    id: '36493'
                    href: /objects/vendor/36493
                Multiple Vendores:
                  value:
                    - key: '36494'
                      id: '36494'
                      href: /objects/vendor/36494
                    - key: '36495'
                      id: '36495'
                      href: /objects/vendor/36495
        '400':
          description: Bad Request
      description: desc
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/vendor'
            examples:
              Single Vendor:
                summary: Create a Vendor
                value:
                  id: MyVendorID
                  description: My Vendor Description
                  name: my vendor name
    delete:
      summary: ''
      description: desc
      tags:
        - vendor
      operationId: delete-v1-objects-vendor-key
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
  /objects/vendor:
    get:
      summary: List Vendors
      tags: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/vendor'
      operationId: get-objects-vendor
      description: List of vendor keys and reference links to get vendor details
  /servcies/model:
    get:
      summary: Get Vendor Object Definition
      tags: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/vendor'
      operationId: get-servcies-model
      description: List all the fields and relationships for the vendor object
      parameters:
        - $ref: '#/components/parameters/name'
components:
  schemas:
    vendor:
      $ref: __test.vendor.s1.schema.yaml
  parameters:
    key:
      name: key
      in: path
      required: true
      schema:
        type: string
        pattern: (\/-?\d+)?
#    jobId:
#      name: jobId
#      in: path
#      required: true
#      schema:
#        type: string
#        pattern: (\/-?\d+)?
    version:
      name: version
      in: query
      required: false
      schema:
        type: string
    name:
      name: name
      in: query
      required: false
      schema:
        type: string
    type:
      name: type
      in: query
      required: false
      schema:
        type: string
    schema:
      name: schema
      in: query
      required: false
      schema:
        type: string
    tags:
      name: tags
      in: query
      required: false
      schema:
        type: string
