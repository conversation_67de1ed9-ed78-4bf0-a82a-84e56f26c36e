<?php

/**
 * Dynamic Fields specific test cases
 *
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 */
/**
 * Class DynamicFieldTest
 *
 */
namespace test\source\api\framework\schema;
require_once (__DIR__.'/../APITestHelper.cls');

/**
 *
 */
class DynamicFieldsTest extends \unitTest\core\UnitTestBaseContext
{

    public function testNothing()
    {
        $this->assertTrue(true);
    }

    /** @var array $dynamicFieldText */
    private $dynamicFieldText = [
        'fullname' => 'Dynamic_Text',
        'required' => false,
        'path' => 'DYNAMIC_TEXT',
        'hidden' => null,
        'desc' => null,
        'recordno' => 148,
        'fastUpdate' => 1,
        'type' => [
            'type' => 'text',
            'ptype' => 'text',
            'maxlength' => 100,
            'size' => 40,
        ],
        'partialedit' => 1
    ];

    /** @var array $dynamicFieldDec */
    private $dynamicFieldDec = [
        'fullname' => 'Dynamic_Num',
        'required' => null,
        'path' => 'DYNAMIC_NUM',
        'hidden' => null,
        'desc' => null,
        'recordno' => 158,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'decimal',
            'type' => 'decimal',
            'maxlength' => 13,
            'size' => 20,
            'format' => '/^[-+]?[0-9]{0,10}(\.[0-9]+)?$/',
            'leftofdecimal' => 10,
            'rightofdecimal' => 2,
            'precision' => 2,
            'noautodecimal' => 1,
            'calcurlargs' => '.noround=1',
        ],
        'partialedit' => 1
    ];

    /** @var array $dynamicFieldCheckbox */
    private $dynamicFieldCheckbox = [
        'fullname' => 'checkbox',
        'required' => null,
        'path' => 'CHECKBOX',
        'hidden' => null,
        'desc' => null,
        'recordno' => 151,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'enum',
            'type' => 'boolean',
            'validlabels' => ["ON", "OFF"],
            'validvalues' => ["true", "false"],
            '_validivalues' => ["T", "F"],
            'size' => 5,
        ],
        'partialedit' => 1,
        'default' => false,
    ];

    /** @var array $dynamicFieldMultiSelect */
    private $dynamicFieldMultiSelect = [
        'fullname' => 'multi_picklist',
        'required' => false,
        'path' => 'MULTI_PICKLIST',
        'hidden' => false,
        'desc' => NULL,
        'recordno' => '261',
        'fastUpdate' => true,
        'type' => [
                'ptype' => 'multipick',
                'type' => 'multipick',
                'delimiter' => '#~#',
                'validvalues' => [ 'one', 'two', 'three', 'four'],
            ],
        'partialedit' => true,
        'default' => '',
        'rowcount' => '',
    ];

    /**
     * @var array
     */
    private $dynamicFieldEnum = [
        'fullname' => 'ENUM',
        'required' => null,
        'path' => 'DYNAMIC_ENUM',
        'hidden' => null,
        'desc' => null,
        'recordno' => 151,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'enum',
            'type' => 'enum',
            'validlabels' => ["ON", "OFF", "NA"],
            'apivalues' => ["on", "off", null],
            'validvalues' => ["a", "b", null],
            '_validivalues' => ["1", "2", "0"],
            'size' => 5,
        ],
        'partialedit' => 1,
        'default' => false,
    ];

    /**
     * @var array $dynamicFieldText1
     */
    private $dynamicFieldText1 = [
        'fullname' => 'Dynamic_Text1',
        'required' => false,
        'path' => 'DYNAMIC_TEXT1',
        'hidden' => null,
        'desc' => null,
        'recordno' => 248,
        'fastUpdate' => 1,
        'type' => [
            'type' => 'text',
            'ptype' => 'text',
            'maxlength' => 100,
            'size' => 40,
        ],
        'partialedit' => 1
    ];

    /** @var array $dynamicFieldDec1 */
    private $dynamicFieldDec1 = [
        'fullname' => 'Dynamic_Num1',
        'required' => null,
        'path' => 'DYNAMIC_NUM1',
        'hidden' => null,
        'desc' => null,
        'recordno' => 258,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'decimal',
            'type' => 'decimal',
            'maxlength' => 13,
            'size' => 20,
            'format' => '/^[-+]?[0-9]{0,10}(\.[0-9]+)?$/',
            'leftofdecimal' => 10,
            'rightofdecimal' => 2,
            'precision' => 2,
            'noautodecimal' => 1,
            'calcurlargs' => '.noround=1',
        ],
        'partialedit' => 1
    ];

    /** @var array $dynamicFieldCheckbox1 */
    private $dynamicFieldCheckbox1 = [
        'fullname' => 'checkbox1',
        'required' => null,
        'path' => 'CHECKBOX1',
        'hidden' => null,
        'desc' => null,
        'recordno' => 151,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'enum',
            'type' => 'boolean',
            'validlabels' => ["ON", "OFF"],
            'validvalues' => ["true", "false"],
            '_validivalues' => ["T", "F"],
            'size' => 5,
        ],
        'partialedit' => 1,
        'default' => false,
    ];

    /** @var array $dynamicFieldMultiSelect1 */
    private $dynamicFieldMultiSelect1 = [
        'fullname' => 'multi_picklist1',
        'required' => false,
        'path' => 'MULTI_PICKLIST1',
        'hidden' => false,
        'desc' => NULL,
        'recordno' => '361',
        'fastUpdate' => true,
        'type' => [
            'ptype' => 'multipick',
            'type' => 'multipick',
            'delimiter' => '#~#',
            'validvalues' => [ 'one', 'two', 'three', 'four'],
        ],
        'partialedit' => true,
        'default' => '',
        'rowcount' => '',
    ];

    /**
     * @var array
     */
    private $dynamicFieldEnum1 = [
        'fullname' => 'ENUM1',
        'required' => null,
        'path' => 'DYNAMIC_ENUM1',
        'hidden' => null,
        'desc' => null,
        'recordno' => 151,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'enum',
            'type' => 'enum',
            'validlabels' => ["ON", "OFF", "NA"],
            'apivalues' => ["on", "off", null],
            'validvalues' => ["a", "b", null],
            '_validivalues' => ["1", "2", "0"],
            'size' => 5,
        ],
        'partialedit' => 1,
        'default' => false,
    ];

    /**
     * @var array $dynamicFieldText2
     */
    private $dynamicFieldText2 = [
        'fullname' => 'Dynamic_Text2',
        'required' => false,
        'path' => 'DYNAMIC_TEXT2',
        'hidden' => null,
        'desc' => null,
        'recordno' => 348,
        'fastUpdate' => 1,
        'type' => [
            'type' => 'text',
            'ptype' => 'text',
            'maxlength' => 100,
            'size' => 40,
        ],
        'partialedit' => 1
    ];

    /** @var array $dynamicFieldDec2 */
    private $dynamicFieldDec2 = [
        'fullname' => 'Dynamic_Num2',
        'required' => null,
        'path' => 'DYNAMIC_NUM2',
        'hidden' => null,
        'desc' => null,
        'recordno' => 258,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'decimal',
            'type' => 'decimal',
            'maxlength' => 13,
            'size' => 20,
            'format' => '/^[-+]?[0-9]{0,10}(\.[0-9]+)?$/',
            'leftofdecimal' => 10,
            'rightofdecimal' => 2,
            'precision' => 2,
            'noautodecimal' => 1,
            'calcurlargs' => '.noround=1',
        ],
        'partialedit' => 1
    ];

    /** @var array $dynamicFieldCheckbox2 */
    private $dynamicFieldCheckbox2 = [
        'fullname' => 'checkbox2',
        'required' => null,
        'path' => 'CHECKBOX2',
        'hidden' => null,
        'desc' => null,
        'recordno' => 151,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'enum',
            'type' => 'boolean',
            'validlabels' => ["ON", "OFF"],
            'validvalues' => ["true", "false"],
            '_validivalues' => ["T", "F"],
            'size' => 5,
        ],
        'partialedit' => 1,
        'default' => false,
    ];

    /** @var array $dynamicFieldMultiSelect2 */
    private $dynamicFieldMultiSelect2 = [
        'fullname' => 'multi_picklist1',
        'required' => false,
        'path' => 'MULTI_PICKLIST1',
        'hidden' => false,
        'desc' => NULL,
        'recordno' => '361',
        'fastUpdate' => true,
        'type' => [
            'ptype' => 'multipick',
            'type' => 'multipick',
            'delimiter' => '#~#',
            'validvalues' => [ 'one', 'two', 'three', 'four'],
        ],
        'partialedit' => true,
        'default' => '',
        'rowcount' => '',
    ];

    /**
     * @var array[]
     */
    private $customUDDs = [
        'DIMTEST1' => [
            'field' => 'GLDIMXXX1',
            'object'      => 'class', // does not matter for this test
            'relation'    => 'MANY2ONE',
        ],
        'DIMTEST2' => [
            'field' => 'GLDIMXXX2',
            'object'      => 'employee', // does not matter for this test
            'relation'    => 'MANY2ONE',
        ],
    ];

    /**
     * @var array
     */
    private $dynamicFieldEnum2 = [
        'fullname' => 'ENUM2',
        'required' => null,
        'path' => 'DYNAMIC_ENUM2',
        'hidden' => null,
        'desc' => null,
        'recordno' => 151,
        'fastUpdate' => 1,
        'type' => [
            'ptype' => 'enum',
            'type' => 'enum',
            'validlabels' => ["ON", "OFF", "NA"],
            'apivalues' => ["on", "off", null],
            'validvalues' => ["a", "b", null],
            '_validivalues' => ["1", "2", "0"],
            'size' => 5,
        ],
        'partialedit' => 1,
        'default' => false,
    ];


    /**
     * @var string $glbatchJSON
     */
    private $glbatchJSON = '{
        "key": "3388",
        "id": "3388",
        "batchNo": 13,
        "batchTitle": "Time entries for 12\/26\/2010 - 12\/31\/2019",
        "balance": null,
        "journal": "ACT",
        "journal": "23",
        "batchDate": "2019-12-31",
        "audit": {
            "whenCreated": "2020-03-24T22:22:08Z",
            "whenModified": "2020-03-24T22:22:08Z",
            "createdBy": "1",
            "modifiedBy": "1",
            "dynamicText1": "dynamic_Text dynamic_Text1",
            "dynamicNum1": 201,
            "dynamicEnum1": "on"
        },
        "state": "Posted",
        "vat": {
            "taxImplications": "None",
            "contact": {
                "id": "1",
                "dynamicText2": "dynamic_Text dynamic_Text2",
                "dynamicNum2": 222,
                "dynamicEnum2": null
            }
        },
        "dynamicText": "dynamic_Text dynamic_Text",
        "dynamicNum": 2,
        "dynamicEnum": "off"
    }';

    /**
     * @var string $glentryJSON
     */
    private $glentryJSON = '
        {
            "key": "6953",
            "id": "6953",
            "glbatch": {
                "key": "3389",
                "id": "3389",
                "name": "Time entries for 12\/26\/2010 - 12\/31\/2019"
            },
            "trType": "1",
            "entryDate": "2019-12-31",
            "amount": 7.5,
            "accountKey": "186",
            "accountNO": "9001",
            "departmentKey": null,
            "department": null,
            "location": "Corporate",
            "description": null,
            "currency": "AOA",
            "audit": {
                "whenCreated": "2020-03-24T22:52:06Z",
                "whenModified": "2020-03-24T22:52:06Z",
                "createdBy": "1",
                "modifiedBy": "1",
                "dynamicText1": "dynamic_Text dynamic_Text11",
                "dynamicNum1": 2100,
                "dynamicEnum1": "on"
            },
            "dynamicText": "dynamic_Text dynamic_Text",
            "dynamicNum": 21,
            "dynamicEnum": "off"
        }
    ';

    /**
     * @var array $glbatchEnt
     */
    private $glbatchEnt = [
        'RECORDNO' => 3388,
        'BATCHNO' => 13,
        'BATCH_TITLE' => 'Time entries for 12/26/2010 - 12/31/2019',
        'BALANCE' => null,
        'JOURNAL' => 'ACT',
        'JOURNALKEY' => 23,
        'BATCH_DATE' => '12/31/2019',
        'WHENCREATED' => '03/24/2020 22:22:08',
        'WHENMODIFIED' => '03/24/2020 22:22:08',
        'CREATEDBY' => 1,
        'MODIFIEDBY' => 1,
        'STATE' => 'Posted',
        'TAXIMPLICATIONS' => '',
        'DYNAMIC_TEXT' => 'dynamic_Text dynamic_Text',
        'DYNAMIC_NUM' => "2.2",
        'DYNAMIC_TEXT1' => 'dynamic_Text dynamic_Text1',
        'DYNAMIC_NUM1' => "3.2",
        'DYNAMIC_TEXT2' => 'dynamic_Text dynamic_Text2',
        'DYNAMIC_NUM2' => "3.2222",
        'DYNAMIC_ENUM' => 'b',
        'DYNAMIC_ENUM1' => 'a',
        'DYNAMIC_ENUM2' => null
    ];

    /**
     * @var string $query1API
     */
    private $query1API = '{
         "fields": ["key", "dynamicText", "dynamicNum", "dynamicEnum", "audit.dynamicText1", "audit.dynamicNum1", "audit.dynamicEnum1", 
                    "vat.contact.dynamicText2", "vat.contact.dynamicNum2", "vat.contact.dynamicEnum2"],
         "filters": [
             {
                 "$ne" : { "dynamicText": null }
             },
             {
                  "$ne" : { "audit.dynamicText1": null }
             },
             {
                  "$ne" : { "vat.contact.dynamicText2": null }
             }
         ]
    }';

    /**
     * @var array $query1Ent
     */
    private $query1Ent = [
        'selects' => ['RECORDNO', 'DYNAMIC_TEXT', 'DYNAMIC_NUM', 'DYNAMIC_ENUM', 'DYNAMIC_TEXT1', 'DYNAMIC_NUM1', 'DYNAMIC_ENUM1',
                      'DYNAMIC_TEXT2', 'DYNAMIC_NUM2', 'DYNAMIC_ENUM2'],
        'filters' => [
            [
                ['DYNAMIC_TEXT', 'is not null'],
                ['DYNAMIC_TEXT1', 'is not null'],
                ['DYNAMIC_TEXT2', 'is not null']
            ]
        ]
    ];


    /**
     * With aliases, so the same for both queries
     * @var array $queryEntResult
     */
    private $queryEntResult = [
        \APIConstants::IA_RESULT_KEY => [
            [
                'API_QUERY_ALIAS__0' => 123,
                'API_QUERY_ALIAS__1' => 'dynamic_Text dynamic_Text',
                'API_QUERY_ALIAS__2' => 2,
                'API_QUERY_ALIAS__3' => 'b',
                'API_QUERY_ALIAS__4' => 'dynamic_Text dynamic_Text1',
                'API_QUERY_ALIAS__5' => 2,
                'API_QUERY_ALIAS__6' => 'a',
                'API_QUERY_ALIAS__7' => 'dynamic_Text dynamic_Text2',
                'API_QUERY_ALIAS__8' => 222,
                'API_QUERY_ALIAS__9' => null
            ]
        ]

    ];

    /**
     * @var string $query2API
     */
    private $query2API = '{
         "fields": ["key", "glbatch.dynamicText", "glbatch.dynamicNum", "glbatch.dynamicEnum", 
                    "glbatch.audit.dynamicText1", "glbatch.audit.dynamicNum1", "glbatch.audit.dynamicEnum1", 
                    "glbatch.vat.contact.dynamicText2", "glbatch.vat.contact.dynamicNum2", "glbatch.vat.contact.dynamicEnum2"],
         "filters": [
            {
             "$ne" : { "glbatch.dynamicText": null }
            },
            {
             "$ne" : { "glbatch.audit.dynamicText1": null }
            },
            {
             "$ne" : { "glbatch.vat.contact.dynamicText2": null }
            }
         ]
    }';

    /**
     * @var array $query2Ent
     */
    private $query2Ent = [
        'selects' => ['RECORDNO', 'GLBATCH.DYNAMIC_TEXT', 'GLBATCH.DYNAMIC_NUM', 'GLBATCH.DYNAMIC_ENUM',
                      'GLBATCH.DYNAMIC_TEXT1', 'GLBATCH.DYNAMIC_NUM1', 'GLBATCH.DYNAMIC_ENUM1',
                      'GLBATCH.DYNAMIC_TEXT2', 'GLBATCH.DYNAMIC_NUM2', 'GLBATCH.DYNAMIC_ENUM2'],
        'filters' => [
            [
                ['GLBATCH.DYNAMIC_TEXT', 'is not null'],
                ['GLBATCH.DYNAMIC_TEXT1', 'is not null'],
                ['GLBATCH.DYNAMIC_TEXT2', 'is not null']
            ]
        ]
    ];


    /**
     * @var array $glentryEnt
     */
    private $glentryEnt = [
        'RECORDNO' => 6953,
        'BATCHNO' => 3389,
        'BATCH_NUMBER' => 14,
        'BATCHTITLE' => 'Time entries for 12/26/2010 - 12/31/2019',
        'TR_TYPE' => 1,
        'ENTRY_DATE' => '12/31/2019',
        'AMOUNT' => 7.5,
        'ACCOUNTKEY' => 186,
        'ACCOUNTNO' => 9001,
        'LOCATION' => 'Corporate',
        'DESCRIPTION' => null,
        'CURRENCY' => 'AOA',
        'WHENCREATED' => '03/24/2020 22:52:06',
        'WHENMODIFIED' => '03/24/2020 22:52:06',
        'CREATEDBY' => 1,
        'MODIFIEDBY' => 1,
        'DYNAMIC_TEXT' => 'dynamic_Text dynamic_Text',
        'DYNAMIC_NUM' => "21.01",
        'DYNAMIC_TEXT1' => 'dynamic_Text dynamic_Text11',
        'DYNAMIC_NUM1' => "11.01",
        'DYNAMIC_ENUM' => 'b',
        'DYNAMIC_ENUM1' => 'a'
    ];

    /**
     * @var string|mixed
     */
    private string $fsResponse = '{
            "ID": "20",
            "POSTMEMO": "Time entries for 12\/26\/2010 - 12\/31\/2019",
            "MY_LIST": [
                {
                    "FIELD1": "Text1",
                    "FIELD2": 2,
                    "DYNAMIC_TEXT2": "dynamic_Text dynamic_Text1",
                    "DYNAMIC_NUM2": 201,
                    "DYNAMIC_ENUM2": "b",
                    "GLDIMXXX1": "123",
                    "GLDIMXXX2": "223"
                },
                {
                    "FIELD1": "Text2",
                    "FIELD2": 3,
                    "DYNAMIC_TEXT2": "dynamic_Text dynamic_Text2",
                    "DYNAMIC_NUM2": 202,
                    "DYNAMIC_ENUM2": "a",
                    "GLDIMXXX1": "124",
                    "GLDIMXXX2": "224"
                }
            ],
            "DYNAMIC_TEXT": "dynamic_Text dynamic_Text",
            "DYNAMIC_NUM": 2,
            "DYNAMIC_ENUM": "a"
        }';

    /**
     * @var string
     */
    private $convertedFSResponse = '{
            "id": "20",
            "postMemo": "Time entries for 12/26/2010 - 12/31/2019",
            "mylist": [
                {
                    "field1": "Text1",
                    "field2": 2,
                    "dynamicText2": "dynamic_Text dynamic_Text1",
                    "dynamicNum2": 201,
                    "dynamicEnum2": "off",
                    "dimensions": {
                        "co/__testclass": {
                            "key": "123"
                        },
                        "exp/__testemployee": {
                            "key": "223"
                        }
                    }
                },
                {
                    "field1": "Text2",
                    "field2": 3,
                    "dynamicText2": "dynamic_Text dynamic_Text2",
                    "dynamicNum2": 202,
                    "dynamicEnum2": "on",
                    "dimensions": {
                        "co/__testclass": {
                            "key": "124"
                        },
                        "exp/__testemployee": {
                            "key": "224"
                        }
                    }
                }
            ],
            "dynamicText": "dynamic_Text dynamic_Text",
            "dynamicNum": 2,
            "dynamicEnum": "on"
        }';

    private string $fsRequest = '{
            "postDate": "2019-12-31",
            "postMemo": "Time entries for 12\/26\/2010 - 12\/31\/2019",
            "group1": {
                "field1": "Text1",
                "field2": 2,
                "dynamicText1": "dynamic_Text dynamic_Text1",
                "dynamicNum1": 201,
                "dynamicEnum1": "on",
                "dimensions": {
                    "co/__testclass": {
                        "key": "123"
                    },
                    "exp/__testemployee": {
                        "key": "124"
                    }
                }
            },
            "dynamicText": "dynamic_Text dynamic_Text",
            "dynamicNum": 2,
            "dynamicEnum": "off"
    }';

    private string $reportRequest = '{
            "asOfDate": "2019-12-31",
            "name": "Time entries for 12\/26\/2010 - 12\/31\/2019",
            "outputType" : "pdf",
            "parameters": {
                "value1": "Text1",
                "dimensionsFields": {
                    "classUDD": true,
                    "dynamicText1": "dynamic_Text dynamic_Text1",
                    "dynamicNum1": 201,
                    "dynamicEnum1": "on"
                },
                "dimensions": {
                    "co/__testclass": {
                        "key": "123"
                    },
                    "exp/__testemployee": {
                        "key": "124"
                    }
                }
            }
    }';

    /**
     *
     */
    public function testDynamicFieldsInSchema()
    {
        $version = '5-test-beta2';
        $this->_testSchemaWithDynamicFields($version);
        $this->_testCRUDConvertToEnt($version);
        $this->_testCRUDConvertToAPI($version);
    }

    /**
     *
     */
    public function testDynamicFieldsInQUERY()
    {
        $version = '5-test-beta2';
        // through __testglbatch
        $mapping = $this->_testQUERYConvertToEnt($version, true);
        $this->assertArrayHasKey(\APIQueryUtil::__API_QUERY_ENT_FIELD_MAPPING, $mapping);
        $this->_testQUERYConvertToAPI($version, true, $mapping);

        // through __testglentry
        $mapping = $this->_testQUERYConvertToEnt($version, false);
        $this->assertArrayHasKey(\APIQueryUtil::__API_QUERY_ENT_FIELD_MAPPING, $mapping);
        $this->_testQUERYConvertToAPI($version, false, $mapping);
    }

    public function testUIMeta()
    {
        $version = '5-test-beta2';
        $objectName = 'gl/__testglentry'; // no dynamic fields

        print "========== testUIMeta for DynamicFields for $objectName V$version ==========\n";
        // getHandler() will add dynamic fields
        $handler = $this->getHandler($version, $objectName);

        $uischema = $handler->getUIMetadata();
        $fields = $uischema['fields'];
        // print json_encode($uischema) . "\n";
        $dynamicFields = $this->getTopDynamicFields();
        foreach ($dynamicFields as $field=>$def) {
            $this->assertArrayHasKey($field, $fields, json_encode($fields));
        }

        $fields = $uischema[\APIConstants::API_GROUPS_KEY]['audit'][\APIConstants::API_FIELDS_KEY];
        $auditDynamicFields = $this->getGroupDynamicFields();
        foreach ($auditDynamicFields as $field=>$def) {
            $this->assertArrayHasKey($field, $fields, json_encode($fields));
        }
    }

    /**
     * @param string $version
     */
    private function _testSchemaWithDynamicFields(string $version)
    {
        $objectName = 'gl/__testglbatch';

        print "========== Testing Adding DynamicFields for V" . $version . " ==========\n";
        // getHandler() will add dynamic fields
        $handler = $this->getHandler($version, $objectName);

        $schema = $handler->getSchemaDefinition();
        $this->validateDynamicFields($handler, "audit");
        $uimeta = $handler->getUIMetadata();

        // check contact ref fields
        $fields = $schema[\APIConstants::API_GROUPS_KEY]['vat'][\APIConstants::API_REFS_KEY]['contact'][\APIConstants::API_FIELDS_KEY];
        $uifields = $uimeta[\APIConstants::API_GROUPS_KEY]['vat'][\APIConstants::API_REFS_KEY]['contact'][\APIConstants::API_FIELDS_KEY];
        $contactDynamicFields = $this->getContactDynamicFields();
        /** @var   \ComplexFieldHandler $contactComplexFieldHandler */
        $contactComplexFieldHandler = $handler->getEntityField('vat')->getEntityField('contact');
        foreach ($contactDynamicFields as $field=>$def) {
            $this->assertArrayHasKey($field, $fields, json_encode($fields));
            $this->assertArrayHasKey($field, $uifields, json_encode($uifields));
            $this->assertTrue(in_array($def['path'], $contactComplexFieldHandler->getAllFields()));
            $this->assertArrayHasKey($field, $contactComplexFieldHandler->getAllFields());
            if (array_key_exists('apivalues', $def['type'])) {
                $this->assertEquals($def['type']['apivalues'], $fields[$field][\APIConstants::API_ENUM_KEY]);
            }
        }
    }

    public function testFSRequestSchemaWithDynamicFieldsAndUDDs()
    {
        $version = '5-test-beta2';
        $name = '__testdomain-beta2/test-fs/dummy';

        print "========== Testing Adding DynamicFields for $name in V" . $version . " ==========\n";
        // getHandler() adds dynamic fields to objects
        $registry = \RegistryLoader::getInstance($version);
        $handler = $registry->getSchemaHandler($name);
        $this->assertNotNull($handler);
        $serviceClass = $handler->getServiceClassName();
        $this->assertNotNull($serviceClass);
        if (method_exists($serviceClass, 'setTestHelper')) {
            $serviceClass::setTestHelper($this);
        }

        // now reset and get dynamic fields auto-populated
        \RegistryLoader::reset();
        $registry = \RegistryLoader::getInstance($version);
        $handler = $registry->getSchemaHandler($name);
        $requestHandler = $handler->getRequestHandler();
        $schema = $this->validateDynamicFields($requestHandler, "group1");
        $dimensions = $schema[\APIConstants::API_GROUPS_KEY]["group1"][\APIConstants::API_GROUPS_KEY][\SchemaHandler::DIMENSIONS_GROUP_NAME] ?? [];
        $this->assertNotEmpty($dimensions, json_encode($schema, 64));
        $UDDsObjects = ["co/__testclass", "exp/__testemployee"];
        foreach ($UDDsObjects as $object) {
            $this->assertArrayHasKey($object, $dimensions[\APIConstants::API_REFS_KEY] ?? [], json_encode($schema, 64));
        }

        $payload = json_decode($this->fsRequest, true);
        $result = $this->convert2EntAndValidate($handler, $payload, $name, $version);
        foreach (array_column($UDDsObjects, 'field') as $field) {
            $this->assertArrayHasKey($field, $result, json_encode($result, JSON_UNESCAPED_SLASHES));
        }

        $convertedBack = \APIUtil::convertEnt2ApiCollection(\ObjectAPIAdapter::CALLER_API, 'query',
                                                            $name, $version, [$result], $handler->getRequestHandler(),
                                                            $handler->getRequestHandler()->getAdapter())[0] ?? [];
        \APITestHelper::unsetRecursive($convertedBack, \APIConstants::API_HREF_FIELD_NAME);
        $this->assertEquals($payload, $convertedBack);
    }

    public function testFSResponseSchemaWithDynamicFieldsAndUDDs()
    {
        $version = '5-test-beta2';
        $name = '__testdomain-beta2/test-fs/dummy';

        print "========== Testing Adding DynamicFields for $name in V" . $version . " ==========\n";
        // getHandler() adds dynamic fields to objects
        $registry = \RegistryLoader::getInstance($version);
        $handler = $registry->getSchemaHandler($name);
        $this->assertNotNull($handler);
        $serviceClass = $handler->getServiceClassName();
        $this->assertNotNull($serviceClass);
        if (method_exists($serviceClass, 'setTestHelper')) {
            $serviceClass::setTestHelper($this);
        }

        // now reset and get dynamic fields auto-populated
        \RegistryLoader::reset();
        $registry = \RegistryLoader::getInstance($version);
        $handler = $registry->getSchemaHandler($name);

        $responseHandler = $handler->getResponseHandler();
        $schema = $this->validateDynamicFields($responseHandler, null, "mylist");
        $dimensions = $schema[\APIConstants::API_LISTS_KEY]["mylist"][\APIConstants::API_GROUPS_KEY][\SchemaHandler::DIMENSIONS_GROUP_NAME] ?? [];
        $this->assertNotEmpty($dimensions, json_encode($schema, 64));
        $UDDsObjects = ["co/__testclass", "exp/__testemployee"];
        foreach ($UDDsObjects as $object) {
            $this->assertArrayHasKey($object, $dimensions[\APIConstants::API_REFS_KEY] ?? [], json_encode($schema, 64));
        }

        // use query request type to bypass insertable/updatable validation
        $entResult = \APIUtil::convertApi2Ent(
            \ObjectAPIAdapter::CALLER_API, 'query', $name, $version, json_decode($this->convertedFSResponse, true), $responseHandler, $responseHandler->getAdapter());
        print json_encode($entResult, 64) . "\n";

        $payload = json_decode($this->fsResponse, true);
        $this->assertEquals($payload, $entResult);

        $result = \APIUtil::convertEnt2ApiCollection(
            \ObjectAPIAdapter::CALLER_API, 'query', $name, $version, [$payload],
            $handler, $handler->getAdapter())[0];
        print json_encode($result, 64) . "\n";

        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldText), $result, json_encode($result, JSON_UNESCAPED_SLASHES));
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldEnum), $result, json_encode($result, JSON_UNESCAPED_SLASHES));
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldDec), $result, json_encode($result, JSON_UNESCAPED_SLASHES));
        $this->assertIsNumeric($result[$this->getAPIFieldName($this->dynamicFieldDec)], json_encode($result, JSON_UNESCAPED_SLASHES));
        $this->assertIsNotString($result[$this->getAPIFieldName($this->dynamicFieldDec)], json_encode($result, JSON_UNESCAPED_SLASHES));
        $list = $result['mylist'];
        foreach ($list as $item) {
            $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldText2), $item, json_encode($item, JSON_UNESCAPED_SLASHES));
            $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldEnum2), $item, json_encode($item, JSON_UNESCAPED_SLASHES));
            $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldDec2), $item, json_encode($item, JSON_UNESCAPED_SLASHES));
            $this->assertIsNumeric($item[$this->getAPIFieldName($this->dynamicFieldDec2)], json_encode($item, JSON_UNESCAPED_SLASHES));
            $this->assertIsNotString($item[$this->getAPIFieldName($this->dynamicFieldDec2)], json_encode($item, JSON_UNESCAPED_SLASHES));
            $this->assertArrayHasKey(\SchemaHandler::DIMENSIONS_GROUP_NAME, $item, json_encode($item, JSON_UNESCAPED_SLASHES));
            $dimensions = $item[\SchemaHandler::DIMENSIONS_GROUP_NAME];
            foreach ($UDDsObjects as $object) {
                $this->assertArrayHasKey($object, $dimensions, json_encode($item, 64));
            }
        }

        $convertedBack = \APIUtil::convertApi2Ent(
            \ObjectAPIAdapter::CALLER_API, 'query', $name, $version, $result, $responseHandler, $responseHandler->getAdapter());
        $this->assertEquals($payload, $convertedBack);
    }

    public function testReportWithUDDs()
    {
        $version = '5-test-beta2';
        $name = 'services/reports/__test-report';

        print "========== Testing Adding DynamicFields for $name in V" . $version . " ==========\n";
        // getHandler() adds dynamic fields to objects
        $registry = \RegistryLoader::getInstance($version);
        $handler = $registry->getSchemaHandler($name);
        $this->assertNotNull($handler);
        $reportClass = $handler->getServiceClassName();
        $this->assertNotNull($reportClass);
        if ( method_exists($reportClass, 'setTestHelper') ) {
            $reportClass::setTestHelper($this);
        } else {
            $this->assertTrue(false, "$reportClass does not have setTestHelper method");
        }

        // now reset and get dynamic fields auto-populated
        \RegistryLoader::reset();
        $registry = \RegistryLoader::getInstance($version);
        $handler = $registry->getSchemaHandler($name);
        $requestHandler = $handler->getRequestHandler();
        $schema = $requestHandler->getSchemaDefinition();
        print json_encode($schema, 64) . "\n";
        $dimensions = $schema[\APIConstants::API_GROUPS_KEY]["parameters"][\APIConstants::API_GROUPS_KEY][\SchemaHandler::DIMENSIONS_GROUP_NAME] ?? [];
        $this->assertNotEmpty($dimensions, json_encode($schema, 64));
        $UDDsObjects = ["co/__testclass", "exp/__testemployee"];
        foreach ($UDDsObjects as $object) {
            $this->assertArrayHasKey($object, $dimensions[\APIConstants::API_REFS_KEY] ?? [], json_encode($schema, 64));
        }
        // now check group inside parameters group
        $groupName = "dimensionsFields";
        /** @var   \ComplexFieldHandler $groupComplexFieldHandler */
        $groupComplexFieldHandler = $requestHandler->getEntityField("parameters")->getEntityField($groupName);
        $fields = $schema[\APIConstants::API_GROUPS_KEY]["parameters"][\APIConstants::API_GROUPS_KEY][$groupName][\APIConstants::API_FIELDS_KEY];
        $this->assertNotEmpty($fields, json_encode($schema, 64));
        $groupDynamicFields = $this->getGroupDynamicFields(); // use "audit" fields
        foreach ( $groupDynamicFields as $field => $def ) {
            $this->assertArrayHasKey($field, $fields, json_encode($fields));
            $this->assertTrue(in_array($def['path'], $groupComplexFieldHandler->getAllFields()));
            $this->assertArrayHasKey($field, $groupComplexFieldHandler->getAllFields());
            if ( array_key_exists('apivalues', $def['type']) ) {
                $this->assertEquals($def['type']['apivalues'], $fields[$field][\APIConstants::API_ENUM_KEY]);
            }
        }

        $payload = json_decode($this->reportRequest, true);
        $result = \APIUtil::convertApi2Ent(
            \ObjectAPIAdapter::CALLER_API, 'query', $name, $version, $payload, $requestHandler, $requestHandler->getAdapter());
        print_r(json_encode($result, 64)) . "\n";

        $convertedBack = \APIUtil::convertEnt2ApiCollection(
            \ObjectAPIAdapter::CALLER_API, 'query', $name, $version, [$result], $requestHandler, $requestHandler->getAdapter())[0] ?? [];
        \APITestHelper::unsetRecursive($convertedBack, \APIConstants::API_HREF_FIELD_NAME);
        $this->assertEquals($payload, $convertedBack);
    }

    /**
     * @param string $version
     */
    private function _testCRUDConvertToEnt(string $version)
    {
        print "========== Testing convertApi2Ent for V" . $version . " ==========\n";

        $objectName = 'gl/__testglbatch';
        $handler = $this->getHandler($version, $objectName);
        $payload = json_decode($this->glbatchJSON, true);
        $schema = $handler->getSchemaDefinition();
        $apiItemName = key($schema[\APIConstants::API_LISTS_KEY]); // only one there
        $payload[$apiItemName][] = json_decode($this->glentryJSON, true);
        $result = $this->convert2EntAndValidate($handler, $payload, $objectName, $version, "ENTRIES");

        $field = $this->dynamicFieldEnum['path'];
        $field1 = $this->dynamicFieldEnum1['path'];
        $this->assertEquals($this->glbatchEnt[$field], $result[$field]);
        $this->assertEquals($this->glbatchEnt[$field1], $result[$field1]);
        $this->assertEquals($this->glentryEnt[$field], $result['ENTRIES'][0][$field]);
        $this->assertEquals($this->glentryEnt[$field1], $result['ENTRIES'][0][$field1]);
    }

    /**
     * @param string $version
     */
    private function _testCRUDConvertToAPI(string $version)
    {
        print "========== Testing convertEnt2Api for V" . $version . " ==========\n";

        $objectName = 'gl/__testglbatch';
        $handler = $this->getHandler($version, $objectName);

        $glbatchEnt = $this->glbatchEnt;
        $glbatchEnt['ENTRIES'][] = $this->glentryEnt;

        // use query to bypass insertable/updatable validation
        $result = \APIUtil::convertEnt2ApiCollection(
            \ObjectAPIAdapter::CALLER_API, 'query', $objectName, $version, [$glbatchEnt],
            $handler, $handler->getAdapter())[0];
        //print_r($result);

        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldText), $result);
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldDec), $result);
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldText1), $result['audit']);
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldDec1), $result['audit']);
        $this->assertIsNumeric($result[$this->getAPIFieldName($this->dynamicFieldDec)]);
        $this->assertIsNotString($result[$this->getAPIFieldName($this->dynamicFieldDec)]);

        $schema = $handler->getSchemaDefinition();
        $apiItemName = key($schema[\APIConstants::API_LISTS_KEY]); // only one there
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldText), $result[$apiItemName][0]);
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldDec), $result[$apiItemName][0]);
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldText1), $result[$apiItemName][0]['audit']);
        $this->assertArrayHasKey($this->getAPIFieldName($this->dynamicFieldDec1), $result[$apiItemName][0]['audit']);
        $this->assertIsNumeric($result[$apiItemName][0][$this->getAPIFieldName($this->dynamicFieldDec)]);
        $this->assertIsNotString($result[$apiItemName][0][$this->getAPIFieldName($this->dynamicFieldDec)]);

        $glbatch = json_decode($this->glbatchJSON, true);
        $glentry = json_decode($this->glentryJSON, true);
        $field = $this->getAPIFieldName($this->dynamicFieldEnum);
        $this->assertEquals($glbatch[$field], $result[$field]);
        $field1 = $this->getAPIFieldName($this->dynamicFieldEnum1);
        $this->assertEquals($glentry['audit'][$field1], $result[$apiItemName][0]['audit'][$field1]);
    }

    /**
     * @param string $version
     * @param bool   $owner
     *
     * @return array of mappings
     * @throws \Exception
     */
    private function _testQUERYConvertToEnt(string $version, bool $owner)
    {

        if ($owner) {
            print "========== Testing convertQueryApi2Ent for V" . $version . " ==========\n";
            $objectName = 'gl/__testglbatch';
            $payload = json_decode($this->query1API, true);
            $handler = $this->getHandler($version, $objectName);
            $expectedResult = $this->query1Ent;
        } else {
            print "========== Testing convertQueryApi2Ent for V" . $version . " ==========\n";
            $objectName = 'gl/__testglentry';
            $payload = json_decode($this->query2API, true);
            $handler = $this->getHandler($version, $objectName);
            $expectedResult = $this->query2Ent;
        }

        $result = \APIQueryUtil::convertQueryApi2Ent($payload, $handler);
        // print_r($result['query']);

        $this->assertTrue(\APITestHelper::assertArraySubset($expectedResult, $result['query']));

        return $result['mapping'];
    }

    /**
     * @param string $version
     * @param bool   $owner
     *
     * @param array  $mapping
     *
     * @throws \Exception
     */
    private function _testQUERYConvertToAPI(string $version, bool $owner, array $mapping)
    {

        if ($owner) {
            print "========== Testing __testglbatch convertQueryEnt2Api for V" . $version . " ==========\n";
            $objectName = 'gl/__testglbatch';
            $selects = json_decode($this->query1API, true)[\APIConstants::API_FIELDS_KEY];
            $handler = $this->getHandler($version, $objectName);
        } else {
            print "========== Testing __testglentry convertQueryEnt2Api for V" . $version . " ==========\n";
            $objectName = 'gl/__testglentry';
            $selects = json_decode($this->query2API, true)[\APIConstants::API_FIELDS_KEY];
            $handler = $this->getHandler($version, $objectName);
        }

        // test convertQueryEnt2Api
        $result = \APIQueryUtil::convertQueryEnt2Api(
             $this->queryEntResult, $handler,
             $mapping)[\APIConstants::IA_RESULT_KEY];
        //print_r($result);

        foreach ($selects as $field) {
            $this->assertArrayHasKey($field, $result[0]);
        }
    }

    /**
     *
     * @return array
     */
    public function getTopDynamicFields(): array
    {
        return [
            $this->getAPIFieldName($this->dynamicFieldText) => $this->dynamicFieldText,
            $this->getAPIFieldName($this->dynamicFieldDec) => $this->dynamicFieldDec,
            $this->getAPIFieldName($this->dynamicFieldCheckbox) => $this->dynamicFieldCheckbox,
            $this->getAPIFieldName($this->dynamicFieldMultiSelect) => $this->dynamicFieldMultiSelect,
            $this->getAPIFieldName($this->dynamicFieldEnum) => $this->dynamicFieldEnum,
        ];
    }

    /**
     * @param array $fieldInfo
     *
     * @return string
     */
    private function getAPIFieldName(array $fieldInfo)
    {
        $fieldName = str_replace(' ', '', ucwords(str_replace('_', ' ', strtolower($fieldInfo[\CustomFieldInfo::API_FIELDINFO_PATH]))));
        // and make it camelCase for function name
        $fieldName[0] = strtolower($fieldName[0]);
        return $fieldName;
    }

    /**
     *
     * @return array
     */
    public function getGroupDynamicFields(): array
    {
        return [
            $this->getAPIFieldName($this->dynamicFieldText1) => $this->dynamicFieldText1,
            $this->getAPIFieldName($this->dynamicFieldDec1) => $this->dynamicFieldDec1,
            $this->getAPIFieldName($this->dynamicFieldCheckbox1) => $this->dynamicFieldCheckbox1,
            $this->getAPIFieldName($this->dynamicFieldMultiSelect1) => $this->dynamicFieldMultiSelect1,
            $this->getAPIFieldName($this->dynamicFieldEnum1) => $this->dynamicFieldEnum1,
        ];
    }

    /**
     * @return array[]
     */
    public function getCustomUDDs() : array
    {
        return $this->customUDDs;
    }

    /**
     *
     * @return array
     */
    public function getContactDynamicFields(): array
    {
        return [
            $this->getAPIFieldName($this->dynamicFieldText2) => $this->dynamicFieldText2,
            $this->getAPIFieldName($this->dynamicFieldDec2) => $this->dynamicFieldDec2,
            $this->getAPIFieldName($this->dynamicFieldCheckbox2) => $this->dynamicFieldCheckbox2,
            $this->getAPIFieldName($this->dynamicFieldMultiSelect2) => $this->dynamicFieldMultiSelect2,
            $this->getAPIFieldName($this->dynamicFieldEnum2) => $this->dynamicFieldEnum2,
        ];
    }

    /**
     * @return array
     */
    public function getListDynamicFields() : array
    {
        return $this->getContactDynamicFields(); // reuse
    }

    /**
     * @param string     $version
     * @param string     $objectName
     *
     * @return \SchemaHandler|null
     */
    private function getHandler(string $version, string $objectName)
    {
        $registry = \RegistryLoader::getInstance($version);
        $handler = $registry->getSchemaHandler($objectName);
        $this->assertNotNull($handler);
        $includeDynamicAddOns = [
            \APIConstants::API_INCLUDE_DYNAMIC_COMPONENTS => "",
            \APIConstants::API_GROUPS_KEY => [
                "audit" => [
                    \APIConstants::API_INCLUDE_DYNAMIC_COMPONENTS => "audit",
                ],
                "vat" => [
                    \APIConstants::API_REFS_KEY => [
                        "contact" => [
                            \APIConstants::API_INCLUDE_DYNAMIC_COMPONENTS => "contact",
                        ]
                    ]
                ]
            ]
        ];
        $topDynamicFields = $this->getTopDynamicFields();
        $auditDynamicFields = $this->getGroupDynamicFields();
        $dynamicFields = [ "" => $topDynamicFields, "audit" => $auditDynamicFields, "contact" => $this->getContactDynamicFields()];
        $handler->internalAddDynamicComponents($includeDynamicAddOns, $dynamicFields);

        // keep only audit dynamic fields for related objects
        $includeDynamicAddOns = [
            \APIConstants::API_INCLUDE_DYNAMIC_COMPONENTS => "",
            \APIConstants::API_GROUPS_KEY => [
                "audit" => [
                    \APIConstants::API_INCLUDE_DYNAMIC_COMPONENTS => "audit",
                ]
            ],
        ];
        $dynamicFields = [ "" => $topDynamicFields, "audit" => $auditDynamicFields ];
        foreach ($handler->getRelatedObjects() as $object) {
            $otherHandler = $handler->getRelatedHandler($object);
            $otherHandler->internalAddDynamicComponents($includeDynamicAddOns, $dynamicFields);
        }
        return $handler;
    }

    /**
     * @param mixed            $apiValue
     * @param mixed            $entValue
     *
     * @param \CustomFieldInfo $dynamicField
     *
     * @return mixed
     * @throws \APIException
     * @throws \APIInternalException
     */
    protected function validateEntAPIConversion($apiValue, $entValue, \CustomFieldInfo $dynamicField )
    {
        $value = $apiValue;
        $dynamicField->validateAndAdjustAPIValue($value);
        $this->assertEquals($entValue, $value);
        $dynamicField->validateAndAdjustEntValue($value);
        $this->assertEquals($apiValue, $value);

        return $value;
    }

    /**
     * @param \SchemaHandler $handler
     * @param string|null    $groupName
     * @param string|null    $listName
     *
     * @return array
     * @throws \APIException
     * @throws \APIInternalException
     */
    private function validateDynamicFields(\SchemaHandler $handler, ?string $groupName, ?string $listName = null) : array
    {
        $schema = $handler->getSchemaDefinition();
        $uimeta = $handler->getUIMetadata();
        $hasUIMeta = !empty($uimeta);
        print json_encode($schema) . "\n";
        if ( $hasUIMeta ) {
            print json_encode($uimeta) . "\n";
        }
        $fields = $schema['fields'];
        $dynamicFields = $this->getTopDynamicFields();
        foreach ( $dynamicFields as $field => $def ) {
            $this->assertArrayHasKey($field, $fields, json_encode($fields));
            if ( $hasUIMeta ) {
                $this->assertArrayHasKey($field, $uimeta['fields'], json_encode($uimeta));
            }
            $this->assertTrue(in_array($field, $handler->getAllObjectFields()));
            if ( array_key_exists(\APIConstants::API_FORMAT_KEY, $def['type'])
                 && $def['type'][\APIConstants::API_FORMAT_KEY] ) {
                $this->assertArrayHasKey(\APIConstants::API_PATTERN_KEY, $fields[$field]);
            } else {
                $this->assertArrayNotHasKey(\APIConstants::API_PATTERN_KEY, $fields[$field]);
            }
            if ( array_key_exists('validvalues', $def['type']) ) {
                if ( $def['type']['type'] === 'boolean' ) {
                    $this->assertArrayNotHasKey(\APIConstants::API_ENUM_KEY, $fields[$field]);
                } else if ( $def['type']['type'] === 'multipick' ) {
                    $this->assertEquals(\APIConstants::API_TYPE_ARRAY, $fields[$field][\APIConstants::API_TYPE_KEY]);
                    $this->assertArrayHasKey("delimiter", $fields[$field]);
                    $this->assertArrayNotHasKey(\APIConstants::API_ENUM_KEY, $fields[$field]);
                    $this->assertArrayHasKey(\APIConstants::API_ENUM_KEY,
                                             $fields[$field][\APIConstants::API_ITEMS_KEY]);
                } else {
                    $this->assertArrayHasKey(\APIConstants::API_ENUM_KEY, $fields[$field]);
                }
            } else {
                $this->assertArrayNotHasKey(\APIConstants::API_ENUM_KEY, $fields[$field]);
            }
            if ( array_key_exists('apivalues', $def['type']) ) {
                $this->assertEquals($def['type']['apivalues'], $fields[$field][\APIConstants::API_ENUM_KEY]);
            }
            if ( $field == 'CHECKBOX' ) {
                // print "CHECKBOX\n";
                $fieldInfo = $handler->getObjectFieldInfo($field);
                $value = true;
                $fieldInfo->validateAndAdjustAPIValue($value, $field);
                $this->assertEquals("true", $value);
                try {
                    $fieldInfo->validateAndAdjustAPIValue($value, $field);
                    $APIErrorCollector = \APIErrorCollectorUtil::getInstance();
                    //throw error if there is any error
                    if ( $APIErrorCollector->hasAPIErrors() ) {
                        throw ( new \APIException() )->setAPIError($APIErrorCollector->getAPIErrors()[0]);
                    }
                    $this->fail("string boolean should fail");
                } catch ( \APIException $e ) {
                    $this->assertStringContainsStringIgnoringCase(\APIErrorMessages::buildMessage(
                        \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024, [
                        "FIELD" => $field,
                        "TYPE" => 'string',
                        "EXPECTED_TYPE" => 'boolean'
                    ]),
                        $e->getAPIError()
                          ->getMessage(), "Unexpected resul");
                }
            }
        }
        $this->assertEquals(\APIConstants::API_TYPE_STRING,
                            $fields[$this->getAPIFieldName($this->dynamicFieldText)]['type']);
        $this->assertEquals(\APIConstants::API_TYPE_NUMBER,
                            $fields[$this->getAPIFieldName($this->dynamicFieldDec)]['type']);
        $this->assertEquals(\APIConstants::API_TYPE_BOOLEAN,
                            $fields[$this->getAPIFieldName($this->dynamicFieldCheckbox)]['type']);

        // check group fields
        if ( $groupName ) {
            $fields = $schema[\APIConstants::API_GROUPS_KEY][$groupName][\APIConstants::API_FIELDS_KEY];
            $uifields = $hasUIMeta ? $uimeta[\APIConstants::API_GROUPS_KEY][$groupName][\APIConstants::API_FIELDS_KEY] : [];
            $groupDynamicFields = $this->getGroupDynamicFields(); // use "audit" fields
            /** @var   \ComplexFieldHandler $groupComplexFieldHandler */
            $groupComplexFieldHandler = $handler->getEntityField($groupName);
            foreach ( $groupDynamicFields as $field => $def ) {
                $this->assertArrayHasKey($field, $fields, json_encode($fields));
                if ( $hasUIMeta ) {
                    $this->assertArrayHasKey($field, $uifields, json_encode($uifields));
                }
                $this->assertTrue(in_array($def['path'], $groupComplexFieldHandler->getAllFields()));
                $this->assertArrayHasKey($field, $groupComplexFieldHandler->getAllFields());
                if ( array_key_exists('apivalues', $def['type']) ) {
                    $this->assertEquals($def['type']['apivalues'], $fields[$field][\APIConstants::API_ENUM_KEY]);
                }
            }
        }
        if ($listName) {
            $fields = $schema[\APIConstants::API_LISTS_KEY][$listName][\APIConstants::API_FIELDS_KEY];
            $listDynamicFields = $this->getListDynamicFields(); // use "audit" fields
            /** @var   \ListFieldHandler $listFieldHandler */
            $listFieldHandler = $handler->getEntityField($listName);
            foreach ( $listDynamicFields as $field => $def ) {
                $this->assertArrayHasKey($field, $fields, json_encode($fields));
                $this->assertTrue(in_array($def['path'], $listFieldHandler->getComplexField()->getAllFields()));
                $this->assertArrayHasKey($field, $listFieldHandler->getComplexField()->getAllFields());
                if ( array_key_exists('apivalues', $def['type']) ) {
                    $this->assertEquals($def['type']['apivalues'], $fields[$field][\APIConstants::API_ENUM_KEY]);
                }
            }
        }
        return $schema;
    }

    /**
     * @param \SchemaHandler $handler
     * @param array          $payload
     * @param string         $objectName
     * @param string         $version
     * @param string|null    $entriesName
     *
     * @return string[]
     */
    private function convert2EntAndValidate(\SchemaHandler $handler, array $payload, string $objectName, string $version, ?string $entriesName = null) : array
    {
        //print_r($payload);
        // use query to bypass insertable/updatable validation
        $result = \APIUtil::convertApi2Ent(
            \ObjectAPIAdapter::CALLER_API, 'query', $objectName, $version, $payload, $handler, $handler->getAdapter());
        print_r(json_encode($result, 64)) . "\n";
        $this->assertArrayHasKey($this->dynamicFieldText['path'], $result, json_encode($result, 64));
        $this->assertArrayHasKey($this->dynamicFieldDec['path'], $result, json_encode($result, 64));
        $this->assertArrayHasKey($this->dynamicFieldText1['path'], $result, json_encode($result, 64));
        $this->assertArrayHasKey($this->dynamicFieldDec1['path'], $result, json_encode($result, 64));
        $this->assertIsString($result[$this->dynamicFieldDec['path']], json_encode($result, 64));

        if ($entriesName) {
            $this->assertArrayHasKey($this->dynamicFieldText['path'], $result[$entriesName][0], json_encode($result, 64));
            $this->assertArrayHasKey($this->dynamicFieldDec['path'], $result[$entriesName][0], json_encode($result, 64));
            $this->assertArrayHasKey($this->dynamicFieldText1['path'], $result[$entriesName][0], json_encode($result, 64));
            $this->assertArrayHasKey($this->dynamicFieldDec1['path'], $result[$entriesName][0], json_encode($result, 64));
            $this->assertIsString($result[$entriesName][0][$this->dynamicFieldDec['path']], json_encode($result, 64));
        }

        return $result;
    }
}
