<?php
  
/**
 * Test for API operations
 *
 * <AUTHOR>
 * (C)2020 Sage Intacct Corporation, All Rights Reserved
 */
/**
 * Class APIUserViewWithAppTest
 *
 * Uses Company and login ID defined in unitTest/core/unitTest.cfg
 *
 */
namespace tests\source\api\framework\query;
require_once (__DIR__.'/../APITestHelper.cls');

/**
 * Class APIUserViewWithAppTest
 */
class APIUserViewWithAppTest extends \unitTest\core\UnitTestBaseContext
{
    /** @var bool $tolog */
    var $tolog = false;
    /**  @var string $userViewobjectName */
    var $userViewobjectName = 'core/user-view';
    /** @var string[]  $userViewSplit*/
    var  $userViewSplit = ['core', 'user-view'];
    /**  @var string $fventName */
    var $fventName = 'filterview';
    /**  @var string $queryService */
    var $queryService = 'core/query';
    /**  @var string $vendorQuery */
    var $vendorQuery = '{
         "object" : "ap/__testvendor",
         "fields": [
             "id",
             "name",
             "status",
             "audit.createdBy"
         ],
         "filters": [{
             "$eq" : { "status": "active" }
         }],
         "orderBy" : [{
            "audit.createdBy" : "asc"
         }]
    }';

    /**  @var string $vendorQueryWDate */
    var $vendorQueryWDate = '{
         "object" : "ap/__testvendor",
         "fields": [
             "id",
             "name"
         ],
         "filters": [{
             "$gt" : { "audit.whenCreated": "today" }
         }]
    }';

    /** @var string $glentryFilter */
    var $glentryFilter = '{
	    "name" : "__testglentry-paged",
	    "query" : {
		    "object" : "gl/__testglentry",
    	    "fields": [
         	    "accountNO",
                "amount",
                "glbatch.batchNo",
                "glbatch.batchTitle",
                "glbatch.batchDate",
                "glbatch.state",
                "glbatch.audit.createdBy"
    	    ],
    	    "filters": [{
                "$gt" : { "amount": 100 }
    	    }],
		    "size" : 3,
		    "start" : 1
	    }
    }';

    /** @var string $badQuery */
    var $badQuery = '{
            "object" : "ap/__testvendor",
            "fields": [
             "id",
             "key"
            ],
            "filters": [{
             "$gte" : { "parent.key": "A" }
            }],
            "orderBy" : [{
                "name" : "desc"
            }]
         }';

    /** @var string $findNonAPIQuery */
    var $findNonAPIQuery = '{
        "object": "core/user-view",
        "fields": [
    	    "key",
            "description",
            "object",
            "query"
        ],
        "filters" : [{
    	    "$contains": {"description": " UI dialect"}
        }]
    }';

    /** @var string $queryAddedUserViewJSON */
    var $queryAddedUserViewJSON = '{
        "object" : "core/user-view",
         "fields": [
             "key",
             "id",
             "name"
         ],
         "filters": [{
              "$eq" : { "name": "vendorQV2" }
         }]
    }';

    /**  @var array $ownerRef */
    var $ownerRef = [
        'key' => 1,
    ];

    /**  @var array $uifoUIQueryParams */
    var $uifoUIQueryParams = [
        \FilterViewService::SELECTS_KEY    => [
            'RECORDNO', 'LOGINID', 'DESCRIPTION',
        ],
        \FilterViewService::FILTERS_KEY    => [
            [ \UIFilterViewDialect::KEY           => '1',
              \UIFilterViewDialect::PATH_KEY      => 'LOGINID',
              \UIFilterViewDialect::OPERATION_KEY => \FilterViewOperation::GREATER_OR_EQUAL,
              \UIFilterViewDialect::VALUE_KEY     => 'Aaron' ],
            [ \UIFilterViewDialect::KEY           => '2',
              \UIFilterViewDialect::PATH_KEY      => 'LOGINID',
              \UIFilterViewDialect::OPERATION_KEY => \FilterViewOperation::LESS_OR_EQUAL,
              \UIFilterViewDialect::VALUE_KEY     => 'Buser' ],
        ],
        \FilterViewService::EXPRESSION_KEY => '1 and 2'
    ];

    /**  @var array $glentryParams */
    var $glentryParams = [
        \FilterViewService::SELECTS_KEY    => [
            'RECORDNO', 'BATCHNO', 'LINE_NO', 'AMOUNT'
        ]
    ];

    /**  @var array $glentryParamsToFail */
    var $glentryParamsToFail = [
        \FilterViewService::SELECTS_KEY    => [
            'RECORDNO', 'BATCHNO', 'LINE_NO', 'AMOUNT', 'GLBATCH.JOURNALKEY'
        ],
        \FilterViewService::FILTERS_KEY    => [
            [
                \UIFilterViewDialect::PATH_KEY      => 'GLBATCH.JOURNALKEY',
                \UIFilterViewDialect::OPERATION_KEY => \FilterViewOperation::EQUALS,
                \UIFilterViewDialect::VALUE_KEY     => 5
            ]
        ]
    ];

    /**
     *
     */
    public function testNothing()
    {
        $this->assertTrue(true);
    }

    /**
     * @throws \APIAdapterException
     */
    public function testNegativeVendor()
    {
        $operation = 'create';
        $version = 5 . '-test-beta2';
        print("\n=========== testNegativeVendor ============\n");
        $request = $this->getNewUserView();
        $request['query'] = json_decode($this->badQuery, true);

        $msg = (\APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0107,
                                                                     ["FIELD" => 'parent.key', "OBJECT" => 'ap/__testvendor', "VERSION" => $version] ));
        $this->executeUserViewCRUDOp($request, $version, $operation, [], $msg);

        // query is good but other values are wrong
        $request['query'] = json_decode($this->vendorQuery, true);

        // simulate copy-and-paste error
        $request[\APIConstants::IA_WARNING_KEY] = "POST - User View is incompatible with version [$version]";
        $msg = \APIErrorMessages::buildMessage(
            \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0022, ["FIELD" => '/' . \APIConstants::IA_WARNING_KEY]
        );
        $this->executeUserViewCRUDOp($request, $version, $operation, [], $msg);
        unset($request[\APIConstants::IA_WARNING_KEY]);

        $request['object'] = 'xxx';
        $msg = (\APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0120, [] ));
        $this->executeUserViewCRUDOp($request, $version, $operation, [], $msg);

        unset($request['object']);
        unset($request['name']);

        $msg = (\APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0118, ["FIELD" => 'name'] ));
        $this->executeUserViewCRUDOp($request, $version, $operation, [], $msg);
    }

    /**
     * @dataProvider __testUIQueryInAPIProvider
     *
     * @param string $entity
     * @param array  $uiquery
     * @param string $description
     * @param string $object
     * @param string $version
     * @param bool   $expectsSuccess
     *
     * @throws \APIException
     * @throws \FilterViewFault
     * @throws \IAException
     */
    public function testUIQueryInAPI(string $entity, array $uiquery, string $description, string $object,
                                     string $version = '5-test-beta2', bool $expectsSuccess = true)
    {
        print("\n=========== testUIQueryInAPI for $entity version $version ============\n");
        $this->createNewFilterView($entity, $uiquery, $description, $version);
        [$queries, $meta] = $this->_testExecuteService($version, "core/query", $this->findNonAPIQuery);
        $this->assertGreaterThan(0, $meta[\APIConstants::API_TOTAL_COUNT_KEY] ?? 0, json_encode($queries));
        $this->assertEquals(0, $meta[\APIConstants::API_TOTAL_ERROR_KEY] ?? 0, json_encode($queries));

        // find the one we need from the result
        foreach ($queries as $query) {
            if ($query['description'] === $description) {
                $this->assertEquals($object, $query[\APIConstants::API_OBJECT_TYPE]);
                if ($expectsSuccess) {
                    $this->assertEquals($object, $query[\APIConstants::API_QUERY][\APIConstants::API_OBJECT_TYPE]);
                    $req = ['KEY' => $query['key'], 'viewType' => 'user'];
                } else {
                    $this->assertArrayHasKey(\APIConstants::IA_WARNING_KEY, $query);
                }
                break;
            }
        }
        if (isset($req)) {
            // print_r($query);
            $this->_testExecuteVersion($version, $req, [], false);
        }
    }

    /**
     * @return array|array[]
     */
    public function __testUIQueryInAPIProvider(): array
    {
        return [
            // entity - UI query - new description - exepected object name - version
            ['glentry', $this->glentryParams, 'GL Filter in UI dialect', 'gl/__testglentry', '5-test-beta2'],
            ['userinfo', $this->uifoUIQueryParams, 'UInfo Filter in UI dialect', 'company-config/user', '0-beta2'],
            ['userinfo', $this->uifoUIQueryParams, 'UInfo Filter in UI dialect-1', 'company-config/user', '1-beta2'],
            // [ 'glentry', $this->glentryParamsToFail, 'GL2 Filter in UI dialect', 'glentry', '1-test', false],// no such object in v1-test
            // [ 'glentry', $this->glentryParamsToFail, 'GL3 Filter in UI dialect', '__testglentry', '2-test', false], // object exists but not field in filter
        ];
    }

    /**
     * @throws \APIAdapterException
     */
    public function testNegativeGLEntry()
    {
        print("\n=========== testNegative Query for GLEntry ============\n");
        $operation = 'create';
        $version = 5 . '-test-beta2';
        /** @noinspection PhpUnusedLocalVariableInspection */
        $handler = \RegistryLoader::getInstance($version)->getSchemaHandler($this->userViewobjectName);
        // get a new one
        $request = json_decode($this->glentryFilter, true);
        $request["query"]["object"] = "gl/__testglentry1";
        $err = \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0010,
                                              ["RESOURCE_NAME" => 'gl/__testglentry1', "VERSION" => '5-test-beta2']);
        $this->executeUserViewCRUDOp($request, $version, $operation, [], $err);
    }

    /***
     * @return array of infos used in the followup tests
     */
    public function testAdd()
    {
        $operation = 'create';
        $version = 5 . '-test-beta2';
        print("\n=========== testAdd Query Start ============\n");
        $request = $this->getNewUserView();

        $result = $this->executeUserViewCRUDOp($request, $version, $operation);
        $this->log($result, "=========== Result ============\n");
        if (\Globals::$g->gErr->ErrorCount) {
            $errlist = [];
            \Globals::$g->gErr->GetErrList($errlist);
            print_r($errlist);
            $this->fail();
        }
        $this->assertNotEmpty($result['key']);
        $recordno = $result['key'];

        // fetch object name
        $queryAsArray = json_decode($this->vendorQuery, true);
        $info = ['KEY' => $recordno,'DATA' => $request, 'OBJECT' => $queryAsArray['object'], 'VERSION' => $version];
        $this->_testGetEM($info);
        print("\n=========== testAdd End ============\n");
        return $info;
    }

    /**
     * @return array
     */
    private function getNewUserView()
    {
        $data = [];
        $data['query'] = json_decode($this->vendorQuery, true);
        $data['name'] = 'vendorQV2';
        $data['owner']['id'] = GetMyLogin();
        return $data;
    }

    /**
     * @return array
     */
    private function getNewFVWithDateFilter()
    {
        $data = [];
        $data['query'] = json_decode($this->vendorQueryWDate, true);
        $data['name'] = 'vendorQWDate';
        $data['owner']['id'] = GetMyLogin();
        return $data;
    }

    /**
     * @param array $info
     */
    private function _testGetEM(array $info)
    {
        print("=========== testGetEM ============\n");
        $mgr = \Globals::$g->gManagerFactory->getManager($this->fventName);
        $data = $mgr->get($info['KEY']); // id is the same as key now
        $this->log($data);
        $this->assertEquals($info['KEY'], $data['RECORDNO']);
        $this->assertEquals($info['VERSION'], $data['APIVERSION']);
        print("=========== End testGetEM ============\n");
    }

    /**
     *
     * @depends testAdd
     *
     * @param array $info
     */
    public function testGetSameVersion(array $info)
    {
        print("=========== testGet Query Same version ============\n");
        $version = 5 . '-test-beta2';

        $res = $this->_testGetForVersion($version, $info);
        $this->assertEquals($version, $res[\APIQueryUtil::API_USER_VIEW_VERSION_FIELD]);
        $this->_testUserViewQuery($version, $info);
    }

    // /**
    //  *
    //  * @depends testAdd
    //  *
    //  * @param array $info
    //  */
    // public function testGetDifVersion(array $info)
    // {
    //     $info['DATA']['query']['filters'][0]['$eq']['status'] = 'active';
    //     $version = 3 . '-test';
    //     $res = $this->_testGetForVersion($version, $info);
    //     $this->assertNotEquals($version, $res[\APIQueryUtil::API_USER_VIEW_VERSION_FIELD]);
    //     $this->_testUserViewQuery($version, $info);
    //
    //     $version = 5 . '-test';
    //     $this->_testGetForVersion($version, $info);
    //     $res = $this->_testGetForVersion($version, $info);
    //     $this->assertNotEquals($version, $res[\APIQueryUtil::API_USER_VIEW_VERSION_FIELD]);
    //     $this->_testUserViewQuery($version, $info);
    //
    //     // error
    //     $version = 1 . '-test';
    //     $err = \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0121,
    //                                           ["VERSION" => $version]);
    //     $this->_testGetForVersion($version, $info, $err);
    // }
    //
    // /**
    //  *
    //  * @depends testAdd
    //  *
    //  * @param array $info
    //  */
    // public function testExecuteDifVersion(array $info)
    // {
    //     for ($i = 3; $i < 6; $i++) {
    //         $version = $i . '-test';
    //         $this->_testExecuteVersion($version, $info);
    //     }
    // }

    /**
     *
     * @depends testAdd
     *
     * @param array $info
     */
    public function testExecuteSameVersion(array $info)
    {
        print("\n=========== testExecuteSameVersion Query Start ============\n");
        $version = 5 . '-test-beta2';
        $this->_testExecuteVersion($version, $info);
    }

    /**
     */
    public function testQueryParamsAndOverrides()
    {
        print("\n=========== testQueryParams Start ============\n");
        $version = 5 . '-test-beta2';
        $operation = 'create';
        $createRequest = $this->getNewFVWithDateFilter();

        $this->log($createRequest, "=== Request ===\n");
        $createRes = $this->executeUserViewCRUDOp($createRequest, $version, $operation);
        $info = ['KEY' => $createRes['key']];
        // without asOfDate will be 0 rows
        $this->_testExecuteVersion($version, $info, [], true);
        $params = [
            "asOfDate" => '1900-01-01'
        ];
        // now should be plenty
        $res1 = $this->_testExecuteVersion($version, $info, $params, false);
        $this->assertGreaterThan(2, count($res1));
        $overides = [
            "size" => 2,
            "start" => 2
        ];
        // just 2
        $res2 = $this->_testExecuteVersion($version, $info, $params, false, $overides);
        $this->assertEquals(2, count($res2));

        // negative
        $overides = [
            "queryParameters" => [
                "asOfDate" => '1900-01-01',
            ],
            "started" => 2,
        ];
        $expectedError = \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0115, []);
        $res3 = json_encode($this->_testExecuteVersion($version, $info, [], true, $overides, $expectedError), 128);
        foreach (array_keys($overides) as $k) {
            $expectedError1 = \APIErrorMessages::buildMessage(\APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0128, ["KEY" => $k]);
            $this->assertStringContainsStringIgnoringCase($expectedError1, $res3);
        }
    }

    /**
     * @throws \APIAdapterException
     */
    public function testDelete()
    {
        $operation = 'create';
        $version = 5 . '-test-beta2';
        print("\n=========== testDelete FS ============\n");
        $request = $this->getNewUserView();
        $request['name'] .= bin2hex(random_bytes(5));
        $result = $this->executeUserViewCRUDOp($request, $version, $operation);

        $this->log($result, "=========== Result ============\n");
        $this->assertTrue(key_exists('key', $result));
        $recordno = $result['key'];
        $operation = 'delete';

        $hierarchyParams = $this->userViewSplit;
        $hierarchyParams[] = $recordno;
        $hierarchyParams[] = '';
        $hierarchyParams[] = '';
        $result = $this->executeUserViewCRUDOp([], $version, $operation, [\APIConstants::API_EXTRA_PARAM_HIERARCHY => $hierarchyParams]);
        $this->log($result, "=========== Delete Result ============\n");
        $this->assertNull($result);

        // test that delete on a non-existent key returns no errors
        $hierarchyParams = $this->userViewSplit;
        $hierarchyParams[] = 9999999999999;
        $hierarchyParams[] = '';
        $hierarchyParams[] = '';
        $result = $this->executeUserViewCRUDOp([], $version, $operation, [\APIConstants::API_EXTRA_PARAM_HIERARCHY => $hierarchyParams]);
        $this->log($result, "=========== 9999999999999 Delete Result ============\n");
        $this->assertNull($result);
    }
    /**
     * @dataProvider _testGLVersionProvider
     *
     * @param string $version
     *
     * @throws \APIAdapterException
     */
    public function testAddGLEntryFilter(string $version)
    {
        print("\n=========== testAdd Query for GLEntry $version ============\n");
        $operation = 'create';
        // get a new one
        $request = json_decode($this->glentryFilter, true);
        $request['name'] .= bin2hex(random_bytes(5));
        $result = $this->executeUserViewCRUDOp($request, $version, $operation);
        if (\Globals::$g->gErr->ErrorCount) {
            $errlist = [];
            \Globals::$g->gErr->GetErrList($errlist);
            print_r($errlist);
            $this->fail();
        }
        $this->assertNotEmpty($result['key']);
    }

    /**
     * @return string[][]
     */
    public function _testGLVersionProvider()
    {
        return [
            [5 . '-test-beta2']
        ];
    }
    /**
     * @param mixed       $obj
     * @param string|null $msg
     */
    private function log($obj, string $msg = '')
    {
        if ($this->tolog) {
            if (is_array($obj)) {
                print $msg . "\n";
                print_r($obj);
            } else {
                print $msg . ' ' . $obj . "\n";
            }
        }
    }

    /**
     * @param array $info
     * @param array $params
     *
     * @param array $overrides
     *
     * @return array[]
     */
    private function getViewExecutionPayload(array $info, array $params = [], array $overrides = []) : array
    {
        $filterRef =  [ "key" => $info['KEY'], "viewType" => "user"];
        // print_r($filterRef);
        foreach ($params as $paramkey => $paramvalue) {
            $filterRef[\APIQueryUtil::API_QUERY_FILTER_PARAMETERS][$paramkey] = $paramvalue;
        }
        foreach ($overrides as $overridekey => $overridevalue) {
            $filterRef[$overridekey] = $overridevalue;
        }

        return $filterRef;
    }

    /**
     * @param string $version
     * @param array  $info
     * @param string $expectedError
     *
     * @return array
     * @throws \APIAdapterException
     */
    private function _testGetForVersion(string $version, array $info, string $expectedError = '')
    {
        print( "=========== testGet Query Version $version ============\n" );

        $this->assertTrue(key_exists('KEY', $info));
        $result = $this->executeUserViewCRUDOp([ 'key' => $info['KEY'] ], $version, 'read', [], $expectedError);
        $this->log($result, "=========== Result ============\n");
        if (!$expectedError){
            $this->assertTrue(\APITestHelper::assertArraySubset($info['DATA'], $result));
        }
        return $result;
    }

    /**
     * @param string $version
     * @param array  $info
     *
     * @return array
     * @throws \APIAdapterException
     */
    private function _testUserViewQuery(string $version, array $info)
    {
        print( "=========== test Query of user view for Version $version ============\n" );

        $this->assertTrue(key_exists('KEY', $info));
        $query = '{
            "object": "core/user-view",
            "fields": [
                "id",
                "object",
                "query"
            ],
            "filters": [{
                "$eq" : { "key": "' . $info['KEY'] . '"}
            }]
        }';
        $api = new \APIProcessor('v' . $version . '/' . \RegistryLoader::GENERIC_SERVICE_NAME .'/core/query');
        $test = $api->invoke('POST', $query);
        $result = json_decode($test, true);
        $result = $result[\APIConstants::IA_RESULT_KEY]?? [];
        $this->log($info['DATA'], "=========== Original ============\n");
        $this->log($result[0], "=========== Result ============\n");
        $this->assertEquals($info['DATA']['query'], $result[0]['query']);
        $this->assertEquals('ap/__testvendor', $result[0]['object']);
        return $result;
    }

    /**
     * @param string      $version
     * @param array       $info
     * @param array       $params
     * @param bool        $expectedNoRows
     * @param array       $overrides
     * @param string|null $expectedError
     *
     * @return mixed
     * @throws \APIException
     * @throws \APIInternalException
     */
    private function _testExecuteVersion(string $version, array $info, array $params = [],
                                         bool $expectedNoRows = false, array $overrides = [],
                                         string $expectedError = null)
    {
        print( "\n=========== testExecuteVersion $version ============\n" );
        $filterRef = $this->getViewExecutionPayload($info, $params, $overrides);
        $filterReq = json_encode($filterRef, 128);
        $this->log($filterReq);

        [$res, $meta] = $this->_testExecuteService($version, "core/view", $filterReq, $expectedError);
        if ( $expectedNoRows && $expectedError === null ) {
            $this->assertEquals(0, $meta[\APIConstants::API_TOTAL_COUNT_KEY], json_encode($res));
        } else if ($expectedError === null) {
            $this->assertEquals(0,$meta[\APIConstants::API_TOTAL_ERROR_KEY], json_encode($res));
        } else {
            $this->assertGreaterThanOrEqual(0, $meta[\APIConstants::API_TOTAL_COUNT_KEY], json_encode($res));
        }

        return $res;
    }

    /**
     * @param array       $request
     * @param string      $version
     * @param string      $operation
     * @param array       $extraParams
     * @param string|null $expectedErr
     *
     * @return array
     * @throws \APIAdapterException
     */
    public function executeUserViewCRUDOp(array $request, string $version, string $operation, array $extraParams = [], string $expectedErr = null)
    {
        if (!isset($extraParams[ \APIConstants::API_EXTRA_PARAM_HIERARCHY ])) {
            $extraParams = [ \APIConstants::API_EXTRA_PARAM_HIERARCHY => $this->userViewSplit ];
            if (isset($request[\APIConstants::API_OBJECT_KEY_FIELD_NAME])) {
                $extraParams[ \APIConstants::API_EXTRA_PARAM_HIERARCHY][] = $request[\APIConstants::API_OBJECT_KEY_FIELD_NAME];
            }
        }
        if (!isset($extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION])) {
            $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        }
        if (!in_array($operation,[\APIConstants::API_OPERATION_CREATE, \APIConstants::API_OPERATION_PATCH])) {
            $request = [];
        } else {
            $this->log($request, "=========== Request ============\n");
        }
        try {
            $result = \APITestHelper::executeCRUDOp($request, $version, $operation, $extraParams);
        } catch(\Throwable $t) {
            $this->assertTrue($t instanceof \APIException && $t->hasAPIError());
            $result = $t->getAPIError()->getErrorResponseObject();
        }
        // print_r($result);
        if ($expectedErr) {
            $this->assertStringContainsStringIgnoringCase($expectedErr, \APIUtil::getJsonString($result));
        } else if ($result) {
            $this->assertArrayNotHasKey(\APIError::KEY_ERROR, $result, json_encode($result));
        } else {
            $this->assertEmpty($expectedErr);
        }
        return $result;
    }

    /**
     * @depends      testAdd
     * @dataProvider _testNegativeUpdateProvider
     *
     *
     * @param string $query
     * @param string $version
     * @param string $msg
     * @param array  $params
     *
     * @throws \APIAdapterException
     * @throws \APIException
     * @throws \APIInternalException
     */
    public function testUpdateNegative(string $query, string $version, string $msg, array $params) : void
    {
        print( "=========== testUpdate Negative: $msg ============\n" );
        $filter1 = $this->getAddedFilter($version);

        $filter1['query'] = json_decode($query, true);
        $this->log($filter1, "BAD FILTER: \n");
        $operation = 'patch';
        $expectedErr = ($msg)? \APIErrorMessages::buildMessage($msg, $params) : null;
        $result = $this->executeUserViewCRUDOp($filter1, $version, $operation, [], $expectedErr);
        $this->log($result);
    }

    /**
     *
     * @depends testAdd
     *
     * @param array $info
     */
    public function testPartialUpdate(array $info)
    {
        print( "=========== testPartialUpdate Query Same Version ============\n" );
        $payload['name'] = 'vendorQV2-1';
        $this->_testUpdateForVersion($info, 5 . '-test-beta2', $payload);
        $this->_testGetEM($info);

        $payload['name'] = 'vendorQV2-2';
        $this->_testUpdateForVersion($info, 5 . '-test-beta2', $payload);
        $this->_testGetEM($info);

        // bad explicit (and wrong) view version should fail
        $payload1['query'] = json_decode($this->vendorQueryWDate, true);
        $payload1[\APIQueryUtil::API_USER_VIEW_VERSION_FIELD] = 5 . '-test';
        $this->_testUpdateForVersion($info, 5 . '-test-beta2', $payload1, \APIErrorMessages::buildMessage(
            \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0029,
            [ "FIELD" => \APIQueryUtil::API_USER_VIEW_VERSION_FIELD ]
        ));
        unset($payload1[\APIQueryUtil::API_USER_VIEW_VERSION_FIELD]);

        // simulate copy-and-paste error
        $payload1[\APIConstants::IA_WARNING_KEY] = 'POST - Filterset is incompatible with version [5-test-beta2]';
        $this->_testUpdateForVersion($info, 5 . '-test-beta2', $payload1, \APIErrorMessages::buildMessage(
            \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0022, ["FIELD" => '/' . \APIConstants::IA_WARNING_KEY]
        ));

        unset($payload1[\APIConstants::IA_WARNING_KEY]);
        $this->_testUpdateForVersion($info, 5 . '-test-beta2', $payload1);

    }

    public function testPatchWithMetadata()
    {
        $p1 = '{
          "query": {
            "object": "accounts-payable/vendor",
            "fields": [
              "id",
              "name",
              "contacts.default.mailingAddress.addressLine1",
              "contacts.default.mailingAddress.city",
              "contacts.default.mailingAddress.state",
              "contacts.default.mailingAddress.postCode",
              "contacts.default.mailingAddress.country",
              "totalDue"
            ],
            "filters": [
              {
                "$eq": {
                  "status": "active"
                }
              },
              {
                "$eq": {
                  "isOneTimeUse": false
                }
              }
            ],
            "orderBy": [
              {
                "id": "asc"
              }
            ]
          },
          "metadata": {
            "columns": [
              {
                "id": "id",
                "textFormat": "clip"
              },
              {
                "id": "name",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.addressLine1",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.city",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.state",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.postCode",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.country",
                "textFormat": "clip"
              },
              {
                "id": "totalDue",
                "textFormat": "clip"
              }
            ],
            "frozenColumnCount": 0
          },
          "name": "Copy of All 1",
          "object": "accounts-payable/vendor",
          "isPublic": false
        }';
        $url = 'v0-beta2/objects/core/user-view';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_POST, $p1);

        $p2 = '{
          "query": {
            "object": "accounts-payable/vendor",
            "fields": [
              "id",
              "name",
              "contacts.default.mailingAddress.addressLine1",
              "contacts.default.mailingAddress.city",
              "contacts.default.mailingAddress.state",
              "contacts.default.mailingAddress.country",
              "totalDue"
            ],
            "filters": [
              {
                "$eq": {
                  "status": "active"
                }
              },
              {
                "$eq": {
                  "isOneTimeUse": false
                }
              }
            ],
            "orderBy": [
              {
                "id": "asc"
              }
            ]
          },
          "metadata": {
            "columns": [
              {
                "id": "id",
                "textFormat": "clip"
              },
              {
                "id": "name",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.addressLine1",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.city",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.state",
                "textFormat": "clip"
              },
              {
                "id": "contacts.default.mailingAddress.country",
                "textFormat": "clip"
              },
              {
                "id": "totalDue",
                "textFormat": "clip"
              }
            ],
            "frozenColumnCount": 0
          },
          "name": "Copy of All 1",
          "object": "accounts-payable/vendor",
          "isPublic": false
        }';

        $url .= '/' . $res[\APIConstants::IA_RESULT_KEY][\APIConstants::API_OBJECT_KEY_FIELD_NAME];
        \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_PATCH, $p2);
        $res= \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        $this->assertEquals(json_decode($p2, true)["metadata"], $res[\APIConstants::IA_RESULT_KEY]["metadata"] ?? []) ;
    }

    /**
     * @return array[]
     */
    public function _testNegativeUpdateProvider()
    {
        return [
            // cannot default the last element as it's replaced with testAdd results if not spelled out
            // [$this->badQuery, 2 . '-test', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0107, ["FIELD" => 'parent.key', "OBJECT" => 'ap/__testvendor', "VERSION" => 2 . '-test']],
            ['{}', 5 . '-test-beta2', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0101, []],
            ['', 5 . '-test-beta2', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0101, []],
            ['{"fields": ["id"]}', 5 . '-test-beta2', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0103, []],
        ];
    }

    public function testTypedToNonTypedVersionAdjust()
    {
        $viewName = "XXXXXX" . bin2hex(random_bytes(5));
        $view = '{
            "name": "' . $viewName . '",
            "query": {
                "object": "ap/__testvendor",
                "fields": [
                    "id",
                    "name",
                    "status",
                    "href"
                ],
                "orderBy": [{
                    "id": "asc"
                }]
            }
        }';
        $url = 'v5-test-beta2/objects/core/user-view';
        $res1 = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_POST, $view);


        $p = '{
                "object": "core/user-view",
                "fields": [
                    "name",
                    "object",
                    "query",
                    "viewVersion"
                ],
                "filters": [
                    {
                        "$eq": {
                            "name": "' . $viewName . '"
                        }
                    }
                ]
            }';

        $version = '5-test-beta2';
        print "fetching with $version\n";
        $url = "v$version/objects/core/user-view/" . $res1[\APIConstants::IA_RESULT_KEY]['key'];
        $res1a = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        $this->assertArrayNotHasKey(\APIConstants::IA_WARNING_KEY, $res1a[\APIConstants::IA_RESULT_KEY], json_encode($res1a));
        $this->assertEquals('ap/__testvendor', $res1a[\APIConstants::IA_RESULT_KEY]['object'], json_encode($res1a));
        $this->assertEquals('ap/__testvendor', $res1a[\APIConstants::IA_RESULT_KEY]['query']['object'], json_encode($res1a));

        $url = "v$version/services/core/query";
        $res2 = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_POST, $p);
        $this->assertArrayNotHasKey(\APIConstants::IA_WARNING_KEY, $res2[\APIConstants::IA_RESULT_KEY][0], json_encode($res2));
        $this->assertEquals('ap/__testvendor', $res2[\APIConstants::IA_RESULT_KEY][0]['object'], json_encode($res2));
        $this->assertEquals('ap/__testvendor', $res2[\APIConstants::IA_RESULT_KEY][0]['query']['object'], json_encode($res2));
        // print json_encode($res2) . "\n";

        // execute now
        print "executing with $version\n";
        $info = ['KEY' => $res1[\APIConstants::IA_RESULT_KEY]['key']];
        $this->_testExecuteVersion($version, $info);
    }


    public function testNonTypedToTypedVersionAdjust()
    {
        $viewName = "XXXXXX" . bin2hex(random_bytes(5));
        $view = '{
            "name": "' . $viewName . '",
            "query": {
                "object": "ap/__testvendor",
                "fields": [
                    "id",
                    "name",
                    "status",
                    "href"
                ],
                "orderBy": [{
                    "id": "asc"
                }]
            }
        }';
        $url = 'v5-test-beta2/objects/core/user-view';
        $res1 = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_POST, $view);

        $p = '{
                "object": "core/user-view",
                "fields": [
                    "name",
                    "object",
                    "query",
                    "viewVersion"
                ],
                "filters": [
                    {
                        "$eq": {
                            "name": "' . $viewName . '"
                        }
                    }
                ]
            }';

        $version = '5-test-beta2';
        print "fetching with $version\n";
        $url = "v$version/objects/core/user-view/" . $res1[\APIConstants::IA_RESULT_KEY]['key'];
        $res1a = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        $this->assertArrayNotHasKey(\APIConstants::IA_WARNING_KEY, $res1a[\APIConstants::IA_RESULT_KEY], json_encode($res1a));
        $this->assertEquals('ap/__testvendor', $res1a[\APIConstants::IA_RESULT_KEY]['object'], json_encode($res1a));
        $this->assertEquals('ap/__testvendor', $res1a[\APIConstants::IA_RESULT_KEY]['query']['object'], json_encode($res1a));

        $url = "v$version/services/core/query";
        $res2 = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_POST, $p);
        $this->assertArrayNotHasKey(\APIConstants::IA_WARNING_KEY, $res2[\APIConstants::IA_RESULT_KEY][0], json_encode($res2));
        $this->assertEquals('ap/__testvendor', $res2[\APIConstants::IA_RESULT_KEY][0]['object'], json_encode($res2));
        $this->assertEquals('ap/__testvendor', $res2[\APIConstants::IA_RESULT_KEY][0]['query']['object'], json_encode($res2));
        // print json_encode($res2) . "\n";

        // execute now
        print "executing with $version\n";
        $info = ['KEY' => $res1[\APIConstants::IA_RESULT_KEY]['key']];
        // inline here to use beta query
        $filterRef = $this->getViewExecutionPayload($info);
        $filterReq = json_encode($filterRef, 128);

        [$res3, $meta] = $this->_testExecuteService($version, "view", $filterReq);
        $this->assertTrue($meta[\APIConstants::API_TOTAL_COUNT_KEY] > 0, json_encode($res3));
    }

    /**
     * @param string  $version
     *
     * @return array
     * @throws \APIAdapterException
     * @throws \APIException
     */
    private function getAddedFilter(string $version) : array
    {
        $api = new \APIProcessor('v' . $version . '/' . \RegistryLoader::GENERIC_SERVICE_NAME .'/core/query');
        $filters = $api->invoke('POST', $this->queryAddedUserViewJSON);
        $this->log($filters, "======== Latest Filters=========");
        $key = json_decode($filters, true)[\APIConstants::IA_RESULT_KEY][0]['key'];

        $filter = $this->executeUserViewCRUDOp([ 'key' => $key ], $version, 'read');
        $this->log($filter, "========Filter========");
        return $filter;
    }

    /**
     * @param array  $info
     *
     * @param string $version
     *
     * @return array
     * @throws \APIAdapterException
     */
    private function getFilterForVersion(array $info, string $version) : array
    {
        $this->assertTrue(key_exists('KEY', $info));
        $operation = 'read';
        $filter = $this->executeUserViewCRUDOp([ 'key' => $info['KEY'] ], $version, $operation);
        $this->log($filter, "FILTER: \n");

        return $filter;
    }

    /**
     * @param string|int  $key
     * @param array       $filter
     * @param string      $version
     * @param string|null $expectedError
     *
     * @throws \APIAdapterException
     */
    private function updateFilter($key, array $filter, string $version, string $expectedError = null) : void
    {
        $operation = 'patch';
        $extraParams = [
            \APIConstants::API_EXTRA_PARAM_HIERARCHY => $this->userViewSplit,
        ];
        $extraParams[\APIConstants::API_EXTRA_PARAM_HIERARCHY][] = $key;
        $result = $this->executeUserViewCRUDOp($filter, $version, $operation, $extraParams, $expectedError);
        if (isset($result[\APIConstants::IA_RESULT_KEY][\APIError::KEY_ERROR])) {
            print_r($result);
            $this->fail('update falied');
        }
        $this->log($result);
        if ($expectedError) {
            $this->assertArrayHasKey(\APIError::KEY_ERROR, $result);
        } else {
            $this->assertEquals($key, $result['key']);
        }
    }

    /**
     * @param array       $info
     * @param string      $version
     * @param array       $payload
     * @param string|null $error
     *
     * @throws \APIAdapterException
     */
    private function _testUpdateForVersion(array $info, string $version, array $payload, string $error = null) : void
    {
        $this->updateFilter($info['KEY'], $payload, $version, $error);
        if (!$error) {
            $filter1 = $this->getFilterForVersion($info, $version);

            $this->log($filter1, "=========== Result ============\n");
            foreach ($payload as $key => $value) {
                $this->assertEquals($value, $filter1[$key]);
            }
        }

    }

    /**
     * @param string      $version
     * @param string      $service
     * @param string      $req
     * @param string|null $expectedError
     *
     * @return array
     * @throws \APIException
     * @throws \APIInternalException
     */
    private function _testExecuteService(string $version, string $service, string $req, string $expectedError = null)
    {
        $api = new \APIProcessor('v' . $version . '/' . \RegistryLoader::GENERIC_SERVICE_NAME .'/'. $service);
        $test = $api->invoke('POST', $req);
        $res = json_decode($test, true);
        //$this->tolog = true;
        $this->log($res);

        $meta = $res[\APIConstants::IA_META_KEY]?? [];
        if ($expectedError) {
            $this->assertEquals($meta[\APIConstants::API_TOTAL_COUNT_KEY], $meta[\APIConstants::API_TOTAL_ERROR_KEY]);
            $this->assertStringContainsStringIgnoringCase($expectedError, $test);
        } else {
            $this->assertArrayHasKey(\APIConstants::API_TOTAL_COUNT_KEY, $meta, $test);
        }
        return [$res[\APIConstants::IA_RESULT_KEY]?? [], $meta];
    }

    /**
     * @param string $entity
     * @param array  $payload
     * @param string $description
     * @param string $version
     *
     * @return bool
     * @throws \FilterViewFault
     * @throws \IAException
     */
    private function createNewFilterView(string $entity, array $payload, string $description, string $version)
    {
        $service = new \FilterViewService($entity, null, \AbstractFilterViewDialect::UI);

        $qp = \QueryParameters::array2queryParameters($payload);
        $name = $entity. '-' . $version;
        $loginId = GetMyLogin();
        // TODO: remove when FFW fixes the use
        $fvid = bin2hex(openssl_random_pseudo_bytes(5));
        $category = 'Test';
        $default = true;

        $filterView = new \FilterView($name, $fvid, $loginId, $qp, $description, $category, $default);
        // print_r($filterView->jsonSerialize());

        $recordNo = $service->create($filterView);
        return $recordNo;
    }

    /**
     * @throws \APIException
     */

    /**
     * @throws \APIException
     */
    public function testCreateUserView() : void
    {
        // test creating more than one user views in a single POST
        $requestBody = '[
            {
                "name": "user-view-1",
                "description": "Testing user-view",
                "query": {
                    "object": "core/user-view",
                    "fields": [
                        "id",
                        "name",
                        "query",
                        "category",
                        "viewVersion"
                    ],
                    "filters": [{
                        "$ne": {
                            "name": null
                        }
                    }]
                }
            },
            {
                "name": "user-view-2",
                "description": "Testing user-view",
                "query": {
                    "object": "core/user-view",
                    "fields": [
                        "id",
                        "name",
                        "query",
                        "category",
                        "viewVersion"
                    ],
                    "filters": [{
                        "$ne": {
                            "name": null
                        }
                    }]
                }
            }
        ]';

        $expectedCount = 2;
        $parameters = [
            'sessionId'     => 'xxx',
            'senderId'      => 'yyyy',
            'url'           => 'v5-test-beta2/objects/core/user-view',
            'httpMethod'    => 'POST',
            'body'          => $requestBody,
        ];
        $disp = \APIDispatcher::getDispatcher($parameters);
        $res = $disp->dispatch();
        $resAsArray = json_decode($res, true);
        $this->assertEquals(201, $res->getStatus(), $res);
        $this->assertEquals($expectedCount, $resAsArray[\APIConstants::IA_META_KEY][\APIConstants::API_TOTAL_COUNT_KEY]);

        // test for combination of success and errors when creating more than one user view in a single POST
        $requestBody = '[
            {
                "name": "user-view-1",
                "description": "Testing user-view",
                "query": {
                    "object": "core/user-view",
                    "fields": [
                        "id",
                        "name",
                        "query",
                        "category",
                        "viewVersion"
                    ],
                    "filters": [{
                        "$ne": {
                            "name": null
                        }
                    }]
                }
            },
            {
                "name": "user-view-2",
                "description": "Testing user-view",
                "query": {
                    "object": "core/user-view",
                    "fields": [
                        "id",
                        "name",
                        "query",
                        "category",
                        "xxx"
                    ],
                    "filters": [{
                        "$ne": {
                            "name": null
                        }
                    }]
                }
            }
        ]';

        $expectedCount = 2;
        $parameters = [
            'sessionId'     => 'xxx',
            'senderId'      => 'yyyy',
            'url'           => 'v5-test-beta2/objects/core/user-view',
            'httpMethod'    => 'POST',
            'body'          => $requestBody,
        ];
        $disp = \APIDispatcher::getDispatcher($parameters);
        $res = $disp->dispatch();
        $resAsArray = json_decode($res, true);
        $this->assertEquals(207, $res->getStatus(), $res);
        $this->assertEquals($expectedCount, $resAsArray[\APIConstants::IA_META_KEY][\APIConstants::API_TOTAL_COUNT_KEY]);
    }

    public function testDefaultUserView()
    {
        $url = 'v5-test-beta2/objects/core/user-view/default-data';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);
        // print (json_encode($res));
        foreach ($res as $field=>$value) {
            if (!is_array($value)) {
                $this->assertEmpty($value);
            } else if ($field === "owner") {
                $this->assertEmpty($value["key"]);
                $this->assertEmpty($value["id"]);
            }
        }
        $url = 'v5-test-beta2/objects/core/user-view/data-from-last?refKey=1&refType=x';
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $url, \APIConstants::API_HTTP_METHOD_GET, null, [], true, 400);
        // print (json_encode($res));
        // TODO: validate that message is correct after CRUD validation order is changed
    }
}
