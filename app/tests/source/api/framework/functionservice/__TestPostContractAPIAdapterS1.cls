<?php

/**
 * __TestPostContractAPIAdapterS1 is an example adapter for domain service API
 *
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

class __TestPostContractAPIAdapterS1 extends FunctionServiceAPIAdapterBase
{
    /**
     * @var ObjectAPIAdapter $nextAdapter
     */
    private $nextAdapter;

    /**
     * @inheritDoc
     */
    public function __construct(string $serviceName, string $entityName, string $serviceClassName, string $functionName,
                                string $requestVersion, string $schemaRevision)
    {
        parent::__construct($serviceName, $entityName, $serviceClassName, $functionName, $requestVersion, $schemaRevision);
        $this->nextAdapter = $this->createNextAdapter();
    }

    /**
     * @inheritDoc
     */
    function preProcess($operation, $objectName, $version,
                        $request, &$ownedRequest, $isOwned = false,
                        $caller = ObjectAPIAdapter::CALLER_ADAPTER_PRE)
    {
        foreach($ownedRequest as &$record) {
            $record['POSTMEMO'] = __METHOD__ . '::' . 'Adapter Mockup';
        }
    }

    /**
     * @inheritDoc
     */
    function getNextAdapter()
    {
        return $this->nextAdapter;
    }
}
