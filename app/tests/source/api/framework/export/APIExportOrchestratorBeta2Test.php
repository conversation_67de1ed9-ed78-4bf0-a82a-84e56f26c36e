<?php

/**
 * Test for API Export Orchestrator operations
 *
 * <AUTHOR>
 * (C)2021 Sage Intacct Corporation, All Rights Reserved
 */

/**
 * Class APIExportOrchestratorBeta2Test
 *
 */

namespace tests\source\api\framework\export;

require_once(__DIR__ . '/../APITestHelper.cls');

class APIExportOrchestratorBeta2Test extends \unitTest\core\UnitTestBaseContext
{

    /**
     * @var string $testedAPIObject
     */
    protected static $testedAPIObject = 'accounts-payable/vendor';

    /**
     * @var string $testedVersion
     */
    protected static $testedVersion = '0-beta2';

    /**
     * @var array $request
     */
    protected static $request;

    /**
     * @var string $dataSet
     */
    protected static $dataSet = '';

    const DataSet1 = [
        'fields'  => [
            0 => 'key',
            1 => 'id',
            2 => 'name',
            3 => 'paymentPriority',
            4 => 'isOnHold',
        ],
        'results' => [
            0 => [
                'key'             => '1',
                'id'              => 'VendorID1',
                'name'            => 'Vendor Name 1',
                'paymentPriority' => 'high',
                'isOnHold'        => true,
            ],
            1 => [
                'key'             => '2',
                'id'              => 'VendorID2',
                'name'            => 'Vendor Name 2',
                'paymentPriority' => 'low',
                'isOnHold'        => false,
            ],
        ],
    ];

    /**
     *
     */
    public static function setUpBeforeClass() : void
    {
        self::$dataSet = self::DataSet1;
    }

    /**
     *
     */
    public function testNothing()
    {
        $this->assertTrue(true);
    }

    /**
     *
     */
    public function testValidatorFiletype()
    {
        $registry = \RegistryLoader::getInstance(self::$testedVersion);
        $handler = $registry->getSchemaHandler(self::$testedAPIObject);
        $orchestrator = new \ExportServiceOrchestrator($handler);

        self::$request = [
            'query'    => [
                'object' => self::$testedAPIObject,
                'fields' => self::$dataSet['fields'],
                'size'   => 100,
                'start'  => 1,
            ],
            "fileType" => "jpg",
        ];

        $this->expectException(\APIException::class);
        $orchestrator->execute(\APIConstants::API_OPERATION_READ, self::$request);
    }

    /**
     *
     */
    public function testValidatorFiletypeError()
    {
        $registry = \RegistryLoader::getInstance(self::$testedVersion);
        $handler = $registry->getSchemaHandler(self::$testedAPIObject);
        $orchestrator = new \ExportServiceOrchestrator($handler);

        self::$request = [
            'query'    => [
                'object' => self::$testedAPIObject,
                'fields' => self::$dataSet['fields'],
                'size'   => 100,
                'start'  => 1,
            ],
            "fileType" => "jpg",
        ];

        try {
            $orchestrator->execute(\APIConstants::API_OPERATION_READ, self::$request);
        } catch (\APIException $e) {
            $this->assertNotEmpty($e->getAPIError());
            // $error = $e->getAPIError();
            // TODO: change this back when the I18n pack is ready under 'locales' folder: IA.INVALID_EXPORT_TYPE_IN_PAYLOAD
            // $this->assertEquals("Invalid export type jpg in payload", $error->getMessage());
        }
    }
}
