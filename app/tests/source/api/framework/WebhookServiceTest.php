<?php
namespace tests\source\api\framework;

require_once 'APITestHelper.cls';

/**
 * Class WebhookServiceTest
 *
 * @package tests\source\api\framework
 */
class WebhookServiceTest extends \unitTest\core\UnitTestBaseContext
{

    /**
     * @var string[] $payload
     */
    var $payload = [
        "POSTINGDATE"=>"10/10/2020",
        "STATE"=>"NEW"
    ];

    /**
     * @var string[] $params
     */
    var $params = [\APIConstants::API_OUTBOUND_WEBHOOK_TYPE =>
        [0 => [
                "version" => "5-test",
                "headers" => ["Content-Type:application/json"]
              ]
        ]
    ];

    /**
     * only applies to manual tests - see \APITestHelper::isInSuite checks
     * @var string $sandBox
     */
    var $sandBox = "users/marina.vatkina/apilayer1";

    /**
     *
     */
    public function testNothing()
    {
        $this->assertTrue(true);
    }

    /**
     * validate that error response schema portion is accessible
     */
    public function testWebhookSchema()
    {
        $webhookName = "__testwebhook2";
        $version = 5 . "-test";
        try {
            $handler = \RegistryLoader::getHandlerForVersion($webhookName, $version);
        } catch (\APIException $e) {
            $this->fail($e->getAPIError()->getMessage());
        }
        $errHandler = $handler->getErrorResponseHandler();
        $this->assertNotNull($errHandler);
        $errSchema = $errHandler->getSchemaDefinition();
        // print_r($errSchema);
        $this->assertNotEmpty($errSchema);
    }

    public function testSimpleWebhook()
    {
        if ( \APITestHelper::isInSuite($this->getCurrentClass() . '::' . __FUNCTION__) ) {
            // skip
            print("============skip ". __FUNCTION__ . "============\n");
            $this->assertTrue(true);
            return;
        }

        $url = "https://dev01.intacct.com/{$this->sandBox}/acct/__testwebhook.phtml";
        print "$url\n";
        $whs = \WebhookService::getInstance();
        $this->params[\APIConstants::API_OUTBOUND_WEBHOOK_TYPE][0]['url'] = $url;
        $result = $whs->invoke("__testwebhook", $this->payload, $this->params);
        // print_r($result);
        $expecteResult = [
            'STATE' => 'acc',
            'UPDATEDFIELDS' => [
                'fieldOne' => 1,
                'fieldText' => 'my text',
                'fieldDate' => '2020-10-10'
            ]
        ];
        $resultAsString = json_encode($result);
        $this->assertArrayHasKey(\APIConstants::IA_RESULT_KEY, $result, $resultAsString);
        $this->assertArrayHasKey(\APIConstants::IA_META_KEY, $result, $resultAsString);
        $this->assertArrayNotHasKey(\APIConstants::API_TOTAL_ERROR_KEY, $result[\APIConstants::IA_META_KEY], $resultAsString);
        $this->assertEquals($expecteResult, $result[\APIConstants::IA_RESULT_KEY][0]);

        $url = "https://dev01.intacct.com/{$this->sandBox}/acct/__testwebhook2.phtml";
        print "$url\n";
        $this->params[\APIConstants::API_OUTBOUND_WEBHOOK_TYPE][0]['url'] = $url;
        $result = $whs->invoke("__testwebhook2", $this->payload, $this->params);
        $expecteResult["META"] = [
            "totaCount"  => 1,
            "totalError" => 0,
        ];
        $resultAsString = json_encode($result);
        // print "$resultAsString\n";
        $this->assertArrayHasKey(\APIConstants::IA_RESULT_KEY, $result, $resultAsString);
        $this->assertArrayHasKey(\APIConstants::IA_META_KEY, $result, $resultAsString);
        $this->assertArrayNotHasKey(\APIConstants::API_TOTAL_ERROR_KEY, $result[\APIConstants::IA_META_KEY], $resultAsString);
        $this->assertEquals($expecteResult, $result[\APIConstants::IA_RESULT_KEY][0]);
    }

    /**
     * @dataProvider __testWebhookErrorResponse
     */
    public function testWebhookErrorResponse(string $webhookScriptName, string $webhookName, int $errorCode)
    {
        if ( \APITestHelper::isInSuite($this->getCurrentClass() . '::' . __FUNCTION__) ) {
            // skip
            print("============skip ". __FUNCTION__ . "============\n");
            $this->assertTrue(true);
            return;
        }

        $url = "https://dev01.intacct.com/{$this->sandBox}/acct/$webhookScriptName";
        print "$url\n";

        $payload = [
            "POSTINGDATE"=>"10/10/1999",
            "STATE"=>"NEW"
        ];
        $whs = \WebhookService::getInstance();
        $this->params[\APIConstants::API_OUTBOUND_WEBHOOK_TYPE][0]['url'] = $url;
        $result = $whs->invoke($webhookName, $payload, $this->params);
        $resultAsString = json_encode($result);
        print "$resultAsString\n";
        $this->assertArrayHasKey(\APIConstants::IA_RESULT_KEY, $result, $resultAsString);
        $this->assertArrayHasKey(\APIConstants::IA_META_KEY, $result, $resultAsString);
        $this->assertArrayHasKey(\APIConstants::API_TOTAL_ERROR_KEY, $result[\APIConstants::IA_META_KEY], $resultAsString);
        $this->assertEquals(1, $result[\APIConstants::IA_META_KEY][\APIConstants::API_TOTAL_ERROR_KEY], $resultAsString);
        $this->assertEquals($errorCode, $result[\APIConstants::IA_RESULT_KEY][0][\APIError::KEY_ERROR][\APIError::KEY_STATUS]?? null, $resultAsString );
        $this->assertArrayHasKey(\APIError::KEY_SUPPORTID, $result[\APIConstants::IA_RESULT_KEY][0][\APIError::KEY_ERROR]?? null, $resultAsString);

    }

    public function __testWebhookErrorResponse() : array
    {
        return [
          ["__testwebhook.phtml", "__testwebhook", 500],
          ["__testwebhook2.phtml", "__testwebhook2", 400]
        ];
    }

    /**
     * @dataProvider __testErrorProvider
     *
     * @param string      $url
     * @param string|null $version
     * @param string      $webhookName
     */
    public function testErrors(string $url, string $version = null, string $webhookName = "__testwebhook")
    {
        print("============testError url: $url ============\n");
        $whs = \WebhookService::getInstance();

        $this->params[\APIConstants::API_OUTBOUND_WEBHOOK_TYPE][0][\WebhookService::OWH_URL_PARAM] = $url;
        if ($version !== null) {
            $this->params[\APIConstants::API_OUTBOUND_WEBHOOK_TYPE][0][\APIConstants::API_VERSION_KEY] = $version;
        }
        $result = $whs->invoke($webhookName, $this->payload, $this->params);
        $resultAsString = json_encode($result);
        print "$resultAsString\n";
        $this->assertArrayHasKey(\APIError::KEY_ERROR, $result[\APIConstants::IA_RESULT_KEY][0]?? null, $resultAsString);
        $this->assertNotEmpty($result[\APIConstants::IA_RESULT_KEY][0][\APIError::KEY_ERROR][\APIError::KEY_STATUS]?? null, $resultAsString);
        $this->assertNotEmpty($result[\APIConstants::IA_RESULT_KEY][0][\APIError::KEY_ERROR][\APIError::KEY_SUPPORTID]?? null, $resultAsString);
        $this->assertGreaterThanOrEqual(400,
                                        $result[\APIConstants::IA_RESULT_KEY][0][\APIError::KEY_ERROR][\APIError::KEY_STATUS]?? null, $resultAsString );
    }

    public function __testErrorProvider(): array
    {
        return [
            // do not use 'https://intacct.com/'
            ['https://zzz.intacct.com/users'],
            ['https://'],
            ['https://intacct.com/users', '99-test'], // non-existent version
            ['https://intacct.com/users', '5-test', 'abc'], // non-existent webhook
        ];
    }

    /**
     * @return string
     */
    private function getCurrentClass(): string
    {
        $parts = explode('\\', get_class());
        return end($parts);
    }
}
