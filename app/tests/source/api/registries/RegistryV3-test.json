{"objects": {"__testclass": {"revision": "s3", "hash": "0", "type": "rootObject"}, "__testcontact": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testcustomer": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testemployee": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testglbatch": {"revision": "s2", "hash": "0", "type": "rootObject"}, "__testglentry": {"revision": "s1", "hash": "0", "type": "ownedObject"}, "__testproject": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testvendor": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testvendorrestrictedlocation": {"revision": "s1", "hash": "0", "type": "ownedObject"}, "__testsimplewarehouse": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testlocation": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testterm": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testtimesheet": {"revision": "s1", "hash": "0", "type": "rootObject"}, "__testtimesheetentry": {"revision": "s1", "hash": "0", "type": "ownedObject"}, "__testtest-old": {"revision": "s1", "hash": "0", "type": "rootObject", "limitedAccessGroup": "group1"}, "__testdepartment": {"revision": "s1", "hash": "0", "type": "rootObject", "limitedAccessGroup": "group1"}, "user-view": {"revision": "s1", "hash": "0", "type": "rootObject"}, "user": {"revision": "s1", "hash": "0", "type": "rootObject"}}, "services": {"view": {"revision": "ALL", "hash": null, "type": "coreService"}, "query": {"revision": "ALL", "hash": null, "type": "coreService"}, "__testmodel": {"revision": "s1", "hash": null, "type": "coreService"}, "__testpreferences": {"__testap": {"revision": "s1", "hash": "0", "type": "preference", "limitedAccessGroup": "group2"}, "__testgen": {"revision": "s1", "hash": "0", "type": "preference", "limitedAccessGroup": "group2"}}, "core": {"async": {"job-status": {"revision": "s1", "hash": "0", "type": "functionService"}}}}}