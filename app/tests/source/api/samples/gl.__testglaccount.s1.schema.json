{"mappedTo": "glaccount", "idempotenceSupported": true, "fields": {"key": {"mappedTo": "RECORDNO", "type": "string", "readOnly": true}, "id": {"mappedTo": "ACCOUNTNO", "mutable": false, "type": "string"}, "name": {"mappedTo": "TITLE", "type": "string"}, "type": {"mappedTo": "ACCOUNTTYPE", "mappedToValues": ["balancesheet", "incomestatement"], "enum": ["balanceSheet", "incomeStatement"], "type": "string"}, "normalBalance": {"mappedTo": "NORMALBALANCE", "enum": ["debit", "credit"], "type": "string"}, "closingType": {"mappedTo": "CLOSINGTYPE", "mappedToValues": ["non-closing account", "closing account", "closed to account"], "enum": ["nonClosingAccount", "closingAccount", "closedToAccount"], "type": "string"}, "alternativeAccount": {"mappedTo": "ALTERNATIVEACCOUNT", "mappedToValues": ["None", "Payables account", "Receivables account"], "enum": ["none", "payablesAccount", "receivablesAccount"], "type": "string"}, "status": {"mappedTo": "STATUS", "mappedToValues": ["active", "inactive"], "enum": ["active", "inactive"], "type": "string", "default": "active"}, "isTaxable": {"mappedTo": "TAXABLE", "type": "boolean"}, "category": {"mappedTo": "CATEGORY", "type": "string"}, "taxCode": {"mappedTo": "TAXCODE", "type": "string"}, "mrcCode": {"mappedTo": "MRCCODE", "type": "string"}, "isSubledgerControlOn": {"mappedTo": "SUBLEDGERCONTROLON", "type": "boolean"}}, "groups": {"wrongRequireDimensions": {"fields": {"department": {"mappedTo": "REQUIREDEPT", "type": "boolean"}, "location": {"mappedTo": "REQUIRELOC", "type": "boolean"}, "project": {"mappedTo": "REQUIREPROJECT", "type": "boolean"}, "customer": {"mappedTo": "REQUIRECUSTOMER", "type": "boolean"}, "vendor": {"mappedTo": "REQUIREVENDOR", "type": "boolean"}, "employee": {"mappedTo": "REQUIREEMPLOYEE", "type": "boolean"}, "item": {"mappedTo": "REQUIREITEM", "type": "boolean"}, "class": {"mappedTo": "REQUIRECLASS", "type": "boolean"}}}, "audit": {"fields": {"createdDateTime": {"mappedTo": "WHENCREATED", "type": "string", "format": "date-time", "readOnly": true}, "modifiedDateTime": {"mappedTo": "WHENMODIFIED", "type": "string", "format": "date-time", "readOnly": true}, "createdBy": {"mappedTo": "CREATEDBY", "type": "integer", "readOnly": true}, "modifiedBy": {"mappedTo": "MODIFIEDBY", "type": "integer", "readOnly": true}}}}, "httpMethods": "OPTIONS,GET,DELETE,PATCH,POST"}