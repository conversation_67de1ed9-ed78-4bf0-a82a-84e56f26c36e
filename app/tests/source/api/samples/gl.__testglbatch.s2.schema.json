{"mappedTo": "glbatch", "fields": {"key": {"mappedTo": "RECORDNO", "type": "string", "description": "The key", "readOnly": true}, "id": {"mappedTo": "RECORDNO", "type": "string", "description": "The id", "readOnly": true}, "batchNo": {"mappedTo": "BATCHNO", "type": "integer", "description": "The transaction number"}, "batchTitle": {"mappedTo": "BATCH_TITLE", "type": "string", "description": "The description"}, "balance": {"mappedTo": "BALANCE", "type": "number", "description": "The balance"}, "batchDate": {"mappedTo": "BATCH_DATE", "type": "string", "format": "date", "description": "The posting date"}, "state": {"mappedTo": "STATE", "type": "string", "description": "The state"}, "journal": {"mappedTo": "JOURNAL", "type": "string", "description": "Journal"}, "locationKey": {"mappedTo": "LOCATIONKEY", "type": "string", "description": "location key"}, "journalKey": {"mappedTo": "JOURNALKEY", "type": "string", "description": "Journal id"}}, "groups": {"audit": {"fields": {"whenCreated": {"mappedTo": "WHENCREATED", "type": "string", "format": "date-time", "description": "Time of the submission", "readOnly": true}, "whenModified": {"mappedTo": "WHENMODIFIED", "type": "string", "format": "date-time", "description": "Time of the modification", "readOnly": true}, "createdBy": {"mappedTo": "CREATEDBY", "type": "string", "description": "User who created this", "readOnly": true}, "modifiedBy": {"mappedTo": "MODIFIEDBY", "type": "string", "description": "User who modified this", "readOnly": true}}}, "vat": {"fields": {"taxImplications": {"mappedTo": "TAXIMPLICATIONS", "type": "string", "enum": [null, "None", "Inbound", "Outbound"], "description": "Tax Implications"}}, "refs": {"contact": {"apiObject": "__testcontact", "fields": {"key": {"mappedTo": "VATCONTACTKEY", "type": "string"}, "id": {"mappedTo": "VATCONTACTID", "type": "string"}}}, "vendor": {"apiObject": "__testvendor", "fields": {"key": {"mappedTo": "VATVENDORKEY", "type": "string"}, "id": {"mappedTo": "VATVENDORID", "type": "string"}}}, "customer": {"apiObject": "__testcustomer", "fields": {"key": {"mappedTo": "VATCUSTOMERKEY", "type": "string"}, "id": {"mappedTo": "VATCUSTOMERID", "type": "string"}}}}}}, "lists": {"items": {"apiObject": "__testglentry", "mappedTo": "ENTRIES"}}, "httpMethods": "GET,POST,PATCH,DELETE,OPTIONS"}