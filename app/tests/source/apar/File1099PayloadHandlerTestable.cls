<?php

namespace tests\source\apar;

class File1099PayloadHandlerTestable extends File1099PayloadHandler
{
    public function __construct($formType, $year)
    {
        $this->formType = $formType;
        $this->year = $year;
    }

    public function getPartnerIntacctBoxMap($formType, $year)
    {
        $handler = new File1099PayloadHandlerTest();
        return $handler->getMockPartnerBoxMap();
    }

    public function buildBussinessInfo($data)
    {
        return parent::buildBussinessInfo($data);
    }

    public function buildTransactionInfo($data)
    {
        return parent::buildTransactionInfo($data);
    }
}
