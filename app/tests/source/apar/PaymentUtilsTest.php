<?php

namespace tests\source\apar;

use Globals;
use PaymentUtils;
use unitTest\core\UnitTestBase;

/**
 * @group unit
 */
class PaymentUtilsTest extends UnitTestBase
{


    public function test_recordTypeToEntry() : void
    {
        $gManagerFactory = Globals::$g->gManagerFactory;

        foreach (PaymentUtils::$recordTypeToEntry as $entry) {

            $manager = $gManagerFactory->getManager($entry);

            // BasePRRecordManager::getEntryManager requires all PaymentUtils::$recordTypeToEntry to be an OwnedObjectManager
            self::assertInstanceOf('OwnedObjectManager', $manager, "$entry must be an OwnedObjectManager");

        }
    }
}
