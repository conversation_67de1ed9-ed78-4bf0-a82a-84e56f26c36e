<?php
namespace tests\source\los;

/**
 * TenantThrottleTest.php
 *
 * <AUTHOR>
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
require_once 'RateLimitProfileTest.php';
require_once (__DIR__.'/../api/framework/APITestHelper.cls');
class TenantThrottleTest extends \unitTest\core\UnitTestBaseContext
{

    const ENT = 'tenantthrottle';
    const THROTTLE_LIFT = 5;

    /**
     * This method is called before each test.
     */
    protected function setUp() : void
    {
        \Globals::$g->gErr->Clear();
        parent::setUp();
    }

    /**
     *
     */
    public function testNothing()
    {
        $this->assertTrue(true);
    }

    /**
     * Testing that missing/null/0 THROTTLE_LIMIT are not allowed
     */
    public function testEmptyThrottle()
    {
        // find cny# which is not used
        $podId = \Globals::$g->gPODManager->getCurrentPOD()->getId();
        $sql = "SELECT s.CNY# as CNY from schemamap s where podid = :1 and not exists (select t.CNY# from TENANTTHROTTLE t where t.CNY# = s.CNY#)";
        $result = QueryResult([$sql, $podId], 0, null, GetGlobalConnection());
        if ($result === false || empty($result)) {
            $this->fail("Cannot get tenant list");
        }

        $cny = $result[0]['CNY'];
        print "using tenant $cny to test missing/empty throttle\n";
        $mgr = \Globals::$g->gManagerFactory->getManager(self::ENT);
        $rec = [
            'CNY#' => $cny,
            'NOTES' => 'abc'
        ];
        $ok = $mgr->add($rec);
        $this->assertFalse($ok, json_encode($rec));

        $rec['THROTTLE_LIMIT'] = null;
        $ok = $mgr->add($rec);
        $this->assertFalse($ok, json_encode($rec));

        $rec['THROTTLE_LIMIT'] = 0;
        $ok = $mgr->add($rec);
        $this->assertFalse($ok, json_encode($rec));

        $rec['THROTTLE_LIMIT'] = '0';
        $ok = $mgr->add($rec);
        $this->assertFalse($ok, json_encode($rec));

        // now add to test bad set
        $rec['THROTTLE_LIMIT'] = 10;
        $ok = $mgr->add($rec);
        $this->assertTrue($ok, json_encode($rec));

        $rec = $mgr->get($cny);
        unset($rec['THROTTLE_LIMIT']);
        $ok = $mgr->set($rec);
        $this->assertFalse($ok, json_encode($rec));

        $rec['THROTTLE_LIMIT'] = null;
        $ok = $mgr->set($rec);
        $this->assertFalse($ok, json_encode($rec));

        $rec['THROTTLE_LIMIT'] = 0;
        $ok = $mgr->set($rec);
        $this->assertFalse($ok, json_encode($rec));

        $rec['THROTTLE_LIMIT'] = '0';
        $ok = $mgr->set($rec);
        $this->assertFalse($ok, json_encode($rec));
    }

    /**
     * @return void
     */
    public function testTenantLiftNoOverride() : void
    {
        // find cny# to reference
        $podId = \Globals::$g->gPODManager->getCurrentPOD()->getId();
        $sql = "SELECT s.CNY# as CNY from schemamap s where podid = :1 and not exists (select t.CNY# from TENANTTHROTTLE t where t.CNY# = s.CNY#)";
        $result = QueryResult([$sql, $podId], 0, null, GetGlobalConnection());
        if ($result === false) {
            $this->fail("Cannot get tenant list");
        }

        $cny = $result[0]['CNY'];

        $mgr = \Globals::$g->gManagerFactory->getManager(self::ENT);
        $rec = $mgr->get($cny);
        if ($rec === false) {
            $rec = [
                'CNY#' => $cny,
                'THROTTLE_LIMIT' => 20,
                'NOTES' => 'abc'
            ];
            $ok = $mgr->add($rec);
            RateLimitProfileTest::validateErrlist('failed ' . __METHOD__);
            $this->assertTrue($ok);
        }
        $now = time();
        $end = date("m/d/Y", $now + 10000);
        $rec['THROTTLE_LIMIT'] = null;
        $rec["THROTTLE_LIMIT_OVERRIDE"] = 125;
        $rec["END_DATE"] = $end;
        $ok = $mgr->set($rec);
        $this->assertTrue($ok, 'failed to update throttle override');

        $throttles = $mgr->getCurrentThrottles();
        $this->assertArrayHasKey($cny, $throttles, json_encode($throttles));
        $this->assertEquals(125, $throttles[$cny][\BaseThrottleManager::THROTTLE_LIMIT_FETCHED], json_encode($throttles));

        $rateLimitHelper = new \RateLimitHelper();
        $cnyInfo = $rateLimitHelper->getTenantInfo($cny);
        $currentThrottleLimits = $rateLimitHelper->getThrottlesByType($cny, \RateLimitHelper::TENANT, $cnyInfo);
        $this->assertEquals(125, $currentThrottleLimits[\BaseThrottleManager::EFFECTIVE_THROTTLE], json_encode($currentThrottleLimits));
        $this->assertEquals(\RateLimitHelper::LIFT, $currentThrottleLimits[\RateLimitHelper::THROTTLE_TYPE], json_encode($currentThrottleLimits));

        // set to expire
        $end = date("m/d/Y", strtotime("-1 month"));
        $rec["END_DATE"] = $end;
        $ok = $mgr->set($rec);
        $this->assertTrue($ok, 'failed to update throttle override');

        $throttles = $mgr->getCurrentThrottles();
        $this->assertArrayNotHasKey($cny, $throttles, json_encode($throttles));

        $currentThrottleLimits = $rateLimitHelper->getThrottlesByType($cny, \RateLimitHelper::TENANT, $cnyInfo);
        $this->assertNotEmpty($currentThrottleLimits[\BaseThrottleManager::EFFECTIVE_THROTTLE], json_encode($currentThrottleLimits));
        $this->assertNotEquals(125, $currentThrottleLimits[\BaseThrottleManager::EFFECTIVE_THROTTLE], json_encode($currentThrottleLimits));
        // do not check what is set to - can be Tier/Type/Default
        $this->assertNotEquals(\RateLimitHelper::LIFT, $currentThrottleLimits[\RateLimitHelper::THROTTLE_TYPE], json_encode($currentThrottleLimits));
    }

    /**
     * @return array
     */
    public function testTenantThrottleOverride() : array
    {
        // find cny# to reference
        $podId = \Globals::$g->gPODManager->getCurrentPOD()->getId();
        $sql = "SELECT s.CNY# as CNY from schemamap s where podid = :1 and not exists (select t.CNY# from TENANTTHROTTLE t where t.CNY# = s.CNY#)";
        $result = QueryResult([$sql, $podId], 0, null, GetGlobalConnection());
        if ($result === false) {
            $this->fail("Cannot get tenant list");
        }

        $cny = $result[0]['CNY'];

        $mgr = \Globals::$g->gManagerFactory->getManager(self::ENT);
        $rec = $mgr->get($cny);
        if ($rec === false) {
            $rec = [
                'CNY#' => $cny,
                'THROTTLE_LIMIT' => 20,
                'NOTES' => 'abc'
            ];
            $ok = $mgr->add($rec);
            RateLimitProfileTest::validateErrlist('failed ' . __METHOD__);
            $this->assertTrue($ok);
        }
        $rec["THROTTLE_LIMIT_OVERRIDE"] = 123;
        unset($rec["END_DATE"]); // unset if exists
        // missing date
        $ok = $mgr->set($rec);
        $this->assertFalse($ok, "not reported missing end date");

        $now = time();
        $end = date("m/d/Y", $now + 10000);
        $rec["END_DATE"] = $end;
        $ok = $mgr->set($rec);
        $this->assertTrue($ok, 'failed to add throttle override');
        // refetch
        $rec = $mgr->get($cny);
        $this->assertNotEmpty($rec['THROTTLE_LIMIT_OVERRIDE']);
        $this->assertEquals(123, $rec['THROTTLE_LIMIT_OVERRIDE']);

        // undo lift and add notes
        unset($rec['THROTTLE_LIMIT_OVERRIDE']);
        $ok = $mgr->set($rec);
        $this->assertFalse($ok, "not reported non-empty end date");

        unset($rec['END_DATE']);
        $ok = $mgr->set($rec);
        $this->assertTrue($ok, 'failed to remove throttle override');

        // refetch
        $rec = $mgr->get($cny);
        $rec['NOTES'] = 'abc-xyz';
        $ok = $mgr->set($rec);
        $this->assertTrue($ok, 'failed to remove throttle override');

        // refetch
        $rec = $mgr->get($cny);
        $this->assertNotEmpty($rec['NOTES']);
        $this->assertEquals('abc-xyz', $rec['NOTES']);

        return $rec;
    }

    /**
     * @depends testTenantThrottleOverride
     * @param array $rec
     *
     * @return void
     * @throws \APIException
     */
    public function testBasicAPI(array $rec)
    {
        // fetch as a REST API
        $url = 'v0-beta2/objects/core/tenant-throttle';
        $key = $rec["RECORDNO"];
        \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET, null, [], false);
        $url .= "/$key";
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET)[\APIConstants::IA_RESULT_KEY];
        // print json_encode($res, 128);
        $limit = $res["limit"] + 1;
        $payload = json_encode([
            "limit" => $limit,
            "notes" => "new notes"
        ]);
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_PATCH, $payload)[\APIConstants::IA_RESULT_KEY];
        $this->assertNotNull(($res[\APIConstants::API_OBJECT_KEY_FIELD_NAME] ?? null), json_encode($res));
        $this->assertNotNull(($res[\APIConstants::API_HREF_FIELD_NAME] ?? null), json_encode($res));
        $res = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET)[\APIConstants::IA_RESULT_KEY];
        $this->assertEquals($limit, $res["limit"],json_encode($res));
    }

    public function testDefaultThrottle()
    {
        $cny = '0';
        $mgr = \Globals::$g->gManagerFactory->getManager(self::ENT);
        $rec = $mgr->get($cny);

        \Globals::$g->gErr->Clear();
        print_r ($rec);
        if ($rec === false) {
            print "adding for default tenant as $cny\n";
            $rec = [
                'CNY#' => $cny,
                'THROTTLE_LIMIT' => 5555
            ];
            $ok = $mgr->add($rec);
        } else {
            print "updating throttle for default tenant as $cny\n";
            $rec['THROTTLE_LIMIT'] = 5555;

            $ok = $mgr->set($rec);
        }

        RateLimitProfileTest::validateErrlist('failed ' . __METHOD__);
        $this->assertTrue($ok);
    }
}
