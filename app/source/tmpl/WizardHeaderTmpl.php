<?php

/*
 * -License	LGPL (http://www.gnu.org/copyleft/lesser.html)
 * -Copyright	2001, Entity
 * -Author	author name, <EMAIL>
 */




/**
 * WizardHeaderTmpl Template Class.
 *
 * Generated by PHPMake Template
 *
 * @access public
 */

class WizardHeaderTmpl
{
    /**
     * Wizard specific tokens
     * @var string[]
     */
     static $tokens = array(
        array("token" => "IA.SETUP_WIZARD", "placeholder" => array()),
        array("token" => "IA.LOGIN", "placeholder" => array()),
        array("token" => "IA.TEMPLATE", "placeholder" => array()),
        array("token" => "IA.CONTACT", "placeholder" => array()),
        array("token" => "IA.COMPANY", "placeholder" => array()),
        array("token" => "IA.PAYMENT", "placeholder" => array()),
        array("token" => "IA.SUBSCRIBE", "placeholder" => array()),
        array("token" => "IA.NEXT", "placeholder" => array()),
        array("token" => "IA.DONE", "placeholder" => array()),
        array("token" => "IA.PREVIOUS", "placeholder" => array()),
        array("token" => "IA.ORDER", "placeholder" => array()),
        array("token" => "IA.ADD", "placeholder" => array()),
    );

    /**
     * This method accepts data and parameters by reference.
     * Those params and data are used to generate output.
     *
     * @param array $params
     * @param array $data
     *
     * @throws I18NException
     */
    function __construct(&$params, &$data)
    {
        global $kSetupPages;
        global $wizParams;
        $_pg = $params['page_number'];

        // tokens
        foreach (self::$tokens as $token) {
            I18N::addToken($token["token"], $token["placeholder"]);
        }
        I18N::getText();

        // Spit the .hlp hidden variable for the wizard screen
        // so that the top menu bar help link can identify the help screen number
    ?>
   <style>
       .old_wizard_title {
           font-family: Arial, Helvetica, sans-serif;
           font-size: 28px;
           color: #000000;
       }
       A.Task:link,
       A.Task:visited,
       A.Task:active,
       A.Task:hover {
           background-color: #587da5;
           border: 1px solid #4180c0;
           color: #fbfcfe;
           font-family: Arial, Helvetica, Geneva, sans-serif;
           font-weight: bold;
           text-decoration: none;
           background: -webkit-gradient(linear, left top, left bottom, from(#84a0bc), to(#587da5));
           font-size: 12px;
           padding: 2px 5px 2px 5px;
           margin: 2px 2px;
           display: inline-block;
       }
       A.Task:hover {
           color: #007E45;
           background: #d7e9b8;
       }
   </style>
   <input type=hidden name="hlp" value="<?= GetHelpFile(); ?>">
<?
    switch($params['wizard_name']) {

        case 'setup_wizard_acct':
            $topparams = $wizParams['setup_wizard_acct'];
            /** @noinspection PhpUnusedLocalVariableInspection */
            $cpawiz = 1;
            // fall through

        case 'setup_wizard_cpa':
            /** @noinspection PhpUndefinedVariableInspection */
            if (!$topparams) {
                $topparams = $wizParams['setup_wizard_cpa'];
            }
            $cpawiz = 1;
            // fall through

        case 'setup_wizard':                            //Setup Wizard
            /** @noinspection PhpUndefinedVariableInspection */
            if (!$topparams) {
                $topparams = $wizParams['setup_wizard'];
            }

        // ovi - seems unused anymore
        case 'tmpl_wizard':                            //Setup Wizard
            /** @noinspection PhpUndefinedVariableInspection */
            if (!$topparams) {
                $topparams = $wizParams['tmpl_wizard'];
            }

        case 'clientcomp_wizard':                            //Setup Wizard
            /** @noinspection PhpUndefinedVariableInspection */
            if (!$topparams) {
                $topparams = $wizParams['clientcomp_wizard'];
            }
            $page = $kSetupPages[$_pg];
    ?>
       <!-- start -->

       <table border="0" cellpadding="0" cellspacing="0" width="800"  class="wizTable">
            <tr>
               <td>
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td class="wizHdr">
                                <span class="old_wizard_title"><?= I18N::getSingleToken("IA.SETUP_WIZARD"); ?></span>
                            </td>
                            <td align="right" class="wizHdr">
                            <? if ($topparams['help_bg']) { ?>
                                <img src="<?= IALayoutManager::getCSSButtonPath("setup_help.gif"); ?>" alt=""  border="0" usemap="#help_map">
                            <? } ?>
                            <!-- start -->
                            <? if ($page['hasprev'] == '1') { ?>
                                <a class="Task" href="javascript:Jump('<?=$_pg - 1;?>');" onmouseover="window.status='Back'; return true;" onfocus="window.status='Back'; return true;" onblur="window.status=''" onmouseout="window.status=''">
                                    <?=I18N::getSingleToken("IA.PREVIOUS");?>
                                </a>
                            <? } ?>
                            <? if ($page['hasnext'] == '1') { ?>
                                <a class="Task" href="javascript:Jump('<?= $_pg + 1; ?>');" onmouseover="window.status='Next'; return true;" onfocus="window.status='Next'; return true;" onblur="window.status=''" onmouseout="window.status=''">
                                    <?=I18N::getSingleToken("IA.NEXT");?>
                                </a>
                            <? } ?>
                            <? if ($page['hasdone'] == '1') { ?>
                                <a class="Task" href="javascript:DoSubmit('<?= $_pg + 1; ?>')" onmouseover="window.status='Done'; return true;" onfocus="window.status='Done'; return true;" onblur="window.status=''" onmouseout="window.status=''">
                                    <?=I18N::getSingleToken("IA.DONE");?>
                                </a>
                            <? } ?>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
	<!-- new row -->
</table>
<table border="0" cellpadding="0" cellspacing="0" width="800"  class1="wizTable" style="margin-top:0px;border1:1px solid #b2b2b2;">
	<tr><td align1="middle" valign="middle">

<?

// set up the tabs
$tabClass1 = $tabClass2 = $tabClass3 = $tabClass4 = $tabClass5 = $tabClass6 = $tabClass7 = "layoutTabOff";

if ($_pg == '7') {
    $tabClass7="layoutTabOn";
}
elseif ($_pg == '6') {
    $tabClass6="layoutTabOn";
}
elseif ($_pg == '5') {
    $tabClass5="layoutTabOn";
}
elseif ($_pg == '4') {
    $tabClass4="layoutTabOn";
}
elseif ($_pg == '3') {
    $tabClass3="layoutTabOn";
}
elseif ($_pg == '2') {
    $tabClass2="layoutTabOn";
}
elseif ($_pg == '1') {
    $tabClass1="layoutTabOn";
}

if ($params['wizard_name'] == 'clientcomp_wizard') { ?>
    <table cellspacing="0" cellpadding="0" border="0" class="wizTabs" width="80%">
        <tr>
            <td width="100" class="<?=$tabClass1;?>">
                <a href="javascript:Jump('1');" onmouseover='window.status="<?= statusdisp('LoginTab');?>"; return true;' onfocus='window.status="<?=statusdisp('LoginTab');?>"; return true;'
                    onmousemove='window.status="<?= statusdisp('LoginTab');?>"; return true;' onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    <?=I18N::getSingleToken("IA.LOGIN");?>
                </a>
            </td>

            <td width="100" class="<?=$tabClass2;?>">
                <a href="javascript:Jump('2');" onmouseover='window.status="<?=statusdisp('TemplateTab');?>"; return true;' onfocus='window.status="<?=statusdisp('TemplateTab');?>"; return true;'
                   onmousemove='window.status="<?= statusdisp('TemplateTab');?>"; return true;' onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    <?=I18N::getSingleToken("IA.TEMPLATE");?>
                </a>
            </td>

            <td width="100" class="<?=$tabClass3;?>">
                <a href="javascript:Jump('3');" onmouseover='window.status="<?=statusdisp('CompanyTab');?>"; return true;' onfocus='window.status="<?=statusdisp('CompanyTab');?>"; return true;'
                    onmousemove='window.status="<?=statusdisp('CompanyTab');?>"; return true;' onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    <?=I18N::getSingleToken("IA.COMPANY");?>
                </a>
            </td>

            <td width="100" class="<?=$tabClass4;?>">
                <a href="javascript:Jump('4');" onmouseover='window.status="<?=statusdisp('SubscriptionTab');?>"; return true;' onfocus='window.status="<?=statusdisp('SubscriptionTab');?>"; return true;'
                    onmousemove='window.status="<?=statusdisp('SubscriptionTab');?>"; return true;' onmouseout ="window.status=''; return true;" onblur="window.status=''; return true;">
                    <?=I18N::getSingleToken("IA.SUBSCRIBE");?>
                </a>
            </td>
        </tr>
    </table>
<? } else { ?>
    <? /** @noinspection PhpUndefinedVariableInspection */
    if ($cpawiz) { ?>
	<table cellspacing="0" cellpadding="0" border="0" class="wizTabs" width="80%">
        <tr>
        <? if ($params['wizard_name'] == 'setup_wizard_acct') { ?>
            <td width="100" class="<?=$tabClass1;?>">
                <a href="javascript:Jump('1');" onmouseover='window.status="<?=statusdisp('CompanyTab');?>"; return true;' onfocus='window.status="<?=statusdisp('CompanyTab');?>"; return true;' onmousemove='window.status="<?=statusdisp('CompanyTab');?>"; return true;'
                    onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    Company
                </a>
            </td>

            <td width="100" class="<?=$tabClass2;?>">
                <a href="javascript:Jump('2');" onmouseover='window.status="<?=statusdisp('TemplateTab'); ?>"; return true;' onfocus='window.status="<?= statusdisp('TemplateTab'); ?>"; return true;' onmousemove='window.status="<?= statusdisp('TemplateTab'); ?>"; return true;'
                onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    Template
                </a>
            </td>
        <? } else { ?>
            <td width="100" class="<?=$tabClass1;?>">
                <a href="javascript:Jump('1');" onmouseover='window.status="<?=statusdisp('LoginTab');?>"; return true;' onfocus='window.status="<?= statusdisp('LoginTab'); ?>"; return true;'
                onmousemove='window.status="<?=statusdisp('LoginTab');?>"; return true;' onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    Login
                </a>
            </td>

            <td width="100" class="<?=$tabClass2;?>">
                <a href="javascript:Jump('2');" onmouseover='window.status="<?=statusdisp('CompanyTab');?>"; return true;' onfocus='window.status="<?=statusdisp('CompanyTab');?>"; return true;'
                    onmousemove='window.status="<?= statusdisp('CompanyTab'); ?>"; return true;' onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    Company
                </a>
            </td>
        <? } ?>
	    </tr>
    </table>
    <? } else { // create company ?>
	<table cellspacing="0" cellpadding="0" border="0" class="wizTabs" width="80%">
        <tr>
            <td width="100" class="<?=$tabClass1;?>">
                <a href="javascript:Jump('1');" onmouseover='window.status="<?=statusdisp('LoginTab');?>"; return true;' onfocus='window.status="<?=statusdisp('LoginTab');?>"; return true;'
                    onmousemove='window.status="<?=statusdisp('LoginTab');?>"; return true;'
                    onmouseout="window.status=''; return true;" onblur ="window.status=''; return true;">
                    Login
                </a>
            </td>

            <td width="100" class="<?=$tabClass2;?>">
                <a href="javascript:Jump('2');" onmouseover='window.status="<?=statusdisp('CompanyTab');?>"; return true;' onfocus='window.status="<?=statusdisp('CompanyTab');?>"; return true;'
                    onmousemove='window.status="<?=statusdisp('CompanyTab');?>"; return true;' onmouseout="window.status=''; return true;" onblur="window.status=''; return true;">
                    Company
                </a>
            </td>

            <td width="100" class="<?=$tabClass3;?>">
                <a href="javascript:Jump('3');" onmouseover='window.status="<?=statusdisp('SubscriptionTab');?>"; return true;' onfocus='window.status="<?=statusdisp('SubscriptionTab');?>"; return true;'
                    onmousemove='window.status="<?=statusdisp('SubscriptionTab');?>"; return true;' onmouseout ="window.status=''; return true;" onblur="window.status=''; return true;">
                    Subscribe
                </a>
            </td>
        </tr>
    </table>
    <? } ?>
<? } ?>

<!--    <map name="help_map">-->
<!--        <area shape="rect" coords="10,0,66,23" href="javascript:Launch('--><?//=GetHelpFile();?><!--', 'HelpWin', 575, 450);" onmouseover='window.status="<?//=statusdisp('Help');?>"; return true;' onfocus='window.status="<?//=statusdisp('Help');?>//"; return true;'-->
<!--            onmousemove='window.status="<?//=statusdisp('Help');?>//"; return true;' onmouseout="window.status=''; return true;" onblur="window.status=''; return true;"> -->
<!--   </map>-->

    </td></tr>
   </table>
   <!-- table after the pagings tab -->
   <table border="0" cellpadding="0" cellspacing="0" width="800" style="margin-top1:10px;border:1px solid #b2b2b2;">
   <tr><td>
   <!-- -->
    <?
    break;                                         // end financials ?>

<tr><td class="wizSubHdr" align="right">

<?

        // ovi - is any of the below still used?

        case 'order_papers':

            $params = $wizParams['order_papers'];
            html_wizard_no_tabs($params);

        ?>

                        <? if ($_pg != '-1' && $_pg != '0') { ?>
                        <a class="Task" href="javascript:doSubmit('0');" onmouseover="window.status='<?=statusdisp('Back')?>'; return true;" onfocus="window.status='<?=statusdisp('Back')?>'; return true;" onblur="window.status=''" onmouseout="window.status=''"><?=I18N::getSingleToken("IA.PREVIOUS");?></a>
                        <? } ?>
                        <? if ($_pg == '0' ) { ?>
                        <a class="Task" href="javascript:document.orderpapers.elements['.order'].value = '1';document.orderpapers.submit();" onmouseover="window.status='<?=statusdisp('order') ?>'; return true;" onfocus="window.status='<?= statusdisp('order') ?>'; return true;"
                           onblur="window.status=''" onmouseout="window.status=''"><?=I18N::getSingleToken("IA.ORDER");?></a>
                        <? } ?>
                        <? if (!empty($data['listfield'])) { ?>
                        <a class="Task" type="nocheck" href="javascript:doAdd();document.orderpapers.submit();" onmouseover="window.status='<?=statusdisp('order')?>'; return true;" onfocus="window.status='<?=statusdisp('order')?>'; return true;" onblur="window.status=''" onmouseout="window.status=''"><?=I18N::getSingleToken("IA.ADD");?></a>
                        <? } ?>
                    </td></tr>
                    <?
            break;

        case 'gledger':                                    //General Ledger Wizard

            $params = $wizParams['gledger'];

            html_wizard_no_tabs($params);
        ?>

         <tr>
                <td bgcolor="#FFFFFF" background="../resources/images/ia-app/backgrounds/shadow.gif"><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="500" height="14" border="0"></td>
               </tr>

        <?
            break;

        case 'glcoa':                                    //General Ledger Chart of Accounts

            $params = $wizParams['glcoa'];

            html_wizard_no_tabs($params);
        ?>

         <tr>
                <td bgcolor="#FFFFFF" background="../resources/images/ia-app/backgrounds/shadow.gif"><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="500" height="14" border="0"></td>
               </tr>

        <?
            break;

        case 'vendor':                                //AP Ledger

               $params = $wizParams['apledger'];
               html_wizard_no_tabs($params);

            break;

        case 'customer':

            $params = $wizParams['arledger'];
            html_wizard_no_tabs($params);

            break;

        case 'form1099':

            $params = $wizParams['form1099'];
            html_wizard_no_tabs($params);

            break;

        case 'vendoraging':

            $params = $wizParams['vendor_aging'];
            html_wizard_no_tabs($params);
        ?>
         <tr>
             <td bgcolor="#FFFFFF" background="../resources/images/ia-app/backgrounds/shadow.gif"><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="410" height="14" border="0"></td>
            </tr>
        <?
            break;

        case 'customeraging':

            $params = $wizParams['customer_aging'];
            html_wizard_no_tabs($params);
        ?>
         <tr>
                   <td bgcolor="#FFFFFF" background="../resources/images/ia-app/backgrounds/shadow.gif"><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="410" height="14" border="0"></td>
               </tr>
        <?
            break;

        case 'sales_register':

            $params = $wizParams['sales_register'];
            html_wizard_no_tabs($params);

            break;

        case 'bank_register':

                $params = $wizParams['bank_register'];
            html_wizard_no_tabs($params);
            break;

        case 'trial_balance':

            $params = $wizParams['trial_balance'];
            html_wizard_no_tabs($params);
        ?>
        <tr>
        <td bgcolor="#FFFFFF" background="../resources/images/ia-app/backgrounds/shadow.gif"><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="500" height="14" border="0"></td>
        </tr>
        <?
            break;

        case 'account_balances':

            $params = $wizParams['account_balances'];
            html_wizard_no_tabs($params);
        ?>
        <tr>
        <td bgcolor="#FFFFFF" background="../resources/images/ia-app/backgrounds/shadow.gif"><img src="../resources/images/ia-app/backgrounds/trans.gif" alt="" width="500" height="14" border="0"></td>
        </tr>
        <?
            break;
        case 'statements':

            $params = $wizParams['statements'];
            html_wizard_no_tabs($params);

            break;
        case 'invoices':

            $params = $wizParams['invoices'];
            html_wizard_no_tabs($params);

            break;
        case 'tbinvoices':

            $params = $wizParams['tbinvoices'];
            html_wizard_no_tabs($params);

            break;
        case 'expenseledger':

            $params = $wizParams['expenseledger'];
            html_wizard_no_tabs($params);

            break;
        case 'selecttopay' :

            $params = $wizParams['selecttopay'];
            html_wizard_no_tabs($params);
            break;

        case 'selecttoreimburse' :
            $params = $wizParams['selecttoreimburse'];
            html_wizard_no_tabs($params);
            break;

        } //end switch
    }
}
