<?php

class DomainServiceAPIObjectClient extends TrustedAPIObjectClient
{
    /**
     * @param string $runtimeOwner the URL prefix for the APIs; usually the DS name, ia-ds-XYZ
     * @param string $object       the API object name
     * @param string $version      the API version to use
     */
    public function __construct(string $runtimeOwner, string $object, string $version)
    {
        $baseURL = DomainServiceUtil::getBaseURL();
        $baseURL .= '/' . $runtimeOwner . '/ia';
        parent::__construct($baseURL, $object, $version);
    }

}