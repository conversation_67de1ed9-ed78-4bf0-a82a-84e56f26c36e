<?php

/**
 * APIReqControlService is a factory that returns APIReqControlLock
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

class APIReqControlService extends APIReqControlLock
{
    const LOCK_TYPE_XML_API_COMPATIBLE = 1;

    /**
     * @param string $controlId
     * @param string $partnerId
     * @param string $object
     * @param string $operation
     * @return APILock
     * @throws APIException
     */
    public static function getLock($controlId, $partnerId, $object, $operation)
    {
        return new APIReqControlLock($controlId, $partnerId, $object, $operation);
    }
}