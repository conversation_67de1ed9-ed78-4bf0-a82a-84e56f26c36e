<?php

/**
 * OutboundWebhookAPIAdapterBase is not for adapting directly but providing implementation
 * which handles common steps that most adapter for the outbound webhook version
 *
 *
 * <AUTHOR>
 * @copyright Copyright (C)2021 Sage Intacct Corporation, All Rights Reserved
 */

class OutboundWebhookAPIAdapterBase extends ObjectAPIAdapterBase
{
    /**
     * @inheritDoc
     */
    public function executeOperation($operation, $objectName, $version, $request, &$ownedRequest, &$extraParams,
                                     $isOwned = false, $caller = ObjectAPIAdapter::CALLER_ADAPTER_PRE)
    {
        if ($this->getNextAdapter()) {
            $nextAdapter = $this->getNextAdapter();
            return $nextAdapter->executeOperation($operation, $objectName, $version, $request, $ownedRequest,
                $extraParams, $isOwned, $caller);
        } else {
            return $ownedRequest;
        }
    }

    /**
     * @inheritDoc
     */
    function executeCRUD( /** @noinspection PhpUnusedParameterInspection */
        $operation, $objectName, $version, $request, &$ownedRequest, &$extraParams = [],
        $isOwned = false, $caller = ObjectAPIAdapter::CALLER_ADAPTER_PRE)
    {
        throw (new APIOrchestratorException())->setAPIError(APIError::getInstance(
            APIErrorMessages::METHOD_NOT_ALLOWED_INVALID_OPERATION_0004, [ "OPERATION" => APIConstants::getOperation($operation), "RESOURCE_NAME" => $objectName, "VERSION" => $version], true));
    }

    /**
     * @inheritDoc
     */
    function executeQuery( /** @noinspection PhpUnusedParameterInspection */
        $operation, $objectName, $version, $request,
        &$ownedRequest, $isOwned = false, $caller = ObjectAPIAdapter::CALLER_ADAPTER_PRE)
    {
        throw (new APIOrchestratorException())->setAPIError(APIError::getInstance(
            APIErrorMessages::METHOD_NOT_ALLOWED_INVALID_OPERATION_0004, [ "OPERATION" => APIConstants::getOperation($operation), "RESOURCE_NAME" => $objectName, "VERSION" => $version], true));
    }
}
