<?php

/**
 * APIOrchestrator is the interface that API Processor will interact with
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */



interface APIOrchestrator
{

    /**
     * The execution interface for Orchestrators
     *
     * @param string $operation
     * @param array $request
     * @param array $extraParams
     *
     * @return array
     * @throws APIAdapterException
     */
    function execute($operation, $request = [], &$extraParams = []);

    /**
     * The execution interface for Orchestrators
     *
     * @param string      $operation
     * @param array|null  $request
     * @param array       $extraParams
     *
     * @return array
     * @throws APIInternalException
     */
    function executeInternal($operation, $request, &$extraParams = []);

    /**
     * The execution interface for internal trusted Orchestrators only
     * Child class implementing this class should suppress "Operation Validation"
     * If any doubt, please check with Valer & API team
     *
     * @param string      $operation
     * @param array|null  $request
     * @param array       $extraParams
     *
     * @return array
     * @throws APIInternalException
     */
    function executeInternalSuppressOperationCheck($operation, $request, &$extraParams = []);

    /**
     * Returns handler associated with this orchestrator
     * @return APIHandler
     */
    function getHandler();

    /**
     * @return bool true if the response schema for this execution is empty
     */
    function hasEmptyResponseSchema() : bool;
}