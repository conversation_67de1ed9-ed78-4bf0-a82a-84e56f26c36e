<?php


/**
 * Vehicle for passing NextGen API layer response to REST layer
 *
 * <AUTHOR> API Team
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

class HTTPAPIResponse
{
    const HTTP_OK = 200;
    const HTTP_CREATED = 201;
    const HTTP_ACCEPTED = 202;
    const HTTP_NO_CONTENT = 204;
    const HTTP_SEE_OTHER = 303;
    const ALLOW_HEADER = 'Allow';

    /** @var APIError|null $apiError*/
    private $apiError = null;

    /** @var array|null $responseBody */
    private $responseBody = null;

    /** @var array $responseHeaders */
    private $responseHeaders = [];

    /** @var int $status */
    private $status = 200;

    /** @var int $contentType */
    private $contentType = APIConstants::CONTENT_TYPE_JSON;

    /** @var bool $isArrayFormat */
    private $isArrayFormat = true;

    /** @var bool $enableOutputWrapper */
    private $enableOutputWrapper = true;

    private function __construct(){}

    /**
     * @return string
     */
    public function __toString(): string
    {

        if ($this->contentType == APIConstants::CONTENT_TYPE_JSON) {
            // json response type needs further processing
            $result = $this->toArray();
            if (!$this->enableOutputWrapper) {
                // skip ia::result & ia::meta wrapping process
                $result = $result[APIConstants::IA_RESULT_KEY] ?? $result;
            } // else, wrap with ia::result and ia::meta as usual
            return APIUtil::getJsonString($result) ?? '';
        } else {
            // raw response (for other contant types) does not need processing, return it as it is
            return $this->responseBody[APIConstants::IA_RAW] ?? '';
        }
    }

    /**
     * @return array|null
     */
    public function toArray(): ?array
    {
        return ($this->isArrayFormat) ?
            $this->responseBody : // bulk responses format, can be all success, partial failed, or all failed
            [   // single error format, add ia::meta for consistency
                // but not guarantee all API error will ia::meta, due to exception thrown outside of API stack
                APIConstants::IA_RESULT_KEY => $this->apiError->getErrorResponseObject(),
                APIConstants::IA_META_KEY => [
                    APIConstants::API_TOTAL_COUNT_KEY   => 1,
                    APIConstants::API_TOTAL_SUCCESS_KEY => 0,
                    APIConstants::API_TOTAL_ERROR_KEY   => 1
                ]
            ];
    }

    /**
     * @return string
     */
    public function getErrorMessage(): string
    {
        if ($this->status < APIErrorMessages::HTTP_STATUS_CODE_MINIMUM_FAIL &&
        $this->status != 207) {
            $errorObject = [];
        } else {
            $errorObject = ($this->isArrayFormat()) ? ($this->responseBody[APIError::KEY_ERROR] ?? []) :
                $this->apiError->getErrorResponsePayload();
        }
        return APIUtil::getJsonString($errorObject);
    }

    /**
     * @return APIError|null
     */
    public function getError() : ?APIError
    {
        return $this->apiError;
    }

    /**
     * @return string
     */
    public function getErrorCode(): string
    {
        return ($this->isArrayFormat) ? (($this->responseBody[APIError::KEY_ERROR][APIError::KEY_ERROR_CATEGORY]) ?? '')
            : $this->apiError->getCategory();
    }

    /**
     * @param String $key
     * @param array $response
     *
     * @return bool
     */
    public static function nestedArrayKeyExists($key, $response): bool
    {
        foreach ($response as $item) {
            // Check if key exists in the 'results' array
            if (isset($item[APIConstants::IA_RESULT_KEY][$key])) {
                return true; // Key found
            }
        }
        return false; // Key not found in any element
    }

    /**
     * @param array $response
     *
     * @return bool
     */
    public static function nestedStatusPartialSuccess($response): bool
    {
        foreach ($response as $item) {
            // Check if status exists in the response
            if (isset($item[APIError::KEY_IA_STATUS]) && $item[APIError::KEY_IA_STATUS] == 207) {
                return true; // nested partial success
            }
        }
        return false; // 207 status not found in any element
    }

    /**
     * @param array|null $response
     * @param int     $status
     * @param bool       $enableOutputWrapper
     *
     * @return HTTPAPIResponse
     * @throws APIInternalException
     */
    public static function getResponseInstance($response, int $status = 200, bool $enableOutputWrapper = true): HTTPAPIResponse
    {
        $apiResponse = new HTTPAPIResponse();
        $apiResponse->enableOutputWrapper = $enableOutputWrapper;

        $metadata = $response[APIConstants::IA_META_KEY]?? [];
        if (!empty($metadata)) {
            unset($response[APIConstants::IA_META_KEY]);
        }
        $body = $response[APIConstants::IA_RESULT_KEY]?? $response;

        // for RAW response no need to process, only return as it is with appropriate content type
        if (isset($response[APIConstants::IA_RAW])) {
            // this is a binary response (used in file download)
            $apiResponse->setStatus($status);
            $apiResponse->setContentType($response[APIConstants::IA_CONTENT_TYPE]);
            $apiResponse->setResponseBody([APIConstants::IA_RAW => $response[APIConstants::IA_RAW]]);
            return $apiResponse;
        } 
        if ($body === null) {
            $status = self::HTTP_NO_CONTENT;
        } else {
            // check that if body is an indexed array (vs fields and values) it does not have null values
            if (!empty($body) && is_numeric(key($body)) && array_keys($body) === range(0, count($body) - 1) && in_array(null, $body, true)) {
                throw (new APIInternalException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0003,[],true));
            }
            if (!isset($metadata[APIConstants::API_TOTAL_COUNT_KEY])) {
                // if a single result -
                $metadata[APIConstants::API_TOTAL_COUNT_KEY] = (empty($body) || is_numeric(key($body)))? count($body) : 1;
            }
            if (array_key_exists(APIConstants::API_ERROR_CODE_KEY, $metadata)) {
                $status = $metadata[APIConstants::API_ERROR_CODE_KEY];
                unset($metadata[APIConstants::API_ERROR_CODE_KEY]);
            } else if (array_key_exists(APIError::KEY_IA_STATUS, $body)) {
                $status = $body[APIError::KEY_IA_STATUS];
                unset($body[APIError::KEY_IA_STATUS]);
                // a single record scenario, should not keep status in payload
            }
            if ($status < APIErrorMessages::HTTP_STATUS_CODE_MINIMUM_FAIL) {
                if (key_exists(APIError::KEY_ERROR, $body)) {
                    // this logic is to tolerate the scenario of an response array (as opposed to APIError) that is
                    // in fact an well formatted error response

                    //default status in case the KEY_STATUS is not set or is not numeric
                    $key_status = 500;
                    if (isset($body[APIError::KEY_ERROR][APIError::KEY_STATUS]) && ctype_digit($body[APIError::KEY_ERROR][APIError::KEY_STATUS])) {
                        $key_status = intval($body[APIError::KEY_ERROR][APIError::KEY_STATUS]);
                    }
                    $status = $key_status;
                } else if (count(array_column($response, APIError::KEY_ERROR)) > 0 ||
                           ( is_numeric(key($response)) && ( self::nestedArrayKeyExists(APIError::KEY_ERROR, $response) || self::nestedStatusPartialSuccess($response)) )) { // partial error 207
                    $response = self::setRecordLevelStatus($body, $status);
                    $body = $response[0];
                    $status = $response[1];
                    if (!isset($metadata[APIConstants::API_TOTAL_SUCCESS_KEY])) {

                        $totalSuccess = 0;
                        foreach ($body as $item) {
                            if ($item[APIError::KEY_IA_STATUS] < APIErrorMessages::HTTP_STATUS_CODE_MINIMUM_FAIL) {
                                $totalSuccess++;
                            }
                        }
                        $metadata[APIConstants::API_TOTAL_SUCCESS_KEY] = $totalSuccess;
                        $metadata[APIConstants::API_TOTAL_ERROR_KEY] = $metadata[APIConstants::API_TOTAL_COUNT_KEY] - $totalSuccess;
                    }
                }
            } else {
                $metadata[APIConstants::API_TOTAL_SUCCESS_KEY] = 0;
                $metadata[APIConstants::API_TOTAL_ERROR_KEY] = $metadata[APIConstants::API_TOTAL_COUNT_KEY];
            }
        }
        // add totalSuccess/totalError if missing but NOT if it is a paged response
        if (!array_key_exists(APIQueryUtil::API_QUERY_RESULT_PAGE_SIZE, $metadata)) {
            $metadata[APIConstants::API_TOTAL_SUCCESS_KEY]  = $metadata[APIConstants::API_TOTAL_SUCCESS_KEY]  ??
                                                              $metadata[APIConstants::API_TOTAL_COUNT_KEY];
            $metadata[APIConstants::API_TOTAL_ERROR_KEY] = $metadata[APIConstants::API_TOTAL_ERROR_KEY] ?? 0;
        }

        $apiResponse->setStatus($status);
        if ($body !== null) {
            $apiResponse->setResponseBody([
                                              APIConstants::IA_RESULT_KEY => $body,
                                              APIConstants::IA_META_KEY => $metadata
                                          ]);
        }
        $apiResponse->setIsArrayFormat(true);
        return $apiResponse;
    }

    /**
     * @param APIError|null $apiError
     *
     * @return HTTPAPIResponse
     * @throws APIInternalException
     */
    public static function getErrorResponseInstance($apiError): HTTPAPIResponse
    {
        if ($apiError->getStatus() === 207) {
            // per API team group discussion, 207 response should not be wrapped with top error object
            // route to self::getResponseInstance to have a consistent wrapping format
            return self::getResponseInstance($apiError->getDetails());
        }
        $apiResponse =  new HTTPAPIResponse();
        $apiResponse->setStatus($apiError->getStatus());
        $apiResponse->setAPIError($apiError);
        $apiResponse->setIsArrayFormat(false);
        if ($apiError->getStatus() === APIError::HTTP_405_STATUS) {
            // 405 requires to have Allowed header
            $allowed = $apiError->getDetailsForCode(APIError::HTTP_405_STATUS);
            $apiResponse->setResponseHeaders([self::ALLOW_HEADER => implode(',', $allowed)]);
        }
        // todo - make sure meta exists
        return $apiResponse;
    }

    public function send()
    {
        foreach ($this->responseHeaders as $header=>$value) {
            header($header . ': ' . ($value?? ''));
        }
        http_response_code($this->status);
        if ($this->responseBody && ($this->responseBody[APIConstants::IA_RAW] ?? null) === APIConstants::IA_SKIP_RESPONSE_VALUE) {
            return; // special marker for an empty raw response to skip
        }
        echo $this;
    }

    /**
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @return array
     */
    public function getResponseHeaders() : array
    {
        return $this->responseHeaders;
    }

    /**
     * @return array|null
     */
    public function getResponseBody()
    {
        return $this->responseBody;
    }

    /**
     * @param string     $header
     * @param mixed|null $value
     *
     * @return HTTPAPIResponse
     */
    public function addResponseHeader(string $header, $value) : HTTPAPIResponse
    {
        $this->responseHeaders[$header] = $value;
        return $this;
    }

    /**
     * @param array $responseHeaders
     *
     * @return HTTPAPIResponse
     */
    public function setResponseHeaders(array $responseHeaders) : HTTPAPIResponse
    {
        $this->responseHeaders = $responseHeaders;
        return $this;
    }

    /**
     * @return bool
     */
    public function isArrayFormat(): bool
    {
        return $this->isArrayFormat;
    }

    /**
     * @param bool $isArrayFormat
     *
     */
    private function setIsArrayFormat(bool $isArrayFormat)
    {
        $this->isArrayFormat = $isArrayFormat;
    }

    /**
     * @param array|null $responseBody
     *
     */
    private function setResponseBody($responseBody)
    {
        $this->responseBody = $responseBody;
    }

    /**
     * @param int $contentType
     */
    public function setContentType($contentType) : void
    {
        $this->contentType = $contentType;
    }

    /**
     * @param APIError|null $apiError
     *
     */
    private function setAPIError($apiError)
    {
        $this->apiError = $apiError;
    }

    /**
     * @param int $status
     *
     * @throws APIInternalException
     */
    private function setStatus(int $status)
    {
        if ($status < 200 || $status > 600) {
            throw (new APIInternalException())->setAPIError(APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0003,[],true));
        }
        $this->status = $status;
    }

    /**
     * Manually populate ia::status for every single record in a partial failure bulk response
     *
     * @param  array  $response
     * @param  int $defaultStatus
     * @return array
     */
    private static function setRecordLevelStatus(array $response, int $defaultStatus)
    {
        if (!is_numeric(key($response))) {return $response;}
        $newResponse = [];
        $responseStatus = null;
        $isMultiStatus = false;
        foreach ($response as $key => $value) {
            if (empty($value)) {
                $newResponse[$key] = [
                    APIError::KEY_IA_STATUS => self::HTTP_NO_CONTENT
                ];
            } else if (array_key_exists(APIError::KEY_ERROR, $value)) {
                // for consistency, move the error status from error object to the record root
                if (array_key_exists(APIError::KEY_STATUS, $value)) {
                    $value[APIError::KEY_IA_STATUS] = $value[APIError::KEY_STATUS];
                } else if (!array_key_exists(APIError::KEY_IA_STATUS, $value)) {

                    //default status in case the KEY_STATUS is not set or is not numeric
                    $key_status = 500;
                    if (isset($value[APIError::KEY_ERROR][APIError::KEY_STATUS]) && ctype_digit($value[APIError::KEY_ERROR][APIError::KEY_STATUS])) {
                        $key_status = intval($value[APIError::KEY_ERROR][APIError::KEY_STATUS]);
                    }
                    $value[APIError::KEY_IA_STATUS] = $key_status;
                }
                unset($value[APIError::KEY_ERROR][APIError::KEY_STATUS]);
                $newResponse[$key] = $value;
            } else {
                // if not empty, and not non-success, use the default status
                if (empty($value[APIError::KEY_IA_STATUS])) {
                    $value[APIError::KEY_IA_STATUS] = $defaultStatus;
                }
                $newResponse[$key] = $value;
            }
            if (!$isMultiStatus) {
                if (!$responseStatus) { // if not set yet, set it
                    $responseStatus = $newResponse[$key][APIError::KEY_IA_STATUS];
                } else if ($responseStatus != $newResponse[$key][APIError::KEY_IA_STATUS]) {
                    $responseStatus = 207;
                    $isMultiStatus = true;
                }
            }
        }
        return [$newResponse, $responseStatus];
    }

    /**
     * @return $this
     */
    public function enableOutputWrapper(): HTTPAPIResponse
    {
        $this->enableOutputWrapper = true;
        return $this;
    }

    /**
     * @return $this
     */
    public function disableOutputWrapper(): HTTPAPIResponse
    {
        $this->enableOutputWrapper = false;
        return $this;
    }
}
