<?php

/**
 * Class APIProcessor
 *
 * API main class to be invoked by all clients for the operation specified
 *
 * <AUTHOR>
 * @copyright 2020 Sage Intacct, Inc. -- All Rights Reserved.
 */
class APIProcessor
{
    /**
     * @var string $requestPath
     */
    var $requestPath;

    /**
     * @var string $requestVersion
     */
    var $requestVersion;

    /**
     * Type of the resource in request (not necessarily the type in registry)
     *
     * @var string $resourceType
     */
    var $resourceType;

    /**
     * @var RegistryLoader $registry
     */
    var $registry;

    /** @var MetricNextGenAPIProcess $apiMetrics */
    var $apiMetrics;

    /**
     * APIProcessor constructor.
     *
     * @param string $requestPath
     *
     * @throws APIException
     */
    public function __construct(string $requestPath)
    {
        $this->registry = RegistryLoader::getInstance($requestPath, true); // will throw an exception if not available
        $this->requestVersion = $this->registry->getVersion();
        $this->requestPath = $requestPath;
    }

    /**
     * Invoke specific API operation with the specified payload
     *
     * @param string $operation
     * @param string $payload a JSON as a string
     * @param array  $extraParams
     *
     * @return HTTPAPIResponse
     * @throws APIException
     * @throws APIInternalException
     */
    public function invoke(string $operation, string $payload, array &$extraParams = []): HTTPAPIResponse
    {
        if ( RegistryLoader::$doTimers) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer = new ScopeTimer('APIProcessor', false, 'RegistryLoader::logTiming', 'APIProcessor::invoke');
        }
        try {
            $this->initMetrics();
            if (APIUtil::getDecodedJson($payload, $payload0, $response, APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0007)) {
                $orchestratorHandler = $this->registry->getOrchestratorHandlerForPath($this->requestPath);
                $ok = $orchestratorHandler !== null;
                if ($ok) {
                    $orchestratorHandler->validateRequestPath($this->requestPath, $extraParams);
                    $orchestratorHandler->validateOperation($operation, $extraParams);
                    $orchestratorHandler->validateQueryString($extraParams[APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][APIConstants::API_URI_QUERY_STRING]??'');

                    // TODO: move conversion to code which cannot use HTTP mthods
                    $operation = APIConstants::API_HTTP_METHOD_TO_OP[$operation] ?? '';
                    $this->apiMetrics->setVersion($this->requestVersion);
                    $this->apiMetrics->startTime();
                    if (APIAsyncUtil::isAsyncRequest($extraParams)) {
                        $response = APIAsyncUtil::processExternalAsyncRequest($operation, $payload0, $extraParams);
                    } else {
                        $orchestrator = $orchestratorHandler->getOrchestrator();
                        $ok = $orchestrator !== null;
                        if ($ok) {
                            $result = $orchestrator->execute($operation, $payload0, $extraParams);
                            $statusCode = $result[APIError::KEY_IA_STATUS]?? $orchestratorHandler->getSuccessCodeForOperation($operation, false);
                            if (is_array($result) && empty($result) && $orchestrator->hasEmptyResponseSchema()) {
                                $result = null;
                                $statusCode = 204;
                            }
                            $response = HTTPAPIResponse::getResponseInstance($result, $statusCode);
                        }
                    }
                }
                if (!$ok) {
                    $response = HTTPAPIResponse::getErrorResponseInstance(
                        APIError::getInstance(
                            APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0008, [
                                // extract the the resource name from the path (if not possible - use as-is
                                "RESOURCE" => RegistryLoader::getResourcePathParts($this->requestPath)[3] ?? $this->requestPath,
                                "VERSION" => $this->requestVersion
                            ], true)->freeze());
                }
            }
        } catch (Throwable $t) {
            if ($t instanceof APIException && $t->hasAPIError()) {
                $response = HTTPAPIResponse::getErrorResponseInstance($t->getAPIError()->freeze());
            } else {
                $apiErr = APIError::getInstance(
                    APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0014, [
                        "OPERATION" => APIConstants::getOperation($operation),
                        "PATH" => $this->requestPath
                    ], true);
                if (!Globals::$g->islive) {
                    $apiErr->addDetailError($apiErr->getCategory(), $t->getMessage(), '');

                    // when exception was not caught by API and ORM layers,
                    // log exception details to file
                    LogToFile("Caught exception: " . $t->getMessage());
                    LogToFile("In file: " . $t->getFile() . " on line " . $t->getLine());
                    LogToFile("Stack trace: " . $t->getTraceAsString());
                }
                $response = HTTPAPIResponse::getErrorResponseInstance($apiErr->addDetailException($t)->freeze());
            }
            if (!Globals::$g->islive) {
                // if non-production, log requests
                $requestStr = __CLASS__
                    .':Request'.':'.$operation
                    .':'.$this->requestVersion
                    .':'.implode('/',$extraParams[APIConstants::API_EXTRA_PARAM_HIERARCHY]??[])
                    .':'.print_r($payload??[], true);
                LogToFile($requestStr);
            }
        } finally {
            $this->addResponseHeaders($response, $extraParams);
            $this->updateMetrics($response, $operation, $extraParams); /** stop profiling meters */

        }
        return $response;
    }

    /**
     * adds response headers if any piled during the process
     *
     * @param HTTPAPIResponse $response
     * @param array $extraParams
     */
    private function addResponseHeaders(HTTPAPIResponse $response, array $extraParams)
    {
        foreach ($extraParams[APIConstants::API_EXTRA_RESPONSE_HEADER]?? [] as $key=>$value) {
            $response->addResponseHeader($key, $value);
        }
    }

    /**
     * @throws IAException
     */
    private function initMetrics()
    {
        /** start profiling meters */
        Globals::$g->perfdata->startChild(new PerfDataForAPI());
        $this->apiMetrics = new MetricNextGenAPIProcess();
        $this->apiMetrics->setStatus(APIError::KEY_ERROR);
        $this->apiMetrics->setType('');
        $this->apiMetrics->setMode('');
    }

    /**
     * @param HTTPAPIResponse         $response
     * @param string                  $operation
     * @param array                   $extraParams
     */
    private function updateMetrics(HTTPAPIResponse $response, string $operation, array $extraParams)
    {
        $serviceOperation = ( $this->requestPath === APIConstants::API_QUERY) ? APIConstants::API_QUERY : $operation;
        $resourcePath =  (implode('/',$extraParams[APIConstants::API_EXTRA_PARAM_HIERARCHY]??[]));
        $type = ( $this->requestPath === APIConstants::API_QUERY) ? APIConstants::API_QUERY :
            "[$serviceOperation] [" . $resourcePath . "]";
        $mode = APIAsyncUtil::isAsyncRequest($extraParams)? 'Async' : 'Sync';
        $status = ($response->getStatus() < APIErrorMessages::HTTP_STATUS_CODE_MINIMUM_FAIL) ?
            APIProfilingUtil::METRICS_RESULT_STATUS_SUCCESS : APIProfilingUtil::METRICS_RESULT_STATUS_ERROR;

        // publish metrics
        if (isset($this->apiMetrics)) {
            $this->apiMetrics->setStatus($status);
            $this->apiMetrics->setMode($mode);
            try {
                $this->apiMetrics->stopTime();
            } catch (IAException) {
                /** timer may not be started, ignore in that case, IAException has logged the exception to file */
            }
            $this->apiMetrics->setType($type);
            $this->apiMetrics->setErrorCode($response->getErrorCode());
            $this->apiMetrics->setErrorMessage($response->getErrorMessage());
            $this->apiMetrics->publish();
        }

        // publish perf data
        // todo - set domain & resource
        $perfData = Globals::$g->perfdata->getChild();
        if ($perfData !== null && $perfData instanceof PerfDataForAPI) {
            $perfData->setVersion($this->requestVersion);
            $perfData->setFunctionName($serviceOperation);
            $perfData->setObject($resourcePath);
            $perfData->setServiceMode($mode);
            $perfData->setStatusCode($response->getStatus());
            $perfData->setProcessor(__CLASS__);
            $perfData->setXmlpartnerid($extraParams[APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][APIConstants::API_EXTRA_PARAM_CONTROL_PARAM_SENDER_ID]?? PerfDataForAPI::NOT_APPLICABLE);
            Globals::$g->perfdata->endChild();
        }
    }
}
