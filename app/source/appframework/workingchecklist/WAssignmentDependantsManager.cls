<?php
/**
 * Assignment dependants Manager file
 *
 * TODO vlad: in a future release, change the British from into the US form: dependents instead of dependants
 *
 * Constraints are representing a One2Many relation between assignments
 * i.e. One assignment can have many assignments on which it depends
 *
 * This file help us get the constriants from parent perspective
 *
 * The dependancy is strictly refering to the Start Date and the End Date of the assignment.
 *
 * A Constraint is defined as a sentence. e.g:
 *  " Task1    'starts on or after'  the      'end of'            Task3"
 *   subject          verb                     adverb            complement
 *
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Intacct Corporation All, Rights Reserved
 */


class WAssignmentDependantsManager extends OwnedObjectManager
{

    /**
     * WAssignmentDependantsManager constructor.
     *
     * @param array $params
     */
    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values) : bool
    {
        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values) : bool
    {
        return true;
    }

    /**
     * @param int|string $ID
     *
     * @return bool
     */
    public function Delete($ID)
    {
        return true;
    }
}