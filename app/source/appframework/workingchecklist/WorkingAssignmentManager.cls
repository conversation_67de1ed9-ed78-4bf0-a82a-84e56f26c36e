<?php

/**
 * Working assignment manager file
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */

class WorkingAssignmentManager extends BaseWorkingAssignmentManager
{
    /** @var bool $showRepeatMessage */
    private $showRepeatMessage = false;

    /**
     * WorkingAssignmentManager constructor.
     *
     * @param array $params
     */
    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    /**
     * Create the error messages
     */
    public function createErrorMessages() : void
    {
        $this->errorMessages = [
            self::MSG_UNIQUE_ID => "The assignment id is not unique.",
            self::MSG_MISSING_CHECKLIST =>  'Checklist does not exist.',
            self::MSG_MISSING_CHECKLIST2 => 'The checklist you selected, \'%s\', is not recognized in this company.',
            self::MSG_NO_NAME => 'Assignment name is not set.',
            self::MSG_NO_NAME2 => 'Assignment name field is empty. Please add a name.',
        ];
    }

    /**
     * Create the error codes
     */
    public function createErrorCodes() : void
    {
        $this->errorCodes = [
            self::MSG_UNIQUE_ID => 'CO-0874',
            self::MSG_MISSING_CHECKLIST => 'CO-0871',
            self::MSG_NO_NAME => 'CO-0867',
        ];
    }

    /**
     * @return string
     */
    public function getIdPrefix() : string
    {
        return 'AP';
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values) : bool
    {
        $source = 'WorkingAssignmentManager::regularAdd';
        $ok = $this->beginTrx($source);
        $ok = $ok && parent::regularAdd($values);
        $ok = $ok && $this->addAssignee($values);
        if ( $ok ) {
            // register a post commit event for every assignment created
            $sendNotifWhenAssignedEvent = new SendNotifWhenAssignedEvent();
            $sendNotifWhenAssignedEvent->setValues($values);
            XACT_REGISTER_POSTCOMMIT_EVENT(
                'workingAssignmentNotificationToAssignee' . ($values['RECORDNO'] ??  $values[':recordno']),
                $sendNotifWhenAssignedEvent
            );
            $ok = $this->commitTrx($source);
        } else {
            if ( ! HasErrors() && HasWarnings() ) {
                $this->rollbackTrx($source);
            } else {
                $msg = "Could not create $this->_entity record!";
                Globals::$g->gErr->addIAError('CO-0827', __FILE__ . ':' . __LINE__, "", [], $msg, ['ENTITY' => $this->_entity]);
                $this->rollbackTrx($source);
            }
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values) : bool
    {
        $oldValues = $this->GetByRecordNo($values['RECORDNO'], ['ASSIGNEE', 'STATUSNAME']);

        $source = 'WorkingAssignmentManager::regularSet';
        $ok = $this->beginTrx($source);
        $ok = $ok && parent::regularSet($values);
        $ok = $ok && $this->setAssignee($values);
        if ( $ok ) {
            if ( $oldValues['ASSIGNEE'] !== $values['ASSIGNEE'] ) {
                $sendNotifWhenAssignedEvent = new SendNotifWhenAssignedEvent();
                $sendNotifWhenAssignedEvent->setValues($values);
                XACT_REGISTER_POSTCOMMIT_EVENT(
                    'workingAssignmentNotificationToAssignee' . ($values['RECORDNO'] ??  $values[':recordno']),
                    $sendNotifWhenAssignedEvent
                );
            }
            if ( $oldValues['STATUSNAME'] !== $values['STATUSNAME'] ) {
                $sendNotifForChecklistOwnerEvent = new SendNotifForChecklistOwnerEvent();
                $sendNotifForChecklistOwnerEvent->setValues($values);
                XACT_REGISTER_POSTCOMMIT_EVENT(
                    'workingAssignmentNotificationToCLOwner' . ($values['RECORDNO'] ??  $values[':recordno']),
                    $sendNotifForChecklistOwnerEvent
                );
            }
            $ok = $this->commitTrx($source);
        } else {
            if ( ! HasErrors() && HasWarnings() ) {
                $this->rollbackTrx($source);
            } else {
                $msg = "Could not update $this->_entity record!";
                Globals::$g->gErr->addIAError('CO-0828', __FILE__ . ':' . __LINE__, "", [], $msg, ['CTITLE' => $this->_entity]);
                $this->rollbackTrx($source);
            }
        }
        return $ok;
    }

    /**
     * Convert the NAME values into KEY values of the child records.
     *
     * @param array  $values Values from UI/API
     * @param string $action Add/Set/Delete
     *
     * @return bool
     */
    protected function preProcessValues(&$values, $action) : bool
    {
        $ok = parent::preProcessValues($values, $action);

        if ( $ok && $action == 'set' && WorkingchecklistUtils::cnyAllowsChecklistRules() ) {
            $currentAssignment = $this->get($values['RECORDNO'], [ 'ASSIGNEE' ]);

            if ( !empty($currentAssignment['ASSIGNEE'] )) {
                $contactMgr = Globals::$g->gManagerFactory->getManager('contact');
                $currentAssignmentContactKey = (int) $contactMgr->GetRecordNoFromVid($currentAssignment['ASSIGNEE']);
            }
            $ok = $ok
                  && ! empty($currentAssignmentContactKey)
                  && WorkingchecklistUtils::isUserAllowedToUpdateAssignment($currentAssignmentContactKey, $values['CHECKLISTKEY'] ?? '' );
        }

        if ( $ok && ! isset($values['STATUSKEY']) && isset($values['STATUSNAME']) && isl_strlen($values['STATUSNAME']) > 0 ) {
            $wStatusAssignmentManager = Globals::$g->gManagerFactory->getManager('wassignmentstatus');
            $assignmentStatus = $wStatusAssignmentManager->getByName($values['STATUSNAME'], ['RECORDNO', 'STATUS']);
            $values['STATUSKEY'] = $assignmentStatus['RECORDNO'] ?? null;
            if ( ! empty($values['STATUSKEY']) && ! empty($assignmentStatus['STATUS']) ) {
                $values['STATUS_ACTIVE'] = $assignmentStatus['STATUS'] == 'active';
            }
        }
        // Check if the constraints added use active assignments - technically UI won't let this happen, but better check
        if ( $ok && !empty($values['CONSTRAINTS']) ) {
            foreach ( $values['CONSTRAINTS'] as &$constraint ) {
                if ( $ok && ! empty($constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTNAME]) ) {
                    // then it comes from the assingment editor, and the assignmentkey is not fetched. Here the NAME actually represents the AssignmentID
                    $parentRecord = $this->getById(
                        $constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTNAME],
                        [ 'RECORDNO', 'PLANNEDSTARTDATE', 'PLANNEDENDDATE', 'STATUS' ]
                    );
                } else if ( $ok && ! empty($constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTKEY]) ) {
                    // the request comes from the checklist editor or from import or api
                    $parentRecord = $this->get(
                        $constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTKEY],
                        [ 'RECORDNO', 'PLANNEDSTARTDATE', 'PLANNEDENDDATE', 'STATUS' ]
                    );
                }

                if ( $ok && !empty($parentRecord) ) { // actually represents the ID
                    $parentRecordNo = $parentRecord['RECORDNO'] ?? null;
                    if ( !empty($parentRecord) && !empty($parentRecordNo) && $parentRecord['STATUS'] == 'active' ) {
                        $constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTKEY] = $parentRecordNo;
                        $constraint['PLANNEDSTARTDATE'] = $parentRecord['PLANNEDSTARTDATE'] ?? null;
                        $constraint['PLANNEDENDDATE'] = $parentRecord['PLANNEDENDDATE'] ?? null;
                    } elseif( $parentRecord['STATUS'] == 'inactive' ) {
                        Globals::$g->gErr->addIAError(
                            "CO-0838",
                            __FILE__ . ":" . __LINE__,
                            "Cannot use an inactive assignment in a constraint.", [],
                            sprintf("The selected assignment, '%s', is inactive.",
                                $constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTNAME]), ['CONSTRAINT_PARENT_ASSIGNMENTNAME'
                            => $constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTNAME]],
                            "Select an active assignment, then try again."
                        );
                        $ok = false;
                    } else {
                        Globals::$g->gErr->addIAError(
                            "CO-0839",
                            __FILE__ . ":" . __LINE__,
                            "The assignment does not exist.", [],
                            sprintf("The selected assignment, '%s', does not appear in this company.",
                                $constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTNAME]), ['CONSTRAINT_PARENT_ASSIGNMENTNAME'
                            => $constraint[WAssignmentConstraintsManager::PARENT_ASSIGNMENTNAME]],
                            "Select an existing assignment, then try again."
                        );
                        $ok = false;
                    }
                }
            }
        }

        return $ok;
    }

    /**
     * @param array      $values
     * @param bool       $transformPaths
     * @param array|null $transformed
     *
     * @return array
     */
    public function _PreProcessForUpdate($values, $transformPaths = false, &$transformed = null) : array
    {
        if ( ! isset($values['ASSIGNMENTID']) || isl_strlen($values['ASSIGNMENTID']) === 0 ) {
            $sequenceNo = (int) ($values['RECORDNO'] ?? $values[':RECORDNO']);
            $values['ASSIGNMENTID'] = $this->generateId($sequenceNo);
        }
        return parent::_PreProcessForUpdate($values, $transformPaths, $transformed);
    }

    /**
     * Validate the proecessed values. KEY values are built here based on the NAME values
     * If EM could not get keys from the given names, the KEY values are null.
     *
     * @param array $values
     *
     * @return bool
     */
   protected function ValidateRecord(&$values) : bool
   {
       $ok = true;

       if ( isset($values['STATUSNAME']) && isl_strlen($values['STATUSNAME']) > 0 && !isset($values['STATUSKEY']) ) {
           Globals::$g->gErr->addIAError('CO-0840',
                                       __FILE__ . ':' . __LINE__,
                                       'Status does not exist.', [],
                                       sprintf('The status you selected \'%s\', is not recognized in this company.',
                                           $values['STATUSNAME']), ['VALUES_STATUSNAME' => $values['STATUSNAME']],
                                       'Enter an existing status or create a new status, then try again.'
           );
           $ok = false;
       }

       if ( $ok && isset($values['STATUS_ACTIVE']) && $values['STATUS_ACTIVE'] === false ) {
           Globals::$g->gErr->addIAError('CO-0841',
                                       __FILE__ . ':' . __LINE__,
                                       'Cannot create an assignment using an inactive status.', [],
                                       sprintf('The status you selected, \'$s\', is inactive.', $values['STATUSNAME']), ['VALUES_STATUSNAME' => $values['STATUSNAME']],
                                       'Enter an active status then try again.'
           );
           $ok = false;
       }

       // validate dates
       if ( $ok &&  ( ! isset($values['PLANNEDSTARTDATE']) || isl_strlen($values['PLANNEDSTARTDATE']) === 0 ) ) {
           Globals::$g->gErr->addError(
               'CO-0842',
               __FILE__ . ':' . __LINE__,
               'The start date for this assignment is not set.',
               'Start Date (planned start date) field is empty.',
               'Enter a start date then try again.'
           );
           $ok = false;
       }

       if ( $ok &&  ( ! isset($values['PLANNEDENDDATE']) || isl_strlen($values['PLANNEDENDDATE']) === 0 ) ) {
           Globals::$g->gErr->addError(
               'CO-0843',
               __FILE__ . ':' . __LINE__,
               'The due date for this assignment is not set.',
               'Due Date (planned end date) field is empty.',
               'Enter a due date then try again.'
           );
           $ok = false;
       }

       $ok = $ok
             && WorkingchecklistUtils::validateAssignmentDates(
                 $values['PLANNEDSTARTDATE'],
                 $values['PLANNEDENDDATE'],
                 $values['ACTUALENDDATE'] ?? ''
           );

       // validate for API - where the the NAME is not used, only the KEY
       if ( $ok && isset($values['STATUSKEY']) && ! isset($values['STATUSNAME']) ) {
           $wAssignmentStatusManager = Globals::$g->gManagerFactory->getManager('wassignmentstatus');
           $status = $wAssignmentStatusManager->get($values['STATUSKEY'], ['RECORDNO']);
           $ok = ($status !== false && count($status) > 0);
       }

       /* validate dates by constraints
       - check if start date is above the minumum start date allowed
       - check if end date is below the maximum end date allowed
       */
       $ok = $ok && $this->areDatesWithinConstraintLimits($values);

       /*
        When the Start Date or the End Date is changing, and the assignment has Dependants,
            we must recalculate the dependents intervals.
        This assignment will not be validated if the Start Date or the Due date
            are creating time intervals that do not comply with the dependents stored values.
        In case of invalid data, the user must change the dependant's date first then come back and change this date, or choose another date here from the start
       */
       $ok = $ok && $this->areDependantsDatesUpToDate($values);

       return $ok && parent::ValidateRecord($values);
   }

    /**
     * Build the link between Assignment and Contact
     *
     * @param array $values
     *
     * @return bool
     */
    private function addAssignee(array $values) : bool
    {
        if ( isset($values['ASSIGNEE']) && isl_strlen($values['ASSIGNEE']) > 0 ) {
            $workingAssigneeManager = Globals::$g->gManagerFactory->getManager('workingassignee');
            $values = [
                'ASSIGNMENTKEY' => $values['RECORDNO'],
                'ASSIGNEE'   => $values['ASSIGNEE'],
            ];
            /** @var WorkingAssigneeManager $workingAssigneeManager */
            $ok = $workingAssigneeManager->regularAdd($values);
        } else {
            Globals::$g->gErr->addError("CO-0844",
                                        __FILE__ . ':' . __LINE__,
                                        "The Assignment has no assignee.","",
                                        "Please add an assignee."
            );
            $ok = false;
        }
        return $ok;
    }

    /**
     * Update the link between Assignment and Contact
     *
     * @param array $values
     *
     * @return bool
     */
    private function setAssignee(array $values) : bool
    {
        if ( isset($values['ASSIGNEE']) && isl_strlen($values['ASSIGNEE']) > 0 ) {
            /** @var WorkingAssigneeManager $workingAssigneeManager */
            $workingAssigneeManager = Globals::$g->gManagerFactory->getManager('workingassignee');
            // For now, we only support ManyToOne realtionship between Assginment and Contact.
            // getListByAssignmentKey will fetch only one record.
            $assigneeValues = $workingAssigneeManager->getListByAssignmentKey($values['RECORDNO'])[0];
            $assigneeValues['ASSIGNEE'] = $values['ASSIGNEE'];
            if ( count($assigneeValues) === 1 ) {
                // if somehow the user was not added, we cannot updated a non-existing record; add it now
                // very unlikely to happen, but better safe than sorry
                $assigneeValues['ASSIGNMENTKEY'] = $values['RECORDNO'];
                $ok = $workingAssigneeManager->regularAdd($assigneeValues);
            } else {
                $ok = $workingAssigneeManager->regularSet($assigneeValues);
            }
        } else {
            Globals::$g->gErr->addError("CO-0844",
                                        __FILE__ . ':' . __LINE__,
                                        "The Assignment has no assignee.",
                                        "",
                                        "Please add an assignee."
            );
            $ok = false;
        }
        return $ok;
    }

    /**
     * Enable audit trail in Working Assignments.
     * We can create them individually (having a parent is not mandatory)
     *
     * @return bool
     */
    public function IsAuditEnabled() : bool
    {
        return true;
    }

    /**
     * Send an email notification to the Assignment owner
     *
     * @param array $values
     *
     * @return bool True if the mail was delivered, false otherwise
     */
    public function SendNotifWhenAssigned($values)
    {
        $ok = false;

        if ( isset($values['STATUS']) && $values['STATUS'] == 'active' && !empty($values['ASSIGNEE']) ) {
            $contactMgr = Globals::$g->gManagerFactory->getManager('contact');
            $values['CONTACTKEY'] = (int) $contactMgr->GetRecordNoFromVid($values['ASSIGNEE']);
            if ( ! empty($values['CONTACTKEY']) && $this->IsChecklistNotificationEnabled($values['CONTACTKEY']) ) {
                $contact = $contactMgr->GetByRecordNo($values['CONTACTKEY']);
                if ( isset($contact['EMAIL1']) && $contact['EMAIL1'] != '' ) {
                    $to = $contact['EMAIL1'];
                    $assignment = $this->GetByRecordNo($values['RECORDNO']);

                    $from = GetValueForIACFGProperty("IA_COMPANYTITLE") . " Customer Support <noreply@"
                            . GetValueForIACFGProperty("IA_EMAILDOMAIN") . ">";

                    $emailToken = I18NEmailToken::buildFromResource('IA.EMAIL.ASSIGNMENTS.NOTIF_WHEN_ASSIGNED');

                    $subject = $emailToken->applyPlaceholders(
                        'subject.text',
                        []
                    );

                    $msgStart = $emailToken->applyPlaceholders(
                        'body.msgStart',
                        [
                            'ASSIGNEE'       => $values['ASSIGNEE'],
                        ]
                    );

                    if ( isset($values['CHECKLISTKEY']) && $values['CHECKLISTKEY'] != '' ) {
                        $wChecklistMgr = Globals::$g->gManagerFactory->getManager('workingchecklist');
                        $checklist = $wChecklistMgr->GetByRecordNo($values['CHECKLISTKEY']);

                        $msgBody = $emailToken->applyPlaceholders(
                            'body.msgBodyWithChecklist',
                            [
                                'ASSIGNMENTID'   => $assignment['ASSIGNMENTID'],
                                'ASSIGNMENTNAME' => $values['NAME'],
                                'CHECKLISTID'    => $checklist['CHECKLISTID'],
                                'CHECKLISTNAME'  => $checklist['NAME'],
                                'PLANNEDDATE'    => ReformatDate($values['PLANNEDENDDATE'], IADATE_STDFORMAT, GetUserDateFormat()),
                            ]
                        );
                    } else {
                        $msgBody = $emailToken->applyPlaceholders(
                            'body.msgBodyWithoutChecklist',
                            [
                                'ASSIGNMENTID'   => $assignment['ASSIGNMENTID'],
                                'ASSIGNMENTNAME' => $values['NAME'],
                                'PLANNEDDATE'    => ReformatDate($values['PLANNEDENDDATE'], IADATE_STDFORMAT, GetUserDateFormat()),
                            ]
                        );
                    }

                    $msgEnd = $emailToken->applyPlaceholders(
                        'body.msgEnd', []
                    );

                    $msg = $msgStart . $msgBody . $msgEnd;

                    $email = new IAEmail($to);
                    $email->contenttype = 'text/html; charset="' . isl_get_charset() . '"';
                    $email->_from = $from;
                    $email->subject = $subject;
                    $email->body = $msg;
                    $ok = $email->send();
                }
            }
        }


        return $ok;
    }

    /**
     * Send an email notification to the Checklist owner
     *
     * @param array $values
     *
     * @return bool True if the mail was delivered, false otherwise
     */
    public function SendNotifForChecklistOwner($values)
    {
        $ok = false;

        if ( isset($values['STATUS'])
             && $values['STATUS'] == 'active'
             && isset($values['CHECKLISTKEY'])
             && $values['CHECKLISTKEY'] !== ''
        ){
            $wChecklistMgr = Globals::$g->gManagerFactory->getManager('workingchecklist');
            $checklist = $wChecklistMgr->GetByRecordNo($values['CHECKLISTKEY']);
            if ( ! empty($checklist['CONTACTKEY']) && $this->IsChecklistNotificationEnabled($checklist['CONTACTKEY']) ) {
                $contactMgr = Globals::$g->gManagerFactory->getManager('contact');
                $contact = $contactMgr->GetByRecordNo($checklist['CONTACTKEY']);
                if ( isset($contact['EMAIL1']) && $contact['EMAIL1'] != '' ) {
                    $to = $contact['EMAIL1'];
                    $assignment = $this->GetByRecordNo($values['RECORDNO']);

                    $from = GetValueForIACFGProperty("IA_COMPANYTITLE") . " Customer Support <noreply@"
                            . GetValueForIACFGProperty("IA_EMAILDOMAIN") . ">";


                    $emailToken = I18NEmailToken::buildFromResource('IA.EMAIL.ASSIGNMENTS.NOTIF_FOR_CHECKLIST_OWNER');

                    $subject = $emailToken->applyPlaceholders(
                        'subject.text',
                        []
                    );

                    $body = $emailToken->applyPlaceholders(
                        'body.text',
                        [
                            'CONTACTNAME'    => $checklist['CONTACTNAME'],
                            'ASSIGNMENTID'   => $assignment['ASSIGNMENTID'],
                            'ASSIGNMENTNAME' => $values['NAME'],
                            'CHECKLISTID'    => $checklist['CHECKLISTID'],
                            'CHECKLISTNAME'  => $checklist['NAME'],
                        ]
                    );

                    $email = new IAEmail($to);
                    $email->contenttype = 'text/html; charset="' . isl_get_charset() . '"';
                    $email->_from = $from;
                    $email->subject = $subject;
                    $email->body = $body;
                    $ok = $email->send();
                }
            }
        }

        return $ok;
    }

    /**
     * @param int $contactKey
     *
     * @return bool
     */
    public function IsChecklistNotificationEnabled(int $contactKey) : bool
    {
        $cny = GetMyCompany();
        $userInfoMgr = Globals::$g->gManagerFactory->getManager('userinfo');

        $result = $userInfoMgr->DoQuery('QRY_USERINFO_SELECT_BY_CONTACTKEY',
                                        [ $cny, $contactKey ]);
        //if we have a user associated with the contact
        if ( isset($result) && $result != [] ) {
            $usserrec = $result[0]['RECORD#'];
            $userPrefMgr = Globals::$g->gManagerFactory->getManager("userpref");

            return $userPrefMgr->getChecklistNotificationFlag($cny, $usserrec);
        } else {
            return true;
        }
    }

    /**
     * List of fields used by the Checklist Editor for it's assignments.
     * It is the most accurate way to check if the grid items changed
     * Because FormEditor does not take into consideration empty fields, we could not accurately compare the new data with the old data.
     *
     * This list needs maintenance, to match with the paths used under ASSIGNMENTS in workingchecklist_form.xml
     *
     * @return array
     */
    protected function getFieldsUsedInChecklistEditor() : array
    {
        return array_merge(
            array_keys($this->GetCustomFields()),
            [
                'NAME', 'DESCRIPTION', 'PLANNEDSTARTDATE', 'PLANNEDENDDATE', 'ASSIGNEE', 'STATUSNAME',
                'PERCENTCOMPLETE', 'ACTUALENDDATE', 'SUPDOCID', 'COMMENT', 'DEPENDANTS', 'CONSTRAINTS',
            ]
        );
    }


    /**
     *      Did entries change? Are the entries now different than they were on disk (table)?
     *
     *
     * @param array             $values                 The document being saved
     * @param EntityManager     $ownerEntityManager     The Document Manager
     */
    public function didExistingAssignmentsChange(&$values, $ownerEntityManager)
    {
        if (!empty($values['EXISTING_ENTRIES']) && !empty($values['ASSIGNMENTS'])) {
            // Let our friend, the Matchmaker, help us out here
            Matchmaker::didExistingEntriesChange(
                $this,
                $values['EXISTING_ENTRIES'],
                $values['ASSIGNMENTS'],
                'RECORDNO',
                $ownerEntityManager,
                function($new, $old) {
                    // see if any assignment has changed - we will ignore LINE_NO for entries.
                    // If the order of the assignments has changed into a grid falls under the checklist rules influence.
                    $allFields = array_unique(array_merge(array_keys($new), $this->getFieldsUsedInChecklistEditor()));
                    $fieldsToSkip = ['LINE_NO', 'CONSTRAINTS', 'DEPENDANTS' ]; // For now, we do not allow editing the Constraints from the checklist editor
                    $fieldsToCompare = array_diff($allFields, $fieldsToSkip);

                    $areIdentical = true;
                    foreach ($fieldsToCompare as $fieldName) {
                        if ($fieldName == 'SUPDOCID') {
                            if (!empty($new['SUPDOCID'])) {
                                $newSupDocId = strstr($new['SUPDOCID'], "--", true);
                            } else {
                                $newSupDocId = '';
                            }
                            $areIdentical = ($newSupDocId  === ($old['SUPDOCID'] ?? ''));
                        } elseif ($fieldName === 'PERCENTCOMPLETE') {
                            $newPercent = isset($new[$fieldName]) ? (float)$new[$fieldName] : null;
                            $oldPercent = isset($old[$fieldName]) ? (float)$old[$fieldName] : null;
                            $areIdentical = ($newPercent === $oldPercent);
                        } else {
                            $areIdentical = ($new[$fieldName] ?? '') === ($old[$fieldName] ?? '');
                        }

                        if (!$areIdentical) {
                            break;
                        }
                    }

                    return $areIdentical;
                },
                true
            );
        }
    }

    /**
     * Detect if the current Assignment has the Start Date and the End Date within the boundaries set by the constraints
     *
     * @param array $values
     *
     * @return bool True if dates are within the boundaries, false otherwise
     */
    private function areDatesWithinConstraintLimits(array $values) : bool
    {
        $constraintsChecker = new WAssignmentConstraintsChecker($values);
        return $constraintsChecker->areDatesWithinConstraintsLimits();
    }

    /**
     * Detect if the new Start Date and End date are not impacting the dependant assignments.
     * If one dependant does not comply with the new constraint, then the user must modify the dependant first.
     *
     * @param array $values
     *
     * @return bool
     */
    private function areDependantsDatesUpToDate(array $values) : bool
    {
        $ok = true;
        if ( !empty($values['DEPENDANTS']) ) {
            if ( $values['STATUS'] == 'inactive' ) {
                $oldValues = $this->get($values['RECORDNO'], ['STATUS']);
                $oldStatus = $oldValues['STATUS'] ?? '';
                // If a user makes the current assignment inactive, then there is no point in recalculating the constraints for the dependants.
                // Just warn them and let them confirm by pressing Save again.
                // The dependants will not take in consideration this assignment anymore, until is reactivated again.
                if ( $oldStatus == 'active' && $this->shouldWarn(null, $values) ) {
                    Globals::$g->gErr->AddIAWarning('CO-0904', __FILE__ . ':' . __LINE__,
                        "This assignment has dependents. Are you sure you want to make this assignment inactive?"
                    );
                    $this->showRepeatMessage = true;
                    $ok = false;
                }
            } else {
                foreach ( $values['DEPENDANTS'] as $dependant ) {
                    if ( ! empty($dependant['ASSIGNMENTKEY']) ) {
                        if ( empty($dependant['CONSTRAINTS']) ) {
                            $dependant = $this->get($dependant['ASSIGNMENTKEY']);
                        }
                        $dependantConstraintChecker = new WAssignmentConstraintsChecker($dependant);
                        $dependantConstraintChecker->setIAmDependant($values);
                        $ok = $dependantConstraintChecker->areDatesWithinConstraintsLimits();
                        if ( ! $ok ) {
                            break;
                        }
                    }
                }
            }
        }
        return $ok;
    }

    /**
     * @return bool
     */
    public function getShowRepeatMessage() : bool
    {
        return $this->showRepeatMessage;
    }
}
