<?php
/**
 * The class builds SQL filters for the system views: Recently Viewed/Modified
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 Sage Intacct, All Rights Reserved
 */
class AuditAccessLogSystemView
{
    const SYSVIEW_RECENTLY_MODIFIED = 'RM';
    const SYSVIEW_ALL_RECENTLY_MODIFIED = 'ARM';
    const SYSVIEW_RECENTLY_VIEWED = 'RV';
    
    /** @var string $entity */
    private string $entity;
    
    /** @var string $objectType */
    private string $objectType;
    
    private bool $enabled;
    
    private string $releaseDate;
    
    /**
     * AuditAccessLogSystemView constructor.
     *
     * @param string $entity
     * @param string $objectType
     */
    public function __construct(string $entity, string $objectType)
    {
        $this->entity = $entity;
        $this->objectType = $objectType;
        $this->releaseDate = GetValueForIACFGProperty('AUDIT_TRAIL')['NEW_AUDIT_ACCESS_LOG_RELEASE_DATE'];
        $this->enabled = FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('NEW_AUDIT_ACCESS_LOG_ENABLED');
    }
    
    /**
     * @return bool
     */
    public function canBeUsed() : bool
    {
        return $this->enabled && strtotime($this->releaseDate . '+ 62 days') < strtotime('today');
    }
    
    /**
     * @param string $systemViewID
     *
     * @return array
     */
    public function buildQuery(string $systemViewID) : array
    {
        $auditSession = AuditTrailSession::getInstance();
        
        $auditMax = GetValueForIACFGProperty("AUDIT_VIEWS_LIMIT");
        $limitAuditRows ="ORDER BY accessTime desc ".
                         "FETCH FIRST " . $auditMax . " ROWS ONLY ";
        $auditTableName = $auditSession->getAuditAccessLogTable();
        
        $auditViewName = $auditTableName;
        
        if (IsMultiEntityCompany()) {
            $orders = " accessTime desc ";
        } else {
            $orders = " $auditViewName.accessTime desc ";
        }
        
        $refDate = date("m-d-y",strtotime('today - 63 days'));
        
        $userFilter = "";
        $inClause = " ";
        $systemQuery = [];
        switch ($systemViewID) {
            case self::SYSVIEW_RECENTLY_VIEWED:
                $userFilter = " and $auditTableName.userKey = ? ";
                $inClause = " and accessMode in (?, ?) and accessTime > TO_DATE( ? , 'MM/DD/YYYY HH24:MI:SS')";
                $systemQuery = [GetMyCompany(), GetMyUserid(), AuditAccessLog::ACCESS_MODE_ACCESS, AuditAccessLog::ACCESS_MODE_MODIFY, $refDate];
                break;
            case self::SYSVIEW_RECENTLY_MODIFIED:
                $userFilter = " and $auditTableName.userKey = ? ";
                $inClause = " and accessMode in (?, ?) and accessTime > TO_DATE( ? , 'MM/DD/YYYY HH24:MI:SS')";
                $systemQuery = [GetMyCompany(), GetMyUserid(), AuditAccessLog::ACCESS_MODE_CREATE, AuditAccessLog::ACCESS_MODE_MODIFY, $refDate];
                break;
            case self::SYSVIEW_ALL_RECENTLY_MODIFIED:
                $inClause = " and accessMode in (?, ?) and accessTime > TO_DATE( ? , 'MM/DD/YYYY HH24:MI:SS')";
                $systemQuery = [GetMyCompany(), AuditAccessLog::ACCESS_MODE_CREATE, AuditAccessLog::ACCESS_MODE_MODIFY, $refDate];
                break;
            default :
                break;
        }
        
        $objectFilter = "objectType = '$this->objectType'";
        
        $froms = " ( SELECT cny#, objectKey, max(accessTime) as accessTime ".
                 "FROM $auditTableName	" .
                 "WHERE cny# = ? $userFilter $inClause and $objectFilter GROUP BY cny#, objectKey " .
                 $limitAuditRows .
                 ") $auditViewName ";
        $systemFilterStr = " $this->entity.RECORD# = $auditViewName.objectKey ";
        
        $systemViewArr = array (
            'systemfroms' => $froms,
            'systemfilters' => $systemFilterStr,
            'systemorders' => $orders,
            'systemViewName' => $auditViewName,
        );
        
       return [
            'query' => $systemViewArr,
            'args' => $systemQuery,
        ];
    }
}