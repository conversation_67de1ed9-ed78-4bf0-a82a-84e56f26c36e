<?xml version="1.0" encoding="ISO-8859-1"?>
<ROOT>
    <entity>supportingdocuments</entity>
    <title>IA.ATTACHMENT</title>

    <view system="true">
        <events>
            <load>attachmentListener();</load>
        </events>
        <pages>
            <page id="supdocpage">
                <title>IA.ATTACHMENT</title>
                <section columnCount="3">
                    <field>
                        <path>DOCUMENTID</path>
                    </field>
                    <field>
                        <path>DOCUMENTNAME</path>
                    </field>
                    <field>
                        <path>GROUPKEY</path>
                    </field>
                    <field>
                        <path>DESCRIPTION</path>
                    </field>
                    <field>
                        <path>CREATED</path>
                    </field>
                    <field>
                        <path>CREATEDBY</path>
                    </field>
                </section>
                <section>
                    <row id="uploadButtonRow" >
                        <field viewAllowed="false" path="ATTACHFILES">
                            <type type='upload' ptype='file'></type>
                        </field>
                        <sentencerow id="uploadInputGroup" hasAssistedFields="true">
                            <button label="IA.BROWSE" id="uploadButton" bsize="full">
                                <name>IA.BROWSE</name>
                                <events>
                                    <click>attachmentUpload();</click>
                                </events>
                            </button>
                            <field assistFieldText="IA.DRAG_AND_DROP" assistPos="right">
                            </field>
                        </sentencerow>
                    </row>
                    <grid clazz="AttachmentsGrid" noDragDrop="true" noPagination="true" showDelete="true">
                        <path>ATTACHMENTS</path>
                        <column>
                            <field viewAllowed="true" userUIControl="AttachmentFileName">
                                <path>NAME</path>
                            </field>
                        </column>
                        <column className="right">
                            <field>
                                <path>SIZE</path>
                            </field>
                        </column>
                        <column className="center">
                            <field className="uploadprogress">
                                <path>PROGRESS</path>
                            </field>
                        </column>
                    </grid>
                </section>
            </page>
        </pages>
    </view>
</ROOT>
