<?php
/**
 * Class definition for EEDetailGetAllBatch object
 *
 * <AUTHOR> Rusu <<EMAIL>>
 * @copyright 2015 Intacct Corporation All, Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 */

/**
 * Class EEDetailGetAllBatch
 * Created by PhpStorm.
 * Date: 6/8/2015
 * Time: 4:00 PM
 */
class EEDetailGetAllBatch extends UnionBaseBatch
{
    /**
     * @param string  $entity
     * @param int     $readTimestamp
     * @param int     $pageSize
     * @param array   $params
     */
    function __construct( $entity, $readTimestamp, $pageSize, $params = array() )
    {
        $this->configurationArray = UnionIteratorConfigurator::getBatchConfigurationArray()['eedetail'];
        parent::__construct($entity, $readTimestamp, $pageSize, $params);
    }

    /**
     * getReportTypeBatch
     *
     * @param array $params
     *
     * @return DetailGetAllReportTypeBatch
     */
    protected function getReportTypeBatch( $params )
    {
        if ( DDS_DEBUG == 1 ) {
            impp('EEDetailGetAllReportTypeBatch', '');
        }
        return new DetailGetAllReportTypeBatch('eedetail', $this->getReadTimestamp(), $this->getPageSize(), $params);
    }

}