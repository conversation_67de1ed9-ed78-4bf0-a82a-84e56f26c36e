<?php
$kSchemas['expensereport'] = array(
    'object' => array(
        'RECORDKEY',
        'COMMENTS',
    ),
    'schema' => array (
        'RECORDKEY' => 'recordkey',
        'COMMENTS' => 'comments',
    ),
    'fieldinfo' => array (
        array (
            'path' => 'RECORDKEY',
            'desc' => 'IA.EXPENSE_RECORD_NUMBER',
            'fullname' => 'IA.EXPENSE_RECORD_NUMBER',
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => '/^[0-9]{0,8}$/',
            ),
            'id' => 1
        ),

        array (
            'path' => 'COMMENTS',
            'desc' => 'IA.COMMENTS',
            'fullname' => 'IA.COMMENTS',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'fastUpdate'  => true,
            'id' => 2
        ),
    ),
    'table' => 'expensereport',
    'fastUpdate'  => true,
    'vid' => 'RECORDKEY',
    'module' => 'ee'
);

