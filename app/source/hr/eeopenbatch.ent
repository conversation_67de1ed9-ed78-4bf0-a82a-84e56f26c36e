<?
/**
 *	FILE:			eeopenbatch.ent
 *	AUTHOR:			<PERSON><PERSON>
 *	DESCRIPTION:	entity for eeopenbatch, extends from eebatch.
 *
 *	(C) 2000, Intacct Corporation, All Rights Reserved
 *
 *	This document contains trade secret data that belongs to Intacct
 *	Corporation and is protected by the copyright laws.  Information
 *	herein may not be used, copied or disclosed in whole or in part
 *	without prior written consent from Intacct Corporation.
 */
require('eebatch.ent');
$kSchemas['eeopenbatch'] = $kSchemas['eebatch'];
$kSchemas['eeopenbatch']['dbfilters'] = array(
	array('eeopenbatch.recordtype', 'in', array('ei' , 'ea')),
	array('eeopenbatch.open', '=', 'T')
);
$kSchemas['eeopenbatch']['printas'] = 'IA.SUMMARY';
$kSchemas['eeopenbatch']['pluralprintas'] = 'IA.SUMMARIES';
