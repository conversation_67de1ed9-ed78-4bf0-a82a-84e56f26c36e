<?php
/**
 * ExpensesApproval entity Manager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation, All Rights Reserved
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

import('ApprovalManager');
import('ManagerFactory');

/**
 * This class will handle all the document approval process, including submitting the document to approver, approving the document,
 * declining a document and notifying the approver.
 */
class ExpensesApprovalManager extends ApprovalManager
{
    /* @var bool $approveEmailsSent */
    private $approveEmailsSent;
    /* @var bool $confirmationEmailSent */
    private $confirmationEmailSent;

    /**
     * @param array $params initialization parameters
     */
    public function __construct($params = array()) 
    {
        parent::__construct($params);
    }

    /**
     * Update the current approval rule with the approve/decline status
     *
     * @param array $docvalues object data
     *
     * @return bool true on success, false on error
     */
    protected function updateCurrentApprovalRule(&$docvalues)
    {
        $res = array();
        
        // If we decline let's check if the user has already approved the document
        if ( $docvalues['REVIEWACTION'] == 'R' ) {
            $res = $this->getLastApprovedRule($docvalues);
        }
        
        // Get the submitted rule for the document 
        if ( empty($res) ) {
            $res = $this->getLastSubmittedRule($docvalues);
        }

        $docvalues['CURRENTRULE'] = $res;

        $updateRuleStmt = " UPDATE approvalhistory SET approvedbykey = :1, eventdate = :2, comments = :3, state = :4 WHERE record# = :5  AND cny# = :6 ";
        $ok = ExecStmt(
            array (
            $updateRuleStmt,
            $this->userid,
            GetCurrentDate(),
            $docvalues['REVIEWCOMMENTS'],
            $docvalues['REVIEWACTION'],
            $res['RECORD#'],
            $this->cny
            )
        );

        return $ok;
    }

    /**
     * getLastSubmittedRule - get submitted rule for the current document
     *
     * @param array $docvalues document data
     *
     * @return array query results from approval history table
     */
    private function getLastSubmittedRule($docvalues)
    {
        $qry = " select record#, prrecordkey, prentrykey, approval_stage, approval_type, approval_level, approverkey, state from approvalhistory 
            where cny# = :1 and prrecordkey = :2 and prentrykey = :3 and state = :4 ";
        
        $res = QueryResult(array($qry, $this->cny, $docvalues['RECORDKEY'], $docvalues['LINE_NO'], STATE_SUBMIT));

        return $res[0];
    }

    /**
     * getLastApprovedRule - query for last approved rule of the document for the current user
     *
     * @param array $docvalues object data
     *
     * @return array query result
     */
    private function getLastApprovedRule($docvalues)
    {
        $qry = " select record#, prrecordkey, prentrykey, approval_stage, approval_type, approval_level, approverkey, state from approvalhistory 
            where cny# = :1 and prrecordkey = :2 and prentrykey = :3 and state = :4 and approvedbykey = :5 order by record# desc";
        
        $res = QueryResult(array($qry, $this->cny, $docvalues['RECORDKEY'], $docvalues['LINE_NO'], STATE_APPROVED, $this->userid));
        
        return $res[0];
    }

    /**
     * getNextApprovalRule - query for the next approval rule in the approval chain
     *
     * @param array $docvalues document data
     *
     * @return array|false query result or false if none
     */
    protected function getNextApprovalRule($docvalues)
    {

        $nextRuleStmt = " select record#, approval_type, approverkey, approval_level, approval_stage, state from approvalhistory
                            where cny# = :1
                            and prrecordkey = :2
                            and prentrykey = :3
                            and state = :4
                            and record# = 
                            (select min(record#) from approvalhistory where cny# = :1 and prrecordkey = :2 and prentrykey = :3 and state = :4) ";

        $res = QueryResult(array($nextRuleStmt, $this->cny, $docvalues['RECORDKEY'], $docvalues['LINE_NO'], STATE_PENDING));

        if (isset($res)) {
            return $res[0];
        } else {
            return false;
        }
    }

    /**
     * For Recall we can just clear all existing approval rules. There are no approval rule in Draft state
     *
     * @param array $docvalues object data
     *
     * @return bool true on success, false on error
    */
    public function recallDocument(&$docvalues)
    {
        $ok = $this->clearExistingApprovalRules($docvalues);
        return $ok;
    }

    /**
     * Prepare the values for storing the approval rule in the approval workflow.
     *
     * @param array  $values        approval object data
     * @param string $stage         approval stage
     * @param int    $level         approval level
     * @param string $type          approval type
     * @param string $approverid    userid of the approver or the delgate
     * @param string $approverTitle title of user
     * @param int    $approverNum   approver number when there are multiple approvers in the same level
     * @param string $objectID      the std object we are subjecting to approval like TD, PRRECORD, Vendor etc
     * @param string $onDelegation  'true' if a delegate is approving instead of the original approver, 'false' otherwise
     * @param string $delegateId    userid of the original approver who got delegated
     * @param string $approverGroupId    approval user group ID
     *
     * @return array containing all necessary info for approval
     */
    protected function PrepValues($values, $stage, $level, $type, $approverid = '', $approverTitle = '', $approverNum = 1, $objectID = '', $onDelegation = 'false', $delegateId = '', $approverGroupId = '')
    {
        $approvalValues = parent::PrepValues($values, $stage, $level, $type, $approverid, $approverTitle, $approverNum, $objectID, $onDelegation, $delegateId);
        $approvalValues['PRRECORDKEY'] = $values['RECORDKEY'];
        $approvalValues['PRENTRYKEY'] = $values['LINE_NO'];
        return $approvalValues;
    }

    /**
     * isAdminApprover - is the specified user the admin approver?
     *
     * @param int $userid user record number
     *
     * @return bool
     */
    protected function isAdminApprover($userid)
    {
        return EESetupManager::isAdminApprover($userid);
    }

    /**
     * hasNextApprovalRule - query if the document approval chain has next approval rule. Is this the last rule?
     *
     * @param array $docvalues document data
     *
     * @return bool true if approval found, false otherwise
     */
    protected function hasNextApprovalRule($docvalues)
    {
        $nextRuleStmt = " select record#, approval_type, approverkey, approval_level, approval_stage, state from approvalhistory
                            where cny# = :1
                            and prrecordkey = :2
                            and prentrykey = :3
                            and state = :4 ";

        $res = QueryResult(array($nextRuleStmt, $this->cny, $docvalues['RECORDKEY'], $docvalues['LINE_NO'], STATE_SUBMIT));

        if (isset($res)) {
            return $res[0];
        } else {
            return false;
        }
    }

    /**
     * disableApprovalRules - mark all the approval rules for the document as declined
     *
     * @param array $docvalues document data
     *
     * @return bool true if approval found, false otherwise
     */
    protected function disableApprovalRules(&$docvalues)
    {
        $ok = true;

        // let's get the previous approvers before we delete the data !
        if ( $this->isEmailNotificationsEnabled() ) {
            $approvers = $this->getPreviousApprovers($docvalues);
            $docvalues['PREVIOUSAPPROVERS'] = $approvers;
        }

        $args = array ($this->cny, $docvalues['RECORDKEY'], $docvalues['LINE_NO'], STATE_SUBMIT, STATE_APPROVED, STATE_PENDING);
        $ok = $ok && $this->DoQuery('QRY_APPROVAL_DELETE_STATE_BY_DOCKEY', $args);
         
        $docvalues['APPROVAL_STATE'] = STATE_DECLINED;

        return $ok;
    }

    /**
     * notifySubmitter
     *
     * @param array  $values approval information
     * @param string $action action being performed
     */
    public function notifySubmitter($values, $action)
    {
        global $gManagerFactory;
        [$employeeID] = explode('--', $values['EMPLOYEEID']);
        $userInfoMgr = $gManagerFactory->getManager('userinfo');
        
        $userRecord = $userInfoMgr->DoQuery('QRY_USERINFO_FROM_EMPLOYEEID', array($employeeID, $this->cny, $this->cny, $this->cny));
        if ( ! $userRecord[0] || ! $userRecord[0]['RECORD#'] ) { 
            return;
        }
        $values['CREATEDUSER'] = $userRecord[0]['RECORD#'];
        parent::NotifySubmitter($values, $action);
    }

    /**
     * notifyPreviousApprovers - notify approver users that are at an higher stage level
     *
     * @param array  $values timesheetapproval object data
     * @param string $action approval action taken
     *
     * @return bool true on success, false on error
     */
    public function notifyPreviousApprovers($values, $action)
    {
        if ( $this->isEmailNotificationsEnabled() ) {
            foreach ( $values['PREVIOUSAPPROVERS'] as $approverContact ) {
                $this->sendActionNotification($values, null, $approverContact, $action);
            }
        }

        return true;
    }

    /**
     * notifyFirstApprover
     *
     * @param array  $values   timesheetapproval object data
     * @param array  $approver user id of approver
     * @param string $action   operation being performed
     */
    public function notifyFirstApprover($values, $approver, $action)
    {
        if ( $this->isEmailNotificationsEnabled() ) {
            $this->sendActionNotification($values, null, $approver, $action);
        }
    }

    /**
     * getFirstApprover - get the first approver of the document
     *
     * @param array $values document data
     *
     * @return array contact data for approver
     */
    public function getFirstApprover($values)
    {
        // Get the approver key
        $filters = array(
        'selects' => array('APPROVERKEY'),
        'filters' => array (
        array (
        array('PRRECORDKEY', '=', $values['RECORDKEY']),
        array('PRENTRYKEY', '=', $values['LINE_NO']),
        array('STATE', '=', 'Submitted'),
        ),
        )
        );
        $res = $this->GetList($filters);

        // Get the contact info of the approver
        $this->GetUserContactObj($res[0]['APPROVERKEY'], $userObj, $contactObj, true);

        return $contactObj;
    }

    /**
     * getPreviousApprovers - get the contact list of approvers that are at an higher stage that the current rule
     *
     * @param array $values object data
     *
     * @return array
     */
    private function getPreviousApprovers($values)
    {
        // get the list of approver keys
        $filters = array(
            'selects' => array('APPROVEDBYKEY'),
            'filters' => array (
                array (
                    array('PRRECORDKEY', '=', $values['RECORDKEY']),
                    array('PRENTRYKEY', '=', $values['LINE_NO']),
                    array('STATE', '=', 'Approved'),
                    array('APPROVAL_STAGE', '>=', $values['CURRENTRULE']['APPROVAL_STAGE']),
                ),
            )
        );
        $res = $this->GetList($filters);

        // Get the contact object
        $contactList = array();
        foreach ( $res as $approverObj ) {
            $this->GetUserContactObj($approverObj['APPROVEDBYKEY'], $userObj, $contactObj, true);
            $contactList[] = $contactObj;
        }
        
        return $contactList;
    }

    /**
     * getApprovalPreferences - fetch approval preferences from expenses setup
     *
     * @param array $values object data (unused)
     *
     * @return array approval preferences or empty if not found
     */
    protected function getApprovalPreferences($values)
    {
        $approvals = EESetupManager::getEEApprovalPreferences();
        return $approvals;
    }

    /**
     * isEmailNotificationsEnabled
     *
     * @return bool
     */
    public function isEmailNotificationsEnabled()
    {
        return EESetupManager::isEmailNotificationsEnabled();
    }

    /**
     * clearExistingApprovalRules
     *
     * @param array $values document data
     *
     * @return bool true on success, false on error
     */
    public function clearExistingApprovalRules($values)
    {
        $args = array ( $this->cny, $values['RECORDKEY'], $values['LINE_NO'], STATE_DECLINED );
        $ok = $this->DoQuery('QRY_APPROVAL_DELETE_BY_DOCKEY', $args);
        
        return $ok;
    }

    /**
     * userDocApprovalPermission - used for Value Approval type, which is unsupported. override to ignore
     * Get the current user's document approval permission levels and check if the user has permission for the particular level.
     *
     * @param int $dochdrkey unused
     *
     * @return bool false - unsupported
     */
    public function userDocApprovalPermission(/** @noinspection PhpUnusedParameterInspection */ $dochdrkey)
    {
        return false;
    }

    /**
     * getDollarLevelApprovalPrefs - get the approval preferences from the amount of the transaction
     *
     * @param array $values object data
     *
     * @return array approval info structure
     */
    protected function getDollarLevelApprovalPrefs($values)
    {
        // need to use Value Approval Amount
        if (isset($values['VALUEAPPROVAL_AMOUNT'])) {
            return EESetupManager::getDollarLevelApprovalPrefs($values['VALUEAPPROVAL_AMOUNT']);
        }
        return EESetupManager::getDollarLevelApprovalPrefs($values['TRX_AMOUNT']);
    }

    /**
     * getValueApprovalTypeName
     *
     * @return string
     */
    protected function getValueApprovalTypeName()
    {
        return APPTYPE_APPROVAL_LEVEL;
    }

    /**
     *  getValueLevelUsers - get list of approvers for specified approval level
     *
     * @param string $level approval level in question
     *
     * @return string[][]|false user info, or false if error
     */
    protected function getValueLevelUsers($level)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $approverPickMgr = $gManagerFactory->getManager('eeapproverpick');
        return $approverPickMgr->getAuthorizedValueLevelUsers('Level'.$level);
    }

    /** 
     *  hasApprovalLevelPerm - check if the user has approval level permission for the particular level
     *
     * @param int $level approval level in question
     *
     * @return bool
     */
    protected function hasApprovalLevelPerm($level)
    {
        $userPerm = $this->getUserApprovalLevels();
        if ( count($userPerm) > 0 && in_array($level, $userPerm) ) {
            return true;
        }
        return false;        
    }

    /** 
     * getUserApprovalLevelsStr - say 1,3,4 meaning, Level 1 -> 1 approver, Level 2 -> 3 approvers etc ...
     *
     * @return string|true
     */
    public function getUserApprovalLevelsStr()
    {
        $userPerm = $this->getUserApprovalLevels();
        $userPermStr = implode(',', $userPerm);
        return $userPermStr;        
    }

    /**
     * getUserApprovalLevels - user Value Level approval permissions. permissions page Level 1, Level 2 etc...
     *
     * @return array
     */
    private function getUserApprovalLevels()
    {
        $opKey = "ee/activities/eeapprovallevels/level";
        $userPerm = array();

        for ( $i = 1 ; $i <= APPROVAL_LEVELS ; $i++ ) {
            $opID = GetOperationId($opKey . $i);
            if ( IsOperationAllowed($opID) ) {
                $userPerm[] = $i;
            }
        }

        return $userPerm;
    }

    /**
     * hasApprovedStage - check that the same person does not approve the same stage twice
     *
     * @param array $docvalues document data
     *
     * @return bool true if approval found, false otherwise
     */
    protected function hasApprovedStage($docvalues)
    {     
        $qry = "select count(*) as cou from approvalhistory where cny# = :1 and prrecordkey = :2 and prentrykey = :3 and approval_type = '" 
            . $this->getValueApprovalTypeName() . "' and state = :4 and approvedbykey = :5 ";
        $result = QueryResult(array($qry, $this->cny, $docvalues['RECORDKEY'], $docvalues['LINE_NO'], STATE_APPROVED, $this->userid));

        if ( isset($result) && $result[0]['COU'] > 0 ) {
            return true;
        }
        return false;
    }

    /**
     * buildLandPageOp - format approvetimesheet operation for inclusion in URL
     *
     * @return string to append to URL representing the approvetimesheet operation
     */
    protected function buildLandPageOp()
    {
        $approvelistop = GetOperationId('ee/activities/approveexpenses');
        return "fo=ee&.navop=".$approvelistop;
    }

    /**
     * sendActionNotification
     *
     * @param array  $values         timesheetapproval object data
     * @param string $approveruserid user id of approver
     * @param array  $contactObj     contact object data
     * @param string $action         operation being performed
     *
     * @return bool true on success, false on error
     */
    public function sendActionNotification($values, $approveruserid, $contactObj, $action)
    {
        // send only one confirmation email per timesheet and action
        if ( $this->confirmationEmailSent && $this->confirmationEmailSent[$action] && $this->confirmationEmailSent[$action][$values['RECORDKEY']] ) { 
            return true; 
        }
        
        $this->confirmationEmailSent[$action][$values['RECORDKEY']] = true;
        
        return parent::SendActionNotification($values, $approveruserid, $contactObj, $action);
    }

    /**
     * sendApprovalNotification
     *
     * @param array  $values             timesheetapproval object data
     * @param array  $contactObj         contact object data
     * @param string $approveruserid     user id of approver
     * @param int    $approverUserRecNum record# of approver
     * @param array  $rule               unused for timesheet approvals
     *
     * @return bool true on success, false on error
     */
    public function sendApprovalNotification($values, $contactObj, $approveruserid, $approverUserRecNum, $rule)
    {
        // send only one approve email per timesheet and approver
        if ( $this->approveEmailsSent && $this->approveEmailsSent[$approveruserid] && $this->approveEmailsSent[$approveruserid][$values['PRRECORDKEY']] ) { 
            return true; 
        }
        
        $this->approveEmailsSent[$approveruserid][$values['PRRECORDKEY']] = true;
        
        return parent::SendApprovalNotification($values, $contactObj, $approveruserid, $approverUserRecNum, $rule);
    }

    /**
     * buildApprovalMessage - construct complete HTML email message notifying approver of timesheet ready for attention
     *
     * @param array   $values             timesheet data
     * @param array   $approveruserid     unused
     * @param int $approverUserRecNum unused
     * @param array   $contactObj         contact information
     * @param string  $url                url for viewing timesheet
     * @param array   $rule               unused
     *
     * @return string message
     */
    protected function buildApprovalMessage($values, $approveruserid, $approverUserRecNum, $contactObj, $url, $rule)
    {
        $firstName = $contactObj['FIRSTNAME'];
        $fullName = $values['PRRECORD']['FULLEMPLOYEENAME'];
        $recordId = $values['PRRECORD']['RECORDID'];
        $description = $values['PRRECORD']['DESCRIPTION'];
        $companyName = GetMyCompanyName();
        $baseTotal = glFormatCurrency($values['PRRECORD']['TOTALDUE']);
        $baseCurrency = $values['PRRECORD']['BASECURR'];
        $emailToken = $this->getI18NEmailTokenObject(NOTIFY_APPROVER);
        if ($this->ismcpEnabled) {
            $trxCurrency = $values['CURRENCY'];
            $trxAmount = $values['PRRECORD']['TRX_TOTALDUE'];
            $body = $emailToken->applyPlaceholders(
                'body.mcpText',
                [
                    'FIRST_NAME' => $firstName,
                    'FULL_NAME' => $fullName,
                    'RECORD_ID' => $recordId,
                    'BASE_TOTAL' => $baseTotal,
                    'DESCRIPTION' => $description,
                    'REVIEW_COMMENTS' => $baseCurrency,
                    'COMPANY_NAME' => $companyName,
                    'CURRENCY' => $baseCurrency,
                    'TRX_CURRENCY' => $trxCurrency,
                    'TRX_AMOUNT' => $trxAmount,
                    'URL' => $url
                ]
            );
        } else {
            $body = $emailToken->applyPlaceholders(
                'body.text',
                [
                    'FIRST_NAME' => $firstName,
                    'FULL_NAME' => $fullName,
                    'RECORD_ID' => $recordId,
                    'BASE_TOTAL' => $baseTotal,
                    'DESCRIPTION' => $description,
                    'REVIEW_COMMENTS' => $baseCurrency,
                    'COMPANY_NAME' => $companyName,
                    'CURRENCY' => $baseCurrency,
                    'URL' => $url
                ]
            );
        }
        return $body;
    }

    /**
     * buildApprovalSubject - construct submittal message to be sent to contact
     *
     * @param array $values     timesheet data
     * @param array $contactObj person to contact
     *
     * @return string message
     */
    protected function buildApprovalSubject($values, $contactObj)
    {
        $emailToken = $this->getI18NEmailTokenObject(NOTIFY_APPROVER);
        $subject = $emailToken->applyPlaceholders(
            'subject.text',
            []
        );
        return $subject;
    }

    /**
     * buildActionMessage - construct appropriate message to be sent to contact
     *
     * @param array  $values         timesheet data
     * @param string $approveruserid user id of timesheet approver
     * @param array  $contactObj     person to contact
     * @param string $action         action that was performed
     *
     * @return string message
     */
    protected function buildActionMessage($values, $approveruserid, $contactObj, $action)
    {
        $emailToken = $this->getI18NEmailTokenObject(APPROVER_ACTION);
        $firstName = $contactObj['FIRSTNAME'];
        $recordId = '';
        if ( isset($values['PRRECORD']['RECORDID']) && $values['PRRECORD']['RECORDID'] != '' ) {
            $recordId = $values['PRRECORD']['RECORDID'];
        }

        $fullName = $values['PRRECORD']['FULLEMPLOYEENAME'];
        $reviewComments = $values['REVIEWCOMMENTS'];
        $companyName = GetMyCompanyName();
        $description = $values['PRRECORD']['DESCRIPTION'];
        $baseAmount = glFormatCurrency($values['PRRECORD']['TOTALDUE']);
        $baseCurrency = $values['PRRECORD']['BASECURR'];
        if ($this->ismcpEnabled) {
            $trxAmount = glFormatCurrency($values['PRRECORD']['TRX_TOTALDUE']);
            $currency = $values['PRRECORD']['CURRENCY'];
            if ( $action === 'approvaldeclined' ) {
                $body = $emailToken->applyPlaceholders(
                    'body.mcpApprovalDeclinedText',
                    [
                        'FIRST_NAME' => $firstName,
                        'RECORD_ID' => $recordId,
                        'FULL_NAME' => $fullName,
                        'BASE_CURRENCY' => $baseCurrency,
                        'BASE_AMOUNT' => $baseAmount,
                        'DESCRIPTION' => $description,
                        'REVIEW_COMMENTS' => $reviewComments,
                        'COMPANY_NAME' => $companyName,
                        'TRX_AMOUNT' => $trxAmount,
                        'CURRENCY' => $currency
                    ]
                );
            } else if ( $action === 'recall' ) {
                $body = $emailToken->applyPlaceholders(
                    'body.mcpRecallText',
                    [
                        'FIRST_NAME' => $firstName,
                        'RECORD_ID' => $recordId,
                        'FULL_NAME' => $fullName,
                        'BASE_CURRENCY' => $baseCurrency,
                        'BASE_AMOUNT' => $baseAmount,
                        'DESCRIPTION' => $description,
                        'COMPANY_NAME' => $companyName,
                        'TRX_AMOUNT' => $trxAmount,
                        'CURRENCY' => $currency
                    ]
                );
            } else if ( $action === 'declined' ) {
                $body = $emailToken->applyPlaceholders(
                    'body.mcpDeclinedText',
                    [
                        'FIRST_NAME' => $firstName,
                        'RECORD_ID' => $recordId,
                        'BASE_CURRENCY' => $baseCurrency,
                        'BASE_AMOUNT' => $baseAmount,
                        'DESCRIPTION' => $description,
                        'REVIEW_COMMENTS' => $reviewComments,
                        'COMPANY_NAME' => $companyName,
                        'TRX_AMOUNT' => $trxAmount,
                        'CURRENCY' => $currency
                    ]
                );
            } else {
                $body = $emailToken->applyPlaceholders(
                    'body.mcpDefaultText',
                    [
                        'FIRST_NAME' => $firstName,
                        'RECORD_ID' => $recordId,
                        'BASE_CURRENCY' => $baseCurrency,
                        'BASE_AMOUNT' => $baseAmount,
                        'DESCRIPTION' => $description,
                        'REVIEW_COMMENTS' => $reviewComments,
                        'COMPANY_NAME' => $companyName,
                        'TRX_AMOUNT' => $trxAmount,
                        'CURRENCY' => $currency
                    ]
                );
            }

        } else {
        if ( $action === 'approvaldeclined' ) {
            $body = $emailToken->applyPlaceholders(
                'body.approvalDeclinedText',
                [
                    'FIRST_NAME' => $firstName,
                    'RECORD_ID' => $recordId,
                    'FULL_NAME' => $fullName,
                    'BASE_CURRENCY' => $baseCurrency,
                    'BASE_AMOUNT' => $baseAmount,
                    'DESCRIPTION' => $description,
                    'REVIEW_COMMENTS' => $reviewComments,
                    'COMPANY_NAME' => $companyName
                ]
            );
        } else if ( $action === 'recall' ) {
        $body = $emailToken->applyPlaceholders(
                'body.recallText',
                [
                    'FIRST_NAME' => $firstName,
                    'RECORD_ID' => $recordId,
                    'FULL_NAME' => $fullName,
                    'BASE_CURRENCY' => $baseCurrency,
                    'BASE_AMOUNT' => $baseAmount,
                    'DESCRIPTION' => $description,
                    'COMPANY_NAME' => $companyName
                ]
            );
        } else if ( $action === 'declined' ) {
            $body = $emailToken->applyPlaceholders(
                'body.declinedText',
                [
                    'FIRST_NAME' => $firstName,
                    'RECORD_ID' => $recordId,
                    'BASE_CURRENCY' => $baseCurrency,
                    'BASE_AMOUNT' => $baseAmount,
                    'DESCRIPTION' => $description,
                    'REVIEW_COMMENTS' => $reviewComments,
                    'COMPANY_NAME' => $companyName
                ]
            );
        } else {
            $body = $emailToken->applyPlaceholders(
                'body.defaultText',
                [
                    'FIRST_NAME' => $firstName,
                    'RECORD_ID' => $recordId,
                    'BASE_CURRENCY' => $baseCurrency,
                    'BASE_AMOUNT' => $baseAmount,
                    'DESCRIPTION' => $description,
                    'REVIEW_COMMENTS' => $reviewComments,
                    'COMPANY_NAME' => $companyName
                ]
            );
        }
        }

        return $body;
    }

    /**
     * buildActionSubject - construct action message to be sent to contact
     *
     * @param array  $values     timesheet data
     * @param array  $contactObj person to contact
     * @param string $action     action that was performed
     *
     * @return string message
     */
    protected function buildActionSubject($values, $contactObj, $action)
    {
        $emailToken = $this->getI18NEmailTokenObject(APPROVER_ACTION);
        if ( $action === 'approvaldeclined' ) {
            $str = $this->buildApprovalDeclinedActionSubject($values, $contactObj, $action, $emailToken);
        } else if ( $action === 'recall' ) {
            $str = $this->buildRecallActionSubject($values, $contactObj, $action, $emailToken);
        } else {
            $str = $this->buildDefaultActionSubject($values, $contactObj, $action, $emailToken);
        }
        return $str;
    }

    /**
     * buildApprovalDeclinedActionSubject - construct approval decline action message to be sent to contact
     *
     * @param array  $values     timesheet data
     * @param array  $contactObj person to contact
     * @param string $action     action that was performed
     * @param I18NEmailToken $emailToken
     *
     * @return string message
     */
    private function buildApprovalDeclinedActionSubject(
        /** @noinspection PhpUnusedParameterInspection */ $values,
        /** @noinspection PhpUnusedParameterInspection */ $contactObj,
        /** @noinspection PhpUnusedParameterInspection */ $action, I18NEmailToken $emailToken)
    {
        $subject = $emailToken->applyPlaceholders(
            'subject.text',
            []
        );
        return $subject;
    }

    /**
     * buildRecallActionSubject - construct recall action message to be sent to contact
     *
     * @param array  $values     timesheet data
     * @param array  $contactObj person to contact
     * @param string $action     action that was performed
     * @param I18NEmailToken $emailToken
     *
     * @return string message
     */
    private function buildRecallActionSubject($values,
        /** @noinspection PhpUnusedParameterInspection */ $contactObj,
        /** @noinspection PhpUnusedParameterInspection */ $action, I18NEmailToken $emailToken)
    {
        $recordId = '';
        // Add the report number is exists
        if ( isset($values['PRRECORD']['RECORDID']) && $values['PRRECORD']['RECORDID'] != '' ) {
            $recordId = ' #' . $values['PRRECORD']['RECORDID'];
        }
        $subject = $emailToken->applyPlaceholders(
            'subject.recallText',
            [
                'RECORD_ID' => $recordId,
            ]
        );

        return $subject;
    }

    /**
     * buildDefaultActionSubject - construct default action message to be sent to contact
     *
     * @param array  $values     timesheet data
     * @param array  $contactObj person to contact
     * @param string $action     action that was performed
     * @param I18NEmailToken $emailToken
     *
     * @return string message
     */
    private function buildDefaultActionSubject($values, /** @noinspection PhpUnusedParameterInspection */ $contactObj, $action, I18NEmailToken $emailToken)
    {
        $recordId = '';
        // Add the report number is exists
        if ( isset($values['PRRECORD']['RECORDID']) && $values['PRRECORD']['RECORDID'] != '' ) {
            $recordId = ' #' . $values['PRRECORD']['RECORDID'];
        }
        if ($action === 'declined') {
            $subject = $emailToken->applyPlaceholders(
                'subject.declinedText',
                [
                    'RECORD_ID' => $recordId
                ]
            );
        } else {
            $subject = $emailToken->applyPlaceholders(
                'subject.approvedText',
                [
                    'RECORD_ID' => $recordId
                ]
            );
        }
        return $subject;
    }

    /**
     * @param string $emailType
     * @return I18NEmailToken
     */
    protected function getI18NEmailTokenObject(string $emailType) : I18NEmailToken
    {
        $emailTokenId = "";
        switch($emailType){
            case NOTIFY_APPROVER:
                $emailTokenId = "IA.EMAIL.EE.NOTIFY_APPROVER";
                break;
            case APPROVER_ACTION:
                $emailTokenId = "IA.EMAIL.EE.APPROVER_ACTION";
                break;
        }
        return I18NEmailToken::buildFromResource($emailTokenId);
    }
}
