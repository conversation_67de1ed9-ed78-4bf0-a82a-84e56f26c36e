<?php

/**
 * <AUTHOR> <anoop.<PERSON>@sage.com>
 */
class TTSetupEditor extends ModuleSetupEditor
{

    /**
     * @param array $_params the parameters to initialize the instance
     */
    public function __construct($_params = [])
    {
        parent::__construct($_params);
    }

    /**
     * @return array list of js files
     */
    protected function getJavaScriptFileNames()
    {
        return [
            '../resources/js/aimlsetup.js',
        ];
    }

    /**
     * @return bool
     */
    protected function getShowRepeatMsg()
    {
        return false;
    }

    /**
     * This is a hook functions for subclases to add the dynamic metadata into the current layout.
     * At the time this function is called, the data, state and view objects are not available.
     * The subclass must operate on the given params structure.
     *
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {
        parent::buildDynamicMetadata($params);
    }

    /**
     * @param $obj
     * @return true
     * @throws Exception
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);
        return true;
    }
}