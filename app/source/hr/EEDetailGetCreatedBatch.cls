<?php
/**
 * Class definition for EEDetailGetCreatedBatch object
 *
 * <AUTHOR> Rusu <<EMAIL>>
 * @copyright 2015 Intacct Corporation All, Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 */

/**
 * Class EEDetailGetCreatedBatch
 * Created by PhpStorm.
 * Date: 6/8/2015
 * Time: 4:00 PM
 */
class EEDetailGetCreatedBatch extends UnionGetCreatedBatch
{
    /**
     * @param string $entity
     * @param string $timestampGMT
     * @param int    $readTimestamp
     * @param int    $pageSize
     * @param array  $params
     */
    function __construct($entity, $timestampGMT, $readTimestamp, $pageSize, $params = array())
    {
        $this->configurationArray = UnionIteratorConfigurator::getBatchConfigurationArray()['eedetail'];
        parent::__construct($entity, $timestampGMT, $readTimestamp, $pageSize, $params);
    }

    /**
     * getReportTypeBatch
     *
     * @param array $params
     *
     * @return DetailGetCreatedReportTypeBatch
     */
    protected function getReportTypeBatch( $params )
    {
        if ( DDS_DEBUG == 1 ) {
            impp('EEDetailGetCreatedReportTypeBatch', '');
        }
        return new DetailGetCreatedReportTypeBatch(
            'eedetail', $this->getTimestampGMT(), $this->getReadTimestamp(),
            $this->getPageSize(), $params 
        );
    }
}