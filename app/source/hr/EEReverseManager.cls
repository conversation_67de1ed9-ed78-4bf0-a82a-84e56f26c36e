<?

class EEReverseManager extends APReverseManager
{

    function __construct()
    {
        // do nothing yet
    }

    /**
     * @param string       $recordKey       Record to be reversed
     * @param string       $_date           Reverse date (in storage format!)
     * @param array|string $reversedRecord  return the new reversed record
     * @param array|string $revParentRecord return the new reversed record
     * @param string       $_desc           incoming reversed context record
     * @param string       $recordType
     * @param bool         $isretainagereleaseinvoice
     *
     * @return bool
     */
    function Reverse($recordKey, $_date, &$reversedRecord, &$revParentRecord = '', $_desc = '', $recordType = '',
                     $isretainagereleaseinvoice = false)
    {
        $ok = true;
        if ( CreditCardManager::useCreditCardWithEmpExpenses() ) {
            $ccMgr = new CCEEResolveManager();
            $ok = $ok && $ccMgr->reverseResolveEntries($recordKey);
        }
        if ( ExpenseUtils::useElectronicReceiptsWithEmpExpenses() ) {
            $receiptMgr = new ReceiptEEResolveManager();
            $ok = $ok && $receiptMgr->reverseResolveEntries($recordKey);
        }

        $ok = $ok && parent::Reverse($recordKey, $_date, $reversedRecord, $revParentRecord, $_desc, $recordType, $isretainagereleaseinvoice);

        return $ok;
    }
}



