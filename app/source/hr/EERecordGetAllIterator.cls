<?php
/**
 * EERecord entity GetAllIterator
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation, All Rights Reserved
 */

/**
 * GetAllBatch class for the EERecord entity
 */
class EERecordGetAllIterator  extends UnionGetAllIterator
{
    /**
     * @param EntityManager $entMgr      entity manager
     * @param GetListBatch  $batch       entity
     * @param array         $queryParams query data structure
     */
    public function __construct(EntityManager $entMgr, GetListBatch $batch, $queryParams)
    {
        parent::__construct($entMgr, $batch, $queryParams, 'eerecord');
    }
}