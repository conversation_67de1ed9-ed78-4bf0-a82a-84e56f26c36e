<?php


global $gRecordNoFieldInfo, $gAUWhenCreatedFieldInfo, $gWhenModifiedFieldInfo,
       $gModifiedByFieldInfo, $gDescription, $gCurrencyFormat;

$kSchemas['electronicreceipts'] = array(
    'children' => array(
        'supdocmaps' => array(
            'fkey' => 'record#',
            'invfkey' => 'recordid',
            'fkeyconvto' => 'char',
            'join' => 'outer',
            'table' => 'supdocmaps',
            'filter' => " supdocmaps.transactiontype(+) = 'EMPEXPENSE' ",
            'children' => array(
                'supdoc' => array(
                    'fkey' => 'documentid',
                    'invfkey' => 'record#',
                    'table' => 'supdoc',
                    'join' => 'outer'
                )
            )
        ),
    ),
    'object' => array(
        'NR_TOTALENTERED',
        'NR_TRX_TOTALENTERED',
        "TRX_TOTALDUE",
        "TRX_TOTALENTERED",
        "TRX_TOTALPAID",
        "TRX_TOTALSELECTED",
        "RAWSTATE",
        'AUWHENCREATED',
        'WHENMODIFIED',
        'MODIFIEDBY',
        'SUPDOCID',
        'SUPDOCKEY'
    ),
    'schema' => array(
        "TRX_TOTALDUE"      => "trx_totaldue",
        "TRX_TOTALENTERED"  => "trx_totalentered",
        "TRX_TOTALPAID"     => "trx_totalpaid",
        "TRX_TOTALSELECTED" => "trx_totalselected",
        "RAWSTATE"          => "state",
        'AUWHENCREATED'     => 'auwhencreated',
        'WHENMODIFIED'      => 'whenmodified',
        'MODIFIEDBY'        => 'modifiedby',
        'SUPDOCID' => 'supdoc.documentid',
        'SUPDOCKEY' => 'supdoc.record#',
    ),
    'publish' => array(
        'RECORDNO',
        'LASTNAME',
        'FIRSTNAME',
        'RECORDID',
        'WHENCREATED',
        'WHENPOSTED',
        'TOTALENTERED',
        'STATE',
        'TOTALPAID',
        'TOTALSELECTED',
        'TOTALDUE',
        'SYSTEMGENERATED',
        'PRBATCH',
        'PRBATCHKEY',
        'DESCRIPTION',
        'MEMO',
        'WHENPAID',
        'WHENMODIFIED',
        'AUWHENCREATED',
        'CREATEDBY',
        'MODIFIEDBY',
        'CURRENCY',
        'BASECURR',
        'NR_TOTALENTERED',
        'NR_TRX_TOTALENTERED',
        'INCLUSIVETAX',
        'TAXSOLUTIONID',
        'TAXMETHOD',
        'SUPDOCID'
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'RECORDKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'electronicreceiptsitem',
            'path' => 'ITEMS',
            'minLinesRequired' => 1,
        ),
    ),
    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'fullname' => 'IA.REASON_FOR_RECEIPT',
            'desc' => 'IA.REASON_FOR_RECEIPT',
            'type' => array (
                'ptype' => 'multitext',
                'type' => 'text',
                'showpopup' => true,
                'maxlength' => 1000,
                'format' => $gDescription,
                'size' => 50,
            ),
            'numofrows' => 1,
            'numofcols' => 50,
            'path' => 'DESCRIPTION',
            'partialedit' => true,
            'fastUpdate' => true,
            'id' => 3
        ),
        array(
            'fullname' => 'IA.STATE',
            "type" => array(
                "ptype" => "enum",
                "type" => "text",
                'validlabels' => [ 'IA.DRAFT', 'IA.USED','IA.REVIEW','IA.ANALYZING' ],
                'validvalues' => [ 'Draft', 'Used', 'Review','Analyzing' ],
                '_validivalues' => array( 'D', 'UD', 'RE','AN'),
            ),
            'desc' => 'IA.STATE',
            'path' => 'STATE',
            'fastUpdate' => true,
            'hidden' => true,
            'readonly' => true,
            'id' => 6
        ),
        array(
            "id" => 55,
            "path" => "RAWSTATE",
            "fullname" => 'IA.RAW_STATE',
            "type" => array(
                "type" => "text"
            ),
            "hidden" => true,
            "partialedit" => true,
            "readonly" => true,
        ),
        array(
            'fullname' => 'IA.STATE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'isHTML' => true,
            'desc' => 'IA.STATUS',
            'path' => 'STATELINK',
            'hidden' => true,
            'readonly' => true,
            'id' => 33
        ),
        array(
            'fullname' => 'IA.ELECTRONIC_RECEIPT_NUMBER',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'size' => 20,
                'maxlength' => 21,
                'format' => '/^.{0,21}$/'
            ),
            'desc' => 'IA.ELECTRONIC_RECEIPT_NUMBER',
            'path' => 'RECORDID',
            'partialedit' => true,
            'id' => 11
            // 'readonly' => true,
        ),
        $gWhenModifiedFieldInfo,
        $gModifiedByFieldInfo,
        $gAUWhenCreatedFieldInfo,
    ),
    'supdocentity' => 'EMPEXPENSE',
    'customerp' => array(
        'SLTypes' => array(CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKWORKFLOW),
        'SLEvents' => array(CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK,
            CUSTOMERP_EVENT_PAID,
        ),
        'AllowCF' => true
    ),
    'dbfilters' => array(
        array('electronicreceipts.recordtype', '=', 'ex'),
    ),
    'printas' => 'IA.ELECTRONIC_RECEIPT',
    'pluralprintas' => 'IA.ELECTRONIC_RECEIPTS',
    'nexus' => array(
        'employee' => array(
            'object' => 'employee',
            'relation' => MANY2ONE,
            'field' => 'EMPLOYEEID'
        ),
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'STATE',
        'WHENMODIFIED'
    ),
    'allowDDS' => false,
    'nochatter' => true,
    'description' => 'IA.HEADER_INFORMATION_FOR_ELECTRONIC_RECEIPT',
    'nameFields' => [ 'RECORDID', 'EMPLOYEEID', 'WHENCREATED' ],
    'fastUpdate'  => true
);
include 'expense.ent';
$kSchemas['electronicreceipts'] = EntityManager::inheritEnts($kSchemas['expense'], $kSchemas['electronicreceipts']);

$kSchemas['electronicreceipts']['customComponentsEntity'] = 'electronicreceipts';

$kSchemas['electronicreceipts']['url'] = array(
    'viewop' => 'ee/lists/electronicreceipts/receipts/view',
    'editop' => 'ee/lists/electronicreceipts/receipts/edit',
);

