<?
//=============================================================================
//
//	FILE:			checkregister.phtml
//	AUTHOR:			<PERSON> (basically thieved from prrecords.phtml)
//	DESCRIPTION:	Lister for payments
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'util.inc';
require_once 'html_header.inc';
require_once 'show_listing.inc';
require_once 'groom_lister.inc';
require_once 'backend_prbatch.inc';
require_once 'backend_payment.inc';

$localTokens = ['IA.ARE_YOU_SURE_YOU_WANT_TO_VOID_THIS_CHECK','IA.REPORTS','IA.EMPLOYEE_EXPENSE_REPORTS','IA.VOID','IA.CHECK_REGISTER','IA.EXPENSE_REPORTS_PAYMENTS','IA.BANK', 'IA.ACCOUNT_ID',
                'IA.DATE', 'IA.CHECK_KEY','IA.AMOUNT','IA.REPORTS','IA.REPORT_KEY','IA.WHEN_FILED','IA.DESCRIPTION', 'IA.REPORT_AMOUNT',
                'IA.AMOUNT_PAID', 'IA.TOTAL_DUE','IA.INTACCT','IA.THERE_ARE_NO_BILLS_ASSOCIATED_WITH_VOIDED_QUICK_CHECKS','IA.EMPLOYEE','IA.INTACCT_TITLE'];
$textMap = getLocalizedText(I18N::tokenArrayToObjectArray($localTokens), $errMsg);

Init();
$_batch      = Request::$r->_batch;
$_rb         = Request::$r->_rb;
$_s          = Request::$r->_s;
$_rs         = Request::$r->_rs;
$_op         = Request::$r->_op;
$_recordtype = &Request::$r->_recordtype;
$_ret        = Request::$r->_ret;
$hlpfile     = &Request::$r->hlpfile;
$_cancel     = Request::$r->_cancel;
$_inv        = Request::$r->_inv;
$_queryfromend = Request::$r->_queryfromend;
$_void         = Request::$r->_void;
$op            = &Request::$r->op;

$perm_arr    = GetPagePermissions($_op);
list($user_rec,$cny) = explode('@', $_userid);
$_recordtype = (isset($_inv)) ? 'ei' : 'ep';

epp("_recordtype = $_recordtype");

$confrimationMessage =GT($textMap, 'IA.ARE_YOU_SURE_YOU_WANT_TO_VOID_THIS_CHECK');
$reports =GT($textMap, 'IA.REPORTS');
$void = GT($textMap, 'IA.VOID');
$void_url = '\'<a href=eecheckregister.phtml?.void=1&.inv=\'|| prrecord.record# ||\'&.op='.$_op.'&' . OptDone(ScriptRequest()) . ' ONMOUSEOVER=\'\'window.status="' . statusdisp('Void') . '"; return true;\'\' onfocus=\'\'window.status="' . statusdisp('Void') . '"; return true;\'\' onblur=\'\'window.status=""\'\' ONMOUSEOUT=\'\'window.status=""\'\' onclick=\'\'return confirm('.$confrimationMessage.');\'\'>'. $void .'</a>\'';
$url = '\'<a href=eecheckregister.phtml?.inv=\'|| prrecord.record# ||\'&.op='.$_op.'&' . OptDone(ScriptRequest()) . ' ONMOUSEOVER=\'\'window.status="' . statusdisp('Reports') . '"; return true;\'\' onfocus=\'\'window.status="' . statusdisp('Reports') . '"; return true;\'\' onblur=\'\'window.status=""\'\' ONMOUSEOUT=\'\'window.status=""\'\'>'. $reports .'</a>\'';
$invurl = '\'<a href=eecheckregister.phtml?.inv=\'|| $_inv ||\'&.op='.$_op.'&' . OptDone(ScriptRequest()) . ' ONMOUSEOVER=\'\'window.status="' . statusdisp('Reports') . '"; return true;\'\' onfocus=\'\'window.status="' . statusdisp('Reports') . '"; return true;\'\' onblur=\'\'window.status=""\'\' ONMOUSEOUT=\'\'window.status=""\'\'>'. $reports .'</a>\'';

/** @noinspection PhpUndefinedVariableInspection */
if ( $_void == 1) {
    // _inv is actually the record# of the payment.
    if (!VoidPayment($_inv, '', $voidpaykey)) {
        include "popuperror.phtml";
        exit();
    }
    else {
        Ret();
    }
}

if ($_cancel) {
    Ret();
}

if ($_ret) {
    Ret();
}

//////////////////////////////
// BUILD PARAMETERS FROM URL
//////////////////////////////

if($_batch != '') {
    $crfilter = "lower(PRRECORD.RECORDTYPE) = 'ep' and PRRECORD.PRBATCHKEY = $_batch and state in ('V','C')";
} else {
    $crfilter = "lower(PRRECORD.RECORDTYPE) = 'ep' and state in ('V','C')";
}
$typeInfo =
    array( 'ep' => array(
        'title' => GT($textMap, 'IA.CHECK_REGISTER'),
        'filter' => $crfilter,
        'addlabel' => GT($textMap,'IA.EXPENSE_REPORTS_PAYMENTS'),
        'fieldlabels' => array(
            GT($textMap,'IA.BANK'), GT($textMap,'IA.ACCOUNT_ID'),
            GT($textMap,'IA.EMPLOYEE'),
            GT($textMap,'IA.DATE'), GT($textMap,'IA.CHECK_KEY'),
            GT($textMap,'IA.AMOUNT'), '',''
        ),
        'fullnames' => array(
            'bankaccount.name',
            'bankaccount.accountid',
            "contact.firstname || ' ' || contact.lastname",
            'prrecord.whenpaid',
            'prrecord.docnumber',
            "decode(prrecord.state,'C',to_char(prrecord.totalpaid),'Voided')",
            $url,
            "decode(prrecord.state,'C',$void_url,'Void')"
        ),
        'fields' => array(
            'NAME', 'ACCOUNTID', 'EMPLOYEENAME',
            'WHENPAID', 'DOCNUMBER',
            'TOTALPAID',
            'INVOICES',
            'VOIDLINK'
        ),
        'groomtype' => array(
            'CHAR', 'CHAR',
            'CHAR', 'DATE', 'CHAR', 'CHARMONEY'
        ),
        'format' => array(
            'EMPLOYEENAME' => array(
                'calign' =>'left',
                'cwidth' => '1%'
            ),
            'TOTALPAID' => array(
                'calign' =>'right',
                'cwidth' => '1%'
            ),
            'WHENPAID' => array(
                'calign' => 'center',
                'cwidth' => '1%'
            ),
            'DOCNUMBER' => array(
                'calign' => 'center',
                'cwidth' => '1%'
            ),
            'NAME' => array(
                'calign' => 'left',
                'cwidth' => '1%'
            ),
            'ACCOUNTID' => array(
                'calign' => 'center',
                'cwidth' => '1%'
            )
        )
    ),
           'ei' => array(
               'title' => GT($textMap, 'IA.REPORTS'),
               'filter' => "lower(PRRECORD.RECORDTYPE) = 'ei'",
               'addlabel' => GT($textMap,'IA.EMPLOYEE_EXPENSE_REPORTS'),
               'fieldlabels' => array(
                   GT($textMap,'IA.REPORT_KEY'),
                   GT($textMap,'IA.WHEN_FILED'),
                   GT($textMap,'IA.DESCRIPTION'), GT($textMap,'IA.REPORT_AMOUNT'),
                   GT($textMap,'IA.AMOUNT_PAID'), GT($textMap,'IA.TOTAL_DUE'),
               ),
               'fullnames' => array(
                   'prrecord.recordid',
                   'prrecord.whencreated',
                   'prrecord.description',
                   'prrecord.totalentered',
                   'invoice.amount',
                   'prrecord.totaldue'
               ),
               'fields' => array(
                   'RECORDID',
                   'WHENCREATED',
                   'DESCRIPTION',
                   'TOTALENTERED',
                   'AMOUNT', 'TOTALDUE'
               ),
               'groomtype' => array(
                   'CHAR', 'DATE', 'CHAR',
                   'MONEY', 'MONEY', 'MONEY'
               ),
               'format' => array(
                   'AMOUNT' => array(
                       'calign' =>'right',
                       'cwidth' => '1%'
                   ),
                   'TOTALENTERED' => array(
                       'calign' =>'right',
                       'cwidth' => '1%'
                   ),
                   'TOTALDUE' => array(
                       'calign' =>'right',
                       'cwidth' => '1%'
                   ),
                   'WHENPAID' => array(
                       'calign' => 'center',
                       'cwidth' => '1%'
                   ),
                   'DOCNUMBER' => array(
                       'calign' => 'center',
                       'cwidth' => '1%'
                   ),
                   'NAME' => array(
                       'calign' => 'center',
                       'cwidth' => '1%'
                   ),
                   'ACCOUNTID' => array(
                       'cwidth' => '1%'
                   )
               )
           )
    );

$obj = $typeInfo[$_recordtype];
$fullnames = $typeInfo[$_recordtype]['fullnames'];
$fields = $typeInfo[$_recordtype]['fields'];
epp("$invurl");
/** @noinspection PhpUndefinedVariableInspection */
$params = array( 'title'       => $obj['title'],
                 'fields'      =>  $obj['fields'],
                 'fieldlabels' => $obj['fieldlabels'],
                 'fullnames'   =>  $obj['fullnames'],
                 'groomtype'   => $obj['groomtype'],
                 'key'         =>  'RECORD#',
                 'op'          =>  $perm_arr,
                 'edit'        => $edit_url,
                 'action'          => ScriptRequest(),
                 'list'         =>  (isset($_inv)) ? "eecheckregister.phtml?.inv=$_inv&.batch=$_batch" : "eecheckregister.phtml?.batch=$_batch",
                 'addlabel'     =>  $obj['addlabel'],
                 'rangebegin'   =>  $_rb,
                 'rangesize'    =>  $_rs,
                 'disabledelete' => true,
                 'disableedit'  => true,
                 'disableadd'   => true,
                 'sortcolumn'   =>  $_s,
                 'format'       => $obj['format'],
                 'queryfromend'    =>  $_queryfromend
);

$listing = ShowListingInit($params);

if (!CalcFiltersLite($listing, $filters)) {
    // some error handling here
    //epp("Error occurred in CalcFiltersLite.");
    $filters = '';
}

epp("recordtype: $_recordtype");
epp(__FILE__ . '.' . __LINE__);
eppp($obj);
epp("filter1: $filters");
if (isset($obj['filter']) && $obj['filter'] != '') {
    $filters .= ($filters == '') ? ' ' : ' and ';
    $filters .= $obj['filter'];
}
if ($_recordtype == "ei") {
    $filters .= " and invoice.paymentkey = '$_inv' and " .
                "prrecord.record# = invoice.recordkey";
}

epp("filters: $filters");

$selectlist = array("prrecord.record#");
for ($i = 0 ; $i < count($fullnames); $i++) {
    $selectlist[] = "$fullnames[$i]  AS $fields[$i]";
}
$selects = join(', ', $selectlist);

// BUILD ORDER BY CLAUSE
$desc_dir = $_queryfromend ? ' asc' : ' desc';
$asc_dir = $_queryfromend ? ' desc' : ' asc';
$order = str_replace(
    ':d', $desc_dir,
    str_replace(':a', $asc_dir, $listing['_sortcolumn'])
);

GetCount('prrecord', $filters, $resultCount);
SetDomainSize($listing, $resultCount);

$table = array();

$table = GetNObjects('prrecord', $selects, $filters, $order, $listing['_rangebegin'], $listing['_rangesize']);

if ($_queryfromend) {
    $table = array_reverse($table);
}

epp("Table:");
eppp($table);
if (isset($table[0]) or $table[0] != '') {
    GroomResultantData($table, $obj['groomtype'], $obj['fields']);
}

if ($_recordtype == 'ei') {
    for ($r = 0; $r < count($table); $r++) {
        $_mod = Request::$r->_mod;
        $op = ($_mod == 'ee') ?  GetChildPermFormRecType($_recordtype) : GetOperationId('ce/activities/eebatch/view');

        $table[$r]['WHENCREATED'] = "<a href=editor.phtml?.it=eexpenses&.r=" . intval($table[$r]['RECORD#']) . "&.recordtype=" . $_recordtype . "&.op=" . $op . "&" . OptDone(ScriptRequest()). ">" . $table[$r]['WHENCREATED'] . "</a>";
    }
}

// SET THE STATUS FIELD
if (isset($obj['status'])) {
    foreach($table as $key => $row) {
        if (isset($row['STATE'])) {
            $table[$key]['STATE'] = $obj['status'][$row['STATE']];
        }
    }
}


if (HasErrors()) {
    // add some error here
    // epp("Error occurred in GetNObjects.");
}
else {
    // groom the data output?
}

$title = ContractUtil::GTP($textMap, 'IA.INTACCT_TITLE', [ 'TITLE' => $typeinfo[$_recordtype]['title'] ]);
if($_inv != '') {
    $hlpfile = 'Reports_screen';
} else {
    $hlpfile = 'Check_Register_(EE)';
}
?>
<?  Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000'); ?>
<?  if (count($table) == 0 && $_recordtype == "pi") { ?>

    <table border="0" cellpadding="1" cellspacing="0" width="70%" bgcolor="#999966">
        <tr> <td valign="top">

                <table border="0" cellpadding="4"
                       cellspacing="0" width="100%" bgcolor="#FFFFCC">
                    <tr>
                        <td valign="middle" align="center">
                            <font face="Verdana, Arial, Helvetica" size="2">
                                <b><?php echo GT($textMap,'IA.THERE_ARE_NO_BILLS_ASSOCIATED_WITH_VOIDED_QUICK_CHECKS'); ?></b>
                            </font>
                        </td>
                    </tr>
                </table>
    </table>

    <?
} ?>
<?  ShowListing($listing, $table); ?>
<?
//htmlButtonFooter($gray_msg_text, $buttons);
?>
    </form>
<?
htmlFooter($title);

