<?php

/**
 *  This file contains a class (URLReplacePOD) which supports URL replacement
*    for the logout to global from POD.
*
*  @file      URLReplacePOD.cls
*  <AUTHOR> <<EMAIL>>
*  @copyright 2013 Intacct
*/

require_once 'URLReplace.cls';

/**
 * Class that implements API URL replacement.
 */
class URLReplacePOD extends URLReplace
{
    const URL_REPLACEAPI_CFG_KEY = 'URL_REPLACE_POD';

    /**
     * @var URLReplacePOD $instancePOD
     */
    static private $instancePOD = null;

    /**
     * Returns singleton URLReplacePOD object reference.
     * @return URLReplacePOD
     */
    public static function getInstance()
    {
        if (! self::$instancePOD ) {
            self::$instancePOD = new URLReplacePOD(self::URL_REPLACEAPI_CFG_KEY);
        }
        return self::$instancePOD;
    }

    /**
     * Gather data from the configuration and build the replacement map
     *
     * @param string $replaceKey
     */
    protected function __construct($replaceKey)
    {
        parent::__construct($replaceKey);
        //$this->configMap = self::getURLReplaceMap(self::URL_REPLACEAPI_CFG_KEY);
    }
}
