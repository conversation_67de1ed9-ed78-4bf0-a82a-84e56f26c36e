<?php
/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */

final class ConsumerEngine
{
    // metrics constants & vars
    const METRIC_COMPONENT_PREFIX = "consumer_";

    /** @var string $metricComponent */
    private $metricComponent = null;
    // end metrics constants & var

    // this is an extra time, over the allocated run time, specific to the DB
    // the lock will be set to expire in (DB limit + this extra time) to account for event processings that could
    // take longer
    //in seconds
    const CONSUMER_LOCK_TIME_BUFFER = 15 * 60;

    /** @var Lock $lock */
    private $lock;
    /** @var IConsumer $consumer */
    private $consumer;

    /** @var string $progressCollectionName */
    private $progressCollectionName;

    /** @var int $dbId */
    private $dbId;
    /** @var string $lockId */
    private $lockId;

    /**
     * @return string
     */
    public function getMetricComponent(): string
    {
        return $this->metricComponent;
    }

    /**
     * @return int
     */
    public function getDbId(): int
    {
        return $this->dbId;
    }

    /**
     * @return string
     */
    public function getLockId(): string
    {
        return $this->lockId;
    }


    /**
     * @return bool
     */
    public function serialize()
    {
        static $expectedParams = [
            'lockId',
            'timeLimit',
            'consumerTimeLimit',
            'jobId',
            'dbId',
            'operation'
        ];

        foreach ( $expectedParams as $paramName ) {
            if ( ! Request::$r->$paramName ) {
                logToFileError("Required parameter '$paramName' is not present. Cannot start the consumer.");
                echo I18N::getSingleToken(
                    "IA.CONSUMER_ENGINE_CANNOT_START_CONSUMER",
                    [
                        [ "name" => "PARAM_NAME", "value" => $paramName]
                    ]
                );
                return false;
            }
        }

        $lockId = Request::$r->lockId;
        $lockTimeLimit = Request::$r->timeLimit;
        $operation = Request::$r->operation;

        // This might be used later if this tracks global limits
        $this->progressCollectionName = DispatcherEngine::GLOBAL_LIMIT_STORAGE_NAME_PREFIX . $operation;

        Globals::$g->perfdata->setAction($operation);
        $consumerClass = $operation . 'Consumer';
        if (!class_exists($consumerClass)) {
            throw new Exception(
                I18N::getSingleToken(
                    "IA.CONSUMER_ENGINE_CONSUMER_CLASS_NOT_FOUND",
                    [
                        [ "name" => "CONSUMER_CLASS", "value" => $consumerClass]
                    ]
                )
            );
        }
        $this->consumer = new $consumerClass();
        if (!($this->consumer instanceof IConsumer)) {
            throw new Exception(
                I18N::getSingleToken(
                    "IA.CONSUMER_ENGINE_INCORRECT_CONSUMER_NAME",
                    [
                        [ "name" => "CONSUMER_NAME", "value" => $operation]
                    ]
                )
            );
        }

        $this->lock = new Lock();
        $ok = true;
        if (!$this->lock->setLock($lockId, self::CONSUMER_LOCK_TIME_BUFFER + $lockTimeLimit, false)) {
            LogToFile($this->consumer->getLogPrefix() . ": Could not get lock on " . $lockId, LOG_FILE);
            $ok = false;
        }

        // Let the consumer know when the consumer started successfully. send the info right after the lock was set
        // We need to reply with this message even if the lock was not set. Otherwise the dispatcher assumes that it
        // failed to start the consumer and it will stop prematurely when the failure counts reaches of the threshold
        ob_end_flush();
        echo DispatcherEngine::ACK_CONSUMER_RESPONSE;
        ob_flush();
        flush();

        return $ok;
    }

    public function run()
    {
        // don't allow POSTs if not from within Intacct network
        if (!ValidateAccess()) {
            logFL();
            return;
        }

        // Ensure the correct DB user
        Database::setUseReportUserId($this->consumer->useReportUserId());

        $jobId = Request::$r->jobId;
        $this->metricComponent = self::METRIC_COMPONENT_PREFIX . Request::$r->operation;

        $endMetric = new MetricConsumerEngineEnd($this->metricComponent, $jobId, Request::$r->parentId);
        $endMetric->startTime();

        // in case we are not in an automation test, initialize the context of the consumer
        // otherwise, don't initialize it as we are using dummy companies for test
        if (isset(Request::$r->_dispatcherAutomationTest) && Request::$r->_dispatcherAutomationTest === "") {
            // we initialize the context of the consumer before recording the start metric to catch the company & db context
            if (!$this->consumer->initContext($jobId)) {
                // the consumer context setting failed. We give up
                return;
            }
        }

        (new MetricConsumerEngineStart($this->metricComponent, $jobId, Request::$r->parentId, Request::$r->dbServer))->publish();

        $this->dbId = (int) Request::$r->dbId;
        $this->lockId = Request::$r->lockId;

        $lockTimeLimit = (int) Request::$r->timeLimit;
        $consumerTimeLimit = (int) Request::$r->consumerTimeLimit;

        // Only track progress of consumers if there is a global limit
        $globalLimit = (int) ( Request::$r->globalLimit ?? -1 );
        if ( $globalLimit > 0 ) {
            // Only save progress if there is a global limit (progressStore is defined)
            try {
                // Initialize the key-value storage to save progress for this consumer
                $progressStore = KVSFactory::get(
                    $this->progressCollectionName,
                    [
                        'STORAGE' => [
                            'STORE_TYPE' => 'GLOBAL',
                            'KEY_TYPE' => 'KEY_ONLY',
                            'SHARD' => MongoDBEnvManager::getInstance()->getHostsManager()->getShardHostGlobal()
                        ],
                    ]
                );

                // Store this consumers existence in MongoDB so that it can be used by a watchdog (dispatcher)
                $progressStore->set(
                    $jobId,
                    [
                        'lockID' => $this->lockId,
                        'dbID' => $this->dbId,
                        'timeLimit' => $lockTimeLimit,
                        'consumerTimeLimit' => $consumerTimeLimit,
                        'UNIQUE_ID' => WebServerInfo::get()->REQUESTID
                    ],
                    $lockTimeLimit
                );

                // Remove this consumers existence in MongoDB (upon leaving this function)
                /** @noinspection PhpUnusedLocalVariableInspection */
                $autoDestruct = new AutoDestructClosure(
                    function () use ($progressStore, $jobId) {
                        try {
                            $progressStore->deleteOne($jobId);
                        } catch ( StorageException $e ) {
                            // Don't care, not mission critical
                        }
                    }
                );
            } catch ( StorageException $e ) {
                // Don't care, not mission critical
            }
        }

        $locks = [];
        // Only track progress of consumers if there is a db schema limit
        $dbSchemaLimit = (int) ( Request::$r->dbSchemaLimit ?? -1 );
        if ( $dbSchemaLimit > 0 ) {
            $locks['dbSchema'] = $this->dbId;
        }

        // Only track progress of consumers if there is a db server limit
        $dbServerLimit = (int) ( Request::$r->dbServerLimit ?? -1 );
        if ( $dbServerLimit > 0 ) {
            $locks['dbServer'] = Request::$r->dbServer;
        }

        // Only track progress of consumers if there is a cny# limit
        $cnyLimit = (int) ( Request::$r->cnyLimit ?? -1 );
        if ( $cnyLimit > 0 ) {
            $locks['cny'] = Request::$r->cnyLimitName;
        }

        if ( !empty($locks) ) {
            // Only save progress if there are dispatcher limits
            try {
                // Initialize the key-value storage to save progress for this consumer
                $progressStoreForDispatcherLimits = KVSFactory::get(
                    DispatcherEngine::RESOURCE_LIMIT_STORAGE_NAME_PREFIX . 'global_process',
                    [
                        'STORAGE' => [
                            'STORE_TYPE' => 'GLOBAL',
                            'KEY_TYPE' => 'KEY_ONLY',
                            'SHARD' => MongoDBEnvManager::getInstance()->getHostsManager()->getShardHostGlobal()
                        ],
                    ]
                );

                // Store this consumers existence in MongoDB so that it can be used by a watchdog (dispatcher)
                $progressStoreForDispatcherLimits->set(
                    $jobId,
                    [
                        'locks' => $locks,
                        'lockID' => $this->lockId,
                        'dbID' => $this->dbId,
                        'timeLimit' => $lockTimeLimit,
                        'consumerTimeLimit' => $consumerTimeLimit,
                        'UNIQUE_ID' => WebServerInfo::get()->REQUESTID
                    ],
                    $lockTimeLimit
                );

                // Remove this consumers existence in MongoDB (upon leaving this function)
                /** @noinspection PhpUnusedLocalVariableInspection */
                $autoDestruct = new AutoDestructClosure(
                    function () use ($progressStoreForDispatcherLimits, $jobId) {
                        try {
                            $progressStoreForDispatcherLimits->deleteOne($jobId);
                        } catch ( StorageException $e ) {
                            // Don't care, not mission critical
                        }
                    }
                );
            } catch ( StorageException $e ) {
                // Don't care, not mission critical
            }
        }

        $moreWork = false;
        // This call returns true if this consumer-instance has more work to do
        // (typically company-based consumers that do a limited set of jobs)
        if ( $this->consumer->run(
            $consumerTimeLimit,
            $jobId,
            $this
        ) ) {
            // set a "max time exceeded" flag for the dispatcher as a memcache lock
            // this will tell the dispatcher to requeue a new consumer for this company
            $mtLock = new Lock();
            if ( $mtLock->setLock($this->lockId . DispatcherEngine::MAX_TIME_LOCK_SUFFIX, 300, false) ) {
                $mtLock->setClearLockOnDestroy(false);
            }
            $moreWork = true;
        }

        $this->lock->releaseLock();

        //FIXME: once the code is merged with Rich's we need to set the proper value on this attribute
        $endMetric->setMoreWork($moreWork);
        $endMetric->publish();  // publish will stop any running timers on the metric
    }
}
