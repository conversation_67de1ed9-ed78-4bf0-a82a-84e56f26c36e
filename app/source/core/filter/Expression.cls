<?php

/**
 * Class Expression
 *
 * Expression implementation used for query filters.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2020 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class Expression
{
    /* @var array $rpnExpressions */
    private static $rpnExpressions = [];

    /**
     * Translates the textual representation of expression into the RPN (reverse polich notation)
     *
     * @param string $expression
     *
     * @return string[]
     * @throws ExpressionFault
     * @throws StackFault
     */
    public static function expression2rpn(string $expression)
    {
        if ( isset(self::$rpnExpressions[$expression]) ) {
            return self::$rpnExpressions[$expression]['rpn'];
        }

        self::$rpnExpressions[$expression] = [ 'is-valid' => false ];

        $ret = [];
        if ( $expression ) {
            $s = $expression;
            $s = str_replace('and', '&', $s);
            $s = str_replace('or', '|', $s);
            $s = str_replace(" ", "", trim($s));

            $pattern = '/^[0-9(][0-9()&|]+[0-9)]$/';

            if ( boolval(preg_match($pattern, $s, $matches)) ) {
                $stack = new Stack();

                $chars = str_split($s);
                $counter = 0;
                $expectNumeric = true;
                $expectBoolean = false;
                $expectOpening = true;
                $expectClosing = false;
                $prevChar = '';
                $operand = '';
                foreach ( $chars as $index => $char ) {
                    switch ($char) {
                        case '(':
                            $operand = '';
                            if ( $expectNumeric && ! $expectOpening ) {
                                throw new ExpressionFault(ExpressionFault::ERROR_MESSAGE_INVALID_OPENING_PARENTHESIS,
                                                          ExpressionFault::INVALID_OPENING_PARENTHESIS);
                            } else if ($expectBoolean && !$expectOpening) {
                                throw new ExpressionFault(ExpressionFault::ERROR_MESSAGE_INVALID_EXPECTING_OPERATOR,
                                                          ExpressionFault::INVALID_EXPECTING_OPERATOR);
                            }
                            else {
                                $stack->push($char);

                                $counter++;
                                $expectNumeric = true;
                                $expectOpening = true;
                            }
                            break;

                        case ')':
                            $operand = '';
                            if ( $expectNumeric && ! $expectClosing ) {
                                throw new ExpressionFault(ExpressionFault::ERROR_MESSAGE_INVALID_OPERAND,
                                                          ExpressionFault::INVALID_OPERAND);
                            } else {
                                while ( ! $stack->isEmpty() && $stack->peek() != '(' ) {
                                    $ret[count($ret)] = $stack->pop();
                                }

                                if ( ! $stack->isEmpty() ) {
                                    $stack->pop();
                                }

                                $expectNumeric = false;
                                $expectBoolean = true;
                                $expectOpening = false;
                            }
                            $counter--;
                            break;

                        case '&':
                        case '|':
                            $operand = '';
                            if ( ! $expectBoolean ) {
                                throw new ExpressionFault(ExpressionFault::ERROR_MESSAGE_INVALID_OPERATOR,
                                                          ExpressionFault::INVALID_OPERATOR);
                            } else {
                                if ( ! $stack->isEmpty()
                                     && self::operandPrecedence($char) <= self::operandPrecedence($stack->peek()) ) {
                                    $ret[count($ret)] = $stack->pop();
                                }
                                $stack->push($char);

                                $expectNumeric = true;
                                $expectOpening = true;
                                $expectClosing = false;
                                $expectBoolean = false;
                            }
                            break;

                        default:
                            // a numeric value
                            if ( is_numeric($prevChar) ) {
                                $operand .= $char;

                                unset($ret[count($ret) - 1]);
                            } else {
                                $operand = $char;
                            }
                            $ret[count($ret)] = $operand;

                            $expectNumeric = false;
                            $expectBoolean = true;
                            $expectOpening = false;
                            $expectClosing = true;
                            break;
                    }

                    $prevChar = $char;
                }

                if ( $counter !== 0 ) {
                    throw new ExpressionFault(ExpressionFault::ERROR_MESSAGE_MISMATCHED_PARENTHESIS,
                                              ExpressionFault::MISMATCHED_PARENTHESIS);
                }

                while ( ! $stack->isEmpty() ) {
                    // $result .= $stack->pop();
                    $ret[] = $stack->pop();
                }
            } else {
                throw new ExpressionFault(ExpressionFault::ERROR_INVALID_EXPRESSION, ExpressionFault::INVALID_EXPRESSION);
            }
        }

        self::$rpnExpressions[$expression]['is-valid'] = true;
        self::$rpnExpressions[$expression]['rpn'] = $ret;

        return $ret;
    }

    /**
     * Specifies the precedence in the expression. Boolean & and | have the highest precedence.
     *
     * @param string $ch
     *
     * @return int
     */
    private static function operandPrecedence(string $ch)
    {
        switch ($ch) {
            case '&':
            case '|':
                return 1;
        }

        return -1;
    }

    /**
     * Converts logical operator into the GetList-friendly operator.
     *
     * @param string $op
     *
     * @return string
     * @throws ExpressionFault
     */
    public static function asOperator(string $op)
    {
        switch ($op) {
            case '&':
                $operator = 'AND';
                break;
            case '|':
                $operator = 'OR';
                break;

            default:
                throw new ExpressionFault("Unsupported operator: $op");
        }

        return $operator;
    }

    /**
     * For expression the string is an operand if it is in the list of [&, |]
     *
     * @param string $item
     *
     * @return bool
     */
    public static function isOperator(string $item)
    {
        return in_array($item, ['&', '|']);
    }

    /**
     * The input is considered an operand if it is numeric.
     *
     * @param string $op
     *
     * @return bool
     */
    public static function isOperand(string $op)
    {
        return is_numeric($op);
    }

}
