<?  
//=============================================================================
//
//	FILE:			picker.phtml
//	AUTHOR:			odysseas
//	DESCRIPTION:	
//
//=============================================================================

require_once 'util.inc';

import('Request');
import('Picker');
import('NLister');
import('NPicker');

Init();
$entity = Request::$r->_it;
$lister = GetEntityPicker($entity);

$simpleFilters = &Request::$r->_simpleFilters;
if( $simpleFilters ) {
    $simpleFilters = Util_DataRecordFormatter::jsonToPhp($simpleFilters);
    $lister->_params['simpleFilters'] = $simpleFilters;
}

$entityContext = Request::$r->_entityContext;
if( $entityContext ) {
    $entityContext = Util_DataRecordFormatter::jsonToPhp($entityContext);
    $lister->_params['entityContext'] = $entityContext;
}

$showInactive = Request::$r->_showinactive;
if( $showInactive ) {
    $lister->_params['showinactive'] = $showInactive;
} else {
    $lister->_params['showinactive'] = 0;
}
$hidenonPosting = Request::$r->_hideNonPostingDim;
if($hidenonPosting){
    $lister->_params['hideNonPostingDim'] = $hidenonPosting;
}else{
    $lister->_params['hideNonPostingDim'] = false;
}

$lister->BuildTable();
$lister->DrawHTML();

