<?php

class AdminAccessControlService
{
    /**
     * @param array $request
     * @param array $extraParams
     *
     * @return array
     */
    public function authenticateBearer(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): array
    {
        if ( ! isset($request['token']) ) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'token']);
            throw (new APIException())->setAPIError($error);
        }
        $response = new OAuth2Response();
        $ok = AccessToken::validateTokenWithHeader($request['token'], $response);


        if ( ! $ok ) {
            // FIXME: how do i return the errors here?
            if (!empty($response->getParameters()['error']) && $response->getParameters()['error']['errorId'] == "REST-2101") {
                $error = APIError::getInstance(APIErrorMessages::UNAUTHORIZED_TOKEN_EXPIRED_0001);
            } else {
                $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0206);
            }
            throw (new APIException())->setAPIError($error);
        }
        Backend_Init::Init();

        if ( Globals::$g->conn !== null && ! Globals::$g->conn->isJoinedConnection() ) {
            Globals::$g->conn->setIsJoinedConnection(true);
        }

        $result = ['auth' => true];

        // process the authorization if needed
        $isAuthz = false;
        if ( isset($request['permissions']) ) {
            $perms = $request['permissions'];
            foreach ( $perms as $perm ) {
                $opId = GetOperationId($perm);
                if ( $opId !== -1 && CheckAuthorization($opId, 1) ) {
                    $isAuthz = true;
                    break;
                }
            }
        } else {
            $isAuthz = true;
        }
        $result['authz'] = $isAuthz;
        return $result;
    }

    /**
     * this service will be leveraged for domain services that needs to get authorized for multiple resources
     * in addition to authorize for the service itself
     * query and composite are ones of this type of services
     *
     * the authz logic is "AND" between all arries and "OR" within an array
     * each array is all the possible permissions that can authorize one service operation
     * multiple arraies are the permissions required to perform operate on all resources in one atomic request
     *
     * Sample request:
     *  {"permissions":
     *      [["ap/lists/apaccountlabel/view", "co/lists/class/view"],
     *      ["co/lists/class/create"],
     *      ["cm/lists/bankacctrecon/delete", "co/lists/taxgroup/delete", "pa/lists/projectcontractline/delete"]]} and
     * sample output:
     *  {"ia::result":{"authz":true},"ia::meta":{"totalCount":1,"totalSuccess":1,"totalError":0}}
     *
     * @param array $request
     * @param array $extraParams
     *
     * @return array
     */
    public function checkPermissions(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): array
    {
        $isAuthz = false;
        if ( is_array($request['permissions'] ?? false) ) {
            foreach ( $request['permissions'] as $perms ) {
                $isAuthz = false;
                foreach ( $perms as $perm ) {
                    if ( -1 === ($opId = GetOperationId($perm)) ) {
                        // op permission not found
                        logToFileDebug('invalid op ' . $perm);
                    } else if ( CheckAuthorization($opId, 1) ) {
                        $isAuthz = true;
                        break;
                    }
                }
                if ( ! $isAuthz ) {
                    // op permission list is not empty, but none are permiitted
                    logToFileDebug(print_r($perms, true) . ' has failed permission check.');
                    break;
                }
            }
        }
        return ['authz'=>$isAuthz];
    }

    public function getSession(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams) : array {
        if (!isset($request['userKey'])) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'userKey']);
            throw (new APIException())->setAPIError($error);
        }

        if (!isset($request['companyKey'])) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'companyKey']);
            throw (new APIException())->setAPIError($error);
        }

        if (!isset($request['locationKey'])) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'locationKey']);
            throw (new APIException())->setAPIError($error);
        }

        if (!isset($request['departmentKey'])) {
            $error = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205, ["PARAMETER" => 'departmentKey']);
            throw (new APIException())->setAPIError($error);
        }

        $session = IASessionHandler::imsSetupSession(
            $request['userKey'] . "@" . $request['companyKey'],
            '',
            $request['locationKey'] != 0 ? $request['locationKey'] : '',
            $request['departmentKey'] != 0 ? $request['departmentKey'] : '',
            IASessionHandler::LOG_PREFIX,
            IASessionHandler::REST_SESSION_KEY);

        if ( Globals::$g->conn !== null && ! Globals::$g->conn->isJoinedConnection() ) {
            Globals::$g->conn->setIsJoinedConnection(true);
        }

        return ["session" => $session->getKey()];
    }
}
