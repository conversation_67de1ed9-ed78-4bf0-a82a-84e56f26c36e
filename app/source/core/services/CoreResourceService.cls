<?php

class CoreResourceService
{
    /**
     * caller provide common seperated domain name(s) as query parameter
     * this GET function will load issue defintion definined in those domains
     * plus error defintion commonly used
     * when domainNameList is not provided, only common error definition is loaded, e.g. REST errors
     *
     * @param array $request
     * @param array $extraParams
     *
     * @return array
     */
    public function loadIssueDefinition(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): array
    {
        $domainList = ['rest']; // common error definition that is needed by most domains
        $domainList = array_merge($domainList, $request['domains'] ?? []);
        $definition = IAIssueDef::getCompressedIssueDefinitions($domainList);
        return ['definition'=>$definition];
    }
}
