<?php

class IodlProxyFsService
{
    public const IODL_FS_VERSION = '2i';
    public const IODL_FS_API_URL_PREFIX = 'ia-ds-iodl';
    public const IODL_FS_DOMAIN = 'iodl';
    public const IODL_FS_SERVICE = 'fs';
    public const ALLOWED_IODL_COMPILE_FUNCTIONS = [
        'retrieve' => true, // assumes option to compile is true
        'compile' => true,  // only used in ds-iodl v2+
    ];

    protected static ?CacheClient $cacheClient = null;

    /**
     * @param string $verb
     * @param array  $request
     *
     * @return array|null
     * @throws APIException
     */
    protected function invoke(string $verb, array $request): ?array
    {
        /**
         * This would be the $baseURL if you run the service locally:
         *   $baseURL = 'http://localhost:8192/ia';
         * ...however DomainServiceAPIFunctionServiceClient pulls this from ia_init.cfg
         *
         * Set this URL as the apiDSEndpoint value in pods.cfg if you want to test locally: "http://localhost:8192/ia"
         *
         * Then run iodl service on the _same_ dev server where you run php:
         * java11 -Djava.util.logging.config.file=logging.properties \
         *        -Dserver.port=8192 \
         *        -Dmanagement.server.port=8193 \
         *        -jar ia-ds-iodl-0.8.5-SNAPSHOT.jar
         */

        $trustedAPI = new DomainServiceAPIFunctionServiceClient(
            self::IODL_FS_API_URL_PREFIX,
            self::IODL_FS_DOMAIN,
            self::IODL_FS_SERVICE,
            self::IODL_FS_VERSION
        );

        // Call the IODL Domain Service
        return $trustedAPI->invokeFunction($verb, $request, APIConstants::API_OPERATION_EXECUTE_RAW);
    }

    /**
     * @param string $verb    one of retrieve/compile
     * @param array  $request request body
     *
     * @return array|null
     * @throws Exception
     */
    protected function invokeForCompile(string $verb, array $request): ?array
    {
        if ( ! (self::ALLOWED_IODL_COMPILE_FUNCTIONS[$verb] ?? false) ) {
            throw new APIException("Not permitted to call iodl function '$verb' for compilation");
        }

        // Start point to measure elapsed time to compile/build the request into a response
        $startTime = microtime(true);

        // Process UI preferences based on IodlFileName
        $uiPrefKeys = $request['options']['getKeyValues'] ?? null;
        // IODL service cannot accept this option
        unset($request['options']['getKeyValues']);

        $result = null;

        /**
         * WARNING INFORMATION (about disabling use of @deprecated functions)
         *
         * The code to get the direct cached-compilation has become too complex and is virtually
         * impossible to maintain given changes on the NGS side (ds-iodl / ds-core-ui). This is especially
         * true because of the dynamic iodl registry and virtual collections, i.e. "ap.stable" -> "ap.v1"
         *
         * Therefore, the code (in this class) that does directly to mongo is _DISABLED_. All requests to compile will
         * go to the iodl service (as it did in the past).
         *
         * In the future, this entire class/interface will be removed once ia-ds-core-ui implements the "extra"
         * functionality done in this class (especially dimension data gathering).
         *
         * TODO: Remove this entire class and API once IADSNGS-97 is completed
         */
//        if ( empty($_COOKIE['use_ds_iodl']) ) { // This cookie can be used for debugging (force ds-iodl call))
//            // Check if the cached compilation exists in memcache, if so, find it in mongo directly
//            $TMP_START = microtime(true);
//            $result = self::getCachedCompilation($request); // Will return null if not cached/found
//            $time['GET_CACHED_COMPILATION'] = round(microtime(true) - $TMP_START, 3);
//        }

        // Call the IODL Domain Service only if the above call (from cache) is null
        if ( empty($result) ) {
            $TMP_START = microtime(true);
            // call ds-iodl directly (may or may not be cached)
            $result = $this->invoke($verb, $request);
            $time['INVOKE_CALL_IODL'] = round(microtime(true) - $TMP_START, 3);
            // If the compilation was cached from ds-iodl, remember this so self::getCachedCompilation (above) will find it
            /** No need to call @deprecated function setCachedCompilation */
//            $TMP_START = microtime(true);
//            self::setCachedCompilation($request, $result);
//            $time['SET_CACHED_COMPILATION'] = round(microtime(true) - $TMP_START, 3);
        }

        $TMP_START = microtime(true);
        // TODO: Replace this with a separate service
        if ($this->addDimensionsToContentData($result, $request['id']['name'] ?? '')) {
            $result['result'][] = "dimensions added";
        }

        $time['ADD_DIMENSIONS'] = round(microtime(true) - $TMP_START, 3);
        $TMP_START = microtime(true);

        // Process tokens from the response using the text service (or I18N)
        $this->processTokens($result);
        $time['PROCESS_TOKENS'] = round(microtime(true) - $TMP_START, 3);

        // Process UI preferences based on IodlFileName
        if ($uiPrefKeys) {
            $TMP_START = microtime(true);
            $this->processUIPreferences($result, $uiPrefKeys);
            $time['PROCESS_UI_PREF'] = round(microtime(true) - $TMP_START, 3);
        }

        $TMP_START = microtime(true);
        // Add company preferences to content.data.uiSettings
        if ( $this->modifyResponseWithPreferences($result) ) {
            $result['result'][] = "uiSettings added";
        } else {
            $result['result'][] = "Error adding uiSettings";
        }
        $time['PROCESS_UI_SETTINGS'] = round(microtime(true) - $TMP_START, 3);

        $elapsedTime = round(microtime(true) - $startTime, 3);
        $time['TOTAL'] = $elapsedTime;
        logToFileInfo(__METHOD__ . " time analysis: " . json_encode($time));
        $result['result'][] = "Elapsed Time: {$elapsedTime}s";

        return $result;
    }

    protected static function getCacheClient(): ?CacheClient
    {
        if ( null === self::$cacheClient ) {
            self::$cacheClient = CacheClient::getInstance(CacheClient::STORE_POOL);
        }
        return self::$cacheClient;
    }

    /**
     * @param array $request
     *
     * @return array
     * @throws APIException
     */
    protected static function extractInfoFromRequest(array $request): array
    {
        static $KEY_PREFIX = "iodl_cache";

        $name = $request['id']['name'] ?? '';
        $type = $request['id']['type'] ?? 'global';

        if ( empty($name) ) {
            throw new APIException("iodl 'id.name' empty");
        }

        return [
            'INFO_MCKEY' => $KEY_PREFIX  . '/' . $type . '/' . $name,
            'INFO_ID' => [
                'name' => $name,
                'type' => $type
            ],
            'INFO_COLLECTION' => $request['options']['sandboxCollection'] ?? 'iodl'
        ];

    }

    /**
     * @param string $collection
     *
     * @return RawStore
     */
    protected static function getMongoStore(string $collection='iodl'): RawStore
    {
        return RSFactory::get(
            $collection,
            [
                'BEHAVIOR' => ['ALLOW_CASED_COLL_NAMES' => true],
                'STORAGE' => [
                    'STORE_TYPE' => 'GLOBAL',
                    'KEY_TYPE' => 'KEY_ONLY',
                    'DB_NAME' => 'iodl',
                    'SHARD' => MongoDBEnvManager::getInstance()->getHostsManager()->getShardHostGlobal()
                ],
            ]
        );
    }

    /**
     * Special function to decode JSON keys from IODL Mongo data
     * - decode (starting) slash dollar to dollar:  "^\$..." -> "$..."
     * - decode (anywhere) slash underscore to dot: "\_" -> "."
     *
     * @param string $key
     * @return string
     */
    protected static function getMongoIODLReplaceKey(string $key): string
    {
        static $patterns = [
            '/^\\\\\\$/',
            "/(\\\\_)/"
        ];

        static $replacements = [
            '$',
            '.'
        ];

        $newKey = preg_replace($patterns, $replacements, $key, -1, $count);
//        if ( !Globals::$g->islive && $count > 0 ) {
//            logToFileDebug("IODL-Mongo Replaced '$key' with '$newKey' ($count count).");
//        }
        return $newKey;
    }

//    /**
//     * For unit testing getMongoIODLReplaceKey
//     *
//     * @return void
//     */
//    public static function testMongoIODLReplaceKey(): void
//    {
//        $testData = [
//            '\\$eq'       => '$eq',               // REPLACE one
//            'abc\\_xyz\\_def'   => 'abc.xyz.def', // REPLACE both
//            '\\$eq\\_abc' => '$eq.abc',           // REPLACE both
//            'abc\\_xyz\\$def'   => 'abc.xyz\\$def', // REPLACE one
//            '\\\\$xd'     => '\\\\$xd',           // NO change
//            'abc\\$eq'    => 'abc\\$eq',          // NO change
//            'abc\\ _xyz'  => 'abc\\ _xyz',        // NO change
//            'cba\\$fed'   => 'cba\\$fed',         // NO change
//            '\\$recur'    => [                    // Recursive test
//                '\\$recur1'    => '$recur1',
//                '\\$que\\_abc' => '$que.abc'
//            ]
//        ];
//
//        printf(
//            "%-16s%-16s%-16s%6s\n",
//            'OLD',
//            'NEW',
//            'EXPECTED',
//            'TEST'
//        );
//
//        foreach ( $testData as $key => $val ) {
//            //echo "VALUE IS: $val\n";
//            $newKey = self::getMongoIODLReplaceKey($key);
//            $success = ($newKey === $val) ? 'PASS' : 'FAIL';
//
//            printf(
//                "%-16s%-16s%-16s%6s\n",
//                $key,
//                $newKey,
//                $val,
//                $success
//            );
//        }
//    }

    /**
     * Modifies keys recursively
     * Also removes keys that have a null value (this is what ia-ds-iodl does)
     *
     * See: getMongoIODLReplaceKey function above
     *      ia-ds-iodl (GH Repo) StorageMongo.java function: decodeMapData
     *
     * @param array $input
     *
     * @return void
     */
    public static function modifyMongoIODLArrayKeys(array &$input): void
    {
        $replaceKeys = [];

        // Loops through finding keys to replace and recurs-ing into array elements
        foreach ($input as $key => &$value) {
            // Remove keys with null values (same as ia-ds-iodl)
            if ( is_null($value) ) {
                unset($input[$key]);
                continue;
            }

            // Recurse into arrays (map or numerical, doesnt matter)
            if ( is_array($value) ) {
                self::modifyMongoIODLArrayKeys($value);
            }

            // Search for key strings to modify if they match the regex
            if ( is_string($key) ) {
                $newKey = self::getMongoIODLReplaceKey($key);
                if ($newKey !== $key) {
                    $replaceKeys[$key] = $newKey; // remember these for later (not safe to do here)
                }
            }
        }

        unset($value); // extra careful

        // Now replace keys
        foreach ( $replaceKeys as $key => $newKey ) {
            $tmpVal = $input[$key]; // avoid using same name as temp variable above
            unset($input[$key]);
            $input[$newKey] = $tmpVal;
        }
    }

    /**
     * @param string $val
     * @param array  $found
     *
     * @return void
     */
    protected static function addTokensFromString(string $val, array &$found): void
    {
        static $REGEX_I18N_TOKEN_STRING = '/(?<I18N_CAPTURE>IA\.[A-Z0-9_.]+)/';

        $matches = [];

        preg_match_all($REGEX_I18N_TOKEN_STRING, $val, $matches, PREG_PATTERN_ORDER);

        foreach ( $matches['I18N_CAPTURE'] as $val ) {
            $found[$val] = 1;
        }
    }

    /**
     * @param array $input
     * @param array $found
     *
     * @return void
     */
    protected static function findAllTokensInArrayRecurse(array $input, array &$found): void
    {
        foreach ($input as $key => $val ) {
            if ( is_array($val) ) {
                self::findAllTokensInArrayRecurse($val, $found);
            } else if ( is_string($val) ) {
                self::addTokensFromString($val, $found);
            } else {
                // do nothing
            }

            // Also check the key
            if ( is_string($key) ) {
                self::addTokensFromString($key, $found);
            }
        }
    }

    /**
     * @deprecated
     * No longer functional due to complexity in iodl namespace/versioning
     *
     * @param array $request
     * @param array $response
     *
     * @return void
     * @throws APIException
     */
    protected static function setCachedCompilation(array $request, array $response): void
    {
        static $DEBUG_REGEX_CAPTURE = '/^Debug: cache hash ([A-Z0-9a-z]+)$/';
        $results = $response['result'] ?? [];

        // Look for string in result array like 'Debug: cache hash 34800022919450E80DCB0048B75FF49F'
        $matches = preg_grep($DEBUG_REGEX_CAPTURE, $results);

        foreach ($matches as $found) {
            // Capture the hash part from the string
            if ( preg_match($DEBUG_REGEX_CAPTURE, $found, $matches) ) {
                $cacheHash = $matches[1];
                $info = self::extractInfoFromRequest($request);
                // Remember this cache hash in memcache for next time
                self::getCacheClient()->set($info['INFO_MCKEY'], $cacheHash);
                break;
            }
            // There should only be one, but let the loop continue anyway
        }
    }

    /**
     * @deprecated
     * No longer functional due to complexity in iodl namespace/versioning
     *
     * Look to see if there is a previously saved md5-hash of this request in memcache
     *
     * @param array $request
     *
     * @return ?array
     * @throws Exception
     */
    protected static function getCachedCompilation(array $request): ?array
    {
        $info = self::extractInfoFromRequest($request);

        $compileHash = self::getCacheClient()->get($info['INFO_MCKEY']);
        if ( !empty($compileHash) ) {
            // Found md5 hash in memcache, now must find in mongo
            $mongoStore = self::getMongoStore($info['INFO_COLLECTION']);

            $_id = $info['INFO_ID'];

            // For some reason mongo does not allow arbitrary order of sub-keys under _id. So we need to make a filter
            $filter = [
                "_id.name" => $_id['name'],
                "_id.type" => $_id['type'],
                "_id.compileHash" => $compileHash
            ];
            // This may be null
            logToFileDebug("START mongoStore->getRecords");
            $doc = $mongoStore->getRecords($filter)[0] ?? null;
            logToFileDebug("STOP mongoStore->getRecords");
            if ( $doc ) {
                /**
                 * To process correctly we must transform raw cached content data into what the API response should be
                 *
                 * RAW CACHED DATA:
                 * content =>
                 *   dataMap => <JSON>
                 *   type => 'JSON'
                 *
                 * EXPECTED OUTPUT:
                 * content =>
                 *   data => <JSON ENCODED STRING>
                 *   type => 'JSON'
                 *   tokens => <Array of tokens as string values>
                 *
                 * NOTE!!!: In addition we must decode the map keys per ds-iodl. This is because
                 *          Mongo (in some versions) does not allow `$` or `.` in JSON keys.
                 *
                 * ORDER of post-processing:
                 * 1. Decode JSON keys according to ds-iodl
                 *    - MUST remove any keys with null values
                 *    - (starting) slash dollar to dollar:  "^\$..." -> "$..."
                 *    - (anywhere) slash underscore to dot: "\_" -> "."
                 * 2. Find all tokens
                 * 3. JSON encode output
                 */

                $contentIn = $doc['content'] ?? null;
                $dataMap = $contentIn['dataMap'] ?? [];
                $tokens = [];

                logToFileDebug("START modifyMongoIODLArrayKeys");
                self::modifyMongoIODLArrayKeys($dataMap);
                logToFileDebug("STOP modifyMongoIODLArrayKeys");

                logToFileDebug("START findAllTokensInArrayRecurse");
                self::findAllTokensInArrayRecurse($dataMap, $tokens);
                logToFileDebug("STOP findAllTokensInArrayRecurse");

                logToFileDebug("START contentIn encode");
                $contentOut = [
                    'dataJSON' => $dataMap,
                    'type' => $contentIn['type'],
                    'tokens' => array_keys($tokens)
                ];
                logToFileDebug("STOP contentIn encode");

                return [
                    'id' => $info['INFO_ID'],
                    'content' => $contentOut,
                    'result' => [
                        'Retrieved',
                        'Cache-Compiled-Direct',
                        "Debug: cache hash $compileHash"
                    ]
                ];
            }
        }

        // A (default) null response implies not found in cache
        return null;
    }

    /**
     * This function looks for the response content-data and modifies by adding dimension info
     *
     * @param $result - The api response
     * @param $iodlFileName - name of the iodl file
     *
     * @return bool - true if modified, false otherwise
     */
    protected function addDimensionsToContentData(array &$result, $iodlFileName): bool
    {
        //TODO Replace this with a different service call. Dimensions should be delivered by a different service.
        $apiVersion = '1-beta2';
        if (isset($result['content']['dataJSON'])) {
            //getting object out of IODL filename. e.g: fetch "vendor" from "ui/ap/vendor.iodl.list"
            //the convention for this pattern (ui/ap/vendor.list.iodl) is standard at this point and is covered in documentation for XG lists.
            //looking for last / from the string
            $start = strrpos($iodlFileName, '/');
            //looking for first . in the string
            $end = strpos($iodlFileName, '.', $start);
            if ($start !== false && $end !== false) {
                $currentEntity = substr($iodlFileName, $start + 1, $end - $start - 1);
                $dimensions = IADimensions::getDimensionObjectsInfo('gl', false);
                //custom dimensions
                $customDimensions = IADimensions::getCustomDimensionObjects();

                //we don't need the dimension of our own object
                if (isset($dimensions[$currentEntity])) {
                    unset($dimensions[$currentEntity]);
                }
                // Find/add label tokens in both dimension arrays
                foreach ($dimensions as $dimension) {
                    I18N::addToken($dimension['label']);
                }
                foreach ($customDimensions as $customDimension) {
                    I18N::addToken($customDimension['label']);
                }
                // Translate
                $translatedTokens = I18N::getText();
                logToFileInfo( __METHOD__ . "; dimensions count: " . count($dimensions) . "\n");
                $registryLoader = \RegistryLoader::getInstance($apiVersion);
                $isTypeBasedRegistry = $registryLoader->isTypeBasedRegistry();
                // Put the translation into the dimension labels
                foreach ($dimensions as &$dimension) {
                    $dimension['label'] = $translatedTokens[$dimension['label']];
                    $apiObject = $registryLoader->getObjectNameForMappedToName($dimension['entity'], true);
                    $dimension["apiObject"] = $apiObject;
                    $applicationName = explode('/', $apiObject)[0];
                    $dimension["applicationName"] = $applicationName;
                }
                logToFileInfo( __METHOD__ . "; customDimensions count: " . count($customDimensions) . "\n");
                unset($dimension);
                foreach ($customDimensions as &$customDimension) {
                    $customDimension['label'] = $translatedTokens[$customDimension['label']];
                    // WARNING: Call to 'getNameAdjustedForNamespace' assumes that custom dimension entity is a valid dimension (no validation)
                    $apiObject = APIDynamicObjectSchemaLoader::getNameAdjustedForNamespace($customDimension['entity'], $isTypeBasedRegistry);
                    // Original call below uses 'getObjectNameForMappedToName', but is slower.
                    // $apiObject = $registryLoader->getObjectNameForMappedToName($customDimension['entity']);
                    logToFileInfo( __METHOD__ . "; Custom Dimension entity " . $customDimension['entity'] . " -> Related API Object: $apiObject\n");
                    $customDimension["apiObject"] = $apiObject;
                    $applicationName = explode('/', $apiObject)[0];
                    $customDimension["applicationName"] = $applicationName;
                }
                unset($customDimension);

                // decode the content data for modification
                $retVal = $result['content']['dataJSON'];

                // Add both dimensions and custom dimensions to the result data
                //$retVal['apiObjectDimensions'][$currentEntity] = $dimensions;
                //$retVal['apiObjectCustomDimensions'][$currentEntity] = $customDimensions;

                // merging the two dimensions arrays based on https://jira.sage.com/browse/IA-125513
                $retVal['apiObjectDimensions'][$currentEntity] = array_merge_recursive($dimensions, $customDimensions);
                // we have to overwrite the value we get on the data model for the costtype entity in order to
                // properly render the values in the field picker.
                // this is a temporary solution - tracked with https://jira.sage.com/browse/IA-129301
                if (isset($retVal['apiObjectDimensions']['fixedasset']['costtype']['entity'])) {
                    $retVal['apiObjectDimensions']['fixedasset']['costtype']['entity'] = 'cost-type';
                }
                // Encode the dimensions back into the result content data
                $result['content']['dataJSON'] = $retVal;
                return true;
            }
        }

        return false;
    }


    /**
     * @deprecated
     * @param array  $request request body (may be modified)
     * @param string $msg
     *
     * @return bool
     */
    protected function modifyRequestWithPreferences(array &$request, string &$msg): bool
    {
        $msg = "";

        if (
               isset($request['options']['compile'])
            && $request['options']['compile'] === true
        ) {
            if ( ! isset($request['prepend']) ) {
                $prependPref = $this->buildPrependPreferences();
                if ( false === $prependPref ) {
                    $msg = "Cannot prepend with preferences: buildPrependPreferences failed.";
                    logToFileError($msg);
                    return false;
                }

                $request['prepend'] = $prependPref;
                $msg = "Prepended UI preferences";
            } else {
                $msg = "Cannot prepend with preferences: 'prepend' body is set by API call.";
                logToFileWarning($msg);
                return false;
            }
        }

        return true;
    }

    /**
     * Append the preferences to the content that comes back from IODL compilation
     * NOTE: this is done _after_ ds-iodl call to avoid changing the cache hash
     *
     * Location in API response: ["ia::result"].content.data.uiSettings
     *
     * To verify this in testing, grab 'content.data' from the response and use:
     * jq '.data | fromjson | .uiSettings' <file_containing_content.data>
     *
     * @return bool
     */
    protected function modifyResponseWithPreferences(&$result): bool
    {
        $UIPrefService = new UiPreferencesService();
        $uiSettings = $UIPrefService->get([], [])['uiSettings'] ?? [];

        if ( empty($uiSettings) ) {
            logToFileError(
                "Unable to find preferences from 'uiSettings' manager."
            );
            return false;
        }

        try {
            // decode the content data for modification
            $retVal = $result['content']['dataJSON'];

            // Get the current content.data.uiSettings (if any) for merging
            $uiSettingsCurrent = $retVal['uiSettings'] ?? [];

            // Modify/Add the uiSettings
            $retVal['uiSettings'] = array_merge_recursive($uiSettingsCurrent, $uiSettings);

            // encode the changes back into the response
            $result['content']['dataJSON'] = $retVal;
        } catch ( JsonException $e ) {
            logToFileError(__METHOD__ . "; JSON exception when encoding/decoding content.data: " . $e->getMessage());
            return false;
        }

        return true;
    }

    /**
     * @deprecated
     *
     * @return false|string
     */
    protected function buildPrependPreferences()
    {
        $UIPrefService = new UiPreferencesService();
        $prefs = $UIPrefService->get([], []);

        if ( ! is_array($prefs) ) {
            logToFileError(
                "Unable to find preferences from 'uiSettings' manager."
            );
            return false;
        }

        $prependArr = [
            'Globals' => [
                'Params' => $prefs
            ]
        ];

        try {
            return json_encode($prependArr, JSON_UNESCAPED_SLASHES | JSON_THROW_ON_ERROR);
        } catch ( JsonException $e ) {
            logToFileError("Unable to json_encode preferences from UI Preferences Service.");
            return false;
        }
    }


    /**
     * Checks if tokens were found in response, then attempts to resolve them using ds-text
     * - tokens from ds-iodl will are response->content.tokens
     * - resolved tokens will be returned as response->content.tokenMap
     *
     * @param array $result - The API response from IODL
     *
     * @return void
     * @throws I18NException
     */
    protected function processTokens(array &$result): void
    {
        $tokens = $result['content']['tokens'] ?? [];

        if ( !empty($tokens) ) {
            $errMsg = "";
            $tokenMap = $this->getTokenMap(I18N::tokenArrayToObjectArray($tokens), $errMsg);

            if ( false === $tokenMap ) {
                $result['result'][] = "Cannot get token values from text service. Reason: '$errMsg'";
            } else if ( is_array($tokenMap) ) {
                // Set the tokenMap into the content element
                $result['content']['tokenMap'] = $tokenMap;
                // Keep the original tokens for now (useful for debugging)
                // unset($result['tokens']);
                if ( !empty($errMsg) ) {
                    $result['result'][] = "Received token values from text service with error. Reason: '$errMsg'";
                }
            } else {
                // This should not happen
                logToFileError(
                    sprintf(
                        "Unexpected return type '%s' from getLocalizedText, expected array or false",
                        gettype($tokenMap)
                    )
                );
            }
        } else {
            $result['content']['tokens'] = [];
        }
    }


    /**
     * Checks if there are any UI preferences set in mongo for the current iodl file name
     *
     * @param array $result - The API response from IODL
     *
     * @return void
     * @throws IAException
     */
    protected function processUIPreferences(array &$result, array $keys): void
    {
        $store = KVSFactory::get('ui_core'); // collection will be "KV.ui_core"
        foreach ($keys as $key) {
            $storeValue = $store->get($key);
            if (!$storeValue) {
                $storeValue = null;
            }
            $result['content']['keyValues'][$key] = $storeValue;
        }
    }


    /**
     * @param array  $tokens
     * @param string $errMsg
     *
     * @return array|bool
     * @throws APIException
     */
    protected function queryTextService(array $tokens, string &$errMsg): array|bool
    {
        // ds-text 0.0.32-SNAPSHOT URL endpoint: ia-ds-text/ia/api/v1/services/text/retrieve/query
        static $TEXT_DS_VERSION = '1i';
        static $TEXT_DS_API_URL_PREFIX = 'ia-ds-text';
        static $TEXT_DS_DOMAIN = 'text';
        static $TEXT_DS_SERVICE = 'retrieve';
        static $TEXT_DS_VERB = 'query';

        /**
         * See: TrustedAPIFunctionServiceClient::__construct(...)
         *
         * URL using hosted K8s:
         * https://dev-us-0.ds.intacct.com:443/ia-ds-text/ia/api/v1/services/text/retrieve/query
         */
        $trustedAPI = new DomainServiceAPIFunctionServiceClient(
            $TEXT_DS_API_URL_PREFIX,
            $TEXT_DS_DOMAIN,
            $TEXT_DS_SERVICE,
            $TEXT_DS_VERSION
        );

        $errMsg = "";

        $body = [
            'skuId' => 'IA',
            'appId' => 'IA',
            'locale' => getUserLocale() ?? LOCALE_DEFAULT_LOCALE,
            'cny' => 0,   // TODO: Use real CNY once text service handles non zero to find default if not for that CNY
            'entries' => $tokens
        ];

        try {
            /**
             * HACK To work around different service configurations (i.e., running multiple DS on different local ports)
             *
             * Start ds-text with something like:
             * - java11 -Dserver.servlet.context-path=/ia-ds-text/ia -Dserver.port=8194 -Dmanagement.server.port=8195 -jar ia-ds-text-0.0.18-SNAPSHOT.jar
             *
             * Add a setter function to TrustedAPIFunctionServiceClient.cls:
             *   public function setURL(string $url) { $this->url = $url; }
             *
             * Set a custom url to the service, that uses the port chosen (above). Example:
             *   $trustedAPI->setURL("http://localhost/ia-ds-text/ia/api/v1i/services/text/retrieve/query");
             */
            $response = $trustedAPI->invokeFunction($TEXT_DS_VERB, $body, APIConstants::API_OPERATION_EXECUTE_RAW);
        } catch ( APIException $e ) {
            if ($e->hasAPIError()) {
                $errMsg = $e->getAPIError()->getMessage();
            } else {
                $errMsg = $e->getMessage();
            }
            return false;
        }

        if ( !isset($response['textEntries']) ) {
            $errMsg = "ds-text response element 'textEntries' missing";
            return false;
        }

        // Convert to token map
        $tokenMap = [];
        foreach ($response['textEntries'] as $entry) {
            // TODO: Add error checking (for id/value) once this function is enabled
            $tokenMap[$entry['id']] = $entry['value'];
        }

        return $tokenMap;
    }

    /**
     * Wrapper to get localized text from tokens
     * - can use I18N or ds-text
     *
     * @param array $tokens list of tokens to evaluate/find text values
     * @return array|false - the array is a token map, token_id => resolved token value
     */
    protected function getTokenMap(array $tokens, string &$errMsg): array|bool
    {
        // ds-text
        //return $this->queryTextService($tokens, $errMsg);

        // I18N
        try {
            return I18N::getTokensForArray($tokens);
        } catch (I18NException $e) {
            $errMsg = $e->getMessage();
            return false;
        }
    }

    /**
     * Info about IODL service
     *
     * API Path:
     * - {{BASEURL}}/api/v0/services/iodl/file-service/info
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function info(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        return $this->invoke(__FUNCTION__, $request);
    }

    /**
     * Delete IODL
     *
     * API Path:
     * - {{BASEURL}}/api/v0/services/iodl/file-service/delete
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function delete(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        static $method = __FUNCTION__;
        throw new APIException("Method '$method' not allowed");
        //return $this->invoke(__FUNCTION__, $request);
    }

    /**
     * Retrieve IODL
     *
     * API Path:
     * - {{BASEURL}}/api/v0/services/iodl/file-service/retrieve
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function retrieve(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        return $this->invoke(__FUNCTION__, $request);
    }


    /**
     * Compile IODL
     *
     * NOTE: Uses new API, for NG UI Namespace/Versioning
     *       id.name:    should be in the form of <NS>/<VER>/path-to-file
     *       id.version: should not be set
     *       id.type:    should not be set (or default "global")
     *       
     * API Path:
     * - {{BASEURL}}/api/v0/services/iodl/file-service/compile
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function compile(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        return $this->invokeForCompile(__FUNCTION__, $request);

      /*
      if ($request['options']['compile'] ?? false) {
            return $this->invokeForCompile(__FUNCTION__, $request);
        } else {
            //we need this setup in payload for plain iodl
            $request['options']['compile'] = false;
            unset($request['options']['getKeyValues']);
            return $this->invoke(__FUNCTION__, $request);
        }
        */
    }

    /**
     * Source method for IODL
     *
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[] (returns plain text IODL file)
     */
    public function source(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        return $this->invoke(__FUNCTION__, $request);
    }

    /**
     * Register IODL
     * uploads a registry file to the database
     * API Path:
     * - {{BASEURL}}/api/v0/services/iodl/file-service/compile
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function register(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        return $this->invokeForCompile(__FUNCTION__, $request);
    }


    /**
     * Deploy IODL
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function deploy(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        return $this->invoke(__FUNCTION__, $request);
    }

    /**
     * Store IODL
     *
     * API Path:
     * - {{BASEURL}}/api/v0/services/iodl/file-service/store
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function store(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        static $method = __FUNCTION__;
        throw new APIException("Method '$method' not allowed");
        //return $this->invoke(__FUNCTION__, $request);
    }

    /**
     * Anonymous IODL
     *
     * API Path:
     * - {{BASEURL}}/api/v0/services/iodl/file-service/anon
     *
     * @param array $request     request body
     * @param array $extraParams extra params
     *
     * @return null|string[]
     */
    public function anon(array $request, /** @noinspection PhpUnusedParameterInspection */ array $extraParams): ?array
    {
        return $this->invoke(__FUNCTION__, $request);
    }
}
