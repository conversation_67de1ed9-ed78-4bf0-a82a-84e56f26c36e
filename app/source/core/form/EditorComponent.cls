<?php

/**
 * Class EditorComponent
 */
abstract class EditorComponent
{
    /**
     * @var array $params
     */
    // used in many places directly and set in AvaPartnerSyncEventLister so making it public after talkign with <PERSON>.
    public $params;
    /**
     * @var FormEditor $editor
     */
    public $editor;
    /**
     * @var EditorContainer $parent
     */
    protected $parent;
    /**
     * @var string $componentType
     */
    private $componentType;
    /**
     * @var bool $renamed
     */
    private $renamed;

    /**
     * @var array $writable_properties
     */
    private static $writable_properties = array('parent', 'componentType');

    /**
     * @param array      $params
     * @param FormEditor $editor
     */
    public function __construct(&$params, FormEditor $editor)
    {
        if (isset($params['include']) && $params['include'] != '') {
            $nparams = false;
            if (isset($params['componentType']) && $params['componentType'] != '') {
                $type = $params['componentType'];

                $id = null;
                /** @noinspection PhpUnusedLocalVariableInspection */
                $path = null;
                $idtype = null;
                if (isset($params['id']) && $params['id'] != '') {
                    $id = $params['id'];
                    $idtype = 'id';
                } else if (isset($params['path']) && $params['path'] != '') {
                    $id = $params['path'];
                    $idtype = 'path';
                }

                $top = null;
                $ok = @include $params['include'];
                if ($ok && isset($top)) {
                    $inclusion = $this->findChildToInclude($top, $type, $idtype, $id);
                    if ($inclusion) {
                        $nparams = $inclusion;
                    }
                }
                if (isset($params['title']) && $params['title'] != '') {
                    /** @noinspection OnlyWritesOnParameterInspection */
                    $nparams['title'] = $params['title'];
                }
            }
            $this->params = &$nparams;
        } else {
            $this->params = &$params;
        }

        $this->renamed = false;
        $this->editor = $editor;
    }

    /**
     * @param array  $top
     * @param string $type
     * @param string $idtype
     * @param int    $id
     *
     * @return bool
     */
    private function findChildToInclude($top, $type, $idtype, $id)
    {
        $useId = (isset($idtype) && $idtype != '');
        $found = null;
        foreach ($top as $key => $child) {
            if ($key === $type) {
                if ($useId) {
                    foreach ($child as $element) {
                        if (isset($element[$idtype]) && $id === $element[$idtype]) {
                            $found = $element;
                            break;
                        }
                    }
                } else {
                    $found = $child[0];
                }
            } else if (is_array($child)) {
                $found = $this->findChildToInclude($child, $type, $idtype, $id);
            }
            if ($found) {
                break;
            }
        }

        return $found;
    }

    /**
     * @return array
     */
    public function getParams()
    {
        return $this->params;
    }

    /**
     * @return EditorContainer
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * @return string
     */
    public function getComponentType()
    {
        return $this->componentType;
    }

    /**
     * @param string $type
     */
    public function setComponentType($type)
    {
        $this->componentType = $type;
    }


    /**
     * @param array  $_pattern
     * @param string|null $type
     * @param array  $found
     *
     * @return bool
     *
     * //TODO: obosolete?
     */
    public function findComponentsX($_pattern, $type, &$found)
    {
        $match = false;
        $isSameType = EditorComponentFactory::isMatchingType($this->componentType, $type);
        if ($isSameType) {
            $type = null;
        }
        if (!$type) {
            $match = true;
            if (is_array($_pattern)) {
                foreach ($_pattern as $k => $v) {
                    // WARNING FIX NEEDED - what should happen if $_params[$k] doesn't exist
                    if ($this->params[$k] != $v) {
                        $match = false;
                        break;
                    }
                }
            }
            if ($match) {
                $found[] = $this;
            }
        }
        return $match;
    }

    /**
     * @param array  $_pattern
     * @param string|null $type
     * @param array  $found
     *
     * @return bool
     */
    public function findComponents($_pattern, $type, &$found)
    {
        $match = false;
        $isSameType = EditorComponentFactory::isMatchingType($this->componentType, $type);
        if ($isSameType) {
            $type = null;
        }

        if (!$type) {
            $match = $this->innerFindComponents($_pattern);
            if ($match) {
                $found[] = $this;
            }
        }
        return $match;
    }

    /**
     * @param array $_pattern
     *
     * @return bool
     */
    protected function innerFindComponents($_pattern)
    {
        $match = true;
        if (is_array($_pattern)) {
            foreach ($_pattern as $k => $v) {
                if (($this->params[$k] ?? null) != $v) {
                    $match = false;
                    break;
                }
            }
        }
        return $match;
    }

    public function expand()
    {
    }

    /**
     * This method is called when the component is removed from the layout.
     * The component should clean whatever it has previously set on the other layout objects.
     */
    protected function remove()
    {
    }

    /**
     * @param array $object
     */
    public function instantiate(/** @noinspection PhpUnusedParameterInspection */ &$object)
    {
        if ($this->parent) {
            $parentReadOnly = $this->parent->params['readonly'] ?? null;
            if (!($this->params['forceReadWrite'] ?? null) && $parentReadOnly) {
                $this->params['readonly'] = $parentReadOnly;
            }
            $watermark = $this->parent->params['watermark'] ?? null;
            if ($watermark && (!(isset($this->params['watermark']) && $this->params['watermark'] != ''))) {
                $this->params['watermark'] = $watermark;
            }
        }

        if ($this->params['forceReadWrite'] ?? null) {
            $this->params['readonly'] = false;
        }

        if (isset($this->params['noTerminologyReplacement'])) {
            $this->renamed = true;
        }

        if (isset($this->params['watermark'])) {
            if (0 == strcasecmp($this->params['watermark'], "true")) {
                if ($this->params['fullname'] ?? null) {
                    $this->params['watermark'] = $this->params['fullname'];
                } else if ($this->params['desc'] ?? null) {
                    $this->params['watermark'] = $this->params['desc'];
                } else {
                    unset($this->params['watermark']);
                }
            } else if (is_array($this->params['watermark'])) {
                $this->params['watermark'] = '';
            } else if (0 == strcasecmp($this->params['watermark'], "false")) {
                unset($this->params['watermark']);
            }
        }

        if (!$this->renamed) {
            $dictionary = $this->editor->getDictionary();
            if (!isset($this->params['renameable']) || (!empty($this->params['renameable']) && 'false' !== $this->params['renameable'])) {
                if ($this->params['fullname'] ?? null) {
                    if (!isset($this->params['udd']) || ($this->params['udd'] != 1 || $this->params['udd'] != true)) {
                        $this->params['fullname'] = $dictionary->GetRenamedText($this->params['fullname']);
                    }
                }
                if ($this->params['label'] ?? null) {
                    if (($this->params['udd'] ?? null) != 1 || ($this->params['udd'] ?? null) != true) {
                        $this->params['label'] = $dictionary->GetRenamedText($this->params['label']);
                    }
                }
                if ($this->params['title'] ?? null) {
                    if (!isset($this->params['udd']) || ($this->params['udd'] != 1 || $this->params['udd'] != true)) {
                        $this->params['title'] = $dictionary->GetRenamedText($this->params['title']);
                    }
                }
                if (isset($this->params['watermark'])) {
                    // Remove any html tags.
                    $watermark = preg_replace('%<(style|script)[^<>]*>.*?</\1>|</?[a-z][a-z0-9]*[^<>]*>|<!--.*?-->%sim', ' ', $this->params['watermark']);
                    $watermark = preg_replace('/\s{2,}/m', ' ', $watermark);
                    $this->params['watermark'] = $dictionary->GetRenamedText($watermark);
                }
            }
            if ($this->params['helpText'] ?? null) {
                $this->params['helpText'] = I18N::getSingleToken($this->params['helpText']);
            }
            if ($this->params['hreftxt'] ?? null) {
                $this->params['hreftxt'] = I18N::getSingleToken($this->params['hreftxt']);
            }

            $this->renamed = true;
        }
    }

    /**
     * @param array $object
     */
    public function finalize(&$object)
    {
    }

    /**
     * @param array $object
     * @param string|array $path
     *
     * @return mixed
     */
    protected function getValueByPath($object, $path)
    {
        if (!is_array($path)) {
            $val = EntityManager::AccessByPath($object, $path);
        } else {
            $val = $object;
            foreach ($path as $pathElement) {
                $val = EntityManager::AccessByPath($val, $pathElement);
            }
        }

        return $val;
    }

    /**
     * @param array  $object
     * @param string|array $path
     * @param mixed  $value
     */
    protected function setValueByPath(&$object, $path, $value)
    {
        if (!is_array($path)) {
            EntityManager::SetByPath($object, $path, $value);
        } else {
            $pathKey = $path[0];
            array_shift($path);
            if (count($path) == 0) {
                $object[$pathKey] = $value;
            } else {
                $object[$pathKey] = array();
                $this->setValueByPath($object[$pathKey], $path, $value);
            }
        }
    }

    /**
     * @param string $name
     * @param bool   $goUp
     *
     * @return mixed
     */
    public function getProperty($name, $goUp = true)
    {
        if (isset($this->params[$name])) {
            return $this->params[$name];
        } else if ($goUp && $this->parent) {
            return $this->parent->getProperty($name, $goUp);
        } else {
            return null;
        }
    }

    /**
     * @param array           $obj
     * @param string|string[] $name
     * @param mixed           $value
     * @param bool            $overwrite
     */
    private function innerSetProperty(&$obj, $name, $value, $overwrite = true)
    {
        if (!is_array($name)) {
            $name = array($name);
        }
        $length = count($name);
        $currObject =& $obj;
        for ($ix = 0; $ix < $length - 1; $ix++) {
            $key = $name[$ix];
            $currObject =& $currObject[$key];
        }
        $name = $name[$length - 1];
        if (!array_key_exists($name, $currObject ?? []) || $overwrite) {
            $currObject[$name] = $value;
        }
    }

    /**
     * @param string|string[] $name
     * @param mixed           $value
     * @param bool            $overwrite
     */
    public function setProperty($name, $value, $overwrite = true)
    {
        $this->innerSetProperty($this->params, $name, $value, $overwrite);
    }

    /**
     * @return mixed
     */
    abstract public function getLabel();

    /**
     * @param array $properties
     * @param bool  $overwrite
     */
    public function propagateProperties($properties, $overwrite = true)
    {
        foreach ($properties as $name => $value) {
            $this->setProperty($name, $value, $overwrite);
        }
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->componentType;
    }

    /**
     * @param string $name
     *
     * @return null|string
     */
    public function &__get($name)
    {
        if ( ! isset($this->$name) ) {
            // Must return variable since this method returns by-ref
            $x = null;
            return $x;
        }

        return $this->$name;
    }

    /**
     * @param string $name
     *
     * @return bool
     */
    public function __isset($name)
    {
        return isset($this->$name);
    }

    /**
     * @param string $name
     */
    public function __unset($name)
    {
        if (in_array($name, self::$writable_properties)) {
            unset($this->$name);
        }
    }

    /**
     * @param array $array
     *
     * @return array
     */
    protected function processArrayForXML($array)
    {
        $newArray = array();
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $value = $this->processArrayForXML($value);
            }
            $newArray[$key] = $value;
        }
        return $newArray;
    }

    /**
     * @param array $array
     *
     * @return array
     */
    public function showArray($array)
    {
        $code = array();
        foreach ($array as $key => $value) {
            $xmlKey = str_replace('.', '__', $key);
            if (is_array($value)) {
                $value = $this->processArrayForXML($value);
            }
            $code[$xmlKey] = $value;
        }
        return $code;
    }

    /**
     * This function generates the code that "draws" the component. The generated code is returned to the caller.
     *
     * @return array representing the XML node
     */
    public function show()
    {
        $code = array();
        $componentType = ucwords($this->getType());
        $code['control'] = $componentType;
        if (array_key_exists('clazz', $this->params)) {
            $code['clazz'] = $this->params['clazz'];
        }
        if ($this->params['id'] ?? null) {
            $code['id'] = $this->params['id'];
        }
        $code = $this->addShowProperties($code);
        return $code;
    }

    /**
     * @return null
     */
    public function gatherAllFieldParams()
    {
        return null;
    }

    /**
     * @param array $code
     *
     * @return array
     */
    protected function addShowProperties($code)
    {
        $ignoreProps = $this->getShowIgnoreProperties() ?? [];

        if (!is_array($code)) {
            $code = [];
        }
        if ((array_key_exists(0, $ignoreProps) && !($ignoreProps[0] === '*')) || empty($ignoreProps)) {
            foreach ($this->params as $path => $value) {
                if (in_array((string)$path, $ignoreProps)) {
                    continue;
                }
                if (is_array($value)) {
                    $value = $this->showArray($value);
                }

                $xmlKey = str_replace('.', '__', $path);
                $xmlKey = str_replace('#', '__', $xmlKey);

                if (isset($value) && $value !== '') {
                    $code[$xmlKey] = $value;
                }
            }
        }
        return $code;
    }

    /**
     * @return array
     */
    protected function getShowIgnoreProperties()
    {
        return array();
    }

    /**
     * @param array  $searchParams
     * @param string $type
     *
     * @return bool|EditorComponent
     */
    public function findFirstComponent($searchParams, $type = null)
    {
        $matches = array();
        $this->findComponents($searchParams, $type, $matches);
        if ($matches) {
            return $matches[0];
        }

        return false;
    }

    /**
     * @param array  $searchParams
     * @param string $type
     *
     * @return bool|EditorComponent
     */
    public function findFirstNonHiddenComponent($searchParams, $type = null)
    {
        $matches = array();
        $this->findComponents($searchParams, $type, $matches);
        if (count($matches) > 1) {
            foreach ($matches as $match) {
                if (!isset($match->params['hidden']) || !$match->params['hidden']) {
                    return $match;
                }
            }
        }
        else {
            return $matches[0];
        }

        return false;
    }

    /**
     * @param array  $searchParams
     * @param array  $propVals
     * @param string $type
     */
    public function findAndSetProperty($searchParams, $propVals, $type = EditorComponentFactory::TYPE_FIELD)
    {
        if ( ! empty($searchParams) && ! empty($propVals) ) {
            // Only continue if the search parameters AND property values are provided
            $matches = [];
            $this->findComponents($searchParams, $type, $matches);
            foreach ( $matches as $match ) {
                foreach ( $propVals as $property => $value ) {
                    $match->setProperty($property, $value);
                }
            }
        }
    }

    /**
     * @param array  $searchParams
     * @param array  $eventVals
     * @param string $type
     */
    public function findAndSetEvent($searchParams, $eventVals, $type = EditorComponentFactory::TYPE_FIELD)
    {
        if ( ! empty($searchParams) && ! empty($eventVals) ) {
            // Only continue if the search parameters AND event values are provided
            $matches = [];
            $this->findComponents($searchParams, $type, $matches);
            foreach ( $matches as $match ) {
                foreach ( $eventVals as $event => $value ) {
                    $match->setEvent($searchParams, $event, $value);
                }
            }
        }
    }

    /**
     * @param array  $_pattern
     * @param string $event
     * @param string $value
     */
    protected function setEvent(/** @noinspection PhpUnusedParameterInspection */ $_pattern, $event, $value)
    {
        if (!isset($this->params['events'])) {
            $this->params['events'] = [];
        }
        $this->params['events'][$event] .= $value;
    }

    /**
     * @param string $indent
     */
    public function dump($indent = '')
    {
        eppp_p($indent . $this->getType() . ' - ' . $this->params['id']);
    }
}
