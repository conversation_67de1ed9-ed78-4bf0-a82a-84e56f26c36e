<?php
//=============================================================================
//
//	FILE:			viewForDelete.phtml
//	AUTHOR:			odysseas
//	DESCRIPTION:		custom version of submit.phtml used to confirm
//				deletion of a particular component
//
//=============================================================================

require_once 'util.inc';

import('Request');
import('ManagerFactory');
import('EntityManager');
import('Editor');

Init();

$entity = Request::$r->_it;
$verb   = Request::$r->_do;
//epp("verb = $verb, entity = $entity");

$editor = GetEntityEditor($entity);
$html = $editor->viewForDelete($_REQUEST['_r']);

if ($html === false) {
    Globals::$g->gUIUtils->ShowError();
}
else {
    echo $html;
}

