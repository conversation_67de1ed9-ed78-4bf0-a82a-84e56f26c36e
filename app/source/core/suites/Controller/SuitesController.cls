<?php

class SuitesController
{
    const APP_HOMEPAGE_IODL = 'core/stable/uiComponents/app-homepage.iodl';
    
    /**
     * @return string
     * @throws Exception
     */
    public function renderAppHomepage(): string
    {
        $host = $this->getHost();
        $apiHost = $this->getApiEndpoint();
        $apiDsHost = $this->getApiDsEndpoint();
        $clientId = $this->getIntacctUIClientIdWrapper();
        $accessToken = $this->createAccessTokenByClientIdWrapper($clientId);
        $sess = $this->getSessionKey();
        $isLive = $this->getGlobalIsLive();
        
        return "<script>
            const pageId = '" . self::APP_HOMEPAGE_IODL . "';
            const isPopup = false;
            const title = 'App Homepage';
            const scheme = 'https';
            const host = '" . util_urlEncode($host) . "';
            const port = '';
            const method = 'formPost';
            const apiHost = '" . $apiHost . "';
            const apiDsHost = '" . $apiDsHost . "';
            const uiServiceHost = '" . $apiDsHost . "';
            const iodlSandbox = '" . $this->determineIODLSandboxWrapper() . "';
            const accessTokenInfo = " . json_encode($accessToken) . "
            const isLive = '" . $isLive . "';
            const session = '" . $sess . "';
            const done = document.location.href;
            const version = 'lister';
            const customApiManager = 'SAGE_INTACCT';
            const actionUiList = '';
            const readtime = '" . urlencode($this->ServeCurrentTimestampWrapper()) . "';
            const viewDoneUrlParam = '';
            const editDoneUrlParam = '';
            const createDoneUrlParam = '';
            const genericActionDoneUrlParam = '';
            const isCreateAllowed = '';
            const isEditAllowed = '';
            const xgShowTurnOffBeta = false;
            const apiObject = '';
            const uuidValue = '';
            const journalID = '';
            const documentType = '';
            const i18nDisabled = false;

            window.xg = {
                pageId: pageId, title, version, scheme, host, port, method, customApiManager, actionUiList, i18nDisabled, isPopup,
                disableGlobalPreloader: true,
                disableTurnOnBetaError: true,
                configParams: {
                    scheme,
                    port,
                    path: 'v1-beta2/services/user-interface/core/composite',
                    host: uiServiceHost,
                    method: 'post',
                    authProtocol: 'oauth2',
                    tokenType: 'Bearer',
                    withCredentials: true,
                    accessTokenInfo,
                    apiObjectParams: {
                        formatBodyParams: {
                            id: { name: pageId, type: 'global' },
                            options: {
                                cache: false,
                                sandbox: iodlSandbox,
                            },
                        },
                    },
                },
                custom: {
                    apiObjectInfo: {
                        apiObject: apiObject,
                        uuid: 'key',
                        uuidValue: uuidValue
                    },
                    host,
                    apiHost,
                    apiDsHost,
                    uiServiceHost,
                    session,
                    done,
                    sandbox: iodlSandbox,
                    isCreateAllowed,
                    isEditAllowed,
                    isLive,
                    accessTokenInfo,
                    readtime,
                    viewDoneUrlParam,
                    editDoneUrlParam,
                    createDoneUrlParam,
                    genericActionDoneUrlParam,
                    xgShowTurnOffBeta,
                    journalID,
                    documentType
                },
            };
        </script>";
    }
    
    /**
     * @return string
     */
    protected function getHost() : string
    {
        return $this->getServerHttpHost() . $this->RootPathWrapper() . "/acct";
    }
    
    /**
     * @return string
     */
    protected function getApiEndpoint() : string
    {
        return $this->getKongEndpointWrapper() . $this->RootPathWrapper() . "/api";
    }
    
    /**
     * @return string
     */
    protected function getApiDsEndpoint() : string
    {
        return $this->getKongEndpointWrapper() . "/ia/api";
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function getSessionKey()
    {
        return Request::$r->_sess;
    }
    
    /**
     * @codeCoverageIgnore
     * @return bool
     */
    protected function getGlobalIsLive()
    {
        return Globals::$g->islive;
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function getServerHttpHost() : string
    {
        return $_SERVER['HTTP_HOST'];
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function RootPathWrapper() : string
    {
        return RootPath();
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function getIntacctUIClientIdWrapper() : string
    {
        return AccessToken::getIntacctUIClientId();
    }
    
    /**
     * @codeCoverageIgnore
     * @param string $clientId
     *
     * @return array
     */
    protected function createAccessTokenByClientIdWrapper(string $clientId) : array
    {
        return AccessToken::createAccessTokenByClientId($clientId);
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function getKongEndpointWrapper()
    {
        return XgRequest::getKongEndpoint();
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function determineIODLSandboxWrapper() : string
    {
        return XgRequest::determineIODLSandbox();
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function ServeCurrentTimestampWrapper() : string
    {
        return ServeCurrentTimestamp();
    }
}