<?php

/**
 * File GetListBatch.cls contains the class GetListBatch
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Represents a batch of records to be fetched. Subclasses decide batch boundaries
 */
abstract class GetListBatch implements IGetListBatch
{
    /**
     * @var string $entity
     */
    protected $entity;

    /**
     * @var string $readTimestamp
     */
    protected $readTimestamp;

    /**
     * @var int $pageSize
     */
    protected $pageSize;

    /**
     * @var int $batchSize
     */
    protected $batchSize;

    /**
     * @var int $batchNumber
     */
    protected $batchNumber = 0;

    /**
     * @var int $maxRec
     */
    protected $maxRec;

    /**
     * @var int $minRec
     */
    protected $minRec;

    /**
     * @var int $currMinRec
     */
    protected $currMinRec;

    /**
     * @var int $currMaxRec
     */
    protected $currMaxRec;

    /**
     * @var bool $nodata
     */
    protected $nodata = false;

    /**
     * @var array $params
     */
    private $params = null;


    /**
     * @var int $optimizedBatchSize;
     */
    protected $optimizedBatchSize = 99999;


    /* @var array $recentRecords */
    protected $recentRecords;


    /**
     * @param string $entity
     * @param int    $readTimestamp
     * @param int    $pageSize
     * @param array  $params
     */
    function __construct($entity, $readTimestamp, $pageSize, $params = array())
    {
        if (!isset($entity)) {
            throw new Exception("entity is required.");
        }

        if (!is_numeric($pageSize)) {
            throw new Exception("Invalid page size. It needs to be a number.");
        }

        $this->entity = $entity;
        $this->pageSize = $pageSize;
        $this->params = $params;

        $this->batchSize = $pageSize * DdsJobRunner::DDS_BATCH_MULTIPLIER;
        $this->optimizedBatchSize = DdsJobRunner::DDS_PAGESIZE * 10 - 1;
        $this->readTimestamp = $readTimestamp;
        $this->recentRecords = [];
        if (DDS_DEBUG == 1) {
            impp('$readTimestamp', $this->readTimestamp);
        }

        // Ensure we retrieve entity owned records as well
        $ok = SetReportViewContext();
        if (!$ok) {
            throw new Exception("Error setting ReportViewContext in DB");
        }

        $this->initialize();
    }

    /**
     * Sets initiate state
     */
    protected function initialize()
    {
        $this->setMinMaxRec();

        if (!$this->nodata) {
            $this->currMinRec = $this->getMinRec();
            if ((DdsSubscription::isDdsSimpleOptimizationEnabled() != true)
                || (DdsJobRunner::NON_STANDARD_ENTITIES[strtolower($this->getEntity())])) {
                if ($this->getMinRec() + $this->getBatchSize() > $this->getMaxRec()) {
                    $this->currMaxRec = $this->getMaxRec() + 1;
                } else {
                    $this->currMaxRec = $this->getMinRec() + $this->getBatchSize();
                }
            } else {
                $this->currMaxRec = $this->fetchNextMaxRec();
            }

            if (DDS_DEBUG == 1) {
                impp('Batch entity', $this->getEntity());
                impp('Batch constructor $this->getMinRec()', $this->getMinRec());
                impp('Batch constructor $this->getMaxRec()', $this->getMaxRec());
                impp('Batch constructor $this->currMinRec', $this->currMinRec);
                impp('Batch constructor $this->currMaxRec', $this->currMaxRec);
            }
        }
    }

    /**
     * Set batch boundaries
     */
    abstract protected function setMinMaxRec();

    /**
     * @return bool
     */
    public function noData()
    {
        return $this->nodata;
    }

    /**
     * @return int
     */
    public function getMinRec()
    {
        if ($this->noData()) {
            if (DDS_DEBUG == 1) {
                impp("Batch has no data. Check noData() before the call", '');
            }
            throw new Exception("Batch has no data. Check noData() before the call");
        }

        return $this->minRec;
    }

    /**
     * @param int $minRec
     */
    protected function setMinRec($minRec)
    {
        $this->minRec = $minRec;
    }

    /**
     * @return int
     */
    public function getBatchSize()
    {
        return $this->batchSize;
    }

    /**
     * @return int
     */
    public function getMaxRec()
    {
        if ($this->noData()) {
            if (DDS_DEBUG == 1) {
                impp("Batch has no data. Check noData() before the call", '');
            }
            throw new Exception("Batch has no data. Check noData() before the call");
        }

        return $this->maxRec;
    }

    //***********************************************************************//
    // Below two functions are used by iterator to receive batch boundaries  //
    //***********************************************************************//

    /**
     * @param int $maxRec
     */
    protected function setMaxRec($maxRec)
    {
        $this->maxRec = $maxRec;
    }

    /**
     * @return string
     */
    public function getEntity()
    {
        return $this->entity;
    }

    /**
     * false if there are no more batches
     *
     * @return bool
     */
    public function next()
    {
        if ($this->noData()) {
            if (DDS_DEBUG == 1) {
                impp("Batch has no data. Check noData() before calling next()", '');
            }
            throw new Exception("Batch has no data. Check noData() before calling next()");
        }

        if ($this->endOfRecords()) {
            if (DDS_DEBUG == 1) {
                impp("Batches are complete. Use isLast() to check before calling next()", '');
            }
            throw new Exception("Batch are complete. You isLast() to check before calling next()");
        }

        $this->nextRecords();
        $this->incBatchNumber();

        return true;
    }

    /**
     * @return bool
     */
    public function endOfRecords()
    {
        if ($this->noData()) {
            if (DDS_DEBUG == 1) {
                impp("Batch has no data. Check noData() before the call", '');
            }
            throw new Exception("Batch has no data. Check noData() before the call");
        }

        return $this->currMaxRec >= $this->getMaxRec();
    }

    protected function nextRecords()
    {
        $this->currMinRec = $this->currMaxRec;

        if ((DdsSubscription::isDdsSimpleOptimizationEnabled() != true)
            || (DdsJobRunner::NON_STANDARD_ENTITIES[strtolower($this->getEntity())])) {
            if (!$this->isLast()) {
                $this->currMaxRec = $this->currMinRec + $this->getBatchSize();
            } else {
                $this->currMaxRec = $this->getMaxRec() + 1;
            }
        } else {
            if (!$this->isLast()) {
                $this->currMaxRec = $this->fetchNextMaxRec();
            } else {
                $this->currMaxRec = $this->getMaxRec() + 1;
            }
        }

        if (DDS_DEBUG == 1) {
            impp('Batch $this->currMinRec', $this->currMinRec);
            impp('Batch $this->currMaxRec', $this->currMaxRec);
        }
    }

    /**
     * @return bool
     */
    public function isLast()
    {
        if ($this->noData()) {
            if (DDS_DEBUG == 1) {
                impp("Batch has no data. Check noData() before the call", '');
            }
            throw new Exception("Batch has no data. Check noData() before the call");
        }
        if ((DdsSubscription::isDdsSimpleOptimizationEnabled() != true)
            || (DdsJobRunner::NON_STANDARD_ENTITIES[strtolower($this->getEntity())])) {
            return $this->getCurrentMin() + $this->getBatchSize() >= $this->getMaxRec();
        } else {
            return $this->currMaxRec >= $this->maxRec;
        }
    }

    /**
     * @return int
     */
    public function getCurrentMin()
    {
        if ($this->noData()) {
            if (DDS_DEBUG == 1) {
                impp("Batch has no data. Check noData() before the call", '');
            }
            throw new Exception("Batch has no data. Check noData() before the call");
        }

        return $this->currMinRec;
    }

    protected function incBatchNumber()
    {
        $this->batchNumber++;
    }

    /**
     * @param array &$fields
     */
    public function processFields(&$fields)
    {
        // No default behavior
    }

    /**
     * @return int
     */
    public function getCurrentMax()
    {
        if ($this->noData()) {
            if (DDS_DEBUG == 1) {
                impp("Batch has no data. Check noData() before the call", '');
            }
            throw new Exception("Batch has no data. Check noData() before the call");
        }

        return $this->currMaxRec;
    }

    /**
     * Current batch
     *
     * @return int
     */
    public function getBatchNumber()
    {
        return $this->batchNumber;
    }

    /**
     * @return string
     */
    public function getReadTimeGMT()
    {
        return gmdate("m/d/Y H:i:s", $this->readTimestamp);
    }

    /**
     * @return string
     */
    public function getReadTimeLocalTZ()
    {
        return date("m/d/Y H:i:s", $this->readTimestamp);
    }

    /**
     * @return int
     */
    public function getPageSize()
    {
        return $this->pageSize;
    }

    /**
     * @return EntityManager
     */
    public function getEntityManager()
    {
        $objeDef = Pt_DataObjectDefManager::getByName($this->entity);
        if ($objeDef instanceof Pt_StdDataObjectDef) {
            return Globals::$g->gManagerFactory->getManager($this->entity);
        } else {
            return new CustomObjectManager($objeDef);
        }

    }

    /**
     * @return int
     */
    public function getCurrMaxRec()
    {
        return $this->currMaxRec;
    }

    /**
     * @param int $currMaxRec
     */
    public function setCurrMaxRec($currMaxRec)
    {
        $this->currMaxRec = $currMaxRec;
    }

    /**
     * @return int
     */
    public function getCurrMinRec()
    {
        return $this->currMinRec;
    }

    /**
     * @param int $currMinRec
     */
    public function setCurrMinRec($currMinRec)
    {
        $this->currMinRec = $currMinRec;
    }

    /**
     * @return array
     */
    public function getParams()
    {
        return $this->params;
    }

    /**
     * @return string
     */
    public function getReadTimestamp()
    {
        return $this->readTimestamp;
    }


    protected function setNodata()
    {
        $this->nodata = true;
    }

    /**
     * @return IGetListBatch[]
     */
    public function getBatches()
    {
        return [];
    }

    /**
     * @return int
     */
    public function getCurrentBatchIndex()
    {
        return 0;
    }

    /**
     * @return string
     */
    public function getFlashBackStartTime()
    {
        return gmdate("m/d/Y H:i:s",$this->readTimestamp);
    }


    /**
     * Returns  record# list for current entity created recently
     *
     * @return array
     */
    public function getRecordList()
    {
        return $this->recentRecords;
    }


    /**
     * Figure out next record range
     *
     * @return int|null
     */
    protected function fetchNextMaxRec()
    {
        // Record max record#
        $entMgr = $this->getEntityManager();

        $dbTable = Util::getMstForTable($entMgr->getTable());

        $asofQry = " AS OF TIMESTAMP TO_TIMESTAMP('". $this->getReadTimeLocalTZ() ."', 'MM/DD/YYYY HH24:MI:SS')";

        if ( $entMgr instanceof CustomObjectManager ) {
            $additionalWhere = " AND obj_def_id = " . $entMgr->getObjectDefinitionId() . " ";
        } else {
            $additionalWhere = '';
        }

        $args = $this->getNextMaxRecArgs();

        $maxQry = $this->getNextMaxRecQuery($dbTable, $asofQry, $additionalWhere);

        array_unshift($args, $maxQry);

        $result = QueryResult($args);

        if (!isset($result[0]['MAXREC'])) {
            return null;
        }
        return intval($result[0]['MAXREC']);
    }

    /**
     * Figure out next record range
     *
     * @return array[]
     */
    public function getNextMaxRecArgs()
    {
        return [];
    }

    /**
     * Figure out next record range
     * @param string $dbTable
     * @param string $asofQry
     * @param string $additionalWhere
     *
     *
     * @return string
     */
    public function getNextMaxRecQuery(
        /** @noinspection PhpUnusedParameterInspection */ string $dbTable,
        /** @noinspection PhpUnusedParameterInspection */ string $asofQry,
        /** @noinspection PhpUnusedParameterInspection */string $additionalWhere)
    {
        return '';
    }
    /**
     *
     * @return int
     */
    public function getOptimizedBatchSize()
    {
        return $this->optimizedBatchSize;
    }


    /**
     * @param array $record
     */
    public function addRecentRecord(array $record)
    {
        $this->recentRecords[] = $record ;
    }
}