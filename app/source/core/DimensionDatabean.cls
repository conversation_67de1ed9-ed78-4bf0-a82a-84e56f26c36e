<?php
/**
 * File DimensionDatabean.cls contains the class DimensionDatabean
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Class DimensionDatabean
 */
abstract class DimensionDatabean extends Databean
{

    /**
     * @var mixed $deptkey
     */
    private $deptkey;
    /**
     * @var mixed $departmentid
     */
    private $departmentid;
    /**
     * @var mixed $departmentname
     */
    private $departmentname;

    /**
     * @var mixed $locationkey
     */
    private $locationkey;
    /**
     * @var mixed $locationid
     */
    private $locationid;
    /**
     * @var mixed $locationname
     */
    private $locationname;

    /**
     * @var mixed $projectdimkey
     */
    private $projectdimkey;
    /**
     * @var mixed $projectid
     */
    private $projectid;
    /**
     * @var mixed $projectname
     */
    private $projectname;

    /**
     * @var mixed $customerdimkey
     */
    private $customerdimkey;
    /**
     * @var mixed $customerid
     */
    private $customerid;
    /**
     * @var mixed $customername
     */
    private $customername;

    /**
     * @var mixed $vendordimkey
     */
    private $vendordimkey;
    /**
     * @var mixed $vendorid
     */
    private $vendorid;
    /**
     * @var mixed $vendorname
     */
    private $vendorname;

    /**
     * @var mixed $employeedimkey
     */
    private $employeedimkey;
    /**
     * @var mixed $employeeid
     */
    private $employeeid;
    /**
     * @var mixed $employeename
     */
    private $employeename;

    /**
     * @var mixed $itemdimkey
     */
    private $itemdimkey;
    /**
     * @var mixed $itemid
     */
    private $itemid;
    /**
     * @var mixed $itemname
     */
    private $itemname;

    /**
     * @var mixed $classdimkey
     */
    private $classdimkey;
    /**
     * @var mixed $classid
     */
    private $classid;
    /**
     * @var mixed $classname
     */
    private $classname;

    /**
     * @var mixed $contractdimkey
     */
    private $contractdimkey;
    /**
     * @var mixed $contractid
     */
    private $contractid;
    /**
     * @var mixed $contractname
     */
    private $contractname;

    /**
     * @var mixed $costtypedimkey
     */
    private $costtypedimkey;
    /**
     * @var mixed $costtypeid
     */
    private $costtypeid;
    /**
     * @var mixed $costtypename
     */
    private $costtypename;

    /**
     * @var mixed $taskdimkey
     */
    private $taskdimkey;
    /**
     * @var mixed $taskid
     */
    private $taskid;
    /**
     * @var mixed $taskname
     */
    private $taskname;

    /**
     * @var mixed $warehousedimkey
     */
    private $warehousedimkey;
    /**
     * @var mixed $warehouseid
     */
    private $warehouseid;
    /**
     * @var mixed $warehousename
     */
    private $warehousename;

    /**
     * @param string $entity
     */
    public function __construct($entity)
    {
        parent::__construct($entity);
    }

    /**
     * @return mixed
     */
    public function getDeptkey()
    {
        return $this->deptkey;
    }

    /**
     * @param mixed $deptkey
     */
    public function setDeptkey($deptkey)
    {
        $this->deptkey = $deptkey;
    }

    /**
     * @return mixed
     */
    public function getDepartmentid()
    {
        return $this->departmentid;
    }

    /**
     * @param mixed $departmentid
     */
    public function setDepartmentid($departmentid)
    {
        $this->departmentid = $departmentid;
    }

    /**
     * @return mixed
     */
    public function getDepartmentname()
    {
        return $this->departmentname;
    }

    /**
     * @param mixed $departmentname
     */
    public function setDepartmentname($departmentname)
    {
        $this->departmentname = $departmentname;
    }

    /**
     * @return mixed
     */
    public function getLocationkey()
    {
        return $this->locationkey;
    }

    /**
     * @param mixed $locationkey
     */
    public function setLocationkey($locationkey)
    {
        $this->locationkey = $locationkey;
    }

    /**
     * @return mixed
     */
    public function getLocationid()
    {
        return $this->locationid;
    }

    /**
     * @param mixed $locationid
     */
    public function setLocationid($locationid)
    {
        $this->locationid = $locationid;
    }

    /**
     * @return mixed
     */
    public function getLocationname()
    {
        return $this->locationname;
    }

    /**
     * @param mixed $locationname
     */
    public function setLocationname($locationname)
    {
        $this->locationname = $locationname;
    }

    /**
     * @return mixed
     */
    public function getProjectdimkey()
    {
        return $this->projectdimkey;
    }

    /**
     * @param mixed $projectdimkey
     */
    public function setProjectdimkey($projectdimkey)
    {
        $this->projectdimkey = $projectdimkey;
    }

    /**
     * @return mixed
     */
    public function getProjectid()
    {
        return $this->projectid;
    }

    /**
     * @param mixed $projectid
     */
    public function setProjectid($projectid)
    {
        $this->projectid = $projectid;
    }

    /**
     * @return mixed
     */
    public function getProjectname()
    {
        return $this->projectname;
    }

    /**
     * @param mixed $projectname
     */
    public function setProjectname($projectname)
    {
        $this->projectname = $projectname;
    }

    /**
     * @return mixed
     */
    public function getCustomerdimkey()
    {
        return $this->customerdimkey;
    }

    /**
     * @param mixed $customerdimkey
     */
    public function setCustomerdimkey($customerdimkey)
    {
        $this->customerdimkey = $customerdimkey;
    }

    /**
     * @return mixed
     */
    public function getCustomerid()
    {
        return $this->customerid;
    }

    /**
     * @param mixed $customerid
     */
    public function setCustomerid($customerid)
    {
        $this->customerid = $customerid;
    }

    /**
     * @return mixed
     */
    public function getCustomername()
    {
        return $this->customername;
    }

    /**
     * @param mixed $customername
     */
    public function setCustomername($customername)
    {
        $this->customername = $customername;
    }

    /**
     * @return mixed
     */
    public function getVendordimkey()
    {
        return $this->vendordimkey;
    }

    /**
     * @param mixed $vendordimkey
     */
    public function setVendordimkey($vendordimkey)
    {
        $this->vendordimkey = $vendordimkey;
    }

    /**
     * @return mixed
     */
    public function getVendorid()
    {
        return $this->vendorid;
    }

    /**
     * @param mixed $vendorid
     */
    public function setVendorid($vendorid)
    {
        $this->vendorid = $vendorid;
    }

    /**
     * @return mixed
     */
    public function getVendorname()
    {
        return $this->vendorname;
    }

    /**
     * @param mixed $vendorname
     */
    public function setVendorname($vendorname)
    {
        $this->vendorname = $vendorname;
    }

    /**
     * @return mixed
     */
    public function getEmployeedimkey()
    {
        return $this->employeedimkey;
    }

    /**
     * @param mixed $employeedimkey
     */
    public function setEmployeedimkey($employeedimkey)
    {
        $this->employeedimkey = $employeedimkey;
    }

    /**
     * @return mixed
     */
    public function getEmployeeid()
    {
        return $this->employeeid;
    }

    /**
     * @param mixed $employeeid
     */
    public function setEmployeeid($employeeid)
    {
        $this->employeeid = $employeeid;
    }

    /**
     * @return mixed
     */
    public function getEmployeename()
    {
        return $this->employeename;
    }

    /**
     * @param mixed $employeename
     */
    public function setEmployeename($employeename)
    {
        $this->employeename = $employeename;
    }

    /**
     * @return mixed
     */
    public function getItemdimkey()
    {
        return $this->itemdimkey;
    }

    /**
     * @param mixed $itemdimkey
     */
    public function setItemdimkey($itemdimkey)
    {
        $this->itemdimkey = $itemdimkey;
    }

    /**
     * @return mixed
     */
    public function getItemid()
    {
        return $this->itemid;
    }

    /**
     * @param mixed $itemid
     */
    public function setItemid($itemid)
    {
        $this->itemid = $itemid;
    }

    /**
     * @return mixed
     */
    public function getItemname()
    {
        return $this->itemname;
    }

    /**
     * @param mixed $itemname
     */
    public function setItemname($itemname)
    {
        $this->itemname = $itemname;
    }

    /**
     * @return mixed
     */
    public function getClassdimkey()
    {
        return $this->classdimkey;
    }

    /**
     * @param mixed $classdimkey
     */
    public function setClassdimkey($classdimkey)
    {
        $this->classdimkey = $classdimkey;
    }

    /**
     * @return mixed
     */
    public function getClassid()
    {
        return $this->classid;
    }

    /**
     * @param mixed $classid
     */
    public function setClassid($classid)
    {
        $this->classid = $classid;
    }

    /**
     * @return mixed
     */
    public function getClassname()
    {
        return $this->classname;
    }

    /**
     * @param mixed $classname
     */
    public function setClassname($classname)
    {
        $this->classname = $classname;
    }

    /**
     * @return mixed
     */
    public function getContractdimkey()
    {
        return $this->contractdimkey;
    }

    /**
     * @param mixed $contractdimkey
     */
    public function setContractdimkey($contractdimkey)
    {
        $this->contractdimkey = $contractdimkey;
    }

    /**
     * @return mixed
     */
    public function getContractid()
    {
        return $this->contractid;
    }

    /**
     * @param mixed $contractid
     */
    public function setContractid($contractid)
    {
        $this->contractid = $contractid;
    }

    /**
     * @return mixed
     */
    public function getContractname()
    {
        return $this->contractname;
    }

    /**
     * @param mixed $contractname
     */
    public function setContractname($contractname)
    {
        $this->contractname = $contractname;
    }

    /**
     * @return mixed
     */
    public function getTaskdimkey()
    {
        return $this->taskdimkey;
    }

    /**
     * @param mixed $taskdimkey
     */
    public function setTaskdimkey($taskdimkey)
    {
        $this->taskdimkey = $taskdimkey;
    }

    /**
     * @return mixed
     */
    public function getTaskid()
    {
        return $this->taskid;
    }

    /**
     * @param mixed $taskid
     */
    public function setTaskid($taskid)
    {
        $this->taskid = $taskid;
    }

    /**
     * @return mixed
     */
    public function getTaskname()
    {
        return $this->taskname;
    }

    /**
     * @param mixed $taskname
     */
    public function setTaskname($taskname)
    {
        $this->taskname = $taskname;
    }

    /**
     * @return mixed
     */
    public function getWarehousedimkey()
    {
        return $this->warehousedimkey;
    }

    /**
     * @param mixed $warehousedimkey
     */
    public function setWarehousedimkey($warehousedimkey)
    {
        $this->warehousedimkey = $warehousedimkey;
    }

    /**
     * @return mixed
     */
    public function getWarehouseid()
    {
        return $this->warehouseid;
    }

    /**
     * @param mixed $warehouseid
     */
    public function setWarehouseid($warehouseid)
    {
        $this->warehouseid = $warehouseid;
    }

    /**
     * @return mixed
     */
    public function getWarehousename()
    {
        return $this->warehousename;
    }

    /**
     * @param mixed $warehousename
     */
    public function setWarehousename($warehousename)
    {
        $this->warehousename = $warehousename;
    }

    /**
     * @return mixed
     */
    public function getCostTypedimkey()
    {
        return $this->costtypedimkey;
    }

    /**
     * @param mixed $costtypedimkey
     */
    public function setCostTypedimkey($costtypedimkey)
    {
        $this->costtypedimkey = $costtypedimkey;
    }

    /**
     * @return mixed
     */
    public function getCostTypeid()
    {
        return $this->costtypeid;
    }

    /**
     * @param mixed $costtypeid
     */
    public function setCostTypeid($costtypeid)
    {
        $this->costtypeid = $costtypeid;
    }

    /**
     * @return mixed
     */
    public function getCostTypename()
    {
        return $this->costtypename;
    }

    /**
     * @param mixed $costtypename
     */
    public function setCostTypename($costtypename)
    {
        $this->costtypename = $costtypename;
    }

    /**
     * Array of UDD name to value
     *
     * @return array|null
     */
    public function getUDDs()
    {
        $udds = null;
        $map = Pt_StandardUtil::generateGLDimensionFieldNamesMap();
        $relationships = $this->getRelationshipValues();
        foreach ( $map as $fieldName) {
            if ($relationships[$fieldName]) {
                $udds[$fieldName] = $relationships[$fieldName];
            }
        }

        return $udds;
    }

    /**
     * With dimension fields
     *
     * @param array $values
     */
    protected function extendedFromArray(&$values)
    {
        if ($values['DEPTKEY']) {
            $this->deptkey          = $values['DEPTKEY'];
            $this->departmentid     = $values['DEPARTMENTID'];
            $this->departmentname   = $values['DEPARTMENTNAME'];
        }

        if ($values['LOCATIONKEY']) {
            $this->locationkey  = $values['LOCATIONKEY'];
            $this->locationid   = $values['LOCATIONID'];
            $this->locationname = $values['LOCATIONNAME'];
        }

        if ($values['PROJECTDIMKEY']) {
            $this->projectdimkey    = $values['PROJECTDIMKEY'];
            $this->projectid        = $values['PROJECTID'];
            $this->projectname      = $values['PROJECTNAME'];
        }

        if ($values['CUSTOMERDIMKEY']) {
            $this->customerdimkey   = $values['CUSTOMERDIMKEY'];
            $this->customerid       = $values['CUSTOMERID'];
            $this->customername     = $values['CUSTOMERNAME'];
        }

        if ($values['VENDORDIMKEY']) {
            $this->vendordimkey = $values['VENDORDIMKEY'];
            $this->vendorid     = $values['VENDORID'];
            $this->vendorname   = $values['VENDORNAME'];
        }

        if ($values['EMPLOYEEDIMKEY']) {
            $this->employeedimkey   = $values['EMPLOYEEDIMKEY'];
            $this->employeeid       = $values['EMPLOYEEID'];
            $this->employeename     = $values['EMPLOYEENAME'];
        }

        if ($values['ITEMDIMKEY']) {
            $this->itemdimkey   = $values['ITEMDIMKEY'];
            $this->itemid       = $values['ITEMID'];
            $this->itemname     = $values['ITEMNAME'];
        }

        if ($values['CLASSDIMKEY']) {
            $this->classdimkey  = $values['CLASSDIMKEY'];
            $this->classid      = $values['CLASSID'];
            $this->classname    = $values['CLASSNAME'];
        }

        if ($values['CONTRACTDIMKEY']) {
            $this->contractdimkey   = $values['CONTRACTDIMKEY'];
            $this->contractid       = $values['CONTRACTID'];
            $this->contractname     = $values['CONTRACTNAME'];
        }

        if ($values['TASKDIMKEY']) {
            $this->taskdimkey   = $values['TASKDIMKEY'];
            $this->taskid       = $values['TASKID'];
            $this->taskname     = $values['TASKNAME'];
        }

        if ($values['WAREHOUSEDIMKEY']) {
            $this->warehousedimkey  = $values['WAREHOUSEDIMKEY'];
            $this->warehouseid      = $values['WAREHOUSEID'];
            $this->warehousename    = $values['WAREHOUSENAME'];
        }

        if ($values['COSTTYPEDIMKEY']) {
            $this->costtypedimkey   = $values['COSTTYPEDIMKEY'];
            $this->costtypeid       = $values['COSTTYPEID'];
            $this->costtypename     = $values['COSTTYPENAME'];
        }

    }

    /**
     * With dimension fields
     *
     * @param array $values
     */
    protected function extendedToArray(&$values)
    {
        if ($this->deptkey) {
            $values['DEPTKEY'] = $this->deptkey;
            $values['DEPARTMENTID'] = $this->departmentid;
            $values['DEPARTMENTNAME'] = $this->departmentname;
        }

        if ($this->locationkey) {
            $values['LOCATIONKEY'] = $this->locationkey;
            $values['LOCATIONID'] = $this->locationid;
            $values['LOCATIONNAME'] = $this->locationname;
        }

        if ($this->projectdimkey) {
            $values['PROJECTDIMKEY'] = $this->projectdimkey;
            $values['PROJECTID'] = $this->projectid;
            $values['PROJECTNAME'] = $this->projectname;
        }

        if ($this->customerdimkey) {
            $values['CUSTOMERDIMKEY'] = $this->customerdimkey;
            $values['CUSTOMERID'] = $this->customerid;
            $values['CUSTOMERNAME'] = $this->customername;
        }

        if ($this->vendordimkey) {
            $values['VENDORDIMKEY'] = $this->vendordimkey;
            $values['VENDORID'] = $this->vendorid;
            $values['VENDORNAME'] = $this->vendorname;
        }

        if ($this->employeedimkey) {
            $values['EMPLOYEEDIMKEY'] = $this->employeedimkey;
            $values['EMPLOYEEID'] = $this->employeeid;
            $values['EMPLOYEENAME'] = $this->employeename;
        }

        if ($this->itemdimkey) {
            $values['ITEMDIMKEY'] = $this->itemdimkey;
            $values['ITEMID'] = $this->itemid;
            $values['ITEMNAME'] = $this->itemname;
        }

        if ($this->classdimkey) {
            $values['CLASSDIMKEY'] = $this->classdimkey;
            $values['CLASSID'] = $this->classid;
            $values['CLASSNAME'] = $this->classname;
        }

        if ($this->contractdimkey) {
            $values['CONTRACTDIMKEY'] = $this->contractdimkey;
            $values['CONTRACTID'] = $this->contractid;
            $values['CONTRACTNAME'] = $this->contractname;
        }

        if ($this->taskdimkey) {
            $values['TASKDIMKEY'] = $this->taskdimkey;
            $values['TASKID'] = $this->taskid;
            $values['TASKNAME'] = $this->taskname;
        }

        if ($this->warehousedimkey) {
            $values['WAREHOUSEID'] = $this->warehouseid;
            $values['WAREHOUSEDIMKEY'] = $this->warehousedimkey;
            $values['WAREHOUSENAME'] = $this->warehousename;
        }

        if ($this->costtypedimkey) {
            $values['COSTTYPEDIMKEY'] = $this->costtypedimkey;
            $values['COSTTYPEID'] = $this->costtypeid;
            $values['COSTTYPENAME'] = $this->costtypename;
        }

    }
}
