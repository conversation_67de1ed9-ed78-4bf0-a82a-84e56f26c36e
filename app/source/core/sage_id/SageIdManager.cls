<?php

/**
 *  This class supports generation of Sage Identities for use in Sage Intacct apps/services. 
 *
 *  <AUTHOR> <<PERSON>@Sage.com>
 *  @copyright 2023 Sage Intacct
 */

require_once 'util.inc';
require_once 'backend_misc.inc';
require_once '../../private/lib/robrichards/autoload.php';

use Rob<PERSON><PERSON>ards\XMLSecLibs\XMLSecurityDSig as XMLSecurityDSig;

/**
 * Class that implements Sage Identities
 */
class SageIdManager extends EntityManager
{
    
    const ENCRIPT_KEY = 'SAGE_ID';
    
    const SUPPORTED_USER_CATEGORIES = [
        UserInfoManager::CATEGORY_INTERNAL,
        UserInfoManager::CATEGORY_SUPPORT,
        UserInfoManager::CATEGORY_SYSTEM
    ];
    
    /** @var string|null $pseudonym */
    private $pseudonym;
    
    /** @var string|null $sageId */
    private $sageId;
    
    /** @var int $nrSuccess */
    private int $nrSuccess = 0;
    
    /** @var int $nrErrors */
    private int $nrErrors = 0;
    
    /** @var SageIdLogger|null $logger */
    private $logger;
    
    /** @var SageIdConfigProvider|null $configProvider */
    private $configProvider;
    
    /** @var SageServiceApiClient|null $sageServiceApiClient */
    private $sageServiceApiClient;
    
    /** @var SageIdMemcacheHandler|null $memcacheHandler */
    private $memcacheHandler;
    
    /** @var SageIdOAuthToken|null $sageIdOAuthToken */
    private $sageIdOAuthToken;
    
    /** @var array|null $sageServiceJwtPayload */
    private $sageServiceJwtPayload;
    
    /** @var string|null $initialUserId */
    private $initialUserId;
    
    /**
     * @param $params
     *
     * @throws Exception
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
    
    public function getCurrentState()
    {
        $this->initialUserId = Globals::$g->_userid;
    }
    
    public function restoreInitialState()
    {
        Globals::$g->_userid = $this->initialUserId;
        if (!empty($this->initialUserId)) {
            [, $cny] = explode('@', Globals::$g->_userid);
            $this->setDBSchema($cny, 'cny#');
        }
    }
    
    /**
     * @param string $cny
     * @param string $pseudonym
     *
     * @return string
     */
    public static function generatePseudoEmail(string $cny, string $pseudonym): string
    {
        return "{$cny}." . $pseudonym . '@' . 'intacct.com';
    }
    
    /**
     * @param string $pseudoEmail
     *
     * @return string
     */
    public static function extractCnyFromPseudoEmail(string $pseudoEmail): string
    {
        return \substr($pseudoEmail, 0, \strpos($pseudoEmail, '.'));
    }
    
    /**
     * @param string $pseudoEmail
     *
     * @return string
     */
    public static function extractPseudonymFromPseudoEmail(string $pseudoEmail): string
    {
        $pseudoEmail = \substr($pseudoEmail, \strpos($pseudoEmail, '.') + 1);
        return \substr($pseudoEmail, 0, \strpos($pseudoEmail, '@'));
    }
    
    /**
     * @param string $cny
     *
     * @return array
     * @throws FatalException
     */
    public function runGetUsersForCny(string $cny): array
    {
        $results = $this->getUsersWithSageIdByCny($cny);
        if (false === $results){
            $this->nrErrors++;
            throw new FatalException('Failed to fetch users witout SageId for cny: ' . $cny);
        }
        
        return $results;
    }
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return array
     * @throws FatalException
     */
    public function runGetUsersForCnyAndUserId(string $cny, string $userId): array
    {
        $results = $this->getUsersWithSageIdByCnyAndUserId($cny, $userId);
        if (false === $results){
            $this->nrErrors++;
            throw new FatalException(
                'Failed to fetch users witout SageId for cny: ' . $cny . ' and userId: ' . $userId
            );
        }
        
        return $results;
    }
    
    /**
     * @param string $schema
     *
     * @return void
     * @throws FatalException
     */
    public function runForSchema(string $schema): void
    {
        $results = $this->getAllCnysFromSchema($schema);
        if (!$results){
            $this->nrErrors++;
            throw new FatalException('Failed to fetch CNYS from schema: ' . $schema);
        }
        foreach($results as $cnyInfo) {
            try {
                $this->runForCny($cnyInfo['CNY#']);
            } catch (FeatureFlagNotEnabledForCnyException $e) {
                $this->getLogger()->critical('Error: ' . $e->getMessage());
            }
        }
    }
    
    /**
     * @param string $cny
     *
     * @return void
     * @throws FatalException
     */
    public function runForCny(string $cny): void
    {
        if (true !== $this->isFeatureEnabled($cny, 'ENABLE_SAGEID_INTEGRATION')) {
            $this->nrErrors++;
            throw new FeatureFlagNotEnabledForCnyException(
                'Feature flag ENABLE_SAGEID_INTEGRATION is not enabled for cny: ' . $cny
            );
        }
        $results = $this->getUsersWitoutSageIdByCny($cny);
        if (false === $results){
            $this->nrErrors++;
            throw new FatalException('Failed to fetch users witout SageId for cny: ' . $cny);
        }
        $this->init();
        foreach($results as $userInfo) {
           $this->processSageId($cny, $userInfo['RECORD#']);
        }
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param bool   $throwOnError
     *
     * @return void
     * @throws FatalException
     */
    public function runForUserId(string $cny, string $userId, bool $throwOnError = false): void
    {
        if (true !== $this->isFeatureEnabled($cny, 'ENABLE_SAGEID_INTEGRATION')) {
            $this->nrErrors++;
            throw new FeatureFlagNotEnabledForCnyException(
                'Feature flag ENABLE_SAGEID_INTEGRATION is not enabled for cny: ' . $cny
            );
        }
        $this->init();
        $result = $this->getSingleUser($cny, $userId);
        if (false === $result) {
            $this->nrErrors++;
            throw new FatalException('Failed to get single user');
        }
        
        if (!(isset($result[0]['RECORD#']) && !empty($result[0]['RECORD#']))) {
            $this->nrErrors++;
            throw new FatalException('User not found');
        }
        
        if (!(isset($result[0]['CATEGORY']) && in_array($result[0]['CATEGORY'], self::SUPPORTED_USER_CATEGORIES))) {
            $this->nrErrors++;
            throw new SageIdUserNotEligibleException('User is not eligible for SageId');
        }
        
        try {
            $this->processSageId($cny, $result[0]['RECORD#'], $throwOnError);
        } catch (SageIdAlreadyGeneratedException | SageServiceValidationErrorException | SageIdAttributeProviderErrorException $e) {
            $this->getLogger()->critical($e->getMessage());
            $this->nrErrors++;
            if ($throwOnError) {
                throw $e;
            }
        }
    }
    
    /**
     * @return int
     */
    public function getNrTotal(): int
    {
        return $this->nrSuccess + $this->nrErrors;
    }
    
    /**
     * @return int
     */
    public function getNrSuccess(): int
    {
        return $this->nrSuccess;
    }
    
    /**
     * @return int
     */
    public function getNrErrors(): int
    {
        return $this->nrErrors;
    }
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return array|bool
     */
    protected function getSingleUser(string $cny, string $userId): array|bool
    {
        $podManager = $this->getPodManager();
        if (true === $podManager->isCnyInCurrentPODGroup($cny)) {
            $this->setDBSchema($cny, 'cny#');
            return $this->getSingleUserFromCurrentPod($cny, $userId);
        } else {
            $query = $this->_QM->_queryList['QRY_USERINFO_SELECT_SINGLE_USER'];
            $blobdata = [];
            $args = [(int)$userId, (int)$cny];
            return $this->runOnCnyDB(
                'QueryResult',
                [[$this->_QM->_ProcessCustomQuery($query, $args, $blobdata, false), (int)$userId, (int)$cny]],
                $cny
            );
        }
    }
    
    /**
     * @param string $schema
     *
     * @return array|bool
     */
    protected function getAllCnysFromSchema(string $schema): array|bool
    {
        return $this->wrapperDoCustomQuery(
            [
                'QUERY' => "select sc.cny# from schemamap sc ,database db where sc.DATABASEID = db.DATABASEID"
                           . " AND substr(db.userid,1, instr(db.userid,'_') - 1) ||'_owner_0'||db.schemacnt = :1",
                'ARGTYPES' => ['text']
            ],
            [strtolower($schema)],
            true,
            '',
            0,
            $this->getGlobalConnection()
        );
    }
    
    /**
     * @param string $cny
     *
     * @return array|bool
     */
    protected function getUsersWitoutSageIdByCny(string $cny): array|bool
    {
        $podManager = $this->getPodManager();
        if (true === $podManager->isCnyInCurrentPODGroup($cny)) {
            $this->setDBSchema($cny, 'cny#');
            return $this->getUsersWitoutSageIdByCnyFromCurrentPod($cny);
        } else {
            return $this->runOnCnyDB(
                'QueryResult',
                [[$this->_QM->_queryList['QRY_USERINFO_SELECT_ALL_BY_CNY_NO_SAGE_ID']['QUERY'], (int)$cny, (int)$cny]],
                $cny
            );
        }
    }
    
    /**
     * @param string $cny
     *
     * @return array|bool
     */
    protected function getUsersWithSageIdByCny(string $cny): array|bool
    {
        $podManager = $this->getPodManager();
        if (true === $podManager->isCnyInCurrentPODGroup($cny)) {
            $this->setDBSchema($cny, 'cny#');
            return $this->getUsersWithSageIdByCnyFromCurrentPod($cny);
        } else {
            return $this->runOnCnyDB(
                'QueryResult',
                [[$this->_QM->_queryList['QRY_USERINFO_SELECT_ALL_BY_CNY_WITH_SAGE_ID']['QUERY'], (int)$cny]],
                $cny
            );
        }
    }
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return array|bool
     */
    protected function getUsersWithSageIdByCnyAndUserId(string $cny, string $userId): array|bool
    {
        $podManager = $this->getPodManager();
        if (true === $podManager->isCnyInCurrentPODGroup($cny)) {
            $this->setDBSchema($cny, 'cny#');
            return $this->getUsersWithSageIdByCnyAndUserIdFromCurrentPod($cny, $userId);
        } else {
            return $this->runOnCnyDB(
                'QueryResult',
                [
                    [
                        $this->_QM->_queryList['QRY_USERINFO_SELECT_ALL_BY_CNY_USERID_WITH_SAGE_ID']['QUERY'],
                        (int)$cny, (int)$userId
                    ]
                ],
                $cny
            );
        }
    }
    
    /**
     * @param string $cny
     *
     * @return array|bool
     */
    protected function getUsersWitoutSageIdByCnyFromCurrentPod(string $cny): array|bool
    {
        return $this->wrapperDoQuery('QRY_USERINFO_SELECT_ALL_BY_CNY_NO_SAGE_ID', [(int)$cny, (int)$cny]);
    }
    
    /**
     * @param string $cny
     *
     * @return array|bool
     */
    protected function getUsersWithSageIdByCnyFromCurrentPod(string $cny): array|bool
    {
        return $this->wrapperDoQuery('QRY_USERINFO_SELECT_ALL_BY_CNY_WITH_SAGE_ID', [(int)$cny]);
    }
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return array|bool
     */
    protected function getUsersWithSageIdByCnyAndUserIdFromCurrentPod(string $cny, string $userId): array|bool
    {
        return $this->wrapperDoQuery(
            'QRY_USERINFO_SELECT_ALL_BY_CNY_USERID_WITH_SAGE_ID', [(int)$cny, (int)$userId]
        );
    }
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return array|bool
     */
    protected function getSingleUserFromCurrentPod(string $cny, string $userId): array|bool
    {
        return $this->wrapperDoQuery('QRY_USERINFO_SELECT_SINGLE_USER', [(int)$userId, (int)$cny]);
    }
    
    /**
     * @return void
     */
    protected function init(): void
    {
        if (null === $this->memcacheHandler || null === $this->sageIdOAuthToken) {
            $this->memcacheHandler = $this->getSageIdMemcacheHandler();
            $this->sageIdOAuthToken = $this->getSageIdOAuthToken($this->getConfigProvider()->getOAuthCacheKey());
            if (null === $this->sageIdOAuthToken) {
                $this->getAndStoreNewOAuthToken();
            }
        }
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param bool   $throwOnError
     *
     * @return void
     */
    protected function processSageIdOnCurrentPod(string $cny, string $userId, bool $throwOnError = false) : void
    {
        try {
            $this->pseudonym = null;
            $computedExpireTime =
                $this->sageIdOAuthToken->getAcquiredAt()
                + $this->sageIdOAuthToken->getExpiresIn()
                - $this->getConfigProvider()->getOAuthExpireInOffset();
            if (time() > $computedExpireTime) {
                $this->getAndStoreNewOAuthToken();
            }
            if ($this->initSageId($cny, $userId)) {
                $attributeProvider = $this->getAttibuteProvider($this->getUserCacheHandler($cny, $userId));
                
                $email = self::generatePseudoEmail($cny, $this->pseudonym);
                
                $response = $this->getSageServiceApiClient()->createUser(
                    $this->sageIdOAuthToken->getAccessToken(),
                    $this->pseudonym,
                    $email,
                    $attributeProvider->getAttributeValue('lastname'),
                    $attributeProvider->getAttributeValue('firstname')
                );
                
                $this->validateResponse($response);
                
                if (false === $this->saveSageId($cny, $userId, $response['userId'])) {
                    $this->nrErrors++;
                    throw new FatalException('Failed to save SageID');
                }
                $this->nrSuccess++;
            }
        } catch (SageIdAlreadyGeneratedException
            | SageServiceValidationErrorException
            | SageIdAttributeProviderErrorException $e
        ) {
            $this->getLogger()->critical($e->getMessage());
            $this->updateUserStatusSageId($cny, $userId, 'ERROR', $userId, $e->getMessage());
            if ($throwOnError) {
                throw $e;
            }
        }
    }
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return void
     * @throws FatalException
     */
    public static function processSageIdOnOtherPod(string $cny, string $userId): void
    {
        $sageIdManager = new SageIdManager();
        $sageIdManager->runForUserId($cny, $userId);
    }
    
    /**
     * @param string $cny
     * @param string $featureFlag
     *
     * @return bool
     * @throws Exception
     */
    public static function isFeatureEnabledOtherPod(string $cny, string $featureFlag): bool
    {
        $sageIdManager = new SageIdManager();
        return $sageIdManager->isFeatureEnabledCurrentPod($cny, $featureFlag);
    }
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return bool
     */
    protected function initSageId(string $cny, string $userId): bool
    {
        Globals::$g->_userid = $userId . '@' . $cny;
        GetUserPreferences($userPreferences);
        $this->pseudonym = $userPreferences['SAGE_ID_PSEUDONYM'] ?? '';
        $this->sageId = $userPreferences['SAGE_ID'] ?? '';
        if ('' !== $this->sageId) {
            $this->nrErrors++;
            throw new SageIdAlreadyGeneratedException('Sage Id already generated, cny=' . $cny . ', userId=' . $userId);
        }
        if ('' === $this->pseudonym) {
            $this->pseudonym = $this->generatePseudonym();
        }
        if (false === $this->setSageIdPseudonym($cny, $userId, $this->pseudonym)) {
            $this->nrErrors++;
            throw new FatalException('Failed to set Sage Id Pseudonym');
        }
        
        $userStatusSageId = $this->getUserStatusSageId($cny, $userId);
        if (empty($userStatusSageId['STATUS'])) {
            return $this->newUserStatusSageId($cny, $userId, 'STARTED', $userId);
        } else {
            return $this->updateUserStatusSageId($cny, $userId, 'STARTED', $userId);
        }
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param string $sageId
     *
     * @return bool
     */
    protected function saveSageId(string $cny, string $userId, string $sageId): bool
    {
        // this is done so late because at this point we are on current POD
        $userStatusSageId = $this->getUserStatusSageId($cny, $userId);
        if ($userStatusSageId['STATUS'] === 'STARTED') {
            Globals::$g->_userid = $userId . '@' . $cny;
            $preferences = [['PROPERTY' => 'SAGE_ID', 'VALUE' => $sageId]];
            $ok = SetPreferences($userId . '@' . $cny, '', $preferences);
            return $ok && $this->updateUserStatusSageId($cny, $userId, 'DONE', $userId);
        }
        
        return false;
    }
    
    /**
     * @codeCoverageIgnore
     * @param string $cny
     *
     * @return FeatureConfigManager
     * @throws Exception
     */
    protected function getFeatureConfigManager(string $cny): FeatureConfigManager
    {
        $featureConf = ia_cfg::get(ia_cfg::FEATURE_CFG, FeatureConfigManager::FEATURE_DECISION);
        $featureEntries = $featureConf->getAll();
        
        $opsEntries = FeatureOperationsConfig::getFeatureOperations();
        
        $cstoolsConf = ia_cfg::get(ia_cfg::FEATURE_CFG, FeatureConfigManager::CSTOOLS_SECTION);
        $cstoolsEntries = $cstoolsConf->getAll();
        
        return new FeatureConfigManager($featureEntries, $opsEntries, $cstoolsEntries, $cny);
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param bool   $throwOnError
     *
     * @return void
     */
    protected function processSageId(string $cny, string $userId, bool $throwOnError = false): void
    {
        $podManager = $this->getPodManager();
        if (true === $podManager->isCnyInCurrentPODGroup($cny)) {
            $this->setDBSchema($cny, 'cny#');
            $this->processSageIdOnCurrentPod($cny, $userId, $throwOnError);
        } else {
            $this->runOnCnyDB(
                ['SageIdManager', 'processSageIdOnOtherPod'],
                [$cny, $userId, $throwOnError],
                $cny
            );
        }
    }
    
    /**
     * @param string $cny
     * @param string $featureFlag
     *
     * @return bool
     */
    protected function isFeatureEnabled(string $cny, string $featureFlag): bool
    {
        $podManager = $this->getPodManager();
        if (true === $podManager->isCnyInCurrentPODGroup($cny)) {
            return $this->isFeatureEnabledCurrentPod($cny, $featureFlag);
        } else {
            return $this->runOnCnyDB(
                ['SageIdManager', 'isFeatureEnabledOtherPod'],
                [$cny, $featureFlag],
                $cny
            );
        }
    }
    
    /**
     * @param string $cny
     * @param string $featureFlag
     *
     * @return bool
     * @throws Exception
     */
    public function isFeatureEnabledCurrentPod(string $cny, string $featureFlag): bool
    {
        $this->setDBSchema($cny, 'cny#');
        $featureManager = $this->getFeatureConfigManager($cny);
        return (bool)$featureManager->isFeatureEnabled($featureFlag);
    }
    
    /**
     * @param SageIdManager $sageIdManager
     * @param string        $cny
     * @param string        $userId
     * @param string        $consoleCny
     * @param string        $sageIdPseudonym
     * @param string        $sageId
     *
     * @return bool
     */
    public static function upsertSageIdProperties(
        SageIdManager $sageIdManager,
        string $cny,
        string $userId,
        string $consoleCny,
        string $sageIdPseudonym,
        string $sageId
    ) : bool {
        return $sageIdManager->processUpsertSageIdProperties($cny, $userId, $consoleCny, $sageIdPseudonym, $sageId);
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param string $consoleCny
     * @param string $sageIdPseudonym
     * @param string $sageId
     *
     * @return bool
     */
    public function processUpsertSageIdProperties(string $cny, string $userId, string $consoleCny, string $sageIdPseudonym, string $sageId) : bool
    {
        $origGlobalUserId = Globals::$g->_userid;
        // if the user has a SAGE_ID_PSEUDONYM set, we need to set it in the destination user as well
        if ('' !== $sageIdPseudonym) {
            $destSageIdPseudonymInfo = $this->wrapperQueryResult(array(
                "SELECT userpref.value FROM userpref WHERE userpref.cny# = :1 AND userpref.userrec = :2 AND userpref.property = :3",
                $cny, $userId, 'SAGE_ID_PSEUDONYM'
            ));
            $destSageIdPseudonym = $destSageIdPseudonymInfo[0]['VALUE'] ?? '';
            $destPseudoEmail = '';
            if ('' !== $destSageIdPseudonym) {
                $destPseudoEmailInfo = $this->wrapperQueryResult(array(
                    "SELECT userpref.value FROM userpref WHERE userpref.cny# = :1 AND userpref.userrec = :2 AND userpref.property = :3",
                    $cny, $userId, 'PSEUDO_EMAIL'
                ));
                $destPseudoEmail = $destPseudoEmailInfo[0]['VALUE'] ?? '';
            }
            if ('' === $destSageIdPseudonym || $destSageIdPseudonym !== $sageIdPseudonym || '' === $destPseudoEmail) {
                Globals::$g->_userid = $userId . '@' . $cny;
                $preferences = [
                    ['PROPERTY' => 'SAGE_ID_PSEUDONYM', 'VALUE' => $sageIdPseudonym],
                    ['PROPERTY' => 'PSEUDO_EMAIL', 'VALUE' => $this->generatePseudoEmail($consoleCny, $sageIdPseudonym)]
                ];
                $this->wrapperSetPreferences($userId . '@' . $cny, '', $preferences);
            }
        }
        
        // if the user has a SAGE_ID set, we need to set it in the destination user as well
        if ('' !== $sageId) {
            $destSageIdInfo = $this->wrapperQueryResult(array(
                "SELECT userpref.value FROM userpref WHERE userpref.cny# = :1 AND userpref.userrec = :2 AND userpref.property = :3",
                $cny, $userId, 'SAGE_ID'
            ));
            $destSageId = $destSageIdInfo[0]['VALUE'] ?? '';
            if ('' === $destSageId || $destSageId !== $sageId) {
                Globals::$g->_userid = $userId . '@' . $cny;
                $preferences = [['PROPERTY' => 'SAGE_ID', 'VALUE' => $sageId]];
                $this->wrapperSetPreferences($userId . '@' . $cny, '', $preferences);
            }
        }
        Globals::$g->_userid = $origGlobalUserId;
        return true;
    }
    
    /**
     * @return void
     * @throws FatalException
     */
    protected function getAndStoreNewOAuthToken(): void
    {
        $tokenData = $this->getSageServiceApiClient()->getOAuthToken();
        if (null === ($token = $tokenData['access_token'] ?? null)) {
            $this->nrErrors++;
            throw new FatalException('Failed to get Oauth token');
        }
        $tokenHandler = $this->getSageServiceTokenHandler();
        $jws = $tokenHandler->unserialize($token);
        $this->sageServiceJwtPayload = $tokenHandler->getPayload($jws);
        if (false === $tokenHandler->verifySignature($jws, $tokenHandler->getJwks())) {
            $this->nrErrors++;
            throw new FatalException('OAuth Token - Failed to verify Signature');
        }
        $this->sageIdOAuthToken = $this->newSageIdOAuthToken(
            $tokenData['access_token'],
            $tokenData['token_type'],
            $tokenData['expires_in']
        );
        $this->memcacheHandler->storeOnCurrentPod(
            $this->getConfigProvider()->getOAuthCacheKey(),
            $this->sageIdOAuthToken,
            true,
            $this->sageIdOAuthToken->getExpiresIn() - $this->getConfigProvider()->getOAuthExpireInOffset()
        );
    }
    
    /**
     * @param array|null $response
     *
     * @return void
     */
    protected function validateResponse(?array $response): void
    {
        if (null === $response) {
            $this->nrErrors++;
            throw new FatalException('Failed to createUser, null response');
        }
        $responseCode =  $this->getSageServiceApiClient()->getInfo(CURLINFO_RESPONSE_CODE);
        switch ($responseCode) {
            case 201:
            case 409:
                if (empty($response['userId'])) {
                    $this->nrErrors++;
                    throw new SageServiceValidationErrorException('Failed to createUser, empty SageID');
                }
                break;
            case 400:
                $this->nrErrors++;
                throw new SageServiceValidationErrorException(
                    'Failed to createUser, errors: ' . json_encode($response['errors'] ?? '')
                );
            default:
                $this->nrErrors++;
                throw new FatalException(
                    'Failed to createUser, response was: ' . json_encode($response['errors'] ?? '')
                );
        }
        
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param string $pseudonym
     *
     * @return bool
     */
    protected function setSageIdPseudonym(string $cny, string $userId, string $pseudonym): bool
    {
        Globals::$g->_userid = $userId . '@' . $cny;
        $preferences = [['PROPERTY' => 'SAGE_ID_PSEUDONYM', 'VALUE' => $pseudonym]];
        return $this->wrapperSetPreferences($userId . '@' . $cny, null, $preferences);
    }
    
    
    /**
     * @param string $cny
     * @param string $userId
     *
     * @return array|null
     */
    protected function getUserStatusSageId(string $cny, string $userId): ?array
    {
        $stmt = [
            "select * from user_status_sageid where cny# = :1 and userkey = :2",
            $cny,
            $userId
        ];
        $result = $this->wrapperQueryResult($stmt);
        return $result[0] ?? [];
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param string $status
     * @param string $createdBy
     *
     * @return bool
     */
    protected function newUserStatusSageId(string $cny, string $userId, string $status, string $createdBy): bool
    {
        $sql = "insert into user_status_sageid (cny#, userkey, status, createdby, record#) values(:1, :2, :3, :4, GET_NEXTRECORDID(:1,'USER_STATUS_SAGEID'))";
        return $this->wrapperExecStmt([$sql, $cny, $userId, $status, $createdBy]);
    }
    
    /**
     * @param string $cny
     * @param string $userId
     * @param string $status
     * @param string $modifiedBy
     * @param string $errorDetails
     *
     * @return bool
     */
    protected function updateUserStatusSageId(
        string $cny,
        string $userId,
        string $status,
        string $modifiedBy,
        string $errorDetails = ''
    ): bool {
        $sql = "update user_status_sageid set status = :1, modifiedby = :2, error_details = :3 where cny#=:4 and userkey=:5";
        return $this->wrapperExecStmt([$sql, $status, $modifiedBy, $errorDetails, $cny, $userId]);
    }
    
    /**
     * @param int $companyId
     * @param int $userId
     *
     * @return UserCacheHandler|null
     */
    protected function getUserCacheHandler(int $companyId, int $userId): ?UserCacheHandler
    {
        $podManager = $this->getPodManager();
        if (true === $podManager->isCnyInCurrentPODGroup($companyId)) {
            $userCacheHandler = $this->getUserCacheHandlerObjectFromCurrentPod($companyId, $userId);
        } else {
            $userCacheHandler = $this->runOnCnyDB(
                ['UserCacheHandler', 'getInstance'],
                [$companyId, $userId],
                $companyId
            );
        }
        return $userCacheHandler;
    }
    
    /**
     * @return SageServiceApiClient
     */
    protected function getSageServiceApiClient(): SageServiceApiClient
    {
        if (null === $this->sageServiceApiClient) {
            $this->sageServiceApiClient = $this->initSageServiceApiClient();
        }
        return $this->sageServiceApiClient;
    }
    
    /**
     * @return SageIdOAuthToken|null
     */
    protected function getSageIdOAuthToken(string $key): ?SageIdOAuthToken
    {
        $sageIdOAuthToken = $this->memcacheHandler->getSageIdOAuthTokenFromCurrentPod($key, true);
        if ($sageIdOAuthToken instanceof SageIdOAuthToken) {
            return $sageIdOAuthToken;
        }
        
        return $this->memcacheHandler->getSageIdOAuthTokenFromPods($key, true);
    }
    
    /**
     * @param string $accessToken
     * @param string $tokenType
     * @param int $expiresIn
     *
     * @return SageIdOAuthToken
     */
    protected function newSageIdOAuthToken(
        string $accessToken,
        string $tokenType,
        int $expiresIn
    ): SageIdOAuthToken {
        return new SageIdOAuthToken($accessToken, $tokenType, $expiresIn);
    }
    
    /**
     * @param UserCacheHandler $userCacheHandler
     *
     * @return SageIdAttributeProvider
     */
    protected function getAttibuteProvider(UserCacheHandler $userCacheHandler) : SageIdAttributeProvider
    {
        return new SageIdAttributeProvider($userCacheHandler);
    }
    
    /**
     * @codeCoverageIgnore
     * @param int $companyId
     * @param int $userId
     *
     * @return UserCacheHandler|null
     */
    protected function getUserCacheHandlerObjectFromCurrentPod(
        int $companyId,
        int $userId
    ): ?UserCacheHandler {
        return UserCacheHandler::getInstance($companyId, $userId);
    }
    
    /**
     * @codeCoverageIgnore
     * @param string $companyId
     * @param string $type
     *
     * @return void
     */
    protected function setDBSchema(string $companyId, string $type): void
    {
        SetDBSchema($companyId, $type);
    }
    
    /**
     * @codeCoverageIgnore
     * @param string $_userid
     * @param mixed  $_r
     * @param array  $preferences
     *
     * @return bool
     */
    protected function wrapperSetPreferences(string $_userid, $_r, array $preferences): bool
    {
        return (bool)SetPreferences($_userid, $_r, $preferences);
    }
    
    /**
     * @return string
     */
    protected function retrieveEntityName()
    {
        return 'userinfo';
    }
    
    /**
     * @codeCoverageIgnore
     * @return string
     */
    protected function generatePseudonym(): string
    {
        return XMLSecurityDSig::generate_GUID('');
    }
    
    /**
     * @return SageServiceTokenHandler
     */
    public function getSageServiceTokenHandler(): SageServiceTokenHandler
    {
        if (null === $this->sageServiceTokenHandler) {
            $this->sageServiceTokenHandler = $this->initSageServiceTokenHandler();
        }
        return $this->sageServiceTokenHandler;
    }
    
    /**
     * @return SageIdMemcacheHandler
     */
    protected function getSageIdMemcacheHandler(): SageIdMemcacheHandler
    {
        if (null === $this->sageIdMemcacheHandler) {
            $this->sageIdMemcacheHandler = $this->initSageIdMemcacheHandler();
        }
        return $this->sageIdMemcacheHandler;
    }
    
    /**
     * @return SageIdConfigProvider
     */
    protected function getConfigProvider(): SageIdConfigProvider
    {
        if (null === $this->configProvider) {
            $this->configProvider = $this->initConfigProvider();
        }
        return $this->configProvider;
    }
    
    /**
     * @return SageIdLogger
     */
    protected function getLogger(): SageIdLogger
    {
        if (null === $this->logger) {
            $this->logger = $this->initLogger();
        }
        return $this->logger;
    }
    
    /**
     * @codeCoverageIgnore
     * @return SageServiceApiClient
     */
    protected function initSageServiceApiClient(): SageServiceApiClient
    {
        return (new SageServiceApiClient($this->getConfigProvider()))->init();
    }
    
    /**
     * @codeCoverageIgnore
     * @return SageIdConfigProvider
     */
    protected function initConfigProvider(): SageIdConfigProvider
    {
        return new SageIdConfigProvider();
    }
    
    /**
     * @codeCoverageIgnore
     * @return SageServiceTokenHandler
     */
    protected function initSageServiceTokenHandler(): SageServiceTokenHandler
    {
        return new SageServiceTokenHandler($this->getConfigProvider());
    }
    
    /**
     * @codeCoverageIgnore
     * @return SageIdMemcacheHandler
     */
    protected function initSageIdMemcacheHandler(): SageIdMemcacheHandler
    {
        return new SageIdMemcacheHandler(CacheClient::getInstance(CacheClient::GLOBAL_POOL));
    }
    
    /**
     * @codeCoverageIgnore
     * @return SageIdLogger
     */
    protected function initLogger(): SageIdLogger
    {
        return new SageIdLogger();
    }
    
    /**
     * @codeCoverageIgnore
     *
     * @return PODManager|null
     */
    protected function getPodManager(): ?PODManager
    {
        return Globals::$g->gPODManager;
    }
    
    /**
     * @codeCoverageIgnore
     *
     * @return DatabaseConnection|null
     */
    protected function getGlobalConnection(): ?DatabaseConnection
    {
        return GetGlobalConnection();
    }
    
    /**
     * @codeCoverageIgnore
     * @param array|string $apiCallback
     * @param array        $apiArgs
     * @param mixed        $cnyId
     *
     * @return array|bool|mixed
     */
    protected function runOnCnyDB(
        array|string $apiCallback,
        array $apiArgs,
        $cnyId
    ) {
        return DBRunner::runOnCnyDB($apiCallback, $apiArgs, $cnyId);
    }
    
    /**
     * @codeCoverageIgnore
     * @param string $code
     * @param array  $args
     *
     * @return array|bool
     */
    protected function wrapperDoQuery(string $code, array $args): array|bool
    {
        return $this->_QM->DoQuery($code, $args);
    }
    
    /**
     * @codeCoverageIgnore
     *
     * @param array                   $query
     * @param array                   $args
     * @param bool                    $headers
     * @param string                  $max
     * @param int                     $first
     * @param DatabaseConnection|null $handle
     *
     * @return bool|string[][]
     */
    protected function wrapperDoCustomQuery(
        array $query,
        array $args,
        bool $headers,
        string $max,
        int $first = 0,
        ?DatabaseConnection $handle = null
    ): array|bool {
        return $this->_QM->DoCustomQuery($query, $args, $headers, $first, $max, $handle);
    }
    
    /**
     * @codeCoverageIgnore
     * @param array|string $stmt
     *
     * @return bool
     */
    protected function wrapperExecStmt( array|string $stmt): bool
    {
        return ExecStmt($stmt);
    }
    
    /**
     * @codeCoverageIgnore
     * @param array|string $stmt
     *
     * @return array|bool
     */
    protected function wrapperQueryResult( array|string $stmt): array|bool
    {
        return QueryResult($stmt);
    }
}
