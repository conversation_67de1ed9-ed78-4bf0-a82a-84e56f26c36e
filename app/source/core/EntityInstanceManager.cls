<?

import("ManagerFactory");

/**
 * FILE: EntityInstanceManager.cls
 * AUTHOR: <PERSON>
 * DESCRIPTION:
 */
class EntityInstanceManager
{

    /* @var array $_instanceArray */
    var $_instanceArray = [];

    function __construct()
    {
    }

    /**
     * @param string $entity name of the entity
     * @param string $vid    vid of the entity
     * @param array  $params constructor params
     * @param bool   $flatten
     * @param bool   $fromPtRelationship
     *
     * @return array
     */
    function getInstance($entity, $vid, $params = [], $flatten = false, $fromPtRelationship = false)
    {
        $entity = isl_strtolower($entity);
        if ( ! $entity ) {
            showStackTrace();
            dieFL("*** fatal: EntityInstanceManager:getInstance called with no entity\n");
        }
        if ( ! $vid && ! isset($vid) ) {
            showStackTrace();
            dieFL("*** fatal: EntityInstanceManager:getInstance called with no vid\n");
        }

        // First check the temporary cache
        $key = self::getTemporaryCacheKey($entity, $vid);
        if ( ! $this->_instanceArray[$entity][$vid] ) {
            if ( AutoDestructCache::exists($key) ) {
                $instance = AutoDestructCache::get($key);
            } else {
                global $gManagerFactory;
                $manager = $gManagerFactory->getManager($entity, false, $params);

                $instance = $fromPtRelationship ? $manager->GetByRecordNo($vid) : $manager->get($vid);

                if ( !$instance ) {
                    // get does not work. May be for a smartlink, Try get for smartlink
                    $instance = $manager->GetForSmartLink($vid);
                }
            }
            if ( $flatten ) {
                $instance = EntityManager::StructuredToFlat($instance);
                $instance = array_change_key_case($instance, CASE_LOWER);
            }
            $this->_instanceArray[$entity][$vid] = $instance;
        }

        return ( $this->_instanceArray[$entity][$vid] );
    }

    /**
     * Returns true of entity with the given vid has been instantiated
     *
     * @param string $entity
     * @param string $vid
     *
     * @return bool
     */
    function entityInstanceExists($entity, $vid)
    {
        return ( isset($this->_instanceArray[$entity]) and isset($this->_instanceArray[$entity][$vid]) );
    }

    /**
     * @param string $entity
     * @param string $vid
     */
    public function removeInstance($entity, $vid)
    {
        if ( isset($this->_instanceArray[$entity][$vid]) ) {
            unset($this->_instanceArray[$entity][$vid]);
        }
    }

    /**
     * Remove all cached instances
     */
    public function removeAll()
    {
        $this->_instanceArray = [];
    }

    /**
     * Get a temporary cache key used by the EntityInstanceManager
     * (usually for smart links)
     *
     * @param string $entity  the entity name
     * @param mixed  $vid     the vid value
     *
     * @return string   the key
     */
    public static function getTemporaryCacheKey($entity, $vid)
    {
        $key = "EntityInstance." . $entity . "." . $vid;

        return $key;
    }

}

global $gEntityInstanceManager;
$gEntityInstanceManager = new EntityInstanceManager();

