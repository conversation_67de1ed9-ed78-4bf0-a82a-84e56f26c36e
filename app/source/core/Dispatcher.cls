<?php

abstract class Dispatcher implements IDispatcher
{
    protected array $currentConsumers = [];

    /**
     * Apply round-robin logic to the job list
     *
     * @param array  $jobList        Job list to manipulate
     * @param array  $processedJobs  Jobs processed (this comes from the Dispatcher Engine)
     *
     * @return array
     */
    public function applyRoundRobin($jobList, $processedJobs)
    {
        // build the list of jobs in a round robin fashion:
        // FOR EACH DB:
        //     jobs that haven't been processed yet, stay at the top of the list, in the order fetched from DB
        //     jobs that have been processed go to the bottom of the list in the order they were processed

        //     we go over the results from DB and we place the jobs in the 2 lists: top or bottom
        //     in the top list they go in by the order from DB
        //     in the bottom list they go in by their processing order
        //     then we sort the bottom list by the keys, aka by the processing order
        //     at last we return the combined lists

        foreach ( $jobList as $dbId => $dbJobs ) {
            $topList = [];
            $bottomList = [];
            $processedKeys = isset($processedJobs[$dbId]) ? array_flip($processedJobs[$dbId]) : [];
            foreach ($dbJobs as $jobId) {
                $processedIndex = $processedKeys[$jobId] ?? -1;
                if ( $processedIndex < 0 ) {
                    $topList[] = $jobId;
                } else {
                    $bottomList[$processedIndex] = $jobId;
                }
            }
            ksort($bottomList);
            $jobList[$dbId] = array_merge($topList, $bottomList);
        }

        return $jobList;
    }

    public function recordCurrentConsumers($currentConsumers)
    {
        $this->currentConsumers = $currentConsumers;
    }
}