<?
import('UIControl');

class FrameControl extends UIControl
{

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct($_params);
    }


    /**
     * @param array $_params
     *
     * @return array
     */
    function CalcParams($_params)
    {
        return $_params;
    }


    function Show() 
    { 
        $framecnt = 0;
        $cRows = "38,*";
        if (QXCommon::isQuixote()) {
            $cRows = "54,*";
        }
        $rows = ($this->_params['drillfilter'])? $cRows : $this->_params['rows'];
        $onload = ($this->_params['onload'] ? "onload=\"".$this->_params['onload']."\"" : "");
        
    ?>
     <frameset id="reportFilter" rows="<?=$rows?>" border='<?=$this->_params['border']?>' <?=$onload?>>
        <? foreach($this->_params['frames'] as $frames){
            $framecnt++;?>
			<frame 
				src="<?=$frames['src']?>" 
				name="<?="iachild_frame__".$framecnt;?>" <?=$frames['noresize']?> 
				marginheight="<?=$frames['marginheight']?>" 
				marginwidth="<?=$frames['marginwidth']?>" 
				scrolling="auto"
				frameborder="<?=$frames['frameborder']?>" 
				bordercolor="<?=$frames['BORDERCOLOR']?>" >
    <? 
} ?>
     </frameset>
    <?
    }

}

