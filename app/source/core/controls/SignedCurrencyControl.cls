<?php
//===========================================================================
//	FILE:			SignedCurrencyControl.cls
//	AUTHOR:			<PERSON>
//	DESCRIPTION:
//
//	(C) 2000, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	Corporation and is protected by the copyright laws.  Information
//	herein may not be used, copied or disclosed in whole or in part
//	without prior written consent from Intacct Corporation.
//===========================================================================

import('CurrencyControl');

class SignedCurrencyControl extends CurrencyControl
{


    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct($_params);
    }


    /**
     * @param string $docType
     */
    function ShowReadOnly($docType = null)
    {
        // STRIP OFF THE '$' AND THE ',' AND ANY OTHER JUNK (keep any negative sign)
        $str = preg_replace("/[^0-9.-]/", "", $this->_params['value']);

        // CONVERT THE VALUE TO FORMATED DECIMAL
        $this->_params['value'] = glFormatCurrency($str);

        AssistedTextControl::ShowReadOnly($docType);
    }

}
