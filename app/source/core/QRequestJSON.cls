<?php
/*
 * This is to implement the Intacct generic version of Ajax called
 * Quick Request with JSON responses.
 */


class QRequestJSON extends QRequest
{
    /**
     * @var array $funcPermMap
     */
    protected static $funcPermMap = [
        'Get' => 'view',
        'Delete' => 'delete',
        'FetchContractItemPrice' => 'view',
        'GetGridRowData' => 'view',
        "getContactData" => "view", // UserInfoManager->getContactData()
        "getFileNames" => "view", // SupportingDocumentsManager->getFileNames()
    ];

    /**
     * @param array $params
     */
    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    /**
     * @return bool
     */
    public function Init()
    {
        global $gManagerFactory;
        $this->restrictedFunctions = ['Get', 'FetchContractItemPrice'];

        parent::Init();
        parent::SetParams();
        $ok = isset($this->qparams['entity']) && isset($this->qparams['function']);
        if( $ok ) {
            $func = $this->qparams['function'];
            if( ! self::$funcPermMap[$func] ) {
                $ok = false;
            }
            else
            {
                $entity = $this->qparams['entity'];
                $mgr = $gManagerFactory->getManager($entity);
                $ok = $mgr && method_exists($mgr, $func);
                if( $ok ) {
                    $secKey = $mgr->GetHomeModule() . '/lists/' . $entity . '/' . self::$funcPermMap[$func];
                    $ok = IsOperationAllowed(GetOperationId($secKey));
                }
            }
        }
        $this->monitorRestrictedFunctions();
        $this->noXMLHeader = true;
        return $ok;
    }

    /**
     * @param string $response
     *
     * @return bool
     */
    public function Run(&$response)
    {
        global $gManagerFactory;

        $func = $this->qparams['function'];
        $mgr = $gManagerFactory->getManager($this->qparams['entity']);

        if (!method_exists($mgr, $func)) {
            return false;
        }

        // parse otherparams and build params array
        $otherparams = explode(',', $this->qparams['otherparams']);
        $params = array();
        foreach ($otherparams as $key) {
            $params[] = $this->qparams[$key];
        }

        switch ($this->qparams["entity"]) {
            case "userinfo":
//                $oUserInfo = $gManagerFactory->getManager("userinfo");
                /** @var UserInfoManager $oUserInfo */
                $oUserInfo = $mgr;
                if ($func === "getContactData") {
                    $data = $oUserInfo->getContactData($this->qparams["id"]);
                    $response = json_encode($data);
                }
                break;

            default:
                $values = call_user_func_array(array($mgr, $func), ensureArray($params));
                $response = json_encode(utf8_encode_struct($values));
        }

        return true;
    }

    public function setContentType()
    {
        header('Content-Type: application/json');
    }
}
