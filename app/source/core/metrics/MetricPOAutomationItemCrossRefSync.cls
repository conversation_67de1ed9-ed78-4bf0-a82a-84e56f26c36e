<?php
/**
 * Metric class for POAutomationcomponent, ItemCrossRefSync metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricPOAutomationItemCrossRefSync  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $guId
     * @param string $whenCreated
     * @param int $cUser
     * @param int $jobRecord
     * @param string $jobAction
     * @param string $ICRAction
     * @param int $ICRRecord
     * @param string $ICRRefType
     * @param string $ICRItemAliasId
     * @param string $ICRItemId
     * @param string $ICRVendorId
     * @param string $status
     * @param int $location
     * @param string $error

     */
    public function __construct($guId = null, $whenCreated = null, $cUser = null, $jobRecord = null, $jobAction = null, $ICRAction = null, $ICRRecord = null, $ICRRefType = null, $ICRItemAliasId = null, $ICRItemId = null, $ICRVendorId = null, $status = null, $location = null, $error = null)
    {
        parent::__construct(
            'POAutomation',
            'ItemCrossRefSync',
            0,
            [
                'time',
                'timeForProcess'
            ],
            [
                'guId',
                'whenCreated',
                'cUser#',
                'jobRecord#',
                'jobAction',
                'ICRAction',
                'ICRRecord#',
                'ICRRefType',
                'ICRItemAliasId',
                'ICRItemId',
                'ICRVendorId',
                'status',
                'location#',
                'error'
            ]
            , $guId, $whenCreated, $cUser, $jobRecord, $jobAction, $ICRAction, $ICRRecord, $ICRRefType, $ICRItemAliasId, $ICRItemId, $ICRVendorId, $status, $location, $error
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @param int|float $quantity
     */
    public function addTimeForProcess($quantity)
    {
        parent::addQuantity('timeForProcess', $quantity);
    }
    
    public function incrementTimeForProcess()
    {
        parent::increment('timeForProcess');
    }
    
    public function decrementTimeForProcess()
    {
        parent::decrement('timeForProcess');
    }

    /**
     * @return float|null
     */
    public function getTimeForProcess(): ?float
    {
        return parent::getValue('timeForProcess');
    }
    
    /**
     * @param float $timeForProcess
     */
    public function setTimeForProcess(float $timeForProcess)
    {
        parent::setValue('timeForProcess', $timeForProcess);
    }

    /**
     * @return string|null
     */
    public function getGuId(): ?string
    {
        return parent::getAttribute('guId');
    }
    
    /**
     * @param string $guId
     */
    public function setGuId(string $guId)
    {
        parent::setAttribute('guId', $guId);
    }

    /**
     * @return string|null
     */
    public function getWhenCreated(): ?string
    {
        return parent::getAttribute('whenCreated');
    }
    
    /**
     * @param string $whenCreated
     */
    public function setWhenCreated(string $whenCreated)
    {
        parent::setAttribute('whenCreated', $whenCreated);
    }

    /**
     * @return int|null
     */
    public function getCUser(): ?int
    {
        return parent::getAttribute('cUser#');
    }
    
    /**
     * @param int $cUser#
     */
    public function setCUser(int $cUser)
    {
        parent::setAttribute('cUser#', $cUser);
    }

    /**
     * @return int|null
     */
    public function getJobRecord(): ?int
    {
        return parent::getAttribute('jobRecord#');
    }
    
    /**
     * @param int $jobRecord#
     */
    public function setJobRecord(int $jobRecord)
    {
        parent::setAttribute('jobRecord#', $jobRecord);
    }

    /**
     * @return string|null
     */
    public function getJobAction(): ?string
    {
        return parent::getAttribute('jobAction');
    }
    
    /**
     * @param string $jobAction
     */
    public function setJobAction(string $jobAction)
    {
        parent::setAttribute('jobAction', $jobAction);
    }

    /**
     * @return string|null
     */
    public function getICRAction(): ?string
    {
        return parent::getAttribute('ICRAction');
    }
    
    /**
     * @param string $ICRAction
     */
    public function setICRAction(string $ICRAction)
    {
        parent::setAttribute('ICRAction', $ICRAction);
    }

    /**
     * @return int|null
     */
    public function getICRRecord(): ?int
    {
        return parent::getAttribute('ICRRecord#');
    }
    
    /**
     * @param int $ICRRecord#
     */
    public function setICRRecord(int $ICRRecord)
    {
        parent::setAttribute('ICRRecord#', $ICRRecord);
    }

    /**
     * @return string|null
     */
    public function getICRRefType(): ?string
    {
        return parent::getAttribute('ICRRefType');
    }
    
    /**
     * @param string $ICRRefType
     */
    public function setICRRefType(string $ICRRefType)
    {
        parent::setAttribute('ICRRefType', $ICRRefType);
    }

    /**
     * @return string|null
     */
    public function getICRItemAliasId(): ?string
    {
        return parent::getAttribute('ICRItemAliasId');
    }
    
    /**
     * @param string $ICRItemAliasId
     */
    public function setICRItemAliasId(string $ICRItemAliasId)
    {
        parent::setAttribute('ICRItemAliasId', $ICRItemAliasId);
    }

    /**
     * @return string|null
     */
    public function getICRItemId(): ?string
    {
        return parent::getAttribute('ICRItemId');
    }
    
    /**
     * @param string $ICRItemId
     */
    public function setICRItemId(string $ICRItemId)
    {
        parent::setAttribute('ICRItemId', $ICRItemId);
    }

    /**
     * @return string|null
     */
    public function getICRVendorId(): ?string
    {
        return parent::getAttribute('ICRVendorId');
    }
    
    /**
     * @param string $ICRVendorId
     */
    public function setICRVendorId(string $ICRVendorId)
    {
        parent::setAttribute('ICRVendorId', $ICRVendorId);
    }

    /**
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return parent::getAttribute('status');
    }
    
    /**
     * @param string $status
     */
    public function setStatus(string $status)
    {
        parent::setAttribute('status', $status);
    }

    /**
     * @return int|null
     */
    public function getLocation(): ?int
    {
        return parent::getAttribute('location#');
    }
    
    /**
     * @param int $location#
     */
    public function setLocation(int $location)
    {
        parent::setAttribute('location#', $location);
    }

    /**
     * @return string|null
     */
    public function getError(): ?string
    {
        return parent::getAttribute('error');
    }
    
    /**
     * @param string $error
     */
    public function setError(string $error)
    {
        parent::setAttribute('error', $error);
    }

}
