<?php
/**
 * Metric class for matchingRuleEnginecomponent, rules metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricMatchingRuleEngineRules  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $id
     * @param string $rulesetid
     * @param string $ruleid
     * @param string $type
     * @param string $docType

     */
    public function __construct($id = null, $rulesetid = null, $ruleid = null, $type = null, $docType = null)
    {
        parent::__construct(
            'matchingRuleEngine',
            'rules',
            0,
            [
                'count',
                'processedIntacctCount',
                'processedBankCount',
                'matchedTxnCount',
                'time'
            ],
            [
                'id',
                'rulesetid',
                'ruleid',
                'type',
                'docType'
            ]
            , $id, $rulesetid, $ruleid, $type, $docType
        );
    }

    /**
     * @param int|float $quantity
     */
    public function addCount($quantity)
    {
        parent::addQuantity('count', $quantity);
    }
    
    public function incrementCount()
    {
        parent::increment('count');
    }
    
    public function decrementCount()
    {
        parent::decrement('count');
    }

    /**
     * @return float|null
     */
    public function getCount(): ?float
    {
        return parent::getValue('count');
    }
    
    /**
     * @param float $count
     */
    public function setCount(float $count)
    {
        parent::setValue('count', $count);
    }

    /**
     * @param int|float $quantity
     */
    public function addProcessedIntacctCount($quantity)
    {
        parent::addQuantity('processedIntacctCount', $quantity);
    }
    
    public function incrementProcessedIntacctCount()
    {
        parent::increment('processedIntacctCount');
    }
    
    public function decrementProcessedIntacctCount()
    {
        parent::decrement('processedIntacctCount');
    }

    /**
     * @return float|null
     */
    public function getProcessedIntacctCount(): ?float
    {
        return parent::getValue('processedIntacctCount');
    }
    
    /**
     * @param float $processedIntacctCount
     */
    public function setProcessedIntacctCount(float $processedIntacctCount)
    {
        parent::setValue('processedIntacctCount', $processedIntacctCount);
    }

    /**
     * @param int|float $quantity
     */
    public function addProcessedBankCount($quantity)
    {
        parent::addQuantity('processedBankCount', $quantity);
    }
    
    public function incrementProcessedBankCount()
    {
        parent::increment('processedBankCount');
    }
    
    public function decrementProcessedBankCount()
    {
        parent::decrement('processedBankCount');
    }

    /**
     * @return float|null
     */
    public function getProcessedBankCount(): ?float
    {
        return parent::getValue('processedBankCount');
    }
    
    /**
     * @param float $processedBankCount
     */
    public function setProcessedBankCount(float $processedBankCount)
    {
        parent::setValue('processedBankCount', $processedBankCount);
    }

    /**
     * @param int|float $quantity
     */
    public function addMatchedTxnCount($quantity)
    {
        parent::addQuantity('matchedTxnCount', $quantity);
    }
    
    public function incrementMatchedTxnCount()
    {
        parent::increment('matchedTxnCount');
    }
    
    public function decrementMatchedTxnCount()
    {
        parent::decrement('matchedTxnCount');
    }

    /**
     * @return float|null
     */
    public function getMatchedTxnCount(): ?float
    {
        return parent::getValue('matchedTxnCount');
    }
    
    /**
     * @param float $matchedTxnCount
     */
    public function setMatchedTxnCount(float $matchedTxnCount)
    {
        parent::setValue('matchedTxnCount', $matchedTxnCount);
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return parent::getAttribute('id');
    }
    
    /**
     * @param string $id
     */
    public function setId(string $id)
    {
        parent::setAttribute('id', $id);
    }

    /**
     * @return string|null
     */
    public function getRulesetid(): ?string
    {
        return parent::getAttribute('rulesetid');
    }
    
    /**
     * @param string $rulesetid
     */
    public function setRulesetid(string $rulesetid)
    {
        parent::setAttribute('rulesetid', $rulesetid);
    }

    /**
     * @return string|null
     */
    public function getRuleid(): ?string
    {
        return parent::getAttribute('ruleid');
    }
    
    /**
     * @param string $ruleid
     */
    public function setRuleid(string $ruleid)
    {
        parent::setAttribute('ruleid', $ruleid);
    }

    /**
     * @return string|null
     */
    public function getType(): ?string
    {
        return parent::getAttribute('type');
    }
    
    /**
     * @param string $type
     */
    public function setType(string $type)
    {
        parent::setAttribute('type', $type);
    }

    /**
     * @return string|null
     */
    public function getDocType(): ?string
    {
        return parent::getAttribute('docType');
    }
    
    /**
     * @param string $docType
     */
    public function setDocType(string $docType)
    {
        parent::setAttribute('docType', $docType);
    }

}
