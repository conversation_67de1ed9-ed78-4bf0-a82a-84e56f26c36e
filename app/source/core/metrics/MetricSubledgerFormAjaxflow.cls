<?php
/**
 * Metric class for subledgerFormcomponent, ajaxflow metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricSubledgerFormAjaxflow  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $object
     * @param string $function
     * @param float $timeForProcess
     * @param int $location
     * @param int $pymtTxnCount
     * @param int $pymtTxnLineCount
     * @param int $lineCount
     * @param bool $isEnabled

     */
    public function __construct($object = null, $function = null, $timeForProcess = null, $location = null, $pymtTxnCount = null, $pymtTxnLineCount = null, $lineCount = null, $isEnabled = null)
    {
        parent::__construct(
            'subledgerForm',
            'ajaxflow',
            0,
            [],
            [
                'object',
                'function',
                'timeForProcess',
                'location#',
                'pymtTxnCount',
                'pymtTxnLineCount',
                'lineCount',
                'isEnabled'
            ]
            , $object, $function, $timeForProcess, $location, $pymtTxnCount, $pymtTxnLineCount, $lineCount, $isEnabled
        );
    }

    /**
     * @return string|null
     */
    public function getObject(): ?string
    {
        return parent::getAttribute('object');
    }
    
    /**
     * @param string $object
     */
    public function setObject(string $object)
    {
        parent::setAttribute('object', $object);
    }

    /**
     * @return string|null
     */
    public function getFunction(): ?string
    {
        return parent::getAttribute('function');
    }
    
    /**
     * @param string $function
     */
    public function setFunction(string $function)
    {
        parent::setAttribute('function', $function);
    }

    /**
     * @param int|float $quantity
     */
    public function addTimeForProcess($quantity)
    {
        parent::addQuantity('timeForProcess', $quantity);
    }
    
    public function incrementTimeForProcess()
    {
        parent::increment('timeForProcess');
    }
    
    public function decrementTimeForProcess()
    {
        parent::decrement('timeForProcess');
    }

    /**
     * @return float|null
     */
    public function getTimeForProcess(): ?float
    {
        return parent::getAttribute('timeForProcess');
    }
    
    /**
     * @param float $timeForProcess
     */
    public function setTimeForProcess(float $timeForProcess)
    {
        parent::setAttribute('timeForProcess', $timeForProcess);
    }

    /**
     * @return int|null
     */
    public function getLocation(): ?int
    {
        return parent::getAttribute('location#');
    }
    
    /**
     * @param int $location#
     */
    public function setLocation(int $location)
    {
        parent::setAttribute('location#', $location);
    }

    /**
     * @return int|null
     */
    public function getPymtTxnCount(): ?int
    {
        return parent::getAttribute('pymtTxnCount');
    }
    
    /**
     * @param int $pymtTxnCount
     */
    public function setPymtTxnCount(int $pymtTxnCount)
    {
        parent::setAttribute('pymtTxnCount', $pymtTxnCount);
    }

    /**
     * @return int|null
     */
    public function getPymtTxnLineCount(): ?int
    {
        return parent::getAttribute('pymtTxnLineCount');
    }
    
    /**
     * @param int $pymtTxnLineCount
     */
    public function setPymtTxnLineCount(int $pymtTxnLineCount)
    {
        parent::setAttribute('pymtTxnLineCount', $pymtTxnLineCount);
    }

    /**
     * @return int|null
     */
    public function getLineCount(): ?int
    {
        return parent::getAttribute('lineCount');
    }
    
    /**
     * @param int $lineCount
     */
    public function setLineCount(int $lineCount)
    {
        parent::setAttribute('lineCount', $lineCount);
    }

    /**
     * @return bool|null
     */
    public function getIsEnabled(): ?bool
    {
        return parent::getAttribute('isEnabled');
    }
    
    /**
     * @param bool $isEnabled
     */
    public function setIsEnabled(bool $isEnabled)
    {
        parent::setAttribute('isEnabled', $isEnabled);
    }

}
