<?php
/**
 * Metric class for DispatcherEnginecomponent, end metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricDispatcherEngineEnd  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $componentName the component name
     * @param int $dbCount

     */
    public function __construct(string $componentName, $dbCount = null)
    {
        parent::__construct(
            $componentName,
            'end',
            0,
            [
                'time',
                'count',
                'errCount'
            ],
            [
                'dbCount'
            ]
            , $dbCount
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @param int|float $quantity
     */
    public function addCount($quantity)
    {
        parent::addQuantity('count', $quantity);
    }
    
    public function incrementCount()
    {
        parent::increment('count');
    }
    
    public function decrementCount()
    {
        parent::decrement('count');
    }

    /**
     * @return float|null
     */
    public function getCount(): ?float
    {
        return parent::getValue('count');
    }
    
    /**
     * @param float $count
     */
    public function setCount(float $count)
    {
        parent::setValue('count', $count);
    }

    /**
     * @param int|float $quantity
     */
    public function addErrCount($quantity)
    {
        parent::addQuantity('errCount', $quantity);
    }
    
    public function incrementErrCount()
    {
        parent::increment('errCount');
    }
    
    public function decrementErrCount()
    {
        parent::decrement('errCount');
    }

    /**
     * @return float|null
     */
    public function getErrCount(): ?float
    {
        return parent::getValue('errCount');
    }
    
    /**
     * @param float $errCount
     */
    public function setErrCount(float $errCount)
    {
        parent::setValue('errCount', $errCount);
    }

    /**
     * @return int|null
     */
    public function getDbCount(): ?int
    {
        return parent::getAttribute('dbCount');
    }
    
    /**
     * @param int $dbCount
     */
    public function setDbCount(int $dbCount)
    {
        parent::setAttribute('dbCount', $dbCount);
    }

}
