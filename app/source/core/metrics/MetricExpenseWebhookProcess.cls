<?php

/**
 * Metric class for StxWebhookcomponent, webhookProcess metric id
 *
 * <AUTHOR> adiserla>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */
/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */

final class MetricExpenseWebhookProcess extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $id
     * @param string $status
     * @param string $type
     * @param string $notificationStatus
     * @param int $timeTakenToProcess
     */
    public function __construct($id = null, $status = null, $type = null, $notificationStatus = null, $timeTakenToProcess = null)
    {
        parent::__construct(
            'ExpenseWebhook',
            'webhookProcess',
            0,
            [
                'time'
            ],
            [
                'id',
                'status',
                'type',
                'notificationStatus',
                'timeTakenToProcess'
            ]
            , $id, $status, $type, $notificationStatus, $timeTakenToProcess
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }

    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float
     */
    public function getTime(): float
    {
        return parent::getValue('time');
    }

    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @return string
     */
    public function getId(): string
    {
        return parent::getAttribute('id');
    }

    /**
     * @param string $id
     */
    public function setId(string $id)
    {
        parent::setAttribute('id', $id);
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return parent::getAttribute('status');
    }

    /**
     * @param string $status
     */
    public function setStatus(string $status)
    {
        parent::setAttribute('status', $status);
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return parent::getAttribute('type');
    }

    /**
     * @param string $type
     */
    public function setType(string $type)
    {
        parent::setAttribute('type', $type);
    }

    /**
     * @return string
     */
    public function getNotificationStatus(): string
    {
        return parent::getAttribute('notificationStatus');
    }

    /**
     * @param string $notificationStatus
     */
    public function setNotificationStatus(string $notificationStatus)
    {
        parent::setAttribute('notificationStatus', $notificationStatus);
    }

    /**
     * @return int
     */
    public function getTimeTakenToProcess(): int
    {
        return parent::getAttribute('timeTakenToProcess');
    }

    /**
     * @param int $timeTakenToProcess
     */
    public function setTimeTakenToProcess(int $timeTakenToProcess)
    {
        parent::setAttribute('timeTakenToProcess', $timeTakenToProcess);
    }

}
