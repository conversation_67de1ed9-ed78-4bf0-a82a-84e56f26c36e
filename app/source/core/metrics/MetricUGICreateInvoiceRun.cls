<?php
/**
 * Metric class for UGIcomponent, createInvoiceRun metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricUGICreateInvoiceRun  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $id
     * @param string $mode
     * @param bool $success
     * @param int $record

     */
    public function __construct($id = null, $mode = null, $success = null, $record = null)
    {
        parent::__construct(
            'UGI',
            'createInvoiceRun',
            0,
            [
                'time'
            ],
            [
                'id',
                'mode',
                'success',
                'record#'
            ]
            , $id, $mode, $success, $record
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return parent::getAttribute('id');
    }
    
    /**
     * @param string $id
     */
    public function setId(string $id)
    {
        parent::setAttribute('id', $id);
    }

    /**
     * @return string|null
     */
    public function getMode(): ?string
    {
        return parent::getAttribute('mode');
    }
    
    /**
     * @param string $mode
     */
    public function setMode(string $mode)
    {
        parent::setAttribute('mode', $mode);
    }

    /**
     * @return bool|null
     */
    public function getSuccess(): ?bool
    {
        return parent::getAttribute('success');
    }
    
    /**
     * @param bool $success
     */
    public function setSuccess(bool $success)
    {
        parent::setAttribute('success', $success);
    }

    /**
     * @return int|null
     */
    public function getRecord(): ?int
    {
        return parent::getAttribute('record#');
    }
    
    /**
     * @param int $record#
     */
    public function setRecord(int $record)
    {
        parent::setAttribute('record#', $record);
    }

}
