<?php
/**
 * Metric class for SBCAccountcomponent, mappingRequestSent metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricSBCAccountMappingRequestSent  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $componentName the component name
     * @param string $orgid
     * @param string $externalid
     * @param string $id

     */
    public function __construct(string $componentName, $orgid = null, $externalid = null, $id = null)
    {
        parent::__construct(
            $componentName,
            'mappingRequestSent',
            0,
            [
                'count'
            ],
            [
                'orgid',
                'externalid',
                'id'
            ]
            , $orgid, $externalid, $id
        );
    }

    /**
     * @param int|float $quantity
     */
    public function addCount($quantity)
    {
        parent::addQuantity('count', $quantity);
    }
    
    public function incrementCount()
    {
        parent::increment('count');
    }
    
    public function decrementCount()
    {
        parent::decrement('count');
    }

    /**
     * @return float|null
     */
    public function getCount(): ?float
    {
        return parent::getValue('count');
    }
    
    /**
     * @param float $count
     */
    public function setCount(float $count)
    {
        parent::setValue('count', $count);
    }

    /**
     * @return string|null
     */
    public function getOrgid(): ?string
    {
        return parent::getAttribute('orgid');
    }
    
    /**
     * @param string $orgid
     */
    public function setOrgid(string $orgid)
    {
        parent::setAttribute('orgid', $orgid);
    }

    /**
     * @return string|null
     */
    public function getExternalid(): ?string
    {
        return parent::getAttribute('externalid');
    }
    
    /**
     * @param string $externalid
     */
    public function setExternalid(string $externalid)
    {
        parent::setAttribute('externalid', $externalid);
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return parent::getAttribute('id');
    }
    
    /**
     * @param string $id
     */
    public function setId(string $id)
    {
        parent::setAttribute('id', $id);
    }

}
