<?php
/**
 * Metric class for approvalbyvendorcomponent, enabled metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricApprovalbyvendorEnabled  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param bool $isEnabled

     */
    public function __construct($isEnabled = null)
    {
        parent::__construct(
            'approvalbyvendor',
            'enabled',
            0,
            [
                'time'
            ],
            [
                'isEnabled'
            ]
            , $isEnabled
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @return bool|null
     */
    public function getIsEnabled(): ?bool
    {
        return parent::getAttribute('isEnabled');
    }
    
    /**
     * @param bool $isEnabled
     */
    public function setIsEnabled(bool $isEnabled)
    {
        parent::setAttribute('isEnabled', $isEnabled);
    }

}
