<?php
/**
 * Metric class for BVAVarianceProcessorcomponent, process metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricBVAVarianceProcessorProcess  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $action

     */
    public function __construct($action = null)
    {
        parent::__construct(
            'BVAVarianceProcessor',
            'process',
            0,
            [
                'time',
                'groupsCount',
                'count',
                'errCount',
                'criticalCount'
            ],
            [
                'action'
            ]
            , $action
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @param int|float $quantity
     */
    public function addGroupsCount($quantity)
    {
        parent::addQuantity('groupsCount', $quantity);
    }
    
    public function incrementGroupsCount()
    {
        parent::increment('groupsCount');
    }
    
    public function decrementGroupsCount()
    {
        parent::decrement('groupsCount');
    }

    /**
     * @return float|null
     */
    public function getGroupsCount(): ?float
    {
        return parent::getValue('groupsCount');
    }
    
    /**
     * @param float $groupsCount
     */
    public function setGroupsCount(float $groupsCount)
    {
        parent::setValue('groupsCount', $groupsCount);
    }

    /**
     * @param int|float $quantity
     */
    public function addCount($quantity)
    {
        parent::addQuantity('count', $quantity);
    }
    
    public function incrementCount()
    {
        parent::increment('count');
    }
    
    public function decrementCount()
    {
        parent::decrement('count');
    }

    /**
     * @return float|null
     */
    public function getCount(): ?float
    {
        return parent::getValue('count');
    }
    
    /**
     * @param float $count
     */
    public function setCount(float $count)
    {
        parent::setValue('count', $count);
    }

    /**
     * @param int|float $quantity
     */
    public function addErrCount($quantity)
    {
        parent::addQuantity('errCount', $quantity);
    }
    
    public function incrementErrCount()
    {
        parent::increment('errCount');
    }
    
    public function decrementErrCount()
    {
        parent::decrement('errCount');
    }

    /**
     * @return float|null
     */
    public function getErrCount(): ?float
    {
        return parent::getValue('errCount');
    }
    
    /**
     * @param float $errCount
     */
    public function setErrCount(float $errCount)
    {
        parent::setValue('errCount', $errCount);
    }

    /**
     * @param int|float $quantity
     */
    public function addCriticalCount($quantity)
    {
        parent::addQuantity('criticalCount', $quantity);
    }
    
    public function incrementCriticalCount()
    {
        parent::increment('criticalCount');
    }
    
    public function decrementCriticalCount()
    {
        parent::decrement('criticalCount');
    }

    /**
     * @return float|null
     */
    public function getCriticalCount(): ?float
    {
        return parent::getValue('criticalCount');
    }
    
    /**
     * @param float $criticalCount
     */
    public function setCriticalCount(float $criticalCount)
    {
        parent::setValue('criticalCount', $criticalCount);
    }

    /**
     * @return string|null
     */
    public function getAction(): ?string
    {
        return parent::getAttribute('action');
    }
    
    /**
     * @param string $action
     */
    public function setAction(string $action)
    {
        parent::setAttribute('action', $action);
    }

}
