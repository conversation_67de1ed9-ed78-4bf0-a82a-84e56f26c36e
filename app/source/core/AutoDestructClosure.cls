<?php

/**
 * Set a closure to be called upon exit of function scope
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation All, Rights Reserved
 */

/**
 * A class to facilitate the automatic execution of a closure when a function exits
 */
class AutoDestructClosure
{
    /**
     * @var callable $closure
     */
    private $closure;
    
    /**
     * @param callable $closure the function to call
     */
    public function __construct($closure)
    {
        $this->closure = $closure;
    }
    
    /**
     * Destructor
     */
    public function __destruct()
    {
        if ( $this->closure != null ) {
            $func = $this->closure;
            $func();
        }
    }
}