<?
/**
 *    FILE:            detotals.ent
 *    AUTHOR:            srao
 *    DESCRIPTION:    entity definition for detotals object
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/
$kSchemas['detotals'] = array (
    'children' => array(
        'item' => array( 'fkey' => 'itemkey', 'invfkey' => 'itemid', 'join' => 'outer', 'table' => 'icitem'),
        'warehouse' => array( 'fkey' => 'warehousekey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'icwarehouse'),
        'department' => array( 'fkey' => 'deptkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'department'),
        'location' => array( 'fkey' => 'locationkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'location'),
    ),
    
    'nexus' => array(
        'item' => array(
            'object' => 'item', 'relation' => MANY2ONE, 'field' => 'ITEM.ITEMID', 'printas' => 'IA.ITEM'
        ), 
        'warehouse' => array(
            'object' => 'warehouse', 'relation' => MANY2ONE, 'field' => 'WAREHOUSE.LOCATIONID', 'printas' => 'IA.WAREHOUSE'
        ), 
        'department'  => array( 
            'object' => 'department', 'relation' => MANY2ONE, 'field' => 'DEPARTMENT.DEPT_NO', 'printas' => 'IA.DEPARTMENT'
        ), 
        'location'  => array( 
            'object' => 'location', 'relation' => MANY2ONE, 'field' => 'LOCATION.LOCATION_NO', 'printas' => 'Location'
        ),
    ),
    
    'object' => array (
            'DOCPARID',
             'DOCCLASS', 
             'SALE_PUR_TRANS',
            'ENTITY',
            'TIMEPERIOD',
            'ITEMKEY',
            'QUANTITY',
            'VALUE',
            'TRX_VALUE',
            'TOTALCOST',
            'WAREHOUSEKEY',
            'WAREHOUSE.LOCATIONID',
            'DEPTKEY',
            'DEPARTMENT.DEPT_NO',
            'LOCATIONKEY',
            'LOCATION.LOCATION_NO',
            'CURRENCY',
            'BASECURR',
            'CUSTDIM1',
            'CUSTDIM2', 
            'CUSTDIM3',
            'CUSTDIM4',
            'CUSTDIM5',
    ),
    'schema' => array (
        'DOCPARID'                => 'docparid', 
        'DOCCLASS'             => 'docclass', 
         'SALE_PUR_TRANS'    => 'sale_pur_trans',
        'ENTITY'            => 'entity',
        'TIMEPERIOD'        => 'timeperiod',
        'ITEMKEY'            => 'itemkey',
        'ITEM'        => array(
            'item.*' => 'item.*'
        ),
        'QUANTITY'            => 'quantity',
        'VALUE'                => 'value',
        'TRX_VALUE'            => 'trx_value',
        'TOTALCOST'            => 'totalcost',
        'WAREHOUSEKEY'        => 'warehousekey',
        'WAREHOUSE'        => array(
            'warehouse.*' => 'warehouse.*'
        ),
        'DEPARTMENT'        => array(
            'department.*' => 'department.*'
        ),
        'LOCATION'        => array(
            'location.*' => 'location.*'
        ),
        'DEPTKEY'            => 'deptkey',
        'LOCATIONKEY'        => 'locationkey',
        'CURRENCY'            => 'currency',
        'BASECURR'            => 'basecurr',
        'CUSTDIM1'            => 'custdim1',
        'CUSTDIM2'            => 'custdim2', 
        'CUSTDIM3'            => 'custdim3',
        'CUSTDIM4'            => 'custdim4',
        'CUSTDIM5'            => 'custdim5',
    ),
    'publish' => array(
        'DOCPARID',
        'DOCCLASS', 
         'SALE_PUR_TRANS',
        'ENTITY',
        'TIMEPERIOD',
        'ITEMKEY',
        'QUANTITY',
        'VALUE',
        'TRX_VALUE',
        'TOTALCOST',
        'WAREHOUSEKEY',
        'WAREHOUSE.LOCATIONID',
        'DEPTKEY',
        'DEPARTMENT.DEPT_NO',
        'LOCATIONKEY',
        'LOCATION.LOCATION_NO',
        'CURRENCY',
        'BASECURR',
        'CUSTOMERDIMKEY',
        'PROJECTDIMKEY',
        'VENDORDIMKEY',
        'EMPLOYEEDIMKEY',
        'CLASSDIMKEY',
        'CONTRACTDIMKEY',
        'TASKDIMKEY',
        'COSTTYPEDIMKEY',
        'CUSTDIM1',
        'CUSTDIM2', 
        'CUSTDIM3',
        'CUSTDIM4',
        'CUSTDIM5',
    ),

    'fieldinfo' => array (
        array (
            'path' => 'DOCPARID',
            'desc' => 'IA.TEMPLATE_NAME',
            'fullname' => 'IA.TEMPLATE_NAME',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',                    
                ),
            'id' => 201
        ),
        array (
            'path' => 'DOCCLASS',
            'desc' => 'IA.DOCUMENT_CLASS',
            'fullname' => 'IA.DOCUMENT_CLASS',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',                    
                ),
            'id' => 2
        ),
        array (
            'path' => 'SALE_PUR_TRANS',
            'desc' => 'IA.DOCUMENT_TYPE',
            'fullname' => 'IA.DOCUMENT_TYPE',
            'type' => array (
                'type' => 'enum',
                'ptype' => 'enum',
                'validvalues' => array ('Order Entry','Purchasing','Inventory','TimeBill'),
                '_validivalues' => array ('S','P','I','T'),
                'validlabels' => array ('IA.ORDER_ENTRY','IA.PURCHASING','IA.INVENTORY','IA.TIMEBILL'),
                ),
            'id' => 3
        ),
        array (
            'path' => 'ENTITY',
            'desc' => 'IA.ENTITY',
            'fullname' => 'IA.ENTITY',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',                    
                ),
            'id' => 4
        ),
        array (
            'path' => 'TIMEPERIOD',
            'desc' => 'IA.TIMEPERIOD',
            'fullname' => 'IA.TIMEPERIOD',
            'type' => array (
                'type' => 'integer',
                'ptype' => 'integer',                    
                ),
            'id' => 5
        ),
        array (
            'path' => 'ITEMKEY',
            'desc' => 'IA.ITEM_ID',
            'fullname' => 'IA.ITEM_ID',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',                    
                ),
            'id' => 6
        ),
        array (
            'path' => 'QUANTITY',
            'desc' => 'IA.QUANTITY',
            'fullname' => 'IA.QUANTITY',
            'type' => array (
                'type' => 'integer',
                'ptype' => 'integer',                    
                ),
            'id' => 7
        ),
        array (
            'path' => 'VALUE',
            'desc' => 'IA.VALUE',
            'fullname' => 'IA.VALUE',
            'type' => array (
                'type' => 'decimal',
                'ptype' => 'decimal',                    
                ),
            'id' => 8
        ),
        array (
            'path' => 'TRX_VALUE',
            'desc' => 'IA.TRANSACTION_VALUE',
            'fullname' => 'IA.TRANSACTION_VALUE',
            'type' => array (
                'type' => 'decimal',
                'ptype' => 'decimal',                    
                ),
            'id' => 9
        ),
        array (
            'path' => 'TOTALCOST',
            'desc' => 'IA.TOTAL_COST',
            'fullname' => 'IA.TOTAL_COST',
            'type' => array (
                'type' => 'decimal',
                'ptype' => 'decimal',                    
                ),
            'id' => 10
        ),        
        array (
            'path' => 'WAREHOUSEKEY',
            'desc' => 'IA.WAREHOUSE_KEY',
            'fullname' => 'IA.WAREHOUSE_KEY',
            'type' => array (
                'type' => 'integer',
                'ptype' => 'integer',                    
                ),
            'id' => 11
        ),
        array (
            'path' => 'DEPTKEY',
            'desc' => 'IA.WAREHOUSE_KEY',
            'fullname' => 'IA.WAREHOUSE_KEY',
            'type' => array (
                'type' => 'integer',
                'ptype' => 'integer',                    
                ),
            'id' => 12
        ),        
        array (
            'path' => 'CURRENCY',
            'desc' => 'IA.CURRENCY',
            'fullname' => 'IA.CURRENCY',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',                    
                ),
            'id' => 13
        ),
        array (
            'path' => 'BASECURR',
            'desc' => 'IA.BASE_CURRENCY',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',                    
                ),
            'id' => 14
        ),
        array ( 'path' => 'DEPARTMENT.DEPT_NO', 'fullname' => 'IA.DEPARTMENT_ID', 'id' => 15),
        array ( 'path' => 'LOCATIONKEY', 'fullname' => 'IA.LOCATION_KEY', 'id' => 16),
        array ( 'path' => 'LOCATION.LOCATION_NO', 'fullname' => 'IA.LOCATION_ID', 'id' => 17),
        array ( 'path' => 'CUSTDIM1', 'fullname' => 'IA.CUSTOM_DIMENSION_1', 'id' => 23),
        array ( 'path' => 'CUSTDIM2', 'fullname' => 'IA.CUSTOM_DIMENSION_2', 'id' => 24),
        array ( 'path' => 'CUSTDIM3', 'fullname' => 'IA.CUSTOM_DIMENSION_3', 'id' => 25),
        array ( 'path' => 'CUSTDIM4', 'fullname' => 'IA.CUSTOM_DIMENSION_4', 'id' => 26),
        array ( 'path' => 'CUSTDIM5', 'fullname' => 'IA.CUSTOM_DIMENSION_5', 'id' => 27),
    ),

    'table' => 'detotals',
    'glDimMergeDBSchema' => true,
    'glDimMergeEntityObject' => true,
    'glDimMergeEntityFieldInfo' => true,
    'glDimMergeEntityNexus' => true,
    'glDimMergeEntityChildren' => true,
    'ignoredimensions' => array(
        'item',
        'warehouse',
    ),
    'printas' => 'IA.DOCUMENT_ENTRY_TOTALS',
    'pluralprintas' => 'IA.DOCUMENT_ENTRIES_TOTALS',
    'vid' => 'DOCCLASS',
    'module' => 'inv',
    'description' => 'IA.INVENTORY_QUANTITY_TOTALS',
);
