<?
$kSchemas['invtotal'] = array(
    'object' => array(
       'RECORDNO', 'NAME', 'UPDATETYPE','STATUS'
    ),
    'valueTranslateFields' => [
        'NAME',
    ],
    'schema' => array(
        'RECORDNO'        =>    'record#',
        'NAME'            =>    'name',
        'UPDATETYPE'    =>     'updatetype',
        'STATUS'        =>  'status',
    ),

    'fieldinfo' => array(
        array (
            'path'        => 'RECORDNO',
            'fullname'    => 'IA.RECORDKEY',
            'desc'        => 'IA.RECORDKEY',
            'type'         => array (
                'type'         => 'sequence',
                'ptype'     => 'integer',
                'size'         => 8,
                'maxlength' => 8,
            ),
            'required' => false,
            'hidden' => true,
            'readonly' => true
        ),
        array (
            'path'        => 'NAME',
            'fullname'    => 'IA.NAME',
            'desc'        => 'IA.NAME',
            'type'         => array (
                'type'         => 'text',
                'ptype'     => 'text',
                'size'         => 30,
                'maxlength' => 30,
                'format'    => '/^[\w\s_\-]{1,30}$/'
            ),
            'required' => true
        ),
        array (
            'path'        => 'UPDATETYPE',
            'fullname'    => 'IA.UPDATE_TYPE',
            'desc'        => 'IA.UPDATE_TYPE',
            'type'         => array (
                'type'             => 'enum',
                'ptype'         => 'enum',
                'validlabels'     =>    array ('IA.ACCUMULATIVE','IA.PER_PERIOD', 'IA.CONTINUOUS'),
                'validvalues'     =>    array ('Accumulative','Per Period', 'Continuous'),
                '_validivalues' =>    array ('ACC','PERIOD','CONTINUOUS'),
            ),
        ),
        $gStatusFieldInfo
    ),

    'table'     =>    'ictotal',
    'printas'    =>    'IA.INVENTORY_TOTAL',
    'pluralprintas' => 'IA.INVENTORY_TOTALS',
    'vid'         =>    'NAME',
    'module'     =>    'inv',
    'nochatter' => true,
);

