<?
/**
*	FILE:		invcustbacklog.rpt
*	AUTHOR:		tgw
*	DESCRIPTION:	ent file inventory status report filters
*
*	(C) 2000, Intacct Corporation, All Rights Reserved
*
*	This document contains trade secret data that belongs to Intacct
*	Corporation and is protected by the copyright laws.  Information
*	herein may not be used, copied or disclosed in whole or in part
*	without prior written consent from Intacct Corporation.
*/

// changing the background color for ItemId & UPC filter labels
$InvRptCtrlGrpORLabel = $gInvRptCtrlORLabel;
$InvRptCtrlGrpORLabel['labelcssclass']		= 'rf_grplabel_cell';
$gInvRptCtlFromItemID['labelcssclass']	= 'rf_grplabel_cell';
$gInvRptCtlToItemID['labelcssclass']	= 'rf_grplabel_cell';
$gInvRptCtrlFromUPC['labelcssclass']		= 'rf_grplabel_cell';
$gInvRptCtrlToUPC['labelcssclass']		= 'rf_grplabel_cell';
$gInvRptCtrlItemOprFilter['fields'][0]['labelcssclass']	= 'rf_grplabel_cell';
$gInvRptCtrlItemOprFilter['fields'][1]['labelcssclass'] = 'rf_grplabel_cell';
$gInvRptCtrlUPCOprFilter['fields'][0]['labelcssclass']	= 'rf_grplabel_cell';
$gInvRptCtrlUPCOprFilter['fields'][1]['labelcssclass']	= 'rf_grplabel_cell';
//


$mod = Request::$r->_mod;
$gCurrencyFilter['hidden'] = !IsMCPEnabled($mod);

if ( IsMultiEntityCompany() ) {
	$deptlocationgroup = array('title' => 'IA.LOCATION_DEPARTMENT_FILTERS',
				   'fields' => array(	$gLocationPick,
							$gDepartmentPick,
							$gLocationSubFilter,
				)
			);
}

$kSchemas['invcustbacklog'] = array (
	'schema' => array (
		array (
			'ITEM' => 'item',
			'CUSTOMER' => 'customer',
			'STATE' => 'state',
		)
	),
	'individualreport' => array (
		'LOCATION'		=> array ( 'default' => false ),
		'DEPARTMENT'	=> array ( 'default' => false ),
	),
	'promptonrun' => array (
		'REPORTINGTIMEPERIODFIELDS'	=> array ( 'default' => false ),
		'STARTENDDATES' => array ( 'default' => false ),
		'LOCATION'		=> array ( 'default' => false ),
		'DEPARTMENT'	=> array ( 'default' => false ),
	),
	'fieldgroup' => array (
		'REPORTINGTIMEPERIODFIELDS' => $gReportingTimePeriodFieldGroup,
		'STARTENDDATES' => $gStartDateEndDateFieldGroup,
		'ITEMOPRFILTER' => $gInvRptCtrlItemOprFilter,
		'UPCOPRFILTER' => $gInvRptCtrlUPCOprFilter,
		'FROMTOITEM' => array(  'layout' => 'landscape',
					'fields' => array($gInvRptCtlFromItemID,
							  $gInvRptCtlToItemID
						)
					),
		'FROMTOUPC' => array(	'layout' => 'landscape',
					'fields'=>array($gInvRptCtrlFromUPC,
							$gInvRptCtrlToUPC
						)
					),
		'FROMTOCUST' => array(	'layout' => 'landscape',
					'fields'=>array($gInvRptCtlFromCustomerID,
							$gInvRptCtlToCustomerID,
						)
					),
		'FROMTOVALUE' => array(	'layout' => 'landscape',
					'fields'=>array($gInvRptCtlFromValue,
							$gInvRptCtlToValue,
						)
					),
	),
	'fieldinfo' => array ( 
		'lines' => array(
				$gTimePeriodGroupFieldInfo,
				array(
					'title' => 'IA.FILTERS',
					'fields' => array(
						$gInvRptCtrlReportOn,
						array(	
						    'type' => array('type' => 'fieldgroup'), 
							'path' => 'FROMTOITEM'
                        ),
						$InvRptCtrlGrpORLabel,
						array(
							'fullname' => 'IA.ITEM_OPRFILTER',
							'type' => array('type' => 'fieldgroup'), 
							'path' => 'ITEMOPRFILTER',
						),
						$gInvRptCtrlORLabel,
						array(	
						    'type' => array('type' => 'fieldgroup'), 
							'path' => 'FROMTOUPC'
                        ),
						$InvRptCtrlGrpORLabel,
						array(
							'fullname' => 'IA.UPC_OPRFILTER',
							'type' => array('type' => 'fieldgroup'), 
							'path' => 'UPCOPRFILTER',
						),
						array(	
						    'type' => array('type' => 'fieldgroup'), 
							'path' => 'FROMTOCUST'
                        ),
						array(	
						    'type' => array('type' => 'fieldgroup'), 
							'path' => 'FROMTOVALUE'
                        ),
						
						array (
							'fullname' => 'IA.MIN_MAX_APPLIES_TO',
							'type' => array (
								'type' 			=>	'enum',
								'ptype' 		=>	'enum',
								'validvalues' 	=>	array (  
															'Order Price',
															'Days to Ship Date',
															'Order Number',
														),
								'_validivalues'	=>	array (  'IV' , 'IUC', 'IQ', 'DLA'),
								'validlabels' 	=>	array ( 'IA.ORDER_PRICE','IA.DAYS_TO_SHIP_DATE','IA.ORDER_NUMBER',),
									'default' 		=> 'Order Price',
								),
							'path' => 'VALUEMODE'
						),
						$gCurrencyFilter,
					),
				),
				$deptlocationgroup,
				array(
					'title' => 'IA.FORMAT',
					'fields' => array(
						array (
							'fullname' => 'IA.SORT_BY',
							'type' => array (
								'type' 			=>	'enum',
								'ptype' 		=>	'enum',
								'validvalues' 	=>	array (  
															'Ship Date',
															'Ship Date Desc',
															'Customer', 
															'Customer Desc',
															'Order Number',
															'Order Number Desc',
															'Order Price', 
															'Order Price Desc',
														),
								'_validivalues'	=>	array (  'I' , 'AV', 'DV', 'AUC', 'DUC', 'AQ', 'DQ'),	
								'validlabels' 	=>	array (  'IA.SHIP_DATE','IA.SHIP_DATE_DESC','IA.CUSTOMER','IA.CUSTOMER_DESC','IA.ORDER_NUMBER','IA.ORDER_NUMBER_DESC','IA.ORDER_PRICE','IA.ORDER_PRICE_DESC',),
								'default' 		=> 'Ship Date',
							),
							'path' => 'SORTMODE'
						),
					),
				)
			)
		),
	'controls' => $gInvRptControls,
	'popupfilterflds'=> array('FROMCUSTOMERID', 'TOCUSTOMERID'),
	'layout' => 'frame',
	'layoutproperties' => $gInvRptLayoutProperties,
	'xsl_file' => 'invbacklog',
	'printas'=>'IA.CUSTOMER_BACKLOG',
	'module' => 'inv',
	'helpfile' => 'Run_a_Customer_Backlog_Report'
);
// override the height of the frame
$kSchemas['invcustbacklog']['layoutproperties']['rows'] = "*, 0";

//Check whether report has been called SFDC User
if (IsSalesforceUser()) {
  include_once("backend_sforce.inc");
	// Change layout  for Processoffline etc
	$kSchemas["invcustbacklog"]["controls"] = array( 
													kShowHTML,
													kShowPDF,
													kShowExcel,
													kShowCSV,
													kShowText,
												);

	///Readonly fields Array
	$readOnlyArr = array('FROMCUSTOMERID', 'TOCUSTOMERID');

	// Make Schema for readonly and exclude fields as per arguments array 
    /** @noinspection PhpUndefinedVariableInspection */
    $kSchemas = MakeSFDCUserSchema($kSchemas, "invcustbacklog", $excludeArr, $readOnlyArr);

}


