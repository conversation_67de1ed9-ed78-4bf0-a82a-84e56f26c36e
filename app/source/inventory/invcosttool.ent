<?
/**
 * Manager class for health check cost computation
 *
 * @file cost.ent
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation, All Rights Reserved
 */

global $gBooleanType;
$kSchemas['invcosttool'] = array(
    'object' => array(
        'ITEMID',
        'WAREHOUSEID',
    ),
    'schema'  => array(
        true
    ),
    'fieldinfo' => array(
        array(
            'path' => 'ITEMID',
            'desc' => 'IA.ITEM',
            'fullname' => 'IA.ITEM',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'pickentity' => 'itempick',
                'entity' => 'item',
                'maxlength' => 40,
                'size' => 20,
                'restrict' => array(
                    array(
                        'pickField' => 'ITEMTYPE',
                        'operand' => 'IN',
                        'value' => array('NI', 'NS', 'K'),
                    ),
                ),
            ),
            'nonew'	=> true,
            'id' => 13
        ),
        array (
            'fullname' => 'IA.WAREHOUSE',
            'type' => array (
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'warehouse',
                'maxlength' => 40,
                'format' => $gWarehouseIDFormat
            ),
            'desc' => 'IA.WAREHOUSEID',
            'path' => 'WAREHOUSEID',
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'renameable' => true,
            'onchange' => "SetToValue(this, 'TOWAREHOUSEID')",
            'id' => 934
        ),
        array (
            'fullname' => 'IA.SELECT_TO_FETCH_FIX',
            'type' => array (
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'warehouse',
                'maxlength' => 40,
                'format' => $gWarehouseIDFormat
            ),
            'desc' => 'IA.WAREHOUSEID',
            'path' => 'WAREHOUSEID',
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'renameable' => true,
            'onchange' => "SetToValue(this, 'TOWAREHOUSEID')",
            'id' => 934
        ),
    ),
    'printas' => 'IA.REVENUE_RECOGNITION_SCHEDULES',
    'pluralprintas' => 'IA.REVENUE_RECOGNITION_SCHEDULES',
    'module' => 'inv',
);
