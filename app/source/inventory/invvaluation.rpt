<?
/**
*    FILE:        invregister.ent
*    AUTHOR:        rpn
*    DESCRIPTION:    ent file inventory register filters
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/

// changing the background color for ItemId & UPC filter labels
$InvRptCtrlGrpORLabel = $gInvRptCtrlORLabel;
$InvRptCtrlGrpORLabel['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtlFromItemsonlyID['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtlToItemsonlyID['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlFromUPC['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlToUPC['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlItemOprFilter['fields'][0]['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlItemOprFilter['fields'][1]['labelcssclass'] = 'rf_grplabel_cell';
$gInvRptCtrlUPCOprFilter['fields'][0]['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlUPCOprFilter['fields'][1]['labelcssclass']    = 'rf_grplabel_cell';
//

$mcmesubscribed = IsMCMESubscribed();

$summaryLevelStr = array (
                        'fullname' => 'IA.SUMMARY_LEVEL',
                        'type' => array (
                            'type'             =>    'enum',
                            'ptype'         =>    'enum',
                            'validvalues'     =>    array (  'Detail', 'Item Summary', 'Warehouse Summary'),
                            '_validivalues'    =>    array (  'D' , 'I', 'W'),    
                            'validlabels' => array('IA.DETAIL', 'IA.ITEM_SUMMARY', 'IA.WAREHOUSE_SUMMARY'),
                            'default'         => 'Detail',
                        ),
                        'path' => 'SUMMARYMODE'
                    );



$kSchemas['invvaluation'] = array (
    'schema' => array (
        array (
            'FROMITEMID' => 'fromitemid',
            'TOITEMID' => 'toitemid',
            'FROMVALUE' => 'fromvalue',
            'TOVALUE' => 'tovalue',
            'FROMWAREHOUSEID' => 'fromwarehouseid',
            'TOWAREHOUSEID' => 'towarehouseid', 
            'SUMMARYMODE' => 'dummy',    
            'SORTMODE' => 'dummy',    
            'SHOWZEROQUANTITIES' => 'dummy',
            'HIDEINACTIVEITEM' => 'dummy',
            'HIDEINACTIVEWH' => 'dummy',
            'ASOFDATE' => 'asofdate',
            'LOCATION' => 'location',
        )
    ),
    'individualreport' => array (
        'LOCATION'        => array ( 'default' => false ),
        'DEPARTMENT'    => array ( 'default' => false ),
    ),
    'promptonrun' => array (
        'LOCATION'        => array ( 'default' => false ),
        'DEPARTMENT'    => array ( 'default' => false ),
    ),
    'fieldgroup' => array (
        'ITEMOPRFILTER' => $gInvRptCtrlItemOprFilter,
        'UPCOPRFILTER' => $gInvRptCtrlUPCOprFilter,
        'FROMTOITEM' => array(  'layout' => 'landscape',
                    'fields' => array($gInvRptCtlFromItemsonlyID,
                            $gInvRptCtlToItemsonlyID,
                        )
                    ),
        'FROMTOUPC' => array(    'layout' => 'landscape',
                    'fields'=>array($gInvRptCtrlFromUPC,
                            $gInvRptCtrlToUPC,
                        )
                    ),
        'FROMTOWH' => array(    'layout' => 'landscape',
                    'fields'=>array($gInvRptCtlFromWarehouseID,
                            $gInvRptCtlToWarehouseID,
                        )
                    ),
        'FROMTOVALUE' => array(    'layout' => 'landscape',
                    'fields'=>array($gInvRptCtlFromValue,
                            $gInvRptCtlToValue,
                        )
                    ),
    ),
    'fieldinfo' => array (
        'userprefs' => true,
        'lines' => array(
            array (
                'title' => 'IA.TIME_PERIODS',
                'fields' => array (
                    $gReportAsOfDateFilter,
                ),
            ),
            array(
                'title' => 'IA.FILTERS',
                'fields' => array(
                    $gInvRptCtrlReportOn,
                    array('type' => array('type' => 'fieldgroup'),'path' => 'FROMTOITEM'),
                    $InvRptCtrlGrpORLabel,
                    array(
                        'fullname' => 'IA.ITEM_OPRFILTER',
                        'type' => array('type' => 'fieldgroup'),
                        'path' => 'ITEMOPRFILTER',
                    ),
                    $gInvRptCtrlORLabel,
                    array('type' => array('type' => 'fieldgroup'), 'path' => 'FROMTOUPC'),
                    $InvRptCtrlGrpORLabel,
                    array(
                        'fullname' => 'IA.UPC_OPRFILTER',
                        'type' => array('type' => 'fieldgroup'),
                        'path' => 'UPCOPRFILTER',
                    ),
                    $gInvRptCtlProdLineID,
                    array('type' => array('type' => 'fieldgroup'), 'path' => 'FROMTOWH'),
                    array('type' => array('type' => 'fieldgroup'), 'path' => 'FROMTOVALUE'),
                    array (
                        'fullname' => 'IA.MIN_MAX_APPLIES_TO',
                        'type' => array (
                            'type'             =>    'enum',
                            'ptype'         =>    'enum',
                            'validvalues'     =>    array (
                                                        'Item Value',
                                                        'Item Unit Cost',
                                                        'Item Quantity',
                                                        'Item Last Cost',
                                                        'DLA days'
                                                    ),
                            '_validivalues'    =>    array (  'IV' , 'IUC', 'IQ', 'DLA'),
                            'validlabels' => array('IA.ITEM_VALUE','IA.ITEM_UNIT_COST','IA.ITEM_QUANTITY','IA.ITEM_LAST_COST','IA.DLA_DAYS'),
                            'default'         => 'Item Value',
                       ),
                       'path' => 'VALUEMODE'
                    ),
                    array (
                        'fullname' => 'IA.DLA_IS',
                        'type' => array (
                            'type'             =>    'enum',
                            'ptype'         =>    'enum',
                            'validvalues'     =>    array (
                                                        'Date Last Sold/Rcvd',
                                                        'Date Last Sold',
                                                        'Date Last Rcvd',
                                                        'Days since Last Sold/Rcvd',
                                                        'Days since Last Sold',
                                                        'Days since Last Rcvd',
                                                    ),
                            '_validivalues'    =>    array (  'IV' , 'IUC', 'IQ', 'DLA'),
                            'validlabels' => array('IA.DATE_LAST_SOLD_RCVD','IA.DATE_LAST_SOLD','IA.DATE_LAST_RCVD','IA.DAYS_SINCE_LAST_SOLD_RCVD','IA.DAYS_SINCE_LAST_SOLD','IA.DAYS_SINCE_LAST_RCVD'),
                            'default'         => 'Date Last Sold/Rcvd',
                        ),
                        'path' => 'DLAMODE'
                    ),
                    array (
                        'fullname' => 'IA.SHOW_ZERO_QUANTITIES',
                        'type' => array (
                            'type'             =>    'boolean',
                            'ptype'         =>    'boolean',
                        ),
                        'default'        => 'false',
                        'path' => 'SHOWZEROQUANTITIES'
                    ),
                    array (
                        'fullname' => 'IA.EXCLUDE_INACTIVE_ITEMS',
                        'type' => array (
                            'type'             =>    'boolean',
                            'ptype'         =>    'boolean',
                        ),
                        'default'        => 'false',
                        'path' => 'HIDEINACTIVEITEM'
                    ),
                    array (
                        'fullname' => 'IA.EXCLUDE_INACTIVE_WAREHOUSES',
                        'type' => array (
                            'type'             =>    'boolean',
                            'ptype'         =>    'boolean',
                        ),
                        'default'        => 'false',
                        'path' => 'HIDEINACTIVEWH'
                    ),
                    array (
                        'fullname' => 'IA.SHOW_ACTUAL_COST_FOR_NEGATIVE_BALANCES',
                        'type' => array (
                            'type'          =>    'boolean',
                            'ptype'         =>    'boolean',
                        ),
                        'default'        => 'false',
                        'path' => 'SHOWNEGVALUES'
                   ),
                   array (
                        'fullname' => 'IA.SHOW_QUANTITIES_WITH_VALUE_ON_SUMMARY',
                        'type' => array (
                            'type'          =>    'boolean',
                            'ptype'         =>    'boolean',
                        ),
                        'default'        => 'false',
                        'path' => 'SHOWQTYWITHVALUE'
                   ),
                   array (
                       'fullname' => 'IA.INCLUDE_ONLY_WAREHOUSES_IN_LOCATION',
                       'type' => array (
                           'type'          =>    'boolean',
                           'ptype'         =>    'boolean',
                       ),
                       'default'        => 'false',
                       'hidden'         => 'true',      // PM wants a feature flag instead
                       'path' => 'SHOWLOCALWAREHOUSES'
                   ),
                   $gLocationPick
               ),
            ),
            array(
                'title' => 'IA.FORMAT',
                'fields' => array(
                    array (
                        'fullname' => 'IA.SORT_BY',
                        'type' => array (
                            'type'             =>    'enum',
                            'ptype'         =>    'enum',
                            'validvalues'     =>    array (
                                'Item',
                                'Value',
                                'Value Desc',
                                'Unit Cost',
                                'Unit Cost Desc',
                                'Quantity',
                                'Quantity Desc',
                                'Last Cost',
                                'Last Cost Desc',
                                'DLA',
                                'DLA Desc',
                            ),
                            '_validivalues'    =>    array (  'I' , 'AV', 'DV', 'AUC', 'DUC', 'AQ', 'DQ'),
                            'validlabels' => array('IA.ITEM','IA.VALUE','IA.VALUE_DESC','IA.UNIT_COST','IA.UNIT_COST_DESC','IA.QUANTITY','IA.QUANTITY_DESC','IA.LAST_COST','IA.LAST_COST_DESC','IA.DLA','IA.DLA_DESC'),
                            'default'         => 'Item',
                        ),
                    'path' => 'SORTMODE'
                ),

            ),
        ),
    ),
    ),
    'controls' => $gInvRptControls,
    'layout' => 'frame',
    'layoutproperties' => $gInvRptLayoutProperties,
    'xsl_file' => 'invvaluation',
    'printas' => 'IA.INVENTORY_VALUATION',
    'module' => 'inv',
    'helpfile' => 'Running_Inventory_Valuation_Reports'
);

// For atlas companies, we do not allow the users to summarize. Because the item cost can vary by warehouse and the  base currencies can be for different warehouses.
// Due to this we cannot sum/average the cost across warehouse as the sum is not the right amount.

 $kSchemas["invvaluation"]['fieldinfo']['lines'][2]['fields'][] = $summaryLevelStr;

// override the height of the frame
$kSchemas['invvaluation']['layoutproperties']['rows'] = "*, 0";

