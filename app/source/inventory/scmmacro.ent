<?php

/**
 * Entity file for scm macro
 */
$kSchemas['scmmacro'] = array(
    'children' => array(
        'documentparams' => array ( 
            'fkey' => 'sourcedocparkey', 'invfkey' => 'record#', 'table' => 'docpar', 'join' => 'outer'
        ),
    ),
    'object' => array(
        'RECORDNO',
        'NAME',
        'DESCRIPTION',
        'SOURCEDOCPARKEY',
        'SOURCEDOCPARID',
        'TARGETDOCFROM',
        'PRIORVERSIONKEY',
        'STATUS',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'NAME' => 'name',
        'DESCRIPTION' => 'description',
        'SOURCEDOCPARKEY' => 'sourcedocparkey',
        'SOURCEDOCPARID' =>  'documentparams.docid',
        'TARGETDOCFROM' => 'targetdocfrom',
        'PRIORVERSIONKEY' => 'priorversionkey',
        'STATUS'    => 'status',
        'WHENCREATED' => 'whencreated', 
        'WHENMODIFIED' => 'whenmodified', 
        'CREATEDBY' => 'createdby', 
        'MODIFIEDBY' => 'modifiedby',
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'SCMMACROKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'scmmacroentry',
            'path' => 'MEMBERS',            
        )
    ),    
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED',
    ),    
    'fieldinfo' => array(
        array(
            'fullname' => 'IA.RECORD_NO',
            'desc' => 'IA.RECORD_NO',
            'path' => 'RECORDNO',
            'type' => array('ptype' => 'text', 'type' => 'text',
                'maxlength' => 8, 'format' => '/[\w\s_\-\.]{0,8}/'),
            'hidden' => true,
            'id' => 1
        ),
        array(
            'fullname' => 'IA.NAME',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
            ),
            'desc' => 'IA.NAME',
            'path' => 'NAME',
            'id' => 3
        ),
        array(
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength' => 500,
            ),
            'desc' => 'IA.DESCRIPTION',
            'path' => 'DESCRIPTION',
            'id' => 4
        ),
        array(
            'fullname' => 'IA.SOURCE_TRANSACTION_TYPE',
            'desc' => 'IA.SOURCE_TRANSACTION_TYPE',
            'required' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'documentparams',
            ),
            'path' => 'SOURCEDOCPARID',
            'id' => 5
        ),        
        array(
            'fullname' => 'IA.TARGET_FROM',
            'type' => array(
                'ptype' => 'enum',
                'type'  => 'enum',
                'validlabels' => array('IA.DOCUMENT', 'IA.ENTRIES'),
                'validvalues' => array('document', 'entries'),
                '_validivalues' => array('D', 'E')
            ),
            'desc' => 'IA.TARGET_FROM',
            'required' => true,
            'path' => 'TARGETDOCFROM',
            'id' => 6
        ),
        $gStatusFieldInfo,
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
    'api' => array(
        'GET_BY_GET' => true,
        'ITEMS_ALIAS' => ['SCMMACROMEMBERS'],
        'ITEM_ALIAS' => ['SCMMACROMEMBER'],
        'ITEMS_INTERNAL' => ['MEMBERS'],
        'PERMISSION_MODULES' => array('co'),
        'PERMISSION_READ' => 'services/invsetup',
        'PERMISSION_CREATE' => 'services/invsetup',
        'PERMISSION_UPDATE' => 'services/invsetup',
        'PERMISSION_DELETE' => 'services/invsetup',        
    ),
    'printas' => 'IA.SCM_MACRO',
    'pluralprintas' => 'IA.SCM_MACROS',
    'auditcolumns' => true,
    'table' => 'scmmacrohdr',
    'module' => 'inv',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'renameable' => true,
    'nochatter' => true,
);
