<?

$kitemlistQueries = array(
  'QRY_ITEMLIST_SELECT_ICITEM' => array(
     'QUERY'=> "SELECT  it.itemid, it.itemid||' - '||it.name item, uom.unit uom, it.productlinekey productlineid,
			it.productlinekey||' - '||pl.description prodline,it.producttype,it.ship_weight, 
			decode(it.cost_method, 'S','Standard','A','Average','F','FIFO',	'L','LIFO','O','Lot','#','Serial#', NULL) cost_method,
			decode(it.status, 'T', 'Active', 'F', 'In Active', NULL) status
		FROM icitem it, icprodline pl, icuom uom
		WHERE ((it.itemid >= ? and it.itemid <= ? ) or it.productlinekey like ? ) and uom.record# (+)= it.std_uom and
			pl.productlineid (+) = it.productlinekey and uom.cny# (+)= it.cny# and pl.cny# (+) = it.cny# and it.cny# = ?
		ORDER BY  1", 
     'ARGTYPES' => array( 'text', 'text', 'text', 'integer' )
  ),
  'QRY_ITEMLIST_SELECT_WAREHOUSE' => array(
     'QUERY'=> "SELECT	itemtotals.itemkey,itemtotals.warehousekey warehouseid, itemtotals.warehousekey||' - '||wh.name warehouse, 
		0 qtyonhand, sum(itemtotals.quantity) qtyonorder, 0 qtyonhold, 0 qtyavailable
		FROM	icitemtotals itemtotals, icwarehouse wh
		WHERE	(itemtotals.itemkey >= ? and itemtotals.itemkey <= ? ) and 
			(itemtotals.warehousekey >= ? and itemtotals.warehousekey <= ? ) 
			and wh.location_no (+) = itemtotals.warehousekey and 
			it1.totalkey (+)= 2 and 
			itemtotals.cny# = ? and wh.cny# = itemtotals.cny#
		GROUP BY itemtotals.itemkey,itemtotals.warehousekey,itemtotals.warehousekey||' - '||wh.name, 0, 0, 0",
     'ARGTYPES' => array( 'text', 'text', 'text', 'text', 'integer' )
  ),
);

