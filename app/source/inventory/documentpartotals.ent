<?

$kSchemas['documentpartotals'] = array(
    'children' => array(
        'documentparams' => array(
            'fkey' => 'docparkey',
            'invfkey' => 'record#',
            'table' => 'docpar'
        ),
        'invtotal' => array(
            'fkey' => 'totalkey',
            'invfkey' => 'record#',
            'table' => 'ictotal'
        ),
    ),
    'object' => array(
        'RECORDNO',
        'DOCPARNO',
        'Q_QV',
        'TOTALID',
        'SIGN',
    ),

    'valueTranslateFields' => [
        'TOTALID',
    ],

    'schema' => array(
        'RECORDNO' => 'record#',
        'DOCPARNO' => 'docparkey',
        'Q_QV' => 'q_qv',
        'TOTALID' => 'invtotal.name',
        'SIGN' => 'sign',
    ),


    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'desc' => 'IA.RECORD_NUMBER',
            'type' => array(
                'type' => 'sequence',
                'ptype' => 'integer',
                'size' => 8,
                'maxlength' => 8
            ),
            'hidden' => true,
            'readonly' => true,
            'id' => 1,
        ),
        array(
            'path' => 'DOCPARNO',
            'fullname' => 'IA.DOCUMENT_NAME',
            'desc' => 'IA.DOCUMENT_NAME',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'documentparams',
                'size' => 10
            ),
            'required' => true,
            'id' => 2,
        ),
        array(
            'path' => 'Q_QV',
            'fullname' => 'IA.MAINTAIN',
            'desc' => 'IA.MAINTAIN',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => array( 'IA.QUANTITY', 'IA.VALUE', 'IA.QUANTITY_AND_VALUE' ),
                'validvalues' => array(
                    'Quantity',
                    'Value',
                    'Quantity & Value'
                ),
                '_validivalues' => array(
                    'Q',
                    'V',
                    'QV'
                ),
            ),
            'required' => true,
            'id' => 3,
        ),
        array(
            'path' => 'TOTALID',
            'fullname' => 'IA.INVENTORY_TOTAL',
            'desc' => 'IA.INVENTORY_TOTAL',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'invtotal',
                'size' => 15,
                'maxlength' => 30,
            ),
            'required' => true,
            'id' => 4,
        ),
        array(
            'path' => 'SIGN',
            'fullname' => 'IA.ADD_SUBTRACT',
            'desc' => 'IA.ADD_SUBTRACT',
            'required' => true,
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'validlabels' => array( 'IA.ADD', 'IA.SUBTRACT' ),
                'validvalues' => array(
                    'Add',
                    'Subtract'
                ),
                '_validivalues' => array(
                    '1',
                    '-1'
                ),
            ),
            'id' => 5,
        ),
    ),
    'table' => 'docpartotals',
    'parententity' => 'documentparams',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'printas' => 'IA.DOCUMENT_PARAMATERS_TOTAL',
    'pluralprintas' => 'IA.DOCUMENT_PARAMETERS_TOTALS',
    'module' => 'inv'
);
