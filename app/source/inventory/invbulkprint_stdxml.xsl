<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">

<xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/> 
<xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/> 

	<xsl:template match="/">
		<xsl:apply-templates/>			
	</xsl:template>
	
	<xsl:template match="reportdata">
		<xsl:apply-templates/>
	</xsl:template>
	
	<xsl:template match="report">

		<report
			department 		= "{@department}"
			location 		= "{@location}"
			orientation 	= "Landscape"
			report_date		= "{@reportdate}"
			report_time		= "{@reporttime}"
			align_currency 	= "left"
			page_number 	= "Y"
			action 			= "editor_multi_delivery.phtml"
			sess			= "{@sess}"
			done			= "{@done}"
			footer_allpages	= "Y"
		>

	<xsl:if test="(@orientation = 'Portrait')">
				<xsl:attribute name="maxfit">Y</xsl:attribute>
	</xsl:if>
	<company s="2"><xsl:value-of select="@co"/></company>
	<title s="3"><xsl:value-of select="@title"/></title>
	<title s="3"><xsl:value-of select="@title2"/></title>
	<footer s="footer" lines="1"><xsl:value-of select="@titlecomment"/></footer>

		<header s="header">
			<hrow s="header">
				<hcol id="0" s="18">
					<xsl:attribute name="submitbutton">1</xsl:attribute>
					<xsl:attribute name="submitbutton_value">IA.DELIVER</xsl:attribute>
					<xsl:attribute name="submitbutton_name">.deliver</xsl:attribute>
					<xsl:attribute name="class">Task</xsl:attribute>
					<hiddenvars hiddenvars_name=".op" hiddenvars_value="{@deliverop}"/>
					<hiddenvars hiddenvars_name=".do" hiddenvars_value="view"/>
					<hiddenvars hiddenvars_name=".start_date" hiddenvars_value="{@start_date}"/>
					<hiddenvars hiddenvars_name=".end_date" hiddenvars_value="{end_date}"/>
					<hiddenvars hiddenvars_name=".start_customer" hiddenvars_value="{@start_customer}"/>
					<hiddenvars hiddenvars_name=".end_customer" hiddenvars_value="{@end_customer}"/>
					<hiddenvars hiddenvars_name=".period" hiddenvars_value="{@period}"/>
					<hiddenvars hiddenvars_name=".nemail" hiddenvars_value="{@nemail}"/>
					<hiddenvars hiddenvars_name=".processed" hiddenvars_value="{@processed}"/>
					<hiddenvars hiddenvars_name=".xslformat" hiddenvars_value="{@xslformat}"/>
					<hiddenvars hiddenvars_name=".sendersemail" hiddenvars_value="{@sendersemail}"/>
					<hiddenvars hiddenvars_name=".sendersphone" hiddenvars_value="{@sendersphone}"/>
					<hiddenvars hiddenvars_name=".sendersname" hiddenvars_value="{@sendersname}"/>
					<hiddenvars hiddenvars_name=".messagetext" hiddenvars_value="{@messagetext}"/>
					<hiddenvars hiddenvars_name=".marketingtext" hiddenvars_value="{@marketingtext}"/>
				</hcol>
			</hrow>
			<hrow s="51">
				<hcol id="0" s="17">IA.WAREHOUSE</hcol>
				<hcol id="0" s="17">IA.TRANSACTION</hcol>
				<hcol id="0" s="16">IA.DATE</hcol>
				<hcol id="0" s="16">IA.DUE_DATE</hcol>
				<hcol id="0" s="18">IA.TOTAL_AMOUNT</hcol>
				<xsl:call-template name="PrintCheckboxHcol">
					<xsl:with-param name="idtype">invoice</xsl:with-param>
				</xsl:call-template>
				<xsl:call-template name="EmailCheckboxHcol">
					<xsl:with-param name="idtype">invoice</xsl:with-param>
				</xsl:call-template>
			</hrow>
		</header>
		<body s="body">
			<row s="12">
				<col  s="19" colspan="{/reportdata/report/@noofcolumns}" ></col>
			</row>
			<xsl:apply-templates/>
		</body>
		<xsl:call-template name="stylegroups"/>
		<script language="javascript">
			<xsl:apply-templates select="@javascript"/>
			<xsl:call-template name="script"/>		
			<xsl:call-template name="doselect"/>		
			<xsl:call-template name="checkEmail"/>		
		</script>


</report>

</xsl:template>



<xsl:template match="NODATA">
    <xsl:if test="string(@NODATA)=1">
		<row s="14">
			<col id="0" s="19" colspan="8">IA.NO_DATA_FOUND</col>
		</row>
    </xsl:if>
</xsl:template>

	<xsl:template match="PODOCS">
		<row s="12">
			<col id="0" s="23">
				<xsl:attribute name="href">
					<xsl:value-of select="@ENTITYHREF"/>
				</xsl:attribute>
				<xsl:value-of select="@ENTITY"/>
				<hiddenvars hiddenvars_name=".invoice[{@INVOICECOUNTER}]" hiddenvars_value="{@DOCNOKEY}"/>
			</col>
			<col id="0" s="23">
				<xsl:attribute name="href">
					<xsl:value-of select="@DOCHREF"/>
				</xsl:attribute>
				<xsl:value-of select="@DOCNOKEY"/>
			</col>
			<col id="0" s="25"><xsl:value-of select="@DOCDATE"/></col>
			<col id="0" s="25"><xsl:value-of select="@WHENDUE"/></col>
			<col id="0" s="26"><xsl:value-of select="@TOTALAMT"/></col>
		   <col id="0" s="25">
			   <xsl:attribute name="checkbox">1</xsl:attribute>
			   <xsl:attribute name="checkbox_value">
				<xsl:if test="contains(@DELIVERYOPTIONS,'P')">
					<xsl:attribute name="checked">1</xsl:attribute>
				</xsl:if>
				   <xsl:text>P</xsl:text>
			   </xsl:attribute>
			   <xsl:attribute name="checkbox_name">
				   <xsl:text>.P[</xsl:text>
				   <xsl:value-of select="@DOCNOKEY"/>
				  <xsl:text>]</xsl:text>
			  </xsl:attribute>
			</col>
		   <col id="0" s="25">
			   <xsl:attribute name="checkbox">1</xsl:attribute>
				<xsl:if test="contains(@DELIVERYOPTIONS,'E')">
					<xsl:attribute name="checked">1</xsl:attribute>
				</xsl:if>
			   <xsl:attribute name="checkbox_value">
				   <xsl:text>E</xsl:text>
			   </xsl:attribute>
			   <xsl:attribute name="checkbox_name">
				   <xsl:text>.E[</xsl:text>
				   <xsl:value-of select="@DOCNOKEY"/>
				   <xsl:text>]</xsl:text>
			  </xsl:attribute>
				<xsl:attribute name="onclick">
					<xsl:text>javascript:checkEmail(this.form, '</xsl:text><xsl:value-of select="@DOCNOKEY"/><xsl:text>')</xsl:text>
				</xsl:attribute>
			</col>
			<col id="0" s="25">
				<xsl:attribute name="textbox">1</xsl:attribute>
				<xsl:attribute name="textbox_size">20</xsl:attribute>
				<xsl:attribute name="textbox_value"><xsl:value-of select="@EMAIL1"/></xsl:attribute>
				<xsl:attribute name="textbox_name">
				   <xsl:text>.custemail[</xsl:text>
				   <xsl:value-of select="@DOCNOKEY"/>
				  <xsl:text>]</xsl:text>
				</xsl:attribute>
				<xsl:attribute name="onblur">
					<xsl:text>javascript:checkEmail(this.form, '</xsl:text><xsl:value-of select="@DOCNOKEY"/><xsl:text>')</xsl:text>
				</xsl:attribute>
			</col>




	</row>
	<row s="14">
		<col id="0" s="19"></col>
	</row>
</xsl:template>
<xsl:template match="TOTALS">
	<row s="14">
		<col id="0" s="33" colspan="3"></col>
		<col id="0" s="33">IA_TOTALS</col>
		<col id="0" s="32"><xsl:value-of select="@GRNTOTALAMT"/></col>
		<col id="0" s="33" colspan="3"></col>
	</row>
</xsl:template>
</xsl:stylesheet>
