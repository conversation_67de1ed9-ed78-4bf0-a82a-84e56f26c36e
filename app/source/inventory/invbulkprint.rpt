<?
/**
*    FILE:        invbulkprint
*    AUTHOR:        tgw    
*    DESCRIPTION:    ent file inventory status report filters
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/
import('DocumentManager');
$transtypes = GetDocumentParams("inv");


    $cny = GetMyCompany();
    $company = array();
    $compQry = " select name as companyname,contactname,contactphone,contactemail,address1,address2,city,zipcode,state,country, logo 
				 from company 
				 where record# = ".$cny;
    $company  = QueryResult($compQry);
    $company = $company[0];

    $co = GetAcctCompany($_userid);
    $mess= $co['MESSAGE_TEXT'];
    $mtext= $co['MARKETING_TEXT'];
    $InvRptCtlSenderEmail = 
            array (
                'fullname' => 'IA.SENDER_EMAIL',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text',
                ),
                'value' => $company['CONTACTEMAIL'],
                'required' => false,
                'desc' => 'IA.SENDER_EMAIL',
                'path' => 'SENDERSEMAIL'
            );
    $InvRptCtlSenderName = 
            array (
                'fullname' => 'IA.SENDER_NAME',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text',
                ),
                'value' => $company['CONTACTNAME'],
                'required' => false,
                'desc' => 'IA.SENDER_NAME',
                'path' => 'SENDERSNAME'
            );
    $InvRptCtlSenderPhone = 
            array (
                'fullname' => 'IA.SENDER_PHONE',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text',
                ),
                'value' => $company['CONTACTPHONE'],
                'required' => false,
                'desc' => 'IA.SENDER_PHONE',
                'path' => 'SENDERSPHONE'
            );

    $kSchemas['invbulkprint'] = array (
    'schema' => array (
        array (
            'ITEM' => 'item',
            'VENDOR' => 'vendor',
            'STATE' => 'state',
            'START_DATE' => 'start_date',
            'END_DATE' => 'end_date',
            'DOCTYPE' => 'doctype',
        )
    ),
    'fieldinfo' => array ( 
            'lines' => array(
                        array(
                            $InvRptCtlSenderEmail,
                            $InvRptCtlSenderName,
                            $InvRptCtlSenderPhone,
                        ),
                        array(
                            $gInvRptCtlStartDate,
                            $gInvRptCtlEndDate,
                            $gInvRptCtlFromItemID,
                            $gInvRptCtlToItemID,
                            $gInvRptCtlFromWarehouseID,
                            $gInvRptCtlToWarehouseID,
                            array(
                                'fullname' => 'IA.TRANSACTION_TYPE',
                                'type'         => array (
                                    'type' => 'enum',
                                    'ptype' => 'enum',
                                    'validvaluesfunc'    => 'return GetDocumentParams("inv");',
                                ),
                                'default' => $transtypes[0],
                                'desc' => 'IA.DOCTYPE',
                                'path' => 'DOCTYPE',
                                'noedit' => true,
                                'nonew' => true,
                                'noview' => true
                            )
                        )
            ),
    ),
    'controls' => array( kShowHTML,  ),
    'layout' => 'frame',
    'layoutproperties' => $gInvRptPopupLayoutProperties,
    'xsl_file' => 'invbulkprint',
    'printas'=>'IA.PRINT_INVENTORY_DOCUMENTS',
    'module' => 'inv',
    'helpfile'    => 'Inventory_Status_Report',
    );


