<?xml version='1.0' encoding='UTF-8'?>
<ROOT assoc="T" >
    <entity>item</entity>
    <pages>
        <page assoc="T" >
            <title>IA.GENERAL_INFO</title>
            <fields>
				<field>RECORDNO</field>
				<field assoc="T">
					<path>ITEMTYPE</path>
					<forcerequired>true</forcerequired>
					<required>true</required>
				</field>
                <field>ITEMID</field>
                <field>NAME</field>
                <field>EXTENDED_DESCRIPTION</field>
                <field>PODESCRIPTION</field>
                <field>SODESCRIPTION</field>                
				<field>PRODUCTLINEID</field>				
				<field>COGSACCTKEY</field>
				<field>INVACCTKEY</field>
				<field>EXPENSEACCTKEY</field>
				<field>INCOMEACCTKEY</field>
				<field>OFFSETOEGLACCOUNTKEY</field>
				<field>OFFSETPOGLACCOUNTKEY</field>
				<field>DEFERREDREVACCTKEY</field>
				<field>DEFAULTREVRECTEMPLKEY</field>
				<field>SHIP_WEIGHT</field>
				<field>BASEPRICE</field>
				<field assoc="T">
					<path>UOMGRP</path>
					<hidden>1</hidden>
				</field>
				<field assoc="T">
					<path>COST_METHOD</path>
					<hidden>1</hidden>
				</field>
				<field>AVERAGE_COST</field>
				<field>STANDARD_COST</field>
				<field>WHENLASTSOLD</field>
                <field>WHENLASTRECEIVED</field>                
                <field>TAXABLE</field>
                <field>TAXGROUP.NAME</field>
                <field>DEFAULT_WAREHOUSE</field>
				<field>TAXCODE</field>
                <field>NOTE</field>
                <field>STATUS</field>
                <field>WHENCREATED</field>
                <field>WHENMODIFIED</field>
                <field>CREATEDBY</field>
                <field>MODIFIEDBY</field>
            </fields>
        </page>		

		<page assoc="T" >
			<title>IA.KITS_COMPONENTS</title>
			<fields>
			<SinglelineLayout assoc="T" key="hbox">
				<fullname>IA.REVENUE_POSTING_PREFERENCE</fullname>
				<columns>
					<column assoc="T">
						<path>REVPOSTING</path>
						<readonly>1</readonly>
					</column>
				</columns>
						<_func>SinglelineLayout</_func>
				</SinglelineLayout>
			<SinglelineLayout assoc="T" key="hbox">
				<fullname>IA.PRINT_FORMAT</fullname>
				<columns>
					<column assoc="T">
						<path>REVPRINTING</path>
						<readonly>1</readonly>
					</column>
				</columns>
						<_func>SinglelineLayout</_func>			
			</SinglelineLayout>
				<MultilineLayout assoc="T"  key="field" >
					<path>COMPONENT_INFO</path>
					<title>IA.KITS_COMPONENTS</title>
					<columns>
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>COMPONENTKEY</path>
									<size>20</size>
									<autofill>1</autofill>
									<fullname>IA.ITEM_ID</fullname>
								</_arg>
							</_args>
						</vbox>
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>ITEMDESC</path>
									<size>30</size>
									<autofill>1</autofill>
									<fullname>IA.ITEM_DESCRIPTION</fullname>
								</_arg>
							</_args>
						</vbox>
						
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>QUANTITY</path>
									<size>8</size>
									<autofill>1</autofill>
									<fullname>IA.NUMBER_OF_UNITS</fullname>
								</_arg>
							</_args>
						</vbox>
						<vbox assoc="T"  key="column" >
						<_func>vbox</_func>
						<_args>
							<_arg assoc="T" >
								<path>UNIT</path>
								<size>8</size>
								<autofill>1</autofill>
								<fullname>IA.STANDARD_UNIT_OF_MEASURE</fullname>
							</_arg>
						</_args>
					</vbox>
					<vbox assoc="T"  key="column" >
						<_func>vbox</_func>
						<_args>
							<_arg assoc="T" >
								<path>KCDLVRSTATUS</path>
								<fullname>IA.DEFAULT_DELIVERY_STATUS</fullname>
							</_arg>
						</_args>
					</vbox>
					<vbox assoc="T"  key="column" >
						<_func>vbox</_func>
						<_args>
							<_arg assoc="T" >
								<path>KCREVDEFSTATUS</path>
								<fullname>IA.DEFAULT_DEFERRAL_STATUS</fullname>
							</_arg>
						</_args>
					</vbox>
						<vbox assoc="T"  key="column" >
						<_func>vbox</_func>
						<_args>
							<_arg assoc="T" >
								<path>COSTMETHOD</path>
								<size>8</size>
								<fullname>IA.COST_METHOD</fullname>
							</_arg>
						</_args>
					</vbox>
						<vbox assoc="T"  key="column" >
						<_func>vbox</_func>
						<_args>
							<_arg assoc="T" >
								<path>COST</path>
								<size>8</size>
								<fullname>IA.STANDARD_COST</fullname>
							</_arg>
						</_args>
					</vbox>
						<vbox assoc="T"  key="column" >
						<_func>vbox</_func>
						<_args>
							<_arg assoc="T" >
								<path>ITEMTYPE1</path>
								<size>8</size>
								<fullname>IA.ITEM_TYPE</fullname>
							</_arg>
						</_args>
					</vbox>
					</columns>
					<_func>MultilineLayout</_func>
				</MultilineLayout>
			</fields>
		</page>

        <page assoc="T" >
	        <title>IA.TERM</title>
	        <fields>
	          <field>HASSTARTENDDATES</field>
	          <field>TERMPERIOD</field>
	          <field>TOTALPERIODS</field>
	          <field>COMPUTEFORSHORTTERM</field>
	          <field>RENEWALMACROID</field>
	        </fields>
		</page>
<page assoc="T" >
	        <title>IA.VSOE_INFORMATION</title>
	        <fields>
	          <field>VSOECATEGORY</field>
	          <field>VSOEDLVRSTATUS</field>
              <field>VSOEREVDEFSTATUS</field>
              <field>FULFILLMENTFOOTNOTE</field>
	        </fields>
	    </page>	
        <page assoc="T" >
        <title>IA.VENDOR_INFORMATION</title>
        <fields>
            <MultilineLayout assoc="T"  key="field" >
            <path>VENDOR_INFO</path>
            <title>IA.VENDOR_INFO</title>
            <columns>
                <column assoc="T" >
                    <path>VENDORID</path>
                    <size>40</size>
                </column>
				<column assoc="T" >
					<path>STOCKNO</path>
                    <size>15</size>
                    <fullname>IA.STOCK_NUMBER</fullname>
				</column>
				<column assoc="T" >
					<path>LEAD_TIME</path>
                	<size>15</size>
                	<fullname>IA.LEAD_TIME</fullname>
				</column>
				<column assoc="T" >
					<path>ECONOMIC_ORDER_QTY</path>
                    <size>15</size>
                    <fullname>IA.ECONOMIC_QUANTITY</fullname>
				</column>
				<column assoc="T" >
					<path>BEST_COST</path>
                    <size>16</size>
                    <fullname>IA.BEST_COST</fullname>
				</column>
				<column assoc="T" >
					<path>LAST_COST</path>
                    <size>16</size>
                    <fullname>IA.LAST_COST</fullname>
				</column>
            </columns>
            <_func>MultilineLayout</_func>
        </MultilineLayout>
        </fields>
    </page>
    <page assoc="T" >
        <title>IA.WAREHOUSE_INFORMATION</title>
        <fields>
            <MultilineLayout assoc="T"  key="field" >
            <path>WAREHOUSE_INFO</path>
            <title>IA.WAREHOUSE_INFO</title>
            <columns>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>WAREHOUSEID</path>
							<autofill>1</autofill>
                            <size>50</size>
                            <fullname>IA.WAREHOUSE_ID</fullname>
                        </_arg>
                        <_arg assoc="T" >
                        	<path>DEFAULT_SUBSECTION</path>
                        	<size>30</size>
	                        <fullname>IA.DEFAULT_SUBSECTION</fullname>
	                    </_arg>
	                    <_arg assoc="T" >
							<path>CYCLE</path>
		                    <size>30</size>
			            	<fullname>IA.CYCLE</fullname>
					    </_arg>
                    </_args>
                </vbox>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>STANDARD_COST</path>
							<size>20</size>
                            <fullname>IA.STANDARD_COST</fullname>
                        </_arg>
                        <_arg assoc="T" >
							<path>LAST_COST</path>
                        	<size>20</size>
                        	<fullname>IA.LAST_COST</fullname>
                    	</_arg>
                        <_arg assoc="T" >
                        	<path>AVERAGE_COST</path>
                        	<size>20</size>
                        	<fullname>IA.AVERAGE_COST</fullname>
                    	</_arg>
                    </_args>
                </vbox>

                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>                        
                        <_arg assoc="T" >
                        	<path>ECONOMIC_ORDER_QTY</path>
                        	<size>20</size>
                        	<fullname>IA.ECONOMIC_QUANTITY</fullname>
                    	</_arg>
                        <_arg assoc="T" >
                        	<path>REORDER_POINT</path>
                        	<size>20</size>
                        	<fullname>IA.REORDER_POINT</fullname>
                    	</_arg>
                    	<_arg assoc="T" >
                            <path>REORDER_METHOD</path>
                            <fullname>IA.REORDER_METHOD</fullname>                            
                        </_arg>
                    </_args>
                </vbox>
                <vbox assoc="T"  key="column" >
                <_func>vbox</_func>
                <_args>
                    <_arg assoc="T" >
                        <path>MIN_ORDER_QTY</path>
                        <size>20</size>
                        <fullname>IA.MIN_ORDER_QUANTITY</fullname>
                    </_arg>
                    <_arg assoc="T" >
                    	<path>MAX_ORDER_QTY</path>
                    	<size>20</size>
                    	<fullname>IA.MAX_ORDER_QUANTITY</fullname>
                	</_arg>
                </_args>
            </vbox>
            <vbox assoc="T"  key="column" >
                <_func>vbox</_func>
                <_args>
                    <_arg assoc="T" >
                        <path>MIN_STOCK</path>
                        <size>20</size>
                        <fullname>IA.MIN_STOCK</fullname>
                    </_arg>
                    <_arg assoc="T" >
                    	<path>MAX_STOCK</path>
                    	<size>20</size>
                    	<fullname>IA.MAX_STOCK</fullname>
                	</_arg>
                </_args>
            </vbox>
			<vbox assoc="T"  key="column" >
                <_func>vbox</_func>
                <_args>
                    <_arg assoc="T" >
                        <path>DATE_LASTSOLD</path>
                        <size>20</size>
                        <fullname>IA.DATE_LAST_SOLD</fullname>
                    </_arg>
                    <_arg assoc="T" >
                    	<path>DATE_LASTRECEIVED</path>
                    	<size>20</size>
                    	<fullname>IA.DATE_LAST_RECEIVED</fullname>
                	</_arg>
                </_args>
            </vbox>
			<vbox assoc="T"  key="column" >
                <_func>vbox</_func>
                <_args>
                    <_arg assoc="T" >
                        <path>DEFAULT_AISLE</path>
                        <fullname>IA.DEFAULT_AISLE</fullname>
                    </_arg>
                    <_arg assoc="T" >
                        <path>DEFAULT_ROW</path>
                        <fullname>IA.DEFAULT_ROW</fullname>
                    </_arg>
                    <_arg assoc="T" >
                        <path>DEFAULT_BIN</path>
                        <fullname>IA.DEFAULT_BIN</fullname>
                    </_arg>
                </_args>
            </vbox>
            </columns>
            <_func>MultilineLayout</_func>
        </MultilineLayout>
        </fields>
    </page>
    </pages>
    <helpfile>Adding_Editing_Viewing_Item_InformationSimplified</helpfile>
</ROOT>
