<?

    require 'document.qry';

/** @noinspection PhpUndefinedVariableInspection */
$krecurdocumentQueries = INTACCTarray_merge($krecurdocumentQueries, $kdocumentQueries);

//->Queries used out of 'recur' entity context
$kdocumentQueries['QRY_RECURDOCUMENT_FIND_CONTACTTAXGROUP'] = array (
    'QUERY'        => 'select taxable, taxgroupkey FROM contact WHERE contact.name = ? and contact.cny# = ?', 
    'ARGTYPES'     => array ('text', 'integer')
);


//->Queries used in
$kdocumentQueries['QRY_RECURDOCUMENT_FIND_TOTALS'] = array (
    'QUERY'        => 'select totalkey, q_qv, sign  from docpartotals where docpartotals.cny# = ? and docpartotals.docparkey = ?', 
    'ARGTYPES'     => array ('integer','integer')
);
$kdocumentQueries['QRY_RECURDOCUMENT_FIND_SUBTOTALS'] = array (
    'QUERY'        => 'select lineno, description, disc_charge , amount_perc,value,apportioned, glaccountkey, deb_cred, baseline, istax from docparsubtotal where (docparsubtotal.cny# = ? ) and (docparsubtotal.docparkey = ?) order by LINENO', 
    'ARGTYPES'     => array ('integer','integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GETDETAILS'] = array (
    'QUERY'     => 'select td.*, account.acct_no, tsd.schedulekey from taxdetail td, taxscheddetails tsd, glaccount account where tsd.schedulekey in (select schedkey from taxschedmap where itemgrpkey = ? and entgrpkey = ? and (docparkey =  ? or docparkey is null) and module = ? and cny# = ?)  and td.record# = tsd.detailkey  and account.record# = td.accountkey and td.cny# = ? and tsd.cny# = ? and account.cny# = ? order by td.record#',
    'ARGTYPES'    => array ('integer', 'integer', 'integer', 'text', 'integer', 'integer', 'integer', 'integer'),
);
$kdocumentQueries['QRY_RECURDOCUMENT_GET_WAREHOUSES'] = array (
    'QUERY'        => "SELECT location_no FROM icwarehouse WHERE status = 'T' and cny# =?",
    'ARGTYPES'    => array ('integer'),
);
$kdocumentQueries['QRY_AVAIL_ONHAND_QTY'] = array (
    'QUERY'     => 'select sum(qtyonhand) as QUANTITY from v_itemavail where warehousekey=? and itemkey=? and cny#=?',
    'ARGTYPES'    => array ('text','text', 'integer'),
);
$kdocumentQueries['QRY_RECURDOCUMENT_ALL_ONHAND_QTY'] = array (
    'QUERY'     => 'select sum(qtyonhand) as QUANTITY from v_itemavail where itemkey=? and cny#=?',
    'ARGTYPES'    => array ('text', 'integer'),
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_TOTALS'] = array (
    'QUERY'     => "select chr('39')||replace(itemkey,chr('39'),'\'||chr('39'))||chr('39')||','||chr('39')||replace(warehousekey,chr('39'),'\'||chr('39'))||chr('39')||','|| (qtyonorder+qtyonhand-qtyonhold) as jsrow from v_itemavail where cny#=?",
    'ARGTYPES'    => array ('integer')
);

$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_CUSTOMER_FOR_EDIT'] = array(
    'QUERY' => "select customerid, customer.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, shipto.name as SHIPTONAME, billto.name as BILLTONAME,customer.termskey, shipmethod.name as shippingmethod from customer, contact displaycontact, contact shipto, contact billto, shipmethod  where customerid = ? and customer.displaycontactkey = displaycontact.record# (+) and customer.shiptokey = shipto.record# (+) and customer.billtokey = billto.record# (+) and customer.shipviakey = shipmethod.record#(+) and customer.cny# = shipmethod.cny#(+) and customer.cny# = ? and displaycontact.cny# (+) = ? and billto.cny# (+) = ? and shipto.cny# (+) = ?",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_CUSTOMER_FOR_EDIT_LIKE_BY_ID'] = array(
    'QUERY' => "SELECT customerid, customer.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, shipto.name as SHIPTONAME, billto.name as BILLTONAME,customer.termskey, shipmethod.name as shippingmethod, customer.creditlimit, customer.totaldue, customer.onhold FROM customer, contact displaycontact, contact shipto, contact billto, shipmethod  where customer.status='T' and customerid like ? and customer.displaycontactkey = displaycontact.record# (+) and customer.shiptokey = shipto.record# (+) and customer.billtokey = billto.record# (+) and customer.shipviakey = shipmethod.record#(+) and customer.cny# = shipmethod.cny#(+) and customer.cny# = ? and displaycontact.cny# (+) = ? and billto.cny# (+) = ? and shipto.cny# (+) = ? order by customerid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_CUSTOMER_CASE_INSENSITIVE_FOR_EDIT_LIKE_BY_ID'] = array(
    'QUERY' => "select customerid, customer.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, shipto.name as SHIPTONAME, billto.name as BILLTONAME,customer.termskey, shipmethod.name as shippingmethod, customer.creditlimit, customer.totaldue, customer.onhold from customer, contact displaycontact, contact shipto, contact billto, shipmethod  where customer.status='T' and lower(customerid) like ? and customer.displaycontactkey = displaycontact.record# (+) and customer.shiptokey = shipto.record# (+) and customer.billtokey = billto.record# (+) and customer.shipviakey = shipmethod.record#(+) and customer.cny# = shipmethod.cny#(+) and customer.cny# = ? and displaycontact.cny# (+) = ? and billto.cny# (+) = ? and shipto.cny# (+) = ? order by customerid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_CUSTOMER_FOR_EDIT_LIKE_BY_NAME'] = array(
    'QUERY' => "select customerid, customer.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, shipto.name as SHIPTONAME, billto.name as BILLTONAME,customer.termskey, shipmethod.name as shippingmethod from customer, contact displaycontact, contact shipto, contact billto, shipmethod  where customer.status='T' and customer.name like ? and customer.displaycontactkey = displaycontact.record# (+) and customer.shiptokey = shipto.record# (+) and customer.billtokey = billto.record# (+) and customer.shipviakey = shipmethod.record#(+) and customer.cny# = shipmethod.cny#(+) and customer.cny# = ? and displaycontact.cny# (+) = ? and billto.cny# (+) = ? and shipto.cny# (+) = ? order by customerid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_CUSTOMER_CASE_INSENSITIVE_FOR_EDIT_LIKE_BY_NAME'] = array(
    'QUERY' => "select customerid, customer.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, shipto.name as SHIPTONAME, billto.name as BILLTONAME,customer.termskey, shipmethod.name as shippingmethod from customer, contact displaycontact, contact shipto, contact billto, shipmethod  where customer.status='T' and lower(customer.name) like ? and customer.displaycontactkey = displaycontact.record# (+) and customer.shiptokey = shipto.record# (+) and customer.billtokey = billto.record# (+) and customer.shipviakey = shipmethod.record#(+) and customer.cny# = shipmethod.cny#(+) and customer.cny# = ? and displaycontact.cny# (+) = ? and billto.cny# (+) = ? and shipto.cny# (+) = ? order by customerid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_VENDOR_FOR_EDIT'] = array(
    'QUERY' => "select vendorid, vendor.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, returnto.name as SHIPTONAME, payto.name as BILLTONAME ,vendor.termskey from vendor, contact displaycontact, contact returnto, contact payto  where vendorid = ? and vendor.displaycontactkey = displaycontact.record# (+) and vendor.returntokey = returnto.record# (+) and vendor.paytokey = payto.record# (+) and vendor.cny# = ? and displaycontact.cny# (+) = ? and payto.cny# (+) = ? and returnto.cny# (+) = ?",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_VENDOR_FOR_EDIT_LIKE_BY_ID'] = array(
    'QUERY' => "select vendorid, vendor.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, returnto.name as SHIPTONAME, payto.name as BILLTONAME ,vendor.termskey, vendor.creditlimit, vendor.totaldue, vendor.onhold from vendor, contact displaycontact, contact returnto, contact payto  where vendor.status='T' and vendorid like ? and vendor.displaycontactkey = displaycontact.record# (+) and vendor.returntokey = returnto.record# (+) and vendor.paytokey = payto.record# (+) and vendor.cny# = ? and displaycontact.cny# (+) = ? and payto.cny# (+) = ? and returnto.cny# (+) = ? order by vendorid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_VENDOR_CASE_INSENSITIVE_FOR_EDIT_LIKE_BY_ID'] = array(
    'QUERY' => "select vendorid, vendor.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, returnto.name as SHIPTONAME, payto.name as BILLTONAME ,vendor.termskey, vendor.creditlimit, vendor.totaldue, vendor.onhold from vendor, contact displaycontact, contact returnto, contact payto  where vendor.status='T' and lower(vendorid) like ? and vendor.displaycontactkey = displaycontact.record# (+) and vendor.returntokey = returnto.record# (+) and vendor.paytokey = payto.record# (+) and vendor.cny# = ? and displaycontact.cny# (+) = ? and payto.cny# (+) = ? and returnto.cny# (+) = ? order by vendorid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_VENDOR_FOR_EDIT_LIKE_BY_NAME'] = array(
    'QUERY' => "select vendorid, vendor.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, returnto.name as SHIPTONAME, payto.name as BILLTONAME ,vendor.termskey from vendor, contact displaycontact, contact returnto, contact payto  where vendor.status='T' and vendor.name like ? and vendor.displaycontactkey = displaycontact.record# (+) and vendor.returntokey = returnto.record# (+) and vendor.paytokey = payto.record# (+) and vendor.cny# = ? and displaycontact.cny# (+) = ? and payto.cny# (+) = ? and returnto.cny# (+) = ? order by vendorid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentQueries['QRY_RECURDOCUMENT_GET_VENDOR_CASE_INSENSITIVE_FOR_EDIT_LIKE_BY_NAME'] = array(
    'QUERY' => "select vendorid, vendor.name, displaycontact.name as DISPLAYCONTACTNAME, displaycontact.visible as VISIBLE, returnto.name as SHIPTONAME, payto.name as BILLTONAME ,vendor.termskey from vendor, contact displaycontact, contact returnto, contact payto  where vendor.status='T' and lower(vendor.name) like ? and vendor.displaycontactkey = displaycontact.record# (+) and vendor.returntokey = returnto.record# (+) and vendor.paytokey = payto.record# (+) and vendor.cny# = ? and displaycontact.cny# (+) = ? and payto.cny# (+) = ? and returnto.cny# (+) = ? order by vendorid",
    'ARGTYPES' => array ('text', 'integer', 'integer', 'integer', 'integer')
);

$krecurdocumentQueries['QRY_AVAIL_NONINV_QTY'] = array (
    'QUERY'     => "SELECT	sum(recurdocentry.quantity) QUANTITY FROM recurdochdr, docpar, recurdocentry, icitem WHERE 	recurdocentry.itemkey = ? and icitem.itemid = recurdocentry.itemkey and recurdocentry.recurdochdrkey = recurdochdr.record# and	docpar.record# = recurdochdr.docparkey and docpar.sale_pur_trans = ? and icitem.cny# = ? and recurdocentry.cny# = ? and docpar.cny# = ? and	recurdochdr.cny# = ? order by recurdochdr.whencreated",
    'ARGTYPES'    => array ('integer','integer', 'integer', 'integer', 'text', 'text', 'text', 'text'),
);

$krecurdocumentQueries['QRY_RECURDOCUMENT_IS_OWNED'] = array(
    'QUERY' => 'select melocationkey from recurdochdr where recurdochdr.record# = ? and recurdochdr.cny#=? ',
    'ARGTYPES' => array ('integer', 'integer')
);


$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_DOCPARID'] = array(
    'QUERY' => 'select docpar.docid from docpar,recurdochdr where recurdochdr.docparkey = docpar.record# and recurdochdr.cny# = ? and docpar.cny# = ?',
    'ARGTYPES' => array ('integer', 'integer')
);

$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_DOCPARID_FOR_THIS_RECURDOC'] = array(
    'QUERY' => 'select docpar.docid from docpar,recurdochdr where recurdochdr.docparkey = docpar.record# and recurdochdr.record# = ? and recurdochdr.cny# = ? and docpar.cny# = ?',
    'ARGTYPES' => array ('integer', 'integer', 'integer')
);

//run when printing OE invoices with convert by line enabled
$krecurdocumentQueries['QRY_RECURDOCUMENT_SELECT_ALL_BASEDOC'] = array(
    'QUERY'    => 'SELECT
                      recurdochdr.record#    recordno,
                      docpar.docid,
                      docentry.lineno + 1    AS lineno,
                      recurdocentry.itemkey,
                      recurdocentry.memo,
                      docentry.uivalue,
                      recurdochdr.schopkey
                  FROM
                      docentry,
                      recurdocentry,
                      recurdochdr,
                      dochdrmst dhchild,
                      docpar
                  WHERE
                          docentry.cny# (+) = ?
                          AND docentry.dochdrkey = ?

                          AND recurdocentry.cny# (+) = ?
                          AND recurdocentry.docentrykey = docentry.record# (+)

                          AND recurdochdr.cny# (+) = ?
                          AND recurdochdr.record# = recurdocentry.recurdochdrkey (+)

                          AND dhchild.cny# (+) = ?
                          AND recurdochdr.schopkey = dhchild.schopkey (+)

                          AND docpar.cny# (+) = ?
                          AND recurdochdr.docparkey = docpar.record# (+)
                  ORDER BY
                      recurdochdr.record#,
                      docentry.lineno',
    'ARGTYPES' => array( 'integer', 'integer', 'integer', 'integer', 'integer', 'integer' ),
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_UPDATE_PARENT_QTY_CONVERTED'] = array(
    'QUERY' => 'update docentrymst set qty_converted = quantity where record# = ? and cny# = ?',
    'ARGTYPES' => array('integer', 'integer'),
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_UPDATE_PARENT_CONVERT_STATUS'] = array(
    'QUERY' => "update dochdrmst set state = 'L' where record# = ? and cny# = ?",
    'ARGTYPES' => array('integer', 'integer'),
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_RESET_PARENT_QTY_CONVERTED'] = array(
    'QUERY' => 'update docentrymst set qty_converted = 0, price_converted = 0, stdpriceconverted = 0, SC_EXISTINGSCHED = null, SC_EXTENDLINEPERIOD= null, SC_CREATERECURSCHED=null, SC_INSTALLPRICING = null where record# = ? and cny# = ?',
    'ARGTYPES' => array('integer', 'integer'),
);

$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_PARENTS_CHILDCOUNT'] = array(
    'QUERY' => "select record# from recurdochdr where dochdrkey = ? and cny# = ?",
    'ARGTYPES' => array('integer', 'integer'),
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_DOCPARKEY'] = array(
    'QUERY' => 'select docpar.record# from docpar where docpar.docid = ? and docpar.cny# = ?',
    'ARGTYPES' => array ('text', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_DOCPAR_RECORDNO'] = array(
    'QUERY' => 'select docpar.docid from docpar,recurdochdr where recurdochdr.docparkey = docpar.record# and recurdochdr.record# = ? and recurdochdr.cny# = ? and docpar.cny# = ?',
    'ARGTYPES' => array ('integer','integer', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_SCHEDULE_STARTDATE'] = array(
    'QUERY' => "select schedule.startdate,schedule.repeatcount from schedule, scheduledoperation where scheduledoperation.cny#= ? and schedule.cny# = ?  and scheduledoperation.schedule# = schedule.record# and scheduledoperation.record# = ?",
    'ARGTYPES' => array ('integer','integer', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_TEMPLATEID'] = array(
    'QUERY' => "select recurdochdr.record# docid from dochdr,recurdochdr where dochdr.schopkey = recurdochdr.schopkey and dochdr.record# = ? and dochdr.cny# = ? and recurdochdr.cny# = ?",
    'ARGTYPES' => array ('integer','integer', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_SALESCONTRACT'] = array(
    'QUERY' => "select dochdr.docid from dochdr,recurdochdr where dochdr.record# = recurdochdr.dochdrkey and recurdochdr.record# = ? and dochdr.cny# = ? and recurdochdr.cny# = ?",
    'ARGTYPES' => array ('integer','integer', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_LINENO_AND_UIVALUE'] = array(
    'QUERY' => "select lineno, uivalue from recurdocentry where recurdochdrkey = ? and cny# = ?",
    'ARGTYPES' => array ('integer', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_CONVERTED_ROWS'] = array(
    'QUERY' => "select lineno, qty_converted from dochdrmst dochdr, docentry where dochdr.record# = docentry.dochdrkey and dochdr.cny# = docentry.cny# and docentry.qty_converted <> 0 and dochdr.docid = ? and dochdr.cny# = ? and usedascontract='Y'",
    'ARGTYPES' => array ('text', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_DOCSTATUS'] = array(
    'QUERY' => "select docstatus from recurdochdr where record# = ? and cny# = ?",
    'ARGTYPES' => array ('integer', 'integer')
);
$krecurdocumentQueries['QRY_RECURDOCUMENT_GET_RECURPARENT_ID'] = array(
    'QUERY' => "select p.record# from dochdr p, dochdr c, recurdochdr rd
where p.cny# = c.cny# and c.cny# = rd.cny#
and c.schopkey = rd.schopkey
and rd.dochdrkey = p.record#
and c.docid = ?
and c.cny# = ?",
    'ARGTYPES' => array('text', 'integer')
    );