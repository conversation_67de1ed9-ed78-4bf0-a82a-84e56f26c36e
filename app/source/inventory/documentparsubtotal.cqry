<?

$kdocumentparsubtotalQueries['QRY_DOCUMENTPARSUBTOTAL_SELECT_TAXABLE_BY_PARENT'] = array(
    'QUERY' => 'SELECT documentparsubtotal.record# FROM docparsubtotal documentparsubtotal WHERE   documentparsubtotal.istax = ? and documentparsubtotal.docparkey = ?  and documentparsubtotal.cny# = ?',
    'ARGTYPES' => array('text', 'integer', 'integer'),
);

$kdocumentparsubtotalQueries['QRY_DOCUMENTPARSUBTOTAL_SELECT_BY_PARENT'] = array(
    'QUERY' => "SELECT documentparsubtotal.record#,documentparsubtotal.docparkey,documentparsubtotal.disc_charge,documentparsubtotal.lineno,documentparsubtotal.description,documentparsubtotal.amount_perc,documentparsubtotal.value,documentparsubtotal.apportioned, decode(nvl(documentparsubtotal.glaccountkey, 0), 0, null, (glaccount.acct_no || '--' || glaccount.title))  acct_no,decode(nvl(documentparsubtotal.gloffsetaccountkey,0), 0, null, (offsetglaccount.acct_no || '--' || offsetglaccount.title)) offsetacct_no,documentparsubtotal.deb_cred,documentparsubtotal.baseline,documentparsubtotal.istax,glaccount.acct_no,offsetglaccount.acct_no,loc.location_no,loc.name,dept.dept_no,dept.title, decode(nvl(documentparsubtotal.deptkey, 0), 0, null, (dept.dept_no || '--' || dept.title)) department, decode(nvl(documentparsubtotal.locationkey, 0), 0, null, (loc.location_no || '--' || loc.name)) location,documentparsubtotal.isavatax,documentparsubtotal.entitykey,entityloc.name FROM docparsubtotal documentparsubtotal,glaccount glaccount,glaccount offsetglaccount,department dept,location loc,locationmst entityloc WHERE (documentparsubtotal.docparkey =  ? ) and documentparsubtotal.glaccountkey = glaccount.record#  (+)   and glaccount.cny# (+) = ?  and documentparsubtotal.gloffsetaccountkey = offsetglaccount.record#  (+)  and offsetglaccount.cny# (+) = ?   and documentparsubtotal.deptkey = dept.record#  (+)   and dept.cny# (+) = ?  and documentparsubtotal.locationkey = loc.record#  (+)   and loc.cny# (+) = ? and documentparsubtotal.entitykey = entityloc.record#  (+)   and entityloc.cny#  (+)  = ? and documentparsubtotal.cny# (+) = ? ORDER BY documentparsubtotal.lineno",
    'ARGTYPES' => array('integer', 'integer', 'integer', 'integer', 'integer')
);

$kdocumentparsubtotalQueries['QRY_DOCUMENTPARSUBTOTAL_SELECT_SUBTOTDESC_ORDERBYLINENO'] = array(
    'QUERY' => "SELECT record#, lineno, description FROM docparsubtotal WHERE docparkey =?  AND cny# =? order by lineno asc",
    'ARGTYPES' => array('integer' ,'integer' ),
);