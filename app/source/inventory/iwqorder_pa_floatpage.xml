<!-- included by inventorywqorder_form.xml -->
<!-- This holds the floating pages -->
<floatingPage move="true" resize="true" close="true" fixedcenter="true" modalsize="extralarge" modal="true">
    <title>IA.PACKING_ORDER_DETAILS</title>
    <id>PACK_DETAILSPAGE</id>
    <path>PACK_DETAILSPAGE</path>
    <pages>
        <page>
            <section path="DETAILSSUMMARY" id="detailsummary" customFields="no" title="IA.DOCID" className="horizontal">
                <field path="ORDERDATE" readonly="true"/>
                <field path="CUSTOMERID" readonly="true"/>
                <field path="SHIPTOCONTACT" readonly="true"/>
                <field path="NUMBEROFLINES" readonly="true" fullname="IA.ORDER_LINES"/>
                <field path="WAREHOUSEID" readonly="true"/>
                <field path="ORDERASSIGNED" readonly="true"/>
                <field path="STATUS" readonly="true"/>
                <field path="ORDERHOLDPROGRESS" readonly="true"/>
            </section>
            <grid noDragDrop="true" className="columns3Grid" hasFixedNumOfRows="true" noNewRows="true" showDelete="false" hideLineNo="true">
                <emptyMessage>IA.LOADING_DETAILS</emptyMessage>
                <enableEmptyMessage>true</enableEmptyMessage>
                <path>PACK_DETAILS</path>
                <gridBulkActions hideInViewMode="true">
                    <button id="bulkassign">
                        <name>IA.ASSIGN_TO</name>
                        <events>
                            <click>bulkAction(this,"ASSIGNTO", false);</click>
                        </events>
                    </button>
                    <button id="bulkprintpack">
                        <name>IA.PRINT_PACK_LIST</name>
                        <events>
                            <click>printAction(this,"PAPRINT", true);</click>
                        </events>
                        <config>pack</config>
                    </button>
                    <button id="bulkpacked">
                        <name>IA.PACKED</name>
                        <events>
                            <click>bulkAction(this,"packed", false);</click>
                        </events>
                        <config>pack</config>
                    </button>
                    <button id="bulkopen">
                        <name>IA.OPEN</name>
                        <events>
                            <click>bulkAction(this,"open", false);</click>
                        </events>
                    </button>
                    <button id="bulkreadytopick">
                        <name>IA.READY_TO_PICK</name>
                        <events>
                            <click>bulkAction(this,"ready to pick", false);</click>
                        </events>
                        <config>pick</config>
                    </button>
                    <button id="bulkpicked">
                        <name>IA.PICKED</name>
                        <events>
                            <click>bulkAction(this,"picked", false);</click>
                        </events>
                        <config>pick</config>
                    </button>
                    <button id="bulkreadytopack">
                        <name>IA.READY_TO_PACK</name>
                        <events>
                            <click>bulkAction(this,"ready to pack", false);</click>
                        </events>
                        <config>pack</config>
                    </button>
                    <button id="bulkreadytosship">
                        <name>IA.READY_TO_SHIP</name>
                        <events>
                            <click>bulkAction(this,"ready to ship", false);</click>
                        </events>
                        <config>ship</config>
                    </button>
                    <button id="bulkreadytoinvoice">
                        <name>IA.READY_TO_INVOICE</name>
                        <events>
                            <click>bulkAction(this,"ready to invoice", false);</click>
                        </events>
                        <config>invoice</config>
                    </button>
                    <button id="bulkhold">
                        <name>IA.HOLD</name>
                        <events>
                            <click>bulkAction(this,"HOLD", false);</click>
                        </events>
                    </button>
                </gridBulkActions>

                <enableSelect>true</enableSelect>
                <selectColumn autoRedraw="true" autoUpdateSelected="true" hideInViewMode="true"></selectColumn>
                <column>
                    <gridHeading className="center" useUIComponent="true" enableGhostText="true">
                        <field noLabel="true" searchPath="LINENO" path="SEARCH_LINENO"></field>
                    </gridHeading>
                    <field sortable="true" path="LINENO"></field>
                </column>
                <column>
                    <gridHeading className="center">
                        <field noLabel="true" searchPath="ITEMID" path="SEARCH_ITEMID"></field>
                    </gridHeading>
                    <field sortable="true" path="ITEMID">
                        <type assoc="T">
                            <type>href</type>
                            <ptype>href</ptype>
                        </type>
                    </field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" >
                        <field noLabel="true" searchPath="SHIPTOCONTACT" path="SEARCH_SHIPTOCONTACT"></field>
                    </gridHeading>
                    <field sortable="true" path="SHIPTOCONTACT"></field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" >
                        <field noLabel="true" searchPath="WHENDUE" path="SEARCH_WHENDUE"></field>
                    </gridHeading>
                    <field sortable="true" path="WHENDUE"></field>
                </column>
                <column>
                    <gridHeading className="center">
                        <field noLabel="true" searchPath="UOM" path="SEARCH_UOM"></field>
                    </gridHeading>
                    <field sortable="true" path="UOM"></field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" enableGhostText="true">
                        <field noLabel="true" searchPath="QUANTITY" path="SEARCH_QUANTITY"></field>
                    </gridHeading>
                    <field sortable="true" path="QUANTITY"></field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" enableGhostText="true">
                        <field noLabel="true" searchPath="QUANTITYONHAND" path="SEARCH_QUANTITYONHAND"></field>
                    </gridHeading>
                    <field sortable="true" path="QUANTITYONHAND"></field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" enableGhostText="true">
                        <field noLabel="true" searchPath="QUANTITYALLOCATED" path="SEARCH_QUANTITYALLOCATED"></field>
                    </gridHeading>
                    <field sortable="true" path="QUANTITYALLOCATED"></field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" enableGhostText="true">
                        <field noLabel="true" searchPath="QUANTITYTOPACK" path="SEARCH_QUANTITYTOPACK"></field>
                    </gridHeading>
                    <field sortable="true" path="QUANTITYTOPACK"></field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" enableGhostText="true">
                        <field noLabel="true" searchPath="QUANTITYPACKED" path="SEARCH_QUANTITYPACKED"></field>
                    </gridHeading>
                    <field sortable="true" path="QUANTITYPACKED"><events><change>onDetailGridQtyPickedPackedChange(this);</change></events></field>
                </column>
                <column supportsIcons="true" clazz="FulfillmentGridColumn">
                    <supportedIcons>
                        <icon value="IA.FULL" title="IA.FULL" color="#418772"></icon>
                        <icon value="IA.PART_PERCENTAGE" title="IA.PART_PERCENTAGE"  color="#84B371"></icon>
                        <icon value="IA.NONE" title="IA.NONE" color="#E35C63"></icon>
                    </supportedIcons>
                    <field sortable="true"  fullname="IA.FULFILLABLE"  userUIControl="FulfillmentInputControlText">
                        <path>PCTFULFILLABLE</path>
                    </field>
                </column>
                <column>
                    <field sortable="true" path="SLBLINK"><events><click>onSLBLinkClick(this);</click></events></field>
                </column>
                <column>
                    <gridHeading className="center">
                        <field noLabel="true" searchPath="ASSIGNED" path="SEARCH_ASSIGNED"></field>
                    </gridHeading>
                    <field sortable="true" path="ASSIGNED"></field>
                </column>
                <column>
                    <gridHeading className="center">
                        <field noLabel="true" searchPath="HOLDPROGRESS" path="SEARCH_HOLDPROGRESS"></field>
                    </gridHeading>
                    <field sortable="true" path="HOLDPROGRESS"><events><change>onHoldChangeHandler(this);</change></events></field>
                </column>
                <column>
                    <gridHeading className="center" useUIComponent="true" >
                        <field noLabel="true" searchPath="STATUS" path="SEARCH_STATUS"></field>
                    </gridHeading>
                    <field sortable="true" path="STATUS"><events><change>onStatusChangeHandler(this);</change></events></field>
                </column>
                <lineDetails clazz="OrderLineDetails">
                    <pages>
                        <page title="">
                            <section title="IA.DETAILS" columnCount="3">
                                <field>NOTES</field>
                                <field>PICKLISTPRINTED</field>
                                <field>PICKCONTAINER</field>
                                <field>PACKLISTPRINTED</field>
                                <field>PACKCONTAINER</field>
                            </section>
                            <section title="IA.ORDER_INFORMATION" columnCount="3">
                                <field>ORDERNOTES</field>
                                <field>DONOTSHIPBEFOREDATE</field>
                                <field>SHIPPEDDATE</field>
                                <field>MEMO</field>
                                <field>DONOTSHIPAFTERDATE</field>
                                <field>NEEDBYDATE</field>
                                <field>CANCELAFTERDATE</field>
                                <field>PCTFULFILLABLE</field>
                            </section>
                            <section id="TrackingSection" customFields="no">
                                <title>IA.SERIAL_LOT_BIN</title>
                                <subsection columnCount="3" className="columns3" readonly="true">
                                    <field fullname="IA.ORDER_LINE_NO">LINENO</field>
                                    <field fullname="IA.QUANTITY_FROM_ORDER_ENTRY_LINE">QUANTITY</field>
                                </subsection>
                                <grid clazz="FulfillmentTrackingGrid" noDragDrop="true" customFields="no" allowEditPage="false" title="">
                                    <path>TRACKING</path>
                                    <column>
                                        <field path="SERIALNO"><events><change>onSerialNoChange(this);</change></events></field>
                                    </column>
                                    <column>
                                        <field path="LOTNO"><events><change>onLotNoChange(this);</change></events></field>
                                    </column>
                                    <column>
                                        <field path="BINID"><events><change>onBinChange(this);</change></events></field>
                                    </column>
                                    <column>
                                        <field path="EXPIRATION"><events><change>onExpirationChange(this);</change></events></field>
                                    </column>
                                    <column>
                                        <field path="QUANTITYAVAIL"></field>
                                    </column>
                                    <column>
                                        <field hasTotal="true" readonly="true" path="QUANTITYPACKED"><events><change>onTrackingGridQtyPickedPackedChange(this);</change></events></field>
                                    </column>
                                </grid>
                            </section>
                        </page>
                    </pages>
                </lineDetails>
            </grid>
        </page>
    </pages>
    <footer>
        <button>
            <name>IA.SAVE</name>
            <events>
                <click>onSaveBtnHandler('PACK');</click>
            </events>
        </button>
        <button>
            <name>IA.CANCEL</name>
            <events>
                <click>window.editor.hidePage('PACK_DETAILSPAGE', false);</click>
            </events>
        </button>
    </footer>
</floatingPage>
