<?
/**
 * Abstract superclass for email and document templates.
 * Table: PT_TEMPLATE
 */
require_once 'Pt_Includes.inc';

abstract class Pt_AbstractTemplate extends Pt_DataEntity
    implements Pt_IPickItem, Pt_IDefinition
{

    /* @var string $origId */
    protected $origId;
    /* @var int $objDefId */
    protected $objDefId;
    /* @var string $name */
    protected $name;
    /* @var string $contentType */
    protected $contentType;
    /* @var bool $isPrivate */
    protected $isPrivate;
    /* @var string $systemName */
    protected $systemName;

    /**
     * @param int      $id
     * @param string   $origId
     * @param int      $objDefId
     * @param string   $name
     * @param string   $contentType
     * @param bool     $isPrivate
     * @param string   $systemName
     * @param int      $createdBy
     * @param string   $createdAt
     * @param int      $updatedBy
     * @param string   $updatedAt
     * @param string[] $appOriginalIds
     */
    public function __construct($id, $origId, $objDefId, $name, $contentType, $isPrivate,
        $systemName, $createdBy, $createdAt, $updatedBy, $updatedAt, $appOriginalIds
    ) {
        parent::__construct($id, $createdBy, $createdAt, $updatedBy, $updatedAt, $appOriginalIds);
        $this->origId = $origId;
        $this->objDefId = $objDefId;
        $this->name = $name;
        $this->contentType = $contentType;
        $this->isPrivate = $isPrivate;
        $this->systemName = $systemName;
    }

    /**
     * Update data.
     *
     * @param string $name
     * @param string $contentType
     * @param bool   $isPrivate
     * @param int    $updatedBy
     * @param string $updatedAt
     * @param string $appOrigId
     */
    public function updateAbstractTemplate($name, $contentType, $isPrivate, $updatedBy, $updatedAt, $appOrigId)
    {
        parent::updateDataEntity($updatedBy, $updatedAt);
        $this->name = $name;
        $this->contentType = $contentType;
        $this->isPrivate = $isPrivate;
        $this->addAppOriginalId($appOrigId);
    }

    /**
     * Get original id of this item.
     *
     * @return string
     */
    public function getOriginalId() 
    {
        return isset($this->origId) && strpos($this->origId, '@') > 0
            ? $this->origId
            : GetMyCompany() . '@' . $this->id;
    }

    /**
     * Is private template?
     *
     * @return bool
     */
    public function isPrivate() 
    {
        return $this->isPrivate && !$this->isSystem();
    }

    /**
     * Get MIME type of template
     *
     * @return string
     */
    public function getContentType() 
    {
        return ($this->contentType==null ? MIME_TEXT : $this->contentType);
    }

    /**
     * VALUE attribute for UI SELECT list.
     *
     * @return string
     */
    public function getSelectValue() 
    {
        return strval($this->id);
    }

    /**
     * Get object definition's id
     *
     * @return int
     */
    public function getObjectDefId() 
    {
        return $this->objDefId;
    }

    /**
     * Get object definition
     *
     * @return Pt_DataObjectDef
     */
    public function getObjectDef() 
    {
        return Pt_DataObjectDefManager::getById($this->objDefId);
    }

    /**
     * String representation
     *
     * @return string
     */
    public function __toString() 
    {
        return strval($this->name);
    }

    /**
     * Get template name
     *
     * @return string the template's visible name
     */
    public function getName() 
    {
        return $this->name;
    }

    /**
     * Get template system name
     *
     * @return string
     */
    public function getSystemName() 
    {
        return $this->systemName;
    }

    /**
     * Is system template?
     *
     * @return bool
     */
    public function isSystem() 
    {
        return isset($this->systemName) && strlen($this->systemName) > 0;
    }

    /**
     * Validate required input parameters.
     *
     * @param string $name
     */
    public static function validate($name) 
    {
        if (strlen($name) == 0) {
            throw new Pt_I18nException('PAAS-0504', "Display name is null or empty");
        }
    }

    /**
     * Compares two templates by name.
     *
     * @param Pt_AbstractTemplate $t1
     * @param Pt_AbstractTemplate $t2
     *
     * @return int
     */
    public static function compareByName($t1, $t2) 
    {
        return isl_strcasecmp($t1->__toString(), $t2->__toString());
    }

    /**
     * Returns true if the body should be considered a full html document.
     * Only currently applicable to email templates.
     *
     * @return bool if true then the body is a full html document
     */
    public function isRawHtml() 
    {
        return false;
    }

    /**
     * Append properties as XML to Packet
     *
     * @param Pt_PacketProxy $proxy
     * @param string         $type
     */
    public function addPropertiesToPacket(Pt_PacketProxy $proxy, string $type)
    {
        xml_appendAttribute($proxy->buff, "type", $type);
    }
}
