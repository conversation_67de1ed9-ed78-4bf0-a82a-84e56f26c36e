<?
/**
 * Document template.
 * Table: PT_TEMPLATE
 */
require_once "Pt_AbstractTemplate.cls";

class Pt_DocTemplate extends Pt_AbstractTemplate
{
    const TYPE = 'doc';
    
    /* @var string $fileName */
    private $fileName;

    /* @var int $templateType */
    private $templateType;

    /**
     * @param int      $id
     * @param string   $origId
     * @param int      $objDefId
     * @param string   $name
     * @param string   $fileName
     * @param string   $contentType
     * @param int      $templateType
     * @param bool     $isPrivate
     * @param string   $systemName
     * @param int      $createdBy
     * @param string   $createdAt
     * @param int      $updatedBy
     * @param string   $updatedAt
     * @param string[] $appOriginalIds
     */
    public function __construct($id, $origId, $objDefId, $name, $fileName, $contentType, $templateType, $isPrivate,
                                $systemName, $createdBy, $createdAt, $updatedBy, $updatedAt, $appOriginalIds
    )
    {
        parent::__construct(
            $id, $origId, $objDefId, $name, $contentType, $isPrivate,
            $systemName, $createdBy, $createdAt, $updatedBy, $updatedAt, $appOriginalIds
        );
        $this->fileName = $fileName;
        $this->templateType = $templateType;
    }

    /**
     * Update data.
     *
     * @param string $name
     * @param string $fileName
     * @param string $contentType
     * @param int    $templateType
     * @param bool   $isPrivate
     * @param int    $updatedBy
     * @param string $updatedAt
     * @param string $appOrigId
     */
    public function updateDocTemplate($name, $fileName, $contentType, $templateType, $isPrivate, $updatedBy,
                                      $updatedAt, $appOrigId)
    {
        parent::updateAbstractTemplate($name, $contentType, $isPrivate, $updatedBy, $updatedAt, $appOrigId);
        $this->fileName = $fileName;
        $this->templateType = $templateType;
    }

    /**
     * Get template file name
     *
     * @return string
     */
    public function getFileName() 
    {
        return $this->fileName;
    }

    /**
     * @return string
     */
    public function getTemplateType()
    {
        return $this->templateType ?? TEMPLATE_TYPE_GENERIC;
    }

    /**
     * Is XML template?
     *
     * @return bool
     */
    public function isXML() 
    {
        return endsWith($this->fileName, EXT_XML) || $this->getContentType()=='text/xml';
    }

    /**
     * Get binary template data
     *
     * @return string
     */
    public function getTemplateData() 
    {
        return Pt_StorageProxy::get(strval($this->id)) ?? "";
    }

    /**
     * Generate XML and append it to Packet
     *
     * @param Pt_PacketProxy $proxy
     */
    public function toPacket(Pt_PacketProxy $proxy)
    {
        $proxy->putId($this->id);
        $proxy->buff .= "<Template";
        $this->addPropertiesToPacket($proxy, self::TYPE);
        $proxy->buff .= " >\n";
        xml_appendElement($proxy->buff, "RawData", base64_encode($this->getTemplateData()));
        $proxy->buff .= "</Template>\n";
    }
    
    /**
     * Append properties as XML to Packet
     *
     * @param Pt_PacketProxy $proxy
     * @param string         $type
     */
    public function addPropertiesToPacket(Pt_PacketProxy $proxy, string $type)
    {
        parent::addPropertiesToPacket($proxy, $type);
        xml_appendAttribute($proxy->buff, "id", $this->id);
        xml_appendAttribute($proxy->buff, "origId", $this->getOriginalId());
        xml_appendAttribute($proxy->buff, "objDef", $this->getObjectDefId());
        xml_appendAttribute($proxy->buff, "name", $this->name);
        xml_appendAttribute($proxy->buff, "contentType", $this->contentType);
        xml_appendAttribute($proxy->buff, "systemName", $this->systemName);
        xml_appendAttribute($proxy->buff, "fileName", $this->fileName);
        xml_appendAttribute($proxy->buff, "templateType", $this->getTemplateType());
    }

}
