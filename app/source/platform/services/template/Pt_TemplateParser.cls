<?
/**
 * Parses text template, replaces tokens with real data.
 */
require_once 'Pt_Includes.inc';

require_once "Pt_TemplateConstants.inc";
require_once "Pt_DataObjectDef.cls";
require_once "Pt_DataObject.cls";
require_once "Pt_PickItemManager.cls";
require_once "Pt_StatusManager.cls";
require_once "Pt_PageParams.cls";
require_once "Pt_IntacctRESTAPIConfig.cls";

class Pt_TemplateParser
{
    /* @var int $encoding */
    protected $encoding;
    /* @var Pt_DataObject $data */
    protected $data;
    /* @var Pt_DataObject $currentRecord */
    protected $currentRecord;
    /* @var Pt_DataObjectDef $objDef */
    protected $objDef;
    /* @var Pt_MailContent $mc */
    protected $mc;
    /* @var bool $loopsEnabled */
    protected $loopsEnabled = true;
    /* @var bool $evalsEnabled */
    protected $evalsEnabled = true;
    /* @var Pt_DataObjectDef[] $map */
    protected $map;
    /* @var int $recursionLevel */
    protected $recursionLevel;
    /* @var Pt_PageParams $params */
    protected $params;
    /* @var Pt_Portal $portal */
    protected $portal;
    /* @var  Pt_DataObject $portalVisitor */
    protected $portalVisitor;
    /* @var array $oldData */
    protected $oldData;
    /* @var Pt_DataObject $origData */
    protected $origData;
    /* @var array $textMap */
    protected $textMap = [];
    /* @var string $context */
    protected $context;
    /* @var string $activation */
    protected $activation;

    /**
     * @param int                            $encoding
     * @param Pt_DataObject|Pt_DataObjectDef $obj
     */
    public function __construct($encoding, $obj) 
    {
        $tokens = ["IA.LOGOUT", "IA.ERROR"];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));

        if ($obj instanceof Pt_DataObject) {
            $this->data = $obj;
            $this->objDef = $obj->getObjectDef();
        } else if ($obj instanceof Pt_DataObjectDef) {
            $this->data = null;
            $this->objDef = $obj;
        }

        if ($encoding < PLAIN_TEXT || $encoding > JSON_TEXT) {
            throw new Pt_I18nException('PAAS-0523', "Illegal encoding $encoding", [ 'ENCODING' => $encoding ]);
        }
        $this->encoding = $encoding;
        $this->currentRecord = $this->data; // This will not change during parsing. Always available through CURR_REC prefix.
        $this->recursionLevel = 1;
    }

    /**
     * Get Context variable.
     *
     * @return string
     */
    public function getContext() 
    {
        return $this->context;
    }

    /**
     * Set Context variable.
     *
     * @param string $value
     */
    public function setContext($value) 
    {
        $this->context = $value;
    }

    /**
     * Get Activation variable.
     *
     * @return string
     */
    public function getActivation() 
    {
        return $this->activation;
    }

    /**
     * Set Activation variable.
     *
     * @param string $value
     */
    public function setActivation($value) 
    {
        $this->activation = $value;
    }

    /**
     * Set object definition
     *
     * @param Pt_DataObjectDef $objDef
     */
    public function setObjectDef($objDef)
    {
        if ( $this->data != null ) {
            $oldObjDef = $this->data->getObjectDef();
            if ( $oldObjDef == null || $oldObjDef->getId() != $objDef->getId() ) {
                $this->data = null;
            }
        }
        $this->objDef = $objDef;
    }

    /**
     * Get MailContent instance.
     *
     * @return Pt_MailContent
     */
    public function getMailContent() 
    {
        return $this->mc;
    }

    /**
     * Set MailContent object
     *
     * @param Pt_MailContent $mc;
     */
    public function setMailContent($mc) 
    {
        $this->mc = $mc;
    }

    /**
     * Get Data object before update operation - to be used for tokens with #before suffix
     *
     * @return array
     */
    public function getOldData() 
    {
        return $this->oldData;
    }

    /**
     * Set values map before update operation
     *
     * @param array $oldData
     */
    public function setOldData($oldData) 
    {
        $this->oldData = $oldData;
    }

    /**
     * Get portal
     *
     * @return Pt_Portal
     */
    public function getPortal() 
    {
        return $this->portal;
    }

    /**
     * Get portal visitor
     *
     * @return Pt_DataObject
     */
    public function getPortalVisitor() 
    {
        return $this->portalVisitor;
    }

    /**
     * Set PageParams
     *
     * @param Pt_PageParams $params
     */
    public function setPageParams(Pt_PageParams $params) 
    {
        $this->params = $params;
        if ($params->isPortal()) {
            $this->portal = $params->getPortal();
            $this->portalVisitor = $params->getPortalVisitor();
        }
    }

    /**
     * Get "loops enabled" flag
     *
     * @return bool
     */
    public function getLoopsEnabled() 
    {
        return $this->loopsEnabled;
    }

    /**
     * Set "loops enabled" flag
     *
     * @param bool $flag
     */
    public function setLoopsEnabled($flag) 
    {
        $this->loopsEnabled = pt_boolval($flag);
    }

    /**
     * Get template encoding
     *
     * @return int
     */
    public function getEncoding() 
    {
        return $this->encoding;
    }

    /**
     * Set template encoding
     *
     * @param int $value
     */
    public function setEncoding($value) 
    {
        $this->encoding = intval($value);
    }

    /**
     * Get Pt_DataObject
     *
     * @return Pt_DataObject
     */
    public function getDataObject() 
    {
        return $this->data;
    }

    /**
     * Set Pt_DataObject
     *
     * @param Pt_DataObject $data
     */
    public function setDataObject($data) 
    {
        $this->data = $data;
        $this->map = null;
    }

    /**
     * Set level of recursion
     *
     * @param int $level
     */
    public function setRecursionLevel($level) 
    {
        if ($level > MAX_REQURSION_LEVEL) {
            throw new Pt_I18nException('PAAS-0524', "Recursion level cannot exceed " . MAX_REQURSION_LEVEL,
                                       [ 'MAXLEVEL' => MAX_REQURSION_LEVEL ]);
        }
        $this->recursionLevel = $level;
    }

    /**
     * Get level of recursion
     *
     * @return int
     */
    public function getRecursionLevel() 
    {
        return $this->recursionLevel;
    }

    /**
     * Replaces tokens in template by values using specified encoding.
     * Returns resulting text.
     *
     * @param string $template
     * @param string $extraInfo
     *
     * @return string
     */
    public function parse($template, $extraInfo='')
    {
        if ($template == null) {
            return null;
        }

        $replaceCache = [];

        $buff = $template;
        $pos = 0;
        while (true) {
            $index1 = isl_strpos($buff, TOKEN_BEGIN, $pos);
            if ($index1===false) {
                break;
            }
            $index2 = isl_strpos($buff, TOKEN_END, $index1+LENGTH_BEGIN);
            if ($index2===false) {
                break;
            }

            $key = isl_substr($buff, $index1+LENGTH_BEGIN, $index2-$index1-LENGTH_BEGIN);
            if (strpos($key, TOKEN_BEGIN)) {
                $pos = $index1+LENGTH_BEGIN;
                continue;
            }

            if ($this->loopsEnabled && strpos($key, LOOP_BEGIN)===0) {
                $pos = $this->processLoop($buff, $key, $index1, $extraInfo);
                continue;
            }

            if ( ! array_key_exists($key, $replaceCache) ) {
                $replace = $this->getReplace($key, $extraInfo);
                $replaceCache[$key] = $replace;
            } else {
                $replace = $replaceCache[$key];
            }

            if ($replace == null) {
                $buff = isl_substr_replace($buff, '', $index1, $index2+LENGTH_END-$index1);
                $pos = $index1;
            } else {
                $sReplace = strval($replace);
                $buff = isl_substr_replace($buff, $sReplace, $index1, $index2+LENGTH_END-$index1);
                $pos = $index1 + isl_strlen($sReplace);
            }
        }

        if ($this->evalsEnabled) {
            $pos = 0;
            while (true) {
                $index1 = isl_strpos($buff, EVAL_BEGIN, $pos);
                if ($index1===false) {
                    break;
                }
                $index2 = isl_strpos($buff, EVAL_END, $index1+LENGTH_EVAL_BEGIN);
                if ($index2===false) {
                    break;
                }

                $key = isl_substr($buff, $index1+LENGTH_EVAL_BEGIN, $index2-$index1-LENGTH_EVAL_BEGIN);
                $pos = $this->processEval($buff, $key, $index1, $extraInfo);
            }
        }

        return $buff;
    }

    /**
     * Return Object, if the whole template represents a single token.
     * Otherwise parse as String.
     *
     * @param string $template
     * @param string $extraInfo
     *
     * @return mixed
     */
    public function parseValue($template, $extraInfo='')
    {
        if ($template == null) {
            return null; 
        }
        $template = isl_trim($template);
        if (isl_substr($template, 0, LENGTH_BEGIN)==TOKEN_BEGIN && isl_substr($template, -2)) {
            $key = isl_substr($template, LENGTH_BEGIN, isl_strlen($template)-LENGTH_BEGIN-LENGTH_END);
            if (strpos($key, TOKEN_BEGIN)===false && strpos($key, TOKEN_END)===false && isl_strlen($key)<50) {
                return $this->getReplace($key, $extraInfo);
            }
        }
        return $this->parse($template, $extraInfo);
    }

    /**
     * Get value to replace token.
     *
     * @param string $key
     * @param string $extraInfo
     *
     * @return mixed  some value that can be used as a string
     */
    protected function getReplace($key, $extraInfo)
    {
        try {
            if (ACTIVATION == $key) {
                return $this->activation;
            } else if (CONTEXT == $key) {
                return $this->context;
            }

            $prefix = '';       // First part of complex name: object def. name or relationship name
            $tokenName = $key;  // Second part of complex name: field's name
            $suffix = '';   // Third part of complex name: extra info on how to render field
            $relType = REL_HETERO;  // Relationship type: distingushes parent and child relationships

            $index = isl_strpos($key, '.');
            if ($index !== false) {
                $prefix = isl_substr($key, 0, $index);
                $tokenName = (string)isl_substr($key, $index + 1);
            }

            $index = isl_strpos($tokenName, '#');
            if ($index !== false) {
                $suffix = isl_substr($tokenName, $index);
                $tokenName = (string)isl_substr($tokenName, 0, $index);
            }
            //eppp_p("getReplace: prefix=$prefix tokenName=$tokenName suffix=$suffix");

            $index = isl_strpos($prefix, '#');
            if ($index !== false) {
                $prefix2 = isl_substr($prefix, $index);
                if ($prefix2 == PARENT_SUFFIX) {
                    $relType = REL_PARENT;
                    $prefix = isl_substr($prefix, 0, $index);
                } else if ($prefix2 == CHILD_SUFFIX) {
                    $relType = REL_CHILD;
                    $prefix = isl_substr($prefix, 0, $index);
                }
            }
            $defValue = ($suffix == ID_SUFFIX || $tokenName == FIELD_ID ? "0" : null);

            if ($prefix == LOOP_BEGIN || $prefix == LOOP_END) {
                if (!$this->loopsEnabled) {
                    return '';
                } else {
                    throw new Pt_I18nException('PAAS-0525', "Unexpected loop found");
                }
            } else if ($prefix == TOKEN_LINK) {
                return $this->getPageLink($tokenName, $suffix, $relType);
            } else if ($prefix == PORTAL) {
                return $this->getPortalPage($tokenName, $suffix);
            } else if ($prefix == OBJ_ID) {
                $objDef = ($tokenName != null ? $this->getObjectDef($tokenName) : $this->objDef);
                return ($objDef == null ? null : $objDef->getId());
            } else if ($prefix == PAGEID) {   // Bug 39463
                $page = Pt_WebPageManager::getByOriginalId($tokenName);
                return ($page == null ? null : $page->getId());
            } else if ($suffix == CURR_SESSION) {
                return Session::getKey();
            } else if ($tokenName == API_ENDPOINT) {
                return $this->encode(APIEndPoint());
            } else if ($tokenName == REST_API_ENDPOINT) {
                return FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('PT_ENABLE_REST_API_TRIGGER') ?
                    $this->encode(RESTAPIEndPoint()) : '';
            } else if (startsWith($prefix,REST_API_ACCESS_TOKEN)) {
                return FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('PT_ENABLE_REST_API_TRIGGER') ?
                    $this->getAccessToken($prefix . '.' . $tokenName) : '{}'; // needs "" wrapped not to produce empty result
            } else if ( $tokenName == ACCT_HOST) {
                return ServerUrl();
            } else if ( $tokenName == ACCT_ENDPOINT) {
                return AcctUrl();
            } else if ($tokenName == CURRENT_ENTITY) {
                if ($suffix == ID_SUFFIX) {
                    return GetContextLocation(true);
                }
                return GetContextLocationName();
            } else if ($prefix == PROFILE) {
                return Profile::getProperty($tokenName);
            } else if ($prefix == SESSION) {
                return Session::getProperty($tokenName);
            } else if ($suffix == TOKEN_TODAY) {
                return util_today();
            } else if ($prefix == HOSTED_FILE) {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $hf = null;
                if (strpos($tokenName, '@') > 0) {  // T 4822
                    $hf = Pt_HostedFileManager::getByOriginalId($tokenName);
                } else {
                    $hf = Pt_HostedFileManager::getById(intval($tokenName));
                }
                return ($hf == null ? null : $hf->getURL($this->params, true));
            } else if ($prefix == CURRENT_COMPANY) {
                if (Profile::hasCompanyCacheProperty('company', $tokenName)) {
                    return $this->encode(Profile::getCompanyCacheProperty('company', $tokenName));
                } else {
                    $localPlaceholderTokens = [
                        [
                            'id'           => "IA.ERROR.ERROR_TOKENNAME_COMPANY_PROPERTY_DOES_NOT:$tokenName",
                            'placeHolders' => [
                                [ 'name' => 'TOKEN_NAME', 'value' => $tokenName ],
                            ],
                        ],
                    ];
                    $txtMap = getIntlTextMap([], $localPlaceholderTokens);
                    return GT($txtMap, "IA.ERROR.ERROR_TOKENNAME_COMPANY_PROPERTY_DOES_NOT:$tokenName");
                }
            } else if ($prefix == CURRENT_USER) {
                if ($tokenName == 'username') {
                    global $gManagerFactory;
                    $objUser = $gManagerFactory->getManager('userinfo');
                    return $objUser->GetContactName(GetMyUserid());
                } else if ($tokenName == 'id') {
                    return GetMyUserid();
                } else if ($tokenName == 'email') { // T 3398
                    return Pt_UserProxy::getEmailAddress(GetMyUserid());
                } else if (Profile::hasProperty($tokenName)) {
                    return Profile::getProperty($tokenName);
                } else {
                    $localPlaceholderTokens = [
                        [
                            'id'           => "IA.ERROR.ERROR_TOKENNAME_PROPERTY_DOES_NOT_EXIST:$tokenName",
                            'placeHolders' => [
                                [ 'name' => 'TOKEN_NAME', 'value' => $tokenName ],
                            ],
                        ],
                    ];
                    $txtMap = getIntlTextMap([], $localPlaceholderTokens);
                    return GT($txtMap, "IA.ERROR.ERROR_TOKENNAME_PROPERTY_DOES_NOT_EXIST:$tokenName");
                }
            } else if ($prefix == CURRENT_VISITOR) {  // Bug 36437
                if ($this->portalVisitor != null) {
                    return $this->portalVisitor->getFieldValue($tokenName);
                }
            }

            $objDef = $this->getObjectDef($prefix);
            if ($objDef == null) {
                return $defValue;
            }

            $field = $objDef->getByName($tokenName);
            if ($field == null) {
                return $defValue;
            }

            $uiField = $field->getUIField();
            if ($uiField == null) {
                return $defValue;
            }
            $defValue = ($uiField->isNumericField() ? "0" : $defValue);
            if ($uiField instanceof Pt_CheckBox && VALUE_SUFFIX == $suffix) { // Bug 35426
                $defValue = 'false';
            }

            if ($suffix == BEFORE_SUFFIX) {
                if ($this->oldData == null) {
                    return $defValue;
                }
                $obj = $this->oldData[$tokenName];
                if ($uiField instanceof Pt_DateInput) {   // T 8869
                    return $uiField->toText($obj, null);
                }
                return ($obj == null ? $defValue : $obj);
            } else if ($suffix == BEFORE_CODE_SUFFIX) {    // Bug 41030
                if ($this->oldData == null || !isset($this->oldData[$tokenName])) {
                    return null;
                }
                /* @var mixed $value */
                $value = $this->oldData[$tokenName];
                if ($uiField instanceof Pt_StatusSelect) {
                    $s = Pt_StatusManager::getById(intval($value));
                    return $this->encode($s == null ? null : $s->getSystemName());
                } else if ($uiField instanceof Pt_ProcessSelect) {     // Bug 40155
                    $p = Pt_ProcessManager::getById(intval($value));
                    return $this->encode($p == null ? null : $p->getSystemName());
                } else if ($uiField instanceof Pt_SelectList) {
                    if ($uiField instanceof Pt_SelectListMultiple) {
                        if (!is_array($value)) {
                            return null;
                        }
                        $items = Pt_PickItemManager::getBySource($uiField->getSource());
                        $buff = '';
                        foreach ($items as $item) {
                            if (!in_array($item->getId(), $value)) {
                                continue;
                            }
                            if (strlen($buff) > 0) {
                                $buff .= ', ';
                            }
                            $buff .= $item->getCode();
                        }
                        return $buff;
                    } else {
                        $id = intval($value);
                        $item = Pt_PickItemManager::getById($id);
                        return ($item == null ? null : $item->getCode());
                    }
                }
            }

            if ($uiField instanceof Pt_SharedImage) {
                return $this->getSharedImage($uiField, $suffix);
            }

            $data = $this->getObject($objDef, $prefix, $relType);
            if ($data == null) {
                return $defValue;
            }

            // #17381: Template fields will completely re-parse themselves inside of toHTML().  This,
            // unfortunately, is the necessary behavior because that's the only place where the full
            // page parameters are available to it.  uiField->getValue() will also parse and evaluate
            // the template in a more limited context but the returned value is useless if we're going
            // to re-parse later in the more correct context.  So here we try our best to not execute
            // the first parse.
            if (!($uiField instanceof Pt_TemplateField && $suffix == '' && $this->encoding == HTML_TEXT)) {
                $value = $uiField->getValue($data, $this->recursionLevel + 1);
                if ($value == null) {
                    return $defValue;
                }
            } else {
                $value = null;
            }

/*
 This code would never execute because Pt_CalendarAttachment fields never have any data so the field's value would
 be null and the return $defValue above will return.  Even if you get past that, there is no getICalAttachment or
 getVCardAttachment functions on Pt_ICalUtil.  To implement those the way that this code is structured would be
 problematic.  addAttachment takes a name which is assumed to be the index into PHP's $_FILES array.  IF I was
 going to make this work I'd create a new API or enhance addAttachment so that the get*Attachment function could
 create a fake Pt_FileUpload without needing to create a temporary file and add it to $_FILES[].
            if ($this->mc != null && $uiField instanceof Pt_CalendarAttachment) {
                if ($objDef->isEvent()) {
                    $this->mc->addAttachment(Pt_ICalUtil::getICalAttachment($data));
                    return "iCal";
                } else if ($objDef->isTask()) {
                    $this->mc->addAttachment(Pt_ICalUtil::getVCardAttachment($data));
                    return "vCard";
                } else {
                    return null;
                }
            }
*/

            if ($value instanceof Pt_FileUpload) {
                if (URL_SUFFIX == $suffix && ($uiField instanceof Pt_ImageInput)) {
                    return $uiField->getFileURL($data);
                } else if (URL_SUFFIX == $suffix && ($uiField instanceof Pt_FileInput)) {
                    return $uiField->getFileURL($data);
                } else if ($this->mc != null && $field instanceof Pt_FieldFile) {
                    $attName = "attachment" . $this->mc->getAttachmentsCount();
                    /* @var Pt_FileInput $uiField */
                    $uiField = $field->getUIField();
                    $value->setParentId($uiField->getParentId($data->getId()));
                    $this->mc->addAttachment($attName, $value);
                    return $value->getOriginalName();
                }
            }

            if (NO_ENCODING == $suffix) {
                return $value;
            } else if (ID_SUFFIX == $suffix) {
                if ( $uiField instanceof Pt_Relationship ) {
                    return util_toInt($value, (int) $defValue);
                } else if ( $uiField instanceof Pt_SelectListMultiple && $this->encoding == JAVA_SCRIPT ) {
                    return util_converge($value);
                }
                return $value;
            } else if (LINK_SUFFIX == $suffix && $value instanceof Pt_DataObject) {
                if ($this->portal != null) {    // Bug 40488
                    $url = Pt_WebUtil::getPortalURL($this->portal, $value->getObjectDefId(), TYPE_VIEW) . "&id=" . $value->getId();
                    return "<a href='$url'>$value</a>";
                } else {
                    return Pt_WebUtil::getViewLink($value);
                }
            } else if (POPUP_SUFFIX == $suffix && $value instanceof Pt_DataObject) {
                $buff = '';
                $pageId = Pt_WebUtil::getPageId($value->getObjectDefId(), TYPE_VIEW);
                Pt_WebUtil::appendObjectPopup($buff, $pageId, $value);
                return $buff;
            } else if (URL_SUFFIX == $suffix && $value instanceof Pt_DataObject) {
                if ($this->portal != null) {
                    return Pt_WebUtil::getPortalURL($this->portal, $value->getObjectDefId(), TYPE_VIEW) . "&id=" . $value->getId();
                } else {
                    return Pt_WebUtil::getPageURL($value->getObjectDefId(), TYPE_VIEW) . "&id=" . $value->getId();
                }
            } else if (CODE_SUFFIX == $suffix) {
                if ($uiField instanceof Pt_StatusSelect) {
                    $s = Pt_StatusManager::getById(intval($value));
                    return $this->encode($s == null ? null : $s->getSystemName());
                } else if ($uiField instanceof Pt_ProcessSelect) {     // Bug 40155
                    $p = Pt_ProcessManager::getById(intval($value));
                    return $this->encode($p == null ? null : $p->getSystemName());
                } else if ($uiField instanceof Pt_SelectListMultiple) {
                    if ($value == null) {
                        return "new Array()";
                    }
                    $codes = [];
                    if ( !is_array($value) ) {
                        $value = [ $value ];
                    }
                    for ($k = 0, $c = count($value); $k < $c; $k++) {
                        $item = Pt_PickItemManager::getById($value[$k]);
                        $codes[$k] = $this->encode($item == null ? null : $item->getCode());
                    }
                    return util_converge($codes);
                } else {
                    $itemId = intval($value);
                    $item = Pt_PickItemManager::getById($itemId);
                    return $this->encode($item == null ? $value : $item->getCode());
                }
            } else if (TEXT_SUFFIX == $suffix) {
                if ($uiField instanceof Pt_UserLink) {
                    return Pt_UserProxy::getHumanName(intval($value));   // Bug 38016
                }
                return $this->encode($uiField->toText($value, $data));
            } else if (VALUE_SUFFIX == $suffix) {
                if ($uiField instanceof Pt_CheckBox) {
                    return (pt_boolval($value) ? 'true' : 'false');    // Bug 37227
                } else if ($uiField instanceof Pt_DecimalInput) {
                    return $uiField->toExport($value, null, false);
                } else if ($uiField instanceof Pt_SelectListMultiple) { // T 9715
                    return Pt_PickItemManager::getPickItemEnum($uiField->getSource(), $value);
                }
            } else if (MS_SUFFIX == $suffix) {    // Bug 36594
                if ( $uiField instanceof Pt_DateTimeInput ) {
                    $date = new DateTime($value);
                    return $date->getTimestamp() * 1000;
                } else if ( $uiField instanceof Pt_DateInput ) {
                    return GetTimestampFromDate($value) * 1000;
                }
            } else if (USERFORMAT_SUFFIX == $suffix) {    // Bug 38435
                if ($uiField instanceof Pt_DateInput) {
                    return $uiField->toText($value, $data);
                }
            } else if (ISO_SUFFIX == $suffix) {    // Bug 38435
                if ($uiField instanceof Pt_DateInput) {
                    return $uiField->toExport($value, $data, false);
                }
            } else if (HTML_SUFFIX == $suffix && JAVA_SCRIPT != $this->encoding) {    // Bug 39874
                return $this->toHTML($uiField, $value, $data);
            }

            switch ($this->encoding) {
                case JAVA_SCRIPT:
                    if (is_array($value)) {
                        return util_converge($value);
                    } else if (VALUE_SUFFIX == $suffix) {
                        return util_jsEncode($uiField->toText($value, $data));
                    } else if ($uiField instanceof Pt_ImageInput || HTML_SUFFIX == $suffix) {
                        return util_jsEncode($this->toHTML($uiField, $value, $data));
                    } else if (is_string($value)) {
                        if ($uiField instanceof Pt_DecimalInput) {
                            return $uiField->fromImport($value, false, $setToNull);
                        }
                        return util_jsEncode($value);
                    }
                    return $value;

                case URL_ENCODING:
                    return urlencode($uiField->toText($value, $data));

                case HTML_TEXT:
                    if (VALUE_SUFFIX == $suffix) {
                        return $uiField->toText($value, $data);
                    } else if ($uiField instanceof Pt_ImageInput) {
                        return $uiField->toHTML($value, $data);
                    } else if ( $uiField instanceof Pt_IntInput && $suffix == ''
                         && FeatureConfigManagerFactory::getInstance()
                                                       ->isFeatureEnabled('ENABLE_USER_PREFS_NUMBER_FORMATTING')
                         && FeatureConfigManagerFactory::getInstance()
                                                       ->isFeatureEnabled('ENABLE_PT_USER_PREFS_NUMBER_FORMATTING')
                         && !$uiField->getFieldDef()?->getProperty(FIELD_NO_FORMAT) ) {
                        return $uiField->toText($value, $data);
                    }
                    return $this->toHTML($uiField, $value, $data);

                case XML_TEXT:
                    if (VALUE_SUFFIX == $suffix) {    // Bug 42379
                        return util_encode($uiField->toText($value, $data));
                    }
                    return util_encode($uiField->toExport($value, $data, false));

                default:
                    // checkbox is shown in UI as a graphical element, so we need the boolean value here
                    // if the client needs translated text, he can use TEXT_SUFFIX = #text
                    if ( $uiField instanceof Pt_CheckBox ) {
                        return $uiField->toExport($value, $data, false);
                    }
                    return $uiField->toText($value, $data);
            }
        } catch (Pt_RecursionException $re) {
            throw $re;
        } catch (Exception $ex) {
            $throw = Pt_FormulaUtil::reportAllJsErrors() || $extraInfo == Pt_FormulaUtil::FROM_FORMULA_DEBUGGER;
            if ($throw) {
                throw $ex;
            }

            return GT($this->textMap, "IA.ERROR") . ": ".$ex->getMessage();
        }
    }

    /**
     * Get HTML text value
     *
     * @param Pt_AbstractField $uiField
     * @param mixed            $value
     * @param Pt_DataObject    $data
     *
     * @return string
     */
    protected function toHTML(Pt_AbstractField $uiField, $value, $data) 
    {
        if ($this->params != null) {
            $uiField->appendViewHTML($buff, $value, $data, $this->params, null, $this->recursionLevel+1);
            return $buff;
        } else {
            return $uiField->toHTML($value, $data); 
        }
    }

    /**
     * Get encoded string value
     *
     * @param string $text
     *
     * @return string
     */
    protected function encode($text) 
    {
        switch ($this->encoding) {
        case JAVA_SCRIPT:
            return util_jsEncode($text);
        case URL_ENCODING:
            return urlencode($text);
        case XML_TEXT:
            return util_encode($text);
        default:
            return $text;
        }
    }

    /**
     * Get object definition by name
     *
     * @param string $symbolName
     *
     * @return Pt_DataObjectDef|null
     */
    protected function getObjectDef($symbolName) 
    {
        if (strlen($symbolName) == 0) {
            return $this->objDef; 
        }
        if ($this->objDef != null && $symbolName == $this->objDef->getObjectDefName()) {
            return $this->objDef; 
        }
        if (CURR_REC == $symbolName && $this->currentRecord != null) {
            return $this->currentRecord->getObjectDef(); 
        }
        if (LOOP_REC == $symbolName && $this->data != null) {     // T 9906
            return $this->data->getObjectDef(); 
        }

        if ($this->map === null) {
            $this->map = [];
        }
        $objDef = $this->map[$symbolName] ?? null;
        if ($objDef != null) {
            return $objDef; 
        }

        $objDef = $this->findObjectDef($symbolName);
        if ($objDef != null) {
            $this->map[$symbolName] = $objDef; 
        }
        return $objDef;
    }

    /**
     * Find object definition by name
     *
     * @param string $symbolName
     *
     * @return Pt_DataObjectDef|null
     */
    protected function findObjectDef($symbolName) 
    {
        if ($symbolName == CURR_VISIT) {
            return ($this->portalVisitor == null ? null : $this->portalVisitor->getObjectDef()); 
        }

        $rel = $this->getRelationship($symbolName);
        if ($rel != null && $this->objDef != null) {
            $objDef = $rel->getOtherObjectDef($this->objDef->getId());
            if ($objDef != null) {
                return $objDef; 
            }
        }

        return Pt_DataObjectDefManager::getByName($symbolName);
    }

    /**
     * Get data object by definition name
     *
     * @param Pt_DataObjectDef $objDef
     * @param string           $symbolName
     * @param int              $relType
     *
     * @return Pt_DataObject|null
     */
    protected function getObject($objDef, $symbolName, $relType) 
    {
        //eppp_p("getObject: symbolName=$symbolName relType=$relType");
        if (CURR_VISIT == $symbolName) {
            return $this->portalVisitor; 
        } else if (CURR_REC == $symbolName) {
            return $this->currentRecord; 
        } else if (LOOP_REC == $symbolName) {   // T 9906
            return $this->data; 
        } else if ($this->origData!=null && $this->origData->getObjectDefId()==$objDef->getId() && $relType!=REL_CHILD) {   // Bug 41359
            return $this->origData; 
        } else if ($objDef!=null && $this->data != null && $this->data->getObjectDefId()==$objDef->getId() && ($relType==REL_HETERO || $relType == REL_CHILD)) {  // Bug 39853
            return $this->data;
        } else {

            $rel = $this->getRelationship($symbolName);
            if ($rel == null || $this->data == null) {    // Bug 38394
                return null; 
            }
                
            $relId = -1;
            if ($relType == REL_PARENT) {   // Bug 39471
                $relValue = Pt_RelationshipManagerChoose::getHierarchyIds($rel, $this->data->getId(), $relType == REL_PARENT);
                if ($relValue instanceof Pt_ArrayIds) {
                    $relValue = $relValue->getIds();
                }
                if ($relValue) {
                    $relId = intval($relValue[0]); 
                }
            } else {
                $relField = $this->data->getObjectDef()->getRelationshipField($rel);
                if ($relField == null) {
                    return null; 
                }
                $relFieldName = $relField->getFieldName();
                $relValue = $this->data->getFieldValue($relFieldName);
                if ( $relValue instanceof Pt_ArrayIds ) {
                    $relValue = $relValue->getIds();
                }

                if (is_array($relValue) && $relValue) {
                    $relId = intval($relValue[0]); 
                } else {
                    $relId = intval($relValue); 
                }
            }

            /** @noinspection PhpUnusedLocalVariableInspection */
            $data2 = null;
            try {
                $data2 = Pt_DataObjectManager::getById($objDef, $relId);
            } catch (Exception $ex2) {    // if object and ID do not match...
                $data2 = null;
            }

            if ($data2 == null) {   // Bug 37084
                $isParent = ($relType==REL_PARENT);
                $data2 = Pt_RelationshipManagerChoose::getObject($rel, $this->data, $isParent);
            }
            
            return $data2;
        }
    }

    /**
     * Process loop of related objects
     *
     * @param string &$buff
     * @param string $key
     * @param int    $index1
     * @param string $extraInfo
     *
     * @return int
     */
    protected function processLoop(& $buff, $key, $index1, $extraInfo='')
    {
        $keyLen = isl_strlen($key);
        $maxLoop = MAX_LOOP_ITEMS;
        $index = isl_strpos($key, '(');
        if ($index!==false) {
            $maxLoop = intval(util_extract($key, '(', ')'));
            $key = (string)isl_substr($key, 0, $index);
        }

        // T 6099
        $fields = null;
        $index = isl_strpos($key, '[');
        if ($index!==false) {
            $str = util_extract($key, '[', ']');
            $key = (string)isl_substr($key, 0, $index);
            $arr = explode(',', $str);
            $fields = [];
            foreach($arr as $fieldName) {
                $fieldName = isl_trim($fieldName);
                if (strlen($fieldName)>0) {
                    $fields[] = $fieldName;
                }
            }
            if (!$fields) {
                $fields = null; 
            }
        }

        $index = isl_strpos($key, '.');
        if ($index===false) {
            return $index1+$keyLen; 
        }
        $tokenName = isl_substr($key, $index+1);
        $suffix = '';
        $index = isl_strpos($tokenName, '#');
        if ($index!==false) {
            $suffix = isl_substr($tokenName, $index+1);
            $tokenName = isl_substr($tokenName, 0, $index);
        }

        $relName = $tokenName;
        $rel = null;
        if (ALL_RECORDS != $relName) {
            $rel = $this->getRelationship($relName);
            if ($rel == null) {
                if ( strpos($relName, LOOKUP_FIELD_IDENTIFIER) === 0 ) {
                    throw new Pt_I18nException('PAAS-0526', "$relName is not a valid lookup field name",
                                               [ 'RELNAME' => $relName ]);
                } else {
                    throw new Pt_I18nException('PAAS-0527', "No relationship definition named $relName",
                                               [ 'RELNAME' => $relName ]);
                }
            }
        }

        $endToken = TOKEN_BEGIN.LOOP_END.'.'.$relName.TOKEN_END;
        $index2 = isl_strpos($buff, $endToken, $index1+LENGTH_BEGIN);
        if ($index2===false) {
            return $index1+$keyLen; 
        }
        $index0 = $index1+LENGTH_BEGIN+$keyLen+LENGTH_END;
        $loopTemplate = isl_substr($buff, $index0, $index2-$index0);
        if ($this->data == null && $rel != null) {
            $buff = isl_substr_replace($buff, '', $index1, $index2+isl_strlen($endToken)-$index1);
            return $index1;
        }

        $arr = [];
        $viewOrigId = strval($suffix);
        $this->getViewParameters($viewOrigId, $fieldName, $operator, $fieldValue);
        $listDef = Pt_ListDefManager::getByOriginalId($viewOrigId);
        if ($listDef != null) {
            $query = new Pt_ListQuery($listDef, $this->data);
            if ($rel != null) {
                $query->setRelationshipDef($rel); 
            }
            $this->processViewParameters($fieldName, $operator, $fieldValue, $extraInfo);
            if ( $fieldName && $operator && ( $objectDef = $listDef->getObjectDef() ) ) {
                $field = $objectDef->getByName($fieldName);
                if ( $field ) {
                    $filter =
                        new Pt_Filter($query->getNumFilters(), $field, $operator, (string) $fieldValue, $fieldValue);
                    $query->addFilter($filter);
                }
            }
            $arr = $query->getDataObjects($maxLoop, $fields);
        } else if ($rel != null) {
            $arr = Pt_RelationshipManagerChoose::getObjects($rel, $this->data, false, null, $fields);    // for hierarchy iterate through children
            if (count($arr) > $maxLoop) {
                $arr = array_slice($arr, 0, $maxLoop); 
            }
        }

        $this->origData = $this->data;
        $buff2 = '';
        foreach ($arr as $data2) {
            $this->setDataObject($data2);
            $str = $this->parse($loopTemplate, $extraInfo);
            $buff2 .= $str;
        }
        $this->setDataObject($this->origData);
        $this->origData = null;

        $buff = isl_substr_replace($buff, $buff2, $index1, $index2+isl_strlen($endToken)-$index1);
        return $index1 + isl_strlen($buff2);
    }

    /**
     * Process EVAL expression
     *
     * @param string $buff
     * @param string $expression
     * @param int    $index1
     * @param string $extraInfo
     *
     * @return int
     */
    protected function processEval(& $buff, $expression, $index1, $extraInfo='')
    {
        $index2 = $index1+LENGTH_EVAL_BEGIN+isl_strlen($expression)+LENGTH_EVAL_END;
        $expression = isl_trim($expression);

        $replace = '';
        if (strlen($expression) > 0) {
            try {
                $replace = Pt_FormulaUtil::evalBareJavascript($expression, $extraInfo);
            } catch (Exception $se) {
                $replace = GT($this->textMap, "IA.ERROR"). ': ' . $se->getMessage();
            }
        }

        $buff = isl_substr_replace($buff, $replace, $index1, $index2-$index1);
        return $index1+isl_strlen($replace);
    }

    /**
     * Get link to view page
     *
     * @param string $tokenName
     * @param string $suffix
     * @param int    $relType
     *
     * @return string|int|null
     */
    protected function getPageLink($tokenName, $suffix, $relType) 
    {
        $objDef = $this->getObjectDef($tokenName);
        if ($objDef == null) {
            return null; 
        }
        $objDefId = $objDef->getId();
        if (ID_SUFFIX == $suffix) {
            return Pt_WebUtil::getPageId($objDefId, TYPE_VIEW);
        }

        if (strlen($suffix) > 0) {
            switch ($suffix) {
            case GENERIC_SUFFIX:
                $pages = Pt_WebPageManager::getByObjectDefAndType($objDefId, TYPE_GENERIC);
                if (!$pages) {
                    return null;
                }
                return Pt_WebUtil::url("pt_main.phtml?pageId=".$pages[0]->getId(), OP_RUNTIME);
            case TEMPLATES_SUFFIX:
                return Pt_WebUtil::url("pt_templates.phtml?objDefId=".$objDefId, OP_RUNTIME);
            case IMPORT_SUFFIX:
                return Pt_WebUtil::url("pt_import.phtml?objDefId=".$objDefId, OP_RUNTIME);
            }
            
            if (strpos($suffix, '@') !== false) {   // Bug 39346
                $origId = isl_substr($suffix, 1);
                $page = Pt_WebPageManager::getByOriginalId($origId);
                if ($page==null) {
                    return null; 
                }
                if ($page->isPortal()) {
                    if ($this->portal == null) {
                        return null; 
                    }
                    $cnyTitle = Profile::getCompanyCacheProperty('company', 'TITLE');
                    return BaseUrl().'pt_p.phtml?.op='.OP_PORTAL.'&p='.$this->portal->getId().'&c='.urlencode($cnyTitle).'&g='.$page->getId();
                }
                return Pt_WebUtil::url("pt_main.phtml?pageId=".$page->getId(), OP_RUNTIME);
            }
        }

        $data = $this->getObject($objDef, $tokenName, $relType);
        if ($data == null) {
            return null; 
        }

        $str = Pt_WebUtil::getViewLink($data);
        if (URL_SUFFIX == $suffix) {
            $str = util_extract($str, "href='", "'"); 
        }
        return $str;
    }

    /**
     * Get link to portal's page
     *
     * @param string $suffix
     * @param string $suffix2
     *
     * @return string
     */
    protected function getPortalPage($suffix, $suffix2) 
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $portal = null;
        $page = null;
        $index = isl_strpos($suffix, '.');
        if ($index > 0) {
            $portalId = strval(isl_substr($suffix, 0, $index));
            $portal = Pt_PortalManager::getByOriginalId($portalId);
            $pagePart = isl_substr($suffix, $index+1);
            if (LOGOUT==$pagePart) {
                return $this->getLogoutLink($portal, $suffix2); 
            } else if (EMAIL_URL==$pagePart) {
                return $portal->getCoreURL();
            }
            $pageId = strval($pagePart);
            $page = Pt_WebPageManager::getByOriginalId($pageId);
        } else {
            $portalId = strval($suffix);
            $portal = Pt_PortalManager::getByOriginalId($portalId);
        }
        if ($portal == null) {
            return null; 
        }
        if ($page == null) {
            $page = Pt_WebPageManager::getById($portal->getMainPageId()); 
        }
        if ($page == null) {
            return null; 
        }
        if (ID_SUFFIX == $suffix2) {
            return strval($page->getId()); 
        }

        $url = Pt_WebUtil::url($portal->getPageURL($page->getId()), OP_PORTAL);
        if (URL_SUFFIX == $suffix2) {
            return $url; 
        }

        $buff2 = "<a href='".$url."'>".$page->__toString()."</a>";
        return $buff2;
    }

    /**
     * Get link to portal's logout
     *
     * @param Pt_Portal $portal
     * @param string    $suffix2
     *
     * @return string
     */
    protected function getLogoutLink($portal, $suffix2) 
    {
        if ($portal == null) {
            return null; 
        }
        $url = $portal->getPageURL()."&act=portalLogout";
        if (URL_SUFFIX == $suffix2) {     // T 10136
            return $url; 
        }

        return "<a href='".$url."'>" . GT($this->textMap, "IA.LOGOUT") . "</a>";
    }

    /**
     * Get shared image
     *
     * @param Pt_SharedImage $imgField
     * @param string         $suffix2
     *
     * @return string
     */
    protected function getSharedImage($imgField, $suffix2) 
    {
        if (URL_SUFFIX == $suffix2) {
            return $imgField->getImageURL(); 
        }
        if ($this->encoding == JAVA_SCRIPT) {
            return util_jsEncode($imgField->toHTML(null, null));
        } else {
            return $imgField->toHTML(null, null);
        }
    }

    /**
     * Get relationship by lookup name or name
     *
     * @param string $relName
     *
     * @return Pt_RelationshipDef|null
     */
    protected function getRelationship($relName)
    {
        if ( $this->objDef != null ) {
            $rel = $this->objDef->getRelationshipDefByRelationshipOrLookupFieldName($relName);
        } else {
            if ( $this->data != null && null != ( $objDef = $this->data->getObjectDef() ) ) {
                $rel = $objDef->getRelationshipDefByRelationshipOrLookupFieldName($relName);
            } else {
                $rel = Pt_RelationshipDefManager::getByName($relName);
            }
        }

        return $rel;
    }
    
    /**
     * @param string &$viewOrigId
     * @param string &$fieldName
     * @param string &$operator
     * @param string &$fieldValue
     *
     * @throws Exception
     */
    private function getViewParameters(&$viewOrigId, &$fieldName, &$operator, &$fieldValue)
    {
        $isByObjectName = false;
        $viewArr = explode('@', $viewOrigId);
        if ( $objDef = Pt_DataObjectDefManager::getByName($viewArr[0]) ) {
            $isByObjectName = true;
            $listDefs = Pt_ListDefManager::getByObjectDef($objDef->getId());
            if ( $listDefs ) {
                $viewOrigId = $listDefs[0]->getOriginalId();
            } else {
                throw new Pt_I18nException('PAAS-0879', "$objDef has no views. Create at least one",
                                           [ 'OBJDEF' => "$objDef" ]);
            }
        }
        $fieldName = '';
        $operator = '';
        $fieldValue = '';
        $cnt = count($viewArr);
        if ( !$isByObjectName ) { // old style; loop over a view using the original id as parameter
            if ( $cnt > 3 ) {
                $viewOrigId = $viewArr[0] . '@' . $viewArr[1];
                $fieldName = $viewArr[2];
                $operator = strtoupper($viewArr[3]);
                for ( $i = 4; $i < $cnt; $i++ ) {
                    $fieldValue .= $viewArr[$i];
                }
            }
        } else { // new style; loop over a view using the object name as parameter
            if ( $cnt > 2 ) {
                $fieldName = $viewArr[1];
                $operator = strtoupper($viewArr[2]);
                for ( $i = 3; $i < $cnt; $i++ ) {
                    $fieldValue .= $viewArr[$i];
                }
            }
        }
    }
    
    /**
     * @param string $fieldName
     * @param string $fieldValue
     * @param string $operator
     * @param string $extraInfo
     *
     * @throws Exception
     */
    private function processViewParameters(&$fieldName, &$operator, &$fieldValue, $extraInfo)
    {
        if ( $fieldName && $operator ) {
            $fieldName = $this->parseParam($fieldName, $extraInfo);
            
            $operator = $this->parseParam($operator, $extraInfo);
            $operator = $this->normalizeOperator($operator);
            $this->validateViewOperator($operator);
            
            if ( !$fieldValue && !in_array($operator, [ OP_NUL, OP_NNUL ]) ) {
                throw new Pt_I18nException('PAAS-0894', "Insufficient parameters. No value specified");
            }
            $fieldValue = $this->parseParam($fieldValue, $extraInfo);
        }
    }
    
    /**
     * @param string $param
     * @param string $extraInfo
     *
     * @return string
     */
    private function parseParam($param, $extraInfo)
    {
        $pattern = '/\.|#|^' . API_ENDPOINT . '$|^' . CURRENT_ENTITY . '$/';
    
        if ( strlen($param) >= 2
             && substr($param, 0, 1) == DONT_INTERPRET
             && substr($param, -1) == DONT_INTERPRET ) {
            $parsedParam = substr($param, 1, -1);
        } else if ( preg_match($pattern, $param) ) {
            $parsedParam = (string) $this->parse("{!$param!}", $extraInfo);
        }
        
        return $parsedParam ?? $param;
    }
    
    /**
     * @param string $operator
     *
     * @return string
     */
    private function normalizeOperator($operator)
    {
        switch ($operator) {
            case '=':
                $operator = OP_EQ;
                break;
            case '!=':
            case '<>':
                $operator = OP_NEQ;
                break;
            case 'STARTS WITH':
            case 'STARTS':
                $operator = OP_ST;
                break;
            case 'CONTAINS':
            case 'LIKE':
                $operator = OP_CT;
                break;
            case 'DOES NOT CONTAIN':
            case '!CONTAINS':
            case '!LIKE':
                $operator = OP_NCT;
                break;
            case '<':
                $operator = OP_LT;
                break;
            case '>':
                $operator = OP_GT;
                break;
            case '<=':
                $operator = OP_LE;
                break;
            case '>=':
                $operator = OP_GE;
                break;
            case 'IS NULL':
            case 'ISNULL':
                $operator = OP_NUL;
                break;
            case '!IS NULL':
            case '!ISNULL':
                $operator = OP_NNUL;
                break;
            default:
                break;
        }
        
        return $operator;
    }
    
    /**
     * @param string $operator
     *
     * @throws Exception
     */
    private function validateViewOperator($operator)
    {
        if ( !in_array($operator, [
            OP_EQ,      // equals
            OP_NEQ,     // not equal
            OP_ST,      // starts with
            OP_CT,      // contains
            OP_NCT,     // does not contain
            OP_LT,      // less than
            OP_GT,      // greater than
            OP_LE,      // less or equal
            OP_GE,      // greater or equal
            OP_NUL,     // is null
            OP_NNUL,    // not null
        ]) ) {
            throw new Pt_I18nException('PAAS-0895', "Wrong operator '$operator'", [ 'OPERATOR' => "$operator" ]);
        }
    }

    /**
     * @param string $definition
     *
     * @return string
     */
    protected function getAccessToken(string $definition) : string
    {
        preg_match('/([\w\_\d]+)\(([\w\W]*)\)/', $definition, $matches);
        if ( $matches[1] === REST_API_ACCESS_TOKEN && !empty($matches[2])) {
            return json_encode(AccessToken::createAccessTokenByClientId($matches[2]));
        }
        return '{}';
    }

}

