<?
/**
 * Configurator event which invokes an REST API call.
 */
require_once('Pt_Includes.inc');

require_once('Pt_EventConfig.cls');
require_once('Pt_EventConstants.inc');
require_once('Pt_TemplateManager.cls');
require_once('Pt_IntacctRESTAPIConfig.cls');

class Pt_IntacctRESTAPIConfig extends Pt_EventConfig
{

    /**
     * @param string $displayName
     * @param string $description
     * @param string $cannotCreateMsg
     */
    public function __construct($displayName, $description, $cannotCreateMsg) {
        parent::__construct($displayName, $description, $cannotCreateMsg);
        $tokens = [ "IA.ENTER_API_VERSION", "IA.CHOOSE_HTTP_METHOD", "IA.HTTP_METHOD", "IA.PLEASE_SELECT", "IA.ENTER_TARGET_RESOURCE_PATH",
                    "IA.TARGET_RESOURCE", "IA.TARGET_RESOURCE_ID", "IA.SELECT_THE_DOCUMENT_TEMPLATE_THAT_SHOULD_BE_USE1", "IA.DOCUMENT_TEMPLATE",
                    "IA.FIND_TEMPLATE", "IA.CREATE_TEMPLATE", "IA.VIEW_TEMPLATE", "IA.ERROR.ERROR_PLEASE_SELECT_A_DOCUMENT_TEMPLATE",
                    "IA.ERROR.ERROR_PLEASE_SPECIFY_API_VERSION", "IA.ERROR.ERROR_PLEASE_SPECIFY_HTTP_METHOD", "IA.ERROR.ERROR_PLEASE_SPECIFY_TARGET_RESOURCE",
                    "IA.CHOOSE_TARGET_RESOURCE_TYPE", "IA.RESOURCE_TYPE" ];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
    }

	/**
	 * Get class name to create runtime event
     *
     * @return string
	 */
	public function getRuntimeClassName()
    {
		return "Pt_IntacctRESTAPI";
	}

	/**
	 * Can create event of given type?
     *
     * @param Pt_DataObjectDef $objDef
     *
     * @return bool
	 */
	public function canCreateEvent(Pt_DataObjectDef $objDef)
    {
	    if ( Pt_SetupComponents::$isQuixote ) {
	        return true;
        }
        $arr = Pt_TemplateManager::getDocTemplates($objDef->getId());
        return (bool)$arr;
	}

	/**
	 * Get HTML to configure EventDef (can be null)
     *
     * @param Pt_PageParams    $params
     * @param Pt_EventDef      $eventDef
     * @param Pt_DataObjectDef $objDef
     *
     * @return string
	 */
	public function getConfigHTML(Pt_PageParams $params, $eventDef, Pt_DataObjectDef $objDef)
    {
        $templateId = http_getIntParameter(FIELD_TEMPLATE_ID);
        if ( !$templateId ) {
            $templateId = ( $eventDef == null ? 0 : $eventDef->getTemplateId() );
        }

		$buff = "<table class='rbs_componentContentTable' cellpadding='0' cellspacing='0'>\n";

        $buff .= "<tr>\n";
        $buff .= "<td class='rbs_grayDetailHTMLcol' colspan=2>" . GT($this->textMap, "IA.ENTER_API_VERSION") . "</td>\n";
        $buff .= "</tr>\n";

        $buff .= "<tr>\n";
        
        $restApiVersion = $_REQUEST[FIELD_REST_API_VERSION];
        if ( $restApiVersion === null ) {
            $restApiVersion = $eventDef ? ( $eventDef->getProperty(FIELD_REST_API_VERSION) ?? '' ) : '';
        }
        $formAction = ( $eventDef instanceof Pt_EventDef ) ? 'pt_eventdEdit.phtml' : 'pt_eventCreate2.phtml';
        $buff .= Pt_SetupComponents::getRestApiVersionsSelector($restApiVersion, $formAction, 'formula', $objDef);

        $buff .= "<tr>\n";
        $buff .= "<td class='rbs_grayDetailHTMLcol' colspan=2>" . GT($this->textMap, "IA.CHOOSE_HTTP_METHOD") . "</td>\n";
        $buff .= "</tr>\n";
    
        $emptyOrSelectPicker = Pt_SetupComponents::$emptyOrSelectPicker;
        $emptyOrFormControl = Pt_SetupComponents::$emptyOrFormControl;
        
        $buff .= "<tr>\n";
        $buff .= "<td id='apiHttpMethod_label' class='rbs_rightLabelRequired'>" . GT($this->textMap, "IA.HTTP_METHOD") . "</td>\n";
        $buff .= "<td id='apiHttpMethod_field' class='rbs_leftDataColWide'><select class='"
                 . $emptyOrSelectPicker . $emptyOrFormControl
                 . "' name='" . FIELD_REST_API_METHOD . "' onchange='setRestMethodChange()'>\n";
        $buff .= "<option value='' class='no-op'>-- " . GT($this->textMap, "IA.PLEASE_SELECT") . " --</option>\n";
        $httpMethod = http_getParameter(FIELD_REST_API_METHOD);
        if ( !$httpMethod ) {
            $httpMethod = util_encode($eventDef ? $eventDef->getProperty(FIELD_REST_API_METHOD) ?? '' : '');
        }
        foreach ( [ APIConstants::API_HTTP_METHOD_POST, APIConstants::API_HTTP_METHOD_PATCH,
                    APIConstants::API_HTTP_METHOD_DELETE ] as $op ) {
            Pt_WebUtil::appendOption($buff, $op, $op, $httpMethod);
        }
        $buff .= "</select><br><span class='rbs_alertMsg' id='apiHttpMethod_error'></span></td>\n";
        $buff .= "</tr>\n";

        $buff .= "<tr>\n";

        // $resourceType = http_getParameter(FIELD_REST_API_RESOURCE_TYPE);
        // if ( !$resourceType ) {
        //     $resourceType = util_encode($eventDef ? $eventDef->getProperty(FIELD_REST_API_RESOURCE_TYPE) ?? '' : '');
        // }
        // $buff .= $this->getRestApiResourceTypeSelector('formula', $resourceType, $formAction);

        $buff .= "<tr>\n";
        $buff .= "<td class='rbs_grayDetailHTMLcol' colspan=2>" . GT($this->textMap, "IA.ENTER_TARGET_RESOURCE_PATH") . "</td>\n";
        $buff .= "</tr>\n";
        $resourcePath = http_getParameter(FIELD_REST_API_RESOURCE_PATH);
        if ( !$resourcePath ) {
            $resourcePath = util_encode($eventDef ? $eventDef->getProperty(FIELD_REST_API_RESOURCE_PATH) ?? '' : '');
        }
        $buff .= "<tr>\n";
        $buff .= "<td id='apiResourcePath_label' class='rbs_rightLabelRequired'>" . GT($this->textMap, "IA.TARGET_RESOURCE") . "</td>\n";
        $buff .= "<td id='apiResourcePath_field' class='rbs_leftDataColWide'><select class='"
                 . $emptyOrSelectPicker . $emptyOrFormControl . "' name='"
                 . FIELD_REST_API_RESOURCE_PATH . "'>\n";
        $buff .= "<option value='' class='no-op'>-- " . GT($this->textMap, "IA.PLEASE_SELECT") . " --</option>\n";
        if ($restApiVersion !== '') {
            try {
                $registry = RegistryLoader::getInstance($restApiVersion);
                if ($registry) {
                    foreach ($this->getRestObjectsNames($restApiVersion) as $restobjectName) {
                        Pt_WebUtil::appendOption($buff, $restobjectName, $restobjectName, $resourcePath);
                    }
                }
            } catch (Exception) {

            }
        }
        $buff .= "</select><br><span class='rbs_alertMsg' id='apiResourcePath_error'></span></td>\n";
        $buff .= "</tr>\n";
        
        $resourcePathId = http_getParameter(FIELD_REST_API_RESOURCE_ID);
        if ( $resourcePathId === null ) {
            $resourcePath = util_encode($eventDef ? $eventDef->getProperty(FIELD_REST_API_RESOURCE_ID) ?? '' : '');
        }

        $buff .= "<tr name='apiResourceIdTr' id='apiResourceIdTr'>\n";
        $buff .= "<td id='apiResourceId_label' class='rbs_rightLabelRequired'>" . GT($this->textMap, "IA.TARGET_RESOURCE_ID") . "</td>\n";
        $buff .= "<td id='apiResourceId_field' class='rbs_leftDataColWide'><input class="
                 . $emptyOrFormControl . " size='80' type='text' name='" . FIELD_REST_API_RESOURCE_ID
                 . "' value='" . util_encode($resourcePath)
                 . "'><br><span class='rbs_alertMsg' id='apiResourceId_error'></span></td>\n";
        $buff .= "</tr>\n";
        
        $buff .= "<tr name='templateIdTrLabel' id='templateIdTrLabel'>\n";
        $buff .= "<td class='rbs_grayDetailHTMLcol' colspan='2'>" . GT($this->textMap, "IA.SELECT_THE_DOCUMENT_TEMPLATE_THAT_SHOULD_BE_USE1") . "</td>\n";
        $buff .= "<tr>\n";
        $buff .= "<tr name='templateIdTr' id='templateIdTr'>\n";
        $buff .= "<td id='templateId_label' class='rbs_rightLabelRequired'>" . GT($this->textMap, "IA.DOCUMENT_TEMPLATE") . "</td>\n";
        $buff .= "<td id='templateId_field' class='rbs_leftDataColWide'>";
        $isQuixote = Pt_SetupComponents::$isQuixote;
        $buff .= "<select" . ( $isQuixote ? " onchange='selectItemToView(\"" . FIELD_TEMPLATE_ID
                                            . "\", this)' id='selectTemplateId'" : "" ) . " class='"
                 . $emptyOrSelectPicker . $emptyOrFormControl
                 . "' name='" . FIELD_TEMPLATE_ID . "'>\n";
        $buff .= "<option value='' class='no-op'>-- " . GT($this->textMap, "IA.PLEASE_SELECT") . " --</option>\n";
        $objDefId = $objDef->getId();
        foreach ( Pt_TemplateManager::getDocTemplates($objDefId) as $t ) {
            if ( $t->getTemplateType() == TEMPLATE_TYPE_REST_API ) {
                Pt_WebUtil::appendOption($buff, $t->getId(), $t->__toString(), $templateId);
            }
        }
        $buff .= "</select>";
        if ( $isQuixote ) {
            $docTemplateLookupPopup =
                new Pt_LookupPopup(GT($this->textMap, "IA.FIND_TEMPLATE"), FIELD_TEMPLATE_ID, $objDefId, false, DOC_TEMPLATES);
            $buff .= $docTemplateLookupPopup->getHTML();
            $docTemplateCreatePopup =
                new Pt_CreatePopup(GT($this->textMap, "IA.CREATE_TEMPLATE"), FIELD_TEMPLATE_ID, $objDefId, 'pt_docTemplEdit.phtml', false);
            $docTemplateCreatePopup->addParameter(FIELD_TEMPLATE_TYPE_FORCED, TEMPLATE_TYPE_REST_API);
            $buff .= $docTemplateCreatePopup->getHTML();
            $docTemplateViewPopup =
                new Pt_ViewPopup(GT($this->textMap, "IA.VIEW_TEMPLATE"), FIELD_TEMPLATE_ID, "pt_templateView.phtml", false);
            $docTemplateViewPopup->addParameter(FIELD_TEMPLATE_TYPE_FORCED, TEMPLATE_TYPE_REST_API);
            $buff .= $docTemplateViewPopup->getHTML();
        }
        $buff .= "<br><span class='rbs_alertMsg' id='templateId_error'></span></td>\n";
        $buff .= "</tr>\n";

		$buff .= "<tr height='10'><td></td></tr>\n";
		$buff .= "</table>\n";

        $buff .= $this->getExtraJS();
		return $buff;
	}
    
    /**
     * Get JavaScript functions
     *
     * @return string
     */
    private function getExtraJS()
    {
        $buff = "<script language='JavaScript'>\n";
        $buff .= "function setRestMethodChange() {
            setFieldLabelRequiredOrNotClass(
                document.getElementById('templateId_label'),
                (document.getElementsByName('apiHttpMethod')[0].value == '" . APIConstants::API_HTTP_METHOD_DELETE . "') ? false : true);
                
                var methodType = document.theForm." . FIELD_REST_API_METHOD . ".value;
                var showId = ( methodType == '" . APIConstants::API_HTTP_METHOD_PATCH . "')
                    || ( methodType == '" . APIConstants::API_HTTP_METHOD_DELETE . "');
                document.getElementById('apiResourceIdTr').style.display = (showId ? '' : 'none');
                
                var hasTemplateId = ( methodType == '" . APIConstants::API_HTTP_METHOD_POST . "')
                    || ( methodType == '" . APIConstants::API_HTTP_METHOD_PATCH . "');
                document.getElementById('templateIdTr').style.display = (hasTemplateId ? '' : 'none');
                document.getElementById('templateIdTrLabel').style.display = (hasTemplateId ? '' : 'none');
        }\n\n";
        
        $buff .= "setRestMethodChange();\n";
        $buff .= "rbf_addOnLoadMethod(rbf_initializeValidationField('templateId'));\n";
        $buff .= "rbf_addOnLoadMethod(rbf_initializeValidationField('apiHttpMethod'));\n";
        $buff .= "rbf_addOnLoadMethod(rbf_initializeValidationField('apiResourcePath'));\n";
        $buff .= "rbf_addOnLoadMethod(rbf_initializeValidationField('apiResourceId'));\n";
		$buff .= "</script>\n";
        
        return $buff;
    }

	/**
	 * Get JavaScript to verify EventDef (can be null)
     * @param Pt_PageParams     $params
     * @param Pt_EventDef       $eventDef
     * @param Pt_DataObjectDef $objDef
     *
     * @return string
	 */
	public function getVerifyJS(Pt_PageParams $params, $eventDef, Pt_DataObjectDef $objDef)
    {
        $buff  = '';
        $buff .= "if (templateId.value == '' && apiHttpMethod.value != 'DELETE') {\n";
		$buff .= "rbf_activateError('templateId','" . GT($this->textMap, "IA.ERROR.ERROR_PLEASE_SELECT_A_DOCUMENT_TEMPLATE") . "');\n";
		$buff .= "hasError=true;\n";
		$buff .= "}\n";

        $buff .= "if(apiVersion.value == '') {\n";
        $buff .= "rbf_activateError('apiVersion','" . GT($this->textMap, "IA.ERROR.ERROR_PLEASE_SPECIFY_API_VERSION") . "');\n";
        $buff .= "hasError=true;\n";
        $buff .= "}\n";

        $buff .= "if(apiHttpMethod.value == '') {\n";
        $buff .= "rbf_activateError('apiHttpMethod','" . GT($this->textMap, "IA.ERROR.ERROR_PLEASE_SPECIFY_HTTP_METHOD") . "');\n";
        $buff .= "hasError=true;\n";
        $buff .= "}\n";

        $buff .= "if(apiResourcePath.value == '') {\n";
        $buff .= "rbf_activateError('apiResourcePath','" . GT($this->textMap, "IA.ERROR.ERROR_PLEASE_SPECIFY_TARGET_RESOURCE") . "');\n";
        $buff .= "hasError=true;\n";
        $buff .= "}\n";
    
        $buff .= "if(apiResourceId.value == '' && apiHttpMethod.value != 'POST') {\n";
        $buff .= "rbf_activateError('apiResourceId','" . GT($this->textMap, "IA.ERROR.ERROR_PLEASE_SPECIFY_TARGET_RESOURCE") . "');\n";
        $buff .= "hasError=true;\n";
        $buff .= "}\n";

		return $buff;
	}

    /**
     * Read event's Properties from REST API request
     *
     * @return array
     */
    public function getProperties()
    {
        $props = [];
        $props[FIELD_REST_API_VERSION] = http_getParameter(FIELD_REST_API_VERSION);
        $props[FIELD_REST_API_METHOD] = http_getParameter(FIELD_REST_API_METHOD);
        // $props[FIELD_REST_API_RESOURCE_TYPE] = http_getParameter(FIELD_REST_API_RESOURCE_TYPE);
        $props[FIELD_REST_API_RESOURCE_PATH] = http_getParameter(FIELD_REST_API_RESOURCE_PATH);
        $props[FIELD_REST_API_RESOURCE_ID] = http_getParameter(FIELD_REST_API_RESOURCE_ID);
        return $props;
    }
    
    /**
     * @param string $codeMirrorComponentName
     * @param string $resourceType
     * @param string $formAction
     *
     * @return string
     * @throws APIException
     */
    public function getRestApiResourceTypeSelector(string $codeMirrorComponentName, string $resourceType, string $formAction)
    {
        $buff = "<td class='rbs_grayDetailHTMLcol' colspan=2>" . GT($this->textMap, "IA.CHOOSE_TARGET_RESOURCE_TYPE") . "</td>\n";
        $buff .= "</tr>\n";
    
        $buff .= "<tr>\n";
        $buff .= "<td id='apiResourceType_label' class='rbs_rightLabelRequired'>" . GT($this->textMap, "IA.RESOURCE_TYPE") . "</td>\n";
        $buff .= "<td id='apiResourceType_field' class='rbs_leftDataColWide'><select class='"
                 . Pt_SetupComponents::$emptyOrSelectPicker . Pt_SetupComponents::$emptyOrFormControl . "' name='"
                 . FIELD_REST_API_RESOURCE_TYPE . "' id='" . FIELD_REST_API_RESOURCE_TYPE . "' onchange='onChangeResourceType()'>\n";
        $buff .= "<option value='' class='no-op'>-- " . GT($this->textMap, "IA.PLEASE_SELECT") . " --</option>\n";

        foreach ( [ RegistryLoader::CRUD_SERVICE_NAME, RegistryLoader::GENERIC_SERVICE_NAME ] as $type ) {
            Pt_WebUtil::appendOption($buff, $type, $type, $resourceType);
        }
        $buff .= "</select><br><span class='rbs_alertMsg' id='apiResourceType_error'></span></td>\n";
        $buff .= "</tr>\n";
    
        $buff .= "<script language='JavaScript'>\n";
        $codeMirrorCode = ( Pt_SetupComponents::$isQuixote ) ? Pt_CodeMirror::setHiddenInputValueCode($codeMirrorComponentName) : '';
        $buff .= "function onChangeResourceType() {
            $codeMirrorCode
            document.theForm.action=\"$formAction\"; document.theForm.submit();
            //document.location.reload(true);
        }\n\n";
        
        $buff .= "rbf_addOnLoadMethod(rbf_initializeValidationField('apiResourcePath'));\n";
        $buff .= "</script>\n";
        
        return $buff;
    }
    
    /**
     * @param string $restApiVersion
     *
     * @return string[]
     */
    private function getRestObjectsNames(string $restApiVersion) : array
    {
        $result = [];
        
        try {
            $restObjects = $this->getRestObjectsList($restApiVersion);
            foreach ( $restObjects as $object ) {
                if ( !in_array($object[APIConstants::API_TYPE_KEY] ?? '', APIConstants::OBJECT_TYPES) ) {
                    continue;
                }
                $result[] = $object[APIConstants::API_OBJECT_REF_KEY];
            }
            sort($result);
        } catch ( Exception $e ) {
            logToFileError("Failed to retrieve object names for REST version $restApiVersion: " . $e->getMessage());
            $result = [];
        }
        
        return $result;
    }
    
    /**
     * @param string $restApiVersion
     *
     * @return array
     * @throws APIException
     * @throws APIInternalException
     */
    private function getRestObjectsList(string $restApiVersion) : array
    {
        $url =
            implode('/', [ "v$restApiVersion", RegistryLoader::GENERIC_SERVICE_NAME, APIConstants::MODEL_OBJECT_NAME ]);
        $parameters = [
            'sessionId'  => Session::getKey(),
            'url'        => $url,
            'httpMethod' => APIConstants::API_HTTP_METHOD_GET,
            'body'       => '',
            'headers'    => [],
        ];
        $disp = APIDispatcher::getDispatcher($parameters);
        $losProfile = LOSManager::getLOSProfileForCompany(GetMyCompany(), LOSProfile::LOS_PROCESS_LOG_TYPE_WEB_SERVICE,
                                                          LOSProfile::LOS_CLIENT_PLATFORM);
        try {
            if ( $losProfile->clientHasTransactionQuota() ) {
                $response = $disp->dispatch();
                if ( $response->getStatus() >= APIErrorMessages::HTTP_STATUS_CODE_MINIMUM_FAIL ) {
                    logToFileError("Failed to retrieve object list for REST version $restApiVersion: "
                                   . $response->__toString());
            
                    return [];
                }
                $resAsArray = json_decode($response, true);
                
                return $resAsArray[APIConstants::IA_RESULT_KEY] ?? [];
            } else {
                throw new Pt_I18nException('PAAS-0904', LOSManager::INSUFFICIENT_QUOTA_MESSAGE);
            }
        } finally {
            //call the finalize to add process log and update summary
            //we need to add a log no matter success or failure
            LOSManager::finalizeLOSProfile($losProfile);
        }
    }

}
