<?php
/**
 * Workflow event which creates new object record.
 */
require_once 'Pt_RuntimeEvent.cls';
require_once 'Pt_EventRunner.cls';

class Pt_CreateObject extends Pt_RuntimeEvent
{

    /**
     * Trigger workflow event.
     *
     * @param Pt_EventRunner $runner
     *
     * @throws Exception
     */
    public function trigger(Pt_EventRunner $runner) 
    {
        if ( !$this->runDownstreamTriggers ) {
            EntityManager::disableTriggers();
        }

        $map = Pt_DataMapManager::getConversionById($this->eventDef->getTemplateId());
        if ($map == null) {
            throw new Pt_I18nException('PAAS-0461', "$this: No conversion map found", [ 'THIS' => "$this" ]);
        }
        $srcObjDef = $map->getSrcObjectDef();

        $destObjDefKey = $map->getDestObjectDefId();
        $fieldData = $map->getMappedValues($this->data);
        if (Pt_StdDataObjectDef::isPhpObjDefId($destObjDefKey)) {
            $destObjDef = $map->getDestObjectDef();

            Pt_ObjectController::populateAutoFields($destObjDef, $fieldData);
            Pt_ObjectController::validateFields($destObjDef, $fieldData, Pt_ObjectController::VALIDATE_SKIP_AUTONUMBER);
            Pt_UniqueValueController::validate($destObjDef, -1, $fieldData, null,
                                               Pt_UniqueValueController::VALIDATE_SKIP_AUTONUMBER);
            Pt_ObjectController::populateAutoNumberFields($destObjDef, $fieldData);
            Pt_ObjectController::validateFields($destObjDef, $fieldData, Pt_ObjectController::VALIDATE_ONLY_AUTONUMBER);
            Pt_UniqueValueController::validate($destObjDef, -1, $fieldData, null,
                                               Pt_UniqueValueController::VALIDATE_ONLY_AUTONUMBER);

            $d = $runner->getDebugger();
            if ($d != null) {
                $localPlaceholderTokens = [
                    [
                        'id'           => 'IA.CREATING_OBJECT_DEF_FIELD_DATA',
                        'placeHolders' => [
                            [ 'name' => 'OBJECT_DEF', 'value' => "$destObjDef" ],
                            [ 'name' => 'FIELD_DATA', 'value' => util_array($fieldData) ],
                        ],
                    ],
                ];
                $txtMap = getIntlTextMap([], $localPlaceholderTokens);
                $this->textMap = array_merge($this->textMap, $txtMap);
                $d->log( GT($this->textMap, "IA.CREATING_OBJECT_DEF_FIELD_DATA") );
            }

            /** @noinspection PhpUnusedLocalVariableInspection */
            $newData = Pt_DataObjectManager::create($destObjDef, $fieldData, $runner);

            // Update source record to include new relationships
            $rels = Pt_RelationshipDefManager::getByDefs($srcObjDef->getId(), $destObjDef->getId());
            foreach ($rels as $rel) {
                $field = $srcObjDef->getRelationshipField($rel);
                if ($field == null) {
                    continue;
                }
                $value = Pt_RelationshipManagerChoose::getObjectIds($rel, $srcObjDef->getId(), $this->data->getId());
                $this->data->setFieldValue($field->getFieldName(), $value);
            }
        } else {
            try {
                $dsAPIClient = new DomainServiceAPIFunctionServiceClient(
                    Util_StandardObjectRegistry::getStandardObjectMapIdDomain()[$destObjDefKey],
                    "paas",
                    "trigger",
                    "1i"
                );

                $response = $dsAPIClient->invokeFunction("create-object", [
                    "data" => $fieldData,
                    "objectDefinitionKey" => $destObjDefKey
                ]);

                $result = $response["result"];
                if ($result !== "") {
                    throw new Exception($result);
                }
            } catch (Exception $exception) {
                throw new Exception($exception->getMessage());
            }
        }

        if ( !$this->runDownstreamTriggers ) {
            EntityManager::enableTriggers();
        }
    }

}
