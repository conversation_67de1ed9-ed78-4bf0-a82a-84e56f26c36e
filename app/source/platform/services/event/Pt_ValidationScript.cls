<?
/**
 * Trigger which validates record's data.
 */
require_once('Pt_RuntimeEvent.cls');
require_once('Pt_EventRunner.cls');

class Pt_ValidationScript extends Pt_RuntimeEvent {

    /**
     * Run trigger.
     *
     * @param Pt_EventRunner $runner
     *
     * @throws Exception
     */
    public function trigger(Pt_EventRunner $runner) {
        $formula = $this->eventDef->getCondition();
        if ($formula == null) {
            return;
        }
        /** @noinspection PhpUnusedLocalVariableInspection */
        $debugger = $runner->getDebugger();

        $parser = $runner->getParser($this->eventDef->getObjectDef());

        $parser->setActivation(ACTIV_VALIDATION);
        $message = Pt_FormulaUtil::evalObject($parser, $this->data, $formula,
            "SOURCE: Trigger " . $this->eventDef->getObjectDef() . "." . $this->getEventDef() . ": validation script");

        if (strlen($message) > 0) {
            throw new Exception($message);
        }
    }
}
