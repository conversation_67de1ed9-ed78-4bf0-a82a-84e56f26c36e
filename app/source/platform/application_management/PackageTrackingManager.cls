<?php

/**
 * Class PackageTrackingManager
 * TABLE: PACKAGE_TRACKING (global schema)
 */
class PackageTrackingManager extends EntityManager
{

    /**
     * Add new tracking entry
     *
     * @param Pt_Application $app
     *
     * @return bool
     */
    public static function trackApplication(Pt_Application $app)
    {
        if ( !FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('APPLICATION_MANAGEMENT')) {
            return true;
        }
        $mgr = Globals::$g->gManagerFactory->getManager('packagetracking');

        $values = [
            'COMPANYID'       => GetMyCompany(),
            'RESOURCE_TYPE'   => PACKAGE_TYPE_APP,
            'PACKAGE_ID'      => $app->getOriginalId(),
            'VERSION'         => $app->getVersion(),
            'NAME'            => $app->getUntranslatedAppName(),
            'IS_PUSH_ALLOWED' => $app->isPushAllowed(),
        ];

        return $mgr->add($values);
    }

    /**
     * Update tracking entry
     *
     * @param Pt_Application $app
     *
     * @return bool
     */
    public static function updateAppTracking(Pt_Application $app)
    {
        if ( !FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('APPLICATION_MANAGEMENT')) {
            return true;
        }
        $manager = Globals::$g->gManagerFactory->getManager('packagetracking');
        $params = [
            "options" => [ "noDBSorts" => true, ],
            'nodbfilters' => true,
            'filters' => [ [ [ "COMPANYID", '=', GetMyCompany() ],
                             [ "RESOURCE_TYPE", '=', PACKAGE_TYPE_APP ],
                             [ "PACKAGE_ID", '=', $app->getOriginalId() ],
                           ] ],
        ];
        $data = $manager->GetList($params);

        if ( $data ) {
            $data[0]['VERSION'] = $app->getVersion();
            $data[0]['IS_PUSH_ALLOWED'] = $app->isPushAllowed();
            $data[0]['NAME'] = $app->getUntranslatedAppName();
            $ok = $manager->set($data[0]);
        } else {
            $ok = self::trackApplication($app);
        }

        return $ok;
    }

    /**
     * Delete tracking entry
     *
     * @param string $packageId
     * @param string $resourceType
     *
     * @return bool
     */
    public static function deleteTracking(string $packageId, string $resourceType) : bool
    {
        if ( !FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('APPLICATION_MANAGEMENT')) {
            return true;
        }
        $manager = Globals::$g->gManagerFactory->getManager('packagetracking');
        $params = [ "selects" => [ 'recordno' ],
                    "options" => [ "noDBSorts" => true, ],
                    'filters' => [ [ [ "COMPANYID", '=', GetMyCompany() ],
                                     [ "RESOURCE_TYPE", '=', $resourceType ],
                                     [ "PACKAGE_ID", '=', $packageId ],
                                   ] ],
        ];
        $data = QueryResult($manager->GetListQuery($params), 0, '', GetGlobalConnection());

        if ( $data[0] && ( $recordNo = ( $data[0]['RECORDNO'] ?? 0 ) ) ) {
            return $manager->delete($recordNo);
        }

        return true;
    }
    
    /**
     * get the companies that don't have the specified version installed
     *
     * @param integer $version
     * @param string  $packageId
     *
     * @return array
     */
    public static function getCompaniesForInstall($version, $packageId)
    {
        if ( !FeatureConfigManagerFactory::getInstance()
                                         ->isFeatureEnabled('APPLICATION_MANAGEMENT') ) {
            return [];
        }
        $result = [];
        $manager = Globals::$g->gManagerFactory->getManager('packagetracking');
        $params = [ "selects" => [ 'RECORDNO' ],
                    "options" => [ "noDBSorts" => true, ],
                    'nodbfilters' => true,
                    'filters' => [ [ [ "RESOURCE_TYPE", '=', 'APP' ],
                                     [ "PACKAGE_ID", '=', $packageId ],
                                     [ "VERSION", '!=', $version ],
                                     [ "IS_PUSH_ALLOWED", '=', 'true' ],
                                   ] ],
        ];
        $data = QueryResult($manager->GetListQuery($params), 0, 0, GetGlobalConnection());
        
        foreach ( $data as $company ) {
            $result[] = $manager->get($company['RECORD#']);
        }
        
        return $result;
    }


    /**
     * Application version tracking - load/ refresh from user schema
     *
     * @param array $appsOnSchemas
     * @param int[] $assignApplicationsOwnership
     *
     * @return bool
     */
    public static function loadApplicationInfoFromUserSchemas($appsOnSchemas, $assignApplicationsOwnership) : bool
    {
        $manager = Globals::$g->gManagerFactory->getManager('packagetracking');
        $isSuccessful = true;
        foreach ($appsOnSchemas as $appsOnSchema) {
            foreach ($appsOnSchema as $app) {
                if (isset($app) && is_array($app)) {
                    try {
                        $appOriginalId = $app['ORIGINAL_ID'];
                        if ( $assignApplicationsOwnership[$appOriginalId] == $app['CNY#'] ) {
                            continue;
                        }
                        $params = [
                            'nodbfilters' => true,
                            "options" => ["noDBSorts" => true,],
                            'filters' => [
                                [
                                    ["COMPANYID", '=', $app['CNY#']],
                                    ["RESOURCE_TYPE", '=', PACKAGE_TYPE_APP],
                                    ["PACKAGE_ID", '=', $appOriginalId],
                                ]
                            ],
                        ];
                        $data = QueryResult($manager->GetListQuery($params), 0, 0, GetGlobalConnection());
                        $props = util_arrayFromText($app['PROPERTIES'] ?? '');
                        $isPushAllowed = pt_boolval($props[FIELD_IS_PUSH_ALLOWED] ?? '0') ? 'true' : 'false';
                        $values = [];
                        $values['COMPANYID'] = $app['CNY#'];
                        $values['RESOURCE_TYPE'] = PACKAGE_TYPE_APP;
                        $values['PACKAGE_ID'] = $appOriginalId;
                        $values['VERSION'] = $app['VERSION'];
                        $values['NAME'] = $app['APPLICATION_NAME'];
                        $values['IS_PUSH_ALLOWED'] = $isPushAllowed;
                        $values['WHENCREATED'] = $app['CREATED_AT'];
                        $values['WHENMODIFIED'] = $app['UPDATED_AT'];
                        $values['MODIFIEDBY'] = -1;
                        if ($data[0]['RECORD#'] ?? []) {
                            //Send SET_FROM_CSTOOLS as true - used just in regularSet to keep MODIFIEDBY value -1
                            $values['SET_FROM_CSTOOLS'] = true;
                            $values['RECORDNO'] = $data[0]['RECORD#'];
                            if (!$manager->set($values)) {
                                $isSuccessful = false;
                                LogToFile(
                                    __FILE__ . "." . __LINE__ . ": Application that fails on refresh has original id "
                                    . $appOriginalId . "\n"
                                );
                            }
                        } else {
                            $values['CREATEDBY'] = -1;
                            if (!$manager->add($values)) {
                                $isSuccessful = false;
                                LogToFile(
                                    __FILE__ . "." . __LINE__ . ": Application that fails on load has original id "
                                    . $appOriginalId . "\n"
                                );
                            }
                        }
                    } catch (Exception $exception) {
                        LogToFile(
                            __FILE__ . "." . __LINE__ . ": Load/refresh from user schema fails here - "
                            . $exception->getMessage() . "\n"
                        );
                    }
                }
            }
        }

        return $isSuccessful;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function regularAdd(&$values)
    {
        if (isset($values['COMPANY_NAME']) && !(isset($values['COMPANYID']))) {
            $companyLoginId = $values['COMPANY_NAME'];
            $values['COMPANYID'] = GetCompanyCny($companyLoginId);
            if ($values['COMPANYID'] === false) {
                Globals::$g->gErr->addIAError(
                    'PAAS-0933', __FILE__ . ':' . __LINE__,
                    "Company $companyLoginId does not exist.", ['COMPANY_NAME' => $companyLoginId]
                );
                return false;
            }
        }
        $values['VERSION'] = $values['VERSION'] ?? '0';
        $values['RESOURCE_TYPE'] = $values['RESOURCE_TYPE'] ?? PACKAGE_TYPE_APP;
        if (isset($values['NAME'])) {
            $appName = $values['NAME'];
            $app = Pt_ApplicationManager::getAppByDisplayName($values['NAME']);
            if ($app) {
                $values['PACKAGE_ID'] = $values['PACKAGE_ID'] ?? $app->getOriginalId();
            } else {
                Globals::$g->gErr->addIAError(
                    'PAAS-0934', __FILE__ . ':' . __LINE__,
                    "Application $appName does not exist.", ['APP_NAME' => $appName]
                );
                return false;
            }
        }
        $values['MODIFIEDBY'] = $values['MODIFIEDBY'] ?? GetMyUserid();
        $values['CREATEDBY'] = $values['CREATEDBY'] ?? GetMyUserid();
        
        return parent::regularAdd($values);
    }

    /**
     * @param $values
     *
     * @return bool
     */
    function regularSet(&$values)
    {
        if (!isset($values['SET_FROM_CSTOOLS'])) {
            $values['MODIFIEDBY'] = GetMyUserid();
        } else {
            unset($values['SET_FROM_CSTOOLS']);
        }
        return parent::regularSet($values);
    }

    /**
     * Returns a list of transaction maps PARTNER_DOCTYPE -> CUSTOMER_DOCTYPE,
     * using the parent package-tracking with the $origAppId and CNY#
     * @param string   $origAppId
     * @param string   $resourceType
     *
     * @return array
     */
    public static function getItemsListByAppId(string $origAppId, string $resourceType = PACKAGE_TYPE_APP): array
    {
        if (!FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('APPLICATION_MANAGEMENT')) {
            return [];
        }
        $result = [];
        $packageTrackingManager = Globals::$g->gManagerFactory->getManager('packagetracking');
        $params = ["selects"     => ['RECORDNO'],
                   "options"     => ["noDBSorts" => true,],
                   'nodbfilters' => true,
                   'filters'     => [[["RESOURCE_TYPE", '=', $resourceType],
                       ["PACKAGE_ID", '=', $origAppId],
                       ["COMPANYID", '=', GetMyCompany()],
                   ]],
        ];

        $response = $packageTrackingManager->GetList($params);
        if (is_array($response) && isset($response[0]['RECORDNO'])) {
            $packageTrackingKey = $response[0]['RECORDNO'];
            $transactionMapManager = Globals::$g->gManagerFactory->getManager('transactionmap');
            $params = ["selects"     => ['PARTNER_DOCTYPE', 'CUSTOMER_DOCTYPE'],
                       "options"     => ["noDBSorts" => true,],
                       'nodbfilters' => true,
                       'filters'     => [[
                           ["PARENT_ID", '=', $packageTrackingKey],
                       ]],
            ];
            $response = $transactionMapManager->GetList($params);
            if (!empty($response)) {
                foreach ($response as $value) {
                    $result[$value['PARTNER_DOCTYPE']] = $value['CUSTOMER_DOCTYPE'];
                }
            }
        }
        return $result;
    }
}
