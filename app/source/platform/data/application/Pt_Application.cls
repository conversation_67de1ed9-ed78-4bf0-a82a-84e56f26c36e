<?
/**
 * Application created by user or installed from Applications Library.
 * Table: PT_APPLICATION
 */
require_once "Pt_Includes.inc";

require_once "Pt_MenuManager.cls";
require_once "Pt_WebPageManager.cls";
require_once "Pt_PortalManager.cls";
require_once "Pt_SysRelsManager.cls";

require_once "NexusDOMHelper.cls";

class Pt_Application extends Pt_DataEntity
    implements Pt_IPickItem, Pt_IDefinition
{
    const APPLICATION_LABEL_IDX = 0;
    const APPLICATION_LABELS_INDEXES = [ self::APPLICATION_LABEL_IDX ];
    use Pt_I18NLabelsTrait;

    /* @var int $companyNo */
    private $companyNo;
    /* @var string $origId */
    private $origId;
    /* @var  string $appName */
    private $appName;
    /* @var int $orderNo */
    private $orderNo;
    /* @var bool $isSystem */
    private $isSystem;
    /* @var array $props */
    private $props;
    /* @var int[] $menuIds */
    private $menuIds;
    /* @var int[] $portalIds */
    private $portalIds;
    /* @var int[] $objDefIds */
    private $objDefIds;
    /* @var int[] $hfIds */
    private $hfIds;
    /* @var string[] $crNames */
    private $crNames;
    /* @var string[] $icrwNames */
    private $icrwNames;
    /* @var string $description */
    private $description;
    /* @var string $pubPacketId */
    private $pubPacketId;
    /* @var int $version */
    private $version;
    /* @var bool $isDeployed */
    private $isDeployed;
    /* @var bool $isPushAllowed */
    private $isPushAllowed;
    /* @var bool $isPullAllowed */
    private $isPullAllowed;
    /* @var bool $isHidden */
    private $isHidden;
    /* @var bool $isManaged */
    private $isManaged;
    /* @var Pt_CspConfig $cspConfig */
    private $cspConfig;

    /** @var array $crwComponents */
    private $crwComponents;
    /** @var array $dvComponents */
    private $dvComponents;
    /* @var array $transactionMap */
    private $transactionMaps = [];
    
    /**
     * @param int             $companyNo
     * @param int             $id
     * @param string          $origId
     * @param Pt_I18nLabels[] $customLabels
     * @param string          $appName
     * @param int             $orderNo
     * @param bool            $isSystem
     * @param array           $props
     * @param string          $description
     * @param string          $pubPacketId
     * @param int             $version
     * @param int             $createdBy
     * @param string          $createdAt
     * @param int             $updatedBy
     * @param string          $updatedAt
     */
    public function __construct($companyNo, $id, $origId, $customLabels,$appName, $orderNo, $isSystem, $props,
        $description, $pubPacketId, $version, $createdBy, $createdAt, $updatedBy, $updatedAt
    ) {
        parent::__construct($id, $createdBy, $createdAt, $updatedBy, $updatedAt);
        $this->companyNo = $companyNo;
        $this->origId = $origId;
        $this->setLabelIndexes(self::APPLICATION_LABELS_INDEXES);
        $this->setAllLabels($customLabels);
        $this->appName = $appName;
        $this->orderNo = $orderNo;
        $this->isSystem = pt_boolval($isSystem);
        $this->isManaged = pt_boolval($this->props[FIELD_IS_MANAGED] ?? false);
        $this->updateProps($props);
        $this->description = $description;
        $this->pubPacketId = $pubPacketId;
        $this->version = $version;
        $this->crwComponents = [];
        $this->dvComponents = [];
        $this->setTransactionMaps();

        $tokens = [ "IA.LIST", "IA.VIEW", "IA.ADD", "IA.EDIT", "IA.DELETE", "IA.IMPORT", "IA.TEMPLATES", "IA.CALENDAR",
                    "IA.MENU", "IA.OBJECTS", "IA.FIELDS", "IA.RELATIONSHIPS" ];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
    }

    /**
     * Update data.
     *
     * @param Pt_I18nLabels[] $customLabels
     * @param string          $appName
     * @param array           $props
     * @param string          $description
     * @param int             $updatedBy
     * @param string          $updatedAt
     */
    public function updateApplication($customLabels, $appName, $props, $description, $updatedBy, $updatedAt)
    {
        $this->appName = $appName;
        $this->setAllLabels($customLabels);
        $this->updateProps($props);
        $this->description = $description;
        parent::updateDataEntity($updatedBy, $updatedAt);
    }

    /**
     * Update properties.
     *
     * @param array $props
     */
    private function updateProps($props) 
    {
        $this->props = $props;
        $this->isDeployed = pt_boolval($this->props[FIELD_IS_DEPLOYED]) || $this->isSystem;
        if ( $this->isInstalled() ) {
            $this->isPushAllowed = pt_boolval($props[FIELD_IS_PUSH_ALLOWED] ?? false);
            $this->isPullAllowed = false;
        } else {
            $this->isPushAllowed = false;
            $this->isPullAllowed = pt_boolval($props[FIELD_IS_PULL_ALLOWED] ?? false);
        }
        $this->isHidden = pt_boolval($this->props[FIELD_IS_HIDDEN] ?? null);
        $this->isManaged = pt_boolval($this->props[FIELD_IS_MANAGED] ?? null);

        $this->menuIds = util_tokenizeInt($props[FIELD_MENU_IDS] ?? '');
        $this->portalIds = util_tokenizeInt($props[FIELD_PORTAL_IDS] ?? '');
        $this->objDefIds = util_tokenizeInt($props[FIELD_OBJ_DEF_IDS] ?? '');
        $this->hfIds = util_tokenizeInt($props[FIELD_HF_IDS] ?? '');
        $this->crNames = util_tokenize($props[FIELD_CR_NAMES] ?? '');
        $this->icrwNames = util_tokenize($props[FIELD_ICRW_NAMES] ?? '');

        $this->cspConfig = new Pt_CspConfig();
        $this->cspConfig->setFromProps($props, true);

        $this->cleanupObjDefIdsProps();
    }

    /**
     * Get company number.
     *
     * @return int
     */
    public function getCompanyNo() 
    {
        return $this->companyNo;
    }

    /**
     * Get original id of this item.
     *
     * @return string
     */
    public function getOriginalId() 
    {
        return isset($this->origId) && strpos($this->origId, '@') > 0
            ? $this->origId
            : GetMyCompany() . '@' . $this->id;
    }

    /**
     * VALUE attribute for UI SELECT list.
     *
     * @return string
     */
    public function getSelectValue() 
    {
        return strval($this->id);
    }

    /**
     * String representation
     *
     * @return string
     */
    public function __toString() 
    {
        return $this->getLabelForUserLocale(strval($this->appName) ?? "", self::APPLICATION_LABEL_IDX);
    }

    /**
     * Get order number for UI sorting
     *
     * @return int
     */
    public function getOrderNo() 
    {
        return $this->orderNo;
    }

    /**
     * Set order number
     *
     * @param int $orderNo
     */
    public function setOrderNo($orderNo) 
    {
        $this->orderNo = $orderNo;
    }

    /**
     * Is system application?
     *
     * @return bool
     */
    public function isSystem() 
    {
        return $this->isSystem;
    }

    /**
     * Is managed application (user cannot make changes)?
     *
     * @return bool
     */
    public function isManaged() 
    {
        return $this->isManaged;
    }

    /**
     * Is application deployed (active)?
     *
     * @return bool
     */
    public function isDeployed() 
    {
        return $this->isDeployed;
    }

    /**
     * Is application isPushAllowed (active)?
     *
     * @return bool
     */
    public function isPushAllowed()
    {
        if ( !$this->isInstalled() ) {
            return false;
        }
        return $this->isPushAllowed;
    }

    /**
     * Is application isPullAllowed (active)?
     *
     * @return bool
     */
    public function isPullAllowed()
    {
        if ( $this->isInstalled() ) {
            return false;
        }
        return $this->isPullAllowed;
    }

    /**
     * Is application hidden from menu bar?
     *
     * @return bool
     */
    public function isHidden() 
    {
        return $this->isHidden;
    }

    /**
     * Is installed from XML package?
     *
     * @return bool
     */
    public function isInstalled()
    {
        if ( FeatureConfigManagerFactory::getInstance()
                                        ->isFeatureEnabled('APPLICATION_MANAGEMENT') ) {
            $repositoryManager = Globals::$g->gManagerFactory->getManager('packageownership');
            $paramsGetList = [
                "nodbfilters" => true,
                "selects"     => [ 'OWNER_CNY' ],
                "options"     => [ "noDBSorts" => true ],
                "filters"     => [
                    [
                        [ "PACKAGE_ID", '=', $this->getOriginalId() ],
                    ],
                ],
            ];
            $data = $repositoryManager->GetList($paramsGetList, false, false);
            if ( $data && $data[0]['OWNER_CNY']  == GetMyCompany() ) {
                return false;
            }
        }
        
        $ownId = GetMyCompany() . '@' . $this->id;

        return $this->getOriginalId() != $ownId;
    }

    /**
     * Can application be deleted after cool off period?
     *
     * @return bool
     */
    public function canBeDeleted() 
    {
        if ($this->isDeployed || $this->isSystem) {
            return false; 
        }
        
        // For now disable delete if UDDs are present
        if ( $this->hasCustomDimensions() ) {
            return false;
        }
        
        return true;
    }

    /**
     * Can application be edited/view
     *
     * @return bool
     */
    public function canBeEdited()
    {
        if ( $this->version ) {
            return true;
        }

        return false;
    }

    /**
     * Can application be view
     *
     * @return bool
     */
    public function canBeViewed()
    {
        if ( $this->version ) {
            return true;
        }

        return false;
    }

    /**
     * @return bool
     */
    public function hasCustomDimensions() 
    {

        $objDefs = $this->getAppObjectDefs();

        /**
         * @var Pt_DataObjectDef $objDef
         */
        foreach( $objDefs as $objDef ) {
            if ( $objDef->isPlatform() && $objDef->isGLDimension() ) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Earliest date to delete after cool off period
     *
     * @return string
     */
    public function getEarliestDeleteDate() 
    {
        return GetGMTTimestampFromTimestamp(GetTimestampFromGMTTimestamp($this->updatedAt) + SEC_IN_HOUR);
    }

    /**
     * Get application's Properties.
     *
     * @return array
     */
    public function getProperties() 
    {
        return $this->props;
    }

    /**
     * @param array $props
     */
    public function setCspProperties($props)
    {
        $this->props = $props;

        $this->cspConfig = new Pt_CspConfig();
        $this->cspConfig->setFromProps($props, true);
    }

    /**
     * Get Intacct code for this app.
     *
     * @return string
     */
    public function getAppCode() 
    {
        return 'PT_'.$this->id;
    }

    /**
     * @param string $type
     *
     * @return Pt_Menu[]
     */
    public function getMenusByPageType($type = '')
    {
        $menus = [];
        foreach ( $this->menuIds as $menuId ) {
            $menu = Pt_MenuManager::getById($menuId);
            if ( $menu && ( !$type || $menu->getPageType() == $type ) ) {
                $menus[$menuId] = $menu;
            }
        }

        return $menus;
    }

    /**
     * Get application's menus as array. Uses the same structure as *.menu files
     *
     * @return array[]
     */
    public function getMenuArray() 
    {
        XACT_BEGIN(__METHOD__);
        try {
            $pol = (IsRolesEnabled() && !IsExternalUser()) ? new RoleCustomPolicy($this->getAppCode()) : new CustomPolicy($this->getAppCode(), GetMyUserid());
            $arr = [];
            $appCode = $this->getAppCode();
            foreach ($this->menuIds as $menuId) {
                $menu = Pt_MenuManager::getById($menuId);
                if ($menu == null) {
                    continue;
                }
                $objDef = $menu->getObjectDef();
                if ($objDef != null && !$objDef->isDeployed()) {
                    continue;
                }
                $arr2 = $menu->getMenuArray($appCode, $pol);
                if ($arr2) {
                    $arr[$menu->__toString()] = $arr2;
                }
            }
            if (!XACT_COMMIT(__METHOD__)) {
                throw new Pt_I18nException('PAAS-0114', "Error saving to the database.");
            }
        } catch (Exception $e) {
            XACT_ABORT(__METHOD__);
            throw $e;
        }

        return $arr;
    }

    /**
     * Get application's info as module array.
     *
     * @return array
     */
    public function getModuleArray() 
    {
        $appCode = $this->getAppCode();
        $arr = [];

        $arr['IAMODULEID'] = "1.".$appCode;
        $arr['NAME'] = $this->__toString();
        $arr['SYMBOL'] = $appCode;
        $arr['COMPANY'] = 'Intacct';
        $arr['PREINSTALLED'] = 'F';
        $arr['CONFIGURABLE'] = 'T';
        $arr['REMOVABLE'] = 'T';
        $arr['PERMKEY'] = $this->id;
        $arr['APP'] = 'AW';
        $arr['MODULEID'] = $appCode;
        $arr['ISCUSTOMMODULE'] = true;
        $arr['ID'] = $this->id;

        return $arr;
    }

    /**
     * Get array of possible permissions
     *
     * @param Pt_DataObjectDef[] $objDefs
     *
     * @return array
     */
    public function getPermissionsArray($objDefs) 
    {
        $appCode = $this->getAppCode();
        $arr = [];

        $arr2 = [];
        $arr5 = [];
        foreach ($objDefs as $objDef) {

            $objectName = null;
            $objDefId = $objDef->getId();

            // If the object defenition is not platform, we do not need to expose the permissions array.
            if($objDef->isPlatform()) {

                $opCode = $appCode . '/' . $objDefId . '/';
                $arr3 = [];
                $menus = [];

                foreach (Pt_MenuManager::getAllByDef($objDefId) as $menu) {
                    $menus[$menu->getPageType()] = $menu->getId();
                }
                $pages = [];
                foreach (Pt_WebPageManager::getByObjectDef($objDefId) as $page) {
                    $pages[$page->getPageType()] = $page->getId();
                }

                if (isset($menus[TYPE_LIST])) {
                    $arr3['list'] = ['eops' => [$opCode . $menus[TYPE_LIST]], 'display' => GT($this->textMap, "IA.LIST")];
                } else {
                    $arr3['list'] = ['eops' => [$opCode . $pages[TYPE_LIST]], 'display' => GT($this->textMap, "IA.LIST")];
                }

                if (isset($pages[TYPE_VIEW])) {
                    $arr3['readonly'] = ['eops' => [$opCode . $pages[TYPE_VIEW]], 'display' => GT($this->textMap, "IA.VIEW")];
                }

                if (isset($menus[TYPE_NEW])) {
                    $arr3['add'] = ['eops' => [$opCode . $menus[TYPE_NEW]], 'display' => GT($this->textMap, "IA.ADD")];
                } else {
                    $arr3['add'] = ['eops' => [$opCode . $pages[TYPE_NEW]], 'display' => GT($this->textMap, "IA.ADD")];
                }

                if (isset($pages[TYPE_EDIT])) {
                    $arr3['modify'] = ['eops' => [$opCode . $pages[TYPE_EDIT]], 'display' => GT($this->textMap, "IA.EDIT")];
                }

                $delId = $objDefId . 'd';
                $arr3['delete'] = ['eops' => [$opCode . $delId], 'display' => GT($this->textMap, "IA.DELETE")];

                if (isset($menus[TYPE_IMPORT])) {
                    $arr3['import'] = ['eops' => [$opCode . $menus[TYPE_IMPORT]], 'display' => GT($this->textMap, "IA.IMPORT")];
                }

                if (isset($menus[TYPE_TEMPLATES])) {
                    $arr3['templ'] = ['eops' => [$opCode . $menus[TYPE_TEMPLATES]], 'display' => GT($this->textMap, "IA.TEMPLATES")];
                }

                if (isset($menus[TYPE_CALENDAR])) {
                    $arr3['calendar'] = ['eops' => [$opCode . $menus[TYPE_CALENDAR]], 'display' => GT($this->textMap, "IA.CALENDAR")];
                }

                $objectName = $objDef->__toString();    // T 4104
                $arr2[$objDefId] = ['values' => $arr3, 'policykey' => $objDefId, 'text' => $objectName];

            }

            if ($objDef->isWorkflow()) {

                if ($objectName === null) {
                    $objectName = $objDef->__toString();  // T 4104
                }
                $localPlaceholderTokens = [
                    [
                        'id'           => "IA.ACTION_OBJECTNAME",
                        'placeHolders' => [
                            [ 'name' => 'OBJECT_NAME', 'value' => "$objectName" ],
                        ],
                    ],
                ];
                $txtMap = getIntlTextMap([], $localPlaceholderTokens);
                foreach (Pt_ActionManager::getByObjectDef($objDefId) as $a) {
                    $actionId = $a->getId();
                    $opCode = $appCode . '/' . $actionId . '/';
                    $arr5['A' . $actionId] = [ 'values' => [
                        'readonly' => [
                            'eops' => [ $opCode ],
                            'display' => GT($txtMap, "IA.ACTION_OBJECTNAME"),
                            'object' => $objectName ]
                        ],
                        'policykey' => $actionId,
                        'text' => $a->__toString()
                    ];
                }

            }
        }
        $arr['Objects'] = $arr2;
        $arr['Actions'] = $arr5;

        // Strore generic menus
        $arr4 = [];
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu != null && $menu->needPermissions()) {
                $opCode = $appCode.'/'.$menuId.'/'.$menuId;
                $arr4['M' . $menuId] = ['values' => ['menu' => ['eops'=> [$opCode], 'display'=>GT($this->textMap, "IA.MENU")]], 'policykey' => $menuId,'text' => $menu->__toString()];
            }
        }
        $arr['Menus'] = $arr4;

        return $arr;
    }

    /**
     * Get array of readonly permissions
     *
     * @param Pt_DataObjectDef[] $objDefs
     *
     * @return array[]
     */
    public function getReadonlyPermArray($objDefs) 
    {
        $arr = [];
        foreach ($objDefs as $objDef) {
            $arr[$objDef->getId()] = ['list', 'readonly'];
        }
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu != null && $menu->needPermissions()) {
                $arr['M' . $menuId] = ['menu'];
            }
        }
        return $arr;
    }

    /**
     * Get array permission keys
     *
     * @param Pt_DataObjectDef[] $objDefs
     *
     * @return int[]  obj def id keyed by op code
     */
    public function getPermissionsKeys($objDefs) 
    {
        $arr = [];
        foreach ($objDefs as $objDef) {
            $opCode = $objDef->__toString();
            $arr[$opCode] = $objDef->id;
        }
        return $arr;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription() 
    {
        return $this->description;
    }

    /**
     * Get version of installed or published app.
     *
     * @return int
     */
    public function getVersion() 
    {
        return $this->version;
    }

    /**
     * Get version as string.
     *
     * @return string
     */
    public function getVersionStr() 
    {
        return ($this->version > 0 ? strval($this->version) : '');
    }

    /**
     * Set version of installed or published app.
     *
     * @param int $version
     */
    public function setVersion($version) 
    {
        $this->version = $version;
    }

    /**
     * Get number of tabs.
     *
     * @return int
     */
    public function getNumTabs() 
    {
        return $this->menuIds==null ? 0 : count($this->menuIds);
    }

    /**
     * Get tab by sequential number.
     *
     * @param int $index
     *
     * @return Pt_Menu|null
     */
    public function getTab($index) 
    {
        return Pt_MenuManager::getById($this->menuIds[$index]);
    }

    /**
     * True, id this app includes given tab.
     *
     * @param int $tabId
     *
     * @return bool
     */
    public function hasTab($tabId) 
    {
        return in_array($tabId, $this->menuIds);
    }

    /**
     * True, id this app includes given standard menu.
     *
     * @param string $moduleID
     * @param string $menuName
     *
     * @return bool
     */
    public function hasStandard($moduleID, $menuName) 
    {
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu == null) {
                continue; 
            }
            if ($moduleID==$menu->getModuleID() && $menuName==$menu->__toString()) {
                return true; 
            }
        }
        return false;
    }

    /**
     * Get all tabs for this application
     *
     * @return Pt_Menu[]
     */
    public function getTabs() 
    {
        $arr = [];
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu != null) {
                $arr[] = $menu;
            }
        }
        return $arr;
    }

    /**
     * Get ids of related tabs as String
     *
     * @return string
     */
    public function getTabIdsStr() 
    {
        return util_converge($this->menuIds);
    }

    /**
     * Append Menu to application's list.
     *
     * @param Pt_Menu $menu
     */
    public function addTab(Pt_Menu $menu) 
    {
        $this->menuIds[] = $menu->getId();
        $this->props[FIELD_MENU_IDS] = $this->getTabIdsStr();
    }

    /**
     * Remove Menu from application's list.
     *
     * @param Pt_Menu $menu
     *
     * @return bool  if the menu was present in the app and removed
     */
    public function removeTab($menu) 
    {
        if ($menu != null) {
            $menuParamId = $menu->getId();
            for ($k = 0, $c = count($this->menuIds); $k < $c; $k++) {
                $menuId = $this->menuIds[$k] ?? 0;
                if ($menuParamId == $menuId) {
                    unset($this->menuIds[$k]);
                    $this->props[FIELD_MENU_IDS] = $this->getTabIdsStr();
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Get assigned and related object definitions
     *
     * @return Pt_DataObjectDef[]
     */
    public function getObjectDefs()
    {
        $arr = $this->getAppObjectDefs();
        usort($arr, "Pt_DataObjectDef::compareByDisplayName");
        return $arr;
    }

    /**
     * Get only assigned object definitions
     *
     * @return Pt_DataObjectDef[]
     */
    public function getAssignedObjectDefs() 
    {
        $arr = [];
        foreach ($this->objDefIds as $objDefId) {
            $objDef = Pt_DataObjectDefManager::getById($objDefId);
            if ($objDef != null) {
                $arr[] = $objDef;
            }
        }
        return $arr;
    }

    /**
     * Get ids of assigned standard definitions
     *
     * @return string[]
     */
    public function getStObjectDefIds() 
    {
        $props = $this->processDoctypes($this->props[FIELD_ST_DEF_IDS] ?? '');
        return util_tokenize($props ?? null);
    }

    /**
     * Get assigned standard definitions
     *
     * @return Pt_StdDataObjectDef[]
     */
    public function getStObjectDefs()
    {
        $arr = [];
        foreach ( $this->getStObjectDefIds() as $stDefId ) {
            $objDef = Pt_DataObjectDefManager::getStByEntityName($stDefId);
            if ( $objDef != null ) {
                $arr[$objDef->getId()] = $objDef;
            }
        }

        return $arr;
    }

    /**
     * @return Pt_DataObjectDef[]
     */
    public function getAllObjects()
    {
        return $this->getStObjectDefs() + $this->getAppObjectDefs();
    }

    /**
     * True, id this app includes given custom object definition.
     *
     * @param int $objDefId
     *
     * @return bool
     */
    public function hasObjectDef($objDefId) 
    {
        if ($objDefId <= 0) {
            return false; 
        }

        return in_array($objDefId, $this->objDefIds);
    }

    /**
     * get the app's obj def ids
     *
     * @return int[] the application's obj def ids
     */
    public function getObjDefIds()
    {
        return $this->objDefIds;
    }

    /**
     * @param int $stdObjDefId
     *
     * @return string
     */
    private function getStObjSelectValue($stdObjDefId)
    {
        $stdObjDef = Pt_DataObjectDefManager::getById($stdObjDefId);
        if ( ! $stdObjDef instanceof Pt_StdDataObjectDef ) {
            return '';
        }

        return $stdObjDef->getSelectValue();
    }

    /**
     * True, id this app includes given standard object definition.
     *
     * @param int $stdObjDefId
     *
     * @return bool
     */
    public function hasStObjectDef($stdObjDefId)
    {
        $objSelect = $this->getStObjSelectValue($stdObjDefId);
        if ( $objSelect == '' ) {
            return false;
        }

        $stdObjDefsSelectValues = $this->getStObjectDefIds();

        return in_array($objSelect, $stdObjDefsSelectValues);
    }

    /**
     * True, id this app includes given scm definition.
     *
     * @param int $stdObjDefId
     *
     * @return bool
     */
    public function hasScmObjectDef($stdObjDefId)
    {
        $objSelect = $this->getStObjSelectValue($stdObjDefId);
        if ( $objSelect == '' ) {
            return false;
        }

        if ( strpos($objSelect, '.') !== false ) {
            $objSelectPieces = explode('.', $objSelect);
            $stdObjDefsSelectValues = $this->getStObjectDefIds();
            foreach ( $stdObjDefsSelectValues as $value ) {
                if ( $value == $objSelectPieces[0] ) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * True, id this app includes tab with given object definition.
     *
     * @param int $objDefId
     *
     * @return bool
     */
    public function hasObjectTab($objDefId) 
    {
        if ($objDefId <= 0) {
            return false; 
        }
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu == null) {
                continue; 
            }
            if ($menu->getObjectDefId() == $objDefId) {
                return true; 
            }
        }
        return false;
    }

    /**
     * fetch a list of all the tab obj def ids
     *
     * @return int[] the obj def ids of the menus
     */
    public function getAllTabObjDefIds()
    {
        $ret = [];
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu) {
                $objDefId = $menu->getObjectDefId();
                if ($objDefId > 0) {
                    $ret[] = $objDefId;
                }
            }
        }
        return $ret;
    }

    /**
     * Get ids of related object definitions
     *
     * @return string
     */
    public function getObjectDefIds() 
    {
        return util_converge($this->objDefIds);
    }

    /**
     * Append related object definition
     *
     * @param Pt_DataObjectDef $objDef
     */
    public function addObjectDef(Pt_DataObjectDef $objDef) 
    {
        $this->objDefIds[] = $objDef->getId();
        $this->props[FIELD_OBJ_DEF_IDS] = $this->getObjectDefIds();
    }

    /**
     * Remove related object definition
     *
     * @param Pt_DataObjectDef $objDef
     *
     * @return bool
     */
    public function removeObjectDef(Pt_DataObjectDef $objDef) 
    {
        for ($k=0,$c=count($this->objDefIds); $k<$c; $k++) {
            $objDefId = $this->objDefIds[$k];
            if ($objDefId == $objDef->getId()) {
                unset($this->objDefIds[$k]);
                $this->props[FIELD_OBJ_DEF_IDS] = $this->getObjectDefIds();
                return true;
            }
        }
        return false;
    }

    /**
     * Get related portals
     *
     * @return Pt_Portal[]
     */
    public function getPortals() 
    {
        $arr = [];
        foreach ($this->portalIds as $id) {
            $portal = Pt_PortalManager::getById($id);
            if ($portal != null) {
                $arr[] = $portal;
            }
        }
        return $arr;
    }

    /**
     * Get ids of related portals
     *
     * @return string
     */
    public function getPortalIds() 
    {
        return util_converge($this->portalIds);
    }

    /**
     * True, id this app includes given portal.
     *
     * @param int $portalId
     *
     * @return bool
     */
    public function hasPortal($portalId) 
    {
        if ($portalId <= 0) {
            return false; 
        }

        $ret = in_array($portalId, $this->portalIds);

        return $ret;

    }

    /**
     * Append related portal
     *
     * @param Pt_Portal $portal
     */
    public function addPortal(Pt_Portal $portal) 
    {
        $this->portalIds[] = $portal->getId();
        $this->props[FIELD_PORTAL_IDS] = $this->getPortalIds();
    }

    /**
     * Remove related portal
     *
     * @param Pt_Portal $portal
     *
     * @return bool
     */
    public function removePortal(Pt_Portal $portal) 
    {
        for ($k=0,$c=count($this->portalIds); $k<$c; $k++) {
            $portalId = $this->portalIds[$k];
            if ($portalId == $portal->getId()) {
                unset($this->portalIds[$k]);
                $this->props[FIELD_PORTAL_IDS] = $this->getPortalIds();
                return true;
            }
        }
        return false;
    }

    /**
     * Get associated hosted files
     *
     * @return Pt_HostedFile[]
     */
    public function getHostedFiles() 
    {
        $arr = [];
        foreach ($this->hfIds as $id) {
            $hf = Pt_HostedFileManager::getById($id);
            if ($hf != null) {
                $arr[]= $hf;
            }
        }
        return $arr;
    }

    /**
     * True, id this app includes given hosted file.
     *
     * @param int $hfId
     *
     * @return bool
     */
    public function hasHostedFile($hfId) 
    {
        if ($hfId <= 0) {
            return false; 
        }

        $ret = in_array($hfId, $this->hfIds);
        return $ret;
    }

    /**
     * Get ids of related hosted files ids
     *
     * @return string
     */
    public function getHostedFilesIds() 
    {
        return util_converge($this->hfIds);
    }

    /**
     * Append related hosted file
     *
     * @param Pt_HostedFile $hf
     */
    public function addHostedFile(Pt_HostedFile $hf) 
    {
        if ($this->hfIds == null) {
            $this->hfIds = [];
        }
        $this->hfIds[] = $hf->getId();
        $this->props[FIELD_HF_IDS] = $this->getHostedFilesIds();
    }

    /**
     * Remove related hosted file
     *
     * @param Pt_HostedFile $hf
     *
     * @return bool
     */
    public function removeHostedFile(Pt_HostedFile $hf) 
    {
        for ($k=0,$c=count($this->hfIds); $k<$c; $k++) {
            $id = $this->hfIds[$k];
            if ($id == $hf->getId()) {
                unset($this->hfIds[$k]);
                $this->props[FIELD_HF_IDS] = $this->getHostedFilesIds();
                return true;
            }
        }
        return false;
    }


    /**
     * Get names of custom reports assigned to the application
     *
     * @return string[]
     */
    public function getReportNames()
    {
        $arr = [];
        if ( is_array($this->crNames ?? null) ) {
            foreach ( $this->crNames as $repId ) {
                if ( $repId ) {
                    $arr[$repId] = $repId;
                }
            }
        }
        foreach ( $this->menuIds as $menuId ) {
            $menu = Pt_MenuManager::getById($menuId);
            if ( !$menu ) {
                continue;
            }
            $reportId = $menu->getReportId();
            if ( (int) $reportId > 0 && $menu->getPageType() === TYPE_REPORT ) {
                $reportName = Pt_StandardUtil::getReportNameFromID($reportId, true);
                if ( $reportName ) {
                    $arr[$reportName] = $reportName;
                }
            }
        }
        
        return array_values($arr);
    }

    /**
     * Get names of interactive custom reports assigned to the application
     *
     * @return string[]
     */
    public function getICRWNames()
    {
        $arr = [];
        if ( is_array($this->icrwNames ?? null) ) {
            foreach ( $this->icrwNames as $repId ) {
                if ( $repId ) {
                    $arr[$repId] = $repId;
                }
            }
        }
        foreach ( $this->menuIds as $menuId ) {
            $menu = Pt_MenuManager::getById($menuId);
            if ( !$menu ) {
                continue;
            }
            $reportId = $menu->getReportId();
            $properties = $menu->getProperties();
            if ( (int) $reportId > 0
                 && ( $menu->getPageType() === TYPE_CRW || $menu->getPageType() === TYPE_DV
                      || ( $properties['reportType'] ?? null ) === Reporting::CRW_REPORT_TYPE
                      || ( $properties['reportType'] ?? null ) === Reporting::DV_REPORT_TYPE ) ) {
                $reportName = Pt_StandardUtil::getReportNameFromID($reportId, false);
                if ( $reportName ) {
                    $arr[$reportName] = $reportName;
                }
            }
        }
        
        return array_values($arr);
    }
    
    /**
     * Check if a report belongs to the application
     *
     * @param array $report
     *
     * @return bool
     */
    public function hasCustomReport($report)
    {
        $id = $report['NAME'];
        
        if ( ( ( $report['TYPE'] ?? '' ) === Reporting::CRW_REPORT_TYPE )
             || ( ( $report['REPORTTYPE'] ?? '' ) === Reporting::CRW_REPORT )
             || ( ( $report['TYPE'] ?? '' ) === Reporting::DV_REPORT_TYPE )
             || ( ( $report['REPORTTYPE'] ?? '' ) === Reporting::DV_REPORT ) ) {
            $reports = $this->getICRWNames();
        } else {
            $reports = $this->getReportNames();
        }
    
        return $id && in_array($id, $reports);
    }
    
    /**
     * Get reports assigned to application
     *
     * @return array[]
     */
    public function getReports()
    {
        $arr = [];
        $crNames = $this->getReportNames();
        $icrwNames = $this->getICRWNames();
        if ( !$crNames && !$icrwNames ) {
            return $arr;
        }
        
        $customReports = Pt_StandardUtil::getReportsList(true);
        foreach ( $customReports as $cr ) {
            if ( in_array($cr['NAME'], $crNames) ) {
                $arr[] = $cr;
            }
        }
        
        $interactiveCustomReports = Pt_StandardUtil::getCrwReports();
        foreach ( $interactiveCustomReports as $icr ) {
            if ( in_array($icr['NAME'], $icrwNames) ) {
                if ( ( $icr['REPORTTYPE'] ?? '' ) === Reporting::CRW_REPORT ) {
                    $icr['TYPE'] = Reporting::CRW_REPORT_TYPE;
                    $arr[] = $icr;
                } else if ( ( $icr['REPORTTYPE'] ?? '' ) === Reporting::DV_REPORT ) {
                    $icr['TYPE'] = Reporting::DV_REPORT_TYPE;
                    $arr[] = $icr;
                }
            }
        }
        
        return $arr;
    }

    /**
     * Get ids of seed data
     *
     * @return int[]
     */
    public function getSeedDataIds() 
    {
        return Pt_SysRelsManager::getRightIds(REL_APP_OBJECT_ID, $this->id);
    }


    /**
     * Generate XML and append it to Packet
     *
     * @param Pt_PacketProxy $proxy
     */
    public function toPacket(Pt_PacketProxy $proxy)
    {
        $proxy->putId($this->id);
        $coreDefs = $proxy->getCoreDefs();
        
        $uddExpImpCfg = GetValueForIACFGProperty('IA_UDD_DISABLE_IMPORT_EXPORT');
        foreach ($coreDefs as $objDef) {
            if ( $uddExpImpCfg == 'T' && $objDef->isGLDimension() ) {
                throw new Pt_I18nException('PAAS-0115',
                                           "This Application contains User Defined Dimensions and cannot be exported.");
            }
        }

        $stObjects = $this->getStObjectDefs();
        unset($proxy->buff);

        $proxy->buff = "<Application";
        xml_appendAttribute($proxy->buff, "id", $this->id);
        xml_appendAttribute($proxy->buff, "origId", $this->getOriginalId());
        xml_appendAttribute($proxy->buff, "orderNo", $this->orderNo);
        xml_appendBoolAttribute($proxy->buff, "isSystem", $this->isSystem);
        xml_appendAttribute($proxy->buff, "version", $proxy->getVersion());
        // never package isPushAllowed, this should be an individual choice of each tennant
        xml_appendAttribute($proxy->buff, "companyNo", $this->companyNo);
        $proxy->buff .= " >\n";
        xml_appendElement($proxy->buff, "CustomLabels",
                          Pt_I18nLabelsController::formatLabelsForInstaller($this->getAllLabels()));
        xml_appendElement($proxy->buff, "DisplayName", $proxy->getAppName());
        xml_appendElement($proxy->buff, "Description", $this->description);


        // Make sure that we don't export any deleted objDefIds
        $this->cleanupObjDefIdsProps();

        $props = array_merge([], $this->props);
        if ($proxy->isManaged()) {
            $props[FIELD_IS_MANAGED] = true;
        }
        xml_appendArrayElement($proxy->buff, "Props", $props);

        $proxy->buff .= "<DependentDefs>\n";
        foreach ($proxy->getDependentDefs() as $objDef) {
            $objDef->toShortXML($proxy->buff);
        }
        unset($objDef);
        $proxy->buff .= "</DependentDefs>\n";

        $coreDefs = $proxy->getCoreDefs();
        $misingStdRels = $this->getMissingStdRelationships($coreDefs, $stObjects);
        $proxy->buff .= "<DataObjectDefs>\n";
        foreach ($coreDefs as $objDef) {
            $objDef->checkState();
            $objDef->toPacket($proxy);
        }
        unset($objDef);

        $std2stdRels = null;
        foreach ($stObjects as $stDef) {
            $stDef->checkState();
            $stDef->toPacket($proxy);

            if ( $stDef->std2stdRels ) {
                if ( !$std2stdRels ) {
                    $std2stdRels = $stDef->std2stdRels;
                }
                else {
                    $std2stdRels = $std2stdRels + $stDef->std2stdRels;
                }
            }
        }

        if ( $std2stdRels ) {
            $relsStr = "";
            $missingStdObjDefs = false;
            foreach ( $std2stdRels as $s2sRel ) {
                $objDef1 = Pt_DataObjectDefManager::getById($s2sRel["objDefId1"]);
                $objDef2 = Pt_DataObjectDefManager::getById($s2sRel["objDefId2"]);
                if ( !$this->hasStObjectDef($objDef1->getId()) || !$this->hasStObjectDef($objDef2->getId()) ) {
                    $missingStdObjDefs = true;
                    // I18N: TODO
                    $localPlaceholderTokens = [
                        [
                            'id'           => "IA.OBJDEF_IS_RELATED_TO_OBJDEF",
                            'placeHolders' => [
                                [ 'name' => 'OBJDEF1', 'value' => $objDef1->getSingularName() ],
                                [ 'name' => 'OBJDEF2', 'value' => $objDef2->getSingularName() ]
                            ],
                        ],
                    ];
                    $txtMap = getIntlTextMap([], $localPlaceholderTokens);
                    $relsStr .= GT($txtMap, "IA.OBJDEF_IS_RELATED_TO_OBJDEF") . "; ";
                }
            }

            if ( $missingStdObjDefs ) {
                throw new Pt_I18nException('PAAS-0116',
                                           "You're attempting to export an application that contains one or more " .
                                           "standard-to-standard dimension relationships:$relsStr " .
                                           "Please add the missing standard objects, then try again. ",[ 'RELSSTR' => $relsStr ]);
            }
        }

        unset($stDef);
        // Include std relationships even if they weren't part of the app  #13013
        foreach ( $misingStdRels as $missingInfo ) {
            /* @var Pt_StdDataObjectDef $stdDef */
            $stdDef = $missingInfo[0];
            /* @var int[] $missingRelIds */
            $missingRelIds = $missingInfo[1];
            $stdDef->toRelationshipPacket($proxy, $missingRelIds);
        }
        $proxy->buff .= "</DataObjectDefs>\n";

        $proxy->buff .= "<Menus>\n";
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu == null) {
                continue; 
            }
            $menu->toPacket($proxy);
        }
        unset($menuId);
        $proxy->buff .= "</Menus>\n";

        $pages = $this->getPages(array_merge($coreDefs, $stObjects));
        $proxy->buff .= "<WebPages>\n";
        foreach ($pages as $page) {
            $page->toPacket($proxy);
        }
        unset($page);
        $proxy->buff .= "</WebPages>\n";

        $proxy->buff .= "<Portals>\n";
        foreach ($this->getPortals() as $portal) {
            $portal->toPacket($proxy);
        }
        unset($portal);
        $proxy->buff .= "</Portals>\n";
        
        $hostedFiles = $this->getHostedFiles();
        if ($hostedFiles) {
            $proxy->buff .= "<HostedFiles>\n";
            foreach ($hostedFiles as $hf) {
                $hf->toPacket($proxy);
            }
            unset($hf);
            $proxy->buff .= "</HostedFiles>\n";
        }
        
        $reports = $this->getReports();
        if ($reports) {
            $proxy->buff .= "<customReports>\n";
            foreach ($reports as $r) {
                if ($r['TYPE'] === Reporting::CRW_REPORT_TYPE || $r['TYPE'] === Reporting::DV_REPORT_TYPE) {
                    //case crw export crw report
                    $this->crwToPacket($r, $proxy);
                } else {
                    $this->reportToPacket($r, $proxy);
                }

            }
            unset($r);
            $proxy->buff .= "</customReports>\n";
        }

        $seedRecs = [];
        foreach ($this->getSeedDataIds() as $id) {
            $data = Pt_DataObjectManager::getById2($id);
            if ($data != null && $proxy->hasObjectDef($data->getObjectDef())) {
                $seedRecs[] = $data;
            }
        }
        if ($seedRecs) {
            $proxy->buff .= "<SeedRecords>\n";
            foreach ($seedRecs as $data) {
                $data->toXML($proxy->buff);
            }
            unset($data);
            $proxy->buff .= "</SeedRecords>\n";
        }

        $proxy->buff .= "<Version>2</Version>\n";
        $proxy->buff .= "<MetaData>\n";
        xml_appendElement($proxy->buff, "PublishedFrom", Profile::getCompanyCacheProperty('company', 'TITLE'));
        xml_appendElement($proxy->buff, "PublishedBy", Profile::getProperty('login'));
        $proxy->buff .= "</MetaData>\n";

        $proxy->buff .= "</Application>\n";

        //  Clean out any control characters (they break the XML).
        $proxy->buff = isl_str_clean($proxy->buff);

        $packageIssue = $this->spiPackageError($proxy->buff);
        if ( $packageIssue ) {
            throw new Pt_I18nException(
                'PAAS-0836', "Failed Publishing Platform Application.", [],
                "You are attempting to publish an application with standard object page changes that are outdated.", [],
                "Please edit, verify any changes and components you want to keep, and re-save the following pages: "
                . implode(', ', $packageIssue) . '.',
                [ 'PAGES' => implode(', ', $packageIssue) ]
            );
        }
    }

    /**
     * @param string $buff
     *
     * @return string[]
     */
    function spiPackageError($buff)
    {
        $error = [];
        $root = xml_parseString($buff);
        foreach ( xml_getChildernElements(xml_getChildElement($root, "WebPages"), "WebPage") as $element ) {
            $objDefId = xml_getIntAttribute($element, "objDefId");
            $objDef = Pt_DataObjectDefManager::getById($objDefId);
            if ( $objDef == null || $objDef->isPlatform() ) {
                continue;
            }
            $pageName = xml_getChildValue($element, "PageName");
            if (isset($error[$pageName])) {
                continue;
            }
            $sections = xml_getChildernElements(xml_getChildElement($element, "PageSections"), "PageSection");
            foreach ( $sections as $element2 ) {
                $props = xml_getPropsElementValue($element2, "Props");
                if (isset($props['viewTabPlatIndex']) || $props['viewTabNo'] < 0
                    || isset($props['viewTabTitle'])
                    || pt_boolval($props['isDefault'])
                ) {
                    ;
                } else {
                    $error[$pageName] = $objDef->getObjectDefName() . ' - ' . $pageName;
                }
            }
        }

        return $error;
    }
    
    /**
     * Attach report to XML buffer.
     *
     * @param array  $r
     * @param Pt_PacketProxy $proxy
     */
    private function reportToPacket($r, $proxy)
    {
        if ( isset($r['ROOT']) && $r['ROOT'] !== "" ) {
            $objName = $r['ROOT'];
            if ( str_starts_with($objName, 'PT_') ) {
                $objName = explode('PT_', $objName)[1];
            }
            $objDef = Pt_DataObjectDefManager::getByName($objName);
            if ( $objDef != null && $objDef->isPlatform() ) {
                if ( !$proxy->hasId($objDef->getId()) ) {
                    return;
                }
            }
        }
        $proxy->buff .= "<customReport \n";
        xml_appendAttribute($proxy->buff, "id", $r['RECORDNO']);
        $proxy->buff .= " >\n";

        xml_appendElement($proxy->buff, "name", $r['NAME']);
        xml_appendElement($proxy->buff, "description", $r['DESCRIPTION']);
        xml_appendElement($proxy->buff, "type", $r['TYPE']);
        xml_appendElement($proxy->buff, "module", $r['MODULEKEY']);
        xml_appendElement($proxy->buff, "root", isl_strtoupper($r['ROOT']));
        xml_appendElement($proxy->buff, "documentType", $r['DOCTYPE']);
        xml_appendElement($proxy->buff, "deploy", $r['DEPLOY']);
        xml_appendElement($proxy->buff, "status", $r['STATUS']);

        xml_appendElement($proxy->buff, "queryDef", base64_encode($r['QUERYDEF']));
        if ($r['RUNTIMEPARAMS']) {
            xml_appendElement($proxy->buff, "runtimeParams", base64_encode($r['RUNTIMEPARAMS']));
        }
        if ($r['FILTERS']) {
            xml_appendElement($proxy->buff, "filters", base64_encode($r['FILTERS']));
        }
        if ($r['ADVANCEDFILTER']) {
            xml_appendElement($proxy->buff, "advancedFilters", base64_encode($r['ADVANCEDFILTER']));
        }
        
        //eppp_p("reportToPacket:");
        //eppp_p($r);
        //diefl();

        $proxy->buff .= "</customReport>\n";
    }


    /**
     * Attach report to XML buffer.
     *
     * @param array  $r
     * @param Pt_PacketProxy $proxy
     */
    private function crwToPacket($r, $proxy)
    {
        if ( isset($r['ROOT']) && $r['ROOT'] !== "" ) {
            $objName = $r['ROOT'];
            if ( str_starts_with($objName, 'PT_') ) {
                $objName = explode('PT_', $objName)[1];
            }
            $objDef = Pt_DataObjectDefManager::getByName($objName);
            if ( !$objDef && $objDef->isPlatform() ) {
                if ( !$proxy->hasId($objDef->getId()) ) {
                    return;
                }
            }
        }
        $reporting = new Reporting();
        if ($r['REPORTTYPE'] === Reporting::DV_REPORT) {
            $xmlData = $reporting->exportItemDefinition($r['REPORTID'], true, true);
        } else {
            $xmlData = $reporting->exportItemDefinitionXML($r['REPORTID']);
            $domDoc = new DOMDocument('1.0');
            $domDoc->loadXML($xmlData);
            $xpath = new DOMXPath($domDoc);
            //filters
            $filterNodes = $xpath->evaluate("//saw:report/saw:criteria/saw:filter/sawx:expr");
            foreach ($filterNodes as $element) {
                /* @var DOMElement $element */
                $type = $element->getAttribute('xsi:type');
                if ($type === Reporting::SAVED_FILTER) {
                    //check if already included as component
                    $path = $element->getAttribute('path');
                    if ((substr($path, 0, strlen(Reporting::INTACCT_LIBRARY_FOLDER))
                         !== Reporting::INTACCT_LIBRARY_FOLDER)
                        && ( ! ($this->crwComponents[$path]))) {
                        $this->crwComponents[$path] = true;
                        $component = $reporting->packageCrwComponent($path, $type);
                        $proxy->buff .= $component;
                    }
                }
            }

            //saved columns
            $columnNodes = $xpath->query("//saw:report/saw:criteria/saw:columns/saw:column[@xsi:type='" .
                                         Reporting::SAVED_REGULAR_COLUMN_REF . "']/@path");
            foreach ($columnNodes as $element) {
                $path = $element->nodeValue;
                if ( ! ($this->crwComponents[$path])) {
                    $this->crwComponents[$path] = true;
                    $component = $reporting->packageCrwComponent($path, Reporting::SAVED_REGULAR_COLUMN_REF);
                    $proxy->buff .= $component;
                }
            }
        }
        $proxy->buff .= "<customReport \n";
        xml_appendAttribute($proxy->buff, "id", $r['REPORTID']);
        $proxy->buff .= " >\n";
        xml_appendElement($proxy->buff, "name", $r['NAME']);
        xml_appendElement($proxy->buff, "description", $r['DESCRIPTION']);
        if ($r['REPORTTYPE'] === Reporting::CRW_REPORT) {
            xml_appendElement($proxy->buff, "type", Reporting::CRW_REPORT_TYPE);
        } elseif ($r['REPORTTYPE'] === Reporting::DV_REPORT) {
            xml_appendElement($proxy->buff, "type", Reporting::DV_REPORT_TYPE);
        }
        xml_appendElement($proxy->buff, "module", $r['MODULE']);
        xml_appendElement($proxy->buff, "status", $r['STATUS']);
        xml_appendElement($proxy->buff, "deploy", 'true');
        xml_appendElement($proxy->buff, "queryDef",base64_encode($xmlData));
        $proxy->buff .= "</customReport>\n";
    }


    /**
     * Get list of pages associated with this application.
     *
     * @param Pt_DataObjectDef[] $objDefs
     *
     * @return Pt_PageStub[]
     */
    public function getPages($objDefs) 
    {
        $map = [];

        foreach ($objDefs as $objDef) {
            if ($objDef==null) {
                continue; 
            }
            $pages = Pt_WebPageManager::getByObjectDef($objDef->getId());
            foreach ($pages as $page) {
                if ($page == null) {
                    continue; 
                }
                $map[$page->getId()] = $page;
            }
        }

        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu == null) {
                continue; 
            }
            $page = $menu->getPageStub();
            if ($page != null) {
                $map[$page->getId()] = $page; 
            }

            $subMenus = Pt_MenuManager::getByParent($menuId);
            foreach ($subMenus as $menu2) {
                $page = $menu2->getPageStub();
                if ($page != null) {
                    $map[$page->getId()] = $page; 
                }
            }
        }

        return $map;
    }

    /**
     * Get all explicity associated object definitions
     *
     * @return Pt_DataObjectDef[]
     */
    public function getExplicitObjectDefs() 
    {
        $map = [];

        // Add explicitly associated objects
        foreach ($this->objDefIds as $id) {
            $objDef = Pt_DataObjectDefManager::getById($id);
            if ($objDef != null) {
                $map[$objDef->getId()] = $objDef; 
            }
        }

        return array_values($map);
    }

    /**
     * Get all object definitions (explicitly and implicitly associated)
     *
     * @return Pt_DataObjectDef[] map by id of object defs assigned to the application
     */
    public function getAppObjectDefs()
    {
        $map = [];

        // Add base definitions
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu == null) {
                continue; 
            }
            $objDef = $menu->getObjectDef();
            if ($objDef != null) {
                $map[$objDef->getId()] = $objDef; 
            }

            $subMenus = Pt_MenuManager::getByParent($menuId);
            foreach ($subMenus as $menu2) {
                $objDef = $menu2->getObjectDef();
                if ($objDef != null) {
                    $map[$objDef->getId()] = $objDef; 
                }
            }
        }
        
        // Add explicitly associated objects: moved before relationships (Bug 35987)
        foreach ($this->objDefIds as $id) {
            $objDef = Pt_DataObjectDefManager::getById($id);
            if ($objDef != null) {
                $map[$id] = $objDef;
            }
        }

        // Add relationships definitions
        foreach ( $map as $objDef1Id => $objDef1) {
            foreach (Pt_RelationshipDefManager::getByObjectDef($objDef1Id) as $rel) {
                $objDef2 = $rel->getOtherObjectDef($objDef1Id);
                if ($objDef2 != null) {
                    $map[$objDef2->getId()] = $objDef2; 
                }
            }
        }

        return $map;
    }
    
    /**
     * Get reports related objects definitions
     *
     * @return Pt_DataObjectDef[] map by id of object defs from reports
     */
    public function getReportAppObjectDefs()
    {
        $map = [];
        
        $reports = $this->getReports();
        foreach ( $reports as $report ) {
            if ( is_array($report) && isset($report['ROOT']) && $report['ROOT'] ) {
                $objName = $report['ROOT'];
                if ( str_starts_with($objName, 'PT_') ) {
                    $objName = explode('PT_', $objName)[1];
                }
                $objDef = Pt_DataObjectDefManager::getByName($objName);
                if ( isset($objDef) && ( $objDef instanceof Pt_DataObjectDef ) ) {
                    $map[$objDef->getId()] = $objDef;
                }
            }
        }
        
        return $map;
    }
    
    /**
     * Get all related objects definitions
     *
     * @return Pt_DataObjectDef[] map by id of object defs from app + reports
     */
    public function getAppObjectDefsIncludingReports()
    {
        return $this->getAppObjectDefs() + $this->getReportAppObjectDefs();
    }
    

    /**
     * Get URL to first (default) page
     *
     * @return string|null
     */
    public function getDefaultPageURL() 
    {
        foreach ($this->menuIds as $menuId) {
            $menu = Pt_MenuManager::getById($menuId);
            if ($menu == null) {
                continue; 
            }
            return $menu->getPageURL();
        }
        return null;
    }


    /**
     * Compares two applications by order.
     *
     * @param Pt_Application $app1
     * @param Pt_Application $app2
     *
     * @return int
     */
    public static function compareByOrder($app1, $app2) 
    {
        $n1 = ($app1==null ? -1 : $app1->getOrderNo());
        $n2 = ($app2==null ? -1 : $app2->getOrderNo());
        return ($n1 < $n2 ? -1 : ($n1 > $n2 ? 1 : 0));
    }

    /**
     * Validate required input parameters.
     *
     * @param string $appName
     */
    public static function validate($appName)
    {
        if (strlen($appName) == 0) {
            throw new Pt_I18nException('PAAS-0117', "Application name is null or empty");
        }
    }

    /**
     * @return array
     */
    public function getCspProps()
    {
        return $this->cspConfig->getCspProps();
    }

    /**
     * @param CspPolicy $pol
     */
    public function setCspHeaders(CspPolicy $pol)
    {
        $this->cspConfig->setCspHeaders($pol);
    }

    /**
     * Get a list of the standard object relationships that aren't
     * included in the app
     *
     * @param Pt_DataObjectDef[]    $coreDefs
     * @param Pt_StdDataObjectDef[] $stdDefs
     *
     * @return array
     */
    private function getMissingStdRelationships($coreDefs, $stdDefs) 
    {

        $ret = [];

        $stdDefIds = [];
        foreach ( $stdDefs as $stdDef ) {
            $stdDefIds[$stdDef->getId()] = true;
        }

        foreach ( $coreDefs as $objDef ) {

            $objDefId = $objDef->getId();
            $rels = Pt_RelationshipDefManager::getByObjectDef($objDefId);
            foreach ( $rels as $rel ) {
                $otherDef = $rel->getOtherObjectDef($objDefId);
                if ( $otherDef && ! $otherDef->isPlatform() ) {
                    $otherDefId = $otherDef->getId();
                    if ( ! isset($stdDefIds[$otherDefId])) {
                        if ( ! isset($ret[$otherDefId]) ) {
                            $ret[$otherDefId] = [$otherDef, []];
                        }
                        $ret[$otherDefId][1][$rel->getId()] = 1;
                    }
                }
            }

        }

        return $ret;

    }

    /**
     * ensure that the app's obj def ids doesn't include any invalid objects.
     */
    private function cleanupObjDefIdsProps() 
    {
        if (Pt_Cache::isLoading()) {
            return;
        }

        $objDefIds = util_tokenizeInt($this->props[FIELD_OBJ_DEF_IDS] ?? '');
        $needsFix = false;
        foreach ( $objDefIds as $i => $objDefId ) {
            $objDef = Pt_DataObjectDefManager::getById($objDefId);
            if ( $objDef == null ) {
                unset($objDefIds[$i]);
                $needsFix = true;
            }
        }

        if ( $needsFix ) {
            $this->props[FIELD_OBJ_DEF_IDS] = join(',', $objDefIds);
            $this->objDefIds = $objDefIds;
        }

    }

    /**
     * @return string
     */
    public function validateIntegrationNames()
    {
        $problems = "";
        $objDefs = $this->getAllObjects();
        $rels = $this->getAllRelationshipDefs();
        $objectProblems = "";
        $fieldProblems = "";
        $relProblems = "";
        foreach ( $objDefs as $objDef ) {
            $integrationName = $objDef->getObjectDefName();
            $objName = $objDef->getSingularName();
            if ( $objDef->isPlatform() && preg_match_all(INTEGRATION_NAME_VALIDATION, $integrationName) ) {
                $objectProblems .= $objName . ", ";
            }
            $fields = $objDef->getFields();
            foreach ( $fields as $field ) {
                $integrationNameField = $field->getFieldName();
                if ( !$field->isStandard() && preg_match_all(INTEGRATION_NAME_VALIDATION, $integrationNameField) ) {
                    $fieldProblems .= " " . $field->getDisplayLabel() . ", ";
                }
            }
        }
        foreach ( $rels as $rel ) {
            $integrationNameRel = $rel->getRelationshipName();
            if ( preg_match_all(INTEGRATION_NAME_VALIDATION, $integrationNameRel) ) {
                $relProblems .= " " . $integrationNameRel . ", ";
            }
        }
        if ( $objectProblems ) {
            $problems .= '<br>' . GT($this->textMap, "IA.OBJECTS") . ': ' . rtrim($objectProblems, ', ');
        }
        if ( $fieldProblems ) {
            $problems .= '<br>' . GT($this->textMap, "IA.FIELDS") . ': ' . rtrim($fieldProblems, ', ');
        }
        if ( $relProblems ) {
            $problems .= '<br>' . GT($this->textMap, "IA.RELATIONSHIPS") . ': ' . rtrim($relProblems, ', ');
        }
        if ( $problems ) {
            $problems .= '<br>';
        }

        return $problems;
    }

    /**
     *
     * @return Pt_RelationshipDef[]
     */
    public function getAllRelationshipDefs()
    {
        $rels = [];
        $objDefs = $this->getAllObjects();
        foreach ( $objDefs as $objDef ) {
            $rels = array_merge(Pt_RelationshipDefManager::getByObjectDef($objDef->getId()), $rels);
        }

        return array_unique($rels, SORT_REGULAR);
    }

    /**
     *
     * @return string
     */
    public function getNamespace()
    {
        return $this->props[NAMESPACE_PARAM] ?? '';
    }
    
    /**
     * Get the original company number (the company where the application was developed originally).
     *
     * @return int
     */
    public function getOriginalCompanyNo()
    {
        $origArr = explode('@', $this->getOriginalId());
        $origCny = ( is_array($origArr) && count($origArr) == 2 ) ? $origArr[0] : 0;
        
        return $origCny;
    }
    
    /**
     * Check if the user is trying to remove any reports that are attached to menus
     *
     * @param array $props
     * @param bool $isIcrw
     *
     * @return bool
     */
    public function checkRemovedReportsFromMenus($props, $isIcrw)
    {
        $menus = $this->getMenusByPageType(TYPE_REPORT);
        $reportsByMenu = [];
        $icrwReportsByMenu = [];
        if ( !$isIcrw ) {
            foreach ( $menus as $menu ) {
                $reportId = $menu->getReportId();
                $reportName = Pt_StandardUtil::getReportNameFromID($reportId, true);
                if ( $reportName && !in_array($reportName, $reportsByMenu) ) {
                    $reportsByMenu[] = $reportName;
                }
            }
            $updatedCrNames = explode(',', $props['crNames']);
            if ( !empty($reportsByMenu) ) {
                foreach ( $updatedCrNames as $updatedCrName ) {
                    if ( in_array($updatedCrName, $reportsByMenu) ) {
                        return true;
                    }
                }
            }
        } else {
            foreach ( $menus as $menu ) {
                $reportId = $menu->getReportId();
                $icrwReport = Pt_StandardUtil::getReportNameFromID($reportId, false);
                if ( $icrwReport && !in_array($icrwReport, $reportsByMenu) ) {
                    $icrwReportsByMenu[] = $icrwReport;
                }
            }
            $updatedIcrwNames = explode(',', $props['icrwNames']);
            if ( !empty($icrwReportsByMenu) ) {
                foreach ( $updatedIcrwNames as $updatedIcrwName ) {
                    if ( in_array($updatedIcrwName, $icrwReportsByMenu) ) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }

    public function getUntranslatedAppName(): string
    {
        return $this->appName;
    }
    
    /**
     * Export app labels to csv
     *
     * @param array $customLabels
     *
     * @return Pt_CSVWriter
     */
    function toCSV($customLabels, $baseLanguage, $desiredLanguage)
    {
        $tokens = [ "IA.SOURCE_LANGUAGE", "IA.TARGET_LANGUAGE" ];
        $placeholderTokens = [];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $exporter = new Pt_CSVWriter();
        $exporter->append(LABELS_KEY);
        $exporter->append(GT($textMap, "IA.SOURCE_LANGUAGE") . '--' . $baseLanguage);
        $exporter->append(GT($textMap, "IA.TARGET_LANGUAGE") . '--' . $desiredLanguage);
        $exporter->nextRow();
        
        foreach ( $customLabels as $key => $values ) {
            $exporter->append($key);
            $exporter->append($values[0]);
            $exporter->append($values[1]);
            $exporter->nextRow();
        }
        
        return $exporter;
    }
    
    /**
     * Generate the CSV file for exporting labels
     *
     * @param string       $name
     * @param Pt_CSVWriter $csvObj
     *
     */
    function generateCsv($name, $csvObj)
    {
        ob_end_clean();
        header("Content-Type: text/csv; charset=UTF-8");
        header("Content-Disposition: attachment; filename=\"$name.csv\"");
        header('Content-Length: ' . $csvObj->getBinaryLength());

        $csvObj->printBuffer();
        exit();
    }

    /**
     * @return void
     * @throws Exception
     */
    private function setTransactionMaps(): void
    {
        if (FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('APPLICATION_MANAGEMENT')) {
            $transactionMaps = PackageTrackingManager::getItemsListByAppId($this->origId);
            $this->transactionMaps = $transactionMaps;
        }
    }

    /**
     * @param string $doctype
     *
     * @return string
     */
    private function getMapDoctype(string $doctype): string
    {
        return $this->transactionMaps[$doctype] ?? $doctype;
    }

    /**
     * @param string $doctype
     *
     * @return string
     */
    private function getReversedMapDoctype(string $doctype): string
    {
        $invDoctypes = array_flip($this->transactionMaps);

        return $invDoctypes[$doctype] ?? $doctype;
    }

    /**
     * Process the objects string to replace the doctypes, using transaction maps
     *
     * @param string $stDefIds
     * @param bool   $isFlipped
     *
     * @return string
     */
    public function processDoctypes(string $stDefIds, bool $isFlipped = false): string
    {
        if (
            FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('APPLICATION_MANAGEMENT')
            && isset($stDefIds)
        ) {
            $stDefIdsList = explode(',', $stDefIds);
            if (is_array($stDefIdsList)) {
                foreach ($stDefIdsList as $key => &$value) {
                    if (!empty($value) && ($obj = explode('.', $value))) {
                        if (count($obj) === 2 && !empty($obj[1])) {
                            $obj[1] = $isFlipped ? $this->getReversedMapDoctype($obj[1]) : $this->getMapDoctype($obj[1]);
                            $value = implode('.', $obj);
                        }
                    }
                }
                $stDefIds = implode(',', $stDefIdsList);
            }
        }

        return $stDefIds;
    }
}
