<?
/**
 * Data object built according to object definition.
 * Table: PT_OBJ_DATA
 */
require_once 'Pt_Includes.inc';

class Pt_DataObject extends Pt_AbstractEntity
    implements Pt_IPickItem, Pt_IXmlable
{

    /* @var int $objDefId */
    protected $objDefId;
    /* @var array $fieldData */
    protected $fieldData;

    /**
     * @param int       $id
     * @param int       $objDefId
     * @param array     $fieldData
     */
    public function __construct($id, $objDefId, $fieldData) 
    {
        parent::__construct($id, []);
        $id = intval($id);
        // We need to check the System User here and allow the id =-1 for it: TM 96642
        // Also check the userinfo objDefId defined in $standardObjectMap in Util_StandardObjectRegistry.cls ('userinfo' => 5058)
        $idNegativeCheck = $id < 0 && ($objDefId !== Util_StandardObjectRegistry::getPermanentObjectTypeId('userinfo')
                || $id !== -1 || (isset($fieldData['LOGINID']) && $fieldData['LOGINID'] !== SYSTEMUSER_LOGINID));

        if ( $idNegativeCheck || $objDefId <= 0 || !is_array($fieldData) ) {
            throw new Pt_I18nException('PAAS-0035', "Illegal data");
        }
        $this->objDefId = $objDefId;
        $this->fieldData = $fieldData;
    }

    /**
     * Update data.
     *
     * @param array $fieldData
     */
    public function update($fieldData) 
    {
        if (is_array($fieldData)) {
            $this->fieldData = array_merge($this->fieldData, $fieldData); 
        }
    }

    /**
     * VALUE attribute for UI SELECT list.
     *
     * @return string
     */
    public function getSelectValue() 
    {
        return (string)$this->id;
    }

    /**
     * Text label for Lookup (if rendered as drop-down)
     *
     * @return string
     */
    public function getLookupLabel() 
    {
        $objDef = $this->getObjectDef();
        if ($objDef!=null) {
            $template = $objDef->getLookupTemplate();
            if (strlen($template ?? '') > 0) {
                return Pt_TemplateEngine::processName($template, $this, "SOURCE: $objDef Name Template");
            }
        }
        return $this->__toString();
    }

    /**
     * Get id of object definition.
     *
     * @return int
     */
    public function getObjectDefId() 
    {
        return $this->objDefId;
    }

    /**
     * Get object definition.
     *
     * @return Pt_DataObjectDef|null
     */
    public function getObjectDef() 
    {
        return Pt_DataObjectDefManager::getById($this->objDefId);
    }

    /**
     * Get company number.
     *
     * @return int
     */
    public function getCompanyNo() 
    {
        return $this->getObjectDef()->getCompanyNo();
    }

    /**
     * Get object's definition display name.
     *
     * @return string|null
     */
    public function getObjectDefName() 
    {
        $objDef = $this->getObjectDef();
        return ($objDef==null ? null : $objDef->__toString());
    }

    /**
     * Get object's definition integration name.
     *
     * @return string|null
     */
    public function getDefName() 
    {
        $objDef = $this->getObjectDef();
        return ($objDef==null ? null : $objDef->getObjectDefName());
    }

    /**
     * Get attributes stored in fields table. Returns new HashMap.
     * This will not include retaltionships, formulas, or related fields.
     *
     * @return array
     */
    public function getShortFieldMap()
    {
        return array_merge([], $this->fieldData);
    }

    /**
     * Get all attributes for this DataObject. Returns new HashMap.
     * This will include retaltionships, formulas, or related fields.
     *
     * @param  bool $includeNull
     * @param  bool $includeRelationships
     *
     * @return array
     */
    public function getFullFieldMap( $includeNull = false, $includeRelationships = true ) 
    {
        $map = [];
        $objDef = $this->getObjectDef();
        foreach ($objDef->getFields() as $field) {
            if ($field instanceof Pt_FieldDummy) {
                continue; 
            }

            if ( !$includeRelationships && ($field instanceof Pt_FieldRelationship) ) {
                continue;
            }

            try {
                $value = $field->getUIField()->getDataValue($this);
            } catch (Exception $ex) {
                $value = null;
            }

            if ($includeNull === true || isset($value)) {
                $map[$field->getFieldName()] = $value;
            }
        }
        $map[FIELD_NAME] = $this->__toString();
        return $map;
    }

    /**
     * Has value for given field's name?    - Bug 38533
     *
     * @param string $fieldName
     *
     * @return bool
     */
    public function hasFieldValue($fieldName) 
    {
        return array_key_exists($fieldName, $this->fieldData);
    }

    /**
     * Get Object value by field's name.
     *
     * @param string $fieldName
     *
     * @return mixed
     */
    public function getFieldValue($fieldName) 
    {

        //  If the field is not set,  see if this is a lazy evaluated value.
        if (!isset($this->fieldData[$fieldName]) && $this->getObjectDef()->isLazyField($fieldName)) {
            $this->fieldData[$fieldName] = $this->getObjectDef()->lazyFieldEval($fieldName, $this);
            return $this->fieldData[$fieldName];
        }

        if (array_key_exists($fieldName, $this->fieldData) || $this->getObjectDef()->isPlatform()) {
            return $this->fieldData[$fieldName] ?? null;
        } else {
            // Field names are upper-cased
            return EntityManager::AccessByPath($this->fieldData, isl_strtoupper($fieldName));
        }
    }

    /**
     * Set Object value by field's name
     *
     * @param string $fieldName
     * @param mixed  $value
     */
    public function setFieldValue($fieldName, $value) 
    {
        if ($value == null) {
            unset($this->fieldData[$fieldName]); 
        } else {
            $this->fieldData[$fieldName] = $value; 
        }
    }

    /**
     * Get String value by field's name.
     *
     * @param string $fieldName
     *
     * @return string
     */
    public function getFieldString($fieldName) 
    {
        return strval($this->fieldData[$fieldName]);
    }

    /**
     * Get Date value by field's name.
     *
     * @param string $fieldName
     *
     * @return string
     */
    public function getFieldDate($fieldName) 
    {
        return FormatDateForDisplay($this->fieldData[$fieldName]);
    }

    /**
     * @param string $fieldName
     *
     * @return string
     */
    public function getFieldTimestamp($fieldName)
    {
        return FormatTimestampForDisplay($this->fieldData[$fieldName]);
    }

    /**
     * Get GMT Date value by field's name.
     *
     * @param string $fieldName
     *
     * @return string
     */
    public function getFieldGMTDate($fieldName)
    {
        return FormatGMTTimestampAsDateForDisplay($this->fieldData[$fieldName]);
    }

    /**
     * Get long value by field's name.
     *
     * @param string $fieldName
     *
     * @return int
     */
    public function getFieldInt($fieldName) 
    {
        return intval($this->fieldData[$fieldName] ?? 0);
    }

    /**
     * Get double value by field's name.
     *
     * @param string $fieldName
     * @param float  $defaultValue
     *
     * @return float
     */
    public function getFieldDouble($fieldName, $defaultValue) 
    {
        $obj = $this->fieldData[$fieldName];
        return ($obj==null ? $defaultValue : floatval($obj));
    }

    /**
     * Get bool value by field's name.
     *
     * @param string $fieldName
     * @param bool   $defaultValue
     *
     * @return bool
     */
    public function getFieldBool($fieldName, $defaultValue) 
    {
        $obj = $this->fieldData[$fieldName];
        return ($obj==null ? $defaultValue : pt_boolval($this->fieldData[$fieldName]));
    }

    /**
     * Get file value by field's name.
     *
     * @param string $fieldName
     *
     * @return Pt_FileUpload|null
     */
    public function getFieldFile($fieldName) 
    {
        $obj = $this->fieldData[$fieldName] ?? null;
        return ($obj instanceof Pt_FileUpload ? $obj : null);
    }

    /**
     * Get process id (if any) assigned for this object.
     *
     * @return int
     */
    public function getProcessId() 
    {
        return $this->getFieldInt(FIELD_PROCESS);
    }

    /**
     * Get workflow process instance assigned for this object.
     *
     * @return Pt_Process
     */
    public function getProcess() 
    {
        $p = Pt_ProcessManager::getById($this->getProcessId());
        if ($p == null) {
            $p = $this->getObjectDef()->getDefaultProcess(); 
        }
        return $p;
    }

    /**
     * Get workflow status id (if any) assigned for this object.
     *
     * @return int
     */
    public function getStatusId() 
    {
        return $this->getFieldInt(FIELD_STATUS);
    }

    /**
     * Get workflow status (if any) assigned for this object.
     *
     * @return Pt_Status|null
     */
    public function getStatus() 
    {
        return Pt_StatusManager::getById($this->getStatusId());
    }

    /**
     * Get workflow status as String.
     *
     * @return string
     */
    public function getStatusStr() 
    {
        $s = $this->getStatus();
        return ($s==null ? '' : $s->__toString());
    }

    /**
     * Change workflow status.
     *
     * @param Pt_Status $newStatus
     * @param int       $updatedBy
     * @param string    $updatedAt
     */
    public function changeStatus($newStatus, $updatedBy, $updatedAt) 
    {
        $this->setFieldValue(FIELD_STATUS, $newStatus->getId());
        $this->setLastUpdate($updatedBy, $updatedAt);
    }

    /**
     * Set updatedBy, updatedAt values.
     *
     * @param int       $updatedBy
     * @param string    $updatedAt
     */
    public function setLastUpdate($updatedBy, $updatedAt) 
    {
        $this->setFieldValue(FIELD_UPDATED_BY, $updatedBy);
        $this->setFieldValue(FIELD_UPDATED_AT, $updatedAt);
    }

    /**
     * String representation.
     *
     * @param string $value
     */
    public function setDisplayName($value) 
    {
        $this->fieldData[FIELD_NAME] = $value;
    }

    /**
     * return this object's name
     *
     * @return string the name of the object
     */
    public function getName()
    {
        $objDef = $this->getObjectDef();
        $objName = '';
        if ($objDef->isPlatform()) {
            $value = $this->fieldData[FIELD_NAME];
            if (!( $value instanceof self )) { // Ticket 4270
                $objName = strval($value);
            }
        } else {
            $objName = strval($this->fieldData[$objDef->getNameColumn()]);
        }
        if (strlen($objName)==0) {
            return $objDef->__toString();
        }
        return $objName;
    }

    /**
     * String representation.
     *
     * @return string
     */
    public function __toString() 
    {
        return $this->getName();
    }

    /**
     * Get id of object creator.
     *
     * @return int
     */
    public function getCreatedById() 
    {
        return $this->getFieldInt(FIELD_CREATED_BY);
    }

    /**
     * Get creation date.
     *
     * @return string
     */
    public function getCreatedAt() 
    {
        return $this->getFieldGMTDate(FIELD_CREATED_AT);
    }

    /**
     * Get id of object updater.
     *
     * @return int
     */
    public function getUpdatedById() 
    {
        return $this->getFieldInt(FIELD_UPDATED_BY);
    }

    /**
     * Get update date.
     *
     * @return string
     */
    public function getUpdatedAt() 
    {
        return $this->getFieldGMTDate(FIELD_UPDATED_AT);
    }

    /**
     * Generate XML and append it to StringBuffer (used for seed records).
     *
     * @param string &$buff
     */
    public function toXML(& $buff) 
    {
        $buff .= "<DataObject";
        xml_appendAttribute($buff, 'id', $this->id);
        xml_appendAttribute($buff, 'objDefId', $this->objDefId);
        $buff .= " >\n";

        $objDef = $this->getObjectDef();
        foreach ($objDef->getFields() as $field) {
            if ($field == null || $field->isReadOnly()) {
                continue; 
            }
            try {
                $uiField = $field->getUIField();
                $key = $field->getFieldName();
                $value = $uiField->getValue($this, 1);
                if ($value == null) {
                    continue; 
                }
                $buff .= "<Field name=\"".$key."\">".xml_encode($uiField->toExport($value, $this, true))."</Field>\n";
            }
            catch (Exception $ex) {
                $buff .= "<Error>".$ex->getMessage()."</Error>\n";
            }
        }

        $buff .= "</DataObject>\n";
    }

    /**
     * Compares two objects by name.
     *
     * @param Pt_DataObject $data1
     * @param Pt_DataObject $data2
     *
     * @return int
     */
    public static function compareByName($data1, $data2)
    {
        return isl_strcasecmp($data1->__toString(), $data2->__toString());
    }
    
    /**
     * @return array
     */
    public function getFieldDataForRest() : array
    {
        $map = $this->fieldData;
        $objDef = $this->getObjectDef();
        foreach ( $objDef->getFields() as $field ) {
            $fieldName = $field->getFieldName();
            if ( !isset($map[$fieldName]) || !( $field instanceof Pt_FieldRelationship ) ) {
                continue;
            }
            
            $value = $map[$fieldName];
            
            $rel = $field->getRelationshipDef();
            if ( !$rel->isOtherMultiple($this->getObjectDefId()) || $rel->isHierarchy() ) {
                if ( $value instanceof Pt_ArrayIds ) {
                    $ids = $value->getIds();
                    if ( count($ids) ) {
                        $map[$fieldName] = $ids[0];
                    } else {
                        unset($map[$fieldName]);
                    }
                } else if ( is_array($value) ) {
                    if ( count($value) ) {
                        $map[$fieldName] = $value[0];
                    } else {
                        unset($map[$fieldName]);
                    }
                }
            }
        }
        
        return $map;
    }
    
}

