<?
/**
 * Manages persistence of Pt_HostedFile instances.
 * Table: PT_HOSTED_FILE
 */
require_once('Pt_Includes.inc');

require_once('Pt_HostedFile.cls');

class Pt_HostedFileManager {

    const SQL_SELECT_ALL =
        "SELECT RECORD#, ORIGINAL_ID, DISPLAY_NAME, FILE_NAME, CONTENT_TYPE, DESCRIPTION, CREATED_BY, TO_CHAR(CREATED_AT, 'MM/DD/YYYY HH24:MI:SS') CREATED_AT, UPDATED_BY, TO_CHAR(UPDATED_AT, 'MM/DD/YYYY HH24:MI:SS') UPDATED_AT FROM PT_HOSTED_FILE WHERE CNY#=:1 ORDER BY DISPLAY_NAME";

    const SQL_SELECT_ID =
        "SELECT RECORD#, ORIGINAL_ID, DISPLAY_NAME, FILE_NAME, CONTENT_TYPE, DESCRIPTION, CREATED_BY, TO_CHAR(CREATED_AT, 'MM/DD/YYYY HH24:MI:SS') CREATED_AT, UPDATED_BY, TO_CHAR(UPDATED_AT, 'MM/DD/YYYY HH24:MI:SS') UPDATED_AT FROM PT_HOSTED_FILE WHERE CNY#=:1 AND RECORD#=:2";

    const SQL_SELECT_ORIG_ID =
        "SELECT RECORD#, ORIGINAL_ID, DISPLAY_NAME, FILE_NAME, CONTENT_TYPE, DESCRIPTION, CREATED_BY, TO_CHAR(CREATED_AT, 'MM/DD/YYYY HH24:MI:SS') CREATED_AT, UPDATED_BY, TO_CHAR(UPDATED_AT, 'MM/DD/YYYY HH24:MI:SS') UPDATED_AT FROM PT_HOSTED_FILE WHERE CNY#=:1 AND ORIGINAL_ID=:2";

    const SQL_INSERT =
        "INSERT INTO PT_HOSTED_FILE (CNY#, RECORD#, ORIGINAL_ID, DISPLAY_NAME, FILE_NAME, CONTENT_TYPE, DESCRIPTION, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT) VALUES (:1, :2, :3, :4, :5, :6, :7, :8, CURRENT_TIMESTAMP AT TIME ZONE 'GMT', :9, CURRENT_TIMESTAMP AT TIME ZONE 'GMT')";

    const SQL_UPDATE =
        "UPDATE PT_HOSTED_FILE SET DISPLAY_NAME=:1, FILE_NAME=:2, CONTENT_TYPE=:3, DESCRIPTION=:4, UPDATED_BY=:5, UPDATED_AT=CURRENT_TIMESTAMP AT TIME ZONE 'GMT' WHERE CNY#=:6 AND RECORD#=:7";

    const SQL_DELETE =
        "DELETE FROM PT_HOSTED_FILE WHERE CNY#=:1 AND RECORD#=:2";

    /**
     * Get list of all hosted files for current customer.
     *
     * @return Pt_HostedFile[]
     */
    public static function getAll() {
        $resultset = db_query([self::SQL_SELECT_ALL, GetMyCompany()]);
        $arr = [];
        foreach ($resultset as $row) {
            if ($row === false) {
                break;
            }
            $arr[] = self::getHostedFile($row);
        }
        return $arr;
    }

    /**
     * Get hosted file by id.
     *
     * @param int $id
     *
     * @return Pt_HostedFile|null
     */
    public static function getById($id) {
        if ($id <= 0) {
            return null;
        }
        $resultset = db_query([self::SQL_SELECT_ID, GetMyCompany(), $id]);
        if (!$resultset) {
            return null;
        }
        return self::getHostedFile($resultset[0]);
    }

    /**
     * Get hosted file by original id.
     *
     * @param string $origId
     *
     * @return Pt_HostedFile|null
     */
    public static function getByOriginalId($origId) {
        if ($origId == null) {
            return null;
        }
        $resultset = db_query([self::SQL_SELECT_ORIG_ID, GetMyCompany(), $origId]);
        if (!$resultset) {
            return null;
        }
        return self::getHostedFile($resultset[0]);
    }

    /**
     * Create new hosted file.
     *
     * @param string $origId
     * @param string $displayName
     * @param string $fileName
     * @param string $contentType
     * @param string $description
     *
     * @return Pt_HostedFile
     */
    public static function create($origId, $displayName, $fileName, $contentType, $description)
    {
        Pt_HostedFile::validate($displayName);
        $createdBy = GetMyUserid();
        $createdAt = util_now_gmt();

        $id = db_nextId('PT_HOSTED_FILE');
        // PHP8_NUMERIC_STRING_COMPARE; Priority: low; Behavior: same, Risk: low, Solution: cast
        if (!isset($origId) || (int) $origId <= 0) {
            $origId = GetMyCompany() . '@' . $id;
        }

        db_exec([self::SQL_INSERT, GetMyCompany(), $id, $origId, db_str($displayName, 50), db_str($fileName, 50),
            db_str($contentType, 30), db_str($description, 2000), $createdBy, $createdBy, $createdBy, $createdBy]);

        return new Pt_HostedFile($id, $origId, $displayName, $fileName, $contentType,
            $description, $createdBy, $createdAt, $createdBy, $createdAt);
    }

    /**
     * Update existing hosted file.
     *
     * @param Pt_HostedFile $hf
     * @param string        $displayName
     * @param string        $fileName
     * @param string        $contentType
     * @param string        $description
     */
    public static function update(Pt_HostedFile $hf, $displayName, $fileName, $contentType, $description)
    {
        Pt_HostedFile::validate($displayName);
        $updatedBy = GetMyUserid();
        $updatedAt = util_now_gmt();

        db_exec([self::SQL_UPDATE, db_str($displayName, 50), db_str($fileName, 50),
            db_str($contentType, 30), db_str($description, 2000), $updatedBy, GetMyCompany(), $hf->getId()]);

        $hf->updateHostedFile($displayName, $fileName, $contentType, $description, $updatedBy, $updatedAt);
    }

    /**
     * Delete existing hosted file.
     *
     * @param Pt_HostedFile $hf
     */
    public static function delete(Pt_HostedFile $hf)
    {
        db_exec([self::SQL_DELETE, GetMyCompany(), $hf->getId()]);
    }

    /**
     * Select hosted file data from ResultSet.
     *
     * @param string[] $row
     *
     * @return Pt_HostedFile
     */
    private static function getHostedFile($row) {
        return new Pt_HostedFile(
            intval($row['RECORD#']),    // id
            $row['ORIGINAL_ID'],        // $origId
            $row['DISPLAY_NAME'],       // displayName
            $row['FILE_NAME'],          // fileName
            $row['CONTENT_TYPE'],       // contentType
            $row['DESCRIPTION'],        // description
            intval($row['CREATED_BY']),     // createdBy
            $row['CREATED_AT'],             // createdAt
            intval($row['UPDATED_BY']),     // updatedBy
            $row['UPDATED_AT']              // updatedAt
        );
    }

}
