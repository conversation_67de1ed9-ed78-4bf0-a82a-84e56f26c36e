<?
/**
 * Manages persistence of Activity trail records.
 * Table: PT_ACT_TRAIL
 */
require_once('Pt_Includes.inc');

require_once('Pt_ActivityTrail.cls');
require_once('Pt_ISpreadsheetWriter.cls');

class Pt_ActivityTrailManager {

    const SQL_SELECT_ID = 'SELECT RECORD#, OBJ_ID, TRAIL_TEXT, EMAIL_TEXT, CREATED_BY, to_char(CREATED_AT, \'MM/DD/YYYY HH24:MI:SS\') CREATED_AT_STR, VERSION FROM PT_ACT_TRAIL WHERE CNY#=:1 AND RECORD#=:2';

    const SQL_SELECT_OBJ = 'SELECT RECORD#, OBJ_ID, TRAIL_TEXT, EMAIL_TEXT, CREATED_BY, to_char(CREATED_AT, \'MM/DD/YYYY HH24:MI:SS\') CREATED_AT_STR, CREATED_AT, VERSION FROM PT_ACT_TRAIL WHERE CNY#=:1 AND OBJ_ID=:2 ORDER BY CREATED_AT DESC, RECORD# DESC';

    const SQL_INSERT = "INSERT INTO PT_ACT_TRAIL (CNY#, RECORD#, OBJ_ID, TRAIL_TEXT, EMAIL_TEXT, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT, VERSION) VALUES (:1, :2, :3, :4, :5, :6, CURRENT_TIMESTAMP AT TIME ZONE 'GMT', :7, CURRENT_TIMESTAMP AT TIME ZONE 'GMT', :8)";

    const SQL_MERGE = 'UPDATE PT_ACT_TRAIL SET OBJ_ID=:1 WHERE CNY#=:2 AND OBJ_ID=:3';

    const SQL_DELETE_OBJ = 'DELETE FROM PT_ACT_TRAIL WHERE CNY#=:1 AND OBJ_ID=:2';

    const PT_AUDIT_VERSION_2 = 2;
    
    const PT_TRAIL_TEXT_MAX_LENGTH = 2000;
    
    const PT_JSON_END = '"}]}';
    
    const PT_JSON_NEW_VALUE = '"},{"name":"NEW_VALUE","value":"';
    

    /**
     * Select list of activity trail records for given Entity
     *
     * @param int $entityId
     * @param int $numRec
     *
     * @return Pt_ActivityTrail[]
     * @throws Exception
     */
    public static function getByEntity($entityId, $numRec=0) {
        $resultset = db_query([self::SQL_SELECT_OBJ, GetMyCompany(), $entityId], 0, $numRec>0 ? $numRec : '');
        $rows = ($numRec > 0) ? array_slice($resultset, 0, $numRec) : $resultset;

        return self::getActivityTrails($rows);
    }

    /**
     * Get activity trail record by id.
     *
     * @param int $id
     *
     * @return Pt_ActivityTrail|null
     * @throws Exception
     */
    public static function getById($id) {
        if ($id <= 0) {
            return null;
        }
        $resultset = db_query([self::SQL_SELECT_ID, GetMyCompany(), $id]);
        if (!$resultset) {
            return null;
        }
        $resultset = self::getActivityTrails($resultset);
        return $resultset[0];
    }

    /**
     * Create record on field's value change
     *
     * @param Pt_DataObject   $data
     * @param Pt_DataFieldDef $field
     * @param mixed           $oldValue
     * @param mixed           $newValue
     *
     * @throws Exception
     */
    public static function createValueChange($data, Pt_DataFieldDef $field, $oldValue, $newValue) {
        $uiField = $field->getUIField();
        if (!util_equals($oldValue, $newValue)) {   // T 9015
            $str1 = $uiField->toText($oldValue, $data);
            $str2 = $uiField->toText($newValue, $data);
            if ($str1 != $str2) {
                $token = [
                    'id' => 'IA.VALUE_OF_FIELD_HAS_BEEN_CHANGED',
                    'placeHolders' => [
                        ['name' => 'FIELD', 'value' => $field->__toString()],
                        ['name' => 'OLD_VALUE', 'value' => $str1],
                        ['name' => 'NEW_VALUE', 'value' => $str2],
                    ],
                ];

                $encodedToken = json_encode($token);
                if (isl_strlen($encodedToken) > self::PT_TRAIL_TEXT_MAX_LENGTH) {
                    $repairLength = self::PT_TRAIL_TEXT_MAX_LENGTH - isl_strlen(self::PT_JSON_END . self::PT_JSON_NEW_VALUE);
                    $shortToken = isl_substr($encodedToken, 0, $repairLength);
                    $token = self::repairLongToken($shortToken);
                }

                self::createI18N($data, $token);
            }
        }
    }

    /**
     * Create new activity trail record.
     *
     * @param Pt_DataObject|Pt_DataEntity|int $entity
     * @param string                          $text
     * @param string                          $emailText
     * @param int|null                        $version
     *
     * @throws Exception
     */
    public static function create($entity, $text, $emailText='', $version = null) {
        $entityId = 0;
        if ($entity instanceof Pt_DataObject) {
            $entityId = $entity->getId();
        } else if ($entity instanceof Pt_DataEntity) {
            $entityId = -$entity->getId();
        } else if (is_numeric($entity)) {
            $entityId = intval($entity);
        }
        if ($entityId == 0) {
            return;
        }
        if ($entity instanceof Pt_DataEntity) {
            if ($entity->blockActivityTrail()) {
                return;
            }
        }
        if (strlen($text) == 0) {
            throw new Pt_I18nException('PAAS-0134', "Cannot create Audit Trail record: no text provided");
        }

        $id = db_nextId('PT_ACT_TRAIL');
        $createdBy = GetMyUserid();
        db_exec([
            self::SQL_INSERT,
            GetMyCompany(),
            $id,
            $entityId,
            db_str($text, 2000),
            $emailText,
            $createdBy,
            $createdBy,
            $version]
        );
    }

    /**
     * Replace entityOld by entityNew
     *
     * @param Pt_AbstractEntity $entityNew
     * @param Pt_AbstractEntity $entityOld
     *
     * @throws Exception
     */
    public static function merge(Pt_AbstractEntity $entityNew, Pt_AbstractEntity $entityOld) {
        if ($entityNew == null || $entityOld == null) {
            return;
        }
        db_exec([self::SQL_MERGE, $entityNew->getId(), GetMyCompany(), $entityOld->getId()]);
    }

    /**
     * Delete all activity trail records for given Entity.
     *
     * @param Pt_AbstractEntity $entity
     *
     * @throws Exception
     */
    public static function deleteEntity(Pt_AbstractEntity $entity) {
        if ($entity == null) {
            return;
        }
        db_exec([self::SQL_DELETE_OBJ, GetMyCompany(), $entity->getId()]);
    }

    /**
     * Export list of activity trail records for given Entity
     *
     * @param int                   $entityId
     * @param Pt_ISpreadsheetWriter $exporter
     *
     * @throws Exception
     */
    public static function exportAll($entityId, Pt_ISpreadsheetWriter $exporter) {
        $tokens = ["IA.DATE_TIME2", "IA.CONTENT", "IA.USER"];
        $placeholderTokens = [];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $exporter->append(GT($textMap, "IA.DATE_TIME2"));
        $exporter->append(GT($textMap, "IA.CONTENT"));
        $exporter->append(GT($textMap, "IA.USER"));
        $exporter->nextRow();

        $resultset = db_query([self::SQL_SELECT_OBJ, GetMyCompany(), $entityId]);
        $atResultSet = self::getActivityTrails($resultset);
        foreach ($atResultSet as $at) {
            $exporter->appendGMTTimestamp($at->getCreatedAt());
            $exporter->append($at->getText());
            $exporter->append(Pt_UserProxy::getName($at->getCreatedById()));
            $exporter->nextRow();
        }
    }

    /**
     * Create ActivityTrail instances from every element in $rows
     *
     * @param string[][] $rows
     *
     * @return Pt_ActivityTrail[]
     */
    private static function getActivityTrails(array $rows): array
    {
        $results = [];
        $rows = self::applyI18N($rows);
        foreach ($rows as $row) {
            $results[] = new Pt_ActivityTrail(
                intval($row['RECORD#']),        // id
                intval($row['OBJ_ID']),         // objId
                $row['TRAIL_TEXT'],             // text
                $row['EMAIL_TEXT'],             // emailText
                intval($row['CREATED_BY']),     // createdBy
                $row['CREATED_AT_STR']          // createdAt
            );
        }

        return $results;
    }

    /**
     * @param Pt_DataObject|Pt_DataEntity|int $entity
     * @param array                           $text
     * @param string                          $emailText
     *
     * @return void
     * @throws Exception
     */
    public static function createI18N($entity, $text, $emailText='')
    {
        self::create($entity, json_encode($text), $emailText, self::PT_AUDIT_VERSION_2);
    }

    /**
     * Bulk decode and translate tokens from TRAIL_TEXT. Returns all rows, even if needed translations or not.
     * Will unset the version for each row.
     *
     * @param string[][] $rows
     *
     * @return string[][]
     */
    public static function applyI18N(array $rows): array
    {
        $decodedTexts = [];
        foreach ($rows as $key => $row) {
            if ($row['VERSION'] == self::PT_AUDIT_VERSION_2 && !empty($row['TRAIL_TEXT'])) {
                if (isl_strlen($row['TRAIL_TEXT']) >= self::PT_TRAIL_TEXT_MAX_LENGTH) {
                    $decodedText = self::repairLongToken($row['TRAIL_TEXT']);
                } else {
                    $decodedText = json_decode($row['TRAIL_TEXT'], true);
                }
                if (is_array($decodedText) && !empty($decodedText['id'])) {
                    $decodedText['id'] .= (isl_strpos($decodedText['id'], ":") !== false) ? ".$key" : ":$key";
                    $decodedTexts[$key] = $decodedText;
                }
            }
            //it is used only for translation
            unset($rows[$key]['VERSION']);
        }

        $textMap = getIntlTextMap([], $decodedTexts);

        foreach ($decodedTexts as $key => $value) {
            $rows[$key]['TRAIL_TEXT'] = GT($textMap, $decodedTexts[$key]['id']);
        }

        return $rows;
    }

    /**
     * Used for Audit History - to translate the data from platform
     *
     * @param array $rows
     *
     * @return void
     * @throws I18NException
     */
    public static function applyI18NAuditHistory(array &$rows, $arrayKey = 'NOTES')
    {
        $decodedTexts = [];
        foreach ($rows as $key => $row) {
            if (!empty($row[$arrayKey]) && str_starts_with($row[$arrayKey], '{"id":"IA.')) {
                if (isl_strlen($row['TRAIL_TEXT']) >= self::PT_TRAIL_TEXT_MAX_LENGTH) {
                    $decodedText = self::repairLongToken($row[$arrayKey]);
                } else {
                    $decodedText = json_decode($row[$arrayKey], true);
                }
                if (is_array($decodedText) && !empty($decodedText['id'])) {
                    $decodedText['id'] .= (isl_strpos($decodedText['id'], ":") !== false) ? ".$key" : ":$key";
                    $decodedTexts[$key] = $decodedText;
                }
            }
        }

        $textMap = getIntlTextMap([], $decodedTexts);

        foreach ($decodedTexts as $key => $value) {
            $rows[$key][$arrayKey] = GT($textMap, $decodedTexts[$key]['id']);
        }
    }

    /**
     * @param string $str
     *
     * @return array
     */
    public static function repairLongToken(string $str): array
    {
        $result = json_decode($str, true);
        if ($result === null) {
            $newStr = $str;
            if (isl_strpos($newStr, '"NEW_VALUE","value":"') !== false) {
                $newStr = rtrim ($newStr, '\\');
                $newStr .= self::PT_JSON_END;
            } else {
                if (isl_substr_count($newStr, '"}') === 2) {
                    $position = isl_strrpos($newStr, '"}');
                    $newStr = isl_substr($newStr, 0, $position);
                }
                // if the string ends in quotes, from OLD_VALUE structure-> remove the quotes
                if (isl_str_endswith($newStr, '"') && !(isl_str_endswith($newStr, '\"'))) {
                    $newStr = isl_substr($newStr, 0, -1);
                }
                $newStr = rtrim ($newStr, '\\');
                $newStr .= self::PT_JSON_NEW_VALUE;
                $newStr .= self::PT_JSON_END;
            }
            $result = json_decode($newStr, true);
            if (is_array($result) && isset($result['placeHolders'])) {
                foreach ($result['placeHolders'] as &$placeHolders) {
                    if (in_array($placeHolders['name'], ['OLD_VALUE', 'NEW_VALUE'])) {
                        $position = isl_strrpos($placeHolders['value'], ',');
                        $placeHolders['value'] = isl_substr($placeHolders['value'], 0, $position);
                    }
                }
            }
        }

        return $result;
    }
}
