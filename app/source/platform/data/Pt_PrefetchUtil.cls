<?
/**
 * Prefetches data to improve performance.
 * Uses local caches like \Pt_RelationshipManager::$useRelationshipCache,
 * \Pt_RelationshipManagerNew::$useRelationshipCache, \Pt_Cache::$localCache.
 */

class Pt_PrefetchUtil
{

    /**
     * Retrieves UDD relationships for multiple records in bulk, starting from a list of relationship fields.
     * Also loads the related objects.
     *
     * @param Pt_FieldRelationship[]|null $fields
     * @param int[]                       $objIds the record numbers
     * @param bool                        $loadRelatedObjects
     *
     * @return void
     */
    public static function prefetchRelationshipsAndRelatedObjects($fields, array $objIds, bool $loadRelatedObjects
    ) : void
    {
        if (empty($fields)) {
            return;
        }
        $objIds = array_filter(array_unique($objIds));
        if (empty($objIds)) {
            return;
        }

        $relationshipData = Pt_RelationshipManagerChoose::prefetchRelationshipsForRecords($fields, $objIds);
        if ($loadRelatedObjects) {
            self::prefetchRelatedObjects($relationshipData);
        }
    }

    /**
     * @param array $relationshipData
     *
     * @return void
     */
    private static function prefetchRelatedObjects(array $relationshipData) : void
    {
        $objectsIdsToPrefetch = [];
        foreach ($relationshipData as $objDefId => $objIdsRels) {
            foreach ($objIdsRels as $objIdRelData) { // $objId => $objIdRelData
                foreach ($objIdRelData as $relId => $relationships) {
                    $rel = Pt_RelationshipDefManager::getById($relId);
                    if ($rel instanceof Pt_RelationshipDef && ($otherObjDef = $rel->getOtherObjectDef($objDefId))) {
                        $otherObjDefId = $otherObjDef->getId();
                        $objectsIdsToPrefetch[$otherObjDefId] = array_unique(
                            array_merge($objectsIdsToPrefetch[$otherObjDefId] ?? [], $relationships)
                        );
                    }
                }
            }
        }

        foreach ($objectsIdsToPrefetch as $objDefId => $ids) {
            Pt_DataObjectManager::getByIds(Pt_DataObjectDefManager::getById($objDefId), $ids);
        }
    }

}