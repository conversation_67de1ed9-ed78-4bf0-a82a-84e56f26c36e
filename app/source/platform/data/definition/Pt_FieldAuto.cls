<?
/**
 * Data field for Pt_AutoNumber.
 */
require_once 'Pt_FieldString.cls';
require_once 'Pt_DataFieldDefManager.cls';

class Pt_FieldAuto extends Pt_FieldString
{

    /**
     * True, if this field's $value is read-only (cannot be edited by user).
     *
     * @return bool true if the field is read-only
     */
    public function isReadOnly() {
        return true;
    }

    /**
     * @param Pt_I18nLabels[] $customLabels
     * @param string          $displayLabel
     * @param string          $description
     * @param array           $props
     * @param int             $updatedBy
     * @param string          $updatedAt
     */
    public function updateDataFieldDef($customLabels, $displayLabel, $description, $props, $updatedBy, $updatedAt)
    {
        // Yea, null, 0, false, or ''
        if ( ! isset($props[FIELD_AUTO_NUM_START]) || ! $props[FIELD_AUTO_NUM_START] ) {

            $props[FIELD_AUTO_NUM_START] = $this->props[FIELD_AUTO_NUM_START];

        } else if ( $props[FIELD_AUTO_NUM_START] != $this->props[FIELD_AUTO_NUM_START] ) {

            $this->resetSequence();

        }
        
        parent::updateDataFieldDef($customLabels, $displayLabel, $description, $props, $updatedBy, $updatedAt);
    }

    /**
     * True, if this field should not be cloned when record is cloned.
     *
     * @return bool
     */
    public function isNoClone() 
    {
        return true;
    }

    /**
     * Whether or not this field participates in DDS
     *
     * @return bool
     */
    public function ddsInclude()
    {
        return true;
    }

    /**
     * fetch and burn the next number in the sequence
     *
     * @return int   the next sequence number
     */
    public function getNextNumber()
    {

        $seqName = $this->getSequenceKey();

        $startAt = $this->getIntProperty(FIELD_AUTO_NUM_START);
        if ( $startAt <= 0 ) {
            $startAt = 1;
        }
        $startSql = "select $startAt from dual";

        $modMgr = Globals::$g->gManagerFactory->getManager('cosetup');

        $ret = $modMgr->GetNextSequence($seqName, 1, false, $startSql, 'P');

        if ( $ret === false ) {
            throw new Pt_I18nException('PAAS-0196', "Unable to assign new sequence number for " . $this->getFieldName(),
                                       [ 'FIELDNAME' => $this->getFieldName() ]);
        }

        return $ret;
    }

    /**
     * fetches the current next number in the sequence
     *
     * @return int|bool    the next number or fals on an error
     *
     * @throws Exception
     */
    public function getCurrentNumber()
    {
        $seqName = $this->getSequenceKey();
        /** @var CoSetupManager $modMgr */
        $modMgr = Globals::$g->gManagerFactory->getManager('cosetup');

        $ret = $modMgr->GetCurrentSequence($seqName, false, 'P');
        return $ret;
    }

    private function resetSequence()
    {
        $seqName = $this->getSequenceKey();
        $modMgr = Globals::$g->gManagerFactory->getManager('cosetup');
        $modMgr->ResetSequence($seqName, 'P');
    }

    /**
     * @return string
     */
    public function getSequenceKey()
    {
        return 'PT_AUTO_' . $this->getId();
    }
}
