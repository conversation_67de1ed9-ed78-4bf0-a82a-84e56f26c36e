<?
/**
 * Data field for int value, which is also stored as relationship.
 */
require_once 'Pt_DataFieldDef.cls';

class Pt_FieldIntRel extends Pt_FieldInt
{

    /**
     * True, if this field needs to save extra info (not in data table)
     *
     * @return bool
     */
    public function needsSaveExtra() 
    {
        return true;
    }

    /**
     * Save extra info (not in data table)
     *
     * @param Pt_DataObject $data
     * @param mixed         $newValue
     * @param mixed         $oldValue
     * @param array         $fieldData
     * @param bool          $doUpdate
     */
    public function saveExtra($data, $newValue, $oldValue, &$fieldData, $doUpdate) 
    {
        if ($doUpdate) {
            return; 
        }
        if ($data == null) {
            throw new Pt_I18nException('PAAS-0197', "Data object is null");
        }
        $relatedId = intval($newValue);
        $relData = Pt_DataObjectManager::getById2($relatedId);
        if ($relData == null) {
            throw new Pt_I18nException('PAAS-0198', "Cannot determine related id from $newValue",
                                       [ 'NEWVALUE' => $newValue ]);
        }
        $relDataObjDefId = $relData->getObjectDefId();

        $dataObjDefId = $data->getObjectDefId();
        $dataId = $data->getId();
        $rels = Pt_RelationshipDefManager::getByDefs($dataObjDefId, $relatedId);
        foreach ($rels as $rel) {
            Pt_RelationshipManagerChoose::createRelationship(
                $rel->getId(), $dataObjDefId, $dataId, $relDataObjDefId, $relatedId
            );
        }
    }

}
