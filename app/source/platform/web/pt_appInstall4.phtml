<?
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2019 Intacct Corporation All, Rights Reserved
 *
 *  Intacct Corporation Proprietary Information.
 *  This document contains trade secret data that belongs to Intacct
 *  corporation and is protected by the copyright laws. Information herein
 *  may not be used, copied or disclosed in whole or part without prior
 *  written consent from Intacct Corporation.
 */

try {
    include 'pt_setupHeader.inc';
    $id = http_getIntParameter(FIELD_ID);
    if (!$id) {
        $fu = Pt_FileUpload::getUpload(FIELD_IMPORT_FILE);
        if ($fu == null) {
            throw new Pt_I18nException('PAAS-0681', "No installation XML data");
        }
        $appXml = $fu->getData();
    } else {
        $applicationId = http_getParameter(FIELD_APPLICATION_ID);
        $app = Pt_ApplicationManager::getByOriginalId($applicationId);
        if ($app == null) {
            throw new Pt_I18nException(
                'PAAS-0917', "Application with id " . $app->getOriginalId() . " not found",
                ['APPLICATIONID' => $app->getOriginalId()]
            );
        }
        $appOriginalId = $app->getOriginalId();

        /* @var PackageRepositoryManager $packageRepositoryMgr */
        $packageRepositoryMgr = $gManagerFactory->getManager('packagerepository');
        $packageRepositoryObj = $packageRepositoryMgr->getByRecordNoDbFilter($id);
        if ( !$packageRepositoryObj ) {
            throw new Pt_I18nException(
                'PAAS-0918', "Application with id " . $app->getOriginalId() . " not found",
                ['APPLICATIONID' => $app->getOriginalId()]
            );
        }
        $appXml = $packageRepositoryObj['PACKAGE'];
        if ( $packageRepositoryObj['PACKAGE_ID'] !== $app->getOriginalId() ) {
            throw new Pt_I18nException(
                'PAAS-0919', "Application with id " . $app->getOriginalId() . " not found",
                ['APPLICATIONID' => $app->getOriginalId()]
            );
        }

    }
    http_setSessionObject(FIELD_IMPORT_FILE, $appXml);

    $tokens = [ "IA.THIS_APPLICATION_CONTAINS_USER_DEFINED_DIMENSIO", "IA.CLICK_CONTINUE_TO_GENERATE_AN_EMAIL_AUTHORIZATI",
                "IA.REQUIRES_AUTHORIZATION", "IA.AN_AUTHORIZATION_REQUEST_HAS_ALREADY_BEEN_SENT",
                "IA.INSTALLATION_REPORT", "IA.DONE" ];
    $placeholderTokens = [];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);

    $uddExpImpCfg = GetValueForIACFGProperty('IA_UDD_DISABLE_IMPORT_EXPORT');
    if ( $uddExpImpCfg == 'T' || !Pt_ApplicationAuthorizationController::hasUnauthorizedUDDs($appXml)
         || Pt_ApplicationAuthorizationController::selfAuthorizeUDDs($appXml) ) {
        ob_clean();
        $_REQUEST[FIELD_ACTION] = 'appInstall';
        include 'pt_setupHeader.inc';
    } else {
        if ( !Pt_ApplicationAuthorizationController::appHasRequestForAuthorization($appXml) ) {
            ?>
            <script language='JavaScript'>
                rbf_addOnLoadMethod(rbf_ShowUDDsMessage());

                function rbf_ShowUDDsMessage() {
                    var dc = '<?= GT($textMap, "IA.THIS_APPLICATION_CONTAINS_USER_DEFINED_DIMENSIO"); ?><br><br>' +
                             '<?= GT($textMap, "IA.CLICK_CONTINUE_TO_GENERATE_AN_EMAIL_AUTHORIZATI"); ?>';
                    var dt = '<?= GT($textMap, "IA.REQUIRES_AUTHORIZATION"); ?>';
                    rbf_showConfirmDialog2(dt, dc, '500px', '180px', 'Continue', function() {
                        rbf_disableAllButtons();
                        rbf_showLoadingMessage();
                        document.theForm.act.value = '<?=ACTION_APP_REQUEST_AUTHORIZATION?>';
                        document.theForm.submit();
                    });
                };
            </script>
            <?
        } else {
            ?>
            <script language='JavaScript'>
                rbf_addOnLoadMethod(rbf_ShowUDDsMessage());

                function rbf_ShowUDDsMessage() {
                    var dc = '<?= GT($textMap, "IA.THIS_APPLICATION_CONTAINS_USER_DEFINED_DIMENSIO"); ?><br><br>' +
                             '<?= GT($textMap, "IA.AN_AUTHORIZATION_REQUEST_HAS_ALREADY_BEEN_SENT"); ?>';
                    var dt = '<?= GT($textMap, "IA.REQUIRES_AUTHORIZATION"); ?>';
                    rbf_showInfoDialog2(dt, dc, '500px', '180px',function() {
                        rbf_doDone();
                    });
                };
            </script>
            <?
        }
    }

    $emptyOrSectionClass = Pt_SetupComponents::$emptyOrSectionClass;
    $rbs_roundedTableOrEmpty = Pt_SetupComponents::$rbs_roundedTableOrEmpty;
    $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
    ?>
    <script language='JavaScript'>
        function rbf_doDone() {
            window.location.href = "<?=Pt_WebUtil::url('pt_applications.phtml')?>";
            return false;
        }
    </script>
    <form action='pt_appInstall5.phtml' method='post' name='theForm' enctype="multipart/form-data"
          onSubmit='rbf_disableAllButtons();rbf_showLoadingMessage();'>
        <?= Pt_WebUtil::hidden() ?>
        <input type='hidden' name='act' value=''>
        <input type="hidden" id="hlp" name="hlp" value="About_Platform_Services">

        <table class="rbs_mainComponentTable" cellpadding=0 cellspacing=0>
            <tr>
                <td>

                    <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
                    <table class='<?=Pt_SetupComponents::$rbs_silverNoborderTableOrEmpty . $emptyOrSectionClass?>' cellpadding=0 cellspacing=0>
                        <tr>
                            <td class='center'>

                                <table>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td class='rbs_PageTopicWide'><?= GT($textMap, "IA.INSTALLATION_REPORT"); ?>&nbsp;&nbsp;&nbsp;</td>
                                        <td class='rbs_recordActionCol' nowrap>

                                            <input type="submit" class="<?=$emptyOrBtnPrimaryClass?>" value=" <?= GT($textMap, "IA.DONE"); ?> " onClick="return rbf_doDone();">&nbsp;&nbsp;

                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

                    <table height='10' class='wide'>
                        <tr>
                            <td></td>
                        </tr>
                    </table>

                    <pre>
<?
$report = http_getSessionObject(FIELD_INST_REPORT);
echo util_encode($report);
?>
</pre>

                    <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
                    <table class="<?=Pt_SetupComponents::$rbs_lightsilverTableOrEmpty . $emptyOrSectionClass?>">
                        <tr>
                            <td class='center'>

                                <table>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td class='rbs_PageTopicWide'>&nbsp;&nbsp;&nbsp;</td>
                                        <td class='rbs_recordActionCol' nowrap>

                                            <input type="submit" class="<?=$emptyOrBtnPrimaryClass?>" value=" <?= GT($textMap, "IA.DONE"); ?> " onClick="return rbf_doDone();">&nbsp;&nbsp;

                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

                </td>
            </tr>

            <tr height='10'>
                <td></td>
            </tr>
        </table>

    <?

    include 'pt_setupFooter.inc';
} catch ( Exception $ex ) {
    error($ex);
}