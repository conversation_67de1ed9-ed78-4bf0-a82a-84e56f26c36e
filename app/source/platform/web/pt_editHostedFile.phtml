<?
try {
    include 'pt_setupHeader.inc';

    $id = http_getIntParameter(FIELD_ID);
    $hf = Pt_HostedFileManager::getById($id);
    $applicationId = http_getIntParameter(FIELD_APPLICATION_ID);
    $createMode = ( $hf == null);

    $emptyOrFormControl = trim(Pt_SetupComponents::$emptyOrFormControl);
    $emptyOrSectionClass = Pt_SetupComponents::$emptyOrSectionClass;
    $rbs_roundedTableOrEmpty = Pt_SetupComponents::$rbs_roundedTableOrEmpty;
    $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
    $boldOrEmpty = Pt_SetupComponents::$boldOrEmpty;

    $tokens = [ "IA.ERROR.ERROR_PLEASE_PROVIDE_A_NAME_FOR_HOSTED_FI", "IA.ERROR.ERROR_PLEASE_UPLOAD_HOSTED_FILE",
                "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES", "IA.NEW_HOSTED_FILE", "IA.EDIT_HOSTED_FILE",
                "IA.SAVE", "IA.CANCEL", "IA.UPLOAD_FILE_TO_BE_HOSTED", "IA.DISPLAY_NAME", "IA.HOSTED_FILE",
                "IA.THE_CONTENT_DISPLAY_IS_NOT_AVAILABLE", "IA.DESCRIPTION" ];
    $placeholderTokens = [];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);
?>

<script language="JavaScript">
var changeFile = false;

function rbf_checkInput() {
    with (document.theForm) {

        rbf_clearErrors();
        var hasError = false;

        if (name.value == null || name.value == "") {
            rbf_activateError('name','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_NAME_FOR_HOSTED_FI"); ?>');
            hasError=true;
        }
        if ( changeFile && ( file.value == null || file.value == "" ) ) {
            rbf_activateError('file','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_UPLOAD_HOSTED_FILE"); ?>');
            hasError=true;
        }

        if (hasError) {
            showInfoMessage('<?= GT($textMap, "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES"); ?>',true);
            return false;
        }

        act.value = '<?=($createMode ? "hostedFileCreate" : "hostedFileUpdate")?>';
    }
    return true;
}

function removeFile() {
    changeFile = true;

    var removeFileBtn = document.getElementById('removeFileBtn');
    if ( removeFileBtn ) {
        removeFileBtn.style.display = 'none';
    }

    var viewUploadedFile = document.getElementById('viewUploadedFile');
    if ( viewUploadedFile ) {
        viewUploadedFile.style.display = 'none';
    }

    var uploadFileInput = document.getElementById('uploadFileInput');
    if ( uploadFileInput ) {
        uploadFileInput.style.display = '';
        uploadFileInput.value = '';
    }
}

</script>

<form action='<?=($applicationId>0 ? "pt_appView.phtml" : "pt_hostedFiles.phtml")?>' method='post' name='theForm' enctype="multipart/form-data" onSubmit='rbf_disableAllButtons()'>
<?=Pt_WebUtil::hidden()?>
<? if (!$createMode) { ?><input type='hidden' name='id' value='<?=$id?>'><?
} ?>
<input type="hidden" id="hlp" name="hlp" value="Hosted_files">
<input type='hidden' name='act' value=''>
<input type='hidden' name='applicationId' value='<?=$applicationId?>'>

<table class="rbs_mainComponentTable" cellpadding=0 cellspacing=0>
<tr>
    <td>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class='<?= Pt_SetupComponents::$rbs_silverNoborderTableOrEmpty . $emptyOrSectionClass ?>' cellpadding=0 cellspacing=0>
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'><?= ( $createMode ? GT($textMap, "IA.NEW_HOSTED_FILE") : GT($textMap, "IA.EDIT_HOSTED_FILE") ) ?>
                                &nbsp;&nbsp;&nbsp;
                            </td>
                            <td class='rbs_recordActionCol' nowrap>
                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.SAVE"); ?> " onClick='return rbf_checkInput()'>
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.UPLOAD_FILE_TO_BE_HOSTED"), true) ?>

        <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
            <tr>
                <td colspan=2 height='10'></td>
            </tr>
            <tr>
                <td id='name_label' class='rbs_rightLabelRequired'><?= GT($textMap, "IA.DISPLAY_NAME"); ?></td>
                <td id='name_field' class='rbs_leftDataColWide'>
                    <input class="<?= $emptyOrFormControl ?>" size='60' type="text" name="name"
                           value='<?= util_encode($hf == null ? "" : $hf->__toString()) ?>'>
                    <br><span class='rbs_alertMsg' id='name_error'></span>
                </td>
            </tr>
            <tr>
                <td id='file_label' class='rbs_rightLabelRequired'><?= GT($textMap, "IA.HOSTED_FILE"); ?></td>
                <td id='file_field' class='rbs_leftDataColWide'>
                    <b id="viewUploadedFile" style="display: <?= $createMode ? "none" : "" ?>">
                        <?= util_encode($hf == null ? "" : $hf->getFileName()) ?>
                    </b>
                    <a id="removeFileBtn" style="display: <?= $createMode ? "none" : "" ?>;margin-left: 5px;" onclick="removeFile()">
                        <?= Pt_WebUtil::getImage("deleteicon.gif", GT($textMap, "IA.THE_CONTENT_DISPLAY_IS_NOT_AVAILABLE"), 12, 12) ?>
                    </a>
                    <input size='30' type="file" name="file" id="uploadFileInput" value='' style="display: <?= $createMode ? "" : "none" ?>">
                    <br>
                    <span class='rbs_alertMsg' id='file_error'></span>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide'><?= GT($textMap, "IA.DESCRIPTION"); ?></td>
                <td class='rbs_leftDataColWide'>
                <textarea class='<?= $emptyOrFormControl ?>' rows='4' cols='60'
                          name='description'><?= util_encode($hf == null ? "" : $hf->getDescription()) ?></textarea>
                </td>
            </tr>
        </table>
        <?= Pt_WebUtil::getContainerEnd('') ?>

        <table height='<?= Pt_SetupComponents::$isQuixote ? "10" : "14" ?>' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class='<?= Pt_SetupComponents::$rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>'>
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'>&nbsp;&nbsp;&nbsp;</td>
                            <td class='rbs_recordActionCol' nowrap>
                                <input type="submit" class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.SAVE"); ?> " onClick='return rbf_checkInput()'>
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>" value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

    </td>
</tr>
    <?= Pt_SetupComponents::$trHeight10OrEmpty ?>
</table>

</form>

<script language='JavaScript'>
    document.theForm.name.focus();
    rbf_addOnLoadMethod(rbf_initializeValidationField("name"));
    rbf_addOnLoadMethod(rbf_initializeValidationField("file"));
</script>
<?
include 'pt_setupFooter.inc';
}
catch (Exception $ex) {
    error($ex);
}
