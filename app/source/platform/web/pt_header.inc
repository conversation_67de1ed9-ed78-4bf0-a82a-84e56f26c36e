<?
require_once "Pt_MainPageParams.cls";
require_once "Pt_UIComponents.cls";
require_once "Pt_ImportData.cls";
require_once "Pt_WebUtil.cls";
require_once "Pt_FilterRequestParams.inc";

require_once "util.inc";

$initparams = [ 'filters' => FILTER_REQUEST_PARAMS ];
Init($initparams);

$params = new Pt_MainPageParams();

$infoMessage = Session::getProperty(ATTR_INFO_MESSAGE);
if (isset($infoMessage)) {
    Session::deleteProperty(ATTR_INFO_MESSAGE); 
}
if ($params->hasErrors()) {
    $infoMessage = $params->getValidationMessage(); 
}

Pt_SetupComponents::initUI(QXCommon::isQuixote() && QXCommon::getPlatformLayoutTypeCondition());

?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<script language="JavaScript">rbv_startLoad = new Date();</script>
<title><?= util_encode($params->getPageTitle())?></title>
<link rel="stylesheet" type="text/css" href="../resources/css/listselect.css" />
<link rel="stylesheet" type="text/css" href="../resources/css/intacct_styles_new.css" />
<link rel="stylesheet" type="text/css" href="../resources/css/pt_browntheme.css" />
<link rel="stylesheet" type="text/css" href="../resources/wysiwyg/css/wysiwyg.css" />

<link rel="stylesheet" type="text/css" href="../resources/thirdparty/yui/css/button.css" />
<link rel="stylesheet" type="text/css" href="../resources/thirdparty/yui/css/container.css" />
<link rel="stylesheet" type="text/css" href="../resources/thirdparty/yui/css/resize.css" />
<link rel="stylesheet" type="text/css" href="../resources/thirdparty/yui/css/skin.css" />
<link rel="stylesheet" type="text/css" href="../resources/thirdparty/jquery-ui/themes/base/jquery-ui.min.css" />
<link rel="stylesheet" type="text/css" href="../resources/thirdparty/fancybox/jquery.fancybox-1.3.4.css" media="screen" />
<link rel="stylesheet" type="text/css" href="../resources/css/datepicker.css" />

<? if ( Pt_SetupComponents::$isQuixote ) { ?>
    <link href="../resources/thirdparty/font-awesome-pro/css/fontawesome-all.css" rel="stylesheet">
    <link rel="stylesheet" href="../resources/thirdparty/bootstrap-3/css/bootstrap.min.css">
    <link href="../resources/thirdparty/bootstrap-select-1.13.18/dist/css/bootstrap-select.css" rel="stylesheet"
          type="text/css">
    <link href="../resources/thirdparty/bootstrap-select-1.13.18/dist/css/bootstrap-select.min.css" rel="stylesheet"
          type="text/css">
    <link href="../resources/minus/qxlister.min.css" rel="stylesheet" type="text/css">
    <link href="../resources/minus/qxplatform.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="../resources/thirdparty/Fonts-com/Fonts/css/HelveticaNeue.css">
<? } ?>
<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv="content-type" content="text/html; charset=<?=isl_get_charset()?>">

<?
addTextMapForCommonFiles();
jsCommonIncludes();

if ( Pt_SetupComponents::$isQuixote ) { ?>
    <script type="text/javascript" src="../resources/qx/forms/js/QXComboBox2.js"></script>
<? } else { ?>
    <script type="text/javascript" src="../resources/js/ComboBox2.js"></script>
<? } ?>
<script type="text/javascript" src="../resources/js/editor.js"></script>
<script type="text/javascript" src="../resources/js/form_editor.js"></script>
<script type="text/javascript" src="../resources/js/common_helper.js"></script>
<script type="text/javascript" src="../resources/js/chatterscripts.js"></script>
<script type="text/javascript" src="../resources/js/platform/pt_utilities.js"></script>
<script type="text/javascript" src="../resources/js/platform/pt_common.js"></script>
<script type="text/javascript" src="../resources/js/platform/pt_calpopup.js"></script>
<script type="text/javascript" src="../resources/js/platform/pt_dynamicdropdown.js"></script>
<script type="text/javascript" src="../resources/js/platform/pt_dialog.js"></script>
<script type="text/javascript" src="../resources/js/platform/pt_utilities.js"></script>
<script type="text/javascript" src="../resources/js/platform/pt_listView.js"></script>
<script type="text/javascript" src="../resources/js/platform/API_Session.js"></script>
<script type="text/javascript" src="../resources/js/base_lib.js"></script>
<script type="text/javascript" src="../resources/wysiwyg/js/wysiwyg.js"></script>

<script type="text/javascript" src="../resources/js/polyfill/promise.js"></script>
<script type="text/javascript" src="../resources/js/qrequest.js"></script>


<script type="text/javascript" src="../resources/thirdparty/yui/yahoo-dom-event.js"></script>
<script type="text/javascript" src="../resources/thirdparty/yui/animation.js"></script>
<script type="text/javascript" src="../resources/thirdparty/yui/dragdrop.js"></script>
<script type="text/javascript" src="../resources/thirdparty/yui/utilities.js"></script>
<script type="text/javascript" src="../resources/thirdparty/yui/button.js"></script>
<script type="text/javascript" src="../resources/thirdparty/yui/container.js"></script>
<script type="text/javascript" src="../resources/thirdparty/yui/resize.js"></script>
<script type="text/javascript" src="../resources/thirdparty/yui/logger.js"></script>



<script type="text/javascript" src="../resources/thirdparty/bigdecimal/BigDecimal-all-last.js"></script>
<script type="text/javascript" src="../resources/thirdparty/datejs/date.js"></script>
<script type="text/javascript" src="../resources/js/calculator.js"></script>
<script type="text/javascript" src="../resources/js/calendar.js"></script>
<script type="text/javascript" src="../resources/js/js_dates.js"></script>
<script type="text/javascript" src="../resources/js/js_auto_dates.js"></script>
<script src="../resources/thirdparty/fancybox/jquery.mousewheel-3.1.13.min.js"></script>
<script src="../resources/thirdparty/fancybox/fancybox.js"></script>
<script src="../resources/thirdparty/jquery/jquery.zoom.min.js"></script>
<?InitJSGlobals();?>
<script language="JavaScript">
var gChatterXsrfToken = '<?= ChatterInterface::generateCSRFToken() ?>';
var gCSSButtonPath = '<? echo IALayoutManager::getCSSButtonPath(); ?>';
<?
require 'pt_commonIncl.inc';
?>
function getAjaxURL() {
    return "<?=Pt_WebUtil::url(BaseUrl().'pt_Ajax.phtml', OP_RUNTIME)?>&rnd="+Math.random();
}
function getAjaxCsrfToken()
{
    return '<?= Pt_WebUtil::generateCsrfToken() ?>';
}

<?= Pt_SetupComponents::getInitPageLayout() ?>
</script>

</head>
<body onload='rbf_runOnLoadMethods();' class='yui-skin-sam <?=trim(Pt_SetupComponents::$emptyOrQuixote) . Pt_SetupComponents::$bodyBorderOrEmpty ?>'>

<?

//  If this object can have a portable URl, add it as a hidden input.
$data = $params->getData();
if ($data) {
    $portableUrl = $data->getFieldValue(FIELD_RECORD_URL);
    if (!empty($portableUrl)) {
        ?><INPUT type="hidden" id="portableUrl" value="<? echo $portableUrl; ?>"><?
    }
}

require 'pt_dialog.inc'; ?>

<table class='center mozcenter <?=Pt_SetupComponents::$orangetableOrEmpty?>' id='rb_infoMessage' style='display:none' cellspacing=0 cellpadding=0 width="100%"><tr><td class='center mozcenter'>
    <table class='<?=Pt_SetupComponents::$rbs_infoMessageOrEmpty . Pt_SetupComponents::$emptyOrEditorMessage?>' cellpadding=0 cellspacing=0>
        <?=Pt_SetupComponents::$isQuixote ? "" : "<tr><td width=5 height=5 class='topleftcorner'></td><td></td><td width=5 height=5 class='toprightcorner'></td></tr>"?>
        <tr class="<?= Pt_SetupComponents::$emptyOrHelpWarning ?>"><td nowrap>&nbsp;&nbsp;&nbsp;</td><td id='rb_infoMessageText' class='bold' align="center"></td><?= Pt_SetupComponents::$mesageAfterSpacing ?></tr>
        <?=Pt_SetupComponents::$isQuixote ? "" : "<tr><td width=5 height=5 class='bottomleftcorner'></td><td></td><td width=5 height=5 class='bottomrightcorner'></td></tr>"?>
    </table>
</td></tr></table>

<?
if ( util_inArgs($params->getPageName(), 'pt_templateView.phtml', 'pt_mailTemplEdit.phtml', 'pt_docTemplEdit.phtml') ) {
    $navigationLinks = $params->getNavigationLinks();
    if ( $navigationLinks ) { ?>
        <table class='<?= Pt_SetupComponents::$rbs_whitePaddedGrayBottomTableOrEmpty
                          . Pt_SetupComponents::$emptyOrBreadcrumb ?>' height='25'>
            <tr>
                <td class='normalSmallLinks <?= Pt_SetupComponents::$smallOrEmpty ?> silver' nowrap>
                    &nbsp;<?= $navigationLinks ?>
                    <script>breadcrumbHref = '<?= htmlspecialchars($params->get2011BreadcrumbLink(),
                                                                   ENT_QUOTES) ?>';</script>
                </td>
            </tr>
        </table>
    <? }
}

echo Pt_UIComponents::getTopLinksRow($params);
