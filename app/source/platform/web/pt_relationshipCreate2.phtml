<?
try {
    include 'pt_setupHeader.inc';

    include_once "Pt_DataObjectDefManager.cls";
    include_once "Pt_RelationshipDefManager.cls";

    $objDefId1 = http_getIntParameter(FIELD_OBJ_DEF_ID);
    $objDef1 = Pt_DataObjectDefManager::getById($objDefId1);
    if ($objDef1 == null) {
        throw new Pt_I18nException('PAAS-0782', "Object definition with id $objDefId1 not found",
                                   [ 'OBJDEFID' => $objDefId1 ] );

    }
    $objDef1HasEditor = $objDef1->hasEditorPage();
    $objName1  = util_encode($objDef1->getSingularName());
    $objName1s = util_encode($objDef1->getPluralName());
    $pages1 = Pt_WebPageManager::getByObjectDef($objDef1->getId());
    $isStandardObj1 = $objDef1 instanceof Pt_StdDataObjectDef;

    $objDefId2 = http_getIntParameter(FIELD_OBJ_DEF_ID2);
    $objDef2 = Pt_DataObjectDefManager::getById(intval($objDefId2));
    if ($objDef2 == null) {
        throw new Pt_I18nException('PAAS-0783', "Destination object definition not found");
    }
    $objDef2HasEditor = $objDef2->hasEditorPage();
    $objName2  = util_encode($objDef2->getSingularName());
    $objName2s = util_encode($objDef2->getPluralName());
    $objSystem2 = $objDef2->isSystem();
    $pages2 = Pt_WebPageManager::getByObjectDef($objDef2->getId());
    $isStandardObj2 = $objDef2 instanceof Pt_StdDataObjectDef;
    $customLabels = Pt_RelationshipDefManager::getCustomLabelsDefaults($objDef1, $objDef2);

    $canClone = $objDef1->isPlatform() && $objDef2->isPlatform();

    $rels = Pt_Cache::getRelationshipDefs();
    $usedNames = '';
    foreach ( $rels as $rel ) {
        $usedNames .= isl_strtoupper(util_encode($rel->getRelationshipName())) . ' ';
    }
    foreach ( $objDef1->getFields() as $field ) {
        if ( $field ) {
            $usedNames .= isl_strtoupper(util_encode($field->getFieldName())) . ' ';
        }
    }
    foreach ( $objDef2->getFields() as $field ) {
        if ( $field ) {
            $usedNames .= isl_strtoupper(util_encode($field->getFieldName())) . ' ';
        }
    }

    $emptyOrSectionClass = Pt_SetupComponents::$emptyOrSectionClass;
    $rbs_lightsilverTableOrEmpty = Pt_SetupComponents::$rbs_lightsilverTableOrEmpty;
    $emptyOrFormControl = Pt_SetupComponents::$emptyOrFormControl;
    $emptyOrTrimmedFormControl = trim($emptyOrFormControl);
    $rbs_roundedTableOrEmpty = Pt_SetupComponents::$rbs_roundedTableOrEmpty;
    $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
    $boldOrEmpty = Pt_SetupComponents::$boldOrEmpty;
    $emptyOrDisplayNone = Pt_SetupComponents::$emptyOrDisplayNone;
    $emptyOrDisplayBlock = Pt_SetupComponents::$emptyOrDisplayBlock;
    $nbspOrEmpty = Pt_SetupComponents::$isQuixote ? "" : "&nbsp;";

    $tokens = ["IA.ERROR.ERROR_PLEASE_PROVIDE_A_SINGULAR_NAME","IA.ERROR.ERROR_PLEASE_PROVIDE_A_PLURAL_NAME", "IA.ERROR.ERROR_THIS_FIELD_NAME_IS_ALREADY_IN_USE_P",
        "IA.ERROR.ERROR_THIS_INTEGRATION_NAME_IS_ALREADY_IN", "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES", "IA.SAVE", "IA.CANCEL",
        "IA.RELATIONSHIP_PROPERTIES", "IA.SINGULAR_NAME", "IA.PLURAL_NAME", "IA.THE_INTEGRATION_NAME_IS_USED_TO_REFERENCE_THIS", "IA.INTEGRATION_NAME",
        "IA.RELATIONSHIP_TYPE", "IA.RELATIONSHIP_TYPE_DETERMINES_WHETHER_RECORDS_OF", "IA.RELATIONSHIP_TYPE", "IA.YOU_HAVE_SELECTED_A_MANY_TO_MANY_RELATIONSHIP_M",
        "IA.ORPHAN_RECORDS_CONTROL_ALLOWS_YOU_TO_SPECIFY_WH", "IA.ORPHAN_RECORDS_CONTROL", "IA.CLONING_CONTROL","IA.CLONING_CONTROL_ALLOWS_YOU_TO_SPECIFY_WHETHER_O",
        "IA.DELETING_RECORDS_CONTROL", "IA.PROPERTIES_OF_LOOKUP_FIELDS", "IA.THIS_MAY_INCREASE_YOUR_DATABASE_SPACE_USAGE_IF",
        "IA.THIS_FIELD_IS_REQUIRED_IN_ALL_FORMS","IA.THIS_FIELD_CAN_BE_USED_IN_FILTER_EXPRESSIONS", "IA.THIS_FIELD_CAN_BE_USED_AS_A_COLUMN_IN_VIEWS",
        "IA.THIS_FIELD_CAN_BE_USED_AS_A_MERGE_FIELD_IN_TEMP", "IA.DO_NOT_CLONE_THIS_FIELD_WHEN_RECORD_IS_CLONED", "IA.ADD_LOOKUP_FIELDS_TO_PAGES", "IA.PAGES", "IA.LOOKUP_FIELD",
        "IA.ADD_RELATED_LIST_VIEWS_TO_PAGES", "IA.RELATED_LIST_VIEW_COMPONENTS_WILL_AUTOMATICALLY", "IA.ERROR.ERROR_PLEASE_PROVIDE_AN_INTEGRATION_NAME",
        "IA.TRACK_ALL_CHANGES_TO_THIS_FIELD_IN_EACH_RECORD", "IA.SELECT_ALL", "IA.THE_INTEGRATION_NAME_CAN_ONLY_CONTAIN_ALPHANUME"];

    // I18N: TODO refactor IA.PLEASE_SPECIFY_SINGULAR_AND_PLURAL_NAMES_FOR_T2 with a context for IA.PLEASE_SPECIFY_SINGULAR_AND_PLURAL_NAMES_FOR_TH
    $placeholderTokens = [
        [
            'id'           => 'IA.NEW_RELATIONSHIP_WITH',
            'placeHolders' => [
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
            ],
        ],
        [
            'id'           => 'IA.PLEASE_SPECIFY_SINGULAR_AND_PLURAL_NAMES_FOR_TH',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
            ],
        ],
        [
            'id'           => 'IA.PLEASE_SPECIFY_SINGULAR_AND_PLURAL_NAMES_FOR_T2',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
            ],
        ],
        [
            'id'           => 'IA.ONE_OBJNAME_TO_ONE_OBJNAME2',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => 'IA.ONE_OBJNAME_TO_MANY_OBJNAME2S',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2S', 'value' => $objName2s ],
                
            ],
        ],
        [
            'id'           => 'IA.MANY_OBJNAME1S_TO_ONE_OBJNAME2',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1S', 'value' => $objName1s ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => "IA.MANY_OBJNAME1S_TO_MANY_OBJNAME2S",
            'placeHolders' => [
                [ 'name' => 'OBJNAME1S', 'value' => $objName1s ],
                [ 'name' => 'OBJNAME2S', 'value' => $objName2s ],
                
            ],
        ],
        [
            'id'           => 'IA.DELETE_OBJNAME_RECORDS_WHEN_RELATED_OBJNAME2_REC',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => 'IA.DELETE_OBJNAME2_RECORDS_WHEN_RELATED_OBJNAME_REC',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => 'IA.CLONE_ALL_RELATED_OBJNAME1S_WHEN_ANY_OBJNAME2_RE',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1S', 'value' => $objName1s ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => 'IA.CLONE_ALL_RELATED_OBJNAME2S_WHEN_ANY_OBJNAME1_RE',
            'placeHolders' => [
                [ 'name' => 'OBJNAME2S', 'value' => $objName2s ],
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ]
                
            ],
        ],
        [
            'id'           => 'IA.DO_NOT_ALLOW_DELETING_OBJNAME_RECORD_WHICH_HAS',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ], 
        [
            'id'           => 'IA.DO_NOT_ALLOW_DELETING_OBJNAME2_RECORD_WHICH_HAS',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => 'IA.PLEASE_SPECIFY_LOOKUP_FIELD_PROPERTIES_FOR_THE',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => 'IA.PLEASE_SPECIFY_LOOKUP_FIELD_PROPERTIES_FOR_TH2',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
                
            ],
        ],
        [
            'id'           => 'IA.OBJNAME_PAGES_OBJNAME_LOOKUP_FIELD:OBJNAME1_OBJNAME2',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName2 ],
    
            ],
        ],
        [
            'id'           => 'IA.OBJNAME_PAGES_OBJNAME_LOOKUP_FIELD:OBJNAME2_OBJNAME1',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName2 ],
                [ 'name' => 'OBJNAME2', 'value' => $objName1 ],
    
            ],
        ],
        [
            'id'           => 'IA.OBJNAME_PAGES_OBJNAME_S_RELATED_LIST_VIEW:OBJNAME1_OBJNAME2S',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName1 ],
                [ 'name' => 'OBJNAME2S', 'value' => $objName2s ],
    
            ],
        ],
        [
            'id'           => 'IA.OBJNAME_PAGES_OBJNAME_S_RELATED_LIST_VIEW:OBJNAME2_OBJNAME1S',
            'placeHolders' => [
                [ 'name' => 'OBJNAME1', 'value' => $objName2 ],
                [ 'name' => 'OBJNAME2S', 'value' => $objName1s ],
    
            ],
        ],
    ];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);

?>

<script language="JavaScript" src="../resources/js/platform/pt_checkBoxes.js"></script>
<?=Pt_I18nLabelsUI::getGenericHTML(); ?>
<script language="JavaScript">

var usedNames = "<?=$usedNames?>";
usedNames = usedNames.replace(/^\s*/, '').replace(/\s*$/, '');
var namesArray = usedNames.split(" ");

<?
$numFields1 = $objDef1->getNumFields();
$rels1 = Pt_RelationshipDefManager::getByObjectDef($objDefId1);
echo "var usedFields1 = new Array(" . ( $numFields1 + count($rels1) ) . ");\n";
$k = 0;
foreach ( $objDef1->getFields() as $field ) {
    if ( $field ) {
        echo "usedFields1[" . $k . "]='" . util_jsEncode(isl_strtoupper($field->getFieldName())) . "';\n";
        $k++;
    }
}
foreach ( $rels1 as $rel ) {
    echo "usedFields1[" . $k . "]='" . util_jsEncode(isl_strtoupper($rel->getRelationshipName())) . "';\n";
    $k++;
}

$numFields2 = $objDef2->getNumFields();
$rels2 = Pt_RelationshipDefManager::getByObjectDef($objDefId2);
echo "var usedFields2 = new Array(" . ( $numFields2 + count($rels2) ) . ");\n";
$k = 0;
foreach ( $objDef2->getFields() as $field ) {
    if ( $field ) {
        echo "usedFields2[" . $k . "]='" . util_jsEncode(isl_strtoupper($field->getFieldName())) . "';\n";
        $k++;
    }
}
foreach ( $rels2 as $rel ) {
    echo "usedFields2[" . $k . "]='" . util_jsEncode(isl_strtoupper($rel->getRelationshipName())) . "';\n";
    $k++;
}
?>

function rbf_inNamesArray(name, arr, useUpperCase) {
    if (name==null || name.length==0)
        return false;
    if (useUpperCase)
        name = name.toUpperCase();
    for (var i=0; i<arr.length; i++) {
        if (name == arr[i]) {
            return true;
        }
    }
    return false;
}

function rbf_checkInput() {
    with (document.theForm) {

        rbf_clearErrors();
        var hasError = false;

        if (singularName1.value == "") {
            rbf_activateError('singularName1','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_SINGULAR_NAME"); ?>');
            hasError=true;
        }
        if (pluralName1.value == "") {
            rbf_activateError('pluralName1','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_PLURAL_NAME"); ?>');
            hasError=true;
        }
        if (name1.value == "") {
            rbf_activateError('name1','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_AN_INTEGRATION_NAME"); ?>');
            hasError=true;
        }
        if (rbf_inNamesArray(name1.value, usedFields2, true)) {
            rbf_activateError('name1','<?= GT($textMap, "IA.ERROR.ERROR_THIS_FIELD_NAME_IS_ALREADY_IN_USE_P"); ?>');
            hasError=true;
        }

        if (singularName2.value == "") {
            rbf_activateError('singularName2','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_SINGULAR_NAME"); ?>');
            hasError=true;
        }
        if (pluralName2.value == "") {
            rbf_activateError('pluralName2','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_PLURAL_NAME"); ?>');
            hasError=true;
        }
        if (name2.value == "") {
            rbf_activateError('name2','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_AN_INTEGRATION_NAME"); ?>');
            hasError=true;
        }
        if (rbf_inNamesArray(name2.value, usedFields1, true)) {
            rbf_activateError('name2','<?= GT($textMap, "IA.ERROR.ERROR_THIS_FIELD_NAME_IS_ALREADY_IN_USE_P"); ?>');
            hasError=true;
        }
        if ( !validateIntegrationName(name2.value) ) {
            rbf_activateError('name2', '<?= GT($textMap, "IA.ERROR") . ": " . GT($textMap, "IA.THE_INTEGRATION_NAME_CAN_ONLY_CONTAIN_ALPHANUME"); ?>');
            hasError = true;
        }
        if ( !validateIntegrationName(name1.value) ) {
            rbf_activateError('name1', '<?= GT($textMap, "IA.ERROR") . ": " . GT($textMap, "IA.THE_INTEGRATION_NAME_CAN_ONLY_CONTAIN_ALPHANUME"); ?>');
            hasError = true;
        }
        if ( !validateIntegrationName(relName.value) ) {
            rbf_activateError('relName', '<?= GT($textMap, "IA.ERROR") . ": " . GT($textMap, "IA.THE_INTEGRATION_NAME_CAN_ONLY_CONTAIN_ALPHANUME"); ?>');
            hasError = true;
        }
        if (rbf_inNamesArray(relName.value, namesArray, true)) {
            rbf_activateError('relName','<?= GT($textMap, "IA.ERROR.ERROR_THIS_INTEGRATION_NAME_IS_ALREADY_IN"); ?>');
            hasError=true;
        }

        if (hasError) {
            showInfoMessage('<?= GT($textMap, "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES"); ?>',true);
            return false;
        }

        act.value = "relationshipCreate";
    }

    return true;
}

function enableCheckboxes() {
    var card = document.theForm.multiple.value;
    showCheckboxes('field1_', card=='0' || card=='2');
    showCheckboxes('field2_', card=='0' || card=='1');
    showCheckboxes('table1_', card=='1' || card=='3');
    showCheckboxes('table2_', card=='2' || card=='3');

    var cell = document.getElementById("many_to_many_warn");
    cell.style.display = (card=="3" ? "block" : "none");

    var noclone2 = document.getElementById("noClone2");
    var noclone1 = document.getElementById("noClone1");

    if (card == '0') {
        noclone2.checked = true;
        noclone1.checked = true;
    } else if (card == '1') {
        noclone2.checked = true;
        noclone1.checked = false;
    } else if (card == '2') {
        noclone2.checked = false;
        noclone1.checked = true;
    } else if (card == '3') {
        noclone2.checked = false;
        noclone1.checked = false;
    }

}

function showCheckboxes(prefix, flag) {
    var sl = document.theForm;
    var len = sl.elements.length;
    for (var k=0; k<len; k++) {
        var ch = sl.elements[k];
        if (ch.type == "checkbox" && ch.name.indexOf(prefix)==0) {
            ch.checked = flag;
        }
    }
}

function synchName() {
    with (document.theForm) {
        updateCustomLabelWrapper(singularName1, '<?=Pt_RelationshipDef::SINGULAR1_LABELS_IDX?>');
        updateCustomLabelWrapper(pluralName1, '<?=Pt_RelationshipDef::PLURAL1_LABELS_IDX?>');
        updateCustomLabelWrapper(singularName2, '<?=Pt_RelationshipDef::SINGULAR2_LABELS_IDX?>');
        updateCustomLabelWrapper(pluralName2, '<?=Pt_RelationshipDef::PLURAL2_LABELS_IDX?>');
    }
}
</script>

<INPUT type="hidden" id="hlp" name="hlp" value="Adding_a_Relationship">

<form action='pt_objectView.phtml' method='post' name='theForm' onSubmit='rbf_disableAllButtons()'>
<?=Pt_WebUtil::hidden()?>
<input type='hidden' name='act' value=''>
<input type='hidden' name='objDefId' value='<?=$objDefId1?>'>
<input type='hidden' name='objDefId2' value='<?=$objDefId2?>'>
<?= Pt_I18nLabelsUI::getInputs($customLabels) ?>

<table class="rbs_mainComponentTable" cellpadding=0 cellspacing=0>

<tr>
    <td class="wide">

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class="<?= $rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'><?= $objName1 ?>:
                            <?= GT($textMap, "IA.NEW_RELATIONSHIP_WITH"); ?>&nbsp;&nbsp;&nbsp;
                            </td>
                            <td class='rbs_recordActionCol' nowrap>
                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.SAVE"); ?> " onClick='return rbf_checkInput()'>&nbsp;&nbsp;
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

    <?=Pt_WebUtil::getContainerBegin($emptyOrSectionClass);?>
    <?=Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.RELATIONSHIP_PROPERTIES"), true)?>

    <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>

    <tr>
        <td class='rbs_grayDetailInfoCol' colspan='2'><?= GT($textMap, "IA.PLEASE_SPECIFY_SINGULAR_AND_PLURAL_NAMES_FOR_TH"); ?></td>
    </tr>

    <tr>
        <td class='rbs_rightLabelRequired'><?= GT($textMap, "IA.SINGULAR_NAME"); ?></td>
        <td id='singularName1_field' class='rbs_leftDataColWide' nowrap>
            <input type='text' class='<?=$emptyOrTrimmedFormControl?>' name='singularName1' value='<?=$objName1?>'
                   maxlength='50' onblur='return synchName();'/>
            <span class='rbs_alertMsg' id='singularName1_error'></span>
            &nbsp;&nbsp;&nbsp;
            <?= Pt_I18nLabelsUI::getEditorLink(FIELD_SINGULAR_NAME1, Pt_RelationshipDef::SINGULAR1_LABELS_IDX) ?>
        </td>
    </tr>

    <tr>
        <td class='rbs_rightLabelRequired'><?= GT($textMap, "IA.PLURAL_NAME"); ?></td>
        <td id='pluralName1_field' class='rbs_leftDataColWide' nowrap>
            <input type='text' class='<?=$emptyOrTrimmedFormControl?>' name='pluralName1' value='<?=$objName1s?>'
                   maxlength='50' onblur='return synchName();'/>
            <span class='rbs_alertMsg' id='pluralName1_error'></span>
            &nbsp;&nbsp;&nbsp;
            <?= Pt_I18nLabelsUI::getEditorLink(FIELD_PLURAL_NAME1, Pt_RelationshipDef::PLURAL1_LABELS_IDX) ?>
        </td>
    </tr>

    <tr>
        <td class='rbs_grayDetailInfoCol' colspan='2'><?= GT($textMap, "IA.PLEASE_SPECIFY_SINGULAR_AND_PLURAL_NAMES_FOR_T2"); ?></td>
    </tr>

    <tr>
        <td class='rbs_rightLabelRequired'><?= GT($textMap, "IA.SINGULAR_NAME"); ?></td>
        <td id='singularName2_field' class='rbs_leftDataColWide' nowrap>
            <input type='text' class='<?=$emptyOrTrimmedFormControl?>' name='singularName2' value='<?=$objName2?>'
                   maxlength='50' onblur='return synchName();'/>
            <span class='rbs_alertMsg' id='singularName2_error'></span>
            &nbsp;&nbsp;&nbsp;
            <?= Pt_I18nLabelsUI::getEditorLink(FIELD_SINGULAR_NAME2, Pt_RelationshipDef::SINGULAR2_LABELS_IDX) ?>
        </td>
    </tr>

    <tr>
        <td class='rbs_rightLabelRequired'><?= GT($textMap, "IA.PLURAL_NAME"); ?></td>
        <td id='pluralName2_field' class='rbs_leftDataColWide' nowrap>
            <input type='text' class='<?=$emptyOrTrimmedFormControl?>' name='pluralName2' value='<?=$objName2s?>'
                   maxlength='50' onblur='return synchName();'/>
            <span class='rbs_alertMsg' id='pluralName2_error'></span>
            &nbsp;&nbsp;&nbsp;
            <?= Pt_I18nLabelsUI::getEditorLink(FIELD_PLURAL_NAME2, Pt_RelationshipDef::PLURAL2_LABELS_IDX) ?>
        </td>
    </tr>

    <tr>
        <td colspan='2' class='rbs_grayDetailHTMLcol'><?= GT($textMap, "IA.THE_INTEGRATION_NAME_IS_USED_TO_REFERENCE_THIS"); ?>
</td>
    </tr>

    <tr>
        <td id='relName_label' class='rbs_rightLabelWide'><?= GT($textMap, "IA.INTEGRATION_NAME"); ?></td>
        <td id='relName_field' class='rbs_leftDataColWide'><input type='text' class='<?=$emptyOrTrimmedFormControl?>' name='relName' value='' maxlength='50'/>
        &nbsp;&nbsp;&nbsp;<br><span class='rbs_alertMsg' id='relName_error'></span></td>
    </tr>

    <tr height='10'><td></td></tr>

    </table>
        <?=Pt_WebUtil::getContainerEnd('');?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.RELATIONSHIP_TYPE")) ?>

        <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
            <tr>
                <td colspan='2' class='rbs_grayDetailHTMLcol'><?= GT($textMap, "IA.RELATIONSHIP_TYPE_DETERMINES_WHETHER_RECORDS_OF"); ?><br>
                    <?= Pt_WebUtil::getImage("relationshipCardinality.gif", "Relationship Type", 637, 113) ?><br>
                    <table width='637' cellpadding=0 cellspacing=2>
                        <tr>
                            <td width='25%' class='rbs_silverBold'><?= GT($textMap, "IA.ONE_OBJNAME_TO_ONE_OBJNAME2"); ?></td>
                            <td width='25%' class='rbs_silverBold'><?= GT($textMap, "IA.ONE_OBJNAME_TO_MANY_OBJNAME2S"); ?></td>
                            <td width='25%' class='rbs_silverBold'><?= GT($textMap, "IA.MANY_OBJNAME1S_TO_ONE_OBJNAME2"); ?></td>
                            <td width='25%' class='rbs_silverBold'><?=  GT($textMap,"IA.MANY_OBJNAME1S_TO_MANY_OBJNAME2S"); ?></td>
                        </tr>
                    </table>
                    <br>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelRequired'><?= GT($textMap, "IA.RELATIONSHIP_TYPE"); ?></td>
                <td class='rbs_leftDataColWide'>
                    <select class='<?= Pt_SetupComponents::$emptyOrSelectPicker . $emptyOrFormControl ?>' name='multiple'
                            onChange='enableCheckboxes()'>
                        <?= Pt_WebUtil::getOption("0", GT($textMap, "IA.ONE_OBJNAME_TO_ONE_OBJNAME2"), "2") ?>
                        <?= Pt_WebUtil::getOption("1", GT($textMap, "IA.ONE_OBJNAME_TO_MANY_OBJNAME2S"), "2") ?>
                        <?= Pt_WebUtil::getOption("2", GT($textMap, "IA.MANY_OBJNAME1S_TO_ONE_OBJNAME2"), "2") ?>
                        <?= Pt_WebUtil::getOption("3", GT($textMap, "IA.MANY_OBJNAME1S_TO_MANY_OBJNAME2S"), "2") ?>
                    </select>
                </td>
            </tr>

            <tr height='10'>
                <td></td>
            </tr>
        </table>

    <div id="many_to_many_warn" style='display:none'>
    <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
    <tr>
        <td ><?= GT($textMap,"IA.YOU_HAVE_SELECTED_A_MANY_TO_MANY_RELATIONSHIP_M"); ?></td>
    </tr>
    <tr height='10'><td colspan='2'></td></tr>
    </table>
    </div>
    <?=Pt_WebUtil::getContainerEnd('')?>

        <table height='10'>
            <tr>
                <td></td>
            </tr>
        </table>

<? if ($canClone) { ?>
    <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
    <?= Pt_WebUtil::getThinSectionTop( GT($textMap,"IA.ORPHAN_RECORDS_CONTROL")) ?>

    <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
        <tr>
            <td colspan='2' class='rbs_grayDetailHTMLcol'><?= GT($textMap,"IA.ORPHAN_RECORDS_CONTROL_ALLOWS_YOU_TO_SPECIFY_WH"); ?></td>
        </tr>
        <tr>
            <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
            <td class='rbs_leftDataColWide'><?= Pt_WebUtil::getCheckbox(
                    "orphan1", false, "orphan1",
                    GT($textMap,"IA.DELETE_OBJNAME_RECORDS_WHEN_RELATED_OBJNAME2_REC")
                ) ?></td>
        </tr>
        <tr>
            <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
            <td class='rbs_leftDataColWide'><?= Pt_WebUtil::getCheckbox(
                    "orphan2", false, "orphan2",
                    GT($textMap,"IA.DELETE_OBJNAME2_RECORDS_WHEN_RELATED_OBJNAME_REC")
                ) ?></td>
        </tr>

        <tr height='10'>
            <td></td>
        </tr>
    </table>
    <?= Pt_WebUtil::getContainerEnd('') ?>

    <table height='10'>
        <tr>
            <td></td>
        </tr>
    </table>

    <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
    <?= Pt_WebUtil::getThinSectionTop( GT($textMap,"IA.CLONING_CONTROL")) ?>

    <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
        <tr>
            <td colspan='2' class='rbs_grayDetailHTMLcol'><?= GT($textMap,"IA.CLONING_CONTROL_ALLOWS_YOU_TO_SPECIFY_WHETHER_O"); ?>
            </td>
        </tr>
        <tr>
            <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
            <td class='rbs_leftDataColWide'><?= Pt_WebUtil::getCheckbox(
                    "clone1", false, "clone1",
                    GT($textMap,"IA.CLONE_ALL_RELATED_OBJNAME1S_WHEN_ANY_OBJNAME2_RE")
                ) ?></td>
        </tr>
        <tr>
            <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
            <td class='rbs_leftDataColWide'><?= Pt_WebUtil::getCheckbox(
                    "clone2", false, "clone2",
                    GT($textMap,"IA.CLONE_ALL_RELATED_OBJNAME2S_WHEN_ANY_OBJNAME1_RE")
                ) ?></td>
        </tr>

        <tr height='10'>
            <td></td>
        </tr>
    </table>
    <?
} ?><?= Pt_WebUtil::getContainerEnd(''); ?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop( GT($textMap,"IA.DELETING_RECORDS_CONTROL")) ?>

        <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td class='rbs_leftDataColWide'><?= Pt_WebUtil::getCheckbox(
                        FIELD_DEL_REC1, false, FIELD_DEL_REC1,
                        GT($textMap,"IA.DO_NOT_ALLOW_DELETING_OBJNAME_RECORD_WHICH_HAS")
                    ) ?></td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td class='rbs_leftDataColWide'><?= Pt_WebUtil::getCheckbox(
                        FIELD_DEL_REC2, false, FIELD_DEL_REC2,
                        GT($textMap,"IA.DO_NOT_ALLOW_DELETING_OBJNAME2_RECORD_WHICH_HAS")
                    ) ?></td>
            </tr>

            <tr height='10'>
                <td></td>
            </tr>
        </table>
        <?= Pt_WebUtil::getContainerEnd(''); ?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.PROPERTIES_OF_LOOKUP_FIELDS")) ?>

        <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
            <tr>
                <td colspan='2' class='rbs_grayDetailHTMLcol'>
                <?= GT($textMap, "IA.PLEASE_SPECIFY_LOOKUP_FIELD_PROPERTIES_FOR_THE"); ?>
                </td>
            </tr>
            <tr class="<?=$emptyOrDisplayBlock?>">
                <td class='rbs_rightLabelRequired'><?= GT($textMap, "IA.INTEGRATION_NAME"); ?></td>
                <td id='name2_field' class='rbs_leftDataColWide' nowrap>
                    <input type='text' class='<?= $emptyOrTrimmedFormControl ?>' name='name2'
                           value='R<?= $objDef2->getObjectDefName(true) ?>' maxlength='30'/>
                    <br><span class='rbs_alertMsg' id='name2_error'></span>
                </td>
            </tr>

            <? if ( $objDef1->isAuditable() ) { ?>
                <tr>
                    <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                    <td id='isaudit2_field' class='rbs_leftDataColWide' nowrap>
                        <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isAuditable2'/><label>".GT($textMap, "IA.TRACK_ALL_CHANGES_TO_THIS_FIELD_IN_EACH_RECORD")."</label>", "checkbox");?>
                        <br><span class='rbs_smallGray'><?= GT($textMap, "IA.THIS_MAY_INCREASE_YOUR_DATABASE_SPACE_USAGE_IF"); ?></span>
                    </td>
                </tr>
            <? } ?>

            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='isrequired2_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isRequired2'><label>".GT($textMap, "IA.THIS_FIELD_IS_REQUIRED_IN_ALL_FORMS")."</label>",
                                                        "checkbox"); ?></td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='issearchable2_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isSearchable2' checked/><label>".GT($textMap, "IA.THIS_FIELD_IS_REQUIRED_IN_ALL_FORMS")."</label>",
                                                        "checkbox"); ?></td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='istablecolumn2_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isTableColumn2' checked/><label>".GT($textMap, "IA.THIS_FIELD_CAN_BE_USED_AS_A_COLUMN_IN_VIEWS")."</label>",
                                                        "checkbox") ?>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='istemplatefield2_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isTemplateField2' checked/><label>".GT($textMap, "IA.THIS_FIELD_CAN_BE_USED_AS_A_MERGE_FIELD_IN_TEMP")."</label>", "checkbox") ?>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='noclone2_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='noClone2' id='noClone2'/><label>".GT($textMap, "IA.DO_NOT_CLONE_THIS_FIELD_WHEN_RECORD_IS_CLONED")."</label>", "checkbox") ?>
                </td>
            </tr>
            <tr>
                <td colspan='2' class='rbs_grayDetailHTMLcol'>
                <?= GT($textMap, "IA.PLEASE_SPECIFY_LOOKUP_FIELD_PROPERTIES_FOR_TH2"); ?>
                </td>
            </tr>
            <tr class="<?=$emptyOrDisplayBlock?>">
                <td class='rbs_rightLabelRequired'><?= GT($textMap, "IA.INTEGRATION_NAME"); ?></td>
                <td id='name1_field' class='rbs_leftDataColWide' nowrap>
                    <input type='text' class='<?= $emptyOrTrimmedFormControl ?>' name='name1'
                           value='R<?= $objDef1->getObjectDefName(true) ?>' maxlength='30'/>
                    <br><span class='rbs_alertMsg' id='name1_error'></span></td>
            </tr>

            <? if ( $objDef2->isAuditable() ) { ?>
                <tr>
                    <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                    <td id='isaudit1_field' class='rbs_leftDataColWide' nowrap>
                        <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isAuditable1'/><label>".GT($textMap, "IA.TRACK_ALL_CHANGES_TO_THIS_FIELD_IN_EACH_RECORD" )."</label>", "checkbox") ?>
                        <br><span class='rbs_smallGray'><?= GT($textMap, "IA.THIS_MAY_INCREASE_YOUR_DATABASE_SPACE_USAGE_IF"); ?></span>
                    </td>
                </tr>
            <? } ?>

            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='isrequired1_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isRequired1'/><label>".GT($textMap, "IA.THIS_FIELD_IS_REQUIRED_IN_ALL_FORMS")."</label>", "checkbox") ?>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='issearchable1_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isSearchable1' checked/><label>".GT($textMap, "IA.THIS_FIELD_CAN_BE_USED_IN_FILTER_EXPRESSIONS")."</label>", "checkbox") ?>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='istablecolumn1_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='isTableColumn1' checked/><label>".GT($textMap, "IA.THIS_FIELD_CAN_BE_USED_AS_A_COLUMN_IN_VIEWS")."</label>", "checkbox") ?>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='istemplatefield1_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv(" <input type='checkbox' name='isTemplateField1' checked/><label>".GT($textMap, "IA.THIS_FIELD_CAN_BE_USED_AS_A_MERGE_FIELD_IN_TEMP")."</label>", "checkbox") ?>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'></td>
                <td id='noclone1_field' class='rbs_leftDataColWide' nowrap>
                    <?= Pt_SetupComponents::addInputDiv("<input type='checkbox' name='noClone1' id='noClone1'/><label>".GT($textMap, "IA.DO_NOT_CLONE_THIS_FIELD_WHEN_RECORD_IS_CLONED")."</label>", "checkbox") ?>
                </td>
            </tr>

            <tr height='10'>
                <td></td>
            </tr>

        </table>
        <?= Pt_WebUtil::getContainerEnd(''); ?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.ADD_LOOKUP_FIELDS_TO_PAGES")) ?>

        <table class='rbs_componentContentTable' cellpadding=0 c
            <tr>
                <td colspan='2' class='rbs_grayDetailHTMLcol'>
                    <?php
                        $lookupImg = Pt_WebUtil::getImage("lookup.gif",  GT($textMap, "IA.LOOKUP_FIELD") , 17, 16);
                        $textMap2 = getIntlTextMap([], [
                            [
                                'id'           => 'IA.A_LOOKUP_FIELD_WILL_AUTOMATICALLY_BE_CREATED_FO',
                                'placeHolders' => [
                                    [ 'name' => 'LOOKUP_IMG', 'value' => $lookupImg],
                                ],
                            ]
                        ]);
                        $textMap = array_merge($textMap, $textMap2);
                    ?>
                    <?= GT($textMap, "IA.A_LOOKUP_FIELD_WILL_AUTOMATICALLY_BE_CREATED_FO"); ?>
                </td>
            </tr>
            <tr>
                <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'>&nbsp;</td>
                <td class='rbs_leftDataColWide'>
                    <table border=0 cellspacing=2 cellpadding=0 class='wide'>
                        <tr>
                            <td valign='top' width='50%'>
                                <? if ( $objDef1HasEditor ) { ?>
                                    <table border=0 cellspacing=2 cellpadding=0>
                                        <tr>
                                            <td class='rbs_SetupDataColWide'><?= GT($textMap, "IA.OBJNAME_PAGES_OBJNAME_LOOKUP_FIELD:OBJNAME1_OBJNAME2"); ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class='rbs_boldDataCol'>
                                                <?= Pt_SetupComponents::addInputDiv('<input type="checkbox" name="field1_All" id="field1_All" checked
                                                            onClick=\'toggleAll(this, "field1_");\'> ' . $nbspOrEmpty . ' <label style="cursor:pointer" for="field1_All" >'.GT($textMap, "IA.SELECT_ALL").'</label>', 'checkbox') ?></td>
                                        </tr>

                                        <? foreach ( $pages1 as $webPage ) {
                                            if ( !util_inArgs($webPage->getPageType(), TYPE_NEW, TYPE_EDIT, TYPE_VIEW,
                                                              TYPE_NEW_QUICK)
                                                 || $webPage->isPortal() ) {
                                                continue;
                                            }
                                            $checkName = 'field1_' . $webPage->getId();
                                            $pageName = $webPage->__toString();
                                            ?>
                                            <tr>
                                                <td class='rbs_SetupDataColWide'>
                                                    <?= Pt_WebUtil::getCheckbox($checkName, false, $checkName,
                                                                                util_encode($pageName)) ?></td>
                                            </tr>
                                            <?
                                        } ?>
                                    </table>
                                    <?
                                } else { ?>
                                    &nbsp;
                                    <?
                                } ?>
                            </td>

                            <td width='10'></td>

                            <td valign='top' width='50%'>
                                <? if ( $objDef2HasEditor ) { ?>
                                    <table border=0 cellspacing=2 cellpadding=0>
                                        <tr>
                                            <td class='rbs_SetupDataColWide'><?= GT($textMap, "IA.OBJNAME_PAGES_OBJNAME_LOOKUP_FIELD:OBJNAME2_OBJNAME1"); ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class='rbs_boldDataCol'>
                                                <?= Pt_SetupComponents::addInputDiv('<input type="checkbox" name="field2_All" id="field2_All" checked
                                                        onClick=\'toggleAll(this, "field2_")\'> ' . $nbspOrEmpty . ' <label style="cursor:pointer" for="field2_All">'.GT($textMap, "IA.SELECT_ALL").'</label>', "checkbox") ?>
                                            </td>
                                        </tr>

                                        <? foreach ( $pages2 as $webPage ) {
                                            if ( !util_inArgs($webPage->getPageType(), TYPE_NEW, TYPE_EDIT, TYPE_VIEW,
                                                              TYPE_NEW_QUICK)
                                                 || $webPage->isPortal() ) {
                                                continue;
                                            }
                                            $checkName = 'field2_' . $webPage->getId();
                                            $pageName = $webPage->__toString();

                                            ?>
                                            <tr>
                                                <td class='rbs_SetupDataColWide'><?= Pt_WebUtil::getCheckbox($checkName,
                                                                                                             false,
                                                                                                             $checkName,
                                                                                                             util_encode($pageName)) ?></td>
                                            </tr>
                                            <?
                                        } ?>
                                    </table>
                                    <?
                                } else { ?>
                                    &nbsp;
                                    <?
                                } ?>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr height='10'>
                <td></td>
            </tr>

        </table>
        <?= Pt_WebUtil::getContainerEnd(''); ?>

    <table height='10' class='wide'>
        <tr>
            <td></td>
        </tr>
    </table>

    <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
    <?= Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.ADD_RELATED_LIST_VIEWS_TO_PAGES")) ?>

    <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
        <tr>
            <td colspan='2' class='rbs_grayDetailHTMLcol'><?= GT($textMap, "IA.RELATED_LIST_VIEW_COMPONENTS_WILL_AUTOMATICALLY"); ?>
            </td>
        </tr>
        <tr>
            <td class='rbs_rightLabelWide<?= $emptyOrDisplayNone ?>'>&nbsp;</td>
            <td class='rbs_leftDataColWide'>
                <table border=0 cellspacing=2 cellpadding=0 class='wide'>
                    <tr>
                        <td valign='top' width='50%'>
                            <? if ( $objDef1HasEditor ) { ?>
                                <table border=0 cellspacing=2 cellpadding=0>
                                    <tr>
                                        <td class='rbs_SetupDataColWide'><?= GT($textMap, "IA.OBJNAME_PAGES_OBJNAME_S_RELATED_LIST_VIEW:OBJNAME1_OBJNAME2S"); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class='rbs_boldDataCol'>
                                            <?= Pt_SetupComponents::addInputDiv('<input type="checkbox" name="table1_All" id="table1_All" onClick=\'toggleAll(this, "table1_")\'> 
                    ' . $nbspOrEmpty . ' <label style="cursor:pointer" for="table1_All">'.GT($textMap, "IA.SELECT_ALL").'</label>', "checkbox") ?>
                                        </td>
                                    </tr>

                                    <? foreach ( $pages1 as $webPage ) {
                                        if ( $webPage->getPageType() != TYPE_VIEW || $webPage->isPortal() ) {
                                            continue;
                                        }
                                        $checkName = 'table1_' . $webPage->getId();
                                        $pageName = $webPage->__toString();
                                        ?>
                                        <tr>
                                            <td class='rbs_SetupDataColWide'>
                                                <?= Pt_WebUtil::getCheckbox($checkName, false,
                                                                            $checkName, util_encode($pageName)) ?>
                                            </td>
                                        </tr>
                                        <?
                                    } ?>
                                </table>
                                <?
                            } else { ?>
                                &nbsp;
                                <?
                            } ?>
                        </td>

                        <td width='10'></td>

                        <td valign='top' width='50%'>
                            <? if ( $objDef2HasEditor ) { ?>
                                <table border=0 cellspacing=2 cellpadding=0>
                                    <tr>
                                        <td class='rbs_SetupDataColWide'><?= GT($textMap, "IA.OBJNAME_PAGES_OBJNAME_S_RELATED_LIST_VIEW:OBJNAME2_OBJNAME1S"); ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class='rbs_boldDataCol'>
                                            <?= Pt_SetupComponents::addInputDiv('<input type="checkbox" name="table2_All" id="table2_All"
                                                        onClick=\'toggleAll(this, "table2_")\'>' . $nbspOrEmpty . '<label style="cursor:pointer" for="table2_All" >'.GT($textMap, "IA.SELECT_ALL").'</label>', 'checkbox') ?>
                                        </td>
                                    </tr>

                                    <? foreach ( $pages2 as $webPage ) {
                                        if ( $webPage->getPageType() != TYPE_VIEW || $webPage->isPortal() ) {
                                            continue;
                                        }
                                        $checkName = 'table2_' . $webPage->getId();
                                        $pageName = $webPage->__toString();
                                        ?>
                                        <tr>
                                            <td class='rbs_SetupDataColWide'><?= Pt_WebUtil::getCheckbox($checkName,
                                                                                                         false,
                                                                                                         $checkName,
                                                                                                         util_encode($pageName)) ?></td>
                                        </tr>
                                        <?
                                    } ?>
                                </table>
                                <?
                            } else { ?>
                                &nbsp;
                                <?
                            } ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <?= Pt_WebUtil::getContainerEnd(''); ?>

    <table height='<?= Pt_SetupComponents::$isQuixote ? "10" : "14" ?>' class='wide'>
        <tr>
            <td></td>
        </tr>
    </table>

    <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
    <table class="<?= $rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
        <tr>
            <td class='center'>
                <table>
                    <tr>
                        <td>&nbsp;</td>
                        <td class='rbs_PageTopicWide'></td>
                        <td class='rbs_recordActionCol' nowrap>
                            <input type="submit"
                                   class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                   value=" <?= GT($textMap, "IA.SAVE"); ?> " onClick='return rbf_checkInput()'>
                            <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                   value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

    </td>
</tr>
<?= Pt_SetupComponents::$trHeight10OrEmpty ?>

</table>
</form>

<script language='JavaScript'>
    document.theForm.singularName1.focus();
    enableCheckboxes();
    rbf_addOnLoadMethod(rbf_initializeValidationField("singularName1"));
    rbf_addOnLoadMethod(rbf_initializeValidationField("pluralName1"));
    rbf_addOnLoadMethod(rbf_initializeValidationField("name1"));
    rbf_addOnLoadMethod(rbf_initializeValidationField("singularName2"));
    rbf_addOnLoadMethod(rbf_initializeValidationField("pluralName2"));
    rbf_addOnLoadMethod(rbf_initializeValidationField("name2"));
    rbf_addOnLoadMethod(rbf_initializeValidationField("relName"));
</script>
<?
include 'pt_setupFooter.inc';
}
catch (Exception $ex) {
    error($ex);
}

