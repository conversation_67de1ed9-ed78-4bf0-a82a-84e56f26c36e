<?
try {
    include 'pt_setupHeader.inc';

    include_once "Pt_PortalManager.cls";

    $pageId = http_getIntParameter(FIELD_PAGE_ID);
    $page = Pt_WebPageManager::getById($pageId);
    if ($page == null) {
        throw new Pt_I18nException('PAAS-0698', "Cannot find web page with id $pageId", [ 'PAGEID' => $pageId ]);
    }

    $portals = Pt_PortalManager::getAll();
    $portalId = http_getIntParameter(FIELD_PORTAL_ID);

    $emptyOrSectionClass = Pt_SetupComponents::$emptyOrSectionClass;
    $rbs_lightsilverTableOrEmpty = Pt_SetupComponents::$rbs_lightsilverTableOrEmpty;
    $rbs_roundedTableOrEmpty = Pt_SetupComponents::$rbs_roundedTableOrEmpty;
    $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
    $boldOrEmpty = Pt_SetupComponents::$boldOrEmpty;

    $tokens = [ "IA.COPY_TO", "IA.COPY", "IA.CANCEL", "IA.SELECT_PORTAL" ];
    $placeholderTokens = [
        [
            'id'           => "IA.COPY_PAGE_PAGE_TO_ANOTHER_PORTAL",
            'placeHolders' => [
                [ 'name' => 'PAGE_NAME', 'value' => util_encode($page) ],
            ],
        ],
    ];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);
?>

<script language="JavaScript">
function rbf_checkInput() {
    with (document.theForm) {
        act.value = 'pagePortalCopy';
    }
    return true;
}
</script>

<form action="<?=($portalId>0 ? 'pt_portalView.phtml':'pt_portalPages.phtml')?>" method='post' name='theForm' onSubmit='rbf_disableAllButtons()'>
<?=Pt_WebUtil::hidden()?>
<input type='hidden' name='act' value=''>
<input type='hidden' name='portalId' value='<?=$portalId?>'>
<input type='hidden' name='pageId' value='<?=$pageId?>'>
<input type="hidden" id="hlp" name="hlp" value="Portal_pages">

<table class="<?=Pt_SetupComponents::$rbs_wideTableOrWide?>" cellpadding=0 cellspacing=0>

<tr>
    <td>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class="<?= $rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'><?= GT($textMap, "IA.COPY_TO"); ?>: <?= util_encode($page->__toString()) ?>&nbsp;&nbsp;&nbsp;</td>
                            <td class='right top' nowrap>
                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.COPY"); ?> " onClick='return rbf_checkInput()'>&nbsp;&nbsp;
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_WebUtil::getContainerBegin($emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.COPY_PAGE_PAGE_TO_ANOTHER_PORTAL"), true) ?>

        <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
            <tr height='14'>
                <td></td>
            </tr>

            <tr>
                <td class='rbs_SetupColumnRequired'><?= GT($textMap, "IA.SELECT_PORTAL"); ?></td>
                <td class='rbs_grayDataCol'>
                    <select class='<?= Pt_SetupComponents::$emptyOrSelectPicker . Pt_SetupComponents::$emptyOrFormControl ?>' name='copyTo'>
                        <? foreach ( $portals as $portal ) {
                            if ( $portal->getId() == $portalId ) {
                                continue;
                            }
                            echo Pt_WebUtil::getOption($portal->getId(), $portal->__toString(), -1);
                        } ?>
                    </select></td>
            </tr>

            <tr height='14'>
                <td></td>
            </tr>
        </table>
        <?= Pt_WebUtil::getContainerEnd('');?>

        <?= Pt_SetupComponents::$emptyOrspace10 ?>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class="<?= $rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'></td>
                            <td class='right top' nowrap>
                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.COPY"); ?> " onClick='return rbf_checkInput()'>&nbsp;&nbsp;
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> ">&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

    </td>
</tr>

    <?= Pt_SetupComponents::$trHeight10OrEmpty ?>

</table>
</form>
<?
include 'pt_setupFooter.inc';
}
catch (Exception $ex) {
    error($ex);
}
