<?
/**
 * Cell which begins form to create object record in AJAX popup.
 */
require_once "Pt_AbstractFormBegin.cls";

class Pt_QuickCreateFormBegin extends Pt_AbstractFormBegin
{
    
    /**
     * @param int      $id
     * @param string   $origId
     * @param int      $sectionId
     * @param int      $orderNo
     * @param int      $fieldId
     * @param string   $text
     * @param array    $props
     * @param string[] $appOriginalIds
     * @param string   $name
     * @param string   $description
     */
    public function __construct($id, $origId, $sectionId, $orderNo, $fieldId, $text, $props, $appOriginalIds,
                                $name = '', $description = '')
    {
        parent::__construct($id, $origId, $sectionId, $orderNo, $fieldId, $text, $props, $appOriginalIds, $name,
                            $description);

        $tokens = [ "IA.QUICK_CREATE_FORM_BEGIN", "IA.SAVE", "IA.CANCEL" ];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
    }

    /**
     * Get localized description of cell's type.
     *
     * @return string|null
     */
    public function getDisplayType() 
    {
        return GT($this->textMap, "IA.QUICK_CREATE_FORM_BEGIN");
    }

    /**
     * Append opening FORM tag
     *
     * @param string        &$buff
     * @param Pt_PageParams $params
     */
    public function appendFormTag(& $buff, Pt_PageParams $params)
    {
        $buff .= "<form action='pt_objectQuickCreate2.phtml' method='post' name='theForm' enctype='multipart/form-data' onSubmit='rbf_disableAllButtons()'>\n";
        $buff .= Pt_WebUtil::hidden(OP_RUNTIME);
    }

    /**
     * Append HTML to render this cell
     *
     * @param string        &$buff
     * @param Pt_PageParams $params
     */
    public function appendHTML(& $buff, Pt_PageParams $params) 
    {
        $objDef = $params->getObjectDef();
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0574', "No object definition for this page");
        }
        $objDefId = $objDef->getId();

        $thisPageId = $params->getPageId();

        $buff .= "<input type='hidden' name='".FIELD_ACTION."' value='quickCreate'>\n";
        $buff .= "<input type='hidden' name='".FIELD_SRC_ID."' value='".$thisPageId."'>\n";
        $buff .= "<input type='hidden' name='".FIELD_OBJ_DEF_ID."' value='".$objDefId."'>\n";
        $relId = http_getIntParameter(FIELD_REL_ID);
        if ($relId > 0) {
            $buff .= "<input type='hidden' name='".FIELD_REL_ID."' value='".$relId."'>\n"; 
        }

        if ($params->getReturnId() > 0) {
            $buff .= "<input type='hidden' name='".FIELD_RETURN_ID."' value='".$params->getReturnId()."'>\n"; 
        }

        $funcName = http_getParameter(FIELD_FUNC_NAME);
        if ($funcName != null) {
            $buff .= "<input type='hidden' name='".FIELD_FUNC_NAME."' value='".util_encode($funcName)."'>\n"; 
        }
        $fieldId = http_getIntParameter(FIELD_FIELD_ID);
        if ($fieldId > 0) {
            $buff .= "<input type='hidden' name='".FIELD_FIELD_ID."' value='".$fieldId."'>\n"; 
        }
        $stdEditor = http_getParameter(STD_EDITOR);
        if ( $stdEditor ) {
            $objDefId2 = http_getIntParameter(FIELD_OBJ_DEF_ID2);
            $returnId2 = http_getIntParameter(FIELD_RETURN_ID2);
            if ( $objDefId2 && $returnId2 ) {
                $buff .= "<input type='hidden' name='" . STD_EDITOR . "' id='" . STD_EDITOR . "' value='1'>\n";
                $buff .= "<input type='hidden' name='" . FIELD_OBJ_DEF_ID2 . "' id='" . FIELD_OBJ_DEF_ID2
                         . "' value=$objDefId2>\n";
                $buff .= "<input type='hidden' name='" . FIELD_RETURN_ID2 . "' id='" . FIELD_RETURN_ID2
                         . "' value=$returnId2>\n";
            }
        }

        $buff2 = '';

        $buff2 .= "<table class='" . Pt_SetupComponents::$rbs_lightsilverTableOrEmpty . " wide'>\n";
        $buff2 .= "<tr>\n";
        $buff2 .= "<td class='center wide' nowrap>\n";
        $buff2 .= "<table class='wide'><tr><td>&nbsp;</td><td class='rbs_PageTopicWide'>";

        $buff2 .= "</td>\n";

        $buff2 .= "<td class='wide' nowrap>&nbsp;</td>\n";

        $buff2 .= "<td class='rbs_recordActionCol' nowrap>\n";
        $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
        
        $buff2 .= "<input type='submit' class='" . Pt_SetupComponents::$boldOrEmpty
                  . $emptyOrBtnPrimaryClass . "' value=' " . GT($this->textMap, "IA.SAVE") . " '/>&nbsp;&nbsp;\n";
        // $onClickClose = 'parent.closeQxDialog ? parent.closeQxDialog(window.frameElement) : parent.rbv_yuiDialog.hide();';
        $onClickClose = 'parent.rbv_yuiDialog.hide();';
        $buff2 .= "<input type='button' class='" . $emptyOrBtnPrimaryClass . "' value=' " . GT($this->textMap, "IA.CANCEL") . " ' onClick='$onClickClose'/>&nbsp;&nbsp;\n";

        $buff2 .= "</td></tr></table>";

        $buff2 .= "</td>\n";
        $buff2 .= "</tr>\n";
        $buff2 .= "</table>\n";

        $params->buttonsRow = $buff2;
    }

}
