<?
/**
 * Cell which renders pageable table of related object records.
 */
require_once "Pt_PageableList.cls";

class Pt_RelatedList extends Pt_PageableList
{
    /* @var int $relId */
    protected $relId;
    /* @var bool $showAttachLink */
    protected $showAttachLink;
    /* @var bool $showDetachLink */
    protected $showDetachLink;
    
    /**
     * @param int      $id
     * @param string   $origId
     * @param int      $sectionId
     * @param int      $orderNo
     * @param int      $fieldId
     * @param string   $text
     * @param array    $props
     * @param string[] $appOriginalIds
     * @param string   $name
     * @param string   $description
     */
    public function __construct($id, $origId, $sectionId, $orderNo, $fieldId, $text, $props, $appOriginalIds,
                                $name = '', $description = '')
    {
        parent::__construct($id, $origId, $sectionId, $orderNo, $fieldId, $text, $props, $appOriginalIds, $name,
                            $description);

        $tokens = [ "IA.ERROR.ERROR_NO_RELATIONSHIP_FOUND", "IA.QUICK_CREATE" ];
        $placeholderTokens = [];
        $this->textMap = array_merge($this->textMap, getIntlTextMap($tokens, $placeholderTokens));
    }

    /**
     * This cell can be displayed by web page?
     *
     * @param Pt_WebPage $page
     *
     * @return bool
     */
    public function canBeDisplayed(Pt_WebPage $page) 
    {
        return ($page->getPageType() == TYPE_VIEW);
    }

    /**
     * Get localized description of cell's type.
     *
     * @return string|null
     */
    public function getDisplayType() 
    {
        $tableObjDef = $this->getTableObjectDef();
        $localPlaceholderTokens = [
            [
                'id'           => 'IA.TABLE_OBJ_DEF_VIEW_RELATED_RECORDS',
                'placeHolders' => [
                    [ 'name' => 'TABLE_OBJ_DEF', 'value' => "$tableObjDef" ],
                ],
            ],
        ];
        $txtMap = getIntlTextMap([], $localPlaceholderTokens);
        $this->textMap = array_merge($this->textMap, $txtMap);
        return GT($this->textMap, "IA.TABLE_OBJ_DEF_VIEW_RELATED_RECORDS");
    }

    /**
     * Set cell's properties
     *
     * @param array $props
     */
    public function setProperties($props) 
    {
        parent::setProperties($props);
        $this->relId = intval($props[FIELD_REL_ID]);
        $this->showAttachLink = pt_boolval($props[FIELD_SHOW_ATTACH_LINK]);
        $this->showDetachLink = pt_boolval($props[FIELD_SHOW_DETACH_LINK] ?? true);
    }

    /**
     * Get relationship definition
     *
     * @return Pt_RelationshipDef|null
     */
    public function getRelationshipDef() 
    {
        return Pt_RelationshipDefManager::getById($this->relId);
    }

    /**
     * Get relationship definition id
     *
     * @return int
     */
    public function getRelationshipDefId() 
    {
        return $this->relId;
    }

    /**
     * Show "Attach Object" link?
     *
     * @return bool
     */
    public function showAttachLink() 
    {
        return $this->showAttachLink;
    }
    
    /**
     * Show "Detach Object" link?
     *
     * @return bool
     */
    public function showDetachLink()
    {
        return $this->showDetachLink;
    }

    /**
     * Show "Filter" link?
     *
     * @param Pt_PageParams $params
     *
     * @return bool
     */
    public function showFilterLink($params) 
    {
        return false;
    }

    /**
     * Get object definition for base objects.
     * This table shows objects, related to base object.
     *
     * @return Pt_DataObjectDef|null
     */
    public function getBaseObjectDef() 
    {
        $rel = $this->getRelationshipDef();
        if ($rel == null) {
            return null; 
        }
        return $rel->getOtherObjectDef($this->getTableObjectDefId());
    }

    /**
     * show a remove action if the related field is not required or if it is required and it has at least 2 records
     * attached
     *
     * @param Pt_PageParams $params
     *
     * @return bool
     */
    private function showRemoveAction(Pt_PageParams $params)
    {
        $data = $params->getData();
        if ( $data == null || $params->isPortal() ) {
            return false;
        }
        $rel = $this->getRelationshipDef();
        if ( $rel ) {
            $objDefId = $params->getObjectDef()->getId();
            if ( $rel->hasObjDefId($objDefId) && $rel->isRelatedFieldRequired($objDefId) ) {
                $numberOfRelatedRecords = $rel->countRelatedRecords($objDefId, $data);

                return $numberOfRelatedRecords && $numberOfRelatedRecords > 1;
            }

            return true;
        }

        return false;
    }

    /**
     * Show "delete" action?
     *
     * @param Pt_PageParams $params
     *
     * @return bool
     */
    public function showDelete(Pt_PageParams $params)
    {
        return $this->showRemoveAction($params);
    }

    /**
     * Show "detach" action?
     *
     * @param Pt_PageParams $params
     *
     * @return bool
     */
    public function showDetach(Pt_PageParams $params)
    {
        $showAction = $this->showRemoveAction($params);

        if ( $showAction ) {
            $rel = $this->getRelationshipDef();
            if ( $rel ) {
                return $this->showDetachLink() && !$rel->isOrphanControl($this->getTableObjectDefId());
            }
        }

        return false;
    }

    /**
     * Create Pt_ListQuery object
     *
     * @param Pt_ListDef    $listDef
     * @param Pt_PageParams $params
     *
     * @return Pt_ListQuery
     */
    public function createQuery(Pt_ListDef $listDef, Pt_PageParams $params)
    {
        $lQuery = new Pt_ListQuery($listDef, $params->getData());
        $lQuery->setRelationshipDef($this->getRelationshipDef());
        return $lQuery;
    }

    /**
     * Append HTML to render this cell
     *
     * @param string        &$buff
     * @param Pt_PageParams $params
     */
    public function appendHTML(& $buff, Pt_PageParams $params) 
    {
        if ($this->getRelationshipDef() == null) {
            $buff .= "";
            Pt_WebUtil::appendEmptyListMessage($buff, GT($this->textMap, "IA.ERROR.ERROR_NO_RELATIONSHIP_FOUND"), false);
            return;
        }
        parent::appendHTML($buff, $params);
    }

    /**
     * Append JavaScript to table's HTML
     *
     * @param string        &$buff
     * @param Pt_PageParams $params
     * @param int           $selViewId
     * @param int           $listPageId
     */
    public function appendJavaScript(& $buff, Pt_PageParams $params, $selViewId, $listPageId)
    {
        parent::appendJavaScript($buff, $params, $selViewId, $listPageId);

        if ($params->isPortal()) {
            return; 
        }

        if (strpos($buff, "pt_selector.js") === false) {
            $buff .= "<script language='JavaScript' src='../resources/js/platform/pt_selector.js'></script>\n"; 
        }
        $pageObject = $params->getObjectDef();
        $returnPortion = (isset($pageObject) && !$pageObject->isPlatform() ? "&returnTo=" 
            . urlencode(URLCleanParams::insert('returnTo', URLS::ScriptRequest())) : '');

        $buff .= "<script language='JavaScript'>\n";
        $buff .= "function create".$this->getId()."(objId2) {\n";

        $buff .= "rbf_doRelCreate(\"".$this->formName."\", \"$returnPortion\", ".$params->getObjectDefId().", ".$params->getDataId().", objId2, ".$this->relId.", ".$this->id.", ".$selViewId.", ".$listPageId.");\n";

        $buff .= "}\n";
        $buff .= "</script>\n";
    }

    /**
     * Get HTML to be added to section's title (if any)
     *
     * @param Pt_PageParams $params
     *
     * @return string|null
     */
    public function getSectionTitleHTML(Pt_PageParams $params) 
    {
        if ($params->getData() == null || $params->isPortal()) {
            return null; 
        }
        $rel = $this->getRelationshipDef();
        if ($rel == null) {
            return null; 
        }

        $objDef = $this->getTableObjectDef();
        if ($objDef==null) {
            return null; 
        }
        $singularName = $objDef->getSingularName();

        $data = $params->getData();
        
        if (!$rel->isMultiple($objDef->getId())) {  // Bug 40578 
            $relData = Pt_RelationshipManagerChoose::getObject($rel, $data);
            if ($relData != null) {
                return null; 
            }
        }

        $buff = '';

        // Only show "New XX" link or "Quick Create" link if user has permission
        // and, if this is a standard object, the object has no parent
        if (util_hasPermission('OBJECT', $objDef->getId(), 'add') && !$objDef->standardHasParent()) {

            $newPageId = Pt_WebUtil::getPageId($objDef->getId(), TYPE_NEW);

            // only show new link if this is a platform object or a standard object
            // without a parent if its a standard object it should also not be one of the utility
            // objects like company preferences
            if ($this->showNewLink && $newPageId > 0 && $objDef->standardAllowNew()) {
                $localPlaceholderTokens1 = [
                    [
                        'id'           => 'IA.NEW_SINGULAR_NAME',
                        'placeHolders' => [
                            [ 'name' => 'SINGULAR_NAME', 'value' => $singularName ],
                        ],
                    ],
                ];
                $txtMap1 = getIntlTextMap([], $localPlaceholderTokens1);
                $this->textMap = array_merge($this->textMap, $txtMap1);

                $buff .= "<a href='".Pt_WebUtil::url('pt_main.phtml', OP_RUNTIME)."&pageId=".$newPageId."&destId=" 
                    . $params->getPageId()."&returnId=".$data->getId()."&objDefId2=".$params->getObjectDefId()
                    . "&returnTo=" . urlencode(URLCleanParams::insert('returnTo', URLS::ScriptRequest()))
                    . "'>" . GT($this->textMap, "IA.NEW_SINGULAR_NAME") . "</a>";
            }

            $quickPageId = Pt_WebUtil::getPageId($objDef->getId(), TYPE_NEW_QUICK);
            if ($this->showQuickCreate && !$objDef->isSystem() && $quickPageId > 0) {
                if (strlen($buff) > 0) {
                    $buff .= " | "; 
                }
                /** @noinspection PhpUndefinedVariableInspection */
                $popupURL .= Pt_WebUtil::url('pt_objectQuickCreate.phtml', OP_RUNTIME) . "&objDefId=" . $this->getTableObjectDefId() .
                             "&pageId=" . $quickPageId . "&returnId=" . $params->getDataId() . "&objDefId2=" . $params->getObjectDefId();

                $buff .= "<a href='#undefined' onClick='return rbf_doQuickCreate(" . $this->id . ", \"" . $popupURL
                         . "\")'>" . GT($this->textMap, "IA.QUICK_CREATE") . "</a>\n";
            }
        }

        $selPageId = Pt_WebUtil::getPageId($objDef->getId(), TYPE_SELECTOR);
        if ($this->showAttachLink && $selPageId > 0) {
            if (strlen($buff) > 0) {
                $buff .= " | "; 
            }
            $localPlaceholderTokens2 = [
                [
                    'id'           => 'IA.ATTACH_SINGULAR_NAME',
                    'placeHolders' => [
                        [ 'name' => 'SINGULAR_NAME', 'value' => $singularName ],
                    ],
                ],
            ];
            $txtMap2 = getIntlTextMap([], $localPlaceholderTokens2);
            $this->textMap = array_merge($this->textMap, $txtMap2);
            $buff .= "<a href='#undefined' onClick='return rbf_attachRelated(".$selPageId.", \"create".$this->getId()."\", \"".$this->getListQuery($params, -1)."\" )'>" . GT($this->textMap, "IA.ATTACH_SINGULAR_NAME") . "</a>\n";
        }

        return $buff;
    }

    /**
     * Is this cell belongs to a packet?
     *
     * @param Pt_PacketProxy $proxy
     *
     * @return bool
     */
    public function belongsTo(Pt_PacketProxy $proxy) 
    {
        return $proxy->hasObjectDef($this->getTableObjectDef()) && $proxy->hasObjectDef($this->getBaseObjectDef());
    }

    /**
     * Compute the url for the export link.
     *
     * @param Pt_PageParams $params    the page parameters
     * @param int           $selViewId the selector view's id
     *
     * @return string    the export url
     */
    protected function getPortalExportUrl(Pt_PageParams $params, $selViewId) 
    {
        $data = $params->getData();
        if ( ! $data ) {
            return "";
        }
        $u = parent::getPortalExportUrl($params, $selViewId);
        $u .= "&relId=" . $this->relId . "&id2=" . $data->getId();
        return $u;
    }


}
