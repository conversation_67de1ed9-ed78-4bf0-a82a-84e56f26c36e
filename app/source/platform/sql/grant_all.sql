
GRANT SELECT, INSE<PERSON>, UPDA<PERSON>, DELETE ON PT_OBJ_DEF TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_FIELD_DEF TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_RELATIONSHIP_DEF TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_OBJ_DATA TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_RELATIONSHIP TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_ACT_TRAIL TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_COMMENT TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_PROCESS TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_STATUS TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_ACTION TO PLATFORM_USER;
GRA<PERSON> SELECT, INSERT, UPDATE, DELETE ON PT_MENU TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_WORKFLOW TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_EVENT_DEF TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_SYS_RELS TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_PORTAL TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_PAGE TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_PAGE_SECTION TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_PAGE_CELL TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_LIST_VIEW TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_USER_FLAGS TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_APPLICATION TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_DATA_INDEX TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_DATA_MAP TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_TEMPLATE TO PLATFORM_USER;
GRANT SELECT, INSERT, UPDATE, DELETE ON PT_LIST_ITEM TO PLATFORM_USER;

GRANT SELECT ON PT_CONFIG_SEQ TO PLATFORM_USER;
GRANT SELECT ON PT_DATA_SEQ TO PLATFORM_USER;

BEGIN
P_CRESYN('PT_OBJ_DEF');
P_CRESYN('PT_FIELD_DEF');
P_CRESYN('PT_RELATIONSHIP_DEF');
P_CRESYN('PT_OBJ_DATA');
P_CRESYN('PT_RELATIONSHIP');
P_CRESYN('PT_ACT_TRAIL');
P_CRESYN('PT_COMMENT');
P_CRESYN('PT_PROCESS');
P_CRESYN('PT_STATUS');
P_CRESYN('PT_ACTION');
P_CRESYN('PT_MENU');
P_CRESYN('PT_WORKFLOW');
P_CRESYN('PT_EVENT_DEF');
P_CRESYN('PT_SYS_RELS');
P_CRESYN('PT_PORTAL');
P_CRESYN('PT_PAGE');
P_CRESYN('PT_PAGE_SECTION');
P_CRESYN('PT_PAGE_CELL');
P_CRESYN('PT_LIST_VIEW');
P_CRESYN('PT_USER_FLAGS');
P_CRESYN('PT_APPLICATION');
P_CRESYN('PT_DATA_INDEX');
P_CRESYN('PT_DATA_MAP');
P_CRESYN('PT_TEMPLATE');
P_CRESYN('PT_LIST_ITEM');

P_CRESYN('PT_CONFIG_SEQ');
P_CRESYN('PT_DATA_SEQ');
END;