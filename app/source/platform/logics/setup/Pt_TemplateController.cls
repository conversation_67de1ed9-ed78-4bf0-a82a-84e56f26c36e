<?
/**
 * Manages HTTP requests to create/update document and email templates.
 */
require_once 'Pt_Includes.inc';

require_once "Pt_TemplateManager.cls";
require_once "Pt_FileUpload.cls";

class Pt_TemplateController
{

    /**
     * Dispatch HTTP requests to template API.
     */
    public static function dispatch()
    {
        $action = http_getParameter(FIELD_ACTION);

        if ( $action ) {
            if ( !Pt_WebUtil::verifyCsrfToken() ) {
                // No csrf token, don't process but keep going.
                $tokens = ["IA.INVALID_REQUEST"];
                $placeholderTokens = [];
                $textMap = getIntlTextMap($tokens, $placeholderTokens);
                Session::setProperty(ATTR_INFO_MESSAGE, GT($textMap, "IA.INVALID_REQUEST"));

                return;
            }
        }

        switch ($action) {
            case 'mailTemplCreate':
                self::mailCreate();
                break;

            case 'mailTemplUpdate':
                self::mailUpdate();
                break;

            case 'docTemplCreate':
                self::docCreate();
                break;

            case 'docTemplUpdate':
                self::docUpdate();
                break;

            case 'templDelete':
                self::delete();
                break;
        }

    }

    /**
     * Create new email template.
     */
    public static function mailCreate() 
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $name = http_getParameter(FIELD_NAME);
        $subject = http_getParameter(FIELD_SUBJECT);
        $body = http_getParameter(FIELD_BODY);
        $contentType = http_getParameter(FIELD_CONTENT_TYPE);
        $isRawHtml = http_getBoolParameter(FIELD_IS_RAWHTML, false);
        $isPrivate = http_isBoxChecked(FIELD_IS_PRIVATE);
        if ( Pt_TemplateManager::getMailByDisplayName($name, $objDefId) != null ) {
            throw new Pt_I18nException('PAAS-0415', "Email template with display name $name already exists",
                                       [ 'NAME' => $name ]);
        }

        XACT_BEGIN('PLATFORM');
        $mt = null;
        try {
            $mt = Pt_TemplateManager::createMail(
                0, $objDefId, $name, $subject,
                $body, $contentType, $isRawHtml, $isPrivate, null, ''
            );

            XACT_COMMIT('PLATFORM');
            if ( Pt_SetupComponents::$isQuixote ) {
                Pt_CreatePopup::setCreateSessionProperty($mt->getId(), $mt->__toString());
            }
        } catch ( Exception $ex ) {
            //I18N: TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.TEMPLATE_HAS_BEEN_CREATED",
                'placeHolders' => [
                    [ 'name' => 'LINK_TEXT', 'value' => util_encode($mt->__toString())],
                    [ 'name' => 'LINK_HREF', 'value' => Pt_WebUtil::url("pt_templateView.phtml?templateId=".$mt->getId())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.TEMPLATE_HAS_BEEN_CREATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Update existing email template.
     */
    public static function mailUpdate() 
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $templateId = http_getIntParameter(FIELD_TEMPLATE_ID);
        $mt = Pt_TemplateManager::getMailById($templateId);
        if ( $mt == null ) {
            throw new Pt_I18nException('PAAS-0416', "Template with id $templateId not found",
                                       [ 'TEMPLATEID' => $templateId ]);
        }
        $name = http_getParameter(FIELD_NAME);
        $subject = http_getParameter(FIELD_SUBJECT);
        $body = http_getParameter(FIELD_BODY);
        $contentType = http_getParameter(FIELD_CONTENT_TYPE);
        $isRawHtml = http_getBoolParameter(FIELD_IS_RAWHTML, false);
        $isPrivate = http_isBoxChecked(FIELD_IS_PRIVATE);

        if ( Pt_TemplateManager::getMailByDisplayName($name, $objDefId, $templateId) != null ) {
            throw new Pt_I18nException('PAAS-0417', "Email template with display name $name already exists",
                                       [ 'NAME' => $name ]);
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_TemplateManager::updateMail($mt, $name, $subject, $body, $contentType, $isRawHtml, $isPrivate, '');
            XACT_COMMIT('PLATFORM');
            if ( Pt_SetupComponents::$isQuixote ) {
                Pt_ViewPopup::setUpdateSessionProperty($mt->getId(), $mt->__toString());
            }
        } catch ( Exception $ex ) {
            //I18N : TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.TEMPLATE_HAS_BEEN_UPDATED",
                'placeHolders' => [
                    [ 'name' => 'LINK_TEXT', 'value' => util_encode($mt->__toString())],
                    [ 'name' => 'LINK_HREF', 'value' => Pt_WebUtil::url("pt_templateView.phtml?templateId=".$mt->getId())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.TEMPLATE_HAS_BEEN_UPDATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * @param string $fileName
     * @param string $content
     * @param string $contentType
     * @param string $templateType
     */
    private static function getQuixoteData(&$fileName, &$content, &$contentType, &$templateType)
    {
        $fileName = http_getParameter('fileName');
        if ( $fileName == '' ) {
            $fileName = "direct_input.txt";
        }
        $content = http_getParameter('template');
        if ( !$content ) {
            $fu = Pt_FileUpload::getUpload(FIELD_FILE);
            if ( $fu != null ) {
                $content = $fu->getData() ?? '';
            } else {
                $content = '';
            }
        }
        $contentType = http_getParameter('contentType');
        if ( $contentType == '' ) {
            $contentType = MIME_DIRECT_INPUT;
        }
        $templateType = http_getParameter('templateType');
    }

    /**
     * Create new document template.
     */
    public static function docCreate() 
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $name = http_getParameter(FIELD_NAME);
        $isPrivate = http_isBoxChecked(FIELD_IS_PRIVATE);
        $apiVersion = http_getParameter(FIELD_REST_API_VERSION, '');
        $isQuixote = Pt_SetupComponents::$isQuixote;
        if ( $isQuixote ) {
            self::getQuixoteData($fileName, $content, $contentType, $templateType);
        } else {
            $fu = Pt_FileUpload::getUpload(FIELD_FILE);
            if ( $fu == null ) {
                throw new Pt_I18nException('PAAS-0418', "No document uploaded");
            }
            $fileName = $fu->getOriginalName();
            $content = $fu->getData();
            $contentType = $fu->getContentType();
            $templateType = '';
        }
        if ( Pt_TemplateManager::getDocByDisplayName($name, $objDefId) != null ) {
            throw new Pt_I18nException('PAAS-0419', "Document template with display name $name already exists",
                                       [ 'NAME' => $name ]);
        }

        XACT_BEGIN('PLATFORM');
        $dt = null;
        try {
            $dt =
                Pt_TemplateManager::createDoc(0, $objDefId, $name, $fileName, $content, $contentType, $templateType,
                                              $isPrivate, null, '', $apiVersion);

            XACT_COMMIT('PLATFORM');
            if ( $isQuixote ) {
                Pt_CreatePopup::setCreateSessionProperty($dt->getId(), $dt->__toString());
            }
        } catch ( Exception $ex ) {
            //I18N : TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.TEMPLATE_HAS_BEEN_CREATED",
                'placeHolders' => [
                    [ 'name' => 'LINK_TEXT', 'value' => util_encode($dt->__toString())],
                    [ 'name' => 'LINK_HREF', 'value' => Pt_WebUtil::url("pt_templateView.phtml?templateId=".$dt->getId())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.TEMPLATE_HAS_BEEN_CREATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Update existing document template.
     */
    public static function docUpdate() 
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $templateId = http_getIntParameter(FIELD_TEMPLATE_ID);
        $apiVersion = http_getParameter(FIELD_REST_API_VERSION, '');
        $dt = Pt_TemplateManager::getDocById($templateId);
        if ( $dt == null ) {
            throw new Pt_I18nException('PAAS-0420', "Document template with id $templateId not found",
                                       [ 'TEMPLATEID' => $templateId ]);
        }
        $name = http_getParameter(FIELD_NAME);
        $isPrivate = http_isBoxChecked(FIELD_IS_PRIVATE);
        $isQuixote = Pt_SetupComponents::$isQuixote;
        if ( $isQuixote ) {
            self::getQuixoteData($fileName, $content, $contentType, $templateType);
        } else {
            $fu = Pt_FileUpload::getUpload(FIELD_FILE);
            $fileName = null;
            $content = null;
            $contentType = $dt->getContentType();
            if ( $fu ) {
                $fileName = $fu->getOriginalName();
                $content = $fu->getData();
                $contentType = $fu->getContentType();
            }
            $templateType = '';
        }

        if ( Pt_TemplateManager::getDocByDisplayName($name, $objDefId, $templateId) != null ) {
            throw new Pt_I18nException('PAAS-0421', "Document template with display name $name already exists",
                                       [ 'NAME' => $name ]);
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_TemplateManager::updateDoc($dt, $name, $fileName, $content, $contentType, $templateType, $isPrivate, '', $apiVersion);
            XACT_COMMIT('PLATFORM');
            if ( $isQuixote ) {
                Pt_ViewPopup::setUpdateSessionProperty($dt->getId(), $dt->__toString());
            }
        } catch ( Exception $ex ) {
            //I18N: TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.TEMPLATE_HAS_BEEN_UPDATED",
                'placeHolders' => [
                    [ 'name' => 'LINK_TEXT', 'value' => util_encode($dt->__toString())],
                    [ 'name' => 'LINK_HREF', 'value' => Pt_WebUtil::url("pt_templateView.phtml?templateId=".$dt->getId())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.TEMPLATE_HAS_BEEN_UPDATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Delete template.
     */
    public static function delete() 
    {
        $templateId = http_getIntParameter(FIELD_TEMPLATE_ID);
        $t = Pt_TemplateManager::getById($templateId);
        if ($t == null) {
            throw new Pt_I18nException('PAAS-0422', "Template with id $templateId not found",
                                       [ 'TEMPLATEID' => $templateId ]);
        }

        $events = Pt_EventDefManager::getByObjectDef($t->getObjectDefId());
        foreach ($events as $event) {
            if ($event->getTemplateId() == $templateId) {
                $tokens = [];
                $placeholderTokens = [
                    [
                        'id'           => "IA.CANNOT_DELETE_TEMPLATE_IT_IS_USED_BY_TRIGGER",
                        'placeHolders' => [
                            [ 'name' => 'TEMPLATE', 'value' => util_encode($t) ],
                            [ 'name' => 'TRIGGER', 'value' => util_encode($event) ]
                        ],
                    ],
                ];
                $textMap = getIntlTextMap($tokens, $placeholderTokens);
                $infoMessage = GT($textMap, "IA.CANNOT_DELETE_TEMPLATE_IT_IS_USED_BY_TRIGGER");
                Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
                return;
            }
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_TemplateManager::delete($t);
            XACT_COMMIT('PLATFORM');
            if ( Pt_SetupComponents::$isQuixote ) {
                Pt_ViewPopup::setDeleteSessionProperty($templateId);
            }
        } catch ( Exception $ex ) {
            //I18N: TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.TEMPLATE_HAS_BEEN_DELETED",
                'placeHolders' => [
                    [ 'name' => 'TEMPLATE', 'value' => util_encode($t) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.TEMPLATE_HAS_BEEN_DELETED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * @param string $contentType
     *
     * @return string
     *
     * @throws Pt_I18nException
     */
    public static function getFileData(&$contentType)
    {
        $data = self::getObjRecordData();
        $templateId = http_getIntParameter(FIELD_TEMPLATE_ID);
        $t = Pt_TemplateManager::getById($templateId);
        if ( $t == null ) {
            throw new Pt_I18nException('PAAS-0857', "Template with id $templateId not found",
                                       [ 'TEMPLATEID' => "$templateId" ]);
        }

        $contentType = $t->getContentType();

        $rawData = null;
        if ( $t instanceof Pt_DocTemplate ) {
            $restApiVersion = ( $t instanceof Pt_RESTAPITemplate ) ? $t->getApiVersion() : '';
            $rawData = Pt_TemplateEngine::processRest((string) $t->getTemplateData(), Pt_TemplateEngine::getTemplateEncoding($t), $data,
                                                  "SOURCE: DocTemplate $t - ptTemplate.phtml", $restApiVersion);
        } else if ( $t instanceof Pt_MailTemplate ) {
            $rawData = Pt_TemplateEngine::process((string) $t->getBody(), $t->isHTML() ? HTML_TEXT : PLAIN_TEXT,
                                                  $data, "SOURCE: MailTemplate $t - ptTemplate.phtml");
        }

        if ( $rawData == null ) {
            throw new Pt_I18nException('PAAS-0858', "Template $t has empty file body.",
                                       [ 'TEMPLATE' => "$t" ]);
        }

        return $rawData;
    }

    /**
     * @param string $contentType
     *
     * @return string
     *
     * @throws Pt_I18nException
     */
    public static function getDirectInputData(&$contentType)
    {
        $templateId = http_getIntParameter(FIELD_TEMPLATE_ID);
        $t = Pt_TemplateManager::getById($templateId);

        $contentType = $t ? $t->getContentType() : MIME_TEXT;
        $rawData = (string) Session::getProperty(TEMPLATE_CONTENT);

        if ( !http_getBoolParameter(DOWNLOAD_TEMPLATE) ) {
            $restApiVersion = http_getParameter(FIELD_REST_API_VERSION,
                                                ( $t instanceof Pt_RESTAPITemplate ) ? $t->getApiVersion() : '');
            $rawData = Pt_TemplateEngine::processRest($rawData,
                                                  Pt_TemplateEngine::getTemplateEncoding($t),
                                                  self::getObjRecordData(),
                                                  "SOURCE: DirectInput " . ( $t ? : "" ) . "- ptTemplate.phtml",
                                                  $restApiVersion);
        }
        if ( $rawData == null ) {
            throw new Pt_I18nException('PAAS-0859', "Template $t has empty direct input body.",
                                       [ 'TEMPLATE' => "$t" ]);
        }

        return $rawData;
    }

    /**
     * @return Pt_DataObject
     *
     * @throws Pt_I18nException
     */
    private static function getObjRecordData()
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ( $objDef == null ) {
            throw new Pt_I18nException('PAAS-0855',
                                       "Object definition with id $objDefId not found",
                                       [ 'OBJDEFID' => "$objDefId" ]);
        }

        $id = http_getIntParameter(FIELD_ID);
        $data = Pt_DataObjectManager::getById($objDef, $id);
        if ( $data == null ) {
            throw new Pt_I18nException('PAAS-0856',
                                       "DataObject with id $id not found",
                                       [ 'ID' => "$id" ]);
        }

        util_checkPermission($objDef, 'readonly');

        return $data;
    }

}
