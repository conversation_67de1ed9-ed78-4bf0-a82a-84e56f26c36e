<?
/**
 * Manages HTTP requests to create/modify menus.
 */
require_once('Pt_Includes.inc');

require_once("Pt_MenuManager.cls");
require_once("Pt_WebPageManager.cls");
require_once("Pt_PageSectionManager.cls");
require_once("Pt_ApplicationManager.cls");
require_once("RoleCustomPolicy.cls");
require_once("CustomPolicy.cls");

class Pt_MenuController {

    /**
     * Create new menu for new object definition.
     */
    public static function createObjDef() {
        $name1 = http_getParameter(FIELD_NAME);
        $name = util_alphanumeric($name1);
        $description = http_getParameter(FIELD_DESCRIPTION);
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0359', "Object Definition is null");
        }
        if (Pt_MenuManager::getByDisplayName($name) != null) {
            throw new Pt_I18nException('PAAS-0360', "Menu with display name $name already exists", [ 'NAME' => $name ]);
        }

        $apps = [];
        foreach (Pt_ApplicationManager::getAll() as $app) {
            if (http_isBoxChecked('app_'.$app->getId())) {
                $apps[] = $app;
            }
        }

        XACT_BEGIN('PLATFORM');
        $menu = null;
        try {
            $menu = self::createObjTab($objDef, $name, $description, -1, $apps);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        // Grant permissions to current user.
        XACT_BEGIN('PLATFORM');
        try {
            foreach ($apps as $app) {
                $pol = (IsRolesEnabled() && !IsExternalUser()) ? new RoleCustomPolicy($app->getAppCode()) : new CustomPolicy($app->getAppCode(), GetMyUserid());
                $ok = $pol->CheckAndAddUserPolicy($objDef->getId(), 'list|readonly|add|modify|delete|import|templ|calendar', 'OBJECT');
                if (!$ok) {
                    //I18N : TODO
                    throw new Exception(Pt_StandardUtil::globalErrorMessages());
                }
                $ok = $pol->CheckAndAddUserPolicy($menu->getId(), 'menu', 'MENU');
                if (!$ok) {
                    //I18N : TODO
                    throw new Exception(Pt_StandardUtil::globalErrorMessages());
                }
            }

            if (!XACT_COMMIT('PLATFORM')) {
                XACT_ABORT('PLATFORM');
            }
        } catch (Exception $ex) {
            // This is just for permissions.  If there's an error, the menu has been created.  Fall through to show message.
            XACT_ABORT('PLATFORM');
        }

        $tokens = [ "IA.MENU_NAME_HAS_BEN_ADJUSTED" ];
        $placeholderTokens = [
            [
                "id"           => "IA.MENU_LINK_TEXT_LINK_HREF_HAS_BEEN_CREATED",
                "placeHolders" => [
                    [ "name" => "LINK_TEXT", "value" => util_encode($menu->__toString()) ],
                    [ "name" => "LINK_HREF", "value" => Pt_WebUtil::url("pt_menuView.phtml?menuId=".$menu->getId()) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $infoMessage = GT($textMap, "IA.MENU_LINK_TEXT_LINK_HREF_HAS_BEEN_CREATED");
        if (strcmp($name1, $name)) {
            $infoMessage .= " " . GT($textMap, "IA.MENU_NAME_HAS_BEN_ADJUSTED");
        }
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Session::setProperty(ATTR_REFRESH_TOP, "TRUE");
    }


    /**
     * Create new menu for new Object Definition.
     *
     * @param Pt_DataObjectDef $objDef
     * @param string           $name
     * @param string           $description
     * @param int              $parentId
     * @param Pt_Application[] $apps
     *
     * @return Pt_Menu
     */
    public static function createObjTab(Pt_DataObjectDef $objDef, $name, $description, $parentId, $apps) {
        $objDefId = $objDef->getId();
        $orderNo = Pt_MenuManager::getNextOrderNo($parentId);
    
        $props = [];
        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_Menu::MENU_LABELS_INDEXES);
        
        foreach ( Pt_Menu::MENU_LABELS_INDEXES as $index ) {
            if ( $customLabels[$index] ?? false ) {
                $props[Pt_I18nLabels::getParamName($index)] = $customLabels[$index]->getCustomLabelId();
            }
        }
        
        $menu = Pt_MenuManager::create(0, $objDefId, $orderNo, $customLabels, $name, $parentId, $props, TYPE_LIST, null, $description);  // Bug 39775
        $menuId = $menu->getId();

        $orderNo2 = 1;
        Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.LIST", $menuId, null, TYPE_LIST, null, null);
        Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.ADD", $menuId, null, TYPE_NEW, null, null);
        Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.IMPORT",$menuId, null, TYPE_IMPORT, "pt_import.phtml", null);
        Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.TEMPLATES", $menuId, null,
                               TYPE_TEMPLATES, "pt_templates.phtml", null);
        if ($objDef->isEvent() || $objDef->isTask()) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.CALENDAR", $menuId, null, TYPE_CALENDAR, "pt_calendarView.phtml", null);
        }

        if ($parentId <= 0) {
            foreach ($apps as $app) {
                Pt_ApplicationManager::addMenu($app, $menu);
            }
        }

        return $menu;
    }

    /**
     * Create new menu.
     */
    public static function create() {
        $tokens = [ "IA.LIST", "IA.ADD", "IA.IMPORT", "IA.TEMPLATES", "IA.MENU_NAME_HAS_BEN_ADJUSTED", "IA.CSP_VIOLATIONS_FOUND" ];
        $placeholderTokens = [];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_Menu::MENU_LABELS_INDEXES);
        $name1 = http_getParameter(FIELD_NAME);
        $name = util_alphanumeric($name1);
        $description = http_getParameter(FIELD_DESCRIPTION);
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        $pageType = http_getIntParameter(FIELD_PAGE_TYPE, TYPE_GENERIC);
        $parentId = http_getIntParameter(FIELD_PARENT_ID);
        $reportId = http_getIntParameter(FIELD_REPORT_ID);
        if (Pt_MenuManager::getByDisplayName($name) != null && !util_inArgs($pageType, TYPE_IMPORT, TYPE_TEMPLATES)) {
            throw new Pt_I18nException('PAAS-0363', "Menu with display name $name already exists", [ 'NAME' => $name ]);
        }

        $orderNo = Pt_MenuManager::getNextOrderNo($parentId);

        XACT_BEGIN('PLATFORM');
        $menu = null;
        try {
            $pageURL = null;
            $props = null;

            if ($pageType==TYPE_GENERIC && $parentId <= 0) {
                $page = Pt_WebPageManager::create(0, $objDefId, $name, TYPE_GENERIC, true, false, null, '');
                Pt_PageSectionManager::createSection($page, $name, BORDER_NONE, TWO_COLUMNS, false, true, '');
                $pageURL = strval($page->getId());

                if ($objDef != null) {
                    $presentationName = 'All '.$objDef->getPluralName();
                    $props2 = [];
                    $props2[FIELD_OBJ_DEF_ID2] = $objDefId;
                    $props2[FIELD_SHOW_NEW_LINK] = true;
                    $props2[FIELD_SHOW_QUICK_CREATE] = true;
                    $props2[FIELD_SHOW_VIEW_SELECTOR] = true;
                    $props2[FIELD_ORDER_VIEW_SELECTOR] = false;
                    $props2[FIELD_SHOW_CHECK_BOXES] = true;
                    $section = Pt_PageSectionManager::createSection($page, $presentationName, BORDER_THICK,
                                                                    ONE_COLUMN, false, false, '');
                    Pt_PageCellManager::createCell($section, "Pt_PageableList", '', 0, ALIGNMENT_LEFT,
                                                   $presentationName, $props2);
                }
                Pt_Cache::setWebPage($page);
                $props = [FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON)];
            } else if ($pageType==TYPE_REPORT) {
                $pageURL = Pt_StandardUtil::getReportURL($reportId);
                $props = [FIELD_REPORT_ID => $reportId, FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON)];
            } else if ($pageType==TYPE_CALENDAR) {
                $pageURL = "pt_calendarView.phtml";
                $parentMenu = Pt_MenuManager::getById($parentId);
                if ($parentMenu != null) {
                    $objDefId = $parentMenu->getObjectDefId();
                }
            } else if ($pageType==TYPE_IMPORT) {
                $pageURL = "pt_import.phtml";
            } else if ($pageType==TYPE_TEMPLATES) {
                $pageURL = "pt_templates.phtml";
            }
            else if ($pageType==TYPE_CRW) {
                $reportId = http_getIntParameter(FIELD_CRW_REPORT_ID);
                $pageURL = Pt_StandardUtil::getCrwReportURL($reportId);
                $props = array(FIELD_REPORT_ID => $reportId, FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON));
            }
            else if ($pageType==TYPE_DV) {
                $reportId = http_getIntParameter(FIELD_DV_REPORT_ID);
                $pageURL = Pt_StandardUtil::getIVEReportURL($reportId);
                $props = array(FIELD_REPORT_ID => $reportId, FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON));
            }

            foreach ( Pt_Menu::MENU_LABELS_INDEXES as $index ) {
                if ( $customLabels[$index] ?? false ) {
                    $props[Pt_I18nLabels::getParamName($index)] = $customLabels[$index]->getCustomLabelId();
                }
            }

            $menu = Pt_MenuManager::create(0, $objDefId, $orderNo, $customLabels, $name, $parentId, $props, $pageType, $pageURL, $description);

            if ($pageType==TYPE_LIST && $objDef != null) {
                $tabId = $menu->getId();
                $orderNo2 = 1;
                Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.LIST", $tabId, null, TYPE_LIST, null, null);
                Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.ADD", $tabId, null, TYPE_NEW, null, null);
                Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.IMPORT", $tabId, null, TYPE_IMPORT, "pt_import.phtml", null);
                /** @noinspection PhpUnusedLocalVariableInspection */
                Pt_MenuManager::create(0, $objDefId, $orderNo2++, [], "IA.TEMPLATES", $tabId, null,
                                       TYPE_TEMPLATES, "pt_templates.phtml", null);
            }

            // For tabs, add new tab to apps.
            if ($parentId <= 0) {
                $apps = Pt_ApplicationManager::getAll();
                foreach ($apps as $app) {
                    if (!http_isBoxChecked('app_'.$app->getId())) {
                        continue;
                    }
                    Pt_ApplicationManager::addMenu($app, $menu);
 
                    $pol = new CustomPolicy($app->getAppCode(), GetMyUserid());
                    $ok = $pol->CheckAndAddUserPolicy($menu->getId(), 'menu', 'MENU');
                    if (!$ok) {
                        //I18N : TODO
                        throw new Exception(Pt_StandardUtil::globalErrorMessages());
                    }

                    if ( FeatureConfigManagerFactory::getInstance()
                                                    ->isFeatureEnabled('PT_ENABLE_CSP_WARNINGS') ) {
                        if ( ! Pt_CspViolationFactory::instantiate($app)->validate() ) {
                            $hasCspIssues = true;
                        }
                    }
                }
            }

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $localPlaceholderTokens = [
            [
                "id"           => "IA.MENU_LINK_TEXT_LINK_HREF_HAS_BEEN_CREATED",
                "placeHolders" => [
                    [ "name" => "LINK_TEXT", "value" => util_encode($menu->__toString()) ],
                    [ "name" => "LINK_HREF", "value" => Pt_WebUtil::url("pt_menuView.phtml?menuId=".$menu->getId()) ],
                ],
            ],
        ];
        $txtMap = getIntlTextMap([], $localPlaceholderTokens);
        $textMap = array_merge($textMap, $txtMap);

        $infoMessage = GT($textMap, "IA.MENU_LINK_TEXT_LINK_HREF_HAS_BEEN_CREATED");
        if (strcmp($name1, $name)) {
            $infoMessage.=" " . GT($textMap, "IA.MENU_NAME_HAS_BEN_ADJUSTED");
        }
        if ( isset($hasCspIssues) && $hasCspIssues ) {
            $infoMessage .= " " . GT($textMap, "IA.CSP_VIOLATIONS_FOUND");
        }
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Session::setProperty(ATTR_REFRESH_TOP, "TRUE");
    }

    /**
     * Update existing menu.
     */
    public static function update() {
        $id = http_getIntParameter(FIELD_MENU_ID);
        $menu = Pt_MenuManager::getById($id);
        if ($menu == null) {
            throw new Pt_I18nException('PAAS-0365', "Pt_Menu with id $id not found", [ 'ID' => $id ]);
        }
        if ($menu->isStandard()) {
            throw new Pt_I18nException('PAAS-0366', "Cannot edit standard menu");
        }
        /** @noinspection PhpUndefinedVariableInspection */
        if ( Pt_MenuManager::getByDisplayName($name, $id) != null) {
            throw new Pt_I18nException('PAAS-0367', "Menu with display name $name already exists", [ 'NAME' => $name ]);
        }

        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_Menu::MENU_LABELS_INDEXES);
        $name1 = http_getParameter(FIELD_NAME);
        $name = util_alphanumeric($name1);
        $description = http_getParameter(FIELD_DESCRIPTION);
        $parentId = http_getIntParameter(FIELD_PARENT_ID);
        $reportId = http_getIntParameter(FIELD_REPORT_ID);
        
        $pageURL = $menu->getRawPageURL();
        $props = null;
        
        $pageType = $menu->getPageType();
        if ($pageType == TYPE_GENERIC) {      // Bug 40490
            $pageId = http_getIntParameter(FIELD_PAGE_ID);
            $pageURL = strval($pageId);
            $props = [FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON)];
        } else if ($pageType == TYPE_REPORT) {   // Bug 40087
            $pageURL = Pt_StandardUtil::getReportURL($reportId);
            $props = [FIELD_REPORT_ID => $reportId, FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON)];
            //in case of crw report set the corresponding id
            if ($reportId === 0 ) {
                $reportId = http_getParameter(FIELD_REPORT_ID);
                if (substr($reportId,0,4) === 'CRW_') {
                    $props['reportType'] = Reporting::CRW_REPORT_TYPE;
                    $props['crwId'] = $reportId;
                }
            }
        }  else if ($pageType == TYPE_CRW) {
            $reportId = http_getParameter(FIELD_CRW_REPORT_ID);
            $pageURL = Pt_StandardUtil::getReportURL($reportId);
            $props = array(FIELD_REPORT_ID => $reportId, FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON));
            $props['reportType'] = Reporting::CRW_REPORT_TYPE;
            $props['crwId'] = $reportId;
        } else if($pageType == TYPE_DV){
            $reportId = http_getParameter(FIELD_DV_REPORT_ID);
            $pageURL = Pt_StandardUtil::getReportURL($reportId);
            $props = array(FIELD_REPORT_ID => $reportId, FIELD_OBJ_ICON => http_getParameter(FIELD_OBJ_ICON));
            $props['reportType'] = Reporting::DV_REPORT_TYPE;
            $props['crwId'] = $reportId;
        }
        foreach ( Pt_Menu::MENU_LABELS_INDEXES as $index ) {
            if ( $customLabels[$index] ?? false ) {
                $props[Pt_I18nLabels::getParamName($index)] = $customLabels[$index]->getCustomLabelId();
            }
        }

        $appsRemove = [];
        if ($menu->isTopLevel() && ($parentId > 0)) {
            foreach (Pt_ApplicationManager::getAll() as $app) {
                if ($app->hasTab($menu->getTabId())) {
                    $appsRemove[] = $app;
                }
            }
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_MenuManager::update($menu, $customLabels, $name, $description, $pageURL, $parentId, $props);

            foreach ($appsRemove as $app) {
                Pt_ApplicationManager::removeMenu($app, $menu);
            }

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $tokens = [ "IA.MENU_NAME_HAS_BEN_ADJUSTED" ];
        $placeholderTokens = [
            [
                "id"           => "IA.MENU_LINK_TEXT_LINK_HREF_HAS_BEEN_UPDATED",
                "placeHolders" => [
                    [ "name" => "LINK_TEXT", "value" => util_encode($menu->__toString()) ],
                    [ "name" => "LINK_HREF", "value" => Pt_WebUtil::url("pt_menuView.phtml?menuId=".$menu->getId()) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $infoMessage = GT($textMap, "IA.MENU_LINK_TEXT_LINK_HREF_HAS_BEEN_UPDATED");
        if (strcmp($name1, $name)) {
            $infoMessage .= " " . GT($textMap, "IA.MENU_NAME_HAS_BEN_ADJUSTED");
        }
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Session::setProperty(ATTR_REFRESH_TOP, "TRUE");
    }

    /**
     * Delete existing menu.
     */
    public static function delete() {
        $id = http_getIntParameter(FIELD_MENU_ID);
        $parentId = http_getIntParameter(FIELD_PARENT_ID);
        $menu = Pt_MenuManager::getById($id);
        if ($menu == null) {
            return;
        }

        XACT_BEGIN('PLATFORM');
        try {
            foreach (Pt_ApplicationManager::getAll() as $app) {
                Pt_ApplicationManager::removeMenu($app, $menu);
            }

            Pt_MenuManager::delete($menu);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $tokens = [];
        $placeholderTokens = [
            [
                "id"           => "IA.MENU_MENU_NAME_HAS_BEEN_DELETED",
                "placeHolders" => [
                    [ "name" => "MENU_NAME", "value" => util_encode($menu) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $infoMessage = GT($textMap, "IA.MENU_MENU_NAME_HAS_BEEN_DELETED");
        Session::setProperty(MENU_PARENT_ID, $parentId);
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Session::setProperty(ATTR_REFRESH_TOP, "TRUE");
    }

    /**
     * Remove menu from application
     */
    public static function remove() {
        $id = http_getIntParameter(FIELD_MENU_ID);
        $menu = Pt_MenuManager::getById($id);
        if ($menu == null) {
            return;
        }
        $appId = http_getIntParameter(FIELD_APPLICATION_ID);
        $app = Pt_ApplicationManager::getById($appId);
        if ($app == null) {
            //<editor-fold desc="Description">
            return;
        }
        //</editor-fold>

        XACT_BEGIN('PLATFORM');
        try {
            Pt_ApplicationManager::removeMenu($app, $menu);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $tokens = [];
        $placeholderTokens = [
            [
                "id"           => "IA.MENU_NAME_MENU_HAS_BEEN_REMOVED_FROM_APPLICATIO",
                "placeHolders" => [
                    [ "name" => "MENU_NAME", "value" => util_encode($menu) ],
                    [ "name" => "APP_NAME", "value" => "$app" ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $infoMessage = GT($textMap, "IA.MENU_NAME_MENU_HAS_BEEN_REMOVED_FROM_APPLICATIO");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Reorder menus for given $tab.
     */
    public static function reorder() {
        $tokens = [ "IA.DISPLAY_ORDER_OF_MENUS_HAS_BEEN_UPDATED" ];
        $placeholderTokens = [];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);

        $id = http_getIntParameter(FIELD_PARENT_ID);
        if (!$id) {
            $id = http_getIntParameter(FIELD_ID);
        }
        $tab = Pt_MenuManager::getById($id);
        if ($tab == null) {
            throw new Pt_I18nException('PAAS-0368', "Pt_Menu with id $id not found", [ 'ID' => $id ]);
        }
        $menuIds = http_getIntArray(FIELD_ASSIGNED);

        XACT_BEGIN('PLATFORM');
        try {
            for ($k=0,$c=count($menuIds); $k<$c; $k++) {
                $m = Pt_MenuManager::getById($menuIds[$k]);
                if ($m == null || $m->getParentId() != $tab->getId()) {
                    continue;
                }
                Pt_MenuManager::setOrderNo($m, $k+1);
            }

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            db_rollback($ex);
        }

        $infoMessage = GT($textMap, "IA.DISPLAY_ORDER_OF_MENUS_HAS_BEEN_UPDATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
        Session::setProperty(ATTR_REFRESH_TOP, "TRUE");
    }
    
    /**
     * Generate labels for component
     *
     * @param Pt_Menu $menu
     *
     */
    private static function generateLabelForComponent(Pt_Menu $menu)
    {
        $labels = $menu->createMissingObjectLabels(Pt_Menu::MENU_LABELS_INDEXES, $menu->getAllLabels(),
                                                   [ $menu->__toString() ]);
        $menu->setAllLabels($labels);
        $props = $menu->getProperties();
        foreach ( Pt_Menu::MENU_LABELS_INDEXES as $index ) {
            if ( $labels[$index] ?? false ) {
                $props[Pt_I18nLabels::getParamName($index)] = $labels[$index]->getCustomLabelId();
            }
        }
        Pt_MenuManager::update($menu, $labels, $menu->getMenuUnitName(), $menu->getDescription(),
                               $menu->getPageURL(), $menu->getParentId(), $props);
    }
    
    /**
     * Get menu's labels
     *
     * @param Pt_Menu $menu
     * @param string  $baseLanguage
     * @param string  $desiredLanguage
     *
     * @return array
     */
    public static function exportLabelForComponent(Pt_Menu $menu, string $baseLanguage, string $desiredLanguage)
    {
        $customLabels = [];
        self::generateLabelForComponent($menu);
        $menuLabelIdx = (int) Pt_Menu::MENU_LABEL_IDX;
        $baseLang = $menu->getSpecificLabelForComponent("", $baseLanguage, $menuLabelIdx);
        $desiredLang = $menu->getSpecificLabelForComponent("", $desiredLanguage, $menuLabelIdx);
        if ( $labelId = $menu->getLabels($menuLabelIdx)
                             ->getCustomLabelId() ?? null ) {
            $customLabels[$labelId] = [ $baseLang, $desiredLang ];
        }
        
        return $customLabels;
    }

}
