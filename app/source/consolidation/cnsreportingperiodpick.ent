<?
$kSchemas['cnsreportingperiodpick'] = array(

    'schema' => array( 'NAME' => 'name', 'START_DATE' => 'start_date', 'END_DATE' => 'end_date' ),


    'object' => array( 'NAME','START_DATE','END_DATE'),


    'fieldinfo' => array(
        array (
            'fullname' => 'IA.NAME',
            'desc' => 'IA.NAME',
            'type' => array (
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 80, 
                'size' => 60, 
                'format' => '/^.{1,80}$/'
            ),
            'required' => true,
            'path' => 'NAME'
        ),
        array (
            'fullname' => 'IA.START_DATE',
            'desc' => 'IA.START_DATE',
            'type' => $gDateType,
            'required' => true,
            'path' => 'START_DATE'
        ),
        array (
            'fullname' => 'IA.END_DATE',
            'desc' => 'IA.END_DATE',
            'type' => $gDateType,
            'required' => true,
            'path' => 'END_DATE'
        ),
    ),

    'table'     => 'v_cnsreportingperiodpick',
    'vid'       => 'NAME',
    'module'    => 'cs',
    'printas'   => 'IA.REPORTING_PERIOD',
    'pluralprintas'   => 'IA.REPORTING_PERIODS'
);

