<?
$kSchemas['cnsrefreshdata'] = array(
    'fake' => true,
    'schema' => array ( 
        'SUBSIDIARY' => array( 'subsidiary.*' => 'subsidiary.*' ),
        'CNSREPORTINGPERIODPICK' => array( 'cnsreportingperiodpick.*' => 'cnsreportingperiodpick.*' ),
    ),
    'fieldinfo' => array (
        array ( 
            'fullname' => 'IA.SELECT_A_COMPANY_ID',
            'desc' => 'IA.COMPANY_ID',
            'required' => true,
            'type' => array ( 
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'subsidiary',
                'maxlength' => 40,
            ),
            'path' => 'SUBSIDIARY.SUBSIDIARYID'
        ),
        array ( 
            'fullname' => 'IA.SELECT_A_REPORTING_PERIOD_BLANK_FOR_ALL',
            'desc' => 'IA.REPORTING_PERIOD',
            'required' => false,
            'type' => array ( 
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'cnsreportingperiodpick',
                'maxlength' => 40,
            ),
            'path' => 'CNSREPORTINGPERIODPICK.NAME'
        )
    ),
    'printas' => 'IA.SELECT_A_COMPANY_ID_AND_REPORTING_PERIOD',
    'pluralprintas' => 'IA.SELECT_A_COMPANY_ID_AND_REPORTING_PERIODS',
    'module' => 'cs',
    'table' => 'dummy',
    'vid'     => 'dummy'
);
