<?  

/**
    * FILE:
*    AUTHOR:        rpn
*    DESCRIPTION:    entity file for cnsperiod object
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/


$kSchemas['cnsperiod'] = array(
    'children' => array(
        'reportingperiod' => array('fkey' => 'periodkey', 'invfkey' => 'record#', 'table' => 'glbudgettype', 'join' => 'outer' ),
        'subsidiary' => array('fkey' => 'subsidiarykey', 'invfkey' => 'record#', 'table' => 'subsidiary', 'join' => 'outer' ),
    ),

    'schema' => array (
        'RECORDNO'        =>    'record#',
        'SUBSIDIARY'        =>    array('subsidiary.*' => 'subsidiary.*'),
        'SUBPERIODNAME'    =>    'subperiodname',
        'END_DATE'            =>    'end_date',
        'PERCENTAGE'        =>    'percentage',
        'REPORTINGPERIOD'    => array('reportingperiod.*' => 'reportingperiod.*'),
        'SUBPERIODKEY'        =>    'subperiodkey',
        'SUBSIDIARYKEY'        =>    'subsidiarykey',
        'PERBSRRATE'        =>    'perbsrrate',
        'PERWARRATE'        =>    'perwarrate',
    ),


    'object' => array (
            'SUBPERIODNAME','REPORTINGPERIOD.NAME','END_DATE','PERCENTAGE','SUBSIDIARY.SUBSIDIARYID','RECORDNO','SUBPERIODKEY','SUBSIDIARYKEY', 'PERBSRRATE', 'PERWARRATE', 'PERIODKEY'
    ),


    'nexus' => array( 'reportingperiod' => array( 'object' => 'reportingperiod'
                                        , 'relation' => MANY2ONE
                                        , 'field' => 'REPORTINGPERIOD.NAME'
                                        , 'printas' => 'IA.PARENT_REPORTING_PERIOD'
                                        ),

        ),

    'publish' =>  array (
            'SUBPERIODNAME','END_DATE','PERCENTAGE', 'PERBSRRATE', 'PERWARRATE',
    ),


    'fieldinfo' => array (
        array ( 
            'fullname' => 'IA.RECORD_NUMBER',
            'desc' => 'IA.RECORD_NUMBER',
            'required' => false,
            'readonly' => true,
            'hidden'    =>     true,
            'type' => array ( 
                'size' => 8,
                'maxlength' => 8,
                'ptype' => 'integer',
                'type' => 'integer',
                'format' => $gRecordNoFormat
            ),
            'path' => 'RECORDNO',
            'id' => 1
        ),
        array ( 
            'fullname' => 'IA.COMPANY_ID',
            'desc' => 'IA.COMPANY_ID',
            'required' => false,
            'readonly' => true,
            'type' => array ( 
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'subsidiary',
                'maxlength' => 40,
            ),
            'path' => 'SUBSIDIARY.SUBSIDIARYID',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.PARENT_REPORTING_PERIOD_NAME',
            'desc' => 'IA.PARENT_REPORTING_PERIOD_NAME',
            'required' => false,
            'type' => array (
                'maxlength' => 80,
                'size' => 60, 
                'ptype' => 'ptr', 
                'type' => 'ptr', 
                'entity' => 'reportingperiod', 
                'format' => '/^.{0,80}$/',
            ),
            'path' => 'REPORTINGPERIOD.NAME',
            'id' => 3
        ),
        array(
            'fullname' => 'IA.SUBSIDIARY_REPORTING_PERIOD_NAME',
            'desc' => 'IA.SUBSIDAIRY_REPORTING_PERIOD_NAME',
            'required' => false,
            'readonly' => true,
            'type' => array (
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 80,
                'format' => '/^.{0,80}$/'
            ),
            'path' => 'SUBPERIODNAME',
            'id' => 4
        ),
        array (
            'fullname' => 'IA.PERCENTAGE_OWNERSHIP',
            'desc' => 'IA.PERCENTAGE_OWNERSHIP',
            'type' => array (
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gDecimalFormat
            ),
            'default' => '100',
            'path' => 'PERCENTAGE',
            'id' => 5
        ),
        array (
            'fullname' => 'IA.PERIOD_END_DATE',
            'readonly' => true,
            'desc' => 'IA.PERIOD_END_DATE',
            'type' => array (
                'ptype' => 'date',
                'type' => 'date',
            ),
            'path' => 'END_DATE',
            'id' => 6
        ),
        array(
            'fullname' => 'IA.CHILD_PERIOD_RECORD_NUMBER',
            'desc' => 'IA.CHILD_PERIOD_RECORD_NUMBER',
            'required' => false,
            'readonly' => true,
            'hidden' => true,
            'type' => array (
                'ptype' => 'integer', 
                'type' => 'integer', 
                'format' => $gRecordNoFormat
            ),
            'path' => 'SUBPERIODKEY',
            'id' => 7
        ),
        array(
            'fullname' => 'IA.SUBSIDIARY_RECORD_NUMBER',
            'desc' => 'IA.SUBSIDIARY_RECORD_NUMBER',
            'required' => false,
            'readonly' => true,
            'hidden' => true,
            'type' => array (
                'ptype' => 'integer', 
                'type' => 'integer', 
                'format' => $gRecordNoFormat
            ),
            'path' => 'SUBSIDIARYKEY',
            'id' => 8
        ),
        array (
            'fullname' => 'IA.PERIOD_BALANCE_SHEET_RATE',
            'desc' => 'IA.PERIOD_BALANCE_SHEET_RATE',
            'required' => true,
            'type' => array (
                'ptype' => 'decimal',
                'type' => 'decimal',
                'size' => 14,        
                'maxlength' => 12,
                'format' => $gDecimalFormat,
            ),
            'path' => 'PERBSRRATE',
            'noformat' => true,
            'id' => 9
        ),
        array (
            'fullname' => 'IA.PERIOD_WEIGHTED_AVERAGE_RATE',
            'desc' => 'IA.PERIOD_WEIGHTED_AVERAGE_RATE',
            'required' => true,
            'type' => array (
                'ptype' => 'decimal',
                'type' => 'decimal',
                'size' => 14,        
                'maxlength' => 12,
                'format' => $gDecimalFormat,
            ),
            'path' => 'PERWARRATE',
            'noformat' => true,
            'id' => 10
        ),
        array('fullname' => 'IA.REPORTING_PERIOD_KEY',
              'path' => 'PERIODKEY',
              'id' => 11),
    ),

    'table'     => 'cnsperiod',
    'vid'         => 'RECORDNO',
    'printas' => 'IA.CONSOLIDATION_PERIOD',
    'pluralprintas' => 'IA.CONSOLIDATION_PERIODS',
    'module'     => 'cs',
    'nosysview' => true,
    'description' => 'IA.THE_PERIODS_IN_THE_CONSOLIDATION_COMPANY_WITH_LINK',
);

