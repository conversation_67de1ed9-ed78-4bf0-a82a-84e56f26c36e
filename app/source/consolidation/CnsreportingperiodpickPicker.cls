<?
import('NLister');
import('NPicker');

/**
 * Class CnsreportingperiodpickPicker
 */
class CnsreportingperiodpickPicker extends NPicker {

	function __construct() {
		parent::__construct(
			array(
				'entity'    	=>  'cnsreportingperiodpick',
				'pickfield'		=>  'NAME',
				'entitynostatus' => true,
				'fields'        =>  array('NAME','START_DATE','END_DATE'),
			)
		);
	}

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @bool $addYuiCss  include the YUI css files
     * @param bool $addYuiCss
     */
	function showScripts($addYuiCss = true) {
		$_refresh = Request::$r->_refresh;
        parent::showScripts($addYuiCss);
		UIUtils::PrintLayerSetupForBrowser();
		UIUtils::PrintSetField($_refresh);
	}
}

