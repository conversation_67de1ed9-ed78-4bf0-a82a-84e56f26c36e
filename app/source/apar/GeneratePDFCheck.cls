<?
//=============================================================================
//
//	FILE:			GeneratePDFCheck.cls
//	AUTHOR:	NKS
//	DESCRIPTION:
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

class GeneratePDFCheck
{

    //take all variables that can be customized.
    //and these variables will be populated based Currency.

    /**
     * @var string $x1
     */
    var $x1;
    /**
     * @var string $x2
     */
    var $x2;
    /**
     * @var string $x3
     */
    var $x3;
    /**
     * @var string $x4
     */
    var $x4;
    /**
     * @var string $y1
     */
    var $y1;
    /**
     * @var string $y2
     */
    var $y2;
    /**
     * @var string $y3
     */
    var $y3;
    /**
     * @var string $MemoY
     */
    var $MemoY;
    /**
     * @var string $signatureX
     */
    var $signatureX;
    /**
     * @var string $signatureTop
     */
    var $signatureTop;
    /**
     * @var string $micrCheckNumX
     */
    var $micrCheckNumX;
    /**
     * @var string $micrBranchIDX
     */
    var $micrBranchIDX;
    /**
     * @var string $micrRoutingNumX
     */
    var $micrRoutingNumX;
    /**
     * @var string $micrAcctNumRightX
     */
    var $micrAcctNumRightX;
    /**
     * @var string $micrAcctNumLeftX
     */
    var $micrAcctNumLeftX;
    /**
     * @var string $micrY
     */
    var $micrY;
    /**
     * @var string $checktop_T
     */
    var $checktop_T;
    /**
     * @var string $checktop_M
     */
    var $checktop_M;
    /**
     * @var string $checktop_B
     */
    var $checktop_B;
    /**
     * @var string $invoicestub1_top_T
     */
    var $invoicestub1_top_T;
    /**
     * @var string $invoicestub1_top_M
     */
    var $invoicestub1_top_M;
    /**
     * @var string $invoicestub1_top_B
     */
    var $invoicestub1_top_B;
    /**
     * @var string $invoicestub2_top_T
     */
    var $invoicestub2_top_T;
    /**
     * @var string $invoicestub2_top_M
     */
    var $invoicestub2_top_M;
    /**
     * @var string $invoicestub2_top_B
     */
    var $invoicestub2_top_B;


    //Template page Master
    /**
     * @var string $pgwidth
     */
    var $pgwidth;
    /**
     * @var string $pgheight
     */
    var $pgheight;
    /**
     * @var string $cadDateTag
     */
    var $cadDateTag;
    /**
     * @var string $locale
     */
    var $locale;
    /**
     * @var int $checksperpage
     */
    var $checksperpage;

    /**
     * Template invoice Stub With Details
     *
     * @var string $PageText
     */
    var $PageText;

    /**
     * @var bool $printasOverflow
     */
    protected $printasOverflow;
    /**
     * @var int $fontSizePayTo
     */
    protected $fontSizePayTo;

    /**
     * memo offset for preprinted check in case of single check
     *
     * @var int $ppMemoSCOffset
     */
    protected $ppMemoSCOffset;
    /**
     * Pay to info default offset for single check
     *
     * @var int $ptInfoSCOffset
     */
    protected $ptInfoSCOffset;
    /**
     * Invoice top stub offset
     *
     * @var int $topInvoiceStubOffset
     */
    protected $topInvoiceStubOffset;
    /**
     * @var string $printon
     */
    var $printon;
    /**
     * @var string $printCheckNumInfo_fontSize
     */
    var $printCheckNumInfo_fontSize;
    /**
     * @var string $printAmountInfo_fontSize
     */
    var $printAmountInfo_fontSize;

    /**
     * @bool $isAddress3InChecksEnabled
     */
    private bool $isAddress3InChecksEnabled = false;

    /**
     * @return bool
     */
    public function isAddress3InChecksEnabled(): bool
    {
        return $this->isAddress3InChecksEnabled;
    }

    /**
     * @param bool $isAddress3InChecksEnabled
     * @return void
     */
    public function setIsAddress3InChecksEnabled(bool $isAddress3InChecksEnabled): void
    {
        $this->isAddress3InChecksEnabled = $isAddress3InChecksEnabled;
    }

    function __construct()
    {
        $this->SetDefaults();
    }


    protected function SetDefaults()
    {
        //Global Variables
        $this->x1="0";
        $this->x2="0.6in";
        $this->x3="4.0in";
        $this->x4="6.4in";
        $this->y1="0";
        $this->y2="0.15";
        $this->y3="0.95in";
        $this->MemoY="0.73";
        
        $this->micrCheckNumX="2.04";
        $this->micrBranchIDX="2.04";

        $this->micrRoutingNumX="2.165";
        $this->micrAcctNumRightX="5.915";
        $this->micrAcctNumLeftX="3.54";
        $this->micrY="2.80";
        $this->checktop_T="0.0in";
        $this->checktop_M="3.5in";
        $this->checktop_B="7.6in";
        $this->invoicestub1_top_T="7.2in";
        $this->invoicestub1_top_M="7.2in";
        $this->invoicestub1_top_B="-0.15in";
        $this->invoicestub2_top_T="3.35in";
        $this->invoicestub2_top_M="-0.15in";
        $this->invoicestub2_top_B="3.85in";

        //Template page Master
        $this->pgwidth="8.5";
        $this->pgheight="11";

        //Template invoice Stub With Details
        $this->PageText="Page";
        $this->printAmountInfo_fontSize="7pt";
        $this->printCheckNumInfo_fontSize="10pt";
        $this->cadDateTag="YYYYMMDD";
        //Setting preprinted check memo offset
        $this->ppMemoSCOffset = 2.5;
        //setting for preprinted check pay to info offset
        $this->ptInfoSCOffset = 0;
        //Setting for invoid stub top offset
        $this->topInvoiceStubOffset = 0;
    }

    /**
     * @return string
     */
    function ReadCADxsl () {
        return "";
    }

    /**
     * @return string
     */
    function ReadBlackCheckStockxsl () : string {
        return "";
    }
    /**
     * @param string $printFormat
     * @return string
     */
    function ReadUSxsl (string $printFormat) : string {
        return "";
    }

    /**
     * @param string $locale
     * @param string $printon
     * @param string $securityEnabledPP
     *
     * @return string
     */
    function GenerateXSL($locale='US', $printon='B' , $securityEnabledPP='F')
    {
        //this function will call other functions to genrate different bodies
        //and append it to one final string xsl
        $this->locale = ($printon == 'B') ? $locale : 'US';
        $this->printon = $printon;
        $this->signatureX= ( $securityEnabledPP == 'H' ) ? "4.65in" : "4.3in";
        $this->signatureTop=( $securityEnabledPP == 'H' ) ? "2.25" : "2.15";
        /** @noinspection PhpUnusedLocalVariableInspection */
        $final_xsl='';
        $final_xsl=$this->GetHeader();
        $final_xsl.="\n".isl_trim($this->GetGlobalVariables());
        $final_xsl.="\n".isl_trim($this->GetTemplateRoot());
        $final_xsl.="\n".isl_trim($this->GetTemplatePageMaster());
        $final_xsl.="\n".isl_trim($this->GetTemplateCheck());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintPage());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintPageWithDetails());
        $final_xsl.="\n".isl_trim($this->GetTemplateCheckStub());
        $final_xsl.="\n".isl_trim($this->GetTemplateCheckStub_firstPage());
        $final_xsl.="\n".isl_trim($this->GetTemplateCheckStub_restPages());
        $final_xsl.="\n".isl_trim($this->GetTemplateNonPrintedCheckTable());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintCompanyInfo());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintPayToInfo());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintAmountInfo());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintBankInfo());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintSignature());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintCheckNumInfo());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintMemoInfo());
        if($this->locale=='MX') {
            $final_xsl.="\n".isl_trim($this->GetTemplatePrintMXNMICRLine());
        } elseif ($this->locale == 'CA') {
            $final_xsl.="\n".isl_trim($this->GetTemplatePrintCADMICRLine());
        } else {
            $final_xsl.= "\n" ;
            $final_xsl.= ($this->printon == 'B') ? isl_trim($this->GetTemplatePrintMICRLine()) : isl_trim($this->GetTemplatePrintMICRLinePrePrintedCheck());
        }
        $final_xsl.="\n".isl_trim($this->GetTemplateInvoiceStub());
        $final_xsl.="\n".isl_trim($this->GetTemplateInvoiceStubWithDetails());
        $final_xsl.="\n".isl_trim($this->GetTemplateInvoiceTable());
        $final_xsl.="\n".isl_trim($this->GetTemplateInvoiceTableWithDetails());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintHeaders());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintDetailedHeadersLine1());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintDetailedHeadersLine2());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintRows());
        $final_xsl.="\n".isl_trim($this->GetTemplatePrintRowsWithDetails());
        $final_xsl.="\n".isl_trim($this->GetTemplateInvoice());
        $final_xsl.="\n".isl_trim($this->GetTemplateInvoiceWithDetails());
        $final_xsl.="\n".isl_trim($this->GetTemplateTotals());
        $final_xsl.="\n".isl_trim($this->GetTemplateContd());
        $final_xsl.="\n".isl_trim($this->GetStrReplaceTemplate());
        $final_xsl.="\n"."</xsl:stylesheet>";
        return isl_trim($final_xsl);
    }


    /**
     * @return string
     */
    function GetHeader()
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
					<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
					<xsl:output method="xml" encoding="utf-8"/>';
    }

    /**
     * @return string
     */
    function GetGlobalVariables()
    {

        $global_variables='<xsl:variable name="VarMultiCurrency">'."\n".
            '<xsl:value-of select="/ROOT/flags/@MULTICURRENCY"/>'."\n".
            '</xsl:variable>'."\n".
            '<!-- Global variables -->'."\n".
            '<xsl:variable name="pgwidth">'.$this->pgwidth.'</xsl:variable>'."\n".
            '<xsl:variable name="pgheight">'.$this->pgheight.'</xsl:variable>'."\n".
            '<xsl:variable name="x1">'.$this->x1.'</xsl:variable>'."\n".
            '<xsl:variable name="x2">'.$this->x2.'</xsl:variable>'."\n".
            '<xsl:variable name="x3">'.$this->x3.'</xsl:variable>'."\n".
            '<xsl:variable name="x4">'.$this->x4.'</xsl:variable>'."\n".
            '<xsl:variable name="y1">'.$this->y1.'</xsl:variable>'."\n".
            '<xsl:variable name="y2">'.$this->y2.'</xsl:variable>'."\n".
            '<xsl:variable name="y3">'.$this->y3.'</xsl:variable>'."\n".
            '<xsl:variable name="companyAddressX" select="$x1"/>'."\n".
            '<xsl:variable name="companyAddressY" select="$y1"/>'."\n".
            '<xsl:variable name="vendorAddressX" select="$x2"/>'."\n".
            '<xsl:variable name="PayToX" select="$x1"/>'."\n".
            '<xsl:variable name="MemoX" select="$x3"/>'."\n".
            '<xsl:variable name="MemoY">'.$this->MemoY.'</xsl:variable>'."\n".
            '<xsl:variable name="BankAddressX" select="$x3"/>'."\n".
            '<xsl:variable name="BankAddressY" select="$y2"/>'."\n".
            '<xsl:variable name="BankCodeX" select="$x3"/>'."\n".
            '<xsl:variable name="BankCodeY" select="$y3"/>'."\n".
            '<xsl:variable name="CheckNumX" select="$x4"/>'."\n".
            '<xsl:variable name="signatureX">'.$this->signatureX.'</xsl:variable>'."\n".
            '<!-- MICR Dimenstions -->'."\n".
            '<xsl:variable name="micrCheckNumX">'.$this->micrCheckNumX.'</xsl:variable>'."\n".
            '<xsl:variable name="micrBranchIDX">'.$this->micrBranchIDX.'</xsl:variable>'."\n".
            '<xsl:variable name="micrRoutingNumX">'.$this->micrRoutingNumX.'</xsl:variable>'."\n".
            '<xsl:variable name="micrAcctNumRightX">'.$this->micrAcctNumRightX.'</xsl:variable>'."\n".
            '<xsl:variable name="micrAcctNumLeftX">'.$this->micrAcctNumLeftX.'</xsl:variable>'."\n".
            '<xsl:variable name="micrY">'.$this->micrY.'</xsl:variable>'."\n".
            '<!--There are definitions of payformanceOffsetY in every single template as we finally '."\n".
            'got the MICR right and affecting $top for the whole stub will move the MICR -->'."\n".
            '<!-- TOP definitions -->'."\n".
            '<!-- These define the TOP coordinates for the Three fo:block-containers'."\n".
            '	 1. Check Section on the Page'."\n".
            '	 2. Invoice Stub 1 (This prints the Line Item Details if the \'shodetails\' attribute is set to \'T\''."\n".
            '	 3. Invoice Stub 2 '."\n".
            '	 The T, M , B suffixes are for \'Check on Top\', \'Check on Middle\',\'Check on bottom (Online Check) cases.'."\n".
            '-->'."\n".
            '<xsl:variable name="checktop_T">'.$this->checktop_T.'</xsl:variable>'."\n".
            '<xsl:variable name="checktop_M">'.$this->checktop_M.'</xsl:variable>'."\n".
            '<xsl:variable name="checktop_B">'.$this->checktop_B.'</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub1_top_T">'.$this->invoicestub1_top_T.'</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub1_top_M">'.$this->invoicestub1_top_M.'</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub1_top_B">'.$this->invoicestub1_top_B.'</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub2_top_T">'.$this->invoicestub2_top_T.'</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub2_top_M">'.$this->invoicestub2_top_M.'</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub2_top_B">'.$this->invoicestub2_top_B.'</xsl:variable>'."\n".
            '<xsl:variable name="cadDateTag">'.$this->cadDateTag.'</xsl:variable>'."\n".
            '<xsl:variable name="checksperpage">'.$this->checksperpage.'</xsl:variable>'."\n".
            '<!-- End of TOP definitions -->'."\n".
            '<!--End   Global variables -->';
        return $global_variables;
    }

    /**
     * @return string
     */
    function GetTemplateRoot()
    {
        $template_root='<xsl:template match="/">'."\n".
            '<xsl:apply-templates select="ROOT"/>'."\n".
            '</xsl:template>'."\n".
            '<xsl:template match="ROOT">'."\n".
            '<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">'."\n".
            '<xsl:call-template name="pageMaster">'."\n".
            '<xsl:with-param name="margin" select="margin"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:apply-templates/>'."\n".
            '</fo:root>'."\n".
            '</xsl:template>'."\n";
        return $template_root;
    }

    /**
     * @return string
     */
    function GetTemplatePageMaster()
    {
        $template_page_master='<xsl:template name="pageMaster">'."\n".
            '<xsl:param name="margin"/>'."\n".
            '<!-- 		$margin/@left, $margin/@top-->'."\n".
            '<fo:layout-master-set>'."\n".
            '<fo:simple-page-master master-name="checkmaster" margin-right="0.1in" margin-left="{$margin/@left}in" margin-bottom="0.1in"'.' margin-top="{$margin/@top}in" page-width="{$pgwidth}in" page-height="{$pgheight}in">'."\n".
            '<fo:region-body overflow="auto" margin-top="0.1in" margin-bottom="0.1in"/>'."\n".
            '</fo:simple-page-master>'."\n".
            '</fo:layout-master-set>'."\n".
            '</xsl:template>';
        return $template_page_master;
    }

    /**
     * @return string
     */
    function GetTemplateCheck()
    {
        $template_check='<xsl:template match="check">'."\n".
            '<xsl:variable name="billsPerPage">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="(@showdetails = \'T\') or (@showdetailedvendstub = \'T\')">6</xsl:when>'."\n".
            '<xsl:otherwise>18</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="noofLines">'."\n".
            '18'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="noOfBills" select="count(invoicetable/invoice)"/>'."\n".
            '<xsl:variable name="noOfLineItems" select="count(invoicetable/invoice/LINEITEM)"/>'."\n".
            '<xsl:variable name="noOfPages">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="(@showdetails = \'T\') or (@showdetailedvendstub = \'T\')">'."\n".
            '<xsl:value-of select="ceiling( $noOfLineItems div $noofLines)"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="ceiling( $noOfBills div $noofLines)"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="lastPageIndex" select="$noOfPages"/>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="(@showdetails = \'T\') or (@showdetailedvendstub = \'T\')">'."\n".
            '<xsl:call-template name="printPageWithDetails">'."\n".
            '<xsl:with-param name="currPageNumber">1</xsl:with-param>'."\n".
            '<xsl:with-param name="startIndex">1</xsl:with-param>'."\n".
            '<xsl:with-param name="check" select="."/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:call-template name="printPage">'."\n".
            '<xsl:with-param name="currPageNumber">1</xsl:with-param>'."\n".
            '<xsl:with-param name="startIndex">1</xsl:with-param>'."\n".
            '<xsl:with-param name="check" select="."/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:template>';
        return $template_check;
    }

    /**
     * @return string
     */
    function GetTemplatePrintPage()
    {
        $template_PrintPage='<xsl:template name="printPage">'."\n".
            '<xsl:param name="currPageNumber"/>'."\n".
            '<xsl:param name="startIndex"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="lastPageIndex"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<!-- set the TOP co-ordinates for the three parts of the CHECK -->'."\n".
            '<xsl:variable name="checktop">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@format = \'T\')">'."\n".
            '<xsl:value-of select="$checktop_T"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:when test="($check/@format =\'M\')">'."\n".
            '<xsl:value-of select="$checktop_M"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$checktop_B"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub1_top">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@format = \'T\')">'."\n".
            '<xsl:value-of select="$invoicestub1_top_T"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:when test="($check/@format = \'M\')">'."\n".
            '<xsl:value-of select="$invoicestub1_top_M"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$invoicestub1_top_B"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub2_top">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@format = \'T\')">'."\n".
            '<xsl:value-of select="$invoicestub2_top_T"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:when test="($check/@format = \'M\')">'."\n".
            '<xsl:value-of select="$invoicestub2_top_M"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$invoicestub2_top_B"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="firstPage">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$currPageNumber= 1">Y</xsl:when>'."\n".
            '<xsl:otherwise>N</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="nextStartIndex" select="($noofLines * $currPageNumber) + 1"/>'."\n".
            '<!-- Printing One Line per Invoice here.'."\n".
            'Do not parse the LINEITEM Element of invoice '."\n".
            'Just traverse thru the invoices in the invoicetable to print the invoice stub '."\n".
            '-->'."\n".
            '<xsl:for-each select="$check/invoicetable/invoice">'."\n".
            '<xsl:variable name="printFirstOnly">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="( $check/bankinfo/@printon = \'P\' and $check/@nonnegotiable = \'\' and $currPageNumber &gt; 1)">0</xsl:when>'."\n".
            '<xsl:otherwise>1</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="currPos" select="position()"/>'."\n".
            '<xsl:if test="(($currPos &gt;  $startIndex) or ($currPos = $startIndex) and $printFirstOnly != 0 )">'."\n".
            '<xsl:if test="$currPos = ($nextStartIndex) ">'."\n".
            '<xsl:call-template name="printPage">'."\n".
            '<xsl:with-param name="currPageNumber" select="$currPageNumber + 1"/>'."\n".
            '<xsl:with-param name="startIndex" select="$currPos"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="($currPos =( ($currPageNumber -1) * $noofLines + 1))">'."\n".
            '<fo:page-sequence master-reference="checkmaster">'."\n".
            '<fo:flow flow-name="xsl-region-body">'."\n".
            '<xsl:call-template name="checkStub">'."\n".
            '<xsl:with-param name="firstPage" select="$firstPage"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$checktop"/>'."\n".
            '</xsl:call-template>'."\n";
        if($this->checksperpage != THREE_CHECKS){
         $template_PrintPage .=   '<xsl:call-template name="invoiceStub">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub1_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="invoiceStub">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub2_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n";
        }
          $template_PrintPage .=  '</fo:flow>'."\n".
            '</fo:page-sequence>'."\n".
            '</xsl:if>'."\n".
            '</xsl:if>'."\n".
            '</xsl:for-each>'."\n".
            '</xsl:template>';
        return $template_PrintPage;
    }

    /**
     * @return string
     */
    function GetTemplatePrintPageWithDetails()
    {
        $PrintPageWithDetails='<xsl:template name="printPageWithDetails">'."\n".
            '<xsl:param name="currPageNumber"/>'."\n".
            '<xsl:param name="startIndex"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="lastPageIndex"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<!-- set the TOP co-ordinates for the three parts of the CHECK -->'."\n".
            '<xsl:variable name="checktop">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@format = \'T\')">'."\n".
            '<xsl:value-of select="$checktop_T"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:when test="($check/@format = \'M\')">'."\n".
            '<xsl:value-of select="$checktop_M"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$checktop_B"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub1_top">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@format = \'T\')">'."\n".
            '<xsl:value-of select="$invoicestub1_top_T"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:when test="($check/@format = \'M\')">'."\n".
            '<xsl:value-of select="$invoicestub1_top_M"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$invoicestub1_top_B"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="invoicestub2_top">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@format = \'T\')">'."\n".
            '<xsl:value-of select="$invoicestub2_top_T"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:when test="($check/@format = \'M\')">'."\n".
            '<xsl:value-of select="$invoicestub2_top_M"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$invoicestub2_top_B"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="firstPage">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$currPageNumber= 1">Y</xsl:when>'."\n".
            '<xsl:otherwise>N</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="nextStartIndex" select="$noofLines * $currPageNumber + 1"/>'."\n".
            '<!-- Printing One Line per Line Item/Invoice here'."\n".
            'Parse the LINEITEM Element of each Invoice, and determine when to print the next page'."\n".
            '(when ROWINDEX on LINEITEM is same as the startIndex on the nextPage) '."\n".
            '-->'."\n".
            '<xsl:for-each select="$check/invoicetable/invoice">'."\n".
            '<!-- The below variable will ensure only the first page of pre-printed checks will be printed if the check has more number of stub details -->'."\n".
            '<xsl:variable name="printFirstOnly">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="( $check/bankinfo/@printon = \'P\' and $check/@nonnegotiable = \'\' and $currPageNumber &gt; 1)">0</xsl:when>'."\n".
            '<xsl:otherwise>1</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:for-each select="LINEITEM">'."\n".
            '<xsl:variable name="currPos" select="@ROWINDEX"/>'."\n".
            '<xsl:if test="(($currPos &gt;  $startIndex or $currPos = $startIndex) and $printFirstOnly != 0 )">'."\n".
            '<xsl:if test="$currPos = $nextStartIndex ">'."\n".
            '<xsl:call-template name="printPageWithDetails">'."\n".
            '<xsl:with-param name="currPageNumber" select="$currPageNumber + 1"/>'."\n".
            '<xsl:with-param name="startIndex" select="$currPos"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="($currPos =( ($currPageNumber -1) * $noofLines + 1))">'."\n".
            '<fo:page-sequence master-reference="checkmaster">'."\n".
            '<fo:flow flow-name="xsl-region-body">'."\n".
            '<xsl:call-template name="checkStub">'."\n".
            '<xsl:with-param name="firstPage" select="$firstPage"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$checktop"/>'."\n".
            '</xsl:call-template>'."\n";
            if($this->checksperpage != THREE_CHECKS){
            $PrintPageWithDetails  .= '<xsl:if test="$check/@showdetailedvendstub = \'F\' and $check/@showdetails = \'T\'">'."\n".
            '<xsl:call-template name="invoiceStubWithDetails">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub1_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="invoiceStub">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub2_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="$check/@showdetailedvendstub = \'T\' and $check/@showdetails = \'F\'">'."\n".
            '<xsl:call-template name="invoiceStub">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub1_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="invoiceStubWithDetails">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub2_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="$check/@showdetailedvendstub = \'T\' and $check/@showdetails = \'T\'">'."\n".
            '<xsl:call-template name="invoiceStubWithDetails">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub1_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="invoiceStubWithDetails">'."\n".
            '<xsl:with-param name="currPage" select="$currPageNumber"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="top" select="$invoicestub2_top"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n";
        }
       $PrintPageWithDetails  .=   '</fo:flow>'."\n".
            '</fo:page-sequence>'."\n".
            '</xsl:if>'."\n".
            '</xsl:if>'."\n".
            '</xsl:for-each>'."\n".
            '</xsl:for-each>'."\n".
            '</xsl:template>';
        return $PrintPageWithDetails;
    }

    /**
     * @return string
     */
    function GetTemplateCheckStub()
    {
        $checkStub='<xsl:template name="checkStub">'."\n".
            '<xsl:param name="firstPage"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="top"/>'."\n".
            '<xsl:variable name="bgImage">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@voidtestcheck != \'\'"><xsl:value-of select="$check/@voidtestcheck"/></xsl:when>'."\n".
            '<xsl:otherwise><xsl:value-of select="$check/@filecopyimage"/></xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<fo:block-container background-repeat="no-repeat" height="3.5in" width="8.5in" top="{$top}"'.' left="0.0in" position="absolute">'."\n".
            '<xsl:if test="($bgImage != \'\')">'."\n".
            '<xsl:attribute name="background-image"><xsl:value-of select="$bgImage"/></xsl:attribute>'."\n".
            '</xsl:if>'."\n".
            '<!-- If printing the first page, Print all the details on the Check section'."\n".
            'Otherwise Print ***VOID*** and Company/Vendor Information on the Check section'."\n".
            '-->'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$firstPage = \'Y\'">'."\n".
            '<xsl:call-template name="checkStub_firstPage">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:call-template name="checkStub_restPages">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return $checkStub;
    }

    /**
     * @return string
     */
    function GetTemplateCheckStub_firstPage()
    {
        $CheckStub_firstPage='<xsl:template name="checkStub_firstPage">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="top"/>'."\n".
            '<fo:block-container height="3.5in" width="8.5in" top="{$top}" left="0.0in" position="absolute">'."\n".
            '<xsl:if test="$check/@notprintedcheck != \'T\'">'."\n".
            '<xsl:call-template name="printCompanyInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printAmountInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printPayToInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printMemoInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printMICRLine">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printCheckNumInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printSignature">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printBankInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="$check/@notprintedcheck = \'T\'">'."\n".
            '<xsl:call-template name="nonPrintedCheckTable">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return $CheckStub_firstPage;
    }

    /**
     * @return string
     */
    function GetTemplateCheckStub_restPages()
    {
        $checkStub_restPages='<xsl:template name="checkStub_restPages">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="top"/>'."\n".
            '<fo:block-container height="3.5in" width="8.5in" top="{$top}" left="0.0in" position="absolute">'."\n".
            '<xsl:call-template name="printCompanyInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:call-template name="printPayToInfo">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '<fo:block-container height="0.7in" width="5.5in" top="1.0in" left="{$x2}" position="absolute">'."\n".
            '<fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="16pt">'."\n".
            '*VOID*'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="right" line-height="11pt" font-family="Helvetica" font-size="8pt">'."\n".
            'This is not a check'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="right" line-height="11pt" font-family="Helvetica" font-size="8pt">'."\n".
            '*VOID*VOID*VOID'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return $checkStub_restPages;
    }

    /**
     * @return string
     */
    function GetTemplatePrintCompanyInfo()
    {
        $printCompanyInfo='<xsl:template name="printCompanyInfo">'."\n";
        $printCompanyInfo .='<xsl:param name="check"/>'."\n".
            '<xsl:variable name="payformanceOffsetY">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@format = \'B\'">0.00</xsl:when>'."\n".
            '<xsl:otherwise>	0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="payformanceOffsetX">'."\n".
            '0'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="leftPrePrinted">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/bankinfo/@printon = \'P\'">0.6</xsl:when>'."\n".
            '<xsl:otherwise>	0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".

            '<xsl:variable name="spaceForLogo">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@cnyLogoFile != \'\') and ($check/bankinfo/@printon = \'P\') ">0.15</xsl:when>'."\n".
            '<xsl:when test="($check/@cnyLogoFile != \'\') and ($check/bankinfo/@printon != \'P\') ">0.75</xsl:when>'."\n".
            '<xsl:otherwise>0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".

            '<xsl:if test="$check/@cnyLogoFile != \'\' ">'."\n".
            '<fo:external-graphic src="{$check/@cnyLogoFile}">'."\n".
            '</fo:external-graphic>'."\n" .
            '</xsl:if>'."\n";

        if ($this->locale=='CA') {
            $printCompanyInfo .=  '<fo:block-container height="0.7in" width="3.0in" top="{$companyAddressY - $payformanceOffsetY}in" left="{$companyAddressX + $payformanceOffsetX + $spaceForLogo}in" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="13pt" font-family="Helvetica" font-size="13pt" font-weight="bold">'."\n";
        }else{
            $printCompanyInfo .=  '<fo:block-container height="0.7in" width="4.0in" top="{$companyAddressY - $payformanceOffsetY}in" left="{$leftPrePrinted + $companyAddressX + $payformanceOffsetX + $spaceForLogo}in" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="14pt">'."\n";
        }
        $printCompanyInfo .= '<xsl:value-of select="$check/firminfo/@name"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">'."\n".
            '<xsl:value-of select="$check/firminfo/@street1"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">'."\n".
            '<xsl:value-of select="$check/firminfo/@street2"/>'."\n".
            '</fo:block>'."\n";
        if ($this->isAddress3InChecksEnabled()) {
            $printCompanyInfo .= '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">' . "\n" .
            '<xsl:value-of select="$check/firminfo/@street3"/>' . "\n" .
            '</fo:block>' . "\n" ;
        }
        $printCompanyInfo .= '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">'."\n".
            '<xsl:value-of select="$check/firminfo/@cityStateZip"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">'."\n".
            '<xsl:value-of select="$check/firminfo/@phone"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n";
        $printCompanyInfo .='</xsl:template>';
        return $printCompanyInfo;
    }

    /**
     * @return string
     */
    function GetTemplatePrintPayToInfo()
    {
        $printPayToInfo='<xsl:template name="printPayToInfo">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:variable name="payformanceOffsetY">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@format = \'B\'">0.15</xsl:when>'."\n".
            '<xsl:when test="$check/@printformat = \'H\'">0.19</xsl:when>'."\n".
            '<xsl:when test="($check/bankinfo/@printon = \'P\' and $checksperpage = \'3\')">0.19</xsl:when>'."\n".
            '<xsl:otherwise>'. $this->ptInfoSCOffset .'</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".

            '<fo:block-container height="1.3in" width="4.0in" top="{1.76 - $payformanceOffsetY}in" left="{$vendorAddressX}" position="absolute">'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@street1"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@street2"/>'."\n".
            '</fo:block>'."\n";
            if ($this->isAddress3InChecksEnabled()) {
                $printPayToInfo .= '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
                '<xsl:value-of select="$check/paytoinfo/@street3"/>'."\n".
                '</fo:block>'."\n";
            }
            $printPayToInfo .= '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@cityStateZip"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@country"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@phone"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return $printPayToInfo;
    }

    /**
     * @return string
     */
    function GetTemplatePrintAmountInfo()
    {

        $printAmountInfo ='<xsl:template name="last-index-of">'.
            '<xsl:param name="txt"/>'.
            '<xsl:param name="remainder" select="$txt"/>'.
            '<xsl:param name="delimiter" select="\' \'"/>'.
            '<xsl:choose>'.
            '<xsl:when test="contains($remainder, $delimiter)">'.
            '<xsl:call-template name="last-index-of">'.
            '<xsl:with-param name="txt" select="$txt"/>'.
            '<xsl:with-param name="remainder" select="substring-after($remainder, $delimiter)"/>'.
            '<xsl:with-param name="delimiter" select="$delimiter"/>'.
            '</xsl:call-template>'.
            '</xsl:when>'.
            '<xsl:otherwise>'.
            '<xsl:variable name="lastIndex" select="string-length(substring($txt, 1, string-length($txt)-string-length($remainder)))+1"/>'.
            '<xsl:choose>'.
            '<xsl:when test="string-length($remainder)=0">'.
            '<xsl:value-of select="string-length($txt)"/>'.
            '</xsl:when>'.
            '<xsl:when test="$lastIndex>0">'.
            '<xsl:value-of select="($lastIndex - string-length($delimiter))"/>'.
            '</xsl:when>'.
            '<xsl:otherwise>'.
            '<xsl:value-of select="0"/>'.
            '</xsl:otherwise>'.
            '</xsl:choose>'.
            '</xsl:otherwise>'.
            '</xsl:choose>'.
            '</xsl:template>';

        $printAmountInfo .=
            '<xsl:template name="printAmountInfo">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<!-- payformance requires an offset as their printing options seem to be different -->'."\n";

        if ($this->locale == 'CA') {
            $printAmountInfo .= '<xsl:variable name="payformanceOffsetY">'."\n".
                '<xsl:choose>'."\n".
                '<xsl:when test="$check/@format = \'B\'">0.1</xsl:when>'."\n".
                '<xsl:otherwise>0</xsl:otherwise>'."\n".
                '</xsl:choose>'."\n".
                '</xsl:variable>'."\n";
        } else {
            $printAmountInfo .= '<xsl:variable name="payformanceOffsetY">'."\n".
                '<xsl:choose>'."\n".
                '<xsl:when test="$check/@format = \'B\'">0.25</xsl:when>'."\n".
                '<xsl:otherwise>0</xsl:otherwise>'."\n".
                '</xsl:choose>'."\n".
                '</xsl:variable>'."\n";
            $printAmountInfo .= '<xsl:variable name="leftForCurrency">'."\n".
                '<xsl:choose>'."\n".
                '<xsl:when test="$check/bankinfo/@printon = \'P\'">0.2 in</xsl:when>'."\n".
                '<xsl:when test="$check/@format = \'B\'">0.1</xsl:when>'."\n".
                '<xsl:otherwise>0.0 in</xsl:otherwise>'."\n".
                '</xsl:choose>'."\n".
                '</xsl:variable>'."\n";
        }

        if ($this->locale=='MX') {
            $printAmountInfo .=
                '<fo:block-container height="0.7in" width="2.5in" top="{0.937 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">Paguese Por</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<fo:block-container height="0.7in" width="2.5in" top="{1.037 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" color="blue">Este Cheque A la orden de</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<xsl:choose>'."\n".
                '<xsl:when test="string-length($check/paytoinfo/@printas) > 60">'."\n".
                '<xsl:variable name="lineEnd">'.
                '<xsl:call-template name="last-index-of">'.
                '<xsl:with-param name="txt" select="title"/>'.
                '<xsl:with-param name="delimiter" select="\' \'">'.
                '</xsl:with-param>'.
                '</xsl:call-template>'.
                '</xsl:variable>'.
                '<fo:block-container height="0.4in" width="5.0in" top="{0.85 - $payformanceOffsetY}in" left="1.4in" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" hyphenate="true" word-break="keep-all" language="en">'."\n".
                '<xsl:value-of select="substring($check/paytoinfo/@printas, 1, $lineEnd)"/>'."\n".
                '</fo:block>'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" hyphenate="true" word-break="keep-all" language="en">'."\n".
                '<xsl:value-of select="substring($check/paytoinfo/@printas, $lineEnd+1)"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '</xsl:when>'."\n".
                '<xsl:otherwise>'."\n".
                '<fo:block-container height="0.2in" width="5.0in" top="{1.00 - $payformanceOffsetY}in" left="1.4in" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="11pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                '<xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '</xsl:otherwise>'."\n".
                '</xsl:choose>'."\n";
        } elseif ($this->locale == 'CA') {
            $printAmountInfo .=
                '<fo:block-container height="0.7in" width="2.5in" top="{0.877 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">Pay To</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<fo:block-container height="0.7in" width="2.5in" top="{0.977 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">The Order Of</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<xsl:choose>'."\n".
                '<xsl:when test="string-length($check/paytoinfo/@printas) > 60">'."\n".
                '<xsl:variable name="lineEnd">'.
                '<xsl:call-template name="last-index-of">'.
                '<xsl:with-param name="txt" select="title"/>'.
                '<xsl:with-param name="delimiter" select="\' \'">'.
                '</xsl:with-param>'.
                '</xsl:call-template>'.
                '</xsl:variable>'.
                '<fo:block-container height="0.4in" width="5.0in" top="{0.80 - $payformanceOffsetY}in" left="{$x2}" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                '<xsl:value-of select="substring($check/paytoinfo/@printas, 1, $lineEnd)"/>'."\n".
                '</fo:block>'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                '<xsl:value-of select="substring($check/paytoinfo/@printas, $lineEnd+1)"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '</xsl:when>'."\n".
                '<xsl:otherwise>'."\n".
                '<fo:block-container height="0.2in" width="5.0in" top="{0.94 - $payformanceOffsetY}in" left="{$x2}" position="absolute">'."\n".
                '<fo:block text-align="start" font-family="Helvetica" font-size="11pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                '<xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '</xsl:otherwise>'."\n".
                '</xsl:choose>'."\n";
        } else {
            if ($this->printon == 'B') {
                if($this->printasOverflow) {
                    $printAmountInfo .=
                        '<fo:block-container height="0.7in" width="2.5in" top="{0.937}in" left="{$PayToX}in" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">Pay To</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '<fo:block-container height="0.7in" width="2.5in" top="{1.037}in" left="{$PayToX}in" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">The Order Of</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '<xsl:choose>'."\n".
                        '<xsl:when test="string-length($check/paytoinfo/@printas) > 60">'."\n".
                        '<xsl:variable name="lineEnd">'.
                        '<xsl:call-template name="last-index-of">'.
                        '<xsl:with-param name="txt" select="title"/>'.
                        '<xsl:with-param name="delimiter" select="\' \'">'.
                        '</xsl:with-param>'.
                        '</xsl:call-template>'.
                        '</xsl:variable>'.
                        '<fo:block-container height="0.4in" width="5.33in" top="{0.85}in" left="{$x2}" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="'.$this->fontSizePayTo.'pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                        '<xsl:value-of select="substring($check/paytoinfo/@printas, 1, $lineEnd)"/>'."\n".
                        '</fo:block>'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="'.$this->fontSizePayTo.'pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                        '<xsl:value-of select="substring($check/paytoinfo/@printas, $lineEnd+1)"/>'."\n".
                        '</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '</xsl:when>'."\n".
                        '<xsl:otherwise>'."\n".
                        '<fo:block-container height="0.2in" width="5.0in" top="{1.00}in" left="{$x2}" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="'.$this->fontSizePayTo.'pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                        '<xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                        '</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '</xsl:otherwise>'."\n".
                        '</xsl:choose>'."\n";
                }else{
                    $printAmountInfo .=
                        '<fo:block-container height="0.7in" width="2.5in" top="{0.937}in" left="{$PayToX}in" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">Pay To</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '<fo:block-container height="0.7in" width="2.5in" top="{1.037}in" left="{$PayToX}in" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">The Order Of</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '<xsl:choose>'."\n".
                        '<xsl:when test="string-length($check/paytoinfo/@printas) > 60">'."\n".
                        '<xsl:variable name="lineEnd">'.
                        '<xsl:call-template name="last-index-of">'.
                        '<xsl:with-param name="txt" select="title"/>'.
                        '<xsl:with-param name="delimiter" select="\' \'">'.
                        '</xsl:with-param>'.
                        '</xsl:call-template>'.
                        '</xsl:variable>'.
                        '<fo:block-container height="0.4in" width="5.0in" top="{0.85}in" left="{$x2}" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                        '<xsl:value-of select="substring($check/paytoinfo/@printas, 1, $lineEnd)"/>'."\n".
                        '</fo:block>'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" language="en">'."\n".
                        '<xsl:value-of select="substring($check/paytoinfo/@printas, $lineEnd+1)"/>'."\n".
                        '</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '</xsl:when>'."\n".
                        '<xsl:otherwise>'."\n".
                        '<fo:block-container height="0.2in" width="5.0in" top="{1.00}in" left="{$x2}" position="absolute">'."\n".
                        '<fo:block text-align="start" font-family="Helvetica" font-size="11pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                        '<xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                        '</fo:block>'."\n".
                        '</fo:block-container>'."\n".
                        '</xsl:otherwise>'."\n".
                        '</xsl:choose>'."\n";
                }
            } else {
                $printAmountInfo .=
                    '<xsl:choose>'."\n".
                    '<xsl:when test="string-length($check/paytoinfo/@printas) > 60">'."\n".
                    '<xsl:variable name="lineEnd">'.
                    '<xsl:call-template name="last-index-of">'.
                    '<xsl:with-param name="txt" select="title"/>'.
                    '<xsl:with-param name="delimiter" select="\' \'">'.
                    '</xsl:with-param>'.
                    '</xsl:call-template>'.
                    '</xsl:variable>'.
                    '<fo:block-container height="0.4in" width="5.0in" top="{0.73}in" left="{$x2}" position="absolute">'."\n".
                    '<fo:block text-align="start" font-family="Helvetica" font-size="9pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                    '<xsl:value-of select="substring($check/paytoinfo/@printas, 1, $lineEnd)"/>'."\n".
                    '</fo:block>'."\n".
                    '<fo:block text-align="start" font-family="Helvetica" font-size="9pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                    '<xsl:value-of select="substring($check/paytoinfo/@printas, $lineEnd+1)"/>'."\n".
                    '</fo:block>'."\n".
                    '</fo:block-container>'."\n".
                    '</xsl:when>'."\n".
                    '<xsl:otherwise>'."\n".
                    '<fo:block-container height="0.2in" width="5.0in" top="{0.85}in" left="{$x2}" position="absolute">'."\n".
                    '<fo:block text-align="start" font-family="Helvetica" font-size="11pt" font-weight="normal" hyphenate="true" language="en">'."\n".
                    '<xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                    '</fo:block>'."\n".
                    '</fo:block-container>'."\n".
                    '</xsl:otherwise>'."\n".
                    '</xsl:choose>'."\n";
            }
        }
        if ($this->printon == 'B') {
            $currSymbol = '<xsl:value-of select="$check/@currencySymbol"/>' ;
            $topForCurrency = "1.23";
            $leftForCurrency = "6.4";
        } else {
            $currSymbol = '';
            $topForCurrency = "0.86";
            $leftForCurrency = "6.6";
        }

        if ($this->locale == 'CA') {
            $printAmountInfo .=
                '<fo:block-container height="0.7in" width="5.5in" top="{1.15 }in" left="{$x2}" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">'."\n".
                '***<xsl:value-of select="$check/@amounttxt"/>***'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<fo:block-container height="0.8in" width="3.5in" top="{1.15 }in" left="{$CheckNumX}" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">'."\n".
                '<xsl:value-of select="$check/@currencySymbol"/>**<xsl:value-of select="$check/@amount"/>**'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        } else {
            $printAmountInfo .=
                '<fo:block-container height="0.7in" width="5.5in" top="{1.23 }in" left="{$x2}" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">'."\n".
                '***<xsl:value-of select="$check/@amounttxt"/>***'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<fo:block-container height="0.8in" width="3.5in" top="{'.$topForCurrency.' }in" left="{'.$leftForCurrency.'}in" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">'."\n".
                $currSymbol.'**<xsl:value-of select="$check/@amount"/>**'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        }
        if ($this->locale != 'MX' && $this->printon == 'B') {
            $printAmountInfo .=
                '<fo:block-container height="0.8in" width="3.5in" top="{1.15  + 0.25}in" left="{$CheckNumX}" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="8pt">'."\n".
                '<xsl:value-of select="$check/@USDText"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        }
        $printAmountInfo .= '</xsl:template>';

        return $printAmountInfo;
    }

    /**
     * @return string
     */
    function GetTemplatePrintBankInfo()
    {
        $printBankInfo='<xsl:template name="printBankInfo">'."\n";
        if($this->printon=='B') {
            $printBankInfo .='<xsl:param name="check"/>'."\n".
                '<xsl:variable name="payformanceOffsetY">'."\n".
                '<xsl:choose>'."\n".
                '<xsl:when test="$check/@format = \'B\'">0.15</xsl:when>'."\n";
            if ($this->isAddress3InChecksEnabled) {
                $printBankInfo .= '<xsl:otherwise>	0.07</xsl:otherwise>'."\n";
            } else {
                $printBankInfo .= '<xsl:otherwise>	0</xsl:otherwise>'."\n";
            }
            $printBankInfo .= '</xsl:choose>'."\n".
                '</xsl:variable>'."\n";
            if($this->locale=='CA') {
                $printBankInfo .= '<fo:block-container height="0.8in" width="3in" top="{$BankAddressY - $payformanceOffsetY}in" left="3.75in" position="absolute">'."\n";
            }else{
                $printBankInfo .= '<fo:block-container height="0.8in" width="3.5in" top="{$BankAddressY - $payformanceOffsetY}in" left="{$BankAddressX}" position="absolute">'."\n";
            }
            $printBankInfo .= '<fo:block text-align="start" line-height="10pt" font-family="Helvetica" font-size="9pt">'."\n".
                '<xsl:value-of select="$check/bankinfo/@name"/>'."\n".
                '</fo:block>'."\n".
                '<fo:block text-align="start" line-height="8pt" font-family="Helvetica" font-size="7pt">'."\n".
                '<xsl:value-of select="$check/bankinfo/@street1"/>'."\n".
                '</fo:block>'."\n".
                '<fo:block text-align="start" line-height="8pt" font-family="Helvetica" font-size="7pt">'."\n".
                '<xsl:value-of select="$check/bankinfo/@street2"/>'."\n".
                '</fo:block>'."\n";
                if ($this->isAddress3InChecksEnabled()){
                    $printBankInfo .= '<fo:block text-align="start" line-height="8pt" font-family="Helvetica" font-size="7pt">'."\n".
                    '<xsl:value-of select="$check/bankinfo/@street3"/>'."\n".
                    '</fo:block>'."\n";
                }

            $printBankInfo .= '<fo:block text-align="start" line-height="8pt" font-family="Helvetica" font-size="7pt">'."\n".
                '<xsl:value-of select="$check/bankinfo/@cityStateZip"/>'."\n".
                '</fo:block>'."\n".
                '<fo:block text-align="start" line-height="9pt" font-family="Helvetica" font-size="8pt">'."\n".
                '<xsl:value-of select="$check/@bankcode"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        }
        $printBankInfo .= '</xsl:template>';
        return $printBankInfo;
    }

    /**
     * @return string
     */
    function GetTemplatePrintSignature()
    {

        $printSignature='<xsl:template name="printSignature">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:if test="$check/@nonnegotiable = \'T\'">'."\n".
            '<xsl:variable name="width" select="3.0"/>'."\n".
            '<fo:block-container height="1.0in" width="{$width}in" top="'.($this->signatureTop-0.4).'in" left="{ $pgwidth - $width }in" position="absolute">'."\n".
            '<fo:block font-family="TimesNewRoman" font-size="18pt" font-weight="bold" text-align="left">'."\n".
            '<xsl:text>Non-negotiable</xsl:text>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="($check/@twosignatures = \'T\' and $check/@demo != \'T\' and $check/@hidesigninfo != \'T\')">'."\n".
            '<xsl:if test="($check/bankinfo/@usesecondsignature = \'T\')">'."\n".
            '<fo:block-container height="0.42in" width="3.0in" top="'.($this->signatureTop-0.64).'in" left="{$signatureX}" position="absolute">'."\n".
            '<fo:table>'."\n".
            '<fo:table-column column-width="3.0in"/>'."\n".
            '<fo:table-body>'."\n".
            '<fo:table-row>'."\n".
            '<fo:table-cell>'."\n".
            '<fo:block text-align="center">'."\n".
            '<xsl:if test="($check/@signature2 != \'\')">'."\n".
            '<fo:external-graphic src="{$check/@signature2}">'."\n".
            '<xsl:if test="$check/@sigWidth2 != \'\' and $check/@sigHeight2 != \'\'">'."\n".
            '<xsl:attribute name="width">'."\n".
            '<xsl:value-of select="$check/@sigWidth2"/>'."\n".
            '</xsl:attribute>'."\n".
            '<xsl:attribute name="height">'."\n".
            '<xsl:value-of select="$check/@sigHeight2"/>'."\n".
            '</xsl:attribute>'."\n".
            '</xsl:if>'."\n".
            '</fo:external-graphic>'."\n".
            '</xsl:if>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-body>'."\n".
            '</fo:table>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:if>'."\n".
            '<fo:block-container height="0.2in" width="3.0in" top="1.73in" left="{$signatureX}" position="absolute">'."\n".
            '<fo:block>'."\n";
            if( !($this->checksperpage == THREE_CHECKS && $this->printon == 'P') ){
                $printSignature .='<xsl:if test="($check/bankinfo/@usesecondsignature = \'T\' and $check/@printformat != \'H\')">'."\n".
                '<fo:leader leader-pattern="rule" rule-thickness="0.004in"/>'."\n".
                '</xsl:if>'."\n";
            }
            $printSignature .=  '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="$check/@hidesigninfo != \'T\'">'."\n".
            '<fo:block-container height="0.42in" width="3.0in" top="'.($this->signatureTop-0.22).'in" left="{$signatureX}" position="absolute">'."\n".
            '<fo:table>'."\n".
            '<fo:table-column column-width="3.0in"/>'."\n".
            '<fo:table-body>'."\n".
            '<fo:table-row>'."\n".
            '<fo:table-cell>'."\n".
            '<fo:block text-align="center">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@demo != \'T\'">'."\n".
            '<xsl:if test="($check/@signature1 != \'\')">'."\n".
            '<fo:external-graphic src="{$check/@signature1}">'."\n".
            '<xsl:if test="$check/@sigWidth1 != \'\' and $check/@sigHeight1 != \'\'">'."\n".
            '<xsl:attribute name="width">'."\n".
            '<xsl:value-of select="$check/@sigWidth1"/>'."\n".
            '</xsl:attribute>'."\n".
            '<xsl:attribute name="height">'."\n".
            '<xsl:value-of select="$check/@sigHeight1"/>'."\n".
            '</xsl:attribute>'."\n".
            '</xsl:if>'."\n".
            '</fo:external-graphic>'."\n".
            '</xsl:if>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:text>NOT-NEGOTIABLE</xsl:text>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-body>'."\n".
            '</fo:table>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="0.2in" width="3.0in" top="'.$this->signatureTop.'in" left="{$signatureX}" position="absolute">'."\n".
            '<fo:block>'."\n";
            if( !($this->checksperpage == THREE_CHECKS && $this->printon == 'P') ){
              $printSignature .= '<xsl:if test="($check/@printformat != \'H\')">'."\n".
                    '<fo:leader leader-pattern="rule" rule-thickness="0.004in"/>'."\n".
                '</xsl:if>'."\n";
            }
            $printSignature .=      '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:if>'."\n".
            '<fo:block-container height="0.2in" width="3.0in" top="2.32in" left="{$signatureX}" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="9pt" text-align="center">'."\n".
            '<xsl:value-of select="$check/@voidtext"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';

        return $printSignature;
    }

    /**
     * @return string
     */
    function GetTemplatePrintCheckNumInfo()
    {
        $printCheckNumInfo='<xsl:template name="printCheckNumInfo">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:variable name="payformanceOffsetY">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@format = \'B\'">0.15</xsl:when>'."\n".
            '<xsl:otherwise>	0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n";
        if ($this->printon=='B') {
            if($this->locale=='CA') {
                $printCheckNumInfo .= '<fo:block-container height="0.8in" width="3.5in" top="{0.0 - $payformanceOffsetY}in" left="6.3in" position="absolute">'."\n";
            }else{
                $printCheckNumInfo .= '<fo:block-container height="0.8in" width="3.5in" top="{0.13 - $payformanceOffsetY}in" left="{$CheckNumX}" position="absolute">'."\n";
            }
            $printCheckNumInfo .=  '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="15pt">'."\n".
                '<xsl:value-of select="$check/@checknum"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        }


        if ($this->locale=='CA') {
            $printCheckNumInfo .= '<fo:block-container height="0.9in" width="3.5in" top="{0.33 - $payformanceOffsetY}in" left="5.8in" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="6pt">DATE</fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<fo:block-container height="0.8in" width="3.5in" top="{0.33 - $payformanceOffsetY}in" left="6.2in" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="12pt" font-weight="bold" letter-spacing="1.1pt"><xsl:value-of select="$check/@date"/></fo:block>'."\n".
                '</fo:block-container>'."\n".
                '<fo:block-container height="0.8in" width="3.5in" top="{0.33 - $payformanceOffsetY + 0.13}in" left="6.219in" position="absolute" >'."\n".
                '<fo:block text-align="justify" line-height="11pt" font-family="Helvetica" font-size="6pt" letter-spacing="7.9pt"><xsl:value-of select="$cadDateTag"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        }else{
            $printCheckNumInfo .= '<fo:block-container height="0.8in" width="3.5in" top="{0.33 - $payformanceOffsetY}in" left="{$CheckNumX}" position="absolute">'."\n".
                '<fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">'."\n";
            if($this->locale=='MX') {
                $printCheckNumInfo .= 'Fecha: <xsl:value-of select="$check/@date"/>'."\n";
            }else{
                $dateText = ($this->printon=='B') ? 'Date: ' : '&#160;&#160;&#160;&#160;&#160;&#160;';
                $printCheckNumInfo .= $dateText.'<xsl:value-of select="$check/@date"/>'."\n";
            }
            $printCheckNumInfo .= '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        }
        $printCheckNumInfo .= '</xsl:template>';
        return $printCheckNumInfo;
    }

    /**
     * @return string
     */
    function GetTemplatePrintMemoInfo()
    {
        $printMemoInfo='<xsl:template name="printMemoInfo">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:variable name="payformanceOffsetY">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@format = \'B\'">0.15</xsl:when>'."\n".
            '<xsl:otherwise>	0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".

        '<xsl:variable name="preprintedMemo">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@printformat = \'H\'">2.238</xsl:when>'."\n".
            '<xsl:when test="($check/bankinfo/@printon = \'P\' and $checksperpage = \'3\')">2.238</xsl:when>'."\n".
            '<xsl:otherwise>'.$this->ppMemoSCOffset.'</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n";
        if ($this->printon =='B') {
            $memo = 'Memo: ';
            $printMemoInfo .='<fo:block-container height="0.8in" width="5.5in" top="{$MemoY}in" left="{$MemoX}" position="absolute">'."\n";
        } else {
            $memo = '';
            $printMemoInfo .='<fo:block-container height="1.3in" width="4.0in" top="{$preprintedMemo}in" left="0.25in" position="absolute">'."\n";
        }
        $printMemoInfo .= '<xsl:if test="$check/@memo != \'\'">'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="9pt" font-weight="bold">'."\n".
            $memo.'<xsl:value-of select="substring($check/@memo, 1, 50)"/>'."\n".
            '</fo:block>'."\n".
            '</xsl:if>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return $printMemoInfo;
    }

    /**
     * @return string
     */
    function GetTemplatePrintCADMICRLine()
    {
        $printMICRLine='<xsl:template name="printMICRLine">'."\n".
            '<!-- MICR SECTION -->'."\n".
            '<!--  Print the Check Number, Routing  Number and the Account Number in MICR fonts -->'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:variable name="micrAcctOffetX">'."\n".
            '<xsl:value-of select="(string-length($check/@accountnoMICR)+4) * 0.125"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="checknumOffsetX">'."\n".
            '<xsl:value-of select="string-length($check/@checkMICR) * 0.125"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="micrSpacePadding">'."\n".
            '<xsl:value-of select="$check/@MICRSpacePadding * 0.125"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="micrAcctNumX">'."\n".
            '<xsl:choose>'."\n".
            '<!-- its 8 digits including the 2 encapsulating chars -->'."\n".
            '<xsl:when test="$check/@bankAcctMICRAlign = \'left\'">'."\n".
            '<xsl:value-of select="$micrAcctNumLeftX + 0.25 + $micrSpacePadding"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$micrAcctNumRightX - $micrAcctOffetX - $micrSpacePadding"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<fo:block-container height="0.8in" width="2.0in" top="2.95in" left="{2.15 - $checknumOffsetX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt">'."\n".
            '<xsl:value-of select="$check/@checkMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<!-- routing no.-->'."\n".
            '<fo:block-container height="0.8in" width="1.4in" top="2.95in" left="2.275in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@routingnoMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<!-- account no.-->'."\n".
            '<fo:block-container height="0.8in" width="2.5in" top="2.95in" left="{$micrAcctNumX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@accountnoMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="0.8in" width="2.5in" top="2.95in" left="5.675in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@MICRTransCode"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<!-- END MICR SECTION -->'."\n".
            '</xsl:template>';
        return    $printMICRLine;
    }

    /**
     * @return string
     */
    function GetTemplatePrintMICRLine()
    {
        $printMICRLine='<xsl:template name="printMICRLine">'."\n".
            '<!-- MICR SECTION -->'."\n".
            '<!--  Print the Check Number, Routing  Number and the Account Number in MICR fonts -->'."\n".
            '<!-- payformance requires an offset as their printing options seem to be different -->'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:variable name="micrAcctOffetX">'."\n".
            '<xsl:value-of select="(string-length($check/@accountnoMICR)+2) * 0.125"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="checknumOffsetX">'."\n".
            '<xsl:value-of select="string-length($check/@checkMICR) * 0.125"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="micrSpacePadding">'."\n".
            '<xsl:value-of select="$check/@MICRSpacePadding * 0.125"/>'."\n".
            '</xsl:variable>'."\n".

            '<xsl:variable name="micrONUSMovFor31">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@ONUS = \'true\') and ($check/@ONUS32 = \'Position 31\')">'."\n".
            '<xsl:value-of select="2 * 0.125"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="0"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".

            '<xsl:variable name="micrONUSMovFor32">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@ONUS = \'true\') and ($check/@ONUS32 = \'Position 32\')">'."\n".
            '<xsl:value-of select="1 * 0.125"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="0"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".

            '<xsl:variable name="micrONUSSymPos">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($check/@ONUS32 = \'Position 31\')">'."\n".
            '<xsl:value-of select="3.665"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="3.54"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".

            '<xsl:variable name="micrAcctNumX">'."\n".
            '<xsl:choose>'."\n".
            '<!-- its 10 digits including the 2 encapsulating chars -->'."\n".
            '<xsl:when test="$check/@bankAcctMICRAlign = \'left\'">'."\n".
            '<xsl:value-of select="$micrAcctNumLeftX + $micrSpacePadding + $micrONUSMovFor31 + $micrONUSMovFor32 "/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="($micrAcctNumRightX - $micrAcctOffetX - $micrSpacePadding) + ($micrONUSMovFor31 + $micrONUSMovFor32)"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<fo:block-container height="0.8in" width="2.0in" top="{$micrY}in" left="{$micrCheckNumX - $checknumOffsetX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt">'."\n".
            '<xsl:value-of select="$check/@checkMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<!-- routing no.-->'."\n".
            '<fo:block-container height="0.8in" width="1.4in" top="{$micrY}in" left="{$micrRoutingNumX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@routingnoMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<!-- account no.-->'."\n".
            '<fo:block-container height="0.8in" width="2.5in" top="{$micrY}in" left="{$micrAcctNumX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@accountnoMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="0.8in" width="2.5in" top="{$micrY}in" left="5.675in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" whitespace-collapse="false">'."\n".
            '<xsl:value-of select="$check/@MICRTransCode"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<xsl:if test="$check/@ONUS = \'true\'">'."\n".
            '<fo:block-container height="0.8in" width="2.5in" top="{$micrY}in" left="{$micrONUSSymPos}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@ONUSSYM"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:if>'."\n".
            '<!-- END MICR SECTION -->'."\n".
            '</xsl:template>';
        return    $printMICRLine;
    }

    /**
     * @return string
     */
    function GetTemplatePrintMICRLinePrePrintedCheck()
    {
        $printMICRLine='<xsl:template name="printMICRLine">'."\n".
            '<!-- No MICR SECTION for pre-printed check stock -->'."\n".
            '</xsl:template>';
        return    $printMICRLine;
    }


    /**
     * @return string
     */
    function GetTemplatePrintMXNMICRLine()
    {
        $printMXNMICRLine='<xsl:template name="printMICRLine">'."\n".
            '<!-- MICR SECTION -->'."\n".
            '<!--  Print the Branch ID, Routing Number, Account Number and Check Number. in MICR fonts -->'."\n".
            '<!-- payformance requires an offset as their printing options seem to be different -->'."\n".
            '<!-- END MICR SECTION -->'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:variable name="micrAcctOffetX">'."\n".
            '<xsl:value-of select="string-length($check/@accountnoMICR) * 0.125"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="checknumOffsetX">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@format = \'M\'"><xsl:value-of select="string-length($check/@checkMICR) * 0.125-0.08"/></xsl:when>'."\n".
            '<xsl:otherwise><xsl:value-of select="string-length($check/@checkMICR) * 0.125"/></xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="checkBranchIDOffsetX">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@format = \'M\'"><xsl:value-of select="string-length($check/@bankcodeMICR) * 0.125-0.08"/></xsl:when>'."\n".
            '<xsl:otherwise><xsl:value-of select="string-length($check/@bankcodeMICR) * 0.125"/></xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="micrSpacePadding">'."\n".
            '<xsl:value-of select="$check/@MICRSpacePadding * 0.125"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="micrAcctNumX">'."\n".
            '<xsl:choose>'."\n".
            '<!-- its 8 digits including the 2 encapsulating chars -->'."\n".
            '<xsl:when test="$check/@bankAcctMICRAlign = \'left\'">'."\n".
            '<xsl:value-of select="$micrAcctNumLeftX + $micrSpacePadding"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$micrAcctNumRightX - $micrAcctOffetX - $micrSpacePadding"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<!-- Branch ID.-->'."\n".
            '<fo:block-container height="0.8in" width="1.4in" top="{$micrY}in" left="{$micrBranchIDX - $checkBranchIDOffsetX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@bankcodeMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<!-- routing no.-->'."\n".
            '<fo:block-container height="0.8in" width="1.4in" top="{$micrY}in" left="{$micrRoutingNumX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@routingnoMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<!-- account no.-->'."\n".
            '<fo:block-container height="0.8in" width="2.5in" top="{$micrY}in" left="{$micrAcctNumX}in" position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@accountnoMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="0.8in" width="2.0in" top="{$micrY}in" left="{$micrAcctNumX + $micrAcctOffetX +0.2}in" '.'position="absolute">'."\n".
            '<fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt">'."\n".
            '<xsl:value-of select="$check/@checkMICR"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return    $printMXNMICRLine;

    }

    /**
     * @return string
     */
    function GetTemplateInvoiceStub()
    {
        $invoiceStub='<xsl:template name="invoiceStub">'."\n".
            '<xsl:param name="currPage"/>'."\n".
            '<xsl:param name="lastPageIndex"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="top"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<xsl:variable name="PrintAsText">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@locale=\'MX\'">'."\n".
            'Nombre de la Impresion :'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            'Print As:'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="InvoiceDateText">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@locale=\'MX\'">'."\n".
            'Fecha:'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            'Date:'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<fo:block-container height="4.25in" width="8.0in" top="{$top}" left="-0.3in" position="absolute">'."\n";
        if ($this->printon =='B') {
            $invoiceStub .=
                '<fo:block-container height="0.25in" width="8.0in" top="0.09in" left="-0.25in" position="absolute">'."\n".
                '<fo:block font-family="Helvetica" font-size="11pt" text-align="right">'."\n".
                '<xsl:value-of select="$check/@checknum"/>'."\n".
                '</fo:block>'."\n".
                '</fo:block-container>'."\n";
        }

        $stubTop = 0.15 + $this->topInvoiceStubOffset;
        $invoiceStub .='<fo:block-container height="0.5in" width="3.5in" top="'.$stubTop.'in" left="0.1in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="10pt" text-align="left">'."\n".
            '<xsl:value-of select="$check/firminfo/@name"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block font-family="Helvetica" font-size="9pt" text-align="left">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@id"/>--<xsl:value-of select="substring($check/paytoinfo/@name,0, 35)"/>'."\n".
            '</fo:block>'."\n";

        if ($this->locale=='MX') {
            $invoiceStub .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="left">'."\n".
                'Nombre de la Impresion: <xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                '</fo:block>'."\n";
        } else {
            $invoiceStub .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="left">'."\n".
                'Print As: <xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                '</fo:block>'."\n";
        }

        $stubTop22 = 0.22 + $this->topInvoiceStubOffset;
        $stubTopAddress = 0.15 + $this->topInvoiceStubOffset;
        if($this->isAddress3InChecksEnabled()){
            $stubTopAddress = 0.11 + $this->topInvoiceStubOffset;
        }

        $invoiceStub .= '</fo:block-container>'."\n".
            '<fo:block-container height="1.5in" width="3.5in" top="'.$stubTopAddress.'in" left="3.8in" position="absolute">'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@street1"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@street2"/>'."\n".
            '</fo:block>'."\n";
            if ($this->isAddress3InChecksEnabled()) {
                $invoiceStub .=  '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
                '<xsl:value-of select="$check/paytoinfo/@street3"/>'."\n".
                '</fo:block>'."\n";
            }
        $invoiceStub .= '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@cityStateZip"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="0.5in" width="2.5in" top="'.$stubTop22.'in" left="5.25in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">'."\n".
            '<xsl:value-of select="$check/bankinfo/@name"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block font-family="Helvetica" font-size="8pt" text-align="right" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@bankacctidno"/>'."\n".
            '</fo:block>'."\n";

        if ($this->locale=='MX') {
            $invoiceStub .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">'."\n".
                'Fecha: <xsl:value-of select="$check/@date"/>'."\n".
                '</fo:block>'."\n";
        } else {
            $invoiceStub .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">'."\n".
                'Date: <xsl:value-of select="$check/@date"/>'."\n".
                '</fo:block>'."\n";
        }
        $stubTop = 0.6 + $this->topInvoiceStubOffset;
        $invoiceStub .= '</fo:block-container>'."\n".
            '<fo:block-container height="2.7in" width="7.5in" top="'.$stubTop.'in" left="0.12in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="10pt" text-align="right">'."\n".
            '<xsl:variable name="lastPageForInvoice">'."\n".
            '<xsl:value-of select="ceiling( count($check/invoicetable/invoice) div $noofLines)"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:if test="$currPage &lt; $lastPageForInvoice or $currPage = $lastPageForInvoice">'."\n".
            '<fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">'."\n".
            '<xsl:call-template name="invoiceTable">'."\n".
            '<xsl:with-param name="currPage" select="$currPage"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</fo:table>'."\n".
            '</xsl:if>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="0.2in" width="2.7in" top="3.05in" left="5.0in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">'."\n".
            'Page <xsl:value-of select="$currPage"/>  of <xsl:value-of select="$lastPageIndex"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return $invoiceStub;
    }

    /**
     * @return string
     */
    function GetTemplateInvoiceStubWithDetails()
    {
        $invoiceStubWithDetails='<xsl:template name="invoiceStubWithDetails">'."\n".
            '<xsl:param name="currPage"/>'."\n".
            '<xsl:param name="lastPageIndex"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="top"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<xsl:variable name="DateText">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@locale=\'MX\'">'."\n".
            'Fecha:'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            'Date:'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="PrintAsText">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@locale=\'MX\'">'."\n".
            'Nombre de la Impresion :'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            'Print As:'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n";

        if($this->printasOverflow) {
            $invoiceStubWithDetails .= '<fo:block-container height="3.25in" width="8.0in" top="{$top}" left="-0.3in" position="absolute">'."\n";
            if ($this->printon =='B') {
                $invoiceStubWithDetails .=
                    '<fo:block-container height="0.25in" width="8.0in" top="0.09in" left="-0.25in" position="absolute">'."\n".
                    '<fo:block font-family="Helvetica" font-size="11pt" text-align="right">'."\n".
                    '<xsl:value-of select="$check/@checknum"/>'."\n".
                    '</fo:block>'."\n".
                    '</fo:block-container>'."\n";
            }
            $invoiceStubWithDetails .=
                '<fo:block-container height="0.5in" width="7in" top="0.15in" left="0.1in" position="absolute">'."\n".
                '<fo:block font-family="Helvetica" font-size="10pt" text-align="left">'."\n".
                '<xsl:value-of select="$check/firminfo/@name"/>'."\n".
                '</fo:block>'."\n".
                '<fo:block font-family="Helvetica" font-size="9pt" text-align="left">'."\n".
                '<xsl:value-of select="$check/paytoinfo/@id"/>--<xsl:value-of select="substring($check/paytoinfo/@name, 0, 35)"/>'."\n".
                '</fo:block>'."\n";
        }else{
            $invoiceStubWithDetails .= '<fo:block-container height="3.25in" width="8.0in" top="{$top}" left="-0.3in" position="absolute">'."\n";
            if ($this->printon =='B') {
                $invoiceStubWithDetails .=
                    '<fo:block-container height="0.25in" width="8.0in" top="0.09in" left="-0.25in" position="absolute">'."\n".
                    '<fo:block font-family="Helvetica" font-size="11pt" text-align="right">'."\n".
                    '<xsl:value-of select="$check/@checknum"/>'."\n".
                    '</fo:block>'."\n".
                    '</fo:block-container>'."\n";
            }
            $invoiceStubWithDetails .=
                '<fo:block-container height="0.5in" width="3.5in" top="0.15in" left="0.1in" position="absolute">'."\n".
                '<fo:block font-family="Helvetica" font-size="10pt" text-align="left">'."\n".
                '<xsl:value-of select="$check/firminfo/@name"/>'."\n".
                '</fo:block>'."\n".
                '<fo:block font-family="Helvetica" font-size="9pt" text-align="left">'."\n".
                '<xsl:value-of select="$check/paytoinfo/@id"/>--<xsl:value-of select="substring($check/paytoinfo/@name, 0, 35)"/>'."\n".
                '</fo:block>'."\n";
        }

        if ($this->locale=='MX') {
            $invoiceStubWithDetails .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="left">'."\n".
                'Nombre de la Impresion: <xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                '</fo:block>'."\n";
        } else {
            $invoiceStubWithDetails .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="left">'."\n".
                'Print As: <xsl:value-of select="$check/paytoinfo/@printas"/>'."\n".
                '</fo:block>'."\n";
        }

        $invoiceStubWithDetails .= '</fo:block-container>'."\n".
            '<fo:block-container height="1.5in" width="3.5in" top="0.11in" left="3.8in" position="absolute">'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@street1"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@street2"/>'."\n".
            '</fo:block>'."\n";
            if ($this->isAddress3InChecksEnabled()) {
                $invoiceStubWithDetails .= '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
                '<xsl:value-of select="$check/paytoinfo/@street3"/>'."\n".
                '</fo:block>'."\n";
            }

            $invoiceStubWithDetails .= '<fo:block text-align="start" font-family="Helvetica" font-size="8pt">'."\n".
            '<xsl:value-of select="$check/paytoinfo/@cityStateZip"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="1.0in" width="2.5in" top="0.22in" left="5.25in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">'."\n".
            '<xsl:value-of select="$check/bankinfo/@name"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block font-family="Helvetica" font-size="8pt" text-align="right" white-space-collapse="false">'."\n".
            '<xsl:value-of select="$check/@bankacctidno"/>'."\n".
            '</fo:block>'."\n";

        if ($this->locale=='MX') {
            $invoiceStubWithDetails .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">Fecha: <xsl:value-of select="$check/@date"/>'."\n".
                '</fo:block>'."\n";
        } else {
            $invoiceStubWithDetails .= '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">Date: <xsl:value-of select="$check/@date"/>'."\n".
                '</fo:block>'."\n";
        }

        $invoiceStubWithDetails .= '</fo:block-container>'."\n".
            '<fo:block-container height="2.7in" width="7.5in" top="0.6in" left="0.12in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="10pt" text-align="right">'."\n".
            '<fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">'."\n".
            '<xsl:call-template name="invoiceTableWithDetails">'."\n".
            '<xsl:with-param name="currPage" select="$currPage"/>'."\n".
            '<xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '</fo:table>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '<fo:block-container height="0.2in" width="2.7in" top="3.05in" left="5.0in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="8pt" text-align="right">'."\n".
            $this->PageText.' <xsl:value-of select="$currPage"/>  of <xsl:value-of select="$lastPageIndex"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';
        return  $invoiceStubWithDetails;
    }


    /**
     * @return string
     */
    function GetTemplateInvoiceTable()
    {
        $invoiceTable='<xsl:template name="invoiceTable">'."\n".
            '<xsl:param name="currPage"/>'."\n".
            '<xsl:param name="lastPageIndex"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<xsl:variable name="refnoColWidth">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@hideamtdue != \'T\'">1.75</xsl:when>'."\n".
            '<xsl:otherwise>2.6</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="addtorefnoColWidth">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displaytermdiscount = \'T\'">0</xsl:when>'."\n".
            '<xsl:otherwise>1.0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<fo:table-column column-width="0.6in"/>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($VarMultiCurrency = \'true\')  and ($check/@doingadvance = \'F\')">'."\n".
            '<fo:table-column column-width="2.3in"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<fo:table-column column-width="2.2in"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-column column-width="{$refnoColWidth + $addtorefnoColWidth}in"/>'."\n".
            '<xsl:if test="$check/@hideamtdue != \'T\'">'."\n".
            '<fo:table-column column-width="0.7in"/>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="$check/@displaytermdiscount = \'T\'">'."\n".
            '<fo:table-column column-width="1.0in"/>'."\n".
            '</xsl:if>'."\n".
            '<fo:table-column column-width="1.2in"/>'."\n".
            '<!-- Always print the Header on each page -->'."\n".
            '<fo:table-header font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="2pt">'."\n".
            '<fo:table-row background-color="#BBBBBB" line-height="2pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">'."\n".
            '<xsl:call-template name="printHeaders">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-header>'."\n".
            '<fo:table-body font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="4.7pt">'."\n".
            '<xsl:call-template name="printRows">'."\n".
            '<xsl:with-param name="currPage" select="$currPage"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '<!-- Print the Total Line only on the Last Page -->'."\n".
            '<xsl:variable name="billcount" select="count($check/invoicetable/invoice)"/>'."\n".
            '<xsl:variable name="printtotalline">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$currPage = $lastPageIndex or (($billcount &lt; $currPage* $noofLines ) or ($billcount = $currPage* $noofLines) )">'."\n".
            '<xsl:text>T</xsl:text>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:text>F</xsl:text>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="tableendcolspan">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@hideamtdue != \'T\'">5</xsl:when>'."\n".
            '<xsl:otherwise>4</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="addcolspan">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displaytermdiscount != \'T\'">1</xsl:when>'."\n".
            '<xsl:otherwise>0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$printtotalline = \'T\'">'."\n".
            '<xsl:call-template name="Totals">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="colspan" select="($tableendcolspan - $addcolspan)"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:call-template name="Contnd">'."\n".
            '<xsl:with-param name="colspan" select="($tableendcolspan - $addcolspan)"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</fo:table-body>'."\n".
            '</xsl:template>';
        return $invoiceTable;
    }

    /**
     * @return string
     */
    function GetTemplateNonPrintedCheckTable()
    {
        $nonPrintedCheckTable =

            '<xsl:template name="nonPrintedCheckTable">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:variable name="paytoinfo" select="$check/paytoinfo[last()]"/>'."\n".
            '<xsl:variable name="headers" select="$check/headers[last()]"/>'."\n".
            '<fo:block-container height="2.7in" width="7.5in" top="0.45in" left="0.12in" position="absolute">'."\n".
            '<fo:block font-family="Helvetica" font-size="10pt" text-align="right">'."\n".
            '<fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">'."\n".
            '<fo:table-column column-width="1.7in"/>'."\n".
            '<fo:table-column column-width="5.4in"/>'."\n".
            '<fo:table-header font-family="Helvetica" font-size="8pt" font-weight="bold">'."\n".
            '<fo:table-row background-color="#BBBBBB" line-height="6pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$headers/@COL1"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$headers/@COL2"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-header>'."\n".
            '<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">'."\n".
            '<fo:table-row>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$paytoinfo/@printas"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$paytoinfo/@street1"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$paytoinfo/@street2"/>'."\n".
            '</fo:block>'."\n";
            if ($this->isAddress3InChecksEnabled()) {
                $nonPrintedCheckTable .= '<fo:block text-align="start">'."\n".
                '<xsl:value-of select="$paytoinfo/@street3"/>'."\n".
                '</fo:block>'."\n";
            }
        $nonPrintedCheckTable .= '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$paytoinfo/@cityStateZip"/>'."\n".
            '</fo:block>'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$paytoinfo/@phone"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-body>'."\n".
            '</fo:table>'."\n".
            '<fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">'."\n".
            '<fo:table-column column-width="1.0in"/>'."\n".
            '<fo:table-column column-width="1.0in"/>'."\n".
            '<fo:table-column column-width="0.9in"/>'."\n".
            '<fo:table-column column-width="1.5in"/>'."\n".
            '<fo:table-column column-width="3.0in"/>'."\n".
            '<fo:table-header font-family="Helvetica" font-size="8pt" font-weight="bold">'."\n".
            '<fo:table-row background-color="#BBBBBB" line-height="6pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$headers/@COL3"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$headers/@COL4"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$headers/@COL5"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$headers/@COL6"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$headers/@COL7"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-header>'."\n".
            '<fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">'."\n".
            '<fo:table-row>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/@date"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/@paymenttype"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/@currencySymbol"/><xsl:text> </xsl:text><xsl:value-of select="$check/@amount"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/@checknum"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/@memo"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-body>'."\n".
            '</fo:table>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</xsl:template>';

        return $nonPrintedCheckTable;
    }

    /**
     * @return string
     */
    function GetTemplateInvoiceTableWithDetails()
    {
        $invoiceTableWithDetails='<xsl:template name="invoiceTableWithDetails">'."\n".
            '<xsl:param name="currPage"/>'."\n".
            '<xsl:param name="lastPageIndex"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<fo:table-column column-width="1.22in"/>'."\n".
            '<xsl:variable name="adjustlocationcollen">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displaytermdiscount = \'T\'">0</xsl:when>'."\n".
            '<xsl:otherwise>0.55</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($VarMultiCurrency = \'true\') and ($check/@doingadvance = \'F\')">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displocacctnocheck = \'T\'">'."\n".
            '<fo:table-column column-width="2.0in" />'."\n".
            '<fo:table-column column-width=".5in" />'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<fo:table-column column-width="2.5in" />'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-column column-width="1.0in"/>'."\n".
            '<fo:table-column column-width="{0.9 + $adjustlocationcollen}in"/>'."\n".
            '<fo:table-column column-width="0.15in"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displocacctnocheck = \'T\'">'."\n".
            '<fo:table-column column-width="2.0in" />'."\n".
            '<fo:table-column column-width=".65in" />'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<fo:table-column column-width="2.65in" />'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-column column-width="0.95in"/>'."\n".
            '<fo:table-column column-width="{0.9 + $adjustlocationcollen}in"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-column column-width="0.7in"/>'."\n".
            '<xsl:if test="$check/@displaytermdiscount = \'T\'">'."\n".
            '<fo:table-column column-width="0.55in"/>'."\n".
            '</xsl:if>'."\n".
            '<fo:table-column column-width="0.7in"/>'."\n".
            '<xsl:variable name="varColSpan">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$VarMultiCurrency = \'true\' and ($check/@doingadvance = \'F\')">7</xsl:when>'."\n".
            '<xsl:otherwise>6</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="addcolspan">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displaytermdiscount != \'T\'">1</xsl:when>'."\n".
            '<xsl:otherwise>0</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<!-- Always print the Header on each page -->'."\n".
            '<fo:table-header font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="6pt">'."\n".
            '<fo:table-row background-color="#BBBBBB" line-height="5pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">'."\n".
            '<xsl:call-template name="printDetailedHeadersLine1">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</fo:table-row>'."\n".
            '<fo:table-row background-color="#BBBBBB" line-height="5pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid" '.'font-style="italic">'."\n".
            '<xsl:call-template name="printDetailedHeadersLine2">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</fo:table-row>'."\n".
            '</fo:table-header>'."\n".
            '<fo:table-body font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="4.7pt">'."\n".
            '<xsl:call-template name="printRowsWithDetails">'."\n".
            '<xsl:with-param name="currPage" select="$currPage"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="billsPerPage" select="$billsPerPage"/>'."\n".
            '<xsl:with-param name="noofLines" select="$noofLines"/>'."\n".
            '</xsl:call-template>'."\n".
            '<!-- Print the Total Line only on the Last Page -->'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$currPage = $lastPageIndex">'."\n".
            '<xsl:call-template name="Totals">'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '<xsl:with-param name="colspan" select="($varColSpan - $addcolspan)"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:call-template name="Contnd">'."\n".
            '<xsl:with-param name="colspan" select="($varColSpan - $addcolspan)"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</fo:table-body>'."\n".
            '</xsl:template>';
        return $invoiceTableWithDetails;
    }

    /**
     * @return string
     */
    function GetTemplatePrintHeaders()
    {
        $printHeaders='<xsl:template name="printHeaders">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/headers/@COL1"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/headers/@COL2"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/headers/@COL6"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:if test="$check/@hideamtdue != \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/headers/@COL3"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="$check/@displaytermdiscount = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/headers/@COL4"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:if>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/headers/@COL5"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:template>';
        return $printHeaders;
    }

    /**
     * @return string
     */
    function GetTemplatePrintDetailedHeadersLine1()
    {
        $printDetailedHeadersLine1='<xsl:template name="printDetailedHeadersLine1">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL1"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL2"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displocacctnocheck = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL121"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:when>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL10"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n";
        if ($this->locale=='MX') {
            $printDetailedHeadersLine1 .= '<fo:table-cell padding="2pt">'."\n".
                '<fo:block text-align="start" font-style="italic">'."\n".
                '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL11"/>'."\n".
                '</fo:block>'."\n".
                '</fo:table-cell>'."\n".
                '<fo:table-cell padding="2pt">'."\n".
                '<fo:block text-align="start">'."\n".
                '</fo:block>'."\n".
                '</fo:table-cell>'."\n".
                '<fo:table-cell padding="2pt">'."\n".
                '<fo:block text-align="start">'."\n".
                '</fo:block>'."\n".
                '</fo:table-cell>'."\n".
                '<fo:table-cell padding="2pt">'."\n".
                '<fo:block text-align="start">'."\n".
                '</fo:block>'."\n".
                '</fo:table-cell>'."\n";
        }
        $printDetailedHeadersLine1 .= '</xsl:template>';
        return $printDetailedHeadersLine1;
    }

    /**
     * @return string
     */
    function GetTemplatePrintDetailedHeadersLine2()
    {
        $printDetailedHeadersLine2='<xsl:template name="printDetailedHeadersLine2">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL3"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL4"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displocacctnocheck = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block start-indent="-5mm" text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL12"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL5"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL6"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:if test="($VarMultiCurrency = \'true\') and ($check/@doingadvance = \'F\')">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL11"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:if>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL7"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:if test="$check/@displaytermdiscount = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL8"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:if>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/detailedheaders/@COL9"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:template>';
        return $printDetailedHeadersLine2;
    }


    /**
     * @return string
     */
    function GetTemplatePrintRows()
    {
        $printRows='<xsl:template name="printRows">'."\n".
            '<xsl:param name="currPage"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<xsl:variable name="startindex" select="(($currPage -1) * $noofLines) + 1"/>'."\n".
            '<xsl:variable name="endindex">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="count($check/invoicetable/invoice)  &lt; ($currPage * $noofLines)">'."\n".
            '<xsl:value-of select="count($check/invoicetable/invoice)"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$currPage * $noofLines"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<!--Call row Template for each of the row within the StartIndex and EndIndex -->'."\n".
            '<xsl:for-each select="$check/invoicetable/invoice">'."\n".
            '<xsl:variable name="position" select="position()"/>'."\n".
            '<xsl:if test="( $position &gt; $startindex or $position = $startindex) and ($position &lt; $endindex or $position = $endindex)">'."\n".
            '<xsl:call-template name="invoice">'."\n".
            '<xsl:with-param name="invoice" select="."/>'."\n".
            '<xsl:with-param name="hideamtdue" select="$check/@hideamtdue"/>'."\n".
            '<xsl:with-param name="displaytermdiscount" select="$check/@displaytermdiscount"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '</xsl:for-each>'."\n".
            '</xsl:template>';
        return $printRows;
    }

    /**
     * @return string
     */
    function GetTemplatePrintRowsWithDetails()
    {
        $printRowsWithDetails='<xsl:template name="printRowsWithDetails">'."\n".
            '<xsl:param name="currPage"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="billsPerPage"/>'."\n".
            '<xsl:param name="noofLines"/>'."\n".
            '<xsl:variable name="startindex" select="(($currPage -1) * $noofLines) + 1"/>'."\n".
            '<xsl:variable name="endindex">'."\n".
            '<xsl:value-of select="($currPage * $noofLines)"/>'."\n".
            '</xsl:variable>'."\n".
            '<!--Call row Template for each of the row within the StartIndex and EndIndex -->'."\n".
            '<xsl:for-each select="$check/invoicetable/invoice/LINEITEM">'."\n".
            '<xsl:variable name="position" select="@ROWINDEX"/>'."\n".
            '<xsl:if test="( $position &gt; $startindex or $position = $startindex ) and ($position &lt; $endindex or $position = $endindex)">'."\n".
            '<xsl:call-template name="invoiceWithDetails">'."\n".
            '<xsl:with-param name="invoice" select="ancestor::*"/>'."\n".
            '<xsl:with-param name="lineitem" select="."/>'."\n".
            '<xsl:with-param name="doingadvance" select="$check/@doingadvance"/>'."\n".
            '<xsl:with-param name="displaytermdiscount" select="$check/@displaytermdiscount"/>'."\n".
            '<xsl:with-param name="check" select="$check"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '</xsl:for-each>'."\n".
            '</xsl:template>';
        return $printRowsWithDetails;
    }


    /**
     * @return string
     */
    function GetTemplateInvoice()
    {
        $invoice='<xsl:template name="invoice">'."\n".
            '<xsl:param name="invoice"/>'."\n".
            '<xsl:param name="hideamtdue"/>'."\n".
            '<xsl:param name="displaytermdiscount"/>'."\n".
            /*Taking out the '...' format of Bill# and let the whole Bill#  is visible in Summary mode (bug 17891)
            '<xsl:variable name="desclen" select="string-length($invoice/@DESCRPTION)"/>'."\n".
            // TRUNCATE THE MIDDLE SECTION IF THE BILL# IS GOING TO BE TOO LONG (bug 17084)
            '<xsl:variable name="desc">'."\n".
                '<xsl:choose>'."\n".
                    '<xsl:when test="$invoice/@DESCRPTION and ($desclen > 21)">'."\n".
                    '<xsl:value-of select="concat(substring($invoice/@DESCRPTION, 1, 10), \'...\', substring($invoice/@DESCRPTION, $desclen - 9, 10))"/>'."\n".
                    '</xsl:when>'."\n".
                    '<xsl:otherwise><xsl:value-of select="$invoice/@DESCRPTION"/>'."\n".
                    '</xsl:otherwise>'."\n".
                '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".*/
            '<xsl:variable name="refnolen" select="string-length($invoice/@BILLREFNO)"/>'."\n".
            '<xsl:variable name="addtorefnolen">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$displaytermdiscount = \'T\'">0</xsl:when>'."\n".
            '<xsl:otherwise>7</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            // TRUNCATE THE MIDDLE SECTION IF THE BILL REF# IS GOING TO BE TOO LONG (bug 17084)
            '<xsl:variable name="refno">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$invoice/@BILLREFNO and ($refnolen > (21 + $addtorefnolen))">'."\n".
            '<xsl:value-of select="concat(substring($invoice/@BILLREFNO, 1, 10), \'...\', substring($invoice/@BILLREFNO, $refnolen - 9, 10))"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise><xsl:value-of select="$invoice/@BILLREFNO"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<fo:table-row>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$invoice/@DATE"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start" >'."\n".
            '<xsl:value-of select="$invoice/@DESCRPTION"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$refno"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:if test="$hideamtdue != \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$invoice/@AMOUNTDUE"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="$displaytermdiscount = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:if test="($invoice/@DISCOUNT) and not ($invoice/@CREDIT)">'."\n".
            '<xsl:value-of select="$invoice/@DISCOUNT"/>'."\n".
            '</xsl:if>'."\n".
            '<xsl:if test="($invoice/@CREDIT) and not ($invoice/@DISCOUNT)">'."\n".
            '<xsl:value-of select="$invoice/@CREDIT"/>'."\n".
            '</xsl:if>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:if>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$invoice/@AMOUNTPAID"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</xsl:template>';

        return $invoice;
    }

    /**
     * @return string
     */
    function GetTemplateInvoiceWithDetails()
    {
        $invoiceWithDetails='<xsl:template name="invoiceWithDetails">'."\n".
            '<xsl:param name="invoice"/>'."\n".
            '<xsl:param name="lineitem"/>'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="doingadvance"/>'."\n".
            '<xsl:param name="displaytermdiscount"/>'."\n".
            '<xsl:variable name="lastLineItemIndex">'."\n".
            '<xsl:value-of select="count($invoice/LINEITEM)"/>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="refnolen" select="string-length($invoice/@BILLREFNO)"/>'."\n".
            '<xsl:variable name="memolen" select="string-length($lineitem/@LINEITEMDESCR)"/>'."\n".
            '<xsl:variable name="deptlen" select="string-length($lineitem/@DEPT)"/>'."\n".
            '<xsl:variable name="loclen" select="string-length($lineitem/@LOCATION)"/>'."\n".
            '<xsl:variable name="loclenseperator">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($VarMultiCurrency = \'true\')">10</xsl:when>'."\n".
            '<xsl:otherwise>18</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="frbklen">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="($VarMultiCurrency = \'true\' and $displaytermdiscount = \'T\')">4</xsl:when>'."\n".
            '<xsl:otherwise>8</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="addtorefnolen">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$displaytermdiscount = \'T\'">0</xsl:when>'."\n".
            '<xsl:otherwise>10</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="refno">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$invoice/@BILLREFNO and ($refnolen > (23 + $addtorefnolen))">'."\n".
            '<xsl:value-of select="concat(substring($invoice/@BILLREFNO, 1, 10), \'...\', substring($invoice/@BILLREFNO, $refnolen - 9, 10))"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise><xsl:value-of select="$invoice/@BILLREFNO"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="memo">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$lineitem/@LINEITEMDESCR and ($memolen > 45)">'."\n".
            '<xsl:value-of select="concat(substring($lineitem/@LINEITEMDESCR, 1, 21), \'...\', substring($lineitem/@LINEITEMDESCR, $memolen - 20, 21))"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise><xsl:value-of select="$lineitem/@LINEITEMDESCR"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="dept">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$lineitem/@DEPT and ($deptlen > 15)">'."\n".
            '<xsl:value-of select="concat(substring($lineitem/@DEPT, 1, 6), \'...\', substring($lineitem/@DEPT, $deptlen - 5, 6))"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise><xsl:value-of select="$lineitem/@DEPT"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<xsl:variable name="location">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$lineitem/@LOCATION and ($loclen > ($loclenseperator + $addtorefnolen))">'."\n".
            '<xsl:value-of select="concat(substring($lineitem/@LOCATION, 1, $frbklen), \'...\', substring($lineitem/@LOCATION, ($loclen - $frbklen + 1), $frbklen))"/>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$lineitem/@LOCATION"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:variable>'."\n".
            '<!-- lineItemIndex = 1print Bill No and Date '."\n".
            'lineItemIndex = count, print Discount and Amount Paid'."\n".
            '-->'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$lineitem/@LINEITEMINDEX= 1">'."\n".
            '<fo:table-row>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$invoice/@DATE"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$invoice/@DESCRPTION"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displocacctnocheck = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$refno"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<fo:table-row font-style="italic">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block-container  height="0.19in" width="1.22in" border-style="null">'."\n".
            '<fo:block text-align="start" hyphenate="true" language="en">'."\n".
            '<xsl:value-of select="$lineitem/@ACCOUNT"/>'."\n".
            '</fo:block>'."\n".
            '</fo:block-container>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$memo"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="$check/@displocacctnocheck = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block start-indent="-5mm" line-height="6pt" text-align="start">'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="string-length($lineitem/@VERSIONACCTNO) > 10">'."\n".
            '<xsl:call-template name="StrWrap">'."\n".
            '<xsl:with-param name="start" select="1"/>'."\n".
            '<xsl:with-param name="len" select="string-length($lineitem/@VERSIONACCTNO)"/>'."\n".
            '<xsl:with-param name="field" select="$lineitem/@VERSIONACCTNO"/>'."\n".
            '<xsl:with-param name="chars" select="10"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$lineitem/@VERSIONACCTNO"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:when>'."\n".
            '</xsl:choose>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:value-of select="$dept"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="start">'."\n".
            '<xsl:call-template name="sp-replace">'."\n".
            '<xsl:with-param name="printfld" select="$location"/>'."\n".
            '</xsl:call-template>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:if test="($VarMultiCurrency = \'true\') and ($doingadvance = \'F\')">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$lineitem/@CURRENCY"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</xsl:if>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$lineitem/@AMOUNT"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '<xsl:if test="$displaytermdiscount = \'T\'">'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$lineitem/@DISCOUNT"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            "</xsl:if>"."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$lineitem/@AMOUNTPAID"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:template>'."\n".
            '<xsl:template name="text_wrapper">'."\n".
            '<xsl:param name="Text"/>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="string-length($Text)">'."\n".
            '<xsl:value-of select="substring($Text,1,10)"/>&#x200b;'."\n".
            '<xsl:call-template name="wrapper_helper">'."\n".
            '<xsl:with-param name="Text" select="substring($Text,11)"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:template>'."\n".
            //this will also display 10 chars of the string, and pass the rest back to text_wrapper
            '<xsl:template name="StrWrap">'."\n".
            '<xsl:param name="start"/>'."\n".
            '<xsl:param name="len"/>'."\n".
            '<xsl:param name="field"/>'."\n".
            '<xsl:param name="chars"/>'."\n".

            '<xsl:if test="$len > 0">'."\n".
            '<xsl:call-template name="sp-replace">'."\n".
            '<xsl:with-param name="printfld" select="substring($field, $start, $chars)"/>'."\n".
            '</xsl:call-template>'."\n".
            '<xsl:if test="($len - $chars) > 0">'."\n".
            '<xsl:text></xsl:text>'."\n".
            '</xsl:if>'."\n".
            '<fo:block whitespace-collapse="false">&#10;</fo:block>'."\n".
            '<xsl:call-template name="StrWrap">'."\n".
            '<xsl:with-param name="start" select="$start + $chars"/>'."\n".
            '<xsl:with-param name="len" select="$len - $chars"/>'."\n".
            '<xsl:with-param name="field" select="$field"/>'."\n".
            '<xsl:with-param name="chars" select="$chars"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:if>'."\n".
            '</xsl:template>'."\n";
        return $invoiceWithDetails;
    }


    /**
     * @return string
     */
    function GetTemplateTotals()
    {
        $Totals='<xsl:template name="Totals">'."\n".
            '<xsl:param name="check"/>'."\n".
            '<xsl:param name="colspan"/>'."\n".
            '<fo:table-row background-color="#BBBBBB" line-height="2pt">'."\n".
            '<fo:table-cell padding="2pt" number-columns-spanned="{$colspan}">'."\n";
        if($this->locale=='MX') {
            $Totals .= '<fo:block text-align="start">Monto Neto</fo:block>'."\n";
        }else{
            $Totals .= '<fo:block text-align="start">Net Amount:</fo:block>'."\n";
        }

        $Totals .= '</fo:table-cell>'."\n".
            '<fo:table-cell padding="2pt">'."\n".
            '<fo:block text-align="end">'."\n".
            '<xsl:value-of select="$check/invoicetable/Totals/@NetAmount"/>'."\n".
            '</fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</xsl:template>';
        return $Totals;
    }

    /**
     * @return string
     */
    function GetTemplateContd()
    {
        $Contnd='<xsl:template name="Contnd">'."\n".
            '<xsl:param name="colspan"/>'."\n".
            '<fo:table-row background-color="#BBBBBB" line-height="2pt">'."\n".
            '<fo:table-cell padding="2pt" number-columns-spanned="{$colspan+1}">'."\n".
            '<fo:block text-align="end">Contd.... </fo:block>'."\n".
            '</fo:table-cell>'."\n".
            '</fo:table-row>'."\n".
            '</xsl:template>';
        return $Contnd;
    }

    /**
     * @return string
     */
    function GetStrReplaceTemplate()
    {
        $strreplace =     '<xsl:template name="sp-replace">'."\n".
            '<xsl:param name="printfld"/>'."\n".
            '<!-- NOTE: There are two spaces   ** here below -->'."\n".
            '<xsl:variable name="sp"><xsl:text> </xsl:text></xsl:variable>'."\n".
            '<xsl:choose>'."\n".
            '<xsl:when test="contains($printfld,$sp)">'."\n".
            '<xsl:value-of select="substring-before($printfld,$sp)"/>'."\n".
            '<xsl:text>&#160;</xsl:text>'."\n".
            '<xsl:call-template name="sp-replace">'."\n".
            '<xsl:with-param name="printfld" select="substring-after($printfld,$sp)"/>'."\n".
            '</xsl:call-template>'."\n".
            '</xsl:when>'."\n".
            '<xsl:otherwise>'."\n".
            '<xsl:value-of select="$printfld"/>'."\n".
            '</xsl:otherwise>'."\n".
            '</xsl:choose>'."\n".
            '</xsl:template>';
        return $strreplace;
    }

    /**
     * @param int $fontSizePayTo
     */
    public function setPrintasOverflowAlert($fontSizePayTo)
    {
        $this->printasOverflow = true;
        $this->fontSizePayTo = $fontSizePayTo;
    }
}

