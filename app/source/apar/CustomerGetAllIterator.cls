<?php
 /**
 * File CustomerGetAllIterator.cls contains the class CustomerGetAllIterator
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
 

class CustomerGetAllIterator extends GetAllIterator
{
    /**
     * @param array $value
     */
    protected function postProcessSingleRecord( &$value )
    {
        parent::postProcessSingleRecord($value);

        if ( isset($value['ENTITY']) && $value['ENTITY'] != '' ) {
            $value['ENTITY'] = substr($value['ENTITY'], 1);
        }
    }
}