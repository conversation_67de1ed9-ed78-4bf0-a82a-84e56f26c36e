<?

//=============================================================================
//
//	FILE:			backend_payall.inc
//	AUTHOR:			rpn
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================	



define("showValidating", "1");
define("doValidating", "2");
define("showDialog", "3");
define("showProcessState", "4");
define("doProcessPayAll", "5");
define("donePayAll", "6");
define("sendEmail", "7");


/**
 * @return bool|int
 */
function HandleAction()
{
    global $kAPid;

    $gManagerFactory = Globals::$g->gManagerFactory;
    $ok = true;
    $_action = Request::$r->_action;
    $_mod = &Request::$r->_mod;
    $_sendmessages = Request::$r->_sendmessages;
    $_entities = Request::$r->_entities;
    $_confirmemail = Request::$r->_confirmemail;
    //$entList = $gRequest->Get('_entities');

    $_mod = ( $_mod ?: $kAPid );

    switch ( $_action ) {
    case showValidating :
        showState(showValidating);
        break;
    case doValidating :
        $sbeMgr = $gManagerFactory->getManager('summarybyentity');
        $sbeMgr->doValidateAction($_entities);
        HandleErrorState(doValidating);
        showConfirmDialog();
        break;
    case showProcessState:
        $ok = showPayEachInfo();
        break;
    case doProcessPayAll:
        $ok = showState(doProcessPayAll);
        $sbeMgr = $gManagerFactory->getManager('summarybyentity');
        $ok = $ok && $sbeMgr->initiateProcess();
        $ok = $ok && $sbeMgr->createAllPayments($_entities, $_confirmemail);
        HandleErrorState(doProcessPayAll);
        $ok = $ok && showState(donePayAll);
        break;
    case sendEmail:
        $sbeMgr = $gManagerFactory->getManager('summarybyentity');
        $ok = $sbeMgr->sendStatusMail('', $_sendmessages, $_confirmemail);
        break;
    default :
        dieFL(' no action specified. Invalid Request.... ');
        break;
    }

    return $ok;
}


/**
 * @param string $action
 *
 * @return int
 */
function showState($action)
{

    $msg = '';
    $doSubmit = '';
    switch ( $action ) {
    case showValidating :
        $msg = "Validating selected entity payments.<br>Depending on the volume this may take a little while.".
        "<br>Thank you for your patience.";
        $doSubmit = "doProcessSubmit(".doValidating.");";
        break;
    case doProcessPayAll :
        $msg = "Processing all selected entity payments.<br>Depending on the volume this may take a little while.".
        "<br>Thank you for your patience.";
        break;
    case donePayAll :
        //$text = "All selected entity payments are processed. New Payment Requests created.";
        //$doSubmit = "baseSetText(document.getElementById('txtMsg'), '$text');";
        //$doSubmit .= "doClose();";
        break;
    default :
        dieFL(' showState() :: no action specified. Invalid Request.... ');
        break;
    }
    
    if ( $msg ) {
        ?>	
     <BR><BR><BR><BR>
     <center>
     <table border="0" cellpadding="1" cellspacing="0" width="70%" bgcolor="#999966">
     <tr>
      <td valign="top">
       <table border="0" cellpadding="4" cellspacing="0" width="100%" bgcolor="#FFFFCC">
       <tr>
        <td id="txtMsg" valign="middle" align="center">
         <font id='msg' face="Verdana, Arial, Helvetica" size="3"><?=$msg;?><BR><BR></font>
					</td>
				</tr>
				</table>
      </td>
     </tr>
     </table>
     </center>
        <?
    }
    if ( $doSubmit ) {
        echo "<script>$doSubmit</script>";
    }

    return 1;
}

/**
 * @return int
 */
function showConfirmDialog()
{
    /** @noinspection PhpUndefinedVariableInspection */
    $_confirmemail = ( $_confirmemail ?: GetEmailForNotification() );
    $msg = "Confirm payment ...";
    $doSubmit = "if ( validEmail() ) doProcessSubmit(".showProcessState.");";

    ?>	
	<BR><BR><BR><BR>
	<center>
	<table border="0" cellpadding="1" cellspacing="0" width="70%" bgcolor="#999966">
	<tr>
		<td >
			<table border="0" cellpadding="4" cellspacing="0" width="100%" bgcolor="#FFFFCC">
			<tr>
				<td align="center" colspan=2>
					<font id="txtMsg" face="Verdana, Arial, Helvetica" size="3"><?=$msg;?></font>
				</td>
			</tr>
			<tr><td colspan=2>&nbsp;</td></tr>
			<tr>
				<td valign="center" align="right">
					<font face="Verdana, Arial, Helvetica" size="2">E-Mail status to this address</font>
				</td>
				<td valign="center" align="left">
					<input type="text" name=".confirmemail" value="<?=$_confirmemail;?>" maxlength=30 size=30>
				</td>
			</tr>
			<tr><td colspan=2>&nbsp;</td></tr>
			<tr>
				<td align="center" colspan=2>
					<input type="button" name="save" value="Confirm" onClick="<?=$doSubmit;?>">
				</td>
			</tr>
			</table>
		</td>
	</tr>
	</table>
	</center>
    <?

    return 1;
}

/**
 * @return bool
 */
function showPayEachInfo()
{   
    $_amountfrom   = Request::$r->_amountfrom;
    $_amountto     = Request::$r->_amountto;
    $_amountfromop = Request::$r->_amountfromop;
    $_amounttoop   = Request::$r->_amounttoop;
    $_payfull      = Request::$r->_payfull;
    $_applycredits = Request::$r->_applycredits;
    $_vendsel      = Request::$r->_vendsel;
    $_entities     = Request::$r->_entities;
    $_confirmemail = Request::$r->_confirmemail;

    $doSubmit = "doHiddenSubmit(".doProcessPayAll.");";
    ?>
	<script>
		var entList = '<?=join("|", $_entities)?>';
		var pfList = '<?=join("|", $_payfull)?>';
		var acList = '<?=join("|", $_applycredits)?>';

		var entities = Array();
		var processedArr = Array();
		var payfull = Array();
		var applycredits = Array();
		var amountfrom = '';
		var amountto = '';
		var amountfromop = '';
		var amounttoop = '';
		var vendsel = '';

    <?
    $i = 0;
    foreach ($_entities as $entity ) {
        echo "entities[$i] = '$entity';";
        echo "processedArr[$i] = '0';";
        $i++;
    }
    $j = 0;
    foreach ($_payfull as $pf ) {
        echo "payfull[$j] = '$pf';";
        $j++;
    }
    $k = 0;
    foreach ($_applycredits as $ac ) {
        echo "applycredits[$k] = '$ac';";
        $k++;
    }
    echo "amountfrom = '$_amountfrom';";
    echo "amountto = '$_amountto';";
    echo "amountfromop = '$_amountfromop';";
    echo "amounttoop = '$_amounttoop';";
    echo "vendsel = '$_vendsel';";
    ?>
	</script>
	<BR><BR><BR><BR>
	<center>
	<table border="0" cellpadding="0" cellspacing="0" width="90%" bgcolor="#ffffff">
    <? if ( $_entities ) { ?>
		<tr>
		<td>
			<table border="0" cellpadding="2" cellspacing="1" width="100%" bgcolor="#999999">
			<tr bgcolor="#CCCCCC">
				<td align="center" width="50%"><font face="Verdana, Arial, Helvetica" size="2">Entity</font></td>
				<td align="center" width="50%"><font face="Verdana, Arial, Helvetica" size="2">Status</font></td>
			</tr>
			</table>
		</td>
		</tr>
		<tr><td>
			<table border="0" cellpadding="2" cellspacing="1" width="100%" bgcolor="#999999" bgcolor1="#999966">
    <?
    foreach ($_entities as $entity ) { ?>
     <tr bgcolor="#CCCCCC">
      <td valign="middle" align="center" width="50%">
       <font id='<?="entityName[$entity]";?>' face="Verdana, Arial, Helvetica" size="2">
        <b><?=urldecode($entity);?></b>
       </font>
      </td>
      <td valign="middle" align="center" width="50%">
       <font id="<?="entityState[$entity]";?>" style='display:block' face="Verdana, Arial, Helvetica" size="2"><b>Processing ....</b></font>
      </td>
     </tr>
    <? 
    }?>
			</table>
		</td>
		</tr>
		<tr><td>
			<div id="close_button" style="position: relative; display:none">
			<table border="0" cellpadding="2" cellspacing="1" width="100%" bgcolor1="#999999" bgcolor="#999966">
			<tr bgcolor="#CCCCCC">
				<td valign="middle" align="center"><p>
					<input type="hidden" name=".confirmemail" value="<?=$_confirmemail?>">
					<input type="button" name=".close" value="Close" onClick="finalClose();">
				</td>
			</tr>
			</table>
			</div>
		</td></tr>
    <? 
} else { ?>
		<tr>
			<td valign="middle" align="center">
				<font id='msg' face="Verdana, Arial, Helvetica" size="3">No Entities Found <BR><BR></font>
			</td>
		</tr>
    <? 
} ?>
	</table>
    <?
    if ( $doSubmit ) { 
        echo "<script>setTimeout('$doSubmit',2000);</script>"; 
        //echo "<script><!--$doSubmit--></script>"; 
    }

    return true;
}

/**
 * @param string    $action
 *
 * @return int
 */
function HandleErrorState($action)
{

    if (HasErrors()) {

        global $noErrorHeader;
        $noErrorHeader = true;

        echo "<script>document.mf.elements['.action'].value = '$action';</script>";
        include_once 'popuperror.phtml';
        exit();
    }
    
    return 1;
}


