<!-- This XML is shared between the appymt_form.xml and the appymt_groupbyentity_form.xml -->
<!-- Advanced filter floating page -->
<floatingPage id="advancedFilterPage" modal="true" compact="true" fullscreen="true" topbuttons="true">
    <title>IA.ADVANCED_FILTERS</title>
    <pages>
        <page>
            <flexstack>
                <!-- provide page padding -->
                <flexsection>
                    <flexstack>
                        <flexrow>
                            <!-- Take up to 20% of available for this child.-->
                            <flexchild basis="20%">
                                <rangecontainer>
                                    <field>
                                        <path>FILTERRECORDNO</path>
                                        <hidden>1</hidden>
                                    </field>
                                    <field fullname="IA.FILTER_NAME">
                                        <path>FILTERNAME</path>
                                        <required>true</required>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                            <flexchild basis="80%">
                                <rangecontainer wrap="nowrap" layout="horizontal">
                                    <field fullname="IA.DEFAULT_FILTER">
                                        <path>DEFAULTFILTER</path>
                                        <type>
                                            <type>boolean</type>
                                        </type>
                                    </field>
                                    <field fullname="IA.SHARE_FILTER">
                                        <path>SHAREDFILTER</path>
                                        <type>
                                            <type>boolean</type>
                                        </type>
                                    </field>
                                    <field fullname="IA.SHOW_TOP_LEVEL_TRANSACTIONS" hidden="true">
                                        <path>SHOWTOPLEVELTRANSACTIONS</path>
                                        <type>
                                            <type>boolean</type>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                        </flexrow>
                        <flexrow wrap="nowrap">
                            <flexchild basis="100%">
                            <rangecontainer layout="horizontal" wrap="nowrap" basis_items="50%">
                                <title>IA.PAY_SOURCE_ENTITY</title>
                                <field fullname="IA.PAY_SOURCE_ENTITY" noLabel="true" hidden="true">
                                    <path>PAYSOURCEENTITY</path>
                                    <fieldentity>locationentity</fieldentity>
                                    <type>
                                        <type>text</type>
                                        <ptype>enum</ptype>
                                    </type>
                                    <events>
                                        <change>ReloadEntityList(this);</change>
                                    </events>
                                </field>
                            </rangecontainer>
                            </flexchild>
                        </flexrow>
                        <flexrow id="payentitySection" wrap="nowrap" hidden="true">

                            <flexchild basis="40%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="50%">
                                    <field>
                                        <path>PAYENTITIES</path>
                                        <fullname>IA.ENTITIES_TO_PAY</fullname>
                                        <fieldentity>locationentity</fieldentity>
                                        <type assoc="T">
                                            <ptype>multipick</ptype>
                                            <type>multipick</type>
                                            <entity>locationentity</entity>
                                            <pickentity>locationentity</pickentity>
                                            <delimiter>#~#</delimiter>
                                            <pick_url>picker.phtml</pick_url>
                                            <maxlength>40</maxlength>
                                        </type>
                                        <assist>fat</assist>
                                        <noedit>1</noedit>
                                        <noroot>1</noroot>
                                        <_func>Field</_func>
                                        <varpath>PAYENTITIES_SEC</varpath>
                                        <varname>_obj__PAYENTITIES_SEC</varname>
                                        <fromlabel>Available</fromlabel>
                                        <tolabel>Selected</tolabel>
                                        <datasort>true</datasort>
                                    </field>
                                    <!--<field fullname="IA.GROUP_BY_ENTITY">
                                        <path>GROUPBYENTITY</path>
                                        <type>
                                            <type>boolean</type>
                                        </type>
                                    </field>-->
                                </rangecontainer>
                            </flexchild>
                            <flexchild basis="60%">
                                <rangecontainer layout="horizontal" wrap="nowrap">
                                    <field fullname="IA.MULTI_ENTITY_BILLS">
                                        <path>MULTIENTITYBILLS</path>
                                        <type>
                                            <type>radio</type>
                                            <ptype>radio</ptype>
                                        </type>
                                        <layout>portrait</layout>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                            <!--<flexchild>
                                <rangecontainer wrap="nowrap" layout="horizontal">
                                    <field fullname="IA.GROUP_BY_ENTITY">
                                        <path>GROUPBYENTITY</path>
                                        <type>
                                            <type>boolean</type>
                                        </type>
                                    </field>
                                    <field fullname="IA.MULTI_ENTITY_BILLS">
                                        <path>MULTIENTITYBILLS</path>
                                        <type>
                                            <type>radio</type>
                                            <ptype>radio</ptype>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>-->
                        </flexrow>
                        <flexrow wrap="nowrap">
                            <flexchild basis="50%">
                                <!-- If parent (flexchild) can't grow and has a determined width (40%),
                                the children must be told how to divide that width. Setting the
                                base_items attribute does that. In this case, two fields so 50%
                                each. -->
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="30%">
                                    <title>IA.DUE_DATE_RANGE</title>
                                    <field fullname="IA.DATE_PERIOD" noLabel="true">
                                        <path>WHENDUERANGEPERIOD</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>enum</type>
                                            <ptype>enum</ptype>
                                        </type>
                                        <events>
                                            <change>toggleDateRangePicker(this.meta,'WHENDUERANGESTART','WHENDUERANGEEND','WHENDUERANGEPERIODOPR','WHENDUERANGEPERIODOPRDAYS');</change>
                                        </events>
                                    </field>
                                    <field fullname="IA.OPERATION" noLabel="true" hidden="true">
                                        <path>WHENDUERANGEPERIODOPR</path>
                                        <type>
                                            <type>webcombo</type>
                                            <ptype>webcombo</ptype>
                                        </type>
                                    </field>
                                    <field fullname="IA.DAYS" hidden="true">
                                        <path>WHENDUERANGEPERIODOPRDAYS</path>
                                        <type>
                                            <type>integer</type>
                                            <ptype>integer</ptype>
                                        </type>
                                    </field>
                                    <field fullname="IA.DUE_DATE_RANGE" noLabel="true" hidden="true">
                                        <path>WHENDUERANGESTART</path>
                                        <fieldentity>billentity</fieldentity>
                                        <range>start</range>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                    </field>
                                    <field fullname="IA.TO" noLabel="true" hidden="true">
                                        <path>WHENDUERANGEEND</path>
                                        <fieldentity>billentity</fieldentity>
                                        <range>end</range>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                            <flexchild basis="50%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="50%">
                                    <title>IA.VENDOR_RANGE</title>
                                    <field fullname="IA.FROM_VENDOR" noLabel="true">
                                        <path>VENDORIDRANGESTART</path>
                                        <fieldentity>vendorentity</fieldentity>
                                        <type>
                                            <type>ptr</type>
                                            <ptype>ptr</ptype>
                                            <entity>vendor</entity>
                                            <pickentity>vendorpick</pickentity>
                                        </type>
                                    </field>
                                    <field fullname="IA.TO" noLabel="true">
                                        <path>VENDORIDRANGEEND</path>
                                        <fieldentity>vendorentity</fieldentity>
                                        <type>
                                            <type>ptr</type>
                                            <ptype>ptr</ptype>
                                            <entity>vendor</entity>
                                            <pickentity>vendorpick</pickentity>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                        </flexrow>
                        <flexrow>
                            <flexchild basis="50%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="30%">
                                    <title>IA.BILL_DATE_RANGE</title>
                                    <field fullname="IA.BILL_PERIOD" noLabel="true">
                                        <path>WHENCREATEDRANGEPERIOD</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>enum</type>
                                            <ptype>enum</ptype>
                                        </type>
                                        <events>
                                            <change>toggleDateRangePicker(this.meta,'WHENCREATEDRANGESTART','WHENCREATEDRANGEEND','WHENCREATEDRANGEPERIODOPR','WHENCREATEDRANGEPERIODOPRDAYS');</change>
                                        </events>
                                    </field>
                                    <field fullname="IA.OPERATION" noLabel="true" hidden="true">
                                        <path>WHENCREATEDRANGEPERIODOPR</path>
                                        <type>
                                            <type>webcombo</type>
                                            <ptype>webcombo</ptype>
                                        </type>
                                    </field>
                                    <field fullname="IA.DAYS" hidden="true">
                                        <path>WHENCREATEDRANGEPERIODOPRDAYS</path>
                                        <type>
                                            <type>integer</type>
                                            <ptype>integer</ptype>
                                        </type>
                                    </field>
                                    <field fullname="IA.BILL_DATE_RANGE" noLabel="true" hidden="true">
                                        <path>WHENCREATEDRANGESTART</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                        <events>
                                            <change>loadDateDetails(this.meta, 'appymt_advanced_filter');</change>
                                        </events>
                                    </field>
                                    <field fullname="IA.TO" noLabel="true" hidden="true">
                                        <path>WHENCREATEDRANGEEND</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                            <flexchild basis="25%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="100%">
                                    <title>IA.BILL_PAYMENT_PRIORITY</title>
                                    <field fullname="IA.BILL_PAYMENT_PRIORITY" noLabel="true">
                                        <path>PAYMENTPRIORITY</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>text</type>
                                            <ptype>enum</ptype>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                            <flexchild basis="25%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="50%">
                                    <title>IA.RECOMMENDED_PAYMENT_DATE</title>
                                    <field fullname="IA.RECOMMENDED_PAYMENT_DATE" noLabel="true">
                                        <path>RECPAYMENTDATE</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                        </flexrow>
                        <flexrow wrap="wrap">
                            <flexchild basis="40%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="50%">
                                    <title>IA.BILL_NUMBER_RANGE</title>
                                    <field fullname="IA.BILL_NUMBER_RANGE" noLabel="true" hidden="true">
                                        <path>RECORDIDRANGESTART</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>integer</type>
                                            <ptype>integer</ptype>
                                        </type>
                                    </field>
                                    <field fullname="IA.TO" noLabel="true" hidden="true">
                                        <path>RECORDIDRANGEEND</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>integer</type>
                                            <ptype>integer</ptype>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                        </flexrow>
                        <flexrow wrap="nowrap">
                            <flexchild basis="25%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="100%">
                                    <title>IA.PAYMENT_RANGE</title>
                                    <field fullname="IA.PAYMENT_RANGE" noLabel="true">
                                        <path>TOTALDUESTARTOPERATOR</path>
                                        <type>
                                            <type>webcombo</type>
                                            <ptype>webcombo</ptype>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                            <flexchild basis="50%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="50%">
                                    <title>IA.AMOUNT</title>
                                    <field fullname="" noLabel="true">
                                        <path>TOTALDUESTART</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>decimal</type>
                                            <ptype>currency</ptype>
                                            <maxlength>14</maxlength>
                                            <format>/^-{0,1}[0-9]*\.{0,1}[0-9]*$/</format>
                                        </type>
                                    </field>
                                    <field fullname="" noLabel="true">
                                        <path>TOTALDUEENDOPERATOR</path>
                                        <type>
                                            <type>webcombo</type>
                                            <ptype>webcombo</ptype>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                             <flexchild basis="25%">
                                <rangecontainer layout="horizontal" wrap="nowrap" basis_items="100%">
                                    <title>IA.AMOUNT</title>
                                    <field fullname="" noLabel="true">
                                        <path>TOTALDUEEND</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>decimal</type>
                                            <ptype>currency</ptype>
                                            <maxlength>14</maxlength>
                                            <format>/^-{0,1}[0-9]*\.{0,1}[0-9]*$/</format>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                        </flexrow>
                        <flexrow wrap="nowrap">
                            <flexchild basis="20%">
                                <!-- Using a container for consistent layout. -->
                                <rangecontainer wrap="nowrap" basis_items="100%">
                                    <field fullname="IA.DISCOUNT_AVAILABLE_AS_OF">
                                        <path>WHENDISCOUNT</path>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                            <flexchild basis="20%">
                                <rangecontainer wrap="nowrap" basis_items="100%">
                                    <field fullname="IA.BILL_CURRENCY" hidden="true">
                                        <path>CURRENCY</path>
                                        <required>false</required>
                                        <fieldentity>billentity</fieldentity>
                                        <type>
                                            <type>ptr</type>
                                            <ptype>ptr</ptype>
                                            <entity>trxcurrencies</entity>
                                        </type>
                                    </field>
                                </rangecontainer>
                            </flexchild>
                        </flexrow>
                    </flexstack>
                </flexsection>
                <flexsection id="allAttrSection">
                    <flexchild grow="1">
                        <grid noDragDrop="false" isCollapsible="true">
                            <title>IA.DRILL_DOWN_FILTERS</title>
                            <path>FILTERATTRIBUTES</path>
                            <column>
                                <field>
                                    <path>ENTITYNAME</path>
                                    <fullname>IA.OBJECT</fullname>
                                    <type assoc="T">
                                        <ptype>webcombo</ptype>
                                        <type>webcombo</type>
                                        <size>60</size>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>ATTRIBUTENAME</path>
                                    <fullname>IA.ATTRIBUTE</fullname>
                                    <type assoc="T">
                                        <ptype>webcombo</ptype>
                                        <type>webcombo</type>
                                        <size>60</size>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>OPERAND</path>
                                    <fullname>IA.OPERATOR</fullname>
                                    <type assoc="T">
                                        <ptype>webcombo</ptype>
                                        <type>webcombo</type>
                                        <size>30</size>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>VALUEFROM</path>
                                    <fullname>IA.VALUE</fullname>
                                    <type assoc="T">
                                        <ptype>text</ptype>
                                        <type>text</type>
                                        <maxlength>80</maxlength>
                                    </type>
                                </field>
                            </column>
                        </grid>
                    </flexchild>
                </flexsection>
                <flexsection id="sortSection">
                    <flexchild grow="1">
                        <grid noDragDrop="false" isCollapsible="true">
                            <title>IA.SORT_BY</title>
                            <path>SORTBY</path>
                            <column>
                                <field>
                                    <path>SORTBYATTRIBUTE</path>
                                    <fullname>IA.ATTRIBUTE</fullname>
                                    <type assoc="T">
                                        <ptype>webcombo</ptype>
                                        <type>webcombo</type>
                                        <size>60</size>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>SORTOPERAND</path>
                                    <fullname>IA.ORDER</fullname>
                                    <type assoc="T">
                                        <ptype>webcombo</ptype>
                                        <type>webcombo</type>
                                        <size>30</size>
                                    </type>
                                </field>
                            </column>
                        </grid>
                    </flexchild>
                </flexsection>
            </flexstack>
        </page>
    </pages>
    <footer>
        <button id="deleteFilter" hidden="true">
            <name>IA.DELETE_THIS_FILTER</name>
            <events>
                <click>
                    deleteFilter(this);
                </click>
            </events>
        </button>
        <button id="saveFilter">
            <name>IA.SAVE</name>
            <events>
                <click>saveFilter(this);</click>
            </events>
        </button>
        <button>
            <name>IA.CANCEL</name>
            <events>
                <click>closeAdvancedFilters();</click>
            </events>
        </button>
    </footer>
    <events>
        <close>closeAdvancedFilters();</close>
    </events>
</floatingPage>

