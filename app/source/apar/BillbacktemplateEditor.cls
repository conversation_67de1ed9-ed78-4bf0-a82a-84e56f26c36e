<?
/**
 *    FILE:        BillbacktemplateEditor.cls
 *    AUTHOR:        Mgohel
 *    DESCRIPTION: BillbacktemplateEditor class
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */


import('Editor');

class BillbacktemplateEditor extends FormEditor
{
    /** @var bool $hasDepartment  */
    private $hasDepartment;

    /**
     * @param array $_params
     */
    public function __construct($_params)
    {
        parent::__construct($_params);
        $this->hasDepartment=$this->hasDepartment();
    }

    /**
     * This is a hook functions for subclases to add the dynamic metadata into the current layout.
     * At the time this function is called, the data, state and view objects are not available.
     * The subclass must operate on the given params structure.
     *
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {
        $_r  = Request::$r->_r;
        $action = Request::$r->_action;
        if (IsMultiEntityCompany() && GetContextLocation() && ($_r == '' || $action == 'copy')) {
            global $gErr;
            $gErr->addError('AR-0335', __FILE__ . ':' . __LINE__, "Cannot create a bill back template at the entity level for an multi-entity company.");
            include 'popuperror.phtml';
            exit();
        }
        parent::buildDynamicMetadata($params);
       if(!$this->hasDepartment) {
           self::findAndSetMetadata($params, array('path' => 'DEPARTMENT'), array('hidden' => true),EditorComponentFactory::TYPE_FIELD);
       }
    }

    /**
     * @return bool
     */
    public function hasDepartment()
    {
        return departmentsExist();
    }

    /**
     * @param EditorGrid    $grid
     * @param EntityManager $entityMgr
     * @param array         $_obj
     */
    protected function innerCleanupLineItems($grid, $entityMgr, &$_obj)
    {
        global $gErr;
        foreach($_obj['BBTEMPLATEITEM'] as &$item){
            if(isset($item['INVACCOUNT']) && $item['INVACCOUNT'] != '') {
                if(!isset($item['BILLACCOUNT']) || $item['BILLACCOUNT'] == '') {
                    $gErr->addError(
                        'AR-0336', __FILE__.':'.__LINE__,
                        "Bill GL Account is required"
                    );
                    include 'popuperror.phtml';
                    exit();
                }
                if (($item['DEPARTMENT'] ?? '') === '') {
                    unset($item['DEPARTMENTKEY']);
                    unset($item['DEPARTMENTID']);
                    unset($item['DEPARTMENTNAME']);
                }
            }
        }
        parent::innerCleanupLineItems($grid, $entityMgr, $_obj);
    }

    /**
     * @param array &$obj entity
     *
     * @return bool
     */
    protected function prepareObjectForSave(&$obj)
    {
        foreach ($obj['BBTEMPLATEITEM'] as &$lineitem){
            unset($lineitem['LINE_NO']);
        }
        return parent::prepareObjectForSave($obj);
    }
    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        foreach ($obj['BBTEMPLATEITEM'] as &$item) {
            if (isset($item['INVACCOUNTKEY']) && $item['INVACCOUNTKEY'] != '') {
                $item['INVACCOUNT'] = $item['INVGLACCOUNTNO'] . '--' . $item['INVGLACCOUNTTITLE'];
            }
            if (isset($item['BILLACCOUNTKEY']) && $item['BILLACCOUNTKEY'] != '') {
                $item['BILLACCOUNT'] = $item['BILLGLACCOUNTNO'] . '--' . $item['BILLGLACCOUNTTITLE'];
            }
            if (isset($item['DEPARTMENTKEY']) && $item['DEPARTMENTKEY'] != '') {
                $item['DEPARTMENT'] = $item['DEPARTMENTID'] . '--' . $item['DEPARTMENTNAME'];
            }
        }
        return parent::mediateDataAndMetadata($obj);
    }
}

