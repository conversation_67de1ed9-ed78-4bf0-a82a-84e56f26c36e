<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <xsl:output method="xml" encoding="utf-8"/>
    <xsl:variable name="VarMultiCurrency">
        <xsl:value-of select="/ROOT/flags/@MULTICURRENCY"/>
    </xsl:variable>
    <!-- Global variables -->
    <xsl:variable name="pgwidth">8.5</xsl:variable>
    <xsl:variable name="pgheight">11</xsl:variable>
    <xsl:variable name="x1">0</xsl:variable>
    <xsl:variable name="x2">0.6in</xsl:variable>
    <xsl:variable name="x3">4.0in</xsl:variable>
    <xsl:variable name="x4">6.4in</xsl:variable>
    <xsl:variable name="y1">0</xsl:variable>
    <xsl:variable name="y2">0.15</xsl:variable>
    <xsl:variable name="y3">0.95in</xsl:variable>
    <xsl:variable name="companyAddressX" select="$x1"/>
    <xsl:variable name="companyAddressY" select="$y1"/>
    <xsl:variable name="vendorAddressX" select="$x2"/>
    <xsl:variable name="PayToX" select="$x1"/>
    <xsl:variable name="MemoX" select="$x3"/>
    <xsl:variable name="MemoY">0.73</xsl:variable>
    <xsl:variable name="BankAddressX" select="$x3"/>
    <xsl:variable name="BankAddressY" select="$y2"/>
    <xsl:variable name="BankCodeX" select="$x3"/>
    <xsl:variable name="BankCodeY" select="$y3"/>
    <xsl:variable name="CheckNumX" select="$x4"/>
    <xsl:variable name="signatureX">4.75in</xsl:variable> <!-- JohnG changed to 4.75in from 4.3in to move the check signature lines to the right-->
    <!-- MICR Dimenstions -->
    <xsl:variable name="micrCheckNumX">2.04</xsl:variable>
    <xsl:variable name="micrBranchIDX">2.04</xsl:variable>
    <xsl:variable name="micrRoutingNumX">2.165</xsl:variable>
    <xsl:variable name="micrAcctNumRightX">5.915</xsl:variable>
    <xsl:variable name="micrAcctNumLeftX">3.54</xsl:variable>
    <xsl:variable name="micrY">2.80</xsl:variable>
    <!--There are definitions of payformanceOffsetY in every single template as we finally
    got the MICR right and affecting $top for the whole stub will move the MICR -->
    <!-- TOP definitions -->
    <!-- These define the TOP coordinates for the Three fo:block-containers
         1. Check Section on the Page
         2. Invoice Stub 1 (This prints the Line Item Details if the 'shodetails' attribute is set to 'T'
         3. Invoice Stub 2
         The T, M , B suffixes are for 'Check on Top', 'Check on Middle','Check on bottom (Online Check) cases.
    -->
    <xsl:variable name="checktop_T">0.0in</xsl:variable>
    <xsl:variable name="checktop_M">3.5in</xsl:variable>
    <xsl:variable name="checktop_B">7.6in</xsl:variable>
    <xsl:variable name="invoicestub1_top_T">6.95in</xsl:variable> <!-- JohnG Orig 7.2in Needed to modify where 'Page x of y' was printing -->
    <xsl:variable name="invoicestub1_top_M">6.95in</xsl:variable> <!-- JohnG Orig 7.2in Needed to modify where 'Page x of y' was printing -->
    <xsl:variable name="invoicestub1_top_B">0.20in</xsl:variable> <!-- JohnG Orig -0.15in Needed to modify where 'Page x of y' was printing -->
    <xsl:variable name="invoicestub2_top_T">3.25in</xsl:variable> <!-- JohnG Orig 3.35in Needed to modify where 'Page x of y' was printing -->
    <xsl:variable name="invoicestub2_top_M">-0.25in</xsl:variable> <!-- JohnG Orig -0.15in Needed to modify where 'Page x of y' was printing -->
    <xsl:variable name="invoicestub2_top_B">3.65in</xsl:variable> <!-- JohnG Orig 3.85in Needed to modify where 'Page x of y' was printing -->
    <xsl:variable name="cadDateTag">YYYYMMDD</xsl:variable>
    <xsl:variable name="checksperpage"></xsl:variable>
    <!-- End of TOP definitions -->
    <!--End   Global variables -->
    <xsl:template match="/">
        <xsl:apply-templates select="ROOT"/>
    </xsl:template>
    <xsl:template match="ROOT">
        <fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
            <xsl:call-template name="pageMaster">
                <xsl:with-param name="margin" select="margin"/>
            </xsl:call-template>
            <xsl:apply-templates/>
        </fo:root>
    </xsl:template>
    <xsl:template name="pageMaster">
        <xsl:param name="margin"/>
        <!-- 		$margin/@left, $margin/@top-->
        <fo:layout-master-set>
            <fo:simple-page-master master-name="checkmaster" margin-right="0.1in" margin-left="{$margin/@left}in" margin-bottom="0.1in" margin-top="{$margin/@top}in" page-width="{$pgwidth}in" page-height="{$pgheight}in">
                <fo:region-body overflow="auto" margin-top="0.1in" margin-bottom="0.1in"/>
            </fo:simple-page-master>
        </fo:layout-master-set>
    </xsl:template>
    <xsl:template match="check">
        <xsl:variable name="billsPerPage">
            <xsl:choose>
                <xsl:when test="(@showdetails = 'T') or (@showdetailedvendstub = 'T')">6</xsl:when>
                <xsl:otherwise>18</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="noofLines">
            18
        </xsl:variable>
        <xsl:variable name="noOfBills" select="count(invoicetable/invoice)"/>
        <xsl:variable name="noOfLineItems" select="count(invoicetable/invoice/LINEITEM)"/>
        <xsl:variable name="noOfPages">
            <xsl:choose>
                <xsl:when test="(@showdetails = 'T') or (@showdetailedvendstub = 'T')">
                    <xsl:value-of select="ceiling( $noOfLineItems div $noofLines)"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="ceiling( $noOfBills div $noofLines)"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="lastPageIndex" select="$noOfPages"/>
        <xsl:choose>
            <xsl:when test="(@showdetails = 'T') or (@showdetailedvendstub = 'T')">
                <xsl:call-template name="printPageWithDetails">
                    <xsl:with-param name="currPageNumber">1</xsl:with-param>
                    <xsl:with-param name="startIndex">1</xsl:with-param>
                    <xsl:with-param name="check" select="."/>
                    <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                    <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                    <xsl:with-param name="noofLines" select="$noofLines"/>
                </xsl:call-template>
            </xsl:when>
            <xsl:otherwise>
                <xsl:call-template name="printPage">
                    <xsl:with-param name="currPageNumber">1</xsl:with-param>
                    <xsl:with-param name="startIndex">1</xsl:with-param>
                    <xsl:with-param name="check" select="."/>
                    <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                    <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                    <xsl:with-param name="noofLines" select="$noofLines"/>
                </xsl:call-template>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>
    <xsl:template name="printPage">
        <xsl:param name="currPageNumber"/>
        <xsl:param name="startIndex"/>
        <xsl:param name="check"/>
        <xsl:param name="lastPageIndex"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <!-- set the TOP co-ordinates for the three parts of the CHECK -->
        <xsl:variable name="checktop">
            <xsl:choose>
                <xsl:when test="($check/@format = 'T')">
                    <xsl:value-of select="$checktop_T"/>
                </xsl:when>
                <xsl:when test="($check/@format ='M')">
                    <xsl:value-of select="$checktop_M"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$checktop_B"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="invoicestub1_top">
            <xsl:choose>
                <xsl:when test="($check/@format = 'T')">
                    <xsl:value-of select="$invoicestub1_top_T"/>
                </xsl:when>
                <xsl:when test="($check/@format = 'M')">
                    <xsl:value-of select="$invoicestub1_top_M"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$invoicestub1_top_B"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="invoicestub2_top">
            <xsl:choose>
                <xsl:when test="($check/@format = 'T')">
                    <xsl:value-of select="$invoicestub2_top_T"/>
                </xsl:when>
                <xsl:when test="($check/@format = 'M')">
                    <xsl:value-of select="$invoicestub2_top_M"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$invoicestub2_top_B"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="firstPage">
            <xsl:choose>
                <xsl:when test="$currPageNumber= 1">Y</xsl:when>
                <xsl:otherwise>N</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="nextStartIndex" select="($noofLines * $currPageNumber) + 1"/>
        <!-- Printing One Line per Invoice here.
        Do not parse the LINEITEM Element of invoice
        Just traverse thru the invoices in the invoicetable to print the invoice stub
        -->
        <xsl:for-each select="$check/invoicetable/invoice">
            <xsl:variable name="printFirstOnly">
                <xsl:choose>
                    <xsl:when test="( $check/bankinfo/@printon = 'P' and $check/@nonnegotiable = '' and $currPageNumber &gt; 1)">0</xsl:when>
                    <xsl:otherwise>1</xsl:otherwise>
                </xsl:choose>
            </xsl:variable>
            <xsl:variable name="currPos" select="position()"/>
            <xsl:if test="(($currPos &gt;  $startIndex) or ($currPos = $startIndex) and $printFirstOnly != 0 )">
                <xsl:if test="$currPos = ($nextStartIndex) ">
                    <xsl:call-template name="printPage">
                        <xsl:with-param name="currPageNumber" select="$currPageNumber + 1"/>
                        <xsl:with-param name="startIndex" select="$currPos"/>
                        <xsl:with-param name="check" select="$check"/>
                        <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                        <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                        <xsl:with-param name="noofLines" select="$noofLines"/>
                    </xsl:call-template>
                </xsl:if>
                <xsl:if test="($currPos =( ($currPageNumber -1) * $noofLines + 1))">
                    <fo:page-sequence master-reference="checkmaster">
                        <fo:flow flow-name="xsl-region-body">
                            <xsl:call-template name="checkStub">
                                <xsl:with-param name="firstPage" select="$firstPage"/>
                                <xsl:with-param name="check" select="$check"/>
                                <xsl:with-param name="top" select="$checktop"/>
                            </xsl:call-template>
                            <xsl:call-template name="invoiceStub">
                                <xsl:with-param name="currPage" select="$currPageNumber"/>
                                <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                <xsl:with-param name="check" select="$check"/>
                                <xsl:with-param name="top" select="$invoicestub1_top"/>
                                <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                <xsl:with-param name="noofLines" select="$noofLines"/>
                            </xsl:call-template>
                            <xsl:call-template name="invoiceStub">
                                <xsl:with-param name="currPage" select="$currPageNumber"/>
                                <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                <xsl:with-param name="check" select="$check"/>
                                <xsl:with-param name="top" select="$invoicestub2_top"/>
                                <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                <xsl:with-param name="noofLines" select="$noofLines"/>
                            </xsl:call-template>
                        </fo:flow>
                    </fo:page-sequence>
                </xsl:if>
            </xsl:if>
        </xsl:for-each>
    </xsl:template>
    <xsl:template name="printPageWithDetails">
        <xsl:param name="currPageNumber"/>
        <xsl:param name="startIndex"/>
        <xsl:param name="check"/>
        <xsl:param name="lastPageIndex"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <!-- set the TOP co-ordinates for the three parts of the CHECK -->
        <xsl:variable name="checktop">
            <xsl:choose>
                <xsl:when test="($check/@format = 'T')">
                    <xsl:value-of select="$checktop_T"/>
                </xsl:when>
                <xsl:when test="($check/@format = 'M')">
                    <xsl:value-of select="$checktop_M"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$checktop_B"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="invoicestub1_top">
            <xsl:choose>
                <xsl:when test="($check/@format = 'T')">
                    <xsl:value-of select="$invoicestub1_top_T"/>
                </xsl:when>
                <xsl:when test="($check/@format = 'M')">
                    <xsl:value-of select="$invoicestub1_top_M"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$invoicestub1_top_B"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="invoicestub2_top">
            <xsl:choose>
                <xsl:when test="($check/@format = 'T')">
                    <xsl:value-of select="$invoicestub2_top_T"/>
                </xsl:when>
                <xsl:when test="($check/@format = 'M')">
                    <xsl:value-of select="$invoicestub2_top_M"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$invoicestub2_top_B"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="firstPage">
            <xsl:choose>
                <xsl:when test="$currPageNumber= 1">Y</xsl:when>
                <xsl:otherwise>N</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="nextStartIndex" select="$noofLines * $currPageNumber + 1"/>
        <!-- Printing One Line per Line Item/Invoice here
        Parse the LINEITEM Element of each Invoice, and determine when to print the next page
        (when ROWINDEX on LINEITEM is same as the startIndex on the nextPage)
        -->
        <xsl:for-each select="$check/invoicetable/invoice">
            <!-- The below variable will ensure only the first page of pre-printed checks will be printed if the check has more number of stub details -->
            <xsl:variable name="printFirstOnly">
                <xsl:choose>
                    <xsl:when test="( $check/bankinfo/@printon = 'P' and $check/@nonnegotiable = '' and $currPageNumber &gt; 1)">0</xsl:when>
                    <xsl:otherwise>1</xsl:otherwise>
                </xsl:choose>
            </xsl:variable>
            <xsl:for-each select="LINEITEM">
                <xsl:variable name="currPos" select="@ROWINDEX"/>
                <xsl:if test="(($currPos &gt;  $startIndex or $currPos = $startIndex) and $printFirstOnly != 0 )">
                    <xsl:if test="$currPos = $nextStartIndex ">
                        <xsl:call-template name="printPageWithDetails">
                            <xsl:with-param name="currPageNumber" select="$currPageNumber + 1"/>
                            <xsl:with-param name="startIndex" select="$currPos"/>
                            <xsl:with-param name="check" select="$check"/>
                            <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                            <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                            <xsl:with-param name="noofLines" select="$noofLines"/>
                        </xsl:call-template>
                    </xsl:if>
                    <xsl:if test="($currPos =( ($currPageNumber -1) * $noofLines + 1))">
                        <fo:page-sequence master-reference="checkmaster">
                            <fo:flow flow-name="xsl-region-body">
                                <xsl:call-template name="checkStub">
                                    <xsl:with-param name="firstPage" select="$firstPage"/>
                                    <xsl:with-param name="check" select="$check"/>
                                    <xsl:with-param name="top" select="$checktop"/>
                                </xsl:call-template>
                                <xsl:if test="$check/@showdetailedvendstub = 'F' and $check/@showdetails = 'T'">
                                    <xsl:call-template name="invoiceStubWithDetails">
                                        <xsl:with-param name="currPage" select="$currPageNumber"/>
                                        <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                        <xsl:with-param name="check" select="$check"/>
                                        <xsl:with-param name="top" select="$invoicestub1_top"/>
                                        <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                        <xsl:with-param name="noofLines" select="$noofLines"/>
                                    </xsl:call-template>
                                    <xsl:call-template name="invoiceStub">
                                        <xsl:with-param name="currPage" select="$currPageNumber"/>
                                        <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                        <xsl:with-param name="check" select="$check"/>
                                        <xsl:with-param name="top" select="$invoicestub2_top"/>
                                        <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                        <xsl:with-param name="noofLines" select="$noofLines"/>
                                    </xsl:call-template>
                                </xsl:if>
                                <xsl:if test="$check/@showdetailedvendstub = 'T' and $check/@showdetails = 'F'">
                                    <xsl:call-template name="invoiceStub">
                                        <xsl:with-param name="currPage" select="$currPageNumber"/>
                                        <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                        <xsl:with-param name="check" select="$check"/>
                                        <xsl:with-param name="top" select="$invoicestub1_top"/>
                                        <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                        <xsl:with-param name="noofLines" select="$noofLines"/>
                                    </xsl:call-template>
                                    <xsl:call-template name="invoiceStubWithDetails">
                                        <xsl:with-param name="currPage" select="$currPageNumber"/>
                                        <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                        <xsl:with-param name="check" select="$check"/>
                                        <xsl:with-param name="top" select="$invoicestub2_top"/>
                                        <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                        <xsl:with-param name="noofLines" select="$noofLines"/>
                                    </xsl:call-template>
                                </xsl:if>
                                <xsl:if test="$check/@showdetailedvendstub = 'T' and $check/@showdetails = 'T'">
                                    <xsl:call-template name="invoiceStubWithDetails">
                                        <xsl:with-param name="currPage" select="$currPageNumber"/>
                                        <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                        <xsl:with-param name="check" select="$check"/>
                                        <xsl:with-param name="top" select="$invoicestub1_top"/>
                                        <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                        <xsl:with-param name="noofLines" select="$noofLines"/>
                                    </xsl:call-template>
                                    <xsl:call-template name="invoiceStubWithDetails">
                                        <xsl:with-param name="currPage" select="$currPageNumber"/>
                                        <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                        <xsl:with-param name="check" select="$check"/>
                                        <xsl:with-param name="top" select="$invoicestub2_top"/>
                                        <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                        <xsl:with-param name="noofLines" select="$noofLines"/>
                                    </xsl:call-template>
                                </xsl:if>
                            </fo:flow>
                        </fo:page-sequence>
                    </xsl:if>
                </xsl:if>
            </xsl:for-each>
        </xsl:for-each>
    </xsl:template>
    <xsl:template name="checkStub">
        <xsl:param name="firstPage"/>
        <xsl:param name="check"/>
        <xsl:param name="top"/>
        <xsl:variable name="bgImage">
            <xsl:choose>
                <xsl:when test="$check/@voidtestcheck != ''"><xsl:value-of select="$check/@voidtestcheck"/></xsl:when>
                <xsl:otherwise><xsl:value-of select="$check/@filecopyimage"/></xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:block-container background-repeat="no-repeat" height="3.5in" width="8.5in" top="{$top}" left="0.0in" position="absolute">
            <xsl:if test="($bgImage != '')">
                <xsl:attribute name="background-image"><xsl:value-of select="$bgImage"/></xsl:attribute>
            </xsl:if>
            <!-- If printing the first page, Print all the details on the Check section
            Otherwise Print ***VOID*** and Company/Vendor Information on the Check section
            -->
            <xsl:choose>
                <xsl:when test="$firstPage = 'Y'">
                    <xsl:call-template name="checkStub_firstPage">
                        <xsl:with-param name="check" select="$check"/>
                    </xsl:call-template>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:call-template name="checkStub_restPages">
                        <xsl:with-param name="check" select="$check"/>
                    </xsl:call-template>
                </xsl:otherwise>
            </xsl:choose>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="checkStub_firstPage">
        <xsl:param name="check"/>
        <xsl:param name="top"/>
        <fo:block-container height="3.5in" width="8.5in" top="{$top}" left="0.0in" position="absolute">
            <xsl:if test="$check/@notprintedcheck != 'T'">
                <xsl:call-template name="printCompanyInfo">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
                <xsl:call-template name="printAmountInfo">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
                <xsl:call-template name="printPayToInfo">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
                <xsl:call-template name="printMemoInfo">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
                <!--<xsl:call-template name="printMICRLine">
                <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>-->
                <xsl:call-template name="printCheckNumInfo">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
                <xsl:call-template name="printSignature">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
                <!--<xsl:call-template name="printBankInfo">
                <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>-->
            </xsl:if>
            <xsl:if test="$check/@notprintedcheck = 'T'">
                <xsl:call-template name="nonPrintedCheckTable">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
            </xsl:if>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="checkStub_restPages">
        <xsl:param name="check"/>
        <xsl:param name="top"/>
        <fo:block-container height="3.5in" width="8.5in" top="{$top}" left="0.0in" position="absolute">
            <xsl:call-template name="printCompanyInfo">
                <xsl:with-param name="check" select="$check"/>
            </xsl:call-template>
            <xsl:call-template name="printPayToInfo">
                <xsl:with-param name="check" select="$check"/>
            </xsl:call-template>
            <fo:block-container height="0.7in" width="5.5in" top="1.0in" left="{$x2}" position="absolute">
                <fo:block text-align="center" line-height="11pt" font-family="Helvetica" font-size="16pt">
                    *VOID*
                </fo:block>
                <fo:block text-align="right" line-height="11pt" font-family="Helvetica" font-size="8pt">
                    This is not a check
                </fo:block>
                <fo:block text-align="right" line-height="11pt" font-family="Helvetica" font-size="8pt">
                    *VOID*VOID*VOID
                </fo:block>
            </fo:block-container>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="nonPrintedCheckTable">
        <xsl:param name="check"/>
        <xsl:variable name="paytoinfo" select="$check/paytoinfo[last()]"/>
        <xsl:variable name="headers" select="$check/headers[last()]"/>
        <fo:block-container height="2.7in" width="7.5in" top="0.45in" left="0.12in" position="absolute">
            <fo:block font-family="Helvetica" font-size="10pt" text-align="right">
                <fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">
                    <fo:table-column column-width="1.7in"/>
                    <fo:table-column column-width="5.4in"/>
                    <fo:table-header font-family="Helvetica" font-size="8pt" font-weight="bold">
                        <fo:table-row background-color="#BBBBBB" line-height="6pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$headers/@COL1"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$headers/@COL2"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-header>
                    <fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
                        <fo:table-row>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$paytoinfo/@printas"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$paytoinfo/@street1"/>
                                </fo:block>
                                <fo:block text-align="start">
                                    <xsl:value-of select="$paytoinfo/@street2"/>
                                </fo:block>
                                <fo:block text-align="start">
                                    <xsl:value-of select="$paytoinfo/@cityStateZip"/>
                                </fo:block>
                                <fo:block text-align="start">
                                    <xsl:value-of select="$paytoinfo/@phone"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">
                    <fo:table-column column-width="0.7in"/>
                    <fo:table-column column-width="1.0in"/>
                    <fo:table-column column-width="0.9in"/>
                    <fo:table-column column-width="1.5in"/>
                    <fo:table-column column-width="3.0in"/>
                    <fo:table-header font-family="Helvetica" font-size="8pt" font-weight="bold">
                        <fo:table-row background-color="#BBBBBB" line-height="6pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$headers/@COL3"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$headers/@COL4"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="end">
                                    <xsl:value-of select="$headers/@COL5"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="'Reference'"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$headers/@COL7"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-header>
                    <fo:table-body font-family="Helvetica" font-weight="normal" font-size="8pt">
                        <fo:table-row>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$check/@date"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$check/@paymenttype"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="end">
                                    <xsl:value-of select="$check/@currencySymbol"/><xsl:text> </xsl:text><xsl:value-of select="$check/@amount"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">

                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                    <xsl:value-of select="$check/@memo"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="printCompanyInfo">
        <xsl:param name="check"/>
        <xsl:variable name="payformanceOffsetY">
            <xsl:choose>
                <xsl:when test="$check/@format = 'B'">0.20</xsl:when>
                <xsl:otherwise>	0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="payformanceOffsetX">
            0
        </xsl:variable>
        <xsl:variable name="leftPrePrinted">
            <xsl:choose>
                <xsl:when test="$check/bankinfo/@printon = 'P'">0.6</xsl:when>
                <xsl:otherwise>	0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="spaceForLogo">
            <xsl:choose>
                <xsl:when test="($check/@cnyLogoFile != '') and ($check/bankinfo/@printon = 'P') ">0.15</xsl:when>
                <xsl:when test="($check/@cnyLogoFile != '') and ($check/bankinfo/@printon != 'P') ">0.75</xsl:when>
                <xsl:otherwise>0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:if test="$check/@cnyLogoFile != '' ">
            <fo:external-graphic src="{$check/@cnyLogoFile}">
            </fo:external-graphic>
        </xsl:if>
        <fo:block-container height="0.7in" width="4.0in" top="{$companyAddressY - $payformanceOffsetY}in" left="{$leftPrePrinted + $companyAddressX + $payformanceOffsetX + $spaceForLogo}in" position="absolute">
            <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="14pt">
                <xsl:value-of select="$check/firminfo/@name"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">
                <xsl:value-of select="$check/firminfo/@street1"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">
                <xsl:value-of select="$check/firminfo/@street2"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">
                <xsl:value-of select="$check/firminfo/@cityStateZip"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal">
                <xsl:value-of select="$check/firminfo/@phone"/>
            </fo:block>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="printPayToInfo">
        <xsl:param name="check"/>
        <xsl:variable name="payformanceOffsetY">
            <xsl:choose>
                <xsl:when test="$check/@format = 'B'">0.15</xsl:when>
                <xsl:when test="$check/@printformat = 'H'">0.19</xsl:when>
                <xsl:when test="($check/bankinfo/@printon = 'P' and $checksperpage = '3')">0.19</xsl:when>
                <xsl:otherwise>0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <!-- JohnG moved vendor printas lines to the left by changing left="0.85in" to left={$x2} so that the start of Vendor
        details is in the same starting position as the PAY amount details above it. Also changed paytoinfo font sizes from 8 to 10pt-->
        <fo:block-container height="1.3in" width="4.0in" top="{1.66 - $payformanceOffsetY}in" left="{$x2}" position="absolute">
            <fo:block text-align="start" font-family="Helvetica" font-size="10pt">
                <xsl:value-of select="$check/paytoinfo/@printas"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="10pt">
                <xsl:value-of select="$check/paytoinfo/@street1"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="10pt">
                <xsl:value-of select="$check/paytoinfo/@street2"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="10pt">
                <xsl:value-of select="$check/paytoinfo/@cityStateZip"/>
            </fo:block>
            <!-- JohnG If vendor's country = Canada then it is a domestic check and for mailing purposes
            you should not put the country code when mailing a check within Canada  -->
            <xsl:choose>
                <xsl:when test="($check/paytoinfo/@country = 'Canada')"></xsl:when>
                <xsl:otherwise>
                    <fo:block text-align="start" font-family="Helvetica" font-size="10pt">
                        <xsl:value-of select="$check/paytoinfo/@country"/>
                    </fo:block>
                </xsl:otherwise>
            </xsl:choose>
            <fo:block text-align="start" font-family="Helvetica" font-size="10pt">
                <xsl:value-of select="$check/paytoinfo/@phone"/>
            </fo:block>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="last-index-of"><xsl:param name="txt"/><xsl:param name="remainder" select="$txt"/><xsl:param name="delimiter" select="' '"/><xsl:choose><xsl:when test="contains($remainder, $delimiter)"><xsl:call-template name="last-index-of"><xsl:with-param name="txt" select="$txt"/><xsl:with-param name="remainder" select="substring-after($remainder, $delimiter)"/><xsl:with-param name="delimiter" select="$delimiter"/></xsl:call-template></xsl:when><xsl:otherwise><xsl:variable name="lastIndex" select="string-length(substring($txt, 1, string-length($txt)-string-length($remainder)))+1"/><xsl:choose><xsl:when test="string-length($remainder)=0"><xsl:value-of select="string-length($txt)"/></xsl:when><xsl:when test="$lastIndex>0"><xsl:value-of select="($lastIndex - string-length($delimiter))"/></xsl:when><xsl:otherwise><xsl:value-of select="0"/></xsl:otherwise></xsl:choose></xsl:otherwise></xsl:choose></xsl:template><xsl:template name="printAmountInfo">
    <xsl:param name="check"/>
    <!-- payformance requires an offset as their printing options seem to be different -->
    <xsl:variable name="payformanceOffsetY">
        <xsl:choose>
            <xsl:when test="$check/@format = 'B'">0.25</xsl:when>
            <xsl:otherwise>0</xsl:otherwise>
        </xsl:choose>
    </xsl:variable>
    <xsl:variable name="leftForCurrency">
        <xsl:choose>
            <xsl:when test="$check/bankinfo/@printon = 'P'">0.2 in</xsl:when>
            <xsl:when test="$check/@format = 'B'">0.1</xsl:when>
            <xsl:otherwise>0.0 in</xsl:otherwise>
        </xsl:choose>
    </xsl:variable>
    <fo:block-container height="0.7in" width="2.5in" top="{0.937 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">
        <fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue"></fo:block>
    </fo:block-container>
    <fo:block-container height="0.7in" width="2.5in" top="{1.037 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">
        <fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue"></fo:block>
    </fo:block-container>
    <xsl:choose>
        <xsl:when test="string-length($check/paytoinfo/@printas) > 60">
            <xsl:variable name="lineEnd"><xsl:call-template name="last-index-of"><xsl:with-param name="txt" select="title"/><xsl:with-param name="delimiter" select="' '"></xsl:with-param></xsl:call-template></xsl:variable><fo:block-container height="0.4in" width="5.0in" top="{0.85 - $payformanceOffsetY}in" left="{$x2}" position="absolute">
            <fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" hyphenate="true" language="en">
                <xsl:value-of select="substring($check/paytoinfo/@printas, 1, $lineEnd)"/>
            </fo:block>
            <fo:block text-align="start" font-family="Helvetica" font-size="8pt" font-weight="normal" language="en">
                <xsl:value-of select="substring($check/paytoinfo/@printas, $lineEnd+1)"/>
            </fo:block>
        </fo:block-container>
        </xsl:when>
        <xsl:otherwise>
            <fo:block-container height="0.2in" width="5.0in" top="{0.90 - $payformanceOffsetY}in" left="{$x2}" position="absolute">
                <fo:block text-align="start" font-family="Helvetica" font-size="11pt" font-weight="normal" hyphenate="true" language="en">

                </fo:block>
            </fo:block-container>
        </xsl:otherwise>
    </xsl:choose>
    <!-- JohnG Added margin-left value to all entries below to move PAY and TO THE ORDER OF more to the left by -0.33 -->
    <fo:block-container height="0.7in" width="5.5in" top="{1.10 - $payformanceOffsetY}in" left="{$x2 - .2}" position="absolute">
        <fo:block text-align="start" line-height="10pt" font-family="Helvetica" font-size="10pt" margin-left="-0.33in" >
            PAY
        </fo:block>
    </fo:block-container> <!-- JohnG increased all $payformanceOffsetY font sizes from 8 to 10pt -->
    <fo:block-container height="1.3in" width="4.0in" top="{1.65 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">
        <fo:block text-align="start" font-family="Helvetica" font-size="10pt" margin-left="-0.33in">TO</fo:block>
    </fo:block-container>
    <fo:block-container height="1.3in" width="4.0in" top="{1.78 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">
        <fo:block text-align="start" font-family="Helvetica" font-size="10pt" margin-left="-0.33in">THE</fo:block>
    </fo:block-container>
    <fo:block-container height="1.3in" width="4.0in" top="{1.91 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">
        <fo:block text-align="start" font-family="Helvetica" font-size="10pt" margin-left="-0.33in">ORDER</fo:block>
    </fo:block-container>
    <fo:block-container height="1.3in" width="4.0in" top="{2.04 - $payformanceOffsetY}in" left="{$PayToX}in" position="absolute">
        <fo:block text-align="start" font-family="Helvetica" font-size="10pt" margin-left="-0.33in">OF</fo:block>
    </fo:block-container>
    <fo:block-container height="0.7in" width="5.5in" top="{1.10 - $payformanceOffsetY}in" left="{$x2}" position="absolute">
        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
            ***<xsl:value-of select="$check/@amounttxt"/>
        </fo:block>
    </fo:block-container>
    <!--
    <fo:block-container height="0.7in" width="2.5in" top="{0.937}in" left="{$PayToX}in" position="absolute">
    <fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">Pay To</fo:block>
    </fo:block-container>
    <fo:block-container height="0.7in" width="2.5in" top="{1.037}in" left="{$PayToX}in" position="absolute">
    <fo:block text-align="start" font-family="Helvetica" font-size="7pt" color="blue">The Order Of</fo:block>
    </fo:block-container>
    -->
    <fo:block-container height="0.8in" width="3.5in" top="{1.10 - $payformanceOffsetY}in" left="{6.6}in" position="absolute">
        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">
            $**<xsl:value-of select="$check/@amount"/>
        </fo:block>
    </fo:block-container>
    <fo:block-container height="0.8in" width="3.5in" top="{1.15 - $payformanceOffsetY + 0.25}in" left="{$CheckNumX}" position="absolute">
        <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="8pt">
            <xsl:value-of select="$check/@USDText"/>
        </fo:block>
    </fo:block-container>
</xsl:template>
    <xsl:template name="printBankInfo">
        <xsl:param name="check"/>
        <xsl:variable name="payformanceOffsetY">
            <xsl:choose>
                <xsl:when test="$check/@format = 'B'">0.15</xsl:when>
                <xsl:otherwise>	0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:block-container height="0.8in" width="3.5in" top="{$BankAddressY - $payformanceOffsetY}in" left="{$BankAddressX}" position="absolute">
            <fo:block text-align="start" line-height="10pt" font-family="Helvetica" font-size="9pt">
                <xsl:value-of select="$check/bankinfo/@name"/>
            </fo:block>
            <fo:block text-align="start" line-height="8pt" font-family="Helvetica" font-size="7pt">
                <xsl:value-of select="$check/bankinfo/@street1"/>
            </fo:block>
            <fo:block text-align="start" line-height="8pt" font-family="Helvetica" font-size="7pt">
                <xsl:value-of select="$check/bankinfo/@street2"/>
            </fo:block>
            <fo:block text-align="start" line-height="8pt" font-family="Helvetica" font-size="7pt">
                <xsl:value-of select="$check/bankinfo/@cityStateZip"/>
            </fo:block>
            <fo:block text-align="start" line-height="9pt" font-family="Helvetica" font-size="8pt">
                <xsl:value-of select="$check/@bankcode"/>
            </fo:block>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="printSignature">
        <xsl:param name="check"/>
        <xsl:if test="$check/@nonnegotiable = 'T'">
            <xsl:variable name="width" select="3.0"/>

            <fo:block-container height="1.0in" width="{$width}in" top="1.75in" left="{$pgwidth - $width}in" position="absolute">
                <fo:block font-family="TimesNewRoman" font-size="18pt" font-weight="bold" text-align="left">
                    <xsl:text>Non-negotiable</xsl:text>
                </fo:block>
            </fo:block-container>
        </xsl:if>
        <xsl:if test="($check/@twosignatures = 'T' and $check/@demo != 'T' and $check/@hidesigninfo != 'T')">
            <xsl:if test="($check/bankinfo/@usesecondsignature = 'T')">

                <!-- JohnG Moved both signature lines down the page. Original was top=1.51
                Original height=0.42in was overlapping with sig1 line so I slightly shortened both -->
                <!-- JohnG Added PER in front of sig underscore line -->
                <fo:block-container height="0.2in" width="3.0in" top="1.86in" left="4.76in" position="absolute">
                    <fo:block text-align="start" line-height="9pt" font-family="Helvetica" font-size="8pt">PER</fo:block>
                </fo:block-container> <!-- JohnG below is the signature -->
                <fo:block-container height="0.42in" width="2.75in" top="1.76in" left="5.0in" position="absolute">
                    <fo:table>
                        <fo:table-column column-width="2.75in"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block text-align="center">
                                        <xsl:if test="($check/@signature2 != '')">
                                            <fo:external-graphic src="{$check/@signature2}">
                                                <xsl:if test="$check/@sigWidth2 != '' and $check/@sigHeight2 != ''">
                                                    <xsl:attribute name="width">
                                                        <xsl:value-of select="$check/@sigWidth2"/>
                                                    </xsl:attribute>
                                                    <xsl:attribute name="height">
                                                        <xsl:value-of select="$check/@sigHeight2"/>
                                                    </xsl:attribute>
                                                </xsl:if>
                                            </fo:external-graphic>
                                        </xsl:if>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block-container>
            </xsl:if>
            <!-- JohnG PER underscore line beneath sig2. Originally top=1.73in -->
            <fo:block-container height="0.2in" width="2.7in" top="1.86in" left="5.1in" position="absolute">
                <fo:block>
                    <xsl:if test="($check/bankinfo/@usesecondsignature = 'T' and $check/@printformat != 'H')">
                        <fo:leader leader-pattern="rule" rule-thickness="0.004in"/> <!--JohnG enabled PER underscore line -->
                    </xsl:if>
                </fo:block>
            </fo:block-container>
        </xsl:if>
        <xsl:if test="$check/@hidesigninfo != 'T'">
            <!-- JohnG Moved both signature lines down but they overlapped so just like signature2 above I increased.
            Original was top=1.93 and height=0.42in.-->
            <!-- JohnG Added PER in front of sig underscore line -->
            <fo:block-container height="0.18in" width="3.0in" top="2.2in" left="4.76in" position="absolute">
                <fo:block text-align="start" line-height="9pt" font-family="Helvetica" font-size="8pt">PER</fo:block>
            </fo:block-container> <!-- JohnG below is the signature -->
            <fo:block-container height="0.42in" width="2.75in" top="2.1in" left="5.0in" position="absolute">
                <fo:table>
                    <fo:table-column column-width="2.75in"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block text-align="center">
                                    <xsl:choose>
                                        <xsl:when test="$check/@demo != 'T'">
                                            <xsl:if test="($check/@signature1 != '')">
                                                <fo:external-graphic src="{$check/@signature1}">
                                                    <xsl:if test="$check/@sigWidth1 != '' and $check/@sigHeight1 != ''">
                                                        <xsl:attribute name="width">
                                                            <xsl:value-of select="$check/@sigWidth1"/>
                                                        </xsl:attribute>
                                                        <xsl:attribute name="height">
                                                            <xsl:value-of select="$check/@sigHeight1"/>
                                                        </xsl:attribute>
                                                    </xsl:if>
                                                </fo:external-graphic>
                                            </xsl:if>
                                        </xsl:when>
                                        <xsl:otherwise>
                                            <xsl:text>NOT-NEGOTIABLE</xsl:text>
                                        </xsl:otherwise>
                                    </xsl:choose>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
            <!-- JohnG moved PER underscore line beneath sig1. Originally top=2.15in. It is in the same top position as word PER -->
            <fo:block-container height="0.2in" width="2.7in" top="2.2in" left="5.1in" position="absolute">
                <fo:block>
                    <xsl:if test="($check/@printformat != 'H')">

                        <fo:leader leader-pattern="rule" rule-thickness="0.004in"/> <!-- JohnG enabled PER underscore line -->
                    </xsl:if>
                </fo:block>
            </fo:block-container>
        </xsl:if>

        <fo:block-container height="0.2in" width="3.0in" top="2.32in" left="{$signatureX}" position="absolute">
            <fo:block font-family="Helvetica" font-size="9pt" text-align="center">
                <xsl:value-of select="$check/@voidtext"/>
            </fo:block>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="printCheckNumInfo">
        <xsl:param name="check"/>
        <xsl:variable name="payformanceOffsetY">
            <xsl:choose>
                <xsl:when test="$check/@format = 'B'">0.15</xsl:when>
                <xsl:otherwise>	0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:block-container height="0.8in" width="3.5in" top="{0.13 - $payformanceOffsetY}in" left="{$CheckNumX}" position="absolute">
            <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="15pt">

            </fo:block>
        </fo:block-container>
        <!-- JohnG CPA-006 says DATE must be min 8pt. so I changed check DATE in top-right from font-size=6pt to 10pt. I also
        changed left=5.8 to 5.6in to move DATE and Date numbers back slightly. I changed top offset from 0.33 to 0.58 (quarter-inch more)
        in all the 3 areas below that start with top="{0.58 - $payformanceOffsetY}-->
        <fo:block-container height="0.9in" width="3.5in" top="{0.58 - $payformanceOffsetY}in" left="5.6in" position="absolute">
            <fo:block text-align="start" line-height="11pt" font-family="Helvetica" font-size="10pt">DATE</fo:block>
        </fo:block-container>
        <fo:block-container height="0.8in" width="3.5in" top="{0.58 - $payformanceOffsetY}in" left="6.214in" position="absolute">
            <fo:block text-align="justify" line-height="11pt" font-family="Helvetica" font-size="10pt" letter-spacing="1.86pt"><xsl:value-of select="$check/@date"/>
            </fo:block>

        </fo:block-container>
        <!-- JohnG This is for the letters beneath the Date numbers (YYYYMMDD). Original value was left=6.219in and because of font-size
        difference with the Date numbers it caused the letters to be misaligned -->
        <fo:block-container height="0.8in" width="3.5in" top="{0.58 - $payformanceOffsetY + 0.13}in" left="6.2147in" position="absolute" >
            <fo:block text-align="justify" line-height="11pt" font-family="Helvetica" font-size="6pt" letter-spacing="7.9pt"><xsl:value-of select="$cadDateTag"/>
            </fo:block>
        </fo:block-container>

    </xsl:template>
    <xsl:template name="printMemoInfo">
        <xsl:param name="check"/>
        <xsl:variable name="payformanceOffsetY">
            <xsl:choose>
                <xsl:when test="$check/@format = 'B'">0.15</xsl:when>
                <xsl:otherwise>	0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="preprintedMemo">
            <xsl:choose>
                <xsl:when test="$check/@printformat = 'H'">2.238</xsl:when>
                <xsl:when test="($check/bankinfo/@printon = 'P' and $checksperpage = '3')">2.238</xsl:when>
                <xsl:otherwise>2.5</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <!-- JohnG Original Memo line prints in top middle of cheque instead of botttom left. Moved to print underneath vendor address lines in bottom left-side of check
        top was 2.4in from Emmanuel but still printed too low on Bottom-style checks. I also took out left={$vendorAddressX} and moved it so it is flush left
        with the PAY and TO THE ORDER OF lines. Original values <fo:block-container height="0.8in" width="5.5in" top="{$MemoY - $payformanceOffsetY}in" left="{$MemoX}" position="absolute"> -->
        <xsl:if test="$check/@memo != ''">
            <fo:block-container height="0.8in" width="5.5in" top="2.35in" margin-left="-0.33in" position="absolute">
                <fo:block text-align="start" font-family="Helvetica" font-size="9pt">
                    MEMO  <xsl:value-of select="substring($check/@memo, 1, 50)"/>
                </fo:block>
            </fo:block-container>
        </xsl:if>
    </xsl:template>
    <!-- JohnG Memo underscore block is a couple of spaces to the right of the word MEMO. Here if someone asks for it.
    <fo:block-container height="0.2in" width="3.5in" top="2.35in" margin-left="0.10in" position="absolute">
    <fo:block>
    <fo:leader leader-pattern="rule" rule-thickness="0.004in"/> JohnG This is the underscore line after word MEMO
    </fo:block>
    </fo:block-container>
    </xsl:if>
    </xsl:template>
    -->
    <xsl:template name="printMICRLine">
        <!-- MICR SECTION -->
        <!--  Print the Check Number, Routing  Number and the Account Number in MICR fonts -->
        <!-- payformance requires an offset as their printing options seem to be different -->
        <xsl:param name="check"/>
        <xsl:variable name="micrAcctOffetX">
            <xsl:value-of select="(string-length($check/@accountnoMICR)+2) * 0.125"/>
        </xsl:variable>
        <xsl:variable name="checknumOffsetX">
            <xsl:value-of select="string-length($check/@checkMICR) * 0.125"/>
        </xsl:variable>
        <xsl:variable name="micrSpacePadding">
            <xsl:value-of select="$check/@MICRSpacePadding * 0.125"/>
        </xsl:variable>
        <xsl:variable name="micrONUSMovFor31">
            <xsl:choose>
                <xsl:when test="($check/@ONUS = 'true') and ($check/@ONUS32 = 'Position 31')">
                    <xsl:value-of select="2 * 0.125"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="0"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="micrONUSMovFor32">
            <xsl:choose>
                <xsl:when test="($check/@ONUS = 'true') and ($check/@ONUS32 = 'Position 32')">
                    <xsl:value-of select="1 * 0.125"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="0"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="micrONUSSymPos">
            <xsl:choose>
                <xsl:when test="($check/@ONUS32 = 'Position 31')">
                    <xsl:value-of select="3.665"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="3.54"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="micrAcctNumX">
            <xsl:choose>
                <!-- its 10 digits including the 2 encapsulating chars -->
                <xsl:when test="$check/@bankAcctMICRAlign = 'left'">
                    <xsl:value-of select="$micrAcctNumLeftX + $micrSpacePadding + $micrONUSMovFor31 + $micrONUSMovFor32 "/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="($micrAcctNumRightX - $micrAcctOffetX - $micrSpacePadding) + ($micrONUSMovFor31 + $micrONUSMovFor32)"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:block-container height="0.8in" width="2.0in" top="{$micrY}in" left="{$micrCheckNumX - $checknumOffsetX}in" position="absolute">
            <fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt">
                <xsl:value-of select="$check/@checkMICR"/>
            </fo:block>
        </fo:block-container>
        <!-- routing no.-->
        <fo:block-container height="0.8in" width="1.4in" top="{$micrY}in" left="{$micrRoutingNumX}in" position="absolute">
            <fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">
                <xsl:value-of select="$check/@routingnoMICR"/>
            </fo:block>
        </fo:block-container>
        <!-- account no.-->
        <fo:block-container height="0.8in" width="2.5in" top="{$micrY}in" left="{$micrAcctNumX}in" position="absolute">
            <fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">
                <xsl:value-of select="$check/@accountnoMICR"/>
            </fo:block>
        </fo:block-container>
        <fo:block-container height="0.8in" width="2.5in" top="{$micrY}in" left="5.675in" position="absolute">
            <fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" whitespace-collapse="false">
                <xsl:value-of select="$check/@MICRTransCode"/>
            </fo:block>
        </fo:block-container>
        <xsl:if test="$check/@ONUS = 'true'">
            <fo:block-container height="0.8in" width="2.5in" top="{$micrY}in" left="{$micrONUSSymPos}in" position="absolute">
                <fo:block text-align="start" line-height="11pt" font-family="GnuMICR" font-size="12pt" white-space-collapse="false">
                    <xsl:value-of select="$check/@ONUSSYM"/>
                </fo:block>
            </fo:block-container>
        </xsl:if>
        <!-- END MICR SECTION -->
    </xsl:template>
    <xsl:template name="invoiceStub">
        <xsl:param name="currPage"/>
        <xsl:param name="lastPageIndex"/>
        <xsl:param name="check"/>
        <xsl:param name="top"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <xsl:variable name="PrintAsText">
            <xsl:choose>
                <xsl:when test="$check/@locale='MX'">
                    Nombre de la Impresion :
                </xsl:when>
                <xsl:otherwise>
                    Print As:
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="InvoiceDateText">
            <xsl:choose>
                <xsl:when test="$check/@locale='MX'">
                    Fecha:
                </xsl:when>
                <xsl:otherwise>
                    Date:
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:block-container height="4.25in" width="8.0in" top="{$top}" left="-0.3in" position="absolute">
            <fo:block-container height="0.25in" width="8.0in" top="0.09in" left="-0.25in" position="absolute">
                <fo:block font-family="Helvetica" font-size="11pt" text-align="right">
                    <!-- JohnG changed the next 2 tops from 0.15 to 0.5in. These settings
                    are where the VendorID-Name and Vendor Address sections start printing on the Vendor stub area. The first stub -->
                </fo:block>
            </fo:block-container>
            <fo:block-container height="0.5in" width="3.5in" top="0.5in" left="0.1in" position="absolute">
                <fo:block font-family="Helvetica" font-size="10pt" text-align="left">
                    <xsl:value-of select="$check/firminfo/@name"/>
                </fo:block>
                <fo:block font-family="Helvetica" font-size="9pt" text-align="left">
                    <xsl:value-of select="$check/paytoinfo/@id"/>--<xsl:value-of select="substring($check/paytoinfo/@name,0, 35)"/>
                </fo:block>
                <fo:block font-family="Helvetica" font-size="8pt" text-align="left">

                </fo:block>
            </fo:block-container>
            <fo:block-container height="1.5in" width="3.5in" top="0.5in" left="3.8in" position="absolute">
                <fo:block text-align="start" font-family="Helvetica" font-size="8pt">
                    <xsl:value-of select="$check/paytoinfo/@street1"/>
                </fo:block>
                <fo:block text-align="start" font-family="Helvetica" font-size="8pt">
                    <xsl:value-of select="$check/paytoinfo/@street2"/>
                </fo:block>
                <fo:block text-align="start" font-family="Helvetica" font-size="8pt">
                    <xsl:value-of select="$check/paytoinfo/@cityStateZip"/>
                </fo:block>
            </fo:block-container>
            <!-- JohnG changed top from 0.22 to 0.54 because I moved the vendor and vendor address lines down to .5 from .15
            This is where the Bank name prints at the start of the first stub on the vendor stub area -->
            <fo:block-container height="0.5in" width="2.5in" top="0.54in" left="5.25in" position="absolute">
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right">
                    <xsl:value-of select="$check/bankinfo/@name"/>
                </fo:block>
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right" white-space-collapse="false">
                    <xsl:value-of select="$check/@bankacctid"/>
                </fo:block>
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right">
                    <!-- JohnG This is the Date and Date numbers in bottom right of Vendor stub details section. The first stub section -->
                    Date: <xsl:value-of select="$check/@date"/>
                </fo:block>
                <!-- JohnG This is for the letters beneath the Date numbers (YYYYMMDD). Modified from check DATE top-right corner
                <fo:block-container height="0.8in" width="3.5in" top="0.75in" left="7.05in" position="absolute" > -->
                <fo:block text-align="justify" text-indent="1.8in" font-family="Helvetica" font-size="6pt" letter-spacing="2.3pt"><xsl:value-of select="$cadDateTag"/>
                </fo:block>
            </fo:block-container>
            <!-- JohnG changed top from 0.6 to .95in. This is the spacing from the end of vendor address line and the beginning of the
            vendor stub details block (first stub section) that begins with a grey header bar -->
            <fo:block-container height="2.7in" width="7.5in" top="1.0in" left="0.12in" position="absolute">
                <fo:block font-family="Helvetica" font-size="10pt" text-align="right">
                    <xsl:variable name="lastPageForInvoice">
                        <xsl:value-of select="ceiling( count($check/invoicetable/invoice) div $noofLines)"/>
                    </xsl:variable>
                    <xsl:if test="$currPage &lt; $lastPageForInvoice or $currPage = $lastPageForInvoice">
                        <fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">
                            <xsl:call-template name="invoiceTable">
                                <xsl:with-param name="currPage" select="$currPage"/>
                                <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                                <xsl:with-param name="check" select="$check"/>
                                <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                                <xsl:with-param name="noofLines" select="$noofLines"/>
                            </xsl:call-template>
                        </fo:table>
                    </xsl:if>
                </fo:block>
            </fo:block-container>
            <!-- JohnG moved Page x of y down. Original was top=3.05in. Also on line 1219 Last top=3.25 width=2.7 -->
            <fo:block-container height="0.2in" width="2.7in" top="3.3in" left="5.0in" position="absolute">
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right">
                    Page <xsl:value-of select="$currPage"/>  of <xsl:value-of select="$lastPageIndex"/>
                </fo:block>
            </fo:block-container>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="invoiceStubWithDetails">
        <xsl:param name="currPage"/>
        <xsl:param name="lastPageIndex"/>
        <xsl:param name="check"/>
        <xsl:param name="top"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <xsl:variable name="DateText">
            <xsl:choose>
                <xsl:when test="$check/@locale='MX'">
                    Fecha:
                </xsl:when>
                <xsl:otherwise>
                    Date:
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="PrintAsText">
            <xsl:choose>
                <xsl:when test="$check/@locale='MX'">
                    Nombre de la Impresion :
                </xsl:when>
                <xsl:otherwise>
                    Print As:
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:block-container height="3.25in" width="8.0in" top="{$top}" left="-0.3in" position="absolute">
            <fo:block-container height="0.25in" width="8.0in" top="0.09in" left="-0.25in" position="absolute">
                <fo:block font-family="Helvetica" font-size="11pt" text-align="right">

                </fo:block>
            </fo:block-container>
            <!-- JohnG changed the next 2 tops from 0.15 to 0.4in. These settings are where the VendorID-Name and
            Vendor Address sections start printing on the Check stub area. The second stub -->
            <fo:block-container height="0.5in" width="3.5in" top="0.4in" left="0.1in" position="absolute">
                <fo:block font-family="Helvetica" font-size="10pt" text-align="left">
                    <xsl:value-of select="$check/firminfo/@name"/>
                </fo:block>
                <fo:block font-family="Helvetica" font-size="9pt" text-align="left">
                    <xsl:value-of select="$check/paytoinfo/@id"/>--<xsl:value-of select="substring($check/paytoinfo/@name, 0, 35)"/>
                </fo:block>
                <fo:block font-family="Helvetica" font-size="8pt" text-align="left">

                </fo:block>
            </fo:block-container>
            <fo:block-container height="1.5in" width="3.5in" top="0.5in" left="3.8in" position="absolute">
                <fo:block text-align="start" font-family="Helvetica" font-size="8pt">
                    <xsl:value-of select="$check/paytoinfo/@street1"/>
                </fo:block>
                <fo:block text-align="start" font-family="Helvetica" font-size="8pt">
                    <xsl:value-of select="$check/paytoinfo/@street2"/>
                </fo:block>
                <fo:block text-align="start" font-family="Helvetica" font-size="8pt">
                    <xsl:value-of select="$check/paytoinfo/@cityStateZip"/>
                </fo:block>
            </fo:block-container>
            <!-- JohnG changed top from 0.22 to 0.54 because I moved the vendor and vendor address lines down to 0.4
            This is where the Bank name prints at the start of the second stub on the Check stub area HEIGHT=1.0in -->
            <fo:block-container height="0.5in" width="2.5in" top="0.54in" left="5.25in" position="absolute">
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right">
                    <xsl:value-of select="$check/bankinfo/@name"/>
                </fo:block>
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right" white-space-collapse="false">
                    <xsl:value-of select="$check/@bankacctid"/>
                </fo:block>
                <!-- JohnG Date and Date numbers used in checkstub section. The second stub on the Check stub area-->
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right">
                    Date: <xsl:value-of select="$check/@date"/>
                </fo:block>
                <!-- JohnG This is for the letters beneath the Date numbers (YYYYMMDD) on second stub. Modified from check DATE top-right corner
                <fo:block-container height="0.8in" width="3.5in" top="0.75in" left="7.05in" position="absolute" > -->
                <fo:block text-align="justify" text-indent="1.8in" font-family="Helvetica" font-size="6pt" letter-spacing="2.3pt">
                    <xsl:value-of select="$cadDateTag"/>
                </fo:block>
            </fo:block-container>
            <!-- JohnG changed top from 0.6 to 0.95in. This is the spacing from the end of vendor address line and the beginning of the
            check stub details block that begins with a grey header bar -->
            <fo:block-container height="2.7in" width="7.5in" top="1.0in" left="0.12in" position="absolute">
                <fo:block font-family="Helvetica" font-size="10pt" text-align="right">
                    <fo:table table-layout="fixed" border-color="black" border-style="solid" border-width="0.0pt">
                        <xsl:call-template name="invoiceTableWithDetails">
                            <xsl:with-param name="currPage" select="$currPage"/>
                            <xsl:with-param name="lastPageIndex" select="$lastPageIndex"/>
                            <xsl:with-param name="check" select="$check"/>
                            <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                            <xsl:with-param name="noofLines" select="$noofLines"/>
                        </xsl:call-template>
                    </fo:table>
                </fo:block>
            </fo:block-container>
            <!-- JohnG Page x of y was printing too high. Original top=3.05in. Also on line 1122 Last top=3.35in -->
            <fo:block-container height="0.2in" width="2.7in" top="3.4in" left="5.0in" position="absolute">
                <fo:block font-family="Helvetica" font-size="8pt" text-align="right">
                    Page <xsl:value-of select="$currPage"/>  of <xsl:value-of select="$lastPageIndex"/>
                </fo:block>
            </fo:block-container>
        </fo:block-container>
    </xsl:template>
    <xsl:template name="invoiceTable">
        <xsl:param name="currPage"/>
        <xsl:param name="lastPageIndex"/>
        <xsl:param name="check"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <xsl:variable name="refnoColWidth">
            <xsl:choose>
                <xsl:when test="$check/@hideamtdue != 'T'">1.75</xsl:when>
                <xsl:otherwise>2.6</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="addtorefnoColWidth">
            <xsl:choose>
                <xsl:when test="$check/@displaytermdiscount = 'T'">0</xsl:when>
                <xsl:otherwise>1.0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:table-column column-width="0.6in"/>
        <xsl:choose>
            <xsl:when test="($VarMultiCurrency = 'true')  and ($check/@doingadvance = 'F')">
                <fo:table-column column-width="2.3in"/>
            </xsl:when>
            <xsl:otherwise>
                <fo:table-column column-width="2.2in"/>
            </xsl:otherwise>
        </xsl:choose>
        <fo:table-column column-width="{$refnoColWidth + $addtorefnoColWidth}in"/>
        <xsl:if test="$check/@hideamtdue != 'T'">
            <fo:table-column column-width="0.7in"/>
        </xsl:if>
        <xsl:if test="$check/@displaytermdiscount = 'T'">
            <fo:table-column column-width="1.0in"/>
        </xsl:if>
        <fo:table-column column-width="1.4in"/> <!-- JohnG original 1.2in -->
        <!-- JohnG This is stub detail for Date1 Vendor stub detail -->
        <fo:table-header font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="2pt">
            <fo:table-row background-color="#BBBBBB" line-height="2pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">
                <xsl:call-template name="printHeaders">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
            </fo:table-row>
        </fo:table-header>
        <fo:table-body font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="4.7pt">
            <xsl:call-template name="printRows">
                <xsl:with-param name="currPage" select="$currPage"/>
                <xsl:with-param name="check" select="$check"/>
                <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                <xsl:with-param name="noofLines" select="$noofLines"/>
            </xsl:call-template>
            <!-- Print the Total Line only on the Last Page -->
            <xsl:variable name="billcount" select="count($check/invoicetable/invoice)"/>
            <xsl:variable name="printtotalline">
                <xsl:choose>
                    <xsl:when test="$currPage = $lastPageIndex or (($billcount &lt; $currPage* $noofLines ) or ($billcount = $currPage* $noofLines) )">
                        <xsl:text>T</xsl:text>
                    </xsl:when>
                    <xsl:otherwise>
                        <xsl:text>F</xsl:text>
                    </xsl:otherwise>
                </xsl:choose>
            </xsl:variable>
            <xsl:variable name="tableendcolspan">
                <xsl:choose>
                    <xsl:when test="$check/@hideamtdue != 'T'">5</xsl:when>
                    <xsl:otherwise>4</xsl:otherwise>
                </xsl:choose>
            </xsl:variable>
            <xsl:variable name="addcolspan">
                <xsl:choose>
                    <xsl:when test="$check/@displaytermdiscount != 'T'">1</xsl:when>
                    <xsl:otherwise>0</xsl:otherwise>
                </xsl:choose>
            </xsl:variable>
            <xsl:choose>
                <xsl:when test="$printtotalline = 'T'">
                    <xsl:call-template name="Totals">
                        <xsl:with-param name="check" select="$check"/>
                        <xsl:with-param name="colspan" select="($tableendcolspan - $addcolspan)"/>
                    </xsl:call-template>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:call-template name="Contnd">
                        <xsl:with-param name="colspan" select="($tableendcolspan - $addcolspan)"/>
                    </xsl:call-template>
                </xsl:otherwise>
            </xsl:choose>
        </fo:table-body>
    </xsl:template>
    <xsl:template name="invoiceTableWithDetails">
        <xsl:param name="currPage"/>
        <xsl:param name="lastPageIndex"/>
        <xsl:param name="check"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <fo:table-column column-width="1.22in"/>
        <xsl:variable name="adjustlocationcollen">
            <xsl:choose>
                <xsl:when test="$check/@displaytermdiscount = 'T'">0</xsl:when>
                <xsl:otherwise>0.55</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:choose>
            <xsl:when test="($VarMultiCurrency = 'true') and ($check/@doingadvance = 'F')">
                <xsl:choose>
                    <xsl:when test="$check/@displocacctnocheck = 'T'">
                        <fo:table-column column-width="2.0in" />
                        <fo:table-column column-width=".5in" />
                    </xsl:when>
                    <xsl:otherwise>
                        <fo:table-column column-width="2.5in" />
                    </xsl:otherwise>
                </xsl:choose>
                <fo:table-column column-width="1.0in"/>
                <fo:table-column column-width="{0.9 + $adjustlocationcollen}in"/>
                <fo:table-column column-width="0.15in"/>
            </xsl:when>
            <xsl:otherwise>
                <xsl:choose>
                    <xsl:when test="$check/@displocacctnocheck = 'T'">
                        <fo:table-column column-width="2.0in" />
                        <fo:table-column column-width=".65in" />
                    </xsl:when>
                    <xsl:otherwise>
                        <fo:table-column column-width="2.65in" />
                    </xsl:otherwise>
                </xsl:choose>
                <fo:table-column column-width="0.95in"/>
                <fo:table-column column-width="{0.9 + $adjustlocationcollen}in"/>
            </xsl:otherwise>
        </xsl:choose>
        <fo:table-column column-width="0.7in"/>
        <xsl:if test="$check/@displaytermdiscount = 'T'">
            <fo:table-column column-width="0.55in"/>
        </xsl:if>
        <fo:table-column column-width="0.7in"/>
        <xsl:variable name="varColSpan">
            <xsl:choose>
                <xsl:when test="$VarMultiCurrency = 'true' and ($check/@doingadvance = 'F')">7</xsl:when>
                <xsl:otherwise>6</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="addcolspan">
            <xsl:choose>
                <xsl:when test="$check/@displaytermdiscount != 'T'">1</xsl:when>
                <xsl:otherwise>0</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <!-- Always print the Header on each page -->
        <fo:table-header font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="6pt">
            <fo:table-row background-color="#BBBBBB" line-height="5pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid">
                <xsl:call-template name="printDetailedHeadersLine1">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
            </fo:table-row>
            <fo:table-row background-color="#BBBBBB" line-height="5pt" border-bottom-width="0.01in" border-color="black" border-bottom-style="solid" font-style="italic">
                <xsl:call-template name="printDetailedHeadersLine2">
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
            </fo:table-row>
        </fo:table-header>
        <fo:table-body font-family="Helvetica" font-weight="normal" font-size="6pt" line-height="4.7pt">
            <xsl:call-template name="printRowsWithDetails">
                <xsl:with-param name="currPage" select="$currPage"/>
                <xsl:with-param name="check" select="$check"/>
                <xsl:with-param name="billsPerPage" select="$billsPerPage"/>
                <xsl:with-param name="noofLines" select="$noofLines"/>
            </xsl:call-template>
            <!-- Print the Total Line only on the Last Page -->
            <xsl:choose>
                <xsl:when test="$currPage = $lastPageIndex">
                    <xsl:call-template name="Totals">
                        <xsl:with-param name="check" select="$check"/>
                        <xsl:with-param name="colspan" select="($varColSpan - $addcolspan)"/>
                    </xsl:call-template>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:call-template name="Contnd">
                        <xsl:with-param name="colspan" select="($varColSpan - $addcolspan)"/>
                    </xsl:call-template>
                </xsl:otherwise>
            </xsl:choose>
        </fo:table-body>
    </xsl:template>
    <xsl:template name="printHeaders">
        <xsl:param name="check"/>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/headers/@COL1"/>
            </fo:block>
        </fo:table-cell>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/headers/@COL2"/>
            </fo:block>
        </fo:table-cell>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="'Reference'"/>
            </fo:block>
        </fo:table-cell>
        <xsl:if test="$check/@hideamtdue != 'T'">
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="$check/invoicetable/headers/@COL3"/>
                </fo:block>
            </fo:table-cell>
        </xsl:if>
        <xsl:if test="$check/@displaytermdiscount = 'T'">
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="$check/invoicetable/headers/@COL4"/>
                </fo:block>
            </fo:table-cell>
        </xsl:if>
        <fo:table-cell padding="2pt">
            <fo:block text-align="end">
                <xsl:value-of select="$check/invoicetable/headers/@COL5"/>
            </fo:block>
        </fo:table-cell>
    </xsl:template>
    <xsl:template name="printDetailedHeadersLine1">
        <xsl:param name="check"/>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL1"/>
            </fo:block>
        </fo:table-cell>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL2"/>
            </fo:block>
        </fo:table-cell>
        <xsl:choose>
            <xsl:when test="$check/@displocacctnocheck = 'T'">
                <fo:table-cell padding="2pt">
                    <fo:block text-align="start">
                        <xsl:value-of select="$check/invoicetable/detailedheaders/@COL121"/>
                    </fo:block>
                </fo:table-cell>
            </xsl:when>
        </xsl:choose>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL10"/>
            </fo:block>
        </fo:table-cell>
    </xsl:template>
    <xsl:template name="printDetailedHeadersLine2">
        <xsl:param name="check"/>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL3"/>
            </fo:block>
        </fo:table-cell>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL4"/>
            </fo:block>
        </fo:table-cell>
        <xsl:choose>
            <xsl:when test="$check/@displocacctnocheck = 'T'">
                <fo:table-cell padding="2pt">
                    <fo:block start-indent="-5mm" text-align="start">
                        <xsl:value-of select="$check/invoicetable/detailedheaders/@COL12"/>
                    </fo:block>
                </fo:table-cell>
            </xsl:when>
            <xsl:otherwise>
            </xsl:otherwise>
        </xsl:choose>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL5"/>
            </fo:block>
        </fo:table-cell>
        <fo:table-cell padding="2pt">
            <fo:block text-align="start">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL6"/>
            </fo:block>
        </fo:table-cell>
        <xsl:if test="($VarMultiCurrency = 'true') and ($check/@doingadvance = 'F')">
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="$check/invoicetable/detailedheaders/@COL11"/>
                </fo:block>
            </fo:table-cell>
        </xsl:if>
        <fo:table-cell padding="2pt">
            <fo:block text-align="end">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL7"/>
            </fo:block>
        </fo:table-cell>
        <xsl:if test="$check/@displaytermdiscount = 'T'">
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="$check/invoicetable/detailedheaders/@COL8"/>
                </fo:block>
            </fo:table-cell>
        </xsl:if>
        <fo:table-cell padding="2pt">
            <fo:block text-align="end">
                <xsl:value-of select="$check/invoicetable/detailedheaders/@COL9"/>
            </fo:block>
        </fo:table-cell>
    </xsl:template>
    <xsl:template name="printRows">
        <xsl:param name="currPage"/>
        <xsl:param name="check"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <xsl:variable name="startindex" select="(($currPage -1) * $noofLines) + 1"/>
        <xsl:variable name="endindex">
            <xsl:choose>
                <xsl:when test="count($check/invoicetable/invoice)  &lt; ($currPage * $noofLines)">
                    <xsl:value-of select="count($check/invoicetable/invoice)"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$currPage * $noofLines"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <!--Call row Template for each of the row within the StartIndex and EndIndex -->
        <xsl:for-each select="$check/invoicetable/invoice">
            <xsl:variable name="position" select="position()"/>
            <xsl:if test="( $position &gt; $startindex or $position = $startindex) and ($position &lt; $endindex or $position = $endindex)">
                <xsl:call-template name="invoice">
                    <xsl:with-param name="invoice" select="."/>
                    <xsl:with-param name="hideamtdue" select="$check/@hideamtdue"/>
                    <xsl:with-param name="displaytermdiscount" select="$check/@displaytermdiscount"/>
                </xsl:call-template>
            </xsl:if>
        </xsl:for-each>
    </xsl:template>
    <xsl:template name="printRowsWithDetails">
        <xsl:param name="currPage"/>
        <xsl:param name="check"/>
        <xsl:param name="billsPerPage"/>
        <xsl:param name="noofLines"/>
        <xsl:variable name="startindex" select="(($currPage -1) * $noofLines) + 1"/>
        <xsl:variable name="endindex">
            <xsl:value-of select="($currPage * $noofLines)"/>
        </xsl:variable>
        <!--Call row Template for each of the row within the StartIndex and EndIndex -->
        <xsl:for-each select="$check/invoicetable/invoice/LINEITEM">
            <xsl:variable name="position" select="@ROWINDEX"/>
            <xsl:if test="( $position &gt; $startindex or $position = $startindex ) and ($position &lt; $endindex or $position = $endindex)">
                <xsl:call-template name="invoiceWithDetails">
                    <xsl:with-param name="invoice" select="ancestor::*"/>
                    <xsl:with-param name="lineitem" select="."/>
                    <xsl:with-param name="doingadvance" select="$check/@doingadvance"/>
                    <xsl:with-param name="displaytermdiscount" select="$check/@displaytermdiscount"/>
                    <xsl:with-param name="check" select="$check"/>
                </xsl:call-template>
            </xsl:if>
        </xsl:for-each>
    </xsl:template>
    <xsl:template name="invoice">
        <xsl:param name="invoice"/>
        <xsl:param name="hideamtdue"/>
        <xsl:param name="displaytermdiscount"/>
        <xsl:variable name="refnolen" select="string-length($invoice/@BILLREFNO)"/>
        <xsl:variable name="addtorefnolen">
            <xsl:choose>
                <xsl:when test="$displaytermdiscount = 'T'">0</xsl:when>
                <xsl:otherwise>7</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="refno">
            <xsl:choose>
                <xsl:when test="$invoice/@BILLREFNO and ($refnolen > (21 + $addtorefnolen))">
                    <xsl:value-of select="concat(substring($invoice/@BILLREFNO, 1, 10), '...', substring($invoice/@BILLREFNO, $refnolen - 9, 10))"/>
                </xsl:when>
                <xsl:otherwise><xsl:value-of select="$invoice/@BILLREFNO"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <fo:table-row>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="$invoice/@DATE"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start" >
                    <xsl:value-of select="$invoice/@DESCRPTION"/>
                </fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="start">
                    <xsl:value-of select="$refno"/>
                </fo:block>
            </fo:table-cell>
            <xsl:if test="$hideamtdue != 'T'">
                <fo:table-cell padding="2pt">
                    <fo:block text-align="end">
                        <xsl:value-of select="$invoice/@AMOUNTDUE"/>
                    </fo:block>
                </fo:table-cell>
            </xsl:if>
            <xsl:if test="$displaytermdiscount = 'T'">
                <fo:table-cell padding="2pt">
                    <fo:block text-align="end">
                        <xsl:if test="($invoice/@DISCOUNT) and not ($invoice/@CREDIT)">
                            <xsl:value-of select="$invoice/@DISCOUNT"/>
                        </xsl:if>
                        <xsl:if test="($invoice/@CREDIT) and not ($invoice/@DISCOUNT)">
                            <xsl:value-of select="$invoice/@CREDIT"/>
                        </xsl:if>
                    </fo:block>
                </fo:table-cell>
            </xsl:if>
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="$invoice/@AMOUNTPAID"/>
                </fo:block>
            </fo:table-cell>
        </fo:table-row>
    </xsl:template>
    <xsl:template name="invoiceWithDetails">
        <xsl:param name="invoice"/>
        <xsl:param name="lineitem"/>
        <xsl:param name="check"/>
        <xsl:param name="doingadvance"/>
        <xsl:param name="displaytermdiscount"/>
        <xsl:variable name="lastLineItemIndex">
            <xsl:value-of select="count($invoice/LINEITEM)"/>
        </xsl:variable>
        <xsl:variable name="refnolen" select="string-length($invoice/@BILLREFNO)"/>
        <xsl:variable name="memolen" select="string-length($lineitem/@LINEITEMDESCR)"/>
        <xsl:variable name="deptlen" select="string-length($lineitem/@DEPT)"/>
        <xsl:variable name="loclen" select="string-length($lineitem/@LOCATION)"/>
        <xsl:variable name="loclenseperator">
            <xsl:choose>
                <xsl:when test="($VarMultiCurrency = 'true')">10</xsl:when>
                <xsl:otherwise>18</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="frbklen">
            <xsl:choose>
                <xsl:when test="($VarMultiCurrency = 'true' and $displaytermdiscount = 'T')">4</xsl:when>
                <xsl:otherwise>8</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="addtorefnolen">
            <xsl:choose>
                <xsl:when test="$displaytermdiscount = 'T'">0</xsl:when>
                <xsl:otherwise>10</xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="refno">
            <xsl:choose>
                <xsl:when test="$invoice/@BILLREFNO and ($refnolen > (23 + $addtorefnolen))">
                    <xsl:value-of select="concat(substring($invoice/@BILLREFNO, 1, 10), '...', substring($invoice/@BILLREFNO, $refnolen - 9, 10))"/>
                </xsl:when>
                <xsl:otherwise><xsl:value-of select="$invoice/@BILLREFNO"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="memo">
            <xsl:choose>
                <xsl:when test="$lineitem/@LINEITEMDESCR and ($memolen > 45)">
                    <xsl:value-of select="concat(substring($lineitem/@LINEITEMDESCR, 1, 21), '...', substring($lineitem/@LINEITEMDESCR, $memolen - 20, 21))"/>
                </xsl:when>
                <xsl:otherwise><xsl:value-of select="$lineitem/@LINEITEMDESCR"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="dept">
            <xsl:choose>
                <xsl:when test="$lineitem/@DEPT and ($deptlen > 15)">
                    <xsl:value-of select="concat(substring($lineitem/@DEPT, 1, 6), '...', substring($lineitem/@DEPT, $deptlen - 5, 6))"/>
                </xsl:when>
                <xsl:otherwise><xsl:value-of select="$lineitem/@DEPT"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <xsl:variable name="location">
            <xsl:choose>
                <xsl:when test="$lineitem/@LOCATION and ($loclen > ($loclenseperator + $addtorefnolen))">
                    <xsl:value-of select="concat(substring($lineitem/@LOCATION, 1, $frbklen), '...', substring($lineitem/@LOCATION, ($loclen - $frbklen + 1), $frbklen))"/>
                </xsl:when>
                <xsl:otherwise>
                    <xsl:value-of select="$lineitem/@LOCATION"/>
                </xsl:otherwise>
            </xsl:choose>
        </xsl:variable>
        <!-- lineItemIndex = 1print Bill No and Date
        lineItemIndex = count, print Discount and Amount Paid
        -->
        <xsl:choose>
            <xsl:when test="$lineitem/@LINEITEMINDEX= 1">
                <fo:table-row>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="start">
                            <xsl:value-of select="$invoice/@DATE"/>
                        </fo:block>
                    </fo:table-cell>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="start">
                            <xsl:value-of select="$invoice/@DESCRPTION"/>
                        </fo:block>
                    </fo:table-cell>
                    <xsl:choose>
                        <xsl:when test="$check/@displocacctnocheck = 'T'">
                            <fo:table-cell padding="2pt">
                                <fo:block text-align="start">
                                </fo:block>
                            </fo:table-cell>
                        </xsl:when>
                        <xsl:otherwise>
                        </xsl:otherwise>
                    </xsl:choose>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="start">
                            <xsl:value-of select="$refno"/>
                        </fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </xsl:when>
            <xsl:otherwise>
                <fo:table-row font-style="italic">
                    <fo:table-cell padding="2pt">
                        <fo:block-container  height="0.19in" width="1.22in" border-style="null">
                            <fo:block text-align="start" hyphenate="true" language="en">
                                <xsl:value-of select="$lineitem/@ACCOUNT"/>
                            </fo:block>
                        </fo:block-container>
                    </fo:table-cell>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="start">
                            <xsl:value-of select="$memo"/>
                        </fo:block>
                    </fo:table-cell>
                    <xsl:choose>
                        <xsl:when test="$check/@displocacctnocheck = 'T'">
                            <fo:table-cell padding="2pt">
                                <fo:block start-indent="-5mm" line-height="6pt" text-align="start">
                                    <xsl:choose>
                                        <xsl:when test="string-length($lineitem/@VERSIONACCTNO) > 10">
                                            <xsl:call-template name="StrWrap">
                                                <xsl:with-param name="start" select="1"/>
                                                <xsl:with-param name="len" select="string-length($lineitem/@VERSIONACCTNO)"/>
                                                <xsl:with-param name="field" select="$lineitem/@VERSIONACCTNO"/>
                                                <xsl:with-param name="chars" select="10"/>
                                            </xsl:call-template>
                                        </xsl:when>
                                        <xsl:otherwise>
                                            <xsl:value-of select="$lineitem/@VERSIONACCTNO"/>
                                        </xsl:otherwise>
                                    </xsl:choose>
                                </fo:block>
                            </fo:table-cell>
                        </xsl:when>
                    </xsl:choose>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="start">
                            <xsl:value-of select="$dept"/>
                        </fo:block>
                    </fo:table-cell>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="start">
                            <xsl:call-template name="sp-replace">
                                <xsl:with-param name="printfld" select="$location"/>
                            </xsl:call-template>
                        </fo:block>
                    </fo:table-cell>
                    <xsl:if test="($VarMultiCurrency = 'true') and ($doingadvance = 'F')">
                        <fo:table-cell padding="2pt">
                            <fo:block text-align="end">
                                <xsl:value-of select="$lineitem/@CURRENCY"/>
                            </fo:block>
                        </fo:table-cell>
                    </xsl:if>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="end">
                            <xsl:value-of select="$lineitem/@AMOUNT"/>
                        </fo:block>
                    </fo:table-cell>
                    <xsl:if test="$displaytermdiscount = 'T'">
                        <fo:table-cell padding="2pt">
                            <fo:block text-align="end">
                                <xsl:value-of select="$lineitem/@DISCOUNT"/>
                            </fo:block>
                        </fo:table-cell>
                    </xsl:if>
                    <fo:table-cell padding="2pt">
                        <fo:block text-align="end">
                            <xsl:value-of select="$lineitem/@AMOUNTPAID"/>
                        </fo:block>
                    </fo:table-cell>
                </fo:table-row>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>
    <xsl:template name="text_wrapper">
        <xsl:param name="Text"/>
        <xsl:choose>
            <xsl:when test="string-length($Text)">
                <xsl:value-of select="substring($Text,1,10)"/>&#x200b;
                <xsl:call-template name="wrapper_helper">
                    <xsl:with-param name="Text" select="substring($Text,11)"/>
                </xsl:call-template>
            </xsl:when>
            <xsl:otherwise>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>
    <xsl:template name="StrWrap">
        <xsl:param name="start"/>
        <xsl:param name="len"/>
        <xsl:param name="field"/>
        <xsl:param name="chars"/>
        <xsl:if test="$len > 0">
            <xsl:call-template name="sp-replace">
                <xsl:with-param name="printfld" select="substring($field, $start, $chars)"/>
            </xsl:call-template>
            <xsl:if test="($len - $chars) > 0">
                <xsl:text></xsl:text>
            </xsl:if>
            <fo:block whitespace-collapse="false">&#10;</fo:block>
            <xsl:call-template name="StrWrap">
                <xsl:with-param name="start" select="$start + $chars"/>
                <xsl:with-param name="len" select="$len - $chars"/>
                <xsl:with-param name="field" select="$field"/>
                <xsl:with-param name="chars" select="$chars"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>
    <xsl:template name="Totals">
        <xsl:param name="check"/>
        <xsl:param name="colspan"/>
        <fo:table-row background-color="#BBBBBB" line-height="2pt">
            <fo:table-cell padding="2pt" number-columns-spanned="{$colspan}">
                <fo:block text-align="start">Net Amount:</fo:block>
            </fo:table-cell>
            <fo:table-cell padding="2pt">
                <fo:block text-align="end">
                    <xsl:value-of select="$check/invoicetable/Totals/@NetAmount"/>
                </fo:block>
            </fo:table-cell>
        </fo:table-row>
    </xsl:template>
    <xsl:template name="Contnd">
        <xsl:param name="colspan"/>
        <fo:table-row background-color="#BBBBBB" line-height="2pt">
            <fo:table-cell padding="2pt" number-columns-spanned="{$colspan+1}">
                <fo:block text-align="end">Contd.... </fo:block>
            </fo:table-cell>
        </fo:table-row>
    </xsl:template>
    <xsl:template name="sp-replace">
        <xsl:param name="printfld"/>
        <!-- NOTE: There are two spaces   ** here below -->
        <xsl:variable name="sp"><xsl:text> </xsl:text></xsl:variable>
        <xsl:choose>
            <xsl:when test="contains($printfld,$sp)">
                <xsl:value-of select="substring-before($printfld,$sp)"/>
                <xsl:text>&#160;</xsl:text>
                <xsl:call-template name="sp-replace">
                    <xsl:with-param name="printfld" select="substring-after($printfld,$sp)"/>
                </xsl:call-template>
            </xsl:when>
            <xsl:otherwise>
                <xsl:value-of select="$printfld"/>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>
</xsl:stylesheet>