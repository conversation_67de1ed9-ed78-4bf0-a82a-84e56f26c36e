<?php
/**
 *  FILE:            ardiscount.ent
 *  AUTHOR:            rpn
 *  DESCRIPTION:    entity definition for ardiscount object
 *
 *  (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *  This document contains trade secret data that belongs to Intacct
 *  Corporation and is protected by the copyright laws.  Information
 *  herein may not be used, copied or disclosed in whole or in part
 *  without prior written consent from Intacct Corporation.
*/

global $gCustomerIDFormat;

require 'prrecord.ent';

$kSchemas['ardiscount'] = array(
    'children' => array(
        'customer' => array( 'fkey' => 'customerkey', 'invfkey' => 'record#', 'table' => 'customer', 'join' => 'outer'),
    ),
    'schema' => array(
        'CUSTOMERID'    => 'customer.customerid',
        'CUSTOMERNAME'    => 'customer.name',
        'CUSTOMERKEY'    => 'customerkey',
        'SI_UUID'     => 'si_uuid',
    ),
    'fieldinfo' => array(
        array(
           'fullname' => 'IA.CUSTOMER_ID',
            'type' => array(
                'ptype' => 'text',
                'type'  => 'text',
                'maxlength' => 20,
                'format' => $gCustomerIDFormat
            ),
            'hidden'    => true,
            'desc' => 'IA.UNIQUE_ID_OF_CUSTOMER',
            'path' => 'CUSTOMERID',
            'renameable' => true,
        ),
        array(
           'fullname' => 'IA.BATCH',
            'desc' => 'IA.BATCH',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'prbatch',
                'maxlength' => 100,
                'size' => 40,
            ),
            'required' => false,
            'path' => 'PRBATCH'
        ),
        array(
            'path' => 'RECORDID',
            'fullname' => 'IA.DOCUMENT',
            'desc' => 'IA.DOCUMENT',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 30,
                'size' => 30,
            ),
        ),
        array(
            'path' => 'WHENCREATED',
            'autofill' => true,
        ),
        array(
            'path' => 'TOTALENTERED',
           'fullname' => 'IA.AMOUNT',
            'desc' => 'IA.AMOUNT',
        ),
        $gSiUuidFieldInfo,
    ),
    'module' => 'ar',
    'sicollaboration' => true,
    'dbfilters' => array(array('ardiscount.recordtype', '=', 'rd')),
    'allowDDS' => true
);

$kSchemas['ardiscount'] = EntityManager::inheritEnts($kSchemas['prrecord'], $kSchemas['ardiscount']);

$kSchemas['ardiscount']['object'][] = 'CUSTOMERID';
$kSchemas['ardiscount']['object'][] = 'CUSTOMERNAME';
$kSchemas['ardiscount']['object'][] = 'DESCRIPTION2';
$kSchemas['ardiscount']['object'][] = 'CUSTOMERKEY';
$kSchemas['ardiscount']['entitytype'] = 'customer';
$kSchemas['ardiscount']['recordtype'] = ['rd'];
