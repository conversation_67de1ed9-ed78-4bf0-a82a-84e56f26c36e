<?

/**
 *    FILE:              ARRevalFilterEditor.cls
 *    AUTHOR:            Girish <PERSON>
 *    DESCRIPTION:       A class for showing the Filter Editor (Filtering) screens for ARRevalReport
 *
 *    (C)2000, Intacct Corporation, All Rights Reserved
 *
 *    Intacct Corporation Proprietary Information.
 *    This document contains trade secret data that belongs to Intacct
 *    corporation and is protected by the copyright laws. Information herein
 *    may not be used, copied or disclosed in whole or part without prior
 *    written consent from Intacct Corporation.
 */

class ARRevalFilterEditor extends FilterEditor
{

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct($_params);
    }

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss include the YUI css files
     */
    function showScripts($addYuiCss = true)
    {
        ?>
        <script language="JavaScript" src="../resources/js/revalfiltereditor.js"></script>
        <?
        parent::showScripts($addYuiCss);

    }

    /**
     * @param array $editvalues
     * @param array $promptonrundata
     */
    function ShowTop($editvalues = array(), $promptonrundata = array())
    {
        $this->_params['onloadjs'] .= 'onLoanRevalReport();';
        parent::ShowTop($editvalues, $promptonrundata);
    }

    /**
     * DisplayControls
     *
     * @return array This function adds kDraftJE button on a Filter Editor
     */
    protected function DisplayControls()
    {
        $buttonControls = parent::DisplayControls();
        $buttonControls[kDraftJE] = [
            'status' => "Create JE",
            'image' => "",
            'alt' => GT($this->translatedTokens, "IA.CREATE_JE")
        ];
        return $buttonControls;
    }
}