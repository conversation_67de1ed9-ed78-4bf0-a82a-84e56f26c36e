<?
//=============================================================================
//
//	FILE:			GenerateBFCheck.inc
//	AUTHOR:	VRAJ
//	DESCRIPTION:
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

class GenerateUSJPMorganCheck extends GeneratePDFCheck
{
    public function __construct()
    {
        parent::__construct();
    }

    function SetDefaults()
    {
        parent::SetDefaults();
//        $this->ppMemoSCOffset = 2.22;
//        $this->ptInfoSCOffset = 0.20;
//        $this->topInvoiceStubOffset = 0.25;
    }

    /**
     * @return string
     */
    function ReadUSxsl (string $printFormat) : string {
        $ss = $stylesheet_path =  "";
        if ($printFormat === 'J' || $printFormat === 'jpmorgan chase business') {
            $stylesheet_path = "../../private/xslinc/chase-buisness-stock.xsl";
        } else if ($printFormat === 'K' || $printFormat === 'jpmorgan chase standard') {
            $stylesheet_path = "../../private/xslinc/chase-blank-check-stock.xsl";
        }
        if (!empty($stylesheet_path)) {
            $stylesheet_fh = fopen($stylesheet_path, "r");
            if ($stylesheet_fh) {
                $ss = fread($stylesheet_fh, filesize($stylesheet_path));
                fclose($stylesheet_fh);
            }
        }
        return $ss;
    }

}

