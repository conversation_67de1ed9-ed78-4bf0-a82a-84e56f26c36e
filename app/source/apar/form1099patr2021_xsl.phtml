<?
$templatePayerAddress = '<xsl:template name="payer_address">
    <fo:block-container width="0.2in" left="2.6in" height="0.2in" position="absolute" top="-0.25in">
	   <xsl:variable name="valueCorrected" select="./CORRECTED"/>
        <xsl:choose>
            <xsl:when test="$valueCorrected!=\'\'">
                <fo:block line-height="0.14in"><xsl:value-of select="$valueCorrected"/></fo:block>
            </xsl:when>
            <xsl:otherwise>
                <fo:block color="white" >0</fo:block>
            </xsl:otherwise>
        </xsl:choose>
	</fo:block-container>
    <fo:block-container width="3.30in" height="0.8in" left="0in"  position="absolute" top="0.1in">
		<fo:block ><xsl:value-of select="//Company/COMPANYNAME"/></fo:block>
		<fo:block ><xsl:value-of select="//Company/ADDRESS1"/></fo:block>

		<xsl:variable name="add2" select="//Company/ADDRESS2"/>
		<xsl:choose>
			<xsl:when test="$add2!=\'\'">
				<fo:block ><xsl:value-of select="$add2"/></fo:block>
			</xsl:when>
			<xsl:otherwise>
				<fo:block  color="white">0</fo:block>
			</xsl:otherwise>
		</xsl:choose>
		<fo:block >
			<xsl:value-of select="//Company/CITY"/><xsl:text>, </xsl:text><xsl:value-of select="//Company/STATE"/><xsl:text> </xsl:text><xsl:value-of select="//Company/ZIPCODE"/>
		</fo:block>
		<xsl:variable name="cphone" select="//Company/CONTACTPHONE"/>
		<xsl:choose>
			<xsl:when test="$cphone!=\'\'"><fo:block><xsl:value-of select="$cphone"/></fo:block></xsl:when>
			<xsl:otherwise><fo:block color="white">0</fo:block></xsl:otherwise>
		</xsl:choose>
	</fo:block-container>
	</xsl:template>';

$templateIdnumbers = '<xsl:template name="idnumbers">
<fo:block-container width="3.30in" height="0.22in" left="0in"  position="absolute" top="1.05in">
		<xsl:variable name="value1" select="//Company/FEDERALID"/>
		<xsl:variable name="value2" select="TAXID"/>

		<fo:block line-height="0.2in">
			<fo:table>
				<fo:table-column column-width="1.5in"/>
				<fo:table-column column-width="1.6in"/>
				<fo:table-body>
					<fo:table-row>
						<fo:table-cell vertical-align="middle" >
							<xsl:choose>
								<xsl:when test="$value1!=\'\'">
									<fo:block text-align="center"><xsl:value-of select="//Company/FEDERALID"/></fo:block>
								</xsl:when>
								<xsl:otherwise>
									<fo:block color="white" text-align="center">blank</fo:block>
								</xsl:otherwise>
							</xsl:choose>
						</fo:table-cell>
						<fo:table-cell vertical-align="bottom" >
							<xsl:choose>
								<xsl:when test="$value2!=\'\'">
									<fo:block text-align="center"><xsl:value-of select="TAXID"/></fo:block>
								</xsl:when>
								<xsl:otherwise>
									<fo:block color="white" text-align="center">blank</fo:block>
								</xsl:otherwise>
							</xsl:choose>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
		</fo:block-container>
	</xsl:template>';

$templateVendorAddress = '<xsl:template name="vendor_address">
    <fo:block-container width="3.30in" height="0.3in" left="0in"  position="absolute" top="1.37in">
      <fo:block ><xsl:value-of select="LINE_ONE"/></fo:block>
      <xsl:variable name="line_two" select="LINE_TWO"/>
	  	<xsl:choose>
			<xsl:when test="$line_two!=\'\'">
        	<fo:block line-height="0.15in">
	  			<xsl:value-of select="$line_two"/>
        	</fo:block>
        	</xsl:when>
			<xsl:otherwise><fo:block color="white">0</fo:block></xsl:otherwise>
		</xsl:choose>
    </fo:block-container>
     <fo:block-container width="3.30in" height="0.3in" left="0in"  position="absolute" top="1.87in" font-size="7.5pt">
		<fo:block vertical-align="top" line-height="0.1in">
			<xsl:value-of select="BILLADDR1"/>
			<xsl:variable name="baddr2" select="./BILLADDR2"/>
			<xsl:choose>
				<xsl:when test="$baddr2!=\'\'"><xsl:text>, </xsl:text><xsl:value-of select="$baddr2"/></xsl:when>
				<xsl:otherwise>
				    <xsl:text>&#160;</xsl:text> 
			    </xsl:otherwise>
			</xsl:choose>
		</fo:block>
		 </fo:block-container>
	    <fo:block-container width="3.30in" height="0.25in" left="0in"  position="absolute" top="2.2in">
		<fo:block vertical-align="top" line-height="0.2in">
			<xsl:value-of select="BILLCITY"/>
			<xsl:variable name="bstate" select="./BILLSTATE"/>
			<xsl:variable name="bzip" select="./BILLZIP"/>
			<xsl:choose>
				<xsl:when test="($bstate!=\'\') or ($bzip!=\'\')"><xsl:text>, </xsl:text><xsl:value-of select="$bstate"/><xsl:text> </xsl:text><xsl:value-of select="$bzip"/></xsl:when>
				<xsl:otherwise>
				    <xsl:text>&#160;</xsl:text> 
			    </xsl:otherwise>
			</xsl:choose>
		</fo:block>
		</fo:block-container>
	</xsl:template>';

$templateAccountNumber = '<xsl:template name="account_number">
    <fo:block-container width="3.30in" height="0.3in" left="0in"  position="absolute" top="2.5in">
	    <fo:block  line-height="0.16in">
		<xsl:variable name="value" select="./VENDORID"/>
		<xsl:choose>
			<xsl:when test="$value!=\'\'">
				<fo:block line-height="0.3in"><xsl:value-of select="$value"/></fo:block>
			</xsl:when>
			<xsl:otherwise>
				<fo:block color="white" >0</fo:block>
			</xsl:otherwise>
		</xsl:choose>
		</fo:block>
		</fo:block-container>
	</xsl:template>';

$templateTinDetails = '	<xsl:template name="tin_details">
        <fo:block-container width="0.6in" left="2.94in" height="0.3in" position="absolute" top="2.565in">
            <fo:block  line-height="0.3in">
                <xsl:variable name="valueTin" select="./FORM1099AMOUNT/BOXTIN"/>
                <xsl:choose>
                    <xsl:when test="$valueTin!=\'\'">
                        <fo:block line-height="0.2in"><xsl:value-of select="$valueTin"/></fo:block>
                    </xsl:when>
                    <xsl:otherwise>
                        <fo:block color="white" >0</fo:block>
                    </xsl:otherwise>
                </xsl:choose>
            </fo:block>
        </fo:block-container>
    </xsl:template>';

$toform1099xsl = '<?xml version="1.0" encoding="iso-8859-1"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
	<xsl:template match="/">
		<fo:root>
			<fo:layout-master-set>

				<xsl:variable name="margin-top"><xsl:value-of select="//MARGIN/TOP"/></xsl:variable>
				<xsl:variable name="margin-left"><xsl:value-of select="//MARGIN/LEFT"/></xsl:variable>

				<fo:simple-page-master master-name="page"
						page-height="11in" page-width="8.5in"
						margin-top="0mm" margin-bottom="0mm"
						margin-left="0mm" margin-right="0mm"
						>
					<fo:region-body
						margin-top="{$margin-top}in" margin-bottom="0in"
						margin-left="{$margin-left}in" margin-right="0.25in"
						overflow1="auto"/>
				</fo:simple-page-master>

			</fo:layout-master-set>

			<fo:page-sequence master-reference="page">
				<fo:flow flow-name="xsl-region-body" font-family="Helvetica" font-size="8.5pt">
					<xsl:apply-templates/>
				</fo:flow>
			</fo:page-sequence>
		</fo:root>
	</xsl:template>

	<xsl:template match="Company"/>

	<xsl:template match="FORMPATR">
		<xsl:variable name="vendorCnt">
			<xsl:value-of select="count(./Vendor)"/>
		</xsl:variable>
		<xsl:for-each select="./Vendor">
			<fo:block-container width="3.1in" height="3.5in" position="absolute">
				<xsl:attribute name="top"><xsl:value-of select="((position() - 1) mod 3) * 3.68"/>in</xsl:attribute>
				<xsl:call-template name="LeftColInt"/>
			</fo:block-container>
			<fo:block-container width="2.5in" left="3.1in" height="4.5in" position="absolute">
				<xsl:attribute name="top"><xsl:value-of select="((position() - 1) mod 3) * 3.68"/>in</xsl:attribute>
				<xsl:call-template name="RightColInt"/>
			</fo:block-container>
			<xsl:if test="(position() &lt; $vendorCnt) and (((position()+1) mod 3) = 1)">
				<fo:block break-before="page"/>
			</xsl:if>
		</xsl:for-each>
	</xsl:template>

	<xsl:template name="LeftColInt">
		<fo:block-container width="3.6in" left="-0.4in" height="4.5in" top="0.05in" position="absolute">
			<xsl:call-template name="payer_address"/>
			<xsl:call-template name="idnumbers"/>
			<xsl:call-template name="vendor_address"/>
			<xsl:call-template name="account_number"/>
			<xsl:call-template name="tin_details"/>
		</fo:block-container>
	</xsl:template>

	'.$templatePayerAddress.'

	'.$templateVendorAddress.'

	'.$templateIdnumbers.'

	'.$templateAccountNumber.'
	
	'.$templateTinDetails.'


	<xsl:template name="showentry">
		<xsl:param name="value"/>
		<xsl:choose>
			<xsl:when test="$value != \'\'">
				<xsl:call-template name="doshow">
					<xsl:with-param name="vsize">0.33in</xsl:with-param>
					<xsl:with-param name="value" select="$value"/>
					<xsl:with-param name="color">black</xsl:with-param>
				</xsl:call-template>
			</xsl:when>
			<xsl:otherwise>
				<xsl:call-template name="showentryblank"/>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

	<xsl:template name="showentryblank">
		<xsl:call-template name="doshow">
			<xsl:with-param name="vsize">0.33in</xsl:with-param>
			<xsl:with-param name="value">0</xsl:with-param>
			<xsl:with-param name="color">white</xsl:with-param>
		</xsl:call-template>
	</xsl:template>

	<xsl:template name="showentrysize">
		<xsl:param name="vsize"/>
		<xsl:param name="value"/>
		<xsl:choose>
			<xsl:when test="$value!=\'\'">
				<xsl:call-template name="doshow">
					<xsl:with-param name="vsize" select="$vsize"/>
					<xsl:with-param name="value" select="$value"/>
					<xsl:with-param name="color">black</xsl:with-param>
				</xsl:call-template>
			</xsl:when>
			<xsl:otherwise>
				<xsl:call-template name="showentryblanksize">
					<xsl:with-param name="vsize" select="$vsize"/>
				</xsl:call-template>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

	<xsl:template name="showentryblanksize">
		<xsl:param name="vsize"/>
		<xsl:call-template name="doshow">
			<xsl:with-param name="vsize" select="$vsize"/>
			<xsl:with-param name="value">0</xsl:with-param>
			<xsl:with-param name="color">white</xsl:with-param>
		</xsl:call-template>
	</xsl:template>

	<xsl:template name="doshow">
		<xsl:param name="vsize"/>
		<xsl:param name="value"/>
		<xsl:param name="color"/>
		<fo:block text-align="end">
			<xsl:attribute name="line-height"><xsl:value-of select="$vsize"/></xsl:attribute>
			<xsl:attribute name="color"><xsl:value-of select="$color"/></xsl:attribute>
			<xsl:value-of select="format-number($value, \'######0.00\')"/>
		</fo:block>
	</xsl:template>

	<xsl:template name="showtextentrysize">
		<xsl:param name="vsize"/>
		<xsl:param name="value"/>
		<xsl:choose>
			<xsl:when test="$value!=\'\'">
				<fo:block vertical-align="top" text-align="end">
					<xsl:attribute name="line-height"><xsl:value-of select="$vsize"/></xsl:attribute>
					<xsl:attribute name="color">black</xsl:attribute>
					<xsl:value-of select="$value"/>
				</fo:block>
			</xsl:when>
			<xsl:otherwise>
				<xsl:call-template name="showentryblanksize">
					<xsl:with-param name="vsize" select="$vsize"/>
				</xsl:call-template>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

	<xsl:template name="RightColInt">
		    <fo:block-container width="1in"  height="0.2in" left="0in"  position="absolute" top="0.1in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.16in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX1"/>
				</xsl:call-template>
			</fo:block-container>
			 <fo:block-container width="1in"  height="0.2in" left="0in"  position="absolute" top="0.4in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.2in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX2"/>
				</xsl:call-template>
			</fo:block-container>
			 <fo:block-container width="1in"  height="0.2in" left="0in"  position="absolute" top="0.75in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.2in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX3"/>
				</xsl:call-template>
			</fo:block-container>
			 <fo:block-container width="1in"  height="0.25in" left="0in"  position="absolute" top="1.05in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.25in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX4"/>
				</xsl:call-template>
			</fo:block-container>
			 <fo:block-container width="1in"  height="0.2in" left="0in"  position="absolute" top="1.645in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.15in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX6"/>
				</xsl:call-template>
			</fo:block-container>
			 <fo:block-container width="1in"  height="0.2in" left="0in"  position="absolute" top="1.92in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.2in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX8"/>
				</xsl:call-template>
			</fo:block-container>
			 <fo:block-container width="1in"  height="0.2in" left="0in"  position="absolute" top="2.25in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.2in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX10"/>
				</xsl:call-template>
			</fo:block-container>
			 <fo:block-container width="1in"  height="0.3in" left="0in"  position="absolute" top="2.6in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.2in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX12"/>
				</xsl:call-template>
			</fo:block-container>
			

			<fo:block-container width="0.8in" left="1.4in" height="0.25in"  position="absolute" top="1.05in">
			    <xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.25in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX5"/>
				</xsl:call-template>
			</fo:block-container>
			<fo:block-container width="0.8in" left="1.4in" height="0.2in"  position="absolute" top="1.645in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.15in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX7"/>
				</xsl:call-template>
			</fo:block-container>
			<fo:block-container width="0.8in" left="1.4in" height="0.2in"  position="absolute" top="1.92in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.2in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX9"/>
				</xsl:call-template>
			</fo:block-container>
		    <fo:block-container width="0.8in" left="1.4in" height="0.2in"  position="absolute" top="2.25in">
				<xsl:call-template name="showentrysize">
					<xsl:with-param name="vsize">0.2in</xsl:with-param>
					<xsl:with-param name="value" select="./FORM1099AMOUNT/BOX11"/>
				</xsl:call-template>
			</fo:block-container>
            <fo:block-container width="0.5in" left="1.75in" height="0.2in"  position="absolute" top="2.6185in">
                <fo:block  line-height="0.28in">
                    <xsl:variable name="valueE" select="./FORM1099AMOUNT/BOX13"/>
                    <xsl:choose>
                        <xsl:when test="$valueE!=\'\'">
                            <fo:block line-height="0.2in" text-align="right" ><xsl:value-of select="translate($valueE,\'x\',\'X\')" /></fo:block>
                        </xsl:when>
                        <xsl:otherwise>
                            <fo:block color="white" >0</fo:block>
                        </xsl:otherwise>
                    </xsl:choose>
                </fo:block>
            </fo:block-container>
	</xsl:template>
      
</xsl:stylesheet>';
