<?php
/**
 * DunningNoticeManager
 *
 * <AUTHOR> / <PERSON><PERSON><PERSON>
 * @copyright 2022 Intacct Corporation All, Rights Reserved
 */

class DunningNoticeManager extends EntityManager
{
    use DunningNoticePrintEmailTrait;

    /** @var string $printUrl */
    private $printUrl = '';

    /** @var int $emailCount */
    private $emailCount = 0;

    /* @var bool $isMCMESubscribed */
    private $isMCMESubscribed;

    /**
     * @param array $params
     */
    public function __construct(array $params = [])
    {
        parent::__construct($params);
    }

    /**
     * @return string
     */
    public function getPrintUrl(): string
    {
        return $this->printUrl;
    }

    /**
     * @param string $printUrl
     */
    public function setPrintUrl(string $printUrl): void
    {
        $this->printUrl = $printUrl;
    }

    /**
     * @return int
     */
    public function getEmailCount(): int
    {
        return $this->emailCount;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function buildSenderDetails(array &$values): bool
    {
        // Set the default sender info
        $arSetupMgr = Globals::$g->gManagerFactory->getManager('arsetup');
        $arpref = $arSetupMgr->getSenderDetails();
        $values['SENDERNAME'] = $arpref['SENDERNAME'];
        $values['SENDEREMAIL'] = $arpref['SENDEREMAIL'];
        $values['SENDERPHONE'] = $arpref['SENDERPHONE'];
        return true;
    }

    /**
     * @return bool
     */
    public function isMCMESubscribed(): bool
    {
        // Are we in an Atlas company ?
        if (isNullOrBlank($this->isMCMESubscribed)) {
            $this->setIsMCMESubscribed(IsMCMESubscribed());
        }
        return $this->isMCMESubscribed;
    }

    /**
     * @param bool $isMCMESubscribed
     */
    public function setIsMCMESubscribed(bool $isMCMESubscribed): void
    {
        $this->isMCMESubscribed = $isMCMESubscribed;
    }

    /**
     * @param array $filters
     * @param array $values
     *
     * @return bool
     */
    private function buildDunningCustomerFilters(array &$filters, array $values, string $alias): bool
    {
        if(!$this->validateCustomerFilters($values)){
            return false;
        }

        $start_customer = '';
        $end_customer = '';
        if ( ! isNullOrBlank($values['FROMCUSTOMERID']) ) {
            [ $_start_customer ] = explode(Util::INTACCTID_SEPARATOR, $values['FROMCUSTOMERID']);
            $start_customer = isl_trim($_start_customer);
        }
        if ( ! isNullOrBlank($values['TOCUSTOMERID']) ) {
            [ $_end_customer ] = explode(Util::INTACCTID_SEPARATOR, $values['TOCUSTOMERID']);
            $end_customer = isl_trim($_end_customer);
        }

        if ( $start_customer !== '' && $end_customer === '' ) {
            $filters[] = [ 'CUSTOMERID', '>=', $start_customer ];
        } else if ( $start_customer !== '' && $end_customer !== '' ) {
            $filters[] = [ 'CUSTOMERID', '>=', $start_customer ];
            $filters[] = [ 'CUSTOMERID', '<=', $end_customer ];
        } else if ( $start_customer === '' && $end_customer !== '' ) {
            $filters[] = [ 'CUSTOMERID', '<=', $end_customer ];
        } else if ( !isNullOrBlank($values['CUSTOMERTYPE']) ) {
            $custtypeMgr = Globals::$g->gManagerFactory->getManager('custtype');
            $custtypeobj = $custtypeMgr->get($values['CUSTOMERTYPE'], [ 'RECORDNO' ]);
            if ( !empty($custtypeobj) ) {
                $filters[] = [ 'CUSTOMER.CUSTTYPEKEY', '=', $custtypeobj['RECORDNO'] ];
            } else {
                Globals::$g->gErr->addIAError('AR-0409', __FILE__ . ':' . __LINE__,
                    "value for Customer type ".$values['CUSTOMERTYPE']." is not valid.Enter valid Customer type,
                     then try again.",
                     ['CUSTOMERTYPE' => $values['CUSTOMERTYPE']]
                );
                return false;
            }
        } else if ( !isNullOrBlank($values['CUSTOMERGROUP']) ) {
            $customerGroupMgr = Globals::$g->gManagerFactory->getManager('customergroup');
            $members = $customerGroupMgr->getGroupMembersById($values['CUSTOMERGROUP'], false);

            if ( !empty($members) ) {
                $filters[] = [ $alias == "customer" ? 'RECORDNO' : 'CUSTOMERRECORDNO', 'IN', $members['MEMBERRECS'] ];
            } else {
                Globals::$g->gErr->addIAError(
                    'AR-0526', __FILE__ . ':' . __LINE__,
                    "value for Customer group ".$values['CUSTOMERGROUP']." is not valid.Enter valid Customer group, then try again.",
                    ['CUSTOMERGROUP' => $values['CUSTOMERGROUP']]
                );
                return false;
            }
        }

        return true;
    }

    /**
     * @param array $values
     * @param array $result
     *
     * @return bool
     */
    private function getAllDunningTransactions(array $values, array &$result): bool
    {
        $entity = 'arinvoice';
        $filters = $this->buildDunningFilters($values, $entity);
        if (!$this->buildDunningCustomerFilters($filters, $values, $entity)) {
            return false;
        }

        $mgr = Globals::$g->gManagerFactory->getManager($entity);
        $param = [
            'selects'     => [
                'ENTITY',
                'COUNT(*) AS INVOICECOUNT',
            ],
            'filters'     => [ $filters ],
            'groupby'     => [
                'ENTITY',
            ],
            'orders'      => [ [ 'ENTITY', 'asc' ] ],
            'nodbfilters' => true,
        ];
        $result = $mgr->GetList($param);
        if (empty($result)) {
            $result = [];
        }
        return true;
    }

    /**
     * @param array $values
     * @param array $customersList
     * @param bool $formatResult
     *
     * @return bool
     */
    public function applyDunningFilters(array $values, array &$customersList, bool $formatResult = false): bool
    {
        $results = [];
        $ok = $this->validateSearchParams($values);
        $ok = $ok && $this->getAllDunningTransactions($values, $results);
        if (!$ok || empty($results)) {
            return $ok;
        }
        $dunningTransaction = [];
        $customerEntityMap = [];

        $deliverAll = $values['DELIVER_ALL'] === 'true' ? true : false;
        $this->setDeliverAll($deliverAll);

        foreach ( $results as $result ) {
            $customerEntityMap[] = $result['ENTITY'];
            $dunningTransaction[$result['ENTITY']] = $result['INVOICECOUNT'];
        }
        $customerDetails = $this->getCustomerContactDetails($customerEntityMap);
        $emailTemplateMgr = Globals::$g->gManagerFactory->getManager('emailtemplate');
        $emailTemplateDetail = $emailTemplateMgr->get($values['EMAILTEMPLATEKEY']);

        foreach ( $customerDetails as $result ) {
            $emailTo = trim($this->buildMailToIds($result), ",");
            $injector = new Injector();
            $toAddress = $injector->inject($emailTemplateDetail['TOADDRESS'], $result);
            $ccAddress = $injector->inject($emailTemplateDetail['CCADDRESS'], $result);
            $bccAddress = $injector->inject($emailTemplateDetail['BCCADDRESS'], $result);
            $toAddress = trim($toAddress, ";,");

            if ( $this->deliverAll === true && ! empty($emailTemplateDetail) ) {
                $toAddress .= ',' . $emailTo;
                $toAddress = trim($toAddress, ",");
            }
            $data = [
                'CUSTOMERID' => $result['CUSTOMERID'],
                'EMAILTO'    => $toAddress ? : $emailTo,
                'EMAILCC'    => trim($ccAddress, ";,") ? : '',
                'EMAILBCC'   => trim($bccAddress, ";,") ? : '',
            ];
            if ( $formatResult === true ) {
                // need to set only for curd+ API
                $data['SELECTPRINT'] = "false";
                $data['SELECTEMAIL'] = "false";
                $customersList['DUNNINGCUSTOMERS']['dunningcustomer'][] = $data;
            } else {
                $data['CUSTOMERNAME'] = $result['CUSTOMERNAME'];
                $data['INVOICECOUNT'] = $dunningTransaction[$result['ENTITY']];
                $customersList[] = $data;
            }
        }
        return $ok;
    }

    /**
     * For CURD+
     *
     * @param string $asofdate
     * @param string $basedon
     * @param string $dunninglevel
     * @param number $fromcustomerid
     * @param number $tocustomerid
     * @param string $customertype
     *
     * @return array
     */
    public function getDunningCustomers(
        $asofdate, $basedon, $dunninglevel,
        $fromcustomerid, $tocustomerid, $customertype, $customerGroup
    ): array
    {
        $data = [
            'ASOFDATE' => $asofdate, 'BASEDON' => $basedon, 'DUNNINGLEVEL' => $dunninglevel,
            'FROMCUSTOMERID' => $fromcustomerid, 'TOCUSTOMERID' => $tocustomerid,
            'CUSTOMERTYPE' => $customertype,'CUSTOMERGROUP' => $customerGroup
        ];

        $results = [];
        $this->applyDunningFilters($data, $results, true);

        return [ 'DUNNINGCUSTOMERS' => $results ];
    }
    
    
    /**
     * Curd+ API for resending the dunning notice
     * @param int $recordNo
     *
     * @return string
     */
    public function resendDunningNotice($recordNo) : string
    {
        $dunningCustomerMgr = Globals::$g->gManagerFactory->getManager('dunningcustomer');
        $ok = $dunningCustomerMgr->resendDunningNotice($recordNo);

        return $ok === true ? "Resend successful" : "Resend email failed!";
    }

    /**
     * @param array $filters
     *
     * @return bool
     */
    private function validateSearchParams(array $filters) : bool
    {
        if ( empty($filters['ASOFDATE']) ) {
            Globals::$g->gErr->addError('AR-0401', __FILE__ . ':' . __LINE__,
                            "Enter the As of date, then try again.");
            return false;
        }

        if ( empty($filters['BASEDON']) ) {
            Globals::$g->gErr->addError('AR-0402', __FILE__ . ':' . __LINE__,
                            "Enter the Based on value, then try again.");
            return false;
        } else if ( ! in_array($filters['BASEDON'], [ "I", "D" ]) ) {
            Globals::$g->gErr->addError('AR-0403', __FILE__ . ':' . __LINE__,
                            "The Based on value is not valid. Enter a valid Based on value, then try again.");
            return false;
        }

        $ok = $this->validateCustomerFilters($filters);

        return $ok && $this->validateDunningLevel($filters['DUNNINGLEVEL']);
    }

    /**
     * add a record to the database
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function regularAdd(&$values) : bool
    {
        $ok = true;
        try {
            $deliverAll = $values['DELIVER_ALL'] === 'true' ? true : false;
            $this->setDeliverAll($deliverAll);
            $attachInvoice = $values['ATTACHINVOICE'] === 'true' ? true : false;
            $this->setAttachInvoice($attachInvoice);

            $ok = $ok && $this->validateDunningHeader($values);
            $ok = $ok && $this->validateTranslateTemplates($values);

            $selectedCustomerIds = [];
            $ok = $ok && $this->validateSelectedDunningCustomers($values, $selectedCustomerIds);

            if (!$ok) {
                return false;
            }

            $this->setAsOfDate($values['ASOFDATE']);
            $this->setPrintTemplateName($values['PRINTTEMPLATENAME']);

            $source = 'DunningNoticeManager::regularAdd()';
            $ok = $this->beginTrx($source);

            $ok = $ok && $this->getInvoiceDetailByCustomerIds($selectedCustomerIds, $values);

            $orgValues = $values;
            $ok = $ok && $this->buildValuesForDunningNotice($orgValues, $values);

            $ok = $ok && parent::regularAdd($values);

            $setPrintUrl = false;
            $ok = $ok && $this->postProcessDunningNotice($values['RECORDNO'], $setPrintUrl);
            if ($ok && $setPrintUrl) {
                $this->setPrintUrl($this->buildDunningPrintUrlByRecord($values['RECORDNO']));
            }

            $ok = $ok && $this->deliverDunningByEmail($values, $this->emailCount);

            $ok = $ok && $this->commitTrx($source);
            $ok = $ok && $this->addKibanaData($values['RECORDNO']);

            if (!$ok) {
                $this->rollbackTrx($source);
            }
        } catch (Exception $e) {
            LogToFile($e->getMessage());
            $ok = false;
        }

        if (!$ok) {
            // do *not* add error, if it has only warnings
            if (!HasErrors() && HasWarnings()) {
                return false;
            }
            $msg = "Could not create $this->_entity record!";
            Globals::$g->gErr->addIAError('AR-0410', __FILE__ . ':' . __LINE__, $msg, ['ENTITY' => $this->_entity]);
        }
        return $ok;
    }

    /**
     * @param string $dunningId
     *
     * @return bool
     */
    private function addKibanaData(string $dunningId)
    {
        $metricDunningNotice = new MetricDunningNoticeDunningData();
        $metricDunningNotice->setId($dunningId);
        $customerTxnDetails = $this->getCustomerTxnDetails();
        $metricDunningNotice->setCustomerCount(countArray($customerTxnDetails));
        $metricDunningNotice->setInvoiceCount($this->totalInvoiceCount);
        $metricDunningNotice->setIsInvoiceAttachment($this->attachinvoice);
        $metricDunningNotice->publish();

        return true;
    }
    /**
     * @param array $values
     *
     * @return bool
     */
    public function API_Add(&$values): bool
    {
        if (isNullOrBlank($values['SENDEREMAIL'])) {
            // Set the default sender info
            $this->buildSenderDetails($values);
        }

        return parent::API_Add($values);
    }

    /**
     * @param array  $value
     * @param string $path
     * @param int    $lineNumber
     * @param string $format
     *
     * @return bool
     */
    private function validateEmail(array $value, string $path, int $lineNumber, $format=''): bool
    {
        global $gCommaSemicolonSeparatedEmailFormat;
        if (isNullOrBlank($format)) {
            $format = $gCommaSemicolonSeparatedEmailFormat;
        }

        if ( !empty($value[$path]) && preg_match($format, $value[$path]) === 0 ) {
            $msg = "Invalid email id passed to {$path}";
            $errorCode = 'AR-0417';
            $placeholder = ['PATH' => $path];

            if ($lineNumber > 0) {
                $msg .= " on line " . $lineNumber;
                $errorCode = 'AR-0411';
                $placeholder = array_merge($placeholder, ['LINE_NUMBER' => $lineNumber]);
            }
            Globals::$g->gErr->addIAError($errorCode, __FILE__ . ':' . __LINE__,$msg, $placeholder);
            return false;
        }
        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function validateDunningHeader(array $values): bool
    {
        if (empty($values['SENDEREMAIL'])) {
            Globals::$g->gErr->addError(
                'AR-0404',
                __FILE__ . ':' . __LINE__,
                "The Sender email address is missing. Enter the Sender email address, then try again."
            );
            return false;
        }
        global $gEmailFormat;
        if (!$this->validateEmail($values, 'SENDEREMAIL', 0, $gEmailFormat)) {
            return false;
        }

        return $this->validateSearchParams($values);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function validateTranslateTemplates(array &$values): bool
    {
        if ( isNullOrBlank($values['PRINTTEMPLATENAME']) ) {
            Globals::$g->gErr->addError('AR-0405', __FILE__ . ':' . __LINE__,
                "Printed document template is a required field.",
                "Select a printed document template, then try again.");

            return false;
        }

        $ok = self::translatePrintTemplateValues($values);
        if (!$ok) {
            Globals::$g->gErr->addIAError('AR-0406', __FILE__ . ':' . __LINE__,
                "Printed document template " . $values['PRINTTEMPLATENAME'] . " 
                does not exist. Update the printed document template, then try again.",
                ['PRINTTEMPLATENAME' => $values['PRINTTEMPLATENAME']]
            );
            return false;
        }

        if ( !isNullOrBlank($values['EMAILTEMPLATEKEY']) ) {
            $emailTmplID = explode(Util::INTACCTID_SEPARATOR, $values['EMAILTEMPLATEKEY']);
            $tmpl = self::getEmailTemplate($emailTmplID[0]);

            if ( empty($tmpl) ) {
                Globals::$g->gErr->addIAError('AR-0412', __FILE__ . ':' . __LINE__,
                    "Email template " . $values['EMAILTEMPLATEKEY'] . " does not exists.
                    Update the printed document template, then try again.",
                    ['EMAILTEMPLATEKEY' => $values['EMAILTEMPLATEKEY']]
                );

                return false;
            }

            $values['EMAILTEMPLATEKEY'] = $tmpl['RECORDNO'];
        }
        return true;
    }

    /**
     * @param $filters
     *
     * @return bool
     */
    private function validateCustomerFilters(array $filters): bool
    {
        $customerRangeExists = !empty($filters['FROMCUSTOMERID']) || !empty($filters['TOCUSTOMERID']);

        if (($customerRangeExists && (!empty($filters['CUSTOMERTYPE']) || !empty($filters['CUSTOMERGROUP'])))
            || (!empty($filters['CUSTOMERTYPE']) && !empty($filters['CUSTOMERGROUP']))
        ) {
            Globals::$g->gErr->addError(
                'AR-0527', __FILE__ . ':' . __LINE__,
                "Invalid Customer Filters.You can pass only one of (FROMCUSTOMERID & TOCUSTOMERID),CUSTOMERTYPE OR CUSTOMERGROUP values, Enter valid customer filters, then try again."
            );
            return false;
        }

        return true;
    }

    /**
     * @param array $values
     * @param array $selectedCustomerIds
     *
     * @return bool
     */
    private function validateSelectedDunningCustomers(array &$values, array &$selectedCustomerIds): bool
    {
        if (!is_iterable($values['DNCUSTOMERS'])) {
            Globals::$g->gErr->addError(
                'AR-0407', __FILE__ . ':' . __LINE__,
                "Customer details are missing. Enter a customer, then try again."
            );
            return false;
        }

        // validate customer values
        $filter = [];
        $fetchedCustomerDetails = $customerTxnDetails = $entityIDMap = [];
        $validateCustomer = false;
        if (!$this->buildDunningCustomerFilters($filter, $values, "customer")) {
            return false;
        }
        if (!empty($filter)) {
            // fetch customer details from here
            $customers = $this->getCustomerDetails($filter);
            if (empty($customers)) {
                Globals::$g->gErr->addError(
                    'AR-0408', __FILE__ . ':' . __LINE__,
                    "Provided customer not found. Enter a different customer, then try again."
                );
                return false;
            }
            $validateCustomer = true;
            foreach ($customers as $customer) {
                $fetchedCustomerDetails[$customer['CUSTOMERID']] = $customer;
            }
        }

        $printCustomerIds = $emailCustomerIds = $emailDetailByCustomerIds = [];
        foreach ($values['DNCUSTOMERS'] as $key => $value) {
            // if customer is not selected for email or print, continue
            if (!($value['SELECTEMAIL'] === 'true' || $value['SELECTPRINT'] === 'true')) {
                continue;
            }
            $lineNumber = $key + 1;

            if (empty($value['CUSTOMERID'])) {
                Globals::$g->gErr->addIAError('AR-0413', __FILE__ . ':' . __LINE__,
                    "Customer is missing for line ".$lineNumber." Enter a customer, then try again.",
                    ['LINE_NUMBER' => $lineNumber]
                );
                return false;
            }

            if ($validateCustomer) {
                $customer = $fetchedCustomerDetails[$value['CUSTOMERID']];
                if (empty($customer)) {
                    Globals::$g->gErr->addIAError('AR-0414', __FILE__ . ':' . __LINE__,
                        "Customer ID " . $value['CUSTOMERID'] . " on line ".$lineNumber.
                        " does not exist. Enter a different customer, then try again.",
                        ['CUSTOMERID' => $value['CUSTOMERID'], 'LINE_NUMBER' => $lineNumber]
                    );

                    return false;
                }
                $customerTxnDetails[$value['CUSTOMERID']] = $customer;
                $entityIDMap[$customer['ENTITY']] = $customer['CUSTOMERID'];
            }

            if ($value['SELECTEMAIL'] === 'true') {
                if (empty($value['EMAILTO'])) {
                    Globals::$g->gErr->addIAError(
                        'AR-0415',
                        __FILE__ . ':' . __LINE__,
                        "Recipient email address is missing for " . $value['CUSTOMERID']." 
                        Enter an email address for the recipient, then try again.",
                        ['CUSTOMERID' => $value['CUSTOMERID']]
                    );
                    return false;
                }
                $ok = $this->validateEmail($value, 'EMAILTO', $lineNumber);
                $ok = $ok && $this->validateEmail($value, 'EMAILCC', $lineNumber);
                $ok = $ok && $this->validateEmail($value, 'EMAILBCC', $lineNumber);
                if (!$ok) {
                    return $ok;
                }

                $emailCustomerIds[] = $value['CUSTOMERID'];
                $emailDetailByCustomerIds[$value['CUSTOMERID']]['EMAILTO'] = $value['EMAILTO'];
                if (!empty($value['EMAILCC'])) {
                    $emailDetailByCustomerIds[$value['CUSTOMERID']]['EMAILCC'] = $value['EMAILCC'];
                }
                if (!empty($value['EMAILBCC'])) {
                    $emailDetailByCustomerIds[$value['CUSTOMERID']]['EMAILBCC'] = $value['EMAILBCC'];
                }
            }

            if ($value['SELECTPRINT'] === 'true') {
                $printCustomerIds[] = $value['CUSTOMERID'];
            }
            $selectedCustomerIds[] = $value['CUSTOMERID'];
        }

        if (count($selectedCustomerIds) === 0) {
            Globals::$g->gErr->addError('AR-0416', __FILE__ . ':' . __LINE__,
                "No customers selected for print or email. Select customers to print or send notices, then try again.");

            return false;
        }
        if (!empty($customerTxnDetails)) {
            $this->setCustomerTxnDetails($customerTxnDetails);
            $this->setCustomerEnityIdMap($entityIDMap);
        }
        // set the print/email customer ids
        $this->setEmailCustomerIds($emailCustomerIds);
        $this->setPrintCustomerIds($printCustomerIds);
        $this->setEmailDetailByCustomerIds($emailDetailByCustomerIds);

        return true;
    }

    /**
     * @param array $orgValues
     * @param array $dunningValues
     *
     * @return bool
     */
    private function buildValuesForDunningNotice(array $orgValues, array &$dunningValues): bool
    {
        $dunningCustomers = [];
        $ok = $this->buildValuesForDunningCustomers($dunningCustomers);

        if (!$ok) {
            return false;
        }
        $dunningDetail = $this->getDunningLevelDetail();
        $emailtemplate = null;
        if (!isNullOrBlank($orgValues['EMAILTEMPLATEKEY'])) {
            $emailtemplate = $orgValues['EMAILTEMPLATEKEY'];
        }
        $this->setSenderDetails($orgValues);
        $sender = $this->getSenderDetails();
        $dunningValues = [
            'DUNNINGLEVEL'      => $dunningDetail['DUNNINGDEFINITIONID'],
            'DUNNINGDEFKEY'     => $dunningDetail['RECORDNO'],
            'CURRENCY'          => $dunningDetail['CURRENCY'],
            'BASECURR'          => $dunningDetail['BASECURR'],
            'BASEDON'           => $orgValues['BASEDON'],
            'ASOFDATE'          => $orgValues['ASOFDATE'],
            'PRINTTEMPLATENAME' => $orgValues['PRINTTEMPLATENAME'],
            'PRINTTEMPLATEKEY'  => $orgValues['PRINTTEMPLATEKEY'],
            'STDPRINTTEMPLATEKEY'=> $orgValues['STDPRINTTEMPLATEKEY'],
            'EMAILTEMPLATEKEY'  => $emailtemplate,
            'SENDEREMAIL'       => $sender['email'],
            'SENDERNAME'        => $sender['name'],
            'SENDERPHONE'       => $orgValues['SENDERPHONE'],
            'ATTACHINVOICE'     => $orgValues['ATTACHINVOICE'],
            'DNCUSTOMERS'       => $dunningCustomers
        ];

        return true;
    }

    /**
     * @param number $dunningdefkey
     *
     * @return bool
     */
    public static function isDunningDefinitionIsUsedForNotice($dunningdefkey): bool
    {
        //latestversionkey will be available only for the records which are being used in dunningrunlog
        global $gQueryMgr;
        // check the system generated for atleast one payment in the batch.
        $quickDepQry = [
            'QUERY' => "select 1 as CNT from dunningrunlog where cny# =:1 and dunningdefkey =:2 and rownum = 1",
            'ARGTYPES' => ['integer', 'integer']
        ];
        $result = $gQueryMgr->DoCustomQuery($quickDepQry, [GetMyCompany(), $dunningdefkey], true);

        if ($result[0]['CNT'] > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param array $record
     * @param array $ownedObjects
     *
     * @return array|mixed
     */
    private function api_formatMasterDetail($record, $ownedObjects)
    {
        foreach ($ownedObjects as $owned) {
            if ( $record && is_array($record) && !array_key_exists($owned['path'], $record)) {
                continue;
            }
            $lineOwnedObjects = $this->GetOwnedObjectManager($owned['entity'])->GetOwnedObjects();
            foreach ($record[$owned['path']] as $key => $child) {
                if (!empty($lineOwnedObjects) && is_array($lineOwnedObjects)) {
                    $child = $this->api_formatMasterDetail($child, $lineOwnedObjects);
                }
                $record[$owned['path']][$owned['entity']][$key] = $child;
                unset($record[$owned['path']][$key]);
            }
        }
        return $record;
    }

    /**
     * Convert a master detail record into something readable externally
     *
     * @param array $record Array record as returned by Entity Manager
     *
     * @return array record formatted for use externally
     */
    protected function _API_FormatMasterDetail($record)
    {
        if (!isset($this->_schemas[$this->_entity]['ownedobjects'])) {
            return $record;
        }

        $record = $this->api_formatMasterDetail($record, $this->_schemas[$this->_entity]['ownedobjects']);

        return $record;
    }

    /**
     *  API_pruneFields
     *   This override of the Entity Manager version looks deeper into the tree of returned values for items.
     *
     * @param array           $values
     * @param string|string[] $fields
     *
     * @return array
     */
    function API_pruneFields(&$values, $fields)
    {
        return parent::API_pruneFieldsOwnedToo($values, $fields, "LESS_GET_FIELDS");
    }

}
