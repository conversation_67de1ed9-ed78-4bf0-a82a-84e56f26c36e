<?xml version='1.0' encoding='UTF-8'?>
<ROOT assoc="T" > 
    <entity>arpostedadvance</entity>
    <title>IA.POSTED_ADVANCE</title>
    <helpfile>Viewing_AR_Posted_Payment_Details</helpfile>
    <pages>
        <page assoc="T" >
            <title>IA.ADVANCE_INFORMATION</title>
            <readonly>1</readonly>
            <fields>
                <field assoc="T">
                    <path>CUSTOMERID</path>
                </field>
				<field assoc="T">
                    <path>CUSTOMERNAME</path>
                </field>
                <field assoc="T">
                    <path>PAYMENTTYPE</path>
                </field>
                <field assoc="T">
                    <path>BATCHTITLE</path>
                </field>
                <field assoc="T">
                    <path>FINANCIALACCOUNT</path>
                </field>
                <field assoc="T">
                    <path>PAYMENTDATE</path>
                </field>
				<field assoc="T">
                    <path>DOCUMENTNUMBER</path>
                </field>
                <field assoc="T">
                    <path>CURRENCY</path>
                </field>
                <field assoc="T">
                    <path>EXCH_RATE_DATE</path>
                </field>
                <field assoc="T">
                    <path>EXCH_RATE_TYPE_ID</path>
                </field>
                <field assoc="T">
                    <path>EXCHANGE_RATE</path>
                </field>
                <field assoc="T">
                    <path>PAYMENTAMOUNT</path>
                </field>
                <field assoc="T">
                    <path>PAYMENTTRXAMOUNT</path>
                </field>
				<field assoc="T">
                    <path>CLEARED</path>
                </field>
				<field assoc="T">
                    <path>CLRDATE</path>
                </field>
                <field assoc="T">
                    <path>RECORDNO</path>
					<hidden>1</hidden>
                </field>

				<MultilineLayout assoc="T" key="field">
					<path>PRENTRY</path>
					<title>IA.ITEMS</title>
					<columns>
						<column assoc="T">
							<fullname>IA.GL_ACCOUNTKEY</fullname>
							<path>ACCOUNTNO</path>
						</column>
						<column assoc="T">
							<fullname>IA.GL_ACCOUNT_TITLE</fullname>
							<path>ACCOUNTTITLE</path>
						</column>
                        <column assoc="T">
                            <fullname>IA.TRX_AMOUNT</fullname>
                            <path>TRX_AMOUNT</path>
                            <totaled>1</totaled>
                        </column>
						<column assoc="T">
							<fullname>IA.AMOUNT</fullname>
							<path>AMOUNT</path>
                            <totaled>1</totaled>
						</column>
						<column assoc="T">
							<fullname>IA.MEMO</fullname>
							<path>ENTRYDESCRIPTION</path>
						</column>
						<column assoc="T">
							<fullname>IA.DEPARTMENT</fullname>
							<path>DEPARTMENTID</path>
						</column>
						<column assoc="T">
							<fullname>IA.LOCATION</fullname>
							<path>LOCATIONID</path>
						</column>
                        <column assoc="T">
                            <fullname>IA.PROJECT</fullname>
                            <path>PROJECTID</path>
                        </column>
                        <column assoc="T">
                            <fullname>IA.CUSTOMER</fullname>
                            <path>CUSTOMERID</path>
                        </column>
                        <column assoc="T">
                            <fullname>IA.VENDOR</fullname>
                            <path>VENDORID</path>
                        </column>
                        <column assoc="T">
                            <fullname>IA.EMPLOYEE</fullname>
                            <path>EMPLOYEEID</path>
                        </column>
                        <column assoc="T">
                            <fullname>IA.ITEM</fullname>
                            <path>ITEMID</path>
                        </column>
                        <column assoc="T">
                            <fullname>IA.CLASS</fullname>
                            <path>CLASSID</path>
                        </column>
					</columns>
					<_func>MultilineLayout</_func>
				</MultilineLayout>

            </fields>
		</page>
		<page assoc="T">
			<title>IA.INVOICES</title>
            <readonly>1</readonly>
            <fields>
                <field assoc="T">
                    <path>CUSTOMERID</path>
                </field>
				<field assoc="T">
                    <path>CUSTOMERNAME</path>
                </field>
				<MultilineLayout assoc="T"  key="field" >
					<path>INVOICES</path>
					<title>IA.INVOICES_PAID</title>
					<norefreshlink>1</norefreshlink>
					<columns>
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>DRILLDOWN</path>
									<size>12</size>
									<fullname>IA.INVOICE_NUMBER</fullname>
								</_arg>
							</_args>
						</vbox>
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>DOCNUMBER</path>
									<size>12</size>
									<fullname>IA.REFERENCE_KEY</fullname>
								</_arg>
							</_args>
						</vbox>
						<vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>WHENCREATED</path>
									<size>12</size>
									<fullname>IA.DATE</fullname>
								</_arg>
							</_args>
						</vbox>
						 <vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>TOTALENTERED</path>
									<size>8</size>
									<fullname>IA.TOTAL_INVOICED</fullname>
									<totaled>1</totaled>
									<type assoc="T">
										<ptype>currency</ptype>
										<type>decimal</type>
										<colalign>right</colalign>
									</type>
								</_arg>
							</_args>
						</vbox>
						 <vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>TOTALDUE</path>
									<size>8</size>
									<fullname>IA.TOTAL_DUE</fullname>
									<totaled>1</totaled>
									<type assoc="T">
										<ptype>currency</ptype>
										<type>decimal</type>
										<colalign>right</colalign>
									</type>
								</_arg>
							</_args>
						</vbox>
						 <vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>PAYMENTAMOUNT</path>
									<size>8</size>
									<fullname>IA.PAYMENT_APPLIED</fullname>
									<type assoc="T">
										<ptype>currency</ptype>
										<type>decimal</type>
										<colalign>right</colalign>
									</type>
									<totaled>1</totaled>
								</_arg>
							</_args>
						</vbox>
						 <vbox assoc="T"  key="column" >
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T" >
									<path>APPLIEDAMOUNT</path>
									<size>8</size>
									<fullname>IA.TOTAL</fullname>
									<type assoc="T">
										<ptype>currency</ptype>
										<type>decimal</type>
										<colalign>right</colalign>
									</type>
									<totaled>1</totaled>
								</_arg>
							</_args>
						</vbox>
					</columns>
					<_func>MultilineLayout</_func>
				</MultilineLayout>
            </fields>
		</page>
    </pages>
</ROOT>
