<?
//=============================================================================
//
//	FILE:			common_apar.inc
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================



define("COMMON_APAR", "common_apar.inc");
require_once 'backend_term.inc';
require_once 'globals.ent';

import('DateTemplates');

/**
 * GetDocumentCreateMessage
 *
 * Central function where the document create message is generated.
 *
 * @param string $docnumber docnumber The document number of the newly created document.
 * @param string $doctitle
 *
 * @return string
 */
function GetDocumentCreateMessage($docnumber, $doctitle) 
{
    if($docnumber != '') {
        if(!$doctitle) {
            $doctitle = 'Document';
        }
        return "$doctitle $docnumber was created successfully.";
    }
    return '';
}

/**
 * @param string $entityid
 * @param string $type
 *
 * @return array
 */
function GetEntityContacts($entityid, $type)
{

    $contactkey1 = ($type == 'vendor') ? 'entity.paytokey' : 'entity.billtokey';
    $contactkey2 = ($type == 'vendor') ? 'entity.returntokey' : 'entity.shiptokey';

    $query = "select paytobilltocontact.name paytobillto, shiptoreturntocontact.name shiptoreturnto, displaycontact.name displaycontactname
		from $type entity, contact paytobilltocontact, contact shiptoreturntocontact, contact displaycontact
		where entity.cny# = :1 and
		entity.".$type."id = :2 and
		shiptoreturntocontact.cny# (+) = entity.cny# and 
		paytobilltocontact.cny# (+) = entity.cny# and 
		displaycontact.cny# = entity.cny# and 
		entity.displaycontactkey = displaycontact.record# and
		$contactkey1 = paytobilltocontact.record# (+) and
		$contactkey2 = shiptoreturntocontact.record# (+)";

    $cny = GetMyCompany();
    $res = QueryResult(array($query, $cny, $entityid));
    return $res[0]; 
}

/**
 * @param string $entity
 * @param string $key
 * @param string $label
 * @param string $where
 * @param string $order
 *
 * @return array
 */
function GetObjectMap($entity, $key, $label, $where='', $order='')
{
    /** @noinspection PhpUnusedLocalVariableInspection */
    $cny = GetMyCompany();

    $select = "$key GOM_KEY, $label GOM_LABEL";
    $order = $order ?: "GOM_LABEL";
    $objs = GetNObjects($entity, $select, $where, $order);

    $objMap = array();
    for ($i=0; $i<count($objs); $i++ ){
        $obj = $objs[$i];
        $objMap[$obj['GOM_KEY']] = $obj['GOM_LABEL'];
    }
    return $objMap;
}

/**
 * @param array $customer
 * @param string $where
 *
 * @return array
 */
function GetCustomerMap($customer, $where='')
{
    /** @noinspection PhpUnusedLocalVariableInspection */
    $objs = GetNonChildren('customer', $customer['CUSTOMERID'], $nonChildren, "customerid,name", $where);
    $objMap = array();
    for ($i=0; $i<count($nonChildren); $i++ ){
        $obj = $nonChildren[$i];
        $objMap[$obj['CUSTOMERID']] = $obj['NAME'];
    }
    return $objMap;
}

/**
 * @param string $where
 *
 * @return array
 */
function GetEmployeMap($where='')
{
    return GetObjectMap('employee', 'employeeid', "contact.firstname || ', ' || contact.lastname", $where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetEmployeeEntityMap($where='')
{
    return GetObjectMap('employee', 'entity', "contact.firstname || ', ' || contact.lastname", $where);    
}

/**
 * @param string $mod
 *
 * @return array
 */
function GetPRBatchMap($mod)
{
    
    if($mod == 'ap') {
        $where = "recordtype in ('pi','pa')";
    } elseif($mod == 'ar') {
        $where = "recordtype in ('ri','ra','rp')";
    } elseif($mod == 'ee') {
        $where = "recordtype in ('ei', 'ea')";
    } elseif($mod == 'he') {
        $where = "recordtype in ('hi')";
    }

    /** @noinspection PhpUndefinedVariableInspection */
    return GetObjectMap('prbatch', 'RECORD#', 'title', $where); 
}

/**
 * @param string $key
 *
 * @return array
 */
function GetContactKeyMap($key)
{
    if( $key != '' ) {
        return GetObjectMap('contact', 'RECORD#', "PRINTAS||' - '||FIRSTNAME||' - '||LASTNAME", "record# = $key");
    }
    return null;
}

/**
 * @param array $vendor
 * @param string $where
 *
 * @return array
 */
function GetVendorMap($vendor, $where='')
{
    // 	return GetObjectMap('vendor', 'vendorid', "name");
    /** @noinspection PhpUnusedLocalVariableInspection */
    $objs = GetNonChildren('vendor', $vendor['VENDORID'], $nonChildren, "vendorid,name", $where);
    $objMap = array();
    for ($i=0; $i<count($nonChildren); $i++ ){
        $obj = $nonChildren[$i];
        $objMap[$obj['VENDORID']] = $obj['NAME'];
    } 
    return $objMap;
}


/**
 * @return array
 */
function GetAllVendorsMap()
{
    return GetObjectMap('vendor', 'vendorid', "name");
}


/**
 * @return array
 */
function GetEmployeeMap()
{
    return GetObjectMap(
        'employee', 'employeeid', 
        "contact.lastname||decode(contact.firstname,null,'',', '||contact.firstname)||' '||contact.mi", "employee.status = 'T'"
    );
}

/**
 * @param string $employeeid
 *
 * @return array
 */
function GetParentEmployeeMap($employeeid)
{
    $childrenFields = "employeeid";
    if (!GetNonChildren('employee', $employeeid, $nonChildren, $childrenFields, " and status = 'T' ")) {
        // add error here 
    }
    $select = "contact.lastname||decode(contact.firstname,null,'',', '||contact.firstname)||' '||contact.mi AS NAME";

    foreach( $nonChildren as $val ){
        $where = array("employee.employeeid = :2", addoraslashes($val['EMPLOYEEID']));
        $objs = GetNObjects('employee', $select, $where);
        $parentMap[$val['EMPLOYEEID']] = $objs[0]['NAME'];
    }

    /** @noinspection PhpUndefinedVariableInspection */
    return $parentMap;
}

/**
 * @param string $id
 *
 * @return string|null
 */
function GetSupervisorName($id)
{
    $where = array("employeeid = :2", $id);
    $res = GetNObjects("employee", "contact.name", $where);
    return $res[0]['NAME'];
}

/**
 * @param string $where
 *
 * @return array
 */
function GetCusttypeMap($where='')
{
    return GetObjectMap('custtype', 'RECORD#', "name", $where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetVendtypeMap($where='')
{
    return GetObjectMap('vendtype', 'RECORD#', "name", $where);
}


//GetTermMap	- Used when presenting a drop-down for selecting terms available in the given module environment: supplies
//					a mapping of term record#s to their names along with the "Select" default option.
//	IN
//	$_op		- The global variable used for checking users' permissions for the current operation being performed.
//	$where		- Optional (csv, SQL syntax) filters to add to the WHERE clause when querying for the terms.
//	
//	Returns: An array containing the name, record#, and the "Select" default dropdown option.
//
/**
 * @param int $_op
 * @param string $where
 *
 * @return array|bool
 */
function GetTermMap($_op, $where='')
{
    GetTermOptionMap($_op, $map, $where);
    return $map;
}


/**
 * @return array
 */
function GetShipmethodMap()
{
    return GetObjectMap('shipmethod', 'RECORD#', "name");
}

/**
 * @param string $where
 *
 * @return array
 */
function GetTerritoryMap($where='')
{
    return GetObjectMap('territory', 'territoryid', "name", $where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetDeptMap($where='')
{
    return GetObjectMap('department', 'RECORD#', "TITLE", $where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetLocMap($where='')
{
    return GetObjectMap('location', 'RECORD#', "NAME", $where);
}

/**
 * @param string $deptno
 *
 * @return array
 */
function GetParentDeptMap($deptno)
{
    $childrenFields = "RECORD#,TITLE";
    if (!GetNonChildren('department', $deptno, $nonChildren, $childrenFields, " and status = 'T' ")) {
        // add some error here
    }
    $deptMap = array();

    foreach( $nonChildren as $key => $val ){
        $deptMap[$nonChildren[$key]['RECORD#']] = isl_htmlspecialchars($nonChildren[$key]['TITLE']);    
    }
    return $deptMap;
}

/**
 * @param string $where
 *
 * @return array
 */
function GetJournalMap($where='')
{

    if(IsMCMESubscribed() || IsGAAPTAXEnabled()) {
        $where .= " and record# in (
				select	bkj.journalkey 
				from	bookjournals bkj 
				where	bkj.cny# = gljournal.cny#
						and bkj.journalkey = gljournal.record#
						and (bookid = '" . ACCRUAL_BOOK . "' or bookid = '" . CASH_BOOK . "'))";
    }
    
    return GetObjectMap('gljournal', 'SYMBOL', "TITLE", $where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetAllJournalMap($where='')
{
    
    if(IsMCMESubscribed() || IsGAAPTAXEnabled()) {
        $where .= " and record# in (
				select	bkj.journalkey 
				from	bookjournals bkj 
				where	bkj.cny# = basejournal.cny#
						and bkj.journalkey = basejournal.record#
						and (bookid = '" . ACCRUAL_BOOK . "' or bookid = '" . CASH_BOOK . "'))";
    }

    return GetObjectMap('basejournal', 'SYMBOL', "TITLE", $where);
}

/**
 * @return array
 */
function GetJournalTypeMap()
{    
    $booksId = ['GAAP%', 'TAX%'];
    $boolStr = ['T', 'F'];

    $qry = "select
                j.title as TITLE,
                j.record# as RECORDNO,                
                j.symbol as SYMBOL, 
                j.statistical as STATISTICAL, 
                j.adj AS ADJ,
                j.status as STATUS,
                b.bookid as BOOKID,
                g.type as BOOKTYPE,
                g.operational,
                CASE WHEN b.bookid LIKE :2 THEN :4 ELSE :5 END AS GAAP,
                CASE WHEN b.bookid LIKE :3 THEN :4 ELSE :5 END AS TAX
			from 
                basejournal j, 
                bookjournals b,
                glbook g 
			where 
                b.cny# = :1
                and b.cny# = j.cny#
                and b.journalkey = j.record#
                and g.cny# = :1 
                and g.bookid=b.bookid";

    $res = QueryResult(array($qry, GetMyCompany(), $booksId[0], $booksId[1], $boolStr[0], $boolStr[1]));
    
    $objMap = array();
    for ( $i = 0 ; $i < count($res); $i++ ) {
        $obj = $res[$i];
        $objMap[$obj['SYMBOL']] = $obj;
    }
                    
    return $objMap;
}

/**
 * @param string $where
 *
 * @return array
 */
function GetJournalMapForCashReporting($where='')
{
    $where .= "and not exists (
				select	bkj.journalkey 
				from	bookjournals bkj, glbook gl 
				where	bkj.cny# = gljournal.cny#
						and bkj.journalkey = gljournal.record#
                        and gl.cny# = bkj.cny#
                        and gl.bookid = bkj. bookid
						and (bkj.bookid = '" . ACCRUAL_BOOK . "'or gl.type in ('".BOOKTYPE_GAAP_VAL."', '".BOOKTYPE_TAX_VAL."', '".BOOKTYPE_USR_VAL."')) ) ";

    return GetJournalMap($where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetJournalMapForAccrualReporting($where='')
{
    $where .= "and not exists (
				select	bkj.journalkey 
				from	bookjournals bkj, glbook gl 
				where	bkj.cny# = gljournal.cny#
						and bkj.journalkey = gljournal.record#
                        and gl.cny# = bkj.cny#
                        and gl.bookid = bkj.bookid
						and (bkj.bookid = '" . CASH_BOOK . "' or gl.type in ('".BOOKTYPE_GAAP_VAL."', '".BOOKTYPE_TAX_VAL."', '".BOOKTYPE_USR_VAL."')) ) ";

    return GetJournalMap($where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetJournalMapForMixed($where='')
{
    $where .= "and exists (
				select	bkj.journalkey 
				from	bookjournals bkj 
				where	bkj.cny# = gljournal.cny#
						and bkj.journalkey = gljournal.record#
						and bookid = '" . ACCRUAL_BOOK . "' ) ";
    $where .= "and exists (
				select	bkj.journalkey 
				from	bookjournals bkj 
				where	bkj.cny# = gljournal.cny#
						and bkj.journalkey = gljournal.record#
						and bookid = '" . CASH_BOOK . "' ) ";

    return GetJournalMap($where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetGLAccountMap($where="status='T'")
{
    global $gGLAccountMapforUI;
    if ( !isset($gGLAccountMapforUI) ) {
        $gGLAccountMapforUI = GetObjectMap('glaccount', 'ACCT_NO', "TITLE", $where);
    }
    return $gGLAccountMapforUI;
}

/**
 * @return array
 */
function GetCustAcctTypeMap()
{
    return array("O" => "Open Item", "B" => "Balance Forward");
}

/**
 * @param string $where
 *
 * @return array
 */
function GetLocationMap($where='')
{
    return GetObjectMap('location', 'RECORD#', "LOCATION_NO", $where);
}

/**
 * @param string $where
 *
 * @return array
 */
function GetDepartmentMap($where='')
{
    return GetObjectMap('department', 'RECORD#', "DEPT_NO", $where);
}

/**
 * @param bool   $includePrompt
 * @param string $altPrompt
 * @param bool $getTranslatedText
 *
 * @return string[]
 */
function GetPeriodMap($includePrompt = true, $altPrompt = '', bool $getTranslatedText = false)
{
    global $gReportGetReportMap;

    if (isEmptyArray($gReportGetReportMap)) {
        $cny = GetMyCompany();
        $entity = 'glbudgettype';
        $select = 'RECORD#, NAME, END_DATE - START_DATE as DATEDIFF';
        $order = 'DATETYPE, END_DATE DESC, DATEDIFF';

        if (!ShowFiscalPeriods()) {
            //	$where = "STATUS = 'T' AND ( DATETYPE = " . DATETMPL_CUSTOM . " OR DATETYPE < " . DATETMPL_FISCAL_NEXTQUARTER . " OR DATETYPE > " . DATETMPL_FISCAL_PRIORYEARNEXTQUARTER .") AND DATETYPE <>".DATETMPL_INTERNAL;
            // because the last fiscal datetype has changed from DATETMPL_FISCAL_PRIORYEARNEXTQUARTER (DATETYPE=43) to DATETMPL_FISCAL_PRIORYEARTHROUGHPRIORQUARTER (DATETYPE=44.58)
            $where = "STATUS = 'T' AND ( DATETYPE = " . DATETMPL_CUSTOM . " OR DATETYPE < " . DATETMPL_FISCAL_NEXTQUARTER . " OR DATETYPE > " . DATETMPL_FISCAL_PRIORYEARTHROUGHPRIORQUARTER . ") AND DATETYPE <>" . DATETMPL_INTERNAL;
        } else {
            $where = "STATUS = 'T' AND DATETYPE <> " . DATETMPL_INTERNAL;
        }

        $objs = GetNObjects($entity, $select, $where, $order);

        global $gReportPeriodSelectText;
        $gReportGetReportMap['prompt'] = I18N::getSingleToken($gReportPeriodSelectText);
        $gReportGetReportMap['recordno_label'] = [];
        $gReportGetReportMap['recordno_dbvalue'] = [];

        $objsCount = countArray($objs);
        for ($i = 0; $i < $objsCount; $i++) {
            $obj = $objs[$i];
            $gReportGetReportMap['recordno_label'][$obj['RECORD#']] = DBTokensHandler::getInstance()->getExternalLabel($obj['NAME']);
            $gReportGetReportMap['recordno_dbvalue'][$obj['RECORD#']] = $obj['NAME'];
        }
    }
    $prompt = $altPrompt ?: $gReportGetReportMap['prompt'] ?? '';
    $objMap = $includePrompt ? array('' => $prompt) : array();

    if (isNonEmptyArray($gReportGetReportMap)) {
        $format = $getTranslatedText ? 'recordno_label' : 'recordno_dbvalue';
        $selectedDataFormatArr = $gReportGetReportMap[$format];
        /* In order this to work, make sure we shouldn't have duplicate keys.*/
        $objMap = $objMap + $selectedDataFormatArr;
    }
    return $objMap;
}

/**
 * @param bool   $includePrompt
 * @param string $altPrompt
 *
 * @return array
 */
function GetBudgetReportingPeriodsMap($includePrompt = true, $altPrompt = '')
{    
    global $gReportPeriodSelectText;

    /** @noinspection PhpUnusedLocalVariableInspection */
    $cny = GetMyCompany();
    $entity = 'glbudgettype';
    $select = 'RECORD#, NAME, END_DATE - START_DATE as DATEDIFF';
    $order = 'DATETYPE, END_DATE DESC, DATEDIFF';

    $where = "STATUS = 'T' AND BUDGETING = 'T' ";

    $objs = GetNObjects($entity, $select, $where, $order);

    $prompt = $altPrompt ?: $gReportPeriodSelectText;
    $objMap = $includePrompt ? array('' => $prompt) : array();
    for ($i=0; $i<count($objs); $i++ ){
        $obj = $objs[$i];
        $objMap[$obj['RECORD#']] = $obj['NAME'];
    }
    return $objMap;
}

/**
 * @return array
 */
function GetCustomPeriods()
{
    /** @noinspection PhpUnusedLocalVariableInspection */
    $cny = GetMyCompany();
    $entity = 'glbudgettype';
    $select = 'NAME';
    $order = 'DATETYPE, END_DATE DESC';
    $where = "STATUS = 'T' AND ( DATETYPE = " . DATETMPL_CUSTOM . " ) ";
    $objs = GetNObjects($entity, $select, $where, $order);

    $CustomPeriods = array();
    for ($i=0; $i<count($objs); $i++ ){
        $CustomPeriods[] = isl_htmlspecialchars($objs[$i]['NAME']);
    }
    return $CustomPeriods;
}

/**
 * @param string $where
 *
 * @return array
 */
function GetPeriodMapByDates($where='')
{    

    $where .= "and STATUS = 'T' AND DATETYPE = " . DATETMPL_CUSTOM;

    $stmt = "select record#, name, start_date, end_date, end_date - start_date as datediff 
			from glbudgettype where cny# = :1 $where order by datetype, start_date, end_date desc";
    $objs = QueryResult(array($stmt, GetMyCompany()));

    $objMap = array('' => '-- Select Reporting Period --');
    for ($i=0; $i<count($objs); $i++ ){
        $obj = $objs[$i];
        $objMap[$obj['START_DATE'].'--'.$obj['END_DATE']] = $obj['NAME'];
    }
    return $objMap;
}

/**
 * @param string $modulekey
 * @param string $where
 *
 * @return array
 */
function GetLabelMap($modulekey = '', $where=" status='T' ")
{
    if ($modulekey != '') {
        $where .= $where ? " and " : "";
        $where .= " modulekey = '$modulekey' ";
    } 
    return GetObjectMap('accountlabel', 'RECORD#', 'LABEL', $where);
}

/**
 * @param string $modulekey
 *
 * @return array|bool
 */
function GetLabelMapDesc($modulekey = '')
{
    $where = " status='T'";
    if ($modulekey != '') {
        $where .= " and modulekey = '$modulekey' ";
    } else {
        global $gErr; $gErr->addError('SL-0193', __FILE__ . '.' . __LINE__, 'Invalid module', 'Invalid module or no module found for this record.');
        return false;
    }
    return GetObjectMap('accountlabel', 'DESCRIPTION', 'RECORD#', $where);
}

/**
 * @param string $modulekey
 *
 * @return array|bool
 */
function GetLabelMapLabel($modulekey = '')
{
    $where = " status='T'";
    if ($modulekey != '') {
        $where .= " and modulekey = '$modulekey' ";
    } else {
        global $gErr; $gErr->addError('SL-0194', __FILE__ . '.' . __LINE__, 'Invalid module', 'Invalid module or no module found for this record.');
        return false;
    }
    return GetObjectMap('accountlabel', 'LABEL', 'RECORD#', $where);
}


/**
 * @param string $modulekey
 *
 * @return array|bool
 */
function GetLabelMapAcct($modulekey = '')
{
    $filters = [];
    $filters [] = ['STATUS', '=', 'active'];
    if ($modulekey != '') {
        $filters[] = ['MODULEKEY', '=', $modulekey];
    } else {
        // MyLog("stack", "stack", 1);
        global $gErr; $gErr->addError('SL-0195', __FILE__ . '.' . __LINE__, 'Invalid module', 'Invalid module or no module found for this record.');
        return false;
    }

    $gManagerFactory = Globals::$g->gManagerFactory;
    $mgr = $gManagerFactory->getManager('accountlabel');

    $params = [
        'selects' => ['RECORDNO', 'GLACCOUNTNO'],
        'filters' => [$filters],
    ];

    $objs = $mgr->GetList($params);
    return Util::isEmptyCountable($objs) ? [] : array_column($objs, 'GLACCOUNTNO', 'RECORDNO');
}

/**
 * @param string $expensePaymentTypeKey
 *
 * @return string gl offsetkey associated with ExpensePaymentType, if any
 */
function getExpensePaymentTypeOffsetAccount($expensePaymentTypeKey) 
{
    $offsetKey = '';
    if (isset($expensePaymentTypeKey) && $expensePaymentTypeKey != '') {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $manager = $gManagerFactory->getManager('expensepaymenttype');
        $filters = array(
            'selects' => array('OFFSETACCTKEY'),
            'filters' => array(
                array(
                    array('RECORDNO', '=', $expensePaymentTypeKey)
                )
            )
        );
        $results = $manager->GetList($filters);
        $offsetKey = $results[0]['OFFSETACCTKEY'];
    }
    return $offsetKey;
}

/**
 * @param string $acctlabelkey
 *
 * @return string|null
 */
function GetLabelOffsetAcct($acctlabelkey)
{
    $where = array("record# = :2 and status = 'T'", $acctlabelkey);
    $res = GetNObjects('accountlabel', 'GLOFFSETKEY', $where);
    return $res[0]['GLOFFSETKEY'];
}

/**
 * This function will check for availability of child  record for given record#
 * This function is  called  while the record changes its status from active to inactive
 *
 * @param string $field
 * @param string $entity
 * @param int    $parentKey
 * @param string $recNo
 * @param string $label
 * @param bool   $addGenericError
 *
 * @return bool
 */
function CheckForChildRecord($field, $entity, $parentKey, $recNo, $label, $addGenericError = true)
{
    // When we make entity as inactive we should only check for child records which are active
    $field = $entity . ".$field ";
    $filter = $entity . ".status != 'F' and ";
    $where = [$filter . $entity . ".$parentKey = :2", $recNo];
    $output = GetNObjects($entity, $field, $where);
    
    if (!empty($output)) {
        if ($addGenericError) {
            $gErr = Globals::$g->gErr;
            $renamedLabel = $label;
            $gErr->addIAError(
                'SL-0678',
                __FILE__ . ':' . __LINE__,
                sprintf('Another active %s is referring this %s', $renamedLabel, $renamedLabel),
                ['GETINSTANCE_GETRENAMEDTEXT' => $renamedLabel],
                'Please make the child inactive and try again.',
                []
            );
        }
        
        return false;
    }
    
    return true;
}

/**
 * @param string $field
 * @param string $entity
 * @param string $parentrecno
 * @param string $label
 * @param string $offendingField  Path of field that is causing error (for error tagging).
 *
 * @return bool
 */
function CheckForParentRecord($field, $entity, $parentrecno, $label, $offendingField="")
{
    $gErr = Globals::$g->gErr;
    /** @noinspection PhpUnusedLocalVariableInspection */
    $cny = GetMyCompany();
    $field = $entity . ".$field ";

    //When we make entity as active we should only check for parent record which are inactive
    $filter = $entity . ".status = 'F' and ";
    $where = array($filter . $field ." = :2", $parentrecno);
    $output = GetNObjects($entity, $field, $where);
    
    if ( !empty($output) ) {
        $getRenamedTextLabel = $label;
        $gErr->addIAFieldError($offendingField, strtoupper($entity),
            'SL-0679',
            __FILE__ . ':' . __LINE__,
            'This '.$getRenamedTextLabel.' is associated with an inactive parent.', ['LABEL' => $getRenamedTextLabel],
            'Please make the parent active and try again.', []
        );
        
        return false;
    }
    
    return true;
}

/**
 * @param string $entity
 * @param string $keyField
 * @param string $value
 *
 * @return array[]
 */
function CheckforKeyInEntity($entity, $keyField, $value)
{
    $mgr = Globals::$g->gManagerFactory->getManager($entity);
    $params = [ 'filters' => [ [ [ $keyField, '=', $value ] ] ] ];

    return $mgr->GetList($params); 
}

/**
 * @param string $entity
 * @param string $typekey
 * @param string $recno
 *
 * @return array|false
 */
function CheckForKeyInVendorOrCustomer($entity, $typekey, $recno)
{
      // This function will check for vendortype key  in vendors or customertype key in customers
      // this function is  called  while the record changes its status from active to inactive
    /** @noinspection PhpUnusedLocalVariableInspection */
    $cny=GetMyCompany();
      $field=$entity.".name";
      $where= array($entity.".$typekey = :2", $recno);
      $output = GetNObjects($entity, $field, $where);
      return $output;
}

/**
 * @param string $recno
 * @param string $entity
 * @param string $field
 *
 * @return bool
 */
function CheckForTransaction($recno, $entity, $field)
{     
      // This function checks for pending bills / Invoices for a given department/location/accountlabel
       
      $cny=GetMyCompany();
      $query  = "  select unique prrecord.record#,prrecord.recordtype,prrecord.state,
               prrecord.totalpaid,prrecord.totalentered from prrecord prrecord,
	       prentry prentry,$entity $entity  where  prentry.cny#= '$cny'
	       and prrecord.cny#= '$cny' and $entity.cny#= '$cny' and prentry.$field='$recno' and prrecord.record#=prentry.recordkey  ";

      return FetchQuery($query);

}


/**
 * @param string $recno
 * @param string $rectype
 *
 * @return bool
 */
function CheckForBatchTransaction($recno, $rectype)
{
    //Checks for pending bills or invoice for given batch
    $cny=GetMyCompany();
    $query = " select unique prrecord.record#,prrecord.recordtype,prrecord.state,prrecord.totalpaid,
		 prrecord.totalentered from prrecord prrecord,prbatch prbatch where
		 prrecord.cny#=$cny and prbatch.cny#=$cny and prrecord.prbatchkey='$recno' 
		 and prrecord.recordtype = '$rectype' ";
     
    return FetchQuery($query);
}

/**
 * @param int $id
 * @param string $field
 *
 * @return bool
 */
function CheckForVendCustTermTransaction($id, $field)
{
    //Check for any transaction pending for given customer or vendor or Term
    $cny=GetMyCompany();
    $query = " select unique prrecord.record#,prrecord.recordtype,prrecord.state,prrecord.totalpaid,
		 prrecord.totalentered from prrecord prrecord  where prrecord.cny#=$cny 
		 and prrecord.$field = '$id' ";
    return FetchQuery($query);
    
}

/**
 * @param string $query
 *
 * @return bool
 */
function FetchQuery($query)
{
        // This function will execute the query and fetch the results
    $result = QueryResult($query);
    // 	eppp($query);
    // 	eppp("result..");eppp($result);
    for ($i=0;$i<count($result);$i++){
        if ($result[$i]['STATE'] != 'V' && $result[$i]['RECORDTYPE']!='pd' && $result[$i]['RECORDTYPE']!='rd') {
            if ($result[$i]['TOTALPAID'] != $result[$i]['TOTALENTERED']) {
                       return false;  
            }
        }
    }
    return true;
}

/**
 * @param string $entity
 * @param string $type
 * @param string $credit
 *
 * @return bool
 */
function GetEntityCreditLimit($entity, $type, &$credit)
{
    $validtypes = "vendor, customer";
    if(!preg_match('/' . str_replace("/", "\\/", $type) . '/', $validtypes)) {
        return false;
    }

    $select = "creditlimit as CREDITLIMIT";
    $entityid = ($type == 'customer' ? "customerid" : "vendorid");
    $where = array("$entityid = :2", addoraslashes($entity));
    /** @noinspection PhpUnusedLocalVariableInspection */
    $res = array();
    $res = GetNObjects($type, $select, $where);
    if(count($res) == 0) {
        return false;
    }
    $credit = $res[0]['CREDITLIMIT'];
    return true;
}



/**
 * returns the default offset account for a given recordtype
 *
 * @param string $recordtype        a valid recordtype
 *
 * @return string|false  record# of the gl offset account or false on errors
 */
function GetDefaultOsAcct($recordtype) 
{

    // 	epp(__FILE__.'.'.__LINE__);
    // 	epp("recordtype: $recordtype");

    $modprefs = array(
    'ei' => 'EI_ACCOUNT',
    'ea' => 'EI_ACCOUNT',
    'er' => 'ER_ACCOUNT',
    'pa' => 'PI_ACCOUNT',
    'pd' => 'PD_ACCOUNT',
    'pi' => 'PI_ACCOUNT',
    'pm' => 'PM_ACCOUNT',
    'pp' => 'PI_ACCOUNT',
    'pr' => 'PR_ACCOUNT',
    'ra' => 'RI_ACCOUNT',    
    'rd' => 'RD_ACCOUNT',
    'ri' => 'RI_ACCOUNT',
    'rm' => 'RM_ACCOUNT',
    'ro' => 'RO_ACCOUNT',
    'rp' => 'RI_ACCOUNT',
    'rr' => 'RR_ACCOUNT',
    'cp' => 'PI_ACCOUNT',
    'ci' => 'PI_ACCOUNT',
    'cq' => 'PI_ACCOUNT'
    );

    if (!in_array(isl_strtolower($recordtype), array_keys($modprefs))) {
        global $gErr; $gErr->addError('SL-0196', __FILE__ . '.' . __LINE__, 'Invalid or null recordtype.');
        return false;
    }

    // see if we found the result in the cache
    $moduleMap = array(
    'p' => '3.AP',
    'r' => '4.AR',
    'e' => '6.EE',
    'c' => '3.AP');
    $module = $moduleMap[isl_substr($recordtype, 0, 1)];

    return GetPreferenceForProperty($module, $modprefs[$recordtype]);

}

/**
 * @param string $recordtype
 *
 * @return PaymentManager
 */
function GetPaymentManager($recordtype)
{
    global $kAPid, $kEEid, $kARid, $kCMid;

    $gManagerFactory = Globals::$g->gManagerFactory;
    $pymtMgr = $gManagerFactory->getManager('payment');

    // GET A MODULE-SPECIFIC PAYMENT MANAGER
    $modulekey = $pymtMgr->GetModuleKeyForRecordtype($recordtype);
    switch ($modulekey) {
    case $kAPid:
        $pymtMgr = $gManagerFactory->getManager('appayment');
        break;
    case $kEEid:
        $pymtMgr = $gManagerFactory->getManager('eppayment');
        break;
    case $kARid:
        $pymtMgr = $gManagerFactory->getManager('arpayment');
        break;
    case $kCMid:
        $pymtMgr = $gManagerFactory->getManager('payment');
        break;
    }

    return $pymtMgr;
}

/**
 * @param string $mod
 * @param bool $isLabel
 *
 * @return string
 */
function PrintAutoFillArray($mod, $isLabel)
{
    if( $mod == 'ar' ) {
        $entMan = Globals::$g->gManagerFactory->getManager('customer');

        $params = array(
        'selects' => array('CUSTOMERID','NAME','ARACCOUNT','ARACCOUNTTITLE','ACCOUNTLABEL','DISPLAYCONTACT.CONTACTNAME', 'BILLTO.CONTACTNAME', 'SHIPTO.CONTACTNAME'), 
        'filters' => array (array (array( 'STATUS', 'in', array('active')))),
        'orders' => array(array('CUSTOMERID', 'asc')),
        );

        $entitylist = $entMan->GetList($params);

        $items=array();
        for($k=0; $k < count($entitylist); $k++) {
            if ($isLabel) {
                if ($entitylist[$k]['ACCOUNTLABEL'] == '' && $entitylist[$k]['ARACCOUNT']!='') {
                    $acct = $entitylist[$k]['ARACCOUNT'].'--'.$entitylist[$k]['ARACCOUNTTITLE'];
                }elseif($entitylist[$k]['ACCOUNTLABEL']!='') {
                    $acct = $entitylist[$k]['ACCOUNTLABEL'];
                }else{
                    $acct = '';
                }
            } else {
                if ($entitylist[$k]['ARACCOUNT']!='') {
                    $acct = $entitylist[$k]['ARACCOUNT'].'--'.$entitylist[$k]['ARACCOUNTTITLE'];
                }else{
                    $acct='';
                }
            }

            if (!$entitylist[$k]['BILLTO.CONTACTNAME']) {
                $entitylist[$k]['BILLTO.CONTACTNAME'] = $entitylist[$k]['DISPLAYCONTACT.CONTACTNAME'];
            }
            if (!$entitylist[$k]['SHIPTO.CONTACTNAME']) {
                $entitylist[$k]['SHIPTO.CONTACTNAME'] = $entitylist[$k]['DISPLAYCONTACT.CONTACTNAME'];
            }

            $items[] = "['".
            str_replace("'", "\'", $entitylist[$k]['CUSTOMERID'])."','".
            str_replace("'", "\'", $entitylist[$k]['NAME'])."','".
            str_replace("'", "\'", $acct)."','".
            str_replace("'", "\'", $entitylist[$k]['BILLTO.CONTACTNAME'])."','".
            str_replace("'", "\'", $entitylist[$k]['SHIPTO.CONTACTNAME']).
            "']";
        }
    } else {
        $entMan = Globals::$g->gManagerFactory->getManager('vendor');

        $params = array(
        'selects' => array('VENDORID','NAME','APACCOUNT','APACCOUNTTITLE','ACCOUNTLABEL', 'FORM1099BOX', 'ACCOUNTNO', 'DISPLAYCONTACT.CONTACTNAME', 'PAYTO.CONTACTNAME', 'RETURNTO.CONTACTNAME', 'VENDTYPE1099TYPE'), 
        'filters' => array (array (array( 'STATUS', 'in', array('active')))),
        'orders' => array(array('VENDORID', 'asc')),
        );

        $entitylist = $entMan->GetList($params);

        $items=array();
        for($k=0; $k < count($entitylist); $k++) {
            if ($isLabel) {
                if ($entitylist[$k]['ACCOUNTLABEL'] == '' && $entitylist[$k]['APACCOUNT']!='') {
                    $acct = $entitylist[$k]['APACCOUNT'].'--'.$entitylist[$k]['APACCOUNTTITLE'];
                }elseif($entitylist[$k]['ACCOUNTLABEL']!='') {
                    $acct = $entitylist[$k]['ACCOUNTLABEL'];
                }else{
                    $acct = '';
                }
            } else {
                if ($entitylist[$k]['APACCOUNT']!='') {
                    $acct = $entitylist[$k]['APACCOUNT'].'--'.$entitylist[$k]['APACCOUNTTITLE'];
                }else{
                    $acct='';
                }
            }
            if (!$entitylist[$k]['PAYTO.CONTACTNAME']) {
                $entitylist[$k]['PAYTO.CONTACTNAME'] = $entitylist[$k]['DISPLAYCONTACT.CONTACTNAME'];
            }
            if (!$entitylist[$k]['RETURNTO.CONTACTNAME']) {
                $entitylist[$k]['RETURNTO.CONTACTNAME'] = $entitylist[$k]['DISPLAYCONTACT.CONTACTNAME'];
            }
            if (!$entitylist[$k]['FORM1099BOX']) {
                $entitylist[$k]['FORM1099BOX'] = $entitylist[$k]['VENDTYPE1099TYPE'];
            }
            $items [] =  "['".
            str_replace("'", "\'", $entitylist[$k]['VENDORID'])."','".
            str_replace("'", "\'", $entitylist[$k]['NAME'])."','".
            str_replace("'", "\'", $acct)."','" .
            str_replace("'", "\'", $entitylist[$k]['PAYTO.CONTACTNAME'])."','".
            str_replace("'", "\'", $entitylist[$k]['RETURNTO.CONTACTNAME'])."','".
            $entitylist[$k]['FORM1099BOX']."','".
            str_replace("'", "\'", $entitylist[$k]['ACCOUNTNO']).
            "']";
        }
    }

    $items = "[" . join(',', $items) . "]";
    $js_items = "var itemdensearr = ".$items.";";

    $js_custdetail = 'var a=0; var custarr =  new Array();';    
    $labelStatus = $isLabel ? 1 : 0; 
    $js_labelstatus = "var isLabel = ". $labelStatus.";";

    $ret = $js_items;
    $ret .= $js_custdetail;
    $ret .= $js_labelstatus;

    return $ret;
}

 /*
  * XXX chak:
  * Get the valid Aging and future options. Do these methods
  * needs to be moved to CashViewAnalysisReport.cls file?
  */

/**
 * @return string
 */
function GetNoneOption()
{
    return "--None--";
}


/**
 * @return string
 */
function GetCustomOption()
{
    return "Custom";
}

/**
 * @return string
 */
function GetDefaultAgingOption()
{
    return "1-30,31-60,61-";
}

/**
 * @param string $arSelectOption
 *
 * @return array
 */
function GetValidAgingOptions(/** @noinspection PhpUnusedParameterInspection */ $arSelectOption)
{
    $ret = array();
    $ret[] = GetDefaultAgingOption();
    $ret[] = GetNoneOption();
    $ret[] = "1-7,8-14,15-21,22-28,29-";
    $ret[] = GetCustomOption();
    sort($ret);
    return $ret;
}

/**
 * @return string
 */
function GetDefaultFutureOption()
{
    return "0-7,8-14,15-21,22-28,29-";
}

/**
 * @param string $arSelectOption
 *
 * @return array
 */
function GetValidFutureOptions(/** @noinspection PhpUnusedParameterInspection */ $arSelectOption)
{
    $ret = array();
    $ret[] = GetDefaultFutureOption();
    $ret[] = GetNoneOption();
    $ret[] = "0-30,31-";
    $ret[] = "0-30,31-60,61-";
    $ret[] = GetCustomOption();
    sort($ret);
    return $ret;
}





function PrintAutoFillFunctions() 
{
    ?>

			var ID = 0;
			var DESC = 1;
			var ACCTNO = 2;
			var CONTACT1 = 3;
			var CONTACT2 = 4;
			var FORM = 5;
			var DESCR = 6;
		
			//Helper function for AutoFill to look up values in the array
			function AutoFillLookUp(arr, field, id) {

				var partialMatchBegIndex;
				var partialMatchMidIndex;
				
				numRec = arr.length;
				idlen = id.length;

				// Look for a case sensitive Complete Match first
				for (i=0; i < numRec; i++) {
					if (arr[i] != null && arr[i][field] == id){
						return i;
					}
				}

				// Look for a case insensitive Complete Match first
				for (i=0, j=0; i < numRec; i++) {
					if (arr[i] != null) {
						if (arr[i][field].toLowerCase() == id.toLowerCase()){
							return i;
						}
			
						//Look for a partial match, matching at Beginning
						if (arr[i][field].substring(0,idlen).toLowerCase() == id.toLowerCase()) {
							j++;
							partialMatchBegIndex = i;
						}

						// If a Beginning match is not found, Look for a partial match, matching within the string
						if (arr[i][field].toLowerCase().indexOf(id.toLowerCase()) != -1) {
							partialMatchMidIndex = i;
						}

					}
				}

				if (partialMatchBegIndex != null) {
					if ( j== 1) {
						return partialMatchBegIndex;
					}else{
						return (-1);
					}
				}

				if (partialMatchMidIndex != null) {
					return partialMatchMidIndex;
				}

				var name = id.split('--');
				var no = name[0];
				for (i=0; i < numRec; i++) {
					if(arr[i]!=null && arr[i][ID]==no) {
						return i;
					}
				}
				
				return (-1); //failed
			}
			
			function AppendEntityAutoFillData(ID, name, term, acct, contact1, contact2) {
				var newEntity = Array(ID, name, acct, contact1, contact2);
				itemdensearr[itemdensearr.length] = newEntity;
			}

			function AutoFill(from) {

                var mod = '<? $_mod = Request::$r->_mod; echo $_mod; ?>';
				var ap1099overridepref = '<?= AP1099Preference() ?>';
				custname = window.document.je.elements['_entity'];
				if (custname.value == '') { return;}

				contact1 = window.document.je.elements['_contact1'];

				if (mod == 'ar') {
					contact2 = window.document.je.elements['_contact2'];
                    vendCust     = 	window.document.je.elements['_CUSTOMERID[0]'];
				}
                else{
                    vendCust     = 	window.document.je.elements['_VENDORID[0]'];
                }

				if(isLabel == 0) {
					acct     = 	window.document.je.elements['_account[0]'];
				}
				else {
				    acct     = 	window.document.je.elements['_label[0]'];
				}
				
				indx = AutoFillLookUp(itemdensearr, ID, custname.value);

				if (indx < 0) { // error
					indx = AutoFillLookUp(itemdensearr,DESC, custname.value);
				}

				if (indx >= 0) {
					acct.value ='';
					window.document.je.elements['.amount[0]'].value = '';
					
					custname.value = itemdensearr[indx][ID] + '--' + itemdensearr[indx][DESC];
                    if(typeof(vendCust) != 'undefined')
                    {
                   		vendCust.value= custname.value;
                    }
					contact1.value = itemdensearr[indx][CONTACT1];
					
					if (mod == 'ar') {
						contact2.value = itemdensearr[indx][CONTACT2];
					}

					acct.value = itemdensearr[indx][ACCTNO];
					if(acct.onchange) {
						acct.onchange();
					}

					if(window.document.je.elements['.prdescription']) {
						window.document.je.elements['.prdescription'].value = itemdensearr[indx][DESCR] ? itemdensearr[indx][DESCR] : '';
					}

					if (itemdensearr[indx][FORM] != '') {
						Check1099FormBoxes(window.document.je, true, ap1099overridepref);
					} else {
						Check1099FormBoxes(window.document.je, false, ap1099overridepref);
					}
				}

				if(indx<0){
					var is_exist=0;
					var c_name;
					var cname= custname.value;
					var opr = cname.match('--');
					if(opr==null){
						opr	= cname.match('-');
						if (opr!=null){
							var splitvalue = cname.split('-');
							if (splitvalue[0]!= null && splitvalue[1]!= null) {
								custname.value = splitvalue[0]+'--'+splitvalue[1];
								// get the Account Information from the array using the ID field
								indx = AutoFillLookUp(itemdensearr, ID, splitvalue[0]);
								acct.value = itemdensearr[indx][ACCTNO];
								if(acct.onchange) {
									acct.onchange();
								}
								contact1.value = itemdensearr[indx][CONTACT1];
								if (mod == 'ar') {
									contact2.value = itemdensearr[indx][CONTACT2];
								}
							} 
						}
					}

					if(custarr.length > 0) { 
						for(i=0;i < custarr.length;i++){
							c_name =  (custname.value).split('--');
							if (c_name[0] =='') c_name[0] = (custname.value).split('-');
							if (c_name[0]=='') c_name[0] = custname.value;
							if(custarr[i]==trimString(c_name[0])){
								is_exist=1;
							}	
						}
					}

					if (!is_exist) {
                        var entity = '';
                        if (mod == "ap") {
                            var canCreateVendorCustomer = '<?= CheckAuthorization(GetOperationId('ap/lists/vendor/create'), 1) ?>';
                            entity = 'Vendor';
                        }
                         if (mod == "ar") {
                            var canCreateVendorCustomer = '<?= CheckAuthorization(GetOperationId('ar/lists/customer/create'), 1) ?>';
                            entity = 'Customer';
                        }
                      
						if (canCreateVendorCustomer == '1') {	
							var ccname =  (custname.value).split('--');
							if (ccname[0] =='') ccname[0] = (custname.value).split('-');
							if (ccname[0] =='') ccname[0] = custname.value;
							custarr[a] = trimString(ccname[0]);
                            Launch('quicksetup.phtml<? $_mod = Request::$r->_mod; $_op = Request::$r->_op; echo "?.op=$_op&.mod=$_mod"; ?>&.val='+escape(custname.value.encrypt()),'quicksetup',600,300);
                        }else {
                            alert(entity + ' '+custname.value+' not found. You do not have permission to create '+entity.toLowerCase()+'s.');
                            window.document.je.elements['_entity'].value =  '';
                            return false;
                        }
					}
					a++;
				}
			}

			function Check1099FormBoxes(form, checkBool, ap1099overridepref) {
				if (checkBool) {
					var bool = true;
				} else {
					var bool = false;
				}
				with (form) {
					var i = 0;
					while (elements['.form1099_helper['+i+']']) {
						elements['.form1099_helper['+i+']'].checked = bool;

                        if (ap1099overridepref == 'N') {
                            elements['.form1099_helper['+i+']'].disabled = true;
                        }
                        else {
                            elements['.form1099_helper['+i+']'].disabled = !bool;
                        }

                        elements['.form1099['+i+']'].value = bool;

						i++;
					}
				}
			}

            function onclick1099 (i) {
                var bool;

                if (window.document.je.elements['.form1099_helper['+i+']'].checked) {
                    bool = 'true';
                }
                else {
                    bool = 'false';
                }

                window.document.je.elements['.form1099['+i+']'].value = bool;
            }

			function trimString (str) {
			  while (str.charAt(0) == ' ')
			    str = str.substring(1);
			  while (str.charAt(str.length - 1) == ' ')
			    str = str.substring(0, str.length - 1);
			  return str;
			}
    <?
}


