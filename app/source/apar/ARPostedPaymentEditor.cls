<?
//===========================================================================
//	FILE:			ARPostedPaymentEditor.cls
//	AUTHOR:			rpn
//	DESCRIPTION:
//
//	(C) 2000, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	Corporation and is protected by the copyright laws.  Information
//	herein may not be used, copied or disclosed in whole or in part
//	without prior written consent from Intacct Corporation.
//===========================================================================

class ARPostedPaymentEditor extends SubLedgerPymtEditor
{

    /**
     * Initializing Class variables
     */
    protected function initClassVariables()
    {
        parent::initClassVariables();
        $this->addConfirmAction();
    }

    /**
     * @return void
     */
    protected function addIntoAdditionalTokens(): void
    {
        $this->additionalTokens[] = 'IA.MULTIPLE_CUSTOMER_SELECTED';
        parent::addIntoAdditionalTokens();
    }

    /**
     * Add the submit action to the ActionHandlers
     */
    protected function addConfirmAction()
    {
        $this->kActionHandlers['confirm'] = array(
            'handler' => 'ProcessConfirmAction',
            'states' => array(
                $this->kShowViewState
            ),
            'csrf' => true,
        );
    }

    /**
     * @return string
     */
    function getAuditEntity()
    {
        $entityMngr = $this->getEntityMgr();
        return $entityMngr->getAuditEntity();
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    protected function ProcessViewAction(&$_params){
        parent::ProcessViewAction($_params);
        $this->setAuditEntiy();
        return true;
    }

    private function setAuditEntiy(){
        $_r = Request::$r->{Globals::$g->kId};
        /* @var ARPostedPaymentManager $entityMngr */
        $entityMngr = $this->getEntityMgr();
        if ($_r ) {
            $paymentDetail = QueryResult(
                array("select 1 from pymtdetail where cny#=:1 and (paymentkey=:2 OR parentpaymentkey=:2)",
                      GetMyCompany(), $_r )
            );

            if ( $paymentDetail) {
                $entityMngr->setAuditEntiy('arpymt');
            }

            $recordType = QueryResult(
                array("select recordtype,totalentered from prrecordmst where cny#=:1 and record#=:2",
                      GetMyCompany(), $_r )
            );

            if($recordType[0]['RECORDTYPE'] == SubLedgerTxnManager::ARPOSTEDOVERPAYMENT_RECTYPE && $recordType[0]['TOTALENTERED'] > 0) {
                $entityMngr->setAuditEntiy('arpostedoverpayment');
            }

            if($recordType[0]['RECORDTYPE'] == PRRECORD_TYPE_RECEIPT_MULTI_ENTITY) {
                $entityMngr->setAuditEntiy('arpymt');
            }
        }
    }

    /**
     * Delivers current record in online as well as offline mode
     *
     * @param array         $_params  the editor params
     * @param bool          $offline  true if we are printing offline else false
     * @param string|false  $out      output when Request::$r->_deliverymethod pdf (the defautl)
     *
     * @return bool
     */
    public function Editor_Deliver(&$_params, $offline, &$out)
    {
        unset($_params['view']['child'][0]['pages']['child'][0]['page'][3]);
        parent::Editor_Deliver($_params, $offline, $out);
        return true;
    }

    /**
     * Return an array of javascript files to include into the page
     *
     * @return array the list of javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/arpostedpayment.js';
        return $jsFiles;
    }

    /**
     * @param string   $entity entity or object
     * @param string   $objId  entityid or objectid
     * @param string   $doctype
     * @param string[] $fields
     *
     * @return array|bool returns the formatted result set
     */
    protected function getEntityData($entity, $objId, $doctype='', $fields=null)
    {
        $entityData = parent::getEntityData($entity, $objId, $doctype, $fields);
        if (!empty($entityData) && $entityData['RECORDTYPE'] === 'rp'
            && isSpecified($entityData['MULTIENTITYPYMTKEY'])) {
            $msg = "This payment is part of a single payment for multiple customers.
            You can only view the entire payment for this transaction. ".$entityData['MULTIENTITYPYMTKEY'];
            Globals::$g->gErr->addError(
                'AR-0541',
                __FILE__ . ':' . __LINE__,
                $msg
            );
            $this->state = $this->kErrorState;
            $entityData = false;
        }
        return $entityData;
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state
     *
     * @param array $obj the data
     *
     * @return bool
     */
    public function mediateDataAndMetadata(&$obj)
    {
        if(empty($obj['CREDITS_TOTALS'])){
            $obj['CREDITS_TOTALS']['TOTALENTERED']=0;
            $obj['CREDITS_TOTALS']['TOTALDUE']=0;
            $obj['CREDITS_TOTALS']['APPLIEDAMOUNT']=0;
        }
        $ok = $this->setDefaultShowHideFields($obj);
        return $ok && parent::mediateDataAndMetadata($obj);

    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    private function setDefaultShowHideFields(array &$obj):bool
    {
        $view = $this->getView();
        if ($obj['RECORDTYPE'] !== PRRECORD_TYPE_RECEIPT_MULTI_ENTITY) {
            $view->findAndSetProperty(['path' => 'PAYERNAME'], ['hidden' => true]);
        } else {
            $this->findAndSetPropertyOtherThenGridColumn($view, ['path' => 'CUSTOMERID'], ['hidden' => true]);
            $this->findAndSetPropertyOtherThenGridColumn($view, ['path' => 'CUSTOMERNAME'], ['hidden' => true]);
            $view->findAndSetProperty(['path' => 'MULTIPLECUSTOMERID'], ['hidden' => false]);

            if (empty($obj['MULTIPLECUSTOMERS'])) {
                return true;
            }
            if (is_array($obj['MULTIPLECUSTOMERS'])) {
                $placeHolder = [
                    ['name' => 'COUNT', 'value' => countArray($obj['MULTIPLECUSTOMERS'])]
                ];
                $obj['MULTIPLECUSTOMERS'] = implode('#~#', $obj['MULTIPLECUSTOMERS']);
            } else {
                $placeHolder = [
                    ['name' => 'COUNT', 'value' => countArray(explode('#~#', $obj['MULTIPLECUSTOMERS']))]
                ];
            }
            $obj['MULTIPLECUSTOMERID'] = I18N::getSingleToken('IA.MULTIPLE_CUSTOMER_SELECTED', $placeHolder);
        }
        // While printing, adjustting below fields
        if ($this->getState() === Editor_DeliverState) {
            $view->findAndSetProperty(['path' => 'CUSTOMERID'], ['hidden' => true], EditorComponentFactory::TYPE_GRID_COLUMN);
            $view->findAndSetProperty(['path' => 'WHENDUE'], ['hidden' => true], EditorComponentFactory::TYPE_GRID_COLUMN);
            $view->findAndSetProperty(['path' => 'DISCOUNTAMOUNT'], ['fullname' => 'IA.DISCOUNT'], EditorComponentFactory::TYPE_GRID_COLUMN);
            $view->findAndSetProperty(['path' => 'PAYMENTAMOUNT'], ['fullname' => 'IA.PAYMENT'], EditorComponentFactory::TYPE_GRID_COLUMN);
            $view->findAndSetProperty(['path' => 'CREDITAMOUNT'], ['fullname' => 'IA.CREDIT'], EditorComponentFactory::TYPE_GRID_COLUMN);
            $view->findAndSetProperty(['path' => 'CUSTOMERNAME'], ['fullname' => 'IA.CUSTOMER'], EditorComponentFactory::TYPE_GRID_COLUMN);
        }
        return true;
    }

    /**
     * @param EditorView $view
     * @param array      $searchParams
     * @param array      $propVals
     * @param string     $type
     *
     * @return void
     */
    private function findAndSetPropertyOtherThenGridColumn(
        EditorView $view, array $searchParams,
        array $propVals, string $type = EditorComponentFactory::TYPE_FIELD
    ) {
        if ( ! empty($searchParams) && ! empty($propVals) ) {
            // Only continue if the search parameters AND property values are provided
            $matches = [];
            $view->findComponents($searchParams, $type, $matches);
            foreach ( $matches as $match ) {
                $parent = $match->getParent();
                $compType = $parent->getComponentType();
                if ($compType === EditorComponentFactory::TYPE_GRID_COLUMN) {
                    continue;
                }
                foreach ( $propVals as $property => $value ) {
                    $match->setProperty($property, $value);
                }
            }
        }
    }

    /**
     * Overridden function to change data before printing
     *
     * @param array &$values Object details
     */
    protected function prepareDataForPrinting(&$values)
    {
        parent::prepareDataForPrinting($values);

        $mgr = Globals::$g->gManagerFactory->getManager('arpostedpayment');
        $all = $mgr->removeCommomFields();
        $invoice = $mgr->removeInvoiceFields();
        $credit = $mgr->removeCreditFields();

        foreach ( $all as $value ) {
            unset($values[$value]);
        }
        foreach ( $values['INVOICES'] as $key => $value ) {
            $values['INVOICES'][$key]['DRILLDOWN'] = $value['RECORDID'];
            $invoiceEntity = $value['RECORDTYPE'] == 'ra' ? 'aradjustment' : 'arinvoice';
            $this->formatDataForPrinting($values['INVOICES'][$key],$invoiceEntity);

            foreach ( $invoice as $value1 ) {
                unset($values['INVOICES'][$key][$value1]);
            }
        }

        foreach ( $values['CREDITS'] as $key => $value ) {
            $values['CREDITS'][$key]['DRILLDOWN'] = $value['RECORDID'];

            foreach ( $credit as $value1 ) {
                unset($values['CREDITS'][$key][$value1]);
            }
        }

        $this->formatDataForPrinting($values, 'arpostedpayment');
    }

    /**
     * Prepare / format the data for printing
     *
     * @param array $values the printing data
     */
    private function formatDataForPrinting(&$values,$entity){
        $entityMgr = Globals::$g->gManagerFactory->getManager($entity);

        foreach ( $values as $fieldPath => $value ) {
            $fieldInfo = $entityMgr->GetFieldInfo($fieldPath);

            if ( $fieldInfo['type']['ptype'] == 'date' ) {
                $values[$fieldPath] = FormatDateForDisplay($value);
            } else if ( $fieldInfo['type']['ptype'] == 'timestamp' ) {
                $values[$fieldPath] = FormatTimestampForDisplay($value);
            }

        }
    }

    /**
     * @return bool
     */
    protected function includeGLPostingTab()
    {
        return true;
    }

    /**
     * Get the GL posting details for the Advance transaction
     */
    protected function ajaxGLPostingDetails()
    {
        $values['RECORDNO'] = Request::$r->{'recordId'};
        if (empty($values['RECORDNO'])) {
            echo '';
            return;
        }
        $isGLOperationAllowed = IsOperationAllowed(GetOperationId('gl/lists/glbatch/view'));
        $response = [];
        if ($isGLOperationAllowed) {
            //populate GL posting data
            //$mgr = $this->getEntityMgr();
            $recordKeys = [$values['RECORDNO']];
            $recordType = PRRecordManager::getRecordType($values['RECORDNO']);
            if (!empty($recordType) && $recordType === PRRECORD_TYPE_RECEIPT_MULTI_ENTITY) {
                $recordKeys = ARMultiCustomerPymtManager::getChildPaymentRecordsByParent($values['RECORDNO']);
            }
            if (empty($recordKeys)) {
                echo '';
                return;
            }
            $this->getGLPostingInfo($values, $recordKeys);
            $response['GL_BATCHES_LIST'] = $values['GL_BATCHES_LIST'];
            $response['GL_BATCHES_IET_LIST'] = $values['GL_BATCHES_IET_LIST'];
        }
        echo json_encode($response);
    }

    /**
     * @param array  $values
     * @param array $recordKeys
     */
    private function getGLPostingInfo(&$values, array $recordKeys)
    {
        $prglpostingmgr = Globals::$g->gManagerFactory->getManager('prglposting');
        $glLinkRecords = $prglpostingmgr->getGlPostingsByRecordKeys($recordKeys);

        if ( $glLinkRecords && ! empty($glLinkRecords) && ! empty($recordKeys) ) {
            $subledgerGL = I18N::getSingleToken('IA.SUBLEDGER_GL');
            $prglpostingmgr->updateGLPostingDetails($glLinkRecords, $subledgerGL);
            $values['GL_BATCHES_LIST'] = $glLinkRecords;
            $batchRecordNumbers = [];
            foreach ( $glLinkRecords as $glBatch ) {
                $batchRecordNumbers[] = $glBatch['BATCHNO'];
            }
            //get unique GL batch numbers
            $batchRecordNumbers = array_unique($batchRecordNumbers);
            $glLinkIetRecords =
                $prglpostingmgr->getIetPostingsByRecordKeys($batchRecordNumbers, $recordKeys);

            if ( $glLinkIetRecords ) {
                $prglpostingmgr->updateGLPostingDetails($glLinkIetRecords, $subledgerGL);
                $values['GL_BATCHES_IET_LIST'] = $glLinkIetRecords;
            }
        }
    }

    /**
     * @return bool
     */
    protected function CanPost(): bool
    {
        // only in case of view state submit is allowed
        $rawState = EntityManager::AccessByPath($this->getViewData(), 'RAWSTATE');
        if ($rawState === ARPymtManager::DRAFT_RAWSTATE &&
            CheckAuthorization(GetOperationId('ar/lists/arpymt/post'), 1)
        ) {
            return true;
        }
        return false;
    }

    /**
     * @return bool
     */
    protected function CanEditDraftPymt(): bool
    {
        $rawState = EntityManager::AccessByPath($this->getViewData(), 'RAWSTATE');
        if ($rawState === ARPymtManager::DRAFT_RAWSTATE &&
            CheckAuthorization(GetOperationId('ar/lists/arpymt/create'), 1)
        ) {
            return true;
        }
        return false;
    }

    /**
     * @param string $state
     *
     * @return array
     */
    public function getStandardButtons($state)
    {
        $buttons = [];
        if ($state === Editor_ShowViewState) {
            if( array_key_exists('confirm', $this->kActionHandlers) && $this->CanPost() ) {
                $this->setButtonDetails($buttons, 'postbuttonid', 'submitbutton', GT($this->textMap, 'IA.POST'), 'confirm');
            }
            if ($this->CanEditDraftPymt()) {
                $url = URLEncryption::buildUrl('editor.phtml',
                    [
                        '.do' => 'edit',
                        '.r' => Request::$r->{Globals::$g->kId},
                        '.op' => GetOperationId('ar/lists/arpymt/create'),
                        '.sess' => Session::getKey(),
                    ]);
                $jsCode = "window.location.href = '" . util_jsStrEncode($url) . "';";
                $this->setButtonDetails($buttons, 'editbuttid', 'editbutton', GT($this->textMap, 'IA.EDIT'),
                    '', false, $jsCode, false);
            }
        }
        $values = parent::getStandardButtons($state);

        return INTACCTarray_merge($buttons, $values);
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    protected function ProcessConfirmAction(&$_params)
    {
        $entityMgr = $this->GetManager('arpymt');

        if ($this->ProcessErrorRetrivalAction($entityMgr)) {
            return true;
        }

        $ok = true;
        // get the payment id
        $objId = Request::$r->{Globals::$g->kId};
        $ok = $ok && $entityMgr->submitPayment($objId);

        if ($ok) {
            // Clear the warning cache struncture WHEN operation is successful
            Globals::$g->gErr->warningsCache->clear();
            // add the message to display on the lister page
            $msg = I18N::getSingleToken('IA.PAYMENT_TRANSACTION_SUCCESSFULLY_CONFIRMED');
            $warn = ['action' => 'arpymt_create', 'success' => 1, 'msg' => $msg];
            Globals::$g->gErr->AddWarning(json_encode($warn));
            $_readtime = ServeCurrentTimestamp(1);
            $warningTime = 'LWARNREC-' . $_readtime;
            $values = [
                'PROPERTY'   => $warningTime,
                'TYPE'       => 'Session',
                'OBJECTDATA' => Globals::$g->gErr->warnings[0],
            ];
            $objectStore = Globals::$g->gManagerFactory->getManager('objectstore');
            if ( ! $objectStore->get($warningTime) ) {
                $objectStore->add($values);
            }
            // update the done url to append the errorTimeStamp
            $_done= Request::$r->_done;
            $_done = preg_replace('/([&|?].errorTimeStamp=)[^&]*/ix', '', $_done);
            $_done = $_done . '&.errorTimeStamp=' . urlencode($_readtime);
            Request::$r->_done = $_done;
            $this->done = $_done;
        }

        if ( !$ok ) {
            $this->state = $this->kErrorState;
        } else if (Globals::$g->gURLs->HasReturnPath()) {
            $this->state = $this->kGoBackState;
        } else if ($_params['popup'] || $_params['ydialog']) {
            $this->state = $this->kCloseState;
        } else {
            $this->state = $this->kGoHomeState;
        }

        return $ok;
    }
}
