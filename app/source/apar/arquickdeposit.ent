<?php

/**
 * Entity for the AR Quick Deposit object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */
$kSchemas['arquickdeposit'] = array(
    'children' => array(
        'bankaccount' => array(
            'fkey' => 'financialentity', 'invfkey' => 'accountid', 'join' => 'outer', 'table' => 'bankaccount'
        ),
        'customer' => array('fkey' => 'entity', 'invfkey' => 'entity', 'table' => 'customermst'),
        'payto' => array(
            'fkey' => 'billtopaytokey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'contactversion'
        ),
        'shipto' => array(
            'fkey' => 'shiptoreturntokey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'contactversion',
            'children' => array(
                'taxgroup' => array(
                    'fkey' => 'taxgroupkey', 'invfkey' => 'record#', 'table' => 'taxgrp', 'join' => 'outer',
                ),
            ),
        ),
        'invoicepayment' => array(
            'fkey' => 'record#', 'invfkey' => 'paymentkey', 'table' => 'prpaymentrecords',
            'children' => array(
                'arinvoice' => array('fkey' => 'recordkey', 'invfkey' => 'record#', 'table' => 'prrecord')
            )
        ),
        'batch' => array(
            'fkey' => 'prbatchkey', 'invfkey' => 'record#', 'table' => 'prbatchmst'
        ),
        'taxsolution' => array(
            'fkey' => 'taxsolutionkey', 'invfkey' => 'record#', 'table' => 'taxsolution', 'join' => 'outer',
        ),
    ),
    'nexus' => array(
        'shipto' => array(
            'object' => 'contactversion',
            'relation' => MANY2ONE,
            'field' => 'shiptoreturntokey',
            'printas' => 'IA.SHIP_TO_CONTACT',
        ),
    ),
    'object' => array(
        'RECORDNO',
        'RECORDTYPE',
        'WHENCREATED',
        'CONTACTTAXGROUP',
        'CUSTOMERID',
        'CUSTOMERRECORDNO',
        'CUSTOMERNAME',
        'FINANCIALENTITY',
        'BANKNAME',
        'DOCNUMBER',
        'DEPOSITID',
        'DESCRIPTION',
        'BILLTOPAYTOCONTACTNAME',
        'SHIPTORETURNTOCONTACTNAME',
        'BILLTOPAYTOKEY',
        'SHIPTORETURNTOKEY',
        'BASECURR',
        'CURRENCY',
        'TOTALENTERED',
        'TRX_TOTALENTERED',
        'TOTALPAID',
        'TRX_TOTALPAID',
        'WHENPAID',
        'STATE',
        'PRBATCH',
        'PRBATCHKEY',
        'AUWHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'TAXSOLUTIONKEY',
        'TAXSOLUTIONID',
        'SHOWMULTILINETAX',
        'SHIPTO.TAXGROUP.NAME',
        'SHIPTO.TAXGROUP.RECORDNO',
        'SHIPTO.TAXID',
        'TAXMETHOD',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'RECORDTYPE' => 'recordtype',
        'WHENCREATED' => 'whencreated',
        'CONTACTTAXGROUP' => 'taxgroup.name',
        'CUSTOMERID' => 'customer.customerid',
        'CUSTOMERRECORDNO' => 'customer.record#',
        'CUSTOMERNAME' => 'customer.name',
        'FINANCIALENTITY' => 'financialentity',
        'BANKNAME' => 'bankaccount.name',
        'DOCNUMBER' => 'arinvoice.recordid', // This is coming from the ri record
        'DEPOSITID' => 'recordid',
        'DESCRIPTION' => 'description',
        'BILLTOPAYTOCONTACTNAME' => 'payto.name',
        'SHIPTORETURNTOCONTACTNAME' => 'shipto.name',
        'BILLTOPAYTOKEY' => 'billtopaytokey',
        'SHIPTORETURNTOKEY' => 'shiptoreturntokey',
        'BASECURR' => 'basecurr',
        'CURRENCY' => 'currency',
        'TOTALENTERED' => 'totalentered',
        'TRX_TOTALENTERED' => 'trx_totalentered',
        'TOTALPAID' => 'totalpaid',
        'TRX_TOTALPAID' => 'trx_totalpaid',
        'WHENPAID' => 'whenpaid',
        'STATE' => 'state',
        'PRBATCH' => 'batch.title',
        'PRBATCHKEY' => 'prbatchkey',
        'AUWHENCREATED' => 'auwhencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'TAXSOLUTIONKEY' => 'taxsolutionkey',
        'TAXSOLUTIONID' => 'taxsolution.solutionid',
        'SHOWMULTILINETAX' => 'taxsolution.showmultilinetax',
        'TAXMETHOD' => 'taxsolution.taxmethod',
        'RETAINAGEPERCENTAGE' => 'retainagepercentage',
        'TRX_TOTALRETAINED' => 'trx_totalretained',
        'TOTALRETAINED' => 'totalretained',
        'TRX_TOTALRELEASED' => 'trx_totalreleased',
        'SHIPTO' => array(
            'contactversion.*' => 'shipto.*',
            'TAXGROUP' => array(
                'taxgroup.*' => 'taxgroup.*',
            ),
        ),
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'AUWHENCREATED',
        'WHENMODIFIED'
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'RECORDKEY',
            'invfkey' => 'RECORDNO',
            'minLinesRequired' => 1,
            'entity' => 'arquickdepositentry',
            'path' => 'ITEMS'
        )
    ),
    'fieldinfo' => array(
        $gRecordNoHiddenFieldInfo,
        array(
            'path' => 'RECORDTYPE',
            'fullname' => 'IA.RECORD_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 2
            ),
            'hidden' => true,
            'id' => 1
        ),
        array(
            'path' => 'WHENCREATED',
            'fullname' => 'IA.DATE',
            'type' => $gDateType,
            'required' => true,
            'id' => 2
        ),
        array(
            'path' => 'CUSTOMERID',
            'fullname' => 'IA.CUSTOMER_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'customer',
                'pickentity' => 'customerpick',
                'pickfield' => array(
                    'PICKID', 'CUSTOMERID', 'NAME', 'ACCOUNTLABEL', 'ARACCOUNT', 'ARACCOUNTTITLE',
                    'DISPLAYCONTACT.CONTACTNAME', 'BILLTO.CONTACTNAME', 'SHIPTO.CONTACTNAME', 'ONHOLD',
                    'CREDITLIMIT', 'TOTALDUE', 'DISPLAYCONTACT.TAXGROUP',
                    'DISPLAYCONTACT.TAXID', 'SHIPTO.TAXGROUP', 'SHIPTO.TAXID'
                )
            ),
            'required' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'renameable' => true,
            'id' => 3
        ),
        array(
            'path' => 'CUSTOMERNAME',
            'fullname' => 'IA.CUSTOMER_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
                'format' => '/^.{0,100}$/'
            ),
            'renameable' => true,
            'id' => 4
        ),
        array(
            'path' => 'FINANCIALENTITY',
            'fullname' => 'IA.BANK',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'bankaccount',
                'pickentity' => 'bankaccountpick',
                'filterForCurrency' => true,
                'pick_url' => 'picker.phtml?.filterForCurrency=1',
                'pickfield' => array('BANKACCOUNTID', 'BANKNAME', 'CURRENCY'),
                'restrict' => array(
                    array(
                        'pickField' => 'CURRENCY',
                        'value' => array(GetBaseCurrency()), // Base currency bank only.
                                                             // This screen is not allowed for Atlas at root.
                        'nulls' => true
                    )
                )
            ),
            'required' => true,
            'id' => 5
        ),
        array(
            'path' => 'BANKACCOUNTNAME',
            'fullname' => 'IA.BANK_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'id' => 23
        ),
        array(
            'path' => 'DOCNUMBER',
            'fullname' => 'IA.INVOICE_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 45
            ),
            'id' => 6
        ),
        array(
            'path' => 'DEPOSITID',
            'fullname' => 'IA.DEPOSIT_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50
            ),
            'id' => 7
        ),
        array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext',
                'size' => 60,
                'maxlength' => 1000
            ),
            'numofcols' => 60,
            'id' => 8
        ),
        array(
            'path' => 'BILLTOPAYTOCONTACTNAME',
            'fullname' => 'IA.BILL_TO',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'noSplit' => true,
                'entity' => 'contact',
                'format' => $gContactNameFormat,
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2', 'MAILADDRESS.ADDRESS3',
                    'MAILADDRESS.CITY', 'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'EMAIL1', 'TAXID', 'TAXGROUP'
                )
            ),
            'id' => 9
        ),
        array(
            'path' => 'SHIPTORETURNTOCONTACTNAME',
            'fullname' => 'IA.SHIP_TO',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'noSplit' => true,
                'entity' => 'contact',
                'format' => $gContactNameFormat,
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2', 'MAILADDRESS.ADDRESS3',
                    'MAILADDRESS.CITY', 'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'EMAIL1', 'TAXID', 'TAXGROUP'
                )
            ),
            'id' => 10
        ),
        array(
            'path' => 'BILLTOPAYTOKEY',
            'fullname' => 'IA.PAYTO_CONTACT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'readonly' => true,
            'hidden' => true,
            'id' => 22
        ),
        array(
            'path' => 'SHIPTORETURNTOKEY',
            'fullname' => 'IA.RETURNTO_CONTACT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'readonly' => true,
            'hidden' => true,
            'id' => 24
        ),
        array(
            'path' => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text'
            ),
            'required' => true,
            'readonly' => true,
            'hidden' => true,
            'id' => 11
        ),
        array(
            'path' => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'required' => true,
            'hidden' => true,
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'id' => 12
        ),
        array(
            'path' => 'TOTALENTERED',
            'fullname' => 'IA.TOTAL_BASE_AMOUNT',
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 13
        ),
        array(
            'path' => 'TRX_TOTALENTERED',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT',
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 14
        ),
        array(
            'path' => 'TOTALPAID',
            'fullname' => 'IA.TOTAL_PAID',
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 15
        ),
        array(
            'path' => 'TRX_TOTALPAID',
            'fullname' => 'IA.TOTAL_TRANSACTION_PAID',
            'type' => array(
                'type' => 'currency',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 16
        ),
        array(
            'path' => 'WHENPAID',
            'fullname' => 'IA.DATE_FULLY_PAID',
            'type' => $gDateType,
            'id' => 17
        ),
        array(
            'path' => 'STATE',
            'fullname' => 'IA.STATE',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validlabels' => array('IA.CONFIRMED'),
                'validvalues' => array('Confirmed'),
                '_validivalues' => array('C')
            ),
            'readonly' => true,
            'id' => 18
        ),
        array(
            'path' => 'PRBATCH',
            'fullname' => 'IA.PAYMENT_SUMMARY',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'arquickdepositbatch',
                'maxlength' => 100
            ),
            'id' => 19
        ),
        array(
            'path' => 'PRBATCHKEY',
            'fullname' => 'IA.PAYMENT_SUMMARY_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 25
        ),
        // --
        // -- START UI ONLY FIELDS
        // --
        array(
            'path' => 'BILLTOPAYTOADDRESS',
            'type' => array(
                'type' => 'multitext',
                'ptype' => 'multitext'
            ),
            'readonly' => true
        ),
        array(
            'path' => 'SHIPTORETURNTOADDRESS',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext'
            ),
            'readonly' => true
        ),
        array(
            'path' => 'SUPDOCID',
            'fullname' => 'IA.ATTACHMENT',
            'type' => array(
                'ptype' => 'supdocptr',
                'type' => 'supdocptr',
                'maxlength' => 20,
                'listAction' => 'pick'
            ),
            'noedit' => false
        ),
        // --
        // -- END UI ONLY FIELDS
        // --
        // VAT fields
        array(
            'path' => 'TAXID',
            'fullname' => 'IA.TAX_ID',
            'desc' => 'IA.TAX_IDENTIFICATION_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => '/^.{0,20}$/'
            ),
            'readonly' => true,
            'showInGroup' => true,
            'id' => 26
        ),
        array(
            'fullname' => 'IA.TAX_SOLUTION',
            'desc' => 'IA.TAX_SOLUTION',
            'path' => 'TAXSOLUTIONID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'taxsolution',
                'pickfield' => array('SOLUTIONID', 'SHOWMULTILINETAX', 'TAXMETHOD', 'RECORDNO'),
                'restrict' => array(
                    array(
                        'pickField' => 'TAXMETHOD',
                        'operand' => 'IN',
                        'value' => ARQuickDepositManager::getTaxImplicationTaxMethodsHelper(false),
                    ),
                )
            ),
            'nonew' => true,
            'id' => 28,
        ),
        array(
            'fullname' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'desc' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'path' => 'TAXIMPLICATIONS',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 29
        ),
        array(
            'path' => 'CONTACTTAXGROUP',
            'fullname' => 'IA.CONTACT_TAX_GROUP',
            'desc' => 'IA.THE_VENDOR_PAYTO_CONTACT_TAX_GROUP',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'taxgroup',
            ),
            'readonly' => true,
            'id' => 30
        ),
        array(
            'path' => 'TAXID',
            'fullname' => 'IA.TAX_ID',
            'desc' => 'IA.TAX_IDENTIFICATION_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => '/^.{0,20}$/'
            ),
            'readonly' => true,
            'showInGroup' => true,
            'id' => 31
        ),
        $gAUWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo
    ),
    'primaryDimensions' => array('CUSTOMERID' => 'CUSTOMERID'),
    'primaryDimensionKey' => ['customer' => ['CUSTOMERKEY' => 'CUSTOMERID']],
    'pairedFields' => array(
        'CUSTOMERID' => 'CUSTOMERNAME',
        'FINANCIALENTITY' => 'BANKNAME'
    ),
    'printas' => 'IA.MANUAL_DEPOSIT',
    'pluralprintas' => 'IA.MANUAL_DEPOSITS',
    'table' => 'prrecord',
    'updatetable' => 'prrecordmst',
    'module' => 'ar',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'supdocentity' => 'ARPAYMENT',
    'atlasentityonly' => true,
    'auditcolumns' => true,
    'customComponentsEntity' => 'arinvoice',
    'platform_entity' => 'arpayment',
    'dbfilters' => array(
        array(
            'arquickdeposit.recordtype',
            '=',
            SubLedgerTxnManager::QUICKDEPOSIT_RECTYPE
        ),
        array(
            'arquickdeposit.systemgenerated',
             '=',
            'T'
        )    
    ),
    'description' => 'IA.HEADER_INFORMATION_FOR_MANUAL_DEPOSITS_DESC',
);
require 'taxsummary.ent';
$kSchemas['arquickdeposit'] = EntityManager::inheritEnts($kSchemas['taxsummary'], $kSchemas['arquickdeposit']);
