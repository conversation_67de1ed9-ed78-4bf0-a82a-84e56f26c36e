<?php

/*
*
*	FILE:			APARAgeReporter.cls
*	AUTHOR:			tgw
*	DESCRIPTION:	extends the base class for all APAR aging reports.
*					 
*	(C)2001, Intacct Corporation, All Rights Reserved
*
*	Intacct Corporation Proprietary Information.
*	This document contains trade secret data that belongs to Intacct 
*	corporation and is protected by the copyright laws. Information herein 
*	may not be used, copied or disclosed in whole or part without prior 
*	written consent from Intacct Corporation.
*
*/


import('Request');
import('UIControl');
import('GroupReporter');

require_once 'prrecordhistory.inc';
require_once 'std_reports.inc';
require_once 'backend_dates.inc';
require_once 'backend_cashmgmt.inc';
require_once 'backend_customer.inc';
require_once 'backend_employee.inc';
require_once 'graph_wiz.inc';
require_once 'html_header.inc';
require_once 'common_apar.inc';
require_once 'Dictionary.cls';


class APARAgeReporter extends GroupReporter
{

    const PRRECORD_SORT_FIELDS = [
        'AMOUNT',
        'AMOUNT_DESC',
        'GLPOSTDATE',
        'GLPOSTDATE_DESC',
    ];

    const ENTITY_SORT_FIELDS = [
        'NAME',
        'CONTACT.LASTNAME',
        'TITLE',
        'VENDORID',
        'EMPLOYEEID',
        'CUSTOMERID',
    ];

    /** @var string $_type */
    protected string $_type;

    /** @var bool $_useEntityFilter */
    protected bool $_useEntityFilter = false;

    /**
     * Map used fetch the DB col names and labels
     *
     * @var string[][] $_groupmap
     */
    var $_groupmap;

    /**
     * Will contain the additional info like name mapped with IDs
     *
     * @var string[][] $_vendcustNameMap
     */
    var $_vendcustNameMap = array();

    /**
     * Following keys needs to be pushed into our result array
     * @var string[] $_pushTypeKeys
     */
    var $_pushTypeKeys = array('VENDTYPEKEY', 'CUSTTYPEKEY', 'EMPTYPEKEY');

    /**
     * @var string[] $_payprioritymap
     */
    var $_payprioritymap = array(
        'U' => 'Urgent',
        'H'    => 'High',
        'N' => 'Normal',
        'L' => 'Low'
    );
    
    /**
     * set to true only if summary mode is on and group by option is selected
     *
     * @var bool $_isGrpBySumm
     */
    var $_isGrpBySumm = false;

    /**
     * @var string[] $nolinks
     */
    var $nolinks;

    /**
     * @var bool $atlas
     */
    var $atlas;

    /**
     * @var string[] $typeInfo
     */
    var $typeInfo;

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        $this->_groupmap = array(
            'vendor' => array(
                'FldIdName' => 'VENDORID',
                'Label' => array('NAME' => 'IA.VENDOR_NAME',
                    'VENDORID' => 'IA.VENDOR_ID',
                    'None' => 'IA.NONE',
                    'VENDTYPE' => 'IA.VENDOR_TYPE',
                    'PAYMENTPRIORITY' => 'IA.PAYMENT_PRIORITY',
                    'DUEDATE' => 'IA.DUE_DATE',
                    'GLPOSTDATE' => 'IA.GL_POSTING_DATE'),
                'DB' => array('NAME' => 'NAME',
                    'VENDORID' => 'ENTITY',
                    'None' => 'ENTITY',
                    'VENDTYPE' => 'VENDTYPEKEY',
                    'PAYMENTPRIORITY' => 'PAYMENTPRIORITY',
                    'DUEDATE' => 'WHENDUE',
                    'GLPOSTDATE' => 'CREATED')
            ),
            'vendorgraph' => array(
                'FldIdName' => 'VENDORID',
                'Label' => array('None' => 'IA.NONE'),
                'DB' => array('None' => 'ENTITY')
            ),
            'customer' => array(
                'FldIdName' => 'CUSTOMERID',
                'Label' => array('NAME' => 'IA.CUSTOMER_NAME',
                    'CUSTOMERID' => 'IA.CUSTOMER_ID',
                    'None' => 'IA.NONE',
                    'CUSTTYPE' => 'IA.CUSTOMER_TYPE',
                    'PAYMENTPRIORITY' => 'IA.PAYMENT_PRIORITY',
                    'DUEDATE' => 'IA.DUE_DATE',
                    'GLPOSTDATE' => 'IA.GL_POSTING_DATE'),
                'DB' => array('NAME' => 'NAME',
                    'CUSTOMERID' => 'ENTITY',
                    'None' => 'ENTITY',
                    'CUSTTYPE' => 'CUSTTYPEKEY',
                    'PAYMENTPRIORITY' => 'PAYMENTPRIORITY',
                    'DUEDATE' => 'WHENDUE',
                    'GLPOSTDATE' => 'CREATED')
            ),

            'customergraph' => array(
                'FldIdName' => 'CUSTOMERID',
                'Label' => array('None' => 'IA.NONE'),
                'DB' => array('None' => 'ENTITY')
            ),
            'employee' => array(
                'FldIdName' => 'EMPLOYEEID',
                'Label' => array('NAME' => 'IA.EMPLOYEE_NAME',
                    'EMPLOYEEID' => 'IA.EMPLOYEE_ID',
                    'None' => 'IA.NONE',
                    'EMPTYPE' => 'IA.EMPLOYEE_TYPE',
                    'DUEDATE' => 'IA.DUE_DATE',
                    'GLPOSTDATE' => 'IA.GL_POSTING_DATE'),
                'DB' => array('NAME' => 'CONTACT.LASTNAME',
                    'EMPLOYEEID' => 'ENTITY',
                    'None' => 'ENTITY',
                    'EMPTYPE' => 'EMPTYPEKEY',
                    'GLPOSTDATE' => 'CREATED')
            )
        );

        GroupReporter::__construct($_params);

        // required
        $this->_report = 'aparage';

        // if there are record types that you don't want to include in aging, then put them here!!!
        $this->ignorerecordtypes = array('rd','pd');

        // if there are record types that need to have their signs flipped, then put them in here!!! 
        $this->flipsignrecordtypes = array( 'rp', 'rr', 'pp', 'pr', 'ep', 'er' );

        // if there are records that should NOT get a link in the html detail view, put them here!!!
        $this->nolinks = array(
            'rp' => 1,
            'pp' => 1,
            'ep' => 1,
        );
        $this->atlas = (IsMCMESubscribed() && !GetContextLocation());
    }

    /**
     * Override for reports that might use retainage, currently Customer and Vendor reports.
     * @return bool
     */
    public function getIsRetainageEnabled()
    {
        return false;
    }

    /**
     * @param string &$dataxml the returned xml
     *
     * @return bool
     */
    function CreateDataXML(&$dataxml)
    {
        // Convert rpt variable to old variable names
        $this->params['name'] = $this->params['REPORTNAME'];

        $this->params['period'] = $this->params['AGINGPERIODS'];
        $this->params['invdue'] = $this->params['BASEDON'];
        if(isset($this->params['OFFLINEREPORTS']) && isset($this->params['ASOFDATE']) && !isset($this->params['SELECTEDDATE'])){
            $this->params['agereportdate']= $this->params['ASOFDATE'];
        }else {
            $this->params['agereportdate'] = $this->params['SELECTEDDATE'];
        }
        $this->params['datemode'] = $this->params['REPORTASOF'];        

        return parent::CreateDataXML($dataxml);
    }

    /**
     * @return array
     */
    function DoMap()
    {
        if ($this->params['isgraph']=='1') {
            $this->map = $this->DoGraphMap();    
        } else {
            $this->map = $this->DoReportMap();
        }
        return $this->map;
    }

    /**
     * @return array
     */
    function DoReportMap()
    {
        $this->map["reportheader"][0]["graph"][0]["isgraph"][0]['cdata'] = '0';
        return $this->map;
    }

    /**
     * Handles map requirement for graph creation
     *
     * @return array
     */
    function DoGraphMap()
    {
        // all these globals need to get pulled out!!!!! (sometime...)
        /* @var array $gReport */
        global $gReport;
        global $kGraphTemplates;

        $graphtype = $this->params['graphtype'];
        $topstart = $this->params['topstart'];
        $topend = $this->params['topend'];
        $_period = $this->params['period'];

        $tempname = INTACCTtempnam(GRAPHDIR, 'graph');
        $unique = isl_substr($tempname, isl_strlen(GRAPHDIR));
        $fname = $tempname . ( function_exists("imagegif") ? '.gif' : '.png' );
        unlink($tempname); // This is no longer used, remove it!

        $prefs = array();
        GetCompanyPreferences($prefs);
        $symbol = isl_trim($prefs['CURRENCYSYMBOL']);
        if ($gReport['ROUNDING'] == 'M') {
            $yaxis = "($symbol" . 'M)';
        } else if ($gReport['ROUNDING'] == 'T') {
            $yaxis = "($symbol" . 'K)';
        } else  {
            $yaxis = "($symbol)";
        }
        
        if((isl_substr($gReport['RE_TYPE'], 6, 3))=='bar') {
                        $xaxis = "($symbol)";
            $yaxis = '';
        }

        $ageperiods = $_period;
        if (empty($ageperiods)) {
            $ageperiods = '-0,1-30,31-60,61-90,91-120,121-';
        }
        $agecols=explode(',', $ageperiods);
        $agecolscnt = count($agecols);
        switch ($graphtype){

        case 'totals':        
            // loop through the grand totals and put them into the tab
            $tab=array();
            $xLabels=array();
            for ($cl=0;$cl<$agecolscnt;$cl++) {
                $xLabels[$cl]=$agecols[$cl];
                $tab[0][$cl]=0;
                $tab[0][$cl]=$this->map['reportcontent'][0]['grandtotal'][0]['period'][$cl]['forgraph'];
            }
            $gLabels = array(I18N::getSingleToken('IA.TOTAL'));

            break;

        case 'vendorcustomer':
            // topstart and topend determine how many bars will appear on the chart
            // 				for ($i=0;$i<$this->params['accountcounter'];$i++){

            $offset = $topstart-1;
               $this->sortMap($this->map, 'AMOUNT_DESC');
                
                

            for ($i=0;$i<$topend;$i++){

                // give the vendors/customers their own bar/column
                $xLabels[$i-$offset] = $this->map['reportcontent'][0]["acctid"][$i]['name'];
                for ($cl=0;$cl<$agecolscnt;$cl++) {
                    $tab[$cl][$i-$offset]=0;
                    $tab[$cl][$i-$offset] = $this->map['reportcontent'][0]["acctid"][$i]['totalgraph'][0]['period'][$cl]['forgraph'];
                }
            }                        
            for ($cl=0;$cl<$agecolscnt;$cl++) {
                $gLabels[$cl]=$agecols[$cl];
            }
            break;
        }
        
        if (isset($this->params['ISDBGRAPH']) || $this->params['type'] == '_html') {
            /** @noinspection PhpUndefinedVariableInspection */
            $dbGraphData = array (
            're_type' => $this->params['re_type'],
            'xlabels' => $xLabels,
            'glabels' => $gLabels,
            'xaxis'   => $xaxis,
            'yaxis'      => $yaxis,
            'tab'      => $tab,
            'title'      => $this->params['title'],
            );
            $this->map = $dbGraphData;
            return $this->map;
        }

        // Use for backward compatibility
        $graphType = isset($kGraphTemplates[$this->params['re_type']]) ? $this->params['re_type'] : 'GRAPH.col.6';
        /** @noinspection PhpUndefinedVariableInspection */
        $fileStoreID = CreateGraphImage($kGraphTemplates[$graphType], $xLabels, $gLabels, $xaxis ?? '', $yaxis ?? '', $tab, $this->params['titlecomment'] ?? '', $fname) ?? '';

        $furl = GRAPHURL . isl_substr($fname, isl_strlen(GRAPHDIR));

        // remember fully qualified path for embeding into MIME email
        $this->graphfiles[] = $fname;
        $this->graphurls[] = $furl;

        $newunique = isl_substr($unique, 5);
        $fo_url = getResourceUrl("/acct/showthis.phtml?showthis=" . $newunique . "&mode=graph" . "&fsid=" . $fileStoreID . '&.sess=' . Session::getKey() . '&.op=' . MAX_DUMMY_ID);

        $this->map["reportheader"][0]["graph"][0]["isgraph"][0]['cdata'] = '1';
        $this->map["reportheader"][0]["graph"][0]["furl"][0]['cdata'] = $fo_url;
        $this->map["reportheader"][0]["graph"][0]["htmlfurl"][0]['cdata'] = $furl;

        return $this->map;    
    }

    /**
     * @return bool
     */
    function IsMultiRegionGraph()
    {
        // If the user chooses Show top we will have a multiserie graph 
        if($this->params['graphtype'] == 'vendorcustomer') { 
            return true;
        }
        return parent::IsMultiRegionGraph();
    }
    
    /**
     * @param array|bool $results
     * @param array      $acctorder
     * @param bool       $allowFutureAging
     * @param string     $basecurr
     * @param string     $currency
     * @param array|bool $accounts
     * @param array      $allAccountsTypeMap
     *
     * @return bool
     */
	function _DoQuery(&$results, &$acctorder, $allowFutureAging, $basecurr, $currency, &$accounts,&$allAccountsTypeMap)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;

        // call the private validate func
        if ( !$this->_validateParams() ) {
            return false;
        }

        $invoiceTypePostedStates = PRINVOICE_POSTED_STATES;
        $prRecordPostedNoVoidState = PRINVOICE_POSTED_NOTVOIDED_STATES;
        // For compatibilty, for Cash analysis report
        if(empty($this->params['GROUPBY']) || ($this->params['GROUPBY'] == 'false')) {
            $this->params['GROUPBY'] = 'None';
        }

        if (empty($this->params['LOCATION']) && IsMultiEntityCompany() && GetContextLocation() != ''){
            $contextLocationDetails = GetContextLocationDetails();
            $this->params['LOCATION'] = array($contextLocationDetails['LOCATION_NO'].'--'.$contextLocationDetails['NAME']);
        }
        
        // resolve location id
        if($this->params['LOCATION'] !='' && !is_array($this->params['LOCATION'])) {
            $this->params['LOCATION'] = array($this->params['LOCATION']);
        }
        if ($this->params['LOCATION'] != '' && count($this->params['LOCATION'])) {
            global $_userid;
            $locs = array();
            foreach ($this->params['LOCATION'] as $loc) {
                [$loc_no] = explode("--", $loc);
                GetRecordNo($_userid, 'location', $loc_no, $locrec);
                $locs[] = $locrec;
            }
            $this->params['loc'] = $locs;
            
        }        

        // resolve dept id
        if($this->params['DEPARTMENT'] !='' && !is_array($this->params['DEPARTMENT'])) {
            $this->params['DEPARTMENT'] = array($this->params['DEPARTMENT']);
        }
        $dept = $this->params['DEPARTMENT'];
        if($dept != '' && count($dept)) {
            foreach($dept as $deptparam) {
                [$deptid] = explode("--", $deptparam);
                global $_userid;
                GetRecordNo($_userid, 'department', $deptid, $deptrec);
                $deptrecs[] = $deptrec;
            }
            /** @noinspection PhpUndefinedVariableInspection */
            $this->params['dept'] = $deptrecs;
        }
        
        $this->params['filterTransactions'] = $this->params['FILTERTRANSACTIONS'];

        // pull params from param statement
        $_object = $this->params['object'];
        $_period = $this->params['period'];
        $_sumdet = $this->params['sumdet'];
        $_invdue = $this->params['invdue'];
        $_territory = $this->params['territory'];
        $_sortby = $this->params['sortby'];
        $showretainageonly = $this->getIsRetainageEnabled() && $this->params['showretainageonly'] == 'yes';

        $loc = $this->params['loc'];
        $dept = $this->params['dept'];
        $filterTransactions = $this->params['filterTransactions'];

        $agereportdate = $this->params['agereportdate'];

        $invno =  $this->params['INVOICENO'];    
        $documenttype = $this->params['DOCUMENTTYPE'];

        $isCCTXNExcluded = $this->params['EXCLUDE_CC_TXN'] == 'true';

        $cny = GetMyCompany();
        
        if (isl_strstr($_object, 'vendor')) {
            $this->_type = 'vendor';
        } else if (isl_strstr($_object, 'employee')) {
            $this->_type = 'employee';
        } else {
            $this->_type = 'customer';
        }
        $_type = $this->_type;
        $source = 'APARAgeReporter';
        XACT_BEGIN($source);

        $collectionInternalFilter = "";
        $showReversalCondition = '';
        if ($_type == 'vendor') {
            global $kAPid;
            $showReversals = GetPreferenceForProperty($kAPid, 'SHOW_REVERSALS');
            $showReversalCondition = (!$showReversals);
            $trtypes = [ 'pi' ];

            if ( ! $isCCTXNExcluded ) {
                $trtypes = INTACCTarray_merge($trtypes, [ 'ci', 'cc' ]);
            }

            $paymentTypes = array('pr');
            $paymentInternalTypes = array('pr');
            $ajtype = 'pa';
            $collectionFilter = " and pr.recordtype IN ('" . join("', '", $paymentTypes) .  "')";
            
            $dueDate = " 
				case 
				when (pr.recordtype in ('" . join("', '", $trtypes) . "') and pr.recordtype != 'ci')
					then to_char(to_date(:2,'mm/dd/yyyy') - pr.whendue,'9999999') 
				when pr.recordtype in ('$ajtype', 'ci')
					then to_char(to_date(:2,'mm/dd/yyyy') - pr.whencreated,'9999999') 
				else null 
				end";

            $dueDateAdv = " to_char(to_date(:2,'mm/dd/yyyy') - pr.whencreated,'9999999') ";
            $date = " 
				case 
				when (pr.recordtype in ('" . join("', '", $trtypes) . "') and pr.recordtype != 'ci')
					then to_char(to_date(:2,'mm/dd/yyyy') - pr.whendue,'9999999') 
				when pr.recordtype = '$ajtype' or pr.recordtype in ('ci','" . join("', '", $paymentTypes) . "')
					then to_char(to_date(:2,'mm/dd/yyyy') - pr.whencreated,'9999999') 
				else null 
				end";
            $stateFilter = " and NVL(pr.state, 'A')  in ($invoiceTypePostedStates) ";
        } else if ($_type == 'employee') {
            $trtypes = array('ei');
            $paymentTypes = array('er');
            $paymentInternalTypes = array('er','eo');
            $ajtype = 'ea';
            // only approved expenses should come in the employee aging report.
            $stateFilter = " and pr.state in ('A') ";
            $collectionFilter = " and pr.recordtype IN ('" . join("', '", $paymentTypes) .  "')";
            
            $dueDate = " to_char(to_date(:2,'mm/dd/yyyy') - pr.whendue,'9999999') ";
            $dueDateAdv = " to_char(to_date(:2,'mm/dd/yyyy') - pr.whencreated,'9999999') ";
          
        } else {
            global $kARid;
            $showReversals = GetPreferenceForProperty($kARid, 'SHOW_REVERSALS');
            $showReversalCondition = ($showReversals != 'true');
            $trtypes = array('ri');
            $paymentTypes = array('rp', 'rr');
            $paymentInternalTypes = array('rp', 'rr');
            $ajtype = 'ra';
            $collectionInternalFilter =     " AND (greatest(nvl(pr.whenpaid, to_date(:2,'mm/dd/yyyy')+1), nvl(pr.maxpayactdate, '01/01/1970')) > to_date(:2,'mm/dd/yyyy')
												or pr.totaldue!=0";
            if ($showretainageonly) {
                // also include it if retainage is non-zero
                $collectionInternalFilter .= " or nvl(pr.trx_totalretained, 0) != 0";
            }
            $collectionInternalFilter .= ")";
            $collectionFilter =     "AND (
								 CASE
									WHEN pr.recordtype  IN ('rp','rr') and ". ARPymtUtils::getPaymentStateFilter('pr') ." and
											(greatest(nvl(pr.whenpaid, to_date(:2,'mm/dd/yyyy')+1), nvl(pr.maxpayactdate, '01/01/1970')) > to_date(:2,'mm/dd/yyyy')
											    or pr.totaldue!=0";
            if ($showretainageonly) {
                // also include it if retainage is non-zero
                $collectionFilter .= " or nvl(pr.trx_totalretained, 0) != 0";
            }
            $collectionFilter .= ")				

									THEN 'T'
                                   -- WHEN pr.recordtype IN ('rr')
                                   -- THEN 'T'
									ELSE 'F'
								  END ) = 'T'";
             
            $dueDate = " to_char(to_date(:2,'mm/dd/yyyy')-nvl(pr.whendue, pr.whencreated),'9999999') ";
            $dueDateAdv = " to_char(to_date(:2,'mm/dd/yyyy') - pr.whencreated,'9999999') ";
            $stateFilter = " and NVL(pr.state, 'A')  in ($invoiceTypePostedStates) ";
        }
        /** @noinspection PhpUndefinedVariableInspection */
        if( $showReversalCondition && $_type != 'employee') {
            $stateFilter = " AND NVL(pr.state, 'A') in (". $prRecordPostedNoVoidState .") ";
        }


        // sortby is empty when this report is viewed from SFDC
        if(!isset($_sortby) || $_sortby == '') {
            if (isl_strstr($_type, 'vendor')) {
                $_sortby = 'VENDORID';
            } else if (isl_strstr($_type, 'employee')) {
                $_sortby = 'EMPLOYEEID';
            } else {
                $_sortby = 'CUSTOMERID';
            }
        }

        // we need the min and max to help with the query do determine which accts to pull			
        $ageperiods = $_period;
        if (empty($ageperiods)) {
            $ageperiods = '0-30,31-60,61-90,91-120';
        }
        $agecols=explode(',', $ageperiods);

        // we need the min and max to help with the query do determine which accts to pull			
        $agcmin=0;
        $agcmax=0;
        foreach ($agecols as $ag) {
            $ag = isl_trim($ag);
            if($ag != '') {
                [$agcstart,$agcend]=explode('-', $ag);

                if (isNullOrBlank($agcstart)) {
                    $agcstart='999999';
                }
                if (isNullOrBlank($agcend)) {
                    $agcend='999999';
                }

                $agcstart    = (int) isl_trim($agcstart);
                $agcend    = (int) isl_trim($agcend);

                // PHP8_NUMERIC_STRING_COMPARE; Priority: low; Behavior: same, Risk: low, Solution: int cast
                if ( $agcstart > $agcend ) {
                    $agcstart= -$agcstart;
                    $agcend= -$agcend;
                }

                $ageper[$ag]['start']=$agcstart;
                $ageper[$ag]['end']=$agcend;

                // remember the min and max values for later
                if ( $agcstart < $agcmin ) {
                    $agcmin=$agcstart;
                }
                if ( $agcend > $agcmax ) {
                    $agcmax=$agcend;
                }
            }
        }

    
        // Begin processing filters.
        $territoryStr = '';
        $entityFilters = [];
        $typeFilter = '';
        $typeTable = '';
        $typeCondition = '';
        if (isset($this->params['CUSTOMERTYPE']) && $this->params['CUSTOMERTYPE'] != '') {
            $custtypeMgr = $gManagerFactory->getManager('custtype');
            $custtypeobj = $custtypeMgr->get($this->params['CUSTOMERTYPE']);
            if (!empty($custtypeobj['RECORDNO'] )) {
                $entityFilters[] = ['CUSTTYPEKEY', '=', $custtypeobj['RECORDNO']];
                $typeTable = ', custtype b';
                $typeCondition .= " and b.cny# = a.cny# and b.record# = ".$custtypeobj['RECORDNO']." and a.custtypekey = b.record#";
            } else {
                return true;
                // return from this method and stops from firing further query since the customer type is invalid.
            }
        }
        if (isset($this->params['VENDORTYPE']) && $this->params['VENDORTYPE'] != '') {
            $vendtypeMgr = $gManagerFactory->getManager('vendtype');
            $vendtypeobj = $vendtypeMgr->get($this->params['VENDORTYPE']);
            if (!empty($vendtypeobj['RECORDNO'])) {
                $entityFilters[] = ['VENDTYPEKEY', '=', $vendtypeobj['RECORDNO']];
                $typeTable = ', vendtype b';
                $typeCondition .= " and b.cny# = a.cny# and b.record# = ".$vendtypeobj['RECORDNO']." and a.vendtypekey = b.record#";
            } else {
                return true;
                // return from this method and stops from firing further query since the vendor type is invalid.
            }
        }
        // check if territory is set
        if (isNonEmptyArray($_territory)) {
            $entityFilters[] = ['TERRITORYKEY', 'IN', array_map(fn($value) => trim($value, "'"), $_territory)];
            $territoryStr = PrepINClauseStmt('', $_territory, ' ', false);
            $typeCondition .= " and a.territorykey $territoryStr";
        }

        if (!empty($typeCondition)) {
            $typeFilter = " AND EXISTS ("
                . "SELECT 1 FROM $_type a $typeTable WHERE a.cny# = :1  
                     and pr.entity = a.entity $typeCondition)";
        }

        $accounts = [];
        $ok = $this->getEntityInformation($_sortby, $territoryStr, $entityFilters, $accounts);

        if (!$ok) {
            return false;
        }
        // if no entity details found, return true from here only, no need to continue for below query
        if (empty($accounts)) {
            return true;
        }

        // Get the column name to be used to fetch the data for group filed
        $grpFldId = $this->_groupmap[$this->params['object']]['DB'][$this->params['GROUPBY']];
        $fldid = $this->_groupmap[$this->params['object']]['FldIdName'];
        $objType = $this->_groupmap[$_object]['DB'][$this->params['GROUPBY']];
        $typeforid = $typeforkey = '';
        $objectTypes = $this->getObjectTypeDetails($typeforid, $typeforkey);

        foreach ($accounts as $acc) {

            $entid = $acc[$grpFldId];
            $this->_vendcustNameMap[$acc['ENTITY']] = $acc;
            if(!isset($acctorder[$entid])){
                $acctorder[$entid] = $acc;
            }
            $acctorder[$entid][$fldid] = $entid;
            $acctorder[$entid]['TOTALDUE'] = $acc['TOTALDUE'];
            if (!isset($acctorder[$entid]['CREATED'])) {
                $acctorder[$entid]['CREATED'] = null;
            }

            if ( !isset($acctorder[$entid]['CREATED_SORT']) ) {
                $acctorder[$entid]['CREATED_SORT'] = null;
            }

            if ( !isset($acctorder[$entid]['TOTALDUE']) ) {
                $acctorder[$entid]['TOTALDUE'] = null;
            }

            if(!isset($entid)){
                $allAccountsTypeMap[$acc['ENTITY']]['TYPE']= 'OTHER';
                $allAccountsTypeMap[$acc['ENTITY']]['NAME']= $acc['NAME'];
            }else if(!empty($objectTypes[$entid])){
                $allAccountsTypeMap[$acc['ENTITY']]['TYPE'] = $objectTypes[$entid];
                $allAccountsTypeMap[$acc['ENTITY']]['NAME']= $acc['NAME'];
            }
        }

        $entFilter = '';
        if ($this->_useEntityFilter) {
            $entFilter = PrepINClauseStmt(
                '', array_keys($this->_vendcustNameMap),
                ' and entity ', false,
                'dummyentity', true
            );
        }

        //based on invoice date or dueate or glpostingdate?
        if ($_invdue == 'Duedate') {
            $date = $dueDate;
        } else if ($_invdue == 'GLPostdate') {
            $date = " 
				case 
				when (prb.created is null or (prb.recordtype='ch' and pr.recordtype='ci'))
					    then to_char(to_date(:2,'mm/dd/yyyy') - pr.whencreated,'9999999') 
				else 
					    to_char(to_date(:2,'mm/dd/yyyy')-prb.created,'9999999') 
				end";

        } else if ($_invdue == 'Invoicedate') {
            $date = "to_char(to_date(:2) - pr.whencreated,'9999999')";
        }

        // an invoice can pay and get paid. So, totaldue = totalentered - how much got paid + how much it paid.
        $totaldue    = " round(pr.totalentered - nvl(pr.nr_totalentered,0) - nvl(pr.totalretained, 0) 
						- (select nvl(sum(prp.amount),0) 
							from prentrypymtrecs prp, prentry pre 
							where prp.cny#= :1  and prp.cny# = pre.cny#
								and prp.recordkey = pr.record# and  prp.paiditemkey = pre.record#
								and trunc(prp.paymentdate) <= to_date(:2,'mm/dd/yyyy') and prp.state='C') 
						+ (select nvl(sum(prp.amount),0) 
							from prentrypymtrecs prp, prentry pre 
							where prp.cny#= :1 and prp.cny# = pre.cny#
								and prp.paymentkey = pr.record# and prp.payitemkey = pre.record#
								and trunc(prp.paymentdate) <= to_date(:2,'mm/dd/yyyy') and prp.state='C'),2) " ;

        if($this->mcpEnabled && $this->params['report'] != 'apagegraph' && $this->params['report'] != 'aragegraph') {
            $trx_totaldue    = ", round(pr.trx_totalentered - nvl(pr.nr_trx_totalentered,0) - nvl(pr.trx_totalretained, 0) 
							- (select nvl(sum(prp.invtrxamt),0)
								from prentrypymtrecs prp, prentry pre 
								where prp.cny#= :1  and prp.cny# = pre.cny#
									and prp.recordkey = pr.record# and  prp.paiditemkey = pre.record#
									and trunc(prp.paymentdate) <= to_date(:2,'mm/dd/yyyy') and prp.state='C') 
							+ (select nvl(sum(prp.invtrxamt),0)
								from prentrypymtrecs prp, prentry pre 
								where prp.cny#= :1 and prp.cny# = pre.cny#
									and prp.paymentkey = pr.record# and prp.payitemkey = pre.record#
									and trunc(prp.paymentdate) <= to_date(:2,'mm/dd/yyyy') and prp.state='C'),2) trx_totaldue" ;
        }

        if (!$allowFutureAging) {
            if($_invdue == 'GLPostdate') {
                $unpaidstr = " and CASE WHEN (prb.created IS NULL or (prb.recordtype='ch' and pr.recordtype='ci')) THEN
								 pr.whencreated 
								ELSE 
								 created 
								END <= to_date(:2,'mm/dd/yyyy')";
            }else{
                $unpaidstr   = " and pr.whencreated <= to_date(:2,'mm/dd/yyyy') ";
            }
        }else{
            $totaldue = ' round(totaldue,2) ';
            if( $this->mcpEnabled && $this->params['report'] != 'apagegraph' && $this->params['report'] != 'aragegraph') {
                $trx_totaldue = ', round(trx_totaldue,2) trx_totaldue ';
            }
            $unpaidstr = ' and totaldue !=0 ';
        }
        
        // set up the department filter in case we need it.					
        if ($dept != '') {
            $filterdepartment = " AND EXISTS ( ".
            "SELECT 1 FROM PRENTRY ".
            "WHERE cny# = :1 and pr.record# = recordkey and dept# in (" . 
            implode(',', $dept) . ") ) ";
        }

        // set up the location filter in case we need it.					
        $filterlocation = BuildLocationFilter($loc, $filterTransactions);            
        if ($filterlocation != '') {
            $filterlocation = ' AND ' . $filterlocation;
        }            

        // set up the currency filter.
        if($currency !='') {
            if ($this->atlas) {
                $filtercurrency = "AND pr.currency = :3";
            } else {
                $filtercurrency = "AND nvl(pr.currency, '$basecurr') = :3";
            }
        }

        if ($this->atlas && $this->params['REPORTINGCURRENCY'] != '' && $this->params['EXCHRATETYPE'] == '') {
            /** @noinspection PhpUndefinedVariableInspection */
            $filtercurrency .= "AND (pr.impliedlocation in (select location# from v_locationent where cny# = :1 and currency = '$basecurr') or (pr.impliedlocation is null and pr.prbatchkey in (select record# from prbatch where cny# = :1 and title like '%($basecurr)%'))) ";
        }

        // set up the invoiceno filter in case we need it.					
        if ($invno != '') {
            $filterinvno = " AND pr.recordid like '$invno%' ";
        }

        if ($documenttype != '') {
            if ($documenttype == "invoice") { 
                $recordtype = "ri"; 
            }
            else if ($documenttype == "adjustment") { 
                $recordtype =  "ra"; // use invoices or adjustments. Do not worry about other ar
            } else {
                global $gErr;
                    $gErr->addError("SL-1181", __FILE__ . __LINE__, "documenttype can be either 'invoice' or 'adjustment'");
                return false;
            }
            $filterrecordtype = " and pr.recordtype = '$recordtype'";
        }

        // Prepare to include qualifying AP,EE advances
        $apconfirmedState = PRRECORD_STATE_PCONFIRMED;
        $stateChk = ' and '. ARPymtUtils::getPaymentStateFilter('pr');
        if($_type == 'vendor') {
            $stateChk = " and pr.state = '$apconfirmedState' ";
            if($showReversals) {
                $stateChk = " AND pr.state IN ('C', 'V') ";
            }
        } else if ($_type == 'employee') {
            $stateChk = " and NVL(pr.state, 'C') = '$apconfirmedState' ";
        }

        // totaldue on the prrecord can not be used. We should always compute totaldue by subtractcting amounts obtained
        // from prentrypymtrecs. (unless it is run based on currrent date). 
        // There are two parts in this query - first part gets all the payments made to or received the invoices/bills 
        // (both positive and negative). 
        // Second part if for payment types. It not only has to get the payments made by or made to the payment records, it also has
        // to get the payments made by or to the child records ('po' and 'ro' )
        //NOTE: We could have made the second query similar to the first one. But this one performs better.

        if( $this->params['sumdet'] == 'Detail' 
            || $_invdue == 'GLPostdate' 
            || ($_sortby == 'GLPOSTDATE' || $_sortby == 'GLPOSTDATE_DESC') 
            || $this->params['GROUPBY'] == 'GLPOSTDATE'
        ) {
            $prbjoin = " and pr.cny# = prb.cny# and pr.prbatchkey = prb.record# ";
            $selectfield = " created, TO_CHAR(created, 'YYYY-MM-DD') created_sort, ";
            //we are showing the transaction date of the charge card transaction instead of the GL posting date if the transaction is in hidden batch.Bug:36430.
            $selectbatchtype = " case when (prb.recordtype = 'ch' and pr.recordtype ='ci') then pr.whencreated else prb.created end as created, ";
            $fromTable = " prbatch prb, ";
        } else {
            $prbjoin ="";
            $selectfield = "";
            $selectbatchtype = "";
            $fromTable = "";
        }

        if ( $_sumdet != 'A' ) {
            $selects = " pr.record#,pr.entity,pr.recordid,pr.prbatchkey, pr.paymentpriority,pr.recordtype,".$selectbatchtype." pr.docnumber, pr.whendue whendue,  ".
              " pr.whencreated whencreated,t.paymentkey transferkey, pr.locationkey, pr.state, ";
        } else {
            $selects = " pr.recordtype, pr.paymentpriority, ";
        }

        if( $this->mcpEnabled && $this->params['report'] != 'apagegraph' && $this->params['report'] != 'aragegraph') {
            $currselect1 = ", pr.currency currency";
            $currselect2 = ", currency";
            $trxtotaldue1 = ", prent.trx_amount + nvl(trx_pos, 0) - nvl(trx_neg, 0)  trx_totaldue";
            $trx_pos_neg = ", sum(decode(itemkeytype, 'PAIDITEM', trx_amt, 0)) as trx_pos, sum(decode(itemkeytype, 'PAYITEM', trx_amt, 0)) as trx_neg";
            $trx_amt = ", nvl(sum(pmt.trx_amount), 0) as trx_amt";
            $trxsubsel = ", trx_totaldue";
            $trxsubsel1 = ", sum(trx_totaldue) trx_totaldue";
            $trxsubgrpby = ", trx_totaldue, currency";
            if($this->params['REVALUATIONTYPE'] == 'base') {
                //Report need to show in Base Currency. ie, If Atlas root level filter 'Convert Currency from == Base Currency is checked'
                $currselect1 .= ", pr.basecurr billbasecurr";
                $currselect2 .= ", billbasecurr";
                $trxsubgrpby .= ", billbasecurr";
            }
        }

        $meglVisibilityCond = ""; // FOR THE $duerecordsqry QUERY BELOW
        if ( IsMultiEntityCompany() ) {
            $meglVisibilityCond = " and exists ( select 1 from prentrymst where cny# = :1 and record# = pmttotals.itemkey)";
        }

        /* The following query is modified ( 07/12/2006 ) in order to enforce MEGL visibility restrictions
        at the prentrypytmrec amount summary level. Previously, it was possible to include payment amounts
        from this table which did not belong to the entity whose context the report was being run for.

        Instead of joining on the prrecordmst table during the process of retrieving the records for 
        summarization, the join is done while summarizing and also links up the prentry table. This additional
        "exists" clause costs us something in performance, but is necessary to weed out amounts not belonging to the 
        context entity.
        */

        $totaldue2Var = " (prent.amount + nvl(pos, 0) - nvl(neg, 0)) ";

        $totalFilter = "and (greatest(nvl(pr.whenpaid, to_date(:2,'mm/dd/yyyy')+1), nvl(pr.maxpayactdate, '01/01/1970')) > to_date(:2,'mm/dd/yyyy')";
        if ($showretainageonly) {
            // also include it if retainage is non-zero
            $totalFilter .= " or nvl(pr.trx_totalretained, 0) != 0";
        }
        $totalFilter .= ")";

        /** @noinspection PhpUndefinedVariableInspection */
        $duerecordsqry =
        "select	$selects $date as nodays, null as voidpaymentkey, 
                    pr.totaldue tdue, pr.totalretained, pr.trx_totalretained, pr.trx_totalreleased, $totaldue totaldue $trx_totaldue $currselect1
			from 	prrecord pr, $fromTable transferlink t  
			where 	pr.cny# = :1
				$prbjoin
				and pr.cny# = t.cny# (+)
				$unpaidstr 
				and pr.recordtype in ('" . join("', '", $trtypes) .  "'," . "'$ajtype') 
				$stateFilter
				and pr.record# = t.paymentkey (+) 
				$totalFilter
				and $date between $agcmin and $agcmax "
        .$typeFilter .$entFilter . $filterdepartment. $filterlocation . $filterinvno . $filterrecordtype . $filtercurrency;
        
                
        // Aging Query for Advance is too expensive. 
        // So lets run a simple query to check if advance exists in the company and if exists then run the costly query
        $chkAdvQry = "SELECT count(1) as advcount FROM prrecordmst pr 
						where pr.cny# = :1
								and pr.recordtype IN ('" . join("', '", $paymentTypes) . "') 
								and rownum<=1
								$stateChk " . $entFilter;
        
        $args = array($chkAdvQry, $cny);
        $advRec = QueryResult($args);

        if($advRec[0]['ADVCOUNT']>0) {
            $locFilter = "";
            if(!empty($locrec)){
                $locFilter = $this->BuildLocationInFilter($loc, $filterTransactions,'pre.');
                $locFilter = " and ". $locFilter;
            }
            //based on invoice date or dueate or glpostingdate?
            $dateFilter = "pr.whencreated";
            $prbjoinEntityCache = "";
            $fromTableEntityCache = "";
            if ($_invdue == 'GLPostdate') {
                $fromTableEntityCache = ", prbatchmst prb ";
                $prbjoinEntityCache = " and pr.cny# = prb.cny# and pr.prbatchkey = prb.record# ";
                $dateFilter = "prb.created";
            }
            // to collect only required line items from prentrypymtrecs
            $collectionsPREnty = "insert into entitycache (entityno, entitytype)(						
							SELECT DISTINCT pre.record#, 'ag_prentry_adv'
							FROM prentrymst pre
							WHERE pre.cny#  =:1
							AND pre.lineitem='T'
							$locFilter
							AND EXISTS
							  (SELECT 1
							  FROM prrecordmst pr
							  $fromTableEntityCache
							  WHERE pr.cny#      =:1
							  AND pr.record#     =pre.recordkey
							  $prbjoinEntityCache
							  AND pr.recordtype IN ('" . join("', '", $paymentInternalTypes) .  "')
							  AND $dateFilter  <= to_date(:2,'mm/dd/yyyy')
							  ". $filterdepartment. $collectionInternalFilter. $entFilter .$typeFilter . $filtercurrency."
							  )
							)";
            // get collections
            $args = array($collectionsPREnty, $cny, $agereportdate);
            if($currency != '') {
                $args[] = $currency;
            }
            if (!ExecStmtEx($args, $rowcount, true)) {
                global $gErr;
                $gErr->addError("SL-1182", __FILE__ . __LINE__, "Failed to get Collections");
                return false;    
            }
            // to collect only required line items from prentrypymtrecs
            $collectionsPREntyAppliedAdvOver = "insert into entitycache (entityno, entitytype)(						
							SELECT DISTINCT pre.record#, 'ag_prentry_op'
							FROM prentrymst pre
							WHERE pre.cny#  =:1
							AND pre.lineitem='T'							
							AND EXISTS (select 1 from entitycache where entityno = pre.parententry and entitytype = 'ag_prentry_adv')							
							)";
            // get collections
            $argsAppliedAdvOver = array($collectionsPREntyAppliedAdvOver, $cny);
            if (!ExecStmtEx($argsAppliedAdvOver, $rowcountAdvOver, true)) {
                global $gErr;
                $gErr->addError("SL-1183", __FILE__ . __LINE__, "Failed to get Collections");
                return false;    
            }

            if ($_invdue=='Duedate') {
                $date = $dueDateAdv;
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $duerecordsqry .=
            "\n UNION ALL 	\n" . 
            "select	$selects $date as nodays, vl.voidpaymentkey, 
                    (prent.amount - prent.totalpaid) tdue, null totalretained, null trx_totalretained, null trx_totalreleased, $totaldue2Var totaldue $trxtotaldue1 $currselect1
			from	prrecordmst pr, $fromTable transferlink t, voidlink vl, (
				SELECT 
					( case 
						when 	prm.recordtype IN ('" . join("', '", $paymentTypes) .  "') 
							  OR 
							prm.parentpayment is NULL 
						then prm.record# 
						else prm.parentpayment 
						end
					) as record, 
					sum(decode(itemkeytype, 'PAIDITEM', amt, 0)) as pos, 
					sum(decode(itemkeytype, 'PAYITEM', amt, 0)) as neg $trx_pos_neg
				FROM 	prrecordmst prm, (
					select 
						pmt.cny#, 
						pmt.recordkey as record, 
						pmt.paiditemkey itemkey, 
						'PAIDITEM' as itemkeytype, 
						nvl(sum(pmt.amount), 0) as amt $trx_amt 
					from prentrypymtrecs pmt 
					where 
						pmt.cny# = :1 
						and trunc(pmt.paymentdate) <= to_date(:2,'mm/dd/yyyy') 
						and pmt.state='C' 
						and exists (select 1 from entitycache where entitytype in ('ag_prentry_op','ag_prentry_adv') and entityno=pmt.paiditemkey)
					group by 
						pmt.cny#, 
						pmt.recordkey, 
						pmt.paiditemkey
			  		  UNION ALL
			  		select 
						pmt.cny#, 
						pmt.paymentkey as record, 
						pmt.payitemkey itemkey, 
						'PAYITEM' as itemkeytype, 
						nvl(sum(pmt.amount), 0) as amt $trx_amt
					from prentrypymtrecs pmt 
					where 
						pmt.cny# = :1 
						and trunc(pmt.paymentdate) <= to_date(:2,'mm/dd/yyyy') 
						and pmt.state='C' 
						and exists (select 1 from entitycache where entitytype in ('ag_prentry_op','ag_prentry_adv') and entityno=pmt.payitemkey)
					group by 
						pmt.cny#, 
						pmt.paymentkey, 
						pmt.payitemkey
					) pmttotals
				WHERE
					prm.cny# = :1 
					and prm.cny# = pmttotals.cny#
					and prm.record# = pmttotals.record 
                    $entFilter $meglVisibilityCond
				GROUP BY
					( case 
						when 	prm.recordtype IN ('" . join("', '", $paymentTypes) .  "') 
							  OR 
							prm.parentpayment is NULL 
						then prm.record# 
						else prm.parentpayment 
						end
					)
				) s,
				(SELECT
				     pre.cny#,
                     pre.recordkey,
                     sum(pre.amount) amount,
                     sum(pre.totalpaid) totalpaid,
                     sum(pre.trx_amount) trx_amount,
                     sum(pre.trx_totalpaid) trx_totalpaid
                 FROM
                     prentrymst pre
                 WHERE
                     pre.cny# = :1
                     AND pre.lineitem = 'T'                     
                     AND EXISTS (select 1 from entitycache where entitytype='ag_prentry_adv' and entityno=pre.record#)                     
                                                               
                group by pre.cny#, pre.recordkey
				) prent
			where 	pr.cny# = :1 
			$prbjoin
			and  pr.cny# = t.cny# (+)
			and prent.cny# = pr.cny# 
			and pr.record# = s.record(+)
			and  pr.cny# = vl.cny# (+) 
			and  pr.record# = vl.paymentkey (+)
			and prent.recordkey = pr.record#              
            $unpaidstr
            $collectionFilter			                           
			$stateChk 
			--AND ( NVL(pr.state, 'C') != 'V' or $totaldue2Var != 0 )
			and $date between $agcmin and $agcmax
			and pr.record# = t.paymentkey (+) " .
            $entFilter. $filterinvno. $filterrecordtype. $filtercurrency;
        }

        // Within the customerid/vendorid/name, the items should be sorted by duedate/glpostingdate,record#
        if ( $_sumdet != 'A' ) {
            $sortCol = 'pr.whendue ';
            if($_invdue == 'GLPostdate') {
                $sortCol = 'created';
                //$sortCol = ($_sortby == 'GLPOSTDATE_DESC') ? 'created desc' : 'created ';
            }elseif($_invdue == 'Duedate') {
                $sortCol = 'pr.whendue';
            }elseif($_invdue == 'Invoicedate') {
                $sortCol = 'pr.whencreated';
            }
            if($_sortby == 'GLPOSTDATE_DESC') {
                $sortCol = 'created desc';
            }elseif($_sortby == 'GLPOSTDATE') {
                $sortCol = 'created';
            }elseif($_sortby == 'AMOUNT') {
                $sortCol = 'totaldue';
            }elseif($_sortby == 'AMOUNT_DESC') {
                $sortCol = 'totaldue desc';
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $startsubsel = "select pr.record#, pr.entity,pr.recordid,pr.prbatchkey, pr.paymentpriority, " . $selectfield .
                           " pr.recordtype,pr.docnumber,pr.whendue, pr.whencreated, transferkey, pr.locationkey, " .
                           " nodays, voidpaymentkey, totaldue, tdue, pr.totalretained, pr.trx_totalretained, pr.trx_totalreleased, pr.state " . $trxsubsel . $currselect2 . " from (";
            $endsubsel = ")pr order by $sortCol , pr.record#";
        } else {
            /** @noinspection PhpUndefinedVariableInspection */
            $startsubsel = "select pr.recordtype, pr.paymentpriority, nodays, sum(totaldue) totaldue" .
                           $trxsubsel1 . $currselect2 . " from (";
            /** @noinspection PhpUndefinedVariableInspection */
            $endsubsel = ")pr group by pr.recordtype, pr.paymentpriority, nodays" . $trxsubgrpby;
        }
        
        // get all the relevant records
        $query =     $startsubsel. $duerecordsqry. $endsubsel;
        $args = array($query, $cny, $agereportdate);
        if($currency != '') {
            $args[] = $currency;
        }
        $results = QueryResult($args);

        if ($this->getIsRetainageEnabled()) {
            // there may have been several retainage releases, consider only those that happened before as of date.
            $this->determineRetainageRelease($results, $agereportdate);
        }

        $tmpGrpBy = array();

        if( $this->params['GROUPBY'] != 'None') {

            foreach($results as $k => &$v) {
                // Append vendor/cust name to the result array  used for display purpose
                $results[$k]['NAME'] = $this->_vendcustNameMap[$v['ENTITY']]['NAME'];
                $results[$k]['LASTNAME'] = $this->_vendcustNameMap[$v['ENTITY']]['LASTNAME'];
                $results[$k]['FIRSTNAME'] = $this->_vendcustNameMap[$v['ENTITY']]['FIRSTNAME'];
                $results[$k]['PAYMENTPRIORITY'] = $this->_payprioritymap[$results[$k]['PAYMENTPRIORITY']] ?? 'OTHER';
                if(in_array($objType, $this->_pushTypeKeys)) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $results[$k][$objType] = (!isset($this->_vendcustNameMap[$results[$k]['ENTITY']][$objType])) ? 'OTHER' : $objectTypes[$this->_vendcustNameMap[$results[$k]['ENTITY']][$objType]];
                }

                // when we are filtering for specific types,we should not add OTHER record 
                if ( $results[$k][$objType] === 'OTHER' && ((isset($this->params['VENDORTYPE']) && $this->params['VENDORTYPE'] != '')
                    || (isset($this->params['CUSTOMERTYPE']) && $this->params['CUSTOMERTYPE'] != ''))
                ) {
                    continue;
                }
                
                if(!in_array($v[$grpFldId], $tmpGrpBy)) {
                    $entid = $v[$grpFldId];
					$tmpGrpBy[] = $entid;
                    $acctorder[$entid][$fldid] = $entid;
                    $acctorder[$entid]['ENTITY'] = $entid;
                    $acctorder[$entid][$grpFldId] = $entid;

                    if (!isset($acctorder[$entid]['CREATED'])) {
                        $acctorder[$entid]['CREATED'] = null;
                    }

                    if ( !isset($acctorder[$entid]['CREATED_SORT']) ) {
                        $acctorder[$entid]['CREATED_SORT'] = null;
                    }

                    if ( !isset($acctorder[$entid]['TOTALDUE']) ) {
                        $acctorder[$entid]['TOTALDUE'] = null;
                    }
                }
            }
            unset($tmpGrpBy);



            if(in_array($objType, $this->_pushTypeKeys)) {
                foreach( $acctorder as $acckey => $accval){
                    if($acckey == 'OTHER') {
                        $objectTypes[$acckey] = 'OTHER';
                    }

                    /** @noinspection PhpUndefinedVariableInspection */
                    $nacctorder[$objectTypes[$acckey]] = $acctorder [$acckey];
                    /** @noinspection PhpUndefinedVariableInspection */
                    $nacctorder[$objectTypes[$acckey]][$typeforid] = $objectTypes[$acckey] ;
                    /** @noinspection PhpUndefinedVariableInspection */
                    $nacctorder[$objectTypes[$acckey]][$typeforkey] = $objectTypes[$acckey] ;
                    $nacctorder[$objectTypes[$acckey]]['ENTITY'] = $objectTypes[$acckey] ;

                    unset($acctorder[$acckey]);
                }

                /** @noinspection PhpUndefinedVariableInspection */
                $acctorder = $nacctorder;
            }

            if(isl_strstr($this->params['GROUPBY'], "DATE")) {
                $acctorder['OTHER']['ENTITY'] = '';
                $acctorder['OTHER']['CREATED'] = 'OTHER';
                $acctorder['OTHER']['CREATED_SORT'] = 'OTHER';
                $acctorder['OTHER']['WHENDUE'] = 'OTHER';
                $acctorder['OTHER']['TOTALDUE'] = null;
            }
        }

        unset($acctorder['']);

        XACT_ABORT($source);
        
        return true;

    }

    /**
     * Determine how much retainage has been released, in order to calculate the retainage balance correctly
     *
     * @param array    &$results
     * @param DateTime  $ageReportDate
     *
     */
    private function determineRetainageRelease(&$results, $ageReportDate)
    {
        if (!is_array($results) || isEmptyArray($results)) {
            return;
        }

        $cny = GetMyCompany();

        $prRecordKeyList = array();

        foreach ($results as $result) {

            if ($result['TRX_TOTALRETAINED'] != 0) {
                $prRecordKeyList[] = $result['RECORD#'];
            }
        }

        if (isEmptyArray($prRecordKeyList)) {
            return;
        }

        $stmt =  "select AMOUNTRELEASED, TRX_AMOUNTRELEASED, RETAINAGE_PRRECORDKEY
                     from retainagerelease rr
                     join retainagereleaseentry rre on rr.record# = rre.rrkey
                     join prrecordmst prr on rre.released_prrecordkey = prr.record#
                     join prbatchmst prb on prr.prbatchkey = prb.record#
                     where rr.cny# = :1 and rr.cny# = rre.cny# and rr.cny# = prr.cny# and rr.cny# = prb.cny# " .
                     PrepINClauseStmt('', $prRecordKeyList, ' and rre.retainage_prrecordkey ');
        $dbDateColumnName = '';
        switch($this->params['invdue']) {
            case 'Invoicedate':
                $dbDateColumnName = 'prr.whencreated';
                break;

            case 'Duedate':
                $dbDateColumnName = 'prr.whendue';
                break;

            case 'GLPostdate':
                $dbDateColumnName = 'prb.created';
                break;
        }

        $dateClause = sprintf("and %s <= :2", $dbDateColumnName);
        $stmt .= $dateClause;

        $retRelArgs = array($stmt, $cny, $ageReportDate);
        $retRelResults = QueryResult($retRelArgs);

        if (!$retRelResults || isEmptyArray($retRelResults)) {
            return;
        }

        foreach ($results as &$result) {

            if ($result['TRX_TOTALRETAINED'] != 0) {
                $prRecordKey = $result['RECORD#'];
                $sumReleasedRetainage = 0;
                foreach ($retRelResults as $retRelResult) {
                    if ($retRelResult['RETAINAGE_PRRECORDKEY'] == $prRecordKey) {
                        // sum them up
                        $sumReleasedRetainage = 
                            ibcadd($sumReleasedRetainage, 
                                $retRelResult['AMOUNTRELEASED'] ?? $retRelResult['TRX_AMOUNTRELEASED'], 14, 1);
                    }
                }
                if ($sumReleasedRetainage != 0) {
                    // remember it so that we can use the value later
                    $result['SUMRELEASEDRETAINAGE'] = $sumReleasedRetainage;
                }
            }
        }
    }

    /**
     * Replace special chars such as & with HTML codes
     * 
     * @param string &$str Input string to be sanitised
     *
     * @return string sanitised string
     */
    private function replaceSpecialChars($str)
    {
        return $str;
    }

    /**
     * @param  string $type
     * @param  string $xml
     *
     * @return string
     */
    function XMLPostProcess($type, $xml)
    {
        if ( $type === kShowExcel && $this->isUsingNewExcelFormat()) {
            return $xml;
        }

        return parent::XMLPostProcess($type, $xml);
    }

    /**
     * @return array
     */
    function GetXSLTParams()
    {
        $params = parent::GetXSLTParams();
        if ( $this->GetOutputType() === kShowExcel && $this->isUsingNewExcelFormat()) {
            $params['dataonly'] = 'N';
        }

        return $params;
    }

    /**
     * @return bool
     */
    function DoQuery()
    {
        $textTokens = [];
        AT($textTokens, 'IA.RETAINAGE_BALANCE');

        // Get Base Currency
        $basecurr = $this->GetCurrencies();
        $revalType = '';
        if(isset($this->params['REPORTINGCURRENCY']) && $this->params['REPORTINGCURRENCY'] != '') {
            $revalType = $this->params['REVALUATIONTYPE'];
        } 
        
        $currency = $this->params['CURRENCY'] ?: '';

        //  The query reads all customers/vendors, suppress for AAT.
        /** @noinspection PhpUnusedLocalVariableInspection - an autodestructor */
        $restorer = AdvAuditTracking::setManagedTracking(false);

        // call the private DoQuery to get the rows for aging
		if ( !$this->_DoQuery($results, $acctorder, false, $basecurr, $currency, $accounts,$allAccountsTypeMap) ) {
            return false;
        }
        unset($restorer);
        
        if( isset($this->params['DATE_COLUMNS']) && $this->params['DATE_COLUMNS'] != '') {
            $date_columns = explode('#~#', $this->params['DATE_COLUMNS']);
            foreach ($date_columns as $column_key) {
                $reportdata['reportheader'][0][$column_key][0]['cdata']= true;
            }
        }

        // pull params from param statement
        $_object = $this->params['object'];
        $_period = $this->params['period'];
        $_sumdet = $this->params['sumdet'];
        $_invdue = $this->params['invdue'];
        $_sortby = $this->params['sortby'];
        $title = $this->params['title'];
        $title2 = $this->params['title2'];
        $titlecomment = $this->params['titlecomment'];
        $showzeros = $this->params['showzeros'];
        $showretainageonly = $this->getIsRetainageEnabled() && $this->params['showretainageonly'] == 'yes';

        $agereportdate = ($this->params['agereportdate'] ?: '');

        $dateran = GetCurrentDate();
    
        if($_sumdet == 'Summary') {
            $asOffDateColSpan = 1; // setting default to 1 
        } else {
            $asOffDateColSpan = 2; // setting default to 2 because 2 colums are not inside any condition in detail mode
        }
        if($this->mcpEnabled) {
            $placeholders= [];//[[ 'name' => 'PRINT_TITLE', 'value' => I18N::getSingleToken($title)]];
            if ($revalType == 'base' ||($basecurr != '' && ($currency == '' || ($currency != '' && $currency != $basecurr && $this->params['EXCHRATETYPE'] != '')))) {
                // determining the exchange rate and the exchange as of date
                $exchMgr = Globals::$g->gManagerFactory->getManager('exchangerate');
                $exchratetype = ($this->params['EXCHRATETYPE'] ?: 'RECORD');
                $today = GetCurrentDate();
                $exchrate = null;
                $exchratedate = ($agereportdate ?: $today);
                if ($exchratetype == 'Intacct Daily Rate' && $agereportdate != ''
                    && DateCompare($agereportdate, $today) > 0
                ) {
                    // if we are using 'Intacct Daily Rate' but
                    // the as of date is a future date, we will not
                    // be able to find exchange rate for future. So
                    // we will only use current date to do the
                    // conversion.
                    $exchratedate = $today;
                }

                // display the exchange as of date in the header
                if ($this->atlas && $exchratetype == 'RECORD') {
                    $titleToken = "IA.AGE_REPORT_TITLE_CURR_DOC_EXCH_BASED";
                    $placeholders[] = [ 'name' => 'BASECURR', 'value' => $basecurr ];
                } elseif ($this->atlas && $currency != '') {
                    $repcurr = $this->params['REPORTINGCURRENCY'] ?: 'USD';
                    $titleToken = "IA.AGE_REPORT_TITLE_REP_CURR_EXCH_ASOF";
                    $placeholders[] = [ 'name' => 'CURRENCY', 'value' => $currency ];
                    $placeholders[] = [ 'name' => 'REPCURR', 'value' => $repcurr ];
                    $placeholders[] = [ 'name' => 'EXCHRATEDATE', 'value' => FormatDateForDisplay($exchratedate) ];
                } elseif ($currency == '' && $exchratetype == 'RECORD') {
                    $titleToken = "IA.AGE_REPORT_TITLE_DOC_EXCH_BASED";
                    $placeholders[] = [ 'name' => 'BASECURR', 'value' => $basecurr ];
                } else {
                    $titleToken = "IA.AGE_REPORT_TITLE_EXCH_ASOF";
                    $placeholders[] = [ 'name' => 'BASECURR', 'value' => $basecurr ];
                    $placeholders[] = [ 'name' => 'EXCHRATEDATE', 'value' => FormatDateForDisplay($exchratedate) ];
                }
            } elseif ($this->atlas && $this->params['EXCHRATETYPE'] == '' && $basecurr != '') {
                $titleToken = "IA.AGE_REPORT_TITLE_BASE_CURR_DOC_EXCH";
                $placeholders[] = [ 'name' => 'CURRENCY', 'value' => $currency ];
                $placeholders[] = [ 'name' => 'BASECURR', 'value' => $basecurr ];
                $exchratetype = 'RECORD';
            } else {
                // if we are only displaying base currency transactions
                $titleToken = "IA.AGE_REPORT_TITLE_NO_TRXN";
                $placeholders[] = [ 'name' => 'CURRENCY', 'value' => $currency ];
            }

            if($revalType == 'base') {
                $titleToken = $titleToken."_BASE_CURR";
            }
            $title .= " " . I18N::getSingleToken($titleToken, $placeholders, true);
        }

        /* @var array $gReport */
        global $gReport;
        global $trtypes,$fldidname,$ajtype;    
        global $gentot;      
        //$_done = Request::$r->_done;
        $_sess = Session::getKey();

        // some header stuff that will need to go into the map 
        $cny = GetMyCompany();
        $gReport['ROUNDING']='C';
        $vendcust = '';
        $vendcust_id = '';
        $vendcust_name = '';
        $vendcust_orig = '';
        $bill_or_invoice_date = '';

        switch($_object){
        case 'vendor':
            $bill_or_invoice = 'IA.BILL';
            $bill_or_invoice_date = 'IA.BILL_DATE';
            $_type = "vendor";
            $vendcust = 'IA.VENDOR';
            $vendcust_id = 'IA.VENDOR_ID';
            $vendcust_name = 'IA.VENDOR_NAME';
            $vendcust_orig = 'Vendor';
            break;
        case 'vendorgraph':
            $bill_or_invoice = 'IA.BILL';
            $bill_or_invoice_date = 'IA.BILL_DATE';
            $_type = "vendor";
            $vendcust = 'IA.VENDOR';
            $vendcust_id = 'IA.VENDOR_ID';
            $vendcust_name = 'IA.VENDOR_NAME';
            $vendcust_orig = 'Vendor';
            break;
        case 'customer':
            $bill_or_invoice = 'IA.INVOICE';
            $bill_or_invoice_date = 'IA.INVOICE_DATE';
            $_type = "customer";
            $vendcust = 'IA.CUSTOMER';
            $vendcust_id = 'IA.CUSTOMER_ID';
            $vendcust_name = 'IA.CUSTOMER_NAME';
            $vendcust_orig = 'Customer';
            break;
        case 'customergraph':
            $bill_or_invoice = 'IA.INVOICE';
            $bill_or_invoice_date = 'IA.INVOICE_DATE';
            $_type = "customer";
            $vendcust = 'IA.CUSTOMER';
            $vendcust_id = 'IA.CUSTOMER_ID';
            $vendcust_name = 'IA.CUSTOMER_NAME';
            $vendcust_orig = 'Customer';
            break;
        case 'employee':
            $bill_or_invoice = 'IA.EXPENSE';
            $bill_or_invoice_date = 'IA.EXPENSE_DATE';
            $_type = "employee";
            $vendcust = 'IA.EMPLOYEE';
            $vendcust_id = 'IA.EMPLOYEE_ID';
            $vendcust_name = 'IA.EMPLOYEE_NAME';
            $vendcust_orig = 'Employee';
            break;
        case 'employeegraph':
            $bill_or_invoice = 'IA.EXPENSE';
            $bill_or_invoice_date = 'IA.EXPENSE_DATE';
            $_type = "employee";
            $vendcust = 'IA.EMPLOYEE';
            $vendcust_id = 'IA.EMPLOYEE_ID';
            $vendcust_name = 'IA.EMPLOYEE_NAME';
            $vendcust_orig = 'Employee';
            break;
        default:
            dieFL('The object was not set properly in APARAgeReporter.cls');
            break;
        }

        /** @noinspection PhpUndefinedVariableInspection */
        if ( $_type == 'vendor') {
            $trtypes = array('pi', 'ci', 'cc');
            $ajtype = 'pa';
            $fldidname = 'VENDORID';
        } else if ($_type == 'employee') {
            $trtypes = array('ei');
            $fldidname = 'EMPLOYEEID';
        } else {
            $trtypes = array('ri');
            $ajtype = 'ra';
            $fldidname = 'CUSTOMERID';
        }
        
        $reportdata['reportheader'][0]['title'][0]['cdata'] = $title;
                
        $loc = $this->params['LOCATION'];
        $locgrp = $this->params['ORIG_LOCATION'];
        if($locgrp) {
            $reportdata['reportheader'][0]['location'][0]['cdata'] = I18N::getSingleToken("IA.LOCATION_LOCID", [ [ 'name' => 'LOCATIONID', 'value' => $this->params['ORIG_LOCATION'] ] ]) ;
        }
        else if($loc && ! $locgrp) {
            $reportdata['reportheader'][0]['location'][0]['cdata'] = I18N::getSingleToken("IA.LOCATION_LOCID", [ [ 'name' => 'LOCATIONID', 'value' => $this->params['LOCATION'][0] ] ]);
        }
        else {
            $reportdata['reportheader'][0]['location'][0]['cdata'] = "";
        }
        if($this->params['LOCATION_IR'] === 'true') {
            $reportdata['reportheader']['0']['location'][0]['cdata'] = I18N::getSingleToken("IA.LOCATION_LOCID", [ [ 'name' => 'LOCATIONID', 'value' => $this->params['LOCATION'][0] ] ]);
        }

        $dept = $this->params['DEPARTMENT'];
        $deptgrp = $this->params['ORIG_DEPARTMENT'];
        if($deptgrp) {
            $reportdata['reportheader'][0]['department'][0]['cdata'] = I18N::getSingleToken("IA.DEPARTMENT_DEPTID", [ [ 'name' => 'DEPARTMENTID', 'value' => $this->params['ORIG_DEPARTMENT'] ] ]);
        }
        else if($dept && ! $deptgrp) {
            $reportdata['reportheader'][0]['department'][0]['cdata'] = I18N::getSingleToken("IA.DEPARTMENT_DEPTID", [ [ 'name' => 'DEPARTMENTID', 'value' => $this->params['DEPARTMENT'][0] ] ]);
        }
        else {
            $reportdata['reportheader'][0]['department'][0]['cdata'] = "";
        }
        if($this->params['DEPARTMENT_IR'] === 'true') {
            $reportdata['reportheader']['0']['department'][0]['cdata'] = I18N::getSingleToken("IA.DEPARTMENT_DEPTID", [ [ 'name' => 'DEPARTMENTID', 'value' => $this->params['DEPARTMENT'][0] ] ]);
        }
                
        //$reportdata['reportheader'][0]['title2'][0]['cdata'] = $gReport['TITLE2'];
        //$reportdata['reportheader'][0]['titlecomment'][0]['cdata'] = $gReport['TITLECOMMENT'];
        $reportdata['reportheader'][0]['title2'][0]['cdata'] = $this->replaceSpecialChars($title2);
        $reportdata['reportheader'][0]['titlecomment'][0]['cdata'] = $this->replaceSpecialChars($titlecomment);
        $reportdata['reportheader'][0]['company'][0]['cdata'] = $cny;
        $reportdata['reportheader'][0]['companyname'][0]['cdata'] = $this->replaceSpecialChars(GetMyCompanyName());
        $reportdata['reportheader'][0]['sumdet'][0]['cdata'] = $_sumdet;
        $reportdata['reportheader'][0]['sortby'][0]['cdata'] = $_sortby;
        $reportdata['reportheader'][0]['agereportdate'][0]['cdata'] = FormatDateForDisplay($agereportdate);
        $reportdata['reportheader'][0]['dateran'][0]['cdata'] = FormatDateForDisplay($dateran);
        $reportdata['reportheader'][0]['datetime'][0]['cdata']     = GetCurrentDate(IADATE_USRFORMAT) . ", " .GetCurrentTZTime();
        $reportdata['reportheader'][0]['grpby'][0]['cdata'] = $this->params['GROUPBY'];
        $reportdata['reportheader'][0]['grpbyname'][0]['cdata'] = $this->_groupmap[$this->params['object']]['Label'][$this->params['GROUPBY']];
        $reportdata['reportheader'][0]['orientation'][0]['cdata'] = $this->params['ORIENTATION'];

        if($this->params['GROUPBY'] !== 'None' && $this->params['sumdet'] !== 'Detail') {
            $this->_isGrpBySumm = true;
        }
        
        if( $_type === 'employee' || $_invdue=='Duedate') {
            $reportdata['reportheader'][0]['hideduedate'][0]['cdata'] = 'y';
        }
        else {
            $reportdata['reportheader'][0]['hideduedate'][0]['cdata'] = 'n';
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $reportdata['reportheader'][0]['vendcust'][0]['cdata'] = $this->replaceSpecialChars($vendcust);
        $reportdata['reportheader'][0]['vendcust_id'][0]['cdata'] = $this->replaceSpecialChars($vendcust_id);
        $reportdata['reportheader'][0]['vendcust_name'][0]['cdata'] = $this->replaceSpecialChars($vendcust_name);
        $reportdata['reportheader'][0]['vendcust_orig'][0]['cdata'] = $vendcust_orig;
        /** @noinspection PhpUndefinedVariableInspection */
        $reportdata['reportheader'][0]['billorinvoice'][0]['cdata'] = $bill_or_invoice;
        $reportdata['reportheader'][0]['billorinvoicedate'][0]['cdata'] = $bill_or_invoice_date;
        if ($_invdue=='Invoicedate') {            
            $reportdata['reportheader'][0]['invdue'][0]['cdata'] = $bill_or_invoice_date ;
        } elseif ($_invdue=='Duedate') {
            $reportdata['reportheader'][0]['invdue'][0]['cdata'] = 'IA.DUE_DATE';
        } else {
            $reportdata['reportheader'][0]['invdue'][0]['cdata'] = 'IA.GL_POSTING_DATE';
        }
        $reportdata['reportheader'][0]['sess'][0]['cdata'] = $_sess;
        //$reportdata['reportheader'][0]['done'][0]['cdata'] = $_done;
        //$reportdata['reportheader'][0]['backhere'][0]['cdata'] = $_backhere;

        if (($this->mcpEnabled || $this->atlas) && $this->params['report'] != 'apagegraph' && $this->params['report'] != 'aragegraph') {
            $asOffDateColSpan = ($_sumdet != 'Summary')? ( $asOffDateColSpan + 2 ) :  $asOffDateColSpan ;
            $reportdata['reportheader'][0]['MULTICURRENCY'][0]['cdata'] = 'true';
            $reportdata['reportheader'][0]['currency'][0]['cdata'] = 'IA.TXN_CURRENCY';
            $reportdata['reportheader'][0]['trxcurrency'][0]['cdata'] = $this->params['CURRENCY'];
            $reportdata['reportheader'][0]['trxamount'][0]['cdata'] = 'IA.TRX_AMOUNT';
        }

        $reportdata['reportheader'][0]['RETAINAGE_ENABLED'][0]['cdata'] = ( $this->getIsRetainageEnabled() ? 'true' : '' );

        if ( $this->getIsRetainageEnabled() ) {
            $reportdata['reportheader'][0]['totalretained'][0]['cdata'] = GT($textTokens, 'IA.RETAINAGE_BALANCE');
        }

        $reportdata['reportheader'][0]['noofdays'][0]['cdata'] = 'IA.DAYS_AGED';

        $ageperiods = $_period;
        if (empty($ageperiods)) {
            $ageperiods = '0-30,31-60,61-90,91-120';
        }

        // build the array that describes the starting and ending timeframes
        $agecols=explode(',', $ageperiods);
        $dumbcounter = 0;

        // we need the min and max to help with the query do determine which accts to pull			
        $agcmin=0;
        $agcmax=0;
        foreach ($agecols as $ag) {
            $ag = isl_trim($ag);
            if($ag != '') {
                [$agcstart,$agcend]=explode('-', $ag);

                $agcstart    = isl_trim($agcstart);
                $agcend    = isl_trim($agcend);
             
                if ($agcstart=='') {
                    $agcstart='999999';
                }
                if ($agcend=='') { 
                    $agcend='999999';
                }

                if ( ($agcstart != '' && $agcend != '' ) && $agcstart > $agcend ) { 
                    $agcstart= -$agcstart;
                    $agcend= -$agcend;
                }

                $ageper[$ag]['start']=$agcstart;
                $ageper[$ag]['end']=$agcend;

                // remember the min and max values for later
                if ($agcstart<$agcmin) {
                    $agcmin=$agcstart;
                }
                if ($agcend>$agcmax) {
                    $agcmax=$agcend;
                }
            
                $reportdata['reportheader'][0]['allageperiods'][0]['ageperiod'][$dumbcounter]['id'] = 'P'.$dumbcounter;
                $reportdata['reportheader'][0]['allageperiods'][0]['ageperiod'][$dumbcounter]['cdata']=$ag;
                $reportdata['reportheader'][0]['numofageperiods'][0]['cdata']=$dumbcounter+1;
                $dumbcounter++;
            } else {
                /** @noinspection PhpUndefinedVariableInspection */
                $f += 1;
            }
        }
        if($this->GetOutputType() ==  kShowPDF) {
            //Calculating the page width dynamically based on the number of aging buckets
            //$pagewidth = ($reportdata['reportheader'][0]['sumdet'][0]['cdata'] == 'Summary') ? 125 : (($this->mcpEnabled) ? 155 :132);
            $extraGroupWidth = 0;
            if (preg_match('/TYPE/', $this->params['GROUPBY']) || preg_match('/PRIORITY/', $this->params['GROUPBY'])) { 
                $extraGroupWidth = 18;
            }
            else if (preg_match('/DATE/', $this->params['GROUPBY'])) {
                $extraGroupWidth = 12;
            }

            $pagewidth = ($reportdata['reportheader'][0]['sumdet'][0]['cdata'] == 'Summary') ? 125 : (($this->mcpEnabled) ? 270 :224);
            $pagewidth += $extraGroupWidth;
            $pagewidth += ($reportdata['reportheader'][0]['numofageperiods'][0]['cdata'] * 26);
            $pagewidth += 20; // pad a little for the wider total
            if ($this->getIsRetainageEnabled()) {
                // add the width of the retainage balance column.
                $pagewidth += 30;
            }

            //Default width for Landscape:297 and for Portrait: 210 
            if ($pagewidth > 240) {
                $this->params['ORIENTATION'] = 'L' ;
                $reportdata['reportheader'][0]['orientation'][0]['cdata'] = 'L';
            }
            if ($this->params['ORIENTATION'] == 'L') {
                $pagewidth = ($pagewidth < 297) ? 297 : $pagewidth;
            }
            else {
                $pagewidth = ($pagewidth < 210) ? 210 : $pagewidth;
            }
            $reportdata['reportheader'][0]['pagewidth'][0]['cdata']= $pagewidth;
            //$reportdata['reportheader'][0]['pagewidth'][0]['cdata']= 400;
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $agecolscnt = count($agecols) - $f;

        // initialize the totals values for each aging bucket.
        for ($gtl=0;$gtl<$agecolscnt;$gtl++) {
            $gentot[$agecols[$gtl]]=0.0;
        }
        $gentot['total']=0.0;
        $gentot['retainage_bal_total']=0.0;

        $accountcounter = 0;
        
        // group the query results in a map by entity.
        $prrecordsByEntity = array();
        $arVoidedPayments = $voidedRecordsToIgnore = array();
        $grpBy = $this->_groupmap[$_object]['DB'][$this->params['GROUPBY']];
        $fldid = $this->_groupmap[$this->params['object']]['FldIdName'];
        $vendcustAgeMap = array();
        //Added for stable ordering
        $oIndex = 0;

        if($grpBy =='CONTACT.LASTNAME'){
            $grpBy = 'LASTNAME FIRSTNAME';
        }


        foreach($results as $rec){
            $rec['ORDERINDEX'] = $oIndex;
            $oIndex++;
            
            $entgrp = $rec[$grpBy];
            $prrecordsByEntity[$entgrp][] = $rec;
            
            if( $_sumdet != 'A' && $rec['STATE'] == 'V' && in_array($rec['RECORDTYPE'], array('rp', 'rr')) ) {
                $arVoidedPayments[$rec['RECORD#']] = $rec;
            }
            if(in_array($_sortby, self::PRRECORD_SORT_FIELDS) &&
                !isset($acctorder[$rec['ENTITY']]['TRX_TOTALDUE'])){
                if(!isset($acctorder[$rec['ENTITY']]['CREATED'])){
                    $acctorder[$rec['ENTITY']]['CREATED'] = $rec['CREATED'];
                    $acctorder[$rec['ENTITY']]['CREATED_SORT'] = $rec['CREATED_SORT'];
                }
                $acctorder[$rec['ENTITY']]['TOTALDUE'] += $rec['TOTALDUE'];
            }
        }
        
        if ( $this->params['GROUPBY'] !== 'None'){
            if($this->params['GROUPBY'] !== $_sortby && in_array($_sortby, array('VENDORID', 'EMPLOYEEID', 'CUSTOMERID', 'NAME', 'CONTACT.LASTNAME'))) {
                $this->ReOrderPrrecordsByEntity($prrecordsByEntity, $accounts, $_sortby);
            }else{
                $this->ReOrderPrrecordsByEntity($prrecordsByEntity, $accounts, $fldid);
            }
        }
        if( count($arVoidedPayments) > 0 ) {
            foreach( $arVoidedPayments as $rec){
                $reversalRec = $arVoidedPayments[$rec['VOIDPAYMENTKEY']];
                if( $reversalRec['RECORD#'] != '' && ibcabs($rec['TDUE']) == ibcabs($reversalRec['TDUE']) ) {
                    $voidedRecordsToIgnore[] = $rec['RECORD#'];
                    $voidedRecordsToIgnore[] = $rec['VOIDPAYMENTKEY'];
                }
            }
        }
        unset($vendcustAgeMap);
        // now loop through all the accounts in the specified order and create the data
        $acctorder = ( $acctorder ?: array() );
        if ($grpBy === 'ENTITY') {
            $skipArr = $this->_vendcustNameMap;
        } else {
            $skipArr = $acctorder;
        }

        $tmpGrpBy = array();
        $agingAmt = array();
        $aatKeys = [];
        $moduleName = Request::$r->_mod;
        $multientity = (IsMultiEntityCompany())? 'Y' : 'N';
        $contextlocation = (GetContextLocation())?: '';
        $op='';

        if ($moduleName == 'ar') {
            $op = GetOperationId("$moduleName/lists/customer/view");
        } elseif ($moduleName == 'ap') {
            $op = GetOperationId("$moduleName/lists/vendor/view");
        } elseif ($moduleName == 'ee') {
            $op = GetOperationId("$moduleName/lists/employee/view");
        }


        switch($_sortby){
            case 'GLPOSTDATE':
                $keys = array_column($acctorder, 'CREATED_SORT');
                array_multisort($keys, SORT_ASC, $acctorder);
                break;
            case 'GLPOSTDATE_DESC':
                $keys = array_column($acctorder, 'CREATED_SORT');
                array_multisort($keys, SORT_DESC, $acctorder);
                break;
            case 'AMOUNT':
                $keys = array_column($acctorder, 'TOTALDUE');
                array_multisort($keys, SORT_ASC, $acctorder);
                break;
            case 'AMOUNT_DESC':
                $keys = array_column($acctorder, 'TOTALDUE');
                array_multisort($keys, SORT_DESC, $acctorder);
                break;            
        }
        foreach( $acctorder as $key => $val ) {
            $entid = ($grpBy === 'ENTITY') ? $this->_vendcustNameMap[$val['ENTITY']][$fldidname] : $val[$fldidname];
            $entname = ( ($_type == 'employee' && ($val['LASTNAME'] || $val['FIRSTNAME'])) ? $val['LASTNAME'].", ".$val['FIRSTNAME'] : $val['NAME']);

            // Change format for date values
            if(isl_strstr($this->params['GROUPBY'], "DATE")) {
                $entid = FormatDateForDisplay($entid);
            }
            $Id=$entid;
            $tottrn = array();
            $tottrn['total']=0.0;
            $tottrn['retainage_bal_total'] = 0.0;

            for ($tl=0;$tl<$agecolscnt;$tl++) {
                $tottrn[$agecols[$tl]]=0.0;
            }
            // now build the array with the results in it.  This array will go into the domap part of things
            
            $trn=array();
            $prrecords =  $prrecordsByEntity[$val['ENTITY']] ?? [];
            $prrecordscnt = count($prrecords);

            // if user is filtering for vendor or vendor type we shld filter accordingly

            if(!array_key_exists($val[$grpBy], $skipArr) ) {
                continue;
            }
            
            $summacctidx = array();
            $hasNonZero = false; // renamed from $hasNonZeroDue to handle both cases, 1. has non-zero due, and 2. has non-zero retainage
            $recidused = false;
            // go through all the records returned for this vendor/customer
            for ($i=0;$i<$prrecordscnt;$i++) {
                $rec=$prrecords[$i];

                $totalretained = ( $this->mcpEnabled ? $rec['TRX_TOTALRETAINED'] : $rec['TOTALRETAINED'] );
                $totalreleased = 0;
                if (isset($rec['SUMRELEASEDRETAINAGE']) && $rec['SUMRELEASEDRETAINAGE'] != 0) {
                    $totalreleased = $rec['SUMRELEASEDRETAINAGE'];
                }
                $retainageBalance = ibcsub($totalretained, $totalreleased, 2);

                $recentname = ( ($_type == 'employee' && ($rec['LASTNAME'] || $rec['FIRSTNAME'])) ? $rec['LASTNAME'].", ".$rec['FIRSTNAME'] : $rec['NAME']);
                // reset object id and value by default
                $recentid = "";
                $recentnme = "";

                // we need to ignore/skip processing voided records for AR payments/overpayments/advances
                // if the original and voided records have same totaldue.
                if ( in_array($rec['RECORD#'], $voidedRecordsToIgnore) ) {
                    if($rec['ENTITY'] != $prrecords[$i+1]['ENTITY']) {
                        $recidused = false;
                    }
                    continue;
                }

                $foundTransferLink = !empty($rec['TRANSFERKEY']);                    

                if ($_sumdet=='Detail') {
                    //$tmpinvno=$rec['RECORDID'];   // commented because sometimes the billno. might be more than 9 chars. 
                    //$trn[$i]['DOCNUMBER']=isl_substr($tmpinvno,0,9);
                    $trn[$i]['DOCNUMBER']=$rec['RECORDID'];
                    $trn[$i]['RECORD#']=$rec['RECORD#'];
                    $trn[$i]['RECORDTYPE']=$rec['RECORDTYPE'];
                }

                $nd=isl_trim($rec['NODAYS']);
                
                // go through all the possible age columns and put the amounts in the right ones.
                $thislinetotal=0.0;
                
                // if there was a non-zero total, be sure to include the identifying info, but only once!
                if(!in_array($entid.'#~#'.$rec['ENTITY'], $tmpGrpBy)) {

                    if($this->params['GROUPBY'] != 'None') {
                        $recentid = $this->_vendcustNameMap[$rec['ENTITY']][$fldid];
                        $recentnme = $recentname;
                    }
                    if(!$this->_isGrpBySumm) {
                        $tmpGrpBy = array();
                    }
                    $tmpGrpBy[] = $entid.'#~#'.$rec['ENTITY'];
                }

                for ($cl=0;$cl<$agecolscnt;$cl++) {
                    $trn[$i][$agecols[$cl]]=0.0;

                    /** @noinspection PhpUndefinedVariableInspection */
                    if ( ( $nd >= $ageper[$agecols[$cl]]['start'] ) && ( $nd <= $ageper[$agecols[$cl]]['end'])) {
                        $thistotaldue=0.0;

                        // if the record type is in our list of "flipsign" record types, flip the sign please.
                        if ($revalType == 'base') {
                            //Calculate the total based on basecurrency.
                            // getting the exchange rate if not stored
                            /** @noinspection PhpUndefinedVariableInspection */
                            if ( $exchratetype != 'RECORD' && ! $exchrate[$rec['BILLBASECURR']]) {
                                    $basecurrTrans = ($basecurr == $rec['CURRENCY'] && $rec['CURRENCY'] == $rec['BILLBASECURR']) ? 1 : 0 ;
                                /** @noinspection PhpUndefinedVariableInspection */
                                $exchrate[$rec['BILLBASECURR']] = ($basecurrTrans) ?: $exchMgr->GetTrxExchangeRate($exchratetype, $rec['BILLBASECURR'], $basecurr, $exchratedate);
                            }                        
                            // totaldue is in base currency
                            /** @noinspection PhpUndefinedVariableInspection */
                            $thistotaldue = $this->FigureTotal(ibcmul($rec['TOTALDUE'], $exchrate[$rec['BILLBASECURR']], 14, 1), $rec['RECORDTYPE']);
                                
                        } /** @noinspection PhpUndefinedVariableInspection */
                        elseif ( $exchratetype && $rec['CURRENCY'] != '' && $rec['CURRENCY'] != $basecurr) {
                            if (bccomp($rec['TOTALDUE'], '0') == 0) {
                                if($rec['ENTITY'] != $prrecords[$i+1]['ENTITY']) {
                                    $recidused = false;
                                }
                                continue;
                            }

                            // getting the exchange rate if not stored
                                /** @noinspection PhpUndefinedVariableInspection */
                                if ( $exchratetype != 'RECORD' && ! $exchrate[$rec['CURRENCY']]) {
                                    /** @noinspection PhpUndefinedVariableInspection */
                                    $exchrate[$rec['CURRENCY']] = $exchMgr->GetTrxExchangeRate($exchratetype, $rec['CURRENCY'], $basecurr, $exchratedate);
                            }
                                /** @noinspection PhpUndefinedVariableInspection */
                                if ( $exchratetype != 'RECORD' && $exchrate[$rec['CURRENCY']]) {
                                // totaldue here is to be converted by transaction currency using the exchange rate
                                $thistotaldue = $this->FigureTotal(ibcmul($rec['TRX_TOTALDUE'], $exchrate[$rec['CURRENCY']], 14, 1), $rec['RECORDTYPE']);
                            } else {
                                // totaldue here is in base currency
                                $thistotaldue = $this->FigureTotal($rec['TOTALDUE'], $rec['RECORDTYPE']);
                            }
                        } elseif ($rec['CURRENCY'] != '') {
                            // totaldue is in foreign currency
                            $thistotaldue = $this->FigureTotal($rec['TRX_TOTALDUE'], $rec['RECORDTYPE']);
                        } else {
                            // totaldue is in base currency
                            $thistotaldue = $this->FigureTotal($rec['TOTALDUE'], $rec['RECORDTYPE']);
                        }
                        // $thistotaldue = ($this->params['CURRENCY'] != '' && $this->params['CURRENCY'] != $basecurr) ? $this->FigureTotal($rec['TRX_TOTALDUE'],$rec['RECORDTYPE']) : $this->FigureTotal($rec['TOTALDUE'],$rec['RECORDTYPE']);

                        // fill in this row
                        $trn[$i][$agecols[$cl]] = ibcadd($trn[$i][$agecols[$cl]], $thistotaldue, 14, 1);
                        $trn[$i]['total']=ibcadd($trn[$i]['total'], $thistotaldue, 14, 1);
                        $thislinetotal=ibcadd($thislinetotal, $thistotaldue, 14, 1);

                        // add to totals for this company
                        $tottrn[$agecols[$cl]]=ibcadd($tottrn[$agecols[$cl]], $thistotaldue, 14, 1);
                        $tottrn['total']=ibcadd($tottrn['total'], $thistotaldue, 14, 1);

                        // add to to totals for this report
                        $gentot[$agecols[$cl]]=ibcadd($gentot[$agecols[$cl]], $thistotaldue, 14, 1);
                        $gentot['total']=ibcadd($gentot['total'], $thistotaldue, 14, 1);

                        if ( $this->getIsRetainageEnabled() ) {
                            $tottrn['retainage_bal_total'] = ibcadd($tottrn['retainage_bal_total'], $retainageBalance, 14, 1);
                            $gentot['retainage_bal_total'] = ibcadd($gentot['retainage_bal_total'], $retainageBalance, 14, 1);
                        }

                        if($this->_isGrpBySumm) {
                            $agingAmt[$accountcounter]['data'][$rec['ENTITY']][$cl] += $thistotaldue;
                        }

                    }
                    else {
                        if($this->_isGrpBySumm) {
                            $agingAmt[$accountcounter]['data'][$rec['ENTITY']][$cl] += 0;
                        }
                    }
                    if($this->_isGrpBySumm) {
                        if ($recentid == '') {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$summacctidx[$rec['ENTITY']]]['period'][0]['P'.$cl] =
                              round($agingAmt[$accountcounter]['data'][$rec['ENTITY']][$cl], 2);
                        }
                        else {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][0]['P'.$cl] =
                            round($agingAmt[$accountcounter]['data'][$rec['ENTITY']][$cl], 2);
                            $summacctidx[$rec['ENTITY']] = $i;
                        }
                    }
                }

                // calulcate summary total whenever we are grouping by some col
                if($this->_isGrpBySumm) {
                    if ($recentid == '') {
                        /** @noinspection PhpUndefinedVariableInspection */
                        $agingAmt[$accountcounter]['data'][$rec['ENTITY']]['total'] += $thistotaldue;
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$summacctidx[$rec['ENTITY']]]['total'][0]['amount'][0]['cdata'] =
                        round($agingAmt[$accountcounter]['data'][$rec['ENTITY']]['total'], 2);
                        unset($reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['total'][0]['amount'][0]['cdata']);
                    }
                    else {
                        /** @noinspection PhpUndefinedVariableInspection */
                        $agingAmt[$accountcounter]['data'][$rec['ENTITY']]['total'] = $thistotaldue;
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['total'][0]['amount'][0]['cdata'] =
                        round($agingAmt[$accountcounter]['data'][$rec['ENTITY']]['total'], 2);

                    }

                    if ( $this->getIsRetainageEnabled() ) {
                        $agingAmt[$accountcounter]['data'][$rec['ENTITY']]['retainage_bal_total'] += $retainageBalance;
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['total'][0]['retainage_bal_total'][0]['cdata'] = $retainageBalance;
                    }
                }
                // do not put this in the report if the total for this account was zero
                $doNonZeroProcessing = $thislinetotal != 0;
                if ($showretainageonly && !$doNonZeroProcessing) {
                    // well, not so fast. if 'Show fully paid invoices with outstanding retainage' is set to 'Yes',
                    // and this one wasn't already included due to non-zero line total, then let's give it another chance
                    $dateIsGood = false;
                    // check if date is good
                    for ($cl = 0; $cl < $agecolscnt; $cl++) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        if (($nd >= $ageper[$agecols[$cl]]['start']) && ($nd <= $ageper[$agecols[$cl]]['end'])) {
                            $dateIsGood = true; // yes, date is good
                        }
                    }
                    // but only include it if it has a non-zero retainage balance
                    $doNonZeroProcessing = $dateIsGood && ($retainageBalance != 0);
                }
                if ($doNonZeroProcessing) {
                    $hasNonZero = true;
                    unset($allAccountsTypeMap[$rec['ENTITY']]);

                    if ($recentid != '') {
                        $Id=$recentid;
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['vid'][0]['cdata'] = $this->replaceSpecialChars($recentid);
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['vname'][0]['cdata'] = $this->replaceSpecialChars($recentnme);
                        $recidused = true;
                    }

                    $script = "editor.phtml?.r=".$Id."&.op=".$op."&.isapopup=1&.popup=1";
                    if ( $_sumdet=='Detail') {
                       if ($_invdue=='Invoicedate') {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['whendue'][0]['cdata'] = FormatDateForDisplay($rec['WHENCREATED']);
                        } elseif($_invdue == 'Duedate') {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['whendue'][0]['cdata'] = FormatDateForDisplay($rec['WHENDUE']);
                        }else {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['whendue'][0]['cdata'] = FormatDateForDisplay($rec['CREATED']);
                        }

                        if(isset($reportdata['reportheader'][0]['DisplayDueDate'])) {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['duedate'][0]['cdata'] = FormatDateForDisplay($rec['WHENDUE']);
                        }
                        if(isset($reportdata['reportheader'][0]['DisplayTxnDate'])) {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['createddate'][0]['cdata'] = FormatDateForDisplay($rec['WHENCREATED']);
                        }
                        if(isset($reportdata['reportheader'][0]['DisplayGLPostingDate'])) {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['postingdate'][0]['cdata'] = FormatDateForDisplay($rec['CREATED']);    
                        }

                        if ( $rec['RECORDID'] != '' ) {
                            if($this->GetOutputType() ==  kShowPDF) {
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['docnumber'][0]['cdata'] = chunk_split($rec['RECORDID'], 17);
                            } else {
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['docnumber'][0]['cdata'] = $this->replaceSpecialChars($rec['RECORDID']);
                            }
                        }else {
                            if($this->GetOutputType() ==  kShowPDF) {
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['docnumber'][0]['cdata'] = chunk_split($rec['DOCNUMBER'], 17);
                            }else{
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['docnumber'][0]['cdata'] = $this->replaceSpecialChars($rec['DOCNUMBER']);
                            }

                        }
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['noofdays'][0]['cdata'] = floatval($prrecordsByEntity[$val['ENTITY']][$i]['NODAYS']);
                        if ($showretainageonly && $thislinetotal == 0) {
                            // if there is nothing in the aging columns (the ones shown on the report), and the invoice is in the report because of retainage balance,
                            // blank out "Days aged" column
                            unset($reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['noofdays'][0]['cdata']);
                        }
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['recordnumber'][0]['cdata'] = $rec['RECORD#'];
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['recordtype'][0]['cdata'] = $rec['RECORDTYPE'];
                        
                        if($this->mcpEnabled && $this->params['report'] != 'apagegraph' && $this->params['report'] != 'aragegraph') {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['currency'][0]['cdata']=$rec['CURRENCY'];
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['trxamount'][0]['cdata']=
                            Currency(round($this->FigureTotal($rec['TRX_TOTALDUE'], $rec['RECORDTYPE']), 2), 1, 1);
                        }

                        if ( $this->getIsRetainageEnabled() ) {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['retainage_bal'][0]['cdata']=
                                Currency(round($this->FigureTotal($retainageBalance, $rec['RECORDTYPE']), 2), 1, 1);
                        }

                        // fill the report array from the values we calculated for each aging period						
                        $zeroFormatted = Currency(0, 1, 1, $basecurr);
                        for ($cl=0;$cl<$agecolscnt;$cl++) {
                            //$reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][$cl]['name'] = $agecols[$cl];
                             
                            if($revalType == 'base') {
                                // put the translated amount into the right bucket	 
                                /** @noinspection PhpUndefinedVariableInspection */
                                $tmpval = round(ibcmul($this->FigureTotal($rec['TOTALDUE'], $rec['RECORDTYPE']), $exchrate[$rec['BILLBASECURR']], 14, 1), 2);
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][0]['P'.$cl] = $trn[$i][$agecols[$cl]] ? Currency($tmpval, 1, 1, $basecurr): $zeroFormatted;
                                $agingAmt[$accountcounter]['data'][$i][$cl] = $trn[$i][$agecols[$cl]] ? $tmpval : 0;
                            } /** @noinspection PhpUndefinedVariableInspection */
                            elseif ( $exchrate[$rec['CURRENCY']]) {
                                // put the translated amount into the right bucket
                                $tmpval = round(ibcmul($this->FigureTotal($rec['TRX_TOTALDUE'], $rec['RECORDTYPE']), $exchrate[$rec['CURRENCY']], 14, 1), 2);
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][0]['P'.$cl] = $trn[$i][$agecols[$cl]] ? Currency($tmpval, 1, 1, $basecurr) : $zeroFormatted;
                                $agingAmt[$accountcounter]['data'][$i][$cl] = $trn[$i][$agecols[$cl]] ? $tmpval : 0;
                            } elseif ($rec['CURRENCY'] == $basecurr) {
                                // put the transaction amount into the right bucket if the transaction currency is the same as the base/reporting currency
                                $tmpval = round($this->FigureTotal($rec['TRX_TOTALDUE'], $rec['RECORDTYPE']), 2);
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][0]['P'.$cl] = $trn[$i][$agecols[$cl]] ? Currency($tmpval, 1, 1, $basecurr) : $zeroFormatted;
                                $agingAmt[$accountcounter]['data'][$i][$cl] = $trn[$i][$agecols[$cl]] ? $tmpval : 0;
                            } else {
                                // put the base amount into the right bucket
                                $tmpval = round($trn[$i][$agecols[$cl]], 2);
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][0]['P'.$cl] = Currency($tmpval, 1, 1, $basecurr);
                                $agingAmt[$accountcounter]['data'][$i][$cl] = $tmpval;
                            }
                            // $_done=something here!!!!
                        }        

                        // GET EDIT FILE TO USE
                        $editorClassedTypes = array('ri', 'ra', 'pr','pi','pa', 'ei','ea','er');
                        if (IsCashMgmtRecordType($rec['RECORDTYPE'])) {
                            $edit = GetCMTransEditFile($rec['RECORDTYPE'], $foundTransferLink);
                        }elseif(in_array($rec['RECORDTYPE'], $editorClassedTypes)) {
                            $edit = 'editor.phtml';
                        }elseif($rec['RECORDTYPE'] == 'rr') {
                            $edit = 'prrecord.phtml';
                        }else{
                            $edit = 'edit_invoice.phtml';
                        }

                        $mod = 'ar';
                        //We have to reset this bcoz once it get set and the next record is 'ri' the value was carry
                        // forwarding to invoice and GL Posting date goes wrong
                        $thebatchkey = '';
                        // GET THE OPERATION ID TO USE
                        if ($rec['RECORDTYPE'] == 'ri') {
                            $theop = GetOperationId('ar/lists/arinvoice/view');
                        }elseif($rec['RECORDTYPE'] == 'ra') {
                            $theop = GetOperationId('ar/lists/aradjustment/view');
                        }elseif ($rec['RECORDTYPE'] == 'pi') {
                            $theop = GetOperationId('ap/lists/apbill/view');
                            $mod = 'ap';
                        }elseif($rec['RECORDTYPE'] == 'pa') {
                            $theop = GetOperationId('ap/lists/apadjustment/view');
                            $mod = 'ap';
                        }elseif($rec['RECORDTYPE'] == 'pr') {
                            $theop = GetOperationId('ap/lists/appostedadvance/view');
                            $mod = 'ap';
                        }elseif($rec['RECORDTYPE'] == 'rr') {
                            $theop = GetChildPermFormRecType('rp');
                            $thebatchkey = $rec['PRBATCHKEY'];
                        }elseif ($rec['RECORDTYPE'] == 'ei') {
                            $theop = GetOperationId('ee/lists/eexpenses/view');
                            $mod = 'ee';
                        }elseif ($rec['RECORDTYPE'] == 'ea') {
                            $theop = GetOperationId('ee/lists/expenseadjustments/view');
                            $mod = 'ee';
                        }elseif ($rec['RECORDTYPE'] == 'er') {
                            $theop = GetOperationId('ee/lists/eepostedadvance/view');
                            $mod = 'ee';
                        } else{
                            $theop = GetChildPermFormRecType($rec['RECORDTYPE']);
                        }

                        // CREATE THE URL FOR THE LINKS
                        // the 'isapopup' tells edit_invoice that it should put in a close button, not a return.
                        $targetUrl = $edit . "?.recordtype=" . $rec['RECORDTYPE'] . "&.op=" . $theop  . 
                                                "&.r=" . $rec['RECORD#'] . "&.isapopup=1&.popup=1";

                        if (isset($thebatchkey) && $thebatchkey != '') {
                            $targetUrl .= "&.batch=$thebatchkey"; 
                        }
                        // only include link in html reports							
                        if ($this->params['type']==kShowHTML) {
                            // check to see if link is in nolinks array - payments do not have links right now.
                            //check if multientity company and record belongs to a different location
                            if (!isset($this->nolinks[$rec['RECORDTYPE']])) {
                                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['total'][0]['link'][0]['cdata'] =
                                "javascript:ReportLaunch($cny,'$multientity','" . $rec['LOCATIONKEY'] . 
                                "','$contextlocation','$mod','".urlencode($targetUrl)."',true,'width=800,height=600','".$_sess."');";
                            }
                        }
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['total'][0]['amount'][0]['cdata'] = Currency(round($trn[$i]['total'], 2), 1, 1, $basecurr);
                        $agingAmt[$accountcounter]['data'][$i]['total'] = round($trn[$i]['total'], 2);

                        
                    }
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['namehref'][0]['cdata'] =
                        "javascript:ReportLaunch($cny,'$multientity','" . $rec['LOCATIONKEY'] .
                        "','$contextlocation','$moduleName','".urlencode($script)."',true,'width=800,height=600','".$_sess."');";
                }
                    else if(!$recidused) {
                        // remove entity id from $tmpGrpBy
                        $tmpGrp = $entid.'#~#'.$rec['ENTITY'];
                        $grpkey  = array_search($tmpGrp, $tmpGrpBy);
                        unset($tmpGrpBy[$grpkey ]);
                    }
                    
                    if($rec['ENTITY'] != $prrecords[$i+1]['ENTITY']) {
                        $recidused = false;
                    }
            }


            if ( $showzeros == 'yes'){
                foreach($allAccountsTypeMap as $typekey => $typeval){
                    if($typeval['TYPE'] == $key ){
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['vid'][0]['cdata'] =  substr($typekey, 1);
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['vname'][0]['cdata'] = $typeval['NAME'];
                        for ($cl=0;$cl<$agecolscnt;$cl++) {
                            $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][0]['P'.$cl] = '0.00';
                        }
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['trxamount'][0]['cdata'] = '0.00';
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['total'][0]['amount'][0]['cdata'] = '0.00';
                        $script = "editor.phtml?.r=".substr($typekey, 1)."&.op=".$op."&.isapopup=1&.popup=1";
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['namehref'][0]['cdata'] =
                            "javascript:ReportLaunch($cny,'$multientity','','$contextlocation','$moduleName','".urlencode($script)."',true,'width=800,height=600','".$_sess."');";
                        unset($allAccountsTypeMap[$typekey]);
                    }
                    $i++;
                }
            }





           //$reportdata['reportcontent'][0]['acctid'][$accountcounter])['data'][$i]['vid'][0]['cdata'] = 'abc';
//            eppp_p($reportdata);
//            dieFL();

            // in summary mode for the entity if the total is 0.0 then unset the entity
            // coz we dont want to show the entity who nets to zero 
            // and user doesnt want to see net to zero entity
            $doUnset = $showzeros != 'yes' && $tottrn['total'] == 0.0;
            if ($showretainageonly) {
                // however, if showing retainage-only invoices, only do the unset if retainage is also zero
                $doUnset = $doUnset && ($tottrn['retainage_bal_total'] == 0);
            }
            if ($doUnset) {
                unset($reportdata['reportcontent'][0]["acctid"][$accountcounter]);
            }

            if($this->_isGrpBySumm) {
                foreach($reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'] as $k => $val2) {
                    for ($cl=0;$cl<$agecolscnt;$cl++) {
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$k]['period'][0]['P'.$cl] = Currency($val2['period'][0]['P'.$cl], 1, 1, $basecurr);
                    }
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$k]['total'][0]['amount'][0]['cdata'] = Currency($val2['total'][0]['amount'][0]['cdata'], 1, 1);
                    if ( $showzeros != 'yes' && $val2['total'][0]['amount'][0]['cdata'] == 0.0 ) {
                        for ($cl=0;$cl<$agecolscnt;$cl++) {
                            $tottrn[$agecols[$cl]] =  $tottrn[$agecols[$cl]] - $val2['period'][0]['P'.$cl];
                        }
                        unset($reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$k]);
                    }
                }
                //If the cdata is 0 we do not need to include the whole $accountcounter from the reportdata,
                //bcoz it adds an emty data() which creates a error while converting the array to XML
                if(empty($reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'])){
                    unset($reportdata['reportcontent'][0]["acctid"][$accountcounter]['data']);
                }
            }

            // in detail mode even if total due is zero and customer has bills/adjustments (non-zero due) then we should display it
            if($showzeros != 'yes' && $hasNonZero) {
                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['id']=$this->replaceSpecialChars($entid);
                $aatKeys[$entid] = $entname;
                if($this->GetOutputType() ==  kShowPDF) {
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['name']=InsertSpaces($entname, 20);
                }else{
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['name']=$this->replaceSpecialChars($entname);
                }
            }
            // only include the total if it was non-zero, unless they asked for zeros!
            $doNonZeroProcessing = $tottrn['total']<>0.0 || $showzeros=='yes';
            if ($showretainageonly) {
                // also include it if retainage is non-zero
                $doNonZeroProcessing = $doNonZeroProcessing || ($tottrn['retainage_bal_total'] != 0);
            }
            if ($doNonZeroProcessing) {
                $aatKeys[$entid] = $entname;
                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['id']=$this->replaceSpecialChars($entid);
                if($this->GetOutputType() ==  kShowPDF) {
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['name']=InsertSpaces($entname, 20);
                }else{
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['name']=$this->replaceSpecialChars($entname);
                }
                for ($cl=0;$cl<$agecolscnt;$cl++) {
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['total'][$i]['period'][0]['P'.$cl] = Currency(round($tottrn[$agecols[$cl]], 2), 1, 1, $basecurr);
                    
                    if ($this->params['isgraph']=='1') {
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['totalgraph'][0]['period'][$cl]['forgraph'] = $tottrn[$agecols[$cl]];
                    }
                    
                    // Caculating total for each aging column for showzero and summary report
                    /** @noinspection PhpUndefinedVariableInspection */
                    $grandtotal[$agecols[$cl]] = ibcadd($grandtotal[$agecols[$cl]], $tottrn[$agecols[$cl]], 14, 1);

                    // if acct doesn't have value we should inject 0s, else it will not be dispalyed in summary
                    if($tottrn['total']==0.0 && $_sumdet != 'Detail') {
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['period'][0]['P'.$cl] = '0.00';
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['total'][0]['amount'][0]['cdata'] = '0.00';
                        $script = "editor.phtml?.r=".$Id."&.op=".$op."&.isapopup=1&.popup=1";
                        $reportdata['reportcontent'][0]["acctid"][$accountcounter]['data'][$i]['namehref'][0]['cdata'] =
                            "javascript:ReportLaunch($cny,'$multientity','','$contextlocation','$moduleName','".urlencode($script)."',true,'width=800,height=600','".$_sess."');";
                    }

                }
                //$reportdata['reportcontent'][0]["acctid"][$accountcounter]['total'][$i]['total'][0]['amount'] = $tottrn['total'];
                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['total'][$i]['total'][0]['amount'][0]['cdata'] = Currency(round($tottrn['total'], 2), 1, 1, $basecurr);
                $reportdata['reportcontent'][0]["acctid"][$accountcounter]['totalgraph'][0]['forgraph'] = $tottrn['total'];

                if ( $this->getIsRetainageEnabled() ) {
                    $reportdata['reportcontent'][0]["acctid"][$accountcounter]['total'][$i]['total'][0]['retainage_bal_total'][0]['cdata'] =
                        Currency(round($tottrn['retainage_bal_total'], 2), 1, 1, $basecurr);
                }
                
            }
            $accountcounter++;
        }
        unset($tmpGrpBy);
        unset($agingAmt);

        $asOffDateColSpan = $asOffDateColSpan + $agecolscnt;
        $asOffDateColSpan = $this->getAsOffDateColSpan($reportdata, $asOffDateColSpan);
        $reportdata['reportheader'][0]['asOffDateColSpan'][0]['cdata'] = $asOffDateColSpan;
        // always include the grand total, even if it is zero
        for ($cl=0;$cl<$agecolscnt;$cl++) {
            //handling special case for showzero and summary
            if( $showzeros=='no' && $_sumdet == 'Summary') {
                if ($this->params['isgraph']=='1') {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $reportdata['reportcontent'][0]["grandtotal"][0]['period'][$cl]['forgraph'] = $grandtotal[$agecols[$cl]];
                }

                /** @noinspection PhpUndefinedVariableInspection */
                $reportdata['reportcontent'][0]["grandtotal"][0]['period'][0]['P' . $cl] = Currency(round($grandtotal[$agecols[$cl]], 2), 1, 1, $basecurr);
            }else{
                if ($this->params['isgraph']=='1') {                    
                    $reportdata['reportcontent'][0]["grandtotal"][0]['data'][0]['period'][$cl]['forgraph'] = $gentot[$agecols[$cl]];
                }

                /** @noinspection PhpUndefinedVariableInspection */
                $reportdata['reportcontent'][0]["grandtotal"][0]['period'][0]['P' . $cl] = Currency(round($grandtotal[$agecols[$cl]], 2), 1, 1, $basecurr);
            }
        }        

        $reportdata['reportcontent'][0]["grandtotal"][0]['total'] = Currency(round($gentot['total'], 2), 1, 1, $basecurr);

        if ( $this->getIsRetainageEnabled() ) {
            $reportdata['reportcontent'][0]["grandtotal"][0]['retainage_bal_total'] = Currency(round($gentot['retainage_bal_total'], 2), 1, 1, $basecurr);
        }

        // remember this so we can go thru the data when we make our graph
        $this->params['accountcounter']=$accountcounter;

        $reportCount = is_array($reportdata['reportcontent'][0]["acctid"]) ? count($reportdata['reportcontent'][0]["acctid"]) : 0;
        if ( $showzeros != 'yes' && $_sumdet != 'Detail' && $reportCount == 0) {
            //xml generation has problems if we don't pass the data in this format
            $reportdata['reportcontent'][0]["acctid"][0] = array();
        }

        $this->map = $reportdata;
        //StartTimer('Sorting Aging period Map');
        if(  $_sortby == 'AMOUNT' || $_sortby == 'AMOUNT_DESC') {
            
            $this->sortMap($this->map, $_sortby);
        }
        //StopTimer('Sorting Aging period Map');

        //  If AAT is on, we need to track the customer/vendors artificially added to the results.
        if (AdvAuditTracking::isCnyTracked() && ($_type == 'customer' || $_type == 'vendor')) {
            $entity = ($_type == 'customer') ? 'CUSTOMER' : 'VENDOR';
            $keys = [];
            foreach ($aatKeys as $k => $v) {
                $keys[$entity][] = $k;
            }
            AdvAuditTracking::registerAccess($keys, AuditTrail::OPID_REPORT);
        }
        return true;

    }

    /**
     * @param array  $prrecordsByEntity
     * @param array  $acctorder
     * @param string $sortby
     *
     * @return bool
     */
    function ReOrderPrrecordsByEntity(&$prrecordsByEntity,
        /** @noinspection PhpUnusedParameterInspection */ &$acctorder, $sortby)
    {
        $tmprecs = array();

        if ($sortby == 'VENDORID' || $sortby == 'EMPLOYEEID' || $sortby == 'CUSTOMERID') {
            $sortMethod = 'sortByEntity';
        } else if ($sortby == 'NAME') {
            $sortMethod = 'sortByName';
        } else {
            $sortMethod = 'sortByLastname';
        }

        foreach ( $prrecordsByEntity as $key => $val ) {
            usort($val, array(&$this, $sortMethod));
            $tmprecs[$key] = $val;
            unset($prrecordsByEntity[$key]);
        }

        $prrecordsByEntity = $tmprecs;
        unset($tmprecs);

        return true;
    }

    /**
     * @param array $rec1
     * @param array $rec2
     *
     * @return int
     */
    function sortByEntity($rec1, $rec2)
    {
        $comp = strcmp($rec1['ENTITY'], $rec2['ENTITY']);
        if ($comp == 0) {
            //for stable ordering
            return ($rec1['ORDERINDEX'] < $rec2['ORDERINDEX']) ? -1 : 1;
        }
        return $comp;
    }

    /**
     * @param array $rec1
     * @param array $rec2
     *
     * @return int
     */
    function sortByName($rec1, $rec2)
    {
        $compName = isl_strcasecmp($rec1['NAME'], $rec2['NAME']);
        if ($compName == 0) {
            //check if they belong to the same entity
            $compEntity = strcmp($rec1['ENTITY'], $rec2['ENTITY']);
            if ($compEntity == 0) {
                //for stable ordering
                return ($rec1['ORDERINDEX'] < $rec2['ORDERINDEX']) ? -1 : 1;
            }
            return $compEntity;
        }
        return $compName;
    }

    /**
     * @param array $rec1
     * @param array $rec2
     *
     * @return int
     */
    function sortByLastname($rec1, $rec2)
    {
        $compName = isl_strcasecmp($rec1['LASTNAME'], $rec2['LASTNAME']);
        if ($compName == 0) {
            //check if they belong to the same entity
            $compEntity = strcmp($rec1['ENTITY'], $rec2['ENTITY']);
            if ($compEntity == 0) {
                //for stable ordering
                return ($rec1['ORDERINDEX'] < $rec2['ORDERINDEX']) ? -1 : 1;
            }
            return $compEntity;
        }
        return $compName;
    }

    /**
     * @param array  $map
     * @param string $_sortby
     *
     * @return bool
     */
    function sortMap(&$map, $_sortby)
    {
        $tempEntNameArray = array();
        $tempEachEntKeyValuearray = array();
        $tempSortedEntArray = array();

        foreach  ( $map['reportcontent'][0]['acctid'] as $val) {
            
            $tempEntNameArray[$val['id']] = $val['totalgraph'][0]['forgraph'];    
            
            $tempEachEntKeyValuearray[$val['id']] = $val; 
            
        }    
        
        if($_sortby == 'AMOUNT') {
            asort($tempEntNameArray);
        }else {
            arsort($tempEntNameArray);
        }

        foreach ( $tempEntNameArray as $key => $val ) {
            $tempSortedEntArray[] = $tempEachEntKeyValuearray[$key];
        }
        
        $map['reportcontent'][0]['acctid'] = $tempSortedEntArray;
        
        return true;
    }

    /**
     * @param string $dataxml
     * @param string $intacctxml
     *
     * @return bool
     */
    function CreateIntacctXML(&$dataxml, &$intacctxml)
    {
        $intacctxml = $dataxml;
        return true;
    }

    /**
     * @param string $val
     * @param int    $numofchars
     *
     * @return string
     */
    function InsertSpaces($val, $numofchars)
    {
        $output = '';
        $charcounter = 0;
        for ($i=0;$i<isl_strlen($val);$i++){
            $output .= isl_substr($val, $i, 1);
            $charcounter++;
            // insert a space every numofchars characters
            // this allows the pdf to break it at the space.
            if ($charcounter==$numofchars) {
                $charcounter = 0;
                $output .= " ";
            }
        }
        return $output;
    }

    /**
     * @return string
     */
    function IgnoreRecordTypes()
    {
        $rectypes = $this->ignorerecordtypes;
        $countrecordtypes = count($rectypes);
        for ($i=0;$i<$countrecordtypes;$i++){
            $ignore[] = "recordtype != '" . $rectypes[$i] . "'"; 
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $ignorestring = \implode(' and ', $ignore);
        return $ignorestring;
    }

    /**
     * @param int    $due
     * @param string $rectype
     *
     * @return int
     */
    function FigureTotal($due, $rectype)
    {
        if (in_array($rectype, $this->flipsignrecordtypes)) {
            $thistotaldue = -$due;                            
        } else {
            $thistotaldue = $due;                            
        }
        return $thistotaldue;
    }

    /**
     * @return bool
     */
    function _validateParams() 
    {

        global $gErr;
        $datemode = $this->params['datemode'];

        if ($datemode=='Single' && isset($this->params['agereportdate'])) {
            // Memorized report come with mm/dd/yyyy format
            // We need to reset to user date format before compare.
            if ( $this->params['OFFLINEREPORTS'] || $this->_showmemorized ) {
                $this->params['agereportdate'] = ReformatDate($this->params['agereportdate'], IADATE_STDFORMAT, GetUserDateFormat());
            }
            if ( !ValidateInputDate($this->params['agereportdate']) ) {
                $gErr->addIAError("SL-0237", __FILE__ . __LINE__,
                    "Selected Date '" . $this->params['agereportdate'] . "' is not valid",
                    ['PARAMS_AGEREPORTDATE' => $this->params['agereportdate']]
                );
                return false;
            } else {
                $agereportdate = FormatDateForStorage($this->params['agereportdate']);
            }
        } else {
            $agereportdate = GetCurrentDate();
        }

        $this->params['agereportdate'] = $agereportdate;

        if (!$this->params['isgraph'] && $this->atlas) {
            if ($this->params['modulekey'] == '11.CM' && $this->params['REPORTINGCURRENCY'] == '') {
                $gErr->addError("SL-1184", __FILE__ . ':' . __LINE__, "Reporting Currency is required");
                return false;
            }
            if ($this->params['REPORTINGCURRENCY'] == '' && $this->params['CURRENCY'] == '') {
                $msg = "Either Reporting Currency or Transaction Currency is required";
                $errorCode = "SL-1185";
                // there is no 'Reporting Currency' for Employee Aging
                if ( ($this->params['modulekey'] == '6.EE')) {
                    $msg = "Transaction Currency is required";
                    $errorCode = "SL-1186";
                }
                $gErr->addError($errorCode, __FILE__ . ':' . __LINE__, $msg);
                return false;
            }
            
            if($this->params['REVALUATIONTYPE'] == '') {
                $gErr->addError("SL-1187", __FILE__ . ':' . __LINE__, "Convert Currency from is a required");
                return false;
            }
            if ($this->params['REPORTINGCURRENCY'] != '') {
                $currMgr = Globals::$g->gManagerFactory->getManager('trxcurrencies');
                if (!$currMgr->IsValidCurrency($this->params['REPORTINGCURRENCY'])) {
                    $gErr->addError("SL-1188", __FILE__ . ':' . __LINE__, "Reporting Currency is not a predefined transaction currency.");
                    return false;
                }
            }
        }

        $_fromid = isl_trim($this->params['from_id']);
        $_toid = isl_trim($this->params['to_id']);
        if ((!empty($_fromid) && empty($_toid)) || (empty($_fromid) && !empty($_toid))) {
            //$gErr->addError();
            return false;
        }

        return true;

    }

    /**
    * Returns output format
     *
     * @return string
    */
    function GetOutputType() 
    {
        if($this->params['offline_mode']) { 
            $outputtype = $this->params['offreporttype'];
        } else {
            $outputtype = $this->params['type'];
        }
        return $outputtype;
    }

    /**
     * @return string
     */
    function GetCurrencies()
    {
        if(isset($this->params['REPORTINGCURRENCY']) &&  $this->params['REPORTINGCURRENCY'] != '') {
            $basecurr = $this->params['REPORTINGCURRENCY'];
        }else if ($this->atlas && $this->params['isgraph']) {
            [$loc] = explode('--', $this->params['LOCATION'][0]);
            $basecurr = GetLocationBaseCurrency($loc);
        } else {
            $basecurr = GetBaseCurrency();
        }
        return $basecurr;
    }

    /**
     * this function accepts the type of the object and returns the type information array
     *
     * @param string $_type the type of the array
     *
     * @return array $typeInfo the type information array :: format array()
     */
    function getTypeInfo($_type)
    {
        if(empty($this->typeInfo)) {
            $this->typeInfo = array();
            if ($_type == 'vendor') {
                $this->typeInfo['idcol'] = 'vendorid';
                $this->typeInfo['namecol'] = 'b.name';
                $this->typeInfo['shortType'] = 'V';
            } else if ($_type == 'employee') {
                $this->typeInfo['idcol'] = 'employeeid';
                $this->typeInfo['namecol'] = 'b.title, c.firstname, c.lastname';
                $this->typeInfo['shortType'] = 'E';
            } else {
                $this->typeInfo['idcol'] = 'customerid';
                $this->typeInfo['namecol'] = 'b.name';
                $this->typeInfo['shortType'] = 'C';
            }
        }
        return $this->typeInfo;
    }

    /**
     * @param array $reportdata
     * @param int   $asOffDateColSpan
     *
     * @return int
     */
    function getAsOffDateColSpan($reportdata, $asOffDateColSpan)
    {

        $groupby = $reportdata['reportheader'][0]['grpby'][0]['cdata'];
        $invdue = $reportdata['reportheader'][0]['invdue'][0]['cdata'];
        $hideduedate = $reportdata['reportheader'][0]['vendcust_orig'][0]['cdata'];
        $_sumdet = $reportdata['reportheader'][0]['sumdet'][0]['cdata'];

        if ($groupby != 'None') {
            $asOffDateColSpan = $asOffDateColSpan + 1;
        }
        if ($groupby != 'CUSTOMERID' and $groupby != 'VENDORID' and $groupby != 'EMPLOYEEID' ) {
            $asOffDateColSpan = $asOffDateColSpan + 1;
        }
        if ($groupby != 'NAME') {
            $asOffDateColSpan = $asOffDateColSpan + 1;
        }
        if( $_sumdet == 'Detail' ) {
            if ( $invdue == 'GL Posting' or $groupby != 'GLPOSTDATE') {
                $asOffDateColSpan = $asOffDateColSpan + 1;
            }
            if ($hideduedate == 'n') {
                $asOffDateColSpan = $asOffDateColSpan + 1;
            }
        }

        return $asOffDateColSpan;
    }

    /** @var array $reportdata  */
    var $reportdata;

    /** @var string[] $ignorerecordtypes  */
    var $ignorerecordtypes;

    /** @var string[] $flipsignrecordtypes  */
    var $flipsignrecordtypes;

    /**
     * Overridden to pass new XSL file name
     * 
     * @param string $usethisxml XSL file name
     * 
     * @return string
     */
    public function GetSingleStageXSLFile($usethisxml)
    {
        if($this->isUsingNewExcelFormat() && $this->GetOutputType() === kShowExcel) {
            return $this->_report .'_newxls.xsl';
        }

        return parent::GetSingleStageXSLFile($usethisxml);
    }

    /**
     * Build the location filter
     *
     * @param string[] $location
     * @param string   $filterOption
     * @param string   $prRecStr
     *
     * @return string
     */
    function BuildLocationInFilter($location, $filterOption, $prRecStr = '')
    {
        $filterInLocation = '';

        $cny = GetMyCompany();

        if ($location != '') {
            if ($filterOption == 'CHILDREN') {
                $locationQuery = "SELECT DISTINCT RECORD# R FROM locationmst START WITH PARENTKEY IN (" .
                                 implode(', ', $location) . ") AND CNY#=$cny CONNECT BY PRIOR RECORD#=PARENTKEY AND CNY#=$cny";
            } else if ($filterOption == 'SELF') {
                $locationQuery = null;
            } else {
                // All locations
                $locationQuery = "SELECT DISTINCT RECORD# R FROM locationmst START WITH RECORD# IN (" .
                                 implode(', ', $location) . ") AND CNY#=$cny CONNECT BY PRIOR RECORD#=PARENTKEY AND CNY#=$cny";
            }

            if ($locationQuery ) {
                $res = QueryResult($locationQuery);
                $location = [];
                foreach ( $res as $row ) {
                    $location[] = $row['R'];
                }
            }

            $filterInLocation = '';
            $filterInLocation = PrepINClauseStmt($filterInLocation, $location, $prRecStr.'location#', false, 'ageLocations');
        }
        return $filterInLocation;
    }

    private function buildRangeFilter(string $column, string $start, string $end, array &$filter): void
    {
        if ($start === '' || $end === '') {
            return;
        }
        $this->_useEntityFilter = true;
        if ($this->_type === 'employee') {
            $filter[] = "$column >= '$start' AND $column <= '$end'";
        } else {
            $filter[] = ($start === $end)
                ? [$column, '=', $start]
                : [$column, 'BETWEEN', [$start, $end]];
        }
    }

    protected function prepareEntityFilters(string $column, array &$filter): bool
    {
        $ok = true;
        $_multipleGrpType = $this->params['MULTIPLEFILTERGRPTYPE'] ?? 'Range';

        if ($_multipleGrpType === 'Multi'){
            $_multipleFilter = $this->params['MULTIPLEFILTER'] ?? null;
            $multiEntityLines = array_filter(explode("#~#", $_multipleFilter));
            $idFilterChunks = [];

            foreach ($multiEntityLines as $line) {
                [$from, $to] = array_map('trim', explode("->", $line));
                $this->buildRangeFilter($column, $from, $to, $idFilterChunks);
            }

            if (!empty($idFilterChunks)) {
                $filter[] = [
                    'operator' => 'OR',
                    'filters' => $idFilterChunks
                ];
            }
        } else if ($_multipleGrpType === 'Group') {
            $ok = $this->prepareEntityGroupFilter($filter);
        } else {
            $_fromid = isl_trim($this->params['from_id']);
            $_toid = isl_trim($this->params['to_id']);
            $this->buildRangeFilter($column, $_fromid, $_toid, $filter);
        }

        return $ok;
    }

    private function getEntityInformation(
        string $_sortby, string $territoryStr, array $entityFilters, array &$accounts
    ): bool
    {
        $typeInfoArr = $this->getTypeInfo($this->_type);
        $idcol = $typeInfoArr['idcol'];
        $sortBy = in_array($_sortby, self::ENTITY_SORT_FIELDS) ? $_sortby : '';
        $_multipleGrpType = $this->params['MULTIPLEFILTERGRPTYPE'] ?? 'Range';
        $idFilter = [];

        $ok = $this->prepareEntityFilters($idcol, $idFilter);
        if (!$ok) {
            return $ok;
        }

        if ($this->_type === 'employee') {
            $territoryStr .= implode(' and ', $idFilter);
            $accounts = GetEmployeesFull($territoryStr, $sortBy);
            return $ok;
        }

        /** @var CustomerManager|VendorManager $entityMgr */
        $entityMgr = Globals::$g->gManagerFactory->getManager($this->_type);
        $querySpec = [
            'selects' => $this->getEntitySelects(),
            'fetchall' => true,
        ];

        if ($_multipleGrpType === 'Range' && isset($this->params['INCLUDECHILDENTITY'])
            && $this->params['INCLUDECHILDENTITY'] === 'true') {
            if (!empty($idFilter)) {
                $querySpec['recursive'] = ['start' => $idFilter];
            }
            $filters = $entityFilters;
        } else {
            $filters = array_merge($entityFilters, $idFilter);
        }

        if (!empty($filters)) {
            $querySpec['filters'] = [$filters];
        }

        if (!empty($sortBy)) {
            $querySpec['orders'] = [[$sortBy]];
        }
        // set the aging report flag as true;
        $entityMgr->setFromAgingReport(true);
        $querySpec['isCaseInsensitive'] = true;
        $accounts = $entityMgr->GetList($querySpec);
        //reset the flag
        $entityMgr->setFromAgingReport(false);

        return $ok;
    }

    protected function getEntitySelects(): array
    {
        if ($this->_type === 'vendor') {
            return [
                'RECORDNO', 'VENDORID', 'NAME', 'ENTITY', 'VENDTYPEKEY'
            ];
        }
        return [
            'RECORDNO', 'CUSTOMERID', 'NAME', 'ENTITY', 'CUSTTYPEKEY'
        ];
    }

    private function getObjectTypeDetails(string &$typeforid, string &$typeforkey): array
    {
        $objType = $this->_groupmap[$this->params['object']]['DB'][$this->params['GROUPBY']];
        $objectTypes = [];
        // Define type mappings
        $typeMappings = [
            'vendor' => ['typeFor' => 'vendtype', 'typeforid' => 'VENDORID', 'typeforkey' => 'VENDTYPEKEY'],
            'customer' => ['typeFor' => 'custtype', 'typeforid' => 'CUSTOMERID', 'typeforkey' => 'CUSTTYPEKEY'],
            'employee' => ['typeFor' => 'employeetype', 'typeforid' => 'EMPLOYEEID', 'typeforkey' => 'EMPTYPEKEY'],
        ];
        // Fetch all the vend/cust/emp types at once and use it as required
        if(in_array($objType, $this->_pushTypeKeys) && isset($typeMappings[$this->_type])) {
            $typeMapping = $typeMappings[$this->_type];
            $typeforid = $typeMapping['typeforid'];
            $typeforkey = $typeMapping['typeforkey'];
            $typeFor = $typeMapping['typeFor'];

            $typeMgr = Globals::$g->gManagerFactory->getManager($typeFor);
            $rs = $typeMgr->GetList();
            foreach( $rs as $val) {
                $objectTypes[$val['RECORDNO']] = $val['NAME'];
            }
            // Release $rs and $typeMgr as it is no more required
            unset($rs);
            unset($typeMgr);
        }
        return $objectTypes;
    }

    protected function prepareEntityGroupFilter(array &$filters): bool
    {
        return true;
    }
}
