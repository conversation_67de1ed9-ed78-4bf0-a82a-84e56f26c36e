<?

//=============================================================================
//
//	FILE:		APSelectToPay.cls
//	AUTHOR:		Ashok <PERSON>
//	DESCRIPTION:	Contains classes for executing bill payments. 
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================	


Class APSelectToPay extends SelectToPay
{
/**
 * @var array $totalentcredits
 */
var $totalentcredits;

/**
 * @var array $negcrs
 */
var $negcrs;

/**
 * @var bool $atlas
 */
var $atlas;

/**
 * @var string|float $totalappliedcredit
 */
var $totalappliedcredit;

/**
 * @var string|float $totaldiscount
 */
var $totaldiscount;

/**
 * @param string[] $arguments
 */
function __construct($arguments)
{
    $this->params = array(

        'ap' => array(
            'recordtype'    => '\'pi\',\'pa\'',
            'checkterm'        => 1,
            'pagetitle'        => _('Select Bills to Pay'),
            'doctype' => 'Bill',
            'entitytype' => 'vendor',
            'fields'    => array(
                array(
                    'header' => 'Select',
                    'headerfunc' => 'PrintSelectRowHeader',
                    'function' => 'PrintSelectRow',
                    'valign' => 'middle',
                ),
                array(
                    'header'    => '',
                    'valign'    => 'middle',
                    'function'    => 'PrintSupDoc',
                    'width'        => '5%',
                ),
                array(
                    'header'    => _('Bill #'),
                    'valign'    => 'top',
                    'function'  => 'PrintRecordID',
                    'width' => '8%',
                ),
                array(
                    'header'    => _('Vendor'),
                    'disableedit'    => true,
                    'valign'    => 'top',
                    'function'  => 'PrintVendor',
                    'width' => '15%',
                ),
                array(
                    'header'    => 'Currency',
                    'function'  => 'PrintCurrency',
                    'valign'    => 'top',
                    'align'        => 'left',
                    'width'     => '5%',
                ),
                array(
                    'header' => "Due date",
                    'align' => 'left',
                    'valign' => 'top',
                    'function' => 'PrintWhenDue',
                    'width' => "10%",
                ),
                array(
                    'header' => 'Amt due',
                    'function' => 'PrintAmountDue',
                    'align' => 'right',
                    'total' => 'PrintTotalDue',
                    'grandtotal' => 'PrintTotalAmount',
                    'valign' => 'top',
                    'width' => "10%",
                ),
                array(
                    'header' => 'Amt to pay&nbsp;',
                    'function' => 'PrintSelectedAmount',
                    'align' => 'right',
                    'total' => 'PrintTotalSelected',
                    'valign' => 'top',
                    'width'  => '10%',
                    'blank' => '5%',
                ),
                array(
                    'header' => 'Balance',
                    'function' => 'PrintBalance',
                    'align' => 'right',
                    'valign' => 'top',
                    'width' => "10%",
                ),
                array(
                    'header' => 'Credit<br/>available',
                    'function' => 'PrintTotalAvailable',
                    'align' => 'right',
                    'valign' => 'top',
                    'width' => '8%',
                    'blank' => '1%',
                ),
                array(
                    'header' => 'Credit to apply',
                    'function' => 'PrintCreditToApply',
                    'align' => 'left',
                    'valign' => 'top',
                    'width' => '10%',
                ),
                array(
                    'header' => '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',
                    'function' => 'PrintMoreFields',
                    'valign' => 'top',
                    'width' => '5%',
                ),

            )
        ),
    );

    parent::__construct($arguments);
}



    /**
     * @param array $uniqueEntities
     * @param bool $translate
     * @param string $parentPayment
     * @param string $currency
     *
     * @return array|bool
     */
    function GetEntityCreditsMap($uniqueEntities, $translate = true, $parentPayment = '', $currency = '')
    {

        global $kAPid;

        //$rType = 'pp';
        /*
        if ($translate) {
        if ($startentity) {
        $startentity = GetEntityValue($rType, $startentity);
        }
        if ($endentity) {
        $endentity = GetEntityValue($rType, $endentity);
        }
        }
        */

        // 
        // get the target entity and their locations which have the relationship with the source entity
        // if the source entity is enabled for advnce IE Relationship we need to filter the Bill entries based on the relationships defined
        $trgentlocList = $this->GetTargetEntityLocations();

        //$result = GetEntityCredits($startentity, $endentity, $kAPid, $parentPayment, $currency);
        // $result = GetEntityCreditsEx($startentity, $endentity, $kAPid, $parentPayment, '', true, $currency);
        if ($parentPayment) {
            
            if (!$uniqueEntities[0]) { 
                global $gErr;
                $gErr->addError(
                    "AP-0084", __FILE__ . __LINE__,
                    "No entity selected".
                    "entity required"
                );
                return false;
            }
            $result = GetPaymentEntityCredits($kAPid, $uniqueEntities[0], $parentPayment, $currency, $trgentlocList);
        } else if(count($trgentlocList)>0) {
            //$result = FindEntityCredits($startentity, $endentity, $kAPid, $currency);
            /** @noinspection PhpUndefinedVariableInspection */
            $result = FindEntityCredits($uniqueEntities, $modID, $currency, $trgentlocList);
        } else {
            //$result = FindEntityCredits($startentity, $endentity, $kAPid, $currency);
            /** @noinspection PhpUndefinedVariableInspection */
            $result = FindEntityCredits($uniqueEntities, $modID, $currency, $this->_locationlist);
        }

        $this->_entityCredits = $result;

        // $itemPymtMgr = Globals::$g->gManagerFactory->getManager('itempayment');

        /**
 * Prepare the map 
**/
        $crKeyMap = array();
        foreach( $result as $creditFound) {
            $crKeyMap[] = $creditFound['RECORD#'];
        }
        /**
 * Prepare the map ends 
**/
        
        if (count($crKeyMap) > 0) {
            /**
 * Get the credits item payments and map it 
**/
            $qry = " SELECT distinct dpe.cny#, 
							dpe.line_no as record#, 
							prrec.record# as crreckey,
							prrec.recordtype, 
							dpe.accountkey, 
							gl.acct_no, 
							gl.title as acct_title, 
							dpe.accountlabelkey, 
							aclabel.label, 
							dpe.dept#, 
							d.dept_no, 
							d.title as dept_title, 
							dpe.location#, 
							l.location_no as loc_no, 
							l.name as loc_title, 
							dpe.amount, 
							dpe.totalselected, 
							dpe.totalpaid, 
							sum((pre.amount - nvl(pre.amountretained, 0) - pre.totalpaid)) as totaldue, 
							dpe.currency, 
							dpe.trx_amount, 
							dpe.trx_totalselected, 
							dpe.trx_totalpaid, 
							sum((pre.trx_amount - nvl(pre.trx_amountretained, 0) - pre.trx_totalpaid)) as trx_totaldue, 
							dpe.exch_rate_type_id, 
							dpe.exchange_rate, 
							dpe.lineitem, 
							dpe.allocationkey, 
							max(pre.recordkey)||'--'||dpe.line_no as recnum, 
							decode(dpe.allocationkey,0,'Custom',alloc.allocationid) as allocation 
					FROM	dispprentry dpe, 
							prentry pre, 
							prrecordmst prrec, 
							glaccount gl, 
							accountlabel aclabel, 
							department d, 
							locationmst l, 
							allocation alloc						
					WHERE	dpe.cny# = pre.cny# 
							and dpe.recordkey = pre.recordkey 
							and dpe.line_no = pre.line_no 
							and dpe.cny# = prrec.cny# 
							and dpe.recordkey = prrec.record# 
							and dpe.cny# = gl.cny# 
							and dpe.accountkey = gl.record# 
							and dpe.cny# = aclabel.cny# (+) 
							and dpe.accountlabelkey = aclabel.record# (+) 
							and dpe.cny# = d.cny# (+) 
							and dpe.dept# = d.record# (+) 
							and dpe.cny# = l.cny# (+) 
							and dpe.location# = l.record# (+) 
							and dpe.cny# = alloc.cny# (+) 
							and dpe.allocationkey = alloc.record# (+) 						
							and pre.trx_totalselected = 0 
							AND (case when (prrec.recordtype not in ('pr', 'rr', 'er'))
									  then (pre.amount - pre.totalpaid) 
									  else 0 end) <= 0
							and pre.trx_amount != pre.trx_totalpaid
							and pre.lineitem = 'T'
							and dpe.cny# = :1 ";

            $qry = PrepINClauseStmt($qry, $crKeyMap, " AND dpe.recordkey ");

            // if pay source entity is selected
            // filter the prentries for the entities & their locations which have IE ralation with the source entity
            if(count($trgentlocList)>0) {
                $qry = PrepINClauseStmt($qry, $trgentlocList, " and pre.location# ", false);
            } else if(countArray($this->_locationlist)>0) {
                $qry = PrepINClauseStmt($qry, $this->_locationlist, " and pre.location# ", false);
            }

            $qry = $qry." GROUP BY dpe.cny#, dpe.line_no, 
							prrec.recordtype, 
							prrec.record#,
							dpe.accountkey, 
							gl.acct_no, 
							gl.title, 
							dpe.accountlabelkey, 
							aclabel.label, 
							dpe.dept#,
							d.dept_no, 
							d.title, 
							dpe.location#, 
							l.location_no,
							l.name, 
							dpe.amount, 
							dpe.totalselected, 
							dpe.totalpaid, 
							dpe.currency, 
							dpe.trx_amount, 
							dpe.trx_totalselected, 
							dpe.trx_totalpaid, 
							dpe.exch_rate_type_id, 
							dpe.exchange_rate, 
							dpe.lineitem, 
							dpe.allocationkey, 
							alloc.allocationid 
					ORDER BY dpe.line_no ";
            $stmt = array($qry, GetMyCompany());

            $creditresult = QueryResult($stmt);

        }
            

        /**
 * Restructure map by pr recordkey index 
**/
        $creditsByRecordKeyMap = array();
        /** @noinspection PhpUndefinedVariableInspection */
        foreach ( $creditresult as $creditline) {
            $creditsByRecordKeyMap[$creditline['CRRECKEY']][] = $creditline;
        }
        //eppp_p($creditsByRecordKeyMap);
        //dieFL();

        foreach($result as $creditFound) {
            $entity = $creditFound['ENTITY'];
            $crKey = $creditFound['RECORD#'];

            $this->totalentcredits[$entity] = bcadd($this->totalentcredits[$entity], ibcabs($creditFound['TOTALDUE']));

            //			$crAvail = abs($creditFound['TOTALDUE']);
            //			if($creditFound['RECORDTYPE'] == 'pi' && $creditFound['DOCTOTALDUE'] != 0) {
            //				$this->negcrs[$entity] = bcadd($this->negcrs[$entity], abs($creditFound['TOTALDUE']));
            //				$crAvail = 0;
            //			}
            
            //$crAvail = in_array($creditFound['RECORDTYPE'], array('pr', 'rr', 'er')) ? abs($creditFound['TOTALDUE']) : abs($creditFound['NEGTOTALDUE']);

            if($creditFound['RECORDTYPE'] == 'pi') {
                $crAvail = 0;
                if($creditFound['DOCTOTALDUE'] < 0) {
                    $this->negcrs[$entity] = bcadd($this->negcrs[$entity], ibcabs($creditFound['DOCTOTALDUE']));
                }
            }
            else {
                $crAvail = ibcabs($creditFound['TOTALDUE']);
            }
            //$crAvail = abs($creditFound['TOTALDUE']);

            // $negCredits = !in_array($creditFound['RECORDTYPE'], array( 'pr', 'rr'));
            //$creditDetail = $itemPymtMgr->GetDisplayItemsForPayment($crKey, "", "", "", $parentPayment, false, $negCredits);

            $creditDetail = $this->GetDisplayItemsForPayment($crKey, $creditsByRecordKeyMap[$crKey]);
            
            foreach ($creditDetail as $crItem) {
                // due on a credit line is totaldue minus the totalselected
                $due = bcsub(ibcabs($crItem['TOTALDUE']), ibcabs($crItem['TOTALSELECTED']));
                // SPLIT/allocations changes: _creditLineItems array structure
                // changed from [entity][prentry.record#] to 
                // [entity][prentry.recordkey--prentry.line_no]
                $crLineArr = $this->_creditLineItems[$entity][$crItem['RECNUM']];
                $crLineAmt = (isset($crLineArr) && $crLineArr != '')?$crLineArr:0;
                $this->_creditLineItems[$entity][$crItem['RECNUM']] = bcadd($crLineAmt, $due);
            }

            /** @noinspection PhpUndefinedVariableInspection */
            $credits[$entity] = bcadd($credits[$entity], $crAvail);
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $credits;
    }


    /**
     * @param int $crKey
     * @param array $itemPymts
     *
     * @return array
     */
    function GetDisplayItemsForPayment($crKey, $itemPymts)
    {
        global $kAPid;
        
        GetModulePreferences($kAPid, $modPref);
        $isLabel = ( $modPref['LABEL'] == 'false' ? false : true );

        $creditDetail = array();

        $itemPymtsCount = countArray($itemPymts);
        for($i = 0; $i < $itemPymtsCount ; $i++) {
            $ipRec = $itemPymts[$i];            
            

            if ($ipRec['ACCOUNTLABELKEY'] && $isLabel) {
                $accountDisplay = $ipRec['LABEL'];
            } else {
                $accountDisplay = $ipRec['ACCT_NO'] . PICK_RECVAL_SEP . $ipRec['ACCT_TITLE'];
            }

            $deptDisplay = ($ipRec['DEPT#']) ? ($ipRec['DEPT_NO'] . PICK_RECVAL_SEP . $ipRec['DEPT_TITLE']) : "";
            $locDisplay = ($ipRec['LOCATION#']) ? ($ipRec['LOC_NO'] . PICK_RECVAL_SEP . $ipRec['LOC_TITLE']) : "";
            /** @noinspection PhpUndefinedVariableInspection */
            $creditDetail[] = array(
                'RECORDNO'        => $ipRec['RECORD#'],
                'ACCOUNT'        => $accountDisplay,
                'AMOUNT'        => ($ipRec['TRX_AMOUNT'] ?: $ipRec['AMOUNT']),
                'DEPARTMENT'    => $deptDisplay,
                'LOCATION'        => $locDisplay,
                'TOTALDUE'        => ($ipRec['TRX_TOTALDUE'] ?: $ipRec['TOTALDUE']),
                'TOTALSELECTED'    => ($ipRec['TRX_TOTALSELECTED'] ?: $ipRec['TOTALSELECTED']),
                'PAIDAMOUNT'    => $amountDisplay ?: '0.00',
                'DISCOUNTS'        => $discountDisplay ?: '0.00',
                'CREDITS'        => $creditDisplay ?: '0.00',
                'ALLOCATIONKEY'    => $ipRec['ALLOCATIONKEY'],
                'RECNUM'        => $ipRec['RECNUM'],
                'ALLOCATION'    => $ipRec['ALLOCATION'],
                'CURRENCY'        => $ipRec['CURRENCY'],
            );
        }

        return $creditDetail;
    }




/**
 * @param string $_default_paydate
 * @param string $_defbank
 * @param string $paymentkey
 * @param string $_ccSelector
 * @param string $_paymentmethodfilter
 * @param string $_selectBillsFor
 *
 * @return bool
 */
function PrintTopHeader($_default_paydate, $_defbank,
                        $paymentkey = '', $_ccSelector = '', $_paymentmethodfilter = '', $_selectBillsFor = ''
    ) {
        
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;

        $_action = Request::$r->_action;
        $_r = Request::$r->_r;
        $_paysourceentity = Request::$r->_paysourceentity;
        $balance = 0.0;

        $this->atlas = (IsMCMESubscribed() && !GetContextLocation());

        if (!$this->_paymentoption) {
            $this->_paymentoption = "vendorpref";
        }

        // get the payment request record
        if ( isset($paymentkey) && $paymentkey != '' ) {
            if (!GetPRRecord($paymentkey, $payrecord) ) {
                return false;
            }
        }
        $setCurrChangeFlag = '';
        if ($this->mcenabled) {
            $setCurrChangeFlag = "setCurrChangeFlag();";
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $indexKey = ( $this->_paymentoption == 'bill') ? $payrecord['RECORD#'] : $payrecord['VENDENTITY'];

        // GET THE BANK SELECTOR
        if (($this->_paymethod != 'Credit Card') &&  $this->_paymethod != OutsourcedCheckUtils::OUTSOURCE_CC_METHOD){

            $finAcctLabel = 'Bank';
            if ($this->mcenabled) {
                if ($this->_r != '') {
                    // run the Init() to set up the bank filter by payment method
                    $this->Init();
                    $this->bankcurr = GetBankAccountCurrency($payrecord['FINANCIALENTITY']);
                    $this->bankcurrency = ($this->bankcurr ?: $this->basecurr);

                    if ($this->basecurr != '' && $this->bankcurrency == $this->basecurr) {
                        $currclause = "and (CASE WHEN bankaccount.currency IS NULL THEN '".$this->basecurr."' ELSE bankaccount.currency END)= '".$this->basecurr."'";
                    } elseif ($this->bankcurrency != '') {
                        $currclause = "and bankaccount.currency = '".$this->bankcurrency."'";
                    }
                } elseif ($this->bankcurr != '') {
                    $currclause = " and bankaccount.currency = '".$this->bankcurr."' ";
                } elseif ($this->_basebankonly == 'Y') {
                    $currclause = " and (CASE WHEN bankaccount.currency IS NULL THEN '".$this->basecurr."' ELSE bankaccount.currency END)= '".$this->basecurr."' ";
                } elseif ($this->basecurr != '' && $this->currency == $this->basecurr) {
                    $currclause = " and bankaccount.currency <> '".$this->basecurr."'";
                } elseif ($this->basecurr != '' && $this->currency != '' && $this->currency != $this->basecurr) {
                    $currclause = " and bankaccount.currency = '".$this->currency."'";
                } elseif (!$this->basecurr) {
                    $currclause = " and bankaccount.location# = location.record# and bankaccount.currency = location.currency ";
                }
            }

            if ($this->_paymethod == 'WF Check') {
                $this->BankAccountFilter .= " and bankaccount.forwfpm = 'T' and bankaccount.pmchecktemplateid is not null ";
            }
            if ($this->_paymethod == 'WF USD Wire') {
                $this->BankAccountFilter .= " and bankaccount.forwfpm = 'T' ";
            }
            if ($this->_paymethod == 'WF Domestic ACH') {
                $this->BankAccountFilter .= " and bankaccount.forwfpm = 'T' and bankaccount.achcompanyid is not null ";
            }
            if ($this->_paymethod == 'ACH') {
                $this->BankAccountFilter .= " and achbankkey is not null ";
                $currclause = " and (CASE WHEN bankaccount.currency IS NULL THEN '".$this->basecurr."' ELSE bankaccount.currency END)= 'USD' ";
            }
            if ($this->_paymethod == 'Printed Check') {
                $this->BankAccountFilter .= " and bankaccount.print_on != 'D' ";
            }
            if ($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD) {
                $this->BankAccountFilter .= "and exists (select 1 from opsubscription where cny# = bankaccount.cny# and bankaccountid = bankaccount.accountid and outsourcecheck = 'T') ";
            }
            if ($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) {
                $this->BankAccountFilter .= "and exists (select 1 from opsubscription where cny# = bankaccount.cny# and bankaccountid = bankaccount.accountid and outsourceach = 'T') ";
            }

            if ($this->atlas) {
                $select = 'bankaccount.accountid, bankaccount.accountno, bankaccount.name, bankaccount.check_currno, bankaccount.currency, bankaccount.company_entry_description';
                /** @noinspection PhpUndefinedVariableInspection */
                $filter = " bankaccount.status = 'T' $this->BankAccountFilter $currclause ";
                $groupby = "bankaccount.accountid, bankaccount.name";
            } else {
                $select = 'bankaccount.accountid, bankaccount.accountno, bankaccount.name, bankaccount.check_currno, bankaccount.currency, bankaccount.company_entry_description';
                /** @noinspection PhpUndefinedVariableInspection */
                $filter = " bankaccount.status = 'T' $this->BankAccountFilter $currclause ";
                $groupby = " bankaccount.accountid, bankaccount.name";
            }

            // add source entity filter
            $this->FilterBankAccountsBySourceEntity($_paysourceentity, $filter);
            
            // added order by field ... 'accountid, name'. This
            // will enhance visibility of information displayed in
            // the bank dropdown control.

            $accts = GetBankAccounts($select, $filter, $groupby);
            
            if ( $accts == '' || count($accts) < 1 ) {
                if ($this->_paymethod == 'ACH') {
                    $msg = "There are no bank accounts that have ACH configuration setup. ";
                        
                    $gErr->addError(
                        "AP-0166",
                        __FILE__ . __LINE__,
                        "ACH Setup missing in bank accounts", $msg, 'Go to Cash Management >> Checking Accounts >> ACH File Setup tab >> configure ACH Bank and save'
                    );

                } else if ($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD) {
                    $msg = "There are no active bank accounts for " . OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD .
                        " payment method. Either subscribe to at least one bank account for " . OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD . " service or wait for ".
                        "activation notification when the bank account becomes active.";
                    $gErr->addIAError("AP-0167", __FILE__ . __LINE__, $msg, ['PAYMENTMETHOD' => OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD]);
                }  else if ($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) {
                    $msg = "There are no active bank accounts for " . OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD .
                        " payment method. Either subscribe to at least one bank account for " . OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD . " service or wait for ".
                        "activation notification when the bank account becomes active.";
                    $gErr->addIAError("AP-0167", __FILE__ . __LINE__, $msg, ['PAYMENTMETHOD' => OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD]);

                } else {
                    $errorCode = 'AP-0306';
                    $placeHolder = [];
                    $msg = "Either no bank accounts have been set up for this company or all existing bank accounts are currently inactive. ".
                    " If the Default Payment Method does not match the bank account type, change it by going to ".
                    "Company > Services > Subscriptions > Configure";
                    if($this->mcenabled && $this->bankcurr){
                        $msg .= " for currency '$this->bankcurr'";
                        $errorCode = $this->atlas ? 'AP-0311' : 'AP-0307';
                        $placeHolder = ['BANKCURR' => $this->bankcurr];
                    }
                    else{
                       if ($this->atlas) {
                           $errorCode = 'AP-0308';
                       }
                    }
                    $msg .= ".";
                    if ($this->atlas) {
                        $msg .= " For multiple base currencies company, we can only use the bank accounts ".
                        "having the same currency as its belonging location.";
                    }
                     $gErr->addIAError($errorCode, __FILE__ . __LINE__, $msg, $placeHolder);
                     //i18N todo - (Code Change Review)
                }
                PrintErrorMessages();
                return false;
            }
        }



        // Commenting code Bug#12460
        /*
        // VALIDATE CC CONSTRAINTS.
        if ( $this->_paymethod == 'Online' && !IsModuleConfigured('26.OBP') ) {
        $gErr->addError("AP-0171",__FILE__ . __LINE__,
        "Please enroll for Intacct's Online Bill ".
        "Payment service to proceed.");
        PrintErrorMessages();
        return false;
        }
        */
        if ( $this->_paymethod == 'Credit Card' || $this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_CC_METHOD ) {

            if ( !$this->CCMap ) {
                $gErr->addIAError(
                    "AP-0172",
                    __FILE__.":".__LINE__,
                    "You must have at least one credit ".
                    "card set up for paying $this->labels ".
                    "to pay by this method.",
                    ['LABELS' => $this->labels]
                );
                PrintErrorMessages();
                return false;
            }

            $finAcctLabel = _('Charge Card');
            if (!$_ccSelector) {
                $_ccSelector = $payrecord['FINANCIALENTITY'];
            }
        }

        //If service is AMEXACH then try getting the maximum transaction limit for 14 day rolling period and
        //Also the payment processed till now in this period
        $amexACHPaymentLimit = null;
        $amexACHRemainingBalance = null;
        if($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD){
            $amexAchPaymentInfo = OutsourcedCheckUtils::getAmexACHRemainingPaymentLimits();
            $amexACHPaymentLimit = $amexAchPaymentInfo['MAX_PAYMENT_LIMIT'];
            $amexACHRemainingBalance = $amexAchPaymentInfo['REMAINING_BALANCE'];
        }

        if ( isset($paymentkey) && $paymentkey != '' ) {

            if (!$_defbank) {
                // _defbank has values already in edit mode
                // when user changes the bank
                $_defbank = $payrecord['FINANCIALENTITY'];
            }

            $this->_paymethod = $payrecord['PAYMETHODKEY'];
            $this->state = $payrecord['STATE'];

            // This code is for getting the right nomenclature
            // for the state of the payment.
            if ($this->_paymethod == 'Printed Check' || $this->_paymethod == JOINT_CHECK) {
                $workflow = 'printedcheck';
            } else if ($this->_paymethod == 'Online') {
                $workflow = 'onlinepayment';
            } else {
                $workflow = 'appayment';
            }

            // GROOM THE HISTORY
            $histobj = $gManagerFactory->getManager('prrecordhistory');
            $histobj->SetWorkflow($workflow);

            // now that we have the workflow, set the state from
            // the nomenclature
            $state = $histobj->GetNomenclatureState(
                $payrecord['STATE']
            );

            // make JS function call stmts for the invoices
            // affected by this payment request
            $onchange = '';
            $invoices = ( $this->_posDueMap ?: array() );
            $invKeys = array_keys($invoices);
            $index = 0;
            foreach ($invKeys as $invKey) {
                $posAmt = ($this->_posDueMap[$invKey]
                ?: 0);
                $negAmt = ($this->_negDueMap[$invKey]
                ?: 0);
                $onchange .= "SetDiscountDate(this,
					'$invKey', '$posAmt', '$negAmt', '$index');";
                $index++;
            }

        }

        $tableTitle = '';

        if ( IsMultiEntityCompany() && GetContextLocation()) {
            $tableTitle = GetContextLocationName();
        }

        if (!$this->bankcurr) {
            $_defaultfinacctid = GetMyDefaultFinAcctID($this->_modID);
            $_defbank = ( $_defbank == '' ? $_defaultfinacctid . '~' : $_defbank );
            $arr = explode('~', $_defbank);
            $this->bankcurr = ($arr[4] ?: $this->basecurr);
        }

        ?>
     <table width="100%" cellspacing=0 cellpadding=0><tr><td align=left>
     <table width="100%" cellspacing=0 cellpadding=0>
        <? if ($tableTitle != '') { ?>
		<tr class="multiline_header_bg">
			<td colspan=10><b>&nbsp;<?=$tableTitle?>&nbsp;</b></td>
		</tr>
    <? 
} ?>
     <TR class=multiline_bg_beige>
     <td class=fs><a href='#Skip' id="topheaderctl" CLASS="baseIcon" iconState="3" onclick="toggleTopHeaderTbl();"></a>Payment options
     </td>
     </TR>
     <tr class="multiline_bg_white">
     <td><div id='topheadertbl'>
      <table width="100%" cellpadding="0" cellspacing="0" border="0" class="field_list_data_old">

      <tr>
      <td width=1>&nbsp;</td>
      <td>Pay method</td>
      <td>
				<?= /** @noinspection PhpUndefinedVariableInspection */
                $finAcctLabel;?>
      </td>
        <? if ( !isset($paymentkey) || $paymentkey == '' ) { ?>
			<td>
				Payment request method
			</td>
            <? if ( ($this->_paymethod == 'ACH') || ($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) ) { ?>
                <td> Payment description </td>
            <? } ?>
			<td>
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						
                        <? if ( ($this->_paymethod == 'ACH') || ($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) ) {  ?>
              	            <td> Estimated payment date </td>
                        <? } else { ?>
              	            <td> Set payment date to </td>
                        <? } ?>
                            <td class="value_cell" valign="middle">
                                <div id="warning.default_paydate" style="visibility:hidden; display:none; color:red; background-color:#FFFFCC; border-style=solid; border-width=1px; border-color=#999999"></div>
                            </td>
                    </tr>
				</table>				
			</td>
            <? if ($this->mcenabled) { ?>
                <td>
                    <?php echo _('Bill currency'); ?>
                </td>
            <? } ?>
            <? if ( $this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD ) { ?>
                <td> 14-day revolving limit </td>
                <td> Amount still available </td>
            <? } ?>
        <?} else { ?>
			<td>
				Status
			</td>
			<td>
				<?= isl_ucfirst($this->params['entitytype']); ?>
			</td>
			<td>
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<? if ( ($payrecord['PAYMETHODKEY'] == 'ACH') || ($payrecord['PAYMETHODKEY'] == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) ) {  ?>
							<td>Estimated payment date</td>
        <?
} else {?>
							<td>Payment date</td>
        <?
}?>

						<td class="value_cell" valign="middle">
							<div id="warning.paydate" style="visibility:hidden; display:none; color:red; background-color:#FFFFCC; border-style=solid; border-width=1px; border-color=#999999"></div>
						</td>
					</tr>
				</table>
			</td>
    <? if ($payrecord['PAYMETHODKEY'] != 'Printed Check' && $payrecord['PAYMETHODKEY'] != JOINT_CHECK) { ?>
                <? if ($payrecord['PAYMETHODKEY'] != 'Online') {?>
				<? if ($payrecord['PAYMETHODKEY'] != 'Check Delivery') {?>
				<td>
				Document number
				</td>
			<? } ?>
            <? } ?>
			<? if ( ($payrecord['PAYMETHODKEY'] == 'ACH') || ($payrecord['PAYMETHODKEY'] == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) ) {  ?>
				<td>Payment description</td>
    <?
} else if ($payrecord['PAYMETHODKEY'] != 'Check Delivery') {?>
				<td >Memo</td>
    <?
}?>
			
    <? 
} if ($this->mcenabled && $payrecord['TRX_TOTALSELECTED'] != $payrecord['TOTALSELECTED']) { ?>
				<td>
					Amount to pay
				</td>
				<td>
					Base amount to pay
				</td>
    <? 
} else { ?>
				<td>
                       Amount to pay
				</td>
    <? 
} 
} if ($this->mcenabled && isset($paymentkey) && $paymentkey != '') { ?>
			<td>
				Currency
			</td>
    <? if ($this->mcenabled && $this->currency != $this->basecurr) {
        if ($payrecord['ITEMS'][0]['EXCH_RATE_TYPE_ID'] != '' && $_action != 'view') { ?>
       <td>
        Exchange rate type
       </td>
        <? 
        } else { ?>
           <td>
            Exchange rate
           </td>
            <? 
        } ?>
    <? 
} 
} 
elseif ($this->mcenabled && $this->currency == $this->basecurr && $this->_basebankonly != 'Y' && $_action != 'view') { ?>
			<td>Exchange rate type</td>
    <? 
} ?>
        <? if ($this->_paymethod == 'WF Domestic ACH' || $this->_paymethod == 'WF USD Wire') { ?>
				<td>Next payment process date</td>
    <? 
} ?>
        <? if ($this->_paymethod == 'WF Check') { ?>
				<td>Check Delivery options</td>
               	<td>Next submission date</td>
    <? 
} ?>
      </tr>
      <tr>
      <td width=1>&nbsp;</td>
      <td>
        <? if ($_r || $_action == 'view') { ?>
				<b><?=$this->_paymethod?>&nbsp;</b>
				<input type=hidden name=".paymethod" value="<?=$this->_paymethod?>">
    <? 
} else {
                $foreignbank = false;
                $foreignbills = false;
    if ($this->mcenabled) {
        $foreignbills = ($this->currency != $this->basecurr);
        $foreignbank = ($this->_basebankonly != 'Y');
    }
                $onchangepm = "document.lo.elements['.changedpaymethod'].value = 1; $setCurrChangeFlag processBeforeSubmit();";

                //Here is where we are making second screen's payment method readonly if some payment method is selected in first screen other than '-- SELECT --' (For AP)
                //if (!isset($_paymentmethodfilter) || $_paymentmethodfilter == '-- SELECT --'){
    if ($_paymentmethodfilter == '-- SELECT --' || $_paymentmethodfilter == '') {
        PayMethodSelector(
        $this->_paymethod,
            $onchangepm, $foreignbank,
            $foreignbills, $this->bankcurr, false, $_selectBillsFor, $this->currency, $this->_basebankonly
        );
    }else { ?>
					<b><?=$this->_paymethod?>&nbsp;</b>
						
				<? 
    }
} ?>
      </td>
			
      <td>
        <?
        /** @noinspection PhpUndefinedVariableInspection */
        ( $this->_paymethod == 'Credit Card' || $this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_CC_METHOD) ?  GetCCSelector($this->CCMap, $_ccSelector, "processBeforeSubmit()") : GetBankControl($accts, $_defbank, ($this->atlas ? "processBeforeSubmit()" : ''), true, $balance, $this->bankcurr, $this->_paymethod);
        ?>
      </td>
        <?
        if ( $_action != 'view' && ($this->_paymethod == 'Credit Card' || $this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_CC_METHOD)
            && count($this->CCMap) 
        ) {
            GetFinAcctDeptLocs(
                $this->CCMap,
                $entDeptLocMap, true
            );
            foreach($this->CCMap as $ccID) { 
                $vendDefs[] =
                $entDeptLocMap[$ccID]['VENDOR'];
                $vendCreditLimitDefs[] = $entDeptLocMap[$ccID]['CREDITLIMIT'];
                $vendTotalDueDefs[] = $entDeptLocMap[$ccID]['TOTALDUE'];
                $vendOnHoldDefs[] = $entDeptLocMap[$ccID]['ONHOLD'];
            }
        ?>
        <script>
        var tocheck = 'appayment';
        var creditlimitcheck = '<? echo GetCreditLimitCheck(); ?>';
        var onholdcheck = '<? echo GetOnHoldCheck(); ?>';
        var crlimit = '';
        var entitydue = '';
        var cardObj = document.lo.elements['.ccSelector'];
        var cardids = new Array("<? echo join("\", \"", $this->CCMap) ?>");
        var vends = new Array('<? /** @noinspection PhpUndefinedVariableInspection */echo join("', '", $vendDefs) ?>');
        var crlimits = new Array('<? /** @noinspection PhpUndefinedVariableInspection */echo join("', '", $vendCreditLimitDefs) ?>');
        var totaldues = new Array('<? /** @noinspection PhpUndefinedVariableInspection */echo join("', '", $vendTotalDueDefs) ?>');
        var vendonholds = new Array('<? /** @noinspection PhpUndefinedVariableInspection */echo join("', '", $vendOnHoldDefs) ?>');
        </script>
        <script src="../resources/js/apar.js"></script>
        <? 
        } ?>

         <? if ( !isset($paymentkey) || $paymentkey == '' ) { ?>
			<td>
				<?if($_selectBillsFor != '' && $_selectBillsFor == 'JP') {?>
					<input type=hidden name=".paymentoption" value="bill"><B>One per <?=$this->params['doctype'];?></B>
				<? }else {
                    $selectpervendor = ($this->_paymentoption == 'vendor') ? 'selected' : '';
                    $selectperbill = ($this->_paymentoption == 'bill') ? 'selected' : '';
                ?>
					<select name=".paymentoption" onchange="<?=$this->_warnOnSaveOnChange?>">
					     <option value="vendorpref"><?php echo _("Use vendor preference"); ?></option>
					     <option value="vendor" <?=$selectpervendor;?>><?php echo _("Merge requests into one per vendor"); ?></option>
					     <option value="bill" <?=$selectperbill;?>><?php echo _("Generate one request per bill"); ?></option>
					</select>
			</td>
                 <? }?>
				<? if ( ($this->_paymethod == 'ACH') || ($this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) ) {
                 /** @noinspection PhpUndefinedVariableInspection */
                 foreach( $accts as $achAcct){
                            if($achAcct['ACCOUNTID'] == rtrim($_defbank, "~")){
                                $_paymentdesc = $achAcct['COMPANY_ENTRY_DESCRIPTION'];
                            }
                        }
                 /** @noinspection PhpUndefinedVariableInspection */
                 if (!$_paymentdesc) {
                            $_paymentdesc = $accts[0]['COMPANY_ENTRY_DESCRIPTION'];
                        }
                ?>
					<td><input type=text name=".paymentdesc" value="<?=$_paymentdesc;?>" ></td>
				<? } ?>
                                     
    <? } else { ?>
			<td>
				<b><?= /** @noinspection PhpUndefinedVariableInspection */
                    $state?></b>
			</td>
    <? } ?>

        <? // NOW LETS PREPARE THE DEFAULT PAYEMNT DATE, ONLY FOR SELECTTOPAY NOT FOR PAYMENTREQUEST
        if ( !isset($paymentkey) || $paymentkey == '' ) {
            ?>
          <td>
           <script>var tempDateState;</SCRIPT>
           <input type="hidden" name="default_paydate_state" value="<?=$_default_paydate;?>" >
								<?
                                   $_params = array();    
                                   $_params["varname"] = ".default_paydate";
                                   $_params["size"] = "12";
                                   $_params["maxlength"] = "12";
                                   $_params["class"] = "amtclass";
                                   $_params["onchange"] = "if(!ValidateDocDate(this)){return false};".$this->_warnOnSaveOnChange.";PopulateDefaultPayDate(this);WarnGLPostingDate('.default_paydate', '', 'CUSTOM');this.form.default_paydate_state.value=this.value;";
                                   $_params["value"] = ($_default_paydate ?: GetCurrentDate());
                                   //$_params["href"] = 'javascript:tempDateState = document.lo.elements[".default_paydate"].value;';
                                   $ctrl = new DateControl($_params);
                                   $ctrl->Show();
                                ?>
          </td>
            <? if ($this->mcenabled) { ?>
                <td>
                    <? if ($_action == 'view') { ?>
                        <b><? echo $this->currency; ?></b>
                    <?
                    } else if ($this->_paymethod ==  'ACH') {
                        $this-> currency = 'USD' ;
                        $this->changecurr = 'USD'; ?>
                        <input type="text" name=".currency"  class=noborder value="<?=$this->changecurr?>" >
                    <? } else if ($this->_paymethod ==  OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) {
                        $this-> currency = 'USD' ;
                        $this->changecurr = 'USD'; ?>
                        <input type="text" name=".currency"  class=noborder value="<?=$this->changecurr?>" >
                    <? } else if ($this->_paymethod ==  'Check Delivery') {
                        $this-> currency = 'USD' ;
                        $this->changecurr = 'USD'; ?>
                        <input type="text" name=".currency"  class=noborder value="<?=$this->changecurr?>" >
                    <? } else { ?>
                        <script src="../resources/js/ComboBox.js"></script>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td width="1%">
                                <?
                                if (!$this->currency) {
                                    $tmp = explode('~', $_defbank);
                                    $this->currency = $tmp[4];
                                }
                                $nocurrselected = false;
                                if (Request::$r->_nocurrselected == 'true') {
                                    $nocurrselected = true;
                                }

                                $params = array();
                                $params['varname'] = ".currency";

                                $params['path'] = "_currency";
                                $params['value'] = $nocurrselected ? '' : $this->currency;
                                $params['type']['size'] = 3;
                                /** @noinspection PhpUndefinedVariableInspection */
                                $params['onchange'] = $gWarnOnSaveJS . "document.lo.elements['.changecurr'].value = 1; $setCurrChangeFlag processBeforeSubmit();";

                                $rendercmbo = PrintEntityCombo('trxcurrencies', $params);

                                if (!$rendercmbo) { ?>
                                    <input type="text" name=".currency" onchange="<?=$gWarnOnSaveJS?>" value="<?=$nocurrselected ? '' : $this->currency?>" size=3 maxlength=3>
                                <? } ?>
                                <input type="hidden" name=".changecurr" value="">
                                <input type="hidden" name=".nocurrselected" value="">
                                </td>
                            </tr>
                        </table>
                        <?  } ?>
                </td>
            <? } ?>

            <? if ( $this->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD ) { ?>
                <td><input type=text class=noborder name=".amexAchPaymentLimit" value="<?=$amexACHPaymentLimit;?>" ></td>
                <td><input type=text class=noborder name=".amexACHRemainingBalance" value="<?=$amexACHRemainingBalance;?>" ></td>
            <? } ?>

            <? if ($this->mcenabled && $this->currency != '' && $this->basecurr != '' && $this->currency == $this->basecurr && $this->_basebankonly != 'Y' && $_action != 'view') { ?>
                <td>
                        <?
                        $exchMgr = $gManagerFactory->getManager('exchangeratetypes');
                        $rtype = $exchMgr->GetDefaultExchangeRateType();
                        $params = array();
                        $params['varname'] = ".paybaseRateType";
                        $params['value'] = ($rtype[0]['NAME'] ?: 'Intacct Daily Rate');
                        $params['type']['size'] = 18;
                        $params['pick_url'] = 'picker.phtml?.hidecustom=1';
                        $params['hidecustom'] = true;
                        // $params['onchange'] = $gWarnOnSaveJS;

                        $rendercmbo = PrintEntityCombo('exchangeratetypesall', $params);

                        if (!$rendercmbo) { ?>
                        <input onchange="<?=$params['onchange']?>" type=text name=".paybaseRateType" value="<?=$params['value']?>" size=18 maxlength=60>
                        <?
                        } ?>
                </td>
            <? } ?>
        <? } ?>
        <? if ( isset($paymentkey) && $paymentkey != '' ) { ?>
						<td class="value_cell">
							<b><?=$payrecord['ENTITY']?></b>
							<input type="hidden" name=".screentype" value="apbill">
						</td>
						<td class="value_cell">
        <? if ($_action == 'view') { ?>
                <b>
                    <? echo FormatDateForDisplay($payrecord['WHENDUE']); ?>
                </b>
        <? } else {
            $_params = array();
            $_params["varname"] = ".paydate";
            $_params["value"] = $payrecord['WHENDUE'];
            $_params["size"] = "12";
            $_params["maxlength"] = "12";
            /** @noinspection PhpUndefinedVariableInspection */
            $_params["onchange"] = $onchange . "WarnGLPostingDate('.paydate', '', 'CUSTOM');";
            $ctrl = new DateControl($_params);
            $ctrl->Show();
        } ?>
        </td>
        <? if ($payrecord['PAYMETHODKEY'] != 'Printed Check' && $payrecord['PAYMETHODKEY'] != JOINT_CHECK) { ?>
                        <? if ($payrecord['PAYMETHODKEY'] != 'Check Delivery') {?>
        <? if ($payrecord['PAYMETHODKEY'] != 'Online') {?>
						<td class="value_cell">
        <? if ($_action == 'view') {
            echo "<b>".$payrecord['DOCNUMBER']."</b>";
} else { ?>
							<input type=text name=".docnumbers[<?=$indexKey?>]" value="<?=$payrecord['DOCNUMBER']?>">
        <? 
} ?>
						</td>
        <? 
} ?>
						<td class="value_cell">
						<? if ($_action == 'view') {
							echo "<b>".$payrecord['DESCRIPTION']."</b>";
						} else {
                            $fldName = ".descs[".$indexKey."]";
                            if ( ($payrecord['PAYMETHODKEY'] == 'ACH') || ($payrecord['PAYMETHODKEY'] == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) ) {
                                $fldName = ".paymentdesc";
                            } ?>
                                <input type=text name="<?= $fldName; ?>" value="<?=$payrecord['DESCRIPTION']?>">
                        <? 
} ?> 
                          </td> 
                        <?
}
                        ?>
                    <? 
} ?>

                    <? if ($this->mcenabled && $payrecord['TRX_TOTALSELECTED'] != $payrecord['TOTALSELECTED']) { ?>
						<td class="value_cell">
							<input type="hidden" name=".paycurrency" value="<?=$payrecord['CURRENCY']?>">
							<input type="hidden" name=".basecurrency" value="<?=$payrecord['BASECURR']?>">
							<b id="trx_totalselected"><?=Currency($payrecord['TRX_TOTALSELECTED'], 0, 0, $payrecord['CURRENCY'])?></b>
							<input type="hidden" name=".totalselected" value="<?=$payrecord['TRX_TOTALSELECTED']?>">
						</td>
						<td class="value_cell">
							<b id="trx_bill_selected"><?=Currency($payrecord['TOTALSELECTED'], 0, 0, $payrecord['BASECURR'])?></b>
							<input type="hidden" name=".totalbaseselected" value="<?=$payrecord['TOTALSELECTED']?>">
							<input type="hidden" name=".int_exchange_rate" value="<?=$payrecord['ITEMS'][0]['EXCHANGE_RATE']?>">
						</td>
        <? 
} else { ?>
						<td class="value_cell"><b id="trx_bill_selected"><?=Currency($payrecord['TOTALSELECTED'], 0, 0, $payrecord['BASECURR'])?></b><input type="hidden" name=".totalbaseselected" value="<?=$payrecord['TOTALSELECTED']?>"></td>
        <? 
} ?>
        <? if ( $this->mcenabled ) { ?>
						<td class="value_cell"><b><?=$this->currency?></b></td>
        <? if ($this->currency != $this->basecurr) {
            if ($payrecord['ITEMS'][0]['EXCH_RATE_TYPE_ID'] != '' && $_action != 'view') { ?>
             <td class="value_cell"><b><?
                                    $exchMgr = $gManagerFactory->getManager('exchangeratetypes');
                                    $exchtypename = $exchMgr->GetExchangeRateTypeName($payrecord['ITEMS'][0]['EXCH_RATE_TYPE_ID']);
                                    echo $exchtypename;
                                ?>
								<input type="hidden" name=".paybaseRateType" value="<?=$exchtypename?>"/>
								</b></td>
            <? 
            } else { ?>
                    <td class="value_cell"><b id=".int_exchange_rate"><?=(bccomp($payrecord['ITEMS'][0]['EXCHANGE_RATE'], '1') == 0 ? '1.0000' : $payrecord['ITEMS'][0]['EXCHANGE_RATE'])?></b></td>
                    <? 
            } ?>
        <? 
} ?>
        <? 
} ?>
				<? 
} ?>

        <? if ($this->_paymethod == 'WF Domestic ACH' || $this->_paymethod == 'WF USD Wire') { ?>
					<td><font size=1><b><?=GetSubmissionDeadline($this->_paymethod);?></b></font></td>
				<? 
} ?>
        <? if ($this->_paymethod == 'WF Check') { ?>
					<td><? $this->PrintCheckDeliveryOptions($payrecord); ?></td>
					<td><font size=1><b><?=GetSubmissionDeadline($this->_paymethod);?></b></font></td>
				<? 
} ?>
       </tr>
      </table>
     </div>
     </td>
     </tr>
     </table></div></td>
     </tr>
     <tr><td height="3px"></td></tr>
     </table>
        <?
        //$cbalance is used only to compare with $this->totaldue
        $cbalance = isl_substr($balance, 4);
        $cbalance = str_replace(',', '', $cbalance);
        $cbc = isl_substr($balance, 0, 3);
        $bal = isl_substr($balance, 4);
        if(!empty($curr)){
            $origbal = $cbc.' '.glFormatCurrency($bal);
        }
        $colorBlack = IALayoutManager::prefersCSS() ? '' : 'color="black"';
        
        if((!isset($this->_r) || $this->_r == '')) { ?>
         <table width="100%"  cellspacing=0 cellpadding=0>
         <tr>
         <td valign="top" align=left>
         <table border=0 height="20" width="100%" cellspacing=0 cellpadding=0>
          <tr class=ctrl_bg>
           <td align="left" height="5px" valign="middle" colspan="11" rowspan=2 nowrap>
            <?php echo _('On selected bills:'); ?>
            <input class="ac" type="Button" value="Apply credits" onclick="javascript:execGroupAction('applycredits');"/> 
            <input class="pf" type="Button" value="Pay full" onclick="javascript:execGroupAction('payfull');"/>
            <input class="cac" type="Button" value="Clear applied credits" onclick="javascript:execGroupAction('clearcredits');"/>
            <input class="cap" type="Button" value="Clear applied payments" onclick="javascript:execGroupAction('clearcash');"/>
           </td>
           <td>
            <? if ($_action != 'view') { 
                $mcpenabled = IsMCPSubscribed();
                /** @noinspection PhpUndefinedVariableInspection */
                if ( !$mcpenabled && $accts ) { ?>
                Uncommitted funds:&nbsp;
                <? 
                } 
} ?>
        </td>
        <td>
        <? if ($_action != 'view') { ?>
				<? /** @noinspection PhpUndefinedVariableInspection */
                if ( $accts && ( !$this->atlas || $_defbank != '~')) { ?>
				Bank balance:&nbsp;
    <? 
} ?>
				</td>
				<td>
				Total amount due:
				</td>
				<? 
} ?>
      <td id="hidescrolltd" align="right" height="5px">
       <a href='#Skip' id="hidescroll" onclick="hideScroll();">Unfreeze pane</a>
      </td>
      </tr>
      <tr class=ctrl_bg>
       <td>
        <? if ($_action != 'view') {
            /** @noinspection PhpUndefinedVariableInspection */
            if ( !$mcpenabled && $accts ) {
                $cbal = ibcsub($cbalance, ibcadd(($this->total ?: 0), ($this->totalappliedcredit ?: 0), 14, 1), 14, 1); ?>
            <font id='ucf' <? echo $colorBlack; ?>><?=$cbc.' '.glFormatCurrency($cbal);?></font>
            <? 
            } 
} ?>
       </td>
       <td>
        <? if ($_action != 'view') { ?>
        <? /** @noinspection PhpUndefinedVariableInspection */
        if ( $accts && ( !$this->atlas || $_defbank != '~')) { ?>
							<font id="balance" <? echo $colorBlack; ?>><?=$origbal?></font>
							<input type="hidden" id="balval" name="balval" value="<?=str_replace(',', '', isl_substr($balance, 4));?>">
        <? 
} 
if ($this->mcenabled) {
    list($paycurr) = explode(' ', $balance);
?>
<input type="hidden" name=".paycurr" value="<?=$paycurr?>">
        <?
} ?>
				</td>
				<td>
        <?=($this->_mod == 'ap' && $this->mcenabled ? $this->currency.'&nbsp;' : '')?><font id='totaltopay'><?=glFormatCurrency($this->totaldue);?></font>
				</td>
				<? 
} ?>
       <td>
       </td>
      </tr>
      </table>
      </td>
      </tr>
      <tr><td height="3px"></td></tr>
      </table>
        <? 
        } else { ?> 
           <table width="100%"  cellspacing=0 cellpadding=0>
           <tr>
           <td valign="top" align=left>
           <table border=0 height="20" width="100%" cellspacing=0 cellpadding=0>
           <tr class=ctrl_bg>
            <? 
            if ($_action != 'view') { ?>
                <? /** @noinspection PhpUndefinedVariableInspection */
                if ( $accts ) { ?>
			<td width="70%">&nbsp;
			</td>
			<td>
					Bank balance:&nbsp;
				<? 
} ?>
    </td>
    <td>
     Total amount due:
    </td>
    <? 
            } ?>
           <td id="hidescrolltd" align="right" height="5px">
				<a href=# id="hidescroll" onclick="hideScroll();">Unfreeze pane</a>
           </td>
           </tr>
    <? if ($_action != 'view') { ?>
			<tr class=ctrl_bg>
				<td width="70%">&nbsp;
				</td>
				<td>
        <? /** @noinspection PhpUndefinedVariableInspection */
        if ( $accts ) { ?>
							<font id="balval" <? echo $colorBlack; ?>><?=$cbc.' '.glFormatCurrency($cbalance)?></font><input type="hidden" id='balance' value="<?=$cbalance;?>" />
        <? 
} 
if ($this->mcenabled) {
    list($paycurr) = explode(' ', $balance);
?>
<input type="hidden" name=".paycurr" value="<?=$paycurr?>">
        <? 
} ?>
				</td>
				<td>
        <?=($this->_mod == 'ap' && $this->mcenabled ? $this->currency.'&nbsp;' : '')?><font id='totaltopay'><?=glFormatCurrency($this->totaldue);?></font>
				</td>
				<td>
				</td>
			</tr>
    <? 
} ?>
           <tr><td height="3px"></td></tr>
           </table>
    <? 
        }

        return true;
    }



    /**
     * @param array $table
     */
    function PrintTable($table)
    {
        $_action = Request::$r->_action;
    ?><input type=hidden name=dateformat value="<?=isl_htmlspecialchars($this->userdateformat)?>"><?
if ($this->_mod == 'ap' && !$this->mcenabled) {
    unset($this->params['fields'][4]);
    $this->params['fields'][2]['width'] = '10%';
    $this->params['fields'][3]['width'] = '18%';
}
else {
    $this->params['fields'][2]['width'] = '8%';
    $this->params['fields'][3]['width'] = '15%';
}
if($this->_mode == 'edit') {
    if($this->mcenabled) {
        $this->params['fields'][2]['width'] = '6%';
        $this->params['fields'][3]['width'] = '12%';
    }
    else {
        $this->params['fields'][2]['width'] = '20%';
        $this->params['fields'][3]['width'] = '18%';
    }
}
        else if($_action == 'view') {
    if($this->mcenabled) {
        $this->params['fields'][2]['width'] = '6%';
        $this->params['fields'][3]['width'] = '12%';
    }
    else {
        $this->params['fields'][2]['width'] = '8%';
        $this->params['fields'][3]['width'] = '15%';
    }
        }
        else {
            unset($this->params['fields'][1]);
        }
        $this->PrintHeader();
        $this->PrintColumns($table);
        $this->PrintTotals();
    }

    

    function PrintHeader()
    {
        $dict = Dictionary::getInstance();
        ?>
     <table id="headerid1" class="field_list_data_old" border=0 width=100% cellspacing=0 cellpadding=2>
     <tr class="multiline_header_bg1"><?
        foreach( $this->params['fields'] as $field){
            if ($field['disableedit'] && $this->_mode == 'edit') { continue; 
            }
            if ($this->_mode == 'edit' && $field['oneditfunc']) {
                $this->{$field['oneditfunc']}();
            } else if ($field['headerfunc']) {
                $this->{$field['headerfunc']}();
            } else {
                // $this->PrintHeaderCell($dict->GetRenamedText($field['header']), $field['rowspan'], $field['divx']);
                $this->PrintHeaderCell($dict->GetRenamedText($field['header']), $field['rowspan'], $field['headercolspan'], $field['align'], $field['valign'], $field['width'], $field['blank']);
            }
        }

        ?>
		
     </tr>
     </table>
        <?
    }



    function PrintSelectRowHeader() 
    {
        ?>
      <td ID="header:1" align="center" width="5%" valign="top">
       <font class=Result1>Select</font><br/>
        <? if(!isset($this->_r) || $this->_r == '' || $this->_mode == 'edit') {
            ?>
					<input name=selectrowhdr class=noborder type=checkbox onClick="javascript: selectAllRows('selectrow');" <?=($this->selectrowhdr ? 'CHECKED' : '')?>/>
				<? 
} ?>
       		</td>
        <?
    }



    /**
     * @param array $table
     */
    function PrintColumns($table)
    {

        // $this->totaldue = 0;
        $this->totalentered = 0;

        $val='';

        $trclass = "multiline_bg_beige";
        $rowIndex = 0;
        $visibilitycheck = (!isset($this->_r) || $this->_r == '') ? 1 : 0;
        foreach ($table as $prrecord) {
        /** @noinspection PhpUndefinedVariableInspection */
        $prrecord['appliedKeyMap'] = $this->appliedCreditMap[$prKey];?>
        <div id="addflds[<?=$rowIndex?>]" style="position:absolute;visibility:hidden;display:none;z-index:1;	
        height:100px;
        width:250px;" onkeydown="if(event.keyCode==27){ CloseMoreDiv(<?=$rowIndex?>, <?=$visibilitycheck?>); }">
         <table border=0 cellpadding="0" cellspacing="0"  bgcolor="#999999" class="field_list_data">
          <tr bgcolor="#DDD7C1">
           <td nowrap colspan=3>
           <table border="0" cellpadding="0" cellspacing="0" width="100%"> <tr> <td align="left">
            <b>&nbsp;<?=($prrecord['RECORDID'] ? 'More: '.util_encode($prrecord['RECORDID']) : 'More')?></b>
            </td>
            <td align="right" width="20%">
            <input style="height=18px;" class="nosavehistory" type="button" id="closebutton[<?=$rowIndex?>]" value="Done" onClick="CloseMoreDiv(<?=$rowIndex?>, <?=$visibilitycheck?>);" onkeydown="if(event.keyCode==9){ CloseMoreDiv(<?=$rowIndex?>, <?=$visibilitycheck?>); }">
            </td></tr></table>
           </td>
          </tr>
            <? if(!isset($this->_r) || $this->_r == '') { ?>
				<tr>
					<td class="label_cell" width="40%">Payment Date</td>
        <? $this->PrintScheduledDate($prrecord, true, $rowIndex) ?>
				</tr>
				<? 
} ?>
          <tr>
           <td class="label_cell" width="40%">Discount Cut-Off</td>
            <? $this->PrintCutoffDate($prrecord, 'colspan="2"') ?>
          </tr>
          <tr>
           <td class="label_cell" width="40%">Discount As Of</td>
            <? $this->PrintDiscountDate($prrecord, true, $rowIndex) ?>
          </tr>
          <tr>
           <td class="label_cell" width="40%">Discount Available</td>
            <? $this->PrintDiscountAmount($prrecord) ?>
          </tr>
         </table>
        </div>
        <? $rowIndex++; 
        }
        $rowIndex = 0; ?>
		<div id='divtable' style="overflow:auto;">
		<table class="field_list_data_old" style="table-layout=fixed;" border=0 width=100% cellspacing=0 cellpadding=2>
    <? foreach ($table as $prrecord) {

        $prKey = $prrecord['RECORD#'];
        $val=$val.$prKey.',';

        $this->totalentered = bcadd($this->totalentered, str_replace(',', '', ($prrecord['TRX_TOTALENTERED'] ?: $prrecord['TOTALENTERED'])));

        if ($this->_selected[$prKey]) {
            $this->totalselected =  bcadd($this->_selected[$prKey], $this->totalselected);
        } else {
            $this->totalselected =  bcadd($prrecord['SELECTED'], $this->totalselected);
        }

        if ($trclass == "multiline_bg_white") {
            $trclass = "multiline_bg_beige";
        } else {
            $trclass = "multiline_bg_white";
        }

        if ($prrecord['NAME'] == '') {
            list( , $prrecord['NAME']) = explode('--', $prrecord['IDNAME']);
        }
    ?>

			<tr height="30px" class="<?=$trclass?>" onClick="toggleColorF(<?=$prKey?>,<? 
    if ($trclass == "multiline_bg_white") {
        echo "'#FFFFFF'";
    } else {
        echo "'#E9E8DF'";
    }
?>);initRowClickState();" id="row1[<?=$prKey?>]" valign="middle">
			<input type=hidden name=".rectype[<?=$prKey?>]" value="<? echo $prrecord['RECORDTYPE'] ?>">
			<input type=hidden name=".entity[<?=$prKey?>]" value="<? echo $prrecord['ENTITY']?>">
			<input type=hidden name=".entityName[<?=$prKey?>]" value="<? echo $prrecord['NAME']?>">
			<input type=hidden name=".posamountdue[<?=$prKey?>]" value="<? echo $this->_posDueMap[$prKey] ?>">
			<input type=hidden name=".negamountdue[<?=$prKey?>]" value="<? echo $this->_negDueMap[$prKey] ?>">
    <? if ($this->mcenabled) { ?>
			<input type=hidden name=".curr[<?=$prKey?>]" value="<? echo $prrecord['CURRENCY']?>">
    <? 
} ?>
    <?
    if ($prrecord['TRX_TOTALENTERED']) {?>
     <input type=hidden name=".exchRate[<?=$prKey?>]" value="<? echo $prrecord['EXCHANGE_RATE'] ?>">
     <input type=hidden name=".exchRateType[<?=$prKey?>]" value="<? echo $prrecord['EXCH_RATE_TYPE_ID'] ?>">

    <?
    }
    foreach ($this->params['fields'] as $fld) {

        // $divx = $fld['divx'];
        // $divx['id'] = ( $fld['divx']['id'] != '' ? $fld['divx']['id'] . "[".$prrecord['RECORD#']."]" : "" );
        if ($fld['disableedit'] && $this->_mode == 'edit') { continue; 
        }
        if ($fld['value']) {
            $bold = false; $nowrap = "nowrap"; $colspan = 1; $rowspan = $fld['rowspan'];
            $valign = ($fld['valign']) ?: "center";
            // $this->PrintCell($prrecord[$fld['value']], $fld['align'], $valign, $bold, $nowrap, $colspan, $rowspan, "#CCCCCC", $divx);
            $this->PrintCell($prrecord[$fld['value']], $fld['align'], $valign, $bold, $nowrap, $colspan, $rowspan);
        }
        else {
            if ($this->_mode == 'edit' && $fld['oneditfunc']) {
                $this->{$fld['oneditfunc']}();
            } else if ($fld['function']) {
                $this->rspan = $fld['rowspan'];
                $this->align = $fld['align'];
                $this->valign = $fld['valign'];
                // $this->{$fld['function']}($prrecord, $divx);
                $this->{$fld['function']}($prrecord, $rowIndex);
            }
        }
    }

    ?></tr><?
            $rowIndex++;
}
            /*schuduledpaydateRecords array is built to identify the Scheduled Payment date fields in 
			UI to do clientside validations --a full fledged array can be built to identify all the fields of the record....*/
    ?>
			</table>
			</div>
		   <script language="javascript">
               var schuduledpaydateRecords=Array(<? echo isl_substr($val, 0, isl_strlen($val)-1); ?>);   
		   </script>	
    <?
    }



    function PrintTotals() 
    {

        if ($this->atlas && !$this->currency) {
            ?>
         <INPUT TYPE=hidden NAME=".total" value=<? echo $this->totaldiscount; ?>>
		        <INPUT TYPE=hidden NAME=collection VALUE=<? echo isl_trim($this->collection); ?>>
         <INPUT TYPE=hidden NAME=".totalappliedcredit" value=<? echo $this->totalappliedcredit; ?>>
         <INPUT TYPE=hidden NAME=".totaldiscount" value=<? echo $this->totaldiscount; ?>>
            <?
            return;
        }

        $_linecredits = &Request::$r->_linecredits;
        $totalcredit = Request::$r->_totalcredit;
        $_action = Request::$r->_action;

        if (!$totalcredit) {
            $totalcredit = $this->credits;
        }
        if($this->mcenabled) {
            $recidwidth = "8%";
            $vendwidth = "15%";
        }
        else {
            $recidwidth = "10%";
            $vendwidth = "18%";
        }
        if($this->_mode == 'edit') {
            $supdocidwidth = '<td width="5%"></td>';
            if($this->mcenabled) {
                $recidwidth = "6%";
            }
            else {
                $recidwidth = "20%";
            }
        }
        else if($_action == 'view') {
            $supdocidwidth = '<td width="5%"></td>';
            if($this->mcenabled) {            
                $recidwidth = "6%";
                $vendwidth = "12%";
            }
            else {
                $recidwidth = "8%";
                $vendwidth = "15%";
            }
        }

        ?>
     <table class="field_list_data" border=0 width=100% cellpadding=2 cellspacing=0>
     <TR class="multiline_header_bg" valign="middle"><td width="5%"></td><?= /** @noinspection PhpUndefinedVariableInspection */
         $supdocidwidth?><td width="<?=$recidwidth?>"></td><? if( $this->_mode != 'edit') { ?> <td width="<?=$vendwidth?>"></td>
        <? 
}
    
foreach ($this->vendcoll as $vkey=>$vcoll) { ?>
      <input type=hidden name=".<?=$vkey?>" value="<?=$vcoll?>">
      <input type=hidden name=".totalcredit[<?=$vkey?>]" value="<?=($this->negcrs[$vkey] ? bcadd($totalcredit[$vkey], $this->negcrs[$vkey]) : $totalcredit[$vkey])?>">
        <?
}
        
     $initVCObjJS = "";

foreach ($this->_creditLineItems as $vendor => $linecredits) {
    // Initialize array of VendorCredit objects for the Apply Credits feature.
    $initVCObjJS .= "var vcObj = new VendorCredit('$vendor');\n";
            
    foreach ($linecredits as $linekey=>$amount) {
        list($crKey, $lineNo) = explode('--', $linekey);
        if ( !$lineNo ) { $lineNo = 1; 
        }

        $initVCObjJS .= "vcObj.updAvAmt($amount, $crKey, $lineNo);\n";
        if (!$_linecredits[$vendor][$linekey]) {
            $_linecredits[$vendor][$linekey] = glFormatCurrency($amount);
        }?>
          <input type=hidden name=".linecredits['<?=$vendor?>']['<?=$linekey?>']" value="<?=$_linecredits[$vendor][$linekey] ?>">
            <?
    }
    $initVCObjJS .= "vcObjCache['$vendor'] = vcObj;\n\n";
}
        
     $jsdefined = true;
if($initVCObjJS == "") {
    $jsdefined = false;
}

foreach ($this->_entityCredits as $crdt) {            
    $crdt['DOCNUMBER'] = str_replace("'", "\\'", $crdt['DOCNUMBER']);
    $entity    = $crdt['ENTITY'];
    $sortData = array(
    $crdt['RECORD#'],
    $crdt['RECORDTYPE'],
    $crdt['WHENCREATED'],
    util_encode($crdt['DOCNUMBER']),
    $crdt['DOCTOTALDUE'],
    );
            
    if (!$jsdefined) {
        // Initialize array of VendorCredit objects for the Apply Credits feature.
        $initVCObjJS .= "var vcObj = new VendorCredit('$entity');\n";
        $initVCObjJS .= "vcObjCache['$entity'] = vcObj;\n\n";
    }
            
    $initVCObjJS .= "if (vcObjCache['".$entity."']) { vcObjCache['".$entity."'].crSortData.push(['" . join("','", $sortData) . "']); }\n";
}

if ($this->_mod == 'ap' && $this->mcenabled) {
    $colspan = ($this->_mode == 'edit') ? '3' : (!$this->_r ? '5' : '4');
    ?>
 <td width="5%">
  &nbsp;
 </td>
    <?
    $this->PrintCell("&nbsp;", "right", "middle", true, "nowrap", "", "", "", "10%");
} else {
    $colspan = ($this->_mode == 'edit') ? '2' : (!$this->_r ? '4' : '3');
    $this->PrintCell("&nbsp;", "right", "middle", true, "nowrap", "", "", "", "10%");
}
        
if ($this->_mod == 'ap') {
    $cnt = 1;
    foreach ($this->params['fields'] as $fld) {
        if ($cnt++ > $colspan) {
            if ($fld['total']) {
                $footerLabel = $fld['nototalheader'] ? '' : $fld['header'];
                $this->PrintCell($footerLabel, "right", "middle", true, "nowrap", "", "", $fld['width']);
                if ($fld['headercolspan'] > 1) {
                    $this->PrintCell('', "right", "middle", true, "nowrap", $fld['headercolspan']-1);
                }
            }
        }
    }

    $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "5%");
    $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "10%");
    $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "8%");
    ?>
 <td width="1%">&nbsp;</td>
    <?
    $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "10%");
    $this->PrintCell("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;", "", "", false, "", "", "", "", "5%");
    ?>
 <td id="lower1td" width="3%">
 &nbsp;&nbsp;&nbsp;&nbsp;
 </td>
 </TR><TR class="multiline_header_bg"><td width="5%"></td><?=$supdocidwidth?><td width=<?=$recidwidth?>></td><? if($this->_mode != 'edit') { ?> <td width="<?=$vendwidth?>"></td>
        <? 
}
if ($this->_mod == 'ap' && $this->mcenabled) {
    $colspan = ($this->_mode == 'edit') ? '3' : (!$this->_r ? '5' : '4');
    ?>
 <td width="5%">
  &nbsp;
 </td>
    <?
    $this->PrintCell("Total amount due", "right", "middle", true, "nowrap", "", "", "", "10%");
} else {
       $colspan = ($this->_mode == 'edit') ? '2' : (!$this->_r ? '4' : '3');
       $this->PrintCell("Total amount due", "right", "middle", true, "nowrap", "", "", "", "10%");
}
}
     $cnt = 1;
        
foreach ($this->params['fields'] as $fld) {
    if ($fld['disableedit'] && $this->_mode == 'edit') {
        continue;
    }
    if ($cnt++ > $colspan) {
        if ($fld['total']) {
            $this->{$fld['total']}();
        } 
    }
}
     $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "10%");
     $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "8%");
        ?>
     <td width="1%">&nbsp;</td>
        <?
        $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "10%");
        $this->PrintCell("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;", "", "", false, "", "", "", "", "5%");
        ?>
     <td id="lower2td" width="3%">
      &nbsp;&nbsp;&nbsp;&nbsp;
     </td>
        <? if ($this->_mod != 'ap') {
            $this->PrintCell("&nbsp;");
            $this->PrintCell("&nbsp;");
}
            
    ?></TR>
        <? if(!isset($this->_r) || $this->_r == '') { ?>
		<TR class="multiline_header_bg"><td width="5%"></td><?=$supdocidwidth?><td width=<?=$recidwidth?>></td><? if($this->_mode != 'edit') { ?> <td width="<?=$vendwidth?>"></td>
    <? 
}
if ($this->_mod == 'ap' && $this->mcenabled) {
    $this->PrintCell("&nbsp;", "", "", false, "nowrap", "", "", "", "5%");
}
        $this->PrintCell(_("Filtered bill total"), "right", "", false, "nowrap", "", "", "", "10%");
        $c = 1;
        
foreach ($this->params['fields'] as $fld) {
    if ($fld['disableedit'] && $this->_mode == 'edit') {
        continue;
    }
    if ($c++ > $colspan) {
        if ($fld['grandtotal']) {
            $this->{$fld['grandtotal']}();
        } 
    }
}
        $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "10%");
        $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "5%");
        $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "10%");
        $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "8%");
    ?>
		<td width="1%">&nbsp;</td>
    <?
    $this->PrintCell("&nbsp;", "", "", false, "", "", "", "", "10%");
    $this->PrintCell("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;", "", "", false, "", "", "", "", "5%");
    ?>
		<td id="lower3td" width="3%">
			&nbsp;&nbsp;&nbsp;&nbsp;
		</td>
    <? if ($this->_mod != 'ap') {
        $this->PrintCell("&nbsp;");
        $this->PrintCell("&nbsp;");
}
        echo "</TR>";
}
        echo "</table>";
        echo "\n<script language='JavaScript'>\n";
        echo $initVCObjJS;
        echo "</script>";
    }



    function PrintTotalEntered() 
    {
        ?><td align="right" width="8%" style="WORD-BREAK:BREAK-ALL;"><font class=Result1>
            <? echo glFormatCurrency($this->totalentered); ?></font></td><?
            //$this->PrintCell(glFormatCurrency($this->totalentered), "right", "", "", "", "", "", "", "8%");
    }



    function PrintTotalDue() 
    {
        ?><td align="right" width="10%" style="WORD-BREAK:BREAK-ALL;"><font class=Result1>
            <? echo glFormatCurrency($this->totaldue); ?></font></td><?
    }



    function PrintTotalAmount() 
    {
        ?><td align="right" width="10%" style="WORD-BREAK:BREAK-ALL;"><font class=Result1>
            <? echo glFormatCurrency($this->totaldue); ?></font></td><?
    }



    function PrintTotalSelected() 
    {
        ?><td valign="middle" align="right" colspan="<?=$this->cspan?>" width="10%">
    <?
        $_action = Request::$r->_action;
    if ($_action == 'view') {
        echo glFormatCurrency($this->totalselected);
    } else {
        $_params = array();
        $_params['varname'] = ".total";                
        $_params['value'] = $this->totalselected;
        $_params['onchange'] = "$this->_warnOnSaveOnChange";
        $_params['onfocus'] = "blur();";            
        $_params['size'] = '12';                            
        $_params['nocalc'] = '1';
        $_params['classname'] = 'amtclass';
        $ctrl = new DecimalControl($_params);
        $ctrl->Show(); ?>
    <? 
    } ?>
		
          <INPUT TYPE=hidden NAME=collection VALUE=<? echo isl_trim($this->collection); ?>>
        </td>
   <td>&nbsp;</td><?
    }



    /**
     * @param array $prrecord
     */
    function PrintAmountDue($prrecord)
    {
        
        $reckey = $prrecord['RECORD#'];
        if (!$this->_hamount[$reckey]) {
            $this->_hamount[$reckey] = $prrecord['HAMOUNT'];
        }
        ?><td align="right" width="10%" style="WORD-BREAK:BREAK-ALL;"><font class=Result1><? echo glFormatCurrency(($prrecord['TRX_POSTOTALDUE'] ?: $prrecord['POSTOTALDUE'])); ?>
     <INPUT TYPE=hidden NAME=.hamount[<?= urlencode($reckey); ?>] size="10" maxlength="15" VALUE="<?= $this->_hamount[$reckey]; ?>" onfocus="this.blur()">
     </font></td><?
    }



    /**
     * @param array $prrecord
     */
    function PrintBalance($prrecord)
    {
        $reckey = $prrecord['RECORD#'];

        if (!$this->_hamount[$reckey]) {
            $this->_hamount[$reckey] = $prrecord['HAMOUNT'];
        }
        if (!$this->_appliedcredit[$reckey]) {
            $this->_appliedcredit[$reckey] = ($prrecord['APPLIEDCREDIT'])?:'0.00';
        }
        
        $totaldue = $prrecord['TRX_POSTOTALDUE'] ?: $prrecord['POSTOTALDUE'];
        
        if( $this->_selected[$reckey] != '0.00' ) {
            $subdisc = $this->_discount[$reckey] ?: '0.00';
            // apply the discount only if we are paying in full
            $leftOver = bcsub($totaldue, $this->_selected[$reckey], 2);
            if( $subdisc != '0.00' && bccomp($leftOver, $subdisc) > 0 ) { $subdisc = '0.00'; 
            }
        }
        else {
            $subdisc = '0.00';
        }
        $subtractamount = ibcadd(ibcadd($this->_selected[$reckey], $this->_appliedcredit[$reckey], 2, true), $subdisc, 2, true); 

        ?><td id=".bal[<?=$reckey?>]" align="right" width="10%" style="WORD-BREAK:BREAK-ALL;"><font class=Result1><? echo glFormatCurrency(bcsub($totaldue, $subtractamount)); ?>
     </font></td><?
    }



    /**
     * @param array $prrecord
     * @param int $rowIndex
     */
    function PrintSelectRow($prrecord, $rowIndex)
    {
        $_action = Request::$r->_action;
        //$gifFile = ($rowIndex % 2 == 0) ? "todo_row_light.gif" : "todo_row_dark.gif";
        $trColor = ($rowIndex % 2 == 0) ? "#FFFFFF" : "#E9E8DF";
        $reckey = $prrecord['RECORD#'];
        if(!isset($this->_r) || $this->_r == '') {
            $checked = $this->selectrow[$reckey] ? "CHECKED" : '' ;
        }
        else {
            $checked = "CHECKED";
        }

        $disabled = ( ($_action == 'view') ? 'disabled' : '' );
        ?>
     <td ID="SELECTROW_TD:<? echo $rowIndex; ?>" align="center" valign="<?=$this->valign?>" rowspan="<?=$this->rspan?>" width="5%">
      <input type=hidden name=".selectedrow[<?=$reckey?>]" value="<?=$reckey?>">
      <input name=selectrow[<? echo urlencode($prrecord['RECORD#'])?>] ID="SELECTROW:<? echo $rowIndex; ?>" class=noborder type=checkbox onClick="javascript: toggleSelectRow(<? echo $reckey; ?>, '<? echo $trColor ?>')" <? echo $checked; ?> <? echo $disabled; ?> />
      <!--<img ID="SELECTROW_IMG:<? echo $reckey; ?>" src="../resources/images/ia-app/icons/<?= /** @noinspection PhpUndefinedVariableInspection */
      $gifFile ?>" width="20" height="20" border="0"/>-->
     </td><?
    }



    /**
     * @param array $prrecord
     * @param string $rowIndex
     */
    function PrintRecordID($prrecord, $rowIndex = '')
    {
        $_action = Request::$r->_action;
        $bold = false; $nowrap = "wrap"; $colspan = 1; $rowspan = 1; $style = "style='WORD-BREAK:BREAK-ALL;'";
        $valign = "middle"; 
        if($this->mcenabled) {
            if($this->_mode == 'edit' || $_action == 'view') {
                $width = "6%";
            }
            else {
                $width = "8%";
            }
        }
        else {
            if($this->_mode == 'edit') {
                $width = "20%";
            }
            else if($_action == 'view') {
                $width = "8%";
            }
            else {
                $width = "10%";
            }
        }
        $recordhash = urlencode($prrecord['RECORD#']);
        $recordtype = urlencode($prrecord['RECORDTYPE']); 
        $action = null;
        if ($recordtype == 'pi') {
            $opid = GetOperationId('ap/lists/apbill/view');
            $action = 'view';
        }
        elseif ($recordtype == 'pa') {
            $opid = GetOperationId('ap/lists/apadjustment/view');
            $action = 'view';
        }
        if ($recordtype == 'pi' || $recordtype == 'pa') {
            if ($action) {
                
                if ($prrecord['RECORDID'] == '') {
                    $prrecord['RECORDID'] = '<i>view details</i>';
                    $printId = '<i>view details</i>';
                } else {
                    $printId = util_encode($prrecord['RECORDID']);  // Don't encode italicized 'view details'
                }

                $script ="editor.phtml";
                /** @noinspection PhpUndefinedVariableInspection */
                $scripturl = $script . "?.do=" . $action . "&.recordtype=" . $recordtype . "&.r=" . $recordhash . "&.op=" . $opid . "&.popup=1" . "&.viewsonly=1";//&.includemebills=".$this->_includemebills."&.groupby=".$this->_groupby."&.payallentities=".$this->_payallentities."&.locationlist=".implode('|',$this->_locationlist);
                if ( ( $this->_includemebills == 'A' || $this->_includemebills == 'O' ) && $this->_groupby == 'N' && $this->_payallentities == 'N' ) {
                    Session::setProperty('locationlist', implode('|', $this->_locationlist));
                    $scripturl .= "&.locationListFlag=Y";
                }
                $prrecord['RECORDID'] = '<a id="recordid:'.$rowIndex.'" href="javascript:Launch(' . "'$scripturl', 'Details', '800', '600');" . '" ONMOUSEOVER=\'window.status="'.statusdisp('View Details') . '"; return true;\' ONMOUSEOUT=\'window.status="";return true;\' >' . 
                $printId . "</a>"; 
            }
        }
        $this->PrintCell($prrecord['RECORDID'], 'left', $valign, $bold, $nowrap, $colspan, $rowspan, '', $width, '', $style);
    }



    /**
     * @param array $prrecord
     */
    function PrintSupDoc($prrecord)
    {
        $bold = false; $colspan = 1; $rowspan = 1; $style = "style='WORD-BREAK:BREAK-ALL;'";
        $valign = "middle"; $width = '5%';
        // Create the attachment link if the record has supdoc attached
        $supdocid = urlencode(URLCleanParams::insert('.val', $prrecord['SUPDOCID']));
        if (isset($supdocid) && $supdocid != '') {
            $_sess = Session::getKey();
            $opid = GetOperationId('co/lists/supportingdocumentdata/view');
            $scripturl = "lister.phtml?.do=view&.it=supportingdocumentdata&.val=$supdocid&.op=$opid&.popup=1&.viewsonly=1&.sess=$_sess";

            $supdoc_link = '<a class="Pick"  id = "fld_obj__SUPDOCID"  title="view_attachment" href="#" onClick="Launch(\''.$scripturl.'\',\'supdoc\',600,400); return	false;"  onmouseover="window.status=\'View the attachments\';return true;"   onmouseout="window.status=\'\'; return true;"><img src="' . IALayoutManager::getCSSButtonPath("paperclip.gif") . '" border="0" alt="view_attachment" ></a>';
            // append paperclip icon next to bill/expense#
            $prrecord['SUPDOC'] =  $supdoc_link;
        }
        $this->PrintCell($prrecord['SUPDOC'], 'middle', $valign, $bold, '', $colspan, $rowspan, '', $width, '', $style);
    }



    /**
     * @param array $prrecord
     * @param string $rowIndex
     */
    function PrintVendor($prrecord, /** @noinspection PhpUnusedParameterInspection */ $rowIndex = '')
    {
        $_action = Request::$r->_action;
        $bold = false; $nowrap = "wrap"; $colspan = 1; $rowspan = 1;
        $valign = "middle"; 
        if($this->mcenabled) {
            if($_action == 'view') {
                $width = "12%";
            }
            else {
                $width = "15%";
            }
        }
        else{
            if($_action == 'view') {
                $width = "15%";
            }
            else {
                $width = "18%";
            }        
        }
        $this->PrintCell(util_encode($prrecord['IDNAME']), 'left', $valign, $bold, $nowrap, $colspan, $rowspan, '', $width);
    }



    /**
     * @param array $prrecord
     * @param string $rowIndex
     */
    function PrintCurrency($prrecord, /** @noinspection PhpUnusedParameterInspection */ $rowIndex = '')
    {
        $bold = false; $nowrap = "wrap"; $colspan = 1; $rowspan = 1; $width = "5%";
        $valign = "middle";
        $this->PrintCell($prrecord['CURRENCY'], 'left', $valign, $bold, $nowrap, $colspan, $rowspan, "", $width);
    }



    /**
     * @param array $prrecord
     * @param string $rowIndex
     */
    function PrintWhenDue($prrecord, /** @noinspection PhpUnusedParameterInspection */ $rowIndex = '')
    {
        $bold = false; $nowrap = "wrap"; $colspan = 1; $rowspan = 1;
        $valign = "middle"; $width = "10%";
        $this->PrintCell($prrecord['WHENDUE'], 'left', $valign, $bold, $nowrap, $colspan, $rowspan, "", $width);
    }



    /**
     * @param array $prrecord
     * @param string $rowIndex
     */
    function PrintMoreFields($prrecord, $rowIndex = '')
    {
        $bold = false; $nowrap = "wrap"; $colspan = 1; $rowspan = 1;
        $valign = "middle"; $width="5%";
        $reckey = $prrecord['RECORD#'];
        $class = '';
        if( isset($this->_discount[$reckey]) && $this->_discount[$reckey] != '0.00' ) {
            $class = "style=font-weight:bold;color:#FF0000;";
        }
        $value = '<a href="#Skip" id="divPopup['.$rowIndex.']" onclick="javascript:AddFields(' .$rowIndex. ', '.$reckey . ')"' . $class . '>More</a>';
        $this->PrintCell($value, 'center', $valign, $bold, $nowrap, $colspan, $rowspan, "", $width);?>
    <?
    }



    /**
     * @param array $prrecord
     */
    function PrintDiscountAmount($prrecord)
    {
        $reckey = $prrecord['RECORD#'];

        if (!isset($this->_discount[$reckey]) || $this->_discount[$reckey] == '') {
            $this->_discount[$reckey] = $prrecord['DISCOUNT'];
        }

        $_action = Request::$r->_action;

        ?>
     <td align=middle 
        <? if ($prrecord['RECORDTYPE'] != "pa") {
            if ($_action == 'view') {
                echo ' id=".discount['.urlencode($reckey).']">'; // Add the ID in TD to get the value
                echo glFormatCurrency($this->_discount[$reckey]?:0);
            } else {
                echo '>'; // close TD
                $_params = array();
                $_params['varname'] = ".discount[".urlencode($reckey)."]";                
                $_params['value'] = ($this->_discount[$reckey])?:'0.00';
                $_params['onchange'] = $this->_warnOnSaveOnChange;
                $_params['onfocus'] = 'blur()';            
                $_params['size'] = '10';                            
                $_params['maxlength'] = '15';
                $_params['nocalc'] = '1';    
                $_params['readonly'] = true;
                $ctrl = new DecimalControl($_params);
                $ctrl->Show(); 
            ?>
             <INPUT TYPE=hidden
               NAME=discountAmount[<?=urlencode($reckey); ?>]
						VALUE=<?= $prrecord['DISCOUNTAMOUNT']; ?> >
				<INPUT TYPE=hidden
						NAME=cutoffDate[<?=urlencode($reckey); ?>]
						VALUE="<?=$prrecord['CUTOFFDATE']; ?>">
				<INPUT TYPE=hidden
					   NAME=invoiceDate[<?=urlencode($reckey); ?>]
					   VALUE="<?=$prrecord['WHENCREATED']; ?>">
				<INPUT TYPE=hidden
					   NAME=dueDate[<?=urlencode($reckey); ?>]
					   VALUE="<?=$prrecord['WHENDUE']; ?>">
            <? 
            } ?>
    <? 
} ?>
     </td><?
    }



    /**
     * @param array $prrecord
     */
    function PrintCreditToApply($prrecord)
    {

        $reckey = $prrecord['RECORD#'];

        $crJSArray = ( isset($this->_crArray[$reckey]) && count($this->_crArray[$reckey]) > 0 && is_array($this->_crArray[$reckey]) ? true : false);
        $didSplit = ($this->_didsplit[$reckey] == 1 || countArray($prrecord['ITEMSELECTS']) > 0 || $crJSArray) ? 1 : "";

        if (!$this->_appliedcredit[$reckey]) {
            // $discount = abs($prrecord['DISCOUNT']);
            // if (($this->_mode != 'edit') && bcadd($discount, abs($this->_negDueMap[$reckey]) ) > $this->_posDueMap[$reckey]) {
            // 	$availCreditsToApply = bcsub($prrecord['APPLIEDCREDIT'], $discount);
            // } else {
            // 	$availCreditsToApply = $prrecord['APPLIEDCREDIT'];
            // }
            // $this->_appliedcredit[$reckey] = glCurrency($availCreditsToApply);
            $this->_appliedcredit[$reckey] = ($prrecord['APPLIEDCREDIT'])?:'0.00';
        }

        $posDue = ( $this->_posDueMap[$reckey] ?: 0 );
        $negDue = ( $this->_negDueMap[$reckey] ?: 0 );

        $onblur = "ok = ApplyCredits(this, $reckey, '$prrecord[ENTITY]', $posDue, $negDue);";

        $disabled = ( !$didSplit ? '' : 'disabled' );

        ?><td align="<?
        if($this->align) { echo($this->align);
        } else { echo("center"); 
        } ?>" width="10%">
        <?
        $_action = Request::$r->_action;

        if ($_action == 'view') {
            echo glFormatCurrency($this->_appliedcredit[$reckey]?:0);
        } else {
            $_params = array();
            $_params['varname'] = ".appliedcredit[".urlencode($reckey)."]";                            
            $_params['value'] = ($this->_appliedcredit[$reckey])?:'0.00';
            $_params['onchange'] = $this->_warnOnSaveOnChange;
            $_params['onblur'] = $onblur;            
            $_params['size'] = '12';                            
            $_params['maxlength'] = '15';
            $_params['nocalc'] = '1';
            $_params['disabled'] = $disabled;
            $_params['classname'] = 'amtclass';
            $ctrl = new DecimalControl($_params);
            $ctrl->Show(); 
        ?>
		
         <INPUT TYPE=hidden NAME=".creditsplit[<?=$reckey?>]" value="<?=$this->_appliedcredit[$reckey]?>">
         <INPUT type=hidden NAME=".appliedcreditamt[<?= urlencode($reckey);?>]" value="<?=$this->_appliedcredit[$reckey]?>">

        <? 
        } ?>
     </td>
        <? if ($this->_mod != 'ap') { ?>
			<td>&nbsp;</td>
    <? 
}
    }



    /**
     * @param array $prrecord
     */
    function PrintAmountBilled($prrecord)
    {
        ?><td align="right" valign="middle" width="8%" style="WORD-BREAK:BREAK-ALL;"><font class=Result1><? 
        // not getting rid of glCurrency applied earlier for now as its
        // a sensitive change
        $totalentered = str_replace(
            ',', '',
            ($prrecord['TRX_TOTALENTERED'] ?:
                $prrecord['TOTALENTERED'])
        );
        echo glFormatCurrency($totalentered);
    ?></font></td><?
    }



    /**
     * @param array $prrecord
     */
    function PrintTotalAvailable($prrecord)
    {
        $_changecurr = Request::$r->_changecurr;

        if(!$_changecurr) {
            $_availcredit_helper = Request::$r->_availcredit_helper;
        }

        $reckey = $prrecord['RECORD#'];
        $credits = str_replace(',', '', $prrecord['ENTITYCREDITS']);

        $totalcredits = bcadd(
            $credits,
            bcadd(ibcabs($this->_negDueMap[$reckey]), $this->negcrs[$prrecord['ENTITY']])
        );

        $creditTaken =
        $this->appliedentcredits[$prrecord['ENTITY']];

        ?>
     <td align="right" valign="middle" width="8%"><font class=Result1 id=".availcredit[<?=$reckey?>]">
        <?
        /** @noinspection PhpUndefinedVariableInspection */
        if (!$_availcredit_helper) {
            $availCredits = ((isset($this->_r) && $this->_r !='')  ?
            bcsub($this->totalentcredits[$prrecord['ENTITY']], $creditTaken) :
            $totalcredits);
            $_availcredit_helper[$reckey] = $availCredits;
        }
        echo glFormatCurrency($_availcredit_helper[$reckey]);
        ?>
      </font>
      <input type = hidden name=".availcredit_helper[<?=$reckey?>]" value="<?=$_availcredit_helper[$reckey]?>">
     </font>
     </td>
     <td width="1%">&nbsp;</td>
        <?
    }



    /**
     * @param array $prrecord
     */
    function PrintSelectedAmount($prrecord)
    {
        $_action = Request::$r->_action;
        $_paysourceentity        = Request::$r->_paysourceentity;
        $_paysourceobjectstoreid = Request::$r->_paysourceobjectstoreid;

        $reckey = $prrecord['RECORD#'];
        $posDue = ( $this->_posDueMap[$reckey] ?: 0 );
        $negDue = ( $this->_negDueMap[$reckey] ?: 0 );
        if (!$this->_selected[$reckey]) {
            if ($prrecord['CURRENCY'] == GetBaseCurrency()) {
                $this->_selected[$reckey] = isl_trim($prrecord['SELECTED'])?:'0.00';
            } else {
                $this->_selected[$reckey] = isl_trim($prrecord['TRX_SELECTED'])?:'0.00';
            }
        }
        $entity = $prrecord['ENTITY'];
        $credits = bcadd(str_replace(',', '', $prrecord['ENTITYCREDITS']), ibcabs($negDue));
        $totcreds = bcadd(
            $credits,
            bcadd(ibcabs($this->_negDueMap[$reckey]), $this->negcrs[$prrecord['ENTITY']])
        );
        
        //		didsplit variable shouldn't be set when coming in the first time while creating or editing.
        //		In Case of editing ITEMSELECTS will have a value and we shouldn't consider it for didsplits.
        //		why is above comment valid anymore # rpn

        $crJSArray = ( isset($this->_crArray[$reckey]) && count($this->_crArray[$reckey]) > 0 && is_array($this->_crArray[$reckey]) ? true : false);
        $didSplit = ($this->_didsplit[$reckey] == 1 || countArray($prrecord['ITEMSELECTS']) > 0 || $crJSArray) ? 1 : "";


        $itemlineno = is_array($prrecord['ITEMSELECTS'])?array_keys($prrecord['ITEMSELECTS']):null;
        $credlines = (isset($this->_crArray[$reckey][$itemlineno[0]])) ? count($this->_crArray[$reckey][$itemlineno[0]]) : 0;

        //  Make sure the entity is properly UTF-8 encoded before it is embedded into the javascript.
        $entityJS = isl_str_to_js($entity);
        if ( ( $this->_includemebills == 'A' || $this->_includemebills == 'O' ) && $this->_groupby == 'N' && $this->_payallentities == 'N' ) {
            $ll = ", 'Y'";
        } else {
            $ll = false;
        }
        $onClick = "javascript: doSplit('$reckey', '$entityJS', '$totcreds', '$this->_r', '$_paysourceentity', '$_paysourceobjectstoreid'$ll);";

        $attribs = LinkNavAttribs("split", ".selected[$reckey]");
        $attribs2 = LinkNavAttribs("clear", ".clear[$reckey]");

        if ( !$didSplit ) {
            $disabled = '';
            $doSplitText = 'split';
            $clearSplitText = '';
        } else {
            $disabled = 'disabled';
            $doSplitText = ($_action == 'view' ? 'view' : 'edit');
            if ($credlines > 0) {
                $clearSplitText = 'clear';
            }
        }

        ?>
     <td align="right" valign="middle" width="10%" nowrap>
        <?
        if ($_action == 'view') {
            echo glFormatCurrency($this->_selected[$reckey]?:0);
        ?><input type=hidden name=".selected[<?=$reckey?>]" value="<?=($this->_selected[$reckey]?:0)?>"><?
        } else {
            $_params = array();
            $_params['varname'] = ".selected[".urlencode($reckey)."]";                                
            $_params['value'] = ($this->_selected[$reckey])?:'0.00';
            $_params['onchange'] = $this->_warnOnSaveOnChange." SetInlineCreditAmount(this, $reckey, $negDue); CheckAmount(this, $reckey, $posDue, $negDue); return InitSplitContext($reckey);";
            //		$_params['onblur'] = "CheckAmount(this, $reckey, $posDue, $negDue);";
            $_params['disabled'] = $disabled;
            $_params['roundcurrency'] = '1';
            $_params['size'] = '12';
            $_params['maxlength'] = '15';                
            $_params['nocalc'] = '1';
            $_params['classname'] = 'amtclass';
            $ctrl = new DecimalControl($_params);
            $ctrl->Show();
        }
        ?>

        <?

        ?>
     <INPUT TYPE=hidden NAME=".selectedsplit[<?=$reckey?>]" value="<?= $this->_selected[$reckey]; ?>">
     </td>
     <td align="left" valign="middle" width="5%" wrap>
        <?
        if( Request::$r->_op != GetOperationId("ap/lists/appaymentrequest")) {
        ?>
          <A <?=$attribs?> href="#<?=$reckey?>" onclick="<?=$onClick?>" align="middle"><?=$doSplitText?></A>&nbsp;
        <?
        }
        else { 
        ?>
          <A <?=$attribs?> href="#<?=$reckey?>" onclick="<?=$onClick?>" align="middle">View</A>&nbsp;
        <?
        } 
        ?>
        <? if (($_action != 'view') && !( Request::$r->_op == GetOperationId("ap/lists/appaymentrequest"))) {

?>
			<A <?=$attribs2?> href="#Skip" onclick="ClearSplits('<?=$reckey?>','<?= /** @noinspection PhpUndefinedVariableInspection */
            $disableAdvance?>');" align="middle"><?= /** @noinspection PhpUndefinedVariableInspection */
                $clearSplitText?></A>
		</td>
    <? 
} ?>
    <?
     $entryArr = isset($prrecord['ITEMSELECTS']) ? array_keys($prrecord['ITEMSELECTS']) : $prrecord['PRENTRYLINENOS'];    
    foreach($entryArr as $entrykey) {
        $prevItemSelAmt = $this->_splits[$reckey][$entrykey];
        $itemSelAmt = $prevItemSelAmt ?: $prrecord['ITEMSELECTS'][$entrykey];
        ?><INPUT TYPE=hidden  NAME=.splits<?="[$reckey][$entrykey]"?> VALUE="<?= $itemSelAmt ?>" ><?
    }
        ?>
        <INPUT TYPE=hidden  NAME=.didsplit[<?= urlencode($reckey);   ?>] VALUE="<?= $didSplit ?>" >
     <input type=hidden name=".selectedamt[<?= urlencode($reckey); ?>]" value="<?=$this->_selected[$reckey];?>">
        <?
        // populate the .crArray in JS fashion
        $cArr = $this->_crArray;
        $crString = '';
        $irArr = $cArr[$reckey];
        if ($irArr && is_array($irArr)) {
            $crString .= "array (";
            foreach($irArr as $ii => $iiArr) { 
                $crString .= " $ii => array(";
                foreach($iiArr as $cr => $crArr) {
                    $crString .= "$cr => array(";
                    foreach($crArr as $ci => $amt) {
                        list( , $lineNo) = explode("--", $ci);
                        $crString .= "$lineNo => $amt,";
                    }
                    $crString .= "),";
                }
                $crString .= "),";
            }
            $crString .= ")";
        }
        if(!empty($crString)) {?>
          <input type=hidden name=".crArray[<?= urlencode($reckey); ?>]" value="<?=$crString?>">
            <?
        } else { ?>
            <input type=hidden name=".crArray[<?= urlencode($reckey); ?>]" value="<?=$prrecord['appliedKeyMap']?>">
            <?
        }?>
           </td>
    <?
    }


    /**
     * @param string $value
     * @param int $rowspan
     * @param int $colspan
     * @param string $align
     * @param string $valign
     * @param string $width
     * @param string $blank
     */
    function PrintHeaderCell($value, $rowspan = 1, $colspan = 1, $align="center", $valign = "", $width="", $blank = "")
    {
        $this->PrintCell($value, $align, ($valign ?: "middle"), "bold", "", 1, $rowspan, "", $width, $blank);
        
        if ($colspan > 1) {
            $this->PrintCell('', $align, ($valign ?: "middle"), "bold", "", $colspan - 1, $rowspan, "", $width, $blank);
        }
    }



    /**
     * @param string $value
     * @param string $align
     * @param string $valign
     * @param bool $bold
     * @param string $nowrap
     * @param int $colspan
     * @param int $rowspan
     * @param string $bgcolor
     * @param string $width
     * @param string $blank
     * @param string $style
     */
    function PrintCell($value,
                       $align = "center",
                       $valign = "middle",
                       $bold = false,
                       $nowrap = "nowrap",
                       $colspan = 1,
                       $rowspan = 1,
                       $bgcolor = "#ffffff",
                       $width = "",
                       $blank = "",
                       $style = ""
    ) {
        echo    "<td align='$align' valign='$valign' $nowrap rowspan='$rowspan' colspan='$colspan' width='$width' $style><font class='Result1'>" .  ( $bold ? "$value" : $value ) . "</td>";
        
        if($blank) {
            echo "<td width='$blank'>&nbsp;</td>";
        }
        
    }


    /**
     * @param int $businessNumDays
     */
    function DefinePaymentDateEstimator($businessNumDays){
        $boxName = 'Estimate delivery date for '.$this->_paymethod.' payments';
        ?>
        <div id="paymentdateestimatordiv"
             style="position:absolute;visibility:hidden;display:none;z-index:1; height:150px; width:300px;"
             onkeydown="if(event.keyCode==27){ ClosePaymentDateEstimator('paymentdateestimatordiv'); }">
            <table border=0 cellpadding="4" cellspacing="0"  bgcolor="#999999" class="field_list_data">
                <tr bgcolor="#DDD7C1">
                    <td nowrap colspan=3>
                        <table border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <td align="left"><b><?=$boxName?></b></td>
                                <td align="right" width="20%">
                                    <input style="height=18px;" class="nosavehistory" type="button"
                                           id="closepaymentdateestbutton" value="Close"
                                           onClick="ClosePaymentDateEstimator('paymentdateestimatordiv');"
                                           onkeydown="if(event.keyCode==9){ ClosePaymentDateEstimator('paymentdateestimatordiv'); }"/>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="label_cell" width="40%">Final approval date</td>
                    <td align="center" colspan=1 nowrap>
                        <? $this->PrintApprovalDate($businessNumDays) ?>
                    </td>
                </tr>
                <tr>
                    <td class="label_cell" width="40%">Estimated payment delivery date</td>
                    <td align="left">
                        <? $this->PrintEstimatedDate() ?>
                    </td>
                </tr>
                <tr bgcolor="#DDD7C1">
                    <td colspan="2" style="font-size: x-small">
                        <?php echo _("Get a rough estimate of when vendors can expect payments."); ?>
                    </td>
                </tr>
            </table>
        </div>
        <?
    }

    /**
     * @param int $businessNumDays
     */
    function PrintApprovalDate($businessNumDays){
        $eventOnChange = "EstimatePaymentDate(this, 'PaymentDateEstimatorApprovalDate', 'PaymentDateEstimatorEstimatedDate', $businessNumDays);";
        $_params = array();
        $_params["varname"] = "PaymentDateEstimatorApprovalDate";
        $_params["size"] = "10";
        $_params["maxlength"] = "15";
        $_params["onchange"] = $eventOnChange;
        $_params["value"] = null;
        $_params['class'] = 'class="amtclass"';
        $_params["warningtext"] = true;
        $_params["true"] = true;
        $_params['defaulttotodayreally'] = true;
        $_params['mindate'] = "0";
        $_params['maxdate'] = "+6M";

        $ctrl = new DateControl($_params);
        $ctrl->Show();
    }

    function PrintEstimatedDate(){
        $eventOnBlur = "if(document.getElementById('closepaymentdateestbutton')) { document.getElementById('closepaymentdateestbutton').focus(); }";
        $_params = array();
        $_params["varname"] = "PaymentDateEstimatorEstimatedDate";
        $_params["size"] = "10";
        $_params["maxlength"] = "15";
        $_params["value"] = "";
        $_params['classname'] = "amtclass";
        $_params['readonly'] = true;
        $_params["onblur"] = $eventOnBlur;
        $ctrl = new TextControl($_params);
        $ctrl->Show();
    }
}

