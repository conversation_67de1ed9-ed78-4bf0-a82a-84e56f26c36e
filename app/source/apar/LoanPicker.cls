<?
//=============================================================================
//
//      FILE:                   LoanPicker.cls
//      AUTHOR:                 Nirmal Shukla
//      DESCRIPTION:
//
//      (C)2000, Intacct Corporation, All Rights Reserved
//
//      Intacct Corporation Proprietary Information.
//      This document contains trade secret data that belongs to Intacct 
//      corporation and is protected by the copyright laws. Information herein 
//      may not be used, copied or disclosed in whole or part without prior 
//      written consent from Intacct Corporation.
//
//=============================================================================
class LoanPicker extends NPicker
{

    function __construct()
    {
        parent::__construct(
            array(
            'entity' => 'loan',
            'title' => 'IA.LOANS',
            'helpfile' => '',
            'fields' => array(
                                        'LOANID',
                                        'DESCRIPTION',
                                        'LOAN_NUMBER'
                                ),
            )
        );
    }
} 


