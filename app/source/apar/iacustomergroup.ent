<?php

/**
 * Entity file for IA Customer Group
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Entity file
 */
$kSchemas['iacustomergroup'] = array(
    'object' => array(
        'RECORDNO',
        'ID',
        'NAME',
        'DESCRIPTION',
        'GROUPTYPE',
        'MEMBERFILTERS',
        'INDUSTRYCODE'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'ID' => 'id',
        'NAME' => 'name',
        'DESCRIPTION' => 'description',
        'GROUPTYPE' => 'grouptype',
        'MEMBERFILTERS' => 'memberfilters',
        'INDUSTRYCODE' => 'industrycode',
    ),
    'fieldinfo' => array(
        array(
            'fullname' => 'IA.RECORD_NO',
            'desc' => 'IA.RECORD_NO',
            'path' => 'RECORDNO',
            'type' => array('ptype' => 'text', 'type' => 'text',
                'maxlength' => 8, 'format' => '/[\w\s_\-\.]{0,8}/'),
            'hidden' => true,
            'id' => 1
        ),
        array(
            'fullname' => 'IA.CUSTOMER_GROUP_ID',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => $gCustomerIDFormat,
            ),
            'desc' => 'IA.UNIQUE_IDENTIFIER',
            'renameable' => true,
            'path' => 'ID',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.NAME',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
            ),
            'desc' => 'IA.NAME',
            'path' => 'NAME',
            'id' => 3
        ),
        array(
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength' => 500,
            ),
            'desc' => 'IA.DESCRIPTION',
            'path' => 'DESCRIPTION',
            'id' => 4
        ),
        array(
            'fullname' => 'IA.GROUP_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.ALL_MEMBERS'),
                'validvalues' => array('ALL'),
                '_validivalues' => array('A'),
            ),
            'default' => 'ALL',
            'desc' => 'IA.GROUP_TYPE',
            'path' => 'GROUPTYPE',
            'id' => 5
        ),
        array(
            'fullname' => 'IA.SORT_ORDER',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.ASCENDING', 'IA.DESCENDING'),
                'validvalues' => array('ASC', 'DESC'),
            ),
            'default' => 'ASC',
            'desc' => 'IA.SORT_ORDER',
            'path' => 'SORTORDER',
            'id' => 6

        ),
        array(
            'fullname' => 'IA.MAX_MATCHES',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 7,
                'maxlength' => 4,
            ),
            'desc' => 'IA.RESTRICT_TO',
            'path' => 'RESTRICTTO',
        ),
        array(
            'fullname' => 'IA.SORT_FIELD',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 30
            ),
            'desc' => 'IA.SORTING_FIELD',
            'path' => 'SORTFIELD',
        ),
        array(
            'fullname' => 'IA.FIELD',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 30
            ),
            'desc' => 'IA.FIELD',
            'path' => 'FIELD',
        ),
        array(
            'fullname' => 'IA.OPERATOR',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 10,
            ),
            'desc' => 'IA.OPERATOR',
            'path' => 'OPERATOR',
        ),
        array(
            'fullname' => 'IA.VALUE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 32
            ),
            'desc' => 'IA.VALUE',
            'path' => 'VALUE',
        ),
        array(
            'fullname' => 'IA.CONDITION',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.CONDITION',
            'path' => 'CONDITION',
        ),
        array(
            'fullname' => 'IA.CONDITION_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.ALL_AND', 'IA.ANY_OR', 'IA.EXPRESSION'),
                'validvalues' => array('AND', 'OR', 'EXPRESSION'),
            ),
            'default' => 'AND',
            'desc' => 'IA.CONDITION_TYPE',
            'path' => 'CONDITIONTYPE',
        ),
        array(
            'path' => 'INDUSTRYCODE',
            'fullname' => 'IA.INDUSTRY',
            'desc' => 'IA.INDUSTRY',
            'required' => true,
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => [],
                'validvalues' => [],
                '_validivalues' => [],
                'forceCombo' => true,
            ),
            'showlabelalways' => true
        ),
    ),
    'printas' => 'IA.CUSTOMER_GROUP',
    'pluralprintas' => 'IA.CUSTOMER_GROUPS',
    'nosysview' => true,
    
    'global' => true,
    'table' => 'iacustomergroup',
    'module' => 'ar',
    'autoincrement' => 'RECORDNO',
    'vid' => 'ID',
    'renameable' => true,
    'nochatter' => true,
);
