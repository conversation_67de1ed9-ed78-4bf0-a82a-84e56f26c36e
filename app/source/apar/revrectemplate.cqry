<?

$krevrectemplateQueries['QRY_REVRECTEMPLATE_TRANSACTION_REFS_EXIST'] = array(
    'QUERY' => 'SELECT 1 from dual WHERE EXISTS (SELECT 1 FROM prentry WHERE revrectemplatekey = ?  AND cny# =?)',
    'ARGTYPES' => array('integer' ,'integer')
);

$krevrectemplateQueries['QRY_REVRECTEMPLATE_DOC_REFS_EXIST'] = array(
    'QUERY' => 'SELECT 1 from dual WHERE EXISTS (SELECT 1 FROM docentry WHERE revrectemplatekey = ?  AND cny# =?)',
    'ARGTYPES' => array('integer' ,'integer')
);

$krevrectemplateQueries['QRY_REVRECTEMPLATE_UPDATE_ICITEMGLGRP_REFS'] = array(
    'QUERY' => 'UPDATE icitemglgrp SET defaultrevrectemplkey = ? WHERE defaultrevrectemplkey = ?  AND cny# =?',
    'ARGTYPES' => array('integer' ,'integer', 'integer')
);

$krevrectemplateQueries['QRY_REVRECTEMPLATE_UPDATE_ACCOUNTLABEL_REFS'] = array(
    'QUERY' => 'UPDATE accountlabel SET revrectemplkey = ? WHERE revrectemplkey = ?  AND cny# =?',
    'ARGTYPES' => array('integer' ,'integer', 'integer')
);

$krevrectemplateQueries['QRY_REVRECTEMPLATE_UPDATE_LATESTVERSION'] = array(
    'QUERY' => 'update revrectemplate set latestversionkey = ? WHERE record# = ? AND cny# =?',
    'ARGTYPES' => array('integer' ,'integer', 'integer')
);

$krevrectemplateQueries['QRY_REVRECTEMPLATE_GET_MAXVERSION'] = array(
    'QUERY' => 'SELECT max(latestversionkey) as maxversion FROM revrectemplate WHERE templateid = ? AND cny# =?',
    'ARGTYPES' => array('text' , 'integer')
);

$krevrectemplateQueries['QRY_REVRECTEMPLATE_SELECT_RAW_TEMPLATEID'] = array(
        'QUERY' => "SELECT * FROM revrectemplate WHERE templateid =?  AND latestversionkey is null AND cny# =?  ",
        'ARGTYPES' => array('text', 'integer' )
    );
$krevrectemplateQueries['QRY_REVRECTEMPLATE_SELECT_RAW_TEMPLATE'] = array(
        'QUERY' => "SELECT * FROM revrectemplate WHERE record# = ?  AND cny# =?  ",
        'ARGTYPES' => array('text', 'integer' )
    );
$krevrectemplateQueries['QRY_REVRECTEMPLATE_GET_ICITEMGLGRP_RECORDS'] = array(
        'QUERY' => "SELECT record# FROM icitemglgrp WHERE defaultrevrectemplkey = ?  AND cny# =?  ",
        'ARGTYPES' => array('integer', 'integer' )
    );
$krevrectemplateQueries['QRY_REVRECTEMPLATE_GET_ACCOUNTLABEL_RECORDS'] = array(
        'QUERY' => "SELECT record# FROM accountlabel WHERE revrectemplkey = ?  AND cny# =?  ",
        'ARGTYPES' => array('integer', 'integer' )
    );
$krevrectemplateQueries['QRY_REVRECTEMPLATE_VSOE_EXISTS'] = array(
        'QUERY' => "SELECT record# FROM revrectemplate WHERE templateid = ?  AND cny# =?  ",
        'ARGTYPES' => array('text', 'integer' )
    );