<?php

/**
 * Picker class for Customergroup Picker
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Picker class for Customergroup Picker
 */
class CustomerGroupPicker extends NPicker
{

    /**
     * __construct     
     */
    function __construct()
 {
        parent::__construct(
            array(
            'entity'        =>  'customergroup',
            'pickfield'        =>  'ID',
                'addlPickFields'=> array('NAME'),
            'fields'        =>  array('ID', 'NAME', 'GROUPTYPE', 'DESCRIPTION'),
            )
        );
    }
}
