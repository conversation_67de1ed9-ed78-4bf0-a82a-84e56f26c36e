<?
/*=============================================================================
*
*	FILE:			edit_appayment.phtml
*	AUTHOR:			<PERSON><PERSON>
*	DESCRIPTION:	
*
*	(C)2000, Intacct Corporation, All Rights Reserved
*
*	Intacct Corporation Proprietary Information.
*	This document contains trade secret data that belongs to Intacct 
*	corporation and is protected by the copyright laws. Information herein 
*	may not be used, copied or disclosed in whole or part without prior 
*	written consent from Intacct Corporation.
*
*	NOTES ON CREDIT SPLITS:
*
*	This page will have
*	(1) creditStrings (JavaScript variable)
*		(a) Maps bill keys to a compact string coded with split credit payment info
*		(b) Populated first via our call to SelectToPay::PrintJavascript()
*		(c) Parsed by PaymentHelper::GetCreditSplits()
*			(i) applypayments.phtml call to InitCreditInfo() uses this parse to
*			populate its credit_info (JavaScript) variable
*		(d) Updated by PaymentHelper::SetSplitSelections()
*	(2) .crArray (HTML hidden input)
*		(a) Submitted as the final decision on how credits will be split
*		(b) Updated by PaymentHelper::SetSplitSelections()
*		(c) Created by SelectToPay::PrintSelectedAmount() method, via its PrintPage() method
*	(3) .linecredits (HTML hidden input)
*		(a) Tracks the amount of credit available for a particular vendor/credit/line no. combination.
*		(b) Created by SelectToPay::PrintTotals() method using that class's _creditLineItems member variable
*		(c) Updated by SetParentSplitSelections() inside splitinvoicelines.phtml as additional credits are used up.
*		(d) Updated by PaymentHelper::UndoCreditSplits(), called via PaymentHelper::ClearSplits().
*		(e) Used by InitCreditInfo() inside applypayments.phtml to display the Amount Available 
*			for each credit line item.
*
*=============================================================================*/

require_once 'html_header.inc';
require_once 'util.inc';
require_once 'show_listing.inc';
require_once 'groom_lister.inc';
require_once 'backend_selecttopay.inc';
require_once 'browser.inc';
require_once 'appayment.cls';
require_once 'backend_npayment.inc';
require_once 'APSelectToPay.cls';
require_once "userreport_pref.inc"; // Fields to keep persistance after view event in Select To Pay screen.

$localTokens = ['IA.SELECT_TO_PAY','IA.VIEW_PAYMENT_REQUEST','IA.EDIT_PAYMENT_REQUEST','IA.EXPENSES','IA.CUSTOMIZE',
    'IA.SAVE', 'IA.EXPENSE','IA.EMPLOYEE_ID','IA.EMPLOYEE_NAME','IA.PAYMENT_DATE','IA.PAYMENT_BR_CURRENCY', 'IA.FILTER',
    'IA.AMOUNT_SELECTED','IA.BASE_BR_CURRENCY','IA.MEMO','IA.CONTINUE','IA.CANCEL','IA.DOCUMENT_NUMBER','IA.AMOUNT_PAID_BY_BANK_NAME',
    'IA.EXPENSE_PAYMENT_REQUESTS_SUMMARY','IA.SELECTING_EXPENSES_PAY','IA.BILLS','IA.OF','IA.ONE_TO_COUNT_OF_TOTAL_RECORD_TYPE','IA.SELECT_ALL_ENTITIES','IA.PROCESSING_PAYMENT_REQUESTS_PLEASE_WAIT'];
I18N::addTokens(I18N::tokenArrayToObjectArray($localTokens));
I18N::getText();


Init();
$_op                = Request::$r->_op;
$_r                 = Request::$r->_r;
$_action            = Request::$r->_action;
$_sess              = Request::$r->_sess;
$_done              = Request::$r->_done;
$_mod               = &Request::$r->_mod;
$_currency          = &Request::$r->_currency;
$_ret               = Request::$r->_ret;
/* @var string[] $_entity */
$_entity            = Request::$r->_entity;
$hlpfile            = &Request::$r->hlpfile;
$_groupby           = Request::$r->_groupby;
$_amountfromop      = Request::$r->_amountfromop;
$_amounttoop        = Request::$r->_amounttoop;
$_numrec            = Request::$r->_numrec;
$_sortbylist        = Request::$r->_sortbylist;
$_includemebills    = Request::$r->_includemebills;
$_locationlist      = Request::$r->_locationlist;
$_payallentities    = Request::$r->_payallentities;
$_withability       = Request::$r->_withability ;
$_basebankonly      = Request::$r->_basebankonly;
$mod                = &Request::$r->mod;
$_oper              = Request::$r->_oper;
$_priority          = Request::$r->_priority;
$_vendsel           = Request::$r->_vendsel;
$_bodyarg           = Request::$r->_bodyarg;
$_billnumfrom       = Request::$r->_billnumfrom;
$_billnumto         = Request::$r->_billnumto;
$_releaseditemsonly = Request::$r->_releaseditemsonly;
$_defbank           = Request::$r->_defbank;
$_paysourceentity   = Request::$r->_paysourceentity;
$_vendtype          = Request::$r->_vendtype;
$_popup             = Request::$r->_popup;
$_amountto          = Request::$r->_amountto;
$_acctid            = &Request::$r->_acctid;
$_appliedcredit     = &Request::$r->_appliedcredit;
$_bankcurr          = Request::$r->_bankcurr;
$_bankname          = Request::$r->_bankname;
$_createdDate       = Request::$r->_createdDate;
$_creditsplit       = &Request::$r->_creditsplit;
$_ccSelector        = Request::$r->_ccSelector;
$_changedpaymethod  = Request::$r->_changedpaymethod;
$_default_paydate   = &Request::$r->_default_paydate;
$_descs             = Request::$r->_descs;
/* @var string[] $_didsplit */
$_didsplit          = &Request::$r->_didsplit;
$_discasof          = Request::$r->_discasof;
$_discdate          = &Request::$r->_discdate;
$_discount          = &Request::$r->_discount;
$_docnumbers        = Request::$r->_docnumbers;
$_duefrom           = Request::$r->_duefrom;
$_dueto             = Request::$r->_dueto;
$_entityName        = &Request::$r->_entityName;
$_exchRate          = Request::$r->_exchRate;
$_exchRateType      = Request::$r->_exchRateType;
$_hamount           = Request::$r->_hamount;
$_payasof           = Request::$r->_payasof;
$_paybaseRateType   = Request::$r->_paybaseRateType;
$_paydate           = &Request::$r->_paydate;
$_paymentoption     = Request::$r->_paymentoption;
$_paymethod         = &Request::$r->_paymethod;
$_paysourceobjectstoreid = &Request::$r->_paysourceobjectstoreid;
$_showsave          = &Request::$r->_showsave;
/* @var string[] $_splits */
$_splits            = &Request::$r->_splits;
$_rectype           = &Request::$r->_rectype;
$_selectBillsFor    = Request::$r->_selectBillsFor;
$_selected          = &Request::$r->_selected;
$_selectedrow       = Request::$r->_selectedrow;
$_selectedsplit     = &Request::$r->_selectedsplit;
$_total             = Request::$r->_total;
$_totalbaseselected = Request::$r->_totalbaseselected;
$_vendorval         = Request::$r->_vendorval;
$_whencreatedfrom   = Request::$r->_whencreatedfrom;
$_whencreatedto     = Request::$r->_whencreatedto;
$invoiceDate        = Request::$r->invoiceDate;
$pay                = Request::$r->pay;
$selectrow          = Request::$r->selectrow;
$selectrowhdr       = &Request::$r->selectrowhdr;
$_continue          = Request::$r->_continue;
$_save              = Request::$r->_save;
$_curr              = Request::$r->_curr;
$_sortby            = Request::$r->_sortby;
$_totalselected     = Request::$r->_totalselected;
$_paymentdesc       = Request::$r->_paymentdesc;
$_amountfrom        = Request::$r->_amountfrom;
$_basetotalselected = Request::$r->_basetotalselected;
$_defbankcurr       = &Request::$r->_defbankcurr;
$atlas              = &Request::$r->atlas;
$releaseditemsonly  = &Request::$r->releaseditemsonly;
$basecurr           = &Request::$r->basecurr;
$whencreated        = &Request::$r->whencreated;
$_do                = Request::$r->_do;
$_wfpmdeliverymethod = Request::$r->_wfpmdeliverymethod;

if ( Request::$r->_continue || Request::$r->_save ) {
    // CSRF validation for all valid posts of this page
    if ( !CsrfUtils::verifyInputToken(Request::$r->_op) ) {
        popupErrorAndShutdown(
            'BL03002133',
            __FILE__.":".__LINE__,
            "Invalid request. Please Retry.",
            "CSRF: Edit APPayment page csrf token mismatch",
            false
        );
    }
}
$basecurr = GetBaseCurrency();
$_paymentmethodfilter = Request::$r->_paymentmethodfilter;

$email_contact = Request::$r->_email_contact;

if(!empty($_paymentmethodfilter) && $_paymentmethodfilter != '-- SELECT --') {
    $_paymethod = $_paymentmethodfilter;
}

// ProcessErrorRetrievalAction is needed only when we enter the pay bills first time. We do not need it once the payment method is changed.
if( (!isset($_changedpaymethod) || $_changedpaymethod == '') && ((!isset($_paymethod) || $_paymethod == '') || $_paymethod == 'Printed Check') ) {
    if( !isset($_basebankonly) || $_basebankonly == ''  || $_currency == $basecurr || ($_currency != $basecurr && $_basebankonly == 'N') ) {
        ProcessErrorRetrievalAction();
    }
}
global $kAPid, $kEEid, $gElementMap, $gErr;

$gManagerFactory = Globals::$g->gManagerFactory;
list($user_rec,$cny) = explode('@', $_userid);

list($securityMod, $dummy) = explode('/', $gElementMap[$_op]['key']);

if ( $securityMod == 'ap' ) {
    $_mod = $kAPid;
} else if ( $securityMod == 'ee' ) {
    $_mod = $kEEid;
}

if(!isset($email_contact) || $email_contact == '' ) {
    $email_contact = GetPrefForPymtEmailNotification($_mod);
}

$start_entity = Request::$r->_start_entity;
$end_entity   = Request::$r->_end_entity;

$close_frame = "";

if ($_ret) {
    Ret();
}

$mcenabled = IsMCPSubscribed();
$atlas = (IsMCMESubscribed() && !GetContextLocation());
if ($atlas) {
    $_basebankonly = 'N';
}

// CAPTURE THE CREDIT LINE ITEM PAYMENT INFO
$crItemPayments    = array();
$creditArrayStrings = Request::$r->_crArray;
if (!empty($creditArrayStrings)) {
    foreach($creditArrayStrings as $invKey => $creditArrayStr) {
        if ( $creditArrayStr != '' ) {
            $lhsEval = new EvalTracker($invKey);
            $rhsEval = new EvalTracker($creditArrayStr . ";");
            $lhsStatus = $lhsEval->tokensAllowed('edit_appayment');
            $rhsStatus = $rhsEval->tokensAllowed('edit_appayment');
            if ( !$lhsStatus || !$rhsStatus ) {
                LogToFile($lhsEval . " \n " . $rhsEval, EVAL_LOGFILE);
                popupErrorAndShutdown(
                    'BL03002133',
                    __FILE__.":".__LINE__,
                    "Invalid request. Please Retry.",
                    "PHP eval: blocked execution of eval string",
                    false
                );
            } else {
                eval("\$crItemPayments[$invKey] = $creditArrayStr;");
            }
        }
    }
}

// ELIMINATE ZERO VALUES FROM THE MAP
if ( $crItemPayments != '' && count($crItemPayments) ) {
    $crItemPaymentsFormal = array();
    foreach($crItemPayments as $paidDocKey => $itemPayments) {
        foreach($itemPayments as $docItemKey => $pymtMap) {
            foreach($pymtMap as $crKey => $pymtItemMap) {
                foreach($pymtItemMap as $crItemKey => $pymtAmt) {
                    if ( $pymtAmt > 0 ) {
                        $crItemPaymentsFormal[$paidDocKey][$docItemKey][$crKey][$crItemKey] = $pymtAmt;
                    }
                }
            }
        }
    }
    $crItemPayments = $crItemPaymentsFormal;
}
switch( $_mod ){
case $kAPid:
    if ($_r) {
        if ($_do === 'edit') {
            $hlpfile = 'Editing_Payment_Request_Information';
        } else {
            $hlpfile = 'Viewing_Payment_Request_Details';
        }
    } else {
        $hlpfile = 'Selecting_Bills_to_Pay';
    }
    $label = I18N::getSingleToken('IA.BILL');
    if ($atlas) {
            $fieldlabels = array(I18N::getSingleToken('IA.VENDOR_ID'), I18N::getSingleToken('IA.VENDOR_NAME'), 'Payment Date', 'Payment<br>Currency', 'Amount Selected', 'Base<br>Currency', 'Memo');
    } elseif ($mcenabled) {
            $fieldlabels = array(I18N::getSingleToken('IA.VENDOR_ID'), I18N::getSingleToken('IA.VENDOR_NAME'), 'Payment Date', 'Payment<br>Currency', 'Amount Selected', 'Memo');
    } else {
            $fieldlabels = array(I18N::getSingleToken('IA.VENDOR_ID'), I18N::getSingleToken('IA.VENDOR_NAME'), 'Payment Date', 'Amount Selected', 'Memo');
    }
    $recordtype = 'pp';
    $recEntityType = 'appaymentrequest';
    $recEntityChildType = 'appaymentrequestitem';
    $modkey = $kAPid;
    $mod = 'ap';
    break;
case $kEEid:
    $hlpfile = 'Selecting_Expense_Reports_to_Pay';
    $label = I18N::getSingleToken( 'IA.EXPENSE');
    if ($atlas) {
        $fieldlabels = array(I18N::getSingleToken( 'IA.EMPLOYEE_ID'), I18N::getSingleToken( 'IA.EMPLOYEE_NAME'), I18N::getSingleToken( 'IA.PAYMENT_DATE'), I18N::getSingleToken( 'IA.PAYMENT_BR_CURRENCY'), I18N::getSingleToken( 'IA.AMOUNT_SELECTED'), I18N::getSingleToken( 'IA.BASE_BR_CURRENCY'), I18N::getSingleToken( 'IA.MEMO'));
    } elseif ($mcenabled) {
        $fieldlabels = array(I18N::getSingleToken( 'IA.EMPLOYEE_ID'), I18N::getSingleToken( 'IA.EMPLOYEE_NAME'), I18N::getSingleToken( 'IA.PAYMENT_DATE'), I18N::getSingleToken( 'IA.PAYMENT_BR_CURRENCY'), I18N::getSingleToken( 'IA.AMOUNT_SELECTED'), I18N::getSingleToken( 'IA.MEMO'));
    } else {
        $fieldlabels = array(I18N::getSingleToken( 'IA.EMPLOYEE_ID'), I18N::getSingleToken( 'IA.EMPLOYEE_NAME'), I18N::getSingleToken( 'IA.PAYMENT_DATE'), I18N::getSingleToken( 'IA.AMOUNT_SELECTED'), I18N::getSingleToken( 'IA.MEMO'));
    }
    $recordtype = 'ep';
    $recEntityType = 'eppaymentrequest';
    $recEntityChildType = 'eppaymentrequestitem';
    $mod = 'ee';
    $modkey = $kEEid;
    break;
}
if ( !isset($_paymethod) || $_paymethod == '' ) {
    /** @noinspection PhpUndefinedVariableInspection */
    $_paymethod = GetMyDefaultPayMethod($modkey);
    $_paymethod = ( $_paymethod ?: 'Printed Check' );
}



if ($_selectBillsFor == 'JP') {
    //Forced for Joint Payee
    $_paymethod = 'Printed Check' ;
}


if ($_continue || $_save) {
    if ( (isl_trim($_paymethod) == "Credit Card") || (isl_trim($_paymethod) == OutsourcedCheckUtils::OUTSOURCE_CC_METHOD) ) {
        $_acctid = $_ccSelector;
    }

    if (!$basecurr) {
        /** @noinspection PhpUndefinedVariableInspection */
        if ( $defbank == '~') {
            if ($_mod == $kAPid) {
                $errMsg = "You need to select a bank to pay the bills.";
                $errorCode = 'AP-0339';
            } else {
                $errMsg = "You need to select a bank to pay the expenses.";
                $errorCode = 'AP-0340';
            }
            $gErr->addError($errorCode, GetFL(), $errMsg);

            PrintErrorMessages();        
        }
    }

    if((!$basecurr || IsMCMESubscribed()) && !$_r && !$_currency) {
            if ($_mod == $kAPid) {
                $errMsg = "You need to select the currency to pay the bills.";
                $errorCode = 'AP-0341';
            } else {
                $errMsg = "You need to select the currency to pay the expenses.";
                $errorCode = 'AP-0342';
            }
            $gErr->addError($errorCode, GetFL(), $errMsg);
            PrintErrorMessages();
    }
}

if ($_defbank) {
    $arr = explode("~", $_defbank);
    $_acctid = $arr[0];
    $_bankname = $arr[2];
    $_defbankcurr = ($arr[4] ?: $basecurr);
}
$_defbankcurr = $_defbankcurr ?? $_bankcurr;
if ( (isl_trim($_paymethod) == 'Credit Card') || (isl_trim($_paymethod) == OutsourcedCheckUtils::OUTSOURCE_CC_METHOD) ) {
    $_defbankcurr = $_currency = $basecurr;
}

if(isset($_paysourceentity) && $_paysourceentity!='') {
    if(!isset($_paysourceobjectstoreid) || $_paysourceobjectstoreid=='') { 
        if(IsMultiEntityCompany() && !GetContextLocation()) {
            //
            // get all the target entities and their locations
            $entityMgr = $gManagerFactory->getManager('locationentity');
            //
            // get all the locations of the target entitites
            $trgentlocList = $entityMgr->GetEntityLocationMapWithIERel($_paysourceentity,  true, true);
            $trgentlocList = array_keys($trgentlocList);
            //		
            //
            $_paysourceobjectstoreid = 'PAY_SOURCE_TRGLOCS_'.mt_rand(0, 1000000);
            $objStoreValues = array( 'PROPERTY'        => $_paysourceobjectstoreid,
             'TYPE'            => 'Session',
             'OBJECTDATA'    => $trgentlocList,
                                );
        
            $objectStore = $gManagerFactory->getManager('objectstore');
            $ok = $objectStore->add($objStoreValues);
        }
    }
}

$source = 'edit_appayment';

//if the value is set, that means it is already in user format, change it to standard format regardless of how we get here. We could be 
//getting here from change of Pay Method or Continue and then Cancel.
if(isset($_discdate)) {
    if(!is_array($_discdate)) {
        $_discdate = FormatDateForStorage($_discdate);
    } else {
        foreach ($_discdate as $dkey => $ddate) {
            if ($ddate != '') {
                $_discdate[$dkey] = FormatDateForStorage($ddate);
            }
        }
    }
}

if(isset($_paydate)) {
    if (!is_array($_paydate)) {
        $_paydate = FormatDateForStorage($_paydate);
    } else {
        foreach ($_paydate as $pkey => $pdate) {
            if ($pdate != '') {
                $_paydate[$pkey] = FormatDateForStorage($pdate);
            }
        }
    }
}
if ( $_save ) {
    GetModulePreferences($kAPid, $preferences);
    XACT_BEGIN($source);
 
    // GROOM DATA FOR STORAGE
    if ( $_discount ) {
        foreach($_discount as $key => $val) {
            $_discount[$key] = str_replace(',', '', $val);
        }
    }
    foreach($_appliedcredit as $key => $val) {
        $_appliedcredit[$key] = str_replace(',', '', $val);
    }
    foreach($_selected as $key => $val) {
        $_selected[$key] = str_replace(',', '', $val);
    }
    foreach($_selected as $key => $val) 
    {   
        $_selected[$key] = str_replace(',', '', $val);
        if(!Util::php7eq0($_selected[$key])) {
            $billDate = FormatDateForStorage($invoiceDate[$key]);
            if (is_array($_paydate)) {
                $datepaid = ($_paydate[$key] != '') ? $_paydate[$key] : GetCurrentDate();
            }else {
                $datepaid = ($_paydate!='') ? $_paydate : GetCurrentDate();
            }

            if(($preferences['PYMTDT_ISGREATER'] == 'Y') && (SysDateCompare($datepaid, $billDate) < 0)) {                
                $gErr->addError('AP-0343', __FILE__ . '.' . __LINE__, "Payment date cannot be before Bill creation date");
                include_once'popuperror.phtml';
                 exit();
            }

            // to validate GL Posting to Future Periods
            if (!is_array($datepaid)) {
                if (!ValidateGLPostingDate($datepaid)) {
                    include_once'popuperror.phtml';
                    exit();
                }
            }
            if($billDate != '') {
                $whencreated[$key] = $billDate;
            }
            else{
                $whencreated[$key] = $_createdDate[$key];
            }

        } elseif (!($_appliedcredit[$key] <> 0)) {
            if (is_array($_paydate)) {
                unset($_paydate[$key]);
            } else {
                // if you are not paying an invoice with a payment or a credit, you don't need payment date
                unset($_paydate);
            }
        }

    }
    $selexratetypes = array();    
    foreach($_selected as $key => $val) {
        $_selected[$key] = str_replace(',', '', $val);

        // Getting only the selected bill's exchange rate type.
        if(!Util::php7eq0($_selected[$key])) {
            $selexratetypes[$key] = $_exchRateType[$key];
        }
    }

    $selectedSplitLoop = $_selectedsplit??[];
    foreach($selectedSplitLoop as $key => $val) {
        $_selectedsplit[$key] = str_replace(',', '', $val);
    }
    unset($selectedSplitLoop);

    $_creditsplitLoop = $_creditsplit??[];
    foreach($_creditsplitLoop as $key => $val) {
        $_creditsplit[$key] = str_replace(',', '', $val);
    }
    unset($_creditsplitLoop);

    if ($_r) {
        $hasbills = false;
        foreach( $_selectedrow as $key => $rowValue ) 
        {
            if(!isset($rowValue) || $rowValue == '') {
                unset($_rectype[$key]);
                unset($_entity[$key]);
                unset($_entityName[$key]);
                unset($_discount[$key]);
                unset($_discdate[$key]);
                unset($_appliedcredit[$key]);
                unset($_selected[$key]);
                unset($_creditsplit[$key]);    
                unset($_splits[$key]);
            } else {
                $hasbills = true;
            }
        }
        if(!$hasbills) {
            $gErr->addError('AP-0344', __FILE__ . '.' . __LINE__, "Payment request should have atleast one item.");
            include_once'popuperror.phtml';
            exit();
        }
    }

    /** @noinspection PhpUndefinedVariableInspection */
    $grpParams = array(
                'PAYMENTMETHOD'         => $_paymethod,
                'PAYMENTOPTION'         => $_paymentoption,
                'PRRECORDTYPE'          => $recordtype,
                'RECORDTYPE'            => $_rectype,
                'ENTITY'                => $_entity,
                'ENTITYNAME'            => $_entityName,
                'DISCOUNT'              => $_discount,
                'DISCOUNTDATE'          => $_discdate,
                'SELECTEDROW'           => $_selectedrow,
                'APPLIEDCREDIT'         =>  $_appliedcredit ,
                'SELECTEDAMOUNT'        => $_selected,
                'PAYMENTDATE'           => $_paydate,
                'DOCNUMBERS'            => $_docnumbers,
                'DESCRIPTION'           => $_descs,
                'ALLOWNEGATIVE'         => false,
                'RECORD#'               => $_r,
                'SUPDOCMAP'             => [],
                'DESCRIPTION2'          => '',
                'SELECTEDSPLIT'         => $_selectsplit,
                'CREDITSPLIT'           =>  $_creditsplit ,
                'INVOICECURR'           => $_currency,
                'PAYMENTCURR'           => $_defbankcurr,
                'PYMTEXCHRATETYPE'      => $_paybaseRateType,
                'BASETOTALSELECTED'     => $_basetotalselected,
                'PAYMENTDESC'           => $_paymentdesc,
                'WHENCREATED'           => $whencreated,      
               
                //'WFPMCHECKDELIVERYMETHOD' => $_wfpmdelivermethod,
        );
    if ($_r) {
        /** @noinspection PhpUndefinedVariableInspection */
        $grpParams['TOTALSELECTED'] = $_totalselected;
        $grpParams['BASETOTALSELECTED'] = $_totalbaseselected;
    }


        $ok = GroupPayments($payRequests, $grpParams);
        $payRequests['baseTotalSelectedMap'] = $grpParams['BASETOTALSELECTED'];

    // Wells Fargo Check Delivery Method
    if ($_paymethod == 'WF Check') {
        /** @noinspection PhpUndefinedVariableInspection */
        if ( $_wfpmdeliverymethod == 'S') {
            $dcode = '000';
        } elseif ($_wfpmdeliverymethod == 'O') {
            $dcode = '001';
        } else {
            $dcode = 'X';
        }
        foreach ($payRequests['ENTITIES'] as $v => $pymt) {

            foreach ($pymt['PAYMENTS'] as $key => $pdetail) {
                $payRequests['ENTITIES'][$v]['PAYMENTS'][$key]['DELIVERYMETHOD'] = $_wfpmdeliverymethod;
                $payRequests['ENTITIES'][$v]['PAYMENTS'][$key]['WFPMDELIVERYMETHOD'] = $dcode;
            }
        }
    }

    foreach ($payRequests['ENTITIES'] as $payVendor => $val) {
        $errVal = '';
        $errText = '';

        //To Check whether Do Not Cut Check Option is selected for vendor
        if ($val['DONOTCUTCHECK'] == 'T') {
            $errText = "has 'Don't pay' option checked";
        }

        if ($errText != '') {
            foreach ($val['PAYMENTS'] as $key => $value) {
                $errVal = $value['name'];
                $gErr->addIAError("AP-0656", __FILE__.":".__LINE__,
                sprintf('The %1$s \'%2$s\' %3$s', I18N::getSingleToken('IA.VENDOR'),
                $errVal, $errText),['VENDOR' => I18N::getSingleToken('IA.VENDOR'),
                'ERR_VAL' => $errVal, 'ERR_TEXT' => $errText]);
                XACT_ABORT($source);
                PrintErrorMessages();        
            }
        } else if (!$_r) {
            foreach ($val['PAYMENTS'] as $key => $payment) {
                $payRequests['ENTITIES'][$payVendor]['PAYMENTS'][$key]['EMAIL_CONTACT'] = $email_contact;
            }
        }

        if(isset($_locationlist) && $_locationlist != null) {
            $ll = APSelectToPay::includeSubLocationList($_locationlist);

            foreach($val['PAYMENTS'] as $payment=>$paymentInfo) {
                foreach($paymentInfo['BILLS'] as $key=>$bill) {
                    $payRequests['ENTITIES'][$payVendor]['PAYMENTS'][$payment]['BILLS'][$key]['ll'] = $ll;
                }
            }

        }
    }

    $releaseditemsonly = ( $_releaseditemsonly == 'Y' ? true : false );

    if ($_action != 'view') {
        ValidatePayMethod($_paymethod, $_currency);
        ValidatePaymentDate($_paymethod, $payRequests, $_currency, $_basebankonly);
        if ( $_mod == $kEEid and $_paymethod == 'ACH' and $_currency !== '' ) {
            PaymentUtils::validateACHBankFilePaymentsTE($_paymethod, $payRequests, $_currency, $_acctid);
        }
    }

    // IF THE USER HAS CUSTOM PAYMENT ITEM SPLITS, ADD THEM TO THE PAYMENT STRUCTURE	
    $noSplitFound = true;
    foreach(array_keys($_didsplit) as $entryKey) {
        if ( $_didsplit[$entryKey] == '' ) {
            unset($_didsplit[$entryKey]);
        }

        if ($noSplitFound && $_didsplit[$entryKey] != '' ) {
            $noSplitFound = false;
            // With SPLIT changes for allocations, for UI we generate splits and credit arrays
            // based on PRENTRY.line_nos - since the BL works on prentry.RECORD#
            // AddItemPaymentStruct is passed converted $_splits and $crItemPayments to make the // RECORD# based - transforms are distSplitLinesToRecnos/ distCrLinesToRecnos
            $splits        = distSplitLinesToRecnos($_splits, false, $releaseditemsonly);
            $crsplits    = distCrLinesToRecnos($crItemPayments, $releaseditemsonly);
            if (!AddItemPaymentStruct($payRequests, $splits, $crsplits, $_entity)) {
                XACT_ABORT($source);
                PrintErrorMessages();
            }
        }
    }

    if (!$payRequests) {
        XACT_ABORT($source);
        PrintErrorMessages();
    }

    //  Create search instance used by underlying functions for registering changed objects.
    //    Since we do not have an actual EntityManager object, set the entity name explicitly.
    global $gSearchInstance;
    $gSearchInstance = new SearchInstance(null, $recEntityChildType);
    $gSearchInstance->SetEntity($recEntityType);
    if ($_r) {
        if (!EditPayments($_r, $payRequests, $_acctid, $paykeys) ) {
            XACT_ABORT($source);
            PrintErrorMessages();
        }
        $gSearchInstance = 0;

        // COMMIT THE CHANGE AND RETURN.
        XACT_COMMIT($source);
        Ret();

    } else {
        $_acctid = isl_trim($_acctid);
        $payRequests['total'] = $_total;
        $payRequests['releaseditemsonly'] = $releaseditemsonly;
        if ($_currency == $basecurr && $_basebankonly != 'Y') {
            $exchRateTypeMgr = $gManagerFactory->getManager('exchangeratetypes');
            $payRequests['currency'] = $_defbankcurr;
            $payRequests['EXCH_RATE_TYPE_ID'] = $exchRateTypeMgr->GetExchangeRateTypeID($_paybaseRateType);
        }
        if (!CreatePayments($recordtype, $_paymethod, $payRequests, $_acctid, $paykeys) ) {
            $warnOnDups = Request::$r->_kNoWarn;
            $blockDups = Request::$r->_kNoDupl;
            
            XACT_ABORT($source);
            $cancelFlag = false;
            $_continue=true;
            if($warnOnDups != 'true' && HasErrors()) {
                include 'popuperror.phtml';
                exit();
            }
            if ($gErr->hasWarnings()) {
                
                if (in_array($_paymethod, array('Printed Check', 'WF Check', 'WF Domestic ACH', 'WF USD Wire'))) {
                    $_continue = false;
                    if ($_paymethod == 'Printed Check' && $_currency != $basecurr && $_basebankonly == 'Y') {
                        $_continue = true;
                    }
                } else {
                    $_continue = true;
                }
                $cancelFlag = true;
                $warnOnDups = true;
            }
        } else {
            // COMMIT THE CHANGE AND RETURN TO THE PARENT FRAME.
            XACT_COMMIT($source);
        }
        $gSearchInstance = 0;

    }

    if ($warnOnDups != 'true') {
        Request::$r->_kNoWarn = '';
        if(HasErrors()) {
            include 'popuperror.phtml';
            exit();
        }
        if ( Request::$r->_popup == 1 ) {
            print("<script>if ( top.window.opener ) { top.window.opener.location.reload(); } top.window.close();</script>");
        } else {    
            print("<script>parent.document.getElementById('reportFilter').rows = '*,0'; self.document.location.href='blankpage.html'; document.close();</script>");
        }    
        // Call Shutdown instead of exit
        Shutdown();            
    }
}
if ($_action == 'edit') {
    $selectrowhdr = true;
}

if ($_continue) {
    GetModulePreferences($kAPid, $preferences);

    $hlpfile = ($mod == 'ap') ? 'Bill_Payments_Requests_Summary_Lister' : 'Viewing_Reimbursement_Information';
    if ($_paymethod != 'Online') {
        $fieldlabels[] = I18N::getSingleToken( 'IA.DOCUMENT_NUMBER');
    }
    if (($basecurr != '' && $_currency != $basecurr && $_basebankonly == 'Y') || (!$basecurr && $_defbankcurr != $_currency)) {
        // $fieldlabels[] = 'Exchange Rate';
        $fieldlabels[] = I18N::getSingleToken('IA.AMOUNT_PAID_BY_BANK_NAME',[['name'=>'CURRENCY', 'value'=>$_defbankcurr]]);
    }
    $title = I18N::getSingleToken('IA.EXPENSE_PAYMENT_REQUESTS_SUMMARY',[['name' => 'EXPENSE','value' => $label]]);
    //$OnLoadFunc = "PrintOnLoad();" ;
    /** @noinspection PhpUndefinedVariableInspection */
    PrintCommonHtmlHeader(array( 'nocheck' => true, 'onloadjs' => $OnLoadFunc, 'leftpadding' =>10));
?>
<script src="../resources/js/base_lib.js"></script>
<?

    
    include_once 'process_status.inc';
    $statustitle = I18N::getSingleToken('IA.SELECTING_EXPENSES_PAY',[['name' => 'ENTITY', 'value' => $label]]);
    $statustext =  I18N::getSingleToken('IA.SELECTING_EXPENSES_PAY',[['name' => 'ENTITY', 'value' => $label]]);
    PrintStatusBarJavaScript($statustitle, $statustext, "payment", $_op);

    InitJSGlobals();
    ?>
	<script src="../resources/js/editor.js"></script>
	<script language=javascript>	
	function PrintOnLoad() {
		
	}
	</script>
<?php
    echo HelpContentEngine::getHelpContentJavaScript(array( 'helpid' => $hlpfile ));
    $_popupValue = util_encode($_popup);
    $_modValue = util_encode($mod);
    $_opValue = util_encode($_op);
?>


    <form name=lo action="<? echo GoUrl("edit_appayment.phtml?.mod=$_modValue&.op=$_opValue&.popup=$_popupValue"); ?>" method=post>
    <?
    //If there is a warning then show the warning and decide to continue or not based on the response
    global $gErr;
    if ($gErr->hasWarnings()) {
        include_once "warnings.phtml";
    ?>
				 <input type="hidden" name="displayWarning" value="false"/>
    <?
    } 
    ?>
<input type="hidden" name='.sess' value="<? echo $_sess; ?>">
<INPUT type="hidden" name="_kNoWarn" value="<? echo Request::$r->_kNoWarn; ?>">
<INPUT type="hidden" name="_kNoDupl" value="<? echo Request::$r->_kNoDupl; ?>">
<input type="hidden" name=".done" value="<?=insertDoneUnEnc($_done)?>">
<input type="hidden" name=".r" value="<?=$_r?>">
<input type="hidden" name=".do" value="<?=$_do?>">
<input type="hidden" name=".paymentoption" value="<?=$_paymentoption?>">
<input type="hidden" name=".paymethod" value="<?=$_paymethod?>">
<input type="hidden" name=".defbank" value="<? echo URLCleanParams::insert('.defbank', $_defbank); ?>">
<input type="hidden" name=".ccSelector" value="<? echo $_ccSelector; ?>">
<input type="hidden" name=".acctid" value="<?=$_acctid?>">
<input type="hidden" name=".currency" value="<?=$_currency?>">
<input type="hidden" name=".basebankonly" value="<?=$_basebankonly?>">
<input type="hidden" name=".releaseditemsonly" value="<?=$_releaseditemsonly?>">
<input type="hidden" name=".bankcurr" value="<?=util_encode($_bankcurr)?>">
<input type="hidden" name=".paybaseRateType" value="<?=$_paybaseRateType?>">
<input type="hidden" name=".op" value="<? echo $_op; ?>">
<input type="hidden" name=".processed" value="0">
<input type="hidden" name=".cancel" value="">
<input type="hidden" name="_save" value="">
<input type="hidden" name=".selecttopay" value="true">
<input type="hidden" name='.readtime' value="<? echo ServeCurrentTimestamp(); ?>">
<input type="hidden" name=".start_entity" value="<?=URLCleanParams::insert('.start_entity', $start_entity)?>">
<input type="hidden" name=".end_entity" value="<?=$end_entity?>">
<input type="hidden" name=".vendorval" value="<?=$_vendorval?>">
<input type="hidden" name=".vendsel" value="<?=$_vendsel?>">
<INPUT type="hidden" name="hlp" value='<?=GetHelpFile()?>'>
<input type="hidden" name="selectrowhdr" value="<?=$selectrowhdr?>">
<INPUT type="hidden" name=".paysourceentity" value='<?=$_paysourceentity?>'>
<INPUT type="hidden" name=".paysourceobjectstoreid" value='<?=$_paysourceobjectstoreid?>'>
<INPUT type="hidden" name=".paymentmethodfilter" value='<?=$_paymentmethodfilter?>'>
<INPUT type="hidden" name=".email_contact" value="<?=htmlentities($email_contact, ENT_COMPAT)?>">
<?php echo CsrfUtils::generateCsrfTokenInput(Request::$r->_op); ?>

    <?
    $_discdateLoop = $_discdate ??[];
    foreach ($_discdateLoop as $dkey => $ddate) {
        if ($ddate != '') {
            $_hiddenDiscdate[$dkey] = FormatDateForDisplay($ddate);
        }
    }
    unset($_discdateLoop);
    $createdDate = array();
    foreach ($_selected as $key => $val) {
        $_selected[$key] = str_replace(',', '', $val);
        if (!Util::php7eq0($_selected[$key])) {
            $createdDate[$key] = FormatDateForStorage($invoiceDate[$key]);
        }
    }
    /** @noinspection PhpUndefinedVariableInspection */
    SetHiddenUIVariables($_rectype, $_entity, $_entityName, $_discount, $_hiddenDiscdate, $_appliedcredit, $_selected, $_paydate, $_splits, $_didsplit, $creditArrayStrings, $_exchRate, $_exchRateType, $_curr, $selectrow, $pay, $createdDate);

    /** @noinspection PhpUndefinedVariableInspection */
    $grpParams = array(
                'PAYMENTMETHOD'         => $_paymethod,
                'PAYMENTOPTION'         => $_paymentoption,
                'PRRECORDTYPE'            => $recordtype,
                'RECORDTYPE'            => $_rectype,
                'ENTITY'                => $_entity,
                'ENTITYNAME'            => $_entityName,
                'DISCOUNT'              => $_discount,
                'DISCOUNTDATE'          => $_discdate,
                'APPLIEDCREDIT'         => $_appliedcredit,
                'SELECTEDAMOUNT'        => $_selected,
                'PAYMENTDATE'           => $_paydate,
                'DOCNUMBERS'            => $_docnumbers,
                'DESCRIPTION'           => $_descs,
                'ALLOWNEGATIVE'         => false,
                'RECORD#'               => $_r,
                'SUPDOCMAP'             => [],
                'DESCRIPTION2'          => '',
                'SELECTEDSPLIT'         => $_selectsplit,
                'CREDITSPLIT'           => $_creditsplit,
                'INVOICECURR'           => $_currency,
                'PAYMENTCURR'           => $_defbankcurr,
                'PYMTEXCHRATETYPE'      => $_paybaseRateType,
                 'BASETOTALSELECTED'    => $_basetotalselected,
                'WHENCREATED'           => $whencreated
    //                'WFPMCHECKDELIVERYMETHOD' => $_wfpmdelivermethod,
        );

    $payRequests = array();
        $ok = GroupPayments($payRequests, $grpParams);

    if (!$ok) {
        PrintErrorMessages();
    }

    foreach($_selected as $key => $val) 
    {   
        $_selected[$key] = str_replace(',', '', $val);
        if (!Util::php7eq0($_selected[$key])) {
            $val = FormatDateForStorage($invoiceDate[$key]);
            if (is_array($_paydate)) {
                $datepaid = ($_paydate[$key] != '') ? $_paydate[$key] : GetCurrentDate();
            }else {
                $datepaid = ($_paydate!='') ? $_paydate : GetCurrentDate();
            }

            if(($preferences['PYMTDT_ISGREATER'] == 'Y') && (SysDateCompare($datepaid, $val) < 0)) {                
                $gErr->addError('AP-0345', __FILE__ . '.' . __LINE__, "Payment date cannot be before Bill creation date");
                include_once'popuperror.phtml';
                 exit();
            }

        }

    }

    $optsbuttons = array(
    array('save' , 'onclick="javascript:launchStatus();"'),
    array('customcancel', 'Cancel', "this.form.elements['.cancel'].value=1;")
    );

    htmlHeaderBar($title, $gray_msg_text, $buttons, $optsbuttons);?>
		<br>
        <div class="ia_content_body">
		<table class="field_list_data" cellpadding=1 cellspacing=0 border=0 width="100%">
		<tr class="multiline_header_bg">

    <? for ($iFld=0; $iFld < count($fieldlabels); $iFld++) { ?>
			<td align="center" valign="middle" wrap>
				<? echo (((($basecurr != '' && $_currency != $basecurr && $_basebankonly == 'Y') || (!$basecurr && $_defbankcurr != $_currency)) && $fieldlabels[$iFld] == "Amount Paid<br>By Bank ($_defbankcurr)") ? '<font color="red">*' : ''); ?><b><?=I18N::getSingleToken($fieldlabels[$iFld]);?></b><? echo (($_currency != $basecurr && $_basebankonly == 'Y' && $fieldlabels[$iFld] == 'Base Amount Selected') ? '</font>' : ''); ?>
			</td>
    <? 
} ?>
		</tr>
		<tr bgcolor="#000000"><td colspan="10" height="1"></td></tr>
    <? if ($mod == 'ap') { 
        // Build the Map for AccountID field, Use this AccountID field as a Default value for Memo 
        $vendorAccts = QueryResult(array("select entity, accountno from vendor where cny# = :1", GetMyCompany()));
        foreach ($vendorAccts as $key => $vendorAcct ) {
            $vendorAcctMap[$vendorAcct['ENTITY']] = $vendorAcct['ACCOUNTNO'] ;
        }
}

        $trclass = "multiline_bg_beige";
foreach ($payRequests['ENTITIES'] as $key => $prec) {
    foreach ($prec['PAYMENTS'] as $pkey => $pprec) {
        if ($trclass == "multiline_bg_white") {
            $trclass = "multiline_bg_beige";
        } else {
            $trclass = "multiline_bg_white";
        }
        $indexKey = ($prec['PAYMENTOPTION'] == 'bill') ? $prec['PAYMENTS'][$pkey]['BILLS'][0]['record#'] : $key;?>

     <tr class="<?=$trclass?>">
      <td align="center" valign="middle" wrap>
        <?=isl_substr($key, 1);?>
      </td>
      <td align="center" valign="middle" wrap>
        <?=$prec['PAYMENTS'][$pkey]['name'];?>
      </td>
      <td align="center" valign="middle" wrap>
        <?=FormatDateForDisplay($prec['PAYMENTS'][$pkey]['paydate']);?>
      </td>
        <? if ($mcenabled) { ?>
					<td align="center" valign="middle" wrap>
        <?=$prec['PAYMENTS'][$pkey]['currency']?>
					</td>
        <? 
} ?>
      <td align="center" valign="middle" wrap>
        <?=glFormatCurrency($prec['PAYMENTS'][$pkey]['trx_totalselected']);?>
      </td>
        <? if ($atlas) { ?>
					<td align="center" valign="middle" wrap>
        <?=$prec['PAYMENTS'][$pkey]['basecurr'];?>
					</td>
        <? 
} ?>
      <td align="center" valign="middle" wrap>
        <? if($mod == 'ap') { ?> 
						<input onchange="<?= /** @noinspection PhpUndefinedVariableInspection */
                        $gWarnOnSaveJS?>" type=text name=".descs[<?=$indexKey?>]" value="<?= /** @noinspection PhpUndefinedVariableInspection */
                        $vendorAcctMap[$key];?>" SIZE=20 MAXLENGTH=20>
        <? 
} else { ?>
						<input onchange="<?= /** @noinspection PhpUndefinedVariableInspection */
                        $gWarnOnSaveJS?>" type=text name=".descs[<?=$indexKey?>]" value="" SIZE=20 MAXLENGTH=20>
        <? 
} ?>
      </td>
        <? if ($_paymethod != 'Online') { ?>
					<td align="center" valign="middle" wrap>
						<input onchange="<?=$gWarnOnSaveJS?>" type=text name=".docnumbers[<?=$indexKey?>]" value="<?= $_docnumbers[$indexKey]; ?>" SIZE=10 MAXLENGTH=15>
					</td>
        <? 
} ?>
        <? if (($basecurr != '' && $_currency != $basecurr && $_basebankonly == 'Y') || (!$basecurr && $_defbankcurr != $_currency)) { ?>
					<td align="center" valign="middle" wrap>
        <?
        $_params = array();
        $_params['varname'] = ($prec['PAYMENTOPTION'] == 'bill' ? ".basetotalselected[$indexKey]" : ".basetotalselected[$indexKey][$pkey]");
        $_params['value'] = $_basetotalselected[$indexKey][$pkey];
        $_params['type']['size'] = 20;
        $_params['maxlength'] = 20;
        $_params['onchange'] = $gWarnOnSaveJS;
        $_params['required'] = 1;
        $_params['roundcurrency'] = 1;
        $_params['nocalc'] = 1;
        $ctrl = new DecimalControl($_params);
        $ctrl->Show();
        ?>
					</td>
        <? 
} ?>
     </tr>
    <? 
    } ?>
    <? //} ?>
    <? 
} ?>
		</table>
			
    <?
    htmlButtonFooter("", $buttons, $optsbuttons);
    echo '</div>';

    htmlFooter($title);

    // Call Shutdown
    Shutdown();        
}

if ($_do=='del') {

    //  Create search instance used by underlying functions for registering changed objects.
    //    Since we do not have an actual EntityManager object, set the entity name explicitly.
    global $gSearchInstance;
    $gSearchInstance = new SearchInstance(null, $recEntityChildType);
    $gSearchInstance->SetEntity($recEntityType);

		if (!DeletePayments($_r, DELETE_FOR_DELETE) ){
			PrintErrorMessages();
		}
		$gSearchInstance = 0;
	}
	//CLASS MAP
	
	// INITIALIZE MODULE SPECIFIC PARAMETERS ------------------------------------------------------------//
	if ($_r) {
        $auditTrailSession = AuditTrailSession::getInstance();
        try {
            $auditTrailSession->addAuditEvent('appayment', $_r, AuditTrail::AUDITTRAIL_EVENT_ACCESS);
        } catch (IAException $exception) {
            LogToFile("Audit entry failed. " . $exception->getMessage() . ' ' . __FILE__.'.'.__LINE__);
        }
	}
	// Do we show 'Save' button or 'Continue' button?
	$_showsave = ((( $_r || in_array($_paymethod, array('Printed Check', 'WF Check', 'WF Domestic ACH', 'WF USD Wire', 'ACH', OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD, OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD))) && $_action != 'view') ? true : false );
	if ($basecurr != '' && !$_r && $_paymethod == 'Printed Check' && $_currency != $basecurr && $_basebankonly == 'Y') {
		$_showsave = false;
	} elseif ($basecurr == '' && !$_r && $_paymethod == 'Printed Check' && $_currency != $_defbankcurr) {
		$_showsave = false;
	}
	// if ($atlas && (!$_currency || !$_defbank)) $_showsave = false;

    // Do we even show 'Save' or 'Continue' button?
    $show = true;

	if ($_r) {
        $title = ($_action == 'view' ?  I18N::getSingleToken( 'IA.VIEW_PAYMENT_REQUEST')  :  I18N::getSingleToken( 'IA.EDIT_PAYMENT_REQUEST')) ;
    } else {
        if ($_mod == "3.AP") {
            $title = I18N::getSingleToken('IA.PAY_BILLS');
        } else {
            $title = I18N::getSingleToken( 'IA.SELECT_TO_PAY');
        }
    }

    $onload = "if ( parent.frames[0] && parent.frames[0].unfreezeControls ) { parent.frames[0].unfreezeControls(); }";
    $onload .= $_mod == "3.AP" ? "if(document.getElementById('divtable')) { InitdivtableHt(document.getElementById('divtable').offsetHeight); }" : '';// Init height of the divtable.
    $customCSS = '<link href="../resources/css/lister-style.css" rel="stylesheet" type="text/css">';
    $customJS = '';
    $className = '';

    
    $customCSS .= '<link href="../resources/thirdparty/yui/css/container.css" rel="stylesheet" type="text/css">';
    $customJS .= '<script language="JavaScript" src="../resources/thirdparty/yui/yuiloader.js"></script>';
    $customJS .= '<script language="JavaScript" src="../resources/thirdparty/yui/event.js"></script>';
    $customJS .= '<script language="JavaScript" src="../resources/thirdparty/yui/dom.js"></script>';
    $customJS .= '<script language="JavaScript" src="../resources/thirdparty/yui/animation.js"></script>';
    $customJS .= '<script language="JavaScript" src="../resources/thirdparty/yui/container-min.js"></script>';
    $customJS .= '<script language="JavaScript" src="../resources/thirdparty/yui/connection-min.js"></script>';
    $className = 'yui-skin-sam';

    $params = array( 
                    'nojs'        => true,
                    'customCSS'    => $customCSS,
                    'onloadjs'  => $onload,
                    'leftpadding' => 10,
                    'includeCustomJS' => $customJS,
                    'className' => $className
                );
    PrintCommonHtmlHeader($params);
    InitJSGlobals();
    ?>
	<script src="../resources/js/editor.js"></script>
	<script src="../resources/js/polyfill/promise.js"></script>
	<script src="../resources/js/qrequest.js"></script>
	<SCRIPT src="../resources/js/jefunctions.js"></script>
    <?
    require_once 'js_common.inc';
    ?>
	<script src="../resources/js/billdrill.js"></script>
	<script src="../resources/js/selecttopay.js"></script>
    <?
    require_once 'process_status.inc';
    require_once 'js_dates.inc';
    showJsDates();
    require_once 'js_auto_dates.inc';
    showJsAutoDates();

    echo HelpContentEngine::getHelpContentJavaScript(array( 'helpid' => $hlpfile ));

    $statustitle = "Selecting ".$label."s to Pay";
    $statustext = "Selecting ".$label."s to Pay...";
    PrintStatusBarJavaScript($statustitle, $statustext, "payment", $_op);

    // SPIT THE FORM HANDLE WHEN SAVE IS FINISHED
    echo "$close_frame";
    $_popupValue = util_encode($_popup);
    $_modValue = util_encode($mod);
    $_opValue = util_encode($_op);
    ?>
    <form name=lo action="<? echo GoUrl("edit_appayment.phtml?.mod=$_modValue&.op=$_opValue&.popup=$_popupValue"); ?>" onsubmit="baseTransferAction();"
          method=post
    <?    if ($_paymethod == 'Online') {
        $forma = GetUserDateFormat();?>
		onsubmit="return checkScPayDatesAndSubmit(this, '<?=$forma?>');"
        <? if ($gErr->hasWarnings()) { ?>		
				<input type="hidden" name="displayWarning" value="false"/>
    <? 
} ?>
    <? 
} ?>
	>
    <?    list($start_entity,$start_entity_name) = explode('--', $start_entity);
    list($end_entity,$end_entity_name) = explode('--', $end_entity);
    ?>
	<script> var stpUrl = document.forms[0].action; </script>
	
    <?
    if ($gErr->hasWarnings()) {
            
        include_once "warnings.phtml";
    ?>
				 <input type="hidden" name="displayWarning" value="false"/>
    <?
    } 
    ?>
	<input type="hidden" name='.sess' value="<? echo $_sess; ?>">
	<input type="hidden" id='.changed' value="false">
	<input type="hidden" name=".done" value="<?=insertDoneUnEnc($_done)?>">
	<input type="hidden" name=".r" value="<?=util_encode($_r)?>">
	<input type="hidden" name=".do" value="<?=util_encode($_do);?>">
    <input type="hidden" name=".bodyarg" value="<? echo util_encode($_bodyarg); ?>">
	<input type="hidden" name=".op" value="<? echo $_op; ?>">
	<input type="hidden" name=".defbank_old" value="<? echo util_encode($_defbank); ?>">
	<input type="hidden" name=".prevpaymethod" value="<?=util_encode($_paymethod)?>">
	<input type="hidden" name=".vendtype" value="<?=util_encode($_vendtype)?>">
	<input type="hidden" name=".start_entity" value="<?=util_encode(URLCleanParams::insert('.start_entity', $start_entity))?>">
	<input type="hidden" name=".end_entity" value="<?=util_encode($end_entity)?>">
	<input type="hidden" name=".billnumfrom" value="<?=util_encode(URLCleanParams::insert('.billnumfrom', $_billnumfrom))?>">
	<input type="hidden" name=".billnumto" value="<?=util_encode($_billnumto)?>">
	<input type="hidden" name=".whencreatedfrom" value="<?=util_encode($_whencreatedfrom)?>">
	<input type="hidden" name=".whencreatedto" value="<?=util_encode($_whencreatedto)?>">
	<input type="hidden" name=".duefrom" value="<?=util_encode($_duefrom)?>">
	<input type="hidden" name=".dueto" value="<?=util_encode($_dueto)?>">
	<input type="hidden" name=".amountfrom" value="<?= /** @noinspection PhpUndefinedVariableInspection */
    util_encode($_amountfrom)?>">
	<input type="hidden" name=".amountto" value="<?=util_encode($_amountto)?>">
	<input type="hidden" name=".amountfromop" value="<?=util_encode($_amountfromop)?>">
	<input type="hidden" name=".amounttoop" value="<?=util_encode($_amounttoop)?>">
	<input type="hidden" name=".currency" value="<?=util_encode($_currency)?>">
	<input type="hidden" name=".bankcurr" value="<?=util_encode($_bankcurr)?>">
	<input type="hidden" name=".basebankonly" value="<?=util_encode($_basebankonly)?>">
	<input type="hidden" name=".releaseditemsonly" value="<?=util_encode($_releaseditemsonly)?>">
	<input type="hidden" name=".oper" value="<?=util_encode($_oper)?>">
	<input type="hidden" name=".vendorval" value="<?=util_encode($_vendorval)?>">
	<input type="hidden" name=".vendsel" value="<?=util_encode($_vendsel)?>">
	<input type="hidden" name=".payasof" value="<?=util_encode($_payasof)?>">
	<input type="hidden" name=".discasof" value="<?=util_encode($_discasof)?>">
	<input type="hidden" name=".priority" value="<?=util_encode($_priority)?>">
	<input type="hidden" name=".sortby" value="<?=util_encode($_sortby)?>">
	<input type="hidden" name=".sortbylist" value="<?=util_encode($_sortbylist)?>">
	<input type="hidden" name=".showsave" value="<?=util_encode($_showsave)?>">
	<input type="hidden" name=".processed" value="0">
	<input type="hidden" name=".groupby" value="<?=util_encode($_groupby)?>">
	<input type="hidden" name=".payallentities" value="<?=util_encode($_payallentities)?>">
	<input type="hidden" name=".locationlist" value="<?=util_encode(URLCleanParams::insert('.locationlist', $_locationlist))?>">
	<input type="hidden" name=".withability" value="<?=util_encode($_withability)?>">
	<input type="hidden" name=".includemebills" value="<?=util_encode($_includemebills)?>">
	<input type="hidden" name=".numrec" value="<?=util_encode($_numrec)?>">
	<input type="hidden" name=".readtime" value="<? echo ServeCurrentTimestamp(); ?>">
	<INPUT type="hidden" name="hlp" value='<?=GetHelpFile()?>'>
	<INPUT type="hidden" name=".changedpaymethod" value='<?=util_encode($_changedpaymethod)?>'>
	<INPUT type="hidden" name=".paysourceentity" value='<?=util_encode($_paysourceentity)?>'>
	<INPUT type="hidden" name=".paysourceobjectstoreid" value='<?=util_encode($_paysourceobjectstoreid)?>'>
    <input type="hidden" name=".selectBillsFor" value="<?=util_encode($_selectBillsFor)?>">
	<INPUT type="hidden" name=".paymethod" value='<?=util_encode($_paymethod)?>'>
	<INPUT type="hidden" name=".paymentmethodfilter" value='<?=util_encode($_paymentmethodfilter)?>'>
    <INPUT type="hidden" name=".email_contact" value="<?=util_encode($email_contact)?>">

    <?php echo CsrfUtils::generateCsrfTokenInput(Request::$r->_op); ?>

    <?php

    $arguments = [
        '_paymethod'      => $_paymethod,
        '_mode'           => ($_action === 'edit') ? 'edit' : 'create',
        '_mod'            => $mod,
        '_groupby'        => $_groupby,
        '_locationlist'   => $_locationlist,
        '_withability'    => $_withability,
        '_payallentities' => $_payallentities,
        '_includemebills' => $_includemebills,
        '_selectBillsFor' => $_selectBillsFor,
    ];

    if (!$_r) {
        $arguments['appliedCreditMap'] = $creditArrayStrings;
    }

    if ($mod == 'ap') {
        $page = new APSelectToPay($arguments);
    } else {
        $page = new SelectToPay($arguments);
    }
    $jsTokens = ['IA.FILTER','IA.CUSTOMIZE','IA.SELECT_ALL_ENTITIES'];
    $textJSON = json_encode(I18N::getTokensForArray(I18N::tokenArrayToObjectArray($jsTokens)));
    ?>
            <script>
                var textMap = <?= $textJSON ?>;
            </script>
            <?php
    $stparr = [  // Fields to keep persistance after view event in Select To Pay screen.
        'r'                   => $_r,
        'numrec'              => $_numrec,
        'default_paydate'     => $_default_paydate,
        'vendtype'            => $_vendtype,
        'start_entity'        => $start_entity,
        'end_entity'          => $end_entity,
        'billnumfrom'         => $_billnumfrom,
        'billnumto'           => $_billnumto,
        'discasof'            => $_discasof,
        'payasof'             => $_payasof,
        'whencreatedfrom'     => $_whencreatedfrom,
        'whencreatedto'       => $_whencreatedto,
        'duefrom'             => $_duefrom,
        'dueto'               => $_dueto,
        'amountfrom'          => $_amountfrom,
        'amountto'            => $_amountto,
        'amountfromop'        => $_amountfromop,
        'amounttoop'          => $_amounttoop,
        'currency'            => $_currency,
        'priority'            => $_priority,
        'sortby'              => $_sortbylist,
        'ccSelector'          => $_ccSelector,
        'oper'                => $_oper,
        'vendorval'           => $_vendorval,
        'vendsel'             => $_vendsel,
        'basebankonly'        => $_basebankonly,
        'releaseditemsonly'   => $_releaseditemsonly,
        'bankcurr'            => $_bankcurr,
        'paysourceentity'     => $_paysourceentity,
        'includemebills'      => $_includemebills,
        'selectBillsFor'      => $_selectBillsFor,
        'paymentdesc'         => $_paymentdesc,
        'paymethod'           => $_paymethod,
        'paymentmethodfilter' => $_paymentmethodfilter,
    ];

    // $table = $page->GetTable($_numrec, $_default_paydate, $_vendtype, $start_entity, $end_entity, $_billnumfrom, 
    // $_billnumto, $_discasof, $_payasof, $_whencreatedfrom, $_whencreatedto, $_duefrom, $_dueto, $_amountfrom, $_amountto, 
    // $_amountfromop, $_amounttoop, $_currency, $_priority, $_sortbylist, $_ccSelector, $_r);
    $table = $page->GetTable($stparr);    // Fields to keep persistance after view event in Select To Pay screen.
    if ($_mod == "3.AP") {
        // Remember user preferences only for Select to Pay, not for Select to Reimburse.
        $overridestparr = array('whencreatedfrom', 'whencreatedto', 'duefrom', 'dueto');
        foreach ($stparr as $key => $value) {
            if (in_array($key, $overridestparr)) {
                $stpparamstostore[$key] = FormatDateForStorage($value);
            } else {
                $stpparamstostore[$key] = $value;
            }
        }
        // Remember the user preferences for $_numrec (it is not a mem report)
        /** @noinspection PhpUndefinedVariableInspection */
        UserReportPrefCommit($stpparamstostore);        // Fields to keep persistance after view event in Select To Pay screen.
    }

    /*
    if ($_r && $table[0]['CURRENCY'] == $basecurr && $_currency != $basecurr) {
    // we do not allow editing
    $_showsave = false;
    $_action = 'view';
    }
    */

    if ($_showsave) {
        if( $_r ) {
            if($_mod == '3.AP') {
                $optsbuttons = array(
                array('save' , 'onClick="javascript: if(processPaymentBeforeSubmit()){ launchStatus();} else { return false; }"', 'onsubmit="javascript: processBeforeSubmit(); return false;"'),array('ret'));
            } else if($_mod == '6.EE') {
                $optsbuttons = array(
                array('save' , 'onClick="javascript: if(processReimburseBeforeSubmit()){ launchStatus();} else { return false; }"', 'onsubmit="javascript: processBeforeSubmit(); return false;"'),array('ret'));
            }
        } else{
            $optsbuttons = array(
                array('save' , 'onClick="javascript: launchStatus(); "', 'onsubmit="javascript: processBeforeSubmit(); return false;"'),
                array('ret')
            );
        }
    } elseif ($_action == 'view') {
        $optsbuttons = array(
        array('ret')
        );
    } else {
        $optsbuttons = array(
        array('continue', 'onclick="javascript: this.value = 1; processBeforeSubmit(); return true;"'),
        array('ret'),
        );
    }

    if( $_r ) {
        $showtable = true;
        htmlHeaderBar($title, $gray_msg_text, $buttons, $optsbuttons, false, false, $showtable);
        if($showtable) { ?>
         <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
          <TR class="multiline_header_bg"><TD height="5px"></TD></TR>
          <TR><TD height="6px"></TD></TR>
         </TABLE>
        <? 
        }
    } else {
        $pagingText = '';
        $showingCount = count($table);
        if ($showingCount > 0) {
            $recType = ( $_mod == '3.AP' ? I18N::getSingleToken( 'IA.BILLS') : I18N::getSingleToken( 'IA.EXPENSES') );
            $pagingText = ' ' . I18N::getSingleToken( 'IA.ONE_TO_COUNT_OF_TOTAL_RECORD_TYPE',[
                    ['name' => 'COUNT', 'value' => $showingCount],
                    ['name' => 'TOTAL', 'value' => $page->_countRecs],
                    ['name' => 'RECORD_TYPE', 'value' => $recType]
                ]) . ' ';
        }
        $show = $show && ( !$table ? false : true );

        $buttons = array();
        if($_mod == '3.AP' && ($page->_paymethod == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD || $page->_paymethod == OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD)){
            $businessNumDays = $page->_paymethod == OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD ? 5 : 4;
            $page->DefinePaymentDateEstimator($businessNumDays);
            $buttons['-1'] = array('name' => 'Estimate Delivery', 'onclick' => "javascript:DisplayPaymentDateEstimator(this, 'paymentdateestimatordiv', 'PaymentDateEstimatorApprovalDate', 'PaymentDateEstimatorEstimatedDate', $businessNumDays);",
                'show' => $show, 'id' => 'paymentdeliveryestbtn', 'width' => '20%');
        }

        $buttons['0'] = array('name' => ($_mod == '3.AP' ? I18N::getSingleToken( 'IA.FILTER') : I18N::getSingleToken( 'IA.CUSTOMIZE')), 'onclick' => "javascript:toggleButton();",
            'show' => 1, 'id' => 'customizeLink');

        GetModulePreferences($kAPid, $preferences);
        
        $setCurrChangeFlag = '';
        if(IsMCPSubscribed()) {
            $setCurrChangeFlag = "setCurrChangeFlag();";
        }
        $paymentRequestMessage = I18N::getSingleToken( 'IA.PROCESSING_PAYMENT_REQUESTS_PLEASE_WAIT');
        $onclick = "warnexceed=" . ($preferences['WARNONEXCEED'] ?: 0) . ";if(warnexceed && (Number(document.lo.elements['.total'].value) > Number(document.getElementById('balval').value)) && !confirm('Total amount entered exceeded funds available in the bank'))" . "{ return false; } else { javascript: document.forms[0].elements['.save'].value=1; $setCurrChangeFlag DisplayLoadingPanel('".$paymentRequestMessage."'); processAction(); } ";

        if ( $_showsave ) {
            $buttons['1'] =  array('name' => I18N::getSingleToken( 'IA.SAVE'),
            'onclick' => $onclick, 
            'id' => 'action_button', 
            'show'=>$show,
            'hrefId'=>'topHref',
                                );
        } else {
            $buttons['1'] =  array('name' => I18N::getSingleToken( 'IA.CONTINUE'),
            'onclick' => "$setCurrChangeFlag javascript: processContinue();",
            'id' => 'action_button', 
            'show'=>$show,
            'hrefId'=>'topHref',
                                );
        }
        if ($gErr->hasWarnings() && $cancelFlag) {
            $buttons['2'] =  array('name' => I18N::getSingleToken( 'IA.CANCEL'),
            'onclick' => "$setCurrChangeFlag javascript: ProcessCancel();",
            'id' => 'action_button', 
            'show'=>$show,
                                    'disableonsubmit'=>'true',
            );
        }

        PrintControlBand($buttons, $title, $pagingText, true);
        ?>
   <TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
    <TR class="multiline_header_bg"><TD height="5px"></TD></TR>
    <TR><TD height="6px"></TD></TR>
   </TABLE>
    <?  // Extra div so embedded tables are correct width with css styling on ia_content_border.
    // See http://mikeage.net/2005/08/12/full-width-tables-in-ie6/
    ?>
   <div class="ia_content_border"><div style="width: 100%;">
    <?
     echo "<input type=hidden name='".( $_showsave ? ".save" : ".continue" )."' value=''>";
    }

    if (!$table) {
        echo "<script> if ( document.getElementById('action_button') ) { document.getElementById('action_button').display='none'; } </script>";
        echo "<br/><b>No " . $label . "s found meeting your criteria. </b>";
        exit();
    }

    if (HasErrors()) {
        PrintErrorMessages();
    }

    // Removed creditArrayStrings parameter: the call to GetTable sets this properly when editing a payment request, 
    // and we do not reuse the information when the form is submitted. 
    $page->SetUIFields($_paymentoption, $_discount, $_discdate, $_selected, $_appliedcredit, $_paydate, $_hamount, $pay, $_splits, $_didsplit, $selectrow, $_total, $selectrowhdr);

    $page->PrintJavascript();

    $_default_paydate = ( $_default_paydate != '' ? FormatDateForStorage($_default_paydate) : GetCurrentDate() );

    if (!$page->PrintPage($_default_paydate, $_defbank, $_r, $_ccSelector, $table, $_paymentmethodfilter,  $_selectBillsFor) ) {
        PrintErrorMessages();
    }

    // PRINT FOOTER.
    if( $_r ) {
        htmlButtonFooter("", $buttons, $optsbuttons);
    } else {
        $buttons['1']['hrefId']='bottomHref';
        PrintControlBand($buttons, '', $pagingText, false);
    }
    
?>
</div></div>
</form>
<script> if ( parent.frames[0] && parent.frames[0].unfreezeControls ) { parent.frames[0].unfreezeControls(); } </script>
</body>
</html>
