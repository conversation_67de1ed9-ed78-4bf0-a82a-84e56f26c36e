<?

/**
*    FILE:            ARRevalReport.cls
*    AUTHOR:            <PERSON><PERSON>
*    DESCRIPTION:
*
*    (C)2000, Intacct Corporation, All Rights Reserved
*
*    Intacct Corporation Proprietary Information.
*    This document contains trade secret data that belongs to Intacct
*    corporation and is protected by the copyright laws. Information herein
*    may not be used, copied or disclosed in whole or part without prior
*    written consent from Intacct Corporation.
*/


import('APARRevalReport.cls');

class ARRevalReport extends APARRevalReport
{

    /** @var string[] AR_TOKENS */
    private const AR_TOKENS
        = [
            'IA.GL_ACCOUNT_PASCAL_CASE', 'IA.CUSTOMER', 'IA.LOCATION'
        ];
    
    /**
     * @param array $params
     */
    function __construct($params)
    {
        parent::__construct(
            INTACCTarray_merge(
                $params,
                array(
                    'report' => 'arreval',
                    'ops' => array("ar/reports/arreval"),
                    '2stage' => 'true',
                    'mod' => 'ar'
                )
            )
        );
    }

    /**
     * @return bool
     */
    function DoQuery()
    {
        //To validate the Inputs
        if(!$this->ValidateInputs()) {
            return false;
        }
        $cnt  = 0;
        $stmt = [];
        $stmt[$cnt++] = '';
        $stmt[$cnt++] = GetMyCompany();
        //To set filters
        $filters = '';

        $revalExchType = $this->params['EXCHRATETYPE'];
        $revalExchTypeOrg = $this->params['EXCHRATETYPE_ORIG'];
        $revalDate     = FormatDateForStorage($this->params['REVALDATE']);
        $baseCurrency  = GetBaseCurrency();
        if(!$baseCurrency && IsMCMESubscribed())
        {
            $location = explode('--',$this->params['LOCATION'][0]);
            $baseCurrency= GetLocationBaseCurrency($location[0]);
        }
        $this->baseCurrency = $baseCurrency;

        $stmt[$cnt++]  = $revalExchType;
        $stmt[$cnt++]  = $revalDate;
        $stmt[$cnt++]  = $baseCurrency;
        if ($this->params['FROMCUSTOMER']) {
            list($custid) = explode(PICK_RECVAL_SEP, isl_trim($this->params['FROMCUSTOMER']));
            $filters .= " AND ENTITY.CUSTOMERID >= :".$cnt++;
            $stmt[]   = $custid;
        }
        if ($this->params['TOCUSTOMER']) {
            list($custid) = explode(PICK_RECVAL_SEP, isl_trim($this->params['TOCUSTOMER']));
            $filters .= " AND ENTITY.CUSTOMERID <= :".$cnt++;
            $stmt[]   = $custid;
        }
        if ($this->params['CUSTOMERTYPE']) {
            list($custtypeid) = explode(PICK_RECVAL_SEP, isl_trim($this->params['CUSTOMERTYPE']));
            $filters .= " AND ENTITYOBJECT.NAME = :".$cnt++;
            $stmt[]  = $custtypeid;
        }
        if ($this->params['CURRENCY']) {
            $currency = $this->params['CURRENCY'];
            $filters .= " AND PRENTRY.CURRENCY = :".$cnt++;
            $stmt[]   = $currency;
        }
        $stmt[] = $revalExchTypeOrg;

        if ($revalExchType && $revalDate) {
            if ($revalExchType == "Intacct Daily Rate") {
                
                //Updating Intacct Rate
                IntacctRate::getRatesForAllCurrencies($baseCurrency, array($revalDate));
                
                $exchFrom = ", (SELECT RATE, 
								FROMCURRENCY FROM_CURRENCY, TOCURRENCY, EXCHDATE, 'Intacct Daily Rate' TYPE_NAME
							FROM   
								INTACCTRATES
							WHERE 
								CNY# = :1 AND EXCHDATE = :3
								AND TOCURRENCY = :4) EXCHANGERATE";
            }
            else  {
                $exchFrom = ", (SELECT 
								E1.*
							FROM   
								V_EXCHANGERATES E1,
								 (SELECT 
									FROM_CURRENCY, MAX(EFFECTIVE_START_DATE) AS EFFECTIVE_START_DATE 
								 FROM 
									V_EXCHANGERATES E1
								 WHERE 
									E1.CNY# = :1 AND EFFECTIVE_START_DATE <= :3 									 
									AND TYPE_NAME= :2 
									AND TO_CURRENCY = :4 
								 GROUP BY 
									FROM_CURRENCY) E2
							WHERE 
								E1.CNY# = :1 
								AND E1.FROM_CURRENCY = E2.FROM_CURRENCY 
								AND E1.TO_CURRENCY = :4
								AND E1.EFFECTIVE_START_DATE = E2.EFFECTIVE_START_DATE) EXCHANGERATE"; 
            }

            $exchSel = "EXCHANGERATE.RATE ";
            $exchWhere = "AND EXCHANGERATE.TYPE_NAME(+) = :2
						  AND PRENTRY.CURRENCY = EXCHANGERATE.FROM_CURRENCY(+)";
        }
        else  {
            global $gErr;
            $gErr->addError("AR-0117", GetFL(), "You have to select both Revaluation As of Date & Revaluation Exchange Rate Types");
            return false;
        }

        //To set filters for Mega Companies
        $this->BuildMegaFilters($filterDepartment, $filterLocation);

        //To set Condition for Reporting period or Start date/End date given
        /*
        $startDate = FormatDateForStorage($this->params['START_DATE']);
        $endDate = FormatDateForStorage($this->params['END_DATE']);

        if ( $startDate == '' || $endDate == '' ) {
        $periods = GetNObjects('glbudgettype', 'start_date, end_date, datetype', " NAME = '".$this->params['PERIOD']."' ");
        $period = $periods[0];
        GetReportingDateRange($period, $this->params['ASOFDATE'], $startDate, $endDate);
        }
        $filters .= " AND PRRECORD.WHENCREATED >='$startDate' AND PRRECORD.WHENCREATED <= '$endDate'";
        */

        if($this->params['BASEDONDATE'] == 'D') {
            $filters .= " AND PRRECORD.WHENCREATED <= :3";
            $docOrGLdate = " PRRECORD.WHENCREATED POSTDATE, ";
        }
        else {
            $filters .= " AND PRBATCH.CREATED <= :3";
            $docOrGLdate = " PRBATCH.CREATED POSTDATE, ";
        }

        // To Set the select for Detail mode
        // UPDATED FOR SUMMARY MODE
        //if ($this->params['SHOWMODE'] == 'D') {
        $accountlabel = GetLabelStatus('ap');
        $acctTable = 'BASEACCOUNTMST ACCOUNTTABLE';
        $acctSelect = "ACCOUNTTABLE.ACCT_NO||'--'|| ACCOUNTTABLE.TITLE ";
        $acctWhere = '';
        if ($accountlabel) {
            $acctTable .= ',ACCOUNTLABEL';
            $acctSelect = "(case when (PRENTRY.ACCOUNTLABELKEY IS NULL)
									 then (ACCOUNTTABLE.ACCT_NO||'--'|| ACCOUNTTABLE.TITLE)
									  else ACCOUNTLABEL.LABEL end) ";
            $acctWhere = "AND PRENTRY.CNY# = ACCOUNTLABEL.CNY# (+) AND
								  PRENTRY.ACCOUNTLABELKEY = ACCOUNTLABEL.RECORD# (+)";
        }
        $detailSelect = " ,PRRECORD.WHENCREATED AS DATEENTERED, PRRECORD.WHENDUE AS DUEDATE,
						(PRENTRY.AMOUNT
						- (select nvl(sum(prp.amount), 0)
							from prentrypymtrecs prp
							where prp.cny# = :1
							and prp.paiditemkey = PRENTRY.record#
							and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
							and prp.state='C')
						+ (select nvl(sum(prp.amount), 0)
							from prentrypymtrecs prp
							where prp.cny# = :1
							and prp.payitemkey = PRENTRY.record#
							and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
							and prp.state='C')) AS AMOUNT,
					(PRENTRY.TRX_AMOUNT
						- (select nvl(sum(prp.trx_amount), 0)
							from prentrypymtrecs prp
							where prp.cny# = :1
							and prp.paiditemkey = PRENTRY.record#
							and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
							and prp.state='C')
						+ (select nvl(sum(prp.trx_amount), 0)
							from prentrypymtrecs prp
							where prp.cny# = :1
							and prp.payitemkey = PRENTRY.record#
							and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
							and prp.state='C')) AS DETTRANSAMOUNT,
					$acctSelect AS ACCOUNT, PRENTRY.EXCHANGE_RATE AS EXCHRATE,
					PRRECORD.RECORDID AS BILL#, PRRECORD.RECORDTYPE AS RECORDTYPE";
        $detailFrom = " ,$acctTable";
        $detailWhere = " $acctWhere AND PRENTRY.CNY# = ACCOUNTTABLE.CNY#  AND
							PRENTRY.ACCOUNTKEY = ACCOUNTTABLE.RECORD#";
        $detailOrder = " , ACCOUNT, DATEENTERED, RECORDTYPE, BILL#";
        //}

        //If AUTOPOSTDRAFTJE is enabled then get GLoffset account
        if (postDraftJEReval::$isPostJEEnabled) {
            APARRevalReport::$aparPRTypeForReval = '1';
        }

        // To Set the select for Group mode
        $groupOrder = '';
        $locSelect = '';
        $locFrom = '';
        $locWhere = '';
        $prentry = "PRENTRY ";
        if ( $filterLocation != '' ) {
            $prentry = "PRENTRYMST ";
        }
        if ($this->params['GROUPMODE'] == 'LOCATION--GL ACCOUNT' 
            || $this->params['GROUPMODE'] == 'GL ACCOUNT--LOCATION'
        ) {
            $locSelect = " ,LOCATION.LOCATION_NO AS LOCID,LOCATION.LOCATION_NO||'--'||LOCATION.NAME AS LOCNAME";
            $locFrom = " ,LOCATION";
            $locWhere = " AND PRENTRY.CNY# = LOCATION.CNY# (+) AND
						PRENTRY.LOCATION# = LOCATION.RECORD# (+)";
            $groupOrder = ($this->params['GROUPMODE'] == 'LOCATION--GL ACCOUNT')? "LOCID, " :"ACCOUNT, ";
            // $prentry = "PRENTRY ";
        }
        if($this->params['GROUPMODE'] == 'CUSTOMER--GL ACCOUNT' 
            || $this->params['GROUPMODE'] == 'GL ACCOUNT--CUSTOMER'
        ) {
            $groupOrder = ($this->params['GROUPMODE'] == 'CUSTOMER--GL ACCOUNT')? "ENTITYID, " :"ACCOUNT, ";
        }

        $retainageWhere = "";
        if (CRESetupManager::isARRetainageEnabled()) {

            // note similar code in APRevalReport.cls
            $retainageWhere = " and not exists (
                                    select 1
                                    from retainagerelease rr, retainagereleaseentry rre 
                                    where rr.cny# = rre.cny# and rr.record# = rre.rrkey and rr.state = 'R'
                                    and rre.cny# = prrecord.cny# and rre.released_prrecordkey = prrecord.record#)";
        }

        $qry = " SELECT * FROM (SELECT
						PRRECORD.ENTITY AS ENTITYID, PRENTRY.CURRENCY AS CURRENCY, "
               . $docOrGLdate .
               "(PRENTRY.TRX_AMOUNT
							- (select nvl(sum(nvl(prp.trx_amount,0)), 0)
								from prentrypymtrecs prp
								where prp.cny# = :1
								and prp.paiditemkey = PRENTRY.record#
								and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
								and prp.state='C')
							+ (select nvl(sum(nvl(prp.trx_amount,0)), 0)
								from prentrypymtrecs prp
								where prp.cny# = :1
								and prp.payitemkey = PRENTRY.record#
								and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
								and prp.state='C')) AS TRANSAMOUNT,
						(PRENTRY.AMOUNT
							- (select nvl(sum(prp.amount), 0)
								from prentrypymtrecs prp
								where prp.cny# = :1
								and prp.paiditemkey = PRENTRY.record#
								and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
								and prp.state='C')
							+ (select nvl(sum(prp.amount), 0)
								from prentrypymtrecs prp
								where prp.cny# = :1
								and prp.payitemkey = PRENTRY.record#
								and prp.paymentdate <= to_date(:3, 'mm/dd/yyyy')
								and prp.state='C')) AS BASEAMOUNT,
						ENTITY.CUSTOMERID||'--'||ENTITY.NAME AS ENTITYNAME, ENTITYOBJECT.NAME AS ENTITYTYPE,
                        ENTITY.CUSTOMERID,
						$exchSel AS REVALEXCHRATE,  :$cnt AS TYPE,
						PRENTRY.RECORD#  
						$locSelect $detailSelect 
					FROM
						PRRECORDMST PRRECORD, PRBATCHMST PRBATCH, $prentry PRENTRY, CUSTOMERMST ENTITY,
						CUSTTYPE ENTITYOBJECT $exchFrom $locFrom $detailFrom
					WHERE
						PRRECORD.CNY# = :1 AND
						PRBATCH.CNY# = PRRECORD.CNY# AND
						GREATEST(NVL(PRRECORD.WHENPAID, TO_DATE(:3,'MM/DD/YYYY')), NVL(PRRECORD.MAXPAYACTDATE, '01/01/1970')) >= TO_DATE(:3,'MM/DD/YYYY') AND
						PRRECORD.PRBATCHKEY = PRBATCH.RECORD# AND
						PRRECORD.RECORDTYPE IN ('ri', 'ra') AND
						PRRECORD.STATE in ( " . PRINVOICE_POSTED_STATES . " ) AND
						PRRECORD.CNY# = PRENTRY.CNY# AND
						PRRECORD.RECORD# = PRENTRY.RECORDKEY AND
						PRENTRY.LINEITEM = 'T' AND
						PRENTRY.AMOUNT<>0 AND
						PRENTRY.CURRENCY <> :4 AND
						PRRECORD.CNY# = ENTITY.CNY# AND
						PRRECORD.ENTITY=ENTITY.ENTITY AND
						ENTITY.CNY# = ENTITYOBJECT.CNY#(+) AND
						ENTITY.CUSTTYPEKEY = ENTITYOBJECT.RECORD#(+)
						$exchWhere $locWhere $detailWhere $retainageWhere $filters $filterDepartment $filterLocation
					ORDER BY
						$groupOrder CURRENCY $detailOrder)
			WHERE TRANSAMOUNT <> 0 ";
        $stmt[0] = $qry;
        $this->rawdata = QueryResult($stmt);

        return true;
    }

    /**
     * @return array
     */
    function DoMap()
    {
        $reportdata = [];
        if (empty($this->rawdata) || $this->rawdata == '') {
            $reportdata['NODATA']['0'] = array('NODATA' => '1');
        }

        //Establish the Report Header
        $lines = $this->ReportHeader();

        if (empty($this->rawdata) || $this->rawdata == '') {
            $lines['report']["0"]['ITEM'] = $reportdata;
            return $lines;
        }

        //Establish the Body
        if (!empty($this->rawdata)) {
            $this->ReportBody($lines, $this->rawdata);
        }

        return $lines;
    }

    /**
     * this function handles setting up the basic report header
     *
     * @return array
     */
    function ReportHeader()
    {
        $lines = parent::ReportHeader();
        $tokens = I18N::getTokensForArray(I18N::tokenArrayToObjectArray(self::AR_TOKENS));

        $groupmode = str_replace('GL ACCOUNT', GT($tokens, 'IA.GL_ACCOUNT_PASCAL_CASE'), $this->params['GROUPMODE']);
        $groupmode = str_replace('CUSTOMER', GT($tokens, 'IA.CUSTOMER'), $groupmode);
        $groupmode = str_replace('LOCATION', GT($tokens, 'IA.LOCATION'), $groupmode);

        $grouparray = explode("--", $groupmode);
        $grpHdr = $grouparray[0];
        $grpHdr2 = $grouparray[1];

        $lines['report']['0']['GROUPHDR1'] = $grpHdr;
        $lines['report']['0']['GROUPHDR2'] = $grpHdr2 ;
        $lines['report']['0']['ARREVAL'] = 'true';

        return $lines;
    }

    /**
     * Added to support the new Grouping.
     *
     * @param array $lines
     * @param array $rawdata
     *
     * @return array
     */
    function ReportBody(&$lines, $rawdata)
    {
        $_mod = Request::$r->_mod;

        //To get Report data and some parameters
        $group = array();
        $members = array();
        $transTotalAmount = 0;
        $baseTotalAmount = 0;
        $revalBaseTotalAmount = 0;
        $grandDR = 0;
        $grandCR = 0;
        $prevDate = $rawdata[0]['POSTDATE'];
        $prevCur = $rawdata[0]['CURRENCY'];
        $curPointer = -1;
        $grouping = $this->params['GROUPMODE'];
        $grparray = explode("--", $grouping);
        $grpName  = $grparray[0];
        $grpName2 = $grparray[1];
        $exchFormat = "%.8f";

        $postDraftJEReval = new postDraftJEReval();
        $postDraftJEReval->assignBatchInformation($this->params, $this->baseCurrency);
        $postDraftJEReval->buildGlOffsetArray($rawdata);

        if($grpName == 'GL ACCOUNT') {
            $prevGrp = $rawdata[0]['ACCOUNT'];
            $hdr1 = 'ACCOUNT';
            $Name = 'ACCOUNT';
        } else {
            $grpName = ($grpName == 'LOCATION')?'LOC':'ENTITY';
            $prevGrp = $rawdata[0][$grpName.'ID'];
            $hdr1 = $grpName.'ID';
            $Name = $grpName.'NAME' ;
        }
        $Name1 = '';
        if ($grpName2 == 'GL ACCOUNT') {
            $Name1 = 'ACCOUNT';
        } else if ($grpName2 == 'LOCATION' || $grpName2 == 'CUSTOMER') {
            $grpName2 = ($grpName2 == 'LOCATION')?'LOC':'ENTITY';
            $Name1 = $grpName2.'NAME' ;
        }
        foreach ($rawdata as $row) {
                $row["REVALBASEAMOUNT"] = ibcmul($row["TRANSAMOUNT"], $row["REVALEXCHRATE"], 2, true);
            
            if ($row[$hdr1]!=$prevGrp || $row['CURRENCY']!=$prevCur || $row['POSTDATE']!=$prevDate) {
                $drcr = ibcsub($revalBaseTotalAmount,$baseTotalAmount);
                // $drcr = $revalBaseTotalAmount!==null? ibcsub($revalBaseTotalAmount,$baseTotalAmount):'';
                $grandDR += ($_mod == 'ap') ? (($drcr>0)?$drcr:0) : (($drcr<0)?ibcabs($drcr):0);
                $grandCR += ($_mod == 'ap') ? (($drcr<0)?ibcabs($drcr):0) : (($drcr>0)?$drcr:0);

                //To Store previous group information
                $group[] = array(
                    'GROUP'            => ($rawdata[$curPointer][$Name] != '--' ) ? $rawdata[$curPointer][$Name] : 'None',
                    'POSTDATE'        => FormatDateForDisplay($rawdata[$curPointer]['POSTDATE']),
                    'CURRENCY'        => $rawdata[$curPointer]['CURRENCY'],
                    'TRANSTOTALAMOUNT'    => $transTotalAmount,
                    'BASEAMOUNT'        => $baseTotalAmount,
                    'REVALEXCHRATE'        => sprintf($exchFormat, $rawdata[$curPointer]['REVALEXCHRATE']),
                    'TYPE'            => $rawdata[$curPointer]['TYPE'],
                    'REVALBASEAMOUNT'    => $revalBaseTotalAmount,
                    'DR'            => ($_mod == 'ap') ? (($drcr>0)?$drcr:'') : (($drcr<0)?ibcabs($drcr):''),
                    'CR'            => ($_mod == 'ap') ? (($drcr<0)?ibcabs($drcr):'') : (($drcr>0)?$drcr:''),
                    'MEMBERS'        => $members
                );
                $members = array();
                $prevGrp = $row[$hdr1];
                $prevDate = $row['POSTDATE'];
                $prevCur = $row['CURRENCY'];
                $transTotalAmount = 0;
                $baseTotalAmount = 0;
                $revalBaseTotalAmount = 0;
            }
            $drcr = ibcsub(ibcmul($row["TRANSAMOUNT"], $row["REVALEXCHRATE"], 2, 1),$row['AMOUNT']);
            $members[] = array(
                'GROUP2'        => $row[$Name1] ?? '',
                'DATE'            => FormatDateForDisplay($row['DATEENTERED']),
                'AMOUNT'        => $row['AMOUNT'],
                'TRANSAMOUNT'        => $row['DETTRANSAMOUNT'],
                'BILLNO'        => $row['BILL#'],
                'DUEDATE'        => FormatDateForDisplay($row['DUEDATE']),
                'EXCHANGE_RATE'        => sprintf($exchFormat, $row['EXCHRATE']),
                'REVALAMOUNT'        => $row["REVALBASEAMOUNT"],
                'DR'            => ($_mod == 'ap') ? (($drcr>0)?$drcr:'') : (($drcr<0)?ibcabs($drcr):''),
                'CR'            => ($_mod == 'ap') ? (($drcr<0)?ibcabs($drcr):'') : (($drcr>0)?$drcr:''),
            );
            $transTotalAmount = ibcadd($transTotalAmount,$row["TRANSAMOUNT"]);
            $baseTotalAmount = ibcadd($baseTotalAmount, $row["BASEAMOUNT"]);
            $revalBaseTotalAmount = ibcadd($revalBaseTotalAmount,$row["REVALBASEAMOUNT"]);
            $curPointer++;
            $postDraftJEReval->formatRowDataForRevalReport($row, $drcr, $_mod);
        }

        if (!$postDraftJEReval->createIMSJobForRevalReport($this->title)) {
            return [];
        }

        //To Store last group information
        if (count($members)!=0) {
            $drcr = ibcsub($revalBaseTotalAmount,$baseTotalAmount);
            // $drcr = $revalBaseTotalAmount!==null? ibcsub($revalBaseTotalAmount,$baseTotalAmount):'';
            $grandDR += ($_mod == 'ap') ? (($drcr>0)?$drcr:0) : (($drcr<0)?ibcabs($drcr):0);
            $grandCR += ($_mod == 'ap') ? (($drcr<0)?ibcabs($drcr):0) : (($drcr>0)?$drcr:0);

            $group[] = array(
                'GROUP'            => ($rawdata[count($rawdata)-1][$Name] != '--' ) ?$rawdata[count($rawdata)-1][$Name] : 'None',
                'POSTDATE'        => FormatDateForDisplay($rawdata[count($rawdata)-1]['POSTDATE']),
                'CURRENCY'        => $rawdata[count($rawdata)-1]['CURRENCY'],
                'TRANSTOTALAMOUNT'    => $transTotalAmount,
                'BASEAMOUNT'        => $baseTotalAmount,
                'REVALEXCHRATE'        => sprintf($exchFormat, $rawdata[$curPointer]['REVALEXCHRATE']),
                'TYPE'            => $rawdata[$curPointer]['TYPE'],
                'REVALBASEAMOUNT'    => $revalBaseTotalAmount,
                'DR'            => ($_mod == 'ap') ? (($drcr>0)?$drcr:'') : (($drcr<0)?ibcabs($drcr):''),
                'CR'            => ($_mod == 'ap') ? (($drcr<0)?ibcabs($drcr):'') : (($drcr>0)?$drcr:''),
                'MEMBERS'        => $members
            );
        }
        $lines['report']["0"]['SHOWDETAIL'] = ($this->params['SHOWMODE'] == 'D')?'true':'false';
        $lines['report']["0"]['GRANDDR'] = $grandDR;
        $lines['report']["0"]['GRANDCR'] = $grandCR;
        $lines['report']["0"]['NET'] = $grandCR - $grandDR;
        $lines['report']['0']['REVAL'] = $group;

        return ($lines);
    }
}
