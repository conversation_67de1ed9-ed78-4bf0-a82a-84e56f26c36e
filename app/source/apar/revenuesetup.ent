<?php

/**
 * revenuesetup.ent
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation, All Rights Reserved
 */

global $gBooleanType;

$kSchemas['revenuesetup'] = array(
    'schema'    => array(
        // Configure basic
        // Accounts Receivable setup
        'ENABLEREVREC'              => 'enablerevrec',
        'RRG_ACCOUNT'               => 'rrg_account',
        'RR_JOURNAL'                => 'rr_journal',
        'EDITDRS'                   => 'edit_drs',

        // Order Entry setup
        'ENABLEREVREC_OE'           => 'dummy',
        'EDITREVRECSCHEDULE'        => 'dummy',
        'USEFULFILLMENT'            => 'dummy',

        // Configure Advance
        'ORVSOEPRICE'               => 'dummy',
        'DEFAULTONEBUNDLE'          => 'dummy',

        // configure contract
        'ENABLE_REVREC_CONTRACTS'       => 'enable_revrec_contracts',
        'MEA_HANDLEOPENENTRIES_DEFAULT' => 'default_mea_handleopenentries',
        'USEFULFILLMENT_CONTRACTS'      => 'usefulfillment_contracts',
        'ENABLE_EXPENSE_CONTRACTS'      => 'enable_expense_contracts',
        'FVAMOUNTBASEDON'               => 'fvamountbasedon',
        'TRACKTIMESHEETVALUEFLAG'       => 'tracktimesheetvalueflag',
        'REV_REC_ON_INVOICE_FLAG'     => 'rev_rec_on_invoice_flag'
    ),
    'fieldinfo' => array(
        // Configure Accounts Receivable setup
        array(
            'fullname'  => 'IA.ENABLE_REVENUE_RECOGNITION_FOR_ACCOUNTS_RECEIVABLE',
            'type'      => array(
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => array('IA.STANDARD_REVENUE_RECOGNITION','IA.NO_REVENUE_RECOGNITION',),
                'validvalues'   => array('true', 'false'),
                '_validivalues' => array('T', 'F'),
            ),
            'default' => 'false',
            'path'      => 'ENABLEREVREC',
            'showlabelalways'   => true
        ),
        array(
            'fullname'  => 'IA.DEFAULT_DEFERRED_REVENUE_ACCOUNT',
            'type'      => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'glaccountpick',
                'entity'        => 'glaccount',
            ),
            'path'      => 'RRG_ACCOUNT'
        ),
        array(
            'fullname'  => 'IA.REVENUE_RECOGNITION_JOURNAL',
            'type'      => array(
                'maxlength'     => 24,
                'ptype'         => 'ptr',
                'type'          => 'ptr',
                'pickentity'    => 'gljournalpick',
                'entity'        => 'gljournal',
            ),
            'path'      => 'RR_JOURNAL'
        ),
        array(
            'fullname'  => 'IA.EDIT_REVENUE_SCHEDULES',
            'type'      => array(
                'ptype'         => 'boolean',
                'type'          => 'boolean',
                'validvalues'   => array('true', 'false'),
                '_validivalues' => array('T', 'F'),
                'validlabels'   => array('IA.TRUE', 'IA.FALSE'),
            ),
            'path'      => 'EDITDRS'
        ),

        // Configure Order Entry setup
        array(
            'fullname'  => 'IA.ENABLE_REVENUE_RECOGNITION_FOR_ORDER_ENTRY',
            'desc'      => 'IA.ENABLE_REVENUE_RECOGNITION_FOR_ORDER_ENTRY',
            'type'      => array(
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => array('IA.STANDARD_REVENUE_RECOGNITION','IA.ADVANCED_REVENUE_RECOGNITION_INCLUDES_MEA','IA.NO_REVENUE_RECOGNITION',),
                'validvalues' => array('S', 'A', 'N'),
            ),
            'default' => 'N',
            'path'      => 'ENABLEREVREC_OE',
            'showlabelalways'   => true
        ),
        array(
            'path'       => 'EDITREVRECSCHEDULE',
            'fullname'   => 'IA.EDIT_REVENUE_SCHEDULES',
            'desc'       => 'IA.EDIT_REVENUE_SCHEDULES',
            'type'       => $gBooleanType,
            'renameable' => true,
        ),
        array(
            'path'     => 'USEFULFILLMENT',
            'fullname' => 'IA.EVENTBASED_RECOGNITION',
            'desc'     => 'IA.EVENT_BASED_RECOGNITION',
            'type'     => $gBooleanType,
        ),

        array(
            'path'       => 'ORVSOEPRICE',
            'fullname'   => 'IA.OVERRIDE_THE_DEFAULT_FAIR_VALUE_PRICE_LIST_ON_TRAN',
            'desc'       => 'IA.OVERRIDE_THE_DEFAULT_FAIR_VALUE_PRICE_LIST_ON',
            'type'       => $gBooleanType,
            'renameable' => true,
        ),
        array(
            'path'       => 'DEFAULTONEBUNDLE',
            'fullname'   => 'IA.AUTOMATICALLY_INCLUDE_ALL_TRANSACTION_ITEMS_IN_MEA',
            'desc'       => 'IA.AUTOMATICALLY_INCLUDE_ALL_TRANSACTION_ITEMS_IN',
            'type'       => $gBooleanType,
            'renameable' => true,
        ),
        array(
            'path'      => 'ENABLE_REVREC_CONTRACTS',
            'fullname'  => 'IA.REVENUE_RECOGNITION',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'radio',
                'maxlength' => 1,
                'size' => 1,
                'validlabels' => array('IA.STANDARD_REVENUE_RECOGNITION','IA.ADVANCED_REVENUE_RECOGNITION_INCLUDES_MEA','IA.NO_REVENUE_RECOGNITION',),
                'validvalues' => array('S', 'A', 'N'),
                'default' => 'S',
            ),
            'showlabelalways'   => true
        ),
        [
            'path'            => 'MEA_HANDLEOPENENTRIES_DEFAULT',
            'fullname'        => 'IA.MEA_OPEN_ENTRIES_HANDLING_LABEL',
            'type'            => [
                'ptype'       => 'radio',
                'type'        => 'radio',
                'maxlength'   => 1,
                'size'        => 1,
                'validlabels' => ['IA.POST_THE_REVENUE_ON_THE_SCHEDULED_POSTING_DATE','IA.CHANGE_THE_SCHEDULED_POSTING_DATE_TO_EQUAL_THE',],
                'validvalues' => [
                    ContractUtil::MEA_OPEN_ENTRIES_HANDLING_POST_V,
                    ContractUtil::MEA_OPEN_ENTRIES_HANDLING_CHANGE_DATE_V,
                ],
                '_validvalues' => [
                    ContractUtil::MEA_OPEN_ENTRIES_HANDLING_POST_D,
                    ContractUtil::MEA_OPEN_ENTRIES_HANDLING_CHANGE_DATE_D,
                ],
                'default'     => ContractUtil::MEA_OPEN_ENTRIES_HANDLING_POST_V,
            ],
            'showlabelalways' => true,
        ],
        array(
            'path'     => 'USEFULFILLMENT_CONTRACTS',
            'fullname' => 'IA.EVENTBASED_RECOGNITION',
            'desc'     => 'IA.EVENT_BASED_RECOGNITION',
            'type'     => $gBooleanType,
        ),
        array(
            'path'      => 'ENABLE_EXPENSE_CONTRACTS',
            'fullname'  => 'IA.EXPENSE_RECOGNITION',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'radio',
                'maxlength' => 1,
                'size' => 1,
                'validlabels' => array('IA.STANDARD_EXPENSE_RECOGNITION','IA.NO_EXPENSE_RECOGNITION',),
                'validvalues' => array('E', 'N'),
                'default' => 'E',
            ),
            'showlabelalways'   => true
        ),
        array(
            'path'      => 'FVAMOUNTBASEDON',
            'fullname'  => 'IA.USE_THE_FAIR_VALUE_PRICE_EFFECTIVE_AS_OF_THE',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'maxlength' => 1,
                'size' => 1,
                'validlabels' => array('IA.CONTRACT_LINE_START_DATE','IA.MEA_ALLOCATION_EFFECTIVE_DATE'),
                'validvalues' => array(ContractUtil::FVPRICE_CONTRACT_LINE_START_DATE_V, ContractUtil::FVPRICE_MEA_EFFECTIVE_DATE_V),
                'default' => ContractUtil::FVPRICE_CONTRACT_LINE_START_DATE,
            ),
        ),
        array(
            'path'     => 'TRACKTIMESHEETVALUEFLAG',
            'fullname' => 'IA.ENABLE_DEFERRED_REVENUE_FOR_TIME_AND_MATERIALS',
            'type' => $gBooleanType,
            'default'  => 'false',
        ),
        [
            'path'     => 'REV_REC_ON_INVOICE_FLAG',
            'fullname' => 'IA.REV_REC_ON_INVOICE_FLAG',
            'type' => $gBooleanType,
            'default' => 'false'
        ],
    ),
    'module'    => 'revenue',
    'api' => array('PERMISSION_READ'    => 'services/revenuesetup/edit',
                   'PERMISSION_MODULES' => array('co')),
);
