<?php

/**
 * =============================================================================
 * FILE:        File1099SubmissionLogFormEditor.cls
 *
 * <AUTHOR> DESCRIPTION:    File 1099 form editor
 *
 * (C)2022 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 * =============================================================================
 */

/**
 * Editor class for File1099 object
 */
class File1099SubmissionLogFormEditor extends FormEditor
{
    const VIEW_SUBMISSIONLOG = 'submissionlogview';
    const VIEW_VIEWERRORS = 'viewerrors';
    const VIEW_REDIRECT = 'viewredirect';


    /** @var  string $currentView Current view */
    private $currentView = self::VIEW_SUBMISSIONLOG;

    /**
     * @var string $kShowRedirectState
     */
    var $kShowRedirectState    = 'showredirect';

    protected $additionalTokens = [
        'IA.REFRESH',
        'IA.GO_TO_TAXBANDITS',
    ];

    /**
     * I18N. Collection of tokens used primarily in JavaScript.
     *
     * @return string[]
     */
    protected function getFormTokens() : array
    {
        $this->textTokens[] = "IA.ALLOW_BROWSER_POPUPS";
        $this->textTokens[] = "IA.SOME_ERROR_PLEASE_RETRY_AFTER_SOME_TIME";
        return parent::getFormTokens();
    }

    /**
     * @param array $_params Initial params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
        $this->kActionHandlers[self::VIEW_VIEWERRORS] = [
            'handler' => 'ProcessViewAction',
            'states' => [
                $this->kShowViewState,
                $this->kShowEditState,
                $this->kShowNewState,
                $this->kInitState,
            ]
        ];

        $this->kActionHandlers[self::VIEW_REDIRECT] = [
            'handler' => 'ProcessEditNewAction',
            'states' => [
                $this->kShowRedirectState,
                $this->kShowNewState,
            ]
        ];

        $action = Request::$r->_action;
        // If action is not set, check the _do values on what action is triggered
        if (empty($action)) {
            $action = Request::$r->_do;
        }

        if ($action == self::VIEW_VIEWERRORS) {
            $this->currentView = self::VIEW_VIEWERRORS;
        }
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    protected function ProcessEditNewAction(&$_params)
    {
        $action = Request::$r->_action;
        if($action == self::VIEW_REDIRECT) {
            $_sess = Session::getKey();
            $opKey = GetOperationId('ap/lists/file1099');
            $filterUrl = 'editor.phtml?.sess=' . $_sess . '&.op=' . $opKey ;
            Redirect($filterUrl);
            return true;
        }
        return parent::ProcessEditNewAction($_params);
    }

    /**
     * @param string $state
     *
     * @return array
     */
    public function getStandardButtons($state)
    {
        $buttons = [];
        if ($this->currentView == self::VIEW_SUBMISSIONLOG) {
            if (File1099Manager::validateSageCloudSubscription()) {
                $this->setButtonDetails($buttons, $this->kShowRedirectState, 'redirectbtn', 'IA.ADD',
                    'viewredirect', false);
                $this->setButtonDetails($buttons, 'refreshbtn', 'refreshbtn', GT($this->textMap,'IA.REFRESH'),
                    'refresh', false, "submissionlogGridHandler.refreshSubmissionLog();", false);
                $this->setButtonDetails($buttons, 'statusbtn', 'statusbtn', GT($this->textMap,'IA.GO_TO_TAXBANDITS'),
                    'status', false, "loadPartnerPageURL(null, false, true);", false);
            }
        }

        return $buttons;
    }

    /**
     * @param array $buttons
     * @param array $buttonsProperty
     */
    protected function createSplitButtonEntry(&$buttons, $buttonsProperty)
    {
        $actions = array();
        foreach ($buttonsProperty as $buttonkey => $buttonproperty) {
            //$action = array();
            $jsCode = $buttonproperty['jsCode'] ?? '';
            $action = $this->createAction(
                $buttonproperty['id'], 'export' . $buttonproperty['id'], $buttonkey,
                $buttonproperty['action'], $buttonproperty['submit'], $jsCode, $buttonproperty['serverAction']
            );

            if (isset($buttonproperty['isDefault']) && $buttonproperty['isDefault'] == true) {
                $action['default'] = true;
            }
            $actions[] = $action;
        }
        $this->createSplitButton($buttons, $actions);
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $this->handleSubmissionLogPage($obj);
        $this->handleErrorViewPage($obj);
        parent::mediateDataAndMetadata($obj);
    }

    /**
     * @param array $obj
     *
     */
    private function handleSubmissionLogPage(&$obj)
    {
        if ($this->currentView == self::VIEW_SUBMISSIONLOG) {
            $this->setYearField($obj);
        }
        if(Request::$r->_fs) {
            $obj['FILESUBMITTED'] = true;
            $obj['SUBMITTEDMSG'] = I18N::getSingleToken('IA.1099_FORM_SUBMISSION_FOR_EFILING');
        }
    }

    /**
     * @param array $obj
     *
     */
    private function setYearField(&$obj)
    {
        $view = $this->getView();

        $yearValidLabels = [];
        $yearValidValues = [];
        $monthYear = explode('-', date('m-Y'));
        $currentMonth = $monthYear[0];
        $currentYear = $monthYear[1];
        for ($i = $currentYear - 1; $i <= $currentYear; $i++) {
            $yearValidLabels[] = $i;
            $yearValidValues[] = $i;
        }
        $defaultYear = $yearValidValues[0];
        if(in_array($currentMonth, [11,12])) {
            $defaultYear = $yearValidValues[1];
        }
        $obj['TAXYEAR'] = (string)$defaultYear;
        // YEAR ENDING
        $view->findComponents(['path' => 'TAXYEAR'], EditorComponentFactory::TYPE_FIELD, $taxYearField);
        if ($taxYearField && $taxYearField[0]) {
            $taxYearField[0]->setProperty(['type', 'validlabels'], $yearValidLabels);
            $taxYearField[0]->setProperty(['type', 'validvalues'], $yearValidValues);
        }
    }

    /**
     * @param array $obj
     *
     */
    private function handleErrorViewPage(&$obj)
    {
        if ($this->currentView == self::VIEW_VIEWERRORS) {
            $this->setErrorField($obj);
        }
    }

    /**
     * @param array $obj
     *
     */
    private function setErrorField(&$obj)
    {
        $view = $this->getView();
        $errorField = [];
        $view->findComponents(['path' => 'ERRORS'], EditorComponentFactory::TYPE_FIELD, $errorField);
        if ($errorField && $errorField[0]) {
            $errors = json_decode($obj['LATESTJSONRESPONSE'], true);
            if (!empty($errors) && $errors['body'] && $errors['body']['Form1099Records'] &&
                $errors['body']['Form1099Records']['ErrorRecords']) {
                $errorList = $errors['body']['Form1099Records']['ErrorRecords'];
                $message = '';
                foreach ($errorList as $error) {
                    $message .= "<span id='errorBlock1' style=\"font-size: 12pt;font-weight: bold;\">";
                    $message .= $error['Name'] . "</span></br>";
                    $message .= $error['Message'];
                    $message .= "</br>";
                }
                $obj['ERRORS'] = $message;
            } else if (!empty($errors) && $errors['body'] && $errors['body']['Errors']) {
                $errorList = $errors['body']['Errors'];
                $message = '';
                foreach ($errorList as $error) {
                    $message .= "<span id='errorBlock1' style=\"font-size: 12pt;font-weight: bold;\">";
                    $message .= $error['Name'] . "</span></br>";
                    $message .= $error['Message'];
                    $message .= "</br>";
                    if($error['Id'] && $error['Id'] === "AUTH-100026") {
                        $message = "<span id='errorBlock1' style=\"font-size: 12pt;font-weight: bold;\">";
                        $message .= $error['Name'] . "</span></br>";
                        $message .= I18N::getSingleToken('IA.TAXBANDITS_AUTH_ERROR');
                    }
                }
                $obj['ERRORS'] = $message;
            } else if (!empty($errors) && $errors['body'] && $errors['body']['errors']) {
                $errorList = $errors['body']['errors'];
                $message = '';
                foreach ($errorList as $key => $error) {
                    $message .= "<span id='errorBlock1' style=\"font-size: 12pt;font-weight: bold;\">";
                    $message .= $key . "</span></br>";
                    $message .= $error[0] ?? '';
                    $message .= "</br>";
                }
                $obj['ERRORS'] = $message;
            } else if (!empty($errors) && $errors['diagnoses']) {
                $errorList = $errors['diagnoses'];
                $message = '';
                foreach ($errorList as $error) {
                    $message .= "<span id='errorBlock1' style=\"font-size: 12pt;font-weight: bold;\">";
                    $message .= $error['$applicationCode'] . "</span></br>";
                    $message .= $error['$message'];
                    $message .= "</br>";
                }
                $obj['ERRORS'] = $message;
            } else if(!empty($errors)) {
                $message = '';
                $payerHeaders = Form1099Helper::PAYERHEADERS;
                foreach ($errors as $error) {
                    foreach($payerHeaders as $header) {
                        if(preg_match('/\b'.$header.'\b/', $error)) {
                            $message .= "<span id='errorBlock1' style=\"font-size: 12pt;font-weight: bold;\">";
                            $message .= $header . "</span></br>";
                        }
                    }
                    if(preg_match('/\bSupport ID\b/', $error)) {
                        $error = explode("[",$error);
                        $error = $error[0];
                    }
                    $message .= $error;
                    $message .= "</br>";
                }
                $obj['ERRORS'] = $message;
            } else {
                $obj['ERRORS'] = '';
            }
        }
    }

    /**
     * Get xml layout file
     *
     * @param array $params
     *
     * @return string name of layout .xml file
     */
    protected function getMetadataKeyName(&$params)
    {
        $xmlName = in_array($this->currentView, [self::VIEW_VIEWERRORS]) ? '_' . $this->currentView : '';
        $xmlName = $params['entity'] . $xmlName;
        return $xmlName . '_form.pxml';
    }

    /**
     * @return string[]
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/file1099.js';
        $jsFiles[] = '../resources/js/file1099submission.js';
        $jsFiles[] = '../resources/js/file1099submissionloghandler.js';
        return $jsFiles;
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;

        switch ($cmd) {
            case 'loadPartnerPageURL':
                $this->ajaxLoadPartnerPageURL();
                break;
            case 'ajaxInitiateOnlinePollingForLogin':
                $this->ajaxInitiateOnlinePollingForLogin();
                break;
            case 'loadSubmissionLog':
                $this->ajaxLoadSubmissionLog($this->getSubmissionLogGridRequest(), true);
                break;
            case 'resubmit':
                $this->ajaxResubmit();
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * load partner page url
     *
     * @return array
     */
    private function ajaxLoadPartnerPageURL()
    {
        $submissionId = Request::$r->submissionId;
        $finalization = Request::$r->finalization;
        $submissionLogKey = Request::$r->submissionLogKey;
        $forceLogin = Request::$r->forceLogin;
        $response = $this->getFile1099Mgr()->redirectToPartnerPage($finalization, $forceLogin, $submissionId, $submissionLogKey);
        echo json_encode($response);
    }

    /**
     * @return File1099Manager
     */
    public function getFile1099Mgr()
    {
        return Globals::$g->gManagerFactory->getManager('file1099');
    }


    /**
     * initiate file submission>
     *
     * @return array
     */
    private function ajaxInitiateOnlinePollingForLogin()
    {
        $type = Request::$r->type;
        $forceLogin = Request::$r->forceLogin === 'true';
        $values = json_decode(Request::$r->values, true) ?? [];
        $result = $this->getEntityMgr()->initiateOnlinePollingForLogin($type, $values['POLLING_URL'], $forceLogin,
            $values['RECORDNO'], $values['SUBMISSIONID']);
        echo json_encode($result);
    }

    /**
     * @param array $request
     * @param bool $pagination
     *
     *
     * @return array|null
     */
    private function ajaxloadSubmissionLog($request, $pagination = true)
    {
        // Get all the set values
        $queryId = $request['QUERYID'];
        $page = $request['PAGE'];
        $pageSize = $request['PAGESIZE'];

        // Get the query filters
        $querySpec = [
            'selects' => ['RECORDNO', 'EXTERNALSUBMISSIONIDLINK', 'WHENSUBMITTED', 'FORMTYPE', 'CREATEDBY',
                'NOOFRECORDS', 'STATE', 'LOCATION', 'LATESTJSONRESPONSE', 'STATUS'],
            'filters' => [$this->getFilters($request)],
        ];
        // Add the orders if any
        $orders = $request['ORDERS'];
        if (!empty($orders)) {
            $querySpec['orders'] = [$orders];
        }

        if ($pagination && $page !== -1 && $pageSize !== -1) {
            $querySpec['start'] = $page * $pageSize;
            $querySpec['max'] = $pageSize;
        }
        $file1099SubmissionLogManager = Globals::$g->gManagerFactory->getManager('file1099submissionlog');
        $rows = $file1099SubmissionLogManager->GetList($querySpec, false, false);
        $this->processlogResult($rows);
        $numRecords = 0;
        if (count($rows)) {
            $numRecords = $rows[0]['QCNT'];
        }

        // Return the data
        $response = ['PARAMS' => ['QUERYID' => $queryId, 'TOTAL_RECORDS' => $numRecords, 'PAGE' => $page,
            'PAGE_SIZE' => $pageSize], 'RESULT_SET' => $rows];
        echo json_encode($response);
    }

    /**
     * Returns the filters.
     *
     * @param array $request
     *
     *
     * @return array
     */
    private function getFilters($request)
    {
        $filters = [];
        if ($request) {
            if ($request['EXTERNALSUBMISSIONID']) {
                $filters[] = ['EXTERNALSUBMISSIONID', 'LIKE', '%' . $request['EXTERNALSUBMISSIONID'] . '%'];
            }
            if ($request['WHENSUBMITTED']) {
                $filters[] = ['WHENSUBMITTED', 'LIKE', '%' . $request['WHENSUBMITTED'] . '%'];
            }
            if ($request['NOOFRECORDS']) {
                $filters[] = ['NOOFRECORDS', '=', $request['NOOFRECORDS']];
            }
//            if($request['STATE']) {
//                if($request['STATE'] == File1099SubmissionLogManager::FILE1099_STATE_NAME_FAILED) {
//                    $filters[] = [ 'STATE', 'IN', [File1099SubmissionLogManager::FILE1099_STATE_FILE_ERROR,
//                        File1099SubmissionLogManager::FILE1099_STATE_LOGIN_PENDING,
//                        File1099SubmissionLogManager::FILE1099_STATE_AWAITING_SUBMISSION,
//                        File1099SubmissionLogManager::FILE1099_STATE_FILE_CREATED,
//                        File1099SubmissionLogManager::FILE1099_STATE_LOGIN_DONE] ];
//                } else if($request['STATE'] == File1099SubmissionLogManager::FILE1099_STATE_NAME_INPROGRESS) {
//                    $filters[] = [ 'STATE', '=', File1099SubmissionLogManager::FILE1099_STATE_SUBMISSION_IN_PROGRESS];
//                } else if ($request['STATE'] == File1099SubmissionLogManager::FILE1099_STATE_NAME_SUBMITTED) {
//                    $filters[] = [ 'STATE', '=', File1099SubmissionLogManager::FILE1099_STATE_FILE_SUBMITTED];
//                }
//            }
            if ($request['LOCATION']) {
                $filters[] = ['LOCATION', 'LIKE', '%' . $request['LOCATION'] . '%'];
            }
            if ($request['STATUS']) {
                $filters[] = ['STATUS', 'LIKE', '%' . $request['STATUS'] . '%'];
            }
            if ($request['FORMTYPE']) {
                $filters[] = ['FORMTYPE', 'LIKE', '%' . $request['FORMTYPE'] . '%'];
            }
            if ($request['CREATEDBY']) {
                $filters[] = ['CREATEDBY', 'LIKE', '%' . $request['CREATEDBY'] . '%'];
            }
            if ($request['TAXYEAR']) {
                $filters[] = ['TAXYEAR', 'LIKE', '%' . $request['TAXYEAR'] . '%'];
            }
        }
        return $filters;
    }

    /**
     * process result
     * @param array $data
     *
     */
    private function processlogResult(&$data)
    {
        if (!empty($data)) {
            $file1099SubmissionLogOpId = GetOperationId('ap/lists/file1099submissionlog/create');
            $errorUrl = 'editor.phtml?.sess=' . Session::getKey() . '&.op=' . $file1099SubmissionLogOpId;
            $errorUrl .= '&.popup=1&.viewOnly=1&.action=viewerrors';
            $props = [
                "modaltype" => "form",
                "cnttype" => "url",
            ];
            foreach ($data as &$logEntry) {
                $errorUrl .= '&.r=' . $logEntry['RECORDNO'];
                $props['content'] = $errorUrl;
                $lineActionMenu = [
                    "label" => "Action",
                    "items" => $this->getActionItems($logEntry, $props)
                ];
                $logEntry["LINEACTIONS"] = $lineActionMenu;
            }
        }
    }

    /**
     * Returns the list of actions for Action drop down at bank account transaction level
     *
     * @param array $logEntry
     * @param array $props
     * @return array
     */
    private static function getActionItems($logEntry, $props)
    {
        if (in_array($logEntry['STATE'], [File1099SubmissionLogManager::FILE1099_STATE_FILE_ERROR])) {
            $lineActionMenuItems [] =
                [
                    "label" => "View errors",
                    "type" => "floatpage",
                    "props" => $props
                ];
            $lineActionMenuItems [] =
                [
                    "label" => "Resubmit",
                    "type" => "callback",
                    "props" => [
                        "func" => 'resubmit',
                        "customProps" => [
                            "recordID" => $logEntry['RECORDNO'],
                        ],
                    ],
                ];
        }
        return $lineActionMenuItems;
    }

    /**
     * Returns the reconciliation request.
     *
     * @return array
     */
    public function getSubmissionLogGridRequest()
    {
        $request = [];
        $request['EXTERNALSUBMISSIONID'] = Request::$r->batchid;
        $request['WHENSUBMITTED'] = Request::$r->date;
        $request['LOCATION'] = Request::$r->location;
        $request['CREATEDBY'] = Request::$r->createdby;
        $request['FORMTYPE'] = Request::$r->formtype;
        $request['NOOFRECORDS'] = Request::$r->noofrecords;
        $request['STATUS'] = Request::$r->status;
        $request['PAGE'] = Request::$r->page;
        $request['PAGESIZE'] = Request::$r->pagesize;
        $request['QUERYID'] = Request::$r->queryid;
        $request['ORDERS'] = Request::$r->orders;
        $request['TAXYEAR'] = Request::$r->taxyear;

        return $request;
    }

    /**
     * resubmit file
     *
     * @return array
     */
    private function ajaxResubmit()
    {
        $recordNo = Request::$r->recordNo;
        $response = [];
        $response['SUCCESS'] = false;
        if ($recordNo) {
            $response = $this->getFile1099Mgr()->resubmit($recordNo);
        }
        echo json_encode($response);
    }

}


