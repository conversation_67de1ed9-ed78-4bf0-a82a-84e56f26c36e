<?

$krevrectemplentryQueries['QRY_REVRECTEMPLENTRY_SELECT_BY_PARENT'] = array(
        'QUERY' => "SELECT revrectemplentry.record#,revrectemplentry.templatekey,revrectemplentry.accountkey,glaccount.acct_no,glaccount.title,revrectemplentry.periodoffset,revrectemplentry.percamount FROM revrectemplentry revrectemplentry,baseaccount glaccount WHERE (revrectemplentry.templatekey =  ? ) and revrectemplentry.accountkey = glaccount.record#  (+)   and glaccount.cny# (+) = ?  and revrectemplentry.cny# (+) = ? order by periodoffset",
        'ARGTYPES' => array('integer', 'integer', 'integer')
);

$krevrectemplentryQueries['QRY_REVRECTEMPLENTRY_DELETE_BY_PARENT'] = array(
    'QUERY' => "DELETE FROM revrectemplentry WHERE templatekey in (
        select record# from revrectemplate 
        where record# = ? 
            and recmethod = 'C' 
            and revrectemplate.cny# = revrectemplentry.cny#
    ) AND cny# = ? ",
    'ARGTYPES' => array('integer' ,'integer' ),
);