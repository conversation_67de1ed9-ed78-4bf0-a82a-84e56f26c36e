<?php

/**
 * ApproveAPBillLister
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */
class ApproveAPBillLister extends NLister
{

    /** @var int $userRec */
    private $userRec;
    /** @var int $cny */
    private $cny;
    /** @var string $viewflag */
    private $viewflag = "S";

    /* @var array $additionalTokens */
    protected $additionalTokens = [
        'IA.VIEW',
        'IA.APPROVE',
        'IA.DECLINE',
        'IA.VIEW_SUBMITTED',
        'IA.VIEW_ALL',
        'IA.APPROVAL_COMMENTS',
        'IA.DONE',
        'IA.CANCEL',
        'IA.BILL',
        'IA.APPROVE_AP_BILL',
        'IA.DECLINE_AP_BILL'
    ];

    public function __construct()
    {
        parent::__construct(
            array(
                'entity' => 'approveapbill',
                'formmethod' => 'GET',
                'title' => 'IA.APPROVE_BILLS',
                'fields' => array(
                    "'VIEW'",
                    "'APPROVE'",
                    "'DECLINE'",
                    'VENDORID',
                    'RECORDID',
                    'WHENCREATED',
                    'CURRENCY',
                    'TRX_TOTALENTERED',
                    'TOTALENTERED',
                    'STATE',
                ),
                'enablecheck' => true,
                'disableadd' => true,
                'disabledelete' => true,
                'disableedit' => true,
                'id' => 'RECORDNO',
                'format' => array(
                    'TOTALENTERED' => array('calign' => 'right'),
                    'TRX_TOTALENTERED' => array('calign' => 'right'),
                ),
                'totalpos' => true,
            )
        );

        $this->userRec = GetMyUserid();
        $this->cny = GetMyCompany();
    }

    /**
     * @return ApproveAPBillManager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof ApproveAPBillManager);
        return $this->entityMgr;
    }

    /**
     * Add filters to the lister based on user permissions
     *
     * @return array
     */
    public function BuildQuerySpec()
    {
        $viewflag = Request::$r->_viewflag;
        if (isset($viewflag) && $viewflag != '') {
            $this->viewflag = $viewflag;
        }

        $querySpec = parent::BuildQuerySpec();

        if ($this->mcenabled) {
            $querySpec['filters'][0][] = array('approveapbill.trx_totalpaid', '=', '0');
            $querySpec['filters'][0][] = array('approveapbill.trx_totalselected', '=', '0');
        } else {
            $querySpec['filters'][0][] = array('approveapbill.totalpaid', '=', '0');
            $querySpec['filters'][0][] = array('approveapbill.totalselected', '=', '0');
        }

        /** @var APBillApprovalManager $mngr */
        $mngr = Globals::$g->gManagerFactory->getManager('apbillapproval');
        if ($mngr->isAdminApprover($this->userRec)) {
            // If the user is an admin approver let's get all the bills without any filter
            $this->getAdminApproverSubQuery($querySpec);
        } else {
            //  If the user is not an admin approver we will get all the bills he can approve/decline
            $this->getApproverSubQuery($querySpec);
        }

        if ( IsMultiEntityCompany() && GetContextLocation() ) {
            $fld = $this->getEntityMgr()->GetMultiEntityFilters();
            if($fld[0]) {
                $querySpec['filters'][0][] = array('approveapbill.locationkey', '=', GetContextLocation());
            }
        }

        return $querySpec;
    }

    /**
     * Prepare subquery to get information from approval history (Admin version)
     *
     * @param array $querySpec query components to augment
     */
    private function getAdminApproverSubQuery(&$querySpec)
    {
        if ($this->viewflag != 'A') {
            // only show bills that I need to approve i.e. those in state 'submitted' or 'partially approved'
            $querySpec['filters'][0][] = array('approveapbill.state', 'IN', array('S','PA'));
        }

        $approverqry = " SELECT prrecordkey FROM approvalhistory a WHERE a.prrecordkey = approveapbill.record# AND a.cny# = $this->cny ";

        if ($this->viewflag == 'A') {
            $approverselectqry = " (SELECT count(*) FROM approvalhistory a WHERE a.cny# = $this->cny
				    AND a.prrecordkey = approveapbill.record# 
			        AND state = 'S' AND approvedbykey IS NULL) as hasapprovals ";
            $querySpec['selects'][] =  $approverselectqry;
        }

        $querySpec['filters'][0][] = array('RECORDNO', 'INSUBQUERY', array($approverqry));
    }

    /**
     * Prepare subquery to get information from approval history
     *
     * @param array $querySpec query components to augment
     */
    private function getApproverSubQuery(&$querySpec)
    {
        /** @var APBillApprovalManager $mngr */
        $mngr = Globals::$g->gManagerFactory->getManager('apbillapproval');
        // Get the user approval level permissions
        $approvalLevels = $mngr->getUserApprovalLevelsStr();
        $approvallevelQryStr = '';
        $approvallevelAllWhereClause = ' ';
        $approvallevelQryStr2 = '';
        $restricedLocationqry = '';
        $restricedDeptqry = '';
        if(IsRestrictedUser()){
            // Get the user restricted locations
            $restricedLocations = $mngr->getRestrictedUserLocation();
            if (!FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('DISABLE_DEPARTMENT_RESTRICTION_FOR_APBILL_APPROVAL')) {
                //$restricedDepts = GetRestrictedUserDepts();
                $restricedDepts = $mngr->getRestrictedUserDepartment();
            }else{
                $restricedDepts = [];
            }

            if(!empty($restricedLocations)){
                $restricedLocationqry = PrepINClauseStmt('', $restricedLocations, ' prentry.location# NOT ');
            }
            if(!empty($restricedDepts)){
                $restricedDeptqry = PrepINClauseStmt('', $restricedDepts, ' prentry.dept# NOT ');
                if(FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('ENABLE_HIDING_ROOT_DEPARTMENT')){
                    $restricedDeptqry = ' ( ' . $restricedDeptqry . ' OR prentry.dept# IS NULL )';
                }
            }

            // Combine location and department restrictions
            $locaDeptRestriction = '';
            if (!empty($restricedLocations) && !empty($restricedDepts)) {
                $locaDeptRestriction = " and ($restricedLocationqry OR $restricedDeptqry)";
            } elseif (countArray($restricedLocations)) {
                $locaDeptRestriction = ' and ' . $restricedLocationqry;
            } elseif (countArray($restricedDepts)) {
                $locaDeptRestriction = ' and ' . $restricedDeptqry;
            }

            if(isSpecified($locaDeptRestriction)){
                $approvallevelQryStr2 = 'AND NOT EXISTS 
                                    (SELECT 1 from prentrymst prentry WHERE prentry.cny#= a.cny#
                                     AND prentry.recordkey= a.prrecordkey ' . $locaDeptRestriction . ')';
            }
        }


        if (isset($approvalLevels) && $approvalLevels != '') {
            $approvallevelQryStr = " OR (
                                    approval_level IN ($approvalLevels)
                                    AND approval_type IN ( '" . APPTYPE_APPROVAL_LEVEL . "')
                                    AND not exists (
                                        SELECT
                                            1
                                        FROM
                                            approvalhistory ap
                                        WHERE
                                            ap.cny# = $this->cny
                                            AND ap.prrecordkey = approveapbill.record#
                                            AND ap.state != 'R'
                                            AND ap.approvedbykey = $this->userRec
                                            AND ap.approval_type IN( '" . APPTYPE_APPROVAL_LEVEL . "')
                                    )
                                ) ";

            // We need to have this as separate where condition for "View All" view and the approve/decline link will be decided by the select clause
            $approvallevelAllWhereClause = " OR (approval_level IN ($approvalLevels) AND approval_type IN ( '" . APPTYPE_APPROVAL_LEVEL . "') 
            AND (approvedbykey is null OR approvedbykey = $this->userRec ) )";
        }

        $approvalUserGroupQuery = "OR EXISTS (
                SELECT 1
                FROM v_ugroupmember
                WHERE v_ugroupmember.parentgroup = a.approverusergroupkey
                  AND v_ugroupmember.rh = $this->userRec
                  AND v_ugroupmember.cny#= $this->cny
            )";

        if ($this->viewflag == 'A') {
            // Get all the bills with an approval history the user has or can approve / decline
            $approverqry = "SELECT prrecordkey
                                    FROM approvalhistory a
                                    WHERE a.prrecordkey = approveapbill.record#
                                    $approvallevelQryStr2
                                    AND a.cny# = $this->cny
                                    AND
                                    (
                                        (
                                            (
                                                approverkey = $this->userRec 
                                                $approvalUserGroupQuery)
                                            AND approval_type in ('" . APPTYPE_USER_LEVEL . "', '" . APPTYPE_VALUE_TXN_DEPT_RULE . "', '".APPTYPE_APPROVAL_BY_VENDOR."')
                                        )
                                        $approvallevelAllWhereClause
                                    )";
            $querySpec['filters'][0][] = array('RECORDNO', 'INSUBQUERY', array($approverqry));

            $approverselectqry = " (SELECT count(1) FROM approvalhistory a WHERE a.cny# = $this->cny
				    AND a.prrecordkey = approveapbill.record#
			            AND state = 'S'
		                    AND approvedbykey IS NULL
                	            AND (((approverkey    = $this->userRec $approvalUserGroupQuery) AND approval_type in 
                	                  ( '" . APPTYPE_USER_LEVEL . "', '" . APPTYPE_VALUE_TXN_DEPT_RULE . "', '".APPTYPE_APPROVAL_BY_VENDOR."'))
                        	    	  $approvallevelQryStr	                                									
									  )
									)
				   				    as hasapprovals ";

            $querySpec['selects'][] = $approverselectqry;
        } else {
            // Get all the bill the user has or can approve / decline
            $approverqry = "SELECT prrecordkey
                            FROM approvalhistory a
                            WHERE a.prrecordkey = approveapbill.record#
                            $approvallevelQryStr2
                            AND a.cny# = $this->cny
                            AND state IN ('S', 'PA')
                            AND ( ((approverkey = $this->userRec $approvalUserGroupQuery)
                            and approval_type in ( '" . APPTYPE_USER_LEVEL . "', '" . APPTYPE_VALUE_TXN_DEPT_RULE . "','".APPTYPE_APPROVAL_BY_VENDOR."' )) 
                            $approvallevelQryStr )";
            $querySpec['filters'][0][] = array('RECORDNO', 'INSUBQUERY', array($approverqry));
        }
    }

    /**
     * Build lister table
     */
    function BuildTable()
    {
        parent::BuildTable();

        $table = $this->table;
        $approveOp = GetOperationId('ap/activities/approveapbill/view');
        $editOp = GetOperationId('ap/activities/approveapbill/edit');

        foreach ($table as $key => $tableEntry) {
            $rec = $tableEntry['RECORDNO'];
            $hasApprovals = isset($tableEntry['HASAPPROVALS']) && $tableEntry['HASAPPROVALS'] == '0' ? false : true;
            $owner = $this->GetObjectOwnership($key);
            $ownerloc = $this->GetObjectOwnerLocation($key);
            $url = 'editor.phtml?.op='.$approveOp.'&editOp='.$editOp.'&.do=view&.r='. urlencode($rec).'&.approvals=' . urlencode($hasApprovals).'&.title=' .
                urlencode(URLCleanParams::insert('.title', GT($this->textMap,'IA.BILL')));
            $url = CallUrl($url);
            $view = GT($this->textMap, 'IA.VIEW');
            $approve = GT($this->textMap, 'IA.APPROVE');
            $decline = GT($this->textMap, 'IA.DECLINE');
            if ($owner > 0 && $ownerloc) {
                $this->table[$key]["'VIEW'"] = '<a href="' . $this->_calcSlideUrl($url, $ownerloc, false) . '"' . '>'.$view.'</a>';
            } else {
                $this->table[$key]["'VIEW'"] = '<a href="' . $url . '"' . '>'.$view.'</a>';
            }

            if ($this->viewflag == 'S' || ($tableEntry['HASAPPROVALS'] == '1') && !in_array($tableEntry['STATE'], array('Declined'))) {

                $escapedRec = XMLUtils::xmlSpecialChars(str_replace('\'', '\\\'', $rec));
                //$escapedRec = str_replace("'", "\\'", $rec);
                $this->table[$key]["'APPROVE'"] = "<a href='#' onclick=\"ApproveBill('$escapedRec', 'Approve', '$ownerloc')\" data-toggle='modal' data-target='#apprcommentdiv'>".$approve."</a>";
                $this->table[$key]["'DECLINE'"] = "<a href='#' onclick=\"ApproveBill('$escapedRec', 'Decline', '$ownerloc')\" data-toggle='modal' data-target='#apprcommentdiv'>".$decline."</a>";

            } else {
                unset($this->table[$key]["'APPROVE'"]);
                unset($this->table[$key]["'DECLINE'"]);
            }
        }

    }

    /**
     * Build top panel of the lister
     *
     * @return string XML for the top panel
     */
    function genTopPanel()
    {
        $ret = "<b id='viewopt'/><b id='approve'/><b id='decline'/>";
        $ret .= parent::genTopPanel();
        return $ret;
    }

    /**
     * Build bottom panel of the lister
     *
     * @return string XML for the bottom panel
     */
    function genBotPanel()
    {
        $ret = "<b id='viewopt'/><b id='approve'/><b id='decline'/>";
        $ret .= parent::genBotPanel();
        return $ret;
    }

    /**
     * Build buttons of the lister
     *
     * @return string XML for the lister buttons
     */
    function genAllButtons()
    {
        $ret = parent::genAllButtons();
        $ret .= "<b id='viewopt'>" . $this->calcViewAllUrl() . "</b>";
        $ret .= "<b id='approve'>" . $this->calcApproveUrl() . "</b>";
        $ret .= "<b id='decline'>" . $this->calcDeclineUrl() . "</b>";
        return $ret;
    }

    /**
     * Build view all button
     *
     * @return string
     */
    function calcViewAllUrl()
    {
        $dst = 'lister.phtml';
        $do = '';
        $op = Request::$r->_op;
        $showprivate = Request::$r->_showprivate;

        if ($this->viewflag == 'A') {
            $text = GT($this->textMap,'IA.VIEW_SUBMITTED');
            $viewflag = 'S';
        } else {
            $text = GT($this->textMap,'IA.VIEW_ALL');
            $viewflag = 'A';
        }
        $url = $this->U($dst, ".do=$do&.r=&.op=$op&.showprivate=$showprivate&.viewflag=" . $viewflag, $this->LJUMP);

        $ret = "<a href=\"" . $url . "\">". $text ."</a>";

        return $ret;
    }

    /**
     * Build Approve button
     *
     * @return string
     */
    function calcApproveUrl()
    {
        $ret = "<chk>";
        $ret .= "<cmd c='set' n='.approve'>".GT($this->textMap,'IA.APPROVE')."</cmd>";
        $ret .= "<cmd c='set' n='.action'>Approve</cmd>";
        $ret .= "<cmd c='addElement' n='" . CsrfUtils::DOT_CSRF_PARAM_NAME . "'>"
            . $this->calcCSRFToken(Request::$r->_op) . "</cmd>";
        $ret .= "<cmd c='set' n='.done'>" . str_replace('&', '&amp;', insertDoneUnEnc(ScriptRequest())) . "</cmd>";
        $ret .= "<cmd c='SubmitAll'>submit.phtml</cmd>";
        $ret .= ApprovalManager::getApproveDeclineLoadingPanel('Approve');
        $ret .= "</chk>";
        return $ret;
    }

    /**
     * Buiold decline button
     *
     * @return string
     */
    function calcDeclineUrl()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $p = &$this->_params; //???
        $ret = "<chk>";
        $ret .= "<cmd c='set' n='.approve'>".GT($this->textMap,'IA.DECLINE')."</cmd>";
        $ret .= "<cmd c='set' n='.action'>Decline</cmd>";
        $ret .= ApprovalManager::getApproveDeclineLoadingPanel('Decline');
        $ret .= "<cmd c='addElement' n='" . CsrfUtils::DOT_CSRF_PARAM_NAME . "'>"
            . $this->calcCSRFToken(Request::$r->_op) . "</cmd>";
        $ret .= "<cmd c='set' n='.done'>" . str_replace('&', '&amp;', insertDoneUnEnc(ScriptRequest())) . "</cmd>";
        $ret .= "<cmd c='SubmitAll'>submit.phtml</cmd>";
        $ret .= "</chk>";
        return $ret;
    }

    /**
     * Build checkbox for the rows
     *
     * @param int $i index of table row to calculate checkbox status for
     *
     * @return string XML for table row's checkbox
     */
    function calcCheckBox($i)
    {
        $t = &$this->table;
        $ret = "";

        $isME = IsMultiEntityCompany();
        $owner = $this->GetObjectOwnership($i);
        $megaFlag = ($isME && $owner) ? false : true;

        $hasApprovalPerm = ($t[$i]['HASAPPROVALS'] == 1 && !in_array($t[$i]['STATE'], array('Declined'))) ? true : false;

        if (($this->viewflag == 'S' || $hasApprovalPerm) && $megaFlag) {
            $ret = parent::calcCheckBox($i);
        }

        return $ret;
    }

    /**
     * @return string
     */
    function CalcHeadIsland()
    {
        $pageOp = Request::$r->_op;

        $headTag = parent::CalcHeadIsland();
        $headTag .=
            "  <script language=javascript>
        var sess = '" . Session::getKey() . "';
		var pageOp = " . $pageOp . ";
		var cny = '" . $this->cny . "';
		var contextLocation = '" . GetContextLocation() . "';
		var isMega = '" . IsMultiEntityCompany() . "';
		var currScript = '" . insertDoneUnEnc(ScriptRequest()) . "';

	    function ApproveBill(rec, appType, ownerloc) {
	        set('.rec',rec);
        	set('.approve',appType);
        	set('.action',appType);
			set('.done', '" . insertDoneUnEnc(ScriptRequest()) . "');
			approvalcommentCtrl = document.getElementById('apprcommentdiv');
			//set('.approveurl',approveurl);
			set('.ownerloc', ownerloc);
			approvalHeader = document.getElementById('aphead');
			approvalButton = document.getElementById('submitlink');
			if (appType == 'Approve') {
			    approvalHeader.innerHTML = '&nbsp;'+GT('IA.APPROVE_AP_BILL');
			    approvalButton.innerHTML = '&nbsp;'+GT('IA.APPROVE');
			} else {
			    approvalHeader.innerHTML = '&nbsp;'+GT('IA.DECLINE_AP_BILL');
			    approvalButton.innerHTML = '&nbsp;'+GT('IA.DECLINE');
			}
			document.getElementById('.approvalcomments').value = '';
        	if (approvalcommentCtrl) {
				return displayControl(approvalcommentCtrl.style);
			} else {
	            return false;
            }
	     }

		function SubmitAll(rec, url) {
			var docSelected = false;
			var docs = document.getElementsByName('.checks[]');
			for (i = 0; i < docs.length; i++) {
				if (docs[i].checked) {
					docSelected = true;
					break;
				}
			}
			if (docSelected) {
				return setactMethod('POST',url);
			} else {
				if (PAGE_LAYOUT_TYPE && PAGE_LAYOUT_TYPE === 'Q') {
				    window.setTimeout(function(){
				        QXUtil.hideLoading();
				    },0);
				} else {
				    YAHOO.loadingPanel.panel.hide();
				}
				return false;
			}
		}
	

		function SaveApprovalWithComments() {
			approvalcomments = document.getElementById('.approvalcomments');
			var csrfToken = '" . $this->calcCSRFToken($pageOp) . "';
            set('.apprcomment',approvalcomments.value);

			ownerloc = document.ff.elements['.ownerloc'].value;

			appType = document.ff.elements['.approve'].value;

            if (appType == 'Approve') {
               ypanelstr = " . ApprovalManager::getApproveDeclineLoadingPanelMessage('Approve') . ";
            } else {
               ypanelstr = " . ApprovalManager::getApproveDeclineLoadingPanelMessage('Decline') . ";
		    }
			LaunchLoadingPanel(ypanelstr);

			if (ownerloc && ownerloc != '') {
				var rec = document.ff.elements['.rec'].value;
				var payLoad = {};
				payLoad['" . CsrfUtils::DOT_CSRF_PARAM_NAME . "'] = csrfToken;
				var approveurl = 'submit.phtml?.op='+pageOp+'&.sess='+sess+'&.rec='+escape(rec)+'&.approve='+appType+'&.action='+appType+'&.apprcomment='+escape(approvalcomments.value)+'&.done='+escape(currScript);
				SlideLaunch('mereportslide',0,cny,'',approveurl,ownerloc, 'false', undefined, payLoad);
			} else {
			    addElement('" . CsrfUtils::DOT_CSRF_PARAM_NAME . "', csrfToken);
				setactMethod('POST', 'submit.phtml');
			}
            return false;
		}

		function CancelApprovalComments() {		
	     	var apprcommentCtrl = document.getElementById('apprcommentdiv')
	        if (apprcommentCtrl) {
	            document.getElementById('.approvalcomments').value = '';
				return this.closeControl(apprcommentCtrl.style);
            } else {
                return false;
            }	
		}
	    </script> ";

        return $headTag;
    }


    /**
     * adds the approval 'dialog' to the lister
     *
     * @return string the approval dialog
     */
    function CalcBodyIsland()
    {
        $approveCmt = GT($this->textMap,'IA.APPROVAL_COMMENTS');
        $comments = GT($this->textMap, 'IA.COMMENTS_OPTIONAL');
        $done = GT($this->textMap, 'IA.DONE');
        $cancel = GT($this->textMap, 'IA.CANCEL');
        if (QXCommon::isQuixote()) {
            $divTag =
                '<div class="quixote-modal modal" id="apprcommentdiv" tabindex="-1" role="dialog">
                <div class="modal-dialog center" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title" id=\'aphead\'>'.$approveCmt.'</h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form class="form-inline" onsubmit="return false;">
                                <div class="form-group">
                                    <label for=".approvalcomments" class="control-label">'.$comments.'</label>
                                    <textarea id=".approvalcomments" value=""></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button id="submitlink" class="btn btn-primary" name="commentsdone" onclick="SaveApprovalWithComments(); return false;">'.$done.'</button>
                            <button class="btn btn-secondary" name=\'commentscancel\' onclick="CancelApprovalComments(); return false;" data-dismiss="modal">'.$cancel.'</button>
                        </div>
                    </div>
                </div>
            </div>';
        } else {
            $divTag = "<DIV id='apprcommentdiv' class=saveview>
                   <table cellpadding='0' cellspacing='1' class=NAVBAR width='270px'>
                      <tr height='20px'>
                         <td>
                            <font class=QVApplication id='aphead'>".$approveCmt."</font>
                         </td>
                         <td align=right>
                         </td>
                      </tr>
                   </table>
                   <table cellpadding='0' cellspacing='1' width='270px' class=field_list_data>
                      <tr>
                         <td class=label_cell id='aplabel'>".$comments."</td>
                         <td class=value_cell><textarea id='.approvalcomments' value=''></textarea></td>
                      </tr>
                      <tr>
                         <td class=label_cell>&nbsp;</td>
                         <td class=value_cell align=right>
                            <A CLASS=Task href='#' name='commentsdone' id='submitlink' onclick=\"SaveApprovalWithComments(); return false;\">".$done."</A>
                            <A CLASS=Task href='#' name='commentscancel' onclick=\"CancelApprovalComments(); return false;\">".$cancel."</A>
                         </td>
                     </tr></table></DIV>";
        }

        return $divTag;
    }

    /**
     * @return string
     */
    function genGlobs()
    {
        $globalsStr = NLister::genGlobs();
        $globalsStr .= "<g name='.done'></g>";
        $globalsStr .= "<g name='.rec'></g>";
        $globalsStr .= "<g name='.action'></g>";
        $globalsStr .= "<g name='.apprcomment'></g>";
        $globalsStr .= "<g name='.ownerloc'></g>";
        $globalsStr .= "<g name='.viewflag'>" . $this->viewflag . "</g>";
        return $globalsStr;
    }
}
