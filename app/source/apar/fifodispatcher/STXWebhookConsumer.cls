<?php
//=============================================================================
//
//	FILE:			STXWebhookConsumer.cls
//	<AUTHOR> Dhabale <<EMAIL>>
//	DESCRIPTION:	STXWebhook Consumer Class
//
//
//=============================================================================


class STXWebhookConsumer extends FIFOConsumer
{
    const TIMEOUT = 'timeout';
    const RECORDNO = 'RECORDNO';

    /**
     * STXWebhookConsumer constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param array $job
     *
     * @return array
     */
    protected function executeJob(array $job): array
    {
        LogToFile('STXWEBHOOK CONSUMER : Start');

        $metric = new MetricConsumer_StxWebhookWebhookProcess();
        $metric->startTime();
        $startTime = time();
        $metric->setId($job['OBJECTRECID']);

        $automatedTrxEnabled = AutomatedTransactionInteractions::isAutomatedTransactionEnabled();

        $job[STXFileUploadQueueManager::TIMESTARTED] = GetTimestampGMT();
        $notificationBody = $job['OBJECT'];

        if (!STXFileuploadManager::isSTXEnabled()) {
            $metric->setIsError('T');
            LogToFile('STXWEBHOOK CONSUMER : STX IS NOT ENABLED');
            $job[STXWebhookQueueManager::STATE] = STXFileUploadQueueManager::CANCELLED_STATE;
            $job[STXWebhookQueueManager::DETAILS] = '{"status":"Failed","reason":"STX IS NOT enabled"}';
            $job[STXWebhookQueueManager::TIMEFINISHED] = GetTimestampGMT();
            $job[STXWebhookQueueManager::WHENMODIFIED] = $job[STXFileUploadQueueManager::TIMEFINISHED];
            $job[STXWebhookQueueManager::STXWEBHOOK] = self::getSTXWebhookQueueHistoryObject($job);
            $job[STXFileUploadQueueManager::OBJECT] = databaseStringCompress(json_encode($job[STXWebhookQueueManager::OBJECT], JSON_THROW_ON_ERROR));
            $this->queueManager->set($job);
            $timeTaken = ibcsub(time(), $startTime);
            $metric->setTimeTakenToProcess($timeTaken);
            $metric->publish();
            return $job;
        }

        if ($automatedTrxEnabled && $job[STXFileUploadQueueManager::TOPIC] == STXSubledgerNotificationHandler::STXWEBHOOK) {
            $metric->setIsError('T');
            LogToFile('STXWEBHOOK CONSUMER : Received STX notification after SAIL is enabled');
            $job[STXWebhookQueueManager::STATE] = STXFileUploadQueueManager::CANCELLED_STATE;
            $job[STXWebhookQueueManager::DETAILS] = '{"status":"Failed","reason":"Received STX notification after SAIL is enabled"}';
            $job[STXWebhookQueueManager::TIMEFINISHED] = GetTimestampGMT();
            $job[STXWebhookQueueManager::WHENMODIFIED] = $job[STXFileUploadQueueManager::TIMEFINISHED];
            $job[STXWebhookQueueManager::STXWEBHOOK] = self::getSTXWebhookQueueHistoryObject($job);
            $job[STXFileUploadQueueManager::OBJECT] = databaseStringCompress(json_encode($job[STXWebhookQueueManager::OBJECT], JSON_THROW_ON_ERROR));
            $this->queueManager->set($job);
            $timeTaken = ibcsub(time(), $startTime);
            $metric->setTimeTakenToProcess($timeTaken);
            $metric->publish();
            return $job;
        }

        if ($automatedTrxEnabled) {
            if (!SailWebhookHandler::createNewSessionFromCompanyId($notificationBody)) {
                $metric->setIsError('T');
                LogToFile('STXWEBHOOK CONSUMER : Create newsession for SAIL failed!');
                $timeTaken = ibcsub(time(), $startTime);
                $metric->setTimeTakenToProcess($timeTaken);
                $metric->publish();
                return $job;
            }
        } else {
            if (!StxWebhookHandler::createNewSessionFromCompanyId($notificationBody)) {
                $metric->setIsError('T');
                LogToFile('STXWEBHOOK CONSUMER : Create newsession failed!');
                $timeTaken = ibcsub(time(), $startTime);
                $metric->setTimeTakenToProcess($timeTaken);
                $metric->publish();
                return $job;
            }
        }

        $source = 'STXWEBHOOKCONSUMER::JOB';
        $ok = $this->queueManager->beginTrx($source);

        try {
            $webhookHandler = $this->getWebhookHandler($job['DOCTYPE'] ?? '', $job['TOPIC'] ?? '');
            if (!empty($webhookHandler)) {
                $ok = $ok && $webhookHandler->handleNotification($notificationBody);
                if ($ok) {
                    LogToFile('STXWEBHOOK CONSUMER : Handle Notifcation success');
                    $job[STXWebhookQueueManager::STATE] = STXFileUploadQueueManager::EXECUTED_STATE;
                    $job[STXWebhookQueueManager::DETAILS] = '{"status":"Success"}';
                    $job[STXWebhookQueueManager::TIMEFINISHED] = GetTimestampGMT();
                    $job[STXWebhookQueueManager::WHENMODIFIED] = $job[STXFileUploadQueueManager::TIMEFINISHED];
                    $job[STXWebhookQueueManager::STXWEBHOOK] = self::getSTXWebhookQueueHistoryObject($job);
                    $job[STXFileUploadQueueManager::OBJECT] = databaseStringCompress(json_encode($job[STXWebhookQueueManager::OBJECT], JSON_THROW_ON_ERROR));
                }
                $ok = $ok && $this->queueManager->set($job);
            } else {
                $ok = false;
                LogToFile('STXWEBHOON CONSUMER : Some error occured at stxwebhook handler');
            }
        } catch (Exception $e) {
            $ok = false;
            LogToFile('STXWEBHOON CONSUMER : an excpetion is ' . $e->getMessage());
        }

        if ($ok) {
            $metric->setIsError('F');
            LogToFile('STXWEBHOOK CONSUMER : Commit success');
            $this->queueManager->commitTrx($source);
        } else {
            $metric->setIsError('T');
            LogToFile('STXWEBHOOK CONSUMER : Rollback success');
            $this->queueManager->rollbackTrx($source);
        }

        $timeTaken = ibcsub(time(), $startTime);
        $metric->setTimeTakenToProcess($timeTaken);
        $metric->publish();

        LogToFile('STXWEBHOOK CONSUMER : End');
        return $job;
    }

    /**
     * @param string $topic
     * @param string $queueTopic
     * @return StxWebhookFileHandler|StxWebhookDraftHandler|SailWebhookHandler|null
     */
    private function getWebhookHandler($topic, $queueTopic)
    {
        if ($queueTopic === ExpensesWebhooks::TOPIC_SAIL_WEBHOOK) {
            LogToFile(__CLASS__ . ' : Creating SailWebhookHandler');
            return new SailWebhookHandler();
        } else {
            switch ($topic) {
                case STXExternalQueueManager::FILE :
                    return new StxWebhookFileHandler();
                case STXExternalQueueManager::DRAFT :
                    return new StxWebhookDraftHandler();
                default :
                    LogToFile('STX Webhook Processor : Something is wrong with this job');
                    break;
            }
        }


        return null;
    }


    /**
     * @param array $object
     *
     * @return bool
     */
    public function validateJobData(array $object): bool
    {
        return isset($object[STXExternalQueueManager::OBJECTRECID],
            $object[STXExternalQueueManager::OBJECT]);
    }

    /**
     * Execute the job through the consmer
     * @return string
     */
    protected function getMetricName(): string
    {
        return 'StxWebhookQueueMetric ';
    }

    /**
     * @return string The entity that handles the queue used by this consumer
     */
    protected function getQueueEntity(): string
    {
        return 'stxwebhookqueue';
    }

    /**
     * Returns the number of jobs allowed for a consumer to process
     *
     * @param int $timeLimit
     *
     * @return int
     */
    protected function batchSize(int $timeLimit): int
    {
        return $timeLimit * 100;
    }

    /**
     * @param array $processResult
     *
     * @return bool
     */
    protected function consumerStopCondition(array $processResult): bool
    {
        return $processResult[$this->queueManager->_cny]['status'] === self::TIMEOUT;
    }


    /**
     * Reset state
     */
    protected function resetState(): void
    {
        ManagerFactory::PurgeObjectInstances();
        Globals::$g->gManagerFactory->PurgeManagerInstances();
    }

    /**
     * Get the prefix string to use in the log messages
     *
     * @return string
     */
    public function getLogPrefix()
    {
        return 'STX Webhook Queue Processor';
    }

    /**
     * @param array $job
     * @return array
     */
    public static function getSTXWebhookQueueHistoryObject(array $job)
    {
        $stxpackagequeue = [];
        if ($job[STXFileUploadQueueManager::STATE] === STXFileUploadQueueManager::EXECUTED_STATE) {
            $stxpackagequeue [] = [
                STXWebhookQueueHistoryManager::CATEGORY => 'e',
                STXWebhookQueueHistoryManager::DETAILS => databaseStringCompress(json_encode($job, JSON_THROW_ON_ERROR)),
            ];
        }
        if ($job[STXFileUploadQueueManager::STATE] === STXFileUploadQueueManager::QUEUED_STATE) {
            $stxpackagequeue [] = [
                STXWebhookQueueHistoryManager::CATEGORY => 'q',
                STXWebhookQueueHistoryManager::DETAILS => databaseStringCompress(json_encode($job, JSON_THROW_ON_ERROR)),
            ];
        }
        if ($job[STXFileUploadQueueManager::STATE] === STXFileUploadQueueManager::CANCELLED_STATE) {
            $stxpackagequeue [] = [
                STXWebhookQueueHistoryManager::CATEGORY => 'c',
                STXWebhookQueueHistoryManager::DETAILS => databaseStringCompress(json_encode($job, JSON_THROW_ON_ERROR)),
            ];
        }
        if ($job[STXFileUploadQueueManager::STATE] === STXFileUploadQueueManager::SKIPPED_STATE) {
            $stxpackagequeue [] = [
                STXWebhookQueueHistoryManager::CATEGORY => 's',
                STXWebhookQueueHistoryManager::DETAILS => databaseStringCompress(json_encode($job, JSON_THROW_ON_ERROR)),
            ];
        }
        return $stxpackagequeue;
    }

    /**
     * @return array
     */
    protected function getOrderByClause()
    {
        return [self::RECORDNO, 'asc'];
    }
}

