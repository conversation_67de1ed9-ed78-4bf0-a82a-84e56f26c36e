<?php
//=============================================================================
//
//	FILE:			 StxFileUploadTrait.cls
//	<AUTHOR> Dhabale <<EMAIL>>
//	DESCRIPTION:	 Trait for the common code of STXFileUploadDispatcher & StxFileUploadConsumer
//
//
//=============================================================================
trait STXFileUploadTrait
{
    /**
     * @var string $processorPrefix
     */
    public static $processorPrefix = 'Stx Sync Processor';

    /**
     * Get the prefix string to use in the log messages
     *
     * @return string
     */
    public function getLogPrefix(): string
    {
        return self::$processorPrefix;
    }

    /**
     * Get cny# based on job id
     *
     * @param string $jobId
     *
     * @return int|false
     */
    public function parseCompanyFromJobID(string $jobId): int
    {
        return (int)$jobId;
    }
}