<?
/**
 * APARAgeFilterEditor.cls
 *
 * <AUTHOR> <raj<PERSON>.<EMAIL>>
 * @copyright 2023 Intacct Corporation All, Rights Reserved
 */

abstract class APARAgeFilterEditor extends FilterEditor
{
    protected bool $isMultipleEntitySelectionEnable = true;

    /**
     * @param array $editvalues
     * @param array $promptonrundata
     */
    function ShowTop($editvalues = [], $promptonrundata = [])
    {
        $this->_params['onloadjs'] .= 'initAging();';
        parent::ShowTop($editvalues, $promptonrundata);
    }

    function QXShowQuickViewDiv()
    {
        $this->buildMultipleEntityContainer();
    }

    protected function getMultipleEntityParams(): array
    {
        return [];
    }

    /**
     * Range control for Quixote layout
     */
    protected function buildMultipleEntityContainer(): void
    {
        if (!$this->isMultipleEntitySelectionEnable) {
            return;
        }

        $containerParams = $this->getMultipleEnti<PERSON>Params();
        if (empty($containerParams)) {
            return;
        }

        $this->MatchTemplates($this->_params['allfields'], ['path' => 'MULTIPLEFILTER'], $multiFilter);

        if (isset($multiFilter[0]['value']) && $multiFilter[0]['value'] !== '') {
            $currentRanges = explode('#~#', $multiFilter[0]['value']);
        } else {
            $currentRanges = ['-- No Members --'];
        }
        ?>
        <div class="qx-range-control-overlay"></div>
        <div id="multipleEntitySection" class="deliverselect qx-rc-container"
             onkeydown="if(event.keyCode==27){ toggleMultipleEntitySection(false); }">
            <div class="qx-rc-header">
                <span><?= I18N::getSingleToken($containerParams['title'] ?? ''); ?></span>
            </div>
            <div class="qx-rc-body">
                <div class="form-group">
                    <label><?= I18N::getSingleToken($containerParams['bodyLabel'] ?? ''); ?></label>
                    <table>
                        <tr>
                            <td>
                                <select tabIndex=-1 multiple name="currentranges" class="form-control qx-range-select"
                                        tabindex=1>
                                    <? ShowOptions('', $currentRanges); ?>
                                </select>
                            </td>
                            <td valign="top" width="5">
                                <p>
                                    <?php
                                    $onClick = "return memberup(document.main.currentranges)";
                                    $this->createLink(
                                        '<i class="fas fa-caret-up"></i>',
                                        'sortIcon', $onClick, 'MemberUp'
                                    );
                                    ?>
                                </p>
                                <p>
                                    <?php
                                    $onClick = "return memberdown(document.main.currentranges)";
                                    $this->createLink(
                                        '<i class="fas fa-caret-down"></i>',
                                        'sortIcon', $onClick, 'MemberDown'
                                    );
                                    ?>
                                </p>
                                <p>
                                    <?php
                                    $onClick = "return memberdel(document.main.currentranges)";
                                    $this->createLink(
                                        '<i class="fas fa-times"></i>',
                                        'sortIcon', $onClick, 'MemberDelete'
                                    );
                                    ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
                <?php $this->buildEntityFields($containerParams); ?>
            </div>
            <div class="qx-rc-footer">
                <input class="btn btn-secondary" type="button" name=".save"
                       value="<?= I18N::getSingleToken('IA.SAVE'); ?>" onclick='saveMultiSectionRanges();'>
                <input class="btn btn-secondary" type="button" name=".ret" onclick='toggleMultipleEntitySection(false)'
                       onblur='shiftFocusToMultiSectionRanges()' value="<?= I18N::getSingleToken('IA.CANCEL'); ?>">
            </div>
        </div>
        <?
    }

    protected function buildEntityFields(array $container): void
    {
        $pickEntity = $container['entity'] ?? '';
        $addBtnLabel = I18N::getSingleToken('IA.ADD');
        if (is_iterable($container['fields']['single'])) :
            ?>
            <table>
                <tr>
                    <?php foreach ($container['fields']['single'] as $field):
                        $path = $field['path'] ?? '';
                        $label = $field['label'] ?? '';
                        $entity = $field['entity'] ?? $pickEntity;
                        if (empty($path) || empty($entity)) {
                            continue;
                        }
                        ?>
                        <td>
                            <?php $this->constructComboField($entity, $path, $label); ?>
                        </td>
                        <td>
                            <?php
                            $onClick = "return multiSectionRangeAdd('$path','$path',document.main.currentranges)";
                            $this->createLink($addBtnLabel, 'btn btn-secondary', $onClick, 'MemberAdd');
                            ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            </table>
        <?php endif;

        if (is_iterable($container['fields']['range'])) :
            ?>
            <div class="form-group">
                <span class="form-control readonly"><b><?= I18N::getSingleToken('IA.OR_ADD_RANGE'); ?></b></span>
            </div>
            <table>
                <?php
                foreach ($container['fields']['range'] as $fields) :
                    $fromPath = $fields['from']['path'] ?? '';
                    $fromLabel = $fields['from']['label'] ?? '';
                    $fromEntity = $fields['from']['entity'] ?? $pickEntity;
                    $toPath = $fields['to']['path'] ?? '';
                    $toLabel = $fields['to']['label'] ?? '';
                    $toEntity = $fields['to']['entity'] ?? $pickEntity;

                    if (empty($fromPath) || empty($toPath) || empty($fromEntity) || empty($toEntity)) {
                        continue;
                    }
                    ?>
                    <tr>
                        <td>
                            <?php
                            $onChangeEvent = "SetToValue(this, '$toPath')";
                            $this->constructComboField($fromEntity, $fromPath, $fromLabel, $onChangeEvent);
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <?php
                            $this->constructComboField($toEntity, $toPath, $toLabel);
                            ?>
                        </td>
                        <td>
                            <?php
                            $onClick = "return multiSectionRangeAdd('$fromPath', '$toPath', document.main.currentranges)";
                            $this->createLink($addBtnLabel, 'btn btn-secondary', $onClick, 'MemberAdd');
                            ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif;
    }

    /**
     * Method to return the rpt fields path, that needs to show in a row as qx-acctrange-elements
     *
     * @return string[]
     */
    protected function getRangeFieldsPath(): array
    {
        return [
            'FIELDGRPRANGE',
            'FIELDGRPMULTIPLE',
            'FIELDGRPGROUP',
        ];
    }

    private function createLink(string $label, string $cls, string $onClick, string $dis): void
    {
        $stEvt = 'window.status="' . statusdisp($dis) . '"; return true;';
        $resetStEvt = 'window.status="";return true;';
        echo '<a href="#here" class="' . $cls . '" onClick="' . $onClick . '" 
        onmouseover=\'' . $stEvt . '\'
        onmousemove=\'' . $stEvt . '\'
        onfocus=\'' . $stEvt . '\'
        onblur=\'' . $resetStEvt . '\'
        onmouseout=\'' . $resetStEvt . '\'>' . $label . '</a>';
    }

    private function constructComboField(string $entity, string $path, string $label, string $onChange = ''): void
    {
        if (empty($entity) || empty($path)) {
            return;
        }
        $label = !empty($label) ? I18N::getSingleToken($label) : '';
        $_field = [
            'fullname' => $label,
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => $entity,
                'addlPickFields' => ['NAME'],
                'maxlength' => 20,
                'pickcount' => PICKCOUNTER,
            ],
            'path' => $path,
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'renameable' => true,
        ];
        if (!empty($onChange)) {
            $_field['onchange'] = $onChange;
        }
        // construct the picker field
        $this->ShowFieldsRow($_field);
    }
}
