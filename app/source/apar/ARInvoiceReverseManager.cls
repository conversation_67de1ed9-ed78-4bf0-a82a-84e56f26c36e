<?php
class ARInvoiceReverseManager extends InvoiceReverseManager
{

    /**
     * @param array $sourceRecord
     * @param array $revSourceRecord
     * @param bool  $quick
     *
     * @return bool
     */
    function _postProcessRecord(&$sourceRecord, &$revSourceRecord, $quick=false)
    {
        $ok = parent::_postProcessRecord($sourceRecord, $revSourceRecord, $quick);
        if ($ok) {
            $ok = ReverseTaxPartnerInvoice($sourceRecord, $revSourceRecord);
        }
        $ok = $ok && $this->terminateRevRec($sourceRecord['RECORD#']);
        // check if amortization is enabled if so then proceed with the amortization reversal process
        if (AmortizationScheduleManager::isAmortizationEnabled($sourceRecord['MODULEKEY'])) {
            $ok = $ok && $this->processAmortizationReverse($sourceRecord, $revSourceRecord);
        }
        return $ok;
    }

    /**
     * @param array $sourceRecord
     * @param array &$revSourceRecord
     *
     * @return bool
     */
    private function processAmortizationReverse(array $sourceRecord, array &$revSourceRecord)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $amortMgr = $gManagerFactory->getManager('aramortizationschedule');
        $source = "ARInvoiceReverseManager::processAmortizationReverse";
        $ok = $amortMgr->_QM->beginTrx($source);

        $ok = $ok && $amortMgr->createRevSourceRecordReverseAmortization($revSourceRecord);
        $ok = $ok && $amortMgr->reverseSourceRecordAmortizationSchedState($sourceRecord['RECORD#']);
        $ok = $ok && $amortMgr->reverseSourceRecordAmortizationSchedEntryState($sourceRecord['RECORD#']);

        $ok = $ok && $amortMgr->_QM->commitTrx($source);
        if (!$ok) {
            $amortMgr->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * @param int $recordKey
     *
     * @return bool
     */
    private function terminateRevRec($recordKey)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $rrsMgr = $gManagerFactory->getManager('revrecschedule');
        $query = array(
            'selects' => array('RECORDNO'),
            'filters' => array(array(
                array('RECORDKEY', '=', $recordKey),
            )),
        );
        $result = $rrsMgr->GetList($query);
        if (empty($result)) {
            return true;
        }
        
        $source = "ARInvoiceReverseManager::terminateRevRec";
        $ok = $rrsMgr->_QM->beginTrx($source);

        foreach ($result as $rrschedule) {
            $rrsMgr->terminateRevRec($rrschedule['RECORDNO'], null, '', 'Invoice Reversed', RevRecScheduleManager::TERMINATE_ALL, null, false, false);
        }
        
        $ok = $ok && $rrsMgr->_QM->commitTrx($source);
        if (!$ok) {
            $rrsMgr->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }

    /**
     * @param array $record
     * @param string $_date
     * @param string $recordBatchkey
     * @param array $revParentRecord
     * @param string $_desc
     *
     * @return bool
     */
    function _createReverseRecordStructure(&$record, $_date,$recordBatchkey, &$revParentRecord, $_desc)
    {
        $ok = parent::_createReverseRecordStructure($record, $_date, $recordBatchkey, $revParentRecord, $_desc);

        if ( AmortizationScheduleManager::isAmortizationEnabled($record['MODULEKEY']) ) {
            foreach ( $record['ITEMS'] as &$item ) {
                $entry = $item['ITEMS'][0];
                if (isset($entry['AMORTIZATIONTEMPLATEKEY']) && $entry['AMORTIZATIONTEMPLATEKEY'] != '') {
                    // set the line item Amortization date to the reversed date
                    $entry['AMORTIZATIONSTARTDATE'] = $_date;
                    $entry['AMORTIZATIONENDDATE'] = $_date;
                    $item['ITEMS'][0] = $entry;
                }
            }
        }
        return $ok;
    }
    
}