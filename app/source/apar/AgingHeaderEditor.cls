<?php
/**
 * Base Manager class for all the VEND/CUST AGING
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 Intacct Corporation All, Rights Reserved
 */
/**
 * Base Manager class for all the VEND/CUST AGING report drill down
 */
class AgingHeaderEditor extends FormEditor
{
    // Constants
    const ARINVOICE = 'ri';
    const ARADJUSTMENT = 'ra';
    const ARADVANCE = 'rr';
    const APBILL = 'pi';
    const APADJUSTMENT = 'pa';
    const APADVANCE = 'pr';
    
    // Class variables
    // -- START
    /**
     * @var string[] $recordTypeToModule
     */
    public static $recordTypeToModule = array(
        self::ARINVOICE => 'ar',
        self::ARADJUSTMENT => 'ar',
        self::ARADVANCE => 'ar',
        self::APBILL => 'ap',
        self::APADJUSTMENT => 'ap',
        self::APADVANCE => 'ap',
    );

    /**
     * @var string[] $recordTypeToEntity
     */
    public static $recordTypeToEntity = array(
        self::ARINVOICE => 'arinvoice',
        self::ARADJUSTMENT => 'aradjustment',
        self::ARADVANCE => 'aradvance',
        self::APBILL => 'apbill',
        self::APADJUSTMENT => 'apadjustment',
        self::APADVANCE => 'appostedadvance',
    );
    // -- STOP  

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
 {
        parent::__construct($params);
 }
    
    /**
     * Render the opkey value for VENDAGING/CUSTAGING report drill down
     *
     * @param string $r
     * @param array  $detail
     */
    public static function getRecordDetail($r, &$detail) 
    {
        $txnMgr = Globals::$g->gManagerFactory->getManager('prrecord');
        $filter = array(
        'selects' => array('RECORDTYPE'),
        'filters' => array(array(array('RECORDNO', '=', $r)))
        );
        $txnRec = $txnMgr->GetList($filter);    
        
        $recordType = $txnRec[0]['RECORDTYPE'];
        $recordModule = self::$recordTypeToModule[$recordType];
        $recordEntity = self::$recordTypeToEntity[$txnRec[0]['RECORDTYPE']];
        
        $opkey = $recordModule."/lists/".$recordEntity."/view";
        $detail['opkey'] = $opkey;
        $detail['recordentity'] = $recordEntity;
        $detail['recordtype'] = $recordType;
        $detail['recordmodule'] = $recordModule;
        $detail['op'] = GetOperationId($opkey);
    }
}