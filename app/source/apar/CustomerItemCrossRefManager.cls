<?
/**
 *  @description This is the CustomerItemCrossRefManager manager
 *
 *    @file    CustomerItemCrossRefManager.cls
 *    <AUTHOR>
 *  @include copyright.txt
 *
*/
import('ItemCrossRefManager');
class CustomerItemCrossRefManager extends ItemCrossRefManager
{
    /**
     * @return array
     */
    protected function getSelectFieldList()
    {
        return [
            'RECORDNO',
            'REFTYPE',
            'ITEMID',
            'CUSTOMERID',
            'REFTYPECONTEXT',
            'ITEMKEY',
            'ITEMALIASID',
            'ITEMALIASDESC'
        ];
    }

}
