<?php

/**
 * Helper class to build payment request and response with all the required information.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */
class APPaymentRequestResponseBuilder extends PaymentRequestResponseBuilder
{
    /**
     * Implementing method to populate the entity manager for AP Payment detail processor.
     */
    protected function populateEntityManager()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $this->entityManager = $gManagerFactory->getManager('appymt');
    }

    /**
     * @param array $entityDetail
     * @param PaymentRequest $paymentRequest
     * @param PaymentDataHelper $dataHelper
     */
    protected function setContactVersionMap($entityDetail, PaymentRequest $paymentRequest, PaymentDataHelper $dataHelper)
    {
        //11. get the vendor version
        /*
         * Get the vendor Contact Map from Request
         * It will avoid duplicate query, and if contact present in map, we will fetch directly
         * Creating map directly on record#, not on vendor Id, because different vendor can override
         * to same pay to contact
         * Structure of Map
         *  Array
            (
                contactversion Record# [157] => contactversion vrec# 205
            )
         */
        $vendContactVerMap = $paymentRequest->getvendorContactVersionMap();
        // $vendorContactMap shoul be empty for each payment request
        $vendorContactMap = array();
        // First look if vendor has override pay to contact or not
        if( $entityDetail['PAYTOKEY'] && !isset($vendContactVerMap[$entityDetail['PAYTOKEY']])) {
            $vendorContactMap[] = $entityDetail['PAYTOKEY'];
        }
        // if vendor contact not overriden then
        // Get the vendor display contact key and also look in map if present or not
        if( $entityDetail['DISPLAYCONTACTKEY'] && !isset($vendContactVerMap[$vendDetail['DISPLAYCONTACTKEY']]) ) {
            $vendorContactMap[] = $entityDetail['DISPLAYCONTACTKEY'];
        }
        // $vendorContactMap will be emptry only if not found in $vendContactVerMap
        // Need to query for new conatct and update the map for next payment request
        if(!empty($vendorContactMap)) {
            // Query will execute inside the function and it will update the $vendContactVerMap
            $dataHelper->getVendorContactVersion($vendorContactMap, $vendContactVerMap);
        }
        if(!empty($vendContactVerMap)) {
            // update the $vendContactVerMap for next payment
            $paymentRequest->setvendorContactVersionMap($vendContactVerMap);
        }

    }

    /**
     * @param array $values
     * @param array $credit
     * @param string $recordKey
     *
     * @return array
     */
    public function buildPaymentRequestForCredits($values, $credit, $recordKey)
    {
        $paymentRequest = parent::buildPaymentRequestForCredits($values, $credit, $recordKey);
        $paymentRequest['VENDORID'] = $values['VENDORID'];

        return $paymentRequest;
    }
}