<?

/*
 *	FILE: APAdvanceApprovalManager.cls
 *	AUTHOR: <PERSON><PERSON><PERSON><PERSON> G<PERSON>
 *	DESCRIPTION:
 * @copyright 2018 Sage Intacct Inc., All Rights Reserved
 */



/**
 * This class will handle all the account payable advance approval process, including submitting the document to approver,
 * approving the document, declining a document and notifying the approver.
 */
class APAdvanceApprovalManager extends APPymtApprovalManager
{
    
    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

}
