<?
import('Editor');

/**
 * Class APPaymentRequestEditor
 */
class APPaymentRequestEditor extends Editor
{
    
    
    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss
     */
    function showScripts($addYuiCss = true)
    {
        parent::showScripts($addYuiCss);
        //$currObj = $gRequest->GetCurrentObject($obj);
        //$listingAdvance = ($currObj['RECORDTYPE'] == PRRECORD_TYPE_APADVANCE);
        //if(!$listingAdvance) {
        ?>
      <script src="../resources/js/billdrill.js"></script>
      <script>
      function doEdit(rec,it,editop,sess,done) {
       url = "editor.phtml?.do=edit&.r="+rec+"&.it="+it+"&.op=1557&.action=edit&.sess="+sess+"&.done="+done;
       location.href = url;
      }
      </script>
        <?
        //}
    }
    
    /**
     * @param array     $_params
     * @param int       $editop
     */
    function ShowEditButtons($_params, $editop)
        {
        $gManagerFactory = Globals::$g->gManagerFactory;
            /** @noinspection PhpUndefinedVariableInspection */
            $currObj = Request::$r->GetCurrentObject($obj);
        $listingAdvance = ($currObj['RECORDTYPE'] == PRRECORD_TYPE_APADVANCE);
        $objhist = $gManagerFactory->getManager('prrecordhistory');
        if(!CnyUsesApproval()) {
            $objhist->SetWorkflow('appayment_noaprvl');
        } else if ($listingAdvance) {
            $objhist->SetWorkflow('apadvance');
        } else {
            $objhist->SetWorkflow('appayment');
        }
        $statenames = $objhist->ReverseStateNom();
        //eppp($statenames);
        //eppp($currObj['STATE']);
        if(!$objhist->CanEdit($statenames[$currObj['STATE']])) {
            return;
        }
        parent::ShowEditButtons($_params, $editop);
    }

    /**
     * getEntityData
     *  helper function to get data from entity manager and format the data for display
     *
     * @param string        $entity  entity or object
     * @param string|int    $objId   entityid or objectid
     *
     * @return array        returns the formatted result set
     */
    protected function getEntityData($entity, $objId)
    {

        $entityData = parent::getEntityData($entity, $objId);
        $entityMgr = $this->GetManager($entity);
        /* @var APPaymentManager $entityMgr */
        $entityData = $entityMgr->GetCreditHistoryData($objId, $entityData);
        $entityData = $entityMgr->BuildDrillDowns($entityData);
        return $entityData;
    }
}

