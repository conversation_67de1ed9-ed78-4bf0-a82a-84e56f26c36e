<?php
//=============================================================================
//
//	FILE:		wfpm.cls
//	AUTHOR:		<PERSON>
//	DESCRIPTION:	to generate WF external payment XML file (base class)
//
//	(C)2008, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'country.inc';
require_once 'ims_publish_1.cls';
require_once 'backend_wfcurl.inc';
require_once 'IAEmail.cls'; // Ensure IAEmail is included. WFPM will be deprecated by end of 2022

class wfpm
{
    /** @var bool $isocountry */
    var $isocountry;
    /** @var string $pmplus*/
    var $pmplus;
    /** @var string $paymethod */
    var $paymethod;
    /** @var array $pRequest*/
    var $pRequest;
    /** @var array $vendorinfo */
    var $vendorinfo;
    /** @var  array $bankinfo */
    var $bankinfo;
    /** @var  array $compinfo */
    var $compinfo;
    // Map to cache recurring data
    /** @var array  $cachedWFPMDataMap*/
    public static $cachedWFPMDataMap = array();

    public function __construct()
    {
        return true;
    }

    /**
     * @param string $processid
     * @param string $xmlstring
     * @param int $numofpymts
     * @param int $pymttotal
     *
     * @return bool
     */
    function SubmitPaymentFile($processid, $xmlstring, $numofpymts, $pymttotal) 
    {
        
        global $gErr;

        $filedate = date('Ymd');
        $filetime = date('His');
        $filecontrolnumber = 'IAT'.str_pad($processid, 12, '0', STR_PAD_LEFT);

        global $kWFPMid;
        $wfconfiginfo = getWFConfigValues(GetMyCompany());
        $companyid = $wfconfiginfo['PMGRCODE'];
        $deployed = GetPreferenceForProperty($kWFPMid, 'DEPLOYED');
        $isTestMode = $deployed == 'Y' ? false : true;
        $filename = $companyid . '.' . $filetime . $filedate . rand(0, 10000000);
        if($isTestMode == true && $wfconfiginfo['ISTEST'] == 'T') {
            //Intacct test WF companyID
            $filename = 'TEST' . $filename;
        }        
        $filepath = "/webdirs/wfpm/".$filename;
        
        $headstring = '<?xml version="1.0" encoding="UTF-8"?>';
        $headstring .= '<File CompanyID="'.$companyid.'" ProcessID="'.$processid.'" DocumentType="XML" PmtRecCount="'.$numofpymts.'" PmtRecTotal="'.number_format($pymttotal, 2, '.', '').'">';
        $headstring .= '<FileInfoGrp FileDate="'.$filedate.'" FileTime="'.$filetime.'" FileControlNumber="'.$filecontrolnumber.'"/>';
        $tailstring = '</File>';

        $finalxml = $headstring.$xmlstring.$tailstring;

        // validate the xml content before we submit the file
        libxml_use_internal_errors(true);

        $xml = new DOMDocument();
        $xml->loadXML($finalxml);
        $errors = libxml_get_errors();
        if (!empty($errors)) {
            $gErr->addError('WF-0065', GetFL(), 'Error in XML file validation before submission. XML file may contain invalid characters.');
            return false;
        }

        // Create the payment manager xml file
        // LogToFile($finalxml, $filepath);
        $fhandle = fopen($filepath, "w");
        
        fwrite($fhandle, $finalxml);
        fclose($fhandle);
        
        //$currentDir = getcwd();
        //$post_data = array();
        //$post_data['File'] = '@'.$filename;
        
        //file we need to upload
        $reqParams = array();
        $reqParams['localfile'] = $filename;
        
    
        //execute curl post
        $error = '';
        $url = GetWFURLSwitch($isTestMode) . GetInboundSFTPFolder($wfconfiginfo['INBOUNDFOLDER']);
        sendCurlCommand($url, 'submit', $reqParams, $error, $isTestMode);
        if ( $error != '' ) {
            $gErr->addIAError('WF-0066', GetFL(),
                            'Error in submitting files to Wells Fargo Payment Manager.' . $error, ['ERROR' => $error]);

            return false;
        }
        
        if($isTestMode) {    
            $cny_title = GetMyCompanyTitle();
            $today = date('Y-m-d');
        
            // now we have a report output to email out
            //$emailto = GetPreferenceForProperty($kWFPMid, 'CONTACTEMAIL');
            $emailto = '<EMAIL>';
            $emailsubject = "WFPM Test File - test".$companyid;
            $emailmessage = "Customer: ".$cny_title." \n\n";
            $emailmessage .= "WFPM ID: ".$companyid." \n\n";
            $emailmessage .= "Date: ".$today." \n\n";
            $emailmessage .= "File Name: ".$filename." \n\n";
            
            $emailobj = new IAEmail($emailto);

            $emailobj->_from = '<EMAIL>';
            $emailobj->_reply_to = '<EMAIL>';

            //if($emailccme) {
            //	$emailobj->cc = $myemail;
            //}

            $emailobj->subject = $emailsubject;
            //$_emailsignature = GetEmailSignature();

            //if($_emailsignature != '') $emailmessage = $emailmessage."\n\n".$_emailsignature;

            //$emailmessage .= "\n\n\n\nThis report is generated and sent to you directly from Intacct's On Demand ERP system. www.intacct.com" .' (' .getenv("IA_SERVER_NAME") .')';

            $emailobj->body = $emailmessage;
            $emailobj->add_attachment($finalxml, $filename);

            $ok = ($emailobj->send());

            if(!$ok) {
                // Send email failure mail
                $myemail = GetMyContactEmail(true);
                $emailto .= ",".$myemail;
                $emailsubject = "Test file unable to be generated";
                $emailmessage = "Customer: ".$cny_title." \n\n";
                $emailmessage .= "WFPM ID: ".$companyid." \n\n";
                $emailmessage .= "Date: ".$today." \n\n";
                
                $this->SendErrorMail($emailto, $emailsubject, $emailmessage);
                return false;
            }
        }

        return true;
    }

    /**
     * @param string $emailto
     * @param string $emailsubject
     * @param string $emailmessage
     */
    function SendErrorMail($emailto, $emailsubject, $emailmessage)
    {
        $emailobj = new IAEmail($emailto);
        $emailobj->_from = '<EMAIL>';
        $emailobj->subject = $emailsubject;
        $emailobj->body = $emailmessage;
        $emailobj->send();
    }


    /**
     * @param array $pRequest
     * @param string $vendorid
     *
     * @return bool|string
     */
    function CreateWFPayments($pRequest, $vendorid) 
    {

        global $gErr, $kWFPMid;
        $this->isocountry = IsISOCountryEnabled();

        $this->pRequest = $pRequest;
        $this->GetBankInfo($pRequest['accountid']);

        if (!isl_preg_match('/^[0-9]*$/', $this->bankinfo['BANKACCOUNTNO'])) {
            $gErr->addError('WF-0067', GetFL(), 'The Wells Fargo bank account number should be numeric.', '', 'Please check your Wells Fargo bank account number setting from the Checking Account screen.');
            return false;
        }

        // populate vendor info
        $dataHelper = new APPaymentDataHelper();
        $this->vendorinfo = $dataHelper->getEntityDetails($vendorid);

        $this->pmplus = GetPreferenceForProperty($kWFPMid, 'PMPLUSENABLED');
        $paytocontactkey = '';

        if ($this->paymethod == 'WF Check') {
            if ($this->vendorinfo['CHECKENABLED'] != 'true') {
                $gErr->addIAError('WF-0068', GetFL(), sprintf('Vendor %s is not enabled with checking outsourcing.', $vendorid), ['VENDORID' => $vendorid]);
                return false;
            }
        }
        if ($this->paymethod == 'WF Domestic ACH') {
            if ($this->vendorinfo['ACHENABLED'] != 'true') {
                $gErr->addIAError('WF-0069', GetFL(), sprintf('Vendor %s is not enabled with domestic ACH.', $vendorid), ['VENDORID' => $vendorid]);
                return false;
            }
        }
        if ($this->paymethod == 'WF USD Wire') {
            if ($this->vendorinfo['WIREENABLED'] != 'true') {
                $gErr->addIAError('WF-0070', GetFL(), sprintf('Vendor %s is not enabled with USD Wire.', $vendorid), ['VENDORID' => $vendorid]);
                return false;
            }
        }

         
        $this->FormatWFPMCompInfo();
        $this->FormatWFPMBankInfo();
        $this->FormatWFPMVendorInfo();
     

        $commonxmltags = $this->CreateWFCommonTags();
        if ($commonxmltags === false) {
            return false;
        }
        $othertags = $this->CreateOtherTags();
        if ($othertags === false) {
            return false;
        }
        $orgnrtags = $this->CreateOrgnrTags();
        if ($orgnrtags === false) {
            return false;
        }
        $orgnrdeptags = $this->CreateOrgnrDepAcctTags();
        if ($orgnrdeptags === false) {
            return false;
        }

        $invoicetags = '';
        // get the bill first to see if we need to obtain the payto contact of the bill
        if ($this->paymethod == 'WF Check'
            || ($this->paymethod == 'WF Domestic ACH' && $this->pmplus == 'T')
            || ($this->paymethod == 'WF USD Wire' && $this->pmplus == 'T')
        ) {
            $invoicetags = $this->CreateInvoiceTags($paytocontactkey);
            if ($invoicetags === false) {
                return false;
            }
        }
        $rcvrtags = $this->CreateReceiverTags($paytocontactkey);
        if ($rcvrtags === false) {
            return false;
        }
        $rcvrdeptags = $this->CreateReceiverDepAcctTags();
        if ($rcvrdeptags === false) {
            return false;
        }

        $docdeliverytags = '';
        if ($this->paymethod != 'WF Check' && $this->pmplus == 'T' && $this->vendorinfo['PMPLUSREMITTANCETYPE'] != 'EDI') {
            $docdeliverytags = $this->CreateDocDeliveryTags();
            if ($docdeliverytags === false) {
                return false;
            }
        }
        $outputxmlstring = $commonxmltags . $othertags . $orgnrtags . $orgnrdeptags . $rcvrtags . $rcvrdeptags . $invoicetags . $docdeliverytags;

        return $outputxmlstring;
    }

    /**
     * @return bool
     */
    function FormatWFPMCompInfo()
    {
        global $gErr;

        if ($this->compinfo['ADDRESS1'] != '' && bccomp(isl_strlen($this->compinfo['ADDRESS1']), '55') > 0) {
            $gErr->addError('WF-0071', GetFL(), 'The address line 1 of the originator party should be no more than 55 characters.');
            return false;
        }
        if ($this->compinfo['ADDRESS2'] != '' && bccomp(isl_strlen($this->compinfo['ADDRESS2']), '55') > 0) {
            $gErr->addError('WF-0072', GetFL(), 'The address line 2 of the originator party should be no more than 55 characters.');
            return false;
        }
        if ($this->compinfo['CITY'] != '' && bccomp(isl_strlen($this->compinfo['CITY']), '30') > 0) {
            $gErr->addError('WF-0073', GetFL(), 'The city of the originator party should be no more than 30 characters.');
            return false;
        }
        if ($this->compinfo['ZIP'] != '') {
            $len =  isl_strlen($this->compinfo['ZIP']);
            if ($len != 5 && $len != 6 && $len != 9) {
                $gErr->addError('WF-0074', GetFL(), 'The length of the zip code of the originator party should be 5 or 9 for US and 6 for international. No punctuation, spaces or special characters are allowed.');
                return false;
            }
        }
        if ($this->compinfo['STATE'] != '' && bccomp(isl_strlen($this->compinfo['STATE']), '3') > 0) {
            $gErr->addError('WF-0075', GetFL(), 'The state of the originator party address should be 2 characters for states or 3 characters for provinces.');
            return false;
        }
        if ($this->compinfo['COUNTRYCODE'] == '' && $this->compinfo['COUNTRY'] != '' && bccomp(isl_strlen($this->compinfo['COUNTRY']), '30') > 0) {
            $gErr->addError('WF-0076', GetFL(), 'The address of the originator party address should be no more than 30 characters.');
            return false;
        }
        $this->_formatXMLData($this->compinfo['NAME'], $this->paymethod);
        $this->_formatXMLData($this->compinfo['TITLE'], $this->paymethod);
        $this->_formatXMLData($this->compinfo['ADDRESS1'], $this->paymethod);
        $this->_formatXMLData($this->compinfo['ADDRESS2'], $this->paymethod);
        $this->_formatXMLData($this->compinfo['CITY'], $this->paymethod);
        $this->_formatXMLData($this->compinfo['STATE'], $this->paymethod);
        $this->_formatXMLData($this->compinfo['COUNTRY'], $this->paymethod);
        return true;
    }

    /**
     * FormatWFPMBankInfo
     */
    function FormatWFPMBankInfo()
    {
        $this->_formatXMLData($this->bankinfo['BANKNAME'], $this->paymethod);
        $this->_formatXMLData($this->bankinfo['MAILADDRESS']['ADDRESS1'], $this->paymethod);
        $this->_formatXMLData($this->bankinfo['MAILADDRESS']['ADDRESS2'], $this->paymethod);
        $this->_formatXMLData($this->bankinfo['MAILADDRESS']['CITY'], $this->paymethod);
        $this->_formatXMLData($this->bankinfo['MAILADDRESS']['STATE'], $this->paymethod);
        $this->_formatXMLData($this->bankinfo['MAILADDRESS']['COUNTRY'], $this->paymethod);
    }

    /**
     * FormatWFPMVendorInfo
     */
    function FormatWFPMVendorInfo() 
    {
        $this->_formatXMLData($this->vendorinfo['NAME'], $this->paymethod);
        $this->_formatXMLData($this->vendorinfo['VENDORID'], $this->paymethod);
        $this->_formatXMLData($this->vendorinfo['PMPLUSEMAIL'], $this->paymethod, 'PMPLUSEMAIL');
        $this->_formatXMLData($this->vendorinfo['PMPLUSFAX'], $this->paymethod, 'PMPLUSFAX');
    }

    /**
     * @param array $contact
     */
    function FormatRecieverContact(&$contact)
    {
        $this->_formatXMLData($contact['PRINTAS'], $this->paymethod);
        $this->_formatXMLData($contact['MAILADDRESS']['ADDRESS1'], $this->paymethod);
        $this->_formatXMLData($contact['MAILADDRESS']['ADDRESS2'], $this->paymethod);
        $this->_formatXMLData($contact['MAILADDRESS']['CITY'], $this->paymethod);
        $this->_formatXMLData($contact['MAILADDRESS']['STATE'], $this->paymethod);
        $this->_formatXMLData($contact['MAILADDRESS']['COUNTRY'], $this->paymethod);        
    }

    /**
     * @param string[] $billrec
     */
    function FormatWFPMRemittInfo(&$billrec)
    {
        $this->_formatXMLData($billrec['RECORDID'], $this->paymethod);
        $this->_formatXMLData($billrec['DOCNUMBER'], $this->paymethod);
        $this->_formatXMLData($billrec['DESCRIPTION'], $this->paymethod);        
    }


    /**
     * @param string|string[]   $xmltxt
     * @param string            $pymtmethod
     * @param string            $fieldname
     */
    function _formatXMLData(&$xmltxt, $pymtmethod='', $fieldname='') 
    {
        $replacechar = ' ';
        $xmltxt = str_replace("*", $replacechar, $xmltxt);
        $xmltxt = str_replace("^", $replacechar, $xmltxt);
        $xmltxt = str_replace("<", $replacechar, $xmltxt);
        $xmltxt = str_replace(">", $replacechar, $xmltxt);
        $xmltxt = isl_preg_replace("(\r\n|\n|\r)", $replacechar, $xmltxt);
        $xmltxt = str_replace("`", $replacechar, $xmltxt);
        $xmltxt = str_replace("|", $replacechar, $xmltxt);
        
        if($pymtmethod == 'WF Check') {            
            $xmltxt = str_replace("~", $replacechar, $xmltxt);
            $xmltxt = str_replace("\\", $replacechar, $xmltxt);            
        }else if($pymtmethod == 'WF Domestic ACH') {            
            $xmltxt = str_replace("~", $replacechar, $xmltxt);            
            $xmltxt = str_replace("\\", $replacechar, $xmltxt);        
        }else if($pymtmethod == 'WF USD Wire') {
            if(!in_array($fieldname, array('PMPLUSEMAIL', 'PMPLUSFAX'))) {    
                $xmltxt = str_replace("-", $replacechar, $xmltxt);
            }            
            $xmltxt = str_replace("/", $replacechar, $xmltxt);                
        }     
        $xmltxt = str_replace("&", "&amp;", $xmltxt);
        $xmltxt = str_replace("'", "&apos;", $xmltxt);
        $xmltxt = str_replace("\"", "&quot;", $xmltxt);
    }

    /**
     * @return bool|string
     */
    function CreateWFCommonTags() 
    {
        global $gErr;
        $pymtid = $this->pRequest['WFPMPAYMENTID'];
        $curamt = number_format($this->pRequest['totalselected'], 2, '.', '');
        $valuedate = ReformatDate(GetCurrentDate(IADATE_USRFORMAT), GetUserDateFormat(), '-Ymd');

        [$dummy1, $dummy2] = explode('-', $pymtid);
        if ($dummy1 == '' || $dummy2 == '') {
            $gErr->addError('WF-0077', GetFL(), 'Reference ID is incorrect or missing.');
            return false;
        }
        if ($curamt == '') {
            $gErr->addError('WF-0078', GetFL(), 'Payment amount is missing.');
            return false;
        }
        if ($curamt < 0) {
            $gErr->addError('WF-0079', GetFL(), 'Payment amount is less than 0.');
            return false;
        }
        if ($curamt == 0) {
            $gErr->addError('WF-0080', GetFL(), 'Payment amount cannot be 0.');
            return false;
        }
        if (bccomp($curamt, '0.01') < 0) {
            $gErr->addError('WF-0081', GetFL(), 'Payment amount is less than 0.01.');
            return false;
        }
        if (bccomp($curamt, round($curamt, 2)) != 0) {
            $gErr->addError('WF-0082', GetFL(), 'Payment amount cannot contain more than 2 decimal places.');
            return false;
        }
        if ($valuedate == '') {
            // should not happen
            $gErr->addError('WF-0083', GetFL(), 'Payment date is missing.');
            return false;
        }

        $xmlstring = '<PmtID>'.$pymtid.'</PmtID>';
        $xmlstring .= '<CurAmt>'.$curamt.'</CurAmt>';
        $xmlstring .= '<ValueDate>'.$valuedate.'</ValueDate>';

        return $xmlstring;
    }

    /**
     * @return string
     */
    function CreateOtherTags() 
    {
        return '';
    }

    /**
     * @return bool|string
     */
    function CreateOrgnrTags() 
    {

        global $gErr;
        if ($this->compinfo['NAME'] == '') {
            $gErr->addError('WF-0084', GetFL(), 'The originator name is missing.');
            return false;
        }
        if ($this->compinfo['TITLE'] == '') {
            $gErr->addError('WF-0085', GetFL(), 'The originator reference company ID is missing.');
            return false;
        }
        if($this->isocountry == true && $this->compinfo['COUNTRYCODE'] == 'US') {
            $this->ReFormatZIPCode($this->compinfo['ZIP']);
        }
        $compname = $this->compinfo['NAME'];        
        if($this->paymethod == 'WF Domestic ACH') {
            if($this->vendorinfo['ACHREMITTANCETYPE'] == 'CCD' && isl_strlen($compname) > 16) {
                $compname = $this->trimXMLEncodedString($compname, 0, 16);
            }else if($this->vendorinfo['ACHREMITTANCETYPE'] == 'IAT' && isl_strlen($compname) > 35) {
                $compname = $this->trimXMLEncodedString($compname, 0, 35);
            }
        }
        $addreq = $this->vendorinfo['ACHREMITTANCETYPE'] == 'IAT' ? true : false;
        if (!$this->ValidateAddress($this->compinfo, $addreq, 'originator party')) {
            return false;
        }
        $xmlstring = '<OrgnrParty>';
        $xmlstring .= $this->CreateAddressNameTag($compname);
        $xmlstring .= $this->CreateAddressRefInfoTag($this->compinfo['TITLE']);
        $xmlstring .= $this->CreateAddressPostalTag(
            $this->compinfo['ADDRESS1'],
            $this->compinfo['ADDRESS2'],
            $this->compinfo['CITY'],
            $this->compinfo['STATE'],
            $this->compinfo['ZIP'],
            ($this->isocountry ? $this->compinfo['COUNTRYCODE'] : ''),
            ($this->isocountry ? GetCountryName($this->compinfo['COUNTRYCODE']) : $this->compinfo['COUNTRY'])
        );
        $xmlstring .= '</OrgnrParty>';

        return $xmlstring;
    }

    /**
     * @param string $zip
     *
     * @return bool
     */
    function ReFormatZIPCode(&$zip)
    {        
        $zip = str_replace("-", "", $zip);
        return true;
    }

    /**
     * @return string
     */
    function CreateOrgnrDepAcctTags() 
    {
        // should be overridden
        return '';
    }

    /**
     * @param string $paytocontactkey
     *
     * @return bool|string
     */
    function CreateReceiverTags($paytocontactkey)
    {
        if ( $paytocontactkey ) {
            //Pay to contact key is used only for ACH and check now.
        }

        global $gErr;
        if ($this->vendorinfo['NAME'] == '') {
            $gErr->addError('WF-0010', GetFL(), 'The receiver name is missing.');
            return false;
        }
        if ($this->vendorinfo['VENDORID'] == '') {
            $gErr->addError('WF-0011', GetFL(), 'The receiver reference ID is missing.');
            return false;
        }
        $vendorname = $this->vendorinfo['NAME'];        

        $xmlstring = '<RcvrParty>';
        $xmlstring .= $this->CreateAddressNameTag($vendorname);
        $xmlstring .= $this->CreateAddressRefInfoTag($this->vendorinfo['VENDORID']);
        $xmlstring .= '</RcvrParty>';
        return $xmlstring;
    }

    /**
     * @return string
     */
    function CreateReceiverDepAcctTags() 
    {
        return '';
    }

    /**
     * @param string $name
     *
     * @return string
     */
    function CreateAddressNameTag($name) 
    {
        return '<Name><Name1>'.$name.'</Name1></Name>';
    }

    /**
     * @param string $id
     *
     * @return string
     */
    function CreateAddressRefInfoTag($id) 
    {
        $xmlstring = '<RefInfo RefType="VN"><RefID>'.$id.'</RefID></RefInfo>';
        return $xmlstring;
    }

    /**
     * @param string $address1
     * @param string $address2
     * @param string $city
     * @param string $state
     * @param string $postalcode
     * @param string $countrycode
     * @param string $countryname
     *
     * @return string
     */
    function CreateAddressPostalTag($address1, $address2, $city, $state, $postalcode, $countrycode, $countryname) 
    {
        $xmlstring = '<PostAddr>';
        $xmlstring .= '<Addr1>'.$address1.'</Addr1>';
        if($this->vendorinfo['ACHREMITTANCETYPE'] != 'IAT') {
            if ($address2 != '') {
                $xmlstring .= '<Addr2>'.$address2.'</Addr2>';
            } else {
                $xmlstring .= '<Addr2/>';
            }
        }
        $xmlstring .= '<City>'.$city.'</City>';
        $xmlstring .= '<StateProv>'.$state.'</StateProv>';
        $xmlstring .= '<PostalCode>'.$postalcode.'</PostalCode>';
        $xmlstring .= ($countrycode != '' ? '<Country>'.$countrycode.'</Country>' : '<Country/>');
        $xmlstring .= ($countryname != '' ? '<CountryName>'.$countryname.'</CountryName>' : '<CountryName/>');
        $xmlstring .= '</PostAddr>';

        return $xmlstring;
    }

    /**
     * @param string $paytocontactkey
     *
     * @return string
     */
    function CreateInvoiceTags(&$paytocontactkey) 
    {
        global $gErr;
        $invinfo = '';
        $noTotalSelectBills = [];
        foreach ($this->pRequest['BILLS'] as $inv) {
            $billrec = $inv['billrec'];
            if ( ( ! isset($billrec['TRX_TOTALSELECTED'])
                   || $billrec['TRX_TOTALSELECTED'] == ''
                   || $billrec['billrec']['TRX_TOTALSELECTED'] === 0 ) ) {
                $noTotalSelectBills [] = $billrec['RECORDID'];
            }
            if($billrec['RECORDID'] == '') {
                $gErr->addError('WF-0086', GetFL(), 'The Invoice Number is missing.');
                return false;
            }
            
            $billrec['DOCNUMBER'] = $this->trimXMLEncodedString($billrec['DOCNUMBER'], 0, 30);
            $billrec['DESCRIPTION'] = $this->trimXMLEncodedString($billrec['DESCRIPTION'], 0, 250);
            $billrec['RECORDID'] = $this->trimXMLEncodedString($billrec['RECORDID'], 0, 30);
            $this->FormatWFPMRemittInfo($billrec);
            $poinfo = $potype = $ponum = '';
            if ($billrec['DOCNUMBER'] != '') {
                $potype = 'POType="PO"';
                $ponum = '<PONum>'.$billrec['DOCNUMBER'].'</PONum>';
                $poinfo = '<POInfo '.$potype.'>'.$ponum.'</POInfo>';
            }
            $noteinfo = $notetype = $notetext = '';
            if ($billrec['DESCRIPTION'] != '') {
                $notetype = ' NoteType="INV"';
                $notetext = '<NoteText>'.$billrec['DESCRIPTION'].'</NoteText>';
                $noteinfo = '<Note'.$notetype.'>'.$notetext.'</Note>';
            }
            
            $effdt = ReformatDate($billrec['WHENCREATED'], IADATE_STDFORMAT, '-Ymd');
            $netcurrAmt = number_format(($inv['selected'] + $inv['trx_credit'] + $inv['discount']), 2, '.', '');
            if ( $netcurrAmt != $billrec['TRX_TOTALSELECTED'] ) {
                $inlineCredit = false;
                //For inline credits, bill's 'TOTALENTERED' and 'TRX_TOTALSELECTED' does not include the negative amount.
                //So get it from the $this->pRequest['CREDITS'] map
                if ( $billrec['RECORDTYPE'] == 'pi' ) {
                    foreach ( $this->pRequest['CREDITS'] as $credit ) {
                        if ( $credit['RECORD#'] == $inv['billrec']['RECORD#'] ) {
                            $inlineCredit = true;
                            if ( $netcurrAmt != number_format($billrec['TRX_TOTALSELECTED'] + ibcabs($credit['NEGTOTALDUE']), 2, '.', '') ) {
                                LogToFile("wfpmlog-" . GetMyCompany() . "-" . GetMyCompanyTitle() . "-" . $billrec['RECORDID'] . "\n");
                                $gErr->addIAError(
                                    'WF-0087',
                                    GetFL(),
                                    sprintf('For the bill \'%1$s\' the Amount Due is only \'%2$s\'.', $billrec['RECORDID'], $billrec['TRX_TOTALSELECTED']),
                                    ['BILLREC_RECORDID' => $billrec['RECORDID'], 'BILLREC_TRX_TOTALSELECTED' => $billrec['TRX_TOTALSELECTED']]
                                );
                                return false;
                            }
                            break;
                        }
                    }
                }

                if ( $inlineCredit == false ) {
                   LogToFile("wfpmlog-" . GetMyCompany() . "-" . GetMyCompanyTitle() . "-" . $billrec['RECORDID'] . "\n");
                    $gErr->addIAError(
                        'WF-0087',
                        GetFL(),
                        sprintf('For the bill \'%1$s\' the Amount Due is only \'%2$s\'.', $billrec['RECORDID'], $billrec['TRX_TOTALSELECTED']),
                        ['BILLREC_RECORDID' => $billrec['RECORDID'], 'BILLREC_TRX_TOTALSELECTED' => $billrec['TRX_TOTALSELECTED']]
                    );
                    return false;
                }
            }
            
            $invinfo .= '<InvoiceInfo InvoiceType="IV" InvoiceNum="'.$billrec['RECORDID'].'" EffDt="'.$effdt.'" NetCurAmt="'.number_format(($inv['selected']+$inv['trx_credit']), 2, '.', '').'" TotalCurAmt="'.number_format($billrec['TOTALENTERED'], 2, '.', '').'" DiscountCurAmt="'.number_format($inv['discount'], 2, '.', '').'">'.$poinfo.$noteinfo.'</InvoiceInfo>';

            if (count($this->pRequest['BILLS']) == 1) {
                $paytocontactkey = $billrec['BILLTOPAYTOKEY'];
            }
        }

        if(!empty($noTotalSelectBills)) {
            $gErr->addError(
                'WF-0088', __FILE__.":".__LINE__,
                "Currently, we can't process your request. Reselect your bills, then try again. ");
            return false;
        }
        foreach ($this->pRequest['CREDITS'] as $credit) {            
            if($credit['RECORDID'] == '') {
                $gErr->addError('WF-0086', GetFL(), 'The Invoice Number is missing.');
                return false;
            }            
            $credit['DOCNUMBER'] = $this->trimXMLEncodedString($credit['DOCNUMBER'], 0, 30);
            $credit['DESCRIPTION'] = $this->trimXMLEncodedString($credit['DESCRIPTION'], 0, 250);
            $credit['RECORDID'] = $this->trimXMLEncodedString($credit['RECORDID'], 0, 30);
            $this->FormatWFPMRemittInfo($credit);
            $poinfo = $potype = $ponum = '';
            if ($credit['DOCNUMBER'] != '') {
                $potype = ' POType="PO"';
                $ponum = '<PONum>'.$credit['DOCNUMBER'].'</PONum>';
                $poinfo = '<POInfo '.$potype.'>'.$ponum.'</POInfo>';
            }
            $noteinfo = $notetype = $notetext = '';
            if ($credit['DESCRIPTION'] != '') {
                $notetype = ' NoteType="INV"';
                $notetext = '<NoteText>'.$credit['DESCRIPTION'].'</NoteText>';
                $noteinfo = '<Note'.$notetype.'>'.$notetext.'</Note>';
            }
            $effdt = ReformatDate($credit['WHENCREATED'], IADATE_STDFORMAT, '-Ymd');
            $invinfo .= '<InvoiceInfo InvoiceType="CM" InvoiceNum="'. $credit['RECORDID'] . '" EffDt="'. $effdt . '" NetCurAmt="'. number_format($credit['TOTALSELECTED'], 2, '.', ''). '" TotalCurAmt="'. number_format($credit['TOTALENTERED'], 2, '.', '').'" >'.$poinfo.$noteinfo.'</InvoiceInfo>';
        }                

        $xmlstring = '<PmtDetail>'.$invinfo.'</PmtDetail>';         
 
        return $xmlstring;
    }

    /**
     * @return bool|string
     */
    function CreateDocDeliveryTags() 
    {
        global $gErr;
        $wfconfiginfo = getWFConfigValues(GetMyCompany());
        $companyid = $wfconfiginfo['PMGRCODE'];

        if ($companyid == '') {
            $gErr->addError('WF-0010', GetFL(), 'The receiver name is missing.');
            return false;
        }

        $xmlstring = '<DocDelivery>';
        $xmlstring .= '<EDDBillerID>10000'.$companyid.'</EDDBillerID>';
        $xmlstring .= '<FileOut>';
        $xmlstring .= '<FileType>XML</FileType><FileFormat>PDF</FileFormat>';
        $xmlstring .= '<Delivery>';
        if (in_array($this->vendorinfo['PMPLUSREMITTANCETYPE'], array('Email', 'Fax'))) {

            if ($this->vendorinfo['PMPLUSREMITTANCETYPE'] == '') {
                $gErr->addError('WF-0089', GetFL(), 'The Payment Manager Plus remittance type for the receiver is missing.');
                return false;
            }

            $xmlstring .= '<DeliveryType>'.isl_strtoupper($this->vendorinfo['PMPLUSREMITTANCETYPE']).'</DeliveryType>';

            $xmlstring .= '<DeliveryContactName>To Whom it may concern</DeliveryContactName>';

            if ($this->vendorinfo['PMPLUSREMITTANCETYPE'] == 'Fax') {
                if ($this->vendorinfo['PMPLUSFAX'] == '') {
                    $gErr->addError('WF-0090', GetFL(), 'The Payment Manager Plus remittance fax number for the receiver is missing.');
                    return false;
                }
                $xmlstring .= '<DeliveryFaxNumber>'.$this->vendorinfo['PMPLUSFAX'].'</DeliveryFaxNumber>';
            } else {
                if ($this->vendorinfo['PMPLUSEMAIL'] == '') {
                    $gErr->addError('WF-0091', GetFL(), 'The Payment Manager Plus remittance email address for the receiver is missing.');
                    return false;
                }
                $xmlstring .= '<DeliveryEmailAddress>'.$this->vendorinfo['PMPLUSEMAIL'].'</DeliveryEmailAddress>';
            }
        }
        $xmlstring .= '</Delivery>';
        $xmlstring .= '</FileOut>';
        $xmlstring .= '</DocDelivery>';

        return $xmlstring;

    }

    /**
     * @param string[] $address
     * @param bool     $addreq
     * @param string   $party
     *
     * @return bool
     */
    function ValidateAddress($address, $addreq, $party)
    {

        global $gErr;
        
        $ok = true;
        
        $ok = $ok && $this->ValidateCommonAddrFlds($address, $addreq, $party);

        if($ok) {
            if ($address['STATE'] != '') {
                $len = isl_strlen($address['STATE']);
                if ($len != 2 && $len != 3) {
                    $gErr->addIAError('WF-0036', GetFL(), "The state address of the $party should be 2 characters for states or 3 characters for provinces. No punctuation, spaces or special characters are allowed.", ['PARTY' => $party]);
                    return false;
                }
            }

            $len = isl_strlen($address['ZIP']);
            if ($address['ZIP'] != '' && $len != 5 && $len != 6 && $len != 9) {
                $gErr->addIAError('WF-0092', GetFL(), sprintf("The zip code length of the %s should be 5 or 9 for US and 6 for international. No punctuation, spaces or special characters are allowed.", $party), ['PARTY' => $party]);
                return false;
            }

            if ($address['COUNTRYCODE'] == '' && $address['COUNTRY'] != '' && bccomp(isl_strlen($address['COUNTRY']), '30') > 0) {
                $gErr->addIAError('WF-0093', GetFL(), "The country address of the $party should be no more than 30 characters.", ['PARTY' => $party]);
                return false;
            }

            return true;
        }else{
            return false;
        }
    }


    /**
     * @param string[] $address
     * @param bool $addreq
     * @param string $party
     *
     * @return bool
     */
    function ValidateCommonAddrFlds($address,$addreq,$party)
    {
        global $gErr;
        
        $addresslength = 55;
        $citylength = 30;
        $correctionMsg = '';
        if ($this->paymethod == 'WF USD Wire') {
            $addresslength = 35;
            $citylength = 25;
        }

		if ( $addreq ) {
            $errFlds = array();
            if ( $address['ADDRESS1'] == '' ) {
                $errFlds[] = I18N::getSingleToken('IA.ADDRESS_LINE_1');
            }
            if ( $address['CITY'] == '' ) {
                $errFlds[] = I18N::getSingleToken('IA.CITY');
            }
            if ( $address['STATE'] == '' ) {
                $errFlds[] = I18N::getSingleToken('IA.STATE_PROVINCE_REGION');
            }
            if ( $address['ZIP'] == '' ) {
                $errFlds[] = I18N::getSingleToken('IA.ZIP_CODE');
            }
            if ( $this->vendorinfo['ACHREMITTANCETYPE'] == 'IAT' &&
                ($address['COUNTRYCODE'] == '' || GetCountryName($address['COUNTRYCODE']) == '') ) {
                $errFlds[] = I18N::getSingleToken('IA.COUNTRY');
            } else if ( $address['COUNTRYCODE'] == '' && $address['COUNTRY'] == '' ) {
                $errFlds[] = I18N::getSingleToken('IA.COUNTRY');
            }

            if ( count($errFlds) > 0 ) {
                $errFldsString = implode(", ",$errFlds);
                if ( $party == 'receiver party' ) {
                    $correctionMsg = '-Out-of-date address: Click the refresh icon beside the contact fields, then try again -Missing address: Add the address to the vendor record, then try again.';
                    $errorMsg = "The following address information of the $party is out-of-date or missing.- $errFldsString";
                    $gErr->addIAError('WF-0097', GetFL(), $errorMsg,['PARTY' => $party, 'ERROR_FIELDS' => $errFldsString],  '', [], $correctionMsg);
                } else {
                    $errorMsg = "The following address information of the $party is missing.- $errFldsString";
                    $gErr->addIAError('WF-0098', GetFL(), $errorMsg,['PARTY' => $party, 'ERROR_FIELDS' => $errFldsString],  '');
                }
                return false;
            }
        }

        if (bccomp(isl_strlen($address['ADDRESS1']), $addresslength) > 0) {
			$gErr->addIAError('WF-0094', GetFL(), "The address line 1 of the $party should be no more than $addresslength characters.", ['PARTY' => $party, 'ADDRESSLENGTH' => $addresslength]);
			return false;
		}

        if (bccomp(isl_strlen($address['ADDRESS2']), $addresslength) > 0) {
            $gErr->addIAError('WF-0095', GetFL(), "The address line 2 of the $party should be no more than $addresslength characters.", ['PARTY' => $party, 'ADDRESSLENGTH' => $addresslength]);
            return false;
        }

        if (bccomp(isl_strlen($address['CITY']), $citylength) > 0) {
            $gErr->addIAError('WF-0096', GetFL(), sprintf('The city address of the %1$s should be no more than %2$s characters.', $party, $citylength), ['PARTY' => $party, 'CITYLENGTH' => $citylength]);
            return false;
        }

        return true;
    }

    /**
     * @param string $accountid
     */
    function GetBankInfo($accountid) 
    {
        global $_userid;

        $gManagerFactory = Globals::$g->gManagerFactory;
        $company = GetAcctCompany($_userid);

        $this->bankinfo = self::$cachedWFPMDataMap['bankaccount'][$accountid];
        if(!isset($this->bankinfo)) {
            /** @var CheckingAccountManager $checkingAcctMgr */
            $checkingAcctMgr = $gManagerFactory->getManager('checkingaccount');
            $this->bankinfo = $checkingAcctMgr->Get($accountid);
            self::$cachedWFPMDataMap['bankaccount'][$accountid] = $this->bankinfo;
        }

        $this->bankinfo['MAILADDRESS']['ZIP'] = str_replace('-', '', $this->bankinfo['MAILADDRESS']['ZIP']);

        $this->compinfo['TITLE'] = $company['TITLE'];

        // get the checking account print check address, i.e. contact info address
        if ($this->bankinfo['CHECKINFOCONTACT']['CONTACTNAME'] != '') {
            $this->compinfo['NAME'] = $this->bankinfo['CHECKINFOCONTACT']['COMPANYNAME'];
            $this->compinfo['ADDRESS1'] = $this->bankinfo['CHECKINFOCONTACT']['MAILADDRESS']['ADDRESS1'];
            $this->compinfo['ADDRESS2'] = $this->bankinfo['CHECKINFOCONTACT']['MAILADDRESS']['ADDRESS2'];
            $this->compinfo['CITY'] = $this->bankinfo['CHECKINFOCONTACT']['MAILADDRESS']['CITY'];
            $this->compinfo['STATE'] = $this->bankinfo['CHECKINFOCONTACT']['MAILADDRESS']['STATE'];
            $this->compinfo['ZIP'] = $this->bankinfo['CHECKINFOCONTACT']['MAILADDRESS']['ZIP'];
            $this->compinfo['COUNTRY'] = $this->bankinfo['CHECKINFOCONTACT']['MAILADDRESS']['COUNTRY'];
            $this->compinfo['COUNTRYCODE'] = $this->bankinfo['CHECKINFOCONTACT']['MAILADDRESS']['COUNTRYCODE'];
        }

        // if entity level, use the entity level contact address
        $ismega = IsMultiEntityCompany();
        $ctx = GetContextLocation();
        if ($this->compinfo['NAME'] == '' && $ismega && $ctx) {
            $entRecordNo  = self::$cachedWFPMDataMap['location'][$ctx];
            if(!isset($entRecordNo)) {
                $qry = array( "select location_no from location where cny# = :1 and record# = :2", GetMyCompany(), $ctx );
                $entres = QueryResult($qry);
                $entRecordNo = $entres[ 0 ][ 'LOCATION_NO' ];
                self::$cachedWFPMDataMap['location'][$ctx] = $entRecordNo;
            }

            // if the enitty is same, then should not execute below query again rather take it from the cache
            $entData = self::$cachedWFPMDataMap['locationEntity'][$entRecordNo];
            if(!isset($entData)) {
                $filter = array(
                    'selects' => array( 'REPORTPRINTAS', 'CONTACTINFO.MAILADDRESS.ADDRESS1',
                        'CONTACTINFO.MAILADDRESS.ADDRESS2', 'CONTACTINFO.MAILADDRESS.CITY',
                        'CONTACTINFO.MAILADDRESS.STATE', 'CONTACTINFO.MAILADDRESS.ZIP',
                        'CONTACTINFO.MAILADDRESS.COUNTRY', 'CONTACTINFO.MAILADDRESS.COUNTRYCODE' ),
                    'filters' => array( array( array( 'LOCATIONID', '=', $entRecordNo ) ) )
                );
                $locEntityMgr = $gManagerFactory->getManager('locationentity');
                $entData = $locEntityMgr->GetList($filter);
                self::$cachedWFPMDataMap['locationEntity'][$entRecordNo] = $entData = $entData[ 0 ];
            }

            $this->compinfo['NAME'] = ($entData['REPORTPRINTAS'] != '' ? $entData['REPORTPRINTAS'] : $company['NAME']);
            $this->compinfo['ADDRESS1'] = $entData['CONTACTINFO.MAILADDRESS.ADDRESS1'];
            $this->compinfo['ADDRESS2'] = $entData['CONTACTINFO.MAILADDRESS.ADDRESS2'];
            $this->compinfo['CITY'] = $entData['CONTACTINFO.MAILADDRESS.CITY'];
            $this->compinfo['STATE'] = $entData['CONTACTINFO.MAILADDRESS.STATE'];
            $this->compinfo['ZIP'] = $entData['CONTACTINFO.MAILADDRESS.ZIP'];
            $this->compinfo['COUNTRY'] = $entData['CONTACTINFO.MAILADDRESS.COUNTRY'];
            $this->compinfo['COUNTRYCODE'] = $entData['CONTACTINFO.MAILADDRESS.COUNTRYCODE'];
        }

        // if not found, get the company address
        if ($this->compinfo['NAME'] == '') {
            $this->compinfo['NAME'] = $company['NAME'];
            $this->compinfo['ADDRESS1'] = $company['ADDRESS1'];
            $this->compinfo['ADDRESS2'] = $company['ADDRESS2'];
            $this->compinfo['CITY'] = $company['CITY'];
            $this->compinfo['STATE'] = $company['STATE'];
            $this->compinfo['ZIP'] = $company['ZIPCODE'];
            $this->compinfo['COUNTRY'] = $company['COUNTRY'];
            $this->compinfo['COUNTRYCODE'] = $company['COUNTRYCODE'];
        }

        $this->compinfo['ZIP'] = str_replace('-', '', $this->compinfo['ZIP']);
    }

    
    /**
     * if $str ends with XML encoded chars (&lt;, &amp;, &gt;, &quot;, &apos; etc) 
     * then trim should not happen in between of these encoded chars. 
     * 
     * @param string  $str   string
     * @param int $start start
     * @param int $len   length
     * 
     * @return string
     */
    function trimXMLEncodedString($str, $start, $len)
    {
        $a = isl_substr($str, ($start + $len - 6), 6);

        $pos = (int) strpos($a, '&');

        if ( $pos > 0 ) {

            $len = $len - (6 - $pos);
        }

        return isl_substr($str, $start, $len);
    }
    
    /**
     * preProcessWFPMPaymentRequest
     * 
     * @param array $values
     * 
     * @return bool
     */
    public static function preProcessWFPMPaymentRequest(&$values)
    {
        $ok = true;
        $kAPid = Globals::$g->kAPid;
        $wfpayMethodMap = array(
            'WF Check' => 'Outsourced Check',
            'WF Domestic ACH' => 'ACH',
            'WF USD Wire' => 'Wire',
        );

        if ( $values['PRXBATCHKEY'] == '' ) {
            $params = array(
                'PAYMETHOD' => $wfpayMethodMap[$values['PAYMENTMETHOD']],
                'WHENCREATED' => GetCurrentDate(), // should not use payment date
                'CURRENCY' => 'USD', // currently only support USD
                //'STATE'    => 'A',
                'AMOUNT' => 0,
                'NUMOFPYMT' => 0,
                'MODULEKEY' => $kAPid
            );

            $wfpmbatchMgr = Globals::$g->gManagerFactory->getManager('wfpmbatch');
            if ( !$wfpmbatchMgr->add($params) ) {
                $gErr = Globals::$g->gErr;
                $gErr->addError('WF-0005', __FILE__ . ':' . __LINE__, 'Cannot create prxatch');
                $ok = false;
            }

            $values['DOCNUMBER'] = self::generateWFPMPaymentID($values['RECORDNO']);
            $values['PRXBATCHKEY'] = $params[':RECORDNO'];
        }

        return $ok;
    }

    /**
     * processWFPMPaymentRequest
     * 
     * @param array $values
     * @param array[] $paymentdetail
     * 
     * @return bool
     */
    public static function processWFPMPaymentRequest(&$values, $paymentdetail)
    {
        $ok = true;

        $gManagerFactory = Globals::$g->gManagerFactory;
        $appymtMgr = $gManagerFactory->getManager('appymtdetail');
        $params = array(
            'selects' => array('APBILL.RECORDNO',
                'APBILL.RECORDTYPE', 'APBILL.RECORDID', 'APBILL.DOCNUMBER', 'APBILL.DESCRIPTION',
                'APBILL.TOTALENTERED', 'APBILL.TOTALSELECTED', 'APBILL.TRX_TOTALSELECTED',
                'APBILL.BILLTOPAYTOKEY'),
            'columnaliases' => array('RECORDNO', 'RECORDTYPE', 'RECORDID', 'DOCNUMBER', 'DESCRIPTION',
                'TOTALENTERED', 'TOTALSELECTED', 'TRX_TOTALSELECTED', 'BILLTOPAYTOKEY'),
            'filters' => array(
                array(
                    array('PAYMENTKEY', '=', $values['RECORDNO']),
                ),
            ),
        );
        $billData = $appymtMgr->GetList($params);

        $wfpmpayxml = false;
        $batchTotal = 0;
        $noOfPymts = 0;
        foreach ( $paymentdetail as $detail ) {
            foreach ( $billData as $bill ) {
                if ( $bill['RECORDNO'] == $detail['RECORDKEY'] ) {
                    $pRequest = $bill;
                }
            }
            $pRequest['totalselected'] = $detail['TRX_PAYMENTAMOUNT'];
            $pRequest['trx_credit'] = 0; //TODO
            $pRequest['discount'] = 0;  //TODO
            $pRequest['accountid'] = $values['FINANCIALENTITY'];
            $pRequest['WFPMPAYMENTID'] = $values['DOCNUMBER'];

            if ( $values['PAYMENTMETHOD'] == BasePymtManager::WFCHECK_PAYMENTMETHOD ) {
                $wfpm = new wfpm_check();
            } elseif ( $values['PAYMENTMETHOD'] == BasePymtManager::WFACH_PAYMENTMETHOD ) {
                $wfpm = new wfpm_ach();
            } else {
                $wfpm = new wfpm_wire();
            }

            $wfpmpayxml .= $wfpm->CreateWFPayments($pRequest, $values['VENDORID']);
            $batchTotal = $batchTotal + $detail['TRX_PAYMENTAMOUNT'];
            $noOfPymts++;
        }

        if ( $wfpmpayxml === false ) {
            $ok = false;
        } else {
            $params = array(
                'RECORDNO' => $values['PRXBATCHKEY'],
                'AMOUNT' => $batchTotal,
                'NUMOFPYMT' => $noOfPymts,
                'PRBATCHKEY' => $values['PRBATCHKEY'],
                'PAYMENT_XML' => TwoWayEncrypt($wfpmpayxml)
            );

            $wfpmbatchMgr = $gManagerFactory->getManager('wfpmbatch');
            if ( !$wfpmbatchMgr->set($params) ) {
                $gErr = Globals::$g->gErr;
                $gErr->addError('WF-0006', __FILE__ . ':' . __LINE__, 'Cannot update prxatch');
                return false;
            }
        }

        return $ok;
    }

    /**
     * @param string $recno
     *
     * @return string
     */
    public static function generateWFPMPaymentID($recno)
    {
        return GetMyCompany() . "-" . $recno;
    }

}
