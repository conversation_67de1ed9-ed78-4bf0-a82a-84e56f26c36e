<?

class CheckRunEditor extends Editor
{
    /**
     * User info specific tokens
     * @var string[]
     */
    protected $additionalTokens = [
        'IA.ENTER_CHECK_NUMBER',
        'IA.CHECK_RUN_SHOULD_BE_NUMBER',
        'IA.ONE_CHECK_PRINTED_STUBS',
        'IA.VENDOR_ID',
        'IA.VENDOR_NAME',
        'IA.AMOUNT',
        'IA.CHECK',
        'IA.DATE',
        'IA.BANK_ID_NAME',
        'IA.ENTITY',
        'IA.BLANK_CHECK_STOCK_MICR',
        'IA.PRE_PRINTED_CHECK_STOCK'
    ];

    /**
     * @var string[] $checkStockValue
     */
    var $checkStockValue = array('Blank check stock (MICR)' => 'B', 'Pre-printed check stock' => 'P');

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        $_params['helpfile'] = 'Adding_a_Check_Run';
        $_params['title'] = 'IA.CHECK_RUN_INFORMATION';
        parent::__construct($_params);
        array_push($this->textTokens,
                'IA.ADD_OR_EDIT',
                'IA.SELECTED_ITEMS',
                'IA.AVAILABLE_ITEMS',
                'IA.ADD_ALL',
                'IA.REMOVE_ALL',
                'IA.DONE_TEXT',
                'IA.SELECT_TO_MOVE_ITEMS_HERE',
                'IA.SHOW_MORE',
                'IA.SELECT_NO_DASHES');
    }

    /**
     * @param int       $renameTerms
     * @param string    $dt
     * @param string    $mod
     * @param bool      $iscustomdoc
     *
     * @return bool
     */
    function GetDataForPrinting($renameTerms = 0, $dt = '', $mod = '', $iscustomdoc = false)
    {
        if(IsMultiEntityCompany() && !GetContextLocation()) {
            SetReportViewContext();
        }

        $r = Request::$r->_r;
        $qry = "SELECT pdfblob,title FROM checkrun WHERE cny# = :1 AND record# = :2";
        $result = QueryResult(array($qry, GetMyCompany(), $r));
        //$checkrunMgr = Globals::$g->gManagerFactory->GetManager('checkrun');
        //$result = $checkrunMgr->GetRaw($r);
        $pdf = databaseStringUncompress($result[0]['PDFBLOB']);
        //eppp_p($pdf); dieFL();
        //$pdf = "'".$result[0]['PDFBLOB']."'";
       
        $filename = 'report.pdf';
        header("Content-type: application/pdf");
        header("Content-Disposition: filename=\"$filename\"");         
        header("Content-Length: " . strlen($pdf));
        echo($pdf);
        return true;
    }

    /**
     * @return bool
     */
    function CanEdit()
    {
        $objId = Request::$r->{Globals::$g->kId};
        $entity = $this->_params['entity'];    

        $vendorMgr = Globals::$g->gManagerFactory->getManager($entity);
        $res = $vendorMgr->GetRaw($objId);

        if($res[0]['STATE'] != 'O') {
            return false;
        }
        return parent::CanEdit();
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    function ProcessCreateAction(&$_params)
    {
        Editor::ProcessCreateAction($_params);
        global $gErr;
        $_sess = Session::getKey();
        if(!$gErr->hasErrors()) {
            $obj =& Request::$r->GetCurrentObject();
            $title = $obj['TITLE'];
            $printOn = $this->checkStockValue[$obj['CHECKSTOCK']];
            $acct =  ($obj['CHECKINGACCOUNT']) ?: '';
            $op    = GetOperationId('ap/activities/checkrunfilter');
            $this->done = "reporteditor.phtml?.op=$op&.do=edit&.sess=$_sess&CHECKRUN=$title&ACCOUNTID=$acct&PRINTON=$printOn";
        }
        return true;
    }

    /**
     * @param array  $_params
     * @param string $entobjname
     */
    function hideCheckingAccount(&$_params,
        /** @noinspection PhpUnusedParameterInspection */ $entobjname)
    {
        $obj =& Request::$r->GetCurrentObject();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $title = $obj['TITLE'];

        $_found = array();
        $this->MatchTemplates($_params, array('path' => 'CHECKSTOCK'), $_found);
        if ($_found) {
            $_found[0]['onchange_js'] = 'javascript:hideBankaccount(this.value);';
            $_found[0]['onload'] = 'javascript:hideBankaccount(this.value);';
        }
    }

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss include the YUI css files
     */
    function showScripts($addYuiCss = true)
    {
        parent::showScripts($addYuiCss);
        ?>
      <script src="../resources/js/checkrun.js"></script>
        <?
    }

    /**
     * @param array $_field
     * @param bool  $_inlineitem
     * @param bool  $encodeLabel
     */
    function ShowSimpleFieldLabel(&$_field, $_inlineitem = false, $encodeLabel = true)
    {
        // Make CHECKINGACCOUNT as required
        if($_field['path'] == 'CHECKINGACCOUNT') {
            $_field['required'] = "1";
        }
        return parent::ShowSimpleFieldLabel($_field, $_inlineitem);
    }

    /**
     * @param array $_params
     */
    function Editor_Instantiate(&$_params)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $source = "CheckRunEditor::Editor_Instantiate";
        parent::Editor_Instantiate($_params);

        $this->hideCheckingAccount($_params, 'CHECKSTOCK');

        $num_fields = count($_params['allfields']);

        for ($num=0; $num < $num_fields; $num++) {
            if ($_params['allfields'][$num]['path'] == 'CHECKINGACCOUNT' ) {
                $bnkMgr = Globals::$g->gManagerFactory->getManager('checkingaccount');
                $banks = $bnkMgr->getPPCSBanks(array('P'));
                $_params['allfields'][$num]['type']['validvalues'] = array_values($banks);
                $_params['allfields'][$num]['type']['validlabels'] = array_values($banks);
            }
            if (in_array($_params['allfields'][$num]['path'], array('CHECKSTOCK','SORTORD')) ) {
                foreach ($_params['allfields'][$num]['type']['validlabels'] as &$label)
                {
                    $label = GT($this->textMap, $label);
                }
            }
        }

        $_found = array();
        $this->MatchTemplates($_params, array('path' => 'CHECKSPERPAGE'), $_found);
        if ($_found) {
            $_found[0]['onchange'] = 'javascript:ToogleStubDetails(this.value);';
        }
    }

    function PrintOnLoad()
    {
        Editor::PrintOnLoad();
        $obj =& Request::$r->GetCurrentObject();
        $printOn = $obj['CHECKSTOCK'];

        $printOn = ($printOn != '') ? $printOn : 'Blank check stock (MICR)' ;
        echo " hideBankaccount('$printOn'); ";

        //Load the Checks Per Page Option
        echo "ToogleStubDetails(".$obj['CHECKSPERPAGE'].");";
    }

    /**
     * @param array $_field
     *
     * @return bool
     */
    function printHelpText($_field){
        if ($_field['path'] == 'CHECKSPERPAGE') {
            ?>
            <font color="#777777" face="verdana, arial" size="1">
                <br/><?php echo GT($this->textMap, $_field['help_text']);?>
            </font>
        <?
        }
        return true;
    }

}
