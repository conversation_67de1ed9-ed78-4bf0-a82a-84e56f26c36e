<?

import('ARBatchPicker');

class ARPaymentBatchPicker extends ARBatchPicker
{
    function __construct()
    {
        parent::__construct('arpaymentbatch');
    }


    function BuildTable() 
    {
        parent::BuildTable();
    }


    /**
     * @return array|bool|null
     */
    function CalcFiltersLite()
    {

        $filters = parent::CalcFiltersLite();

        $onlinecard = Request::$r->_onlinecard;
        if(isset($onlinecard) && $onlinecard != '') {
            $filters[] = array ('ACCOUNTNOKEY', '=' , $onlinecard);
        }

        return $filters;
    }


    /**
     * @return array
     */
    function BuildQuerySpecAll()
    {
        $qspec = parent::BuildQuerySpecAll();
        $this->AddFilters($qspec);
        return $qspec;
    }


    /**
     * @return array
     */
    function BuildQuerySpec()
    {
        $qspec = parent::BuildQuerySpec();
        $this->AddFilters($qspec);
        return $qspec;
    }


    /**
     * @param array $qrySpec
     */
    function AddFilters(&$qrySpec)
    {
        $onlinecard = Request::$r->_onlinecard;
        $qrySpec['filters'][0][] = array ('OPEN', '=' , 'Open');
        if ($onlinecard) {
            $qrySpec['filters'][0][] = array ('ACCOUNTNOKEY', '=' , $onlinecard);
        } else {
            $qrySpec['filters'][0][] = array(
                'operator' => 'OR',
                'filters' => array(
                    array('accountnokey', 'ISNOTNULL'),
                    array('undepacct.acct_no', 'ISNOTNULL'))
            );
        }

        $field = Request::$r->_field;
        if ($field == '_obj__PRBATCH') {
            $basecurr = GetBaseCurrency();
            // Atlas Root base currency is null, hence show all the batches
            if(!empty($basecurr)) {
                $qrySpec['filters'][0][] = [ 'CURRENCY', 'ISNULL', '', 'R' ];
                $qrySpec['filters'][0][] = [ 'CURRENCY', '=', $basecurr ];
            }
        }
    }


    /**
     * @return string
     */
    function genGlobs()
    {
        $onlinecard = Request::$r->_onlinecard;
        $ret = parent::genGlobs();
        $ret .= "<g name='.onlinecard'>" . $onlinecard . "</g>";
        return $ret;
    }


}

