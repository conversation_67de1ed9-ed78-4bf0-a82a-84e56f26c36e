<?
/**
 *    FILE:
 *    AUTHOR:            <PERSON>
 *    DESCRIPTION:    entity for jointpayeebillresolve table
 *                  (between APBill - PRRECORD and CONTACT).
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
global $gRecordNoFormat;
global $gJointPayeeNameFormat;
global $gJointPayeePrintAsFormat;

$kSchemas['apbilljointpayee'] = [
    'children' => [
        'apbill' => ['fkey' => 'prrecordkey', 'invfkey' => 'record#', 'table' => 'prrecord', 'join' => 'outer'],
    ],
    'nexus' => [
        'apbill' => [
            'object' => 'apbill',
            'relation' => ONE2ONE,
            'field' => 'prrecordkey',
        ]
    ],
    'object' => [
        'RECORDNO',
        'JOIN<PERSON>AYEENA<PERSON>',
        'JOINTPAYEEPRINTAS',
        'APBILLKEY',
        'APBILLID',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'schema' => [
        'RECORDNO' => 'record#',
        'JOINTPAYEENAME' => 'jointpayeename',
        'JOINTPAYEEPRINTAS' => 'jointpayeeprintas',
        'APBILLKEY' => 'prrecordkey',
        'APBILLID' => 'apbill.recordid',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby'
    ],
    'publish' => [
        'RECORDNO',
        'JOINTPAYEENAME',
        'JOINTPAYEEPRINTAS',
        'APBILLKEY',
        'APBILLID',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat,
            ),
            'readonly' => true,
            'noapiadd' => true,
            'id' => 1,
        ],
        [
            'path'        => 'JOINTPAYEENAME',
            'desc'        => 'IA.JOINT_PAYEE',
            'fullname'    => 'IA.JOINT_PAYEE',
            'required'    => true,
            'type'        => array(
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 200,
                'format'    => $gJointPayeeNameFormat,
            ),
            'id' => 2,
        ],
        [
            'path'        => 'JOINTPAYEEPRINTAS',
            'desc'        => 'IA.JOINT_PAYEE_PRINT_AS',
            'fullname'    => 'IA.JOINT_PAYEE_PRINT_AS',
            'required' => true,
            'type'        => array(
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 148,
                'format'    => $gJointPayeePrintAsFormat,
            ),
            'id' => 3,
        ],
        [
            'path' => 'APBILLKEY',
            'desc' => 'IA.AP_BILL_KEY',
            'fullname' => 'IA.AP_BILL_KEY',
            'required' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat,
            ),
            'hidden' => true,
            'noapiset' => true,
            'id' => 4,
        ],
        [
            'path' => 'APBILLID',
            'desc' => 'IA.BILL_NUMBER',
            'fullname' => 'IA.BILL_NUMBER',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 45
            ),
            'readonly' => true,
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 5,
        ],

        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'api' => [
        'PERMISSION_READ' => 'lists/apbill/view',
        'PERMISSION_CREATE' => 'lists/apbill/edit',
        'PERMISSION_UPDATE' => 'lists/apbill/edit',
        'PERMISSION_DELETE' => 'lists/apbill/edit',
    ],
    'platformProperties' => [
        // PRR needs this setting. If you intend to change it, please confirm it first with someone from the Platform team
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ],
    'table' => 'apjointpayee',
    'printas'    =>    'IA.JOINT_PAYEE',
    'pluralprintas' => 'IA.JOINT_PAYEES',
    'module' => 'ap',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'nosysview' => true,
    'auditcolumns' => true,
    'audittrail_disabled' => true,
    'description' => 'IA.JOINT_PAYEE_DESCRIPTION',
    'dbsorts' => [ ['RECORDNO', 'ASC'] ],
    'allowTriggers' => false,
];