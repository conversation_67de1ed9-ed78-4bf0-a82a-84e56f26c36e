<?php

/**
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 Intacct Corporation, All Rights Reserved
 */
class STXFilePoller extends IMSPoller
{
    // List of accepted Dispatcher types
    // Key: represents the entity queue
    // Value: must be unique and a single char - it will be stored in DB
    const DISPATCHER_TYPES = [
        'S' => 'smarteventjobqueue',
        'I' => 'sforcequeue',
        'F' => 'stxfileuploadqueue',
        'E' => 'stxexternalqueue',
        'D' => 'digitalnetworksyncqueue',
        'U' => 'file1099submissionqueue',
        'P' => 'podigitalnetworksyncqueue',
        'W' => 'stxwebhookqueue',
    ];
    const RETRY_DELAY = 600; // 10 minutes (seconds)
    const RETRY_DELAY_24_HRS = 86400; // 24hrs (seconds)

    const RETRY_COUNT = 10;

    /* @var string $topic is the WPB Companny Id for this tenant */
    private string $topic;

    /* @var string $doctype */
    private string $doctype;

    /* @var array $object */
    private array $object;

    /* @var string $objectrecid */
    private string $objectrecid;

    /* @var string $details */
    private string $details;

    /*  @var string $state */
    private string $state;

    /* @var int|null $locationkey */
    private int|null $locationkey;

    /* @var string $type */
    private string $type;

    private string $companyId;


    /**
     * WPBBankFilePoller constructor.
     *
     * @param string $topic
     * @param string $doctype
     * @param array $object
     * @param string $objectrecid
     * @param string $details
     * @param string $state
     * @param int|null $locationkey
     * @param string $type
     *
     */
    public function __construct(string $topic, string $doctype, array $object, string $objectrecid, string $details, string $state, int|null $locationkey, string $type)
    {
        parent::__construct();
        $this->topic = $topic;
        $this->doctype = $doctype;
        $this->object = $object;
        $this->objectrecid = $objectrecid;
        $this->details = $details;
        $this->state = $state;
        $this->locationkey = $locationkey;
        $this->type = $type;
    }

    /**
     * execute
     *
     * @param string  $packageId
     * @param int    &$retryDelay
     *
     * @return bool
     */
    protected function execute($packageId, &$retryDelay): bool
    {
        $metric = new MetricStxpollerJobs();
        $metric->startTime();
        $metric->setFileid($this->objectrecid);
        $metric->setRetryDelay($retryDelay);
        logToFileWarning('STXFilePoller-Log : file=' . __FILE__ . ' method=' . __FUNCTION__
            . " FIle ID = $this->objectrecid"
            . " fifoqueuetype = $this->type"
            . ' iterationCount='
            . $this->iterationCount . ' Entering Package ID=' . $packageId . ' time='
            . GetCurrentUTCTimestamp());

        $fifoQueueJob = [];

        $fifoQueueJob['topic'] = $this->topic;
        $fifoQueueJob['doctype'] = $this->doctype;
        $fifoQueueJob['object'] = databaseStringCompress(json_encode($this->object, JSON_THROW_ON_ERROR));
        $fifoQueueJob['objectrecid'] = $this->objectrecid;
        $fifoQueueJob['details'] = $this->details;
        $fifoQueueJob['state'] = $this->state;
        $fifoQueueJob['createdBy'] = UserInfoManager::SYSTEMUSER;
        $fifoQueueJob['locationkey'] = $this->locationkey;
        $fifoQueueJob['type'] = $this->type;
        $fifoQueueJob['whencreated'] = GetCurrentUTCTimestamp();

        /* @var STXFileUploadQueueManager|STXExternalQueueManager $queueManager */
        $queueManager = Globals::$g->gManagerFactory->getManager(self::DISPATCHER_TYPES[$this->type]);
        if (empty($queueManager)) {
            logToFileCritical('STXFILEPoller : Queue Manager not found for type: ' . $this->type);
            return false;
        }

        $source = 'STXFilePoller::execute';
        $ok = $queueManager->beginTrx($source);

        $params = array(
            'selects' => array(
                'RECORDNO'
            ),
            'filters' => array(
                array(
                    array('OBJECTRECID', '=', $this->objectrecid),
                    array('STATE', '=', 'Q'),
                ),
            ),
        );
        $resultFifoQueue = $queueManager->GetList($params);
        if(empty($resultFifoQueue)){
            $ok = $ok && $queueManager->add($fifoQueueJob);
            if($ok){
                $metric->setAddedJobInQueue('T');
            }
        }else{
            $ok = true;
            $metric->setAddedJobInQueue('F');
        }

        if ($ok) {
            if ($queueManager->commitTrx($source)) {
                $retryDelay = 0;
                logToFileInfo('STXFILEPoller : Job added in queue for type: ' . $this->type);
            } else {
                $queueManager->rollbackTrx($source);
                logToFileCritical('STXFILEPoller : Failed to commit transaction for type: ' . $this->type);
                $ok = false;
            }
        } else {
            $queueManager->rollbackTrx($source);
            $metric->setAddedJobInQueue('FL');
            logToFileCritical('STXFILEPoller : Failed to add job in queue for type: ' . $this->type);
            $ok = false;
        }

        logToFileWarning('STXFilePoller-Log : '
            . " FIle ID = $this->objectrecid"
            . " fifoqueuetype = $this->type"
            . " iterationCount= $this->iterationCount " . ' Exiting Package ID=' . $packageId . ' time='
            . GetCurrentUTCTimestamp());
        $metric->stopTime();
        $metric->publish();
        return $ok;
    }

    /**
     * @return string the IMS sender
     */
    protected function getSender() : string
    {
        return 'STXFILE_POLLER';
    }

    /**
     * get description
     *
     * @return string
     */
    protected function getDescription(): string
    {
        return 'Company: ' . GetMyCompany() . '. STX File Poller';
    }
}