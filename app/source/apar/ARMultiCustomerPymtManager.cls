<?php
/**
 * Manager class for the AR Multi Customer Payments
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Intacct Corporation All, Rights Reserved
 */

class ARMultiCustomerPymtManager extends SubLedgerPymtManager
{
    /** @var bool $isMultiEntityPymtDelete */
    private static $isMultiEntityPymtDelete = false;
    /** Use Traits for arpymt methods*/
    use ARPymtTrait;
    /**
    * Get the payment key field path in the payment details data structure
    *
    * @return string the payment key field path in the payment details data structure
    */
    protected function getPaymentKeyPath()
    {
        return '';
    }

    /**
     * Get the payment entry key field path in the payment details data structure
     *
     * @return string the payment entry key field path in the payment details data structure
     */
    protected function getPaymentEntryKeyPath()
    {
        return '';
    }

    /**
     * Get the payment date field path in the payment details data structure
     *
     * @return string the paymentdate field path in the payment details data structure
     */
    protected function getPaymentDatePath()
    {
        return 'PAYMENTDATE';
    }

    /**
     * Get the payment amount field path in the payment details data structure
     *
     * @return string the the payment amount field path in the payment details data structure
     */
    protected function getPaymentAmountPath()
    {
        return '';
    }

    /**
     * Get the payment base amount field path in the payment details data structure
     *
     * @return string the payment amount field path in the payment details data structure
     */
    protected function getPaymentBaseAmountPath()
    {
        return '';
    }

    /**
     * Get the record types of the transaction we are paying
     *
     * @return string[] the record type of the transaction we are paying
     */
    protected function getPaidTransactionRecordTypes()
    {
        return [];
    }

    /**
     * Get the record types of the parent transactions we need to fetch for the payment
     *
     * @return string[] the record types of the parent transactions we need to fetch for the payment
     */
    protected function getParentTransactionRecordTypes()
    {
        return [];
    }

    /**
     * Get the list of valid payment methods
     *
     * @param string|null $paymentProvider
     * @param string|null $providerPayMethod
     *
     * @return array a list of valid payment method
     */
    public function getValidPaymentMethod(string $paymentProvider = null, string $providerPayMethod = null)
    {
        $payMethod = parent::getValidPaymentMethod($paymentProvider, $providerPayMethod);
        $arPaymethod = array(
            BasePymtManager::CC_PAYMENTMETHOD,
            BasePymtManager::ACH_PAYMENTMETHOD,
            BasePymtManager::ONLINE_ACH_DEBIT_PAYMENTMETHOD,
            BasePymtManager::ONLINE_CHARGECARD_PAYMENTMETHOD
        );

        return array_merge($payMethod, $arPaymethod);
    }


    /**
     * @return string
     */
    protected function getDefaultStateForSubmitAction()
    {
        return self::DRAFT_RAWSTATE;
    }

    /**
     * Initialize the class vaiables and data for update
     *
     * @param array &$values the transaction data
     *
     * @return bool false if error else true
     */
    protected function initialize(&$values)
    {
        $ok = true;
        // Does the transaction exists already ?
        $result = $this->getHeaderInfo($values['RECORDNO']);

        // Keep the header information for futher processing
        $values['EXISTING_HEADER'] = $result;
        return $ok;
    }

    /**
     * Method to call the base payment creation.
     *
     * @param array     $payment
     *
     * @return bool
     */
    public function createPayment(&$payment)
    {
        return parent::regularAdd($payment);
    }

    /**
     * @return bool
     */
    public static function isMultiEntityPymtDelete(): bool
    {
        return self::$isMultiEntityPymtDelete;
    }

    /**
     * Translate the entries data
     *
     * This will contain all the basic, non-conditional data translations we should be doing on our PRENTRIES
     * independly from where they come from.
     *
     * @param array &$values the transaction data
     *
     * @return bool false if error else true
     */
    protected function translateEntries(&$values)
    {
        return true;
    }

    /**
     * Some of the payment related activities specific to various payment method eg: Amex, WF payments
     *
     * @param array $values the payment details data
     *
     * @return bool is this action is success or failure
     */
    protected function onAfterAdd(&$values)
    {
        // Don't do anything yet, lets do the post create activities in postAdd
        return true;
    }

    /**
     * After the payment we need to create payment detail entry, so lets return true.
     *
     * @return bool
     */
    protected function isPaymentDetailCreationAllowed()
    {
        return true;
    }

    /**
     * @return array|null|string
     */
    public function getPaymentRecordType()
    {
        return PRRECORD_TYPE_RECEIPT_MULTI_ENTITY;
    }

    /**
     * @param array $values
     * @param string $journalSymbol
     *
     * @return bool
     */
    protected function updateBatch($values, $journalSymbol)
    {
        return true;
    }

    /**
     * Change the transaction PR Batch
     *
     * @param array     $values
     * @param int       $origBatchKey  the original PR Batch Key
     * @param int       $newBatchKey   the new PR Batch Key
     * @param string    $journalSymbol the journal symbol
     *
     * @return bool false if error else true
     */
    protected function changeTransactionBatch($values, $origBatchKey, $newBatchKey, $journalSymbol)
    {
        // in case of payment, we dont need to post the batch from here, confirmPayment will be taking care
        return true;
    }

    /**
     * Figure out the batch date
     *
     * @param array $values the transaction data
     *
     * @return string the batch date
     */
    protected function getBatchDate($values)
    {
        if (!empty($values['RECEIPTDATE'])) {
            $batchDate = $values['RECEIPTDATE'];
        } else {
            $batchDate = parent::getBatchDate($values);
        }
        return $batchDate;
    }

    /**
     * Validate the transaction data entries
     *
     * This will contain all the basic, non-conditional data validations we should be doing on our PRENTRIES
     * independly from where they come from.
     *
     * @param array &$values the transaction data
     *
     * @return bool false if error else true
     */
    protected function validateEntries(&$values)
    {
        return true;
    }

    /**
     * Process the line items ( initialize values, build couplets, etc. )
     *
     * @param array &$values          the data
     * @param array &$impliedLocation the list of implied locations
     *
     * @return bool false if error else true
     */
    protected function processItems(&$values, &$impliedLocation)
    {
        // no line items in case of multi payment
        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function createOffsetLinks($values)
    {
        return true;
    }

    /**
     * Validate the bank account
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function validateBankAccount($values)
    {
        return $this->validateFinancialEntity($values);
    }

    /**
     * getAuditEntity - Return the audit trail object type name.  Usually, this is just the entity name.
     *   It should be rare to overwrite this method.
     *
     * @return string
     */
    function getAuditEntity()
    {
        return 'arpymt';
    }

    /**
     * @param int  $paymentKey
     * @param bool $recordType
     *
     * @return array
     */
    public static function getChildPaymentRecordsByParent(int $paymentKey, bool $recordType = false): array
    {
        $select = $recordType === true ? 'record#,recordtype': 'record#';
        $query = [
            "SELECT $select FROM prrecordmst WHERE cny# = :1 AND recordtype = :2 AND multientitypymtkey = :3",
            GetMyCompany(),
            PRRECORD_TYPE_RECEIPT,
            $paymentKey
        ];
        $result = QueryResult($query);
        $childPymt = [];
        if (!empty($result)) {
            if ($recordType) {
                $childPymt = array_column($result, 'RECORDTYPE', 'RECORD#');
            } else {
                $childPymt = array_column($result,'RECORD#');
            }
        }
        return $childPymt;
    }

    /**
     * Figure out the record type from the entity
     *
     * @return string the record type
     */
    public function getRecordType()
    {
        return PRRECORD_TYPE_RECEIPT_MULTI_ENTITY;
    }

    /**
     * @param $values
     *
     * @return bool
     */
    protected function isPaymentRecordType($values) : bool
    {
        return $values['RECORDTYPE'] === PRRECORD_TYPE_RECEIPT_MULTI_ENTITY;
    }

    /**
     *
     * @param array $values object data
     *
     * @return bool true on success, false on error
     */
    public function isApprovalsAllowed($values)
    {
        return false;
    }

    /**
     * @param bool $simulate
     * @param bool $fromGateway
     *
     * @return ARPaymentController|null
     */
    public function getPaymentController($simulate = false, $fromGateway = false)
    {
        return new ARPaymentController($simulate, $fromGateway);
    }

    /**
     * @param array                $payment
     * @param bool                 $fromService
     * @param PaymentResponse|null $paymentResponse
     *
     * @return bool
     */
    public function checkAndConfirmPayment(
        array $payment, bool $fromService = true, ?PaymentResponse $paymentResponse = null
    ): bool
    {
        $paymentKeys = self::getChildPaymentRecordsByParent($payment['RECORDNO']);
        if(empty($paymentKeys)) {
            return false;
        }

        // in case of multiple payment, we need to generate the sequence payment id
        SubLedgerPymtUtils::generateSequencePaymentID($this->getModuleKey(), true);
        $ok = true;
        // confirm the child payments
        $pymtMgr = Globals::$g->gManagerFactory->getManager('arpymt');
        foreach ($paymentKeys as $paymentKey) {
            // confirm all the rp payments and their child payments
            logToFileInfo(__FILE__ . ": " . $paymentKey . " Child payment is confirm ". $payment['RECORDNO']);
            // prepare the child payment data
            $childPayment = [
                'RECORDNO' => $paymentKey,
                'PAYMENTMETHOD' => $payment['PAYMENTMETHOD'],
                'RAWSTATE' => $payment['RAWSTATE'],
            ];
            $ok = $ok && $pymtMgr->checkAndConfirmPayment($childPayment, $fromService);
            if (!$ok) {
                break;
            }
        }

        unset($payment['ITEMS'], $payment['OFFSETS']);
        // confirm the rl record
        $ok = $ok && parent::checkAndConfirmPayment($payment, $fromService);

        return $ok;
    }

    /**
     * @param array                $payment
     * @param bool                 $fromService
     * @param PaymentResponse|null $paymentResponse
     *
     * @return bool
     */
    public function confirmPayment(array $payment, bool $fromService, ?PaymentResponse $paymentResponse = null): bool
    {
        $paymentController = $this->getPaymentController();
        $ok = $paymentController->multiEntityPymtProcessor->confirmPayment($payment, '', false);
        return $ok;
    }

    /**
     * Hook function for the sub classes to perform specific tasks right before the record is deleted
     *
     * @param mixed &$values  the transaction data
     *
     * @return bool  true on success and false on failure
     */
    protected function beforeDelete(&$values)
    {
        return true;
    }

    /**
     * API to remove the payment records.
     *
     * @param string|int    $ID Payment request id that need to be deleted.
     *
     * @return bool returns true for success else false.
     */
    public function Delete($ID)
    {
        logToFileInfo(__FILE__ . ": " . $ID . " Multi entity payment is selected for Delete");
        $paymentKeys = self::getChildPaymentRecordsByParent($ID);
        if(empty($paymentKeys)) {
            Globals::$g->gErr->addError(
                'AR-0557',
                __FILE__.":".__LINE__,
                'Payment record not found',
                'The payment record you are trying to delete is not found.'
            );
            return false;
        }
        $source = __CLASS__.'::'.__FUNCTION__;
        $ok = $this->_QM->beginTrx($source);
        // set this flag, so if user directly try to delete the child payment, will validate and throw an error
        // static variable
        self::$isMultiEntityPymtDelete = true;
        $pymtMgr = Globals::$g->gManagerFactory->getManager('arpymt');
        foreach ($paymentKeys as $paymentKey) {
            // delete all the rp payments and their child payments
            logToFileInfo(__FILE__ . ": " . $paymentKey . " Child payment is selected for Delete of ". $ID);
            $ok = $ok && $pymtMgr->Delete($paymentKey);
            if (!$ok) {
                break;
            }
        }
        // if isUpdatePayment is false, no need to keep the multi entity payment
        if (!SubLedgerPymtManager::isUpdatePayment()) {
            // Delete the Multi Entity Payment
            $ok = $ok && parent::delete($ID);
        }
        self::$isMultiEntityPymtDelete = false;

        if (!$ok || !$this->_QM->commitTrx($source)) {
            $ok = false;
            $msg = "Oops, we've encountered a glitch, review your payment, then try again.";
            Globals::$g->gErr->addError('AR-0556', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }
}
