<?php
/**
 * =============================================================================
 *
 * @filesource SubledgerSummaryManager.cls
 * <AUTHOR>
 * @copyright  2000,2009, Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 * =============================================================================
 */

abstract class SubledgerSummaryManager extends EntityManager
{
    const ALL_ENTITIES_LABEL = 'All entities';
    /** set OPEN_ACTION */
    const OPEN_ACTION = 'open';
    /** set CLOSE_ACTION*/
    const CLOSE_ACTION = 'close';
    /** @var string $moduleId */
    private $moduleId = '';
    /** @var string $subledgerAction */
    private $subledgerAction = '';
    /** @var string $subledgerModule */
    private $subledgerModule = '';

    /**
     * SubledgerSummaryManager constructor.
     *
     * @param array $_params
     */
    function  __construct($_params = [])
    {
        $this->setModule();
        parent::__construct($_params);
    }

    /**
     * @return mixed
     */
    abstract protected function setModule();

    /**
     * @return string
     */
    public function getModuleId()
    {
        return $this->moduleId;
    }

    /**
     * @param string $moduleId
     */
    public function setModuleId($moduleId)
    {
        $this->moduleId = $moduleId;
    }

    /**
     * @return string
     */
    public function getSubledgerAction()
    {
        return $this->subledgerAction;
    }

    /**
     * @param string $subledgerAction
     */
    public function setSubledgerAction($subledgerAction)
    {
        $this->subledgerAction = $subledgerAction;
    }

    /**
     * @return string
     */
    public function getSubledgerModule()
    {
        return $this->subledgerModule;
    }

    /**
     * @param string $subledgerModule
     */
    public function setSubledgerModule($subledgerModule)
    {
        $this->subledgerModule = $subledgerModule;
    }

    /**
     * @param string $ID
     * @param string $fields
     *
     * @return array|false
     */
    function get($ID, $fields=null)
    {
        $obj = array();
        $obj['OPENDATE'] = $this->getOpenDate();
        return $obj;
    }

    /**
     * @param string|bool $location
     * @param bool   $nodefault
     *
     * @return string
     */
    function getOpenDate($location='', $nodefault=false)
    {
        $stmt = array(
            "select startopendate from moduleopen where cny# = :1 and modulekey = :2",
            GetMyCompany(),
            $this->getModuleId()
        );
        $count = count($stmt);
        $location = ( $location == '' ? GetContextLocation() : $location );
        if($location){
            $stmt[0] .= ' and locationkey = :'.$count;
            $stmt[$count] = $location;
        }else{
            $stmt[0] .= ' and locationkey is null';
        }

        $res = QueryResult($stmt);

        $startopendate = ( $res[0]['STARTOPENDATE'] || $nodefault ? $res[0]['STARTOPENDATE'] : GetCompanyOpenDate() );
        return $startopendate ;
    }

    /**
     * Delete
     *
     * @param string $ID record no
     *
     * @return bool
     */
    function Delete($ID)
    {
        $gErr = Globals::$g->gErr;

        $msg = " This operation is not allowed!! ";
        $gErr->addError('SL-1001', __FILE__ . ':' . __LINE__, $msg);

        return false;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        return $this->set($values);
    }

    /**
     * @param string $locationKey
     */
    abstract public function getPeriods($locationKey);
    /**
     * @param array $values
     *
     * @return mixed
     */
    abstract protected function updatePRBatches($values);

    /**
     * @param string $batchkey
     *
     * @return mixed
     */
    abstract protected function updatePRBatch($batchkey);

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function openBatches($values)
    {
        $ok = true;
        $locationListValue = '';
        if(!empty($values['ENTITYRECS'])) {
            $locationListValue = implode(', ', $values['ENTITYRECS']);
        }
        $ok = $ok && OpenPRBatches($this->getSubledgerModule(),$values['PERIODRECNO'],true,
                                   $values['ENTITYRECS'],$locationListValue);
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function closeBatches($values)
    {
        $ok = true;
        $locationListValue = '';
        if(!empty($values['ENTITYRECS'])) {
            $locationListValue = implode(', ', $values['ENTITYRECS']);
        }
        $ok = $ok && ClosePRBatches($this->getSubledgerModule(),$values['PERIODRECNO'],$values['ENTITYRECS'],$locationListValue);
        return $ok;
    }

    /**
     * @param string $batchKey
     *
     * @return bool
     */
    protected function openBatch($batchKey)
    {
        $ok = true;
        $ok = $ok && OpenPRBatch($batchKey);
        return $ok;
    }

    /**
     * @param string $batchkey
     *
     * @return bool
     */
    protected function closeBatch($batchkey)
    {
        $ok = true;
        $ok = $ok && ClosePRBatch($batchkey);
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $gErr = Globals::$g->gErr;
        $source = "SubledgerSummaryManager::Set";
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->ValidateSelectedValues($values);

        if($ok) {
            if ( $values['PERIODRECNO'] != '' ) {
                //use abstract method changes
                $ok = $this->updatePRBatches($values);
                if ( ! $ok ) {
                    $subledgerAction = $this->getSubledgerAction();
                    $gErr->addIAError(
                        'SL-1002', __FILE__ . ":" . __LINE__,
                        "Unable to ". $subledgerAction." the summary.",
                        [ 'SUBLEDGERACTION' => $subledgerAction]
                    );
            //TODO:i18N-SL-Error-Message(code change review)
                }
            } else if ( $values['SUMMARY'] != '' ) {
                if ( ! GetPRBatchRecFromTitle($values['SUMMARY'], $this->getSubledgerModule(), $batchrec) ) {
                    $gErr->addIAError(
                        'SL-1003', __FILE__ . ':' . __LINE__,
                        'Unable to convert summary ' . $values['SUMMARY'] . ' to a record#',
                        [ 'VALUES_SUMMARY' => $values['SUMMARY']]
                    );
                    $ok = false;
                }
                if ( $batchrec ) {
                    foreach ( $batchrec as $batchkey ) {
                        $ok = $this->updatePRBatch($batchkey);

                        if ( ! $ok ) {
                            $subledgerAction = $this->getSubledgerAction();
                            $gErr->addIAError(
                                'SL-1004', __FILE__ . ':' . __LINE__,
                                "Unable to ".$subledgerAction." the summary.",
                                [ 'SUBLEDGERACTION' => $subledgerAction ]
                            );
                            break;
                        //TODO:i18N-SL-Error-Message(code change review)
                        }
                    }
                }
            } else {
                $gErr->addError("SL-0387", __FILE__ . ":" . __LINE__,
                                "Either the reporting period or the summary title are required");
                $ok = false;
            }
        }
        if ( !$ok ) {
            $msg = sprintf('Could not complete %1$s', $source);
            $gErr->addIAError('SL-1005', __FILE__ . ':' . __LINE__, $msg,[ 'SOURCE' => $source]);
            $this->_QM->rollbackTrx($source);
        } else {
            $this->auditOpenClose($values);
            if ( !$this->_QM->commitTrx($source) ) {
                $msg = sprintf('Error on committing %1$s', $source);
                $gErr->addIAError('SL-1006', __FILE__ . ':' . __LINE__, $msg, [ 'SOURCE' => $source]);
                $this->_QM->rollbackTrx($source);
                $ok = false;
            }
        }
        $locationKey = '';
        if ( isset($values['ENTITYID']) && $values['ENTITYID'] != '' ) {
            $locs = $this->getLocationMembers($values['ENTITYID']);
            $locationKey = $locs[0];
        }
        $values['OPENDATE'] = $this->getOpenDate($locationKey);
        $values['SUMMARY'] = '';
        return $ok;
    }

    /**
     * Validate the form values before updating the records
     * @param array $values
     * @return bool
     */
    function ValidateSelectedValues(&$values)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gErr = Globals::$g->gErr;

        $ok = true;
        if($this->subledgerAction != 'open' && $this->subledgerAction != 'close'){
            $gErr->addError('SL-0388', __FILE__ . ':' . __LINE__, "Invalid action (must be open or close).");
            return false;
        }

        $periodDash = '-- ' . I18N::getSingleToken('IA.SELECT_PERIOD') . ' --';
        if ( !isset($values['PERIODNAME']) || $values['PERIODNAME'] == '' || $values['PERIODNAME'] == $periodDash ) {
            $values['PERIODNAME'] = '';
        }
        if(!empty($values['PERIODNAME']) && !empty($values['SUMMARY'])){
                $gErr->addError("SL-0389", __FILE__ . ":" . __LINE__,
                    "Pass Either the reporting period or the summary title");
                return false;
        }

        if($values['PERIODNAME'] != '') {
            $glbudgettypeMgr = $gManagerFactory->getManager('glbudgettype');
            $params = [
                'selects' => [ 'RECORD#' ],
                'filters' => [ [ [ 'NAME', '=', $values['PERIODNAME'] ] ] ]
            ];

            $periodInfo = $glbudgettypeMgr->GetList($params);

            if ( ! isset($periodInfo[0]['RECORD#']) || $periodInfo[0]['RECORD#'] == '' ) {
                $msg = 'Invalid Period';
                $act = 'Enter a valid period name';
                $gErr->addIAError(
                    'SL-1007',
                    __FILE__ . ':' . __LINE__,
                    sprintf('Unable to retrieve glbudgettype %1$s', $periodInfo[0]['RECORD#']),
                    ['PERIOD_INFO_RECORD' => $periodInfo[0]['RECORD#']],
                    $msg, [],
                    $act, []
                );

                return false;
            }
            $values['PERIODRECNO'] = $periodInfo[0]['RECORD#'];
        }

        $locs = array();
        // get member of location group or the record no of location
        if ( isset($values['ENTITYID']) && $values['ENTITYID'] != '' ) {
            $locs = $this->getLocationMembers($values['ENTITYID']);

            if ( count($locs) == 0 ) {
                $msg = "Invalid Entity/Group id";
                $corr = "Enter valid Entity/Group id";
                $gErr->addError("SL-1008", __FILE__ . ":" . __LINE__, $msg, '', $corr);
                return false;
            }
        } else if ( IsMultiEntityCompany() && GetContextLocation() ) {// from entity context
            $rootStartOpen = GetCompanyOpenDate(false);
            $openDate = $values['OPENDATE'] ?? $this->getOpenDate(GetContextLocation());
            // check whether the date is less than root's date
            if ( DateCompare($openDate, $rootStartOpen) <= -1 ) {
                $msg = "Entity/Locations Books cannot be opened. ";
                $corr = "Parent companies books should be opened first.";
                $gErr->addError("SL-1009", __FILE__ . ":" . __LINE__, $msg, '', $corr);
                return false;
            }

            $locs = array(GetContextLocation());
        }

        $ok = BooksManager::validateRestrictedUserLocation($locs);

        $values['ENTITYRECS'] = $locs;

        return $ok;
    }

    /**
     * get all entities of group or entity's record no
     *
     * @param string $location location number or group id
     *
     * @return array $loc entities
     */
    public function getLocationMembers($location)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $isgrp = IsGroup('location', $location, $locgrprec) ? true : false;
        $locMgr = $gManagerFactory->getManager('locationentity');
        $locs = array();
        if ( $isgrp ) {
            $locGrpMgr = $gManagerFactory->getManager('locationgroup');
            $locmems = $locGrpMgr->GetLocationGrpMembers($location);

            $params = array(
                'selects' => array('RECORDNO'),
                'filters' => array(array(array('RECORDNO', 'IN', $locmems))),
                'SUBS_TABLE' => array(
                    'location' => 'locationmst',
                ),
            );

            $locationInfo = $locMgr->GetList($params);

            foreach ($locationInfo as $locNos) {
                $locs[] = $locNos['RECORDNO'];
            }

        } else {
            list($locationId) = explode('--', $location);
            $params = array(
                'selects' => array('RECORDNO'),
                'filters' => array(array(array('LOCATIONID', '=', $locationId))),
                'SUBS_TABLE' => array(
                    'location' => 'locationmst',
                ),
            );

            $locationInfo = $locMgr->GetList($params);
            if ( $locationInfo[0]['RECORDNO']) {
                $locs[] = $locationInfo[0]['RECORDNO'];
            }
        }

        return $locs;
    }
    /**
     * Audit the open/close action.
     *
     * @param array $values
     */
    private function auditOpenClose($values)
    {
        //  Add the audit action.
        if (!$this->IsAuditEnabled()) {
            return;
        }

        $auditTrailSession = AuditTrailSession::getInstance();
        $auditValues = $values;
        // To add all location id's in audit logs         
        if (IsMultiEntityCompany() && empty($auditValues['SUMMARY']) && empty($values['ENTITYRECS']) 
            && empty($auditValues['ENTITYID'])) {
            $locMgr = Globals::$g->gManagerFactory->getManager('locationentity');
            $params = array(
                'selects' => array('RECORDNO'),
                'SUBS_TABLE' => array(
                    'location' => 'locationmst',
                ),
            );

            $locationInfo = $locMgr->GetList($params);
            foreach ($locationInfo as $location) {
                $values['ENTITYRECS'][] = $location['RECORDNO'];
            }
        }
        //  Generate a list of the entities actually opened or closed.
        $entityList = [];
        if (isset($values['ENTITYRECS']) && count($values['ENTITYRECS']) > 0) {
            $locMgr =  Globals::$g->gManagerFactory->getManager('locationentity');

            $lmap = $locMgr->GetEntitiesMap(true);
            foreach ($values['ENTITYRECS'] as $entityId) {
                if (isset($lmap[$entityId]) && isset($lmap[$entityId]['LOCATION_NO'])) {
                    $entityList[] = $lmap[$entityId]['LOCATION_NO'];
                }
            }
        }
        $allEntityList = implode(',', $entityList);

        $entities = [];
        if (empty($auditValues['ENTITYID'])) {

            //  If the ENTITYID is empty, could be one of two situations.  1) we are in an entity;
            //   in that case use the location name in the ENTITYID field, or 2) we are in the root;
            //   that means we're opening/closing for ALL entities, so name it as such.
            $locIdName = GetContextLocation(true);
            if ($locIdName) {
                $entities[$locIdName] = [ 'ENTITYID' => $locIdName, 'ENTITYLIST' => $locIdName ];
            } else {
                $entities['*'] = [ 'ENTITYID' => self::ALL_ENTITIES_LABEL, 'ENTITYLIST' => $allEntityList ];
            }
        } else {
            foreach ($entityList as $thisEntity) {
                $entities[$thisEntity] = [ 'ENTITYID' => $auditValues['ENTITYID'], 'ENTITYLIST' => $thisEntity ];
            }
        }
        $auditEntity = $this->getAuditEntity();
        $auditAction = ($this->getSubledgerAction() == 'open')
            ? AuditTrail::AUDITTRAIL_ACTION_OPEN : AuditTrail::AUDITTRAIL_ACTION_CLOSE;

        foreach ($entities as $auditKey => $info) {
            if(empty($auditValues['SUMMARY'])){
                $auditValues['ENTITYID'] = $info['ENTITYID'];
                $auditValues['ENTITYLIST'] = $info['ENTITYLIST'];
            }
            AuditTrailSession::createAuditActionEvent($auditEntity, $auditKey, $auditAction);

            $auditPayload = new AuditTrailPayload($auditEntity, $auditKey, $auditValues);
            $auditTrailSession->addAuditEvent($auditEntity, $auditKey,
                AuditTrail::AUDITTRAIL_EVENT_UPDATE, $auditPayload);
        }
    }
    /**
     * getAuditObjId - Get the key value for the audit trail record.
     *
     * @return string Audit record key value.
     */
    public function getAuditObjId()
    {
        return self::getSLBooksAuditObjId();
    }

    /**
     * Checks to see if there is any restricted access on this object (only called in restricted context).
     *
     * @param string $vid Vid of object to check.
     *
     * @return bool True means access ok, false means it is not.
     */
    public function checkAuditAccess($vid)
    {
        //  Assume access if location matches, or root.
        $locName = GetContextLocation(true);
        if ($locName && $locName != $vid) {
            return false;
        }
        return true;
    }

    /**
     * Get the key value for the audit trail record. It is the entity name or '' for root.
     *
     * @param bool $forSearch True means get id used by Editor for search, false means get stored version.
     *
     * @return string Audit record key value.
     */
    public static function getSLBooksAuditObjId($forSearch=false)
    {
        $locName = GetContextLocation(true);
        if ($locName) {
            return $locName;
        }
        $rlocs = GetRestrictedUserLocIds(true);
        if ($rlocs && count($rlocs) > 0) {
            if ($forSearch) {
                return AuditTrail::makeMultiObjectKey($rlocs);
            }
            return implode(':', $rlocs);
        }
        return '*';
    }

    /**
     *  transformOldAndNewAuditFields - allows subclasses to alter old/new field values before display.
     *
     * @param array &$oldValues Old field values.
     * @param array &$newValues New field values.
     */
    public function transformOldAndNewAuditFields(&$oldValues,
        /** @noinspection PhpUnusedParameterInspection */ &$newValues)
    {
        foreach ($oldValues as $field => $value) {
            //  Since the book audit fields don't form a traditional trail, but are independent events,
            //    get rid of the 'old' values.
            $oldValues[$field] = '';
        }
    }

    /**
     * @param string  $operation
     * @param array  &$values
     *
     * @return bool
     */
    function API_Validate($operation, &$values = null)
    {
        if ($operation == API_READ || $operation == API_READ_API ||
            $operation == API_READ_BY_QUERY || $operation == API_READ_BY_NAME ||
            $operation == API_QUERY) {
            global $gErr;
            $msg = "API operation $operation cannot be performed on objects of type " . $this->_entity;
            $gErr->addIAError(
                'SL-1010', __FILE__ . ':' . __LINE__,
                $msg, [ 'OPERATION' => $operation, 'ENTITY' => $this->_entity]
            );
            return false;
        } else {
            return parent::API_Validate($operation, $values);
        }
    }
    /**
     * Determines if audit trail entries for this class are independent
     *
     * @return bool Returns true because GL Books Open/Close events are independent of each other.
     */
    public function isAuditIndependentEntries()
    {
        return true;
    }
}
