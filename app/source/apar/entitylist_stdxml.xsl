<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
<xsl:import href="../../private/xslinc/report_helpers.xsl"/>

<xsl:output encoding="UTF-8"/>
 
<xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/>

<xsl:variable  name="report_format" select="/reportdata/report/@report_format" />

<xsl:template match="/">
	<xsl:apply-templates/>			
</xsl:template>

<xsl:template match="reportdata">
	<xsl:apply-templates/>
</xsl:template>

<xsl:template match="report">
		<report 
			showHeader="{@showHeader}"
			department="{@department}" 
			location="{@location}" 
			entity="{@entity}" 
			orientation="Landscape" 
			report_date="{@reportdate}" 
			report_time="{@reporttime}" 
			align_currency="left" 
			page_number="Y"
			action="" 			 
			footer_allpages="Y">

			<company s="2"><xsl:value-of select="@co"/></company>
			<title s="3" titleNum="1"><xsl:value-of select="@title"/></title>
			<title s="3" titleNum="2"><xsl:value-of select="@title2"/></title>
			<footer s="5" lines="1" footerNum="1"><xsl:value-of select="@titlecomment"/></footer>
	        <xsl:for-each select="rtdim">
                <!-- added s attribute below for Excel.  Probably safe for all -->
	        	<rtdim name="@name" s="2">
	        		<name><xsl:value-of select="@name"/></name>
					<value><xsl:value-of select="@value"/></value>
				</rtdim>
	        </xsl:for-each>

		<header s="header">
		<xsl:choose>
		    <xsl:when test="(@lineFormat= 'Single Line')">
                <xsl:if  test="number($narrow_format) ='1'">
		            <hrow s="header">	
		            	<hcol s="txt"/>
		            	<hcol s="txt"/>
		            	<xsl:if test="string(/reportdata/report/@entity) != 'employee'">
		            		<hcol s="txt"/>
		            		<hcol s="txt"/>
		            		<hcol s="txt"/>
		            	</xsl:if>
		            	<xsl:if test="string(/reportdata/report/@entity) = 'employee'">
		            		<hcol s="txt"/>
		            		<hcol s="txt"/>
		            		<hcol s="txt"/>
		            	</xsl:if>
 		            	<xsl:if test="string(/reportdata/report/@incDE542Sent)='true'">
		            		<hcol s="txt"/>	
		            	</xsl:if>
		            	<xsl:if test="string(/reportdata/report/@incEntityType)='true'">
		            		<hcol/>
		            	</xsl:if>	
		            	<xsl:if test="string(/reportdata/report/@incCreditLimit)='true'">
		            		<hcol/>	
		            	</xsl:if>	
		            	<xsl:if test="string(/reportdata/report/@incBalance)='true'">
		            		<hcol/>	
		            	</xsl:if>	
		            	<xsl:if test="string(/reportdata/report/@incInactiveEntity)='true'">
		            		<hcol/>	
		            	</xsl:if>
		            	<xsl:if test="string(/reportdata/report/@incInactiveEntity)='true'">
		            		<hcol/>	
		            	</xsl:if>	
		        	    <xsl:if test="string(/reportdata/report/@onHoldEntity)='true'">
		        	    	<hcol/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@defCurrency)='true'">
		        	    	<hcol/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incAddress)='true'">
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>
							<xsl:if test="string(/reportdata/report/@thirdRowAddress)='true'">
								<hcol/>
							</xsl:if>
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incPrimaryContact)='true'">
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>
							<xsl:if test="string(/reportdata/report/@thirdRowAddress)='true'">
								<hcol/>
							</xsl:if>
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incContact1)='true'">
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incContact2)='true'">
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    	<hcol/>	
		        	    </xsl:if>
		        	    
		        	    <!-- added
		        	     to showup additional fields in customer report. -->
 		        	    <xsl:if test="string(/reportdata/report/@incTerritory)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>			
 		        	    <xsl:if test="string(/reportdata/report/@incCustRepresentative)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incResaleNumber)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incLastInvoiceDate)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incLastStatementDate)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incDeliveryOptions)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incShipMethod)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incEPaymentSuccess)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incEPaymentFailure)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>

		        	    <!-- added
		        	     to showup additional fields in vendor list report. -->
 		        	    <xsl:if test="string(/reportdata/report/@incPaymentPriority)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
 		        	    <xsl:if test="string(/reportdata/report/@incDefault)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@displaytermdiscount)='true'">	
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>

		        	    <!-- added
		        	     to showup additional fields in employee list report. -->
 		        	    <xsl:if test="string(/reportdata/report/@incDepartment)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incLocation)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
 		        	    <xsl:if test="string(/reportdata/report/@incSSN)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
		        	    <xsl:if test="string(/reportdata/report/@incDateOfBirth)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>
 		        	    <xsl:if test="string(/reportdata/report/@incTitle)='true'">
		        	    	<hcol s="txt"/>	
		        	    </xsl:if>                                
                    </hrow>
                </xsl:if>

		        <hrow s="51">
		    		<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col1"/></hcol>
		    		<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col2"/></hcol>
		    		<xsl:if test="string(/reportdata/report/@entity) = 'employee'">					
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col113"/></hcol>
		    			<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col115"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col117"/></hcol>
		    		</xsl:if>

		    		<xsl:if test="string-length(/reportdata/report/headings/@col3)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col3"/></hcol>
		    		</xsl:if>	
		    		<xsl:if test="string(/reportdata/report/@entity) != 'employee'">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col4"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col5"/></hcol>
		    		</xsl:if>

		    		<xsl:if test="string(/reportdata/report/@entity)='vendor'">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col6"/></hcol>
		    			<xsl:if test="string(/reportdata/report/@incDE542Sent)='true'">
		    				<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col7"/></hcol>
		    			</xsl:if>	
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col8)!=0">
		    			<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col8"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col9)!=0">
		    			<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col9"/></hcol>
		    		</xsl:if>
		    		
		    		<xsl:if test="string(/reportdata/report/@incInactiveEntity)='true'">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col120"/></hcol>
                    </xsl:if>
		    		<xsl:if test="string(/reportdata/report/@onHoldEntity)='true'">
		    	       <hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col121"/></hcol>
                    </xsl:if>
		    		<xsl:if test="string(/reportdata/report/@defCurrency)='true'">
		    	       <hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col122"/></hcol>
                    </xsl:if>


		    		<!-- <xsl:if test="string-length(/reportdata/report/headings/@col10)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col10"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col11)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col11"/></hcol>
		    		</xsl:if> -->

		    		<!-- added
		    		 to showup additional fields in customer report. -->
 		    		<xsl:if test="string-length(/reportdata/report/headings/@col102)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col102"/></hcol>
		    		</xsl:if>
 		    		<xsl:if test="string-length(/reportdata/report/headings/@col103)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col103"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col98)!=0">
		    			<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col98"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col99)!=0">
		    			<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col99"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col100)!=0">
		    			<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col100"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col101)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col101"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col104)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col104"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col118)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col118"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col119)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col119"/></hcol>
		    		</xsl:if>

		    		<!-- added
		    		 to showup additional fields in vendor report. -->
		    		<xsl:if test="string-length(/reportdata/report/headings/@col105)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col105"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col106)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col106"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col107)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col107"/></hcol>
		    		</xsl:if>

		    		<!-- added
		    		 to showup additional fields in employee list report. -->
 		    		<xsl:if test="string-length(/reportdata/report/headings/@col110)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col110"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col111)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col111"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col112)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col112"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col116)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col116"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string-length(/reportdata/report/headings/@col114)!=0">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col114"/></hcol>
		    		</xsl:if>

		    		<xsl:if test="string(/reportdata/report/@incAddress)='true'">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col10"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col11"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col12"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col13"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col14"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col15"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col16"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col17"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col18"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col19"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col20"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col21"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col22"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col23"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col24"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col25"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col26"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col27"/></hcol>
						<xsl:if test="string(/reportdata/report/@thirdRowAddress)='true'">
							<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col123"/></hcol>
						</xsl:if>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col28"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col29"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col30"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col31"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string(/reportdata/report/@incPrimaryContact)='true'">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col32"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col33"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col34"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col35"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col36"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col37"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col38"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col39"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col40"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col41"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col42"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col43"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col44"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col45"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col46"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col47"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col48"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col49"/></hcol>
						<xsl:if test="string(/reportdata/report/@thirdRowAddress)='true'">
							<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col124"/></hcol>
						</xsl:if>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col50"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col51"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col52"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col53"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string(/reportdata/report/@incContact1)='true'">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col54"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col55"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col56"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col57"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col58"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col59"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col60"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col61"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col62"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col63"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col64"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col65"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col66"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col67"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col68"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col69"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col70"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col71"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col72"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col73"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col74"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col75"/></hcol>
		    		</xsl:if>
		    		<xsl:if test="string(/reportdata/report/@incContact2)='true'">
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col76"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col77"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col78"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col79"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col80"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col81"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col82"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col83"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col84"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col85"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col86"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col87"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col88"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col89"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col90"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col91"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col92"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col93"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col94"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col95"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col96"/></hcol>
		    			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col97"/></hcol>
		    		</xsl:if>
		    	</hrow>
		    </xsl:when>
		<xsl:otherwise>

        <xsl:if  test="$report_format!='_excel'">
		<hrow s="header">
			<hcol s="txt"/>
			<hcol s="txt"/>			
			<xsl:if test="string(/reportdata/report/@entity) != 'employee'">
				<hcol s="txt"/>
				<hcol s="txt"/>
				<hcol s="txt"/>
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@entity) = 'employee'">
				<hcol s="txt"/>
				<hcol s="txt"/>
				<hcol s="txt"/>
			</xsl:if>

			<xsl:if test="string(/reportdata/report/@incEntityType)='true'">
				<hcol/>
			</xsl:if>	
			<xsl:if test="string(/reportdata/report/@incCreditLimit)='true'">
				<hcol/>	
			</xsl:if>	
			<xsl:if test="string(/reportdata/report/@incBalance)='true'">
				<hcol/>	
			</xsl:if>	
			<xsl:if test="string(/reportdata/report/@incInactiveEntity)='true'">
				<hcol/>	
			</xsl:if>		
			<xsl:if test="string(/reportdata/report/@onHoldEntity)='true'">
				<hcol/>	
			</xsl:if>	
			<xsl:if test="string(/reportdata/report/@defCurrency)='true'">
				<hcol/>	
			</xsl:if>		
			
			<!-- added to showup additional fields in customer list report. -->
 			<xsl:if test="string(/reportdata/report/@incTerritory)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incCustRepresentative)='true'">
				<hcol s="txt"/>	
			</xsl:if>
 			<xsl:if test="string(/reportdata/report/@incResaleNumber)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incLastInvoiceDate)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incLastStatementDate)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incDeliveryOptions)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incShipMethod)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incEPaymentSuccess)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incEPaymentFailure)='true'">
				<hcol s="txt"/>	
			</xsl:if>

			<!-- added
			 to showup additional fields in vendor list report. -->
 			<xsl:if test="string(/reportdata/report/@incPaymentPriority)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incDefaultAcct)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@displaytermdiscount)='true'">
				<hcol s="txt"/>	
			</xsl:if>

			<xsl:if test="string(/reportdata/report/@incDE542Sent)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<!-- added
			 to showup additional fields in employee list report. -->
 			<xsl:if test="string(/reportdata/report/@incDepartment)='true'">
				<hcol s="txt"/>	
			</xsl:if>
			<xsl:if test="string(/reportdata/report/@incLocation)='true'">
				<hcol s="txt"/>	
			</xsl:if>
 			<xsl:if test="string(/reportdata/report/@incSSN)='true'">
				<hcol s="txt"/>	
			</xsl:if> 			
			<xsl:if test="string(/reportdata/report/@incDateOfBirth)='true'">
				<hcol s="txt"/>	
			</xsl:if>
 			<xsl:if test="string(/reportdata/report/@incTitle)='true'">
				<hcol s="txt"/>	
			</xsl:if>

		</hrow>
		</xsl:if>

		<hrow s="51">
			<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col1"/></hcol>
				<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col2"/></hcol>
				<xsl:if test="string(/reportdata/report/@entity) = 'employee'">					
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col113"/></hcol>
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col115"/></hcol>
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col117"/></hcol>
				</xsl:if>

				<xsl:if test="string-length(/reportdata/report/headings/@col3)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col3"/></hcol>
				</xsl:if>	
				<xsl:if test="string(/reportdata/report/@entity) != 'employee'">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col4"/></hcol>
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col5"/></hcol>
				</xsl:if>

				<xsl:if test="string(/reportdata/report/@entity)='vendor'">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col6"/></hcol>
					<xsl:if test="string(/reportdata/report/@incDE542Sent)='true'">
						<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col7"/></hcol>
					</xsl:if>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col8)!=0">
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col8"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col9)!=0">
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col9"/></hcol>
				</xsl:if>

				<xsl:if test="string(/reportdata/report/@incInactiveEntity)='true'">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col120"/></hcol>
                </xsl:if>
				<xsl:if test="string(/reportdata/report/@onHoldEntity)='true'">
			       <hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col121"/></hcol>
                </xsl:if>
				<xsl:if test="string(/reportdata/report/@defCurrency)='true'">
			       <hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col122"/></hcol>
                </xsl:if>

			    <xsl:if test="string-length(/reportdata/report/headings/@col10)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col10"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col11)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col11"/></hcol>
				</xsl:if>

				<!-- added
				 to showup additional fields in customer report. -->
 				<xsl:if test="string-length(/reportdata/report/headings/@col102)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col102"/></hcol>
				</xsl:if>
 				<xsl:if test="string-length(/reportdata/report/headings/@col103)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col103"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col98)!=0">
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col98"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col99)!=0">
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col99"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col100)!=0">
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col100"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col101)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col101"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col104)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col104"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col118)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col118"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col119)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col119"/></hcol>
				</xsl:if>

				<!-- added
				 to showup additional fields in vendor report. -->
				<xsl:if test="string-length(/reportdata/report/headings/@col105)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col105"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col106)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col106"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col107)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col107"/></hcol>
				</xsl:if>

				<!-- added
				 to showup additional fields in employee list report. -->
 				<xsl:if test="string-length(/reportdata/report/headings/@col110)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col110"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col111)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col111"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col112)!=0">
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col112"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col116)!=0">
					<hcol  s="18"><xsl:value-of select="/reportdata/report/headings/@col116"/></hcol>
				</xsl:if>
				<xsl:if test="string-length(/reportdata/report/headings/@col114)!=0">
					<hcol  s="17"><xsl:value-of select="/reportdata/report/headings/@col114"/></hcol>
				</xsl:if>			
		</hrow>					
		</xsl:otherwise>
		</xsl:choose>	
	</header>	

		<body s="body">
			<xsl:choose>
			<xsl:when test="(@lineFormat= 'Single Line')">
				<xsl:apply-templates mode="singlelinelayout"/>
			</xsl:when>
			<xsl:otherwise>
				<xsl:apply-templates mode="multilinelayout"/>
			</xsl:otherwise>
			</xsl:choose>						
		</body>
			

		<xsl:call-template name="stylegroups"/>		
		<script>
			function drilldown(which, rec, ids, loc) {
				curloc = '<xsl:value-of select="/reportdata/report/@locationcontext"/>'; 
				ismegl = '<xsl:value-of select="/reportdata/report/@ismegl"/>'; 
				companyid = '<xsl:value-of select="/reportdata/report/@companyid"/>';
				entity = '<xsl:value-of select="/reportdata/report/@entity"/>';

				popup = false;
				var allowed_ids = ids.split(',');
				var aUrl;
				if( allowed_ids[which] ){
					aUrl = 'editor.phtml?.op=' + escape(allowed_ids[which]) + '&amp;.r=' + escape(rec);
				}
				else{
					aUrl = '#';
				}
				
				module = (entity == 'Vendor')? 'ap': 'ar';			
				ReportLaunch(companyid, ismegl, loc, curloc, module, aUrl, false, '', '<xsl:value-of select="/reportdata/report/@sess"/>');
			}
		</script>
	</report>
</xsl:template>


<xsl:template match="ENTITY" mode="multilinelayout">
	<xsl:if test="string(@NODATA)=1">
		<row s="14">
			<col  s="19" colspan="{/reportdata/report/@noofcolumns}" >IA.NO_DATA_FOUND</col>
		</row>
	</xsl:if>
<!--	<row s="12"> -->
	<row>
		<col  s="24">
			<xsl:attribute name="href">
				<xsl:text>javascript:drilldown(0,'</xsl:text>
				<xsl:value-of select="@ENTITYID"/>
				<xsl:text>', '</xsl:text>
				<xsl:value-of select="/reportdata/report/@ops"/>
				<xsl:text>', '</xsl:text>
				<xsl:value-of select="@ENTITYLOCATION"/>
				<xsl:text>');</xsl:text>
			</xsl:attribute>
			<xsl:value-of select="@ENTITYID"/>
		</col>
		<col  s="24"><xsl:value-of select="@ENTITY"/></col>
		<xsl:if test="string(/reportdata/report/@entity) != 'employee'">
			<xsl:if test="string(/reportdata/report/@incEntityType)='true'">
				<col  s="24">
					<xsl:attribute name="href">
						<xsl:text>javascript:drilldown(1,'</xsl:text>
						<xsl:value-of select="@ENTITYTYPE"/>
					<xsl:text>', '</xsl:text>
					<xsl:value-of select="/reportdata/report/@ops"/>
					<xsl:text>', '</xsl:text>
					<xsl:value-of select="@ENTITYTYPELOCATION"/>
					<xsl:text>');</xsl:text>
					</xsl:attribute>
					<xsl:value-of select="@ENTITYTYPE"/>
				</col>
			</xsl:if>		
			<col  s="24"><xsl:value-of select="@TERM"/></col>
			<col  s="24"><xsl:value-of select="@TAXID"/></col>
		</xsl:if>

		<xsl:if test="string(/reportdata/report/@entity)='employee'">
			<col  s="24"><xsl:value-of select="@GENDER"/></col>
			<col  s="26"><xsl:value-of select="@STARTDATE"/></col>
			<col  s="24"><xsl:value-of select="@MANAGER"/></col>
			<xsl:if test="string(/reportdata/report/@incEntityType)='true'">
				<col  s="24"><xsl:value-of select="@ENTITYTYPE"/></col>
			</xsl:if>
		</xsl:if>


		<xsl:if test="string(/reportdata/report/@entity)='vendor'">
			<col  s="24"><xsl:value-of select="@ACCOUNTNO"/></col>
			<xsl:if test="string(/reportdata/report/@incDE542Sent)='true'">
				<col  s="24"><xsl:value-of select="@DE542SENT"/></col>
			</xsl:if>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incCreditLimit)='true'">
			<col  s="34"><xsl:value-of select="@CREDITLIMIT"/></col>
		</xsl:if>	
		<xsl:if test="string(/reportdata/report/@incBalance)='true'">
			<col  s="34"><xsl:value-of select="@TOTALDUE"/></col>
		</xsl:if>	
		<xsl:if test="string(/reportdata/report/@incInactiveEntity)='true'">	
			<col  s="24"><xsl:value-of select="@STATUS"/></col>
		</xsl:if>			
		<xsl:if test="string(/reportdata/report/@onHoldEntity)='true'">	
			<col  s="24"><xsl:value-of select="@ONHOLD"/></col>
		</xsl:if>			
		<xsl:if test="string(/reportdata/report/@defCurrency)='true'">	
			<col  s="24"><xsl:value-of select="@DEFCURRENCY"/></col>
		</xsl:if>			

		<!-- added
		 to showup additional fields in customer report. -->
  		<xsl:if test="string(/reportdata/report/@incTerritory)='true'">	
			<col  s="24"><xsl:value-of select="@TERRITORYID"/></col>
		</xsl:if>	

  		<xsl:if test="string(/reportdata/report/@incCustRepresentative)='true'">	
			<col  s="24"><xsl:value-of select="@CUST_REPRESENTATIVE"/></col>
		</xsl:if>	

 		<xsl:if test="string(/reportdata/report/@incResaleNumber)='true'">	
			<col  s="24"><xsl:value-of select="@RESALE_NUMBER"/></col>
		</xsl:if>	

 		<xsl:if test="string(/reportdata/report/@incLastInvoiceDate)='true'">	
			<col  s="24"><xsl:value-of select="@LASTINVOICEDATE"/></col>
		</xsl:if>	

 		<xsl:if test="string(/reportdata/report/@incLastStatementDate)='true'">	
			<col  s="24"><xsl:value-of select="@LASTSTATEMENTDATE"/></col>
		</xsl:if>	

 		<xsl:if test="string(/reportdata/report/@incDeliveryOptions)='true'">	
			<col  s="24"><xsl:value-of select="@DELIVERYOPTIONS"/></col>
		</xsl:if>
		
		<xsl:if test="string(/reportdata/report/@incShipMethod)='true'">	
			<col  s="24"><xsl:value-of select="@SHIPMETHOD"/></col>
		</xsl:if>
		
		<xsl:if test="string(/reportdata/report/@incEPaymentSuccess)='true'">	
			<col  s="24"><xsl:value-of select="@EPAYMENTSUCCESS"/></col>
		</xsl:if>
		
		<xsl:if test="string(/reportdata/report/@incEPaymentFailure)='true'">	
			<col  s="24"><xsl:value-of select="@EPAYMENTFAILURE"/></col>
		</xsl:if>

		<!-- added
		 to showup additional fields in vendor report. -->
 		<xsl:if test="string(/reportdata/report/@incPaymentPriority)='true'">	
			<col  s="24"><xsl:value-of select="@PAYMENTPRIORITY"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incDefaultAcct)='true'">	
			<col  s="24"><xsl:value-of select="@ACCOUNT"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@displaytermdiscount)='true'">	
			<col  s="24"><xsl:value-of select="@DISPLAYTERMDISCOUNT"/></col>
		</xsl:if>

		<!-- added
		 to showup additional fields in employee report. -->
 		<xsl:if test="string(/reportdata/report/@incDepartment)='true'">	
			<col  s="24"><xsl:value-of select="@DEPARTMENT"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incLocation)='true'">	
			<col  s="24"><xsl:value-of select="@LOCATION"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incSSN)='true'">	
			<col  s="26"><xsl:value-of select="@SSN"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incDateOfBirth)='true'">	
			<col  s="24"><xsl:value-of select="@DATEOFBIRTH"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incTitle)='true'">	
			<col  s="24"><xsl:value-of select="@TITLE"/></col>
		</xsl:if>		
	</row>
	<xsl:apply-templates mode="multilinelayout"/>

    <!-- blank separator line -->
    <xsl:if  test="(/reportdata/report/@incContact1 ='true'  or /reportdata/report/@incContact2 ='true' or /reportdata/report/@incPrimaryContact ='true' or /reportdata/report/@incAddress='true')">
	    <row s="14" style="height:8px">
	    	<col  s="19"> 
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns"/></xsl:attribute>
  		        </xsl:if>
                <xsl:if  test="($report_format='_html')">
                    <xsl:text disable-output-escaping="yes"><![CDATA[&nbsp;]]></xsl:text>
  		        </xsl:if>
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 1"/>
                </xsl:call-template>
  		    </xsl:if>
	    </row>
    </xsl:if>
</xsl:template>

<xsl:template match="ENTITY" mode="singlelinelayout">
	<xsl:if test="string(@NODATA)=1">
		<row s="14">
			<col  s="19" colspan="{/reportdata/report/@noofcolumns}" >IA.NO_DATA_FOUND</col>
		</row>
	</xsl:if>
<!--	<row s="12"> -->
	<row>
		<col  s="24">
			<xsl:attribute name="href">
				<xsl:text>javascript:drilldown(0,'</xsl:text>
				<xsl:value-of select="@ENTITYID"/>
				<xsl:text>', '</xsl:text>
				<xsl:value-of select="/reportdata/report/@ops"/>
				<xsl:text>');</xsl:text>
			</xsl:attribute>
			<xsl:value-of select="@ENTITYID"/>
		</col>
		<col  s="24"><xsl:value-of select="@ENTITY"/></col>
		<xsl:if test="string(/reportdata/report/@entity) != 'employee'">
			<xsl:if test="string(/reportdata/report/@incEntityType)='true'">
				<col  s="24">
					<xsl:attribute name="href">
						<xsl:text>javascript:drilldown(1,'</xsl:text>
						<xsl:value-of select="@ENTITYTYPE"/>
						<xsl:text>', '</xsl:text>
					<xsl:value-of select="/reportdata/report/@ops"/>
					<xsl:text>');</xsl:text>
					</xsl:attribute>
					<xsl:value-of select="@ENTITYTYPE"/>
				</col>
			</xsl:if>		
			<col  s="24"><xsl:value-of select="@TERM"/></col>
			<col  s="24"><xsl:value-of select="@TAXID"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@entity)='employee'">
			<col  s="24"><xsl:value-of select="@GENDER"/></col>
			<col  s="24"><xsl:value-of select="@STARTDATE"/></col>
			<col  s="24"><xsl:value-of select="@MANAGER"/></col>
			<xsl:if test="string(/reportdata/report/@incEntityType)='true'">
				<col  s="24"><xsl:value-of select="@ENTITYTYPE"/></col>
			</xsl:if>
		</xsl:if>


		<xsl:if test="string(/reportdata/report/@entity)='vendor'">
			<col  s="24"><xsl:value-of select="@ACCOUNTNO"/></col>
			<xsl:if test="string(/reportdata/report/@incDE542Sent)='true'">
				<col  s="24"><xsl:value-of select="@DE542SENT"/></col>
			</xsl:if>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incCreditLimit)='true'">
			<col  s="34"><xsl:value-of select="@CREDITLIMIT"/></col>
		</xsl:if>	
		<xsl:if test="string(/reportdata/report/@incBalance)='true'">
			<col  s="34"><xsl:value-of select="@TOTALDUE"/></col>
		</xsl:if>	
		<xsl:if test="string(/reportdata/report/@incInactiveEntity)='true'">	
			<col  s="24"><xsl:value-of select="@STATUS"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@onHoldEntity)='true'">	
			<col  s="24"><xsl:value-of select="@ONHOLD"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@defCurrency)='true'">	
			<col  s="24"><xsl:value-of select="@DEFCURRENCY"/></col>
		</xsl:if>

		<!-- added
		 to showup additional fields in customer list report. -->
 		<xsl:if test="string(/reportdata/report/@incTerritory)='true'">
			<col  s="24"><xsl:value-of select="@TERRITORYID"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incCustRepresentative)='true'">
			<col  s="24"><xsl:value-of select="@CUST_REPRESENTATIVE"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incResaleNumber)='true'">
			<col  s="24"><xsl:value-of select="@RESALE_NUMBER"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incLastInvoiceDate)='true'">
			<col  s="24"><xsl:value-of select="@LASTINVOICEDATE"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incLastStatementDate)='true'">
			<col  s="24"><xsl:value-of select="@LASTSTATEMENTDATE"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incDeliveryOptions)='true'">
			<col  s="24"><xsl:value-of select="@DELIVERYOPTIONS"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incShipMethod)='true'">
			<col  s="24"><xsl:value-of select="@SHIPMETHOD"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incEPaymentSuccess)='true'">
			<col  s="24"><xsl:value-of select="@EPAYMENTSUCCESS"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incEPaymentFailure)='true'">
			<col  s="24"><xsl:value-of select="@EPAYMENTFAILURE"/></col>
		</xsl:if>

		<!-- added
		 to showup additional fields in vendor list report. -->
 		<xsl:if test="string(/reportdata/report/@incPaymentPriority)='true'">
			<col  s="24"><xsl:value-of select="@PAYMENTPRIORITY"/></col>
		</xsl:if>
 		<xsl:if test="string(/reportdata/report/@incDefaultAcct)='true'">
			<col  s="24"><xsl:value-of select="@ACCOUNT"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@displaytermdiscount)='true'">	
			<col  s="24"><xsl:value-of select="@DISPLAYTERMDISCOUNT"/></col>
		</xsl:if>

		<!-- added
		 to showup additional fields in employee report. -->
 		<xsl:if test="string(/reportdata/report/@incDepartment)='true'">	
			<col  s="24"><xsl:value-of select="@DEPARTMENT"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incLocation)='true'">	
			<col  s="24"><xsl:value-of select="@LOCATION"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incSSN)='true'">	
			<col  s="26"><xsl:value-of select="@SSN"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incDateOfBirth)='true'">	
			<col  s="24"><xsl:value-of select="@DATEOFBIRTH"/></col>
		</xsl:if>
		<xsl:if test="string(/reportdata/report/@incTitle)='true'">	
			<col  s="24"><xsl:value-of select="@TITLE"/></col>
		</xsl:if>



		<xsl:apply-templates mode="singlelinelayout"/>
	</row>

    <!-- BLANK SEPARATOR ROW -->
    <xsl:if  test="($report_format!='_excel')">
	<row s="14">
		<col  s="19" colspan="{/reportdata/report/@noofcolumns}" />
	</row>
    </xsl:if>
</xsl:template>

<xsl:template match="DISPLAYCONTACT" mode="singlelinelayout" >
	<xsl:call-template name="PROCESS_SL_CONTACTFIELDS"/>
</xsl:template>

<xsl:template match="DISPLAYCONTACT" mode="multilinelayout" >
	<xsl:call-template name="PROCESSCONTACTFIELDS"/>
</xsl:template>

<xsl:template match="PRIMARYCONTACT" mode="singlelinelayout">
	<xsl:call-template name="PROCESS_SL_CONTACTFIELDS"/>
</xsl:template>

<xsl:template match="PRIMARYCONTACT" mode="multilinelayout">
	<xsl:call-template name="PROCESSCONTACTFIELDS"/>
</xsl:template>

<xsl:template match="CONTACT1" mode="singlelinelayout">
	<xsl:call-template name="PROCESS_SL_CONTACTFIELDS"/>
</xsl:template>

<xsl:template match="CONTACT1" mode="multilinelayout">
	<xsl:call-template name="PROCESSCONTACTFIELDS"/>
</xsl:template>

<xsl:template match="CONTACT2" mode="singlelinelayout">
	<xsl:call-template name="PROCESS_SL_CONTACTFIELDS"/>
</xsl:template>

<xsl:template match="CONTACT2" mode="multilinelayout">
	<xsl:call-template name="PROCESSCONTACTFIELDS"/>
</xsl:template>


<xsl:template name="PROCESSCONTACTFIELDS">			
	<row s="14">
		<col  s="24"></col>
		<col  s="24"><xsl:value-of select="@TYPE"/></col>
		<xsl:if test="string-length(@CONTACT)!=0">
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@CONTACT" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</xsl:if>
	</row>
	
	<xsl:if test="string-length(@ADDRESSLINE1)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@ADDRESSLINE1" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>
	</xsl:if>

	<xsl:if test="string-length(@ADDRESSLINE2)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@ADDRESSLINE2" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>
	</xsl:if>

	<xsl:if test="string-length(@PHONE)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@PHONE" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>
	</xsl:if>	

	<xsl:if test="string-length(@CELLPHONE)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@ST_CELLPHONE" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>		
	</xsl:if>	

	<xsl:if test="string-length(@PAGER)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@ST_PAGER" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>
	</xsl:if>	

	<xsl:if test="string-length(@FAX)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@ST_FAX" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>
	</xsl:if>	
	
	<xsl:if test="string-length(@EMAIL)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@EMAIL" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>
	</xsl:if>	
	
	<xsl:if test="string-length(@URL)!=0">
		<row s="14">
			<col  s="24"></col>
			<col  s="24"></col>
			<col  s="24">
                <xsl:if  test="($report_format!='_excel')">
                    <xsl:attribute name="colspan"><xsl:value-of select="/reportdata/report/@noofcolumns -2"/></xsl:attribute>
  		        </xsl:if>
                <xsl:value-of select="@URL" />
            </col>
            <xsl:if  test="(/reportdata/tbreport/@report_format='_excel')">
                <!-- this is called recursively to fill in cols.  colspan not desired for excel -->
                <xsl:call-template name="add_empty_col">
                    <xsl:with-param name="i" select="/reportdata/report/@noofcolumns - 3"/>
                </xsl:call-template>
  		    </xsl:if>
		</row>
	</xsl:if>	
	
</xsl:template>

<xsl:template name="PROCESS_SL_CONTACTFIELDS">				
	<col  s="24"  ><xsl:value-of select="@CONTACTNAME"/></col>
	<col  s="24"  ><xsl:value-of select="@COMPANYNAME"/></col>
	<col  s="24" ><xsl:value-of select="@MRMRS"/></col>
	<col  s="24" ><xsl:value-of select="@FIRSTNAME"/></col>
	<col  s="24"  ><xsl:value-of select="@MI"/></col>
	<col  s="24"  ><xsl:value-of select="@LASTNAME"/></col>
	<col  s="24"  ><xsl:value-of select="@PRINTAS"/></col>
	<col  s="24"  ><xsl:value-of select="@PHONE1"/></col>
	<col  s="24"  ><xsl:value-of select="@PHONE2"/></col>
	<col  s="24"  ><xsl:value-of select="@CELLPHONE"/></col>
	<col  s="24"  ><xsl:value-of select="@PAGER"/></col>
	<col  s="24"  ><xsl:value-of select="@FAX"/></col>
	<col  s="24"  ><xsl:value-of select="@EMAIL1"/></col>
	<col  s="24"  ><xsl:value-of select="@EMAIL2"/></col>
	<col  s="24"  ><xsl:value-of select="@URL1"/></col>
	<col  s="24" ><xsl:value-of select="@URL2"/></col>
	<col  s="24" ><xsl:value-of select="@ADDR1"/></col>
	<col  s="24"  ><xsl:value-of select="@ADDR2"/></col>
	<xsl:if test="string(/reportdata/report/@thirdRowAddress)='true'">
		<col  s="24"  ><xsl:value-of select="@ADDR3"/></col>
	</xsl:if>
	<col  s="24" ><xsl:value-of select="@CITY"/></col>
	<col  s="24"  ><xsl:value-of select="@STATE"/></col>
	<col  s="24"  ><xsl:value-of select="@ZIP"/></col>
	<col  s="24"  ><xsl:value-of select="@COUNTRY"/></col>
</xsl:template>

</xsl:stylesheet>
