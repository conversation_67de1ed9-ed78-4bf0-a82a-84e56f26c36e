<?php

class ARRecurPaymentManager extends EntityManager
{
    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values) {
		$source = "ARRecurPaymentManager::Add";
		$ok = $this->_QM->beginTrx($source);
		$ok = $ok && $this->Translate($values);
		$ok = $ok && parent::regularAdd($values);
		$ok = $ok && $this->_QM->commitTrx($source);
		if (!$ok) {
			$this->_QM->rollbackTrx($source);
		}
		
		return $ok;
	}

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values) {
		$source = "ARRecurPaymentManager::Set";
		$ok = $this->_QM->beginTrx($source);
		$ok = $ok && $this->Translate($values);
		$ok = $ok && parent::regularSet($values);
		$ok = $ok && $this->_QM->commitTrx($source);
		if (!$ok) {
			$this->_QM->rollbackTrx($source);
		}
		
		return $ok;
	}

    /**
     * @param string $result
     * @param int    $recordno
     *
     * @return bool
     */
    public function UpdateResult($result, $recordno) {
		$source = "ARRecurPaymentManager::UpdateResult";
		$ok = $this->_QM->beginTrx($source);
		$ok = $ok && $this->DoQuery('QRY_ARRECURPAYMENT_UPDATE_LASTRESULT',array($result, $recordno));
		$ok = $ok && $this->_QM->commitTrx($source);
		if (!$ok) {
			$this->_QM->rollbackTrx($source);
		}
		
		return $ok;
	}

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function Translate(&$values) {
		global $gErr;
		$gManagerFactory = Globals::$g->gManagerFactory;
		$ok = true;
		if (empty($values['PAYMETHOD'])) { 
		    $values['PAYMETHOD'] = 'None'; 
		}
		if ($values['PAYMETHOD'] == 'None') {
			$values['PAYMENTAMOUNT'] = '';
			$values['CUSTOMERCREDITCARDKEY'] = '';
			$values['CUSTOMERBANKACCOUNTKEY'] = '';
			$values['CREDITCARDTYPE'] = '';
			$values['ACCOUNTTYPE'] = '';
			$values['BANKACCOUNTID'] = '';
			$values['GLACCOUNTKEY'] = '';			
		} else {
			if ($values['PAYMETHOD'] != 'Online Charge Card') {
				$values['CUSTOMERCREDITCARDKEY'] = '';
			}
			if ($values['PAYMETHOD'] != 'Online ACH Debit') {
				$values['CUSTOMERBANKACCOUNTKEY'] = '';
			}
			if ($values['PAYMETHOD'] != 'Credit Card') {
				$values['CREDITCARDTYPE'] = '';
			}
			if ($values['PAYINFULL'] != 'true' && empty($values['PAYMENTAMOUNT'])) {
				$gErr->addError('AR-0116', __FILE__ . ":" . __LINE__, 'Payment Amount is required');
				$ok = false;
			}
			if ($values['ACCOUNTTYPE'] == 'Bank') {
				if( $values['BANKACCOUNTID']) {
					list($bankaccountid) = explode('--',$values['BANKACCOUNTID']);
					$values['BANKACCOUNTID'] = $bankaccountid;
				}
				$values['GLACCOUNTKEY'] = '';
				if (empty($values['BANKACCOUNTID'])) {
					$gErr->addIAError('AR-0233', __FILE__ . ":" . __LINE__,
					    'Bank Account is required', [],
						'You have not entered a Bank Account or you do not have any bank accounts with a currency of ' . $values['CURRENCY'] . '.',
						['CURRENCY' => $values['CURRENCY']],
						'Create a ' . $values['CURRENCY'] . ' bank account in cash management.',
						['CURRENCY' => $values['CURRENCY']]
					);
					$ok = false;
				}
			} else {
				if($values['GLACCOUNTKEY']) {
					$glaccountMgr = $gManagerFactory->getManager('glaccount');
					list($glaccount) = explode('--',$values['GLACCOUNTKEY']);
		 			$account = $glaccountMgr->GetRaw($glaccount);
					if (!isset($account[0]) || ($account[0] == '')){
                        /** @noinspection PhpUndefinedVariableInspection */
                        $gErr->addIAError('AR-0234', GetFL(),
                            $values['GLACCOUNTKEY'] . " is not a valid GL accountno.", ['GLACCOUNTKEY' => $values['GLACCOUNTKEY']]
                        );
						$ok = false;
					} else {
						$values['GLACCOUNTKEY'] = $account[0]['RECORD#'];
					}
				}
				$values['BANKACCOUNTID'] = '';
			}
			if($values['CUSTOMERCREDITCARDKEY']) {
				$custcardMgr = $gManagerFactory->getManager('customercreditcard');
				$params = array(
					'filters' => array(array(
						array('CUSTOMERID', '=', $values['CUSTOMERID']),
						array('CARDID', '=', $values['CUSTOMERCREDITCARDKEY']),
					)),
				);
				$account = $custcardMgr->GetList($params);
				if (!empty($account[0])) {
					$values['CUSTOMERCREDITCARDKEY'] = $account[0]['RECORDNO'];
				} else {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $gErr->addIAError('AR-0235', __FILE__ . '.' . __LINE__,
                        sprintf('This Credit card does not belong to customer \'%s\'', $_customerId),
                        ['CUSTOMERID' => $_customerId]
                    );
					$ok = false;
				}
			}
			if($values['CUSTOMERBANKACCOUNTKEY']) {
				$custbankMgr = $gManagerFactory->getManager('customerbankaccount');
				list($bankname, $accountnumber) = explode('--',$values['CUSTOMERBANKACCOUNTKEY']);
				$params = array(
					'filters' => array(array(
						array('CUSTOMERID', '=', $values['CUSTOMERID']),
						array('BANKNAME', '=', $bankname),
						array('ACCOUNTNUMBER', '=', $accountnumber),
					)),
				);
				$account = $custbankMgr->GetList($params);
				if (!empty($account[0])) {
					$values['CUSTOMERBANKACCOUNTKEY'] = $account[0]['RECORDNO'];
				} else {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $gErr->addIAError('AR-0237', __FILE__ . '.' . __LINE__,
                        'This ACH account does not belong to customer '."'".$_customerId."'",
                        ['CUSTOMERID' => $_customerId]
                    );
					$ok = false;
				}
			}
		}
		return $ok;
	}

    /**
     * @param bool   $ok
     * @param array  $result
     * @param string $module
     */
    public static function SendNotifications($ok, $result, $module) {
//		logFL($result,'/tmp/invoicepayments.warren');
//		logFL($module,'/tmp/invoicepayments.warren');
		global $kARid;
		$gManagerFactory = Globals::$g->gManagerFactory;
		$prefs = GetModulePreferences($kARid);
		$expiredNotifyEmail = $prefs['EXPIREDNOTIFYEMAIL'];
		$from = GetValueForIACFGProperty("IA_COMPANYTITLE"). " Customer Support <noreply@".GetValueForIACFGProperty("IA_EMAILDOMAIN").">";
        $emailTokenObj = I18NEmailToken::buildFromResource('IA.EMAIL.AP.RECUR_PAYMENT_MANAGER');
		if ($ok) {
//			if ($arPrefs['NOTIFYRECURPAYMENTSUCCESS'] == 'T' && !empty($arPrefs['NOTIFYRECURPAYMENTEMAIL'])) {
//				$to = str_replace(';', ',', $arPrefs['NOTIFYRECURPAYMENTEMAIL']);
//				$subject = 'Successfully charged ' . $result['CUSTOMERNAME'];
//				$body = 'Recurring Payments has successfully charged ' . $result['CUSTOMERNAME'] . 
//					' ' . $paymentamount .
//					' using ' . $result['PAYMETHOD'] . '.';
//				if (!empty($returnValue['TRANSACTION_ID'])) {
//					$body .= ' Transaction id = ' . $returnValue['TRANSACTION_ID'];
//				}
//				logFL($body,'/tmp/invoicepayments.warren');
//				$email = new IAEmail($to);
//				$email->subject = $subject;
//				$email->body = $body;
//				$email->_Exec();
//			}
			if ($result['PAYMETHOD'] == 'Online Charge Card' && !empty($expiredNotifyEmail)) {
				$cardManager = $gManagerFactory->getManager('customercreditcard');
				$params = array(
					'filters' => array(array(
						array('CUSTOMERID', '=', $result['CUSTOMERID']),
						array('CARDID', '=', $result['CUSTOMERCREDITCARD']['CARDID']),
					)),
				);
//				logFL($params,'/tmp/invoicepayments.warren');
				$card = $cardManager->GetList($params);
				if (!empty($card[0])) { 
				    $card = $card[0]; 
				}
//				logFL($card,'/tmp/invoicepayments.warren');
				$curDate = $result['NEXTEXECDATE'];
				$curMonth = GetMonthFromDate($curDate);
				$curYear = GetYearFromDate($curDate);
				$curYearMonth = $curYear . $curMonth;
//				logFL($curYearMonth,'/tmp/invoicepayments.warren');
				$fieldInfo = $cardManager->GetFieldInfo('EXP_MONTH');
				$valMonth = $cardManager->_TransformValue($card['EXP_MONTH'], $fieldInfo['type'], 0);
				$valYear = $card['EXP_YEAR'];
				$valYearMonth = $valYear . $valMonth;
//				logFL($valYearMonth,'/tmp/invoicepayments.warren');
				if (bcsub($curYearMonth,$valYearMonth) > 0) {
					$to = str_replace(';', ',', $expiredNotifyEmail);
                    $subject = $emailTokenObj->applyPlaceholders('subject.text',[]);
					if ($module == $kARid) {
						$application = 'Accounts Receivable';
						$doctype = 'AR Invoice';
						$recordno = $result['RECORDNO'];
						$recurop = GetOperationId('ar/lists/arrecurinvoice/view');
						$recurpage = urlencode("fo=ar&.navop=$recurop&.do=view&.it=arrecurinvoice&.r=$recordno");
					} else {
						$application = 'Order Entry';
						$doctype = $result['DOCPARID'];
						$recordno = $result['RECUR_RECORDNO'];
						$recurop = GetOperationId('so/lists/sorecurdocument/view');
						$recurpage = urlencode("fo=so&.navop=$recurop&.do=view&.r=$recordno");
					}
					$recurpage .= '&.company=' . urlencode(URLCleanParams::insert('.company', GetMyCompanyTitle()));
			        $customerop = GetOperationId('ar/lists/customer/view');
			        $custpage = urlencode("fo=ar&.navop=$customerop&.do=view&.it=customer&.r={$result['CUSTOMERID']}");
			        $custpage .= '&.company=' . urlencode(URLCleanParams::insert('.company', GetMyCompanyTitle()));
					$loginURL = 'https://' . $result['HTTP_HOST'] . BasePath() . '/login.phtml';
                    $recurPageUrl = $loginURL . '?.done=frameset.phtml?.' . $recurpage;
                    $custPageUrl = $loginURL . '?done=frameset.phtml?' . $custpage;
                    $body = $emailTokenObj->applyPlaceholders('body.text', [
                        'APPLICATION' => $application,
                        'CUSTOMERNAME' => $result['CUSTOMERNAME'],
                        'DOCTYPE' => $doctype,
                        'RECORDNO' => $recordno,
                        'CARD' => $result['CUSTOMERCREDITCARD']['CARDID'],
                        'EXPMONTH' => $card['EXP_MONTH'],
                        'EXPYEAR' => $card['EXP_YEAR'],
                        'RECURPAGEURL' => $recurPageUrl,
                        'CUSTPAGEURL' => $custPageUrl
                    ]);

//					logFL($body,'/tmp/invoicepayments.warren');
                    $emailObj = I18NEmail::constructFromToken($emailTokenObj, $to);
					$emailObj->contenttype='text/html; charset="' . isl_get_charset() . '"';
                    $emailObj->_from = $from;
                    $emailObj->subject = $subject;
                    $emailObj->body = $body;
                    $emailObj->send();
				}
			}
		} else {
			$userInfoMgr = $gManagerFactory->getManager('userinfo');
			$emailResultSet = $userInfoMgr->_QM->DoQuery('QRY_USERINFO_ADMIN_RECURTXNFAIL_EMAIL', array('B', '1', '2', GetMyCompany(), 'RECURTXNFAILEMAIL', 'Y'));
//			logFL($emailResultSet,'/tmp/invoicepayments.warren');
			$to = '';
			foreach ($emailResultSet as $user) {
				if (!empty($user['EMAIL1'])) {
					$to .= $user['EMAIL1'] . ',';
				} elseif (!empty($user['EMAIL2'])) {
					$to .= $user['EMAIL2'] . ',';
				}
			}
//			logFL($to,'/tmp/invoicepayments.warren');
			if (!empty($to)) {
                $subject = $emailTokenObj->applyPlaceholders('subject.notok',[]);
				//$subject = 'Recurring Transaction Error Notification - Payment failure';
                $body = $emailTokenObj->applyPlaceholders('body.notok', [
                    'WHENCREATED' => $result['WHENCREATED']
                ]);
//				$body = "Hello,
//
//A recurring transaction payment you had scheduled for creation on {$result['WHENCREATED']} failed.
//
//For electronic payments, please go to Accounts Receivable > Records > Electronic Payments to see the reason for the failure.
//
//If you need to talk with someone in regards to this error notification message, contact your Intacct Customer Support Representative.
//
//Thank You.
//
//Intacct Customer Support
//www.intacct.com
//";
				if (!empty($returnValue['MESSAGE'])) {
					$body .= ' ' . $returnValue['MESSAGE'];
				}
//				logFL($body,'/tmp/invoicepayments.warren');
                $emailObj = I18NEmail::constructFromToken($emailTokenObj, $to);
                $emailObj->_from = $from;
                $emailObj->subject = $subject;
                $emailObj->body = $body;
                $emailObj->send();
			}
		}
	}
}