<?php
import('Editor');
require_once 'backend_module.inc';
require_once 'backend_partner.inc';

/**
 * Class ObpsetupEditor
 */
class ObpsetupEditor extends Editor
{

    /**
     * @param array $_params
     */
    function ShowFormButtons($_params ) {
        $_params['buttons']['deliverbutton'] = 0;
        $_params['buttons']['deliveraction'] = 0;
        $_params['buttons']['saveandnewbutton'] = 0;
        $_params['buttons']['saveandnewaction'] = 0;
        Editor::ShowFormButtons($_params);
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    function ProcessCreateAction(&$_params) {
        $entityMgr = Globals::$g->gManagerFactory->getManager('obpsetup');
        $obj =& Request::$r->GetCurrentObject();
        $ok = $this->PrepareInputValues($_params, $obj);

        $ok = $ok && $entityMgr->set($obj);        
        
        $entityDesc = $_params['entityDesc'];

        if (!$ok) { 
            global $gErr;
            $gErr->addIAError(
                'AR-0653', __FILE__.":".__LINE__, "Updating $entityDesc failed", ['ENTITY_DESC' => $entityDesc]
            );
            $this->state = $this->kErrorState;
        }

        return $ok;
    }

    /**
     * @param array $_params
     *
     */
    function ProcessEditAction(&$_params) {
        $entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);

        if ($this->ProcessErrorRetrivalAction($entityMgr)) {        
            return;
        }
        Request::$r->_changed = false;

        $ID = Request::$r->_r;

        $obj = $entityMgr->get($ID);
        if (!$obj) {
            global $gErr;
            $entityDesc = $_params['entityDesc'];
            /** @noinspection PhpUndefinedVariableInspection */
            $gErr->addIAError(
                'AR-0654', __FILE__ . ":" . __LINE__, "Fetching $entityDesc '$objId' failed", ['DESCRIPTION' => $entityDesc, 'ID' => $objId]
            );
            $this->state = $this->kErrorState;
        }
        else {
            // If Module is not installed, show the default e-mail id
            if (!IsModuleIdInstalled('26.OBP')) {
                $obj['CONTACTEMAIL'] = GetEmailForNotification();
            } else {
                $prefs = GetModulePreferences('26.OBP');
                $contactmailsaved = false;
                // NOTE: In this case, isset can not be used, as this field is optional 
                // and when user saves a Empty E-Mail, value of CONTACTMAIL is 'undef'
                foreach ( $prefs as $key => $value) {
                    if ($key == 'CONTACTEMAIL') {
                        $contactmailsaved = true;
                    }
                }
                // If the Contact E-Mail is not set in the ModulePref table,
                // Get the default e-mail
                if (!$contactmailsaved) {
                    $obj['CONTACTEMAIL'] = GetEmailForNotification();
                }
            }

            Request::$r->SetCurrentObject($obj);
            $this->state = $this->kShowEditState;
        }
    }

    /**
     * @param array $_params
     */
    function ProcessCancelAction(&$_params) {
        /* @var URLS $gURLs */
        global $gURLs;

        /** @noinspection PhpUnusedLocalVariableInspection */
        $entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);
        //		DeinstallModule($_r);
        $gURLs->Ret();
    }

    //Turn off the save and new button
    function ShowNew() 
    {
        unset($this->_params['buttons']['saveandnewbutton']); 
        $this->ShowTop();
    }

    /**
     * @param array $_params
     *
     * @return mixed
     */
    function Editor_Expand($_params) {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);
        return Editor::Editor_Expand($_params);
    }

    /**
     * @param array $_params
     */
    function Editor_Instantiate(&$_params)
    {
        global $ims_live;

        /** @noinspection PhpUnusedLocalVariableInspection */
        $entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);
        parent::Editor_Instantiate($_params);
        foreach($_params['allfields'] as $key => $field) {
                
            if ($field['path'] == "AGREEMENTLINK" ) {
                if ($ims_live) {
                    $pricing = "https://www.intacct.com/ia/acct/integration_pricing.phtml";
                }
                else {
                    $pricing = "http://dev01.intacct.com" . RootPath() . "/acct/integration_pricing.phtml";
                }

                $url = 'module_agreement.phtml?.partner=' . $pricing; 
                $_params['allfields'][$key]['onclick'] ="javascript:Launch('$url','Service',575,450)
					";
                $_params['allfields'][$key]['value'] ="(Read the Service Agreement)";
            }
        }
    }
}
