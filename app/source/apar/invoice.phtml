<?php
//=============================================================================
//
//	FILE:			invoice.phtml
//	AUTHOR:			rpn
//	DESCRIPTION:	Source file for pdf generation
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'util.inc';
require_once 'backend_pdf.inc';
require_once 'backend_invoice.inc';
require_once 'backend_delivery.inc';

Init();
$_r          = Request::$r->_r;
$_op         = Request::$r->_op;
$_entity     = &Request::$r->_entity;
$section     = Request::$r->section;
$mod         = Request::$r->mod;
$_rec        = Request::$r->_rec;
$_markettext = Request::$r->_markettext;
$_message	 = Request::$r->_message;
$_object     = Request::$r->_object;
$_baseobject = &Request::$r->_baseobject;
$entity      = &Request::$r->entity;
$_invformat  = Request::$r->_invformat;

if ($_object || $_rec) {

    if (isl_trim($_rec)) {
        $wheres['RECORD#'] = isl_trim($_rec);
    } 
    if (isl_trim($_object)) {
        $wheres['OBJECT'] = isl_trim($_object);
    }
    if (isl_trim($_entity)) {
        $wheres['ENTITY'] = isl_trim($_entity);
    }
    /** @noinspection PhpUndefinedVariableInspection */
    $objArr = GetDeliveryLog($wheres);
    $xml = $objArr[0]['XML'];

    if (isl_trim($_object) == 'doc_temp') {
        header("Content-type: application/pdf");
        header("Content-Disposition: filename=\"report.pdf\"");
        header("Content-Length: " . strlen($xml));
        echo($xml);
    } else {
        if (!isl_strstr($xml, "<?xml version=\"1.0\"")) {
            $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>". $xml;
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $fo = GetInvoiceFO($xml, $_invformat);
        genPDF($fo);
    }

    exit();

} else {

   $securityKey = GetOperation($_op);
        
    list($mod, $section, $entity, $verb) = explode('/', $securityKey);
    if ( !isset($_baseobject) || $_baseobject == '' ) {
        $_baseobject = $entity;
    }

    /** @noinspection PhpUndefinedVariableInspection */
    $params = array(
        '_r'            => $_r,
        'xsltemplateid' => $_invformat,
        'message'        => isl_html_to_charset($_message, true),
        'markettext'     => isl_html_to_charset($_markettext, true)
    );

    $editor = GetEntityEditor($_baseobject);
    assert($editor instanceof SubLedgerTxnEditor);
    Request::$r->_deliverymethod = 'pdfpreview';
    $editor->Editor_Deliver($params, false, $out);
}

