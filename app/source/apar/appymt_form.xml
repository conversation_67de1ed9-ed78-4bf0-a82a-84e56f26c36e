<?xml version="1.0" encoding='utf-8'?>
<ROOT>
    <title>IA.PAY_BILLS</title>
    <view id="paybills" system="true" compact="true">
        <pages>
            <page title="IA.SELECT_BILLS" id="select_bills" hidden="false" customFields="no">
                <!-- Force vertical components to form width. Flexstack inherits from flexcolumn. -->
                <flexstack>

                    <xi:include href="appymt_form_filtering.xml" xmlns:xi="http://www.w3.org/2003/XInclude" />

                    <!-- horizontal header with space between left & right child and distinctive background color for
                        first and secondary children. Flex rows & columns can do double duty as
                         children. Flexheader is a descendent of flexrow.
                         -->
                    <flexheader customFields="no">
                        <!-- Allow child to grow horizontally and stretch vertically. The number given to the 'grow' and 'shrink'
                        attributes, is the rate at which the child grows or shrinks compared to other children.
                         twice the space for another one. Must use a flexchild since containers do not support flex
                         properties.
                         -->
                        <flexchild id="payInfoSection" grow="1" shrink="1" align="stretch" customFields="no">
                            <!-- Grid contains 12 cells. Grid will have 3 columns consisting
                                of 3, 5, and 4 cells respectively. '4 4 4' would result in 3 equally sized columns.
                                -->
                            <gridcontainer columncells="3 3 3" responsive="true" customFields="no">
                                <field fullname="IA.PAYMENT_PROVIDER" hidden="true">
                                    <path>PAYMENTPROVIDER_D</path>
                                    <type>
                                        <type>enum</type>
                                        <ptype>enum</ptype>
                                    </type>
                                    <events>
                                        <change>populatePaymentMethod();</change>
                                    </events>
                                </field>
                                <field fullname="IA.PAYMENT_METHOD">
                                    <path>PAYMENTMETHOD_D</path>
                                    <type>
                                        <type>enum</type>
                                        <ptype>enum</ptype>
                                    </type>
                                    <events>
                                        <change>populateFinancialEntity(false, true);</change>
                                    </events>
                                </field>
                                <field fullname="IA.BANK_CURRENCY" hidden="true">
                                    <path>BANKCURRENCY</path>
                                    <type>
                                        <type>ptr</type>
                                        <ptype>ptr</ptype>
                                        <entity>trxcurrencies</entity>
                                    </type>
                                    <events>
                                        <change>populateFinancialEntity(false, false);</change>
                                    </events>
                                </field>
                                <field fullname="IA.BILL_CURRENCY" hidden="true" userUIControl="BillCurrencyControl">
                                    <path>BILLCURRENCY</path>
                                    <required>true</required>
                                    <type>
                                        <type>ptr</type>
                                        <ptype>ptr</ptype>
                                        <entity>trxcurrencies</entity>
                                    </type>
                                    <events>
                                        <change>populateFinancialEntity(true, true);</change>
                                    </events>
                                </field>
                                <field fullname="IA.PAYMENT_REQUEST_METHOD">
                                    <path>PAYMENTREQUESTMETHOD</path>
                                    <type>
                                        <type>text</type>
                                        <ptype>enum</ptype>
                                    </type>
                                </field>
                                <field fullname="IA.BANK" ignoreLargeDataPicker="true">
                                    <path>FINANCIALENTITY</path>
                                    <type>
                                        <type>webcombo</type>
                                        <ptype>webcombo</ptype>
                                    </type>
                                    <events>
                                        <change>financialEntityHandler(this.meta, true);</change>
                                    </events>
                                </field>
                                <field fullname="IA.CHARGE_CARD" required="true" hidden="true">
                                    <path>CREDITCARD</path>
                                    <type>
                                        <type>webcombo</type>
                                        <ptype>webcombo</ptype>
                                    </type>
                                    <events>
                                        <change>applyFilters();</change>
                                    </events>
                                </field>
                                <field fullname="IA.USE_BASE_CURRENCY_BANKS_TO_PAY" hidden="true" groupWith="FINANCIALENTITY">
                                    <path>USEBASECURRBANK</path>
                                    <fieldentity>bankaccountentity</fieldentity>
                                    <type><type>boolean</type></type>
                                    <events>
                                        <change>populateFinancialEntity(false, false);</change>
                                    </events>
                                </field>
                                <field hidden="true">
                                    <path>BANKACCOUNTTYPE</path>
                                </field>
                                <field hidden="true">
                                    <path>PRINTON</path>
                                </field>
                                <field hidden="true">
                                    <path>ACHBANKKEY</path>
                                </field>
                                <field fullname="IA.EXCHANGE_RATE_TYPE" hidden="true">
                                    <path>EXCH_RATE_TYPE_ID</path>
                                </field>
                                <field fullname="IA.SET_PAYMENT_DATE_TO">
                                    <path>WHENPAID</path>
                                    <events>
                                        <change>applyPaymentDate(this.meta, false);</change>
                                    </events>
                                </field>
                                <field fullname="IA.DESCRIPTION" hidden="true">
                                    <path>AMEXPYMYDESC</path>
                                    <type>
                                        <type>textarea</type>
                                        <ptype>textarea</ptype>
                                    </type>
                                </field>
                                <button id="estimateDelivery" order="4" bsize="full" hidden="true">
                                    <name>IA.ESTIMATE_DELIVERY</name>
                                    <events>
                                        <click>openEstimatedDeliveryDetailsPage(this.meta); false;</click>
                                    </events>
                                </button>
                                <field fullname="IA.CHECK_DELIVERY_OPTIONS" hidden="true">
                                    <path>WFPMDELIVERYMETHOD</path>
                                    <type>
                                        <type>text</type>
                                        <ptype>enum</ptype>
                                    </type>
                                </field>
                                <field fullname="IA.NEXT_PAYMENT_PROCESS_DATE" hidden="true" readonly="true">
                                    <path>WFPMPROCESSINGDATE</path>
                                    <type assoc="T">
                                        <type>date</type>
                                        <ptype>date</ptype>
                                    </type>
                                </field>
                                <field fullname="IA.SEND_PAYMENT_NOTIFICATIONS_FROM">
                                    <path>EMAILCONTACT_D</path>
                                    <type assoc="T">
                                        <ptype>ptr</ptype>
                                        <type>text</type>
                                        <entity>contact</entity>
                                        <pickentity>contact</pickentity>
                                    </type>
                                    <events>
                                        <change>populateContactEmail(this.meta);</change>
                                    </events>
                                </field>
                                <field fullname="IA.PAYMENT_DESCRIPTION" hidden="true">
                                    <path>PYMTDESCRIPTION</path>
                                    <type>
                                        <type>textarea</type>
                                        <ptype>textarea</ptype>
                                    </type>
                                </field>
                            </gridcontainer>
                        </flexchild>
                        <!-- Start at 25% of available space and grow to fill available space. Center children by putting space around them. -->
                        <flexrow id="bankInfoSection" grow="1" shrink="0" basis="18%" align="stretch"
                                 justify_content="space-around">
                            <flexchild align="center" customFields="no">
                                <!-- Create a table that uses the label of a field as left column and value as right column.
                                Attributes are propagated to children. Column alignment indicates the alignment for the field
                                 in cell.
                                 -->
                                <stmttable align_columns="left right" readonly="true" wrap_columns="nowrap">
                                    <stmtrow>
                                        <field hidden="true" readonly="true">
                                            <path>MAX_PAYMENT_LIMIT</path>
                                            <fullname>IA.14_DAY_REVOLVING_LIMIT</fullname>
                                        </field>
                                    </stmtrow>
                                    <stmtrow>
                                        <field hidden="true" readonly="true">
                                            <path>REMAINING_BALANCE</path>
                                            <fullname>IA.AMOUNT_STILL_AVAILABLE</fullname>
                                        </field>
                                    </stmtrow>
                                    <stmtrow>
                                        <field fullname="IA.BANK_BALANCE">
                                            <path>BANKBALANCE</path>
                                        </field>
                                    </stmtrow>
                                    <stmtrow>
                                        <field fullname="IA.SELECTED_BILLS_TOTAL">
                                            <path>BILLSELECTEDAMOUNT</path>
                                        </field>
                                    </stmtrow>
                                    <stmttotal>
                                        <field fullname="IA.ADJUSTED_BANK_BALANCE">
                                            <path>ADJBANKBALANCE</path>
                                        </field>
                                    </stmttotal>
                                    <stmttotal>
                                        <field fullname="IA.KEY_OF_BILLS_SELECTED">
                                            <path>TOTALPAYABLESSELECTED</path>
                                            <default>0</default>
                                        </field>
                                    </stmttotal>
                                </stmttable>
                            </flexchild>
                        </flexrow>
                    </flexheader>
                    <flexstack id="creditToggleSection">
                        <gridsection customFields="no">
                            <grid path="PAYABLES" noDragDrop="true" className="right_border" hasFixedNumOfRows="true"
                                  noNewRows="true" showDelete="false" customFields="no" clazz="PayablesGrid"
                                  enableSelect="true" hasSort = "false">
                                <selectColumn></selectColumn>
                                <gridBulkActions>
                                    <button id="bulkAction" path="bulkAction">
                                        <name>IA.APPLY_CREDITS</name>
                                        <events>
                                            <click>applyCredits();</click>
                                        </events>
                                    </button>
                                    <button id="bulkAction1" path="bulkAction1">
                                        <name>IA.CLEAR_CREDITS</name>
                                        <events>
                                            <click>clearCredits();</click>
                                        </events>
                                    </button>
                                    <button id="bulkAction2" path="bulkAction2">
                                        <name>IA.CLEAR_PAYMENT_AMOUNT</name>
                                        <events>
                                            <click>clearAmtToPay();</click>
                                        </events>
                                    </button>
                                </gridBulkActions>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" readonly="true" hidden="true" required="false" searchPath="COMPLIANCE_ICON" >
                                            <path>COMPLIANCE_ICON_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.VENDOR_COMPLIANCE' path="COMPLIANCE_ICON" readonly="true" hidden="true" 
                                           sortable="false">
                                        <href>javascript:void(0);</href>
                                        <events>
                                            <click>openComplianceList(this.meta);</click>
                                        </events>
                                        <type assoc="T">
                                            <type>text</type>
                                            <ptype>href</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="VENDORNAME" >
                                            <path>VENDORNAME_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.VENDOR' sortable="false">
                                        <path>VENDORNAME</path>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>VENDORID</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="RECORDID">
                                            <path>RECORDID_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.BILL_NUMBER_SYMBOL' sortable="false" isToken="true">
                                        <path>RECORDID</path>
                                        <type type='text' ptype='href'></type>
                                        <events>
                                            <click>openDrilldownPage(this.meta);</click>
                                        </events>
                                        <default>IA.VIEW_DETAILS</default>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="WHENCREATED">
                                            <path>WHENCREATED_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.BILL_DATE' readonly="true" required="false" sortable="false">
                                        <path>WHENCREATED</path>
                                        <type assoc="T">
                                            <type>date</type>
                                            <ptype>date</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="WHENDUE">
                                            <path>WHENDUE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.DUE_DATE' readonly="true" sortable="false">
                                        <path>WHENDUE</path>
                                        <type assoc="T">
                                            <type>date</type>
                                            <ptype>date</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="INVCURRENCY">
                                            <path>INVCURRENCY_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.CURRENCY' required="false" readonly='true' sortable="false">
                                        <path>INVCURRENCY</path>
                                        <type assoc="T">
                                            <type>text</type>
                                            <ptype>text</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="TRX_TOTALDUE">
                                            <path>TRX_TOTALDUE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.AMOUNT_DUE' hasTotal="true" sortable="false">
                                        <path>TRX_TOTALDUE</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="WHENPAID">
                                            <path>WHENPAID_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.PAYMENT_DATE" clazz="whenPaidPayable" sortable="false">
                                        <path>WHENPAID</path>
                                        <events>
                                            <change>applyLinePaymentDate(this.meta, false, false);</change>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.PREFERRED_PAYMENT_TYPE" readonly="true">
                                        <path>PAYMENTTYPE</path>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>TERMVALUE</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="CREDITSAVAILABLE">
                                            <path>CREDITSAVAILABLE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field readonly="true" formatZero="true" sortable="false">
                                        <path>CREDITSAVAILABLE</path>
                                        <fullname>IA.CREDITS_AVAILABLE</fullname>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="CREDITSAPPLIED">
                                            <path>CREDITSAPPLIED_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field hasTotal="true" sortable="false">
                                        <path>CREDITSAPPLIED</path>
                                        <fullname>IA.CREDITS_TO_APPLY</fullname>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                        <events>
                                            <change>applyBillCredits(this.meta, false, false);</change>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="DISCOUNTAPPLIED">
                                            <path>DISCOUNTAPPLIED_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label="IA.DISCOUNTS" path="DISCOUNTAPPLIED" readonly="true" formatZero="true"
                                           clazz="discountPayable" sortable="false">
                                        <href>javascript:void(0);</href>
                                        <events>
                                            <click>openDiscountDetailsPage(this.meta); false;</click>
                                        </events>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>RECORDNO</path>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>ENTITY</path>
                                    </field>
                                </column>
                                <column>
                                    <field hasTotal="true">
                                        <path>PAYMENTAMOUNT</path>
                                        <fullname>IA.AMOUNT_TO_PAY</fullname>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                        <events>
                                            <change>populateBillPayment(this.meta, false, false);</change>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>JOINTDETAILS</path>
                                        <type type='text' ptype='href'></type>
                                        <hreftxt>IA.PAYEE_DETAILS</hreftxt>
                                        <events>
                                            <click>populatePayableJointDetails(this);</click>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <field>
                                        <path>LINEDETAILS</path>
                                        <type type='text' ptype='href'></type>
                                        <hreftxt>IA.LINE_DETAILS</hreftxt>
                                        <events>
                                            <click>populatePayableLineDetails(this);</click>
                                        </events>
                                    </field>
                                </column>
                            </grid>
                            <grid path="PAYABLES_SERVERSIDE" noDragDrop="true" hidden="true" className="right_border" hasFixedNumOfRows="true"
                                  noNewRows="true" showDelete="false" customFields="no" clazz="PayablesGrid"
                                  enableSelect="true">
                                <selectColumn></selectColumn>
                                <serverPagination>true</serverPagination>
                                <drawBatchSize>1000</drawBatchSize>
                                <maxRows>500</maxRows>
                                <enableEmptyMessage>true</enableEmptyMessage>
                                <emptyMessage>IA.NO_RECORDS_TO_DISPLAY</emptyMessage>
                                <gridBulkActions>
                                    <button id="bulkAction" path="bulkAction">
                                        <name>IA.APPLY_CREDITS</name>
                                        <events>
                                            <click>creditHandler.applyCreditFromHeaderPage(true);</click>
                                        </events>
                                    </button>
                                    <button id="bulkAction1" path="bulkAction1">
                                        <name>IA.CLEAR_CREDITS</name>
                                        <events>
                                            <click>headerGridHandler.clearCredits();</click>
                                        </events>
                                    </button>
                                    <button id="bulkAction2" path="bulkAction2">
                                        <name>IA.CLEAR_PAYMENT_AMOUNT</name>
                                        <events>
                                            <click>headerGridHandler.clearAmtToPay();</click>
                                        </events>
                                    </button>
                                </gridBulkActions>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" readonly="true" hidden="true" required="false" searchPath="COMPLIANCE_ICON" >
                                            <path>COMPLIANCE_ICON_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='Compliance' readonly="true" hidden="true" required="false" sortable="false">
                                        <path>COMPLIANCE_ICON</path>
                                        <type type='text' ptype='href'></type>
                                        <events>
                                            <click>openComplianceList(this.meta);</click>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="VENDORNAME" >
                                            <path>VENDORNAME_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.VENDOR' sortable="false">
                                        <path>VENDORNAME</path>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>VENDORID</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="RECORDID">
                                            <path>RECORDID_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.BILL_NUMBER_SYMBOL' sortable="true" isToken="true">
                                        <path>RECORDID</path>
                                        <type type='text' ptype='href'></type>
                                        <events>
                                            <click>commonHandler.openDrilldownPage(this.meta);</click>
                                        </events>
                                        <default>IA.VIEW_DETAILS</default>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="WHENCREATED">
                                            <path>WHENCREATED_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.BILL_DATE' readonly="true" required="false" sortable="true">
                                        <path>WHENCREATED</path>
                                        <type assoc="T">
                                            <type>date</type>
                                            <ptype>date</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="WHENDUE">
                                            <path>WHENDUE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.DUE_DATE' readonly="true" sortable="true">
                                        <path>WHENDUE</path>
                                        <type assoc="T">
                                            <type>date</type>
                                            <ptype>date</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="INVCURRENCY">
                                            <path>INVCURRENCY_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.CURRENCY' required="false" readonly='true' sortable="false">
                                        <path>INVCURRENCY</path>
                                        <type assoc="T">
                                            <type>text</type>
                                            <ptype>text</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="TRX_TOTALDUE">
                                            <path>TRX_TOTALDUE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label='IA.AMOUNT_DUE' hasTotal="true" sortable="false">
                                        <path>TRX_TOTALDUE</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="WHENPAID">
                                            <path>WHENPAID_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.PAYMENT_DATE" clazz="whenPaidPayable" sortable="false">
                                        <path>WHENPAID</path>
                                        <events>
                                            <change>headerGridHandler.changePaymentDateFromRow(this.meta, false);</change>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.PREFERRED_PAYMENT_TYPE" readonly="true">
                                        <path>PAYMENTTYPE</path>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>TERMVALUE</path>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>INITIALCREDITSAVAILABLE</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="CREDITSAVAILABLE">
                                            <path>CREDITSAVAILABLE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field readonly="true" formatZero="true" sortable="false">
                                        <path>CREDITSAVAILABLE</path>
                                        <fullname>IA.CREDITS_AVAILABLE</fullname>
                                        <href>javascript:void(0);</href>
                                        <events>
                                            <click>PaybillsServerSideCommon.openFloatingPage('CREDITS_SERVERSIDE_PAGE', this.meta, false);</click>
                                        </events>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="CREDITSAPPLIED">
                                            <path>CREDITSAPPLIED_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field hasTotal="true" sortable="false">
                                        <path>CREDITSAPPLIED</path>
                                        <fullname>IA.CREDITS_TO_APPLY</fullname>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                        <events>
                                            <change>creditHandler.applyCreditFromHeaderPage(false,this.meta);</change>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading hidden="true">
                                        <field noLabel="true" searchPath="DISCOUNTAPPLIED">
                                            <path>DISCOUNTAPPLIED_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field label="IA.DISCOUNTS" path="DISCOUNTAPPLIED" readonly="true" formatZero="true"
                                           clazz="discountPayable" sortable="false">
                                        <href>javascript:void(0);</href>
                                        <events>
                                            <click>PaybillsServerSideCommon.openFloatingPage('DISCOUNT_SERVERSIDE_PAGE', this.meta, false);</click>
                                        </events>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>RECORDNO</path>
                                    </field>
                                </column>
                                <column>
                                    <field hidden="true">
                                        <path>ENTITY</path>
                                    </field>
                                </column>
                                <column>
                                    <field hasTotal="true">
                                        <path>PAYMENTAMOUNT</path>
                                        <fullname>IA.AMOUNT_TO_PAY</fullname>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                        <events>
                                            <change>commonHandler.manualEnterAmtToPay(this.meta, false);</change>
                                        </events>
                                    </field>
                                </column>
                                <column>
                                    <field>
                                        <path>LINEDETAILS</path>
                                        <type type='text' ptype='href'></type>
                                        <hreftxt>IA.LINE_DETAILS</hreftxt>
                                        <events>
                                            <click>PaybillsServerSideCommon.openFloatingPage('BILLLINEDETAILS_SERVERSIDE_PAGE', this.meta, false);</click>
                                        </events>
                                    </field>
                                </column>
                            </grid>
                        </gridsection>
                    </flexstack>
                </flexstack>
            </page>
        </pages>
        <!-- Credits floating page -->
        <xi:include href="appymt_creditspage.xml" xmlns:xi="http://www.w3.org/2003/XInclude" />
        <!-- Discounts floating page -->
        <floatingPage modal="true" compact="true" autoHideLoading="false">
            <events>
                <close>closeFloatingPage('DISCOUNTPAGE', false)</close>
            </events>
            <title>IA.APPLY_DISCOUNT</title>
            <id>DISCOUNTPAGE</id>
            <path>DISCOUNTPAGE</path>
            <pages>
                <page>
                    <flexstack>
                        <!-- provide page padding -->
                        <flexsection>
                            <flexstack>
                                <flexrow>
                                    <field fullname="IA.BILL_KEY" readonly="true">
                                        <path>RECORDID_D</path>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.PAYMENT_DATE">
                                        <path>WHENPAID_D</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                        <events>
                                            <change>applyLinePaymentDate(this.meta, true, false);</change>
                                        </events>
                                    </field>
                                    <field hidden="true">
                                        <path>ROWNUM</path>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.DISCOUNT_CUT_OFF" readonly="true">
                                        <path>WHENDISCOUNT_D</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.DISCOUNT_AS_OF">
                                        <path>DISCOUNTDATE</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                        <events>
                                            <change>applyLinePaymentDate(this.meta, true, true);</change>
                                        </events>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.DISCOUNT_AVAILABLE" readonly="true">
                                        <path>DISCOUNTAPPLIED_D</path>
                                    </field>
                                </flexrow>
                            </flexstack>
                        </flexsection>
                    </flexstack>
                </page>
            </pages>
            <footer>
                <button>
                    <name>IA.SAVE</name>
                    <events>
                        <click>closeFloatingPage('DISCOUNTPAGE', true);</click>
                    </events>
                </button>
                <button>
                    <name>IA.CANCEL</name>
                    <events>
                        <click>closeFloatingPage('DISCOUNTPAGE', false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
        <floatingPage modal="true" compact="true" autoHideLoading="false">
            <title>IA.APPLY_DISCOUNT</title>
            <id>DISCOUNT_SERVERSIDE_PAGE</id>
            <path>DISCOUNT_SERVERSIDE_PAGE</path>
            <pages>
                <page>
                    <flexstack>
                        <!-- provide page padding -->
                        <flexsection>
                            <flexstack>
                                <flexrow>
                                    <field fullname="IA.BILL_KEY" readonly="true">
                                        <path>RECORDID_DISCOUNT</path>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.PAYMENT_DATE">
                                        <path>WHENPAID_DISCOUNT</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                        <events>
                                            <change>headerGridHandler.changePaymentDateFromRow(this.meta, true);</change>
                                        </events>
                                    </field>
                                    <field hidden="true">
                                        <path>ROWNUM_DISCOUNT</path>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.DISCOUNT_CUT_OFF" readonly="true">
                                        <path>WHENDISCOUNT_DISCOUNT</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.DISCOUNT_AS_OF">
                                        <path>DISCOUNTDATE_DISCOUNT</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                        <events>
                                            <change>headerGridHandler.changePaymentDateFromRow(this.meta, true);</change>
                                        </events>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.DISCOUNT_AVAILABLE" readonly="true">
                                        <path>DISCOUNTAPPLIED_DISCOUNT</path>
                                    </field>
                                </flexrow>
                            </flexstack>
                        </flexsection>
                    </flexstack>
                </page>
            </pages>
            <footer>
                <button>
                    <name>IA.SAVE</name>
                    <events>
                        <click>PaybillsServerSideCommon.closeFloatingPage('DISCOUNT_SERVERSIDE_PAGE', true);</click>
                    </events>
                </button>
                <button>
                    <name>IA.CANCEL</name>
                    <events>
                        <click>PaybillsServerSideCommon.closeFloatingPage('DISCOUNT_SERVERSIDE_PAGE', false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
        <floatingPage move="true" resize="true" close="true" fixedcenter="true" modal="true" fullscreen="true" topbuttons="true">
            <title>IA.APPLY_CREDIT</title>
            <id>CREDITS_SERVERSIDE_PAGE</id>
            <path>CREDITS_SERVERSIDE_PAGE</path>
            <pages>
                <page>
                    <flexstack id="creditToggleSection">
                        <section>
                            <field hidden="true">
                                <path>BILLROWNUM</path>
                            </field>
                            <field hidden="true">
                                <path>BILLLINEROWNUM</path>
                            </field>
                            <field hidden="true">
                                <path>BILLRECORDKEY</path>
                            </field>
                            <field hidden="true">
                                <path>BILLLINERECORDKEY</path>
                            </field>
                        </section>
                        <gridsection>
                            <grid path="CREDITS_SERVERSIDE" noDragDrop="true" className="columns3Grid" hasFixedNumOfRows="true"
                                  noNewRows="true" deleteOnGrid="false" showDelete="false"  enableSelect="true">
                                <selectColumn></selectColumn>
                                <serverPagination>true</serverPagination>
                                <drawBatchSize>1000</drawBatchSize>
                                <maxRows>500</maxRows>
                                <enableEmptyMessage>true</enableEmptyMessage>
                                <emptyMessage>IA.NO_RECORDS_TO_DISPLAY</emptyMessage>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="CREDITTYPE" >
                                            <path>CREDITTYPE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.CREDIT_TYPE" readonly='true' sortable="true">
                                        <path>CREDITTYPE</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="RECORDID" >
                                            <path>RECORDID_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.REFERENCE_NUMBER" readonly='true' isToken="true" sortable="true">
                                        <path>RECORDID</path>
                                        <type type='text' ptype='href'></type>
                                        <events>
                                            <click>commonHandler.openDrilldownPage(this.meta);</click>
                                        </events>
                                        <default>IA.VIEW_DETAILS</default>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="LINE_NO" >
                                            <path>LINE_NO_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.LINE_KEY" readonly='true' sortable="true">
                                        <path>LINE_NO</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="CREDITDATE" >
                                            <path>CREDITDATE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.CREDIT_DATE" readonly='true' required="false" sortable="true">
                                        <path>CREDITDATE</path>
                                        <type assoc="T">
                                            <type>date</type>
                                            <ptype>date</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="ACCOUNT" >
                                            <path>ACCOUNTLABEL_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.ACCOUNT" readonly="true" sortable="true">
                                        <path>ACCOUNTLABEL</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="DEPT" >
                                            <path>DEPT_TITLE_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.DEPARTMENT" readonly="true" sortable="true">
                                        <path>DEPT_TITLE</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="LOCATION" >
                                            <path>LOCATION_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.LOCATION" readonly="true" sortable="true">
                                        <path>LOC_TITLE</path>
                                    </field>
                                </column>
                                <column>
                                    <gridHeading>
                                        <field noLabel="true" searchPath="DOCNUMBER" >
                                            <path>DOCNUMBER_SEARCH</path>
                                        </field>
                                    </gridHeading>
                                    <field fullname="IA.REFERENCE_BILL_NUMBER" readonly="true" sortable="true">
                                        <path>DOCNUMBER</path>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.AMOUNT" readonly="true">
                                        <path>TRX_AMOUNT</path>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.CREDITS_AVAILABLE" readonly="true" formatZero="true">
                                        <path>CREDITAVAILABLE</path>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.CREDITS_TO_APPLY" hasTotal="true" readonly="false">
                                        <path>CREDITAPPLIED</path>
                                        <type assoc="T">
                                            <ptype>currency</ptype>
                                            <type>decimal</type>
                                            <maxlength>14</maxlength>
                                        </type>
                                        <default>0</default>
                                        <events>
                                            <change>creditHandler.manualCreditChangeFromPopUp(this.meta);</change>
                                        </events>
                                    </field>
                                </column>
                            </grid>
                        </gridsection>
                    </flexstack>
                </page>
            </pages>
            <footer>
                <button id="creditSaveButton">
                    <name>IA.SAVE</name>
                    <events>
                        <click>PaybillsServerSideCommon.closeFloatingPage('CREDITS_SERVERSIDE_PAGE', true, this.meta);</click>
                    </events>
                </button>
                <button>
                    <name>IA.CANCEL</name>
                    <events>
                        <click>PaybillsServerSideCommon.closeFloatingPage('CREDITS_SERVERSIDE_PAGE', false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
        <floatingPage id="BILLLINEDETAILS_SERVERSIDE_PAGE"  modal="true" fullscreen="true" topbuttons="true">
            <title>IA.BILL_DETAILS</title>
            <pages>
                <page className="columnSetupPadding">
                    <section id="billLineControlSection" className="horizontal headerCustom" customFields="no">
                        <field hidden="true">
                            <path>BILLRECORDNO</path>
                        </field>
                        <field hidden="true">
                            <path>RECORDTYPE</path>
                        </field>
                        <field fullname="IA.BILL_KEY" readonly="true" isToken="true">
                            <path>BILLRECORDID</path>
                            <type type='text' ptype='href'></type>
                            <events>
                                <click>commonHandler.openDrilldownPage(this.meta);</click>
                            </events>
                            <default>IA.VIEW_DETAILS</default>
                        </field>
                        <field fullname="IA.VENDOR" readonly="true">
                            <path>BILLVENDORNAME</path>
                        </field>
                        <field readonly="true" hidden="true">
                            <path>DOCNUMBER</path>
                        </field>
                        <field fullname="IA.VENDOR_ID" hidden="true">
                            <path>BILLVENDORID</path>
                        </field>
                        <field fullname="IA.BILL_DATE" readonly="true">
                            <path>BILLWHENCREATED</path>
                            <type assoc="T">
                                <type>date</type>
                                <ptype>date</ptype>
                            </type>
                        </field>
                        <field fullname="IA.DUE_DATE" readonly="true">
                            <path>BILLWHENDUE</path>
                            <type assoc="T">
                                <type>date</type>
                                <ptype>date</ptype>
                            </type>
                        </field>
                        <field fullname="IA.CREDITS_AVAILABLE" readonly="true" formatZero="true">
                            <path>BILLCREDITSAVAILABLE</path>
                            <type assoc="T">
                                <ptype>decimal</ptype>
                                <type>currency</type>
                            </type>
                            <default>0</default>
                        </field>
                        <field fullname="IA.DISCOUNT_AVAILABLE" readonly="true" formatZero="true">
                            <path>BILLDISCOUNTAVAILABLE</path>
                            <type assoc="T">
                                <ptype>decimal</ptype>
                                <type>currency</type>
                            </type>
                            <default>0</default>
                        </field>
                        <field fullname="IA.AMOUNT_DUE" readonly="true">
                            <path>BILLTRX_TOTALDUE</path>
                            <type assoc="T">
                                <ptype>decimal</ptype>
                                <type>currency</type>
                            </type>
                            <default>0</default>
                        </field>
                    </section>
                    <grid path="BILLITEMS_SERVERSIDE" noDragDrop="true" className="columns3Grid" hasFixedNumOfRows="true"
                          noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no"  shadeToColumnFiltering = "true">
                        <column>
                            <field hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <path>RECORDKEY</path>
                            </field>
                        </column>
                        <column>
                            <field readonly='true'>
                                <path>ACCOUNTTITLE</path>
                                <fullname>IA.ACCOUNT</fullname>
                                <fieldentity>payableitem</fieldentity>
                                <type assoc="T">
                                    <ptype>text</ptype>
                                    <type>text</type>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field readonly='true'>
                                <path>DEPARTMENTNAME</path>
                                <fullname>IA.DEPARTMENT</fullname>
                                <fieldentity>payableitem</fieldentity>
                                <type assoc="T">
                                    <ptype>text</ptype>
                                    <type>text</type>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field readonly='true'>
                                <path>LOCATIONNAME</path>
                                <fullname>IA.LOCATION</fullname>
                                <fieldentity>payableitem</fieldentity>
                                <type assoc="T">
                                    <ptype>text</ptype>
                                    <type>text</type>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field readonly="true" required="false">
                                <path>CURRENCY</path>
                                <fullname>IA.CURRENCY</fullname>
                                <fieldentity>payableitem</fieldentity>
                                <type assoc="T">
                                    <ptype>text</ptype>
                                    <type>text</type>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field readonly='true' hasTotal="true">
                                <path>TRX_TOTALDUE</path>
                                <fullname>IA.AMOUNT_DUE</fullname>
                                <fieldentity>payableitem</fieldentity>
                                <type assoc="T">
                                    <ptype>decimal</ptype>
                                    <type>currency</type>
                                </type>
                                <default>0</default>
                            </field>
                        </column>
                        <column>
                            <field readonly="true" formatZero="true">
                                <path>CREDITSAVAILABLE</path>
                                <fullname>IA.CREDITS_AVAILABLE</fullname>
                                <fieldentity>payable</fieldentity>
                                <type assoc="T">
                                    <ptype>decimal</ptype>
                                    <type>currency</type>
                                </type>
                                <default>0</default>
                            </field>
                        </column>
                        <column>
                            <field formatZero="true" hasTotal="true">
                                <path>CREDITSAPPLIED</path>
                                <fullname>IA.CREDITS_TO_APPLY</fullname>
                                <fieldentity>payableitem</fieldentity>
                                <type assoc="T">
                                    <ptype>decimal</ptype>
                                    <type>currency</type>
                                </type>
                                <default>0</default>
                                <events>
                                    <change>creditHandler.applyCreditFromLinePage(this.meta);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field>
                                <path>CREDITDETAILS</path>
                                <type type='text' ptype='href'></type>
                                <hreftxt>IA.CREDIT_DETAILS</hreftxt>
                                <events>
                                    <click>PaybillsServerSideCommon.openFloatingPage('CREDITS_SERVERSIDE_PAGE', this.meta, true);</click>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field readonly="true" formatZero="true" hasTotal="true">
                                <path>DISCOUNTAPPLIED</path>
                                <fullname>IA.DISCOUNTS</fullname>
                                <type assoc="T">
                                    <ptype>decimal</ptype>
                                    <type>currency</type>
                                </type>
                                <default>0</default>
                            </field>
                        </column>
                        <column>
                            <field hasTotal="true" className="qx-grid-total">
                                <path>PAYMENTAMOUNT</path>
                                <fullname>IA.AMOUNT_TO_PAY</fullname>
                                <fieldentity>payableitem</fieldentity>
                                <type assoc="T">
                                    <ptype>decimal</ptype>
                                    <type>currency</type>
                                </type>
                                <default>0</default>
                                <events>
                                    <change>commonHandler.manualEnterAmtToPay(this.meta, true);</change>
                                </events>
                            </field>
                        </column>
                    </grid>
                </page>
            </pages>
            <footer>
                <button className="right" id="blSaveButton">
                    <name>IA.SAVE</name>
                    <events>
                        <click>PaybillsServerSideCommon.closeFloatingPage('BILLLINEDETAILS_SERVERSIDE_PAGE', true);</click>
                    </events>
                </button>
                <button className="right" id="blCancelButton">
                    <name>IA.CANCEL</name>
                    <events>
                        <click>PaybillsServerSideCommon.closeFloatingPage('BILLLINEDETAILS_SERVERSIDE_PAGE', false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
        <floatingPage modal="true" compact="true">
            <events>
                <close>closeFloatingPage('ESTIMATEDELIVERYPAGE', false)</close>
            </events>
            <title>IA.ESTIMATE_DELIVERY_DATE_FOR_CHECK_DELI</title>
            <id>ESTIMATEDELIVERYPAGE</id>
            <path>ESTIMATEDELIVERYPAGE</path>
            <pages>
                <page>
                    <flexstack>
                        <!-- provide page padding -->
                        <flexsection>
                            <flexstack>
                                <flexrow>
                                    <field fullname="IA.FINAL_APPROVAL_DATE">
                                        <path>FINALAPPROVALDATE_D</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                        <events>
                                            <change>loadEstimatedPymtDeliveryDate(this.meta);</change>
                                        </events>
                                    </field>
                                </flexrow>
                                <flexrow>
                                    <field fullname="IA.ESTIMATED_PAYMENT_DELIVERY_DATE">
                                        <path>ESTIMATEDPYMTDELDATE_D</path>
                                        <type>
                                            <type>date</type>
                                            <ptype>date</ptype>
                                            <maxlength>12</maxlength>
                                            <size>12</size>
                                            <format>/^[0-9\.\s\-\/]{1,10}$/</format>
                                        </type>
                                        <readonly>1</readonly>
                                    </field>
                                </flexrow>
                            </flexstack>
                        </flexsection>
                    </flexstack>
                </page>
            </pages>
            <footer>
                <button>
                    <name>IA.CLOSE</name>
                    <events>
                        <click>closeFloatingPage('ESTIMATEDELIVERYPAGE', false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
        <!-- Floating page that opens maximized. The footer buttons are placed in a separate non-scrolling
        footer. -->
        <!-- floating page for the advanced filter view -->
        <xi:include href="appymt_advanced_filter.xml" xmlns:xi="http://www.w3.org/2003/XInclude" />

        <!-- Bill line details floating page -->
        <xi:include href="appymt_billlinedetailspage.xml" xmlns:xi="http://www.w3.org/2003/XInclude" />
        <!-- Joint line details floating page -->
        <xi:include href="appymt_jointdetailspage.xml" xmlns:xi="http://www.w3.org/2003/XInclude" />
        <!-- Joint line details floating page -->
        <xi:include href="appymt_jointspage.xml" xmlns:xi="http://www.w3.org/2003/XInclude" />
        <!-- Payments - more info floating page -->
        <floatingPage compact="true" modal="true" fullscreen="true" topbuttons="true" autoHideLoading="false">
            <events>
                <close>closeMoreDetailsPage(false)</close>
            </events>
            <title>IA.ADD_MORE_DETAILS</title>
            <id>MOREDETAILSPAGE</id>
            <path>MOREDETAILSPAGE</path>
            <pages>
                <page>
                    <flexstack>
                        <field fullname="IA.MERGE_FLAG" readonly='true' hidden="true">
                            <path>ISMERGE</path>
                        </field>
                        <field fullname="IA.ITEM_FILTERS" readonly='true' hidden="true">
                            <path>ITEMFILTERS</path>
                        </field>
                        <gridsection>
                            <grid path="MOREDETAILS" noDragDrop="true" className="columns3Grid" hasFixedNumOfRows="true"
                                  noNewRows="true" showDelete="false">
                                <column>
                                    <field fullname="IA.VENDOR_ID" required="false" readonly='true'>
                                        <path>VENDORID</path>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.VENDOR_NAME" readonly='true'>
                                        <path>VENDORNAME</path>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.PAYMENT_DATE" readonly='true'>
                                        <path>PAYMENTDATE</path>
                                        <type assoc="T">
                                            <type>date</type>
                                            <ptype>date</ptype>
                                        </type>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.PAYMENT_CURRENCY" required="false" readonly="true">
                                        <path>PAYMENTCURRENCY</path>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.AMOUNT_SELECTED" readonly="true">
                                        <path>AMOUNTSELECTED</path>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.BASE_CURRENCY" readonly="true">
                                        <path>BASECURRENCY</path>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.MEMO">
                                        <path>DESCRIPTION</path>
                                    </field>
                                </column>
                                <column>
                                    <field fullname="IA.DOC_KEY">
                                        <path>DOCNUMBER</path>
                                    </field>
                                </column>
                                <column>
                                    <field required="true">
                                        <path>AMOUNTTOPAY</path>
                                    </field>
                                </column>
                            </grid>
                        </gridsection>
                    </flexstack>
                </page>
            </pages>
            <footer>
                <button>
                    <name>IA.SAVE</name>
                    <events>
                        <click>closeMoreDetailsPage(true);</click>
                    </events>
                </button>
                <button>
                    <name>IA.CANCEL</name>
                    <events>
                        <click>closeMoreDetailsPage(false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
        <floatingPage move="false" resize="false" modalsize="large" close="true" fixedcenter="true" modal="true">
            <events>
                <close>resetMergePreview();window.editor.hidePage('mergepayrequest', true);</close>
            </events>
            <title>IA.MERGE_PAYMENT_REQUEST</title>
            <id>mergepayrequest</id>
            <path>MERGE_PYMT_PAGE</path>
            <pages>
                <page>
                    <field fullname="" readonly="true" hidden="true" isToken="false">
                        <path>MERGEPYMTINFO</path>
                        <type>
                            <type>text</type>
                            <ptype>text</ptype>
                        </type>
                        <default>IA.THERE_IS_1_PAYMENT_REQUEST_TO_THE_SAME_VENDOR</default>
                    </field>
                    <section id="merge_pymt">
                        <grid noNewRows="true" showDelete="false" noDragDrop="true" hasFixedNumOfRows="true">
                            <path>MERGE_PYMT_ITEMS</path>
                            <column>
                                <field readonly="true" hidden="false" isToken="true">
                                    <path>MERGEWITHREQ</path>
                                    <type type='text' ptype='href'></type>
                                    <events>
                                        <click>mergePymt('mergepayrequest','MERGE', 'select', this.meta);</click>
                                    </events>
                                    <default>IA.MERGE</default>
                                </field>
                            </column>
                            <column>
                                <field readonly="true">
                                    <path>MERGEVENDOR</path>
                                    <type>
                                        <type>text</type>
                                        <ptype>text</ptype>
                                    </type>
                                    <fullname>IA.VENDOR</fullname>
                                </field>
                            </column>
                            <column>
                                <field readonly="true">
                                    <path>MERGEDATE</path>
                                    <type>
                                        <type>date</type>
                                        <ptype>date</ptype>
                                    </type>
                                    <fullname>IA.DATE</fullname>
                                </field>
                            </column>
                            <column>
                                <field readonly="true">
                                    <path>MERGEPYMTMETHOD</path>
                                    <type>
                                        <type>text</type>
                                        <ptype>text</ptype>
                                    </type>
                                    <fullname>IA.PAYMENT_METHOD</fullname>
                                </field>
                            </column>
                            <column>
                                <field readonly="true">
                                    <path>MERGEAMOUNT</path>
                                    <type>
                                        <type>decimal</type>
                                        <ptype>currency</ptype>
                                    </type>
                                    <fullname>IA.AMOUNT</fullname>
                                </field>
                            </column>
                            <column>
                                <field readonly="true">
                                    <path>MERGECREATEDBY</path>
                                    <type>
                                        <type>text</type>
                                        <ptype>text</ptype>
                                    </type>
                                    <fullname>IA.CREATED_BY</fullname>
                                </field>
                            </column>
                        </grid>
                    </section>
                    <section id="merge_review">
                        <field fullname="IA.IN_THE_FUTURE_WHEN_THERE_IS_ONE_MATCH" hidden="true">
                            <path>MERGEPYMTREVIEW</path>
                            <type>
                                <type>boolean</type>
                            </type>
                        </field>
                    </section>
                </page>
            </pages>
            <footer>
                <button id="MERGEBTN">
                    <name>IA.MERGE</name>
                    <events>
                        <click>mergePymt('mergepayrequest','MERGE', 'noselect', this.meta);</click>
                    </events>
                </button>
                <button id="DONTMERGEBTN">
                    <name>IA.DONT_MERGE</name>
                    <events>
                        <click>mergePymt('mergepayrequest','DONTMERGE', 'noselect', this.meta);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
    </view>
</ROOT>
