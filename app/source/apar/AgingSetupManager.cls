<?php
/**
 *    FILE:            AgingSetupManager.cls
 *    AUTHOR:          <PERSON><PERSON><PERSON> <<EMAIL>>
 *    DESCRIPTION:     Sample Owned Object for modulepref enhancements
 *
 *    (C) 2023, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class AgingSetupManager extends EntityManager
{
    /**
     *
     * @param array $values
     *
     * @return bool
     *
     */
    protected function regularAdd(&$values)
    {
        $ok = true;
        $values['MODULEKEY'] = Globals::$g->kARid;
        $ok = $ok && parent::regularAdd($values);
        return $ok;
    }

    /**
     *
     * @return array
     *
     */
    public static function getAgingPeriods()
    {
        $mgr = Globals::$g->gManagerFactory->getManager('agingsetup');
        $query = ['selects' => ['RECORDNO']];
        $aging = $mgr->GetList($query);
        if(empty($aging)){
            return [];
        }

        $mgr = Globals::$g->gManagerFactory->getManager('agingentry');
        $query = [ 'selects' => ['LISTAGG( MINRANGE || \'-\' || MAXRANGE, \',\') as AGINGPERIODS'],'filters' => [[['RECORDKEY', 'IN', array_column($aging, 'RECORDNO')]]] , 'groupby' => ['RECORDKEY']];
        $agingData = $mgr->GetList($query);

        if(!empty($agingData)){
            return array_column($agingData, 'AGINGPERIODS');
        }else{
            return [];
        }

    }
}