<?
/**
 *    FILE:          apamortizationscheduleentry.ent
 *    AUTHOR:        Shone
 *    DESCRIPTION:
 *
 *    (C) 2024, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

$kSchemas['apamortizationscheduleentry'] = [
    'children'        => [
        'account'                => [
            'fkey'    => 'accountkey',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'baseaccountmst',
        ],
        'offsetaccount'          => [
            'fkey'    => 'offsetaccountkey',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'baseaccountmst',
        ],
        'apamortizationschedule' => [
            'fkey'    => 'schedulekey',
            'invfkey' => 'record#',
            'table'   => 'amortizationschedule',
            'children' => [
                                'prentry' => [
                                    'fkey'    => 'prentrykey',
                                    'invfkey' => 'record#',
                                    'join'    => 'outer',
                                    'table'   => 'prentrymst',
                                ],
                                'journal'       => [
                                    'fkey'    => 'journalkey',
                                    'invfkey' => 'record#',
                                    'table'   => 'basejournalmst',
                                ],
                            ],
        ],
        'glbatch'                => [
            'fkey'    => 'glbatchkey',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'glbatchmst',
        ],
    ],
    'object'          => [
        'RECORDNO',
        'SCHEDULEKEY',
        'PRENTRYKEY',
        'LOCATION#',
        'DEPT#',
        'CURRENCY',
        'BASECURR',
        'EXCH_RATE_DATE',
        'EXCHANGE_RATE',
        'EXCH_RATE_TYPE_ID',
        'JOURNALKEY',
        'GLBATCHKEY',
        'SCHOPKEY',
        'JOURNALID',
        'ACCOUNTID',
        'OFFSETACCOUNTID',
        'ACCOUNTKEY',
        'OFFSETACCOUNTKEY',
        'POSTINGDATE',
        'POSTINGAMOUNT',
        'SCHEDULEENTRYSTATE',
        'MODULEKEY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'schema'          => [
        'RECORDNO'           => 'record#',
        'SCHEDULEKEY'        => 'schedulekey',
        'PRENTRYKEY'         => 'prentry.record#',
        'LOCATION#'          => 'prentry.location#',
        'DEPT#'              => 'prentry.dept#',
        'CURRENCY'           => 'prentry.currency',
        'BASECURR'           => 'prentry.basecurr',
        'EXCHANGE_RATE'      => 'prentry.exchange_rate',
        'EXCH_RATE_DATE'     => 'prentry.exch_rate_date',
        'EXCH_RATE_TYPE_ID'  => 'prentry.exch_rate_type_id',
        'JOURNALKEY'         => 'journal.record#',
        'JOURNALID'          => 'journal.symbol',
        'GLBATCHKEY'         => 'glbatchkey',
        'SCHOPKEY'           => 'schopkey',
        'ACCOUNTID'          => 'account.acct_no',
        'ACCOUNTKEY'         => 'accountkey',
        'OFFSETACCOUNTKEY'   => 'offsetaccountkey',
        'OFFSETACCOUNTID'    => 'offsetaccount.acct_no',
        'POSTINGDATE'        => 'postingdate',
        'POSTINGAMOUNT'      => 'postingamount',
        'SCHEDULEENTRYSTATE' => 'state',
        'MODULEKEY'          => 'apamortizationschedule.modulekey',
        'WHENCREATED'        => 'whencreated',
        'WHENMODIFIED'       => 'whenmodified',
        'CREATEDBY'          => 'createdby',
        'MODIFIEDBY'         => 'modifiedby',
        'LINE_NO'            => 'line_no',
        'TR_TYPE'            => 'tr_type',
    ],
    'publish'         => [
        'RECORDNO',
        'SCHEDULEKEY',
        'PRENTRYKEY',
        'JOURNALID',
        'ACCOUNTID',
        'OFFSETACCOUNTID',
        'POSTINGDATE',
        'POSTINGAMOUNT',
        'SCHEDULEENTRYSTATE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'sqldomarkup'     => true,
    'sqlmarkupfields' => [
        'WHENCREATED',
        'WHENMODIFIED',
    ],
    'fieldinfo'       => [
        [
            'path'     => 'RECORDNO',
            'desc'     => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden'   => true,
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
            'id'       => 1,
        ],
        [
            'path'     => 'SCHEDULEKEY',
            'fullname' => 'IA.SCHEDULEKEY',
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
            'required' => true,
            'desc'     => 'IA.SCHEDULEKEY',
            'id'       => 2,
        ],
        [
            'path'     => 'ACCOUNTID',
            'fullname' => 'IA.EXPENSE_ACCOUNT',
            'type'     => [
                'type'       => 'text',
                'ptype'      => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'     => 'glaccount',
            ],
            'id'       => 4,
        ],
        [
            'path'     => 'OFFSETACCOUNTID',
            'fullname' => 'IA.AMORTIZATION_ACCOUNT',
            'type'     => [
                'type'       => 'text',
                'ptype'      => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'     => 'glaccount',
            ],
            'id'       => 5,
        ],
        [
            'fullname' => 'IA.AMOUNT',
            'type'     => [
                'ptype'     => 'currency',
                'type'      => 'decimal',
                'maxlength' => 18,
                'size'      => 10,
                'format'    => $gCurrencyFormat,
            ],
            'desc'     => 'IA.AMOUNT',
            'path'     => 'POSTINGAMOUNT',
            'readonly' => true,
            'id'       => 7,
        ],
        [
            'fullname' => 'IA.POSTING_DATE',
            'type'     => $gDateType,
            'path'     => 'POSTINGDATE',
            'id'       => 8,
        ],
        [
            'path'     => 'GLBATCHKEY',
            'fullname' => 'IA.GL_BATCH',
            'type'     => [
                'type'   => 'text',
                'ptype'  => 'href',
                'entity' => 'glbatch',
            ],
            'id'       => 9,
        ],
        [
            'path' => 'SCHOPKEY',
            'type'     => [
                'type'      => 'integer',
            ],
            'hidden' => true,
            'id' => 10,
        ],
        [
            'fullname' => 'IA.SCHEDULE_ENTRY_STATE',
            'type'     => [
                'ptype'         => 'enum',
                'type'          => 'text',
                'validlabels'   => [ 'IA.OPEN', 'IA.POSTED', 'IA.ON_HOLD', 'IA.TERMINATED', 'IA.ERROR', 'IA.HISTORICAL' ],
                'validvalues'   => [ 'Open', 'Posted', 'On hold', 'Terminated', 'Error', 'Historical' ],
                '_validivalues' => [ 'O', 'P', 'H', 'T', 'E', 'L' ],
            ],
            'default'  => 'Open',
            'required' => false,
            'readonly' => true,
            'desc'     => 'IA.SCHEDULE_ENTRY_STATE',
            'path'     => 'SCHEDULEENTRYSTATE',
            'id'       => 11,
        ],
        [
            'path'     => 'MODULEKEY',
            'fullname' => 'IA.MODULE_KEY',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 20,
            ],
            'readonly' => true,
            'hidden'   => true,
            'id'       => 12,
        ],
        [
            'path' => 'SCHEDULEENTRYCHECKBOX',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 13,
        ],
        [
            'path' => 'ERRORDESC',
            'fullname' => 'Error',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 200,
            ],
            'id' => 14,
        ],
        [
            'path' => 'LINE_NO',
            'fullname' => 'IA.LINE_NO',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 10,
                'size' => 10,
            ),
            'readonly' => true,
            'id' => 15,
        ],
        [
            'path' => 'TR_TYPE',
            'fullname' => 'IA.TR_TYPE',
            'desc' => 'IA.TR_TYPE',
            'hidden' => true,
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 1,
            ],
            'id' => 16,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'api'             => [
        'PERMISSION_READ'   => 'lists/apamortizationscheduleline/view',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ],
    'table'           => 'amortizationscheduleentry',
    'dbsorts'         => [ [ 'apamortizationscheduleentry.RECORD#' ] ],
    'dbfilters'       => [ [ 'apamortizationschedule.modulekey', '=', '3.AP' ], ],
    'parententity'    => 'apamortizationschedule',
    'module'          => 'ap',
    'vid'             => 'RECORDNO',
    'autoincrement'   => 'RECORDNO',
    'auditcolumns'    => true,
    'nochatter'       => true,
    'printas'         => 'IA.AMORTIZATION_TEMPLATE',
    'pluralprintas'   => 'IA.AMORTIZATION_TEMPLATE',
    'description'     => 'IA.AMORTIZATION_TEMPLATE',
];
