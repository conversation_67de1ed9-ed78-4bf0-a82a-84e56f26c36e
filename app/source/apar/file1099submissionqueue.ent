<?php
//=============================================================================
//
//	FILE:			 file1099submissionqueue.ent
//	<AUTHOR> C
//	DESCRIPTION:	 file1099submissionqueue entity file
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

require 'fifodispatcherqueue.ent';
$kSchemas['file1099submissionqueue'] = $kSchemas['fifodispatcherqueue'];
$kSchemas['file1099submissionqueue']['ownedobjects'] =  [
    [
        'fkey' => File1099SubmissionQueueHistoryManager::DISPATCHERQUEUEKEY,
        'invfkey' => 'RECORDNO',
        'entity' => 'file1099submissionqueuehistory',
        'path' => 'FILE1099SUBMISSION'
    ]
];
