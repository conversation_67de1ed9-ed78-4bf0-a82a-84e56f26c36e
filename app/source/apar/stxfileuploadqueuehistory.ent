<?php
//=============================================================================
//
//	FILE:			 stxfileuploadqueuehistory.ent
//	<AUTHOR> D<PERSON>ale <<EMAIL>>
//	DESCRIPTION:	 stxfileuploadqueuehistory entity file
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

require 'fifodispatcherhistory.ent';
$kSchemas['stxfileuploadqueuehistory'] = $kSchemas['fifodispatcherhistory'];
$kSchemas['stxfileuploadqueuehistory']['parententity'] = 'stxfileuploadqueue';
$kSchemas['stxfileuploadqueuehistory']['table'] = 'fifodispatcherhistory';
