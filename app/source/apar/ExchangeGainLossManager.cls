<?php

/**
 * Manager class for the Gain and Loss
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for the AP/AR Exchange gain and loss Payments
 */
class ExchangeGainLossManager extends BasePymtManager
{
    // Constants
    const DEFAULT_EXCH_RATE_TYPE = 'Intacct Daily Rate';
    const CREDIT = -1;
    /**
     * @var array $exchangeGLMap
     */
    private $exchangeGLMap;

    /**
     * @return array
     */
    public function getExchangeGLMap() {
        return $this->exchangeGLMap;
    }

    /**
     * @param array $exchangeGLMap
     */
    public function setExchangeGLMap($exchangeGLMap) {
        $this->exchangeGLMap = $exchangeGLMap;
    }
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Get the list of valid payment methods
     *
     * @param string|null $paymentProvider
     * @param string|null $providerPayMethod
     *
     * @return array a list of valid payment method
     */
    public function getValidPaymentMethod(string $paymentProvider = null, string $providerPayMethod = null)
    {
        $payMethod = [
            BasePymtManager::CHECK_PAYMENTMETHOD,
            BasePymtManager::EFT_PAYMENTMETHOD,
            BasePymtManager::CASH_PAYMENTMETHOD,
            BasePymtManager::ACH_PAYMENTMETHOD,
            APQuickPayManager::MANUAL_PAYMENT,
        ];

        return $payMethod;
    }

    /**
     * Add a new payment record
     *
     * @param array &$values the payment request data
     *
     * @return bool false if error else true
     */
    protected function regularAdd(&$values)
    {
        return parent::regularAdd($values);
    }

    /**
     * Execute Some of the payment related activities specific to various payment method eg: Amex, WF payments
     *
     * @param array $values the payment details data
     *
     * @return bool is this action is success or failure
     */
    protected function onAfterAdd(&$values)
    {
        return true;
    }


    /**
     * Return the offset account key for the transaction
     * Each subclass needs to define from where we will pick this offset from
     *
     * @param array $values the transaction data
     *
     * @return string|null
     */

    public function getOffsetAccountKey($values)
    {
        $cny = GetMyCompany();

        $trx_recordno = $values['ITEMS'][0]['__trx_recordno'];
        $trxline_recordno = $values['ITEMS'][0]['__trxline_recordno'];
        $paymentrecordno = $values['ITEMS'][0]['__paymentrecordno'];

        $offsetAccountkey = $this->exchangeGLMap[$trx_recordno][$trxline_recordno]['ACCOUNTKEY'];

        if (!isset($offsetAccountkey) && $offsetAccountkey == '') {
            $offsetAccountNo = $this->exchangeGLMap[$trxline_recordno][$trx_recordno][$paymentrecordno]['ACCOUNT'];
            $qry = "select record# from glaccount where 
                    cny# = :1 and acct_no = :2 ";
            $acctid = QueryResult(array($qry, $cny, $offsetAccountNo));
            $offsetAccountkey = $acctid ? $acctid[0]['RECORD#'] : null;
        }
        return $offsetAccountkey;
    }

    /**
     * Initialize the class vaiables and data for update
     *
     * @param array &$values the transaction data
     *
     * @return bool false if error else true
     */
    protected function initialize(&$values)
    {
        return true;
    }

    /**
     * Get the Bank Location, in case of exchange gain and loss, it should return ''
     * @return string
     */
    protected function getBankLocation() {
        return '';
    }


    /**
     * @return string
     */
    protected function getDefaultStateForSubmitAction()
    {
        return '';
    }

    /**
     * Translate the entry department
     *
     * @param array &$values the transaction data
     * @param array &$entry the line data
     * @param int $index the line index
     *
     * @return bool false if error else true
     */
    protected function translateDepartment(&$values, &$entry, $index)
    {
        $ok = true;
        if (isset($entry['DEPT#']) && $entry['DEPT#'] != '') {
            return $ok;
        }
        return parent::translateDepartment($values, $entry, $index);
    }

    /**
     * Translate the entry location
     *
     * @param array &$values the transaction data
     * @param array &$entry the line data
     * @param int $index the line index
     *
     * @return bool false if error else true
     */
    protected function translateLocation(&$values, &$entry, $index)
    {
        $ok = true;
        if (isset($entry['LOCATION#']) && $entry['LOCATION#'] != '') {
            $entry['BASELOCATION'] = $values['BASELOCATION'] ?: $entry['LOCATION#'];
            return $ok;
        }
        return parent::translateLocation($values, $entry, $index);
    }

    /**
     * @param string $baselocationKey
     * @param string $itemLocation
     * @param array $values
     *
     * @return string
     */
    protected function getBaseLocation($baselocationKey, $itemLocation, $values)
    {
        return $baselocationKey ?: $itemLocation;
    }

    /**
     * Validate the payment method
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function validatePaymentMethod($values)
    {
        return true;
    }

    /**
     * Validate the bank account
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function validateBankAccount($values)
    {
        return true;
    }

    /**
     * @param array     $values
     *
     * @return bool
     */
    protected function translatePaymentMethod(&$values) {
        $ok = true;

        if(isset($values['PAYMENTMETHOD']) && $values['PAYMENTMETHOD'] != '') {
            $ok = parent::translatePaymentMethod($values);
        }
        return $ok;
    }

}