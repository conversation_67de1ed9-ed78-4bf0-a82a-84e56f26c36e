<?
require '1099.rpt';

$kSchemas["1099file"]=$kSchemas["1099"];
$kSchemas["1099file"]['printas']='IA.EXPORT_1099_FILE';
$kSchemas["1099file"]['controls']= array(
                                          kShowBackground,
                                          kShowCSV,
                                          kShowExcel,
                                          kXMLExport,
                                          kXSDExport,
                                         );
$kSchemas["1099file"]['xsl_file']='1099file';
$kSchemas["1099file"]['helpfile']='Exporting_1099_Data';
$kSchemas["1099file"]['fieldinfo']['lines'][2]['fields'][0]['onchange']='';
unset($kSchemas["1099file"]['fieldinfo']['lines'][2]['fields'][2]);
unset($kSchemas["1099file"]['fieldinfo']['lines'][3]);
unset($kSchemas["1099file"]['fieldinfo']['lines'][4]);
unset($kSchemas["1099file"]['fieldinfo']['lines'][2]['fields'][3]);

