<?php

/**
 * =============================================================================
 * FILE:        File1099FormEditor.cls
 *
 * <AUTHOR> DESCRIPTION:    File 1099 form editor
 *
 * (C)2022 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 * =============================================================================
 */

/**
 * Editor class for File1099 object
 */
class File1099FormEditor extends FormEditor
{

    const VIEW_FILE1099 = 'file1099view';
    const VIEW_REDIRECT = 'viewredirect';
    const VIEW_REDIRECT_DONE = 'viewredirectdone';
    /**
     * @var string $kShowRedirectState
     */
    var $kShowRedirectState = 'showredirect';

    /**
     * @var string $kShowDoneState
     */
    var $kShowDoneState = 'showdone';

    protected $additionalTokens = [
        'IA.E_FILE',
    ];

    /**
     * I18N. Collection of tokens used primarily in JavaScript.
     *
     * @return string[]
     */
    protected function getFormTokens() : array
    {
        $this->textTokens[] = "IA.ALLOW_BROWSER_POPUPS";
        $this->textTokens[] = "IA.SOME_ERROR_PLEASE_RETRY_AFTER_SOME_TIME";
        $this->textTokens[] = "IA.WAIT_AND_SUBMIT_BATCH";
        $this->textTokens[] = "IA.ENTITY_GROUPS_ARE_NOT_SUPPORTED_FOR_DOWNLOAD_FI";
        return parent::getFormTokens();
    }

    /**
     * @param array $_params Initial params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
        $this->kActionHandlers[self::VIEW_REDIRECT] = [
            'handler' => 'ProcessViewAction',
            'states' => [
                $this->kShowRedirectState,
                $this->kShowNewState,
            ]
        ];
        $this->kActionHandlers[self::VIEW_REDIRECT_DONE] = [
            'handler' => 'ProcessViewAction',
            'states' => [
                $this->kShowDoneState,
                $this->kShowNewState,
            ]
        ];
    }

    /**
     * @param string $state
     *
     * @return array
     */
    public function getStandardButtons($state)
    {
        $buttons = [];
        if (File1099Manager::validateSageCloudSubscription()) {
            $this->setButtonDetails($buttons, $this->kShowRedirectState, 'efilebtn', GT($this->textMap,'IA.E_FILE'),
                'file', false, "vendorGridHandler.loadVendorPage();", false);
            $this->setButtonDetails($buttons, $this->kShowRedirectState, 'redirectbtn', 'IA.CANCEL',
                self::VIEW_REDIRECT, false);
            $this->setButtonDetails($buttons, $this->kShowDoneState, 'donebtn', 'IA.DONE',
                self::VIEW_REDIRECT_DONE, false, '', true, false, '.fs=1');
        }
        return $buttons;
    }

    /**
     * @param array $buttons
     * @param array $buttonsProperty
     */
    protected function createSplitButtonEntry(&$buttons, $buttonsProperty)
    {
        $actions = array();
        foreach ($buttonsProperty as $buttonkey => $buttonproperty) {
            //$action = array();
            $jsCode = $buttonproperty['jsCode'] ?? '';
            $action = $this->createAction(
                $buttonproperty['id'], 'export' . $buttonproperty['id'], $buttonkey,
                $buttonproperty['action'], $buttonproperty['submit'], $jsCode, $buttonproperty['serverAction']
            );

            if (isset($buttonproperty['isDefault']) && $buttonproperty['isDefault'] == true) {
                $action['default'] = true;
            }
            $actions[] = $action;
        }
        $this->createSplitButton($buttons, $actions);
    }

    /**
     * Returns the reconciliation request.
     *
     * @return array
     */
    public function getSubmissionLogGridRequest()
    {
        $request = [];
        $request['EXTERNALSUBMISSIONID'] = Request::$r->batchid;
        $request['WHENSUBMITTED'] = Request::$r->date;
        $request['LOCATION'] = Request::$r->location;
        $request['CREATEDBY'] = Request::$r->createdby;
        $request['FORMTYPE'] = Request::$r->formtype;
        $request['NOOFRECORDS'] = Request::$r->noofrecords;
        $request['STATUS'] = Request::$r->status;
        $request['PAGE'] = Request::$r->page;
        $request['PAGESIZE'] = Request::$r->pagesize;
        $request['QUERYID'] = Request::$r->queryid;
        $request['ORDERS'] = Request::$r->orders;
        $request['TAXYEAR'] = Request::$r->taxyear;

        return $request;
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    protected function ProcessViewAction(&$_params)
    {
        $action = Request::$r->_action;
        if (in_array($action, [self::VIEW_REDIRECT, self::VIEW_REDIRECT_DONE])) {
            $showMessage = ($action === self::VIEW_REDIRECT_DONE);
            $this->redirectToBatchSummary($showMessage);
            return true;
        }
        return parent::ProcessViewAction($_params);
    }

    /**
     * Redirect to summary page of file submission
     * @param array $params
     * @param bool $showMessage
     *
     * @return bool
     */
    private function redirectToBatchSummary($showMessage)
    {
        $_sess = Session::getKey();
        $opKey = GetOperationId('ap/lists/file1099submissionlog/create');
        $url = 'editor.phtml?.sess=' . $_sess . '&.op=' . $opKey;
        if ($showMessage) {
            $url .= '&.fs=1';
        }
        Redirect($url);
        return true;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $view = $this->getView();
        $yearEndingField = [];

        // YEAR ENDING
        $yearValidLabels = [];
        $yearValidValues = [];
        $view->findComponents(['path' => 'YEARENDING'], EditorComponentFactory::TYPE_FIELD, $yearEndingField);
        if ($yearEndingField && $yearEndingField[0]) {
            $monthYear = explode('-', date('m-Y'));
            $currentMonth = $monthYear[0];
            $currentYear = $monthYear[1];
            for ($i = $currentYear - 1; $i <= $currentYear; $i++) {
                $yearValidLabels[] = $i;
                $yearValidValues[] = $i;
            }
            $yearEndingField[0]->setProperty(['type', 'validlabels'], $yearValidLabels);
            $yearEndingField[0]->setProperty(['type', 'validvalues'], $yearValidValues);
            $defaultYear = $yearValidValues[0];
            if(in_array($currentMonth, [11,12])) {
                $defaultYear = $yearValidValues[1];
            }
            $obj['YEARENDING'] = (string)$defaultYear;
        }

        $showEntity = false;
        if (IsMultiEntityCompany() && GetContextLocation() == '') {
            global $kMEid;
            GetModulePreferences($kMEid, $preferences);
            $_form1099 = $preferences['FORM1099'];
            if ($_form1099 == 'true') {
                $showEntity = true;
            }
        }

        // LOCATION / LOCATION GROUP
        $locationGroup = [];
        $view->findComponents(['path' => 'LOCATIONGROUP'], EditorComponentFactory::TYPE_FIELD, $locationGroup);

        if ($locationGroup && $locationGroup[0]) {
            $locationGroup[0]->setProperty('hidden', !$showEntity);
        }

        // FORMS TO PRINT
        $forms = [];
        $view->findComponents(['path' => 'FORMTYPE'], EditorComponentFactory::TYPE_FIELD, $forms);
        if ($forms && $forms[0]) {

            $form1099types = GetForm1099Types();
            array_shift($form1099types);
            $validValues = [];
            $validLables = [];
            foreach ($form1099types as $key => $value) {
                $validValues[] = $key;
                $validLables[] = $value;
            }

            $forms[0]->setProperty(['type', 'validlabels'], $validLables);
            $forms[0]->setProperty(['type', 'validvalues'], $validValues);
            $obj['FORMTYPE'] = $validValues[0];
        }

        parent::mediateDataAndMetadata($obj);
    }

    /**
     * @return string[]
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/file1099.js';
        $jsFiles[] = '../resources/js/file1099submissionloghandler.js';
        return $jsFiles;
    }

    /**
     * Delivers the report view in CSV format
     *
     * @param array $_params the metadata
     *
     * @return bool false if error else true
     */
    protected function Editor_Export(&$params)
    {
        $ok = $this->retrieveObjectFromView($params, $obj);
        if ($ok) {
            $obj['FORMAT'] = kShowExcel;
            $obj['report'] = '1099file';
            $this->getEntityMgr()->getLocationList($obj);
            if ($obj['ISGROUP']) {
                $ok = false;
                $this->state = $this->kErrorState;
                $msg = "Entity groups are not supported for download file. Select an entity and try again.";
                Globals::$g->gErr->addError('AP-0511', __FILE__ . ':' . __LINE__, $msg, '', $msg);
            }
            if ($ok) {
                $data = $this->getEntityMgr()->get1099Details($obj);
                switch ($obj['FORMAT']) {
                    case kShowCSV:
                        $this->exportToCSV($data, $obj);
                        break;
                    case kShowExcel:
                        $this->exportToExcel($data, $obj);
                        break;
                    default:
                        break;
                }
            }
        }
        return $ok;
    }

    /**
     * @return File1099Manager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof File1099Manager);
        return $this->entityMgr;
    }

    /**
     * exportToCSV - exports/echos the given audit trail in CSV format.
     * @param array $data
     * @param array $params
     */
    private function exportToCSV($data, $params)
    {
        $form1099helper = new Form1099Helper();
        $rowData = [];

        // Open php output buffer
        $outputBuffer = fopen("php://output", 'w');
        $filename = self::getExportFilename($params['YEARENDING'], $params['FORMTYPE']);
        // Set CSV file header
        header("Content-type: application/csv");
        header("Content-Disposition: filename=" . $filename . ".csv");

        // Output the header
        $headers = $form1099helper->getColumnHeadings($params['FORMTYPE'], $params['FORMAT'], $params['YEARENDING']);
        fputcsv($outputBuffer, $headers);

        // Output the transactions
        foreach ($data as $vendor) {
            foreach ($vendor as $key => $value) {
                foreach ($headers as $header) {
                    if ($header == $key) {
                        $rowData[$header] = $value;
                    }
                }
            }
            $sorted = [];
            foreach ($headers as $header) {
                if (!array_key_exists($header, $rowData)) {
                    $sorted[$header] = null;
                } else {
                    $sorted[$header] = $rowData[$header];
                }
            }

            fputcsv($outputBuffer, $sorted);
        }

        fclose($outputBuffer);

        return true;

    }

    /**
     * getExportFilename - gets the export filename (without extension) for this audit trail/key.
     *
     * @param string $yearEnding
     * @param string $formType
     *
     * @return string Safe filename.
     */
    public static function getExportFilename($yearEnding, $formType)
    {
        return filename_safe($yearEnding . "_1099" . $formType);
    }

    /**
     * exportToExcel - exports/echos the given audit trail in Excel format.
     *
     * @param array $data
     * @param array $params
     */
    private function exportToExcel($data, $params)
    {
        $form1099helper = new Form1099Helper();

        // Open php output buffer
        $outputBuffer = fopen("php://output", 'w');
        $filename = self::getExportFilename($params['YEARENDING'], $params['FORMTYPE']);
        // Set CSV file header
        header("Content-type: application/vnd.ms-excel");
        header("Content-Disposition: attachment; filename=" . $filename . ".xls");

        // Output the header
        $headers = $form1099helper->getColumnHeadings($params['FORMTYPE'], $params['FORMAT'], $params['YEARENDING']);
        fputcsv($outputBuffer, $headers, "\t");

        // Output the transactions
        foreach ($data as $vendor) {
            $rowData = [];
            foreach ($vendor as $key => $value) {
                foreach ($headers as $header) {
                    if (isl_strtolower($header) == isl_strtolower($key)) {
                        $rowData[$header] = $value;
                    }
                }
            }
            $sorted = [];
            foreach ($headers as $header) {
                if (!array_key_exists($header, $rowData)) {
                    $sorted[$header] = null;
                } else {
                    $sorted[$header] = $rowData[$header];
                }
            }

            fputcsv($outputBuffer, $sorted, "\t");
        }
        fclose($outputBuffer);

        return true;
    }

    /**
     * Declare global variables to be used in editor
     *
     * @return array $vars all the global variables
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        $vars['FILE1099OPID'] = self::getFile1099OpID();
        return $vars;
    }

    /**
     * @return int
     */
    public static function getFile1099OpID(): int
    {
        return GetOperationId('ap/reports/file1099');
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;

        switch ($cmd) {
            case 'loadVendorList':
                $this->ajaxloadVendorList();
                break;
            case 'submitForEFile':
                $this->ajaxSubmitForEfile();
                break;
            case 'loadPartnerPageURL':
                $this->ajaxLoadPartnerPageURL();
                break;
            case 'ajaxInitiateOnlinePollingForLogin':
                $this->ajaxInitiateOnlinePollingForLogin();
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * @param array $request
     * @param bool $pagination
     *
     *
     * @return array|null
     */
    private function ajaxloadVendorList($pagination = true)
    {
        // Get all the set values
        $page = Request::$r->page;
        $pageSize = Request::$r->pagesize;
        $queryId = Request::$r->queryid;
        $filters = json_decode(Request::$r->data, true) ?? [];
        $initialLoad = Request::$r->initialLoad;

        if ($pagination && $page !== -1 && $pageSize !== -1) {
            $start = $page * $pageSize;
            $max = $pageSize;
        }
        $data = [];
        // download file button is hidden based on this flag
        $isGroup = false;
        if (!empty($filters)) {
            $ok = $this->getEntityMgr()->checkIfBoxMapExists($filters['YEARENDING']);
            if ($ok) {
                $rows = $this->getEntityMgr()->get1099Details($filters);
                $numRecords = count($rows) ?? 0;
                if (!empty($rows)) {
                    if ($max && count($rows) > $max) {
                        $rows = array_slice($rows, $start, $max);
                    }
                    $this->formatDataForDisplay($filters, $rows, $data, $initialLoad);
                    $this->getEntityMgr()->getLocationList($filters);
                    $isGroup = $filters['ISGROUP'];
                }
            }
        }
        $errors = $this->fetchErrors();
        $TBEmailID = Form1099Helper::getPartnerEmail();

        // Return the data
        $response = ['PARAMS' => ['QUERYID' => $queryId, 'TOTAL_RECORDS' => $numRecords, 'PAGE' => $page,
            'PAGE_SIZE' => $pageSize], 'RESULT_SET' => $data, 'ERRORS' => $errors, 'ISGROUP' => $isGroup,
            'TBEMAILID' => $TBEmailID];
        echo json_encode($response);
    }

    /**
     * fetch 1099 data
     *
     * @param array $filters
     * @param array $response
     * @param array $data
     * @param bool $initialLoad
     */
    protected function formatDataForDisplay($filters, $response, &$data, $initialLoad)
    {
        if (!empty($response)) {
            if ($initialLoad) {
                $data['HEADERS'] = [];
                $data['HEADERS']['ENTITY'] = ($filters['LOCATION'] ? explode("--", $filters['LOCATION'])[1]
                    : explode("--", $filters['LOCATIONGROUP'])[1]);
                $data['HEADERS']['FORM'] = $filters['FORMTYPE'];
                $data['HEADERS']['YEAR'] = $filters['YEARENDING'];
                $infoText = "</br>";
                $infoText .= "Tax year: " . $data['HEADERS']['YEAR'] . "</br>";
                $infoText .= "Entity: " . $data['HEADERS']['ENTITY'] . "</br>";
                $infoText .= "Form: " . $data['HEADERS']['FORM'] . "</br>";
                $data['HEADERS']['INFOTEXT'] = $infoText;
            }
            foreach ($response as $vendor) {
                $data['VENDORS'][] = (array)$vendor;
            }
        }
    }

    /**
     * Fetch errors to usable array
     *
     * @return array
     */
    private function fetchErrors()
    {
        $co = Globals::$g->gErr->ErrorCount;
        $myerrs = [];

        if (!empty($co)) {
            for ($i = 0; $i < $co; $i++) {
                $myerrs[$i] = Globals::$g->gErr->GetNextError();
            }
        } else {
            $co = Globals::$g->gErr->errors;

            for ($i = 0; $i < count($co); $i++) {
                $myerrs[$i] = $co[$i];
            }
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $myerrs;
    }

    /**
     * fetch 1099 data
     *
     * @return array
     */
    protected function ajaxSubmitForEfile()
    {
        $filters = Request::$r->data ?? [];
        $response = [];
        if ($filters) {
            $filters = json_decode($filters, true);

            if (!empty($filters)) {
                $ok = $this->getEntityMgr()->submitFile($filters, $response);
                if (!$ok && (!HasWarnings() || HasErrors())) {
                    $response['ERRORS'] = $this->fetchErrors();
                }
                if ($ok) {
                    if (!empty($response) && $response['NAVIGATE_TO_URL']) {
                        $response['MESSAGE'] = "Please proceed with login to continue with submission.";
                    }
                }
            }
        }

        echo json_encode($response);
    }

    /**
     * load partner page url
     *
     * @return array
     */
    private function ajaxLoadPartnerPageURL()
    {
        $submissionId = Request::$r->submissionId;
        $finalization = Request::$r->finalization;
        $submissionLogKey = Request::$r->submissionLogKey;
        $forceLogin = Request::$r->forceLogin;
        $response = $this->getEntityMgr()->redirectToPartnerPage($finalization, $forceLogin, $submissionId, $submissionLogKey);
        echo json_encode($response);
    }


    /**
     * initiate file submission>
     *
     * @return array
     */
    private function ajaxInitiateOnlinePollingForLogin()
    {
        $type = Request::$r->type;
        $forceLogin = Request::$r->forceLogin === 'true';
        $values = json_decode(Request::$r->values, true) ?? [];
        $result = $this->getEntityMgr()->initiateOnlinePollingForLogin($type, $values['POLLING_URL'], $forceLogin,
            $values['RECORDNO'], $values['SUBMISSIONID'], $values['ISGROUP']);
        echo json_encode($result);
    }


}


