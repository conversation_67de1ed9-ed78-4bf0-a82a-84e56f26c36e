<?php
/**
 * Migration files for update the Payment ID (RECORDID) for AR Payment / Advance records
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Intacct Corporation All, Rights Reserved
 */

class ARPaymentIDMigration
{
    const PAYMENTID_PROCESS_LOCK_KEY = 'update_payment_id_for';
    /** @var array $recordTypeByChildRecords */
    private array $recordTypeByChildRecords = [];
    /** @var array $noUpdateChildRecordType */
    private array $noUpdateChildRecordType = [
        PRRECORD_TYPE_INVOICE,
        PRRECORD_TYPE_ARADJUSTMENT,
        PRRECORD_TYPE_ARADVANCE
    ];
    /** @var bool $noRecordsFound */
    private bool $noRecordsFound = false;
    /** @var int $_cny */
    private readonly int $_cny;
    /** @var SeqNumManager $seqMgr */
    private readonly SeqNumManager $seqMgr;

    /**
     * @param string $object
     */
    public function __construct(
        private readonly string $object // Possible values: "aradvance", "arpymt"
    ) {
        $this->_cny = GetMyCompany();
        $this->seqMgr = Globals::$g->gManagerFactory->getManager('seqnum');
    }

    /**
     * @param string $sequenceNumber
     *
     * @return bool
     * @throws IAException
     */
    public function migrate(string $sequenceNumber): bool
    {
        $start = microtime(true);
        $cmd = 'migrate_'.$this->object;
        LogToFile("Invoke ".__FILE__."::$cmd:::: at ".date('F jS Y h:i:s A')."\n");
        $lock = new Lock();
        $lockKey = ARPaymentIDMigration::PAYMENTID_PROCESS_LOCK_KEY.'_'.$this->object.'_'.GetMyCompany();
        if( !$lock->setLock($lockKey, 10800, true) ) {
            $message = "Another process to assign Payment IDs to existing payments is running.";
            Globals::$g->gErr->addError('AR-0532', __FILE__ . __LINE__, $message);
            return false;
        }
        try {
            $source = 'ARPaymentIDMigration::migrate';
            //check the timing and decide on it
            $ok = XACT_BEGIN($source);
            $sequenceDetails = $updateValues = [];
            $ok = $ok && $this->getSequenceDetails($sequenceNumber, $sequenceDetails);
            $ok = $ok && $this->buildPaymentIdValues($sequenceDetails, $updateValues);
            $ok = $ok && $this->updateTransactions($updateValues, $sequenceDetails);
            $ok = $ok && XACT_COMMIT($source);
            if (!$ok) {
                Globals::$g->gErr->addError(
                    'AR-0534',
                    __FILE__ . '.' . __LINE__,
                    'Updating the Payment ID for existing transactions was unsuccessful. Try again.'
                );
                XACT_ABORT($source);
                $ok = false;
            }
        } catch (Exception $e) {
            LogToFile($e->getMessage());
            Globals::$g->gErr->addError(
                'AR-0534',
                __FILE__ . '.' . __LINE__,
                'Updating the Payment ID for existing transactions was unsuccessful. Try again.'
            );
            $ok = false;
        }
        $lock->releaseLock();
        $timeTaken = microtime(true) - $start;
        SubLedgerPymtUtils::addToSubLedgerAjaxMetric(get_class($this), ['CMD' => $cmd, 'TIMETAKEN' => $timeTaken]);
        LogToFile("Done. TIME TAKEN for ".__FILE__."::$cmd:::: $timeTaken\n");
        return $ok;
    }

    /**
     * @param string $sequenceNumber
     * @param array  $sequenceDetails
     *
     * @return bool
     * @throws IAException
     */
    private function getSequenceDetails(string $sequenceNumber, array &$sequenceDetails): bool
    {
        // Select the sequence number for update
        $qry = 'QRY_SEQNUM_SELECT_FORUPDATE';
        $result = $this->seqMgr->DoQuery($qry, [$sequenceNumber], false);

        $result = !empty($result[0]) ? $result[0] : [];
        // Convert values to external representation
        $result = $this->seqMgr->_ProcessResult($result);
        if (empty($result) || isNullOrBlank($result['TITLE'])) {
            Globals::$g->gErr->addError(
                'AR-0535',
                __FILE__ . ':' . __LINE__,
                'Select a valid document sequence for payments.'
            );
            return false;
        }

        if ($result['TYPE'] === 'Alpha') {
            Globals::$g->gErr->addError(
                'AR-0533',
                __FILE__ . ':' . __LINE__,
                'The alpha document sequence type is not supported for payments.'
            );
            return false;
        }

        $sequenceDetails = $result;
        return true;
    }

    /**
     * @param array $sequenceDetails
     * @param array $updateValues
     *
     * @return bool
     * @throws IAException
     */
    private function buildPaymentIdValues(array &$sequenceDetails, array &$updateValues = []): bool
    {
        $paymentMap = $childPymtKeyMap = [];
        $ok = $this->fetchAllRecords($paymentMap, $childPymtKeyMap);

        if (!$ok) {
            return $ok;
        }

        $countResult = count($paymentMap);
        if ($countResult === 0) {
            LogToFile("No records Found to update the PAYMENT ID for object ".$this->object);
            $this->noRecordsFound = true;
            //No need to update anything, as the company doesn't have any payment records created
            return true;
        } else if ($sequenceDetails['TONUMBER'] != '' && $countResult > $sequenceDetails['TONUMBER']) {
            Globals::$g->gErr->addError(
                'AR-0535',
                __FILE__ . ':' . __LINE__,
                'The ending number for the selected document sequence is lower than the total number of payments. Update the ending number or select a different document sequence.'
            );
            return false;
        }

        $this->recordTypeByChildRecords = $this->getChildRecordType($childPymtKeyMap);

        $start = microtime(true);
        foreach ($paymentMap as $parentPymtKey => $payment)
        {
            // Generate the sequence number
            $paymentId = $this->seqMgr->GenSequence($sequenceDetails);
            // set the same payment id to all the child payment records
            $this->generatePaymentIdForRecords (
                $parentPymtKey, $payment, $paymentId, $updateValues
            );
            // update the next value
            $sequenceDetails['NEXT'] += 1;
        }
        $timeTaken = microtime(true) - $start;
        LogToFile("Done. TIME TAKEN for buildPaymentIdValues::paymentMap FOREACH:::: $timeTaken");
        LogToFile("paymentMap count - ".count($paymentMap));
        return $ok;
    }

    /**
     * @param array $sequenceDetails
     *
     * @return bool
     */
    private function updateSequenceNextVal(array $sequenceDetails): bool
    {
        $seqQuery = [
            "UPDATE SEQNUM set NEXTVAL = :1,lastupdated = sysdate where name = :2 and cny# = :3",
            $sequenceDetails['NEXT'], $sequenceDetails['TITLE'], $this->_cny
        ];
        // update the nextval in seqnum
        return ExecStmt($seqQuery, false);
    }

    /**
     * @param array $updateValues
     * @param array $sequenceDetails
     *
     * @return bool
     * @throws IAException
     */
    private function updateTransactions(array $updateValues, array $sequenceDetails): bool
    {
        if ($this->noRecordsFound) {
            LogToFile("updateTransactions::No records Found to update the PAYMENT ID for object ".$this->object);
            return true;
        }
        LogToFile("Inside update transactions");
        $source = 'ARPaymentIDMigration::updateTransactions';
        //check the timing and decide on it
        $ok = XACT_BEGIN($source);
        // update the Payment ID for all the payment records
        $ok = $ok && $this->updatePaymentId($updateValues);
        // update the Seqnum NEXTVAL with next number
        $ok = $ok && $this->updateSequenceNextVal($sequenceDetails);

        $ok = $ok && XACT_COMMIT($source);
        if (!$ok) {
            Globals::$g->gErr->addError(
                'AR-0534',
                __FILE__ . '.' . __LINE__,
                'Updating the Payment ID for existing transactions was unsuccessful. Try again.'
            );
            XACT_ABORT($source);
            return false;
        }

        return $ok;
    }

    /**
     * @param int    $parentPymtKey
     * @param array  $payment
     * @param string $paymentId
     * @param array  $updateValues
     *
     * @return void
     */
    private function generatePaymentIdForRecords(
        int $parentPymtKey, array $payment, string $paymentId, array &$updateValues
    ): void
    {
        $updateValues[] = [
            $paymentId,
            $this->_cny,
            $parentPymtKey
        ];

        if (!empty($payment['CHILDPAYMENT'])) {
            foreach ($payment['CHILDPAYMENT'] as $childPymtKey) {
                if (isset($this->recordTypeByChildRecords[$childPymtKey])
                    && !in_array($this->recordTypeByChildRecords[$childPymtKey], $this->noUpdateChildRecordType)) {
                    $updateValues[] = [
                        $paymentId,
                        $this->_cny,
                        $childPymtKey
                    ];
                }
            }
        }

        // check the reversed record and update the payment id
        if (!empty($payment['REVERSED'])) {
            $paymentId .= '-Reversed';
            foreach ($payment['REVERSED'] as $voidParentPymtKey => $voidPayment) {
                $this->generatePaymentIdForRecords(
                    $voidParentPymtKey, $voidPayment, $paymentId, $updateValues
                );
            }
        }
    }

    /**
     * @return array
     */
    protected function paymentQuery(): array
    {
        return [
            'SELECT pr.record#, pr.state, pymt.paymentkey, v.voidpaymentkey
            FROM prrecordmst pr 
            JOIN prentrypymtrecs pymt ON pymt.cny# = pr.cny# AND pymt.parentpymt = pr.record#
            LEFT JOIN voidlink v ON v.cny# = pr.cny# AND v.paymentkey = pr.record# 
            WHERE pr.cny# = :1 AND pr.recordtype IN ( :2, :3 ) AND pr.recordid is null
            GROUP BY pr.record#, pr.state, pymt.paymentkey, v.voidpaymentkey
            ORDER BY pr.record# ASC',
            $this->_cny, 'rp', 'ro'
        ];
    }

    /**
     * @return array
     */
    protected function advanceQuery(): array
    {
        return [
            'SELECT pr.record#, pr.state, v.voidpaymentkey
            FROM prrecordmst pr 
            LEFT JOIN voidlink v ON v.cny# = pr.cny# AND v.paymentkey = pr.record# 
            WHERE pr.cny# = :1 AND pr.recordtype IN (:2) AND pr.recordid is null
            GROUP BY pr.record#, pr.state, v.voidpaymentkey
            ORDER BY pr.record# ASC',
            $this->_cny, 'rr'
        ];
    }

    /**
     * @param array $paymentMap
     * @param array $childPymtKeyMap
     *
     * @return bool
     * @throws IAException
     */
    private function fetchAllRecords(array &$paymentMap, array &$childPymtKeyMap)
    {
        // fetch total Count of Payment records for the company where payment id is null
        if ($this->object === 'aradvance') {
            $query = $this->advanceQuery();
        } else {
            $query = $this->paymentQuery();
        }

        $paymentResults = QueryResult($query);
        $paymentResults = !is_array($paymentResults) ? [] : $paymentResults;
        $reversePymtKeyMap = [];
        $start = microtime(true);
        foreach ($paymentResults as $payment) {
            $parentPymtKey = $payment['RECORD#'];
            $paymentKey = $payment['PAYMENTKEY'] ?? $parentPymtKey;
            $voidPaymentKey = $payment['VOIDPAYMENTKEY'];

            if ($payment['STATE'] === 'V' && isset($reversePymtKeyMap[$parentPymtKey])) {
                $actualPymtKey = $reversePymtKeyMap[$parentPymtKey];
                if ($paymentKey != $parentPymtKey) {
                    $paymentMap[$actualPymtKey]['REVERSED'][$parentPymtKey]['CHILDPAYMENT'][$paymentKey] = $paymentKey;
                    $childPymtKeyMap[$paymentKey] = $paymentKey;
                }
            } else {
                if (!isset($paymentMap[$parentPymtKey])) {
                    $paymentMap[$parentPymtKey]['PARENTPAYMENTKEY'] = $parentPymtKey;
                }
                if ($paymentKey != $parentPymtKey) {
                    $paymentMap[$parentPymtKey]['CHILDPAYMENT'][$paymentKey] = $paymentKey;
                    $childPymtKeyMap[$paymentKey] = $paymentKey;
                }

                if (!empty($voidPaymentKey)) {
                    $paymentMap[$parentPymtKey]['REVERSED'][$voidPaymentKey]['PARENTPAYMENTKEY'] = $voidPaymentKey;
                    $reversePymtKeyMap[$voidPaymentKey] = $parentPymtKey;
                }
            }
        }
        $timeTaken = microtime(true) - $start;
        LogToFile("Done. TIME TAKEN for fetchAllPaymentRecords::FOREACH:::: $timeTaken\n");
        return true;
    }

    /**
     * @param array $paymentKeys
     *
     * @return array
     */
    private function getChildRecordType(array $paymentKeys): array
    {
        if (empty($paymentKeys)) {
            return [];
        }
        $qparams = [
            "SELECT record#, recordtype FROM prrecordmst WHERE cny#=:1",
            $this->_cny
        ];
        $qparams = PrepINClauseStmt($qparams, $paymentKeys, " AND record# ", true, 'paymentidmigration');
        $results = QueryResult($qparams);
        $results = is_array($results) ? $results : [];
        return array_column($results, 'RECORDTYPE', 'RECORD#');
    }

    /**
     * @param array $updateValues
     *
     * @return bool
     * @throws IAException
     */
    private function updatePaymentId(array $updateValues): bool
    {
        $ok = true;
        // Turn off the triggers before updating the transactions
        $stmtstr = "begin DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F'); end;";
        $ok = $ok && ExecSimpleStmt($stmtstr);

        // update the Payment ID for all the payment records
        $bulkQuery = [
            "UPDATE prrecordmst SET RECORDID = :1(i) WHERE cny# = :2(i) AND record# = :3(i) AND recordid is null",
            $updateValues,
            ['text', 'integer', 'integer']
        ];

        $ok = $ok && ExecBulkStmt($bulkQuery);
        if ( ! $ok ) {
            $msg = "Updating the Payment ID for existing transactions was unsuccessful. Try again.";
            Globals::$g->gErr->addError('AR-0534', __FILE__ . ':' . __LINE__, $msg);
        }

        //Turn the triggers back on
        $stmtstr = "begin DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T'); end;";
        $ok = $ok && ExecSimpleStmt($stmtstr);

        return $ok;
    }
}