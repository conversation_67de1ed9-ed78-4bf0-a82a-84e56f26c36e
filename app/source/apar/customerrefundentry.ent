<?php
/**
 * Entity for the Customer Refund Entry object
 *
 * <AUTHOR> <raj<PERSON>.<EMAIL>>
 * @copyright 2024 Intacct Corporation All, Rights Reserved
 */

$kSchemas['customerrefundentry'] = [
    'children'              => [
        'glaccount'      => [
            'fkey'    => 'accountkey',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'glaccountmst',
        ],
        'department'     => [
            'fkey'    => 'dept#',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'departmentmst',
        ],
        'location'       => [
            'fkey'    => 'location#',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'locationmst',
        ],
        'locentity'      => [
            'fkey'    => 'location#',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'locationmst',
        ],
        'acctlabel'      => [
            'fkey'    => 'accountlabelkey',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'accountlabel',
        ],
        'alloc'          => [
            'fkey'    => 'allocationkey',
            'invfkey' => 'record#',
            'join'    => 'outer',
            'table'   => 'allocation',
        ],
        'customerrefund' => [
            'fkey'    => 'recordkey',
            'invfkey' => 'record#',
            'join'    => 'inner',
            'table'   => 'prrecordmst',
        ],
    ],
    'nexus'                 => [
        'glaccount'      => [
            'object'   => 'glaccount',
            'relation' => MANY2ONE,
            'field'    => 'accountno',
        ],
        'department'     => [
            'object'   => 'department',
            'relation' => MANY2ONE,
            'field'    => 'departmentid',
        ],
        'location'       => [
            'object'   => 'location',
            'relation' => MANY2ONE,
            'field'    => 'locationid',
        ],
        'customerrefund' => [
            'object'   => 'customerrefund',
            'relation' => MANY2ONE,
            'field'    => 'RECORDKEY',
        ],
        'locationentity' => [
            'object'   => 'locationentity',
            'relation' => ONE2MANY,
            'field'    => 'locationid',
            'dbalias'  => 'locentity',
        ],
        'accountlabel'   => [
            'object'   => 'accountlabel',
            'relation' => ONE2MANY,
            'field'    => 'accountlabel',
            'dbalias'  => 'acctlabel',
        ],
        'allocation'     => [
            'object'   => 'allocation',
            'relation' => ONE2MANY,
            'field'    => 'allocation',
            'dbalias'  => 'alloc',
        ],
    ],
    'object'                => [
        'RECORDNO',
        'RECORDKEY',
        'ACCOUNTKEY',
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'AMOUNT',
        'TRX_AMOUNT',
        'DEPARTMENTKEY',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LOCATIONKEY',
        'LOCATIONID',
        'LOCATIONNAME',
        'ENTRYDESCRIPTION',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'LINE_NO',
        'CURRENCY',
        'BASECURR',
        'TOTALPAID',
        'TRX_TOTALPAID',
        'TOTALSELECTED',
        'TRX_TOTALSELECTED',
        'RECORDTYPE',
        'PARENTENTRY',
        'LINEITEM',
        'STATE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'schema'                => [
        'RECORDNO'          => 'record#',
        'RECORDKEY'         => 'recordkey',
        'ACCOUNTKEY'        => 'accountkey',
        'ACCOUNTNO'         => 'glaccount.acct_no',
        'ACCOUNTTITLE'      => 'glaccount.title',
        'ALLOCATION'        => 'alloc.allocationid',
        'ACCOUNTLABEL'      => 'acctlabel.label',
        'AMOUNT'            => 'amount',
        'TRX_AMOUNT'        => 'trx_amount',
        'DEPARTMENTKEY'     => 'dept#',
        'DEPARTMENTID'      => 'department.dept_no',
        'DEPARTMENTNAME'    => 'department.title',
        'LOCATIONKEY'       => 'location#',
        'LOCATIONID'        => 'location.location_no',
        'LOCATIONNAME'      => 'location.name',
        'ENTRYDESCRIPTION'  => 'description',
        'EXCH_RATE_DATE'    => 'exch_rate_date',
        'EXCH_RATE_TYPE_ID' => 'exch_rate_type_id',
        'EXCHANGE_RATE'     => 'exchange_rate',
        'LINEITEM'          => 'lineitem',
        'LINE_NO'           => 'line_no',
        'CURRENCY'          => 'currency',
        'BASECURR'          => 'basecurr',
        'TOTALPAID'         => 'totalpaid',
        'TRX_TOTALPAID'     => 'trx_totalpaid',
        'TOTALSELECTED'     => 'totalselected',
        'TRX_TOTALSELECTED' => 'trx_totalselected',
        'RECORDTYPE'        => 'recordtype',
        'PARENTENTRY'       => 'parententry',
        'WHENCREATED'       => 'whencreated',
        'WHENMODIFIED'      => 'whenmodified',
        'CREATEDBY'         => 'createdby',
        'MODIFIEDBY'        => 'modifiedby',
        'STATE'             => 'state',
    ],
    'publish'               => [
        'RECORDNO',
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'ACCOUNTLABEL',
        'ALLOCATION',
        'AMOUNT',
        'TRX_AMOUNT',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LOCATIONID',
        'LOCATIONNAME',
        'ENTRYDESCRIPTION',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'LINE_NO',
        'BASECURR',
        'CURRENCY',
        'TOTALPAID',
        'TRX_TOTALPAID',
        'TOTALSELECTED',
        'TRX_TOTALSELECTED',
    ],
    'sqldomarkup'           => true,
    'sqlmarkupfields'       => [
        'WHENCREATED',
        'WHENMODIFIED',
    ],
    'fieldinfo'             => [
        [
            'path'     => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 15,
                'format'    => $gRecordNoFormat,
            ],
            'hidden'   => true,
            'readonly' => true,
            'id'       => 1,
        ],
        [
            'path'     => 'RECORDKEY',
            'fullname' => 'IA.PARENT_KEY',
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 15,
                'format'    => $gRecordNoFormat,
            ],
            'hidden'   => true,
            'id'       => 2,
        ],
        [
            'path'     => 'ACCOUNTKEY',
            'fullname' => 'IA.ACCOUNT_KEY',
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 15,
                'format'    => $gRecordNoFormat,
            ],
            'derived'  => true,
            'hidden'   => true,
            'id'       => 3,
        ],
        [
            'path'     => 'ACCOUNTNO',
            'fullname' => 'IA.ACCOUNT_NUMBER',
            'type'     => [
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'     => 'glaccount',
            ],
            'id'       => 4,
        ],
        [
            'path'     => 'ACCOUNTTITLE',
            'fullname' => 'IA.GL_ACCOUNT_TITLE',
            'type'     => [
                'type'      => 'text',
                'ptype'     => 'text',
                'maxlength' => 80,
            ],
            'id'       => 5,
        ],
        [
            'path'     => 'AMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'type'     => [
                'type'      => 'currency',
                'ptype'     => 'currency',
                'maxlength' => 14,
                'format'    => $gDecimalFormat,
                'size'      => 18,
            ],
            'required' => true,
            'readonly' => true,
            'id'       => 6,
        ],
        [
            'path'     => 'TRX_AMOUNT',
            'fullname' => 'IA.TRANSACTION_AMOUNT',
            'type'     => [
                'type'      => 'currency',
                'ptype'     => 'currency',
                'maxlength' => 14,
                'format'    => $gDecimalFormat,
                'size'      => 18,
            ],
            'required' => true,
            'id'       => 7,
        ],
        [
            'path'       => 'DEPARTMENTKEY',
            'fullname'   => 'IA.DEPARTMENT_KEY',
            'type'       => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 15,
                'format'    => $gRecordNoFormat,
            ],
            'renameable' => true,
            'derived'    => true,
            'hidden'     => true,
            'id'         => 8,
        ],
        [
            'path'       => 'DEPARTMENTID',
            'fullname'   => 'IA.DEPARTMENT_ID',
            'type'       => [
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'entity'     => 'department',
                'pickentity' => 'departmentpick',
            ],
            'renameable' => true,
            'id'         => 9,
        ],
        [
            'path'     => 'DEPARTMENTNAME',
            'fullname' => 'IA.DEPARTMENT_NAME',
            'type'     => [
                'type'      => 'text',
                'ptype'     => 'text',
                'maxlength' => 40,
            ],
            'id'       => 10,
        ],
        [
            'path'       => 'LOCATIONKEY',
            'fullname'   => 'IA.LOCATION_KEY',
            'type'       => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 15,
                'format'    => $gRecordNoFormat,
            ],
            'renameable' => true,
            'derived'    => true,
            'hidden'     => true,
            'id'         => 11,
        ],
        [
            'path'       => 'LOCATIONID',
            'fullname'   => 'IA.LOCATION_ID',
            'type'       => [
                'type'       => 'ptr',
                'ptype'      => 'ptr',
                'entity'     => 'location',
                'pickentity' => 'locationpick',
            ],
            'renameable' => true,
            'id'         => 12,
        ],
        [
            'path'     => 'LOCATIONNAME',
            'fullname' => 'IA.LOCATION_NAME',
            'type'     => [
                'type'      => 'text',
                'ptype'     => 'text',
                'maxlength' => 40,
            ],
            'id'       => 13,
        ],
        [
            'path'     => 'ENTRYDESCRIPTION',
            'fullname' => 'IA.MEMO',
            'type'     => [
                'type'      => 'textarea',
                'ptype'     => 'textarea',
                'maxlength' => 1000,
            ],
            'id'       => 14,
        ],
        [
            'path'     => 'EXCH_RATE_DATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type'     => [
                'type'   => 'date',
                'ptype'  => 'date',
                'format' => $gDateFormat,
            ],
            'id'       => 15,
        ],
        [
            'path'     => 'EXCH_RATE_TYPE_ID',
            'fullname' => 'IA.EXCHANGE_RATE_TYPE',
            'type'     => [
                'type'   => 'ptr',
                'ptype'  => 'ptr',
                'entity' => 'exchangeratetypesall',
            ],
            'id'       => 16,
        ],
        [
            'path'       => 'EXCHANGE_RATE',
            'fullname'   => 'IA.EXCHANGE_RATE',
            'type'       => [
                'type'      => 'decimal',
                'ptype'     => 'decimal',
                'maxlength' => 12,
            ],
            'precision'  => 12,
            'noformat'   => true,
            'readonly'   => true,
            'rpdMeasure' => false,
            'id'         => 17,
        ],
        [
            'path'     => 'LINEITEM',
            'fullname' => 'IA.LINE_ITEM',
            'type'     => [
                'type'        => 'enum',
                'ptype'       => 'enum',
                'validlabels' => ['IA.T', 'IA.F'],
                'validvalues' => ['T', 'F'],
            ],
            'hidden'   => true,
            'id'       => 18,
        ],
        [
            'path'     => 'LINE_NO',
            'fullname' => 'IA.LINE_NO',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 4,
                'format'    => $gLineNoFormat,
            ],
            'readonly' => true,
            'derived'  => true,
            'id'       => 19,
        ],
        [
            'path'     => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type'     => [
                'type'   => 'ptr',
                'ptype'  => 'ptr',
                'entity' => 'trxcurrencies',
            ],
            'readonly' => true,
            'required' => true,
            'id'       => 20,
        ],
        [
            'path'     => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type'     => [
                'type'   => 'ptr',
                'ptype'  => 'ptr',
                'entity' => 'trxcurrencies',
            ],
            'readonly' => true,
            'required' => true,
            'id'       => 21,
        ],
        [
            'path'     => 'TOTALPAID',
            'fullname' => 'IA.TOTAL_PAID',
            'type'     => [
                'type'      => 'currency',
                'ptype'     => 'currency',
                'maxlength' => 14,
                'format'    => $gDecimalFormat,
            ],
            'id'       => 22,
        ],
        [
            'path'     => 'TRX_TOTALPAID',
            'fullname' => 'IA.TOTAL_TRANSACTION_PAID',
            'type'     => [
                'type'      => 'currency',
                'ptype'     => 'currency',
                'maxlength' => 14,
                'format'    => $gDecimalFormat,
            ],
            'id'       => 23,
        ],
        [
            'path'     => 'TOTALSELECTED',
            'fullname' => 'IA.TOTAL_SELECTED',
            'type'     => [
                'type'      => 'currency',
                'ptype'     => 'currency',
                'maxlength' => 14,
                'format'    => $gDecimalFormat,
            ],
            'id'       => 24,
        ],
        [
            'path'     => 'TRX_TOTALSELECTED',
            'fullname' => 'IA.TOTAL_TRANSACTION_SELECTED',
            'type'     => [
                'type'      => 'currency',
                'ptype'     => 'currency',
                'maxlength' => 14,
                'format'    => $gDecimalFormat,
            ],
            'id'       => 25,
        ],
        [
            'path'     => 'RECORDTYPE',
            'fullname' => 'IA.RECORD_TYPE',
            'type'     => [
                'type'      => 'text',
                'ptype'     => 'text',
                'maxlength' => 2,
            ],
            'hidden'   => true,
            'id'       => 26,
        ],
        [
            'fullname' => 'IA.ACCOUNT_LABEL',
            'type'     => [
                'ptype' => 'text',
                'type'  => 'text',
            ],
            'desc'     => 'IA.ACCOUNT_LABEL',
            'path'     => 'ACCOUNTLABEL',
            'id'       => 29,
        ],
        [
            'fullname'      => 'IA.ALLOCATION',
            'desc'          => 'IA.ALLOCATION',
            'type'          => [
                'ptype'     => 'ptr',
                'entity'    => 'allocation',
                'type'      => 'text',
                'maxlength' => 50,
                'size'      => 10,
            ],
            'noedit'        => 'true',
            'noview'        => 'true',
            'nonew'         => 'true',
            'required'      => false,
            'autofill'      => true,
            'afterfunction' => 'AutoFill',
            'path'          => 'ALLOCATION',
            'id'            => 30,
        ],
        [
            'path'     => 'ISTAX',
            'fullname' => 'IA.IS_TAX',
            'type'     => $gBooleanType,
            'default'  => 'false',
            'readonly' => true,
            'id'       => 31,
        ],
        [
            'fullname' => 'IA.DETAIL_ID',
            'type'     => [
                'ptype'  => 'ptr',
                'type'   => 'ptr',
                'entity' => 'sotaxdetail',
            ],
            'noedit'   => true,
            'nonew'    => true,
            'desc'     => 'IA.DETAIL_ID',
            'path'     => 'DETAILID',
            'id'       => 32,
        ],
        [
            'path'     => 'DETAILKEY',
            'fullname' => 'IA.TAX_DETAIL_KEY',
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 15,
                'format'    => $gRecordNoFormat,
            ],
            'derived'  => true,
            'hidden'   => true,
            'id'       => 33,
        ],
        [
            'path'     => 'PARENTENTRY',
            'fullname' => 'IA.PARENT_ENTRY_KEY',
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 15,
                'format'    => $gRecordNoFormat,
            ],
            'hidden'   => true,
            'readonly' => true,
            'id'       => 34,
        ],
        [
            'path'        => 'PAYMENTTAXCAPTURE',
            'fullname'    => 'IA.CAPTURE_PAYMENT_TAX',
            'type'        => $gBooleanType,
            'default'     => 'false',
            'readonly'    => false,
            'partialedit' => false,
            'id'          => 35,
        ],
        [
            'path'     => 'STATE',
            'fullname' => 'IA.STATE',
            'type'     => [
                'ptype'  => 'enum',
                'type'   => 'text',
                'format' => '/.{0,100}/',
            ],
            'hidden'   => true,
            'readonly' => true,
            'id'       => 36,
        ],
        [
            'path'     => 'PARENTENTRY',
            'fullname' => 'IA.PARENT_ENTRY_KEY',
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'integer',
                'maxlength' => 8,
                'format'    => $gRecordNoFormat,
            ],
            'hidden'   => true,
            'readonly' => true,
            'id'       => 37,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'pairedFields'          => [
        'ACCOUNTNO'    => 'ACCOUNTTITLE',
        'DEPARTMENTID' => 'DEPARTMENTNAME',
        'LOCATIONID'   => 'LOCATIONNAME',
    ],
    'url'                   => ['no_short_url' => true],
    'bulkoperation'         => true,
    'api'                   => [
        'PERMISSION_READ'   => 'NONE',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ],
    'dbsorts'               => [
        ['LINE_NO'],
        ['RECORDNO'],
    ],
    'dbfilters'             => [
        ['customerrefundentry.lineitem', '=', 'T'],
        ['customerrefundentry.recordtype', '=', PRRECORD_TYPE_REFUND,],
    ],
    'printas'               => 'IA.CUSTOMER_REFUND_LINE_DETAIL',
    'pluralprintas'         => 'IA.CUSTOMER_REFUND_LINE_DETAILS',
    'table'                 => 'prentry',
    'updatetable'           => 'prentrymst',
    'parententity'          => 'customerrefund',
    'module'                => 'ar',
    'vid'                   => 'RECORDNO',
    'hasdimensions'         => true,
    'auditcolumns'          => true,
    'cachecustomdimensions' => true,
    'description'           => 'IA.INFORMATION_FOR_CUSTOMER_REFUND_LINE_DETAILS',
];
