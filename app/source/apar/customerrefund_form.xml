<?xml version="1.0" encoding="utf-8"?>
<ROOT>
    <title>IA.CUSTOMER_REFUND</title>
    <view system="true">
        <events>
            <load>initiateFields()</load>
        </events>
        <pages>
            <page>
                <section id="summarySection" readonly="true" className="horizontal headerCustom" customFields="no" hidden="true">
                    <field hidden="true">RECORDNO</field>
                    <field path="SUMMARY_RECORDID" fullname="IA.REFUND_NUMBER"></field>
                    <field path="SUMMARY_PAYMETHOD" fullname="IA.PAYMENT_METHOD">
                        <type type="text" ptype="enum"/>
                    </field>
                    <field fullname="IA.REFUND_TOTAL" isHTML="true">TRX_TOTALENTEREDHEADER</field>
                    <field>
                        <path>CLRDATE</path>
                    </field>
                    <field>
                        <path>CLEARED</path>
                    </field>
                    <field>
                        <path>STAMP</path>
                        <type type="stamp" ptype="stamp"></type>
                    </field>
                    <field fullname="IA.STATE" readonly="true" isHTML="true">STATEHEADER</field>
                </section>
                <section className="transaction-section">
                    <title>IA.CUSTOMER_INFORMATION</title>
                    <customFields>no</customFields>
                    <row>
                        <field fullname="IA.CUSTOMER" clazz="EntityField">
                            <path>CUSTOMERID</path>
                            <events>
                                <change>customerChanged(this.meta);</change>
                            </events>
                        </field>
                        <field userUIControl="ContactControl" clazz="contactField">
                            <path>BILLTOPAYTONAME</path>
                            <events>
                                <change>ContactAddress.populate(this.meta);</change>
                            </events>
                        </field>
                        <field>
                            <path>DOCNUMBER</path>
                        </field>
                        <field>
                            <path>SUPDOCID</path>
                        </field>
                    </row>
                    <row>
                        <field noLabel="true" isHTML='true' readonly='true' path="dummyField">
                            <type type='textlabel' ptype='textlabel'></type>
                            <default></default>
                        </field>
                        <field isHTML="true" readonly="true">
                            <path>BILLTOPAYTOADDRESS</path>
                            <type>
                                <type>multitext</type>
                                <ptype>multitext</ptype>
                            </type>
                        </field>
                    </row>
                </section>
                <section className="transaction-section">
                    <title>IA.PAYMENT_INFORMATION</title>
                    <customFields>no</customFields>
                    <row>
                        <field fullname="IA.REFUND_DATE" readonly="false" required="true">
                            <path>REFUNDDATE</path>
                            <events>
                                <change>refundDateChange(this.meta);</change>
                            </events>
                        </field>
                        <field readonly="true" required="true">
                            <path>PAYMENTMETHOD</path>
                            <events>
                                <change>paymentMethodChanged(this.meta);</change>
                            </events>
                        </field>
                        <field fullname="IA.BANK">
                            <path>FINANCIALENTITY</path>
                            <type>
                                <type>webcombo</type>
                                <ptype>webcombo</ptype>
                            </type>
                            <events>
                                <change>financialAccountChanged(this.meta);</change>
                            </events>
                        </field>
                        <field readonly="true">
                            <path>RECORDID</path>
                        </field>
                    </row>
                </section>
                <section className="transaction-section">
                    <title>IA.CURRENCY</title>
                    <customFields>no</customFields>
                    <row column="4">
                        <field fullname="IA.CREDIT_CURRENCY" required="true">
                            <path>TRANSACTIONCURRENCY</path>
                            <type>
                                <ptype>enum</ptype>
                            </type>
                            <events>
                                <change>transactionCurrencyChanged(this.meta);</change>
                            </events>
                        </field>
                        <field fullname="IA.BANK_CURRENCY" readonly="true">
                            <path>FINANCIALACCOUNTCURRENCY</path>
                        </field>
                        <field hidden="true" readonly="false">
                            <path>AMOUNTTOREFUND</path>
                            <events>
                                <change>amountToRefundChanged(this.meta);</change>
                            </events>
                        </field>
                        <field hidden="true" readonly="false">
                            <path>TRX_AMOUNTTOREFUND</path>
                            <events>
                                <change>amountToRefundChanged(this.meta);</change>
                            </events>
                        </field>
                    </row>
                    <row>
                        <field hidden="true">
                            <path>BASECURR</path>
                            <events>
                                <change>baseCurrChanged(this.meta);</change>
                            </events>
                        </field>
                        <field>
                            <path>EXCH_RATE_TYPE_ID</path>
                        </field>
                        <field>
                            <path>EXCHANGE_RATE</path>
                        </field>
                    </row>
                </section>
                <section id="selectedCreditsSection">
                    <title>IA.AVAILABLE_CREDITS</title>
                    <customFields>no</customFields>
                    <section id="selectedCreditsSummary" className="horizontal">
                        <summaryLayout>inline</summaryLayout>
                        <customFields>no</customFields>
                        <readonly>true</readonly>
                        <field fullname="IA.CUSTOMER_BALANCE">
                            <path>CUSTOMERBALANCE</path>
                            <type type="text" ptype="text"/>
                            <default>0.00</default>
                        </field>
                        <field path="SUMMARY_TOTALCREDITAVAILABLE" fullname="IA.CREDITS_AVAILABLE" hidden="false">
                            <type type="decimal" ptype="decimal"/>
                            <default>0.00</default>
                        </field>
                        <field path="SUMMARY_TOTALREFUNDAPPLIED" fullname="IA.TOTAL_AMOUNT_TO_REFUND">
                            <type type="decimal" ptype="decimal"/>
                            <default>0.00</default>
                        </field>
                    </section>
                    <field hidden="true">REFUNDAPPLIEDMAP</field>
                    <field hidden="true">PRESETRECORDNO</field>
                    <grid enableColumnFilterValidation="true">
                        <path>REFUNDDETAILS</path>
                        <noDragDrop>true</noDragDrop>
                        <enableSelect>true</enableSelect>
                        <hasSort>true</hasSort>
                        <noNewRows>true</noNewRows>
                        <deleteOnGrid>false</deleteOnGrid>
                        <maxRows>500</maxRows>
                        <drawBatchSize>500</drawBatchSize>
                        <uniquePropOnRow>RECORDNO</uniquePropOnRow>
                        <hasFixedNumOfRows>true</hasFixedNumOfRows>
                        <enableEmptyMessage>true</enableEmptyMessage>
                        <emptyMessage>IA.NO_CUSTOMER_SELECTED_OR_NO_AVAILABLE_CREDITS</emptyMessage>
                        <selectColumn></selectColumn>
                        <serverPagination>true</serverPagination>
                        <column>
                            <field hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <path>RECORDTYPE</path>
                            </field>
                        </column>
                        <column columnWidth="120" hasFieldTooltip="true">
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="CREDITTYPE">
                                    <path>SEARCH_CREDITTYPE</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.CREDIT_TYPE">
                                <readonly>true</readonly>
                                <path>CREDITTYPE</path>
                                <sortable>true</sortable>
                            </field>
                        </column>
                        <column hasFieldTooltip="true">
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="RECORDID">
                                    <path>SEARCH_RECORDID</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.TRANSACTION_NUMBER" clazz="floatingLink">
                                <readonly>true</readonly>
                                <path>RECORDID</path>
                                <sortable>true</sortable>
                                <default>IA.VIEW_DETAILS</default>
                            </field>
                        </column>
                        <column columnWidth="150" hasFieldTooltip="true">
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="DOCNUMBER">
                                    <path>SEARCH_DOCNUMBER</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.REFERENCE_NUMBER">
                                <readonly>true</readonly>
                                <path>DOCNUMBER</path>
                                <sortable>true</sortable>
                            </field>
                        </column>
                        <column columnWidth="100" hasFieldTooltip="true">
                            <gridHeading className="center" useUIComponent="true">
                                <field noLabel="true" searchPath="WHENCREATED">
                                    <path>SEARCH_WHENCREATED</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.DATE" hidden="false">
                                <readonly>true</readonly>
                                <path>WHENCREATED</path>
                                <sortable>true</sortable>
                                <type>
                                    <ptype>date</ptype>
                                    <type>date</type>
                                </type>
                            </field>
                        </column>
                        <column hasFieldTooltip="true">
                            <gridHeading className="center" useUIComponent="true">
                                <field noLabel="true" searchPath="TRX_TOTALENTERED">
                                    <path>SEARCH_TRX_TOTALENTERED</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.TXN_AMOUNT">
                                <readonly>true</readonly>
                                <hasTotal>true</hasTotal>
                                <path>TRX_TOTALENTERED</path>
                                <sortable>true</sortable>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                    <format>/^-{0,1}[0-9]*\.{0,1}[0-9]*$/</format>
                                </type>
                            </field>
                        </column>
                        <column hasFieldTooltip="true">
                            <gridHeading className="center" useUIComponent="true">
                                <field noLabel="true" searchPath="TRX_TOTALDUE">
                                    <path>SEARCH_TRX_TOTALDUE</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.CREDITS_AVAILABLE">
                                <readonly>true</readonly>
                                <hasTotal>true</hasTotal>
                                <path>TRX_TOTALDUE</path>
                                <sortable>true</sortable>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                    <format>/^-{0,1}[0-9]*\.{0,1}[0-9]*$/</format>
                                </type>
                            </field>
                        </column>
                        <column hasFieldTooltip="true">
                            <field fullname="IA.REFUND_AMOUNT">
                                <readonly>false</readonly>
                                <hasTotal>true</hasTotal>
                                <path>TRX_REFUNDAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                    <format>/^-{0,1}[0-9]*\.{0,1}[0-9]*$/</format>
                                </type>
                                <events>
                                    <change>refundAmountChanged(this.meta, true);</change>
                                </events>
                            </field>
                        </column>
                    </grid>
                </section>
            </page>
            <!-- GL Posting tabs -->
            <page id="glPosting">
                <readonly>true</readonly>
                <hidden>true</hidden>
                <title>IA.POSTING_DETAILS</title>
                <xi:include href="glposting_grid.xml" xmlns:xi="http://www.w3.org/2003/XInclude"/>
            </page>
        </pages>
    </view>
</ROOT>
