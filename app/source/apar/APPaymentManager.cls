<?
import('PaymentManager');
require_once 'PRRecordEventManagerPP.cls';
require_once 'PRRecordHistoryManager.cls';

/**
 * Class APPaymentManager
 */
class APPaymentManager extends PaymentManager
{
    
    function __construct()
    {
        parent::__construct();
        $this->_SetMyTypes();
        $this->_trackingSelectedAmts = true;
    }
    
    
    /**
     * @param string $ID
     * @param array|null   $fields
     *
     * @return array|false
     */
    function get($ID, $fields = null)
    {
        $obj = parent::get($ID, $fields);

        if(empty($obj)) {
            return $obj;
        }

        if($this->fromGateway || $this->fetchOtherPaymentDetail ) {
            $ID = $obj['RECORDNO'];
            $obj = $this->GetCreditHistoryData($ID, $obj);
            $obj = $this->BuildDrillDowns($obj);
        }

        return $obj;
    }
    
    /**
     * Convert a master detail record into something readable externally
     *
     * @param array $record
     *
     * @return array record formatted for use externally
     */
    protected function _API_FormatMasterDetail($record)
    {
        $ID = $record['RECORDNO'];
        if( !empty($record) && !isset($record['HISTORY'])) {
            $record = $this->GetCreditHistoryData($ID, $record);
            $record = $this->BuildDrillDowns($record);
        }
        return parent::_API_FormatMasterDetail($record);
    }


    function _SetMyTypes() 
    {
        global $kAPid;

        $this->_rType = PRRECORD_TYPE_PAYMENT;
        $this->_moduleKey = $kAPid;
        $this->_batchRType = PRRECORD_TYPE_PAYMENT;
        $this->_pymt_entity = 'vendor';
    }
    
    
    /**
     * @param array $obj
     *
     * @return array
     */
    public function BuildDrillDowns(&$obj)
    {

        // FORMAT AP-SPECIFIC FIELDS
        foreach($obj['ITEMS'] as $itemkey => $item) {
            // make the HTML drilldown a separate field so as not to interfere with other clients (XML)
            $obj['ITEMS'][$itemkey]['DRILLDOWN'] = $this->_BuildDrillDown($item);

            $obj['ITEMS'][$itemkey]['SUPDOC'] = $this->_BuildSupDoc($item);

        }

        return $obj;
    }
    
    
    /**
     * @param array $params
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array[]
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {

        $result = parent::GetList($params, $_crosscny, $nocount);
        $this->SetStatusFromWorkflow($result);

        return $result;

    }

    /**
     * @return string
     */
    function GetPayableDocName()
    {
        return I18N::getSingleToken('IA.BILL');
    }
    
    
    /**
     * @param array  $valuesParam
     *
     * @return bool
     */
    function ValidateInputValues(&$valuesParam)
    {
        // see the cooment in the base class function
        $values = $valuesParam;

        global $gErr;

        $paymethod    = $values['PAYMENTTYPE'];
        $paymentdate = $values['PAYMENTDATE'];
        $financialAcct = $values['FINANCIALACCOUNT'];

        $imOK = true;
        $pymtMgrOK = PaymentManager::ValidateInputValues($values);

        // HANDLE ANY MISSING BANK OR CHARGE CARD ACCOUNT
        if (empty($financialAcct)) {
            $errorCode = ($paymethod == "Credit Card") ? "AP-0281" : "AP-0282";
            $gErr->addError($errorCode, __FILE__ . ":" . __LINE__, "");
            $imOK =  false;
        } 

        // to validate GL Posting to Future Periods
        $imOK = $imOK && ValidateGLPostingDate($paymentdate);

        return ($imOK && $pymtMgrOK);
    }
    
    
    /**
     * @param string[]  $item
     *
     * @return string
     */
    function _BuildSupDoc($item)
    {
        $recordtype = urlencode($item['RECORDTYPE']); 
        $supdoc_link = '';

        // Creating the view_attachment link for pi and ei records
        if($recordtype == 'pi' || $recordtype == 'ei') {
            $supdocid = urlencode(URLCleanParams::insert('.val', $item['SUPDOCID']));
            if (isset($supdocid) && $supdocid != '') {
                $_sess = Session::getKey();
                $opid = GetOperationId('co/lists/supportingdocumentdata/view');
                $scripturl = "lister.phtml?.do=view&.it=supportingdocumentdata&.val=$supdocid&.op=$opid&.popup=1&.viewsonly=1&.sess=$_sess";

                $supdoc_link = '<a class="Pick"  id = "fld_obj__SUPDOCID"  title="view_attachment" href="#" onClick="Launch(\''.$scripturl.'\',\'supdoc\',600,400); return	false;"  onmouseover="window.status=\'View the attachments\';return true;"   onmouseout="window.status=\'\'; return true;"><img src="' . IALayoutManager::getCSSButtonPath("paperclip.gif") . '" border="0" alt="view_attachment" ></a>';
            }
        }

        return $supdoc_link;
    }
    
    
    /**
     * @param array    $paymentRec
     * @param bool     $readRecord
     *
     * @return bool
     */
    function validateEntityLevelPymt($paymentRec, $readRecord = false)
    {

        if($readRecord == true) {
            $t = $this->GetRaw($paymentRec['RECORDNO']);
            $paymentRec = $t[0];
        }
        $type = 'Payments';
        if($paymentRec['RECORDTYPE'] == PRRECORD_TYPE_REIMBURSEMENT) {
            $type = 'Reimbursements';
        }
        
        if( IsMultiEntityCompany() ) {
            $contextLoc = GetContextLocation();
            if(  isset($contextLoc) && $contextLoc != '' && (!isset($paymentRec['LOCATIONKEY']) 
                || $paymentRec['LOCATIONKEY'] == '')
            ) {
                Globals::$g->gErr->addIAError(
                    'AP-0216', "", "Payment approvals or rejections are required
					above the entity level.", [],
					"<br>Go to the Approval $type screen at the top level of your
					company, and try again.", ['TYPE' => $type]
                );
                return false;
            }
        }

        return true;
    }
    
    /**
     * Method to merge the field info.
     */
    public function MergeFieldInfo()
    {
        parent::MergeFieldInfo();
        $kWFPMid = Globals::$g->kWFPMid;
        $enableWFPM = GetPreferenceForProperty($kWFPMid, 'ENABLEWFPM');

        if ($enableWFPM) {
            foreach($this->_schemas[$this->_entity]['fieldinfo'] as $key => $value) {
                if ( $value['path'] == 'PAYMENTTYPE' && (isset($this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['validlabels']) || isset($this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['validvalues']))) {
                    $this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['validlabels'] = array_unique(array_merge($this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['validlabels'], array('IA.WF_CHECK', 'IA.WF_USD_WIRE', 'IA.WF_DOMESTIC_ACH')));
                    $this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['validvalues'] = array_unique(array_merge($this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['validvalues'], array('WF Check', 'WF USD Wire', 'WF Domestic ACH')));
                    $this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['_validivalues'] = array_unique(array_merge($this->_schemas[$this->_entity]['fieldinfo'][$key]['type']['validvalues'], array('WF Check', 'WF USD Wire', 'WF Domestic ACH')));
                }
            }
        }
    }


    /**
     *  Ceates edit/view access URLs for a given object.
     *
     * @param int           $cny           The company id
     * @param string|null   $objectId      Object (record#) id.  If empty, can use $objectVid.
     * @param string|null   $objectVid     Optional object vid. Can be used to find $objectId.
     * @param string        &$editUrl      Returned edit URL.
     * @param string        &$viewUrl      Returned view URL.
     * @param bool          $putInFrameset True means add URL to frameset, false means return 'bare'.
     * @param string        $location
     *
     * @return bool
     */
    public function createAccessUrls($cny, $objectId, $objectVid, &$editUrl, &$viewUrl, $putInFrameset = true,
                                     &$location = null)
    {
        if($this->_entity == 'appostedpayment') {
           return parent::createAccessUrls($cny, $objectId, $objectVid, $editUrl, $viewUrl, $putInFrameset, $location);
        }
        $objMgr = Globals::$g->gManagerFactory->getManager('appymt');
        return  $objMgr->createAccessUrls($cny, $objectId, $objectVid,$editUrl,$viewUrl, $putInFrameset,$location );
    }

    /**
     * @return string
     */
    protected function getAppliedAdvanceRecordTypes(): string
    {
        return PRRECORD_TYPE_APOVERPAYAPPL;
    }

    /**
     * @param mixed $ID
     * @param array $obj
     * @return array
     */
    public function getAppliedCreditDetails(mixed $ID, array $obj): array
    {
        $pymtCondQuery = 'prp.parentpymt = :2';
        $prentryPymtDetailMap = $this->getPRentryDetails($pymtCondQuery, $ID);

        if(!empty($prentryPymtDetailMap)) {
            $payQuery = array();
            $payQuery[0] = "SELECT pay.record#, pay.parentpayment, pay.recordtype, pay.recordid, pay.docnumber, 
                        v.vendorid, v.name vendorname, pay.whendue, pay.whenpaid, pay.whencreated, pay.description
                        FROM prrecordmst pay, vendormst v WHERE pay.cny# = :1
                          and v.cny# = pay.cny# and pay.vendorkey = v.record#";
            $payQuery[1] = GetMyCompany();
            $payQuery = PrepINClauseStmt($payQuery, $this->getSubledgerRecordTypes(), " and pay.recordtype ");
            $payQuery = PrepINClauseStmt($payQuery, array_keys($prentryPymtDetailMap), " and pay.record# ");
            $payQuery[0] .= " order by record# asc";
        }
        $allCredits = array();
        // do the payment query
        if(!empty($payQuery)) {
            $result = QueryResult($payQuery);
            if($result){
                $allCredits = $result;
            }
        }

        $this->prepareCreditInformation($prentryPymtDetailMap, $allCredits, $obj);
        return $obj;
    }


    /**
     * @return array
     */
    private function getSubledgerRecordTypes() : array
    {
        return [PRRECORD_TYPE_BILL, PRRECORD_TYPE_APADJUSTMENT, PRRECORD_TYPE_APOVERPAYAPPL, PRRECORD_TYPE_APADVANCE];
    }
}
