<?php

/**
 * APApproval Rule FormEditor
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */
class APApprovalRuleFormEditor extends ApprovalRuleFormEditor
{

    /**
     * APApprovalRuleFormEditor constructor.
     *
     * @param array $_params
     */
    public function __construct(array $_params)
    {
        parent::__construct($_params);
    }

    /**
     * Returns javascript files used
     *
     * @return array|null
     */
    protected function getJavaScriptFileNames()
    {
        return array('../resources/js/apapprovalrule.js');
    }

    /**
     * I18N. Collection of tokens used primarily in JavaScript.
     *
     * @return array $textTokens
     */
    protected function getFormTokens() : array
    {
        $this->textTokens[] = 'IA.WE_SUPPORT_UPTO_COUNT_LEVELS_OF_APPROVAL_REDUCE_YOUR_LEVELS_AND_THEN_SAVE';
        $this->textTokens[] = 'IA.LEVEL_LINE_NUMBER';

        return parent::getFormTokens();
    }
    /**
     * Defines whether duplicate allowed to create from editor
     *
     * @return bool
     */
    protected function CanDuplicate()
    {
        return false;
    }

    /**
     * Defines whether duplicate allowed to create from editor
     *
     * @return bool
     */
    protected function CanPrint()
    {
        return false;
    }
}