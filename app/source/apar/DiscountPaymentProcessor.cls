<?php

/**
 * Account payable discount payment processing implementation.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */
abstract class DiscountPaymentProcessor extends BasePaymentProcessor
{
    // Class variables
    /**
     * @var bool $isDiscountProcessed
     */
    protected $isDiscountProcessed = false;
    /**
     * @var array $discountDateMap
     */
    protected $discountDateMap = [];
    /** @var array $discountAppliedType term or custom discount */
    protected $discountAppliedType = [];

    /**
     * Get the payment base amount field path in the payment details data structure
     *
     * @return string the payment amount field path in the payment details data structure
     */
    protected function getPaymentBaseAmountPath()
    {
        return 'DISCOUNTAMOUNT';
    }

    /**
     * Get the payment amount field path in the payment details data structure
     *
     * @return string the the payment amount field path in the payment details data structure
     */
    protected function getPaymentAmountPath()
    {
        return 'TRX_DISCOUNTAMOUNT';
    }


    /**
     * Get the payment date field path in the payment details data structure
     *
     * @return string the paymentdate field path in the payment details data structure
     */
    protected function getPaymentDatePath()
    {
        return 'PAYMENTDATE';
    }


    /**
     * Get the payment entry key field path in the payment details data structure
     *
     * @return string the payment entry key field path in the payment details data structure
     */
    protected function getPaymentEntryKeyPath()
    {
        return 'DISCOUNTENTRYKEY';
    }
    /**
     * Get the payment key field path in the payment details data structure
     *
     * @return string the payment key field path in the payment details data structure
     */
    protected function getPaymentKeyPath()
    {
        return 'DISCOUNTKEY';
    }

    /**
     * Get the payment amount
     *
     * @param PaymentDetailDTO $pymtDetail the payment details data
     *
     * @return string the payment amount
     */
    protected function getPaymentAmount($pymtDetail)
    {
        return $pymtDetail->getTrxDiscountAmount();
    }

    /**
     * Process the payments for the given request.
     *
     * @param PaymentRequest  $request
     * @param PaymentResponse $response
     *
     * @return bool false if error else true
     */
    public function processPayments(PaymentRequest $request, PaymentResponse $response)
    {
        // process the discount transactions before applying the payments.
        $ok = $this->applyTransactionsDiscounts($request);
        // go further only if the discount is applied for any transactions
        if($this->isDiscountProcessed) {
            // call the parent to apply the payments.
            $ok = $ok && parent::processPayments($request, $response);
        }
        return $ok;
    }

    /**
     * Process the transactions discounts
     *
     * @param PaymentRequest  $request
     *
     * @return bool
     */
    protected function applyTransactionsDiscounts(PaymentRequest $request)
    {
        $ok = true;

        $discountRequestMap = $request->getPaymentDTO()->getDiscountRequestMap();
        if (empty($discountRequestMap)) {
            return $ok;
        }

        foreach ($discountRequestMap as $transactionKey => $discountMap) {
            $transaction = $request->getTransactions()[$transactionKey] ?? null;
            if (empty($transaction) || !$transaction instanceof PRRecordDTO ||
                !in_array($transaction->getRecordType(), $this->getPaidTransactionRecordTypes())
            ) {
                LogToFile("No transaction details found while apply discount.");
                continue;
            }
            $ok = $ok && $this->applyTransactionsDiscount($request, $transaction, $discountMap);
        }

        return $ok;
    }

    /**
     * Process a transactions discount
     *
     * @param PaymentRequest  $request
     * @param PRRecordDTO     $transaction
     * @param array           $discountMap
     *
     * @return bool
     */
    private function applyTransactionsDiscount(
        PaymentRequest $request, PRRecordDTO $transaction, array $discountMap
    ): bool
    {
        $ok = true;

        $recordNo = $transaction->getRecordNo();
        // set the discount value from the discount map
        $discountDate = $discountMap['DATE'];
        $discountAmount = $discountMap['AMOUNT'];
        $discountType = $discountMap['TYPE'];

        // Build the payment request for the term discount
        $pymtDetail = $this->buildDiscountPymtDetail($recordNo, $discountDate, $discountAmount);

        if ( empty($pymtDetail) ) {
            return true;
        } else {
            // turn the discount process flag to true to process further
            $this->isDiscountProcessed = true;
            // update the discount date mpa for the transaction
            $this->discountDateMap[$recordNo] = $discountDate;
            // set the payment detail into input payment for processing
            $paymentDetailList = $request->getPaymentDTO()->getPaymentDetailList();
            $paymentDetailList[] = $pymtDetail;
            $request->getPaymentDTO()->setPaymentDetailList($paymentDetailList);
        }

        logToFileInfo(
            "Discount applied for transaction " . $recordNo . " with amount " .
            $discountAmount. " and date " . $discountDate ." as discount using as " . $discountType
        );

        return $ok;
    }

    /**
     * Build the payment detail data structure for the payment request
     *
     * @param string    $recordNo
     * @param string    $discountDate
     * @param string    $discountAmount
     *
     * @return PaymentDetailDTO|null
     */
    private function buildDiscountPymtDetail($recordNo, $discountDate, $discountAmount)
    {
        // If no discount is applicable there is not need to continue
        if ( !ibccomp($discountAmount, '0') ) {
            return null;
        }

        $pymtDetail = new PaymentDetailDTO(array());
        $pymtDetail->setRecordKey($recordNo);
        $pymtDetail->setTrxDiscountAmount($discountAmount);
        $pymtDetail->setDiscountDate($discountDate);

        return $pymtDetail;
    }

    /**
     * Build the payment detail data
     *
     * @param array $payment the payment data
     * @param array $entry   the payment entry data
     *
     * @return PaymentDetailDTO the payment detail data
     */
    protected function buildPaymentDetail($payment, $entry)
    {
        $pymtDetail  = parent::buildPaymentDetail($payment, $entry);
        $discountDate = $this->discountDateMap[$pymtDetail->getRecordKey()];
        // update the discount date in pymtdetail
        $pymtDetail->setDiscountDate($discountDate);
        return $pymtDetail;
    }
    
    /**
     * Build the payment record entry
     *
     * @param PREntryDTO      $entry PREntryDTO the transaction entry
     * @param string          $nextRecordNo
     * @param PaymentResponse $response
     *
     * @return string[] the payment record entry
     */
    protected function buildPaymentEntry($entry, $nextRecordNo, PaymentResponse $response)
    {
        $pymtEntry = parent::buildPaymentEntry($entry, $nextRecordNo, $response);
        //set the negative value for the discount amounts
        if(!empty($pymtEntry)) {
            $pymtEntry['TRX_AMOUNT'] = -$pymtEntry['TRX_AMOUNT'];
            $customExchangeRate = $response->getPaymentInfoObj()->getCustomExchangeRate();
            $pymtEntry['EXCHANGE_RATE'] = $customExchangeRate;

            // For vat discount payment store the tax GL account to be used later by the offset discount tax entry
            if ($entry->isVatTermDiscount() && $entry->getisTax() === 'true') {
                $pymtEntry['OVERRIDEOFFSETACCOUNTKEY'] = $entry->getCustomFieldValue('ACCOUNTKEY');
                $pymtEntry['OFFSETISTAX'] = true;
                $pymtEntry['OFFSETDETAILKEY'] = $entry->getTaxDetailKey();
            }
        }
        return $pymtEntry;
    }
    
    /**
     * Build the payment record header
     *
     * @param PaymentDTO    $paymentDTO PaymentDTO
     * @param string        $billKey    bill key
     *
     * @return string[] the payment record header
     */
    protected function buildPaymentHeader(PaymentDTO $paymentDTO, $billKey)
    {
        $header = parent::buildPaymentHeader($paymentDTO, $billKey);
        // add the parent payment details, in this case its bill key
        $header['PARENTPAYMENTKEY'] = $billKey;
        $discountMap = $paymentDTO->getDiscountRequestMap()[$billKey];
        // get the discount date and update to the header
        $header['WHENDISCOUNT'] = $discountMap['DATE'];
        // set the description
        $header['DESCRIPTION'] = 'Discount';
        if ($discountMap['TYPE'] === PaymentUtils::DISCOUNT_CUSTOM) {
            $header['DESCRIPTION'] = 'Custom discount';
        }

        return $header;
    }

    /**
     * Build the payment records
     *
     * @param PaymentRequest  $request
     * @param PaymentResponse $response
     * @param array           $payments the payment data
     *
     * @return bool false if error else true
     */
    protected function buildPaymentRecords(PaymentRequest $request, PaymentResponse $response, &$payments)
    {
        $ok = parent::buildPaymentRecords($request, $response, $payments);
        // Update the exchange rate if the exchange rate type is custom
        $this->populateCustomExchangeRate($response, $payments);
        return $ok;
    }
    
    /**
     * Method to implement the payment confirmation logic for discount payment.
     *
     * @param string[]      $payment       array of payment information
     * @param string[]      $parentPayment array of parent payment record
     * @param bool          $void          bool true if payment is being voided otherwise false
     *
     * @return bool
     */
    public function confirmPayment(&$payment, $parentPayment, $void = false)
    {
        // Get the parent payment record batchkey
        if(!empty($parentPayment)) {
            $prBatchKey = $parentPayment['PRBATCHKEY'];
        } else {
            $dbFormattedDate = $payment['WHENCREATED'];
            $recordtype = $this->getRecordType();
            // credit payment, not pp record created
            if (!CreateDiscountBatch($dbFormattedDate, $recordtype, $batchobj)) {
                $gErr = Globals::$g->gErr;
                $gErr->addError('SL-0220', __FILE__ . '.' . __LINE__, 'CreateDiscountBatch failed.');
                return false;
            }
            $prBatchKey = $batchobj['RECORD#'];
        }

        $payment['PRBATCHKEY'] = $prBatchKey;
        // reverse the foreign key records so that it can have the correct values
        ReverseForeignKeysRec($payment);
        // Update the discount payment record
        $valueSet = [
            'PRBATCHKEY' => $prBatchKey,
        ];
        $state = ($void) ? PRRECORD_STATE_DVOIDED : PRRECORD_STATE_PCONFIRMED;
        $ok = $this->updatePaymentRecordState($payment, $state, $valueSet);

        $ok = $ok && parent::confirmPayment($payment, $valueSet['RECORDNO']);

        return $ok;
    }

    /**
     * Implementing class shall override the payment distribution preferences if need be.
     *
     * @return string
     */
    protected function getPaymentDistributionPreference()
    {
        // For discounts, use only weighted average distribution
        return PaymentUtils::WEIGHTED_AVERAGE_DISTRIBUTION;
    }

    /**
     * @param PaymentDTO $paymentDTO
     *
     * @return string
     */
    protected function getExistingRecordNo(PaymentDTO $paymentDTO)
    {
        return '';
    }

    /**
     * Apply the payments.
     *
     * @param PaymentRequest  $request
     * @param PaymentResponse $response
     *
     * @return bool false if error else true
     */
    protected function applyPayments(PaymentRequest $request, PaymentResponse $response)
    {
        // Before calling the parent payment, base payment amount need to be distributed if that present
        $basePaymentAmount = $request->getPaymentDTO()->getCustomFieldValue('AMOUNTTOPAY');

        $paymentDetails = $request->getPaymentDTO()->getPaymentDetailList();
        if(!empty($paymentDetails) && !empty($basePaymentAmount) && $basePaymentAmount > 0) {

            $distroCount = 0;
            $totalTrxAmont = $this->getPaymentTotalTrxAmount($request, $distroCount);

            if($distroCount > 0) {
                //$counter = 0;
                // Get the exchange rate based on the base payment amount and trx amount
                $exchangeRate = ibcdiv($basePaymentAmount, $totalTrxAmont, 14, true);
                // Set the exchange rate in the response object- this should be used down the line for proessing
                $response->getPaymentInfoObj()->setCustomExchangeRate($exchangeRate);
            }
        }
        return parent::applyPayments($request, $response);
    }

    /**
     * Basic implementation for the payment confirmation.
     *
     * @param PaymentResponse $paymentResponse
     * @param array           $batchRepostCollect
     *
     * @return bool
     */
    public function processConfirmPayment(PaymentResponse $paymentResponse, &$batchRepostCollect = array())
    {
        $ok = true;
        $storePaymentKey = [];
        if(isset($paymentResponse)) {
            $payments = $paymentResponse->getPaymentInfoObj()->getPayments();
            $parentPayment = $paymentResponse->getParentPayment();
            foreach ($payments as &$payment) {
                // create only payments related to this record type
                if ($this->isRecordTypeMatchForPayment($payment) && !$this->checkIfCachedPaymentKey($storePaymentKey, $payment['RECORDNO'])) {
                    // Call the confirm payment
                    $ok = $ok && $this->confirmPayment($payment, $parentPayment);
                    if (!$ok) {
                        break;
                    }else{
                        $paymentResponse->setConfirmPaymentKeys($payment['RECORDNO'], $storePaymentKey);
                        $batchRepostCollect[$payment['PRBATCHKEY']] = $payment['PRBATCHKEY'];
                    }
                }
            }
            // Set the payments back in the PaymentInfo
            $paymentResponse->getPaymentInfoObj()->setPayments($payments);
        }
        return $ok;
    }

    /**
     * Set vat tax fields in the payment record
     * @param array $payment
     * @param PRRecordDTO $transaction
     * @param bool $setVatEnabled
     * @return void
     */
    protected function setVatFieldsForPaymentRecord(array &$payment, PRRecordDTO $transaction, bool $setVatEnabled = false) : void
    {
        $isTaxSubscribed = TaxSetupManager::isTaxModuleConfigured();
        if ($isTaxSubscribed && $transaction->getTxnIsVatEnabled() &&
            TaxSetupManager::isVatDiscountRecordType($payment['RECORDTYPE'] ?? null)) {
            $termKey = $transaction->getTermKey();
            if (isset($termKey)) {
                GetTerm($termKey, $term);
                $discountType = $term['DISCCALCON'] ?? null;
                $payment['ISVATTERMDISCOUNT'] = ($discountType === 'T' || $discountType === 'V') ? true : false;
                // If the term discount has tax implication, set the header tax fields
                if ($payment['ISVATTERMDISCOUNT']) {
                    $setVatEnabled = true;
                }
            }
        }
        parent::setVatFieldsForPaymentRecord($payment, $transaction, $setVatEnabled);
    }

    /**
     * Set vat tax fields in payment entry
     * @param array $payment
     * @param PREntryDTO $entry
     * @return void
     */
    protected function setVatFieldsForPaymentEntry(array $payment, PREntryDTO $entry) : void
    {
        parent::setVatFieldsForPaymentEntry($payment, $entry);
        $entry->setIsVatTermDiscount($payment['ISVATTERMDISCOUNT'] ?? false);
    }

    /**
     * Set VAT fields for tax entries in discount transaction.
     * @param array      &$values         the transaction data
     */
    public static function prepVatEntriesForDiscountTxn(array &$values)
    {
        $recordType = $values['RECORDTYPE'] ?? null;
        if (($values['TXNISVATENABLED'] ?? null) === true) {
            // For VAT discount transaction, set RECORDNO for offset entries if it is not set
            $entryType = PaymentUtils::$recordTypeToEntry[$recordType];
            $entryManager = Globals::$g->gManagerFactory->getManager($entryType);
            foreach ($values['ITEMS'] as &$line) {
                if (empty($line['OFFSET']['RECORDNO'])) {
                    $nextId = $entryManager->GetNextRecordKey();
                    $line['OFFSET'][':record#'] = $line['OFFSET']['RECORDNO'] = $nextId;
                }
            }

            // Create two maps:
            // 1) store the position of an item in the ITEMS array
            // 2) trx entries map to map paid line item to payment line item
            $itemsMap = [];
            $trxEntriesMap = [];
            $lookupParentRecs = [];
            foreach ($values['ITEMS'] as $key => $item) {
                $lineEntry = $item['ITEMS'][0];
                $recNo = $lineEntry['RECORD#'] ?? $lineEntry['RECORDNO'];
                $itemsMap[$recNo] = $key;
                if (!empty($lineEntry['__trxline_recordno'])) {
                    $trxEntriesMap[$lineEntry['__trxline_recordno']] = $lineEntry['RECORDNO'];
                    $isTax = $item['OFFSET']['ISTAX'] ?? null;
                    if ($isTax === 'true' && empty($lineEntry['__trx_parententry'])) {
                        $lookupParentRecs[] = $lineEntry['__trxline_recordno'];
                    }
                }
            }

            // For OE/PO document, parententry is null in prentry. We have to retrieve it from the database
            $parentEntriesMap = DiscountTaxRecordManager::getParentEntryForTaxEntry($lookupParentRecs);

            // Set parent relationship between discount tax entry and discount line item entry
            foreach ($values['ITEMS'] as &$paymentItem) {
                $isTax = $paymentItem['OFFSET']['ISTAX'] ?? null;
                if ($isTax !== 'true') {
                    continue;
                }
                $trxEntryKey = $paymentItem['ITEMS'][0]['__trxline_recordno'] ?? null;
                $trxParentEntryKey = $paymentItem['ITEMS'][0]['__trx_parententry'] ?? null;
                if (empty($trxParentEntryKey) && !empty($trxEntryKey)) {
                    $trxParentEntryKey = $parentEntriesMap[$trxEntryKey];
                }

                if (!empty($trxParentEntryKey)) {
                    $parentIndex = $itemsMap[$trxEntriesMap[$trxParentEntryKey]];
                    $paymentItem['OFFSET']['PARENTENTRY'] = $values['ITEMS'][$parentIndex]['OFFSET']['RECORDNO'];
                }
            }
        }
    }
}