<?
$url = "'<a href=\"javascript:invoiceWindowByRecNo('||prrecord.RECORD#||');\">View PDF</a>' URL";

$obj = array(
    'filter' => "prrecord.recordtype = 'ra'",
    'fieldlabels' => array(
        'Customer', 'Adjustment#', 'Date', 'Amount', '', 'Batch Title', ''
    ),
    'qryfields' => array(
        'prrecord.record#',
        'customer.name',
        'prrecord.recordid',
        'prrecord.whencreated',
        'prrecord.totalentered',
        'prrecord.state',
        'prbatch.title',
        'prrecord.prbatchkey',
        $url,
        'prrecord.totalpaid',
        'prrecord.totalselected',
        'prrecord.totaldue'
    ),
    'tblfields' => array(
        'NAME', 'RECORDID', 'WHENCREATED', 'TOTALENTERED', 'STATE', 'TITLE', 'URL'
    ),
    'fullnames' => array(
        'CUSTOMER.NAME', 'RECORDID', 'WHENCREATED', 'TOTALENTERED', 'STATE', 'PRBATCH.TITLE', 'URL'
    ),
    'groomtype' => array(
        'CHAR', 'CHAR', 'DATE', 'MONEY', 'ARSTATE', 'CHAR'
    ),
    'format' => array(
        'TOTALENTERED' => array(
            'calign' =>'right',
            'cwidth' => '1%'
        ),
        'WHENCREATED' => array(
            'calign' => 'center',
            'cwidth' => '1%'
        ),
        'STATE' => array(
            'calign' => 'center',
            'cwidth' => '1%'
        )
    ),
    'hlpfile' => 'Viewing_and_Managing_AR_Adjustments',
    'sortcol' => 'WHENCREATED:d',
    'detail' => "prrecord.phtml?.recordtype=ra&.op=" . GetOperationId('ar/lists/aradjustment'),
    'editurl' => FwdUrl(
        "editor.phtml?.recordtype=ra&.op=" .
        GetOperationId('ar/lists/aradjustment/edit'), $embeddedDone
    ),
    'addurl' => FwdUrl(
        "editor.phtml?.recordtype=ra&.op=" .
        GetOperationId('ar/lists/aradjustment/create'), $embeddedDone
    )
);
