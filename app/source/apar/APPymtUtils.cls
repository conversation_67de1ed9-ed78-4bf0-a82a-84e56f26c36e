<?php

/**
 * Utility class for AP payments.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation All, Rights Reserved
 */
class APPymtUtils extends SubLedgerPymtUtils
{
    /**
     * Method to set payment type for non US countries if ACH/BankFile
     *
     * @param string $paymentType
     * @param string $currency
     *
     * @return bool
     */
    public static function setNonUSACHPaymentType(&$paymentType, $currency)
    {
        // if pymt type is equal to ach/bankfile and country not US
        if($currency && ($paymentType == PaymentUtils::ACH_PAYMENTMETHOD) && ($currency !== 'USD')) {
            $paymentType = PaymentUtils::ET_PAYMENTMETHOD;
        }
        return true;
    }

}