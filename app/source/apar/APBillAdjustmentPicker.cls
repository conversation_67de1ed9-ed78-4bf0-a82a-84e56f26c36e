<?
class APBillAdjustmentPicker extends NPicker
{
    function __construct()
    {
        /*
        if (!Request::$r->_s) {
            Request::$r->_s = 'WHENCREATED:d';
        }
        */
        parent::__construct(
            array(
            'entity' => 'apbilladjustment',
            'fields' => array(
            'RECORDID', 
            'WHENCREATED',
            'TOTALENTERED', 
            'VENDORID'),
            'format' => array(
            'TOTALENTERED' => array(
            'calign' => 'right'
            )
            ),
            'pickfield' => 'RECORDID',
            'helpfile' => ''
            )
        );
        $this->addLabelMapping('WHENCREATED', 'IA.DOCUMENT_NUMBER', true);
        $this->addLabelMapping('TOTALENTERED', 'IA.AMOUNT', true);
        $this->addLabelMapping('VENDORID', 'IA.VENDOR_ID', true);


    }
}

