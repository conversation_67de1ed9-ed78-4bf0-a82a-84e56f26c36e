<?
require_once 'common_apar.inc';

class CustTypeManager extends HierarchicalEntityManager
{
    /**
     * Delete a record from the database
     * this implementation is usually sufficient for single table objects
     *
     * @param int|string $ID
     *
     * @return bool
     */
    function Delete($ID) 
    {
        $source = 'CustTypeManager::Delete()';
        global $gErr;
        $ok = $this->_QM->beginTrx($source);

        $this->DoEvent('Delete', $ID, false);
        $code = 'QRY_' . isl_strtoupper($this->_entity) . '_DELETE_VID';
        $raw = $this->GetRaw($ID);
        $uid = $raw[0]['NAME'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $custtypeid = $raw[0]['RECORD#'];

        if( !$this->IsDeletable($ID, false) ) { 
            return false;
        }

        $children = $this->getHierarchy($uid);
        
        if (!empty($children) && count($children) > 1) {
            $gErr->addError('AR-0382', __FILE__ . ':' . __LINE__,
            'Another Customertype is refering this Customertype');
            $ok = false;
        }
 
        $ok = $ok && $this->DoQuery($code, array($uid));
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = "Customertype '$ID' cannot be deleted !";
            $gErr->addIAError('AR-0383', __FILE__ . ':' . __LINE__, $msg, ['ID' => $ID]);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }

    /**
     * Provides by-value wrapper for Add.  Can be removed, and AddByRef renamed to Add,
     * when it's verified that it's safe to call this class' Add by-reference.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        return $this->AddByRef($values);
    }
        
    /**
     * @param array $values
     *
     * @return bool
     */
    function AddByRef(&$values)
    {
        $source = $this->_entity . "CustTypeManager::Add";
        global $gErr;
        $ok = $this->_QM->beginTrx($source);
        $nextId = $this->GetNextRecordKey();
        $ok = $ok && isset($nextId);
        if (count($this->GetPrimaryKey()) > 1) { 
            dieFL('Composite primary key not supported.');
        }
        if ($ok) {
            $pkey = $this->GetPrimaryKey();
            $values[':' . $pkey[0]] = $nextId;
        }

        $ok = $ok && $this->translateParentID($values);
        $ok = $ok && parent::regularAdd($values);

        // This is used for a return value
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = "Could not create custtype record!";
            $gErr->addError('AR-0386', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }
    
    /**
     * Wrapper for php5.4 conversion.  Provides by-value wrapper for Set.  Can be removed, and SetByRef renamed to Set,
     * when it's verified that it's safe to call this class' Set by-reference.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        return $this->SetByRef($values);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function SetByRef(&$values)
    {
        $source = $this->_entity . "CustTypeManager::Set";
        global $gErr;

        $parent_id = $values['PARENT']['NAME'];
        if(empty($parent_id)){
            unset($values['PARENT']['RECORDNO']);
        }
        $child_id = $values['NAME'];
        if($parent_id && $child_id) {
            if(!$this->IsValidHierarchy($child_id, $parent_id)) {
                return false;
            }
        }

        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->translateParentID($values);
        $ok = $ok && $this->ValidateCustType($values);
        $ok = $ok && parent::regularSet($values);

        // This is used for a return value
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $msg = I18N::getSingleToken("IA.COULD_NOT_UPDATE_CUSTTYPE_RECORD");
            /** @noinspection PhpUndefinedVariableInspection */
            $gErr->AddDBError("SetCusttype", __FILE__ . ':' . __LINE__, $_Custtype);
            $this->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function ValidateCustType(&$values)
    {
        global $gErr;
        $ok = true;
        $raw = $this->GetRaw($values['NAME']);
        $custtypeid = $raw[0]['RECORD#'];
        
        if ($this->_EtoIstatus($values['STATUS'])=='F') {
            $ok = $ok && CheckForChildRecord('record#', 'custtype', 'parentkey', $custtypeid, 'Customer Type');
            
            $result = CheckForKeyInVendorOrCustomer('customer', 'custtypekey', $custtypeid);
            if (!empty($result)) {
                $gErr->addError('AR-0389', __FILE__ . ':' . __LINE__,
                'This customertype has some  customers');
                $ok = false;
            }
        } else if ($this->_EtoIstatus($values['STATUS']) == 'T' && isset($values[':parentkey']) && $values[':parentkey'] != '') {
            $ok = $ok && CheckForParentRecord('record#', 'custtype', $values[':parentkey'], 'Customer Type');
        }

        return $ok;
    }

    /**
     * @param string $ctype
     *
     * @return bool|string[][]
     */
    function GetAncestors($ctype)
    {
        $qry        = 'QRY_CUSTTYPE_GETANCESTORS';
        $args        = array ($this->_cny,$this->_cny,$ctype);
        return $this->DoQuery($qry, $args);
    }

    /**
     * @param string $ctype
     * @param string $fmt
     *
     * @return bool|array
     */
    function GetChildren($ctype, $fmt='array')
    {
        $qry        = 'QRY_CUSTTYPE_GETCHILDREN';
        $args        = array ($this->_cny,$this->_cny,$ctype);
        $children    = $this->DoQuery($qry, $args);
        if ($fmt == 'array') {
            return $children;
        }
        elseif($fmt == 'tree') {
            foreach($children as $line) {
                $node = array (
                'PARENT'    => $line['PARENTKEY'],
                'ID'        => $line['RECORD#'],
                );
                epp("INSERTING NODE");
                eppp($node);
                dieFL();
                $ok = $this->_TreeMgr->InsertNode($node);
                epp("DONE INSERTING NODE");
                if (!$ok) {
                    return false;
                }
            }
            return $this->_TreeMgr->GetTree();
        }
        return false;
    }

    /**
     * @param string $ctype
     *
     * @return bool
     */
    function UpdateGLGroupsOnParentChange($ctype)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;

        $customerMgr = $gManagerFactory->getManager('customer');
        $nextGrp = $customerMgr->FindGLGroupAncestor($ctype);

        // the root node is in docpargl, so leave things as they are
        if (strcmp($nextGrp['NAME'], $ctype) == 0) {
            return true;
        }

        // get the decendants as a Tree
        $children = $this->GetChildren($ctype, 'tree');
        $prodLineMgr = $gManagerFactory->getManager('productline');
        return $prodLineMgr->UpdateItemsForChildren($children, $nextGrp['RECORD#']);
    }

    /**
     * @param string $ID
     *
     * @return bool
     */
    function CheckCustTypeinDocParGL($ID)
    {
        $qry = 'QRY_CUSTTYPE_CHECK_DOCPARGL' ;
        $args = array ( $ID );

        $ret = $this->DoQuery($qry, $args);
        if (isset($ret[0])) {
            return true;
        }
        else {
            return false;
        }

    }

    /**
     * @param array $tree
     * @param string $nextGrpKey
     *
     * @return bool|array
     */
    function UpdateCustomersForChildren($tree, $nextGrpKey)
    {
        if (count($tree) == 0) { 
            return true; 
        }
        global $gQueryMgr;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $customerMgr = Globals::$g->gManagerFactory->getManager('customer');

        // 1 - Check if root in docpargl
        $rootID = $tree['ID'];
        if ($this->CheckCustTypeinDocParGL($rootID) ) {
            return true;
        }

        $myqry['QRY_UPDATE_CUSTOMER_GLGROUP_SPCL'] = array (
        'QUERY'     => "UPDATE customer SET glgrpkey = $nextGrpKey WHERE custtype = ? and cny# = ?",
        'ARGTYPES'    => array ('integer','integer')
        );
        $gQueryMgr->LoadQueries($myqry);
        $args = array ( $rootID, $this->_cny );
        $ok = $this->DoQuery('QRY_UPDATE_CUSTOMER_GLGROUP_SPCL', $args);

        // recurse down the tree
        foreach ($tree as $node) {
            $ok = $ok && $this->UpdateCustomersForChildren($node['CHILDREN'], $nextGrpKey);
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    private function translateParentID(array &$values) : bool
    {
        $parentID = '';
        $filterKey = "NAME";
        if (isset($values['PARENT']['NAME']) && isSpecified($values['PARENT']['NAME'])) {
            $parentID = $values['PARENT']['NAME'];
        } elseif (isset($values['PARENT']['RECORDNO']) && isSpecified($values['PARENT']['RECORDNO'])) {
            $parentID = $values['PARENT']['RECORDNO'];
            $filterKey = 'RECORDNO';
        }
        if (!empty($parentID)) {
            $parentCustType = $this->GetList(['selects' => ['RECORDNO', 'NAME', 'STATUS'], 'filters' => [[[$filterKey, '=', $parentID]]]]);
            if (!(isset($parentCustType[0])) || $parentCustType[0]['RECORDNO'] == '') {
                Globals::$g->gErr->addError('AR-0384', __FILE__ . ':' . __LINE__,'The parent customer type does not exist');
                return false;
            } else if ($parentCustType[0]['STATUS'] == 'F' || $parentCustType[0]['STATUS'] == 'inactive') {
                Globals::$g->gErr->addError('AR-0385', __FILE__ . ':' . __LINE__, 'Could not add an inactive customer type as the parent type');
                return false;
            } else {
                $values[':parentkey'] = $parentCustType[0]['RECORDNO'];
                $values['PARENT']['NAME'] = $values['PARENT']['NAME'] ?? $parentCustType[0]['NAME'];
            }
        }
        return true;
    }
}

