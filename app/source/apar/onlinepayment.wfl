<?

/*
 *
 * FILE:        onlinepayment.wfl
 * AUTHOR:      <PERSON><PERSON>
 * DESCRIPTION: Online Payment workflow structure, inherits from appayment and overrides the nomenclature
 *
 *	(C) 2001, Intacct Corporation, All Rights Reserved
 *
 *	This document contains trade secret data that belongs to Intacct
 *	Corporation and is protected by the copyright laws.  Information
 *	herein may not be used, copied or disclosed in whole or in part
 *	without prior written consent from Intacct Corporation.
 */

/** @noinspection PhpUndefinedVariableInspection */
if (!is_array($kWorkflow['appayment'])) {
    include( 'appayment.wfl' );
}
I18N::addTokens(I18N::tokenArrayToObjectArray([
    'IA.SCHEDULED','IA.CONFIRMED_DELIVERY'
]));
I18N::getText();
$kWorkflow['onlinepayment'] = $kWorkflow['appayment'];

$kWorkflow['onlinepayment']['nomenclature']['action'][PRRECORD_ACTION_DELIVER] = array('Scheduled', 'schedule');
$kWorkflow['onlinepayment']['nomenclature']['action'][PRRECORD_ACTION_CONFIRMDELIVERY] = array('Confirmed delivery', 'confirm delivery');
$kWorkflow['onlinepayment']['nomenclature']['translated_action'][PRRECORD_ACTION_DELIVER] = array(I18N::getSingleToken('IA.SCHEDULED'), 'schedule');
$kWorkflow['onlinepayment']['nomenclature']['translated_action'][PRRECORD_ACTION_CONFIRMDELIVERY] = array(I18N::getSingleToken('IA.CONFIRMED_DELIVERY'), 'confirm delivery');
$kWorkflow['onlinepayment']['nomenclature']['state'][PRRECORD_STATE_DELIVERED] = 'Scheduled';
$kWorkflow['onlinepayment']['nomenclature']['state'][PRRECORD_STATE_PVOIDED] = 'Delivery canceled';

