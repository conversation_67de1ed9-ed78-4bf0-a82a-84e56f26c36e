<?
/**
 * class <PERSON><PERSON>geGraphLister extends ReportLister
 */

 import('ReportLister');

class ARAgeGraphLister extends ReportLister
{

    function __construct()
    {
        parent::__construct(
            array(
            'entity'        =>  'aragegraph',
            'title'            => 'IA.AR_AGING_GRAPHS',
            'helpfile'        => 'Payment_Methods_Tab',
            'edit'            => 'edit_aparage.phtml?_object=customergraph',
            'linkinfo'            => array(
                                        'haspdf'        => 1,
                                        'hashtml'        => 1,
                                        'opID'            => 'ar/reports/aragegraph/view',
                                        'href'            => "edit_aparage.phtml?_object=customergraph"
                                    )
            )
        );
    }
}


