<?php

/**
 * Entity file for Task Group Members
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */
/**
 * Entity file
 */
$kSchemas['taskgrpmember'] = array(
    'children' => array(
        'task' => array(
            'fkey' => 'taskkey', 'invfkey' => 'record#', 'table' => 'task'),
        'project' => array (
            'fkey' => 'projectkey', 'invfkey' => 'record#', 'table' => 'project'),
    ),
    'object' => array(
        'RECORDNO',
        'GROUPKEY',
        'TASKKEY',
        'TASKID',
        'TASKNAME',
        'TASKSTATUS',
        'PROJECTKEY',
        'PROJECTID',
        'SORTORD',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'GROUPKEY' => 'groupkey',
        'TASKKEY' => 'taskkey',
        'TASKID' => 'task.taskid',
        'TASKNAME' => 'task.name',
        'TASKSTATUS' => 'task.taskstatus',
        'PROJECTKEY' => 'projectkey',
        'PROJECTID' => 'project.projectid',
        'SORTORD' => 'sortord',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED',
    ),
    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'fullname' => 'IA.GROUP',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'desc' => 'IA.GROUP_RECORDNO',
            'path' => 'GROUPKEY',
            'id' => 1
        ),
        array(
            'fullname' => 'IA.MEMBERS',
            'type' => array(
                'entity' => 'task',
                'ptype' => 'ptr',
                'size' => 85,
                'restrict' => array(
                    // This 'restrict' only serves as an indicator into TaskManager that we in fact do want _all_
                    // tasks regardless of Project.  (Without this, there is code in TaskManager that rejects the request
                    // because usually task should never be queried without specifying a project.)
                    array(
                        'pickField' => 'PROJECTKEY',
                        'operand' => '!=',
                        'value' => '-1'
                    ),
                ),
            ),
            'desc' => 'IA.TASK',
            'path' => 'TASKKEY',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.SORT_ORDER',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'desc' => 'IA.SORT_ORDER',
            'path' => 'SORTORD',
            'id' => 3
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
    'auditcolumns' => true,
    'table' => 'taskgrpmembers',
    'dbsorts' => array(array('SORTORD')),
    'parententity' => 'taskgroup',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'printas' => 'IA.TASK_GROUP_MEMBERS',
    'pluralprintas' => 'IA.TASK_GROUP_MEMBERS',
    'module' => 'pa',
);
