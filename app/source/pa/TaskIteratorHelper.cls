<?php
/**
 * Class ContractIteratorHelper
 *
 * Extension of the SupdocAwareIteratorHelper implementing required
 * abstract methods
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2018 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class TaskIteratorHelper extends SupdocAwareIteratorHelper
{
    /**
     * Returns the string representing the intacct object Id field.
     *
     * @return string
     */
    protected function getIntacctObjectIdField() {
        return 'RECORDNO';
    }

    /**
     * Returns the string representing the transaction type for the
     * Intacct object
     *
     * @return string
     */
    protected function getTransactionType() {
        return 'TASK';
    }

}