<?php
/**
 * Project entity Lister
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation, All Rights Reserved
 */

require_once 'backend_employee.inc';
import('NLister');

/**
 * Lister class for the Project entity
 */
class ProjectLister extends NLister
{
    /* @var bool $error */
    private $error;

    /**
     * @var array
     */
    protected $additionalTokens = [
        'IA.GANTT_CHART',
        'IA.TASKS',
        'IA.PROJECT_GANTT_CHART',
    ];

    function __construct()
    {
        $this->error = false;
        
        $empID = GetMyEmpid();
        if ($empID != '') {
            if (!IsEmployeeActive($empID)) {
                Globals::$g->gErr->addError('PA-0045', GetFL(), 'Your employment record is either inactive or terminated.');
                $this->error = true;
            }
        }

        $_op = &Request::$r->_op;
        $gElementMap = &Globals::$g->gElementMap;
        list($mod) = explode('/', $gElementMap[$_op]['key']);

        $this->mod = $mod ?? 'ar';

        $fields = array('PROJECTID', 'NAME', 'PARENTID', 'PROJECTCATEGORY', 'PROJECTSTATUS', 'PROJECTTYPE',
                        'CUSTOMERID','MANAGERID', 'RECORDNO', "'TASK'");
        $helpfile = 'Viewing_and_Managing_Projects';

        parent::__construct(
            array(
                'entity'            =>  'project',
                'title'             =>  'IA.PROJECTS',
                'importtype'        =>  'project',
                'helpfile'          =>  $helpfile,
                'fields'            =>  $fields,
                // the list of fields which need special encoding
                'nonencodedfields' => array('PROJECTID', 'RECORD_URL', 'CONTACTINFO.RECORD_URL', 'BILLTO.RECORD_URL',
                                            'SHIPTO.RECORD_URL', "'GANTTCHART'", "'TASK'"),
                'fieldlabels'       =>  [],
                'enablemultidelete' =>  true,
            )
        );

        // Overrides for column labels
        $this->addLabelMapping('NAME', 'IA.NAME', true);
        $this->addLabelMapping('PARENTID', 'IA.PARENT_ID', true);
        $this->addLabelMapping('PROJECTCATEGORY', 'IA.CATEGORY', true);
        $this->addLabelMapping('PROJECTTYPE', 'IA.TYPE', true);
        $this->addLabelMapping('MANAGERID', 'IA.MANAGER_ID', true);
    }

    /**
     * processSubmit
     *
     * @return bool
     */
    public function processSubmit()
    {
        return ! $this->error;
    }

    /**
     * constructedOK
     *
     * @return bool
     */
    public function constructedOK() 
    {
        return ! $this->error;
    }

    /**
     * buildQuerySpec - override base class to provide additional query metadata
     *
     * @return array containing all query metadata
     */
    function buildQuerySpec()
    {
        $querySpec = parent::BuildQuerySpec();
        $gManagerFactory = Globals::$g->gManagerFactory;            

        // If we are not at the root level let's not list project from an other entity
        if ( !IsRoot() ) {            
            $locMgr =  $gManagerFactory->getManager('location');
            $tempQry = array(
                'selects' => array( 'RECORDNO' ),
                'recursive' => array(
                    'start' => array( array( 'RECORDNO', '=', GetContextLocation() ) )
                ),
            );
            $tempQry = $locMgr->prepareGetList($tempQry);

            $querySpec['filters'][0][] = array(
                'operator' => 'OR',
                'filters' => array(
                    array( 'project.projectlocationkey', 'IS NULL' ),
                    array(
                        'project.projectlocationkey',
                        'INSUBQUERY',
                        array(
                            'STMT' => $tempQry['STMT']['QUERY'],
                            'ARGS' => $tempQry['ARGS'],
                            'ARGTYPES' => $tempQry['STMT']['ARGTYPES'],
                        ),
                    ),
                ),
            );
        }

        $dimComp = Request::$r->_dimComp; 
        if ( isset($dimComp) && $dimComp!='' ) {
            if ($this->showPrivate ) {
                SetReportViewContext();
            }
            $dimCompMgr = $gManagerFactory->getManager('glacctgrp');
            $members = $dimCompMgr->getDimensionMembers($dimComp, $memberType);
            if ( !count($members) ) {
                $members = array('9999999');                
            }
            $querySpec['filters'][0][] = array('RECORDNO', 'IN', $members);
        }
        
        // when it comes from group
        $grpId = Request::$r->_groupid;        
        if ( isset($grpId) && $grpId!='' && $grpId!='None' ) {            
            if ($this->showPrivate ) {
                SetReportViewContext();
            }
            $groupMgr = $gManagerFactory->getManager('projectgroup');
            $members = $groupMgr->getGroupMembersById($grpId, false);
            
            $querySpec['filters'][0][] = array('RECORDNO', 'IN', $members['MEMBERRECS']);
        }        

        return $querySpec;
    }

    /**
     * genGlobs - override base class to provide additional global declarations
     *
     * @return string containing all required client side global declarations
     */
    function genGlobs()
    {
        $ret = parent::genGlobs();    
        $ret .= "<g name='.groupid'>" . isl_htmlspecialchars($this->_params['groupid']) . "</g>";
        $ret .= "<g name='.dimComp'>" . isl_htmlspecialchars(Request::$r->_dimComp) . "</g>";
        return $ret;
    }

    /**
     * @return bool
     */
    protected function useMSTandSkipOuterQryAtEntityLevel() : bool
    {
        return true;
    }
    
    /**
     * getMoreTags - override base class to provide additional tags
     *
     * @return string containing client side tags required
     */
    protected function getMoreTags()
    {
        $gManagerFactory = Globals::$g->gManagerFactory; 
        $grpMgr = $gManagerFactory->getManager('projectgroup');
        
        $params = array(
            'selects' => array('ID'),
            'orders' => array(array('ID')),
        );
        
        $lists = $grpMgr->GetList($params);
        $grps = array('None');
        foreach ($lists as $grp) {
            $grps[] = $grp['ID'];
        }      
        
        $groupsMenu = implode('~~', $grps);
        $grpid = $this->_params['groupid'] ?: 'None';
        
        $tags = "<groupsMenu>$groupsMenu</groupsMenu>\n
                <grpid>$grpid</grpid>\n";
        
        return $tags;
    }

    /**
     * calcParams - override base class to insert additional parameter info
     *
     * @param array $_params input parameter metadata
     *
     * @return array
     */
    function calcParams($_params)
    {
        $_params = parent::CalcParams($_params);
        $_params['groupid'] = Request::$r->_groupid;
 
        return $_params;
    }

    /**
     * buildTable - override base class to provide additional table customization
     */
    function buildTable()
    {
        parent::BuildTable();
      
        $table = &$this->table;
        $tasksListOP = GetOperationId('pa/lists/task');
        $viewGanttOp = GetOperationId('pa/activities/ganttchart/view');

        $kPAid = Globals::$g->kPAid;
        $paModule = ModuleIdToSymbol($kPAid);

        $canListTasks =  IsInstalled($paModule) && IsOperationAllowed($tasksListOP);
        $resourceScheduleEnabled = GetPreferenceForProperty($kPAid, 'ENABLEPROJRESSCHED') == 'T';

        foreach ($table as &$rec) {
            $key = $rec['RECORDNO'];
            if ( $canListTasks ) {
                $key = $rec['RECORDNO'];
                $id = urlencode($rec['PROJECTID']);
                $urlTask = CallUrl(ExtendUrl('lister.phtml', ".op=$tasksListOP&.projectkey=$key&.projectid=$id"));
                $rec["'TASK'"] = '<a href="' . $urlTask . '" >' . GT($this->textMap, 'IA.TASKS') . '</a>';
            } else {
                $rec["'TASK'"] = '';
            }
            if ($resourceScheduleEnabled) {
                $urlGantt = CallUrl(
                    ExtendUrl(
                        'flashcomphelper.phtml',
                        ".op=$viewGanttOp&.entityNo=$key&.type=G&.chart_type=Gantt&.chart_title="
                        . GT($this->textMap, 'IA.PROJECT_GANTT_CHART') . "&.popup=1&.external=1"
                    )
                );
                $rec["'GANTTCHART'"] = "<a target='_blank' href=\"" . $urlGantt . "\">" . GT($this->textMap, 'IA.GANTT_CHART') . "</a>";
            }
        }
        unset($rec);

        $fields = array('PROJECTID', 'NAME', 'PARENTID', 'PROJECTCATEGORY', 'PROJECTSTATUS', 'PROJECTTYPE', 'CUSTOMERID', 'MANAGERID');

        if ($resourceScheduleEnabled) {
            $fields[] = "'GANTTCHART'";
        }
        $fields[] = "'TASK'";

        $this->SetOutputFields($fields, []);
    }

    protected function calcShowHierarchy()
    {
        if (FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('ENABLE_PROJECT_LISTER_NO_HIERARCHY') && !isset(Request::$r->_showhierarchy)) {
            return false;
        } else {
            return parent::calcShowHierarchy();
        }
    }
}
