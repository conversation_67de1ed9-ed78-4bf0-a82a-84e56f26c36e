<?php
/**
 * Timesheet utility file
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2023 Intacct Corporation All, Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 */

class TimesheetUtils {

    const ERROR_MAP = [
    "PA-0463" => "Not authorized to create another employee's timesheet.",
    "PA-0464" => "Not authorized to update another employee's timesheet.",
    "PA-0467" => "Not authorized to create another employee's timesheet entry.",
    "PA-0468" => "Not authorized to update another employee's timesheet entry.",
    "PA-0466" => "Not authorized to view another employee's timesheet.",
    "PA-0470" => "Not authorized to view another employee's timesheet entry."
    ];

    /**
     * @param string    $action
     * @returns bool
     */
    public static function canCheckUserForMyTimesheetPermissions($action) {
        if ($action == 'create') {
            $eeStaffTimesheetOpToUse = 'ee/lists/timesheet/create';
            $eeMyTimesheetOpToUse = 'ee/lists/mytimesheet/create';
            $paStaffTimesheetOpToUse = 'pa/lists/timesheet/create';
            $paMyTimesheetOpToUse = 'pa/lists/mytimesheet/create';
        } else if ($action == 'update') {
            $eeStaffTimesheetOpToUse = 'ee/lists/timesheet/edit';
            $eeMyTimesheetOpToUse = 'ee/lists/mytimesheet/edit';
            $paStaffTimesheetOpToUse = 'pa/lists/timesheet/edit';
            $paMyTimesheetOpToUse = 'pa/lists/mytimesheet/edit';
        } else if ($action == 'delete') {
            $eeStaffTimesheetOpToUse = 'ee/lists/timesheet/delete';
            $eeMyTimesheetOpToUse = 'ee/lists/mytimesheet/delete';
            $paStaffTimesheetOpToUse = 'pa/lists/timesheet/delete';
            $paMyTimesheetOpToUse = 'pa/lists/mytimesheet/delete';
        } else {
            $eeStaffTimesheetOpToUse = 'ee/lists/timesheet/view';
            $eeMyTimesheetOpToUse = 'ee/lists/mytimesheet/view';
            $paStaffTimesheetOpToUse = 'pa/lists/timesheet/view';
            $paMyTimesheetOpToUse = 'pa/lists/mytimesheet/view';
        }

        $hasMyTimesheetPermission = (($operationId = GetOperationId($eeMyTimesheetOpToUse)) > 0 && CheckAuthorization($operationId, 1)) || (($operationId = GetOperationId($paMyTimesheetOpToUse)) > 0 && CheckAuthorization($operationId, 1));
        $hasStaffTimesheetPermission = (($operationId = GetOperationId($eeStaffTimesheetOpToUse)) > 0 && CheckAuthorization($operationId, 1)) || (($operationId = GetOperationId($paStaffTimesheetOpToUse)) > 0 && CheckAuthorization($operationId, 1));

        if ($hasStaffTimesheetPermission) {
            return false;
        } else if ($hasMyTimesheetPermission) {
            return true;
        }
        return false;
    }

    /**
     * @param array|null $values
     * @return int|null
     */
    public static function getTimesheetEmployeeId($values = NULL)
    {
        $tsEmpId = null;
        if ($values != null) {
            $values = (is_array($values[0])) ? $values[0] : $values;
            $tsEmpId = $values['EMPLOYEEID'];
            $empMgr = Globals::$g->gManagerFactory->getManager('employee');
            if (empty($tsEmpId)) {
                if (!empty($values['EMPLOYEEDIMKEY'])) {
                    $employeeKey = $values['EMPLOYEEDIMKEY'];
                } else {
                    $employeeKey = $values['EMPLOYEEKEY'];
                }
                if (!empty($employeeKey)) {
                    $tsEmpId = $empMgr->GetVidFromRecordNo($employeeKey);
                }
            }
            if ( empty($tsEmpId) && !empty($values['TIMESHEETKEY'])) {
                $timesheetMgr = Globals::$g->gManagerFactory->getManager('timesheet');
                $getParams = [
                    'selects' => [ 'EMPLOYEEID' ],
                    'filters' => [
                        [ [ 'RECORDNO', '=', $values['TIMESHEETKEY'] ] ],
                    ],
                ];
                $result = $timesheetMgr->GetList($getParams);
                if ( $result!=null ){
                    $tsEmpId = $result[0]['EMPLOYEEID'];
                }
            }
        }
        return $tsEmpId;
    }
}
