<?php
/**
 * Entity for the Project object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */
$kSchemas['project'] = array(
    'children' => array(
        'parent' => array(
            'fkey' => 'parentkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'projectmst'
        ),
        'customer' => array(
            'fkey' => 'customerkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'customermst'
        ),
        'projectstatus' => array(
            'fkey' => 'projectstatuskey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'projectstatus'
        ),
        'projecttype' => array(
            'fkey' => 'projecttypekey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'projecttype'
        ),
        'projlocation' => array(
            'fkey' => 'projectlocationkey',
            'invfkey' => 'record#',
            'table' => 'location',
            'join' => 'outer'
        ),
        'melocation' => array(
            'fkey' => 'locationkey',
            'invfkey' => 'record#',
            'table' => 'location',
            'join' => 'outer'
        ),
        'projdept' => array(
            'fkey' => 'projectdeptkey',
            'invfkey' => 'record#',
            'table' => 'department',
            'join' => 'outer'
        ),
        'userinfomst' => array(
            'fkey' => 'custuserkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'userinfomst'
        ),
        'manager' => array(
            'fkey' => 'managerkey',
            'invfkey' => 'record#',
            'table' => 'employeemst',
            'join' => 'outer',
            'children' => array(
                'managercontact' => array(
                    'fkey' => 'contactkey',
                    'table' => 'contact',
                    'join' => 'outer'
                ),
            ),
        ),
        'salescontact' => array(
            'fkey' => 'salescontactkey',
            'invfkey' => 'record#',
            'table' => 'employeemst',
            'join' => 'outer',
            'children' => array(
                'salesconcontact' => array(
                    'fkey' => 'contactkey',
                    'invfkey' => 'record#',
                    'join' => 'outer',
                    'table' => 'contact',
                    'children' => array(
                        'salesconmailaddress' => array(
                            'fkey' => 'mailaddrkey',
                            'table' => 'mailaddress',
                            'join' => 'outer'
                        )
                    )
                ),
            )
        ),
        'primarycontact' => array(
            'fkey' => 'contactkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'contact',
            'children' => array(
                'primarymailaddress' => array(
                    'fkey' => 'mailaddrkey',
                    'table' => 'mailaddress',
                    'join' => 'outer'
                )
            )
        ),
        'shiptocontact' => array(
            'fkey' => 'shiptokey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'contact',
            'children' => array(
                'shiptomailaddress' => array(
                    'fkey' => 'mailaddrkey',
                    'table' => 'mailaddress',
                    'join' => 'outer'
                )
            )
        ),
        'billtocontact' => array(
            'fkey' => 'billtokey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'contact',
            'children' => array(
                'billtomailaddress' => array(
                    'fkey' => 'mailaddrkey',
                    'table' => 'mailaddress',
                    'join' => 'outer'
                )
            )
        ),
        'term' => array(
            'fkey' => 'termskey',
            'invfkey' => 'record#',
            'table' => 'term',
            'join' => 'outer'
        ),
        'projectsummary' => array(
            'fkey' => 'record#',
            'invfkey' => 'projectkey',
            'table' => 'v_projectsummary',
            'join' => 'outer'
        ),
        'projectactualsummary' => array(
            'fkey' => 'record#',
            'invfkey' => 'projectkey',
            'table' => 'v_projecttotals',
            'join' => 'outer'
        ),
        'projectglsummary' => array(
            'fkey' => 'record#',
            'invfkey' => 'projectkey',
            'table' => 'v_projectgltotals',
            'join' => 'outer'
        ),
        'class' => array(
            'fkey' => 'classkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'class'
        ),
        'contract' => array(
            'fkey' => 'contractkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'contract'
        ),
        'budgetheader' => array(
            'fkey' => 'budgetkey',
            'invfkey' => 'record#',
            'table' => 'budgetheader',
            'join' => 'outer'
        ),
        'supdocmaps' => array(
            'fkey' => 'projectid',
            'invfkey' => 'recordid',
            'join' => 'outer',
            'table' => 'supdocmaps',
            'filter' => " supdocmaps.transactiontype(+) = 'PROJECT' ",
            'children' => array(
                'supdoc' => array(
                    'fkey' => 'documentid',
                    'invfkey' => 'record#',
                    'table' => 'supdoc',
                    'join' => 'outer'
                )
            )
        ),
        'rootparent' => array(
            'fkey' => 'rootparentkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'project'
        ),
        'rollupproject' => array(
            'fkey' => 'rollup_proj_key',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'project'
        ),
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'PROJECTKEY',             // field name of parent in owned object
            'invfkey' => 'RECORDNO',            // field name of parent in parent object
            'entity' => 'projectresources',
            'skipOwnedObjectWithNoValues' => true, // Skip owned object if it has no values
            'path' => 'PROJECT_RESOURCES'
        ),
        array(
            'fkey' => 'PROJECTKEY',             // field name of parent in owned object
            'invfkey' => 'RECORDNO',            // field name of parent in parent object
            'entity' => 'projecttransactionrule',
            'skipOwnedObjectWithNoValues' => true, // Skip owned object if it has no values
            'path' => 'PROJECT_RULES'
        ),
        array(
            'fkey' => 'PROJECTKEY',             // field name of parent in owned object
            'invfkey' => 'RECORDNO',            // field name of parent in parent object
            'entity' => 'obspctcompletedproject',
            'skipOwnedObjectWithNoValues' => true, // Skip owned object if it has no values
            'path' => 'PROJECT_OBSPCTCOMPLETED'
        ),
    ),
    'object' => array(
        'RECORDNO',
        'PROJECTID',
        'NAME',
        'DESCRIPTION',
        'CURRENCY',
        'PROJECTCATEGORY',
        'PROJECTSTATUSKEY',
        'PROJECTSTATUS',
        'PREVENTTIMESHEET',
        'PREVENTEXPENSE',
        'PREVENTAPPO',
        'PREVENTGENINVOICE',
        'STATUS',
        'BEGINDATE',
        'ENDDATE',
        'BUDGETAMOUNT',
        'CONTRACTAMOUNT',
        'ACTUALAMOUNT',
        'BUDGETQTY',
        'ESTQTY',
        'ACTUALQTY',
        'APPROVEDQTY',
        'REMAININGQTY',
        'PERCENTCOMPLETE',
        'OBSPERCENTCOMPLETE',
        'BILLINGTYPE',
        'SONUMBER',
        'PONUMBER',
        'POAMOUNT',
        'PQNUMBER',
        'SFDCKEY',
        'QARROWKEY',
        'OAKEY',
        'PARENTKEY',
        'PARENTID',
        'PARENTNAME',
        'INVOICEWITHPARENT',
        'CUSTOMERKEY',
        'CUSTOMERID',
        'CUSTOMERNAME',
        'SALESCONTACTKEY',
        'SALESCONTACTID',
        'SALESCONTACTNAME',
        'PROJECTTYPEKEY',
        'PROJECTTYPE',
        'MANAGERKEY',
        'MANAGERID',
        'MANAGERCONTACTNAME',
        'PROJECTDEPTKEY',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'PROJECTLOCATIONKEY',
        'LOCATIONID',
        'LOCATIONNAME',
        'CONTACTINFO.CONTACTNAME',
        'SHIPTO.CONTACTNAME',
        'BILLTO.CONTACTNAME',
        'TERMSKEY',
        'TERMNAME',
        'DOCNUMBER',
        'CUSTUSERKEY',
        'CUSTUSERID',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'BUDGETEDCOST',
        'CLASSID',
        'CLASSNAME',
        'CLASSKEY',
        'USERRESTRICTIONS',
        'BILLABLEEXPDEFAULT',
        'BILLABLEAPPODEFAULT',
        'BUDGETID',
        'BUDGETKEY',
        'BILLINGRATE',
        'BILLINGPRICING',
        'EXPENSERATE',
        'EXPENSEPRICING',
        'POAPRATE',
        'POAPPRICING',
        'CONTACTKEY',
        'SHIPTOKEY',
        'BILLTOKEY',
        'INVOICEMESSAGE',
        'INVOICECURRENCY',
        'BILLINGOVERMAX',   // what to do if actual billings exceed project budget
        'EXCLUDEEXPENSES',  // should billable expenses be excluded from actual & prior billing amounts when Max is enforced
        'CONTRACTKEY',
        'CONTRACTID',
        'SUPDOCID',
        'ROOTPARENTKEY',
        'ROOTPARENTID',
        'ROOTPARENTNAME',
        'CFDA',
        'FUNDEDNAME',
        'AGENCY',
        'PAYER',
        'FUNDINGOTHERID',
        'ASSISTANCETYPE',
        'REVRESTRICTION',
        'RESTRICTIONEXPIRY',
        'RESTRICTIONEXPIRATIONDATE',
        'TIMESATISFACTIONSCHEDULED',
        'ROLLUP_PROJ_KEY',
        'ROLLUPPROJECTID',
        'ROLLUPPROJECTNAME',
        'WIPEXCLUDE',
        'MELOCATIONKEY',
        'MELOCATIONID',
        'MELOCATIONNAME',
        'SCOPE',
        'INCLUSIONS',
        'EXCLUSIONS',
        'TERMS',
        'SCHEDULESTARTDATE',
        'ACTUALSTARTDATE',
        'SCHEDULEDCOMPLETIONDATE',
        'REVISEDCOMPLETIONDATE',
        'SUBSTANTIALCOMPLETIONDATE',
        'ACTUALCOMPLETIONDATE',
        'NOTICETOPROCEED',
        'RESPONSEDUE',
        'EXECUTEDON',
        'SCHEDULEIMPACT',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'PROJECTID' => 'projectid',
        'NAME' => 'name',
        'DESCRIPTION' => 'description',
        'CURRENCY' => 'currency',
        'PROJECTCATEGORY' => 'projectcategory',
        'PROJECTSTATUSKEY' => 'projectstatuskey',
        'PROJECTSTATUS' => 'projectstatus.name',
        'PREVENTTIMESHEET' => 'projectstatus.blockts',
        'PREVENTEXPENSE' => 'projectstatus.blockexp',
        'PREVENTAPPO' => 'projectstatus.blockappo',
        'PREVENTGENINVOICE' => 'projectstatus.blockgeninv',
        'STATUS' => 'status',
        'BEGINDATE' => 'begindate',
        'ENDDATE' => 'enddate',
        'BUDGETAMOUNT' => 'budgetamount',
        'CONTRACTAMOUNT' => 'contractamount',
        'ACTUALAMOUNT' => 'actualamount',
        'BUDGETQTY' => 'budgetqty',
        'ESTQTY' => 'estqty',
        'ACTUALQTY' => 'actualqty',
        'APPROVEDQTY' => 'approvedqty',
        'REMAININGQTY' => 'remainingqty',
        'PERCENTCOMPLETE' => 'percentcomplete',
        'OBSPERCENTCOMPLETE' => 'obspercentcomplete',
        'BILLINGTYPE' => 'billingtype',
        'SONUMBER' => 'sonumber',
        'PONUMBER' => 'ponumber',
        'POAMOUNT' => 'poamount',
        'PQNUMBER' => 'pqnumber',
        'SFDCKEY' => 'sfdckey',
        'QARROWKEY' => 'qarrowkey',
        'OAKEY' => 'oakey',
        'MANAGERKEY' => 'managerkey',
        'MANAGERID' => 'manager.employeeid',
        'MANAGERCONTACTNAME' => 'managercontact.name',
        'PROJECTDEPTKEY' => 'projectdeptkey',
        'DEPARTMENTID' => 'projdept.dept_no',
        'DEPARTMENTNAME' => 'projdept.title',
        'PROJECTLOCATIONKEY' => 'projectlocationkey',
        'LOCATIONID' => 'projlocation.location_no',
        'LOCATIONNAME' => 'projlocation.name',
        'PARENTKEY' => 'parentkey',
        'PARENTID' => 'parent.projectid',
        'PARENTNAME' => 'parent.name',
        'INVOICEWITHPARENT' => 'invoicewithparent',
        'CUSTOMERKEY' => 'customerkey',
        'CUSTOMERID' => 'customer.customerid',
        'CUSTOMERNAME' => 'customer.name',
        'PROJECTTYPEKEY' => 'projecttypekey',
        'PROJECTTYPE' => 'projecttype.name',
        'SALESCONTACTKEY' => 'salescontactkey',
        'SALESCONTACTID' => 'salescontact.employeeid',
        'SALESCONTACTNAME' => 'salesconcontact.name',
        'CONTACTINFO' => array(
            'CONTACTNAME' => 'primarycontact.name',
            'contact.*' => 'primarycontact.*',
            'MAILADDRESS' => array(
                'mailaddress.*' => 'primarymailaddress.*',
            )
        ),
        'BILLTO' => array(
            'CONTACTNAME' => 'billtocontact.name',
            'contact.*' => 'billtocontact.*',
            'MAILADDRESS' => array(
                'mailaddress.*' => 'billtomailaddress.*',
            )
        ),
        'SHIPTO' => array(
            'CONTACTNAME' => 'shiptocontact.name',
            'contact.*' => 'shiptocontact.*',
            'MAILADDRESS' => array(
                'mailaddress.*' => 'shiptomailaddress.*',
            )
        ),
        'TERMSKEY' => 'termskey',
        'TERMNAME' => 'term.name',
        'DOCNUMBER' => 'docnumber',
        'CUSTUSERKEY' => 'custuserkey',
        'CUSTUSERID' => 'userinfomst.loginid',
        'WHENMODIFIED' => 'whenmodified',
        'WHENCREATED' => 'whencreated',
        'MODIFIEDBY' => 'modifiedby',
        'CREATEDBY' => 'createdby',
        'BUDGETEDCOST' => 'budgetedcost',
        'CLASSID' => 'class.classid',
        'CLASSNAME' => 'class.name',
        'CLASSKEY' => 'classkey',
        'USERRESTRICTIONS' => 'userrestrictions',
        'BILLABLEEXPDEFAULT' => 'billableexpdefault',
        'BILLABLEAPPODEFAULT' => 'billableappodefault',
        'BUDGETID' => 'budgetheader.budgetid',
        'BUDGETKEY' => 'budgetkey',
        'BILLINGRATE' => 'billingrate',
        'BILLINGPRICING' => 'billingpricing',
        'EXPENSERATE' => 'expenserate',
        'EXPENSEPRICING' => 'expensepricing',
        'POAPRATE' => 'poaprate',
        'POAPPRICING' => 'poappricing',
        'CONTACTKEY' => 'contactkey',
        'SHIPTOKEY' => 'shiptokey',
        'BILLTOKEY' => 'billtokey',
        'INVOICEMESSAGE' => 'invoicemessage',
        'INVOICECURRENCY' => 'invoicecurrency',
        'BILLINGOVERMAX' => 'billingovermax',
        'EXCLUDEEXPENSES' => 'excludeexpenses',
        'CONTRACTKEY' => 'contractkey',
        'CONTRACTID' => 'contract.contractid',
        'SUPDOCID' => 'supdoc.documentid',
        'ROOTPARENTKEY' => 'rootparentkey',
        'ROOTPARENTID' => 'rootparent.projectid',
        'ROOTPARENTNAME' => 'rootparent.name',
        'CFDA' => 'cfda',
        'FUNDEDNAME' => 'fundedname',
        'AGENCY' => 'agency',
        'PAYER' => 'payer',
        'FUNDINGOTHERID' => 'fundingotherid',
        'ASSISTANCETYPE' => 'assistancetype',
        'REVRESTRICTION' => 'revrestriction',
        'RESTRICTIONEXPIRY' => 'restrictionexpiry',
        'RESTRICTIONEXPIRATIONDATE' => 'restrictiondate',
        'TIMESATISFACTIONSCHEDULED' => 'timesatisfaction',
        'ROLLUP_PROJ_KEY' => 'rollup_proj_key',
        'ROLLUPPROJECTID' => 'rollupproject.projectid',
        'ROLLUPPROJECTNAME' => 'rollupproject.name',
        'WIPEXCLUDE' => 'wipexclude',
        'MELOCATIONKEY' => 'locationkey',
        'MELOCATIONID' => 'melocation.location_no',
        'MELOCATIONNAME' => 'melocation.name',
        'SCOPE' => 'scope',
        'INCLUSIONS' => 'inclusions',
        'EXCLUSIONS' => 'exclusions',
        'TERMS' => 'terms',
        'SCHEDULESTARTDATE' => 'scheduled_start_date',
        'ACTUALSTARTDATE' => 'actual_start_date',
        'SCHEDULEDCOMPLETIONDATE' => 'scheduled_completion_date',
        'REVISEDCOMPLETIONDATE' => 'revised_completion_date',
        'SUBSTANTIALCOMPLETIONDATE' => 'substantial_completion_date',
        'ACTUALCOMPLETIONDATE' => 'actual_completion_date',
        'NOTICETOPROCEED' => 'notice_to_proceed',
        'RESPONSEDUE' => 'response_due',
        'EXECUTEDON' => 'executed_on',
        'SCHEDULEIMPACT' => 'schedule_impact',
        'SI_UUID'     => 'si_uuid',
    ),
    'publish' => array(
        'RECORDNO',
        'PROJECTID',
        'NAME',
        'DESCRIPTION',
        'CURRENCY',
        'PROJECTCATEGORY',
        'PROJECTSTATUS',
        'STATUS',
        'BEGINDATE',
        'ENDDATE',
        'BUDGETAMOUNT',
        'CONTRACTAMOUNT',
        'ACTUALAMOUNT',
        'BUDGETQTY',
        'ESTQTY',
        'ACTUALQTY',
        'APPROVEDQTY',
        'REMAININGQTY',
        'PERCENTCOMPLETE',
        'OBSPERCENTCOMPLETE',
        'BILLINGTYPE',
        'SONUMBER',
        'PONUMBER',
        'POAMOUNT',
        'PQNUMBER',
        'SFDCKEY',
        'QARROWKEY',
        'OAKEY',
        'PARENTKEY',
        'PARENTID',
        'PARENTNAME',
        'INVOICEWITHPARENT',
        'CUSTOMERKEY',
        'CUSTOMERID',
        'CUSTOMERNAME',
        'SALESCONTACTKEY',
        'SALESCONTACTID',
        'PROJECTTYPEKEY',
        'PROJECTTYPE',
        'MANAGERKEY',
        'MANAGERID',
        'PROJECTDEPTKEY',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'PROJECTLOCATIONKEY',
        'LOCATIONID',
        'LOCATIONNAME',
        'CONTACTINFO.CONTACTNAME',
        'SHIPTO.CONTACTNAME',
        'BILLTO.CONTACTNAME',
        'TERMNAME',
        'DOCNUMBER',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'BUDGETEDCOST',
        'USERRESTRICTIONS',
        'BILLABLEEXPDEFAULT',
        'BILLABLEAPPODEFAULT',
        'BUDGETID',
        'BILLINGRATE',
        'BILLINGPRICING',
        'EXPENSERATE',
        'EXPENSEPRICING',
        'POAPRATE',
        'POAPPRICING',
        'CONTACTKEY',
        'SHIPTOKEY',
        'BILLTOKEY',
        'INVOICEMESSAGE',
        'INVOICECURRENCY',
        'BILLINGOVERMAX',
        'EXCLUDEEXPENSES',
        'CONTRACTID',
        'ROOTPARENTKEY',
        'ROOTPARENTID',
        'ROOTPARENTNAME',
        'CFDA',
        'FUNDEDNAME',
        'AGENCY',
        'PAYER',
        'FUNDINGOTHERID',
        'ASSISTANCETYPE',
        'REVRESTRICTION',
        'RESTRICTIONEXPIRY',
        'RESTRICTIONEXPIRATIONDATE',
        'TIMESATISFACTIONSCHEDULED',
        'ROLLUP_PROJ_KEY',
        'ROLLUPPROJECTID',
        'ROLLUPPROJECTNAME',
        'WIPEXCLUDE',
        'SCOPE',
        'INCLUSIONS',
        'EXCLUSIONS',
        'TERMS',
        'SCHEDULESTARTDATE',
        'ACTUALSTARTDATE',
        'SCHEDULEDCOMPLETIONDATE',
        'REVISEDCOMPLETIONDATE',
        'SUBSTANTIALCOMPLETIONDATE',
        'ACTUALCOMPLETIONDATE',
        'NOTICETOPROCEED',
        'RESPONSEDUE',
        'EXECUTEDON',
        'SCHEDULEIMPACT',
    ),
    'nexus' => array(
        'PARENT' => array(
            'object' => 'project',
            'relation' => MANY2ONE,
            'field' => 'PARENTID',
            'printas' => 'IA.PARENT_PROJECT'
        ),
        'shiptocontact' => array(
            'object' => 'contact',
            'relation' => MANY2ONE,
            'field' => 'shipto.contactname',
            'printas' => 'IA.SHIP_TO_CONTACT'
        ),
        'billtocontact' => array(
            'object' => 'contact',
            'relation' => MANY2ONE,
            'field' => 'billto.contactname',
            'printas' => 'IA.BILL_TO_CONTACT'
        ),
        'primarycontact' => array(
            'object' => 'contact',
            'relation' => MANY2ONE,
            'field' => 'contactinfo.contactname',
            'printas' => 'IA.PRIMARY_CONTACT'
        ),
        'salescontact' => array(
            'object' => 'employee',
            'relation' => MANY2ONE,
            'field' => 'salescontactid',
            'printas' => 'IA.SALES_CONTACT'
        ),
        'term' => array(
            'object' => 'arterm',
            'relation' => MANY2ONE,
            'field' => 'termname'
        ),
        'department' => array(
            'object' => 'department',
            'relation' => ONE2MANY,
            'field' => 'departmentid',
            'dbalias' => 'projdept'
        ),
        'location' => array(
            'object' => 'location',
            'relation' => ONE2MANY,
            'field' => 'locationid',
            'dbalias' => 'projlocation'
        ),
        'manager' => array(
            'object' => 'employee',
            'relation' => MANY2ONE,
            'field' => 'managerid',
            'printas' => 'IA.MANAGER'
        ),
        'customer' => array(
            'object' => 'customer',
            'relation' => MANY2ONE,
            'field' => 'customerid',
            'printas' => 'IA.CUSTOMER'
        ),
        'userinfomst' => array(
            'object' => 'userinfo',
            'relation' => MANY2ONE,
            'field' => 'custuserid',
            'printas' => 'IA.EXTERNAL_USER_CUSTOMER'
        ),
        'projectsummary' => array(
            'object' => 'projectsummary',
            'relation' => ONE2MANY,
            'field' => 'record#'
        ),
        'projectactualsummary' => array(
            'object' => 'projectactualsummary',
            'relation' => ONE2MANY,
            'field' => 'record#'
        ),
        'projectglsummary' => array(
            'object' => 'projectglsummary',
            'relation' => ONE2MANY,
            'field' => 'record#'
        ),
        'class' => array(
            'object' => 'class',
            'relation' => ONE2MANY,
            'field' => 'classid',
        ),
        'budgetheader' => array(
            'object' => 'budgetheader',
            'relation' => ONE2MANY,
            'field' => 'budgetid'
        ),
        'ROOTPARENT' => array(
            'object' => 'project',
            'relation' => MANY2ONE,
            'field' => 'ROOTPARENTID',
            'printas' => 'IA.ROOT_PROJECT'
        ),
        'rollupproject' => array(
            'object' => 'project',
            'relation' => MANY2ONE,
            'field' => 'ROLLUPPROJECTID',
            'printas' => 'IA.ROLL_UP_TO_WIP_PROJECT'
        ),
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED'
    ),
    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ),
            'id' => 1
        ),
        array(
            'fullname' => 'IA.PROJECT_ID',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => $gProjectIDFormat,
            ),
            'showInGroup' => true,
            'path' => 'PROJECTID',
            'renameable' => true,
            'id' => 2,
            'gbParent' => true,
        ),
        // following field is required to show projectid in second tab in editor
        array(
            'fullname' => 'IA.PROJECT_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'path' => 'DISPLAYPROJECTID',
            'readonly' => true,
        ),
        array(
            'fullname' => 'IA.PROJECT_NAME',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200,
                'size' => 40,
            ),
            'showInGroup' => true,
            'path' => 'NAME',
            'renameable' => true,
            'id' => 4,
            'gbParent' => true,
        ),
        array(
            'fullname' => 'IA.PROJECT_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200,
                'size' => 40,
            ),
            'path' => 'DISPLAYPROJECTNAME',
        ),

        $gStatusFieldInfo,
        array(
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 4000,
            ),
            'numofrows' => 6,
            'numofcols' => 80,
            'showInGroup' => true,
            'path' => 'DESCRIPTION',
            'id' => 5
        ),
        array(
            'fullname' => 'IA.PROJECT_TYPE_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'PROJECTTYPEKEY',
            'renameable' => true,
            'id' => 6,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.PROJECT_TYPE',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'projecttype',
            ),
            'showInGroup' => true,
            'path' => 'PROJECTTYPE',
            'renameable' => true,
            'idw' => false,
            'id' => 7
        ),
        array(
            'fullname' => 'IA.PARENT_PROJECT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'PARENTKEY',
            'renameable' => true,
            'id' => 8,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.PARENT_PROJECT_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'project',
                'pickentity' => 'projectpick',
            ),
            'showInGroup' => true,
            'path' => 'PARENTID',
            'renameable' => true,
            'idw' => false,
            'id' => 9
        ),
        array(
            'fullname' => 'IA.PARENT_PROJECT_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'path' => 'DISPLAYPARENTID',
        ),
        array(
            'fullname' => 'IA.PARENT_PROJECT_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'path' => 'PARENTNAME',
            'renameable' => true,
            'idw' => false,
            'id' => 10
        ),
        array(
            'fullname' => 'IA.CUSTOMER_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'CUSTOMERKEY',
            'renameable' => true,
            'id' => 11,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.CUSTOMER_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'customer',
                'pickentity' => 'customerpick',
                'pickfield' => array('PICKID', 'CURRENCY'),
            ),
            'path' => 'CUSTOMERID',
            'showInGroup' => true,
            'renameable' => true,
            'idw' => false,
            'id' => 12
        ),
        array(
            'path' => 'CUSTOMERNAME',
            'fullname' => 'IA.CUSTOMER_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'renameable' => true,
            'idw' => false,
            'id' => 62
        ),
        array(
            'fullname' => 'IA.PRIMARY_CONTACT',
            'required' => false,    // Need to override the CONTACTNAME required flag in contact.ent
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'contact',
                'format' => $gContactNameFormat,
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2','MAILADDRESS.ADDRESS3',  'MAILADDRESS.CITY',
                    'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'MAILADDRESS.COUNTRY', "EMAIL1",
                ),
            ),
            'showInGroup' => true,
            'path' => 'CONTACTINFO.CONTACTNAME',
            'idw' => false,
            'id' => 14
        ),
        array(
            'fullname' => '',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'text',
            ),
            'desc' => '',
            'readonly' => true,
            'path' => 'CONTACTINFOADDRESS',
            'id' => 159
        ),
        array(
            'fullname' => 'IA.BILLTO_CONTACT',
            'required' => false,    // Need to override the CONTACTNAME required flag in contact.ent
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'contact',
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2', 'MAILADDRESS.ADDRESS3', 'MAILADDRESS.CITY',
                    'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'MAILADDRESS.COUNTRY', "EMAIL1",
                ),
            ),
            'path' => 'BILLTO.CONTACTNAME',
            'showInGroup' => true,
            'idw' => false,
            'id' => 15
        ),
        array(
            'fullname' => '',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'text',
            ),
            'desc' => '',
            'readonly' => true,
            'path' => 'BILLTOADDRESS',
            'id' => 160
        ),
        array(
            'fullname' => 'IA.SHIPTO_CONTACT',
            'required' => false,    // Need to override the CONTACTNAME required flag in contact.ent
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'contact',
                'format' => $gContactNameFormat,
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2', 'MAILADDRESS.ADDRESS3', 'MAILADDRESS.CITY',
                    'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'MAILADDRESS.COUNTRY', "EMAIL1",
                ),
            ),
            'showInGroup' => true,
            'path' => 'SHIPTO.CONTACTNAME',
            'idw' => false,
            'id' => 16
        ),
        array(
            'fullname' => '',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'text',
            ),
            'desc' => '',
            'readonly' => true,
            'path' => 'SHIPTOADDRESS',
            'id' => 161
        ),
        array(
            'fullname' => 'IA.PROJECT_CURRENCY',
            'showInGroup' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'trxcurrencies',
            ),
            'path' => 'CURRENCY',
            'noview' => true,
            'nonew' => true,
            'id' => 18
        ),
        array(
            'fullname' => 'IA.PROJECT_CATEGORY',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('IA.CONTRACT', 'IA.CAPITALIZED', 'IA.INTERNAL_NON_BILLABLE', 'IA.INTERNAL_BILLABLE'),
                'validvalues' => array('Contract', 'Capitalized', 'Internal Non-billable', 'Internal Billable'),
                '_validivalues' => array('C', 'Z', 'I', 'B'),
            ),
            'required' => true,
            'showInGroup' => true,
            'path' => 'PROJECTCATEGORY',
            'renameable' => true,
            'id' => 19
        ),
        array(
            'fullname' => 'IA.PROJECT_STATUS',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'projectstatus',
            ),
            'path' => 'PROJECTSTATUS',
            'showInGroup' => true,
            'renameable' => true,
            'idw' => false,
            'id' => 20
        ),
        array(
            'fullname' => 'IA.PROJECT_MANAGER_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'MANAGERKEY',
            'renameable' => true,
            'id' => 21,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.PROJECT_MANAGER_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'pickentity' => 'employeepick',
                'entity' => 'employee',
            ),
            'path' => 'MANAGERID',
            'showInGroup' => true,
            'renameable' => true,
            'idw' => false,
            'id' => 22
        ),
        array(
            'path' => 'MANAGERCONTACTNAME',
            'fullname' => 'IA.PROJECT_MANAGER_NAME',
            'renameable' => true,
            'idw' => false,
            'id' => 65
        ),
        array(
            'fullname' => 'IA.EXTERNAL_USER_CUSTOMER',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'userinfo',
                'restrict' => array(
                    array(
                        'value' => array('business user', 'employee user', 'project manager user'),
                        'pickField' => 'USERTYPE',
                    ),
                    array(
                        'pickField' => 'CATEGORY',
                        'operand' => '!=',
                        'value' => UserInfoManager::CATEGORY_EXTERNAL
                    ),
                    array(
                        'pickField' => 'visible',
                        'value' => 'T'
                    ),
                ),
            ),
            'showInGroup' => true,
            'path' => 'CUSTUSERID',
            'renameable' => true,
            'idw' => false,
            'id' => 73,
        ),
        array(
            'fullname' => 'IA.EXTERNAL_USER_CUSTOMER_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'CUSTUSERKEY',
            'renameable' => true,
            'id' => 74,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.DEPARTMENT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'PROJECTDEPTKEY',
            'renameable' => true,
            'id' => 25,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.DEPARTMENT_ID',
            'showInGroup' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'pickentity' => 'departmentpick',
                'entity' => 'department',
                'maxlength' => 40,
            ),
            'path' => 'DEPARTMENTID',
            'idw' => false,
            'renameable' => true,
            'id' => 26
        ),
        array(
            'path' => 'DEPARTMENTNAME',
            'fullname' => 'IA.DEPARTMENT_NAME',
            'renameable' => true,
            'idw' => false,
            'id' => 66
        ),
        array(
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'hidden' => true,
            'path' => 'PROJECTLOCATIONKEY',
            'renameable' => true,
            'id' => 27,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.LOCATION_ID',
            'showInGroup' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'pickentity' => 'locationpick',
                'pickfield' => array('PICKID', 'LOC_CURRENCY'),
                'entity' => 'location',
                'maxlength' => 40,
            ),
            'path' => 'LOCATIONID',
            'renameable' => true,
            'idw' => false,
            'id' => 28
        ),
        array(
            'path' => 'LOCATIONNAME',
            'fullname' => 'IA.LOCATION_NAME',
            'renameable' => true,
            'idw' => false,
            'id' => 67
        ),
        array(
            'fullname' => 'IA.BEGIN_DATE',
            'type' => $gDateType,
            'path' => 'BEGINDATE',
            'showInGroup' => true,
            'id' => 29
        ),
        array(
            'fullname' => 'IA.END_DATE',
            'type' => $gDateType,
            'showInGroup' => true,
            'path' => 'ENDDATE',
            'id' => 30
        ),
        array(
            'fullname' => 'IA.PROJECTED_BILLING_AMOUNT',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 10,
                // Decimal precision was increased at Clarizen’s request. Later, the field was made available in a
                // more general capacity for other 3rd party timesheet integrations.
                // Since its size has been exposed for some time now to other parties via the API, changing it could
                // possibly break some 3rd party integrations.
                'format' => $gCurrencyFormatTenDec
            ),
            'showInGroup' => true,
            'path' => 'BUDGETAMOUNT',
            'id' => 32
        ),
        array(
            'fullname' => 'IA.CONTRACT_AMOUNT',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 15,  // > 15 may cause forced rounding in JS
                                    // JavaScript uses doubles for really large numbers. Doubles lose precision above 2^53 = 9007199254740992 and the number gets rounded.
                'format' => $gCurrencyFormat
            ),
            'path' => 'CONTRACTAMOUNT',
            'showInGroup' => true,
            'id' => 33
        ),
        array(
            'fullname' => 'IA.ACTUAL_AMOUNT',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gCurrencyFormat
            ),
            'path' => 'ACTUALAMOUNT',
            'id' => 34
        ),
        array(
            'fullname' => 'IA.PROJECTED_DURATION_HOURS',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'path' => 'BUDGETQTY',
            'showInGroup' => true,
            'id' => 35
        ),
        array(
            'fullname' => 'IA.ESTIMATED_DURATION',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'path' => 'ESTQTY',
            'showInGroup' => true,
            'readonly' => true,
            'id' => 36
        ),
        array(
            'fullname' => 'IA.ACTUAL_DURATION',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'showInGroup' => true,
            'path' => 'ACTUALQTY',
            'readonly' => true,
            'id' => 37
        ),
        array(
            'fullname' => 'IA.CALCULATED_PERCENTAGE_COMPLETED',
            'desc' => 'IA.CALCULATED_PERCENT_COMPLETED',
            'type' => array(
                'ptype' => 'percent',
                'type' => 'decimal'
            ),
            'showInGroup' => true,
            'path' => 'PERCENTCOMPLETE',
            'readonly' => true,
            'id' => 38
        ),
        array(
            'fullname' => 'IA.OBSERVED_PERCENTAGE_COMPLETED',
            'desc' => 'IA.OBSERVED_PERCENT_COMPLETED',
            'type' => array(
                'ptype' => 'percent',
                'type' => 'decimal'
            ),
            'showInGroup' => true,
            'readonly' => true,
            'calculated' => true,
            'formula' => array(
                'typeOf' => 'PERCENTCOMPLETE',  // must have 'typeof' from a non-calculated field to be processed properly by EntityManager::_processWhereClause (extractFieldPathAndAlias/GetFieldTypeForFilter)
                'fields' => array('CNY#', 'RECORDNO'),
                'function' => "(nvl((select percent from (select (percent / 100.00) as percent from obspctcomplete where cny# = \${1} and projectkey = \${2} order by asofdate desc) where rownum=1),0))",
            ),
            'path' => 'OBSPERCENTCOMPLETE',
            'id' => 126
        ),
        array(
            'fullname' => 'IA.BILLING_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('IA.SELECT', 'IA.TIME_AND_MATERIAL', 'IA.FIXED_FEE', 'IA.FIXED_FEE_AND_EXPENSES'),    // For now validlabels and validvalues have to match coz when it'IA.S_READONLY_THE_UI_LAYER_DOESN't translate the value
                'validvalues' => array('', 'Time & Material', 'Fixed Fee', 'Fixed Fee & Expenses'),
                '_validivalues' => array('', 'T', 'X', 'E'),
            ),
            'path' => 'BILLINGTYPE',
            'showInGroup' => true,
            'id' => 39
        ),
        array(
            'fullname' => 'IA.SALES_ORDER_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40
            ),
            'showInGroup' => true,
            'path' => 'SONUMBER',
            'id' => 40
        ),
        array(
            'fullname' => 'IA.PURCHASE_ORDER_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40
            ),
            'showInGroup' => true,
            'path' => 'PONUMBER',
            'id' => 41
        ),
        array(
            'fullname' => 'IA.PURCHASE_ORDER_AMOUNT',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 20,
                'format' => $gCurrencyFormat
            ),
            'showInGroup' => true,
            'path' => 'POAMOUNT',
            'id' => 42
        ),
        array(
            'fullname' => 'IA.PURCHASE_QUOTE_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40
            ),
            'showInGroup' => true,
            'path' => 'PQNUMBER',
            'id' => 43
        ),
        array(
            'fullname' => 'IA.SALESFORCE_KEY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40
            ),
            'hidden' => true,
            'path' => 'SFDCKEY',
            'id' => 44
        ),
        array(
            'fullname' => 'IA.QUICK_ARROW_KEY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40
            ),
            'hidden' => true,
            'path' => 'QARROWKEY',
            'id' => 45
        ),
        array(
            'fullname' => 'IA.OPEN_AIR_KEY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40
            ),
            'hidden' => true,
            'path' => 'OAKEY',
            'id' => 46
        ),
        array(
            'path' => 'BUDGETID',
            'fullname' => 'IA.GL_BUDGET_ID',
            'desc' => 'IA.GL_BUDGET_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'budgetheader',
                'pickentity' => 'budgetheader',
                'pickfield' => array('BUDGETID', 'RECORDNO')
            ),
            'idw' => false,
            'id' => 47
        ),
        array(
            'path' => 'BUDGETKEY',
            'fullname' => 'IA.BUDGET_KEY',
            'desc' => 'IA.BUDGET_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ),
            'hidden' => true,
            'derived' => true,
            'id' => 115
        ),
        array(
            'path' => 'GLBUDGETAMOUNT',
            'fullname' => 'IA.BUDGETED_COST_FROM_GL',
            'desc' => 'IA.BUDGETED_COST_FROM_GL',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 114
        ),
        array(
            'fullname' => 'IA.BUDGET_ACCOUNT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'glaccount',
                'pickentity' => 'glaccountpick',
                'maxlength' => 40
            ),
            'path' => 'BUDGETACCOUNT',
            'id' => 48
        ),
        array(
            'fullname' => 'IA.BUDGET_DEPARTMENT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'department',
                'pickentity' => 'departmentpick',
                'maxlength' => 40
            ),
            'path' => 'BUDGETDEPT',
            'renameable' => true,
            'id' => 49
        ),
        array(
            'fullname' => 'IA.BUDGET_LOCATION',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'location',
                'pickentity' => 'locationpick',
                'maxlength' => 40
            ),
            'path' => 'BUDGETLOCATION',
            'renameable' => true,
            'id' => 50
        ),
        array(
            'path' => 'PROJECTSTATUSKEY',
            'id' => 61,
            'type' => array('type' => 'integer'),
            'derived' => true
        ),
        array(
            'path' => 'SALESCONTACTKEY',
            'fullname' => 'IA.SALES_CONTACT_KEY',
            'type' => array('type' => 'integer'),
            'id' => 63,
            'derived' => true
        ),
        array(
            'fullname' => 'IA.SALES_CONTACT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'pickentity' => 'employeepick',
                'entity' => 'employee',
            ),
            'showInGroup' => true,
            'path' => 'SALESCONTACTID',
            'renameable' => true,
            'idw' => false,
            'id' => 17
        ),
        array(
            'path' => 'SALESCONTACTNAME',
            'fullname' => 'IA.SALES_CONTACT_NAME',
            'idw' => false,
            'id' => 64
        ),
        array(
            'fullname' => 'IA.APPROVED_DURATION',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'path' => 'APPROVEDQTY',
            'showInGroup' => true,
            'readonly' => true,
            'id' => 68,
        ),
        array(
            'fullname' => 'IA.REMAINING_DURATION',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'path' => 'REMAININGQTY',
            'showInGroup' => true,
            'readonly' => true,
            'id' => 69,
        ),
        array(
            'fullname' => 'IA.TERM',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'arterm',
            ),
            'showInGroup' => true,
            'path' => 'TERMNAME',
            'idw' => false,
            'id' => 70,
        ),
        array(
            'fullname' => 'IA.TERM_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'TERMSKEY',
            'derived' => true,
            'id' => 71,
        ),
        array(
            'fullname' => 'IA.REFERENCE_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 120,
                'format' => '/^.{0,120}$/'
            ),
            'maxlength' => 120,
            'path' => 'DOCNUMBER',
            'showInGroup' => true,
            'id' => 72,
        ),
        array(
            'path' => 'WHENMODIFIED',
            'fullname' => 'IA.WHEN_MODIFIED',
            'type' => array(
                'type' => 'timestamp',
                'maxlength' => 22,
            ),
            'id' => 75
        ),
        $gWhenCreatedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        array(
            'path' => 'TOTALDEFERREDREVENUE',
            'fullname' => 'IA.TOTAL_DEFERRED_REVENUE',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 76
        ),
        array(
            'path' => 'TOTALREVENUE',
            'fullname' => 'IA.TOTAL_REVENUE',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 77
        ),
        array(
            'path' => 'TOTALEXPENSES',
            'fullname' => 'IA.TOTAL_EXPENSES',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'readonly' => true,
            'showInGroup' => true,
            'id' => 80
        ),
        array(
            'path' => 'BUDGETEDCOST',
            'fullname' => 'IA.PROJECTED_COST',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'decimal',
                // Decimal precision was increased at Clarizen’s request. Later, the field was made available in a
                // more general capacity for other 3rd party timesheet integrations.
                // Since its size has been exposed for some time now to other parties via the API, changing it could
                // possibly break some 3rd party integrations.
                'format' => $gCurrencyFormatTenDec,
            ),
            'showInGroup' => true,
            'id' => 81
        ),
        array(
            'path' => 'INVOICEDAMOUNT',
            'fullname' => 'IA.ACTUAL_BILLINGS',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 127
        ),
        array(
            'path' => 'TOTALCOST',
            'fullname' => 'IA.TOTAL_COST_OF_GOODS',
            'type' => array(
                'ptype' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'readonly' => true,
            'id' => 82
        ),
        array(
            'path' => 'BUDGETAMOUNTDIFFERENCE',
            'fullname' => 'IA.PROJECTED_BILLING_AMOUNT_DIFFERENCE',
            'type' => array(
                'ptype' => 'decimal',
                'format' => $gDecimalFormat,
            ),
            'readonly' => true,
            'id' => 83
        ),
        array(
            'path' => 'BUDGETCOSTDIFFERENCE',
            'fullname' => 'IA.PROJECTED_COST_DIFFERENCE',
            'type' => array(
                'ptype' => 'decimal',
                'format' => $gDecimalFormat,
            ),
            'readonly' => true,
            'id' => 84
        ),
        array(
            'path' => 'BUDGETDURATIONDIFFERENCE',
            'fullname' => 'IA.PROJECTED_DURATION_DIFFERENCE_HOURS',
            'type' => array(
                'ptype' => 'decimal',
                'format' => $gDecimalFormat,
            ),
            'readonly' => true,
            'id' => 85
        ),
        array(
            'path' => 'BILLABLEHOURS',
            'fullname' => 'IA.TOTAL_BILLABLE_HOURS',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'format' => $gDecimalFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'hasTotal' => true,
            'readonly' => true,
            'id' => 86
        ),
        array(
            'path' => 'UNBILLEDHOURS',
            'fullname' => 'IA.TOTAL_UNBILLED_HOURS',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'format' => $gDecimalFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'hasTotal' => true,
            'readonly' => true,
            'id' => 87
        ),
        array(
            'path' => 'BILLEDHOURS',
            'fullname' => 'IA.TOTAL_BILLED_HOURS',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'format' => $gDecimalFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'hasTotal' => true,
            'readonly' => true,
            'id' => 88
        ),
        array(
            'path' => 'NONBILLABLEHOURS',
            'fullname' => 'IA.TOTAL_NONBILLABLE_HOURS',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'format' => $gDecimalFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'hasTotal' => true,
            'readonly' => true,
            'id' => 89
        ),
        array(
            'path' => 'BILLABLEEXPENSES',
            'fullname' => 'IA.TOTAL_BILLABLE_EXPENSES',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'format' => $gDecimalFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 90
        ),
        array(
            'path' => 'UNBILLEDEXPENSES',
            'fullname' => 'IA.TOTAL_UNBILLED_EXPENSES',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 91
        ),
        array(
            'path' => 'BILLEDEXPENSES',
            'fullname' => 'IA.TOTAL_BILLED_EXPENSES',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'format' => $gDecimalFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 92
        ),
        array(
            'path' => 'NONBILLABLEEXPENSES',
            'fullname' => 'IA.TOTAL_NONBILLABLE_EXPENSES',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'format' => $gDecimalFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 93
        ),
        array(
            'path' => 'TOTALPAYMENTS',
            'fullname' => 'IA.TOTAL_PAYMENTS',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 94
        ),
        array(
            'path' => 'TOTALWAGES',
            'fullname' => 'IA.TOTAL_WAGES',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 95
        ),
        array(
            'path' => 'TOTALGROSSPROFIT',
            'fullname' => 'IA.GROSS_PROFIT',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 96
        ),
        array(
            'path' => 'TOTALNETINCOME',
            'fullname' => 'IA.NET_INCOME_LOSS',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15    // in a grid, we need size
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 97
        ),
        array(
            'fullname' => 'IA.ACTUAL_DURATION_HOURS',
            'type' => array(
                'ptype' => 'decimal',
                'format' => $gDecimalFormat,
            ),
            'path' => 'ACTUALDURATION',
            'hasTotal' => true,
            'readonly' => true,
            'id' => 98
        ),
        array(
            'path' => 'ENTITY_NO',
            'fullname' => 'IA.ENTITY_ID',
            'type' => array(
                'ptype' => 'text',
                'size' => 15    // in a grid, we need size
            ),
            'readonly' => true,
            'id' => 99
        ),
        array(
            'path' => 'ENTITY_NAME',
            'fullname' => 'IA.ENTITY_NAME',
            'type' => array(
                'ptype' => 'text',
                'size' => 15    // in a grid, we need size
            ),
            'readonly' => true,
            'id' => 100
        ),
        array(
            'path' => 'ENTITY_CURRENCY',
            'fullname' => 'IA.ENTITY_CURRENCY',
            'type' => array(
                'ptype' => 'text',
                'size' => 15    // in a grid, we need size
            ),
            'readonly' => true,
            'id' => 101
        ),
        array(
            'fullname' => 'IA.PROJECTED_BILLING_PERCENT_VARIANCE',
            'type' => array(
                'ptype' => 'percent',
                'format' => $gPercentFormat,
            ),
            'path' => 'BUDGETAMOUNTVARIANCE',
            'readonly' => true,
            'id' => 102
        ),
        array(
            'fullname' => 'IA.PROJECTED_COST_PERCENT_VARIANCE',
            'type' => array(
                'ptype' => 'percent',
                'format' => $gPercentFormat,
            ),
            'path' => 'BUDGETCOSTVARIANCE',
            'readonly' => true,
            'id' => 103
        ),
        array(
            'fullname' => 'IA.PROJECTED_DURATION_PERCENT_VARIANCE',
            'type' => array(
                'ptype' => 'percent',
                'format' => $gPercentFormat,
            ),
            'path' => 'BUDGETDURATIONVARIANCE',
            'readonly' => true,
            'id' => 104
        ),
        array(
            'fullname' => 'IA.CLASS_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'class',
                'pickentity' => 'classpick',
            ),
            'showInGroup' => true,
            'path' => 'CLASSID',
            'renameable' => true,
            'idw' => false,
            'id' => 105
        ),
        array(
            'fullname' => 'IA.CLASS_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'path' => 'CLASSNAME',
            'renameable' => true,
            'idw' => false,
            'id' => 106
        ),
        array(
            'fullname' => 'IA.CLASS_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'CLASSKEY',
            'id' => 107,
            'derived' => true
        ),
        array(
            'fullname'     => 'IA.CONTACT_RECORD_NUMBER',
            'required'     => false,
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'path' => 'CONTACTKEY',
            'id' => 108,
            'derived' => true
        ),
        array(
            'fullname'     => 'IA.BILLTO_CONTACT_RECORD_NUMBER',
            'required'     => false,
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'path' => 'BILLTOKEY',
            'id' => 109,
            'derived' => true
        ),
        array(
            'fullname'     => 'IA.SHIPTO_CONTACT_RECORD_NUMBER',
            'required'     => false,
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'path' => 'SHIPTOKEY',
            'id' => 110,
            'derived' => true
        ),
        array(
            'fullname' => 'IA.TIMESHEET_AND_EXPENSE_USER_RESTRICTIONS',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.USE_SYSTEM_CONFIGURATION_OPTION', 'IA.TIME_CAN_BE_ENTERED_BY_ANY_USER', 'IA.ONLY_USERS_ASSIGNED_TO_THE_PROJECT_CAN_ENTER', 'IA.ONLY_USERS_ASSIGNED_TO_THE_PROJECT_AND_TASK_CAN'),
                'validvalues' => array('System Default', 'Any User', 'Project Users', 'Project Task Users'),
                '_validivalues' => array('D', 'A', 'P', 'T'),
            ),

            'default' => 'System Default',
            'renameable' => true,
            'path' => 'USERRESTRICTIONS',
            'id' => 112
        ),
        array (
            'path'        => 'INVOICEWITHPARENT',
            'fullname'    => 'IA.INVOICE_WITH_PARENT',
            'type'        =>  $gBooleanType,
            'default'    => 'false',
            'id' => 113
        ),
        array (
            'path'        => 'BILLABLEEXPDEFAULT',
            'fullname'    => 'IA.BILLABLE_EMPLOYEE_EXPENSES',
            'type'        => $gBooleanType,
            'default'    => 'false',
            'id'        => 124
        ),
        array (
            'path'        => 'BILLABLEAPPODEFAULT',
            'fullname'    => 'IA.BILLABLE_AP_PO',
            'type'        => $gBooleanType,
            'default'    => 'false',
            'id'        => 125
        ),
        array(
            'fullname' => '',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => array('IA.USE_BILLING_RATE', 'IA.APPLY_COST_PLUS_FEE'),
                'validvalues' => array('Billing rate', 'Cost plus fee'),
                '_validivalues' => array('B', 'C')
            ),
            'path' => 'PRICINGOPTION',
            'id' => 116
        ),
        array(
            'fullname' => 'IA.FEE_PERCENTAGE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 5,
                'format' => $gDecimalFormat
            ),
            'path' => 'PRICINGFEE',
            'id' => 117
        ),
        array(
            'path' => 'BILLINGRATE',
            'fullname' => 'IA.DEFAULT_LABOR_RATE_FEE_PERCENTAGE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'id' => 118
        ),
        array(
            'path' => 'EXPENSERATE',
            'fullname' => 'IA.DEFAULT_EXPENSE_RATE_FEE_PERCENTAGE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'id' => 119,
        ),
        array(
            'path' => 'POAPRATE',
            'fullname' => 'IA.DEFAULT_AP_PO_RATE_FEE_PERCENTAGE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'id' => 120,
        ),
        array(
            'path' => 'BILLINGPRICING',
            'fullname' => 'IA.LABOR_PRICING_OPTION',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.BILLING_RATE', 'IA.COST_PLUS_FEE_1'),
                'validvalues' => array('Billing rate', 'Cost plus fee'),
                '_validivalues' => array('B', 'C')
            ),
            'showlabelalways' => true,
            'default' => 'Billing rate',
            'id' => 121
        ),
        array(
            'path' => 'EXPENSEPRICING',
            'fullname' => 'IA.EXPENSE_PRICING_OPTION',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.BILLING_RATE', 'IA.COST_PLUS_FEE_1'),
                'validvalues' => array('Billing rate', 'Cost plus fee'),
                '_validivalues' => array('B', 'C')
            ),
            'showlabelalways' => true,
            'default' => 'Cost plus fee',
            'id' => 122
        ),
        array(
            'path' => 'POAPPRICING',
            'fullname' => 'IA.AP_PO_PRICING_OPTION',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.COST_PLUS_FEE_1'), // Only 'IA.COST_PLUS_FEE' is allowed for PO
                'validvalues' => array('Cost plus fee'),
                '_validivalues' => array('C')
            ),
            'showlabelalways' => true,
            'default' => 'Cost plus fee',
            'id' => 123
        ),
        array(
            'path' => 'POCOMMITMENTS',
            'fullname' => 'IA.TOTAL_PO_COMMITMENTS',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15
            ),
            'showInGroup' => true,
            'readonly' => true,
            'id' => 128
        ),
        array(
            'path' => 'PREVENTTIMESHEET',
            'fullname' => 'IA.PREVENT_TIMESHEET_ENTRY',
            'type' => $gBooleanType,
            'default' => 'false',
            'renameable' => true,
            'id' => 130,
        ),
        array(
            'path' => 'PREVENTEXPENSE',
            'fullname' => 'IA.PREVENT_EXPENSE_ENTRY',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 131,
        ),
        array(
            'path' => 'PREVENTAPPO',
            'fullname' => 'IA.PREVENT_AP_PO_ENTRY',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 132,
        ),
        array(
            'path' => 'PREVENTGENINVOICE',
            'fullname' => 'IA.PREVENT_GENERATE_INVOICES',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 133,
        ),
        array(
            'fullname' => 'IA.ATTACHMENT',
            'type' => array(
                'ptype' => 'supdocptr',
                'type' => 'supdocptr',
                'maxlength' => 20,
            ),
            'desc' => 'IA.ATTACHMENT',
            'path' => 'SUPDOCID',
            'noedit' => false,
            'partialedit' => true,
            'id' => 134
        ),
        array (
            'path'         => 'INVOICEMESSAGE',
            'fullname'    => 'IA.INVOICE_MESSAGE',
            'type'         => array (
                'type' => 'text',
                'ptype' => 'multitext',
                'maxlength' => 4000,
                'showpopup' => true,
            ),
            'id' => 135
        ),
        array(
            'path' => 'INVOICECURRENCY',
            'fullname' => 'IA.INVOICE_CURRENCY_OVERRIDE',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'trxcurrencies',
            ),
            'showInGroup' => true,
            'noview' => true,
            'nonew' => true,
            'id' => 136
        ),
        array(
            'path' => 'BILLRATECURRENCY',
            'fullname' => 'IA.BILLING_RATE_CURRENCY',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'trxcurrencies',
            ),
            'showInGroup' => true,
            'noview' => true,
            'nonew' => true,
            'readonly' => true,
            'id' => 137,
        ),
        array(
            'path' => 'BUDGETCURRENCY',
            'fullname' => 'IA.CURRENCY',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'trxcurrencies',
            ),
            'showInGroup' => true,
            'noview' => true,
            'nonew' => true,
            'readonly' => true,
            'id' => 138,
        ),
        array(
            'path' => 'BILLINGOVERMAX',
            'fullname' => 'IA.IF_ACTUAL_BILLINGS_EXCEED_PROJECTED_BILLING_AMOUNT',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.DO_NOTHING', 'IA.ISSUE_A_WARNING_MESSAGE', 'IA.PREVENT_BILLING'),
                'validvalues' => array('Do nothing', 'Issue a warning message', 'Prevent billing'),
                '_validivalues' => array('N', 'W', 'P')
            ),
            'showlabelalways' => true,
            'default' => 'Do nothing',
            'id' => 139,
        ),
        array (
            'path'        => 'EXCLUDEEXPENSES',
            'fullname'    => 'IA.EXCLUDE_EXPENSES',
            'type'        =>  $gBooleanType,
            'default'    => 'false',
            'id' => 140,
        ),
        array(
            'fullname' => 'IA.CONTRACT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'CONTRACTKEY',
            'id' => 141,
        ),
        array(
            'fullname' => 'IA.CONTRACT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'contract',
                'pickentity' => 'contractpick',
            ),
            'showInGroup' => true,
            'path' => 'CONTRACTID',
            'renameable' => true,
            'readonly' => true,
            'id' => 142,
        ),
        // the following two fields are used on a popup to ask user about adjusting project/task/resource dates
        array(
            'path' => 'SHIFTTASKDATES',
            'fullname' => 'IA.SHIFT_ALL_TASK_AND_RESOURCE_ASSIGNMENT_DATES',
            'type' => $gBooleanType,
            'default' => 'true',
            'id' => 143,
        ),
        array(
            'path' => 'SHIFTENDDATE',
            'fullname' => 'IA.SHIFT_PROJECT_END_DATE',
            'type' => $gBooleanType,
            'default' => 'true',
            'id' => 144,
        ),
        array(
            'fullname' => 'IA.ROOT_PROJECT_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'project',
                'pickentity' => 'projectpick',
            ),
            'path' => 'ROOTPARENTID',
            'readonly' => true,
            'id' => 146
        ),
        array(
            'fullname' => 'IA.ROOT_PROJECT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'ROOTPARENTKEY',
            'readonly' => true,
            'renameable' => true,
            'id' => 147,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.ROOT_PROJECT_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'path' => 'ROOTPARENTNAME',
            'readonly' => true,
            'renameable' => true,
            'id' => 148,
            'derived' => true,
        ),
        array(
            'fullname' => 'IA.ALN',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 255,
            ),
            'showInGroup' => true,
            'path' => 'CFDA',
            'renameable' => true,
            'id' => 149
        ),
        array(
            'fullname' => 'IA.FUNDED_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 255,
            ),
            'showInGroup' => true,
            'path' => 'FUNDEDNAME',
            'renameable' => true,
            'id' => 150
        ),
        array(
            'fullname' => 'IA.AGENCY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 255,
            ),
            'showInGroup' => true,
            'path' => 'AGENCY',
            'renameable' => true,
            'id' => 151
        ),
        array(
            'fullname' => 'IA.PAYER',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('', 'IA.FEDERAL', 'IA.THIRD_PARTY'),    // For now validlabels and validvalues have to match coz when it'IA.S_READONLY_THE_UI_LAYER_DOESN't translate the value
                'validvalues' => array('', 'Federal', 'Third-party'),
                '_validivalues' => array('', 'F', 'T'),
            ),
            'path' => 'PAYER',
            'showInGroup' => true,
            'id' => 152
        ),
        array(
            'fullname' => 'IA.OTHER_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 255,
            ),
            'showInGroup' => true,
            'path' => 'FUNDINGOTHERID',
            'renameable' => true,
            'id' => 153
        ),
        array(
            'fullname' => 'IA.ASSISTANCE_PROVIDED',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('', 'IA.CASH', 'IA.NON_CASH'),    // For now validlabels and validvalues have to match coz when it'IA.S_READONLY_THE_UI_LAYER_DOESN't translate the value
                'validvalues' => array('', 'Cash', 'Non-Cash'),
                '_validivalues' => array('', 'C', 'N'),
            ),
            'path' => 'ASSISTANCETYPE',
            'showInGroup' => true,
            'id' => 154
        ),
        array(
            'fullname' => 'IA.REVENUE_RESTRICTION',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('', 'IA.PURPOSE', 'IA.TIME', 'IA.NA'),    // For now validlabels and validvalues have to match coz when it'IA.S_READONLY_THE_UI_LAYER_DOESN't translate the value
                'validvalues' => array('', 'Purpose', 'Time','NA'),
                '_validivalues' => array('', 'P', 'T', 'N'),
            ),
            'path' => 'REVRESTRICTION',
            'showInGroup' => true,
            'id' => 155
        ),
        array(
            'fullname' => 'IA.RESTRICTION_EXPIRATION_YEARS',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 80,
            ),
            'showInGroup' => true,
            'path' => 'RESTRICTIONEXPIRY',
            'renameable' => true,
            'id' => 156
        ),
        array(
            'fullname' => 'IA.FIRST_RESTRICTION_EXPIRATION_DATE',
            'type' => $gDateType,
            'desc' => 'IA.DATE',
            'path' => 'RESTRICTIONEXPIRATIONDATE',
            'id' => 157
        ),
        array(
            'path' => 'TIMESATISFACTIONSCHEDULED',
            'fullname' => 'IA.TIME_SATISFACTION_SCHEDULED',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 158,
        ),
        array(
            'path'     => 'SHOWALLROOTPROJECTDOCS',
            'fullname' => 'IA.SHOW_ALL',
            "noLabel" => true,
            'type'     => array(
                'ptype'       => 'checkbox',
                'type'        => 'checkbox',
                'validlabels' => array( 'IA.SHOW_ALL' ),
                'validvalues' => array( 'T'),
            ),
            'id' => 159,
        ),
        [
            'path'      => 'FINSUMMARYGLBUDGET',
            'fullname'  => 'IA.FINANCIAL_SUMMARY_GL_BUDGET',
            'type'      => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'budgetheader',
                'pickfield' => ['BUDGETID', 'RECORDNO'],
                'restrict' => array(
                    array('pickField' => 'ISCONSOLIDATED', 'value' => 'false'),
                ),
            ],
            'nonew'  => true,
        ],
        [
            'path'      => 'ACCTGROUPFORBUDGET',
            'fullname'  => 'IA.ACCOUNT_GROUP_FOR_BUDGET',
            'type'      => [
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => [
                    'IA.TOTAL_PAYMENTS',
                    'IA.TOTAL_DEFERRED_REVENUE',
                    'IA.TOTAL_REVENUE',
                    'IA.TOTAL_EXPENSES',
                    'IA.TOTAL_COST_OF_GOODS',
                    'IA.TOTAL_WAGES',
                    'IA.GROSS_PROFIT',
                    'IA.NET_INCOME_LOSS',
                ],
                'validvalues' => [
                    'TOTALPAYMENTS',
                    'TOTALDEFERREDREVENUE',
                    'TOTALREVENUE',
                    'TOTALEXPENSES',
                    'TOTALCOST',
                    'TOTALWAGES',
                    'TOTALGROSSPROFIT',
                    'TOTALNETINCOME',
                ],
            ],
            'showlabelalways' => true,
        ],
        [
            'path' => 'FS_BUDGETAMOUNT',
            'fullname' => 'IA.BUDGET',
            'type' => [
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15,   // in a grid, we need size
            ],
            'showInGroup' => true,
            'readonly' => true,
        ],
        [
            'path' => 'FS_BUDGETDIFF',
            'fullname' => 'IA.BUDGET_DIFFERENCE',
            'type' => [
                'ptype' => 'currency',
                'type' => 'currency',
                'format' => $gCurrencyFormat,
                'size' => 15,   // in a grid, we need size
            ],
            'showInGroup' => true,
            'readonly' => true,
        ],
        [
            'fullname' => 'IA.ROLLUP_PROJECT_KEY',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
            'path' => 'ROLLUP_PROJ_KEY',
        ],
        array(
            'fullname' => 'IA.ROLLUP_PROJECT_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'path' => 'ROLLUPPROJECTID',
            'readonly' => true
        ),
        array(
            'fullname' => 'IA.ROLLUP_PROJECT_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'path' => 'ROLLUPPROJECTNAME',
            'readonly' => true,
            'renameable' => true,
            'derived' => true,
        ),
        [
            'fullname' => 'IA.EXCLUDE_WIP_SCHEDULE',
            'type'  =>  $gBooleanType,
            'path' => 'WIPEXCLUDE',
            'default' => 'true',
        ],
        [
            'fullname' => 'IA.ME_LOCATION_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'hidden' => true,
            'path' => 'MELOCATIONKEY',
            'renameable' => true,
            'id' => 270,
            'derived' => true,
        ],
        [
            'fullname' => 'IA.ME_LOCATION_ID',
            'showInGroup' => true,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'pickentity' => 'locationpick',
                'pickfield' => array('PICKID', 'LOC_CURRENCY'),
                'entity' => 'location',
                'maxlength' => 40,
            ),
            'path' => 'MELOCATIONID',
            'renameable' => true,
            'idw' => false,
            'id' => 271
        ],
        [
            'path' => 'MELOCATIONNAME',
            'fullname' => 'IA.ME_LOCATION_NAME',
            'renameable' => true,
            'idw' => false,
            'id' => 272
        ],
        $gSiUuidFieldInfo,
        [
            'fullname' => 'IA.SCOPE',
            'type' => [
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 4000,
            ],
            'numofrows' => 6,
            'numofcols' => 80,
            'showInGroup' => true,
            'path' => 'SCOPE',
            'id' => 273
        ],
        [
            'fullname' => 'IA.INCLUSIONS',
            'type' => [
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 4000,
            ],
            'numofrows' => 6,
            'numofcols' => 80,
            'showInGroup' => true,
            'path' => 'INCLUSIONS',
            'id' => 274
        ],
        [
            'fullname' => 'IA.EXCLUSIONS',
            'type' => [
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 4000,
            ],
            'numofrows' => 6,
            'numofcols' => 80,
            'showInGroup' => true,
            'path' => 'EXCLUSIONS',
            'id' => 275
        ],
        [
            'fullname' => 'IA.TERMS',
            'type' => [
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 4000,
            ],
            'numofrows' => 6,
            'numofcols' => 80,
            'showInGroup' => true,
            'path' => 'TERMS',
            'id' => 276
        ],
        [
            'fullname' => 'IA.SCHEDULED_START_DATE',
            'type' => $gDateType,
            'path' => 'SCHEDULESTARTDATE',
            'showInGroup' => true,
            'id' => 277
        ],
        [
            'fullname' => 'IA.ACTUAL_START_DATE',
            'type' => $gDateType,
            'path' => 'ACTUALSTARTDATE',
            'showInGroup' => true,
            'id' => 278
        ],
        [
            'fullname' => 'IA.SCHEDULED_COMPLETION_DATE',
            'type' => $gDateType,
            'path' => 'SCHEDULEDCOMPLETIONDATE',
            'showInGroup' => true,
            'id' => 279
        ],
        [
            'fullname' => 'IA.REVISED_COMPLETION_DATE',
            'type' => $gDateType,
            'path' => 'REVISEDCOMPLETIONDATE',
            'showInGroup' => true,
            'id' => 280
        ],
        [
            'fullname' => 'IA.SUBSTANTIAL_COMPLETION_DATE',
            'type' => $gDateType,
            'path' => 'SUBSTANTIALCOMPLETIONDATE',
            'showInGroup' => true,
            'id' => 281
        ],
        [
            'fullname' => 'IA.ACTUAL_COMPLETION_DATE',
            'type' => $gDateType,
            'path' => 'ACTUALCOMPLETIONDATE',
            'showInGroup' => true,
            'id' => 282
        ],
        [
            'fullname' => 'IA.NOTICE_TO_PROCEED',
            'type' => $gDateType,
            'path' => 'NOTICETOPROCEED',
            'showInGroup' => true,
            'id' => 283
        ],
        [
            'fullname' => 'IA.RESPONSE_DUE',
            'type' => $gDateType,
            'path' => 'RESPONSEDUE',
            'showInGroup' => true,
            'id' => 284
        ],
        [
            'fullname' => 'IA.EXECUTED_ON',
            'type' => $gDateType,
            'path' => 'EXECUTEDON',
            'showInGroup' => true,
            'id' => 285
        ],
        [
            'fullname' => 'IA.SCHEDULE_IMPACT',
            'path' => 'SCHEDULEIMPACT',
            'type'=>[
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
                'size' => 100,
            ],
            'id' => 286,
        ],
        [
            'path'     => 'SHOW_ALL_ROOT_PROJECT_CHANGE_ORDERS',
            'fullname' => 'IA.SHOW_ALL',
            "noLabel" => true,
            'type'     => [
                'ptype'       => 'checkbox',
                'type'        => 'checkbox',
                'validlabels' => [ 'IA.SHOW_ALL' ],
                'validvalues' => [ 'T' ],
            ],
            'id' => 287,
        ],
        [
            'path'     => 'SHOW_ALL_ROOT_CHANGE_REQUESTS',
            'fullname' => 'IA.SHOW_ALL',
            "noLabel" => true,
            'type'     => [
                'ptype'       => 'checkbox',
                'type'        => 'checkbox',
                'validlabels' => [ 'IA.SHOW_ALL' ],
                'validvalues' => [ 'T' ],
            ],
            'id' => 288,
        ],
        [
            'path'     => 'SHOW_ALL_ROOT_PROJECT_CONTRACTS',
            'fullname' => 'IA.SHOW_ALL',
            "noLabel" => true,
            'type'     => [
                'ptype'       => 'checkbox',
                'type'        => 'checkbox',
                'validlabels' => [ 'IA.SHOW_ALL' ],
                'validvalues' => [ 'T' ],
            ],
            'id' => 289,
        ],
        [
            'path'     => 'SHOW_ALL_ROOT_PROJECT_INVOICES',
            'fullname' => 'IA.SHOW_ALL',
            "noLabel" => true,
            'type'     => [
                'ptype'       => 'checkbox',
                'type'        => 'checkbox',
                'validlabels' => [ 'IA.SHOW_ALL' ],
                'validvalues' => [ 'T' ],
            ],
            'id' => 291,
        ],
        [
            'path'     => 'SHOW_ALL_ROOT_RETAINAGE_RELEASE_INVOICES',
            'fullname' => 'IA.SHOW_ALL',
            "noLabel" => true,
            'type'     => [
                'ptype'       => 'checkbox',
                'type'        => 'checkbox',
                'validlabels' => [ 'IA.SHOW_ALL' ],
                'validvalues' => [ 'T' ],
            ],
            'id' => 292,
        ],
        [
            'path'     => 'SHOW_ALL_ROOT_PAYMENTS_CREDITS_AND_ADJUSTMENTS',
            'fullname' => 'IA.SHOW_ALL',
            "noLabel" => true,
            'type'     => [
                'ptype'       => 'checkbox',
                'type'        => 'checkbox',
                'validlabels' => [ 'IA.SHOW_ALL' ],
                'validvalues' => [ 'T' ],
            ],
            'id' => 293,
        ],
    ),
    'table' => 'project',
    'printas' => 'IA.PROJECT',
    'pluralprintas' => 'IA.PROJECTS',
    'sicollaboration' => true,
    'vid' => 'PROJECTID',
    'module' => 'ar',
    'module_list' => array('ar','pa'),
    'renameable' => true,
    'auditcolumns' => true,
    'upsertEntriesPaths' => ['PROJECT_OBSPCTCOMPLETED'],
    'autoincrement' => 'RECORDNO',
    //'supdocentity' => 'PROJECT',
    'showhierarchy' => true,
    'customerp' => array(
        'SLTypes' => array(
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKWORKFLOW
        ),
        'SLEvents' => array(
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK
        ),
        'AllowCF' => true,
    ),
    'pairedFields' => array(
        'PARENTID' => 'PARENTNAME',
        'CUSTOMERID' => 'CUSTOMERNAME',
        'DEPARTMENTID' => 'DEPARTMENTNAME',
        'LOCATIONID' => 'LOCATIONNAME',
        'MANAGERID' => 'MANAGERCONTACTNAME',
        'SALESCONTACTID' => 'SALESCONTACTNAME',
        'CLASSID' => 'CLASSNAME',
        'ROOTPARENTID' => 'ROOTPARENTNAME',
        'ROLLUPPROJECTID' => 'ROLLUPPROJECTNAME',
    ),
    'allowDDS' => true,
    'platformProperties' => array(
        FIELD_IS_GL_DIMENSION => true,
        FIELD_LOOKUP_TEMPLATE => '{!PROJECTID!}--{!NAME!}'
    ),
    'api' => [
        'LESS_GET_FIELDS' => [
            'PROJECT_OBSPCTCOMPLETED'
        ]
    ],
    'audittrail_cache_add' => true,
    'audittrail_cache_set' => true,
    'audittrail_unneeded_fields' => array(
        'DFLTBILLINGPRICING'=>'','DFLTEXPENSEPRICING'=>'','DFLTPOAPPRICING'=>'','PROJECTSUMMARYWRNG'=>'',
        'PROJECT_LABEL' => '','BILLINGRATE_LEGEND' => '','EXPENSERATE_LEGEND' => '','POAPRATE_LEGEND' => '',
    ),
    SearchTable::GSALLOWED => true,
    SearchTable::GSDEFAULTFILTERFIELD => 'NAME',
    SearchTable::GSANYFIELD => 'PROJECTID',
    SearchTable::GSCOLUMNS => [ 'PROJECTID', 'NAME', 'PROJECTSTATUS', 'CUSTOMERNAME', 'BEGINDATE', 'ENDDATE',
                                'PROJECTTYPE'],
    SearchTable::GSFIELDEXCLUSIONS => [ 'PREVENTTIMESHEET', 'PROJECT_RESOURCES', ],
    'description' => 'IA.INFORMATION_ABOUT_EACH_PROJECT_DESC',
    'nameFields' => [ 'PROJECTID', 'LOCATIONID' ],
);
