#Makefile.in
#

ENTITY_XMLS=                            \
    $(EMPTY)

ENTITY_ENTS=                            \
    approvetimesheet.ent                \
    approvetimesheetentry.ent           \
    employeeoutofoffice.ent             \
    employeepositionskill.ent           \
    expensebilling.ent                  \
    mytimesheet.ent                     \
    mytimesheetentry.ent                \
    outofoffice.ent                     \
    obspctcompletedproject.ent          \
    obspctcompletedtask.ent             \
    positionskill.ent                   \
    project.ent                         \
    projectgroup.ent                    \
    projectgrpmember.ent                \
    projectngrouppick.ent               \
    projectresources.ent                \
    projectstatus.ent                   \
    projecttransactionrule.ent          \
    projecttype.ent                     \
    task.ent                            \
    taskgroup.ent                       \
    taskgrpmember.ent                   \
    taskngrouppick.ent                  \
    taskpick.ent                        \
    taskresources.ent                   \
    timesheet.ent                       \
    timesheetapproval.ent               \
    timesheetentry.ent                  \
    timetype.ent                        \
    transactionrule.ent                 \
    transactionruledetail.ent           \
    tsmgmtemail.ent                     \
    tsremployee.ent                     \
    tsrules.ent                         \
    pasetup.ent                         \
    projectpick.ent                     \
    timesheethelper.ent					\
    timesheetentryhelper.ent			\
    obspctcompleted.ent                 \
    wipreliefrun.ent                    \
    $(EMPTY)

QUERY_OBJECTS=$(ENTITY_ENTS:.ent=.qry)

LAYOUT_XMLS=                            \
	$(EMPTY)

LAYOUT_PHPS=$(LAYOUT_XMLS:.xml=.php)

LAYOUT_XSLS=$(LAYOUT_XMLS:.xml=.xsl)

include ../Makefile.in
