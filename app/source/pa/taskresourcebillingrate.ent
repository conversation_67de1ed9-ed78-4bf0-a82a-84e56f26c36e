<?php
/**
 * Entity for Task Resource Cost Rate
 * This entity holds billing rates for a Task Resource (taskresources object) based on infrmation in corresponding
 * Project Resources (projectresources object).  The relationship between the two objects is determined by project ID,
 * employee ID, item ID and task resource's planned start date vs. project start date.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation - All Rights Reserved
 */
$kSchemas['taskresourcebillingrate'] = array(
    'object' => array(
        'TASKRESOURCEKEY',
        'PRJITEMKEY',
        'PRJSTARTDATE',
        'BILLINGRATE',
        'EXPENSERATE',
        'POAPRATE',
    ),
    'schema' => array(
        'TASKRESOURCEKEY'   => 'taskresourcekey',
        'PRJITEMKEY'        => 'prjitemkey',
        'PRJSTARTDATE'      => 'prjstartdate',
        'BILLINGRATE'       => 'billingrate',
        'EXPENSERATE'       => 'expenserate',
        'POAPRATE'          => 'poaprate',
    ),
    'publish' => array(
        'PRJITEMKEY',
        'PRJSTARTDATE',
        'BILLINGRATE',
        'EXPENSERATE',
        'POAPRATE',
    ),
    'fieldinfo' => array(
        array(
            'fullname' => 'IA.TASK_RESOURCE_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'path' => 'TASKRESOURCEKEY',
            'derived' => true,
            'hidden' => true,
        ),
        array(
            'path' => 'BILLINGRATE',
            'fullname' => 'IA.LABOR_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 8,
                'size' => 20,
            )
        ),
        array(
            'path' => 'EXPENSERATE',
            'fullname' => 'IA.EXPENSE_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 8,
                'size' => 20,
            )
        ),
        array(
            'path' => 'POAPRATE',
            'fullname' => 'IA.AP_PO_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 8,
                'size' => 20,
            )
        ),
        array(
            'path' => 'PRJSTARTDATE',
            'fullname' => 'IA.RATE_START_DATE',
            'type' => array(
                'ptype' => 'date',
                'type' => 'date',
                'maxlength' => 12,
                'size' => 20,
                'format' => $gDateFormat
            ),
        ),
        array(
            'path' => 'PRJITEMKEY',
            'fullname' => 'IA.RATE_ITEM_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
        ),
    ),
    'table' => 'v_taskresourcebillingrate',
    'printas' => 'IA.TASK_RESOURCE_BILLING_RATES',
    'pluralprintas' => 'IA.TASK_RESOURCE_BILLING_RATES',
    'vid' => 'TASKRESOURCEKEY',
    'module' => 'pa',
    'description' => 'IA.LSIT_OF_EMPLOYEES_ASSIGNED_TO_EACH_TASK_AND_THE',
);
