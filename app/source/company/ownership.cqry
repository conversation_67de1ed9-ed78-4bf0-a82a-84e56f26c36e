<?

$kownershipQueries['QRY_OWNERSHIP_SELECT_SINGLE_DISTRIBUTE_VID'] = array(
		'QUERY' => "SELECT ownership.record#,ownership.ownerkey,owner.eqglaccount,owner.eqglaccountlabel,glaccount.acct_no,glaccount.title, accountlabel.label,ownership.percentowned,ownership.locationkey,VENDOR.NAME, VENDOR.VENDORID, VENDOR.CREDITLIMIT, VENDOR.TOTALDUE, VENDOR.ONHOLD FROM ownership ownership,vendor vendor, owner owner, glaccount glaccount, accountlabel accountlabel WHERE (ownership.locationkey =  ? ) and ownership.ownerkey = vendor.record#  (+)  and vendor.cny# (+) = ?  and owner.holddist != 'T' and vendor.record# = owner.vendorkey(+) and owner.cny#(+) = ? and ownership.cny# (+) = ? and owner.eqglaccount = glaccount.record# and glaccount.cny# = ? and owner.eqglaccountlabel = accountlabel.record#(+) and accountlabel.cny#(+)=? ",
		'ARGTYPES' => array('integer', 'integer', 'integer', 'integer', 'integer', 'integer'),
);
$kownershipQueries['QRY_NONMEGA_OWNERSHIP_SELECT_SINGLE_DISTRIBUTE_VID'] = array(
		'QUERY' => "SELECT ownership.record#,ownership.ownerkey, owner.eqglaccount, owner.eqglaccountlabel, glaccount.acct_no,glaccount.title,accountlabel.label,ownership.percentowned,ownership.locationkey,VENDOR.NAME, VENDOR.VENDORID, VENDOR.CREDITLIMIT, VENDOR.TOTALDUE, VENDOR.ONHOLD FROM ownership ownership,vendor vendor, owner owner, glaccount glaccount, accountlabel accountlabel WHERE ownership.ownerkey = vendor.record#  (+)  and vendor.cny# (+) = ?  and owner.holddist != 'T' and vendor.record# = owner.vendorkey(+) and owner.cny#(+) = ? and ownership.cny# (+) = ? and owner.eqglaccount = glaccount.record# and glaccount.cny# = ? and owner.eqglaccountlabel = accountlabel.record#(+) and accountlabel.cny#(+)=? ",
		'ARGTYPES' => array('integer', 'integer', 'integer', 'integer', 'integer'),
);
$kownershipQueries['QRY_NONMEGA_OWNERSHIP_SELECT_SINGLE_VID'] = array(
		'QUERY' => "SELECT ownership.record#,ownership.ownerkey,ownership.percentowned,ownership.locationkey,VENDOR.NAME,VENDOR.VENDORID,OWNER.EQGLACCOUNT,OWNER.EQGLACCOUNTLABEL,owneraccountlabel.label,owneraccount.acct_no,owneraccount.title FROM ownership ownership,vendor vendor,owner owner,accountlabel owneraccountlabel,glaccount owneraccount WHERE ownership.ownerkey = vendor.record#  (+)   and vendor.cny# (+) = ?  and ownership.ownerkey = owner.vendorkey  (+)   and owner.cny# (+) = ?  and owner.eqglaccountlabel = owneraccountlabel.record#  (+)   and owneraccountlabel.cny# (+) = ?  and owner.eqglaccount = owneraccount.record#  (+)   and owneraccount.cny# (+) = ?  and ownership.cny# (+) = ? ORDER BY VENDOR.VENDORID ",
		'ARGTYPES' => array('integer', 'integer', 'integer', 'integer', 'integer'),
);

$kownershipQueries['QRY_NONMEGA_OWNERSHIP_DELETE_VID'] = array(
		'QUERY' => "DELETE FROM ownership WHERE cny# =? ",
		'ARGTYPES' => array('integer' ,'integer' ),
);

$kownershipQueries['QRY_GET_OWNERSHIP_VENDOR_LOCATIONS'] = array(
		'QUERY' => "SELECT locationkey FROM  ownership WHERE ownerkey = ? AND cny# = ? ",
		'ARGTYPES' => array('text','integer'),
);