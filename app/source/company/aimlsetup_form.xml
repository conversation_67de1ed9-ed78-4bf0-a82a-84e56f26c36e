<?xml version='1.0' encoding='UTF-8'?>
<ROOT>
    <entity>aimlsetup</entity>
    <title>IA.CONFIGURE_AI_SERVICE</title>
    <view system="true">
        <events>
            <load>AIMLSetupLoad();</load>
        </events>
        <pages>
            <page className="columnSetupPadding" title="IA.AI_SERVICES">
                <section title="IA.AI_SERVICES" isCollapsible="true" id="ai_settings">
                    <subsection className="subSection qx-cfg-subsection">
                        <field>
                            <path>ENABLE_DATAPIPELINE</path>
                            <events>
                                <change>
                                    toggleAiServices(this.meta.value);
                                </change>
                            </events>
                        </field>
                    </subsection>
                </section>
                <section title="IA.SAGE_COPILOT" isCollapsible="true" id="copilot_settings">
                    <subsection className="subSection qx-cfg-subsection">
                        <field hidden="true">
                            <path>BUDGET_INSIGHT_ENABLED</path>
                        </field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection">
                        <field hidden="true">
                            <path>COMPANY_ASSISTS_ENABLED</path>
                        </field>
                    </subsection>
                </section>
                <section title="IA.OUTLIER_DETECTION" isCollapsible="true" id="outlier_settings">
                    <subsection className="subSection qx-cfg-subsection">
                        <field>
                            <path>GLOD_ENABLED</path>
                            <default>true</default>
                            <infoText>IA.WE_WILL_START_BUILDING_YOUR_DATA_MODEL</infoText>
                            <events>
                                <change>
                                    showHideGLODControls();
                                </change>
                            </events>
                        </field>
                        <subsection className="subSection qx-cfg-subsection" id="outlier_status">
                            <row>
                                <field>
                                    <path>GLOD_MODELREADY</path>
                                </field>
                            </row>
                        </subsection>
                    </subsection>
                </section>
            </page>
        </pages>
    </view>
    <helpfile></helpfile>
</ROOT>
