<?php
//===========================================================================
//	FILE:			IndustryManager.cls
//	AUTHOR:			Senthil
//	DESCRIPTION:
//
//	(C) 2000, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	Corporation and is protected by the copyright laws.  Information
//	herein may not be used, copied or disclosed in whole or in part
//	without prior written consent from Intacct Corporation.
//===========================================================================

/**
 * Class IndustryManager
 */
class IndustryManager extends EntityManager
{
    /**
     * @param array $values
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "IndustryManager::Add";
        global $gErr;

        $ok = $this->_QM->beginTrx($source);
        $ok = $ok && parent::regularAdd($values);
        if($values['COPYFROM']) {
            $ok = $ok && $this->CopyFromIndustry($values);
        }
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = "Could not create Industry record!";
            $gErr->addIAError('CO-0751', __FILE__ . ":" . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        
        return $ok;
    }

    /**
     * @param string[][] $values
     * @return bool
     */
    function CopyFromIndustry($values)
    {
        include_once 'IAObjectsSync.cls';
        $syncObj = new IAObjectsSync();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $kDBSchemas = $syncObj->GetSchemas();

        $ok = true;
        /** @noinspection PhpUndefinedVariableInspection */
        $iaObjects = array_keys($kIAObjects);

        $newRecNo = array();
        $ignoreTables = array('INDUSTRYCODE', 'IAGLACCTGRPMEMBERS', 'IACOACATMEMBERS', 'IACOMPGRPMEMBERS',
                                'IAREPORTGROUPS', 'IAREPORTROWS', 'IAREPORTCOMPS', 'IAREPORTCOLS', 
                                'IAREPORTFORMAT', 'IABEANS', 'IAPACKAGE', 'IAKPICOMPONENT');

        foreach ($iaObjects as $object) {
            
            // skip industry table
            if( in_array($object,  $ignoreTables)) {
                continue;
            }
            $colNames = $syncObj->AllRecFields($object);

            // skip all child tables
            if(!isl_strstr($colNames, 'INDUSTRYCODE')) {
                continue;
            }

            // get values to be inserted
            $res = QueryResult(array("SELECT " .$colNames. " FROM $object WHERE industrycode=:1",$values['COPYFROM']));
            if (strstr($colNames, 'RECORD#')) {
                $maxNo = QueryResult("SELECT max(abs(RECORD#)) RECNO FROM $object");
                $maxNo = $maxNo[0]["RECNO"];
            }

            foreach ($res as $resvalue) {
                $insertValues = array();                    
                foreach ($syncObj->iaObjFields[$object] as $key => $fieldName) {

                    if ($fieldName == 'INDUSTRYCODE') {
                        $value = $values['CODE'];
                    } else if ($fieldName == 'RECORD#') {
                        /** @noinspection PhpUndefinedVariableInspection */
                        $maxNo += 1;
                        $value = $maxNo;
                        if ($resvalue[$fieldName] < 0) {
                            $value = $value * -1;
                        }

                        $newRecNo[$object][$resvalue[$fieldName]] = $value;
                    } else {
                        $value = $resvalue[$fieldName];
                    }
                    $value = $syncObj->ApplyMarkup($object, $key, $value);

                    $insertValues[] = $value;
                }

                // to make comma separated values
                $insVal = join(',', $insertValues);

                // final insert statement
                $insStr = "insert into $object (".$colNames.") values ($insVal)";

                $ok = $ok && ExecSimpleStmt($insStr, 0);
                if (!$ok) {
                    return false;
                }
            }
        }


        // insert all children tables with new parent# and record#

        $iaglacctgrpmembers = "insert into iaglacctgrpmembers (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) (
									select 
										(select nvl(max(record#),0) as max from iaglacctgrpmembers)+rownum a1,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.parent#)) a2,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.child#)) a3,
										sortord a4,
										:1
									from 
										iaglacctgrpmembers childtab
									where 
										exists (select 1 from iaglacctgrp where industrycode=:2 and record#= childtab.parent#))";
        
        $ok = $ok && ExecStmt(array($iaglacctgrpmembers,$values['CODE'],$values['COPYFROM']), 0);


        $iacoacatmembers = "insert into iacoacatmembers (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) (
									select 
										(select nvl(max(record#),0) as max from iacoacatmembers)+rownum a1,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.parent#)) a2,
										(select record# from iacoacat where industrycode=:1 and name=(select name from iacoacat where record#= childtab.categorykey)) a3,
										sortord a4,
										:1
									from 
										iacoacatmembers childtab
									where 
										exists (select 1 from iaglacctgrp where industrycode=:2 and record#= childtab.parent#))";
        
        $ok = $ok && ExecStmt(array($iacoacatmembers,$values['CODE'],$values['COPYFROM']), 0);

        $iacompgrpmembers = "insert into iacompgrpmembers (RECORD#,PARENTKEY,LHSACCTGRPKEY,LHSCONST,OPERATOR,RHSACCTGRPKEY,RHSCONST,PRECISION,DISPLAYAS,UOM,INDUSTRYCODE) (
									select 
										(select nvl(max(record#),0) as max from iacompgrpmembers)+rownum a1,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.parentkey)) a2,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.lhsacctgrpkey)) a3,
										lhsconst a4,
										operator a5,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.rhsacctgrpkey)) a6,
										rhsconst a7,
										precision a8,
										displayas a9,
										uom a10,
										:1
									from 
										iacompgrpmembers childtab
									where 
										exists (select 1 from iaglacctgrp where industrycode=:2 and record#= childtab.parentkey))";
        
        $ok = $ok && ExecStmt(array($iacompgrpmembers,$values['CODE'],$values['COPYFROM']), 0);

        $iareportgroups = "insert into iareportgroups (REPORT#,SORTORD,ACCTGRP#,INDUSTRYCODE) (
									select 
										(select record# from iareportinfo where industrycode=:1 and (name,category) in (select name,category from iareportinfo where record#= childtab.report#)) a1,
										sortord a2,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.acctgrp#)) a3,
										:1
									from 
										iareportgroups childtab
									where 
										exists (select 1 from iareportinfo where industrycode=:2 and record#= childtab.report#))";
        
        $ok = $ok && ExecStmt(array($iareportgroups,$values['CODE'],$values['COPYFROM']), 0);

        $iareportrows = "insert into iareportrows (REPORT#, PATH, SUMMARY, EXPANDBY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE,INDUSTRYCODE) (
									select 
										(select record# from iareportinfo where industrycode=:1 and (name,category) in (select name,category from iareportinfo where record#= childtab.report#)) a1,
										PATH, SUMMARY, EXPANDBY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, 
										ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.operandgrpkey)) a3,
										OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, :1
									from 
										iareportrows childtab
									where 
										exists (select 1 from iareportinfo where industrycode=:2 and record#= childtab.report#))";
        
        $ok = $ok && ExecStmt(array($iareportrows,$values['CODE'],$values['COPYFROM']), 0);

        $iareportcomps = "insert into iareportcomps (REPORT#,PATH,SORTORD,OPERANDGRPKEY,OPERANDCONSTANT,OPERATION,INDUSTRYCODE) (
									select 
										(select record# from iareportinfo where industrycode=:1 and (name,category) in (select name,category from iareportinfo where record#= childtab.report#)) a1,
										PATH,SORTORD,
										(select record# from iaglacctgrp where industrycode=:1 and name=(select name from iaglacctgrp where record#= childtab.operandgrpkey)) a3,
										OPERANDCONSTANT,OPERATION, :1
									from 
										iareportcomps childtab
									where 
										exists (select 1 from iareportinfo where industrycode=:2 and record#= childtab.report#))";
        
        $ok = $ok && ExecStmt(array($iareportcomps,$values['CODE'],$values['COPYFROM']), 0);
        
        $iareportcols = "insert into iareportcols (REPORT#,SORTORD,PERIOD#,DATETYPE,HEADERCOMMENT,SUBCOLUMNS,COMPAREBY,EXPANDBY,COLHDR1,COLHDR2,BUDGETKEYS,TYPE,BUDGETKEY,SHOWAS,PRECISION,
							TITLE,PERIODOFFSET,COLFONT,COLPGBREAK,COLWIDTH,EXPOF,SUMMARYOP,SUMMARYCOLS,COLCOMPVALUE,SUMMARYSTR,COLHIDE,COMPONCOL,COLAMOUNTTYPE,INDUSTRYCODE) (
									select 
										(select record# from iareportinfo where industrycode=:1 and (name,category) in (select name,category from iareportinfo where record#= childtab.report#)) a1,
										SORTORD,PERIOD#,DATETYPE,HEADERCOMMENT,SUBCOLUMNS,COMPAREBY,EXPANDBY,COLHDR1,COLHDR2,BUDGETKEYS,TYPE,BUDGETKEY,SHOWAS,PRECISION,
										TITLE,PERIODOFFSET,COLFONT,COLPGBREAK,COLWIDTH,EXPOF,SUMMARYOP,SUMMARYCOLS,COLCOMPVALUE,SUMMARYSTR,COLHIDE,COMPONCOL,COLAMOUNTTYPE, :1
									from 
										iareportcols childtab
									where 
										exists (select 1 from iareportinfo where industrycode=:2 and record#= childtab.report#))";
        
        $ok = $ok && ExecStmt(array($iareportcols,$values['CODE'],$values['COPYFROM']), 0);

        $iareportformat = "insert into iareportformat (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME,
							NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, 
							RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, APPLYREPFILTERONLY, INDUSTRYCODE) (
									select 
										(select record# from iareportinfo where industrycode=:1 and (name,category) in (select name,category from iareportinfo where record#= childtab.report#)) a1,
										PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME,
										NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN,
										RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, APPLYREPFILTERONLY, :1
									from 
										iareportformat childtab
									where 
										exists (select 1 from iareportinfo where industrycode=:2 and record#= childtab.report#))";
        
        $ok = $ok && ExecStmt(array($iareportformat,$values['CODE'],$values['COPYFROM']), 0);

        $iabeans = "insert into iabeans (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY,INDUSTRYCODE) (
									select 
										(select nvl(max(record#),0) as max from iaglacctgrpmembers)+rownum a1,
										DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED,
										(select record# from iadashboard where industrycode=:1 and title=(select title from iadashboard where record#= childtab.dashboardkey)) a2,
										:1
									from 
										iabeans childtab
									where 
										exists (select 1 from iadashboard where industrycode=:2 and record#= childtab.dashboardkey))";
        
        $ok = $ok && ExecStmt(array($iabeans,$values['CODE'],$values['COPYFROM']), 0);        


        // need to replace PATH in iareportrows and iareportcomps
        $iareportrows = QueryResult(array("select report#, path from iareportrows where report# in (select record# from iareportinfo where industrycode=:1)",$values['CODE']));

        $stmt = "update iareportrows set path=:3 where report#=:1 and path=:2";
        $delstmt = "delete from iareportrows where report#=:1 and path=:2";
        foreach ($iareportrows as $val) {
            $oldpath = explode('/', $val['PATH']);
            $newpathrecArr = array();
            $wrongPath = false;
            foreach ($oldpath as $pathrec) {
                if ($pathrec!='') {
                    if ($newRecNo['IAGLACCTGRP'][$pathrec]) {
                        $newpathrecArr[] = $newRecNo['IAGLACCTGRP'][$pathrec];
                    } else {
                        $wrongPath = true;
                    }
                }
            }
            $newpathrec = '/'.join('/', $newpathrecArr);
            if($wrongPath) {
                $ok = $ok && ExecStmt(array($delstmt,$val['REPORT#'],$val['PATH']), 0);        
            } else {
                $ok = $ok && ExecStmt(array($stmt,$val['REPORT#'],$val['PATH'],$newpathrec), 0);        
            }
        }

        $iareportcomps = QueryResult(array("select report#, path from iareportcomps where report# in (select record# from iareportinfo where industrycode=:1)",$values['CODE']));

        $stmt = "update iareportcomps set path=:3 where report#=:1 and path=:2";
        $delstmt = "delete from iareportcomps where report#=:1 and path=:2";
        foreach( $iareportcomps as $val) {
            $oldpath = explode('/', $val['PATH']);
            $newpathrecArr = array();
            $wrongPath = false;
            foreach ($oldpath as $pathrec) {
                if ($pathrec!='') {
                    if($newRecNo['IAGLACCTGRP'][$pathrec]) {
                        $newpathrecArr[] = $newRecNo['IAGLACCTGRP'][$pathrec];
                    } else {
                        $wrongPath = true;
                    }
                }
            }
            $newpathrec = '/'.join('/', $newpathrecArr);
            if ($wrongPath) {
                $ok = $ok && ExecStmt(array($delstmt,$val['REPORT#'],$val['PATH']), 0);        
            } else {
                $ok = $ok && ExecStmt(array($stmt,$val['REPORT#'],$val['PATH'],$newpathrec), 0);        
            }
        }

        return $ok;

    }

    /**
     * To get map to show in select box
     *
     * @access private
     *
     * @param bool $needSelect
     * @param bool $showDBB
     * @param bool $active by default on false to see all templates from the industrycode table (active or not)
     * @param bool $showSIFR
     * @param bool $showFrance
     * @param bool $showEMR
     *
     * @return array $map
     */
    function GetIndustryMap(
                            bool $needSelect=false,
                            bool $showDBB=true,
                            bool $active=true,
                            bool $showSIFR=true,
                            bool $showFrance=true,
                            bool $showEMR=true
                            )
    {
        if ( defined('INTACCTCONTEXT') ) {
            return [];
        }

        $sql = "SELECT code, description FROM industrycode WHERE 1=1";

        if ( ! $showDBB ) {
            $sql .= " AND code NOT LIKE '%DBB'";
        }

        if ( ! $showSIFR ) {
            $sql .= " AND code != 'NFP-SIFR'";
        }

        if (!$showEMR) {
            $sql .= " AND code NOT LIKE '%-EMR%'";
        }

        if ( ! $showFrance ) {
            $sql .= " AND code NOT LIKE '%-FRANCE%'";
        }

        if ( $active ) {
            $sql .= " AND active = :1";
        }

        $indRes = QueryResult([ $sql, 'T' ]);
        $map = [];

        if ( $needSelect ) {
            $map[''] = "--Select--";
        }

        foreach ( $indRes as $res ) {
            $map[$res['CODE']] = $res['DESCRIPTION'];
        }

        return $map;
    }

    /**
     * update the record in the database
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        global $gErr;
        $sql = "UPDATE industrycode SET description = :1, active = :2 WHERE code = :3";
        $active = ($values["ACTIVE"] === true) ? "T" : "F";
        $query = array($sql, $values["DESCRIPTION"], $active, $values["CODE"]);

        //update all schemas from all pods
        $results = DBRunner::runOnAll("ExecStmtEx", array($query));

        //check for errors
        $errors = "";
        $success = "";
        $i = 0;
        foreach ($results as $id => $result) {
            if (!$result) {
                $errors .= $id . ', ';
                $i++;
            } else {
                $success .=  $id . ', ';
            }
        }

        if ($success !== "") {
            $schemas = rtrim($success, ", ");
            $success_msg = sprintf(_("Schemas '%s' were updated"), $schemas);

            $gErr->AddWarning($success_msg, __FILE__ . ':' . __LINE__);
        }

        if ($errors !== "") {
            $errors = rtrim($errors, ", ");
            if ($i === 1) {
                $msg = sprintf("Schema '%s' could not be updated.", $errors);
                $gErr->addIAError('CO-0752', __FILE__ . ":" . __LINE__, $msg, ['ERRORS' => $errors]);
            } else {
                $msg = sprintf("Schemas '%s' could not be updated.", $errors);
                $gErr->addIAError('CO-0753', __FILE__ . ":" . __LINE__, $msg, ['ERRORS' => $errors]);
            }

            return false;
        }

        return true;
    }
}

