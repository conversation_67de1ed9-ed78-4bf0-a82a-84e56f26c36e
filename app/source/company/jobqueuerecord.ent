<?php

//require 'imspackagedetail.ent';

/* @var array $kSchemas */
$kSchemas['jobqueuerecord'] = array(
    'children' => array(
        'userinfo' => array(
            'fkey' => 'users',
            'invfkey' => 'loginid',
            'table' => 'userinfomst',
            'join' => 'outer'
        ),
    ),
    'object' => array(
        JobQueueRecordManager::IMSQS_PACKAGEID,
        JobQueueRecordManager::IMSQS_TIMEQUEUED,
        JobQueueRecordManager::IMSQS_JOBTYPE,
        JobQueueRecordManager::IMSQS_ACTION,
        JobQueueRecordManager::IMSQS_DETAILS,
        JobQueueRecordManager::IMSQS_USER,
        IMSPackageDetailManager::IMSPD_STATE,
        JobQueueRecordManager::IMSQS_ACTIVETIME,
        JobQueueRecordManager::IMSQS_TIMEINQUEUE,
        IMSPackageDetailManager::IMSPD_TIMESTARTED,
        JobQueueRecordManager::IMSQS_EXEC_REQ_TIME,
        JobQueueRecordManager::IMSQS_ACTION_CANCEL,
        JobQueueRecordManager::IMSQS_ACTION_PROCESS_NEXT
    ),
    'schema' => array(
        JobQueueRecordManager::IMSQS_PACKAGEID           => 'packageid',
        JobQueueRecordManager::IMSQS_TIMEQUEUED          => 'timequeued',
        JobQueueRecordManager::IMSQS_JOBTYPE              => 'topic',
        JobQueueRecordManager::IMSQS_ACTION              => 'action',
        JobQueueRecordManager::IMSQS_DETAILS             => 'details',
        JobQueueRecordManager::IMSQS_USER                => 'users',
        IMSPackageDetailManager::IMSPD_STATE              => 'state',
        JobQueueRecordManager::IMSQS_ACTIVETIME          => 'activetime',
        JobQueueRecordManager::IMSQS_TIMEINQUEUE         => 'waitinqueue',
        IMSPackageDetailManager::IMSPD_TIMESTARTED        => 'timestarted',
        JobQueueRecordManager::IMSQS_EXEC_REQ_TIME       => 'exec_req_time',
        JobQueueRecordManager::IMSQS_ACTION_CANCEL       => 'packageid',
        JobQueueRecordManager::IMSQS_ACTION_PROCESS_NEXT => 'packageid',
        'USERINFO' => array (
            'userinfo.*' => 'userinfo.*',
        ),
    ),
    'publish' => array(
        'JOBID',
        'TIMEQUEUED',
        'JOBTYPE',
        'ACTION',
        'DETAILS',
        'USERS',
        'STATE',
        'ACTIVETIME',
        'WAITINQUEUE',
        'TIMESTARTED',
    ),
    'fieldinfo' => array(
        array (
            'path'        => JobQueueRecordManager::IMSQS_PACKAGEID,
            'fullname'    => 'IA.JOB_ID',
            'readonly'    => true,
            'hidden'     => false,
            'desc'        => 'IA.JOB_ID',
            'type'         => array (
                'ptype'     => 'text',
                'type'         => 'text',
                'maxlength' => 80
            ),
            'id' => 1,
        ),
        array (
            'path'      => JobQueueRecordManager::IMSQS_TIMEQUEUED, //timestamp_date
            'fullname'  => 'IA.TIME_QUEUED',
            'readonly'  => true,
            'hidden'    => false,
            'desc'      => 'IA.TIME_WHEN_THE_PACKAGE_WAS_QUEUED',
            'type'      => array (
                'ptype'     => 'timestamp',
                'type'      => 'timestamp',
                'maxlength' => 12
                ),
            'id' => 2,
        ),
        array (
            'path'        => JobQueueRecordManager::IMSQS_JOBTYPE,
            'fullname'    => 'IA.JOB_TYPE',
            'readonly'    => true,
            'hidden'     => false,
            'desc'        => 'IA.JOB_TYPE',
            'type'         => array (
                'ptype'     => 'text',
                'type'         => 'text',
                'maxlength' => 80
            ),
            'id' => 3,
        ),
        array (
            'path'        => JobQueueRecordManager::IMSQS_ACTION,
            'fullname'    => 'IA.ACTION',
            'readonly'    => true,
            'hidden'     => false,
            'desc'        => 'IA.PACKAGE_DETAILS_ACTION',
            'type'         => array (
                'ptype'     => 'text',
                'type'         => 'text',
                'maxlength' => 100
            ),
            'id' => 4,
        ),
        array (
            'path'        => JobQueueRecordManager::IMSQS_DETAILS,
            'fullname'    => 'IA.DETAILS',
            'readonly'    => true,
            'hidden'     => false,
            'desc'        => 'IA.PACKAGE_DETAILS',
            'type'         => array (
                'ptype'     => 'text',
                'type'         => 'text',
                'maxlength' => 1000
                ),
            'id' => 5,
        ),
        array (
            'path'        => JobQueueRecordManager::IMSQS_USER,
            'fullname'    => 'IA.USERS',
            'readonly'    => true,
            'hidden'     => false,
            'desc'        => 'IA.USER_LOGIN_ID',
            'type'         => array (
                'ptype'     => 'text',
                'type'         => 'text'
            ),
            'id' => 6,
        ),
        array (
            'path'        => IMSPackageDetailManager::IMSPD_STATE,
            'fullname'    => 'IA.STATE',
            'readonly'    => true,
            'hidden'     => false,
            'desc'        => 'IA.STATE',
            'type'        => array (
                'ptype'         => 'enum',
                'type'            => 'enum',
                'validlabels'    => array ('IA.FAILED', 'IA.PARKED', 'IA.QUEUED', 'IA.IN_PROGRESS', 'IA.DELIVERED', 'IA.DEFERRED', 'IA.CANCELLED'),
                'validvalues'    => array ('Failed', 'Parked', 'Queued', 'In progress', 'Delivered', 'Deferred', 'Canceled'),
                '_validivalues' => array ('D', 'P', 'Q', 'T', 'S', 'R', 'C'),
            ),
            'id' => 7,
        ),
        array (
            'path'      => JobQueueRecordManager::IMSQS_ACTIVETIME,
            'fullname'  => 'IA.ACTIVE_TIME',
            'readonly'  => true,
            'hidden'    => false,
            'desc'      => 'IA.CURRENT_TIME_MINUS_TIMESTARTED',
            'type'      => array (
                'ptype' => 'text',
                'type'  => 'text'
            ),
            'id' => 8,
        ),
        array (
            'path'      => JobQueueRecordManager::IMSQS_TIMEINQUEUE,
            'fullname'  => 'IA.JOB_WAIT_TIME',
            'readonly'  => true,
            'hidden'    => false,
            'desc'      => 'IA.CURRENT_TIME_MINUS_TIMEQUEUED',
            'type'      => array (
                'ptype' => 'text',
                'type'  => 'text'
            ),
            'id' => 9,
        ),
        array (
            'path'        => IMSPackageDetailManager::IMSPD_TIMESTARTED,
            'fullname'    => 'IA.JOB_STARTED',
            'readonly'    => true,
            'hidden'     => true,
            'desc'        => 'IA.TIME_WHEN_THE_PACKAGE_WAS_STARTED',
            'type'         => array (
                'ptype'     => 'timestamp',
                'type'         => 'timestamp',
                'maxlength' => 12
            ),
            'id' => 10,
        ),
        array (
            'path'      => JobQueueRecordManager::IMSQS_EXEC_REQ_TIME,
            'fullname'  => 'IA.EXECUTION_REQUEST_TIME',
            'readonly'  => true,
            'hidden'    => true,
            'desc'      => 'IA.EXECUTION_REQUEST_TIME',
            'type'      => array (
                'ptype'     => 'timestamp',
                'type'      => 'timestamp',
                ),
            'platform'=>false,
            'id' => 11,
        ),
        array(
            'path' => 'USERINFO.LOGINID',
            'fullname' => 'IA.USER_ID',
            'idw' => false,
            'id' => 12,
        ),
     ),
    'nexus' => array(
        'userinfo' => array(
            'object' => 'userinfo',
            'relation' => ONE2MANY,
            'field' => 'users',
            'printas' => 'IA.USER',
        )
    ),
    'table' => 'imsqueuestatus',
    'printas' => 'IA.OFFLINE_JOB_QUEUE',
    'pluralprintas' => 'IA.OFFLINE_JOB_QUEUES',
    'vid' => JobQueueRecordManager::IMSQS_PACKAGEID,
    'module' => 'co',
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'TIMEQUEUED',
        'TIMESTARTED'
    ),
    'api' => array(
        'PERMISSION_MODULES' => array('co'),
        'PERMISSION_READ'    => 'lists/jobqueuerecord',
        'PERMISSION_CREATE'  => 'NONE',
        'PERMISSION_UPDATE'  => 'NONE',
        'PERMISSION_DELETE'  => 'NONE',
        'PERMISSION_CANCEL'  => 'lists/jobqueuerecord/cancel',
        'PERMISSION_PROMOTE' => 'lists/jobqueuerecord/promote',
        'LESS_GET_FIELDS' => [JobQueueRecordManager::IMSQS_ACTION_CANCEL, JobQueueRecordManager::IMSQS_ACTION_PROCESS_NEXT],
    ),
    'url' => array(
        'no_short_url' => true,    // Don't allow short url.
    ),
    'nosysview' => true,
    'description' => 'IA.LIST_OF_OFFLINE_JOBS_INCLUDING_STATUS_FOR_PAST_2_W',
    'valueTranslateFields' => [
        JobQueueRecordManager::IMSQS_JOBTYPE
    ],
);

