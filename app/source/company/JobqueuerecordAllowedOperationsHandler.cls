<?php

/**
 * JobQueueRecordAllowedOperationsHandler
 *
 * <AUTHOR>
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 *
 */
class JobqueuerecordAllowedOperationsHandler extends AllowedOperationsHandler
{
    /**
     * @return array
     */
    protected function getOperations(): array
    {
        return array_merge(
            parent::getOperations(),
            array("canCancel", "canPromote")
        );
    }

    /**
     * Record Level Check for Cancel Operation
     *
     * @param array  $record
     * @param string|null $moduleKey
     *
     * @return bool
     */
    protected function canCancel(array $record, /** @noinspection PhpUnusedParameterInspection */ string|null $moduleKey): bool
    {
        return (
            JobQueueRecordManager::isJobOperationAllowed(GetOperationId('co/lists/jobqueuerecord/cancel')) !== false &&
            strtolower($record['STATE']) === 'queued'
        );
    }

    /**
     * Record Level Check for Promote Operation
     *
     * @param array  $record
     * @param string|null $moduleKey
     *
     * @return bool
     */
    protected function canPromote(array $record, /** @noinspection PhpUnusedParameterInspection */ string|null $moduleKey): bool
    {
        return (
           JobQueueRecordManager::isJobOperationAllowed(GetOperationId('co/lists/jobqueuerecord/promote')) !== false &&
           strtolower($record['STATE']) === 'queued'
        );
    }

    /**
     * @return string[]
     */
    protected function getFieldsForPermissionChecks(): array
    {
        return array_merge(
            parent::getFieldsForPermissionChecks(),
            ['STATE']
        );
    }

}
