<?php
/**
 *  FILE:           WSOUserInfoLister.cls
 *  AUTHOR:         dprundus
 *  DESCRIPTION:    Web services Only User Info Lister
 *
 *  (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *  This document contains trade secret data that belongs to Intacct
 *  Corporation and is protected by the copyright laws.  Information
 *  herein may not be used, copied or disclosed in whole or in part
 *  without prior written consent from Intacct Corporation.
 */


/**
 * Class WSOUserInfoLister
 */
class WSOUserInfoLister extends UserInfoLister
{

    /**
     * @param array $params
     */
    public function __construct($params = [])
    {
        $myParams = [
            'entity' => 'wsouserinfo',
            'title' => 'IA.WEB_SERVICES_USERS',
        ];
        $params = array_merge($myParams, $params);

        parent::__construct($params);
    }

    /**
     * @param array $querySpec
     * @return  bool
     */
    function getUserFilters(&$querySpec)
    {
        if (isset($querySpec['filters'][0])) {
            $querySpec['filters'][0][] = [ 'USERTYPE', '!=', 'CRM user' ];
            $querySpec['filters'][0][] = [ 'LOGINDISABLED', '=', 'true' ];
        }

        return true;
    }

}
