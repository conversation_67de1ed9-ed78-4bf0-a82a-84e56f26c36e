<?php

/**
 * Function service for getAuditTrail functionality in AuditTrail
 */
class CompanyConfigAuditTrailService
{
    public function __construct()
    {
        BindLocale();
    }
    
    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return array
     * @throws APIInvalidFieldException
     * @throws APIException|I18NException
     */
    public function list(array $request, array $extraContext): array
    {
        $entityAuditData = [];
        if (!array_key_exists("OBJECT", $request) || !array_key_exists("KEY", $request)) {
            // 400 error
            throw new APIInvalidFieldException($extraContext, "ID", APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0307, ['FIELD1' => 'OBJECT', 'FIELD2' => 'KEY']);
        } else {
            $restEntity= $request['OBJECT'] ?? '';
            $entityKey = $request['KEY'] ?? '';
            $registryLoader = RegistryLoader::getInstance($extraContext['version']);
        }
    
        try {
            $handler = $registryLoader->getHandler($restEntity);
            if (is_null($handler)) {
                throw (new APIException())->setAPIError(APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0030, ['PARAMETER' => $restEntity], true));
            }
            $adapter = $handler->getAdapter();
            $manager = $adapter->getEntityManager();
            if (is_null($manager)) {
                throw (new APIException())->setAPIError(APIError::getInstance(APIErrorMessages::INTERNAL_SERVER_ERROR_INTERNAL_ERROR_0001, [], true));
            }

            if ($manager instanceof ModuleSetupManager) {
                $manager = AuditTrail::getEntityManager('modulepref', $manager->_moduleKey);
                if (!$manager) {
                    return $entityAuditData;
                }
            }

            $auditEntity = $manager->getAuditEntity();

            if($manager->useNewAuditKey()) {
                $auditEntityKey = $entityKey . '--REC';
            }
            else {
                $auditEntityKey = $manager->GetVidFromRecordNo($entityKey);
            }

            // Check to see if the user has the 'read' permission on this object
            if (!$manager->API_ValidateWithoutError(API_READ_AUDITTRAIL, $auditEntityKey, $opId)) {
                throw (new APIException())->setAPIError(APIError::getInstance(APIErrorMessages::FORBIDDEN_FORBIDDEN_0001,[], true));
            } else {
                $auditTrail = new AuditTrail($manager);
                $entityAuditData = $auditTrail->getFormattedAuditTrail($auditEntityKey, true, true, false);

                if (is_array($entityAuditData)){
                    foreach ($entityAuditData as $key => &$singleChange){
                        $singleChange['KEY'] = $key + 1;
                        $singleChange["DATETIMEAPI"] = $singleChange['ACCESSTIME']->format('Y-m-d\TH:i:s\Z');
                    }
                }

                $auditSession = AuditTrailSession::getInstance();
                $auditSession->trackOperation(AuditTrailSession::OP_VIEW_AUDIT_TRAIL, $auditEntity,
                    $auditEntityKey, []);
            }
        
        } catch (IAException $e) {
            throw ( new APIException("", 0, $e) )->setAPIError(APIError::getInstance(
                APIErrorMessages::FORBIDDEN_ACCESS_ISSUE_0001, [ "RESOURCE" => 'AuditTrail' ], true));
        }
    
        return $entityAuditData;
    }
    
}