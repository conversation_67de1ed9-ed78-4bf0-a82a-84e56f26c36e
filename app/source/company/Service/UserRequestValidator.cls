<?php

class UserRequestValidator implements APIValidator
{

    private UserInfoManager $userInfoManager;
    private userPermManager $userPermManager;
    private PermissionManager $permissionManager;
    const API_NAME_PERMISSION_ASSIGNMENTS = 'permissionAssignments';
    const API_NAME_CUSTOM_PERMISSION_ASSIGNMENTS = 'customPermissionAssignments';

    public function __construct()
    {
        global $gManagerFactory;
        $this->userInfoManager = $gManagerFactory->getManager('userinfo');
        $this->userPermManager = $gManagerFactory->getManager('userperm');
        $this->permissionManager = $gManagerFactory->getManager('permission');
    }

    /**
     * @param $operation
     * @param $request
     * @param $handler
     * @param array $extraParams
     * @return void
     */
    function validateRequest($operation, &$request, $handler, array &$extraParams = [])
    {
        // This implementation is not needed
    }

    /**
     * @param $operation
     * @param $response
     * @param $handler
     * @return void
     */
    function validateResponse($operation, &$response, $handler)
    {
        // This implementation is not needed
    }

    /**
     * @param $operation
     * @param $request
     * @param $handler
     * @param array $extraParams
     * @return void
     * @throws APIException
     * @throws APIInternalException
     */
    function validateSingleRequest($operation, &$request, $handler, array &$extraParams = [])
    {
        if ($operation === APIConstants::API_OPERATION_PATCH || $operation === APIConstants::API_OPERATION_CREATE) {
            if ($operation === APIConstants::API_OPERATION_PATCH) {
                $userInfo = $this->userInfoManager->getRowByRecordNo($request['key']);
            }
            if (isset($request[self::API_NAME_PERMISSION_ASSIGNMENTS])) {
                foreach ($request[self::API_NAME_PERMISSION_ASSIGNMENTS] as $assignment) {
                    $errors = [];
                    $this->validateUserAccessibilityToPermissions($userInfo ?? false, $errors);
                    if (empty($errors)) {
                        $this->validatePermissionAssignment($operation, $assignment, $errors, $request);
                    }
                    if (!empty($errors)) {
                        APIQueryOrchestrator::handleErrors($errors);
                    }
                }
            }
            if (isset($request[self::API_NAME_CUSTOM_PERMISSION_ASSIGNMENTS])) {
                foreach ($request[self::API_NAME_CUSTOM_PERMISSION_ASSIGNMENTS] as $assignment) {
                    $errors = [];
                    $this->validateUserAccessibilityToPermissions($userInfo ?? false, $errors);
                    if (empty($errors)) {
                        $this->validateCustomPermissionAssignment($operation, $assignment, $errors, $request);
                    }
                    if (!empty($errors)) {
                        APIQueryOrchestrator::handleErrors($errors);
                    }
                }
            }
        }
    }

    private function validatePermissionAssignment($operation, $assignment, &$errors, $request)
    {
        $this->validateMandatoryFields($assignment, false, $errors);

        if (empty($errors) && $operation === APIConstants::API_OPERATION_PATCH) {
            $permissionInfo = $this->getPermissionInfo($assignment['permission'], $errors);
            if ($permissionInfo !== false) {
                $userInfo = $this->userInfoManager->getRowByRecordNo($request['key']);
                $this->userPermManager->processPermissionAllowedAccessRights($userInfo, $permissionInfo);
                $this->validateAccessRightsAndModules($operation, $assignment, $permissionInfo, $userInfo);
            }
        }
    }

    private function validateCustomPermissionAssignment($operation, $assignment, &$errors, $request)
    {
        $this->validateMandatoryFields($assignment, true, $errors);
    }

    private function validateMandatoryFields($assignment, $isCustom, &$errors)
    {
        if (!isset($assignment['accessRights'])) {
            $field = "'permissionAssignments.accessRights'";
            if ($isCustom) {
                $field = "'customPermissionAssignments.accessRights'";
            }
            $errors[] = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0118,
                ['FIELD' => $field]
            );
        }

        if($isCustom) {
            if (!isset($assignment['application']['key'])) {
                $errors[] = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0118,
                    ['FIELD' => "'customPermissionAssignments.application.key'"]
                );
            }

            if (!isset($assignment['application']['permission'][0]['name'])) {
                $errors[] = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0118,
                    ['FIELD' => "'customPermissionAssignments.application.permission.name'"]
                );
            }

            if (!isset($assignment['application']['permission'][0]['group'])) {
                $errors[] = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0118,
                    ['FIELD' => "'customPermissionAssignments.application.permission.group'"]
                );
            }
        } else {
            if (!isset($assignment['permission']['key'])) {
                $errors[] = APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0118,
                    ['FIELD' => 'permissionAssignments.permission.key']
                );
            }
        }
    }

    private function getPermissionInfo($permission, &$errors)
    {
        $permissionInfo = $this->permissionManager->get($permission['key']);
        if ($permissionInfo === false) {
            $errors[] = APIError::getInstance(APIErrorMessages::NOT_FOUND_NOT_FOUND_0001, ['OBJECT' => 'permission', 'RECORD_NO' => $permission['key']]);
        }

        return $permissionInfo;
    }

    private function validateUserAccessibilityToPermissions($userInfo, &$errors)
    {
        if ($userInfo !== false) {
            if ($userInfo['STATUS'] !== 'T'
                || (GetMyAdminLevel() < UserInfoManager::DB_FULL_ADMIN && $userInfo['ADMIN'] == UserInfoManager::DB_FULL_ADMIN)
                //The possibility to add or change standard or custom permissions has been removed for some categories of users until all the constraints are documented and clear
                || $userInfo['CATEGORY'] === UserInfoManager::CATEGORY_PORTAL
                || $userInfo['CATEGORY'] === UserInfoManager::CATEGORY_TEMPLATE_ADMIN
                || $userInfo['CATEGORY'] === UserInfoManager::CATEGORY_ANONYMOUS
                || $userInfo['CATEGORY'] === UserInfoManager::CATEGORY_EXTERNAL
                || $userInfo['CATEGORY'] === UserInfoManager::CATEGORY_SERVICE
                || $userInfo['CATEGORY'] === UserInfoManager::CATEGORY_SERVICE_DEFAULT
                || $userInfo['CATEGORY'] === UserInfoManager::CATEGORY_CONSOLIDATION
            ) {
                $errors[] = APIError::getInstance(APIErrorMessages::FORBIDDEN_FORBIDDEN_0001);
            }
        }
    }

    private function validateAccessRightsAndModules($operation, $assignment, $permissionInfo, $userInfo)
    {
        $valid = validateAccessRights($assignment['accessRights'], $permissionInfo);
        if (!$valid) {
            $this->throwBadInputError(APIErrorMessages::FORBIDDEN_FORBIDDEN_0001);
        }

        $valid = $this->userPermManager->validateAllowedModules($userInfo, $permissionInfo['MODULE']);
        if (!$valid) {
            $this->throwBadInputError(APIErrorMessages::UNPROCESSABLE_ENTITY_OPERATION_FAILED_0001, [['OPERATION' => $operation, 'RESOURCE_NAME' => 'role']]);
        }
    }

    /**
     * @param string $apiErrorCode
     * @param array  $args
     *
     * @return void
     * @throws APIException
     * @throws APIInternalException
     */
    private function throwBadInputError(string $apiErrorCode, array $args = []) : void
    {
        throw (new APIException())->setAPIError(APIError::getInstance($apiErrorCode, $args, true));
    }

    /**
     * @param $operation
     * @param $response
     * @param $handler
     * @return void
     */
    function validateSingleResponse($operation, &$response, $handler)
    {
        // This implementation is not needed
    }

    /**
     * @param string $operation
     * @param bool $isRequest
     * @return bool
     */
    function suppressBaseValidation(string $operation, bool $isRequest): bool
    {
        return false;
    }
}