<?php
/**
 * Manager file for the Note object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class NoteManager extends EntityManager
{
    /* @var string $noteObjectId */
    private $noteObjectId = null;

    /* @var string $noteObjectType */
    private $noteObjectType = null;

    /**
     * @return string
     */
    public function getNoteObjectId()
    {
        return $this->noteObjectId;
    }

    /**
     * @param string $noteObjectId
     */
    public function setNoteObjectId($noteObjectId)
    {
        $this->noteObjectId = $noteObjectId;
    }

    /**
     * @return string
     */
    public function getNoteObjectType()
    {
        return $this->noteObjectType;
    }

    /**
     * @param string $noteObjectType
     */
    public function setNoteObjectType($noteObjectType)
    {
        $this->noteObjectType = $noteObjectType;
    }

    /**
     * The generic method that returns all the notes specified for the given
     * object. The caller of this method has to specify the object as follows:
     * <code>
     *     $cnChecklistManager = $gManagerFactory->GetManager('contractcompliancetask');
     *     $cnChecklistManager->setClObjectId($obj['RECORDNO']);
     *     $obj['CHECKLIST'] = $cnChecklistManager->getCheckList();
     * </code>
     *
     * @return array[]
     */
    public function getNotes()
    {
        $notes = $this->getExistingNotes();

        return $notes;
    }

    /**
     * Returns all the existing check list records
     *
     * @return array[]
     */
    private function getExistingNotes()
    {
        $clObjectId = $this->getNoteObjectId();
        $params = [
            'selects' => $this->getFields(),
            'filters' => [
                [
                    [$this->getEntity() . '.objectid', '=', "$clObjectId"],
                    ['STATUS', '=', 'active']
                ]
            ],
            'orders' => [
                ['RECORDNO', 'desc']
            ]
        ];

        $checkList = parent::GetList($params);
        return $checkList;
    }

    /**
     * By default queries all the fields specified in the entity. This method
     * should be overwritten if the entity has fields not mapped to any
     * column in the database.
     *
     * @return string[]
     */
    protected function getFields()
    {
        return $this->GetGetFields();
    }
}


