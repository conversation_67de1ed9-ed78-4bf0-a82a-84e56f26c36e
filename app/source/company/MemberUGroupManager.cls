<?php

/**
 * File MemberUGroupManager.cls contains the class MemberUGroupManager
 *
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
import('EntityManager');

/**
 * Class MemberUGroupManager
 */
class MemberUGroupManager extends EntityManager
{

    /**
     * @param array $params the init parameters
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
    protected function regularAdd(&$values)
    {
        global $gErr;

        $memb_em = new UserGroupMembersManager();
        $ok = $this->_Translate($values);
        if(!$ok){
            return false;
        }

        $userId = $values['USERID'];
        $u_o_gkey = $values[':u_o_gkey'];
        $toGroupName = $values['PARENTGROUPNAME'];
        $toGroupId = $values[':parentgroup'];

        if ( $userId ) {
            if ( IsExternalUserById($userId) ) {
                $gErr->addIAError(
                    'CO-0099',
                    __FILE__ . ':' . __LINE__,
                    "Can not add External user '$userId' to user groups",
                    ['USER' => $userId]
                );
                return false;
            }
            if ( IsServicesUserById($userId) ) {
                $gErr->addIAError(
                    'CO-0100',
                    __FILE__ . ':' . __LINE__,
                    "Can not add Services user '$userId' to user groups",
                    ['USER' => $userId]
                );
                return false;
            }

            $ok = true;
            $source = 'Adding_User_To_Group_API : ' . $u_o_gkey;
            $ok = $ok && $this->beginTrx($source);
            $invalidUserList = array();

            if ( ! $memb_em->UserAlreadyIn($u_o_gkey, $toGroupId) ) {
                $ok = $ok && $memb_em->AddUserToGroup($u_o_gkey, $toGroupId);
                if($ok){
                    $r = $memb_em->GetExistingRecord($u_o_gkey, $toGroupId);
                    $values['RECORDNO'] = $r[0]["RECORD#"];
                }
                $ok = $ok && SetPermissionCacheValidity($u_o_gkey, false, 0, false);
                $invalidUserList[] = $u_o_gkey;
            } else {
                $gErr->addIAError(
                    'CO-0198',
                    __FILE__ . ':' . __LINE__,
                    "The user ".$userId." already exists in the group ".$toGroupName.".",
                    ['USER' => $userId, 'GROUP' => $toGroupName]
                );
                $ok = false;
            }

            $ok = $ok && UserRightsManager::CreateImsJobToGenerateUserPerms($invalidUserList);
            $ok = $ok && $this->commitTrx($source);
            if ( ! $ok ) {
                $this->rollbackTrx($source);
            }
        }
        return $ok;
    }

    function _Translate(&$values)
    {
        global $gManagerFactory;
        global $gErr;

        $userMgr = $gManagerFactory->getManager('userinfo');
        $userinfo = $userMgr->GetRaw($values['USERID']);
        if ( !$userinfo ) {
            $gErr->addIAError(
                'CO-0155',
                __FILE__ . ':' . __LINE__,
                "The user '".$values['USERID']."' does not exist.",
                ['LOGINID' => $values['USERID']]
            );
            return false;
        }else{
            $values[':u_o_gkey'] = $userinfo[0]['RECORD#'];
        }

        $userGroupMgr = $gManagerFactory->getManager('usergroup');
        $group = $userGroupMgr->GetRaw($values['PARENTGROUPNAME']);
        if ( !$group ) {
            $gErr->addIAError(
                'CO-0197',
                __FILE__ . ':' . __LINE__,
                "The group '".$values['PARENTGROUPNAME']."' does not exist.",
                ['NAME' => $values['PARENTGROUPNAME']]
            );
            return false;
        }else{
            $values[':parentgroup'] = $group[0]['RECORD#'];
        }

        return true;
    }

}