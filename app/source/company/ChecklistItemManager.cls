<?php
/**
 * Manager file for the generic check list item object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

import('ChecklistRecordManager');

class ChecklistItemManager extends EntityManager
{

    /**
     * Creates a new record.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(& $values)
    {
        global $gErr;

        $source = get_class($this) . '::Add';
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->preprocessEntity($values, ProcessingMode::CREATE);

        if ($ok) {
            $validator = new ChecklistItemValidator($this, AbstractValidator::VALIDATION_MODE_ADD);
            $ok = $validator->validate($values);
        }

        $ok = $ok && $this->adjustOrdinalNumbers($values, ProcessingMode::CREATE);

        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if(!HasErrors() && HasWarnings()) {
                $this->_QM->rollbackTrx($source);
            } else{
                $msg = "Could not create check list item record!";
                $gErr->addIAError('CO-0930', __FILE__ . ':' . __LINE__, $msg);
                epp("$source: Error: $msg");
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * Updates the record in the database.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        return $this->_setWithOpts($values, true);
    }

    /**
     * Updates the record in the database.
     *
     * @param array $values
     * @param bool  $adjustOrdinalNo
     *
     * @return bool
     */
    function setWithOpts(&$values, $adjustOrdinalNo)
    {
        return $this->_setWithOpts($values, $adjustOrdinalNo);
    }

    /**
     * Updates the record in the database.
     *
     * @param array $values
     * @param bool  $adjustOrdinalNo
     *
     * @return bool
     */
    private function _setWithOpts(&$values, $adjustOrdinalNo)
    {
        global $gErr;

        $source = get_class($this) . "::Set";
        $ok = $this->_QM->beginTrx($source);

        if ($adjustOrdinalNo) {
            $ok = $ok && $this->preprocessEntity($values, ProcessingMode::UPDATE);

            if ($ok) {
                $validator = new ChecklistItemValidator($this, AbstractValidator::VALIDATION_MODE_EDIT);
                $ok = $validator->validate($values);
            }

            $ok = $ok && $this->adjustOrdinalNumbers($values, ProcessingMode::UPDATE);
        }

        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if(!HasErrors() && HasWarnings()) {
                $this->_QM->rollbackTrx($source);
            } else{
                $msg = "Could not create check list item record!";
                $gErr->addIAError('CO-0930', __FILE__ . ':' . __LINE__, $msg);
                epp("$source: Error: $msg");
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * Iterates over the list of out of order items and corrects their
     * ordinal numbers.
     *
     * @param array $values
     * @param string $processingMode
     *
     * @return bool
     */
    private function adjustOrdinalNumbers($values, $processingMode)
    {
        global $gErr;
        $ok = true;

        $oldOrdinalNumber = null;
        if ($processingMode === ProcessingMode::UPDATE) {
            $oldOrdinalNumber = $this->get($values['RECORDNO'])['ORDINAL_NO'];
        }
        if ($oldOrdinalNumber != $values['ORDINAL_NO']) {

            $outOfOrderItems =
                $this->getOutOfOrderItems($values['CHECKLIST_AREA'], $values['ORDINAL_NO'], $oldOrdinalNumber, $processingMode);

            switch ($processingMode) {
            case ProcessingMode::CREATE:
                $ordinalNumber = $values['ORDINAL_NO'] +  1;
                break;
            case ProcessingMode::UPDATE:
                if (!isArrayValueProvided($values, 'ORDINAL_NO') && "" !== $oldOrdinalNumber) {
                    // The user changed from active to INactive
                    $ordinalNumber = $oldOrdinalNumber;
                } else if ("" !== $values['ORDINAL_NO'] && (!isset($oldOrdinalNumber) || "" === $oldOrdinalNumber)) {
                    // The user changed from INactive to active
                    $ordinalNumber = $values['ORDINAL_NO'] +  1;
                } else {
                    if ($oldOrdinalNumber > $values['ORDINAL_NO']) {
                        $ordinalNumber = $values['ORDINAL_NO'] +  1;
                    } else {
                        $ordinalNumber = $oldOrdinalNumber;
                    }
                }
                break;
            case ProcessingMode::DELETE:
                $ordinalNumber = $values['ORDINAL_NO'];
                break;
            default:
                throw new InvalidArgumentException(
                    get_class($this) . "::adjustOrdinalNumbers: Processing mode is not supported.");
            }

            try {
                foreach ($outOfOrderItems as $item) {
                    $item['ORDINAL_NO'] = $ordinalNumber;
                    $this->setWithOpts($item, false);
                    $ordinalNumber++;
                }
            } catch (Exception $e) {
                $ok = false;
                $msg = "Unexpected error in ChecklistItemManager::adjustOrdinalNumbers!";
                $gErr->addIAError('CO-0931', __FILE__ . ':' . __LINE__, $msg);
            }
        }

        return $ok;
    }

    /**
     * Delete the record.
     *
     * @param int|string $id
     *
     * @return bool
     */
    public function delete($id)
    {
        global $gErr;

        $source = get_class($this) . "::Delete";
        $ok = $this->_QM->beginTrx($source);

        $boldMessage = 'Unable to delete record';
        $checklistItem = $this->get($id);
        if (!isset($checklistItem)) {
            $ok = false;
            $msg = get_class($this) . '::Delete: cannot find a checklist item with the given ID.';
            $gErr->addIAError('CO-0932', __FILE__ . ':' . __LINE__, $boldMessage, $msg, ['CLASS' => get_class($this)]);
        }

        $ok = $ok && $this->adjustOrdinalNumbers($checklistItem, ProcessingMode::DELETE);

        if ($ok) {
            if ($this->isInUse($checklistItem)) {
                $msg = 'Check list item is in use and cannot be deleted.';
                $gErr->addIAError('CO-0906', __FILE__ . ':' . __LINE__, $boldMessage, [], $msg);
                $ok = false;
            } else {
                $ok = $ok && parent::Delete($id);
            }
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if(!HasErrors() && HasWarnings()) {
                $this->_QM->rollbackTrx($source);
            } else{
                $msg = 'Could not delete check list item record!';
                $gErr->addIAError('CO-0907', __FILE__ . ':' . __LINE__, $boldMessage, [], $msg);
                epp("$source: Error: $msg");
                $this->_QM->rollbackTrx($source);
            }
        }
        return $ok;
    }

    /**
     * @param array $checklistItem
     *
     * @return mixed
     */
    private function isInUse($checklistItem)
    {
        $id = $checklistItem['RECORDNO'];
        $checkListAreaId = $this->getAreaId($checklistItem['CHECKLIST_AREA']);
        /** @var  ChecklistItemHandler $checklistHandlerManager */
        $checklistHandlerManager = $this->getChecklistItemHandlerManager($checkListAreaId);
        return $checklistHandlerManager->isChecklistItemInUse($id);
    }

    /**
     * @param string $areaId
     *
     * @return ContractComplianceTaskManager|ContractDetailManager
     */
    private function getChecklistItemHandlerManager($areaId)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $manager = null;
        switch ($areaId) {
        case ChecklistRecordArea::CONTRACT_COMPLIANCE_TASK:
            /** @var ContractComplianceTaskManager $manager */
            $manager = Globals::$g->gManagerFactory->getManager('contractcompliancetask');
            break;
        case ChecklistRecordArea::CONTRACT_DETAIL_CHANGE_CONTROL:
            /** @var ContractDetailManager $manager */
            $manager = Globals::$g->gManagerFactory->getManager('contractdetail');
            break;
        default:
            throw new InvalidArgumentException(
                "ChecklistItemManager::getChecklistItemHandlerManager: Unsupported areaId parameter.");
        }
        return $manager;
    }

    /**
     * @param array $values
     * @param string $mod
     *
     * @return bool
     */
    private function preprocessEntity(& $values, $mod)
    {
        if (!isset($mod)) {
            throw new InvalidArgumentException(
                get_class($this) . "::preprocessEntity: Required mod parameter is not provided.");
        }

        $ok = true;
        if (!isArrayValueProvided($values, 'CHECKLIST_AREA')) {
            $values['CHECKLIST_AREA'] = ChecklistRecordArea::getLabel($this->getAreaId());
        }

        if ('active' === $values['STATUS']) {

            if (isArrayValueProvided($values, 'ORDINAL_NO') && !is_numeric($values['ORDINAL_NO'])) {
                global  $gErr;
                $msg = 'Ordinal Number is expected to be a numeric value.';
                $gErr->addIAError('CO-0908', __FILE__ . ':' . __LINE__, '', [], $msg);
                $ok = false;
            }

            $checkListAreaId = $this->getAreaId($values['CHECKLIST_AREA']);
            $params = [
                'filters' => [
                    [
                        ['checklist_area', '=', "$checkListAreaId"],
                        ['status', '=', 'T']
                    ]
                ],
                'orders' => [
                    ['checklistitem.ordinal_no', 'asc']
                ]
            ];
            $checkListItemCount = $this->GetCount($params);
            if (!isArrayValueProvided($values, 'ORDINAL_NO')
                || $values['ORDINAL_NO'] > $checkListItemCount) {
                if (ProcessingMode::UPDATE === $mod) {
                    $values['ORDINAL_NO'] = $checkListItemCount;
                } else if (ProcessingMode::CREATE === $mod) {
                    $values['ORDINAL_NO'] = $checkListItemCount + 1;
                } else {
                    throw new InvalidArgumentException(
                        get_class($this) . "::preprocessEntity: Unsupported mod parameter.");
                }
            }
        } else {
            $values['ORDINAL_NO'] = '';
        }
        return $ok;
    }

    /**
     * Returns the list of all checklist items in the given checklist area
     * having order number equal or larger than the one entered by the user.
     *
     * @param string $area
     * @param string $newOrdinalNo
     * @param string $oldOrdinalNo
     * @param string $processingMode
     *
     * @return array
     */
    private function getOutOfOrderItems($area, $newOrdinalNo, $oldOrdinalNo, $processingMode)
    {
        $checkListAreaId = $this->getAreaId($area);
        $params = [
            'filters' => [
                [
                    ['checklist_area', '=', "$checkListAreaId"],
                    ['status', '=', 'T'],
                ]
            ],
            'orders' => [
                ['ordinal_no', 'asc']
            ]
        ];
        switch ($processingMode) {
        case ProcessingMode::UPDATE:
            if ((!isset($newOrdinalNo) || "" === $newOrdinalNo) && "" !== $oldOrdinalNo) {
                // The user changed from active to inactive
                $params['filters'][0][] = ['ordinal_no', '>', "$oldOrdinalNo"];
            } else if ("" !== $newOrdinalNo && (!isset($oldOrdinalNo) || "" === $oldOrdinalNo)) {
                // The user changed from INactive to active
                $params['filters'][0][] = ['ordinal_no', '>=', "$newOrdinalNo"];
            } else {
                if ($newOrdinalNo < $oldOrdinalNo) {
                    $params['filters'][0][] = ['ordinal_no', '>=', "$newOrdinalNo"];
                    $params['filters'][0][] = ['ordinal_no', '<', "$oldOrdinalNo"];
                } else if ($oldOrdinalNo < $newOrdinalNo) {
                    $params['filters'][0][] = ['ordinal_no', '>', "$oldOrdinalNo"];
                    $params['filters'][0][] = ['ordinal_no', '<=', "$newOrdinalNo"];
                }
            }
            break;
        case ProcessingMode::CREATE:
            $params['filters'][0][] = ['ordinal_no', '>=', "$newOrdinalNo"];
            break;
        case ProcessingMode::DELETE:
            $params['filters'][0][] = ['ordinal_no', '>', "$newOrdinalNo"];
            break;
        default:
            throw new InvalidArgumentException(
                "ChecklistItemManager::getOutOfOrderItems: Unsupported mod parameter.");
        }
        $checkList = parent::GetList($params);

        return $checkList;
    }

    /**
     * Returns internal representation of the area. Throws error if invalid.
     *
     * @param string $area
     *
     * @return string
     */
    protected function getAreaId($area = null)
    {
        if (is_null($area)) {
            throw new InvalidArgumentException(
                "ChecklistItemManager::getAreaId: No area is provided.");
        }

        static $areaIdCache = [];
        if (isset($areaIdCache[$area])) {
            return $areaIdCache[$area];
        }

        $checkListAreaId = null;
        $fieldTypeInfo = $this->GetFieldInfo('CHECKLIST_AREA')['type'];
        foreach ($fieldTypeInfo['validvalues'] as $ind => $validValue) {
            if ($validValue == $area) {
                $checkListAreaId = $fieldTypeInfo['_validivalues'][$ind];
                break;
            }
        }
        if (is_null($checkListAreaId)) {
            throw new InvalidArgumentException(
                get_class($this) . "::getAreaId: checklist area is not valid -> $area");
        }

        $areaIdCache[$area] = $checkListAreaId;
        return $checkListAreaId;
    }

    /**
     * Returns the list of checklist items assigned to the logged in company
     * by the given checklist area.
     *
     * @param string $area
     *
     * @return array
     */
    public function getByArea($area) {
        $filter = array(
            'filters' => [
                [['CHECKLIST_AREA', '=', $area]]
                ]
        );

        return $this->GetList($filter);
    }
}

abstract class ProcessingMode
{
    const CREATE = 'create';
    const UPDATE = 'update';
    const DELETE = 'delete';
}

