<?php
/**
 *    FILE:         affiliateentitypick.ent
 *    AUTHOR:       <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 *  
 */

$kSchemas['affiliateentitypick'] = array(

    'schema' => array( 'PICKID' => 'affiliateentitypick', 'STATUS' => 'status' ),
    'object' => array( 'PICKID', 'STATUS'),
    'fieldinfo' => array(
        array (
            'fullname' => 'IA.AFFILIATE_ENTITY',
            'type' => array ( 'ptype' => 'text', 'type' => 'text', 'maxlength' => 200 ),
            'required' => false,
            'desc' => 'IA.AFFILIATE_ENTITY_DIMENSION_ID_AND_NAME',
            'path' => 'PICKID',
            'renameable' => true,
        ),
        $gStatusFieldInfo,
    ),
    'table'     => 'v_affiliateentitypick',
    'vid'       => 'PICKID',
    'module'    => 'co',
    'printas'   => 'IA.AFFILIATE_ENTITY',
    'pluralprintas' => 'IA.AFFILIATE_ENTITIES',
    'renameable' => true,
);