<?php
/**
 * File description
 * Class file for AffiliateEntityPickManager
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @copyright 2024 Sage Intacct Corporation
 */

class AffiliateEntityPickManager extends HierarchicalEntityManager
{
    /**
     * PickID clause
     *
     * @return string
     */
    public function GetPlatformContextPickId() : string
    {
        return "A.AFFILIATEENTITYID || '--' || TRIM(A.NAME)";
    }

    /**
     * PickID clause
     *
     * @return string
     */
    public function GetPlatformContextPickIdSql() : string
    {
        return $this->GetPlatformContextPickId();
    }

    /**
     * Table to join for platform_visiblity
     *
     * @return string
     */
    public function getPlatformContextJoinTable() : string
    {
        return "v_affiliateentity";
    }
}