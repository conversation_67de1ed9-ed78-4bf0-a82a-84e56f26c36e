<?php
class LocationFilterPickPicker extends NPicker
{

    function __construct()
    {
        $param = array('entity' => 'locationfilterpick',
        'fields'        => array('PICKID'),
        'pickfield'     => 'PICKID',
        'sortcolumn'    =>  'sorttype:a,pickid:a',
        'suppressPrivate' =>true,
        'helpfile'      => ''
        );


        //For Owner Distribution
        $showentitynogroup = Request::$r->_showentitynogroup;
        if ($showentitynogroup) {
            $addparam = array('fieldlabels'    => array('Entity'),
               'title' => 'IA.ENTITY');
            $param = INTACCTarray_merge($param, $addparam);
        }

        parent::__construct($param);

        parent::setLocationFilterParams();

    }

    /**
     * @return array
     */
    function BuildQuerySpec()
    {
        $qspec = parent::BuildQuerySpec();
        // for 1099 picker
        if ($this->showonlyentity) {
            $cny = GetMyCompany();
            $subquery = array("
							SELECT (ID || '--' || TRIM(NAME)) ENTITYGRPPICK
							FROM LOCATIONGROUP 
							WHERE
							CNY# = $cny AND
							RECORD# NOT IN
							(
							  SELECT LOCATIONGROUP 
							  FROM LOCATIONGRPMEMBERS B
							  WHERE 
								CNY# = $cny AND
									EXISTS 
										( SELECT 1 
										  FROM LOCATION 
										  WHERE CNY# = B.CNY# 
													AND RECORD# = B.LOCATION 
													AND LOCATIONTYPE != 'E' 
										)
							)
						");
            $qspec['filters'][0][] = array (
            'operator' => 'or',
            'filters' => array(
            array('LOCATIONTYPE', '=', 'E'), 
            array(
            'operator' => 'and',
            'filters' => array(
             array('LOCATIONTYPE', '=', 'G'), 
             array('PICKID', 'INSUBQUERY', $subquery), 
            ),    
            ),
            )
            );
        }
        
        //For Owner Distribution
        if ($this->showentitynogroup) {
            $qspec['filters'][0][] = array('LOCATIONTYPE', '=', 'E');
        }

        //For German GoBD and Datev Reports
        if ($this->showonlygermanentity) {
            $cny = GetMyCompany();
            $subquery = array("
                SELECT (LOCATION_NO || '--' || TRIM(NAME)) LOCATIONPICK
                FROM LOCATION
                WHERE
                CNY# = $cny AND
                LOCATIONTYPE = 'E' AND TAXSOLUTIONKEY IN (
                  SELECT RECORD#
                  FROM TAXSOLUTION
                  WHERE CNY# = $cny
                  AND SOLUTIONID = 'Deutsche Umsatzsteuer - SYS')
            ");
            $qspec['filters'][0][] = array('PICKID', 'INSUBQUERY', $subquery);
        }

        // for 1099 E file location filter
        if ($this->showentityandgroup) {
            $qspec['filters'][0][] = array('LOCATIONTYPE', 'IN', ['E','G']);
        }
        
        return $qspec;
    }

    /**
     * @return array
     */
    function BuildQuerySpecAll()
    {
        return $this->BuildQuerySpec();
    }


    /**
     * @return string xml tags
     */
    function genGlobs()
    {
        $showonlyentity = isl_htmlspecialchars(Request::$r->_showonlyentity);
        $showentitynogroup = isl_htmlspecialchars(Request::$r->_showentitynogroup);
        $showonlygermanentity = isl_htmlspecialchars(Request::$r->_showonlygermanentity);
        $ret = parent::genGlobs();
        $ret .= "<g name='.showonlyentity'>" . $showonlyentity . "</g>";
        $ret .= "<g name='.showentitynogroup'>" . $showentitynogroup . "</g>";
        $ret .= "<g name='.showonlygermanentity'>" . $showonlygermanentity . "</g>";
        return $ret;
    }


}
