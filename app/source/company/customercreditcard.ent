<?  
/**
 *    FILE:        customercreditcard.ent
 *    AUTHOR:        <PERSON><PERSON><PERSON>
 *    DESCRIPTION: 
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */


    require_once 'backend_creditcard.inc';
    require_once 'country.inc';
    require_once 'isl.inc';
    $_op = Request::$r->_op;

    global $kCCPaymentMethods;
    global $kCCPaymentMethodLabels;

    $agreement = 'service_agreement';
    $currYear = intval(GetYearFromDate(GetCurrentDate()));
for ($i = 1999; $i <= $currYear +20; $i++) {
    $yearOptsArray[] = strval($i);
}
    GetCompanyPreferences($prefs);
if ($prefs['USESAGREEMENT']) {
    $agreement = $prefs['USESAGREEMENT'];
}

/** @noinspection PhpUndefinedVariableInspection */
$kSchemas['customercreditcard'] = array (

        'children' => array(
                'mailaddress' => array( 'fkey' => 'mailaddrkey', 'invfkey' => 'record#', 'table' => 'mailaddress', 'join' => 'outer' ),
                'customer' => array( 'fkey' => 'customerid', 'invfkey' => 'customerid', 'table' => 'customer', 'join' => 'outer' )
            ),
        'object' => array (
            'RECORDNO',
            'CARDID',
            'DESCRIPTION',
            'CARDNUM',
            'CARDTYPE',
            'EXP_MONTH',
            'EXP_YEAR',
            'MAILADDRESS.ADDRESS1',
            'MAILADDRESS.ADDRESS2',
            'MAILADDRESS.CITY',
            'MAILADDRESS.STATE',
            'MAILADDRESS.ZIP',
            'MAILADDRESS.COUNTRY',
            'MAILADDRESS.COUNTRYCODE',
            'STATUS',
            'CUSTOMERID',
            'DEFAULTCARD',
            'USEBILLTOADDR',
            'CUSTOMER.NAME',
            'FINANCIALINSTITUTIONKEY',
            'CUSTOMERRECORDNO',
        ),
        'schema' => array (
            'RECORDNO'    => 'record#',
            'CARDID'    => 'cardid',
            'DESCRIPTION' => 'description',
            'CARDNUM' => 'cardnum',
            'CARDTYPE' => 'cardtype',
            'EXP_MONTH' => 'exp_month',
            'EXP_YEAR' => 'exp_year',
            'MAILADDRESS' => array(
                'mailaddress.*' => 'mailaddress.*'
            ),
            'STATUS' => 'status',
            'CUSTOMERID' => 'customerid',
            'DEFAULTCARD' => 'default_card',
            'USEBILLTOADDR' => 'usebilltoaddr',
            'CUSTOMER.NAME'        => 'customer.name',
            'FINANCIALINSTITUTIONKEY'  =>  'financialinstitutionkey',
            'CUSTOMERRECORDNO' => 'customer.record#',
        ),
        'fieldinfo' => array (
        array ( 
                'fullname' => 'IA.RECORD_NO',
                'type' => array ( 
                    'ptype' => 'integer',
                    'type' => 'integer',
                    'maxlength' => 8,
                ),
                'hidden' => true, 
                'readonly' => true, 
                //'required' => true,
                'desc' => 'IA.RECORD_NO',
                'path' => 'RECORDNO',
                'id' => 1
            ),
            array (
                'fullname' => 'IA.CARD_ID',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text', 
                    'maxlength' => 40, 
                    'size' => 40, 
                    'format' => '/^.{1,40}$/' 
                ),
                'hidden' => true, 
                'required' => false,
                'desc' => 'IA.CARDID',
                'path' => 'CARDID',
                'id' => 2
            ),
            array (
                'fullname' => 'IA.DESCRIPTION',
                'type' => array ( 
                    'ptype' => 'textarea', 
                    'type' => 'textarea', 
                    'maxlength' => 80, 
                    'format' => '/^.{1,80}$/' 
                ),
                    'numofrows' => 5, 
                    'numofcols' => 60, 
                'required' => false,
                'desc' => 'IA.DESCRIPTION',
                'path' => 'DESCRIPTION',
                'id' => 3
            ),
            array (
                'fullname' => 'IA.CARD_NUMBER',
                'type' => array ( 
                    'ptype' => 'mask', 
                    'type' => 'mask', 
                    'maxlength' => 16, 
                    'size' => 24 
                ),
                'required' => true,
                'desc' => 'IA.CARD_NUMBER',
                'path' => 'CARDNUM',
                'id' => 4
            ),
            array (
                'fullname' => 'IA.CARD_TYPE',
                'type' => array ( 
                    'ptype' => 'enum', 'type' => 'enum', 
                    'validvalues' => array_values($kCCPaymentMethods),
                    '_validivalues' => array_keys($kCCPaymentMethods),
                    'validlabels' => array_values($kCCPaymentMethodLabels),
                ),
                'required' => true,
                'desc' => 'IA.CARD_TYPE',
                'path' => 'CARDTYPE',
                'id' => 5
            ),
            array (
                'fullname' => 'IA.EXPIRATION_MONTH',
                'type' => array ( 
                    'ptype' => 'enum', 'type' => 'enum', 
                    'validlabels' => array('IA.JANUARY', 'IA.FEBRUARY', 'IA.MARCH', 'IA.APRIL', 'IA.MAY', 'IA.JUNE', 'IA.JULY', 'IA.AUGUST', 'IA.SEPTEMBER', 'IA.OCTOBER', 'IA.NOVEMBER', 'IA.DECEMBER'),    
                    'validvalues' => array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'),
                    '_validivalues' => array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12')
                ),
                'required' => true,
                'desc' => 'IA.EXPIRATION_MONTH',
                'path' => 'EXP_MONTH',
                'id' => 6
            ),
            array (
                'fullname' => 'IA.EXPIRATION_YEAR',
                'type' => array ( 
                    'ptype' => 'enum', 'type' => 'enum', 
                    'validlabels' =>  $yearOptsArray,
                    'validvalues' => $yearOptsArray,
                    '_validivalues' => $yearOptsArray,
                ),
                'required' => true,
                'desc' => 'IA.EXPIRATION_YEAR',
                'path' => 'EXP_YEAR',
                'id' => 7
            ),
            array (
                'fullname' => 'IA.ADDRESS_LINE_1',
                'desc' => 'IA.ADDRESS_LINE_1',
                'required' => false,
                'path' => 'MAILADDRESS.ADDRESS1',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text', 
                    'maxlength' => 100, 
                    'size' => 75, 
                ),
                'id' => 8
            ),
            array (
                'fullname' => 'IA.ADDRESS_LINE_2',
                'desc' => 'IA.ADDRESS_LINE_2',
                'path' => 'MAILADDRESS.ADDRESS2',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text', 
                    'maxlength' => 100, 
                    'size' => 75, 
                ),
                'id' => 9
            ),
            array (
                'fullname' => 'IA.CITY',
                'type' => array ( 'ptype' => 'text', 'type' => 'text', 'maxlength' => 80 ),
                'desc' => 'IA.CITY',
                'required' => false,
                'path' => 'MAILADDRESS.CITY',
                'id' => 10
            ),
            array (
                'type' => array ( 'ptype' => 'text', 'type' => 'text', 'maxlength' => 40 ),
                'required' => false,
                'path' => 'MAILADDRESS.STATE',
                'id' => 11
            ),
            array (
                'type' => array ( 'ptype' => 'text', 'type' => 'text', 'maxlength' => 30 ),
                'required' => false,
                'path' => 'MAILADDRESS.ZIP',
                'id' => 12
            ),
            array (
                'fullname' => 'IA.COUNTRY',
                'type' => array ( 'ptype' => 'text', 'type' => 'text', 'maxlength' => 60 ),
                'desc' => 'IA.COUNTRY',
                'default' => 'USA',
                'path' => 'MAILADDRESS.COUNTRY',
                'id' => 13
            ),
            array (
                'fullname' => 'IA.COUNTRY_CODE',
                'type' => array (
                    'ptype' => 'enum',
                    'type' => 'enum',
                    'validlabels' => GetCountryCodeList(!isl_str_endswith(GetOperation($_op), "/create")),        /* We default to US on new */
                    'validvalues' => GetCountryCodeList(!isl_str_endswith(GetOperation($_op), "/create")),
                ),
                'desc' => 'IA.COUNTRY_CODE',
                'required' => false,
                'path' => 'MAILADDRESS.COUNTRYCODE',
                'id' => 14
            ),
            $gStatusFieldInfo,
            array(
                'fullname' => 'IA.CUSTOMER_ID',
                'required' => true,
                'type' => array (
                        'ptype' => 'ptr', 
                        'type' => 'ptr',
                        'entity' => 'customer',
                        'pickentity' => 'customerpick',
                        'maxlength' => 20,
                        'size' => 20,
                    ),
                'desc' => 'IA.UNIQUE_ID_OF_CUSTOMER',
                'path' => 'CUSTOMERID',
                'renameable' => true,
                'onchange' => 'ResetDefaultCard(true);',
                'id' => 15
            ),
            array (
                'fullname' => 'IA.DEFAULT_CARD',
                'type' => $gBooleanType, 
                'default' => 'false',
                'desc' => 'IA.DEFAULT_CARD',
                'path' => 'DEFAULTCARD',
                'onchange' => "ResetDefaultCard(false);",
                'id' => 16
            ),                      
            array (
                'fullname' => 'IA.CUSTOMER_NAME',
                'desc' => 'IA.CUSTOMER_NAME',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text', 
                ),
                'required' => false,
                'renameable' => true,
                'path' => 'CUSTOMER.NAME',
                'id' => 17
            ),
            array (
                'fullname' => 'IA.CUSTOMER_KEY',
                'desc' => 'IA.CUSTOMER_KEY',
                'type' => array(
                    'type' => 'integer',
                    'ptype' => 'sequence',
                    'maxlength' => 8,
                    'size' => 8,
                    'format' => $gRecordNoFormat,
                ),
                'required' => false,
                'readonly' => true,
                'path' => 'CUSTOMERRECORDNO',
                'id' => 20
            ),
            array (
                'fullname' => 'IA.USE_BILLTO_CONTACT_ADDRESS_FOR_VERIFICATION',
                'type' => $gBooleanType, 
                'default' => 'true',
                'desc' => 'IA.USE_BILL_TO_ADDRESS',
                'path' => 'USEBILLTOADDR',
                'id' => 18
            ),
            array (
            'fullname' => 'IA.KEY_SELECT_TO_USE_THE_CUSTOMER_S_BILL_TO',
            'desc' => 'IA.USE_BILL_TO_HELPER',
            'type' => array (
            'ptype' => 'text',
            'type' => 'text',
            ),
            'path' => 'BILLTOHELPER',
            'readonly' => true,
            'default' => '',
            ),
        ),
        'table' => 'custcreditcard',
        'autoincrement' => 'RECORDNO',
        'parententity' => 'financialinstitution',
        'vid' => 'RECORDNO',
        'module' => 'co',
        'printas' => 'IA.CUSTOMER_CREDIT_CARD',
        'pluralprintas' => 'IA.CUSTOMER_CREDIT_CARDS',
        'renameable' => true,
        'nochatter' => true,
        'api' => array(
            'PERMISSION_READ'   => 'lists/customercreditcard/view',
            'PERMISSION_CREATE' => 'lists/customercreditcard/create',
            'PERMISSION_UPDATE' => 'lists/customercreditcard/edit',
            'PERMISSION_DELETE' => 'lists/customercreditcard/delete',
        ),
        'audittrail_unneeded_fields' => array(
            'CARDNUM'=>'','EXP_MONTH'=>'','EXP_YEAR'=>'','MAILADDRESS'=>''
        ),
        'maskparms' => true,
        
    );

