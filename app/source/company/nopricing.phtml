<?  
//=============================================================================
//
//	FILE:			noauthorization.phtml
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================
 
    require_once 'util.inc';
    require_once 'html_header.inc';
    // to prevent JS/SQL injections
    Request::FilterRequest();
    InitBase();
    InitProfile();
    InitGlobals();
    InitModules();
    InitAuthorization();

    $props = array();
    $props['nocheck'] = true;
    $props['nojs'] = true;
    $props['nohotkey'] = true;
    $props['nobeancss'] = true;
    $props['incJSValidationFiles'] = false;
    $props['title'] = 'Sorry, No Pricing';
    PrintCommonHtmlHeader($props);

    require_once 'js_common.inc' 
?>

<table border="0" width="100%" height="80%"><tr><td width="100%" height="100%" align="center" valign="middle">
<table border="0" cellpadding="1" cellspacing="0" bgcolor="#336699" width="1%"><tr><td>
<table border="0" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" width="1%">
<tr><td><img src="../images/misc/pricing.gif" alt="" width="400" height="106" border="0"></td></tr>
<tr><td>

<table border="0" cellpadding="10" width="100%"><tr><td align="center">



<font face="Verdana, Arial, Helvetica" size="2">
Sorry, There is no online pricing information available for this module.
Please contact Intacct Customer Service for pricing information. </font><p>
<p>
<font face="Verdana, Arial, Helvetica" size="2">Click 
<a href="javascript: history.back()"
	ONMOUSEOVER='window.status="<? echo statusdisp('Previous'); ?>"; return true;' 
	ONFOCUS='window.status="<? echo statusdisp('Previous'); ?>"; return true;' 
	ONMOUSEMOVE='window.status="<? echo statusdisp('Previous'); ?>"; return true;'
	ONMOUSEOUT ="window.status=''; return true;"
	ONBLUR ="window.status=''; return true;">
<b>here</b></a> to return to the previous screen.</font>


<? //echo RetUrl(); ?>

<p>&nbsp;<p>

</td></tr></table>
</td></tr></table>
</td></tr></table>
</td></tr></table>


</body>
</html>
