<?php

/**
 * XE
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation, All Rights Reserved
 */


/**
 * Communicate with XE.com to get rates
 */
class XE
{
    /* @var string $apiKey */
    private static $apiKey;
    
    const MAX_REQUESTS = 15;

    /**
     * getRates
     *
     * @param string $baseCurrency   base currency
     * @param array  $fromCurrencies currency list
     * @param array  $exchRateDates  exchange rate dates
     *
     * @return array|bool exchange rates (['fromcurrency']['date'] = 'rate') )
     */
    public static function getRates($baseCurrency, $fromCurrencies, $exchRateDates)
    {
        $urls = array();
        $intacctExchRates = array();
        $urlExchRateDate = array();
        $newCurrencies = IntacctRate::getNewCurrencies();
        $startDate = '';
        $endDate = '';
        $startDateMDY = '';
        $endDateMDY = '';

        //collect all exchange rate dates and sort it to send start and end date
        foreach ($exchRateDates as $exchRateDate) {
            $previousDay = AddDays($exchRateDate, -1);
            $urlExchRateDate[$exchRateDate] = ReformatDate($previousDay, '/mdY', '-Ymd');
        }
        asort($urlExchRateDate);
        $urlExchRateDate = array_flip($urlExchRateDate);

        // get first and last date
        $cnt = 1;
        $dateCount = count($urlExchRateDate);

        foreach ($urlExchRateDate as $key => $date) {
            if ($cnt == 1) {
                $startDate = $key;
                $startDateMDY = AddDays($date, -1);
            }
            if ($cnt == $dateCount) {
                $endDate = $key;
                $endDateMDY = AddDays($date, -1);
            }

            ++$cnt;
        }

        if (empty($startDate) || empty($endDate)) {
            throw new Exception ("Start date or End date cannot be empty!");
        }

        $bitCoinCode = false;
        if ($baseCurrency == 'BTC') {
            $baseCurrency = 'XBT';
            $bitCoinCode = true;
        }

        //build urls
        $urlCurrencies = array();
        $fetch = false;
        foreach ( $fromCurrencies as $fromCur) {

            if ($fromCur == 'BTC') {
                $fromCur = 'XBT';
                $bitCoinCode = true;
            }

            // check effective start date for new currencies
            if (isset($newCurrencies[$fromCur])) {
                $newStartDate = $startDate;
                if (SysDateCompare($startDateMDY, $newCurrencies[$fromCur]) < 0) {
                    $newStartDate = ReformatDate($newCurrencies[$fromCur], '/mdY', '-Ymd');
                }
                if (SysDateCompare($endDateMDY, $newCurrencies[$fromCur]) < 0) {
                    continue;
                }
                $urls[] = self::buildUrl(array($fromCur), $baseCurrency, $newStartDate, $endDate);
                $fetch = true;
                continue;
            } else if (isset($newCurrencies[$baseCurrency])) {
                $newStartDate = $startDate;
                if (SysDateCompare($startDateMDY, $newCurrencies[$baseCurrency]) < 0) {
                    $newStartDate = ReformatDate($newCurrencies[$baseCurrency], '/mdY', '-Ymd');
                }
                if (SysDateCompare($endDateMDY, $newCurrencies[$baseCurrency]) < 0) {
                    continue;
                }
                $urls[] = self::buildUrl(array($fromCur), $baseCurrency, $newStartDate, $endDate);
                $fetch = true;
                continue;
            }
            
            $urlCurrencies[] = $fromCur;
            $fetch = true;
        }

        // if no need to fetch then it is not an error.. just return true
        if (!$fetch) {
            return true;
        }
        if (!empty($urlCurrencies)) {
            $urls[] = self::buildUrl($urlCurrencies, $baseCurrency, $startDate, $endDate);
        }

        $apiKey = self::getAPIKey();

        // send all URLs
        LogToFile("XE: sending requests\n");
        $response = Util::curlParallelRequests($urls, self::MAX_REQUESTS, $apiKey);
        LogToFile("XE: received the response\n");

        if (empty($response)) {
            throw new Exception ("No response from XE call.");
        }

        //prepare the array from response
        if ($dateCount > 1) {
            //handling multiple dates response
            foreach ( $response as $fetchvalue) {

                if (empty($fetchvalue) || strstr($fetchvalue, "code")) {
                    throw new Exception ("Error executing XE call " . $fetchvalue);
                }

                $fetchvalue = Util_DataRecordFormatter::jsonToPhp($fetchvalue);

                foreach ($fetchvalue['to'] as $fCur => $xRates) {

                    foreach ($xRates as $rates) {
                        list($date) = explode('T', $rates['timestamp']);

                        if ($bitCoinCode) {
                            if ($fCur == 'XBT') {
                                $fCur = 'BTC';
                            }
                        }

                        if (isset($urlExchRateDate[$date])) {

                            $exchRateDate = $urlExchRateDate[$date];

                            if (
                                empty($fCur)
                                || empty($exchRateDate)
                                || empty($rates['inverse'])
                                || !is_numeric($rates['inverse'])
                            ) {
                                throw new Exception ("Error processing XE's reponse");
                            }

                            $intacctExchRates[$fCur][$exchRateDate]['RATE'] = $rates['inverse'];
                        }
                    }
                }
            }
        } else {
            //handling single date response
            foreach ( $response as $fetchvalue) {

                if (empty($fetchvalue) || strstr($fetchvalue, "code")) {
                    throw new Exception ("Error executing XE call " . $fetchvalue);
                }

                $fetchvalue = Util_DataRecordFormatter::jsonToPhp($fetchvalue);

                list($date) = explode('T', $fetchvalue['timestamp']);
                
                foreach ($fetchvalue['to'] as $rates) {

                    $fCur = $rates['quotecurrency'];
                    if ($bitCoinCode) {
                        if ($fCur == 'XBT') {
                            $fCur = 'BTC';
                        }
                    }

                    $exchRateDate = $urlExchRateDate[$date];

                    if (
                        empty($fCur)
                        || empty($exchRateDate)
                        || empty($rates['inverse'])
                        || !is_numeric($rates['inverse'])
                    ) {
                        throw new Exception ("Error processing XE's reponse");
                    }

                    $intacctExchRates[$fCur][$exchRateDate]['RATE'] = $rates['inverse'];
                }
            }
        }

        return $intacctExchRates;
    }

    /**
     * build XE URL
     *
     * @param array  $fromCurs     from currencies
     * @param string $baseCurrency base currency
     * @param string $startDate    start date
     * @param string $endDate      end date
     *
     * @return string
     */
    private static function buildUrl($fromCurs, $baseCurrency, $startDate, $endDate)
    {
        $fromCur = implode(",", $fromCurs);
        if ( $startDate != $endDate ) {
            $url = "https://xecdapi.xe.com/v1/historic_rate/period.json/?from=$baseCurrency&to=$fromCur"
                . "&start_timestamp=$startDate&end_timestamp=$endDate&inverse=true&per_page=31";
        } else {
            $url = "https://xecdapi.xe.com/v1/historic_rate.json/?from=$baseCurrency&to=$fromCur"
                . "&date=$startDate&inverse=true";
        }
        
        return $url;
    }

    /**
     * get API Key of XE
     *
     * @return string
     */
    private static function getAPIKey()
    {
        if (empty(self::$apiKey)) {
            self::$apiKey = GetValueForIACFGProperty("XE_API_KEY", ia_cfg::DECRYPT_KEY);
        }

        return self::$apiKey;
    }
    
}