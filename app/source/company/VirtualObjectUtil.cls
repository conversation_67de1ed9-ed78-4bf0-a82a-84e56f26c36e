<?php
 /**
 * File VirtualObjectUtil.cls contains the class VirtualObjectUtil
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
 

class VirtualObjectUtil
{
    /**
     * @param array $entities
     * @param array $object
     * @param array $fieldInfo
     */
    public static function MergeFieldInfo($entities, &$object, &$fieldInfo)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;

        foreach ( $entities as $entity ) {

            $entMgr = $gManagerFactory->getManager($entity);

            // Custom fields
            $custFields = $entMgr->GetCustomFields();

            if ($custFields !== null) {
                foreach ( $custFields as $cf ) {
                    $cfFieldInfo = $cf->MergeFieldInfo();
                    $fieldInfo[] = $cfFieldInfo;
                    $object[] = $cfFieldInfo['path'];
                }
            }
        }

    }

    /**
     * @param array $entities
     * @param array $object
     * @param array $fieldInfo
     */
    public static function MergePlatformRelationships($entities, &$object, &$fieldInfo)
    {
        /**
 * @var ManagerFactory $gManagerFactory 
*/
        $gManagerFactory = Globals::$g->gManagerFactory;

        $relFields = array();
        foreach ( $entities as $entity ) {

            /**
 * @var EntityManager $entMgr 
*/
            $entMgr = $gManagerFactory->getManager($entity);

            $myId = Util_StandardObjectMap::getObjectId($entity);
            if ($myId == null) {
                continue;
            }

            $relationshipDefs = Pt_RelationshipDefManager::getByObjectDef($myId);
            if (count($relationshipDefs) == 0) {
                continue;
            }

            foreach( $relationshipDefs as $relDef ) {

                // Repeating logic from addPlatformRelationshipField

                $mySide = ($myId == $relDef->getObjectDefId1()) ? 1 : 2;
                $otherObjDef = ( $mySide == 1 ) ? Pt_DataObjectDefManager::getById($relDef->getObjectDefId2())
                    : Pt_DataObjectDefManager::getById($relDef->getObjectDefId1());

                // TODO: this is just so we can keep working.  Need to clean this up.  The problem we're facing
                // is that the relationship may be between two standard objects.  Depending on the sequence
                // those objects are loaded into the cache, one side or the other may not have an
                // object definition yet.
                if (!$otherObjDef instanceof Pt_DataObjectDef) {
                    continue;
                }

                // if the other side is multiple, we don't want to do anything here
                if ($mySide == 1 && $relDef->isMultiple2()) {
                    continue;
                }
                else if ($mySide == 2 && $relDef->isMultiple1()) {
                    continue;
                }

                $rawRelationFieldDefNoCache = $entMgr->getRawRelationFieldDefNoCache();

                //get the fieldname
                foreach ( $rawRelationFieldDefNoCache[$myId ] as $rawfield ) {
                    if ( isl_strpos($rawfield['PROPERTIES'], $relDef->getId()) !== false ) {
                        $fieldName = isl_strtoupper($rawfield['FIELD_NAME']);
                        break;
                    }
                }

                if ($mySide == 1) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $relFields[$fieldName ][] = $relDef->getSingularName2() . ' Record Number';
                }
                else {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $relFields[$fieldName ][] = $relDef->getSingularName1() . ' Record Number';
                }
            }
        }

        $id = 10000;
        foreach ( $relFields as $fieldName => $fullNames ) {

            $unqiueFullNames = INTACCTarray_unique($fullNames);

            $object[] = $fieldName;
            $fieldInfo[] = array(
                'path' => $fieldName,
                'fullname' => implode(", ", $unqiueFullNames),
                'type'      =>  array (
                    'ptype'     =>  'integer',
                    'type'      =>  'integer',
                ),
                'id' => $id++
            );
        }
    }
}