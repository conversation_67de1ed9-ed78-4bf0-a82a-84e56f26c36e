<?php
/** 
 * Manager for group users view
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2001 Intacct Corporation, All Rights Reserved
 */

/**
 * Manager for group users view
 */
class GroupUsersManager extends EntityManager
{
    /**
     * Initialize the manager.
     *
     * @param array $params  the parameters
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
    
    /**
     * Retrieve the list of users for the given user group
     * 
     * @param int $groupkey  the user group key
     * 
     * @return array  the list of users of that group
     */
    public function getGroupUsers($groupkey)
    {
        $res = $this->GetList(
            array(
                'selects' => array('USERKEY', 'USERLOGINID'),
                'filters' => array( array( array('GROUPKEY', '=', $groupkey) ) ),
            )
        );
        
        $users = array();
        foreach ( $res as $row ) {
            $users[$row['USERKEY']] = $row['USERLOGINID'];
        }
        
        return $users;
    }
}