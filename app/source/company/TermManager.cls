<?
/**
 *    FILE:            termmanager.cls
 *    AUTHOR:            <PERSON>/rpn
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

require_once 'backend_term.inc';

class TermManager extends EntityManager
{
    /**
     * @param string $seqname
     *
     * @return int
     */
    function GetNextSequence($seqname)
    {
        global $gManagerFactory;
        /* @var CoSetupManager $myMM  */
        $myMM = $gManagerFactory->getManager('cosetup');

        if ($seqname == '') {
            $seqname = 'TERM';
        }
        return($myMM->GetNextSequence($seqname));
    }

    /**
     * Get a single record
     * decode the value string to term string which could be displayed
     *
     * @param string $ID
     * @param string[]|null   $fields
     *
     * @return array|bool
     */
    function Get($ID, $fields=null) 
    {
        $obj = parent::get($ID, $fields);

        //  Don't return object if it was not found.  Otherwise, an empty object gets merged with fields below
        //   to form a non-existing object.
        if (!is_array($obj) || count($obj) == 0) {
            return null;
        }
        // parse the term value into the displayable control values for due, discount and penalties
        $nvalues= SetFromValues($obj);
        // merge the result set and the parsed values
        $fullobj = INTACCTarray_merge($obj, $nvalues);
        return $fullobj;
    }

    
    /**
     * API entry point for the Add method.  For term objects, the VALUE field is read-only on Add and Set.
     *
     * @param array $values
     *
     * @return bool
     */
    function API_Add(&$values) 
    {
        if (isset($values['VALUE'])) {
            throw new Exception("The VALUE field is read-only for AP/AR Term objects");
        }
        return parent::API_Add($values);
    }

    /**
     * API entry point that merges given update values with existing values.
     *  For term objects, the VALUE field is read-only for Set, so make sure the VALUE
     *  is not passed in the inputs.
     *
     * @param array $object Existing object values
     * @param array $values Newly passed in values
     *
     * @return array merged values
     */
    function API_UpdateMerge(&$object, &$values) 
    {
        if (isset($values['VALUE'])) {
            throw new Exception("The VALUE field is read-only for AP/AR Term objects");
        }
        return parent::API_UpdateMerge($object, $values);
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        return $this->_addWithOpts($values, false);
    }

    /**
     * @param array $values
     * @param bool  $spawnTerm
     *
     * @return bool
     */
    function addWithOpts(&$values, $spawnTerm)
    {
        return $this->_addWithOpts($values, $spawnTerm);
    }

    /**
     * @param array $values
     * @param bool  $spawnTerm
     *
     * @return bool
     */
    private function _addWithOpts(&$values, $spawnTerm)
    {
        global $gErr;

        $source = "TermManager::Add";

        $ok = $this->_QM->beginTrx($source);

        $nextId = $this->GetNextRecordKey();
        $ok = $ok && isset($nextId);
        $values[':record#'] = $nextId;
        // force base class from generating new record number
        $values['RECORDNO'] = $nextId;
        
        if ($spawnTerm) {
            $values[':value'] = $values['VALUE'];
            unset($values['VALUE']);
        } else {
            $nvalues=SetFromFields($values);
            // eppp($nvalues);
            $values[':value'] = $nvalues;
        }

        if (!$spawnTerm) {
            // Get which label we really are 
            $minimod = isl_strtolower(isl_substr(get_class($this), 0, 2));
            if ($minimod == 'ap') {
                $values[':modulekey'] = '3.AP';
            }
            else {
                $values[':modulekey'] = '4.AR';
            }
        } else {
            $values['STATUS'] = $this->_ItoEstatus($values['STATUS']);
        }
        $ok = $ok && $this->CheckTerm($values);
        $ok = $ok && parent::regularAdd($values);
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if (HasErrors()) {
                $msg = _("Could not create Term record!");
                $gErr->addIAError('CO-0959', __FILE__ . ':' . __LINE__, $msg);
                epp("$source: Error: $msg");
            }
            
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        global $gErr;

        $source = "TermManager::Set";

        $ok = $this->_QM->beginTrx($source);

        $rawrow = $this->GetRaw($values['NAME']);
        $ID = $rawrow[0]['RECORD#'];
        $ok = $ok && isset($ID);
        $values[':record#'] = $ID;

        $nvalues=SetFromFields($values);
        // eppp($nvalues);
        $values[':value'] = $nvalues;

        // Get which label we really are 
        $minimod = isl_strtolower(isl_substr(get_class($this), 0, 2));
        if ($minimod == 'ap') {
            $values[':modulekey'] = '3.AP';
        }
        else {
            $values[':modulekey'] = '4.AR';
        }
        $ok = $ok && $this->CheckTerm($values);
        $ok = $ok && $this->CheckCalculationBasedOn($values);
        $modflag = $this->CheckIfModified($values);
        $ok = $ok && parent::regularSet($values);

        if($ok && $modflag) {
            TimeStampModifiedTerm($values[':record#']);
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if (HasErrors()) {
                $msg = _("Could not set Term record!");
                $gErr->addIAError('CO-0960', __FILE__ . ':' . __LINE__, $msg);
                epp("$source: Error: $msg");
            }
            
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    function CheckCalculationBasedOn(&$values)
    {
        global $gErr;
        $ok = true;

        // First check the term has a valid discount
        if (TermManager::isTermWithDiscount($values)) {

            // Next get the original/from radio button
            $termKey = $values['RECORDNO'];
            $query = ['selects' => ['DISCCALCON'], 'filters' => [[['RECORDNO', '=', $termKey]]]];
            $result = $this->GetList($query);
            if (Util::countOrZero($result) > 0) {
                // Detect a switch from radio button #1 to radio button #2 or #3,
                // or a switch from radio button #2 or #3 to radio button #1
                $fromRadioButtonOne = $result[0]['DISCCALCON'] === 'Line items total' || $result[0]['DISCCALCON'] === 'L';
                $toRadioButtonTwoOrThree = !($values['DISCCALCON'] === 'Line items total' || $values['DISCCALCON'] === 'L');
                if (($fromRadioButtonOne && $toRadioButtonTwoOrThree)) {
                    // Now we want to see if there are any partial payment invoices with outstanding balances
                    $arInvoiceMgr = Globals::$g->gManagerFactory->getManager('arinvoice');

                    $query = ['filters' => [[['TAXMETHOD', '=', 'VAT'], ['TOTALPAID', '>', 0], ['TRX_TOTALDISCOUNTAPPLIED', '>', 0], ['TERMKEY', '=', $termKey]]]];
                    // Note:  During development we included the condition ['TOTALDUE', '>', 0] to make this apply to
                    //        partial payments only, but this was removed to restrict with any payment level including
                    //        fully paid.
                    $count = (int)$arInvoiceMgr->GetCount($query);
                    if ($count > 0) {
                        $msg = 'You cannot change the term definition to use the selected calculation method because this term is used in one or more invoices with payments.';
                        $corr = 'Create a new term with the selected calculation method.';
                        $gErr->addIAError('CO-1091', GetFL(), $msg, [], '', [], $corr);
                        $ok = false;
                    }
                }
            }
        }
        return $ok;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    function CheckTerm(&$values)
    {
        // 		eppp ('CheckTerm');
        // 		eppp ($values);
        /** @noinspection PhpUnusedLocalVariableInspection */
        $source = "TermManager::CheckTerm";
        global $gErr;
        global $days_array;
        $newTermIndexes = [ 'AE', 'EE'];
        $exumptedIndexes = array_merge(['99'], $newTermIndexes);
       
        $state  = Request::$r->_state;
        $dueFromIndex = $this->TransformValue('DUEFROM',$values['DUEFROM'],0);
        $discFromIndex = $this->TransformValue('DISCFROM',$values['DISCFROM'],0);

        // we allow discounts & penalty for multitax companies when they are used in non-VAT transactions
        // but since single tax companies today allow only VAT, we should block them like before
        $isVatDiscountSupported = false;
        if (TaxSetupManager::isTaxModuleConfigured()) {
            $modulekey = $values[':modulekey'] ?? null;
            // If the term doesn't have modulekey find it from its parent term
            if (empty($modulekey) && !empty($values['PARENTTERM'])) {
                GetTerm($values['PARENTTERM'], $parentTerm);
                $modulekey = $parentTerm['MODULEKEY'] ?? null;
            }

            $isVatDiscountSupported = TaxSetupManager::isVatDiscountSupportedModule($modulekey);
            if (TaxSetupManager::isSingleTaxJurisdictionConfigured()) {
                $taxSoln = TaxSolutionManager::getSingleTaxSolution();
                $taxSolnId = empty($taxSoln) ? '' : $taxSoln['SOLUTIONID'];
                $isTermDiscountSupported = $isVatDiscountSupported || TaxSolutionManager::isTermWithDiscountSupportedTaxSoln($taxSolnId);
                if (!$isTermDiscountSupported && TermManager::isTermWithDiscount($values)) {
                    $msg = 'You cannot use Terms with discount or penalty because you are using singletax jurisdiction.';
                    $corr = 'Use a Term without discount and penalty.';
                    $gErr->addIAError('CO-0961', GetFL(), $msg, [], '', [], $corr);
                } else if (TermManager::isTermWithPenalty($values)) {
                    $msg = 'You cannot use Terms with penalty because you are using singletax jurisdiction.';
                    $corr = 'Use a Term without penalty.';
                    $gErr->addIAError('CO-1063', GetFL(), $msg, [], '', [], $corr);
                }
            }
        }

        // API or CSV: For non-vat company or discount not supported module, block the calculating discount on taxes option
        if (!$isVatDiscountSupported && ($values['DISCCALCON'] ?? null) === 'VAT line items total') {
            $msg = 'To apply a term discount that calculates on the line level including taxes, your transaction must use a VAT or GST tax solution.';
            $corr = 'Select a term discount with a valid calculation option to apply to your transaction.';
            $gErr->addIAError('CO-1066', GetFL(), $msg, [], '', [], $corr);
        }

        $dayFields = array('DUEDATE' => 'Due Date',
                           'DISCDATE' => 'Discount Date',
                           'DISCFUDGEDAYS' => 'Grace Days for Discounts',
                           'PENFUDGEDAYS' => 'Grace Days for Penalties');

        foreach($dayFields as $fieldName  => $fieldDescription)
        {
            if(!isset($values[$fieldName])) {
                continue; 
            }

            if(intval($values[$fieldName]) < 0) {
                $gErr->addIAError("CO-0962", "backend_term.inc",
                    "The " . $fieldDescription . " should be an integer greater than or equal to zero.",
                    ['FIELD_DESCRIPTION' => $fieldDescription]
                );
            }
        }

        if (in_array($discFromIndex, $newTermIndexes) && ($discFromIndex != $dueFromIndex)) {
            $msg = "Discount term '" . _($values['DISCFROM']) . "' is supported only for due term '" . _($values['DISCFROM']) . "''";
            $gErr->addIAError("CO-0963", __FILE__ . ':' . __LINE__, "", [], $msg, ['DISC_FROM' => $values['DISCFROM']]);
        }

        // To perform this validation all 4 fields need to be set with numerical values
        if (is_numeric($values['DUEDATE']) && is_numeric($values['DISCDATE']) &&
            is_numeric($days_array[$dueFromIndex]) && is_numeric($days_array[$discFromIndex]) &&
            $values['DUEDATE'] + $days_array[$dueFromIndex] < $values['DISCDATE'] + $days_array[$discFromIndex]) {
            $gErr->addIAError("SL-0161", "backend_term.inc", "The discount date should not be greater than the due date");
        }

        if (($this->_EtoIstatus($values['STATUS'])=='F') && ($state != 'shownew')) {
            $raw = $this->GetRaw($values['NAME']);
            $_r = $raw[0]['RECORD#'];
            $ok = CheckForVendCustTermTransaction($_r, 'termkey');

            //Checking for any Term refered in Vendor
            $ok = $ok && CheckForKeyInVendorOrCustomer('vendor', 'termskey', $_r);


            //Checking for any Term refered in Customer
            $ok = $ok && CheckForKeyInVendorOrCustomer('customer', 'termskey', $_r);
            if (!$ok && $this->getWarningValidation() && $this->shouldWarn(null, $values, []) ) {
                $gErr->addIAWarning('AP-0748', GetFL(), 'This term is in use by pending transactions or referenced in vendor or customer records. After deactivation, it cannot be applied to new transactions or used as a default term but remains in effect on existing transactions.');
            }

        }

        if (!in_array($dueFromIndex, $exumptedIndexes)) {
            if($values['DUEDATE'] >31) {
                $gErr->addIAError("CO-0967", __FILE__ . ':' . __LINE__, "DUE DAYS should be less than or equal to 31 days");
            }
        }

        if (!in_array($discFromIndex, $exumptedIndexes)) {
            if($values['DISCDATE'] >31) {
                $gErr->addIAError("CO-0968", __FILE__ . ':' . __LINE__, "DISCOUNT DAYS should be less than or equal to 31 days");
            }
        }
            
        if($values['DISCPERCAMN'] == '%' && (float)isl_trim($values['DISCAMOUNT']) > 99.99) {
            $gErr->addIAError("CO-0969", __FILE__ . ':' . __LINE__, "The discount amount should not be more than 99.99%");
        }

        if((float)$values['DISCAMOUNT'] < 0) {
            $gErr->addIAError("CO-0970", __FILE__ . ':' . __LINE__, "The discount amount should not be less than zero.");
        }

        if ((float)$values['DISCAMOUNT'] > 0 && !isset($values['DISCDATE'])) {
            $gErr->addIAError("CO-0971", __FILE__ . ':' . __LINE__, "DISCOUNT days cannot be blank.");
        }

        if ((float)$values['PENAMOUNT'] < 0) {
            $gErr->addIAError("CO-0972", __FILE__ . ':' . __LINE__, "The penalty amount should not be less than zero.");
        }

        return !HasErrors() && !HasWarnings();
    }

    /**
     * @param int $reserveCnt
     *
     * @return int|false
     */
    function GetNextRecordKey($reserveCnt=1)
    {
        return $this->_MM->GetNextSequence('TERM');
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function CheckIfModified($values)
    {
        $modified = false;
        $lastVersion = $this->Get($values['NAME']);
        if(($lastVersion['VALUE'] != $values[':value'])  
            || ($lastVersion['STATUS'] != $values['STATUS']) 
            || ($lastVersion['DESCRIPTION'] != $values['DESCRIPTION'])
        ) {
            $modified = true;
        }
        return $modified;
    }

    /**
     * We should only fill subscriptions on the child objects.
     *
     * @param string $verb
     * @param string $key
     * @param mixed  $param1
     * @param mixed  $param2
     * @param array  $values
     *
     * @param bool   $fastUpdate
     *
     * @return bool
     */
    function DoEvent($verb, $key, $param1 = null, $param2 = null, $values = [], $fastUpdate = false) {
        if ($this->getEntity() == 'arterm' || $this->getEntity() == 'apterm') {
            parent::DoEvent($verb, $key, null, null, $fastUpdate);
        }
        return true;
    }

    /**
     * check if term contains discounts
     *
     * @param array $term
     *
     * @return bool true if discounts are present
     */
    public static function isTermWithDiscount($term)
    {
        return ( ! ( empty($term['DISCDATE'])
                     && empty($term['DISCAMOUNT'])
                     && empty($term['DISCFUDGEDAYS'])
        ) );
    }

    /**
     * check if term contains penalties
     *
     * @param array $term
     *
     * @return bool true if penalties are present
     */
    public static function isTermWithPenalty($term)
    {
        return ( ! ( ( empty($term['PEN_TYPES']) || $term['PEN_TYPES'] === 'O' || $term['PEN_TYPES'] === 'No Penalty' )
            && empty($term['PENAMOUNT'])
            && empty($term['PENFUDGEDAYS'])
        ) );
    }

    /**
     * check if term is valid for a given tax soln
     *
     * @param array $term
     * @param string $taxSolutionId
     * @param bool &$termWithDiscountSupportedTaxSoln
     *
     * @return bool true if term is valid for the tax soln
     */
    public static function isValidTermForTaxSoln($term, $taxSolutionId, &$termWithDiscountSupportedTaxSoln = false)
    {
        $termWithDiscountSupportedTaxSoln = TaxSolutionManager::isTermWithDiscountSupportedTaxSoln($taxSolutionId);
        // all VAT tax solns don't support penalty, so if penalty is present in term return false
        if ( TermManager::isTermWithPenalty($term) ) {
            return false;
        }
        // if discount is present in term for the tax solns which don't support discount in term, return false
        if ( !$termWithDiscountSupportedTaxSoln && TermManager::isTermWithDiscount($term) ) {
             return false;
        }
        return true;
    }
}

