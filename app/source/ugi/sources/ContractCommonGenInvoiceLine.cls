<?php
/**
 * File ContractCommonGenInvoiceLine.cls contains the class ContractCommonGenInvoiceLine
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Contract gen invoice line business object
 *
 * Class ContractCommonGenInvoiceLine
 */
abstract class ContractCommonGenInvoiceLine extends GenInvoiceLine
{
    /* @var int $contractLineNo */
    private $contractLineNo;
    /* @var string $arBilledAcctNo */
    private $arBilledAcctNo;
    /* @var string $drBilledAcctNo */
    private $drBilledAcctNo;
    /* @var string $priceCalcMemo */
    private $priceCalcMemo;
    /* @var string $itemDesc */
    private $itemDesc;
    /* @var string $servicePeriodStartDate */
    private $servicePeriodStartDate;
    /* @var string $servicePeriodEndDate */
    private $servicePeriodEndDate;

    /**
     * Returns contract line no
     *
     * @return int
     */
    public function getContractLineNo()
    {
        return $this->contractLineNo;
    }

    /**
     * Sets contract line no
     *
     * @param int $contractLineNo contract line number
     */
    public function setContractLineNo($contractLineNo)
    {
        $this->contractLineNo = $contractLineNo;
    }

    /**
     * Returns AR billed acct no
     *
     * @return string
     */
    public function getARBilledAcctNo()
    {
        return $this->arBilledAcctNo;
    }

    /**
     * Sets AR billed acct no
     *
     * @param string $arBilledAcctNo account number
     */
    public function setARBilledAcctNo($arBilledAcctNo)
    {
        $this->arBilledAcctNo = $arBilledAcctNo;
    }

    /**
     * Returns DR billed acct no
     *
     * @return string
     */
    public function getDRBilledAcctNo()
    {
        return $this->drBilledAcctNo;
    }

    /**
     * Sets DR billed acct no
     *
     * @param string $drBilledAcctNo account number
     */
    public function setDRBilledAcctNo($drBilledAcctNo)
    {
        $this->drBilledAcctNo = $drBilledAcctNo;
    }

    /**
     * Returns price calc memo
     *
     * @return string
     */
    public function getPriceCalcMemo()
    {
        return $this->priceCalcMemo;
    }

    /**
     * Sets preice calc memo
     *
     * @param int $priceCalcMemo
     */
    public function setPriceCalcMemo($priceCalcMemo)
    {
        $this->priceCalcMemo = $priceCalcMemo;
    }

    /**
     * @return string
     */
    public function getItemDesc()
    {
        return $this->itemDesc;
    }

    /**
     * @param string $itemDesc
     */
    public function setItemDesc($itemDesc)
    {
        $this->itemDesc = $itemDesc;
    }

    /**
     * Returns service period start date
     *
     * @return string
     */
    public function getServicePeriodStartDate()
    {
        return $this->servicePeriodStartDate;
    }

    /**
     * Sets service period start date
     *
     * @param string $servicePeriodStartDate service period start date
     */
    public function setServicePeriodStartDate($servicePeriodStartDate)
    {
        $this->servicePeriodStartDate = $servicePeriodStartDate;
    }

    /**
     * Returns service period end date
     *
     * @return string
     */
    public function getServicePeriodEndDate()
    {
        return $this->servicePeriodEndDate;
    }

    /**
     * Sets service period end date
     *
     * @param string $servicePeriodEndDate service period en date
     */
    public function setServicePeriodEndDate($servicePeriodEndDate)
    {
        $this->servicePeriodEndDate = $servicePeriodEndDate;
    }

    /**
     * Return the cached data for this preview line. At the minimum the returned array must containg LINENO, ARBILLEDACCTNO, and DRBILLEDACCTNO
     *
     * @param int  $previewLineKey
     * @return array
     */
    abstract protected function getCachedContractData($previewLineKey);



    /**
     * loadDetails - load contract billing schedule entry attributes
     */
    public function loadDetails()
    {
        $lineArr = $this->getCachedContractData($this->getRecordNo());

        if ($lineArr !== null) {
            $this->setContractLineNo($lineArr['LINENO']);
            $this->setARBilledAcctNo($lineArr['ARBILLEDACCTNO']);
            $this->setDRBilledAcctNo($lineArr['DRBILLEDACCTNO']);
            $this->setItemDesc($lineArr['ITEMDESCR']);
        }
    }


    /**
     * innerToDocEntry - contract billing schedule specific doc entry attributes
     *
     * @param   array   $docEntry   Doc entry array
     */
    public function innerToDocEntry(array &$docEntry)
    {
        $docEntry['ARBILLEDACCTNO'] = $this->getARBilledAcctNo();
        $docEntry['DRBILLEDACCTNO'] = $this->getDRBilledAcctNo();

        $docEntry['PRICECALCMEMO'] = $this->getPriceCalcMemo();
        $docEntry['ITEMDESC'] = $this->getItemDesc();

        $docEntry['SERVICEPERIODSTARTDATE'] = $this->getServicePeriodStartDate();
        $docEntry['SERVICEPERIODENDDATE'] = $this->getServicePeriodEndDate();
    }

    /**
     * Contract billing schedule specific grid fields are added here
     *
     * @param array $gridLine column values to display on grid line
     */
    public function innerToFormGridLine(&$gridLine)
    {
        $cn_viewop = GenInvoiceUtil::getOpId('cn/lists/contract/view');
        $sess = Session::getKey();

        $cn_url = "editor.phtml?.op=$cn_viewop&.sess=$sess&.r=".$this->getContractId().'&.popup=1';
        $contractIdLineHTML = '<a href=\'javascript:Launch( "' . $cn_url
                                . '" , "contractid");\' target1="_blank">' . $this->getContractId() . '</a>';

        if ($this->getContractId() != null) {
            $gridLine['CONTRACTIDLINEHTML'] = $contractIdLineHTML;
        }

        if ($this->getContractLineNo() != null) {
            $gridLine['CNLINENO'] = $this->getContractLineNo();
            $gridLine['CNITEMDESC'] = $this->getItemDesc();
        }

        $gridLine['SERVICEPERIODSTARTDATE'] = $this->getServicePeriodStartDate();
        $gridLine['SERVICEPERIODENDDATE'] = $this->getServicePeriodEndDate();
    }

    /**
     * Copy service period attributes from raw lines in DB
     *
     * @param string[] $rawLine raw data array from EntityManager
     */
    public function innerFromRawLine($rawLine)
    {
        $this->servicePeriodStartDate = $rawLine['SERVICEPERIODSTARTDATE'];
        $this->servicePeriodEndDate = $rawLine['SERVICEPERIODENDDATE'];
    }

}