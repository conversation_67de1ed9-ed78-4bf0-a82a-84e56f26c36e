<?php
/**
 * File GenInvoiceLine.cls contains the class GenInvoiceLine
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Gen invoice line business object
 *
 * Class GenInvoiceLine
 */
abstract class GenInvoiceLine
{
    /* @var int $recordNo */
    private $recordNo;

    /* @var int $recordKey */
    private $recordKey;

    /* @var int $headerKey */
    private $headerKey;

    /* @var float $trxAmount */
    private $trxAmount;

    /* @var string $currency */
    private $currency;

    /* @var float $exchangeRate */
    private $exchangeRate;

    /* @var float $amount */
    private $amount;

    /* @var int $quantity */
    private $quantity;

    /* @var float $price */
    private $price;

    /* @var string $state */
    private $state;

    /* @var string $errMsg */
    private $errMsg;

    /* @var string $selected */
    private $selected;

    /* @var int $itemKey */
    private $itemKey;

    /* @var string $itemId */
    private $itemId;

    /* @var string $itemName */
    private $itemName;

    /* @var int $customerKey */
    private $customerKey;

    /* @var string $customerId */
    private $customerId;

    /* @var string $customerName */
    private $customerName;

    /* @var int $contractKey */
    private $contractKey;

    /* @var string $contractId */
    private $contractId;

    /* @var string $contractName */
    private $contractName;

    /* @var int $locationKey */
    private $locationKey;

    /* @var string $locationId */
    private $locationId;

    /* @var string $locationName */
    private $locationName;

    /* @var int $deptKey */
    private $deptKey;

    /* @var string $deptId */
    private $deptId;

    /* @var string $deptName */
    private $deptName;

    /* @var int $projectKey */
    private $projectKey;

    /* @var string $projectId */
    private $projectId;

    /* @var string $projectName */
    private $projectName;

    /* @var  int   $vendorKey */
    private $vendorKey;

    /** @var string $vendorId */
    private $vendorId;

    /* @var  int   $employeeKey */
    private $employeeKey;

    /** @var string $employeeId */
    private $employeeId;

    /* @var  int   $assetKey */
    private $assetKey;

    /** @var string $assetId */
    private $assetId;

    /* @var  int   $classKey */
    private $classKey;

    /* @var string $classId */
    private $classId;

    /* @var  int   $taskKey */
    private $taskKey;

    /* @var  string   $taskId */
    private $taskId;

    /* @var  int   $warehouseKey */
    private $warehouseKey;

    /* @var  string   $warehouseId */
    private $warehouseId;

    /* @var  int $shiptoKey */
    private $shipToKey;


    /** @var  string $shipToContactName */
    private $shipToContactName;

    /**
     * @return int
     */
    public function getShipToKey()
    {
        return $this->shipToKey;
    }

    /**
     * @param int $shipToKey
     */
    public function setShipToKey($shipToKey)
    {
        $this->shipToKey = $shipToKey;
    }


    /**
     * @return string
     */
    public function getShipToContactName()
    {
        return $this->shipToContactName;
    }

    /**
     * @param string $shipToContactName
     */
    public function setShipToContactName($shipToContactName)
    {
        $this->shipToContactName = $shipToContactName;
    }


    /* @var array $customFields */
    private $customFields;

    /* @var array $relationships */
    private $relationships;

    /* @var bool $firstForType true if this is the first line in the GenInvoiceHeader for the Type */
    private $firstForType;

    /* @var bool $lastForType true if this is the last line in the GenInvoiceHeader for the Type */
    private $lastForType;

    /**
     * @param int $recordNo  Record no
     * @param int $headerKey Header key
     * @param int $recordKey Gen invoice record key
     */
    public function __construct($recordNo, $headerKey, $recordKey)
    {
        $this->recordNo     = $recordNo;
        $this->recordKey    = $recordKey;
        $this->headerKey    = $headerKey;
    }

    /**
     * Copy relevant attributes from raw lines in DB
     *
     * @param array  $rawLine        Raw line
     * @param string $recordTypeCode Record type code
     */
    public function fromRawLine($rawLine, /** @noinspection PhpUnusedParameterInspection */ $recordTypeCode)
    {
        $this->setTrxAmount($rawLine['TRX_AMOUNT']);
        $this->setQuantity($rawLine['QUANTITY']);
        $this->setPrice($rawLine['PRICE']);

        $this->setSelected($rawLine['SELECTED']);
        $this->setState($rawLine['STATE']);
        $this->setErrMsg($rawLine['ERRMSG']);

        if (ContractUtil::isMCPEnabled()) {
            $this->setCurrency($rawLine['CURRENCY']);
            $this->setAmount($rawLine['AMOUNT']);
            $this->setExchangeRate($rawLine['EXCHRATE']);
        } else { // single currency for ticket 78789
            $this->setAmount($rawLine['TRX_AMOUNT']);
        }

        $this->setItemKey($rawLine['ITEMKEY']);
        $this->setItemId($rawLine['ITEMID']);
        $this->setItemName($rawLine['ITEMNAME']);

        $this->setCustomerKey($rawLine['CUSTOMERKEY']);
        $this->setCustomerId($rawLine['CUSTOMERID']);
        $this->setCustomerName($rawLine['CUSTOMERNAME']);

        $this->setContractKey($rawLine['CONTRACTKEY']);
        $this->setContractId($rawLine['CONTRACTID']);
        $this->setContractName($rawLine['CONTRACTNAME']);

        $this->setLocationKey($rawLine['LOCATIONKEY']);
        $this->setLocationId($rawLine['LOCATIONID']);
        $this->setLocationName($rawLine['LOCATIONNAME']);

        $this->setDeptKey($rawLine['DEPTKEY']);
        $this->setDeptId($rawLine['DEPTID']);
        $this->setDeptName($rawLine['DEPTNAME']);

        $this->setProjectKey($rawLine['PROJECTKEY']);
        $this->setProjectId($rawLine['PROJECTID']);
        $this->setProjectName($rawLine['PROJECTNAME']);



        // All all other dimensions
        $this->setClassKey($rawLine['CLASSKEY']);
        $this->setClassId($rawLine['CLASSID']);
        $this->setVendorKey($rawLine['VENDORDIMKEY']);
        $this->setEmployeeKey($rawLine['EMPLOYEEDIMKEY']);
        $this->setAssetKey($rawLine['ASSETDIMKEY']);
        $this->setTaskKey($rawLine['TASKDIMKEY']);
        $this->setWarehouseKey($rawLine['WAREHOUSEDIMKEY']);

        $this->setShipToKey($rawLine['SHIPTOKEY']);
        $this->setShipToContactName($rawLine['SHIPTOCONTACTNAME']);

        // Custom fields
        $docType = $rawLine['DOCPARID'];
        assert(isset($docType), 'Missing Transaction Definition');

        $cfIds = GenInvoiceUtil::getCustomFieldIds('sodocumententry', $docType);
        if ($cfIds) {
            foreach ($cfIds as $id) {
                if (isset($rawLine[$id]) && $rawLine[$id] !== '') {
                    $this->setCustomFieldValue($id, $rawLine[$id]);
                }
            }
        }

        // UDDs
        $uddIds = GenInvoiceUtil::getUDDFieldIds();
        if ($uddIds) {
            foreach ($uddIds as $id) {
                if ($rawLine[$id]) {
                    $this->setRelationshipValue($id, $rawLine[$id]);
                }
            }
        }

        $this->innerFromRawLine($rawLine);
    }

    /**
     * Returns recordno
     *
     * @return int
     */
    public function getRecordNo()
    {
        return $this->recordNo;
    }

    /**
     * Returns record key
     *
     * @return int
     */
    public function getRecordKey()
    {
        return $this->recordKey;
    }

    /**
     * Returns header key
     *
     * @return int
     */
    public function getHeaderKey()
    {
        return $this->headerKey;
    }

    /**
     * Returns transaction amount
     *
     * @return float
     */
    public function getTrxAmount()
    {
        return $this->trxAmount;
    }

    /**
     * Sets transaction amount
     *
     * @param float $trxAmount Transaction amount
     */
    public function setTrxAmount($trxAmount)
    {
        $this->trxAmount = $trxAmount;
    }

    /**
     * Returns currency
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Sets currecny
     *
     * @param string $currency Currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * Returns amount
     *
     * @return float
     */
    public function getExchangeRate()
    {
        return $this->exchangeRate;
    }

    /**
     * @param float $exchangeRate
     */
    public function setExchangeRate($exchangeRate)
    {
        $this->exchangeRate = $exchangeRate;
    }

    /**
     * @return float
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * Sets amount
     *
     * @param float $amount Amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    /**
     * Returns quantity
     *
     * @return int
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * Sets quantity
     *
     * @param int $quantity Quantity
     */
    public function setQuantity($quantity)
    {
        $this->quantity = $quantity;
    }

    /**
     * Returns state
     *
     * @return string
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * Sets state
     *
     * @param string $state State
     */
    public function setState($state)
    {
        $this->state = $state;
    }

    /**
     * Returns error message
     *
     * @return string
     */
    public function getErrMsg()
    {
        return $this->errMsg;
    }

    /**
     * Sets error message
     *
     * @param string $errMsg Error message
     */
    public function setErrMsg($errMsg)
    {
        $this->errMsg = $errMsg;
    }

    /**
     * Returns selected flag
     *
     * @return string
     */
    public function getSelected()
    {
        return $this->selected;
    }

    /**
     * Sets selected flag
     *
     * @param string $selected Selected
     */
    public function setSelected($selected)
    {
        $this->selected = $selected;
    }

    /**
     * Returns item key
     *
     * @return int
     */
    public function getItemKey()
    {
        return $this->itemKey;
    }

    /**
     * Sets item key
     *
     * @param int $itemKey Item key
     */
    public function setItemKey($itemKey)
    {
        $this->itemKey = $itemKey;
    }

    /**
     * Returns item id
     *
     * @return string
     */
    public function getItemId()
    {
        return $this->itemId;
    }

    /**
     * Returns item id
     *
     * @param string $itemId Item id
     */
    public function setItemId($itemId)
    {
        $this->itemId = $itemId;
    }

    /**
     * Returns item name
     *
     * @return string
     */
    public function getItemName()
    {
        return $this->itemName;
    }

    /**
     * Sets item name
     *
     * @param string $itemName Item name
     */
    public function setItemName($itemName)
    {
        $this->itemName = $itemName;
    }

    /**
     * Returns customer key
     *
     * @return int
     */
    public function getCustomerKey()
    {
        return $this->customerKey;
    }

    /**
     * Sets customer key
     *
     * @param int $customerKey Customer key
     */
    public function setCustomerKey($customerKey)
    {
        $this->customerKey = $customerKey;
    }

    /**
     * Returns customer id
     *
     * @return string
     */
    public function getCustomerId()
    {
        return $this->customerId;
    }

    /**
     * Sets customer id
     *
     * @param string $customerId Customer id
     */
    public function setCustomerId($customerId)
    {
        $this->customerId = $customerId;
    }

    /**
     * Returns customer name
     *
     * @return string
     */
    public function getCustomerName()
    {
        return $this->customerName;
    }

    /**
     * Sets customer name
     *
     * @param string $customerName Customer name
     */
    public function setCustomerName($customerName)
    {
        $this->customerName = $customerName;
    }

    /**
     * Returns contract key
     *
     * @return int
     */
    public function getContractKey()
    {
        return $this->contractKey;
    }

    /**
     * Sets contract key
     *
     * @param int $contractKey Contract key
     */
    public function setContractKey($contractKey)
    {
        $this->contractKey = $contractKey;
    }

    /**
     * Returns contract id
     *
     * @return string
     */
    public function getContractId()
    {
        return $this->contractId;
    }

    /**
     * Sets contract id
     *
     * @param string $contractId Contract id
     */
    public function setContractId($contractId)
    {
        $this->contractId = $contractId;
    }

    /**
     * Return contract name
     *
     * @return string
     */
    public function getContractName()
    {
        return $this->contractName;
    }

    /**
     * Sets contract name
     *
     * @param string $contractName Contract name
     */
    public function setContractName($contractName)
    {
        $this->contractName = $contractName;
    }

    /**
     * Returns location key
     *
     * @return int
     */
    public function getLocationKey()
    {
        return $this->locationKey;
    }

    /**
     * Sets location key
     *
     * @param int $locationKey Location key
     */
    public function setLocationKey($locationKey)
    {
        $this->locationKey = $locationKey;
    }

    /**
     * Returns location id
     *
     * @return string
     */
    public function getLocationId()
    {
        return $this->locationId;
    }

    /**
     * Sets location id
     *
     * @param string $locationId Location id
     */
    public function setLocationId($locationId)
    {
        $this->locationId = $locationId;
    }

    /**
     * Returns location name
     *
     * @return string
     */
    public function getLocationName()
    {
        return $this->locationName;
    }

    /**
     * Sets location name
     *
     * @param string $locationName Location name
     */
    public function setLocationName($locationName)
    {
        $this->locationName = $locationName;
    }

    /**
     * Return dept key
     *
     * @return int
     */
    public function getDeptKey()
    {
        return $this->deptKey;
    }

    /**
     * Sets dept key
     *
     * @param int $deptKey Dept key
     */
    public function setDeptKey($deptKey)
    {
        $this->deptKey = $deptKey;
    }

    /**
     * Returns dept id
     *
     * @return string
     */
    public function getDeptId()
    {
        return $this->deptId;
    }

    /**
     * Sets dept id
     *
     * @param string $deptId dept id
     */
    public function setDeptId($deptId)
    {
        $this->deptId = $deptId;
    }

    /**
     * Returns dept name
     *
     * @return string
     */
    public function getDeptName()
    {
        return $this->deptName;
    }

    /**
     * Sets dept name
     *
     * @param string $deptName dept name
     */
    public function setDeptName($deptName)
    {
        $this->deptName = $deptName;
    }

    /**
     * Returns project key
     *
     * @return int
     */
    public function getProjectKey()
    {
        return $this->projectKey;
    }

    /**
     * Sets project key
     *
     * @param int $projectKey Project key
     */
    public function setProjectKey($projectKey)
    {
        $this->projectKey = $projectKey;
    }

    /**
     * Sets project id
     *
     * @param string $projectId Project id
     */
    public function setProjectId($projectId)
    {
        $this->projectId = $projectId;
    }

    /**
     * Returns project id
     *
     * @return string
     */
    public function getProjectId()
    {
        return $this->projectId;
    }

    /**
     * Returns project name
     *
     * @return string
     */
    public function getProjectName()
    {
        return $this->projectName;
    }

    /**
     * Sets project name
     *
     * @param string $projectName Project name
     */
    public function setProjectName($projectName)
    {
        $this->projectName = $projectName;
    }

    /**
     * @return int
     */
    public function getVendorKey()
    {
        return $this->vendorKey;
    }

    /**
     * @param int $vendorKey
     */
    public function setVendorKey($vendorKey)
    {
        $this->vendorKey = $vendorKey;
    }

    /**
     * Returns vendor id
     *
     * @return string
     */
    public function getVendorId()
    {
        return $this->vendorId;
    }

    /**
     * Sets vendor id
     *
     * @param string $vendorId vendor id
     */
    public function setVendorId($vendorId)
    {
        $this->vendorId = $vendorId;
    }

    /**
     * @return int
     */
    public function getEmployeeKey()
    {
        return $this->employeeKey;
    }

    /**
     * @param int $employeeKey
     */
    public function setEmployeeKey($employeeKey)
    {
        $this->employeeKey = $employeeKey;
    }

    /**
     * Returns employee id
     *
     * @return string
     */
    public function getEmployeeId()
    {
        return $this->employeeId;
    }

    /**
     * Sets employee id
     *
     * @param string $employeeId employee id
     */
    public function setEmployeeId($employeeId)
    {
        $this->employeeId = $employeeId;
    }

    /**
     * @return int
     */
    public function getAssetKey()
    {
        return $this->assetKey;
    }

    /**
     * @param int $assetKey
     */
    public function setAssetKey($assetKey)
    {
        $this->assetKey = $assetKey;
    }

    /**
     * Returns asset id
     *
     * @return string
     */
    public function getAssetId()
    {
        return $this->assetId;
    }

    /**
     * Sets asset id
     *
     * @param string $assetId asset id
     */
    public function setAssetId($assetId)
    {
        $this->assetId = $assetId;
    }

    /**
     * @return int
     */
    public function getClassKey()
    {
        return $this->classKey;
    }

    /**
     * @param int $classKey
     */
    public function setClassKey($classKey)
    {
        $this->classKey = $classKey;
    }

    /**
     * Returns class id
     *
     * @return string
     */
    public function getClassId()
    {
        return $this->classId;
    }

    /**
     * Sets class id
     *
     * @param string $classId class id
     */
    public function setClassId($classId)
    {
        $this->classId = $classId;
    }

    /**
     * @return int
     */
    public function getTaskKey()
    {
        return $this->taskKey;
    }

    /**
     * @param int $taskKey
     */
    public function setTaskKey($taskKey)
    {
        $this->taskKey = $taskKey;
    }

    /**
     * Returns task id
     *
     * @return string
     */
    public function getTaskId()
    {
        return $this->taskId;
    }

    /**
     * Sets task id
     *
     * @param string $taskId task id
     */
    public function setTaskId($taskId)
    {
        $this->taskId = $taskId;
    }

    /**
     * @return int
     */
    public function getWarehouseKey()
    {
        return $this->warehouseKey;
    }



    /**
     * @param int $warehouseKey
     */
    public function setWarehouseKey($warehouseKey)
    {
        $this->warehouseKey = $warehouseKey;
    }

    /**
     * Returns warehouse id
     *
     * @return string
     */
    public function getWarehouseId()
    {
        return $this->warehouseId;
    }

    /**
     * Sets warehouse id
     *
     * @param string $warehouseId warehouse id
     */
    public function setWarehouseId($warehouseId)
    {
        $this->warehouseId = $warehouseId;
    }

    /**
     * Returns price
     *
     * @return mixed
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * Sets price
     *
     * @param mixed $price Price
     */
    public function setPrice($price)
    {
        $this->price = $price;
    }

    /**
     * Sets custom field values
     *
     * @param array $customFieldsMap Custom field values map
     */
    public function setCustomFieldValues($customFieldsMap)
    {
        $this->customFields = $customFieldsMap;
    }

    /**
     * Sets custom field value
     *
     * @param string $fieldId Field id
     * @param mixed  $value   Value
     */
    public function setCustomFieldValue($fieldId, $value)
    {
        $this->customFields[$fieldId] = $value;
    }

    /**
     * Returns custom field values
     *
     * @return mixed
     */
    public function getCustomFieldValues()
    {
        return $this->customFields;
    }

    /**
     * Sets relationship values
     *
     * @param array $relationshipsMap Relationships map
     */
    public function setRelationshipValues($relationshipsMap)
    {
        $this->relationships = $relationshipsMap;
    }

    /**
     * Sets relationship value
     *
     * @param string $lookupId Lookup id
     * @param mixed  $value    Value
     */
    public function setRelationshipValue($lookupId, $value)
    {
        $this->relationships[$lookupId] = $value;
    }

    /**
     * Return relationship values
     *
     * @return mixed
     */
    public function getRelationshipValues()
    {
        return $this->relationships;
    }

    /**
     * @return bool
     */
    public function isFirstForType()
    {
        return $this->firstForType;
    }

    /**
     * @param bool $firstForType
     */
    public function setFirstForType($firstForType)
    {
        $this->firstForType = $firstForType;
    }

    /**
     * @return bool
     */
    public function isLastForType()
    {
        return $this->lastForType;
    }

    /**
     * @param bool $lastForType
     */
    public function setLastForType($lastForType)
    {
        $this->lastForType = $lastForType;
    }

    /**
     * Associative array of attributes for storage
     *
     * @return array
     *
     * @throws IAException
     */
    public function toDocEntry()
    {
        $entry = [];

        // Give chance to sub-classes
        $this->innerToDocEntry($entry);

        $entry['ITEMID']    = $this->getItemId();
        $entry[':use_item_taxable'] = true;     // always use the taxable flag of the item since they can't override anyway

        $entry['UIQTY']     = $this->getQuantity();
        if (ContractUtil::isMCPEnabled()) {
            $entry['UIPRICE'] = ibcmul(
                $this->getPrice(), $this->getExchangeRate(),
                ContractUtil::AMOUNT_PRECISION, true
            );
        } else {
            $entry['UIPRICE'] = $this->getPrice();
        }
        //$entry['UIVALUE']   = $this->getAmount();

        $entry['TRX_PRICE'] = $this->getPrice();
       // $entry['TRX_VALUE'] = $this->getTrxAmount();

        $entry['PRICE'] = $this->getPrice();

        if(ContractUtil::isShipToLineItemEnabled()){
            $entry['SHIPTO'] =['CONTACTNAME'=>$this->getShipToContactName()];
        }

        $entry['GENINVOICELINEKEY'] = $this->getRecordNo();

        $entry['MEMO'] = isl_substr($this->getMemo(), 0, GenInvoiceUtil::MAX_MEMO_LENGTH);

        $unit = GenInvoiceUtil::getItemUOM($this->getItemId());
        if (!$unit) {
            $msg = "No Unit of measure set for item ".$this->getItemId();
            throw IAException::newIAException('UGI-0071', $msg,
                ['ITEM_ID' => $this->getItemId()]);
        }
        $entry['UNIT'] = $unit;

        $this->populateEntryDimensions($entry);

        // Custom fields
        $cfMap = $this->getCustomFieldValues();
        if ($cfMap) {
            $entry = $entry + $cfMap;
        }

        // UDDs
        $uddMap = $this->getRelationshipValues();
        if ($uddMap) {
            $entry = $entry + $uddMap;
        }

        return $entry;
    }

    /**
     * Populate entry dimensions
     *
     * @param array $entry Doc entry
     */
    private function populateEntryDimensions(&$entry)
    {
        $entry['CONTRACTDIMKEY'] = $this->getContractKey();
        $entry['CONTRACTID'] = $this->getContractId();
        $entry['CUSTOMERDIMKEY'] = $this->getCustomerKey();

        $entry['LOCATION'] = $this->getLocationId();
        $entry['DEPARTMENT'] = $this->getDeptId();

        // Rest of the dimensions
        $entry['PROJECTDIMKEY'] = $this->getProjectKey();
        $entry['PROJECTID'] = $this->getProjectId();
        $entry['VENDORDIMKEY'] = $this->getVendorKey();
        $entry['VENDORID'] = $this->getVendorId();
        $entry['EMPLOYEEDIMKEY'] = $this->getEmployeeKey();
        $entry['EMPLOYEEID'] = $this->getEmployeeId();
        $entry['ASSETDIMKEY'] = $this->getAssetKey();
        $entry['ASSETID'] = $this->getAssetId();
        $entry['CLASSDIMKEY'] = $this->getClassKey();
        $entry['CLASSID'] = $this->getClassId();
        $entry['TASKKEY'] = $this->getTaskKey();
        $entry['TASKID'] = $this->getTaskId();

        $entry['WAREHOUSEID'] = $this->getWarehouseId();
    }

    /**
     * Returns formatted value of common fields in the grid
     *
     * @param bool $includeState Include state
     *
     * @return array
     */
    public function toFormGridLine($includeState=false)
    {
        $item_viewop = GenInvoiceUtil::getOpId('so/lists/item/view');

        $sess = Session::getKey();

        $item_url = "editor.phtml?.op=$item_viewop&.sess=$sess&.r=".$this->getItemId().'&.popup=1';
        $itemIdHTML = '<a href=\'javascript:Launch( "'.$item_url
            . '" , "itemid");\' target1="_blank">'.$this->getItemId().'--'.util_encode($this->getItemName()). '</a>';

        $gridLine = [
            'LINEKEY'       => $this->getRecordNo(),
            'ITEMIDHTML'    => $itemIdHTML,
            'TRX_AMOUNT'    => $this->getTrxAmount(),
            'AMOUNT'        => $this->getAmount(),
            'QUANTITY'      => $this->getQuantity(),
            'PRICE'         => $this->getPrice(),
            'SELECTED'      => $this->getSelected()
        ];

        if(ContractUtil::isShipToLineItemEnabled()){
            $gridLine['SHIPTOCONTACTNAME'] =$this->getShipToContactName();
        }

        if ($includeState) {
            $gridLine['LINESTATE'] = $this->getState();
            $gridLine['LINEERRMSG'] = $this->getErrMsg();
        }

        $this->innerToFormGridLine($gridLine);

        return $gridLine;
    }

    /**
     * Source type code
     *
     * @return string
     */
    abstract public function getTypeCode();

    /**
     * Copy relevant attributes from raw lines in DB.
     * Fields that are persisted in GENINVOICELINE table are fetched and set here
     *
     * @param array $rawLine Raw line
     */
    abstract public function innerFromRawLine($rawLine);

    /**
     * Loads required additional attributes, both for display and to convert into docentry
     * Additional fields that are *not* persisted in GENINVOICELINE table are fetched and set here
     */
    abstract public function loadDetails();

    /**
     * Loads the common dimension ids
     *
     * @param array $line the line containining the common dimension ids
     */
    protected function populateCommonDetails($line)
    {
        $this->setProjectId($line['PROJECTID']);
        $this->setEmployeeId($line['EMPLOYEEID']);
        $this->setAssetId($line['ASSETID']);
        $this->setVendorId($line['VENDORID']);
        $this->setWarehouseId($line['WAREHOUSEID']);
        $this->setTaskId($line['TASKID']);
    }

    /**
     * Set object specific doc entry attributes
     *
     * @param array $docEntry Doc entry
     */
    abstract public function innerToDocEntry(array &$docEntry);

    /**
     * Updates status to billed and associated processing goes here
     *
     * @param string $invoiceDate Invoice date
     * @param string $glPostDate  GL post date
     *
     * @throws IAException
     */
    final public function preDocHeaderAdd($invoiceDate, $glPostDate)
    {
        $this->innerPreDocHeaderAdd($invoiceDate, $glPostDate);
    }

    /**
     * Updates status to billed and associated processing goes here
     *
     * @param string $invoiceDate Invoice date
     * @param string $glPostDate  GL post date
     *
     * @throws IAException
     */
    protected function innerPreDocHeaderAdd($invoiceDate, $glPostDate)
    {
        // By default we don't do anything, subclass will get a chance to do something before the DocHdr is added
    }

    /**
     * Updates status to billed and associated processing goes here
     *
     * @param string $invoiceDate Invoice date
     * @param string $glPostDate  GL post date
     * @param int    $docRecordNo The dochdr record#
     *
     * @throws IAException
     */
    public function markBilled($invoiceDate, $glPostDate, $docRecordNo)
    {
        $this->innerMarkBilled($invoiceDate, $glPostDate, $docRecordNo);

        /* @var   GenInvoicePrebillLineManager   $lineMgr */
        $lineMgr = Globals::$g->gManagerFactory->getManager('geninvoiceprebillline');
        $ok = $lineMgr->setState($this->getRecordNo(), GenInvoiceUtil::STATECODE_SUCCESS);
        if (!$ok) {
            throw new IAException("Error updating pre-bill line state.",'UGI-0072');
        }
    }

    /**
     * Updates status to billed and associated processing goes here
     *
     * @param string $invoiceDate Invoice date
     * @param string $glPostDate  GL post date
     * @param int    $docRecordNo The dochdr record#
     *
     * @throws IAException
     */
    abstract public function innerMarkBilled($invoiceDate, $glPostDate, $docRecordNo);

    /**
     * Source specific grid fields are added here
     *
     * @param array $gridLine Grid line
     */
    abstract public function innerToFormGridLine(&$gridLine);

    /**
     * Returns doc entry memo for line
     *
     * @return string
     */
    abstract public function getMemo();

    /**
     * @param array $entries
     * @return bool
     */
    public function splitLastDocEntry(&$entries){
        if($entries) {
            return true;
        }
        return false;
    }
}