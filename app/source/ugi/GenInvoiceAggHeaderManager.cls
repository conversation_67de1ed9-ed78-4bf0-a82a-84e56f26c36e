<?php
/**
 * File GenInvoiceAggHeaderManager.cls contains the class GenInvoiceAggHeaderManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Manages CRUD for Gen Invoices Aggregate Timesheet Entry
 *
 * Class GenInvoiceAggHeaderManager
 */
class GenInvoiceAggHeaderManager extends EntityManager
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = [])
    {
        parent::__construct($params);
    }
}
