<?php

/**
 * Header class for Bill to
 *
 * Class BillToHeader
 */
class BillT<PERSON><PERSON>eader extends HeaderGeneratorDecorator
{
    /**
     * @var array   $cnDetails
     */
    private $cnDetails;

    /**
     * @var string  $context
     */
    private $context;

    /**
     * BillToHeader constructor.
     *
     * @param HeaderGenerator $generator
     * @param string          $context  the context for the header (defined in HeaderContext)
     * @param array           $cnDetails
     */
    function __construct(HeaderGenerator $generator, $context, $cnDetails)
    {
        parent::__construct($generator);
        $this->context = $context;
        $this->cnDetails = $cnDetails;
    }

    /**
     * @param string                    $grouping
     * @param GroupHandler              $groupHandler
     * @param GenInvoiceBillableLine[]  $billableLines
     *
     * @return mixed
     */
    public function generate($grouping, $groupHandler, $billableLines)
    {
        $header = parent::generate($grouping, $groupHandler, $billableLines);

        if ($this->context == HeaderContext::GROUP) {
            $header[GenInvoiceUtil::BILLTOKEY] = $groupHandler->getGroupValue(BillToGrouper::ID, $grouping);
        } elseif ($this->context == HeaderContext::CONTRACT) {
            $cnDetail = $this->cnDetails[$groupHandler->getGroupValue(ContractGrouper::ID, $grouping)];
            $header[GenInvoiceUtil::BILLTOKEY] = $cnDetail[GenInvoiceUtil::BILLTOKEY];
        } elseif ($this->context == HeaderContext::RESOLVE) {
            $customerDetails = GenInvoiceUtil::getCustomerDetailsToResolve(
                $groupHandler->getGroupValue(CustomerGrouper::ID, $grouping));
            $contractInfos = GenInvoiceUtil::getContractInfoForHeader($billableLines, $this->cnDetails);
            $header[GenInvoiceUtil::BILLTOKEY] = GenInvoiceUtil::getResolvedKey($customerDetails,
                                                                                $contractInfos,
                                                                                GenInvoiceUtil::BILLTOKEY,
                                                                                true,
                                                                                GenInvoiceUtil::DISPLAYCONTACTKEY);
        }

        return $header;
    }
}