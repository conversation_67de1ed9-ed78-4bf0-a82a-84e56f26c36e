<?php
/**
 * File GenInvoiceEntryLockHelper.cls
 *
 * @copyright 2000-2022 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Helper for building GenInvoiceEntryLock entries
 *
 * Class GenInvoiceEntryLockHelper
 */
class GenInvoiceEntryLockHelper
{
    /**
     * @var array $entriesToBeLocked
     */
    private $entriesToBeLocked = [];

    /**
     * @var int $contractRunEntryKey
     */
    private $contractRunEntryKey;

    /**
     * @var int $genInvoiceHeaderKey
     */
    private $genInvoiceHeaderKey;

    /**
     * @var int $locationKey
     */
    private $locationKey;

    /**
     * @param int $contractRunEntryKey the contract run entry key
     * @param int $genInvoiceHeaderKey the gen invoice header key
     * @param int $locationKey         the location key
     */
    function __construct($contractRunEntryKey, $genInvoiceHeaderKey, $locationKey)
    {
        $this->contractRunEntryKey = $contractRunEntryKey;
        $this->genInvoiceHeaderKey = $genInvoiceHeaderKey;
        $this->locationKey = $locationKey;
    }

    /**
     * Adds the entry to be locked
     *
     * @param string $billableLineClass the class of the billable line
     * @param array  $genInvoiceLine    the gen invoice line created from the billable line
     */
    public function addEntryToBeLocked($billableLineClass, $genInvoiceLine)
    {
        switch($billableLineClass) {
            case 'ContractBillableLine':
                $this->entriesToBeLocked[] =
                    $this->getLockingEntry($genInvoiceLine['CNBILLINGSCHENTRYKEY'], 'CNBILLINGSCHENTRYKEY');
                break;
            case 'ContractUsageBillableLine':
                $cnUsageKeys = $this->getCnUsageKeysFromBillingId($genInvoiceLine['CNUSAGEBILLINGID']);
                foreach ($cnUsageKeys as $cnUsageKey) {
                    $this->entriesToBeLocked[] = $this->getLockingEntry($cnUsageKey, 'CNUSAGEKEY');
                }
                break;
            case 'TimesheetBillableLine':
                $this->entriesToBeLocked[] = $this->getLockingEntry($genInvoiceLine['TSENTRYKEY'], 'TSENTRYKEY');
                break;
            case 'AggregateTimesheetBillableLine':
                $tsEntryKeys = $this->getTsEntryKeysFromAggregrate($genInvoiceLine['AggTimesheetEntryMapping']);
                foreach ($tsEntryKeys as $tsEntryKey) {
                    $this->entriesToBeLocked[] = $this->getLockingEntry($tsEntryKey, 'TSENTRYKEY');
                }
                break;
            case 'ExpenseBillableLine':
                $this->entriesToBeLocked[] = $this->getLockingEntry($genInvoiceLine['PRENTRYKEY'], 'PRENTRYKEY');
                break;
            case 'APBillBillableLine':
                $this->entriesToBeLocked[] = $this->getLockingEntry($genInvoiceLine['PRENTRYKEY'], 'PRENTRYKEY');
                break;
            case 'PODocumentBillableLine':
                $this->entriesToBeLocked[] = $this->getLockingEntry($genInvoiceLine['DOCENTRYKEY'], 'DOCENTRYKEY');
                break;
            default:
                throw IAException::newIAException('UGI-0099', "Entry could not be locked! $billableLineClass is not a valid class!" ,['BILLABLE_LINE_CLASS' => $billableLineClass]);
        }
    }

    /**
     * Gets the geninvoicelockingentry to be saved
     *
     * @param int $key           the key
     * @param string $keyContext the context of the key (e.g. CNBILLINGSCHENTRYKEY, CNUSAGEKEY, etc.)
     *
     * @return array
     */
    private function getLockingEntry($key, $keyContext)
    {
        $lockingEntry['CONTRACTRUNENTRYKEY'] = $this->contractRunEntryKey;
        $lockingEntry['GENINVOICEHEADERKEY'] = $this->genInvoiceHeaderKey;
        // set location key if applicable; For root, location key is null so no need to set
        if (isset($this->locationKey)) {
            $lockingEntry['LOCATIONKEY'] = $this->locationKey;
        }
        $lockingEntry[$keyContext] = $key;
        return $lockingEntry;
    }

    /**
     * Gets the contract usage keys (i.e. CNSUSAGEKEYs) associated to the specified contract usage billing id
     *
     * @param int $cnUsageBillingId the contract usage billing id
     *
     * @return array
     */
    private function getCnUsageKeysFromBillingId($cnUsageBillingId)
    {
        $cnUsageKeys = [];
        $contractUsageBillings = EntityManager::GetListQuick('contractusagebilling', ['CONTRACTUSAGEKEY'],
                                                    ['USAGEBILLINGID' => $cnUsageBillingId]);
        if (!empty($contractUsageBillings)) {
            foreach ($contractUsageBillings as $contractUsageBilling) {
                $cnUsageKeys[] = $contractUsageBilling['CONTRACTUSAGEKEY'];
            }
        }
        return $cnUsageKeys;
    }

    /**
     * Gets the timesheet entry keys (i.e. TSENTRYKEYs) included in the timesheet aggregation
     *
     * @param array $aggTimesheetEntryMapping
     *
     * @return array
     */
    private function getTsEntryKeysFromAggregrate($aggTimesheetEntryMapping)
    {
        $tsEntryKeys = [];
        foreach ($aggTimesheetEntryMapping as $genInvoiceLineAndTsEntry) {
            $tsEntryKeys[] = $genInvoiceLineAndTsEntry['TSENTRYKEY'];
        }
        return $tsEntryKeys;
    }

    /**
     * Gets the entries to be locked (i.e. to be saved to GENINVOICENTRYLOCK table)
     *
     * @return array
     */
    public function getEntriesToBeLocked()
    {
        return $this->entriesToBeLocked;
    }
}
