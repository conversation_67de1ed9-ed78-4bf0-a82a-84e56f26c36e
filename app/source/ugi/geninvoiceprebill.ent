<?php
/**
 * File geninvoiceprebill.ent contains entity definition for geninvoiceprebill
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 *
 */

$kSchemas['geninvoiceprebill'] = [
    
    'object' => [
        // NAM<PERSON> needs to be first to make picker work
        'NAME',
        'RECORDNO',
        'TYPE',
        'DOCPARID',
        'INVOICEMSG',
        'INVOICEDATE',
        'INVOICEBY',
        'GLPOSTDATE',
        'FILTERKEY',
        'FILTERNAME',
        'POLICYKEY',
        'POLICYNAME',
        'ASOFDATE',
        'BILLSCHSTARTDATE',
        'CUSTOMERKEY',
        'CUSTOMERID',
        'CONTRACTKEY',
        'CONTRACTGROUPKEY',
        'CONTRACTID',
        'CONTRACTGROUPID',
        'PROJECTKEY',
        'PROJECTID',
        'PRICELISTKEY',
        'PRICELISTID',
        'CURRENCY',
        'CUSTOMERTYPEKEY',
        'CUSTOMERTYPEID',
        'PROJECTTYPEKEY',
        'PROJECTTYPEID',
        'PROJECTMANAGERKEY',
        'PROJECTMANAGERID',
        'PROJECTMANAGERNAME',
        'EMPLOYEEKEY',
        'EMPLOYEEID',
        'EMPLOYEENAME',
        'ASSETKEY',
        'ASSETID',
        'ASSETNAME',
        'ITEMKEY',
        'ITEMID',
        'ITEMNAME',
        'ITEMGROUPKEY',
        'ITEMGROUPID',
        'ITEMGROUPNAME',
        'DEPTFILTERKEY',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LOCFILTERKEY',
        'LOCATIONID',
        'LOCATIONNAME',
        'CLASSKEY',
        'CLASSID',
        'CLASSGROUPKEY',
        'CUSTOMERGROUPKEY',
        'PROJECTGROUPKEY',
        'DEPARTMENTGROUPKEY',
        'EMPLOYEEGROUPKEY',
        'ASSETGROUPKEY',
        'LOCATIONGROUPKEY',
        'CLASSGROUPID',
        'CUSTOMERGROUPID',
        'PROJECTGROUPID',
        'DEPARTMENTGROUPID',
        'EMPLOYEEGROUPID',
        'ASSETGROUPID',
        'LOCATIONGROUPID',
        //'INVCURRENCYSOURCE',
        //'INVCURRENCY',
        //'EXCHRATETYPEID',
        //'EXCHRATETYPENAME',
        'INCLUDECONTRACTS',
        'INCLUDECONTRACTUSAGE',
        'INCLUDETIMESHEETS',
        'INCLUDEEXPENSES',
        'INCLUDEAPBILLS',
        'INCLUDEPODOCUMENTS',
        'APPROVALSTATUS',
        'BLANKPRICESONLY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'SERVICEPERIODSTARTDATE',
        'SERVICEPERIODENDDATE',
        'SERVICEPERIODOVERRIDEOPTION'
    ],
    
    'schema' => [
        'RECORDNO'          => 'record#',
        'NAME'              => 'name',
        'TYPE'              => 'type',
        'DOCPARID'          => 'docparid',
        'INVOICEMSG'        => 'invoicemsg',
        'INVOICEDATE'       => 'invoicedate',
        'INVOICEBY'         => 'invoiceby',
        'GLPOSTDATE'        => 'glpostdate',
        'FILTERKEY'         => 'filterkey',
        'FILTERNAME'        => 'filter.name',
        'POLICYKEY'         => 'filterkey',
        'POLICYNAME'        => 'filter.name',
        'ASOFDATE'          => 'asofdate',
        'BILLSCHSTARTDATE'  => 'billschstartdate',
        'CUSTOMERKEY'       => 'customerkey',
        'CUSTOMERID'        => 'customer.customerid',
        'CONTRACTKEY'       => 'contractkey',
        'CONTRACTGROUPKEY'  => 'contractgroupkey',
        'CONTRACTGROUPID'   => 'contractgroup.id',
        'CONTRACTID'        => 'contract.contractid',
        'PROJECTKEY'        => 'projectkey',
        'PROJECTID'         => 'project.projectid',
        'PRICELISTKEY'      => 'pricelistkey',
        'PRICELISTID'       => 'pricelist.name',
        'CURRENCY'          => 'currency',
        'CUSTOMERTYPEKEY'   => 'customertypekey',
        'CUSTOMERTYPEID'    => 'customertype.name',
        'PROJECTTYPEKEY'    => 'projecttypekey',
        'PROJECTTYPEID'     => 'projecttype.name',
        'PROJECTMANAGERKEY' => 'projectmanagerkey',
        'PROJECTMANAGERID'  => 'projectmanager.employeeid',
        'PROJECTMANAGERNAME'=> 'managercontact.name',
        'EMPLOYEEKEY'       => 'employeekey',
        'EMPLOYEEID'        => 'employee.employeeid',
        'EMPLOYEENAME'      => 'employeecontact.name',
        'ASSETKEY'          => 'assetkey',
        'ASSETID'           => 'asset.assetid',
        'ASSETNAME'         => 'asset.name',
        'ITEMKEY'           => 'itemkey',
        'ITEMID'            => 'item.itemid',
        'ITEMNAME'          => 'item.name',
        'ITEMGROUPKEY'      => 'itemgroupkey',
        'ITEMGROUPID'       => 'itemgroup.id',
        'ITEMGROUPNAME'     => 'itemgroup.name',
        'DEPTFILTERKEY'     => 'deptfilterkey',
        'DEPARTMENTID'      => 'dept.dept_no',
        'DEPARTMENTNAME'    => 'dept.title',
        'LOCFILTERKEY'      => 'locfilterkey',
        'LOCATIONID'        => 'loc.location_no',
        'LOCATIONNAME'      => 'loc.name',
        'INVCURRENCYSOURCE' => 'invcurrencysource',
        'INVCURRENCY'       => 'invcurrency',
        'EXCHRATEDATE'      => 'exchratedate',
        'EXCHRATETYPEID'    => 'exchratetypeid',
        'EXCHRATETYPENAME'  => 'exchangerateinfo.name',
        'EXCHRATE'          => 'exchrate',
        'CLASSKEY'          => 'classkey',
        'CLASSID'           => 'class.classid',
        'CLASSGROUPKEY'     => 'classgroupkey',
        'CUSTOMERGROUPKEY'  => 'customergroupkey',
        'PROJECTGROUPKEY'   => 'projectgroupkey',
        'DEPARTMENTGROUPKEY'=> 'departmentgroupkey',
        'EMPLOYEEGROUPKEY'  => 'employeegroupkey',
        'ASSETGROUPKEY'     => 'assetgroupkey',
        'LOCATIONGROUPKEY'  => 'locationgroupkey',
        'CLASSGROUPID'     => 'classgroup.id',
        'CUSTOMERGROUPID'  => 'customergroup.id',
        'PROJECTGROUPID'   => 'projectgroup.id',
        'DEPARTMENTGROUPID'=> 'departmentgroup.id',
        'EMPLOYEEGROUPID'  => 'employeegroup.id',
        'ASSETGROUPID'     => 'assetgroup.id',
        'LOCATIONGROUPID'  => 'locationgroup.id',
        'INCLUDECONTRACTS'  => 'includecontracts',
        'INCLUDECONTRACTUSAGE'  => 'includecontractusage',
        'INCLUDETIMESHEETS'     => 'includetimesheets',
        'INCLUDEEXPENSES'       => 'includeexpenses',
        'INCLUDEAPBILLS'        => 'includeapbills',
        'INCLUDEPODOCUMENTS'    => 'includepodocuments',
        'APPROVALSTATUS'        => 'approvalstatus',
        'BLANKPRICESONLY'       => 'blankpricesonly',
        'ISTEMP'            => 'istemp',
        'ISSTALE'           => 'isstale',
        'WHENMODIFIED'      => 'whenmodified',
        'WHENCREATED'       => 'whencreated',
        'CREATEDBY'         => 'createdby',
        'CREATEDBYUSER'     => 'userinfo.description',
        'MODIFIEDBY'        => 'modifiedby',
        'SCHEDULED'         => 'scheduled',
        'SERVICEPERIODSTARTDATE'      => 'serviceperiodstartdate',
        'SERVICEPERIODENDDATE'        => 'serviceperiodenddate',
        'SERVICEPERIODOVERRIDEOPTION' => 'serviceperiodoverrideoption'
    ],

    'children' => [
        'filter' => [
            'fkey' => 'filterkey', 'invfkey' => 'record#',
            'table' => 'geninvoicefilters', 'join' => 'outer'
        ],
        'project' => [
            'fkey' => 'projectkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'project',
        ],
        'contract' => [
            'fkey' => 'contractkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'contract',
        ],
        'contractgroup' => [
            'fkey' => 'contractgroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'contractgroup',
        ],
        'customer' => [
            'fkey' => 'customerkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'customer',
        ],
        'pricelist' => [
            'fkey' => 'pricelistkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'oeprclst',
        ],
        'customertype' => [
            'fkey' => 'customertypekey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'custtype',
        ],
        'projecttype' => [
            'fkey' => 'projecttypekey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'projecttype',
        ],
        'projectmanager' => [
            'fkey' => 'projectmanagerkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'employee',
            'children' => [
                'managercontact' => [
                    'fkey' => 'contactkey', 'table' => 'contact', 'join' => 'outer',
                ],
            ],
        ],
        'employee' => [
            'fkey' => 'employeekey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'employee',
            'children' => [
                'employeecontact' => [
                    'fkey' => 'contactkey', 'table' => 'contact', 'join' => 'outer',
                ],
            ],
        ],
        'asset' => [
            'fkey' => 'assetkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'faasset',
        ],
        'item' => [
            'fkey' => 'itemkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'icitem',
        ],
        'itemgroup' => [
            'fkey' => 'itemgroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'icitemgroup',
        ],
        'dept' => [
            'fkey' => 'deptfilterkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'department',
        ],
        'loc' => [
            'fkey' => 'locfilterkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'location',
        ],
        'exchangerateinfo' => [
            'fkey' => 'exchratetypeid', 'invfkey' => 'id', 'table' => 'v_exchangeratetypes', 'join' => 'outer'
        ],
        'class' => [
            'fkey' => 'classkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'class',
        ],
        'classgroup' => [
            'fkey' => 'classgroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'classgroup',
        ],
        'customergroup' => [
            'fkey' => 'customergroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'customergroup',
        ],
        'projectgroup' => [
            'fkey' => 'projectgroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'projectgroup',
        ],
        'departmentgroup' => [
            'fkey' => 'departmentgroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'departmentgroup',
        ],
        'employeegroup' => [
            'fkey' => 'employeegroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'employeegroup',
        ],
        'assetgroup' => [
            'fkey' => 'assetgroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'faassetgroup',
        ],
        'locationgroup' => [
            'fkey' => 'locationgroupkey', 'invfkey' => 'record#',
            'join' => 'outer', 'table' => 'locationgroup',
        ],
        'userinfo' => [
            'fkey' => 'createdby', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'userinfo',
        ]
    ],
    'nexus' => [
        'customer' => [
            'object' => 'customer', 'relation' => MANY2ONE, 'field' => 'CUSTOMERKEY'
        ],
        'contract' => [
            'object' => 'contract', 'relation' => MANY2ONE, 'field' => 'CONTRACTKEY'
        ],
        'exchangerateinfo' => [
            'object' => 'exchangerateinfo', 'relation' => MANY2ONE, 'field' => 'EXCHRATETYPEID'
        ],
    ],

    'sqldomarkup' => true,
    'sqlmarkupfields' => [
        'WHENCREATED',
        'WHENMODIFIED'
    ],

    'fieldinfo' => [
        $gRecordNoFieldInfo,
        [
            'id'        => 1,
            'path'      => 'NAME',
            'fullname'  => 'IA.NAME',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 60,
            ],
            'required'  => true
        ],
        [
            'id'        => 2,
            'path'      => 'ASOFDATE',
            'fullname'  => 'IA.AS_OF_DATE',
            'desc'      => 'IA.AS_OF_DATE',
            'type'      => $gDateType,
            'required' => true,
        ],
        [
            'id'        => 3,
            'path'      => 'CUSTOMERKEY',
            'fullname'  => 'IA.CUSTOMER_KEY',
            'desc'      => 'IA.CUSTOMER_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 4,
            'path'      => 'CONTRACTKEY',
            'fullname'  => 'IA.CONTRACT_KEY',
            'desc'      => 'IA.CONTRACT_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 6,
            'path'      => 'CURRENCY',
            'fullname'  => 'IA.CONTRACT_CURRENCY',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 3,
            ],
            'readonly'  => true,
        ],
        [
            'id'        => 7,
            'path'      => 'TYPE',
            'fullname'  => 'IA.TYPE',
            'type'      => [
                'ptype'         => 'enum',
                'type'          => 'text',
                'validlabels' => [ 'IA.PRE_BILL' ],
                'validvalues'   => [
                    GenInvoiceUtil::RECORD_TYPE_PREBILL
                ],
                '_validivalues' => [
                    GenInvoiceUtil::RECORD_TYPECODE_PREBILL
                ],
            ],
            'hidden'    => true
        ],
        [
            'id'        => 8,
            'path'      => 'CUSTOMERID',
            'fullname'  => 'IA.CUSTOMER',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'id'        => 9,
            'path'      => 'CONTRACTID',
            'fullname'  => 'IA.CONTRACT',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],

        [
            'id'        => 11,
            'path'      => 'FILTERKEY',
            'fullname'  => 'IA.FILTER_KEY',
            'desc'      => 'IA.FILTER_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 12,
            'path'      => 'FILTERNAME',
            'fullname'  => 'IA.FILTER_NAME',
            'desc'      => 'IA.FILTER_NAME',
            'type'      => [
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'geninvoicepolicy',
                'pickentity' => 'geninvoicepolicy'
            ],
        ],
        [
            'id'        => 73,
            'path'      => 'POLICYKEY',
            'fullname'  => 'IA.INVOICE_POLICY_KEY',
            'desc'      => 'IA.INVOICE_POLICY_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 74,
            'path'      => 'POLICYNAME',
            'fullname'  => 'IA.INVOICE_POLICY',
            'desc'      => 'IA.INVOICE_POLICY',
            'type'      => [
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'geninvoicepolicy',
                'pickentity' => 'geninvoicepolicy'
            ],
        ],
        [
            'id'        => 13,
            'path'      => 'PROJECTKEY',
            'fullname'  => 'IA.PROJECT_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 14,
            'path'      => 'PRICELISTKEY',
            'fullname'  => 'IA.PRICE_LIST_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 15,
            'path'      => 'CUSTOMERTYPEKEY',
            'fullname'  => 'IA.CUSTOMER_TYPE_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 16,
            'path'      => 'PROJECTTYPEKEY',
            'fullname'  => 'IA.PROJECT_TYPE_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 17,
            'path'      => 'PROJECTMANAGERKEY',
            'fullname'  => 'IA.PROJECT_MANAGER_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        // not a dimension of this object, but a filter to use, so do not use standard field name
        [
            'id'        => 18,
            'path'      => 'DEPTFILTERKEY',
            'fullname'  => 'IA.DEPARTMENT_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        // not a dimension of this object, but a filter to use, so do not use standard field name
        [
            'id'        => 19,
            'path'      => 'LOCFILTERKEY',
            'fullname'  => 'IA.LOCATION_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 20,
            'path' => 'INVCURRENCYSOURCE',
            'fullname' => 'IA.DETERMINE_INVOICE_CURRENCY_FROM',
            'type' => [
                'type'           =>    'radio',
                'ptype'          =>    'radio',
                'validlabels'    =>    ['IA.PROJECT_CUSTOMER', 'IA.MANUAL'],
                'validvalues'    =>    ['Project/Customer', 'Manual'],
                '_validivalues'  =>    ['P' , 'M'],
            ],
            'default' => 'Project/Customer',
            'layout'  => 'landscape',
            'hidden'  => true
        ],
        [
            'id'        => 21,
            'path' => 'INVCURRENCY',
            'fullname' => 'IA.INVOICING_CURRENCY',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'trxcurrencies',
                'size' => 5,
            ],
            'hidden' => true
        ],
        [
            'id'        => 22,
            'path'      => 'EXCHRATEDATE',
            'fullname'  => 'IA.EXCHANGE_RATE_DATE',
            'events'    => [
                'change' => 'updateExchangeRate();',
            ],
            'type'      => $gDateType,
            'hidden' => true
        ],
        [
            'id'        => 23,
            'path'      => 'EXCHRATETYPENAME',
            'fullname'  => 'IA.EXCHANGE_RATE_TYPE',
            'events'    => [
                'change' => 'updateExchangeRate();',
            ],
            'type'      => [
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'exchangeratetypesall',
                'maxlength' => 40,
                'size'      => 20,
            ],
            'noview'    => true,
            'nonew'     => true,
            'hidden' => true
        ],
        [
            'id'        => 24,
            'path'      => 'EXCHRATE',
            'fullname'  => 'IA.EXCHANGE_RATE',
            'type'      => [
                'ptype'     => 'decimal',
                'type'      => 'decimal',
                'maxlength' => 12,
                'size'      => 15,
                'format'    => '/^-{0,1}[0-9]*\.{0,1}[0-9]{0,10}$/',
            ],
            'noformat'      => true,
            'precision'     => 12,
            'showZeroFormat'=> false,
            'noViewFormat'  => false,
            'hidden'        => true
        ],
        [
            'id'        => 25,
            'path'      => 'INCLUDECONTRACTS',
            'fullname'  => 'IA.CONTRACTS',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false',
            'renameable' => true,
        ],
        [
            'id'        => 26,
            'path'      => 'INCLUDECONTRACTUSAGE',
            'fullname'  => 'IA.CONTRACT_USAGE',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false',
            'renameable' => true,
        ],
        [
            'id'        => 27,
            'path'      => 'INCLUDETIMESHEETS',
            'fullname'  => 'IA.TIMESHEETS',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false',
            'renameable' => true,
        ],
        [
            'id'        => 28,
            'path'      => 'INCLUDEEXPENSES',
            'fullname'  => 'IA.EXPENSES',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false',
            'renameable' => true,
        ],
        [
            'id'        => 29,
            'path'      => 'INCLUDEAPBILLS',
            'fullname'  => 'IA.ACCOUNTS_PAYABLE_BILLS',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false',
            'renameable' => true,
        ],
        [
            'id'        => 30,
            'path'      => 'INCLUDEPODOCUMENTS',
            'fullname'  => 'IA.PURCHASING_TRANSACTIONS',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false',
            'renameable' => true,
        ],
        [
            'id'        => 32,
            'path'      => 'APPROVALSTATUS',
            'fullname'  => 'IA.APPROVAL_STATUS',
            'type'      => [
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => ['IA.ONLY_APPROVED', 'IA.ONLY_UNAPPROVED', 'IA.ALL_APPROVED_AND_UNAPPROVED'],
                'validvalues' => ['Approved', 'Unapproved', 'Both'],
                '_validivalues' => ['A', 'U', 'B'],
            ],
            'default'   => 'Approved',
            'showlabelalways' => true,
        ],
        [
            'id'        => 33,
            'path' => 'BLANKPRICESONLY',
            'fullname' => 'IA.ONLY_SHOW_ITEMS_WITH_BLANK_PRICES',
            'type'      => Globals::$g->gBooleanType,
            'default' => 'false',
        ],
        [
            'id' => 36,
            'path' => 'PROJECTID',
            'fullname' => 'IA.PROJECT',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'project',
                'pickentity' => 'projectpick',
                'pickfield' => ['PICKID', 'CUSTOMERID', 'INVOICECURRENCY'],
                'forceCombo' => true,
                'restrict' => [
                    [
                        'pickField' => 'PREVENTGENINVOICE',
                        'operand' => '!=',
                        'value' => 'true',
                        'nulls' => true,
                    ],
                ],
            ],
            'noedit' => true,
            'nonew' => true,
            'renameable' => true,
        ],
        [
            'id' => 37,
            'path'         => 'PRICELISTID',
            'fullname'    => 'IA.PRICE_LIST_FOR_PROJECT_BILLING',
            'type'         => [
                'ptype'     => 'ptr',
                'type'         => 'ptr',
                'entity'     => 'sopricelist',
                'pickentity' => 'sopricelist',
                'size'        => 30,
                'forceCombo' => true,
            ],
            'noedit' => true,
            'nonew' => true,
        ],
        [
            'id' => 38,
            'path' => 'CUSTOMERTYPEID',
            'fullname' => 'IA.CUSTOMER_TYPE',
            'required' => false,
            'type' => [
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'custtype',
                'pickentity' => 'custtype',
                'forceCombo' => true,
            ],
            'noedit' => true,
            'nonew' => true,
            'renameable' => true,
        ],
        [
            'id' => 39,
            'path' => 'PROJECTTYPEID',
            'fullname' => 'IA.PROJECT_TYPE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'projecttype',
                'pickentity' => 'projecttype',
                'forceCombo' => true
            ],
            'noedit' => true,
            'nonew' => true,
            'renameable' => true
        ],
        [
            'id' => 40,
            'path' => 'PROJECTMANAGERID',
            'fullname' => 'IA.PROJECT_MANAGER',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'employee',
                'pickentity' => 'employeepick',
                'forceCombo' => true
            ],
            'noedit' => true,
            'nonew' => true,
            'renameable' => true
        ],
        [
            'id' => 41,
            'path' => 'EMPLOYEEID',
            'fullname' => 'IA.EMPLOYEE',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'employee',
                'pickentity' => 'employeepick',
                'maxlength' => 40,
                'size' => 20,
                'forceCombo' => true,
            ],
            'noedit' => true,
            'nonew' => true,
            'desc' => 'IA.EMPLOYEE_ID',
        ],
        [
            'id' => 76,
            'path' => 'ASSETID',
            'fullname' => 'IA.ASSET',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'fixedasset',
                'pickentity' => 'assetpick',
                'maxlength' => 40,
                'size' => 20,
                'forceCombo' => true,
            ],
            'noedit' => true,
            'nonew' => true,
            'desc' => 'IA.ASSET_ID',
        ],
        [
            'id' => 42,
            'path' => 'ITEMNGROUPID',
            'fullname' => 'IA.ITEM_AND_GROUP',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'itemngrouppick',
                'pickentity' => 'itemngrouppick',
                'pickfield' => ['PICKID', 'TYPE'],
                'maxlength' => 20,
            ],
            'renameable' => true,
        ],
        [
            'id' => 58,
            'path' => 'CUSTOMERNGROUPID',
            'fullname' => 'IA.CUSTOMER_AND_GROUP',
            'desc' => 'IA.NAME',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'customerngrouppick',
                'pickentity' => 'customerngrouppick',
                'pickfield' => ['PICKID', 'TYPE'],
            ],
        ],
        [
            'id' => 59,
            'path' => 'CONTRACTNGROUPID',
            'fullname' => 'IA.CONTRACT_AND_GROUP',
            'desc' => 'IA.NAME',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'contractngrouppick',
                'pickentity' => 'contractngrouppick',
                'pickfield' => ['PICKID', 'TYPE']
            ],
        ],
        [
            'id' => 60,
            'path' => 'PROJECTNGROUPID',
            'fullname' => 'IA.PROJECT_AND_GROUP',
            'desc' => 'IA.NAME',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'projectngrouppick',
                'pickentity' => 'projectngrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
            ],
        ],
        [
            'id' => 61,
            'path' => 'EMPLOYEENGROUPID',
            'fullname' => 'IA.EMPLOYEE_AND_GROUP',
            'desc' => 'IA.NAME',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'employeengrouppick',
                'pickentity' => 'employeengrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
            ]
        ],
        [
            'id' => 77,
            'path' => 'ASSETNGROUPID',
            'fullname' => 'IA.ASSET_AND_GROUP',
            'desc' => 'IA.NAME',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'assetngrouppick',
                'pickentity' => 'assetngrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
            ]
        ],
        [
            'id' => 43,
            'path' => 'DEPARTMENTID',
            'fullname' => 'IA.DEPARTMENT',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'department',
                'pickentity' => 'departmentpick',
            ],
            'noedit' => true,
            'nonew' => true,
            'renameable' => true,
        ],
        [
            'id' => 62,
            'path' => 'DEPARTMENTNGROUPID',
            'fullname' => 'IA.DEPARTMENT_AND_GROUP',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'departmentfilterpick',
                'pickentity' => 'departmentfilterpick',
                'pickfield' => array('PICKID', 'TYPE'),
            ],
        ],
        [
            'id' => 63,
            'path' => 'CLASSNGROUPID',
            'fullname' => 'IA.CLASS_AND_GROUP',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'classngrouppick',
                'pickentity' => 'classngrouppick',
                'pickfield' => array('PICKID', 'TYPE'),
            ],
        ],
        [
            'id' => 64,
            'path' => 'LOCATIONNGROUPID',
            'fullname' => 'IA.LOCATION_AND_GROUP',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'locationfilterpick',
                'pickentity' => 'locationfilterpick',
                'pickfield' => array('PICKID', 'TYPE'),
            ],
        ],
        [
            'id' => 44,
            'path' => 'LOCATIONID',
            'fullname' => 'IA.LOCATION',
            'type' => [
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'location',
                'pickentity' => 'locationpick',
            ],
            'noedit' => true,
            'nonew' => true,
            'renameable' => true,
        ],
        [
            'id'        => 45,
            'path'      => 'INVOICEBY',
            'fullname'  => 'IA.INVOICE_BY',
            'type' => [
                'ptype'         => 'multipick',
                'type'          => 'multipick',
                'delimiter'     => GenInvoiceUtil::INVOICEBY_DELIMITER,
                'validlabels' => [ 'IA.BILL_TO', 'IA.CONTRACT', 'IA.CUSTOMER', 'IA.PROJECT', ],
                'validvalues'   => [
                    GenInvoiceUtil::INVOICEBY_BILLTO,
                    GenInvoiceUtil::INVOICEBY_CONTRACT,
                    GenInvoiceUtil::INVOICEBY_CUSTOMER,
                    GenInvoiceUtil::INVOICEBY_PROJECT
                ],
                '_validivalues' => [
                    GenInvoiceUtil::INVOICEBYCODE_BILLTO,
                    GenInvoiceUtil::INVOICEBYCODE_CONTRACT,
                    GenInvoiceUtil::INVOICEBYCODE_CUSTOMER,
                    GenInvoiceUtil::INVOICEBYCODE_PROJECT
                ],
            ],
            'default'   => GenInvoiceUtil::INVOICEBY_DEFAULT_VALUE,
            'layout' => 'landscape',
            'transformFunction' => 'translateInvoiceBy'
        ],
        [
            'id'        => 46,
            'path'  => 'DOCPARID',
            'desc'  => 'IA.TRANSACTION_DEFINITION',
            'fullname' => 'IA.TRANSACTION_DEFINITION',
            'type' => [
                'ptype'         => 'enum',
                'type'          => 'enum',
                'validlabels'   => [],
                'validvalues'   => [],
                '_validivalues' => [],
            ],
            'required' => true
        ],
        [
            'id'        => 47,
            'path'      => 'INVOICEMSG',
            'fullname'  => 'IA.INVOICE_MESSAGE',
            'type'      => [
                'ptype'     => 'multitext',
                'type'      => 'text',
                'maxlength' => 4000,
            ],
        ],
        [
            'id'        => 48,
            'path'      => 'INVOICEDATE',
            'fullname'  => 'IA.INVOICE_AND_GL_POST_DATE',
            'type'      => $gDateType,
            'required'  => true
        ],
        [
            'id'        => 49,
            'path'      => 'EXCHRATETYPEID',
            'fullname'  => 'IA.EXCHANGE_RATE_TYPE_ID',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 50,
            'path'      => 'ISTEMP',
            'fullname'  => 'IA.TEMPORARY',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false'
        ],
        [
            'id'        => 51,
            'path' => 'BILLSCHSTARTDATE',
            'fullname' => 'IA.SCHEDULED_BILLING_START_DATE',
            'desc' => 'IA.NAME',
            'type' => $gDateType,
        ],
        [
            'id'        => 52,
            'path'      => 'CONTRACTGROUPKEY',
            'fullname'  => 'IA.CONTRACT_GROUP_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
        ],
        [
            'id'        => 53,
            'path'      => 'CONTRACTGROUPID',
            'fullname'  => 'IA.CONTRACT_GROUP',
            'type'      => [
                'ptype'         => 'ptr',
                'type'          => 'text',
                'entity'        => 'contractgroup',
                'pickentity'    => 'contractgroup',
            ],
        ],
        [
            'id'        => 54,
            'path'      => 'GLPOSTDATE',
            'fullname'  => 'IA.GL_POST_DATE',
            'required'  => true,
            'type'      => $gDateType,
        ],
        [
            'id'        => 55,
            'path'      => 'ITEMID',
            'fullname'  => 'IA.ITEM_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 56,
            'path'      => 'ITEMGROUPID',
            'fullname'  => 'IA.ITEM_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 57,
            'path'      => 'ISSTALE',
            'fullname'  => 'IA.IS_STALE',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false'
        ],
        [
            'id'        => 65,
            'path'      => 'CUSTOMERGROUPID',
            'fullname'  => 'IA.CUSTOMER_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 66,
            'path'      => 'PROJECTGROUPID',
            'fullname'  => 'IA.PROJECT_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 67,
            'path'      => 'DEPARTMENTGROUPID',
            'fullname'  => 'IA.DEPARTMENT_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 68,
            'path'      => 'EMPLOYEEGROUPID',
            'fullname'  => 'IA.EMPLOYEE_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 78,
            'path'      => 'ASSETGROUPID',
            'fullname'  => 'IA.ASSET_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 69,
            'path'      => 'LOCATIONGROUPID',
            'fullname'  => 'IA.LOCATION_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 70,
            'path'      => 'CLASSGROUPID',
            'fullname'  => 'IA.CLASS_GROUP_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        [
            'id'        => 75,
            'path'      => 'SCHEDULED',
            'fullname'  => 'IA.SCHEDULED',
            'type'      => Globals::$g->gBooleanType,
            'default'   => 'false',
            'hidden'    => true
        ],
        [
            'id'        => 79,
            'path'      => 'SERVICEPERIODOVERRIDEOPTION',
            'fullname'  => 'IA.OVERRIDE_SERVICE_PERIOD_DATES',
            'type'      => [
                'ptype' => 'radio',
                'type' => 'radio',
                'validlabels' => ['IA.DO_NOT_OVERRIDE', 'IA.OVERRIDE_EMPTY_DATES_ONLY', 'IA.OVERRIDE_ALL_DATES'],
                'validvalues' => [ContractCommonBillableLineServicePeriodHandler::DO_NOT_OVERRIDE,
                                  ContractCommonBillableLineServicePeriodHandler::OVERRIDE_EMPTY_DATES_ONLY,
                                  ContractCommonBillableLineServicePeriodHandler::OVERRIDE_ALL_DATES],
                '_validivalues' => ['N', 'E', 'A'],
            ],
            'default'   => ContractCommonBillableLineServicePeriodHandler::DO_NOT_OVERRIDE,
            'showlabelalways' => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_SERVICE_PERIOD']
        ],
        [
            'id'        => 80,
            'path'      => 'SERVICEPERIODSTARTDATE',
            'fullname'  => 'IA.CN_SERVICE_PERIOD_START_DATE',
            'type'      => $gDateType,
            'hidden' => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_SERVICE_PERIOD']
        ],
        [
            'id'        => 81,
            'path'      => 'SERVICEPERIODENDDATE',
            'fullname'  => 'IA.CN_SERVICE_PERIOD_END_DATE',
            'type'      => $gDateType,
            'hidden' => true,
            EntityManager::FEATURE_FLAGS_AND => ['ENABLE_SERVICE_PERIOD']
        ],
        /*-------- UI-only fields -----------*/
        [
            'path'      => 'CUSTOMERIDHTML',
            'fullname'  => 'IA.CUSTOMER',
            'desc'      => 'IA.NAME',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'size'      => 20,
            ],
            'isHTML'    => true,
            'readonly'  => true,
        ],
        [
            'path'      => 'CONTRACTIDHTML',
            'fullname'  => 'IA.CONTRACT',
            'desc'      => 'IA.NAME',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'size'      => 20,
            ],
            'isHTML'    => true,
            'readonly'  => true,
        ],
        [
            'path'      => 'BILLTONAMEHTML',
            'fullname'  => 'IA.BILL_TO',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'size'      => 20,
            ],
            'isHTML'    => true,
            'readonly'  => true,
        ],
        [
            'path'      => 'SHIPTONAMEHTML',
            'fullname'  => 'IA.SHIP_TO',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'size'      => 20,
            ],
            'isHTML'    => true,
            'readonly'  => true,
        ],
        [
            'path'      => 'TERMNAMEHTML',
            'fullname'  => 'IA.TERM',
            'desc'      => 'IA.TERM',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'size'      => 20,
            ],
            'isHTML'    => true,
            'readonly'  => true,
        ],
        [
            'path'      => 'PROJECTIDHTML',
            'fullname'  => 'IA.PROJECT',
            'desc'      => 'IA.PROJECT_NAME',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
                'size'      => 20,
            ],
            'isHTML'    => true,
            'readonly'  => true,
        ],
        [
            'path'      => 'TRX_AMOUNTHTML',
            'fullname'  => 'IA.TRANSACTION_AMOUNT',
            'desc'      => 'IA.TRANSACTION_AMOUNT',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'decimal',
                'size'      => 20,
            ],
            'isHTML'    => true,
            'readonly'  => true,
        ],
        [
            'path'      => 'AMOUNT',
            'fullname'  => 'IA.BASE_AMOUNT',
            'desc'      => 'IA.BASE_AMOUNT',
            'type'      => [
                'ptype'     => 'decimal',
                'type'      => 'decimal',
                'size'      => 20,
            ],
            'readonly'  => true,
            'hidden'  => true,
        ],
        [
            'path'      => 'MESSAGE',
            'fullname'  => 'IA.INVOICE_MESSAGE',
            'type'      => [
                'type'      => 'multitext',
                'ptype'     => 'multitext',
                'maxlength' => 4000,
                'showpopup' => true,
            ],
        ],
        [
            'path'      => 'PREBILLKEY',
            'fullname'  => 'IA.GENERATE_INVOICES_RECORD_KEY',
            'desc'      => 'IA.GENERATE_INVOICES_RECORD_KEY',
            'type'      => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
            'hidden'  => true,
        ],
        [
            'path'      => 'CUSTOMERCOUNTTXT',
            'fullname'  => '',
            'type'      => [
                'type'      => 'text',
                'ptype'     => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'INVOICECOUNTTXT',
            'fullname'  => '',
            'type'      => [
                'type'      => 'text',
                'ptype'     => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'RECORDTOTALAMOUNT',
            'fullname'  => '',
            'type'      => [
                'type'      => 'text',
                'ptype'     => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path'      => 'HEADERCURRENCY',
            'fullname'  => 'IA.CURRENCY',
            'desc'      => 'IA.CURRENCY',
            'type'      => [
                'type'      => 'text',
                'ptype'     => 'ptr',
                'entity'    => 'trxcurrencies'
            ],
            'readonly'  => true,
            'hidden'  => true,
        ],
        [
            'path'      => 'CREATEDBYUSER',
            'fullname'  => 'IA.CREATED_BY',
            'type'      => [
                'ptype'     => 'text',
                'type'      => 'text',
            ],
            'readonly'  => true,
        ],
        [
            'path' => 'INVOICEBYUI',
            'fullname' => 'IA.INVOICE_BY',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40,
            ],
        ],
        [
            'id'        => 72,
            'path'      => 'CLASSID',
            'fullname'  => 'IA.CLASS_ID',
            'type'      => [
                'ptype' => 'text',
                'type' => 'text',
            ],
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],

    'dbfilters' => [
        ['geninvoiceprebill.type', '=', GenInvoiceUtil::RECORD_TYPECODE_PREBILL],
    ],

    'platformProperties' => [
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ],

    'module'        => 'cn',
    'table'         => 'geninvoicerecord',
    'auditcolumns'  => true,
    'autoincrement' => 'RECORDNO',
    'vid'           => 'NAME',
    'nosysview'     => true,

    'printas'       => 'IA.GENERATE_INVOICES_PREVIEW_SNAPSHOT_RUN',
    'pluralprintas' => 'IA.GENERATE_INVOICES_PREVIEW_SNAPSHOT_RUNS',
    'audittrail_disabled' => true,
    'description' => 'IA.HEADER_INFORMATION_FOR_A_GIVEN_BILLING_RUN',
];
