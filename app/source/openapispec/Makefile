# Makefile

ifndef SANDBOX
	# SANDBOX is the parent of /app
	SANDBOX = ../../..
endif

ifndef TOOLSLOC
	TOOLSLOC = $(SANDBOX)/app/tools
endif

OAS_REGISTRY_BUILDER = $(TOOLSLOC)/eng/build_temp_registry.bin

OAS_BUILD_COMPONENTS = ../../build/components

all: sentinel

generator: convert mm_services update_v0_registry validate_registry

OAS_TARGET ?= $(PWD)/$(OAS_BUILD_COMPONENTS)

REGISTRY_FOLDER ?= $(PWD)/../api/registries/

clean:
	@rm -rf "$(OAS_TARGET)"/../RegistryV0*.json
	@rm -rf "$(OAS_TARGET)"

javaconvert:
	@if ! command -v java-ds >/dev/null ; then \
		echo "ERROR: The 'java-ds' command does not exist."; \
		exit 1; \
	else \
		echo "The version used for java is:"; \
		java-ds --version; \
	fi
	@echo "Running YAML Converter";
	@if ! java-ds -jar $(IA_BUILD_TOOLS)/ia-yaml-converter-14.jar "./" "$(OAS_TARGET)" --version --addMD5; then \
		echo "One or more files failed conversion of the API Objects from Yaml to Json"; \
		exit 1; \
	fi

convert: javaconvert
	@echo "Generate sentinel"; \
	tar cf - . | md5sum > $(OAS_TARGET)/yaml.sentinel

sentinel:
ifneq ("$(wildcard $(OAS_TARGET)/yaml.sentinel)","")
	@echo "Checking for API schema update"; \
	tar cf - . | md5sum > $(OAS_TARGET)/tmp.sentinel; \
	cmp --silent $(OAS_TARGET)/tmp.sentinel $(OAS_TARGET)/yaml.sentinel; \
	if [ $$? -eq 0 ]; then \
		echo "No schema change detected, re-using existing objects"; else \
		$(MAKE) generator;\
	fi
else
	$(MAKE) generator
endif

update_v0_registry:
	@echo "Build RegistryV0"
	@cd $(OAS_TARGET); $(OAS_REGISTRY_BUILDER)

validate_registry:
	@echo "Validate the Registries"
	@if ! java-ds -jar $(IA_BUILD_TOOLS)/registry-application-1.jar "$(REGISTRY_FOLDER)" "$(OAS_TARGET)" --validate; then \
  		echo "One or more Registry in Beta1 format is not valid"; \
  		make clean; \
		exit 1; \
	fi
	@if ! java-ds -jar $(IA_BUILD_TOOLS)/registry-application-3.jar "$(REGISTRY_FOLDER)" "$(OAS_TARGET)" --validate; then \
  		echo "One or more Registry in Beta2 format is not valid"; \
  		make clean; \
		exit 1; \
	fi

spectral:
	@echo "Running Spectral Linting"; \
	$(eval SPECTRAL_OUTPUT=$(shell spectral lint ./*/paths/*.api.yaml --ignore-unknown-format --ruleset=.core-rules.yml -D)) \
	echo '$(SPECTRAL_OUTPUT)' | grep -i "No results with a severity of error found!" > /dev/null 2>&1; \
	if [ $$? -ne 0 ]; then \
	   echo '$(SPECTRAL_OUTPUT)'; \
	   exit 1; \
	fi


mm_services: # maintenance_mode_services
	@cd ../../tools/eng; ./gen_mm_service_list.bin; cd - > /dev/null
