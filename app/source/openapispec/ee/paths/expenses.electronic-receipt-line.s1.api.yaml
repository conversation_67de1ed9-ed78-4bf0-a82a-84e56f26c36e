openapi: 3.0.0
info:
  title: electronic receipt line
  description: Electronic receipt line API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Electronic receipt lines
    description: Details for an electronic receipt line on an electronic receipt, including individual expenses and their attributes. 
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/expenses/electronic-receipt-line:
    get:
      summary: List electronic receipt lines
      description: Returns a collection with a key, ID, and link for each electronic receipt line.   
      tags:
        - Electronic receipt lines
      operationId: list-expenses-electronic-receipt-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                description: List of electronic receipt line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of electronic receipt lines:
                  value:
                    'ia::result':
                      - key: '100'
                        id: '100'
                        href: /objects/expenses/electronic-receipt-line/100
                      - key: '101'
                        id: '101'
                        href: /objects/expenses/electronic-receipt-line/101
                      - key: '102'
                        id: '102'
                        href: /objects/expenses/electronic-receipt-line/102
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 5
                      next: 0
                      previous: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/expenses/electronic-receipt-line/{key}':
    parameters:
      - name: key
        description: System-assigned key for the electronic receipt line.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an electronic receipt line
      description: Returns detailed information for a specified electronic receipt line. 
      tags:
        - Electronic receipt lines
      operationId: get-expenses-electronic-receipt-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                description: Details of the electronic receipt line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/electronic-receipt-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an electronic receipt line:
                  value:
                    'ia::result':
                      key: '312'
                      id: '312'
                      electronic-receipt:
                        id: '136'
                        key: '136'
                        href: /objects/expenses/electronic-receipt/136
                      glAccount:
                        key: '158'
                        id: '6775.30'
                        name: Travel
                        href: /objects/gl-account/account/158
                      entryDate: '2025-03-11'
                      paidTo: Stella Johnson
                      paidFor: Hotel stay
                      dimensions:
                        location:
                          key: '1'
                          id: '1'
                          name: 'USA'
                          href: /objects/company-config/location/1
                        department:
                          key: '1'
                          id: '1'
                          name: 'IT'
                          href: /objects/company-config/department/1
                        class:
                          key: '731'
                          id: 'REST_CLS_001'
                          name: 'Education'
                          href: /objects/company-config/class/731
                        item:
                          key: '13'
                          id: '13'
                          name: 'Support'
                          href: /objects/inventory-control/item/13
                        employee:
                          key: '10'
                          id: '10'
                          href: /objects/company-config/employee/10
                        vendor:
                          key: '357'
                          id: '*************'
                          name: 'Boston Properties'
                          href: /objects/accounts-payable/vendor/357
                        customer:
                          key: '13'
                          id: '113'
                          name: 'Jack In the Box'
                          href: /objects/accounts-payable/customer/13
                        project:
                          key: '2'
                          id: 'NET-XML30-2'
                          name: 'Binford Implementation'
                          href: /objects/projects/project/2
                        task:
                          key: '2'
                          id: 'tet'
                          name: 'Implementation services'
                          href: /objects/projects/task/2
                        warehouse:
                          key: '6'
                          id: 'WH01'
                          name: 'WH01 Name'
                          href: /objects/inventory-control/warehouse/6
                      lineNumber: 1
                      expenseType:
                        key: Meals
                        id: '6000'
                      state: draft
                      quantity: '5'
                      unitRate: '20'
                      currency: 'USD'
                      txnAmount: '100'
                      href: /objects/expenses/electronic-receipt-line/312
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    electronic-receipt-line:
      $ref: ../models/expenses.electronic-receipt-line.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml