title: Employee expense type
x-mappedTo: eeaccountlabel
type: object
description: Details for an employee expense type.
properties:
  key:
    type: string
    description: System-assigned key for the employee expense type.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '47'
  id:
    type: string
    description: Unique identifier for the employee expense type.  
    x-mappedTo: ACCOUNTLABEL
    example: Travel
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  description:
    type: string
    description: Description of the employee expense type.
    x-mappedTo: DESCRIPTION
    example: Non-reimbursable expense for travel
  href:
    type: string
    description: URL endpoint for the expense type. 
    readOnly: true
    example: /objects/expenses/employee-expense-type/47  
  glAccount:
    type: object
    x-object: general-ledger/account
    x-mappedTo: glaccount
    description: GL account assigned to the employee expense type.
    properties:
      key:
        type: string
        description: Unique key for the GL account.
        x-mappedTo: GLACCOUNTRECORDNO
        example: '5500'
      id:
        type: string
        description: Unique identifier for the GL account.
        x-mappedTo: GLACCOUNTNO
        example: Travel expenses
      name:
        type: string
        description: Name of the GL account.
        x-mappedTo: GLACCOUNTTITLE
        readOnly: true
        example: Automobile expenses
      href:
        type: string
        description: URL endpoint for the GL account.
        readOnly: true
        example: /objects/general-ledger/account/5500
  offsetGLAccount:
    type: object
    x-object: general-ledger/account
    x-mappedTo: glaccount
    description: Offset account assigned to the employee expense type or the default GL account for employee liabilities. 
    properties:
      key:
        type: string
        description: Unique key for the offset GL account.
        x-mappedTo: GLOFFSETKEY
        nullable: true
        example: '6000'
      id:
        type: string
        description: Unique identifier for the offset GL account.
        x-mappedTo: OFFSETGLACCOUNTNO
        nullable: true
        example: Offset travel expenses
      name:
        type: string
        description: Name of the offset GL account.
        x-mappedTo: OFFSETGLACCOUNTTITLE
        readOnly: true
        nullable: true
        example: Offset automobile expenses
      href:
        type: string
        description: URL endpoint for the offset GL account.
        readOnly: true
        example: /objects/general-ledger/account/6000
  item:
    type: object
    x-object: inventory-control/item
    x-mappedTo: item
    description: Defined category of expense, such as software, meals or training. (Projects subscription)
    properties:
      key:
        type: string
        description: Unique key for the item.
        x-mappedTo: ITEMKEY
        nullable: true
        example: '311'
      id:
        type: string
        description: Unique identifier for the item.
        x-mappedTo: ITEMID
        nullable: true
        example: '10'
      name:
        type: string
        description: Name of the item.
        x-mappedTo: ITEMNAME
        readOnly: true
        nullable: true
        example: Laptop
      href:
        type: string
        description: URL endpoint for the item.
        readOnly: true
        example: /objects/inventory-control/item/311
  unitCurrency:
    type: string
    description: Currency of the transaction amount for multi-currency companies and rate-based expenses.
    x-mappedTo: UNITCURRENCY
    nullable: true
    example: USD
  form1099:
    type: object
    description: Form 1099 information for the employee expense type.
    readOnly: true
    properties:
      type:
        type: string
        description: Type of form 1099.
        x-mappedTo: FORM1099TYPE
        readOnly: true
        nullable: true
        example: MISC
      box:
        type: string
        description: Box value of form 1099.
        x-mappedTo: FORM1099BOX
        readOnly: true
        nullable: true
        example: '3'
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml
  unitRates:
    type: array
    description: Line items of the employee expense type.
    x-mappedTo: UNIT_RATES
    x-object: expenses/unit-rate
    items:
      $ref: expenses.unit-rate.s1.schema.yaml
