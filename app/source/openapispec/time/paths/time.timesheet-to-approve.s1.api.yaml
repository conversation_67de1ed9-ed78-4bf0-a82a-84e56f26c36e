openapi: 3.0.0
info:
  title: time-timesheet-to-approve
  description: Approve timesheet API
  version: '1.0'
  contact:
    name: suresh adiserla
    email: <EMAIL>
tags:
  - name: Approve timesheet
    description: timesheet-to-approve description -- explain what it's for and how it's used.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/time/timesheet-to-approve:
    get:
      summary: List
      description: 'Returns a collection with a key, ID, and link for each timesheet-to-approve.'
      tags:
        - Approve timesheet
      operationId: list-time-timesheet-to-approve
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of approve timesheet objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Approve timesheet:
                  value:
                    'ia::result':
                      - key: '101'
                        id: '101'
                        href: /objects/time/timesheet-to-approve/101
                      - key: '102'
                        id: '102'
                        href: /objects/time/timesheet-to-approve/102
                      - key: '103'
                        id: '103'
                        href: /objects/time/timesheet-to-approve/103
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: 0
                      previous: 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/time/timesheet-to-approve/{key}:
    parameters:
      - name: key
        description: system-assigned unique key for the timesheet-to-approve.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a/an timesheet-to-approve
      description: Returns detailed information for a particular timesheet-to-approve.
      tags:
        - Approve timesheet
      operationId: get-time-timesheet-to-approve-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the timesheet-to-approve
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/time-timesheet-to-approve'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the approve timesheet:
                  value:
                    'ia::result':
                      key: '23'
                      id: '23'
                      href: /objects/projects/timesheet/23
                      beginDate: '2023-01-01'
                      endDate: '2023-12-31'
                      postingDate: '2023-01-01'
                      state: submitted
                      unitOfMeasure: Hours
                      hoursInDay: 8
                      billableQuantity: 40.00
                      nonBillableQuantity: 2.00
                      description: Week of 2023-01-01
                      calculationMethod: hourly
                      postActualLaborCost: false
                      employee:
                        key: '973'
                        id: E-001
                        href: /objects/company-config/employee/973
                      employeeContact:
                        key: '973'
                        name: John Smith
                        firstName: John
                        lastName: Smith
                        href: /objects/company-config/contact/973
                      attachment:
                        key: '8420'
                        id: Attach-01
                        href: string
                      lines:
                        - key: '1411'
                          id: '1411'
                          href: /objects/time/timesheet-to-approve-line/1411
                          timesheetToApprove:
                            key: '11'
                            id: '11'
                            href: /objects/time/timesheet-to-approve/11
                          entryDate: '2023-04-01'
                          quantity: 6
                          lineNumber: 1
                          description: Week of 04/01/23
                          notes: Talked to client regarding project
                          state: approved
                          timeType:
                            key: '1'
                            id: Salaries At Root
                            name: Salaries At Root
                            href: /objects/projects/time-type/1
                          isBillable: true
                          isBilled: 'false'
                          statisticalJournal:
                            key: '7483'
                            id: TSSJ
                            href: /objects/general-ledger/statistical-journal/7483
                          billableUtilizedGLAccount:
                            key: '8293'
                            id: '9293'
                            href: /objects/general-ledger/statistical-account/8293
                          nonBillableUtilizedGLAccount:
                            key: '8294'
                            id: '9294'
                            href: /objects/general-ledger/statistical-account/8294
                          billableNonUtilizedGLAccount:
                            key: '8295'
                            id: '9295'
                            href: /objects/general-ledger/statistical-account/8295
                          nonBillableNonUtilizedGLAccount:
                            key: '8296'
                            id: '9296'
                            href: /objects/general-ledger/statistical-account/8296
                          hours:
                            billable: 8
                            nonBillable: 2
                            approved: 10
                            approvedBillable: 8
                            approvedNonBillable: 2
                            utilized: 10
                            nonUtilized: 4
                            approvedUtilized: 3
                            approvedNonUtilized: 2
                          externalPayroll:
                            costRate: 1
                            billingRate: 1
                            amount: '90'
                            employerTaxes: 15
                            fringes: 10
                            cashFringes: 2
                          laborClass:
                            key: '15'
                            id: LC001
                            name: Labor Class
                            href: /objects/construction/labor-class/15
                          laborShift:
                            key: '18'
                            id: LS001
                            name: Labor Shift
                            href: /objects/construction/labor-shift/18
                          laborUnion:
                            key: '20'
                            id: LU001
                            name: Labor Union
                            href: /objects/construction/labor-union/20
                          dimensions:
                            location:
                              key: '1'
                              id: '1'
                              name: USA
                            department:
                              key: '1'
                              id: '1'
                              name: IT
                            employee:
                              key: '10'
                              id: EMP-10
                              name: 'Thomas, Glenn'
                              href: /objects/company-config/employee/10
                            project:
                              key: '2'
                              id: NET-XML30-2
                              name: Talcomp training
                              href: /objects/projects/project/2
                            customer:
                              key: '13'
                              id: CUST-13
                              name: Jack In the Box
                              href: /objects/accounts-receivable/customer/13
                            vendor:
                              key: '357'
                              id: '*************'
                              name: GenLab
                              href: /objects/accounts-payable/vendor/357
                            item:
                              key: '13'
                              id: Case 13
                              name: Platform pack
                              href: /objects/inventory-control/item/13
                            warehouse:
                              key: '6'
                              id: WH01
                              name: WH01
                              href: /objects/inventory-control/warehouse/6
                            class:
                              key: '731'
                              id: REST_CLS_001
                              name: Enterprises
                              href: /objects/company-config/class/731
                            task:
                              id: '1'
                              key: '1'
                              name: Project Task
                              href: /objects/projects/task/1
                            costType:
                              id: '2'
                              key: '2'
                              name: Project Expense
                              href: /objects/construction/cost-type/2
                            asset:
                              id: A001
                              key: '1'
                              name: Laptop 1
                              href: /objects/asset/1
                            contract:
                              id: CON-0045-1
                              key: '12'
                              name: ACME Widgets - Service
                              href: /objects/contracts/contract/12
                          audit:
                            createdDateTime: '2022-04-20T16:20:00Z'
                            modifiedDateTime: '2022-04-20T16:20:00Z'
                            createdBy: '1'
                            modifiedBy: '95'
                          entity:
                            key: '46'
                            id: Western Region
                            name: Western Region
                            href: /objects/company-config/entity/46
                      audit:
                        createdDateTime: '2022-04-20T16:20:00Z'
                        modifiedDateTime: '2022-04-20T16:20:00Z'
                        createdBy: '1'
                        modifiedBy: '95'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /workflows/time/timesheet-to-approve/approve:
    post:
      summary: Approve a timesheet
      description: When timesheet approval is enabled, submitted timesheets are placed in an approval queue. Designated approvers review these timesheets and approve them. For more information, see [About Approve or decline timesheets](https://www.intacct.com/ia/docs/en_US/help_action/Time_and_Expenses/Timesheets/approve-or-decline-a-timesheet.htm?tocpath=Applications%7CTime%20and%20Expenses%7CTimesheets%7C_____7) section in the Sage Intacct Help Center.
      tags:
        - Approve timesheet
      operationId: approve-time-timesheet
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/time.timesheet-to-approve.s1.workflows.yaml#/time-timesheet-to-approve-actions-approve-request
            examples:
              Approve a timesheet request:
                value:
                  key: '156'
                  comments: Approved by Admin
              Partially approve a timesheet request:
                value:
                  key: '155'
                  comments: Approved by Admin
                  lines:
                    - '382'
                    - '383'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../workflows/time.timesheet-to-approve.s1.workflows.yaml#/time-timesheet-to-approve-actions-approve-response
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Approve a timesheet response:
                  value:
                    'ia::result':
                      id: '156'
                      key: '156'
                      state: approved
                      href: /objects/time/timesheet-to-approve/156
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
                Partially approve a timesheet response:
                  value:
                    'ia::result':
                      id: '155'
                      key: '155'
                      state: partiallyApproved
                      href: /objects/time/timesheet-to-approve/155
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /workflows/time/timesheet-to-approve/decline:
    post:
      summary: Decline a timesheet
      description: When timesheet approval is enabled, submitted timesheets are placed in an approval queue. Designated approvers review these timesheets and decline them. For more information, see [About Approve or decline timesheets](https://www.intacct.com/ia/docs/en_US/help_action/Time_and_Expenses/Timesheets/approve-or-decline-a-timesheet.htm?tocpath=Applications%7CTime%20and%20Expenses%7CTimesheets%7C_____7) section in the Sage Intacct Help Center.
      tags:
        - Approve timesheet
      operationId: decline-time-timesheet
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/time.timesheet-to-approve.s1.workflows.yaml#/time-timesheet-to-approve-actions-decline-request
            examples:
              Decline a timesheet request:
                value:
                  key: '157'
                  comments: Declined by Admin
              Partially decline a timesheet request:
                value:
                  key: '155'
                  comments: Declined by Admin
                  lines:
                    - '382'
                    - '383'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../workflows/time.timesheet-to-approve.s1.workflows.yaml#/time-timesheet-to-approve-actions-decline-response
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Decline a timesheet response:
                  value:
                    'ia::result':
                      id: '157'
                      key: '157'
                      state: declined
                      href: /objects/time/timesheet-to-approve/157
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
                Partially decline a timesheet response:
                  value:
                    'ia::result':
                      id: '155'
                      key: '155'
                      state: partiallyDeclined
                      href: /objects/time/timesheet-to-approve/155
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /workflows/time/timesheet-to-approve/recall:
    post:
      summary: Recall a timesheet
      description: Recalls a timesheet after it has been submitted and not yet approved. The transaction state is changed from submitted to draft. This allows changes to be made without going through the approval process or having the transaction edited by someone with permission to edit. For more information, see [About timesheets](https://www.intacct.com/ia/docs/en_US/help_action/Time_and_Expenses/Timesheets/ab-TOC-timesheets.htm?tocpath=Applications%7CTime%20and%20Expenses%7CTimesheets%7C_____0) section in the Sage Intacct Help Center.
      tags:
        - Approve timesheet
      operationId: recall-time-timesheet
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/time.timesheet-to-approve.s1.workflows.yaml#/time-timesheet-to-approve-actions-recall-request
            examples:
              Recall timesheet request:
                value:
                  key: '155'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../workflows/time.timesheet-to-approve.s1.workflows.yaml#/time-timesheet-to-approve-actions-recall-response
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Recall timesheet response:
                  value:
                    'ia::result':
                      id: '155'
                      key: '155'
                      state: draft
                      href: /objects/time/timesheet-to-approve/155
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    time-timesheet-to-approve:
      $ref: ../models/time.timesheet-to-approve.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
