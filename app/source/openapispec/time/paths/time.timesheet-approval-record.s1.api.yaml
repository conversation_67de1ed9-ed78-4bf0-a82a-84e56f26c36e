openapi: 3.0.0
info:
  title: time-timesheet-approval-record
  description: Record of employee timesheet approvals.
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Timesheet approval records
    description: 'An approval record is the history of a specific timesheet entry’s approvals. The approval record does not exist for timesheet entries that are auto-approved or do not require approval. Approvals can include from 1 to 5 stages. The approval record also includes the current approval state. Approval states are: Submitted, Pending Approval, Approved, and Declined. Note that a subscription to Projects is required to approve timesheets.'  
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/time/timesheet-approval-record:
    get:
      summary: List timesheet approval records
      description: 'Returns a collection with a key, ID, and link for each timesheet approval record.'
      tags:
        - Timesheet approval records
      operationId: list-time-timesheet-approval-record
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of timesheet-approval-record objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of time sheet approval record objects:
                  value:
                    'ia::result':
                      - key: '46'
                        id: '46'
                        href: /objects/time/timesheet-approval-record/46
                      - key: '44'
                        id: '44'
                        href: /objects/time/timesheet-approval-record/44
                      - key: '40'
                        id: '40'
                        href: /objects/time/timesheet-approval-record/40
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 5
                      next: 0
                      previous: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/time/timesheet-approval-record/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the timesheet approval record.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a timesheet approval record
      description: Returns detailed information for a particular timesheet approval record.
      tags:
        - Timesheet approval records
      operationId: get-time-timesheet-approval-record-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the timesheet-approval-record
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/time-timesheet-approval-record'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Example 1:
                  value:
                    'ia::result':
                      key: '200'
                      id: '200'
                      href: /objects/time/timesheet-approval-record/200 
                      approvalStage: 1
                      approvalType: Department Approval
                      approvalLevel: '2'
                      comments: Approved by Admin
                      recordDate: '2021-01-23'
                      state: submitted
                      timesheet:
                        key: '101'
                        id: '101'
                        href: /objects/time/timesheet/101
                      timesheetLine:
                        key: '142'
                        id: '142'
                        lineNumber: 3
                        href: /objects/time/timesheet-line/142
                      approvedBy:
                        key: '202'
                        id: 'cjackson'
                        href: /objects/company-config/user/202
                      approver:
                        key: '203'
                        id: jlee
                        href: /objects/company-config/user/203
                      completedBy:
                        key: '204'
                        id: 'gadams'
                        href: /objects/company-config/user/204
                      initiatedBy:
                        key: '205'
                        id: 'vluce'
                        href: /objects/company-config/user/205
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    time-timesheet-approval-record:
      $ref: ../models/time.timesheet-approval-record.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
