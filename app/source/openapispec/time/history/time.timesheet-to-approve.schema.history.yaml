'ia::definition':
    methodPermissions:
        GET:
            - pa/activities/approvetimesheet/view
            - pa/activities/approvetimesheet
    workflowPermissions:
        approve:
            - pa/activities/approvetimesheet
            - ee/activities/approvetimesheet
        decline:
            - pa/activities/approvetimesheet
            - ee/activities/approvetimesheet
        recall:
            - pa/lists/mytimesheet/edit
            - ee/lists/mytimesheet/edit
s1:
    hash: '0'
    type: rootObject
    workflows:
        revision: s1
        hash: '0'
    systemViews:
        systemfw1:
            revision: s1
            hash: '0'
        systemfw2:
            revision: s1
            hash: '0'
        systemfw3:
            revision: s1
            hash: '0'
    uiMetadataHash: '0'
