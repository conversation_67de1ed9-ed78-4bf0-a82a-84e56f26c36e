key: purchasing/price-schedule::systemPurchasing/price-scheduleFW1
id: systemPurchasing/price-scheduleFW1
object: purchasing/price-schedule
name: All
description: Specifies all active purchasing/price-schedules
query:
  object: purchasing/price-schedule
  fields:
    - id
    - description
    - discountPercent
    - priceList.id
    - status
  orderBy:
    - id: asc
  filters:
    - $eq:
        status: active
metadata:
  frozenColumnsCount: 2
  columns:
    - id: "id"
      format: "clip"
      size: 40
    - id: "description"
      format: "clip"
      size: 40
    - id: "discountPercent"
      format: "clip"
      size: 40
    - id: "priceList.id"
      format: "clip"
      size: 40
    - id: "status"
      format: "clip"
      size: 40
contexts:
  - __default