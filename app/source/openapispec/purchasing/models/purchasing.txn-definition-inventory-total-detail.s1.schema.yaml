title: purchasing-txn-definition-inventory-total-detail
x-mappedTo: podocumentpartotals
x-ownedBy: purchasing/txn-definition
type: object
description: Specify the inventory running total that will be affected by the transaction, whether to track the quantity, value, or both, and whether that amount will increase or decrease when the user saves the transaction.
properties:
  key:
    type: string
    description: System-assigned key for the transaction definition inventory total detail.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '88'
    maxLength: 8
  id:
    type: string
    description: System-assigned ID for the transaction definition inventory total detail. This value is the same as the key for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '88'
    maxLength: 8
  href:
    type: string
    description: URL endpoint for the transaction definition inventory total detail.
    readOnly: true
    example: /objects/purchasing/txn-definition-inventory-total-detail/88
  maintainType:
    type: string
    description: Specify whether to track the quantity, value, or both for the specified `inventoryTotal`.
    x-mappedTo: Q_QV
    enum:
      - quantity
      - value
      - quantityAndValue
    x-mappedToValues:
      - Quantity
      - Value
      - Quantity & Value
    example: value
  operation:
    type: string
    description: Specify whether the amount will increase or decrease when the user saves the transaction.
    x-mappedTo: SIGN
    enum:
      - add
      - subtract
    x-mappedToValues:
      - Add
      - Subtract
    example: add
  inventoryTotal:
    type: object
    x-mappedTo: invtotal
    x-object: inventory-control/total
    description: Specify the inventory total that will be affected by the transaction.
    properties:
      key:
        type: string
        description: System-assigned key for the inventory total.
        x-mappedTo: TOTALRECORDNO
        example: '45'
      id:
        type: string
        description: ID for the inventory total.
        x-mappedTo: TOTALID
        example: ONHAND
      href:
        type: string
        description: URL endpoint for the inventory total.
        readOnly: true
        example: /objects/inventory-control/total/45
  purchasingTxnDefinition:
    title: purchasing-txn-definition
    type: object
    description: Purchasing transaction definition associated with this inventory total detail.
    x-mappedTo: podocumentparams
    x-object: purchasing/txn-definition
    properties:
      key:
        type: string
        description: System-assigned key for the purchasing transaction definition.
        x-mappedTo: DOCPARNO
        example: '77'
        maxLength: 8
      id:
        type: string
        description: ID for the purchasing transaction definition.
        x-mappedTo: DOCPARID
        example: Purchase Quote
        maxLength: 30
      href:
        type: string
        description: URL for the purchasing transaction definition.
        readOnly: true
        example: /objects/purchasing/txn-definition/77
