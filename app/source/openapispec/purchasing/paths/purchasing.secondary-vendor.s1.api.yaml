openapi: 3.0.0
info:
  title: purchasing-secondary-vendor
  description: purchasing.secondary-vendor API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Secondary vendors
    description: The secondary vendor object allows a general contractor to include a secondary vendor on a primary document, for compliance purposes. If joint checks is enabled in Accounts Payable, secondary vendors from the primary document are copied to an AP bill during invoice creation. Construction subscription is needed.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/secondary-vendor:
    get:
      summary: List secondary vendors
      description: Returns a collection with a key, ID, and link for each secondary vendor.
      tags:
        - Secondary vendors
      operationId: list-purchasing-secondary-vendor
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of secondary vendor objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              example:
                List of secondary vendors:
                  value:
                    ia::result:
                      - key: '45'
                        id: '45'
                        href: /objects/purchasing/secondary-vendor/45
                      - key: '46'
                        id: '46'
                        href: /objects/purchasing/secondary-vendor/46
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a secondary vendor
      description: Creates a new secondary vendor.
      tags:
        - Secondary vendors
      operationId: create-purchasing-secondary-vendor
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-secondary-vendor'
                - $ref: '#/components/schemas/purchasing-secondary-vendorRequiredProperties'
            example:
              Create a secondary vendor:
                value:
                  name: Ace Plumbing
                  printAs: Ace Plumbing
                  primaryDocument:
                    key: '371'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New secondary vendor
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              example:
                Reference to new secondary vendor:
                  value:
                    ia::result:
                      id: '132'
                      key: '132'
                      href: /objects/purchasing/secondary-vendor/132
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/secondary-vendor/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the secondary vendor.
        in: path
        required: true
        schema:
          type: string
          example: '132'
    get:
      summary: Get a secondary vendor
      description: Returns detailed information for a specified secondary vendor.
      tags:
        - Secondary vendors
      operationId: get-purchasing-secondary-vendor-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the secondary vendor
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-secondary-vendor'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              example:
                Get a secondary vendor:
                  value:
                    ia::result:
                      id: '132'
                      key: '132'
                      primaryDocument:
                        key: '371'
                        id: Purchase order-PO#0045#doc
                        href: /objects/purchasing/document/371
                      name: Ace Plumbing
                      printAs: Ace Plumbing
                      generateInvoiceLienWaiver: true
                      generatePaymentLienWaiver: "doNotGenerate"
                      audit:
                        createdDateTime: '2024-12-04T00:00:00Z'
                        modifiedDateTime: '2024-12-04T00:00:00Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/purchasing/secondary-vendor/132
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a secondary vendor
      description: Updates an existing secondary vendor by setting field values. Any fields not provided remain unchanged.
      tags:
        - Secondary vendors
      operationId: update-purchasing-secondary-vendor-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-secondary-vendor'
                - type: object
                  properties:
                    id:
                      readOnly: true
            example:
              Update a secondary vendor:
                value:
                  printAs: Ace Plumbing & Hardware
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated secondary vendor
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              example:
                Reference to updated secondary vendor:
                  value:
                    ia::result:
                      id: '132'
                      key: '132'
                      href: /objects/purchasing/secondary-vendor/132
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a secondary vendor
      description: Deletes a secondary vendor.
      tags:
        - Secondary vendors
      operationId: delete-purchasing-secondary-vendor-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-secondary-vendor:
      $ref: ../models/purchasing.secondary-vendor.s1.schema.yaml
    purchasing-secondary-vendorRequiredProperties:
      type: object
      required:
        - name
        - printAs
        - primaryDocument
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml