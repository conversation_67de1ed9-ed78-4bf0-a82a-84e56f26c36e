openapi: 3.0.0
info:
  title: purchasing-approval-rule-set
  description: purchasing.approval-rule-set API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Purchasing approval rule sets
    description: A purchasing approval rule set is a predefined collection of rules and criteria that govern the approval process for purchasing transactions within an organization.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/approval-rule-set:
    get:
      summary: List purchasing approval rule sets
      description: Returns a collection with a key, ID, and link for each purchasing approval rule set.
      tags:
        - Purchasing approval rule sets
      operationId: list-purchasing-approval-rule-set
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of approval-rule-set objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of purchasing approval rule sets:
                  value:
                    ia::result:
                      - key: "6"
                        id: Transaction Department
                        href: /objects/purchasing/approval-rule-set/6
                      - key: '7'
                        id: Department based
                        href: /objects/purchasing/approval-rule-set/7
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a purchasing approval rule set
      description: Creates a new purchasing approval rule set.
      tags:
        - Purchasing approval rule sets
      operationId: create-purchasing-approval-rule-set
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/purchasing-approval-rule-set'
                - $ref: '#/components/schemas/purchasing-approval-rule-setRequiredProperties'
            examples:
              Creates a purchasing approval rule set:
                value:
                  id: Department based
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New approval-rule-set
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new purchasing approval rule set:
                  value:
                    ia::result:
                      key: 7
                      id: Department based
                      href: /objects/purchasing/approval-rule-set/7
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/approval-rule-set/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the purchasing approval rule set.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a purchasing approval rule set
      description: Returns detailed information for a specified purchasing approval rule set.
      tags:
        - Purchasing approval rule sets
      operationId: get-purchasing-approval-rule-set-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the approval-rule-set
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-approval-rule-set'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the purchasing approval rule set:
                  value:
                    ia::result:
                      id: Department based
                      key: '7'
                      audit:
                        createdDateTime: "2022-09-20T07:35:52Z"
                        modifiedDateTime: "2024-07-03T12:13:52Z"
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/purchasing/approval-rule-set/7
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a purchasing approval rule set
      description: Deletes a purchasing approval rule set.
      tags:
        - Purchasing approval rule sets
      operationId: delete-purchasing-approval-rule-set-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-approval-rule-set:
      $ref: ../models/purchasing.approval-rule-set.s1.schema.yaml
    purchasing-approval-rule-setRequiredProperties:
      type: object
      required:
        - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml