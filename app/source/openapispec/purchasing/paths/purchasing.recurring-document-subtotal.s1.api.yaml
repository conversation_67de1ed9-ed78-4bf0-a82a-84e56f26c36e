openapi: 3.0.0
info:
  title: purchasing-recurring-document-subtotal
  description: purchasing.recurring-document-subtotal API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: An<PERSON>.<PERSON><EMAIL>
tags:
  - name: Recurring document subtotals
    description: Details of subtotals, taxes, discounts, charges, and more for the transaction.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/purchasing/recurring-document-subtotal:
    get:
      summary: List recurring document subtotals
      description: Returns a collection with a key, ID, and link for each recurring document subtotal.
      tags:
        - Recurring document subtotals
      operationId: list-purchasing-recurring-document-subtotal
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of recurring-document-subtotal objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Purchasing recurring document subtotals:
                  value:
                    ia::result:
                      - key: '7'
                        id: '7'
                        href: /objects/purchasing/recurring-document-subtotal/7
                      - key: '8'
                        id: '8'
                        href: /objects/purchasing/recurring-document-subtotal/8
                      - key: '1520'
                        id: '1520'
                        href: /objects/purchasing/recurring-document-subtotal/15
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null

        '400':
          $ref: '#/components/responses/400error'
  '/objects/purchasing/recurring-document-subtotal/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the recurring document subtotal.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a recurring document subtotal
      description: Returns detailed information for a specified recurring document subtotal.
      tags:
        - Recurring document subtotals
      operationId: get-purchasing-recurring-document-subtotal-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the recurring-document-subtotal
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/purchasing-recurring-document-subtotal'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the Purchasing recurring document subtotal:
                  value:
                    ia::result:
                      id: '1520'
                      key: '1520'
                      recurringDocumentHeader:
                        key: '6'
                        id: '6'
                        documentType: Purchase Invoice
                        href: /objects/purchasing/recurring-document::Purchase%20Invoice/6
                      description: Purchase Tax
                      absoluteValue: '80.45'
                      percentValue: '8.25'
                      total: '80.**************'
                      dimensions:
                        location:
                          key: Chicago
                          id: '1'
                          href: /objects/company-config/location/Chicago
                        vendor:
                          key: '22'
                          id: CAR
                          href: /objects/accounts-payable/vendor/22
                      txnAbsoluteValue: '80.45'
                      txnTotal: '80.**************'
                      href: /objects/purchasing/recurring-document-subtotal/1520
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    purchasing-recurring-document-subtotal:
      $ref: ../models/purchasing.recurring-document-subtotal.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
