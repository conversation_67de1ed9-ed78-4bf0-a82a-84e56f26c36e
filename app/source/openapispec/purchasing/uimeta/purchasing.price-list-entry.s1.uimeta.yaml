uiLabel: IA.PRICE_LIST_ENTRIES
fields:
  endDate:
    uiType: date
    uiLabel: IA.END_DATE
  currency:
    uiType: text
    uiLabel: IA.CURRENCY
  id:
    uiType: text
    uiLabel: IA.RECORD_NUMBER
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  startDate:
    uiType: date
    uiLabel: IA.START_DATE
  value:
    uiType: decimal
    uiLabel: IA.VALUE
  valueType:
    uiType: enum
    uiLabel: IA.VALUE_TYPE
    enumsLabels:
      - label: IA.ACTUAL
        value: actual
      - label: IA.DOLLAR_MARKUP
        value: dollarMarkup
      - label: IA.DOLLAR_DISCOUNT
        value: dollarDiscount
      - label: IA.PERCENT_MARKUP
        value: markupPercent
      - label: IA.PERCENT_DISCOUNT
        value: discountPercent
  status:
    uiType: enum
    uiLabel: Status
    enumsLabels:
      -
        label: Active
        value: active
      -
        label: Inactive
        value: inactive
groups:
  audit:
    fields:
      createdBy:
        uiType: text
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: text
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
refs:
  productLine:
    fields:
      id:
        uiType: text
        uiLabel: IA.PRODUCT_LINE_ID
      key:
        uiType: text
        uiLabel: IA.PRODUCT_LINE_KEY
  item:
    fields:
      id:
        uiType: text
        uiLabel: IA.ITEM_ID
      key:
        uiType: text
        uiLabel: IA.ITEM_KEY
      name:
        uiType: text
        uiLabel: IA.ITEM_NAME
  employee:
    fields:
      id:
        uiType: text
        uiLabel: IA.EMPLOYEE_ID
      key:
        uiType: integer
        uiLabel: IA.EMPLOYEE_KEY
  status:
    uiType: enum
    uiLabel: Status
    enumsLabels:
      -
        label: Active
        value: active
      -
        label: Inactive
        value: inactive
  entity:
    fields:
      id:
        uiType: text
        uiLabel: IA.ENTITY_ID
      key:
        uiType: integer
        uiLabel: IA.ENTITY_KEY