title: console-template
x-mappedTo: cpatemplate
type: object
description: List of console templates
properties:
  key:
    type: string
    description: Template Key
    readOnly: true
    x-mappedTo: RECORDNO
    example: '101000900012350'
  id:
    type: string
    description: Template title
    x-mappedTo: TITLE
    readOnly: true
    example: 'SunSets:General template'
  guid:
    type: string
    description: Global unique identifier
    readOnly: true
    x-mappedTo: GUID
    example: 2C5CCF672BF8A3D1E06359065E0A5649
  consoleKey:
    type: string
    description: Console cny
    readOnly: true
    x-mappedTo: CONSOLE_CNY
    example: '********'
  name:
    type: string
    description: Template name
    x-mappedTo: NAME
    x-mutable: false
    example: General template
  description:
    type: string
    description: Template description
    x-mappedTo: DESCRIPTION
    example: General console template used for create new instances
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  accountingPeriods:
    type: string
    description: Template accounting periods
    x-mappedTo: NONSTANDARDPERIODS
    x-mutable: false
    default: standard
    example: standard
    enum:
      - null
      - standard
      - custom
    x-mappedToValues:
      - ''
      - F
      - T
  reportingMethod:
    type: string
    description: Reporting method
    x-mappedTo: ACCRUAL
    x-mutable: false
    default: accrual
    example: accrual
    enum:
      - null
      - accrual
      - cash
    x-mappedToValues:
      - ''
      - Accrual only
      - Cash only
  firstFiscalMonth:
    type: string
    description: The month that your fiscal year starts
    writeOnly: true
    x-mappedTo: FIRSTMONTH
    x-mutable: false
    default: january
    example: march
    enum:
      - january
      - february
      - march
      - april
      - may
      - june
      - july
      - august
      - september
      - october
      - november
      - december
    x-mappedToValues:
      - 1
      - 2
      - 3
      - 4
      - 5
      - 6
      - 7
      - 8
      - 9
      - 10
      - 11
      - 12
  firstTaxMonth:
    type: string
    description: The month that your annual tax reporting starts
    writeOnly: true
    x-mappedTo: FIRSTMONTHTAX
    x-mutable: false
    default: january
    example: march
    enum:
      - january
      - february
      - march
      - april
      - may
      - june
      - july
      - august
      - september
      - october
      - november
      - december
    x-mappedToValues:
      - 1
      - 2
      - 3
      - 4
      - 5
      - 6
      - 7
      - 8
      - 9
      - 10
      - 11
      - 12
  currencyFormat:
    type: string
    description: The primary or accounting currency of the company
    writeOnly: true
    x-mappedTo: CURRENCYFORMAT
    x-mutable: false
    default: unitedStates
    example: british
    enum:
      - australian
      - brazilian
      - british
      - canadian
      - danish
      - dutch
      - european
      - finnish
      - flemish
      - french
      - frenchCanadian
      - german
      - greek
      - indian
      - italian
      - mexican
      - norwegian
      - southAfrica
      - spanish
      - swedish
      - swissFrench
      - swissGerman
      - swissItalian
      - swissFranc
      - unitedStates
      - unitedArabEmirates
    x-mappedToValues:
      - Australian
      - Brazilian
      - British
      - Canadian
      - Danish
      - Dutch
      - European
      - Finnish
      - Flemish
      - French
      - French Canadian
      - German
      - Greek
      - Indian
      - Italian
      - Mexican
      - Norwegian
      - South Africa
      - Spanish
      - Swedish
      - Swiss French
      - Swiss German
      - Swiss Italian
      - Swiss Franc
      - United States
      - United Arab Emirates
  sourceTemplate:
    type: object
    description: Create new console template from existing template
    x-object: console/template
    properties:
      key:
        type: string
        x-mappedTo: INDUSTRY1KEY
        example: '101000900012365'
      name:
        type: string
        writeOnly: true
        x-mappedTo: INDUSTRY1
        x-mutable: false
        example: Scratch template
      href:
        type: string
        readOnly: true
        example: /objects/console/template/101000900012365
  sourceInstance:
    type: object
    description: Create new console template from existing instance
    x-object: console/instance
    properties:
      key:
        type: string
        x-mappedTo: INDUSTRY2KEY
        example: '101000900012345'
      id:
        type: string
        writeOnly: true
        x-mappedTo: INDUSTRY2
        x-mutable: false
        example: RisingSun
      href:
        type: string
        readOnly: true
        example: /objects/console/instance/101000900012345
  state:
    type: string
    description: State
    x-mappedTo: STATE
    x-mutable: false
    readOnly: true
    example: ready
    enum:
      - new
      - inProgress
      - ready
      - failed
    x-mappedToValues:
      - NEW
      - IN_PROGRESS
      - READY
      - FAILED
  error:
    type: string
    description: Error
    x-mappedTo: ERROR
    x-mutable: false
    nullable: true
    readOnly: true
    example: 'Cannot create console template'
  href:
    type: string
    description: Endpoint for the console template
    readOnly: true
    example: /objects/console/template/101000900012350
