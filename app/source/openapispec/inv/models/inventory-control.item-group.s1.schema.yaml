title: inventory-control-item-group
x-mappedTo: itemgroup
type: object
description: An item group categorizes item dimension records, mainly for the purpose of structuring financial reporting.
properties:
  key:
    type: string
    description: System-assigned key for the item-group.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Unique identifier for the Item group.
    x-mappedTo: ID
    example: '1st Level Items'
  href:
    type: string
    description: item group URL.
    readOnly: true
    example: /objects/inventory-control/item-group/23
  name:
    type: string
    description: Name of the item group.
    x-mappedTo: NAME
    example: 'Level 1 Items'
  description:
    type: string
    description: Description about the item group.
    x-mappedTo: DESCRIPTION
    example: "Level 1 Items"
  groupType:
    type: string
    description: |
      Group membership type. The group type and related fields determine which items are included in the group. If you are creating reports that use items in rows or columns, these fields also determine the column headings and row headings that will appear, and the order in which they will be listed.

      * `specific` - Specify the items to include and their order in the `groupMembers` array. You can optionally filter the array with the `memberFilter` settings.
      * `all` - All items that match the `memberFilter` criteria. 
    x-mappedTo: GROUPTYPE
    example: all
    enum:
      - all
      - specific
    x-mappedToValues:
      - ALL
      - SPECIFIC
  memberFilter:
    type: object
    title: memberFilter
    description: One or more filters to select the items to include in the item group. Can be used in addition to the `groupMembers` array to filter from a selected list of items.
    allOf:
      - $ref: ../../common/models/member-filter.s1.schema.yaml
      - type: object
        x-mappedToType: memberFilter:inventory-control/item
        x-mappedTo: MEMBERFILTERS
  groupMembers:
    type: array
    description: Members of the item group and their sort order. Only applicable when `groupType` = `specific`.
    title: members
    x-mappedTo: MEMBERS
    x-object: inventory-control/item-group-member
    items:
      $ref: ./inventory-control.item-group-member.s1.schema.yaml
  glAccountGroup:
    type: object
    description: General ledger (GL) account group associated with the item group.
    x-mappedTo: GLACCTGRP
    x-object: general-ledger/account-group
    properties:
      key:
        type: string
        x-mappedTo: DIMGRPCOMPKEY
        example: '23'
      id:
        type: string
        description: ID for the gl-account-group.
        x-mappedTo: DIMGRPCOMP
        example: "1st Level Items"
      href:
        type: string
        description: URL for the gl-account-group.
        readOnly: true
        example: /objects/inventory-control/gl-account-group/23
  createDimensionComponents:
    type: boolean
    description: Set to 'true' to create a dimension structure that enables the group to be used on the rows and columns of financial reports. If this field is set to false, the group can only be used for filtering.
    x-mappedTo: CREATEDIMCOMP
    default: false
    example: true
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml