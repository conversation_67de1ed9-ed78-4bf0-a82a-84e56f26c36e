openapi: 3.0.0
info:
  title: inventory-control-replenishment-forecast-line
  description: inventory-control.replenishment-forecast-line API
  version: '1.0'
  contact:
    email: <EMAIL>
    name: <PERSON><PERSON> Shan
tags:
  - name: Replenishment forecast lines
    description:  Line information for Replenishment forecast
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/inventory-control/replenishment-forecast-line:
    get:
      summary: List replenishment forecast lines
      description: Returns a collection with a key, ID, and link for each replenishment forecast line.
      tags:
        - Replenishment forecast lines
      operationId: list-inventory-control-replenishment-forecast-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of replenishment-forecast-line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of replenishment forecast lines:
                  value:
                    "ia::result":
                      - "key": "66"
                        "id": "66"
                        "href": "/objects/inventory-control/replenishment-forecast-line/66"
                      - "key": "67"
                        "id": "67"
                        "href": "/objects/inventory-control/replenishment-forecast-line/67"
                      - "key": "68"
                        "id": "68"
                        "href": "/objects/inventory-control/replenishment-forecast-line/68"
                    "ia::meta":
                      "totalCount": 3
                      "start": 1
                      "pageSize": 100
                      "next": null
                      "previous": null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/inventory-control/replenishment-forecast-line/{key}':
    parameters:
      - name: key
        description: system-assigned unique key for the replenishment forecast line.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a replenishment forecast line
      description: Returns detailed information for a particular replenishment forecast line.
      tags:
        - Replenishment forecast lines
      operationId: get-inventory-control-replenishment-forecast-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the replenishment-forecast-line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/inventory-control-replenishment-forecast-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the replenishment forecast line:
                  value:
                    "ia::result":
                      "id": "118"
                      "key": "118"
                      "item":
                        "key": "34"
                        "id": "B001"
                        "name": "Monitor-HP"
                        "href": "/objects/inventory-control/item/34"
                      "warehouse":
                        "id": "null"
                        "name": "null"
                      "effectiveDate": "2019-06-30"
                      "demandQuantity": "45"
                      "audit":
                        "createdDateTime": "2024-04-22T23:35:28Z"
                        "modifiedDateTime": "2024-04-23T17:15:31Z"
                        "createdBy": "1"
                        "modifiedBy": "1"
                      "replenishmentForecast":
                        "id": "3"
                        "key": "3"
                        "name": "B001 forecast"
                        "href": "/objects/inventory-control/replenishment-forecast/3"
                      "href": "/objects/inventory-control/replenishment-forecast-line/118"
                    "ia::meta":
                      "totalCount": 1
                      "totalSuccess": 1
                      "totalError": 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a replenishment forecast line
      description: Deletes a replenishment forecast line.
      tags:
        - Replenishment forecast lines
      operationId: delete-inventory-control-replenishment-forecast-line-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    inventory-control-replenishment-forecast-line:
      $ref: ../models/inventory-control.replenishment-forecast-line.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml