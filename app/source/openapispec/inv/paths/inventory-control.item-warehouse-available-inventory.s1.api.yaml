openapi: 3.0.0
info:
  title: inventory-control-item-warehouse-available-inventory
  description: inventory-control-item-warehouse-available-inventory API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <PERSON><PERSON><PERSON>.<EMAIL>
tags:
  - name: Item warehouse available inventory
    description: Information about available quantities for items in warehouse with tracking information.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/inventory-control/item-warehouse-available-inventory:
    get:
      summary: List item warehouse available inventory objects
      description: Returns a collection with a key, ID, and link for each inventory-control.item-warehouse-available-inventory.
      tags:
        - Item warehouse available inventory
      operationId: list-inventory-control-item-warehouse-available-inventory
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of inventory-control-item-warehouse-available-inventory objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List available inventory objects:
                  value:
                    'ia::result':
                      - key: '46863'
                        id: '46863'
                        href: /objects/inventory-control/item-warehouse-available-inventory/46863
                      - key: '46864'
                        id: '46864'
                        href: /objects/inventory-control/item-warehouse-available-inventory/46864
                      - key: '46865'
                        id: '46865'
                        href: /objects/inventory-control/item-warehouse-available-inventory/46865
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: 2
                      previous: 1
        '400':
          $ref: '#/components/responses/400error'
  '/objects/inventory-control/item-warehouse-available-inventory/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the item warehouse available inventory object.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an item warehouse available inventory object
      description: Returns detailed information for a particular item warehouse available inventory object.
      tags:
        - Item warehouse available inventory
      operationId: get-objects-inventory-control-item-warehouse-available-inventory-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the inventory-control-item-warehouse-available-inventory
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/inventory-control-item-warehouse-available-inventory'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the item warehouse available inventory:
                  value:
                    ia::result:
                      key: '46865'
                      id: '46865'
                      item: Dell_15_6_XPS_15_Multi_Touch
                      warehouse: WH10001
                      bin: Z4-A5-R3a-B10
                      aisle: A5
                      row: R3a
                      zone: Z4
                      serialNumber: '33213'
                      lotNumber: Lot2
                      expirationDate: '2024-07-26'
                      dateReceived: '2022-07-26'
                      quantity:
                        quantityLeft: '5.0000000000'
                        quantityAllocated: '1.0000000000'
                        quantityReserved: '2.0000000000'
                      itemDescription: 12th Gen Core™ i3-1215U (10 MB cache, 6 cores, 8 threads, up to 4.40 GHz Turbo)
                      unitOfMeasure: Each
                      warehouseName: WH10001--US AZ Warehouse 10001
                      href: /objects/inventory-control/item-warehouse-available-inventory/46865
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    inventory-control-item-warehouse-available-inventory:
      $ref: ../models/inventory-control.item-warehouse-available-inventory.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml