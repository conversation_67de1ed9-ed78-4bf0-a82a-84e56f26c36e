openapi: 3.0.0
info:
  title: item-group
  description: An item group categorizes item dimension records, mainly for the purpose of structuring financial reporting.
  version: '1.0'
  contact:
    email: ram<PERSON>.<EMAIL>
    name: <PERSON><PERSON> Shan
tags:
  - name: Item groups
    description: An item group categorizes item dimension records, mainly for the purpose of structuring financial reporting.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/inventory-control/item-group:
    get:
      summary: List item group objects
      description: Returns up to 100 item group references from the collection with a key, ID, and link for each item group. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      tags:
        - Item groups
      operationId: list-inventory-control-item-group
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of item-group objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List item groups:
                  value:
                    'ia::result':
                      - key: '1'
                        id: 'Top Level Items'
                        href: /objects/inventory-control/item-group/1
                      - key: '5'
                        id: 'Items with Activity'
                        href: /objects/inventory-control/item-group/5
                      - key: '6'
                        id: 'Costing Group'
                        href: /objects/inventory-control/item-group/6
                    'ia::meta':
                        totalCount: 3
                        start: 1
                        pageSize: 100
                        next: null
                        previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create an item group
      description: Creates a new item group.
      tags:
        - Item groups
      operationId: create-inventory-control-item-group
      requestBody:
        description: Create a new item group
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/inventory-control-item-group'
                - $ref: '#/components/schemas/inventory-control-item-groupRequiredProperties'
            examples:
              Create an item group:
                value:
                  id: "InsuranceItemsGroup"
                  "name": "Insurance Items Group"
                  "description": "Items that require insurance"
                  "groupType": "specific"
                  "createDimensionComponents": true
                  "memberFilter":
                        object: inventory-control/item
                        filterExpression: and
                        orderBy:
                          - name: asc
                  "groupMembers":
                    - "item":
                        "id": "Monitor-HP"
                      "sortOrder": 0
                    - "item":
                        "id": "Monitor-Dell"
                      "sortOrder": 1
                    - "item":
                        "id": "Monitor-Asus"
                      "sortOrder": 2
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New item group
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new item group:
                  value:
                    "ia::result":
                      "key": "14"
                      "id": "InsuranceItemsGroup"
                      "href": "/objects/inventory-control/item-group/14"
                    "ia::meta":
                      "totalCount": 1
                      "totalSuccess": 1
                      "totalError": 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/inventory-control/item-group/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the item group.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an item group
      description: Returns detailed information for a specified item group.
      tags:
        - Item groups
      operationId: get-inventory-control-item-group-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the item-group
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/inventory-control-item-group'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an item group:
                  value:
                    "ia::result":
                      "key": "12"
                      "id": "BooksGroup"
                      "name": "BooksGroup"
                      "description": "Group for Books"
                      "groupType": "all"
                      "memberFilter":
                        "object": "inventory-control/item"
                        "filterExpression": "and"
                        "filters":
                          - "$contains":
                              "productLineId": "BK"
                        "orderBy":
                          - "productLineId": "asc"
                      "audit":
                        "createdDateTime": "2023-12-12T23:33:05Z"
                        "modifiedDateTime": "2023-12-12T23:33:05Z"
                        "createdBy": "1"
                        "modifiedBy": "1"
                      "glAccountGroup":
                        "id": "BooksGroup"
                      "groupMembers": []
                      "href": "/objects/inventory-control/item-group/12"
                    "ia::meta":
                      "totalCount": 1
                      "totalSuccess": 1
                      "totalError": 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update an item group
      description: Updates an existing item group by setting field values. Any fields not provided remain unchanged.
      tags:
        - Item groups
      operationId: update-inventory-control-item-group-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/inventory-control-item-group'
                - type: object
                  properties:
                    id:
                      example: 12
                      readOnly: true
            examples:
              Update a single value:
                 value:
                   description: Group for Reference Books
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated item group:
                  value:
                    'ia::result':
                      key: '12'
                      id: 'BooksGroup'
                      href: /objects/inventory-control/item-group/12
                    'ia::meta':
                        totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete an item group
      description: Deletes an item group. An item group can be deleted if it is not being used for reporting.
      tags:
        - Item groups
      operationId: delete-inventory-control-item-group-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    inventory-control-item-group:
      $ref: ../models/inventory-control.item-group.s1.schema.yaml
    inventory-control-item-groupRequiredProperties:
      type: object
      required:
        - id
        - name
        - groupType
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
