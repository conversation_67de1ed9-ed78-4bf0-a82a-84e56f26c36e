title: platform-package-tracking
x-mappedTo: packagetracking
type: object
description: platform-package-tracking object
properties:
  key:
    type: string
    description: System-assigned key for the package tracking.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '38'
  id:
    type: string
    description: Unique identifier. This value is the same as `key` for this object.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '38'
  companyId:
    type: string
    description:  Unique identifier for tenant
    x-mappedTo: COMPANYID
    example: '49200'
    x-mutable: false
    nullable: false
  companyName:
    type: string
    description:  Unique identifier for tenant
    x-mappedTo: COMPANY_NAME
    example: 'intacct'
  companyType:
    type: string
    description: Company Type
    x-mappedTo: COMPANY_TYPE
    readOnly: true
    example: 'production'
  resourceType:
    type: string
    description: Resource type
    x-mappedTo: RESOURCE_TYPE
    example: application
    x-mutable: false
    nullable: false
    enum:
      - application
    x-mappedToValues:
      - APP
  packageId:
    type: string
    description: Package id. For applications, this is also known as original id.
    x-mappedTo: PACKAGE_ID
    example: 12345@1234565
    x-mutable: false
    nullable: false
  version:
    type: string
    description: version
    x-mappedTo: VERSION
    example: 11.2
    nullable: false
  name:
    type: string
    description: Package name
    x-mappedTo: NAME
    nullable: false
    example: MyApp
  isPushAllowed:
    type: boolean
    description: Is push allowed
    x-mappedTo: IS_PUSH_ALLOWED
    x-mappedToType: string
    example: true
    nullable: false
    default: false
  transactionMaps:
    type: array
    description: Transaction definitions mapping
    x-mappedTo: ITEMS
    x-object: platform/txn-map
    items:
      $ref: platform.txn-map.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  href:
    type: string
    description: Endpoint URL for the package tracking.
    readOnly: true
    example: /objects/platform/package-tracking/36
