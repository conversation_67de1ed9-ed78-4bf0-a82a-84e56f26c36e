title: platform-package-version
x-mappedTo: packagerepository
type: object
description: package-version object
properties:
  key:
    type: string
    description: System-assigned key for the package-version.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description:  ID.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the package-version.
    readOnly: true
    example: /objects/platform/package-version/23
  resourceType:
    type: string
    description: Resource type.
    x-mappedTo: RESOURCE_TYPE
    x-mutable: false
    enum:
      - application
    x-mappedToValues:
      - APP
    example: application
  packageId:
    type: string
    description: ID. For custom applications, this is the original ID.
    nullable: false
    x-mappedTo: PACKAGE_ID
    x-mutable: false
    example: '12314'
  version:
    type: string
    description: Version.
    x-mappedTo: VERSION
    nullable: false
    readOnly: true
    example:
  name:
    type: string
    description: Package name
    x-mappedTo: NAME
    nullable: false
    readOnly: true
    example:
  deprecated:
    type: boolean
    description: Deprecated.
    x-mappedTo: DEPRECATED
    default: false
    example: true
    x-mappedToType: string
  isPublic:
    type: boolean
    description: Public.
    x-mappedTo: IS_PUBLIC
    readOnly: true
    example: false
    x-mappedToType: string
  isManaged:
    type: boolean
    description: Is managed.
    x-mappedTo: IS_MANAGED
    x-mutable: false
    default: true
    example: true
    x-mappedToType: string
  allowPull:
    type: boolean
    description: Allow pull
    x-mappedTo: ALLOW_PULL
    default: false
    example: true
    x-mappedToType: string
  package:
    type: string
    description: Package.
    x-mappedTo: PACKAGE
    readOnly: true
    example: | 
      <Application id="10020" origId="44136485@10020" orderNo="11" isSystem="F" version="1.9" companyNo="44136485" >
          <DisplayName>test_app3</DisplayName>
          <Props>
              <isDeployed>1</isDeployed>
              <menuIds></menuIds>
          </Props>
          <DependentDefs></DependentDefs>
          <DataObjectDefs></DataObjectDefs>
          <Menus></Menus>
          <WebPages></WebPages>
          <Portals></Portals>
          <Version>2</Version>
          <MetaData>
              <PublishedFrom>IM01_TicketMaster-vali21</PublishedFrom>
              <PublishedBy>Admin</PublishedBy>
          </MetaData>
      </Application>
  description:
    type: string
    description: Description.
    x-mappedTo: DESCRIPTION
    example:
  note:
    type: string
    description: Note.
    x-mappedTo: NOTE
    example:
  publisher:
    type: string
    description: Publisher
    x-mappedTo: PUBLISHER
    readOnly: true
    example:
  companyId:
    type: string
    description: Unique identifier for tenant
    x-mappedTo: COMPANYID
    readOnly: true
    example: 
  audit:
    type: object
    title: audit
    properties:    
      createdBy:
        description: User who created this
        type: string
        example: '1'
        x-mappedTo: CREATEDBY
        readOnly: true
        nullable: false
      createdDateTime:
        description: Time of the submission
        type: string
        format: date-time
        example: 2022-04-20T16:20:00Z
        x-mappedTo: WHENCREATED
        readOnly: true
        nullable: false
