openapi: 3.0.0
info:
  title: user-view
  description: user-view API
  version: '1.0'
  contact:
    email: an<PERSON><PERSON>.<EMAIL>
    name: <PERSON><PERSON><PERSON>
tags:
  - name: User views
    description: User view description -- explain what it's for and how it's used.
servers:
  - url: 'http://localhost:3000'
paths:
  '/objects/user-view/{key}':
    parameters:
      - schema:
          type: string
          example: '262'
        name: key
        in: path
        required: true
        description: System-assigned key for the user view.
    get:
      summary: Get a user view
      description: Returns detailed information for a particular user view.
      tags:
        - User views
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the user-view
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/user-view'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Example-1:
                  value:
                    'ia::result':
                      key: '262'
                      id: '262'
                      name: vendorTest3
                      description: ' testing vendor query'
                      category: null
                      owner:
                        key: '1'
                        id: Admin
                        href: /objects/user/1
                      object: vendor
                      query:
                        object: vendor
                        fields:
                          - id
                          - name
                          - status
                          - href
                        filters:
                          - $eq:
                              status: active
                          - $eq:
                              billingType: openItem
                        filterExpression: 1 and 2
                        orderBy:
                          - id: asc
                      status: active
                      viewVersion: '0'
                      audit:
                        createdDateTime: '2021-05-16T17:41:55Z'
                        modifiedDateTime: '2021-05-17T17:41:55Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/user-view/262
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      operationId: get-objects-user-view-key-test
    patch:
      summary: Update a user view
      description: Updates an existing user view by setting field values. Any fields not provided remain unchanged.
      operationId: patch-objects-user-view-key-test
      tags:
        - User views
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated user-view
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
        '400':
          $ref: '#/components/responses/400error'
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/user-view'
                - type: object
                  properties:
                    id:
                      readOnly: true
    delete:
      summary: Delete a user view
      description: Deletes a user view.
      tags:
        - User views
      operationId: delete-objects-user-view-key-test
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
  /objects/user-view:
    get:
      summary: List user views
      description: Returns a collection with a key, ID, and link for each user view.
      tags:
        - User views
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of user-view objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
        '400':
          $ref: '#/components/responses/400error'
      operationId: get-objects-user-view-test
    post:
      summary: Create a user view
      description: Creates a new  user view.
      tags:
        - User views
      operationId: post-objects-user-view-test
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New user-view
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
        '400':
          $ref: '#/components/responses/400error'
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/user-view'
                - $ref: '#/components/schemas/user-viewRequiredProperties'
            examples:
              User view for vendor:
                value:
                  name: vendorTest5
                  query:
                    object: vendor
                    fields:
                      - id
                      - name
                      - status
                      - href
                    filters:
                      - $eq:
                          status: inactive
                      - $eq:
                          billingType: balanceForward
                    filterExpression: 1 and 2
                    orderBy:
                      - id: desc
components:
  schemas:
    user-view:
      $ref: ../models/user-view.s1.schema.yaml
    user-viewRequiredProperties:
      type: object
      required:
        - name
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
