title: Tax entries
description: Tax entries for line items.
type: object
properties:
  key:
    type: string
    description: System-assigned key for the tax entry.
    example: '7149'
    readOnly: true
    x-mappedTo: RECORDNO
  id:
    type: string
    description: Unique identifier for the tax entry.
    example: '7149'
    readOnly: true
    x-mappedTo: RECORDNO
  baseTaxAmount:
    type: string
    description: Base tax amount.
    format: decimal-precision-2
    example: '100.00'
    x-mappedTo: TAX
  txnTaxAmount:
    type: string
    description: Transaction tax amount. For a PATCH request, set to `null` if you want Sage Intacct to recalculate the amount, or set to the value you want if you don't want the system to recalculate.
    format: decimal-precision-2
    example: '100.00'
    x-mappedTo: TRX_TAX
  taxRate:
    type: number
    description: Tax rate.
    x-mappedTo: TAXRATE
    example: 1.0299