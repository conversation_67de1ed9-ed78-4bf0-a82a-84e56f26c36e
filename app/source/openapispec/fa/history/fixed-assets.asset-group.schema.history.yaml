ia::definition:
  methodPermissions:
    GET:
      - fa/lists/fixedassetgroup/view
    POST:
      - fa/lists/fixedassetgroup/create
    PATCH:
      - fa/lists/fixedassetgroup/edit
    DELETE:
      - fa/lists/fixedassetgroup/delete
s1:
  hash: '0'
  type: rootObject
  systemViews:
    systemAssetGroupFW1:
      revision: s1
      hash: '0'
    systemAssetGroupFW2:
      revision: s1
      hash: '0'
  uiMetadataHash: '0'
