openapi: 3.0.0
info:
  title: general-ledger-account-allocation-reverse
  description: general-ledger.account-allocation-reverse API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Account allocation reversals
    description: |
      Account allocation reverse creates offset entries to undo previous dynamic account allocation calculations.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/general-ledger/account-allocation-reverse:
    get:
      summary: List account allocation reversals
      description: Returns a collection with a key, ID, and link for each account allocation reversal.
      tags:
        - Account allocation reversals
      operationId: list-general-ledger-account-allocation-reverse
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation reversals
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List account allocation reversals:
                  value:
                    'ia::result':
                      - key: '16'
                        id: '16'
                        href: /objects/general-ledger/account-allocation-reverse/16
                      - key: '20'
                        id: '20'
                        href: /objects/general-ledger/account-allocation-reverse/20
                      - key: '21'
                        id: '21'
                        href: /objects/general-ledger/account-allocation-reverse/21
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/general-ledger/account-allocation-reverse/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account allocation reversal.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation reversal
      description: Returns detailed information for a specified account allocation reversal.
      tags:
        - Account allocation reversals
      operationId: get-general-ledger-account-allocation-reverse-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation reversal
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/general-ledger-account-allocation-reverse'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an account allocation reversal:
                  value:
                    'ia::result':
                      id: '21'
                      key: '21'
                      glAccountAllocation:
                        id: '29'
                        key: '29'
                        href: /objects/general-ledger/account-allocation/29
                      glAccount:
                        id: null
                        key: null
                        name: null
                      useSourceAccount: true
                      audit:
                        createdDateTime: '2024-06-25T12:16:48Z'
                        modifiedDateTime: '2024-06-25T12:58:13Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      dimensions:
                        location:
                          key: '72'
                          id: AZ
                          name: Arizona
                          href: /objects/company-config/location/72
                        department:
                          key: '6'
                          id: '6'
                          name: Marketing
                          href: /objects/company-config/department/6
                        project:
                          key: null
                          name: null
                          id: null
                        customer:
                          key: null
                          name: null
                          id: null
                        vendor:
                          key: null
                          name: null
                          id: null
                        employee:
                          key: null
                          name: null
                          id: null
                        item:
                          key: null
                          name: null
                          id: null
                        class:
                          key: null
                          name: null
                          id: null
                        contract:
                          key: null
                          name: null
                          id: null
                        warehouse:
                          key: null
                          name: null
                          id: null
                      href: /objects/general-ledger/account-allocation-reverse/21
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update an account allocation reversal
      description: Updates an existing account allocation reversal by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account allocation reversals
      operationId: update-general-ledger-account-allocation-reverse-key
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/general-ledger-account-allocation-reverse'
            examples:
              Update an account allocation reversal:
                value:
                  glAccount:
                    id: '1000'
                  useSourceAccount: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account allocation reversal
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated account allocation reversal:
                  value:
                    'ia::result':
                      key: '21'
                      id: '21'
                      href: /objects/general-ledger/account-allocation-reverse/21
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    general-ledger-account-allocation-reverse:
      $ref: ../models/general-ledger.account-allocation-reverse.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
