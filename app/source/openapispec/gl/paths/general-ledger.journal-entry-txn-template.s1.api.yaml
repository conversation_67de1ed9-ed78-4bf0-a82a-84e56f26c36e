openapi: 3.0.0
info:
  title: general-ledger-journal-entry-txn-template
  description: journal-entry-txn-template API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Journal entry transaction template
    description: Journal entry transaction template provide a simplified method for users to enter common journal transactions, without having to remember accounting details such as the journal to use and the debit and credit accounts.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/general-ledger/journal-entry-txn-template:
    get:
      summary: List
      description: Returns a collection with a key, ID, and link for each journal-entry-txn-template.
      tags:
        - Journal entry transaction template
      operationId: get-objects-journal-entry-txn-template
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of journal-entry-txn-template objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                list of transaction templates:
                  value:
                    'ia::result':
                      - key: '256'
                        id: '256'
                        href: /objects/general-ledger/journal-entry-txn-template/1
                      - key: '132'
                        id: '132'
                        href: /objects/general-ledger/journal-entry-txn-template/2
                      - key: '56'
                        id: '56'
                        href: /objects/general-ledger/journal-entry-txn-template/3
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a/an journal-entry-txn-template
      description: Creates a new journal-entry-txn-template.
      tags:
        - Journal entry transaction template
      operationId: post-objects-journal-entry-txn-template
      requestBody:
        description: Create a Journal entry transaction template with accounting details such as the journal to use and the debit and credit accounts.
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-journal-entry-txn-template'
                - $ref: '#/components/schemas/general-ledger-journal-entry-txn-templateRequiredProperties'
            examples:
              Create a transaction template:
                value:
                  id: 'Monthly payroll'
                  status: 'active'
                  journal:
                    id: 'BAJ'
                  offsetGLAccount:
                    id: '1000.99'
                  description: 'template for baj journal'
                  lines:
                    - glAccount:
                        id: '1000.US'
                      accountIdentifier: 'us accnt'
                      dimensions:
                        department:
                          id: '3'
                        location:
                          id: '1'
                      departmentSettings:
                        isDepartmentEditable: 'F'
                        hideDepartment: 'F'
                      locationSettings:
                        isLocationEditable: 'F'
                        hideLocation: 'F'
                      dimensionSettings:
                        isDimensionEditable: 'F'
                        hideDimension: 'F'
                      transactionType: debit
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New journal-entry-txn-template
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Sample POST response:
                  value:
                    'ia::result':
                      key: '24'
                      id: 'Monthly payroll'
                      href: '/objects/general-ledger/journal-entry-txn-template/24'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
                
        '400':
          $ref: '#/components/responses/400error'
  '/objects/general-ledger/journal-entry-txn-template/{key}':
    parameters:
      - name: key
        description: system-assigned unique key for the journal-entry-txn-template.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a/an journal-entry-txn-template
      description: Returns detailed information for a particular journal-entry-txn-template.
      tags:
        - Journal entry transaction template
      operationId: get-objects-journal-entry-txn-template-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the journal-entry-txn-template
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/general-ledger-journal-entry-txn-template'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the journal-entry-txn-template:
                  value:
                    'ia::result':
                      id: 'Monthly payroll'
                      key: '1'
                      status: active
                      journal:
                        id: 'AP ADJ'
                        name: 'AP Adjustment Journal'
                        key: '18'
                        isBillable: false
                        isAdjustment: false
                        href: '/objects/general-ledger/journal/18'
                      offsetGLAccount:
                        id: '1001'
                        name: 'Citi Bank'
                        key: '5'
                        href: '/objects/general-ledger/account/5'
                      description: 'Transaction Template for AP Adjustment'
                      version: '1'
                      audit:
                        createdDateTime: '2024-10-08T13:29:18Z'
                        modifiedDateTime: '2024-10-08T13:29:18Z'
                        createdBy: '34'
                        modifiedBy: '1'
                        createdByUser:
                          key: '34'
                          id: 'Admin'
                          href: /objects/company-config/user/34
                        modifiedByUser:
                          key: '1'
                          id: 'Aman'
                          href: /objects/company-config/user/1
                      lines:
                        - id: '1'
                          key: '1'
                          status: active
                          txnTemplate:
                            id: 'Monthly payroll'
                            key: '1'
                            href: '/objects/general-ledger/journal-entry-txn-template/1'
                          glAccount:
                            id: '1003'
                            name: 'Chase'
                            key: '12'
                            href: '/objects/general-ledger/account/12'
                          accountIdentifier: 'chase bank'
                          dimensions:
                            department:
                              id: null
                              name: null
                              key: null
                            location:
                              id: '1'
                              name: United States of America
                              key: '1'
                              href: '/objects/company-config/location/1'
                            project:
                              id: null
                              name: null
                              key: null
                            customer:
                              id: null
                              name: null
                              key: null
                            vendor:
                              id: null
                              name: null
                              key: null
                            employee:
                              id: null
                              name: null
                              key: null
                            item:
                              id: null
                              name: null
                              key: null
                            class:
                              id: null
                              name: null
                              key: null
                            warehouse:
                              id: null
                              name: null
                              key: null
                            task:
                              id: null
                              name: null
                              key: null
                            nsp::udd1:
                              key: null
                            nsp::shildim:
                              key: null
                            nsp::newdim:
                              key: null
                            nsp::justcheck:
                              key: null
                            nsp::general:
                              key: null
                            nsp::new_udd:
                              key: null
                          departmentSettings:
                            isDepartmentEditable: 'F'
                            hideDepartment: 'F'
                          locationSettings:
                            isLocationEditable: 'F'
                            hideLocation: 'F'
                          dimensionSettings:
                            isDimensionEditable: 'F'
                            hideDimension: 'F'
                          transactionType: debit
                          isBillable: false
                          nsp::GLDIMUDD:
                          href: '/objects/general-ledger/journal-entry-txn-template-line/1'
                        - id: '2'
                          key: '2'
                          status: active
                          txnTemplate:
                            id: 'Monthly payroll'
                            key: '1'
                            href: '/objects/general-ledger/journal-entry-txn-template/1'
                          glAccount:
                            id: '2050.01'
                            name: 'Labor Cost'
                            key: '114'
                            href: '/objects/general-ledger/account/114'
                          accountIdentifier: 'labor cost'
                          dimensions:
                            department:
                              id: null
                              name: null
                              key: null
                            location:
                              id: '1'
                              name: 'United States of America'
                              key: '1'
                              href: '/objects/company-config/location/1'
                            project:
                              id: null
                              name: null
                              key: null
                            customer:
                              id: null
                              name: null
                              key: null
                            vendor:
                              id: null
                              name: null
                              key: null
                            employee:
                              id: null
                              name: null
                              key: null
                            item:
                              id: null
                              name: null
                              key: null
                            class:
                              id: null
                              name: null
                              key: null
                            warehouse:
                              id: null
                              name: null
                              key: null
                            task:
                              id: null
                              name: null
                              key: null
                            nsp::udd1:
                              key: null
                            nsp::shildim:
                              key: null
                            nsp::newdim:
                              key: null
                            nsp::justcheck:
                              key: null
                            nsp::general:
                              key: null
                            nsp::new_udd:
                              key: null
                          departmentSettings:
                            isDepartmentEditable: 'F'
                            hideDepartment: 'F'
                          locationSettings:
                            isLocationEditable: 'F'
                            hideLocation: 'F'
                          dimensionSettings:
                            isDimensionEditable: 'F'
                            hideDimension: 'F'
                          transactionType: credit
                          isBillable: false
                          nsp::GLDIMUDD:
                          href: '/objects/general-ledger/journal-entry-txn-template-line/2'
                      href: '/objects/general-ledger/journal-entry-txn-template/1'
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a/an journal-entry-txn-template
      description: Updates an existing journal-entry-txn-template by setting field values. Any fields not provided remain unchanged.
      tags:
        - Journal entry transaction template
      operationId: patch-objects-journal-entry-txn-template-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-journal-entry-txn-template'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates  journal-entry-txn-template:
                value:
                  journal:
                    id: 'CCJ'
                  description: 'update template to ccj journal' 
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated journal-entry-txn-template
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated journal-entry-txn-template:
                  value:
                    'ia::result':
                      key: '24'
                      id: '24'
                      href: '/objects/general-ledger/journal-entry-txn-template/24'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0   
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a/an journal-entry-txn-template
      description: Deletes a/an journal-entry-txn-template.
      tags:
        - Journal entry transaction template
      operationId: delete-objects-journal-entry-txn-template-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    general-ledger-journal-entry-txn-template:
      $ref: ../models/general-ledger.journal-entry-txn-template.s1.schema.yaml
    general-ledger-journal-entry-txn-templateRequiredProperties:
      type: object
      required:
        - id
        - description
        - journal
        - lines
      properties:
        lines:
          type: array
          items:
            required:
              - glAccount
              - accountIdentifier
              - transactionType
              - departmentSettings
              - locationSettings
              - dimensionSettings
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml