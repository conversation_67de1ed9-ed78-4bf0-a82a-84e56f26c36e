openapi: 3.0.0
info:
  title: general-ledger-account-group
  description: general-ledger.account-group API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Account groups
    description: |-
      All financial reports include account groups, these indicate which accounting data to include in your  reports. For example, a report might include the "Accounts Payable" account group for a specific customer or in a specific location. 
      
      Account groups organize accounts into re-usable structures, creating the groupings and amounts that you want to see on reports. You can create as many groups as you need, from large hierarchical structures like "Net Income" to flat account groups such as "Cash and Cash Equivalents," and then use them across many different reports, graphs, and dashboard performance cards.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/general-ledger/account-group:
    get:
      summary: List account groups
      description: |-
        Returns a collection with a key, ID, and link for each account group. This operation is mostly for use in testing; use the query service to find account groups that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Account groups
      operationId: list-general-ledger-account-group
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account group objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List account groups:
                  value:
                    'ia::result':
                      - key: '647'
                        id: Liquidity Ratios
                        href: /objects/general-ledger/account-group/647
                      - key: '621'
                        id: Cash and ST Investments
                        href: /objects/general-ledger/account-group/621
                      - key: '629'
                        id: Days in Month
                        href: /objects/general-ledger/account-group/629
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create an account group
      description: Creates a new account group.
      tags:
        - Account groups
      operationId: create-general-ledger-account-group
      requestBody:
        description: Account group to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-group'
                - $ref: '#/components/schemas/general-ledger-account-groupRequiredProperties'
            examples:
              Create an account group:
                value:
                  id: AGG Group
                  normalBalance: debit
                  calculationMethod: period
                  includeChildAmount: false
                  title: ReportFilter
                  displayTotalLineAs: Total ReportFilter
                  reportFilters:
                    debitOrCredit: both
                    department: noFilter
                    location: noFilter
                    vendor: noFilter
                    customer: noFilter
                    project: noFilter
                    employee: noFilter
                    item: noFilter
                    class: noFilter
                    task: noFilter
                    warehouse: noFilter
                    asset: noFilter
                    affiliateEntity: noFilter
                  groupType: accounts
                  isKPI: false
                  manager: AG
                  accountGroupPurpose:
                    id: '21'
                  accountRanges:
                    - sortOrder: 0
                      rangeFrom: '1000'
                      rangeTo: '2000'
                    - sortOrder: 1
                      rangeFrom: '3000'
                      rangeTo: '3000'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New account group
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new account group:
                  value:
                    'ia::result':
                      key: '719'
                      id: AGG Group
                      href: /objects/general-ledger/account-group/719
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/general-ledger/account-group/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account group.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an account group
      description: Returns detailed information for a specified account group.
      tags:
        - Account groups
      operationId: get-general-ledger-account-group-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account group
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/general-ledger-account-group'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get an account group:
                  value:
                    'ia::result':
                      # # key: '713'
                      id: AG-Computation-01
                      normalBalance: debit
                      calculationMethod: period
                      includeChildAmount: false
                      title: AG-Computation-01
                      displayTotalLineAs: Total AG-Computation-01
                      dimensions:
                        location:
                          key: null
                          name: null
                          id: null
                        department:
                          key: '9'
                          name: Partner Sales
                          id: PRS
                          href: /objects/company-config/department/9
                        departmentGroup:
                          key: null
                          name: null
                          id: null
                        locationGroup:
                          key: '7'
                          name: USA Locations
                          id: USA-GRP
                        project:
                          key: null
                          id: null
                          name: null
                        customer:
                          key: null
                          id: null
                          name: null
                        vendor:
                          key: null
                          id: null
                          name: null
                        employee:
                          key: null
                          id: null
                          name: null
                        item:
                          key: null
                          id: null
                          name: null
                        class:
                          key: null
                          id: null
                          name: null
                        task:
                          key: null
                          id: null
                          name: null
                        warehouse:
                          key: null
                          id: null
                          name: null
                        asset:
                          key: null
                          id: null
                          name: null
                        affiliateEntity:
                          key: null
                          id: null
                          name: null
                        entity:
                          key: null
                          id: null
                          name: null
                      reportFilters:
                        debitOrCredit: both
                        department: specificHierarchy
                        location: specific
                        vendor: noFilter
                        customer: noFilter
                        project: noFilter
                        employee: noFilter
                        item: noFilter
                        class: noFilter
                        contract: null
                        task: noFilter
                        warehouse: noFilter
                        costType: null
                        asset: noFilter
                        affiliateEntity: noFilter
                      groupType: computation
                      isKPI: false
                      audit:
                        createdDateTime: '2024-10-08T13:29:18Z'
                        modifiedDateTime: '2024-10-08T13:29:18Z'
                        createdBy: '34'
                        modifiedBy: '1'
                        createdByUser:
                          key: '34'
                          id: 'Admin'
                          href: /objects/company-config/user/34
                        modifiedByUser:
                          key: '1'
                          id: 'Aman'
                          href: /objects/company-config/user/1
                      manager: John Doe
                      accountGroupPurpose:
                        key : '10'
                        id: 'P&L'
                        href: /objects/general-ledger/account-group-purpose/10
                      accountRanges: [ ]
                      accountGroupMembers: [ ]
                      accountGroupComputation:
                        - id: '59'
                          key: '59'
                          glAccountGroup:
                            key: '713'
                            id: AG-Computation-01
                            href: /objects/general-ledger/account-group/713
                          formulaLeft:
                            glAccount:
                              key: null
                              id: null
                              name: null
                            glAccountGroup:
                              key: '1902'
                              id: Acc grp with Dept filter
                              href: /objects/general-ledger/account-group/1902
                            constant: '10'
                            asOf: forPeriod
                          operator: add
                          formulaRight:
                            glAccount:
                              key: null
                              id: null
                              name: null
                            glAccountGroup:
                              key: '1044'
                              id: 990IX Advertising and Promotion
                              href: /objects/general-ledger/account-group/1044
                            constant: '10'
                            asOf: forPeriod  
                          numberOfDecimalPlaces: 2
                          displayAs: number
                          unit: AK
                          unitPlacement: left
                          audit:
                            createdDateTime: '2024-06-17T05:00:51Z'
                            modifiedDateTime: '2024-06-17T05:00:51Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          href: /objects/general-ledger/account-group-computation/59  
                      accountGroupCategoryMembers: [ ]
                      statisticalAccountGroupCategoryMembers: [ ]
                      statisticalAccountRanges: [ ]
                      href: /objects/general-ledger/account-group/713
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update an account group
      description: Updates an existing account group by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account groups
      operationId: update-general-ledger-account-group-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-group'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update an account group:
                value:
                  accountRanges:
                    - sortOrder: 0
                      rangeFrom: '1001'
                      rangeTo: '2000'
                    - sortOrder: 1
                      rangeFrom: '2001'
                      rangeTo: '5000'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account group
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated account group:
                  value:
                    'ia::result':
                      key: '719'
                      id: AGG Group
                      href: /objects/general-ledger/account-group/719
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete an account group
      description: Deletes an account group.
      tags:
        - Account groups
      operationId: delete-general-ledger-account-group-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    general-ledger-account-group:
      $ref: ../models/general-ledger.account-group.s1.schema.yaml
    general-ledger-account-groupRequiredProperties:
      type: object
      required:
        - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
