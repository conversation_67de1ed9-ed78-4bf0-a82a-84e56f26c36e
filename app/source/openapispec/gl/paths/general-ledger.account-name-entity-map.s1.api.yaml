openapi: 3.0.0
info:
  title: GL Account name entity map
  description: GL Account name entity map API
  version: '1.0'
  contact:
    name: <PERSON>tra Ghosh
    email: <EMAIL>
tags:
  - name: GL Account name entity maps
    description: Set up account titles map at entity level.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/general-ledger/account-name-entity-map:
    get:
      summary: List GL account name entity maps
      description: 'Returns a collection with a key, ID, and link for each account title entity map.'
      tags:
        - GL Account name entity maps
      operationId: list-account-title-by-location
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List GL account name by entity maps
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of GL Account name entity maps:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: /objects/general-ledger/account-name-entity-map/1
                      - key: '2'
                        id: '2'
                        href: /objects/general-ledger/account-name-entity-map/2
                      - key: '5'
                        id: '5'
                        href: /objects/general-ledger/account-name-entity-map/5
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a GL account name entity map
      description: Creates a new GL account name entity map.
      tags:
        - GL Account name entity maps
      operationId: create-account-title-by-location
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-name-entity-map'
                - type: object
                  properties:
                    id:
                      readOnly: true
                - $ref: '#/components/schemas/general-ledger-account-name-entity-mapRequiredProperties'
            examples:
              Creates a new GL account name entity map request:
                value:
                  name: Bank of Tanjore (West India)
                  location:
                    id: Western Region
                  glAccount:
                    id: '1410.01'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New GL account name entity map
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New GL account name entity map response:
                  value:
                    'ia::result':
                      key: '5'
                      id: '5'
                      href: /objects/general-ledger/account-name-entity-map/5
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
  /objects/general-ledger/account-name-entity-map/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account title entity map.
        in: path
        required: true
        schema:
          type: string
          example: '2'
    get:
      summary: Get a GL account name entity map
      description: Returns detailed information for a particular GL account name entity map.
      tags:
        - GL Account name entity maps
      operationId: get-account-title-by-location-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account title entity map
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/general-ledger-account-name-entity-map'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the Account title entity map:
                  value:
                    'ia::result':
                      key: '2'
                      id: '2'
                      name: Bank of Tanjore (West India)
                      glAccount:
                        id: '1410.01'
                        key: '21'
                        name: 'Corporation Bank of India'
                        href: /objects/general-ledger/account/21
                      location:
                        id: Western Region
                        key: '2'
                        name: Corporate (Central) - Inter Entity Receivable
                        href: /objects/company-config/location/2
                      href: /objects/general-ledger/account-name-entity-map/2
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a GL account name entity map
      description: Updates an existing GL account name entity map by setting field values. Any fields not provided remain unchanged.
      tags:
        - GL Account name entity maps
      operationId: update-account-title-by-location-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-name-entity-map'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates a GL account name entity map:
                value:
                  name: Bank of Tanjore (West India)
                  location:
                    id: Western Region
                  glAccount:
                    id: '1003'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated GL account name entity map
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated GL account name entity map:
                  value:
                    'ia::result':
                      key: '5'
                      id: '5'
                      href: /objects/general-ledger/account-name-entity-map/5
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a GL account name entity map
      description: Deletes a GL account name entity map.
      tags:
        - GL Account name entity maps
      operationId: delete-account-title-by-location-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    general-ledger-account-name-entity-map:
      $ref: ../models/general-ledger.account-name-entity-map.s1.schema.yaml
    general-ledger-account-name-entity-mapRequiredProperties:
      type: object
      required:
        - name
        - glAccount
        - location
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
