title: general-ledger-account-category
x-mappedTo: glacctcategory
type: object
description: Account category
properties:
  key:
    type: string
    description: System-assigned key for the account category.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '134'
  id:
    type: string
    description: Unique identifier for the account category.
    readOnly: true
    x-mappedTo: NAME
    example: Cash and Cash Equivalents
  href:
    type: string
    description: URL endpoint for the account category.
    readOnly: true
    example: /objects/general-ledger/account-category/23
  isStatistical:
    type: boolean
    description: Indicates whether the account category is used for tracking statistical data such as operational metrics.
    x-mappedTo: STATISTICAL
    x-mappedToType: string
    example: false
  accountType:
    type: string
    description: Specifies the account type associated with the account category.
    example: asset
    x-mappedTo: ACCOUNTTYPE
    enum:
      - 'asset'
      - 'liability'
      - 'equity'
      - 'income'
      - 'costOfRevenue'
      - 'expense'
      - 'other'
    x-mappedToValues:
      - '1A'
      - '2L'
      - '3E'
      - '4I'
      - '5C'
      - '6X'
      - '9O'
  normalBalance:
    type: string
    description: Normal balance.
    x-mappedTo: NORMAL_BALANCE
    example: debit
    enum:
      - 'debit'
      - 'credit'
    x-mappedToValues:
      - 'debit'
      - 'credit'