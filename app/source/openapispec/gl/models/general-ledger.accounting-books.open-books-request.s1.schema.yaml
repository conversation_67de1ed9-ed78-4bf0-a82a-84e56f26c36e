title: open-books
x-mappedTo: openbooks
type: object
description: Opens company books
properties:
  entity:
    type: string
    description: Specify a single entity, an entity group, or leave blank for all entities
    x-mappedTo: ENTITYID
    example: 'Central Region'
  reportingPeriodKey:
    type: string
    description: Opens books from the specified period key
    x-mappedTo: PERIODRECNO
    example: '65'
  openAPBook:
    type: boolean
    description: Open accounts payable book
    x-mappedTo: OPENAP
    x-mutable: false
    example: false
    default: false
  openARBook:
    type: boolean
    description: Open accounts receivable book
    x-mappedTo: OPENAR
    x-mutable: false
    example: false
    default: false
  openCMBook:
    type: boolean
    description: Open cash management book
    x-mappedTo: OPENCM
    x-mutable: false
    example: false
    default: false
  openEEBook:
    type: boolean
    description: Open time & expenses book
    x-mappedTo: OPENEE
    x-mutable: false
    example: false
    default: false