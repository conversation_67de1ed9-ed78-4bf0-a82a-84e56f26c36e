title: order-entry-document-entry-detail
x-mappedTo: docentrydetail
type: object
description: Detail information for MEA allocations in Order Entry (Legacy).
properties:
  key:
    type: string
    description: System-assigned key for the document-entry-detail.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Order Entry transaction fair value detail ID.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the document-entry-detail.
    readOnly: true
    example: /objects/document-entry-detail/23
  itemComponentKey:
    type: string
    description: Item ID.
    x-mappedTo: ITEMCOMPONENTKEY
    example: 'Bundle Discount'
  kitId:
    type: string
    description: Kit ID.
    x-mappedTo: KITID
    example: '2'
    nullable: true
  bundleId:
    type: string
    description: Bundle ID.
    x-mappedTo: BUNDLEID
    example: '2'
    nullable: true
  lineNo:
    type: string
    description: Line no..
    x-mappedTo: LINENO
    example: '1'
    nullable: true
  vsoeExtendedPrice:
    type: string
    format: decimal-precision-10
    description: Extended fair value price.
    x-mappedTo: VSOEEXTPRICE
    example: '196.33'
    nullable: true
  vsoePercent:
    type: string
    format: decimal-precision-10
    description: Reallocation percent.
    x-mappedTo: VSOEPERCENT
    example: '25.76238715'
    nullable: true
  invoicePrice:
    type: string
    format: decimal-precision-10
    description: Extended price.
    x-mappedTo: INVOICEPRICE
    example: '-2.9'
  vsoeAllocation:
    type: string
    format: decimal-precision-10
    description: Fair value allocation
    x-mappedTo: VSOEALLOCATION
    example: '201.1'
  txnVsoeAllocation:
    type: string
    format: decimal-precision-10
    description: MEA allocation (txn).
    x-mappedTo: TRX_VSOEALLOCATION
    example: '201.1'
  deliveryStatus:
    type: string
    description: Delivery status.
    x-mappedTo: DLVR_STATUS
    example: delivered
    enum:
      - null
      - delivered
      - undelivered
    x-mappedToValues:
      - ''
      - 'Delivered'
      - 'Undelivered'
    nullable: true
  deliveryDate:
    type: string
    description: Delivery date.
    x-mappedTo: DLVRDATE
    format: date
    example: '2022-01-08'
    nullable: true
  revenueDeferralStatus:
    type: string
    description: Deferral status.
    x-mappedTo: REVDEFSTATUS
    example: 'deferUntilItemIsDelivered'
    enum:
      - null
      - 'deferUntilItemIsDelivered'
      - 'deferBundleUntilItemIsDelivered'
    x-mappedToValues:
      - ''
      - 'Defer until item is delivered'
      - 'Defer bundle until item is delivered'
    nullable: true
  isRevenueOnHold:
    type: boolean
    x-mappedToType: string
    description: Is revenue on hold.
    x-mappedTo: ISREVONHOLD
    example: true
    nullable: true
  orderEntryDocumentLine:
    type: object
    x-mappedTo: sodocumententry
    x-object: order-entry/document-line
    properties:
      key:
        type: string
        description: System-assigned key for the document-line.
        readOnly: true
        x-mappedTo: DOCENTRYKEY
        example: '23'
      id:
        type: string
        description: ID for the for the Order Entry document line item.
        readOnly: true
        x-mappedTo: DOCENTRYKEY
        example: '23'
      href:
        type: string
        description: Endpoint for the sodocumententry.
        readOnly: true
        example: /objects/order-entry/document-line/23