title: order-entry-txn-definition-subtotal-detail
x-mappedTo: sodocumentparsubtotal
x-ownedBy: order-entry/txn-definition
type: object
description: Provides details about the type of subtotals that are supported for the transaction.
properties:
  key:
    type: string
    description: System-assigned unique key for the subtotal detail object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '41'
  id:
    type: string
    description: System-assigned ID for the subtotal detail object. This value is the same as the key for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '41'
  href:
    type: string
    description: URL endpoint for the subtotal detail object.
    readOnly: true
    example: /objects/order-entry/txn-definition-subtotal-detail/41
  subtotalType:
    type: string
    description: Specify the subtotal type.
    x-mappedTo: DISC_CHARGE
    example: discount
    enum:
      - null
      - discount
      - charge
    x-mappedToValues:
      - ''
      - Discount
      - Charge
    nullable: true
    default: null
  lineNumber:
    type: integer
    description: Line number to which the subtotal applies.
    x-mappedTo: LINENO
    example: 1
    maxLength: 8
  description:
    type: string
    description: Description of the subtotal.
    x-mappedTo: DESCRIPTION
    example: Discount
  valueType:
    type: string
    description: Specify the value type for the subtotal.
    x-mappedTo: AMT_PERC
    example: amount
    enum:
      - null
      - amount
      - percent
    x-mappedToValues:
      - ''
      - Amount
      - Percent
    nullable: true
    default: null
  subtotalValue:
    type: string
    x-mappedTo: VALUE
    format: decimal-precision-2
    description: Provide a default value for the subtotal. If `valueType` is `amount`, provide the amount of the subtotal. For example, enter 250. If `valueType` is `percent`, specify a percentage as a whole number. For example, specify 10 for 10%. Users can override the default subtotal value in the transaction.
    example: '10'
    maxLength: 12
  isApportioned:
    type: boolean
    description: Set to `true` to distribute the discount subtotal proportionally across all line items in the transaction. Set this field to `true` if you are enabling MEA allocations in Order Entry. Leave this field `false` if you are enabling MEA allocations in Contracts.
    x-mappedTo: APPORTIONED
    example: true
    x-mappedToType: string
    default: false
  glAccount:
    type: object
    x-object: general-ledger/account
    description: Specify the GL account to which the subtotal will post.
    properties:
      key:
        type: string
        description: Unique key for the GL account.
        x-mappedTo: GLACCOUNTKEY
        example: '5'
      id:
        type: string
        description: User-assigned number for the GL account.
        x-mappedTo: GLACCTID
        example: '1501.04'
      href:
        type: string
        description: URL endpoint for the GL account. 
        readOnly: true
        example: /objects/general-ledger/account/5
  offsetGLAccount:
    type: object
    x-object: general-ledger/account
    description: Specify the offset GL account to which the subtotal will post.
    properties:
      key:
        type: string
        description: Unique key for the GL account.
        x-mappedTo: GLOFFSETACCOUNTKEY
        example: '16'
      id:
        type: string
        description: Unique ID for the account.
        x-mappedTo: GLOFFSETACCTID
        example: '1501.04'
      href:
        type: string
        description: URL endpoint for the GL account. 
        readOnly: true
        example: /objects/general-ledger/account/16
  txnType:
    type: string
    description: Specify whether to debit or credit the account.
    x-mappedTo: DEBIT_CREDIT
    example: 'debit'
    enum:
      - null
      - debit
      - credit
    x-mappedToValues:
      - ''
      - Debit
      - Credit
    nullable: true
    default: null
  appliedToLineNumber:
    type: integer
    description: |
     Specify the subtotal line number to which the subtotal will apply. Sage Intacct treats the initial subtotal line as line number 0. The first defined subtotal from the transaction definition is line number 1, the second defined subtotal is line number 2, and so on.
     
     The value of `appliedToLineNumber` is implicitly assumed to be 0. Taxes, all other charges, and discounts are computed at the line level. This fields does not apply if `lineLevelSimpleTax` is set to `true` in the owning transaction definition.
    x-mappedTo: BASELINE
    example: 1
    maxLength: 8
  isTax:
    type: boolean
    description: Flags the subtotal as a tax. When the transaction posts, the tax will post separately based on the items in the transaction. Set this field to `true` for tax subtotal lines if your organization uses Simple Tax, Advanced Tax, or Avalara AvaTax. 
    x-mappedTo: ISTAX
    example: true
    x-mappedToType: string
    default: false
  department:
    type: object
    x-mappedTo: department
    x-object: company-config/department
    description: Specify a default department for the subtotal.
    properties:
      href:
        type: string
        description: URL endpoint for the department.
        readOnly: true
        example: /objects/company-config/department/19
      key:
        type: string
        description: Unique key for the department.
        x-mappedTo: DEPTKEY
        example: '19'
      id:
        type: string
        description: Name or other unique ID for the department.
        x-mappedTo: DEPARTMENTID
        example: Sales
  location:
    type: object
    x-mappedTo: LOCATION
    x-object: company-config/location
    description: Specify a default location for the subtotal.
    properties:
      key:
        type: string
        description: System-assigned unique key for the location.
        readOnly: true
        x-mappedTo: LOCATIONKEY
        example: '22'
      id:
        type: string
        description: Unique ID for the location.
        readOnly: true
        x-mappedTo: LOCATIONID
        example: Arizona
      href:
        type: string
        description: URL endpoint for the location.
        readOnly: true
        example: /objects/company-config/location/22
    readOnly: true
  enableAvalaraTax:
    type: boolean
    description: Set to `true` if the subtotal line item applies to AvaTax. This field applies only if your company uses Avalara AvaTax integration. 
    x-mappedTo: ISAVATAX
    example: true
    x-mappedToType: string
    default: false
  entity:
    type: object
    x-mappedTo: locationentity
    x-object: company-config/entity
    description: Sets the context where the transaction is created.
    properties:
      href:
        type: string
        description: URL endpoint for the entity.
        readOnly: true
        example: /objects/company-config/entity/3345
      key:
        type: string
        description: Unique key for the entity. 
        readOnly: true
        x-mappedTo: ENTITYNO
        example: '3345'
      id:
        type: string
        description: Name of the entity.
        x-mappedTo: ENTITY_NAME
        readOnly: true
        example: Australia
    readOnly: true
  order-entry-txn-definition:
    title: order-entry-txn-definition
    type: object
    description: Header level details for the owning transaction definition object.
    x-mappedTo: sodocumentparams
    x-object: order-entry/txn-definition
    properties:
      key:
        type: string
        description: Unique key for the transaction definition.
        x-mappedTo: DOCPARNO
        example: '29'
      id:
        type: string
        description:  Unique ID for the transaction definition.
        x-mappedTo: DOCPARID
        example: Sales Quote
      href:
        type: string
        description: URL endpoint for the transaction definition.
        readOnly: true
        example: /objects/order-entry/txn-definition/29
