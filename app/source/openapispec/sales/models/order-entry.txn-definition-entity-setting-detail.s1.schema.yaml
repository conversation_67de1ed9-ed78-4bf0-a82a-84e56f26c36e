title: order-entry-txn-definition-entity-setting-detail
x-mappedTo: sodocumentparentityprop
x-ownedBy: order-entry/txn-definition
type: object
description: Entity settings for Order Entry transaction definitions. 
properties:
  key:
    type: string
    description: System-assigned unique key for the entity setting detail object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '52'
  id:
    type: string
    description: System-assigned unique ID for the entity setting detail object. This value is the same as the key for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '52'
  href:
    type: string
    description: URL endpoint for the entity setting detail object.
    readOnly: true
    example: /objects/order-entry/txn-definition-entity-setting-detail/52
  enableNumberingSequence:
    type: boolean
    description: Set to `true` to enable numbering sequences to be set for transactions.
    x-mappedTo: ENABLE_SEQNUM
    example: false
    default: false
    x-mappedToType: string
  preserveNumberingSequence:
    type: boolean
    description: Set to `true` to ensure that numbers in a sequence are never skipped. Enabling this option can affect performance when a large volume of transactions is entered at the same time.
    x-mappedTo: PRESERVE_SEQNUM
    example: false
    default: false
    x-mappedToType: string
  canInheritSourceDocumentNumber:
    type: boolean
    description: Set to `true` if transactions should inherit source document numbers. For example, a transaction could inherit a sales invoice number.
    x-mappedTo: INHERIT_SOURCE_DOCNO
    example: false
    default: false
    x-mappedToType: string
  documentTemplate:
    type: object
    description: Specify a printed document template to use as the default for printed output (PDFs) for the corresponding entity. If you do not specify a value for this field, the Order Entry transaction definition document template is used.
    title: Document Template
    properties:
      key:
        type: string
        description: Unique key for the document template.
        x-mappedTo: XSLTEMPLATE
        example: '65'
        maxLength: 8
      id:
        type: string
        description: The name or other ID for the document template.
        x-mappedTo: XSLTEMPLATEDESCRIPTION
        example: Sales Order
        maxLength: 100
  enableCreateTransactionRule:
    type: boolean
    description: The value for this field is set by Sage Intacct and is derived from the value specified for the `multiEntityRuleForTransaction` field in the owning transaction definition.  
    x-mappedTo: ENTITY_CREATION_RULE
    example: false
    default: false
    x-mappedToType: string
  subtotalTemplate:
    type: object
    description: If `enableSubtotals` is set to `true`in the owning transaction definition, you can specify the subtotal template to use to calculate subtotals.
    x-mappedTo: sosubtotaltemplate
    x-object: order-entry/subtotal-template
    properties:
      href:
        type: string
        description: URL endpoint for the subtotal template.
        readOnly: true
        example: /objects/order-entry/subtotal-template/23
      key:
        type: string
        description: Unique key for the subtotal template.
        x-mappedTo: SUBTOTALTEMPLATEKEY
        example: '23'
      id:
        type: string
        description: Name or other ID for the subtotal template.
        x-mappedTo: SUBTOTALTEMPLATE
        example: Subtotal template
  showExpandedTaxDetail:
    type: boolean
    description: Set to `true` to show expanded tax details in the transaction user interface and in printed output. This field applies only to companies that use Avalara AvaTax or Sage Intacct Advanced Tax.
    x-mappedTo: SHOWEXPANDEDTOTALS
    example: false
    default: false
    x-mappedToType: string
  enableOverrideTax:
    type: boolean
    description: Indicates whether users can override whether a line item is taxable.
    x-mappedTo: ENABLEOVERRIDETAX
    example: false
    default: false
    x-mappedToType: string
  enableLineLevelSimpleTax:
    type: boolean
    description: Set to `true` to allow the tax rate to be overridden at the line level when using Simple Tax.
    x-mappedTo: LINELEVELSIMPLETAX
    example: false
    default: false
    x-mappedToType: string
  entity:
    type: object
    x-object: company-config/entity
    properties:
      href:
        type: string
        description: URL endpoint for the entity.
        readOnly: true
        example: /objects/company-config/entity/41
      key:
        type: string
        description: Unique key for the entity.
        x-mappedTo: ENTITYNO
        example: '41'
      id:
        type: string
        description: Unique ID for the entity. The ID cannot be changed after the entity has been created and the maximum number of characters for the ID is 20.
        x-mappedTo: ENTITY_LOCATION_NO
        maxLength: 20
        example: Lyon
  documentSequence:
    type: object
    description: Specify the document numbering sequence to use to automatically number transactions for the corresponding entity. Omit a numbering sequence here if you want to use the numbering sequence defined for the owning transaction definition, or if you want converted transactions to inherit the source document number.
    x-mappedTo: seqnum
    x-object: company-config/document-sequence
    properties:
      key:
        type: string
        description: Unique key for the document numbering sequence.
        x-mappedTo: SEQNUMKEY
        example: "10"
      href:
        type: string
        description: URL endpoint for the document numbering sequence.
        readOnly: true
        example: /objects/company-config/document-sequence/10
      id:
        type: string
        description: ID of the document numbering sequence.
        x-mappedTo: SEQUENCE
        example: Adjustment Decrease
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  order-entry-txn-definition:
    title: order-entry-txn-definition
    type: object
    description: Header level details for the owning transaction definition object.
    x-mappedTo: sodocumentparams
    x-object: order-entry/txn-definition
    properties:
      key:
        type: string
        description: System-assigned key for the order-entry-txn-definition.
        x-mappedTo: DOCPARNO
        example: '55'
      id:
        type: string
        description: System-assigned ID for the order-entry-txn-definition.
        x-mappedTo: DOCPARID
        example: Sales Quote
      href:
        type: string
        description: URL for the order entry transaction definition.
        readOnly: true
        example: /objects/order-entry/txn-definition/55