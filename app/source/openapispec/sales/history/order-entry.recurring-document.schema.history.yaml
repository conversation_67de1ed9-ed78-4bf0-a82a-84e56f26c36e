ia::definition:
  methodPermissions:
    GET:
      - so/lists/sorecurdocument/view
    POST:
      - so/activities/sorecurdocument/create
      - so/lists/sorecurdocument/create
    PATCH:
      - so/lists/sorecurdocument/edit
    DELETE:
      - so/lists/sorecurdocument/delete
s1:
  hash: '0'
  type: rootDocTypeObject
  systemViews:
    systemfw1:
      revision: s1
      hash: '0'
    systemfw2:
      revision: s1
      hash: '0'
  uiMetadataHash: '0'
