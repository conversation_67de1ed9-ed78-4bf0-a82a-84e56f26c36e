key: order-entry/customer-gl-group::systemOrder-entry/customer-gl-groupFW2
id: systemOrder-entry/customer-gl-groupFW2
object: order-entry/customer-gl-group
name: IA.RECENTLY_MODIFIED
description: IA.SPECIFIES_RECENTLY_MODIFIED
query:
  object: order-entry/customer-gl-group
  fields:
    - id
    - status
    - audit.createdDateTime
    - audit.modifiedDateTime
    - audit.modifiedBy
  filters:
    - $eq:
        status: active
  orderBy:
    - audit.modifiedDateTime: desc
metadata:
  frozenColumnsCount: 2
  columns:
    - id: "id"
      format: "clip"
      size: 40
    - id: "status"
      format: "clip"
      size: 40
    - id: "audit.createdDateTime"
      format: "clip"
      size: 40
    - id: "audit.modifiedDateTime"
      format: "clip"
      size: 40
    - id: "audit.modifiedBy"
      format: "clip"
      size: 40
contexts:
  - __default