key: order-entry/document::systemOrderEntryDocumentFW1
id: systemOrderEntryDocumentFW1
object: order-entry/document
name: IA.ALL
description: IA.ALL_ACTIVE_TRANSACTIONS
query:
  object: order-entry/document
  fields:
    - documentNumber
    - documentType
    - referenceNumber
    - customer.name
    - txnDate
    - state
    - status
  filters:
    - $eq:
        status: active
  orderBy:
    - txnDate: desc
contexts:
  - __default