openapi: 3.0.0
info:
  title: order-entry-revenue-recognition-schedule
  description: order-entry.revenue-recognition-schedule API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: naras<PERSON><PERSON>.<EMAIL>
tags:
  - name: Revenue recognition schedules
    description: Revenue recognition schedules description -- explain what it's for and how it's used.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/order-entry/revenue-recognition-schedule:
    get:
      summary: List revenue recognition schedules
      description: Returns a collection with a key, ID, and link for each revenue recognition schedules.
      tags:
        - Revenue recognition schedules
      operationId: list-order-entry-revenue-recognition-schedule
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of revenue-recognition-schedule objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of revenue recognition schedules:
                  value:
                    ia::result:
                      - key: '7'
                        id: '7'
                        href: "/objects/order-entry/revenue-recognition-schedule/7"
                      - key: '8'
                        id: '8'
                        href: "/objects/order-entry/revenue-recognition-schedule/8"
                      - key: '18'
                        id: '18'
                        href: "/objects/order-entry/revenue-recognition-schedule/18"
                      - key: '19'
                        id: '19'
                        href: "/objects/order-entry/revenue-recognition-schedule/19"
                    'ia::meta':
                      totalCount: 4
        '400':
          $ref: '#/components/responses/400error'
  /objects/order-entry/revenue-recognition-schedule/{key}:
    parameters:
      - name: key
        description: system-assigned unique key for the revenue recognition schedules.
        in: path
        required: true
        schema:
          type: string
        example: '1287'
    get:
      summary: Get a revenue recognition schedules
      description: Returns detailed information for a particular revenue recognition schedules.
      tags:
        - Revenue recognition schedules
      operationId: get-order-entry-revenue-recognition-schedule-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the revenue-recognition-schedule
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/order-entry-revenue-recognition-schedule'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the revenue recognition schedules:
                  value:
                    'ia::result':
                      - id: '13'
                        key: '13'
                        revenueRecognitionTemplate:
                          key: '8'
                          id: '12M_AutoPost_CatchUp_R'
                          postingMethod: 'automatic'
                          recognitionMethod: 'straightLine'
                          milestoneSource: 'manual'
                          calculation:
                            source: 'Project'
                            basedOn: 'Estimated Hours'
                          schedulePeriod: 'monthly'
                          totalPeriods: 12
                          postingDay: 1
                          href: /objects/accounts-receivable/revenue-recognition-template/8
                        status: 'onHold'
                        invoice:
                          invoiceNumber: null
                          invoiceDate: null
                          id: null
                          key: null
                        orderEntryDocument:
                          key: '4'
                          id: 'SUB Invoice RevRec-SUBINV#0103#doc'
                          createdDate: '2005-01-01'
                          href: '/objects/order-entry/document/4'
                        description: 'SFDC support_R'
                        forScheduleRange:
                          key: null
                          startDate: null
                          endDate: null
                        orderEntryDocumentEntry:
                          id: '13'
                          key: '13'
                          deliveryDate: '2005-01-01'
                          deliveryStatus: 'undelivered'
                          href: '/objects/order-entry/document-entry-detail/13'
                        orderEntryDocumentLine:
                          id: '13'
                          key: '13'
                          itemId: 'SFDC support_R'
                          itemDescription: 'SFDC support_R'
                          lineNumber: 4
                          txnPrice: '212.0000000000'
                          startDate: '2010-02-05'
                          endDate: '2011-01-05'
                          href: '/objects/order-entry/document-line/13'
                        revenueRecognitionCategory:
                          key: '2'
                          id: 'Delivery Commitment'
                          href: '/objects/inventory-control/revenue-recognition-category/2'
                        memo: 'product or service has not been delivered'
                        daysInLastPeriod: 31
                        project:
                          key: null
                          id: null
                          name: null
                        task:
                          key: null
                          name: null
                          id: null
                        customer:
                          id: 'vsoe_cust_005_R'
                          key: '44'
                          href: '/objects/accounts-receivable/customer/44'
                        audit:
                          createdDateTime: '2024-02-29T12:51:22Z'
                          modifiedDateTime: '2024-02-29T12:51:22Z'
                          createdBy: '1'
                          modifiedBy: '1'
                        lines:
                          - id: '145'
                            key: '145'
                            revenueRecognitionSchedule:
                              id: '13'
                              key: '13'
                              status: 'onHold'
                              href: '/objects/order-entry/revenue-recognition-schedule/13'
                            status: 'onHold'
                            postingDate: '2010-03-01'
                            revenueGLAccount:
                              key: '4000--Sales'
                              id: '4000'
                              name: 'Sales'
                              href: '/objects/general-ledger/account/4000--Sales'
                            deferredRevenueGLAccount:
                              id: '4000'
                              key: '157'
                              href: '/objects/general-ledger/account/157'
                            glJournal:
                              key: '16'
                              id: 'RRJ'
                              href: '/objects/general-ledger/journal/16'
                            baseAmount: '17.42'
                            journalEntry:
                              id: null
                              key: null
                            txnAmount: '17.42'
                            postedAmount: '0.00'
                            currency:
                              baseCurrency: 'USD'
                              exchangeRateDate: '2005-01-01'
                              exchangeRateTypeID: null
                              exchangeRate: 1
                            description: null
                            isUnscheduled: false
                            budgetedHours: null
                            estimatedHours: null
                            plannedHours: null
                            approvedHours: null
                            percentComplete: null
                            percentRecognized: null
                            observedPercentComplete: null
                            budgetedCost: null
                            totalCost: null
                            audit:
                              createdDateTime: '2024-02-29T00:00:00Z'
                              modifiedDateTime: '2024-02-29T00:00:00Z'
                              createdBy: '1'
                              modifiedBy: '1'
                            dimensions:
                              nsp::contract_x:
                                key: null
                            href: '/objects/inventory-control/revenue-recognition-schedule-line/145'
                          - id: '146'
                            key: '146'
                            revenueRecognitionSchedule:
                              id: '13'
                              key: '13'
                              status: 'onHold'
                              href: '/objects/order-entry/revenue-recognition-schedule/13'
                            status: 'onHold'
                            postingDate: '2010-04-01'
                            revenueGLAccount:
                              key: '4000--Sales'
                              id: '4000'
                              name: 'Sales'
                              href: '/objects/general-ledger/account/4000--Sales'
                            deferredRevenueGLAccount:
                              id: '4000'
                              key: '157'
                              href: '/objects/general-ledger/account/157'
                            glJournal:
                              key: '16'
                              id: 'RRJ'
                              href: '/objects/general-ledger/journal/16'
                            baseAmount: '17.41'
                            journalEntry:
                              id: null
                              key: null
                            txnAmount: '17.41'
                            postedAmount: '0.00'
                            currency:
                              baseCurrency: 'USD'
                              exchangeRateDate: '2005-01-01'
                              exchangeRateTypeID: null
                              exchangeRate: 1
                            description: null
                            isUnscheduled: false
                            budgetedHours: null
                            estimatedHours: null
                            plannedHours: null
                            approvedHours: null
                            percentComplete: null
                            percentRecognized: null
                            observedPercentComplete: null
                            budgetedCost: null
                            totalCost: null
                            audit:
                              createdDateTime: '2024-02-29T00:00:00Z'
                              modifiedDateTime: '2024-02-29T00:00:00Z'
                              createdBy: '1'
                              modifiedBy: '1'
                            dimensions:
                              nsp::contract_x:
                                key: null
                            href: '/objects/inventory-control/revenue-recognition-schedule-line/146'
                        txnAmount: '208.93'
                        href: '/objects/order-entry/revenue-recognition-schedule/13'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a/an revenue recognition schedules
      description: Updates an existing revenue recognition schedules by setting field values. Any fields not provided remain unchanged.
      tags:
        - Revenue recognition schedules
      operationId: update-order-entry-revenue-recognition-schedule-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/order-entry-revenue-recognition-schedule'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              add a/an revenue recognition schedules:
                value:
                  lines:
                    - postingDate: '2022-05-25'
              update a revenue recognition schedule:
                value:
                  lines:
                    - key: '12'
                      postingDate: '2022-05-25'
              update multiple revenue recognition schedules:
                value:
                  lines:
                    - key: '12'
                      postingDate: '2022-05-25'
                    - key: '13'
                      postingDate: '2022-05-26'
              Delete a revenue schedule line:
                value:
                  lines:
                    - ia::operation: delete
                      key: '12'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated revenue-recognition-schedule
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated revenue recognition schedules:
                  value:
                    'ia::result':
                      id: '7'
                      key: '7'
                      href: /objects/order-entry/revenue-recognition-schedule/7
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    order-entry-revenue-recognition-schedule:
      $ref: ../models/order-entry.revenue-recognition-schedule.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
