uiLabel: IA.REVENUE_TXN_ENTRY
fields:
  id:
    uiType: sequence
    uiLabel: IA.RECORD_NUMBER
  documentType:
    uiType: text
    uiLabel: IA.TRANSACTION_DEFINITION
  lineNumber:
    uiType: integer
    uiLabel: IA.ENTRY_NUMBER
  memo:
    uiType: multitext
    uiLabel: IA.MEMO
  priceInTxnCurrency:
    uiType: currency
    uiLabel: IA.PRICE
  quantity:
    uiType: decimal
    uiLabel: IA.QUANTITY
  unit:
    uiType: text
    uiLabel: IA.UNIT
  extendedPriceInTxnCurrency:
    uiType: currency
    uiLabel: IA.EXTENDED_PRICE
  revenueRecognitionStartDate:
    uiType: date
    uiLabel: IA.START_DATE
  revenueRecognitionEndDate:
    uiType: date
    uiLabel: IA.END_DATE
groups:
  documentHeader:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.DOCUMENT_ID
      key:
        uiType: integer
        uiLabel: IA.TRANSACTION_RECORD_NUMBER
  document:
    fields:
      state:
        uiType: enum
        uiLabel: IA.STATE
        enumsLabels:
          - label: IA.DRAFT
            value: draft
      txnDate:
        uiType: date
        uiLabel: IA.DATE
      postingDate:
        uiType: date
        uiLabel: IA.GL_POSTING_DATE
      dueDate:
        uiType: date
        uiLabel: IA.DATE_DUE
      memo:
        uiType: multitext
        uiLabel: IA.MESSAGE
      baseCurrency:
        uiType: text
        uiLabel: IA.BASE_CURRENCY
      txnCurrency:
        uiType: text
        uiLabel: IA.TXN_CURRENCY
      documentNumber:
        uiType: text
        uiLabel: IA.DOCUMENT_NUMBER
    groups:
      customer:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.CUSTOMER_ID
          name:
            uiType: text
            uiLabel: IA.CUSTOMER_NAME
      billTo:
        fields:
          id:
            uiType: text
            uiLabel: IA.BILL_TO_CONTACT_NAME
      shipTo:
        fields:
          id:
            uiType: text
            uiLabel: IA.SHIP_TO_CONTACT_NAME
      paymentTerm:
        fields:
          id:
            uiType: text
            uiLabel: IA.PAYMENT_TERM
      exchangeRate:
        fields:
          date:
            uiType: date
            uiLabel: IA.EXCHANGE_RATE_DATE
          typeId:
            uiType: ptr
            uiLabel: IA.EXCHANGE_RATE_TYPE_ID
          typeName:
            uiType: ptr
            uiLabel: IA.EXCHANGE_RATE_TYPE
  item:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.ITEM_ID
      key:
        uiType: integer
        uiLabel: IA.ITEM_KEY
  revenueRecognitionTemplate:
    fields:
      id:
        uiType: text
        uiLabel: IA.REVENUE_RECOGNITION_TEMPLATE
      key:
        uiType: text
        uiLabel: IA.REVENUE_RECOGNITION_TEMPLATE_KEY
  dimensions:
    fields: { }
    refs:
      class:
        fields:
          id:
            uiType: text
            uiLabel: IA.CLASS_ID
          name:
            uiType: text
            uiLabel: IA.CLASS_NAME
      contract:
        fields:
          id:
            uiType: text
            uiLabel: IA.CONTRACT_ID
          name:
            uiType: text
            uiLabel: IA.CONTRACT_NAME
      department:
        fields:
          id:
            uiType: text
            uiLabel: IA.DEPARTMENT_ID
          name:
            uiType: text
            uiLabel: IA.DEPARTMENT_NAME
      employee:
        fields:
          id:
            uiType: text
            uiLabel: IA.EMPLOYEE_ID
          name:
            uiType: text
            uiLabel: IA.EMPLOYEE_NAME
      location:
        fields:
          id:
            uiType: text
            uiLabel: IA.LOCATION_ID
          name:
            uiType: text
            uiLabel: IA.LOCATION_NAME
      vendor:
        fields:
          id:
            uiType: text
            uiLabel: IA.VENDOR_ID
          name:
            uiType: text
            uiLabel: IA.VENDOR_NAME
      project:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.PROJECT_ID
          name:
            uiType: text
            uiLabel: IA.PROJECT_NAME
  audit:
    fields:
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID