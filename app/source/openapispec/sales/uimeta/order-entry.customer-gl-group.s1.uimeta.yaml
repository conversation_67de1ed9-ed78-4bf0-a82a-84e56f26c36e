fields:
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  id:
    uiType: string
    uiLabel: IA.NAME
  status:
    uiType: enum
    uiLabel: IA.STATUS
    enumsLabels:
      -
        label: IA.ACTIVE
        value: active
      -
        label: IA.INACTIVE
        value: inactive
groups:
  audit:
    uiLabel: IA.AUDIT
    fields:
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.XG_CREATED_ON
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.XG_MODIFIED_ON
    refs:
      createdByUser:
        uiLabel: IA.CREATED_BY_USER
        fields:
          key:
            uiType: integer
            uiLabel: IA.XG_CREATED_BY_USER_KEY
          id:
            uiType: text
            uiLabel: IA.XG_CREATED_BY
      modifiedByUser:
        uiLabel: IA.MODIFIED_BY_USER
        fields:
          key:
            uiType: integer
            uiLabel: IA.XG_MODIFIED_BY_USER_KEY
          id:
            uiType: text
            uiLabel: IA.XG_MODIFIED_BY
refs:
  entity:
    fields:
      id:
        uiType: text
        uiLabel: IA.ENTITY_ID
      key:
        uiType: integer
        uiLabel: IA.ENTITY_KEY