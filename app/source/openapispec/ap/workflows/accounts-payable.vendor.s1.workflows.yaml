accounts-payable-vendor-actions-approve-request:
  type: object
  x-mappedTo: vendor
  properties:
    key:
      type: string
      description: System-assigned key for the vendor.
      x-mappedTo: RECORDNO
      example: '518'
    notes:
      type: string
      description: Notes or comments about this vendor.
      x-mappedTo: COMMENTS
      example: Approved, ready for use
accounts-payable-vendor-actions-approve-response:
  type: object
  x-mappedTo: vendor
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      readOnly: true
      example: '518'
    id:
      type: string
      x-mappedTo: VENDORID
      readOnly: true
      example: 'V-00014'
    href:
      type: string
      readOnly: true
      example: /objects/accounts-payable/vendor/518
    state:
      type: string
      description: Vendor state
      readOnly: true
      x-mappedTo: STATE
      enum:
        - approved
      x-mappedToValues:
        - A
      example: approved
accounts-payable-vendor-actions-decline-request:
  type: object
  x-mappedTo: vendor
  properties:
    key:
      type: string
      description: System-assigned key for the vendor.
      x-mappedTo: RECORDNO
      example: '518'
    notes:
      type: string
      description: Notes or comments about this vendor.
      x-mappedTo: COMMENTS
      example: Declined, missing information
accounts-payable-vendor-actions-decline-response:
  type: object
  x-mappedTo: vendor
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      readOnly: true
      example: '518'
    id:
      type: string
      x-mappedTo: VENDORID
      readOnly: true
      example: 'V-00014'
    href:
      type: string
      readOnly: true
      example: /objects/accounts-payable/vendor/518
    state:
      type: string
      description: Vendor state
      readOnly: true
      x-mappedTo: STATE
      enum:
        - declined
      x-mappedToValues:
        - R
      example: declined