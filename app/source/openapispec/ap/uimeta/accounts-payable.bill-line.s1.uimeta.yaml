uiLabel: IA.AP_BILL_DETAIL
fields:
  memo:
    uiType: text
    uiLabel: IA.MEMO
  totalTxnAmount:
    uiType: text
    uiLabel: IA.TRANSACTION_TOTAL
  txnAmount:
    uiType: text
    uiLabel: IA.TRANSACTION_AMOUNT
  baseAmount:
    uiType: text
    uiLabel: IA.BASE_AMOUNT
  hasForm1099:
    uiType: text
    uiLabel: IA.1099
  releaseToPay:
    uiType: enum
    uiLabel: IA.RELEASE_TO_PAY
    enumsLabels:
      - label: IA.TRUE
        value: true
      - label: IA.False
        value: false
groups:
  purchasing:
    uiLabel: IA.PURCHASING
    fields:
      id:
        uiType: text
        uiLabel: IA.ALLOCATION_ID
    refs:
      document:
        fields:
          id:
            uiType: text
            uiLabel: IA.DOCUMENT_ID
      documentLine:
        fields:
          id:
            uiType: text
            uiLabel: IA.PRIMARY_DOCUMENT_LINE_NUMBER
  fixedAsset:
    uiLabel: IA.FIXED_ASSET
    fields:
      nameOfAcquiredAsset:
        uiType: text
        uiLabel: IA.NAME_OF_ACQUIRED_ASSET
      includeTaxInAssetCost:
        uiType: enum
        uiLabel: IA.INCLUDE_TAX_IN_ASSET_COST
        enumsLabels:
          - label: IA.TRUE
            value: true
          - label: IA.False
            value: false
  project:
    uiLabel: IA.PROJECT
    fields:
      isBillable:
        uiType: enum
        uiLabel: IA.BILLABLE
        enumsLabels:
          - label: IA.TRUE
            value: true
          - label: IA.False
            value: false
  form1099:
    uiLabel: IA.FORM1099
    fields:
      type:
        uiType: text
        uiLabel: IA.FORM_1099_TYPE
      box:
        uiType: text
        uiLabel: IA.FORM_1099_BOX
  dimensions:
    uiLabel: IA.DIMENSIONS
    fields:
    refs:
      location:
        uiLabel: IA.LOCATION
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_LOCATION_ID
      employee:
        uiLabel: IA.EMPLOYEE
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_EMPLOYEE_ID
      project:
        uiLabel: IA.PROJECT
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_PROJECT_ID
      customer:
        uiLabel: IA.CUSTOMER
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_CUSTOMER_ID
      vendor:
        uiLabel: IA.VENDOR
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_VENDOR_ID
      item:
        uiLabel: IA.ITEM
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_ITEM_ID
      warehouse:
        uiLabel: IA.WAREHOUSE
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_WAREHOUSE_ID
      class:
        uiLabel: IA.CLASS
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_ClASS_ID
      task:
        uiLabel: IA.TASK
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_TASK_ID
      contract:
        uiLabel: IA.CONTRACT
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_CONTRACT_ID
      costType:
        uiLabel: IA.COST_TYPE
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_COST_TYPE_ID
      affiliateEntity:
        uiLabel: IA.AFFILIATE_ENTITY
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_AFFILIATE_ENTITY_ID
      department:
        uiLabel: IA.DEPARTMENT
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_DEPARTMENT_ID
      asset:
        uiLabel: IA.ASSET
        fields:
          id:
            uiType: text
            uiLabel: IA.DIMENSIONS_ASSET_ID
refs:
  bill:
    uiLabel: IA.AP_BILL_DETAIL
    fields:
      id:
        uiType: text
        uiLabel: IA.BILL_NO
  baseLocation:
    uiLabel: IA.BASE_LOCATION
    fields:
      id:
        uiType: text
        uiLabel: IA.BASE_LOCATION_ID
  glAccount:
    uiLabel: IA.GL_ACCOUNT
    fields:
      id:
        uiType: text
        uiLabel: IA.GL_ACCOUNT_ID
  overrideOffsetGLAccount:
    uiLabel: IA.GL_ACCOUNT
    fields:
      id:
        uiType: text
        uiLabel: IA.GL_ACCOUNT_ID
  accountLabel:
    uiLabel: IA.ACCOUNT_LABEL
    fields:
      id:
        uiType: text
        uiLabel: IA.ACCOUNT_LABEL_ID
  allocation:
    uiLabel: IA.ALLOCATION
    fields:
      id:
        uiType: text
        uiLabel: IA.ALLOCATION_ID

