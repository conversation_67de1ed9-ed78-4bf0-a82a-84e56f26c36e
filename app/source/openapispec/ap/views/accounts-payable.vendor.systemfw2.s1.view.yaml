key: accounts-payable/vendor::systemVendorFW2
id: systemVendorFW2
object: accounts-payable/vendor
name: IA.RECENTLY_MODIFIED
description: Specifies Recently Modified
query:
  object: accounts-payable/vendor
  fields:
    - id
    - name
    - contacts.default.mailingAddress.addressLine1
    - contacts.default.mailingAddress.city
    - contacts.default.mailingAddress.state
    - contacts.default.mailingAddress.postCode
    - contacts.default.mailingAddress.country
    - totalDue
    - status
    - attachment.id
    - audit.modifiedDateTime
    - audit.modifiedBy
  orderBy:
    - audit.modifiedDateTime: desc
  filterParameters:
    includeHierarchyFields: true
  filters:
    - $eq:
        status: active
    - $eq:
        isOneTimeUse: false
metadata:
  frozenColumnsCount: 2
  columns:
    - id: "id"
      format: "clip"
      size: 40
    - id: "name"
      format: "clip"
      size: 40
    - id: "computed.viewLedger"
      format: "clip"
      size: 40
    - id: "contacts.default.mailingAddress.addressLine1"
      format: "clip"
      size: 40
    - id: "contacts.default.mailingAddress.city"
      format: "clip"
      size: 40
    - id: "contacts.default.mailingAddress.state"
      format: "clip"
      size: 40
    - id: "contacts.default.mailingAddress.postCode"
      format: "clip"
      size: 40
    - id: "contacts.default.mailingAddress.country"
      format: "clip"
      size: 40
    - id: "totalDue"
      format: "clip"
      size: 40
    - id: "status"
      format: "clip"
      size: 40
    - id: "attachment.id"
      format: "clip"
      size: 40
    - id: "audit.modifiedDateTime"
      format: "clip"
      size: 40
    - id: "audit.modifiedBy"
      format: "clip"
      size: 40
contexts:
  - __default
