key: accounts-payable/recurring-bill::systemAccounts-payable/recurring-billFW1
id: systemAccounts-payable/recurring-billFW1
object: accounts-payable/recurring-bill
name: All
description: Specifies all active accounts-payable/recurring-bills
query:
  object: accounts-payable/recurring-bill
  fields:
    - key
    - vendor.id
    - vendor.name
    - contract.description
    - currency.txnCurrency
    - txnTotalEntered
    - currency.baseCurrency
    - totalEntered
    - schedule.nextExecutionDate
    - schedule.repeatBy
    - schedule.executionCount
    - attachment.id
    - status
    - recurringSchedule.key
  orderBy:
    - vendor.name: asc
  filters:
    - $eq:
        status: active
metadata:
  frozenColumnsCount: 2
  columns:
    - id: "key"
      format: "clip"
      size: 40
    - id: "vendor.id"
      format: "clip"
      size: 40
    - id: "vendor.name"
      format: "clip"
      size: 40
    - id: "contract.description"
      format: "clip"
      size: 40
    - id: "currency.txnCurrency"
      format: "clip"
      size: 40
    - id: "txnTotalEntered"
      format: "clip"
      size: 40
    - id: "currency.baseCurrency"
      format: "clip"
      size: 40
    - id: "totalEntered"
      format: "clip"
      size: 40
    - id: "schedule.nextExecutionDate"
      format: "clip"
      size: 40
    - id: "schedule.repeatBy"
      format: "clip"
      size: 40
    - id: "schedule.executionCount"
      format: "clip"
      size: 40
    - id: "computed.lastResult"
      format: "clip"
      size: 40
    - id: "attachment.id"
      format: "clip"
      size: 40
    - id: "status"
      format: "clip"
      size: 40

contexts:
  - __default