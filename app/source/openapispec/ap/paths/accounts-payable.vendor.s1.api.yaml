openapi: 3.0.0
info:
  title: accounts-payable-vendor
  description: accounts-payable.vendor API
  version: '1.0'
  contact:
    email: ravi.shan<PERSON><PERSON><PERSON><PERSON><PERSON>@sage.com
    name: <PERSON>
tags:
  - name: Vendors
    description: | 
     Vendors are individuals or companies that you pay for goods and services. 
     
     When you create a vendor, you provide a name and a unique identifier (unless the company is configured to auto-generate identifiers). 
     
     You can also designate a credit limit, set a payment priority, establish the payment method for the vendor, and so forth. For more information, see [Create a vendor](https://www.intacct.com/ia/docs/en_US/help_action/Default.htm#cshid=Adding_a_Vendor) in the Sage Intacct Help Center. 
servers:
  - url: 'https://dev01.intacct.com/users/anjali.israni/projects.nextgenapi/api/v0'
    description: Development server.
paths:
  /objects/accounts-payable/vendor:
    get:
      summary: List vendors
      description: |
        Returns a collection with a key, ID, and link for each vendor. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View Vendors
      tags:
        - Vendors
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of vendor objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List vendors:
                  value:
                    'ia::result':
                      - key: '84'
                        id: Vend-00001
                        href: /objects/accounts-payable/vendor/84
                      - key: '85'
                        id: Vend-00002
                        href: /objects/accounts-payable/vendor/85
                      - key: '60'
                        id: Vend-00003
                        href: /objects/accounts-payable/vendor/60
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
      operationId: list-accounts-payable-vendor
    post:
      summary: Create a vendor
      description: Creates a new vendor. When you add a new vendor, you can provide key descriptive information about that vendor and establish how you want to pay them.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, View, Add, Edit Vendors (Bank Details permissions required to access vendor bank account.)
      tags:
        - Vendors
      operationId: create-accounts-payable-vendor
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-vendor'
                - $ref: '#/components/schemas/accounts-payable-vendorRequiredProperties'
            examples:
              Create a vendor:
                value:
                  id: Vend-00010
                  name: Design Works
                  creditLimit: 40000
                  isOnHold: false
                  doNotPay: false
                  billingType: openItem
                  paymentPriority: normal
                  billPayment:
                    defaultDateOptions: none
              Create an unrestricted vendor:
                value:
                  id: Vendor_A0000003
                  name: Global Networks
                  form1099:
                    is1099Eligible: true
                    nameOn1099: ISC 1099
                    type: MISC
                    box: '3'
                  parent:
                    id: '201'
                  contacts:
                    default:
                      mailingAddress:
                        addressLine1: 300 Park Avenue
                        addressLine2: Suite 1400
                        addressLine3: Western industrial area
                        city: San Jose
                        country: United States
                        postCode: '95110'
                        state: California
                      tax:
                        isTaxable: true
                      URL1: 'https://mycompany.com'
                      URL2: 'https://anothercompany.com'
                      companyName: Ecology Corporation
                      email1: <EMAIL>
                      email2: <EMAIL>
                      fax: '***********'
                      firstName: Alice
                      lastName: Smith
                      middleName: Mary
                      mobile: '***********'
                      pager: '***********'
                      phone1: '***********'
                      printAs: Smith.Alice
                      hideContactList: false
                    primary:
                      id: 1099 IntCA
                    payTo:
                      id: 1099 Int
                    returnTo:
                      id: 111(V111)
                    recipient1099:
                      id: 123515(V123515)
                  term:
                    id: Net 60
                  vendorAccountNumber: '*********'
                  taxId: 111-22-1111
                  creditLimit: 1000
                  billingType: openItem
                  vendorType:
                    id: Retailer
                  accountGroup:
                    id: Stationary VGL Group
                  priceSchedule:
                    id: PO Price S
                  discountPercent: 20
                  priceList:
                    id: PO Price List
                  notes: Comments of IN202
                  defaultExpenseGLAccount:
                    id: '6700.04'
                  paymentPriority: normal
                  status: active
                  isOneTimeUse: true
                  isOnHold: true
                  doNotPay: true
                  ach:
                    enablePayments: true
                    routingNumber: '*********'
                    accountNumber: '*********'
                    accountType: checkingAccount
                    remittanceType: businessCTX
                  displayTermDiscountOnCheckStub: false
                  displayVendorAccountOnCheckStub: false
                  sendPaymentNotification: true
                  preferredPaymentMethod: printedCheck
                  mergePaymentRequests: true
                  overrideOffsetGLAccount:
                    id: '1000.00'
                  attachment:
                    id: Attach-12574
                  contactList:
                    - name: primary
                      contact:
                        id: 1099 IntCA
                    - name: pay to
                      contact:
                        id: 1099 Int
                    - name: return to
                      contact:
                        id: 111(V111)
                    - name: '1099'
                      contact:
                        id: 123515(V123515)
                  vendorAccountNumberList:
                    - locationId: 1--United States of America
                      accountNo: '*********'
                  vendorEmailTemplates:
                    - vendor:
                        id: Vendor_A0000003
                      txnDefinitionName: Purchase Order
                      emailTemplate:
                        id: '5'
                        name: AP
                  billPayment:
                    defaultDateOptions: none
              Create a restricted vendor:
                value:
                  id: Vend-00100
                  name: Regulatory Filing Group
                  parent:
                    id: '202'
                  contacts:
                    default:
                      mailingAddress:
                        addressLine1: '9500 Arboretum Blvd'
                        addressLine2: 'Suite 310'
                        addressLine3: 'Western industrial area'
                        city: Austin
                        country: United States
                        postCode: '78759'
                        state: TX
                      tax:
                        isTaxable: true
                      URL1: 'https:/rfginc.com'
                      companyName: RFG Inc
                      email1: <EMAIL>
                      email2: <EMAIL>
                      fax: '***********'
                      firstName: Jack
                      lastName: Jones
                      middleName: Inc
                      mobile: '***********'
                      pager: '***********'
                      phone1: '***********'
                      printAs: RFG Inc
                      hideContactList: true
                  term:
                    id: Net 60
                  vendorAccountNumber: '203'
                  taxId: 192-35-4308
                  creditLimit: 350000
                  billingType: balanceForward
                  notes: None
                  defaultExpenseGLAccount:
                    id: '1500'
                  paymentPriority: normal
                  status: active
                  state: a
                  isOneTimeUse: false
                  isOnHold: false
                  doNotPay: false
                  displayTermDiscountOnCheckStub: false
                  displayVendorAccountOnCheckStub: false
                  sendPaymentNotification: false
                  mergePaymentRequests: true
                  billPayment:
                    defaultDateOptions: none
              Create a vendor in a PO-match enabled company:
                value:
                  id: VEND-0001
                  name: Office Supplies
                  taxId: 12-3456788
                  creditLimit: 50000
                  isOnHold: false
                  doNotPay: false
                  alwaysCreateBill: true
                  billingType: openItem
                  paymentPriority: normal
                  billPayment:
                    defaultDateOptions: none
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New vendor
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to new vendor:
                  value:
                    'ia::result':
                      key: '111'
                      href: /objects/accounts-payable/vendor/111
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
  /objects/accounts-payable/vendor/{key}:
    parameters:
      - schema:
          type: string
          example: '111'
        name: key
        in: path
        required: true
        description: System-assigned key for the vendor.
        example: '111'
    get:
      summary: Get a vendor
      description: Returns detailed information for a specified vendor.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View Vendors
      tags:
        - Vendors
      operationId: get-accounts-payable-vendor-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the vendor
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-payable-vendor'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a vendor:
                  value:
                    'ia::result':
                      key: '111'
                      id: VEND-00010
                      name: Design Works
                      status: active
                      state: a
                      taxId: 12-3456789
                      form1099:
                        nameOn1099: Design Works Inc
                        type: MISC
                        box: '3'
                      creditLimit: 40000
                      isOnHold: true
                      doNotPay: true
                      alwaysCreateBill: false
                      currency: USD
                      contacts:
                        primary:
                          id: 'Doe, John'
                          key: '271'
                          href: /objects/company-config/contact/271
                        payTo:
                          id: 'Ho, Amy'
                          key: '298'
                          href: /objects/company-config/contact/298
                        returnTo:
                          id: 'Doe, John'
                          key: '271'
                          href: /objects/company-config/contact/271
                        recipient1099:
                          id: 'Ho, Amy'
                          key: '298'
                          href: /objects/company-config/contact/298
                      mergePaymentRequests: true
                      sendPaymentNotification: true
                      restriction:
                        restrictionType: unrestricted
                      billingType: openItem
                      paymentPriority: normal
                      displayTermDiscountOnCheckStub: true
                      billPayment:
                        defaultDateOptions: none
                      displayVendorAccountOnCheckStub: true
                      ach:
                        enablePayments: false
                        routingNumber: null
                        accountNumber: null
                        accountType: null
                        remittanceType: null
                      audit:
                        modifiedDateTime: '2021-03-24T21:16:54Z'
                        createdDateTime: '2021-03-24T21:16:54Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      entity:
                        id: '1'
                        key: '1'
                        name: 'United States of America'
                      href: /objects/accounts-payable/vendor/111
                    'ia::meta':
                      totalCount: 1
                Get an unrestricted vendor:
                  value:
                    'ia::result':
                      key: '69'
                      id: Vendor_A0000001
                      name: Vendor_A0000001
                      form1099:
                        nameOn1099: ISC 1099
                        type: MISC
                        box: '3'
                      parent:
                        key: '47'
                        id: '201'
                        name: PG & E
                        href: /objects/accounts-payable/vendor/47
                      contacts:
                        default:
                          mailingAddress:
                            addressLine1: 300 Park Avenue
                            addressLine2: Suite 1400
                            addressLine3: Western industrial area
                            city: San Jose
                            country: United States
                            postCode: '95110'
                            state: California
                          tax:
                            isTaxable: true
                          URL1: 'https://mycompany.com'
                          URL2: 'https://anothercompany.com'
                          companyName: Ecology Corp
                          email1: <EMAIL>
                          email2: <EMAIL>
                          fax: '***********'
                          firstName: Alice
                          id: Vendor_A0000001
                          lastName: Smith
                          middleName: Marie
                          mobile: '***********'
                          pager: '***********'
                          phone1: '***********'
                          phone2: '14085559876'
                          prefix: 'Ms.'
                          printAs: Alice.Cooper
                          key: '237'
                          hideContactList: false
                          href: /objects/company-config/contact/237
                        primary:
                          id: 1099 IntCA
                          key: '206'
                          href: /objects/company-config/contact/206
                        payTo:
                          id: 1099 Int
                          key: '211'
                          href: /objects/company-config/contact/211
                        returnTo:
                          id: 111(V111)
                          key: '748'
                          href: /objects/company-config/contact/748
                        recipient1099:
                          id: 123515(V123515)
                          key: '341'
                          href: /objects/company-config/contact/341
                      term:
                        id: Net 60
                        key: '11'
                        href: /objects/accounts-payable/term/11
                      vendorAccountNumber: '*********'
                      taxId: 111-22-1111
                      creditLimit: 1000
                      totalDue: '0'
                      billingType: openItem
                      vendorType:
                        id: Retailer
                        key: '14'
                        href: /objects/accounts-payable/vendor-type/14
                      accountGroup:
                        id: Stationary VGL Group
                        key: '14'
                        href: /objects/accounts-payable/vendor-account-group/14
                      priceSchedule:
                        id: PO Price S
                      discountPercent: 20
                      priceList:
                        id: PO Price List
                        key: '2'
                        href: /objects/purchasing/price-list/2
                      notes: Comments of IN202
                      accountlabel:
                        id: Equipment
                        key: '10'
                      defaultExpenseGLAccount:
                        id: '6700.04'
                        name: CC Other Charges & Fees
                        key: '326'
                        href: /objects/general-ledger/account/326
                      paymentPriority: normal
                      status: active
                      state: a
                      isOneTimeUse: true
                      isOnHold: true
                      audit:
                        modifiedDateTime: '2022-05-26T04:49:44Z'
                        createdDateTime: '2018-07-28T19:35:16Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      doNotPay: true
                      alwaysCreateBill: false
                      currency: USD
                      ach:
                        enablePayments: true
                        routingNumber: '*********'
                        accountNumber: '*********'
                        accountType: checkingAccount
                        remittanceType: businessCTX
                      displayTermDiscountOnCheckStub: false
                      displayVendorAccountOnCheckStub: false
                      sendPaymentNotification: true
                      preferredPaymentMethod: printedCheck
                      mergePaymentRequests: true
                      overrideOffsetGLAccount:
                        id: '1000.00'
                        key: '1000.00'
                        name: '10000'
                        href: /objects/general-ledger/account/1000.00--10000
                      attachment:
                        id: Attach-12574
                        key: Attach-12574
                        href: /objects/company-config/attachment/Attach-12574
                      defaultLeadTime: 5
                      retainagePercentage: 10
                      lastBillCreatedDate: null
                      lastPaymentMadeDate: null
                      tpar:
                        isTparEnabled: true
                        name: 4IMPRINT INC
                      t5018:
                        isT5018Enabled: true
                        t5018Number: 123OOR652
                      contactList:
                        - id: '215'
                          key: '215'
                          name: primary
                          contact:
                            id: 1099 IntCA
                          audit:
                            modifiedDateTime: '2022-05-26T04:49:44Z'
                            createdDateTime: '2022-05-26T04:49:44Z'
                            createdBy: '1'
                            modifiedBy: '1'
                        - id: '216'
                          key: '216'
                          name: pay to
                          contact:
                            id: 1099 Int
                          audit:
                            modifiedDateTime: '2022-05-26T04:49:44Z'
                            createdDateTime: '2022-05-26T04:49:44Z'
                            createdBy: '1'
                            modifiedBy: '1'
                        - id: '217'
                          key: '217'
                          name: return to
                          contact:
                            id: 111(V111)
                          audit:
                            modifiedDateTime: '2022-05-26T04:49:44Z'
                            createdDateTime: '2022-05-26T04:49:44Z'
                            createdBy: '1'
                            modifiedBy: '1'
                        - id: '218'
                          key: '218'
                          name: '1099'
                          contact:
                            id: 123515(V123515)
                          audit:
                            modifiedDateTime: '2022-05-26T04:49:44Z'
                            createdDateTime: '2022-05-26T04:49:44Z'
                            createdBy: '1'
                            modifiedBy: '1'
                      vendorAccountNumberList:
                        - locationId: 1--United States of America
                          accountNo: '*********'
                      vendorEmailTemplates:
                        - id: '11'
                          key: '11'
                          vendor:
                            key: '69'
                            id: Vendor_A0000001
                            href: /objects/accounts-payable/vendor/69
                          txnDefinitionName: Purchase Order
                          emailTemplate:
                            id: '5'
                            key: '5'
                            name: AP
                            href: /objects/company-config/email-template/5
                          href: /objects/accounts-payable/vendor-email-template/11
                      vendorPaymentProviders: []
                      billPayment:
                        defaultDateOptions: none
                      href: /objects/accounts-payable/vendor/69
                    'ia::meta':
                      totalCount: 1
                Get a restricted vendor:
                  value:
                    'ia::result':
                      key: '49'
                      id: '203'
                      name: 'NCS, Inc.'
                      form1099:
                        nameOn1099: null
                        type: null
                        box: null
                      parent:
                        key: '48'
                        id: '202'
                        name: Pac Bell
                        href: /objects/accounts-payable/vendor/48
                      contacts:
                        default:
                          mailingAddress:
                            addressLine1: '9500'
                            addressLine2: 'Arboretum Blvd, Suite 310'
                            addressLine3: 'Western industrial area'
                            city: Austin
                            country: United States
                            postCode: '78759'
                            state: TX
                          tax:
                            isTaxable: true
                          URL1: 'https://mycompany.com'
                          URL2: 'https://anothercompany.com'
                          companyName: 'NCS, Inc.'
                          email1: <EMAIL>
                          email2: <EMAIL>
                          fax: '***********'
                          firstName: Jack
                          id: 'NCS, Inc.'
                          lastName: Henry
                          middleName: James
                          mobile: '***********'
                          pager: '***********'
                          phone1: '***********'
                          phone2: '14085559876'
                          prefix: 'Mr.'
                          printAs: Jack Henry & Associates
                          key: '217'
                          hideContactList: true
                          href: /objects/company-config/contact/217
                        primary:
                          id: null
                          key: null
                        payTo:
                          id: null
                          key: null
                        returnTo:
                          id: null
                          key: null
                        recipient1099:
                          id: null
                          key: null
                      term:
                        id: Net 60
                        key: '11'
                        href: /objects/accounts-payable/term/11
                      vendorAccountNumber: '203'
                      taxId: 192-35-4308
                      creditLimit: 350000
                      totalDue: '0'
                      billingType: balanceForward
                      vendorType:
                        id: Wholesaler
                        key: '12'
                      accountGroup:
                        id: Midwest
                        key: '5'
                      priceSchedule:
                        id: null
                      discountPercent: null
                      priceList:
                        id: null
                        key: null
                      notes: None
                      accountlabel:
                        id: null
                        key: null
                      defaultExpenseGLAccount:
                        id: '1500'
                        name: Equipment
                        key: '93'
                        href: /objects/general-ledger/account/93
                      paymentPriority: normal
                      status: active
                      state: a
                      isOneTimeUse: false
                      isOnHold: false
                      audit:
                        modifiedDateTime: '2024-05-26T05:28:54Z'
                        createdDateTime: '2023-07-28T19:35:15Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      doNotPay: false
                      alwaysCreateBill: false
                      currency: USD
                      ach:
                        enablePayments: false
                        routingNumber: null
                        accountNumber: null
                        accountType: null
                        remittanceType: null
                      displayTermDiscountOnCheckStub: false
                      displayVendorAccountOnCheckStub: false
                      sendPaymentNotification: false
                      preferredPaymentMethod: null
                      mergePaymentRequests: true
                      overrideOffsetGLAccount:
                        id: null
                        key: null
                        name: null
                      attachment:
                        id: null
                        key: null
                      defaultLeadTime: null
                      retainagePercentage: null
                      lastBillCreatedDate: null
                      lastPaymentMadeDate: null
                      tpar:
                        isTparEnabled: true
                        name: 4IMPRINT INC
                      t5018:
                        isT5018Enabled: true
                        t5018Number: 123OOR652
                      contactList: []
                      vendorAccountNumberList: []
                      vendorEmailTemplates: []
                      vendorPaymentProviders: []
                      billPayment:
                        defaultDateOptions: none
                      href: /objects/accounts-payable/vendor/49
                    'ia::meta':
                      totalCount: 1
                Get a vendor enabled for United Kingdom (GB) bank files:
                  value:
                    'ia::result':
                      key: '203'
                      id: UKVendor
                      name: UKVendor
                      form1099:
                        nameOn1099: null
                        type: null
                        box: null
                      parent:
                        key: null
                        id: null
                        name: null
                      contacts:
                        default:
                          mailingAddress:
                            addressLine1: 300 Park Avenue
                            addressLine2: Suite 1400
                            addressLine3: Western industrial area
                            city: San Jose
                            country: United Kingdom
                            postCode: '95110'
                            state: California
                          tax:
                            isTaxable: true
                          URL1: 'https://mycompany.com'
                          URL2: 'https://anothercompany.com'
                          companyName: AlcoSoft Inc
                          email1: <EMAIL>
                          email2: <EMAIL>
                          fax: '***********'
                          firstName: Alice
                          id: UKVendor(VUKVendor)
                          lastName: Smith
                          middleName: Marie
                          mobile: '***********'
                          pager: '***********'
                          phone1: '***********'
                          phone2: '14085559876'
                          prefix: 'Ms.'
                          printAs: UKVendor
                          key: '442'
                          hideContactList: false
                          href: /objects/company-config/contact/237
                        primary:
                          id: null
                          key: null
                        payTo:
                          id: null
                          key: null
                        returnTo:
                          id: null
                          key: null
                        recipient1099:
                          id: null
                          key: null
                      term:
                        id: null
                        key: null
                      vendorAccountNumber: null
                      taxId: null
                      creditLimit: 1000
                      totalDue: '507.40'
                      billingType: openItem
                      vendorType:
                        id: null
                        key: null
                      accountGroup:
                        id: null
                        key: null
                      priceSchedule:
                        id: null
                      discountPercent: null
                      priceList:
                        id: null
                        key: null
                      notes: null
                      accountlabel:
                        id: null
                        key: null
                      defaultExpenseGLAccount:
                        id: null
                        name: null
                        key: null
                      paymentPriority: normal
                      status: active
                      state: a
                      isOneTimeUse: false
                      isOnHold: false
                      audit:
                        modifiedDateTime: '2024-01-03T06:43:34Z'
                        createdDateTime: '2024-01-03T06:39:33Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      doNotPay: false
                      alwaysCreateBill: false
                      currency: USD
                      ach:
                        enablePayments: false
                        routingNumber: null
                        accountNumber: null
                        accountType: null
                        remittanceType: null
                      bankFiles:
                        paymentCountryCode: gb
                        paymentCurrency: GBP
                      displayTermDiscountOnCheckStub: false
                      displayVendorAccountOnCheckStub: false
                      sendPaymentNotification: false
                      preferredPaymentMethod: null
                      mergePaymentRequests: true
                      overrideOffsetGLAccount:
                        id: null
                        key: null
                        name: null
                      attachment:
                        id: null
                        key: null
                      defaultLeadTime: null
                      retainagePercentage: null
                      lastBillCreatedDate: null
                      lastPaymentMadeDate: null
                      tpar:
                        isTparEnabled: true
                        name: 4IMPRINT INC
                      t5018:
                        isT5018Enabled: true
                        t5018Number: 123OOR652
                      vendorBankFileSetup:
                        - id: '26'
                          key: '26'
                          vendor:
                            key: '203'
                            id: UKVendor
                            href: /objects/accounts-payable/vendor/203
                          bsbNumber: '047-359'
                          paymentReference: BACS HSBC corp
                          sortCode: '849571'
                          branchCode: '213456'
                          bankAccountType: '4'
                          bankAccountCode: '345624'
                          proofOfPayment: true
                          bankAccountNumber: '********'
                          bankAccountName: UK Bank Corp.
                          businessIdCode: AIBKIE2D491
                          href: /objects/accounts-payable/vendor-bank-file-setup/26
                      vendorEmailTemplates: null
                      vendorPaymentProviders: []
                      billPayment:
                        defaultDateOptions: none
                      href: /objects/accounts-payable/vendor/203
                    'ia::meta':
                      totalCount: 1
                Get a vendor with French primary contact:
                  value:
                    'ia::result':
                      key: '203'
                      id: UKVendor
                      name: UKVendor
                      form1099:
                        nameOn1099: null
                        type: null
                        box: null
                      parent:
                        key: null
                        id: null
                        name: null
                      contacts:
                        default:
                          electronicInvoiceDetails:
                            legalCategory: '24 Fiduciary'
                            mainActivity: '10.3 Transformation and conservation of fruits and vegetables'
                            registeredCapital: '37 000'
                            typeOfCompany: '03 Intermediate sized enterprises'
                            valueAddedTaxRegime: Monthly
                          mailingAddress:
                            addressLine1: '300 Park Avenue'
                            addressLine2: Suite 1400
                            addressLine3: Western industrial area
                            city: Paris
                            country: France
                            postCode: '95110'
                            state: null
                          tax:
                            isTaxable: true
                          URL1: 'https://mycompany.com'
                          URL2: 'https://anothercompany.com'
                          companyName: AllPro Inc
                          email1: <EMAIL>
                          email2: <EMAIL>
                          fax: '***********'
                          firstName: Alice
                          id: '1099 Int'
                          internationalTaxId: '123'
                          lastName: Smith
                          middleName: Marie
                          mobile: '***********'
                          pager: '***********'
                          phone1: '***********'
                          phone2: '14085559876'
                          prefix: 'Ms.'
                          printAs: '1099 Int'
                          key: '211'
                          hideContactList: false
                          href: /objects/company-config/contact/211
                        primary:
                          id: null
                          key: null
                        payTo:
                          id: null
                          key: null
                        returnTo:
                          id: null
                          key: null
                        recipient1099:
                          id: null
                          key: null
                      term:
                        id: null
                        key: null
                      vendorAccountNumber: null
                      taxId: null
                      creditLimit: 1000
                      totalDue: '507.40'
                      billingType: openItem
                      vendorType:
                        id: null
                        key: null
                      accountGroup:
                        id: null
                        key: null
                      priceSchedule:
                        id: null
                      discountPercent: null
                      priceList:
                        id: null
                        key: null
                      notes: null
                      accountlabel:
                        id: null
                        key: null
                      defaultExpenseGLAccount:
                        id: null
                        name: null
                        key: null
                      paymentPriority: normal
                      status: active
                      state: a
                      isOneTimeUse: false
                      isOnHold: false
                      audit:
                        modifiedDateTime: '2024-01-03T06:43:34Z'
                        createdDateTime: '2024-01-03T06:39:33Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      doNotPay: false
                      alwaysCreateBill: false
                      currency: USD
                      ach:
                        enablePayments: false
                        routingNumber: null
                        accountNumber: null
                        accountType: null
                        remittanceType: null
                      bankFiles:
                        paymentCountryCode: gb
                        paymentCurrency: GBP
                      displayTermDiscountOnCheckStub: false
                      displayVendorAccountOnCheckStub: false
                      sendPaymentNotification: false
                      preferredPaymentMethod: null
                      restrictions:
                        restrictionType: unrestricted
                      mergePaymentRequests: true
                      overrideOffsetGLAccount:
                        id: null
                        key: null
                        name: null
                      attachment:
                        id: null
                        key: null
                      defaultLeadTime: null
                      retainagePercentage: null
                      lastBillCreatedDate: null
                      lastPaymentMadeDate: null
                      tpar:
                        isTparEnabled: true
                        name: 4IMPRINT INC
                      t5018:
                        isT5018Enabled: true
                        t5018Number: 123OOR652
                      vendorBankFileSetup: []
                      vendorEmailTemplates: []
                      vendorPaymentProviders: []
                      billPayment:
                        defaultDateOptions: none
                      href: /objects/accounts-payable/vendor/43
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a vendor
      description: Updates an existing vendor by setting field values. Any fields not provided remain unchanged.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, View, Edit Vendors
      tags:
        - Vendors
      operationId: update-accounts-payable-vendor-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-payable-vendor'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a single value:
                value:
                  billingType: balanceForward
              Update multiple values:
                value:
                  defaultLeadTime: 5
                  taxId: '*********'
                  form1099:
                    nameOn1099: NCS
                    type: MISC
                    box: '3'
                  isOnHold: true
                  doNotPay: true
                  alwaysCreateBill: true
                  creditLimit: 10000
                  retainagePercentage: 20
                  notes: Make sure to include on 1099
                  discountPercent: 20
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated vendor
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated vendor:
                  value:
                    'ia::result':
                      key: '1'
                      id: RituDate11-21-2015
                      href: /objects/accounts-payable/vendor/1
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a vendor
      description: Deletes a vendor. You can only delete vendors that aren't tied to any transactions or payments.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business
            permissions: List, View, Delete Vendors
      tags:
        - Vendors
      operationId: delete-accounts-payable-vendor-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
  /workflows/accounts-payable/vendor/approve:
    post:
      summary: Approve a vendor
      description: |
       When vendor approval is enabled, new and recently edited vendors are automatically submitted to an approval queue. Vendors submitted to the approval queue have a state of `submitted`. Designated approvers review those vendors and either approve or decline them. When a vendor is approved, the state for that vendor changes from `submitted` to `approved`. 
       
       For more information, see [About vendor approvals](https://www.intacct.com/ia/docs/en_US/help_action/Default.htm#cshid=About_vendor_approvals) in the Sage Intacct Help Center.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business, Employee
            permissions: Approve vendors - List
        configuration: Vendor approval enabled
      tags:
        - Vendors
      operationId: approve-accounts-payable-vendor
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/accounts-payable.vendor.s1.workflows.yaml#/accounts-payable-vendor-actions-approve-request
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../workflows/accounts-payable.vendor.s1.workflows.yaml#/accounts-payable-vendor-actions-approve-response
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
        '400':
          $ref: '#/components/responses/400error'
  /workflows/accounts-payable/vendor/decline:
    post:
      summary: Decline a vendor
      description: |
       When vendor approval is enabled, new and recently edited vendors are automatically submitted to an approval queue. Vendors submitted to the approval queue have a state of `submitted`. Designated approvers review those vendors and either approve or decline them. When a vendor is declined, the state for that vendor changes from `submitted` to `declined`.  
       
       For more information, see [About vendor approvals](https://www.intacct.com/ia/docs/en_US/help_action/Default.htm#cshid=About_vendor_approvals) in the Sage Intacct Help Center.
      x-documentationFlags:
        subscription: Accounts Payable
        userPermissions:
          - userType: Business, Employee
            permissions: Approve vendors - List
        configuration: Vendor approval enabled
      tags:
        - Vendors
      operationId: decline-accounts-payable-vendor
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/accounts-payable.vendor.s1.workflows.yaml#/accounts-payable-vendor-actions-decline-request
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../workflows/accounts-payable.vendor.s1.workflows.yaml#/accounts-payable-vendor-actions-decline-response
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-payable-vendor:
      $ref: ../models/accounts-payable.vendor.s1.schema.yaml
    accounts-payable-vendorRequiredProperties:
      type: object
      required:
        - id
        - name
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
