title: accounts-payable-adjustment-tax-entry
x-mappedTo: apadjustmenttaxentry
x-ownedBy: accounts-payable/adjustment-line
type: object
description: For VAT enabled transactions, adjustment line items will have tax entries.
allOf:
  - $ref: ../../common/models/tax-entries.s1.schema.yaml
  - type: object
    properties:
      purchasingTaxDetail:
        type: object
        description: Purchasing tax details describe a specific type of tax that applies to lines in Accounts Payable transactions.
        x-object: tax/purchasing-tax-detail
        properties:
          key:
            type: string
            description: System-assigned key for the tax detail.
            x-mappedTo: DETAILKEY
            example: '1'
          id:
            type: string
            description: Unique ID for the tax detail.
            x-mappedTo: DETAILID
            example: Alaska Tax Detail
          href:
            type: string
            description: URL endpoint for the tax detail object.
            readOnly: true
            example: /objects/tax/purchasing-tax-detail/1
      adjustmentLine:
        title: adjustment-line
        description: Line item that the tax entries are associated with.
        readOnly: true
        type: object
        x-mappedTo: apadjustmentitem
        x-object: accounts-payable/adjustment-line
        properties:
          id:
            type: string
            description: Unique ID for the adjustment line object.
            example: '100'
            readOnly: true
            x-mappedTo: PARENTENTRY
          key:
            type: string
            description: Unique key for the adjustment line object.
            example: '100'
            readOnly: true
            x-mappedTo: PARENTENTRY
          href:
            type: string
            description: URL endpoint for the adjustment line object.
            readOnly: true
            example: /objects/accounts-payable/adjustment-line/100