title: accounts-payable-payment-line
x-mappedTo: appymtentry
x-ownedBy: accounts-payable/payment
type: object
description: AP payment line items represent entries in an AP payment object. 
properties:
  key:
    type: string
    description: System-assigned unique key for the AP payment line item.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: ID for the AP payment line item. This value is the same as the key for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: URL endpoint for the AP payment line item.
    readOnly: true
    example: /objects/accounts-payable/payment-line/23
  description:
    type: string
    description: Memo or brief description of the AP payment line item.
    x-mappedTo: ENTRYDESCRIPTION
    nullable: true
    example: Penalty
  exchangeRate:
    type: object
    description: Exchange rate details used to calculate the base amount.
    readOnly: true
    title: exchangeRate
    properties:
      date:
        type: string
        format: date
        example: '2023-01-23'
        description: Exchange rate date for this transaction. Can be the current date, the date the transaction was issued, or the date the transaction will be paid.
        readOnly: true
        x-mappedTo: EXCH_RATE_DATE
      rate:
        type: string
        description: Exchange rate used to calculate the base amount from the transaction amount.
        readOnly: true
        x-mappedTo: EXCHANGE_RATE
        example: '1.0789'
      typeId:
        type: string
        description: Exchange rate type used to calculate the base amount from the transaction amount.
        readOnly: true
        x-mappedTo: EXCH_RATE_TYPE_ID
        example: '1'
        nullable: true
  lineNumber:
    type: integer
    description: Line number of the AP payment line item.
    readOnly: true
    x-mappedTo: LINE_NO
    example: 1
  baseCurrency:
    type: object
    description: Line item amounts in base currency.
    readOnly: true
    properties:
      currency:
        type: string
        description: Base currency.
        readOnly: true
        x-mappedTo: BASECURR
        example: USD
      amount:
        type: string
        format: decimal-precision-2
        description: Base amount.
        readOnly: true
        x-mappedTo: AMOUNT
        example: '10.00'
      totalPaid:
        type: string
        format: decimal-precision-2
        description: Total base amount paid for the line item.
        readOnly: true
        x-mappedTo: TOTALPAID
        example: '10.00'
      totalSelected:
        type: string
        format: decimal-precision-2
        description: Total base amount selected for the line item.
        readOnly: true
        x-mappedTo: TOTALSELECTED
        example: '0.00'
  txnCurrency:
    type: object
    description: Line item amounts in transaction currency.
    readOnly: true
    properties:
      currency:
        type: string
        description: Transaction currency.
        readOnly: true
        x-mappedTo: CURRENCY
        example: USD
      amount:
        type: string
        format: decimal-precision-2
        description: Transaction amount.
        readOnly: true
        x-mappedTo: TRX_AMOUNT
        example: '10.00'
      totalPaid:
        type: string
        format: decimal-precision-2
        description: Transaction total paid for the line item.
        readOnly: true
        x-mappedTo: TRX_TOTALPAID
        example: '10.00'
      totalSelected:
        type: string
        format: decimal-precision-2
        description: Transaction total selected for the line item.
        readOnly: true
        x-mappedTo: TRX_TOTALSELECTED
        example: '0.00'
  paymentLineRecord:
    type: string
    description: |
      Record type of the line item:
      * `pp` - AP payment record
      * `po` - applied advance record
    readOnly: true
    x-mappedTo: RECORDTYPE
    example: pp
  baseLocation:
    type: string
    description: Key for the base location associated with the payment line.
    readOnly: true
    x-mappedTo: BASELOCATION
    example: '2'
  isTax:
    type: boolean
    description: Indicates whether the line item amount includes taxes.
    readOnly: true
    x-mappedTo: ISTAX
    example: true
    x-mappedToType: string
    default: false
  taxDetail:
    type: object
    description: Purchasing tax detail for the line item. A tax schedule map applies tax details to taxable lines.
    readOnly: true
    x-mappedTo: detail
    x-object: tax/purchasing-tax-detail
    properties:
      key:
        type: string
        description: Unique key for the tax detail.
        readOnly: true
        x-mappedTo: DETAILKEY
        example: '13'
        nullable: true
      id:
        type: string
        description: ID for the tax detail.
        readOnly: true
        x-mappedTo: DETAILID
        example: 'AUS-TAX'
        nullable: true
      href:
        type: string
        description: URL endpoint for the tax detail.
        readOnly: true
        example: /objects/tax/purchasing-tax-detail/13
  glAccount:
    type: object
    description: General Ledger (GL) account associated with the payment line.
    x-mappedTo: glaccount
    x-object: general-ledger/account
    readOnly: true
    properties:
      key:
        type: string
        description: Unique key for the GL account.
        readOnly: true
        x-mappedTo: ACCOUNTKEY
        example: '23'
      id:
        type: string
        description: ID for the GL account.
        readOnly: true
        x-mappedTo: ACCOUNTNO
        example: '6330'
      name:
        type: string
        description: Title of the GL account.
        readOnly: true
        x-mappedTo: ACCOUNTTITLE
        example: 'Compensation- Salary'
      href:
        type: string
        description: URL endpoint for the GL account.
        readOnly: true
        example: /objects/general-ledger/account/23
  dimensions:
    type: object
    allOf:
      - $ref: ../../common/references/dimension-ref.s1.schema.yaml
      - type: object
        properties:
          department:
            type: object
            description: Department associated with the AP payment line item.
            readOnly: true
            x-object: company-config/department
            x-mappedTo: department
            properties:
              key:
                type: string
                description: Unique key for the department.
                readOnly: true
                x-mappedTo: DEPARTMENTKEY
                example: '12'
                nullable: true
              id:
                type: string
                description: ID for the department.
                readOnly: true
                x-mappedTo: DEPARTMENTID
                example: '12'
                nullable: true
              name:
                type: string
                description: Name of the department.
                readOnly: true
                x-mappedTo: DEPARTMENTNAME
                example: 'Accounts'
                nullable: true
              href:
                type: string
                description: URL endpoint for the department.
                readOnly: true
                example: /objects/company-config/department/12
          location:
            type: object
            description: Location associated with the AP payment line item.
            readOnly: true
            x-mappedTo: location
            x-object: company-config/location
            properties:
              key:
                type: string
                description: Unique key for the location.
                readOnly: true
                x-mappedTo: LOCATIONKEY
                example: '22'
                nullable: true
              id:
                type: string
                description: ID for the location.
                readOnly: true
                x-mappedTo: LOCATIONID
                example: LOC-22
                nullable: true
              name:
                type: string
                description: Name of the location.
                readOnly: true
                x-mappedTo: LOCATIONNAME
                example: 'India'
                nullable: true
              href:
                type: string
                description: URL endpoint for the location.
                readOnly: true
                example: /objects/company-config/location/22
  apPayment:
    type: object
    description: Header level details for the AP payment's line entries.
    readOnly: true
    x-mappedTo: appymt
    x-object: accounts-payable/payment
    properties:
      key:
        type: string
        description: Unique key for the AP payment.
        readOnly: true
        x-mappedTo: RECORDKEY
        example: '3220'
      id:
        type: string
        description: ID for the AP payment.
        readOnly: true
        x-mappedTo: RECORDKEY
        example: '3220'
      href:
        type: string
        description: URL endpoint for the AP payment.
        readOnly: true
        example: /objects/accounts-payable/payment/3220
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml