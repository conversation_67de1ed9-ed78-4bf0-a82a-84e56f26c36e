openapi: 3.0.0
info:
  title: admin.provisioning.copy-company-cache
  description: Copy company cache API
  version: '1.0-internal'
  contact:
    name: <PERSON><PERSON>
    email: adrian<PERSON>.<EMAIL>
tags:
  - name: admin.provisioning.copy-company-cache
    description: Copy company data stored in mongoDB. Internal API used by the Provisioning domain service
paths:
  /services/admin/provisioning/copy-company-cache:
    post:
      summary: Copy company cache
      description: Copy company cache from MongoDB
      tags:
        - admin.provisioning.copy-company-cache
      operationId: post-services-admin-provisioning-copy-company-cache
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.provisioning.copy-company-cache-req'
            examples:
              example-1:
                value:
                  companyKey: '5008931455'
                  sourceCompanyKey: '*********'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.provisioning.copy-company-cache-resp'
              examples:
                example-1:
                  value:
                    ia:result:
                      status: true
                      data:
                        message: 'Company cache from MongoDB was copied successfully.'
                    ia:meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
                example-2:
                  value:
                    ia:result:
                      status: false
                      errors:
                        - code: 'CJ-0718'
                          message: 'Cannot copy company cache from MongoDB.'
                          supportId: 'SupportId-Hash1234'
                    ia:meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    admin.provisioning.copy-company-cache-req:
      description: Copy company cache request
      type: object
      x-mappedTo: __custom__
      properties:
        companyKey:
          type: string
          description: New company cny
          x-mappedTo: companyKey
          example: '5008931455'
        sourceCompanyKey:
          type: string
          description: Source company cny
          x-mappedTo: sourceCompanyKey
          example: '*********'
    admin.provisioning.copy-company-cache-resp:
      description: Copy company cache response
      type: object
      x-mappedTo: __custom__
      properties:
        status:
          type: boolean
          description: The status of the request
          x-mappedTo: status
          default: false
          example: true
        errors:
          type: array
          description: List of errors
          x-mappedTo: errors
          items:
            type: object
            properties:
              code:
                type: string
                description: Error code
                x-mappedTo: code
                example: 'CJ-0718'
              message:
                type: string
                description: Error message
                x-mappedTo: message
                example: 'Cannot copy company cache from MongoDB.'
              supportId:
                type: string
                description: Error support id
                x-mappedTo: supportId
                example: 'DV8796245EFgh08nalGT679nbIlVgR'
        data:
          type: object
          description: Success message
          x-mappedTo: data
          properties:
            message:
              type: string
              description: Success message
              x-mappedTo: data.message
              example: 'Company cache from MongoDB was copied successfully.'
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
