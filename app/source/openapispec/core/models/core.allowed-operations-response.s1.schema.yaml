title: allowed-operations-response
description: Array of objects and allowed operations.
x-mappedTo: __custom__
type: array
items:
  type: object
  properties:
    key:
      type: string
      description: Unique key of the object.
    operations:
      type: array
      description: List of operations supported by the object. If an `operations` list was included in the request, the response only includes supported operations from that list.
      items:
        type: string