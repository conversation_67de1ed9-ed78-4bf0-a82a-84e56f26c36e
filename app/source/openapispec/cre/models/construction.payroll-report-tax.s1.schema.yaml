title: construction-payroll-report-tax
x-mappedTo: payrollreporttax
type: object
description: Construction payroll tax object used for reporting.
properties:
  key:
    type: string
    description: System-assigned payroll report tax record number(key).
    x-mappedTo: RECORDNO
    readOnly: true
    example: '1'
  id:
    type: string
    description: System-assigned identifier for the payroll report tax.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '1'
  classificationCode:
    type: string
    description: Specifies the classification code for the tax.
    x-mappedTo: CLASSIFICATIONCODE
    example: 'SG'
  taxCategoryId:
    type: string
    description: Specifies the category identifier for the tex.
    x-mappedTo: TAXCATEGORYID
    nullable: false
    example: 'PA-EIT'
  taxableEarnings:
    type: string
    format: decimal-precision-10
    description: Taxable earnings.
    x-mappedTo: TAXABLEEARNINGS
    example: '5000.00'
  taxedEarnings:
    type: string
    format: decimal-precision-10
    description: Taxed earnings.
    x-mappedTo: TAXEDEARNINGS
    example: '2000.00'
  employeeTaxedEarnings:
    type: string
    format: decimal-precision-10
    description: Employee taxed earnings.
    x-mappedTo: EMPLOYEETAXEDEARNINGS
    example: '8000.00'
  employerTaxedEarnings:
    type: string
    format: decimal-precision-10
    description: Employer taxed earnings.
    x-mappedTo: EMPLOYERTAXEDEARNINGS
    example: '7000.00'
  surtaxEarnings:
    type: string
    format: decimal-precision-10
    description: Surtax earnings.
    x-mappedTo: SURTAXEARNINGS
    example: '65000.00'
  deductionTaken:
    type: string
    format: decimal-precision-10
    description: Deduction taken.
    x-mappedTo: DEDUCTIONTAKEN
    example: '56000.00'
  taxableHours:
    type: string
    format: decimal-precision-10
    description: Taxable hours.
    x-mappedTo: TAXABLEHOURS
    example: '40.00'
  taxedHours:
    type: string
    format: decimal-precision-10
    description: Taxed hours.
    x-mappedTo: TAXEDHOURS
    example: '40.00'
  employeeTaxAmount:
    type: string
    format: decimal-precision-10
    description: Employee tax amount.
    x-mappedTo: EMPLOYEETAXAMOUNT
    nullable: false
    example: '45000.00'
  employerTaxAmount:
    type: string
    format: decimal-precision-10
    description: Employer tax amount.
    x-mappedTo: EMPLOYERTAXAMOUNT
    nullable: false
    example: '15000.00'
  employeeSurtaxAmount:
    type: string
    format: decimal-precision-10
    description: Employee surtax amount.
    x-mappedTo: EMPLOYEESURTAXAMOUNT
    example: '57000.00'
  employee:
    $ref: ../../common/references/employee-ref.s1.schema.yaml
  payrollReportCheck:
    $ref: ../references/payroll-report-check-ref.s1.schema.yaml
  payrollReportTimecard:
    type: object
    description: Timecard associated with the payroll report tax.
    allOf:
      - $ref: ../references/payroll-report-timecard-ref.s1.schema.yaml
      - type: object
        properties: 
          timeCardId:
            type: string
            description: Identifier assigned for the payroll report timecard.
            x-mappedTo: TIMECARDID
            nullable: false
            example: 'Punch'
        required:
          - timeCardId
  payrollReportTaxSetup:
    type: object
    x-mappedTo: payrollreporttaxsetup
    x-object: construction/payroll-report-tax-setup
    description: Reference to the payroll report tax setup.
    properties:
      key:
        type: string
        description: System-assigned payroll report tax setup record number (key).
        x-mappedTo: PAYROLLREPORTTAXSETUPKEY
        example: '1'
      id:
        type: string
        description: User-defined identifier assigned for the Payroll report tax setup.
        x-mappedTo: TAXID
        example: 'IL-SIT'
      href:
        type: string
        description: URL endpoint of the payroll report tax setup record.
        example: /objects/construction/payroll-report-tax-setup/1
        readOnly: true
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml