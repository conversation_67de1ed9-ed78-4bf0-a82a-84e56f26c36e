title: construction-payroll-report-tax-setup
x-mappedTo: payrollreporttaxsetup
type: object
description: Construction payroll tax setup object used for categorizing payroll report taxes without the need for updating all check tax entries if a change is made.
properties:
  key:
    type: string
    description: System-assigned unique key for the payroll-report-tax-setup.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: User-defined identifier assigned for the Payroll report tax setup.
    x-mutable: false
    x-mappedTo: TAXID
    example: 'IL-SIT'
  href:
    type: string
    description: URL endpoint for the payroll report tax setup.
    readOnly: true
    example: /objects/construction/payroll-report-tax-setup/23
  taxType:
    type: string
    description: Tax calculation method used for this tax setup.
    x-mappedTo: TAXTYPE
    example: 'PercentOfEarnings'
  reportingLevel:
    type: string
    description: Tax reporting levels used for this tax setup.
    x-mappedTo: REPORTINGLEVEL
    example: 'federal'
    nullable: true
    default: null
    enum:
      - null
      - 'federal'
      - 'state'
      - 'local'
    x-mappedToValues:
      - ''
      - 'Federal'
      - 'State'
      - 'Local'
  taxCategoryId:
    type: string
    description: Additional tax categorization used to note taxes that follow specific rules for this tax setup.
    x-mappedTo: TAXCATEGORYID
    example: 'SUI'
  reportAs:
    type: string
    description: Facilitate certified reporting for this tax setup.
    x-mappedTo: REPORTAS
    example: 'Social Security'
  state:
    type: string
    description: Indicates the Territory, it can be a state or province for this tax setup.
    x-mappedTo: STATE
    example: 'NY'
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml