uiLabel: IA.CHANGE_REQUESTS
fields:
  description:
    uiType: multitext
    uiLabel: IA.DESCRIPTION
  exclusions:
    uiType: multitext
    uiLabel: IA.EXCLUSIONS
  id:
    uiType: text
    uiLabel: IA.CHANGE_REQUEST_ID
  inclusions:
    uiType: multitext
    uiLabel: IA.INCLUSIONS
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  priceEffectiveDate:
    uiType: date
    uiLabel: IA.PRICE_EFFECTIVE_DATE
  changeRequestDate:
    uiType: date
    uiLabel: IA.DATE
  scope:
    uiType: multitext
    uiLabel: IA.SCOPE
  changeRequestState:
    uiType: enum
    uiLabel: IA.STATE
    enumsLabels:
      - label: IA.DRAFT
        value: draft
      - label: IA.POSTED
        value: posted
  terms:
    uiType: multitext
    uiLabel: IA.TERMS
  totalCost:
    uiType: currency
    uiLabel: IA.TOTAL_COST
  totalPrice:
    uiType: currency
    uiLabel: IA.TOTAL_PRICE
  projectContractLineSource:
    uiType: enum
    uiLabel: IA.PROJECT_CONTRACT_LINE_SOURCE
    enumsLabels:
      - label: IA.NONE
        value: none
      - label: IA.PROJECT_CHANGE_ORDER
        value: projectChangeOrder
      - label: IA.CHANGE_REQUEST
        value: changeRequest
      - label: IA.CHANGE_REQUEST_LINE
        value: changeRequestLine
groups:
  audit:
    fields:
      createdBy:
        uiType: text
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: text
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
  externalReference:
    fields:
      approvedOnDate:
        uiType: date
        uiLabel: IA.EXTERNAL_APPROVED_ON
      referenceNumber:
        uiType: text
        uiLabel: IA.EXTERNAL_REFERENCE_NO
      signedOnDate:
        uiType: date
        uiLabel: IA.EXTERNAL_SIGNED_ON
    refs:
      approvedBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.EXTERNAL_APPROVED_BY
          key:
            uiType: text
            uiLabel: IA.APPROVED_BY_KEY
      signedBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.EXTERNAL_SIGNED_BY
          key:
            uiType: text
            uiLabel: IA.SIGNED_BY_KEY
      verbalApprovalBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.EXTERNAL_VERBAL_BY
          key:
            uiType: text
            uiLabel: IA.VERBAL_BY_KEY
  internalReference:
    fields:
      approvedOnDate:
        uiType: date
        uiLabel: IA.INTERNAL_APPROVED_ON
      issuedOnDate:
        uiType: date
        uiLabel: IA.INTERNAL_ISSUED_ON
      referenceNumber:
        uiType: text
        uiLabel: IA.INTERNAL_REFERENCE_NO
      signedOnDate:
        uiType: date
        uiLabel: IA.INTERNAL_SIGNED_ON
      source:
        uiType: text
        uiLabel: IA.INTERNAL_SOURCE
      sourceReferenceNumber:
        uiType: text
        uiLabel: IA.INTERNAL_SOURCE_REFERENCE_NO
    refs:
      approvedBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.INTERNAL_APPROVED_BY
          key:
            uiType: text
            uiLabel: IA.APPROVED_BY_KEY
          name:
            uiType: text
            uiLabel: IA.INTERNAL_APPROVED_BY_NAME
      initiatedBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.INTERNAL_INITIATED_BY
          key:
            uiType: text
            uiLabel: IA.INITIATED_BY_KEY
          name:
            uiType: text
            uiLabel: IA.INTERNAL_INITIATED_BY_NAME
      issuedBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.INTERNAL_ISSUED_BY
          key:
            uiType: text
            uiLabel: IA.ISSUED_BY_KEY
          name:
            uiType: text
            uiLabel: IA.INTERNAL_ISSUED_BY_NAME
      signedBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.INTERNAL_SIGNED_BY
          key:
            uiType: text
            uiLabel: IA.SIGNED_BY_KEY
          name:
            uiType: text
            uiLabel: IA.INTERNAL_SIGNED_BY_NAME
      verbalApprovalBy:
        fields:
          id:
            uiType: ptr
            uiLabel: IA.INTERNAL_VERBAL_BY
          key:
            uiType: text
            uiLabel: IA.VERBAL_BY_KEY
          name:
            uiType: text
            uiLabel: IA.INTERNAL_VERBAL_BY_NAME
  schedule:
    fields:
      actualCompletionDate:
        uiType: date
        uiLabel: IA.ACTUAL_COMPLETION_DATE
      actualStartDate:
        uiType: date
        uiLabel: IA.ACTUAL_START_DATE
      executedOnDate:
        uiType: date
        uiLabel: IA.EXECUTED_ON
      noticeToProceedDate:
        uiType: date
        uiLabel: IA.NOTICE_TO_PROCEED
      responseDueDate:
        uiType: date
        uiLabel: IA.RESPONSE_DUE
      revisedCompletionDate:
        uiType: date
        uiLabel: IA.REVISED_COMPLETION_DATE
      scheduleImpact:
        uiType: text
        uiLabel: IA.SCHEDULE_IMPACT
      scheduledCompletionDate:
        uiType: date
        uiLabel: IA.SCHEDULED_COMPLETION_DATE
      scheduledStartDate:
        uiType: date
        uiLabel: IA.SCHEDULED_START_DATE
      substantialCompletionDate:
        uiType: date
        uiLabel: IA.SUBSTANTIAL_COMPLETION_DATE
  entity:
    uiType: text
    uiLabel: IA.ENTITY
refs:
  attachment:
    fields:
      key:
        uiType: supdocptr
        uiLabel: IA.ATTACHMENT_KEY
      id:
        uiType: supdocptr
        uiLabel: IA.ATTACHMENT_ID
  changeRequestStatus:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.CHANGE_REQUEST_STATUS
      key:
        uiType: integer
        uiLabel: IA.CHANGE_REQUEST_STATUS_KEY
  projectCustomer:
    fields:
      id:
        uiType: text
        uiLabel: IA.CUSTOMER
      key:
        uiType: integer
        uiLabel: IA.CUSTOMER_KEY
      name:
        uiType: text
        uiLabel: IA.CUSTOMER_NAME
  location:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.LOCATION_ID
      key:
        uiType: integer
        uiLabel: IA.LOCATION_KEY
      name:
        uiType: text
        uiLabel: IA.LOCATION_NAME
  project:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.PROJECT_ID
      key:
        uiType: integer
        uiLabel: IA.PROJECT_KEY
      name:
        uiType: text
        uiLabel: IA.PROJECT_NAME
  changeRequestType:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.CHANGE_REQUEST_TYPE
      key:
        uiType: integer
        uiLabel: IA.CHANGE_REQUEST_TYPE_KEY
  projectChangeOrder:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.PROJECT_CHANGE_ORDER_ID
      key:
        uiType: integer
        uiLabel: IA.PROJECT_CHANGE_ORDER_KEY
  projectContract:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.PROJECT_CONTRACT_ID
      key:
        uiType: integer
        uiLabel: IA.PROJECT_CONTRACT_KEY
      name:
        uiType: text
        uiLabel: IA.PROJECT_CONTRACT_NAME
  projectContractLine:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.PROJECT_CONTRACT_LINE_ID
      key:
        uiType: integer
        uiLabel: IA.PROJECT_CONTRACT_LINE_KEY
      name:
        uiType: text
        uiLabel: IA.PROJECT_CONTRACT_LINE_NAME
  entity:
    fields:
      id:
        uiType: text
        uiLabel: IA.ENTITY_ID
      key:
        uiType: integer
        uiLabel: IA.ENTITY_KEY