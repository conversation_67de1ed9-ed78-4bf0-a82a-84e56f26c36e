title: custom-field-sequence-properties
type: object
description: Data type for a custom field with `fieldType` set to `sequence`, which represents a document sequence.
properties:
  sequence:
    type: object
    x-object: company-config/document-sequence
    x-mappedTo: seqnum
    properties:
      key:
        type: string
        description: System-assigned key for the document sequence.
        x-mappedTo: SEQUENCE.RECORDNO
        example: '16'
      id:
        type: string
        description: Unique ID for the document sequence.
        x-mappedTo: SEQUENCE.TITLE
        example: Vendors
      href:
        type: string
        description: Endpoint URL for the document sequence.
        readOnly: true
        example: /objects/company-config/document-sequence/39
