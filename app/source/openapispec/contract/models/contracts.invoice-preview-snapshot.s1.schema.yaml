title: invoice-preview-snapshot
x-mappedTo: geninvoiceprebillheader
type: object
description: Information for Contract invoice preview snapshot.
allOf:
  - $ref: contracts.invoice.s1.schema.yaml
  - type: object
    properties:
      key:
        type: string
        description: System-assigned key for the invoice-preview-snapshot.
        readOnly: true
        x-mappedTo: RECORDNO
        example: '23'
      id:
        type: string
        description: System-assigned key for the invoice-preview-snapshot.
        readOnly: true
        x-mappedTo: RECORDNO
        example: '23'
      href:
        type: string
        description: URL endpoint for the invoice-preview-snapshot.
        readOnly: true
        example: /objects/contracts/invoice-preview-snapshot/23
      invoiceMessage:
        type: string
        description: Invoice message.
        x-mappedTo: INVOICEMSG
        readOnly: true
        nullable: true
        example: '03/24 Invoice'
      invoicePreviewSnapshotSummary:
        type: object
        x-mappedTo: geninvoiceprebill
        x-object: contracts/invoice-preview-snapshot-summary
        readOnly: true
        properties:
          href:
            type: string
            description: URL endpoint for the invoice preview snapshot summary.
            readOnly: true
            example: /objects/contracts/invoice-preview-snapshot-summary/23
          key:
            type: string
            x-mappedTo: PREBILLKEY
            readOnly: true
            example: '23'
          id:
            type: string
            description: Name of the invoice-preview-snapshot summary.
            x-mappedTo: NAME
            example: Preview Snapost 3-4-2024
          documentType:
            type: string
            x-mappedTo: DOCPARID
            readOnly: true
            example: Contract Invoice
      audit:
        $ref: ../../common/models/audit.s1.schema.yaml