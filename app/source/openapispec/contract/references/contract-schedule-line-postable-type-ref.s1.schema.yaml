type: object
title: contract-schedule-line-postable
required:
  - scheduledPostingDate
properties:
  scheduledPostingDate:
    type: string
    description: Scheduled posting date.
    x-mappedTo: POSTINGDATE
    format: date
    example: '2022-04-30'
    nullable: false
  posted:
    type: boolean
    description: Indicates if the line has been posted.
    x-mappedTo: POSTED
    readOnly: true
    example: false
  actualPostingDate:
    type: string
    description: Actual posting date.
    x-mappedTo: ACTUALPOSTINGDATE
    readOnly: true
    format: date
    example: '2022-04-30'
  journalEntry:
    type: object
    x-object: general-ledger/journal-entry
    properties:
      key:
        type: string
        description: Journal entry key.
        x-mappedTo: GLBATCHKEY
        readOnly: true
        example: '8153'
      id:
        type: string
        description: Journal entry ID.
        x-mappedTo: GLBATCHNO
        readOnly: true
        example: '4765'
      href:
        type: string
        description: Endpoint URL of the journal entry.
        readOnly: true
        example: /objects/general-ledger/journal-entry/1981
