openapi: 3.0.0
info:
  title: contracts-expense-schedule-line
  description: contracts.expense-schedule-line API
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: Contract expense schedule lines
    description: A contract expense schedule line represents a single expense for a contract line.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/contracts/expense-schedule-line:
    get:
      summary: List contract expense schedule lines
      description: 'Returns a collection with a key, ID, and link for each contract-expense-schedule-line.'
      tags:
        - Contract expense schedule lines
      operationId: list-contracts-expense-schedule-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of contract expense schedule line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of contract expense schedules:
                  value:
                    'ia::result':
                      - key: '84'
                        id: RT-00001
                        href: /objects/contracts/expense-schedule-line/84
                      - key: '85'
                        id: RT-00002
                        href: /objects/contracts/expense-schedule-line/85
                      - key: '60'
                        id: RT-00003
                        href: /objects/contracts/expense-schedule-line/60
                      - key: '78'
                        id: RT-00004
                        href: /objects/contracts/expense-schedule-line/78
                      - key: '79'
                        id: RT-00005
                        href: /objects/contracts/expense-schedule-line/79
                    'ia::meta':
                      totalCount: 5
                      start: 1
                      pageSize: 100
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../../common/models/error-response.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                List of contract expense schedule line errors:
                  value:
                    'ia::result':
                      'ia::error':
                        code: invalidRequest
                        message: 'Object named [contract-expense-schedule-line] is not supported in version [0]'
                        supportId: '-hOIM%7EYagYsDEpVbp0kVn1KUJ45gAAAAQ'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 0
                      totalError: 1
  /objects/contracts/expense-schedule-line/{key}:
    parameters:
      - name: key
        description: System-assigned key for the contract expense schedule line.
        in: path
        required: true
        schema:
          type: string
          example: '8241'
    get:
      summary: Get a contract expense schedule line
      description: Returns detailed information for a particular contract expense schedule line.
      tags:
        - Contract expense schedule lines
      operationId: get-contracts-expense-schedule-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the contract expense schedule line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/contracts-expense-schedule-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  key:
                    type: string
                  href:
                    type: string
                  'ia::result':
                    $ref: ../../common/models/error-response.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Update a contract expense schedule line error:
                  value:
                    key: '12'
                    href: /objects/contracts/expense-schedule-line/12
                    'ia::result':
                      'ia::error':
                        code: invalidRequest
                        message: 'Field [id] is a read-only field'
                        supportId: YCbdS%7EYagQGDEvVbn0UIY1o-JKOAAAAAo
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 0
                      totalError: 1
  /workflows/contracts/expense-schedule-line/post:
    post:
      summary: post expense schedule entries
      description: post expense schedule entries
      tags:
        - expense schedule lines
      operationId: post-contracts-expense-schedule-line
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/contracts.expense-schedule-line.s1.workflows.yaml#/contracts-expense-schedule-line-actions-post-request
            examples:
              post expense schedule entries:
                value:
                  key: '12958'
                  actualPostingDate: '03/31/2012'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../workflows/contracts.expense-schedule-line.s1.workflows.yaml#/contracts-expense-schedule-line-actions-post-response
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to posted expense schedule entries:
                  value:
                    'ia::result':
                      key: '12958'
                      state: posted
                      href: /objects/contracts/expense-schedule-line/12958
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /workflows/contracts/expense-schedule-line/unpost:
    post:
      summary: unpost expense schedule entries
      description: unpost expense schedule entries
      tags:
        - expense schedule lines
      operationId: unpost-contracts-expense-schedule-line
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/contracts.expense-schedule-line.s1.workflows.yaml#/contracts-expense-schedule-line-actions-unpost-request
            examples:
              unpost expense schedule entries:
                value:
                  key: '12958'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: ../workflows/contracts.expense-schedule-line.s1.workflows.yaml#/contracts-expense-schedule-line-actions-unpost-response
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to unpost expense schedule entries:
                  value:
                    'ia::result':
                      key: '12958'
                      state: open
                      href: /objects/contracts/expense-schedule-line/12958
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    contracts-expense-schedule-line:
      type: object
      allOf:
        - $ref: ../models/contracts.expense-schedule-line.s1.schema.yaml
        - $ref: ../references/contract-schedule-line-type-ref.s1.schema.yaml
        - $ref: ../references/contract-schedule-line-postable-type-ref.s1.schema.yaml
