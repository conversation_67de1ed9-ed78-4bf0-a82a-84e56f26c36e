openapi: 3.0.0
info:
  title: contracts-revenue-schedule
  description: contracts.revenue-schedule API
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: Revenue schedules
    description: |
      A contract line specifies dates, pricing, and a revenue template. The revenue template defines the rules for how to schedule the revenue. Sage Intacct then generates a revenue schedule for the contract line based on the defined criteria.

      A revenue schedule for a term contract shows when a contract line's deferred revenue is expected to be recognized during the contract line term. It shows the scheduled posting date and amount to be posted for each potential journal entry. For periods that have been posted, it shows the actual posting date, the exchange rate used (if applicable), and the amount posted.

      A revenue schedule for an evergreen contract line shows when the revenue associated with the corresponding recurring billing period is scheduled to be posted or was posted.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/contracts/revenue-schedule:
    get:
      summary: List contract revenue schedules
      description: Returns up to 100 object references from the collection with a key, ID, and link for each revenue schedule. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      tags:
        - Revenue schedules
      operationId: list-contracts-revenue-schedule
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of contract revenue schedule objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of contract revenue schedules:
                  value:
                    'ia::result':
                      - key: '84'
                        id: '84'
                        href: /objects/contracts/revenue-schedule/84
                      - key: '85'
                        id: '85'
                        href: /objects/contracts/revenue-schedule/85
                      - key: '60'
                        id: '60'
                        href: /objects/contracts/revenue-schedule/60
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/contracts/revenue-schedule/{key}:
    parameters:
      - name: key
        description: System-assigned key for the contract revenue schedule.
        in: path
        required: true
        schema:
          type: string
        example: '1287'
    get:
      summary: Get a contract revenue schedule
      description: Returns detailed information for a specified contract revenue schedule.
      tags:
        - Revenue schedules
      operationId: get-contracts-revenue-schedule-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the contract revenue schedule
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/contracts-revenue-schedule'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Contract revenue schedule:
                  value:
                    'ia::result':
                      key: '1578'
                      id: '1578'
                      journal: 'J1'
                      contract:
                        key: '143'
                        id: 'Committed_Quantity_Billing'
                        href: '/objects/contracts/contract/143'
                      contractLine:
                        id: '387'
                        key: '387'
                        href: '/objects/contracts/contract-line/387'
                      status: 'inProgress'
                      cancellationDate: null
                      audit:
                        createdDateTime: '2024-02-01T06:14:00Z'
                        modifiedDateTime: '2024-02-01T06:14:01Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      estimateRevaluationDate: null
                      revenueScheduleLines:
                        - key: '14423'
                          id: '14423'
                          contractRevenueSchedule:
                            id: '1578'
                            key: '1578'
                            href: '/objects/contracts/revenue-schedule/1578'
                          scheduledOperationKey: null
                          scheduledAmount: '16666.66'
                          scheduledBaseAmount: '16666.66'
                          scheduledExchangeRate: '1.000000000000'
                          scheduledPostingDate: '2024-01-31'
                          actualPostingDate: null
                          derivedPostingDate: '2024-01-31'
                          posted: false
                          isHistorical: false
                          status: 'open'
                          journalEntry:
                            key: null
                            id: null
                          computationMemo: null
                          meaDetails: null
                          adjustedFor: ''
                          approvedHours: null
                          sourceHours: null
                          percentageRecognized: null
                          audit:
                            createdDateTime: '2024-02-01T06:14:00Z'
                            modifiedDateTime: '2024-03-28T04:14:44Z'
                            createdBy: '1'
                            modifiedBy: null
                          linkedBillingScheduleLine:
                            id: null
                            key: null
                          href: '/objects/contracts/revenue-schedule-line/14423'
                        - key: '14424'
                          id: '14424'
                          contractRevenueSchedule:
                            id: '1578'
                            key: '1578'
                            href: '/objects/contracts/revenue-schedule/1578'
                          scheduledOperationKey: null
                          scheduledAmount: '16666.66'
                          scheduledBaseAmount: '16666.66'
                          scheduledExchangeRate: '1.000000000000'
                          scheduledPostingDate: '2024-02-29'
                          actualPostingDate: null
                          derivedPostingDate: '2024-02-29'
                          posted: false
                          isHistorical: false
                          status: 'open'
                          journalEntry:
                            key: null
                            id: null
                          computationMemo: null
                          meaDetails: null
                          adjustedFor: ''
                          approvedHours: null
                          sourceHours: null
                          percentageRecognized: null
                          audit:
                            createdDateTime: '2024-02-01T06:14:00Z'
                            modifiedDateTime: '2024-03-28T04:14:44Z'
                            createdBy: '1'
                            modifiedBy: null
                          linkedBillingScheduleLine:
                            id: null
                            key: null
                          href: '/objects/contracts/revenue-schedule-line/14424'
                      href: '/objects/contract-revenue-schedule/1578'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a contract revenue schedule
      description: Updates an existing contract revenue schedule by setting field values. Any fields not provided remain unchanged.
      tags:
        - Revenue schedules
      operationId: update-contracts-revenue-schedule-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/contracts-revenue-schedule'
            examples:
              Update a revenue schedule line:
                value:
                  key: '145'
                  revenueScheduleLines:
                    - key: '12'
                      computationMemo: 'Memo'
                      scheduledAmount: '125.34'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated contract revenue schedule
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to thee updated contract revenue schedule:
                  value:
                    'ia::result':
                      key: '145'
                      id: '145'
                      href: '/objects/contracts/revenue-schedule/145'
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    contracts-revenue-schedule:
      type: object
      allOf:
        - $ref: ../models/contracts.revenue-schedule.s1.schema.yaml
        - $ref: ../references/contract-schedule-type-ref.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
