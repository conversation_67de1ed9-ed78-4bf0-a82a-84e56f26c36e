title: accounts-receivable-account-label
x-mappedTo: araccountlabel
type: object
description: AR account labels provide more descriptive names for accounts.
properties:
  key:
    type: string
    description: System-assigned key for the AR account label.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '8'
  id:
    type: string
    description: Name or other unique identifier for the account label. This unique identifier cannot be modified.
    x-mappedTo: ACCOUNTLABEL
    x-mutable: false
    example: Software
  href:
    type: string
    description: Endpoint for the AR account label.
    readOnly: true
    example: /objects/accounts-receivable/account-label/23
  description:
    type: string
    description: A note about the purpose and use of the account label.
    x-mappedTo: DESCRIPTION
    example: Software sales
  isTaxable:
    type: boolean
    description: Set to true to enable Advanced Tax to calculate the sales tax automatically.
    x-mappedTo: TAXABLE
    example: false
    x-mappedToType: string
    default: false
  isSubtotal:
    type: boolean
    description: Set to true if the account label is for a subtotal type. This field is applicable only for entities that use the Avalara AvaTax solution. By default, Accounts Receivable is automatically configured to display tax and subtotal fields for invoices.
    x-mappedTo: SUBTOTAL
    example: false
    x-mappedToType: string
    default: false
  isTax:
    type: boolean
    description: Set to true only if the account label is for a subtotal and the subtotal is for a tax, such as sales tax or excise tax. Do not enable for other subtotals, such as discounts, shipping, or handling charges. This field is applicable only for entities that use the Avalara AvaTax.
    x-mappedTo: ISTAX
    example: true
    x-mappedToType: string
    default: false
  taxGroup:
    type: object
    description: Tax group to which the label is assigned. Account label tax groups organize taxable account labels under one tax category. Relevant only for companies configured to use Advanced Tax, and only if this account label is not a subtotal.
    x-mappedTo: TAXGROUP
    x-object: tax/account-label-tax-group
    properties:
      key:
        type: string
        description: System-assigned key for the tax group.
        x-mappedTo: TAXGROUPKEY
        nullable: true
        example: '23'
      id:
        type: string
        description: Unique identifier for the tax group.
        x-mappedTo: TAXGROUP.NAME
        nullable: true
        example: Goods Exempt Rate - CA
      href:
        type: string
        description: Endpoint URL for the tax group.
        readOnly: true
        example: /objects/tax/account-label-tax-group/23
  taxCode:
    type: string
    description: AR account labels Tax Code.
    nullable: true
    x-mappedTo: TAXCODE
    example: CST
  offsetGLAccount:
    type: object
    description: The general ledger account where the system posts offsets to items posted to this label. This is typically a receivables account.
    x-object: general-ledger/account
    x-mappedTo: OFFSETGLACCOUNT
    properties:
      key:
        type: string
        description: System-assigned key for the offset general ledger account.
        x-mappedTo: OFFSETGLACCOUNTRECORDNO
        nullable: true
        example: '2'
      id:
        type: string
        description: Account number for the offset general ledger account.
        x-mappedTo: OFFSETGLACCOUNTNO
        nullable: true
        example: '1215--EquipmentOffset'
      href:
        type: string
        readOnly: true
        example: /objects/general-ledger/account/2
  glAccount:
    type: object
    description: General ledger account this AR account label is assigned to.
    x-object: general-ledger/account
    x-mappedTo: GLACCOUNT
    properties:
      href:
        type: string
        description: Endpoint for the glaccount.
        readOnly: true
        example: /objects/general-ledger/account/356
      id:
        type: string
        description: General ledger account number.
        x-mappedTo: GLACCOUNTNO
        example: '1501'
      key:
        type: string
        description: System-assigned key for the general ledger account.
        x-mappedTo: GLACCOUNTRECORDNO
        example: '356'
  revenueRecognitionTemplate:
    type: object
    x-mappedTo: rrtemplate
    x-object: accounts-receivable/revenue-recognition-template
    properties:
      key:
        type: string
        description: System-assigned key for the revenue recognition template.
        x-mappedTo: REVRECTEMPLETERECORDNO
        nullable: true
        example: '1'
      id:
        type: string
        description: Unique identifier for the revenue recognition template.
        x-mappedTo: REVRECTEMPLTITLE
        nullable: true
        example: Straight Line
      href:
        type: string
        description: URL for the revenue recognition template.
        example: /objects/accounts-receivable/revenue-recognition-template/1
        readOnly: true
  deferredRevenueGLAccount:
    type: object
    x-mappedTo: defrrglaccount
    x-object: general-ledger/account
    properties:
      key:
        type: string
        description: System-assigned key for the revenue gl account.
        x-mappedTo: DEFERREDREVACCTRECORDNO
        nullable: true
        example: '1'
      id:
        type: string
        description: Unique identifier for the revenue gl account.
        x-mappedTo: DEFRRACCOUNTNO
        nullable: true
        example: '1001'
      href:
        type: string
        description: URL for the deffer revenue gl account template.
        example: /objects/general-ledger/account/356
        readOnly: true
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml
