title: accounts-receivable-amortization-template
x-mappedTo: aramortizationtemplate
type: object
description: A predefined pattern used as an aid for setting up amortizations.
properties:
  key:
    type: string
    description: System-assigned key for the amortization-template.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Amortization Template identification.
    x-mappedTo: TEMPLATEID
    x-mutable: false
    example: AR Monthly 12
  href:
    type: string
    description: Endpoint for the amortization-template.
    readOnly: true
    example: /objects/accounts-receivable/amortization-template/23
  description:
    type: string
    description: Description of the unique template.
    x-mappedTo: DESCRIPTION
    example: Template for 12 month amortizations.
  templateType:
    type: string
    description: System assigned amortization type.
    readOnly: true
    x-mappedTo: TEMPLATETYPE
    example: pca
    enum:
      - 'pca'
    x-mappedToValues:
      - 'Produits constatés d’avance'
    default: pca
  term:
    type: string
    description: Schedule period.
    x-mappedTo: TERM
    example: monthly
    default: monthly
    enum:
      - monthly
      - quarterly
      - annually
    x-mappedToValues:
      - Monthly
      - Quarterly
      - Annually
  location:
    type: object
    description: Location to use for GL posting (optional).
    readOnly: true
    x-mappedTo: location
    x-object: company-config/location
    properties:
      key:
        type: string
        nullable: true
        description: System-assigned key for the location.
        x-mappedTo: LOCATIONKEY
        example: '123'
      id:
        type: string
        nullable: true
        description: Location designation
        x-mappedTo: LOCATIONID
        example: San Jose Office
      href:
        type: string
        description: URL for the location.
        readOnly: true
        example: /objects/company-config/location/123
  glAccount:
    type: object
    description: The general ledger account where the amortization is applied.
    x-mappedTo: account
    x-object: general-ledger/account
    properties:
      key:
        type: string
        description: key
        x-mappedTo: ACCOUNTKEY
        example: '456'
      id:
        type: string
        description: Account.
        x-mappedTo: ACCOUNTID
        example: 1000--Cash in Bank
      href:
        type: string
        description: Endpoint for the account.
        readOnly: true
        example: /objects/account/456
  glJournal:
    type: object
    description: The journal where the amortization activity is recorded.
    x-mappedTo: journal
    x-object: general-ledger/journal
    properties:
      key:
        type: string
        description: key
        x-mappedTo: JOURNALKEY
        example: '789'
      id:
        type: string
        description: Journal.
        x-mappedTo: JOURNALID
        example: POJ
      href:
        type: string
        description: Endpoint for the journal.
        readOnly: true
        example: /objects/journal/789
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
