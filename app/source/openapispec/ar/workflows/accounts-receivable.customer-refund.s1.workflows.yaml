info:
  title: accounts-receivable-customer-refund-workflows
  description: Workflows for customer refund. Actions to manage state of a customer refund.
accounts-receivable-customer-refund-actions-reverse-request:
  type: object
  x-mappedTo: customerrefund
  properties:
    key:
      type: string
      description: System-assigned key for customer refund.
      x-mappedTo: RECORDNO
      example: '22'
    reversedDate:
      type: string
      format: date
      example: '2024-04-15'
      description: Date this transactions is reversed.
      x-mappedTo: VOIDDATE
    description:
      type: string
      description: Notes or comments about the reason for the reverse of the customer refund.
      x-mappedTo: DESCRIPTION
      example: Reversed the refund for duplicate entry
  required:
    - key
    - reversedDate
accounts-receivable-customer-refund-actions-reverse-response:
  type: object
  x-mappedTo: customerrefund
  properties:
    key:
      type: string
      description: System-assigned unique record key for the new customer refund.
      x-mappedTo: RECORDNO
      example: '23'
    id:
      type: string
      x-mappedTo: RECORDID
      description: Unique identifier for the new customer refund.
      example: 'REF-01'
    href:
      type: string
      description: URL endpoint for customer refund.
      example: /objects/accounts-receivable/customer-refund/23
    state:
      type: string
      description: Customer refund state after reverse
      x-mappedTo: STATE
      example: voided
accounts-receivable-customer-refund-actions-submit-request:
  type: object
  x-mappedTo: customerrefund
  properties:
    key:
      type: string
      description: System-assigned key for the customer refund.
      x-mappedTo: RECORDNO
      example: '12'
  required:
    - key
accounts-receivable-customer-refund-actions-submit-response:
  type: object
  x-mappedTo: customerrefund
  properties:
    key:
      type: string
      description: System-assigned key for the customer refund.
      x-mappedTo: RECORDNO
      example: '12'
    id:
      type: string
      description: Unique identifier for the customer refund.
      x-mappedTo: RECORDID
      example: 'REF-02'
    href:
      type: string
      description: URL endpoint for this customer refund.
      example: /objects/accounts-receivable/customer-refund/12
    state:
      type: string
      description: State of the customer refund
      x-mappedTo: STATE
      enum:
        - posted
      x-mappedToValues:
        - Posted
      example: posted