info:
  title: accounts-receivable-payment-workflows
  description: Workflows for AR payment. Actions to manage state of a payment.
accounts-receivable-payment-actions-reverse-request:
  type: object
  x-mappedTo: arpymt
  properties:
    key:
      type: string
      description: System-assigned key for AR payment.
      x-mappedTo: RECORDNO
      example: '22'
    reversedDate:
      type: string
      format: date
      example: '2024-04-15'
      description: Date this transactions is reversed.
      x-mappedTo: VOIDDATE
    memo:
      type: string
      description: Notes or comments about the reason for the reverse of the payment.
      x-mappedTo: DESCRIPTION
      example: Reversed the payment for duplicate entry
  required:
    - key
    - reversedDate
accounts-receivable-payment-actions-reverse-response:
  type: object
  x-mappedTo: arpymt
  properties:
    key:
      type: string
      description: System-assigned unique record key for the new AR payment.
      x-mappedTo: RECORDNO
      example: '23'
    id:
      type: string
      x-mappedTo: RECORDNO
      description: Unique identifier for the new AR payment.
      example: '23'
    href:
      type: string
      description: URL endpoint for AR payment.
      example: /objects/accounts-receivable/payment/23
    state:
      type: string
      description: AR payment state after reverse
      x-mappedTo: STATE
      example: reversal
accounts-receivable-payment-actions-submit-request:
  type: object
  x-mappedTo: arpymt
  properties:
    key:
      type: string
      description: System-assigned key for the AR payment.
      x-mappedTo: RECORDNO
      example: '12'
  required:
    - key
accounts-receivable-payment-actions-submit-response:
  type: object
  x-mappedTo: arpymt
  properties:
    key:
      type: string
      description: System-assigned key for the AR payment.
      x-mappedTo: RECORDNO
      example: '12'
    id:
      type: string
      description: Unique identifier for the AR payment.
      x-mappedTo: RECORDNO
      example: '12'
    href:
      type: string
      description: URL endpoint for this AR payment.
      example: /objects/accounts-receivable/payment/12
    state:
      type: string
      description: State of the payment entry
      x-mappedTo: STATE
      enum:
        - completed
      x-mappedToValues:
        - C
      example: completed