openapi: 3.0.0
info:
  title: accounts-receivable-customer-restricted-department
  description: accounts-receivable.customer-restricted-department API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Customer restricted departments
    description: In shared multi-entity companies, customers can be restricted to specific departments or department groups to prevent the customer from being used in the wrong entity. Create and maintain restricted departments from the owning [customer object](accounts-receivable.customer).
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-receivable/customer-restricted-department:
    get:
      summary: List customer restricted departments
      description: Returns a collection with a key, ID, and link for each customer restricted department. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      tags:
        - Customer restricted departments
      operationId: list-accounts-receivable-customer-restricted-department
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of customer-restricted-department objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List customer restricted departments:
                  value:
                    ia::result:
                      - key: '76'
                        id: '76'
                        href: /objects/accounts-receivable/customer-restricted-department/76
                      - key: '77'
                        id: '77'
                        href: /objects/accounts-receivable/customer-restricted-department/77
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
  '/objects/accounts-receivable/customer-restricted-department/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the customer restricted department.
        in: path
        required: true
        example: '77'
        schema:
          type: string
    get:
      summary: Get a customer restricted department
      description: Returns detailed information for a specified customer restricted department.
      tags:
        - Customer restricted departments
      operationId: get-accounts-receivable-customer-restricted-department-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the customer-restricted-department
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-receivable-customer-restricted-department'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a customer restricted department:
                  value:
                    ia::result:
                      key: '77'
                      objectType: CUSTOMER
                      id: '77'
                      department:
                        key: '24'
                        id: DES
                        href: /objects/company-config/department/24
                      departmentGroup:
                        key:
                        id:
                      customer:
                        key: '330'
                        id: D10
                        href: /objects/accounts-receivable/customer/142
                      href: /objects/accounts-receivable/customer-restricted-department/77
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-receivable-customer-restricted-department:
      $ref: ../models/accounts-receivable.customer-restricted-department.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml