openapi: 3.0.0
info:
  title: accounts-receivable-tax-entry
  description: accounts-receivable.recurring-invoice-tax-entry API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Recurring invoice tax entries
    description: For VAT enabled transactions, recurring invoice line items will have tax entries. Create and maintain recurring invoice tax entries from the owning [recurring invoice object](accounts-receivable.recurring-invoice).
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-receivable/recurring-invoice-tax-entry:
    get:
      summary: List recurring invoice tax entries
      description: Returns a collection with a key, ID, and link for each recurring invoice tax entry.
      tags:
        - Recurring invoice tax entries
      operationId: list-accounts-receivable-recurring-invoice-tax-entry
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of recurring invoice tax entry objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List recurring invoice tax entries:
                  value:
                    'ia::result':
                      - key: '8'
                        id: '8'
                        href: /objects/accounts-receivable/recurring-invoice-tax-entry/8
                      - key: '9'
                        id: '9'
                        href: /objects/accounts-receivable/recurring-invoice-tax-entry/9
                      - key: '10'
                        id: '10'
                        href: /objects/accounts-receivable/recurring-invoice-tax-entry/10
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/accounts-receivable/recurring-invoice-tax-entry/{key}':
    parameters:
      - name: key
        description: System-assigned key for the recurring invoice tax entry.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a recurring invoice tax entry
      description: Returns detailed information for a specified recurring invoice tax entry.
      tags:
        - Recurring invoice tax entries
      operationId: get-accounts-receivable-recurring-invoice-tax-entry-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the recurring invoice tax entry
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-receivable-recurring-invoice-tax-entry'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a recurring invoice tax entry:
                  value:
                    'ia::result':
                      id: '52'
                      key: '52'
                      recurringInvoiceLine:
                        id: '51'
                        key: '51'
                        href: /objects/accounts-receivable/recurring-invoice-line/51
                      baseTaxAmount: '40.00'
                      txnTaxAmount: '40.00'
                      taxRate: 10
                      orderEntryTaxDetail:
                        id: G1 Goods and Services Tax
                        key: '20'
                        href: /objects/tax/order-entry-tax-detail/20
                      href: /objects/accounts-receivable/recurring-invoice-tax-entry/52
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-receivable-recurring-invoice-tax-entry:
      $ref: ../models/accounts-receivable.recurring-invoice-tax-entry.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml