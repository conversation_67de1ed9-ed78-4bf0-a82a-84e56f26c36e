openapi: 3.0.0
info:
  title: accounts-receivable-territory-group
  description: accounts-receivable.territory-group API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Territory groups
    description: Group territories to generate reports that contain data for that group of territories.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-receivable/territory-group:
    get:
      summary: List territory groups
      description: Returns a collection with a key, ID, and link for each territory group.
      tags:
        - Territory groups
      operationId: list-accounts-receivable-territory-group
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of territory-group objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List territory groups:
                  value:
                    'ia::result':
                      - key: '8'
                        id: TG-1
                        href: /objects/accounts-receivable/territory-group/8
                      - key: '9'
                        id: TG-2
                        href: /objects/accounts-receivable/territory-group/9
                    'ia::meta':
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a territory group
      description: Creates a new territory group.
      tags:
        - Territory groups
      operationId: create-accounts-receivable-territory-group
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-receivable-territory-group'
                - $ref: '#/components/schemas/accounts-receivable-territory-groupRequiredProperties'
            examples:
              Create a territory group:
                value:
                  id: TG-1
                  name: Territory Group NG-1
                  description: Territory group NG-1
                  members:
                    - territory:
                        id": TT-NG2
                    - territory:
                        id: TT-NG1
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New territory-group
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New territory group:
                  value:
                    'ia::result':
                      key: '8'
                      id: TG-1
                      href: /objects/accounts-receivable/territory-group/8
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/accounts-receivable/territory-group/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the territory group.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a territory group
      description: Returns detailed information for a specified territory group.
      tags:
        - Territory groups
      operationId: get-accounts-receivable-territory-group-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the territory-group
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-receivable-territory-group'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a territory group:
                  value:
                    'ia::result':
                      key: '9'
                      id: TG-2
                      name: Territory Group NG-3 Updated
                      description: This description is updated via Nextgen API
                      audit:
                        createdDateTime: '2023-09-18T10:53:14Z'
                        modifiedDateTime: '2023-09-18T15:53:36Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      members:
                        - id: '30'
                          key: '30'
                          territoryGroup:
                            key: '9'
                            id: TG-2
                            href: /objects/accounts-receivable/territory-group/9
                          territory:
                            key: '11'
                            id: '6345'
                            name: South Central
                            href: /objects/accounts-receivable/territory/11
                          sortOrder: '0'
                          audit:
                            createdDateTime: '2023-09-18T15:53:36Z'
                            modifiedDateTime: '2023-09-18T15:53:36Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          href: /objects/accounts-receivable/territory-group-member/30
                      href: /objects/accounts-receivable/territory-group/9
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a territory group
      description: Updates an existing territory group by setting field values. Any fields not provided remain unchanged.
      tags:
        - Territory groups
      operationId: update-accounts-receivable-territory-group-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/accounts-receivable-territory-group'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a territory group:
                value:
                  name: Territory Group NG-3 Updated
                  description: This description is updated via Nextgen API
                  members:
                    - ia::operation: delete
                      id: '23'
                    - ia::operation": delete
                      id: '24'
                    - territory:
                        id: '6345'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated territory-group
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated territory group:
                  value:
                    'ia::result':
                      key: '9'
                      id: TG-2
                      href: /objects/accounts-receivable/territory-group/9
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a territory group
      description: Deletes a territory group.
      tags:
        - Territory groups
      operationId: delete-accounts-receivable-territory-group-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-receivable-territory-group:
      $ref: ../models/accounts-receivable.territory-group.s1.schema.yaml
    accounts-receivable-territory-groupRequiredProperties:
      type: object
      required:
        - id
        - name
        - members
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
