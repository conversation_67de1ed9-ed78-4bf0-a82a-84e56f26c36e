fields:
  description:
    uiType: multitext
    uiLabel: IA.TEMPLATE_DESCRIPTION
  id:
    uiType: text
    uiLabel: IA.TEMPLATE_ID
  key:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  latestVersion:
    uiType: decimal
    uiLabel: IA.LATEST_VERSION_KEY
  milestoneSource:
    uiType: radio
    uiLabel: IA.PERCENT_OR_MILESTONE_SOURCE
    enumsLabels:
      -
        label: IA.USER_SPECIFIED
        value: manual
      -
        label: IA.PROJECT_ACCOUNTING
        value: project
  postingDay:
    uiType: enum
    uiLabel: IA.POSTING_DAY
    enumsLabels:
      -
        label: '1'
        value: '1'
      -
        label: '2'
        value: '2'
      -
        label: '3'
        value: '3'
      -
        label: '4'
        value: '4'
      -
        label: '5'
        value: '5'
      -
        label: '6'
        value: '6'
      -
        label: '7'
        value: '7'
      -
        label: '8'
        value: '8'
      -
        label: '9'
        value: '9'
      -
        label: '10'
        value: '10'
      -
        label: '11'
        value: '11'
      -
        label: '12'
        value: '12'
      -
        label: '13'
        value: '13'
      -
        label: '14'
        value: '14'
      -
        label: '15'
        value: '15'
      -
        label: '16'
        value: '16'
      -
        label: '17'
        value: '17'
      -
        label: '18'
        value: '18'
      -
        label: '19'
        value: '19'
      -
        label: '20'
        value: '20'
      -
        label: '21'
        value: '21'
      -
        label: '22'
        value: '22'
      -
        label: '23'
        value: '23'
      -
        label: '24'
        value: '24'
      -
        label: '25'
        value: '25'
      -
        label: '26'
        value: '26'
      -
        label: '27'
        value: '27'
      -
        label: '28'
        value: '28'
      -
        label: '29'
        value: '29'
      -
        label: '30'
        value: '30'
      -
        label: '31'
        value: '31'
      -
        label: IA.END_OF_PERIOD
        value: endOfPeriod
      -
        label: IA.DAILY
        value: daily
  postingMethod:
    uiType: radio
    uiLabel: IA.POSTING_METHOD
    enumsLabels:
      -
        label: IA.AUTOMATIC
        value: automatic
      -
        label: IA.MANUAL
        value: manual
  recognitionMethod:
    uiType: enum
    uiLabel: IA.RECOGNITION_METHOD
    enumsLabels:
      -
        label: IA.STRAIGHT_LINE_1
        value: straightLine
      -
        label: IA.STRAIGHT_LINE_PRORATE_EXACT_DAYS
        value: 'straightLine,prorateExactDays'
      -
        label: IA.STRAIGHT_LINE_PERCENT_ALLOCATION
        value: 'straightLine,percentAllocation'
      -
        label: IA.STRAIGHT_LINE_PERCENT_ALLOCATION_END_OF_PERIOD
        value: 'straightLine,percentAllocation,endOfPeriod'
      -
        label: IA.EXACT_DAYS_PER_PERIOD_PRORATE_DAYS
        value: 'exactDaysPerPeriod,prorateDays'
      -
        label: IA.EXACT_DAYS_PER_PERIOD_PRORATE_DAYS_END_OF
        value: 'exactDaysPerPeriod,prorateDays,endOfPeriod'
      -
        label: IA.PERCENT_COMPLETED
        value: percentCompleted
      -
        label: IA.MILESTONE
        value: milestone
      -
        label: IA.CUSTOM
        value: custom
  recognitionStartDate:
    uiType: enum
    uiLabel: IA.RECOGNITION_START_DATE
    enumsLabels:
      -
        label: IA.TRANSACTION_DATE
        value: transactionDate
      -
        label: IA.USER_SPECIFIED
        value: userSpecified
  recognitionTerm:
    uiType: radio
    uiLabel: IA.RECOGNITION_TERM
    enumsLabels:
      -
        label: IA.FIXED_PERIOD
        value: fixedPeriod
      -
        label: IA.CONTRACT_TERM
        value: contractTerm
      -
        label: IA.PROJECT
        value: project
  resumeOption:
    uiType: radio
    uiLabel: IA.SYSTEM_RESUME_OPTION
    enumsLabels:
      -
        label: IA.CATCH_UP
        value: catchUp
      -
        label: IA.WALKFORWARD
        value: walkforward
  schedulePeriod:
    uiType: enum
    uiLabel: IA.RECOGNITION_SCHEDULE_PERIOD
    enumsLabels:
      -
        label: IA.DAILY
        value: daily
      -
        label: IA.MONTHLY
        value: monthly
      -
        label: IA.QUARTERLY
        value: quarterly
      -
        label: IA.SEMI_ANNUALLY
        value: semiAnnually
      -
        label: IA.ANNUALLY
        value: annually
  status:
    uiType: enum
    uiLabel: IA.STATUS
    enumsLabels:
      -
        label: IA.ACTIVE
        value: active
      -
        label: IA.INACTIVE
        value: inactive
  totalPeriods:
    uiType: integer
    uiLabel: IA.NUMBER_OF_PERIODS
  useStandard:
    uiType: enum
    uiLabel: IA.USE_STANDARD_CALENDAR_AMORTIZATION
    enumsLabels:
      -
        label: IA.TRUE
        value: true
      -
        label: IA.FALSE
        value: false
groups:
  audit:
    fields:
      createdBy:
        uiType: ptr
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: ptr
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
  calculation:
    fields:
      basedOn:
        uiType: enum
        uiLabel: IA.BASED_ON
        enumsLabels:
          -
            label: IA.ESTIMATED_HOURS
            value: estimatedHours
          -
            label: IA.PLANNED_HOURS
            value: plannedHours
          -
            label: IA.BUDGETED_HOURS
            value: budgetedHours
          -
            label: IA.BUDGETED_COST_FROM_GL
            value: budgetedCostFromGL
          -
            label: IA.BUDGETED_COST_FROM_SUMMARY
            value: budgetedCostFromSummary
          -
            label: IA.OBSERVED_PERCENT_COMPLETED
            value: observed%Completed
      source:
        uiType: enum
        uiLabel: IA.CALCULATE_ON
        enumsLabels:
          -
            label: IA.PROJECT
            value: project
          -
            label: IA.TASK
            value: task
refs:
  entity:
    fields:
      id:
        uiType: text
        uiLabel: IA.ENTITY_ID
      key:
        uiType: integer
        uiLabel: IA.ENTITY_KEY