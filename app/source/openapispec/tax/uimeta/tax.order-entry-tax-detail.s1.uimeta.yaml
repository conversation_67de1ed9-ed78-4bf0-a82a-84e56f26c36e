fields:
    key:
        uiType: integer
        uiLabel: IA.RECORD_ID
    id:
        uiType: text
        uiLabel: IA.DETAIL_ID
    status:
        uiType: enum
        uiLabel: IA.STATUS
        enumsLabels:
            -
                label: Active
                value: active
            -
                label: Inactive
                value: inactive
    taxUniqueId:
        uiType: text
        uiLabel: IA.TAX_UID
    taxRate:
        uiType: enum
        uiLabel: IA.TAXRATE
        enumsLabels:
            -
                label: Standard
                value: standard
            -
                label: Reduced
                value: reduced
            -
                label: Exempt
                value: exempt
            -
                label: Zero
                value: zero
            -
                label: Federal
                value: federal
            -
                label: Provincial
                value: provincial
    amountToTax:
        uiType: enum
        uiLabel: IA.INCLUDE_IN_TAXABLE_AMOUNT
        enumsLabels:
            -
                label: IA.FULL_AMOUNT
                value: fullAmount
            -
                label: IA.AMOUNT_WITHIN_RANGE
                value: amountWithinRange
    description:
        uiType: multitext
        uiLabel: IA.DESCRIPTION
    taxPercent:
        uiType: decimal
        uiLabel: IA.PERCENT
    reverseCharge:
        uiType: boolean
        uiLabel: IA.IS_REVERSE_CHARGE
        enumsLabels:
            -
                label: IA.TRUE
                value: true
            -
                label: IA.FALSE
                value: true
    useExpenseAccount:
        uiType: enum
        uiLabel: IA.ASSIGN_TAXES_TO_EXPENSE_ACCOUNT
        enumsLabels:
            -
                label: IA.TRUE
                value: true
            -
                label: IA.FALSE
                value: false
    isSystemGenerated:
        uiType: enum
        uiLabel: IA.SYSTEM_GENERATED_FIELD
        enumsLabels:
            -
                label: IA.TRUE
                value: true
            -
                label: IA.FALSE
                value: false
groups:
    taxLimit:
        fields:
            minTaxable:
                uiType: decimal
                uiLabel: IA.MINIMUM_TAXABLE_AMOUNT
            maxTaxable:
                uiType: decimal
                uiLabel: IA.MAXIMUM_TAXABLE_AMOUNT
            minTax:
                uiType: decimal
                uiLabel: IA.MINIMUM_TAX
            maxTax:
                uiType: decimal
                uiLabel: IA.MAXIMUM_TAX
refs:
    accountLabel:
        fields:
            id:
                uiType: ptr
                uiLabel: IA.ACCOUNT_LABEL
            key:
                uiType: sequence
                uiLabel: IA.ACCOUNT_LABEL_RECORD_NUMBER
    taxAuthority:
        fields:
            id:
                uiType: ptr
                uiLabel: IA.TAX_AUTHORITY
            key:
                uiType: sequence
                uiLabel: IA.TAX_AUTHORITY_RECORD_NUMBER
    salesGLAccount:
        fields:
            id:
                uiType: ptr
                uiLabel: IA.GL_ACCOUNT
            key:
                uiType: sequence
                uiLabel: IA.GL_ACCOUNT_RECORD_NUMBER
    taxSolution:
        fields:
            key:
                uiType: sequence
                uiLabel: IA.TAX_SOLUTION_RECORD_NUMBER
            id:
                uiType: ptr
                uiLabel: IA.TAX_SOLUTION
