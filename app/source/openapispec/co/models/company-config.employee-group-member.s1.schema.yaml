title: company-config-employee-group-member
x-mappedTo: employeegrpmember
type: object
description: Employee group members
properties:
  key:
    type: string
    description: System-assigned key for the employee group member.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: System-assigned id for the employee group member.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the employee group member.
    readOnly: true
    example: /objects/company-config/employee-group-member/23
  sortOrder:
    type: integer
    description: Sort order.
    x-mappedTo: SORTORD
    example: 1
  contact:
    type: object
    x-mappedTo: contact
    x-object: company-config/contact
    properties:
      key:
        type: string
        description: System generated key for the contact.
        x-mappedTo: CONTACTKEY
        example: '973'
      href:
        type: string
        description: Endpoint for the contact.
        readOnly: true
        example: /objects/company-config/contact/23
      id:
        type: string
        x-mappedTo: EMPLOYEENAME
        example: '<PERSON>'
  employee:
    type: object
    x-mappedTo: employee
    x-object: company-config/employee
    properties:
      key:
        type: string
        description: Employee key.
        x-mappedTo: EMPLOYEEKEY
        example: '973'
      id:
        type: string
        description: Employee ID.
        x-mappedTo: EMPLOYEEID
        example: 'E-001'
      href:
        type: string
        description: Endpoint for the employee.
        readOnly: true
        example: /objects/company-config/employee/973
      status:
        type: string
        x-mappedTo: EMPLOYEESTATUS
        example: 'active'
  employeeGroup:
    title: Employee group
    description: Employee group.
    type: object
    readOnly: true
    x-mappedTo: employeegroup
    x-object: company-config/employee-group
    properties:
      id:
        type: string
        description: Identifier for the Employee group.
        x-mappedTo: ID
        readOnly: true
        example: Top Employeees
      key:
        type: string
        description: Unique system-assigned key of the employee group to which this member belongs.
        x-mappedTo: GROUPKEY
        readOnly: true
        example: '33'
      href:
        type: string
        description: Endpoint for the Employee group.
        readOnly: true
        example: /objects/company-config/employee-group/33
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml