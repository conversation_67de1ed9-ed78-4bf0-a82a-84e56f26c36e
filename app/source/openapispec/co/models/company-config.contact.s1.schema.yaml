title: company-config-contact
x-mappedTo: contact
type: object
description: A contact hold all the information needed to contact an individual or business, including name, email address, phone number, mailing address, and more.
properties:
  key:
    type: string
    description: System-assigned unique key for the contact.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '21'
  id:
    type: string
    description: Unique identifier for the contact.
    x-mappedTo: CONTACTNAME
    x-mutable: false
    example: rkincaid
  href:
    type: string
    readOnly: true
    description: URL endpoint for the contact.
    example: /objects/company-config/contact/21
  lastName:
    type: string
    description: Last name.
    x-mappedTo: LASTNAME
    example: Kincaid
    nullable: true
  firstName:
    type: string
    description: First name.
    x-mappedTo: FIRSTNAME
    example: Reuben
    nullable: true
  middleName:
    type: string
    description: Middle name or initial.
    x-mappedTo: INITIAL
    example: X
    nullable: true
  prefix:
    type: string
    description: Prefix, such as Mr., Mrs., or Ms.
    x-mappedTo: PREFIX
    example: Mr.
    nullable: true
  email1:
    type: string
    description: Primary email address.
    x-mappedTo: EMAIL1
    example: re<PERSON>@mycompany.com
    nullable: true
  email2:
    type: string
    description: Secondary email address.
    x-mappedTo: EMAIL2
    example: <EMAIL>
    nullable: true
  phone1:
    type: string
    description: Primary phone number.
    x-mappedTo: PHONE1
    example: '4151231234'
    nullable: true
  phone2:
    type: string
    description: Secondary phone number.
    x-mappedTo: PHONE2
    example: '4158661823'
    nullable: true
  mobile:
    type: string
    description: Mobile phone number.
    x-mappedTo: CELLPHONE
    example: '4159879876'
    nullable: true
  pager:
    type: string
    description: Pager number.
    x-mappedTo: PAGER
    example: '4151112222'
    nullable: true
  fax:
    type: string
    description: Fax number.
    x-mappedTo: FAX
    example: '4152221111'
    nullable: true
  URL1:
    type: string
    description: Web page address for this contact.
    x-mappedTo: URL1
    example: https://mycompany.com
    nullable: true
  URL2:
    type: string
    description: Secondary web page address.
    x-mappedTo: URL2
    example: https://whitehouse.gov
    nullable: true
  companyName:
    type: string
    description: Name of the company associated with this contact.
    x-mappedTo: COMPANYNAME
    example: AlcoSoft Inc
    nullable: true
  printAs:
    type: string
    description: The contact's name as it will appear on bills, invoices, AR statements, advances, adjustments, checks, expense reports, expense reimbursements, and 1099s.
    x-mappedTo: PRINTAS
    example: Reuben Kincaid
    nullable: false
  showInContactList:
    type: boolean
    description: Set to `true` to display the contact in the contact list.
    x-mappedTo: VISIBLE
    x-mappedToType: string
    default: true
    example: true
  discount:
    type: string
    description: Default discount percentage to be applied to order entry transactions involving this contact.
    x-mappedTo: DISCOUNT
    example: '33'
    nullable: true
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  entity:
    type: object
    description: The entity that the contact is associated with. Contacts created at the top level do not have an entity reference so the `key`, `id`, and `name` properties will be `null`.
    x-object: company-config/entity
    readOnly: true
    properties:
      key:
        type: string
        description: Entity key.
        readOnly: true
        x-mappedTo: MEGAENTITYKEY
        example: '54'
      id:
        type: string
        description: Entity ID.
        readOnly: true
        x-mappedTo: MEGAENTITYID
        example: Western Region
      name:
        type: string
        description: Entity name.
        readOnly: true
        x-mappedTo: MEGAENTITYNAME
        example: Western Region
      href:
        type: string
        description: URL endpoint for the entity.
        readOnly: true
        example: /objects/company-config/entity/54
  mailingAddress:
    type: object
    title: mailingAddress
    description: The mailing address of the contact.
    properties:
      addressLine1:
        type: string
        description: The first address line for shipping, billing, etc.
        x-mappedTo: MAILADDRESS.ADDRESS1
        example: 300 Park Avenue
        nullable: true
      addressLine2:
        type: string
        description: The second address line for shipping, billing, etc.
        x-mappedTo: MAILADDRESS.ADDRESS2
        example: Suite 1400
        nullable: true
      addressLine3:
        type: string
        description: The third address line for shipping, billing, etc. which provides additional geographical information.
        x-mappedTo: MAILADDRESS.ADDRESS3
        example: Western industrial area
        nullable: true
      city:
        type: string
        description: City.
        x-mappedTo: MAILADDRESS.CITY
        example: San Jose
        nullable: true
      state:
        type: string
        description: State.
        x-mappedTo: MAILADDRESS.STATE
        example: California
        nullable: true
      postCode:
        type: string
        description: Zip or Postal Code.
        x-mappedTo: MAILADDRESS.ZIP
        example: '95110'
        nullable: true
      country:
        type: string
        description: Country. This field is deprecated, please use `isoCountryCode`.
        deprecated: true
        x-mappedTo: MAILADDRESS.COUNTRY
        default: United States
        example: United States
        nullable: true
      isoCountryCode:
        type: string
        description: ISO country code. This field takes prevalence over `country` which is now deprecated.
        x-mappedTo: MAILADDRESS.COUNTRYCODE
        default: US
        example: US
        nullable: true
  priceList:
    type: object
    title: price list
    description: Default price list used in order entry when this contact places orders. Read [the notes in the Help Center](https://www.intacct.com/ia/docs/en_US/help_action/Order_Entry/Setting_up_Order_Entry/Price_lists/assign-OE-price-list-to-objects.htm#Assignapricelisttoacontact) for requirements and additional information.
    x-object: order-entry/price-list
    x-mappedTo: PRICELIST
    properties:
      key:
        type: string
        description: Price list key.
        x-mappedTo: PRICELISTKEY
        example: '7'
        nullable: true
      id:
        type: string
        description: Price list ID.
        x-mappedTo: PRICELIST
        example: Custom-Pricelist
        nullable: true
      href:
        type: string
        description: URL endpoint for the price list.
        readOnly: true
        example: /objects/order-entry/price-list/7
  priceSchedule:
    type: object
    title: price schedule
    description: Price schedule used in order entry when this contact places orders.
    x-object: order-entry/price-schedule
    x-mappedTo: PRICESCHEDULE
    properties:
      key:
        type: string
        description: Price schedule key.
        x-mappedTo: PRICESCHEDULEKEY
        example: '7'
        nullable: true
      id:
        type: string
        description: Price schedule ID.
        x-mappedTo: PRICESCHEDULE
        example: Discount-Premium
        nullable: true
      href:
        type: string
        description: URL endpoint for the price schedule.
        readOnly: true
        example: /objects/order-entry/price-schedule/7
  tax:
    type: object
    title: tax
    description: Tax settings needed for Intacct to calculate taxes on transactions for this contact.
    properties:
      isTaxable:
        type: boolean
        description: Set to `true` to enable tax calculation.
        x-mappedTo: TAXABLE
        x-mappedToType: string
        default: true
        example: true
      taxId:
        type: string
        description: State tax ID or VAT registration number for this contact.
        x-mappedTo: TAXID
        example: US2333
        nullable: true
      group:
        type: object
        x-object: tax/contact-tax-group
        x-mappedTo: taxgroup
        description: The contact tax group to be used with this contact.
        properties:
          key:
            type: string
            description: Contact tax group key.
            x-mappedTo: TAXGROUPKEY
            example: '7'
            nullable: true
          id:
            type: string
            description: Contact tax group ID.
            x-mappedTo: TAXGROUP
            example: Taxes-for-residents
            nullable: true
          href:
            type: string
            description: URL endpoint of the contact tax group.
            readOnly: true
            example: /objects/company-config/contact-tax-group/7
