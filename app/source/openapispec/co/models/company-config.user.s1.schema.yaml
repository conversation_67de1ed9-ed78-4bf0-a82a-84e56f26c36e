title: company-config-user
description: User information
x-mappedTo: userinfo
type: object
properties:
  key:
    description: System-assigned unique key for the user. Used to identify the user in URLs or JSON bodies for all operations on the user.
    type: string
    x-mappedTo: RECORDNO
    example: '29'
    readOnly: true
  id:
    description: User login ID. This unique identifier cannot be changed after the user is created.
    type: string
    x-mappedTo: LOGINID
    x-mutable: false
    example: john.doe
  userName:
    description: The name that will be used to identify the user. This name is displayed in the user interface.
    type: string
    x-mappedTo: DESCRIPTION
    example: John Doe
  accountEmail:
    description: User email address.
    type: string
    x-mappedTo: EMAIL
    example: <EMAIL>
  userType:
    description: |
      The user type for this user. User types determines the _maximum_ set of features and activities that a user can access and perform in Intacct. The things a user can do also depend on the permissions that are assigned to the user. For more information see [User types](https://www.intacct.com/ia/docs/en_US/help_action/Administration/Users/<USER>
    type: string
    x-mappedTo: USERTYPE
    enum:
      - business
      - employee
      - viewOnly
      - dashboard
      - projectManager
      - paymentApprover
      - platform
      - crm
      - warehouse
      - constructionManager
    x-mappedToValues:
      - business user
      - employee user
      - view only user
      - dashboard user
      - project manager user
      - payment approver
      - platform user
      - CRM user
      - warehouse user
      - Construction manager user
    default: business
    example: employee
  adminPrivileges:
    description: |
      User admin privileges.

      * `off` - No admin privileges
      * `full` - Complete administration privileges including the ability to create other full administrators and use Platform Services features. Requires that `userType` is set to `business`.
      * `limited` - All administration privileges, except for the previously mentioned items.  Requires that `userType` is set to `business`.
    type: string
    x-mappedTo: ADMIN
    enum:
      - 'off'
      - limited
      - full
    x-mappedToValues:
      - 'false'
      - 'true'
      - Full
    default: 'off'
    example: 'off'
  status:
    type: string
    description: |-
      User status.

      - `active` - The user can log in and access Intacct.
      - `inactive` -  Retains the user information in the system, but hides the user from lists throughout Intacct. Cannot be used when creating a new user.
      - `lockedOut` - Set by Intacct due to too many failed login attempts, or by an administrator to prevent the user from logging in. The user cannot log in until an administrator sets the status to `active`.
    x-mappedTo: STATUS
    enum:
      - active
      - inactive
      - lockedOut
    x-mappedToValues:
      - active
      - inactive
      - lockedout
    default: active
    example: active
  webServices:
    type: object
    description: Whether the user can use Sage Intacct Web Services (API).
    properties:
      isEnabled:
        description: Set to `true` to allow the user to use web services.
        type: boolean
        x-mappedTo: VISIBLE
        x-mappedToType: string
        default: true
        example: true
      isRestricted:
        description: Set to `true` to restrict the user to Web Services only. That is, the user cannot log in to the Sage Intacct user interface.
        type: boolean
        x-mappedTo: LOGINDISABLED
        x-mappedToType: string
        default: false
        example: true
  password:
    type: object
    description: Password properties.
    properties:
      neverExpires:
        type: boolean
        description: Set to `true` to prevent the user's password from expiring. Use this setting only for Web Services API users.
        x-mappedTo: PWDNEVEREXPIRES
        example: true
        default: false
      requiresReset:
        type: boolean
        description: Set to `true` to trigger the password reset flow for the user.
        x-mappedTo: RESETPASSWORD
        example: false
        default: false
      disablePassword:
        type: boolean
        description: Disable password requirements for the user.
        x-mappedTo: PWDQLYNOTENFORCED
        default: false
        example: false
  sso:
    type: object
    description: Single sign-on settings for the user.
    properties:
      isSSOEnabled:
        type: boolean
        description: Set to `true` to enable single sign-on for the user. 
        x-mappedTo: SSO_ENABLED
        example: true
        default: false
      federatedSSOId:
        type: string
        description: Federated SSO user ID.
        x-mappedTo: SSO_FEDERATED_ID
        example: john.doe
  entityAccess:
    type: object
    description: Entity level restrictions.
    properties:
      allowUnrestrictedAccess:
        description: Set to `true` to allow the user to access all entities in the company.
        type: boolean
        x-mappedTo: UNRESTRICTED
        default: true
        example: true
      allowTopLevelAccess:
        description: Set to `true` to enable access to the top-level. It's a best practice to enable access to the top level for all users who are restricted to a single entity in a multi-entity shared company.
        type: boolean
        x-mappedTo: TOPLEVELACCESS
        default: false
        example: false
  contact:
    type: object
    description: The contact associated with this user. 
    allOf:
      - $ref: ../../common/references/contact-ref.s1.schema.yaml
      - type: object
        x-mappedToPrefix: CONTACTINFO
        x-object: company-config/contact
        x-mappedTo: contact
        x-mappedToKey: CONTACTKEY
  trustedDevices:
    type: string
    x-mappedTo: MFA_NOTRUST
    description: Whether to recognize trusted devices for the user-- always, never, or use the company setting.
    enum:
      - companyDefault
      - always
      - never
    x-mappedToValues:
      - none
      - always
      - never
    default: companyDefault
    example: always
  isChatterDisabled:
    type: boolean
    x-mappedTo: CHATTER_DISABLED
    description: Set to `true` to prevent this user from accessing the Intacct Collaborate feature.
    x-mappedToType: string
    default: false
    example: false
  hideOtherDepartmentTransactions:
    type: boolean
    x-mappedTo: HIDENODEPT
    description: Set to `true` to hide transactions that do not belong to the departments listed in the `departments` array.
    default: false
    example: false
  locations:
    type: array
    description: This field is deprecated, please use entities.
    deprecated: true
    x-mappedTo: USERLOCATIONS
    x-schemaOverride: true
    x-object: company-config/location
    items:
      type: object
      properties:
        key:
          type: string
          description: Location key.
          x-mappedTo: LOCATIONKEY
          example: '42'
        id:
          type: string
          description: Location ID.
          x-mappedTo: LOCATIONID
          example: PNW
        href:
          type: string
          description: Endpoint URL for the location.
          readOnly: true
          example: /objects/company-config/location/21
  entities:
    type: array
    description: Locations that the user is allowed to view and work with. Leave empty to allow the user to work with all locations.
    x-mappedTo: USERLOCATIONS
    x-schemaOverride: true
    x-object: company-config/entity
    items:
      type: object
      properties:
        key:
          type: string
          description: Location key.
          x-mappedTo: LOCATIONKEY
          example: '42'
        id:
          type: string
          description: Location ID.
          x-mappedTo: LOCATIONID
          example: PNW
        href:
          type: string
          description: Endpoint URL for the location.
          readOnly: true
          example: /objects/company-config/entity/21
  departments:
    type: array
    description: Departments that the user is allowed to view and work with. Leave empty to allow the user to work with all departments.
    x-mappedTo: USERDEPARTMENTS
    x-schemaOverride: true
    x-object: company-config/department
    items:
      type: object
      properties:
        key:
          type: string
          description: Department key.
          x-mappedTo: DEPTKEY
          example: '79'
        id:
          type: string
          description: Department ID.
          x-mappedTo: DEPARTMENTID
          example: Sales
        href:
          type: string
          description: URL endpoint for the department.
          readOnly: true
          example: /objects/company-config/department/7
  territories:
    type: array
    description: List of territories that the user is assigned to.
    x-mappedTo: USERTERRITORIES
    x-schemaOverride: true
    x-object: accounts-receivable/territory
    items:
      type: object
      properties:
        key:
          type: string
          description: Territory key.
          x-mappedTo: TERRITORYKEY
          example: '9'
        id:
          type: string
          description: Territory ID.
          x-mappedTo: TERRITORYID
          example: T1
        href:
          type: string
          description: URL endpoint for the territory.
          readOnly: true
          example: /objects/accounts-receivable/territory/9
  roles:
    type: array
    description: List of roles assigned to the user. The array will be empty for companies that have user-based permissions.
    x-mappedTo: USERROLES
    x-schemaOverride: true
    x-object: company-config/role
    items:
      type: object
      properties:
        key:
          type: string
          description: Role key.
          x-mappedTo: ROLEKEY
          example: '7'
        id:
          type: string
          description: Role ID.
          x-mappedTo: ROLENAME
          example: Employee
        href:
          type: string
          description: URL endpoint for the role.
          readOnly: true
          example: /objects/company-config/role/7
  permissionAssignments:
    type: array
    description: Array of objects that define the permissions and access rights assigned to the user.
    x-mappedTo: USERPERMISSIONS
    x-schemaOverride: true
    items:
      type: object
      properties:
        permission:
          type: object
          description: Reference to a permission that is assigned to the user.
          x-object: company-config/permission
          properties:
            key:
              type: string
              description: Permission key.
              x-mappedTo: POLICYKEY
              example: '414'
            id:
              type: string
              description: Permission ID.
              x-mappedTo: POLICYKEY
              example: '414'
            href:
              type: string
              description: Endpoint URL for the permission.
              readOnly: true
              example: /objects/company-config/permission/404
        accessRights:
          type: array
          description: List of functions or tasks that the user can perform for the specified permission.
          x-delimiter: '|'
          x-mappedTo: POLICYVAL
          example: [ 'list', 'readonly', 'add', 'modify', 'delete' ]
          items:
            $ref: permission-access-rights.s1.schema.yaml
  customPermissionAssignments:
    type: array
    description: Array of objects that define the user's permissions and access rights for custom applications.
    x-mappedTo: CUSTOMPERMISSIONS
    x-schemaOverride: true
    items:
      type: object
      properties:
        application:
          type: object
          x-object: platform/custom-application
          properties:
            key:
              type: string
              description: Custom application key.
              x-mappedTo: CUSTAPPKEY
              example: '10005'
            id:
              type: string
              description: Custom application ID.
              x-mappedTo: CUSTAPPNAME
              x-mutable: false
              example: Custom Dimension
            href:
              type: string
              description: URL endpoint for the custom application.
              readOnly: true
              example: /objects/platform/custom-application/404
            permission:
              type: array
              description: List of permissions for the custom application that are assigned to the user.
              x-mappedTo: PERMISSION
              maxItems: 1
              items:
                type: object
                properties:
                  name:
                    type: string
                    description: Resource name.
                    x-mappedTo: RESOURCENAME
                    example: custom_object
                  group:
                    type: string
                    description: Resource type.
                    enum:
                      - 'actions'
                      - 'objects'
                      - 'menus'
                    x-mappedToValues:
                      - 'ACTION'
                      - 'OBJECT'
                      - 'MENU'
                    x-mappedTo: RESOURCETYPE
                    example: objects
        accessRights:
          type: array
          description: Permission access rights.
          x-delimiter: '|'
          x-mappedTo: POLICYVAL
          example: [ 'list', 'readonly', 'add', 'modify', 'delete' ]
          items:
            $ref: permission-access-rights.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  entity:
    type: object
    description: The entity that the user is associated with. Users created at the top level do not have an entity reference so the `key`, `id`, and `name` properties will be `null`.
    x-object: company-config/entity
    properties:
      key:
        type: string
        description: Entity Key.
        x-mappedTo: MEGAENTITYKEY
        example: '54'
      id:
        type: string
        description: Entity ID.
        x-mappedTo: MEGAENTITYID
        example: '313131'
      name:
        type: string
        description: Entity Name.
        readOnly: true
        x-mappedTo: MEGAENTITYNAME
        example: Central Region
      href:
        type: string
        description: URL endpoint for the entity.
        readOnly: true
        example: /objects/company-config/entity/54
  href:
    type: string
    readOnly: true
    description: URL endpoint for user.
    example: /objects/company-config/user/21