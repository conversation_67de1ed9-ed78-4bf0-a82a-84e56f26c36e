key: company-config/assignment::systemAssignmentFW2
id: systemAssignmentFW2
object: company-config/assignment
name: IA.RECENTLY_MODIFIED
description: Specifies all active assignments
query:
  object: company-config/assignment
  fields:
    - assignmentId
    - name
    - description
    - assignee.id
    - plannedStartDate
    - dueDate
    - assignmentStatus.name
    - actualEndDate
    - status
    - audit.modifiedDateTime
    - audit.modifiedByUser.id
  orderBy:
    - audit.modifiedDateTime: desc
  filters:
    - $eq:
        status: active
metadata:
  frozenColumnCount: 2
contexts:
  - __default