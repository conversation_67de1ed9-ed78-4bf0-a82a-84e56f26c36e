uiLabel: IA.CONTACT
fields:
    key:
        uiType: integer
        uiLabel: 'IA.RECORD_NUMBER'
    id:
        uiType: text
        uiLabel: 'IA.CONTACT_NAME'
    lastName:
        uiType: text
        uiLabel: 'IA.LAST_NAME'
    firstName:
        uiType: text
        uiLabel: 'IA.FIRST_NAME'
    middleName:
        uiType: text
        uiLabel: 'IA.MIDDLE_NAME'
    prefix:
        uiType: text
        uiLabel: 'IA.MRMS'
    email1:
        uiType: text
        uiLabel: 'IA.EMAIL_ADDRESS'
    email2:
        uiType: text
        uiLabel: 'IA.SECONDARY_EMAIL_ADDRESS'
    phone1:
        uiType: text
        uiLabel: 'IA.PHONE_NUMBER'
    phone2:
        uiType: text
        uiLabel: 'IA.PHONE_2'
    mobile:
        uiType: text
        uiLabel: 'IA.MOBILE_PHONE'
    pager:
        uiType: text
        uiLabel: 'IA.PAGER'
    fax:
        uiType: text
        uiLabel: 'IA.FAX'
    URL1:
        uiType: text
        uiLabel: 'IA.URL'
    URL2:
        uiType: text
        uiLabel: 'IA.URL_2'
    companyName:
        uiType: text
        uiLabel: 'IA.COMPANY_NAME'
    printAs:
        uiType: text
        uiLabel: 'IA.PRINT_AS'
    showInContactList:
        uiType: enum
        uiLabel: 'IA.VISIBLE'
        enumsLabels:
            -
                label: 'true'
                value: true
            -
                label: 'false'
                value: false
    status:
        uiType: enum
        uiLabel: 'IA.STATUS'
        enumsLabels:
            -
                label: 'IA.ACTIVE'
                value: 'active'
            -
                label: 'IA.INACTIVE'
                value: 'inactive'
groups:
    mailingAddress:
        fields:
            city:
                uiType: text
                uiLabel: 'IA.CITY_TOWN'
            state:
                uiType: text
                uiLabel: 'IA.STATE_PROVINCE'
            postCode:
                uiType: text
                uiLabel: 'IA.ZIP_CODE'
            country:
                uiType: text
                uiLabel: 'IA.COUNTRY_NON_ISO'
            isoCountryCode:
              uiType: enum
              uiLabel: 'IA.COUNTRY'
              enumsLabels:
                - 'label': 'IA.UNITED_STATES'
                  'value': 'us'
                - 'label': 'IA.AFGHANISTAN'
                  'value': 'af'
                - 'label': 'IA.ALAND_ISLANDS'
                  'value': 'ax'
                - 'label': 'IA.ALBANIA'
                  'value': 'al'
                - 'label': 'IA.ALGERIA'
                  'value': 'dz'
                - 'label': 'IA.AMERICAN_SAMOA'
                  'value': 'as'
                - 'label': 'IA.ANDORRA'
                  'value': 'ad'
                - 'label': 'IA.ANGOLA'
                  'value': 'ao'
                - 'label': 'IA.ANGUILLA'
                  'value': 'ai'
                - 'label': 'IA.ANTARCTICA'
                  'value': 'aq'
                - 'label': 'IA.ANTIGUA_AND_BARBUDA'
                  'value': 'ag'
                - 'label': 'IA.ARGENTINA'
                  'value': 'ar'
                - 'label': 'IA.ARMENIA'
                  'value': 'am'
                - 'label': 'IA.ARUBA'
                  'value': 'aw'
                - 'label': 'IA.AUSTRALIA'
                  'value': 'au'
                - 'label': 'IA.AUSTRIA'
                  'value': 'at'
                - 'label': 'IA.AZERBAIJAN'
                  'value': 'az'
                - 'label': 'IA.BAHAMAS'
                  'value': 'bs'
                - 'label': 'IA.BAHRAIN'
                  'value': 'bh'
                - 'label': 'IA.BANGLADESH'
                  'value': 'bd'
                - 'label': 'IA.BARBADOS'
                  'value': 'bb'
                - 'label': 'IA.BELARUS'
                  'value': 'by'
                - 'label': 'IA.BELGIUM'
                  'value': 'be'
                - 'label': 'IA.BELIZE'
                  'value': 'bz'
                - 'label': 'IA.BENIN'
                  'value': 'bj'
                - 'label': 'IA.BERMUDA'
                  'value': 'bm'
                - 'label': 'IA.BHUTAN'
                  'value': 'bt'
                - 'label': 'IA.BOLIVIA'
                  'value': 'bo'
                - 'label': 'IA.BONAIRE_SINT_EUSTATIUS_AND_SABA'
                  'value': 'bq'
                - 'label': 'IA.BOSNIA_AND_HERZEGOVINA'
                  'value': 'ba'
                - 'label': 'IA.BOTSWANA'
                  'value': 'bw'
                - 'label': 'IA.BOUVET_ISLAND'
                  'value': 'bv'
                - 'label': 'IA.BRAZIL'
                  'value': 'br'
                - 'label': 'IA.BRITISH_INDIAN_OCEAN_TERRITORY'
                  'value': 'io'
                - 'label': 'IA.BRUNEI_DARUSSALAM'
                  'value': 'bn'
                - 'label': 'IA.BULGARIA'
                  'value': 'bg'
                - 'label': 'IA.BURKINA_FASO'
                  'value': 'bf'
                - 'label': 'IA.BURUNDI'
                  'value': 'bi'
                - 'label': 'IA.CAMBODIA'
                  'value': 'kh'
                - 'label': 'IA.CAMEROON'
                  'value': 'cm'
                - 'label': 'IA.CANADA'
                  'value': 'ca'
                - 'label': 'IA.CAPE_VERDE'
                  'value': 'cv'
                - 'label': 'IA.CAYMAN_ISLANDS'
                  'value': 'ky'
                - 'label': 'IA.CENTRAL_AFRICAN_REPUBLIC'
                  'value': 'cf'
                - 'label': 'IA.CHAD'
                  'value': 'td'
                - 'label': 'IA.CHILE'
                  'value': 'cl'
                - 'label': 'IA.CHINA'
                  'value': 'cn'
                - 'label': 'IA.CHRISTMAS_ISLAND'
                  'value': 'cx'
                - 'label': 'IA.COCOS_KEELING_ISLANDS'
                  'value': 'cc'
                - 'label': 'IA.COLOMBIA'
                  'value': 'co'
                - 'label': 'IA.COMOROS'
                  'value': 'km'
                - 'label': 'IA.CONGO'
                  'value': 'cg'
                - 'label': 'IA.CONGO_DEMOCRATIC_REPUBLIC'
                  'value': 'cd'
                - 'label': 'IA.COOK_ISLANDS'
                  'value': 'ck'
                - 'label': 'IA.COSTA_RICA'
                  'value': 'cr'
                - 'label': 'IA.C_TE_D_IVOIRE'
                  'value': 'ci'
                - 'label': 'IA.CROATIA'
                  'value': 'hr'
                - 'label': 'IA.CUBA'
                  'value': 'cu'
                - 'label': 'IA.CURACAO'
                  'value': 'cw'
                - 'label': 'IA.CYPRUS'
                  'value': 'cy'
                - 'label': 'IA.CZECH_REPUBLIC'
                  'value': 'cz'
                - 'label': 'IA.DENMARK'
                  'value': 'dk'
                - 'label': 'IA.DJIBOUTI'
                  'value': 'dj'
                - 'label': 'IA.DOMINICA'
                  'value': 'dm'
                - 'label': 'IA.DOMINICAN_REPUBLIC'
                  'value': 'do'
                - 'label': 'IA.ECUADOR'
                  'value': 'ec'
                - 'label': 'IA.EGYPT'
                  'value': 'eg'
                - 'label': 'IA.EL_SALVADOR'
                  'value': 'sv'
                - 'label': 'IA.EQUATORIAL_GUINEA'
                  'value': 'gq'
                - 'label': 'IA.ERITREA'
                  'value': 'er'
                - 'label': 'IA.ESTONIA'
                  'value': 'ee'
                - 'label': 'IA.ESWATINI'
                  'value': 'sz'
                - 'label': 'IA.ETHIOPIA'
                  'value': 'et'
                - 'label': 'IA.FALKLAND_ISLANDS_MALVINAS'
                  'value': 'fk'
                - 'label': 'IA.FAROE_ISLANDS'
                  'value': 'fo'
                - 'label': 'IA.FIJI'
                  'value': 'fj'
                - 'label': 'IA.FINLAND'
                  'value': 'fi'
                - 'label': 'IA.FRANCE'
                  'value': 'fr'
                - 'label': 'IA.FRENCH_GUIANA'
                  'value': 'gf'
                - 'label': 'IA.FRENCH_POLYNESIA'
                  'value': 'pf'
                - 'label': 'IA.FRENCH_SOUTHERN_TERRITORIES'
                  'value': 'tf'
                - 'label': 'IA.GABON'
                  'value': 'ga'
                - 'label': 'IA.GAMBIA'
                  'value': 'gm'
                - 'label': 'IA.GEORGIA'
                  'value': 'ge'
                - 'label': 'IA.GERMANY'
                  'value': 'de'
                - 'label': 'IA.GHANA'
                  'value': 'gh'
                - 'label': 'IA.GIBRALTAR'
                  'value': 'gi'
                - 'label': 'IA.GREECE'
                  'value': 'gr'
                - 'label': 'IA.GREENLAND'
                  'value': 'gl'
                - 'label': 'IA.GRENADA'
                  'value': 'gd'
                - 'label': 'IA.GUADELOUPE'
                  'value': 'gp'
                - 'label': 'IA.GUAM'
                  'value': 'gu'
                - 'label': 'IA.GUATEMALA'
                  'value': 'gt'
                - 'label': 'IA.GUERNSEY'
                  'value': 'gg'
                - 'label': 'IA.GUINEA'
                  'value': 'gn'
                - 'label': 'IA.GUINEA_BISSAU'
                  'value': 'gw'
                - 'label': 'IA.GUYANA'
                  'value': 'gy'
                - 'label': 'IA.HAITI'
                  'value': 'ht'
                - 'label': 'IA.HEARD_IS_AND_MCDONALD_ISLANDS'
                  'value': 'hm'
                - 'label': 'IA.HONDURAS'
                  'value': 'hn'
                - 'label': 'IA.HONG_KONG'
                  'value': 'hk'
                - 'label': 'IA.HUNGARY'
                  'value': 'hu'
                - 'label': 'IA.ICELAND'
                  'value': 'is'
                - 'label': 'IA.INDIA'
                  'value': 'in'
                - 'label': 'IA.INDONESIA'
                  'value': 'id'
                - 'label': 'IA.IRAN_ISLAMIC_REPUBLIC_OF'
                  'value': 'ir'
                - 'label': 'IA.IRAQ'
                  'value': 'iq'
                - 'label': 'IA.IRELAND'
                  'value': 'ie'
                - 'label': 'IA.ISLE_OF_MAN'
                  'value': 'im'
                - 'label': 'IA.ISRAEL'
                  'value': 'il'
                - 'label': 'IA.ITALY'
                  'value': 'it'
                - 'label': 'IA.JAMAICA'
                  'value': 'jm'
                - 'label': 'IA.JAPAN'
                  'value': 'jp'
                - 'label': 'IA.JERSEY'
                  'value': 'je'
                - 'label': 'IA.JORDAN'
                  'value': 'jo'
                - 'label': 'IA.KAZAKHSTAN'
                  'value': 'kz'
                - 'label': 'IA.KENYA'
                  'value': 'ke'
                - 'label': 'IA.KIRIBATI'
                  'value': 'ki'
                - 'label': 'IA.KOREA_REPUBLIC_OF'
                  'value': 'kr'
                - 'label': 'IA.KOREA_DEMO_PEOPLE_S_REP'
                  'value': 'kp'
                - 'label': 'IA.KOSOVO'
                  'value': 'xk'
                - 'label': 'IA.KUWAIT'
                  'value': 'kw'
                - 'label': 'IA.KYRGYZSTAN'
                  'value': 'kg'
                - 'label': 'IA.LAO'
                  'value': 'la'
                - 'label': 'IA.LATVIA'
                  'value': 'lv'
                - 'label': 'IA.LEBANON'
                  'value': 'lb'
                - 'label': 'IA.LESOTHO'
                  'value': 'ls'
                - 'label': 'IA.LIBERIA'
                  'value': 'lr'
                - 'label': 'IA.LIBYAN_ARAB_JAMAHIRIYA'
                  'value': 'ly'
                - 'label': 'IA.LIECHTENSTEIN'
                  'value': 'li'
                - 'label': 'IA.LITHUANIA'
                  'value': 'lt'
                - 'label': 'IA.LUXEMBOURG'
                  'value': 'lu'
                - 'label': 'IA.MACAO'
                  'value': 'mo'
                - 'label': 'IA.MACEDONIA'
                  'value': 'mk'
                - 'label': 'IA.MADAGASCAR'
                  'value': 'mg'
                - 'label': 'IA.MALAWI'
                  'value': 'mw'
                - 'label': 'IA.MALAYSIA'
                  'value': 'my'
                - 'label': 'IA.MALDIVES'
                  'value': 'mv'
                - 'label': 'IA.MALI'
                  'value': 'ml'
                - 'label': 'IA.MALTA'
                  'value': 'mt'
                - 'label': 'IA.MARSHALL_ISLANDS'
                  'value': 'mh'
                - 'label': 'IA.MARTINIQUE'
                  'value': 'mq'
                - 'label': 'IA.MAURITANIA'
                  'value': 'mr'
                - 'label': 'IA.MAURITIUS'
                  'value': 'mu'
                - 'label': 'IA.MAYOTTE'
                  'value': 'yt'
                - 'label': 'IA.MEXICO'
                  'value': 'mx'
                - 'label': 'IA.MICRONESIA'
                  'value': 'fm'
                - 'label': 'IA.MOLDOVA_REPUBLIC_OF'
                  'value': 'md'
                - 'label': 'IA.MONACO'
                  'value': 'mc'
                - 'label': 'IA.MONGOLIA'
                  'value': 'mn'
                - 'label': 'IA.MONTENEGRO'
                  'value': 'me'
                - 'label': 'IA.MONTSERRAT'
                  'value': 'ms'
                - 'label': 'IA.MOROCCO'
                  'value': 'ma'
                - 'label': 'IA.MOZAMBIQUE'
                  'value': 'mz'
                - 'label': 'IA.MYANMAR'
                  'value': 'mm'
                - 'label': 'IA.NAMIBIA'
                  'value': 'na'
                - 'label': 'IA.NAURU'
                  'value': 'nr'
                - 'label': 'IA.NEPAL'
                  'value': 'np'
                - 'label': 'IA.NETHERLANDS'
                  'value': 'nl'
                - 'label': 'IA.NETHERLANDS_ANTILLES'
                  'value': 'an'
                - 'label': 'IA.NEW_CALEDONIA'
                  'value': 'nc'
                - 'label': 'IA.NEW_ZEALAND'
                  'value': 'nz'
                - 'label': 'IA.NICARAGUA'
                  'value': 'ni'
                - 'label': 'IA.NIGER'
                  'value': 'ne'
                - 'label': 'IA.NIGERIA'
                  'value': 'ng'
                - 'label': 'IA.NIUE'
                  'value': 'nu'
                - 'label': 'IA.NORFOLK_ISLAND'
                  'value': 'nf'
                - 'label': 'IA.NORTHERN_MARIANA_ISLANDS'
                  'value': 'mp'
                - 'label': 'IA.NORWAY'
                  'value': 'no'
                - 'label': 'IA.OMAN'
                  'value': 'om'
                - 'label': 'IA.PAKISTAN'
                  'value': 'pk'
                - 'label': 'IA.PALAU'
                  'value': 'pw'
                - 'label': 'IA.PALESTINIAN_TERRITORY_OCCUPIED'
                  'value': 'ps'
                - 'label': 'IA.PANAMA'
                  'value': 'pa'
                - 'label': 'IA.PAPUA_NEW_GUINEA'
                  'value': 'pg'
                - 'label': 'IA.PARAGUAY'
                  'value': 'py'
                - 'label': 'IA.PERU'
                  'value': 'pe'
                - 'label': 'IA.PHILIPPINES'
                  'value': 'ph'
                - 'label': 'IA.PITCAIRN'
                  'value': 'pn'
                - 'label': 'IA.POLAND'
                  'value': 'pl'
                - 'label': 'IA.PORTUGAL'
                  'value': 'pt'
                - 'label': 'IA.PUERTO_RICO'
                  'value': 'pr'
                - 'label': 'IA.QATAR'
                  'value': 'qa'
                - 'label': 'IA.REUNION'
                  'value': 're'
                - 'label': 'IA.ROMANIA'
                  'value': 'ro'
                - 'label': 'IA.RUSSIAN_FEDERATION'
                  'value': 'ru'
                - 'label': 'IA.RWANDA'
                  'value': 'rw'
                - 'label': 'IA.SAINT_BARTHELEMY'
                  'value': 'bl'
                - 'label': 'IA.SAINT_HELENA'
                  'value': 'sh'
                - 'label': 'IA.SAINT_KITTS_AND_NEVIS'
                  'value': 'kn'
                - 'label': 'IA.SAINT_LUCIA'
                  'value': 'lc'
                - 'label': 'IA.SAINT_MARTIN'
                  'value': 'mf'
                - 'label': 'IA.SAINT_PIERRE_AND_MIQUELON'
                  'value': 'pm'
                - 'label': 'IA.SAINT_VINCENT_AND_THE_GRENADINES'
                  'value': 'vc'
                - 'label': 'IA.SAMOA'
                  'value': 'ws'
                - 'label': 'IA.SAN_MARINO'
                  'value': 'sm'
                - 'label': 'IA.SAO_TOME_AND_PRINCIPE'
                  'value': 'st'
                - 'label': 'IA.SAUDI_ARABIA'
                  'value': 'sa'
                - 'label': 'IA.SENEGAL'
                  'value': 'sn'
                - 'label': 'IA.SERBIA'
                  'value': 'rs'
                - 'label': 'IA.SEYCHELLES'
                  'value': 'sc'
                - 'label': 'IA.SIERRA_LEONE'
                  'value': 'sl'
                - 'label': 'IA.SINGAPORE'
                  'value': 'sg'
                - 'label': 'IA.SINT_MAARTEN'
                  'value': 'sx'
                - 'label': 'IA.SLOVAKIA'
                  'value': 'sk'
                - 'label': 'IA.SLOVENIA'
                  'value': 'si'
                - 'label': 'IA.SOLOMON_ISLANDS'
                  'value': 'sb'
                - 'label': 'IA.SOMALIA'
                  'value': 'so'
                - 'label': 'IA.SOUTH_AFRICA'
                  'value': 'za'
                - 'label': 'IA.S_GEORGIA_AND_S_SANDWICH_IS'
                  'value': 'gs'
                - 'label': 'IA.SPAIN'
                  'value': 'es'
                - 'label': 'IA.SRI_LANKA'
                  'value': 'lk'
                - 'label': 'IA.SUDAN'
                  'value': 'sd'
                - 'label': 'IA.SOUTH_SUDAN'
                  'value': 'ss'
                - 'label': 'IA.SURINAME'
                  'value': 'sr'
                - 'label': 'IA.SVALBARD_AND_JAN_MAYEN'
                  'value': 'sj'
                - 'label': 'IA.SWEDEN'
                  'value': 'se'
                - 'label': 'IA.SWITZERLAND'
                  'value': 'ch'
                - 'label': 'IA.SYRIAN_ARAB_REPUBLIC'
                  'value': 'sy'
                - 'label': 'IA.TAIWAN'
                  'value': 'tw'
                - 'label': 'IA.TAJIKISTAN'
                  'value': 'tj'
                - 'label': 'IA.TANZANIA_UNITED_REPUBLIC_OF'
                  'value': 'tz'
                - 'label': 'IA.THAILAND'
                  'value': 'th'
                - 'label': 'IA.TIMOR_LESTE'
                  'value': 'tl'
                - 'label': 'IA.TOGO'
                  'value': 'tg'
                - 'label': 'IA.TOKELAU'
                  'value': 'tk'
                - 'label': 'IA.TONGA'
                  'value': 'to'
                - 'label': 'IA.TRINIDAD_AND_TOBAGO'
                  'value': 'tt'
                - 'label': 'IA.TUNISIA'
                  'value': 'tn'
                - 'label': 'IA.TURKEY'
                  'value': 'tr'
                - 'label': 'IA.TURKMENISTAN'
                  'value': 'tm'
                - 'label': 'IA.TURKS_AND_CAICOS_ISLANDS'
                  'value': 'tc'
                - 'label': 'IA.TUVALU'
                  'value': 'tv'
                - 'label': 'IA.UGANDA'
                  'value': 'ug'
                - 'label': 'IA.UKRAINE'
                  'value': 'ua'
                - 'label': 'IA.UNITED_ARAB_EMIRATES'
                  'value': 'ae'
                - 'label': 'IA.UNITED_KINGDOM'
                  'value': 'gb'
                - 'label': 'IA.US_MINOR_OUTLYING_ISLANDS'
                  'value': 'um'
                - 'label': 'IA.URUGUAY'
                  'value': 'uy'
                - 'label': 'IA.UZBEKISTAN'
                  'value': 'uz'
                - 'label': 'IA.VANUATU'
                  'value': 'vu'
                - 'label': 'IA.VATICAN_CITY_STATE'
                  'value': 'va'
                - 'label': 'IA.VENEZUELA'
                  'value': 've'
                - 'label': 'IA.VIETNAM'
                  'value': 'vn'
                - 'label': 'IA.VIRGIN_ISLANDS_BRITISH'
                  'value': 'vg'
                - 'label': 'IA.VIRGIN_ISLANDS_US'
                  'value': 'vi'
                - 'label': 'IA.WALLIS_AND_FUTUNA'
                  'value': 'wf'
                - 'label': 'IA.WESTERN_SAHARA'
                  'value': 'eh'
                - 'label': 'IA.YEMEN'
                  'value': 'ye'
                - 'label': 'IA.ZAMBIA'
                  'value': 'zm'
                - 'label': 'IA.ZIMBABWE'
                  'value': 'zw'
            addressLine1:
                uiType: textarea
                uiLabel: 'IA.ADDRESS_1'
            addressLine2:
                uiType: textarea
                uiLabel: 'IA.ADDRESS_2'
            addressLine3:
                uiType: textarea
                uiLabel: 'IA.ADDRESS_THREE'
    tax:
        fields:
            isTaxable:
                uiType: enum
                uiLabel: 'IA.TAXABLE'
                enumsLabels:
                    -
                        label: 'IA.TRUE'
                        value: true
                    -
                        label: 'IA.FALSE'
                        value: false
            taxId:
                uiType: text
                uiLabel: 'IA.TAX_ID'
        refs:
            group:
                fields:
                    key:
                        uiType: sequence
                        uiLabel: 'IA.TAX_GROUP_RECORD_NUMBER'
                    id:
                        uiType: ptr
                        uiLabel: 'IA.CONTACT_TAX_GROUPS'
    entity:
        fields:
            key:
                uiType: integer
                uiLabel: 'IA.ENTITY_KEY'
            id:
                uiType: ptr
                uiLabel: 'IA.ENTITY_ID'
            name:
                uiType: text
                uiLabel: 'IA.ENTITY_NAME'
