openapi: 3.0.0
info:
  title: projects-invoice-run
  description: projects.invoice-run API
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: Projects invoice run
    description: Record created every time you generate invoices in Projects.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/projects/invoice-run:
    get:
      summary: List invoice runs
      description: Returns a collection with a key, ID, and link for each invoice run.
      tags:
        - Invoice runs
      operationId: list-projects-invoice-run
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of invoice-run objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of invoice runs:
                  value:
                    'ia::result':
                      - key: '6'
                        id: '6'
                        href: /objects/projects/invoice-run/6
                      - key: '5'
                        id: '5'
                        href: /objects/projects/invoice-run/5
                      - key: '3'
                        id: '3'
                        href: /objects/projects/invoice-run/3
                      - key: '4'
                        id: '4'
                        href: /objects/projects/invoice-run/4
                    'ia::meta':
                      totalCount: 4
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/projects/invoice-run/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the invoice run.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an invoice run
      description: Returns detailed information for a specified invoice run.
      tags:
        - Invoice runs
      operationId: get-projects-invoice-run-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the invoice-run
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/projects-invoice-run'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Example 1:
                  value:
                    'ia::result':
                      id: '175'
                      key: '175'
                      invoiceTemplate:
                        id: '146'
                        key: '146'
                        name: Invoice Progress Billing
                        href: /objects/order-entry/txn-definition/146
                      priceList:
                        key: null
                        id: null
                      expensePriceMarkup: null
                      description: 'Invoice run created at 2024-05-08 13:33:45  GMT'
                      isOffline: false
                      state: success
                      errorData: null
                      createdBy:
                        key: '1'
                        id: Admin
                        href: /objects/company-config/user/1
                      invoiceRunDateTime: '2022-04-20T16:20:00Z'
                      href: /objects/projects/invoice-run/175
                      entity:
                        key: '46'
                        id: Western Region
                        name: Western Region
                        href: /objects/company-config/entity/15
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    projects-invoice-run:
      $ref: ../models/projects.invoice-run.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml