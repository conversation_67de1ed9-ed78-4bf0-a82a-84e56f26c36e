openapi: 3.0.0
info:
  title: projects-txn-rule-detail
  description: Transaction rule detail API
  version: '1.0'
  contact:
    name: suresh adiserla
    email: <EMAIL>
tags:
  - name: Transaction rule detail
    description: Transaction-rule-detail description -- explain what it's for and how it's used.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/projects/txn-rule-detail:
    get:
      summary: List
      description: 'Returns a collection with a key, ID, and link for each txn-rule-detail.'
      tags:
        - Transaction rule detail
      operationId: list-projects-txn-rule-detail
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of transaction rule detail objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of transaction rule details:
                  value:
                    'ia::result':
                      - key: '101'
                        id: '101'
                        href: /objects/projects/txn-rule-detail/101
                      - key: '102'
                        id: '102'
                        href: /objects/projects/txn-rule-detail/102
                      - key: '103'
                        id: '103'
                        href: /objects/projects/txn-rule-detail/103
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: 0
                      previous: 0
        '400':
          $ref: '#/components/responses/400error'
  /objects/projects/txn-rule-detail/{key}:
    parameters:
      - name: key
        description: System-assigned unique key of the txn-rule-detail.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a transaction rule detail
      description: Returns detailed information for a particular txn-rule-detail.
      tags:
        - Transaction rule detail
      operationId: get-projects-txn-rule-detail-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the txn-rule-detail
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/projects-txn-rule-detail'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the transaction rule detail:
                  value:
                    'ia::result':
                      key: '30'
                      id: '30'
                      href: /objects/projects/txn-rule-detail/30
                      txnRule:
                        key: '8'
                        id: 'Txn rule for timesheet'
                        href: /objects/projects/txn-rule/8
                      sequenceNumber: '10'
                      description: null
                      action: 'processSourceTxn'
                      isPost: true
                      subtotalRange: null
                      txnAmountSource: costAmount
                      rate: '1'
                      rateNote: null
                      applyToBillableOnly: false
                      resultingAction: 'Multiply rate times cost amount and Post to GL a debit to 5900 and a credit to 7900'
                      debitPosting:
                        glAccount:
                          key: '158'
                          id: '5900'
                          href: /objects/general-ledger/account/158
                        location:
                          key: '1'
                          id: 'DIA'
                          name: 'US'
                          href: /objects/company-config/location/1
                        department:
                          key: '10'
                          id: '20'
                          name: 'Sales'
                          href: /objects/company-config/department/10
                        class:
                          key: '1'
                          id: 'C10'
                          name: 'Construction'
                          href: /objects/company-config/class/1
                        project:
                          key: '114'
                          id: '15-GAR'
                          name: 'Parking garage renovation'
                        isBillable: false
                      creditPosting:
                        isBillable: false
                        class:
                          key: '1'
                          href: /objects/company-config/class/1
                          id: 'C1'
                          name: 'Goods'
                        creditDepartment:
                          key: '12'
                          href: /objects/company-config/department/12
                          id: 'SW'
                          name: 'Software Dev'
                        glAccount:
                          key: '150'
                          href: /objects/general-ledger/account/150
                          id: '7900'
                        location:
                          key: '1'
                          href: /objects/company-config/location/1
                          id: 'Lyon'
                          name: 'Lyon-France'
                        project:
                          key: '114'
                          href: /objects/projects/project/114
                          id: 'P-0045'
                          name: 'Implementation Project'
                      employee:
                        key: '123'
                        href: /objects/company-config/employee/123
                        id: 'E001'
                        name: 'John Smith'
                      employeeGroup:
                        href: /objects/employeegroup/4
                        id: 'MGR'
                        key: '4'
                        name: 'Sr. Managers'
                      itemGLGroup:
                        href: /objects/inventory-control/item-gl-group/23
                        name: 'Services'
                        key: '23'
                      audit:
                        createdDateTime: '2022-04-20T16:20:00Z'
                        modifiedDateTime: '2022-04-20T16:20:00Z'
                        createdBy: '1'
                        modifiedBy: '95'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    projects-txn-rule-detail:
      $ref: ../models/projects.txn-rule-detail.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
