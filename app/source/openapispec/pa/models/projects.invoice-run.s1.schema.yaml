title: invoice-run
x-mappedTo: invoicerun
type: object
description: Invoice run from Projects - Generate invoices.
properties:
  key:
    type: string
    description: System-assigned key for the invoice-run.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Invoice run ID (same as key).
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: URL endpoint of the invoice-run.
    readOnly: true
    example: /objects/projects/invoice-run/23
  expensePriceMarkup:
    type: string
    x-mappedTo: EXPENSEPRICEMARKUP
    description: Expense price percent markup.
    nullable: true
    example: '10'
  description:
    type: string
    description: System-generated description of the invoice run.
    x-mappedTo: DESCRIPTION
    example: 'Invoice run created at 2024-05-08 13:33:45  GMT'
  isOffline:
    type: boolean
    x-mappedTo: ISOFFLINE
    description: Indicates whether the invoice run was executed offline.
    x-mappedToType: string
    readOnly: true
    example: false
  state:
    type: string
    description: State of the invice run.
    x-mappedTo: STATE
    readOnly: true
    nullable: true
    example: 'success'
    enum:
      - null
      - 'success'
      - 'failed'
      - 'inTransit'
      - 'partialSuccess'
    x-mappedToValues:
      - ''
      - 'Success'
      - 'Failed'
      - 'In Transit'
      - 'Partial Success'
  errorData:
    type: string
    description: Serialized error details, if any were encountered during invoice run.
    x-mappedTo: ERRORDATA
    readOnly: true
    nullable: true
  invoiceRunDateTime:
    type: string
    x-mappedTo: CREATEDDATE
    format: date-time
    description: Invoice run date.
    example: '2022-04-20T16:20:00Z'
    readOnly: true
  priceList:
    type: object
    x-mappedTo: pricelist
    x-object: order-entry/price-list
    description: Price list used to override pricing for transactions billed in this invoice run.
    readOnly: true
    properties:
      key:
        type: string
        description: Price list key.
        x-mappedTo: OVERRIDE_PRICELISTKEY
        example: '4'
        nullable: true
      id:
        type: string
        description: Price list ID.
        x-mappedTo: OVERRIDE_PRICELISTID
        example: Base price list
        nullable: true
      href:
        type: string
        description: URL endpoint of the price list.
        readOnly: true
        example: /objects/order-entry/price-list/4
  invoiceTemplate:
    type: object
    x-mappedTo: sodocumentparams
    x-object: order-entry/txn-definition
    readOnly: true
    properties:
      key:
        type: string
        description: System-assigned key for the invoice template.
        x-mappedTo: DOCPARKEY
        example: '4'
        readOnly: true
      id:
        type: string
        description: Invoice template ID (same as key).
        x-mappedTo: DOCPARKEY
        readOnly: true
        example: '4'
      name:
        type: string
        description: Invoice template name.
        x-mappedTo: DOCPARID
        readOnly: true
        example: 'Sales Invoice'
      href:
        type: string
        description: URL endpoint of the invoice template.
        readOnly: true
        example: /objects/order-entry/txn-definition/4
  createdBy:
    type: object
    x-mappedTo: userinfo
    x-object: company-config/user
    readOnly: true
    properties:
      key:
        type: string
        x-mappedTo: CREATEDBYKEY
        readOnly: true
        description: System-assigned key for the created-by user
        example: "2"
      id:
        type: string
        x-mappedTo: CREATEDBY
        readOnly: true
        description: Created-by user ID.
        example: "JSmith"
      href:
        type: string
        readOnly: true
        description: URL endpoint of the created-by user
        example: /objects/company-config/user/2
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml