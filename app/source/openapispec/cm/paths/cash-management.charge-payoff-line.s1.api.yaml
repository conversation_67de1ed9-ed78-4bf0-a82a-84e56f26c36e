openapi: 3.0.0
info:
  title: cash-management-charge-payoff-line
  description: cash-management.charge-payoff-line API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Charge payoff lines
    description: Line items represent posted charge payoffs.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/cash-management/charge-payoff-line:
    get:
      summary: List charge payoff lines
      description: Returns a collection with a key, ID, and link for each charge payoff line.
      tags:
        - Charge payoff lines
      operationId: list-cash-management-charge-payoff-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of charge-payoff-line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of charge payoff lines:
                  value:
                    'ia::result':
                      - key: '1717'
                        id: '1717'
                        href: /objects/charge-payoff-line/1717
                      - key: '1718'
                        id: '1718'
                        href: /objects/charge-payoff-line/1718
                      - key: '1719'
                        id: '1719'
                        href: /objects/charge-payoff-line/1719
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/cash-management/charge-payoff-line/{key}':
    parameters:
      - name: key
        description: system-assigned unique key for the charge payoff line.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a charge payoff line
      description: Returns detailed information for a particular charge payoff line.
      tags:
        - Charge payoff lines
      operationId: get-cash-management-charge-payoff-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the charge-payoff-line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-charge-payoff-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the charge payoff line:
                  value:
                    'ia::result':
                      id: '1255'
                      key: '1255'
                      chargePayoff:
                        id: '628'
                        key: '628'
                        recordType: 'cx'
                        href: /objects/cash-management/charge-payoff/628
                      glAccount:
                        id: '33'
                        key: '33'
                        name: 'CalOil Card - USA'
                        href: /objects/general-ledger/account/33
                      amount: '500.00'
                      txnAmount: '500.00'
                      dimensions:
                        department:
                          key: '3'
                          id: '3'
                          name: 'Engineering'
                          href: '/objects/company-config/department/3'
                        location:
                          key: '1'
                          id: '1'
                          name: 'United States of America'
                          href: '/objects/company-config/location/1'
                        project:
                          key: '9'
                          id: '9'
                          name: 'Implementation - Logic Solutions'
                          href: '/objects/projects/project/9'
                        customer:
                          key: '2'
                          id: '2'
                          name: 'Logic Solutions'
                          href: '/objects/accounts-receivable/customer/2'
                        vendor:
                          key: ''
                          id: ''
                          name: ''
                        employee:
                          key: '1'
                          id: '1'
                          name: 'Reser'
                          href: '/objects/company-config/employee/1'
                        item:
                          key: '1'
                          id: '1'
                          name: 'PC Computer'
                          href: '/objects/inventory-control/item/1'
                        class:
                          key: ''
                          id: ''
                          name: ''
                      baseLocation:
                        name: 'United States of America'
                        key: '1'
                        href: /objects/company-config/location/1
                      description: 'Meals'
                      currency:
                        exchangeRate:
                          date: null
                          typeId: null
                          rate: 1.0
                        baseCurrency: USD
                        txnCurrency: USD
                      status: active
                      audit:
                        createdDateTime: '2024-04-19T16:28:50Z'
                        modifiedDateTime: '2024-04-19T16:28:50Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/cash-management/charge-payoff-line/1255
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    cash-management-charge-payoff-line:
      $ref: ../models/cash-management.charge-payoff-line.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml