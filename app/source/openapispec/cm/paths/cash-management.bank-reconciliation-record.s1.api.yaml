openapi: 3.0.0
info:
  title: cash-management-bank-reconciliation-record
  description: Bank reconciliation source records
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Reconciliation source records
    description: Bank reconciliation source records
servers:
  - url: 'https://dev09.intacct.com/users/neema.shetty/projects.bankFeedRestApi/api/v0'
paths:
  /objects/cash-management/bank-reconciliation-record:
    get:
      summary: Get reconciliation source record list
      description: Returns a collection of bank reconciliation records
      tags:
        - Reconciliation source records
      operationId: list-cash-management-bank-reconciliation-record
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Bank reconciliation source records
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-bank-reconciliation-record'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                List of source records:
                  value:
                    'ia::result':
                      - key: '306'
                        id: '306'
                        href: /objects/cash-management/bank-reconciliation-record/306
                      - key: '307'
                        id: '307'
                        href: /objects/cash-management/bank-reconciliation-record/307
                      - key: '308'
                        id: '308'
                        href: /objects/cash-management/bank-reconciliation-record/308
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/cash-management/bank-reconciliation-record/{key}:
    parameters:
      - name: key
        description: bank-reconciliation-record Key
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a bank reconciliation source record
      description: Get details for a particular bank reconciliation source record
      tags:
        - Reconciliation source records
      operationId: get-cash-management-bank-reconciliation-record-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Bank reconciliation source records
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-bank-reconciliation-record'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Source record by key example:
                  value:
                    'ia::result':
                      key: '2214'
                      id: '2214'
                      bankReconciliation:
                        key: '16'
                        id: '16'
                        href: /objects/cash-management/bank-reconciliation/16
                      bankAccount:
                        id: 'BOA'
                        key: '38'
                        href: /objects/cash-management/bank-account/38
                      txnInformation:
                        recordType: apPayment
                        subledgerRecord:
                          key: '1981'
                          id: '1981'
                          href: /objects/accounts-payable/subledger-record/1981
                        journalEntryLine:
                          key: null
                          id: null
                        intialOpenItem:
                          key: null
                          id: null
                        txnType: withdrawal
                        documentNumber: null
                        documentDate: '2021-05-26'
                        txnAmount: '482.88'
                        baseAmount: '482.88'
                        txnCurrency: USD
                        baseCurrency: USD
                        postingDate: '2021-05-26'
                        reconciliationInformation:
                          lastReconcileDate: null
                          state: unmatched
                        payee: V213
                        description: null
                        postingState: confirmed
                      audit:
                        createdDateTime: '2021-10-08T00:00:00Z'
                        modifiedDateTime: '2021-10-08T00:00:00Z'
                        createdBy: '1'
                        modifiedBy: null
                      href: /objects/cash-management/bank-reconciliation-record/2214
                    'ia::meta':
                      totalCount: 1
        '404':
          description: Not Found
components:
  schemas:
    cash-management-bank-reconciliation-record:
      $ref: ../models/cash-management.bank-reconciliation-record.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
