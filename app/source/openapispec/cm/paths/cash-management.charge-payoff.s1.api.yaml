openapi: 3.0.0
info:
  title: cash-management-charge-payoff
  description: cash-management.charge-payoff API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON> <PERSON>
    email: <EMAIL>
tags:
  - name: Charge payoffs
    description: Group of credit card transactions that you want to pay off.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/cash-management/charge-payoff:
    get:
      summary: List charge payoffs
      description: Returns a collection with a key, ID, and link for each charge payoff.
      tags:
        - Charge payoffs
      operationId: list-cash-management-charge-payoff
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of charge-payoff objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of charge payoffs:
                  value:
                    'ia::result':
                      - key: '1218'
                        id: '1218'
                        href: /objects/cash-management/charge-payoff/1218
                      - key: '1219'
                        id: '1219'
                        href: /objects/cash-management/charge-payoff/1219
                      - key: '1220'
                        id: '1220'
                        href: /objects/cash-management/charge-payoff/1220
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '201':
          description: Created
        '400':
          $ref: '#/components/responses/400error'
        '401':
          description: Unauthorized
    post:
      summary: Create a charge payoff
      description: Creates a new charge payoff.
      tags:
        - Charge payoffs
      operationId: create-cash-management-charge-payoff
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/cash-management-charge-payoff'
                - $ref: '#/components/schemas/cash-management-charge-payoffRequiredProperties'
            examples:
              Creates a charge payoff:
                value:
                  payoffDate: '2024-04-19'
                  description: 'A0000001 liability transferred to AP through api'
                  creditCardAccount:
                    id: 'A0000001'
                  referenceNumber: 'cc-000111'
                  attachment:
                    id: '18'
                  chargePayoffDetails:
                    - id: '124'
                      amountToPay: '700'
                    - id: '125'
                      amountToPay: '25'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New charge-payoff
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New charge payoff:
                  value:
                    'ia::result':
                      key: '501'
                      id: '501'
                      href: /objects/cash-management/charge-payoff/501
                      'ia::meta':
                        totalCount: 1
                        totalSuccess: 1
                        totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/cash-management/charge-payoff/{key}':
    parameters:
      - name: key
        description: system-assigned unique key for the charge payoff.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a charge payoff
      description: Returns detailed information for a particular charge payoff.
      tags:
        - Charge payoffs
      operationId: get-cash-management-charge-payoff-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the charge-payoff
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-charge-payoff'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the charge payoff:
                  value:
                    'ia::result':
                      id: '628'
                      key: '628'
                      vendor:
                        id: 'CAL'
                        key: '43'
                        href: '/objects/accounts-payable/vendor/43'
                      creditCardAccount:
                        id: 'A0000001'
                        key: '1'
                        href: '/objects/cash-management/credit-card-account/1'
                      payoffDate: '2024-04-19'
                      reversedBy:
                        id: null
                        key: null
                        reversalDate: null
                      reversalOf:
                        id: null
                        key: null
                        txnDate: null
                      state: 'confirmed'
                      reconciliationState: 'uncleared'
                      totalEntered: '725.00'
                      txnTotalEntered: '725.00'
                      currency:
                        baseCurrency: USD
                        txnCurrency: USD
                      audit:
                        createdDateTime: '2024-04-19T16:28:50Z'
                        modifiedDateTime: '2024-04-19T16:28:50Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      bill:
                        id: '346'
                        key: '346'
                        href: /objects/accounts-payable/bill/346
                      referenceNumber: 'cc-000111'
                      description: 'A0000001 liability transferred to AP'
                      txnPaidDate: '2024-04-19'
                      attachment:
                        key: '18'
                        id: 'Attach-01'
                        href: /objects/company-config/attachment/18
                      lines:
                        - id: '1253'
                          key: '1253'
                          chargePayoff:
                            id: '628'
                            key: '628'
                            recordType: 'cx'
                            href: /objects/cash-management/charge-payoff/628
                          glAccount:
                            id: '33'
                            key: '33'
                            name: 'CalOil Card - USA'
                            href: /objects/general-ledger/account/33
                          amount: '25.00'
                          txnAmount: '25.00'
                          dimensions:
                            department:
                              key: ''
                              id: ''
                              name: ''
                            location:
                              key: '1'
                              id: '1'
                              name: 'United States of America'
                              href: '/objects/company-config/location/1'
                            project:
                              key: ''
                              id: ''
                              name: ''
                            customer:
                              key: ''
                              id: ''
                              name: ''
                            vendor:
                              key: ''
                              id: ''
                              name: ''
                            employee:
                              key: ''
                              id: ''
                              name: ''
                            item:
                              key: ''
                              id: ''
                              name: ''
                            class:
                              key: ''
                              id: ''
                              name: ''
                          baseLocation:
                            name: 'United States of America'
                            key: '1'
                            href: /objects/company-config/location/1
                          description: 'Finance charge'
                          currency:
                            exchangeRate:
                              date: null
                              typeId: null
                              rate: 1.0
                            baseCurrency: USD
                            txnCurrency: USD
                          status: active
                          audit:
                            createdDateTime: '2024-04-19T16:28:51Z'
                            modifiedDateTime: '2024-04-19T16:28:51Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          href: /objects/cash-management/charge-payoff-line/1253
                        - id: '1254'
                          key: '1254'
                          chargePayoff:
                            id: '628'
                            key: '628'
                            recordType: 'cx'
                            href: /objects/cash-management/charge-payoff/628
                          glAccount:
                            id: '33'
                            key: '33'
                            name: 'CalOil Card - USA'
                            href: /objects/general-ledger/account/33
                          amount: '200.00'
                          txnAmount: '200.00'
                          dimensions:
                            department:
                              key: '3'
                              id: '3'
                              name: 'Engineering'
                              href: '/objects/company-config/department/3'
                            location:
                              key: '1'
                              id: '1'
                              name: 'United States of America'
                              href: '/objects/company-config/location/1'
                            project:
                              key: ''
                              id: ''
                              name: ''
                            customer:
                              key: ''
                              id: ''
                              name: ''
                            vendor:
                              key: ''
                              id: ''
                              name: ''
                            employee:
                              key: ''
                              id: ''
                              name: ''
                            item:
                              key: ''
                              id: ''
                              name: ''
                            class:
                              key: ''
                              id: ''
                              name: ''
                          baseLocation:
                            name: 'United States of America'
                            key: '1'
                            href: /objects/company-config/location/1
                          description: 'Gasoline'
                          currency:
                            exchangeRate:
                              date: null
                              typeId: null
                              rate: 1.0
                            baseCurrency: USD
                            txnCurrency: USD
                          status: active
                          audit:
                            createdDateTime: '2024-04-19T16:28:50Z'
                            modifiedDateTime: '2024-04-19T16:28:50Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          href: /objects/cash-management/charge-payoff-line/1254
                        - id: '1255'
                          key: '1255'
                          chargePayoff:
                            id: '628'
                            key: '628'
                            recordType: 'cx'
                            href: /objects/cash-management/charge-payoff/628
                          glAccount:
                            id: '33'
                            key: '33'
                            name: 'CalOil Card - USA'
                            href: /objects/general-ledger/account/33
                          amount: '500.00'
                          txnAmount: '500.00'
                          dimensions:
                            department:
                              key: '3'
                              id: '3'
                              name: 'Engineering'
                              href: '/objects/company-config/department/3'
                            location:
                              key: '1'
                              id: '1'
                              name: 'United States of America'
                              href: '/objects/company-config/location/1'
                            project:
                              key: '9'
                              id: '9'
                              name: 'Implementation - Logic Solutions'
                              href: '/objects/projects/project/9'
                            customer:
                              key: '2'
                              id: '2'
                              name: 'Logic Solutions'
                              href: '/objects/accounts-receivable/customer/2'
                            vendor:
                              key: ''
                              id: ''
                              name: ''
                            employee:
                              key: '1'
                              id: '1'
                              name: 'Reser'
                              href: '/objects/company-config/employee/1'
                            item:
                              key: '1'
                              id: '1'
                              name: 'PC Computer'
                              href: '/objects/inventory-control/item/1'
                            class:
                              key: ''
                              id: ''
                              name: ''
                          baseLocation:
                            name: 'United States of America'
                            key: '1'
                            href: /objects/company-config/location/1
                          description: 'Meals'
                          currency:
                            exchangeRate:
                              date: null
                              typeId: null
                              rate: 1.0
                            baseCurrency: USD
                            txnCurrency: USD
                          status: active
                          audit:
                            createdDateTime: '2024-04-19T16:28:50Z'
                            modifiedDateTime: '2024-04-19T16:28:50Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          href: /objects/cash-management/charge-payoff-line/1255
                      chargePayoffDetails:
                        - id: '587'
                          key: '587'
                          payee: 'Vendor 1'
                          description: 'Expenses'
                          txnType: 'Charge Card Transaction'
                          totalEntered: '700.00'
                          totalDue: '0.00'
                          txnTotalEntered: '700.00'
                          txnTotalDue: '0.00'
                          amountPaid: '700'
                          txnAmountPaid: '700'
                          currency:
                            exchangeRate:
                              date: null
                              typeId: null
                              rate: 1.0
                            baseCurrency: USD
                            txnCurrency: USD
                          chargePayoff:
                            id: '628'
                            key: '628'
                            href: /objects/cash-management/charge-payoff/628
                          href: /objects/cash-management/charge-payoff-detail/587
                        - id: '588'
                          key: '588'
                          payee: ''
                          description: 'Charges'
                          txnType: 'Credit Card Finance Charge/Fee'
                          totalEntered: '25.00'
                          totalDue: '0.00'
                          txnTotalEntered: '25.00'
                          txnTotalDue: '0.00'
                          amountPaid: '25'
                          txnAmountPaid: '25'
                          currency:
                            exchangeRate:
                              date: null
                              typeId: null
                              rate: 1.0
                            baseCurrency: USD
                            txnCurrency: USD
                          chargePayoff:
                            id: '628'
                            key: '628'
                            href: /objects/cash-management/charge-payoff/628
                          href: /objects/cash-management/charge-payoff-detail/588
                      href: /objects/cash-management/charge-payoff/628
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  /workflows/cash-management/charge-payoff/reverse:
    post:
      summary: Reverse a charge payoff
      description: Reverse a charge payoff
      tags:
        - Charge payoffs
      operationId: reverse-cash-management-charge-payoff
      requestBody:
        content:
          application/json:
            schema:
              $ref: ../workflows/cash-management.charge-payoff.s1.workflows.yaml#/cash-management-charge-payoff-actions-reverse-request
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: ../workflows/cash-management.charge-payoff.s1.workflows.yaml#/cash-management-charge-payoff-actions-reverse-response
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    cash-management-charge-payoff:
      $ref: ../models/cash-management.charge-payoff.s1.schema.yaml
    cash-management-charge-payoffRequiredProperties:
      type: object
      required:
        - payoffDate
      properties:
        creditCardAccount:
          type: object
          required:
            - id
        chargePayoffDetails:
          type: array
          items:
            required:
              - id
              - amountToPay
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml