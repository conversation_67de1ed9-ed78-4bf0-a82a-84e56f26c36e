uiLabel: IA.CREDIT_CARD_TRANSACTION_TEMPLATE
fields:
  id:
    uiType: text
    uiLabel: IA.ID
  name:
    uiType: text
    uiLabel: IA.NAME
  description:
    uiType: text
    uiLabel: IA.DESCRIPTION
  numberOfRulesUsingTemplate:
    uiType: integer
    uiLabel: IA.NO_OF_RULES
    enumsLabels:
      -
        label: IA.ACTIVE
        value: active
      -
        label: IA.INACTIVE
        value: inactive
  status:
    uiType: enum
    uiLabel: IA.STATUS
    enumsLabels:
      -
        label: IA.ACTIVE
        value: active
      -
        label: IA.INACTIVE
        value: inactive
groups:
  audit:
    fields:
      createdBy:
        uiType: text
        uiLabel: IA.CREATED_BY
      createdDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_CREATED
      modifiedBy:
        uiType: text
        uiLabel: IA.MODIFIED_BY
      modifiedDateTime:
        uiType: timestamp
        uiLabel: IA.WHEN_MODIFIED
    refs:
      createdByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.CREATED_BY
          id:
            uiType: text
            uiLabel: IA.CREATED_BY_LOGIN_ID
      modifiedByUser:
        fields:
          key:
            uiType: integer
            uiLabel: IA.MODIFIED_BY
          id:
            uiType: text
            uiLabel: IA.MODIFIED_BY_LOGIN_ID
refs:
  entity:
    fields:
      id:
        uiType: text
        uiLabel: IA.ENTITY_ID
      key:
        uiType: integer
        uiLabel: IA.ENTITY_KEY