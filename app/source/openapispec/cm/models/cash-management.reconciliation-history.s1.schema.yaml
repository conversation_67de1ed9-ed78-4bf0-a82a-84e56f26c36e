title: cash-management-reconciliation-history
x-mappedTo: reconhistory
type: object
description: Bank and credit card reconciliation history record.
properties:
  key:
    type: string
    description: System-assigned key for the reconciliation history object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Identifier for the reconciliation history object. This value is the same as key for this object and can be ignored. Use key for all references to this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the reconciliation history object.
    readOnly: true
    example: /objects/cash-management/reconciliation-history/23
  financialEntity:
    type: object
    description: Financial account from which the reconciliation is done.
    x-mappedTo: bankaccount
    x-object: cash-management/bank-account
    properties:
      key:
        type: string
        description: System-assigned key for the bank account.
        x-mappedTo: FINACCTKEY
        readOnly: true
        example: '1002'
      id:
        type: string
        description: Account number or other unique identifier for the bank account.
        x-mappedTo: FINACCTID
        readOnly: true
        example: BOA
      accountType:
        type: string
        description: Type of bank account.
        x-mappedTo: FINACCTTYPE
        readOnly: true
        enum:
          - checking
          - savings
          - creditCard
        x-mappedToValues:
          - CHECKING
          - SAVINGS
          - Credit Card
        example: checking
      name:
        type: string
        description: Bank or other financial institution name.
        x-mappedTo: FINACCTNAME
        readOnly: true
        example: Bank of America
      href:
        type: string
        description: URL for the bank account.
        readOnly: true
        example: /objects/cash-management/bank-account/10
  reconciliationDate:
    type: string
    format: date
    description: Date of reconciliation.
    x-mappedTo: RECDATE
    readOnly: true
    example: '2024-03-31'
  reconciliationBalance:
    type: string
    format: decimal-precision-2
    description: Reconciliation amount.
    x-mappedTo: RECBAL
    readOnly: true
    example: '10500.78'
  reconciledBy:
    type: object
    description: User who reconciled this.
    x-mappedTo: userinfo
    x-object: company-config/user
    readOnly: true
    properties:
      key:
        type: string
        x-mappedTo: LOGINKEY
        readOnly: true
        description: System-assigned key for the reconciled-by user
        example: "2"
      id:
        type: string
        x-mappedTo: LOGINID
        readOnly: true
        description: Reconciled-by user ID.
        example: "JSmith"
      href:
        type: string
        readOnly: true
        description: URL endpoint of the reconciled-by user
        example: /objects/company-config/user/2
  createdDateTime:
    type: string
    format: date-time
    description: Created date for this reconciliation .
    x-mappedTo: CREATED
    example: '2024-04-20T16:20:00Z'
  feedType:
    type: string
    x-mappedTo: FILETYPE
    description: |
      Bank transaction feed source for this reconciliation.
      **Valid values:**
      - `online` - Online bank feed through Sage Cloud Services.
      - `xml` - Transactions are in XML format.
      - `csv` - Transactions are in CSV format.
      - `qif` - Transactions are in QIF format.
    enum:
      - null
      - xml
      - online
      - csv
      - qif
    x-mappedToValues:
      - ''
      - xml
      - onl
      - csv
      - qif
    example: online
    nullable: true
    default: null
  isReopened:
    type: boolean
    x-mappedTo: REOPENED
    description: Reconciliation reopen status.
    default: false
    readOnly: true
    example: false
  reconciliationStatus:
    $ref: reconciliation-status.s1.schema.yaml
    x-mappedTo: STATE
    readOnly: true
  location:
    type: object
    description: Location where the reconciliation was created.
    readOnly: true
    x-mappedTo: location
    x-object: company-config/location
    properties:
      key:
        type: string
        description: System-assigned key for the location.
        readOnly: true
        x-mappedTo: LOCATIONKEY
        example: '1'
        nullable: true
      id:
        type: string
        description: Identifier for the location.
        readOnly: true
        x-mappedTo: LOCATIONID
        example: USA
        nullable: true
      name:
        type: string
        description: Name of the location.
        readOnly: true
        x-mappedTo: LOCATIONNAME
        example: 'USA'
        nullable: true
      href:
        type: string
        description: URL for the location.
        readOnly: true
        example: /objects/company-config/location/1
  attachment:
    type: object
    x-mappedTo: supdoc
    x-object: company-config/attachment
    properties:
      href:
        type: string
        description: Endpoint for the supporting document.
        readOnly: true
        example: /objects/supdoc/23
      key:
        type: string
        description: Supporting document key.
        x-mappedTo: SUPDOCKEY
        example: '21'
      id:
        type: string
        description: Supporting document ID.
        x-mappedTo: SUPDOCID
        example: 'Recon 1'
  status:
    $ref: ../../common/models/status.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml