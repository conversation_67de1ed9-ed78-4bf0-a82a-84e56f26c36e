title: cash-management-undeposited-fund
x-mappedTo: undepositedfunds
type: object
description: Transaction record for undeposited fund
properties:
  key:
    type: string
    description: System-assigned key for the undeposited fund.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Undeposited funds ID.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the undeposited fund.
    readOnly: true
    example: /objects/cash-management/undeposited-fund/23
  customer:
    type: object
    description: Customer associated with the deposit.
    x-mappedTo: customer
    x-object: accounts-receivable/customer
    readOnly: true
    properties:
      key:
        type: string
        description: System-assigned key for the customer.
        readOnly: true
        x-mappedTo: CUSTOMERKEY
        example: '23'
      id:
        type: string
        description: Identifier for the customer.
        readOnly: true
        x-mappedTo: CUSTOMERID
        maxLength: 21
        example: 'Cust-1'
      name:
        type: string
        description: Name of the customer.
        readOnly: true
        x-mappedTo: CUSTOMERNAME
        example: <PERSON>
      href:
        type: string
        description: URL for the customer.
        example: /objects/accounts-receivable/customer/23
        readOnly: true
  payee:
    type: string
    description: Name of the payee.
    x-mappedTo: PAYEE
    readOnly: true
    example: 'Alter Windows'
  totalEntered:
    type: string
    format: decimal-precision-2
    description: Total base amount
    x-mappedTo: TOTALENTERED
    readOnly: true
    example: '100.99'
  txnTotalEntered:
    type: string
    format: decimal-precision-2
    description: Total transaction amount
    x-mappedTo: TRX_TOTALENTERED
    readOnly: true
    example: '100.99'
  currency:
    type: string
    description: Transaction currency.
    x-mappedTo: CURRENCY
    readOnly: true
    example: USD
  paymentMethod:
    type: string
    description: Payment method for the transaction.
    x-mappedTo: PAYMETHOD
    readOnly: true
    example: cash
    enum:
      - check
      - chargeCard
      - onlineChargeCard
      - recordTransfer
      - cash
    x-mappedToValues:
      - Check
      - Charge card
      - Online Charge Card
      - Record transfer
      - Cash
  documentNumber:
    type: string
    description: Transaction number.
    x-mappedTo: DOCNUMBER
    readOnly: true
    example: 'Check 0019'
  audit:
    readOnly: true
    $ref: ../../common/models/audit.s1.schema.yaml