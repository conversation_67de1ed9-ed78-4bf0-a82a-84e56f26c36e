title: cash-management-reconciliation-record-map
type: object
x-mappedTo: acctfinrecordmap
description: Reconciliation record map
properties:
  key:
    type: string
    description: System-assigned key for the reconciliation record map.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '11'
  id:
    type: string
    description: ID for the reconciliation record map.
    x-mappedTo: RECORDNO
    readOnly: true
    example: '11'
  reconciliationSourceRecord:
    type: object
    description: Reconciliation source record
    x-object: cash-management/reconciliation-source-record
    readOnly: true
    properties:
      key:
        type: string
        description: System-assigned key for the reconciliation source record.
        x-mappedTo: ACCTRECONRECORDKEY
        example: '110'
      id:
        type: string
        description: ID for the reconciliation source record.
        x-mappedTo: ACCTRECONRECORDKEY
        example: '110'
      href:
        type: string
        description: URL for the reconciliation source record.
        readOnly: true  
        example: /objects/cash-management/reconciliation-source-record/110
  bankTxnRecord:
    type: object
    description: Bank transaction record
    x-object: cash-management/bank-transaction
    readOnly: true
    properties:
      key:
        type: string
        description: System-assigned key for the bank transaction.
        x-mappedTo: FINACCTTXNRECORDKEY
        example: '201'
      id:
        type: string
        description: ID for the bank transaction.
        x-mappedTo: FINACCTTXNRECORDKEY
        example: '201'
      href:
        type: string
        description: URL for the bank transaction.
        readOnly: true 
        example: /objects/cash-management/bank-transaction/201
  matchRuleKey:
    type: string
    description: Key for the matching rule.
    x-mappedTo: MATCHRULEKEY
    readOnly: true
    example: '3'
  matchedAmount:
    type: string
    format: decimal-precision-2
    description: Transaction amount to match.
    x-mappedTo: AMOUNT
    readOnly: true
    example: '10.00'
  matchType:
    type: string
    description: Type of matching for reconciliation.
    x-mappedTo: MATCHMODE
    readOnly: true
    enum:
      - manual
      - automatch
    x-mappedToValues:
      - Manual
      - Automatch
    example: automatch
  matchedStatus:
    type: string
    description: Matched status of the transaction.
    x-mappedTo: STATUS
    readOnly: true
    enum:
      - matched
      - unmatched
    x-mappedToValues:
      - 'true'
      - 'false'
    example: matched
  bankTxnMatchRuleKey:
    type: string
    description: Key for the bank transaction match rule.
    x-mappedTo: MATCHRULEKEY
    readOnly: true
    example: '5'
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml