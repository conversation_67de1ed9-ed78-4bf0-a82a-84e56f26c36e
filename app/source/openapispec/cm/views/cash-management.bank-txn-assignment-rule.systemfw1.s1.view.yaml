key: cash-management/bank-txn-assignment-rule::systemfw1
id: systemfw1
object: cash-management/bank-txn-assignment-rule
name: All
description: Specifies all active cash-management/bank-txn-assignment-rules
query:
  object: cash-management/bank-txn-assignment-rule
  fields:
    - ruleId
    - name
    - description
    - customer.id
    - customer.name
    - audit.createdDateTime
    - audit.modifiedDateTime
    - status
  orderBy:
    - ruleId: asc
  filters:
    - $eq:
        status: active
  metadata:
    frozenColumnsCount: 2
contexts:
  - __default