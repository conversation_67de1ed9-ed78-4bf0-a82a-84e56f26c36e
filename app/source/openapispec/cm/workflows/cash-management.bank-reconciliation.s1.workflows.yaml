actions:
  reopen:
    request:
      type: object
      x-mappedTo: bankacctrecon
      properties:
        key:
          type: string
          description: System-assigned unique record key for the bank reconciliation.
          x-mappedTo: RECORDNO
          example: '12'
        bankAccountId:
          type: string
          description: ID of the bank account which is reconciled.
          x-mappedTo: FINANCIALENTITY
          example: 'BOA'
        reconciliationDate:
          type: string
          format: date
          description: Date this transaction is reconciled.
          x-mappedTo: RECONDATE
          example: '2024-01-23'
      required:
        - key
        - bankAccountId
        - reconciliationDate
    response:
      type: object
      x-mappedTo: bankacctrecon
      properties:
        key:
          type: string
          description: System-assigned unique record key for the bank reconciliation.
          x-mappedTo: RECORDNO
          readOnly: true
          example: '12'
        bankAccountId:
          type: string
          description: ID of the bank account which is reconciled.
          x-mappedTo: FINANCIALENTITY
          readOnly: true
          example: 'BOA'
        state:
          type: string
          description: Bank reconciliation state
          x-mappedTo: STATE
          readOnly: true
          example: reopened
        message:
          type: string
          description: Bank reconciliation reopen message about number of periods reopened and duration
          x-mappedTo: MESSAGE
          readOnly: true
          example: 'Number of reconciliations to reopen and reconcile again: 1, from 05/31/2024 to 06/05/2024'
