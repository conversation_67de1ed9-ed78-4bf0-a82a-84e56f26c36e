<?php
/**
 * DdsHistoryLister.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation, All Rights Reserved
 */
import('NLister');

/**
 * Class DdsScheduleLister
 *
 * Lister class for Data Delivery Service Schedule
 */
class DdsScheduleLister extends NLister
{

    function __construct()
    {
        $tokens = [['id' => 'IA.NAME'],
                   ['id' => 'IA.FREQUENCY'],
                   ['id' => 'IA.NEXT_DELIVERY'],
                   ['id' => 'IA.ENABLE']];
        I18N::addTokens($tokens);
        $tokenMap = I18N::getText();
        parent::__construct(
            array(
                'entity'        =>  'ddsschedule',
                'title'         =>    'IA.AUTOMATIC',
                'fields'        =>  array('NAME', 'FREQUENCY', 'SCHEDULED_TIME', 'ENABLED' ),
                'fieldlabels'   =>  array($tokenMap['IA.NAME'],
                                          $tokenMap['IA.FREQUENCY'],
                                          $tokenMap['IA.NEXT_DELIVERY'],
                                          $tokenMap['IA.ENABLE'])),
        );
    }

    /**
     * Generate the Table
     * Override parent
     */
    function BuildTable()
    {
        parent::BuildTable();
    }

}
