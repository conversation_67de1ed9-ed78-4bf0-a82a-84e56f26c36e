<?php
/**
 * File DdsDispatcher.cls contains the class DdsDispatcher
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
require_once 'DdsJobRunner.cls';
require_once 'DdsJob.cls';
/**
 * Class DdsDispatcher
 * Used to run DDS Jobs
 */
class DdsDispatcher
{

    const DDS_ONLINE = 'online';
    const DDS_OFFLINE = 'offline';

    const DDS_LARGE_OBJECTS = [
        'GLACCOUNTBALANCE'
    ];

    /* @var string $deliveryMode */
    private $deliveryMode;

    /**
     * @param string $deliveryMode delivery mode
     *
     * @throws DdsException
     */
    public function __construct($deliveryMode = self::DDS_OFFLINE)
    {
        if ($deliveryMode != self::DDS_OFFLINE && $deliveryMode != self::DDS_ONLINE) {
            throw new DdsException("Invalid delivery mode $deliveryMode.");
        }
        $this->deliveryMode = $deliveryMode;
    }

    /**
     * Given a ddsJob, do it.
     *
     * @param DdsJob $ddsJob ddsjob object
     *
     * @throws DdsException
     *
     * @return bool
     */
    public function adHocJob(DdsJob $ddsJob)
    {
        global $gManagerFactory;

        $ddsJobMgr = $gManagerFactory->getManager('ddsjob');
        //manual, scheduled and API jobs go through here
        $filter1 = ['JOBTYPE', "= 'all'"];
        $filter2 = ['OBJECT', '=', $ddsJob->getDdsSubscription()->getObject()];
        $filter3 = [
            'operator' => 'AND',
            'filters' => [
                [
                    'operator' => 'OR',
                    'filters'  => [
                        ['STATUS', "= 'Q'"],
                        ['STATUS', "= 'P'"],
                    ],
                ],
                [
                    'operator' => 'OR',
                    'filters'  => [
                        ['QUEUETIME', ">= sysdate - 7"],
                        ['STARTTIME', ">= sysdate - 7"],
                    ]
                ]
            ]
        ];
        $queryParams['filters'] = array (array ( $filter1, $filter2, $filter3));

        $jobs = $ddsJobMgr->GetList($queryParams);
        if ($jobs) {
            // we already have a job
            $ddsJob->setStatus(DdsJobManager::DDS_CANCELLED_STATUS);
            // I18N : TODO
            $ddsJob->setError(I18N::getSingleToken('IA.A_DDS_JOB_FOR_THIS_OBJECT_ALREADY_EXISTS_IN_QUEUE'));

            return false;
        }

        if (!$ddsJob->isJobTypeAllowedForObject($timeIntervalMessage, $timeDelayMessage)) {
            $jobTypeName = $ddsJob->getJobType() === 'all' ? 'all data' : 'changed data';
            $errMessage = "This job requesting " . $jobTypeName . " on " . $ddsJob->getDdsSubscription()->getObject()
                            . " " . $timeIntervalMessage . " " . $timeDelayMessage;
            throw new DdsException($errMessage, $ddsJob);
        }

        // create the DDS record
        $ddsJobObj = $ddsJob->toEntity();

        if (!$ddsJobMgr->add($ddsJobObj)) {
            throw new DdsException("Failed to persist object. Check gErr", $ddsJob);
        }

        if (DDS_DEBUG == 1) {
            impp('$ddsJobObj', pp($ddsJobObj));
        }

        // no point in creating an ims package when the state is cancelled
        if ($ddsJobObj['STATUS'] !== DdsJobManager::DDS_CANCELLED_STATUS) {
            $ddsJob->setRecordNo($ddsJobObj['RECORDNO']);
            if ($this->deliveryMode === self::DDS_OFFLINE) {
                return $this->runOffline($ddsJob);
            } else {
                return $this->runOnline($ddsJob);
            }
        }

        return true;
    }

    /**
     * Run a DDS job online
     *
     * @param DdsJob $ddsJob ddsjob object
     *
     * @return bool
     */
    private function runOnline(DdsJob $ddsJob)
    {
        return DdsJobRunner::runJob($ddsJob);
    }

    /**
     * Run a DdsJob offline
     *
     * @param DdsJob $ddsJob ddsjob object
     *
     * @return bool
     */
    public function runOffline(DdsJob $ddsJob)
    {
        $imsPub = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);
        $fileList = $ddsJob->getFileList();
        $ddsJob->prepareForSerialization();

        if (DdsSubscription::isDdsLargeQueueSeparatedFromDDSPackagesEnabled()) {
            $imsType = in_array(strtoupper($ddsJob->getDdsSubscription()->getObject()), self::DDS_LARGE_OBJECTS)
                ? IMS_TYPE_LARGE : IMS_TYPE_NORMAL;
        } else {
            $imsType = IMS_TYPE_LARGE;
        }

        $result = $imsPub->PublishMsg(
            'DDSDISPATCHER',
            'DDS',
            'RUNDDSJOB',
            IMS_PRIORITY_DEFAULT,
            ['ddsJob' => serialize($ddsJob)],
            [],
            $response,
            0,
            $imsType
        );
        $ddsJob->addFileList($fileList);
        return $result;
    }

}
