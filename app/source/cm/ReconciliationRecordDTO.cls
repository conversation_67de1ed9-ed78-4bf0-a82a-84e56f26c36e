<?php
/**
 * Data object to process the intacct transaction records.
 *
 * <AUTHOR>
 * @copyright 2018 Sage Intacct Inc., All Rights Reserved
 */
class ReconciliationRecordDTO extends BaseDTO
{
    /* @var string $recordNo */
    private $recordNo;
    /* @var string $acctReconNo */
    private $acctReconNo;
    /* @var string $acctReconRecordNo */
    private $acctReconRecordNo;
    /* @var string $recordKey */
    private $financialEntity;
    /* @var  string $reconDate */
    private $recordKey;
    /* @var string $recordType */
    private $recordType;
    /* @var string $prrecordNo */
    private $prrecordNo;
    /* @var string $glentryNo */
    private $glentryNo;
    /* @var string $ioitemsNo */
    private $ioitemsNo;
    /* @var string $sioitemsNo */
    private $sioitemsNo;
    /* @var string $trxType */
    private $trxType;
    /* @var string $docNo */
    private $docNo;
    /* @var string $docDate */
    private $docDate;
    /* @var float|string $amount */
    private $amount;
    /* @var float|string $baseAmount */
    private $baseAmount;
    /* @var string $currency */
    private $currency;
    /* @var string $baseCurrency */
    private $baseCurrency;
    /* @var string $recordNo */
    private $postingDate;
    /* @var string $payee */
    private $payee;
    /* @var string $description */
    private $description;
    /* @var string $cleared */
    private $cleared;
    /* @var string $state */
    private $state;
    /* @var  string $whenCreated */
    private $whenCreated;
    /* @var  string $whenModified */
    private $whenModified;
    /* @var  string $createdBy */
    private $createdBy;
    /* @var  string $modifiedBy */
    private $modifiedBy;
    /* @var string $recDate */
    private $recDate;

    /**
     * ReconciliationRecordDTO constructor.
     *
     * @param array $arrParams Array of attributes
     */
    public function __construct(array $arrParams)
    {
        // set the base array params value
        $this->arrayParams = $arrParams;
        $this->recordNo = $arrParams['RECORDNO'] ?? $arrParams['RECORD#'];
        $this->acctReconNo = $arrParams['BANKACCTRECONKEY'];
        $this->acctReconRecordNo = $arrParams['ACCTRECONRECORDKEY'];
        $this->financialEntity = $arrParams['FINANCIALENTITY'];
        $this->recordKey = $arrParams['RECORDKEY'];
        $this->recordType = $arrParams['RECORDTYPE'];
        $this->prrecordNo = $arrParams['PRRECORDKEY'] ?? null;
        $this->glentryNo = $arrParams['GLENTRYKEY'] ?? null;
        $this->ioitemsNo = $arrParams['IOITEMSKEY'] ?? null;
        $this->sioitemsNo = $arrParams['SIOITEMSKEY'] ?? null;
        $this->trxType = $arrParams['TRANSACTIONTYPE'];
        $this->docNo = $arrParams['DOCNO'] ?? null;
        $this->docDate = $arrParams['DOCDATE'] ?? null;
        $this->amount = $arrParams['TRX_AMOUNT'] ?? $arrParams['TRX_TOTALENTERED'];
        $this->baseAmount = $arrParams['AMOUNT'] ?? $arrParams['TOTALENTERED'];
        $this->currency = $arrParams['CURRENCY'];
        $this->baseCurrency = $arrParams['BASECURRENCY'] ?? $arrParams['BASECURR'];
        $this->postingDate = $arrParams['POSTINGDATE'];
        $this->payee = $arrParams['PAYEE'];
        $this->description = $arrParams['DESCRIPTION'];
        $this->cleared = $arrParams['CLEARED'];
        $this->state = $arrParams['STATE'];
        $this->setSelection($this->cleared);
        $this->whenCreated = $arrParams['WHENCREATED'] ?? null;
        $this->whenModified = $arrParams['WHENMODIFIED'] ?? null;
        $this->createdBy = $arrParams['CREATEDBY'] ?? null;
        $this->modifiedBy = $arrParams['MODIFIEDBY'] ?? null;
        $this->recDate = $arrParams['RECDATE'];
        // Set transaction flag voided flag - this is needed to differenciate from reversed txn
        if ($arrParams['STATE'] == 'V' &&
            (isset($arrParams['DOCNO']) && strrpos($arrParams['DOCNO'], 'Voided') !== false)) {
            $this->arrayParams['VOIDED'] = true;
        }
    }

    /**
     * @return string
     */
    public function getRecordNo()
    {
        return $this->recordNo;
    }

    /**
     * @param string $recordNo
     */
    public function setRecordNo($recordNo)
    {
        $this->recordNo = $recordNo;
    }

    /**
     * @return string
     */
    public function getReconDate()
    {
        return $this->recDate;
    }

    /**
     * @param string $recDate
     */
    public function setReconDate($recDate)
    {
        $this->recDate = $recDate;
    }

    /**
     * @return string
     */
    public function getAcctReconNo()
    {
        return $this->acctReconNo;
    }

    /**
     * @param string $acctReconNo
     */
    public function setAcctReconNo($acctReconNo)
    {
        $this->acctReconNo = $acctReconNo;
    }

    /**
     * @return string
     */
    public function getAcctReconRecordNo()
    {
        return $this->acctReconRecordNo;
    }

    /**
     * @param string $acctReconRecordNo
     */
    public function setAcctReconRecordNo(string $acctReconRecordNo)
    {
        $this->acctReconRecordNo = $acctReconRecordNo;
    }

    /**
     * @return string
     */
    public function getFinancialEntity()
    {
        return $this->financialEntity;
    }

    /**
     * @param string $financialEntity
     */
    public function setFinancialEntity(string $financialEntity)
    {
        $this->financialEntity = $financialEntity;
    }

    /**
     * @return string
     */
    public function getRecordKey()
    {
        return $this->recordKey;
    }

    /**
     * @param string $recordKey
     */
    public function setRecordKey($recordKey)
    {
        $this->recordKey = $recordKey;
    }

    /**
     * @return string
     */
    public function getRecordType()
    {
        return $this->recordType;
    }

    /**
     * @param string $recordType
     */
    public function setRecordType($recordType)
    {
        $this->recordType = $recordType;
    }

    /**
     * @return string
     */
    public function getPrrecordNo()
    {
        return $this->prrecordNo;
    }

    /**
     * @param string $prrecordNo
     */
    public function setPrrecordNo($prrecordNo)
    {
        $this->prrecordNo = $prrecordNo;
    }

    /**
     * @return string
     */
    public function getGlentryNo()
    {
        return $this->glentryNo;
    }

    /**
     * @param string $glentryNo
     */
    public function setGlentryNo($glentryNo)
    {
        $this->glentryNo = $glentryNo;
    }

    /**
     * @return string
     */
    public function getIoitemsNo()
    {
        return $this->ioitemsNo;
    }

    /**
     * @param string $ioitemsNo
     */
    public function setIoitemsNo($ioitemsNo)
    {
        $this->ioitemsNo = $ioitemsNo;
    }

    /**
     * @return string
     */
    public function getSioitemsNo()
    {
        return $this->sioitemsNo;
    }

    /**
     * @param string $sioitemsNo
     */
    public function setSioitemsNo($sioitemsNo)
    {
        $this->sioitemsNo = $sioitemsNo;
    }

    /**
     * @return string
     */
    public function getTrxType()
    {
        return $this->trxType;
    }

    /**
     * @param string $trxType
     */
    public function setTrxType($trxType)
    {
        $this->trxType = $trxType;
    }

    /**
     * @return string
     */
    public function getDocNo()
    {
        return $this->docNo;
    }

    /**
     * @param string $docNo
     */
    public function setDocNo($docNo)
    {
        $this->docNo = $docNo;
    }

    /**
     * @return string
     */
    public function getDocDate()
    {
        return $this->docDate;
    }

    /**
     * @param string $docDate
     */
    public function setDocDate($docDate)
    {
        $this->docDate = $docDate;
    }

    /**
     * @return float|string
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param float|string $amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    /**
     * @return float|string
     */
    public function getBaseAmount()
    {
        return $this->baseAmount;
    }

    /**
     * @param float|string $baseAmount
     */
    public function setBaseAmount($baseAmount)
    {
        $this->baseAmount = $baseAmount;
    }

    /**
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * @return string
     */
    public function getBaseCurrency()
    {
        return $this->baseCurrency;
    }

    /**
     * @param string $baseCurrency
     */
    public function setBaseCurrency($baseCurrency)
    {
        $this->baseCurrency = $baseCurrency;
    }

    /**
     * @return string
     */
    public function getPostingDate()
    {
        return $this->postingDate;
    }

    /**
     * @param string $postingDate
     */
    public function setPostingDate($postingDate)
    {
        $this->postingDate = $postingDate;
    }

    /**
     * @return string
     */
    public function getPayee()
    {
        return $this->payee;
    }

    /**
     * @param string $payee
     */
    public function setPayee($payee)
    {
        $this->payee = $payee;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param string $description
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

    /**
     * @return string
     */
    public function getCleared()
    {
        return $this->cleared;
    }

    /**
     * @param string $cleared
     */
    public function setCleared($cleared)
    {
        $this->cleared = $cleared;
        $this->setSelection($cleared);
    }

    /**
     * @return string
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * @param string $state
     */
    public function setState(string $state)
    {
        $this->state = $state;
    }

    /**
     * @param string $cleared
     */
    private function setSelection($cleared)
    {
        // Set the flag for selection
        $selection = false;
        if($cleared == ReconciliationUtils::CLEARED || $cleared == ReconciliationUtils::MATCHED) {
            $selection = true;
        }
        $this->setCustomFieldValue('SELECTED', $selection);
    }

    /**
     * @return string
     */
    public function getWhenCreated()
    {
        return $this->whenCreated;
    }

    /**
     * @param string $whenCreated
     */
    public function setWhenCreated($whenCreated)
    {
        $this->whenCreated = $whenCreated;
    }

    /**
     * @return string
     */
    public function getWhenModified()
    {
        return $this->whenModified;
    }

    /**
     * @param string $whenModified
     */
    public function setWhenModified($whenModified)
    {
        $this->whenModified = $whenModified;
    }

    /**
     * @return string
     */
    public function getCreatedBy()
    {
        return $this->createdBy;
    }

    /**
     * @param string $createdBy
     */
    public function setCreatedBy($createdBy)
    {
        $this->createdBy = $createdBy;
    }

    /**
     * @return string
     */
    public function getModifiedBy()
    {
        return $this->modifiedBy;
    }

    /**
     * @param string $modifiedBy
     */
    public function setModifiedBy($modifiedBy)
    {
        $this->modifiedBy = $modifiedBy;
    }

    /**
     * @return array
     */
    public function getArrayParams()
    {
        return $this->arrayParams;
    }

    /**
     * @param array $arrayParams
     */
    public function setArrayParams($arrayParams)
    {
        $this->arrayParams = $arrayParams;
    }

    /**
     * @return int
     */
    public function getAmountOperator()
    {
        return $this->getTrxType() == ReconciliationUtils::TRXTTYPE_DEBIT ? -1 : 1;
    }

    /**
     * Returns the values of the object.
     *
     * @return array
     */
    public function getValues()
    {
        $this->arrayParams['RECORDNO'] = $this->recordNo;
        $this->arrayParams['BANKACCTRECONKEY'] = $this->acctReconNo;
        $this->arrayParams['ACCTRECONRECORDKEY'] = $this->acctReconRecordNo;
        $this->arrayParams['FINANCIALENTITY'] = $this->financialEntity;
        $this->arrayParams['RECORDKEY'] = $this->recordKey;
        $this->arrayParams['RECORDTYPE'] = $this->recordType;
        $this->arrayParams['PRRECORDKEY'] = $this->prrecordNo;
        $this->arrayParams['GLENTRYKEY'] = $this->glentryNo;
        $this->arrayParams['IOITEMSKEY'] = $this->ioitemsNo;
        $this->arrayParams['SIOITEMSKEY'] = $this->sioitemsNo;
        $this->arrayParams['TRANSACTIONTYPE'] = $this->trxType;
        $this->arrayParams['DOCNO'] = $this->docNo;
        $this->arrayParams['DOCDATE'] = $this->docDate;
        $this->arrayParams['TRX_AMOUNT'] = $this->amount;
        $this->arrayParams['AMOUNT'] = $this->baseAmount;
        $this->arrayParams['CURRENCY'] = $this->currency;
        $this->arrayParams['BASECURRENCY'] = $this->baseCurrency;
        $this->arrayParams['POSTINGDATE'] = $this->postingDate;
        $this->arrayParams['PAYEE'] = $this->payee;
        $this->arrayParams['DESCRIPTION'] = $this->description;
        $this->arrayParams['CLEARED'] = $this->cleared;
        $this->arrayParams['STATE'] = $this->state;
        $this->arrayParams['WHENCREATED'] = $this->whenCreated;
        $this->arrayParams['WHENMODIFIED'] = $this->whenModified;
        $this->arrayParams['CREATEDBY'] = $this->createdBy;
        $this->arrayParams['MODIFIEDBY'] = $this->modifiedBy;
        return $this->arrayParams;
    }

}