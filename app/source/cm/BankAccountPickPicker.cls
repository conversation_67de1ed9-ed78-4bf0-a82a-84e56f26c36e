<?php

/**
 * Picker class for the Bank Account object
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */

/**
 * Picker class for the Bank Account object
 */
class BankAccountPickPicker extends NPicker
{

    // Class variable
    /* @var bool $opSubscriptionFilter */
    public $opSubscriptionFilter;

    public function __construct()
    {
        $this->filterForCurrency = (Request::$r->_filterForCurrency == '1') ? true : false;
        $this->opSubscriptionFilter = (Request::$r->_opsubscriptionfilter == '1') ? true : false;

        parent::__construct(
            array(
                'fields' => array('BANKACCOUNTID', 'BANKNAME', 'CURRENCY'),
                'pickfield' => 'BANKACCOUNTID',
                'entity' => 'bankaccountpick',
                'nonencodedfields' => ['BANKACCOUNTID', 'CURRENCY'],
                'helpfile' => ''
            )
        );
    }

    /**
     * Build the query specs 
     * 
     * @return array the query specs
     */
    public function buildQuerySpec()
    {
        $querySpec = parent::BuildQuerySpec();
        $this->addCurrencyFilters($querySpec);
        return $querySpec;
    }

    /**
     * Build the query specs 
     * 
     * @return array the query specs
     */
    public function buildQuerySpecAll()
    {
        $querySpec = parent::BuildQuerySpecAll();
        $this->addCurrencyFilters($querySpec);
        return $querySpec;
    }

    /**
     * Add the currency filters
     * 
     * @param array $querySpec the query specs
     */
    private function addCurrencyFilters(&$querySpec)
    {
        if ( $this->filterForCurrency ) {
            
            // If we are at the entity level we need to filter out the list of bank account
            $ctx = GetContextLocation();
            
            if ( IsMCMESubscribed() && !empty($ctx) ) {
         
                // Show all the bank account with no location
                $querySpec['filters'][0][] = array(
                    'LOCATION#',
                    'ISNULL',
                    '',
                    'r'
                );

                // Show only the bank account with a location that is within the same context we are 
                $filter = array(
                    'LOCATION#',
                    'INSUBQUERY',
                    array(
                        'STMT' => 'select location# from v_locationent where currency = ? and cny# = ?',
                        'ARGS' => array(GetBaseCurrency(), GetMyCompany()),
                        'ARGTYPES' => array('integer', 'integer'),
                    ),
                );
                $querySpec['filters'][0][] = $filter;   
            }
        }

        $opSubscriptionID = GetOperationId('co/services/outsourcepaymentssetup/create');
        if ($this->opSubscriptionFilter || in_array($opSubscriptionID, $this->_params['_op'])) {
            if (GetBaseCurrency() == 'USD') {
                $querySpec['filters'][0][] = array(
                    'CURRENCY',
                    '=',
                    'USD',
                    'r'
                );
                $querySpec['filters'][0][] = array(
                    'CURRENCY',
                    'ISNULL',
                );
            } else {
                $querySpec['filters'][0][] = array(
                    'CURRENCY',
                    '=',
                    'USD',
                );
            }
        }
    }
}
