<?php

/**
 * CMReconActionInterface,  for Bank transaction line level actions.
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */
interface CMReconActionInterface
{
    const ACTION_CREATE_JE = 'createJE';
    const ACTION_IGNORE_TXN  = 'ignoreTransaction';
    const ACTION_CREATE_CC_TXN  = 'createCCTransaction';
    const ACTION_CREATE_OTHER_RECEIPT = 'createOtherReceipt';
    const ACTION_CREATE_MANUAL_PAYMENT = 'createManualPayment';
    const ACTION_CREATE_RECEIVE_PAYMENT  = 'createReceivePayment';
    const ACTION_CREATE_FUND_TRANSFER  = 'createFundTransfer';
    const ACTION_CREATE_BANK_FEE  = 'createBankFee';
    const ACTION_CREATE_CREDIT_CARD_FEE  = 'createCreditCardFee';

    /**
     * Populates and returns the default data to populate the create JE page
     *
     * @param array $rowData
     * @param  string   $feedType
     * @param array $accountDetails
     *
     * @return array
     */
    public function getActionResponse($rowData, $feedType, $accountDetails = null);
}