<?php
import('Core_ReconFileHandler');

/**
 * to handle the XML type upload for the reconciliation process.
 * <AUTHOR>
 * @copyright    Intacct Corporation
 * @access       public
 */
class XML_ReconFileHandler extends Core_ReconFileHandler
{
    public function __construct()
    {
        // Do not call the parent constructor because we don't read a file, instead we read data
        // (the parent constructor sets auto_detect_line_endings which we don't need)
        // parent::__construct();
    }

    /**
     * Initialize transaction file upload
     * Basic validation for file header to be done here and the main logic call for parsing the file will be performed.
     *
     * @param array $uploadTxns
     *
     * @return array|bool
     */
    function InitTransFileUpload($uploadTxns) 
    {
        $count = 0;
        global $gErr;

        if (count($uploadTxns) == 0) {
            $gErr->addError("**********", __FILE__ . ':' . __LINE__, "Please enter valid rows in input XML request.");
            return false;
        }

        $tempArray = array();
        $uploadTrans = array();

        foreach ( $uploadTxns as $val) {
            $this->ParseTransLine($val, $tempArray, $count);
            if (is_array($tempArray) && count($tempArray) > 0) {
                $uploadTrans[] = $tempArray;
            }
            $tempArray = array();
            $count++;
        }
        return $uploadTrans;
    }

    /**
     * Parse the transaction file line by line.
     * Parse the uploaded transaction file line by line and populate the parsed values into a temporary array
     * to form the final php array.
     *
     * @param array $buffer
     * @param array $tempArray
     * @param int|null $count
     *
     * @return bool
     */
    function ParseTransLine($buffer, &$tempArray, /** @noinspection PhpUnusedParameterInspection */ $count) 
    {
        $transLine = $buffer;

        if (is_array($transLine) && count($transLine) > 1) {
            $this->ParseDateLine($transLine['POSTINGDATE'], $tempArray);
            $this->ParseAmountLine($transLine['AMOUNT'], $tempArray);
            $this->ParseDocNumberLine($transLine['DOCUMENT'], $tempArray);
            $this->ParsePayeeLine($transLine['PAYEE'], $tempArray);
            $this->ParseMemoLine($transLine['DESCRIPTION'], $tempArray);
        }
        return true;
    }
} 

