<?xml version="1.0" encoding="UTF-8"?>
<ROOT>
    <view system="true">
        <events>
            <load>
                cmtxnOnload();
                subledgertxnOnLoad('P');
            </load>
        </events>
        <title>IA.BANK_INTEREST_AND_CHARGES_TITLE</title>
        <pages>
            <page title="IA.TRANSACTION">
                <section id="header_bottom" className="horizontal headerCustom" customFields="no">
                    <field readonly="true">FINANCIALENTITYHEADER</field>
                    <field readonly="true">WHENCREATEDHEADER</field>
                    <field readonly="true">TRANSACTIONTYPEHEADER</field>
                    <field isHTML="true" readonly="true">TRX_TOTALENTEREDHEADER</field>
                    <field isHTML="true" readonly="true" hidden="true">TOTALENTEREDHEADER</field>
                    <field noLabel="true" readonly="true" isHTML="true" className="stamp">REVERSESTATEHEADER</field>
                </section>
                <section id="mainSection" columnCount="2">
                    <field clazz="bankField">
                        <path>FINANCIALENTITY</path>
                        <events>
                            <change>
                                defaultFinancialEntityValues(this.meta);
                                defaultBankAccounts();
                                bankfeeCurrenyHandler(this.meta);
                                populateHeaderField(this.meta);
                                restrictLocation(this.meta);
                            </change>
                        </events>
                    </field>
                    <field>
                        <path>WHENCREATED</path>
                        <events>
                            <change>
                                populateExchangeRateDate(this.meta);
                                populateHeaderField(this.meta);
                            </change>
                        </events>
                    </field>
                    <field>
                        <path>TRANSACTIONTYPE</path>
                        <events>
                            <change>
                                defaultMemoFromFieldValue(this.meta);
                                updateAccountLabelType(this.meta);
                                handleCmTransactionTypeChange(this.meta, 'P');
                                populateHeaderField(this.meta);
                            </change>
                        </events>
                    </field>
                    <field>RECORDID</field>
                    <field>DESCRIPTION</field>
                    <field>SUPDOCID</field>
                    <field hidden="true" isHTML="true">REVERSALDATE</field>
                    <field hidden="true" isHTML="true">REVERSEDDATE</field>
                    <row noLabel="true">
                        <field hidden="true" path="TAXIMPLICATIONS">
                            <events>
                                <change>handleTaxImplicationsCheckboxChange(this.meta, 'P');</change>
                            </events>
                        </field>
                        <field hidden="true" path="TAXSOLUTIONID">
                            <events>
                                <change>handleCmTaxSolutionChange(this.meta, 'P');</change>
                            </events>
                        </field>
                        <field rightSideLabel="true" hidden="true">
                            <path>INCLUSIVETAX</path>
                            <events>
                                <change>
                                    handleInclusiveTaxSelectEvent(this.meta);
                                </change>
                            </events>
                        </field>
                    </row>
                    <field hidden="true">
                        <path>RECORDNO</path>
                    </field>
                </section>
                <section id="mcpSection" columnCount="2">
                    <title>IA.CURRENCY</title>
                    <field>
                        <path>CURRENCY</path>
                        <events>
                            <change>updateExchangeRate(this.meta);</change>
                        </events>
                    </field>
                    <field>BASECURR</field>
                    <field>
                        <path>EXCH_RATE_DATE</path>
                        <events>
                            <change>updateExchangeRate(this.meta);</change>
                        </events>
                    </field>
                    <field>
                        <path>EXCH_RATE_TYPE_ID</path>
                        <events>
                            <change>updateExchangeRate(this.meta);</change>
                        </events>
                    </field>
                    <field>
                        <path>EXCHANGE_RATE</path>
                        <events>
                            <change>c_updateExchangeRate(this.meta.getValue());calculateBaseAmount(null, this.meta);
                            </change>
                        </events>
                    </field>
                </section>
                <grid allowEditPage="true" clazz="EntriesGrid">
                    <path>ITEMS</path>
                    <title>IA.ENTRIES</title>
                    <column>
                        <field required="true" hidden="true" defaultEntityPath="LOCATIONID" clazz="accountLabelField">
                            <path>SERVICECHARGEACCOUNTLABEL</path>
                            <events>
                                <change>populateGLAccount(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field required="true" hidden="true" defaultEntityPath="LOCATIONID" clazz="accountLabelField">
                            <path>INTERESTEARNEDACCOUNTLABEL</path>
                            <events>
                                <change>populateGLAccount(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field required="true" hidden="true">
                            <path>ACCOUNTNO</path>
                            <events>
                                <change>populateLineMemo(this.meta, 'DESCRIPTION');</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field>
                            <path>TRX_AMOUNT</path>
                            <events>
                                <change>updateUserDefinedExchangeRate(this.meta);calculateLineItemTax(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field>
                            <path>AMOUNT</path>
                            <events>
                                <change>updateUserDefinedExchangeRate(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column className="center">
                        <field hidden="true">
                            <path>MULTIPLETAXES</path>
                            <events>
                                <change>onClickMultipleTaxes(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.DEPARTMENT">DEPARTMENTID</field>
                    </column>
                    <column>
                        <field fullname="IA.LOCATION">LOCATIONID</field>
                    </column>
                    <column>
                        <field fullname="IA.TAX_DETAIL">
                            <path>DETAILID</path>
                            <events>
                                <change>onChangeTaxDetail(this.meta);</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.RATE" hidden="true">
                            <path>TAXRATE</path>
                            <readonly>true</readonly>
                        </field>
                    </column>
                    <column>
                        <field hidden="true">
                            <path>TRX_TAX</path>
                            <events>
                                <change>calculateBaseTaxForOverriddenTax(this.meta);updateParentGridTotalAmountValues(this.meta.getGrid(), this.meta.getLineNo());</change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field hidden="true">
                            <path>TAX</path>
                        </field>
                    </column>
                    <column>
                        <field>DESCRIPTION</field>
                    </column>
                    <column>
                        <field readonly="true" required="false" hasTotal="true" hidden="true">
                            <path>TOTALTRXAMOUNT</path>
                            <events>
                                <change>calculateInclusiveTax(this.meta, false);
                                </change>
                            </events>
                        </field>
                    </column>
                    <column>
                        <field readonly="true" required="false" hasTotal="true" hidden="true">
                            <path>TOTALBASEAMOUNT</path>
                        </field>
                    </column>
                    <lineDetails clazz="EntriesDetails">
                        <pages>
                            <page title="IA.DETAILS">
                                <section id='ITEMSCustomFields' title='IA.CUSTOM_FIELDS' skipFieldSet="true"
                                         customFields="ITEMS" columnCount="2">
                                </section>
                                <section id="vatSection" title="IA.TAXES">
                                    <grid clazz="TaxEntriesGrid">
                                        <path>TAXENTRIES</path>
                                        <column>
                                            <field fullname="IA.TAX_DETAIL">
                                                <path>DETAILID</path>
                                                <events>
                                                    <change>onChangeTaxDetail(this.meta);</change>
                                                </events>
                                            </field>
                                        </column>
                                        <column>
                                            <field fullname="IA.RATE">
                                                <path>TAXRATE</path>
                                                <readonly>true</readonly>
                                            </field>
                                        </column>
                                        <column>
                                            <field>
                                                <path>TRX_TAX</path>
                                                <events>
                                                    <change>calculateBaseTaxForOverriddenTax(this.meta);updateTxnTaxBasedOnMultiTaxTotal(this.meta);</change>
                                                </events>
                                            </field>
                                        </column>
                                        <column>
                                            <field>
                                                <path>TAX</path>
                                            </field>
                                        </column>
                                        <column>
                                            <field noLabel="true" hidden="true">
                                                <path>OVERRIDDENTAX</path>
                                                <type type='boolean' ptype='boolean'></type>
                                                <default>false</default>
                                            </field>
                                        </column>
                                    </grid>
                                </section>
                                <section hidden="true" id="taxTotalsSection" title="" columnCount="3">
                                    <field hidden="true">
                                        <path>BASETAXAMOUNT</path>
                                    </field>
                                    <field>
                                        <path>BASECURR</path>
                                    </field>
                                </section>
                                <section id='ITEMSIA.DIMENSIONS' title="IA.DIMENSIONS" dimFields="ITEMS" columnCount="2">
                                </section>
                            </page>
                        </pages>
                    </lineDetails>
                </grid>
                <grid hasFixedNumOfRows="true" readonly="true" hidden="true">
                    <path>TAXSUMMARY</path>
                    <title>IA.TAX_SUMMARY</title>
                    <caption className="float_left" id="taxGridCalculateTaxCaption"
                             isCollapsible="false">
                        <button id="taxGridCalculateTaxButton">
                            <name>IA.SHOW_SUMMARY</name>
                            <events>
                                <click>reCalculateTax();</click>
                            </events>
                        </button>
                    </caption>
                    <column>
                        <field fullname="IA.DESCRIPTION">
                            <path>DETAILID</path>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.PERCENT">
                            <path>TAXRATE</path>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.TRANSACTION_TAX_TOTAL" hasTotal="true">
                            <path>TRX_TAX</path>
                        </field>
                    </column>
                    <column>
                        <field fullname="IA.BASE_TAX_TOTAL" hasTotal="true">
                            <path>TAX</path>
                        </field>
                    </column>
                </grid>
            </page>
            <xi:include href="prglposting_tab.xml" xmlns:xi="http://www.w3.org/2003/XInclude"/>
        </pages>
    </view>
    <helpfile></helpfile>
</ROOT>
