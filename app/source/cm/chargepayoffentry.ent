<?php

/**
 * Entity for the Charge Payoff Entry object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */

$kSchemas['chargepayoffentry'] = array(
    'children' => array(
        'chargepayoff' => array(
            'fkey' => 'recordkey', 'invfkey' => 'record#', 'table' => 'prrecordmst'
        ),
        'department' => array(
            'fkey' => 'dept#', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'department'
        ),
        'location' => array(
            'fkey' => 'location#', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'location'
        ),
        'glaccount' => array(
            'fkey' => 'accountkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'baseaccount'
        ),
    ),
    'object' => array(
        'RECORDNO',
        'RECORDKEY',
        'RECORDTYPE',
        'ACCOUNTKEY',
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'AMOUNT',
        'TRX_AMOUNT',
        'DEPT#',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LOCATION#',
        'LOCATIONID',
        'LOCATIONNAME',
        'DESCRIPTION',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'LINEITEM',
        'CURRENCY',
        'BASECURR',
        'BASELOCATION',
        'STATUS',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'DEPARTMENTKEY',
        'LOCATIONKEY'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'RECORDKEY' => 'recordkey',
        'RECORDTYPE' => 'chargepayoff.recordtype',
        'ACCOUNTKEY' => 'accountkey',
        'ACCOUNTNO' => 'glaccount.acct_no',
        'ACCOUNTTITLE' => 'glaccount.title',
        'AMOUNT' => 'amount',
        'TRX_AMOUNT' => 'trx_amount',
        'DEPT#' => 'dept#',
        'DEPARTMENTID' => 'department.dept_no',
        'DEPARTMENTNAME' => 'department.title',
        'LOCATION#' => 'location#',
        'LOCATIONID' => 'location.location_no',
        'LOCATIONNAME' => 'location.name',
        'DESCRIPTION' => 'description',
        'EXCH_RATE_DATE' => 'exch_rate_date',
        'EXCH_RATE_TYPE_ID' => 'exch_rate_type_id',
        'EXCHANGE_RATE' => 'exchange_rate',
        'LINEITEM' => 'lineitem',
        'CURRENCY' => 'currency',
        'BASECURR' => 'basecurr',
        'BASELOCATION' => 'baselocation',
        'STATUS' => 'status',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'DEPARTMENTKEY' => 'dept#',
        'LOCATIONKEY' => 'location#'
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),
    'publish' => array(
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'AMOUNT',
        'TRX_AMOUNT',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LOCATIONID',
        'LOCATIONNAME',
        'DESCRIPTION',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'CURRENCY',
        'BASECURR',
        'STATUS',
        'WHENCREATED',
        'WHENMODIFIED'
    ),
    'nexus' => array(
        'chargepayoff' => array(
            'object' => 'chargepayoff',
            'relation' => ONE2MANY,
            'field' => 'RECORDKEY'
        ),
        'department' => array(
            'object' => 'department',
            'relation' => MANY2ONE,
            'field' => 'DEPARTMENTID'
        ),
        'location' => array(
            'object' => 'location',
            'relation' => MANY2ONE,
            'field' => 'LOCATIONID'
        ),
        'glaccount' => array(
            'object' => 'glaccount',
            'relation' => MANY2ONE,
            'field' => 'accountno'
        )
    ),
    'fieldinfo' => array(
        $gRecordNoHiddenFieldInfo,
        array(
            'path' => 'RECORDKEY',
            'fullname' => 'IA.PARENT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'id' => 1
        ),
        array(
            'path' => 'ACCOUNTKEY',
            'fullname' => 'IA.ACCOUNT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'hidden' => true,
            'id' => 2
        ),
        array(
            'path' => 'ACCOUNTNO',
            'fullname' => 'IA.ACCOUNT',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity' => 'glaccount'
            ),
            'id' => 3
        ),
        array(
            'path' => 'ACCOUNTTITLE',
            'fullname' => 'IA.ACCOUNT_TITLE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 80,
            ),
            'id' => 4
        ),
        array(
            'path' => 'AMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'hasTotal' => true,
            'hidden' => true,
            'readonly' => true,
            'id' => 5
        ),
        array(
            'path' => 'TRX_AMOUNT',
            'fullname' => 'IA.TRANSACTION_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'hasTotal' => true,
            'required' => true,
            'id' => 6
        ),
        array(
            'path' => 'DEPT#',
            'fullname' => 'IA.DEPARTMENT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'idw' => false,
            'id' => 7
        ),
        array(
            'path' => 'DEPARTMENTID',
            'fullname' => 'IA.DEPARTMENT_ID',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'department',
                'pickentity' => 'departmentpick'
            ),
            'renameable' => true,
            'standard' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'id' => 8
        ),
        array(
            'path' => 'DEPARTMENTNAME',
            'fullname' => 'IA.DEPARTMENT_NAME',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 40
            ),
            'id' => 9
        ),
        array(
            'path' => 'LOCATION#',
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'idw' => false,
            'id' => 10
        ),
        array(
            'path' => 'LOCATIONID',
            'fullname' => 'IA.LOCATION_ID',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'location',
                'pickentity' => 'locationpick'
            ),
            'renameable' => true,
            'standard' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'id' => 11
        ),
        array(
            'path' => 'LOCATIONNAME',
            'fullname' => 'IA.LOCATION_NAME',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 40
            ),
            'id' => 12
        ),
        array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.MEMO',
            'type' => array(
                'type' => 'text',
                'ptype' => 'textarea',
                'maxlength' => 1000
            ),
            'id' => 13
        ),
        array(
            'path' => 'EXCH_RATE_DATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type' => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat
            ),
            'id' => 14
        ),
        array(
            'path' => 'EXCH_RATE_TYPE_ID',
            'fullname' => 'IA.EXCHANGE_RATE_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'exchangeratetypesall'
            ),
            'id' => 15
        ),
        array(
            'path' => 'EXCHANGE_RATE',
            'fullname' => 'IA.EXCHANGE_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 12
            ),
            'precision' => 12,
            'noformat' => true,
            'readonly' => true,
            'rpdMeasure' => false,
            'id' => 16
        ),
        array(
            'path' => 'LINEITEM',
            'fullname' => 'IA.LINE_ITEM',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.T', 'IA.F'),
                'validvalues' => array('T', 'F')
            ),
            'hidden' => true,
            'id' => 17
        ),
        array(
            'path' => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'readonly' => true,
            'required' => true,
            'id' => 19
        ),
        array(
            'path' => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'readonly' => true,
            'required' => true,
            'id' => 18,
        ),
        array(
            'path' => 'BASELOCATION',
            'fullname' => 'IA.BASE_LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'hidden' => true,
            'id' => 20
        ),
        array(
            'path' => 'DEPARTMENTKEY',
            'fullname' => 'IA.DEPARTMENT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'id' => 21
        ),
        array(
            'path' => 'LOCATIONKEY',
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'id' => 22
        ),
        $gStatusFieldInfo,
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        array (
            'path'      =>  'RECORDTYPE',
            'fullname'  =>  'IA.RECORD_TYPE',
            'desc'      =>  'IA.RECORD_TYPE',
            'type'      =>  array (
                'ptype'     =>  'text',
                'type'      =>  'text',
            ),
            'id' => 200
        ),
    ),
    'pairedFields' => array(
        'ACCOUNTNO' => 'ACCOUNTTITLE',
        'DEPARTMENTID' => 'DEPARTMENTNAME',
        'LOCATIONID' => 'LOCATIONNAME'
    ),
    'dbfilters' => array(
        array('chargepayoffentry.lineitem', '=', 'T'),
        array(
            'chargepayoff.recordtype',
            '=',
            SubLedgerTxnManager::PAYOFF_RECTYPE
        )
    ),
    'printas' => 'IA.CHARGE_PAYOFFS_DETAILS',
    'pluralprintas' => 'IA.CHARGE_PAYOFFS_DETAILS',
    'table' => 'prentry',
    'updatetable' => 'prentrymst',
    'parententity' => 'chargepayoff',
    'module' => 'cm',
    'vid' => 'RECORDNO',
    'hasdimensions' => true,
    'auditcolumns' => true,
    'cachecustomdimensions' => true,
    'followgldimensions' => true,
    'api' => array(
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ),
    'allowDDS' => false,
    'description' => 'IA.DETAIL_AND_HEADER_INFORMATION_OF_CREDIT_CARD_PAYOF',
);
