<?
/** class APPrintChecksLister extends PrintChecksLister
 */

import('PrintableChecksLister');


class AC<PERSON>ileGeneratorLister extends PrintableChecksLister {

    /**
     * @return array the parameters that have been initialized
     */
	function getInitializedParams() {
		$_rb = Request::$r->_rb;
		$params = array(
			'id' 		=> 'RECORDNO',
			'entity' 	=> $this->getEntityName(),
			'title' 	=> $this->getTitle(),
			'formmethod'    => 'post',
			'disablefilter'	=> true,
			'disablesort'	=> true,
			'disablepaging'	=> false,
			'showstatus'	=> false,
			'rangebegin'	=> $_rb,
			'fields'	=> array(
				'VENDORID',
				'VENDORNAME',
				'FINANCIALENTITY',
				'PAYMENTDATE',
				'PAYMENTAMOUNT',
				//'LOCATIONKEY',
			),
			
			'checkbox_specs' => $this->getCheckboxSpec(),
			'suppressPrivate' => true,
		);

		if ($this->showEntityChecks) {
			$params['fields'][] = 'LOCATIONKEY';
		}

		// If we don't have a MAXROWS something has gone wrong. Put a max value just in case:
		$params['rangesize'] = isl_preg_match('/^\d+$/', $this->userInput['MAXROWS'])
			? intval($this->userInput['MAXROWS'])
			: MAX_ALLOWED_IN_PICKLIST; // Might as well use this for now

		$params['textfields'] = $this->getTextfieldSpec();

		return $params;
	}

    /**
     * Overrides the parent class method used to describe the sorting scheme for the query that
     * returns our list results.
     * This mapping of dropdown display strings to database field IDs is used by the downstream logic
     * to set the ORDER BY statement of our list query. Our .RPT file determines what the default selection
     * will be.
     *
     * @param array $args
     *
     * @return array the data structure for the ORDER BY statement of the query used to display our list.
     */
	function PrepCalcOrders($args) {
		$orders = array();
		
		$sortby = urldecode($args['SORTBY']);
		$sorts		= $sortby?explode('#~#',$sortby):array();

		// BECAUSE OUR ENUM CONTROL DEFAULT PARAM USAGE IS BROKEN
		// BOTH OUR ID FIELD KEY AND ID FIELD LABEL MUST MAP TO
		// OUR ID FIELD NAME
				
		$sortMap = array(
			'ENTITYID' => 'VENDORID',
			'vendor' => 'VENDORNAME',
			'Bank' => 'FINANCIALENTITY',
			'Date' => 'PAYMENTDATE',
			'Entity' => 'LOCATIONKEY',
    	);
		if ( IsMultiEntityCompany() && GetContextLocation() == '' && !IsRestrictedUser()) {
			$sortMap[] = 'LOCATIONKEY';
		}
		
		$sortKey = 0;

        foreach ( $sorts as $sortBy) {
			if ( array_key_exists($sortBy, $sortMap) ) {
				$orders[$sortKey] = $sortMap[$sortBy] ." asc";
				$sortKey++;
			}
		}
		
		
		return $orders;
	}

    /**
     * Sets the parameter information for our superclass to create any required columns
     * that contain checkboxes. These checkboxes parameterize the user's submitted request.
     *
     * (assoc) : maps the column field ID to its related parameters
     *
     * @return array
     */
	function getCheckboxSpec() {
		$chkboxSpec = parent::getCheckboxSpec();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $addCol = ($this->showEntityChecks)?1:0;
		$chkboxSpec['GENERATEACH'] = array(
			'col_nbr' => 1,
			'fieldlabel' => ['IA.SELECT'],
		);
		return $chkboxSpec;
	}

    /**
     * Retrieves the feature-specific title to display on the page.
     *
     * @return string the title
     */
	function getTitle() {
		return 'IA.GENERATE_ACH_FILE';
	}

    /**
     * Initializes common member variables across check-printing subclasses.
     */
	function initVars() {
        $this->entIDKey		= 'VENDORID';
		$this->entNameKey	= 'VENDORNAME';
		$this->entLabel		= _('Vendor');

		parent::initVars();
	
		$this->printColumnID = 'GENERATEACH';
		$this->selectedFutureChkCnt 	= 0;
		$this->selectedPrintedChkCnt	= 0;
		$this->selectedConfirmVoidChkCnt = 0;
		$this->userRequestVerbs = array(
			'generate',
			'confirm',
			'void',
		);

		$this->userInput 	= Request::$r->GetCurrentLines();

		if ($this->userInput['ENTITYLEVELPAYMENTS']=='true') {
			$this->showEntityChecks = true;
		}

        $this->additionalTokens[] = 'IA.GENERATE_FILE';
        $this->additionalTokens[] = 'IA.CUSTOMIZE_LIST';
	}

    /**
     * Sets the parameter information for our superclass to create any required columns
     * that contain textfields.
     *
     * (assoc) : maps the column field ID to its related parameters
     *
     * @return array
     */
	function getTextfieldSpec() {
		$textFieldSpec = array(
			'MEMO' => array(
				'maxlength' => 50,
				'size' => 15,
			),
		);
		return $textFieldSpec;
	}

    /**
     * Provides the JavaScript to execute whenever the checkbox indicated at
     * row $i, column $j is clicked.
     * Begins with the JavaScript prescribed by the parent class method, and then
     * adds JS calls to functions that update the count of certain types of checks
     * that are selected. Typically a popup warning will be displayed to the user
     * if one of these types of checks is selected, or if none are selected for
     * the requested action.
     *
     * @param int $i zero-based index of the row of the checkbox control
     * @param int $j zero-based index of the column of the checkbox control
     *
     * @return string JavaScript code for the checkbox onClick attribute
     */
	function checkboxOnClick( $i, $j ) {
		$jsStr = parent::checkboxOnClick( $i, $j );

		$p = &$this->_params;
		$cboxID = $p['_fields'][$j];

		// THESE FUNCTIONS WILL KEEP TRACK OF HOW MANY OF A PARTICULAR KIND OF
		// CHECK IS SELECTED BY THE USER (used to popup warnings)

		if ( $cboxID == 'PRINTCHECK' ) {
			$whenDue = &$this->table[$i]['WHENDUE'];
			$isFutureCheck = ( SysDateCompare($whenDue, $this->currentDate) > 0 );
		
			// CHECK WITH A FUTURE DATE
			if ( $isFutureCheck ) { 
				$jsStr .= "UpdateFutureCheckCnt(this); "; 
			}

			// ALREADY PRINTED CHECK
			if ( $this->table[$i]['STATE'] == 'P' ) { 
				$jsStr .= "UpdatePrintedCheckCnt(this); "; 
			}
		}
		return $jsStr;
	}

    /**
     * Determines whether or not the application will automatically select the checkbox indicated at
     * row $i, column $j.
     * The checkbox will be selected by default if it is a 'confirm' or 'void' checkbox and the
     * associated check was just printed. Otherwise it is selected if our parent's method says so.
     *
     * @param int $i zero-based index of the row of the checkbox control
     * @param int $j zero-based index of the column of the checkbox control
     *
     * @return bool true if the checkbox should initilize as selected. false otherwise.
     */
	function forceBoxAsChecked( $i, $j ) {
		global $kVerb;

		$p = &$this->_params;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $cboxID = $p['_fields'][$j];

		$verb = Request::$r->$kVerb;

		$inp = $this->userInput;
		$recID = &$this->table[$i][$p['_id']];
		
		// AFTER PRINTING THE CHECK WE AUTOMATICALLY SELECT THE CONFIRM/VOID
		// OPTION FOR THE USER
		//
		if ( $verb == 'generate' && Request::$r->_kNoWarn != 'true' && Request::$r->_kNoDupl != 'true') {
			$chksToPrint = $inp['GENERATEACH'];
			if ( is_array($chksToPrint) && in_array($recID, $chksToPrint) ) {
				return true;
			}
		}
		return parent::forceBoxAsChecked( $i, $j );
	}

    /**
     * Determines whether or not the requested user action requires processing logic beyond a
     * simple display of the list.
     *
     * @param string $verb the submitted value representing the users request. It is set by the ".do" hidden input.
     *
     * @return bool TRUE, if the requested action requires processing. FALSE, otherwise.
     */
	function processingAction($verb)
    {
        $actionsToProcess = array(
            'generate',
        );
        return in_array($verb, $actionsToProcess);
    }

    /**
	* Overrides the parent method. After calling the parent's version, this method removes any columns not
	* to appear in the final display list.
	*/
	function BuildTable() {
		$this->Init();

		//parent::BuildTable();
		$querySpec = $this->BuildQuerySpec();

		// getting the count of the result and set the domain size.
		$table = $this->getACHFileList($querySpec,$querySpec['start'], $querySpec['max']);

		if ($table) {
			for ( $i = 0; $i < count($table); $i++ ) {
				$table[$i]['PAYMENTAMOUNT'] = Currency(round($table[$i]['PAYMENTAMOUNT'], 2), 0, 0, $table[$i]['CURRENCY']);
			}


			$resultCount = count($table);

			$this->SetDomainSize($resultCount);

			$this->table = $table;
		}

		$p = &$this->_params;
		$this->SetOutputFields(
			$p['_fields'],
			$p['_fieldlabels'],
			$p['_fieldorders'],
			$p['_checkbox_specs']
        );

        $this->loadTokenLabels();
	}

    /**
     * Determines whether or not the requested user action implies that we are displaying at least
     * one check in PDF form.
     *
     * @param string $verb  the submitted value representing the users request. It is set by the
     *                ".do" hidden input.
     *
     * @return bool TRUE, if the requested action implies PDF printing. FALSE, otherwise.
     */
    function printing($verb)
    {
        return ( $verb == 'generate' );
    }

    /**
     * Get raw table data
     *
     * @param array  $querySpec
     * @param string $querytype
     *
     * @return string[][]
     */
    function GetList($querySpec, $querytype = 'normal')
    {
        $ret = $this->getACHFileList($querySpec);
        if ($ret === false) {
            $ret = [];
        }
        return $ret;
    }

    /**
     * @param array      $querySpec
     * @param int        $start
     * @param int|string $max
     *
     * @return string[][]|false
     */
    function getACHFileList($querySpec, $start = 0, $max = '') {
		
		//Get base prentries due for payment query - Note: cny# is always parm#1
		$args = $this->getListQuery($querySpec);

		$table = QueryResult($args, $start, $max);

		return $table;
	}

    /**
     * @param array $querySpec
     *
     * @return array
     */
	function getListQuery(/** @noinspection PhpUnusedParameterInspection */ $querySpec){
	
		// PROCESS WHERE CLAUSE
		$args = array();
        $argCount = 0;
		$args[$argCount] = "";
		$args[++$argCount] = GetMyCompany();

		$inp = $this->userInput;

        /** @noinspection PhpUnusedLocalVariableInspection */
        $maxRows = $inp['MAXROWS'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $account = $inp['ACCOUNTID'];
		$selAllVendors = $inp['SELECTALLSUPPLIERS'];
		$selAllEmployees = $inp['SELECTALLEMPLOYEE'];
		$fromVendor = $inp['FROMVENDOR'];
		$toVendor = $inp['TOVENDOR'];
		$fromEmployee = $inp['FROMEMPLOYEE'];
		$toEmployee = $inp['TOEMPLOYEE'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $location = $inp['LOCATION'];
		$showEntLevelPayments = $inp['ENTITYLEVELPAYMENTS'];

		if ( !ProcessDateFilter($inp, $startDate, $endDate) ) {
			require('popuperror.phtml');
			exit(0);
		}
	
		if ( isl_trim($args['START_DATE']) == '' ) {
			$inp['START_DATE'] = $startDate;
		}
		if ( isl_trim($args['END_DATE']) == '' ) {
			$inp['END_DATE'] = $endDate;
		}

		//From vendor and To vendor filters
		$vendFilter = $this->PrepVendEmpFilter($fromVendor, $toVendor, 'vendor');

		$empFilter = $this->PrepVendEmpFilter($fromEmployee, $toEmployee, 'employee');

		$acctFilter = $this->PrepAcctFilter($inp);
		$locFilter = $this->PrepLocFilter($inp);

		if ($showEntLevelPayments=='true') {
			SetReportViewContext();
		}
		
		$dateFilter = $this->PrepDateFilter($inp);

		$orders = $this->PrepCalcOrders($inp);

		$allvendqry = "(SELECT vendor.vendorid vendorid,
				  vendor.name vendorname,
				  achfilegenerator.financialentity financialentity,
				  achfilegenerator.whencreated paymentdate,
				  achfilegenerator.trx_totalentered paymentamount,
				  location.location_no locationkey,
				  achfilegenerator.record# recordno,
				  achfilegenerator.currency currency
				FROM prrecord achfilegenerator,
				  vendormst vendor,
				  location location
				WHERE (achfilegenerator.recordtype IN ( 'pp','pr' )
				AND achfilegenerator.paymethodkey   = 12
				AND achfilegenerator.currency       = 'USD'
				AND achfilegenerator.state         IN ( 'A' ))
				AND achfilegenerator.entity         = vendor.entity
				AND vendor.cny#                     = :$argCount
				AND achfilegenerator.locationkey    = location.record# (+)
				AND location.cny# (+)               = :$argCount
				AND achfilegenerator.cny#           = :$argCount
				AND vendor.filepaymentservice = 'ACH'
				{$vendFilter}
				{$acctFilter}
				{$locFilter}
				{$dateFilter}";

		$allempqry = "(SELECT employee.employeeid vendorid,
				  contact.printas vendorname,
				  achfilegenerator.financialentity financialentity,
				  achfilegenerator.whencreated paymentdate,
				  achfilegenerator.trx_totalentered paymentamount,
				  location.location_no locationkey,
				  achfilegenerator.record# recordno,
				  achfilegenerator.currency currency
				FROM prrecord achfilegenerator,
				  employeemst employee,
				  location location,
				  contact contact
				WHERE (achfilegenerator.recordtype IN ( 'ep','er' )
				AND achfilegenerator.paymethodkey   = 12
				AND achfilegenerator.currency       = 'USD'
				AND achfilegenerator.state         IN ( 'A' ))
				AND achfilegenerator.entity         = employee.entity
			    AND employee.cny#                   = :$argCount
				AND achfilegenerator.locationkey    = location.record# (+)
				AND location.cny# (+)               = :$argCount
				AND achfilegenerator.cny#           = :$argCount
				AND contact.cny#					= employee.cny#
				AND contact.record#					= employee.contactkey
				AND employee.filepaymentservice = 'ACH'
				{$empFilter}
				{$acctFilter}
				{$locFilter}
				{$dateFilter}";

        $contextLocation = GetContextLocation();
        if(IsMultiEntityCompany() && isset($contextLocation) && $contextLocation!= ''){
            $args[++$argCount] = $contextLocation;
            $allvendqry .= " AND achfilegenerator.locationkey = :$argCount ";
            $allempqry .= " AND achfilegenerator.locationkey = :$argCount ";
        }
		

		if (!isset($selAllVendors) && !isset($selAllEmployees)) {
			$qry =  $allvendqry . ') union ' . $allempqry . ')';
			$args[0] = "$qry";
			return $args;
		}
		
		$vendFilterExist = ($selAllVendors == 'true' || isset($fromVendor) || isset($toVendor)) ? 'true' : 'false';
		$empFilterExist = ($selAllEmployees == 'true' || isset($fromEmployee) || isset($toEmployee)) ? 'true' : 'false';

		if ($vendFilterExist == 'true') {
				if ($empFilterExist == 'true') {
					$qry =  $allvendqry . ') union ' . $allempqry . ')';
				} else {
					$qry =  $allvendqry . ')';
				}
		} else if ($vendFilterExist == 'false') {
			if ($empFilterExist == 'true') {
				$qry =  $allempqry . ')';
			}else{
				$qry = '';

			}

		}
		
		$comma_separated_orders = implode(" ,", $orders);

		if ($comma_separated_orders!='') {
			$orders = "order by " .$comma_separated_orders;
		}

        /** @noinspection PhpUndefinedVariableInspection */
        $finalqry = "select vendorid, vendorname, financialentity, paymentdate, paymentamount, locationkey, recordno
					from ( " . $qry . ")" . $orders;

		$args[0] = "$finalqry";

		return $args;
	}

    /**
     * @param string $from
     * @param string $to
     * @param string $table
     *
     * @return string
     */
	 function PrepVendEmpFilter($from, $to, $table) {
         list($fromID)	= explode(PICK_RECVAL_SEP, $from);
         list($toID)	= explode(PICK_RECVAL_SEP, $to);

		$from	= $this->formatPRRecordEntity($fromID, $table);
		$to	= $this->formatPRRecordEntity($toID, $table);
	
		
		if ( $from != '' ) {
			if ( $to == '' ) {
				$filter = " and $table.entity >= '$from'";
			} else if ( $from == $to ) {
				$filter = " and $table.entity = '$from'";
			} else {
				$filter = " and $table.entity >= '$from' and $table.entity <= '$to'";
			}
		} else if ( $to != '' ) {
			$filter = " and $table.entity <= '$to'";

		}

         /** @noinspection PhpUndefinedVariableInspection */
         return $filter;
	}

    /**
     * @param array $inp
     *
     * @return string
     */
	function PrepAcctFilter($inp){
		$selectingAll = ProcessCheckingAcctFilter($inp);
		$acctid = $inp['ACCOUNTID'];
		if ( !$selectingAll ) {
			$filter = " and achfilegenerator.financialentity = '$acctid'";
			
		}

        /** @noinspection PhpUndefinedVariableInspection */
        return $filter;
	}

    /**
     * @param array $inp
     *
     * @return string
     */
	function PrepLocFilter($inp) {
		if (isset($inp['LOCATION']) && $inp['LOCATION']!='') {
			$loc = $inp['LOCATION'];
            list($locno) = explode(PICK_RECVAL_SEP, $loc);
			$locrec = QueryResult(array("select record# from location where cny# = :1 and location_no = :2",GetMyCompany(), $locno));
			$locrec = $locrec[0]['RECORD#'];
			
			$filter = " and achfilegenerator.locationkey = '$locrec'";
		}

        /** @noinspection PhpUndefinedVariableInspection */
        return $filter;
	}

    /**
     * @param array $inp
     *
     * @return string
     */
	function PrepDateFilter($inp) {

		$startDate	= $inp['START_DATE'];
		$endDate 	= $inp['END_DATE'];

		if ($startDate) {
			$stfilter = "and achfilegenerator.whencreated >= '$startDate'";
		}
		
		if ($endDate) {
			$endfilter = " and achfilegenerator.whencreated <= '$endDate'";
		}

        /** @noinspection PhpUndefinedVariableInspection */
        $filter = $stfilter . $endfilter;

		return $filter;
		
	}




	/**
	* Loops through the list query results to determine how many of the previously selected
	* checks will be displayed as selected in the newly displayed list as well.
	*
	* Here we update the counts for
	*	(a) checks selected to be confirmed or voided
	*	(b) checks selected for printing that have future pay dates
	*	(c) checks selected for printing which have already been printed
	*/
	function calcSelectedCheckCounts() {
		parent::calcSelectedCheckCounts();

		global $kVerb;
		$inp = $this->userInput;
		$t = &$this->table;

		$verb = Request::$r->$kVerb;

		$gotChksForPrint = ( is_array($inp['GENERATEACH']) && count($inp['GENERATEACH']) > 0 );
		$gotChksForConfirmVoid = ( is_array($inp['CONFIRMVOID']) && count($inp['CONFIRMVOID']) > 0 );

		if ( $gotChksForPrint || $gotChksForConfirmVoid ) {
			foreach($t as $listedChk) {
				$justPrinted = false;
				if ( $gotChksForPrint ) {
					// IF WE"RE STILL SHOWING IT ...
					if ( in_array($listedChk['RECORDNO'], $inp['PRINTCHECK']) ) {

						// IF JUST PRINTED ...
						// ADD IT TO OUR CONFIRM/VOID COUNT (WE WILL SELECT BY DEFAULT)
						if ( $verb == 'generate' ) {
							$this->selectedConfirmVoidChkCnt++;
							$justPrinted = true;
						}
						// IF IT'S GOT A FUTURE DATE, ADD IT TO OUR COUNT
               	     	if ( SysDateCompare($listedChk['WHENDUE'], $this->currentDate) > 0 ) {
							$this->selectedFutureChkCnt++;
						}
						// IF IT'S BEEN PRINTED ALREADY, ADD IT OUR COUNT
						if ( $listedChk['STATE'] == 'P' ) {
							$this->selectedPrintedChkCnt++;	
						}
					}
				}
				if ( $gotChksForConfirmVoid && !$justPrinted ) {
					// IF WE"RE STILL SHOWING IT ...
					if ( in_array($listedChk['RECORDNO'], $inp['CONFIRMVOID']) ) {

						// ADD IT TO OUR COUNT
						$this->selectedConfirmVoidChkCnt++;
					}
				}
			}
		}
	}

    /**
     * Overrides the parent method. Provides XML for what will be hidden variables for our
     * specific actions to process, as well as our specific counts of various kinds of checks
     * which are currently selected.
     *
     * @return string the XML as used in the NLister class for rendering hidden inputs.
     */
	function genGlobs() 
    {
		$ret = parent::genGlobs();

		$ret .= "<g name='.generate'/>";
		$ret .= "<g name='.fcheckcnt'>" . $this->selectedFutureChkCnt . "</g>";
		$ret .= "<g name='.pcheckcnt'>" . $this->selectedPrintedChkCnt . "</g>";
		$ret .= "<g name='.cvcheckcnt'>" . $this->selectedConfirmVoidChkCnt . "</g>";
		$ret .= "<g name='_kNoWarn'>" . (Request::$r->_kNoWarn) . "</g>";
		$ret .= "<g name='_kNoDupl'>" . (Request::$r->_kNoDupl) . "</g>";
		$ret .= "<g name='_warnDuplicateMsg'>" . (Request::$r->_warnDuplicateMsg) . "</g>";

		return $ret;
	}

    /**
     * Overrides the superclass method. Provides XML used by the NLister class to produce the buttons shown above
     * the list. Our 'Customize' button is to be separated as its own distinct button 'group' apart from the
     * regular action buttons.
     *
     * @return string the XML as used in the NLister class.
     */
	function genTopPanel() {
		$ret = "<b id='generate'/><b id='customize' grpbreak='T'/>";

		return $ret;		
	}

	
    /**
     * Overrides the superclass method. Provides XML used by the NLister class to produce the buttons shown on
     * the list screen
     *
     * @return string the XML as used in the NLister class.
     */
	function genAllButtons(){
		$ret  = "<b id='generate'>".
            $this->_calcButtonCmd('generate', GT($this->textMap, 'IA.GENERATE_FILE')) . "</b>";
		$ret .= "<b id='customize'>".
            $this->calcShowFilterButtonCmd('customize', GT($this->textMap, 'IA.CUSTOMIZE_LIST') ) . "</b>";
		return $ret;
	}

	
    
	// Replace the customize buttom by a redirection on the filter page
	// We have to keep all the params in the URL to reselect them in the filer
    /**
     * @param string $id
     * @param string $buttonText
     *
     * @return string
     */
	function calcShowFilterButtonCmd($id, $buttonText) {
		$_oldsess = Request::$r->_oldsess;
        $_op 	  = Request::$r->_op;
		// If we have an old session that means we slided in an entity
		// We need to use the old session to go back to the good filter
		if(isset($_oldsess) && $_oldsess != ''){
			$sess = $_oldsess;
		} else {
			$sess = Session::getKey();
		}

		// Add all params to the URL
		$args = '';
		foreach($this->userInput as $param => $value) {
			
			// FilterEditor->ShowCtrl->DateControl automatically converts date values into UI, so we should reset this back to DB format.
			if ($param == 'ASOFDATE') {
				$value = FormatDateForStorage($value);
			}
			$args .= "&_obj__$param=" . URLCleanParams::insert("_obj__$param", $value);
		}
		$script = "filters.phtml?.sess=$sess&.op=$_op".$args;
		$url = urlencode($script);

		$ret = "<a href='#skip' onclick='javascript:window.location.href=unescape(\"$url\");return false;'>$buttonText</a>";
		return $ret;
	}

    /**
     * Overrides the parent method. Provides XML used by the NLister class to produce the footer
     * message seen at the bottom of the various check printing list pages.
     *
     * @return string the XML as used in the NLister class.
     */
	function genFooterComments() {
        return '';
        
    }

    /* @var int $selectedFutureChkCnt */
	var $selectedFutureChkCnt;

	/* @var int $selectedPrintedChkCnt  */
	var $selectedPrintedChkCnt;

	/* @var int $selectedConfirmVoidChkCnt */
	var $selectedConfirmVoidChkCnt; // integer : number of checks selected for either confirmation or void

    /**
     * @param string $entityID
     * @param string $tab
     *
     * @return string
     */
	function formatPRRecordEntity($entityID, $tab='') {
		return $entityID != '' ? ($tab == 'vendor' ? 'V' . $entityID : 'E' .$entityID) : ''; 
    }

    /**
     * @return array
     */
	function BuildQuerySpec() {

		$querySpec = NLister::BuildQuerySpec();
        //$querySpec['filters'][0][] = array('RECORD#', 'IN', array($subquery));
		return $querySpec;
	}


	


}


