<?php


$kchargepaymententryQueries = array(

	'QRY_CHARGEPAYMENTENTRY_INSERT' => array(
		'QUERY' => "INSERT INTO v_chargepaymententry (record#,recordtype,whencreated,description2,description,activity,totalentered,totaldue,amountpaid,parentpayment,payment_entity,cny#) VALUES (?,?,?,?,?,?,?,?,?,?,?,?) ",
		'ARGTYPES' => array('integer','text','date','text','text','text','decimal','decimal','decimal','integer','text','integer'),
	),
	'QRY_CHARGEPAYMENTENTRY_UPDATE' => array(
		'QUERY' => "UPDATE v_chargepaymententry SET recordtype=?, whencreated=?, description2=?, description=?, activity=?, totalentered=?, totaldue=?, amountpaid=?, parentpayment=?, payment_entity=? WHERE record# =?  AND cny# =?  ",
		'ARGTYPES' => array('text','date','text','text','text','decimal','decimal','decimal','integer','text','integer','integer' ),
	),
	'QRY_CHARGEPAYMENTENTRY_DELETE_VID' => array(
		'QUERY' => "DELETE FROM v_chargepaymententry WHERE record# =?  AND cny# =? ",
		'ARGTYPES' => array('integer' ,'integer' ),
	),
	'QRY_CHARGEPAYMENTENTRY_SELECT_SINGLE_VID' => array(
		'QUERY' => "SELECT chargepaymententry.record#,chargepaymententry.recordtype,chargepaymententry.whencreated,chargepaymententry.description2,chargepaymententry.description,chargepaymententry.activity,chargepaymententry.totalentered,chargepaymententry.totaldue,chargepaymententry.amountpaid,chargepaymententry.parentpayment,chargepaymententry.payment_entity FROM v_chargepaymententry chargepaymententry WHERE (chargepaymententry.record# =  ? ) and chargepaymententry.cny# = ? ",
		'ARGTYPES' => array('integer', 'integer'),
	),
	'QRY_CHARGEPAYMENTENTRY_SELECT_RAW_VID' => array(
		'QUERY' => "SELECT * FROM v_chargepaymententry WHERE record# =?  AND cny# =?  ",
		'ARGTYPES' => array('integer' ,'integer' ),
	),
	'QRY_CHARGEPAYMENTENTRY_SELECT_BY_PARENT' => array(
		'QUERY' => "SELECT chargepaymententry.record#,chargepaymententry.recordtype,chargepaymententry.whencreated,chargepaymententry.description2,chargepaymententry.description,chargepaymententry.activity,chargepaymententry.totalentered,chargepaymententry.totaldue,chargepaymententry.amountpaid,chargepaymententry.parentpayment,chargepaymententry.payment_entity FROM v_chargepaymententry chargepaymententry WHERE (chargepaymententry.parentpayment =  ? ) and chargepaymententry.cny# = ? ",
		'ARGTYPES' => array('integer', 'integer'),
	),
	'QRY_CHARGEPAYMENTENTRY_DELETE_BY_PARENT' => array(
		'QUERY' => "DELETE FROM v_chargepaymententry WHERE parentpayment =?  AND cny# =? ",
		'ARGTYPES' => array('integer' ,'integer' ),
	),
);


