<?php

/**
 * Manager class for the Bank transaction rule run details
 *
 * <AUTHOR>
 * @copyright 2023 Sage Intacct Inc., All Rights Reserved
 */
class MatchToAmount extends ApplyPaymentMethod
{

    public function applyPayments(array &$bankTxn, array &$intacctUnpaidTxns)
    {
        $logMsg = ' Paying application method: ' . $this->genRcptDataObj->getPayApplicationMethod() . ' : ';
        $basecurr = GetBaseCurrency() === "" ? $bankTxn['LOCCURRENCY'] : GetBaseCurrency();
        foreach ($intacctUnpaidTxns as &$intacctUnpaidTxn) {
            if ($intacctUnpaidTxn['TRX_TOTALDUE'] == $bankTxn['AMOUNTTOMATCH']
                && !isset($intacctUnpaidTxn['PAYMENTAPPLIED'])) {
                $intacctUnpaidTxn['PAYMENTAPPLIED'] = self::FULLY_PAID;
                $bankTxn['AMOUNTTOMATCH'] = 0;
                $bankTxn["INTACCTTXNS"][] = $intacctUnpaidTxn;
                $trxkeyPath = PaymentUtils::$recordTypeToPath[$intacctUnpaidTxn['RECORDTYPE']];
                $bankTxn["RECEIVEPAYMENTOBJECT"] = [
                    "FINANCIALENTITY" => $bankTxn["FINANCIALENTITY"],
                    "PAYMENTMETHOD" => $this->genRcptDataObj->getPaymentMethod(),
                    "CUSTOMERID" => $bankTxn["CUSTOMERID"],
                    "DESCRIPTION" => $this->genRcptDataObj->getPaymentMemo(),
                    "RECEIPTDATE" => $bankTxn['POSTINGDATE'],
                    "PAYMENTDATE" => $bankTxn['POSTINGDATE'],
                    "DOCSOURCE" => BankTxnRuleUtil::BANK_DATASOURCE,
                    "DOCNUMBER" => $bankTxn['DOC#'],
                    "EXCH_RATE_TYPE_ID" => $this->genRcptDataObj->getExchRateTypeId(),
                    "BASECURR" =>   $basecurr,
                    "ACTION" => $this->genRcptDataObj->isDraftPayment() ? 'Draft' : 'Submit',
                    "PYMTDETAILS" => [
                        [
                            $trxkeyPath => $intacctUnpaidTxn["RECORDNO"],
                            "TRX_PAYMENTAMOUNT" => $intacctUnpaidTxn['TRX_TOTALDUE'],
                        ],
                    ],
                ];

                $logMsg = $logMsg . " *** " . "INTACCTTXNREORDNO: " . $intacctUnpaidTxn["RECORDNO"]
                    . " PAYMENTAMOUNT: " . $intacctUnpaidTxn['TRX_TOTALDUE']
                    . " STATE: " . ($intacctUnpaidTxn['PAYMENTAPPLIED'] ?? 'None')
                    . " Created Payment detail for: " . $bankTxn['RECEIVEPAYMENTOBJECT']['PYMTDETAILS'][0][$trxkeyPath] ?? '';

                logToFileWarning("GenerateReceipt-Log: " . __FILE__ . " " . __FUNCTION__
                    . " Creating payments for: "
                    . " BANKRECORDNO : " . $bankTxn["BANKTXNKEY"]
                    . $logMsg);

                break;
            }
        }

        if ($bankTxn['AMOUNTTOMATCH'] > 0 &&
            $this->genRcptDataObj->getCreateAdvances() == "true" &&
            !$this->genRcptDataObj->getIsPreview()) {
                $createAdvancePaymentMethod = new CreateAdvancePaymentMethod($this->genRcptDataObj);
                $intacctTxns = [];
                $createAdvancePaymentMethod->applyPayments($bankTxn, $intacctTxns);
        }
    }
}
