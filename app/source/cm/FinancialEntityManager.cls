<?
//=============================================================================
//
//	FILE:			FinancialEntityManager.cls
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	Entity manager for Financial Entitys (financialaccount table).
//
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

class FinancialEntityManager extends EntityManager
{
    /**
     * @param array $params
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array|array[]
     */
	function GetList($params = [], $_crosscny = false, $nocount = true) {
		$result = parent::GetList($params, $_crosscny, $nocount);
		
		//Apply Bank Restrictions
		if ( IsMultiVisibilitySubscribed('checkingaccount') || IsMultiVisibilitySubscribed('savingsaccount') ) { 
			$gManagerFactory = Globals::$g->gManagerFactory;
			$bankMgr = $gManagerFactory->getManager('bankaccount');
			$result = $bankMgr->FilterFinncialAccounts($result);
		}

		for($cnt = 0; $cnt < count($result); $cnt++) {
			if (isset($result[$cnt]['ACCTNO'])) {
				$result[$cnt]['ACCTNO'] = TwoWayDecrypt($result[$cnt]['ACCTNO']);
			}
		}

		return $result;
	}
    
    /**
     * Alter the filter parameters for all Get/GetList calls
     * 
     * @param array $params the filter parameters
     * 
     * @return array the filter parameters
     */
    protected function processContextClause(&$params)
    {
        // If we are at the entity level we need to filter out the list of bank account
        $ctx = GetContextLocation();
        if ( empty($ctx) || !IsMCMESubscribed() ) {
            return $params;
        }    
        
        // Show all the bank account with no location
        $params['filters'][0][] = array(
            'LOCATION#',
            'ISNULL',
            '',
            'r'
        );
        
        // Show only the bank account with a location that is within the same context we are 
        $filter = array(
            'LOCATION#',
            'INSUBQUERY',
            array(
                'STMT' => 'select location# from v_locationent where currency = ? and cny# = ?',
                'ARGS' => array(GetBaseCurrency(), GetMyCompany()),
                'ARGTYPES' => array('integer', 'integer'),
            ),
        );
        $params['filters'][0][] = $filter;
        
        return $params;
    }
    
}

