<?
/**
 * Entity for the bank transaction rule set.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 */
$kSchemas['banktxnruleset'] = array (

    'children' => array(
        'locationkey' => array('fkey' => 'location#', 'join' => 'outer','table'=>'locationmst'),
    ),

    'object' => array (
        'RECORDNO',
        'RULESETID',
        'RULESETNAME',
        'RULESETDESC',
        'ACCOUNTTYPE',
        'ACCOUNTCOUNT',
        'RULESETTYPE',
        'RULECOUNT',
        'STATUS',
        'LOCATIONKEY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),

    'publish' => array(
        'RECORDNO',
        'RULESETID',
        'RULESETNAME',
        'RULESETDESC',
        'ACCOUNTTYPE',
        'ACCOUNTCOUNT',
        'RULESETTYPE',
        'RULECOUNT',
        'STATUS',
        'LOCATIONKEY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),

    'schema' => array(
        'RECORDNO'      => 'record#',
        'RULESETID'     => 'rulesetid',
        'RULESETNAME'   => 'rulesetname',
        'RULESETDESC'   => 'rulesetdesc',
        'ACCOUNTTYPE'   => 'accounttype',
        'RULESETTYPE'   => 'rulesettype',
        'STATUS'        => 'status',
        'LOCATIONKEY'   => 'locationkey',
        'WHENCREATED'   => 'whencreated',
        'WHENMODIFIED'  => 'whenmodified',
        'CREATEDBY'     => 'createdby',
        'MODIFIEDBY'    => 'modifiedby',
        'SI_UUID'       => 'si_uuid',
    ),

    'ownedobjects' => array(
        array(
            'fkey' => 'RULESETKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'BANKTXNRULEMAP',
            'path' => 'RULEMAPS'
        )
    ),

    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED',
    ),

    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'path' => 'RULESETID',
            'fullname' => 'IA.ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
                'format' => $gRuleSetIDFormat
            ),
            'required' => true,
            'id' => 2,
        ),
        array(
            'path' => 'RULESETNAME',
            'fullname' => 'IA.NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ),
            'desc' => 'IA.RULE_SET_NAME',
            'required' => true,
            'id' => 3,
        ),
        array(
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'maxlength' => 500,
            'desc' => 'IA.RULESET_DESCRIPTION',
            'path' => 'RULESETDESC',
            'id' => 4,
        ),
        array(
            'fullname' => 'IA.ACCOUNT_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.BANK', 'IA.CREDIT_CARD'),
                'validvalues' => array('bank', 'creditcard'),
                '_validivalues' => array('ba', 'cc'),
            ),
            'desc' => 'IA.ACCOUNT_TYPE',
            'path' => 'ACCOUNTTYPE',
            'default' => 'bank',
            'required' => true,
            'id' => 5,
        ),
        [
            'path' => 'ACCOUNTCOUNT',
            'fullname' => 'IA.NO_OF_ACCOUNTS',
            'type' => [
                'ptype' => 'integer',
                'type'  => 'integer'
            ],
            'formula' => [
                'fields' => ['CNY#', 'RECORDNO'],
                'function' => "( (select count(1) from bankaccount where cny# = \${1} and rulesetkey = \${2}) + (select count(1) from ccdata where cny# = \${1} and rulesetkey = \${2}) ) ",
            ],
            'default' => 0,
            'calculated' => true,
            'id' => 6,
        ],
        [
            'path' => 'RULECOUNT',
            'fullname' => 'IA.NO_OF_RULES',
            'type' => [
                'ptype' => 'integer',
                'type'  => 'integer'
            ],
            'formula' => [
                'fields' => ['CNY#', 'RECORDNO'],
                'function' => "(select count(1) from BANKTXNRULEMAP where cny# = \${1} and rulesetkey = \${2})",
            ],
            'default' => 0,
            'calculated' => true,
            'id' => 7,
        ],
        array(
            'fullname' => 'IA.TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.MATCH_RULE', 'IA.CREATE_RULE', 'IA.ALL'),
                'validvalues' => array('match', 'create', 'all'),
                '_validivalues' => array('M', 'C', 'A'),
            ),
            'default' => 'all',
            'desc' => 'IA.RULESET_TYPE',
            'path' => 'RULESETTYPE',
            'id' => 8,
        ),
        array(
            'fullname' => 'IA.STATUS',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.ACTIVE', 'IA.INACTIVE'),
                'validvalues' => array('active', 'inactive'),
                '_validivalues' => array('T', 'F'),
            ),
            'default' => 'active',
            'desc' => 'IA.STATUS',
            'path' => 'STATUS',
            'id' => 9,
        ),
        array(
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'readonly' => true,
            'desc' => 'IA.LOCATION_KEY',
            'path' => 'LOCATIONKEY',
            'id' => 10,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        $gSiUuidFieldInfo,
    ),

    'table' => 'banktxnruleset',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'printas' => 'IA.BANK_TRANSACTION_RULESET',
    'pluralprintas' => 'IA.BANK_TRANSACTION_RULESETS',
    'sicollaboration' => true,
    'module' => 'cm',
    'auditcolumns' => true,
    'api' => array(
        'GET_BY_GET' => true,
        'ITEMS_ALIAS' => ['BANKTXNRULEMAPS'],
        'ITEM_ALIAS' => ['BANKTXNRULEMAP'],
        'ITEMS_INTERNAL' => ['RULEMAPS'],
    ),
    'platformProperties' => [
        // PRR needs this setting. If you intend to change it, please confirm it first with someone from the Platform
        // team
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ],

);
