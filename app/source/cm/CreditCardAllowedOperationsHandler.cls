<?php
/**
 * CreditCardAllowedOperationsHandler
 */

class CreditCardAllowedOperationsHandler extends AccountAllowedOperationsHandler
{

    /**
     * @param array $record
     * @param string|null $moduleKey
     * @return bool
     */
    protected function canReconcile(array $record, /** @noinspection PhpUnusedParameterInspection */ string $moduleKey = null): bool
    {
        if (!empty($record['VENDORID'])) {
            return true;
        }
        return false;
    }

    /**
     * @param array $record
     * @param string|null $moduleKey
     * @return bool
     */
    protected function hasReconciliationHistory(array $record, /** @noinspection PhpUnusedParameterInspection */ string $moduleKey = null): bool
    {
        if (!empty($record['VENDORID'])) {
            return true;
        }
        return false;
    }

    /**
     * @return array|string[]
     */
    protected function getFieldsForPermissionChecks(): array
    {
        $fields = parent::getFieldsForPermissionChecks();
        return array_merge($fields, ['VENDORID', 'LOCATION']);
    }

    /**
     * @return string
     */
    protected function getLocationFieldName(): string
    {
        return 'LOCATION';
    }
}