<?

//=============================================================================
//
//	FILE:		ACHFileGeneratorManager.cls
//	AUTHOR:		
//	DESCRIPTION:	A manager class for the ACHFileGeneratorManager entity
//
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================
import('ACHFileGenerator');
class ACHFileGeneratorManager extends EntityManager
{
    /* @var  array $prrecordTraceNumber */
	var $prrecordTraceNumber;

    /* @var array $prrecordDocumentNumber */
    private $prrecordDocumentNumber;

    /**
     * @return string
     */
    function getReverseMgrRecordtype()
    {
        return 'pp';
    }

    /**
     * @param string $verb
     * @param array  $obj
     * @param bool   $multiselect
     *
     * @return bool|string
     */
    function submit($verb, $obj, $multiselect = false)
    {
		if ( $verb == 'generate' ) {
			$achPaymentKeys = $obj['GENERATEACH'];
			$ok = $this->GenerateACHFile($verb, $achPaymentKeys);
		}

        /** @noinspection PhpUndefinedVariableInspection */
        return $ok;
    }

    /**
     * Overrides the superclass method in order to filter for entity-owned records only
     * in the case of being in a MEGA company context.
     *
     * @param string[] $atom array containing the essential components to build a WHERE clause
     * @param string   $_querytype (not documented or used in the EntityManager superclass method)
     * @param array    $qryarray array containing the WHERE clause statements being built
     *
     * @return string the final WHERE clause with all statements combined (used recursively)
     */
	function _ProcessWhereClause($atom, $_querytype, &$qryarray) {
		$whereClauseRet = parent::_ProcessWhereClause($atom, $_querytype, $qryarray);

		if ( $atom['operator'] ) {
			$contextLocation = GetContextLocation();
			if ( $contextLocation ) {
				$whereClauseRet .= " and (" . $this->_entity . ".locationkey = $contextLocation)";
			}
		}

		return $whereClauseRet;
	}

    /**
     * @param string $verb
     * @param array $achPaymentKeys
     *
     * @return bool|string
     */
    function GenerateACHFile(/** @noinspection PhpUnusedParameterInspection */ $verb, $achPaymentKeys)
    {
		$_done = Request::$r->_done;
		$_sess = Session::getKey();
				
		$source = "GenerateACHFileManager::GenerateACHFile";
		XACT_BEGIN($source);
		if(IsMultiEntityCompany() && !GetContextLocation()) {
		    SetReportViewContext();
		}

        $ok = PaymentManager::validatePymtForRestrictedUser($achPaymentKeys);

        $achGenerator = new ACHFileGenerator();

		$ok = $ok && $this->ProcessACHTraceNumber($achPaymentKeys);
		
        $ok = $ok && $achGenerator->GetPaymentRecordsDetails($achPaymentKeys, $paymentRecordDetails, ACHFileGenerator::ACHFILE_GENERATE_STATE);
		$traceNumbers = $this->prrecordTraceNumber;
        /** @noinspection PhpUndefinedVariableInspection */
        $ok = $ok && $achGenerator->GroupAndGenerateACHFile($paymentRecordDetails, $formattedACHPaymentRecord, $traceNumbers);

		
		$userLogin = GetMyLogin();
		
		$ok = $ok && XACT_COMMIT($source);
		if(!$ok){
			XACT_ABORT($source);
			return false;
		}else{
			$url = "lister.phtml?.op=5024&.sess=".$_sess."&.userlogin=" .$userLogin."&.fromGenACHFile=true";
		    Fwd($url, $_done);
			
			return $url;
		}
    }

    /**
     * @param array $achPaymentKeys
     *
     * @return bool
     */
    function ProcessACHTraceNumber($achPaymentKeys)
    {
        $cny = GetMyCompany();
		$qry = "select financialentity, record# from prrecordmst where cny# = :1 and vendentity is not null";

        $bankAccounts = [];

        $qry = PrepINClauseStmt($qry, $achPaymentKeys, " and prrecordmst.record# ");
        $qry .= "  order by prrecordmst.financialentity, prrecordmst.record#";
        $ACHPymtQry = array($qry, $cny);
        $ACHVendPymtRecords = QueryResult($ACHPymtQry);

        $qry = "select financialentity, record# from prrecordmst where cny# = :1 and empentity is not null";

        $qry = PrepINClauseStmt($qry, $achPaymentKeys, " and prrecordmst.record# ");
        $qry .= "  order by prrecordmst.financialentity, prrecordmst.record#";
        $ACHPymtQry = array($qry, $cny);
        $ACHEmpPymtRecords = QueryResult($ACHPymtQry);

		$ACHPymtRecords = array_merge($ACHVendPymtRecords, $ACHEmpPymtRecords);

		//getting all the distinct banks for the ach payments
		foreach($ACHPymtRecords as $bank){
			$bankAccounts[] = $bank['FINANCIALENTITY'];
		}
        /** @noinspection PhpUndefinedVariableInspection */
        $bankAccounts = array_unique($bankAccounts);
		
		//grouping the achpayment under the respective bank
		foreach($bankAccounts as $bank){
			foreach($ACHPymtRecords as $pymt){
				if($bank == $pymt['FINANCIALENTITY']){
					$achPymtsByBank[$bank][] = $pymt['RECORD#'];
				}
			}
		}

        /** @noinspection PhpUndefinedVariableInspection */
        return $this->GetTraceNumberForPrrecord($achPymtsByBank);
	}

    /**
     * @param array $achPaymentKeys
     *
     * @return bool
     */
    function RetrievePrrecordTraceNumbers($achPaymentKeys)
    {
        $qry = "select docnumber, record# from prrecordmst where cny# = :1 ";

        $qry = PrepINClauseStmt($qry, $achPaymentKeys, " and prrecordmst.record# ");
        $TraceNoQry = array($qry, GetMyCompany());
        $TraceNoRecords = QueryResult($TraceNoQry);
		
		if(!isset($TraceNoRecords)) { 
		    return false; 
		}

		foreach($TraceNoRecords as $traceRecord){
			$this->prrecordTraceNumber[$traceRecord['RECORD#']] = $traceRecord['DOCNUMBER'];
		}

		return true;
	}

    /**
     * @param array $achPymtsByBank
     *
     * @return bool
     */
    function GetTraceNumberForPrrecord($achPymtsByBank)
    {
		global $gErr;

        $gManagerFactory = Globals::$g->gManagerFactory;
        /** @var CheckingAccountManager $bankMgr */
		$bankMgr = $gManagerFactory->getManager('checkingaccount');
		$seqnumMgr = $gManagerFactory->getManager('seqnum');

		foreach($achPymtsByBank as $bank => $pymtkeys){
			$bankObj = $bankMgr->Get($bank);
			$useTraceNumSeq = $bankObj['USETRACENUMBER'];
			$traceNumSeqID	= $bankObj['TRACENUMBERSEQ'];
			$docNumSeqID	= $bankObj['DOCNUMBERSEQ'];

			foreach($pymtkeys as $pymtkey){
                $docNumSameAsTraceNum = false;
                if(!isset($traceNumSeqID) || $traceNumSeqID == ''){
                    $errStr = "No trace number sequence set for bank ".$bankObj['BANKACCOUNTID'];
                    $gErr->addError("BL03000097", GetFL(), $errStr);
                    return false;
                }
                if ($useTraceNumSeq && ( ($useTraceNumSeq == 'T') || ($useTraceNumSeq == 'true') )) {
                    $docNumSameAsTraceNum = true;
                } else if(isset($bankObj['DOCNUMBERSEQ']) && $bankObj['DOCNUMBERSEQ'] != '') {
                    $docNumSeqID = $bankObj['DOCNUMBERSEQ'];
                } else{
                    //we must have either trace number or document number sequence
                    $errStr = "Either a trace number sequence or a doc number sequence has to be set for the bank ".$bankObj['BANKACCOUNTID'];
                    $gErr->addError("BL03000097", GetFL(), $errStr);
                    return false;
                }

				$nextval = $seqnumMgr->GetNextSequence($traceNumSeqID);
				$this->prrecordTraceNumber[$pymtkey] = $bankObj['FINANCIALINSTITUTION'].$nextval;
                if (!$docNumSameAsTraceNum) {
                    if ($docNumSeqID != $traceNumSeqID) {
                        $this->prrecordDocumentNumber[$pymtkey] = $seqnumMgr->GetNextSequence($docNumSeqID);
                    } else {
                        $this->prrecordDocumentNumber[$pymtkey] = $nextval;
                    }
                } else {
                    $this->prrecordDocumentNumber[$pymtkey] = $this->prrecordTraceNumber[$pymtkey];
                }
			}

		}
		return true;
	}

    /**
     * @param array $achPaymentKeys
     * @param int|string $achFileKey
     *
     * @return bool
     */
    function PrintACHPayments($achPaymentKeys, $achFileKey)
    {
		if ( !is_array($achPaymentKeys) ) {
			return true;
		}


		$source = "ACHFileGeneratorManager::PrintACHPayments";
		XACT_BEGIN($source);

		foreach ($achPaymentKeys as $rkey) {
			if (!GetNObject('prrecord', $rkey, $prrecord) ) {
				# eppp ('error getting payment record');
				XACT_ABORT($source);
				return false;
			}
			$prrecord['BANKFILEKEY'] = $achFileKey;
			if (!$prrecord['DOCNUMBER']) {
				$pymtDocNumber = $this->prrecordDocumentNumber[$rkey];

				if(isset($pymtDocNumber) && $pymtDocNumber != ''){
					$prrecord['DOCNUMBER'] = $pymtDocNumber;
				}

				// Create a log for printing the ACH Tracenumber, for online delivery the entry 
				// will be made by prrecordeventmanagerpp
				$rtype = $prrecord['RECORDTYPE'];
 				if (!CreatePRRecordHistory($rtype, $rkey, "", PRRECORD_ACTION_DELIVER, '', false) ) {
					eppp_p("Unable to add the delivery history record.");
					XACT_ABORT($source);
					return false;
				}
		 	}
         	
			// Store the trace number and mark the state as printed
			$prrecord['STATE'] = PRRECORD_STATE_DELIVERED;

			 // Update payment prrecord
			 //eppp_p('Before updating prrecord');
			 if (!SetNObject('prrecord', $prrecord, $rkey) ) {
				 # eppp ('error getting payment record');
				 XACT_ABORT($source);
				 return false;
			 }
		}
		if ( ! XACT_COMMIT($source) ) {
		    XACT_ABORT($source);
		    return false;
		}
		return true;
	}

    /**
     * @param string|int $achFileRecordKey
     * @param string $achPymtKey
     *
     * @return bool
     */
    function VoidACHPaymentFile($achFileRecordKey, $achPymtKey = '')
    {
		if(IsMultiEntityCompany() && !GetContextLocation()) {
		    SetReportViewContext();
		}
		//if voiding one payment out of ACH file just void that payment else do for all payments in the ACH file
		if(isset($achPymtKey) && $achPymtKey != ''){
			$achPymtKeys = array($achPymtKey);
		}else{
			$achPymtKeys = $this->GetPrrecordKeysArray($achFileRecordKey);
		}

		if ( !is_array($achPymtKeys) ) {
			return true;
		}
        /**
         * Validate the all the payment lines visibility with tthe restricted user
         * impacted screen:
         *  a. Print checkes
         *  b. Check Run
         */
        $ok = PaymentManager::validatePymtForRestrictedUser($achPymtKeys);
        if(!$ok) {
            return $ok;
        }
		global $gErr;
		import('ReverseManager');

		$rType = $this->getReverseMgrRecordtype();
		$voidDate = GetCurrentDate();
		$reverseMgr = GetReverseManager($rType);
		$cny = GetMyCompany();
		
		$qry = 'select count(*) cnt from prrecord where cny# = :1 and bankfilekey = :2 and locationkey is not null';
		
		$locCnt = QueryResult(array($qry, $cny, $achFileRecordKey));
		
		$locCnt = $locCnt[0];
	

		if (isset($locCnt) && $locCnt['CNT']>0){

			$reverseMgr->entitylevelchecks  = true;
			
		}

		$source = "ACHFileGeneratorManger::voidACHPayments";
		XACT_BEGIN($source);

		$ok = true;
		foreach ( $achPymtKeys as $paymentKey ) {

			$ok = $ok && $reverseMgr->Reverse($paymentKey, $voidDate, $payReversal);

			if ( !$ok ) {
				break;
			} 

		}

		$ok = $ok && XACT_COMMIT($source);
		if ( ! $ok ) {
		    $errStr = "Error generated in while voiding ACH payments";
		    $gErr->addError("BL03000097", GetFL(), $errStr);
		    XACT_ABORT($source);
		    return false;
		}

		return true;
	}

    /**
     * @param string|int $achFileRecordKey
     *
     * @return bool
     */
    function ConfirmACHPaymentFile($achFileRecordKey)
    {
		global $gErr;
		$achPymtFileMgr = Globals::$g->gManagerFactory->getManager('achpaymentfile');
		if(IsMultiEntityCompany() && !GetContextLocation()) {
		    SetReportViewContext();
		}

		$achPymtModuleBased = $this->GetPrrecordKeysBasedOnModuleKeyArray($achFileRecordKey);

		if ( !is_array($achPymtModuleBased) ) {
			return true;
		}

		$source = "ACHFileGenerationManager::Submit";
		XACT_BEGIN($source);
        
        $memos = array();
        $paymentRecEntityMap = array();

        foreach ($achPymtModuleBased as $moduleKey => $achPymtKeys) {
            if ($moduleKey === Globals::$g->kAPid) {
                $paymentController = new APPaymentController();
                $ok = $paymentController->confirmPayments($achPymtKeys, $memos, $paymentRecEntityMap);
            } else {
                /**
                 * Validate the all the payment lines visibility with tthe restricted user
                 * impacted screen:
                 *  a. Print checkes
                 *  b. Check Run
                 */
                $ok = PaymentManager::validatePymtForRestrictedUser($achPymtKeys);

                $ok = $ok && ConfirmPayments($achPymtKeys, $memos, [], false, $paymentRecEntityMap);
            }
        }

		if ( !$ok ) {
			// No error message reqd. sinc the api is adding to HasErrors.
			$gErr->addError("SL-0544", __FILE__ . __LINE__, "Error Confirming Payments");
			$ok = false;
		} 

		//Updating ach payment file state to confirmed
		$cny = GetMyCompany();
		$ok = $ok && $achPymtFileMgr->DoQuery("QRY_ACHPAYMENTFILE_UPDATE_STATE", array('C', $cny, $achFileRecordKey));

		//eppp_p($achPymtKeys);
		//diefl();

		$ok = $ok && XACT_COMMIT($source);
		if (!$ok) {
			XACT_ABORT($source);
		} else {
        unset($paymentRecEntityMap['MODULEKEY']);
        $paymentModuleKeyArray=array();
            foreach( $paymentRecEntityMap as $paymentRecEntityMapValue) {
            $paymentModuleKeyArray[]=$paymentRecEntityMapValue['MODULEKEY'];
        }
        $paymentModuleKeyArray=INTACCTarray_unique($paymentModuleKeyArray);
        foreach($paymentModuleKeyArray as $paymentModuleKey){
            /** @noinspection PhpUnusedLocalVariableInspection */
            $res = SendPymtNotificationEmail($paymentRecEntityMap, $paymentModuleKey);
        }
		}

		return $ok;
	}

    /**
     * @param string|int $achFileRecordKey
     *
     * @return array
     */
    function GetPrrecordKeysArray($achFileRecordKey)
    {
		$cny = GetMyCompany();
		$qry = 'select record# recordno from prrecordmst where cny# = :1 and bankfilekey = :2';
		$achPymtRecords = QueryResult(array($qry, $cny, $achFileRecordKey));
		foreach($achPymtRecords as $achPymt){
			$achPymtKeys[] = $achPymt['RECORDNO'];
		}

        /** @noinspection PhpUndefinedVariableInspection */
        return $achPymtKeys;
	}

     // DB value for filter comparison may be a function

    /**
     * @param array $filterAtom
     *
     * @return string
     */
    function GetFieldTypeForFilter($filterAtom)
    {
        list( , $filterField) = explode(".", $filterAtom[0]);
        $filterField = ( $filterField == $filterAtom[0] || $filterField == '' ? $filterAtom[0] : $filterField );

        switch ( isl_strtoupper($filterField) ) {
            case 'FINANCIALENTITY':
                // a hack to make financialentity filter work without adding the field to the dbschema
                $ret = 'text';
                break;
            case 'PAYMETHODKEY':
                // a hack to ensure we have Printed Checks without adding the field to the dbschema
                $ret = 'integer';
                break;
            default:
                $ret = parent::GetFieldTypeForFilter($filterAtom);
                break;
        }

        return $ret;
    }

    /**
     * @param string|int $achFileRecordKey
     *
     * @return array
     */
    function GetPrrecordKeysBasedOnModuleKeyArray($achFileRecordKey)
    {
        $cny = GetMyCompany();
        $qry = 'select record# recordno, modulekey from prrecordmst where cny# = :1 and bankfilekey = :2';
        $achPymtRecords = QueryResult(array($qry, $cny, $achFileRecordKey));
        foreach($achPymtRecords as $achPymt){
            $achPymtKeys[$achPymt['MODULEKEY']][] = $achPymt['RECORDNO'];
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $achPymtKeys;
    }
}


