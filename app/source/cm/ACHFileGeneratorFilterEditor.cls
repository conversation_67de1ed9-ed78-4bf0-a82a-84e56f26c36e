<?

/**
*    FILE:        ACHFileGeneratorFilterEditor.cls
*    AUTHOR:        G P Sri Vidta
*    DESCRIPTION:    A class for showing the Filter Editor (Filtering) screens for 
*            APPrintChecks.
*
*    (C)2000, Intacct Corporation, All Rights Reserved
*
*    Intacct Corporation Proprietary Information.
*    This document contains trade secret data that belongs to Intacct 
*    corporation and is protected by the copyright laws. Information herein 
*    may not be used, copied or disclosed in whole or part without prior 
*    written consent from Intacct Corporation.
*/


import('PrintChecksFilterEditor');



class ACHFileGeneratorFilterEditor extends PrintChecksFilterEditor
{
    /**
     * @param string $modKey
     *
     * @return array
     */
    function GetCheckingAccountSelector($modKey)
    {
        $bnkMgr = Globals::$g->gManagerFactory->getManager('checkingaccount');
        $map = $bnkMgr->GetBankAccountMap('ACH', "", "-----All Checking Accounts-----", "IA.ALL_CHECKING_ACCOUNTS", "");
            
        $selector = array ( 
        'fullname' => _('Checking account'),
        'type' => array ( 
        'ptype' => 'enum',
        'type' => 'enum',
        'validvalues' => array_keys($map),
        'validlabels' => array_values($map),
        ),
        'default' => GetMyDefaultFinAcctID($modKey),
        'desc' => 'checking account',
        'path' => 'ACCOUNTID'
        );
    
        return $selector;
    }

    

}
