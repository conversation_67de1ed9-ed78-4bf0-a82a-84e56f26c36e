<?php

/**
 * Utilit class to process the reconciliation tasks on the accounts.
 *
 * <AUTHOR>
 * @copyright 2018 Sage Intacct Inc., All Rights Reserved
 */
define('TXNTYPE_CHECKSDEBITS_INIT', _('Checks/Debits'));
define('UNCLEARED_CD_INIT', _('Uncleared Intacct - Checks/Debits'));
define('CLEARED_CD_INIT', 'Cleared Intacct - Checks/Debits');

class ReconciliationUtils
{
    // Constants reconciliation modes
    const RECONMODE_MANUAL = 'M';
    const RECONMODE_AUTOMATCH = 'A';
    const RECONMODE_SELF_MATCHED = 'S';
    const MATCHMODE_SELF_MATCHED = 'S';
    const MATCHMODE_MANUALLY_UNMATCHED = 'U';
    const MATCHMODE_MANUAL = 'M';
    const MATCHMODE_AUTOMATCH = 'A';
    const MATCHMODE_UNMATCH = 'F';
    const RECONMODE_AUTOMATCHREVIEW = 'R';
    const RECONMODE_MANUAL_VAL = 'Manual';
    const RECONMODE_AUTOMATCH_VAL = 'Automatch';
    const RECONMODE_AUTOMATCHREVIEW_VAL = 'AutomatchReview';
    // Constants for reconciliation states
    const RECONSTATE_INITIATED = 'I';
    const RECONSTATE_DRAFT = 'D';
    const RECONSTATE_RECONCILED = 'R';
    const RECONSTATE_INITIATED_VAL = 'initiated';
    const RECONSTATE_DRAFT_VAL = 'draft';
    const RECONSTATE_RECONCILED_VAL = 'reconciled';
    // Constants for transaction types
    const TRXTTYPE_DEBIT = 'D';
    const TRXTTYPE_CREDIT = 'C';
    const TRXTTYPE_DEBIT_W = 'withdrawal';
    const TRXTTYPE_CREDIT_D = 'deposit';
    const TRXTTYPE_DEBIT_CC_W = 'charge';
    const TRXTTYPE_CREDIT_CC_D = 'payment';
    // Constants for the reconciliation view options
    const UNCLEARED_CD = UNCLEARED_CD_INIT;
    const UNCLEARED_DC = 'Uncleared Intacct - Depostis/Credits';
    const UNCLEARED_ALL = 'Uncleared Intacct - All';
    const CLEARED_CD = CLEARED_CD_INIT;
    const CLEARED_DC = 'Uncleared Intacct - Depostis/Credits';
    const CLEARED_ALL = 'Cleared Intacct - All';

    const STATE_MATCHED = 'Matched';
    const STATE_MATCHED_LABEL = 'IA.MATCHED';
    const STATE_DRAFT_MATCHED = 'Draft matched';
    const STATE_DRAFT_MATCHED_LABEL = 'IA.DRAFT_MATCHED';
    const STATE_CLEARED = 'Cleared';
    const STATE_CLEARED_LABEL = 'IA.CLEARED';
    const STATE_UNMATCHED = 'Unmatched';
    const STATE_UNMATCHED_LABEL = 'IA.UNMATCHED';
    const STATE_PARTIALLY_MATCHED = 'Partially matched';
    const STATE_PARTIALLY_MATCHED_LABEL = 'IA.PARTIALLY_MATCHED';
    const STATE_SELECTED_TO_MATCH = 'Selected to match';
    const STATE_SELECTED_TO_MATCH_LABEL = 'IA.SELECTED_TO_MATCH';
    const STATE_SELECTED_TO_UNMATCH = 'Selected to unmatch';
    const STATE_SELECTED_TO_UNMATCH_LABEL = 'IA.SELECTED_TO_UNMATCH';
    const STATE_ALL = 'All';
    const STATE_ALL_LABEL = 'IA.ALL';
    const STATE_IGNORED = 'Ignored';
    const STATE_IGNORED_LABEL = 'IA.IGNORED';
    const STATE_SOFT_DELETED = 'Soft deleted';

    const STATE_COMPLETED = 'C';
    const STATE_INPROGRESS = 'I';
    const STATE_FAILED = 'F';

    const STATE_COMPLETED_VAL = 'processed';
    const STATE_INPROGRESS_VAL = 'inprogress';
    const STATE_FAILED_VAL = 'failed';

    const TXNTYPE_CHECKSDEBITS = TXNTYPE_CHECKSDEBITS_INIT;
    const TXNTYPE_CHECKSDEBITS_LABEL = 'IA.CHECKS_DEBITS';
    const TXNTYPE_DEPOSITSCREDITS = 'Deposits/Credits';
    const TXNTYPE_DEPOSITSCREDITS_LABEL = 'IA.DEPOSITS_CREDITS';
    const TXNTYPE_ALL = 'All';
    const TXNTYPE_ALL_LABEL = 'IA.ALL';

    //Cleared status
    const SELECTED = 'S';
    const CLEARED = 'T';
    const MATCHED = 'M';
    const DRAFT_MATCHED = 'L';
    const SELECTED_TO_UNMATCH = 'U';
    const UNCLEARED = 'F';
    const DUPLICATE = 'E';
    const IGNORED = 'I';
    const PARTIALLY_MATCHED = 'P';
    const SOFT_DELETED = 'D';

    //ignore action
    const ACTION_IGNORE_TRANSACTIONS = 'IGN';
    const ACTION_UNIGNORE_TRANSACTIONS = 'UIGN';

    //ignore action
    const ACTION_IGNORE_LABEL = 'IA.IGNORE';
    const ACTION_UNIGNORE_LABEL = 'IA.UNIGNORE';

    // Account type
    const ACCOUNT_TYPE_BANK = 'ba';
    const ACCOUNT_TYPE_CREDIT = 'cc';

    // Full Account Type
    const ACCOUNT_TYPE_BANK_BA = 'Bank';
    const ACCOUNT_TYPE_CREDIT_CC = 'Credit Card';

    // File types
    const FEEDTYPE_CSV = 'csv';
    const FEEDTYPE_XML = 'xml';
    const FEEDTYPE_ONLINE = 'onl';
    const FEEDTYPE_QIF = 'qif';

    const FEEDTYPE_CSV_LABEL = "IA.CSV_IMPORT";
    const FEEDTYPE_XML_LABEL = "IA.XML_IMPORT";
    const FEEDTYPE_ONLINE_LABEL = "IA.ONLINE_FEED";
    const FEEDTYPE_QIF_LABEL = "IA.QIF_IMPORT";

    // Source record recordTypes
    const RECORDTYPE_GL = 'gl';
    const RECORDTYPE_PR = 'pr';
    const RECORDTYPE_INITIAL = 'initial';

    // date format
    const DATE_FORMAT = 'm/d/Y';
    /* @var array $recordTypeToPaymentDescMap */
    public static $recordTypeToPaymentDescMap = array(
        'cb' => 'IA.DEBIT_CARD_TRANSACTION',
        'cc' => 'IA.CREDIT_CARD_FEES',
        'cd' => 'IA.DEPOSIT_SLIPS',
        'ch' => 'IA.BANK_CHARGE',
        'ci' => 'IA.CREDIT_CARD_CHARGE',
        'ck' => 'IA.MANUAL_CHECK',
        'cn' => 'IA.BANK_INTEREST',
        'cp' => 'IA.CREDIT_CARD_PAYMENT',
        'cq' => 'IA.QUICK_INVOICE',
        'cr' => 'IA.OTHER_RECEIPTS',
        'ct' => 'IA.FUNDS_TRANSFER',
        'cw' => 'IA.CASH_MGMT_PRINTED_QUICK_CHECK',
        'cx' => 'IA.CHARGE_PAYOFF_PAYMENT',
        'ei' => 'IA.EMPLOYEE_EXPENSES',
        'em' => 'IA.EXPENSE_EXPENSE_REALIZED_MULTI_CURRENCY_GAIN_LOSS',
        'eo' => 'IA.APPLIED_EMPLYEE_ADVANCE',
        'ep' => 'IA.EMPLOYEE_REIMBURSEMENTS',
        'er' => 'IA.EMPLOYEE_ADVANCE',
        'pa' => 'IA.AP_ADJUSTMENTS',
        'pd' => 'IA.AP_DISCOUNT',
        'pe' => 'IA.INTER_ENTITY_PAYABLE',
        'pi' => 'IA.AP_BILL',
        'pm' => 'IA.AP_REALIZED_MULTI_CURRENCY_GAIN_LOSS',
        'po' => 'IA.AP_APPLIED_ADVANCE',
        'pp' => 'IA.AP_PAYMENT',
        'pr' => 'IA.AP_ADVANCE',
        'ra' => 'IA.AR_ADJUSTMENTS',
        'rd' => 'IA.AR_DISCOUNT',
        're' => 'IA.INTER_ENTITY_RECEIVABLE',
        'ri' => 'IA.AR_INVOICE',
        'rm' => 'IA.AR_REALIZED_MULTI_CURRENCY_GAIN_LOSS',
        'ro' => 'IA.AR_APPLIED_ADVANCE_OVERPAYMENT',
        'rp' => 'IA.AR_RECEIPTS',
        'rl' => 'IA.AR_RECEIPTS',
        'rr' => 'IA.AR_ADVANCE',
        'rf' => 'IA.CUSTOMER_REFUND',
        'gl' => 'IA.JOURNAL_ENTRY',
        'rpgl' => 'IA.AR_JOURNAL_ENTRY',
        'ppgl' => 'IA.AP_JOURNAL_ENTRY',
        'rpigl' => 'IA.AR_JOURNAL_ENTRY',
        'ppigl' => 'IA.AP_JOURNAL_ENTRY',
        'rpoi' => 'IA.AR_INITIAL_OPEN_ITEMS',
        'ppoi' => 'IA.AP_INITIAL_OPEN_ITEMS',
        'rpsoi' => 'IA.AR_INITIAL_OPEN_ITEMS',
        'ppsoi' => 'IA.AP_INITIAL_OPEN_ITEMS',
        'rcoi' => 'IA.AR_INITIAL_OPEN_ITEMS',
        'pcoi' => 'IA.AP_INITIAL_OPEN_ITEMS',
        'rcsoi' => 'IA.AR_INITIAL_OPEN_ITEMS',
        'pcsoi' => 'IA.AP_INITIAL_OPEN_ITEMS',
    );

    /* @var array $reconStateToReconDescMap */
    public static $reconStateToReconDescMap = array(
        self::RECONSTATE_INITIATED_VAL => 'IA.INITIATED',
        self::RECONSTATE_DRAFT_VAL => 'IA.DRAFT',
        self::RECONSTATE_RECONCILED_VAL => 'IA.RECONCILED',
    );

    /* @var array $feedStateToFeedDescMap */
    public static $feedStateToFeedDescMap = array(
        self::STATE_INPROGRESS_VAL => 'IA.IN_PROGRESS',
        self::STATE_FAILED_VAL => 'IA.FAILED',
        self::STATE_COMPLETED_VAL => 'IA.COMPLETED',
    );

    /** @var array $validMatchAttributes */
    public static $validMatchAttributes = ['DOCNO', 'AMOUNT', 'POSTINGDATE'];

    /** @var array $validMatchAttributesLabels */
    public static $validMatchAttributesLabels = ['Document number', 'Amount', 'Date'];

    /* @var array $clearedFlagDescriptionMap */
    // public static $clearedFlagDescriptionMap = array (
    //     self::UNCLEARED => self::STATE_UNMATCHED,
    //     self::MATCHED => self::STATE_MATCHED,
    //     self::PARTIALLY_MATCHED => self::STATE_PARTIALLY_MATCHED,
    // );

    /* @var array $clearedFlagDescriptionMap */
    public static $clearedFlagLabelToIValueMap = array(
        self::STATE_MATCHED => self::MATCHED,
        self::STATE_CLEARED => self::CLEARED,
        self::STATE_UNMATCHED => self::UNCLEARED,
        self::PARTIALLY_MATCHED => self::PARTIALLY_MATCHED,
        self::STATE_SELECTED_TO_MATCH => self::SELECTED,
        self::STATE_SELECTED_TO_UNMATCH => self::SELECTED_TO_UNMATCH,
        self::STATE_IGNORED => self::IGNORED,
        self::STATE_DRAFT_MATCHED => self::DRAFT_MATCHED,
        self::STATE_SOFT_DELETED => self::SOFT_DELETED
    );


    /** @var array $bankTxnTypeDescMap */
    public static $bankTxnTypeDescMap = array(
        self::TRXTTYPE_DEBIT_W => self::TRXTTYPE_DEBIT,
        self::TRXTTYPE_CREDIT_D => self::TRXTTYPE_CREDIT,
        self::TRXTTYPE_DEBIT_CC_W => self::TRXTTYPE_DEBIT,
        self::TRXTTYPE_CREDIT_CC_D => self::TRXTTYPE_CREDIT,
    );

    /** @var array $accountTypeDescMap */
    public static $accountTypeDescMap = array(
        self::ACCOUNT_TYPE_BANK_BA => self::ACCOUNT_TYPE_BANK,
        self::ACCOUNT_TYPE_CREDIT_CC => self::ACCOUNT_TYPE_CREDIT,
    );

    /** @var array $clearedStateLabelMap */
    public static $clearedStateLabelMap = array(
        self::STATE_MATCHED => self::STATE_MATCHED_LABEL,
        self::STATE_DRAFT_MATCHED => self::STATE_DRAFT_MATCHED_LABEL,
        self::STATE_CLEARED => self::STATE_CLEARED_LABEL,
        self::STATE_UNMATCHED => self::STATE_UNMATCHED_LABEL,
        self::STATE_PARTIALLY_MATCHED => self::STATE_PARTIALLY_MATCHED_LABEL,
        self::STATE_IGNORED => self::STATE_IGNORED_LABEL,
        self::STATE_SELECTED_TO_MATCH => self::STATE_SELECTED_TO_MATCH_LABEL,
        self::STATE_SELECTED_TO_UNMATCH => self::STATE_SELECTED_TO_UNMATCH_LABEL
    );

    // Labels for credit card
    const UNCLEARED_CD_CC = 'Uncleared Intacct - Charges';
    const UNCLEARED_DC_CC = 'Uncleared Intacct - Payments';
    const CLEARED_CD_CC = 'Cleared Intacct - Checks/Debits';
    const CLEARED_DC_CC = 'Cleared Intacct - Payments';
    const TXNTYPE_CHECKSDEBITS_CC = 'Charges';
    const TXNTYPE_CHECKSDEBITS_CC_LABEL = 'IA.CHARGES';
    const TXNTYPE_DEPOSITSCREDITS_CC = 'Payments';
    const TXNTYPE_DEPOSITSCREDITS_CC_LABEL = 'IA.PAYMENTS';
    /* @var string[] $creditCardLabels */
    public static $creditCardLabels = [
        'CLEAREDAMT_DEBIT' => 'IA.MATCHED_CHARGES',
        'CLEAREDAMT_DEBIT_A' => 'IA.MATCHED_CHARGES',
        'CLEAREDAMT_CR' => 'IA.MATCHED_PAYMENTS',
        'CLEAREDAMT_CR_A' => 'IA.MATCHED_PAYMENTS',
        'SELECTTOMATCHAMT_DB' => 'IA.CHARGES',
        'SELECTTOMATCHAMT_CR' => 'IA.PAYMENTS',
    ];

    /**
     * Get the end date for the statement date which is Always the last day of the month
     *
     * @param string $stmtdate
     *
     * @return string last date of the month same as that of the statement date.
     */
    public static function constructEndDate($stmtdate)
    {
        if (Globals::$g->gNonStandardPeriods) {
            $StartAndEndDates = Period2Dates(Date2Period($stmtdate));
            $endDate =  $StartAndEndDates[1];
            // Incase of non standard period not available, send back the statement date as end date
            if (isNullOrBlank($endDate) || $endDate === -1) {
                $endDate = $stmtdate;
            }
            return $endDate;
        } else {
            return GetLastDateOfMonth($stmtdate);
        }
    }

    /**
     * @param string $action
     *
     * @return bool
     */
    public static function validateAction($action)
    {
        return !empty ($action)
            && ($action == self::ACTION_IGNORE_TRANSACTIONS
                || self::ACTION_UNIGNORE_TRANSACTIONS);
    }

    /**
     * @param string $action
     *
     * @return string
     */
    public static function getIValueForAction($action)
    {
        $iValue = '';
        if (self::validateAction($action)) {
            switch ($action) {
                case self::ACTION_IGNORE_TRANSACTIONS:
                    $iValue = self::IGNORED;
                    break;
                case self::ACTION_UNIGNORE_TRANSACTIONS:
                    $iValue = self::UNCLEARED;
                    break;
                default:
                    $iValue = '';
            }
        }
        return $iValue;
    }

    /**
     * Method to return the transaction type for reconciliation view.
     *
     * @param string $trxType
     *
     * @return null|string
     */
    public static function getTrxTypeForReconView($trxType)
    {
        if ($trxType == self::TXNTYPE_CHECKSDEBITS || $trxType == self::TXNTYPE_CHECKSDEBITS_CC) {
            return self::TRXTTYPE_DEBIT;
        } else if ($trxType == self::TXNTYPE_DEPOSITSCREDITS || $trxType == self::TXNTYPE_DEPOSITSCREDITS_CC) {
            return self::TRXTTYPE_CREDIT;
        }
        return null;
    }

    /**
     * Method to return the cleared flag for the reconciliation view.
     *
     * @param string $reconState
     *
     * @return null|string
     */
    public static function getClearFlagForReconView($reconState)
    {
        if ($reconState == self::STATE_ALL) {
            return null;
        }
        return $reconState;
    }

    /**
     * Method to return the cleared flag for the reconciliation view.
     *
     * @param string $label
     *
     * @return null|string
     */
    public static function getClearIValueForLabel($label)
    {
        if (!empty($label)) {
            return self::$clearedFlagLabelToIValueMap[$label];
        }
        return null;
    }

    /**
     * Returns the array with unique key and value.
     *
     * @param array $records
     *
     * @return array
     */
    public static function getRecordDataWithKey($records)
    {
        return self::getRecordDataWithIuputKey($records, 'RECORDNO');
    }

    /**
     * Returns the array with unique key and value.
     *
     * @param array $records
     * @param string $key
     *
     * @return array
     */
    public static function getRecordDataWithIuputKey($records, $key)
    {
        $keyValueData = [];
        if (!empty($records)) {
            foreach ($records as $record) {
                $keyValueData[$record[$key]] = $record;
            }
        }
        return $keyValueData;
    }

    /**
     * Returns the array with unique key and value.
     *
     * @param array $reconRecords
     *
     * @return array
     */
    public static function getReconRecordDataWithKey($reconRecords)
    {
        $keyValueData = [];
        if (!empty($reconRecords)) {
            foreach ($reconRecords as $reconRecord) {
                $key = $reconRecord['RECORDKEY'] . '-' . $reconRecord['RECORDTYPE'];
                if (empty($reconRecord['PAYEE']) && !empty($reconRecord['PAYER'])) {
                    $reconRecord['PAYEE'] = $reconRecord['PAYER'];
                }
                $keyValueData[$key] = $reconRecord;
            }
        }
        return $keyValueData;
    }

    /**
     * Returns the array with unique key and value.
     *
     * @param array $records
     * @param string $key1
     * @param string $key2
     * @param string $delimiter
     *
     * @return array
     */
    public static function getDataMapWithKey($records, $key1, $key2, $delimiter)
    {
        $keyValueData = [];
        if (!empty($records)) {
            foreach ($records as $record) {
                $key = $record[$key1] . $delimiter . $record[$key2];
                $keyValueData[$key] = $record;
            }
        }
        return $keyValueData;
    }

    /**
     * Returns the array with unique key and value.
     *
     * @param array $records
     *
     * @return array
     */
    public static function getBankTxnRecordNos($records)
    {
        $data = [];
        if (!empty($records)) {
            foreach ($records as $record) {
                $data[] = $record['FINACCTTXNRECORDKEY'];
            }
        }
        return $data;
    }


    /**
     * Returns the subdocument key for the given document name/id.
     *
     * @param string $docId
     *
     * @return null|string
     */
    public static function getSupdocKey($docId)
    {
        if (isset($docId)) {
            $supdocmgr = Globals::$g->gManagerFactory->getManager('supportingdocuments');
            $cny = GetMyCompany();
            $supdockey = $supdocmgr->DoQuery('QRY_SUPPORTINGDOCUMENTS_SELECT_BY_DOCUMENTID', array($docId, $cny));
            if (!empty($supdockey)) {
                return $supdockey[0]['RECORD#'];
            }
        }
        return null;
    }

    /**
     * Returns the document name for the given document record number.
     *
     * @param string $docNo
     *
     * @return null|string
     */
    public static function getSupdocName($docNo)
    {
        if (isset($docNo)) {
            $query = "SELECT DOCUMENTID, DOCUMENTNAME FROM SUPDOC WHERE CNY# = :1 and RECORD# = :2";
            $supdoc = QueryResult(array($query, GetMyCompany(), $docNo));
            if (!empty($supdoc)) {
                return $supdoc[0]['DOCUMENTID'] . '--' . $supdoc[0]['DOCUMENTNAME'];
            }
        }
        return null;
    }

    /**
     * Returns the document name for the given document record number.
     *
     * @param string $recordId
     *
     * @return null|string
     */
    public static function getLoginId($recordId)
    {
        if (isset($recordId)) {
            $query = "SELECT LOGINID FROM USERINFO WHERE CNY# = :1 and RECORD# = :2";
            $result = QueryResult(array($query, GetMyCompany(), $recordId));
            if (!empty($result)) {
                return $result[0]['LOGINID'];
            }
        }
        return null;
    }

    /**
     * Returns if the bank currency is base currency or not.
     *
     * @param string $bankCurrency
     *
     * @return bool
     */
    public static function isBaseBank($bankCurrency)
    {
        // For Atlas company at root there is no base currency set, so return default true
        if (IsMCMESubscribed() && !GetContextLocation()) {
            return true;
        }
        return (empty($bankCurrency) || $bankCurrency == GetBaseCurrency());
    }

    /**
     * @param string $finEntity
     * @return array $fields
     */
    public static function getLastReconciliationType($finEntity)
    {
        $fields = [];
        $finxtxnfeedFields = EntityManager::GetListQuick('bankacctrecon', ['FEEDTYPE', 'MODE'],
            ['FINANCIALENTITY' => $finEntity,
                'STATE' => self::RECONSTATE_RECONCILED_VAL],
            [['STMTENDINGDATE', 'DESC']]);
        if ($finxtxnfeedFields) {
            $fields['LASTTRANSACTIONTYPE'] = $finxtxnfeedFields[0]['MODE'] == 'Manual'
                ? I18N::getSingleToken('IA.VIA_MANUAL') :
                ($finxtxnfeedFields[0]['FEEDTYPE'] == self::FEEDTYPE_ONLINE ? I18N::getSingleToken('IA.VIA_FEED')
                    : I18N::getSingleToken('IA.VIA_IMPORT'));
            $fields['LASTFILETYPE'] = $finxtxnfeedFields[0]['FEEDTYPE'];
            $fields['LASTRECONMODE'] = $finxtxnfeedFields[0]['MODE'];

        }
        return $fields;
    }

    /**
     * find the account has any active reconciliation going on
     * @param string|null $finEntity
     * @return bool
     */
    public static function accountHasActiveReconciliation(string|null $finEntity): bool
    {
        if (!empty($finEntity)) {
            $result = QueryResult(["SELECT count(1) as CNT FROM acctrecon WHERE cny#=:1
                    AND financialentity = :2
                      AND state in (:3,:4)",
                GetMyCompany(), $finEntity,  self::RECONSTATE_DRAFT, self::RECONSTATE_INITIATED]);

            return is_array($result) && isset($result[0]['CNT']) && $result[0]['CNT'] !== "0";
        }
        return false;
    }

    /**
     * Get the banks to reconcile
     *
     * @param int|string $accountid filter by the account if it is selected, $showOptions
     * @param bool $showOptions
     * @param bool $toMatchTxn
     * @param bool $activeOnly
     *
     * @return array|bool
     */
    public static function getBanksToReconcile($accountid, $showOptions, $toMatchTxn = false, $activeOnly = true)
    {
        $_sess = Session::getKey();
        $_type = Request::$r->_type;

        $selects = "financialdata.type as FINACCTTYPE, accountid, accountno, name, glaccountkey, recbal, recdate, inpbal, inpdate, cutoffdate, financialdata.reconmode as reconmode, reconparams as reconparams, currency, inpsupdockey, rulesetkey, location#, dept#, seqnumkey, seqforautomatch, seqformanualmatch  ";

        $whereStmt = [''];
        $argn = 1;
        $argc = 2;
        $wheres = '';

        if ($showOptions) {
            if (!empty($_type)) {
                $wheres = ($_type == 'chk') ? "bankaccount.type = 'CHECKING'" : "bankaccount.type = 'SAVINGS'";
            }
        } else {
            $wheres = "accountid = :" . $argc++;
            $whereStmt[$argn++] = $accountid;

        }

        $wheres .= ($wheres ? " and " : "") ;
        if ($activeOnly) {
            $wheres .= " status = 'T'";
        }

        $orderby = "accountid";

        if (IsMultiEntityCompany() && GetContextLocation()) {
            //Bank transactions are shown at entity level , so should be able to create transactions and match
            if ($toMatchTxn) {
                $wheres = ($wheres ? " $wheres and " : '')
                    . " bankaccount.location# in ( select locationkey from sessionlocbelow where sessionkey = :"
                    . $argc . " )";
            } else {
                //$showbuttons = false;
                $wheres = ($wheres ? " $wheres and " : '')
                    . " bankaccount.locationkey in ( select locationkey from sessionlocbelow where sessionkey = :"
                    . $argc . " )";
            }
            $whereStmt[$argn] = $_sess;
        }

        $whereStmt[0] = $wheres;

        $accts = GetBankAccounts($selects, $whereStmt, $orderby);

        return $accts;
    }

    /**
     * Returns financial entity location.
     *
     * @param string    $accountid
     * @param bool      $isBank
     * @return string|null
     */
    public static function getFinancialEntityLocation($accountid, $isBank)
    {
        $table = $isBank ? 'BANKACCOUNT' : 'CCDATA';
        $column = $isBank ? 'ACCOUNTID' : 'CARDID';
        $query = "SELECT LOCATIONKEY FROM $table WHERE CNY# = :1 and $column = :2";
        $result = QueryResult(array($query, GetMyCompany(), $accountid));
        if (!empty($result)) {
            return $result[0]['LOCATIONKEY'];
        }
        return null;
    }

    /**
     * Get the banks to reconcile
     *
     * @param int|string $cardId filter by the card if it is selected
     * @param bool $chkExpiry
     * @param bool $toMatchTxn
     *
     * @return array|bool
     */
    public static function getCreditsToReconcile($cardId, $chkExpiry = false, $toMatchTxn = false)
    {
        $atlas = IsMCMESubscribed();
        $isMe = IsMultiEntityCompany();
        $basecurr = GetBaseCurrency();
        $entityLoc = GetContextLocation();
        $types = ['Credit'];

        $selects = "CARDTYPE, CARDID, CARDNUM, LIABACCTKEY as GLACCOUNTKEY, VENDORID, LOCATION#, LOCATIONKEY, LIABILITYTYPE, recbal, recdate, inpbal, inpdate, cutoffdate, inpsupdockey, rulesetkey, dept#, financialdata.seqnumkey, financialdata.seqforautomatch, financialdata.seqformanualmatch ";

        $whereStmt = [''];
        $argn = 1;
        $argc = 2;
        $where = "";
        if (!empty($cardId)) {
            $where = "creditcard.cardid = :" . $argc++;
            $whereStmt[$argn++] = $cardId;
        }
        $where .= ($where ? " and " : "") . " creditcard.status != 'F' ";
        $where .= " and liabilitytype in ('" . join("', '", $types) . "')";
        $where .= ($isMe && !$entityLoc ? ' and locationkey is null ' : '');
        $where .= " and liabacctkey is not null ";

        if ($chkExpiry) {
            [$month, , $year] = explode("/", GetCurrentDate());
            $where .= " and ((EXP_YEAR = '$year' and EXP_MONTH >= '$month') or (EXP_YEAR > '$year')) ";
        }


        if (isset($entityLoc) && $entityLoc != '') {
            if ($atlas) {
                // for Atlas company, at entity level we should only retrieve the
                // charge cards that are with the same currency as the base currency
                // of the entity and only those charge cards that belong to the entity/sublocation
                $where .= " and exists (select 1 from v_locationent l where l.cny# = :1 and l.location# = creditcard.location# and l.entity# = $entityLoc and l.currency = :" . $argc . ") ";
                $whereStmt[$argn] = $basecurr;
            } else {
                //At Entity level we should select only those charge cards that belong to the entity/sublocation because
                //when you charge pay off at entity level we will have to create the Bill on AP side with the locaiton as that of the charge card
                //which violates the Mega rule
                $where .= " and exists (select 1 from v_locationent l where l.cny# = :1 and (l.location# = creditcard.location# or creditcard.location# is null) and l.entity# = :" . $argc . ") ";
                $whereStmt[$argn] = $entityLoc;
            }
        }

        $whereStmt[0] = $where;
        $cards = GetCreditCards($selects, $whereStmt, 'cardid asc');

        // Filter the cards
        $filteredCards = [];
        foreach ($cards as $card) {
            //Added this to avoid php warnings in reconciliation code where  CURRENCY is referenced
            $card['CURRENCY'] = $card['CURRENCY'] ?? $basecurr;
            if ($isMe && $entityLoc != '' && !$toMatchTxn) {
                if ($card['LOCATIONKEY'] == $entityLoc) {
                    $filteredCards[] = $card;
                }
            } else {
                $filteredCards[] = $card;
            }
        }

        return $filteredCards;
    }

    /**
     * Returns the GL account number for the given GL accountkey.
     *
     * @param string $glAcctKey
     *
     * @return string|null
     */
    public static function getGLAccountNumber($glAcctKey)
    {
        $glAcctNo = null;
        if (!empty($glAcctKey)) {
            $glAcctMgr = Globals::$g->gManagerFactory->getManager('glaccount');
            $_glaccNoArr = $glAcctMgr->GetAcctNoTitle($glAcctKey);
            if (!empty($_glaccNoArr) && isset($_glaccNoArr[0]['ACCT_NO'])) {
                $glAcctNo = $_glaccNoArr[0]['ACCT_NO'];
            }
        }
        return $glAcctNo;
    }

    /**
     * @param string $financialAccount
     *
     * @return string|null
     */
    public static function getBankCurrency($financialAccount)
    {
        $bnkrMgr = Globals::$g->gManagerFactory->getManager('bankaccount');
        $currmap = $bnkrMgr->GetBankCurrency();
        return $currmap[$financialAccount];
    }

    /**
     * Returns bank account details for the bank account id.
     *
     * @param string $accountId
     *
     * @return array|null
     */
    public static function getBankAccountDetail($accountId)
    {
        $accountMap = self::getBankAccountDetails([$accountId]);
        if (!empty($accountMap)) {
            return $accountMap[$accountId];
        }
        return null;
    }

    /**
     * Returns bank account details for the bank account ids.
     *
     * @param array $accountIds
     *
     * @return array
     */
    public static function getBankAccountDetails($accountIds)
    {
        $bnkrMgr = Globals::$g->gManagerFactory->getManager('bankaccount');
        $filters[] = ['BANKACCOUNTID', 'IN', $accountIds];
        $querySpec = [
            'filters' => [$filters],
        ];
        $accounts = $bnkrMgr->GetList($querySpec);
        $accountsMap = [];
        if (!empty($accounts)) {
            foreach ($accounts as $account) {
                $accountsMap[$account['BANKACCOUNTID']] = $account;
            }
        }
        return $accountsMap;
    }

    /**
     * Returns cleared flag description for given id.
     *
     * @param string $cleared
     *
     * @return string
     */
    // public static function getClearFlagDescription($cleared)
    // {
    //     if(!empty($cleared)) {
    //         return self::$clearedFlagDescriptionMap[$cleared];
    //     }
    //     return self::STATE_UNMATCHED;
    // }

    /**
     * Returns the payee names with payee id as key.
     *
     * @return array $chachedEntities
     */
    public static function getPayeeNames()
    {
        $query = "	select entity, name from vendormst where cny# = :1
					union all
					select entity, name from customermst where cny# = :1
					union all
					select e.entity entity, c.printas name from contactmst c, employeemst e
					where e.cny# = :1 and c.cny# = e.cny# and c.record# = e.contactkey ";

        $resultSet = QueryResult(array($query, GetMyCompany()));

        // CREATE AN INDEXED ARRAY OF CACHED ENTITIES
        $payeeNames = array();
        foreach ($resultSet as $row) {
            $payeeNames[$row['ENTITY']] = $row['NAME'];
        }
        return $payeeNames;
    }

    /**
     * F3018 - If a bank transaction has cleared state of M and its recDate is more than
     * the statement ending date then show it as unmatched on the UI.
     * $reconDate
     * @param array $transactions
     * @param string $stmtendingdate
     */
    public static function populateFinancialAccountTransactionDetails(&$transactions,
                                                                      $stmtendingdate)
    {
        foreach ($transactions as &$transaction) {
            if ($stmtendingdate != null
                && $transaction['CLEARED'] == self::STATE_MATCHED
                && $transaction['RECDATE'] != null
                && DateDiff($transaction['RECDATE'], $stmtendingdate) > 0) {
                $transaction['CLEARED'] = self::STATE_UNMATCHED;
                logToFileWarning("Reconcile flow: "
                    . " file=" . __FILE__
                    . " method=" . __FUNCTION__
                    . " recordno=" . $transaction['RECORDNO']
                    . " type=FinancialEntity"
                    . " cleared=" . $transaction['CLEARED']
                    . " recDate" . $transaction['RECDATE']
                    . " stmtendingdate=$stmtendingdate"
                    . " message=Shown on UI as unmatched");
            }
        }
    }

    /**
     * Populated the details on the transactions.
     *
     * @param array $transactions
     * @param string $bankCurrency
     * @param string $debitTxnLabel
     * @param string $creditTxnLabel
     * @param null|string $stmtendingdate
     */
    public static function populateTransactionDetails(&$transactions, $bankCurrency,
                                                      $debitTxnLabel = self::TXNTYPE_CHECKSDEBITS,
                                                      $creditTxnLabel = self::TXNTYPE_DEPOSITSCREDITS,
                                                      $stmtendingdate = null)
    {
        if (!empty($transactions)) {
            $payeeNames = self::getPayeeNames();
            $bankCurrency = $bankCurrency ?? GetBaseCurrency();
            foreach ($transactions as &$transaction) {
                $transaction['PYMTTYPEDESC'] =
                    I18N::getSingleToken(self::$recordTypeToPaymentDescMap[$transaction['RECORDTYPE']]);
                // Set the payee name
                if (!empty($transaction['PAYER'])) {
                    $transaction['PAYEE'] = $transaction['PAYER'];
                } elseif (!empty($transaction['PAYEE'])) {
                    $transaction['PAYEE'] = $payeeNames[$transaction['PAYEE']] ?: $transaction['PAYEE'];
                }
                // Set the bank currency and amount
                $transaction['BANKCURRENCY'] = $bankCurrency;
                $transaction['BANKAMOUNT'] = ($bankCurrency == $transaction['BASECURRENCY']) ? $transaction['AMOUNT'] : $transaction['TRX_AMOUNT'];
                // Set posting date as doc date if its empty
                $transaction['POSTINGDATE'] = $transaction['POSTINGDATE'] ?? $transaction['DOCDATE'];
                $transaction['TRXTYPEDESC'] = I18N::getSingleToken(($transaction['TRANSACTIONTYPE'] == self::TRXTTYPE_DEBIT) ?
                    $debitTxnLabel : $creditTxnLabel);
                // 2021 R2 F3018 - A matched transaction with a recdate more than the Bank statement ending date
                // is considered as UNMATCHED
                if ($stmtendingdate != null
                    && $transaction['CLEARED'] == self::STATE_MATCHED
                    && $transaction['RECDATE'] != null
                    && DateDiff($transaction['RECDATE'], $stmtendingdate) > 0) {
                    $transaction['CLEARED'] = self::STATE_UNMATCHED;
                    logToFileWarning("Reconcile flow: "
                        . " file=" . __FILE__
                        . " method=" . __FUNCTION__
                        . " recordno=" . $transaction['RECORDNO']
                        . " type=Intacct"
                        . " cleared=" . $transaction['CLEARED']
                        . " recDate" . $transaction['RECDATE']
                        . " stmtendingdate=$stmtendingdate"
                        . " message=Shown on UI as unmatched");
                }

                // sourcereconrecord.ent does not define CLEARED as enum so we need use the bankacctreconrecord
                // to find the valid value for CLEARED STATE.
                $mgr = Globals::$g->gManagerFactory->getManager('bankacctreconrecord');
                $transaction['CLEARED_STATE'] = $mgr->transformValidiValueToValidValue('CLEARED', $transaction['CLEARED'] ?? '');
                if (!empty(self::$clearedStateLabelMap[$transaction['CLEARED_STATE']])) {
                    $transaction['CLEARED_STATE'] = I18N::getSingleToken(self::$clearedStateLabelMap[$transaction['CLEARED_STATE']]);
                }
            }
        }
    }

    /**
     * Returns the batch number for the record.
     *
     * @param string $recordNo
     * @param string $recordType
     *
     * @return array
     */
    public static function getBatchNoAndLocationKey($recordNo, $recordType)
    {
        $values = [];
        $query = "SELECT PRBATCHKEY AS BATCHKEY, LOCATIONKEY FROM PRRECORD WHERE CNY# = :1 AND RECORD# = :2";
        if ($recordType == 'rpgl' or $recordType == 'ppgl') {
            $query = "SELECT gle.batch# AS BATCHKEY, gl.locationkey as LOCATIONKEY FROM GLENTRY gle, GLBATCH gl 
                        WHERE gl.cny# = gle.cny#  AND gl.record# = gle.batch#  AND  gle.CNY# = :1 AND gle.RECORD# = :2";
        }
        SetReportViewContext();
        $resultSet = QueryResult(array($query, GetMyCompany(), $recordNo));

        if (!empty($resultSet)) {
            $values[] = $resultSet[0]['BATCHKEY'];
            $values[] = $resultSet[0]['LOCATIONKEY'];
        }
        return $values;
    }

    /**
     * Returns the drill down details.
     *
     * @param string $recordNo
     * @param string $recordType
     * @param bool $forBankTxn true for bank transaction drill down
     *                              false for credit transaction drill down
     *
     * @return string[]
     */
    public static function getDrillDownData($recordNo, $recordType, $forBankTxn)
    {
        $values = self::getBatchNoAndLocationKey($recordNo, $recordType);
        $batch = $values[0];
        $locationkey = $values[1];
        $module = '';
        $entity = '';

        $URL = '';
        switch ($recordType) {
            case 'ri':
                $op = GetOperationId('ar/lists/arinvoice/edit');
                $URL = 'editor.phtml?.do=edit&.op=' . $op . '&.fo=ar&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ar';
                break;
            case 'ra':
                $op = GetOperationId('ar/lists/aradjustment/edit');
                $URL = 'editor.phtml?.do=edit&.op=' . $op . '&.fo=ar&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ar';
                break;
            case 'ro':
            case 'rr':
                $op = GetOperationId('ar/lists/aradvance/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.fo=ar&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ar';
                break;
            case 'rp':
            case 'rl':
                $op = GetOperationId('ar/lists/arpostedpayment/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.fo=ar&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ar';
                break;
            case 'rd':
                $op = GetOperationId('ar/lists/ardiscount/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.fo=ar&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ar';
                break;
            case 'rf':
                $op = GetOperationId('ar/lists/customerrefund/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.fo=ar&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ar';
                break;
            case 'pi':
                $op = GetOperationId('ap/lists/apbill/edit');
                $URL = 'editor.phtml?.do=edit&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ap';
                break;
            case 'pa':
                $op = GetOperationId('ap/lists/apadjustment/edit');
                $URL = 'editor.phtml?.do=edit&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ap';
                break;
            case 'po':
                $op = GetOperationId('ap/lists/appostedadvance');
                $URL = 'lister.phtml?.do=&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.recordkey=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ap';
                break;
            case 'ea':
            case 'ep':
                if ($forBankTxn) {
                    $op = GetOperationId('cm/reports/bankregister');
                } else {
                    $op = GetOperationId('cm/reports/ccregister');
                }
                $URL = 'reportor.phtml?.type=_html&.do=&.op=' . $op . '&_obj__SHOWDETAILS=true&.fo=ap&.r=' . $recordNo . '&.recordtype=' . $recordType
                    . '&.glentrykey=' . $recordNo . '&.prentrykey=' . $recordNo;
                $module = 'ap';
                break;
            case 'er':
                if ($forBankTxn) {
                    $op = GetOperationId('cm/reports/bankregister');
                } else {
                    $op = GetOperationId('cm/reports/ccregister');
                }
                $URL = 'reportor.phtml?.type=_html&.do=&.op=' . $op . '&_obj__SHOWDETAILS=true&.fo=ap&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ap';
                break;
            case 'eo':
                $op = GetOperationId('ee/lists/eepostedadvance');
                $URL = 'lister.phtml?.do=&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.recordkey=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ap';
                break;
            case 'pp':
            case 'pr':
                $op = GetOperationId('ap/lists/appostedpayment/view');
                $URL = 'editor.phtml?.do=&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType . "&.popup=1";
                $module = 'ap';
                break;
            case 'pd':
                $op = GetOperationId('ap/lists/apdiscount/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType;
                $module = 'ap';
                break;
            case 'ei':
                $op = GetOperationId('ee/activities/eebatch/view');
                $URL = 'edit_eirecord.phtml?.do=view&.op=' . $op . '&.fo=ee&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType . '&.entity=' . $entity;
                $module = 'ee';
                break;
            case 'ck':
                $op = GetOperationId('ap/lists/apquickpay/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.r=' . $recordNo . '&.popup=1';
                $module = 'ap';
                break;
            case 'cw':
                $op = GetOperationId('ap/lists/apquickpay/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.r=' . $recordNo . '&.popup=1';
                $module = 'ap';
                break;
            case 'cq':
                $op = GetOperationId('ap/activities/apbatch/edit');
                $URL = 'edit_invoice.phtml?.do=view&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.r=' . $recordNo . '&.recordtype=' . $recordType . "&.popup=1";
                $module = 'ap';
                break;
            case 'cc':
                $op = GetOperationId('cm/lists/creditcardfee/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.r=' . $recordNo . '&.it=creditcardfee&.popup=1';
                $module = 'cm';
                break;
            case 'cr':
                $op = GetOperationId('cm/lists/otherreceipts/edit');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.fo=ap&.batch=' . $batch . '&.r=' . $recordNo . '&.it=otherreceipts&.popup=1';
                $module = 'cm';
                break;
            case 'ci':
                $op = GetOperationId('cm/lists/cctransaction/edit');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.r=' . $recordNo;
                $module = 'ap';
                break;
            case 'cb':
                $op = GetOperationId('cm/lists/cctransaction/edit');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.r=' . $recordNo;
                $module = 'ap';
                break;
            case 'ct':
            case 'cp':
                $op = GetOperationId('cm/lists/fundstransfer/view');
                $URL = 'editor.phtml?.do=view&.popup=1&.op=' . $op . '&.r=' . $recordNo;
                $module = 'cm';
                break;
            case 'cd':
                $op = GetOperationId('cm/lists/deposit/view');
                $URL = 'editor.phtml?.do=view&.op=' . $op . '&.r=' . $recordNo;
                break;
            case 'rpgl':
            case 'ppgl':
                $op = GetOperationId('gl/lists/glbatch/view');
                $URL = 'editor.phtml?.do=view&.popup=1&.r=' . $batch . '&.it=glbatch&.op=' . $op;
                break;
            case 'cx':
            case 'ch':
            case 'cn':
                $op = GetIdForRecType($recordType);
                $URL = 'editor.phtml?.do=view&.popup=1&.op=' . $op . '&.r=' . $recordNo;
                $module = 'cm';
                break;
            case 'rpoi':
            case 'ppoi':
            case 'rpsoi':
            case 'ppsoi':
            case 'rcoi':
            case 'pcoi':
            case 'rcsoi':
            case 'pcsoi':
                $op = GetOperationId('cm/lists/initopenitems/view');
                $URL = 'editor.phtml?.do=view&.popup=1&.op=' . $op . '&.r=' . $recordNo;
                $module = 'cm';
                break;
        }
        $response['MODULE'] = $module;
        $response['URL'] = $URL;
        $response['CNY#'] = GetMyCompany();
        $response['BATCH'] = $batch;
        $ismegl = (IsMultiEntityCompany()) ? 'Y' : 'N';
        $response['ISMEGL'] = $ismegl;
        $response['SESS'] = Session::getKey();
        $response['LOCATIONKEY'] = $locationkey;
        return $response;

    }

    /**
     * Transforms the record object to source record
     *
     * @param array $recordDetail
     * @param string $type
     *
     * @return array
     */
    public static function transformToSourceRecord($recordDetail, $type)
    {
        $sourceRecord = [];
        $rti = $recordDetail['RECORDTYPE'];
        $recordKey = $recordDetail['RECORD#'];
        // Set the respective record number based on record type
        if (false !== strpos($rti, self::RECORDTYPE_GL)) {
            $sourceRecord["GLENTRYKEY"] = $recordKey;
        } else {
            $sourceRecord["PRRECORDKEY"] = $recordKey;
        }
        $sourceRecord['TRANSACTIONTYPE'] = $type;
        $sourceRecord['POSTINGDATE'] = $recordDetail['WHENCREATED'];
        $sourceRecord['RECORDKEY'] = $recordKey;
        $sourceRecord["RECORDTYPE"] = $recordDetail["RECORDTYPE"];
        $sourceRecord["FINANCIALENTITY"] = $recordDetail["FINANCIALENTITY"];
        $sourceRecord["DOCNO"] = $recordDetail["DOCNUMBER"];
        $sourceRecord['DOCDATE'] = $recordDetail['WHENCREATED'];
        $sourceRecord['TRX_AMOUNT'] = $recordDetail['TRX_TOTALENTERED'];
        $sourceRecord['AMOUNT'] = $recordDetail['TOTALENTERED'];
        $sourceRecord['CURRENCY'] = $recordDetail['CURRENCY'];
        $sourceRecord['BASECURRENCY'] = $recordDetail['BASECURR'];
        $sourceRecord['PAYEE'] = $recordDetail['ENTITY'];
        $sourceRecord['CLEARED'] = $recordDetail['CLEARED'];
        $sourceRecord['STATE'] = $recordDetail['STATE'];

        return $sourceRecord;
    }

    /**
     * Translates the reconciliationb mode.
     *
     * @param string $mode
     *
     * @return string
     */
    public static function translateMode($mode)
    {
        if ($mode == self::RECONMODE_AUTOMATCH_VAL) {
            $mode = self::RECONMODE_AUTOMATCH;
        } else if ($mode == self::RECONMODE_AUTOMATCHREVIEW_VAL) {
            $mode = self::RECONMODE_AUTOMATCHREVIEW;
        } else if ($mode == self::RECONMODE_MANUAL_VAL) {
            $mode = self::RECONMODE_MANUAL;
        }
        return $mode;
    }

    /**
     * Translates the reconciliation state.
     *
     * @param string $state
     *
     * @return string
     */
    public static function translateState($state)
    {
        if ($state == self::RECONSTATE_INITIATED_VAL) {
            $state = self::RECONSTATE_INITIATED;
        } else if ($state == self::RECONSTATE_DRAFT_VAL) {
            $state = self::RECONSTATE_DRAFT;
        } else if ($state == self::RECONSTATE_RECONCILED_VAL) {
            $state = self::RECONSTATE_RECONCILED;
        }
        return $state;
    }

    /**
     * Method to check if the transaction type is debit
     *
     * @param string $trxType
     *
     * @return bool
     */
    public static function isDebitTransactionType($trxType)
    {
        if (!isset($trxType) || empty($trxType)) {
            return false;
        }
        if ($trxType === self::TRXTTYPE_DEBIT || $trxType === self::TRXTTYPE_DEBIT_W
            || $trxType === self::TRXTTYPE_DEBIT_CC_W) {
            return true;
        }

        return false;
    }

    /**
     * Method to check if the transaction type is credit
     *
     * @param string $trxType
     *
     * @return bool
     */
    public static function isCreditTransactionType($trxType)
    {
        if (!isset($trxType) || empty($trxType)) {
            return false;
        }
        if ($trxType === self::TRXTTYPE_CREDIT || $trxType === self::TRXTTYPE_CREDIT_D
            || $trxType === self::TRXTTYPE_CREDIT_CC_D) {
            return true;
        }

        return false;
    }

    /**
     * Implementation method to retrive the bank/card account details.
     *
     * @param string $accountId
     * @param string $accountType
     *
     * @return array
     */
    public static function getAccountDetails($accountId, $accountType)
    {
        $entity = "bankaccount";
        if (isl_strcasecmp(self::ACCOUNT_TYPE_CREDIT, $accountType) === 0
            || isl_strcasecmp(self::ACCOUNT_TYPE_CREDIT_CC, $accountType) === 0) {
            $entity = "creditcard";
        }
        $bankMgr = Globals::$g->gManagerFactory->getManager($entity);

        return $bankMgr->get($accountId);
    }

    /**
     * @param array $recordNos
     * @param string $recordType
     * @return bool
     * @throws Exception
     *
     * handle the associated reconciliation entries if there is any
     */
    public static function handleBankTransaction($recordNos, $recordType)
    {
        $sourceReconRecordMgr = Globals::$g->gManagerFactory->getManager('sourcereconrecord');
        return $sourceReconRecordMgr->deleteRecords($recordNos, $recordType, ["Caller" => __CLASS__ . "::" . __FUNCTION__]);
    }

    /**
     * Returns the ID for the key.
     *
     * @param string $table
     * @param string $idField
     * @param string|int $key
     *
     * @return mixed|string
     */
    public static function getIDFromKey($table, $idField, $key)
    {
        $id = null;
        $query[0] = 'SELECT ' . $idField . ' from ' . $table . ' where cny# = :1 and record# = :2';
        $query[1] = GetMyCompany();
        $query[2] = $key;
        $idResult = QueryResult($query);
        if ($idResult) {
            $id = $idResult[0][$idField];
        }
        return $id;
    }

    /**
     * Returns query to include zero doller gl entried based on configuration.
     *
     * @param bool $isTrxAmount
     *
     * @return string
     */
    public static function getZeroGLEntryQuery($isTrxAmount = true)
    {
        $amount = $isTrxAmount ? 'trx_amount' : 'amount';
        $zeroGLQuery = " and glentry." . $amount . " != 0 ";
        $allowZeroGl = GetValueFromProperty(Globals::$g->kCMid, 'ALLOW_ZERO_DOLLER_TXNS_TO_RECONCILE');
        if ($allowZeroGl === 'T') {
            $zeroGLQuery = "";
        }
        return $zeroGLQuery;
    }


    /**
     * Returns  values and labels for bank txn recon state
     *
     * @return array
     */
    public static function getBankTransactionReconciliationStates(): array
    {
        return [
            ReconciliationUtils::STATE_UNMATCHED => ReconciliationUtils::STATE_UNMATCHED_LABEL,
            ReconciliationUtils::STATE_MATCHED => ReconciliationUtils::STATE_MATCHED_LABEL,
            ReconciliationUtils::STATE_DRAFT_MATCHED => ReconciliationUtils::STATE_DRAFT_MATCHED_LABEL,
            ReconciliationUtils::STATE_PARTIALLY_MATCHED => ReconciliationUtils::STATE_PARTIALLY_MATCHED_LABEL,
            ReconciliationUtils::STATE_ALL => ReconciliationUtils::STATE_ALL_LABEL];
    }

    /**
     * Returns  values and labels for Intacct txn recon state
     *
     * @return array
     */
    public static function getIntacctTransactionReconciliationStates(): array
    {
        return [
            ReconciliationUtils::STATE_UNMATCHED => ReconciliationUtils::STATE_UNMATCHED_LABEL,
            ReconciliationUtils::STATE_MATCHED => ReconciliationUtils::STATE_MATCHED_LABEL,
            ReconciliationUtils::STATE_ALL => ReconciliationUtils::STATE_ALL_LABEL];
    }

    /**
     *
     * @return string
     */
    public static function getCallingFunctionName()
    {
        $trace=debug_backtrace();
        $caller=$trace[2];
        if (isset($caller['class']))
            return $caller['class'] ."::". $caller['function'];
        else
            return $caller['function'];
    }

    /**
     * Validate to check if restricted user and validate restricted user entities with bank
     * restricted entities
     *
     * @param string|int $userkey
     * @param string     $bankaccountid
     *
     * @return bool
     */
    public static function checkUserRestrictionWithBank($userkey, $bankaccountid)
    {
        if (IsMultiEntityCompany() && IsRestrictedUser() && !GetContextLocation()) {

            $qryUnrestrictredBank = "SELECT COUNT(*) COUNT FROM molamst WHERE cny# = :1 AND objecttype IN ('CHECKINGACCOUNT', 'SAVINGSACCOUNT') 
                                AND objectid = :2 AND locationkey IS NULL";
            $resUnrestrictedBank = QueryResult([$qryUnrestrictredBank, GetMyCompany(), $bankaccountid]);
            //Restricted user cannot delete records from unrestricted bank
            if ($resUnrestrictedBank[0]['COUNT'] > 0) {
                return false;
            }
            // Restricted user should have same or more location than bank
            if (IsMultiVisibilitySubscribed('checkingaccount') || IsMultiVisibilitySubscribed('savingsaccount')) {
                $qryRestrictedBank = "SELECT COUNT(*) COUNT
                        FROM molamst
                        WHERE cny# = :1 AND objectid = :2
                        AND objecttype IN ('CHECKINGACCOUNT', 'SAVINGSACCOUNT')
                        AND locationkey NOT IN
                          (SELECT location# FROM v_locationent
                          WHERE cny# = :1 AND entity# IN
                            (SELECT locationkey FROM userloc WHERE cny# = :1 AND userkey = :3
                            )
                          )";
                $resRestrictedBank = QueryResult([$qryRestrictedBank, GetMyCompany(), $bankaccountid, $userkey]);
                //Check if bank restricted locations are within the user restricted locations.
                if ($resRestrictedBank[0]['COUNT'] > 0) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @return array
     */
    public static function getFinancialEntityWithTimezone ()
    {
        $query = array();
        $query[0] = "select entity from financialaccount where cny#=:1 and timezoneid is not null";
        $query[1]= GetMyCompany();
        $res= QueryResult($query);
        $entities = [];
        if (is_array($res)) {
            $entities = array_column($res, 'ENTITY');
        }

        return $entities;
    }

    /**
     * @return array
     */
    public static function getBankTransactionsWithImportId (?string $financialEntity = null)
    {
        $query = array();
        $query[0] = "select fr.record# as recordno from finaccttxnrecord fr, finaccttxnfeed ff 
                    where fr.cny#=:1 and fr.cny#= ff.cny# and fr.finaccttxnfeedkey = ff.record# and ff.importid is not null and fr.cleared <> :2";
        $query[1]= GetMyCompany();
        $query[2]= ReconciliationUtils::SOFT_DELETED;

        if ($financialEntity !== null) {
            $query[0] = $query[0] . " and ff.financialentity=:3";
            $query[3] = $financialEntity;
        }

        $res= QueryResult($query);
        $banktransactions = [];
        if (is_array($res)) {
            $banktransactions = array_column($res, 'RECORDNO');
        }

        return $banktransactions;
    }

    /**
     * @param string|null $date
     * @param string|null $accountId
     *
     * @return string
     * @throws Exception
     */
    public static function convertDateForBankTimeZone (?string $date, ?string $accountId, ?string $recordNo = null) {
        $convertedDate = null;

        if ($date !== null && $accountId !== null) {
            $convertedDate = (substr($date, 0, 10));
            $noImportId = true;
            if ($recordNo !== null) {
                $bankTxnsWithImportId = ReconciliationUtils::getBankTransactionsWithImportId($accountId);
                $noImportId = ( isEmptyArray($bankTxnsWithImportId)
                                || ( isNonEmptyArray($bankTxnsWithImportId)
                                     && ! in_array($recordNo, $bankTxnsWithImportId) ) );
            }

            if ($noImportId === true) {
                $tzDate = GMTToUserTZ($date);
                $convertedDate = (substr($tzDate, 0, 10));

                if ( FinancialAccountManager::isBankTimeZoneEnabled() ) {
                    $entityWithTimezone = ReconciliationUtils::getFinancialEntityWithTimezone();
                    if ( is_array($entityWithTimezone) && in_array($accountId, $entityWithTimezone) ) {
                        $convertedDate = (substr($date, 0, 10));
                    }
                }
            }
        }
        return $convertedDate;
    }

    /**
     * Returns AR payment query for reconcialation.
     *
     * @param bool $addTrxEntered
     *
     * @return string
     */
    public static function getARPaymentQuery(bool $addTrxEntered = true): string
    {
        $stateFilter = ARPymtUtils::getPaymentStateFilter('prrecord');
        if (FeatureConfigManagerFactory::getInstance()->isFeatureEnabled('ENABLE_BTA_DRAFT_PAYMENT')) {
            $stateFilter = "(".$stateFilter." or (prrecord.state = 'D' and prrecord.docsource ='BANK'))";
        }

        $recordTypeFilter = ARPymtManager::multiEntityPymtFeatureEnable()
            ? " (prrecord.recordtype IN ('rl', 'rr') or (prrecord.recordtype = 'rp' and prrecord.multientitypymtkey is null)) "
            : " prrecord.recordtype IN ('rp', 'rr') ";

        $amountFilter = $addTrxEntered ? "and prrecord.trx_totalentered <> 0" : '';

        return " ( {$recordTypeFilter} {$amountFilter} and {$stateFilter}) ";
    }

    /**
     * Method to update bank transactions for display.
     *
     * @param array $bankTxns
     * @param bool $convertAmount
     *
     * @return void
     * @throws Exception
     */
    public static function updateBankTransactionsForDisplay(&$bankTxns, $convertAmount = true)
    {
        if (!empty($bankTxns)) {
            $entityWithTimezone = ReconciliationUtils::getFinancialEntityWithTimezone();
            $bankTxnsWithImportId = ReconciliationUtils::getBankTransactionsWithImportId();
            $bankTimezoneEnabled = FinancialAccountManager::isBankTimeZoneEnabled();
            foreach ($bankTxns as $key => $row) {
                if ($bankTimezoneEnabled
                    && is_array($entityWithTimezone)
                    && in_array($row['FINANCIALENTITY'], $entityWithTimezone)) {
                    // no need to take care of bank transactions with import id here becasue it is taken care of in OnlineFileDataHandler::parseDateLineWithImportID
                    $bankTxns[$key]['POSTINGDATE'] = substr($bankTxns[$key]['POSTINGDATE'], 0, 10);
                } else {
                    $tzDate = $bankTxns[$key]['POSTINGDATE'];
                    if (!in_array($row['RECORDNO'], $bankTxnsWithImportId)) {
                        // do not convert the posting date for imported file's transactions
                        $tzDate = GMTToUserTZ($tzDate);
                    } else {
                        logToFileInfo("has import id");
                    }
                    $bankTxns[$key]['POSTINGDATE'] = substr($tzDate, 0, 10);
                }
                if ($convertAmount) {
                    foreach (['AMOUNT', 'AMOUNTTOMATCH'] as $fieldPath) {
                        if (isset($bankTxns[$key][$fieldPath])) {
                            $currency = $bankTxns[$key]['CURRENCY'];
                            $bankTxns[$key][$fieldPath] = Currency($bankTxns[$key][$fieldPath], 0, 0, $currency);
                        }
                    }
                }
            }
        }
    }

    /**
     * Create the reconciliation order string for the queries.
     *
     * @param string        $sortbyList
     *
     * @return string
     */
    public static function createReconcileOrderString($sortbyList)
    {
        $ordertxt = "";
        if ( isSpecified($sortbyList)) {
            $ordarr = explode("#~#", $sortbyList);

            foreach ($ordarr as $ord) {
                $ordertxt .= ($ordertxt ? ', ' : '');
                $ordertxt .= $ord;
            }
        }

        return $ordertxt ?: " whencreated, docnumber ";
    }
}
