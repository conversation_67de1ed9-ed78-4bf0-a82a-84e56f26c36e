<?php
/**
 * Base manager class for the financial account transaction feeds.
 *
 * <AUTHOR>
 * @copyright 2018 Sage Intacct Inc., All Rights Reserved
 */
abstract class FinancialAcctTxnFeedManager extends EntityManager
{

    /**
     * Abstract method to return the entry entity.
     *
     * @return string
     */
    abstract protected function getEntryEntity();

    /**
     * Abstract method to return the entry entity.
     *
     * @return string
     */
    abstract protected function getReconEntryEntity();

    /**
     * Abstract method to refresh bank
     *
     * @param array   $values
     * @param bool    $offline
     * @param bool    $match
     * @param string  $financialEntity
     *
     * @return bool
     */
    abstract public function refreshBankFeed( &$values, $offline = true, $match = true,$financialEntity = null);

    /**
     * Abstract method to refresh bank
     *
     * @param array $values
     *
     * @return array
     */
    //abstract public function refreshBank(&$values);

    /**
     * Abstract method to return the bank feed
     *
     * @param string $financialEntity
     *
     * @return FinAcctTxnFeedDTO[]
     */
    //abstract public function fetchBankFeed($financialEntity);

    /**
     * Add a new bank feed transactions
     *
     * @param array &$values the transaction data
     *
     * @return bool false if error else true
     */
    protected function regularAdd(&$values)
    {
        // Pre process the values
        $ok = $this->preProcessAction($values);
        // Validate
        $ok = $ok && $this->validate($values);
        // Translate
        $ok = $ok && $this->translateData($values);
        // Create for other feeds except online. For online, process only if the processed flag is true.
        if (($values['FEEDTYPE'] != ReconciliationUtils::FEEDTYPE_ONLINE) ||
            ($values['FEEDTYPE'] == ReconciliationUtils::FEEDTYPE_ONLINE && $values['PROCESSED'] == true)) {
            return $ok && parent::regularAdd($values);
        } else {
            // For online bank feeds, call to refresh the bank feeds first
            $ok = $this->refreshBankFeed($values, false, true, $values['FINANCIALENTITY']);
            if ($ok) {
                // Populate the first record no
                if (!empty($values)) {
                    $values['RECORDNO'] = $values[0]['RECORDNO'];
                }
            }
            return $ok;
        }
    }

    /**
     * Process the action value
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function preProcessAction(&$values)
    {
        // Unset all the read only attributes for XML APIs only
        if ($values['FEEDTYPE'] == ReconciliationUtils::FEEDTYPE_XML) {

            unset($values['STATE']);
            unset($values['BANKACCTRECONKEY']);
            unset($values['ERRORID']);
            unset($values['ERRORMSG']);
            unset($values['ENTITYKEY']);
        }
        return true;
    }

    /**
     * Translate a api
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function translateData(&$values)
    {
        $ok = true;

        // Set the state if its not set already
        if (empty($values['STATE'])) {
            $values['STATE'] = ReconciliationUtils::STATE_COMPLETED_VAL;
        }
        // Add the context location
        $values['ENTITYKEY'] = GetContextLocation();
        // Translate data for xml type
        if ($values['FEEDTYPE'] == ReconciliationUtils::FEEDTYPE_XML) {
            $xmlHandler = new XMLFileDataHandler();
            $translatedRecords = [];
            $ok = $xmlHandler->process($values['BANKRECORDS'], $translatedRecords);
            // Set the translated record
            $values['BANKRECORDS'] = $translatedRecords;
        }
        return $ok;
    }

    /**
     * Validates the input.
     *
     * @param array     $values
     *
     * @return bool
     */
    protected function validate($values)
    {
        if(!empty($values)) {
            $feedType =  $values['FEEDTYPE'] ?? null;
            if(!empty($feedType) && is_string($feedType)) {
                // Validate for online feed type
                if (strcmp($feedType, ReconciliationUtils::FEEDTYPE_ONLINE) == 0 &&
                    !empty($values['BANKRECORDS'])) {
                    Globals::$g->gErr->addError('CM-0172', __FILE__ . ':' . __LINE__,
                        'Bank feed records are not expected in online feed, please remove feed records and try again.');

                    return false;
                } // Validate for empty bank feed records
                else if (strcmp($feedType, ReconciliationUtils::FEEDTYPE_XML) === 0 &&
                    empty($values['BANKRECORDS'])) {
                    Globals::$g->gErr->addError('CM-0173', __FILE__ . ':' . __LINE__,
                        'No bank feed records provided, please provide valid feed records and try again.');

                    return false;
                }
                // Validate feed type
                if (!empty($values['BANKRECORDS'])
                    && strcmp($feedType, ReconciliationUtils::FEEDTYPE_XML) != 0) {
                    Globals::$g->gErr->addIAError('CM-0174', __FILE__ . ':' . __LINE__,
                        'Invalid feed type ' . $feedType . ', please provide valid feed type',
                        ['FEEDTYPE' => $feedType]);
                    return false;
                }
            }
            else{
                 Globals::$g->gErr->addIAError('CM-0174', __FILE__ . ':' . __LINE__,
                        'Invalid feed type ' . $feedType . ', please provide valid feed type',
                        ['FEEDTYPE' => $feedType]);
                    return false;
            }
        } else {
            // Check for empty input
            Globals::$g->gErr->addError('CM-0175', __FILE__ . ':' . __LINE__,
                                        'Invalid input data, please provide valid feed data.');
            return false;
        }
        return true;
    }

    /**
     * Returns the bank feeds /files for the given reconciliation.
     *
     * @param string $reconNo
     *
     * @return array[]
     */
    public function getReconciliationBankFeeds($reconNo)
    {
        $querySpec = array(
            'selects' => array('RECORDNO', 'FINANCIALENTITY', 'FEEDDATE', 'FEEDTYPE', 'FILENAME', 'STATE', 'BANKACCTRECONKEY',
                               'ATTRMAPNO', 'ERRORID', 'ERRORMSG', 'WHENCREATED', 'WHENMODIFIED', 'CREATEDBY', 'MODIFIEDBY'),

            'filters' => array(
                array(
                    array('BANKACCTRECONKEY', '=', $reconNo),
                ),
            ),
        );
        return $this->GetList($querySpec);
    }

    /**
     * Processes the feed data uploaded earlier.
     *
     * @param string    $feedRecNo
     * @param string    $reconNo
     *
     * @return bool
     */
    public function processFeed($feedRecNo, $reconNo)
    {
        $ok = true;
        // Process only the files which isn't processed before
        $querySpec = array(
            'filters' => array(
                array(
                    array('RECORDNO', '=', $feedRecNo),
                    array('STATE', '=', ReconciliationUtils::STATE_INPROGRESS_VAL),
                ),
            ),
        );
        $result = $this->GetList($querySpec);

        if(!empty($result)) {
            $feedData = $result[0];
            // If there is feeddata blob, then use that to process.
            // this could be from earlier uploaded files.
            if (!empty($feedData['FEEDDATA'])) {
                $fileData = unserialize($feedData['FEEDDATA']);
            }
            // For new uploads it would be always the clob and uses the below
            else {
                $fileName = "/tmp/tempfile.bankfile";
                file_put_contents($fileName, $feedData['CFEEDDATA']);
                $fileData = file($fileName);
            }

            if(!empty($fileData)) {
                // File type
                $dataHandler = $this->getFeedDataHandler($feedData['FEEDTYPE']);
                $rawData = $dataHandler->convertData($fileData);
                $records = [];
                $ok = $dataHandler->process($rawData, $records);
                // Add the bank records
                $ok = $ok && $this->getEntryManager()->bulkAdd($records, $feedRecNo, $reconNo);
                // Update the feed with recon number as well as the state
                $feedData['BANKACCTRECONKEY'] = $reconNo;
                $feedData['STATE'] = ReconciliationUtils::STATE_COMPLETED;
                $ok = $ok && $this->updateFeedData($feedData);
            }
        }
        return $ok;
    }

    /**
     * Returns the file data handler based on the file type.
     *
     * @param string    $fileType
     *
     * @return FinancialEntityDataHandler|null
     */
    protected function getFeedDataHandler($fileType)
    {
        if($fileType == ReconciliationUtils::FEEDTYPE_XML) {
            return new XMLFileDataHandler();
        }
        else if($fileType == ReconciliationUtils::FEEDTYPE_CSV) {
            return new CSVFileDataHandler();
        }
        else if($fileType == ReconciliationUtils::FEEDTYPE_ONLINE) {
            return new OnlineFileDataHandler();
        }
        else if($fileType == ReconciliationUtils::FEEDTYPE_QIF) {
            return new QIFFileDataHandler();
        }
        return null;
    }

    /**
     * Returns entry entity manager.
     *
     * @return FinancialAcctTxnRecordManager
     */
    public function getEntryManager()
    {
        $entryMgr = Globals::$g->gManagerFactory->getManager($this->getEntryEntity());
        assert($entryMgr instanceof FinancialAcctTxnRecordManager);
        return $entryMgr;
    }

    /**
     * Updates the feed record.
     *
     * @param array $feedData
     *
     * @return bool
     */
    protected function updateFeedData($feedData)
    {
        $updateQry[] = " UPDATE FINACCTTXNFEED SET FEEDDATE = :1, STATE = :2, ACCTRECONKEY = :3, ATTRMAPKEY = :4, ERRORID = :5, 
                        ERRORMSG = :6, WHENMODIFIED = :7, MODIFIEDBY = :8  WHERE CNY#= :9 AND RECORD# = :10 ";
        $updateQry[] = $feedData['FEEDDATE'];
        $updateQry[] = $feedData['STATE'];
        $updateQry[] = $feedData['BANKACCTRECONKEY'];
        $updateQry[] = $feedData['ATTRMAPNO'];
        $updateQry[] = $feedData['ERRORID'];
        $updateQry[] = $feedData['ERRORMSG'];
        $updateQry[] = GetCurrentDate();
        $updateQry[] = GetMyUserid();
        $updateQry[] = GetMyCompany();
        $updateQry[] = $feedData['RECORDNO'];

        return ExecStmt($updateQry);
    }

    /**
     * Delete a record from the database
     *
     * This implementation is usually sufficient for single table objects
     *
     * @param string|int $ID vid of entity
     *
     * @return bool
     */
    public function Delete($ID)
    {
        $mapMgr = Globals::$g->gManagerFactory->getManager('acctfinrecordmap');
        $feed = $this->get($ID);
        $ok = $this->validateBeforeDelete($feed);
        if ($ok) {
            $result = $mapMgr->getFinAcctFeedRecordMap($ID);
            if (!empty($result)) {
                // Get the recon records
                $acctRecords = array_column($result, 'ACCTRECONRECORDKEY');
                // Set CLEARED flag false for mapped recon records before deleting the mapped feed data
                /** @var SourceReconRecordManager $sourceEntryMgr */
                $sourceEntryMgr = Globals::$g->gManagerFactory->getManager('sourcereconrecord');
                $ok = $sourceEntryMgr->updateTransactionsForIntacctTxns(
                    acctRecords: $acctRecords,
                    financialEntity: null,
                    oldFlag: ReconciliationUtils::MATCHED,
                    newFlag: ReconciliationUtils::UNCLEARED,
                    context: ["Caller" => __CLASS__ . "::" . __FUNCTION__]);
            }
            $ok = $ok && parent::Delete($ID);
        }

        return $ok;
    }

    /**
     * Force deletes a record from the database without any validations.
     * This should be called only incase of reopen or direct delete of the feed without no concerns.
     *
     *
     * @param string|int $ID vid of entity
     *
     * @return bool
     */
    public function forceDelete($ID)
    {
        return parent::Delete($ID);
    }

    /**
     * @param string $financialEntity
     *
     * @return bool
     */
    public function DeleteAllButCleared($financialEntity)
    {
        $ok = true;
        $msg = "WPB-Log:" . __FILE__ . " " . __FUNCTION__. " FinancialEntity=$financialEntity";
        $mapMgr = Globals::$g->gManagerFactory->getManager('acctfinrecordmap');
        // Get ACCTFINRECORDMAP records for all uncleared bank transactions for this financialEntity
        $result = $mapMgr->getRecordMapForUnclearBankTxns($financialEntity);
        if ( ! empty($result) ) {
            $acctRecords = array_column($result, 'ACCTRECONRECORDKEY');
            $sourceEntryMgr = Globals::$g->gManagerFactory->getManager('sourcereconrecord');
            // Update MATCHED Cleared flag of Intacct transactions to UNCLEARED
            $ok = $sourceEntryMgr->updateTransactionsForIntacctTxns(
                acctRecords: $acctRecords,
                financialEntity: $financialEntity,
                oldFlag: ReconciliationUtils::MATCHED,
                newFlag: ReconciliationUtils::UNCLEARED,
                context: ["Caller" => __CLASS__ . "::" . __FUNCTION__]);
            if (!$ok) {
                $msg .= "updateTransactions failed for $financialEntity";
            }
        }

        if ( $ok ) {
            // Get all FINACCTTXNFEEDS for this financialEntity
            $feeds = $this->getFinAcctFeeds($financialEntity, ReconciliationUtils::FEEDTYPE_ONLINE);

            $bankTxnRecordsMgr = Globals::$g->gManagerFactory->getManager('bankaccttxnrecord');

            foreach ( $feeds as $feed ) {
                // Get all FINACCTTXNRECORDS for each FINACCTTXNFEED
                $finaccttxnrecords = $bankTxnRecordsMgr->GetFinAcctTxnRecordNos($feed['RECORDNO']);
                // Find all uncleared FINACCTTXNRECORDS
                $unclearedfinaccttxnrecords = array_filter($finaccttxnrecords, function($finaccttxnrecord) {
                    return ( $finaccttxnrecord['CLEARED'] != ReconciliationUtils::STATE_CLEARED );
                });
                // If none of FINACCTTXNRECORDS were not cleared then delete the FINACCTTXNFEED
                // Deleting the parent will delete the FINACCTTXNRECORDS which in turn will delete
                // the corresponding ACCTFINRECORDMAP.
                if ( count($finaccttxnrecords) == count($unclearedfinaccttxnrecords) ) {
                    $msg .= " Deleting parent record of FILENAME=" . $feed['FILENAME']
                            . "Total count=" . count($finaccttxnrecords)
                            . " Uncleared count=" . count($unclearedfinaccttxnrecords)
                            . " FinancialEntity=" . $financialEntity;
                    logToFileWarning($msg);
                    $ok = parent::Delete($feed['RECORDNO']);
                } else {
                    // Do bulk delete of the uncleared FINACCTTXNRECORDS
                    $msg .= " Bulk delete records because none of the record were cleared. Total count="
                            . count($finaccttxnrecords)
                            . " Uncleared count=" . count($unclearedfinaccttxnrecords)
                            . " FinancialEntity=" . $financialEntity;
                    logToFileWarning($msg);
                    $txnRecords = array_column($unclearedfinaccttxnrecords, 'RECORDNO');
                    $ok = $bankTxnRecordsMgr->bulkDelete($txnRecords);
                }

                if ( ! $ok ) {
                    logToFileError(" Failed to delete bank transactions" . $msg);
                    break;
                }
            }
        }

        return $ok;
    }

    /**
     * Returns the bank feeds /files for the given reconciliation.
     *
     * @param string $financialEntity
     * @param string $feedType  Feed type
     * @param bool   $openFeed True gets only unreconciled open feeds, false gets all feeds
     *
     * @return array[]
     */
    function getFinAcctFeeds($financialEntity, $feedType = null, $openFeed = false)
    {
        $querySpec = array(
            'selects' => array('RECORDNO'),

            'filters' => array(
                array(
                    array('FINANCIALENTITY', '=', $financialEntity),
                ),
            ),
        );
        // Add the filter condition if the feed type is present
        if (isset($feedType)) {
            $querySpec['filters'][0][] = array('FEEDTYPE', '=', $feedType);
        }
        // Add condition to get the open feed if its true
        if ($openFeed == true) {
            $querySpec['filters'][0][] = array('BANKACCTRECONKEY', 'ISNULL');
        }
        return $this->GetList($querySpec);
    }

    /**
     * Validates the input.
     *
     * @param array     $values
     *
     * @return bool
     */
    protected function validateBeforeDelete($values)
    {
        if (!empty($values)) {
            // Validate for online feed
            if ($values['FEEDTYPE'] == ReconciliationUtils::FEEDTYPE_ONLINE) {
                Globals::$g->gErr->addError('CM-0176', __FILE__ . ':' . __LINE__,
                    'Online bank feed can not be deleted');
                return false;
            } else {
                // If any of the transactions in the feed already associated with reconciliation that is closed then do not allow to delete
                $querySpec = array(
                    'filters' => array(
                        array(
                            array('FINACCTTXNFEEDKEY', '=', $values['RECORDNO']),
                            array('CLEARED', '=', ReconciliationUtils::STATE_CLEARED)
                        ),
                    ),
                );
                $records = $this->getEntryManager()->GetList($querySpec);
                if (!empty($records)) {
                    Globals::$g->gErr->addError('CM-0177', __FILE__ . ':' . __LINE__,
                        'Can not delete the feed which is reconciled already');
                    return false;
                }
            }
        } else {
            // Check for empty input
            Globals::$g->gErr->addError('CM-0178', __FILE__ . ':' . __LINE__,
                'Invalid feed record.');
            return false;
        }
        return true;
    }

    /**
     * Delinks the reconciliation from the feed data.
     *
     * @param string $feedNo
     * @param string $reconNo
     * @param array  $context
     *
     * @return bool
     */
    public function linkReconciliation($feedNo, $reconNo, $context = [])
    {
        $source = "FinancialAcctTxnFeedManager::linkReconciliation";
        XACT_BEGIN($source);
        // Call the entry class to delink the records
        $ok = $this->getEntryManager()->linkReconciliationRecords($feedNo, $reconNo,
                                                                  ["Caller" => __CLASS__ . "::" . __FUNCTION__]);

        $query[0] = "UPDATE FINACCTTXNFEED SET ACCTRECONKEY = :2 WHERE CNY# = :1 AND RECORD# = :3";

        $query[1] = GetMyCompany();
        $query[2] = $reconNo;
        $query[3] = $feedNo;

        $ok = $ok && ExecStmt($query);

        if ($ok) {
            XACT_COMMIT($source);
            logToFileWarning("Reconcile-Log:"
                             . " Function = " .__CLASS__. "::". __FUNCTION__
                             . " Updated FINACCTTXNFEED"
                             . " RECORD# = $feedNo"
                             . " ACCTRECON = $reconNo"
                             . " Context = " .  preg_replace("/\r|\n/", "", print_r ($context, true))
            );
        } else {
            XACT_ABORT($source);
        }
        return $ok;
    }

    /**
     * Delinks the reconciliation from the feed data.
     *
     * @param string $reconNo
     * @param null   $newsource
     * @param array  $context
     *
     * @return bool
     */
    public function delinkReconciliationForBankFeeds ($reconNo, $newsource = null, $context = [])
    {
        $source = "FinancialAcctTxnFeedManager::delinkReconciliation";
        XACT_BEGIN($source);
        // Call the entry class to delink the records
        $ok = $this->getEntryManager()->delinkReconciliationRecords($reconNo, $newsource, ["Caller" => __CLASS__ . "::" . __FUNCTION__]);

        $query[0] = "UPDATE FINACCTTXNFEED SET ACCTRECONKEY = NULL WHERE CNY# = :1 AND ACCTRECONKEY = :2";

        $query[1] = GetMyCompany();
        $query[2] = $reconNo;
        // If there is new source present then add the feed type filter
        if (isset($newsource)) {
            $query[0] .= " AND FILETYPE != :3 ";
            $query[3] = $newsource;
        }

        $ok = $ok && ExecStmt($query);

        if ($ok) {
            XACT_COMMIT($source);
            logToFileWarning("Reconcile-Log:"
                             . " Function = " .__CLASS__. "::". __FUNCTION__
                             . " Setting ACCTRECONKEY = NULL in FINACCTTXNFEED for"
                             . " ACCTRECONKEY = " . $reconNo
                             . " newsource = $newsource"
                             . " Context = " .  preg_replace("/\r|\n/", "", print_r ($context, true))
            );
        } else {
            XACT_ABORT($source);
        }
        return $ok;
    }
}
