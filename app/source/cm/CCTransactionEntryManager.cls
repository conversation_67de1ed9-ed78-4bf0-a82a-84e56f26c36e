<?php

/**
 * Manager class for the Charge Card Transaction Entry object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for the Charge Card Transaction Entry object
 */
class CCTransactionEntryManager extends SubLedgerTxnEntryManager
{


    /**
     * @var array $offsetDeptMap Array Map to save the Dept# from Line T
     */
    private $offsetDeptMap = array();

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        $this->setSmartLinkForOffsets(false);
    }

    /**
     * @param array $values
     * @param bool $setOnly
     * @param bool $ignoreOverrideFlag
     * @param array $glDimAutofillCache   from EntityManager::buildGlDimValidateCache
     *
     * @return bool
     */
    public function validateAndSetPlatformEnforcedAutoFill(&$values, $setOnly = false,
                                                           $ignoreOverrideFlag = false,
                                                           $glDimAutofillCache = [])

    {
        $ok = parent::validateAndSetPlatformEnforcedAutoFill($values, $setOnly,
                                                             $ignoreOverrideFlag,
                                                             $glDimAutofillCache);
        if ( $ok ) {
            if ( $values['LINEITEM'] == 'T' ) {
                $this->offsetDeptMap[$values['OFFSETACCOUNTKEY']]['DEPT#'] = $values['DEPT#'];
            } else {
                if ( ! isset($values['DEPT#']) && isset($this->offsetDeptMap[$values['ACCOUNTKEY']]['DEPT#']) ) {
                    $values['DEPT#'] = $this->offsetDeptMap[$values['ACCOUNTKEY']]['DEPT#'];
                }
            }
        }

        return $ok;
    }

    /**
     * Method returns the entity name of the tax entry of the parent entity
     *
     * @return string
     */
    protected function getTaxEntryEntityName()
    {
        return 'cctransactiontaxentry';
    }
}
