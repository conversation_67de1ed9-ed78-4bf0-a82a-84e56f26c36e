<floatingPage modal="true" close="false" compact="true" modalsize="medium">
    <title>IA.IMPORT_FILES</title>
    <id>BANKFILEUPLOADPAGE</id>
    <path>BANKFILEUPLOADPAGE</path>
    <pages>
        <page>
            <section>
                <field>
                    <path>TEMPLATE</path>
                    <type type='text' ptype='href'></type>
                    <hreftxt>IA.BANK_RECON_TRANSACTION_TEMPLATE</hreftxt>
                    <events>
                        <click>downLoadCSVFile();</click>
                    </events>
                </field>
            </section>
            <section className="transaction-section">
                <field className="inputfile">
                    <path>BANKTXNFILE</path>
                    <fullname>IA.BANK_TRANSACTION_FILE</fullname>
                    <type>
                        <type>upload</type>
                        <ptype>file</ptype>
                    </type>
                    <events>
                        <click>validateUploadedFile(this.meta);</click>
                    </events>
                </field>
                <grid clazz="FileUploadGrid" noDragDrop="true" noPagination="true">
                    <path>BANKTXNFILES</path>
                    <column>
                        <field viewAllowed="true" readonly="true"  userUIControl="BankFileName">
                            <path>FILENAME</path>
                            <fullname>IA.FILE_NAME</fullname>
                            <type>
                                <type>attachmentname</type>
                                <ptype>attachmentname</ptype>
                            </type>
                        </field>
                    </column>
                    <column className="right" readonly="true" >
                        <field>
                            <path>FEEDTYPE</path>
                        </field>
                    </column>
                    <column className="right" readonly="true" >
                        <field>
                            <fullname>IA.UPLOADED_ON</fullname>
                            <path>FEEDDATE</path>
                            <type assoc="T">
                                <type>date</type>
                                <ptype>date</ptype>
                            </type>
                        </field>
                    </column>
                    <column className="right" readonly="true" >
                        <field>
                            <fullname>IA.UPLOADED_BY</fullname>
                            <path>UPLOADEDBY</path>
                        </field>
                    </column>
                    <column className="right" readonly="true" >
                        <field>
                            <fullname>IA.STATUS</fullname>
                            <path>STATEDESC</path>
                        </field>
                    </column>
                    <column>
                        <field hidden="true">
                            <path>STATE</path>
                        </field>
                    </column>
                    <column>
                        <field hidden="true">
                            <path>RECORDNO</path>
                            <fullname>IA.RECORD_NUMBER</fullname>
                        </field>
                    </column>
                </grid>
            </section>
        </page>
    </pages>
    <footer>
        <button>
            <name>IA.DONE</name>
            <events>
                <click>closeFloatingPage('BANKFILEUPLOADPAGE', false);</click>
            </events>
        </button>
    </footer>
</floatingPage>
