<?
/**
 * class CheckingAccount<PERSON>ister extends BankAccountLister
 */
import('BankAccountLister');
class CheckingAccountLister extends BankAccountLister
{
    function __construct()
    {
        $mcenabled = IsMCPSubscribed();

        /** @noinspection PhpUnusedLocalVariableInspection */

        $fields = [
            'BANKACCOUNTID',
            'BANKACCOUNTNO',
            'BANKNA<PERSON>',
            'CUTOFFDATE',
            'G<PERSON>CCOUNTKEY',
            'INPDATE',
            'INPBAL',
            "'BANKTXNS'",
            'LASTRECONCILEDDATE',
            "'RECONCILE'",
            "'HISTORYLOG'",
            "'PENDINGRECON'",
            'LOCATIONID',
            'WPBSTATUS',
        ];
        if ( $mcenabled ) {
            $fields[] = 'CURRENCY';
        }
        parent::__construct(
            array(
            'entity'        => 'checkingaccount',
            'title'        => "IA.CHECKING_ACCOUNTS",
            'helpfile'    => 'Viewing_and_Managing_Checking_Accounts',
            'fields'    => $fields,
            )
        );
    }

}


