<?php

/**
 * Manager class for the GL batch template
 *
 * <AUTHOR>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 */
class CMRuleGLBatchTmplManager extends EntityManager
{
    /**
     * Translate the record
     *
     * @param array $values  the object values
     *
     * @return bool  true on success and false on failure
     */
    protected function TranslateRecord(&$values)
    {
        $ok = true;
        // Populate the modulekey
        $values['MODULEKEY'] = $values['MODULE'] = Globals::$g->kCMid;

        if ( $values['JOURNAL'] ) {
            [$journalId] = explode('--', $values['JOURNAL']);
            $journalMap = GetJournalTypeMap();

            if ( $journalMap[$journalId]['STATUS'] != 'F' ) {
                if ( isset($journalMap[$journalId]) ) {
                    $values['JOURNALKEY'] = $journalMap[$journalId]['RECORDNO'] ?? $values['JOURNALKEY'];
                }
            }
            // Populate the transaction number
            $recurTransNo = '';
            if ( !$this->GetNextRecurBatchNo($values['JOURNALKEY'], $recurTransNo) ) {
                Globals::$g->gErr->addError(
                    'CM-0129', __FILE__ . ':' . __LINE__, 'Error retrieving next Recurring Transaction Number'
                );
                $ok = false;
            }
            $values['TRANSACTIONNO'] = $recurTransNo;
        }
        $ok = $ok && $this->translateGLEntryTemplate($values['CMRULEGLENTRYTMPL']);

        $ok = $ok && parent::TranslateRecord($values);

        return $ok;
    }

    /**
     * Get the next recurring transaction number
     *
     * @param int $journalRecNo the journal record#
     * @param int $recurTransNo the next transaction number
     *
     * @return bool false if error else true
     */
    private function GetNextRecurBatchNo($journalRecNo, &$recurTransNo)
    {
        $cny = GetMyCompany();
        $property = 'RecurringJETransNo__' . $journalRecNo;
        $calc_start_sql = '
            SELECT NVL (MAX (transactionno), 0) + 1
			FROM recurglbatch
			WHERE journal# = '.$journalRecNo.' AND cny# = '.$cny;

        $desc = array(
            'SELECT GET_NEXTRECORDID (:1, :2, :3, :4) NEXTVAL FROM DUAL',
            $cny,
            $property,
            1,
            $calc_start_sql
        );

        $rec = QueryResult($desc);
        if ( isset($rec[0]) ) {
            $recurTransNo = (int)$rec[0]['NEXTVAL'];
            return true;
        }

        return false;
    }

    /**
     * Override because the entity file points to a view v_recurglbatchschop.
     *
     * @param int $reserveCnt
     *
     * @return int|false
     */
    function GetNextRecordKey($reserveCnt = 1)
    {
        return $this->_MM->GetNextSequence('RECURGLBATCHMST', $reserveCnt);
    }

    /**
     * Translate the gl entry template values.
     *
     * @param array $entries
     *
     * @return bool
     */
    private function translateGLEntryTemplate(&$entries)
    {
        $ok = true;
        if (!empty($entries)) {

            $gManagerFactory = Globals::$g->gManagerFactory;
            $gErr = Globals::$g->gErr;
            $locMgr = $gManagerFactory->getManager('location');
            $deptMgr = $gManagerFactory->getManager('department');
            $acctMgr = $gManagerFactory->getManager('glaccount');
            $allocMgr = $gManagerFactory->getManager('allocation');
            $exTypeMgr = $gManagerFactory->getManager('exchangeratetypes');

            $accountMap = [];
            $locationMap = [];
            $departmentMap = [];
            $exchTypeMap = [];

            foreach ($entries as &$entry) {
                if ( empty($entry['ACCOUNT#']) && isset($entry['ACCOUNTNO']) && $entry['ACCOUNTNO']!='' ) {
                    [$acctId] = explode('--', $entry['ACCOUNTNO']);

                    if ( !$accountMap[$acctId] ) {
                        $params = array(
                            'selects' => array('RECORDNO', 'SUBLEDGERCONTROLON'),
                            'filters' => array(array(array('ACCOUNTNO', '=', $acctId))),
                        );
                        $recs = $acctMgr->GetList($params);

                        if (!isset($recs[0]['RECORDNO'])) {
                            $msg = "Invalid Account " . $acctId . " selected";
                            $corr = "Pick a valid account.";
                            $gErr->addIAError('CM-0130', __FILE__ . ':' . __LINE__, $msg, ['ACCOUNTID' => $acctId], '', [], $corr);
                            $ok = false;
                        }

                        // TODO Disallow direct transactions from GL
                        if (!$entry['SKIPCONTROLLEDACCOUNTSFORREVAL'] && isset($recs[0]['SUBLEDGERCONTROLON'])
                            && $recs[0]['SUBLEDGERCONTROLON'] == "true"
                        ) {
                            $msg = "You cannot have direct transaction from GL for the controlled account " . $acctId;
                            $gErr->addIAError('CM-0131', __FILE__ . ':' . __LINE__, $msg, ['ACCOUNTID' => $acctId]);
                            $ok = false;
                        }
                        $accountMap[$acctId] = $recs[0]['RECORDNO'];
                    }
                    $entry['ACCOUNT#'] = $accountMap[$acctId];
                    $entry['ACCOUNTNO'] = $acctId;
                }

                // dimension translation
                if ( empty($entry['LOCATION#']) && isset($entry['LOCATION']) && $entry['LOCATION']!='' ) {
                    [$locId] = explode('--', $entry['LOCATION']);

                    if ( !$locationMap[$locId] ) {
                        $params = array(
                            'selects' => array('RECORD#'),
                            'filters' => array(array(array('LOCATIONID', '=', $locId))),
                        );
                        $params['filters'][0][] = array('STATUS', '=', 'active');

                        $recs = $locMgr->GetList($params);

                        if ( !isset($recs[0]['RECORD#']) ) {
                            $locationLabel = I18N::getSingleToken('IA.LOCATION');
                            $msg = "Invalid " . $locationLabel . " " . $locId . " selected";
                            $corr = "Pick a valid active " . $locationLabel;
                            $gErr->addIAError('CM-0132', __FILE__ . ':' . __LINE__, $msg, ['LOCATION' => $locationLabel, 'LOCATIONID' => $locId],
                                '', [], $corr, ['LOCATION' => $locationLabel]);
                            $ok = false;
                        }
                        $locationMap[$locId] = $recs[0]['RECORD#'];
                    }

                    $entry['LOCATION#'] = $locationMap[$locId];
                    $entry['LOCATION'] = $locId;
                }

                if ( empty($entry['DEPT#']) && isset($entry['DEPARTMENT']) && $entry['DEPARTMENT']!='' ) {
                    [$deptId] = explode('--', $entry['DEPARTMENT']);

                    if ( !$departmentMap[$deptId] ) {
                        $params = array(
                            'selects' => array('RECORD#'),
                            'filters' => array(array(array('DEPARTMENTID', '=', $deptId))),
                        );
                        $params['filters'][0][] = array('STATUS', '=', 'active');

                        $recs = $deptMgr->GetList($params);

                        if ( !isset($recs[0]['RECORD#']) ) {
                            $DepartmentLabel = I18N::getSingleToken('IA.DEPARTMENT');
                            $msg = "Invalid ".$DepartmentLabel." " . $deptId . " selected";
                            $corr = "Pick a valid active ".$DepartmentLabel;
                            $gErr->addIAError('CM-0133', __FILE__ . ':' . __LINE__, $msg, ['DEPARTMENT' => $DepartmentLabel, 'DEPARTMENTID' => $deptId],
                                '', [], $corr, ['DEPARTMENT' => $DepartmentLabel]);
                            $ok = false;
                        }
                        $departmentMap[$deptId] = $recs[0]['RECORD#'];
                    }

                    $entry['DEPT#'] = $departmentMap[$deptId];
                    $entry['DEPARTMENT'] = $deptId;
                }
            }

            if ( empty($entry['ALLOCATIONKEY']) && isset($entry['ALLOCATION']) && $entry['ALLOCATION']!='' ) {
                // if allocation is set get the record# of the allocation
                $params = array(
                    'selects' => array('RECORDNO', 'TYPE', 'STATUS'),
                    'filters' => array(
                        array(array('ALLOCATIONID', '=', $entry['ALLOCATION']))
                    ),
                );
                $params['filters'][0][] = array('STATUS', '=', 'active');

                $params['donottransform'] = true;
                $allocRec = $allocMgr->GetList($params);

                if ( !isset($allocRec[0]['RECORDNO']) ) {
                    $allocationLabel = I18N::getSingleToken('IA.ALLOCATION');
                    $msg = "Invalid " . $allocationLabel . " " . $entry['ALLOCATION'] . " selected";
                    $corr = "Pick a valid active " . $allocationLabel;
                    $gErr->addIAError('CM-0134', __FILE__ . ':' . __LINE__, $msg, ['ALLOCATION' => $allocationLabel, 'ALLOCATIONID' => $entry['ALLOCATION']],
                        '', [], $corr, ['ALLOCATION' => $allocationLabel]);
                    $ok = false;
                }
                else {
                    $entry['ALLOCATIONKEY'] = $allocRec[0]['RECORDNO'];

                    //validate dimensions in allocation are not set up the lines of this template
                    $lineManager = Globals::$g->gManagerFactory->getManager("allocationentry");
                    $allocLines = $lineManager->GetByParent($allocRec[0]['RECORDNO']);
                    $glEntryAllocDimConficts = $this->getGLEntryDimAllocDimConflicts($allocLines, $entry);
                    if (count($glEntryAllocDimConficts) > 0) {
                        $dimConflicts = implode(", ", $glEntryAllocDimConficts);
                        $gErr->addIAError(
                            'CM-0370', __FILE__ . '.' . __LINE__,
                            "Dimensions '. $dimConflicts . ' on the line in JE template, are also setup in the Allocation." .$dimConflicts,
                            ['DIM_CONFLICTS' => $dimConflicts],
                            '', [],
                            'Please Provide the value(s) only for Allocation or Dimension Fields',  ['DIM_CONFLICTS' => $dimConflicts]
                        );
                        $ok = false;
                    }

                }
            }

            // for exchange rate type
            if ( isset($entry['EXCH_RATE_TYPE_ID']) && $entry['EXCH_RATE_TYPE_ID']!='' ) {

                $exchId = $entry['EXCH_RATE_TYPE_ID'];

                if (!$exchTypeMap[$exchId]) {
                    $exchTypeMap[$exchId] = $exTypeMgr->GetExchangeRateTypeID($exchId);
                }
                $entry['EXCH_RATE_TYPE_ID'] = $exchTypeMap[$exchId];
            }

            // Default the location
            if (GetContextLocation() && empty($entry['LOCATION#'])) {
                $this->setDefaultLocation($entry, 'LOCATION#', false);
            }
        }
        return $ok;
    }

    /**
     * Validate record
     *
     * @param array &$values Array of values
     *
     * @return bool true if valid
     */
    protected function ValidateRecord(&$values)
    {
        $ok = parent::ValidateRecord($values);
        if ($ok) {
            // Validate for split units
            if ($values['SPLIT'] == BankTxnRuleUtil::SPLIT_PERCENTAGE) {
                $lines = [];
            // Filter any emplty lines
            foreach ($values['CMRULEGLENTRYTMPL'] as $entry) {
                if (!empty($entry)) {
                    $lines[] = $entry;
                }
            }

                // For phase 1, we allow only one line item
                if (count($lines) > 1) {
                    Globals::$g->gErr->addError("CM-0135", __FILE__ . '-' . __LINE__,
                        "Only one journal entry line item supported .");
                    $ok = false;
                }
            }
            if ($ok) {
                $hasAllocation = false;
                // Check for allocation in entries
                foreach ($values['CMRULEGLENTRYTMPL'] as $entry) {
                    if (!empty($entry['ALLOCATION'])) {
                        $hasAllocation = true;
                        break;
                    }
                }
                if ($hasAllocation) {
                    // Check if GL config has allocation in JE
                    $glAllocationEnabled = (GetPreferenceForProperty(Globals::$g->kGLid, 'USEALLOCATION') == 'T');
                    if (!$glAllocationEnabled) {
                        Globals::$g->gErr->addError("CM-0369", __FILE__ . '-' . __LINE__,
                            "Enable allocation in GL to use allocation in journal entry templates.");
                        $ok = false;
                    }
                    // Validate for draft state and allocation
                    if ($values['STATE'] == 'Draft') {
                        Globals::$g->gErr->addError("CM-0136", __FILE__ . '-' . __LINE__,
                            "Only posted state is allowed for transaction involving allocation.");
                        $ok = false;
                    }
                    // Validate for tax implications and allocation
                    if (TaxSetupManager::isVATEnabled() && !empty($values['TAXIMPLICATIONS'])) {
                        if (in_array($values['TAXIMPLICATIONS'], [TaxValidationHelper::VAT_INBOUND, TaxValidationHelper::VAT_OUTBOUND])) {
                            $msg = "Allocations are not supported for journal entries that contain tax implications.";
                            $corr = "Remove the allocation and try again.";
                            Globals::$g->gErr->addError('CM-0137', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                            $ok = false;
                        }
                    }
                }
            }
        }
        $ok = $ok && $this->validateTaxAttributes($values);
        return $ok;
    }

    /**
     * @param string $ID
     * @param null|array   $fields
     *
     * @return array|false
     */
    function get($ID, $fields=null)
    {
        [$tmplId] = explode('--', $ID);
        $obj = parent::get($tmplId, $fields);

        if (!isset($obj) || empty($obj)) {
            return $obj;
        }

        $exTypeMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
        $exchTypeMap = array();
        foreach ( $obj['CMRULEGLENTRYTMPL'] ?? [] as $key => $entry ) {
            if ( $entry['EXCH_RATE_TYPE_ID'] ) {
                $exchId = $entry['EXCH_RATE_TYPE_ID'];
                if ( !$exchTypeMap[$exchId] ) {
                    $exchTypeMap[$exchId] = $exTypeMgr->GetExchangeRateTypeName($exchId);
                }
                $obj['CMRULEGLENTRYTMPL'][$key]['EXCH_RATE_TYPE_ID'] = $exchTypeMap[$exchId];
            }
        }
        return $obj;
    }

    /**
     * @return bool
     */
    protected function API_GetNeedsToConvertRecNos()
    {
        return true;
    }


    /**
     * Validates tax fields in create/edit request of GL template for bank txn rules
     *
     * @param array &$values
     *
     * @return bool false if error else true
     */
    protected function validateTaxAttributes(array &$values)
    {
        $ok = true;
        // return if not VAT enabled
        if (!TaxSetupManager::isVATEnabled()) {
            return $ok;
        }
        //validate tax solutionid/key
        $ok = $ok && TaxValidationHelper::validateAndTranslateTaxSoultion($values);
        //validate tax schedule id/key
        $ok = $ok && TaxValidationHelper::validateAndTranslateTaxSchedule($values);
        //validate VAT vendor
        $ok = $ok && TaxValidationHelper::validateAndTranslateVatVendor($values);
        //validate VAT customer
        $ok = $ok && TaxValidationHelper::validateAndTranslateVatCustomer($values);

        return $ok;
    }

    /**
     * @return array
     */
    public function getDBTranslatedFieldsForEditor(): array
    {
        // Translate EXCH_RATE_TYPE_ID field explicitely in formeditor
        // EXCHRATETYPE exists in ownedobject cmruleglentrytmpl
        return ['EXCHRATETYPE'];
    }

    /**
     * @param string[][] $allocEntryRows
     * @param string[] $line
     *
     * @return string[]
     */
    private function getGLEntryDimAllocDimConflicts($allocEntryRows, $line)
    {
        $allocMgr =  Globals::$g->gManagerFactory->getManager('allocation');
        $allDimension = $allocMgr->getDimensionIDs();
        $glEntryAllocDimConflict = [];
        foreach ($allocEntryRows as $allocEntryRow) {
            foreach ($allDimension as $dimension) {
                $allocDim = $allocEntryRow[$dimension];
                $lineDim = $this->getLineDimVal($line, $dimension, $transDim);
                if ($allocDim != "") {
                    if ($lineDim != "") {
                        // if glentry and allocentry both have same dimension, set  conflict flag
                        if (!isset($glEntryAllocDimConflict[$transDim])) {
                            $glEntryAllocDimConflict[$transDim] = true;
                        }
                    }
                }
            }
        }

        return array_keys($glEntryAllocDimConflict);
    }

    /**
     * @param array $line
     * @param string $dimension
     * @param string $transDim
     *
     * @return string
     */
    private function getLineDimVal($line, $dimension, &$transDim)
    {
        $transDim = $dimension;
        $ret = $line[$transDim];
        if (isNullOrBlank($ret)) {
            switch ($dimension) {
                case "LOCATIONID":
                    if (!isNullOrBlank($line['LOCATION'])) {
                        $transDim = 'LOCATION';
                        $ret = $line[$transDim];
                    }
                    break;
                case "DEPARTMENTID":
                    if (!isNullOrBlank($line['DEPARTMENT'])) {
                        $transDim = 'DEPARTMENT';
                        $ret = $line[$transDim];
                    }
                    break;
            }
        }
        return $ret;
    }
}
