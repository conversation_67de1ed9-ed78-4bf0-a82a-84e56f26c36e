<floatingPage modal="true" close="false" fullscreen="true" topbuttons="true" compact="true">
    <title>IA.MATCH_TRANSACTIONS</title>
    <id>FINDMATCHTXNSPAGE</id>
    <path>FINDMATCHTXNSPAGE</path>
    <pages>
        <page>
            <className>qx-compact</className>
            <flexstack>
                <flexsection type="entity-summary-section" justify_content="space-evenly" align_items="center" order="1">
                    <flexchild halign_content="center" order="1">
                        <field fullname="IA.CHECK_DEBIT_KEY" readonly="true">
                            <path>MDOCNO</path>
                        </field>
                    </flexchild>
                    <vr height="40px" mb="8px" order="2"/>
                    <flexchild halign_content="center" order="3">
                        <field fullname="IA.DOCUMENT_DATE" readonly="true">
                            <path>MDOCDATE</path>
                            <type assoc="T">
                                <type>date</type>
                                <ptype>date</ptype>
                            </type>
                        </field>
                    </flexchild>
                    <vr height="40px" mb="8px" order="4"/>
                    <flexchild customFields="no" halign_content="center" order="5">
                        <field fullname="IA.TXN_TYPE" readonly="true">
                            <path>MTRXTYPEDESC</path>
                        </field>
                    </flexchild>
                    <vr height="40px" mb="8px" order="6"/>
                    <flexchild halign_content="center" order="7">
                        <field fullname="IA.VENDOR_PAYEE" readonly="true">
                            <path>MPAYEE</path>
                        </field>
                    </flexchild>
                    <vr height="40px" mb="8px" order="8"/>
                    <flexchild customFields="no" halign_content="center" order="9">
                        <field fullname="IA.MATCH_TYPE" readonly="true">
                            <path>MATCHMODE</path>
                            <type assoc="T">
                                <type>text</type>
                                <ptype>enum</ptype>
                            </type>
                        </field>
                    </flexchild>
                    <vr height="40px" mb="8px" order="10"/>
                    <flexchild halign_content="center" order="11">
                        <field fullname="IA.AMOUNT" readonly="true">
                            <path>MAMOUNT</path>
                            <type assoc="T">
                                <type>decimal</type>
                                <ptype>currency</ptype>
                                <align>center</align>
                            </type>
                        </field>
                    </flexchild>
                    <vr height="40px" mb="8px" order="12"/>
                    <flexchild halign_content="center" order="13">
                        <field readonly="true" fullname="IA.REMAINING_TO_MATCH">
                            <path>AMOUNTTOMATCH_H</path>
                            <type assoc="T">
                                <ptype>currency</ptype>
                                <type>decimal</type>
                                <maxlength>14</maxlength>
                            </type>
                            <default>0</default>
                        </field>
                    </flexchild>
                </flexsection>
                <flexsection type="entity-summary-section" mt="10px" order="2">
                    <section id="generalMatchInfo" columnCount="4">
                        <title>IA.GENERAL_INFO</title>
                        <field fullname="IA.DESCRIPTION" readonly="true">
                            <path>MDESCRIPTION</path>
                        </field>
                        <field fullname="IA.MATCHED_BY" readonly="true">
                            <path>MATCHEDBY</path>
                        </field>
                        <field fullname="IA.RULE_TYPE" readonly="true">
                            <path>BANKTXNRULETYPE</path>
                            <type assoc="T">
                                <type>text</type>
                                <ptype>enum</ptype>
                            </type>
                        </field>
                        <field fullname="IA.MATCHED_DATE" readonly="true">
                            <path>MATCHEDDATE</path>
                            <type assoc="T">
                                <type>timestamp</type>
                                <ptype>timestamp</ptype>
                            </type>
                        </field>
                        <field fullname="IA.RULE_ID" readonly="true">
                            <path>BANKTXNRULEID</path>
                            <type type="text" ptype="href"></type>
                            <events>
                                <click>openRule(this.meta); false;</click>
                            </events>
                        </field>
                        <field hidden="true">
                            <path>RULEKEY</path>
                        </field>
                        <field fullname="IA.RULE_UPDATED_AFTER_MATCH" readonly="true">
                            <path>RULEUPDATED</path>
                            <type assoc="T">
                                <type>text</type>
                                <ptype>text</ptype>
                            </type>
                        </field>
                        <field fullname="IA.RULE_NAME" readonly="true">
                            <path>BANKTXNRULENAME</path>
                            <type type="text" ptype="href"></type>
                            <events>
                                <click>openRule(this.meta); false;</click>
                            </events>
                        </field>
                        <field fullname="IA.DISPLAYLETTRAGECODE" readonly="true">
                            <path>DISPLAY_LETTRAGE_CODE</path>
                        </field>
                        <field fullname="IA.RECORDNO" hidden="true">
                            <path>MRECORDNO</path>
                        </field>
                        <field fullname="IA.TRANSACTION_TYPE" hidden="true">
                            <path>MTXNTYPE</path>
                        </field>
                        <field fullname="IA.GRID_ROW_NUMBER" hidden="true">
                            <path>MROWNUMBER</path>
                        </field>
                    </section>
                </flexsection>
                <flexchild order="3">
                    <grid noDragDrop="true" className="AcctReconGrid" hasFixedNumOfRows="true" serverPagination="true"  enableSelect="true"
                           noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no" hidden="true" maxRows="500">
                        <path>MATCHBANKTXNS</path>
                        <title>IA.SELECT_BANK_TRANSACTIONS_TO_MATCH</title>
                        <selectColumn></selectColumn>
                        <column>
                            <field fullname='IA.RECORDNO' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="POSTINGDATE_M">
                                    <path>SEARCH_POSTINGDATE_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.DATE" readonly="true" sortable="true">
                                <path>POSTINGDATE_M</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="DOCNO_M">
                                    <path>SEARCH_DOCNO_M</path>
                                </field>
                            </gridHeading>
                            <field fullname='IA.CHECK_DOC_NO' readonly="true" sortable="true">
                                <path>DOCNO_M</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="BAMOUNT">
                                    <path>SEARCH_BANKAMOUNT_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.BANK_AMOUNT" readonly="true" sortable="true">
                                <path>BAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="PAYEE_M">
                                    <path>SEARCH_PAYEE_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.PAYEE" readonly="true" sortable="true">
                                <path>PAYEE_M</path>
                            </field>
                        </column>
                        <column columnWidth="200px" hasFieldTooltip="true">
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="DESCRIPTION_M">
                                    <path>SEARCH_DESCRIPTION_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.DESCRIPTION" readonly="true" sortable="true">
                                <path>DESCRIPTION_M</path>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.TXN_TYPE' readonly="true" sortable="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                    </grid>
                    <grid noDragDrop="true" hasFixedNumOfRows="true" serverPagination="true" enableSelect="true"
                         drawBatchSize="1000" className="AcctReconGrid" customFields="no" hidden="true" maxRows="500">
                        <path>MATCHINTACCTTXNS</path>
                        <title>IA.SELECT_INTACCT_TRANSACTION_TO_MATCH</title>
                        <selectColumn></selectColumn>
                        <column>
                            <field fullname='IA.RECORD_NO' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="POSTINGDATE_M">
                                    <path>SEARCH_POSTINGDATE_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.GL_POST_DATE" readonly="true" sortable="true">
                                <path>POSTINGDATE_M</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="DOCNO_M">
                                    <path>SEARCH_DOCNO_M</path>
                                </field>
                            </gridHeading>
                            <field fullname='IA.CHECK_DOC_NO' readonly="true" sortable="true">
                                <path>DOCNO_M</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="BANKAMOUNT_M">
                                    <path>SEARCH_BANKAMOUNT_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.BANK_AMOUNT" readonly="true" sortable="true">
                                <path>BANKAMOUNT_M</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="PAYEE_M">
                                    <path>SEARCH_PAYEE_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.PAYEE" readonly="true" sortable="true">
                                <path>PAYEE_M</path>
                            </field>
                        </column>
                        <column columnWidth="200px" hasFieldTooltip="true">
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="DESCRIPTION_M">
                                    <path>SEARCH_DESCRIPTION_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.DESCRIPTION" readonly="true" sortable="true">
                                <path>DESCRIPTION_M</path>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.TXN_TYPE' readonly="true" sortable="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.PYMT_TYPE' readonly="true" sortable="true">
                                <path>PYMTTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="DOCDATE_M">
                                    <path>SEARCH_DOCDATE_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.TXN_DATE" readonly="true" sortable="true">
                                <path>DOCDATE_M</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="CURRENCY_M">
                                    <path>SEARCH_CURRENCY_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.TXN_CURR" readonly="true" sortable="true">
                                <path>CURRENCY_M</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="TRX_AMOUNT_M">
                                    <path>SEARCH_TRX_AMOUNT_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.TXN_AMOUNT" readonly="true" sortable="true">
                                <path>TRX_AMOUNT_M</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center">
                                <field noLabel="true" searchPath="AMOUNT_M">
                                    <path>SEARCH_AMOUNT_M</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.BASE_AMOUNT" readonly="true" sortable="true" clazz="LinkControlDecimal" userUIControl="BaseAmountControl">
                                <path>AMOUNT_M</path>
                                <type type='decimal' ptype='href'></type>
                                <events>
                                    <click>openDrilldownPage(this.meta);</click>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BANK_CURR" hidden="true" sortable="true">
                                <path>BANKCURRENCY_M</path>
                            </field>
                        </column>
                    </grid>
                </flexchild>
                <flexchild order="4">
                    <grid noDragDrop="true" className="AcctReconGrid" hasFixedNumOfRows="true"
                          noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no" hidden="true" maxRows="500">
                        <path>ALLMATCHEDBANKTXNS</path>
                        <title>IA.ALL_TRANSACTIONS_IN_THIS_GROUP</title>
                        <column>
                            <field fullname='IA.RECORD_NO' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.DATE" readonly="true">
                                <path>POSTINGDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.CHECK_DOC_NO' readonly="true">
                                <path>DOCNO</path>
                            </field>
                        </column>
                        <column columnWidth="200px" hasFieldTooltip="true">
                            <field fullname="IA.DESCRIPTION" readonly="true">
                                <path>DESCRIPTION</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BANK_AMOUNT" readonly="true">
                                <path>BAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.AMOUNT_TO_MATCH" readonly="true">
                                <path>AMOUNTTOMATCH</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.PAYEE" readonly="true">
                                <path>PAYEE</path>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.TXN_TYPE' readonly="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                    </grid>
                    <grid noDragDrop="true" className="AcctReconGrid" hasFixedNumOfRows="true"
                          noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no" hidden="true" maxRows="500">
                        <path>ALLMATCHEDINTACCTTXNS</path>
                        <title>IA.ALL_TRANSACTIONS_IN_THIS_GROUP</title>
                        <column>
                            <field fullname='IA.RECORDNO' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.GL_POST_DATE" readonly="true">
                                <path>POSTINGDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.CHECK_DOC_NO' readonly="true">
                                <path>DOCNO</path>
                            </field>
                        </column>
                        <column columnWidth="200px" hasFieldTooltip="true">
                            <field fullname="IA.DESCRIPTION" readonly="true">
                                <path>DESCRIPTION</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BANK_AMOUNT" readonly="true">
                                <path>BANKAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.PAYEE" readonly="true">
                                <path>PAYEE</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.TXN_DATE" readonly="true">
                                <path>DOCDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.TXN_TYPE' readonly="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.PYMT_TYPE' readonly="true">
                                <path>PYMTTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.TXN_CURR" readonly="true">
                                <path>CURRENCY</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.TXN_AMOUNT" readonly="true">
                                <path>TRX_AMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BASE_AMOUNT" readonly="true" clazz="LinkControlDecimal" userUIControl="BaseAmountControl">
                                <path>AMOUNT</path>
                                <type type='decimal' ptype='href'></type>
                                <events>
                                    <click>openDrilldownPage(this.meta);</click>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BANK_CURR" hidden="true">
                                <path>BANKCURRENCY</path>
                            </field>
                        </column>
                    </grid>
                </flexchild>
                <flexchild order="5">
                    <grid noDragDrop="true" className="AcctReconGrid" hasFixedNumOfRows="true"
                          noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no" hidden="true" maxRows="500">
                        <path>MATCHEDBANKTXNS</path>
                        <title>IA.MATCHED_TO_TRANSACTIONS</title>
                        <!--<column>
                            <field noLabel="true">
                                <path>MATCHEDTXN</path>
                                <type assoc="T">
                                    <ptype>boolean</ptype>
                                    <type>boolean</type>
                                    <default>false</default>
                                </type>
                            </field>
                        </column>-->
                        <column>
                            <field fullname='IA.RECORDNO' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.DATE" readonly="true">
                                <path>POSTINGDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.CHECK_DOC_NO' readonly="true">
                                <path>DOCNO</path>
                            </field>
                        </column>
                        <column columnWidth="200px" hasFieldTooltip="true">
                            <field fullname="IA.DESCRIPTION" readonly="true">
                                <path>DESCRIPTION</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BANK_AMOUNT" readonly="true">
                                <path>BAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.AMOUNT_TO_MATCH" readonly="true">
                                <path>AMOUNTTOMATCH</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.PAYEE" readonly="true">
                                <path>PAYEE</path>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.TXN_TYPE' readonly="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                    </grid>
                    <grid noDragDrop="true" className="AcctReconGrid" hasFixedNumOfRows="true"
                          noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no" hidden="true" maxRows="500">
                        <path>MATCHEDINTACCTTXNS</path>
                        <title>IA.MATCHED_TO_TRANSACTIONS</title>
                        <!--<column>
                            <field noLabel="true">
                                <path>MATCHEDTXN</path>
                                <type assoc="T">
                                    <ptype>boolean</ptype>
                                    <type>boolean</type>
                                    <default>false</default>
                                </type>
                            </field>
                        </column>-->
                        <column>
                            <field fullname='IA.RECORDNO' hidden="true">
                                <path>RECORDNO</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.GL_POST_DATE" readonly="true">
                                <path>POSTINGDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.CHECK_DOC_NO' readonly="true">
                                <path>DOCNO</path>
                            </field>
                        </column>
                        <column columnWidth="200px" hasFieldTooltip="true">
                            <field fullname="IA.DESCRIPTION" readonly="true">
                                <path>DESCRIPTION</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.STATE" readonly="true">
                                <path>CLEARED_STATE</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BANK_AMOUNT" readonly="true">
                                <path>BANKAMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.PAYEE" readonly="true">
                                <path>PAYEE</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.TXN_DATE" readonly="true">
                                <path>DOCDATE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.TXN_TYPE' readonly="true">
                                <path>TRXTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <field fullname='IA.PYMT_TYPE' readonly="true">
                                <path>PYMTTYPEDESC</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.TXN_CURR" readonly="true">
                                <path>CURRENCY</path>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.TXN_AMOUNT" readonly="true">
                                <path>TRX_AMOUNT</path>
                                <type assoc="T">
                                    <ptype>currency</ptype>
                                    <type>decimal</type>
                                    <maxlength>14</maxlength>
                                </type>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BASE_AMOUNT" readonly="true" clazz="LinkControlDecimal" userUIControl="BaseAmountControl">
                                <path>AMOUNT</path>
                                <type type='decimal' ptype='href'></type>
                                <events>
                                    <click>openDrilldownPage(this.meta);</click>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BANK_CURR" hidden="true">
                                <path>BANKCURRENCY</path>
                            </field>
                        </column>
                    </grid>
                </flexchild>
                <flexsection order="6">
                    <flexrow justify_content="flex-end" align="flex-end">
                        <field readonly="true" fullname="IA.REMAINING_TO_MATCH">
                            <path>AMOUNTTOMATCH_M</path>
                            <type assoc="T">
                                <ptype>currency</ptype>
                                <type>decimal</type>
                                <maxlength>14</maxlength>
                            </type>
                            <default>0</default>
                        </field>
                    </flexrow>
                </flexsection>
            </flexstack>
        </page>
    </pages>
    <footer >
        <button className="right" id="prevTxnButton" hidden="true">
            <name>IA.PREVIOUS</name>
            <events>
                <click>navigateMatchTransactions('false', 'true');</click>
            </events>
        </button>
        <button className="right" id="nextTxnButton" hidden="true">
            <name>IA.NEXT</name>
            <events>
                <click>navigateMatchTransactions('true', 'true');</click>
            </events>
        </button>
        <button className="right" id="matchTxnButton">
            <name>IA.MATCH</name>
            <events>
                <click>saveMatchTransactions('true', 'false');</click>
            </events>
        </button>
        <button className="right" id="matchNextTxnButton" hidden="true">
            <name>IA.MATCH_AND_GO_TO_NEXT</name>
            <events>
                <click>saveMatchTransactions('true', 'true');</click>
            </events>
        </button>
        <button className="right" id="unmatchTxnButton" hidden="true">
            <name>IA.UNMATCH</name>
            <events>
                <click>saveMatchTransactions('false', 'false');</click>
            </events>
        </button>
        <button className="right" id="cancelButton">
            <name>IA.CANCEL</name>
            <events>
                <click>closeMatchTransactionPage('true');</click>
            </events>
        </button>
        <button  className="right" id="helpButton">
            <name>IA.HELP</name>
            <events>
                <click>pageHelpOpenTopic('Find_match')</click>
            </events>
        </button>
    </footer>
</floatingPage>