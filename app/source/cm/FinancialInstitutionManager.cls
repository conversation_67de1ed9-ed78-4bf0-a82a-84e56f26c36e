<?php
import('EntityManager');
import('SBCServiceFactory');

class FinancialInstitutionManager extends EntityManager
{

    /**
     * @var array $sbcServices
     */
    private $sbcServices = [];

    /**
     * @var array $bankAccountIds
     */
    private $bankAccountIds = [];

    /**
     * @var array $creditCardIds
     */
    private $creditCardIds = [];

    /**
     * @var array $invalidAccountKeys
     */
    private $invalidAccountKeys = [];

    /**
     * @param array $params
     */
    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    /**
     * @param  int    $recordNo
     * @param  array    $sbcServiceKeys
     */
    private function populateSBCServices($recordNo, $sbcServiceKeys = [])
    {
        if ( count($this->sbcServices) == 0 ) {
            if ( count($sbcServiceKeys) == 0 ) {
                $sbcServiceKeys =
                    self::GetListQuick('finsbcservice', [ 'SBCSERVICEKEY' ],
                                       [ 'FINANCIALINSTITUTIONKEY' => $recordNo ]);
            }
            foreach ( $sbcServiceKeys as $sbcServiceKey ) {
                $sbcService = SBCServiceFactory::getService($sbcServiceKey['SBCSERVICEKEY']);
                $this->sbcServices[SBCServiceKey::getName($sbcServiceKey['SBCSERVICEKEY'])] = $sbcService;
            }
        }
    }

    /**
     * @param int       $recordNo
     * @param string    $accountID
     * @param array     $sbcServiceKeys
     *
     * @return bool
     */
    private function checkWithServices($recordNo, $accountID, $sbcServiceKeys = [])
    {
        $this->populateSBCServices($recordNo, $sbcServiceKeys);

        $isAllowed = true;

        foreach ( $this->sbcServices as $sbcService ) {
            $isAllowed = $sbcService->isAllowed($recordNo, $accountID);
            if ( ! $isAllowed ) {
                break;
            }
        }

        return $isAllowed;
    }

    /**
     * @param string    $financialInstitutionID
     * @param string    $accountId
     *
     * @return bool
     */
    public function associateAccount($financialInstitutionID, $accountId)
    {
        $ok = false;
        $msg = _("financialInstitutionId=" . $financialInstitutionID
                 . " accountId=$accountId");
        // check with all services if this action is allowed
        $finInst = self::GetListQuick('financialinstitution', [ 'RECORDNO' ],
                                      [
                                          'FINANCIALINSTITUTIONID' => $financialInstitutionID,
                                      ]);
        if ( isset($finInst) && $finInst[0] ) {
            if ( $this->checkWithServices($finInst[0]['RECORDNO'], $accountId) ) {
                $ok = $this->internalAssociateAccount($finInst[0]['RECORDNO'],
                                                      $accountId);
                self::logMsg($msg, __FUNCTION__, __LINE__, 'Associating Account', $ok, 1);
            }
        }

        return $ok;
    }

    /**
     * @param int           $financialInstitutionKey
     * @param string        $accountId
     *
     * @param array|null    $values
     *
     * @return bool
     */
    private function internalAssociateAccount($financialInstitutionKey, $accountId, &$values = null)
    {
        // $ok = false;
        $msg = _("financialInstitutionKey=" . $financialInstitutionKey
                 . " accountId=$accountId");
        XACT_BEGIN(__METHOD__);
        try {
            FinancialInstitutionUtils::getAccountInfo($accountId,
                                 $recordNo,
                                 $accountType,
                                 $entityName,
                                 $entityFieldName);
            $values['RECORDNO'] = $recordNo;
            $values['ACCOUNTTYPE'] = $accountType;

            $tableName = $accountType != FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? 'creditcard' : 'bankaccount';
            $idFieldName = $accountType != FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? 'cardid' : 'accountid';
            $stmt = "update $tableName set financialinstitutionkey = :2 WHERE cny# = :1 AND $idFieldName = :3";
            $ok = ExecStmt([$stmt, GetMyCompany(), $financialInstitutionKey, $accountId]);

        } catch ( Exception $e ) {
            $ok = false;
            $msg = $msg . _(' Exception msg=' . $e->getMessage());
            self::logMsg($msg, __FUNCTION__, __LINE__,'internalAssociateAccount',$ok,  3, $e);
        }
        $ok = $ok && XACT_COMMIT(__METHOD__);
        If ( ! $ok ) {
            self::logMsg($msg, __FUNCTION__, __LINE__,'internalAssociateAccount',$ok,  2);
            XACT_ABORT(__METHOD__);
        }
        return $ok;
    }

    /**
     * @param int    $financialInstitutionKey
     * @param string $accountType
     * @param string $accountId
     *
     * @return bool
     */
    public function associateAccount2($financialInstitutionKey, $accountType, $accountId)
    {
        // $ok = false;
        $msg = _("financialInstitutionKey=" . $financialInstitutionKey
                 . " accountType=$accountType"
                 . " accountId=$accountId");

        XACT_BEGIN(__METHOD__);
        try {
            $tableName = $accountType != FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? 'creditcard' : 'bankaccount';
            $idFieldName = $accountType != FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? 'cardid' : 'accountid';
            $stmt = "update $tableName set financialinstitutionkey = :2 WHERE cny# = :1 AND $idFieldName = :3";
            $ok = ExecStmt([$stmt, GetMyCompany(), $financialInstitutionKey, $accountId]);
            self::logMsg($msg, __FUNCTION__, __LINE__, 'Setting financialinstitutionkey', $ok, 1);
        } catch ( Exception $e ) {
            $ok = false;
            $msg = $msg . _(' Exception msg=' . $e->getMessage());
            self::logMsg($msg, __FUNCTION__, __LINE__,'Setting financialinstitutionkey',$ok,  3, $e);
        }
        $ok = $ok && XACT_COMMIT(__METHOD__);
        If ( ! $ok ) {
            self::logMsg($msg, __FUNCTION__, __LINE__,'Setting financialinstitutionkey',$ok,  2);
            XACT_ABORT(__METHOD__);
        }
        return $ok;
    }

    /**
     * @param int $financialInstitutionKey
     * @param string $accountType
     * @param string $accountId
     *
     * @return bool
     */
    public static function  disassociateAccount($financialInstitutionKey, $accountType, $accountId)
    {
        // $ok = false;
        $msg = _("financialInstitutionKey=" . $financialInstitutionKey
                 . " accountType=$accountType"
                 . " accountId=$accountId");

        XACT_BEGIN(__METHOD__);
        try {
            $tableName = $accountType != FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? 'creditcard' : 'bankaccount';
            $idFieldName = $accountType != FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? 'cardid' : 'accountid';
            $stmt = "update $tableName set financialinstitutionkey = null WHERE cny# = :1 AND $idFieldName = :3";
            $ok = ExecStmt([$stmt, GetMyCompany(), $financialInstitutionKey, $accountId]);
            self::logMsg($msg, __FUNCTION__, __LINE__,'Disassociating financialinstitutionkey',$ok,  1);
        } catch ( Exception $e ) {
            $ok = false;
            $msg = $msg . _(' Exception msg=' . $e->getMessage());
            self::logMsg($msg, __FUNCTION__, __LINE__,'Disassociating financialinstitutionkey',$ok,  3, $e);
        }
        $ok = $ok && XACT_COMMIT(__METHOD__);
        If ( ! $ok ) {
            self::logMsg($msg, __FUNCTION__, __LINE__,'Commit after disassociating financialinstitutionkey',$ok,  2);
            XACT_ABORT(__METHOD__);
        }


        return $ok;
    }

    /**
     * @param string|int    $_id
     * @param bool          $doget
     * @param array|null      $raw
     *
     * @return bool
     */
    function IsDeletable($_id, $doget = true, $raw = null)
    {
        $ok = false;
        $cny = GetMyCompany();
        $query =
            "select count(*) as COUNT from finsbcservice where cny# = :1 and financialinstitutionkey = :2 ";

        $cnt = QueryResult([ $query, $cny,  $_id]);
        if($cnt[0]['COUNT'] == 0) {
            $ok= parent::IsDeletable($_id, $doget, $raw);
        } else {
            $errMsg = "This Financial Institution has associated accounts.";
            $correctionMsg = "Disassociate all accounts before you delete this Financial Institution.";
            Globals::$g->gErr->addError("CM-0203", __FILE__ . ':' . __LINE__, $errMsg,'',
                $correctionMsg);
        }
        return $ok;
    }


    /**
     * @param string    $financialinstitutionid
     * @param string    $serviceName
     * @param array    $contract
     *
     * @return bool
     */
    public function setService($financialinstitutionid, $serviceName, $contract)
    {
        return $this->fulfillContractForService($financialinstitutionid,
                                                $serviceName,
                                                $contract);
    }

    /**
     * @param string $financialinstitutionid
     * @param string $serviceName
     * @param array $contract
     *
     * @return bool
     */
    private function fulfillContractForService($financialinstitutionid, $serviceName, $contract)
    {
        $ok = false;
        if ( $serviceName == SBCServiceKey::MALSERVICE
             && isset($contract['PRIMARYACCOUNTKEY'])
             && isset($contract['PRIMARYACCOUNTTYPE']) ) {
            if ( array_search($contract['PRIMARYACCOUNTTYPE'],
                              [ FinancialInstitutionUtils::SAVINGS, FinancialInstitutionUtils::CHECKING ]) !== false ) {
                $primayAccountId = $this->bankAccountIds[$contract['PRIMARYACCOUNTKEY']]['ACCOUNTID'];
            } else {
                $primayAccountId = $this->creditCardIds[$contract['PRIMARYACCOUNTKEY']]['ACCOUNTID'];
            }
            $ok = $this->fulfillContractForMAL($financialinstitutionid,
                                               $primayAccountId);
        }

        return $ok;
    }

    /**
     * @param string    $financialinstitutionid
     * @param string    $primaryAccountID
     *
     * @return bool
     */
    private function fulfillContractForMAL ($financialinstitutionid, $primaryAccountID)
    {
        return $this->setPrimaryAccount($financialinstitutionid, SBCServiceKey::MALSERVICE, $primaryAccountID);
    }

    /**
     * @param string    $financialinstitutionid
     * @param string    $serviceName
     * @param string    $accountID
     *
     * @return bool
     */
    public function setPrimaryAccount($financialinstitutionid, $serviceName, $accountID)
    {
        $source = "FinancialInstitutionManager::setPrimaryAccount";
        $ok = true;
        // Start the session
        XACT_BEGIN($source);
        $serviceKey = SBCServiceKey::getKey($serviceName);
        if ( $serviceKey ) {
            $finInst = self::GetListQuick('financialinstitution', [ 'RECORDNO' ],
                                          [
                                              'FINANCIALINSTITUTIONID' => $financialinstitutionid,
                                          ]);
            if ( isset($finInst) && $finInst[0] ) {
                $sbcServicekeys [] = [ 'SBCSERVICEKEY' => $serviceKey ];
                if ( $this->checkWithServices($finInst[0]['RECORDNO'], $accountID, $sbcServicekeys) ) {
                    $accountValues = [];
                    $ok = $this->internalAssociateAccount($finInst[0]['RECORDNO'],
                                                          $accountID,
                                                          $accountValues);
                    if ( $ok && $accountValues ) {
                        $ok = $this->upsertIntoResolve($finInst[0]['RECORDNO'],
                                                       $serviceKey,
                                                       $accountValues['ACCOUNTTYPE'],
                                                       $accountValues['RECORDNO']);
                    }
                } else {
                    $ok = false;
                    $msg = sprintf( "The contract for %s is not valid for %s", SBCServiceKey::getName($serviceKey), $financialinstitutionid);
                    Globals::$g->gErr->addIAError("CM-0204", __FILE__ . __LINE__, $msg,
                    ['SERVICE' => SBCServiceKey::getName($serviceKey), 'FINANCIALINSTITUTIONID' =>$financialinstitutionid]);
                }
            }
        } else {
            $ok = false;
            $msg = sprintf( "The valid values for the SERVICENAME are %s", SBCServiceKey::getAllServiceNames());
            Globals::$g->gErr->addIAError("CM-0205", __FILE__ . __LINE__, $msg,
            ['SERVICE_NAMES'=> SBCServiceKey::getAllServiceNames()]);
        }
        if ($ok) {
            XACT_COMMIT($source);
        } else {
            XACT_ABORT($source);
        }
        return $ok;
    }

    /**
     * Returns the primary account associated with the financial institution for the give service.
     *
     * @param string    $financialinstitutionid
     * @param string    $serviceName
     *
     * @return array
     */
    public function getPrimaryAccount($financialinstitutionid, $serviceName)
    {
        $finsbcserviceMgr = Globals::$g->gManagerFactory->getManager('finsbcservice');
        $filters[] = [ 'FINANCIALINSTITUTIONKEY', '=', $financialinstitutionid ];
        $filters[] = [ 'SBCSERVICEKEY', '=', $serviceName ];
        $params = [
            'filters' => [ $filters ]
        ];
        $accounts = $finsbcserviceMgr->GetList($params);
        $primaryAccount = [];
        if (!empty($accounts)) {
            $primaryAccount = $accounts[0];
        }
        return $primaryAccount;
    }

    /**
     * @param string $financialInstitutionKey
     * @param string $serviceKey
     * @param string $accountType
     * @param int   $accountRecordNo
     *
     * @return bool
     */
    private function upsertIntoResolve($financialInstitutionKey, $serviceKey, $accountType, $accountRecordNo)
    {
        $finsbcserviceMgr = $this->getFinSbcSeviceEntityMgr();
        $service = $finsbcserviceMgr->getFinSbcService($financialInstitutionKey, $serviceKey);

        $msg = _("financialInstitutionKey=$financialInstitutionKey"
                 . " accountType=$accountType"
                 . " accountkey=$accountRecordNo");
        // If there is no existing service then add
        if (empty($service)) {
            $values['CNY#'] = GetMyCompany();
            $values['PRIMARYBANKACCOUNTKEY'] =
                $accountType == FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? $accountRecordNo : null;
            $values['PRIMARYCCDATAKEY'] =
                $accountType == FinancialInstitutionUtils::CC_ACCOUNT_TYPE ? $accountRecordNo : null;
            $values['FINANCIALINSTITUTIONKEY'] = $financialInstitutionKey;
            $values['SBCSERVICEKEY'] = $serviceKey;
            $values['CREATEDBY'] = GetMyUserid();
            $values['WHENCREATED'] = GetCurrentUTCTimestamp();
            $ok = $finsbcserviceMgr->regularAdd($values);
            self::logMsg($msg, __FUNCTION__, __LINE__,  'FinSbcService::Add',$ok, 1);
        } else {
            $service['PRIMARYBANKACCOUNTKEY'] =
                $accountType == FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? $accountRecordNo : null;
            $service['PRIMARYCCDATAKEY'] =
                $accountType == FinancialInstitutionUtils::CC_ACCOUNT_TYPE ? $accountRecordNo : null;
            $service['MODIFIEDBY'] = GetMyUserid();
            $service['WHENMODIFIED'] = GetCurrentUTCTimestamp();
            $ok = $finsbcserviceMgr->regularSet($service);
            self::logMsg($msg, __FUNCTION__, __LINE__,'FinSbcService::Set',$ok,  1);
        }
        return $ok;
    }

    /**
     * @return array
     */
    public function unconnectedaccounts()
    {
        $params['selects'] = [ 'ACCOUNTRECORDNO', 'ACCOUNTID', 'ACCOUNTNAME', 'ACCOUNTTYPE' ];
        $params['filters'][0][] = [
            'operator' => 'OR',
            'filters'  => [
                [ 'WPBBANKACCOUNTSTATUS', 'ISNULL' ],
                [ 'WPBBANKACCOUNTSTATUS', '=', 'CONNECTIONREQUESTED' ],
            ],
        ];

        $result = $this->getAccounts($params);

        $accounts['UNCONNECTEDINTACCTACCOUNTS'] = FinancialInstitutionUtils::excludeAccountsWithOnlyFileImport($result);
        // $accounts['UNCONNECTEDINTACCTACCOUNTS'] = $result;

        return $accounts;
    }

    /**
     * @param array $params
     *
     * @return array
     */
    private function getAccounts($params)
    {
        $glAccountManager = Globals::$g->gManagerFactory->getManager('allaccounts');

        $result = $glAccountManager->GetList($params);

        $allaccounts = [];
        if ( is_array($result) && count($result) > 0 ) {
            if ( $result[0]['ACCOUNTTYPE'] ) {
                $allaccounts['CHECKINGS']['CheckingAccount'] =
                    array_filter($result, function($account) {
                        return ( $account['ACCOUNTTYPE'] == 'CHECKING' );
                    });

                $allaccounts['SAVINGS']['SavingsAccount'] =
                    array_filter($result, function($account) {
                        return ( $account['ACCOUNTTYPE'] == 'SAVINGS' );
                    });

                $allaccounts['CREDITCARD']['CreditCards'] =
                    array_filter($result, function($account) {
                        return ( $account['ACCOUNTTYPE'] == 'CREDITCARD' );
                    });
            }
        }
        return $allaccounts;
    }

    /**
     * @return array
     */
    public function getUnconnectedaccountsNonAPIuse(): array
    {
        $allUnconnectedAccounts = [];
        $unconnectedAccounts = $this->unconnectedaccounts();
        if (count($unconnectedAccounts) > 0) {
            $allUnconnectedAccounts['BANK'] =
                is_array($unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['CHECKINGS']['CheckingAccount']) &&
                is_array($unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['SAVINGS']['SavingsAccount']) ?
                    array_merge($unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['CHECKINGS']['CheckingAccount'],
                        $unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['SAVINGS']['SavingsAccount']) :
                    (is_array($unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['CHECKINGS']['CheckingAccount']) ?
                        $unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['CHECKINGS']['CheckingAccount'] :
                        (is_array($unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['SAVINGS']['SavingsAccount']) ?
                            $unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['SAVINGS']['SavingsAccount'] : []));

            $allUnconnectedAccounts['CREDITCARD'] = is_array($unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['CREDITCARD']['CreditCards']) ?
                $unconnectedAccounts['UNCONNECTEDINTACCTACCOUNTS']['CREDITCARD']['CreditCards'] : [];
        }

        return $allUnconnectedAccounts;
    }

    /**
     * @return array
     */
    public function malsupportedaccounts(): array
    {
        $params['selects'] = [ 'ACCOUNTID', 'ACCOUNTNAME', 'ACCOUNTTYPE' ];
        $params['filters'] = [[
                                 ['WPBBANKSUPPORTMAL', '=', 'true'],
                                 ['financialinstitutionkey', 'ISNULL'],
                                 ['WPBBANKACCOUNTSTATUS' , '=', 'CONNECTED']
                             ]];

        $result = $this->getAccounts($params);

        $accounts['MALSUPPORTEDACCOUNTS'] = $result;

        return $accounts;
    }


    /**
     * @param  string $financialInstitutionID
     *
     * @return array
     */
    public function getAvailableAccountsToConnect($financialInstitutionID)
    {
        $finInst = self::GetListQuick('financialinstitution', [ 'RECORDNO' ],
                                      [
                                          'FINANCIALINSTITUTIONID' => $financialInstitutionID,
                                      ]);
        return $this->getAvailableAccountsToConnectByFinKey($finInst[0]['RECORDNO']);
    }

    /**
     * @param string    $financialInstitutionKey
     *
     * @return array
     */
    public function getAvailableAccountsToConnectByFinKey($financialInstitutionKey)
    {
        $result = [];
        if ( isset($financialInstitutionKey )) {
            $this->populateSBCServices($financialInstitutionKey);
            if ( array_key_exists("MAL", $this->sbcServices) ) {
                $sbcPrimaryKeys =
                    self::GetListQuick('finsbcservice',
                                       [ 'PRIMARYBANKACCOUNTKEY', 'PRIMARYCCDATAKEY' ],
                                       [ 'FINANCIALINSTITUTIONKEY' => $financialInstitutionKey ]);

                $accountKey = $sbcPrimaryKeys[0]['PRIMARYBANKACCOUNTKEY'] != null
                    ?
                    $sbcPrimaryKeys[0]['PRIMARYBANKACCOUNTKEY']
                    :
                    $sbcPrimaryKeys[0]['PRIMARYCCDATAKEY'];

                $accountType = $sbcPrimaryKeys[0]['PRIMARYBANKACCOUNTKEY'] != null
                    ?
                    WPBBankAccountManager::BANK_ACCOUNT_TYPE
                    :
                    WPBBankAccountManager::CC_ACCOUNT_TYPE;

                $availableAccountsToConnect = $this->sbcServices["MAL"]->getAvailableAccountsToConnect($accountKey, $accountType);

                $result['AVAILABLEACCOUTSTOCONNECT']['AVAILABLEACCOUTTOCONNECT'] = $availableAccountsToConnect;

            }
        }
        return $result;
    }

    /**
     * @param string  $accountType
     *
     * @return array
     */
    public function getAvailableAccountsToConnectByAccntId($accountKey, $accountType)
    {
        $result = [];
        $result['AVAILABLEACCOUTSTOCONNECT']['AVAILABLEACCOUTTOCONNECT'] = WPBBankAccountManager::getAvailableBankAccounts($accountType, $accountKey);
        return $result;
    }

    /**
     * @param string $financialInstitutionId
     * @param string $bankaccountcandidateID
     * @param string $accountID
     * @param string $requestedStartDate
     *
     * @return array
     */
    public function mapAccount($financialInstitutionId, $bankaccountcandidateID, $accountID, $requestedStartDate)
    {
        $accountsToMap [] = [ 'bankAccountCandidateId' => $bankaccountcandidateID,
                           'requestedStartDate'     => $requestedStartDate,
                           'accountID'              => $accountID ];
        return $this->mapAccounts($financialInstitutionId, $accountsToMap);
    }

    /**
     * @param string $financialInstitutionId
     * @param array  $accountsToMap
     *
     * @return array
     */
    public function mapAccounts ($financialInstitutionId, $accountsToMap)
    {
        $accountMapped = [];
        try {

            if (FinancialInstitutionUtils::getPrimaryInfo($financialInstitutionId,
                                                          SBCServiceKey::getKey(SBCServiceKey::MALSERVICE),
                                                          $financialInstitutionRecordNo)) {
                $sbcServicekeys [] = [ 'SBCSERVICEKEY' => SBCServiceKey::getKey(SBCServiceKey::MALSERVICE) ];
                $this->populateSBCServices($financialInstitutionId,
                                           $sbcServicekeys);
                if ( $this->sbcServices[SBCServiceKey::MALSERVICE] ) {
                    $this->sbcServices[SBCServiceKey::MALSERVICE]->mapAccounts($financialInstitutionId,
                                                                                     $financialInstitutionRecordNo,
                                                                                     $accountsToMap,
                                                                                     $accountMapped);
                }
            } else {
                $msg = _("$financialInstitutionId does not use MAL service");
                Globals::$g->gErr->addError("CM-0206", __FILE__ . __LINE__, $msg,
                ['FINANCIAL_INSTITUTION_ID'=> $financialInstitutionId]);
            }
        } catch (Exception $e) {

        }
        $mappedStatus['MAPPEDACCOUNTSTATUS'] = $accountMapped;
        return $mappedStatus;
    }

    // /**
    //  * @param string $finacialInstitutionKey
    //  * @param int    $accountkey
    //  * @param string $accountType
    //  * @param string $accountId
    //  *
    //  * @return bool
    //  */
    // public function onDelete($finacialInstitutionKey, $accountkey, $accountType, $accountId)
    // {
    //     // logToFileWarning("accountHasDisconnected $finacialInstitutionKey, $accountkey, $accountType, $accountId");
    //     $ok = true;
    //     if (is_numeric($finacialInstitutionKey) && is_numeric($accountkey) && is_string($accountType)) {
    //         $ok = $this->maintainContractForServices($finacialInstitutionKey,
    //                                                  $accountkey,
    //                                                  $accountType,
    //                                                  $accountId);
    //     }
    //     return $ok;
    // }

    /**
     * @param int    $finacialInstitutionKey
     * @param int    $accountkey
     * @param string $accountType
     * @param string $accountId
     * @param array $sbcAccountStatus
     *
     * @return bool
     */
    public function onSBCAccountStatusChange($finacialInstitutionKey,
                                             $accountkey,
                                             $accountType,
                                             $accountId,
                                             $sbcAccountStatus)
    {
        $ok = true;
        if ( is_numeric($finacialInstitutionKey) && is_numeric($accountkey) && is_string($accountType) ) {
            $ok = $this->maintainContractForServices($finacialInstitutionKey,
                                                     $accountkey,
                                                     $accountType,
                                                     $accountId,
                                                     FinancialInstitutionUtils::SBC_ACCOUNT_STATUS_CHANGE,
                                                     $sbcAccountStatus);
        }

        return $ok;
    }
    /**
     * @param string $finacialInstitutionKey
     * @param int    $accountkey
     * @param string $accountType
     * @param string $accountId
     *
     * @return bool
     */
    public function onDisconnect($finacialInstitutionKey,
                                 $accountkey,
                                 $accountType,
                                 $accountId)
    {
        $ok = true;
        if ( is_numeric($finacialInstitutionKey) && is_numeric($accountkey) && is_string($accountType) ) {
            $ok = $this->maintainContractForServices($finacialInstitutionKey,
                                                     $accountkey,
                                                     $accountType,
                                                     $accountId,
                                                     FinancialInstitutionUtils::ACCOUNT_DISCONNECT, []);
        }

        return $ok;
    }

    /**
     * @param string $finacialInstitutionKey
     * @param int    $accountkey
     * @param string $accountType
     * @param int    $accountId
     * @param int    $action
     * @param array  $extraData
     *
     * @return bool
     */
    private function maintainContractForServices($finacialInstitutionKey,
                                                 $accountkey,
                                                 $accountType,
                                                 $accountId,
                                                 $action,
                                                 $extraData)
    {

        $ok = true;
        $msg = _("financialInstitutionKey=$finacialInstitutionKey"
                 . " accountType=$accountType"
                 . " accountkey=$accountkey"
                 . " accountId=$accountId");
        XACT_BEGIN(__METHOD__);
        try {
            if (is_numeric($finacialInstitutionKey) && is_numeric($accountkey) && is_string($accountType)) {
                $finServices = EntityManager::GetListQuick('finsbcservice', null,
                                                           [ 'FINANCIALINSTITUTIONKEY' => $finacialInstitutionKey ]);
                foreach ( $finServices as $finService ) {
                    if ( ( $finService['SBCSERVICEKEY'] ) == '1'/*MAL*/ ) {
                        $ok = $this->maintainContractForMAL($finService,
                                                            $accountkey,
                                                            $accountType,
                                                            $accountId,
                                                            $action,
                                                            $extraData);
                    }
                    if ( ! $ok ) {
                        break;
                    }
                }
            }
        } catch ( Exception $e ) {
            $ok = false;
            $msg = $msg . _(' Exception msg=' . $e->getMessage());
            self::logMsg($msg, __FUNCTION__, __LINE__,'maintainContractForServices',$ok,  3, $e);
        }
        $ok = $ok && XACT_COMMIT(__METHOD__);
        If ( ! $ok ) {
            self::logMsg($msg, __FUNCTION__, __LINE__,'maintainContractForServices',$ok,  2);
            XACT_ABORT(__METHOD__);
        }

        return $ok;
    }

    /**
     * @param array  $finService
     * @param int    $accountkey
     * @param string $accountType
     * @param string $accountId
     * @param int    $action
     * @param array  $extraData
     *
     * @return bool
     */
    private function maintainContractForMAL ($finService,
                                             $accountkey,
                                             $accountType,
                                             $accountId,
                                             $action,
                                             $extraData)
    {
        $ok = true;
        logToFileWarning("maintainContractForMAL $finService, $accountkey, $accountType, $accountId");
        if ($action == FinancialInstitutionUtils::ACCOUNT_DISCONNECT) {
            $ok =  self::disassociateAccount($finService['FINANCIALINSTITUTIONKEY'],
                                             $accountType,
                                             $accountId);
            $ok = $ok && $this->handlePrimaryAccount($finService,
                                                     $accountkey,
                                                     $accountType,
                                                     $accountId);

        } else if ($action == FinancialInstitutionUtils::SBC_ACCOUNT_STATUS_CHANGE) {
            /* @var MALSbcService $malsbcService */
            $malsbcService = SBCServiceFactory::getService(SBCServiceKey::getKey(SBCServiceKey::MALSERVICE) );
            if (null != $malsbcService) {
                $ok = $malsbcService->onSBCAccountStatusChange($finService['FINANCIALINSTITUTIONKEY'],
                                                         $extraData[FinancialInstitutionUtils::SBCACCOUNTSTATUS]);
            }
        }

        return $ok;
    }

    /**
     * @param array  $finService
     * @param int    $accountkey
     * @param string $accountType
     * @param string $accountId
     *
     * @return bool
     */
    private function handlePrimaryAccount($finService, $accountkey, $accountType, $accountId)
    {
        $ok = true;
        $msg = _("financialInstitutionKey=" . $finService['FINANCIALINSTITUTIONKEY']
                 . " accountType=$accountType"
                 . " accountkey=$accountkey"
                 . " accountId=$accountId");

        $fieldName = $accountType == FinancialInstitutionUtils::BANK_ACCOUNT_TYPE ? 'PRIMARYBANKACCOUNTKEY' : 'PRIMARYCCDATAKEY';

        if ($finService[$fieldName] == $accountkey) {
            $cny = GetMyCompany();
            $financialInstitutionKey =  $finService['FINANCIALINSTITUTIONKEY'];
            $query = "select record# as RECORDNO, accountid as ACCOUNTID from bankaccount where cny# = :1 and financialinstitutionkey = :2";
            $accountType = FinancialInstitutionUtils::BANK_ACCOUNT_TYPE;
            $result = QueryResult([ $query, $cny, $financialInstitutionKey]);
            if (!$result) {
                $accountType = FinancialInstitutionUtils::CC_ACCOUNT_TYPE;
                $query = "select record# as RECORDNO, cardid as ACCOUNTID from creditcard where cny# = $cny and financialinstitutionkey =:2";
                $result = QueryResult([ $query, $cny, $financialInstitutionKey]);
            }
            $isPrimaryAccountSet = false;
            foreach ($result as $row) {
                if ($row['ACCOUNTID'] != $accountId) {
                    $ok = $this->upsertIntoResolve($financialInstitutionKey, "1",
                                                   $accountType, $row['RECORDNO']);
                    $isPrimaryAccountSet = true;
                    self::logMsg($msg, __FUNCTION__, __LINE__,'Change primary account',$ok,  1);
                    break;
                }
            }

            if ( $isPrimaryAccountSet == false ) {
                $query =
                    " delete from finsbcservice where cny#=:1 and financialinstitutionkey=:2 "
                    . " and sbcservicekey = :3 ";
                $ok = ExecStmt([$query, $cny, $financialInstitutionKey, 1]);

                self::logMsg($msg, __FUNCTION__, __LINE__,'Delete FinSbcService record',$ok,  1);

            }

        }
        return $ok;

    }
    /**
     * @return FinsbcserviceManager
     */
    public function getFinSbcSeviceEntityMgr()
    {
        return Globals::$g->gManagerFactory->getManager('finsbcservice');
    }

    /**
     * Get a single record
     *
     * @param string        $ID
     * @param string[]|null $fields
     *
     * @return array|false
     */
    public function get($ID, $fields = null)
    {
        $values = parent::get($ID, $fields);

        $values['LASTMODIFIEDDATE'] = $values['WHENMODIFIED'];
        $this->getAddonServices($values);

        return $values;
    }

    /**
     * @param array $values
     */
    private function getAddonServices(&$values)
    {
        foreach ( $values['SBCSERVICE'] as $sbcService ) {
            $values['ADDONSERVICES'] [] = [
                "NAME"            => SBCServiceKey::getName($sbcService['SBCSERVICEKEY']),
                "SERVICECONTRACT" => [
                    "PRIMARYACCOUNTKEY"  => $sbcService['PRIMARYBANKACCOUNTKEY'] ?? $sbcService['PRIMARYCCDATAKEY'],
                    "PRIMARYACCOUNTTYPE" => $sbcService['PRIMARYBANKACCOUNTKEY'] ? $sbcService['PRIMARYBANKACCOUNTTYPE']
                        : FinancialInstitutionUtils::CREDITCARD,
                ],
            ];
        }
    }

    /**
     * Determines if the 'setOwnedObjects' function should do either an upsert/add during
     * processing.  Some classes need to avoid this behavior (i.e. because they handle it themselves,
     * like userinfo/userroles).
     *
     * @param array  $parentObject
     * @param string $ownedObject
     *
     * @return bool sucess or failure
     */
    protected function setOwnedObject(/** @noinspection PhpUnusedParameterInspection */ $parentObject,
        /** @noinspection PhpUnusedParameterInspection */ $ownedObject)
    {
        return true;
    }

    /**
     * to insert values of owned objects
     *
     * @param array &$values new values
     *
     * @return bool
     */
    protected function addOwnedObjects(&$values)
    {
        return true;
    }
    /**
     * Allow parent entity to skip fetching the ownedobjects
     *
     * @param array $objRec
     * @param bool  $validateReadOnly true if you want to skip for reads, false otherwise
     *
     * @return bool true if you want to read the owned object, false if you want caller to skip retrieval
     */
    protected function useOwnedObject(/** @noinspection PhpUnusedParameterInspection */ $objRec,
        /** @noinspection PhpUnusedParameterInspection */                               $validateReadOnly)
    {
        if ( array_search($objRec['path'],
                          [ 'BANKACCOUNTS', 'CUSTOMERCREDITCARDS', 'PRACTICEBANKACCOUNT', 'PRACTICECREDITCARD' ]) ) {
            return false;
        }

        return true;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        // Start the session
        XACT_BEGIN(__METHOD__);
        $localValues = $values;
        $ok = parent::regularAdd($values);
        // Set the primary account for MAL
        if ($ok && isset($localValues['PRIMARYACCOUNT'])) {
            $accountID = substr($values['PRIMARYACCOUNT'], 0, strrpos($values['PRIMARYACCOUNT'],'-'));
            $ok = $ok && $this->setPrimaryAccount($values['FINANCIALINSTITUTIONID'],
                                                  SBCServiceKey::MALSERVICE,
                                                  $accountID);
        }
        if ($ok) {
            XACT_COMMIT(__METHOD__);
        } else {
            XACT_ABORT(__METHOD__);
        }

        if ( $ok && isset($localValues['ADDONSERVICES']) ) {
            // no need to check for the success or failure for this function.
            $localValues['RECORDNO'] = $values['RECORDNO'];
            $this->gatherValidAccountIDs($localValues);
            if ($this->handleServiceRequest($localValues) ) {
                $this->performSerivceTask( $localValues);
            }
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        // Start the session
        XACT_BEGIN(__METHOD__);
        $localValues = $values;
        $ok = true;
        // Set the primary account for MAL
        if (isset($values['PRIMARYACCOUNT'])) {
            $accountID = substr($values['PRIMARYACCOUNT'], 0, strrpos($values['PRIMARYACCOUNT'],'-'));
            $ok = $this->setPrimaryAccount($values['FINANCIALINSTITUTIONID'],
                                           SBCServiceKey::MALSERVICE,
                                           $accountID);
        }
        // Update the name
        $stmt = ['update financialinstitution set name = :1, modifiedby = :2, whenmodified = :3 where cny# = :4 and record# = :5',
                 $values['FINANCIALINSTITUTIONNAME'], GetMyUserid(), GetCurrentDate(), GetMyCompany(), $values['RECORDNO']];
        $ok = $ok && ExecStmt($stmt);
        if ($ok) {
            XACT_COMMIT(__METHOD__);
        } else {
            XACT_ABORT(__METHOD__);
        }

        if ( $ok && isset($localValues['ADDONSERVICES']) ) {
            // no need to check for the success or failure for this function.
            $this->gatherValidAccountIDs($values);
            $this->performSerivceTask( $localValues);

        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return boolean
     */
    private function handleServiceRequest($values)
    {
        $ok = true;
        if ( isset($values['ADDONSERVICES']) ) {
            foreach ( $values['ADDONSERVICES'] as $addOnSevice ) {
                if ( ! $this->setService($values['FINANCIALINSTITUTIONID'],
                                         $addOnSevice['NAME'],
                                         $addOnSevice['SERVICECONTRACT']) ) {
                    $ok = false;
                    $msg = "financialInstitutionKey=" . $values['FINANCIALINSTITUTIONID']
                             . " serviceName= " . $addOnSevice['NAME']
                             . " contract= " . $addOnSevice['CONTRACT'];
                    self::logMsg($msg, __FUNCTION__, __LINE__, 'Failed to add service', false, 1);
                }
            }
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return array
     */
    private function performSerivceTask($values)
    {
        $result = [];
        $finsbcserviceMgr = $this->getFinSbcSeviceEntityMgr();
        $allServices = $finsbcserviceMgr->getAllServices($values['RECORDNO']);
        foreach ( $allServices as $service ) {
            switch (SBCServiceKey::getName($service['SBCSERVICEKEY'])) {
                case SBCServiceKey::MALSERVICE:
                    $result = $this->performMALServiceTask($values);
                    break;
                default:
                    break;
            }
        }

        return $result;
    }

    /**
     * @param array $values
     */
    private function performMALServiceTask($values)
    {
        $accountsToMap = [];
        foreach ( $values['CHECKINGACCOUNTS'] as $checkingAccount ) {
            if ( $this->bankAccountIds[$checkingAccount['RECORDNO']]['ACCOUNTID'] != null ) {
                $accountsToMap [] =
                    [ 'bankAccountCandidateId' => $checkingAccount['EXTERNALBANKACCOUNT']['ACTUALBANKACCOUNTID'],
                      'requestedStartDate'     => $checkingAccount['REQUESTEDSTARTDATE'],
                      'accountID'              => $this->bankAccountIds[$checkingAccount['RECORDNO']]['ACCOUNTID'],
                      'sbcAccountName'         => $checkingAccount['EXTERNALBANKACCOUNT']['ACTUALACCOUNTNAME'] ];
            }
        }

        foreach ( $values['SAVINGSACCOUNTS'] as $savingsAccount ) {
            if ( $this->bankAccountIds[$savingsAccount['RECORDNO']]['ACCOUNTID'] != null ) {
                $accountsToMap [] =
                    [ 'bankAccountCandidateId' => $savingsAccount['EXTERNALBANKACCOUNT']['ACTUALBANKACCOUNTID'],
                      'requestedStartDate'     => $savingsAccount['REQUESTEDSTARTDATE'],
                      'accountID'              => $this->bankAccountIds[$savingsAccount['RECORDNO']]['ACCOUNTID'],
                      'sbcAccountName'         => $savingsAccount['EXTERNALBANKACCOUNT']['ACTUALACCOUNTNAME'] ];
            }
        }

        foreach ( $values['CREDITCARDS'] as $creditCard ) {
            if ( $this->creditCardIds[$creditCard['RECORDNO']]['ACCOUNTID'] != null ) {
                $accountsToMap [] =
                    [ 'bankAccountCandidateId' => $creditCard['EXTERNALBANKACCOUNT']['ACTUALBANKACCOUNTID'],
                      'requestedStartDate'     => $creditCard['REQUESTEDSTARTDATE'],
                      'accountID'              => $this->creditCardIds[$creditCard['RECORDNO']]['ACCOUNTID'],
                      'sbcAccountName'         => $creditCard['EXTERNALBANKACCOUNT']['ACTUALACCOUNTNAME'] ];
            }
        }

        if (!empty($accountsToMap)) {
            $mappedAccounts = $this->mapAccounts($values['FINANCIALINSTITUTIONID'], $accountsToMap);
        }

        return $mappedAccounts;
    }

    /**
     * @param array $values
     */
    private function gatherValidAccountIDs ($values)
    {
        $bankAccounts = [];
        $creditCards= [];
        if (isset($values['ADDONSERVICES'])) {
            foreach ( $values['ADDONSERVICES'] as $addOnSevice ) {
                if ($addOnSevice['NAME'] == SBCServiceKey::MALSERVICE) {
                    if (array_search($addOnSevice['SERVICECONTRACT']['PRIMARYACCOUNTTYPE'], ['CHECKING', 'SAVINGS']) !== false) {
                        $bankAccounts [] = $addOnSevice['SERVICECONTRACT'] ['PRIMARYACCOUNTKEY'];
                    } else {
                        $creditCards [] = $addOnSevice['SERVICECONTRACT'] ['PRIMARYACCOUNTKEY'];
                    }
                }
            }
        }

        foreach ($values['CHECKINGACCOUNTS'] as $checkingAccount) {
            if ( isset($checkingAccount['RECORDNO']) && isset($checkingAccount['REQUESTEDSTARTDATE'])
                 && isset($checkingAccount['EXTERNALBANKACCOUNT']['ACTUALBANKACCOUNTID'])
                 && isset($checkingAccount['EXTERNALBANKACCOUNT']['ACTUALACCOUNTNAME']) ) {
                $bankAccounts [] = $checkingAccount['RECORDNO'];
            } else {
                $this->invalidAccountKeys [] = $checkingAccount['RECORDNO'];
            }
        }

        foreach ($values['SAVINGSACCOUNTS'] as $savingsAccount) {
            if ( isset($savingsAccount['RECORDNO']) && isset($savingsAccount['REQUESTEDSTARTDATE'])
                 && isset($savingsAccount['EXTERNALBANKACCOUNT']['ACTUALBANKACCOUNTID'])
                 && isset($savingsAccount['EXTERNALBANKACCOUNT']['ACTUALACCOUNTNAME']) ) {
                $bankAccounts [] = $savingsAccount['RECORDNO'];
            } else {
                $this->invalidAccountKeys [] = $savingsAccount['RECORDNO'];
            }
        }

        foreach ( $values['CREDITCARDS'] as $creditCard ) {
            if ( isset($creditCard['RECORDNO']) && isset($creditCard['REQUESTEDSTARTDATE'])
                 && isset($creditCard['EXTERNALBANKACCOUNT']['ACTUALBANKACCOUNTID'])
                 && isset($creditCard['EXTERNALBANKACCOUNT']['ACTUALACCOUNTNAME']) ) {
                $creditCards [] = $creditCard['RECORDNO'];
            } else {
                $this->invalidAccountKeys [] = $creditCard['RECORDNO'];
            }
        }

        $localBankAccounts = $bankAccounts;
        $localCreditCards = $creditCards;
        FinancialInstitutionUtils::gatherValidAccountIDs($bankAccounts, $creditCards);
        $validBankAccountKeys = array_keys($bankAccounts);
        $validCreditCardKeys = array_keys($creditCards);

        $this->invalidAccountKeys = array_merge($this->invalidAccountKeys,
                                                array_diff($localBankAccounts, $validBankAccountKeys),
                                                array_diff($localCreditCards, $validCreditCardKeys));

        $this->bankAccountIds = $bankAccounts;
        $this->creditCardIds = $creditCards;

        if (count($this->invalidAccountKeys) > 0 ) {
            $this->logMsg(" Invalid_Account_keys=[" . $this->invalidAccountKeys . "]",
                          __FUNCTION__, __LINE__, 'Failed to add account keys', false, 1);
        }

    }

    /**
     * @param string $msg
     * @param string $method
     * @param int $line
     * @param string $operation
     * @param bool   $status
     * @param int $level
     * @param Exception $e
     */
    private static function logMsg($msg, $method, $line, $operation, $status, $level, $e = null)
    {
        $sta = $status ? "Success " : "Failed ";
        $msg = _("WPB-Log - MAL flow : " . " method=" . $method . " line=" . $line
                 . " orgId=" . WPBSetupManager::getWPBOrgId()
                 . " companyId=" . WPBSetupManager::getWPBCompanyId()
                 . " operation=$operation"
                 . " status=$sta "
                 . $msg);
        if ($level == 1) {
            logToFileWarning($msg);
        } elseif ($level == 2) {
            logToFileError($msg);
        } elseif ($level == 3) {
            $e !== null ? logToFileException($e, $msg) :  logToFileError($msg);
        }
    }

    /**
     * Returns true if the current user has authorization to map account else false.
     *
     * @return bool
     */
    public function isUserAuthorizedToMapAccounts()
    {
        $reportOp = GetOperationId('cm/lists/financialinstitution/mapaccount');
        return CheckAuthorization($reportOp, 1);
    }
}
