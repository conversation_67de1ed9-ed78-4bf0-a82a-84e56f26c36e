{"name": "financialinstitution", "type": "entity", "operations": [{"name": "unconnectedaccounts", "internal": true, "response": ["UNCONNECTEDINTACCTACCOUNTS"]}, {"name": "malsupportedaccounts", "internal": true, "response": ["MALSUPPORTEDACCOUNTS"]}, {"name": "associateaccount", "internal": true, "args": [{"name": "financialinstitutionid", "type": "string", "required": true}, {"name": "ACCOUNTID", "type": "string", "required": true}], "response": ["ASSOCIATEDACCOUNTS"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "internal": true, "args": [{"name": "FINANCIALINSTITUTIONID", "type": "string", "required": true}, {"name": "SERVICENAME", "type": "string", "required": true}, {"name": "ACCOUNTID", "type": "string", "required": true}]}, {"name": "setservice", "internal": true, "args": [{"name": "FINANCIALINSTITUTIONID", "type": "string", "required": true}, {"name": "SERVICENAME", "type": "string", "required": true}, {"name": "PRIMARYACCOUNTID", "type": "string", "required": true}]}, {"name": "getavailableaccountstoconnect", "internal": true, "args": [{"name": "FINANCIALINSTITUTIONID", "type": "string", "required": true}], "response": ["AVAILABLEACCOUTSTOCONNECT"]}, {"name": "mapaccount", "internal": true, "args": [{"name": "FINANCIALINSTITUTIONID", "type": "string", "required": true}, {"name": "BANKACCOUNTCANDIDATEID", "type": "string", "required": true}, {"name": "ACCOUNTID", "type": "string", "required": true}, {"name": "REQUESTEDSTARTDATE", "type": "string", "required": true}], "response": ["MAPPEDACCOUNTSTATUS"]}]}