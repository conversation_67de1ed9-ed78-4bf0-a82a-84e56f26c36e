<?xml version='1.0' encoding='UTF-8'?>
<ROOT>
    <title>IA.CONFIGURE_TAXES</title>
    <view system="true">
        <events>
            <load>onLoad();</load>
        </events>
        <pages>
            <page className="columnSetupPadding">
                <section id="ValidationResultSectionId" hidden="true">
                    <title>IA.VALIDATION_RESULTS</title>
                    <field noLabel="1" isHTML="1" isToken="false" readonly="1" path="VALIDATIONERRORS">
                        <type type='textlabel' ptype='textlabel'></type>
                        <default>IA.THERE_ARE_VALIDATION_ERRORS</default>
                    </field>
                </section>
                <section id="InstallationSectionId" hidden="true">
                    <title>IA.INSTALLATION_OPTIONS</title>
                    <field noLabel="1" isHTML="1" isToken="false" readonly="1" path="INSTALLATIONHELP">
                        <type type='textlabel' ptype='textlabel'></type>
                        <default>IA.SELECT_WHETHER_YOU_NEED_TAX_SUPPORT_FOR_ONE_OR</default>
                    </field>
                    <field path="SINGLEORMULTITAX" noLabel="true">
                        <events>
                            <change>handleSingleOrMultiTaxChange(this.meta);</change>
                        </events>
                    </field>
                    <field path="SINGLETAXSOLUTION">
                    </field>
                    <field path="DNCHECKTAXID"></field>
                    <field path="DNFLTRTAXDETAIL"></field>
                    <field noLabel="1" isHTML="1" isToken="false" readonly="1" path="POSTINSTALLGUIDE">
                        <type type='textlabel' ptype='textlabel'></type>
                        <default></default>
                    </field>
                </section>
                <section id="MigrationSectionId" hidden="true">
                    <title>IA.CONVERSION_STATUS</title>
                    <field noLabel="1" isHTML="1" isToken="false" readonly="1" path="MIGRATIONSTATUS">
                        <type type='textlabel' ptype='textlabel'></type>
                        <default>IA.MIGRATION_STATUS</default>
                    </field>
                </section>
            </page>
        </pages>
        <floatingPage modalsize="medium">
            <id>confirmMultiTaxPopupPage</id>
            <title>IA.CONFIRM_SWITCHING_TO_MULTIPLE_TAX_JURISDICTIONS</title>
            <pages>
                <page>
                    <section>
                        <field noLabel="1" isHTML="1" path="DUMMY">
                            <type type='textlabel' ptype='textlabel'></type>
                            <default>IA.YOU_HAVE_CHOSEN_TO_ENABLE_MULTIPLE_TAX</default>
                        </field>
                    </section>
                </page>
            </pages>
            <footer>
                <button>
                    <name>IA.YES</name>
                    <events>
                        <click>confirmMultiTaxChange();</click>
                    </events>
                </button>
                <button>
                    <name>IA.NO</name>
                    <events>
                        <click>cancelMultiTaxChange();</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
    </view>
    <helpfile>Configuring_Taxes</helpfile>
</ROOT>
