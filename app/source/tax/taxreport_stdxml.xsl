<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <xsl:import href="../../private/xslinc/report_helpers.xsl"/>

    <xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/>
    <xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/>

    <xsl:template match="/">
        <xsl:apply-templates/>
    </xsl:template>

    <xsl:template match="reportdata">
        <xsl:apply-templates/>
    </xsl:template>

    <xsl:template match="report">
        <report
                showHeader="{@showHeader}"
                location 		= "{@location}"
                orientation 		= "Landscape"
                report_date		= "{@reportdate}"
                report_time		= "{@reporttime}"
                align_currency 		= "left"
                page_number 		= "Y"
                action 			= ""
                sess			= "{@sess}"
                done			= "{@done}"
                footer_allpages		= "Y"
        >

            <xsl:if test="(@orientation = 'Portrait')">
                <xsl:attribute name="maxfit">Y</xsl:attribute>
            </xsl:if>
            <company s="2"><xsl:value-of select="@co"/></company>
            <title s="3" titleNum="1"><xsl:value-of select="@title"/></title>
            <title s="3" titleNum="2"><xsl:value-of select="@title2"/></title>
            <title s="4" otherTitle="As of Date"><xsl:value-of select="@asofdate"/></title>
            <footer s="5" lines="1" footerNum="1"><xsl:value-of select="@titlecomment"/></footer>
            <xsl:for-each select="rtdim">
                <rtdim s="3" name="@name">
                    <name><xsl:value-of select="@name"/></name>
                    <value><xsl:value-of select="@value"/></value>
                </rtdim>
            </xsl:for-each>

            <header>
                <xsl:if test="number($narrow_format) = 1">
                    <hrow s="header">
                        <xsl:for-each select="HEADERS">
                            <hcol width="10" s="txt"/>      <!-- "Tax box" -->
                        </xsl:for-each>
                    </hrow>
                </xsl:if>
                <hrow s="51">
                    <xsl:for-each select="HEADERS">
                        <hcol id="0" s="17">
                            <xsl:value-of select="."/>
                        </hcol>
                    </xsl:for-each>
                </hrow>
                <xsl:if test="number($narrow_format) = 1">
                    <hrow s="14">
                        <hcol id="0" s="19" colspan="18"></hcol>
                    </hrow>
                </xsl:if>
            </header>

            <body s="body">
                <xsl:if test="number($narrow_format) = 1">
                    <row s="12">
                        <col  s="19" colspan="{/reportdata/report/@noofcolumns}" ></col>
                    </row>
                </xsl:if>
                <xsl:apply-templates/>
            </body>
            <xsl:call-template name="stylegroups"/>
            <script language="javascript">
                <xsl:apply-templates select="@javascript"/>
                <xsl:call-template name="script"/>
            </script>
        </report>
    </xsl:template>

    <xsl:template match="NODATA">
        <xsl:if test="string(@NODATA)=1">
            <row s="14">
                <col id="0" s="21" colspan="20">IA.NO_DATA_FOUND</col>
            </row>
        </xsl:if>
    </xsl:template>

    <xsl:template match="ITEMS">
        <row s="12">
            <xsl:for-each select="@*">
                <xsl:choose>
                    <xsl:when test="contains(name(), 'AMOUNT')">
                        <col id="0" s="22">
                            <xsl:value-of select="."/>
                        </col>
                    </xsl:when>
                    <xsl:when test="contains(name(), 'ENTRY_DATE')">
                        <col id="0" s="24" href="{../@TAXRECORDLINK}">
                            <xsl:value-of select="."/>
                        </col>
                    </xsl:when>
                    <xsl:when test="contains(name(), 'GL_BATCH')">
                        <col id="0" s="24" href="{../@GLLINK}">
                            <xsl:value-of select="."/>
                        </col>
                    </xsl:when>
                    <xsl:when test="contains(name(), 'LINK')">
                    </xsl:when>
                    <xsl:otherwise>
                        <col id="0" s="24">
                            <xsl:value-of select="."/>
                        </col>
                    </xsl:otherwise>
                </xsl:choose>
            </xsl:for-each>
        </row>
    </xsl:template>

</xsl:stylesheet>
