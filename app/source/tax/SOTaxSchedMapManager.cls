<?

/**
 *    FILE: SOTaxSchedMapManager.cls
 *    AUTHOR: <PERSON><PERSON><PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2022, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class SOTaxSchedMapManager  extends TaxSchedMapManager
{
    function __construct()
    {
        parent::__construct();
    }

    /**
     * @param array $values
     * @param string $verb 'add' or 'set'
     *
     * @return bool
     */
    protected function _PrepValues(&$values, $verb)
    {
        $values['MODULE'] = 'Sales';
        $ok = parent::_PrepValues($values, $verb);
        return $ok;
    }
}