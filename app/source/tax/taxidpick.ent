<?
/**
 *    FILE: taxid.ent
 *    AUTHOR: <PERSON><PERSON> V
 *    DESCRIPTION: Tax id picker object
 *
 *    (c) 2018 - 2022, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

$kSchemas['taxidpick'] = array(
    'schema' => array(
        'PICKID'        => 'taxidpick',
    ),
    'object' => array(
        'PICKID',
    ),
    'fieldinfo' => array(
        array(
            'fullname'  => 'IA.TAX_ID',
            'type'      =>
                array(
                    'ptype'     => 'text',
                    'type'      => 'text',
                    'maxlength' => 20
                ),
            'required'  => true,
            'desc'      => 'IA.TAX_ID',
            'path'      => 'PICKID'
        ),
    ),
    'vid' => 'PICKID',
    'module' => 'gl',
    'dbsorts' => array(array('PICKID')),
    'printas' => 'IA.TAX_ID',
    'pluralprintas' => 'IA.TAX_IDS'
);
