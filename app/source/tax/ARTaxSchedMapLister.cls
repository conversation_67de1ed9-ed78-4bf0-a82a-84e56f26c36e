<?

/**
 *    FILE: ARTaxSchedMapLister.cls
 *    AUTHOR: NaveenS
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *  Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

import('TaxSchedMapLister');

class ARTaxSchedMapLister extends TaxSchedMapLister
{
    function __construct()
    {
        if ( TaxSetupManager::isVATEnabled() ) {
            if ( IsInstalled(Globals::$g->kSOid) ) {
                $fields = ['ITEMGROUP', 'ENTITYGROUP', 'TAXSCHED' ];
            } else {
                $fields = ['ENTITYGROUP', 'TAXSCHED' ];
            }
            $fields[] = 'TAXSOLUTIONID';
        } else {
            $fields = ['ACCOUNTGROUP', 'ENTITYGROUP', 'TAXSCHED' ];
        }

        parent::__construct(array(
                'entity' => 'artaxschedmap',
                'importtype' => 'artaxschedmap',
                'importperm' => "ar/lists/artaxschedmap/create",
                'fields' => $fields,
            )
        );
    }
}