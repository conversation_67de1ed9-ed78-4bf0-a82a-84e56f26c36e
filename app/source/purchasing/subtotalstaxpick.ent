<?
/**
 * Entity for the Simple tax subtotal tax pick object
 *
 * <AUTHOR>
 * @copyright 2018 Intacct Corporation All, Rights Reserved
 */
require 'documentparsubtotal.ent';
$kSchemas['subtotalstaxpick']['fieldinfo'][] = array(
    'path'        => 'DOCPARKEY',
    'fullname'    => 'IA.RECORD_NUMBER',
    'desc'        => 'IA.RECORD_NUMBER',
    'type'         => array (
        'type'         => 'integer',
        'ptype'     => 'sequence',
        'size'         => 8,
        'maxlength' => 8
    ),
    'required' => true,
    'hidden' => true,
    'readonly' => true,
);
$kSchemas['subtotalstaxpick'] = EntityManager::inheritEnts($kSchemas['documentparsubtotal'], $kSchemas['subtotalstaxpick']);
