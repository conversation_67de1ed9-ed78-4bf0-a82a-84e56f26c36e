<?php
/** @noinspection PhpUndefinedVariableInspection */
$kSchemas['podocumentparsubtotal'] = array(
    'children' => array(
        'podocumentparams' => array ('fkey' => 'docparkey','invfkey' => 'record#', 'table' => 'docpar'),
        'glaccount' => array ('fkey' => 'glaccountkey','invfkey' => 'record#', 'table' => 'glaccount', 'join' => 'outer'),
        'offsetglaccount' => array ('fkey' => 'gloffsetaccountkey','invfkey' => 'record#', 'table' => 'glaccount', 'join' => 'outer'),
        'dept' => array( 'fkey' => 'deptkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'department'),
        'location' => array( 'fkey' => 'locationkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'location'),
        'entityloc' => array('fkey' => 'entitykey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'locationmst' ),
    ),
    'object' => array(
        'RECORDNO', 'DOCPARNO', 'DOCPARID', 'DISC_CHARGE', 'LINENO', 'DESCRIPTION',
        'AMT_PERC', 'VALUE', 'APPORTIONED', 'GLACCOUNT', 'GLOFFSETACCOUNT', 'DEBIT_CREDIT', 'BASELINE', 'ISTAX',
        'GLACCOUNTKEY', 'GLACCTID', 'GLOFFSETACCOUNTKEY', 'GLOFFSETACCTID',
        'LOCATIONID', 'LOCATIONNAME', 'DEPARTMENTID', 'DEPARTMENTNAME', 'DEPARTMENT', 'LOCATION', 'ISAVATAX',
        'ENTITYNO', 'ENTITY_NAME',
    ),


    'schema' => array(
        'RECORDNO'                 =>    'record#',
        'DOCPARNO'                =>    'docparkey',
        'DOCPARID'              => 'podocumentparams.docid',
        'DISC_CHARGE'            =>    'disc_charge',
        'LINENO'                =>    'lineno',
        'DESCRIPTION'            =>    'description',
        'AMT_PERC'                =>    'amount_perc',
        'VALUE'                    =>    'value',
        'APPORTIONED'            =>    'apportioned',
        'GLACCOUNT'                =>    'glaccountkey',
        'GLOFFSETACCOUNT'        =>    'gloffsetaccountkey',
        'DEBIT_CREDIT'            =>    'deb_cred',
        'BASELINE'                =>    'baseline',
        'ISTAX'                    =>     'istax',
        'GLACCOUNTKEY' => 'glaccountkey',
        'GLACCTID'                =>    'glaccount.acct_no',
        'GLOFFSETACCOUNTKEY' => 'gloffsetaccountkey',
        'GLOFFSETACCTID'        =>    'offsetglaccount.acct_no',
        'LOCATIONID'            => 'location.location_no',
        'LOCATIONNAME'          => 'location.name',
        'DEPARTMENTID'          => 'dept.dept_no',
        'DEPARTMENTNAME'        => 'dept.title',
        'LOCATIONKEY'           => 'locationkey',
        'DEPTKEY'               => 'deptkey',
        'LOCATION'              => 'location', // overwritten by custom query.
        'DEPARTMENT'            => 'department',      // ""
        'ISAVATAX'                => 'isavatax',
        'ENTITYNO'              =>  'entitykey',
        'ENTITY_NAME'           =>  'entityloc.name',
    ),


    'fieldinfo' => array(
        array (
            'path'        => 'RECORDNO',
            'fullname'    => 'IA.RECORD_NUMBER',
            'desc'        => 'IA.RECORD_NUMBER',
            'type'         => array (
                'type'         => 'integer',
                'ptype'     => 'sequence',
                'size'         => 8,
                'maxlength' => 8
            ),
            'required' => true,
            'hidden' => true,
            'readonly' => true,
            'id' => 1,
        ),
        array (
            'path'        => 'DOCPARNO',
            'fullname'    => 'IA.DOCUMENT_NAME',
            'desc'        => 'IA.DOCUMENT_NAME',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'podocumentparams',
                'size'         => 10
            ),
            'required' => true,
            'id' => 2,
        ),
        array (
            'path'        => 'DISC_CHARGE',
            'fullname'    => 'IA.SUBTOTAL_TYPE',
            'desc'        => 'IA.SUBTOTAL_TYPE',
            'type'         => array (
        // 				'type' 		=> 'enum',
        // 				'ptype' 	=> 'enum',
                'type'         => 'text',
                'ptype'     => 'enum',
                'size'        => 15,
                'validvalues' => array ('','Discount','Charge'),
                '_validivalues' => array ('','DISC','CHARGE'),
                'validlabels' => array ('','IA.DISCOUNT','IA.CHARGE'),
            ),
            'default' => '',
            'required' => true,
            'id' => 3,
        ),
        array (
            'path'        => 'LINENO',
            'fullname'    => 'IA.LINE_NUMBER',
            'desc'        => 'IA.LINE_NUMBER',
            'type'         => array (
                'type'         => 'integer',
                'ptype'     => 'text',
                'maxlength'    => 8,
                'size'         => 8
            ),
            'required' => true,
            'id' => 4,
        ),
        array (
            'path'        => 'DESCRIPTION',
            'fullname'    => 'IA.DESCRIPTION',
            'desc'        => 'IA.DESCRIPTION',
            'required'  => true,
            'type'         => array (
                'type'         => 'text',
                'ptype'     => 'text',
                'maxlength'    => 100,
                'size'         => 25
            ),
            'id' => 5,
        ),
        array (
            'path'        => 'AMT_PERC',
            'fullname'    => 'IA.VALUE_TYPE',
            'desc'        => 'IA.VALUE_TYPE',
            'type'         => array (
        // 				'type' 		=> 'enum',
        // 				'ptype' 	=> 'enum',
                'type'         => 'text',
                'ptype'     => 'enum',
                'size'        => 15,
                'validvalues'     =>    array ('','Amount','Percent'),
                '_validivalues' =>    array ('','A','P'),
                'validlabels'     =>    array ('','IA.AMOUNT','IA.PERCENT'),
            ),
            'default' => '',
            'required' => true,
            'id' => 6,
        ),
        array (
            'path'        => 'VALUE',
            'fullname'    => 'IA.VALUE',
            'desc'        => 'IA.VALUE',
            'type'         => array (
                'type'         => 'decimal',
                'ptype'     => 'decimal',
                'size'        => 12,
                'maxlength'    => 12,
                //'format'	=> $gDecimalFormat
            ),
            'noautodecimal' => true,
            'precision' => 10,
            'id' => 7,
        ),
        array (
            'path'        => 'APPORTIONED',
            'fullname'    => 'IA.APPORTIONED',
            'desc'        => 'IA.APPORTIONED',
            'type'         => $gBooleanType,
            'default'    => 'false',
            'id' => 7,
        ),
        array (
            'path'        => 'GLACCOUNT',
            'fullname'    => 'IA.GL_ACCOUNT',
            'desc'        => 'IA.GL_ACCOUNT',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'glaccount',
                'pickentity' => 'glaccountpick',
                'size'         => 24,
                'maxlength' => 24,
            ),
            'id' => 8,
        ),
        array (
            'path'        => 'GLOFFSETACCOUNT',
            'fullname'    => 'IA.GL_OFFSET_ACCOUNT',
            'desc'        => 'IA.GL_OFFSET_ACCOUNT',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'glaccount',
                'pickentity' => 'glaccountpick',
                'size'         => 24,
                'maxlength' => 24,
            ),
            'id' => 9,
        ),
        array (
            'path'        => 'DEBIT_CREDIT',
            'fullname'    => 'IA.DEBIT_CREDIT',
            'desc'        => 'IA.DEBIT_CREDIT',
            'type'         => array (
                'type'         => 'text',
                'ptype'     => 'enum',
                'size'        => 15,
                'validvalues'     => array ('','Debit','Credit'),
                '_validivalues' => array ('','1','-1'),
                'validlabels'     => array ('','IA.DEBIT','IA.CREDIT'),
            ),
            'default' => '',
            'id' => 10,
        ),
        array (
            'path'        => 'BASELINE',
            'fullname'    => 'IA.APPLIED_TO_LINE',
            'desc'        => 'IA.APPLIED_TO_LINE',
            'type'         => array (
                'ptype'     => 'text',
                'type'         => 'integer',
                'size'         => 8,
                'maxlength' => 8,
            ),
            'id' => 11,
        ),
        array (
            'path'        => 'ISTAX',
            'fullname'    => 'IA.IS_TAX',
            'desc'        => 'IA.IS_TAX',
            'type'         => $gBooleanType,
            'default'    => 'false',
            'id' => 12,
        ),
        array (
            'fullname' => 'IA.DEPARTMENT',
            'desc' => 'IA.DEPARTMENT',
            'type' => array (
                'ptype' => 'ptr',
                'pickentity' => 'departmentpick',
                'entity' => 'department',
                'type' => 'text',
                'maxlength' => 40,
                'size' => 20,
            ),
            'required' => false,
            'path' => 'DEPARTMENT',
            'renameable' => true,
            'id' => 13,
        ),
        array (
            'fullname' => 'IA.LOCATION',
            'desc' => 'IA.LOCATION',
            'type' => array (
                'ptype' => 'ptr',
                'pickentity' => 'locationpick',
                'entity' => 'location',
                'type' => 'text',
                'maxlength' => 40,
                'size' => 20,
            ),
            'required' => false,
            'path' => 'LOCATION',
            'renameable' => true,
            'id' => 14,
        ),
        array (
            'path'        => 'ISAVATAX',
            'fullname' => 'IA.SELECT_FOR_AVALARA',
            'desc'        => 'IA.SELECT_FOR_AVALARA',
            'type'         => $gBooleanType,
            'default' => 'false',
            'id' => 15,
        ),
        array (
            'path' 		=> 'ENTITY_NAME',
            'fullname'	=> 'IA.ENTITY_NAME',
            'desc'	=> 'IA.ENTITY_NAME',
            'type' 		=> array (
                'ptype' 	=> 'ptr',
                'type' 		=> 'ptr',
                'entity' 	=> 'locationentity',
                'pickentity' => 'locationentity',
                'restrict' => array(
                    array(
                        'pickField' => 'LOCATIONTYPE',
                        'value' => 'E'
                    ),
                ),
            ),
            'id' => 16,
            'readonly' => true,
        ),
    ),

    'table' => 'docparsubtotal',
    'parententity' => 'podocumentparams',
    'vid'         => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'dbsorts'         => array (array ('LINENO')),
    'printas'    =>    'IA.DOCUMENT_PARAMS_SUBTOTAL',
    'pluralprintas'    =>    'IA.DOCUMENT_PARAMS_SUBTOTALS',
    'module'     => 'inv'
);
