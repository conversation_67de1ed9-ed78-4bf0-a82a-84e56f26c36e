<?php

/**
 * POApproverPick Manager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Sage Intacct Inc., All Rights Reserved
 */
class POApproverPickManager extends ApproverPickManager
{

    /**
     * POApproverPickManager constructor.
     *
     * @param array $params
     */
    public function __construct(array $params = array())
    {
        parent::__construct($params);
    }

    /**
     * @param string $locationKey
     * @param array $userList
     *
     * @return null
     */
    function getAuthorizedApprovalUsers($locationKey = null, array $userList = [])
    {
        return null;
    }
    public static function GetSystemApprovers()
    {
        $approvers = parent::GetSystemApprovers();
        $approvers[] = ['PICKID' => APPTYPE_USER_LEVEL];
        $approvers[] = ['PICKID' => APPTYPE_USER_GROUP_LEVEL];
        return $approvers;
    }
}