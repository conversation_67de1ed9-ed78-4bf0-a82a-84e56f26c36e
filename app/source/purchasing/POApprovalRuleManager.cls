<?php

/**
 * Created by PhpStorm.
 * User: kghosh
 * Date: 2/13/2017
 * Time: 7:32 AM
 */
class POApprovalRuleManager extends ApprovalRuleManager
{

    /**
     * @param array $params the initialization parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        parent::setModule(Globals::$g->kPOid);
        parent::setMod('po');
    }

    /**
     * Add approval rule
     *
     * @param array $values the transaction data
     *
     * @return bool false if error else true
     */
    protected function regularAdd(&$values)
    {

        if (!$this->prepValues($values)) {
            return false;
        }
        return parent::regularAdd($values);
    }

    /**
     * Set approval rule
     *
     * @param array &$values the data
     *
     * @return bool false if error else true
     */
    protected function regularSet(&$values)
    {
        if (!$this->prepValues($values)) {
            return false;
        }
        return parent::regularSet($values);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function prepValues(array &$values) : bool
    {
        $values['MODULE'] = $this->getModule();

        return $this->prepareRuleDetails($values,$values['RULETYPE']);
    }

    /**
     * @param array $values
     * @param string $ruleType
     *
     * @return bool
     */
    function prepareRuleDetails(&$values,$ruleType) : bool
    {
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $ok = true;
        $entries = $values['RULE_DETAILS'];
        if ($ruleType == 'Permission-based') {
            foreach (($entries ?? []) as $key => $value) {
                if (!isset($value['LEVEL_LABEL'])) {
                    $entries[$key]['LEVEL_LABEL'] = 'Level' . $value['LINE_NO'] + 1;
                }
            }
            $values['DIMTYPE'] = null;
        } else {
            // As of now we only support department level approval
            $values['DIMTYPE'] = APPROVAL_RULE_DEPARTMENT;
            // List named user approvers from entries, for validation.
            $userList = array_filter(array_map(function ($arEntry) {return explode("--", $arEntry['APPROVERUSERID'])[0];}, $entries ?? []));

            // List user group approvers from entries, for validation.
            $userGroupList = array_filter(array_column($entries ?? [],'APPROVERUSERGROUPID'));

            //Validate if user approvers are unique
            $ok = $this->validateDuplicateApprover($userList, $userGroupList);
            if(!$ok){
                return false;
            }

            $userListWithRecordno = [];
            // Get the recordno of the user approvers provided in the rule,
            // this function return array with user login id as key and recordno as value.
            $approverPickMgr = $gManagerFactory->getManager('adminapproverpick');
            if(!empty($userList)){
                $userListWithRecordno = $approverPickMgr->getAuthorizedUsersRecordno($userList);
            }

            // Get the recordno of the user group approvers provided in the policy,
            // this function return array with user group name as key and recordno as value.
            $userGroupListWithRecordno = [];
            if(!empty($userGroupList)){
                $approverGroupPickMgr = $gManagerFactory->getManager('poapprovergrouppick');
                $groupApprovers = $approverGroupPickMgr->getApproverGroup($userGroupList);
                $userGroupListWithRecordno = array_column($groupApprovers?? [],'RECORD#','NAME');
            }

            $count = count($entries);
            for ($i = 0; $i < $count; $i++) {
                $line = $i + 1;
                $approver_type = $entries[$i]['APPROVER'];
                $approver =  $entries[$i]['APPROVERUSERID'];

                //Only validate User Level approvals, rest are validated in parent class
                if($approver_type == APPTYPE_USER_LEVEL) {
                    // make sure we check the loginid parts only
                    list($approvername) = explode('--', $approver);

                    // verify that approver is a valid approver and has transaction approval permission
                    if ($approvername == '') {
                        $msg = "Specify an approver for line " . $line . " of the approval workflow. When the approval rule type is set to userLevel, a user must be provided in the approver field.";
                        $gErr->addIAError('PO-0192', __FILE__ . ':' . __LINE__,  $msg, ['LINEI' => $line]);
                        $ok = false;
                    }
                    elseif (array_key_exists($approvername, $userListWithRecordno ?? [])) {
                        $values['RULE_DETAILS'][$i]['APPROVERUSERKEY'] = $userListWithRecordno[$approvername];
                    }else{
                        $msg = "The user specified as the approver for line " . $line . " is not a valid approver. Specify an unrestricted user who has approval permissions.";
                        $gErr->addIAError('PO-0194', __FILE__ . ':' . __LINE__, $msg, ['LINEI' => $line]);
                        $ok = false;
                    }
                } else if($approver_type == APPTYPE_USER_GROUP_LEVEL) {
                    $approverGroup =  $entries[$i]['APPROVERUSERGROUPID'];

                    // verify that approver is a valid approver and has transaction approval permission
                    if ($approverGroup == '') {
                        $msg = "Specify an approver group for line " . $line . " of the approval workflow. When the approval rule type is set to userGroupLevel, a user group must be provided in the approver group field.";
                        $gErr->addIAError('PO-0205', __FILE__ . ':' . __LINE__,  $msg, ['LINEI' => $line]);
                        $ok = false;
                    }
                    elseif (array_key_exists($approverGroup, $userGroupListWithRecordno ?? [])) {
                        $values['RULE_DETAILS'][$i]['APPROVERUSERGROUPKEY'] = $userGroupListWithRecordno[$approverGroup];
                    }else{
                        $msg = "The user group specified as the approver for line " . $line . " is not a valid approver group.";
                        $gErr->addIAError('PO-0206', __FILE__ . ':' . __LINE__, $msg, ['LINEI' => $line]);
                        $ok = false;
                    }
                }
                //For Department dimension-based rules, MIN_APPROVERS must be 1.
                if($entries[$i]['MIN_APPROVERS'] > 1){
                    $msg = "A value other than 1 was specified for minimum approvers in approval rule line " . $line . ". For department-based value approvals, the minimum approvers must be set to 1.";
                    $gErr->addIAError('PO-0196', __FILE__ . ':' . __LINE__, $msg, ['LINE' => $line]);
                    $ok = false;
                }
            }
        }
        return $ok;
    }

    function validateDuplicateApprover(array $userList, array $userGroupList) : bool
    {
        $gErr = Globals::$g->gErr;
        $ok = true;
        //Validate if user approvers are unique
        if(!empty($userList)){
            $duplicatesUsers = array_diff_assoc($userList, array_unique($userList));
            if (count($duplicatesUsers) > 0) {
                foreach ($duplicatesUsers as $user) {
                    $msg = "".$user." was specified in multiple approval rule lines in a single approval policy. Specify a unique user as the approver for each approval rule line in an approval policy.";
                    $gErr->addIAError('PO-0193', __FILE__ . ':' . __LINE__, $msg, ['APPROVER' => $user]);
                    $ok = false;
                }
            }
        }

        //Validate if user group approvers are unique
        if(!empty($userGroupList)){
            $duplicatesUserGroups = array_diff_assoc($userGroupList, array_unique($userGroupList));
            if (count($duplicatesUserGroups) > 0) {
                foreach ($duplicatesUserGroups as $group) {
                    $msg = "".$group." was specified in multiple approval rule lines in a single approval policy. Specify a unique user as the approver for each approval rule line in an approval policy.";
                    $gErr->addIAError('PO-0209', __FILE__ . ':' . __LINE__, $msg, ['APPROVER' => $group]);
                    $ok = false;
                }
            }
        }
        return $ok;
    }

    /**
     * @param array $entries
     * @param int $i
     * @param int $count
     * @param array $validApproverNames
     * @return bool
     * @throws IAException
     */
    function validateDepartmentBasedRuleDetails(array $entries,int $i,int $count,array $validApproverNames) : bool
    {
        $gErr = Globals::$g->gErr;
        $line = $i + 1;

        // verify that approver field is present
        if (!isset($entries[$i]['APPROVER']) || $entries[$i]['APPROVER'] == '' ) {
            $msg = "You must choose an ruletype for line " . $line . " of the approval workflow.";
            $gErr->addIAError('PO-0008', __FILE__ . ':' . __LINE__, $msg,['LINE' => $line]);
            return false;
        }
        // verify that approvers are unique
        $approver = $entries[$i]['APPROVER'];
        // if approver is a user level or user group level approver then skip the check, as user level and user group level
        // approvers can be repeated type.
        if(!in_array($approver, [APPTYPE_USER_LEVEL,APPTYPE_USER_GROUP_LEVEL])){
            for ($j = $i + 1; $j < $count; $j++) {
                if ($approver == $entries[$j]['APPROVER']) {
                    $msg = "" . $approver . " was specified in multiple approval rule lines in a single approval policy. Specify a unique rule type for each approval rule line in an approval policy.";
                    $gErr->addIAError('PO-0190', __FILE__ . ':' . __LINE__, $msg, ['RULENAME' => $approver]);
                    return false;
                }
            }
        }
        if (!in_array($approver, $validApproverNames ?? [])) {
            $msg = "The approver or ruletype you selected " . $approver . " in line " . $line . " is not valid.";
            $gErr->addIAError('PO-0009', __FILE__ . ':' . __LINE__, '', [], $msg, ['RULENAMEID' => $approver, 'LINEI' => $line]);
            return false;
        }
        return true;
    }
}