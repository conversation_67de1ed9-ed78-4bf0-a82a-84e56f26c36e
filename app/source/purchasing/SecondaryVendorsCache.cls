<?php

/**
 * Caches secondary vendor data
 */
class SecondaryVendorsCache
{
    private static array $SECONDARY_VENDORS_CACHE = [];

    private const DOCPAR_ID = 'DOCPARID';

    private const VALID_DOC = 'VALIDDOC';

    private const PRIMARY_DOC = 'PRIMARYDOC';

    public static function setDocParId(string $docKey, string $docParId)
    {
        self::$SECONDARY_VENDORS_CACHE[$docKey][self::DOCPAR_ID] = $docParId;
    }

    public static function isDocParIdSet(string $docKey) : bool
    {
        return array_key_exists(self::DOCPAR_ID, self::$SECONDARY_VENDORS_CACHE[$docKey] ?? []);
    }

    public static function setValidDoc(string $docKey, bool $isValidDoc)
    {
        self::$SECONDARY_VENDORS_CACHE[$docKey][self::VALID_DOC] = $isValidDoc;
    }

    public static function isValidDocSet(string $docKey) : bool
    {
        return array_key_exists(self::VALID_DOC, self::$SECONDARY_VENDORS_CACHE[$docKey] ?? []);
    }

    public static function setPrimaryDoc(string $docKey, bool $isPrimaryDoc)
    {
        self::$SECONDARY_VENDORS_CACHE[$docKey][self::PRIMARY_DOC] = $isPrimaryDoc;
    }

    public static function isPrimaryDocSet(string $docKey) : bool
    {
        return array_key_exists(self::PRIMARY_DOC, self::$SECONDARY_VENDORS_CACHE[$docKey] ?? []);
    }

    public static function getDocParId(string $docKey) : string
    {
        return self::$SECONDARY_VENDORS_CACHE[$docKey][self::DOCPAR_ID] ?? '';
    }
    public static function isValidDoc(string $docKey) : bool
    {
        return self::$SECONDARY_VENDORS_CACHE[$docKey][self::VALID_DOC] ?? false;
    }

    public static function isPrimaryDoc(string $docKey) : bool
    {
        return self::$SECONDARY_VENDORS_CACHE[$docKey][self::PRIMARY_DOC] ?? false;
    }

    public static function clear()
    {
        self::$SECONDARY_VENDORS_CACHE = [];
    }
}