<?
	if (!$kSchemas['invpricelist']) { include 'invpricelist.ent'; }

	$diffSchema = array (
		'dbfilters'	=> array ( array ( 'popricelist.salepur','<>','S','r'), array ( 'popricelist.salepur', 'ISNULL')),
		'module'	=> 'po'
	);

	$kSchemas['popricelist'] = EntityManager::inheritEnts($kSchemas['invpricelist'],$diffSchema);
	$kSchemas['popricelist']['printas'] = 'IA.PO_PRICE_LIST';
	$kSchemas['popricelist']['pluralprintas'] = 'IA.PO_PRICE_LISTS';
	$kSchemas['popricelist']['module'] = 'po';
    $kSchemas['popricelist']['nochatter'] = true;
    $kSchemas['popricelist']['audittrail_log_reread'] = false;   // Don't complain about audit trail re-reads.
