<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" 
	xmlns:xsl="http://www.w3.org/1999/XSL/Transform" 
	xmlns:fo="http://www.w3.org/1999/XSL/Format" 
	xmlns:svg="http://www.w3.org/2000/svg"
	xmlns:fox="http://xml.apache.org/fop/extensions">

<xsl:variable name="myfont">Helvetica</xsl:variable>
<xsl:variable name="backgroundcolor">#FFFFFF</xsl:variable>

<xsl:template name="otherbody">

		<fo:block>
			<fo:table table-top="7.5cm" border-color="black" border-style="solid" border-width="0.1pt" >
			<fo:table-column column-width="1.9in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>
			<fo:table-column column-width="2.9in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>
			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>
			<fo:table-column column-width="1.7in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>

				<fo:table-header font-family="sans-serif" font-weight="normal" font-size="8pt">
					<fo:table-row line-height="18pt" background-color="#BBBBBB">
						<xsl:call-template name="columnheader_item"/>
						<xsl:call-template name="columnheader_description"/>
						<xsl:call-template name="columnheader_unit"/>
						<xsl:call-template name="columnheader_qty"/>
					</fo:table-row>
				</fo:table-header>

				<fo:table-body font-family="sans-serif" font-weight="normal" font-size="8pt">
					<xsl:apply-templates select="//REC/ENTRIES" mode="withoutamounts"/>
				</fo:table-body>
			</fo:table>
		</fo:block>

	<xsl:call-template name="message"/>

</xsl:template>

<xsl:template name="listbody">

		<fo:block>
			<fo:table table-layout="fixed" table-top="7.5cm" border-color="black" border-style="solid" border-width="0.1pt" >

			<fo:table-column column-width="1.9in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>
			<fo:table-column column-width="2.9in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>
			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>
			<fo:table-column column-width="1.7in" border-color="black" border-style="solid" border-left-width="0.0pt" border-right-width="0.1pt" border-bottom-width="0.0pt" border-top-width="0.0pt"/>

				<fo:table-header font-family="sans-serif" font-weight="normal" font-size="8pt">
					<fo:table-row line-height="18pt" background-color="#BBBBBB">
						<xsl:call-template name="columnheader_item"/>
						<xsl:call-template name="columnheader_description"/>
						<xsl:call-template name="columnheader_unit"/>
						<xsl:call-template name="columnheader_qtyshipped"/>
					</fo:table-row>
				</fo:table-header>

				<fo:table-body font-family="sans-serif" font-weight="normal" font-size="8pt">
					<xsl:apply-templates select="REC/ENTRIES" mode="withoutamounts"/>
				</fo:table-body>

			</fo:table>
		</fo:block>

	<xsl:call-template name="message"/>

</xsl:template>

<xsl:template name="quotebody">

	<fo:block>
		<fo:table table-layout="fixed"  table-top="7.5cm" border-color="black" border-style="solid" border-width="0.05pt" >
			<fo:table-column column-width="0.2in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<fo:table-column column-width="1.4in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<fo:table-column column-width="2.2in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<fo:table-column column-width="1.2in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<fo:table-column column-width="0.7in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<fo:table-column column-width="0.8in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-width="0.05pt"/>

			<fo:table-header font-family="{$myfont}" font-weight="normal" font-size="8pt">

				<fo:table-row line-height="18pt" background-color="#BBBBBB">
					<xsl:call-template name="columnheader_itemnumber"/>
					<xsl:call-template name="columnheader_item"/>
					<xsl:call-template name="columnheader_description"/>
					<xsl:call-template name="columnheader_unit"/>
					<xsl:call-template name="columnheader_qty"/>
					<xsl:call-template name="columnheader_unitprice"/>
					<xsl:call-template name="columnheader_amount"/>

				</fo:table-row>

			</fo:table-header>

    		<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
			<xsl:apply-templates select="REC/ENTRIES" mode="withamounts"/>
    		</fo:table-body>
		</fo:table>
	<xsl:call-template name="quotesubtotals"/>
	</fo:block>

	<xsl:call-template name="message"/>

</xsl:template>



<xsl:template name="message">

	<xsl:call-template name="genericmessage">
		<xsl:with-param name="themessage" select=".//MESSAGE"/>
	</xsl:call-template>
	<xsl:call-template name="genericmessage">
		<xsl:with-param name="themessage" select=".//FIXED_MESG"/>
	</xsl:call-template>

	<fo:block
		text-align		="end"
		space-before		="20pt"
		space-after.optimum	="3pt"
		line-height		="12pt"
		font-family		="{$myfont}"
		font-size		="10pt"
		white-space-collapse	="false">
		<xsl:text>Approved By: ________________________________</xsl:text>
	</fo:block>	

	<fo:block
			text-align		="center"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="12pt"
			font-weight		="bold"
			font-family		="{$myfont}"
			font-size		="11pt"
			break-before		="page"
			white-space-collapse	="false">
	<xsl:text>TERMS AND CONDITIONS - KODEOS COMMUNICATION</xsl:text>
	</fo:block>

	<fo:block
			text-align		="left"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>All purchase orders (the "Agreement") issued by Kodeos Communications and any of their direct or indirect subsidiaries adopting these terms and conditions on the face of the purchase order, are made expressly subject to these additional terms and conditions:</xsl:text>
	</fo:block>

<fo:block
			text-align		="left"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>1. Specifications. All specifications, drawings and data submitted to Seller by Buyer in connection with this Agreement are hereby incorporated herein and made a part hereof.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>2. Confidentiality. Each party acknowledges that its respective performance of its obligations hereunder may require that it have access to confidential business and proprietary information of the other. Each party agrees on behalf of itself and its officers, directors, employees and agents to use its/their best efforts to prevent either duplication or disclosure of data, plans, specifications, formulae, drawing or any other information whether business or technical, of a confidential nature, which has been furnished directly or in directly, in writing or otherwise to the other. "Confidential information" shall include such information as would be apparent to a reasonable person, familiar with the disclosing party's business and the industry in which it operates, that such information is of a confidential or proprietary nature and that maintenance of its confidentiality would likely be of commercial value to the disclosing party. "Confidential information" shall not include information that is in the public domain prior to its disclosure, becomes part of the public domain through no wrongful act of the receiving party, was in the lawful possession of the receiving party prior to its disclosure to the receiving party or was independently developed by the receiving party.</xsl:text>
</fo:block>

<fo:block
			text-align		="left"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>3. Warranty. Seller warrants to Buyer, Buyer's customers and users of the products manufactured by Buyer incorporating the goods herein described, that:
(a) It has good title to any and all goods supplied hereunder, and said goods will be free and clear of any and all liens and encumbrances.
(b) Any and all goods supplied hereunder will be of merchantable quality.
(c) Any and all goods supplied hereunder shall be fit for the particular use intended, free from defects, whether patent or latent, in material and workmanship, and shall conform to all contract specifications
		and requirements.
(d) Seller shall, in the performance of its obligations hereunder, comply with all, and shall not violate any, applicable federal, state, and local laws and governmental regulations and orders.
(e) The foregoing warranties shall survive acceptance of the goods by Buyer and shall be in addition to any warranties of additional scope given to Buyer by Seller.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>4. Titles and Risk of Loss. Title to the goods and risk of loss thereof, or damage thereto, shall pass to Buyer upon delivery to Buyer. All goods shall be received by Buyer subject to its right of inspection and rejection. Buyer shall be allowed a reasonable period of time to inspect the goods and to notify Seller of any non-conformance with the terms and conditions of this Agreement. Buyer may reject any goods, which do not conform to the terms and conditions of this order. Goods so rejected may be returned to Seller or held by Buyer for pick-up by Seller, all at Seller's expense.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>5. Transportation. No charge will be made to Buyer for storage or packing unless specified. Carrier and route used must result in lowest rate possible consistent with service rendered except when otherwise specified by Buyer, and penalties or increased charges due to failure to do so will be charged to Seller. Shipping instructions, if any, are made part of this Agreement.</xsl:text>
</fo:block>

<fo:block
			text-align		="left"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>6. Indemnification. Seller shall defend, indemnify and save harmless Buyer, its officers, directors, successors, assigns, employees, agents, customers and users of its products incorporating the goods herein, of and from any claim, loss, damage or expense (including reasonable attorney's fees), including any incidental or consequential damages, directly or indirectly arising out of
(a) Any infringement or claim of infringement of any letters patent or trade secrets by reason of the use or sale of goods purchased hereunder, excepting unpatented staple articles of commerce and goods
		manufactured in accordance with Buyer's design;
(b) Injury to persons or property by reason of any defects in the goods, or breach by Seller of any of its warranties, or Seller's failure timely to deliver the goods purchased hereunder; or
(c) Any noncompliance or violation of law as provided in paragraph 3(d), above. Seller shall at its own expense, if so requested by Buyer, defend all claims, proceedings or suits against Buyer, its 
		successors, assigns, employees, customers and users of its products, in which any of the aforesaid claims are alleged, provided Seller is duly notified of such claims, proceedings or suits.
		If, in any such suit, said goods are held to constitute an infringement of any letters patent or trade secrets and use thereof is enjoined, Seller shall, at Buyer's election, either (1) procure for 
		Buyer the right to continue using the goods, or (2) replace the same with no infringing apparatus, or (3) modify the same so that it becomes no infringing.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>7. Ownership. Data, drawings, specifications or other technical information furnished directly or indirectly, in writing or otherwise, to Seller by Buyer pursuant to this Agreement shall in no event become the property of Seller and shall be used only in fulfilling the obligations imposed by this Agreement and for no other purpose and shall not be duplicated or disclosed to others. Such furnishing of data, drawings, specifications, or technical information shall not be construed as granting any rights whatsoever, express or implied, under any patents of Buyer.</xsl:text>
</fo:block>

<fo:block
			text-align		="left"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>8. Price. Seller will give Buyer the benefit of any price reductions available or in effect at the actual time of shipment.</xsl:text>
</fo:block>

<fo:block
			text-align		="left"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>9. Changes. Buyer reserves the right at any time to make changes in the following:
(a) specifications, drawings and data incorporated in this order where the goods to be furnished are to be specially manufactured for Buyer; 
(b) methods of shipment or packing; 
(c) place of delivery; and 
(d) time of delivery. If any such change causes an increase or decrease in the cost of or the time required for performance of this Agreement, an equitable adjustment shall be made in the purchase price 
		or delivery schedule, or both. If the parties cannot agree to such price or time adjustment within ten (10) business days (or such other time as may then be agreeable to both parties), of Seller's 
		receipt of Buyer's request for a change, either party may terminate this Agreement upon five (5) business days prior notice to the other. Any changes, if agreeable to Buyer, whether initiated by 
		Seller or Buyer, shall be denominated as a "revision" to this Agreement. Only Buyer shall issue revisions and, if issued, shall be numbered serially, and each such revision shall be further subject
		to these terms and conditions.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>10. Termination. Buyer may at any time terminate this Agreement, in whole or in part, upon one business day's prior notice to Seller. If this contract is terminated by Buyer for any reason other than for breach by Seller, any Seller must notify Buyer of any claim resulting from Buyer's termination within ninety (90) days of the effective date of termination. Such claim shall be settled on the basis of the reasonable costs Seller has incurred in the performance of this contract prior to receipt of Buyer's notice of termination.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>11. Non-Waiver. The failure by either party to pursue any remedy hereunder shall not constitute a waiver on its part to pursue such remedy with respect to the same or similar breach.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>12. Entire Agreement. This Agreement constitutes the entire Agreement between the parties with respect to the subject matter hereof and supersedes and replaces all prior Agreements, understandings, and representations, whether written or oral.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>13. Governing Law. This Agreement shall be construed in accordance with the laws of the State of New Jersey without regard to the conflicts provisions thereof. Venue shall be proper in the state courts of Middlesex County or the federal district court for Central New Jersey.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>14. Severability. If any provision of this Agreement shall be held invalid or unenforceable to any extent, the remainder of the Agreement shall not be affected thereby and shall be enforced to the greatest extent permitted by law.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>15. Relationship of the Parties. The relationship of the parties is solely that of buyer and seller and nothing contained in this Agreement shall be construed as creating any agency, partnership, joint venture or employment relationship.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>16. Agreement to Govern. Any purchases by Buyer hereunder shall be governed by this Agreement and this Agreement shall prevail over any contrary or inconsistent terms contained in any order, estimate, shipment or invoicing document of either party, unless expressly incorporated herein on the face of this Agreement.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>17. Assignment. Neither party may assign this Agreement in whole or in part without the prior written consent of the other, which consent shall not be unreasonably withheld.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>18. Force Majeure. Performance by either party hereunder shall be excused in the event and for the period of time that such party is unable to perform its obligations because of strikes or other labor difficulties, labor shortage, fire, flood, war, breakdowns, delays in or lack of transportation, governmental priorities or allocation, or any other cause beyond the reasonable control of such party.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>19 Notices. Notices shall be sent by a nationally recognized overnight courier service to the addresses specified on the face hereof and shall be effective one day after dispatch.</xsl:text>
</fo:block>

<fo:block
			text-align		="justify"
			text-indent		="-3em"
			space-before		="5pt"
			space-after.optimum	="3pt"
			line-height		="7pt"
			font-family		="{$myfont}"
			font-size		="6pt"
			white-space-collapse	="false">
	<xsl:text>20 Legal Effect. The parties acknowledge and agree that transmission of this Agreement or any revision thereof by fax, which contains a typed name of Buyer, and or Buyer's logo shall satisfy the requirements of both a "writing" and a "signature" for purposes Ill. Rev. Code 2-201.</xsl:text>
</fo:block>



</xsl:template>

<xsl:template name="genericmessage">
	<xsl:param name="themessage"/>
	<fo:block
		text-align			="start"
		space-before		="8pt"
		space-after.optimum	="3pt"
		line-height			="12pt"
		font-family			="{$myfont}"
		font-size			="10pt"
		white-space-collapse = "false">

		<xsl:value-of select="$themessage"/>
	</fo:block>

</xsl:template>



<xsl:template name="quotesubtotals">
	<fo:table table-layout="fixed">
		<fo:table-column column-width="5in"   border-color="white" border-style="solid"  border-bottom-width="0.01pt" border-top-width="0.000pt" border-left-width="0.01pt" border-right-width="0.01pt"/>
		<fo:table-column column-width="1.5in" border-color="black" border-style="solid"  border-bottom-width="0.01pt" border-top-width="0.000pt" border-left-width="0.01pt" border-right-width="0.01pt"/>
		<fo:table-column column-width="1.0in" border-color="black" border-style="solid"  border-bottom-width="0.01pt" border-top-width="0.000pt" border-left-width="0.01pt" border-right-width="0.01pt"/>

		<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
			<xsl:apply-templates select="REC/SUBTOTALS"/>
		</fo:table-body>
	</fo:table>
</xsl:template>

<xsl:template name="genericsubtotals" match="SUBTOTALS">
		<xsl:choose>
			<xsl:when test="position()=1">
				<fo:table-row line-height="18pt">
					<fo:table-cell padding="6pt">
						<fo:block text-align="end"/>
					</fo:table-cell>
					<fo:table-cell padding="4pt">
						<fo:block text-align="left">
							<xsl:value-of select="child::DESCRIPTION"/>
						</fo:block>
					</fo:table-cell>
					<fo:table-cell padding="4pt">
						<fo:block text-align="end">
							<xsl:call-template name="zeropadwithdollar">
								<xsl:with-param name="data" select="child::TOTAL"/>
							</xsl:call-template>
							<fo:inline color="white">
								<xsl:text> T</xsl:text>
							</fo:inline>
							<xsl:text>  </xsl:text>
						</fo:block>
					</fo:table-cell>
				</fo:table-row>
			</xsl:when>
			<xsl:when test="child::ABSVAL=0"/>
			<xsl:when test="position()=last()">
				<fo:table-row line-height="18pt">
					<fo:table-cell padding="6pt">
						<fo:block text-align="end"/>
					</fo:table-cell>
					<fo:table-cell border-width="0.05pt" border-color="black" border-style="solid" font-weight="bold" background-color="{$backgroundcolor}" padding="5pt">
						<fo:block text-align="left">
							<xsl:value-of select="child::DESCRIPTION"/>
						</fo:block>
					</fo:table-cell>
					<fo:table-cell border-width="0.05pt" border-color="black" border-style="solid" font-weight="bold" background-color="{$backgroundcolor}" padding="5pt">
						<fo:block text-align="end">
							<xsl:call-template name="zeropadwithdollar">
								<xsl:with-param name="data" select="child::TOTAL"/>
							</xsl:call-template>
							<fo:inline color="{$backgroundcolor}">
								<xsl:text> T</xsl:text>
							</fo:inline>
						</fo:block>
					</fo:table-cell>
				</fo:table-row>
			</xsl:when>
			<xsl:otherwise>
				<fo:table-row line-height="18pt">
					<fo:table-cell padding="6pt">
						<fo:block text-align="end"/>
					</fo:table-cell>
					<fo:table-cell padding="4pt">
						<fo:block text-align="left">
							<xsl:choose>
								<xsl:when test="child::PERCENTVAL != 0">
									<xsl:value-of select="translate(child::DESCRIPTION,'%',' ' )"/>(  <xsl:value-of select="child::PERCENTVAL"/>%)
                                                              </xsl:when>
								<xsl:otherwise>
									<xsl:value-of select="child::DESCRIPTION"/>
								</xsl:otherwise>
							</xsl:choose>
						</fo:block>
					</fo:table-cell>
					<fo:table-cell padding="6pt">
						<fo:block text-align="end">
							<xsl:call-template name="zeropadwithdollar">
								<xsl:with-param name="data" select="child::ABSVAL"/>
							</xsl:call-template>
							<fo:inline color="white">
								<xsl:text> T</xsl:text>
							</fo:inline>
						</fo:block>
					</fo:table-cell>
				</fo:table-row>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>



<xsl:template match="ENTRIES" mode="withoutamounts">

	<fo:table-row line-height="10pt">
		
		<xsl:call-template name="tableentry_lineitem">
			<xsl:with-param name="data" select="child::LINE_NO"/>
		</xsl:call-template>

		<xsl:call-template name="tableentry_item">
			<xsl:with-param name="data" select="child::ITEMID"/>
		</xsl:call-template>

		<xsl:call-template name="tableentry_itemdesc">
			<xsl:with-param name="data" select="child::ITEMDESC"/>
			<xsl:with-param name="data2" select="child::EXTENDED_DESCRIPTION"/>
			<xsl:with-param name="data4" select="child::STOCK_NUMBER"/>
		</xsl:call-template>

		<xsl:call-template name="tableentry_unit">
			<xsl:with-param name="data" select="child::UNIT"/>
		</xsl:call-template>

		<xsl:call-template name="tableentry_quantity">
			<xsl:with-param name="data" select="child::UIQTY"/>
		</xsl:call-template>

	</fo:table-row>

</xsl:template>


<xsl:template match="ENTRIES" mode="withamounts">

		<xsl:variable name="theprice">
			<xsl:call-template name="zeropadwithdollarandprecision">
				<xsl:with-param name="data" select="child::UIPRICE"/>
			</xsl:call-template>
		</xsl:variable>

		<xsl:variable name="thecalculatedvalue">
			<xsl:call-template name="zeropadwithdollar">
				<xsl:with-param name="data" select="child::UIPRICE * child::UIQTY"/>
			</xsl:call-template>
		</xsl:variable>

		<fo:table-row line-height="10pt">

			<xsl:call-template name="tableentry_lineitem">
				<xsl:with-param name="data" select="child::LINE_NO"/>
			</xsl:call-template>

			<xsl:call-template name="tableentry_item">
				<xsl:with-param name="data" select="child::ITEMID"/>
			</xsl:call-template>

			<xsl:call-template name="tableentry_itemdesc">
				<xsl:with-param name="data" select="child::ITEMDESC"/>
				<xsl:with-param name="data2" select="child::EXTENDED_DESCRIPTION"/>
				<xsl:with-param name="data3" select="child::MEMO"/>
				<xsl:with-param name="data4" select="child::STOCK_NUMBER"/>
			</xsl:call-template>

			<xsl:call-template name="tableentry_unit">
				<xsl:with-param name="data" select="child::UNIT"/>
			</xsl:call-template>

			<xsl:call-template name="tableentry_quantity">
				<xsl:with-param name="data" select="child::UIQTY"/>
			</xsl:call-template>

			<xsl:call-template name="tableentry_price">
				<xsl:with-param name="data" select="$theprice"/>
			</xsl:call-template>

			<xsl:call-template name="tableentry_value">
				<xsl:with-param name="data" select="$thecalculatedvalue"/>
			</xsl:call-template>

		</fo:table-row>

</xsl:template>

<xsl:template name="footer">
	<xsl:param name="col1"/>
	<xsl:param name="col2"/>
	<xsl:param name="col3"/>
	<xsl:param name="col4"/>
	<xsl:param name="col5"/>
	<xsl:param name="col6"/>
	<xsl:param name="pagenum"/>

	<xsl:call-template name="footerline"/>

	<fo:block-container
		height		="0.25in"
		width		="7.5in"
		top			="0.20in"
		left		="0.0in"
		position	="absolute">
		<fo:table
			table-layout="fixed"
			height="0.25in">

			<fo:table-column column-width="6.25in"/>
			<fo:table-column column-width="1.25in"/>

			<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
				<fo:table-row line-height="11pt">
					<fo:table-cell>
						<fo:block>
							<xsl:call-template name="genericfootercolumn">
								<xsl:with-param name="data" select="$col1"/>
							</xsl:call-template>
							<xsl:call-template name="genericfootercolumn">
								<xsl:with-param name="data" select="$col2"/>
							</xsl:call-template>
							<xsl:call-template name="genericfootercolumn">
								<xsl:with-param name="data" select="$col3"/>
							</xsl:call-template>
							<xsl:call-template name="genericfootercolumn">
								<xsl:with-param name="data" select="$col4"/>
							</xsl:call-template>
							<xsl:call-template name="genericfootercolumn">
								<xsl:with-param name="data" select="$col5"/>
							</xsl:call-template>
							<xsl:call-template name="genericfootercolumn">
								<xsl:with-param name="data" select="$col6"/>
							</xsl:call-template>
						</fo:block>
					</fo:table-cell>
					<fo:table-cell>
						<fo:block text-align="end">
							<xsl:call-template name="genericfootercolumn">
								<xsl:with-param name="data" select="$pagenum"/>
							</xsl:call-template>
						</fo:block>
					</fo:table-cell>
				</fo:table-row>
			</fo:table-body>

		</fo:table>
	</fo:block-container>
</xsl:template>

<xsl:template name="genericfootercolumn">
	<xsl:param name="data"/>
	<xsl:if test="$data=''">
		<xsl:call-template name="footerblank"/>
	</xsl:if>
	<xsl:if test="$data='footerorderdate'">
		<xsl:call-template name="footerorderdate"/>
	</xsl:if>
	<xsl:if test="$data='footerterms'">
		<xsl:call-template name="footerterms"/>
	</xsl:if>
	<xsl:if test="$data='footerduedate'">
		<xsl:call-template name="footerduedate"/>
	</xsl:if>
	<xsl:if test="$data='footershipdate'">
		<xsl:call-template name="footershipdate"/>
	</xsl:if>
	<xsl:if test="$data='footershipvia'">
		<xsl:call-template name="footershipvia"/>
	</xsl:if>
	<xsl:if test="$data='footerpage'">
		<xsl:call-template name="footerpage"/>
	</xsl:if>
	<xsl:if test="$data='footerinvoicedate'">
		<xsl:call-template name="footerinvoicedate"/>
	</xsl:if>
	<xsl:if test="$data='footerquotedate'">
		<xsl:call-template name="footerquotedate"/>
	</xsl:if>
	<xsl:if test="$data='footervaliduptodate'">
		<xsl:call-template name="footervaliduptodate"/>
	</xsl:if>
	<xsl:if test="$data='footerexpirationdate'">
		<xsl:call-template name="footerexpirationdate"/>
	</xsl:if>
	<xsl:if test="$data='footerdate'">
		<xsl:call-template name="footerdate"/>
	</xsl:if>
</xsl:template>

<xsl:template name="zeropad">
	<xsl:param name="data"/>
	<xsl:value-of select="format-number($data,'#,###,##0.00')"/>
</xsl:template>

<xsl:template name="oldzeropad">
	<xsl:param name="data"/>
	<xsl:choose>
		<xsl:when test="contains($data,'.')">
			<xsl:value-of select="$data"/>
		</xsl:when>
		<xsl:otherwise>
			<xsl:value-of select="concat($data,'.00')"/>
		</xsl:otherwise>
	</xsl:choose>
</xsl:template>

<xsl:template name="zeropadwithdollar">
	<xsl:param name="data"/>
	<xsl:text>$</xsl:text>
	<xsl:call-template name="zeropad">
		<xsl:with-param name="data" select="$data"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="zeropadwithdollarandprecision">
	<xsl:param name="data"/>
	<xsl:text>$</xsl:text>
	<xsl:value-of select="$data"/>
</xsl:template>

<xsl:template name="generictableentry">
	<xsl:param name="data"/>
	<xsl:param name="data2"/>
	<xsl:param name="data3"/>
	<xsl:param name="data4"/>
	<xsl:param name="checktax"/>
	<xsl:param name="position"/>
	<fo:table-cell padding="6pt">

		<fo:block text-align="{$position}">
			<xsl:value-of select="$data"/>
			<xsl:if test="$checktax='true'">
				<xsl:choose>
					<xsl:when test="child::ITEM/TAXABLE='true' ">
						<xsl:text> T</xsl:text>
					</xsl:when>
					<xsl:otherwise>
						<fo:inline color="white">
							<xsl:text> X</xsl:text>
						</fo:inline>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:if>

		</fo:block>
		<fo:block
			text-align		="{$position}"
			start-indent	=".25in"
			font-size		="7pt">
			<xsl:value-of select="$data2"/>

		</fo:block>
		<fo:block
			text-align		="{$position}"
			start-indent	=".25in"
			font-size		="7pt">
			<xsl:value-of select="$data3"/>

		</fo:block>
		<xsl:if test="$data4 != ''">
			<fo:block
				text-align		="{$position}"
				start-indent	=".25in"
				font-size		="7pt">
				<xsl:text>Stock#: </xsl:text>
				<xsl:value-of select="$data4"/>

			</fo:block>
		</xsl:if>
	</fo:table-cell>
</xsl:template>

<xsl:template name="generictableentry_center">
	<xsl:param name="data"/>
	<xsl:param name="data2"/>
	<xsl:param name="checktax"/>
	<xsl:call-template name="generictableentry">
		<xsl:with-param name="data" 	select="$data"/>
		<xsl:with-param name="data2" 	select="$data2"/>
		<xsl:with-param name="checktax"	select="$checktax"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="generictableentry_start">
	<xsl:param name="data"/>
	<xsl:param name="data2"/>
	<xsl:param name="data3"/>
	<xsl:param name="data4"/>
	<xsl:param name="checktax"/>
	<xsl:call-template name="generictableentry">
		<xsl:with-param name="data" 	select="$data"/>
		<xsl:with-param name="data2" 	select="$data2"/>
		<xsl:with-param name="data3" 	select="$data3"/>
		<xsl:with-param name="data4" 	select="$data4"/>
		<xsl:with-param name="checktax"	select="$checktax"/>
		<xsl:with-param name="position" select="'start'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="generictableentry_end">
	<xsl:param name="data"/>
	<xsl:param name="data2"/>
	<xsl:param name="checktax"/>
	<xsl:call-template name="generictableentry">
		<xsl:with-param name="data" 	select="$data"/>
		<xsl:with-param name="data2" 	select="$data2"/>
		<xsl:with-param name="checktax"	select="$checktax"/>
		<xsl:with-param name="position" select="'end'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="tableentry_lineitem">
	<xsl:param name="data"/>
	<xsl:call-template name="generictableentry_center">
		<xsl:with-param name="data" 	select="$data + 1"/>
		<xsl:with-param name="width" 	select="'10'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="tableentry_item">
	<xsl:param name="data"/>
	<xsl:call-template name="generictableentry_start">
		<xsl:with-param name="data" 	select="$data"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="tableentry_itemdesc">
	<xsl:param name="data"/>
	<xsl:param name="data2"/>
	<xsl:param name="data3"/>
	<xsl:param name="data4"/>
	<xsl:call-template name="generictableentry_start">
		<xsl:with-param name="data" 	select="$data"/>
		<xsl:with-param name="data2" 	select="$data2"/>
		<xsl:with-param name="data3" 	select="$data3"/>
		<xsl:with-param name="data4" 	select="$data4"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="tableentry_unit">
	<xsl:param name="data"/>
	<xsl:call-template name="generictableentry_center">
		<xsl:with-param name="data" 	select="$data"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="tableentry_quantity">
	<xsl:param name="data"/>
	<xsl:call-template name="generictableentry_end">
		<xsl:with-param name="data" 	select="$data"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="tableentry_price">
	<xsl:param name="data"/>
	<xsl:call-template name="generictableentry_end">
		<xsl:with-param name="data" 	select="$data"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="tableentry_value">
	<xsl:param name="data"/>
	<xsl:call-template name="generictableentry_end">
		<xsl:with-param name="data" 	select="$data"/>
		<xsl:with-param name="checktax" select="'true'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="topspacer">
	<fo:block-container
		border-color		="white"
		border-style		="solid"
		border-width		="0.05pt"
		height				="0.2cm"
		width				="4cm"
		top					="7cm"
		left				="15cm"
		padding				="2pt"
		position="absolute">
		<fo:block
			text-align			="start"
			space-after.optimum	="3pt"
			line-height			="10pt"
			font-family			="{$myfont}"
			font-size="10pt"/>
	</fo:block-container>
</xsl:template>


<xsl:template name="columnheader_item">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="'Item'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_description">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="'Description'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_unit">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="'Unit'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_qtyshipped">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="'Quantity Shipped'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_qty">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="'Quantity'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_unitprice">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="'Unit Price'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_amount">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="'Amount'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_itemnumber">
	<xsl:call-template name="genericcolumnheader">
		<xsl:with-param name="label" 	select="''"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="genericcolumnheader">

	<xsl:param name="label"/>
	<xsl:param name="position"/>

  	<fo:table-cell
  		border-width	="0.05pt"
		border-style	="solid"			
		padding		="6pt">


		<fo:block
			text-align="{$position}">
			<xsl:value-of select="$label"/>
		</fo:block>
	</fo:table-cell>

</xsl:template>

<xsl:template name="topnotesspacer">
	<fo:block
		space-before		="1.0cm"
		text-align			="start"
		space-after.optimum	="3pt"
		font-family			="{$myfont}"
		font-size			="10pt"
		border-color		="white"
		border-style		="solid"
		border-width		="1pt"
		height				="0.5cm"
		width				="4cm"
		top					="6.5cm"
		left				="0cm"
		padding				="2pt"
		position			="absolute">
	</fo:block>
</xsl:template>

<xsl:template name="topnotes">

	<xsl:param name="label0"/>
	<xsl:param name="data0"/>
	<xsl:param name="label1"/>
	<xsl:param name="data1"/>
	<xsl:param name="label2"/>
	<xsl:param name="data2"/>

	<xsl:call-template name="topnotesspacer"/>


	<fo:block space-before="0.5cm"  border-color="white">
	<fo:table
		table-layout="fixed"
		border-color	="white"
		border-style	="solid"
		border-width	="0.005pt"
		height		="0.1cm">
		<fo:table-column column-width="6.0cm"/>
		<fo:table-column column-width="6.0cm"/>
		<fo:table-column column-width="7.0cm"/>
		<fo:table-body
			font-family		="{$myfont}"
			font-weight		="normal"
			font-size		="10pt">

			<fo:table-row
				>

				<xsl:call-template name="topnotescell">
					<xsl:with-param name="label" 	select="$label0"/>
					<xsl:with-param name="data" 	select="$data0"/>
					<xsl:with-param name="location" select="'start'"/>
				</xsl:call-template>

				<xsl:call-template name="topnotescell">
					<xsl:with-param name="label" select="$label1"/>
					<xsl:with-param name="data" select="$data1"/>
					<xsl:with-param name="location" select="'center'"/>
				</xsl:call-template>

				<xsl:call-template name="topnotescell">
					<xsl:with-param name="label" select="$label2"/>
					<xsl:with-param name="data" select="$data2"/>
					<xsl:with-param name="location" select="'end'"/>
				</xsl:call-template>

			</fo:table-row>
		</fo:table-body>
	</fo:table>
	</fo:block>

	<xsl:call-template name="topspacer"/>

</xsl:template>

<xsl:template name="topnotescell">
	<xsl:param name="label"/>
	<xsl:param name="data"/>
	<xsl:param name="location"/>

	<fo:table-cell
		padding			="6pt"
		>
		<fo:block
			space-before		="0cm"
			text-align			="{$location}"
			space-after.optimum	="3pt"
			line-height			="12pt"
			font-family			="{$myfont}"
			font-size			="10pt"
			border-color		="white"
			border-style		="solid"
			border-width		="1pt"
			height				="0.5cm"
			width				="4cm"
			top					="6.5cm"
			left				="0cm"
			padding				="2pt"
			position			="absolute">

			<xsl:value-of select="$label"/>
			<xsl:if test="$label!=''">
				<xsl:text> : </xsl:text>
			</xsl:if>
			<xsl:value-of select="$data"/>

		</fo:block>
	</fo:table-cell>
</xsl:template>



<xsl:template name="pagemaster">
	<xsl:variable name="pgwidth">8.5in</xsl:variable>
	<xsl:variable name="pgheight">11in</xsl:variable>

    <fo:layout-master-set>
        <fo:simple-page-master
			master-name			="first"
			margin-right		="1.2cm"
			margin-left			="1.2cm"
			margin-bottom		="0.5cm"
			margin-top			="1.0cm"
			page-width			="{$pgwidth}"
			page-height			="{$pgheight}">
			<fo:region-body
				overflow			="auto"
				margin-top			="0.20in"
				margin-bottom		="0.60in"/>
			<fo:region-before extent="1.00in"/>
			<fo:region-after extent="0.60in"/>
        </fo:simple-page-master>


		<fo:simple-page-master
			master-name			="rest"
			margin-right		="1.2cm"
			margin-left			="1.2cm"
			margin-bottom		="0.5cm"
			margin-top			="1.0cm"
			page-width			="{$pgwidth}"
			page-height			="{$pgheight}">
			<fo:region-body
				overflow			="auto"
				margin-top			="0.20in"
				margin-bottom		="0.60in"/>
			<fo:region-before extent="1.00in"/>
			<fo:region-after extent="0.60in"/>
        </fo:simple-page-master>

		<fo:page-sequence-master master-name="psmA">
			<fo:repeatable-page-master-alternatives>
				<fo:conditional-page-master-reference master-reference="first"
					page-position="first" />
				<fo:conditional-page-master-reference master-reference="rest"
					page-position="rest" />
				<!-- recommended fallback procedure -->
				<fo:conditional-page-master-reference master-reference="rest" />
			</fo:repeatable-page-master-alternatives>
		</fo:page-sequence-master>
    </fo:layout-master-set>
</xsl:template>

<xsl:template name="documentlabel">

   <xsl:variable name="printtitle">
	   <xsl:choose>
		<xsl:when test="REC/_DOCPAR/PRINTTITLE !=''">
			<xsl:value-of select="REC/_DOCPAR/PRINTTITLE"/>
		</xsl:when>
		<xsl:otherwise>
			<xsl:value-of select="REC/_DOCPAR/SEQUENCE"/>
		</xsl:otherwise>
	   </xsl:choose>
   </xsl:variable>

   <xsl:variable name="sizeoffset">
           <xsl:choose>
		<xsl:when test="string-length($printtitle) > 20">
			<xsl:value-of select="(string-length($printtitle) - 20) * 0.125"/>
		</xsl:when>
		<xsl:otherwise>0</xsl:otherwise>
	   </xsl:choose>
   </xsl:variable>


   <fo:block-container
   		height		="0.61in"
		width		="{2.6 + $sizeoffset}in"
		top		="0in"
		left		="{4.25 - $sizeoffset}in"
		position	="absolute">

	<fo:block>
		<fo:instream-foreign-object>
			<svg:svg
				width		="{2.6 + $sizeoffset}in"
				height		="0.61in">

				<svg:rect
					x	="0.15in"
					y	="0in"
					width	="{2.3 + $sizeoffset}in"
					height	="0.3in"
					style	="fill: #000000"/>

				<svg:circle
					cx	="0.15in"
					cy	="0.15in"
					r	="0.15in"
					style	="fill: #000000"/>

				<svg:circle
					cx	="{2.45 + $sizeoffset}in"
					cy	="0.15in"
					r	="0.15in"
					style	="fill: #000000"/>

			</svg:svg>
		</fo:instream-foreign-object>
	</fo:block>
   </fo:block-container>

   <fo:block-container
   		height		="0.61in"
		width		="{2.3 + $sizeoffset}in"
		top		=".08in"
	        left		="{4.4 - $sizeoffset}in"
		position	="absolute">

   		<fo:block
			text-align		="center"
			color			="white"
     			font-family		="Times Roman"
			font-size		="16pt"
			font-weight		="bold">
       			<xsl:value-of select="$printtitle"/>

   		</fo:block>
 	</fo:block-container>
</xsl:template>

<xsl:template name="genericbillshipaddress">
	<xsl:param name="billorship"/>
	<xsl:param name="printas"/>
	<xsl:param name="address1"/>
	<xsl:param name="address2"/>
	<xsl:param name="city"/>
	<xsl:param name="state"/>
	<xsl:param name="zip"/>
	<xsl:param name="country"/>
	<xsl:param name="phone1"/>
	<xsl:param name="email1"/>
	<xsl:param name="firstname"/>
	<xsl:param name="lastname"/>
	<xsl:param name="fax"/>
	<xsl:param name="prefix"/>
		<fo:inline start-indent="0.42in">
		<fo:block
			text-align		="start"
			font-weight		="bold" >
			<xsl:value-of select="$billorship"/>
			<xsl:text> : </xsl:text>
		</fo:block>
		<fo:block
			text-align		="start"
			font-weight		="bold" >
			<xsl:value-of select="$printas"/>
		</fo:block>
		<fo:block text-align		="start" >
			<xsl:value-of select="$prefix"/><xsl:text> </xsl:text><xsl:value-of select="$firstname"/><xsl:text> </xsl:text><xsl:value-of select="$lastname"/>
		</fo:block>
		<fo:block
			text-align		="start" >
			<xsl:value-of select="$address1"/>
		</fo:block>
		<fo:block
			text-align		="start">
			<xsl:value-of select="$address2"/>
		</fo:block>
		<fo:block text-align		="start" >
			<xsl:value-of select="$city"/>
			<xsl:if test="$state != '' and $city != ''">
				<xsl:text>,  </xsl:text>
			</xsl:if>
			<xsl:value-of select="$state"/>
			<xsl:text>  </xsl:text>
			<xsl:value-of select="$zip"/>

		</fo:block>
		<fo:block text-align		="start" >
				<xsl:value-of select="$country"/>
		</fo:block>
		<xsl:if test="$phone1 != ''">
			<fo:block text-align		="start" >
				<xsl:text>Tel: </xsl:text><xsl:value-of select="$phone1"/>
			</fo:block>
		</xsl:if>
		<xsl:if test="$fax != ''">
			<fo:block text-align		="start" >
				<xsl:text>Fax: </xsl:text><xsl:value-of select="$fax"/>
			</fo:block>
		</xsl:if>
		<xsl:if test="$email1 != ''">
			<fo:block text-align		="start" >
				<xsl:text>Email: </xsl:text><xsl:value-of select="$email1"/>
			</fo:block>
		</xsl:if>
		</fo:inline>
</xsl:template>

<xsl:template name="genericbilltoaddress">
	<xsl:param name="billorship"/>

	<xsl:call-template name="genericbillshipaddress">
		<xsl:with-param name	="billorship"	select	="$billorship"/>	
		<xsl:with-param name	="printas" 	select	=".//BILLTO/PRINTAS"/>	
		<xsl:with-param name	="address1" 	select	=".//BILLTO/MAILADDRESS/ADDRESS1"/>	
		<xsl:with-param name	="address2" 	select	=".//BILLTO/MAILADDRESS/ADDRESS2"/>	
		<xsl:with-param name	="city" 	select	=".//BILLTO/MAILADDRESS/CITY"/>	
		<xsl:with-param name	="state" 	select	=".//BILLTO/MAILADDRESS/STATE"/>	
		<xsl:with-param name	="zip" 		select	=".//BILLTO/MAILADDRESS/ZIP"/>	
		<xsl:with-param name	="country" 	select	=".//BILLTO/MAILADDRESS/COUNTRY"/>	
		<xsl:with-param name	="phone1" 	select	=".//BILLTO/PHONE1"/>
		<xsl:with-param name	="email1" 	select	=".//BILLTO/EMAIL1"/>
		<xsl:with-param name	="firstname" 	select	=".//BILLTO/FIRSTNAME"/>
		<xsl:with-param name	="lastname" 	select	=".//BILLTO/LASTNAME"/>
		<xsl:with-param name	="fax" 	select	=".//BILLTO/FAX"/>
		<xsl:with-param name	="prefix" 	select	=".//BILLTO/PREFIX"/>
	</xsl:call-template>	
	
</xsl:template>
<xsl:template name="genericshiptoaddress">
	<xsl:param name="billorship"/>

	<xsl:call-template name="genericbillshipaddress">
		<xsl:with-param name	="billorship"	select	="$billorship"/>
		<xsl:with-param name	="printas" 	select	=".//SHIPTO/PRINTAS"/>	
		<xsl:with-param name	="address1" 	select	=".//SHIPTO/MAILADDRESS/ADDRESS1"/>	
		<xsl:with-param name	="address2" 	select	=".//SHIPTO/MAILADDRESS/ADDRESS2"/>	
		<xsl:with-param name	="city" 	select	=".//SHIPTO/MAILADDRESS/CITY"/>	
		<xsl:with-param name	="state" 	select	=".//SHIPTO/MAILADDRESS/STATE"/>	
		<xsl:with-param name	="zip" 		select	=".//SHIPTO/MAILADDRESS/ZIP"/>	
		<xsl:with-param name	="country" 	select	=".//SHIPTO/MAILADDRESS/COUNTRY"/>	
		<xsl:with-param name	="phone1" 	select	=".//SHIPTO/PHONE1"/>
		<xsl:with-param name	="email1" 	select	=".//SHIPTO/EMAIL1"/>
		<xsl:with-param name	="firstname" 	select	=".//SHIPTO/FIRSTNAME"/>
		<xsl:with-param name	="lastname" 	select	=".//SHIPTO/LASTNAME"/>
		<xsl:with-param name	="fax" 	select	=".//SHIPTO/FAX"/>
		<xsl:with-param name	="prefix" 	select	=".//SHIPTO/PREFIX"/>
	</xsl:call-template>	

</xsl:template>

<xsl:template name="companyaddress">
	<xsl:variable name="companynameindent">0.5in</xsl:variable>

	<xsl:if test="(.//LOGO != '')">
		<fo:block
			start-indent="{$companynameindent}">

			<fo:external-graphic 
				height		="0.6in" 
				max-width	="1.0in" 
				src		="{.//COMPANY/LOGO}"/>
		</fo:block>
	</xsl:if>
		<fo:block
			text-align		="start"
			line-height		="18pt"
			font-family		="{$myfont}"
			font-weight		="bold"
			font-size		="14pt"
			height			="4cm"
			width			="12cm"
			start-indent	="{$companynameindent}">
				<xsl:value-of select=".//COMPANY/TITLE"/>
		</fo:block>
		<fo:block
			font-size		="10pt"
			start-indent	="{$companynameindent}">
				<xsl:value-of select=".//COMPANY/ADDRESS1"/>
		</fo:block>
		<xsl:if test=".//COMPANY/ADDRESS2!=''">
			<fo:block
				font-size		="10pt"
				start-indent	="{$companynameindent}">
					<xsl:value-of select=".//COMPANY/ADDRESS2"/>
			</fo:block>
		</xsl:if>
		<fo:block
			font-size		="10pt"
			start-indent	="{$companynameindent}">
			<xsl:value-of select=".//COMPANY/CITY"/>
			<xsl:if test=".//COMPANY/STATE != '' and .//COMPANY/CITY != ''">
				<xsl:text>,  </xsl:text>
			</xsl:if>
			<xsl:value-of select=".//COMPANY/STATE"/>
			<xsl:text>  </xsl:text>
			<xsl:value-of select=".//COMPANY/ZIPCODE"/>
		</fo:block>
		<xsl:if test=".//COMPANY/CONTACTPHONE != ''">
			<fo:block
				font-size		="10pt"
				start-indent	="{$companynameindent}">
					<xsl:text>Ph:  </xsl:text>
					<xsl:value-of select="..//COMPANY/CONTACTPHONE"/>
			</fo:block>
		</xsl:if>
		<xsl:if test=".//COMPANY/FAX != ''">
			<fo:block
				font-size		="10pt"
				start-indent	="{$companynameindent}">
					<xsl:text>Fax: </xsl:text>
					<xsl:value-of select=".//COMPANY/FAX"/>
			</fo:block>
		</xsl:if>
</xsl:template>


<xsl:template name="generictitle">
	<xsl:param name="label"/>
	<xsl:param name="data"/>
	<xsl:param name="position"/>

   		<fo:block-container
			border-color	="white"
			border-style	="solid"
			border-bottom-width="0.000pt"
			border-top-width="0.000pt"
			border-left-width="0.000pt"
			border-right-width="0.000pt"
			height			="0.5cm"
			width			="2.5in"
			top				="{$position}"
			left		="4.3in"
			padding			="2pt"
			position		="absolute">

			<fo:block
				text-align			="start"
				space-after.optimum	="3pt"
				font-family			="{$myfont}"
				font-size			="10pt">

				<xsl:value-of select="$label"/>
				<xsl:text> : </xsl:text>
				<xsl:value-of select="$data"/>

			</fo:block>
		</fo:block-container>
	</xsl:template>

<xsl:template name="titlequotenumber">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Quote #'"/>
		<xsl:with-param name="data" 		select="REC/DOCNO"/>
		<xsl:with-param name="position"		select="'1.2cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titleordernumber">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Order #'"/>
		<xsl:with-param name="data" 		select="REC/DOCNO"/>
		<xsl:with-param name="position"		select="'1.2cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titledocumentnumber">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Document #'"/>
		<xsl:with-param name="data" 		select="REC/DOCNO"/>
		<xsl:with-param name="position"		select="'1.2cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titlequotedate">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Quote Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENCREATED"/>
		<xsl:with-param name="position"		select="'1.7cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titlevaliduptodate">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Valid up to'"/>
		<xsl:with-param name="data" 		select="REC/WHENDUE"/>
		<xsl:with-param name="position"		select="'2.2cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titleexpirationdate">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Expiration Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENDUE"/>
		<xsl:with-param name="position"		select="'2.2cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titleorderdate">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Order Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENCREATED"/>
		<xsl:with-param name="position"		select="'1.7cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titledate">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENCREATED"/>
		<xsl:with-param name="position"		select="'1.7cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titleorderduedate">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Due Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENDUE"/>
		<xsl:with-param name="position"		select="'2.2cm'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="titleordershipdate">
	<xsl:call-template name="generictitle">
		<xsl:with-param name="label" 		select="'Ship Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENDUE"/>
		<xsl:with-param name="position"		select="'2.2cm'"/>
	</xsl:call-template>
</xsl:template>
	
<xsl:template name="titleordershipvia"> 
	<xsl:if test="REC/SHIPVIA != ''">
		<xsl:call-template name="generictitle">
			<xsl:with-param name="label" 		select="'Ship Via'"/>
			<xsl:with-param name="data" 		select="REC/SHIPVIA"/>
			<xsl:with-param name="position"		select="'2.7cm'"/>
		</xsl:call-template>
	</xsl:if>
</xsl:template>

<xsl:template name="titleinvoicenumber">
	<xsl:choose>
		<xsl:when test="//_DOCPAR/CATEGORY='R'">
			<xsl:call-template name="generictitle">
				<xsl:with-param name="label" 		select="'Return #'"/>
				<xsl:with-param name="data" 		select="REC/DOCNO"/>
				<xsl:with-param name="position"		select="'1.2cm'"/>
			</xsl:call-template>
		</xsl:when>
		<xsl:otherwise>
			<xsl:call-template name="generictitle">
				<xsl:with-param name="label" 		select="'Invoice #'"/>
				<xsl:with-param name="data" 		select="REC/DOCNO"/>
				<xsl:with-param name="position"		select="'1.2cm'"/>
			</xsl:call-template>
		</xsl:otherwise>
	</xsl:choose>
</xsl:template>


<xsl:template name="titleinvoicedate">
	<xsl:choose>
		<xsl:when test="//_DOCPAR/CATEGORY='R'">
			<xsl:call-template name="generictitle">
				<xsl:with-param name="label" 		select="'Return Date'"/>
				<xsl:with-param name="data" 		select="REC/WHENCREATED"/>
				<xsl:with-param name="position"		select="'1.7cm'"/>
			</xsl:call-template>
		</xsl:when>
		<xsl:otherwise>
			<xsl:call-template name="generictitle">
				<xsl:with-param name="label" 		select="'Invoice Date'"/>
				<xsl:with-param name="data" 		select="REC/WHENCREATED"/>
				<xsl:with-param name="position"		select="'1.7cm'"/>
			</xsl:call-template>
		</xsl:otherwise>
	</xsl:choose>
</xsl:template>

<xsl:template name="titleinvoiceduedate">
	<xsl:choose>
	<xsl:when test="//_DOCPAR/CATEGORY='R'">
	</xsl:when>
	<xsl:otherwise>
		<xsl:call-template name="generictitle">
			<xsl:with-param name="label" 		select="'Due Date'"/>
			<xsl:with-param name="data" 		select="REC/WHENDUE"/>
			<xsl:with-param name="position"		select="'2.2cm'"/>
		</xsl:call-template>
	</xsl:otherwise>
	</xsl:choose>
</xsl:template>


<xsl:template name="genericfooter">
	<xsl:param name="label"/>
	<xsl:param name="data"/>
	<xsl:if test="$label!=''">
			<fo:inline font-weight="bold">
				<xsl:value-of select="$label"/>
				<xsl:text> : </xsl:text>
			</fo:inline>
			<xsl:value-of select="$data"/>
			<fo:inline color="white">
				<xsl:text>XXX</xsl:text>
			</fo:inline>
	</xsl:if>
</xsl:template>

<xsl:template name="genericfooterwhencreated">
	<xsl:param name="label"/>
	<xsl:call-template name="genericfooter">
		<xsl:with-param	name ="label" select	="$label"/>
		<xsl:with-param name ="data"  select="REC/WHENCREATED"/>
	</xsl:call-template>
</xsl:template>


<xsl:template name="genericfooterwhendue">
	<xsl:param name="label"/>
	<xsl:call-template name="genericfooter">
		<xsl:with-param	name	="label" select	="$label"/>
   		<xsl:with-param name	="data" select	="REC/WHENDUE"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="genericfootershipvia">
	<xsl:param name="label"/>
	<xsl:if test="REC/SHIPVIA != ''">
		<xsl:call-template name="genericfooter">
			<xsl:with-param	name ="label" select	="$label"/>
			<xsl:with-param name ="data" select	="REC/SHIPVIA"/>
		</xsl:call-template>
	</xsl:if>
</xsl:template>

<xsl:template name="footerblank">
	<xsl:call-template name="genericfooter">
		<xsl:with-param
			name	="label"
			select	="''"/>
   		<xsl:with-param
			name	="data"
			select	="''"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footerquotedate">
	<xsl:call-template name="genericfooterwhencreated">
		<xsl:with-param
			name	="label"
			select	="'Quote Date'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footerdate">
	<xsl:call-template name="genericfooterwhencreated">
		<xsl:with-param	name = "label" select	="'Date'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footerorderdate">
	<xsl:call-template name="genericfooterwhencreated">
		<xsl:with-param	name="label" select="'Order Date'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footervaliduptodate">
	<xsl:call-template name="genericfooterwhendue">
		<xsl:with-param	name="label" select="'Valid Up To'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footerexpirationdate">
	<xsl:call-template name="genericfooterwhendue">
		<xsl:with-param	name ="label" select	="'Expiration Date'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footerinvoicedate">
	<xsl:choose>
		<xsl:when test="REC/_DOCPAR/CATEGORY='R'">  
			<xsl:call-template name="genericfooterwhencreated">
				<xsl:with-param	name ="label" select	="'Return Date'"/>
			</xsl:call-template>
		</xsl:when>
		<xsl:otherwise>
			<xsl:call-template name="genericfooterwhencreated">
				<xsl:with-param	name="label" select	="'Invoice Date'"/>
			</xsl:call-template>
		</xsl:otherwise>
	</xsl:choose>
</xsl:template>

<xsl:template name="footerterms">
	<xsl:call-template name="genericfooter">
		<xsl:with-param	name	="label" select	="'Terms'"/>
   		<xsl:with-param name	="data"  select	="REC/TERM/NAME"/>
	</xsl:call-template>
</xsl:template>


<xsl:template name="footerduedate">
	<xsl:call-template name="genericfooterwhendue">
		<xsl:with-param
			name	="label"
			select	="'Due Date'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footershipdate">
	<xsl:call-template name="genericfooterwhendue">
		<xsl:with-param
			name	="label"
			select	="'Ship Date'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footershipvia">
	<xsl:call-template name="genericfootershipvia">
		<xsl:with-param
			name	="label"
			select	="'Ship Via'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="footerpage">
	<fo:inline font-weight="bold">
		<xsl:text>Page </xsl:text>
		<fo:page-number/>
	</fo:inline>
</xsl:template>

<xsl:template name="footerline">
    <fo:block-container
		height		="0.2in"
		width		="7.5in"
		top			="0.01in"
        left		="0.4in"
		position	="center">
        <fo:block>
        	<fo:leader
		   		leader-pattern		="rule"
				rule-thickness		="0.003in"/>
        </fo:block>
	</fo:block-container>
</xsl:template>

<xsl:template name="pagetop">
	<xsl:call-template name="companyaddress"/>
	<xsl:call-template name="documentlabel"/>
</xsl:template>


<xsl:template name="billtoshiptoaddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'billto'"/>
		<xsl:with-param name="addressb" select="'shipto'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="paytoreturntoaddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'payto'"/>
		<xsl:with-param name="addressb" select="'returnto'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="billtoaddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'billto'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="shiptoaddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'shipto'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="quotetoaddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'quoteto'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="requestbyaddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'requestby'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="vendoraddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'vendor'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="customeraddress">
	<xsl:call-template name="internalbilltoshiptoaddress">
		<xsl:with-param name="addressa" select="'customer'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="internalbilltoshiptoaddress">
	<xsl:param name="addressa"/>
	<xsl:param name="addressb"/>

     <fo:block
	 		space-before	="1.5cm"
			border-color	="white">
	   <fo:table
	   		table-layout="fixed"
			border-color	="white"
			border-style	="solid"
			border-bottom-width="0.000pt"
			border-top-width="0.000pt"
			border-left-width="0.000pt"
			border-right-width="0.000pt"
			>
	       <fo:table-column column-width	="9.7cm"/>
	       <fo:table-column column-width	="9.0cm"/>
		   <fo:table-body
				font-family		="{$myfont}"
				font-weight		="normal"
				font-size		="10pt">
			   <fo:table-row
					line-height		="15pt"
					>
					<xsl:call-template name="internalinsertaddress">
						<xsl:with-param name="whichaddress" select="$addressa"/>
					</xsl:call-template>
					<xsl:call-template name="internalinsertaddress">
						<xsl:with-param name="whichaddress" select="$addressb"/>
					</xsl:call-template>
				</fo:table-row>
			</fo:table-body>
     	</fo:table>
   	</fo:block>

</xsl:template>

<xsl:template name="internalinsertaddress">
	<xsl:param name="whichaddress"/>
  	<fo:table-cell
		padding		="6pt"
		>
		<xsl:if test="$whichaddress='billto'">
			<xsl:call-template name="genericbilltoaddress">
				<xsl:with-param name	="billorship" 	select	="'Bill To'"/>
			</xsl:call-template>
		</xsl:if>
		<xsl:if test="$whichaddress='shipto'">
			<xsl:call-template name="genericshiptoaddress">
				<xsl:with-param name	="billorship" 	select	="'Ship To'"/>
			</xsl:call-template>
		</xsl:if>
		<xsl:if test="$whichaddress='quoteto'">
			<xsl:call-template name="genericbilltoaddress">
				<xsl:with-param name	="billorship" 	select	="'Quoted To'"/>
			</xsl:call-template>
		</xsl:if>
		<xsl:if test="$whichaddress='requestby'">
			<xsl:call-template name="genericbilltoaddress">
				<xsl:with-param name	="billorship" 	select	="'Requested By'"/>
			</xsl:call-template>
		</xsl:if>
		<xsl:if test="$whichaddress='payto'">
			<xsl:call-template name="genericbilltoaddress">
				<xsl:with-param name	="billorship" 	select	="'Pay To'"/>
			</xsl:call-template>
		</xsl:if>
		<xsl:if test="$whichaddress='returnto'">
			<xsl:call-template name="genericshiptoaddress">
				<xsl:with-param name	="billorship" 	select	="'Return To'"/>
			</xsl:call-template>
		</xsl:if>
		<xsl:if test="$whichaddress='vendor'">
			<xsl:call-template name="genericshiptoaddress">
				<xsl:with-param name	="billorship" 	select	="'Vendor'"/>
			</xsl:call-template>
		</xsl:if>
		<xsl:if test="$whichaddress='customer'">
			<xsl:call-template name="genericshiptoaddress">
				<xsl:with-param name	="billorship" 	select	="'Customer'"/>
			</xsl:call-template>
		</xsl:if>
	</fo:table-cell>
</xsl:template>


<xsl:template name="displaycontacts">

		<xsl:variable name="showtitle1">
			<xsl:value-of select="REC/_DOCPAR/SHOWTITLE1" />
		</xsl:variable>

		<xsl:variable name="showtitle2">
			<xsl:value-of select="REC/_DOCPAR/SHOWTITLE2" />
		</xsl:variable>


		 <fo:block
				space-before	="1.5cm"
				border-color	="white">
		   <fo:table
				table-layout="fixed"
				border-color	="white"
				border-style	="solid"
				border-bottom-width="0.000pt"
				border-top-width="0.000pt"
				border-left-width="0.000pt"
				border-right-width="0.000pt"
				>
			   <fo:table-column column-width	="9.7cm"/>
			   <fo:table-column column-width	="9.0cm"/>
			   <fo:table-body
					font-family		="{$myfont}"
					font-weight		="normal"
					font-size		="10pt">
				   <fo:table-row line-height="15pt">

							<fo:table-cell padding="6pt">
								<!-- title1 is always billto and title2 is always shipto-->
									<xsl:choose>
										<xsl:when test="$showtitle1='true'">
											<xsl:call-template name="genericbilltoaddress">
												<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE1"/>
											</xsl:call-template>
										</xsl:when>
										<xsl:otherwise>
											<xsl:if test="$showtitle2='true'">
												<xsl:call-template name="genericshiptoaddress">
													<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE2"/>
												</xsl:call-template>
											</xsl:if>
										</xsl:otherwise>
									</xsl:choose>
							</fo:table-cell>

							<fo:table-cell padding="6pt">
									<xsl:if test="$showtitle1='true' and $showtitle2='true'">
										<xsl:call-template name="genericshiptoaddress">
											<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE2"/>
										</xsl:call-template>
									</xsl:if>
							</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>

</xsl:template>

<xsl:template match="/">
	<fo:root 
		xmlns:fo="http://www.w3.org/1999/XSL/Format"  
		xmlns:svg="http://www.w3.org/2000/svg" 
		xmlns:fox="http://xml.apache.org/fop/extensions">
		<xsl:call-template name="pagemaster"/>
		<xsl:apply-templates/>
	</fo:root>
</xsl:template>

<xsl:template match="DOCUMENT">
	<xsl:apply-templates/>	
</xsl:template>

<xsl:template match="OLDROOT">
	<fo:page-sequence master-reference="psmA" initial-page-number="1" >
		<fo:static-content flow-name="xsl-region-after" >			
			<xsl:call-template name="footer">					
				<xsl:with-param name="pagenum" select="'footerpage'"/>  
			</xsl:call-template>

		</fo:static-content>
		<fo:flow flow-name="xsl-region-body" >
			<xsl:call-template name="pagetop"/>  
			<xsl:call-template name="titleordernumber"/>  
			<xsl:call-template name="titleorderdate"/>  
			<xsl:call-template name="titleorderduedate"/>
			<xsl:call-template name="titleordershipvia"/>
			<xsl:choose>
				<xsl:when test="(REC/_DOCPAR/SHOWTITLE1 !='' ) and (REC/_DOCPAR/SHOWTITLE2 !='' )">
					<xsl:call-template name="displaycontacts">
					</xsl:call-template>
				</xsl:when>
				<xsl:otherwise>
					<xsl:call-template name="billtoshiptoaddress"/>
				</xsl:otherwise>
			</xsl:choose>

			 <xsl:choose>
				<xsl:when test="REC/_DOCPAR/CATEGORY='R'">
					<xsl:call-template name="topnotes">
						<xsl:with-param name="label0"        select="'GL Acct'"/>
						<xsl:with-param name="data0"         select="REC/PONUMBER"/>
				   </xsl:call-template>
				</xsl:when>
				<xsl:otherwise>
					<xsl:call-template name="topnotes">
						<xsl:with-param name="label0"        select="'GL Acct'"/>
						<xsl:with-param name="data0"         select="REC/PONUMBER"/>
						<xsl:with-param name="label1"        select="'Capital/Expense'"/>
						<xsl:with-param name="data1"         select="REC/VENDORDOCNO"/>
						<xsl:with-param name="label2"        select="'Terms'"/>
						<xsl:with-param name="data2"         select="REC/TERM/NAME"/>
				   </xsl:call-template>
				</xsl:otherwise>
			</xsl:choose>

			<xsl:call-template name="quotebody"/>	   			
        </fo:flow>
    </fo:page-sequence>
</xsl:template>

</xsl:stylesheet>
