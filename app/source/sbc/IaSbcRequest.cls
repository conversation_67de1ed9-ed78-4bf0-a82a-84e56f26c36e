<?php
//
// FILE:            IaSbcRequest.cls
// AUTHOR:          <PERSON><<EMAIL>>
// DESCRIPTION:     Intacct's main entry point to call Sage Banking Cloud
//
// (C)2000, Intacct Corporation, All Rights Reserved
//
// Intacct Corporation Proprietary Information.
// This document contains trade secret data that belongs to Intacct
// corporation and is protected by the copyright laws. Information herein
// may not be used, copied or disclosed in whole or part without prior
// written consent from Intacct Corporation.
//

class IaSbcRequest implements IaSbcRequestInterface
{

    /**
     * @var SbcHttpRequestInterface $sbcHttpRequest
     */
    private $sbcHttpRequest;

    /**
     * @var SbcIntegrationInterface $iaSbcIntegration
     */
    private $iaSbcIntegration;

    /**
     * @var bool $requiresNonce
     */
    private $requiresNonce;

    /**
     * IaSbcRequest constructor.
     *
     * @param SbcHttpRequestInterface $sbcHttpRequest
     * @param SbcIntegrationInterface $iaSbcIntegration
     * @param bool                    $requiresNonce
     *
     */
    public function __construct(SbcHttpRequestInterface $sbcHttpRequest,
                                SbcIntegrationInterface $iaSbcIntegration,
                                bool $requiresNonce)
    {
        $this->sbcHttpRequest = $sbcHttpRequest;
        $this->iaSbcIntegration = $iaSbcIntegration;
        $this->requiresNonce = $requiresNonce;
    }

    /**
     * @param string   $url
     * @param string[] $headers
     *
     * @return SbcHttpResult
     * @throws Exception
     */
    public function get(string $url, array $headers = []) : SbcHttpResult
    {
        $nonce = $this->requiresNonce ? $this->iaSbcIntegration->generateNonce() : null;
        $result = $this->sbcHttpRequest->makeHttpRequest('GET', $url, '', $nonce, $headers);

        return $result;
    }

    /**
     * @param string $url
     * @param string $body
     * @param string[]  $headers
     *
     * @return SbcHttpResult
     * @throws Exception
     */
    public function sync_post(string $url, string $body, array $headers = []) : SbcHttpResult
    {
        $nonce = $this->requiresNonce ? $this->iaSbcIntegration->generateNonce() : null;
        $result = $this->sbcHttpRequest->makeHttpRequest('POST', $url, $body, $nonce, $headers);

        return $result;
    }

    /**
     * @param string $url
     * @param string[]  $headers
     *
     * @return SbcHttpResult
     * @throws Exception
     */
    public function sync_delete(string $url, array $headers = []) : SbcHttpResult
    {
        $nonce = $this->requiresNonce ? $this->iaSbcIntegration->generateNonce() : null;
        $result = $this->sbcHttpRequest->makeHttpRequest('DELETE', $url, '', $nonce, $headers);

        return $result;
    }

    /**
     * @param string                             $url
     * @param string                             $body
     * @param IaSbcAsyncResponseHandlerInterface $responseHandler
     * @param string[]                           $headers
     *
     * @throws Exception when we're not able to publish the IMS package to handle the request
     */
    public function post(string $url, string $body, IaSbcAsyncResponseHandlerInterface $responseHandler,
                         array $headers = []
    )
    {
        $this->setupAsync('POST', $url, $headers, $body, $responseHandler);
    }

    /**
     * @param string                             $url
     * @param string                             $body
     * @param IaSbcAsyncResponseHandlerInterface $responseHandler
     * @param string[]                           $headers
     *
     * @throws Exception when we're not able to publish the IMS package to handle the request
     */
    public function put(string $url, string $body, IaSbcAsyncResponseHandlerInterface $responseHandler,
                        array $headers = [])
    {
        $this->setupAsync('PUT', $url, $headers, $body, $responseHandler);
    }

    /**
     * @param string                             $url
     * @param string                             $body
     * @param IaSbcAsyncResponseHandlerInterface $responseHandler
     * @param string[]                           $headers
     *
     * @throws Exception when we're not able to publish the IMS package to handle the request
     */
    public function patch(string $url, string $body, IaSbcAsyncResponseHandlerInterface $responseHandler,
                          array $headers = [])
    {
        $this->setupAsync('PATCH', $url, $headers, $body, $responseHandler);
    }

    /**
     * @param string                             $url
     * @param IaSbcAsyncResponseHandlerInterface $responseHandler
     * @param string[]                           $headers
     *
     * @throws Exception when we're not able to publish the IMS package to handle the request
     */
    public function delete(string $url, IaSbcAsyncResponseHandlerInterface $responseHandler, array $headers = [])
    {
        $this->setupAsync('DELETE', $url, $headers, '', $responseHandler);
    }

    /**
     * create a JWT used to access other auth services.  This is only needed when passing the JWT to some other
     * service outside this framework.  Normally the JWT is handled transparently inside the other api calls.
     *
     * @param bool $forceRefresh
     * @return string      the JWT scoped to this instance's org or company
     * @throws Exception
     */
    public function getJwt($forceRefresh = false) : string
    {
        return $this->sbcHttpRequest->getJwt($forceRefresh);
    }

    /**
     * @return SbcIntegrationInterface
     */
    public function getIaSbcIntegration()
    {
        return $this->iaSbcIntegration;
    }

    /**
     * @param string                             $httpMethod
     * @param string                             $url
     * @param string[]                           $headers
     * @param string                             $body
     * @param IaSbcAsyncResponseHandlerInterface $responseHandler
     *
     * @throws Exception
     */
    private function setupAsync(string $httpMethod, string $url, array $headers, string $body,
                                IaSbcAsyncResponseHandlerInterface $responseHandler)
    {
        $integration = $this->iaSbcIntegration;
        $nonce = $this->requiresNonce ? $this->iaSbcIntegration->generateNonce() : null;

        $poller = new IaSbcRequestPoller($httpMethod, $url, $headers, $body, $nonce,
                                         $responseHandler, $this->sbcHttpRequest,
                                         $this->iaSbcIntegration);
        if ( false === $integration->publishImsPoller($poller, IaSbcRequestPoller::INITIAL_DELAY_SECONDS) ) {
            throw new Exception("Unable to publish SBC IMS package.");
        }
    }

    /**
     * @param string $url
     * @param string $body
     * @param string[]  $headers
     *
     * @return SbcHttpResult
     * @throws Exception
     */
    public function sync_patch(string $url, string $body, array $headers = []) : SbcHttpResult
    {
        $nonce = $this->requiresNonce ? $this->iaSbcIntegration->generateNonce() : null;
        $result = $this->sbcHttpRequest->makeHttpRequest('PATCH', $url, $body, $nonce, $headers);

        return $result;
    }


}