<?php

class PublishImsPollerEvent implements Event
{
    /**
     * @var string
     */
    private string $url;

    public function __construct()
    {
    }

    /**
     * @param string $url
     * @return void
     */
    public function setUrl(string $url): void
    {
        $this->url = $url;
    }

    public function execute($eventType): bool
    {
        $response = null;
        Util::httpCall($this->url, '', $response);
        return true;
    }

    public function save(string $eventType): ?array
    {
        return [
            "args" => json_encode([
                "url" => $this->url
            ])
        ];
    }

    public function restore(array $args, string $eventType): void
    {
        $this->url = $args['url'];
    }
}