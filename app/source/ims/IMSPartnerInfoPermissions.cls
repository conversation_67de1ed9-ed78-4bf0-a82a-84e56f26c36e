<?php

/**
 * Class IMSPartnerInfoPermission
 *
 * This class holds the constants for each Sender ID Permission Bit.
 * Changing any of these constants will require migration and adjustments to
 * IMSPartnerInfoPermissionRoles.cls.
 */
class IMSPartnerInfoPermissions {

    /**
     * Permission bit to let 'intacct' support user bypass the whitelist
     */
    const SUPPORT_BYPASS = 2;

    /**
     * Permission bit to access production tenant types
     */
    const TYPE_PRODUCTION = 4;

    /**
     * Permission bit to access sandbox tenant types
     */
    const TYPE_SANDBOX = 8;

    /**
     * Permission bit to access implementation tenant types
     */
    const TYPE_IMPLEMENTATION = 16;

    /**
     * Permission bit to access archive tenant types
     */
    const TYPE_ARCHIVE = 32;

    /**
     * Permission bit to access template tenant types
     */
    const TYPE_TEMPLATE = 64;

    /**
     * Permission bit to access debug tenant types
     */
    const TYPE_DEBUG = 128;

    /**
     * Permission bit to access salesdemo tenant types
     */
    const TYPE_SALESDEMO = 256;

    /**
     * Permission bit to access demo tenant types
     */
    const TYPE_DEMO = 512;

    /**
     * Permission bit to access developer tenant types
     */
    const TYPE_DEVELOPER = 1024;

    /**
     * Permission bit to access sample tenant types
     */
    const TYPE_SAMPLE = 2048;

    /**
     * Permission bit to access training tenant types
     */
    const TYPE_TRAINING = 4096;

    /**
     * Permission bit to access test tenant types
     */
    const TYPE_TEST = 8192;
}
