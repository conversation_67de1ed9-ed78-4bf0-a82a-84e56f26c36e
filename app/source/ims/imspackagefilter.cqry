<?php

// Overwrite the Select query, without the cny#.
$kimspackagefilterQueries['QRY_IMSPACKAGEFILTER_SELECT_SINGLE_VID'] =
    array(
        'QUERY' => "SELECT imspackagefilter.cny#,imspackagefilter.record#,imspackagefilter.recipient,imspackagefilter.loginid,imspackagefilter.topic,imspackagefilter.package_action,imspackagefilter.package_details,to_char(imspackagefilter.starttime,'MM-DD-YYYY HH24:MI:SS'),to_char(imspackagefilter.endtime,'MM-DD-YYYY HH24:MI:SS'),imspackagefilter.reason,imspackagefilter.action,imspackagefilter.state FROM imspackagefilter imspackagefilter WHERE imspackagefilter.record# = ?",
        'ARGTYPES' => array('integer'),
    );

// Overwrite the update query, without the cny#.
$kimspackagefilterQueries['QRY_IMSPACKAGEFILTER_UPDATE'] =
    array(
        'QUERY' => "UPDATE imspackagefilter SET cny#=?,recipient=?,loginid=?,topic=?,package_action=?,package_details=?,starttime=to_date( ?, 'MM-DD-YYYY HH24:MI:SS'),endtime=to_date( ?, 'MM-DD-YYYY HH24:MI:SS'),reason=?,action=?,state=? WHERE record# = ?",
        'ARGTYPES' => array('integer','text','text','text','text','text','date','date','text','text','text','integer'),
    );

// Overwrite the insert query, without the cny#.
$kimspackagefilterQueries['QRY_IMSPACKAGEFILTER_INSERT'] =
    array(
        'QUERY' => "INSERT INTO imspackagefilter(cny#,record#,recipient,loginid,topic,package_action,package_details,starttime,endtime,reason,action,state) VALUES (?,?,?,?,?,?,?,to_date(?,'MM-DD-YYYY HH24:MI:SS'),to_date(?,'MM-DD-YYYY HH24:MI:SS'),?,?,?)",
        'ARGTYPES' => array('integer','integer','text','text','text','text','text','date','date','text','text','text'),
    );

// Overwrite the delete query, without the cny#.
$kimspackagefilterQueries['QRY_IMSPACKAGEFILTER_DELETE_VID'] =
    array(
        'QUERY' => "DELETE FROM imspackagefilter WHERE record# =?",
        'ARGTYPES' => array('integer'),
    );
