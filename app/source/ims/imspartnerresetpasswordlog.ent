<?php
/**
 * Ent for the sender id password reset log tracker
 *
 * LICENSE:
 * (C)2000-2017 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2017 Intacct Corporation
 */
require_once 'globals.ent';
$kSchemas['imspartnerresetpasswordlog'] = array(
    'object' => array(
        'RECORDNO',
        'PARTNERIDKEY',
        'IP',
        'EMAIL',
        'WHENCREATED',
        'DESCRIPTION',
    ),
    'schema' => array(
        'RECORDNO'     => 'record#',
        'PARTNERIDKEY' => 'partneridkey',
        'IP'           => 'ip',
        'EMAIL'        => 'email',
        'WHENCREATED'  => 'whencreated',
        'DESCRIPTION'  => 'description',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
    ),
    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'fullname' => 'IA.PARTNER_RECORDKEY',
            'type'     => array('ptype' => 'number', 'type' => 'number', 'size' => 8),
            'desc'     => 'IA.PARTNER_RECORD_NUMBER',
            'path'     => 'PARTNERIDKEY',
            'id'       => 1,
        ),
        array (
            'path'      => 'IP',
            'fullname'  => 'IA.IP_ADDRESS',
            'desc'      => 'IA.IP_ADDRESS',
            'type'      => array (
                'type' => 'text',
                'ptype' => 'text',
            ),
            'id'        => 2,
        ),
        array(
            'fullname'  => 'IA.CONTACT_EMAIL',
            'type' => array('ptype' => 'text', 'type' => 'text', 'maxlength' => 80, 'format' => $gEmailFormat),
            'desc'      => 'IA.CONTACT_EMAIL',
            'path'      => 'EMAIL',
            'id'        => 3,
        ),
        array (
            'path'      => 'DESCRIPTION',
            'fullname'  => 'IA.DESCRIPTION_OF_THE_PASSWORD_RESET',
            'desc'      => 'IA.PASSWORD_RESET_ACTIVITY',
            'type'      => array (
                'type' => 'text',
                'ptype' => 'text',
            ),
            'id'        => 4,
        ),
        array (
            'path'      => 'WHENCREATED',
            'fullname'  => 'IA.WHENCREATED',
            'desc'      => 'IA.WHENCREATED',
            'type'      => array (
                'type' => 'timestamp',
                'ptype' => 'timestamp',
            ),
            'id'        => 5,
        ),
    ),
    'table' => 'imspartnerresetpasswordlog',
    'vid' => 'RECORDNO',
    'global'=> true,
    'globalschema' => true
);
