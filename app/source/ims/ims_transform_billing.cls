<?
//==============================================================================
//
// FILE:			ims_transform_billing.cls
// AUTHOR:			<PERSON>
// DESCRIPTION:		Messaging Adapter for Intacct Billing System  
//
//	(C)2003, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//==============================================================================


require_once 'ims_transform_base.cls';


/**
 * @property array topiclist
 */
class ims_request_billing extends ims_request
{


    /**
     * @param ims_package $package
     */
    function __construct($package)
    {

        $this->topiclist = array(
            'CREATE_SOTRANSACTION' => '_CreateSOTransaction',
            'SYNCH_CUSTOMERANDCONTACT' => '_SynchCustomerAndContact'
        );

        $this->ConstructAdapter($package);
    }


    /**
     * @param ims_package $response
     *
     * @return bool
     */
    function DeliverPackage(&$response)
    {

        $fp = $this->topiclist[$this->ims_package->GetTopic()];
        $args = $this->ims_package->PopMessageVariable();
        $this->SetPackageContext('BILLTO: ' . $args['CUSTVENDID'] . ' | SHIPTO: ' . $args['SHIPTO']['CONTACTNAME']);
        $ret = $this->$fp($args);
        $this->PackageResponse($ret, $args);
        $response = $this->objResponsepkg;
        return true;

    }


    /**
     * @param array $args
     *
     * @return bool
     */
    function _CreateSOTransaction(&$args)
    {

        // Create the SalesOrder
        $fargs = array(
            'includefile' => 'IntacctBilling.cls',
            'classname' => 'IntacctBilling',
            'cargs' => null,
            'function' => 'CreateSOTransaction',
            'fargs' => array('values' => $args),
            'retvalue' => ''
        );

        $result = $this->ims_CallIntacctApi($fargs, false);
        if ($result === false) {
            return false;
        }
        return $fargs['retvalue'];

    }


    /**
     * @param array $args
     *
     * @return bool
     */
    function _SynchCustomerAndContact(&$args)
    {

        $fargs = array(
            'includefile' => 'IntacctBilling.cls',
            'classname' => 'IntacctBilling',
            'cargs' => null,
            'function' => 'SynchCustomerAndContact',
            'fargs' => array(
                'customerid' => $args['CUSTVENDID'],
                'contactid' => $args['SHIPTO']['CONTACTNAME']
            ),
            'retvalue' => ''
        );

        $result = $this->ims_CallIntacctApi($fargs, false);
        if ($result === false) {
            return false;
        }
        return $fargs['retvalue'];
    }

}

