<?php

// OVERWRITE & ADD NEW QUERIES AS NEEDED

$kimspartnerfiltershistoryQueries['QRY_IMSPARTNERFILTERSHISTORY_INSERT'] =
    [
        'QUERY'    => 'INSERT INTO imspartnerfiltershistory (partnerid, filterid, filterdescription, ip, company, username, object, function, message, status, activefrom, activeto, action, owner) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        'ARGTYPES' => [ 'text', 'text', 'text', 'text', 'text', 'text', 'text', 'text', 'text', 'text', 'timestamp', 'timestamp', 'text', 'text' ],
    ];

$kimspartnerfiltershistoryQueries['QRY_IMSPARTNERFILTERSHISTORY_SELECT_SINGLE_VID'] =
    [
        'QUERY'    => 'SELECT partnerid, filterid, filterdescription, ip, company, username, object, function, message, status, to_char(activefrom, \'MM/DD/YYYY HH24:MI:SS\') AS activefrom, to_char(activeto, \'MM/DD/YYYY HH24:MI:SS\') AS activeto, action, owner FROM imspartnerfiltershistory WHERE filterid = ? AND partnerid (+) = ?',
        'ARGTYPES' => [ 'text', 'text' ],
    ];
