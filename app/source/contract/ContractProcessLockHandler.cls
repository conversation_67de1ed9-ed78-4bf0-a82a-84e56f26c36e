<?php
/**
 *    FILE: ContractProcessLockHandler.cls
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2020, Sage Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Sage Intacct Corporation.
 */
class ContractProcessLockHandler
{
    const supported_processs = [
        ContractProcessLockInfo::CLEAR_ALL_MEA,
        ContractProcessLockInfo::CLEAR_LAST_ACTIVE,
        ContractProcessLockInfo::MANUAL_LOCK,
        ContractProcessLockInfo::POST_REVREC,
        ContractProcessLockInfo::APPLY_MEA,
        ContractProcessLockInfo::POST_POC_REVREC,
        ContractProcessLockInfo::PROCESS_INVOICE
    ];

    const MAX_WAIT_SECONDS = 300; // 5 minutes

    /** @var string|null $lockedRequestId */
    private static string|null $lockedRequestId;
    
    private static array $contractKeyMap = [];
    private static array $contractIdMap = [];
    
    
    /**
     * @param int    $contractKey
     * @param string $processType
     * @param int    $expiration
     * @return ContractProcessLockInfo
     */
    public static function createLockByProcess(int $contractKey, string $processType, $expiration = 2400) {
        $lockInfo = self::getContractLockInfoByContractKey($contractKey, $processType);
        if (ContractUtil::isContractLockFeatureEnabled()) {
            self::loadLockForContractAndProcess($lockInfo);

            if (!$lockInfo->isLocked()) {
                $lockKey = $lockInfo->getLockKey();
                $innerLock = new Lock();
                $innerLock->setClearLockOnDestroy(false);
                $innerLock->setLock($lockKey, $expiration);
                $lockInfo->initializeWithLockInfo($innerLock);
                self::$lockedRequestId = Globals::$g->perfdata->getSerialnbr();
            }
        }
    
        return $lockInfo;
    }

    /**
     * @param ContractProcessLockInfo $lockInfo
     */
    public static function releaseByLockInfo(ContractProcessLockInfo  $lockInfo) {
        if (ContractUtil::isContractLockFeatureEnabled()) {
            $checkLock = new Lock();
            if ($checkLock->initUsingExistingLock($lockInfo->getLockKey(), false)) {
                self::$lockedRequestId = null;
                $checkLock->releaseLock();
            }
        }
    }
    /**
     * @param int $contractKey
     * @param string $processType
     * @return ContractProcessLockInfo
     */
    public static function getContractLockInfoByContractKey($contractKey, $processType) {
        return new ContractProcessLockInfo($processType, $contractKey);
    }

    /**
     * @param ContractProcessLockInfo $lockInfo
     */
    public static function loadLockForContractAndProcess($lockInfo) {
        $lockKey = $lockInfo->getLockKey();
        $checkLock = new Lock();
        if ($checkLock->initUsingExistingLock($lockKey, false)) {
            $checkLock->setClearLockOnDestroy(false);
            $lockInfo->initializeWithLockInfo($checkLock);
        }
    }

    /**
     * @param int $contractKey
     * @return ContractProcessLockInfo|bool
     */
    public static function checkAllLocksForContractKey($contractKey = null) {
        if (!ContractUtil::isContractLockFeatureEnabled()) {
            return false;
        }
        if ($contractKey == null) {
            return false;
        }
        foreach (self::supported_processs as $processType) {
            $innerLockInfo = self::getContractLockInfoByContractKey($contractKey, $processType);
            self::loadLockForContractAndProcess($innerLockInfo);
            if($innerLockInfo->isLocked()) {
                return $innerLockInfo;
            }
        }
        return false;
    }
    
    /**
     * @param string $contractId
     *
     * @return false|ContractProcessLockInfo
     */
    public static function checkAllLocksForContractId($contractId = null)
    {
        return self::getExistingLock($contractId);
    }
    
    /**
     * Gets and existing lock if it exists, otherwise returns false.
     *
     * @param string $contractId
     * @param int    $contractKey
     * @param bool   $retrieveId    If contractId is not provided, retrieve it (for error message)
     *
     * @return ContractProcessLockInfo|bool
     */
    protected static function getExistingLock(&$contractId = null, &$contractKey = null, $retrieveId = false)
    {
        if (!ContractUtil::isContractLockFeatureEnabled()) {
            return false;
        }
        if ($contractId == null && $contractKey == null) {
            return false;
        }
    
        if ($contractId == null && $retrieveId) {
            $contractId = self::$contractIdMap[$contractKey] ?? null;
        }
        if ($contractId == null && $retrieveId) {
            $result = EntityManager::GetListQuick('contract', ['CONTRACTID'], ['RECORDNO' => $contractKey]);
            if (!empty($result)) {
                $contractId = $result[0]['CONTRACTID'];
                self::$contractIdMap[$contractKey] = $contractId;
            }
        }
    
        if ($contractKey == null) {
            $contractKey = self::$contractKeyMap[$contractId] ?? null;
        }
        if ($contractKey == null) {
            $result = EntityManager::GetListQuick('contract', ['RECORDNO'], ['CONTRACTID' => $contractId]);
            if (!empty($result)) {
                $contractKey = $result[0]['RECORDNO'];
                self::$contractKeyMap[$contractId] = $contractKey;
            }
        }
    
        return self::checkAllLocksForContractKey($contractKey);
    }
    
    /**
     * Checks if there's an existing lock.  If a lock exists and it's not from the existing request then throws an exception.
     *
     * @param string $contractId
     * @param int    $contractKey
     * @param bool   $createdBySameRequest
     *
     * @throws IAException
     */
    public static function checkLocksAndThrowException($contractId = null, $contractKey = null, bool &$createdBySameRequest = false)
    {
        $contractLockInfo = self::getExistingLock($contractId, $contractKey, true);
        $createdBySameRequest = isset(self::$lockedRequestId) && self::$lockedRequestId === Globals::$g->perfdata->getSerialnbr();
        if ($contractLockInfo && !$createdBySameRequest) { // allow same requestId to lock

            $errorInfo = $contractLockInfo->getLockedMessage($contractId, true);
            throw IAException::newIAException($errorInfo->getErrorId(), $errorInfo->getErrorMessage(),
                $errorInfo->getPlaceholders());
        }
    }
    
    /**
     * Checks if there's an existing lock.  If no lock exists, returns true.  Otherwise adds global error and returns false.
     *
     * @param string $contractId
     * @param string $contractKey
     * @param bool   $createdBySameRequest
     *
     * @return bool
     */
    public static function checkLocksAndAddGlobalError($contractId = null, $contractKey = null, bool &$createdBySameRequest = false)
    {
        $result = true;
        try {
            self::checkLocksAndThrowException($contractId, $contractKey, $createdBySameRequest);
        } catch (Exception $e) {
            $result = false;
            ContractUtil::addThrowableError($e, __FILE__ . ':' . __LINE__);
        }
        return $result;
    }
    
    /**
     * Creates a new contract process lock.  If the lock already exists then it waits up to a maximum of $waitSeconds.
     * If the lock still exists after $waitSeconds an exception is thrown.
     *
     * @param int    $contractKey
     * @param string $processType
     * @param int    $waitSeconds
     * @param int    $expiration
     *
     * @return ContractProcessLockInfo
     * @throws IAException
     */
    public static function createNewProcessLockOrException(
        $contractKey,
        $processType,
        $waitSeconds = 0,
        $expiration = 2400,
    ) {
        $success = false;
        
        $lockInfo = self::getContractLockInfoByContractKey($contractKey, $processType);
        if (ContractUtil::isContractLockFeatureEnabled()) {
            self::loadLockForContractAndProcess($lockInfo);

            // Limit waitSeconds to the maximum
            if ($waitSeconds && ibccomp($waitSeconds, self::MAX_WAIT_SECONDS) === 1) {
                $waitSeconds = self::MAX_WAIT_SECONDS;
            }
    
            $start = microtime(true);
            $contractLockInfo = false;
    
            // If there is a lock keep checking every second until $waitSeconds, then throw an error
            do {
                if ($contractLockInfo) {
                    sleep(1);
                }
        
                $contractLockInfo = self::checkAllLocksForContractKey($contractKey);
                $waitExpired = ibccomp(microtime(true) - $start, $waitSeconds) >= 0;
            } while ($contractLockInfo && !$waitExpired);
    
            if (!$contractLockInfo) {
                $lockKey = $lockInfo->getLockKey();
                $innerLock = new Lock();
                $innerLock->setClearLockOnDestroy(false);
                $success = $innerLock->setLock($lockKey, $expiration, !empty($waitSeconds), $waitSeconds);
                if ($success) {
                    $lockInfo->initializeWithLockInfo($innerLock);
                    self::$lockedRequestId = Globals::$g->perfdata->getSerialnbr();
                }
            } else {
                $lockInfo = $contractLockInfo;
            }
    
            if (!$success) {
                $contractId = self::$contractIdMap[$contractKey] ?? null;
    
                if ($contractId == null) {
                    $result = EntityManager::GetListQuick('contract', ['CONTRACTID'], ['RECORDNO' => $contractKey]);
                    if (!empty($result)) {
                        $contractId = $result[0]['CONTRACTID'];
                        self::$contractIdMap[$contractKey] = $contractId;
                    }
                }

                $errorInfo = $lockInfo->getLockedMessage($contractId, true);
                throw IAException::newIAException($errorInfo->getErrorId(),
                    $errorInfo->getErrorMessage(), $errorInfo->getPlaceholders());
            }
        }
        
        return $lockInfo;
    }

    /**
     * Creates a new contract process lock.  If the lock already exists then it waits up to a maximum of $waitSeconds.
     * If the lock still exists after $waitSeconds a global error is added and null is returned.
     *
     * @param int    $contractKey
     * @param string $processType
     * @param int    $waitSeconds
     * @param int    $expiration
     *
     * @return ContractProcessLockInfo|null
     */
    public static function createNewProcessLockOrGlobalError(
        $contractKey,
        $processType,
        $waitSeconds = 0,
        $expiration = 2400,
    ) {
        $lockInfo = null;
        
        try {
            $lockInfo = self::createNewProcessLockOrException($contractKey, $processType, $waitSeconds, $expiration);
        } catch (Exception $e) {
            ContractUtil::addThrowableError($e, __FILE__ . ':' . __LINE__);
        }
        
        return $lockInfo;
    }
    
    /**
     * This will lock the particular record key, and hold the record key for
     * the expiration time and do not allow any other transaction untill expiry
     *
     * @param string $dbTable
     * @param int    $recordNo
     *
     * @return false
     */
    public static function acquireLock($dbTable, $recordNo)
    {
        $ok = true;

        $expirationTime = Lock::DEFAULT_EXPIRATION_TIME; // If need to change contract specific then change here
        // Acquire lock for recordno
        if ($recordNo) {
            $editlock = new Lock();
            $lockName = self::getLockName($dbTable, $recordNo);

            if ($editlock->lockHeld($lockName) || !$editlock->setLock($lockName, $expirationTime, false)) {
                $gErr = Globals::$g->gErr;
                $msg = "Another user is currently working on this transaction. Wait a few minutes, and then try again";
                $gErr->addError('CN-0556', __FILE__ . '.' . __LINE__, "Unable to access transaction", $msg);
                $ok = false;
            }

        }

        return $ok;
    }

    /**
     * @param string $dbTable
     * @param int $recordNo
     */
    public static function releaseLocks($dbTable, $recordNo)
    {

        $lockName =  self::getLockName($dbTable, $recordNo);
        $lock = new Lock();
        if ($lock->initUsingExistingLock($lockName, false)) {
            $lock->releaseLock();
        }

    }

    /**
     * @param string $dbTable
     * @param int $recordNo
     * @return string
     */
    private  static  function  getLockName(string $dbTable, int $recordNo): string
    {
        return GetMyCompany() . '_' . strtoupper($dbTable) . '_' . $recordNo;
    }
}

class ContractProcessLockInfo {
    const CLEAR_ALL_MEA = "Clearing_All_MEA";
    const CLEAR_LAST_ACTIVE = "Clearing_last_active_MEA";
    const APPLY_MEA = "APPLYING_MEA";
    const POST_REVREC = "Posting_rev_rec";
    const POST_POC_REVREC = "Posting_poc_rev_rec";
    const MANUAL_LOCK = "Contract_Locked_by_API";
    const PROCESS_INVOICE = 'Processing_invoice';
    const PROCESS_INVOICE_WAIT_SECONDS = 60;
    
    /** @var array $processNames */
    private static $processNames;

    /** @var string $processId */
    private $processId;
    /** @var string $userId */
    private $userId;
    /** @var int[] $logicalId */
    private $logicalId;
    /** @var int $timestamp */
    private $timestamp;
    /** @var bool $isLocked */
    private $isLocked = false;
    /** @var null|string $rawLockData  */
    private $rawLockData = null;

    private array $textMap;
    
    /**
     * ContractProcessLockInfo constructor.
     *
     * @param string $processId
     * @param string $logicalId
     *
     * @throws I18NException
     * @throws IAException
     */
    public function __construct(string $processId, string $logicalId)
    {
        $tokens = [
            'IA.CNLOCK_CLEAR_ALL_MEA',
            'IA.CNLOCK_CLEAR_LAST_ACTIVE_MEA',
            'IA.CNLOCK_POST_REVENUE_RECOGNITION',
            'IA.CNLOCK_API_INSTRUCTION',
            'IA.CNLOCK_CREATE_MEA',
            'IA.CNLOCK_UPDATE_PERCENT_SCHEDULES',
            'IA.CNLOCK_GENERATE_INVOICE',
            'IA.CNLOCK_ERROR_MESSAGE',
        ];
        
        $textMap = $this->textMap = getLocalizedTextWithThrow(I18N::tokenArrayToObjectArray($tokens));
        
        self::$processNames = [
            self::CLEAR_ALL_MEA => GT($textMap, 'IA.CNLOCK_CLEAR_ALL_MEA'),
            self::CLEAR_LAST_ACTIVE => GT($textMap, 'IA.CNLOCK_CLEAR_LAST_ACTIVE_MEA'),
            self::POST_REVREC => GT($textMap, 'IA.CNLOCK_POST_REVENUE_RECOGNITION'),
            self::MANUAL_LOCK => GT($textMap, 'IA.CNLOCK_API_INSTRUCTION'),
            self::APPLY_MEA => GT($textMap, 'IA.CNLOCK_CREATE_MEA'),
            self::POST_POC_REVREC => GT($textMap, 'IA.CNLOCK_UPDATE_PERCENT_SCHEDULES'),
            self::PROCESS_INVOICE => GT($textMap, 'IA.CNLOCK_GENERATE_INVOICE'),
        ];
        
        if (in_array($processId, ContractProcessLockHandler::supported_processs)) {
            $this->processId = $processId;
            $cnyInfo = GetMyCompany();
            $this->logicalId = $logicalId . $cnyInfo;
        } else {
            throw IAException::newIAException('CN-0982',
                $processId . 'is not supported for locks', ['PROCESS_ID' => $processId]);
        }
    }

    /**
     * @return false|string
     */
    public function getLockKey() {
        return $this->processId . ":" . $this->logicalId;
    }

    /**
     * @param string $lockKey
     * @return ContractProcessLockInfo
     */
    public static function getContractProcessLockFromKey(string $lockKey) {
        $innerArray = preg_split('/:/', $lockKey);
        return new ContractProcessLockInfo($innerArray[0], $innerArray[1]);
    }

    /**
     * @return string
     */
    public function getProcessId(): string
    {
        return $this->processId;
    }

    /**
     * @param string $processId
     * @return string
     */
    public function getProcessName(string $processId) {
        return self::$processNames[$processId];
    }
    /**
     * @return string
     */
    public function getUserId(): string
    {
        return $this->userId;
    }

    /**
     * @return string
     */
    public function getLogicalId(): string
    {
        return $this->logicalId;
    }

    /**
     * @return int
     */
    public function getTimestamp(): int
    {
        return $this->timestamp;
    }

    /**
     * @return bool
     */
    public function isLocked() {
        return $this->isLocked;
    }

    /**
     * @return string|null
     */
    public function getRawLockData() {
        return $this->rawLockData;
    }
    /**
     * @param Lock $lock
     */
    public function initializeWithLockInfo(Lock $lock) {
        $this->rawLockData = $lock->getLock();
        $lockProps = preg_split('/:/', $this->rawLockData);
        $this->userId = $lockProps[3];
        $this->timestamp = (int)$lockProps[2];
        $this->isLocked = true;
    }
    
    /**
     * @return string|ContractI18nErrorInfo
     */
    public function getLockedMessage($contractId, $contractI18nErrorInfoReturnType = false) {
        [$date, $time] = explode("  ", $this->getTimestamp());
        $dt = date('m/d/Y h:i:s', $date);

        // not sure how userId can be null, here to make lock message work in this case.
        $userName = "UNKNOWN";
        if ($this->userId) {
            [$user, $cny] = explode('@', $this->userId);
            $userInfoManager = Globals::$g->gManagerFactory->getManager('userinfo');
            $userInfo = $userInfoManager->GetByRecordNo($user);
            $userName = $userInfo['DESCRIPTION'];
        }

        ContractUtil::logMessageWhenDebug($cny . $time);

        $processName = $this->getProcessName($this->getProcessId());

        if ($contractI18nErrorInfoReturnType) {

            $msg = "Contract $contractId is locked by $userName at $dt by the following action: $processName.";

            return new ContractI18nErrorInfo('CN-0981',
                [
                    'CONTRACTID' => $contractId,
                    'PROCESS_NAME' => $processName,
                    'USER_NAME' => $userName,
                    'LOCK_DATETIME' => $dt
                ],
                $msg);

        } else {
            return ContractUtil::GTP($this->textMap, 'IA.CNLOCK_ERROR_MESSAGE', [
                'CONTRACTID' => $contractId,
                'PROCESS_NAME' => $processName,
                'USER_NAME' => $userName,
                'LOCK_DATETIME' => $dt,
            ]);
        }

    }
}