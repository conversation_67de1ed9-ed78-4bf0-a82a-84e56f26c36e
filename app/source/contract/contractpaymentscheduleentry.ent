<?php
/**
 * File contractpaymentscheduleentry.ent contains entity definition for contractpaymentscheduleentry
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */


$kSchemas['contractpaymentscheduleentry'] = array(

    'object' => array (
        'RECORDNO',
        'SCHEDULEENTRYKEY',
        'SCHEDULEKEY',
        'AMOUNT',
        'BASEAMOUNT',
        'POSTEDBASEAMOUNT',
        'EXCHANGE_RATE',
        'POSTEDEXCHANGE_RATE',
        'POSTINGDATE',
        'ACTUALPOSTINGDATE',
        'DERIVEDPOSTINGDATE',
        'APPROVEDHOURS',
        'SOURCEQTY',
        'PERCENTRECOGNIZED',
        'POSTED',
        'HISTORICAL',
        'STATE',
        'COMPUTATIONMEMO',
        'BILLSCHEDULEENTRYKEY',
        'ORIPMTSCHEDULEENTRYKEY',
        'PAYMENTPRENTRYKEY',
        'PAY<PERSON>NTPRRECORDKEY',
        'TYPE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),

    'schema' => array(
        'RECORDNO'              => 'record#',
        'SCHEDULEENTRYKEY'      => 'record#',
        'SCHEDULEKEY'           => 'schedulekey',
        'AMOUNT'                => 'amount',
        'BASEAMOUNT'            => 'baseamount',
        'POSTEDBASEAMOUNT'      => 'postedbaseamount',
        'EXCHANGE_RATE'         => 'exchange_rate',
        'POSTEDEXCHANGE_RATE'   => 'postedexchange_rate',
        'POSTINGDATE'           => 'postingdate',
        'ACTUALPOSTINGDATE'     => 'actualpostingdate',
        'DERIVEDPOSTINGDATE'    => 'derivedpostingdate',
        'APPROVEDHOURS'         => 'approvedhours',
        'SOURCEQTY'             => 'sourceqty',
        'PERCENTRECOGNIZED'     => 'percentrecognized',
        'POSTED'                => 'posted',
        'HISTORICAL'                => 'historical',
        'STATE'                 => 'state',
        'POSTABLE'              => 'postable',
        'COMPUTATIONMEMO'       => 'computationmemo',
        'BILLSCHEDULEENTRYKEY'  => 'billschentrykey',
        'ORIPMTSCHEDULEENTRYKEY'=> 'oripmtschentrykey',
        'PAYMENTPRENTRYKEY'     => 'paymentprentrykey',
        'PAYMENTPRRECORDKEY'    => 'paymentprrecordkey',
        'REVERSALRECORDNO'      => 'reversalscheduleentry.record#',
        'TYPE'                  => 'contractpaymentschedule.type',
        'CONTRACTDETAILKEY'     => 'contractpaymentschedule.contractdetailkey',
        'CONTRACTKEY'           => 'contractdetail.contractkey',
        'CNDETAILBEGINDATE'     => 'contractdetail.begindate',
        'CNDETAILCANCELDATE'    => 'contractdetail.canceldate',
        'CONTRACTID'            => 'contract.contractid',
        'CONTRACTLINENO'        => 'contractdetail.lineno',
        'DOCID'                 => 'dochdr.docid',
        'WHENMODIFIED'          => 'whenmodified',
        'WHENCREATED'           => 'whencreated',
        'CREATEDBY'             => 'createdby',
        'MODIFIEDBY'            => 'modifiedby',
    ),

    'children' => array(
        'contractpaymentschedule' => [
            'fkey' => 'schedulekey', 'invfkey' => 'record#', 'join' => 'inner', 'table' => 'contractpaymentschedule',
            'children' => [
                'contractdetail' => [
                    'fkey' => 'contractdetailkey', 'invfkey' => 'record#', 'table' => 'contractdetail', 'join' => 'inner',
                    'children' => array(
                        'contract' => array(
                            'fkey' => 'contractkey', 'invfkey' => 'record#', 'join' => 'inner','table' => 'contract',
                        ),
                    ),
                 ],
            ],
        ],
        'reversalscheduleentry' => [
            'fkey' => 'record#', 'invfkey' => 'oripmtschentrykey', 'join' => 'outer', 'table' => 'contractscheduleentry',
        ],
        'contractschedulesresolve' => [
            'fkey' => 'record#', 'invfkey' => 'pmtschentrykey', 'join' => 'outer', 'table' => 'contractschedulesresolve',
        ],
        'docentry' => [
            'fkey' => 'record#', 'invfkey' => 'billablecontractschentrykey', 'join' => 'outer', 'table' => 'docentry',
            'children' => [
                'dochdr' => [
                    'fkey' => 'dochdrkey', 'invfkey' => 'record#', 'table' => 'dochdr', 'join' => 'outer',
                ],
            ],
        ]
    ),

    'nexus' => array(
        'contractpaymentschedule' => array(
            'object' => 'contractpaymentschedule', 'relation' => MANY2ONE, 'field' => 'SCHEDULEKEY'
        ),
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),
    'fieldinfo' => array(
        array (
            'id' => 1,
            'path' => 'RECORDNO',
            'desc' => 'IA.SCHEDULE_ENTRY_KEY',
            'fullname' => 'IA.SCHEDULE_ENTRY_KEY',
            'type' => array (
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ),
            'readonly' => true
        ),
        array (
            'id' => 2,
            'path' => 'SCHEDULEKEY',
            'desc' => 'IA.SCHEDULE_KEY',
            'fullname' => 'IA.SCHEDULE_KEY',
            'type' => array (
                'type' => 'integer',
                'maxlength' => 8,
            ),
        ),
        array (
            'id' => 3,
            'path' => 'AMOUNT',
            'desc' => 'IA.AMOUNT',
            'fullname' => 'IA.AMOUNT',
            'type' => array (
                'type' => 'integer',
            ),
        ),
        array (
            'id' => 5,
            'path' => 'BASEAMOUNT',
            'desc' => 'IA.BASE_AMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'type' => array (
                'type' => 'integer',
            ),
        ),
        array (
            'id' => 6,
            'path' => 'POSTINGDATE',
            'fullname' => 'IA.PAYMENT_DATE',
            'type' => $gDateType,
            'required'    => false,
            'desc' => 'IA.PAYMENT_DATE',
        ),
        array (
            'id'    => 7,
            'path'  => 'ACTUALPOSTINGDATE',
            'fullname' => 'IA.ACTUAL_POSTING_DATE',
            'type' => $gDateType,
            'required'    => false,
            'desc' => 'IA.ACTUAL_POSTING_DATE',
        ),
        array (
            'id' => 8,
            'path' => 'POSTED',
            'fullname' => 'IA.BILLED',
            'type' => $gBooleanType,
            'required'    => false,
            'desc' => 'IA.BILLED',
            'readonly' => true,
            'default' => 'false',
        ),
        array (
            'id' => 9,
            'path' => 'PAYMENTIDLINK',
            'fullname' => 'IA.PAYMENT_ID',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',
            ),
            'isHTML' => true,
            'readonly' => true
        ),
        array (
            'id' => 10,
            'path' => 'COMPUTATIONMEMO',
            'fullname' => 'IA.COMPUTATION_MEMO',
            'type' => array (
                'type' => 'text',
                'ptype' => 'text',
            ),
            'readonly' => true
        ),
        array(
            'path' => 'STATE',
            'fullname' => 'IA.STATE',
            'desc' => 'IA.STATE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.OPEN', 'IA.TERMINATED', 'IA.POSTED'),
                'validvalues' => array('Open', 'Terminated', 'Posted'),
                '_validivalues' => array('O', 'T', 'P'),
                'default' => 'O'
            ),
            'id' => 11
        ),
        array (
            'id' => 12,
            'path' => 'POSTABLE',
            'fullname' => 'IA.POSTABLE',
            'type' => $gBooleanType,
            'required'    => false,
            'desc' => 'IA.POSTABLE',
            'readonly' => true,
            'hidden' => true,
            'default' => 'true',
        ),
        array (
            'id' => 13,
            'path' => 'BILLSCHEDULEENTRYKEY',
            'fullname' => 'IA.BILLING_SCHEDULE_ENTRY_KEY_OF_PAYMENT',
            'type'      =>  array (
                'ptype'     =>  'integer',
                'type'      =>  'integer',
                'size'      =>  8,
                'maxlength' =>  8,
                'format'    => $gRecordNoFormat
            ),
            'required'    => true,
        ),
        array (
            'id' => 14,
            'path' => 'ORIPMTSCHEDULEENTRYKEY',
            'fullname' => 'IA.ORIGINAL_PAYMENT_SCHEDULE_ENTRY_KEY',
            'type'      =>  array (
                'ptype'     =>  'integer',
                'type'      =>  'integer',
                'size'      =>  8,
                'maxlength' =>  8,
                'format'    => $gRecordNoFormat
            ),
        ),
        [
            'id' => 15,
            'path' => 'TYPE',
            'fullname' => 'IA.SCHEDULE_TYPE',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array('IA.PAYMENT'),
                'validvalues' => array('Payment'),
                '_validivalues' => array('P'),
            ),
            'required' => false,
            'idw' => false,
        ],
        array (
            'id' => 17,
            'path' => 'DERIVEDPOSTINGDATE',
            'fullname' => 'IA.DERIVED_SCHEDULED_POSTING_DATE',
            'type' => $gDateType,
            'required'    => false,
            'desc' => 'IA.DERIVED_SCHEDULED_POSTING_DATE',
            'hidden' => true,
        ),
        array (
            'id' => 18,
            'path' => 'APPROVEDHOURS',
            'desc' => 'IA.APPROVED_HOURS',
            'fullname' => 'IA.APPROVED_HOURS',
            'type' => array (
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 18,
                'size' => 10,
            ),
            'hidden' => true,
        ),

        array (
            'id' => 19,
            'path' => 'SOURCEQTY',
            'desc' => 'IA.SRC_QTY',
            'fullname' => 'IA.APPROVED_HOURS',
            'type' => array (
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 18,
                'size' => 10,
            ),
            'hidden' => true,
        ),

        array (
            'id' => 20,
            'path' => 'PERCENTRECOGNIZED',
            'desc' => 'IA.PERCENTAGE_TO_RECOGNIZE',
            'fullname' => 'IA.PERCENTAGE_TO_RECOGNIZE',
            'type' => array (
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 18,
                'size' => 10,
            ),
            'hidden' => true,
        ),
        array (
            'id' => 21,
            'path' => 'HISTORICAL',
            'fullname' => 'IA.HISTORICAL',
            'type' => $gBooleanType,
            'readonly' => true,
        ),
        [
            'id' => 22,
            'path' => 'PAYMENTPRENTRYKEY',
            'fullname' => 'IA.PAYMENT_PR_ENTRY_KEY',
            'type'      =>  [
                'ptype'     =>  'integer',
                'type'      =>  'integer',
                'size'      =>  8,
                'maxlength' =>  8,
                'format'    => $gRecordNoFormat
            ],
        ],
        [
            'id' => 23,
            'path' => 'PAYMENTPRRECORDKEY',
            'fullname' => 'IA.PAYMENT_PR_RECORD_KEY',
            'type'      =>  [
                'ptype'     =>  'integer',
                'type'      =>  'integer',
                'size'      =>  8,
                'maxlength' =>  8,
                'format'    => $gRecordNoFormat
            ],
        ],


        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
    'dbsorts' => array(
        array('POSTINGDATE'),
        array('RECORDNO')
    ),
    'dbfilters' => array(
        array(
            'contractpaymentschedule.type', '=', 'P'
        )
    ),
    'bulkoperation' => true,
    'parententity' => 'contractpaymentschedule',
    'table'    => 'contractscheduleentry',
    'vid'     => 'RECORDNO',
    'module' => 'cn',
    'autoincrement' => 'RECORDNO',
    'auditcolumns' => true,
    'printas' => 'IA.CONTRACT_PAYMENT_SCHEDULE_ENTRY',
    'pluralprintas' => 'IA.CONTRACT_PAYMENT_SCHEDULE_ENTRIES',
);
