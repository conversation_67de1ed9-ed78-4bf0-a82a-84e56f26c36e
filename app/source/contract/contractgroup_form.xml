<?xml version='1.0' encoding='ISO-8859-1'?>
<ROOT>
    <title>IA.CONTRACT_GROUP_INFORMATION</title>
	<view system="true">
		<pages id="mainPages">
			<page id="mainPage" title="IA.TRANSACTION">
				<child>
					<section id="mainSection">
                        <child>
                            <field>
                                <path>ID</path>
                                <events>
                                    <change>autoPopulateName(this.value)</change>
                                </events>
                            </field>
                        </child>
						<child><field>NAME</field></child>
		                <child><field>DESCRIPTION</field></child>
                        <child>
                            <field>
                                <path>GROUPTYPE</path>
                                <events>
                                    <change>showHideMembers(this)</change>                                
                                </events>     
                            </field>
                        </child>
                        <child>
                            <subsection id="TopMatch" className="sectionnoborder">
                                <row className="subElement">
                                    <field>SORTORDER</field>
                                    <field>SORTFIELD</field>
                                </row>
                                <row className="subElement" helpText="IA.LEAVE_BLANK_TO_SHOW_ALL_MEMBERS">
                                    <field>RES<PERSON>ICTTO</field>
                                </row>
                            </subsection>
                        </child>               
                        <child>
                            <field helpText="IA.CREATES_A_DIMENSION_STRUCTURE_WITH_THE_SAME">
                                <path>CREATEDIMCOMP</path>
                            </field>                      
                        </child>                                 
					</section>
				</child>
				<child>
                    <grid isCollapsible="true">
                        <path>MEMBERS</path>
                        <title>IA.MEMBERS</title>
                        <column>
                            <field>
                                <path>CONTRACTID</path>
                            </field>
                        </column>
                    </grid>
                </child>
				<child>
                    <grid noDragDrop="true" isCollapsible="true">
                        <path>MEMBERFILTERS</path>
                        <title>IA.FILTERS</title>
                        <column>
                            <field>
                                <path>FIELD</path>
                            </field>
                        </column>
                        <column>
                            <field>
                                <path>OPERATOR</path>
                            </field>
                        </column>
                        <column>
                            <field>
                                <path>VALUE</path>
                            </field>
                        </column>
                    </grid>
                </child>
                <child>
                    <section className="qx-section-oldui-nostyle">
                        <row id="Conditionrow">
                            <field>
                                <path>CONDITIONTYPE</path>
                                <events>
                                    <change>changeCondition(this)</change>
                                </events>
                            </field>
                            <field>CONDITION</field>
                        </row>
                    </section>
				</child>                           
			</page>
		</pages>
        <events>
            <load>showHideMembers(null);changeCondition(null);changeGridFldTypes("contractgroup");</load>                                
        </events> 
	</view>
    <helpfile>Adding_Editing_and_Viewing_a_Contract_group</helpfile>
</ROOT>
