<?php
/**
 * File ContractUAGroupManager.cls contains the class ContractUAGroupManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * CRUD handler for Contract UAGroup
 *
 * Class ContractUAGroupManager
 */
class ContractUAGroupManager extends ContractBundleManager
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        $this->setObjectType(ContractBundleManager::BUNDLE_TYPE_USAGE_AGGREGATION);
    }

    /**
     * buildBundleValueColumn
     *
     * @param array $values array of fields
     * @param array $selects array of select fields
     * @param array $aliases array of select alias names
     *
     */
    protected function buildBundleValueColumn(&$values, &$selects, &$aliases)
    {
        // if we are editing then pull in current bundles value as a column...
        $selects[] =
            "( select value from contractbundleentry cbe, contractbundle cb
                   where cbe.cny# = contractdetail.cny#
                   and cbe.contractdetailkey = contractdetail.record#
                   and cbe.contractkey = contractdetail.contractkey
                   and cb.cny# = cbe.cny# and cb.record# = cbe.bundlekey
                   and cb.type = 'UA'
                )";

        $aliases[] = 'GROUPNO';
    }

    /**
     * getContractDetailBundle
     *
     *  This function tries to retrieve all contract detail lines which have duplicate items (2 or more)
     *  and those detail lines also configured as quatity based billing method...
     *
     * @param array $values
     *
     * @return array $resultSet
     *
     */
    public function getContractDetailBundle(&$values)
    {
        $gManageFactory = Globals::$g->gManagerFactory;
        list($contractid) = explode('--', $values['CONTRACTID']);

        // let's make data for bundles grid by querying the contractdetails
        $cnDetailMgr =  $gManageFactory->getManager('contractdetail');

        $selects = array(
            'CONTRACTKEY', 'CONTRACTID', 'CONTRACT.DESCRIPTION', 'CUSTOMERID', 'CUSTOMERNAME',
            'RECORDNO', 'LINENO', 'ITEMID', 'ITEM.NAME',
            array(
                'fields' => array('CONTRACTKEY', 'ITEMKEY'),
                'function' => 'count(1) OVER (PARTITION BY ${1}, ${2}) ',
            )
        );

        $aliases = array(
            'CONTRACTKEY', 'CONTRACTID', 'CONTRACTNAME', 'CUSTOMERID', 'CUSTOMERNAME',
            'CONTRACTDETAILKEY', 'LINENO', 'ITEMID', 'ITEMNAME', 'ITEMCNT'
        );

        // for usage aggregation the contractdetail.billingmethod = qty based
        $filters = array(
            array('CONTRACTID', '=', $contractid),
            array('BILLINGMETHOD', '=', ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_QTY_BASED)
        );

        // if we are editing then pull in current bundles value as a column...
        $this->buildBundleValueColumn($values, $selects, $aliases);

        $querySpec = array(
            'selects' => $selects,
            'columnaliases' => $aliases,
            'filters' => array(
                $filters
            ),
            'orders' => array(
                array('CONTRACTID', 'asc'),
                array('ITEMID', 'asc'),
                array('LINENO', 'asc')
            ),
        );

        $resultSet = $cnDetailMgr->GetList($querySpec);

        // eliminate non-duplicate itemid records
        $outArray = array();
        foreach ( $resultSet as $row ) {
            if ( $row['ITEMCNT'] !== '1' ) {
                $outArray[] = $row;
            }
        }

        if ( !$outArray || count($outArray) === 0 ) {
            Globals::$g->gErr->addIAError(
                'CN-0667', GetFL(),
                sprintf(_("This contract '%s': does not have contract details defined with 'quatity based' billing method Or there are no duplicate items in contract details to group or aggregate."), $contractid),
                ['CONTRACTID' => $contractid]
            );
        }

        return $outArray;
    }

    /**
     * Upsert
     *
     * @param array $values
     *
     * @return bool
     *
     */
    public function Upsert(&$values)
    {

        // find if the given row already exists
        $querySpec = array(
            'selects' => array('RECORDNO', 'NAME', 'DESCRIPTION'),
            'filters' => array(
                array(
                    array('CONTRACTKEY', '=', $values['CONTRACTKEY']),
                    array('TYPE', '=', 'UA' ),
                    //array('CONTRACTDETAILKEY', '=', $values['CONTRACTDETAILKEY']),
                )
            ),
        );

        $resultSet = $this->GetList($querySpec);

        if ( !empty($resultSet[0]['RECORDNO']) ) {
            $values['RECORDNO'] = $resultSet[0]['RECORDNO'];
            $values['NAME'] = $resultSet[0]['NAME'];
            $values['DESCRIPTION'] = $resultSet[0]['DESCRIPTION'];
            $ok = $this->set($values);
            //if ( !empty($values['GROUPNO']) ) {
            //} else {
                // delete the record when the bundleno is empty..
                //$ok = $this->Delete($resultSet[0]['RECORDNO']);
            //}
        } else {
            $values['NAME'] = $values['DESCRIPTION'] = ContractBundleManager::BUNDLE_TYPE_USAGE_AGGREGATION;
            $ok = $this->add($values);
        }

        return $ok;
    }
}