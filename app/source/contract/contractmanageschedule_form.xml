<?xml version="1.0" encoding="UTF-8"?>
<ROOT>
    <entity>contractmanageschedule</entity>
    <title>IA.MANAGE_SCHEDULES</title>
    <view system="true">
        <events>
            <load> refreshScreen(); </load>
        </events>
        <pages id="mainPages">
            <page id="mainPage">
                <field>OFFLINE</field>
                <section id="options" isCollapsible="true" columnCount="4">
                    <title>IA.OPTIONS</title>
                    <subsection className="noborder" forceColumn="true">
                        <field>
                            <path>SCHEDULESELIGIBLETO</path>
                            <events>
                                <change>actionChange(true);</change>
                            </events>
                        </field>
                    </subsection>
                    <subsection className="noborder" forceColumn="true">
                        <field>
                            <path>ASOFDATE</path>
                        </field>
                    </subsection>
                    <field>
                        <path>TYPEFILTER</path>
                        <events>
                            <change>typeChanged();</change>
                        </events>
                    </field>
                    <field>
                        <path>JOURNALFILTER</path>
                        <events>
                            <change>typeChanged();</change>
                        </events>
                    </field>
                </section>
                <section id="filters" isCollapsible="true" showCollapsed="true" columnCount="4">
                    <title>IA.FILTERS</title>
                    <subsection className="noborder" forceColumn="true">
                        <field>
                            <path>CUSTOMERNGROUPID</path>
                            <events>
                                <change>customerGroupChange(this)</change>
                            </events>
                        </field>
                        <field>
                            <path>CUSTOMERTYPE</path>
                        </field>
                        <field>
                            <path>CONTRACTNGROUPID</path>
                            <events>
                                <change>contractGroupChange(this)</change>
                            </events>
                        </field>
                        <field>
                            <path>CONTRACTTYPE</path>
                        </field>
                        <field >
                            <path>TRANSACTIONCURR</path>
                        </field>
                    </subsection>
                    <subsection className="noborder" forceColumn="true">
                        <field>
                            <path>LOCATIONNGROUPID</path>
                            <events>
                                <change>locationGroupChange(this)</change>
                            </events>
                        </field>
                        <field>
                            <path>DEPTNGROUPID</path>
                            <events>
                                <change>deptGroupChange(this)</change>
                            </events>
                        </field>
                        <field>
                            <path>CLASSNGROUPID</path>
                            <events>
                                <change>classGroupChange(this)</change>
                            </events>
                        </field>
                        <field>
                            <path>ITEMNGROUPID</path>
                            <events>
                                <change>itemGroupChange(this)</change>
                            </events>
                        </field>
                        <field>
                            <path>REVRECTEMPLATE</path>
                        </field>
                        <field>
                            <path>EXPENSETEMPLATE</path>
                        </field>
                    </subsection>
                    <subsection className="noborder" forceColumn="true">
                        <field>
                            <path>PROJECTNGROUPID</path>
                            <events>
                                <change>projectGroupChange(this)</change>
                            </events>
                        </field>
                        <field>
                            <path>PROJECTTYPE</path>
                        </field>
                        <field>
                            <path>TASKNGROUPID</path>
                            <events>
                                <change>taskGroupChange(this)</change>
                            </events>
                        </field>
                        <field >
                            <path>PROJECTMANAGERID</path>
                        </field>
                        <field >
                            <path>EMPLOYEENGROUPID</path>
                            <events>
                                <change>employeeGroupChange(this)</change>
                            </events>
                        </field>
                    </subsection>
                    <subsection className="noborder" forceColumn="true">
                        <field>
                            <path>MAXRECCNT</path>
                        </field>
                        <row label="IA.SORT_BY_SORT_ORDER">
                            <field noLabel="true">
                                <path>SORTBY</path>
                            </field>
                            <field noLabel="true">
                                <path>SORTORDER</path>
                            </field>
                        </row>
                    </subsection>
                </section>
                <section id="dimensions" title="IA.USER_DEFINED_DIMENSIONS" dimFields="contractdetail" columnCount="4" className="columns4"
                         isCollapsible="true" showCollapsed="true">
                </section>
                <section id="PostSection" columnCount="1" isCollapsible="true" hidden="true">
                    <title>IA.POSTING_OPTIONS</title>
                    <field noLabel="true">
                        <path>POSTINGOPTIONS</path>
                        <events>
                            <change>radioCheckBoxHandler("POSTINGOPTIONS");</change>
                        </events>
                    </field>
                    <field>
                        <path>WHENCREATED</path>
                    </field>
                </section>
                <section id="HoldSection" columnCount="2" isCollapsible ="true" hidden="true">
                    <title>IA.HOLD_OPTIONS</title>
                    <child>
                        <field>
                            <path>SCHEDULESTOHOLD</path>
                            <events>
                                <change>onSchedulesToHoldChange(this);</change>
                            </events>
                        </field>
                    </child>
                    <child>
                        <field columnBreak="true">HOLDDATE</field>
                    </child>
                    <child>
                        <field>HOLDMEMO</field>
                    </child>
                </section>
                <section id="ResumeSection" columnCount="3" className="columns3" isCollapsible ="true" hidden="true">
                    <title>IA.RESUME_OPTIONS</title>
                    <child>
                        <field>
                            <path>SCHEDULESTORESUME</path>
                            <events>
                                <change>onSchedulesToResumeChange(this.meta);</change>
                            </events>
                        </field>
                    </child>
                    <child>
                        <field columnBreak="true">RESUMEDATE</field>
                    </child>
                    <child>
                        <field>RESUMEMEMO</field>
                    </child>
                    <child>
                        <field columnBreak="true">REVENUEADJUSTMENTTYPE</field>
                    </child>
                </section>
                <section id="DeliverySection" columnCount="1" isCollapsible="true" hidden="true">
                    <title>IA.DELIVER_OPTIONS</title>
                    <child>
                        <field>DELIVERYDATE</field>
                    </child>
                </section>
                <section id="results" hidden="true">
                    <grid noDragDrop="true" allowEditPage="false" className="right_border" hasFixedNumOfRows="true" noNewRows="true"
                          readOnly="true" enableSelect="true">
                        <path>SCHEDULEENTRIES</path>
                        <title>IA.REVENUE_SCHEDULES</title>
                        <selectColumn autoRedraw="true" autoUpdateSelected="true"></selectColumn>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="CUSTOMERIDHTML">
                                    <path>CUSTOMERIDHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>CUSTOMERIDHTML</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="CONTRACTNOHTML">
                                    <path>CONTRACTNOHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>CONTRACTNOHTML</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="CONTRACTLINENO">
                                    <path>CONTRACTLINENOHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field className="float_right" sortable="true">
                                <sortcolumn>CONTRACTLINENO</sortcolumn>
                                <path>CONTRACTLINENOHTML</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <type type="integer"/>
                                <path>CONTRACTLINENO</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="EXPENSELINENO">
                                    <path>EXPENSELINENOHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field className="float_right" sortable="true">
                                <sortcolumn>EXPENSELINENO</sortcolumn>
                                <path>EXPENSELINENOHTML</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <type type="integer"/>
                                <path>EXPENSELINENO</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="ITEMIDHTML">
                                    <path>ITEMIDHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>ITEMIDHTML</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="PROJECTHTML">
                                    <path>PROJECTHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>PROJECTHTML</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="POSTINGDATE" className="center">
                                    <path>POSTINGDATE_LABEL</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>POSTINGDATE</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="ACTUALPOSTINGDATE" className="center">
                                    <path>ACTUALPOSTINGDATE_LABEL</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>ACTUALPOSTINGDATE</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="AMOUNTHTML">
                                    <path>AMOUNTHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field className="float_right" sortable="true">
                                <sortcolumn>AMOUNT</sortcolumn>
                                <path>AMOUNTHTML</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <type type="decimal"/>
                                <path>AMOUNT</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="TOTALAMOUNTHTML">
                                    <path>TOTALAMOUNTHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.TOTAL_AMOUNT" className="float_right" sortable="true">
                                <sortcolumn>TOTALAMOUNT</sortcolumn>
                                <path>TOTALAMOUNTHTML</path>
                            </field>
                        </column>
                        <column>
                            <field hidden="true">
                                <type type="decimal"/>
                                <path>TOTALAMOUNT</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="TEMPLATEHTML">
                                    <path>TEMPLATEHTML_LABEL</path>
                                </field>
                            </gridHeading>
                            <field sortable="true">
                                <path>TEMPLATEHTML</path>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="LINESTARTDATE">
                                    <path>LINESTARTDATE_LABEL</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.LINE_START_DATE" readonly="true" size="1" sortable="true">
                                <path>LINESTARTDATE</path>
                                <type type="date" ptype="date"/>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="LINEENDDATE">
                                    <path>LINEENDDATE_LABEL</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.LINE_END_DATE" readonly="true" size="1" sortable="true">
                                <path>LINEENDDATE</path>
                                <type type="date" ptype="date"/>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="START_DATE">
                                    <path>START_DATE_LABEL</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.SCHEDULE_START_DATE" readonly="true" size="1" sortable="true">
                                <path>START_DATE</path>
                                <type type="date" ptype="date"/>
                            </field>
                        </column>
                        <column>
                            <gridHeading>
                                <field noLabel="true" searchPath="END_DATE">
                                    <path>END_DATE_LABEL</path>
                                </field>
                            </gridHeading>
                            <field fullname="IA.SCHEDULE_END_DATE" readonly="true" size="1" sortable="true">
                                <path>END_DATE</path>
                                <type type="date" ptype="date"/>
                            </field>
                        </column>
                        <column>
                            <field>
                                <path>SCHEDULEENTRYKEY</path>
                            </field>
                        </column>
                        <column>
                            <field>
                                <path>SCHEDULEKEY</path>
                            </field>
                        </column>
                        <lineDetails id="scheduleDetails">
                            <pages>
                                <page title="IA.DETAILS">
                                    <section id='detailsSection' title="IA.DETAILS" columnCount="4">
                                        <field fullname="IA.TASK" readonly="true" sortable="true">
                                            <path>TASKHTML</path>
                                            <type type="string"/>
                                        </field>
                                        <field fullname="IA.CUSTOMER_TYPE" readonly="true" sortable="true">
                                            <path>CUSTOMERTYPE</path>
                                            <type type="string"/>
                                        </field>
                                        <field fullname="IA.CONTRACT_TYPE" readonly="true" sortable="true">
                                            <path>CONTRACTTYPE</path>
                                            <type type="string"/>
                                        </field>
                                        <field fullname="IA.PROJECT_TYPE" readonly="true" sortable="true">
                                            <path>PROJECTTYPE</path>
                                            <type type="string"/>
                                        </field>
                                        <field fullname="IA.BASE_CURRENCY" readonly="true" sortable="true">
                                            <path>BASECURR</path>
                                            <type type="string" ptype="string"/>
                                        </field>
                                        <field fullname="IA.TRANSACTION_CURRENCY" readonly="true" sortable="true">
                                            <path>CURRENCY</path>
                                            <type type="string" ptype="string"/>
                                        </field>
                                        <field sortable="true">
                                            <path>PERCENTRECOGNIZED</path>
                                        </field>
                                        <field fullname="IA.DELIVERY_STATUS" readonly="true" sortable="true">
                                            <path>DELIVERYSTATUS_DISPLAY</path>
                                            <type type="string" ptype="string"/>
                                        </field>
                                        <field fullname="IA.DELIVERY_DATE" readonly="true" sortable="true">
                                            <path>DELIVERYDATE</path>
                                            <type type="date" ptype="date"/>
                                        </field>
                                        <field fullname="IA.SCHEDULE_STATUS" readonly="true" size="1" sortable="true">
                                            <path>SCHEDULESTATE_DISPLAY</path>
                                            <type type="string" ptype="string"/>
                                        </field>
                                        <field sortable="true">
                                            <path>COMPUTATIONMEMO</path>
                                        </field>
                                    </section>
                                </page>
                            </pages>
                        </lineDetails>
                    </grid>
                </section>
            </page>
        </pages>
    </view>
    <helpfile>Adding_Editing_and_Viewing_a_Contract_manage_schedules</helpfile>
</ROOT>
