<?xml version='1.0' encoding='UTF-8'?>
<ROOT>
    <entity>cnsetup</entity>
    <title>IA.CONFIGURE_CONTRACTS</title>
    <view system="true">
        <events>
            <load>initContractSetupPage();</load>
        </events>
        <pages>
            <page className="columnSetupPadding"  title="IA.GENERAL_CONFIGURATION">
                <section title="IA.JOURNALS" isCollapsible="true" id="journals_settings">
                    <subsection>
                        <child>
                            <field path="JOURNAL_INFO_TEXT" rightSideLabel="true" isHTML="true" noLabel="true">
                                <type type='textlabel' ptype='textlabel'></type>
                            </field>
                        </child>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection columns2" columnCount="2">
                        <child>
                        <field>
                            <path>ENABLEJOURNAL1</path>
                            <events>
                                <change>onEnableJournal(1, false);</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field>
                            <path>REVENUEJOURNAL1</path>
                            <events>
                                <change>onChangeJournalValue();</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field>
                            <path>EXPENSEJOURNAL1</path>
                            <events>
                                <change>onChangeJournalValue();</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field>
                            <path>SHOWJOURNAL1</path>
                            <events>
                                <change>onShowJournal(1, true);</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field fullname="" noLabel="1" isHTML="1">
                            <path>SHOWJOURNALHELP1</path>
                            <type type='msgcomp' ptype='msgcomp'></type>
                            <infoText>IA.THIS_HIDES_JOURNAL_1_AND_RESTRICTS_IT_FROM</infoText>
                        </field>
                        </child>
                        <child>
                        <field columnBreak="true">
                            <path>ENABLEJOURNAL2</path>
                            <events>
                                <change>onEnableJournal(2, false);</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field>
                            <path>REVENUEJOURNAL2</path>
                            <events>
                                <change>onChangeJournalValue();</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field>
                            <path>EXPENSEJOURNAL2</path>
                            <events>
                                <change>onChangeJournalValue();</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field>
                            <path>SHOWJOURNAL2</path>
                            <events>
                                <change>onShowJournal(2, true);</change>
                            </events>
                        </field>
                        </child>
                        <child>
                        <field noLabel="1" fullname="" isHTML="1">
                            <path>SHOWJOURNALHELP2</path>
                            <type type='msgcomp' ptype='msgcomp'></type>
                            <infoText>IA.THIS_HIDES_JOURNAL_2_AND_RESTRICTS_IT_FROM</infoText>
                        </field>
                    </child>
                    </subsection>
                </section>
                <section title="IA.CURRENCY" isCollapsible="true" id="currency_settings">
                    <subsection className="subSection qx-cfg-subsection">
                        <field>EXCHRATETYPE</field>
                    </subsection>
                </section>
                <section title="IA.BILLING" isCollapsible="true" id="billing_settings">
                    <subsection className="subSection qx-cfg-subsection" id="termtype_option" hidden="true">
                        <field path="TERMTYPE_OPTION">
                            <events>
                                <change>showHideEvergreenUsageCreationOption();</change>
                            </events>
                        </field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection">
                        <field>
                            <path>DEFAULT_TD</path>
                        </field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection" id="sched_inv_policies">
                        <field>
                            <path>ENABLE_SCHED_INV_POLICIES</path>
                            <events>
                                <change>enableSchedInvPoliciesChange(this.meta);</change>
                            </events>
                        </field>
                        <field>
                            <path>SCHED_INV_POLICIES_EMAIL</path>
                        </field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection">
                        <field>
                            <path>ENABLE_USAGEBILLING</path>
                             <events>
                                 <change>showHideEvergreenUsageCreationOption();</change>
                             </events>
                        </field>
                        <field hidden="true">
                            <path>SPLIT_TIERS</path>
                        </field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection" id="evergreen_gl_post_section">
                        <title>IA.EVERGREEN_CONTRACTS</title>
                        <field>
                            <path>RELAX_EVERGREEN_GL_POST</path>
                        </field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection" id="evergreen_usage_creation_option_section">
                        <field>
                            <path>EVERGREEN_USAGE_CREATION_OPTION</path>
                        </field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection" title="IA.BILLING_SCHEDULES">
                        <field>OVERRIDEGLPOSTFLAG</field>
                    </subsection>
                </section>
                <section title="IA.REVENUE" isCollapsible="true" id="revenue_settings">

                </section>
                <section title="IA.DOCUMENT_SEQUENCING" isCollapsible="true" id="document_sequencing">
                    <subsection className="subSection qx-cfg-subsection">
                        <field path="CONTSEQUENCEID">
                            <fullname>IA.CONTRACT_ID_SEQUENCE</fullname>
                        </field>
                       <field path="SUBSCRIBED" hidden="true"/>
                    </subsection>
                </section>
                <section title="IA.HISTORICAL_CONTRACTS" isCollapsible="true" id="historical_contracts">
                    <subsection className="subSection qx-cfg-subsection">
                        <row label=" ">
                            <field path="ALLOWPASTDATED" rightSideLabel="true"
                                   fullname="IA.ALLOW_IMPORT_CREATION_OF_CONTRACTS_WI">
                                <helpText>IA.SELECT_THIS_OPTION_BEFORE_IMPORTING_OR_CREATING</helpText>
                            </field>
                        </row>
                    </subsection>
                </section>
                <section title="IA.IMPORTED_CONTRACTS_PROCESSING" isCollapsible="true" id="contracts_acp">
                    <subsection className="subSection qx-cfg-subsection">
                        <row label=" ">
                            <field path="ACP_TD" fullname="IA.TRANSACTION_DEFINITION"/>
                        </row>
                    </subsection>
                </section>
            </page>
            <page id="postingConfigPage" title="IA.POSTING_CONFIGURATION">
                <child>
                    <section title="IA.REVENUE_POSTING" isCollapsible="true" id="revPosting">
                        <grid>
                            <showDelete>true</showDelete>
                            <entity>contractrevenueglconfig</entity>
                            <path>REVPOSTING</path>
                            <column>
                                <field>
                                    <path>ITEMGLGROUP</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>CUSTOMERGLGROUP</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>ARUNBILLEDACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>ARBILLEDACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>DRUNBILLEDACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>DRBILLEDACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>DRPAIDACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>SALESUNBILLEDACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>SALESBILLEDACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>SALESPAIDACCTNO</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                    <section title="IA.EXPENSE_POSTING" id="expPosting" isCollapsible="true">
                        <grid>
                            <showDelete>true</showDelete>
                            <entity>contractexpenseglconfig</entity>
                            <path>EXPPOSTING</path>
                            <column>
                                <field>
                                    <path>ITEMGLGROUP</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>CUSTOMERGLGROUP</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>DEFERREDEXPENSEACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>RECOGNIZEDEXPENSEACCTNO</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>EXPENSEACCRUALACCTNO</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </child>
            </page>
            <page id="invPoliciesConfigPage" title="IA.INVOICE_POLICIES_CONFIGURATION" hidden="true">
                <child>
                    <section title="IA.SPECIFY_INVOICE_POLICY_ORDER_OF_PRECE" isCollapsible="false" id="precedenceSection">
                        <grid deleteOnGrid="false" showDelete="false" noDragDrop="true" noNewRows="true" hideLineNo="true" readonly="true">
                            <entity>geninvoicepolicyconfig</entity>
                            <path>POLICYCONFIGS</path>
                            <column>
                                <field>
                                    <path>ENTITYNAME</path>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>INVOICEPOLICIESHTML</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </child>
            </page>
        </pages>
        <floatingPage modalsize="medium">
            <title>IA.ENABLE_SCHEDULED_INVOICE_POLICIES</title>
            <id>enableSchedInvPoliciesWarning</id>
            <pages>
                <page className="noborder">
                    <section>
                        <field noLabel="true" path="DUMMY">
                            <type type="textlabel" ptype="textlabel"></type>
                            <default>IA.AFTER_SCHEDULED_INVOICE_POLICIES_ARE_ENABLED</default>
                        </field>
                        <field noLabel="true" path="DUMMY">
                            <type type="textlabel" ptype="textlabel"></type>
                            <default>IA.DO_YOU_WANT_TO_CONTINUE</default>
                        </field>
                    </section>
                </page>
            </pages>

            <footer>
                <button>
                    <name>IA.YES</name>
                    <events>
                        <click>saveContractConfiguration();</click>
                    </events>
                </button>
                <button>
                    <name>IA.NO</name>
                    <events>
                        <click>hideEnableSchedInvPoliciesWarning();</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
    </view>
    <helpfile>Setting_Up_the_Contract_Application</helpfile>
</ROOT>
