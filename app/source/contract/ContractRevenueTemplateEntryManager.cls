<?php
/**
 * File ContractRevenueTemplateEntryManager.cls contains the class ContractRevenueTemplateEntryManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * CRUD handler for Revenue template entry
 *
 * Class ContractRevenueTemplateEntryManager
 */
class ContractRevenueTemplateEntryManager extends OwnedObjectManager
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Validates attributes
     *
     * @param array $values Array of attributes
     *
     * @return bool
     */
    protected function ValidateRecord(&$values)
    {
        $ok = parent::ValidateRecord($values);
        if (!$ok) {
            return false;
        }

        if (strpos($values['PERIODOFFSET'], ".") !== false) {
            Globals::$g->gErr->addError('CN-0676', __FILE__ . ':' . __LINE__,
                "Period offset cannot be a decimal number"
            );
            $ok = false;
        }

        if ($values['PERIODOFFSET'] > 1000) {
            Globals::$g->gErr->addError('CN-0677', __FILE__ . ':' . __LINE__,
                "Period offset cannot be > 1000"
            );
            $ok = false;
        }

        return $ok;
    }
}