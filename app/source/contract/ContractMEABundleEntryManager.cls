<?php
/**
 * File ContractMEABundleEntryManager.cls contains the class ContractMEABundleEntryManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * CRUD handler for Contract MEA Bundle Entries
 *
 * Class ContractMEABundleEntryManager
 */
class ContractMEABundleEntryManager extends ContractBundleEntryManager
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
}