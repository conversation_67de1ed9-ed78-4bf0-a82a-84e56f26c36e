<?php
/**
 * File ContractPaymentScheduleEntryManager.cls contains the class ContractPaymentScheduleEntryManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * CRUD handler for Payment schedule entry
 *
 * Class ContractPaymentScheduleEntryManager
 */
class ContractPaymentScheduleEntryManager extends ContractScheduleEntryManager
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
}
