<?xml version='1.0' encoding='UTF-8'?>
<ROOT assoc="T">
    <entity>contractmeaitempricelist</entity>
    <title>IA.MEA_ITEM_PRICING_INFORMATION</title>
    <helpfile>Adding_a_FairValue_Price_Entry</helpfile>
    <view system='true'>
        <events>
            <load>RefreshValueTypeInformation();</load>
        </events>
        <pages>
            <page id="mainPage">
                <section columnCount="2">
                    <child>
                        <field>
                            <path>RECORDNO</path>
                        </field>
                        <field>
                            <path>PRICELISTNAME</path>
                        </field>
                        <field>
                            <path>ITEMID</path>
                        </field>
                        <field>
                            <path>CURRENCY</path>
                        </field>
                        <field path="ITMPRCLSTTYP">
                            <events>
                                <!-- <change>SetNoCheckRequired();RefreshValueTypeInformation();clearGridValues();</change>-->
                                <change>RefreshValueTypeInformation();clearGridValues();</change>
                            </events>
                        </field>
                        <field>
                            <path>PERCENTOF</path>
                        </field>
                        <field>
                            <path>PERCENTBASE_VIEW</path>
                        </field>

                        <field>
                            <path>USEPRICEBANDS</path>
                            <events>
                                <change>RefreshPriceBandsFields(this);reCalculatePriceBands(this,false)</change>
                            </events>
                        </field>
                        <field>
                            <path>PRICEBANDTYPE</path>
                            <events>
                                <change>reCalculatePriceBands(this,false)</change>
                            </events>
                        </field>
                        <field>
                            <path>PRICEBANDRULE</path>
                        </field>
                        <field>
                            <path>STATUS</path>
                        </field>
                    </child>
                </section>
                <section>
                    <grid>
                        <path>PRICELISTENTRY</path>
                        <title>IA.ENTRIES</title>
                        <column>
                            <field path="STARTDATE"/>
                        </column>
                        <column>
                            <field path="VALUE">
                                <events>
                                    <change>reCalculatePriceBands(this,true);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field path="MARKDOWN">
                                <events>
                                    <change>reCalculatePriceBands(this,true);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field path="MARKUP">
                                <events>
                                    <change>reCalculatePriceBands(this,true);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field path="BANDDOWN">
                                <events>
                                    <change>reCalculatePriceBands(this,true);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field path="BANDUP">
                                <events>
                                    <change>reCalculatePriceBands(this,true);</change>
                                </events>
                            </field>
                        </column>
                        <column>
                            <field path="MEMO"/>
                        </column>
                    </grid>
                </section>
            </page>
        </pages>
    </view>
</ROOT>
