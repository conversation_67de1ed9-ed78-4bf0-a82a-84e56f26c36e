<?php
/**
 * File ContractTemplateBasedSchedule.cls contains the class ContractTemplateBasedSchedule
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Abstract base class for all contract templates
 *
 * Class ContractTemplateBasedSchedule
 */
abstract class ContractTemplateBasedSchedule extends ContractSchedule
{
    public static int|null $schOpLocationKey;   // If set, it overrides locationKey when creating scheduler operation

    /**
     * Creates schedule based on parameters
     *
     * @param ContractScheduleCreateParams $params value object representing required attributes
     *
     * @return bool
     *
     * @throws IAException
     */
    public function create(ContractScheduleCreateParams $params)
    {
        $this->createParams = $params;
        $schedule = $this->generateSchedule();
        $this->generateScheduleEntries();

        if ($this->createParams->isGetProrateBillingDataFlow()) {
            // We just need projected prorate billing data.
            // No need to add schedule entries details to DB.
            return true;
        }

        $billingSch = $this->createParams->getBillingSch();
        $revSchEntryList = $this->contractScheduleInfo->getEntries();

        if($this->createParams->isBillRevSchLinesParity() && !empty($billingSch)) {
            // Process billing and revenue schedule lines parity.

            $billSchEntryList = $billingSch->getContractScheduleInfo()->getEntries();
            $this->billingRevenueScheduleLinesParity($billSchEntryList, $revSchEntryList);
        }

        $source = 'ContractTemplateBasedSchedule::create()';
        XACT_BEGIN($source);

        try {

            $this->createSchedule($schedule, $revSchEntryList);

            $isPostingTypeAutomatic = $this->createParams->isPostingTypeAutomatic();
            $postingConversionDate = $this->createParams->getPostingConversionDate();

            if ($isPostingTypeAutomatic) {
                $pending = in_array($this->contractScheduleInfo->getState(), [ContractScheduleState::STATE_PENDINGDELIVERY, ContractScheduleState::STATE_PENDINGDELIVERYALL]);
                foreach ($this->contractScheduleInfo->getEntries() as $entry) {
                    if ( $entry->isPostable() === true ) {
                        $entryEMValues = $entry->getValues();
                        if ($pending) {
                            $entryEMValues['_pending'] = true;
                        }
                        // If posting date has been specified by converting the posting type use that to override the template
                        if ($postingConversionDate !== null) {
                            if (DateCompare($entry->getPostingDate(), $postingConversionDate) >= 0) {
                                $this->createScheduledOperation($entryEMValues);
                            }
                        } else {
                            $this->createScheduledOperation($entryEMValues);
                        }
                    }
                }
            }
        } catch(IAException $ex) {
            XACT_ABORT($source);
            throw $ex;
        }

        $ok = XACT_COMMIT($source);
        if (!$ok) {
            XACT_ABORT($source);
        }

        return $ok;
    }

    /**
     * Billing and revenue schedule lines parity.
     *
     * @param ContractScheduleEntry[] $billSchEntryList Billing schedule entry.
     * @param ContractScheduleEntry[] $revSchEntryList Revenue schedule entry.
     */
    private function billingRevenueScheduleLinesParity(&$billSchEntryList, &$revSchEntryList)
    {
        if (empty($billSchEntryList) || empty($revSchEntryList) ) {
            return;
        }

        $billSchEntryCount = count($billSchEntryList);

        if ($billSchEntryCount != count($revSchEntryList)) {
            // Billing and revenue schedule entry count don't match.
            // Schedule lines parity is not possible.

            return;
        }

        for ($i = 0; $i < $billSchEntryCount; $i++) {
            $billSchEntry = $billSchEntryList[$i];
            $revSchEntry = $revSchEntryList[$i];

            $revSchEntry->setAmount($billSchEntry->getAmount());
            $revSchEntry->setBaseAmount($billSchEntry->getBaseAmount());

            if ($i == 0 || $i == ($billSchEntryCount - 1)) {
                // First or last schedule entry, modify the computation memo.

                $revSchEntry->setComputationMemo($billSchEntry->getComputationMemo());
            }
        }

        $this->contractScheduleInfo->setEntries($revSchEntryList);
    }

    /**
     * Creates scheduled operation for cron job
     *
     * @param array $entry schedule entry
     *
     * @return bool|true
     *
     * @throws IAException
     */
    public function createScheduledOperation(&$entry)
    {
        $ok = true;

        if ($entry['POSTABLE'] == 'false') {
            throw new IAException("Attemp to create schedule operation for non postable entry.", 'CN-1545');
        }

        if (isArrayValueFalse($entry, 'HISTORICAL')) {
            $schedMgr = Globals::$g->gManagerFactory->getManager('schedule');
            $operMgr = Globals::$g->gManagerFactory->getManager('operation');
            $schedOperMgr = Globals::$g->gManagerFactory->getManager('scheduledoperation');

            /* @var ContractScheduleEntryManager $entryMgr */
            $entryMgr = Globals::$g->gManagerFactory->getManager($this->getScheduleEntryEntity());

            // do not activate schedule if item is pending delivery
            $isActive = ! (isset($entry['_pending']) && $entry['_pending']);

            // Schedule
            $schedule = $this->createSchedulerSchedule($entry, $isActive);
            $ok = $schedMgr->add($schedule);

            // Operation
            $operation = $this->createSchedulerOperation($entry, $schedule);
            $ok = $ok && $operMgr->add($operation);

            // Scheduled Operation
            $schedop = $this->createSchedulerScheduledOperation($schedule, $operation);
            $ok = $ok && $schedOperMgr->add($schedop);

            // Set Schop key in entry
            $ok = $ok && $entryMgr->updateSchopKey($entry['RECORDNO'], $schedop[':record#']);
            //$ok = $ok && $entryMgr->updateSchopKey($entry->getRecordNo(), $schedop[':record#']);

            if ( ! $ok) {
                $gErr = Globals::$g->gErr;
                $msg = "Unable to create contract schedule";
                $gErr->addError('CN-0453', __FILE__ . ':' . __LINE__, $msg);
                throw new IAException($msg, 'CN-0453');
            }
        }

        return $ok;
    }

    /**
     * @param array $entry
     * @return bool
     */
    public function deleteAndCreateScheduledOperation(&$entry) {
        $ok = true;
        if (isArrayValueFalse($entry, 'HISTORICAL')) {
            $schedMgr = Globals::$g->gManagerFactory->getManager('schedule');
            $operMgr = Globals::$g->gManagerFactory->getManager('operation');
            $schedOperMgr = Globals::$g->gManagerFactory->getManager('scheduledoperation');
            $schedEntryMgr = Globals::$g->gManagerFactory->getManager('contractrevenuescheduleentry');

            $schopKey = $entry['SCHOPKEY'];
            $schOpData = null;
            if ($schopKey != null) {
                $schOpData = $schedOperMgr->GetList([
                    'filters' => [
                        [
                            ['RECORDNO', '=', $schopKey]
                        ]
                    ]
                ]);
            }

            if ($ok && $schOpData != null && count($schOpData) == 1) {
                $opKey = $schOpData[0]['OPERATIONNO'];
                $schKey = $schOpData[0]['SCHEDULENO'];
                $schData = $schedMgr->GetList([
                    'filters' => [
                        [
                            ['RECORDNO', '=', $schKey]
                        ]
                    ]
                ]);
                $operData = $operMgr->GetList([
                    'filters' => [
                        [
                            ['RECORDNO', '=', $opKey]
                        ]
                    ]
                ]);
                if (count($schData) == 1 && DateCompare($entry['POSTINGDATE'],$schData[0]['STARTDATE']) != 0) {
                    $ok = $ok && $schedEntryMgr->clearSchopKey($entry['RECORDNO']);
                    $ok = $ok && $schedOperMgr->Delete($schOpData[0]['NAME']);
                    $ok = $ok && $operMgr->Delete($operData[0]['NAME']);
                    $ok = $ok && $schedMgr->Delete($schData[0]['NAME']);
                    $entry['SCHOPKEY'] = null;
                    $ok = $ok && $this->createScheduledOperation($entry);
                }
            } else {
                $ok = $ok && $this->createScheduledOperation($entry);
            }
        }
        return $ok;
    }
    /**
     * Creates intacct scheduler schedule
     *
     * @param array $entry    Schedule entry attributes
     * @param bool  $isActive initial status of schedule - false if recognition is pending
     *
     * @return array
     */
    protected function createSchedulerSchedule($entry, $isActive = true)
    {
        $cny = GetMyCompany();
        $curdate = GetCurrentDate();

        $date = getdate();
        extract($date);
        /** @noinspection PhpUndefinedVariableInspection */
        $timestamp = "$year.$mon.$mday $hours:$minutes:$seconds";

        $startdate = $entry['POSTINGDATE'];
        //$startdate = $entry->getPostingDate();

        $type = $this->getType();

        // Fill up schedule values
        $schedule = array();
        $schedule['NAME'] = "Automatic Contract $type - $timestamp " . $entry['RECORDNO'];  // adding entry record# to ensure name uniqueness
        $schedule['CNY#'] = $cny;
        $schedule['WHENCREATED'] = $curdate;
        $schedule['DESCRIPTION'] = "Automatic Contract $type";
        $schedule['STATUS'] = $isActive ? 'active' : 'inactive';
        $schedule['STARTDATE'] = $startdate;
        $schedule['LASTEXECDATE'] = '';
        $schedule['EXECCOUNT'] = 0;
        $schedule[':nextexecdate'] = $startdate;

        $schedule['REPEATCOUNT'] = '1';
        $schedule['REPEATINTERVAL'] = '1';
        $schedule['REPEATBY'] = 'None';

        // Scheduled to run by daily script
        $schedule['EXECTYPE'] = 'Automatic';
        $execTime = GetCurrentDate();
        if (SysDateCompare($startdate, $execTime) < 0 && $entry['noImmediate'] === null ) {
            $schedule['EXECTYPE'] = 'Immediate';
        }

        return $schedule;
    }

    /**
     * Creates intacct scheduler operation
     *
     * @param array $entry    schedule entry attributes
     * @param array $schedule intacct schedule attributes
     *
     * @return array
     */
    protected function createSchedulerOperation($entry, $schedule)
    {
        $bodydata = array();
        $bodydata['SCHEDULETYPE'] = $this->getType();
        $bodydata['SCHEDULEENTRYKEY'] = $entry['RECORDNO'];
        //$bodydata['SCHEDULEENTRYKEY'] = $entry->getRecordNo();
        $bodydata['SCHEDULEKEY'] = $entry['SCHEDULEKEY'];
        //$bodydata['SCHEDULEKEY'] = $entry->getScheduleKey();

        $operation = array();
        $operation['CNY#'] = $schedule['CNY#'];
        $operation['NAME'] = $schedule['NAME'];
        $operation['DESCRIPTION'] = $schedule['DESCRIPTION'];
        $operation['ENTITY'] = 'CONTRACTSCHEDULE';
        $operation['ACTION'] = 'POST';
        $operation['BODYDATA'] = $bodydata;
        $operation['STATUS'] = 'active';
        $operation['LOCATIONKEY'] = self::$schOpLocationKey ?? Profile::getProperty('LOCATIONKEY');

        return $operation;
    }

    /**
     * Creates intacct scheduler scheduled operation
     *
     * @param array $schedule  Intacct schedule attributes
     * @param array $operation Intacct scheduled operation attributes
     *
     * @return array
     */
    protected function createSchedulerScheduledOperation($schedule, $operation)
    {

        $schedKey = $schedule[':record#'];
        $operKey = $operation[':record#'];

        $schedop = array();
        $schedop['NAME'] = $schedule['NAME'];
        $schedop['OPERATION']['NAME'] = $schedule['NAME'];
        $schedop['SCHEDULE']['NAME'] = $schedule['NAME'];
        $schedop['DESCRIPTION'] = $schedule['DESCRIPTION'];
        $schedop['CNY#'] = $schedule['CNY#'];
        $schedop['STATUS'] = 'active';
        $schedop['SCHEDULE#'] = $schedKey;
        $schedop['OPERATION#'] = $operKey;
        $schedop[':schedule#'] = $schedKey;
        $schedop[':operation#'] = $operKey;

        return $schedop;
    }

    public function createBalancingEntryForSchedule()
    {
        //Revenue and billing schedules would override this method.
    }

    /**
     * Return required fields from contract detail
     *
     * @param  int  $contractDetailKey
     *
     * @return array
     *
     * @throws IAException
     */
    protected function getRequiredContractDetail($contractDetailKey)
    {
        $selects = [
            'CURRENCY', 'EXCH_RATE_TYPE_ID', 'EXCH_RATE_DATE', 'EXCHANGE_RATE', 'CONTRACTKEY', 'ITEMKEY', 'CONTRACT.CUSTOMERNAME',
            'REVENUETEMPLATENAME', 'REVENUE2TEMPLATENAME','TOTALFLATAMOUNT', 'TOTALBASEFLATAMOUNT', 'ENDDATE',
            'REVENUEJOURNAL', 'REVENUE2JOURNAL', 'ARUNBILLEDACCTNO', 'ARUNBILLEDACCTKEY',
            'ARBILLEDACCTNO', 'ARBILLEDACCTKEY', 'DRUNBILLEDACCTNO', 'DRUNBILLEDACCTKEY', 'DRBILLEDACCTNO',
            'DRBILLEDACCTKEY', 'DRPAIDACCTNO', 'DRPAIDACCTKEY', 'SALESUNBILLEDACCTNO', 'SALESUNBILLEDACCTKEY',
            'SALESBILLEDACCTNO', 'SALESBILLEDACCTKEY', 'SALESPAIDACCTNO', 'SALESPAIDACCTKEY',
            'LOCATIONKEY', 'LOCATIONID', 'DEPTKEY', 'DEPARTMENTID','BILLINGMETHOD','USAGELINETYPE'
        ];

        // Get GL dimensions
        $gleMgr = Globals::$g->gManagerFactory->getManager('glentry');
        $dimFields = $gleMgr->getDimFieldNames(true, ['CONTRACTDIMKEY', 'ITEMDIMKEY']);

        $selects = array_merge($selects, $dimFields);

        $contractDetail = EntityManager::GetListQuick('contractdetail', $selects, ['RECORDNO' => $contractDetailKey]);
        if (!$contractDetail[0]) {
            $msg = 'Invalid Contract detail key:'.$contractDetailKey;
            throw IAException::newIAException( 'CN-1546', $msg, ['CONTRACT_DETAIL_KEY' => $contractDetailKey]);
        }

        $contractDetail = $contractDetail[0];
        $contractDetail['CUSTOMERNAME'] = $contractDetail['CONTRACT.CUSTOMERNAME'];
        $contractDetail['CONTRACTDIMKEY'] = $contractDetail['CONTRACTKEY'];
        $contractDetail['ITEMDIMKEY'] = $contractDetail['ITEMKEY'];
        $contractDetail['CONTRACTDETAILKEY'] = $contractDetailKey;
        return $contractDetail;
    }
}