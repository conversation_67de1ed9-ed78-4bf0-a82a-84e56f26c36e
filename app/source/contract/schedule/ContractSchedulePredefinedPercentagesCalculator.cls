<?php
/**
 *    FILE: ContractSchedulePredefinedPercentagesCalculator.cls
 *    AUTHOR: <EMAIL>
 *    DESCRIPTION:
 *
 *    (C) 2016, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class ContractSchedulePredefinedPercentagesCalculator extends BaseContractScheduleEntryCalculator
{
    /**
     * @return ContractScheduleEntry[]
     * @throws IAException
     */
    protected function innerCalculateEntries()
    {
        $templateObject = $this->contractTemplate;
        $amt = $this->scheduleEntryParams->getAmount();
        $baseAmt = $this->scheduleEntryParams->getBaseAmount();
        $scheduleStartDate = $this->startDate;
        $scheduleEndDate = $this->endDate;
        $exchangeRate = $this->exchRate;
        
        $entries = array();
        if ( $templateObject !== null && ($templateObject instanceof ContractRevenueTemplate ||
                $templateObject instanceof ContractExpenseTemplate) ) {
            $templateEntries = $templateObject->getEntries();
            foreach ( $templateEntries as $oneEntry ) {
                $periodOffset = $oneEntry['PERIODOFFSET'];
                $periodPercent = $oneEntry['PERIODPERCENT'];
                $lineDate = AddMonths($scheduleStartDate, $periodOffset);
                $lineDate = GetLastDateOfMonth($lineDate);
                if (DateCompare($lineDate, $scheduleEndDate) > 0) {
                    $msg = "The template date $lineDate is after the schedule end date 
                    $scheduleEndDate for period $periodOffset.";
                    throw IAException::newIAException(
                        'CN-1542',
                        $msg,
                        [
                            'LINE_DATE' => FormatDateForDisplay($lineDate),
                            'SCHEDULE_END_DATE' => FormatDateForDisplay($scheduleEndDate),
                            'PERIOD_OFFSET' => $periodOffset,
                        ]
                    );
                }
                $scheduleAmount = ContractUtil::multiplyTwoAmounts($amt,
                    ContractUtil::divideTwoAmounts($periodPercent, '100')
                );
                $scheduleBaseAmount = ContractUtil::multiplyTwoAmounts($scheduleAmount, $exchangeRate);
                $thisEntry = $this->getContractScheduleEntryObject($scheduleAmount, $scheduleBaseAmount, $exchangeRate,
                    $lineDate);
                $entries[] = $thisEntry;
            }
        }
        ContractUtil::correctObjectRoundingErrors($amt, $entries);
        ContractUtil::correctObjectRoundingBaseAmountErrors($baseAmt, $entries);

        return $entries;
    }

    /**
     * @param float  $meaAmount
     * @param float  $meaBaseAmount
     * @param string $effectiveDate
     * @param string $adjustmentType
     */
    protected function innerAdjustEntries($meaAmount, $meaBaseAmount, $effectiveDate, $adjustmentType)
    {
        $resultEntries = [];
        $adjustmentEntries = [];
        $sumOfAdjustmentEntries = 0;
        $baseSumOfAdjustmentEntries = 0;

        $scheduleInfo = $this->scheduleInfo;
        $scheduleEntries = $scheduleInfo->getEntries();
        $contractDetail = $this->contractDetail;
        $totalFlatAmount = $contractDetail->getTotalFlatAmount();
        $exchangeRate = $contractDetail->getExchangeRate();
        
        if ($effectiveDate == $contractDetail->getGlPostingDate()) {
            // one time and distributed are the same in this case
            if ($scheduleEntries != null) {
                $this->handleMEASameDate($resultEntries, $scheduleEntries, $totalFlatAmount, $meaAmount, $exchangeRate);
            } else {
                // This must be the 0 amount line, so create entries
                $resultEntries = $this->createScheduleEntries($meaAmount, false, $effectiveDate);
            }
        } else {
            $entriesOnAndAfterEffectiveDate = [];
            $entriesBeforeEffectiveDate = [];
            $sumOfAmountBeforeEffectiveDate = 0;
            $baseSumOfAmountBeforeEffectiveDate = 0;
            $entryState = ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_ADJUSTED;

            if ($scheduleEntries == null) {
                // This must be the 0 amount line, so create entries
                $scheduleEntries = $this->createScheduleEntries($meaAmount, true, $effectiveDate);
                $entryState = ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_NEW;
            }

            // Get the schedule entries before and after the effective date since they will be needed later on for processsing
            foreach ($scheduleEntries as & $oneEntry) {
                $postingDate = $oneEntry->getPostingDate();
                $compareResult = DateCompare($postingDate, $effectiveDate);
                if ( $compareResult >= 0 ) {
                    if ($oneEntry->getAmount() != 0) {
                        $entriesOnAndAfterEffectiveDate[] = $oneEntry;
                    } else {
                        $oneEntry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_DELETED_NO_UNPOST);
                    }
                } else {
                    $entriesBeforeEffectiveDate[] = $oneEntry;
                    $sumOfAmountBeforeEffectiveDate = ContractUtil::addTwoAmounts($sumOfAmountBeforeEffectiveDate, $oneEntry->getAmount());
                    $baseSumOfAmountBeforeEffectiveDate = ContractUtil::addTwoAmounts($baseSumOfAmountBeforeEffectiveDate, $oneEntry->getBaseAmount());
                }
            }
            unset($oneEntry);

            $innerAllocation = $meaAmount;
            if ($meaAmount == 0 && $sumOfAmountBeforeEffectiveDate != 0) {
                $innerAllocation = ContractUtil::multiplyTwoAmounts($sumOfAmountBeforeEffectiveDate, -1);
            }

            // Calculate the adjustment amount that needs to be applied when MEA is being applied. The adjustment amount
            // is the same for one time and distributed.
            // For one time we will create a new entry with the adjustment amount and for distributed we will distribute
            // the adjustment amount over the entries after the effective date.
            if (count($entriesOnAndAfterEffectiveDate) > 0) {
                foreach ($entriesOnAndAfterEffectiveDate as $oneEntry) {
                    $sourceAmount = $oneEntry->getSourceQty();
                    if ($sourceAmount == null) {
                        $sourceAmount = $totalFlatAmount;
                    }
                    // calculate percentages before appying mea
                    $oneEntryPercentage = ContractUtil::divideTwoAmounts($oneEntry->getAmount(), $sourceAmount);

                    $oneEntryScheduleAmount = ContractUtil::multiplyTwoAmounts($innerAllocation, $oneEntryPercentage);
                    $oneEntryBaseScheduleAmount = ContractUtil::multiplyTwoAmounts($oneEntryScheduleAmount, $exchangeRate);
                    $this->setScheduleEntry($oneEntry, $oneEntryScheduleAmount, $oneEntryBaseScheduleAmount, $meaAmount, $entryState);
                    $adjustmentEntries[] = $oneEntry;

                    $sumOfAdjustmentEntries = ContractUtil::addTwoAmounts($sumOfAdjustmentEntries, $oneEntryScheduleAmount);
                    $baseSumOfAdjustmentEntries = ContractUtil::addTwoAmounts($baseSumOfAdjustmentEntries, $oneEntryBaseScheduleAmount);
                }
                unset($oneEntry);
            } else {
                $scheduleTemplate = $this->scheduleInfo->getScheduleTemplate();
                if ($scheduleTemplate == null) {
                    //throw error here
                    throw new IAException("Template information not found.", 'CN-1543');
                }
                //create the schedule entries from the template
                $templateEntries = $scheduleTemplate->getEntries();
                $scheduleStartDate = $this->scheduleInfo->getStartDate();
                foreach ($templateEntries as $oneEntry) {
                    $periodOffset = $oneEntry['PERIODOFFSET'];
                    $periodPercent = $oneEntry['PERIODPERCENT'];
                    $lineDate = AddMonths($scheduleStartDate, $periodOffset);
                    $lineDate = GetLastDateOfMonth($lineDate);
                    if (DateCompare($lineDate,$effectiveDate) >= 0) {
                        $scheduleAmount = ContractUtil::multiplyTwoAmounts($innerAllocation,
                            ContractUtil::divideTwoAmounts($periodPercent,100)
                        );
                        $scheduleBaseAmount = ContractUtil::multiplyTwoAmounts($scheduleAmount, $exchangeRate);
                        $sumOfAdjustmentEntries = ContractUtil::addTwoAmounts($sumOfAdjustmentEntries, $scheduleAmount);
                        $baseSumOfAdjustmentEntries = ContractUtil::addTwoAmounts($baseSumOfAdjustmentEntries, $scheduleBaseAmount);
                        $thisEntry = $this->getContractScheduleEntryObject($scheduleAmount, $scheduleBaseAmount, $exchangeRate, $lineDate);
                        $thisEntry->setScheduleKey($this->scheduleInfo->getRecordNo());
                        $thisEntry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_NEW);
                        $thisEntry->setPostable( true);
                        $adjustmentEntries[] = $thisEntry;
                    }
                }
            }

            $scheduleSubTotalAmount = ContractUtil::addTwoAmounts($sumOfAmountBeforeEffectiveDate, $sumOfAdjustmentEntries);
            $baseScheduleSubTotalAmount = ContractUtil::addTwoAmounts($baseSumOfAmountBeforeEffectiveDate, $baseSumOfAdjustmentEntries);

            $adjustmentAmount = ContractUtil::subtractTwoAmounts($meaAmount, $scheduleSubTotalAmount);
            $baseAdjustmentAmount = ContractUtil::subtractTwoAmounts($meaBaseAmount, $baseScheduleSubTotalAmount);

            $sumOfAdjustmentEntries = ContractUtil::addTwoAmounts($sumOfAdjustmentEntries, $adjustmentAmount);
            $baseSumOfAdjustmentEntries = ContractUtil::addTwoAmounts($baseSumOfAdjustmentEntries, $baseAdjustmentAmount);


            //do not create adjustment entries only if can be handled with rounding code
            //other wise let there be an onetime adjustment entry
            // If the MEA effective date is after the last schedule entry then do a one time catch up always even if
            // the user selected distributed
            $createOneTime = (abs($adjustmentAmount) > 1 || count($adjustmentEntries) == 0);
            if ($createOneTime == true && ($adjustmentType === ContractMEAScheduleAdjustmentType::CATCH_UP_ONETIME
                    || empty($entriesOnAndAfterEffectiveDate)) && $adjustmentAmount != 0) {
                // Create a new entry with the adjustment amount
                $adjustmentEntry = new ContractScheduleEntry($adjustmentAmount, $baseAdjustmentAmount, $exchangeRate, $effectiveDate);
                $adjustmentEntry->setScheduleKey($scheduleInfo->getRecordNo());
                $adjustmentEntry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_NEW);
                $adjustmentEntry->setPostable(true);
                $adjustmentEntry->setSourceQty($meaAmount);

                // Clear any previous memo entries after the current MEA effective date
                foreach ($entriesOnAndAfterEffectiveDate as $oneEntry) {
                    $oneEntry->setComputationMemo('');
                }
                unset($oneEntry);

                $adjustmentEntry->setComputationMemo(ContractUtil::GTP(self::$textMap,
                    'IA.MEA_WITH_EFFECTIVE_DATE_AND_CATCH_UP_ONE_TIME_ADJUSTMENT_AMOUNT',
                    ['EFFECTIVE_DATE' => ContractUtil::getDateForMemo($effectiveDate),'ADJUSTMENT_AMOUNT' => $adjustmentAmount])
                );
                $adjustmentEntries[] = $adjustmentEntry;
            } else if($adjustmentType === ContractScheduleAdjustmentType::CATCH_UP_DISTRIBUTED && $adjustmentAmount != 0) {
                // Distribute the adjustment amount over the entries after the effective date.
                // Note that we are adjusting the entries in place by using a reference.
                foreach ($adjustmentEntries as &$oneEntry) {
                    // Need to subtract the adjustment amount to calculate the percentages correctly since we already
                    // added the adjustment amount above
                    $sumOfAdjustmentEntriesForDistributed = ContractUtil::subtractTwoAmounts($sumOfAdjustmentEntries, $adjustmentAmount);

                    $oneEntryPercentage = ContractUtil::divideTwoAmounts($oneEntry->getAmount(), $sumOfAdjustmentEntriesForDistributed);
                    $oneEntryAdjustmentAmount = ContractUtil::multiplyTwoAmounts($adjustmentAmount, $oneEntryPercentage);

                    $oneEntryScheduleAmount = ContractUtil::addTwoAmounts($oneEntry->getAmount(), $oneEntryAdjustmentAmount);
                    $oneEntryBaseScheduleAmount = ContractUtil::multiplyTwoAmounts($oneEntryScheduleAmount, $exchangeRate);

                    $oneEntry->setComputationMemo(ContractUtil::GTP(self::$textMap,
                        'IA.MEA_AMOUNT_AND_CATCH_UP_DISTRIBUTED_ADJUSTMENT_AMOUNT',
                        ['MEA_AMOUNT' => $oneEntry->getAmount(),'ADJUSTMENT_AMOUNT' => $oneEntryAdjustmentAmount])
                    );
                    $this->setScheduleEntry($oneEntry, $oneEntryScheduleAmount, $oneEntryBaseScheduleAmount, $meaAmount, $entryState);
                }
            }
            // append adjustment entries after effective date to the entries before effective date to get the complete
            // set of schedule of entries
            $resultEntries = array_merge($entriesBeforeEffectiveDate, $adjustmentEntries);
        }

        // If no adjustments are needed then do penny adjustment on the entire schedule otherwise do it only on the
        // adjusted entries
        if ($sumOfAdjustmentEntries == 0) {
            ContractUtil::correctObjectRoundingErrors($meaAmount, $resultEntries);
            ContractUtil::correctObjectRoundingBaseAmountErrors($meaBaseAmount, $resultEntries);
        } else {
            ContractUtil::correctObjectRoundingErrors($sumOfAdjustmentEntries, $adjustmentEntries);
            ContractUtil::correctObjectRoundingBaseAmountErrors($baseSumOfAdjustmentEntries, $adjustmentEntries);
        }
        $this->scheduleInfo->setEntries($resultEntries);
    }

    /**
     * @param array                    $resultEntries
     * @param ContractScheduleEntry[]  $scheduleEntries
     * @param float                    $totalFlatAmount
     * @param float                    $meaAmount
     * @param float                    $exchangeRate
     */
    private function handleMEASameDate(&$resultEntries, $scheduleEntries, $totalFlatAmount, $meaAmount, $exchangeRate)
    {
        $runningSum = 0;
        foreach ($scheduleEntries as $oneEntry) {
            $sourceAmount = $oneEntry->getSourceQty();
            if ($sourceAmount == null) {
                $sourceAmount = $totalFlatAmount;
            }
            if ($sourceAmount == 0 && $oneEntry->getAmount() == 0) {
                $oneEntryPercentage = 1;
            } else {
                // calculate percentages before appying mea
                $oneEntryPercentage = ContractUtil::divideTwoAmounts($oneEntry->getAmount(), $sourceAmount);
            }
            // Apply MEA as per the percentages calculated above, the first time it should be the same as the predefined
            // template
            $oneEntryScheduleAmount = ContractUtil::multiplyTwoAmounts($meaAmount, $oneEntryPercentage);
            $oneEntryBaseScheduleAmount = ContractUtil::multiplyTwoAmounts($oneEntryScheduleAmount, $exchangeRate);
            if (abs($runningSum) >= abs($meaAmount) && $runningSum != 0) {
                $oneEntry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_DELETED);
            } else {
                $runningSum = ContractUtil::addTwoAmounts($runningSum, $oneEntryScheduleAmount);
                $this->setScheduleEntry($oneEntry, $oneEntryScheduleAmount, $oneEntryBaseScheduleAmount, $meaAmount,
                    ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_ADJUSTED);
                if ($oneEntry->getExchangeRate() == 0) {
                    $oneEntry->setExchangeRate($exchangeRate);
                }
            }
            $resultEntries[] = $oneEntry;
        }
    }

    /**
     * @param ContractScheduleEntry $entry
     * @param float $entryScheduleAmount
     * @param float $entryBaseScheduleAmount
     * @param float $meaAmount
     * @param string $state
     */
    private function setScheduleEntry(&$entry, $entryScheduleAmount, $entryBaseScheduleAmount, $meaAmount, $state)
    {
        $entry->setAmount($entryScheduleAmount);
        $entry->setBaseAmount($entryBaseScheduleAmount);
        $entry->setSourceQty($meaAmount);
        $entry->setStateChanged($state);
    }

    /**
     * @param   float                   $meaAmount
     * @param   bool                    $isDifferentEffectiveDate
     * @param   string                  $effectiveDate
     * @return  ContractScheduleEntry[] $entries
     * @throws  IAException
     */
    private function createScheduleEntries($meaAmount, $isDifferentEffectiveDate, $effectiveDate)
    {
        $templateObject = $this->contractTemplate;
        $scheduleStartDate = $this->startDate;
        $scheduleEndDate = $this->endDate;
        $exchangeRate = $this->exchRate;
        $scheduleInfo = $this->scheduleInfo;

        $entries = array();
        if ($templateObject !== null && ($templateObject instanceof ContractRevenueTemplate ||
                $templateObject instanceof ContractExpenseTemplate)) {
            $templateEntries = $templateObject->getEntries();
            foreach ( $templateEntries as $oneEntry ) {
                $periodOffset = $oneEntry['PERIODOFFSET'];
                $periodPercent = $oneEntry['PERIODPERCENT'];
                $lineDate = AddMonths($scheduleStartDate, $periodOffset);
                $lineDate = GetLastDateOfMonth($lineDate);
                if (DateCompare($lineDate, $scheduleEndDate) > 0) {
                    $msg = "The template date $lineDate is after the schedule end date 
                    $scheduleEndDate for period $periodOffset.";
                    throw IAException::newIAException(
                        'CN-1542',
                        $msg,
                        [
                            'LINE_DATE' => FormatDateForDisplay($lineDate),
                            'SCHEDULE_END_DATE' => FormatDateForDisplay($scheduleEndDate),
                            'PERIOD_OFFSET' => $periodOffset,
                        ]
                    );
                }

                $setState = true;
                if ($isDifferentEffectiveDate && DateCompare($lineDate, $effectiveDate) < 0) {
                    // For 0 line amount, we create new 0 amount schedule entries for the rest of the calculations to go
                    // through.
                    // These entries do not get added to the schedule since we do not set the state.
                    $scheduleAmount = 0;
                    $setState = false;
                } else {
                    $scheduleAmount = ContractUtil::multiplyTwoAmounts($meaAmount,
                        ContractUtil::divideTwoAmounts($periodPercent, '100')
                    );
                }
                $scheduleBaseAmount = ContractUtil::multiplyTwoAmounts($scheduleAmount, $exchangeRate);
                $thisEntry = $this->getContractScheduleEntryObject($scheduleAmount, $scheduleBaseAmount, $exchangeRate,
                    $lineDate);

                $thisEntry->setScheduleKey($scheduleInfo->getRecordNo());
                $thisEntry->setPostable(true);
                $thisEntry->setSourceQty($meaAmount);
                if ($setState) {
                    $thisEntry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_NEW);
                }
                $entries[] = $thisEntry;
            }
        }

        return $entries;
    }

    /**
     * @return bool
     */
    public function canReallocate()
    {
        if ($this->fromClearMEA) {
            return true;
        } else {
            return false;
        }
    }
    /**
     * @param float  $amount
     * @param float  $baseAmount
     * @param string $startDate
     * @param string $endDate
     * @param string $locationKey
     * @param bool   $fromClearMEA
     */
    public function reallocate($amount, $baseAmount, $startDate, $endDate, $locationKey, $fromClearMEA)
    {
        $entries = $this->scheduleInfo->getEntries();
        $exchangeRate = $this->exchRate;
        $sumPostedAmount = null;
        $sumPostedBaseAmount = null;
        //mark all the non posted entries as deleted
        foreach ($entries as $oneEntry) {
            if ($fromClearMEA && DateCompare($oneEntry->getPostingDate(), $startDate) < 0) {
                $sumPostedAmount = ContractUtil::addTwoAmounts($sumPostedAmount, $oneEntry->getAmount());
                $sumPostedBaseAmount = ContractUtil::addTwoAmounts($sumPostedBaseAmount,$oneEntry->getBaseAmount());
            } else {
                if ($oneEntry->isPosted() === true) {
                    //throw error here
                    throw new IAException("Encountered posted entries.", 'CN-1544');
                } else {
                    $oneEntry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_DELETED);
                }
            }
        }
        $amountToReAllocate = ContractUtil::subtractTwoAmounts($amount,$sumPostedAmount);
        $baseAmountToReAllocate = ContractUtil::subtractTwoAmounts($baseAmount, $sumPostedBaseAmount);
        $scheduleTemplate = $this->scheduleInfo->getScheduleTemplate();
        if ($scheduleTemplate == null) {
            //throw error here
            throw new IAException("Template information not found.", 'CN-1543');
        }
        $newEntries = null;
        $scheduleStartDate = $this->scheduleInfo->getStartDate();
        $templateEntries = $scheduleTemplate->getEntries();
        $totalPercentAfterEffectiveDate = 0;
        foreach ($templateEntries as $oneEntry) {
            $periodOffset = $oneEntry['PERIODOFFSET'];
            $periodPercent = $oneEntry['PERIODPERCENT'];
            $lineDate = AddMonths($scheduleStartDate, $periodOffset);
            $lineDate = GetLastDateOfMonth($lineDate);
            if (DateCompare($lineDate, $startDate) >= 0) {
                $totalPercentAfterEffectiveDate = ibcadd($totalPercentAfterEffectiveDate, $periodPercent,
                    ContractUtil::INTERMEDIATE_PRECISION, true);
            }
        }
        foreach ($templateEntries as $oneEntry) {
            $periodOffset = $oneEntry['PERIODOFFSET'];
            $periodPercent = $oneEntry['PERIODPERCENT'];
            $lineDate = AddMonths($scheduleStartDate, $periodOffset);
            $lineDate = GetLastDateOfMonth($lineDate);
            if (DateCompare($lineDate,$startDate) >= 0) {
                $scheduleAmount = ContractUtil::multiplyTwoAmounts($amountToReAllocate,
                    ContractUtil::divideTwoAmounts($periodPercent,$totalPercentAfterEffectiveDate)
                );
                $scheduleBaseAmount = ContractUtil::multiplyTwoAmounts($scheduleAmount, $exchangeRate);
                $thisEntry = $this->getContractScheduleEntryObject($scheduleAmount, $scheduleBaseAmount, $exchangeRate, $lineDate);
                $thisEntry->setScheduleKey($this->scheduleInfo->getRecordNo());
                $thisEntry->setStateChanged(ContractScheduleEntry::CONTRACT_SCHEDULE_ENTRY_NEW);
                $thisEntry->setPostable( true);
                $newEntries[] = $thisEntry;
            }
        }
        if ($newEntries != null) {
            ContractUtil::correctObjectRoundingErrors($amountToReAllocate, $newEntries);
            ContractUtil::correctObjectRoundingBaseAmountErrors($baseAmountToReAllocate, $newEntries);
            $finalEntries = INTACCTarray_merge($entries,$newEntries);
        } else {
            $finalEntries = $entries;
        }
        $this->scheduleInfo->setEntries($finalEntries);
    }
}