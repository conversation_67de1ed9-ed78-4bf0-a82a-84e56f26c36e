<?php

/**
 * File ContractGLReclassOnUndoMEAv3.cls contains the class ContractGLReclassOnUndoMEAv3
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2020 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Handler undo of 'On MEA' event v3 before new MEA can be applied
 *
 * Class ContractGLReclassOnUndoMEAv2
 */
class ContractGLReclassOnUndoMEAv2 extends ContractGLReclassOnMEAv2
{

    /* @var array $addlDataOld */
    protected $addlDataOld;

    /* @var  array $meaDistOld */
    protected $meaDistOld;

    /* @var int[] $cnDetailKeysNew */
    protected $cnDetailKeysNew;

    /* @var array $addlDataNew */
    protected $addlDataNew;
    
    /**
     * @param string $bundleType Bundle type (MEA or Kit)
     * @param int    $bundleKey Bundle key for Kits (null for MEA)
     * @param int    $contractKey Contract detail key
     * @param string $effectiveDate MEA effective date
     * @param array  $addlData Additional data stored with resolve records like schedule links today
     * @param string $jrnlCode Journal code
     * @param int[]  $cnDetailKeys Array contract detail keys involved in old MEA
     * @param array  $futureEventByCNDetailKey Future events in case of redo
     * @param array  $addlDataOld Last MEA's schedule links
     * @param int[]  $cnDetailKeysNew Array contract detail keys involved in new MEA
     * @param array  $meaDistOld
     * @param string $historicalFlag ACP reclass 'historical flag'.
     * @param bool   $storeNewBlob true if redoing
     */
    function __construct(
        $bundleType,
        $bundleKey,
        $contractKey,
        $effectiveDate,
        $addlData,
        $jrnlCode,
        $cnDetailKeys,
        $futureEventByCNDetailKey,
        $addlDataOld,
        $cnDetailKeysNew,
        $meaDistOld,
        $historicalFlag,
        $storeNewBlob
    ) {
        $this->log(__FUNCTION__, json_encode(func_get_args()));
    
        parent::__construct(
            $bundleType,
            $bundleKey,
            $contractKey,
            $effectiveDate,
            $addlData,
            $jrnlCode,
            $cnDetailKeys,
            $futureEventByCNDetailKey,
            $historicalFlag,
            $storeNewBlob
        );

        $this->cnDetailKeysNew = $cnDetailKeysNew;
        $this->addlDataOld = $addlDataOld;
        $this->meaDistOld = $meaDistOld;
    }

    /**
     *
     */
    protected function postProcessDeltaTotals()
    {
        // In links structure source and destination were swapped for Undo MEA but baseamount not modified to use new source's exchange rate
        if ( ContractUtil::isMCPEnabled() && count($this->deltaUnbilledTotals) > 0 ) {

            /** @noinspection PhpUnusedLocalVariableInspection */
            foreach ($this->deltaUnbilledTotals as $key => &$total) {

                // TODO: Penny losses??
                $total['BASEAMOUNT'] = ibcmul($total['AMOUNT'], $total['POSTEDEXCHANGE_RATE'],ContractUtil::AMOUNT_PRECISION, true);

            }

        }
    }

    /**
     * Returns old state of links passed through the contructor and retrieves current
     *
     * @return array
     */
    protected function getCachedScheduleLinks()
    {
        if ( ! ( $this->addlData && $this->addlData['SCHEDULELINKS'] ) ) {
            throw new IAException("Undo MEA must get schedule links from the constructor. This is not expected.", 'CN-1393');
        }

        $this->addlDataNew = self::getScheduleLinks($this->cnDetailKeysNew, $this->jrnlCode);

        return $this->addlData['SCHEDULELINKS'];
    }

    /**
     * @return array
     */
    public function getAddlDataNew()
    {
        return $this->addlDataNew;
    }

    /**
     * Prepares additional data array for storage, which includes both new and old
     *
     * @return string compressed
     */
    public function getAddlDataToStore()
    {
        if ( ! $this->addlDataToStore ) {

            $toStore = [
                'OLD_SCHEDULELINKS'   => $this->addlDataOld,
                'SCHEDULELINKS'       => $this->addlDataNew,
                'OLD_MEADISTRIBUTION' => $this->meaDistOld,
                'UNDO_MEAINS'         => $this->getInstructions()
            ];

            $this->addlDataToStore = databaseStringCompress(serialize($toStore));
        }

        return $this->addlDataToStore;
    }

    /**
     * @return bool
     */
    protected function isUndo()
    {
        return true;
    }

    /**
     * @param string $name
     * @param string $data
     */
    protected function log(String $name, String $data)
    {
        ContractUtil::log("UndoMEAv2", $name, $data);
    }


    /**
     * @param array $billinSchEntrykeys
     * @param array $links
     */
    protected function correctPastFlagOfBillingEntry($billinSchEntrykeys, &$links){

        //While redoing need to correct status of billing as there is possible billing is posted or payment of posted bill is done in the  pass of MEA
        $pmtSchEntryMgr = Globals::$g->gManagerFactory->getManager('contractpaymentscheduleentry');
        $filters = [[
            ['BILLSCHEDULEENTRYKEY', 'in', $billinSchEntrykeys],
            [
                [
                    'fields'   => ['POSTED'],
                    'function' => "nvl(\${1}, 'F')"
                ],
                '=',
                true
            ],
            ['ACTUALPOSTINGDATE', '<=', $this->effectiveDate]

        ]];
        $pmtSchEntriesValues = $pmtSchEntryMgr->GetList(
            [
                'selects' => ['BILLSCHEDULEENTRYKEY', 'POSTED', [
                    'fields'   => ['AMOUNT'],
                    'function' => "SUM(\${1})"
                ],

                ],
                'filters' => $filters,
                'groupby'   => ['BILLSCHEDULEENTRYKEY', 'POSTED']
            ]);



        $pmtSchEntriesValues = array_column($pmtSchEntriesValues, 'AMOUNT', 'BILLSCHEDULEENTRYKEY');

        $billingSchEntryMgr = Globals::$g->gManagerFactory->getManager('contractbillingscheduleentry');
        $filters = [[
            ['RECORDNO', 'in', $billinSchEntrykeys],
            [
                [
                    'fields'   => ['POSTED'],
                    'function' => "nvl(\${1}, 'F')"
                ],
                '=',
                true
            ],
            ['ACTUALPOSTINGDATE', '<=', $this->effectiveDate]
        ]];
        $billingSchEntryValues = $billingSchEntryMgr->GetList(
            [
                'selects' => ['RECORDNO', 'POSTED', 'POSTEDEXCHANGE_RATE', 'POSTEDBASEAMOUNT' ],
                'filters' => $filters
            ]);


        $billingSchEntryValues = array_column($billingSchEntryValues, null, 'RECORDNO');

        foreach ( $links as $idx => $link ) {

            if ( isset($billingSchEntryValues[$link['BILLSCHENTRYKEY']]) && $billingSchEntryValues[$link['BILLSCHENTRYKEY']]['POSTED'] = 'true' ) {
                if(isset($pmtSchEntriesValues[$link['BILLSCHENTRYKEY']])  && $pmtSchEntriesValues[$link['BILLSCHENTRYKEY']] != 0 ){
                    $links[$idx]['BILLPOSTED'] = 'T';
                    $links[$idx]['PMTPOSTED'] = 'T';
                    $links[$idx]['BILLPOSTEDEXCHANGE_RATE'] = $billingSchEntryValues[$link['BILLSCHENTRYKEY']]['POSTEDEXCHANGE_RATE'];
                    $links[$idx]['POSTEDBILLBASEAMOUNT'] = self::calcBaseAmt($links[$idx]['AMOUNT'], $billingSchEntryValues[$link['BILLSCHENTRYKEY']]['POSTEDEXCHANGE_RATE']);
                }else{

                        $links[$idx]['BILLPOSTED'] = 'T';
                        $links[$idx]['PMTPOSTED'] = 'F';
                        $links[$idx]['BILLPOSTEDEXCHANGE_RATE'] = $billingSchEntryValues[$link['BILLSCHENTRYKEY']]['POSTEDEXCHANGE_RATE'];
                        $links[$idx]['POSTEDBILLBASEAMOUNT'] = self::calcBaseAmt($links[$idx]['AMOUNT'], $billingSchEntryValues[$link['BILLSCHENTRYKEY']]['POSTEDEXCHANGE_RATE']);
                }
            }else{
                $links[$idx]['BILLPOSTED'] = 'F';
                $links[$idx]['PMTPOSTED'] = 'F';
            }

        }
        $this->log('correctPastFlagOfBillingEntry $links', json_encode($links));
    }
}
