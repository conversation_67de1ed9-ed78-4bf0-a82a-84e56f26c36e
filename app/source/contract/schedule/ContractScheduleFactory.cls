<?php
/**
 * File ContractScheduleFactory.cls contains the class ContractScheduleFactory
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Factory interface for contract schedule creation
 *
 * Class ContractScheduleFactory
 */
class ContractScheduleFactory
{
    /**
     * Returns appropriate Contract schedule handler based on type
     *
     * @param string $type
     * @param ContractScheduleCreateParams $paramsObj
     *
     * @return ContractSchedule
     */
    public static function createTemplateBasedSchedule($type, ContractScheduleCreateParams $paramsObj)
    {
        $classname = "Contract{$type}Schedule";
        /* @var ContractSchedule $schedule */
        $schedule = new $classname();
        $schedule->create($paramsObj);

        return $schedule;
    }

    /**
     * Returns ContractSchedule object representing the $key passed
     *
     * @param string $type type of schedule
     * @param int $key schedule key
     *
     * @return ContractSchedule
     */
    public static function createSchedule($type, $key)
    {
        $classname = "Contract{$type}Schedule";
        $schedule = new $classname($key);
        return $schedule;
    }

    /**
     * @param string $type
     * @param string $method
     *
     * @return BaseContractScheduleEntryCalculator
     * @throws IAException
     */
    public static function createCalculatorForScheduleEntries( $type, $method )
    {
        $amortizationTypes = [
                                ContractSchedule::TYPE_EXPENSE, ContractSchedule::TYPE_EXPENSE2, 
                                ContractSchedule::TYPE_REVENUE, ContractSchedule::TYPE_REVENUE2
                             ];
        if ( in_array($type, $amortizationTypes) ) {
            if ( $method == ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_STRAIGHT_LINE ) {
                return new ContractScheduleEntryStraightLineCalculator();
            }
            if ( $method == ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_DAILY_RATE ) {
                return new ContractScheduleEntryDailyRateCalculator();
            }
            if ( $method == ContractRevrecAndBillingMethodTypes::CONTRACT_REVREC_METHOD_QTY_BASED ) {
                return new ContractScheduleEntryQuantityBasedCalculator();
            }
            if ( $method == ContractRevrecAndBillingMethodTypes::CONTRACT_TEMPLATE_METHOD_PROJECT_PERCENT_COMPLETE ) {
                    return new ContractScheduleEntryProjectBasedCalculator(['SCHEDULETYPE'=>ContractSchedule::TYPE_REVENUE]);
            }
            if ( $method == ContractRevrecAndBillingMethodTypes::CONTRACT_TEMPLATE_METHOD_TASK_PERCENT_COMPLETE ) {
                return new ContractScheduleEntryProjectBasedCalculator(['SCHEDULETYPE'=>ContractSchedule::TYPE_REVENUE]);
            }
            if ( $method == ContractRevrecAndBillingMethodTypes::CONTRACT_TEMPLATE_METHOD_PREDEFINED_PERCENTAGES ) {
                return new ContractSchedulePredefinedPercentagesCalculator();
            }
            if ( $method == ContractRevrecAndBillingMethodTypes::CONTRACT_TEMPLATE_METHOD_REV_REC_ON_INVOICE ) {
                return new ContractRecOnInvoiceRevenueEntryCalculator();
            }
        }
        
        if ( $type === ContractSchedule::TYPE_BILLING ) {
            if ( $method == ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_FIXED_PRICE || $method == ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PREDEFINED_PERCENTAGES ) {
                return new ContractBillingScheduleEntryFixedPriceCalculator();
            }
            if ( $method == ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_PROJECT_PERCENT_COMPLETE || $method == ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_TASK_PERCENT_COMPLETE) {
                return new ContractScheduleEntryProjectBasedCalculator(['SCHEDULETYPE'=>ContractSchedule::TYPE_BILLING]);
            }
            if ( $method == 'One-time' ) {
                return new ContractBillingScheduleEntryOneTimeCalculator();
            }
            if($method == 'BALANCINGENTRY'){
                return new ContractBalancingEntryBillingCalculator();
            }
            
            if ( $method == 'Include with every invoice' ) {
                return new ContractBillingScheduleEntryIncludeWithInvoiceCalculator();
            }

            if ($method == 'PRORATE_BILLING_PERIOD') {
                // Contract billing frequency is monthly.

                return new ContractProrateBillingScheduleEntryCalculator();
            } else if ($method == 'PRORATE_QUARTERLY_BILLING_PERIOD') {
                // Contract billing frequency is quarterly.

                return new ContractProrateBillingPeriodScheduleEntryCalculator(ContractUtil::getProrateBillingMonthsInQuarter());
            } else if ($method == 'PRORATE_ANNUALLY_BILLING_PERIOD') {
                // Contract billing frequency is annually.

                return new ContractProrateBillingPeriodScheduleEntryCalculator(ContractUtil::getProrateBillingMonthsInYear());
            }
        }
        
        // If we get here, there's something wrong
        $msg = "No Calculator found for '$type' and '$method'.";
        throw IAException::newIAException( 'CN-1539', $msg, ['TYPE' => $type, 'METHOD' => $method]);
    }

    /**
     * @param string $type
     * @param string $templateName
     *
     * @return ContractScheduleTemplate|null
     *
     * @throws IAException
     */
    public static function createScheduleTemplateObject( $type, $templateName )
    {
        $template = null;

        if ($templateName != null) {
            $templateEntity = null;

            if ( $type == ContractSchedule::TYPE_REVENUE || $type == ContractSchedule::TYPE_REVENUE2 ) {
                $templateEntity = 'contractrevenuetemplate';
            }

            if ( $type == ContractSchedule::TYPE_EXPENSE || $type == ContractSchedule::TYPE_EXPENSE2 ) {
                $templateEntity = 'contractexpensetemplate';
            }

            if ( $type === ContractSchedule::TYPE_BILLING ) {
                $templateEntity = 'contractbillingtemplate';
            }

            if ( $type === ContractSchedule::TYPE_PAYMENT ) {
                $templateEntity = 'contractpaymenttemplate';
            }

            if ( $templateEntity === null ) {
                $msg = "Invalid type.  There is no corresponding template entity for $type";
                throw IAException::newIAException( 'CN-1540', $msg, ['TYPE' => $type]);
            }

            /** @var ContractAmortizationTemplateManager $tmplMgr */
            $tmplMgr = Globals::$g->gManagerFactory->getManager($templateEntity);

            $template = $tmplMgr->getObject($templateName);

            if (!$template) {
                throw new IAException("Invalid template name provided.", 'CN-1541');
            }
        }

        return $template;
        
    }

    /**
     * @param string $scheduleType
     * @param int    $scheduleId
     * @param string $templateName
     *
     * @return ContractSchedule
     */
    public static function createScheduleWithTemplate( $scheduleType, $scheduleId, $templateName )
    {
        //this call gives the appropriate Contact{type}Schedule Object
        $thisConSchedule = self::createSchedule($scheduleType, $scheduleId);
        $scheduleTemplate = self::createScheduleTemplateObject($scheduleType, $templateName);
        if ( $scheduleTemplate !== null ) {
            $thisConSchedule->getContractScheduleInfo()->setScheduleTemplate($scheduleTemplate);
        }
        return $thisConSchedule;
    }

    /**
     * @param string $type
     *
     * @return ContractScheduleEntryManager
     */
    public static function getScheduleEntryManager( $type )
    {
        $mgr = null;

        switch($type) {
            case ContractSchedule::TYPE_REVENUE:
            case ContractSchedule::TYPE_REVENUE2:
                $mgr = Globals::$g->gManagerFactory->getManager('contractrevenuescheduleentry' );
                break;
            case ContractSchedule::TYPE_BILLING:
                $mgr = Globals::$g->gManagerFactory->getManager('contractbillingscheduleentry' );
                break;
            case ContractSchedule::TYPE_PAYMENT:
                $mgr = Globals::$g->gManagerFactory->getManager('contractpaymentscheduleentry' );
                break;
            case ContractSchedule::TYPE_EXPENSE:
            case ContractSchedule::TYPE_EXPENSE2:
                $mgr = Globals::$g->gManagerFactory->getManager('contractexpensescheduleentry' );
                break;
        }

        return $mgr;

    }
}
