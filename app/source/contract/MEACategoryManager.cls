<?php
/**
 * File MEACategoryManager.cls contains the class MEACategoryManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2017 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * CRUD handler for MEA Category
 *
 * Class MEACategoryManager
 */
class MEACategoryManager extends EntityManager
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
}