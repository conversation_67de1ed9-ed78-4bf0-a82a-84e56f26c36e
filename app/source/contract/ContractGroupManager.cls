<?php

/**
 * Manager class for Contract Group
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for Class Group
 */
class ContractGroupManager extends DimensionGroupManager
{

    /**
     * __construct     
     * 
     * @param array $params entitymanager param
     */
    function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * to set the dimension name (vendor, customer, etc)
     */
    protected function setDimensionEntity()
    {
        $this->dimEntity = 'contract';
    }

    /**
     * to set the name of the dimension ID (VENDORID, CUSTOMERID, etc)
     */
    protected function setDimensionEntityID()
    {
        $this->dimEntityID = 'CONTRACTID';
    }

    /**
     * to set the dimension's DB field (VENDORKEY, CUSTOMERID, etc)
     */
    protected function setDimensionEntityKey()
    {
        $this->dimEntityKey = 'CONTRACTKEY';
    }

    /**
     * Add     
     * 
     * @param array &$values to be added to the table
     * 
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $gErr = Globals::$g->gErr;
        $source = "ContractGroupManager::Add";
        $uiValues = $values;

        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->TranslateValues($values);
        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            // do *not* add error, if it has only warnings
            if ( !HasWarnings() || HasErrors() ) {
                $msg = "Could not create Contract Group record!";
                $gErr->addError('CN-0469', __FILE__ . ':' . __LINE__, $msg);
                epp("$source: Error: $msg");
            }

            $this->_QM->rollbackTrx($source);
        }

        // assigning back $uiValues as MEMBERFILTERS are converted to jason array
        $values = $uiValues;

        return $ok;
    }

    /**
     * Get     
     * 
     * @param string &$ID    batch no
     * @param array  $fields fields to get
     * 
     * @return array
     */
    function Get($ID, $fields=null)
    {
        $obj = parent::Get($ID);
        return $obj;
    }

    /**
     * Set     
     *      
     * @param array &$values new values
     * 
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $gErr = Globals::$g->gErr;
        $source = "ContractGroupManager::Set";
        $uiValues = $values;

        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->TranslateValues($values);
        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            // do *not* add error, if it has only warnings
            if ( !HasWarnings() || HasErrors() ) {
                $msg = "Could not update Contract Group record!";
                $gErr->addError('CN-0470', __FILE__ . ':' . __LINE__, $msg);
                epp("$source: Error: $msg");
            }

            $this->_QM->rollbackTrx($source);
        }

        // assigning back $uiValues as MEMBERFILTERS are converted to jason array
        $values = $uiValues;

        return $ok;
    }

}
