<?php
/**
 * File description
 *
 * Entity for icrw backup
 *
 * <AUTHOR> Rusu <<EMAIL>>
 * @copyright 2016 Intacct Corporation, All Rights Reserved
 */

$kSchemas['crwbackup'] = array(
    'object' => array(
        'RECORDNO',
        'REPORTPATH',
        'PERMISSIONS',
        'REPORTDEF',
        'SIGNATURE',
        'TYPE',
        'CREATED_BY',
        'MODIFIED_BY',
        'WHENCREATED',
        'WHENMODIFIED',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'REPORTPATH' => 'reportpath',
        'CREATED_BY' => 'createdby',
        'MODIFIED_BY' => 'modifiedby',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'PERMISSIONS' => 'permissions',
        'REPORTDEF' => 'reportdef',
        'SIGNATURE' => 'signature',
        'TYPE' => 'type',
    ),
    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'desc' => 'IA.RECORD_NUMBER',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'sequence',
                'maxlength' => 8
            ),
            'readonly' => true,
            'id' => 1
        ),
        array(
            'path' => 'REPORTPATH',
            'fullname' => 'IA.REPORT_PATH',
            'desc' => 'IA.REPORT_PATH',
            'required' => true,
            'type' => array(
                'type'      => 'text',
                'ptype'     => 'text',
                'maxlength' => 2000,
            ),
            'id' => 2,
        ),
        array(
            'path' => 'PERMISSIONS',
            'fullname' => 'IA.PERMISSIONS',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'id' => 3,
        ),
        array(
            'path' => 'REPORTDEF',
            'fullname' => 'IA.REPORT_DEFINITION',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'blob',
            ),
            'id' => 4,
        ),
        array(
            'path' => 'TYPE',
            'fullname' => 'IA.REPORT_TYPE',
            'desc' => 'IA.REPORT_TYPE',
            'required' => true,
            'type' => array(
                'type'      => 'text',
                'ptype'     => 'text',
                'maxlength' => 200,
            ),
            'id' => 5,
        ),
        array(
            'path' => 'SIGNATURE',
            'fullname' => 'IA.SIGNATURE',
            'desc' => 'IA.SIGNATURE',
            'required' => true,
            'type' => array(
                'type'      => 'text',
                'ptype'     => 'text',
                'maxlength' => 200,
            ),
            'id' => 6,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),
    'vid' => 'REPORTPATH',
    'printas' => 'IA.INTERACTIVE_CUSTOM_REPORT_BACKUP',
    'pluralprintas' => 'IA.INTERACTIVE_CUSTOM_REPORT_BACKUPS',
    'module' => 'co',
    'module_list' => array('co', 'cerp'),
    'renameable' => true,
    'nochatter' => true,
    'auditcolumns' => true,
    'nosysview' => true,
    'table' => 'crw_backup',
);
