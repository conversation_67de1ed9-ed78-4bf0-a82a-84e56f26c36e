<?php
/**
 * DvFilterEditor.cls contains the implementation of DvFilterEditor
 *
 * <AUTHOR> Rusu <<EMAIL>>
 * @copyright 2017 Intacct Corporation, All Rights Reserved
 */
/**
 * Class DvFilterEditor
 */
class DvFilterEditor extends FilterEditor
{
    /**
     * Show the report
     *
     * @param bool  $isedit               if edit mode
     * @param array $editvalues           edit values
     * @param array $promptonrundata      input prompts
     * @param array $reportparammetadata  parameter values
     */
    function show($isedit = false, $editvalues = [], $promptonrundata = [], $reportparammetadata = [])
    {
        $url = Reporting::createDvForDashboardUrl($this->_params['_reportPath']);
        DvEditor::obieeRedirectWithAuth($url);
    }
}