<?php
/**
 * File description
 *
 * <AUTHOR> Rusu <<EMAIL>>
 * @copyright 2016 Intacct Corporation, All Rights Reserved
 */

/**
 * Manager class for SubjectArea
 */
class SubjectAreaManager extends EntityManager
{
    /**
    * @var array $reportAreas
    */
    protected $reportAreas;

    /**
     * Override parent - return list of available subject areas
     *
     * @param array $params    input parameters
     * @param bool  $_crosscny cross company
     * @param bool  $nocount   no count option
     *
     * @return array
     */
    function getList($params = [], $_crosscny = false, $nocount = true)
    {
        $getCustom = Request::$r->_customRA;
        $this->reportAreas = [];
        $filters = self::getFilters();
        if ($getCustom !== 'true') {
            $filePath = stream_resolve_include_path(RPDJobs::REPORTING_AREAS_FILE_NAME);
            $modulesPerm = GetAllowedModules();
            $crwPermissionsModulesMap = Reporting::objectToArray(json_decode(file_get_contents($filePath)));
            foreach ($this->fetchSubjectAreas() as $subjectArea) {
                $saModule = trim(str_replace("\"", " ", explode(":", $subjectArea->name)[0]));
                $saModulesPermissons = $crwPermissionsModulesMap[$subjectArea->displayName]['modules'];
                if ((in_array($subjectArea->displayName, Reporting::SL_SUBJECTAREAS)
                     and IsOperationAllowed(GetOperationId('cerp/lists/sl')) === false)
                    or
                    (array_key_exists($saModule, $modulesPerm) === false
                     and isset($saModulesPermissons)
                         and isEmptyArray(array_intersect($saModulesPermissons, array_keys($modulesPerm))))
                ) {
                    continue;
                }
                $module = Globals::$g->kIAModules[ModuleSymbolToId($saModule)]['IAKEY'];
                $reportArea = [
                    'NAME' => $subjectArea->displayName, 'DETAILS' => $subjectArea->description, 'STATUS' => 'active',
                    'ID'   => $subjectArea->name, 'APPLICATION' => $module, 'TYPE' => 'Standard'
                ];
                if (self::passFilters($filters, $reportArea)) {
                    $this->reportAreas[] = $reportArea ;
                }
            }
        }


        $qny = count($this->reportAreas);

        foreach ($this->reportAreas as $key => $val) {
            $this->reportAreas[$key]['QCNT'] = $qny;
        }
        $orders = $params['orders'][0][1];
        $columnToSort = $params['orders'][0][0];
        $this->sortColumns($orders, $columnToSort);
        $reportListPage = array_slice($this->reportAreas, $params['start'], $params['max']);
        return $reportListPage;
    }

    /**
     * Override parent - return the number of available subject areas
     *
     * @param array $params
     *
     * @return int
     */
    function GetCount($params)
    {
        return count($this->getList($params));
    }

    /**
     * Fetch the list of available subject areas
     *
     * @return array|null of subject areas
     * @throws Exception
     */
    public function fetchSubjectAreas()
    {
        $instance = OBIEEInstance::getInstance();
        $client = new OBIEESoapClient($instance->getAdminUser(), $instance->getAdminPassword(), $instance);
        $procedureName = "getSubjectAreas";

        try {
            $client->logonAs(OBIEEInstance::getOBIEEUserId());

            $result = $client->callSOAPMethod(
                $procedureName,
                ["parameters" => ["sessionID" => $client->getSessionID()]]
            );
        } catch (Exception $e) {
            throw $e;
        }

        $results = [];
        if (is_array($result->subjectArea) || is_null($result->subjectArea)) {

            $results =  $result->subjectArea;
        } else if (!is_null($result->subjectArea)) {
            $results =  [$result->subjectArea];
        }
        foreach ($results as &$sa) {
            if (preg_match('/^[^:]+:/', $sa->displayName)) {
                [, $sa->displayName] = explode(':', $sa->displayName, 2);
                $sa->displayName = Reporting::getCnyTranslatedReportingArea($sa->displayName);
            }
        }
        /*
        foreach ($results as $key => $repArea) {
            $client->logon();
            $parameters['parameters'] = [
                    'subjectAreaName' => $repArea->name,
                    'detailsLevel' => 'IncludeTablesAndColumns',
                    'sessionID' =>  $client->getSessionID()
                ];
            $result = $client->callSOAPMethod(
                'describeSubjectArea',
                $parameters
            );
        }
*/
        return $results;
    }

    /** Sort columns from Interactive custom reports lister
     *
     * @param string $orders
     * @param string $columnToSort
     */
    public function sortColumns($orders, $columnToSort)
    {
        if ($orders != null && $columnToSort != null) {
            if ($orders === 'asc') {
                switch ($columnToSort) {
                    case "NAME":
                        usort($this->reportAreas, function($a, $b) {
                            return strnatcasecmp($a['NAME'], $b['NAME']);
                        });
                        break;
                    case "APPLICATION":
                        usort($this->reportAreas, function($a, $b) {
                            return strnatcasecmp(trim($a['MODULE']), $b['MODULE']);
                        });
                        break;
                }
            } else if (substr($orders, 0, 4) === 'desc') {
                switch ($columnToSort) {
                    case "NAME":
                        usort($this->reportAreas, function($a, $b) {
                            return -1 * strnatcasecmp($a['NAME'], $b['NAME']);
                        });
                        break;
                    case "APPLICATION":
                        usort($this->reportAreas, function($a, $b) {
                            return -1 * strnatcasecmp($a['MODULE'], $b['MODULE']);
                        });
                        break;
                }
            }
        }
    }

    /**
     * Retrieve the report lister filters
     *
     * @return string[]
     */
    public static function getFilters()
    {
        $filters = [];
        if (isset(Request::$r->F_NAME)) {
            $filters['NAME'] = isl_strtoupper(Request::$r->F_NAME);
        }
        if (isset(Request::$r->F_APPLICATION)) {
            $filters['APPLICATION']  = isl_strtoupper(Request::$r->F_APPLICATION);
        }
        if (isset(Request::$r->F_CREATED_BY)) {
            $filters['CREATED_BY'] = isl_strtoupper(Request::$r->F_CREATED_BY);
        }
        if (isset(Request::$r->F_CREATED_AT)) {
            $filters['CREATED_AT'] = isl_strtoupper(Request::$r->F_CREATED_AT);
        }
        if (isset(Request::$r->F_DETAILS)) {
            $filters['DETAILS'] = isl_strtoupper(Request::$r->F_SUBJECTAREA);
        }
        return $filters;
    }


    /**
     * Check if the report pass the filters
     *
     * @param string[] $filters filters
     * @param string[] $reportArea  report area
     *
     * @return bool
     */
    public static function passFilters($filters, $reportArea)
    {
        if (empty($filters)) {
            return true;
        } else {
            if ((isset($filters['NAME'])) && ($filters['NAME'] != '')
                && (isl_strpos(isl_strtoupper($reportArea['NAME']), $filters['NAME']) === false)
            ) {
                return false;
            }
            $moduleName = $reportArea['APPLICATION'];
            if ((isset($filters['APPLICATION'])) && ($filters['APPLICATION'] != '')
                && (isl_strpos(isl_strtoupper($moduleName), $filters['APPLICATION']) === false)
            ) {
                return false;
            }
            if ((isset($filters['DETAILS'])) && ($filters['DETAILS'] != '')
                && (isl_strpos(isl_strtoupper($reportArea['DETAILS']), $filters['SUBJECTAREA']) === false)
            ) {
                return false;
            }
        }
        return true;
    }

    /**
     * Fetch the list of available subject areas
     * @param string $subjectAreaName
     * @return string
     * @throws Exception
     */
    public function fetchSubjectAreaDetails($subjectAreaName)
    {
        $instance = OBIEEInstance::getInstance();
        $client = new OBIEESoapClient($instance->getAdminUser(), $instance->getAdminPassword(), $instance);
        $procedureName = "describeSubjectArea";

        try {
            $client->logon();
            $parameters = ['subjectAreaName' => $subjectAreaName, 'sessionID' =>  $client->getSessionID()];
            $result = $client->callSOAPMethod(
                $procedureName,
                $parameters
            );
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * Adds supdocdata if it has data associated with.
     *
     * @param array $values Data info
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $zipN = new ZipArchive();
        $zipName = $values['ATTACHMENTS'][0]['NAME'] . ".zip";
        $filePath = INTACCTtempnam(Reporting::CRW_PATH, 'crw');
        if ($filePath !== false) {
            unlink($filePath);
            if (!mkdir($filePath)) {
                throw new Exception("Can't create temporary folder");
            }
        } else {
            throw new Exception("Can't create temporary files");
        }
        $filePath .= '/';

        $zipPathName = $filePath . $zipName;
        //deleting the old zip file
        unlink($zipPathName);
        touch($zipPathName);
        // create new archive
        if ($zipN->open($zipPathName, ZipArchive::CREATE) !== true) {
            throw new DdsException("Cannot create new zip file");
        }
        foreach ($values['ATTACHMENTS'] as $attachment) {
            $zipN->addFile($attachment['PATHINFO'] . $attachment['NAME'], $attachment['NAME']);
        }

        $zipN->close();
        $values['DATASOURCE'] = file_get_contents($zipPathName);
        $values['PERMLIST'] = serialize($values['PERMLIST']);
        return parent::regularAdd($values);
    }

    /**
     * Get a single record
     *
     * @param string   $ID
     * @param string[] $fields
     *
     * @return array|false
     */
    function Get($ID, $fields=null)
    {
        list($r) = explode('--', $ID);
        return parent::get($r, $fields);
    }

    /**
     * update the record in the database
     *
     * @param array &$values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        if (isset($values['DATA'])) {
            $values['DATA'] = databaseStringCompress($values['DATA']);
            return parent::regularSet($values);
        }

        return true;
    }

}
