<?php
/**
 * Helper class for manipulating data in CRWGLACCTGROUP table
 * The table holds the membership structure of GL Account Groups
 *
 * <AUTHOR> Rusu
 * @copyright 2000-2019 Sage Intacct, All Rights Reserved
 */

/**
 * Helper class for  manipulating data in CRWGLACCTGROUP table
 */
class CrwGLAcctGrpProcessor
{

    const CRW_ADDITIONAL_CLAUSE = " AND (whencreated >= to_date(:2, 'MM/DD/YYYY HH24:MI:SS') OR whenmodified >= to_date(:2, 'MM/DD/YYYY HH24:MI:SS'))" ;
    const CRW_GROUP_STMT = "SELECT record# groupkey FROM glacctgrp WHERE cny# = :1 ";
    const CRW_ACCOUNT_STMT = "SELECT record# accountkey FROM baseaccount WHERE cny# = :1";
    const CRW_RANGES_STMT = "SELECT  record# groupkey FROM glacctgrp WHERE cny# = :1";
    const CRW_GET_DELETED_STMT = "SELECT recordkey, object FROM delete_log WHERE cny# = :1 and object in ('glaccount', 'stataccount', 'glacctgrp', 'glacctgrpmember', 'acctrange') "
    . "AND (whendeleted >= to_date(:2, 'MM/DD/YYYY HH24:MI:SS'))";

    const CRW_DELETE_DATA_STMT = "DELETE from CRWGLACCTGROUP WHERE cny# = :1";


    const CRW_INSERT_DATA_CONDITION = "CONDITION";

    const CRW_SELECT_DATA_1 =
        "SELECT DISTINCT :1, grp.record# as GLACCTGRPKEY,  grp.name as GLACCTGRPNAME, grp.title as GLACCTGRPTITLE, grp.normal_balance as GLACCTGRNORMAL_BALANCE, ba.record# as ACCOUNTKEY "
        ." FROM BASEACCOUNT ba, GLACCTGRPRANGES ga, GLACCTGRP grp "
        ." WHERE ba.cny# = :1 AND ba.CNY# = ga.CNY# AND ba.cny# = grp.cny# "
        . "  AND ba.ACCT_NO BETWEEN ga.RANGEFROM AND ga.RANGETO
              AND (
                    ga.PARENTKEY IN
                    (
                        SELECT
                            CHILD#
                        FROM
                            GLACCTGRPMEMBERS
                        START WITH PARENT# = grp.RECORD# AND CNY# = ga.CNY#
                        CONNECT BY PRIOR CHILD# = PARENT# AND CNY# = ga.CNY#
                    )
                OR ga.PARENTKEY = grp.RECORD#
            )";

    const CRW_SELECT_DATA_2 =
        "SELECT DISTINCT :1, grp2.record# as GLACCTGRPKEY, grp2.name as GLACCTGRPNAME, grp2.title as GLACCTGRPTITLE, grp2.normal_balance as GLACCTGRNORMAL_BALANCE,  ba2.record# as ACCOUNTKEY "
        . "FROM BASEACCOUNT ba2, COACATMEMBERS ga2, GLACCTGRP grp2"
        ." WHERE ba2.CNY# = :1 AND ba2.CNY# = ga2.CNY# AND ba2.CNY# = grp2.cny# AND ba2.CATEGORYKEY = ga2.CATEGORYKEY "
        . " AND (
                    ga2.PARENT# IN
                    (
                        SELECT
                            CHILD#
                        FROM
                            GLACCTGRPMEMBERS
                        START WITH PARENT# = grp2.RECORD# AND CNY# = ga2.CNY#
                        CONNECT BY PRIOR CHILD# = PARENT# AND CNY# = ga2.CNY#
                    )
                OR ga2.PARENT# = grp2.RECORD#
                )";


    const CRW_SELECT_DATA_DC_1 =
        "SELECT DISTINCT :1, grp.record# as GLACCTGRPKEY,  grp.name as GLACCTGRPNAME, grp.title as GLACCTGRPTITLE, grp.normal_balance as GLACCTGRNORMAL_BALANCE, ba.record# as ACCOUNTKEY "
        ." FROM BASEACCOUNT ba, GLACCTGRPRANGES ga, GLACCTGRP grp "
        ." WHERE ba.cny# = :1 AND ba.CNY# = ga.CNY# AND ba.cny# = grp.cny# AND ga.PARENTKEY = grp.RECORD# AND ba.ACCT_NO BETWEEN ga.RANGEFROM AND ga.RANGETO";

    const CRW_SELECT_DATA_DC_2 =
        "SELECT DISTINCT :1, grp2.record# as GLACCTGRPKEY, grp2.name as GLACCTGRPNAME, grp2.title as GLACCTGRPTITLE, grp2.normal_balance as GLACCTGRNORMAL_BALANCE,  ba2.record# as ACCOUNTKEY "
        . "FROM BASEACCOUNT ba2, COACATMEMBERS ga2, GLACCTGRP grp2"
        ." WHERE ba2.CNY# = :1 AND ba2.CNY# = ga2.CNY# AND ba2.CNY# = grp2.cny# AND ba2.CATEGORYKEY = ga2.CATEGORYKEY   and ga2.PARENT# = grp2.RECORD#";


    const CRW_SELECT_DATA_HIERARCHY_1 = "SELECT glacctgrpmembers.parent#, glacctgrpmembers.child# , glacctgrpmembers.sortord "
        . "FROM glacctgrpmembers WHERE glacctgrpmembers.cny# = :1 and glacctgrpmembers.child# in (";



    const LAST_COMPLETED_PROP = 'CRWGLACCTGRP_LASTCOMPLETED';


    const MAX_DEPTH = 5;



    /**
     * Updates data in CRWGLACCTGROUP table for this company for a particular GL account
     *
     * @param string $cny
     * @param string $condition
     *
     * @return bool true if CRWGLACCTGROUP data were successfully recreated, otherwise false
     */
    static function updateData($cny, $condition = '')
    {
        //determine the groups hierarchy
        $query1 = str_ireplace(self::CRW_INSERT_DATA_CONDITION, $condition, self::CRW_SELECT_DATA_1);
        $res1 = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ $query1, $cny ] ],
            $cny
        ) ?: [];
        $query2 = str_ireplace(self::CRW_INSERT_DATA_CONDITION, $condition, self::CRW_SELECT_DATA_2) ;

        $res2 = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ $query2, $cny ] ],
            $cny
        ) ?: [];


        $queryDirectConnection1 = str_ireplace(self::CRW_INSERT_DATA_CONDITION, $condition, self::CRW_SELECT_DATA_DC_1);
        $resDirectConnection1 = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ $queryDirectConnection1, $cny ] ],
            $cny
        ) ?: [];
        $queryDirectConnection2 = str_ireplace(self::CRW_INSERT_DATA_CONDITION, $condition, self::CRW_SELECT_DATA_DC_2) ;

        $resDirectConnection2 = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ $queryDirectConnection2, $cny ] ],
            $cny
        ) ?: [];
        $resDirectConnection = array_merge($resDirectConnection1, $resDirectConnection2);

        $results = array_merge($res1, $res2);


        $groupsL0 = [];
        $namesMap = [];
        foreach ($results as $toInsertResult) {
            if (isArrayValueProvided($toInsertResult, 'GLACCTGRPKEY')) {
                $groupsL0[$toInsertResult['GLACCTGRPKEY']] = $toInsertResult['GLACCTGRPKEY'];
            }
        }

        $hierarchy = [];
        $uniqueIdMap = [];
        $flatHierarchy = [];
        $groupToAccountMap = [];
        $names = self::getGroupInfo($groupsL0);
        foreach ($names as $name) {
            $namesMap[$name['RECORDNO']] = $name;
        }

        $ok =  self::addLevel($cny, $groupsL0, $namesMap, $uniqueIdMap, $hierarchy,1 );

        foreach ($resDirectConnection as $resl0) {
            $groupToAccountMap[$resl0['GLACCTGRPKEY']][] = $resl0['ACCOUNTKEY'];
            $flatKey = $resl0['GLACCTGRPKEY'] . '::::';
            $flatHierarchy[$flatKey] = [
                $resl0['GLACCTGRPKEY'],
                null,
                null,
                null,
                null,
                1,
            ];
        }

        foreach ($hierarchy[1] as $innerLevel1Key =>  $innerLevel1Value) {
            self::buildStructForInsert($hierarchy, $flatHierarchy, 0, [$innerLevel1Key]);
        }


        $bulkArrFlat = [];

        $bulkArrFlat[0] = "INSERT INTO CRWGLACCTGROUP (
                CNY#,
                RECORD#,
                ACCOUNTKEY, 
                CHILDL1KEY, 
                CHILDL2KEY,
                CHILDL3KEY, 
                CHILDL4KEY,
                CHILDL5KEY, 
                CHILDL1NAME, 
	            CHILDL1TITLE, 
	            CHILDL1NORMAL_BALANCE,
	            CHILDL2NAME, 
	            CHILDL2TITLE, 
	            CHILDL2NORMAL_BALANCE,
	            CHILDL2SORTORD,
	            CHILDL3NAME, 
	            CHILDL3TITLE, 
	            CHILDL3NORMAL_BALANCE,
	            CHILDL3SORTORD,
	            CHILDL4NAME, 
	            CHILDL4TITLE, 
	            CHILDL4NORMAL_BALANCE,
	            CHILDL4SORTORD,
	            CHILDL5NAME, 
	            CHILDL5TITLE, 
	            CHILDL5NORMAL_BALANCE,
	            CHILDL5SORTORD
                ) ".
            "VALUES (
            $cny, :1(i), :2(i), :3(i), :4(i), :5(i), :6(i), :7(i), :8(i), :9(i), :10(i),
             :11(i), :12(i), :13(i), :14(i), :15(i) , :16(i), :17(i), :18(i), :19(i), :20(i),
              :21(i), :22(i), :23(i), :24(i), :25(i), :26(i))";
        $bulkArrFlat[1] = [];
        $bulkArrFlat[2] = [
            "integer", "integer", "integer", "integer", "integer", "integer", "integer",
            "text", "text", "text",
            "text", "text", "text", "integer",
            "text", "text", "text", "integer",
            "text", "text", "text", "integer",
            "text", "text", "text", "integer",
        ];


        $recordNo = 0;
        foreach ($flatHierarchy as $flatValue) {
            $lowerGroup = $flatValue[$flatValue[5] - 1];
            $accounts = $groupToAccountMap[$lowerGroup];

            if(is_iterable($accounts)) {
                foreach ( $accounts as $account ) {
                    $bulkArrFlat[1][] = [
                        $recordNo++,
                        $account,
                        $flatValue[0],
                        $flatValue[1],
                        $flatValue[2],
                        $flatValue[3],
                        $flatValue[4],
                        $namesMap[$flatValue[0]]['NAME'],
                        $namesMap[$flatValue[0]]['TITLE'],
                        $namesMap[$flatValue[0]]['NORMAL_BALANCE'],
                        $namesMap[$flatValue[1]]['NAME'],
                        $namesMap[$flatValue[1]]['TITLE'],
                        $namesMap[$flatValue[1]]['NORMAL_BALANCE'],
                        $flatValue[6],
                        $namesMap[$flatValue[2]]['NAME'],
                        $namesMap[$flatValue[2]]['TITLE'],
                        $namesMap[$flatValue[2]]['NORMAL_BALANCE'],
                        $flatValue[7],
                        $namesMap[$flatValue[3]]['NAME'],
                        $namesMap[$flatValue[3]]['TITLE'],
                        $namesMap[$flatValue[3]]['NORMAL_BALANCE'],
                        $flatValue[8],
                        $namesMap[$flatValue[4]]['NAME'],
                        $namesMap[$flatValue[4]]['TITLE'],
                        $namesMap[$flatValue[4]]['NORMAL_BALANCE'],
                        $flatValue[9],
                    ];
                }
            }
        }

        return $ok && ExecBulkStmt($bulkArrFlat);
    }



    /**
     * Delete data in CRWGLACCTGROUP table for this company for a condition
     *
     * @param string $cny
     * @param string[] $condition
     *
     * @return bool true if CRWGLACCTGROUP data were successfully deleted, otherwise false
     */
    static function cleanData($cny, $condition = null) {
        $query = self::CRW_DELETE_DATA_STMT;
        if ($condition !== null) {
            $query .= "AND " .  $condition;
        }

        return ExecStmt([$query, $cny]);
    }

    /**
     * @param string $cny
     */
    public function process($cny)
    {
        SetDBSchema($cny, 'cny#');
        Backend_Init::SetEnvironment($cny);
        Backend_Init::Init();
        $timestamp = GetCurrentUTCTimestamp();

        $moduleQry = "select m.cny# cny#, m.value value from modulepref m where property = 'CRWGLACCTGRP_LASTCOMPLETED' and cny# = :1";
        $moduleRes = QueryResult(array($moduleQry, $cny));

        if(isset($moduleRes[0]['VALUE'])) {
            $needsUpdate = CrwGLAcctGrpDispatcher::needsUpdate($cny, $moduleRes[0]['VALUE']);
            $tenantLifeCycleMgr = new TenantLifeCycleManager($cny);
            $tenantType = $tenantLifeCycleMgr->getTenantType();
            if ($tenantType == COMPANY_TYPE_SALESDEMO) {
                $nowDT = date_create(date("d-m-Y H:i:s"));
                $lastUpdatedDT = date_create($moduleRes[0]['VALUE']);
                $interval = date_diff($nowDT, $lastUpdatedDT);
                if($interval->format('%a') < 1) {
                    $needsUpdate = false;
                }
            }
        } else {
            $needsUpdate = true;
        }
        if ($needsUpdate) {
            $crwSetupMgr = Globals::$g->gManagerFactory->getManager('crwsetup');
            $ok = $crwSetupMgr->SetPreference(self::LAST_COMPLETED_PROP, $timestamp, true, Globals::$g->kCRWid);
            $ok = $ok && self::cleanData($cny);
            $ok = $ok && self::updateData($cny);
            if ($ok) {
                echo "Update gl account group structures completed";
            } else {
                echo "Update gl account group structures failed";
            }
        }
    }

    /**
     *
     * @param string $cny
     * @param int    $timestamp
     * @return int[]
     */
    public static function getChangedGroups($cny, $timestamp)
    {
        $groupQry = self::CRW_GROUP_STMT . self::CRW_ADDITIONAL_CLAUSE;
        $res = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ $groupQry, $cny, $timestamp ] ],
            $cny
        );
        if ($res === false) {
            return [];
        }
        return $res;
    }

    /**
     *
     * @param string $cny
     * @param int    $timestamp
     * @return int[]
     */
    public static function getChangedAccounts($cny, $timestamp)
    {
        $acctQry = self::CRW_ACCOUNT_STMT . self::CRW_ADDITIONAL_CLAUSE;
        $res = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ $acctQry, $cny, $timestamp ] ],
            $cny
        );
        if ($res === false) {
            return [];
        }
        return $res;
    }


    /**
     *
     * @param string $cny
     * @param int    $timestamp
     * @return int[]
     */
    public static function getChangedRanges($cny, $timestamp)
    {
        $rangeQry = self::CRW_RANGES_STMT . self::CRW_ADDITIONAL_CLAUSE;
        $res = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ $rangeQry, $cny, $timestamp ] ],
            $cny
        );

        if ($res === false) {
            return [];
        }
        return $res;
    }

    /**
     *
     * @param string $cny
     * @param int    $timestamp
     * @return int[]
     */
    public static function getDeletedElements($cny, $timestamp)
    {
        $res = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ self::CRW_GET_DELETED_STMT, $cny, $timestamp ] ],
            $cny
        );

        if ($res === false) {
            return [];
        }
        return $res;
    }







    /** Find all parents ("ancestors") of a GL account group in the group membership structure.
     *
     * @param array $children
     *
     * @return array
     */
    public static function getGroupInfo($children) {
        $querySpec = [
            'selects' => ['RECORDNO', 'TITLE', 'NAME', 'NORMAL_BALANCE'],
            'filters' => [
                [
                    ['RECORDNO', 'IN', $children],
                ],
            ],
        ];

        $glAcctGrpMgr = Globals::$g->gManagerFactory->getManager('glacctgrp');

        return $glAcctGrpMgr->GetList($querySpec);
    }

    /** Find all children for specific groups.
     * @param string $cny
     * @param array $parents
     * @return array
     */
    public static function getChildrenGroups($cny, $parents) {
        $groupList = implode(',', $parents);
        $results = DBRunner::runOnCnyDB(
            'QueryResult',
            [ [ self::CRW_SELECT_DATA_HIERARCHY_1 . $groupList . ')' , $cny]],
            $cny
        );
        
        return is_array($results) ? $results : [];
    }

    /** Create map.
     * @param array $groupList
     * @param array &$hierarchy
     * @param int $level
     *
     * @return array
     */
    public static function createChildrenGroupMap($groupList, &$hierarchy, $level) {
        $childrenMap = [];

        foreach ($groupList as $child) {
            $childrenMap[$child['CHILD#']] = $child['CHILD#'];
            $hierarchy[$level][$child['PARENT#']][$child['CHILD#']] = $child['CHILD#'];
            $hierarchy[0][$child['PARENT#']][$child['CHILD#']] = $child['SORTORD'];
        }
        return $childrenMap;
    }


    /** Create map.
     * @param string $cny
     * @param array $groupList
     * @param array &$namesMap
     * @param array &$uniqueMap
     * @param array &$hierarchy
     * @param int $level
     * @return bool
     */
    public static function addLevel($cny, $groupList, &$namesMap, &$uniqueMap, &$hierarchy, $level) {

        $ok = true;
        $childrenL1 = self::getChildrenGroups($cny, $groupList);
        $children1q = self::createChildrenGroupMap($childrenL1, $hierarchy, $level);
        if (count($children1q) > 0) {
            $names = self::getGroupInfo($children1q);
            foreach ($names as $name) {
                $namesMap[$name['RECORDNO']] = $name;
            }
            if ($level < self::MAX_DEPTH) {
                $ok = self::addLevel($cny, $children1q, $namesMap, $uniqueMap,$hierarchy,$level + 1);
            }
        }
        return $ok;
    }

    /** Create map.
     * @param array $hierarchy
     * @param array &$flatHierarchy
     * @param int $level
     * @param array $keys
     */
    public static function buildValue($hierarchy, &$flatHierarchy, $level, $keys)
    {
        $flatKey = $keys[0];
        $value = [$keys[0]];
        for($index=1; $index < 6; ++$index) {
            $flatKey .= ':' . $keys[$index];
        }
        for($index=1; $index < count($keys); ++$index) {
            $value[$index] = $keys[$index];
            $value[$index + 5] =  $hierarchy[0][$keys[$index -1]][$keys[$index]];
        }
        $value[5] = $level  + 1;
        $flatHierarchy[$flatKey] = $value;
    }
    /** Create map.
     * @param array $hierarchy
     * @param array &$flatHierarchy
     * @param int $level
     * @param array $keys
     */
    public static function buildStructForInsert($hierarchy, &$flatHierarchy, $level, $keys)
    {
        if (is_array($hierarchy[$level + 1][$keys[$level]])) {
            if ($level < self::MAX_DEPTH - 1) {
                foreach ($hierarchy[$level + 1][$keys[$level]] as  $innerLevelKey => $innerLevelValue) {
                    $keys[$level + 1] = $innerLevelKey;
                    self::buildStructForInsert($hierarchy, $flatHierarchy, $level + 1, $keys);
                }
            } else {
                self::buildValue($hierarchy, $flatHierarchy, $level, $keys);
            }
        } else {
            self::buildValue($hierarchy, $flatHierarchy, $level, $keys);
        }
    }
}

