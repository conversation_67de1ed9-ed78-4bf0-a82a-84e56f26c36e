<?php
/**
 * ARReleaseableRecord entity Lister
 *
 * <AUTHOR>
 * @copyright 2019 Sage Intacct Corporation, All Rights Reserved
 */

/**
 * Lister class for the ARReleaseableRecord
 */
class ARReleaseableRecordLister extends NLister
{

    /**
     * User info specific tokens
     * @var string[]
     */
    protected $additionalTokens = [
        'IA.ADD_SELECTED',
        'IA.DONE',
    ];

    /* @var array $fields */
    private $fields = [];

    public function __construct()
    {
        $fields = [
            'CUSTOMERNAME',
            'PROJECTID',
            'PROJECTNAME',
            'ITEMID',
            'DOCNO',
            'SOURCEDOCUMENTID',
            'DOCDATE',
            'TRX_AMOUNT',
            'TRX_AMOUNTRETAINED',
            'TRX_TOTALRELEASED'
        ];
        if (IADimensions::IsDimensionEnabled('pa', 'task')) {
            array_splice( $fields, 2, 0, 'TASKID' );
        }
        $this->fields = $fields;

        if(CRESetupManager::isCREInstalled()){
            $this->fields = array_merge($this->fields, ['PROJECTCONTRACTID', 'PROJECTCONTRACTLINEID']);
        }

        $helpfile = 'Viewing_and_Managing_Projects';

        parent::__construct(
            [
                'entity'            => 'arreleaseablerecord',
                'helpfile'          => $helpfile,
                'fields'            => $this->fields,
                'disableadd'        => true,
                'disabledelete'     => true,
                'entitynostatus'    => true,
                'suppressPrivate'   => true,
                'enablemultidelete' => false,
                'sortcolumn'        => 'RETAINAGE_PRRECORDKEY:d,RETAINAGE_PRENTRYKEY:a',
                'enablecheck'       => true,
                'id'                => 'RETAINAGEINVOICEITEMKEY',
                'bulkactions'        => true,
            ]
        );
    }

    /**
     * we dont want any buttons for this lister, the bulk action buttons will be added separately
     * @return string XML for the lister buttons
     */
    function genAllButtons()
    {
        return "";
    }

    /**
     * generate the bulk action buttons
     * @return string
     */
    protected function genBulkActionButtons() : string
    {
        $bulkButtons = parent::genBulkActionButtons();

        $addSelectedLabel = GT($this->textMap,'IA.ADD_SELECTED');
        $doneLabel = GT($this->textMap,'IA.DONE');
        $bulkButtons .= "<bulk id='addSelectedItems'><a href='javascript:void(0);' onclick='retainageReleaseHandler.addRetainageReleaseEntries();'>{$addSelectedLabel}</a></bulk>";
        $bulkButtons .= "<bulk id='closeWindow'><a href='javascript:void(0);' onclick='retainageReleaseHandler.closeSelectReleasableItemsPage();'>{$doneLabel}</a></bulk>";
        return $bulkButtons;
    }

    /**
     * @return string
     */
    function genJSIncludes()
    {
        $jsInc = parent::genJSIncludes();
        $jsInc .= "<jslib>../resources/js/retainagerelease.js</jslib>";
        return $jsInc;
    }

    /**
     * @return string
     */
    function genSystemViewStr()
    {
        $ret = '';
        if (!$this->_params['nosysview']) {
            $ret .= self::SYSVIEW_ALL.'##'.GT($this->textMap, 'IA.ALL');
        }

        return $ret;
    }
}
