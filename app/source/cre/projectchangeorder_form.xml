<?xml version="1.0" encoding='UTF-8'?>
<ROOT>
    <entity>projectchangeorder</entity>
    <title>IA.PROJECT_CHANGE_ORDER</title>
    <view system="true">
        <pages id="crpages">
            <page id="crpage">
                <title>IA.PROJECT_CHANGE_ORDER</title>
                <section isCollapsible="true" showCollapsed="false" id="projectchangeorder_section" columnCount="1">
                    <title>IA.PROJECT_CHANGE_ORDER_INFORMATION</title>
                    <subsection className="subSection" id="projectchangeorder_subsection" columnCount="4">
                        <row>
                            <field fullname="IA.PROJECT_CHANGE_ORDER_ID" >PROJECTCHANGEORDERID</field>
                            <field>
                                <fullname>IA.PROJECT</fullname>
                                <path>PROJECTID</path>
                                <events>
                                    <change>pjchangeorder_onChangeProject(this);</change>
                                </events>
                            </field>
                            <field>PROJECTCHANGEORDERDATE</field>
                            <field>PRICEEFFECTIVEDATE</field>
                        </row>
                        <row>
                            <field>DESCRIPTION</field>
                            <field>PCOTEMPLATE</field>
                            <field fullname="IA.SEND_TO_CONTACT">SENDTO</field>
                            <field fullname="IA.BILLING_ITEM">ITEMID</field>
                        </row>
                        <row>
                            <field>
                                <fullname>IA.PROJECT_CONTRACT</fullname>
                                <path>PROJECTCONTRACTID</path>
                                <events>
                                    <change>pjchangeorder_onChangePCN(this);</change>
                                </events>
                            </field>
                            <field userUIControl="ProjectContractLineInputControlPtr">
                                <fullname>IA.PROJECT_CONTRACT_LINE</fullname>
                                <path>PROJECTCONTRACTLINEID</path>
                            </field>
                            <field fullname="IA.CHANGE_REQUEST_STATUS">CHANGEREQUESTSTATUSNAME</field>
                            <field>SUPDOCID</field>
                        </row>
                        <row>
                            <field fullname="IA.CUSTOMER">CUSTOMERID</field>
                            <field fullname="IA.TOTAL_PRICE" readonly="true" nullValue="0.00">
                                <path>TOTALPRICE</path>
                                <type type='decimal' ptype='currency'></type>
                            </field>
                            <field fullname="IA.TOTAL_COST" readonly="true" nullValue="0.00">
                                <path>TOTALCOST</path>
                                <type type='decimal' ptype='currency'></type>
                            </field>
                            <field fullname="IA.STATE">
                                <path>PROJECTCHANGEORDERSTATE</path>
                            </field>
                        </row>
                    </subsection>
                </section>
                <section isCollapsible="true" showCollapsed="true">
                    <title>IA.ADDITIONAL_INFORMATION</title>
                    <section id="scope_section" columnCount="2">
                        <title>IA.SCOPE</title>
                        <row>
                            <field>SCOPE</field>
                            <field>TERMS</field>
                        </row>
                        <row>
                            <field>INCLUSIONS</field>
                            <field>EXCLUSIONS</field>
                        </row>
                    </section>
                    <section id="schedule_section" columnCount="3">
                        <title>IA.SCHEDULE</title>
                        <row>
                            <field>SCHEDULEDSTARTDATE</field>
                            <field>ACTUALSTARTDATE</field>
                            <field>SCHEDULEDCOMPLETIONDATE</field>
                            <field>REVISEDCOMPLETIONDATE</field>
                        </row>
                        <row>
                            <field>SUBSTANTIALCOMPLETIONDATE</field>
                            <field>ACTUALCOMPLETIONDATE</field>
                            <field>NOTICETOPROCEED</field>
                            <field>RESPONSEDUE</field>
                        </row>
                        <row>
                            <field>EXECUTEDON</field>
                            <field>SCHEDULEIMPACT</field>
                        </row>
                    </section>
                    <section id="internalref_section" columnCount="3">
                        <title>IA.INTERNAL_REFERENCE</title>
                        <row>
                            <field>INTERNALREFNO</field>
                            <field>INTERNALINITIATEDBY</field>
                            <field>INTERNALVERBALBY</field>
                        </row>
                        <row>
                            <field>INTERNALISSUEDBY</field>
                            <field>INTERNALISSUEDON</field>
                            <field>INTERNALAPPROVEDBY</field>
                            <field>INTERNALAPPROVEDON</field>
                        </row>
                        <row>
                            <field>INTERNALSIGNEDBY</field>
                            <field>INTERNALSIGNEDON</field>
                            <field>INTERNALSOURCE</field>
                            <field>INTERNALSOURCEREFNO</field>
                        </row>
                    </section>
                    <section id="externalref_section" columnCount="3">
                        <title>IA.EXTERNAL_REFERENCE</title>
                        <field>EXTERNALREFNO</field>
                        <field>EXTERNALVERBALBY</field>
                        <field>EXTERNALAPPROVEDBY</field>
                        <field>EXTERNALAPPROVEDON</field>
                        <field>EXTERNALSIGNEDBY</field>
                        <field>EXTERNALSIGNEDON</field>
                    </section>
                </section>
                <section isCollapsible="true" showCollapsed="false">
                    <title>IA.CHANGE_REQUESTS</title>
                    <grid clazz="ChangeRequestsOnPCOGrid" id="changerequestgrid" deleteOnGrid="true" showDelete="false" noNewRows="true">
                        <entity>changerequest</entity>
                        <path>PCO_CHANGEREQUESTS</path>
                        <title>IA.CHANGE_REQUESTS</title>
                        <hideTitle>true</hideTitle>
                        <noDragDrop>true</noDragDrop>
                        <enableSelect>true</enableSelect>
                        <uniquePropOnRow>RECORDNO</uniquePropOnRow>
                        <allowEditPage>true</allowEditPage>
                        <gridContextualActions hideInViewMode="false">
                            <button id="addCRs" path="addCRs">
                                <name>IA.SELECT_NO_DASHES</name>
                                <events>
                                    <click>changeRequestSelectorHandler.showSelectChangeRequestsPage(6016);</click>
                                </events>
                            </button>
                        </gridContextualActions>
                        <gridBulkActions hideInViewMode="false">
                            <button id="removeSelectedCRs" path="removeSelectedCRs">
                                <name>IA.REMOVE</name>
                                <events>
                                    <click>changeRequestSelectorHandler.removeSelectedCRs();</click>
                                </events>
                            </button>
                        </gridBulkActions>
                        <selectColumn autoRedraw="true" autoUpdateSelected="true"></selectColumn>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="CHANGEREQUESTID"><path>SEARCH_CHANGEREQUESTID</path></field></gridHeading>
                            <field fullname="IA.CHANGE_REQUEST_ID" sortable="true" readonly="true" required="false">
                                <path>CHANGEREQUESTID</path>
                                <type assoc="T">
                                    <type>text</type>
                                    <ptype>href</ptype>
                                </type>
                                <events>
                                    <click>PCOFunctions.openDrilldownPage(this.meta);</click>
                                </events>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="CHANGEREQUESTDATE"><path>SEARCH_CHANGEREQUESTDATE</path></field></gridHeading>
                            <field path="CHANGEREQUESTDATE" fullname="IA.DATE" sortable="true" readonly="true" required="false"/>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="DESCRIPTION"><path>SEARCH_DESCRIPTION</path></field></gridHeading>
                            <field path="DESCRIPTION" sortable="true" readonly="true"/>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="PROJECTID"><path>SEARCH_PROJECTID</path></field></gridHeading>
                            <field fullname="IA.PROJECT_ID" sortable="true" readonly="true" required="false">
                                <path>PROJECTID</path>
                                <type assoc="T">
                                    <type>text</type>
                                    <ptype>href</ptype>
                                </type>
                                <events>
                                    <click>PCOFunctions.openDrilldownPage(this.meta);</click>
                                </events>
                            </field>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="PROJECTNAME"><path>SEARCH_PROJECT</path></field></gridHeading>
                            <field path="PROJECTNAME" sortable="true" readonly="true"/>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="CHANGEREQUESTTYPENAME"><path>SEARCH_CHANGEREQUESTTYPENAME</path></field></gridHeading>
                            <field path="CHANGEREQUESTTYPENAME" sortable="true" readonly="true"/>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="CHANGEREQUESTSTATUSNAME"><path>SEARCH_CHANGEREQUESTSTATUSNAME</path></field></gridHeading>
                            <field path="CHANGEREQUESTSTATUSNAME" sortable="true" readonly="true"/>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="TOTALCOST"><path>SEARCH_TOTALCOST</path></field></gridHeading>
                            <field path="TOTALCOST" hasTotal="true" sortable="true" readonly="true"/>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="TOTALPRICE"><path>SEARCH_TOTALPRICE</path></field></gridHeading>
                            <field path="TOTALPRICE" hasTotal="true" sortable="true" readonly="true"/>
                        </column>
                        <column>
                            <gridHeading className="center"><field noLabel="true" searchPath="PROJECTCONTRACTID"><path>SEARCH_PROJECTCONTRACTID</path></field></gridHeading>
                            <field path="PROJECTCONTRACTID" sortable="true" readonly="true">
                                <type assoc="T">
                                    <type>text</type>
                                    <ptype>href</ptype>
                                </type>
                                <events>
                                    <click>PCOFunctions.openDrilldownPage(this.meta);</click>
                                </events>
                            </field>
                        </column>
                        <lineDetails id="itemDetails" customFields="PCO_CHANGEREQUESTS">
                            <pages>
                                <page columnCount="1">
                                    <field sortable="true" readonly="true">WFTYPE</field>
                                    <field readonly="true">SUPDOCID</field>
                                    <field readonly="true">COSTEFFECTIVEDATE</field>
                                    <field readonly="true">PRICEEFFECTIVEDATE</field>
                                    <field readonly="true">PROJECTCONTRACTLINEID</field>
                                    <section title="IA.DIMENSIONS">
                                        <field sortable="true" readonly="true">
                                            <path>LOCATIONID</path>
                                        </field>
                                    </section>
                                </page>
                            </pages>
                        </lineDetails>
                    </grid>
                </section>
            </page>
            <page id="DUMMYPAGE"></page>
        </pages>
        <floatingPage move="true" resize="true" close="true"
                      fixedcenter="true">
            <title>IA.PRINT_EMAIL</title>
            <id>printEmailPage</id>
            <path>PRINTEMAIL_PAGE</path>
            <pages>
                <page>
                    <section>
                        <child>
                            <row label="">
                                <field id="deliveryMethodLabel"
                                       path="DELIVERYMETHODLABEL"
                                       default="IA.DELIVERY_METHOD"
                                       className="li_aligned_font_normal">
                                    <type type="textlabel" ptype="textlabel"></type>
                                </field>
                                <field rightSideLabel="true" forceReadWrite="true">
                                    PRINT_DELIVERY
                                </field>
                                <field rightSideLabel="true" forceReadWrite="true">
                                    <path>EMAIL_DELIVERY</path>
                                    <events>
                                        <change>
                                            pcoShowHideEmailFields(this.meta);
                                        </change>
                                    </events>
                                </field>
                            </row>
                        </child>
                        <child>
                            <field hidden="true" isHTML="true" readonly="true"
                                   helpText="IA.SEPARATE_EMAIL_ADDRESSES_WITH_COMMAS_OR">
                                PREVIEWLINK
                            </field>
                        </child>
                        <child>
                            <field hidden="true" readonly="true">
                                EMAILPOPUP_SUPDOCID
                            </field>
                        </child>
                        <child>
                            <field hidden="true">INCLUDE_ATTACHMENTS</field>
                        </child>
                        <child>
                            <field hidden="true" forceReadWrite="true"
                                   clazz="PCOMultipleEmailsField">CUSTOMEREMAIL
                            </field>
                        </child>
                        <child>
                            <field hidden="true" forceReadWrite="true"
                                   clazz="PCOMultipleEmailsField">EMAILCC
                            </field>
                        </child>
                        <child>
                            <field hidden="true" forceReadWrite="true"
                                   clazz="PCOMultipleEmailsField">EMAILBCC
                            </field>
                        </child>
                        <child>
                            <field hidden="true" forceReadWrite="true">
                                <path>EMAILTEMPLATE</path>
                                <events>
                                    <change>pcoFetchEmailTemplate(this.meta);</change>
                                </events>
                            </field>
                        </child>
                        <child>
                            <section title="IA.FROM" className="subSection">
                                <child>
                                    <field hidden="true" forceReadWrite="true">
                                        SENDERNAME
                                    </field>
                                </child>
                                <child>
                                    <field hidden="true" forceReadWrite="true">
                                        SENDEREMAIL
                                    </field>
                                </child>
                                <child>
                                    <field hidden="true" forceReadWrite="true">
                                        SENDERPHONE
                                    </field>
                                </child>
                            </section>
                        </child>
                    </section>
                </page>
            </pages>
            <footer>
                <button>
                    <name>IA.OK</name>
                    <events>
                        <click>pcoDocPrintEmail(this.meta);</click>
                    </events>
                </button>
                <button>
                    <name>IA.CANCEL</name>
                    <events>
                        <click>window.editor.hidePage('printEmailPage',false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
    </view>
    <helpfile>Adding_Editing_and_Viewing_a_ProjectChangeOrder</helpfile>
</ROOT>
