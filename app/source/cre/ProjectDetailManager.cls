<?php
/**
 * File ProjectDetailManager.cls contains the class ProjectDetailManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2023 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Class ProjectDetailManager
 */
class ProjectDetailManager extends EntityManager
{
    /**
     * @param bool        $merged
     *
     * @return array
     */
    public static function proxy_getDimensionPreferencesInfo($merged = false)
    {
        return IADimensions::getDimensionObjectsInfo('gl', $merged);
    }
    
}