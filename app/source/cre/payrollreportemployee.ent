<?php

/**
 * Entity for the Payroll Report Employee object
 *
 * <AUTHOR> M <<EMAIL>>
 * @copyright 2024 Sage Intacct Inc.
 *
 */
global $gRecordNoFormat, $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gCreatedByFieldInfo, $gModifiedByFieldInfo, $gEmployeeIDFormat, $gBooleanType, $gDateType, $gPositiveNumberFormat;
$kSchemas['payrollreportemployee'] = [
    'children' => [
        'employee' => [
            'fkey' => 'employeekey',
            'invfkey' => 'record#',
            'table' => 'employee',
            'join' => 'outer',
        ],
        'payrollreportemployeetradedetail' => [
            'fkey' => 'record#',
            'invfkey' => 'payrollemployeekey',
            'table' => 'payrollreportemployeetradedetail',
            'join' => 'outer',
        ],
        'payrollreportemployeetaxclassification' => [
            'fkey' => 'record#',
            'invfkey' => 'payrollemployeekey',
            'table' => 'PAYROLLREPORTEMPLOYEETAXCLASSIFICATION',
            'join' => 'outer',
        ],
        'payrollreportpaymodifiersetup' => [
            'fkey' => 'record#',
            'invfkey' => 'payrollreportemployeekey',
            'table' => 'payrollreportpaymodifiersetup',
            'join' => 'outer',
        ],
    ],
    'nexus' => [
        'EMPLOYEE' => [
            'object' => 'employee',
            'relation' => ONE2ONE,
            'field' => 'EMPLOYEEID',
        ],
        'PAYROLLREPORTEMPLOYEETRADEDETAIL' => [
            'object' => 'payrollreportemployeetradedetail',
            'relation' => ONE2MANY,
            'field' => 'RECORDNO',
        ],
        'PAYROLLREPORTEMPLOYEETAXCLASSIFICATION' => [
            'object' => 'PAYROLLREPORTEMPLOYEETAXCLASSIFICATION',
            'relation' => ONE2MANY,
            'field' => 'RECORDNO',
        ],
        'PAYROLLREPORTPAYMODIFIERSETUP' => [
            'object' => 'PAYROLLREPORTPAYMODIFIERSETUP',
            'relation' => ONE2MANY,
            'field' => 'RECORDNO',
        ],
    ],
    'schema' => [
        'RECORDNO' => 'record#',
        'EMPLOYEEID'   => 'employee.employeeid',
        'EMPLOYEEKEY'  => 'employeekey',
        'EFFECTIVEDATE' => 'effectivedate',
        'EXPIRATIONDATE' => 'expirationdate',
        'PAYGROUPID' => 'paygroupid',
        'ISDISABLED' => 'isdisabled',
        'ISFULLTIME' => 'isfulltime',
        'ISSEASONAL' => 'isseasonal',
        'ISSTATUTORY' => 'isstatutory',
        'EXCLUDEFROMCERTIFIEDPAYROLL' => 'excludefromcertifiedpayroll',
        'ACCEPTSELECTRONICW2' => 'acceptselectronicw2',
        'ETHNICITY' => 'ethnicity',
        'EEOJOBCATEGORY' => 'eeojobcategory',
        'EEOETHNICITY' => 'eeoethnicity',
        'CAEEOETHNICITY' => 'caeeoethnicity',
        'STANDARDOCCUPATIONALCLASSIFICATIONCODE' => 'standardoccupationalclassificationcode',
        'ALASKAGEOGRAPHICCODE' => 'alaskageographiccode',
        'DEFAULTTRADEID' => 'defaulttradeid',
        'PTOACCRUALBASISDATE' => 'ptoaccrualbasisdate',
        'PTOACCRUALSCHEDULES' => 'ptoaccrualschedules',
        'REVISIONNUMBER' => 'revisionnumber',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
    ],

    'object' => [
        'RECORDNO',
        'EMPLOYEEID',
        'EMPLOYEEKEY',
        'EFFECTIVEDATE',
        'EXPIRATIONDATE',
        'ACTIVERECORD',
        'PAYGROUPID',
        'ISDISABLED',
        'ISFULLTIME',
        'ISSEASONAL',
        'ISSTATUTORY',
        'EXCLUDEFROMCERTIFIEDPAYROLL',
        'ACCEPTSELECTRONICW2',
        'ETHNICITY',
        'EEOJOBCATEGORY',
        'EEOETHNICITY',
        'CAEEOETHNICITY',
        'STANDARDOCCUPATIONALCLASSIFICATIONCODE',
        'ALASKAGEOGRAPHICCODE',
        'DEFAULTTRADEID',
        'PTOACCRUALBASISDATE',
        'PTOACCRUALSCHEDULES',
        'REVISIONNUMBER',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'publish' => [
        'RECORDNO',
        'EMPLOYEEID',
        'EMPLOYEEKEY',
        'EFFECTIVEDATE',
        'EXPIRATIONDATE',
        'ACTIVERECORD',
        'PAYGROUPID',
        'ISDISABLED',
        'ISFULLTIME',
        'ISSEASONAL',
        'ISSTATUTORY',
        'EXCLUDEFROMCERTIFIEDPAYROLL',
        'ACCEPTSELECTRONICW2',
        'ETHNICITY',
        'EEOJOBCATEGORY',
        'EEOETHNICITY',
        'CAEEOETHNICITY',
        'STANDARDOCCUPATIONALCLASSIFICATIONCODE',
        'ALASKAGEOGRAPHICCODE',
        'DEFAULTTRADEID',
        'PTOACCRUALBASISDATE',
        'PTOACCRUALSCHEDULES',
        'REVISIONNUMBER',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NO',
            'desc' => 'IA.RECORD_NO',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ],
            'disableReportSelection' => true,
            'hidden' => true,
            'readonly' => true,
            'noapiadd' => true,
            'id' => 1,
        ],
        [
            'path' => 'EMPLOYEEID',
            'fullname' => 'IA.EMPLOYEE_ID',
            'desc' => 'IA.EMPLOYEE_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => $gEmployeeIDFormat,
            ],
            'required' => true,
            'id' => 2,
        ],
        [
            'path' => 'EMPLOYEEKEY',
            'fullname' => 'IA.EMPLOYEE_KEY',
            'desc' => 'IA.EMPLOYEE_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ],
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 3,
        ],
        [
            'path' => 'EFFECTIVEDATE',
            'fullname' => 'IA.EFFECTIVE_DATE',
            'desc' => 'IA.EFFECTIVE_DATE',
            'type' => $gDateType,
            'required' => true,
            'id' => 4,
        ],
        [
            'path' => 'EXPIRATIONDATE',
            'fullname' => 'IA.EXPIRATION_DATE',
            'desc' => 'IA.EXPIRATION_DATE',
            'type' => $gDateType,
            'id' => 5,
        ],
        [
            'path' => 'ACTIVERECORD',
            'fullname' => 'IA.ACTIVE_RECORD',
            'desc' => 'IA.ACTIVE_RECORD',
            'type' => $gBooleanType,
            'formula' => array(
                'fields' => array('EFFECTIVEDATE', 'EXPIRATIONDATE'),
                'typeOf' => 'PAYGROUPID',
                'function' => "CASE WHEN \${1} <= CURRENT_DATE AND CURRENT_DATE <= NVL( \${2}, CURRENT_DATE) THEN 'true' ELSE 'false' END",
            ),
            'rpdFormula' => array(
                'fields' => array('EFFECTIVEDATE', 'EXPIRATIONDATE'),
                'function' => "CASE WHEN \${1} <= CURRENT_DATE AND CURRENT_DATE <= IFNULL( \${2}, CURRENT_DATE) THEN 'IA.TRUE' ELSE 'IA.FALSE' END",
                'tokenMap' => [ // Do not change the tokanMap entries unless you clear this up with someone who knows the RPD build process
                                // These values are used in the current ICRW reports' results, filters and prompts
                                'IA.TRUE' => 'true',
                                'IA.FALSE' => 'false',
                ],
            ),
            'calculated' => true,
            'readonly' => true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'id' => 23,
        ],
        [
            'path' => 'PAYGROUPID',
            'fullname' => 'IA.PAY_GROUP_ID',
            'desc' => 'IA.PAY_GROUP_ID',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 6,
        ],
        [
            'path' => 'ISDISABLED',
            'fullname' => 'IA.IS_DISABLED',
            'desc' => 'IA.IS_DISABLED',
            'type' => $gBooleanType,
            'id' => 7,
        ],
        [
            'path' => 'ISFULLTIME',
            'fullname' => 'IA.IS_FULL_TIME',
            'desc' => 'IA.IS_FULL_TIME',
            'type' => $gBooleanType,
            'id' => 8,
        ],
        [
            'path' => 'ISSEASONAL',
            'fullname' => 'IA.IS_SEASONAL',
            'desc' => 'IA.IS_SEASONAL',
            'type' => $gBooleanType,
            'id' => 9,
        ],
        [
            'path' => 'ISSTATUTORY',
            'fullname' => 'IA.IS_STATUTORY',
            'desc' => 'IA.IS_STATUTORY',
            'type' => $gBooleanType,
            'id' => 10,
        ],
        [
            'path' => 'EXCLUDEFROMCERTIFIEDPAYROLL',
            'fullname' => 'IA.EXCLUDE_FROM_CERTIFIED_PAYROLL',
            'desc' => 'IA.EXCLUDE_FROM_CERTIFIED_PAYROLL',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 11,
        ],
        [
            'path' => 'ACCEPTSELECTRONICW2',
            'fullname' => 'IA.ACCEPTS_ELECTRONIC_W2',
            'desc' => 'IA.ACCEPTS_ELECTRONIC_W2',
            'type' => $gBooleanType,
            'id' => 12,
        ],
        [
            'path' => 'ETHNICITY',
            'fullname' => 'IA.ETHNICITY',
            'desc' => 'IA.ETHNICITY',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 13,
        ],
        [
            'path' => 'EEOJOBCATEGORY',
            'fullname' => 'IA.EEO_JOB_CATEGORY',
            'desc' => 'IA.EEO_JOB_CATEGORY',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 14,
        ],
        [
            'path' => 'EEOETHNICITY',
            'fullname' => 'IA.EEO_ETHNICITY',
            'desc' => 'IA.EEO_ETHNICITY',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 15,
        ],
        [
            'path' => 'CAEEOETHNICITY',
            'fullname' => 'IA.CA_EEO_ETHNICITY',
            'desc' => 'IA.CA_EEO_ETHNICITY',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 16,
        ],
        [
            'path' => 'STANDARDOCCUPATIONALCLASSIFICATIONCODE',
            'fullname' => 'IA.STANDARD_OCCUPATIONAL_CLASSIFICATION_CODE',
            'desc' => 'IA.STANDARD_OCCUPATIONAL_CLASSIFICATION_CODE',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 17,
        ],
        [
            'path' => 'ALASKAGEOGRAPHICCODE',
            'fullname' => 'IA.ALASKA_GEOGRAPHIC_CODE',
            'desc' => 'IA.ALASKA_GEOGRAPHIC_CODE',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 10,
                'maxlength' => 10,
            ],
            'id' => 18,
        ],
        [
            'path' => 'DEFAULTTRADEID',
            'fullname' => 'IA.DEFAULT_TRADE_ID',
            'desc' => 'IA.DEFAULT_TRADE_ID',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 19,
        ],
        [
            'path' => 'PTOACCRUALBASISDATE',
            'fullname' => 'IA.PTO_ACCRUAL_BASIS_DATE',
            'desc' => 'IA.PTO_ACCRUAL_BASIS_DATE',
            'type' => $gDateType,
            'id' => 20,
        ],
        [
            'path' => 'PTOACCRUALSCHEDULES',
            'fullname' => 'IA.PTO_ACCRUAL_SCHEDULES',
            'desc' => 'IA.PTO_ACCRUAL_SCHEDULES',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 21,
        ],
        [
            'path' => 'REVISIONNUMBER',
            'fullname' => 'IA.REVISION_NUMBER',
            'desc' => 'IA.REVISION_NUMBER',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 30
            ],
            'id' => 22,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'ownedobjects' => [
        [
            'fkey'    => 'PAYROLLEMPLOYEEKEY',
            'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity'  => 'payrollreportemployeetradedetail',
            'path'    => 'PAYROLL_REPORT_EMPLOYEE_TRADE_DETAIL',
        ],
        [
            'fkey'    => 'PAYROLLEMPLOYEEKEY',
            'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity'  => 'payrollreportemployeetaxclassification',
            'path'    => 'PAYROLL_REPORT_EMPLOYEE_TAX_CLASSIFICATION',
        ],
    ],
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED'
    ),
    'url' => [
        'no_short_url' => true,    // Don't allow short url
    ],
    'table' => 'payrollreportemployee',
    'printas' => 'IA.PAYROLL_REPORT_EMPLOYEE',
    'pluralprintas' => 'IA.PAYROLL_REPORT_EMPLOYEES',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'module' => 'pa',
    'description' => 'IA.PAYROLL_REPORT_EMPLOYEE',
    'allowDDS' => false,
    'platformProperties' => [
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ],
    'allowTriggers' => false,
    'api' => [
        'PERMISSION_MODULES' => ['pay'],
        'ITEM_ALIAS' => ['PAYROLLREPORTEMPLOYEETRADEDETAIL','PAYROLLREPORTEMPLOYEETAXCLASSIFICATION'],
        'ITEMS_ALIAS' => ['PAYROLLREPORTEMPLOYEETRADEDETAILS','PAYROLLREPORTEMPLOYEETAXCLASSIFICATIONS'],
        'ITEMS_INTERNAL' => ['PAYROLL_REPORT_EMPLOYEE_TRADE_DETAIL','PAYROLL_REPORT_EMPLOYEE_TAX_CLASSIFICATION'],
        'GET_BY_GET' => true,
    ],
    'auditcolumns' => true,
];