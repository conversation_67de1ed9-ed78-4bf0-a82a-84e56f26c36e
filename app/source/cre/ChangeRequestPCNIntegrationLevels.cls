<?php
/**
 * Encapsulates requirements for change request integration with project contract
 */
class ChangeRequestPCNIntegrationLevels
{
    const ERROR_CR_PCN_INVALID = 1;
    const ERROR_CR_PCL_INVALID = 2;
    const ERROR_CRE_PCN_INVALID = 3;
    const ERROR_CRE_PCL_INVALID = 4;
    const ERROR_NONE = 0;
    
    private static string $renamedProject;
    
    private static string $renamedProjectContract;
    
    public function __construct()
    {
        self::$renamedProject = strtolower(I18N::getSingleToken('IA.PROJECT'));
        self::$renamedProjectContract = strtolower(I18N::getSingleToken('IA.PROJECT_CONTRACT'));
    }
    
    /**
     * Add error for cr, pcn validation
     *
     * @param int $error error level
     *
     * @return void
     */
    public function addError(int $error) : void
    {
        if ( $error === self::ERROR_NONE) {
            return;
        }

        match($error) {
            self::ERROR_CR_PCN_INVALID =>
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0659',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                                'PROJECT' => self::$renamedProject,
                                'PROJECT_CONTRACT' => self::$renamedProjectContract
                            ]
                ),
            self::ERROR_CR_PCL_INVALID =>
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0660',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                                'PROJECT' => self::$renamedProject,
                                'PROJECT_CONTRACT' => self::$renamedProjectContract
                            ]
                ),
            self::ERROR_CRE_PCN_INVALID =>
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0661',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                                'PROJECT' => self::$renamedProject,
                                'PROJECT_CONTRACT' => self::$renamedProjectContract
                            ]
                ),
            self::ERROR_CRE_PCL_INVALID =>
                Globals::$g->gErr->addIAError(
                    number: 'CRE-0662',
                    source: __FILE__ . ':' . __LINE__,
                    desc1PHs: [
                                'PROJECT' => self::$renamedProject,
                                'PROJECT_CONTRACT' => self::$renamedProjectContract
                            ]
                ),
            default => '',
        };
    }
    
    /**
     * Gets valid PCN ids for a change request
     *
     * @param string $crProjectId Change request project id
     *
     * @return array PCN id array
     */
    public function getPCNIdsForChangeRequest(string $crProjectId) : array
    {
        $pcnIds = [];
        $results = self::getPCNsForChangeRequest($crProjectId);
        foreach ($results as $result) {
            array_push($pcnIds, $result['PROJECTCONTRACTID']);
        }
    
        return $pcnIds;
    }
    
    /**
     * Gets valid PCL ids for a change request
     *
     * @param string $crProjectId Change request project id
     * @param string $crProjectContractId Change request project contract id
     *
     * @return array PCL id array
     */
    public function getPCLIdsForChangeRequest(string $crProjectId, string $crProjectContractId) : array
    {
        $pclIds = [];
        $results = self::getPCLsForChangeRequest($crProjectId, $crProjectContractId);
        foreach ($results as $result) {
            array_push($pclIds, $result['PROJECTCONTRACTLINEID']);
        }
    
        return $pclIds;
    }
    
    
    /**
     * Gets valid project contract records for a change request.
     *
     * @param string $crProjectId Change request project id
     *
     * @return array project contract records array: [PROJECTCONTRACTID, NAME, PROJECTID]
     */
    public static function getPCNsForChangeRequest(string $crProjectId) : array
    {
        $projectIds = self::getAncestorProjectsInclusiveForChangeRequestFormattedForSQLInClause($crProjectId);
        $pcnQuery = "select pcn.projectcontractid, pcn.name, pj.projectid
                     from projectcontract pcn, project pj
                     where pcn.cny# = :1
                     and pj.cny# = pcn.cny#
                     and pcn.projectkey = pj.record#
                     and pcn.status = 'T' ";
    
        $pcnQuery = PrepINClauseStmt($pcnQuery, $projectIds, " and pj.projectid ");
        $pcnQuery .= " order by pcn.projectcontractid";
        $pcnQuery = [$pcnQuery, GetMyCompany()];
    
        return self::executeQuery($pcnQuery);
    }
    
    /**
     * Gets valid project contract line records for a change request.
     *
     * @param string $crProjectId Change request project id
     * @param string $crProjectContractId Change request project contract id
     *
     * @return array project contract line records array: [PROJECTCONTRACTLINEID, NAME, PROJECTCONTRACTID, PROJECTID]
     */
    public static function getPCLsForChangeRequest(string $crProjectId, string $crProjectContractId) : array
    {
        $projectIds = self::getAncestorProjectsInclusiveForChangeRequestFormattedForSQLInClause($crProjectId);
        $pclQuery = "select pcl.projectcontractlineid, pcl.name, pcn.projectcontractid, pj.projectid
                     from projectcontractline pcl, projectcontract pcn, project pj
                     where pcl.cny# = :1
                     and pcl.cny# = pj.cny#
                     and pcl.cny# = pcn.cny#
                     and pcl.projectcontractkey = pcn.record#
                     and pcl.projectdimkey = pj.record#
                     and pcl.status = 'T'
                     and pcn.projectcontractid = :2
                     and pcn.status = 'T' ";
    
        $pclQuery = PrepINClauseStmt($pclQuery, $projectIds, " and pj.projectid ");
        $pclQuery .= " order by pcl.projectcontractlineid";
        $pclQuery = [$pclQuery, GetMyCompany(), $crProjectContractId];
    
        return self::executeQuery($pclQuery);
    }
    
    /**
     * Returns project ids for a change request, based on the project selected for the change request.
     * This includes ancestor projects in the hierarchy.
     *
     * @param string $crProjectId Change request project id
     *
     * @return string[] Project ids array
     */
    private static function getAncestorProjectsInclusiveForChangeRequestFormattedForSQLInClause(string $crProjectId) :
    array
    {
        // Single quotes added for SQL, since the results are used with IN clause
        $projectIds = [ "'" . $crProjectId . "'"];
        if (isNullOrBlank($crProjectId)) {
            return $projectIds;
        }
        $crMgr = Globals::$g->gManagerFactory->getManager('changerequest');
        $ancestorProjects = $crMgr->getParentHierarchyForProject($crProjectId);
        foreach($ancestorProjects as $ancestorProject) {
            array_push($projectIds, "'" . $ancestorProject . "'");
        }
        
        return $projectIds;
    }
    
    /**
     * Wrapper for QueryResult, to return array
     *
     * @param array $queryParams
     *
     * @return array query result
     */
    private static function executeQuery(array $queryParams) : array
    {
        $queryResult = QueryResult($queryParams);
        // If QueryResult returns false, assign empty array
        if (!$queryResult) {
            $queryResult = [];
        }
        return $queryResult;
    }
}