<child>
    <section isCollapsible="true" title="IA.PRIMARY_DOCUMENT_DETAILS">
        <grid readonly="true" hideTitle="true" clazz="EntriesGrid" className="columns3Grid" hidden="true" title="IA.PRIMARY_DOCUMENT_DETAILS" id="grid_primary_document_details">
            <path>PRIMARY_DOC_DETAILS</path>
            <gridBulkActions>
                <button id="ctxAction" path="ctxAction">
                    <name>IA.EXPORT_TO_CSV</name>
                    <events>
                        <click>gridToCSV.export('PRIMARY_DOC_DETAILS', 'IA.PRIMARY_DOCUMENT_DETAILS');</click>
                    </events>
                </button>
            </gridBulkActions>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="LINE_NO">
                        <path>SEARCH_PDD_LINE_NO</path>
                    </field>
                </gridHeading>
                <field label="IA.LINE_NUMBER" sortable="true">
                    <path>LINE_NO</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="PROJECTNAME">
                        <path>SEARCH_PDD_PROJECTNAME</path>
                    </field>
                </gridHeading>
                <field label="IA.PROJECT_DIMENSION_ID_AND_NAME" sortable="true">
                    <path>PROJECTNAME</path>
                    <type assoc="T">
                        <type>text</type>
                        <ptype>href</ptype>
                    </type>
                    <events>
                        <click>
                            PrimaryDocumentDetailsTabHandler.openDrilldownPageUsingRecordId(this.meta, "project", "PROJECTID");
                        </click>
                    </events>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="TASKNAME">
                        <path>SEARCH_PDD_TASKNAME</path>
                    </field>
                </gridHeading>
                <field label="IA.TASK_DIMENSION_ID_AND_NAME" sortable="true">
                    <path>TASKNAME</path>
                    <type assoc="T">
                        <type>text</type>
                        <ptype>href</ptype>
                    </type>
                    <events>
                        <click>
                            PrimaryDocumentDetailsTabHandler.openDrilldownPageUsingRecordId(this.meta, "task", "TASKKEY");
                        </click>
                    </events>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="ORIGINAL_AMOUNT">
                        <path>SEARCH_PDD_ORIGINAL_AMOUNT</path>
                    </field>
                </gridHeading>
                <field label="IA.ORIGINAL_AMOUNT" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>ORIGINAL_AMOUNT</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="PENDING_CHANGES">
                        <path>SEARCH_PDD_PENDING_CHANGES</path>
                    </field>
                </gridHeading>
                <field label="IA.PENDING_CHANGES" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>PENDING_CHANGES</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="POSTED_CHANGES">
                        <path>SEARCH_PDD_POSTED_CHANGES</path>
                    </field>
                </gridHeading>
                <field label="IA.POSTED_CHANGES" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>POSTED_CHANGES</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="REVISED_AMOUNT">
                        <path>SEARCH_PDD_REVISED_AMOUNT</path>
                    </field>
                </gridHeading>
                <field label="IA.REVISED_AMOUNT" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>REVISED_AMOUNT</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="TOTAL_AMOUNT_INVOICED">
                        <path>SEARCH_PDD_TOTAL_AMOUNT_INVOICED</path>
                    </field>
                </gridHeading>
                <field label="IA.AMOUNT_INVOICED" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>TOTAL_AMOUNT_INVOICED</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="RETAINAGE_HELD">
                        <path>SEARCH_PDD_RETAINAGE_HELD</path>
                    </field>
                </gridHeading>
                <field label="IA.RETAINAGE_HELD" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>RETAINAGE_HELD</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="NET_RETAINAGE_AMOUNT_INVOICED">
                        <path>SEARCH_PDD_NET_RETAINAGE_AMOUNT_INVOICED</path>
                    </field>
                </gridHeading>
                <field label="IA.AMOUNT_INVOICED_NET_RETAINAGE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>NET_RETAINAGE_AMOUNT_INVOICED</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="NET_RETAINAGE_AMOUNT_PAID">
                        <path>SEARCH_PDD_NET_RETAINAGE_AMOUNT_PAID</path>
                    </field>
                </gridHeading>
                <field label="IA.AMOUNT_PAID_NET_RETAINAGE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>NET_RETAINAGE_AMOUNT_PAID</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="NET_RETAINAGE_INVOICE_BALANCE">
                        <path>SEARCH_PDD_NET_RETAINAGE_INVOICE_BALANCE</path>
                    </field>
                </gridHeading>
                <field label="IA.INVOICE_BALANCE_NET_RETAINAGE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>NET_RETAINAGE_INVOICE_BALANCE</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="RETAINAGE_RELEASED">
                        <path>SEARCH_PDD_RETAINAGE_RELEASED</path>
                    </field>
                </gridHeading>
                <field label="IA.RETAINAGE_RELEASED" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>RETAINAGE_RELEASED</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="RETAINAGE_HELD_BALANCE">
                        <path>SEARCH_PDD_RETAINAGE_HELD_BALANCE</path>
                    </field>
                </gridHeading>
                <field label="IA.RETAINAGE_HELD_BALANCE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>RETAINAGE_HELD_BALANCE</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="RETAINAGE_PAID">
                        <path>SEARCH_PDD_RETAINAGE_PAID</path>
                    </field>
                </gridHeading>
                <field label="IA.RETAINAGE_PAID" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>RETAINAGE_PAID</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="RETAINAGE_INVOICE_BALANCE">
                        <path>SEARCH_PDD_RETAINAGE_INVOICE_BALANCE</path>
                    </field>
                </gridHeading>
                <field label="IA.RETAINAGE_INVOICE_BALANCE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>RETAINAGE_INVOICE_BALANCE</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="TOTAL_PAID">
                        <path>SEARCH_PDD_TOTAL_PAID</path>
                    </field>
                </gridHeading>
                <field label="IA.TOTAL_PAID" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>TOTAL_PAID</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="INVOICE_BALANCE">
                        <path>SEARCH_PDD_INVOICE_BALANCE</path>
                    </field>
                </gridHeading>
                <field label="IA.INVOICE_BALANCE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>INVOICE_BALANCE</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="REMAINING_TO_INVOICE">
                        <path>SEARCH_PDD_REMAINING_TO_INVOICE</path>
                    </field>
                </gridHeading>
                <field label="IA.REMAINING_TO_INVOICE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>REMAINING_TO_INVOICE</path>
                </field>
            </column>
            <lineDetails>
                <pages>
                    <page title="IA.DETAILS">
                        <section columnCount="5">
                            <field label="IA.PRIMARY_DOCUMENT_ID" sortable="true">
                                <path>DOCNAME</path>
                                <type assoc="T">
                                    <type>text</type>
                                    <ptype>href</ptype>
                                </type>
                                <events>
                                    <click>
                                        PrimaryDocumentDetailsTabHandler.openDrilldownPageUsingRecordId(this.meta, "podocument", "DOCKEY");
                                    </click>
                                </events>
                            </field>
                            <field label="IA.COST_TYPE_DIMENSION_ID_AND_NAME" sortable="true">
                                <path>COSTTYPENAME</path>
                                <type assoc="T">
                                    <type>text</type>
                                    <ptype>href</ptype>
                                </type>
                                <events>
                                    <click>
                                        PrimaryDocumentDetailsTabHandler.openDrilldownPageUsingRecordId(this.meta, "costtype", "COSTTYPEKEY");
                                    </click>
                                </events>
                            </field>
                            <field label="IA.ITEM_DIMENSION_ID_AND_NAME" sortable="true">
                                <path>ITEMNAME</path>
                                <type assoc="T">
                                    <type>text</type>
                                    <ptype>href</ptype>
                                </type>
                                <events>
                                    <click>
                                        PrimaryDocumentDetailsTabHandler.openDrilldownPageUsingRecordId(this.meta, "item", "ITEMID");
                                    </click>
                                </events>
                            </field>
                            <field label="IA.LOCATION_DIMENSION_ID_AND_NAME" sortable="true">
                                <path>LOCATIONNAME</path>
                                <type assoc="T">
                                    <type>text</type>
                                    <ptype>href</ptype>
                                </type>
                                <events>
                                    <click>
                                        PrimaryDocumentDetailsTabHandler.openDrilldownPageUsingRecordId(this.meta, "location", "LOCATIONKEY");
                                    </click>
                                </events>
                            </field>
                            <field label="IA.DEPARTMENT_DIMENSION_ID_AND_NAME" sortable="true">
                                <path>DEPARTMENTNAME</path>
                            </field>
                            <field label="IA.MEMO" sortable="true">
                                <path>MEMO</path>
                            </field>
                            <field label="IA.BASE_ORIGINAL_AMOUNT" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_ORIGINAL_AMOUNT</path>
                            </field>
                            <field label="IA.BASE_PENDING_CHANGES" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_PENDING_CHANGES</path>
                            </field>
                            <field label="IA.BASE_POSTED_CHANGES" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_POSTED_CHANGES</path>
                            </field>
                            <field label="IA.BASE_REVISED_AMOUNT" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_REVISED_AMOUNT</path>
                            </field>
                            <field label="IA.BASE_AMOUNT_INVOICED" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_TOTAL_AMOUNT_INVOICED</path>
                            </field>
                            <field label="IA.BASE_RETAINAGE_HELD" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_RETAINAGE_HELD</path>
                            </field>
                            <field label="IA.BASE_AMOUNT_INVOICED_NET_RETAINAGE" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_NET_RETAINAGE_AMOUNT_INVOICED</path>
                            </field>
                            <field label="IA.BASE_AMOUNT_PAID_NET_RETAINAGE" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_NET_RETAINAGE_AMOUNT_PAID</path>
                            </field>
                            <field label="IA.BASE_RETAINAGE_RELEASED" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_RETAINAGE_RELEASED</path>
                            </field>
                            <field label="IA.BASE_RETAINAGE_PAID" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_RETAINAGE_PAID</path>
                            </field>
                            <field label="IA.BASE_TOTAL_PAID" sortable="true" hasTotal="true">
                                <type type="decimal" ptype="currency"/>
                                <path>BASE_TOTAL_PAID</path>
                            </field>
                        </section>
                    </page>
                </pages>
            </lineDetails>
        </grid>
    </section>
</child>
