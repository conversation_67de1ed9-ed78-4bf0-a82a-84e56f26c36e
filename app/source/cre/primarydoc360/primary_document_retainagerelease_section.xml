<child>
    <section isCollapsible="true" title="IA.RETAINAGE_RELEASE_BILLS">
        <grid readonly="true" className="columns3Grid">
            <path>RETAINAGE_RELEASE_BILLS</path>
            <gridBulkActions>
                <button id="ctxAction" path="ctxAction">
                    <name>IA.EXPORT_TO_CSV</name>
                    <events>
                        <click>gridToCSV.export('RETAINAGE_RELEASE_BILLS', 'IA.RETAINAGE_RELEASE_BILLS');</click>
                    </events>
                </button>
            </gridBulkActions>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="BILLNO">
                        <path>SEARCH_PDRRB_BILLNO</path>
                    </field>
                </gridHeading>
                <field label="IA.BILL_NUMBER" sortable="true">
                    <path>BILLNO</path>
                    <type assoc="T">
                        <type>text</type>
                        <ptype>href</ptype>
                    </type>
                    <events>
                        <click>
                            PrimaryDocumentDetailsTabHandler.openDrilldownPageUsingRecordId(this.meta, "apbill", "APBILL");
                        </click>
                    </events>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="BILL_STATE">
                        <path>SEARCH_PDRRB_BILL_STATE</path>
                    </field>
                </gridHeading>
                <field label="IA.STATE" sortable="true">
                    <path>BILL_STATE</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="BILL_DATE">
                        <path>SEARCH_PDRRB_BILL_DATE</path>
                    </field>
                </gridHeading>
                <field label="IA.DATE" sortable="true">
                    <path>BILL_DATE</path>
                    <type type="date" ptype="date"/>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="GLPOSTING_DATE">
                        <path>SEARCH_PDRRB_GLPOSTING_DATE</path>
                    </field>
                </gridHeading>
                <field label="IA.GL_POSTING_DATE" sortable="true">
                    <path>GLPOSTING_DATE</path>
                    <type type="date" ptype="date"/>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="TOTAL_AMOUNT_INVOICED">
                        <path>SEARCH_PDRRB_TOTAL_AMOUNT_INVOICED</path>
                    </field>
                </gridHeading>
                <field label="IA.AMOUNT_INVOICED" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>TOTAL_AMOUNT_INVOICED</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="TOTAL_AMOUNT_PAID">
                        <path>SEARCH_PDRRB_TOTAL_AMOUNT_PAID</path>
                    </field>
                </gridHeading>
                <field label="IA.AMOUNT_PAID" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>TOTAL_AMOUNT_PAID</path>
                </field>
            </column>
            <column>
                <gridHeading className="center">
                    <field noLabel="true" searchPath="INVOICE_BALANCE">
                        <path>SEARCH_PDRRB_INVOICE_BALANCE</path>
                    </field>
                </gridHeading>
                <field label="IA.INVOICE_BALANCE" sortable="true" hasTotal="true">
                    <type type="decimal" ptype="currency"/>
                    <path>INVOICE_BALANCE</path>
                </field>
            </column>
        </grid>
    </section>
</child>
