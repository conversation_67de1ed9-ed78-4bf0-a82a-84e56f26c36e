<?php
/**
 * Manager class for the LaborUnion object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2021 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class LaborUnionManager extends EntityManager
{
    /**
     * Constructor of the class
     *
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Overridden function to support Get call to retrieve LaborUnion object
     *
     * @param string        $ID
     * @param string[]|null $fields
     *
     * @return array|false
     */
    public function get($ID, $fields=null)
    {
        [$laborUnionId] = explode('--', $ID);
        $obj = parent::get($laborUnionId);

        return $obj;
    }
}
