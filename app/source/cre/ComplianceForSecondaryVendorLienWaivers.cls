<?php
/**
 * Compliance for Secondary Vendor Lien <PERSON>
 */
class ComplianceForSecondaryVendorLienWaivers
{
    private string $primaryDocKey = '';
    private array $secondaryVendors = [];

    public function __construct(string $primaryDocKey)
    {
        $this->primaryDocKey = $primaryDocKey;
    }

    /**
     * Get secondary vendors for primary doc
     * @return array
     */
    public function getSecondaryVendors() : array
    {
        $this->secondaryVendorManager = Globals::$g->gManagerFactory->getManager('secondaryvendor');
        $this->secondaryVendors = $this->secondaryVendorManager->getSecondaryVendorsForPrimaryDoc([$this->primaryDocKey]);

        return $this->secondaryVendors;
    }

    /**
     * Check if lien waiver record should be generated
     * @param string $recordNo
     * @param string $generationEvent
     * @param string $checkPrintAs
     * @param string $prRecordKey
     * @return bool
     */
    public function shouldGenerateLienWaiverRecord(string $recordNo, string $generationEvent, string $checkPrintAs = '', string $prRecordKey = '') : bool
    {
        if ($generationEvent === ComplianceDefinitionManager::apBill) {
            return $this->shouldGenerateForInvoice($recordNo);
        } elseif ($generationEvent === ComplianceDefinitionManager::apPayment) {
            return $this->shouldGenerateForPayment($recordNo, $checkPrintAs, $prRecordKey);
        }

        return false;
    }

    /**
     * Check if lien waiver record for invoice should be generated
     * @param string $recordNo
     * @return bool
     */
    private function shouldGenerateForInvoice(string $recordNo) : bool
    {
        $secondaryVendor = $this->getSecondaryVendorRecord($recordNo);
        if (empty($secondaryVendor)) {
            return false;
        }

        return !empty($secondaryVendor['GENINVOICELIENWAIVER']) && filter_var($secondaryVendor['GENINVOICELIENWAIVER'], FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Check if lien waiver record for payment should be generated
     * @param string $recordNo
     * @param string $checkPrintAs
     * @param string $prRecordKey
     * @return bool
     */
    private function shouldGenerateForPayment(string $recordNo, string $checkPrintAs, string $prRecordKey) : bool
    {
        $secondaryVendor = $this->getSecondaryVendorRecord($recordNo);
        if (empty($secondaryVendor)) {
            return false;
        }

        $generateForPayment = $secondaryVendor['GENPAYMENTLIENWAIVER'];
        if ($generateForPayment === SecondaryVendorConstants::GENERATE_FOR_ALL_PAYMENTS) {
            return true;
        } elseif ($generateForPayment === SecondaryVendorConstants::GENERATE_FOR_JOINT_CHECK_PAYMENTS_ONLY) {
            return $this->jointPayeesAreSame($checkPrintAs, $prRecordKey);
        }

        return false;
    }

    /**
     * Get a secondary vendor record
     * @param string $recordNo
     * @return array
     */
    private function getSecondaryVendorRecord(string $recordNo) : array
    {
        $key = array_search($recordNo, array_column($this->secondaryVendors, 'RECORDNO'), true);
        return $key === false ? [] : $this->secondaryVendors[$key];
    }

    /**
     * Check if check printAs value in payment matches that set for secondary vendor
     * @param string $checkPrintAs
     * @param string $prRecordKey
     * @return bool
     */
    private function jointPayeesAreSame(string $checkPrintAs, string $prRecordKey) : bool
    {
        $jointPayeeManager = Globals::$g->gManagerFactory->getManager('apbilljointpayee');
        $jointPayees = $jointPayeeManager->getJointPayeesForPRRecord($prRecordKey);
        if (empty($jointPayees)) {
            return false;
        }

        $jointPayeePrintAs = $jointPayees[0]['JOINTPAYEEPRINTAS'] ?? '';
        return $jointPayeePrintAs === $checkPrintAs;
    }
}