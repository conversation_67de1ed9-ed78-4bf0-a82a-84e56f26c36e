<?xml version="1.0" encoding='utf-8'?>
<ROOT>
    <entity>projectcontractline</entity>
    <title>IA.PROJECT_CONTRACT_LINE</title>
    <helpfile>Adding_Editing_Viewing_ProjectContractLine</helpfile>
    <view system="true">
        <events>
            <load>onContractLineLoad();</load>
        </events>
        <pages>
            <page id="pcl_general" title="IA.GENERAL">
                <title>IA.DETAILS</title>
                <child>
                    <section id="totals" className="horizontal" customFields="no">
                        <field fullname="IA.ORIGINAL_PRICE" path="HDR_ORIGINALPRICE" readonly="true" nullvalue="0.00">
                            <type type="decimal" ptype="currency"/>
                        </field>
                        <field fullname="IA.APPROVED_CHANGE_PRICE" path="HDR_APPROVEDCHANGEPRICE" readonly="true" nullvalue="0.00">
                            <type type="decimal" ptype="currency"/>
                        </field>
                        <field fullname="IA.TOTAL_REVISED_PRICE" path="HDR_TOTALREVISEDPRICE" readonly="true" nullvalue="0.00">
                            <type type="decimal" ptype="currency"/>
                        </field>
                        <field path="BILLEDPRICE" readonly="true"/>
                        <field path="RETAINAGEBALANCE" readonly="true"/>
                        <field path="PAYMENTSRECEIVED" readonly="true"/>
                    </section>
                </child>
                <child>
                    <section isCollapsible="true" showCollapsed="false" id="projectcontractline_section">
                        <title>IA.PROJECT_CONTRACT_LINE_INFORMATION</title>
                        <subsection columnCount="2">
                            <row>
                                <field readonly="true" fullname="IA.PROJECT_CONTRACT">
                                    <path>PROJECTCONTRACTID</path>
                                </field>
                                <field readonly="true">PROJECTCONTRACTLINEID</field>
                                <field>NAME</field>
                                <field>PARENTID</field>
                                <field>DESCRIPTION</field>
                                <field>CONTRACTLINEDATE</field>
                            </row>
                            <row>
                                <field>ACCOUNTNO</field>
                                <field>GLEXCLUDE</field>
                                <field>RETAINAGEPERCENTAGE</field>
                                <field>BILLABLE</field>

                                <field>PCLTAXSCHEDULEID</field>
                                <field>SUPDOCID</field>
                                <field>STATUS</field>
                            </row>
                        </subsection>
                    </section>
                    <section isCollapsible="true" showCollapsed="false" id="billing_section" columnCount="1" title="IA.BILLING">
                        <row>
                            <subsection columnCount="3">
                                <field>
                                    <path>BILLINGTYPE</path>
                                    <events>
                                        <change>onChangeBillType(this.meta);</change>
                                    </events>
                                </field>
                                <field readonly="true">
                                    <path>MAXIMUMBILLING</path>
                                    <events>
                                        <change>onChangeMaxBilling(this.meta);</change>
                                    </events>
                                </field>
                                <field hidden="true">MAXIMUMBILLINGAMOUNT</field>
                            </subsection>
                            <subsection>
                                <field hidden="true">SUMMARIZEBILL</field>
                            </subsection>
                        </row>
                        <row id="ratetable_section">
                            <subsection columnCount="1" title="IA.RATE_TABLES">
                                <subsection columnCount="1">
                                    <field>DEFAULTRATETABLEID</field>
                                </subsection>
                                <subsection columnCount="3">
                                    <field>TSRATETABLEID</field>
                                    <field>PORATETABLEID</field>
                                    <field>APRATETABLEID</field>
                                    <field>GLRATETABLEID</field>
                                    <field>CCRATETABLEID</field>
                                    <field>EERATETABLEID</field>
                                </subsection>
                            </subsection>
                        </row>
                    </section>
                    <section isCollapsible="true" showCollapsed="false" id="summary_section" columnCount="2"
                             title="IA.SUMMARY">
                        <row>
                            <field>ORIGINALPRICE</field>
                            <field>REVISIONPRICE</field>
                            <field>APPROVEDCHANGEPRICE</field>
                            <field>TOTALREVISEDPRICE</field>
                        </row>
                        <row>
                            <field>PENDINGCHANGEPRICE</field>
                            <field>OTHERPRICE</field>
                            <field>FORECASTPRICE</field>
                        </row>
                    </section>
                    <section isCollapsible="true" showCollapsed="true" id="additionalinfo_section" columnCount="1" title="IA.ADDITIONAL_INFORMATION">
                        <section id="scope_section" columnCount="3">
                            <title>IA.SCOPE</title>
                            <row>
                                <field>SCOPE</field>
                                <field>TERMS</field>
                            </row>
                            <row>
                                <field>INCLUSIONS</field>
                                <field>EXCLUSIONS</field>
                            </row>
                        </section>
                        <section id="schedule_section" columnCount="3">
                            <title>IA.SCHEDULE</title>
                            <row>
                                <field>SCHEDULEDSTARTDATE</field>
                                <field>ACTUALSTARTDATE</field>
                                <field>SCHEDULEDCOMPLETIONDATE</field>
                                <field>REVISEDCOMPLETIONDATE</field>
                            </row>
                            <row>
                                <field>SUBSTANTIALCOMPLETIONDATE</field>
                                <field>ACTUALCOMPLETIONDATE</field>
                                <field>NOTICETOPROCEED</field>
                                <field>RESPONSEDUE</field>
                            </row>
                            <row>
                                <field>EXECUTEDON</field>
                                <field>SCHEDULEIMPACT</field>
                            </row>
                        </section>
                        <section id="internalref_section" columnCount="3">
                            <title>IA.INTERNAL_REFERENCE</title>
                            <row>
                                <field>INTERNALREFNO</field>
                                <field>INTERNALINITIATEDBY</field>
                                <field>INTERNALVERBALBY</field>
                            </row>
                            <row>
                                <field>INTERNALISSUEDBY</field>
                                <field>INTERNALISSUEDON</field>
                                <field>INTERNALAPPROVEDBY</field>
                                <field>INTERNALAPPROVEDON</field>
                            </row>
                            <row>
                                <field>INTERNALSIGNEDBY</field>
                                <field>INTERNALSIGNEDON</field>
                                <field>INTERNALSOURCE</field>
                                <field>INTERNALSOURCEREFNO</field>
                            </row>
                        </section>
                        <section id="externalref_section" columnCount="3">
                            <title>IA.EXTERNAL_REFERENCE</title>
                            <field>EXTERNALREFNO</field>
                            <field>EXTERNALVERBALBY</field>
                            <field>EXTERNALAPPROVEDBY</field>
                            <field>EXTERNALAPPROVEDON</field>
                            <field>EXTERNALSIGNEDBY</field>
                            <field>EXTERNALSIGNEDON</field>
                        </section>
                    </section>
                    <section title="IA.DIMENSIONS" dimFields='PCL' columnCount="3" isCollapsible="true">
                        <field>
                            <path>PROJECTLOCATIONID</path>
                            <type assoc="T">
                                <type>text</type>
                                <ptype>href</ptype>
                            </type>
                            <events>
                                <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                            </events>
                        </field>
                        <field>DEPARTMENTID</field>
                    </section>
                    <section id="validProjectKeys_section" hidden="true">
                        <field hidden="true">VALIDPROJECTKEYS</field>
                    </section>
                </child>
                <child>
                    <section title="IA.ENTRIES" isCollapsible="true">
                        <grid allowEditPage="false" clazz="ProjectContractLineEntryGrid">
                            <entity>projectcontractlineentry</entity>
                            <path>ITEMS</path>
                            <title>IA.ENTRIES</title>
                            <hideTitle>true</hideTitle>
                            <noDragDrop>true</noDragDrop>
                            <allowEditPage>true</allowEditPage>
                            <enableSelect>true</enableSelect>
                            <numofrows>1</numofrows>
                            <uniquePropOnRow>RECORDNO</uniquePropOnRow>
                            <selectColumn>
                                <autoRedraw>true</autoRedraw>
                                <autoUpdateSelected>true</autoUpdateSelected>
                            </selectColumn>
                            <gridBulkActions hideInViewMode="true">
                                <button id="removeSelectedRowsFromGrid" path="removeSelectedRowsFromGrid">
                                    <name>IA.REMOVE</name>
                                    <events>
                                        <click>
                                            removeSelectedRowsFromGrid('ITEMS');
                                        </click>
                                    </events>
                                </button>
                            </gridBulkActions>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="WFTYPE"><path>SEARCH_PCL_ENTRY_WFTYPE</path></field></gridHeading>
                                <field required="false">
                                    <path>WFTYPE</path>
                                    <events>
                                        <change>pclentry_onChangeWFType(this);pclentry_prefillPriceEffectiveDate(this);</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PRICE"><path>SEARCH_PCL_ENTRY_PRICE</path></field></gridHeading>
                                <field hasTotal="true" sortable="true">
                                    <path>PRICE</path>
                                    <events>
                                        <change>calcMarkup(this);pclentry_prefillPriceEffectiveDate(this);</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="MEMO"><path>SEARCH_PCL_ENTRY_MEMO</path></field></gridHeading>
                                <field sortable="true">
                                    <path>MEMO</path>
                                    <events>
                                        <change>pclentry_prefillPriceEffectiveDate(this);</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PRICEEFFECTIVEDATE"><path>SEARCH_PCL_ENTRY_PRICEEFFECTIVEDATE</path></field></gridHeading>
                                <field clazz="priceEffectiveDateForm" required="true" sortable="true">
                                    <path>PRICEEFFECTIVEDATE</path>
                                </field>
                            </column>
                            <lineDetails id="pclEntries_itemDetails">
                                <pages>
                                    <page columnCount="1">
                                        <section>
                                            <field sortable="true">
                                                <path>QTY</path>
                                                <events>
                                                    <change>calcPrice(this);pclentry_prefillPriceEffectiveDate(this);</change>
                                                </events>
                                            </field>
                                            <field sortable="true">
                                                <path>EUOM</path>
                                                <events>
                                                    <change>pclentry_prefillPriceEffectiveDate(this);</change>
                                                </events>
                                            </field>
                                            <field sortable="true">
                                                <path>UNITPRICE</path>
                                                <events>
                                                    <change>calcPrice(this);pclentry_prefillPriceEffectiveDate(this);</change>
                                                </events>
                                            </field>
                                            <field sortable="true">
                                                <path>PRICEMARKUPPERCENT</path>
                                                <events>
                                                    <change>calcMarkup(this);pclentry_prefillPriceEffectiveDate(this);</change>
                                                </events>
                                            </field>
                                            <field hasTotal="true" sortable="true">
                                                <path>PRICEMARKUPAMOUNT</path>
                                                <events>
                                                    <change>calcLinePrice(this);pclentry_prefillPriceEffectiveDate(this);</change>
                                                </events>
                                            </field>
                                            <field hasTotal="true" sortable="true">
                                                <path>LINEPRICE</path>
                                                <events>
                                                    <change>pclentry_prefillPriceEffectiveDate(this);</change>
                                                </events>
                                            </field>
                                        </section>
                                        <section title="IA.DIMENSIONS" dimFields="ITEMS" columnCount="2">
                                            <field>
                                                <path>LOCATIONID</path>
                                                <type assoc="T">
                                                    <type>text</type>
                                                    <ptype>href</ptype>
                                                </type>
                                                <events>
                                                    <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                                </events>
                                            </field>
                                            <field sortable="true">
                                                <path>DEPARTMENTID</path>
                                            </field>
                                        </section>
                                    </page>
                                </pages>
                            </lineDetails>
                        </grid>
                    </section>
                </child>
                <child>
                    <section title="IA.LINKED_CHANGE_REQUEST_ENTRIES" isCollapsible="true">
                        <grid allowEditPage="false" title="IA.LINKED_CHANGE_REQUEST_ENTRIES" hideTitle="true" readonly="true">
                            <path>CRENTRIES</path>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="CHANGEREQUESTID"><path>SEARCH_CR_ENTRY_CHANGEREQUESTID</path></field></gridHeading>
                                <field sortable="true" fullname="IA.CHANGE_REQUEST_ID">
                                    <path>CHANGEREQUESTID</path>
                                    <type assoc="T">
                                        <type>text</type>
                                        <ptype>href</ptype>
                                    </type>
                                    <events>
                                        <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PROJECTCHANGEORDERID"><path>SEARCH_CR_ENTRY_PROJECTCHANGEORDERID</path></field></gridHeading>
                                <field sortable="true" fullname="IA.PROJECT_CHANGE_ORDER_ID">
                                    <path>PROJECTCHANGEORDERID</path>
                                    <type assoc="T">
                                        <type>text</type>
                                        <ptype>href</ptype>
                                    </type>
                                    <events>
                                        <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="WFTYPE"><path>SEARCH_CR_ENTRY_WFTYPE</path></field></gridHeading>
                                <field>
                                    <path>WFTYPE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PROJECTID"><path>SEARCH_CR_ENTRY_PROJECTID</path></field></gridHeading>
                                <field sortable="true" fullname="IA.PROJECT_ID">
                                    <path>PROJECTID</path>
                                    <type assoc="T">
                                        <type>text</type>
                                        <ptype>href</ptype>
                                    </type>
                                    <events>
                                        <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="TASKID"><path>SEARCH_CR_ENTRY_TASKID</path></field></gridHeading>
                                <field sortable="true">
                                    <path>TASKID</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="COSTTYPEID"><path>SEARCH_CR_ENTRY_COSTTYPEID</path></field></gridHeading>
                                <field sortable="true">
                                    <path>COSTTYPEID</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="ITEMID"><path>SEARCH_CR_ENTRY_ITEMID</path></field></gridHeading>
                                <field sortable="true">
                                    <path>ITEMID</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="QTY"><path>SEARCH_CR_ENTRY_QTY</path></field></gridHeading>
                                <field sortable="true">
                                    <path>QTY</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="EUOM"><path>SEARCH_CR_ENTRY_EUOM</path></field></gridHeading>
                                <field sortable="true">
                                    <path>EUOM</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="UNITPRICE"><path>SEARCH_CR_ENTRY_UNITPRICE</path></field></gridHeading>
                                <field sortable="true">
                                    <path>UNITPRICE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PRICE"><path>SEARCH_CR_ENTRY_PRICE</path></field></gridHeading>
                                <field sortable="true" hasTotal="true">
                                    <path>PRICE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PRICEMARKUPPERCENT"><path>SEARCH_CR_ENTRY_PRICEMARKUPPERCENT</path></field></gridHeading>
                                <field sortable="true">
                                    <path>PRICEMARKUPPERCENT</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PRICEMARKUPAMOUNT"><path>SEARCH_CR_ENTRY_PRICEMARKUPAMOUNT</path></field></gridHeading>
                                <field sortable="true" hasTotal="true">
                                    <path>PRICEMARKUPAMOUNT</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="LINEPRICE"><path>SEARCH_CR_ENTRY_LINEPRICE</path></field></gridHeading>
                                <field sortable="true" hasTotal="true">
                                    <path>LINEPRICE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PRICEEFFECTIVEDATE"><path>SEARCH_CR_ENTRY_PRICEEFFECTIVEDATE</path></field></gridHeading>
                                <field sortable="true">
                                    <path>PRICEEFFECTIVEDATE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="MEMO"><path>SEARCH_CR_ENTRY_MEMO</path></field></gridHeading>
                                <field sortable="true">
                                    <path>MEMO</path>
                                </field>
                            </column>
                            <lineDetails id="itemDetails">
                                <pages>
                                    <page columnCount="1">
                                        <section title="IA.DIMENSIONS" columnCount="2">
                                            <field>
                                                <path>LOCATIONID</path>
                                            </field>
                                            <field sortable="true">
                                                <path>DEPARTMENTID</path>
                                                <type assoc="T">
                                                    <type>text</type>
                                                    <ptype>href</ptype>
                                                </type>
                                                <events>
                                                    <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                                </events>
                                            </field>
                                        </section>
                                    </page>
                                </pages>
                            </lineDetails>
                        </grid>
                    </section>
                </child>
            </page>
            <page id="task_mapping" title="IA.TASKS">
                <title>IA.TASKS</title>
                <child>
                    <section>
                        <grid noNewRows="true" readonly="true" deleteOnGrid="false" noDragDrop="true" hideLineNo="true" drawBatchSize="5000" enableSelect="true" allowEditPage="false">
                            <path>PCLTASKS</path>
                            <gridContextualActions>
                                <button id="addTasks" path="addTasks">
                                    <name>IA.ADD_TASKS</name>
                                    <events>
                                        <click>taskSelectorHandler.showSelectTasksPage(4820);</click>
                                    </events>
                                </button>
                                <button id="removeTasks" path="removeTasks">
                                    <name>IA.REMOVE_TASKS</name>
                                    <events>
                                        <click>taskSelectorHandler.deleteSelectedEntries();</click>
                                    </events>
                                </button>
                            </gridContextualActions>
                            <gridBulkActions>
                            </gridBulkActions>
                            <selectColumn></selectColumn>
                            <column>
                                <field clazz="taskFloatingLink" sortable="true">
                                    <path>TASKID</path>
                                </field>
                            </column>
                            <column>
                                <field sortable="true">
                                    <path>PCLTASKNAME</path>
                                </field>
                            </column>
                            <column>
                                <field clazz="projectFloatingLink" sortable="true">
                                    <path>PROJECTID</path>
                                </field>
                            </column>
                            <column>
                                <field sortable="true">
                                    <path>PCLPROJECTNAME</path>
                                </field>
                            </column>
                            <column>
                                <field sortable="true">
                                    <path>PCLTASKSTATUS</path>
                                </field>
                            </column>
                            <column>
                                <field sortable="true">
                                    <path>PCLTASKBILLABLE</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </child>
            </page>
            <page id="pcl_billing" title="IA.BILLING_DETAILS">
                <child>
                    <section isCollapsible="true" showCollapsed="false" id="billing_summary_section">
                        <title>IA.PROJECT_CONTRACT_LINE_BILLING_SUMMARY</title>
                            <subsection id="billing_summary_subsection" columnCount="2">
                                <row>
                                    <field>BILLEDPRICE</field>
                                    <field>BALANCETOBILL</field>
                                    <field>BILLEDNETRETAINAGE</field>
                                    <field>PERCENTAGEBILLED</field>
                                    <field>PERCENTAGEBILLEDNETRETAINAGE</field>
                                </row>
                                <row>
                                    <field>RETAINAGEHELD</field>
                                    <field>RETAINAGERELEASED</field>
                                    <field>RETAINAGEBALANCE</field>
                                    <field>PAYMENTSRECEIVED</field>
                                </row>
                        </subsection>
                    </section>
                </child>
                <child>
                    <section id="billing_invoices_section" isCollapsible="true" showCollapsed="false">
                        <title>IA.PROJECT_CONTRACT_LINE_INVOICES</title>
                        <grid title="IA.PROJECT_CONTRACT_LINE_INVOICES" hideTitle="true" readonly="true">
                            <path>INVOICES</path>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="DOCID"><path>SEARCH_INV_DOCID</path></field></gridHeading>
                                <field label="IA.DOCUMENT_ID">
                                    <path>DOCID</path>
                                    <type assoc="T">
                                        <type>text</type>
                                        <ptype>href</ptype>
                                    </type>
                                    <events>
                                        <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="DOCNO"><path>SEARCH_INV_DOCNO</path></field></gridHeading>
                                <field label="IA.AR_INVOICE_ID">
                                    <path>DOCNO</path>
                                    <type assoc="T">
                                        <type>text</type>
                                        <ptype>href</ptype>
                                    </type>
                                    <events>
                                        <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="DOCTYPE"><path>SEARCH_INV_DOCTYPE</path></field></gridHeading>
                                <field label="IA.DOCUMENT_TYPE">
                                    <path>TDNAME</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="DATECREATED"><path>SEARCH_INV_DATECREATED</path></field></gridHeading>
                                <field label="IA.TRANSACTION_DATE">
                                    <path>DATECREATED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="WHENPOSTED"><path>SEARCH_INV_WHENPOSTED</path></field></gridHeading>
                                <field label="IA.GL_POSTING_DATE">
                                    <path>WHENPOSTED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="STATE"><path>SEARCH_INV_STATE</path></field></gridHeading>
                                <field label="IA.DOC_STATE">
                                    <path>STATE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="TOTAL_BILLED"><path>SEARCH_INV_AMOUNT</path></field></gridHeading>
                                <field label="IA.CONTRACT_LINE_INVOICE_AMOUNT" hasTotal="true">
                                    <type type="decimal" ptype="currency"/>
                                    <path>TOTAL_BILLED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="TOTAL_RETAINAGE_HELD"><path>SEARCH_TOTAL_RETAINAGE_HELD</path></field></gridHeading>
                                <field label="IA.RETAINAGE_HELD" hasTotal="true">
                                    <type type="decimal" ptype="currency"/>
                                    <path>TOTAL_RETAINAGE_HELD</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="TOTAL_RETAINAGE_RELEASED"><path>SEARCH_TOTAL_RETAINAGE_RELEASED</path></field></gridHeading>
                                <field label="IA.RETAINAGE_RELEASED" hasTotal="true">
                                    <type type="decimal" ptype="currency"/>
                                    <path>TOTAL_RETAINAGE_RELEASED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="TOTAL_PAID"><path>SEARCH_TOTAL_PAID</path></field></gridHeading>
                                <field label="IA.PAYMENTS_RECEIVED" hasTotal="true">
                                    <type type="decimal" ptype="currency"/>
                                    <path>TOTAL_PAID</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </child>
                <child>
                    <section id="retainage_release_invoices_section" isCollapsible="true" showCollapsed="true">
                        <title>IA.RETAINAGE_RELEASE_INVOICES</title>
                        <grid title="IA.RETAINAGE_RELEASE_INVOICES" hideTitle="true" readonly="true">
                            <path>RETAINAGERELEASE_INVOICES</path>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="DOCNO"><path>SEARCH_RR_INV_DOCNO</path></field></gridHeading>
                                <field label="IA.RETAINAGE_RELEASE_INVOICE_ID">
                                    <path>RECORDID</path>
                                    <type assoc="T">
                                        <type>text</type>
                                        <ptype>href</ptype>
                                    </type>
                                    <events>
                                        <click>projectContractHandler.openDrilldownPage(this.meta);</click>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="DATECREATED"><path>SEARCH_RR_INV_DATECREATED</path></field></gridHeading>
                                <field label="IA.TRANSACTION_DATE">
                                    <path>DATECREATED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="WHENPOSTED"><path>SEARCH_RR_INV_WHENPOSTED</path></field></gridHeading>
                                <field label="IA.GL_POSTING_DATE">
                                    <path>WHENPOSTED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="STATE"><path>SEARCH_RR_INV_STATE</path></field></gridHeading>
                                <field label="IA.DOC_STATE">
                                    <path>STATE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="RETAINAGE_RELEASED"><path>SEARCH_RR_INV_AMOUNT</path></field></gridHeading>
                                <field label="IA.AMOUNT" hasTotal="true">
                                    <type type="decimal" ptype="currency"/>
                                    <path>RETAINAGE_RELEASED</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="TOTAL_PAID"><path>SEARCH_RR_TOTAL_PAID</path></field></gridHeading>
                                <field label="IA.PAYMENTS_RECEIVED" hasTotal="true">
                                    <type type="decimal" ptype="currency"/>
                                    <path>TOTAL_PAID</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </child>
                <child>
                    <section id="payments_section" isCollapsible="true" showCollapsed="false">
                        <title>IA.AR_INVOICE_PAYMENTS_RECEIVED</title>
                        <grid title="IA.AR_INVOICE_PAYMENTS_RECEIVED" hideTitle="true" readonly="true">
                            <path>PAYMENTS</path>
                            <column>
                                <field label="IA.PAYMENT" isHTML="true">
                                    <path>DOCNO</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PAYMENTTYPE"><path>SEARCH_PMT_PAYMENTTYPE</path></field></gridHeading>
                                <field label="IA.PAYMENT_TYPE">
                                    <path>PAYMENTTYPE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="FINANCIALACCOUNT"><path>SEARCH_PMT_FINANCIALACCOUNT</path></field></gridHeading>
                                <field label="IA.BANK_ACCOUNT">
                                    <path>ACCOUNTNOKEY</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="DOCNUMBER"><path>SEARCH_PMT_DOCNUMBER</path></field></gridHeading>
                                <field label="IA.PAYMENT_REFERENCE">
                                    <path>DOCNUMBER</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PAYMENTDATE"><path>SEARCH_PMT_PAYMENTDATE</path></field></gridHeading>
                                <field label="IA.PAYMENT_DATE">
                                    <path>POSTINGDATE</path>
                                </field>
                            </column>
                            <column>
                                <gridHeading className="center"><field noLabel="true" searchPath="PAYMENTAMOUNT"><path>SEARCH_PMT_PAYMENTAMOUNT</path></field></gridHeading>
                                <field label="IA.PAYMENT_AMOUNT"  hasTotal="true">
                                    <type type="decimal" ptype="currency"/>
                                    <path>PAYMENTAMOUNT</path>
                                </field>
                            </column>
                        </grid>
                    </section>
                </child>
            </page>
        </pages>
    </view>
    <helpfile>Editing_ProjectContractLine</helpfile>
</ROOT>
