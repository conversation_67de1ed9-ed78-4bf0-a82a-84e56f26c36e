<?php

/**
 * podocumententrypicker class
 *
 * <AUTHOR>
 * @copyright 2024 Intacct Corporation, All Rights Reserved
 */
class PODocumentEntryPicker extends NPicker {
    public function __construct()
    {
        parent::__construct(
            [
                'fields'            => ['LINE_NO', 'ITEMID', 'ITEMNAME', 'PROJECTID', 'PROJECTNAME', 'TASKID', 'TASKNAME', 'LOCATIONID', 'DEPARTMENTID', 'MEMO', 'TRX_REVISEDVALUE', 'PRIMARYDOCNO', 'PRIMARYDOCLINENO'],
                'pickfield'         => ['DOCID'],
                'entity'            => 'podocumententry',
                'pickentity'        => 'podocumententry',
            ]
        );
    }

    /**
     * @param int $i
     * @param string $refr
     *
     * @return string
     */
    function calcSelectUrl($i, $refr = "")
    {
        $p = &$this->_params;
        $t = &$this->table;

        $text = $p['_selectbutton'];

        if ($refr) {
            $refr = '1';
        }

        //  Properly encode to JS-ready (e.g. UTF-8 chars).
        if(isset($t[$i]['LINE_NO']) && !empty($t[$i]['ITEMID'])){
            $t[$i]['SOURCEDOCLINEID'] = $t[$i]['LINE_NO'] . "--" . $t[$i]['ITEMID'];
            $pick = isl_str_to_js($t[$i]['SOURCEDOCLINEID']);
        }else{
            $pick = isl_str_to_js($t[$i]['RECORDNO']);
        }
        $ret = "<a id=\"select\" href=\"javascript:SetField('" . $pick . "', '', '$refr')\" >" . $text . "</a>";

        return $ret;
    }

    /**
     * @return array
     */
    protected function calcSelects()
    {
        $selects = parent::calcSelects();

        $additionFieldsForCalculation = ['UIPRICE', 'TRX_VALUE', 'TRX_REVISEDPRICE', 'UIQTY', 'REVISEDQTY', 'QTY_CONVERTED', 'PRICE_CONVERTED', 'CONVERSIONTYPE'];
        foreach ($additionFieldsForCalculation as $fields) {
            $selects[] = $fields;
        }

        return $selects;
    }

    /**
     * Method to build a table
     */
    function BuildTable()
    {
        NPicker::BuildTable();
        foreach ($this->table as $key => $row) {
            if (isset($this->table[$key]['LINE_NO'])) {
                $revisedPrice = $this->table[$key]['TRX_REVISEDPRICE'] ?? 0;
                $revisedQty = $this->table[$key]['REVISEDQTY'] ?? 0;
                $priceConverted = $this->table[$key]['PRICE_CONVERTED'] ?? 0;
                $qtyConverted = $this->table[$key]['QTY_CONVERTED'] ?? 0;
                $conversionType = $this->table[$key]['CONVERSIONTYPE'] ?? '';
                $uiQty = $this->table[$key]['UIQTY'] ?? 0;
                $uiPrice = $this->table[$key]['UIPRICE'] ?? 0;

                if ($conversionType === 'Quantity') {
                    $uiQty = ($revisedQty > 0) ? $revisedQty : $uiQty;
                    $uiQty = ($qtyConverted > 0) ? ibcsub($uiQty, $qtyConverted) : $uiQty;
                    $uiPrice = (!empty($revisedPrice)) ? $revisedPrice : $uiPrice;
                    $extendedPrice = ibcmul($uiQty, $uiPrice);
                } else {
                    $uiPrice = ($revisedPrice > 0) ? $revisedPrice : $uiPrice;
                    $uiPrice = ($priceConverted > 0) ? ibcsub($uiPrice, $priceConverted) : $uiPrice;
                    $uiQty = (!empty($revisedQty)) ? $revisedQty : $uiQty;
                    $extendedPrice = ibcmul($uiQty, $uiPrice);
                }
                $this->table[$key]['TRX_REVISEDVALUE'] = glFormatCurrency($extendedPrice);
                $this->table[$key]['LINE_NO'] = $this->table[$key]['LINE_NO'] + 1;
            }
        }
    }
}