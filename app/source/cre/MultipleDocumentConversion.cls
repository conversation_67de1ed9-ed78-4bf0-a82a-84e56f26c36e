<?php
/**
 * Multiple document conversion related functions.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Sage Intacct Corporation All, Rights Reserved
 *
 * Sage Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Sage Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Sage Intacct Corporation.
 */

trait MultipleDocumentConversion
{

    /**
     * @var array
     */
    protected $documentCache = [];

    /**
     * check and initialize multi doc conversion
     *
     * @param array $values
     *
     * @throws Exception
     * @return bool
     */
    public function initMultiDocumentConversion(&$values): bool
    {
        // change orders cannot be converted
        if (($values['_DOCPAR']['ENABLEDOCCHANGE'] ?? 'No change') === 'Change Order') {
            return true;
        }

        $ok = true;
        //if multidoc conversion is enabled and this document is trying for it, lets set flags
        if (CRESetupManager::isMultiDocumentConversionEnabled() && $this->isMultiDocumentConversion($values)) {
            //set the copymode to update
            Request::$r->copymode = Request::$r->_copymode = "Update";
            $values['CREATEDFROM'] = ConversionManager::getmultipleSourceDocsText();
        }

        if (IsMCPEnabled($this->mod)) {
            $ok = $ok && $this->validateCurrency($values);
        }

        return $ok;
    }

    /**
     * Validate base and transaction currencies from header and source document selected in entries
     * @param array $values
     * @return true
     * @throws Exception
     */
    protected function validateCurrency(array $values)
    {
        // Extract the base and transaction currencies from the $values array
        $baseCurrency = $values['BASECURR'] ?? null;
        $transactionCurrency = $values['CURRENCY'] ?? null;
        $ok = true;
        if (!empty($this->documentCache)) {
            foreach ($this->documentCache as $sourceDocId => $sourceDoc) {
                // Check if the currency matches
                if ($sourceDoc['BASECURR'] !== $baseCurrency || $sourceDoc['CURRENCY'] !== $transactionCurrency) {
                    Globals::$g->gErr->addIAError(
                        number: 'CRE-5001',
                        source: __FILE__ . ':' . __LINE__
                    );
                    $ok = false;
                    break;
                }
            }
        }

        return $ok;
    }

    /**
     * Update source doc key
     * @param $entry
     * @return void
     */
    public function updateSourceDocKeyForMultiDoc(&$entry)
    {
        if (!empty($entry['SOURCEDOCID'])) {
            if (empty($entry['SOURCE_DOCID'])) {
                $entry['SOURCE_DOCID'] = $entry['SOURCEDOCID'];
            }
            $sourceDoc = $this->getSourceDocKey($entry);
            $entry['SOURCE_DOCKEY'] = $sourceDoc['RECORDNO'];
        }
    }

    /**
     * check and return if this is a multidocument conversion
     * we need to go through all the $values['ENTRIES'] and check the different sourcedocid for each
     * if there are multiple sourcedocid, then this is a multidocument conversion
     * @param $values
     *
     * @return bool
     */
    protected function isMultiDocumentConversion(&$values): bool
    {
        $sourceDocIds = [];
        foreach ($values['ENTRIES'] as &$entry) {
            if (isset($entry['SOURCEDOCID'])) {
                $sourceDocIds[] = $entry['SOURCEDOCID'];
                $this->updateSourceDocKeyForMultiDoc($entry);
            }
        }
        $sourceDocIds = array_unique($sourceDocIds);
        if (empty($values['CREATEDFROM']) && count($sourceDocIds) == 1) {
            $values['CREATEDFROM'] = $sourceDocIds[0];
        }
        return count($sourceDocIds) > 1;
    }

    /**
     * METHOD IS TO FETCH THE DOCENTRY DETAILS FROM GIVEN DOCENTRY RECORD# AND RETURN IN JSON OUTPUT.
     * @param array $params
     *
     * @return string
     */
    public function FetchSourceDocLineDetailForConversion($params)
    {
        $data = $this->getSourceDocLineDetailsForConv($params);
        $json = htmlentities(json_encode($data));
        $xml = "<SourceDocLineDetails>{$json}</SourceDocLineDetails>";
        return $xml;
    }

    /**
     * METHOD WILL FETCH THE DATA FROM PODOCUMENTENTRY AND ASSIGN IT TO THE ARRAY
     *
     * @param array $params
     *
     * @return array
     */
    protected function getSourceDocLineDetailsForConv(array $params)
    {
        $select = ['LINE_NO', 'PRICE as TRX_PRICE', 'REVISEDUNITQTY', 'TRX_REVISEDPRICE', 'REVISEDQTY', 'REVISEDUNITPRICE',
            'TRX_REVISEDVALUE', 'REVISEDUNITVALUE', 'DRAFTCHANGEUNITQTY', 'ITEMID', 'UNIT',
            'WAREHOUSE.LOCATION_NO', 'WAREHOUSE.LOCATION_NO as WAREHOUSEID', 'DEPARTMENTID',
            'LOCATIONID', 'REVISEDUNITPRICE as UIPRICE', 'MEMO', 'ITEMDESC',
            'CONVERSIONTYPE', 'RETAINAGEPERCENTAGE', 'TRX_AMOUNTRETAINED', 'BILLABLE', 'DRAFTCHANGEPRICE',
            'UIQTY', 'TRX_VALUE', 'UIVALUE','DRAFTCHANGEBASEPRICE', 'DOCHDRKEY as SOURCE_DOCHDRKEY', 'QTY_CONVERTED', 'PRICE_CONVERTED','ITEM.ITEMTYPE as ITEMTYPE', 'PRIMARYDOCNO'];

        $renameFields = [
            'DEPARTMENTID' => 'DEPARTMENT',
            'LOCATIONID' => 'LOCATION',
        ];

        $lineRecordNo = $params['sourceDocLineId'];
        $docPar = $params['dt'];
        $entryMGR = "podocumententry";
        $mgr = Globals::$g->gManagerFactory->getManager($entryMGR, true, array('DOCTYPE' => $docPar));
        $dimensionFields = $mgr->GetDimensionFields();
        $customFields = $mgr->GetCustomFields();
        $platformFields = $mgr->getPlatformRelationFields();

        $select = array_merge(
            $select,
            array_column($dimensionFields ?? [], 'path') ?? [],
            array_keys($customFields),
            array_column($platformFields ?? [], 'path') ?? []
        );

        $filter = array(
            'selects' => $select,
            'filters' => [[['RECORDNO', '=', $lineRecordNo]]]
        );

        $result = $mgr->GetList($filter);

        $data = $result[0] ?? [];

        foreach ($renameFields as $oldFieldName => $newFieldName) {
            if (array_key_exists($oldFieldName,$data)) {
                $data[$newFieldName] = $data[$oldFieldName];
                unset($data[$oldFieldName]);
            }
        }

        if ($data) {
            $docMgr = Globals::$g->gManagerFactory->getManager('document');
            if ($docMgr->isChangeOrderEnabled()) {
                if (!empty($data['TRX_REVISEDPRICE'])) {
                    $data['TRX_PRICE'] =  $data['UIPRICE'] = $data['UIVALUE'] = $data['TRX_REVISEDPRICE'] ?? "";
                }
                if (!empty($data['REVISEDQTY'])) {
                    $data['QUANTITY'] = $data['UIQTY'] = $data['REVISEDQTY'] ?? "";
                }
            }

            if ($data['CONVERSIONTYPE'] == 'Price') {
                if (!empty($data['PRICE_CONVERTED'])) {
                    $data['TRX_PRICE'] = $data['PRICE'] = $data['UIPRICE'] =
                        ibcsub($data['TRX_PRICE'], $data['PRICE_CONVERTED']);
                    $data['UIVALUE'] = ibcsub($data['UIVALUE'], $data['PRICE_CONVERTED']);
                }
                $data['TRX_VALUE'] = $data['UIPRICE'] = ibcmul($data['TRX_PRICE'], $data['UIQTY']);
            }

            if ($data['CONVERSIONTYPE'] == 'Quantity') {
                if (!empty($data['QTY_CONVERTED'])) {
                    $data['UIQTY'] = ibcsub($data['UIQTY'], $data['QTY_CONVERTED']);
                }
                $data['UIPRICE'] = $data['TRX_PRICE'];
                $data['UIVALUE'] = $data['TRX_VALUE'] = ibcmul($data['TRX_PRICE'], $data['UIQTY']);
            }
            if (!IsMCPEnabled($this->mod)) {
                unset($data['TRX_PRICE']);
            }
            unset($data['REVISEDUNITQTY']);
            unset($data['TRX_REVISEDPRICE']);
            unset($data['TRX_REVISEDVALUE']);

            if (GetPreferenceForProperty(Globals::$g->kPOid, 'PREFILL_CONVERSION_ZERO') == 'T' ) {
                $data['TRX_VALUE'] = "0";
                if ($data['CONVERSIONTYPE'] == 'Price') {
                    $data['TRX_PRICE'] = "0";
                } else {
                    $data['UIQTY'] = "0";
                }
            }

            $data['TRX_AMOUNTRETAINED'] = $data['AMOUNTRETAINED'] = ibcdiv(ibcmul($data['TRX_VALUE'], $data['RETAINAGEPERCENTAGE'], 2), '100', 2, true);
        }

        return $data;
    }

    /**
     * function to get the source document key from dochdr table based on the given source doc id in the sourcedoc array
     * @param array $sourceDoc
     *
     * @return array
     */
    protected function getSourceDocKey(array $sourceDoc) : array
    {
        $select = ['RECORDNO', 'DOCID', 'BASECURR', 'CURRENCY'];
        $sourceDocId = $sourceDoc['SOURCEDOCID'];

        $filter = array(
            'selects' => $select,
            'filters' => [[['DOCID', '=', $sourceDocId]]]
        );
        $result = Globals::$g->gManagerFactory->getManager('podocument', true)->GetList($filter);
        $select = [];
        if ($result) {
            if (empty($this->documentCache[$sourceDocId])) {
                $this->documentCache[$sourceDocId] = $result[0];
            }
            $select = $result[0];
        }

        return $select;
    }

    /**
     * Unset multi-doc fields from entry
     * @param array $obj
     * @return void
     * @throws Exception
     */
    public function unsetMultiDocDetails(array &$obj)
    {
        foreach ($obj['ENTRIES'] ?? [] as $key => $entry) {
            if (empty($entry['SOURCEDOCID']) && !empty($obj['ENTRIES'][$key]['SOURCEDOCLINEID'])) {
                $obj['ENTRIES'][$key]['SOURCEDOCLINEID'] = null;
            }
        }
    }
}
