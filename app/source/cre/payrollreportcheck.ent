<?php

/**
 * Entity for the Payroll Report Check object
 *
 * <AUTHOR> <Naresh.Kanna<PERSON>@sage.com>
 * @copyright 2022 Sage Intacct Inc.
 *
 */
global $gRecordNoFormat, $gDateFormat, $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gCreatedByFieldInfo, $gModifiedByFieldInfo, $gEmployeeIDFormat, $gDecimalFormat, $gBooleanType, $gDateType, $gCurrencyFormatTenDec,$gPositiveNumberFormat;
$kSchemas['payrollreportcheck'] = [
    'children' => [
        'employee' => [
            'fkey' => 'employeedimkey',
            'invfkey' => 'record#',
            'table' => 'employee',
            'join' => 'outer',
        ],
        'location' => [
            'fkey' => 'payrollchecklocationkey',
            'invfkey' => 'record#',
            'table' => 'location',
            'join' => 'outer',
        ],
        'payrollreportchecksum<PERSON>y' => [
            'fkey' => 'record#',
            'invfkey' => 'checkkey',
            'table' => 'v_payrollreportchecksummary',
            'join' => 'outer',
        ],
        'payrollreporttimecardreport' => [
            'fkey' => 'record#',
            'invfkey' => 'checkkey',
            'table' => 'v_payrollreporttimecardreport',
            'join' => 'outer',
        ],
    ],
    'nexus' => [
        'EMPLOYEE' => [
            'object' => 'employee',
            'relation' => MANY2ONE,
            'field' => 'EMPLOYEEID',
        ],
        'PAYROLLREPORTCHECKSUMMARY' => [
            'object' => 'payrollreportchecksummary',
            'relation' => ONE2MANY,
            'field' => 'CHECKID',
        ],
        'PAYROLLREPORTTIMECARDREPORT' => [
            'object' => 'payrollreporttimecardreport',
            'relation' => ONE2MANY,
            'field' => 'CHECKID',
        ],
    ],
    'schema' => [
        'RECORDNO' => 'record#',
        'CHECKID' => 'checkid',
        'CHECKTYPE' => 'checktype',
        'EMPLOYEEID'   => 'employee.employeeid',
        'EMPLOYEEKEY'  => 'employeedimkey',
        'LEGALENTITYID' => 'legalentityid',
        'EXTERNALENTITYID' => 'externalentityid',
        'CHECKSTATUS' => 'checkstatus',
        'WASPOSTED' => 'wasposted',
        'TIMECARDSWEREEXPORTED' => 'timecardswereexported',
        'WASPAID' => 'waspaid',
        'WASCHECKSTUBGENERATED' => 'wascheckstubgenerated',
        'WASADVICENOTICEGENERATED' => 'wasadvicenoticegenerated',
        'CHECKDATE' => 'checkdate',
        'REVISIONNUMBER' => 'revisionnumber',
        'PAYGROUPID' => 'paygroupid',
        'PAYPERIODNUMBER' => 'payperiodnumber',
        'PAYROLLYEAR' => 'payrollyear',
        'PERIODBEGINDATE' => 'periodbegindate',
        'PERIODENDDATE' => 'periodenddate',
        'ERRORMESSAGE' => 'errormessage',
        'GROSSPAY' => 'grosspay',
        'NETPAY' => 'netpay',
        'EMPLOYEEISDISABLED' => 'employeeisdisabled',
        'EMPLOYEEISFULLTIME' => 'employeeisfulltime',
        'EMPLOYEEISSEASONAL' => 'employeeisseasonal',
        'EMPLOYEEISSTATUTORY' => 'employeeisstatutory',
        'EMPLOYEEACCEPTSELECTRONICW2' => 'employeeacceptselectronicw2',
        'EMPLOYEEETHNICITY' => 'employeeethnicity',
        'EMPLOYEEEEOJOBCATEGORY' => 'employeeeeojobcategory',
        'EMPLOYEEEEOETHNICITY' => 'employeeeeoethnicity',
        'EMPLOYEECAEEOETHNICITY' => 'employeecaeeoethnicity',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'SOURCESYSTEM' => 'sourcesystem',
        'PAYROLLCHECKLOCATIONKEY' => 'payrollchecklocationkey',
        'LOCATIONID' => 'location.location_no',
        'LOCATIONNAME' => 'location.name',
    ],

    'object' => [
        'RECORDNO',
        'CHECKID',
        'CHECKTYPE',
        'EMPLOYEEID',
        'EMPLOYEEKEY',
        'LEGALENTITYID',
        'EXTERNALENTITYID',
        'CHECKSTATUS',
        'WASPOSTED',
        'TIMECARDSWEREEXPORTED',
        'WASPAID',
        'WASCHECKSTUBGENERATED',
        'WASADVICENOTICEGENERATED',
        'CHECKDATE',
        'REVISIONNUMBER',
        'PAYGROUPID',
        'PAYPERIODNUMBER',
        'PAYROLLYEAR',
        'PERIODBEGINDATE',
        'PERIODENDDATE',
        'ERRORMESSAGE',
        'GROSSPAY',
        'NETPAY',
        'EMPLOYEEISDISABLED',
        'EMPLOYEEISFULLTIME',
        'EMPLOYEEISSEASONAL',
        'EMPLOYEEISSTATUTORY',
        'EMPLOYEEACCEPTSELECTRONICW2',
        'EMPLOYEEETHNICITY',
        'EMPLOYEEEEOJOBCATEGORY',
        'EMPLOYEEEEOETHNICITY',
        'EMPLOYEECAEEOETHNICITY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'SOURCESYSTEM',
        'PAYROLLCHECKLOCATIONKEY',
        'LOCATIONID',
        'LOCATIONNAME',
    ],
    'publish' => [
        'RECORDNO',
        'CHECKID',
        'CHECKTYPE',
        'EMPLOYEEID',
        'EMPLOYEEKEY',
        'LEGALENTITYID',
        'EXTERNALENTITYID',
        'CHECKSTATUS',
        'WASPOSTED',
        'TIMECARDSWEREEXPORTED',
        'WASPAID',
        'WASCHECKSTUBGENERATED',
        'WASADVICENOTICEGENERATED',
        'CHECKDATE',
        'REVISIONNUMBER',
        'PAYGROUPID',
        'PAYPERIODNUMBER',
        'PAYROLLYEAR',
        'PERIODBEGINDATE',
        'PERIODENDDATE',
        'ERRORMESSAGE',
        'GROSSPAY',
        'NETPAY',
        'EMPLOYEEISDISABLED',
        'EMPLOYEEISFULLTIME',
        'EMPLOYEEISSEASONAL',
        'EMPLOYEEISSTATUTORY',
        'EMPLOYEEACCEPTSELECTRONICW2',
        'EMPLOYEEETHNICITY',
        'EMPLOYEEEEOJOBCATEGORY',
        'EMPLOYEEEEOETHNICITY',
        'EMPLOYEECAEEOETHNICITY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'SOURCESYSTEM',
        'PAYROLLCHECKLOCATIONKEY',
        'LOCATIONID',
        'LOCATIONNAME',
    ],
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NO',
            'desc' => 'IA.RECORD_NO',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ],
            'disableReportSelection' => true,
            'hidden' => true,
            'readonly' => true,
            'noapiadd' => true,
            'id' => 1,
        ],
         [
             'path' => 'CHECKID',
             'fullname' => 'IA.CHECK_ID',
             'desc' => 'IA.CHECK_ID',
             'type' => [
                 'type' => 'text',
                 'ptype' =>  'text',
                 'size' => 100,
                 'maxlength' => 100,
             ],
             'required' => true,
             'id' => 2,
         ],
        [
            'path' => 'CHECKTYPE',
            'fullname' => 'IA.CHECK_TYPE',
            'desc' => 'IA.CHECK_TYPE',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 3,
        ],
        [
            'path' => 'LEGALENTITYID',
            'fullname' => 'IA.LEGAL_ENTITY_ID',
            'desc' => 'IA.LEGAL_ENTITY_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 4,
        ],
        [
            'path' => 'EXTERNALENTITYID',
            'fullname' => 'IA.EXTERNAL_ENTITY_ID',
            'desc' => 'IA.EXTERNAL_ENTITY_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 5,
        ],
        [
            'path' => 'CHECKSTATUS',
            'fullname' => 'IA.CHECK_STATUS',
            'desc' => 'IA.CHECK_STATUS',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'required' => true,
            'id' => 6,
        ],
        [
            'path' => 'WASPOSTED',
            'fullname' => 'IA.WAS_POSTED',
            'desc' => 'IA.WAS_POSTED',
            'type' => $gBooleanType,
            'default' => 'false',
            'required' => true,
            'id' => 7,
        ],
        [
            'path' => 'TIMECARDSWEREEXPORTED',
            'fullname' => 'IA.TIMECARDS_WERE_EXPORTED',
            'desc' => 'IA.TIMECARDS_WERE_EXPORTED',
            'type' => $gBooleanType,
            'default' => 'false',
            'required' => true,
            'id' => 8,
        ],
        [
            'path' => 'WASPAID',
            'fullname' => 'IA.WAS_PAID',
            'desc' => 'IA.WAS_PAID',
            'type' => $gBooleanType,
            'default' => 'false',
            'required' => true,
            'id' => 9,
        ],
        [
            'path' => 'WASCHECKSTUBGENERATED',
            'fullname' => 'IA.WAS_CHECK_STUB_GENERATED',
            'desc' => 'IA.WAS_CHECK_STUB_GENERATED',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 10,
        ],
        [
            'path' => 'WASADVICENOTICEGENERATED',
            'fullname' => 'IA.WAS_ADVICE_NOTICE_GENERATED',
            'desc' => 'IA.WAS_ADVICE_NOTICE_GENERATED',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 11,
        ],
        [
            'path' => 'CHECKDATE',
            'fullname' => 'IA.CHECK_DATE',
            'desc' => 'IA.CHECK_DATE',
            'type' => $gDateType,
            'id' => 12,
        ],
        [
            'path' => 'REVISIONNUMBER',
            'fullname' => 'IA.REVISION_NUMBER',
            'desc' => 'IA.REVISION_NUMBER',
            'type' => [
                'type' => 'numeric',
                'ptype' => 'numeric',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gPositiveNumberFormat
            ],
            'required' => true,
            'id' => 13,
        ],
        [
            'path' => 'PAYGROUPID',
            'fullname' => 'IA.PAY_GROUP_ID',
            'desc' => 'IA.PAY_GROUP_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 14,
        ],
        [
            'path' => 'PAYPERIODNUMBER',
            'fullname' => 'IA.PAY_PERIOD_NUMBER',
            'desc' => 'IA.PAY_PERIOD_NUMBER',
            'type' => [
                'type' => 'numeric',
                'ptype' => 'numeric',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gPositiveNumberFormat
            ],
            'required' => true,
            'id' => 15,
        ],
        [
            'path' => 'PAYROLLYEAR',
            'fullname' => 'IA.PAYROLL_YEAR',
            'desc' => 'IA.PAYROLL_YEAR',
            'type' => [
                'type' => 'numeric',
                'ptype' => 'numeric',
                'size' => 10,
                'maxlength' => 10,
            ],
            'required' => true,
            'id' => 16,
        ],
        [
            'path' => 'PERIODBEGINDATE',
            'fullname' => 'IA.PERIOD_BEGIN_DATE',
            'desc' => 'IA.PERIOD_BEGIN_DATE',
            'type' => $gDateType,
            'id' => 17,
        ],
        [
            'path' => 'PERIODENDDATE',
            'fullname' => 'IA.PERIOD_END_DATE',
            'desc' => 'IA.PERIOD_END_DATE',
            'type' => $gDateType,
            'id' => 18,
        ],
        [
            'path' => 'ERRORMESSAGE',
            'fullname' => 'IA.ERROR_MESSAGE',
            'desc' => 'IA.ERROR_MESSAGE',
            'type' => [
                'ptype' => 'textarea',
                'type' => 'textarea',
                'maxlength' => 2000,
            ],
            'id' => 19,
        ],
        [
            'path' => 'GROSSPAY',
            'fullname' => 'IA.GROSS_PAY',
            'desc' => 'IA.GROSS_PAY',
            'type' => [
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 30,
                'format' => $gCurrencyFormatTenDec,
                'size' => 15,
            ],
            'id' => 20,
        ],
        [
            'path' => 'NETPAY',
            'fullname' => 'IA.NET_PAY',
            'desc' => 'IA.NET_PAY',
            'type' => [
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 30,
                'format' => $gCurrencyFormatTenDec,
                'size' => 15,
            ],
            'id' => 21,
        ],
        [
            'path' => 'EMPLOYEEISDISABLED',
            'fullname' => 'IA.EMPLOYEE_IS_DISABLED',
            'desc' => 'IA.EMPLOYEE_IS_DISABLED',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 22,
        ],
        [
            'path' => 'EMPLOYEEISFULLTIME',
            'fullname' => 'IA.EMPLOYEE_IS_FULLTIME',
            'desc' => 'IA.EMPLOYEE_IS_FULLTIME',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 23,
        ],
        [
            'path' => 'EMPLOYEEISSEASONAL',
            'fullname' => 'IA.EMPLOYEE_IS_SEASONAL',
            'desc' => 'IA.EMPLOYEE_IS_SEASONAL',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 24,
        ],
        [
            'path' => 'EMPLOYEEISSTATUTORY',
            'fullname' => 'IA.EMPLOYEE_IS_STATUTORY',
            'desc' => 'IA.EMPLOYEE_IS_STATUTORY',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 25,
        ],
        [
            'path' => 'EMPLOYEEACCEPTSELECTRONICW2',
            'fullname' => 'IA.EMPLOYEE_ACCEPTS_ELECTRONIC_W2',
            'desc' => 'IA.EMPLOYEE_ACCEPTS_ELECTRONIC_W2',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 26,
        ],
        [
            'path' => 'EMPLOYEEETHNICITY',
            'fullname' => 'IA.EMPLOYEE_ETHNICITY',
            'desc' => 'IA.EMPLOYEE_ETHNICITY',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 27,
        ],
        [
            'path' => 'EMPLOYEEEEOJOBCATEGORY',
            'fullname' => 'IA.EMPLOYEE_EEO_JOBCATEGORY',
            'desc' => 'IA.EMPLOYEE_EEO_JOBCATEGORY',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 28,
        ],
        [
            'path' => 'EMPLOYEEEEOETHNICITY',
            'fullname' => 'IA.EMPLOYEE_EEO_ETHNICITY',
            'desc' => 'IA.EMPLOYEE_EEO_ETHNICITY',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 29,
        ],
        [
            'path' => 'EMPLOYEECAEEOETHNICITY',
            'fullname' => 'IA.EMPLOYEE_CAEEO_ETHNICITY',
            'desc' => 'IA.EMPLOYEE_CAEEO_ETHNICITY',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 30,
        ],
        [
            'path' => 'SOURCESYSTEM',
            'fullname' => 'IA.SOURCE_SYSTEM',
            'desc' => 'IA.SOURCE_SYSTEM',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'size' => 100,
                'maxlength' => 100,
            ],
            'id' => 31,
        ],
        [
            'path' => 'EMPLOYEEID',
            'fullname' => 'IA.EMPLOYEE_ID',
            'desc' => 'IA.EMPLOYEE_ID',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => $gEmployeeIDFormat,
            ],
            'required' => true,
            'id' => 32,
        ],
        [
            'path' => 'EMPLOYEEKEY',
            'fullname' => 'IA.EMPLOYEE_KEY',
            'desc' => 'IA.EMPLOYEE_KEY',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ],
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 33,
        ],
       [
           'path' => 'PAYROLLCHECKLOCATIONKEY',
           'fullname' => 'IA.LOCATION_KEY',
           'desc' => 'IA.LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ),
           'noapiadd' => true,
           'noapiset' => true,
           'id' => 34,
        ],
        [
            'path' => 'LOCATIONID',
            'fullname' => 'IA.LOCATION_ID',
            'desc' => 'IA.LOCATION_ID',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
            ),
            'id' => 35,
        ],
        [
            'path' => 'LOCATIONNAME',
            'fullname' => 'IA.LOCATION_NAME',
            'desc' => 'IA.LOCATION_NAME',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 40
            ),
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 36
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED'
    ),
    'url' => [
        'no_short_url' => true,    // Don't allow short url
    ],
    'table' => 'payrollreportcheck',
    'printas' => 'IA.REPORT_CHECK',
    'pluralprintas' => 'IA.REPORT_CHECKS',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'module' => 'pa',
    'description' => 'IA.REPORT_CHECK',
    'allowDDS' => true,
    'api' => [
        'PERMISSION_MODULES' => ['pay'],
    ],
    'auditcolumns' => true,
    'customerp' => array(
        'SLTypes' => array(
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKWORKFLOW
        ),
        'SLEvents' => array(
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK
        ),
        'AllowCF' => true
    )
];