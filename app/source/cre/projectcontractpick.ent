<?php

/**
 * pick ent for project contract pick
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 *
 */

$kSchemas['projectcontractpick'] = [
    'children' => [
        'projectcontract' => ['fkey' => 'record#', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'projectcontract']
    ],
    'schema' => [
        'PICKID' => 'projectcontractpick',
        'RECORDNO' => 'record#',
        'CONTRACTDATE' => 'contractdate',
        'TOTALRETAINAGEHELD' => 'projectcontract.totalretainageheld',
        'ORIGINALPRICE' => 'projectcontract.originalprice',
    ],
    'object' => [
        'PICKID',
        'RECORDNO',
        'CONTRACTDATE',
        'TOTALRETAINAGEHELD',
        'ORIGINALPRICE'
    ],
    'fieldinfo' => [
        [
            'fullname' => 'IA.PROJECT_CONTRACT',
            'desc' => 'IA.PROJECTCONTRACTID_AND_DESCRIPTION',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200,
            ],
            'required' => false,
            'path' => 'PICKID',
            'id' => 1
        ],
        [
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => [
                'type' => 'integer',
                'ptype' => 'sequence',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ],
            'id' => 2
        ],
        [
            'path' => 'CONTRACTDATE',
            'fullname' => 'IA.CONTRACT_DATE',
            'type' => array(
                'ptype' => 'date',
                'type' => 'date',
            ),
            'id' => 3,
        ],
        [
            'path' => 'TOTALRETAINAGEHELD',
            'fullname' => 'IA.TOTAL_RETAINAGE_HELD',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ],
            'id' => 4,
        ],
        [
            'path' => 'ORIGINALPRICE',
            'fullname' => 'IA.ORIGINAL_PRICE',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ],
            'id' => 5,
        ],
    ],
    'table' => 'v_projectcontractpick',
    'vid' => 'PICKID',
    'module' => 'pa',
    'printas' => 'IA.PROJECT_CONTRACT',
    'pluralprintas' => 'IA.PROJECT_CONTRACTS',
    'renameable' => true,
];
