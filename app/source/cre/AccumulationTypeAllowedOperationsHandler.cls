<?php
/**
 * AccumulationTypeAllowedOperationsHandler
 *
 * <AUTHOR>
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 *
 */

class AccumulationTypeAllowedOperationsHandler extends AllowedOperationsHandler
{
    /**
     * Override of parent method, necessary for non-megalized entity.
     * @param array $record
     * @return bool
     */
    protected function isValidOwner(array $record) : bool
    {
        return true;
    }
}