<?php

/**
 * Manager for the Compliance Record Detail object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2023 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class ComplianceRecordDetailManager extends EntityManager
{
    /**
     * add a record to the database
     *
     * @param array &$values
     *
     * @return bool
     */
    public function regularAdd(&$values)
    {
        $this->translateValues($values);
        
        return parent::regularAdd($values);
    }

    /**
     * update the record in the database
     *
     * @param array &$values
     *
     * @return bool
     */
    public function regularSet(&$values)
    {
        $this->translateValues($values);
        
        return parent::regularSet($values);
    }

    /** Prepare values for add and update. 
     * @param array $values
     * @return void
     */
    private function translateValues(&$values)
    {
        // Set APPAYMENTITEMKEY or APBILLITEMKEY based on PRRECORDTYPE.
        if (is_array($values) && isset($values['PRENTRYKEY'])) {
            if (isset($values['PRRECORDTYPE']) && $values['PRRECORDTYPE'] == 'pp') {
                $values['APPAYMENTITEMKEY'] = $values['PRENTRYKEY'];
            } else if (isset($values['PRRECORDTYPE']) && $values['PRRECORDTYPE'] == 'pi') {
                $values['APBILLITEMKEY'] = $values['PRENTRYKEY'];
            }
        }
    }
}