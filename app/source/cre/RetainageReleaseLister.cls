<?php

/**
 * RetainageRelease entity Lister - abstraction of ap/ar objects
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Sage Intacct Corporation, All Rights Reserved
 */

abstract class RetainageReleaseLister extends NLister
{
    /**
     * @var array $fields
     */
    protected $fields = [];
    /**
     * @var string $objectType
     */
    protected $objectType;

    /**
     * RetainageReleaseLister constructor.
     */
    function __construct()
    {
        $this->fields = [
            'RECORDNO',
            'RELEASEDATE',
            'DESCRIPTION',
            'STATE',
            'WHENCREATED',
            'CREATEDUSER',
            'WHENMODIFIED',
            'MODIFIEDUSER',
        ];

        $helpfile = 'Viewing_and_Managing_Projects';

        parent::__construct(
            [
                'entity'            => "{$this->objectType}retainagerelease",
                'helpfile'          => $helpfile,
                'fields'            => $this->fields,
                'entitynostatus'    => true,
                'enablemultidelete' => true,
                'sortcolumn'        => 'RECORDNO:d',
                'nonencodedfields'  => [],
            ]
        );
    }

    /**
     * @param int         $i
     * @param string|null $owner
     * @param string|null $ownerloc
     * @param string|null $ownedObj
     *
     * @return array|string
     */
    function calcDeleteUrl($i, $owner = null, $ownerloc = null, $ownedObj = null)
    {
        if ( $this->GetObjectOwnership($i) != 0 ) {
            $val['url'] = 'Not owned';
            return $val;
        }
        if ( $this->values[$i]['STATE'] === 'D' ) {
            return parent::calcDeleteUrl($i);
        }
        return [];
    }

    /**
     * in case the record state is anything other than draft, we set it as non-editable
     * @param int $i
     * @return string
     */
    function calcEditUrl($i)
    {
        $this->noedit = ( $this->values[$i]['STATE'] !== 'D' );

        if ( $this->GetObjectOwnership($i) != 0 ) {
            $this->noedit = true;
        }

        return parent::calcEditUrl($i);
    }

    /**
     * @param array $querySpec
     *
     * @return bool
     */
    protected function needTopLevelRecords($querySpec)
    {
        return false;
    }
}