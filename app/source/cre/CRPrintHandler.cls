<?php
/**
 *    This is the handler to print Changer Request
 *
 *    <AUTHOR> <<EMAIL> >
 *    @copyright 2021 Intacct Corporation, All Rights Reserved
 */

class CRPrintHandler extends EmailPrintHandler
{

    /** @var bool $overridenFrom */
    var $overridenFrom;

    /**
     * @param array $params the parameters supplied by the caller
     */
    public function __construct($params)
    {
        parent::__construct($params);
        $this->overridenFrom = $params['overridenFrom'];
        $this->setMenuName(I18N::getSingleToken('IA.CHANGE_REQUEST'));
    }

    /**
     * Return the document type
     *
     * @param int $key  the record key
     *
     * @return string  the document type
     *
     */
    protected function getDocumentType($key)
    {
        return $this->custentityCache[$key];
    }

    /**
     * Return the XSL template for this record and document type
     *
     * @param int     $key        the record key
     * @param string  $docType    the document type
     * @param string  $xslformat  xsl format override
     *
     * @return string   the xsl document template
     *
     */
    protected function getXSLTemplate($key, $docType, $xslformat)
    {
        /** @var ChangeRequestEditor $docEditor*/
        $docEditor = $this->getEditor($docType);
        $xsl = $docEditor->GetXSLTemplate($key, $docType, $xslformat, []);
        return $xsl;
    }

    /**
     * Retrieve the xml representation of the record
     *
     * @param int $key  the record key
     *
     * @return string
     *
     * @see EmailPrintHandler::getIndividualXML()
     */
    protected function getIndividualXML($key)
    {
        $docValues = $this->custentityCache[$key];

        $params = [
            'ENTITYID'      => $docValues['ENTITYID'] ?? '',
            'DOCCLASS'      => $docValues['DOCCLASS'] ?? '',
            'DOCPARID'      => $docValues['DOCTYPE'] ?? '',
            'DOCID'         => $key,
            'xsltemplateid' => $this->xslformat,
            'agingOn'       => $this->agingOn,
        ];

        $editor = $this->getEditor($docValues['DOCTYPE']);
        assert(Request::$r->_deliverymethod == 'xmldataraw');
        $editor->Editor_Deliver($params, false, $out);

        return $out;
    }

    /**
     * Retrieve the FOP xml representation of the record
     *
     * @param string $xml  the record in xml format
     * @param string $xsl  the xsl template
     *
     * @return array  the FOP xml representation of the record
     *
     * @see EmailPrintHandler::getFO()
     */
    protected function getFO($xml, $xsl)
    {
        $arr = array();
        $xml = str_replace('<ROOT', '<OLDROOT', $xml);
        $xml = str_replace('</ROOT', '</OLDROOT', $xml);

        $arr['xml'] = $xml;
        $xml = '<ROOT>' . $xml . '</ROOT>';
        $ok = XSLTInProcess($xsl, $xml, $fo);

        if (!$ok) {
            global $gErr;
            $gErr->addIAError("CRE-0007", __FILE__ . ":" . __LINE__);
            include "../acct/popuperror.phtml";
            exit();
        }
        $arr['fo'] = $fo;
        return $arr;
    }

    /**
     * Generate the message for emailing the document
     *
     * @param int       $key         the document key
     * @param string    $entityname  the entity name
     *
     * @return string  the message
     */
    protected function generateEmailMessage($key, $entityname)
    {
        $emailTokenObj = I18NEmailToken::buildFromResource('IA.EMAIL.INV.EMAIL_PRINT_HANDLER');
        $msg = $emailTokenObj->applyPlaceholders('body.text9',[
            'CHANGEREQUESTID' => $this->custentityCache[$key]['CHANGEREQUESTID'],
            'DESCRIPTION' => $this->custentityCache[$key]['DESCRIPTION'],
            'TOTALPRICE' => $this->custentityCache[$key]['TOTALPRICE']
        ]);
        return $msg;
    }

    protected function preBuildDocuments()
    {
        //Get all Doc IDs selected
        $selectedDocID = INTACCTarray_merge(array_keys($this->printDocIds ?? []), array_keys($this->emailDocIds ?? []));
        $selectedDocID = array_unique($selectedDocID);

        $selects = ['RECORDNO', 'CHANGEREQUESTID', 'PROJECTID', 'PROJECTNAME', 'DESCRIPTION', 'TOTALPRICE', 'WHENCREATED', 'CRSENDTONAME'];

        $emailTemplates = $this->getEmailTemplates();
        foreach ( $emailTemplates as $emailTemplate ) {
            $this->retrieveInjectionParams($selects, $emailTemplate, 'FROMADDRESS');
            $this->retrieveInjectionParams($selects, $emailTemplate, 'SUBJECT');
            $this->retrieveInjectionParams($selects, $emailTemplate, 'BODY');
        }

        $crMGR = Globals::$g->gManagerFactory->getManager('changerequest');

        $queryparams = [
            'selects' => $selects,
            'filters' => [ [
                               [ 'CHANGEREQUESTID', 'IN', $selectedDocID ],
                           ] ],
        ];
        $result = $crMGR->GetList($queryparams);

        foreach ( $result as $row ) {
            $row['DOCTYPE'] = 'pa';
            $row['DOCUMENTDATE'] = FormatDateForStorage($row['WHENCREATED']);
            $row['CUSTENTITY'] = $row['WHENCREATED'];
            $row['CUSTENTITY'] = "";
            $row['ENTITYID'] = "";
            $row['DOCHDRREC'] = $row['WHENCREATED'];
            $this->custentityCache[$row['CHANGEREQUESTID']] = $row;
        }

    }

    /**
     * @param string[] $selects
     * @param array    $emailTemplate
     * @param string   $templateElement
     */
    private function retrieveInjectionParams(&$selects, $emailTemplate, $templateElement)
    {
        $prefix = 'CHANGEREQUEST.';
        $prefixLen = strlen($prefix);
        $params = isset($emailTemplate[$templateElement]) && is_string($emailTemplate[$templateElement])
            ? Injector::retrieveInjectionParameters($emailTemplate[$templateElement]) : [];
        foreach ( $params as $param ) {
            if ( isl_str_startswith($param, $prefix) ) {
                $field = isl_substr($param, $prefixLen);
                // TODO: validate $field is valid in EntityManager
                if ( ! in_array($field, $selects) ) {
                    $selects[] = $field;
                }
            }
        }
    }

    /**
     * Return the template type for this record and document type
     * @param int               $key
     * @param array|bool|string $docType
     * @param string            $xslformat
     * @param string            $entityid
     * @param string            $docclass
     *
     * @return array
     */
    protected function getTemplateType($key, $docType, $xslformat, $entityid, $docclass)
    {
        // $indexKey = $docType . '#~#' . $entityid . '#~#' . $docclass;
        // if ( !isset($this->templateCache[$indexKey]) ) {
        /** @var ChangeRequestEditor $docEditor*/
        $docEditor = $this->getEditor($docType);
        $this->templateCache[$key] = $docEditor->GetTemplateType($key, $xslformat);
        // }

        return $this->templateCache[$key];
    }

    /**
     * Retrieve the XML for pritning from the editor instance
     *
     * @param int $key          the key of the record to print
     * @param bool $isCustomDoc true if the request is for a custom doc
     *
     * @return string the xml
     */
    protected function getXMLForPrinting($key, $isCustomDoc)
    {
        return $this->getIndividualXML($key);
    }

    /**
     * Mark the document as printed
     *
     * @param int $key  the document key
     *
     * @return bool  true if success and false otherwise
     */
    protected function markDocumentAsPrinted($key)
    {
        // TODO: Implement markDocumentAsPrinted() method.
        return true;
    }

    /**
     * Generate the subject for emailing the document
     *
     * @param int       $key         the document key
     * @param string    $entityname  the entity name
     *
     * @return string  the subject
     */
    protected function generateEmailSubject($key, $entityname)
    {
        $subject = $this->getEmailTemplateElement($key, 'SUBJECT');
        if ( $subject === null ) {
            $emailTokenObj = I18NEmailToken::buildFromResource('IA.EMAIL.INV.EMAIL_PRINT_HANDLER');
            $subject = $emailTokenObj->applyPlaceholders('subject.text3', [
                'CHANGEREQUESTID' => $this->custentityCache[$key]['CHANGEREQUESTID'],
                'PROJECTID' => $this->custentityCache[$key]['PROJECTID'],
                'PROJECTNAME' => $this->custentityCache[$key]['PROJECTNAME']
            ]);
        }

        return $subject;
    }

    /**
     * @param int|string $key
     * @param string     $entityname
     *
     * @return null|string
     */
    protected function generateCompleteEmailMessage($key, $entityname)
    {
        $msg = $this->getEmailTemplateElement($key, 'BODY');
        if ( $msg === null ) {
            // Overriding entity with sent to name
            $entityname = isNullOrBlank($this->custentityCache[$key]['CRSENDTONAME']) ? "Customer" : $this->custentityCache[$key]['CRSENDTONAME'];
            $msg = parent::generateCompleteEmailMessage($key, $entityname);
        } else {
            EmailTemplateManager::replaceNewLineWithBR($msg, false);

            // Add logo
            $this->addLogoImage($key, $msg);
        }

        $companyCache = CompanyCacheHandler::getInstance();
        $poweredByProperty = $companyCache->getProperty('COMPANYPREF', 'EMAILPOWEREDBYDISABLED');
        if ( $poweredByProperty !== 'T' ) {
            $msg .= '<br/><br/><br/><br/><br/><hr/><img width="120" src="' . self::POWEREDBY_URL . '"/>';
        }

        return $msg;
    }

    /**
     * @param string $key
     * @param string $msg
     */
    protected function addLogoImage($key, &$msg)
    {
        $emailTemplate = $this->getEmailTemplate($key);
        //eppp_p($emailTemplate);dieFL();

        // No logo
        if ( $emailTemplate['INCLUDE_LOGO'] != 'true' ) {
            return;
        }

        // Embedded image
        $uri = "cid:email_logo";
        $this->positionLogo($emailTemplate, $uri, $msg);
    }

    /**
     * @param array  $emailTemplate
     * @param string $logoURI
     * @param string $msg
     */
    protected function positionLogo($emailTemplate, $logoURI, &$msg)
    {
        if ( $emailTemplate['HORIZONTAL_LOGO_POSITION'] == 'Right' ) {
            $img = '<img style="border: 0;" align="right" src="' . $logoURI . '"><br/>';
            $img .= '<br/><br/><br/><br/><br/><br/>';
        } else {
            $img = '<img style="border: 0;" src="' . $logoURI . '"><br/>';
        }

        $msg = ( $emailTemplate['VERTICAL_LOGO_POSITION'] == 'Top' ) ? $img . $msg : $msg . $img;
    }

    /**
     * @return Injector
     */
    protected function getInjector()
    {
        if (!isset($this->injector)) {
            $this->injector = new Injector();
        }
        return $this->injector;
    }

    /**
     * @param   int     $key
     * @param   string   $element
     *
     * @return null|string
     */
    protected function getEmailTemplateElement($key, $element)
    {
        $retval = null;
        $emailTemplate = $this->getEmailTemplate($key);
        if (isset($emailTemplate[$element])) {
            $injector = $this->getInjector();
            $obj = $this->custentityCache[$key];
            $retval = $injector->inject($emailTemplate[$element], $obj);
        }
        return $retval;
    }

    /**
     * @param int $key
     *
     * @return array|null
     */
    protected function getEmailTemplate($key)
    {
        $retval = null;
        if (isset($this->emailtemplate[$key])) {
            $emailTemplateID = $this->emailtemplate[$key];
            $emailTemplates = $this->getEmailTemplates();
            if (isset($emailTemplates[$emailTemplateID])) {
                $retval = $emailTemplates[$emailTemplateID];
            }
        }
        return $retval;
    }

    /**
     * @return array[]
     */
    protected function getEmailTemplates()
    {
        if (!isset($this->emailTemplateCache)) {
            $this->emailTemplateCache = [];
            /** @var EmailTemplateManager $emailTemplateMgr */
            $emailTemplateMgr = Globals::$g->gManagerFactory->getManager('emailtemplate');
            foreach ($this->emailtemplate as $docid => $emailTemplateID) {
                if (isset($this->emailDocIds[$docid]) && !isset($this->emailTemplateCache[$emailTemplateID])) {
                    $this->emailTemplateCache[$emailTemplateID] = $emailTemplateMgr->Get($emailTemplateID);
                }
            }
        }
    
        return $this->emailTemplateCache;
    }

    /**
     * @param int|string $key
     *
     * @return string
     */
    protected function getMailFrom($key)
    {
        // Let caller override from information even when email template is provided
        // Given senders email and name is provided
        if ( $this->overridenFrom && $this->sendersemail && $this->sendersname ) {
            return parent::getMailFrom($key);
        }

        $sendersEmail = $this->getEmailTemplateElement($key, 'FROMADDRESS');
        if ($sendersEmail !== null) {
            $sendersName = $this->getEmailTemplateElement($key, 'FROMNAME');
            if ($sendersName !== null) {
                $mailFrom = $sendersName . " <" . $sendersEmail . ">";
            } else {
                $mailFrom = $sendersEmail;
            }
        } else {
            $mailFrom = parent::getMailFrom($key);
        }
        return $mailFrom;

    }

    /**
     * @param string $key
     * @param array  $emailData
     *
     * @return bool
     */
    protected function addAdditionalEmailData($key, &$emailData)
    {
        $emailTemplate = $this->getEmailTemplate($key);
        if ( $emailTemplate['INCLUDE_LOGO'] != 'true' ) {
            return true;
        }

        $gManagerFactory = Globals::$g->gManagerFactory;
        $cny_info_mgr = $gManagerFactory->getManager('company_info');
        $cny_info = $cny_info_mgr->get('');
        $filetype = $cny_info['EMAILLOGO'];
        if ( ! isset($filetype) || $filetype == '' ) {
            Globals::$g->gErr->addIAError("CRE-0008", __FILE__ . __LINE__);

            return false;
        }
        $filetype = isl_substr($filetype, 0, isl_strrpos($filetype, '.'));

        $cny = GetMyCompany();
        $imgMgr = new ImageManager($cny);

        $image = $imgMgr->GetImageBinary('', $filetype, '%logo_%', $cny);
        if ( $image === false ) {
            Globals::$g->gErr->addIAError("CRE-0009", __FILE__ . __LINE__);

            return false;
        }

        $mimetype = ImageManager::GetFiletypeInfo($filetype);

        $imageInfo = getimagesizefromstring($image);
        if ( isset($imageInfo['mime']) && $imageInfo['mime'] !== '' ) {
            $mimetype = $imageInfo['mime'];
        }

        if ( ! isset($mimetype) || $mimetype == '' ) {
            Globals::$g->gErr->addIAError("CRE-0010", __FILE__ . __LINE__);

            return false;
        }

        $emailData['embedded_content'] = [
            '0' =>
                [
                    'content_attributes' => [
                        'Content-ID'                => '<email_logo>',
                        'Content-Type'              => $mimetype,
                        'Content-Description'       => 'Email Logo',
                        'Content-Disposition'       => 'inline;',
                        'Content-Transfer-Encoding' => 'base64',
                    ],
                    'content'            => base64_encode($image),
                ],
        ];

        return true;
    }

    /**
     * Stored document attachments sent along with email
     *
     * @param string $docid
     * @param array  $deliverylog
     * @param array  $attachments
     *
     * @return bool
     */
    protected function attachAndLogAttachments($docid, &$deliverylog, &$attachments)
    {
        $emailTemplate = $this->getEmailTemplate($docid);

        if (!$emailTemplate) {
            return true;
        }

        if ($emailTemplate['INCLUDE_ATTACHMENTS'] != 'true') {
            return true;
        }

        $supDocId = $this->getSupdocId($docid);
        if (!$supDocId) {
            return true;
        }

        // Let $new_attachments go as null, this is required if we want logic to set only the supdockey
        $new_attachments = ($attachments != null) ? [] : null;

        if (!$this->getAttachmentsFromSupdoc($supDocId, $new_attachments, $deliverylog)) {
            return false;
        }

        if ($attachments != null) {
            // Let PDF be the first in email attachments
            $attachments = array_merge($new_attachments, $attachments);
        }

        return true;
    }

    /**
     * @param string $docid
     *
     * @return null|string
     *
     * @throws Exception
     */
    private function getSupdocId($docid)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $supdocmapsMgr = $gManagerFactory->getManager('supdocmaps');
        $supdocmap = $supdocmapsMgr->getSupDocMap($docid, 'CHANGEREQUEST');

        // No attachments
        if ( !$supdocmap ) {
            return null;
        }

        return $supdocmap['DOCUMENTID'];
    }

    /**
     * @param string $supdocid
     * @param array  $attachments
     * @param array  $deliverylog
     *
     * @return bool
     */
    private function getAttachmentsFromSupdoc($supdocid, &$attachments, &$deliverylog)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $supdocMgr = $gManagerFactory->getManager('supportingdocuments');
        $supdoc = $supdocMgr->GetEx($supdocid, true);

        if ( ! $supdoc ) {
            Globals::$g->gErr->addIAError("CRE-0011", __FILE__ . __LINE__);

            return false;
        }

        if ( $deliverylog !== null ) {
            // Let delivery log refer to supporting doc record
            $deliverylog['SUPDOCKEY'] = $supdoc['RECORDNO'];
        }

        if ( $attachments !== null ) {
            foreach ( $supdoc['ATTACHMENTS'] as $attachment ) {
                $attachments[] = [
                    'filename' => $attachment['NAME'],
                    'message'  => base64_encode($attachment['DATA']),
                    'encoding' => 'base64',
                ];
            }
        }

        return true;
    }

    /**
     * @noinspection PhpUnusedPrivateMethodInspection
     * @param string $attachmentHdrKey
     * @param array  $attachments
     *
     * @return bool
     */
    private function getAttachmentsFromAttachmentHdr($attachmentHdrKey, &$attachments)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $dahMgr = $gManagerFactory->getManager('deliveryattachmentheader');
        $daHeader = $dahMgr->get($attachmentHdrKey);
        if (!$daHeader) {
            Globals::$g->gErr->addIAError("CRE-0011", __FILE__ . __LINE__);
            return false;
        }

        foreach($daHeader['DELIVERY_ATTACHMENT'] as $attachment) {
            $attachments[] = array(
                'filename' => $attachment['NAME'],
                'message' => base64_encode($attachment['DATA']),
                'encoding' => 'base64'
            );
        }

        return true;
    }

}
