<?php

/**
 * Project Contract Billing Invoice Detail object manager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Sage Intacct Corporation All, Rights Reserved
 */

class PCBInvDetailManager extends EntityManager {

    use ProjectContractBillingTrait;
    use PCBInvoiceReportingTrait;

    /* @var array $allFields */
    var array $allFields = [];

    /* @var array $dateFields */
    var array $dateFields = [];

    /* @var array $numFields */
    var array $numFields = [];

    const PCB_ACTION_INSERT = "insert";
    const PCB_ACTION_REFRESH = "refresh";

    /* @var array $projectContractLinePriceCache */
    var array $projectContractLinePriceCache = [];

    /* @var array $priorApplicationAmountCache */
    var array $priorApplicationAmountCache = [];

    /* @var array $docEntryPriceCache */
    var array $docEntryPriceCache = [];

    /* @var array $projectContractDetailsCache */
    var array $projectContractDetailsCache = [];

    /* @var array $retainagePriceCache */
    var array $retainagePriceCache = [];

    /* @var array $retainageBilledToDateCache */
    var array $retainageBilledToDateCache = [];

    /* @var array $argsType */
    var array $argsType = [];

    /* @var array $bulkStmtColumnsName */
    var array $bulkStmtColumnsName = [];

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        $this->setFieldsInfo();
    }

    /**
     * Handle PCB invoice data
     * @param $obj
     * @param $action
     * @param $document
     */
    public function handlePCBInvoiceData(&$obj, $action, $document = []): bool {
        $ok = false;
        $dochdrKey = $obj['DOCHDRKEY'] ?? 0;
        $prRecordKey = $obj['PRRECORDKEY'] ?? 0;
        if ((int)$dochdrKey > 0 && ($obj['ISMANUALRETAINAGERELEASE'] == 'false' || empty($obj['ISMANUALRETAINAGERELEASE']))) {
            /*Delete existing records from details and summary table*/
            $this->deletePCBInvoice($dochdrKey);

            if ($action == self::PCB_ACTION_INSERT && !empty($document)) {
                $obj['STATE'] = $document['STATE'];
                $ok = $this->processPCBInvoiceDetails($obj, $document);
            } else {
                $document = $this->getDocDetails($dochdrKey);
                $obj['STATE'] = $document['STATE'];
                if (!empty($document['DOCID']) && empty($document['ENTRIES'])) {
                    $document['ENTRIES'] = $this->getDocEntry($document['DOCID']);
                    $ok = $this->processPCBInvoiceDetails($obj, $document);
                }
            }
        }else if((int)$prRecordKey > 0 && (($obj['ISMANUALRETAINAGERELEASE'] ?? 'false') == 'true' || ($obj['STATE'] ?? '') == 'Reversal')){
            $ok = $this->handleManualRetainageReleaseInvoicePCBData($obj, $action, []);
        }
        return $ok;
    }

    /**
     * Get all project contract lines and inlude unbilled lines into the current invoice
     * @param $document
     * @param $prRecordKey
     */
    protected function getUnbilledProjectContractLines(&$document, $prRecordKey = 0)
    {
        $documentData = $document;
        if(!empty($prRecordKey) && isset($document[$prRecordKey])){
            $documentData = $document[$prRecordKey];
        }
        $projectContractLines = $this->getProjectContractLines($documentData['PROJECTCONTRACTID']);
        $unBilledLines = [];

        if(!empty($projectContractLines)){
            foreach($projectContractLines as $lines){
                if($lines['BILLABLE'] == 'true'){

                    foreach($documentData["ITEMS"] as $items){

                        if($lines['RECORDNO'] == $items['PROJECTCONTRACTLINEKEY']){
                            $trxAmount = $unBilledLines[$lines['RECORDNO']]['TRX_AMOUNT'] ?? 0;
                            $unBilledLines[$lines['RECORDNO']] = [
                                'TRX_AMOUNT' => ibcadd($trxAmount, $items['TRX_AMOUNT']),
                                'PROJECTCONTRACTLINEKEY' => $lines['RECORDNO'],
                                'LOCATION#' => $documentData['MEGAENTITYKEY'] ?? $documentData['LOCATION#']
                            ];
                        }
                        else if(!isset($unBilledLines[$lines['RECORDNO']])){
                            $unBilledLines[$lines['RECORDNO']] = [
                                'TRX_AMOUNT' => 0,
                                'PROJECTCONTRACTLINEKEY' => $lines['RECORDNO'],
                                'LOCATION#' => $documentData['MEGAENTITYKEY'] ?? $documentData['LOCATION#']
                            ];
                        }
                    }

                    if(!empty($prRecordKey) && !empty(($document[$prRecordKey]))){
                        $document[$prRecordKey]['NEW_ITEMS'] = $unBilledLines;
                    }else{
                        $document['NEW_ITEMS'] = $unBilledLines;
                    }
                }
            }
        }
    }

    /**
     * Handle manual retinage release data while creating new invoice
     * @param $values
     * @param $action
     */
    public function handlePCBManualRetainageReleaseInvoice($values, $action){
        if(isset($values) && !empty($values['RELEASED_LINES'])){
            $releasedLines = $this->processReleasedLines($values);
            foreach($releasedLines as $prRecordKey => $releasedData){
                $obj['PRRECORDKEY'] = $prRecordKey;
                if(!empty($releasedData['ITEMS'])){
                    $releasedData['MANUAL_RET_REL'] = $values['MANUAL_RET_REL'];
                    $releasedData['GLPOSTINGDATE'] = $values['GLPOSTINGDATE'];
                    $releasedData['RELEASEDATE'] = $values['RELEASEDATE'];
                    $releasedData['WHENCREATED'] = $values['WHENCREATED'];
                    $releasedData['MEGAENTITYKEY'] = $values['MEGAENTITYKEY'];
                    $this->handleManualRetainageReleaseInvoicePCBData($obj, $action, $releasedData);
                }
            }
        }
    }

    /**
     * Organize retinage release data based on project contcat and lines and get unbilled project contract lines
     * @param $values
     * @return array
     */
    protected function processReleasedLines($values){

        $processedReleasedLines = [];
        foreach($values['RELEASED_LINES'] as $releasedLine){
            $trxAmount = $processedReleasedLines[$releasedLine['RECORDKEY']][$releasedLine['PROJECTCONTRACTKEY']][$releasedLine['PROJECTCONTRACTLINEKEY']]['TRX_AMOUNT'] ?? 0;
            $processedReleasedLines[$releasedLine['RECORDKEY']][$releasedLine['PROJECTCONTRACTKEY']][$releasedLine['PROJECTCONTRACTLINEKEY']] = [
                    'TRX_AMOUNT' => ibcadd($trxAmount, $releasedLine['TRX_AMOUNT']),
                    'PROJECTCONTRACTID' => $releasedLine['PROJECTCONTRACTID'],
                    'WHENCREATED' => $releasedLine['WHENCREATED'],
                    'RELEASEDATE' => $values['RELEASEDATE'],
                    'LOCATION#' => $releasedLine['LOCATION#'],
            ];
        }

        $finalData = [];
        foreach($processedReleasedLines as $prRecordKey => $lines){
            foreach($lines as $pcKey => $pcData	){
                $pclkeyData = [];
                foreach($pcData as $pclKey => $data){
                    $pclkeyData[] = ['PROJECTCONTRACTLINEKEY' => $pclKey, 'TRX_AMOUNT' => $data['TRX_AMOUNT'], 'LOCATION#' => $data['LOCATION#']];
                    $finalData[$prRecordKey] = [
                        'WHENCREATED' => $data['WHENCREATED'],
                        'RELEASEDATE' => $data['RELEASEDATE'],
                        'PROJECTCONTRACTKEY' => $pcKey,
                        'PROJECTCONTRACTID' => $data['PROJECTCONTRACTID'],
                        'ITEMS' => $pclkeyData,
                        'LOCATION#' => $data['LOCATION#'],
                    ];
                }
                $this->getUnbilledProjectContractLines($finalData, $prRecordKey);
            }
        }

        return $finalData;
    }

    /**
     * Handle manual retainage release PCB data
     * @param $obj
     * @param $action
     * @param $document
     */
    protected function handleManualRetainageReleaseInvoicePCBData(&$obj, $action, $document){
        /*Delete existing records from details and summary table*/
        $this->deleteManualRetainageReleasePCBInvoice($obj['PRRECORDKEY']);
        $this->deleteManualRetainageReleasePCBSummaryIfDuplicate($obj['PRRECORDKEY']);
        $ok = true;
        if ($action == self::PCB_ACTION_INSERT && !empty($document)) {
           $ok = $this->processManualRetainageReleaseInvoiceDetails($obj, $document);
        }else if($action == self::PCB_ACTION_REFRESH){
            $arInvoiceManager = Globals::$g->gManagerFactory->getManager('arinvoice');
            $arinvoice = $arInvoiceManager->get($obj['PRRECORDKEY']);
            if(!empty($arinvoice) && !empty($arinvoice['ITEMS'])){
                $arinvoice['MANUAL_RET_REL'] =  $obj['ISMANUALRETAINAGERELEASE'] ?? 'false';
                $this->getUnbilledProjectContractLines($arinvoice);
                $ok = $this->processManualRetainageReleaseInvoiceDetails($obj, $arinvoice);
            }
        }
        return $ok;
    }

    /**
     * Calculate totals for manual retinage release lines
     * @param $obj
     * @param $document
     */
    protected function processManualRetainageReleaseInvoiceDetails($obj, $document){

        $ok = true;
        if(empty($document['BILLTHROUGHDATE'])){
            $document['BILLTHROUGHDATE'] = ($document['RELEASEDATE'] ?? $document['WHENCREATED']);
        }

        if(isset($document['NEW_ITEMS']) && !empty($document['NEW_ITEMS'])){
            $document['PRRECORDKEY'] = $pcb_details['PRRECORDKEY'] = $obj['PRRECORDKEY'];
            $document['PCB_LOCATION_KEY'] = $document['LOCATION#'];
            $pcbinvsummary = Globals::$g->gManagerFactory->getManager('pcbinvsummary');
            if(!empty($document['PRRECORDKEY'])){
                $ok = $this->handleSummarySave($document,'PRRECORDKEY');
            }
            if($ok){
                foreach($document['NEW_ITEMS'] as $releasedLines){
                    $pcb_details['PCB_INVOICE_SUMMARY_KEY'] = $document['PCB_SUMMARY_RECORDNO'];
                    $pcb_details['PROJECTCONTRACTKEY'] = $document['PROJECTCONTRACTKEY'];
                    $pcb_details['PROJECTCONTRACTLINEKEY'] = $releasedLines['PROJECTCONTRACTLINEKEY'];
                    $ok = $ok && $this->processPCLinesForManualRetainage($pcb_details, $document, $releasedLines);
                }
            }
        }

        if($ok && isset($pcbinvsummary)){
            $ok = $pcbinvsummary->processPCBInvoiceSummary($obj, $document);
        }

        return $ok;
    }

    /**
     * Insert processed PCB invoice details
     * @param $pcb_details
     * @param $document
     * @param $releasedLines
     * @return bool
     */
    protected function processPCLinesForManualRetainage(&$pcb_details, &$document, $releasedLines){

        $this->resetCalculatedFields($pcb_details);
        $this->getProjectContractLinePrice($releasedLines['PROJECTCONTRACTLINEKEY'], $document['BILLTHROUGHDATE'], $pcb_details);
        $this->getProjectContractDetails($document['PROJECTCONTRACTKEY'], $releasedLines['PROJECTCONTRACTLINEKEY'],  $pcb_details);
        $this->getDocEntryDetailsforRetainageReleaseInvoice($releasedLines, $document, $pcb_details);
        $this->getRetainagePrice($document, $releasedLines['PROJECTCONTRACTLINEKEY'],  $pcb_details, $releasedLines);
        $this->calculateDueTotal($pcb_details);
        $pcb_details['LOCATIONKEY'] =  $releasedLines['LOCATION#'] ?? ($document['LOCATION#'] ?? $document['MEGAENTITYKEY']);
        $values = $pcb_details;

        /*Insert the data*/
        $ok = parent::regularAdd($values);
        if($ok){
            unset($values);
        }
        return $ok;
    }

    /**
     * Get invoice details for Retainage Release
     * @param $releasedLines
     * @param $pcb_details
     */
    protected function getDocEntryDetailsforRetainageReleaseInvoice($releasedLines, $document, &$pcb_details){

        $query = "SELECT DOCHDR.CNY#,DOCHDR.PROJECTCONTRACTKEY,DOCENTRY.PROJECTCONTRACTLINEKEY,
                               SUM(CASE WHEN DOCENTRY.ISRETAINAGERELEASE IS NULL OR DOCENTRY.ISRETAINAGERELEASE = 'F' THEN NVL(DOCENTRY.TRX_VALUE,0) ELSE 0 END) TRX_VALUE
                        FROM DOCENTRY
                            JOIN DOCHDR ON DOCENTRY.CNY# = DOCHDR.CNY# AND DOCENTRY.DOCHDRKEY = DOCHDR.RECORD# AND DOCHDR.PROJECTCONTRACTKEY=:2
                        WHERE DOCENTRY.CNY#=:1 AND DOCHDR.STATE != 'I' AND DOCENTRY.PROJECTCONTRACTKEY= :2 AND DOCENTRY.PROJECTCONTRACTLINEKEY= :3 AND DOCHDR.WHENPOSTED <= TO_DATE(:4, 'MM/DD/YYYY')
                        GROUP BY DOCHDR.CNY#,DOCHDR.PROJECTCONTRACTKEY,DOCENTRY.PROJECTCONTRACTLINEKEY";
        $result =  QueryResult(array($query, GetMyCompany(), $document['PROJECTCONTRACTKEY'], (int)$releasedLines['PROJECTCONTRACTLINEKEY'], $document['BILLTHROUGHDATE']));

        if(!empty($result[0])) {
            $result = $result[0];
            $pcb_details['COMPPRIORAPP'] = $pcb_details['TCTODATE'] = $result['TRX_VALUE'];
        }

        $pcb_details['COMPTHISPERIOD'] = $pcb_details['STOREDMATERIALS'] = $pcb_details['INVAMOUNTRETAINED'] = $pcb_details['RETAINAGEPERCENTAGE'] = 0;
        $pcb_details['PERCCOMPTODATE'] = ibcmul(ibcdiv($pcb_details['TCTODATE'] ?? 0, ibcadd($pcb_details['ORIGINALCONTRACTAMOUNT'], $pcb_details['TNACHANGES']), 4, true), '100', 2, true);
        $pcb_details['INVRETAINAGEBILLED'] = $pcb_details['TOTALBILLED'] = $releasedLines['TRX_AMOUNT'];
        $pcb_details['NETCHANGERETHELD'] = ibcsub($pcb_details['INVAMOUNTRETAINED'], $pcb_details['INVRETAINAGEBILLED']);
    }


    /**
     * Process PCB invoice details
     * @param $obj
     * @param $document
     */
    protected function processPCBInvoiceDetails($obj, $document): bool {
        $this->setInvoiceCount($document);
        $pcbinvsummary = Globals::$g->gManagerFactory->getManager('pcbinvsummary');

        if(empty($document['BILLTHROUGHDATE'])){
            $document['BILLTHROUGHDATE'] = $document['WHENCREATED'];
        }

        $document['DOCHDRKEY'] = $obj['DOCHDRKEY'];
        $pcb_details['DOCHDRKEY'] = $obj['DOCHDRKEY'];
        $pcb_details['DOCNO'] = $document['DOCNO'];
        $pcb_details['PROJECTCONTRACTKEY'] = $document['PROJECTCONTRACTKEY'];
        $pcb_details['PROJECTCONTRACTID'] = $document['PROJECTCONTRACTID'];
        $pcb_details['PROJECTCONTRACTNAME'] = $document['PROJECTCONTRACTNAME'];
        $pcb_details['LOCATIONKEY'] =  $document['MEGAENTITYKEY'];

        $projectContractLines = $this->getProjectContractLines($document['PROJECTCONTRACTID']);

        $billableLines = [];
        if(!empty($projectContractLines)){
            foreach($projectContractLines as $lines){
                if($lines['BILLABLE'] == 'true'){
                    $billableLines[$lines['RECORDNO']] = $lines['PROJECTCONTRACTLINEID'];
                }
            }
        }

        $invoiceLines = $mergedBillableLines = $retainagetToBill = [];
        if(!empty($document['ENTRIES'])) {
            foreach ($document['ENTRIES'] as $entries) {
                $projectContractLineKey = (int)$entries['PROJECTCONTRACTLINEKEY'];

                if($entries['ISRETAINAGERELEASE'] == 'true'){
                    $retainagetToBill[$projectContractLineKey] = ibcadd($retainagetToBill[$projectContractLineKey] ?? 0, $entries['TRX_VALUE']);
                }
                if (!in_array($projectContractLineKey, array_keys($invoiceLines))) {
                    $invoiceLines[$projectContractLineKey] = $entries;
                }else if(in_array($projectContractLineKey, array_keys($invoiceLines)) && $entries['ISRETAINAGERELEASE'] == 'true'){
                    $invoiceLines[$projectContractLineKey]['RETAINAGETOBILL'] = $retainagetToBill[$projectContractLineKey] ?? 0;
                }
            }
        }
        $ok = false;
        if(!empty($billableLines) && !empty($invoiceLines)){
            foreach($billableLines as $lineKey => $lineID){
                $mergedBillableLines[$lineKey] = ['BILLED_IN_THIS_INVOICE' => false, 'LINEID' => $lineID];
                if(in_array($lineKey, array_keys($invoiceLines))){
                    $mergedBillableLines[$lineKey] = [
                            'BILLED_IN_THIS_INVOICE' => true,
                            'LINEID' => $lineID,
                            'TRX_AMOUNTRETAINED' => $invoiceLines[$lineKey]['TRX_AMOUNTRETAINED'] ?? 0,
                            'RETAINAGETOBILL' => $invoiceLines[$lineKey]['RETAINAGETOBILL'] ?? 0,
                            'COMPLETEDTHISPERIOD' => $invoiceLines[$lineKey]['COMPLETEDTHISPERIOD'] ?? 0,
                            'STOREDMATERIALS' => $invoiceLines[$lineKey]['STOREDMATERIALS'] ?? 0,
                            'TOTALCOMPLETEDTODATE' => $invoiceLines[$lineKey]['TOTALCOMPLETEDTODATE'] ?? 0,
                            'NETCHANGERETHELD' => ibcsub((float)($invoiceLines[$lineKey]['TRX_AMOUNTRETAINED'] ?? 0), (float)($invoiceLines[$lineKey]['RETAINAGETOBILL'] ?? 0))
                    ];
                }
            }
        }

        $this->getInvoiceSubTotal($obj['DOCHDRKEY'], $document, $pcb_details);

        $ok = false;
        if(!empty($mergedBillableLines)){
            $document['PCB_LOCATION_KEY'] = $document['MEGAENTITYKEY'] ?? null;
            if(!empty($document['DOCHDRKEY'])){
                $ok = $this->handleSummarySave($document,'DOCHDRKEY');
            }
            $schemaObject = $this->_schemas[$this->_entity]['object'];
            $schemaObject = array_diff($schemaObject, ['MEGAENTITYKEY', 'MEGAENTITYID', 'MEGAENTITYNAME']);
            $bulkInsert = [];
            $cny = GetMyCompany();
            $coMgr = Globals::$g->gManagerFactory->getManager('cosetup');
            $reserveCnt = count($mergedBillableLines);
            $recordNo = $coMgr->GetNextSequence('PCB_INVOICE_DETAILS', $reserveCnt);

            foreach($mergedBillableLines as $lineKey => $lineValue){
                $pcb_details['PROJECTCONTRACTLINEKEY'] = $lineKey;
                $pcb_details['PCB_INVOICE_SUMMARY_KEY'] = $document['PCB_SUMMARY_RECORDNO'] ?? 0;
                $dochdrKey = (int)$obj['DOCHDRKEY'];
                if($lineValue['BILLED_IN_THIS_INVOICE'] == false){
                    $dochdrKey = $this->getLastBilledInvoiceKey($document, $lineKey);
                }
                $this->resetCalculatedFields($pcb_details);
                $this->getProjectContractLinePrice($lineKey, $document['BILLTHROUGHDATE'], $pcb_details);
                $this->getDocEntryPrice($dochdrKey, $document, $lineKey,  $pcb_details, $lineValue);
                $this->getProjectContractDetails($document['PROJECTCONTRACTKEY'], $lineKey,  $pcb_details);
                $this->getRetainagePrice($document, $lineKey,  $pcb_details, $lineValue);
                $this->calculateDueTotal($pcb_details);
                $values = $pcb_details;

                $data = $this->mapValuesToSchemaObject($schemaObject, $values);
                $nvalues = $this->_PreProcessForUpdate($data, true);
                $this->updateAuditColumns($nvalues, 'add');
                $nvalues['cny#'] = $cny;
                $nvalues['record#'] = $recordNo;
                $this->setColumnsName($nvalues);
                $this->setArgsType($nvalues);
                $bulkInsert[] = array_values($nvalues);
                unset($nvalues);
                unset($values);
                $recordNo++;
            }
            $ok = $ok && $this->processBulkInsert($bulkInsert);
        }


        if($ok){
            $ok = $ok && $pcbinvsummary->processPCBInvoiceSummary($obj, $document);
        }

        return $ok;
    }


    /**
     * Get doc entries
     * @param $docID
     * @return array
     */
    protected function getDocEntry($docID){
       return self::GetListQuick('sodocumententry', [], ['DOCID' => $docID]);
    }

    /**
     * Get doc details
     * @param $dochdrkey
     */
    protected function getDocDetails($dochdrkey){

        $sodocument= Globals::$g->gManagerFactory->getManager('sodocument');
        $params = array(
            'selects' => [ 'BILLTHROUGHDATE', 'BILLAPPLICATIONNO', 'WHENCREATED', 'WHENPOSTED', 'DOCID', 'DOCNO','MEGAENTITYNAME', 'MEGAENTITYKEY', 'PROJECTCONTRACTKEY','PROJECTCONTRACTID', 'PROJECTCONTRACTNAME', 'STATE'],
            'filters' => array(
                array(
                    array('RECORDNO', '=', $dochdrkey)
                ),
            ),
        );

        $result = $sodocument->GetList($params);
        if(!empty($result) && !empty($result[0])){
            $result = $result[0];
            if(empty($result['BILLTHROUGHDATE'])){
                $result['BILLTHROUGHDATE'] = $result['WHENCREATED'];
            }
        }
        return $result;
    }

    /**
     * Calculate project contract line fields
     * @param $projectcontractlinekey
     * @param $billthroughDate
     */
    protected function getProjectContractLinePrice($projectcontractlinekey, $billthroughDate, &$pcb_details){

        if(empty($pcb_details['PROJECTCONTRACTKEY'])){
            return [];
        }

        $pcKey = $pcb_details['PROJECTCONTRACTKEY'];
        if(!$this->projectContractLinePriceCache[$pcKey]){
            $pclStmt = "select record# recordno from projectcontractline where cny# =:1 and projectcontractkey =:2 and status='T'";
            $pclresult = QueryResult([$pclStmt, GetMyCompany(), $pcKey]);

            $stmt = "SELECT cd.cny#, cd.record#, cd.pclkey, cd.wftype, cd.lineprice, CASE WHEN cd.recordtype = 'cl' THEN
                    cd.pricedate ELSE ch.pricedate END pricedate, cd.recordtype, cd.updatepjcontract
                    FROM crdetail cd LEFT JOIN crheader ch ON cd.cny# = ch.cny# AND cd.crkey = ch.record#
                    WHERE cd.cny# =:1 AND ( recordtype = 'cl' OR ( recordtype = 'cr'AND updatepjcontract = 'T' ) )
                    AND cd.wftype IN ( 'O', 'R', 'A' )";
            $stmt = PrepINClauseStmt($stmt, array_column($pclresult, 'RECORDNO'), " and cd.pclkey ");
            $results = QueryResult([$stmt, GetMyCompany()]);
            $data = [];
            $dateTime = new DateTime($billthroughDate);
            $dateTime->modify('first day of this month');
            $monthBeginningDate =  $dateTime->format("m/d/Y");
            $dateTime->modify("last day of previous month");
            $previousMonthEndDate = $dateTime->format("m/d/Y");
            foreach($results as $result){
                $total_o = $total_ra = $total_ra_pv = $total_ra_nv = 0;
                $total_capm_add = $total_capm_ded = $total_cacm_add = $total_cacm_ded = $original_contract_amount = $total_net_approved = 0;
                $linePrice = $result["LINEPRICE"] ?? 0;
                $priceDate = $result["PRICEDATE"];
                if($result['WFTYPE'] == 'O'){
                    $total_o = $linePrice;
                }elseif($result['WFTYPE'] == 'R' || $result['WFTYPE'] == 'A'){
                    $total_ra = $linePrice;
                    if($linePrice > 0){
                        $total_ra_pv = $linePrice;
                    }else{
                        $total_ra_nv = $linePrice;
                    }
                }

                if(DateCompare($priceDate, $previousMonthEndDate) != 1){
                    $total_capm_add = $total_ra_pv;
                    $total_capm_ded = $total_ra_nv;
                }
                if(DateCompare($priceDate, $monthBeginningDate) != -1 && DateCompare($priceDate, $billthroughDate) != 1 ){
                    $total_cacm_add = $total_ra_pv;
                    $total_cacm_ded = $total_ra_nv;
                }

                if(DateCompare($priceDate, $billthroughDate) != 1){
                    $original_contract_amount = $total_o;
                    $total_net_approved = $total_ra;
                }

                $data[$result['PCLKEY']]['TCAPMADDITION'] = ibcadd($data[$result['PCLKEY']]['TCAPMADDITION'],  $total_capm_add);
                $data[$result['PCLKEY']]['TCAPMDEDUCTION'] = ibcadd($data[$result['PCLKEY']]['TCAPMDEDUCTION'], $total_capm_ded);
                $data[$result['PCLKEY']]['TCATMADDITION'] = ibcadd($data[$result['PCLKEY']]['TCATMADDITION'], $total_cacm_add);
                $data[$result['PCLKEY']]['TCATMDEDUCTION'] = ibcadd($data[$result['PCLKEY']]['TCATMDEDUCTION'], $total_cacm_ded);
                $data[$result['PCLKEY']]['ORIGINALCONTRACTAMOUNT'] = ibcadd($data[$result['PCLKEY']]['ORIGINALCONTRACTAMOUNT'], $original_contract_amount);
                $data[$result['PCLKEY']]['TNACHANGES'] = ibcadd($data[$result['PCLKEY']]['TNACHANGES'], $total_net_approved);
                $data[$result['PCLKEY']]['TRCLAMOUNT'] = ibcadd($data[$result['PCLKEY']]['ORIGINALCONTRACTAMOUNT'], $data[$result['PCLKEY']]['TNACHANGES']);
            }
            $this->projectContractLinePriceCache[$pcKey] = $data;
        }

        $pcb_details = array_merge($pcb_details, $this->projectContractLinePriceCache[$pcKey][$projectcontractlinekey] ?? []);
    }

    /**
     * Calculate doc entry fields
     * @param $dochdrkey
     * @param $document
     * @param $projectcontractlinekey
     * @param $pcb_details
     */
    protected function getDocEntryPrice($dochdrkey, $document, $projectcontractlinekey, &$pcb_details, $value){

        $pcb_details['COMPPRIORAPP'] = $this->getPriorApplicationAmount($dochdrkey, $document, $projectcontractlinekey, $value);

        if(!isset($this->docEntryPriceCache[$dochdrkey])){

            $query = "WITH DE_TOTALS AS (
                    SELECT DOCENTRY.CNY#,DOCENTRY.DOCHDRKEY,DOCENTRY.PROJECTCONTRACTKEY,DOCENTRY.PROJECTCONTRACTLINEKEY,
                           SUM(NVL(DOCENTRY.TRX_VALUE,0)) TOTAL_BILLED,
                           SUM(NVL(DOCENTRY.TRX_AMOUNTRETAINED,0)) INV_AMT_RET,
                           SUM(NVL(DOCENTRY.AMOUNTRETAINED,0)) AMT_RET,
                           SUM(CASE WHEN DOCENTRY.ISRETAINAGERELEASE = 'T' THEN NVL(DOCENTRY.TRX_VALUE,0) END) INV_RET_BILLED,
                           SUM(CASE WHEN (DOCENTRY.ISRETAINAGERELEASE IS NULL OR DOCENTRY.ISRETAINAGERELEASE = 'F') THEN NVL(DOCENTRY.COMPLETEDTHISPERIOD,0) END) COMP_THIS_PERIOD,
                           SUM(CASE WHEN (DOCENTRY.ISRETAINAGERELEASE IS NULL OR DOCENTRY.ISRETAINAGERELEASE = 'F') THEN NVL(DOCENTRY.STOREDMATERIALS,0) END) STORED_MAT,
                           SUM(CASE WHEN DOCENTRY.ISRETAINAGERELEASE IS NULL OR DOCENTRY.ISRETAINAGERELEASE = 'F' THEN NVL(DOCENTRY.TRX_VALUE,0) ELSE 0 END) COMP_THIS_INVOICE
                    FROM DOCENTRY JOIN DOCHDR ON DOCENTRY.CNY# = DOCHDR.CNY# AND DOCENTRY.DOCHDRKEY = DOCHDR.RECORD# AND DOCHDR.PROJECTCONTRACTKEY= :2
                    WHERE DOCENTRY.CNY#=:1 AND DOCHDR.STATE != 'I' AND DOCENTRY.PROJECTCONTRACTKEY=:2
                    GROUP BY DOCENTRY.CNY#,DOCENTRY.DOCHDRKEY,DOCENTRY.PROJECTCONTRACTKEY,DOCENTRY.PROJECTCONTRACTLINEKEY
                ),
                CTD AS (
                    SELECT CNY#,DOCHDRKEY,PROJECTCONTRACTKEY,PROJECTCONTRACTLINEKEY, NVL(TOTAL_BILLED,0) TOTAL_BILLED, NVL(INV_AMT_RET,0) INV_AMT_RET, NVL(AMT_RET,0) AMT_RET, NVL(INV_RET_BILLED,0) INV_RET_BILLED, NVL(COMP_THIS_PERIOD,0) COMP_THIS_PERIOD, NVL(STORED_MAT,0) STORED_MAT, NVL(COMP_THIS_INVOICE,0) COMP_THIS_INVOICE,
                           SUM(NVL(COMP_THIS_INVOICE,0)) OVER (PARTITION BY CNY#,PROJECTCONTRACTKEY,PROJECTCONTRACTLINEKEY ORDER BY DOCHDRKEY DESC ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING) TOTAL_COMPLETED_TILL_DATE
                    FROM DE_TOTALS
                ) select CTD.*, NVL(NVL(CTD.TOTAL_COMPLETED_TILL_DATE,0) - NVL(CTD.COMP_THIS_INVOICE,0),0) COMPLETED_FROM_PRIOR_APPLICATIONS from CTD where ctd.DOCHDRKEY=:3";

            $results =  QueryResult(array($query, GetMyCompany(), (int)$document['PROJECTCONTRACTKEY'], $dochdrkey));
            $data = [];
            foreach($results as $res){
                $data[$res['PROJECTCONTRACTLINEKEY']] = $res;
            }
            $this->docEntryPriceCache[$dochdrkey] = $data;
        }

        if($document['STATE'] == 'Draft'){
            $pcb_details['NETCHANGERETHELD'] = $value['NETCHANGERETHELD'] ?? 0;
            $pcb_details['COMPTHISPERIOD'] = $value['COMPLETEDTHISPERIOD'] ?? 0;
            $pcb_details['STOREDMATERIALS'] = $value['STOREDMATERIALS'] ?? 0;
            $pcb_details['TCTODATE'] = ibcadd($pcb_details['COMPPRIORAPP'], ibcadd($pcb_details['COMPTHISPERIOD'], $pcb_details['STOREDMATERIALS']));
            $pcb_details['INVAMOUNTRETAINED'] = $value['TRX_AMOUNTRETAINED'] ?? 0;
            $pcb_details['INVRETAINAGEBILLED'] = $value['RETAINAGETOBILL'] ?? 0;
            $pcb_details['PERCCOMPTODATE'] = ibcmul(ibcdiv($pcb_details['TCTODATE'], ibcadd($pcb_details['ORIGINALCONTRACTAMOUNT'], $pcb_details['TNACHANGES']), 4, true), '100', 2, true);
            $pcb_details['RETAINAGEPERCENTAGE'] =  ibcmul(ibcdiv($pcb_details['INVAMOUNTRETAINED'], ibcadd($pcb_details['COMPTHISPERIOD'], $pcb_details['STOREDMATERIALS']), 4, true), '100', 2, true);
            $pcb_details['TOTALBILLED'] = ibcadd(ibcadd($pcb_details['COMPTHISPERIOD'], $pcb_details['STOREDMATERIALS']), $pcb_details['INVRETAINAGEBILLED']);
        }
        $result = $this->docEntryPriceCache[$dochdrkey][$projectcontractlinekey] ?? [];
        if(!empty($result)) {
            $pcb_details['TOTALBILLED'] = $value['BILLED_IN_THIS_INVOICE'] ? $result['TOTAL_BILLED'] : 0;
            $pcb_details['COMPTHISPERIOD'] = $value['BILLED_IN_THIS_INVOICE'] ? $result['COMP_THIS_PERIOD'] : 0;
            $pcb_details['STOREDMATERIALS'] = $value['BILLED_IN_THIS_INVOICE'] ? $result['STORED_MAT'] : 0;
            $pcb_details['TCTODATE'] = $value['BILLED_IN_THIS_INVOICE'] ? ibcadd($pcb_details['COMPPRIORAPP'], $result['COMP_THIS_INVOICE']) : $pcb_details['COMPPRIORAPP'];
            $pcb_details['PERCCOMPTODATE'] = ibcmul(ibcdiv($pcb_details['TCTODATE'], ibcadd($pcb_details['ORIGINALCONTRACTAMOUNT'], $pcb_details['TNACHANGES']), 4, true), '100', 2, true);
            $pcb_details['INVAMOUNTRETAINED'] = $value['BILLED_IN_THIS_INVOICE'] ? $result['INV_AMT_RET'] : 0;
            $pcb_details['INVRETAINAGEBILLED'] = $value['BILLED_IN_THIS_INVOICE'] ? $result['INV_RET_BILLED'] : 0;
            $pcb_details['RETAINAGEPERCENTAGE'] =  ibcmul(ibcdiv($pcb_details['INVAMOUNTRETAINED'], $result['COMP_THIS_INVOICE'], 4, true), '100', 2, true);
            $pcb_details['NETCHANGERETHELD'] = ibcsub($pcb_details['INVAMOUNTRETAINED'], $pcb_details['INVRETAINAGEBILLED']);
        }
    }

    /**
     * Calculate prior application amount based on billing through date and GL posting date
     * @param $dochdrkey
     * @param $document
     * @param $projectcontractlinekey
     * @param $value
     */
    protected function getPriorApplicationAmount($dochdrkey, $document, $projectcontractlinekey, $value){
        if(!isset($this->priorApplicationAmountCache[$document['PROJECTCONTRACTKEY']])){

            $query = "SELECT DOCENTRY.CNY#,DOCENTRY.DOCHDRKEY,DOCENTRY.PROJECTCONTRACTKEY,DOCENTRY.PROJECTCONTRACTLINEKEY,
                               SUM(CASE WHEN DOCENTRY.ISRETAINAGERELEASE IS NULL OR DOCENTRY.ISRETAINAGERELEASE = 'F' THEN NVL(DOCENTRY.TRX_VALUE,0) ELSE 0 END) TRX_VALUE
                        FROM DOCENTRY
                            JOIN DOCHDR ON DOCENTRY.CNY# = DOCHDR.CNY# AND DOCENTRY.DOCHDRKEY = DOCHDR.RECORD# AND DOCHDR.PROJECTCONTRACTKEY=:2
                        WHERE DOCENTRY.CNY#=:1 AND DOCHDR.STATE != 'I' AND DOCENTRY.PROJECTCONTRACTKEY= :2 AND DOCENTRY.TRX_VALUE != 0  AND DOCHDR.WHENPOSTED <= TO_DATE(:3, 'MM/DD/YYYY')
                        GROUP BY DOCENTRY.CNY#,DOCENTRY.DOCHDRKEY,DOCENTRY.PROJECTCONTRACTKEY,DOCENTRY.PROJECTCONTRACTLINEKEY";

            $result =  QueryResult(array($query, GetMyCompany(), $document['PROJECTCONTRACTKEY'], $document['BILLTHROUGHDATE']));
            $data = [];
            if(!empty($result)) {
                foreach ( $result as $line ) {
                    $data[$line['PROJECTCONTRACTLINEKEY']][] = $line;
                }
            }
            $this->priorApplicationAmountCache[$document['PROJECTCONTRACTKEY']] = $data;
        }
        $result = $this->priorApplicationAmountCache[$document['PROJECTCONTRACTKEY']][$projectcontractlinekey];
        $priorApp = 0;
        if(!empty($result)){
            foreach ($result as $line){
                    if($value['BILLED_IN_THIS_INVOICE'] == true && $line['DOCHDRKEY'] != $dochdrkey){
                        $priorApp = ibcadd($priorApp, $line['TRX_VALUE']);
                    }else if($value['BILLED_IN_THIS_INVOICE'] == false){
                        $priorApp = ibcadd($priorApp, $line['TRX_VALUE']);
                    }
            }
        }

        return $priorApp;
    }


    /**
     * Get project contract details
     * @param $projectcontractkey
     * @param $projectcontractlinekey
     * @param $pcb_details
     */
    protected function getProjectContractDetails($projectcontractkey, $projectcontractlinekey, &$pcb_details){

        if(!isset($this->projectContractDetailsCache[$projectcontractkey])){

            $query = "SELECT PCL.RECORD# RECORDNO, PCL.PROJECTCONTRACTLINEID,PCL.EXTERNAL_REFNO,PCL.INTERNAL_REF_NO,P.RECORD# PROJECTKEY,P.PROJECTID,T.RECORD# TASKKEY,T.TASKID,PCL.BILLABLE
                    FROM PROJECTCONTRACTLINE PCL
                    LEFT JOIN PROJECT P ON PCL.CNY# = P.CNY# AND PCL.PROJECTDIMKEY = P.RECORD#
                    LEFT JOIN TASK T ON PCL.CNY# = T.CNY# AND PCL.TASKDIMKEY = T.RECORD#
                  WHERE PCL.CNY# = :1 AND PCL.PROJECTCONTRACTKEY = :2";

            $result = QueryResult([$query, GetMyCompany(), $projectcontractkey]);
            $data = [];
            foreach($result as $res){
                $data[$res['RECORDNO']] = $res;
            }
            $this->projectContractDetailsCache[$projectcontractkey] = $data;
        }

        $result = $this->projectContractDetailsCache[$projectcontractkey][$projectcontractlinekey];

        if(!empty($result)) {
            $pcb_details['EXTERNALREFNO'] = $result['EXTERNAL_REFNO'];
            $pcb_details['INTERNALREFNO'] = $result['INTERNAL_REF_NO'];
            $pcb_details['PROJECTKEY'] = $result['PROJECTKEY'];
            $pcb_details['PROJECTID'] = $result['PROJECTID'];
            $pcb_details['TASKKEY'] = $result['TASKKEY'];
            $pcb_details['TASKID'] = $result['TASKID'];
            $pcb_details['BILLABLE'] = ($result['BILLABLE'] == 'T') ? true : false;
        }
    }

    /**
     * Calculate invoice subtotals
     * @param $dochdrKey
     * @param $document
     * @param $pcb_details
     */
    protected function getInvoiceSubTotal($dochdrKey, &$document, &$pcb_details)
    {
        $pcb_details['TAXES'] = $pcb_details['CHARGES'] = $pcb_details['DISCOUNTS'] = $document['TAX_TOTAL'] = $document['CRG_TOTAL'] = $document['DIS_TOTAL'] = 0;
        $query = "WITH SUB_TOTALS AS (
                    SELECT DOCHDR.CNY#,DOCHDRSUBTOTALS.DOCHDRKEY,DOCHDR.PROJECTCONTRACTKEY,DOCPARSUBTOTAL.ISTAX,DOCPARSUBTOTAL.DISC_CHARGE, SUM(NVL(DOCHDRSUBTOTALS.TOTAL,0)) TOTAL
                    FROM DOCPARSUBTOTALMST DOCPARSUBTOTAL, DOCHDRSUBTOTALSMST DOCHDRSUBTOTALS,DOCHDRMST DOCHDR, DOCPARMST DOCPAR
                    WHERE DOCHDR.CNY# = :1 and  DOCPARSUBTOTAL.CNY#= :1 AND DOCHDRSUBTOTALS.DOCHDRKEY= :2 and DOCHDR.PROJECTCONTRACTKEY= :3 and
                            DOCHDRSUBTOTALS.CNY# = DOCPARSUBTOTAL.CNY#
                      AND DOCHDR.CNY# = DOCPARSUBTOTAL.CNY# AND DOCPAR.CNY# = DOCPARSUBTOTAL.CNY#
                      AND DOCPARSUBTOTAL.RECORD# = DOCHDRSUBTOTALS.DOCPARSUBTOTALKEY
                      AND DOCHDRSUBTOTALS.DOCHDRKEY = DOCHDR.RECORD#
                      AND DOCPAR.RECORD# = DOCHDR.DOCPARKEY AND DOCPAR.ENABLE_CONTRACT_BILLING = 'T'
                      AND DOCPAR.AR_POSTING_METHOD = 'Addition' AND DOCPAR.UPDGL = 'A'
                    GROUP BY DOCHDR.CNY#,DOCHDRSUBTOTALS.DOCHDRKEY,DOCHDR.PROJECTCONTRACTKEY,DOCPARSUBTOTAL.ISTAX,DOCPARSUBTOTAL.DISC_CHARGE
                  )
                  SELECT NVL(SUM(CASE WHEN S.ISTAX =  'T' AND S.DISC_CHARGE = 'CHARGE' THEN S.TOTAL END), 0) TAX_TOTAL
                        ,NVL(SUM(CASE WHEN S.ISTAX <> 'T' AND S.DISC_CHARGE = 'DISC' THEN S.TOTAL END), 0) DIS_TOTAL
                        ,NVL(SUM(CASE WHEN S.ISTAX <> 'T' AND S.DISC_CHARGE = 'CHARGE' THEN S. TOTAL END), 0) CRG_TOTAL
                  FROM SUB_TOTALS S WHERE S.ISTAX IN ('T', 'F') AND S.DISC_CHARGE IN ('CHARGE', 'DISC')
                  GROUP BY S.CNY#, S.DOCHDRKEY ,S.PROJECTCONTRACTKEY";

        $result = QueryResult([$query, GetMyCompany(), $dochdrKey, $document['PROJECTCONTRACTKEY']]);
        if($result && !empty($result[0])){
            $result = $result[0];
            $document['TAX_TOTAL'] = $result['TAX_TOTAL'];
            $document['CRG_TOTAL'] = $result['CRG_TOTAL'];
            $document['DIS_TOTAL'] = $result['DIS_TOTAL'];
        }
    }

    /**
     * Set invoice count
     * @param $document
     */
    protected function setInvoiceCount(&$document){
         $query = "select count(*) INV_CNT from DOCHDR where cny#=:1 and projectcontractkey=:2";

        $result = QueryResult([$query, GetMyCompany(), $document['PROJECTCONTRACTKEY']]);
        if($result && !empty($result[0])){
            $document['INVOICE_COUNT'] = (int)$result[0]['INV_CNT'];
        }


    }

    /**
     * Calculate Retainage price
     * @param $document
     * @param $pclKey
     * @param $pcb_details
     * @param $lineValue
     */
    protected function getRetainagePrice(&$document, $pclKey, &$pcb_details, $lineValue = [])
    {
        $pcKey = $document['PROJECTCONTRACTKEY'];
        if(!isset($this->retainagePriceCache[$pcKey])){
            $query1 = "WITH PRRECORDS_HELD AS (
                    SELECT PRENTRY.CNY#,PRENTRY.PROJECTCONTRACTKEY,PRENTRY.PROJECTCONTRACTLINEKEY,SUM(NVL(PRENTRY.TRX_AMOUNTRETAINED, 0)) TRX_AMOUNTRETAINED
                    FROM PRENTRY
                             JOIN PRRECORD ON PRENTRY.CNY# = PRRECORD.CNY# AND PRENTRY.RECORDKEY = PRRECORD.RECORD#
                    WHERE PRENTRY.CNY#=:1 AND PRRECORD.CNY# = :1
                          AND PRENTRY.PROJECTCONTRACTKEY=:3 AND PRRECORD.DESCRIPTION2 in (select DOCID from DOCHDR where (WHENPOSTED <= :2 OR RECORD# = :4 ) and CNY# = :1 and PROJECTCONTRACTKEY = :3)
                          AND ((PRENTRY.ISRETAINAGERELEASE = 'F' OR PRENTRY.ISRETAINAGERELEASE IS NULL) OR (PRENTRY.ISRETAINAGERELEASE = 'T' AND PRENTRY.TRX_AMOUNTRETAINED IS NULL))        
                    GROUP BY PRENTRY.CNY#,PRRECORD.DESCRIPTION2,PRENTRY.PROJECTCONTRACTKEY,PRENTRY.PROJECTCONTRACTLINEKEY
                ) SELECT PROJECTCONTRACTLINEKEY, SUM(NVL(TRX_AMOUNTRETAINED, 0)) RETAINAGE_HELD_TO_DATE FROM PRRECORDS_HELD GROUP BY CNY#,PROJECTCONTRACTKEY,PROJECTCONTRACTLINEKEY";
            $result = QueryResult([$query1, GetMyCompany(), $document['BILLTHROUGHDATE'], $document['PROJECTCONTRACTKEY'], $pcb_details['DOCHDRKEY'] ?? 0]);
            $data = [];
            foreach($result as $res){
                $data[$res['PROJECTCONTRACTLINEKEY']] = $res;
            }
            $this->retainagePriceCache[$pcKey] = $data;
        }

        $result = $this->retainagePriceCache[$pcKey][$pclKey] ?? [];

        $pcb_details['RETHELDTODATE'] = ($document['STATE'] == 'Draft') ? ($lineValue['TRX_AMOUNTRETAINED'] ?? 0) : 0;
        if (!empty($result)) {
            $pcb_details['RETHELDTODATE'] = ibcadd($pcb_details['RETHELDTODATE'], $result['RETAINAGE_HELD_TO_DATE']);
        }
        $pcb_details['RETBILLTODATE'] = ($document['STATE'] == 'Draft')? ($lineValue['RETAINAGETOBILL'] ?? 0) : 0;

        if ((!empty($document['INVOICE_COUNT']) && $document['INVOICE_COUNT'] > 1) || $document['MANUAL_RET_REL'] == 'true') {
            if(!isset($this->retainageBilledToDateCache[$pcKey])){
                $query2 = "WITH RBTD AS (
                        SELECT 
                            RET_ENTRY.CNY#, RET_ENTRY.PROJECTCONTRACTKEY, RET_ENTRY.PROJECTCONTRACTLINEKEY, SUM(NVL(RET_ENTRY.AMOUNTRELEASED, 0)) RET_AMOUNTRELEASED
                        FROM 
                            RETAINAGERELEASEENTRY RET_ENTRY
                                 JOIN RETAINAGERELEASE RET
                                      ON RET_ENTRY.CNY# = RET.CNY# AND RET_ENTRY.RRKEY = RET.RECORD# AND RET.RECORDTYPE = 'ri' AND RET.STATE in ('R', 'V')
                                 JOIN PRRECORD
                                      ON RET_ENTRY.CNY# = PRRECORD.CNY# AND RET_ENTRY.RELEASED_PRRECORDKEY = PRRECORD.RECORD#
                        WHERE RET_ENTRY.CNY# = :1 and RET.CNY# = :1
                          AND RET_ENTRY.PROJECTCONTRACTKEY = :2
                          AND (RET.GLPOSTINGDATE <= :3 OR RET.DESCRIPTION IN (SELECT DOCID FROM DOCHDR WHERE CNY# = :1 AND RECORD# = :4)) 
                        GROUP BY RET_ENTRY.CNY#, PRRECORD.DESCRIPTION2, RET_ENTRY.PROJECTCONTRACTKEY, RET_ENTRY.PROJECTCONTRACTLINEKEY, RET.GLPOSTINGDATE, PRRECORD.RETAINAGEINVTYPE
                    )
                    SELECT PROJECTCONTRACTLINEKEY, SUM(NVL(RET_AMOUNTRELEASED, 0)) RETAINAGE_BILLED_TO_DATE FROM RBTD GROUP BY CNY#,PROJECTCONTRACTKEY,PROJECTCONTRACTLINEKEY";
                $result = QueryResult([$query2, GetMyCompany(), $document['PROJECTCONTRACTKEY'],  $document['BILLTHROUGHDATE'],$pcb_details['DOCHDRKEY'] ?? 0]);
                $data = [];
                foreach ($result as $res){
                    $data[$res['PROJECTCONTRACTLINEKEY']] = $res;
                }

                $this->retainageBilledToDateCache[$pcKey] = $data;
            }

            $result = $this->retainageBilledToDateCache[$pcKey][$pclKey] ?? [];

            if($document['MANUAL_RET_REL'] == 'true' && $document['WHENPOSTED'] > $document['BILLTHROUGHDATE']){
                $pcb_details['RETBILLTODATE'] = $lineValue['TRX_AMOUNT'] ?? 0;
            }

            if (!empty($result)) {
                $pcb_details['RETBILLTODATE'] = ibcadd($pcb_details['RETBILLTODATE'], $result['RETAINAGE_BILLED_TO_DATE']);
            }
        }
        $pcb_details['RETBALTODATE']= ibcsub($pcb_details['RETHELDTODATE'], $pcb_details['RETBILLTODATE']);
        $pcb_details['PREVRETBALANCE'] = ibcsub($pcb_details['RETBALTODATE'], $pcb_details['NETCHANGERETHELD']);
    }

    /***
     * Delete PCB Invoice detail records
     * @param $dochdrKey
     */
    protected function deletePCBInvoice($dochdrKey){
        //Delete from PCB invoice details
        ExecStmt(["DELETE FROM PCB_INVOICE_DETAILS WHERE CNY#= :1 AND DOCHDRKEY = :2",GetMyCompany(), $dochdrKey]);
    }

    /**
     * Delete manual retainage release PCB Invoice detail records
     * @param $prRecordKey
     */
    protected function deleteManualRetainageReleasePCBInvoice($prRecordKey){
        //Delete from PCB invoice details
        ExecStmt(["DELETE FROM PCB_INVOICE_DETAILS WHERE CNY#= :1 AND PRRECORDKEY = :2",GetMyCompany(), $prRecordKey]);
    }

    /**
     * Handle PCB Invoice Summary Save
     * Insert or update
     * @param array  $document
     * @param string $objectKey
     *
     * @return bool
     */
    public function handleSummarySave(array &$document, string $objectKey):bool {
        $ok = false;
        $summaryRecord = $this->getPCBSummaryByDoc(['RECORDNO' => $document[$objectKey]],$objectKey);
        if(!empty($summaryRecord['PCBINVOICESUMMARY']['RECORDNO'])){
            $document['PCB_SUMMARY_RECORDNO'] = $summaryRecord['PCBINVOICESUMMARY']['RECORDNO'];
            $ok = true;
        }
        else{
            $pcbinvsummary = Globals::$g->gManagerFactory->getManager('pcbinvsummary');
            $ok = $pcbinvsummary->regularAdd($document);
        }
        return $ok;
    }

    /***
     * Reset calculations fields
     * @param $pcb_details
     */
    protected function resetCalculatedFields(&$pcb_details){
        $resetFields = ['TCAPMADDITION','TCAPMDEDUCTION','TCATMADDITION','TCATMDEDUCTION','ORIGINALCONTRACTAMOUNT','TNACHANGES','TRCLAMOUNT','COMPPRIORAPP','COMPTHISPERIOD','STOREDMATERIALS','TCTODATE','PERCCOMPTODATE','INVAMOUNTRETAINED','INVRETAINAGEBILLED','RETAINAGEPERCENTAGE','NETCHANGERETHELD','RETHELDTODATE','RETBILLTODATE','RETBALTODATE','PREVRETBALANCE','TOTALBILLED','TAXES','CHARGES','DISCOUNTS'];
        foreach($resetFields as $field){
            $pcb_details[$field] = 0;
        }
    }

    /**
     * Calulate blance to finish and due amount
     * @param array $pcb_details
     */
    protected function calculateDueTotal(array &$pcb_details){
        if(!empty($pcb_details)){
            $pcb_details['BALANCETOFINISH'] =  ibcadd(ibcsub($pcb_details['TRCLAMOUNT'], $pcb_details['TCTODATE']), $pcb_details['RETBALTODATE']);
            $pcb_details['AMOUNTDUE'] =  ibcadd(ibcsub(ibcadd($pcb_details['COMPTHISPERIOD'], $pcb_details['STOREDMATERIALS']), $pcb_details['INVAMOUNTRETAINED']), $pcb_details['RETBILLTODATE']);
        }
    }

    /**
     * Get last billed invoice key for teh project contract line
     * @param $document
     * @param $projectcontractlinekey
     */
    protected function getLastBilledInvoiceKey($document, $projectcontractlinekey){
        $lastBilledKey = 0;
        $query = "SELECT 
                    DISTINCT(DOCENTRY.DOCHDRKEY) DOCHDRKEY 
                    FROM DOCENTRY JOIN DOCHDR ON DOCENTRY.CNY# = DOCHDR.CNY# AND DOCENTRY.DOCHDRKEY = DOCHDR.RECORD# AND DOCHDR.PROJECTCONTRACTKEY= :2
                    WHERE DOCENTRY.CNY#= :1 AND DOCHDR.STATE != 'I' AND DOCENTRY.PROJECTCONTRACTKEY= :2 AND DOCENTRY.PROJECTCONTRACTLINEKEY= :3
                    AND DOCHDR.WHENPOSTED <= TO_DATE(:4, 'MM/DD/YYYY') ORDER BY DOCENTRY.DOCHDRKEY DESC FETCH FIRST 1 ROW ONLY";

        $result = QueryResult([$query, GetMyCompany(), $document['PROJECTCONTRACTKEY'], $projectcontractlinekey, $document['BILLTHROUGHDATE']]);
        if ($result && !empty($result[0])) {
            $lastBilledKey = $result[0]['DOCHDRKEY'] ?? 0;
        }
        return $lastBilledKey;
    }

    /**
     * set Fields info
     * @return void
     */
    public function setFieldsInfo() : void
    {
        $this->allFields = $this->getAllFieldsInfo();
        foreach($this->allFields as $key => $field){
            if ( $field['type']['type'] == 'currency' ) {
                $this->numFields[] = $key;
            } elseif ($field['type']['type'] == 'date') {
                $this->dateFields[] = $key;
            }
        }
    }

    /**
     * getFieldsbyType method to get fields based on type
     * @param string $type
     *
     * @return array
     */
    public function getFieldsbyType(string $type = '') : array
    {
        return match ( $type ) {
            'currency' => $this->numFields,
            'date' => $this->dateFields,
            default => $this->allFields,
        };
    }

    /**
     * @param $values
     *
     * @return array
     */
    protected function getfieldsType($values){
        $schema = self::StructuredToFlat($this->_schemas[$this->_entity]['schema']);
        $schemaFlip = array_flip($schema);
        $fieldType = [];
        foreach ($values as $key => $value){
            if(isset($schemaFlip[$key])){
                $fieldInfo  = $this->GetFieldInfo($schemaFlip[$key]);
                $type = empty($fieldInfo['type']['type']) ? 'text' : $fieldInfo['type']['type'];
                switch ($type){
                    case $type == 'decimal' || $type == 'currency' || $type == 'integer' :
                        $dataType = 'integer';
                        break;
                    case $type == 'date' || $type == 'timestamp' :
                        $dataType = 'date';
                        break;
                    case $type == 'percent' || $type == 'decimal'|| $type == 'currency' :
                        $dataType = 'decimal';
                        break;
                    default:
                        $dataType = 'text';
                }
            }elseif(in_array($key, ['whenmodified', 'whencreated'])) {
                $dataType = 'date';
            }elseif(in_array($key, ['modifiedby', 'createdby', 'cny#'])) {
                $dataType = 'integer';
            }else{
                $dataType = 'text';
            }
            $fieldType[$key] = $dataType;

        }
        return $fieldType;

    }

    /**
     * @param array  &$values
     *
     * @param string $action
     */
    function updateAuditColumns(&$values, $action){
        if ($this->hasAuditColumns()) {
            $currentUserRec = GetMyUserid();
            $values['whenmodified'] = GetCurrentDate();
            $values['modifiedby'] = $currentUserRec;
            if($action == 'add'){
                $values['whencreated'] = GetCurrentDate();
                $values['createdby'] = $currentUserRec;
            }
        }
    }

    /**
     * @param array $values
     *
     */
    protected function setColumnsName($values){
        if(empty($this->bulkStmtColumnsName)){
            $this->bulkStmtColumnsName = array_keys($values);
        }

    }

    /**
     * @return array|int[]|string[]
     */
    protected function getColumnsName(){
        return $this->bulkStmtColumnsName ?? [];
    }

    /**
     * @param $values
     */
    protected function setArgsType($values){
        if(empty($this->argsType)){
            $this->argsType = array_values($this->getfieldsType($values));
        }
    }

    /**
     * @return array
     */
    protected function getArgsType(){
        return $this->argsType ?? [];
    }

    /**
     * @param $schemaObject
     * @param $values
     *
     * @return array
     */
    protected function mapValuesToSchemaObject($schemaObject, $values){
        $data = [];
        foreach($schemaObject as $path){
            $data[$path] = "";
            if(isset($values[$path])){
                $data[$path] = $values[$path];
            }
        }
        return $data;
    }

    /**
     * @param $bulkInsertData
     *
     * @return bool
     */
    protected function processBulkInsert($bulkInsertData){
        $owner = "PCBInvDetailManager::processBulkInsert";
        XACT_BEGIN($owner);
        $bulkArr = [];
        $bulkArr[0] = $this->getBulkInsertStmt();
        $bulkArr[1] = $bulkInsertData;
        $bulkArr[2] = $this->getArgsType();
        $ok = ExecBulkStmt($bulkArr);
        $ok = $ok && XACT_COMMIT($owner);
        if (!$ok) {
            XACT_ABORT($owner);
        }
        return $ok;
    }

    /**
     * @return string
     */
    protected function getBulkInsertStmt(){
        $tableName = strtoupper($this->getTable());
        $columnsName = implode(", ", $this->getColumnsName());
        $rowIndexs = $this->getNoOfRowsForBulkInsert();
        $stmt = "INSERT INTO $tableName ($columnsName) VALUES ($rowIndexs)";
        return $stmt;
    }

    /**
     * @return string
     */
    protected function getNoOfRowsForBulkInsert(){
        $columns = $this->getColumnsName();
        array_unshift($columns,"");
        unset($columns[0]);
        return ":".implode("(i),:",array_keys($columns))."(i)";
    }

    /**
     * Deleting duplicate created row in pcbinvsummary if found more than once. Later in process it will create fresh row.
     */
    protected function deleteManualRetainageReleasePCBSummaryIfDuplicate(string $prRecordKey): void {
        $pcbManager = Globals::$g->gManagerFactory->getManager('pcbinvsummary');
        $params = [
            'filters' => [
                [
                    [ 'PRRECORDKEY', '=', $prRecordKey ],
                    [ 'CNY#', '=', GetMyCompany() ]
                ]
            ]
        ];
        $summary = $pcbManager->GetList($params);
        if (count($summary) > 1) {
            ExecStmt(["DELETE FROM PCB_INVOICE_SUMMARY WHERE CNY#= :1 AND PRRECORDKEY = :2",GetMyCompany(), $prRecordKey]);
        }
    }
}
