<?php

/**
 * Editor class for the SRE Tenants object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Sage Intacct Corporation, All Rights Reserved
 */
class TenantsEditor extends FormEditor
{

    /**
     * @param array $obj object metadata
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        if ( CRESetupManager::isSIREEnabled() && $this->verb == 'create' ) {
            $url = FwdUrl(
                "sre.phtml?.sess=" . Session::getKey() . "&.op=" . Request::$r->_op."&.target=tenant-setup"
            );
            Globals::$g->gURLs->Redirect($url);
        }
    }
}
