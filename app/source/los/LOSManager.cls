<?php
/**
 *   FILE: LOSManager.cls
 *   AUTHOR: <PERSON>
 *   DESCRIPTION: This file contains code for managing los logic
 *
 *   (C) 2014, Intacct Corporation, All Rights Reserved
 *
 *   This document contains trade secret data that belongs to Intacct
 *   Corporation and is protected by the copyright laws.  Information
 *   herein may not be used, copied or disclosed in whole or in part
 *   without prior written consent from Intacct Corporation.
 */

class LOSManager
{
    const INSUFFICIENT_QUOTA_MESSAGE = "Insufficient quota for running transaction. Please contact Intacct Support.";
    const EXPIRING_QUOTA_MESSAGE = "The transaction quota for the plan is about to expire. Please contact Intacct Support.";
    //this is done to reduce the number of calls to CacheClient::getInstance();

    /**
     * @var CacheClient|null $memcacheClient
     */
    private static $memcacheClient = null;

    /**
     * @return CacheClient
     */
    private static function getMemcacheClient()
    {
        if ( self::$memcacheClient == null) {
            return self::$memcacheClient = CacheClient::getInstance();
        } else {
            return self::$memcacheClient;
        }
    }

    /**
     * get the LOS Profile for the company provided. This code checks the memcache
     * for the stored profile and returns a LOSProfile object
     *
     * @param int    $company
     * @param string $processLogType
     * @param string $clientType
     *
     * @return LOSProfile
     */
    public static function getLOSProfileForCompany($company, $processLogType, $clientType)
    {
        //get the LOS Subscription Plan here for the company and create a profile for company
        //we can get the profile from Db for memcache. which is populated by the backend job
        $losSubscriptionPlan = new LOSPlan($company);
        $losSubscriptionPlan->load();
        $losProfile = new LOSProfile($clientType, LOSProfile::LOS_PROFILE_BEGIN, $company, $losSubscriptionPlan,
            $processLogType);
        return $losProfile;
    }

    /**
     * @param int  $company
     * @param int  $currentCount
     * @param int  $monthlyCount
     * @param bool $countAllTransactions
     */
    public static function getTransactionCountForCompanyFromDB(
        $company,
        &$currentCount,
        &$monthlyCount,
        $countAllTransactions = false
    ) {
        $transactionTypeAppend = "";
        if (LOSPlan::$transactionTypesToThrottle == null) {
            LOSPlan::loadTransactionTypeToThrottle();
        }

        if (LOSPlan::$transactionTypesToThrottle != null && $countAllTransactions == false) {
            $addComma = false;
            foreach ( LOSPlan::$transactionTypesToThrottle as $value) {
                if ($addComma == true) {
                    $transactionTypeAppend = $transactionTypeAppend . ",";
                }
                $transactionTypeAppend = $transactionTypeAppend . "'" . $value . "'";
                $addComma = true;
            }
        }

        $result = [];
        $losConfiguration = GetValueForIACFGProperty('LOS_CONFIGURATION');
        if ($losConfiguration['LOS_TRX_LIMIT_ENABLED'] != 0) {
            $result = self::queryDatabaseForLOSCounts($company, false, $transactionTypeAppend );
        }

        if (count($result) > 0) {
            $currentCount = $result[0]['DAILY_SUM'];
            $monthlyCount = $result[0]['TOTAL_TRANS'];
        } else {
            //this condition should not happen
            $currentCount = 0;
            $monthlyCount = 0;
        }
    }

    /**
     * we need to get the total count of all transaction used in calculating the overall usage
     * for sending alerts to the customer.
     *
     * @param int $company
     *
     * @return int
     */
    public static function getMonthlyCountForNonThrottledTransactions($company)
    {
        $summaryList = [];
        $losConfiguration = GetValueForIACFGProperty('LOS_CONFIGURATION');
        if ($losConfiguration['LOS_TRX_LIMIT_ENABLED'] != 0) {
            $summaryList = self::queryDatabaseForLOSCounts($company, true );
        }

        if (LOSPlan::$transactionTypesToThrottle == null) {
            LOSPlan::loadTransactionTypeToThrottle();
        }
        $monthlyCount = 0;

        if (count($summaryList) > 0) {
            foreach ( $summaryList as $value) {
                $cType = $value['CLIENT_TYPE'];
                $tCount = $value['DAILY_SUM'];
                if (in_array($cType, LOSPlan::$transactionTypesToThrottle) == false) {
                    $monthlyCount = $monthlyCount + $tCount;
                }
            }
        }
        return $monthlyCount;
    }

    /**
     * This is efficient call compared to using v_los_transaction_summary. This implementation
     * will limit the number of rows in the union all section as compared to running against all
     * the rows in the two tables i.e. xml_partnerlog and web_services_log
     *
     * @param string      $company
     * @param bool|string $groupByClientType
     * @param string      $transactionTypeAppend
     *
     * @return array|false
     */
    private static function queryDatabaseForLOSCounts($company, $groupByClientType = false, $transactionTypeAppend = '')
    {
        $clientTypeAddition = "";
        if ($groupByClientType == true) {
            $clientTypeAddition = "client_type,";
        }
        $filterByClientType = "";
        if ($transactionTypeAppend != "") {
            $filterByClientType = " and client_type in (" . $transactionTypeAppend . ")";
        }

        $qry = "select distinct total_trans, cny#, daily_sum, $clientTypeAddition trunc(SYSDATE,'DD') from
                (select
                        sum(n_trans) over (partition by cny#) as total_trans,
                        cny#,
                        $clientTypeAddition
                        sum(case when created_date between trunc(SYSDATE,'DD') and trunc((SYSDATE + 1),'DD') then n_trans else 0 end)
                            over (partition by  $clientTypeAddition cny#) as daily_sum
                from (
                    (select
                        cny#,
                        1 as n_trans ,
                        $clientTypeAddition
                        trunc(created, 'DD') as created_date
                    from
                        xml_partnerlog
                    where
                        cny# = :1
                        and created between TRUNC (SYSDATE, 'month') and Last_day(sysdate) + 1
                        and function <> 'getAPISession' $filterByClientType
                    )
                union all
                    (select
                        cny#,
                        no_of_transaction as n_trans,
                        $clientTypeAddition
                        trunc(start_time, 'DD') as created_date
                        from web_services_log
                    where
                        cny# = :1
                        and start_time between TRUNC (SYSDATE, 'month') and Last_day(sysdate) + 1 $filterByClientType
                    )
                )
            )";
        $result = QueryResult(array($qry, $company));
        return $result;
    }

    /**
     * @param string            $company
     * @param array|string|null $currentCount
     * @param array|string|null $monthlyCount
     *
     * @return string|string[]|mixed|null
     */
    public static function getCurrentTransCountForCompany($company, &$currentCount = null, &$monthlyCount = null)
    {
        self::getLOSMemcacheKeys($company, $currentDateKey, $currentMonthKey);
        $memcacheClient = self::getMemcacheClient();
        //force a db fetch
        //$memcacheClient->delete($currentDateKey);
        //$memcacheClient->delete($currentMonthKey);
        $currentCount = $memcacheClient->get($currentDateKey);
        $monthlyCount = $memcacheClient->get($currentMonthKey);
        if (LOS_DEBUG) {
            logFL("Memcache current count = $currentCount and monthly count = $monthlyCount");
        }
        if (isset($currentCount) && $currentCount != false) {
            return $currentCount;
        } else {
            //get the count from the database
            $dbCounter = self::populateMemcacheWithDBValues(array( $company, null));
            $currentCount = $dbCounter['CURRENT_COUNT'];
            $monthlyCount = $dbCounter['MONTHLY_COUNT'];
            if (LOS_DEBUG) {
                logFL("DB current count = $currentCount and monthly count = $monthlyCount will be set in memcache");
            }
            return null;
        }
    }

    /**
     * @param array $functionArguments
     *
     * @return array
     */
    public static function populateMemcacheWithDBValues($functionArguments)
    {
        $result = array();
        $currentCount = null;
        $monthlyCount = null;

        $company = $functionArguments[0];
        $memcacheExpiration = is_numeric($functionArguments[1] ?? null) ? (int) $functionArguments[1] : CacheClient::DEFAULT_EXPIRATION;

        self::getTransactionCountForCompanyFromDB($company, $currentCount, $monthlyCount);
        $result['CURRENT_COUNT'] = $currentCount;
        $result['MONTHLY_COUNT'] = $monthlyCount;
        self::setMemcacheCounters($company, $currentCount, $monthlyCount, true, $memcacheExpiration, false, true);
        return $result;
    }

    /**
     * @param int  $company
     * @param int  $dayCountToSet
     * @param int  $monthlyCountToSet
     * @param bool $isIncrement
     * @param int  $memcacheExpiration
     * @param bool $doCallBack
     * @param bool $resetValue
     */
    public static function setMemcacheCounters(
        $company,
        $dayCountToSet,
        $monthlyCountToSet,
        /** @noinspection PhpUnusedParameterInspection */ $isIncrement,
        $memcacheExpiration = CacheClient::DEFAULT_EXPIRATION,
        $doCallBack = false,
        $resetValue = false
    ) {
        self::getLOSMemcacheKeys($company, $currentDateKey, $currentMonthKey);
        $memcacheClient = self::getMemcacheClient();
        $callback = null;
        $callbackArgs = null;
        if ($doCallBack == true) {
            $callback = "LOSManager::populateMemcacheWithDBValues";
            $callbackArgs = array(array($company, 600));
        }
        //we need to reset the values when the count is fetch from the DB 
        //this is needed to make sure that the interlockedIncrement will not double the 
        //memcache values
        if ($resetValue == true) {
            $memcacheClient->delete($currentDateKey);
            $memcacheClient->delete($currentMonthKey);
        }

        //if ( $isIncrement ) {
        $memcacheClient->interlockedIncrement($currentDateKey, $memcacheExpiration, $dayCountToSet, true, $callback,
            $callbackArgs);
        $memcacheClient->interlockedIncrement($currentMonthKey, $memcacheExpiration, $monthlyCountToSet, true,
            $callback, $callbackArgs);
        //} else {
        //    $memcacheClient->interlockedDecrement($currentDateKey, $memcacheExpiration, $dayCountToSet, true, $callback, $callbackArgs);
        //    $memcacheClient->interlockedDecrement($currentMonthKey, $memcacheExpiration, $monthlyCountToSet, true, $callback, $callbackArgs);
        //}
    }

    /**
     * @param LOSProfile $losProfile
     * @param string     $notificationType
     * @param bool       $saveCompanyProfile
     *
     * @return bool
     */
    public static function isClientNotified($losProfile, $notificationType, $saveCompanyProfile = true)
    {
        $clientNotified = false;
        $gCompanyPrefs = array();
        GetCompanyPreferences($gCompanyPrefs, $losProfile->getCompany());
        $setPreferences = false;
        $notificationPreference = array();

        if ($notificationType == 'BY_DATE') {
            //current date in YYYY\MM\DD
            $currentDate = GetCurrentDate();
            //these are the last notification values
            $notifiedDate = $gCompanyPrefs['LOS_LAST_DAY_NOTIFIED'];
            if (isset($notifiedDate) && $notifiedDate == $currentDate) {
                $clientNotified = true;
                if (LOS_DEBUG) {
                    logFL('Client already notified for this date ' . $currentDate);
                }
            } else {
                $notificationPreference['LOS_LAST_DAY_NOTIFIED'] = $currentDate;
                $setPreferences = true;
            }
        } else {
            if ($notificationType == 'BY_MONTH') {
                //current date in YYYY/MM/DD
                $currentDate = GetCurrentDate();
                $cMonth = GetMonthFromDate($currentDate);
                $cYear = GetYearFromDate($currentDate);

                //current month in YYYY/MM
                $currentMonth = $cYear . '/' . $cMonth;
                $notifiedMonth = $gCompanyPrefs['LOS_LAST_MONTH_NOTIFIED'];
                if (isset($notifiedMonth) && $notifiedMonth == $currentMonth) {
                    $clientNotified = true;
                    if (LOS_DEBUG) {
                        logFL('Client already notified for this month ' . $currentMonth);
                    }
                } else {
                    $setPreferences = true;
                    $notificationPreference['LOS_LAST_MONTH_NOTIFIED'] = $currentMonth;
                }
            }
        }
        if ($setPreferences == true && $saveCompanyProfile == true) {
            SetCompanyPreferences('@' . $losProfile->getCompany(), $notificationPreference);
        }

        return $clientNotified;
    }

    /**
     * @param string $company
     * @param string $currentDateKey
     * @param string $currentMonthKey
     */
    public static function getLOSMemcacheKeys($company, &$currentDateKey, &$currentMonthKey)
    {
        $currentDate = GetCurrentDate();
        $currentMonth = substr($currentDate, 0, 5);
        $currentDateKey = $company . "_LOS_" . $currentDate;
        $currentMonthKey = $company . "_LOS_" . $currentMonth;
    }

    /**
     * The main operation of finalize Profile is to save the process logs
     *
     * @param LOSProfile $losProfile
     */
    public static function finalizeLOSProfile(&$losProfile)
    {
        if ($losProfile->isLOSProfileFinalized()) {
            return;
        }
        $losProfile->finalizeLOSProfile();

        WebServicesLogManager::saveWebServicesLogFromLOSProfile($losProfile);
        unset($losProfile);
    }
}

