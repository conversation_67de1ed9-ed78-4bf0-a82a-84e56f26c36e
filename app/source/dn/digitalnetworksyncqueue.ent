<?php
//=============================================================================
//
//	FILE:			 digitalnetworksyncqueue.ent
//	<AUTHOR> C <<EMAIL>>s
//	DESCRIPTION:	 digitalnetworksyncqueue entity file
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================
global $kSchemas;

require 'fifodispatcherqueue.ent';
$kSchemas['digitalnetworksyncqueue'] = $kSchemas['fifodispatcherqueue'];
$kSchemas['digitalnetworksyncqueue']['ownedobjects'] =  [
    [
        'fkey' => DigitalNetworkSyncQueueHistoryManager::DISPATCHERQUEUEKEY,
        'invfkey' => 'RECORDNO',
        'entity' => 'digitalnetworksyncqueuehistory',
        'skipOwnedObjectWithNoValues' => true, // Skip owned object if it has no values
        'path' => 'DIGITALNETWORKSYC'
    ]
];
$kSchemas['digitalnetworksyncqueue']['upsertEntriesPaths'] = ['DIGITALNETWORKSYC'];
