<?php

//=============================================================================
//
//	FILE:			 DNItemCrossRefCommonEventHandler.cls
//	<AUTHOR> Hebbar <<EMAIL>>
//	DESCRIPTION:	 Common Event Handler for Item Cross Ref Add/Set DN Sync
//
//
//=============================================================================

abstract class DNItemCrossRefCommonEventHandler extends DNEventHandler
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param string $guid
     * @param string $itemCrossRefRecord
     *
     */
    protected function buildItemCrossRefCompanyVisibility($guid, $itemCrossRefRecord)
    {
        //Populated Item Cross Ref at Top level
        $topExternalID = $guid . '_0';
        $this->setExternalIDMap($itemCrossRefRecord, $topExternalID);

        //GetList call on entity details
        $allEntities = EntityManager::GetListQuick('locationentity',
            ['RECORDNO']
        );

        //Populated Item Cross Ref at all entity level
        // Loop on each entity and add event SUBSCRIBE_ENTITY
        foreach ($allEntities as $entity) {
            $externalid = $guid . '_' . $entity['RECORDNO'];
            $this->setExternalIDMap($itemCrossRefRecord, $externalid);
        }
    }
}