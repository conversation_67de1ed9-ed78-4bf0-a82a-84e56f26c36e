<?php

/**
 * Event Handler for Delete GL Account
 *
 * <AUTHOR> <ravi.shan<PERSON><PERSON><PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2022 Intacct Corporation, All Rights Reserved
 */

class DNDeleteGLAccountEventHandler extends DNDeleteCommonEventHandler
{
    public function __construct()
    {
       parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr()
    {
        return '_GLACCOUNT_';
    }

    /**
     * @return string
     */
    protected function getDimType()
    {
        return DNEventHandlerConstaint::GLACCOUNT_TYPE;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName()
    {
        return DNEventHandlerConstaint::GLACCOUNTDELETED_EVENT;
    }

    /**
     * @return string
     */
    protected function getObjectIDField()
    {
        return 'ACCT_NO';
    }
}