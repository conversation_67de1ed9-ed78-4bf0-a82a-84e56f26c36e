<?php

/**
 * Event Handler for Delete Department
 *
 * <AUTHOR> <ravi.shankar<PERSON><PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2022 Intacct Corporation, All Rights Reserved
 */

class DNDeleteDeptEventHandler extends DNDeleteCommonEventHandler
{
    public function __construct()
    {
       parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr()
    {
        return '_DEPARTMENT_';
    }

    /**
     * @return string
     */
    protected function getDimType()
    {
        return DNEventHandlerConstaint::DEPT_TYPE;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName()
    {
        return DNEventHandlerConstaint::DEPTDELETED_EVENT;
    }

    /**
     * @return string
     */
    protected function getObjectIDField()
    {
        return 'dept_no';
    }
}