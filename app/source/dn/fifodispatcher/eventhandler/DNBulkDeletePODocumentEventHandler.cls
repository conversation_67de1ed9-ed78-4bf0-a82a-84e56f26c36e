<?php

/**
 * Event Handler for Bulk Delete PO Document
 *
 * FILE:			 DNBulkDeletePODocumentEventHandler.cls
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Intacct Corporation, All Rights Reserved
 */

class DNBulkDeletePODocumentEventHandler extends DNEventHandler
{

    /** @var string $orgId */
    protected $orgId;

    public function __construct()
    {
       parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr()
    {
        return '_PODOC_';
    }

    /**
     * @param array $job
     * @param array $jobDetails
     *
     * @return bool
     */
    public function processEvent(&$job, $jobDetails) : bool
    {
        $ok = true;

        $sourceDocState = [ self::PENDING_STATE,
                            self::PARTIALLY_CONVERTED_STATE,
                            self::CONVERTED_STATE,
                            self::INPROGRESS_STATE,
                            self::CLOSED_STATE
        ];

        $bulkDeleteMetric = new MetricBulkDeletePODocumentDnSync();

        $bulkDeleteMetric->startTime();
        $bulkDeleteMetric->setRecord($job['RECORDNO']);
        $bulkDeleteMetric->setAction($job['TOPIC']);
        $bulkDeleteMetric->setWhenCreated($job['WHENCREATED']);
        $bulkDeleteMetric->setCUser($job['CREATEDBY']);

        // org/compmany is nont subscribe
        if (WPBSetupManager::isSubscribed()) {

            // Begin the transaction
            XACT_BEGIN(__METHOD__);
            $startTime = time();

            if ( IsMultiEntityCompany()) {
                Database::switchToNonMegaContext();
            }

            // Company GUID
            $guid = Profile::getCompanyCacheProperty('COMPANYPREF', 'GUID');
            $bulkDeleteMetric->setGuId($guid);

            // get the max limit and start
            $max = $job['OBJECT']['limit'];
            $start = $job['OBJECT']['start'];

            if(isNullOrBlank($start)) {
                $start = "0";
            }

            if(isNullOrBlank($max)) {
                $max = DNEventHandlerConstaint::BULKSYNCLIMIT;
            }

            $bulkDeleteMetric->setStartWith($start);

            $jobLocatioContext = $this->getJobLocationContext($job);
            if(isNullOrBlank($jobLocatioContext)) {
                $bulkDeleteMetric->setLocation('0');
            } else {
                $bulkDeleteMetric->setLocation($jobLocatioContext);
            }

            $sourceDocType  = $job['OBJECT']['dataMap']['sourceDocType'];

            $docMgr = Globals::$g->gManagerFactory->getManager('podocument');

            // Build Query Spec
            $querySpec = [
                'selects'=> ['RECORDNO'],
                'start' => $start,
                'max' => $max,
                'usemst' => true,
            ];

            // Query order by
            $querySpec['orders'][0] = ['RECORDNO', 'ASC'];

            if (IsMultiEntityCompany()) {
                if(!isNullOrBlank($jobLocatioContext)){
                    $querySpec['filters'][0][] = ['MEGAENTITYKEY', '=', $jobLocatioContext];
                } else {
                    $querySpec['filters'][0][] = ['MEGAENTITYKEY', 'ISNULL' ];
                }
            }

            $querySpec['filters'][0][] = ['STATE', 'IN', $sourceDocState];

            $querySpec['filters'][0][] = ['DOCPARID', '=', $sourceDocType];

            // PO Dcoument Data Result Set
            $results = $docMgr->GetList($querySpec);
            LogToFile($job['TOPIC'] . " The total number of PO Documents fetched from backend are =>  ". count($results) . " For location => " . $jobLocatioContext);

            if (!empty($results)) {

                $externalID = $guid . '_0';
                if (!isNullOrBlank($jobLocatioContext)) {
                    $externalID = $guid . '_'.$jobLocatioContext;
                }

                foreach ($results as $result) {

                    $objData = $result;

                    //For PO Document we need to sych the data to root all the time
                    //Because we can convert the PO document from the root which is created at any entity
                    $this->setExternalIDMap($objData['RECORDNO'], $externalID);

                    // Company GUID
                    $objData['GUID'] = $guid;

                    $subjectStr = $this->getEventSubjectStr();

                    // Set Subject ID -- FORMAT - GUID_VEN_Record#
                    $subjectID = $guid . $subjectStr . $objData['RECORDNO'];

                    // set the idemPotencyKey
                    $idemPotencyKey = $guid . '_' . $job['RECORDNO'] . '_' . $job['TOPIC'];

                    // set subject
                    $this->setSubjectID($subjectID);

                    // set idempotencyKey
                    $this->setIdemPotencyKey($idemPotencyKey);

                    $objData = $this->changeToLowerCase($objData);
                    //4. Populate "companyids"
                    $objData = $this->populateExternalIDMap($objData);

                    //5. Prepare Final Sync Map
                    $syncData[] = $this->prepareEntitySyncDataMap($objData);
                }

                // Total batch count
                $syncDataCount = count($syncData);
                $bulkDeleteMetric->setHeaderCount($syncDataCount);

                $batchSynchedCount = 0;

                // Prepare batch of 10 and send to AWS Event Brige, continue till it reach end
                while ($batchSynchedCount < $syncDataCount) {
                    $batchSyncData = [];
                    // prepare batch of 10
                    for ($i = 0; $i < 10; $i++) {
                        if (!empty($syncData[$batchSynchedCount])) {
                            $batchSyncData[$i] = $syncData[$batchSynchedCount++];
                        } else {
                            break;
                        }
                    }

                    $ok = $this->putEvents($batchSyncData, $respHttpCode, $response);

                    if (isNullOrBlank($response['FailedEntryCount'])) {
                        LogToFile($job['TOPIC'] . " Failed to send PO Dcoument to DN Event Bridge => ". count($batchSyncData));
                        // batch has some issue, stop the sync
                        break;
                    } else {
                        LogToFile($job['TOPIC'] . " The total number of PO Dcoument successfully deleted to  DN =>  ". count($batchSyncData));
                    }

                    // update the $job['start']
                    $ok = $ok && $this->updateBulkSyncBatchStatus($job, count($batchSyncData));

                    if (!$ok) {
                        $errMsg = "DigitalNetworkSync Processor :: Bulk Delete PO Dcoument Batch Failed :: ";
                        Globals::$g->gErr->addError('PO-0133', __FILE__ . ':' . __LINE__, $errMsg);
                        $bulkDeleteMetric->setError($errMsg);
                        break;
                    } else {
                        $dnBatchCount = ibcadd($dnBatchCount, count($batchSyncData));
                    }
                }
                if ($ok) {
                    // set the sync status in progress
                    $this->setSyncStatus(self::STATUS_INPROGRESS);
                    $bulkDeleteMetric->setStatus(self::STATUS_INPROGRESS);
                    $bulkDeleteMetric->setBatchCount($dnBatchCount);
                }
            } else {
                // either failure or reach last
                $this->setSyncStatus(self::STATUS_COMPLETE);
                $bulkDeleteMetric->setStatus(self::STATUS_COMPLETE);
            }
            if ( IsMultiEntityCompany()) {
                Database::switchBackToMegaContext();
            }
        } else {
            $errMsg = "DigitalNetworkSync Processor :: Organization/Company not subscribed ";
            Globals::$g->gErr->addError('PO-0129', __FILE__ . ':' . __LINE__, $errMsg);
            $ok = false;
            $bulkDeleteMetric->setError(" Organization/Company not subscribed ");
        }

        $ok = $ok && XACT_COMMIT(__METHOD__);

        // Error in commit
        if ( ! $ok ) {
            XACT_ABORT(__METHOD__);
            $errMsg = "PO DigitalNetworkSync Processor :: Bulk Delete PO Dcoument Batch Failed :: ";
            Globals::$g->gErr->addError('PO-0135', __FILE__ . ':' . __LINE__, $errMsg);
            $bulkDeleteMetric->setError($errMsg);
        }

        $timeProcess = time() - $startTime;
        $bulkDeleteMetric->setTimeForProcess($timeProcess);
        $bulkDeleteMetric->stopTime();
        $bulkDeleteMetric->publish();

        return $ok;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName()
    {
        return DNEventHandlerConstaint::PODOCUMENTDELETED_EVENT;
    }

    /**
     * @return bool
     */
    protected function validateAuditFields($values): bool
    {
        return true;
    }

    /**
     * @return string
     */
    protected function getObjectIDField()
    {
        //Since it is BULK delete we just pass object
        //No need of passing RECORNO
        return 'recordno';
    }
}