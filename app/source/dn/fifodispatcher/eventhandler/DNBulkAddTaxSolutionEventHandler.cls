<?php

//=============================================================================
//
//	FILE:			 DNBulkAddTaxSolutionEventHandler.cls
//	<AUTHOR> <PERSON> <<EMAIL>>
//	DESCRIPTION:	 Common Event Handler for Tax Solution Bulk Add DN Sync
//
//
//=============================================================================

class DNBulkAddTaxSolutionEventHandler extends DNDimCommonEventHandler
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr() : string
    {
        return '_TAXSOLUTION_';
    }

    /**
     * @param array $querySpec
     * @return bool|array
     *
     */
    protected function getEventData(array $querySpec) : bool|array
    {

        $mgr = Globals::$g->gManagerFactory->getManager('taxsolution');

        $result = $mgr->GetList($querySpec);

        return $result;
    }


    /**
     * @return string
     */
    protected function getDimType() : string
    {
        return DNEventHandlerConstaint::TAXSOLUTION_TYPE;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName() : string
    {
        return DNEventHandlerConstaint::TAXSOLUTIONCREATE_EVENT;
    }

    /**
     * @return string
     */
    protected function getObjectIDField() : string
    {
        return 'solutionid';
    }
}