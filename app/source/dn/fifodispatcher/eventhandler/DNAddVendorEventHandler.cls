<?php

/**
 * Event Handler for Add Vendor
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Intacct Corporation, All Rights Reserved
 */
class DNAddVendorEventHandler extends DNVendorCustomerCommonEventHandler
{
    /** @var string $orgId */
    protected $orgId;


    public function __construct()
    {
       parent::__construct();
    }

    /**
     * @param array $job
     * @param array $jobDetails
     *
     * @return bool
     */
    public function processEvent(&$job, $jobDetails) : bool
    {

        XACT_BEGIN(__METHOD__);

        // org/compmany is nont subscribe
        if (WPBSetupManager::isSubscribed()) {
            $objData = $job['OBJECT']['dataMap'];

            $locationContext = $job['LOCATIONKEY'];

            // Company GUID
            $guid = Profile::getCompanyCacheProperty('COMPANYPREF', 'GUID');

            //Multientity and top level
            if (IsMultiEntityCompany() && !GetContextLocation()) {

                $restrictedVendor = false;
                if ($objData['OBJECTRESTRICTION'] == 'Restricted') {
                    if(!empty($objData["RESTRICTEDLOCATIONS"])) {
                        $restrictedVendor = true;
                    }
                }

                // If Restricted vendor then build the map
                if($restrictedVendor) {
                    $this->getRestrictionDetails([$objData['VENDORID']], $guid);
                }else {
                    // unrestricted or restricted to top level or restricted to department
                    $this->buildCompanyVisibility($guid, $objData['VENDORID']);
                }
            }

            // Top Level or non Mega company
            if (isNullOrBlank($locationContext)) {
                // 1. Populate Top ExternalID GUID_0
                $topExternalID = $guid . '_0';
                $this->setExternalIDMap($objData['VENDORID'], $topExternalID);
            } else {
                // 1. Populate Top ExternalID GUID_0
                $topExternalID = $guid . '_'.$locationContext;
                $this->setExternalIDMap($objData['VENDORID'], $topExternalID);
            }

            // Set Subject ID -- FORMAT - GUID_VEN_Record#
            $subjectID = $guid . '_VEN_' . $objData['RECORDNO'];

            // set the idemPotencyKey
            $idemPotencyKey = $guid . '_' . $job['RECORDNO'] . '_' . $job['TOPIC'];

            // set subject
            $this->setSubjectID($subjectID);

            // set idempotencyKey
            $this->setIdemPotencyKey($idemPotencyKey);

            // 2. Filter Black List
            $objData = $this->filterBlackListField($objData, DNEventHandlerConstaint::VENDOR_TYPE);

            // Reformat Date
            $objData = $this->reformatDateFields($objData, DNEventHandlerConstaint::VENDOR_TYPE);

            $objData = $this->changeToLowerCase($objData);

            $this->translateContactDetail($objData, DNEventHandlerConstaint::VENDOR_TYPE);

            //4. Populate "companyids"
            $objData = $this->populateExternalIDMap($objData);

            //5. Prepare Final Sync Map
            $syncData[] = $this->prepareEntitySyncDataMap($objData);

            $ok = $this->putEvents($syncData, $respHttpCode, $response);

        } else {
            $errMsg = self::$processorPrefix. " Organization/Company not subscribed ";
            Globals::$g->gErr->addError('**********', __FILE__ . ':' . __LINE__, $errMsg);
            $ok = false;
        }
        // set the ADD_ENTITY Event queue status
        $ok && $this->setSyncStatus(self::STATUS_COMPLETE);

        $ok = $ok && XACT_COMMIT(__METHOD__);

        if ( ! $ok ) {
            XACT_ABORT(__METHOD__);
            logToFileCritical(self::$processorPrefix . " Error in processing Add vendor Event");
            $errMsg = self::$processorPrefix. " Erro processing Add Vendor ";
            Globals::$g->gErr->addError('**********', __FILE__ . ':' . __LINE__, $errMsg);

        }
        return $ok;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName()
    {
        return DNEventHandlerConstaint::VENDORCREATE_EVENT;
    }

    /**
     * @return string
     */
    protected function getObjectIDField()
    {
        return 'vendorid';
    }

    /**
     * @return string
     */
    protected function getMolaObject()
    {
        return 'VENDOR';
    }
}