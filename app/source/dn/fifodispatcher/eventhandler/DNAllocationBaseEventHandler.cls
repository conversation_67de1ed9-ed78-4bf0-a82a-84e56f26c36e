<?php

//=============================================================================
//
//	FILE:			 DNAllocationBaseEventHandler.cls
//	<AUTHOR> C <<EMAIL>>
//	DESCRIPTION:	 Common Event Handler for Allocation Add/Set/Delete DN Sync
//
//
//=============================================================================

abstract class DNAllocationBaseEventHandler extends DNDimCommonEventHandler
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr()
    {
        return '_ALLOCATION_';
    }

    /**
     * @return string
     */
    protected function getDimType()
    {
        return DNEventHandlerConstaint::ALLOCATION_TYPE;
    }

    /**
     * @return string
     */
    protected function getObjectIDField(): string
    {
        return 'allocationid';
    }

    /**
     * @param array $results
     * @param array $job
     * @return void
     */
    protected function constructExternalIDMap($results, $job, $verb = ''): void
    {
        $contextLoc = $this->getJobLocationContext($job);

        $guid = Profile::getCompanyCacheProperty('COMPANYPREF', 'GUID');

        $topExternalID = $guid . '_0';

        if (!isSpecified($verb)) {
            $objID = isl_strtoupper($this->getObjectIDField());
        } else {
            $objID = 'RECORD#';
        }

        if (!isSpecified($contextLoc) || $this->isNonMegaEventJob($job)) {
            // TopLevel
            //GetList call on entity details
            $allEntities = EntityManager::GetListQuick('locationentity',
                ['RECORDNO']
            );
            foreach ($results as $result) {
                foreach ($allEntities as $entity) {
                    foreach ($entity as $entityRec) {
                        $externalid = $guid . '_' . $entityRec;

                        $this->setExternalIDMap($result[$objID], $externalid);
                    }
                }
                $this->setExternalIDMap($result[$objID], $topExternalID);
            }

        } else {
            $entityExternalID = $guid . '_' . $contextLoc;
            // Enntity Level
            foreach ($results as $result) {
                $this->setExternalIDMap($result[$objID], $entityExternalID);
            }
        }
    }

    /**
     * @param array $querySpec
     * @return bool|array
     *
     */
    protected function getEventData($querySpec): bool|array
    {

        $mgr = Globals::$g->gManagerFactory->getManager('allocation');

        $result = $mgr->GetList($querySpec);
        $allocationKeyList = [];
        if(!empty($result)) {
            foreach($result as $allocation) {
                $allocationKeyList[] = $allocation['RECORDNO'];
            }

            if(!empty($allocationKeyList)) {
                $mgr = Globals::$g->gManagerFactory->getManager('allocationentry');
                $querySpecEntry = [
                    'filters' => [[['ALLOCATIONKEY', 'IN', $allocationKeyList]]]
                ];
                $querySpecEntry['orders'][0] = ['RECORDNO', 'ASC'];
                $entryResult = $mgr->GetList($querySpecEntry);
                if(!empty($entryResult)) {
                   foreach($result as &$allocation) {
                       $entryKeylabel = $this->getEntryField();
                      $allocation[$entryKeylabel] = [];
                      foreach($entryResult as $entry) {
                        if($entry['ALLOCATIONKEY'] == $allocation['RECORDNO']) {
                             $allocation[$entryKeylabel][] = $entry;
                        }
                      }
                   }
                }
            }
        }
        return $result;
    }

    /**
     * @param array $job
     * @param MetricAllocationDnSync $metric
     * @return array
     */
    protected function buildQuerySpec($job, $metric): array
    {
        // get the max limit and start
        $max = $job['OBJECT']['limit'];
        $start = $job['OBJECT']['start'];

        if (isNullOrBlank($start)) {
            $start = "0";
        }
        $metric->setStartWith($start);

        if (isNullOrBlank($max)) {
            $max = DNEventHandlerConstaint::BULKSYNCLIMIT;
        }

        $jobLocationContext = $this->getJobLocationContext($job);
        if (isNullOrBlank($jobLocationContext)) {
            $metric->setLocation('0');
        } else {
            $metric->setLocation($jobLocationContext);
        }

        // Build Query Spec
        $querySpec = [
            'start' => $start,
            'max' => $max,
        ];

        // Query order by
        $querySpec['orders'][0] = ['RECORDNO', 'ASC'];

        if (IsMultiEntityCompany() && !isNullOrBlank($jobLocationContext)) {
            $querySpec['filters'][] = [['MEGAENTITYKEY', '=', $jobLocationContext]];

        }

        return $querySpec;
    }

    /**
     *
     * @return string
     *
     */
    public function getEntryField(): string
    {
        return DNEventHandlerConstaint::ALLOCATIONENTRY_TYPE;
    }
}