<?

/**
*    FILE:            InvoicesAnalysisGraphReport.cls
*    AUTHOR:            Senthil
*    DESCRIPTION:    
*
*    (C)2000, Intacct Corporation, All Rights Reserved
*
*    Intacct Corporation Proprietary Information.
*    This document contains trade secret data that belongs to Intacct 
*    corporation and is protected by the copyright laws. Information herein 
*    may not be used, copied or disclosed in whole or part without prior 
*    written consent from Intacct Corporation.
*/

import('DefRevForecastReport');
require_once 'Dictionary.cls';

class DefRevForecastGraphReport extends DefRevForecastReport
{
    /* @var string[] $reportdata */
    var $reportdata;

    /* @var string $fo_url */
    var $fo_url;

    /* @var string $furl */
    var $furl;

    /**
     * @param array $params
     */
    function __construct($params)
    {
        parent::__construct(
            INTACCTarray_merge(
                $params, 
                array( 
                'report' => 'defrevforecastgraph',                         
                '2stage' => 'false',
                ) 
            )
        );                
        $this->params['isgraph'] = '1';        
        $this->params['2stage'] = false;
    }


    /**
     * @return array
     */
    function DoMap()
    {
        $this->reportdata = parent::DoMap();

        $this->DoGraphMap();
        $lines['report']['0']["graph"][0]["isgraph"] = '1';
        $lines['report']['0']["graph"][0]["furl"] = $this->fo_url;
        $lines['report']['0']["graph"][0]["htmlfurl"] = $this->furl;
        $lines['report']['0']["graph"][0]["co"] = GetMyCompanyName(); 
        $lines['report']['0']["graph"][0]["title"] = $this->title;
        $lines['report']['0']["graph"][0]["title2"] = $this->title2;
        $lines['report']['0']["graph"][0]['location'] = $this->_currloc;
        $lines['report']['0']["graph"][0]['department'] = $this->_currdept;
        $lines['report']['0']["graph"][0]['reportdate'] = GetCurrentDate(IADATE_USRFORMAT);
        return $lines;
    }


    /**
     * @return array|null
     */
    function DoGraphMap()
    {
        /** @var array $reportdata */
        $reportdata = $this->reportdata['report'][0]['REVENUE'];

        //the last one is GRAND total line, which we don't need at this moment.
        /** @noinspection PhpUnusedLocalVariableInspection */
        $count = sizeof($reportdata) - 1;
        $tab = array();
        $xLabels = array();
        $gLabels = array();

        for ($iG=0;$iG<count($reportdata);$iG++) {
            $periods = $reportdata[$iG];
            $iP = 0;
            foreach ($periods as $key => $val) {
                if ($key=='ACTITLE') {
                    $gLabels[] = $val;
                    continue;
                }
                elseif ($key=='TOTAL' || $key=='LEVEL') {
                    continue;
                }
                    
                $tab[$iG][$iP] = $val;
                $iP++;
            }
        }
        foreach ( $this->periods as $val) {
            if ($val['TITLE'] == 'Total') {
                continue;
            }
            $xLabels[] = $val['TITLE'];
        }

        global $kGraphTemplates;
        $tempname = INTACCTtempnam(GRAPHDIR, 'graph');
        $unique = isl_substr($tempname, isl_strlen(GRAPHDIR));
        $fname = $tempname . ( function_exists("imagegif") ? '.gif' : '.png' );
        unlink($tempname); // This is no longer used, remove it!

        $prefs = array();
        GetCompanyPreferences($prefs);
        $symbol = isl_trim($prefs['CURRENCYSYMBOL']);

        $yaxis = "($symbol)";
        if((isl_substr($this->params['GRAPHFORMAT'], 6, 3))=='bar') {
            $xaxis = "($symbol)";
            $yaxis = "";
        }

        // Use for backward compatibility
        $graphType = isset($kGraphTemplates[$this->params['GRAPHFORMAT']]) ? $this->params['GRAPHFORMAT'] : 'GRAPH.col.6';
        $graphType = graph_mapGraphTypeForBackwardsCompatibility($graphType);
        $graphTemplate = $kGraphTemplates[$graphType];
        
        //For HTML type, Graph size will be overwritten by user's Input
        /** @noinspection PhpUnusedLocalVariableInspection */
        $type = $this->params['type'];
        if ($this->params['offline_mode']) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $type = $this->params['offreporttype'];
        }
        if (isset($this->params['ISDBGRAPH']) || $this->params['type'] == '_html') {
            /** @noinspection PhpUndefinedVariableInspection */
            $dbGraphData = array (
            're_type' => $this->params['re_type'],
            'xlabels' => $xLabels,
            'glabels' => $gLabels,
            'xaxis'   => $xaxis,
            'yaxis'      => $yaxis,
            'tab'      => $tab,
            'title'      => $this->title,
            );
            $this->map = $dbGraphData;
            return $this->map;
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $fileStoreID = CreateGraphImage($graphTemplate, $xLabels, $gLabels, $xaxis ?? '', $yaxis ?? '', $tab, $this->params['titlecomment'] ?? '', $fname) ?? '';

        $furl = GRAPHURL . isl_substr($fname, isl_strlen(GRAPHDIR));

        // remember fully qualified path for embeding into MIME email
        $this->graphfiles[] = $fname;
        $this->graphurls[] = $furl;

        $newunique = isl_substr($unique, 5);
        $fo_url = getResourceUrl("/acct/showthis.phtml?showthis=" . $newunique . "&mode=graph" . "&fsid=" . $fileStoreID . '&.sess=' . Session::getKey() . '&.op=' . MAX_DUMMY_ID);

        $this->fo_url = $fo_url;
        $this->furl = $furl;

        return null;
    }

    /**
     * Override GenerateHTML function to replace the old VH graph image by FusionCharts flash file
     *
     * @param string $xml
     * @param string $offline
     *
     * @return string|true  text if offline, true if echo'd directly to output
     */
    function GenerateHTML(&$xml, $offline = 'false')
    {
        $this->insertReportChart();
        return true;
    }

    /**
     * @return bool
     */
    function IsMultiRegionGraph() 
    {
        // All graph for defered revenue forecast are multiserie graph
        return true;
    }
}

