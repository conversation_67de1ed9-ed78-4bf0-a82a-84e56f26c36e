<?
/**
*    FILE: glbudgetitemmanager.cls
*    AUTHOR: Nithin MG <<EMAIL>>
*    DESCRIPTION:
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/



/**
 * Class GLBudgetManager
 */
class GLBudgetItemManager extends OwnedObjectManager
{

    /* @var int $bdgtRecNumReserveCnt */
    private $bdgtRecNumReserveCnt = 0;

    /* @var int $nextRecordKeyAvailable */
    private $nextRecordKeyAvailable = -1;

    /**
     * GLBudgetItemManager constructor.
     *
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * @param $ignoreDimensionList
     *
     * @return bool
     */
    protected function ignoreDimensions(&$ignoreDimensionList) : bool
    {
        return false;
    }
    
    /**
     * @return string
     */
    public function getIntegrationObjectName()
    {
        return 'GLBUDGET';
    }

    /**
     * @param string $fieldName
     *
     * @return string
     */
    protected function getDimensionRecordNoField($fieldName)
    {
        if ($fieldName == 'LOCATION') {
            return 'LOCATIONKEY';
        }

        if ($fieldName == 'DEPARTMENT') {
            return 'DEPTKEY';
        }

        if ($fieldName == 'ACCOUNTNO') {
            /* Workaround to handle platform restriction, in our case we have only one key field
               for both financial and stat account, but we have two objects for account. Actual key is 'ACCOUNTKEY'.  */
            return 'GLACCOUNTKEY';
        }

        if ($fieldName == 'STATACCOUNTNO') {
            /* Workaround to handle platform restriction, in our case we have only one key field
               for both financial and stat account, but we have two objects for account. Actual key is 'ACCOUNTKEY'. */
            return 'STATACCOUNTKEY';
        }

        return parent::getDimensionRecordNoField($fieldName);
    }

    protected function getStdDimensionFieldName2RecordNoFieldNames()
    {
        $dimensionFieldName2RecordNoFieldName = parent::getStdDimensionFieldName2RecordNoFieldNames();
        $dimensionFieldName2RecordNoFieldName['LOCATION'] = $this->getDimensionRecordNoField('LOCATION');
        $dimensionFieldName2RecordNoFieldName['DEPARTMENT'] = $this->getDimensionRecordNoField('DEPARTMENT');
        $dimensionFieldName2RecordNoFieldName['ACCOUNTNO'] = $this->getDimensionRecordNoField('ACCOUNTNO');
        $dimensionFieldName2RecordNoFieldName['STATACCOUNTNO'] = $this->getDimensionRecordNoField('STATACCOUNTNO');
        return $dimensionFieldName2RecordNoFieldName;
    }

    /**
     * Translate the record
     *
     * @param array $values  the object values
     *
     * @return bool  true on success and false on failure
     */
    function TranslateRecord(&$values)
    {
        /**
         * 'multientity_filters' is not implemented properly for glbudgets. So user entered 'LOCATION#' field is getting
         * overridden by Entity contextid. To fix this, either we need to reset the below mega filters or fix the root cause.
         * We know this is not the right place to reset the MEGAENTITY values. Since EntityManager::addMEGAEntityFields
         * is not allowed to override, this is a temporary solution.
         */

        unset($values['MEGAENTITYID']);
        unset($values['MEGAENTITYKEY']);
        unset($values['MEGAENTITYNAME']);

        return parent::TranslateRecord($values);
    }

    /**
     * @return array|null
     */
    function GetMultiEntityFilterFields()
    {
        return null;
    }


    /**
     * @param string $_verb
     * @param array $_object
     * @param bool $multiselect
     * @return bool
     */
    function Submit($_verb, $_object, $multiselect = false)
    {

        if ($_verb === "zeroout" && isset(Request::$r->_r)) {
            return $this->API_Delete(Request::$r->_r);
        }

        return parent::Submit($_verb, $_object, $multiselect);
    }

    /**
     * @param string $recordNo
     *
     * @return bool
     */
    public function zeroOut($recordNo)
    {
        $amount = 0;
        $updateQry = "update glbudget set amount = :1 where cny# = :2 and record# = :3";
        $ok = ExecStmtEx([$updateQry, $amount, GetMyCompany(), $recordNo], $rowCount, 1);
        $ok = $ok && ($rowCount > 0);
        if (!$ok) {
            $msg = sprintf('Record not found: %s ', $recordNo);
            Globals::$g->gErr->addIAError('GL-2002', __FILE__ . ':' . __LINE__, $msg, ['RECORD_NO' => $recordNo]);
        }

        return $ok;
    }

    /**
     * @param string $recordno
     * @return bool
     */
    function API_Delete($recordno)
    { 
       return $this->zeroOut($recordno);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        if (GlBudgetHandler::getInstance()->blockIfMigrating(__FILE__, __LINE__)) {
            return false;
        }
        $this->nextRecordKeyAvailable = -1;
        $this->bdgtRecNumReserveCnt
            = Globals::$g->gManagerFactory->getManager("glbudgetheader")->getReserveCountForLineItem();

        return parent::regularAdd($values);
    }

    /**
     * @return false|int
     */
    protected function getNextRecordKeyForBaseAdd()
    {
        if ($this->bdgtRecNumReserveCnt > 0) {
            if ($this->nextRecordKeyAvailable === -1) {
                /* Calling Get Next Record Key in a loop is costly, so reverse the count and return for the variable */
                $glBudgetItemMgr = Globals::$g->gManagerFactory->getManager("glbudgetitem");
                $this->nextRecordKeyAvailable = $glBudgetItemMgr->GetNextRecordKey($this->bdgtRecNumReserveCnt);
            }
            $this->bdgtRecNumReserveCnt--;
            return $this->nextRecordKeyAvailable++;
        }

        /* Safe side: not expecting this case */
        return parent::getNextRecordKeyForBaseAdd();
    }


    /**
     * Update GL Budget
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        if (GlBudgetHandler::getInstance()->blockIfMigrating(__FILE__, __LINE__)) {
            return false;
        }

        return parent::regularSet($values);
    }

    /**
     * @param int|string $ID
     * @return bool
     */
    function Delete($ID)
    {
        if (GlBudgetHandler::getInstance()->blockIfMigrating(__FILE__, __LINE__)) {
            return false;
        }

        if (!isNullOrBlank($ID)) {
            $result = $this->getMaxBudgetRecByAnyRecNo($ID);
            if ($ID == $result["RECORD#"]) {
                return parent::Delete($ID);
            }
        }

        if (isset(Request::$r->_r)) {
            $budgetRec = GLBudgetHeaderManager::parseBudgetGroupKey(Request::$r->_r, true);

            $querySpec = ["selects" => ["RECORDNO"]];
            Globals::$g->gManagerFactory->getManager("glbudgetitem")
                ->getQuerySpecForBudgetComb($budgetRec, $querySpec);

            $records = $this->GetList($querySpec);
            $source = "GLBudgetItemManager::delete";
            $ok = $this->_QM->beginTrx($source);
            foreach ($records as $record) {
                $ok = $ok && parent::Delete($record["RECORDNO"]);
                if (!$ok) {
                    break;
                }
            }
            $ok = $ok && $this->_QM->commitTrx($source);
            if (!$ok) {
                $this->_QM->rollbackTrx($source);
            }
            return $ok;
        }

        Globals::$g->gErr->addIAError(
            'GL-2003',
            "(" . __FILE__ . ':' . __LINE__ . ')',
            sprintf("Intermediate reporting period data for the budget # %s can't be deleted.", $ID), ['ID' => $ID],
            "", [] , "Please provide the last record# of a budget.", [] );
        return false;
    }


    /**
     * @param array $budgetRec
     */
    public function CleanBudgetRec(&$budgetRec)
    {
        $elementToClean = array('ACTYPE');
        $tempBudgetRec = array();
        foreach ($budgetRec as $key => $value) {
            if (!in_array($key, $elementToClean)) {
                $tempBudgetRec [$key] = $value;
            }
        }
        $budgetRec = $tempBudgetRec;
    }


    /**
     * @param int $def_id
     * @param string $val
     * @return int|string
     */
    public function getCustomDataKey($def_id, $val)
    {
        $objDef = Pt_DataObjectDefManager::getById($def_id);
        $dataObject = Pt_DataObjectManager::getByName($objDef, $val);

        if ($dataObject != null) {
            return $dataObject->getId();
        }

        return '';
    }

    /**
     * @param array $budgetRec
     * @param array $querySpec
     */
    public function getQuerySpecForBudgetComb($budgetRec, &$querySpec)
    {
        $custDimFieldNames = [];
        // Custom dimension details
        if (!util_isPlatformDisabled()) {
            $custDimMap = Pt_StandardUtil::getGLDimensionFieldNamesForGetList();
            $custDimFieldNames = array_values($custDimMap);
        }

        $this->CleanBudgetRec($budgetRec);
        foreach ($budgetRec as $entitykey => $val) {
            if (strpos($entitykey, "CUSTDIM_", 0) !== false) {
                $parts = explode("_", $entitykey);
                $defId = $parts[1];

                /** @noinspection PhpUndefinedVariableInspection */
                $entitykey = $custDimMap[$defId];

                // Convert into record#
                if ($val != '') {
                    $val = $this->getCustomDataKey($defId, $val);
                }
            }
            // Custom field name will also be present in the array, we will ignore it since we have taken care of it
            // as 'CUSTDIM_' check above
            elseif (isset($custDimFieldNames) && in_array($entitykey, $custDimFieldNames)) {
                continue;
            }

            if ($val != '') {
                $querySpec['filters'][0][] = array($entitykey, '=', $val);
            } else {
                $querySpec['filters'][0][] = array($entitykey, 'ISNULL');
            }
        }
    }


    /**
     * @param int|string $recNo
     * @return false|string[]
     */
    public function getMaxBudgetRecByAnyRecNo($recNo)
    {
        $dimKeys = IADimensions::getAllDimensionKeys();
        
        $selectDims = implode(",", $dimKeys);
        $whereDims = "";
        foreach ($dimKeys as $dimKey) {
            $whereDims .= " AND nvl(glbudget.$dimKey,0) = nvl(singleglb.$dimKey,0)";
        }
        
        $query = "SELECT
                    glbudget.*
                FROM
                    glbudgettype,
                    (
                        SELECT
                            glbudget.cny#,
                            glbudget.record#,
                            glbudget.bud_type#
                        FROM
                            glbudget,
                            (
                                SELECT
                                    cny#,
                                    budgetkey,
                                    account#,
                                    dept#,
                                    location#,
                                    $selectDims
                                FROM
                                    glbudget
                                WHERE
                                    cny# = :1
                                    AND record# = :2
                            ) singleglb
                        WHERE
                            glbudget.cny# = singleglb.cny#
                            AND nvl(glbudget.budgetkey, 0) = nvl(singleglb.budgetkey,0)
                            AND nvl(glbudget.account#, 0)= nvl(singleglb.account#,0)
                            AND nvl(glbudget.dept#, 0) = nvl(singleglb.dept#, 0)
                            AND nvl(glbudget.location#, 0) = nvl(singleglb.location#,0)
                            $whereDims
                    ) glbudget
                WHERE
                    glbudgettype.cny# = glbudget.cny#
                    AND glbudgettype.record# = glbudget.bud_type#
                ORDER BY
                    start_date DESC
                FETCH FIRST ROW ONLY";

        $result = QueryResult(array($query, GetMyCompany(), $recNo));

        return $result[0] ?? false;
    }

    /**
     * @param Pt_RelationshipDef $relDef
     * @param int|null $platformObjDefId
     * @param string $entity
     * @param array $schemas
     */
    public static function innerAddPlatformRelationshipField($relDef, $platformObjDefId , $entity, &$schemas)
    {

        $objDefId = $relDef->getObjectDefId2();

        $custDimMap = GLEntryManager::getPTFieldsMap();
        $fieldName = $custDimMap[$objDefId];

        $fieldName = Pt_StandardUtil::getFieldNameForGetList($fieldName);

        $relationshipName = $relDef->getRelationshipName();

        // Only if 'children' already has relationship defined with pt_relationship table
        if ( !Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($relDef) ) {
            if (!isset($schemas[$entity]['children'][$relationshipName])) {
                return;
            }
        }

        //For display part of a relationship/custom diamension object
        $fkObject_disp = $fieldName . "_";
        $field_disp = array('path' => $fkObject_disp,
            'type' => array('type' => 'platformptr',
                'relationshipDef' => $relDef)
        );

        $maskedFKObject_disp = '"' . $fieldName . '_"';
        $field_disp['fullname'] = $relDef->getSingularName2();

        $schemas[$entity]['fieldinfo'][] = $field_disp;
        $schemas[$entity]['object'][] = $fkObject_disp;

        // Is it as simple as adding to schema, children, etc. here?
        $schemas[$entity]['schema'][strtoupper($field_disp['path'])] = $maskedFKObject_disp . ".obj_name";

        if ( !Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($relDef) ) {
            $schemas[$entity]['children'][$relationshipName]['children'][$maskedFKObject_disp] = [
                'fkey' => 'obj2_id',
                'table' => 'pt_obj_Data',
                'join' => 'outer',
                'invfkey' => 'RECORD#'];
        } else {
            $column = Pt_DataFieldDefManager::getColumnNameFiedldByObjDefIdAndName($platformObjDefId, $fieldName);
            $schemas[$entity]['children'][$maskedFKObject_disp] = [
                'fkey' => $column,
                'table' => 'pt_obj_Data',
                'join' => 'outer',
                'invfkey' => 'RECORD#'];
        }
    }

    /**
     * @param Pt_RelationshipDef $relDef
     * @param int|null $platformObjDefId
     */
    function addPlatformRelationshipField($relDef, $platformObjDefId = null)
    {
        parent::addPlatformRelationshipField($relDef, $platformObjDefId);
        self::innerAddPlatformRelationshipField($relDef, $platformObjDefId, $this->_entity,
            $this->_schemas);
    }

    /**
     * @return bool
     */
    function ExistSmartLinks()
    {
        return false;
    }

    /**
     * @param string $fieldName
     * @return string
     */
    public function getTranslatedDimFieldName($fieldName)
    {
        if ($fieldName == 'LOCATION') {
            return 'LOCATION_NO';
        }

        if ($fieldName == 'DEPARTMENT') {
            return 'DEPT_NO';
        }

        if ($fieldName == 'ACCOUNTNO') {
            return 'ACCT_NO';
        }

        if ($fieldName == 'STATACCOUNTNO') {
            return 'ACCT_NO';
        }

        return parent::getTranslatedDimFieldName($fieldName);
    }
}
