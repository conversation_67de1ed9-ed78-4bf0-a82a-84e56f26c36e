<?php

/**
 *    FILE:          GLAcctGrpPurposeLister.cls
 *    AUTHOR:        <PERSON><PERSON><PERSON><PERSON> <janarthanan.r<PERSON><PERSON><PERSON>@sage.com>
 *    DESCRIPTION:   Lister Class for object GLACCTGRPPURPOSE
 *
 *    (C) 2014, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class GLAcctGrpPurposeLister extends NLister
{

    /**
     * GLAcctGrpPurposeLister constructor.
     */
    public function __construct()
    {
        parent::__construct(
            [
                'entity'           => 'glacctgrppurpose',
                'title'            => 'IA.ACCOUNT_GROUP_PURPOSES',
                'fields'           => ['NAME', 'STATUS'],
                'nonencodedfields' => ['RECORD_URL'],
                'importtype'       => 'glacctgrppurpose',
            ]
        );
    }
}