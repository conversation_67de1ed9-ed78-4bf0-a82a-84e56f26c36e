<?php
/**
 * Entity file for Stat Accounts
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 Intacct Corporation All, Rights Reserved
 */
$kSchemas['reportingacheader'] = array(
    'children' => array(
        'userinfo' => array(
                        'fkey' => 'userkey', 
                        'invfkey' => 'record#', 
                        'table' => 'userinfo', 
                        'join' => 'outer' 
                        ),
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'REPORTINGACHDRKEY',
            'invfkey' => 'RECORDNO',
            'entity' => 'reportingac',
            'path' => 'REPORTINGAC',
        ),
        [
            'fkey'    => 'REPORTINGACHEADERKEY',
            'invfkey' => 'RECORDNO',
            'entity'  => 'reportingacheaderpermissions',
            'path'    => 'PERMISSIONS',
        ],
    ),
    'object' => array(
        'RECORDNO',
        'HEADERID',
        'DESCRIPTION',
        'STATUS',
        'WHENCREATED', 
        'WHENMODIFIED', 
        'CREATEDBY', 
        'MODIFIEDBY',
        'USERKEY',
        'ADMINISTRATOR'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'HEADERID'  => 'headerid',
        'DESCRIPTION'  => 'description',
        'STATUS'  => 'status',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'USERKEY' => 'userkey',
        'ADMINISTRATOR' => 'userinfo.loginid'
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),
    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'readonly' => true,
            'hidden' => true,
            'desc' => 'IA.RECORD_NUMBER',
            'type' => array(
                'ptype' => 'sequence',
                'type' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'id' => 1
        ),
        array(
            'fullname' => 'IA.ACCOUNT_SET',
            'type' => array(
                'ptype' => 'text', 
                'type' => 'text',
                'maxlength' => 40, 
                'format' => $grptAcHdrIDFormat,
             ),
            'required' => true,
            'desc' => 'IA.ACCOUNT_SET',
            'path' => 'HEADERID',
            'id' => 2,
        ),
        array(
            'fullname' => 'IA.DESCRIPTION',
            'type' => array('ptype' => 'text', 'type' => 'text', 'maxlength' => 100),
            'desc' => 'IA.DESCRIPTION',
            'path' => 'DESCRIPTION',
            'id' => 3,
        ),
        array(
            'path' => 'ADMINISTRATOR',
            'fullname' => 'IA.ADMINISTRATOR',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'userinfo',
            ),
            'id' => 4,
        ),
        array(
            'path'       => 'GROUP',
            'fullname'   => '',
            'type'       => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'usergroup',
            ),
            'id' => 5,
        ),
        array(
            'path'       => 'USER',
            'fullname'   => '',
            'type'       => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'userinfo',
            ),
            'id' => 6,
        ),
        array(
            'fullname' => 'IA.COLUMN',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 30
            ),
            'desc' => 'IA.COLUMN',
            'path' => 'COLUMN',
            'id' => 7,
        ),
        array(
            'fullname' => 'IA.OPERATOR',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'size' => 15,
            ),
            'desc' => 'IA.OPERATOR',
            'path' => 'OPERATOR',
            'id' => 8,
        ),
        array(
            'fullname' => 'IA.VALUE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 32
            ),
            'desc' => 'IA.VALUE',
            'path' => 'VALUE',
            'id' => 9,
        ),
        array(
            'fullname' => 'IA.CONDITION',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.CONDITION',
            'path' => 'CONDITION',
            'id' => 10,
        ),        
        array(
            'fullname' => 'IA.CONDITION_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',                
                'validlabels' => array('IA.ALL_AND', 'IA.ANY_OR', 'IA.EXPRESSION'),                
                'validvalues' => array('AND', 'OR', 'EXPRESSION'),
            ),
            'default' => 'OR',
            'desc' => 'IA.CONDITION_TYPE',
            'path' => 'CONDITIONTYPE',
            'id' => 11,
        ),
        array(
            'fullname' => 'IA.MAP_GL_ACCOUNTS_TO_REPORTING_ACCOUNTS',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'path' => 'IMPORTTEXT',
            'readonly'    => true,
            'id' => 12,
        ),
        array(
            'fullname' => 'IA.IMPORT_REPORTING_ACCOUNTS',
            'type' => array(
                'ptype' => 'button',
                'type' => 'button',
            ),
            'path' => 'IMPORTBUTTON',
            'id' => 13,
        ),
        array(
            'fullname' => 'IA.REPORTING_ACCOUNT_SET',
            'type' => array('ptype' => 'text', 'type' => 'text', 'maxlength' => 40),
            //'required' => true,
            'desc' => 'IA.REPORTING_ACCOUNTS_SET',
            'path' => 'DUPLICATENAME',
            'id' => 14,
        ),
        array(
            'fullname' => 'IA.REPORTING_ACCOUNTS',
            'type' => $gBooleanType,
            'path' => 'COPYGLACCOUNTS',
            'default' => false,
            'id' => 15,
        ),
        array(
            'fullname' => 'IA.REPORTING_ACCOUNT_MAPPINGS',
            'type' => $gBooleanType,
            'path' => 'COPYACCTSMAPPING',
            'default' => false,
            'id' => 16,
        ),
        array(
            'path'     => 'PERMLISTMEMBER',
            'fullname' => 'IA.GROUP_USER',
            'readonly' => true,
            'type'     => array(
                'ptype'  => 'text',
                'type'   => 'text',
            ),
            'id' => 17,
        ),
        array(
            'path'     => 'ALLOWACCESS',
            'fullname' => 'IA.ALLOW_ACCESS',
            'type'     => $gBooleanType,
            'hidden'   => true,
            'id' => 18,
        ),
        array(
            'path'     => 'ALLOWACCESS2',
            'fullname' => 'IA.ACCESS_RIGHTS',
            'type'     => array(
                'ptype'  => 'enum',
                'type'   => 'enum',
                'validlabels'   => array('IA.ALLOW', 'IA.DENY'),
                'validvalues'   => array('allow', 'deny'),
            ),
            'id' => 19,
        ),
        array(
            'path'     => 'USERGROUP',
            'fullname' => '',
            'type'     => array(
                'ptype'  => 'enum',
                'type'   => 'enum',
                'validlabels'   => array('IA.GROUP', 'IA.USER'),
                'validvalues'   => array('group', 'user'),
            ),
            'id' => 20,
        ),

        array(
            'fullname' => 'IA.REPORTING_ACCOUNT_NUMBER',
            'type' => array(
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 24, 
                'format' => '/^[\w\-\_\:\~\.]{1,24}$/'
            ),
            'desc' => 'IA.REPORTING_ACCOUNT_NUMBER',
            'path' => 'REPORTINGACNO',
            'id' => 21
        ),
        array(
            'fullname' => 'IA.REPORTING_ACCOUNT_TITLE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text', 
                'maxlength' => 80
            ),
            'desc' => 'IA.REPORTING_ACCOUNT_TITLE',
            'path' => 'REPORTINGACTITLE',
            'id' => 22
        ),
        array(
            'fullname' => 'IA.GL_ACCOUNT_NUMBER',
            'type' => array(
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 24, 
                'format' => '/^[\w\-\_\:\~\.]{1,24}$/'
            ),
            'desc' => 'IA.REPORTING_ACCOUNT_NUMBER',
            'path' => 'NATIVEACNO',
            'id' => 23
        ),
        array(
            'fullname' => 'IA.GL_ACCOUNT_TITLE',
            'type' => array(
                'ptype' => 'text', 
                'type' => 'text', 
                'maxlength' => 80
            ),
            'desc' => 'IA.REPORTING_ACCOUNT_TITLE',
            'path' => 'NATIVEACTITLE',
            'id' => 24
        ),
        array(
            'path' => 'STATISTICAL',
            'fullname' => 'IA.STATISTICAL',
            'desc' => 'IA.STATISTICAL',
            'type' => $gBooleanType,
            'id' => 25
        ),
        array(
            'fullname' => 'IA.GL_ACCOUNT_TYPE',
            'required' => true,
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validvalues' => array('N', 'I'),
                'validlabels' => array('IA.N', 'IA.I'),
            ),
            'default' => 'N',
            'desc' => 'IA.WHEN_STATISTICAL_N_CUMULATIVE_I_FORPERIOD',
            'path' => 'NATIVEACTYPE',
            'id' => 26
        ),
        array(
            'fullname' => 'IA.REPORTING_ACCOUNT_TYPE',
            'required' => true,
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validvalues' => array('N', 'I'),
                'validlabels' => array('IA.N', 'IA.I'),
            ),
            'default' => 'N',
            'desc' => 'IA.WHEN_STATISTICAL_N_CUMULATIVE_I_FORPERIOD',
            'path' => 'REPORTINGACTYPE',
            'id' => 27,
        ),
        array(
            'fullname' => 'IA.BASE_ACCOUNT',
            'desc' => 'IA.BASE_ACCOUNT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'baseaccount',
                'pickentity' => 'baseaccountpick',
            ),
            'path' => 'BASEGLACCOUNT',
            'id' => 28,
        ),
        $gStatusFieldInfo,
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
    'api' => array(
        'GET_BY_GET' => true,                
    ),
    'table' => 'v_reportingacheader_restricted',
    'printas' => 'IA.REPORTING_ACCOUNTS',
    'pluralprintas' => 'IA.REPORTING_ACCOUNTS',
    'vid' => 'RECORDNO', 
    'autoincrement' => 'RECORDNO',
    'module' => 'gl',
    'auditcolumns' => true,
    'upsertEntriesPaths' => ['REPORTINGAC'],
    'nameFields' => [ 'REPORTINGACTITLE', 'REPORTINGACNO' ],
);
