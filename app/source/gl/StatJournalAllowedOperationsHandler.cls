<?php
/**
 * StatJournalAllowedOperationsHandler
 *
 * <AUTHOR>
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 *
 */

class StatJournalAllowedOperationsHandler extends AllowedOperationsHandler
{

    public function __construct(EntityManager $entManager)
    {
        parent::__construct($entManager);
        $this->statJournalMgr = Globals::$g->gManagerFactory->getManager('statjournal');
    }

    protected function canDelete(array $record, string $moduleKey = null) : bool
    {
        if ( IsMCMESubscribed() ) {
            return $this->canEditOrDeleteCNBook($record['SYMBOL']);
        }

        return true;
    }

    protected function canEdit(array $record, string $moduleKey = null) : bool
    {
        if ( IsMCMESubscribed() ) {
            return $this->canEditOrDeleteCNBook($record['SYMBOL']);
        }

        return true;
    }

    protected function canMemorize(array $record, string $moduleKey = null) : bool
    {
        // Inactive journals cannot be memorized
        $canMemorize = $record['STATUS'] === 'active' ? true : false;

        if ( IsMultiEntityCompany() ) {
            $canMemorize = $canMemorize && $this->canEditOrDeleteCNBook($record['SYMBOL']);
        }
        
        // Disable direct posting journals cannot be memorized
        $canMemorize = $canMemorize && !($this->statJournalMgr->IsDirectJrnlPostingDisabled($record['SYMBOL']));
        
        return $canMemorize;
    }

    protected function getOperations() : array
    {
        $operations = parent::getOperations();
        $operations = array_merge($operations, [ 'canMemorize' ]);

        return $operations;
    }

    protected function getFieldsForPermissionChecks() : array
    {
        $fields = parent::getFieldsForPermissionChecks();
        $fields = array_merge($fields, [ 'SYMBOL', 'STATUS' ]);

        return $fields;
    }

    private function canEditOrDeleteCNBook(string $symbol)
    {
        if ( GLBookManager::IsConsolidationBook($this->statJournalMgr->GetJournalBook($symbol), false) ) {
            return false;
        }

        return true;
    }
    /**
     * Override of parent method, necessary for non-megalized entity.
     *
     * @inheritDoc
     */
    protected function isValidOwner(array $record): bool
    {
        return true;
    }
}
