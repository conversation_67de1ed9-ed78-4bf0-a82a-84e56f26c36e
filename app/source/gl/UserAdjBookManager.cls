<?php

/**
 * Manager class for User-Defined Book
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for User-Defined Book
 */
class UserAdjBookManager extends EntityManager
{

    /**
     * @param array $params entitymanager param
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Add     
     *      
     * @param array &$values new values
     * 
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $gErr = Globals::$g->gErr;

        $source = "UserAdjBookManager::Add";
        $ok = $this->_QM->beginTrx($source);

        if ( in_array(strtoupper($values['BOOKID']), array(ACCRUAL_BOOK, CASH_BOOK, 'GAAP', 'TAX', 'GAAPADJ', 'TAXADJ')) ) {
            $msg = "Enter a valid Book ID. The given Book ID '" . $values['BOOKID'] . "' is not supported.";
            $gErr->addIAError('GL-0024', __FILE__ . ':' . __LINE__, $msg, ['BOOKID' => $values['BOOKID']]);
            $ok = false;
        }
        
        $ok = $ok && $this->createGLBooks($values);

        $ok = $ok && $this->TranslateValues($values);
        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = "Could not create User-Defined Book!";
            $gErr->addError('GL-0002', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Set     
     *      
     * @param array &$values new values
     * 
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $glbookMgr = $gManagerFactory->getManager('glbook');
        $source = "UserAdjBookManager::Set";

        $ok = $this->_QM->beginTrx($source);
        
        // $ok = $ok && $this->deleteGLBooks($values['BOOKID']);

        // $ok = $ok && $this->createGLBooks($values);
        $ok = $ok && $this->TranslateValues($values);
        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = "Could not update User-Defined Book!";
            $gErr->addError('GL-0003', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Delete     
     * 
     * @param string $ID record no
     * 
     * @return bool
     */
    function Delete($ID)
    {
        $gErr = Globals::$g->gErr;
        $source = "UserAdjBookManager::Delete";

        $ok = $this->_QM->beginTrx($source);

        $obj = $this->get($ID);

        $ok = $ok && $this->deleteGLAndIGCBooks($obj['BOOKID']);

        $ok = $ok && parent::Delete($ID);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = "Could not delete User-Defined Book!";
            $gErr->addError('GL-0004', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * TranslateValues     
     *      
     * @param array &$values new values
     * 
     * @return bool
     */
    protected function TranslateValues(/** @noinspection PhpUnusedParameterInspection */ &$values)
    {
        $ok = true;
        global $kGLid;
        $gErr = Globals::$g->gErr;
        $preferences = GetModulePreferences($kGLid);
        if((!isset($preferences['ENABLEUSERBOOK']) || $preferences['ENABLEUSERBOOK'] == 'F')) {
            $msg = "Enable user-defined books to add/edit books";
            $gErr->addIAError('GL-7036', __FILE__ . ':' . __LINE__, $msg, []);
            $ok = false;
        }
        return $ok;
    }

    /**
     * createGLBooks
     * 
     * @param array $values
     * 
     * @return bool
     */
    protected function createGLBooks($values)
    {
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $glbookMgr = $gManagerFactory->getManager('glbook');

        $source = "UserAdjBookManager::createGLBooks";
        $ok = $this->_QM->beginTrx($source);

        $params = array(
            'selects' => array('BOOKID'),
        );

        $params['filters'][0] = array(array('TYPE', '=', BOOKTYPE_STD));

        $stdBooks = $glbookMgr->GetList($params);

        foreach ( $stdBooks as $stdBook ) {
            $newBookID = $values['BOOKID'] . $stdBook['BOOKID'];
            $adjBookVal = array(
                'BOOKID' => $newBookID,
                'DESCRIPTION' => $values['DESCRIPTION'],
                'STATUS' => $values['STATUS'],
                'TYPE' => BOOKTYPE_USR,
                'OPERATIONAL' => $values['OPERATIONAL']
            );

            $ok = $ok && $glbookMgr->add($adjBookVal);
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = "Could not create GL Book!";
            $gErr->addError('GL-0001', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * deleteGLAndIGCBooks This function deletes ACCRUAL and consolidation UDB books if there is no transaction exists
     *
     * @param string $adjBookID
     *
     * @return bool
     */
    protected function deleteGLAndIGCBooks($adjBookID)
    {
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $glbookMgr = $gManagerFactory->getManager('glbook');

        $ok = true;
        $cny = GetMyCompany();

        $params = array(
            'selects' => ['BOOKID', 'PARENTKEY'],
        );
        $params['filters'][0] = array(array('TYPE', '=', BOOKTYPE_STD));
        $stdBooks = $glbookMgr->GetList($params);

        foreach ($stdBooks as &$stdBook) {
            $stdBook['BOOKID'] = $adjBookID . $stdBook['BOOKID'];
        }

        $filters = array(
            'selects' => ['BOOKID', 'PARENTKEY'],
            'filters' => array(
                array(
                    array('SOURCEBOOKID', '=', $adjBookID),
                    array('TYPE', '=', BOOKTYPE_ADJCSN)
                )
            )
        );
        //Get UDB book included in IGC book
        $consBookIds = $glbookMgr->GetList($filters);
        //Merging both consolidation and ACCRUAL books
        $stdBooks = array_merge($stdBooks, $consBookIds);

        foreach ($stdBooks as $stdBook) {
            $thisBook = $stdBook['BOOKID'];
            $acRes = QueryResult(
                array("select distinct(journalkey) from bookjournals where cny#  = :1 and bookid = :2", $cny, $thisBook)
            );
            foreach ($acRes as $value) {
                $jKeys[] = $value['JOURNALKEY'];
            }
            $ok = $ok && ExecStmt(array("DELETE FROM BOOKJOURNALS WHERE cny# = :1 and bookid = :2", $cny, $thisBook));
            $ok = $ok && ExecStmt(array("DELETE FROM GLBOOKENTJRNLS WHERE cny# = :1 and bookkey = :2", $cny, $thisBook));
            $ok = $ok && $glbookMgr->Delete($thisBook);
            if (isset($jKeys) && $jKeys != '') {
                //delete consolidation glbatch as we upsert only glentry so we need to delete batch if there is no trx
                if (!is_null($stdBook['PARENTKEY'])) {
                    $stmt = [];
                    $stmt[0] = "DELETE FROM GLBATCHMST where cny# = :1";
                    $stmt[1] = $cny;
                    $stmt = PrepINClauseStmt($stmt, $jKeys, " and JOURNAL#");
                    $ok = $ok && ExecStmt($stmt);
                }

                $stmt = [];
                $stmt[0] = "DELETE FROM basejournal where cny# = :1";
                $stmt[1] = $cny;
                $stmt = PrepINClauseStmt($stmt, $jKeys, " and record#");
                $ok = $ok && ExecStmt($stmt);
            }
            $ok = $ok
                && ExecStmt(
                    array("DELETE FROM BASEGLTOTALSMST WHERE cny# = :1 and bookid = :2", $cny, $thisBook)
                );

            if (!$ok) {

                if (!is_null($stdBook['PARENTKEY'])) {
                    $msg = 'This user-defined book may include previously consolidated transactions. To delete this
                            user-defined book, re-consolidate book "' . $stdBook['PARENTKEY'] . '", which includes this
                            user-defined book, using the period with consolidated data from the book. Then delete the
                            user-defined book.' . " Intacct automatically deletes its journal.";
                    $gErr->addIAError(
                        'GL-0023',
                        __FILE__ . ':' . __LINE__,
                        'Unable to delete record', [],
                        $msg, ['PARENTKEY' => $stdBook['PARENTKEY'] ]
                    );
                }
                else {
                    $msg = 'Please delete book "' . $adjBookID . '" journal and its transactions, before deleting book.';
                    $gErr->addIAError(
                        'GL-0022',
                        __FILE__ . ':' . __LINE__,
                        'Unable to delete record', [],
                        $msg , ['ADJBOOKID'=> $adjBookID]);

                }

                 // i18n todo - IA-46429 tokenized placeholder (code changed  review )
            }
        }
        return $ok;
    }

    /**
     * @return bool
     */
    public function hasNonEditableFields()
    {
        return true;
    }

    /**
     * If the current record has non-editable fields
     *
     * @param       array   $values         Line attributes in DB
     *
     * @return      bool
     */
    public function recordHasNonEditableFields($values)
    {
        return true;
    }

    /**
     * List of all NonEditable fields in a contract detail based on schedule is editable
     *
     * @param array $values
     *
     * @return array
     */
    public function getNonEditableFields($values)
    {
        $nonEditableFields = ['OPERATIONAL'];
        return $nonEditableFields;
    }
}

