<?php
/**
 * Created by PhpStorm.
 * User: akumar
 * Date: 21-12-2018
 * Time: 16:09
 */

trait GlBookFilterTrait
{

    /* @var bool $isStatistical */
    protected $isStatistical;

    /**
     * Contains the relationship b/w <BOOKID> and linked <BOOKTYPE UI-labels> mapping (used in BOOKTYPE field).
     * Ex :  array('GAAPADJACCRUAL' => 'GAAP', 'ACCRUAL' => 'Accrual') ...
     *
     * @var string[] $bookIdLabelMap
     */
    private $bookIdLabelMap = array();

    /**
     * Contains the relationship b/w <BOOK> and <BOOKTYPE DB Value>. Here the array will contain only those book,
     * where we have the permission to create it's journal entry.
     *
     * Ex :  array(ACCRUAL_BOOK => BOOKTYPE_STD_VAL, GAAP_BOOK => BOOKTYPE_GAAP_VAL) ...
     *
     * @var string[] $allowedBookTypeMap
     */
    private $allowedBookTypeMap = array();

    /* @var bool|null $isAdjGlBatchAllowed */
    private $isAdjGlBatchAllowed = null;

    /* @var bool $isBookFldVisibleAllowed */
    private $isBookFldVisibleAllowed = true;

    /**
     * @return bool
     */
    protected function isBookFldVisibleAllowed(): bool
    {
        return $this->isBookFldVisibleAllowed;
    }

    /**
     * @param bool $isBookFldVisibleAllowed
     */
    protected function setIsBookFldVisibleAllowed(bool $isBookFldVisibleAllowed)
    {
        $this->isBookFldVisibleAllowed = $isBookFldVisibleAllowed;
    }


    /**
     * @param array $params
     */
    protected function prepareDynamicBookTypeNdJrnl($params)
    {
        $adjBookTypes = getEnabledAdjBookTypes();

        /*
         * Include only those booktype in journal dropdown where we have the permission to create it's journal.
         */
        if (IsOperationAllowed(GetOperationId('gl/lists/glbatch/create'))) {
            $this->allowedBookTypeMap[ACCRUAL_BOOK] = BOOKTYPE_STD_VAL;
            $this->allowedBookTypeMap[CASH_BOOK] = BOOKTYPE_STD_VAL;
        }
        $this->isAdjGlBatchAllowed = IsOperationAllowed(GetOperationId('gl/lists/adjglbatch/create'));

        if (count($adjBookTypes) != 0) {
            foreach ($adjBookTypes as $adjBookType) {
                switch ($adjBookType) {
                    case GAAP_BOOK:
                        if (IsOperationAllowed(GetOperationId('gl/lists/gaapglbatch/create'))) {
                            $this->allowedBookTypeMap[GAAP_BOOK] = BOOKTYPE_GAAP_VAL;
                        }
                        break;
                    case TAX_BOOK:
                        if (IsOperationAllowed(GetOperationId('gl/lists/taxglbatch/create'))) {
                            $this->allowedBookTypeMap[TAX_BOOK] = BOOKTYPE_TAX_VAL;
                        }
                        break;
                    case 'USERBOOK':
                        if (IsOperationAllowed(GetOperationId('gl/lists/userglbatch/create'))) {
                            $this->allowedBookTypeMap[USR_BOOK] = BOOKTYPE_USR_VAL;
                        }
                        break;
                }
            }
        }

        $matches = array();
        self::findElements($params, array('path' => 'JOURNAL'), EditorComponentFactory::TYPE_FIELD,
            $matches);

        if ($matches) {
            $this->setJournalDefaultEntDef($matches[0]);
        }
    }

    /**
     * @param array $match
     */
    protected function setJournalDefaultEntDef(&$match)
    {
        if ($match) {
            $type = array(
                'ptype' => 'ptr',
                'pickentity' => 'alljournalpick',
                'entity' => 'alljournalpick',
                'pick_url' => 'picker.phtml',
                'pickfield' => array('PICKID', 'BILLABLE', 'ADJ'),
                'restrict' => array(
                    array(
                        'pickField' => 'statistical',
                        'value' => ($this->isStatistical ? 'T' : 'F'),
                    ),
                    array(
                        'value' => "T",
                        'pickField' => 'status',
                    ),
                )
            );

            $match['type'] = $type;
            $match['noedit'] = true;
            $match['nonew'] = true;
        }
    }

    /**
     * @param array $params
     */
    protected function setDynamicBookTypeDef($params)
    {
        $bookTypesPair = $this->getBookTypeKeyValuePair();
        if (count($bookTypesPair) > 0) {
            $matches = array();
            self::findElements($params, array('path' => 'BOOKTYPE'),
                EditorComponentFactory::TYPE_FIELD, $matches);
            if ($matches[0]) {
                $btype['type'] = 'webcombo';
                $btype['ptype'] = 'webcombo';
                $btype['validlabels'] = array_keys($bookTypesPair);
                $btype['validvalues'] = array_values($bookTypesPair);
                $matches[0]['type'] = $btype;
            }
        }
    }

    /**
     * @param string[] $var
     * @param string $key
     * @param string $bookid
     *
     * @return bool
     */
    private function setBookTypeNdPrepMap(&$var, $key, $bookid)
    {
        if (!isset($this->allowedBookTypeMap[$key])) {
            return false;
        }
        $bookTypeUIMap = array(
            ACCRUAL_BOOK => "IA.ACCRUAL",
            CASH_BOOK => "IA.CASH",
            USR_BOOK => "IA.USER_DEFINED",
            GAAP_BOOK => "IA.GAAP",
            TAX_BOOK => "IA.TAX"
        );

        $val = I18N::getSingleToken($bookTypeUIMap[$key]);
        $var[$key] = $val;
        $this->bookIdLabelMap[$bookid] = $val;
        return true;
    }


    /**
     * @return array
     */
    private function getBookTypeKeyValuePair()
    {
        $bookTypesPair = array();
        $coPref = Profile::getCompanyCacheProperty('COMPANYPREF');
        $BookCategorymap = array(
            BOOKTYPE_USR => USR_BOOK,
            BOOKTYPE_GAAP => GAAP_BOOK,
            BOOKTYPE_TAX => TAX_BOOK,
        );
        foreach ($coPref['BOOKTYPEMAP'] as $bookid => $booktype) {
            if ($bookid == ACCRUAL_BOOK || $bookid == CASH_BOOK) {
                $this->setBookTypeNdPrepMap($bookTypesPair, $bookid, $bookid);
            } else {
                $this->setBookTypeNdPrepMap($bookTypesPair, $BookCategorymap[$booktype], $bookid);
            }
        }
        return $bookTypesPair;
    }

    /**
     * @param object $view
     * @param array $obj
     */
    protected function mediateDataAndMetaDataBkJrnlComponent($view, &$obj)
    {
        $journalMatches = array();
        $journalMgr = Globals::$g->gManagerFactory->getManager('journal');
        $bookId = "";
        $booktypeDbVal = "";
        $bookDetails = $journalMgr->getJournalBookDetails($obj['JOURNAL']);
        if ($bookDetails) {
            $bookId = $bookDetails[0]["BOOKID"];
            $booktypeDbVal = $bookDetails[0]["BOOKTYPE"];
        }
        $view->findComponents(array('path' => 'JOURNAL'),
            EditorComponentFactory::TYPE_FIELD, $journalMatches);

        if ($journalMatches) {
            $journalMatches[0]->setProperty('hidden', false);
            $jrnlMatchType = $journalMatches[0]->getProperty('type');

            /*Adding additional filter to Journal since bookid is already available. Hence we should display
               only those journal which are linked with selected bookid.*/
            if (isset($bookId)) {
                if (!$this->isAdjGlBatchAllowed) {
                    array_push($jrnlMatchType['restrict'], array('pickField' => 'ADJ', 'value' => 'F'));
                }

                if (isset(array_flip($this->allowedBookTypeMap)[$booktypeDbVal])) {
                    array_push($jrnlMatchType['restrict'], array(
                        'operand' => "IN",
                        'value' => array($booktypeDbVal),
                        'pickField' => 'BOOKTYPE'
                    ));
                }

                /* If a book is of <ACCRUAL_BOOK> or <CASH_BOOK> type then both will have booktype as
                 * <BOOKTYPE_STD_VAL>. So to distinguish between them we need addition filter.
                 * Hence we have used BOOKID.
                */
                if ($booktypeDbVal === BOOKTYPE_STD_VAL) {
                    $jrnlMatchType['restrict'][] = array('pickField' => 'BOOKID', 'value' => $bookId);
                }
                $journalMatches[0]->setProperty(array('type', 'restrict'), $jrnlMatchType['restrict']);
            }
        }

        $bookMatches = array();
        $view->findComponents(array('path' => 'BOOKTYPE'),
            EditorComponentFactory::TYPE_FIELD, $bookMatches);

        /* $isBookFldVisibleAllowed flg - In static journal we don't need BookType field. */
        if ($bookMatches) {
            $bookMatches[0]->setProperty('hidden', !$this->isBookFldVisibleAllowed());
            if ($bookId) {
                $obj["BOOKTYPE"] = $this->bookIdLabelMap[$bookId] ?? "";
            }
        }
    }

    /**
     * @param array $vars
     */
    protected function appendEditorGlobalsBookType(&$vars)
    {
        $vars['BOOKTYPE_LABEL_VAL'] = array(
            "ACCURAL" => array("label" => i18n::getSingleToken('IA.ACCRUAL'), "value" => BOOKTYPE_STD_VAL),
            "CASH" => array("label" => i18n::getSingleToken('IA.CASH'), "value" => BOOKTYPE_STD_VAL),
            "TAX" => array("label" => i18n::getSingleToken('IA.TAX'), "value" => BOOKTYPE_TAX_VAL),
            "GAAP" => array("label" => i18n::getSingleToken('IA.GAAP'), "value" => BOOKTYPE_GAAP_VAL),
            "USR" => array("label" => i18n::getSingleToken('IA.USER_DEFINED'), "value" => BOOKTYPE_USR_VAL),
        );
    }
}