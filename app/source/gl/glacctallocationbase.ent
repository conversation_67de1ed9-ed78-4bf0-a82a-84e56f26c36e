<?
/**
 *    FILE:        glacctallocationbase.ent
 *    AUTHOR:        Harish B
 *    DESCRIPTION:    entity definition for allocation object
 *
 *    (C) 2018, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

$kSchemas['glacctallocationbase'] = array(

    'children' => array(
        'glacctallocationtarget' => array(
            'fkey' => 'record#',
            'invfkey' => 'glacctallocationkey',
            'table' => 'glacctallocationtarget',
            'join' => 'outer',
            'children' => array(
                'basejournal' => array(
                    'fkey' => 'journalkey',
                    'table' => 'basejournal',
                    'join' => 'outer'),
            ),
        ),
        'glacctallocationsource' => array(
            'fkey' => 'record#',
            'invfkey' => 'glacctallocationkey',
            'table' => 'glacctallocationsource',
            'join' => 'outer',
        ),
        'glacctallocationbasis' => array(
            'fkey' => 'record#',
            'invfkey' => 'glacctallocationkey',
            'table' => 'glacctallocationbasis',
            'join' => 'outer',
        ),
        'glacctallocationreverse' => array(
            'fkey' => 'record#',
            'invfkey' => 'glacctallocationkey',
            'table' => 'glacctallocationreverse',
            'join' => 'outer',
        ),
        'userinfo' => array(
            'fkey' => 'createdby',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'userinfomst'
        ),
        'userinfomodifiedby' => array(
            'fkey' => 'modifiedby',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'userinfomst',
        ),
        'glacctallocationrunbatch' => array(
            'fkey' => 'record#',
            'invfkey' => 'allocdefrecno',
            'table' => 'glacctallocationrunbatch',
            'join' => 'outer',
        ),
    ),
    'nexus' => array(
        'glacctallocationsource' => array(
            'object' => 'glacctallocationsource',
            'relation' => ONE2ONE,
            'field' => 'RECORDNO'
        ),
        'glacctallocationbasis' => array(
            'object' => 'glacctallocationbasis',
            'relation' => ONE2ONE,
            'field' => 'RECORDNO'
        ),
        'glacctallocationtarget' => array(
            'object' => 'glacctallocationtarget',
            'relation' => ONE2ONE,
            'field' => 'RECORDNO'
        ),
        'glacctallocationreverse' => array(
            'object' => 'glacctallocationreverse',
            'relation' => ONE2ONE,
            'field' => 'RECORDNO'
        )
    ),
    // object
    'object' => array(
        'RECORDNO',
        'ACCTALLOCATIONID',
        'NOOFPOSTEDBATCH',
        'MAXPOSTEDBATCHDATE',
        'DESCRIPTION',
        'METHODOLOGY',
        'STATUS',
        'ACTIVITYDELTA',
        'AUTOREVERSEPRIORPOSTEDJE',
        'FOCUSLOCATION',
        'FOCUSDEPARTMENT',
        'FOCUSPROJECT',
        'FOCUSCUSTOMER',
        'FOCUSVENDOR',
        'FOCUSEMPLOYEE',
        'FOCUSITEM',
        'FOCUSCLASS',
        'FOCUSCONTRACT',
        'FOCUSWAREHOUSE',
        'LATESTVERSIONKEY',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'POSTINGJOURNAL',
        'JOURNALKEY',
        'ALLOWALLOCATION',
        'CREATEDBYUSERDESC',
        'MODIFIEDBYUSERDESC'
    ),
    // schema
    'schema' => array(
        'RECORDNO' => 'record#',
        'ACCTALLOCATIONID' => 'acctallocationid',
        'NOOFPOSTEDBATCH' => 'glacctallocationrunbatch.noofpostedbatch',
        'MAXPOSTEDBATCHDATE' => 'glacctallocationrunbatch.maxpostedbatchdate',
        'ALLOCTYPE' => 'alloctype',
        'DESCRIPTION' => 'description',
        'METHODOLOGY' => 'methodology',
        'STATUS' => 'status',
        'ACTIVITYDELTA' => 'activitydelta',
        'AUTOREVERSEPRIORPOSTEDJE' => 'autoreversepriorpostedje',
        'FOCUSLOCATION' => 'focuslocation',
        'FOCUSDEPARTMENT' => 'focusdepartment',
        'FOCUSPROJECT' => 'focusproject',
        'FOCUSCUSTOMER' => 'focuscustomer',
        'FOCUSVENDOR' => 'focusvendor',
        'FOCUSEMPLOYEE' => 'focusemployee',
        'FOCUSCLASS' => 'focusclass',
        'FOCUSITEM' => 'focusitem',
        'FOCUSCONTRACT' => 'focuscontract',
        'FOCUSWAREHOUSE' => 'focuswarehouse',
        'LATESTVERSIONKEY' => 'latestversionkey',
        'LOCATIONKEY' => 'locationkey',
        'DEPTKEY' => 'deptkey',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'POSTINGJOURNAL' => 'basejournal.symbol',
        'JOURNALKEY' => 'glacctallocationtarget.journalkey',
        'ALLOWALLOCATION' => 'allowallocation',
        'CREATEDBYUSERDESC' => 'userinfo.description',
        'MODIFIEDBYUSERDESC' => 'userinfomodifiedby.description'
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'GLACCTALLOCATIONKEY', 'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity' => 'glacctallocationsource',
            'path' => 'GLACCTALLOCATIONSOURCE'
        ),
        array(
            'fkey' => 'GLACCTALLOCATIONKEY', 'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity' => 'glacctallocationbasis',
            'path' => 'GLACCTALLOCATIONBASIS'
        ),
        array(
            'fkey' => 'GLACCTALLOCATIONKEY', 'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity' => 'glacctallocationtarget',
            'path' => 'GLACCTALLOCATIONTARGET'
        ),
        array(
            'fkey' => 'GLACCTALLOCATIONKEY', 'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity' => 'glacctallocationreverse',
            'path' => 'GLACCTALLOCATIONREVERSE'
        )
    ),
    // fieldinfo
    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'fullname' => 'IA.ALLOCATION_ID',
            'desc' => 'IA.NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => $gLocationIDFormat
            ),
            'required' => true,
            'path' => 'ACCTALLOCATIONID',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.DESCRIPTION',
            'desc' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200
            ),
            'required' => true,
            'path' => 'DESCRIPTION',
            'id' => 3
        ),
        array(
            'fullname' => 'IA.METHODOLOGY',
            'desc' => 'IA.METHODOLOGY',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext',
                'maxlength' => 2000,
            ),
            'required' => false,
            'path' => 'METHODOLOGY',
            'id' => 4
        ),
        $gStatusFieldInfo,
        array(
            'fullname' => 'IA.LATEST_VERSION_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
            ),
            'desc' => 'IA.LATEST_VERSION_KEY',
            'path' => 'LATESTVERSIONKEY',
            'id' => 5,
            'readonly' => true
        ),
        array(
            'fullname' => 'IA.LOCATION',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES',  'IA.ALLOCATION_FOCUS'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED,  GLAcctAllocationManager::ALONG_FOR_RIDE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL , GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_LOCATION',
            'path' => 'FOCUSLOCATION',
            'hidden' => true,
            'id' => 6
        ),
        array(
            'fullname' => 'IA.DEPARTMENT',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES',  'IA.ALLOCATION_FOCUS'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED,  GLAcctAllocationManager::ALONG_FOR_RIDE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL , GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_DEPARTMENT',
            'path' => 'FOCUSDEPARTMENT',
            'hidden' => true,
            'id' => 7
        ),
        array(
            'fullname' => 'IA.PROJECT',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES', 'IA.ALLOCATION_FOCUS', 'IA.PER_DIMENSION_VALUE'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED, GLAcctAllocationManager::ALONG_FOR_RIDE, GLAcctAllocationBaseManager::PER_DIMENSION_VALUE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL, GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL, GLAcctAllocationBaseManager::PER_DIMENSION_VALUE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_PROJECT',
            'path' => 'FOCUSPROJECT',
            'hidden' => true,
            'id' => 8
        ),
        array(
            'fullname' => 'IA.CUSTOMER',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES',  'IA.ALLOCATION_FOCUS'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED,  GLAcctAllocationManager::ALONG_FOR_RIDE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL , GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_CUSTOMER',
            'path' => 'FOCUSCUSTOMER',
            'hidden' => true,
            'id' => 9
        ),
        array(
            'fullname' => 'IA.VENDOR',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES',  'IA.ALLOCATION_FOCUS'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED,  GLAcctAllocationManager::ALONG_FOR_RIDE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL , GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_VENDOR',
            'path' => 'FOCUSVENDOR',
            'hidden' => true,
            'id' => 10
        ),
        array(
            'fullname' => 'IA.EMPLOYEE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES', 'IA.ALLOCATION_FOCUS', 'IA.PER_DIMENSION_VALUE'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED, GLAcctAllocationManager::ALONG_FOR_RIDE, GLAcctAllocationBaseManager::PER_DIMENSION_VALUE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL, GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL, GLAcctAllocationBaseManager::PER_DIMENSION_VALUE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_EMPLOYEE',
            'path' => 'FOCUSEMPLOYEE',
            'hidden' => true,
            'id' => 11
        ),
        array(
            'fullname' => 'IA.CLASS',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES',  'IA.ALLOCATION_FOCUS'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED,  GLAcctAllocationManager::ALONG_FOR_RIDE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL , GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_CLASS',
            'path' => 'FOCUSCLASS',
            'hidden' => true,
            'id' => 12
        ),
        array(
            'fullname' => 'IA.ITEM',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED', 'IA.PRESERVE_VALUES',  'IA.ALLOCATION_FOCUS'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED, GLAcctAllocationManager::PRESERVED,  GLAcctAllocationManager::ALONG_FOR_RIDE),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL, GLAcctAllocationManager::PRESERVED_INTERNAL , GLAcctAllocationManager::ALONG_FOR_RIDE_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_ITEM',
            'hidden' => true,
            'path' => 'FOCUSITEM',
            'id' => 13
        ),
        array(
            'fullname' => 'IA.CONTRACT',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_CONTRACT',
            'hidden' => true,
            'path' => 'FOCUSCONTRACT',
            'id' => 14
        ),
        array(
            'fullname' => 'IA.WAREHOUSE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
                'validlabels' => array('IA.NOT_CONSIDERED'),
                'validvalues' => array(GLAcctAllocationManager::NOT_INVITED),
                '_validivalues' => array(GLAcctAllocationManager::NOT_INVITED_INTERNAL),
            ),
            'default' => GLAcctAllocationManager::NOT_INVITED,
            'desc' => 'IA.FOCUS_WAREHOUSE',
            'hidden' => true,
            'path' => 'FOCUSWAREHOUSE',
            'id' => 15
        ),
        array(
            'path' => 'LOCATIONKEY',
            'fullname' => 'IA.LOCATION_AT',
            'desc' => 'IA.MEGA_LOCATION_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer'
            ),
            'id' => 16
        ),
        array(
            'path' => 'DEPTKEY',
            'fullname' => 'IA.DEPT_AT',
            'desc' => 'IA.MEGA_DEPARTMENT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer'
            ),
            'id' => 17
        ),
        array(
            'fullname' => 'IA.DIMENSIONS',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
            ),
            'desc' => 'IA.TARGET_OVERRIDE_DIMENSION',
            'path' => 'TARGET_OVERRIDEDIMENSION',
            'id' => 18
        ),
        array(
            'fullname' => 'IA.NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.TARGET_OVERRIDE_DIMENSION_NAME',
            'path' => 'TARGET_OVERRIDEDIMENSION_VALUE',
            'id' => 19
        ),
        array(
            'fullname' => 'IA.DIMENSIONS',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'enum',
            ),
            'desc' => 'IA.REVERSE_OVERRIDE_DIMENSION',
            'path' => 'REVERSE_OVERRIDEDIMENSION',
            'id' => 20
        ),
        array(
            'fullname' => 'IA.NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.REVERSE_OVERRIDE_DIMENSION_NAME',
            'path' => 'REVERSE_OVERRIDEDIMENSION_VALUE',
            'id' => 21
        ),
        array(
            'path' => 'SOURCECALCLINK',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 80
            ),
            'isHTML' => true,
            'id' => 22
        ),
        array(
            'path' => 'BASISCALCLINK',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 80
            ),
            'isHTML' => true,
            'id' => 23
        ),
        array(
            'fullname' => 'IA.ACTIVITY_DELTA',
            'type'     => $gBooleanType,
            'default' => false,
            'desc' => 'IA.ACTIVITY_DELTA',
            'path' => 'ACTIVITYDELTA',
            'id' => 24
        ),
        array(
            'fullname' => 'IA.AUTOREVERSE_PRIOR_POST',
            'type'     => $gBooleanType,
            'default' => false,
            'desc' => 'IA.AUTO_REVERSE_PRIOR_POST',
            'path' => 'AUTOREVERSEPRIORPOSTEDJE',
            'id' => 25
        ),
        array(
            'path' => 'SUPDOCID',
            'fullname' => 'IA.ATTACHMENT',
            'desc' => 'IA.ATTACHMENT',
            'type' => array(
                'type' => 'supdocptr',
                'ptype' => 'supdocptr',
                'maxlength' => 30,
                'width' => 695,
                'height' => 440,
            ),
            'id' => 26
        ),
        array(
            'fullname' => 'IA.ALLOW_ALLOCATIONS',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'enum',
                'validlabels' => [ 'IA.WITHIN_ONE_ENTITY', 'IA.ACROSS_ENTITIES' ],
                'validvalues' => [
                    GLAcctAllocationManager::ALLOWALLOCATION_WITHINENTITY,
                    GLAcctAllocationManager::ALLOWALLOCATION_ACROSSENTITIES
                ],
                '_validivalues' => [
                    GLAcctAllocationManager::ALLOWALLOCATION_WITHINENTITY_INTERNAL,
                    GLAcctAllocationManager::ALLOWALLOCATION_ACROSSENTITIES_INTERNAL
                ],
            ),
            'default' => GLAcctAllocationManager::ALLOWALLOCATION_WITHINENTITY,
            'desc' => 'IA.ALLOW_ALLOCATIONS',
            'path' => 'ALLOWALLOCATION',
            'hidden' => true,
            'id' => 27
        ),
        array (
            'path' => 'TRUEUP',
            'fullname' => 'IA.TRUEUP',
            'type' => array (
                'ptype' => 'radio',
                'type' => 'text',
                'validlabels' => array( 'IA.ACTIVITY_DELTA', 'IA.AUTO_REVERSE_PRIOR_POST', 'IA.NONE' ),
                'validvalues' => array(GLAcctAllocationManager::TRUEUP_DELTA, GLAcctAllocationManager::TRUEUP_AUTOREVERSE, GLAcctAllocationManager::TRUEUP_NONE),
                '_validivalues' => array('A', 'T', 'N')
            ),
            'events' => array(
                'change' => 'adjustTimePeriodRestriction(this.meta);',
            ),
            'isHTML' => true,
            'default' => GLAcctAllocationManager::TRUEUP_NONE
        ),
        array (
            'path' => 'SOURCEDIMENSIONFILTER_VIEWMODE',
            'fullname' => 'IA.DIMENSION_FILTERS',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'isHTML' => true,
            'readonly' => true
        ),
        array (
            'path' => 'DROPNEGATIVE_VIEWMODE',
            'fullname' => 'IA.DROP_NEGATIVE_BASIS_LINES_FROM_CONSIDERATION',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'isHTML' => true,
            'readonly' => true
        ),
        array (
            'path' => 'BASISDIMENSIONFILTER_VIEWMODE',
            'fullname' => 'IA.DIMENSION_FILTERS',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
            ),
            'isHTML' => true,
            'readonly' => true
        ),
        array(
            'fullname' => 'IA.ALLOCATION_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array( 'IA.DYNAMIC_ALLOCATION', 'IA.RESTRICTION_RELEASE', ),
                'validvalues' => array(
                    GLAcctAllocationBaseManager::TYPE_DYNAMIC_ALLOC,
                    GLAcctAllocationBaseManager::TYPE_RESTRICTION_RELEASE
                ),
                '_validivalues' => array(
                    GLAcctAllocationBaseManager::INTERNAL_TYPE_DYNAMIC_ALLOC,
                    GLAcctAllocationBaseManager::INTERNAL_TYPE_RESTRICTION_RELEASE,
                )
            ),
            'default' => false,
            'desc' => 'IA.ALLOCATION_TYPE',
            'path' => 'ALLOCTYPE',
            'id' => 28
        ),
        array(
            'fullname' => 'IA.JOURNAL',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.JOURNAL',
            'path' => 'POSTINGJOURNAL',
            'id' => 29
        ),
        array(
            'path'      => 'CREATEDBYUSERDESC',
            'fullname'  => 'IA.CREATED_BY',
            'type'      => array(
                'ptype'     => 'text',
                'type'      => 'text',
            ),
            'readonly'  => true,
            'noapiadd' => true,
            'noapiset' => true
        ),
        array(
            'path'      => 'MODIFIEDBYUSERDESC',
            'fullname'  => 'IA.MODIFIED_BY',
            'type'      => array(
                'ptype'     => 'text',
                'type'      => 'text',
            ),
            'readonly'  => true,
            'noapiadd' => true,
            'noapiset' => true
        ),
        [
            'path' => 'NOOFPOSTEDBATCH',
            'fullname' => 'IA.KEY_POSTED',
            'type'      =>  array (
                'ptype'     =>  'integer',
                'type'      =>  'integer',
                'size'      =>  15,
                'maxlength' =>  15,
                'format'    => $gRecordNoFormat
            ),
            'readonly'  => true,
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 30
        ],
        [
            'path'      => 'MAXPOSTEDBATCHDATE',
            'fullname'  => 'IA.POSTED_DATE',
            'type'      => [
                'ptype'     => 'date',
                'type'      => 'date',
                'maxlength' => 12,
            ],
            'readonly'  =>  true,
            'noapiadd'  =>  true,
            'noapiset'  =>  true,
            'id'        => 31,
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
    'autoincrement' => 'RECORDNO',
    'table' => 'glacctallocation',
    'printas' => 'IA.GL_ACCOUNT_ALLOCATION',
    'pluralprintas' => 'IA.GL_ACCOUNT_ALLOCATIONS',
    'module' => 'gl',
    'vid' => 'RECORDNO',
    'customComponentsEntity' => 'glacctallocation',
    'platform_entity' => 'glacctallocation',
    'api' => array(
        'GETNAME_BY_GET' => true, // Use individual Get instead of GetList
        'GET_BY_GET' => true,
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED',
    ),
    'auditcolumns' => true,
    'addauditloginids' => true,
    'upsertEntries' => true,
    'dbfilters' => array(array('latestversionkey', 'ISNULL')),
    'ignoredimensions' => GLAcctAllocationBaseManager::NOT_SUPPORTED_DIMENSION_LIST,
    'description' => 'IA.HEADER_AND_DETAIL_INFORMATION_FOR_DEFINED_ACCOUNT',
);
