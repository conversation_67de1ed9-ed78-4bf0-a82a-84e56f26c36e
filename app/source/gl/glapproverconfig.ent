<?php
/**
 * glapproverconfig.ent
 *
 * Entity for glapproverconfig object. Used for GL Setup Approval Configuration.
 *
 * <AUTHOR> <raja.muth<PERSON><PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */

$kSchemas['glapproverconfig'] = [
    'children'        => [
        'journal'               => [
            'fkey'   => 'journalkey',
            'invkey' => 'record#',
            'table'  => 'basejournal',
            'join'   => 'outer',
        ],
        'primary_approver'         => [
            'fkey'   => 'primaryapprover',
            'invkey' => 'record#',
            'table'  => 'userinfomst',
            'join'   => 'outer',
        ],
        'alternate_approver'       => [
            'fkey'   => 'alternateapprover',
            'invkey' => 'record#',
            'table'  => 'userinfomst',
            'join'   => 'outer',
        ],
        'primary_approver_group'   => [
            'fkey'   => 'primaryapprovergroup',
            'invkey' => 'record#',
            'table'  => 'ugroup',
            'join'   => 'outer',
        ],
        'alternate_approver_group' => [
            'fkey'   => 'alternateapprovergroup',
            'invkey' => 'record#',
            'table'  => 'ugroup',
            'join'   => 'outer',
        ],
    ],
    'object'          => [
        'RECORDNO',
        'LINENUMBER',
        'JOURNALKEY',
        'LOCATIONKEY',
        'APPROVALLEVEL',
        'PRIMARYAPPROVER',
        'PRIMARYAPPROVERGROUP',
        'PRIMARYAPPROVERTYPE',
        'ALTERNATEAPPROVER',
        'ALTERNATEAPPROVERGROUP',
        'ALTERNATEAPPROVERTYPE',
        'OUTLIERASSISTANT',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ],
    'schema' => [
        'RECORDNO'               => 'record#',
        'LINENUMBER'             => 'linenumber',
        'JOURNALKEY'              => 'journalkey',
        'LOCATIONKEY'            => 'locationkey',
        'APPROVALLEVEL'          => 'approvallevel',
        'PRIMARYAPPROVER'        => 'primaryapprover',
        'PRIMARYAPPROVERGROUP'   => 'primaryapprovergroup',
        'PRIMARYAPPROVERTYPE'    => 'primaryapprovertype',
        'ALTERNATEAPPROVER'      => 'alternateapprover',
        'ALTERNATEAPPROVERGROUP' => 'alternateapprovergroup',
        'ALTERNATEAPPROVERTYPE'  => 'alternateapprovertype',
        'OUTLIERASSISTANT'       => 'outlierassistant',
        'WHENCREATED'            => 'whencreated',
        'WHENMODIFIED'           => 'whenmodified',
        'CREATEDBY'              => 'createdby',
        'MODIFIEDBY'             => 'modifiedby',
    ],
    'fieldinfo'       => [
        [
            'path'     => 'JOURNALKEY',
            'fullname' => 'IA.JOURNAL_ENTRY',
            'desc'     => 'IA.JOURNAL_ENTRY',
            'type'     => ['ptype' => 'ptr', 'type' => 'ptr', 'entity' => 'basejournal'],
        ],
        [
            'path'     => 'PRIMARY_APPROVER',
            'fullname' => 'IA.PRIMARY_APPROVER',
            'desc'     => 'IA.PRIMARY_APPROVER',
            'type'     => ['ptype' => 'ptr', 'type' => 'ptr', 'entity' => 'userinfo'],
        ],
        [
            'path'     => 'ALTERNATE_APPROVER',
            'fullname' => 'IA.ALTERNATE_APPROVER',
            'desc'     => 'IA.ALTERNATE_APPROVER',
            'type'     => ['ptype' => 'ptr', 'type' => 'ptr', 'entity' => 'userinfo'],
        ],
        [
            'path'     => 'PRIMARY_APPROVER_GROUP',
            'fullname' => 'IA.PRIMARY_APPROVER_GROUP',
            'desc'     => 'IA.PRIMARY_APPROVER_GROUP',
            'type'     => ['ptype' => 'ptr', 'type' => 'ptr', 'entity' => 'ugroup'],
        ],
        [
            'path'     => 'ALTERNATE_APPROVER_GROUP',
            'fullname' => 'IA.ALTERNATE_APPROVER_GROUP',
            'desc'     => 'IA.ALTERNATE_APPROVER_GROUP',
            'type'     => ['ptype' => 'ptr', 'type' => 'ptr', 'entity' => 'ugroup'],
        ],
        [
            'path'      => 'OUTLIERASSISTANT',
            'fullname'  => 'IA.OUTLIER_ASSISTANT',
            'desc'      => 'IA.OUTLIER_ASSISTANT',
            'type'      => Globals::$g->gBooleanType,
            'default'  => 'false',
        ],
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ],
    'table'           => 'glapproverconfig',
    'module'          => 'gl',
    'vid'             => 'RECORDNO',
    'autoincrement'   => 'RECORDNO',
    'auditcolumns'    => true,
    'printas'         => 'IA.GL_APPROVER_CONFIG',
    'pluralprintas'         => 'IA.GL_APPROVER_CONFIGS',
    'sqldomarkup'     => true,
    'sqlmarkupfields' => [
        'WHENMODIFIED',
        'WHENCREATED',
    ],
];
