<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XML Spy v4.0 U (http://www.xmlspy.com) by <PERSON><PERSON> (Intacct Corporation) -->
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
<xsl:import href="../../private/xslinc/report_helpers.xsl"/>                                                                                                            

<xsl:output encoding="UTF-8"/>

<xsl:param name="disableFloatHeader">N</xsl:param>

<xsl:variable name="VarMultiCurrency">
	<xsl:value-of select="/reportdata/report/@multicurrency"/>
</xsl:variable>

<xsl:variable name="varConsolidationBook">
    <xsl:value-of select="/reportdata/report/@consolidationreport"/>
</xsl:variable>

<xsl:variable name="isBookPostingToCSNEntryTable">
    <xsl:value-of select="/reportdata/report/@isBookPostingToCSNEntryTable"/>
</xsl:variable>

<xsl:variable name="consolidationMemo">
    <xsl:value-of select="/reportdata/report/@consolidationMemo"/>
</xsl:variable>
<!-- We added this var to show transaction amount column, based on below logic when company is non MCP -->
<!-- and in Report Filter the reporting book selected is consolidation book -->   
<xsl:variable name="varNonMultiCurrencyConsolidation">
    <xsl:choose>
        <xsl:when test="($varConsolidationBook = 'true') and ($VarMultiCurrency != 'true')">T</xsl:when>
        <xsl:otherwise>F</xsl:otherwise>
    </xsl:choose>
</xsl:variable>
    
<xsl:variable name="varMCColspan">
	<xsl:choose>
		<xsl:when test="($VarMultiCurrency = 'true')">2</xsl:when>
        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">1</xsl:when>
        <xsl:otherwise>0</xsl:otherwise>
	</xsl:choose>
</xsl:variable>

<xsl:variable name="memowidth">
	<xsl:choose>
		<xsl:when test="($VarMultiCurrency = 'true')">0</xsl:when>
        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">1</xsl:when>
        <xsl:otherwise>5</xsl:otherwise>
	</xsl:choose>
</xsl:variable>

<xsl:variable name="nonmcpwidth">
	<xsl:choose>
		<xsl:when test="($VarMultiCurrency = 'true')">0</xsl:when>
        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">1</xsl:when>
        <xsl:otherwise>2</xsl:otherwise>
	</xsl:choose>
</xsl:variable>

<xsl:variable name="repeatAccountOnLines">
    <xsl:value-of select="/reportdata/report/@repeatAccountOnLines"/>
</xsl:variable>
<!-- variable to determine the report type when request is from process&store / email -->
<xsl:variable name="backgroundformat">
    <xsl:value-of select="/reportdata/report/@offreporttype"/>
</xsl:variable>

<xsl:variable name = "transactionTitle">
	<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
		<xsl:value-of select="/reportdata/report/@showTransactionNumberTitle"/>
	</xsl:if>
</xsl:variable>

<xsl:variable name = "transactionTitleCount">
	<xsl:choose>
		<xsl:when test="(/reportdata/report/@showTransactionNumber = 'true')">1</xsl:when>
		<xsl:otherwise>0</xsl:otherwise>
	</xsl:choose>
</xsl:variable>

<xsl:variable name = "showMatchingCode">
    <xsl:choose>
        <xsl:when test="(/reportdata/report/@showMatchingCode = 'T')">1</xsl:when>
        <xsl:otherwise>0</xsl:otherwise>
    </xsl:choose>
</xsl:variable>

    <xsl:variable name="columnAdjust">
        <xsl:choose>
            <xsl:when test="(/reportdata/report/@showdetail = 'T')">8</xsl:when>
            <xsl:otherwise>6</xsl:otherwise>
        </xsl:choose>
    </xsl:variable>

    <xsl:variable name="locdeptflag">
        <xsl:value-of select="/reportdata/report/@locdeptflag"/>
    </xsl:variable>
    
    <xsl:variable name="default_column">
        <xsl:choose>
            <xsl:when test="(/reportdata/report/@showdetail = 'T')">
                <xsl:choose>
                    <xsl:when test="($locdeptflag = 'none')">7</xsl:when>
                    <xsl:when test="($locdeptflag = 'loc')">8</xsl:when>
                    <xsl:when test="($locdeptflag = 'dept')">8</xsl:when>
                    <xsl:otherwise>9</xsl:otherwise>
                </xsl:choose>
            </xsl:when>
            <xsl:otherwise>
                <xsl:choose>
                    <xsl:when test="($locdeptflag = 'none')">6</xsl:when>
                    <xsl:when test="($locdeptflag = 'loc')">7</xsl:when>
                    <xsl:when test="($locdeptflag = 'dept')">7</xsl:when>
                    <xsl:otherwise>8</xsl:otherwise>
                </xsl:choose>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:variable>

	<xsl:variable name="dimcount">
		<xsl:value-of select="(/reportdata/report/@dimcount)"/>
	</xsl:variable>

	<xsl:variable name="totalColumns" select="$default_column + $dimcount + $transactionTitleCount + $showMatchingCode" />

    <!-- This decides the colspan for the element1 header. -->
    <!-- If none is selected then colspan=1 (because Posted date will be the second col)  -->
    <!-- If loc (or) dept is selected then colspan=2 (because Posted date will be the third col)  -->
    <!-- If loc-dept (or) dept-loc is selected then colspan=3 (because Posted date will be the fourth col)  -->
    <xsl:variable name="elementHeadAdjust">
        <xsl:choose>
            <xsl:when test="($locdeptflag = 'none')">1</xsl:when>
            <xsl:when test="($locdeptflag = 'dept') or ($locdeptflag = 'loc')">2</xsl:when>
            <xsl:otherwise>3</xsl:otherwise>
        </xsl:choose>
    </xsl:variable>
    <xsl:variable name="element1HeadText">
        <xsl:value-of select="/reportdata/report/@Element1Text"/>
    </xsl:variable>
    <xsl:variable name="element2HeadText">
        <xsl:value-of select="/reportdata/report/@Element2Text"/>
    </xsl:variable>
    <xsl:variable name="element2DataText">
        <xsl:value-of select="/reportdata/report/@Element2DataText"/>
    </xsl:variable>
    <xsl:variable name="element3HeadText">
        <xsl:value-of select="/reportdata/report/@Element3Text"/>
    </xsl:variable>
    <xsl:variable name="basecurr">
        <xsl:value-of select="/reportdata/report/@BASECURR"/>
    </xsl:variable>
	<xsl:variable name="varISMCME">
		<xsl:value-of select="/reportdata/report/@ISMCMESUBSCR"/>
	</xsl:variable>
    
    <xsl:variable name="pDateHeadText" select="'IA.POSTED_DATE_SHORT'"/>
    <xsl:variable name="dDateHeadText" select="'IA.DOC_DATE_SHORT'"/>
    <xsl:variable name="docHeadText" select="'IA.DOC'"/>
    <xsl:variable name="memoHeadText" select="'IA.MEMO_SLASH_DESCRIPTION'"/>
    <xsl:variable name="jnlHeadText" select="'IA.JOURNAL_SHORT'"/>
    <xsl:variable name="dbHeadText" select="'IA.DEBIT'"/>
    <xsl:variable name="crHeadText" select="'IA.CREDIT'"/>
    <xsl:variable name="balHeadText" select="'IA.BALANCE'"/>
    <xsl:variable name="balFwdText" select="'IA.BALANCE_FORWARD'"/>
    <xsl:variable name="balFwdText1" select="'IA.BALANCE_FORWARD_SHORT'"/>

	<!-- DIMENSION TEMPLATE -->
	<xsl:template match="dimensionHeader">
		<xsl:apply-templates select="dimension" mode='header'/>
	</xsl:template>

	<xsl:template match="dimension" mode='header'>
		<hcol width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
			 <xsl:value-of select="@hdrtitle"/>
        </hcol>
	</xsl:template>

	<xsl:template match="dimensionTrans">
		<xsl:apply-templates select="dimension" mode='trans'/>
	</xsl:template>

	<xsl:template match="dimension" mode='trans'>
		<col id="5" s="24" >
			<xsl:value-of select="@value"/>
        </col>
	</xsl:template>
	<!-- END DIMENSION TEMPLATE -->

	<xsl:variable name="docColSpan">
		<xsl:choose>
			<xsl:when test="(/reportdata/report/@orientation = 'Portrait')">2</xsl:when>
			<xsl:otherwise>0</xsl:otherwise>
		</xsl:choose>
	</xsl:variable>

    <xsl:variable name="backhere">
        <xsl:call-template name="escapeQuotes">
            <xsl:with-param name="strToEscapetxt" select="/reportdata/report/@backhere"/>
        </xsl:call-template>
    </xsl:variable>

    <xsl:template match="report">
        <report showHeader="{@showHeader}" department="{@deptname}" location="{@locname}" rptAcctSet="{@rptAcctSet}" rptAcctFooterText="{@rptAcctFooterText}"  origLocation="{@location}" reportingBook="{@reportingBook}" orientation="{@orientation}" report_date="{@date}" report_time="{@time}" align_currency="left" page_number="Y" action="gledger_rpt.phtml" sess="{@sess}" done="{@backhere}" footer_allpages="Y" wrap="Y" maxfit="Y" nofloathdr="{$disableFloatHeader}">
            <!-- enable fit to page if we have more than 2 dimension cols selected in the report -->
            <xsl:if test="($dimcount &gt; 2 or $locdeptflag != 'none' or /reportdata/report/@showTransactionNumber = 'true') and @fitToOnePage = 'T'">
                <xsl:attribute name="pagelayoutsize">custom</xsl:attribute>
                <xsl:attribute name="additionalColCount">
                    <xsl:choose>
                        <xsl:when test="($locdeptflag = 'none')">
                            <xsl:value-of select="($dimcount - 2) + $transactionTitleCount + $showMatchingCode"/>
                        </xsl:when>
                        <xsl:when test="($locdeptflag = 'loc') or ($locdeptflag = 'dept')">
                            <xsl:value-of select="($dimcount - 2) + $transactionTitleCount + 1 + $showMatchingCode"/>
                        </xsl:when>
                        <xsl:otherwise>
                            <xsl:value-of select="($dimcount - 2) + $transactionTitleCount + 2 + $showMatchingCode"/>
                        </xsl:otherwise>
                    </xsl:choose>
                    
                </xsl:attribute>
                <xsl:attribute name="dynamicColWidth">50</xsl:attribute>
            </xsl:if>
			<!-- Need to set the default alignment, required by to_pdf.xsl-->
            <custheader alignment="L"></custheader>
            <xsl:if test="(@locname != '')">
                <locationtitles>
                    <xsl:for-each select="locationtitles/custtitles">
						<xsl:if test="(. != '')">
							<custtitle><xsl:value-of select="."/></custtitle>
						</xsl:if>
                    </xsl:for-each>
                </locationtitles>
            </xsl:if>
            <xsl:if test="(@deptname != '')">
                <departmenttitles>
                     <xsl:for-each select="departmenttitles/custtitles">
						<xsl:if test="(. != '')">
							<custtitle><xsl:value-of select="."/></custtitle>
						</xsl:if>
                    </xsl:for-each>
                </departmenttitles>
            </xsl:if>
            <company s="2">
                <xsl:value-of select="@co"/>
            </company>
            <title s="3" titleNum="1">
                <xsl:value-of select="@title"/>
            </title>
            <title s="3" titleNum="2">
                <xsl:value-of select="@title2"/>
            </title>
            <title s="4">
                <xsl:value-of select="@period"/>
            </title>
            <footer s="5" lines="1" footerNum="1">
                <xsl:value-of select="@titlecomment"/>
            </footer>
            <footer s="5"/>
            <cpafooter s="5"/>
            <logo align="left"/>
            <xsl:for-each select="rtdim">
            	<rtdim name="@name">
            		<name><xsl:value-of select="@name"/></name>
					<value><xsl:value-of select="@value"/></value>
				</rtdim>
            </xsl:for-each>
            <header>
                <!-- This row has to be commented before check in -->
                <!-- Used by me to find out the inp params -->
                <!--hrow s="6"><hcol id="0" width="10" colspan="{$totalColumns - 1}">
                    Params are  :: 
                    showdetails = <xsl:value-of select="@showdetail"/> 
                    locdeptflag = <xsl:value-of select="@locdeptflag"/> 
                    totCols = <xsl:copy-of select="$totalColumns" />
                    element2datatext = <xsl:copy-of select="$element2DataText" />
                    showDept = [<xsl:copy-of select="$showDept" />]
                    showLoc = [<xsl:copy-of select="$showLoc" />]
                    showDeptLocActivity = [<xsl:value-of select="@showDeptLocActivity"/> ]
                    orientation = [<xsl:value-of select="@orientation"/> ]
                </hcol></hrow-->
                <hrow s="6">
                    <xsl:choose>
                        <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                            <hcol s="7"  width="8"></hcol>
                        </xsl:when>
                    </xsl:choose>
                    <xsl:choose>
                        <xsl:when test="(@showdetail = 'T')">
                            <!-- Showdetail is true, detailed mode , 11 column header-->
                            <!-- The first column is common to all the 3 cases-->
                            <!--hcol id="0" width="8" > <xsl:copy-of select="$element1HeadText" />  </hcol-->
                            <!-- END of first column is common to all the 3 cases-->
                            <xsl:choose>
                                <xsl:when test="(@locdeptflag = 'none')">
                                    <!-- CASE 1: locdeptflag is none , Element1 only is present-->
                                    <hcol s="7" id="0" width="8">
                                        <xsl:copy-of select="$pDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="1" width="8">
                                        <xsl:copy-of select="$dDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="2" width="{7 + $docColSpan}">
                                        <xsl:copy-of select="$docHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="3">
                                        <xsl:attribute name="width"><xsl:choose><xsl:when test="$dimcount >= 2"><xsl:value-of select="12+$memowidth"/></xsl:when><xsl:when test="$dimcount = 0"><xsl:value-of select="26+$memowidth"/></xsl:when><xsl:otherwise><xsl:value-of select="16+$memowidth"/></xsl:otherwise></xsl:choose></xsl:attribute>
                                        <xsl:copy-of select="$memoHeadText"/>
                                    </hcol>
									 
									<xsl:apply-templates select="dimensionHeader"/>
									
									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<hcol width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
											<xsl:copy-of select="$transactionTitle"/>
										</hcol>
									</xsl:if>

                                    <!-- END of CASE 1: locdeptflag is none , Element1 only is present-->
                                </xsl:when>
                                <xsl:when test="(@locdeptflag = 'loc') or (@locdeptflag = 'dept')">
                                    <!-- CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                                    <hcol id="0" width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
                                        <xsl:copy-of select="$element2HeadText"/>
                                    </hcol>
                                    <hcol s="7" id="1" width="8">
                                        <xsl:copy-of select="$pDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="2" width="8">
                                        <xsl:copy-of select="$dDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="3" width="{7 + $docColSpan}">
                                        <xsl:copy-of select="$docHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="4">
                                        <xsl:attribute name="width"><xsl:choose><xsl:when test="$dimcount >= 1"><xsl:value-of select="10+$memowidth"/></xsl:when><xsl:otherwise><xsl:value-of select="18+$memowidth"/></xsl:otherwise></xsl:choose></xsl:attribute>
                                        <xsl:copy-of select="$memoHeadText"/>
                                    </hcol>

									<xsl:apply-templates select="dimensionHeader"/>

									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<hcol width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
											<xsl:copy-of select="$transactionTitle"/>
										</hcol>
									</xsl:if>

                                    <!-- END of CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                                </xsl:when>
                                <xsl:otherwise>
                                    <!-- CASE 3:  Element1 & Element2 & Element 3 are present-->
                                    <hcol id="0" width="{9+$nonmcpwidth}" s="7" >
                                        <xsl:copy-of select="$element2HeadText"/>
                                    </hcol>
                                    <hcol id="1" width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
                                        <xsl:copy-of select="$element3HeadText"/>
                                    </hcol>
                                    <hcol s="7" id="2" width="8">
                                        <xsl:copy-of select="$pDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="3" width="8">
                                        <xsl:copy-of select="$dDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="4" width="{7 + $docColSpan}">
                                        <xsl:copy-of select="$docHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="5" width="{14 - $docColSpan + $memowidth}">
                                        <xsl:copy-of select="$memoHeadText"/>
                                    </hcol>
									
									<xsl:apply-templates select="dimensionHeader"/>

									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<hcol width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
											<xsl:copy-of select="$transactionTitle"/>
										</hcol>
									</xsl:if>

                                    <!-- END of CASE 3:  Element1 & Element2 & Element 3 are present-->
                                </xsl:otherwise>
                            </xsl:choose>
                            <!-- The last 4 columns are common to all the 3 cases-->
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <hcol s="7" width="5">
                                        IA.MATCHING_CODE
                                    </hcol>
                                </xsl:when>
                            </xsl:choose>
                            <hcol s="7" id="6" width="5">
                                <xsl:copy-of select="$jnlHeadText"/>
                            </hcol>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <hcol s="7" width="3" >IA.CURR</hcol>
                                    <hcol width="7" s="8">IA.TXN_AMT</hcol>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <hcol width="8" s="8">IA.TXN_AMT</hcol>
                                </xsl:when>
                            </xsl:choose>
                            <hcol id="7" width="11" s="8">
                                <xsl:copy-of select="$dbHeadText"/><xsl:if test="($varISMCME = 'true')"> (<xsl:copy-of select="$basecurr"/>)</xsl:if>
                            </hcol>
                            <hcol id="8" width="11" s="8">
                                <xsl:copy-of select="$crHeadText"/><xsl:if test="($varISMCME = 'true')"> (<xsl:copy-of select="$basecurr"/>)</xsl:if>
                            </hcol>
                            <hcol id="9" width="{9+$nonmcpwidth}" s="8">
                                <xsl:copy-of select="$balHeadText"/><xsl:if test="($varISMCME = 'true')"> (<xsl:copy-of select="$basecurr"/>)</xsl:if>
                            </hcol>
                            <!-- END of The last 4 columns are common to all the 3 cases-->
                            <!-- END of Showdetail is true, detailed mode , 11 column header-->
                        </xsl:when>
                        <xsl:otherwise>
                            <!-- Showdetail is not true, Summary mode , 9 column header-->
                            <!-- The first column is common to all the 3 cases-->
                            <!--hcol id="0" width="10" > <xsl:copy-of select="$element1HeadText" /> </hcol-->
                            <!-- END of The first column is common to all the 3 cases-->
                            <xsl:choose>
                                <xsl:when test="(@locdeptflag = 'none')">
                                    <!-- CASE 4: locdeptflag is none , Element1 only is present-->
                                    <hcol s="7" id="0" width="8">
                                        <xsl:copy-of select="$pDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="1" width="{7 + $docColSpan}">
                                        <xsl:copy-of select="$docHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="2">
                                        <xsl:attribute name="width"><xsl:choose><xsl:when test="$dimcount >= 2"><xsl:value-of select="13+$memowidth"/></xsl:when><xsl:when test="$dimcount = 0"><xsl:value-of select="33+$memowidth"/></xsl:when><xsl:otherwise><xsl:value-of select="23+$memowidth"/></xsl:otherwise></xsl:choose></xsl:attribute>
                                        <xsl:copy-of select="$memoHeadText"/>
                                    </hcol>
									
									<xsl:apply-templates select="dimensionHeader"/>

									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<hcol width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
											<xsl:copy-of select="$transactionTitle"/>
										</hcol>
									</xsl:if>

                                    <!-- END of CASE 4: locdeptflag is none , Element1 only is present-->
                                </xsl:when>
                                <xsl:when test="(@locdeptflag = 'loc') or (@locdeptflag = 'dept')">
                                    <!-- CASE 5: locdeptflag is loc or dept , Element1 abd Element2 are present-->
                                    <hcol id="0" width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
                                        <xsl:copy-of select="$element2HeadText"/>
                                    </hcol>
                                    <hcol s="7" id="1" width="8">
                                        <xsl:copy-of select="$pDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="2" width="{7 + $docColSpan}">
                                        <xsl:copy-of select="$docHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="3">
                                         <xsl:attribute name="width"><xsl:choose><xsl:when test="$dimcount >= 1"><xsl:value-of select="13+$memowidth"/></xsl:when><xsl:otherwise> 23 </xsl:otherwise></xsl:choose></xsl:attribute>
                                        <xsl:copy-of select="$memoHeadText"/>
                                    </hcol>
									
									<xsl:apply-templates select="dimensionHeader"/>
									
									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<hcol width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
											<xsl:copy-of select="$transactionTitle"/>
										</hcol>
									</xsl:if>

									<!-- END of CASE 5: locdeptflag is loc or dept , Element1 abd Element2 are present-->
                                </xsl:when>
                                <xsl:otherwise>
                                    <!-- CASE 6:  Element1 & Element2 & Element 3 are present-->
                                    <hcol id="0" width="{10+$nonmcpwidth}" s="7" dynamicCol='y'>
                                        <xsl:copy-of select="$element2HeadText"/>
                                    </hcol>
                                    <hcol id="1" width="{10+$nonmcpwidth}" s="7" dynamicCol='y'>
                                        <xsl:copy-of select="$element3HeadText"/>
                                    </hcol>
                                    <hcol s="7" id="2" width="8">
                                        <xsl:copy-of select="$pDateHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="3" width="{7 + $docColSpan}">
                                        <xsl:copy-of select="$docHeadText"/>
                                    </hcol>
                                    <hcol s="7" id="4" width="{19 - $docColSpan + $memowidth}">
                                        <xsl:copy-of select="$memoHeadText"/>
                                    </hcol>
									
									<xsl:apply-templates select="dimensionHeader"/>

									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<hcol width="{9+$nonmcpwidth}" s="7" dynamicCol='y'>
											<xsl:copy-of select="$transactionTitle"/>
										</hcol>
									</xsl:if>

                                    <!-- CASE 6:  Element1 & Element2 & Element 3 are present-->
                                </xsl:otherwise>
                            </xsl:choose>
                            <!-- The last 4 columns are common to all the 3 cases-->
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <hcol s="7" width="5">
                                        IA.MATCHING_CODE
                                    </hcol>
                                </xsl:when>
                            </xsl:choose>
                            <hcol s="7" id="5" width="5">
                                <xsl:copy-of select="$jnlHeadText"/>
                            </hcol>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <hcol s="7" width="3" >IA.CURR</hcol>
                                    <hcol width="9" s="8">IA.TXN_AMT</hcol>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <hcol width="9" s="8">IA.TXN_AMT</hcol>
                                </xsl:when>
                            </xsl:choose>
                            <hcol id="6" s="8" width="11">
                                <xsl:copy-of select="$dbHeadText"/><xsl:if test="($varISMCME = 'true')"> (<xsl:copy-of select="$basecurr"/>)</xsl:if>
                            </hcol>
                            <hcol id="7" s="8" width="11">
                                <xsl:copy-of select="$crHeadText"/><xsl:if test="($varISMCME = 'true')"> (<xsl:copy-of select="$basecurr"/>)</xsl:if>
                            </hcol>
                            <hcol id="8" s="8" width="{12+$nonmcpwidth}">
                                <xsl:copy-of select="$balHeadText"/><xsl:if test="($varISMCME = 'true')"> (<xsl:copy-of select="$basecurr"/>)</xsl:if>
                            </hcol>
                            <!-- END of The last 4 columns are common to all the 3 cases-->
                            <!-- END of Showdetail is not true, Summary mode , 9 column header-->
                        </xsl:otherwise>
                    </xsl:choose>
                </hrow>
            </header>
            <body s="1">
                <xsl:apply-templates/>
                <row>
                    <!-- This colspan attribute decides the col span for Grand Total. If Detailed report then 8 columns, else 6 columns-->
                    <xsl:choose>
                        <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                            <col s="21">
                            </col>
                        </xsl:when>
                    </xsl:choose>
                    <col id="0" s="12" colspan="{$totalColumns - 2 + $varMCColspan}">
                        <xsl:text>IA.GRAND_TOTAL</xsl:text>
                    </col>
                    <col id="{$totalColumns - 3}" s="38">
                        <xsl:value-of select="@grtotdb"/>
                    </col>
                    <col id="{$totalColumns - 2}" s="38">
                        <xsl:value-of select="@grtotcr"/>
                    </col>
                    <col id="{$totalColumns - 1}" s="38">
                        <xsl:value-of select="@grtotbal"/>
                    </col>
                </row>
            </body>
            <xsl:call-template name="generatestyles"/>
            <xsl:call-template name="script"/>
        </report>
    </xsl:template>
    <xsl:template match="acct">
        <!-- This Row displays the Element 1 header-->
        <row>
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <col id="0" s="12" colspan="{$totalColumns + $varMCColspan}">
                <xsl:value-of select="@id"/>
                <xsl:text> - </xsl:text>
                <xsl:value-of select="@name"/> (<xsl:copy-of select="$balFwdText"/> As of <xsl:value-of select="@fwddate"/>)
        </col>
            <!-- the 'posted date' col id will depend on the report layout either 2 or 3 or 4-->
            <!--col id="{$elementHeadAdjust}" s="12" colspan="{$totalColumns - 3}">
            <xsl:copy-of select="$balFwdText" /> as on <xsl:value-of select="@fwddate"/>
        </col-->
            <!-- next column display is the Memo field. -->
            <!--col id="{$elementHeadAdjust + 2}" s="12" colspan="{$elementHeadAdjust}">
             <xsl:copy-of select="$balFwdText" />
        </col-->
            <col id="{$totalColumns - 1}" s="40">
                <xsl:value-of select="@fwd"/>
            </col>
        </row>
        <!-- This will display the transactions of element1,  if any -->
        <xsl:apply-templates/>
        <!-- This Row displays the Element 1 Totals-->
        <row>
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <col id="0" s="12" colspan="{$totalColumns - 2 + $varMCColspan}">
            Totals for <xsl:value-of select="@id"/>
                <xsl:text> - </xsl:text>
                <xsl:value-of select="@name"/>
            </col>
            <!-- the 'posted date' col id will depend on the report layout either 2 or 3 or 4-->
            <col id="{$totalColumns - 3}" s="37">
                <xsl:value-of select="@db"/>
            </col>
            <col id="{$totalColumns - 2}" s="37">
                <xsl:value-of select="@cr"/>
            </col>
            <col id="{$totalColumns - 1}" s="37">
                <xsl:value-of select="@bal"/>
            </col>
        </row>
		<row>
			<xsl:choose>
				<xsl:when test="(/reportdata/report/@shownetchange = 'T')">
                    <xsl:choose>
                        <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                            <col s="21">
                                <xsl:value-of select="@id"/>
                            </col>
                        </xsl:when>
                    </xsl:choose>
					<col id="0" s="32" colspan="{$totalColumns}">
                        <xsl:call-template name="string-replace">
                            <xsl:with-param name="haystack" select="'IA.NET_CHANGE_FOR'" />
                            <xsl:with-param name="replace" select="'${NET_CHANGE}'" />
                            <xsl:with-param name="by" select="@name" />
                        </xsl:call-template>
					</col>
					<col id="1" s="40">
						<xsl:value-of select="@nc"/>
					</col>				
				</xsl:when>
			</xsl:choose>
		</row>
        <!-- one empty row-->
        <row>
            <col id="0" s="31" colspan="{$totalColumns + 1 + $varMCColspan}"/>
        </row>
    </xsl:template>
    <!-- TEMPLATE TO DISPLAY Element 2 Header and totals -->
    <xsl:template match="element2">
        <!-- This Row displays the Element 2 header-->
        <row>
            <!--col id="0" s="15"> </col-->
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="ancestor::acct[1]/@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <col id="0" s="32" colspan="{$totalColumns - 3 + $varMCColspan}">
                <xsl:value-of select="@fullname"/>
            </col>
            <col id="{$totalColumns - 4}" s="32">
                <xsl:copy-of select="$balFwdText1"/>
            </col>
            <col id="{$totalColumns - 3}"/>
            <col id="{$totalColumns - 2}"/>
            <col id="{$totalColumns - 1}" s="40">
                <xsl:value-of select="@fwd"/>
            </col>
        </row>
        <!-- This will display the transactions of element2,  if any -->
        <xsl:apply-templates/>
        <!-- This Row displays the Element 2 Totals-->
        <row>
            <!--col id="0" s="15"> </col-->
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="ancestor::acct[1]/@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <col id="0" s="32" colspan="{$totalColumns - 2 + $varMCColspan}">
                <xsl:call-template name="string-replace">
                    <xsl:with-param name="haystack" select="'IA.TOTALS_FOR_ELEMENT_2'" />
                    <xsl:with-param name="replace" select="'${ELEMENT_2}'" />
                    <xsl:with-param name="by" select="@fullname" />
                </xsl:call-template>
            </col>
            <col id="{$totalColumns - 3}" s="40">
                <xsl:value-of select="@db"/>
            </col>
            <col id="{$totalColumns - 2}" s="40">
                <xsl:value-of select="@cr"/>
            </col>
            <col id="{$totalColumns - 1}" s="40">
                <xsl:value-of select="@bal"/>
            </col>
        </row>
		<row>
			<xsl:choose>
				<xsl:when test="(/reportdata/report/@shownetchange = 'T')">
                    <xsl:choose>
                        <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                            <col s="21">
                                <xsl:value-of select="ancestor::acct[1]/@id"/>
                            </col>
                        </xsl:when>
                    </xsl:choose>
                    <col id="0" s="32" colspan="{$totalColumns}">
                        <xsl:call-template name="string-replace">
                            <xsl:with-param name="haystack" select="'IA.NET_CHANGE_FOR'" />
                            <xsl:with-param name="replace" select="'${NET_CHANGE}'" />
                            <xsl:with-param name="by" select="@fullname" />
                        </xsl:call-template>
                    </col>
					<col id="1" s="40">
						<xsl:value-of select="@nc"/>
					</col>				
				</xsl:when>
			</xsl:choose>
		</row>
    </xsl:template>
    <xsl:template match="other">
        <!-- This Row displays the other header-->
        <row>
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="ancestor::acct[1]/@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <xsl:choose>
                <xsl:when test="(@level > '3')">
                    <col id="0" s="32"/>
                    <col id="0" s="32" colspan="{$totalColumns - 4 + $varMCColspan}">
                        <xsl:value-of select="@name"/>
                    </col>
                    <col id="{$totalColumns - 4}" s="32">
                        <xsl:copy-of select="$balFwdText1"/>
                    </col>
                    <col id="{$totalColumns - 3}"/>
                    <col id="{$totalColumns - 2}"/>
                    <col id="{$totalColumns - 1}" s="40">
                        <xsl:value-of select="@fwd"/>
                    </col>
                </xsl:when>
                <xsl:otherwise>
                    <col id="0" s="15" colspan="{$totalColumns - 3 + $varMCColspan}">
                        <xsl:value-of select="@name"/>
                    </col>
                    <col id="{$totalColumns - 4}" s="15">
                        <xsl:copy-of select="$balFwdText1"/>
                    </col>
                    <col id="{$totalColumns - 3}"/>
                    <col id="{$totalColumns - 2}"/>
                    <col id="{$totalColumns - 1}" s="16">
                        <xsl:value-of select="@fwd"/>
                    </col>
                </xsl:otherwise>
            </xsl:choose>
        </row>
        <!-- This will display the transactions of other,  if any -->
        <xsl:apply-templates/>
        <!-- This Row displays the Other Totals-->
        <row>
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="ancestor::acct[1]/@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <xsl:choose>
                <xsl:when test="(@level &gt; '3')">
                    <col id="0" s="15"/>
                    <col id="0" s="32" colspan="{$totalColumns - 3 + $varMCColspan}">
                        <xsl:call-template name="string-replace">
                            <xsl:with-param name="haystack" select="'IA.TOTALS_FOR_OTHERS'" />
                            <xsl:with-param name="replace" select="'${OTHERS}'" />
                            <xsl:with-param name="by" select="@name" />
                        </xsl:call-template>
                    </col>
                    <col id="{$totalColumns - 3}" s="40">
                        <xsl:value-of select="@db"/>
                    </col>
                    <col id="{$totalColumns - 2}" s="40">
                        <xsl:value-of select="@cr"/>
                    </col>
                    <col id="{$totalColumns - 1}" s="40">
                        <xsl:value-of select="@bal"/>
                    </col>
                </xsl:when>
                <xsl:otherwise>
                    <col id="0" s="32" colspan="{$totalColumns - 2 + $varMCColspan}">
                        <xsl:call-template name="string-replace">
                            <xsl:with-param name="haystack" select="'IA.TOTALS_FOR_OTHERS'" />
                            <xsl:with-param name="replace" select="'${OTHERS}'" />
                            <xsl:with-param name="by" select="@name" />
                        </xsl:call-template>
                    </col>
                    <col id="{$totalColumns - 3}" s="40">
                        <xsl:value-of select="@db"/>
                    </col>
                    <col id="{$totalColumns - 2}" s="40">
                        <xsl:value-of select="@cr"/>
                    </col>
                    <col id="{$totalColumns - 1}" s="40">
                        <xsl:value-of select="@bal"/>
                    </col>
                </xsl:otherwise>
            </xsl:choose>
        </row>
		<row>
			<xsl:choose>
				<xsl:when  test="(/reportdata/report/@shownetchange = 'T')">
                    <xsl:choose>
                        <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                            <col s="21">
                                <xsl:value-of select="ancestor::acct[1]/@id"/>
                            </col>
                        </xsl:when>
                    </xsl:choose>
					<xsl:choose>
						<xsl:when test="(@level &gt; '3')">
							<col id="0" s="15"/>
                            <col id="0" s="32" colspan="{$totalColumns - 1}">
                                <xsl:call-template name="string-replace">
                                    <xsl:with-param name="haystack" select="'IA.NET_CHANGE_FOR'" />
                                    <xsl:with-param name="replace" select="'${NET_CHANGE}'" />
                                    <xsl:with-param name="by" select="@name" />
                                </xsl:call-template>
                            </col>
						   <col id="1" s="40">
								<xsl:value-of select="@nc"/>
							</col>				
						</xsl:when>
						<xsl:otherwise>
                            <col id="0" s="32" colspan="{$totalColumns}">
                                <xsl:call-template name="string-replace">
                                    <xsl:with-param name="haystack" select="'IA.NET_CHANGE_FOR'" />
                                    <xsl:with-param name="replace" select="'${NET_CHANGE}'" />
                                    <xsl:with-param name="by" select="@name" />
                                </xsl:call-template>
                            </col>
							<col id="1" s="40">
								<xsl:value-of select="@nc"/>
							</col>				
						</xsl:otherwise>
					</xsl:choose>
				</xsl:when>
			</xsl:choose>
		</row>
    </xsl:template>
    <!-- TEMPLATE TO DISPLAY TRANSACTIONS -->
    <xsl:template match="trans">
        <xsl:choose>
            <xsl:when test="./sub">
                <row>
                    <!-- I assume that it is always a detailed rep here , because if detailed report is not selected then, sub trans will never be there. if sub trans are not there then subtotals is displayed for the batch itself!!?-->
                    <xsl:choose>
                        <xsl:when test="($locdeptflag = 'none')">
                            <!-- CASE 1: locdeptflag is none , Element1 only is present-->
                            <col id="4" s="21">
                                <xsl:call-template name="dogldatelink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@date"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="1"/>
                            <col id="0" colspan="{6+ $varMCColspan}" s="22">
                                <xsl:value-of select="@title"/>
                            </col>
                            <!-- END of CASE 1: locdeptflag is none , Element1 only is present-->
                        </xsl:when>
                        <xsl:when test="($locdeptflag = 'loc') or ($locdeptflag = 'dept')">
                            <!-- CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                            <col id="0" s="21"/>
                            <col id="1" s="21">
                                <xsl:call-template name="dogldatelink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@date"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="2" s="21"/>
                            <col id="0" colspan="{6+ $varMCColspan}" s="22">
                                <xsl:value-of select="@title"/>
                            </col>
                            <!-- END of CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                        </xsl:when>
                        <xsl:otherwise>
                            <!-- CASE 3:  Element1 & Element2 & Element 3 are present-->
                            <col id="0" s="21"/>
                            <col id="1" s="21"/>
                            <col id="2" s="21">
                                <xsl:call-template name="dogldatelink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@date"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="3" s="21"/>
                            <col id="0" colspan="{5+ $varMCColspan}" s="22">
                                <xsl:value-of select="@title"/>
                            </col>
                            <!-- END of CASE 3:  Element1 & Element2 & Element 3 are present-->
                        </xsl:otherwise>
                    </xsl:choose>
                </row>
                <xsl:apply-templates/>
                <row>
                    <xsl:choose>
                        <xsl:when test="($locdeptflag = 'none')">
                            <!-- CASE 1: locdeptflag is none , Element1 only is present-->
                            <col id="0" s="22"/>
                            <col id="1" s="22"/>
                            <col id="0" colspan="{$totalColumns - 5 +  ( ($repeatAccountOnLines = 'T') and  ($report_format = '_csv' or $backgroundformat = '_csv') ) }"  s="23">
                                <xsl:text>IA.TOTALS_FOR_SUMMARY_ENTRY</xsl:text>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="6" s="23">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col ></col>
                                    <col ></col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col ></col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="7" s="26">
                                <xsl:call-template name="doglnumlink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="8" s="26">
                                <xsl:call-template name="doglnumlink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="9" s="26">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 1: locdeptflag is none , Element1 only is present-->
                        </xsl:when>
                        <xsl:when test="($locdeptflag = 'loc') or ($locdeptflag = 'dept')">
                            <!-- CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                            <col id="0" s="22"/>
                            <col id="1" s="22"/>
                            <col id="2" s="22"/>
                            <col id="0" colspan="{$totalColumns - 6}" s="23">
                                <xsl:text>IA.TOTALS_FOR_SUMMARY_ENTRY</xsl:text>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="6" s="23">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col ></col>
                                    <col ></col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col ></col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="7" s="26">
                                <xsl:call-template name="doglnumlink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="8" s="26">
                                <xsl:call-template name="doglnumlink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="9" s="26">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                        </xsl:when>
                        <xsl:otherwise>
                            <!-- CASE 3:  Element1 & Element2 & Element 3 are present-->
                            <col id="0" s="22"/>
                            <col id="1" s="22"/>
                            <col id="2" s="22"/>
                            <col id="3" s="22"/>
                            <col id="0" colspan="{2 + $dimcount + $transactionTitleCount + $showMatchingCode}" s="23">
                                <xsl:text>IA.TOTALS_FOR_SUMMARY_ENTRY</xsl:text>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="6" s="23">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col ></col>
                                    <col ></col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col ></col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="7" s="26">
                                <xsl:call-template name="doglnumlink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="8" s="26">
                                <xsl:call-template name="doglnumlink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
									<xsl:with-param name="opVal" select="@glbatchop"/>
                                </xsl:call-template>
                            </col>
                            <col id="9" s="26">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 3:  Element1 & Element2 & Element 3 are present-->
                        </xsl:otherwise>
                    </xsl:choose>
                </row>
            </xsl:when>
            <xsl:otherwise>
                <row>
                    <xsl:choose>
                        <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                            <col s="21">
                                <xsl:value-of select="ancestor::acct[1]/@id"/>
                            </col>
                        </xsl:when>
                    </xsl:choose>
                    <xsl:choose>
                        <xsl:when test="(/reportdata/report/@showdetail = 'T')">
                            <!-- Showdetail is true, detailed mode , 12 column header-->
                            <xsl:choose>
                                <xsl:when test="($locdeptflag = 'none')">
                                    <!-- CASE 1: locdeptflag is none , Element1 only is present-->
                                    <!--col id="0" width="10" >  </col-->
                                    <col id="1" s="21">
                                        <xsl:call-template name="domoddatelink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@date"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="2" s="21">
                                        <xsl:value-of select="@docdate"/>
                                    </col>
                                    <col id="3" s="24">
                                        <xsl:value-of select="@doc"/>
                                    </col>
                                    <col id="4" s="34">
                                        <xsl:value-of select="@memo"/>
                                    </col>
									
									<xsl:apply-templates select="dimensionTrans"/>

									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<col s="24">
											<xsl:value-of select="@batch_no"/>
										</col>
									</xsl:if>
                                    <xsl:choose>
                                        <xsl:when test="($showMatchingCode = 1)">
                                            <col s="24">
                                                <xsl:value-of select="@matchingcode"/>
                                            </col>
                                        </xsl:when>
                                    </xsl:choose>
									<col id="7" s="24">
                                        <xsl:value-of select="@sym"/>
									</col>
                                    <xsl:choose>
                                        <xsl:when test="($VarMultiCurrency = 'true')">
                                            <col s="24"> <xsl:value-of select="@currency"/> </col>
                                            <col s="25">
                                                <xsl:choose>
                                                    <xsl:when test="($varConsolidationBook = 'true')">
                                                        <xsl:call-template name="doentitylink">
                                                            <xsl:with-param name="node" select="."/>
                                                            <xsl:with-param name="val" select="@transamount"/>
                                                        </xsl:call-template>
                                                    </xsl:when>
                                                    <xsl:otherwise>
                                                        <xsl:value-of select="@transamount"/>
                                                    </xsl:otherwise>
                                                </xsl:choose>
                                            </col>
                                        </xsl:when>
                                        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
            <!-- this will be called only when company is not multi currency and report is for consolidation book-->
                                            <col s="25">
                                                <xsl:call-template name="doentitylink">
                                                    <xsl:with-param name="node" select="."/>
                                                    <xsl:with-param name="val" select="@transamount"/>
                                                </xsl:call-template>
                                            </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="8" s="25">
										<xsl:choose>
											<xsl:when test="(@trtype='pm') or (@trtype='rm') or (@trtype='em')">
												<xsl:value-of select="@db"/>
											</xsl:when>
											<xsl:otherwise>
												<xsl:call-template name="dolink">
													<xsl:with-param name="node" select="."/>
													<xsl:with-param name="val" select="@db"/>
													<xsl:with-param name="opVal" select="@glbatchop"/> <!--detail mode link here!!!-->
												</xsl:call-template>
											</xsl:otherwise>
										</xsl:choose>
                                    </col>
                                    <col id="9" s="25">
                                        <xsl:choose>
											<xsl:when test="(@trtype='pm') or (@trtype='rm') or (@trtype='em')">
												<xsl:value-of select="@cr"/>
											</xsl:when>
											<xsl:otherwise>
												<xsl:call-template name="dolink">
													<xsl:with-param name="node" select="."/>
													<xsl:with-param name="val" select="@cr"/>
													<xsl:with-param name="opVal" select="@glbatchop"/>
												</xsl:call-template>
											</xsl:otherwise>
										</xsl:choose>
                                    </col>
                                    <col id="10" s="25">
                                        <xsl:value-of select="@bal"/>
                                    </col>
                                    <!-- END of CASE 1: locdeptflag is none , Element1 only is present-->
                                </xsl:when>
                                <xsl:when test="($locdeptflag = 'loc') or ($locdeptflag = 'dept')">
                                    <!-- CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                                    <!--col id="0" width="10" >  </col-->
                                    <col id="1"/>
                                    <col id="2" s="21">
                                        <xsl:call-template name="domoddatelink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@date"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="3" s="21">
                                        <xsl:value-of select="@docdate"/>
                                    </col>
                                    <col id="4" s="24">
                                        <xsl:value-of select="@doc"/>
                                    </col>
                                    <col id="5" s="34">
                                        <xsl:value-of select="@memo"/>
                                    </col>
									
									<xsl:apply-templates select="dimensionTrans"/>
									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<col s="24">
											<xsl:value-of select="@batch_no"/>
										</col>
									</xsl:if>
                                    <xsl:choose>
                                        <xsl:when test="($showMatchingCode = 1)">
                                            <col s="24">
                                                <xsl:value-of select="@matchingcode"/>
                                            </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="7" s="24">
                                        <xsl:value-of select="@sym"/>
                                    </col>
                                    <xsl:choose>
                                        <xsl:when test="($VarMultiCurrency = 'true')">
                                            <col s="24"> <xsl:value-of select="@currency"/> </col>
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="8" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@db"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="9" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@cr"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="10" s="25">
                                        <xsl:value-of select="@bal"/>
                                    </col>
                                    <!-- END of CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                                </xsl:when>
                                <xsl:otherwise>
                                    <!-- CASE 3:  Element1 & Element2 & Element 3 are present-->
                                    <!--col id="0" width="10"> </col-->
                                    <col id="1"/>
                                    <col id="2"/>
                                    <col id="3" s="21">
                                        <xsl:call-template name="domoddatelink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@date"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="4" s="21">
                                        <xsl:value-of select="@docdate"/>
                                    </col>
                                    <col id="5" s="24">
                                        <xsl:value-of select="@doc"/>
                                    </col>
                                    <col id="6" s="34">
                                        <xsl:value-of select="@memo"/>
                                    </col>
									
									<xsl:apply-templates select="dimensionTrans"/>
									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<col s="24">
											<xsl:value-of select="@batch_no"/>
										</col>
									</xsl:if>
                                    <xsl:choose>
                                        <xsl:when test="($showMatchingCode = 1)">
                                            <col s="24">
                                                <xsl:value-of select="@matchingcode"/>
                                            </col>
                                        </xsl:when>
                                    </xsl:choose>
									<col id="7" s="24">
                                        <xsl:value-of select="@sym"/>
                                    </col>
                                    <xsl:choose>
                                        <xsl:when test="($VarMultiCurrency = 'true')">
                                            <col s="24"> <xsl:value-of select="@currency"/> </col>
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="8" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@db"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="9" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@cr"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="10" s="25">
                                        <xsl:value-of select="@bal"/>
                                    </col>
                                    <!-- END of CASE 3:  Element1 & Element2 & Element 3 are present-->
                                </xsl:otherwise>
                            </xsl:choose>
                            <!-- END of Showdetail is true, detailed mode , 11 column header-->
                        </xsl:when>
                        <xsl:otherwise>
                            <!-- Showdetail is not true, Summary mode , 10 column header-->
                            <xsl:choose>
                                <xsl:when test="($locdeptflag = 'none')">
                                    <!-- CASE 4: locdeptflag is none , Element1 only is present-->
                                    <!--col id="0" width="10" >  </col-->
                                    <col id="1" s="21">
                                        <xsl:call-template name="domoddatelink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@date"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="2" s="24">
                                        <xsl:value-of select="@doc"/>
                                    </col>
                                    <col id="3" s="34">
                                        <xsl:value-of select="@memo"/>
                                    </col>
									
									<xsl:apply-templates select="dimensionTrans"/>

									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<col s="24">
											<xsl:value-of select="@batch_no"/>
										</col>
									</xsl:if>
                                    <xsl:choose>
                                        <xsl:when test="($showMatchingCode = 1)">
                                            <col s="24">
                                                <xsl:value-of select="@matchingcode"/>
                                            </col>
                                        </xsl:when>
                                    </xsl:choose>
									<col id="6" s="24">
                                        <xsl:value-of select="@sym"/>
                                    </col>
                                    <xsl:choose>
                                        <xsl:when test="($VarMultiCurrency = 'true')">
                                            <col s="24"> <xsl:value-of select="@currency"/> </col>
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="7" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@db"/>
											<xsl:with-param name="opVal" select="@glbatchop"/> <!--Non-detail mode link here-->
                                        </xsl:call-template>
                                    </col>
                                    <col id="8" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@cr"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="9" s="25">
                                        <xsl:value-of select="@bal"/>
                                    </col>
                                    <!-- END of CASE 4: locdeptflag is none , Element1 only is present-->
                                </xsl:when>
                                <xsl:when test="($locdeptflag = 'loc') or ($locdeptflag = 'dept')">
                                    <!-- CASE 5: locdeptflag is loc or dept , Element1 abd Element2 are present-->
                                    <!--col id="0" width="10" >  </col-->
                                    <col id="1"/>
                                    <col id="2" s="21">
                                        <xsl:call-template name="domoddatelink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@date"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="3" s="24">
                                        <xsl:value-of select="@doc"/>
                                    </col>
                                    <col id="4" s="34">
                                        <xsl:value-of select="@memo"/>
                                    </col>
									
									<xsl:apply-templates select="dimensionTrans"/>
									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<col s="24">
											<xsl:value-of select="@batch_no"/>
										</col>
									</xsl:if>
                                    <xsl:choose>
                                        <xsl:when test="($showMatchingCode = 1)">
                                            <col s="24">
                                                <xsl:value-of select="@matchingcode"/>
                                            </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="6" s="24">
                                        <xsl:value-of select="@sym"/>
                                    </col>
                                    <xsl:choose>
                                        <xsl:when test="($VarMultiCurrency = 'true')">
                                            <col s="24"> <xsl:value-of select="@currency"/> </col>
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="7" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@db"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="8" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@cr"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="9" s="25">
                                        <xsl:value-of select="@bal"/>
                                    </col>
                                    <!-- END of CASE 5: locdeptflag is loc or dept , Element1 abd Element2 are present-->
                                </xsl:when>
                                <xsl:otherwise>
                                    <!-- CASE 6:  Element1 & Element2 & Element 3 are present-->
                                    <!--col id="0" width="10"> </col-->
                                    <col id="1"/>
                                    <col id="2"/>
                                    <col id="3" s="21">
                                        <xsl:call-template name="domoddatelink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@date"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="4" s="24">
                                        <xsl:value-of select="@doc"/>
                                    </col>
                                    <col id="5" s="34">
                                        <xsl:value-of select="@memo"/>
                                    </col>
									
									<xsl:apply-templates select="dimensionTrans"/>

									<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
										<col s="24">
											<xsl:value-of select="@batch_no"/>
										</col>
									</xsl:if>
                                    <xsl:choose>
                                        <xsl:when test="($showMatchingCode = 1)">
                                            <col s="24">
                                                <xsl:value-of select="@matchingcode"/>
                                            </col>
                                        </xsl:when>
                                    </xsl:choose>
									<col id="6" s="24">
                                        <xsl:value-of select="@sym"/>
                                    </col>
                                    <xsl:choose>
                                        <xsl:when test="($VarMultiCurrency = 'true')">
                                            <col s="24"> <xsl:value-of select="@currency"/> </col>
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                        <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                            <col s="25"> <xsl:value-of select="@transamount"/> </col>
                                        </xsl:when>
                                    </xsl:choose>
                                    <col id="7" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@db"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="8" s="25">
                                        <xsl:call-template name="dolink">
                                            <xsl:with-param name="node" select="."/>
                                            <xsl:with-param name="val" select="@cr"/>
											<xsl:with-param name="opVal" select="@glbatchop"/>
                                        </xsl:call-template>
                                    </col>
                                    <col id="9" s="25">
                                        <xsl:value-of select="@bal"/>
                                    </col>
                                    <!-- CASE 6:  Element1 & Element2 & Element 3 are present-->
                                </xsl:otherwise>
                            </xsl:choose>
                            <!-- END of Showdetail is not true, Summary mode , 9 column header-->
                        </xsl:otherwise>
                    </xsl:choose>
                </row>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>

    <!--***********************************************-->
    <!-- This template processes periods when monthly *-->
    <!--    or custom subtotals are being generated   *-->
    <!--***********************************************-->
    <xsl:template match="periods">
        <xsl:apply-templates/>
	</xsl:template>
    <!--*******End of period template*******-->

    <!--***********************************************-->
    <!-- This template processes monthly or custom    *-->
    <!--    period subtotals                          *-->
    <!--***********************************************-->
    <xsl:template match="subtotal-for-period">
		<row hideincsv="true">
			<col id="0" s="36"></col>
			<col id="1"
				 s="36"
				 colspan="{$totalColumns - 3 + $varMCColspan}">
				 <xsl:value-of select="@caption"/>
			</col>
			<col id="{$totalColumns - 3}" s="35">
				<xsl:value-of select="@db"/>
			</col>
			<col id="{$totalColumns - 2}" s="35">
				<xsl:value-of select="@cr"/>
			</col>
			<col id="{$totalColumns - 1}" s="36"></col>
		</row>		
	</xsl:template>
    <!--*******End of subtotal-for-period template*******-->

    <xsl:template match="sub">
        <row>
            <xsl:choose>
                <xsl:when test="(/reportdata/report/@showdetail = 'T')">
                    <!-- Showdetail is true, detailed mode , 11 column header-->
                    <xsl:choose>
                        <xsl:when test="($locdeptflag = 'none')">
                            <!-- CASE 1: locdeptflag is none , Element1 only is present-->
                            <!--col id="0" width="10" >  </col-->
                            <xsl:choose>
                                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                                    <col id="0"/>
                                </xsl:when>
                            </xsl:choose>
                            <col id="4" s="21">
                                <xsl:value-of select="@date"/>
                            </col>
                            <col id="1" s="21">
                                <xsl:value-of select="@docdate"/>
                            </col>
                            <col id="2" s="24">
                                <xsl:value-of select="@doc"/>
                            </col>
                            <col id="3" s="24">
                                <xsl:value-of select="@title"/>
                            </col>

							<xsl:apply-templates select="dimensionTrans"/>
							
							<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
								<col s="24">
									<xsl:value-of select="../@batch_no"/>
								</col>
							</xsl:if>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
							<col id="6" s="24">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col id="4" s="24"> <xsl:value-of select="@currency"/> </col>
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="7" s="25">
								<xsl:choose>
									<xsl:when test="(@trtype='pm') or (@trtype='rm')">
										<xsl:value-of select="@db"/>
									</xsl:when>
									<xsl:otherwise>
										<xsl:call-template name="domodlink">
											<xsl:with-param name="node" select="."/>
											<xsl:with-param name="val" select="@db"/>
										</xsl:call-template>
									</xsl:otherwise>
								</xsl:choose>
                            </col>
							<col id="8" s="25">
								<xsl:choose>
									<xsl:when test="(@trtype='pm') or (@trtype='rm')">
										<xsl:value-of select="@cr"/>
									</xsl:when>
									<xsl:otherwise>
										<xsl:call-template name="domodlink">
											<xsl:with-param name="node" select="."/>
											<xsl:with-param name="val" select="@cr"/>
										</xsl:call-template>
									</xsl:otherwise>
								</xsl:choose>
                            </col>
                            <col id="9" s="25">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 1: locdeptflag is none , Element1 only is present-->
                        </xsl:when>
                        <xsl:when test="($locdeptflag = 'loc') or ($locdeptflag = 'dept')">
                            <!-- CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                            <!--col id="0" width="10" >  </col-->
                            <col id="0"/>
                            <col id="1" s="21">
                                <xsl:value-of select="@date"/>
                            </col>
                            <col id="2" s="21">
                                <xsl:value-of select="@docdate"/>
                            </col>
                            <col id="3" s="24">
                                <xsl:value-of select="@doc"/>
                            </col>
                            <col id="4" s="24">
                                <xsl:value-of select="@title"/>
                            </col>

							<xsl:apply-templates select="dimensionTrans"/>

							<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
								<col s="24">
									<xsl:value-of select="../@batch_no"/>
								</col>
							</xsl:if>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="6" s="24">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col id="4" s="24"> <xsl:value-of select="@currency"/> </col>
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="7" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
                                </xsl:call-template>
                            </col>
                            <col id="8" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
                                </xsl:call-template>
                            </col>
                            <col id="9" s="25">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 2: locdeptflag is loc or dept, Element1 & Element2 are present-->
                        </xsl:when>
                        <xsl:otherwise>
                            <!-- CASE 3:  Element1 & Element2 & Element 3 are present-->
                            <!--col id="0" width="10"> </col-->
                            <col id="0"/>
                            <col id="1"/>
                            <col id="2" s="21">
                                <xsl:value-of select="@date"/>
                            </col>
                            <col id="3" s="21">
                                <xsl:value-of select="@docdate"/>
                            </col>
                            <col id="4" s="24">
                                <xsl:value-of select="@doc"/>
                            </col>
                            <col id="5" s="24">
                                <xsl:value-of select="@title"/>
                            </col>
				            <xsl:apply-templates select="dimensionTrans"/>
							<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">

								<col s="24">
									<xsl:value-of select="../@batch_no"/>
								</col>
							</xsl:if>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="6" s="24">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col id="4" s="24"> <xsl:value-of select="@currency"/> </col>
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="7" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
                                </xsl:call-template>
                            </col>
                            <col id="8" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
                                </xsl:call-template>
                            </col>
                            <col id="9" s="25">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 3:  Element1 & Element2 & Element 3 are present-->
                        </xsl:otherwise>
                        <!-- END of Showdetail is true, detailed mode , 11 column header-->
                    </xsl:choose>
                </xsl:when>
                <xsl:otherwise>
                    <!-- Showdetail is not true, Summary mode , 9 column header-->
                    <xsl:choose>
                        <xsl:when test="($locdeptflag = 'none')">
                            <!-- CASE 4: locdeptflag is none , Element1 only is present-->
                            <!--col id="0" width="10" >  </col-->
                            <col id="0" s="21">
                                <xsl:value-of select="@date"/>
                            </col>
                            <col id="1" s="24">
                                <xsl:value-of select="@title"/>
                            </col>

							<xsl:apply-templates select="dimensionTrans"/>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="4" s="24">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col id="4" s="24"> <xsl:value-of select="@currency"/> </col>
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="5" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
                                </xsl:call-template>
                            </col>
                            <col id="6" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
                                </xsl:call-template>
                            </col>
                            <col id="7" s="25">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 4: locdeptflag is none , Element1 only is present-->
                        </xsl:when>
                        <xsl:when test="($locdeptflag = 'loc') or ($locdeptflag = 'dept')">
                            <!-- CASE 5: locdeptflag is loc or dept , Element1 abd Element2 are present-->
                            <!--col id="0" width="10" >  </col-->
                            <col id="0"/>
                            <col id="1" s="21">
                                <xsl:value-of select="@date"/>
                            </col>
                            <col id="2" s="24">
                                <xsl:value-of select="@title"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($locdeptflag = 'loc')">
                                    <col id="3" s="24">
                                        <xsl:value-of select="@deptname"/>
                                    </col>
                                </xsl:when>
                                <xsl:when test="($locdeptflag = 'dept')">
                                    <col id="3" s="24">
                                        <xsl:value-of select="@locname"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
							<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
								<col s="24">
									<xsl:value-of select="@batch_no"/>
								</col>
							</xsl:if>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="4" s="24">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col id="4" s="24"> <xsl:value-of select="@currency"/> </col>
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="5" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
                                </xsl:call-template>
                            </col>
                            <col id="6" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
                                </xsl:call-template>
                            </col>
                            <col id="7" s="25">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- END of CASE 5: locdeptflag is loc or dept , Element1 abd Element2 are present-->
                        </xsl:when>
                        <xsl:otherwise>
                            <!-- CASE 6:  Element1 & Element2 & Element 3 are present-->
                            <!--col id="0" > </col-->
                            <col id="0"/>
                            <col id="1"/>
                            <col id="2" s="21">
                                <xsl:value-of select="@date"/>
                            </col>
                            <col id="3" s="24">
                                <xsl:value-of select="@title"/>
                            </col>
							<xsl:if test="(/reportdata/report/@showTransactionNumber = 'true')">
								<col s="24">
									<xsl:value-of select="@batch_no"/>
								</col>
							</xsl:if>
                            <xsl:choose>
                                <xsl:when test="($showMatchingCode = 1)">
                                    <col s="24">
                                        <xsl:value-of select="@matchingcode"/>
                                    </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="4" s="24">
                                <xsl:value-of select="@sym"/>
                            </col>
                            <xsl:choose>
                                <xsl:when test="($VarMultiCurrency = 'true')">
                                    <col id="4" s="24"> <xsl:value-of select="@currency"/> </col>
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                                <xsl:when test="($varNonMultiCurrencyConsolidation = 'T')">
                                    <col id="4" s="25"> <xsl:value-of select="@transamount"/> </col>
                                </xsl:when>
                            </xsl:choose>
                            <col id="5" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@db"/>
                                </xsl:call-template>
                            </col>
                            <col id="6" s="25">
                                <xsl:call-template name="dolink">
                                    <xsl:with-param name="node" select="."/>
                                    <xsl:with-param name="val" select="@cr"/>
                                </xsl:call-template>
                            </col>
                            <col id="7" s="25">
                                <xsl:value-of select="@bal"/>
                            </col>
                            <!-- CASE 6:  Element1 & Element2 & Element 3 are present-->
                        </xsl:otherwise>
                    </xsl:choose>
                    <!-- END of Showdetail is not true, Summary mode , 9 column header-->
                </xsl:otherwise>
            </xsl:choose>
        </row>
    </xsl:template>
    <!-- TEMPLATE TO DISPLAY Element 3 Header and totals -->
    <xsl:template match="element3">
        <!-- This Row displays the Element 3 header-->
        <row>
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="ancestor::acct[1]/@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <col id="0" s="15"/>
            <col id="0" s="32" colspan="{$totalColumns - 4 + $varMCColspan}">
                <xsl:value-of select="@fullname"/>
            </col>
            <col id="{$totalColumns - 4}" s="32">
                <xsl:copy-of select="$balFwdText1"/>
            </col>
            <col id="{$totalColumns - 3}"/>
            <col id="{$totalColumns - 2}"/>
            <col id="{$totalColumns - 1}" s="40">
                <xsl:value-of select="@fwd"/>
            </col>
        </row>
        <!-- This will display the transactions of element3,  if any -->
        <xsl:apply-templates/>
        <!-- This Row displays the Element 3 Totals-->
        <row>
            <xsl:choose>
                <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                    <col s="21">
                        <xsl:value-of select="ancestor::acct[1]/@id"/>
                    </col>
                </xsl:when>
            </xsl:choose>
            <col id="0" s="15"/>
            <col id="0" s="32" colspan="{$totalColumns - 3 + $varMCColspan}">
                <xsl:text>Totals for </xsl:text>
                <xsl:value-of select="@fullname"/>
            </col>
            <col id="{$totalColumns - 3}" s="40">
                <xsl:value-of select="@db"/>
            </col>
            <col id="{$totalColumns - 2}" s="40">
                <xsl:value-of select="@cr"/>
            </col>
            <col id="{$totalColumns - 1}" s="40">
                <xsl:value-of select="@bal"/>
            </col>
        </row>
		<row>

            <xsl:choose>
                <xsl:when test="(/reportdata/report/@shownetchange = 'T')">
                    <xsl:choose>
                        <xsl:when test="($repeatAccountOnLines = 'T') and (($report_format = '_csv') or ($backgroundformat ='_csv'))">
                            <col s="21">
                                <xsl:value-of select="ancestor::acct[1]/@id"/>
                            </col>
                        </xsl:when>
                    </xsl:choose>
                    <col id="0" s="15"/>
                    <col id="0" s="32" colspan="{$totalColumns - 1}">
                        <xsl:text>Net Change for </xsl:text>
                        <xsl:value-of select="@fullname"/>
                    </col>
                    <col id="1" s="40">
                        <xsl:value-of select="@nc"/>
                    </col>
                </xsl:when>
                <xsl:otherwise>
                    <col id="0" s="15"/>
                </xsl:otherwise>
			</xsl:choose>
		</row>
    </xsl:template>
    <!--  THIS TEMPLATE IS RETAINED AS IS -->
    <xsl:template name="doglnumlink">
        <xsl:param name="node"/>
        <xsl:param name="val"/>
		<xsl:param name="opVal"/>
        <xsl:if test="($val!='0')">
            <xsl:variable name="jsStr">
                <xsl:text>javascript:GoGL('</xsl:text>
                <xsl:value-of select="$node/@glbatchscript"/><xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@glownerloc"/><xsl:text>');</xsl:text>
            </xsl:variable>
            <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
            <xsl:value-of select="$val"/>
        </xsl:if>
    </xsl:template>
    <!--  THIS TEMPLATE IS RETAINED AS IS -->
    <xsl:template name="dogldatelink">
        <xsl:param name="node"/>
        <xsl:param name="val"/>
		<xsl:param name="opVal"/>
        <xsl:variable name="jsStr">
            <xsl:text>javascript:GoGL('</xsl:text>
            <xsl:value-of select="$node/@glbatchscript"/><xsl:text>','</xsl:text>
            <xsl:value-of select="$node/@glownerloc"/><xsl:text>');</xsl:text>
        </xsl:variable>
        <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
        <xsl:value-of select="$val"/>
    </xsl:template>
    <!--  THIS TEMPLATE IS RETAINED AS IS -->
    <xsl:template name="dolink">
        <xsl:param name="node"/>
        <xsl:param name="val"/>
		<xsl:param name="opVal"/>
        <xsl:if test="($val!='0.00')">
            <xsl:choose>
                <!--  Remove Debit/Credit hyperlink for books posting to new consolidation table -->
                <xsl:when test="($isBookPostingToCSNEntryTable='T')">
                    <xsl:value-of select="$val"/>
                </xsl:when>
                <xsl:when test="($node/@type='gl') and ($node/@ce='') and (/reportdata/report/@showdetail='F')">
                    <xsl:variable name="jsStr">
                        <xsl:text>javascript:GoGL('</xsl:text>
                        <xsl:value-of select="$node/@glbatchscript"/><xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@glownerloc"/><xsl:text>');</xsl:text>
                    </xsl:variable>
                    <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                    <xsl:value-of select="$val"/>
                </xsl:when>
                <xsl:when test="($node/@type='gl') and ($node/@ce!='') and (/reportdata/report/@showdetail='F')">
                    <xsl:variable name="jsStr">
                        <xsl:text>javascript:GoGL('</xsl:text>
                        <xsl:value-of select="$node/@glbatchscript"/><xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@glownerloc"/><xsl:text>');</xsl:text>
                    </xsl:variable>
                    <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                    <xsl:value-of select="$val"/>
                </xsl:when>
                <xsl:when test="($node/@type='gl') and ($node/@ce!='') and (/reportdata/report/@showdetail='T')">
                    <xsl:variable name="jsStr">
                        <xsl:text>javascript:GoSub('</xsl:text>
                        <xsl:value-of select="$node/@ce"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@date"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@ac"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@dpt"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@loc"/>
                        <xsl:text>');</xsl:text>
                    </xsl:variable>
                    <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                    <xsl:value-of select="$val"/>
                </xsl:when>
                <xsl:when test="($node/@type='gl') and ($node/@ce='') and (/reportdata/report/@showdetail='T')">
                    <xsl:variable name="jsStr">
                        <xsl:text>javascript:GoGL('</xsl:text>
                        <xsl:value-of select="$node/@glbatchscript"/><xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@glownerloc"/><xsl:text>');</xsl:text>
                    </xsl:variable>
                    <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                    <xsl:value-of select="$val"/>
                </xsl:when>
                <xsl:when test="($node/@type='mod') and (/reportdata/report/@showdetail='F')">
                    <xsl:variable name="jsStr">
                        <xsl:text>javascript:GoGL('</xsl:text>
                        <xsl:value-of select="$node/@glbatchscript"/><xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@glownerloc"/><xsl:text>');</xsl:text>
                    </xsl:variable>
                    <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                    <xsl:value-of select="$val"/>
                </xsl:when>
                <xsl:when test="($node/@type='mod') and (/reportdata/report/@showdetail='T')">
                    <xsl:variable name="jsStr">
                        <xsl:text>javascript:GoMod('</xsl:text>
                        <xsl:value-of select="$node/@trtype"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@batchno"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@acctno"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@date"/>
                        <xsl:text>','</xsl:text>
			<xsl:value-of select="$node/@paymethodkey"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@recordid"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@doc"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@ent"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@recno"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@finaccttype"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@glentrykey"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@prentrykey"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@dpt"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@ownerloc"/>
                        <xsl:text>');</xsl:text>
                    </xsl:variable>
                    <xsl:if test="($node/@trtype != 'pe') and ($node/@trtype != 're')">
                        <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                    </xsl:if>
                    <xsl:value-of select="$val"/>
                </xsl:when>
            </xsl:choose>
        </xsl:if>
    </xsl:template>


	<xsl:template name="doentitylink">
        <xsl:param name="node"/>
        <xsl:param name="val"/>
        <xsl:if test="($val!='0.00')">
            <xsl:choose>
                <!-- Provide hyperlink to GAAP/TAX consolidation memo as well -->
                <xsl:when test="($node/@type='gl') and ($node/@iscta='F') and ($node/@ce='') and (/reportdata/report/@showdetail='T') and (contains($node/@memo, $consolidationMemo))">
                    <xsl:variable name="jsStr">
                        <xsl:text>javascript:gl_gldrill('</xsl:text>
                        <xsl:value-of select="$node/@accounthash"/>
                        <xsl:text>','</xsl:text>
                        <xsl:value-of select="$node/@jsonfilter"/>
                        <xsl:text>');</xsl:text>
                    </xsl:variable>
                    <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                    <xsl:value-of select="$val"/>
                </xsl:when>
				<xsl:otherwise>
                  <xsl:value-of select="$val"/>
				</xsl:otherwise>
            </xsl:choose>
        </xsl:if>
    </xsl:template>

    <!--  THIS TEMPLATE IS RETAINED AS IS -->
    <xsl:template name="domoddatelink">
        <xsl:param name="node"/>
        <xsl:param name="val"/>
		<xsl:param name="opVal"/>
        <xsl:choose>
            <!--  Remove posted date hyperlink for books posting to new consolidation table -->
            <xsl:when test="($node/@type!='gl') and ($isBookPostingToCSNEntryTable!='T')">
                <xsl:variable name="jsStr">
                    <xsl:text>javascript:GoGL('</xsl:text>
                    <xsl:value-of select="$node/@glbatchscript"/><xsl:text>','</xsl:text>
                    <xsl:value-of select="$node/@glownerloc"/><xsl:text>');</xsl:text>
                </xsl:variable>
                <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
                <xsl:value-of select="$val"/>
            </xsl:when>
            <xsl:otherwise>
                <xsl:value-of select="$val"/>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>
    <!--  THIS TEMPLATE IS RETAINED AS IS -->
    <xsl:template name="domodlink">
        <xsl:param name="node"/>
        <xsl:param name="val"/>
        <xsl:if test="($val!='0.00')">
            <xsl:variable name="jsStr">
                <xsl:text>javascript:GoMod('</xsl:text>
                <xsl:value-of select="$node/@trtype"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@batchno"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@acctno"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@date"/>
                <xsl:text>','</xsl:text>
		<xsl:value-of select="$node/@paymethodkey"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@recordid"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@doc"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@ent"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@recno"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@finaccttype"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@glentrykey"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@prentrykey"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@dpt"/>
                <xsl:text>','</xsl:text>
                <xsl:value-of select="$node/@ownerloc"/>
                <xsl:text>');</xsl:text>
            </xsl:variable>
            <xsl:if test="($node/@trtype != 'pe') and ($node/@trtype != 're')">
                <xsl:attribute name="href"><xsl:value-of select="$jsStr"/></xsl:attribute>
            </xsl:if>
            <xsl:value-of select="$val"/>
        </xsl:if>
    </xsl:template>
    <!--  THIS TEMPLATE IS RETAINED AS IS -->
    <xsl:template name="generatestyles">
        <stylegroups>
            <stylegroup id="1" name="body" class="BODY" font="Helvetica" size="7" weight="normal" style="normal" color="black"/>
            <stylegroup id="2" name="company" class="W" font="Helvetica" size="10" weight="bold" style="normal" color="black"/>
            <stylegroup id="3" name="title" class="W" font="Helvetica" size="10" weight="bold" style="normal" color="black"/>
            <stylegroup id="4" name="title2" class="W" font="Helvetica" size="9" weight="bold" style="normal" color="black"/>
            <stylegroup id="5" name="footer" class="FOOT" font="Helvetica" size="7" weight="bold" style="normal" color="black" alignment="M"/>
            <stylegroup id="6" name="header_row" font="Helvetica" size="8" weight="bold" style="normal" color="black" underline_type="1">
                <xsl:choose>
                    <xsl:when test="($disableFloatHeader = 'Y')">
                        <xsl:attribute name="class">HEADWithOutFloat</xsl:attribute>
                    </xsl:when>
                    <xsl:otherwise>
                        <xsl:attribute name="class">HEAD</xsl:attribute>
                    </xsl:otherwise>
                </xsl:choose>                
            </stylegroup>
            <stylegroup id="7" name="header_CT" class="REPCOLHDL" font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
            <stylegroup id="8" name="header_CN" class="REPCOLHDR" type="number"/>
            <stylegroup id="9" name="acct_row" class="GB" font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
            <stylegroup id="10" name="acct_CT" class="W" type="date"/>
            <stylegroup id="11" name="acct_CT1" class="WI"/>
            <stylegroup id="12" name="acct_CT2" class="DGB" font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
            <stylegroup id="13" name="acct_CN" class="R" type="currency" size="8"/>
            <stylegroup id="14" name="deptloc_row" class="RDGB" font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
            <stylegroup id="15" name="deptloc_CT" class="W"/>
            <stylegroup id="16" name="deptloc_CN" class="R" type="currency"/>
            <stylegroup id="17" name="other_row" class="GB"/>
            <stylegroup id="18" name="other_CT" class="W"/>
            <stylegroup id="19" name="other_CN" class="R" type="currency"/>
            <stylegroup id="20" name="trans_row" class="R"/>
            <stylegroup id="21" name="trans_CT" class="W" type="date"/>
            <stylegroup id="22" name="trans_CT1" class="WI"/>
            <stylegroup id="23" name="trans_CT2" class="GI"/>
            <stylegroup id="24" name="trans_CT3" class="W" type="textonly"/>
            <stylegroup id="25" name="trans_CN1" class="R" type="currency"/>
            <stylegroup id="26" name="trans_CN2" class="RG" type="currency"/>
            <stylegroup id="27" name="sub_CT" class="W" type="date"/>
            <stylegroup id="28" name="sub_CT1" class="W"/>
            <stylegroup id="29" name="sub_CN" class="R" type="currency"/>
            <stylegroup id="30" name="dateformat" type="date"/>
            <stylegroup id="31" name="emptyRow" class="SPACER_ROW"/>

		    <xsl:choose>
                <xsl:when test="($report_format='_excel')">
     		        <stylegroup id="32" name="deptloc_CT1" class="W"/>
  		        </xsl:when>
  		        <xsl:otherwise>
                    <stylegroup id="32" name="deptloc_CT1" class="WI" font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
  		        </xsl:otherwise>
		    </xsl:choose>

            <stylegroup id="33" name="netchange_HEAD" class="HEAD2" weight="bold"/>
    		<stylegroup id="34" name="trans_CT3" class="W1"/>
			<stylegroup id="35" name="italic_currency" class="RCLR" type="currency" style="italic"/>
			<stylegroup id="36" name="italic_caption" class="WIB" style="italic" weight="bold" />
            <stylegroup id="37" name="acct_CN" class="REPTOTR" type="currency"  font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
            <stylegroup id="38" name="acct_CN" class="REPGTOTR" type="currency"  font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
		    <stylegroup id="39" name="grand_total_cn" class="REPGTOTR" type="currency" font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
            <stylegroup id="40" name="bold_no_under" class="R" type="currency" font="Helvetica" size="8" weight="bold" style="normal" color="black"/>
        </stylegroups>
    </xsl:template>
    <!--  THIS TEMPLATE IS RETAINED AS IS -->
    <xsl:template name="script">
        <script>
        var op='<xsl:value-of select="/reportdata/report/@glop"/>';
        var gSess = '<xsl:value-of select="/reportdata/report/@sess"/>';
        function GoGL(aUrl, loc) {
            curloc = '<xsl:value-of select="/reportdata/report/@locationcontext"/>'; 
            ismegl = '<xsl:value-of select="/reportdata/report/@ismegl"/>'; 
            companyid = '<xsl:value-of select="/reportdata/report/@companyid"/>';
	    reportingacset = '<xsl:value-of select="/reportdata/report/@reportingaccountset"/>';
            if(reportingacset){
		aUrl += '&amp;_obj__REPORTINGACCOUNTS=true&amp;.obj__REPORTINGACCOUNTSET='+reportingacset;
	    }

            ReportLaunch(companyid, ismegl, loc, curloc, 'gl', aUrl, false, '','<xsl:value-of select="/reportdata/report/@sess"/>');            
        }

        function GoMod(recordtype, batch, acctno, whenpaid, paymethod, recordid, docnumber, entity, recno, finaccttype, glentrykey, prentrykey, dept, loc) {
            var URL;
            var MODULE;
            curloc = '<xsl:value-of select="/reportdata/report/@locationcontext"/>'; 
            ismegl = '<xsl:value-of select="/reportdata/report/@ismegl"/>'; 
            companyid = '<xsl:value-of select="/reportdata/report/@companyid"/>';
	        reportingacset = '<xsl:value-of select="/reportdata/report/@reportingaccountset"/>';
            done = '<xsl:value-of select="/reportdata/report/@done"/>';
            backhere = '<xsl:value-of select="$backhere"/>';

			reportSlide = ( ismegl == 'Y' &amp;&amp; loc != '' &amp;&amp; (loc != curloc) ? true : false );

            switch (recordtype){
                case 'ri':
					editMode = ( reportSlide ? 'view' : '<xsl:value-of select="/reportdata/report/@rido"/>' );
                    editOp = ( reportSlide ? '<xsl:value-of select="/reportdata/report/@riviewop"/>' : '<xsl:value-of select="/reportdata/report/@riop"/>' );
                    URL = '<xsl:value-of select="/reportdata/report/@ripage"/>?.do='+editMode+'&amp;.op='+editOp+'&amp;.fo=ar&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ar';
                    break;
                case 'ra':
					editMode = ( reportSlide ? 'view' : '<xsl:value-of select="/reportdata/report/@rado"/>' );
                    editOp = ( reportSlide ? '<xsl:value-of select="/reportdata/report/@raviewop"/>' : '<xsl:value-of select="/reportdata/report/@raop"/>' );
                    URL = '<xsl:value-of select="/reportdata/report/@rapage"/>?.do='+editMode+'&amp;.op='+editOp+'&amp;.fo=ar&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ar';
                    break;
                case 'ro':
                case 'rr':
                case 'rp':
                    URL = '<xsl:value-of select="/reportdata/report/@rppage"/>?.do=<xsl:value-of select="/reportdata/report/@rpdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@rpop"/>&amp;.fo=ar&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ar';
                    break;
                case 'rd':
                    URL = '<xsl:value-of select="/reportdata/report/@rdpage"/>?.do=<xsl:value-of select="/reportdata/report/@rddo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@rdop"/>&amp;.fo=ar&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ar';
                    break;
                case 'pi':
					editMode = ( reportSlide ? 'view' : '<xsl:value-of select="/reportdata/report/@pido"/>' );
                    editOp = ( reportSlide ? '<xsl:value-of select="/reportdata/report/@piviewop"/>' : '<xsl:value-of select="/reportdata/report/@piop"/>' );
                    URL = '<xsl:value-of select="/reportdata/report/@pipage"/>?.do='+editMode+'&amp;.op='+editOp+'&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'pa':
					editMode = ( reportSlide ? 'view' : '<xsl:value-of select="/reportdata/report/@pado"/>' );
                    editOp = ( reportSlide ? '<xsl:value-of select="/reportdata/report/@paviewop"/>' : '<xsl:value-of select="/reportdata/report/@paop"/>' );
                    URL = '<xsl:value-of select="/reportdata/report/@papage"/>?.do='+editMode+'&amp;.op='+editOp+'&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'po':
                    URL = '<xsl:value-of select="/reportdata/report/@popage"/>?.do=<xsl:value-of select="/reportdata/report/@podo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@poop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.recordkey='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'ep':
                    if (finaccttype == 'ba') {
                        URL = '<xsl:value-of select="/reportdata/report/@ba_eppage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@ba_epdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ba_epop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.glentrykey='+glentrykey+'&amp;.prentrykey='+prentrykey;
                    } else if (finaccttype == 'cc') {
                        URL = '<xsl:value-of select="/reportdata/report/@cc_eppage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@cc_epdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cc_epop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.glentrykey='+glentrykey+'&amp;.prentrykey='+prentrykey;
                    }
                    MODULE = 'ap';
                    break;
                case 'er':
                    if (finaccttype == 'ba') {
                        URL = '<xsl:value-of select="/reportdata/report/@ba_erpage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@ba_erdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ba_erop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    } else if (finaccttype == 'cc') {
                        URL = '<xsl:value-of select="/reportdata/report/@cc_erpage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@cc_erdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cc_erop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    }
                    MODULE = 'ap';
                    break;
                case 'eo':
                    URL = '<xsl:value-of select="/reportdata/report/@eopage"/>?.do=<xsl:value-of select="/reportdata/report/@eodo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@eoop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.recordkey='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'pp':
                    if (finaccttype == 'ba') {
                        if (paymethod == '1' || paymethod == '4'){
                            URL = '<xsl:value-of select="/reportdata/report/@ba_pppage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@ba_ppdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ba_ppopblr"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.drillpayment='+recno+'&amp;.location=<xsl:value-of select="/reportdata/report/@orig_loc"/>&amp;.drillfilter=1';
                        } else{
                            URL = '<xsl:value-of select="/reportdata/report/@ba_pppage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@ba_ppdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ba_ppop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.glentrykey='+glentrykey+'&amp;.prentrykey='+prentrykey;
                        }                        
                    } else if (finaccttype == 'cc') {
                        URL = '<xsl:value-of select="/reportdata/report/@cc_pppage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@cc_ppdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cc_ppop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.glentrykey='+glentrykey+'&amp;.prentrykey='+prentrykey+'&amp;F_WHENPAID='+escape(whenpaid);
                    }
                    MODULE = 'ap';
                    break;
                case 'pr':
                    if (finaccttype == 'ba') {
                        URL = '<xsl:value-of select="/reportdata/report/@ba_prpage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@ba_prdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ba_prop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.glentrykey='+glentrykey+'&amp;.prentrykey='+prentrykey;
                    } else if (finaccttype == 'cc') {
                        URL = '<xsl:value-of select="/reportdata/report/@cc_prpage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@cc_prdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cc_prop"/>&amp;.fo=ap&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.glentrykey='+glentrykey+'&amp;.prentrykey='+prentrykey;
                    }
                    MODULE = 'ap';
                    break;
                case 'pd':
                    URL = '<xsl:value-of select="/reportdata/report/@pdpage"/>?.do=<xsl:value-of select="/reportdata/report/@pddo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@pdop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'ei':
                    URL = '<xsl:value-of select="/reportdata/report/@eipage"/>?.do=<xsl:value-of select="/reportdata/report/@eido"/>&amp;.op=<xsl:value-of select="/reportdata/report/@eiop"/>&amp;.fo=ee&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.entity='+escape(entity);
                    MODULE = 'ee';
                    break;
                case 'ea':
                    URL = '<xsl:value-of select="/reportdata/report/@eapage"/>?.do=<xsl:value-of select="/reportdata/report/@eado"/>&amp;.op=<xsl:value-of select="/reportdata/report/@eaop"/>&amp;.fo=ee&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.entity='+escape(entity);
                    MODULE = 'ee';
                    break;
                case 'ck':
                    URL = '<xsl:value-of select="/reportdata/report/@ckpage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@ckdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ckop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;F_ACCOUNTID='+escape(recordid)+'&amp;F_WHENPAID='+escape(whenpaid)+'&amp;F_DOCNUMBER='+escape(docnumber);
                    MODULE = 'ap';
                    break;
                case 'cw':
                    URL = '<xsl:value-of select="/reportdata/report/@cwpage"/>?.type=_html&amp;.do=<xsl:value-of select="/reportdata/report/@cwdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cwop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;F_ACCOUNTID='+escape(recordid)+'&amp;F_WHENPAID='+escape(whenpaid)+'&amp;F_DOCNUMBER='+escape(docnumber)+'&amp;.drillpayment='+recno+'&amp;.location=<xsl:value-of select="/reportdata/report/@orig_loc"/>&amp;.drillfilter=1';
                    MODULE = 'ap';
                    break;
                case 'cq':
                    URL = '<xsl:value-of select="/reportdata/report/@cqpage"/>?.do=<xsl:value-of select="/reportdata/report/@cqdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cqop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'cc':
                    URL = '<xsl:value-of select="/reportdata/report/@ccpage"/>?.do=<xsl:value-of select="/reportdata/report/@ccdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ccop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'ch':
                    URL = '<xsl:value-of select="/reportdata/report/@chpage"/>?.do=<xsl:value-of select="/reportdata/report/@chdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@chop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'cn':
                    URL = '<xsl:value-of select="/reportdata/report/@cnpage"/>?.do=<xsl:value-of select="/reportdata/report/@cndo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cnop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'cr':
                    URL = '<xsl:value-of select="/reportdata/report/@crpage"/>?.do=<xsl:value-of select="/reportdata/report/@crdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@crop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.it=otherreceipts';
                    MODULE = 'cm';
                    break;
                case 'ci':
                    URL = '<xsl:value-of select="/reportdata/report/@cipage"/>?.do=<xsl:value-of select="/reportdata/report/@cido"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ciop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'cb':
                    URL = '<xsl:value-of select="/reportdata/report/@cbpage"/>?.do=<xsl:value-of select="/reportdata/report/@cbdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cbop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'ct':
                    URL = '<xsl:value-of select="/reportdata/report/@ctpage"/>?.do=<xsl:value-of select="/reportdata/report/@ctdo"/>&amp;.op=<xsl:value-of select="/reportdata/report/@ctop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype;
                    MODULE = 'ap';
                    break;
                case 'cd':
                    URL = '<xsl:value-of select="/reportdata/report/@cdpage"/>?.do=<xsl:value-of select="/reportdata/report/@cddo"/>&amp;.sess=<xsl:value-of select="/reportdata/report/@sess"/>&amp;.op=<xsl:value-of select="/reportdata/report/@cdop"/>&amp;.fo=ap&amp;.batch='+batch+'&amp;.r='+recno+'&amp;.recordtype='+recordtype+'&amp;.backhere='+escape(backhere)+'&amp;.done='+escape(done);
                    break;
            }
	    if(reportingacset){
		URL += '&amp;_obj__REPORTINGACCOUNTS=true&amp;_obj__REPORTINGACCOUNTSET='+reportingacset;
	    }
            ReportLaunch(companyid, ismegl, loc, curloc, MODULE, URL, false, '','<xsl:value-of select="/reportdata/report/@sess"/>');                
        }

        function GoSub(cns, trdate, acct, dept, loc) {
            var URL;
            var SDY;
            SDY = '<xsl:value-of select="/reportdata/report/@sdy"/>';
            URL = 'slide.phtml?.desttype=subsidiary&amp;.dest='+cns+'&amp;.sess=<xsl:value-of select="/reportdata/report/@sess"/>&amp;.op=<xsl:value-of select="/reportdata/report/@subop"/>&amp;.date='+escape(trdate)+'&amp;.acct='+acct+'&amp;.dept='+dept+'&amp;.loc='+loc+'&amp;.showsubtotal=<xsl:value-of select="/reportdata/report/@showsubtotal"/>';
            var name = 'cs'+cns;
            var mywidth = screen.width;
            var myheigth = screen.height/1.25;
            var params =  'width=' + mywidth + ',height=' + myheigth +',toolbar=no,location=no,directories=no,status=yes,menubar=no,scrollbars=yes,resizable=yes,left=0,top=0,screenX=0,screenY=0';
            var popup = window.open(URL, name, params);     
            if (popup == null) {return;}
            if (popup.opener == null ) {popup.opener = self;}
            popup.focus();
            return;
        }
    </script>
    </xsl:template>
</xsl:stylesheet>
