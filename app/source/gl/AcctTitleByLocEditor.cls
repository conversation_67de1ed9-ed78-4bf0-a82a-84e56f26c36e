<?php
/**
 * This is the Account Title By Entity editor
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * AcctTitleByLocEditor
 *    editor sub class for Account Title By Entity
 */
class AcctTitleByLocEditor extends FormEditor
{
    /* @var string $currentView */
    var $currentView;

    /* @var string|null $filterAccountNo */
    var $filterAccountNo;

    /* @var string|null $filterLocationID */
    var $filterLocationID;

    /* @var string $action */
    var $action;

    /**
     * @param array $params parameters
     */
    public function __construct($params)
    {
        parent::__construct($params);

        $this->filterAccountNo = Request::$r->_accountno;
        $this->filterLocationID = Request::$r->_locationid;

        // lets figure out what view we need to offer in the UI
        $this->currentView = ( Request::$r->_currentview == 'gridview' ? 'gridview' : '' );

        // add custom actions to the form editor
        $this->addGridViewAction();
        $this->addGridSaveAction();
    }

    /**
     * getEditorGlobals
     *     override parent
     *
     * @return array
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        $vars['_accountno'] = $this->filterAccountNo;
        $vars['_locationid'] = $this->filterLocationID;
        $vars['_currentview'] = $this->currentView;
        $vars['_action'] = $this->action;
        return $vars;
    }

    /**
     * getMetadataKeyName
     *     override parent and figure out which layout file to load
     *
     * @param array &$params array of fields
     *
     * @return string layout file name
     */
    protected function getMetadataKeyName(&$params)
    {
        $entity = $params['entity'] . ( $this->currentView == 'gridview' ? '_gridview' : '' );
        return "${entity}_form.pxml";
    }

    /**
     * getStandardButtons
     *     override parent and handle buttons by currentView
     *
     * @param string $state current state
     *
     * @return array values
     */
    public function getStandardButtons($state)
    {
        $values = array();

        if ( $this->currentView == 'gridview' ) {
            $this->setButtonDetails(
                $values, Editor_SaveBtnID, 'savebutton', 'IA.SAVE', 'gridsave', false,
                "window.editor.submit(true, 'gridsave');", false
            );
            $this->setButtonDetails($values, Editor_CancelBtnID, 'cancelbutton', 'IA.CANCEL', 'cancel', false);
        } else {
            $values = parent::getStandardButtons($state);
        }

        return $values;
    }

    /**
     * addGridViewAction
     *     override parent and handle currentView
     */
    protected function addGridViewAction()
    {
        $this->kActionHandlers['gridview'] = array(
            'handler' => 'ProcessGridViewAction',
            'states' => array(
                $this->kShowViewState,
                $this->kShowEditState,
                $this->kShowNewState,
                $this->kInitState,
            )
        );
        $this->kDefaultVerbActions['submit'] = $this->kSubmitAction;
        $this->kRequireVerbForAction['submit'] = $this->kSubmitAction;
    }

    /**
     * ProcessGridViewAction
     *     override parent
     *
     * @param array &$_params params array
     *
     * @return bool return status
     */
    protected function ProcessGridViewAction(&$_params)
    {
        $gErr = Globals::$g->gErr;

        $this->state = Editor_ShowNewState;
        $ok = true;

        if (!is_object($this->entityMgr)) {
            $this->entityMgr = $this->GetManager($_params['entity']);
        }

        if ( !$this->ProcessErrorRetrivalAction($this->getEntityMgr()) ) {

            $ok = $this->retrieveObjectFromView($_params, $obj);
            $obj = $this->DoRefreshAction($_params, $obj);

            if ( $ok && $this->currentView == 'gridview' ) {
                $filters = array(
                    'ACCOUNTNO' => $this->filterAccountNo,
                    'LOCATIONID' => $this->filterLocationID
                );
                $retObj['TITLEGRID'] = $this->getEntityMgr()->getFilteredList($filters);
            }

            /** @noinspection PhpUndefinedVariableInspection */
            if ( !$retObj ) {
                $retObj = $obj;
            }
            $this->state = Editor_ShowNewState;
            Request::$r->SetCurrentObject($retObj);
        }

        if ( $gErr->hasErrors() ) {
            $this->state = $this->kErrorState;
        }

        return $ok;
    }

    /**
     * addGridSaveAction
     *     override parent and handle currentView
     */
    protected function addGridSaveAction()
    {
        $this->kActionHandlers['gridsave'] = array(
            'handler' => 'ProcessGridSaveAction',
            'states' => array(
                $this->kShowViewState,
                $this->kShowEditState,
                $this->kShowNewState,
                $this->kInitState,
            ),
            'csrf' => true,
        );
        $this->kDefaultVerbActions['submit'] = $this->kSubmitAction;
        $this->kRequireVerbForAction['submit'] = $this->kSubmitAction;
    }

    /**
     * ProcessGridSaveAction
     *     override parent
     *
     * @param array &$_params params array
     *
     * @return bool return status
     */
    protected function ProcessGridSaveAction(&$_params)
    {
        $gErr = Globals::$g->gErr;
        /* @var URLS $gURLs */
        $gURLs = &Globals::$g->gURLs;

        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = $this->retrieveObjectFromView($_params, $obj);
        $obj = $this->DoRefreshAction($_params, $obj);

        // lets prepare $values structure to save
        $values = array();
        foreach ( $obj['TITLEGRID'] as $row ) {

            if ( (!isset($row['ACCOUNTNO']) || $row['ACCOUNTNO'] == '')
                && $obj['TOPACCOUNT'] != '' && $row['TITLE'] != ''
            ) {
                $row['ACCOUNTNO'] = $obj['TOPACCOUNT'];
            }

            if ( (!isset($row['LOCATIONID']) || $row['LOCATIONID'] == '')
                && $obj['TOPLOCATION'] != '' && $row['TITLE'] != ''
            ) {
                $row['LOCATIONID'] = $obj['TOPLOCATION'];
            }

            if ( isset($row['_dummy'])
                && (!isset($row['TITLE']) || $row['TITLE'] == '')
                && (!isset($row['ACCOUNTNO']) || $row['ACCOUNTNO'] == '')
                && (!isset($row['LOCATIONID']) || $row['LOCATIONID'] == '')
            ) {
                continue;
            }

            $values['ENTRIES'][] = $row;
        }

        // lets addd the rows to be deleted
        foreach ( $obj['TITLEGRID_DELETED_ROWS'] as $row ) {
            if ( $row['RECORDNO'] != '' ) {
                $values['ENTRIES'][]['RECORDNO'] = $row['RECORDNO'];
            }
        }

        $ok = $this->getEntityMgr()->saveAll($values);

        if ( !$ok || $gErr->hasErrors() ) {
            $this->state = $this->kErrorState;
        } else {

            if ($gURLs->HasReturnPath()) {
                $this->state = $this->kGoBackState;
            } else if ($_params['popup']) {
                $this->state = $this->kCloseState;
            } else if ($this->state != $this->kErrorState) {
                $this->state = $this->kShowNewState;
            }

        }

        // lets set the url arg back in so that we dont loose in case of redirection
        global $gGoBackParams;

        $this->action = 'gridview';
        $this->currentView = 'gridview';

        Request::$r->_action = 'gridview';
        Request::$r->_currentview = 'gridview';

        if ( $this->filterAccountNo != '' ) {
            $gGoBackParams['_accountno'] = $this->filterAccountNo;
        }

        if ( $this->filterLocationID != '' ) {
            $gGoBackParams['_locationid'] = $this->filterLocationID;
        }

        $gGoBackParams['_action'] = $this->action;
        $gGoBackParams['_currentview'] = $this->currentView;

        Request::$r->SetCurrentObject($obj);

        return $ok;
    }

    /**
     * mediateDataAndMetadata
     *     override parent
     *
     * @param array &$obj params array
     *
     * @return bool return status
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $ok = parent::mediateDataAndMetadata($obj);

        if (!$ok) {
            return false;
        }

        $state = $this->getState();
        $view = $this->getView();

        if ( $this->currentView == 'gridview' ) {

            $gridObject = array();
            $view->findComponents(array('path' => 'TITLEGRID'), EditorComponentFactory::TYPE_GRID, $gridObject);

            if ( $this->filterAccountNo != '' ) {
                list($acctno, $accttitle) = explode('--', $this->filterAccountNo);
                $obj['TOPACCOUNT'] = $acctno;
                $obj['TOPACCOUNTTITLE'] = $accttitle;

                $matches = array();
                $view->findComponents(array('path' => 'TOPACCOUNT'), EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]->setProperty('hidden', false);

                $matches = array();
                $view->findComponents(array('path' => 'TOPACCOUNTTITLE'), EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]->setProperty('hidden', false);
            }

            if ( $this->filterLocationID != '' ) {
                list($locid, $locname) = explode('--', $this->filterLocationID);
                $obj['TOPLOCATION'] = $locid;
                $obj['TOPLOCATIONNAME'] = $locname;

                //$obj['TOPLOCATION'] = $this->filterLocationID;
                $matches = array();
                $view->findComponents(array('path' => 'TOPLOCATION'), EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]->setProperty('hidden', false);

                $matches = array();
                $view->findComponents(array('path' => 'TOPLOCATIONNAME'), EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]->setProperty('hidden', false);
            }

            if ( $gridObject ) {
                $gridObject =& $gridObject[0];

                if ( IsMultiEntityCompany() && GetContextLocation() && $this->filterAccountNo != '' ) {
                    $gridObject->setProperty('noNewRows', true);
                }

                foreach ( $gridObject->children as $column ) {
                    if ( $this->filterAccountNo != '' ) {
                        $matches = array();
                        /** @var EditorComponent $column */
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $childMatch = $column->findComponents(
                            array('path' => 'ACCOUNTNO'), EditorComponentFactory::TYPE_FIELD, $matches
                        );

                        foreach ($matches as $match) {
                            $match->setProperty('hidden', true);
                            $match->setProperty('required', false);
                        }
                    }

                    if ( $this->filterLocationID != '' ) {
                        $matches = array();
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $childMatch = $column->findComponents(
                            array('path' => 'LOCATIONID'), EditorComponentFactory::TYPE_FIELD, $matches
                        );
                        foreach ( $matches as $match ) {
                            $match->setProperty('hidden', true);
                            $match->setProperty('required', false);
                        }
                    }
                }
            }

        } else {

            if ( IsMultiEntityCompany() && GetContextLocation() != '' ) {
                $matches = array();
                $view->findComponents(array('path' => 'LOCATIONID'), EditorComponentFactory::TYPE_FIELD, $matches);
                foreach ( $matches as $match ) {
                    $match->setProperty('readonly', true);
                }

                if ( $state == Editor_ShowNewState ) {
                    $obj['LOCATIONID'] = DefaultMELocation(true);
                }
            }
        }

        return $ok;
    }

    /**
     * CanDeleteOnGrid
     *     override parent and disable delete on grid
     *
     * @param array $entity entity name
     * @param array $object object array
     *
     * @return bool status
     */
    public function CanDeleteOnGrid($entity, $object)
    {
        return false;
    }

    /**
     * FigureOutNumOfRows
     *     override parent and set the numOfRows
     *
     * @param array $_layout layout array
     * @param array &$obj    object array
     *
     * @return int numOfRows
     */
    public function FigureOutNumOfRows($_layout, &$obj = null)
    {
        $numOfRows = parent::FigureOutNumOfRows($_layout, $obj);

        if ( $_layout['path'] == 'TITLEGRID' ) {
            $numOfRows = ( IsMultiEntityCompany() && GetContextLocation() != '' ? 1 : 10 );
        }

        return $numOfRows;
    }

    /**
     * @return AcctTitleByLocManager|EntityManager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof AcctTitleByLocManager);

        return $this->entityMgr;
    }
}

