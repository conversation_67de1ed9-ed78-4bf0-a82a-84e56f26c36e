<?php
//=============================================================================
//
//	FILE:			drill.pthml
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	Contains functions for generating the report pages
//
//	(C)2001, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'html_header.inc';
require_once 'util.inc';
require_once 'std_reports.inc';
require_once 'backend_reports.inc';

Init();
$_from = Request::$r->_from;
$_groupid = Request::$r->_groupid;
$_location = &Request::$r->_location;
$_reportingBook = &Request::$r->_reportingBook;
$_offreporttype = Request::$r->_offreporttype;
$_drill = Request::$r->_drill;
$_ownerKey = &Request::$r->_ownerKey;
$_reportid = Request::$r->_reportid;
$_email = Request::$r->_email;
$_colIdx = Request::$r->_colIdx;
$_path = Request::$r->_path;
$_emailccme = &Request::$r->_emailccme;
$_emailmessage = Request::$r->_emailmessage;
$_emailsubject = Request::$r->_emailsubject;
$_emailto = Request::$r->_emailto;
$_reportingachdrkey = &Request::$r->_reportingachdrkey;
$_pdf = Request::$r->_pdf;
$_excel = Request::$r->_excel;
$_deptlocgrpsort = &Request::$r->_deptlocgrpsort;

//Retrieve the report
if ( isset($_drill) ) {
    $report = GetDrillReport($_reportid, $_groupid, $_drill, $_path);
    if ( $_drill == 'explode' ) {
        $report['hlpfile'] = 'Summary_Detail';
    } else {
        $report['hlpfile'] = 'HTML_Financial_Report';
    }
} else {
    $report = GetTempReport(Globals::$g->_userid, $_reportid);

    $report['hlpfile'] = 'HTML_Financial_Report';
    if ( isset($report['LOCATIONGRP#']) && $report['LOCATIONGRP#'] != '' ) {
        global $LocGrpRec;
        $LocGrpRec = $report['LOCATIONGRP#'];
    }
    if ( isset($report['DEPTGRP#']) && $report['DEPTGRP#'] != '' ) {
        global $DeptGrpRec;
        $DeptGrpRec = $report['DEPTGRP#'];
    }
    if ( isset($report['PROJECTGROUPKEY']) && $report['PROJECTGROUPKEY'] != '' ) {
        global $ProjectGrpRec;
        $ProjectGrpRec = $report['PROJECTGROUPKEY'];
    }
    if ( isset($report['OWNERKEY']) && ( $report['OWNERKEY'] != '' ) ) {
        global $_allowOwnerValid;
        $_ownerKey = $report['OWNERKEY'];
        $_allowOwnerValid = true;
    }
    if ( isset($report['LOCATION#']) && ( $report['LOCATION#'] != '' ) ) {
        $_location = $report['LOCATION#'];
    }

    if ( isset($report['format']['REPORTINGACHDRKEY'])
         && $report['format']['REPORTINGACHDRKEY'] != ''
    ) {
        $_reportingachdrkey = $report['format']['REPORTINGACHDRKEY'];
    }

    $repParams = unserialize($report['PARAMETERS']);

    if ( $repParams['DEPTLOCGRPSORT'] == 'T' ) {
        $_deptlocgrpsort = 'T';
    }

    $_reportingBook = $report['REPORTINGBOOK'];
}

$finDimInfo = GetCategorizedReportDimensions();
$reporttitledimensions = [];

// Builds the dimensions map for the report title section.
DimensionsMap($finDimInfo, $reporttitledimensions, $report);
$report['REPORTTITLEDIMENSIONS'][0] = $reporttitledimensions;

// we need to set all dimension global variables
$dimFields = GetDimensionFieldInfo();

foreach ( $dimFields as $dimid => $field ) {
    if ( in_array($dimid, [ 'department', 'location' ]) ) {
        continue;
    }

    // global variable for the dimension
    $val = str_replace(".", "_", $field['path']);

    $$val = &Request::$r->$val;

    // get the value from $report
    $dimdbkey = isl_strtoupper($field['dimdbkey']);
    $dimval = $report[$dimdbkey];
    $$val = $dimval;
}

$report['DRILL'] = htmlspecialchars($_drill, ENT_COMPAT);
$report['DRILLGROUP'] = htmlspecialchars($_groupid, ENT_COMPAT);
$report['_DRILLPATH'] = htmlspecialchars($_path, ENT_COMPAT);
$report['_FROM'] = htmlspecialchars($_from, ENT_COMPAT);
$report['_COLIDX'] = htmlspecialchars($_colIdx, ENT_COMPAT);

if ( $_from === 'graph' && isset($_colIdx) && $_colIdx !== '' ) {
    $report['showcolswhiledrilldown'][] = 0; // Always show DSC column
    $report['showcolswhiledrilldown'][] = (int) $_colIdx + 1;
}

$reportParams = [
    'reportparams' => $report,
    'userid'       => Globals::$g->_userid,
    '2stage'       => true,
    'report'       => 'gl_fin',
    'description'  => $report['NAME'],
    'title'        => $report['NAME'],
];

if ( isset($_pdf) && $_pdf ) {
    $reportParams['type'] = kShowPDF;
} else if ( isset($_excel) && $_excel ) {
    $reportParams['type'] = kShowExcel;
} else if ( isset($_email) && $_email ) {
    if ( ! CheckEmailList($_emailto) ) {
        Globals::$g->gErr->addError('GL-3522', __FILE__ . ':' . __LINE__, "Illegal format");
        include 'popuperror.phtml';
        exit();
    }
    $reportParams['type'] = kEmail;
    $reportParams['emailoptions'] = [
        'emailto'      => $_emailto,
        'emailccme'    => $_emailccme == 'on',
        'emailsubject' => $_emailsubject,
        'emailmessage' => $_emailmessage,
    ];
    $reportParams['offreporttype'] = $_offreporttype;
} else {
    $reportParams['type'] = kShowHTML;
}

/**
 * ShowReport from the Reporter takes care of all Business logic to build the Financial report
 */
$glFinReporter = new GLFinancialReporter($reportParams);
$ok = $glFinReporter->ShowReport();

// This block is only for _email type
if ( isset($reportParams['offreporttype']) ) {
    $tokens = [ 'IA.DONE', 'IA.COULDNT_GENERATE_REPORT_CHECK_YOUR_FILTERS', 'IA.REPORT_PROCESSED_EMAIL_MSG',
                'IA.INTACCT_GL_FINANCIALS_REPORT_TITLE' ];
    I18N::addTokens(I18N::tokenArrayToObjectArray($tokens));
    $textMap = I18N::getText();
    $showMessage = false;
    $msg = '';
    if ( ! $ok ) {
        if ( HasErrors() ) {
            include 'popuperror.phtml';
        } else {
            $msg = "IA.COULDNT_GENERATE_REPORT_CHECK_YOUR_FILTERS";
            $showMessage = true;
        }
    } else {
        $msg = 'IA.REPORT_PROCESSED_EMAIL_MSG';
        $showMessage = true;
    }
    if ( $showMessage ) {
        $props = [];
        $props['nocheck'] = true;
        $props['nojs'] = true;
        $props['nohotkey'] = true;
        $props['nobeancss'] = true;
        $props['incJSValidationFiles'] = false;
        $props['title'] = GT($textMap, 'IA.INTACCT_GL_FINANCIALS_REPORT_TITLE');
        PrintCommonHtmlHeader($props);
        $msg = GT($textMap, $msg);
        UIUtils::ShowMessageStatic($msg, '', 'center');

        ?>
        <table valign="center">
            <tr>
                <td><input class="btn btn-primary" type="submit" name=".done"
                           value="<?= GT($textMap, 'IA.DONE') ?>"
                           onclick="window.close();"></td>
            </tr>
        </table>
        </body>
        </html>
        <?php
    }
} else if ( HasErrors() ) {
    include 'popuperror.phtml';
}
Shutdown();
