<?php

/**
 * Editor class for the Transaction Template type object
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for the Transaction Template type object
 */
class TransTmplBatchEditor extends FormEditor
{

    /**
     * @param array $_params the parameters of the class
     */
    public function __construct($_params)
    {
        parent::__construct($_params);
    }
    
    /**
     * Return an Array of javascript files to include into the page
     * 
     * @return array the list of javascript files to include 
     */
    protected function getJavaScriptFileNames()
    {
        return array('../resources/js/posttmpl.js');
    }

    /**
     * Transform the data before the view is build
     *
     * @param array &$obj the object data
     * 
     * @return bool false if errors else true
     */
    protected function transformBizObjectToView(&$obj)
    {
        // Loop through the entries and convert the original USERPROV / HIDE values to their
        // equilavent in the drop-down
        if (!empty($obj['TRANSTMPLENTRY']) && is_array($obj['TRANSTMPLENTRY'])) {
            foreach ($obj['TRANSTMPLENTRY'] as &$entry) {

                $columns = array('DEPT', 'LOC', 'DIM');

                foreach ($columns as $column) {
                    if ($entry[$column . 'USERPROV'] == $entry['HIDE' . $column]) {
                        $entry[$column . 'DISPLAY'] = 'Read-only';
                    } else if ($entry[$column . 'USERPROV'] == 'T') {
                        $entry[$column . 'DISPLAY'] = 'Editable';
                    } else {
                        $entry[$column . 'DISPLAY'] = 'Hidden';
                    }
                }
            }
            unset($entry); // Unset the reference
        }

        return true;
    }

    /**
     * Transform the view data before is reaches the manager
     *
     * @param array &$obj the view data
     * 
     * @return bool false if errors else true
     */
    protected function transformViewObjectToBiz(&$obj)
    {
        // Loop through the entries and convert the drop-down value to the original USERPROV / HIDE values
        foreach ( $obj['TRANSTMPLENTRY'] as &$entry ) {

            $columns = array('DEPT', 'LOC', 'DIM');

            foreach ( $columns as $column ) {

                switch ( $entry[$column . 'DISPLAY'] ) {
                case 'Read-only':
                    $entry[$column . 'USERPROV'] = $entry['HIDE' . $column] = 'F';
                    break;
                case 'Editable':
                    $entry[$column . 'USERPROV'] = 'T';
                    $entry['HIDE' . $column] = 'F';
                    break;
                case 'Hidden':
                    $entry[$column . 'USERPROV'] = 'F';
                    $entry['HIDE' . $column] = 'T';
                    break;
                }
            }
        }
        unset($entry); // Unset the reference

        return true;
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according
     * to the current data & state
     *
     * @param array &$obj the data
     *
     * @return bool|array
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        // Get the view
        $view = $this->getView();

        // We need to remove the dimensions related fields in case the company has no dimensions enabled
        $dims = IADimensions::getActiveGLDimensions(true);
        
        if ( empty($dims) ) {
            // Hide the line details
            $matches = array();
            $view->findComponents(array('id' => 'entryDetails'), EditorComponentFactory::TYPE_LINEDETAILS, $matches);
            if ( $matches ) {
                $matches[0]->setProperty('hidden', true);
            }
        }

        if ( $this->state != Editor_ShowNewState ) {
            // Template ID should be read-only if we are not in Add mode
            $matches = array();
            $view->findComponents(array('path' => 'TEMPLATEID'), EditorComponentFactory::TYPE_FIELD, $matches);
            if ( $matches ) {
                $matches[0]->setProperty('readonly', true);
            }
        }

        /* @var GLBatchManager $glBatchMgr */
        $glBatchMgr = Globals::$g->gManagerFactory->getManager('glbatch');
        if (!isset($obj['JOURNAL_BILLABLE'])) {
            $journalArr = explode("--", $obj['GLJOURNALNAME']);
            $showBillable = $glBatchMgr->isJournalBillabled($journalArr[0]);
        } else {
            $showBillable = ($glBatchMgr->isBillableEnabled() && $obj['JOURNAL_BILLABLE'] === 'T');
        }

        /* If Billable is applicable for selected journal-entry, then we need to unhide Billable column in the Grid*/
        if ($showBillable) {
            $billableMatches = array();
            $view->findComponents(array('path' => 'BILLABLE'), EditorComponentFactory::TYPE_GRID,
                $billableMatches);
            if ($billableMatches[0]) {
                $billableMatches[0]->setProperty('hidden', false);
            }
        }
        return true;
    }

    /**
     * Sets glaccount's auto-fill flag from platform
     *
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {
        if ( !util_isPlatformDisabled() ) {
            $matches = array();
            self::findElements(
                $params, array('path' => 'GLACCTNO'), EditorComponentFactory::TYPE_FIELD, $matches
            );

            if ( $matches ) {
                $matches[0]['autofillrelated'] = Pt_StandardUtil::autoFillRelated('glaccount');
            }
        }
    }

    /**
     * This function will return the list of fields to add in the Defaults popup for the grid
     * 
     * @param array &$_params the metadata
     * @param array $objRec   the ownedobject information
     * 
     * @return array the list of fields to add into the popup
     */
    protected function getGridDefaultsFields(&$_params, $objRec)
    {
        $fields = parent::getGridDefaultsFields($_params, $objRec);

        // Add 'dimensions show as' if we have dimensions
        $dims = IADimensions::getActiveGLDimensions();
        if ( !empty($dims) ) {
            $fields[] = array('path' => 'DIMDISPLAY', 'required' => false);
        }  
        
        // Do we have departments ?
        if ( departmentsExist() ) {
            $fields[] = array('path' => 'DEPARTMENT');
            $fields[] = array('path' => 'DEPTDISPLAY', 'required' => false);
        }

        // Do we have locations ?
        if ( locationsExist() ) {
            $fields[] = array('path' => 'LOCATION');
            $fields[] = array('path' => 'LOCDISPLAY', 'required' => false);
        }
        
        return $fields;
    }
    
    /**
     * Hook for subclasses to prepare the object during a copy for new action
     * At the time of the call the object is in business form
     * 
     * @param array $obj the object (in and out)
     * 
     * @return bool  false if error else true
     */  
    protected function prepareObjectForCopyNew(&$obj)
    {
        // unset the template ID
        unset($obj['TEMPLATEID']);
        return true;
    }
    
    /**
     * Reset the obj data array keys on Save and New / Duplicate
     * 
     * @param EntityManager  $entityMgr
     * @param array   &$obj
     * @param bool $isCopyNew 
     */
    protected function resetKeys($entityMgr, &$obj, $isCopyNew = false)
    {
        // We will remove all the data for save and new
        if ( $isCopyNew ) {
            parent::resetKeys($entityMgr, $obj, $isCopyNew);
        }else {        
            $obj = array();
        } 
    }

    /**
     * @return array
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        /* @var GLBatchManager $glBatchMgr */
        $glBatchMgr = Globals::$g->gManagerFactory->getManager('glbatch');
        $vars['IS_BILLABLE'] = $glBatchMgr->isBillableEnabled();
        if ( !util_isPlatformDisabled() ) {
            if ( $vars == null ) {
                $vars = array();
            }

            $vars['platformPTRDimensionReverseFieldNames'] = array('ACCOUNTNO' => 'GLACCTNO');
            $vars['platformPTRDimensionFieldNames'] = array('GLACCTNO' => 'ACCOUNTNO');
        }

        return $vars;
    }

    /**
     * transtmplbatch object is not exposed to platform but is a transaction screen
     *
     * @return bool
     */
    protected function isTransactionObject()
    {
        return true;
    }

    /**
     * By default none of the transactions will have accountno as dimension
     * GLBatchEditor needs to unset it
     *
     * @param array $platformPTRNotSupportedDimensionFieldNames
     */
    protected function addNotSupportedDimensionFieldNames_AutofillRelated(&$platformPTRNotSupportedDimensionFieldNames)
    {
        $platformPTRNotSupportedDimensionFieldNames[] = 'STATACCOUNTNO';
    }
}
