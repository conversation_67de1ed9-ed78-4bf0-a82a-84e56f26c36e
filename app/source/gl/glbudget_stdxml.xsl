<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
	<xsl:output encoding="UTF-8"/>
    <xsl:variable name="column_count">
        <xsl:value-of select="(/reportdata/report/@column_count)"/>
    </xsl:variable>
	<xsl:template match="report">
		<report showHeader="{@showHeader}"
                department="{@department}" location="{@location}" territory="{@territory}"
                orientation="{@orientation}" report_date="{@reportdate}" report_time="{@reporttime}"
                align_currency="left" page_number="Y" action="reportor.phtml" sess="{@sess}"
                done="" footer_allpages="Y" showtitlessection="{@showtitlessection}">
            <xsl:if test="($column_count &gt; 0)">
                <xsl:attribute name="pagelayoutsize">custom</xsl:attribute>
                <xsl:attribute name="additionalColCount">
                    <xsl:value-of select="$column_count"/>
                </xsl:attribute>
                <xsl:attribute name="dynamicColWidth">40</xsl:attribute>
            </xsl:if>
            <xsl:if test="(@orientation = 'Portrait')">
                <xsl:attribute name="maxfit">Y</xsl:attribute>
            </xsl:if>
            <company s="2">
                <xsl:value-of select="@co"/>
            </company>

            <title s="3" titleNum="1">
                <xsl:value-of select="@title"/>
            </title>
            <title s="4" titleNum="2">
                <xsl:value-of select="@title2"/>
            </title>
			<header>
				 <hrow s="6">
					<xsl:apply-templates select="dimensionHeader"/>
				 </hrow>				
			</header>
			<body s="1">					
				<xsl:apply-templates select="exportdata"/>						
			</body>
			<xsl:call-template name="stylegroups"/>
            <xsl:call-template name="script"/>
		</report>
	</xsl:template>

	<xsl:template match="dimensionHeader">
		<xsl:apply-templates select="dimension" mode='header'/>
	</xsl:template>

	<xsl:template match="dimension" mode='header'>
        
        <xsl:choose>
            <xsl:when test="@amount = 1">
                <hcol width="9" s="8" dynamicCol="y" id="{position()-1}">
                    <xsl:value-of select="@hdrtitle"/>
                </hcol>
            </xsl:when>
            <xsl:otherwise>
                <hcol width="15" s="7" dynamicCol="y" id="{position()-1}">
			        <xsl:value-of select="@hdrtitle"/>
		        </hcol>
            </xsl:otherwise>

        </xsl:choose>         
		
	</xsl:template>

	<xsl:template match="exportdata">
		<xsl:choose>
			<xsl:when test="string(@NODATA)=1">
				<row s="9">
                    <col id="0" s="10" colspan="3">IA.NO_DATA_FOUND_FOR_SELECTED_FILTERS</col>
                </row>
			</xsl:when>
			<xsl:otherwise>	
				<xsl:apply-templates select="budgetinfo" mode='trans'/>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>

	<xsl:template match="budgetinfo" mode='trans'>
		<row s="9">
			<xsl:apply-templates select="budgetrow" mode='trans'/>
		</row>
	</xsl:template>

	<xsl:template match="budgetrow" mode='trans'>
        <xsl:choose>
			<xsl:when test="@amount = 1 and (@url != '' or @sibpurl != '')">
                <col s="14" id="{position()-1}">
					<xsl:choose>
						<!--SIBP drill down -->
						<xsl:when test="@sibpurl != ''">
							<xsl:attribute name="href">
								<xsl:text>javascript:window.open('</xsl:text>
								<xsl:value-of select="@sibpurl"/>
								<xsl:text>');</xsl:text>
							</xsl:attribute>
						</xsl:when>
						<!-- Default Intacct drill down -->
						<xsl:otherwise>
							<xsl:attribute name="href">
								<xsl:text>javascript:budgetdrill('</xsl:text>
								<xsl:value-of select="@url"/>
								<xsl:text>');</xsl:text>
							</xsl:attribute>
						</xsl:otherwise>
					</xsl:choose>
                    <xsl:value-of select="@budgetdata"/>
                </col>
            </xsl:when>
            <xsl:when test="@amount = 1 and @field = 'total'">
                <col s="14" id="{position()-1}">
                    <xsl:value-of select="@budgetdata"/>
                </col>
            </xsl:when>
            <xsl:otherwise>
                <col s="10" id="{position()-1}">
                    <xsl:value-of select="@budgetdata"/>
                </col>
            </xsl:otherwise>

        </xsl:choose>        
	</xsl:template>

	<xsl:template name="stylegroups">
		<stylegroups>
			<stylegroup id="1" name="body" class="BODY" font="Helvetica" size="8" weight="normal" style="normal" color="black"/>
			<stylegroup id="2" name="company" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>
			<stylegroup id="3" name="title" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>
			<stylegroup id="4" name="title2" class="W" font="Helvetica" size="12" weight="bold" style="normal" color="black"/>
			<stylegroup id="5" name="footer" class="FOOT" font="Helvetica" size="9" weight="bold" style="normal" color="black" alignment="M"/>
			
			<stylegroup id="6" name="header_row" class="HEAD" font="Helvetica" size="9" weight="bold" style="normal" color="black"  underline_type="1"/>
			<stylegroup id="7" name="header_col" class="REPCOLHDL"/>
			<stylegroup id="8" name="header_CN1" class="REPCOLHDR" type="number"/>
            
			
			<stylegroup id="9" name="inv_row" font="Helvetica" size="8" style="normal" weight="normal" color="black"/>
			<stylegroup id="10" name="inv_ct" class="W1"/>
			<stylegroup id="11" name="inv_cn" class="R" type="currency"/>
			
			<stylegroup id="12" name="total_row" class="RDGB" font="Helvetica" size="9" style="normal" weight="bold" color="black" underline_type="2" totals_line="Y"/>
			<stylegroup id="13" name="total_ct" class="DGB"/>
			<stylegroup id="14" name="total_cn" class="R" type="currency"/>
			
			<stylegroup id="15" name="dateformat" type="date"/>
			<stylegroup id="16" name="chk_det" class="CLR" style="italic"/>
			<stylegroup id="17" name="chk_det_amt" class="RCLR" type="currency" style="italic"/>
			<stylegroup id="18" name="Billdatehdr" class="R" align = 'right'  style="italic"/>
			<stylegroup id="19" name="check_row" class="RDGB" font="Helvetica" size="8" style="normal" weight="normal" color="black"/>
			<stylegroup id="20" name="BilldatehdrLeft"  align = 'left'  style="italic"/>

		</stylegroups>
	</xsl:template>

    <xsl:template name="script">
        <script language="javascript">
            var gSess = '<xsl:value-of select="/reportdata/report/@sess"/>';
            function budgetdrill(url) {
                url += '&amp;.popup=1&amp;.sess='+gSess;
		        var params = 'status=yes,resizable=yes,scrollbars=yes,left=50,top=50' ;  
                var hWnd = window.open(url, 'glbudgetdrill', params);
		        if (hWnd == null) {return;}    
		        hWnd.focus();		
		    }
        </script>
    </xsl:template>
    
</xsl:stylesheet>
