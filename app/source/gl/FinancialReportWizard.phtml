<?php
//=============================================================================
//
//	FILE:			FinancialReportWizard.phtml
//	AUTHOR:
//	DESCRIPTION:
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

    // Date in the past
if (($_SERVER['REQUEST_METHOD'] == "GET")) {
    header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
    header("Cache-Control: no-cache");
    header("Pragma: no-cache");
}

define("GRPACCT", "ACCT");
define("GRPACCTALL", "ACCTALL");
define("GRPACCTLEAF", "ACCTLEAF");

define("GRPDEPT", "DEP");
define("GRPDEPTP", "DEPP");
define("GRPDEPTNOP", "DEPNOP");
define("GRPALLDEPT", "DEPALL");
define("GRPALLDEPTP", "DEPALLP");
define("GRPALLDEPTNOP", "DEPALLNOP");
define("GRPALLDEPTMYBAL", "DEPALLMYBAL");


define("GRPLOC", "LOC");
define("GRPLOCP", "LOCP");
define("GRPLOCNOP", "LOCNOP");
define("GRPALLLOC", "LOCALL");
define("GRPALLLOCP", "LOCALLP");
define("GRPALLLOCNOP", "LOCALLNOP");
define("GRPALLLOCMYBAL", "LOCALLMYBAL");

define("GRPQUARTERS", "PRDQ");
define("GRPMONTHS", "PRDM");
define("GRPWEEKS", "PRDW");
define("GRPDAYS", "PRDD");

define("GRPCHILDREN", "F");
define("GRPSUMMARY", "T");
define("GRPBUDGETPERIODS", "PRDB");

define('TEXTFIELD_SIZE', 30);
define('MANDPAGES',5);

//require_once('IALayoutManager.cls');
require_once 'html_header.inc';
require_once 'util.inc';
require_once "cpa_util.inc";

// Need to initialize before loading any of the files required
// for FRW, localization constants are loaded here
Init();

require_once('std_reports.inc');
require_once('FinancialReportWizard.inc');
require_once('browser.inc');

require_once 'FinancialReportWizard.cls';

// backend_reports required for pdf stuff
require_once 'backend_reports.inc';


require_once 'Dictionary.cls';
require_once 'ReportGroupsManager.cls';

IALayoutManager::setForceLayoutType('2');

$text = TextHelper::getInstance(FinancialReportWizard::class);

$_lineno            = &Request::$r->_lineno;
$_r                 = &Request::$r->_r;
$_sess              = &Request::$r->_sess;
$_status            = &Request::$r->_status;
$_type              = &Request::$r->_type;
$_do                = &Request::$r->_do;
$_noWarn            = &Request::$r->_noWarn;
$_forceSave            = &Request::$r->_forceSave;

if(isset(Request::$r->_op)){
    Request::$r->_op = (int) Request::$r->_op;
}

// we don't need to check the create action
if ($_do !== 'create') {
    if (Globals::$g->gBlockDuplicateRequest->addLock(basename(__FILE__), [Request::$r->_op, $_do, $_r, $_sess]) === false) {
        Globals::$g->gErr->addError('GL-3532', basename(__FILE__), 'WARNING: Duplicate request attempt in FinancialReportWizard.phtml');
        include 'popuperror.phtml';

        exit();
    }
}

if ($_do === 'view' || $_do === 'edit') {
    /** @var FinancialReportLister $financialReportLister */
    $financialReportLister = Lister::getEntityLister('FinancialReport');
    // $financialReportLister->VerifyPermissions($_r);
    if(!Request::$r->_fromCSTool){
        $financialReportLister->VerifyPermissions($_r);
    }
}

$_title             = &Request::$r->_title;
$name               = &Request::$r->name;
$path               = &Request::$r->path;
$_ret               = &Request::$r->_ret;
$_titlecomment      = &Request::$r->_titlecomment;
$hlpfile            = &Request::$r->hlpfile;
$_industry          = &Request::$r->_industry;
$_dept              = &Request::$r->_dept;
$_ownerKey          = &Request::$r->_ownerKey;
$_reportingachdrkey = &Request::$r->_reportingachdrkey;
$_fromCSTool        = &Request::$r->_fromCSTool;
$_reportingBook     = &Request::$r->_reportingBook;
$_name              = &Request::$r->_name;
$_cancel            = &Request::$r->_cancel;
Request::$r->_accountOrDimensionRows = htmlspecialchars(Request::$r->_accountOrDimensionRows ?? '', ENT_COMPAT);
$_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
$_aboveThresholdColor    = &Request::$r->_aboveThresholdColor;
$_align_currency         = &Request::$r->_align_currency;
$_belowThresholdColor    = &Request::$r->_belowThresholdColor;
$_colAccountGrp          = &Request::$r->_colAccountGrp;
$_colasof                = &Request::$r->_colasof;
$_colAttributeField  = &Request::$r->_colAttributeField;
$_colAttributeObject = &Request::$r->_colAttributeObject;
$_colBudValue        = &Request::$r->_colBudValue;
$_colCompValue       = &Request::$r->_colCompValue;
$_colNotationValue       = &Request::$r->_colNotationValue;
$_colfontbold        = &Request::$r->_colfontbold;
$_colfontcolor       = &Request::$r->_colfontcolor;
$_colfontitalic      = &Request::$r->_colfontitalic;
$_colfontname        = &Request::$r->_colfontname;
$_colfontsize        = &Request::$r->_colfontsize;
$_colhdr1            = &Request::$r->_colhdr1;
$_colhdr2            = &Request::$r->_colhdr2;
$_colhide            = &Request::$r->_colhide;
$_colPeriod          = &Request::$r->_colPeriod;
$_colPeriodOffset    = &Request::$r->_colPeriodOffset;
$_colPeriodOffsetBy  = &Request::$r->_colPeriodOffsetBy;
$_colpgbreak         = &Request::$r->_colpgbreak;
$_colPrecision       = &Request::$r->_colPrecision;
$_colreportingMethod = &Request::$r->_colreportingMethod;
$_colShowAs          = &Request::$r->_colShowAs;
$_colTitle           = &Request::$r->_colTitle;
$_colValueType       = &Request::$r->_colValueType;
$_colwidth           = &Request::$r->_colwidth;
$_compareby          = &Request::$r->_compareby;
$_compOnCol          = &Request::$r->_compOnCol;
$_conditionWithMessage = &Request::$r->_conditionWithMessage;
$_definedby            = &Request::$r->_definedby;
$_footer_alignment   = &Request::$r->_footer_alignment;
$_expof              = &Request::$r->_expof;
$_firsttime          = &Request::$r->_firsttime;
$_enableColorScale   = &Request::$r->_enableColorScale;
$_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;
$_jumpto             = &Request::$r->_jumpto;
$_jumpfrom           = &Request::$r->_jumpfrom;
$_jump               = &Request::$r->_jump;
$_isadd              = &Request::$r->_isadd;
$_currentcompcolindex= &Request::$r->_currentcompcolindex;
$_currentnotationcolindex= Request::$r->_currentnotationcolindex;
$_isremove           = &Request::$r->_isremove;
$_indicatorType      = &Request::$r->_indicatorType;
$_hidezerobalcol     = &Request::$r->_hidezerobalcol;
$_displaydash        = &Request::$r->_displaydash;
$_displayfooteras    = &Request::$r->_displayfooteras;
$_groups             = &Request::$r->_groups;
$_originalColSortOrd = &Request::$r->_originalColSortOrd;
$_pg                 = &Request::$r->_pg;
$_next               = &Request::$r->_next;
$_prev               = &Request::$r->_prev;
$_ratiobaseGrp       = &Request::$r->_ratiobaseGrp;
$_repname            = &Request::$r->_repname;
$_reportingachdrkeyAsOf = &Request::$r->_reportingachdrkeyAsOf;
$_repTimeStamp          = &Request::$r->_repTimeStamp;
$_rightlefthide         = &Request::$r->_rightlefthide;
$_rptowner              = &Request::$r->_rptowner;
$_runtimeparams         = &Request::$r->_runtimeparams;
$_save               = &Request::$r->_save;
$_topbotcol          = &Request::$r->_top;
$_topbotcol          = &Request::$r->_topbotcol;
$_trendIncreaseClr   = &Request::$r->_trendIncreaseClr;
$_trendType          = &Request::$r->_trendType;
$_totalcompcol       = &Request::$r->_totalcompcol;
$_totalnotationcol       = &Request::$r->_totalnotationcol;
$_compName           = &Request::$r->_compName;
$_compDesciption    = &Request::$r->_compDescription;
$_notationName           = &Request::$r->_notationName;
$_notationDesciption    = &Request::$r->_notationDescription;
$_saveas             = &Request::$r->_saveas;
$_savestay           = &Request::$r->_savestay;
$_sortord            = &Request::$r->_sortord;
$_summaryStr         = &Request::$r->_summaryStr;
$_switchColsTable    = &Request::$r->_switchColsTable;
$_threshold          = &Request::$r->_threshold;
$_thresholdType      = &Request::$r->_thresholdType;
$_title2             = &Request::$r->_title2;
$_schopKey           = &Request::$r->_schopKey;
$addChangeAccountsGroups_STRINGS = &Request::$r->addChangeAccountsGroups_STRINGS;
$_viewe              = &Request::$r->_viewe;
$_viewh              = &Request::$r->_viewh;
$_viewp              = &Request::$r->_viewp;
$_viewt              = &Request::$r->_viewt;
$_showDimIDNAME      = &Request::$r->_showDimIDNAME;
$_ir_dept            = &Request::$r->_ir_dept;
$_ir_location        = &Request::$r->_ir_location;
$_ir_project        = &Request::$r->_ir_project;
$threshold           = &Request::$r->threshold; // TODO ERG: Request variable ? - set in Request in app/source/gl/FinancialReportWizard.inc, MergeDimensionFields($pg) var $kSetupFields which contains it
$_header_alignment   = &Request::$r->_header_alignment;
$_cumbal             = &Request::$r->_cumbal;
$_glreporttype       = &Request::$r->_glreporttype;
$_glreportaudience   = &Request::$r->_glreportaudience;
$_ownershipStructure   = &Request::$r->_ownershipStructure;
$_doNotWarn = &Request::$r->_doNotWarn;
$doNotWarnCacheKey = FinancialReportWizard::getDoNotWarnCacheKey();
$doNotWarnCached = CacheClient::getInstance()->get($doNotWarnCacheKey);

if($_doNotWarn && $doNotWarnCached == false){
    $doNotWarnCached = CacheClient::getInstance()->set($doNotWarnCacheKey, 'true');
}
if($doNotWarnCached){
    $_forceSave = $_noWarn = true;
}

$_version = &Request::$r->_version;
$frwVersionHistorymgr = Globals::$g->gManagerFactory->getManager('frwversionhistory');
$postBackUrl = ScriptRequest();
$report_warnings = [];
if ( ! empty($_version) ) {
    if(! $frwVersionHistorymgr->isValidVersionForReport($_version, $_r)){
        Globals::$g->gErr->addError('GL-7030', basename(__FILE__), 'Invalid financial report version');
        include 'popuperror.phtml';

        exit();
    }

    // If its a save action with version param, then we are restoring the version
    if ( $_savestay ) {
        $postBackUrl = URLS::removeQueryParams($postBackUrl, ['_version']);
        Request::$r->_restoredFrom = $_version;
        Request::$r->_version = null;
    }
}



global $NEWREPORTEDITOR;
$NEWREPORTEDITOR = true;
$gNonStandardPeriods = Globals::$g->gNonStandardPeriods;

[$user_rec,$cny] = explode('@', $_userid);

$_fromCSTool = &Request::$r->_fromCSTool;
$fromCSTool = (isset($_fromCSTool) && $_fromCSTool!='')?true:false;

// lets run the Supervisor if we do not have a save operation
if (!$_save && !$_saveas && !$_savestay) {
    $supervisor = new Supervisor();
    $supervisor->run();
}

$_colPeriodOffset = htmlspecialchars($_colPeriodOffset ?? '', ENT_COMPAT);

//Below Parms must be int
if( isset($_r) ){
    $_r = (int) $_r;
}
if( isset($_jumpto) ){
    $_jumpto = (int) $_jumpto;
}
if( isset($_pg) ){
    $_pg = (int) $_pg;
}
if( isset($_jumpfrom) ){
    $_jumpfrom = (int) $_jumpfrom;
}

try {
    if ($_fromCSTool == false && $_pg != '') {
        if ( !CsrfUtils::verifyInputToken(Request::$r->_op)) {
            popupErrorAndShutdown(
                'BL03002133',
                __FILE__ . ":" . __LINE__,
                "Invalid request. Please Retry.",
                "CSRF: Financial Report Wizard page csrf token mismatch",
                false
            );
        }
    }
    if ($_cancel) {
        $popup = &Request::$r->_popup;
        if (isset($popup) && $popup === "1") {
            $js .= 'window.close();';
            echo '<html><head><title>'.$text->GT('IA.DONE').'</title></head><body onload="' . $js . '"><A href="#" onclick="' . $js
                 . '">'.$text->GT('IA.CLOSE_THIS_WINDOW').'</A></body></html>';
            exit();
        }
        if ( ! empty($_version) ) {
            // to return back to original page instead of lister
            Redirect(URLS::removeQueryParams(ScriptRequest(), ['_version']));
        } else {
            Ret();
        }
    }

    if (Request::$r->_addToDashboard) {
        setDashboardForReport();
        exit();
    };

    global $_columnTabRows;
    global $kSetupPages;

    global $gNoReportSave;

    $_industry = &Request::$r->_industry;

    $_switchColsTable = 'C'; // always C now

    global $appendDimGroupToEndOfValue;
    $appendDimGroupToEndOfValue = true;

    // default page if not set, and handle next/previous/delete actions
    if ( !isset($_pg) || $_pg == '') {
        $_pg = '1';
    }
    if ($_next) {
        $_pg++;
    } else if ($_prev) {
        $_pg--;
    } else if ($_jump) {
        $_pg = $_jumpto;
    } else if (isset($_do) && $_do == 'del') {
        ExecStmt(["delete from reportinfo where cny# = :1 and record# = :2", $cny, $_r]);
    }

    if (($_pg == ($_jumpto == '3')) && isset($addChangeAccountsGroups_STRINGS) && $addChangeAccountsGroups_STRINGS != '') {
        //We have names not IDs because of the frackking list fields archetecure. What a crock.
        InitReportBaseForAddingGroupsByName($fromCSTool, $_industry);

        $groups = [];
        $kDelim = '#~#';
        $groups = explode($kDelim, $addChangeAccountsGroups_STRINGS);

        $paths = [];

        $new_groups = '';
        $bogusAccountGroupsCt = 0;
        $curname = '';
        foreach ($groups as & $group) {
            $curname = $group;
            if ($curname == '') {
                continue;
            }
            $group = GetUIAcctGrpIDFromName($curname);
            if ( !isset($group)) {
                $bogusAccountGroupsCt++;
                $bogusAccountGroups = ( !isSet($bogusAccountGroups)) ? $curname : ($bogusAccountGroups . ', ' . $curname);
            }

            $new_groups = ($new_groups == '') ? $group : $new_groups . $kDelim . $group;
        }
        unset($group);
        $_groups = $new_groups;
    }

    // to set dimension fields in global variables to make wizard works
    //page 1 is to get everything initialized correctly.
    if ($_pg == '1' || $_pg == '10' || $_jumpfrom == '10') {
        //calls GetDimensionFieldInfo internally which sets the global  $finDimInfo :-(
        MergeDimensionFields('10'); //This is all the DIM fields that are required on page '10' the filters page.
    }
    $kSetupPages = GetFinWizardPage();
    $page = $kSetupPages[$_pg];

    global $_userid, $gManagerFactory;
    $_repTimeStamp = $_repTimeStamp ?: ServeCurrentTimestamp(1);
    $objStoreName = "FinWizard_" . $_userid . "_" . $_r . $_repTimeStamp;

    //Here we need to update the values from previous tab4
    $prevTabVals = $kSetupPages[$_jumpfrom]['reqhiddenfields'];

    if ($prevTabVals) {
        StoreFields($prevTabVals, $objStoreName);
    }
    $objectStore = $gManagerFactory->getManager('objectstore');
    $objectStoreVals = $objectStore->get($objStoreName);
    if ($objectStoreVals) {
        foreach ($objectStoreVals['OBJECTDATA'] as $key => $val) {
            //Values has to be set in the variables
            $name = $key;
            if ($key === '' || $key[0] !== '_') {
                $name = "_" . $key;
            }
            Request::$r->$name = $val;
        }
    }

    if (isset($_reportingachdrkeyAsOf) && $_reportingachdrkeyAsOf != '0') {
        $_reportingachdrkey = $_reportingachdrkeyAsOf;
    } else if (isset($_reportingachdrkeyAsOf) && $_reportingachdrkeyAsOf == '0') {
        $_reportingachdrkey = '';
    }
    if ( !isset($_status)) {
        $_status = 'active';
    }
    if (isset($_status) && $_status != 'active') {
        $_status = 'inactive';
    }
} catch (Exception $excp) {
    Globals::$g->gErr->addError('GL-2120', __FILE__ . ':' . __LINE__, "Error with the transaction.".$excp->getMessage());
    //i18n::todo (code change review)
    $ok = false;
} finally {
    //equivalent to if (!$_save && !$_saveas && !$_savestay) above
    if (isset($supervisor)) {
        $supervisor->stop();
    }
}

if ($_save || $_saveas || $_savestay || $_viewt || $_viewh || $_viewp || $_viewe) {
    getLocDeptRecNo();
}

Globals::$g->gBlockDuplicateRequest->deleteLock();

if ($_pg ==  '3' || $_pg ==  '5' ) {
    $gOrderByTypeFirst = true;
};
    InitReportBase($fromCSTool, $_industry);

/**
 * @param array $actgrpmap
 *
 * @return array
 */
function fw_fixAccountGrpMap($actgrpmap)
{
    //the gAccount groups is filter by type and then name.
    //a late change to the UI was to only do this for DIM STRRUCTURES/COMPONENTS
    //and it requires a resorting.

    $accounts = array();
    $systemAccounts = array();
    $dims_structure = array();
    $systemDimStructures = array();
    $dims_group = array();

    $accountGroupAcctTypes = array('A', 'G', 'S',  'C', 'T', 'L');
    foreach ($actgrpmap as $key => $val) {
        if (in_array($val['MEMBERTYPE'], $accountGroupAcctTypes)) {
            //$val['MEMBERTYPE'] = 'A';
            if (isl_str_startswith($val['NAME'], 'System_')) {
                $systemAccounts[$key] =  $val;
            } else {
                $accounts[$key] =  $val;
            }
        } else {
            if ( isl_str_endswith($val['MEMBERTYPE'], 'GD') ) {
                $dims_group[$key] = $val;
            } else if ( isl_str_startswith($val['NAME'], 'System_') ) {
                $systemDimStructures[$key] = $val;
            } else {
                $dims_structure[$key] = $val;
            }
        }
    }

    $dims = array_merge($dims_group, $dims_structure);
    uasort($accounts, 'fw_AccountGroupSortFunction');

    $fromCSTool = empty(Request::$r->_fromCSTool) ? false : true;
    if ($fromCSTool || IsPracticeCompany(GetMyCompany())) {
        return array_merge($accounts, $systemAccounts, $dims, $systemDimStructures);
    }

    return array_merge($accounts, $dims);
}

/**
 * @param array $a
 * @param array $b
 *
 * @return int
 */
function fw_AccountGroupSortFunction($a, $b)
{
    if ($a == $b) {
        return 0;
    }
    return ($a['NAME'] < $b['NAME']) ? -1 : 1;
}
$gAccountGroups = &fw_fixAccountGrpMap($gAccountGroups);


$defaultBudget = '';
$budgetMap = array();

$budgets = GLBudgetManager::getBudgets($_reportingBook);

foreach ( $budgets as $budget ) {
    $budgetMap[$budget['RECORD#']] = isl_htmlspecialchars($budget['BUDGETID']);

    if ($budget['SYSTEMGENERATED'] === 'T') {
        $defaultBudget = $budget['RECORD#'];
    }

    if (!$defaultBudget && $budget['DEFAULT_BUDGET'] === 'T') {
        $defaultBudget = $budget['RECORD#'];
    }
}

if (!$defaultBudget) {
    $defaultBudget = $budgets[0]['RECORD#'];
}

// firsttime is used to decide on the smartdefaulting behavior for show_currency and attach_currency on the rows and totals tabs
if ($_r) {
    $_firsttime = '0';
}
if ($_firsttime !== '0') {
    $_firsttime = '1';
}

/************************************************************************************************************
 * NOTE: START DEFAULTING OF COLUMN HEADERS.
 *
 * We need to check the order of the columns settings to make sure they are in the same order as the periods.
 * This used to be done in the column tab section of the code, but then if you changed periods and ran
 * the report, the code didn't run and therefore the columns settings would not follow their period.
 *
 * There's a problem here in that no column info may be set up yet, even if they picked periods.
 * if there is no icol0 yet, then there is no chance that they can be misaligned, so only do
 * the alignment if there is some data to be aligned.
 *
 * It's possible that the user has changed the desired order of the reporting periods.
 * So we'll need to find the index that this record hash had the last time we displayed the page,
 * and then assign the old index's value to the new index's location in the table.
 * done moving the columns settings to follow the period settings
 *
 ************************************************************************************************************/

// need to default any column headers here that are not set up
$budgetValueArray = array('b','P','V','v','nV','nv','CB','pb', 'RB', 'F', 'FF', 'rV', 'nrV');

$colhdr1 = array();
$colhdr2 = array();
$colasof = array();
$colhide = array();
$colPeriod = array();
$colPeriodOffset = array();
$colPeriodOffsetBy = array();
$colValueType = array();
$colAttributeObject = array();
$colAttributeField = array();
$compOnCol = array();

$colwidth = array();
$colTitle = [];
$colBudValue = [];
$colhdr1 = [];
$colhdr2 = [];
$colasof = [];
$sortord = [];
$originalColSortOrd = [];
$expof = [];
$colpgbreak = [];
$colfontname = [];
$colfontcolor = [];
$colfontsize = [];
$colfontitalic = [];
$colfontbold = [];

// need to split the column info into the arrays and rebuild in the correct order.

if ( isset($_colPeriod) && $_colPeriod != '' ) {
    $colPeriod = explode($kDelim, $_colPeriod);
}
if ( isset($_colPeriodOffset) && $_colPeriodOffset != '' ) {
    $colPeriodOffset = explode($kDelim, $_colPeriodOffset);
}
if ( isset($_colPeriodOffsetBy) && $_colPeriodOffsetBy != '' ) {
    $colPeriodOffsetBy    = explode($kDelim, $_colPeriodOffsetBy);
}
if ( isset($_colhdr1) && $_colhdr1 != '' ) {
    $colhdr1         = explode($kDelim, $_colhdr1);
}
if ( isset($_colhdr2) && $_colhdr2 != '' ) {
    $colhdr2         = explode($kDelim, $_colhdr2);
}
if ( isset($_colasof) && $_colasof != '' ) {
    $colasof         = explode($kDelim, $_colasof);
}
if ( isset($_colhide) && $_colhide != '' ) {
    $colhide         = explode($kDelim, $_colhide);
}
if ( isset($_compareby) && $_compareby != '' ) {
    $compareby     = explode($kDelim, $_compareby);
}
if ( isset($_rightlefthide) && $_rightlefthide != '' ) {
    $rightlefthide     = explode($kDelim, $_rightlefthide);
}
if ( isset($_summaryStr) && $_summaryStr != '' ) {
    $summaryStr     = explode($kDelim, $_summaryStr);
}
if ( isset($_colValueType) && $_colValueType != '' ) {
    $colValueType    = explode($kDelim, $_colValueType);
}
if ( isset($_colAttributeObject) && $_colAttributeObject != '' ) {
    $colAttributeObject    = explode($kDelim, $_colAttributeObject);
}
if ( isset($_colAttributeField) && $_colAttributeField != '' ) {
    $colAttributeField    = explode($kDelim, $_colAttributeField);
}
if ( isset($_colBudValue) && $_colBudValue != '' ) {
    $colBudValue    = explode($kDelim, $_colBudValue);
}
if ( isset($_colCompValue) && $_colCompValue != '' ) {
    $colCompValue    = explode($kDelim, $_colCompValue);
}
if ( isset($_colNotationValue) && $_colNotationValue != '' ) {
    $colNotationValue    = explode($kDelim, $_colNotationValue);
}
if ( isset($_compOnCol) && $_compOnCol != '' ) {
    $compOnCol    = explode($kDelim, $_compOnCol);
}
if ( isset($_colTitle) && $_colTitle != '' ) {
    $colTitle        = explode($kDelim, $_colTitle);
}
if ( isset($_colShowAs) && $_colShowAs != '' ) {
    $colShowAs    = explode($kDelim, $_colShowAs);
}
if ( isset($_colPrecision) && $_colPrecision != '' ) {
    $colPrecision    = explode($kDelim, $_colPrecision);
}
if ( isset($_colreportingMethod) && $_colreportingMethod != '' ) {
    $colreportingMethod    = explode($kDelim, $_colreportingMethod);
}
if ( isset($_indicatorType) && $_indicatorType != '' ) {
    $indicatorType    = explode($kDelim, $_indicatorType);
}
if ( isset($_enableColorScale) && $_enableColorScale != '' ) {
    $enableColorScale    = explode($kDelim, $_enableColorScale);
}
if ( isset($_threshold) && $_threshold != '' ) {
    $threshold    = explode($kDelim, $_threshold);
}
if ( isset($_aboveThresholdColor) && $_aboveThresholdColor != '' ) {
    $aboveThresholdColor    = explode($kDelim, $_aboveThresholdColor);
}
if ( isset($_belowThresholdColor) && $_belowThresholdColor != '' ) {
    $belowThresholdColor    = explode($kDelim, $_belowThresholdColor);
}
if ( isset($_thresholdType) && $_thresholdType != '' ) {
    $thresholdType    = explode($kDelim, $_thresholdType);
}
if ( isset($_conditionWithMessage) && $_conditionWithMessage != '' ) {
    $conditionWithMessage    = explode($kDelim, $_conditionWithMessage);
}
if ( isset($_trendType) && $_trendType != '' ) {
    $trendType    = explode($kDelim, $_trendType);
}
if ( isset($_trendIncreaseClr) && $_trendIncreaseClr != '' ) {
    $trendIncreaseClr    = explode($kDelim, $_trendIncreaseClr);
}
if ( isset($_colAccountGrp) && $_colAccountGrp != '' ) {
    $colAccountGrp    = explode($kDelim, $_colAccountGrp);
}

if ( isset($_expof) && $_expof !== '' ) {
    $expof = explode($kDelim, $_expof);
}
if ( isset($_sortord) && $_sortord != '' ) {
    $sortord = explode($kDelim, $_sortord);
}
if ( isset($_originalColSortOrd) && $_originalColSortOrd != '' ) {
    $originalColSortOrd = explode($kDelim, $_originalColSortOrd);
}

if ( isset($_colwidth) && $_colwidth != '' ) {
    $colwidth = explode($kDelim, $_colwidth);
}
if ( isset($_colpgbreak) && $_colpgbreak != '' ) {
    $colpgbreak = explode($kDelim, $_colpgbreak);
}
if ( isset($_colfontname) && $_colfontname != '' ) {
    $colfontname = explode($kDelim, $_colfontname);
}
if ( isset($_colfontcolor) && $_colfontcolor != '' ) {
    $colfontcolor = explode($kDelim, $_colfontcolor);
}
if ( isset($_colfontsize) && $_colfontsize != '' ) {
    $colfontsize = explode($kDelim, $_colfontsize);
}
if ( isset($_colfontitalic) && $_colfontitalic != '' ) {
    $colfontitalic = explode($kDelim, $_colfontitalic);
}
if ( isset($_colfontbold) && $_colfontbold != '' ) {
    $colfontbold = explode($kDelim, $_colfontbold);
}

if (empty($_hidezerobalcol)) {
    $_hidezerobalcol = "T";
}

$_columnTabRows = (count($colValueType) > 0) ? count($colValueType) : 2;
if (count($colValueType) == 0) {
    $colTitle[0] = '';
    $colTitle[1] = 'Actual';
    $_colTitle = $kDelim . "Actual";
}

for ($i = 0; $i < $_columnTabRows; $i++) {

    $colhdr1[$i] = ( isset($colhdr1[$i]) && $colhdr1[$i] == '' ? 'Period_Header_1' : $colhdr1[$i] );
    $colhdr2[$i] = ( isset($colhdr2[$i]) && $colhdr2[$i] == '' ? 'Period_Header_2' : $colhdr2[$i] );

    if (!isset($colValueType[$i]) || $colValueType[$i] == '') {
        $colValueType[$i] = ($i == 0 ? 'DSC' : 'x');
    }

    if ($colValueType[$i] == 'a') {
        $colValueType[$i] = 'x';
    }
}

// INITALIZE ALL THE COLUMNS PERIODS IF THEY ARE EMPTY
for ($i = 0; $i < $_columnTabRows; $i++) {
    $colPeriod[$i] = isset($colPeriod[$i]) ? isl_trim($colPeriod[$i]) : "";
    if ($colPeriod[$i] == '' && !isset($currentPeriod)) {
        $kPeriodMap = ($fromCSTool) ? GetIAPeriodMap() : GetPeriodMap();
        $temp_periodvalues = array_flip($kPeriodMap);
        $currentPeriod = $temp_periodvalues['Current Month']; //default
    }
    $colPeriod[$i] = ( $colPeriod[$i] != '' ? $colPeriod[$i] : $currentPeriod );

    if (in_array($colValueType[$i], $budgetValueArray)) {
        if (!isset($budgetMap[$colBudValue[$i]])) {
            $colBudValue[$i] = $defaultBudget;
            $colTitle[$i] = $budgetMap[$defaultBudget];
        }
    }
}

$_colPeriod = implode($kDelim, $colPeriod);
$_colPeriodOffset = implode($kDelim, $colPeriodOffset);
$_colPeriodOffsetBy = implode($kDelim, $colPeriodOffsetBy);
$_colValueType = implode($kDelim, $colValueType);
$_colTitle = implode($kDelim, $colTitle);
$_colBudValue = implode($kDelim, $colBudValue);
$_colAttributeObject = implode($kDelim, $colAttributeObject);
$_colAttributeField = implode($kDelim, $colAttributeField);
$_colhdr1 = implode($kDelim, $colhdr1);
$_colhdr2 = implode($kDelim, $colhdr2);
$_colasof = implode($kDelim, $colasof);
$_compOnCol = implode($kDelim, $compOnCol);
$_colhide = implode($kDelim, $colhide);
$_sortord = implode($kDelim, $sortord);
$_originalColSortOrd = implode($kDelim, $originalColSortOrd);
$_colwidth = implode($kDelim, $colwidth);
$_expof = implode($kDelim, $expof);
$_colpgbreak = implode($kDelim, $colpgbreak);
$_colfontname = implode($kDelim, $colfontname);
$_colfontcolor = implode($kDelim, $colfontcolor);
$_colfontsize = implode($kDelim, $colfontsize);
$_colfontitalic = implode($kDelim, $colfontitalic);
$_colfontbold = implode($kDelim, $colfontbold);

// end defaulting of column headers.
$target = (Profile::getUserCacheProperty('USERPREF', 'WINOPEN') || !Profile::hasUserCacheProperty('USERPREF', 'WINOPEN'))?$user_rec.$cny:'_new';

if ($_title == null || $_title=='' ) {
    $_title = $_name ;
    if (!$_r) {
        $_title2 = isl_htmlspecialchars($text->GT('IA.AS_OF').' [As_of_Date_in_Word]');
    }
}

$ok = true;

//Need to check top/bottom info to make sure the selected col is OK.
//part of ticket 16405.
//this can happen in a few ways where on the frontend we can't tell if a
//column is sutible for top/bottom;
if (showTopBottomSettingsOnCol()) {
    $topbotcolMems = explode($kDelim, $_topbotcol);
    foreach ($topbotcolMems as $key => $value) {
        if (!empty($value)) {
            if (!empty($compareby[$value -1]) && $compareby[$value -1] != 'T') {
                //oops, can't expand use this for top/bottom!
                //This will case the UI to force the user to manually
                //pick a valid column for top/bottom/
                $topbotcolMems[$key] = '';
            }
        }
    }
    $_topbotcol    = join($kDelim, $topbotcolMems);
}

$report = array();
if ($_save || $_saveas || $_savestay || $_viewt || $_viewh || $_viewp || $_viewe) {
    global $gErr;
    if (IsMCMESubscribed()) {
        $hasSystemGeneratedBudget = false;
        $hasConsolidatedBudget = false;
        $hasUserCreatedEntityBudget = false;

        $columnCount = sizeof($colValueType);

        $inClause = "";
        $count = 0;

        for ($i = 0; $i < $columnCount; $i++)
           {
            if (in_array($colValueType[$i], $budgetValueArray)) {
                if (empty($colBudValue[$i])) {
                    $inClause = '';
                    $count = 1;
                    break;
                }

                if ($count > 0) {
                    $inClause .= ",";
                }
                    $inClause .= $colBudValue[$i];
                    $count++;
            }
        }

        if ($inClause != '') {
            $budgetTypeQuery = "select systemgenerated, melockey, currency from budgetheader where cny#= :1 and record# in ($inClause)";
            $budgetTypeRS = QueryResult(array($budgetTypeQuery, $cny));
            foreach($budgetTypeRS as $budgetRow) {
                if ($budgetRow['SYSTEMGENERATED'] == 'T') {
                    $hasSystemGeneratedBudget = true;
                } else {
                    if (isset($budgetRow['MELOCKEY']) && $budgetRow['MELOCKEY'] != '') {
                        $hasUserCreatedEntityBudget = true;
                    }
                }
                if ($budgetRow['CURRENCY'] != '') {
                    $hasConsolidatedBudget = true;
                }
            }
        } else if ($count>0) {
            $gErr->addError(
                    "GL-3533", __FILE__ . ':' . __LINE__,
                    "Budget cannot be empty. Go to columns tab to verify your budget selection before save/view the report."
            );
            global $errorPages;
            if (!isset($errorPages)) {
                $errorPages = array();
            }
            $errorPages['5'] = $text->GT('IA.CHECK_BUDGET');
        }

        if (locationFilterNeeded()) {
            $gErr->addError("GL-3534", __FILE__ . ':' . __LINE__, "Please select a specific location on the filters tab.");
            global $errorPages;
            if (!isset($errorPages)) {
                $errorPages = array();
            }
            $errorPages['10'] = $text->GT('IA.SPECIFIC_LOCATION_NEEDED');
        }

        if (!GLBookManager::IsConsolidationBook($_reportingBook, false)) {
            if ($hasSystemGeneratedBudget || $hasConsolidatedBudget) {
                $gErr->addError(
                    "GL-3535", __FILE__ . ':' . __LINE__,
                    "Your budget may be out of sync with reporting book selection. 
                    Go to columns tab to verify your budget selection before save/view the report."
                );
                global $errorPages;
                if (!isset($errorPages)) {
                    $errorPages = array();
                }
                $errorPages['5'] = $text->GT('IA.CHECK_REPORTING_BOOK');
            }
        } elseif ($hasUserCreatedEntityBudget) {
            $gErr->addError(
                "GL-3535", __FILE__ . ':' . __LINE__,
                "Your budget may be out of sync with reporting book selection. 
                Go to columns tab to verify your budget selection before save/view the report."
            );
            global $errorPages;
            if (!isset($errorPages)) {
                $errorPages = array();
            }
            $errorPages['10'] = $text->GT('IA.CHECK_REPORTING_BOOK');
        } else if (false) {
            //$gErr->addError("GL-3536", __FILE__ . ':' . __LINE__, "Russ test.");
            // i18N (code change review)
            global $errorPages;
            if (!isset($errorPages)) {
                $errorPages = array();
            }
            $errorPages['10'] = 'Check if you are russ';
        }
    }

    $missingComputations = false;
    $noComputationsAvailable = $_totalcompcol == '' || (isset($_totalcompcol) && ($_totalcompcol == 0 || $_totalcompcol=='0'));
    $noNotationsAvailable = $_totalnotationcol == '' || (isset($_totalnotationcol) && ($_totalnotationcol == 0 || $_totalnotationcol=='0'));
    $columnCount = sizeof($colValueType);
    for ($i = 0; $i < $columnCount; $i++) {
        if (in_array(isl_trim($colValueType[$i]), array('CA', 'CB')) && ($colCompValue[$i] == '' || $noComputationsAvailable)) {
            $missingComputations = true;
            break;
        }
    }

    $missingAccountGroupsRowsTab = missingAccountGroupsRowsTab();
    $missingAccountGroupsColumnsTab = missingAccountGroupsColumnsTab();
    $missingTopBottomSetting = missingTopBottomSettings();
    $missingDimensionStructureOnColumnTab = missingDimensionStructureColumnsTab();
    $cannotHideTotal = !canHideTotal();
    $validAdjBookSelection = isValidAdjBookSelection();

    if (!$_name && !$_repname) {
        $gErr->addError("GL-2828", __FILE__ . ':' . __LINE__, "Report name cannot be empty.");
        $errorPages['1'] = 'Add report name';
    } elseif (!isl_trim($_title) ) {
        $gErr->addError("GL-2829", __FILE__ . ':' . __LINE__, "Report title cannot be empty.");
        $errorPages['1'] = 'Add report title';
        $_viewt=0; //If $_viewt or $_viewh
    } elseif (!isl_trim($_name) ) {
        $gErr->addError("GL-2828", __FILE__ . ':' . __LINE__, "Report name cannot be empty.");
        $errorPages['1'] = 'Add report name';
        $_viewt=0;
    } elseif ($missingComputations) {
        global $errorPages;
        if (!isset($errorPages)) {
            $errorPages = array();
        }
        $errorPages['4'] = $text->GT('IA.DEFINE_COMPUTATIONS');;
        $errorPages['5'] = $text->GT('IA.COMPUTATION_NOT_DEFINED');;

        $gErr->addError(
            "GL-3537", __FILE__ . ':' . __LINE__,
            "In the columns section, one or more columns has the type Computation on actual or Computation on budget, but a computation hasn't been defined. " . "Use the Computations tab to create a computation. Then, locate the column in the Columns tab and select the computation."
        );
        $_viewt=0;
    } elseif ($missingAccountGroupsColumnsTab) {
        global $errorPages;
        if (!isset($errorPages)) {
            $errorPages = array();
        }
        $errorPages['5'] = $text->GT('IA.VALUES_REQUIRED');

        $gErr->addError(
                "GL-3538", __FILE__ . ':' . __LINE__,
                "In the columns section, one or more columns is missing a required account group."
        );
        $_viewt=0;
    } elseif ($missingDimensionStructureOnColumnTab) {
        global $errorPages;
        if (!isset($errorPages)) {
            $errorPages = array();
        }
        $errorPages['5'] = $text->GT('IA.STRUCTURE_MISSING');

        $gErr->addError(
                "GL-3539", __FILE__ . ':' . __LINE__,
                "On the Columns tab, one or more columns is Expanded by a dimension structure but the dimension structure is not selected. Choose a value from the Dimension structure on the drop-down menu for the column, then try saving or running the report again."
        );
        $_viewt=0;
    } elseif ($missingAccountGroupsRowsTab) {
        global $errorPages;
        if (!isset($errorPages)) {
            $errorPages = array();
        }
        $errorPages['3'] = $text->GT('IA.VALUES_REQUIRED');

        $gErr->addError(
                "GL-3540", __FILE__ . ':' . __LINE__,
                "In the rows section, one or more row is missing a required account group."
        );
        $_viewt=0;
    } elseif ($missingTopBottomSetting) {
        global $errorPages;
        if (!isset($errorPages)) {
            $errorPages = array();
        }
        $errorPages['5'] = $text->GT('IA.SELECT_TOP_BOTTOM');

        $gErr->addError(
                "GL-3541", __FILE__ . ':' . __LINE__,
                "You currently have a section on the Rows tab that shows only the top or bottom values (such as the top 5 customers), but the column you selected can't be used to determine the top/bottom values. A top/bottom match can be based only on a Budget or Actual column that does not have show as set and is not expanded by time period, dimension, or account group. \n\nTo resolve this problem, you can either go to the Columns tab and use a different column for the top/bottom match. Or, you can go to the Rows tab and change the Detail Level to show all values."
        );
        $_viewt=0;
    } elseif ($cannotHideTotal) {
        global $errorPages;
        if (!isset($errorPages)) {
            $errorPages = array();
        }
        $errorPages['5'] = $text->GT('IA.CANNOT_HIDE_TOTAL');

        $gErr->addError(
                "GL-3542", __FILE__ . ':' . __LINE__,
                "You currently have a column on the Columns tab where the total is set to Hide and where expand by is set to Account group/Dimension structure but the total in this case can't be hidden. \n\nTo resolve this problem, change the total to display either on the right or left."
        );
        $_viewt=0;
    } elseif ($validAdjBookSelection == false) {
        global $errorPages;
        if (!isset($errorPages)) {
            $errorPages = array();
        }
        $errorPages['5'] = $text->GT("IA.INVALID_OTHER_BOOKS_SELECTION");

        $gErr->addError(
                "GL-3544", __FILE__ . ':' . __LINE__,
                "You currently have a column on the Columns tab where there is no 'Other books' selected but 'Combine reporting book' is unchecked . \n\nTo resolve this problem, try edit and save 'Other book' selection."
        );
        $_viewt=0;
    }  else if (!(HasErrors() && $gErr->ErrorCount)) {
        $initialNameForSaveAndStay = $_name;
        $report = GetReportFromParams();
        if ( $report == false ) {
            if (!($_save || $_saveas || $_savestay)) {
                if (HasErrors() && $gErr->ErrorCount) {
                    include 'popuperror.phtml';
                } else {
                    $msg = $text->GT('IA.COULD_NOT_GENERATE_REPORT_CHECK_YOUR_FILTERS');
                    UIUtils::ShowMessageStatic('Intacct -- Report', $msg, true);
                }
                exit();
            }

            if (HasErrors() && $gErr->ErrorCount) {
                for ($iii = 0; $iii < count($gErr->errors); $iii++) {
                    $theError = $gErr->errors[$iii]['CDESCRIPTION'];
                    $gErr->errors[$iii]['CDESCRIPTION'] = str_replace(array("<br/>", "<br>"), "\n", $theError);
                }
            } else {
                $msg = $text->GT('IA.COULD_NOT_GENERATE_REPORT_CHECK_YOUR_FILTERS');
                $gErr->addError("GL-3543", __FILE__ . ':' . __LINE__, $msg);
            }

        } else {
            // need to set category so we only see financial reports, since we're saving all kinds of reports now.
            $report['CATEGORY']='glfinancial';
        }

        // Validate the report for Forecast and Computation account group combination
        if ($_save || $_saveas || $_savestay) {
            validateReportForecast($report);
        }

        // All User Defined Layout settings will check here
        $validLayout = true;
        if ($_save || $_saveas || $_savestay || $_viewp) {
            if (!$_fromCSTool && ($report['CATEGORY'] == 'glfinancial') && ($report['format']['DEFINEDBY'] == 'U')) {
                $validlayout = CheckReportLayout($_userid, $report);
            }
        }

        if ($_save || $_savestay) {
            $report_warnings = GetReportWarnings($report);
            $doNotSave = !empty($report_warnings);
            //valid layout could have generated errors
            if (!(HasErrors() && $gErr->ErrorCount)) {
                if ($_r) {
                    !$doNotSave && SaveReport($_userid, $report, true);
                    if ($_savestay || ($_save && $doNotSave)) {
                        InitLineRowMaps($report);
                        $report['DEPT_IR'] = ($report['DEPT_IR'] == 'Y') ? 'true' : 'false';
                        $report['LOCATION_IR'] = ($report['LOCATION_IR'] == 'Y') ? 'true' : 'false';
                        $report['PROJECT_IR'] = ($report['PROJECT_IR'] == 'Y') ? 'true' : 'false';
                        SetParamsFromReport($report);
                    }
                } else {
                    $co = true;
                    if ( ! $doNotSave ) {
                        $co = CreateReport($_userid, $report);
                    }
                    if (!$co) {
                        $gErr->AddDBError("CreateReport", __FILE__.':'.__LINE__, $report);
                    } else {
                        InitLineRowMaps($report);
                        $report['DEPT_IR'] = ($report['DEPT_IR'] == 'Y') ? 'true' : 'false';
                        $report['LOCATION_IR'] = ($report['LOCATION_IR'] == 'Y') ? 'true' : 'false';
                        $report['PROJECT_IR'] = ($report['PROJECT_IR'] == 'Y') ? 'true' : 'false';
                        SetParamsFromReport($report);
                        //Need to create a new memcache here!
                        $_r = $report["RECORD#"];
                        if ($_r) {
                            $objStoreNameNew = "FinWizard_" . $_userid . "_" . $_r . $_repTimeStamp;
                            CloneStoreFields($objStoreName, $objStoreNameNew);
                        }
                    }
                }
            }
        } elseif ($_saveas) {
            if (!(HasErrors() && $gErr->ErrorCount)) {
                $report['NAME'] = $_repname;
                $co = CreateReport($_userid, $report);
                if (!$co) {
                    $gErr->AddDBError("CreateReport", __FILE__.':'.__LINE__, $report);
                }
                $report['NAME'] = $initialNameForSaveAndStay;
                $_repname = $initialNameForSaveAndStay;
                $_name = $initialNameForSaveAndStay;
            }
        } elseif ($_viewt || $_viewh || $_viewp || $_viewe) {

            // cool, it appears that all I need is in $report, so I'll just pass a single param into the new reporter class
            include_once 'GLFinancialReporter.cls';

            if ($_viewt) {
                $_type = 'txt';
            }
            if ($_viewh) {
                $_type = 'html';
            }
            if ($_viewp) {
                $_type = 'pdf';
            }
            if ($_viewe) {
                $_type = 'excel';
            }

            if ( $_type == 'html' ) {
                $thistype = kShowHTML;
            }
            if ( $_type == 'pdf' ) {
                $thistype = kShowPDF;
            }
            if ( $_type == 'txt' ) {
                $thistype = kShowText;
            }
            if ( $_type == 'excel' ) {
                $thistype = kShowExcel;
            }

            if (isset($LocGrpRec)) {
                $report['LOCATIONGRP#']=$LocGrpRec;
            } else if(isset($_location_grp)) {
                $report['LOCATIONGRP#']=$_location_grp;
            }

            $report['LOCATION_IR'] = $_ir_location == 'true' ? 'Y' : 'N';

            if (isset($DeptGrpRec)) {
                $report['DEPTGRP#']=$DeptGrpRec;
            } else if (isset($_dept_grp)) {
                $report['DEPTGRP#']=$_dept_grp;
            }

            $report['DEPT_IR'] = $_ir_dept == 'true' ? 'Y' : 'N';

            if (isset($ProjectGrpRec)) {
                $report['PROJECTGROUPKEY']=$ProjectGrpRec;
            } else if (isset($_project_grp)) {
                $report['PROJECTGROUPKEY']= $_project_grp;
            }

            $report['PROJECT_IR'] = $_ir_project == 'true' ? 'Y' : 'N';

            // instantiate
            $thisreport = new GLFinancialReporter(
                [
                    'reportparams'      => $report,
                    'userid'            => $_userid,
                    'type'              => $thistype,
                    'oldtype'           => $_type,
                    '2stage'            => 'true',
                    'title'             => $report['NAME'],
                    'reportingAccounts' => true,
                ]
            );
            // set the _report parameter, this tells the reporting class how to look for the names of the xsls
            $thisreport->_report = "gl_fin";

            //  Add audit trail record.
            AuditTrailSession::createAuditActionEvent('financialreport', $_r,
             AuditTrail::AUDITTRAIL_ACTION_PRINT_REPORT);

            $ok = $thisreport->ShowReport();

            if (!$ok) {
                if(HasErrors() && $gErr->ErrorCount) {
                    include 'popuperror.phtml';
                } else {
                    $msg = "Couldn't Generate Report. Check your filters!!!!";
                    UIUtils::ShowMessageStatic('Intacct -- Report', $msg, true);
                }
            }

            exit();
        }
    }
}

if ($_ret || $_save || $_saveas) {
    if($_save) {
        $popup = &Request::$r->_popup;
        if ( isset($popup) && $popup === "1" ) {
            $_appendnew = &Request::$r->_appendnew;

            if ($_appendnew) {

                // Append the new report back in the dropdown
                ?>
                <script>
                    window.opener.appendNameToList("<?echo "Financials -- " . $_name; ?>", "<?echo $_r; ?>"); window.close();
               </script>
                <?
            } else {
            $js .= 'window.close();';
                echo '<html><head><title>Done</title></head><body onload="' . $js . '"><A href="#" onclick="' . $js
                    . '">Close this Window</A></body></html>';
            }

            exit();
        }
    }
    if (isset($_POST['whatNextPostSaveAction']) && $_POST['whatNextPostSaveAction'] != "") {
        $doneurl = 'lister.phtml?.op=29&.ifmod=gl&.sess='.Session::getKey();
        $gotothisurl = $doneurl;
        if ( $_POST['whatNextPostSaveAction']== 'schedule') {
            if (!isset($_schopKey) && !isset($_POST['_schopKey']) ||  $_POST['_schopKey'] == '') {
                $the_opid = GetOperationId('co/lists/finrptschop/create');
            } else {
                $the_opid = GetOperationId('co/lists/finrptschop/edit');
                $schKey = '&.schopkey='.( $_schopKey ?? $_POST['_schopKey'] );
            };

            $gotothisurl = 'editor.phtml?.sess='.Session::getKey().'&.op='.$the_opid.$schKey.'&.memrec='.$_r.'&.done='.insertDoneUnEnc($doneurl);
        } else if ( $_POST['whatNextPostSaveAction']== 'reportgroup') {
            $the_opid =GetOperationId('co/lists/memrptgrp');
            $gotothisurl = 'lister.phtml?.sess='.Session::getKey().'&.op='.$the_opid.'&.done='.insertDoneUnEnc($doneurl);
        }
        /** @noinspection PhpUndefinedVariableInspection */
        LogToFile("FinancialReportWizard.phtml : done=$errText\n");
        Go($gotothisurl);
    } else {
        if(!empty($report_warnings)){
            // stay on the same page
        } else {
            $errText = RetUrl();
            LogToFile("FinancialReportWizard.phtml : done=$errText\n");
            Ret();
        }
    }
}// $_savestay

if (isset($_r) && !empty($_r)) {
    if (!(isset($_name) && !empty($_name))) { // want to refetch data if the SAVE_AND_STAY succeeds!
        $report = GetReport($_userid, $_r, $fromCSTool);
        InitLineRowMaps($report);
        $report['DEPT_IR'] = ($report['DEPT_IR'] == 'Y') ? 'true' : 'false';
        $report['LOCATION_IR'] = ($report['LOCATION_IR'] == 'Y') ? 'true' : 'false';
        $report['PROJECT_IR'] = ($report['PROJECT_IR'] == 'Y') ? 'true' : 'false';
        SetParamsFromReport($report);

            //need to reset $colValueType!
            $colValueType = explode($kDelim, $_colValueType);
    }
    if (!$fromCSTool && !$_savestay) {
        $auditTrailSession = AuditTrailSession::getInstance();
        try {
            $auditTrailSession->addAuditEvent('financialreport', $_r, AuditTrail::AUDITTRAIL_EVENT_ACCESS);
        } catch (IAException $exception) {
            LogToFile("Audit entry failed. " . $exception->getMessage() . ' ' . __FILE__.'.'.__LINE__);
        }
    }
} else {
    if ((!isset($_r) || empty($_r)) && ($ok && !HasErrors() && $_savestay) ) {
           $_industry = &Request::$r->_industry;
           $_r = getReportRecNum($_name, $_industry);
    }
    // Setting the report owner for the first time. Need not to call GetMyLogin() for all tabs.
    if(!isset($_rptowner) && $_rptowner=='') {
        $_rptowner = GetMyLogin();
    }

    if ($_pg+0 < MANDPAGES) {
        $page['hasdone'] = '0';
    }
}

    $gNoReportSave = '0';
if($_do !== 'create' && !empty($_r) && isset($_name) && IsMultiEntityCompany() && GetContextLocation() ) {
    $myreport = GetReport($_userid, $_r, $fromCSTool);
    if( $myreport['LOCATIONKEY'] != GetContextLocation() ) {
        $gNoReportSave = '1';
    }
}

$showBooks = checkMultiBook($companyBooks, $adjBooks);

// Check whether we are having multiple books. If yes, then get the book to be defaulted
if ($showBooks) {
    $_reportingBook = ( $_reportingBook ?: GetDefaultBook() );
}

// Display ownership structure dropdown only when consolidation subscription type is Tier consolidation AND
// list ownership structure permission is enabled.
$structureBookArray = $strctureRecArray = [];
$consolidationSubscriptionType = GetPreferenceForProperty(Globals::$g->kATLASid, 'SUBSCRIPTIONTYPE');
if ( $consolidationSubscriptionType === ATLASSetupManager::SUBSCRIPTION_TYPE_TIER_CONSOLIDATION
     && IsOperationAllowed(GetOperationId('atlas/lists/gcownershipstructure')) ) {
    $gcBookMgr = $gManagerFactory->getManager('gcbook');
    [$structureBookArray, $strctureRecArray] = $gcBookMgr->getCSNStructureMapForFinReport();
}

// RESIZE THE COLUMN-TAB ROW MATRIX BASED ON USER SELECTION IF ANY
$ok = manageRowsResize($report);

// RESIZE THE COMPUTATION-TAB ROW MATRIX BASED ON USER SELECTION IF ANY
if ($ok) {
    manageCompResize();
}


$prompt_for_ownerKeyChecked = isl_strstr($_runtimeparams, "owner") ?  "checked" : "";

// page 1
$kRounding = $text->getBulkTokens(array(
                         'C' => 'IA.NO_ROUNDING',
                         'D' => 'IA.IN_WHOLE_NUMBER',
                         'T' => 'IA.IN_THOUSANDS',
                         'M' => 'IA.IN_MILLIONS'
                     ));
$kStatus = $text->getBulkTokens(array(
    'active' => 'IA.ACTIVE',
    'inactive' => 'IA.INACTIVE',
));

$finDimInfo = GetCategorizedReportDimensions($fromCSTool);
$dimInfo = array_keys($finDimInfo);

if (HasErrors() && $gErr->ErrorCount ) {
    //$_location and $_dept can get whacked from GetCategorizedReportDimensions!!!!?
    resetDepartmentLocation();
}

if ($_pg == '1') {
    $hlpfile = 'Report_Wizard_Title_Tab';
}

// page 2
/*
$groups = array();
if (isset($_groups) && $_groups != '') {
$groups = explode($kDelim, $_groups);
}
*/
$groups = array();
if (isset($_groups) && $_groups != '') {
    $groups = explode($kDelim, $_groups);
}


// page 3
$prompt_for_acctgrp = isl_strstr($_runtimeparams, "acctgrp") ?  "checked" : "";
$prompt_for_dateChecked = isl_strstr($_runtimeparams, "asofdate") ?  "checked" : "";
$prompt_for_showpreviewdateChecked = isl_strstr($_runtimeparams, "showpreviewdate") ?  "checked" : "";
$prompt_for_reportbookChecked = isl_strstr($_runtimeparams, "repbook") ?  "checked" : "";

$krowASOF = array(
    'D'  => 'IA.DEFAULT_FROM_GROUP',
    'P' => 'IA.FOR_THE_PERIOD',
    'B' => 'IA.START_OF_PERIOD',
    'E' => 'IA.END_OF_PERIOD'
);

$krowASOF = $text->getBulkTokens($krowASOF);

$kColumnOperation = array(
'-- NO SELECT --' => 'IA.SELECT',
'A'        => 'IA.ADD',
'S'        => 'IA.SUBTRACT',
'SF'    => 'IA.SUBTRACT_FROM',
'M'        => 'IA.MULTIPLY',
'D'        => 'IA.DIVIDE',
'DI'    => 'IA.DIVIDE_INTO',
'-- NO SELECT 2--' => '_______________',
''    => 'IA.HIDE_REPORT_CELL',
);

$kColumnOperation = $text->getBulkTokens($kColumnOperation);

global $gAccountGroupMap;
$_cumbal = &Request::$r->_cumbal;
$groupnames = array();
$groupnames_errors = array();

$hasAccountComponents = false;
$hasDimensionComponents = false;
if (isset($_groups) && $_groups != '' ) {
    $paths = array();

    $acctArray  = Array( 'A', 'G','S','C','T','L');
    $dimArray = Array('LOC', 'DEP', 'VEN', 'CUS', 'PRJ', 'EMP', 'ITM', 'CLS', 'CON', 'TSK', 'WHS', 'CST','AFE',
        'LOCGD', 'DEPGD', 'VENGD', 'CUSGD', 'PRJGD', 'EMPGD', 'ITMGD', 'CLSGD', 'CONGD', 'TSKGD', 'WHSGD', 'CSTGD','AFEGD');

    foreach ( $groups as & $group ) {
        $gn = $gAccountGroupMap[$group]['NAME'];
        $mb = $gAccountGroupMap[$group]['MEMBERTYPE'];
        $hasAccountComponents |= in_array($mb, $acctArray);
        $hasDimensionComponents |= in_array($mb, $dimArray);

        if ($gn != null) {
            $groupnames[] = $gn;
            GetPaths($group, $paths, null, null);
        } else {
            $groupnames_errors[] = $curname;
            $group = null;
        }

    }
    unset($group);

    $_cumbal = is_array($_cumbal) ? $_cumbal : [];
    $_ratiobaseGrp = is_array($_ratiobaseGrp) ? $_ratiobaseGrp : [];

    foreach ($paths as $path) {
        $nodekeys = str_replace("/", "_", $path);
        $_ratiobaseGrp[$nodekeys] = ( $_ratiobaseGrp[$nodekeys] ?: '' );
        $_cumbal[$nodekeys] = ( $_cumbal[$nodekeys] ?: '' );
    }

}

$hasDimensionRowsExpandByAccount = false; //controls if the expandby is from the row or column
if (empty($_r) && empty($_accountOrDimensionRows)) {
    $_accountOrDimensionRows = 'account';
    $_dimensionRowsExpandByAccount = 'F';
} else if ($_dimensionRowsExpandByAccount == 'T') {
    $hasDimensionRowsExpandByAccount = true;
}

// page 4
$kCompMap = array();
$canShowComputations = $_totalcompcol > 0;
//$_totalcompcol = ($_totalcompcol)?$_totalcompcol:3;
$_totalcompcol = ($_totalcompcol)?:0;
$_compName = $_compName ?: [];
$_compDesciption = $_compDesciption ?: [];

$kNotationMap = array();
$_totalnotationcol = ($_totalnotationcol)?:0;
$canShowNotations = $_totalnotationcol > 0;
$_notationName = $_notationName ?: [];
$_notationDesciption = $_notationDesciption ?: [];

if($_isadd && $_currentcompcolindex != ''){
    $currentIndex = (int) $_currentcompcolindex - (int) Request::$r->_isabove;
    $_compName = insertValueAt($_compName, $currentIndex, ['']);
    $_compDesciption = insertValueAt($_compDesciption, $currentIndex, ['']);
}
for ($i=1; $i<=$_totalcompcol; $i++) {
    $compName = $_compName[$i-1];
    $kCompMap["$i"] = isset($compName) && ! empty($compName) ? $compName : 'Computation '.$i;
}

if($_isadd && $_currentnotationcolindex != '' && Request::$r->_opNotation == 'true'){
    $currentIndex = (int) $_currentnotationcolindex - (int) Request::$r->_isabove;
    $_notationName = insertValueAt($_notationName, $currentIndex, ['']);
    $_notationDesciption = insertValueAt($_notationDesciption, $currentIndex, ['']);
    MigrateNotations($currentIndex + 1);
}
if($_isremove && $_currentnotationcolindex != '' && Request::$r->_opNotation == 'true'){
    $currentIndex = (int) $_currentnotationcolindex;
    unset($_notationName[$currentIndex]);
    $_notationName = array_values($_notationName);
    unset($_notationDesciption[$currentIndex]);
    $_notationDesciption = array_values($_notationDesciption);
    MigrateNotations($currentIndex);
}
for ($i=0; $i<$_totalnotationcol; $i++) {
    $notationName = $_notationName[$i];
    $kNotationMap["$i"] = isset($notationName) && ! empty($notationName) ? $notationName : 'Notation '. ($i + 1);
}

// page 3/5

if (($_accountOrDimensionRows === 'dimension' && $_dimensionRowsExpandByAccount !=='T')
    || ($_accountOrDimensionRows !== 'dimension' && $_dimensionRowsExpandByAccount !=='T' && !$fromCSTool)
) {
    //on the columns tab we will show the accountgrp/dinensiongrp row.
} else {
    //should not be set in this case! Fuxup incase ut was set from toggling the report type on page one
    //around, after the cols tab was set (user would have had to delete the rows first.)
    if ( isset($_colAccountGrp) && $_colAccountGrp != '' ) {
        if (empty($colAccountGrp)) {
            $colAccountGrp    = explode($kDelim, $_colAccountGrp);
        }
        foreach ($colAccountGrp as $key => $value) {
            $colAccountGrp[$key] = '';
        }
        $_colAccountGrp = join($kDelim, $colAccountGrp);
    }

}

$kShowDetail =  array(
GRPCHILDREN => 'IA.DETAILS',
GRPSUMMARY => 'IA.SUMMARY'
);

if (($_pg == "5" && !$hasDimensionRowsExpandByAccount && $_accountOrDimensionRows == 'dimension')
    || ($_pg == "3" && $hasDimensionRowsExpandByAccount && $_accountOrDimensionRows == 'dimension')
) {

    $kShowDetail = array_merge(
        $kShowDetail, array(
        '0a' => "-- " . $text->GT("IA.ACCOUNT_GROUP") . " --",
        GRPACCT =>          "IA.ACCOUNT_GROUP_ONE_LEVEL_DOWN",
        GRPACCTALL =>       "IA.ACCOUNT_GROUP_ALL_LEVELS",
        GRPACCTLEAF =>      "IA.ACCOUNTS_LEAF_AMOUNTS",
        )
    );
}

if (($_pg == "3" && $_dimensionRowsExpandByAccount === 'T' && $_accountOrDimensionRows === 'account')) {

    $kShowDetail = array_merge(
        $kShowDetail, array(
        '0a' => "-- " . $text->GT("IA.DIMENSION_STRUCTURE") . " --",
        GRPACCT =>          "IA.DIMENSION_STRUCTURE_ONE_LEVEL_DOWN",
        GRPACCTALL =>       "IA.DIMENSION_STRUCTURE_ALL_LEVELS",
        GRPACCTLEAF =>      "IA.DIMENSIONS_LEAF_AMOUNTS",
        )
    );
}

if ( IsMultiEntityCompany() && isset($_ownerKey) && $_ownerKey != '' ) {
    $kShowDetail = array_merge(
        $kShowDetail, array(
        '1a' => "-- " . $text->GT("IA.DEPARTMENTS") . " --",
        GRPDEPTP => "IA.DEPARTMENTS_ONE_LEVEL_DOWN_WITH_ROLL_UP",
        GRPALLDEPTP => "IA.DEPARTMENTS_ALL_LEVELS_WITH_ROLL_UP",
        GRPALLDEPTMYBAL => "IA.DEPARTMENTS_ALL_LEVELS_WITH_INDIVIDUAL_BALANCE",
        GRPDEPTNOP => "IA.DEPARTMENTS_LEAF_AMOUNTS_ONE_LEVEL_DOWN",
        GRPALLDEPTNOP => "IA.DEPARTMENTS_LEAF_AMOUNTS_ALL_LEVELS",
        '2a' => "-- " . $text->GT("IA.LOCATIONS") . " --",
        GRPLOC => 'IA.ENTITY_LEVEL',
        )
    );
} else {
    $kShowDetail = array_merge(
        $kShowDetail, array(
        '1a' => "-- " . $text->GT("IA.DEPARTMENTS") . " --",
        GRPDEPTP => "IA.DEPARTMENTS_ONE_LEVEL_DOWN_WITH_ROLL_UP",
        GRPALLDEPTP => "IA.DEPARTMENTS_ALL_LEVELS_WITH_ROLL_UP",
        GRPALLDEPTMYBAL => "IA.DEPARTMENTS_ALL_LEVELS_WITH_INDIVIDUAL_BALANCE",
        GRPDEPTNOP => "IA.DEPARTMENTS_LEAF_AMOUNTS_ONE_LEVEL_DOWN",
        GRPALLDEPTNOP => "IA.DEPARTMENTS_LEAF_AMOUNTS_ALL_LEVELS",
        '2a' => "-- " . $text->GT("IA.LOCATIONS") . " --",
        GRPLOCP => "IA.LOCATIONS_ONE_LEVEL_DOWN_WITH_ROLL_UP",
        GRPALLLOCP => "IA.LOCATIONS_ALL_LEVELS_WITH_ROLL_UP",
        GRPALLLOCMYBAL => "IA.LOCATIONS_ALL_LEVELS_WITH_INDIVIDUAL_BALANCE",
        GRPLOCNOP => "IA.LOCATIONS_LEAF_AMOUNTS_ONE_LEVEL_DOWN",
        GRPALLLOCNOP => "IA.LOCATIONS_LEAF_AMOUNTS_ALL_LEVELS",
        )
    );
}


if ($_accountOrDimensionRows == 'dimension') {
    $kcolASOF = array(
            'D' => 'IA.DEFAULT',
            'P' => 'IA.FOR_THE_PERIOD',
            'B' => 'IA.START_OF_PERIOD',
            'E' => 'IA.END_OF_PERIOD'
        );
} else {
    $kcolASOF = array(
        'D' => 'IA.DEFAULT',
        'R' => 'IA.APPLY_ROWS_TAB_SETTINGS',
        'P' => 'IA.FOR_THE_PERIOD',
        'B' => 'IA.START_OF_PERIOD',
        'E' => 'IA.END_OF_PERIOD'
    );
}

$kCompareBy = array(
    GRPSUMMARY => 'IA.DO_NOT_EXPAND',
);
if (($_pg == "5" && !$hasDimensionRowsExpandByAccount)
    || ($_pg == "3" && $hasDimensionRowsExpandByAccount)
) {
    if ($_accountOrDimensionRows == 'dimension') {
        $kCompareBy = array_merge(
            $kCompareBy, array(
            '0a' => "-- " . $text->GT("IA.ACCOUNT_GROUP") . " --",
            GRPACCT =>      "IA.ACCOUNT_GROUP_ONE_LEVEL_DOWN",
            GRPACCTALL =>   "IA.ACCOUNT_GROUP_ALL_LEVELS",
            )
        );
    } else {
        if ($_pg == "3" || ($_pg == "5" && $_dimensionRowsExpandByAccount !== 'T' && $_accountOrDimensionRows == 'account')) {
            $kCompareBy = array_merge(
                $kCompareBy, array(
                '0a' => "-- " . $text->GT("IA.DIMENSION_STRUCTURE") . " --",
                GRPACCT =>      "IA.DIMENSION_STRUCTURE_ONE_LEVEL_DOWN",
                GRPACCTALL =>   "IA.DIMENSION_STRUCTURE_ALL_LEVELS",
                )
            );
        }
    }
}

if ( IsMultiEntityCompany() && isset($_ownerKey) && $_ownerKey != '' ) {
    $kCompareBy = array_merge(
        $kCompareBy,  array(
        '1a' => "-- " . $text->GT("IA.DEPARTMENTS") . " --",
        GRPDEPTP => "IA.DEPARTMENTS_ONE_LEVEL_DOWN_WITH_ROLL_UP",
        GRPDEPT => "IA.DEPARTMENTS_ONE_LEVEL_DOWN_WITH_SUMMARY_BALANCE",
        GRPALLDEPTP => "IA.DEPARTMENTS_ALL_LEVELS_WITH_ROLL_UP",
        GRPALLDEPTMYBAL => "IA.DEPARTMENTS_ALL_LEVELS_WITH_INDIVIDUAL_BALANCE",
        GRPALLDEPT => "IA.DEPARTMENTS_ALL_LEVELS_WITH_SUMMARY_BALANCE",
        '2a' => "-- " . $text->GT("IA.LOCATIONS") . " --",
        GRPLOC => 'IA.ENTITY_LEVEL',
        )
    );
} else {
    $kCompareBy = array_merge(
        $kCompareBy, array(
        '1a' => "-- " . $text->GT("IA.DEPARTMENTS") . " --",
        GRPDEPTP => "IA.DEPARTMENTS_ONE_LEVEL_DOWN_WITH_ROLL_UP",
        GRPDEPT => "IA.DEPARTMENTS_ONE_LEVEL_DOWN_WITH_SUMMARY_BALANCE",
        GRPALLDEPTP => "IA.DEPARTMENTS_ALL_LEVELS_WITH_ROLL_UP",
        GRPALLDEPTMYBAL => "IA.DEPARTMENTS_ALL_LEVELS_WITH_INDIVIDUAL_BALANCE",
        GRPALLDEPT => "IA.DEPARTMENTS_ALL_LEVELS_WITH_SUMMARY_BALANCE",
        '2a' => "-- " . $text->GT("IA.LOCATIONS") . " --",
        GRPLOCP => "IA.LOCATIONS_ONE_LEVEL_DOWN_WITH_ROLL_UP",
        GRPLOC => "IA.LOCATIONS_ONE_LEVEL_DOWN_WITH_SUMMARY_BALANCE",
        GRPALLLOCP => "IA.LOCATIONS_ALL_LEVELS_WITH_ROLL_UP",
        GRPALLLOCMYBAL => "IA.LOCATIONS_ALL_LEVELS_WITH_INDIVIDUAL_BALANCE",
        GRPALLLOC => "IA.LOCATIONS_ALL_LEVELS_WITH_SUMMARY_BALANCE",
        )
    );
}

// 1 && 2 for department and location respectively
// build array for compare by
$kc = 3;
foreach($finDimInfo as $key => $val) {
    // The Cost type dimension is currently not allowed for "Expand by" in Finance Report Writer.
    if ( in_array($key, array('department', 'location', 'costtype')) ) {
        continue;
    }
    $dimcompareby = $val['compareby'];
    $dimcomparebyP = $val['comparebyP'];
    $dimcomparebyAll = $val['comparebyAll'];
    $dimcomparebyAllP = $val['comparebyAllP'];
    $dimcomparebyMyBal = $val['comparebyAllMyBal'];
    $dimcomparebyNoP = $val['comparebyNoP'];
    $dimcomparebyAllNoP = $val['comparebyAllNoP'];

    // getting renamed text
    $entity = $val['pt_entity'] ?: $val['entity'];
    $dimMgr = $gManagerFactory->getManager($entity);
    $fname = $dimMgr->GetPluralPrintAs();
    // $fname = $dict->GetRenamedText($fname);

    if ( $val['standard'] !== false ) {
        $kCompareBy["$kc"."a"] = "-- " . $text->GT($fname) . " --";
        $kCompareBy[$dimcomparebyP] = "IA.". strtoupper($entity)."S_ONE_LEVEL_DOWN_WITH_ROLL_UP";
        $kCompareBy[$dimcompareby] = "IA.". strtoupper($entity)."S_ONE_LEVEL_DOWN_WITH_SUMMARY_BALANCE";
        $kCompareBy[$dimcomparebyAllP] = "IA.". strtoupper($entity)."S_ALL_LEVELS_WITH_ROLL_UP";
        $kCompareBy[$dimcomparebyMyBal] = "IA.". strtoupper($entity)."S_ALL_LEVELS_WITH_INDIVIDUAL_BALANCE";
        $kCompareBy[$dimcomparebyAll] = "IA.". strtoupper($entity)."S_ALL_LEVELS_WITH_SUMMARY_BALANCE";

        $kShowDetail["$kc"."a"] = "-- " . $text->GT($fname) . " --";
        $kShowDetail[$dimcomparebyP] = $kCompareBy[$dimcomparebyP];
        $kShowDetail[$dimcomparebyAllP] = $kCompareBy[$dimcomparebyAllP];
        $kShowDetail[$dimcomparebyMyBal] = $kCompareBy[$dimcomparebyMyBal];
        $kShowDetail[$dimcomparebyNoP] = "IA.". strtoupper($entity)."S_LEAF_AMOUNTS_ONE_LEVEL_DOWN";
        $kShowDetail[$dimcomparebyAllNoP] = "IA.". strtoupper($entity)."S_LEAF_AMOUNTS_ALL_LEVELS";
    } else {
        $kCompareBy["$kc"."a"] = "-- " . $text->GT($fname) . " --";
        $kCompareBy[$dimcomparebyAllP] = $text->GT('IA.UDD_NAME_ALL_LEVELS_WITH_SUMMARY_BALANCE', ['UDD_NAME' => $fname]);

        $kShowDetail["$kc"."a"] = "-- " . $text->GT($fname) . " --";
        $kShowDetail[$dimcomparebyAllP] = $kCompareBy[$dimcomparebyAllP];
    }

    $kc++;
}

$kSummaryOp = [
    ''    => 'IA.SELECT',
    'Avg' => 'IA.AVERAGE',
    'Min' => 'IA.MINIMUM',
    'Max' => 'IA.MAXIMUM',
    'Med' => 'IA.MEDIAN',
    'Add' => 'IA.ADD',
    'Sub' => 'IA.SUBTRACT',
    'Mul' => 'IA.MULTIPLY',
    'Div' => 'IA.DIVIDE',
];

$mapDimensionToDimensionClassID = getDimensionToDimensionClassIDMap();
$kMeasures = array();
    $kMeasures['-- NO SELECT'] = 'IA.CHOOSE_COLUMN_TYPE'; //All 'a' will be mapped to 'x'
if ($_accountOrDimensionRows === 'account') {
    $kMeasures['-- NO SELECT 1--'] = 'IA.ACCOUNT';
    $kMeasures['DSC'] = 'IA.ACCOUNT_NAME';
    $kMeasures['AN'] = 'IA.ACCOUNT_NUMBER';
    $kMeasures['DSCN'] = 'IA.ACCOUNT_NUMBER_AND_NAME';
    $kMeasures['-- NO SELECT 1.1'] = 'IA.ACCOUNT_ATTRIBUTE';
    $kMeasures['ATR<-->account'] = 'IA.ACCOUNT';
    $kMeasures['ATR<-->statisticalaccount'] = 'IA.STATISTICAL_ACCOUNT';
}

if ($_accountOrDimensionRows === 'dimension') {
    $kMeasures['-- NO SELECT 1--'] = 'IA.DIMENSION';
    $kMeasures['DSC'] = 'IA.DIMENSION_NAME';
    $kMeasures['AN'] = 'IA.DIMENSION_ID';
    $kMeasures['DSCN'] = 'IA.DIMENSION_ID_AND_NAME';
    if (!empty($mapDimensionToDimensionClassID)) {
        $kMeasures['-- NO SELECT 1.1'] = 'IA.DIMENSION_ATTRIBUTE';
        foreach( $mapDimensionToDimensionClassID as $k => $v ) {
            $kMeasures['ATR<-->'.$v['dim_name']] = isl_htmlspecialchars($v['displayAsPick']);
        }
    }
}


$kMeasures['-- NO SELECT 2'] = 'IA.ACTUAL';
$kMeasures['x'] = 'IA.ACTUAL';

$kMeasures['-- NO SELECT 3'] = 'IA.COMPUTATION';
if ($_accountOrDimensionRows === 'account') {
    $kMeasures['CA'] = 'IA.COMPUTATION_ON_ACTUAL';
    $kMeasures['CB'] = 'IA.COMPUTATION_ON_BUDGET';
}
$kMeasures['S'] = 'IA.SUMMARY_ON_COLUMNS';

$kMeasures['-- NO SELECT 3.1'] = 'IA.PERIOD_COMPARISON';
$kMeasures['d'] = 'IA.PERIOD_VARIANCE';
$kMeasures['nd'] = 'IA.PERIOD_VARIANCE_NORMALIZED';
$kMeasures['D'] = 'IA.PERIOD_DIFFERENCE';
$kMeasures['nD'] = 'IA.PERIOD_DIFFERENCE_NORMALIZED';

$kMeasures['-- NO SELECT 4'] = 'IA.BUDGET';
$kMeasures['b'] = 'IA.BUDGET';
$kMeasures['v'] = 'IA.BUDGET_VARIANCE';
$kMeasures['V'] = 'IA.ACTUAL_BUDGET_DIFFERENCE';
$kMeasures['rV'] = 'IA.BUDGET_ACTUAL_DIFFERENCE';
$kMeasures['nv'] = 'IA.BUDGET_VARIANCE_NORMALIZED';
$kMeasures['nV'] = 'IA.ACTUAL_BUDGET_DIFFERENCE_NORMALIZED';
$kMeasures['nrV'] = 'IA.BUDGET_ACTUAL_DIFFERENCE_NORMALIZED';
$kMeasures['P'] = 'IA.BUDGET_RATIO';

$kMeasures['-- NO SELECT 5'] = 'IA.BUDGET_FORECAST';
$kMeasures['RB'] = 'IA.REMAINING_BUDGET';
$kMeasures['F'] = 'IA.FORECAST_PRORATED';
$kMeasures['FF'] = 'IA.FORECAST_FULL_PERIOD';


if ($_accountOrDimensionRows !== 'dimension') {
    $kMeasures['-- NO SELECT 6'] = 'IA.PERCENTAGE_OF_AMOUNT';
    $kMeasures['p'] = 'IA.PERCENT_ON_ACTUAL';
    $kMeasures['pb'] = 'IA.PERCENT_ON_BUDGET';
}

$kMeasures['-- NO SELECT 7'] = 'IA.VERTICAL_DIVIDER';
$kMeasures['VDS'] = 'IA.SPACER';

$kMeasures['-- NO SELECT 8'] = 'IA.NOTATIONS';
$kMeasures['NOT'] = 'IA.NOTATION';


$kColShowAs = array(
    '-- NO SELECT'        => 'IA.SELECT',
    'NC'    => 'IA.VALUE',
    'VC'    => 'IA.VALUES_AS_CURRENCY',
    'R'        => 'IA.RATIO_WITH_DECIMALS',
    'F'        => 'IA.RATIO_WITHOUT_DECIMALS',
    'P'        => 'IA.PERCENT',
    'D'        => 'IA.DAILY_AVERAGE',
    'W'        => 'IA.WEEKLY_AVERAGE',
    'M'        => 'IA.MONTHLY_AVERAGE',
    'Q'        => 'IA.QUARTERLY_AVERAGE',
    'YD'    => 'IA.ANNUALIZE_BY_DAYS',
);
//Annualize by Months is not applicable for Non-standard Company
if ($gNonStandardPeriods != 1) {
        $kColShowAs['YM']    = 'IA.ANNUALIZE_BY_MONTHS';
}

$kColShowAs = $text->getBulkTokens($kColShowAs);
$kSummaryOp = $text->getBulkTokens($kSummaryOp);
$kcolASOF = $text->getBulkTokens($kcolASOF);
$kShowDetail = $text->getBulkTokens($kShowDetail);
$kCompareBy = $text->getBulkTokens($kCompareBy);
$kMeasures = $text->getBulkTokens($kMeasures);

$kPrecisions = array( '0' => '0', 1 => '1', 2 => '2', 3 => '3', 4 => '4', 5 => '5', 6 => '6', 7 => '7', 8 => '8', 9 => '9' );

$_enablelayouts = 'T';
$_definedby = $_definedby ?: 'S';

// ASSIGN HELP IDS
switch ( $_pg ) {
case '1' :
    $hlpfile = 'Report_New_Wizard_Title_Tab';
    $focus = $_r ? '.title' : '.name';
    break;
case '3' :
    $hlpfile = 'Report_New_Wizard_Rows_Tab';
    break;
case '4' :
    $hlpfile = 'Report_Wizard_Computations_Tab';
    break;
case '5' :
    $hlpfile = 'Report_Wizard_Columns_Tab';
    break;
case '10' :
    $hlpfile = 'Report_Wizard_SliceAadDice_Tab';
    break;
case '7' :
    $hlpfile = 'ReportWizard_Formatting_Tab';
    break;
}

$noattachEvent = (in_array($_pg, array('3','4','5'))?true:false);
$nocheck = false;

if ($_pg == '5') {
    // Fix for DE18605 (we have to roll back the jquery.dataTables.js update and use the 1.9.2 version & its extensions)
    $dataTables_js = '<script src="../resources/thirdparty/jquery-datatables/DataTables-1.9.2/jquery.dataTables.js"></script>'.
                     '<script src="../resources/thirdparty/jquery-datatables/DataTables-1.9.2/extensions/fixedColumns/js/FixedColumns.js"></script>';
}
if ($_pg == '3' || $_pg == '5') {
    $multiselect_js = '<script src="../resources/thirdparty/jquery-ui-multiselect/js/ui.multiselect.js"></script>';
}

//  Set onloadjs with space to trigger call to baseOnLoad.
$uaInfo = UAInfo();
global $IE_8_HACKATHON;
$IE_8_HACKATHON = false;
$IE_8_CSS_KLUDGES = '';
if ($uaInfo['browser'] == 'IE' && $uaInfo['version.major'] == 8) {
    $IE_8_HACKATHON = true;
    $IE_8_CSS_KLUDGES = '<link href="../resources/css/financialreportwizard_ie8.css" rel="stylesheet" type="text/css"/>';
};
//because there is a forceLayoutType, we have to check for Q from preferences
$QUIXOTE_FRW_CSS = '';
$QUIXOTE_FRW_JS = '';
if (QXCommon::getLayoutType() === 'Q') {
    $QUIXOTE_FRW_CSS = getLiveOrDebugStylusTag('../resources/minus/qxfinancialrw.min.css', '../resources/qx/stylus/qxfinancialrw.min.css');
    $QUIXOTE_FRW_CSS .= getLiveOrDebugStylusTag('../resources/qx/iafonts/iafonts.css', '../resources/qx/iafonts/iafonts.css');
    $QUIXOTE_FRW_JS = '<script src="../resources/qx/js/qxcommon.js"></script>';
}

if (!($_viewt || $_viewh || $_viewp || $_viewe)) {
    if (HasErrors() && $gErr->ErrorCount) {
        $errco = $gErr->ErrorCount;
        $myerrs = [];
        for($i=0; $i<$errco; $i++) {
            $myerrs[$i] = $gErr->GetNextError();
            if ($myerrs[$i]['NUMBER'] === '**********') {
                //hack-a-roo
                $myerrs[$i]['DESCRIPTION'] = 'Problem saving report';
            };
        };

        $noNonDBCorrections = true;
        $hasDBError = false;
        for($i=$errco-1; $i>=0; $i--) {
            /*** @var IAError $err */
            $err = $myerrs[$i][IAIssueHandler::KEY_I18N_MESSAGES];
            if (isl_strpos($err->getLegacyErrorNo(), "DL")===0) { // id a db error
                $hasDBError = true;
            } else {
                if ( !empty($err->getCorrectionLogString()) ) {
                    $noNonDBCorrections = false;
                };
            };
        }

        $moreThanOneError = false;
        $moreThanOneCt = 0;
        for($i=$errco-1; $i>=0; $i--) {
            /*** @var IAError $err */
            $err = $myerrs[$i][IAIssueHandler::KEY_I18N_MESSAGES];
            $printthiserror = ($_dbg!=0);
            //ticket: 16420; If we have all DB errors then show them!
            if (!(isl_strpos($err->getLegacyErrorNo(), "DL")===0)) { // not a db error:
                if ( !empty($err->getCorrectionLogString()) ) {
                    if ($moreThanOneCt > 0) {
                        $moreThanOneError = true;
                        break;
                    } else {
                        $moreThanOneCt ++;
                        continue;
                    }
                };
            };
        };

        $errorTitle = '';
        $correctionString = '';
        for($i=$errco-1; $i>=0; $i--){
            /*** @var IAError $err */
            $err = $myerrs[$i][IAIssueHandler::KEY_I18N_MESSAGES];
            $printthiserror = ($_dbg!=0);
            if (!(isl_strpos($err->getLegacyErrorNo(), "DL")===0)) { // not a db error
                if (!empty($err->getCorrectionLogString())) {
                    $printthiserror=1;
                } else if ($noNonDBCorrections && !$hasDBError) {
                    $printthiserror=1;
                    $correctionString = $text->GT('IA.PLEASE_TRY_THE_OPERATION_AGAIN_CONTACT_SUPPORT');
                };
            } else if ($noNonDBCorrections) {
                $printthiserror=1;
                $correctionString = $text->GT('IA.PLEASE_TRY_THE_OPERATION_AGAIN_CONTACT_SUPPORT');
            };
            if($printthiserror) {
                if (startsWith($err->getCorrectionLogString(), 'Change the report name')) {
                    global $errorPages;
                    if (!isset($errorPages)) {
                        $errorPages = array();
                    }
                    $errorPages['1'] = $text->GT('IA.CHANGE_REPORT_NAME');
                };
                /** @noinspection PhpUndefinedVariableInspection */
                $firstError = ( $loadErrorMsg == "");
                if ($firstError) {
                    $errorTitle = isl_htmlspecialchars($err->getDescription1String());
                }
                $loadErrorMsg = ($firstError) ? "" : $loadErrorMsg."<hr style='margin: 5px'>";
                if ($firstError) {
                    $loadErrorMsg = $loadErrorMsg."<p style='font-size: 12px; font-weight: 700; padding-bottom:12px;'><img style='padding-right: 5px;vertical-align: bottom' src='../resources/images/ia-app/icons/warning.png'>" . $text->GT('IA.THE_REPORT_WAS_NOT_SAVED');
                }
                $correctionString = !empty($correctionString) ? $correctionString : $err->getCorrectionString();
                $loadErrorMsg = $loadErrorMsg.'<p>'.
                    (($firstError && !$moreThanOneError) ? "" : "<B>".$err->getDescription1String()."</B><br><br>"). // first error is in the errorTitle
                    //isl_htmlspecialchars($err['CDESCRIPTION']).
                    str_replace(array("\r\n","\r","\n"), "<br/>", isl_htmlspecialchars($err->getCDescriptionMessageText(IAMessageFormatStyles::LEGACY_WITH_ID))).
                    ((!empty($correctionString))? "<br><br>".isl_htmlspecialchars($correctionString) : "");
            };
        };
        $gErr->ErrorCount = 0;
        $gErr->errorMsgs = array();
    };
} else {
    //PrintCommonHtmlHeader will return an error page!
}
PrintCommonHtmlHeader(
    array('title' => $text->GT('IA.FINANCIAL_REPORT_WRITER'), 'nocheck' => $nocheck, 'curpos'=> $focus,
        'nobeancss' => true, 'nojs'=> true, 'formname' => 'mf', 'nohotkey'=>true,
        'noattachevents'=>$noattachEvent, 'onloadjs'=>' ', 'html5'=>true,
        'isFinancialRW' => true,
        'customCSS2' => <<<EOT
	        <link href="../resources/css/iamm.css" rel="stylesheet" type="text/css"/> 
	        <link href="../resources/css/sprites.css" rel="stylesheet" type="text/css"/> 
	        
	        <link href="../resources/thirdparty/jquery-ui-multiselect/css/ui.multiselect.css"  rel="stylesheet" type="text/css"/>
	        <link href="../resources/css/listselect.css"  rel="stylesheet" type="text/css"/>
	        <link href="../resources/css/ComboBox2.css" rel="stylesheet" type="text/css"/>
	        	        
	        <link href="../resources/css/dialog2.css" rel="stylesheet" type="text/css"/>
	        <link href="../resources/css/fieldhelpmodal.css" rel="stylesheet" type="text/css"/>
	        <link href="../resources/css/financialreportwizard.css" rel="stylesheet" type="text/css"/>
	        $IE_8_CSS_KLUDGES
	        $QUIXOTE_FRW_CSS
EOT
        ,
        'textMap' => json_encode($text->getTextMapForClient()),
        'includeCustomJS' => <<< EOT
	        <script src="../resources/js/editor.js"></script>
	        <script src="../resources/js/ComboBox2.js"></script>
	        <script src="../resources/js/iamm.js"></script>
            <script src="../resources/js/dialog.js"></script>
            <script src="../resources/js/fieldhelpmodal.js"></script>
	        <script src="../resources/js/base_lib.js"></script>
	        <script src="../resources/js/attachEvents.js"></script>
            <script src="../resources/js/listbox_new.js"></script>
	        <script src="../resources/thirdparty/jquery/hoverIntent.js"></script>
	        $dataTables_js
	        $multiselect_js
	        $QUIXOTE_FRW_JS
EOT
    )
);

$pageMap = array (
    '1'  => 'Title',
    '3'  => (($_accountOrDimensionRows === 'account') ? 'Rows_AccountsOnRows':'Rows_DimensionsOnRows'),
    '4'  => 'Computations',
    '5'  => 'Columns',
    '10' => 'Filters',
    '7'  => 'Format', //for other stuff
    '12'  => 'Format', // for rows
    '11'  => 'Permissions', // share this w/Page1 because page was moved from there.
    '8'  => 'NextSteps',
    '13' => 'Notations',
);

$frwHelpContent = getFRWPageHelpContent($pageMap[$_pg]);

echo $frwHelpContent;

$acctgrpOp = GetOperationId('gl/lists/glacctgrp');
require_once 'js_auto_dates.inc';
showJsAutoDates();

if (IALayoutManager::prefersCSS()) {
    $coloredBG =  'class="list_column_headers"';
    $coloredBG2 =  'class="list_column_headers"';
    $coloredBG_NC =  ' list_column_headers ';

} else {
    $coloredBG =  'style="background: #DDD7C1"';
    $coloredBG2 =  'style="background: #E9E8DF"';
}
$coloredWhite = 'style="background: #FFF"';

if ($_REQUEST['savedFromNewEditorWarning'] && $_REQUEST['_pg'] === null && $_pg=='1' && $_REQUEST['_do'] == 'edit' && !($_save || $_saveas || $_savestay || $_viewt || $_viewh || $_viewp || $_viewe )) {
    $_REQUEST['savedFromNewEditorWarning'] = false;
    $errorTitle = 'Using the new Financial Report Writer';
    $loadErrorMsg = 'This report was previously saved with the new Financial Report Writer. We we&apos;ll automatically open this report for you in the new report writer so you can edit it.';
    //$loadErrorMsg .= "<br><br>To switch to the new Financial Report Writer for all reports, and avoid seeing this message:<br><table style='margin-top:10px;margin-left:20px;'><tr><td style='vertical-align: baseline;'><P style='color:#333333'>&bull;&nbsp;</td><td><P style='color:#333333'>Go to the Company menu and click My Preferences.<td></tr><tr><td style='vertical-align: baseline;'><P style='color:#333333'>&bull;&nbsp;</td><td><P style='color:#333333'>In the Display section, select the option to &quot;Use the new Financial Report Writer.&quot;<td></tr></table>";
}

$restoredFrom = Request::$r->_restoredFrom;
$showingErrorModal = false;
if ( (! empty($restoredFrom) || $_savestay) && !isset($loadErrorMsg) ) {
    if( ! empty($restoredFrom)){
        $dialogText = $text->GT('IA.VERSION_RESTORED_FROM_VERSION_AS_NEW_CURRENT_VERSION', ['RESTORED_FROM' => $restoredFrom]);
    }

    if ( ! empty($dialogText) ) {
        $showingErrorModal = true;
    ?>
    <script>
        var overallHeight;
        if (document.body.clientHeight) {
            overallHeight = document.body.clientHeight;
        } else if (window.innerHeight) {
            overallHeight = window.innerHeight;
        } else if (document.documentElement && document.documentElement.clientHeight) {
            overallHeight = document.documentElement.clientHeight
        } else {
            overallHeight = document.body.offsetHeight;
        }
        jq()
            .ready(function() {
                var dialogText = "<?=$dialogText;?>";
                var dialogTitle = "<?=$text->GT('IA.ALERT')?>";
                $GLOBAL_LEAF_DIALOG = jq(
                    '<div title=\'' + dialogTitle + '\'><div style=\'max-height: ' + (Math.max(overallHeight, 180) - 150) +
                    'px; font-size: 12px; margin: 10px; white-space: normal\'>' + dialogText + '<div></div>')
                    .dialog(
                        {
                            width: '450px',
                            dialogClass: 'addDimensionDialog',
                            closeText: 'X',
                            modal: true,
                            resizable: false,
                            minHeight: '0px',
                            buttons: {
                                "<?=$text->GT('IA.OK')?>": function() {
                                    jq(this)
                                        .dialog('close');
                                }
                            },
                            dragStop: function(event, ui) {
                                if ( ui.offset.top < 0 ) {
                                    jq(this)
                                        .closest('.ui-draggable')
                                        .css('top', '0px');
                                }
                            }
                        });
            });
    </script>
    <?php
    }
}

if (isset($loadErrorMsg)) {
    /*
    echo "<script>jq().ready(function(){alert(";
    echo  json_encode($loadErrorMsg);
    echo")});</script>";
    */
    $showingErrorModal = true;
?>
<script>
    var overallHeight;
    if (document.body.clientHeight) {
        overallHeight = document.body.clientHeight;
    } else if (window.innerHeight) {
        overallHeight = window.innerHeight;
    } else if (document.documentElement && document.documentElement.clientHeight) {
        overallHeight = document.documentElement.clientHeight
    } else {
        overallHeight = document.body.offsetHeight;
    }

    jq().ready(function() {
        var dialogText = "<?=$loadErrorMsg;?>";
        var dialogTitle = "<?=$errorTitle;?>";
        $GLOBAL_LEAF_DIALOG = jq("<div title='"+dialogTitle+"'><div style='max-height: "+(Math.max(overallHeight, 180) - 150)+"px; font-size: 12px; margin: 10px; white-space: normal'>"+dialogText+"<div></div>").dialog(
            {width: '450px',
                dialogClass: 'addDimensionDialog',
                closeText: 'X',
                modal: true,
                resizable: false,
                minHeight: '0px',
                buttons: {
                    "<?=$text->GT('IA.OK')?>" : function() {
                        jq( this ).dialog( "close" );
                    },
                },
                dragStop: function(event, ui){
                    if (ui.offset.top < 0) {
                        jq(this).closest(".ui-draggable").css('top','0px');
                    }
                }
            });
    });
</script>
<?
}

// We can show warnings if there is any
// if( !$showingErrorModal && $_noWarn){
if($_savestay || $_save){
    ?>
        <style>
            .addDimensionDialog ul {
                list-style: disc;
                padding-left: 20px;
                padding-top: 5px;
            }
            .addDimensionDialog a {
                color: #007E45;
            }

            .frw-continue-right {
                float: right;
                margin-right: 20px;
            }
        </style>
    <script>
        var overallHeight;
        if (document.body.clientHeight) {
            overallHeight = document.body.clientHeight;
        } else if (window.innerHeight) {
            overallHeight = window.innerHeight;
        } else if (document.documentElement && document.documentElement.clientHeight) {
            overallHeight = document.documentElement.clientHeight
        } else {
            overallHeight = document.body.offsetHeight;
        }

        window.warn_user = <?=json_encode(!$_noWarn)?>;
        window.report_warnings = <?=json_encode($report_warnings)?>;
        window.save_action = <?=json_encode($_save)?>;
        window.save_and_stay_action = <?=json_encode($_savestay)?>;
        window.warning_help_url_1 = '<?=GetLocalizedHelpUrl() . 'Reporting/Distribute_and_run_reports/Run_reports/run-financial-reports.htm?tocpath=Reporting%7CDeliver%20reports%7CRun%20a%20report%7C_____2#Runareportofflinestoreareport'?>';
        window.warning_help_url_2 = '<?=GetLocalizedHelpUrl() . 'Reporting/Financial_reports/alternate-reporting-options.htm'?>';

        if (  window.warn_user && window.report_warnings.length > 0 ) {
            jq()
                .ready(function() {
                    var dialogText = getWarningsText();
                    var dialogTitle = "<?=$text->GT('IA.OPTIMIZE_REPORTING_PERFORMANCE')?>";
                    $GLOBAL_LEAF_DIALOG = jq('<div title=\'' + dialogTitle + '\'><div style=\'max-height: ' +
                                             (Math.max(overallHeight, 180) - 150) +
                                             'px; font-size: 12px; margin: 10px; white-space: normal\'>' + dialogText +
                                             '<div></div>')
                        .dialog(
                            {
                                width: '850px',
                                dialogClass: 'addDimensionDialog',
                                closeText: 'X',
                                modal: true,
                                resizable: false,
                                minHeight: '0px',
                                buttons: [
                                    {
                                        text : "<?=$text->GT('IA.CONTINUE')?>",
                                        click : function() {
                                            jq(this).dialog('close');
                                            if ( window.save_and_stay_action ) {
                                                document.mf.elements['.forceSave'].value = 1;
                                                DoSubmit(listFields, '.savestay', '');
                                            } else if ( window.save_action ) {
                                                document.mf.elements['.forceSave'].value = 1;
                                                DoSubmit(listFields, '.save', '');
                                            }
                                        },
                                        'class': 'frw-continue-right'
                                    }
                                ],
                                dragStop: function(event, ui) {
                                    if ( ui.offset.top < 0 ) {
                                        jq(this)
                                            .closest('.ui-draggable')
                                            .css('top', '0px');
                                    }
                                },
                                open: function(event, ui){
                                    jq('.ui-dialog-buttonset')
                                        .prepend("<span style='float: left;margin-left: 15px;'><input type='checkbox' name='doNotWarn' id='doNotShowAgainCheckbox' onclick='onclick_doNotShowagainCheckbox(this);' style='margin-right: 5px;'><label><?=$text->GT('IA.DONT_SHOW_ME_THIS_AGAIN_WITHOUT_DOT')?></label></span>");
                                }
                            });
                });
        }

        function getWarningsText() {
            var headerText = '<?=$text->GT('IA.YOUR_REPORT_PROBABLY_CANNOT_BE_GENERATED_ONLINE_DUE_TO_THE_FOLLOWING_REASONS')?>:';

            var modalFooter = GT({
                                     'id':'IA.IF_YOU_CANNOT_MAKE_THE_RECOMMENDED_MODIFICATIONS_TO_THIS_REPORT',
                                     'placeHolders': [
                                         {
                                             'name': 'LINK_RUN_REPORT_OFFLINE',
                                             'value': window.warning_help_url_1
                                         },
                                         {
                                             'name': 'LINK_ALTERNATE_REPORTING_OPTIONS',
                                             'value': window.warning_help_url_2
                                         }
                                     ]});

            return headerText + '<br><br>' +  window.report_warnings.join('<br>') + '<br><br>' + modalFooter;
        }
    </script>
<?php
}

$USE_FIX_FIRST_ROW = ($_pg =='5') ? 'true': 'false';

?>
<script>
function getAjaxURL() {
    return window.ptAjaxURL + '&rnd=' + Math.random();
}
function getAjaxCsrfToken()
{
    return window.ptAjaxCsrfToken;
}
var READY_STATE_COMPLETE = 4;
var ptAjaxURL = '<?=Pt_WebUtil::url(BaseUrl().'pt_Ajax.phtml', OP_RUNTIME);?>';
var ptAjaxCsrfToken = '<?= Pt_WebUtil::generateCsrfToken() ?>';

var $USE_FIX_FIRST_ROW =<?echo $USE_FIX_FIRST_ROW?>;

var ACCOUNT_OR_DIMENSION_ROWS='<?echo util_encode($_accountOrDimensionRows) ?>';

<? if ($_pg =='3' && isSet($bogusAccountGroups)) {
        $bogusAccountGroups_encoded = json_encode($bogusAccountGroups);
?>
jq().ready(function(){
    alert("<?=$text->GT('IA.THE_GROUP_NAMES_DOES_NOT_EXIST', ['GROUP_NAMES' => $bogusAccountGroups_encoded])?>");
});
<? } ?>

function BeforeSubmit(fld, arg, val) {
	DoSubmit(fld, arg, val);
}
function BeforeList(fld, vtype) {
	//UnSet(vtype);
	document.lf.elements[vtype].value = 0;
	document.lf.elements[vtype].value = 0;
	DoList(fld, vtype);
}

var FORMWILLHANDLESTRETCH = false;
var ROWSANDTOTALS = false;
function SetContentHeight(noresize) {
    var winheight = jq(window).height();
    var pageFooter = 6; // slop?
    var headerHeight = jq("#EditReportWizardHeader").height();
    var tempTableHeader = jq(".EditReportWizard .fwtable>THEAD");
    var tableHeader = tempTableHeader.length ? tempTableHeader.height() : 0;
    var contentHeader = jq("#contentHeader").height();
    if (isIE && getInternetExplorerVersion().major <= 8) {
        contentHeader+= jq("#wizardColumnTable_wrapper .dataTables_scrollHead").height();
    }

    var contentHeight = winheight - pageFooter - headerHeight - tableHeader + 2 - contentHeader;
    var tabHeight = jq(".EditReportWizardTabs").height() - tableHeader - contentHeader;
    contentHeight = (contentHeight < tabHeight) ? tabHeight : contentHeight;

    if (!FORMWILLHANDLESTRETCH) {
        contentHeight = (contentHeight < (winheight - (headerHeight + 3))) ? winheight - (headerHeight + 3) : contentHeight + 5;

        contentHeight -=1; // add this for IE9. :-(
        if (FF || (isIE && getInternetExplorerVersion().major >= 10)) {
            contentHeight -=1; //have some fractional width issues.
        }
    }

    if ((FF || isIE) && ROWSANDTOTALS ) {
        contentHeight -=1; // add this for IE9. :-(
        if (FF || (isIE && getInternetExplorerVersion().major >= 10)) {
            contentHeight -=1; //have some fractional width issues.
        }
    }

    jq(".EditReportWizard").height(winheight);
    jq(".EditReportWizardOuterTabs").height(contentHeight);


    return contentHeight;
}

function SetContentWidth(noresize) {
    var winwidth = jq(window).width();
    var tabWidth = jq('.EditReportWizardOuterTabs').width() + 1;
    contentWidth = winwidth - tabWidth;
    if (contentWidth < tabWidth + 100) {
        contentWidth = tabWidth + 100;
    }

    jq("#hide_table_contents").width(contentWidth + 'px');

    return contentWidth;
}

var COLUMN_TABLE_ACTIVE = false;
var REFRESH_HEADER_TEXT = false;
var redrawTimer = null;
function fixHeaderPosition(selfCall) {

    var $divHeader = jq('.EditReportWizard #divHeader');
    var $fwtable = jq('.EditReportWizard .fwtable');
    var $DTFC_LeftBodyWrapper = jq('.EditReportWizard .DTFC_LeftBodyWrapper');

    var $aboveHeader = jq('.EditReportWizard #divHeaderScroller>div');

    var ie8HackSlop = 0;
    if (isIE && getInternetExplorerVersion().major <= 8) {
        ie8HackSlop = jq('.EditReportWizardOuterTabs').width() + 2;
    }

    var divLeftOffset = $fwtable.offset().left +
        $DTFC_LeftBodyWrapper.width()
        - jq(window).scrollLeft() + ie8HackSlop;

    var slop = 0;
    if (FF && (COLUMN_TABLE_ACTIVE))  {
        slop = 3;
    } else if (!FF && !isIE && (COLUMN_TABLE_ACTIVE)) { // webkit
        slop = 2;

    }
    $divHeader.css('left', divLeftOffset -slop +  'px'); // outer container, clips the inner contaioner.

    $aboveHeader.each(function(i, elm) {
        var $elmTD = jq(elm);
        var $inputHeaderId = jq(elm).find('.aboveHeader');
        if ($inputHeaderId.length) {
            var $realelm = jq("#"+$inputHeaderId[0].value);
            var centerOffset = ($elmTD.width()/2) - ($realelm.width()/2);
            var startOfLabel = $elmTD.offset().left + centerOffset - jq(window).scrollLeft();
            startOfLabel += 5; // just looks better;
            $realelm.css('left', startOfLabel + 'px');
            $realelm.css('top', '<?echo $_pg=='4' ? '97' : '75'?>px');

            if (startOfLabel < $fwtable.offset().left +  $DTFC_LeftBodyWrapper.width() -  $DTFC_LeftBodyWrapper.width()/2  - jq(window).scrollLeft()) {
                $realelm.hide();
            } else {
                if (startOfLabel < $fwtable.offset().left +  $DTFC_LeftBodyWrapper.width() - jq(window).scrollLeft()) {
                    var percent = ((startOfLabel - $fwtable.offset().left) /($DTFC_LeftBodyWrapper.width() - jq(window).scrollLeft()));
                    percent = (percent/.5) - 1;
                    $realelm.css('opacity',percent);
                } else {
                    $realelm.css('opacity',1);
                }
                $realelm.show();
            }
        }
    });

    jq('.EditReportWizard #divHeaderScroller').css('left',
        -1 * jq(jq('.EditReportWizard .dataTables_scrollBody')[0]).scrollLeft() +'px');

    jq('.EditReportWizard .DTFC_LeftHeadWrapper .fwtable THEAD TR.list_column_headers TH.fixedCol:nth-child(1)').height($divHeader.height()-2) ;


    if (selfCall !== true) {
        if (redrawTimer != null) {
            clearTimeout(redrawTimer); // kill a pending callback.
            redrawTimer = null;
        }
        // bug in webkit where sometimes need to force this again.
        redrawTimer = window.setTimeout('fixHeaderPosition(true)', 100);
    } else {
        //we just ran!
        redrawTimer = null;
    };

    var slop = 1;
    if (isIE) slop = 0;
    if (FF) slop = 3;
    jq(".EditReportWizard .DTFC_LeftHeadWrapper THEAD>TR>TH.fixedCol:nth-child(1)").height(jq("#divHeader").height() + slop);

    jq('.DTFC_LeftHeadWrapper .fwtable').css('width', '');
    jq(".DTFC_LeftHeadWrapper .fwtable .list_column_headers .fixedCol>div").css('width', ($DTFC_LeftBodyWrapper.width()) + 'px');

    if (!$USE_FIX_FIRST_ROW) {
        jq(".EditReportWizard .dataTables_scrollBody").width((jq("BODY").width() - jq(".EditReportWizardTabs").width() - 2) + 'px');
    }
    else {
        jq(".EditReportWizard .dataTables_scrollBody").width('100%');
    }

    if (isIE && getInternetExplorerVersion().major <= 8) {
        jq('.EditReportWizard .DTFC_LeftHeadWrapper tr.list_column_headers TH.fixedCol:nth-child(1)').css('border-right-width', '2px');
    }

    var daLeftWidth = jq(".EditReportWizard .DTFC_ScrollWrapper .DTFC_LeftWrapper").width() -1;
    if (isIE && getInternetExplorerVersion().major <= 8) {
        jq(".EditReportWizard .list_column_headers").css('border-left-color', 'transparent');
        if (daLeftWidth > 0) {
            jq(".EditReportWizard .DTFC_ScrollWrapper .DTFC_LeftWrapper").width('0px')

            jq(".EditReportWizard .DTFC_ScrollWrapper .dataTables_scroll").css('left', '1px');

            var daRightWidth = jq(".EditReportWizard .dataTables_scroll").width();
            jq(".EditReportWizard .DTFC_ScrollWrapper .dataTables_scroll").width(daRightWidth + daLeftWidth + 'px');
        }
    } else {
        var slop = (isIE) ? -1 : 1;
        jq(".EditReportWizard .DTFC_ScrollWrapper .dataTables_scroll").css('left', daLeftWidth - slop + 'px');
    }
};

var timerIDSimple = null;
function fixHeaderPositionSimple(selfCall) {
    if (selfCall !== true) {
        if (timerIDSimple != null) {
            clearTimeout(timerIDSimple); // kill a pending callback.
            timerIDSimple = null;
        }
        timerIDSimple = window.setTimeout('fixHeaderPositionSimple(true)', 200);
        return;
    }
        //we just ran!
    timerIDSimple = null;

    var $scroller = jq('#hide_table_contents_simple');
    var divLeftOffset = $scroller.scrollLeft();

    var $scrollerHeader = (ROWSANDTOTALS) ? jq('#wizardRowTableHeader') : jq('#wizardComputationTableHeader');
    $scrollerHeader.css('left', -1 * divLeftOffset  +  'px'); // outer container, clips the inner contaioner.

    if (!ROWSANDTOTALS) {
        var $fwtable = jq('.EditReportWizard .fwtable');
        var $aboveHeader = jq('.EditReportWizard #wizardComputationTableHeader TH');
        $aboveHeader.each(function(i, elm) {
            var $elmTD = jq(elm);
            var $inputHeaderId = jq(elm).find('.aboveHeader');
            if ($inputHeaderId.length) {
                var $realelm = jq("#"+$inputHeaderId[0].value);
                var centerOffset = ($elmTD.width()/2) - ($realelm.width()/2);
                var startOfLabel = $elmTD.offset().left + centerOffset - jq(window).scrollLeft();
                $realelm.css('left', startOfLabel + 'px');
                $realelm.css('top',  $elmTD.offset().top + ($elmTD.height()/2) - 3 + 'px');
            }
        });
    }

    return;
};

var userAgent = navigator.userAgent.toLowerCase();
var FF = /mozilla/.test( userAgent ) && /(firefox)/.test( userAgent );
var WEBKIT = /mozilla/.test( userAgent ) && /(chrome|safari|webkit)/.test( userAgent );
var isIE = globalIs.ie;

//Have a few styles targetted to specific browsers.
if (FF) {
    jq().ready(function () {
        jq('.EditReportWizard').addClass('firefox')
    });
} else if (WEBKIT) {
    jq().ready(function () {
        jq('.EditReportWizard').addClass('webkit')
    });
}

var fixHeaderWidthsSimple_scrolldelay = null;
var fixHeaderWidthsSimple_scrolldelayOffset_scrollLeft = 0;
function fixHeaderWidthsSimple(scrollorresize, setTimeoutCallback) {
    var scrollLeft = jq('#hide_table_contents_simple')[0].scrollLeft;

    if (scrollorresize) {
        if (fixHeaderWidthsSimple_scrolldelay == null) {
            if (fixHeaderWidthsSimple_scrolldelayOffset_scrollLeft == scrollLeft) {
                return;
            }
            fixHeaderWidthsSimple_scrolldelay = window.setTimeout("fixHeaderWidthsSimple(true, true)", 500);
            return;
        } else {
            if (fixHeaderWidthsSimple_scrolldelay!=null && setTimeoutCallback == undefined) {
                if (fixHeaderWidthsSimple_scrolldelayOffset_scrollLeft == scrollLeft) {
                    return;
                }
                window.clearTimeout(fixHeaderWidthsSimple_scrolldelay);
                fixHeaderWidthsSimple_scrolldelay = null;
                fixHeaderWidthsSimple_scrolldelay = window.setTimeout("fixHeaderWidthsSimple(true, true)", 500);
                return;
            }
        }
    }
        var tableName = (ROWSANDTOTALS) ? 'wizardRowTable': 'wizardComputationTable';
        var tableNameHeader = (ROWSANDTOTALS) ? 'wizardRowTableHeader': 'wizardComputationTableHeader';
        var firstColW = jq('#'+tableName+'>thead>TR>TH:first').width();
        var $td = jq(jq(jq('#'+tableNameHeader+'>thead>TR')[1]).find('>th')[0]);
        if (FF || (isIE)) firstColW = firstColW-1;
        $td.width(firstColW + 'px');
        $td.css('min-width', firstColW + 'px');

        // Fix for DE19939/123593: we should also subtract the width of the scrollbar from
        // the contentWidth but, because of the positioning of the table in this case we
        // couldn't use jq('body').innerWidth() so, we assume the scrollbar is 20px wide
        var contentWidth = jq(window).width() - jq('.EditReportWizardOuterTabs').width() - 20;
        jq('#hide_table_contents_simple').width(contentWidth + 'px')
        jq('#hide_table_contents_simple_header').width(contentWidth + 'px')
        return;
}

function fixHeaderWidths() {

    var height = jq('.EditReportWizard #divHeaderScroller').height() - 2;
    var $bodytds = jq('.EditReportWizard .dataTables_scrollBody .fwtable>TBODY>TR:nth-child(1)>TD');
    jq('.EditReportWizard #divHeaderScroller>div').each(function(index, elm) {
        $elm = jq(elm);
        var plusAmount = FF ? 0 : 1;
        $associatedTD = jq($bodytds[index]);
        if (isIE && $associatedTD.hasClass('seperatorLeft') ) {
            plusAmount = 0;
        } else if (isIE && jq('#wizardColumnTable').length) {
           plusAmount = 0;
           var $selTDs = jq("TD.colsel");
           if ($selTDs.length) {
               var selColIndex = jq($selTDs[0]).parent().children().index($selTDs[0]);
               if (index == 0 || selColIndex == index) {
                   plusAmount = -1;
               }
           }
        } else {
            plusAmount = 0;
        }

        if (!$USE_FIX_FIRST_ROW) {
            if (index == 0) $elm.css('padding-right', '7px');
        }


        $elm.css('width', $associatedTD.width() + plusAmount + 'px');
        $elm.css('height', height + 'px');
        });

    fixHeaderPosition();

};

var SCROLLTIMER = null;
function fixScrollHeight(finalcallback) {

    var height = SetContentHeight();
    //if (!$USE_FIX_FIRST_ROW) {
        height = height + 2;
    //};

    jq(".dataTables_scrollBody").height(height + 'px');
    jq(".DTFC_LeftBodyWrapper").height(height + 'px');
    jq(".DTFC_ScrollWrapper").height(height + 2 + jq('#divHeader').height() + 'px');

    if (SCROLLTIMER != null) {
        clearTimeout(SCROLLTIMER);
        SCROLLTIMER = null;
    }
    if (!finalcallback) {
        SCROLLTIMER = window.setTimeout('fixScrollHeight(true)', 500);
    }
};

function fixScrollHeightSimple(finalcallback) {
    var height = SetContentHeight();
    //if (!$USE_FIX_FIRST_ROW) {
        height = height + 2;
    //};

    var idName =  'hide_table_contents_simple';
    jq("#" + idName).css('height', height - 2 - parseInt(jq("#hide_table_contents").css('margin-top'), 10) + 'px');
    jq("#" + idName).css('overflow', 'auto');

    if (SCROLLTIMER != null) {
        clearTimeout(SCROLLTIMER);
        SCROLLTIMER = null;
    }
    if (!finalcallback) {
        SCROLLTIMER = window.setTimeout('fixScrollHeightSimple(true)', 500);
    }
};


function pinLeftTabMenu() {
    jq('.EditReportWizard .EditReportWizardOuterTabs>div').width(jq('.EditReportWizard .EditReportWizardTabs').width() + 'px');
    jq('.EditReportWizard .EditReportWizardTabs').css('top', jq('.EditReportWizard .EditReportWizardHeader').css('top') + 'px');
}

function initDivHeaders() {
    //if (!$USE_FIX_FIRST_ROW) {
        jq("HTML").css('overflow', 'hidden');
        jq(".EditReportWizard").css('overflow', 'hidden');
        //}
    pinLeftTabMenu();

    jq('.EditReportWizard .fwtable').css('width', '1px');

    jq('.EditReportWizard .dataTables_scrollHead').css('visibility', 'hidden');
    var $divheadertds = jq('.EditReportWizard .dataTables_scrollHead .fwtable>THEAD>TR:nth-child(1)>TH');
    var $divs = jq('.EditReportWizard #divHeaderScroller>div');
    $divs.each(function(index, elm) {
        jq(elm).html(jq($divheadertds[index]).html());
        elm.style.fontWeight = $divheadertds[index].style.fontWeight;
    });

    /*
    var $bodytds = jq('.EditReportWizard .fwtable>TBODY>TR:nth-child(1)>TD');
    jq('.EditReportWizard .fwtable COLGROUP>COL').each(function(index, elm) {
        elm.style.minWidth=$bodytds[index].offsetWidth+'px';
        });
    */
    function max(x, y) {
        return ((x-0) > (y-0)) ? x : y;
    }

    // set min-widths on headers and in body rows

    var $bodyheadertds = jq('.EditReportWizard .dataTables_scrollBody .fwtable>Thead>TR:nth-child(1)>TH');
    $divs.each(function(index, elm) {
        var $elm = jq(elm);
        elm.style.minWidth = max(elm.scrollWidth, $bodyheadertds[index].scrollWidth) + 'px';
            //elm.style.minWidth = max($elm.width(), jq($bodyheadertds[index]).width()) + 'px';
        });

    //now the divs hane the min width, now need to set the body
    //for some reason col groups are not working so I enum all the rows. :-(
    jq('.EditReportWizard .dataTables_scrollBody .fwtable>TBody>TR').each(function(i, tr) {
      //jq('.EditReportWizard .dataTables_scrollBody .fwtable>COLGROUP').each(function(i, tr) {
        jq(tr).each(function (index, tr2) {
            jq(tr2).children('TD').each(function(in2, td) {
                td.style.minWidth = $divs[in2].style.minWidth;
            });
        });
    });

    jq('.EditReportWizard #divHeader').height(jq('.EditReportWizard #divHeaderScroller').height());

    jq('.EditReportWizard .fwtable').css('width', (FF) ? '100%' : '0px');

    if (isIE) {
        jq('.DTFC_LeftHeadWrapper').css('z-index', '1');
        jq('.DTFC_LeftHeadWrapper div.spacerFixedColDiv').css('border-bottom-width', '2px');
        if (isIE && getInternetExplorerVersion().major >= 9) {
            jq(".EditReportWizard THEAD > TR > TH.fixedCol").css('border-right-width', '1px');
        }


        var $t = jq(".EditReportWizard .DTFC_LeftBodyWrapper .fwtable");
        $t.css('border-right-width', '1px');
        $t.css('border-right-style', 'solid');
        $t.css('border-right-color', '#e0eaf6');

        jq(".EditReportWizard .DTFC_LeftBodyWrapper").css('margin-top', '-2px');
        $t.width(jq(".EditReportWizard .DTFC_LeftBodyWrapper").width() + 'px');
     } else if (FF) {
        jq('.DTFC_LeftHeadWrapper div.spacerFixedColDiv').css('bottom', '3px');
        jq('.DTFC_LeftHeadWrapper div.spacerFixedColDiv').css('border-bottom-width', '2px');
        jq(".EditReportWizard .DTFC_LeftBodyWrapper").css('margin-top', '-3px');
     }

    jq("#divHeaderScroller .help_field_modal").each(function() {
        FieldHelpModal_rebindClick(jq(this));
    });

    if ((isIE || FF) && !$USE_FIX_FIRST_ROW) {
        jq("#divHeader").css('margin-top', '1px');
    }

    if ((FF) && $USE_FIX_FIRST_ROW) {
        jq("#wizardColumnTable tbody>tr td:nth-child(1):not(.combo-box-td)").css({'border-left': '2px solid #b0c6e0'});
        jq(".fwtablewrapper .dataTables_scrollBody").css('margin-left', '-1px');
    }

    if ((FF) && $USE_FIX_FIRST_ROW) {
        jq(".fwcolumntable .list_column_headers th").css('border-right', '2px solid #b0c6e0');
    } else {
        jq(".fwcolumntable .list_column_headers th").css('border-right', '1px solid #b0c6e0');
    }

    //have to do this because at least on IE this guy is not copied correctly into the DOM when it gets moved into a new div.
    FieldHelpModal('group_headings');
    FieldHelpModal_rebindClick(jq('#group_headings'));
}

function initDivHeadersSimple() {
    //var firstColW = jq('#wizardRowTable>thead>TR>TH:first').width();
    //jq('#wizardRowTableHeader>thead>TR>TH:first').width(firstColW + 'px');
    jq("HTML").css('overflow', 'hidden');

    var tableName = (ROWSANDTOTALS) ? 'wizardRowTable': 'wizardComputationTable';
    var tableNameHeader = (ROWSANDTOTALS) ? 'wizardRowTableHeader': 'wizardComputationTableHeader';
    var $theadtrth = jq(jq('#'+tableName+'>thead>TR')[1]).find('>TH');
    var $thead2tr = jq(jq('#'+tableNameHeader+'>thead>TR')[1]).find('>TH');

    $thead2tr.each(function(i) {
        var $headTH = jq($theadtrth[i]);
        var newWidth = $headTH.width();

        //HACK CITY FOR FF && IE
        if (FF && !ROWSANDTOTALS && (i == 2 || i == 4)) {
            //what a HACK
            newWidth = newWidth -1;
        } else if (isIE && ROWSANDTOTALS && $headTH.hasClass('seperatorLeft')) {
            newWidth = newWidth -1;
        } else if (isIE && !ROWSANDTOTALS && (i == 2 )) {
           newWidth = newWidth +1;
        }

        if (ROWSANDTOTALS) {
            jq(this).width(newWidth + 'px');
            jq(this).css('min-width', newWidth + 'px');
        } else {
            var otherWidth = jq(this).width();
            if (otherWidth <= newWidth ) {
                jq(this).width(newWidth + 'px');
                jq(this).css('min-width', newWidth + 'px');
            } else {
                $headTH.width(otherWidth + 'px');
                $headTH.css('min-width', otherWidth + 'px');
            }
        }

    });

    pinLeftTabMenu();

    return;
}

function hideSubs(tableId, $tr, index, hideSubs) {
	var rowIndex = index;
	var labelRows = jq(".DTFC_LeftBodyWrapper ."+tableId+">tbody>tr");
	var bodyRows = jq("#"+tableId+">tbody>tr");
	if (!labelRows.length) {
	    labelRows = bodyRows;
	}
	var totalRows = bodyRows.length;
	var baseindent = jq('#indentLevel_' + rowIndex ).attr('value');
	for (var i = rowIndex+1; i < totalRows ; i++) {
		var curIndent = jq('#indentLevel_' + i).attr('value');
		var $curRow = jq(labelRows[i]);
		if (parseInt(curIndent) > parseInt(baseindent)) {


		    if (hideSubs) {
		        jq(bodyRows[i]).hide();
		        if (labelRows != bodyRows)jq(labelRows[i]).hide();
    		} else {
		        jq(bodyRows[i]).show();
    		    if (labelRows != bodyRows) jq(labelRows[i]).show();

        		if($curRow.find('td:first-child div.expanded').length == 0) {
            		//skip the rest at this level and below
            		var collaspedIndent = jq('#indentLevel_' + i ).attr('value');
            		for (i++; i < totalRows ; i++) {
                		var curIndent = jq('#indentLevel_' + i ).attr('value');
                		if (parseInt(curIndent) <= parseInt(collaspedIndent)) {
                    		i--;
                    		break;
                		};

        		        jq(bodyRows[i]).hide();
        		        if (labelRows != bodyRows)jq(labelRows[i]).hide();
                    }
        		};
    		}


		} else {
    		break;
    	}
	}
}

function hidedimensionDetail(tableId, rowIndex, trelm) {
    if ((jq('#indentLevel_' + rowIndex ).attr('value') - 0) > 0) return;

    var $this = jq(trelm);
    var expanded = expanded = $this.find('td:first-child div.expanded').length;

    var $dimDetail = $this.find("td>div>div.dimensiondetail");
    if ($dimDetail.length) {
        $dimDetail[0].style.visibility = expanded ? 'hidden' : '';
    }
    if (expanded) {
        hideSubs(tableId, $this, rowIndex, false);
    } else if ($this.find('td:first-child div.collapsed').length &&
        jq('#indentLevel_' + rowIndex ).attr('value')-0 == 0 ) {
        hideSubs(tableId, $this, rowIndex, true);
    }
}
</script>
<form name=mf action="<? echo isl_htmlspecialchars($postBackUrl); ?>" method=post target="_self">

<!-- <input type=hidden name='.sess' value="<? echo $_sess; ?>"> -->
<input type=hidden name=".repname">
<input type=hidden name=".repTimeStamp" value="<?echo util_encode($_repTimeStamp)?>">
<input type=hidden name="hlpcontext" value="<? echo GetHelpContext(); ?>">
<input type=hidden name="_changed" value="<? echo util_encode(Request::$r->_changed); ?>">
<input type=hidden name="_currentfocus" value="">
<input type=hidden name="_currentform" value="">
<input type=hidden name="_currentlayer" value="">
<input type=hidden name="onfocusvalue" value="">
<input type=hidden name="AccGrpPickCurrField" value="">
<?php echo CsrfUtils::generateCsrfTokenInput(Request::$r->_op); ?>

<? PrintReportStateFields($_r, 1, $_pg, true); ?>

<!-- begin javascript -->
        <?=TextHelper::getInstance(FinancialReportWizard::class)->printClientTextMap();?>

    <script src="../resources/js/ComboBox.js"></script>
<SCRIPT src="../resources/js/jefunctions.js"></script>

<script language="javascript">

// define global vars
// this is the acctgrp map var name which will be used data map for all
// the acctgrp combo-boxes in page 4 and also the js
var gJSSess = '<?=$_sess?>';
var _acctgrp_map = new Array;
var _acctgrppurpose_map = new Array;
var _reporttype_map = new Array;
var _reportaudience_map = new Array;
var _grppurpose_map = new Array;
var _acctgrp_acct_map = new Array;
var _acctgrp_dim_all_map = new Array;
var _acctgrppath_map = new Array;
var listField = '<?=$page['listfield']?>';
var page = '<?=$_pg?>';
var	defaultBud = '<? echo $defaultBudget; ?>';

var switchColsTable = 'C';

var PAGE_LAYOUT_TYPE = '<? echo QXCommon::getLayoutType(); ?>';

var aCompareByLab = new Array();
var aCompareByVal = new Array();
<?  $i = 0;

foreach ($kCompareBy as $key => $val) {?>
		aCompareByLab[<?=$i?>] = '<?=$val?>';
		aCompareByVal[<?=$i?>] = '<?=$key?>';
<?
 $i++;
}
?>

//Initializing Compare by options which are needed for rolling period
var TIME_PERIOD_WITH_DASH = '-- ' + GT('IA.TIME_PERIOD_WITH_DASH') + ' --';
var aRollingCompareByLab = [TIME_PERIOD_WITH_DASH,'IA.EXPAND_QUARTERS','IA.EXPAND_MONTHS','IA.EXPAND_WEEKS','IA.EXPAND_DAYS','IA.EXPAND_BUD_PERIODS'];
aRollingCompareByLab = aRollingCompareByLab.map(function(token){ return GT(token); });
var aRollingCompareByVal = new Array('15','<?=GRPQUARTERS?>','<?=GRPMONTHS?>','<?=GRPWEEKS?>','<?=GRPDAYS?>','<?=GRPBUDGETPERIODS?>');

<?
    $periodDbValues = [
        "Current Year",
        "Current Year To Date",
        "Fiscal - Current Year",
        "Fiscal - Current Year To Date",
        "Trailing 1 Year",
        "Trailing 4 Quarters",
        "12 Months to Current Month",
        "12 Months to Current Date",
        "Current Quarter",
        "Current Quarter To Date",
        "Fiscal - Current Quarter",
        "Fiscal - Current Quarter To Date",
        "Current Month",
        "Current Month To Date",
        "This Week",
        "Current Year Through Current Month",
        "Current Year Through Prior Month",
        "Prior Year Through Current Month",
        "Prior Year Through Prior Month",
        "Fiscal - Current Year Through Current Month",
        "Fiscal - Current Year Through Prior Month",
        "Fiscal - Prior Year Through Current Month",
        "Fiscal - Prior Year Through Prior Month",
        "Current Year Through Current Quarter",
        "Current Year Through Prior Quarter",
        "Prior Year Through Current Quarter",
        "Prior Year Through Prior Quarter",
        "Fiscal - Current Year Through Current Quarter",
        "Fiscal - Current Year Through Prior Quarter",
        "Fiscal - Prior Year Through Current Quarter",
        "Fiscal - Prior Year Through Prior Quarter",
        "Today"
    ];
    $periodDbValuesMap = [];
    foreach ( $periodDbValues as $dbValue ) {
        $externalLabel = DBTokensHandler::getInstance()->getExternalLabel($dbValue);
        $periodDbValuesMap[$externalLabel] = $dbValue;
    }
?>

var colPeriodLabelDbMap = <?=json_encode($periodDbValuesMap)?>;

//Hard coding required rolling period values depends on reporting period
var yearRolOpts = new Array(0,1,2,5);
var tMonthRolOpts = new Array(0,2,5);
var quarterRolOpts = new Array(0,2,5);
var monthRolOpts = new Array(0,3);
var weekRolOpts = new Array(0,4);
var trail4QuarterRolOpts = new Array(0,1,5);

var yearThroughMonthRolOpts = new Array(0,2,5);
var yearThroughQuarterRolOpts = new Array(0,1,2,5);

var _acctgrpOp = <?=$acctgrpOp?>;
var nonStandardPeriod = <?=$gNonStandardPeriods?'true':'false'?>;
</script>

<!--Including js functions from financialreportwizard.js-->
<script src="../resources/js/financialreportwizard.js"></script>

<? if (in_array($_pg, array('3','4','5'))) { ?>
<script language="javascript">
/** this code is for "enter" key press functionality **/
	document.onkeypress = checkKey;
</script>
<?
}?>

<!-- end javascript -->

<?

function EditReportHeaderSeperator()
{
    global $uaInfo;
    echo "<div class='reportHeaderSeperator".(($uaInfo['browser'] == 'IE' && $uaInfo['version.major'] == 9) ? ' reportHeaderSeperator_ie9':'')."'></div>";
}

function EditReportSectionSeperator()
{
    echo "<div class='reportSectionSeperator'></div>";
}

function EditReportGeneratePagePermissionsHack()
{
    //We keep movng this section in and out of the first page. :-()

    EditReportHeaderSeperator();
    EditReportGeneratePagePermissions();
    ?>
    <script>
        pinLeftTabMenu();
        SetContentHeight(true);
        jq(window).bind('resize', function () {
            SetContentHeight();
        });
        jq().ready(function() {
            try {
                //could be read only text and not an input... so have in try catch.
                document.getElementById('rptowner').focus();
                document.getElementById('rptowner').select();
            } catch (e) {

            }
        });
    </script>
    <?
}

/**
 * @param bool $permissionHack
 */
function EditReportGeneratePageTitle($permissionHack=false)
{
    global $fromCSTool;
    $gManagerFactory = Globals::$g->gManagerFactory;
    $text = TextHelper::getInstance(FinancialReportWizard::class);

    $_status    = &Request::$r->_status;
    $_name      = &Request::$r->_name;
    $_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
    $_accountOrDimensionRows = htmlspecialchars($_accountOrDimensionRows, ENT_COMPAT);
    $_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;

    $name = isl_htmlspecialchars($_name);
    $disabled = "";
    if ( ! empty(Request::$r->_version) ) {
        $disabled = "readonly";
    }
    $_glreporttype = &Request::$r->_glreporttype;
    $_glreportaudience = &Request::$r->_glreportaudience;
    $fs_name = FieldSize('reportinfo.name');
    GenerateReportTagsMap();
    EditReportHeaderSeperator();

    ?>

    <div id='pageinfosection'>

        <input type=hidden name=".colcomparison">

            <fieldset style='margin-left: 10px'>

                <table  style=' margin-top:20px'>
                    <tr>
                        <td class='reportFieldLabel' style='vertical-align: top;padding-top: 4px;font-weight: 700'><label><?=$text->GT('IA.REPORT_NAME')?><span style='width: 4px;visibility: hidden;display: inline-block'><input type='radio'></span><span style='visibility: hidden'><br><?=$text->GT('IA.REPORT_STRUCTURE')?></span></label></td>
                        <td class='reportFieldValue'>
                            <div class="requiredOuter">
                                <div class="requiredIcon"></div>
                                <input type=text name=".name"  value="<?=$name?>" autocomplete="off" size="50" maxlength="<?=$fs_name?>" onchange="" <?=$disabled?>>
                                <input type=hidden name=".permname" value="<?=$name?>" <?=$disabled?>>
                                <div class='reportHelp'><nobr><?=$text->GT('IA.IDENTIFIES_THE_REPORT_SO_YOU_CAN_FIND_IT_LATER')?></nobr></div>
                            </div>
                        </td>

                <?if (!$fromCSTool) { ?>
                    <tr>
                        <td class="reportFieldLabel" ><label>&nbsp</label></td>
                        <td class="reportFieldValue">
                            <table>
                                <tr>
                                    <td>
                                        <span style="float: right;"><?=$text->GT('IA.REPORT_TYPE')?></span>
                                    </td>
                                    <td>
                                        <?
                                        $reportTypeComboStr = customComboStr('glreporttype', 0, 'reporttype','', $_glreporttype, false, '', false, '_glreporttype');
                                        ?>
                                        <div>
                                            <div style="display:inline-block;position:relative;vertical-align: middle;">
                                                <div style="max-height: 22px"></div>
                                                <span class="componentcombo" ><? echo $reportTypeComboStr; ?></span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span style="float: right;"><?=$text->GT('IA.REPORT_AUDIENCE')?></span></td>
                                    <td>
                                        <? $reportAudienceComboStr = customComboStr('glreportaudience', 0, 'reportaudience','', $_glreportaudience, false, '', false, '_glreportaudience');
                                        ?>
                                        <div>
                                            <div style="display:inline-block;position:relative;vertical-align: middle;">
                                                <div style="max-height: 22px"></div>
                                                <span class="componentcombo" ><? echo $reportAudienceComboStr; ?></span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <table style='margin-top: 10px'><tr><td> <!-- / here to align the damm checkbox -->
                            <input name=".status" type='checkbox' value='active' <?php if ($_status == 'active') { echo 'checked';
}?>>
                            </td><td class='checkboxalign'>
                            <label><?=$text->GT('IA.THIS_REPORT_IS_ACTIVE')?></label>
                            </td></tr></table>
                        </td>
                  </tr>
                <?
} else {
                    global $industryMap;
                    $_industry = &Request::$r->_industry;
                    $indMgr = $gManagerFactory->getManager('industry');
                    $industryMap = $indMgr->GetIndustryMap();

                    ?>
                    <tr>
                        <td class="reportFieldLabel" ><label>Industry</label></td>
                        <td class="reportFieldValue"   style='width: 1px;'>
                            <select name=".industry">
                                <?ShowFinOptions($_industry,  $industryMap);?>
                            </select>
                            <input type=hidden name=".runtimeparams" value="dept:location:asofdate:repbook">
                        </td>
                        <td style='width: 100%'></td>
                    </tr>
                <?
}?>
               </table>

               </fieldset>

        <?if (!$permissionHack) {
            //Need this here for things to initialize correctly when we get to the permissions page with Group/Everyone
            echo "<div style='display:none'>";
                EditReportGeneratePagePermissions();
            echo "</div>";
}?>

        <?
        global $hasAccountComponents;
        global $hasDimensionComponents;
        $_r = &Request::$r->_r;
        $disabledReportTypeChoices = '';
        if (!empty($_r)) {
            $disabledReportTypeChoices = "disabled";
        }
        if ($hasAccountComponents || $hasDimensionComponents) {
            $disabledReportTypeChoices = "disabled";
        };
        ?>

        <fieldset style='margin-left: 10px'>
        <table style=' margin-top:20px'>
            <tr>
                <td class='reportFieldLabel' style='vertical-align: top;padding-top: 2px;font-weight: 700'><label><?=$text->GT('IA.REPORT_STRUCTURE')?></label><span style='width: 4px;visibility: hidden;display: inline-block'><input type='radio'></span></td>
                <td>
                    <table class="requiredOuter"  style='display: table'>
                        <tr>
                            <td>
                                <!--hidden here because disabled values are not posted back-->
                                <input type=hidden   id="accountOrDimensionRows" name=".accountOrDimensionRows" value="<?=util_encode($_accountOrDimensionRows)?>" ) >
                                <input onclick='toggleDimensionRowsExpandByDiv(false);  change_financial_wizard_structure_examples("account")' onchange='jq("#accountOrDimensionRows").val(this.value)' type=radio id=".accountOrDimensionRows1" name=".accountOrDimensionRowsHack" <?echo $disabledReportTypeChoices?> <? echo ($_accountOrDimensionRows !=='dimension' ? 'checked' : '')?> value="account">
                            </td>
                            <td>
                                <label style="cursor: pointer; cursor: default;" for=".accountOrDimensionRows1" >&nbsp;<?=$text->GT('IA.ACCOUNTS')?></label>
                            </td>
                            <td>
                                &nbsp;&nbsp;<input   onclick='toggleDimensionRowsExpandByDiv(true); change_financial_wizard_structure_examples("dimension")' onchange='jq("#accountOrDimensionRows").val(this.value)'  type=radio id=".accountOrDimensionRows2" name=".accountOrDimensionRowsHack" <?echo $disabledReportTypeChoices?> <? echo ($_accountOrDimensionRows ==='dimension' ? 'checked' : '')?> value="dimension">
                            </td>
                            <td>
                                <label style="cursor: pointer; cursor: default;" for=".accountOrDimensionRows2" >&nbsp;<?=$text->GT('IA.DIMENSIONS')?></label>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                </td>
                <td>

            <div  style='padding:0px; margin:0px;' class='reportSection reportHelp'><?=$text->GT('IA.DETERMINE_THE_BASIC_REPORT_STRUCTURE_BY_SELECTING')?></div>
                    <?if ($disabledReportTypeChoices !='' && !$fromCSTool) { echo "<div class='reportHelp' style=' margin-top: 4px;color:#515151'>".$text->GT('IA.NOTE_THE_CURRENT_REPORT_STRUCTURE_CAN_T_BE_CHANGED')."</div>";
}?>
                    <?if ($fromCSTool) { echo "<div class='reportHelp' style=' margin-top: 4px;'><span style='font-weight:700;'>Note:</span> Only Accounts are available from the CS Tools.</div>";
}?>

                    <script>
                        function toggleDimensionRowsExpandByDiv(dim) {
                            <?
                                $text = TextHelper::getInstance(FinancialReportWizard::class);
                                $filterAccounts = '&nbsp;' . $text->GT('IA.FILTER_BY_DIMENSION_STRUCTURE_ON_ROWS');
                                $expandDimensions = '&nbsp;' . $text->GT('IA.FILTER_BY_ACCOUNT_GROUP_ON_ROWS');
                                $expandDimensionsDefault = $_accountOrDimensionRows ==='dimension' ? $expandDimensions : $filterAccounts;

                            ?>
                            document.getElementById('dimensionRowsExpandByAccount').value = '';
                            document.getElementById('.dimensionRowsExpandByAccounthack').checked = false;
                            if (dim) {
                                jq("#expandbylabel").html('<?=$expandDimensions?>');
                                jq("#page1_account_cb_text").hide();
                                jq("#page1_dimension_cb_text").show();
                                jq("#page1_account_radio_text").hide();
                                jq("#page1_dimension_radio_text").show();
                            } else {
                                jq("#expandbylabel").html('<?=$filterAccounts?>'); //Expand dimensions by account
                                jq("#page1_account_cb_text").show();
                                jq("#page1_dimension_cb_text").hide();
                                jq("#page1_account_radio_text").show();
                                jq("#page1_dimension_radio_text").hide();
                            }

                        }
                    </script>

                    <table style="display:inline-table; max-width: 600px; margin-top: 10px">
                    <tr>
                        <td colspan="1" class='DimensionRowsExpandByDiv' style='vertical-align: top; min-width:400px' >
                            <?
                            $accountTextStyle =  ($_accountOrDimensionRows == 'dimension') ? ' style="display:none" ': '';
                            $dimensionTextStyle =  ($_accountOrDimensionRows == 'dimension') ? '' : ' style="display:none" ';
                            ?>

                            <table class="requiredOuter" style='margin-top: 0px; display: table' >
                                <tr>
                                    <td>
                                        <!-- hack because disabled values do not get posted -->
                                        <input type=hidden   id="dimensionRowsExpandByAccount" name=".dimensionRowsExpandByAccount" <? echo ($_dimensionRowsExpandByAccount ==='T' ? 'value="T"' : 'value=""')?> >
                                        <input type=checkbox  onchange="jq('#dimensionRowsExpandByAccount').val(this.checked ? 'T' : ''); change_financial_wizard_structure_examples()" id=".dimensionRowsExpandByAccounthack" name=".dimensionRowsExpandByAccounthack"  <?echo $disabledReportTypeChoices?> <? echo ($_dimensionRowsExpandByAccount ==='T' ? 'checked' : '')?> value="T">
                                    </td>
                                    <td style="vertical-align: top">
                                        <label id='expandbylabel' style="cursor: pointer; cursor: default;" for=".dimensionRowsExpandByAccount" ><?=$expandDimensionsDefault?></label>
                                    </td>
                                </tr>
                            </table>
                            <div id='page1_account_cb_text' <?=$accountTextStyle?>>
                                <!--<div  style='padding-top: 6px' class='reportHelp'>Includes option to pick different dimensional data for different report sections.  For instance, Revenue by product line and Expenses by department.</div>-->
                            </div>
                            <div id='page1_dimension_cb_text' <?=$dimensionTextStyle?>>
                                <!--<div  style='padding-top: 6px' class='reportHelp'>Includes option to select different account data for different report sections. For instance, Top 10 Customers by Revenue and Projects by Expenses.</div>-->
                            </div>

                        </td>
                    </tr>
                </table>
                <table>
                    <tr>
                        <td class='' style='vertical-align: middle; text-align: center; width: 100%' >
                            <div id='financial_wizard_structure_examples' style="min-width: 300px; min-height: 350px; font-weight: 700; text-align: center"></div>
                        </td>
                    </tr>
                </table>
            </td>
            </tr>
            </table>
        </fieldset>

    </div>

	<script>
	pinLeftTabMenu();
	SetContentHeight(true);
    jq(window).bind('resize', function () {
    	SetContentHeight();
    });
    <?
       $editImageA = '../resources/images/ui/blue/images/Help_button.png';
       $editImageB = '../resources/images/ia-app/icons/small_video.png';
       $editImageC = '../resources/images/map/arrow_left.png';
       $editImageD = '../resources/images/map/arrow_right.png';
       echo "\nvar FW_HELP_BUTTON='".$editImageA."';var FW_SMALL_VIDEO='".$editImageB."';var FW_ARROW_LEFT='".$editImageC."';var FW_ARROW_RIGHT='".$editImageD."';\n";
    ?>
    jq().ready(function() {
        if (FieldHelpModal_iconContentDefined("financial_wizard_structure_examples")) {
            FieldHelpModal_showContentInline("financial_wizard_structure_examples");

            init_financial_wizard_structure_examples();

        document.getElementsByName('.name')[0].focus();
        document.getElementsByName('.name')[0].select();
        }});

    function change_financial_wizard_structure_examples(accountOrDimension) {
        if (accountOrDimension === undefined) {
            accountOrDimension = jq("#accountOrDimensionRows").val();
        }

        var expandchecked = jq('#dimensionRowsExpandByAccount').val() == 'T';

        if (accountOrDimension == 'account' && !expandchecked) {
            setReportStructurePage('default_account');
        } else if (accountOrDimension == 'account' && expandchecked) {
            setReportStructurePage('default_account_filterby');
        } else if (accountOrDimension == 'dimension' && !expandchecked) {
            setReportStructurePage('default_dimension');
        } else {
            setReportStructurePage('default_dimension-expandby');
        };

    }

    function init_financial_wizard_structure_examples() {
        <?
        $_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
        $_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;
        $showAccount = ($_accountOrDimensionRows === 'account' && $_dimensionRowsExpandByAccount !=='T') ? 'true' : 'false';
        $showAccountExpand = ($_accountOrDimensionRows === 'account' && $_dimensionRowsExpandByAccount ==='T') ? 'true' : 'false';
        $showDimension = ($_accountOrDimensionRows === 'dimension' && $_dimensionRowsExpandByAccount !=='T') ? 'true' : 'false';
        /** @noinspection PhpUnusedLocalVariableInspection */
        $showDimensionExpand = ( $_accountOrDimensionRows === 'dimension' && $_dimensionRowsExpandByAccount === 'T') ? 'true' : 'false';
        ?>

        if (<?=$showAccount?>) {
            setReportStructurePage('default_account');
        } else if (<?=$showAccountExpand?>) {
            setReportStructurePage('default_account_filterby');
        } else if (<?=$showDimension?>) {
            setReportStructurePage('default_dimension');
        } else {
            setReportStructurePage('default_dimension-expandby');
        };

    }

	</script>
<?
}

/**
 * @param string $line
 * @param string $inner
 * @param string $indent
 * @param int|float $indentLevel
 */
function parseLineInfoBecauseNoAPI($line, &$inner, &$indent, &$indentLevel)
{
    $inner='';
    $indent='';
    $indentLevel = 0;

    $line = str_replace(array("\n", "\r"), '', $line); // ugh... I have seen NL in the line passed in!

    if (preg_match('/(<td[^>]*>)(.*)(<\/td.*)/', $line, $matches)) {
        $inner = $matches[2];
        $indent = "";
        if (preg_match('/(<img[^>]*>)(.*)/', $inner, $matches2)) {
            $inner = $matches2[2];
            $indent = $matches2[1];

            if (preg_match('/(width=")([0-9]*)/', $indent, $matches3)) {
                $value = $matches3[2];
                $indentLevel = round($value/15);
            }
        }
    }
}

function GenerateReportTagsMap(){
    $factory = &Globals::$g->gManagerFactory;
    $reportTypeMap = $factory->getManager('glreporttype')->getComboBoxMap();
    $reportAudienceMap = $factory->getManager('glreportaudience')->getComboBoxMap();

    $js = "<SCRIPT language='JavaScript'>";
    $js .= " _reporttype_map = ". json_encode($reportTypeMap) .";\n";

    $js .= " _reportaudience_map = ". json_encode($reportAudienceMap) .";\n";
    $js .= "</SCRIPT>";

    echo $js;
}

/**
 * @return array
 */
function GenerateDimensionAndAccountMaps()
{
    $_pg = &Request::$r->_pg;
    $_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
    $mapDimensionToDimensionClassID = getDimensionToDimensionClassIDMap();

    $acctGrpUIMap = GetUIAcctGrpMap(false, 'json', 'all', false);
    $acctGrpUIAllDimMap = GetUIAcctGrpMap(false, 'json', 'dim-all', true);
    $acctGrpUIDimMaps = array();
    foreach( $mapDimensionToDimensionClassID as $k => $v ) {
        $acctGrpUIDimMaps[$k] = GetUIAcctGrpMap(false, 'json', $k, true);
    }

    $filterSystemAccounts = ($_pg !=  '5' && ($_pg !=  '3' && $_accountOrDimensionRows === 'dimension') );
    $acctGrpUIAcctMap = GetUIAcctGrpMap(false, 'json', 'acct',
                                        I18N::getSingleToken('IA.ACCOUNT_GROUPS'),
                                        $filterSystemAccounts);
    $acctGrpUIAcctList = GetUIAcctGrpMap(false, 'json', 'acct-list', false);

    $acctGrpMapWithPurpose = Globals::$g->gManagerFactory->getManager('glacctgrp')->GetPurposeMap();
    $acctGrpPurpose = Globals::$g->gManagerFactory->getManager('glacctgrppurpose')->getComboBoxMap();
    $jsArrStr = '';

    $jsArrDimAllStr = '';
    $jsArrDimStrs = [];

    $jsacctGrpMapWithPurpose = json_encode($acctGrpMapWithPurpose);
    array_walk($acctGrpPurpose, function(&$v, $k) { $v = [$v]; });
    $jsacctGrpPurpose  = json_encode($acctGrpPurpose);

    foreach( $mapDimensionToDimensionClassID as $k => $v ) {
        $jsArrDimStrs[$k] = '';
    }

    $jsArrAcctStr = '';
    /** @noinspection PhpUnusedLocalVariableInspection */
    $jsAG = '';
    $jsAGP = '';

    foreach ( $acctGrpUIMap as $val) {
        $jsArrStr .= "[" . $val . "],";
    }

    foreach ( $acctGrpUIAllDimMap as $val) {
        $jsArrDimAllStr .= "{" . $val . "},";
    }

    foreach ($acctGrpUIDimMaps as $key => $value) {
        foreach ( $value as $value2) {
            $jsArrDimStrs[$key] .= "{" . $value2 . "},";
        }
    }

    foreach ( $acctGrpUIAcctMap as $val) {
        $jsArrAcctStr .= "{" . $val . "},";
    }
    foreach ( $acctGrpUIAcctList as $val) {
        /** @noinspection PhpUndefinedVariableInspection */
        $jsArrAcctListStr .= $val . ": 1,";
    }


    $jsArrStr = isl_substr($jsArrStr, 0, isl_strlen($jsArrStr)-1);
    $jsArrDimAllStr = isl_substr($jsArrDimAllStr, 0, isl_strlen($jsArrDimAllStr)-1);
    foreach ($jsArrDimStrs as $key => $value) {
        $jsArrDimStrs[$key] = isl_substr($value, 0, isl_strlen($value)-1);
    }

    $jsArrAcctStr = isl_substr($jsArrAcctStr, 0, isl_strlen($jsArrAcctStr)-1);
    /** @noinspection PhpUndefinedVariableInspection */
    $jsArrAcctListStr = isl_substr($jsArrAcctListStr, 0, isl_strlen($jsArrAcctListStr) - 1);

    $jsAG = "<SCRIPT language='JavaScript'>";
    $jsAG .= " _acctgrp_map = [";
    $jsAG .= $jsArrStr."];\n";

    $jsAG .= " _grppurpose_map = ";
    $jsAG .= $jsacctGrpPurpose.";\n";

    $jsAG .= " _acctgrppurpose_map =";
    $jsAG .= $jsacctGrpMapWithPurpose.";\n";

    $jsAG .= " _acctgrp_dim_all_map = [";
    $jsAG .= $jsArrDimAllStr."];\n";

    foreach( $mapDimensionToDimensionClassID as $k => $v ) {
        $jsAG .= " ".$v['picker_map']." = [";
        $jsAG .= $jsArrDimStrs[$k]."];\n";
    }

    $jsAG .= " _acctgrp_acct_map = [";
    $jsAG .= $jsArrAcctStr."];\n";
    $jsAG .= " _acctgrp_acct_list_obj = {";
    $jsAG .= $jsArrAcctListStr."};\n";
    $jsAG .= "</SCRIPT>";

    echo $jsAG;
    echo $jsAGP;

    return $acctGrpUIMap;
}

function EditReportGeneratePageRowsStructure()
{
    EditReportGeneratePageRowsAndTotals_combo(true);
}

function EditReportGeneratePageRowsFormatting()
{
    EditReportGeneratePageRowsAndTotals_combo(false);
}

/**
 * @param bool $showRowStructure
 */
function EditReportGeneratePageRowsAndTotals_combo($showRowStructure)
{
    // this was until very late all on one page.

    //rows
    $_lineno                       = &Request::$r->_lineno;
    $_accountOrDimensionRows       = &Request::$r->_accountOrDimensionRows;
    $_always                       = &Request::$r->_always;
    $_attach_currency              = &Request::$r->_attach_currency;
    $_bot                          = &Request::$r->_bot;
    $_center_group_headers         = &Request::$r->_center_group_headers;
    $_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;
    $_empty_rows_before            = &Request::$r->_empty_rows_before;
    $_emptyrows                    = &Request::$r->_emptyrows;
    $_expandMembers                = &Request::$r->_expandMembers;
    $_ratiobaseGrp                 = &Request::$r->_ratiobaseGrp;
    $_rowasof                      = &Request::$r->_rowasof;
    $_rowExpandByAccountGroup      = &Request::$r->_rowExpandByAccountGroup;
    $_runtimeparams                = &Request::$r->_runtimeparams;
    $_pgbreak                      = &Request::$r->_pgbreak;
    $_topbotcol                    = &Request::$r->_topbotcol;
    $_underline_type               = &Request::$r->_underline_type;
    $_show_currency                = &Request::$r->_show_currency;
    $_skiph                        = &Request::$r->_skiph;
    $_skipt                        = &Request::$r->_skipt;
    $_skiptotalsline               = &Request::$r->_skiptotalsline;
    $_summary                      = &Request::$r->_summary;
    $_top                          = &Request::$r->_top;
    $_cumbal                       = &Request::$r->_cumbal;
    global $groups;
    global $kShowDetail;
    global $krowASOF;
    $text = TextHelper::getInstance(FinancialReportWizard::class);

    //totals
    global $kDelim;
    global $groupnames, $report;
    global $gLineMap, $gAccountGroupMap, $coloredBG;

    //Map will have accountgroupkey & lineno
    $lineNoMap = explode($kDelim, $_lineno);
    $lineNoMap = array_flip($lineNoMap);

    $summaryMap = array();
    if (isset($_summary) && $_summary != '') {
        $summary = explode($kDelim, $_summary);
        foreach ( $summary as $val ) {
            $summaryMap[] = $val;
        }
    }

    $_always = util_encode($_always);
    $alwaysMap = explode($kDelim, $_always);
    if (isset($_always) && $_always != '') {
        $always = explode($kDelim, $_always);
        foreach ( $always as $alway ) {
            $boolVal = explode("@@", $alway);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $alwaysMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $_expandMembers = util_encode($_expandMembers);
    $expMembersMap = array();
    if (isset($_expandMembers) && $_expandMembers != '') {
        $expMems = explode($kDelim, $_expandMembers);
        foreach ( $expMems as $val ) {
            $expMembersMap[] = $val;
        }
    }

    $_top = util_encode($_top);
    $topMap = array();
    if (isset($_top) && $_top != '') {
        $topMems = explode($kDelim, $_top);
        foreach ( $topMems as $val ) {
            $topMap[] = $val;
        }
    }

    $_bot = util_encode($_bot);
    $botMap = array();
    if (isset($_bot) && $_bot != '') {
        $botMems = explode($kDelim, $_bot);
        foreach ( $botMems as $val ) {
            $botMap[] = $val;
        }
    }

    $_topbotcol = util_encode($_topbotcol);
    $topbotcolMap = array();
    if (isset($_topbotcol) && $_topbotcol != '') {
        $topbotcolMems = explode($kDelim, $_topbotcol);
        //for ($i=0;$i<count($topbotcolMems); $i++) {
        foreach ( $topbotcolMems as $val ) {
            $topbotcolMap[] = $val;
        }
    }

    $rowExpandByAccountGroupMap = array();
    if (isset($_rowExpandByAccountGroup) && $_rowExpandByAccountGroup != '') {
        $rowExpandByAccountGroupMems = explode($kDelim, $_rowExpandByAccountGroup);
        //for ($i=0;$i<count($rowExpandByAccountGroupMems); $i++) {
        foreach ( $rowExpandByAccountGroupMems as $val ) {
            $rowExpandByAccountGroupMap[] = $val;
        }
    }

    $skiphMap = array();
    if (isset($_skiph) && $_skiph != '') {
        $skiph = explode($kDelim, $_skiph);
        foreach ( $skiph as $skph ) {
            $boolVal = explode("@@", $skph);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $skiphMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $pgbreakMap = array();
    if (isset($_pgbreak) && $_pgbreak != '') {
        $pgbreak = explode($kDelim, $_pgbreak);
        foreach ( $pgbreak as $pgbrk ) {
            $boolVal = explode("@@", $pgbrk);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $pgbreakMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $emptyrowsMap = array();
    if (isset($_emptyrows) && $_emptyrows != '') {
        $emptyrows = explode($kDelim, $_emptyrows);
        foreach ( $emptyrows as $emptyrow ) {
            $countVal = explode("@@", $emptyrow);
            if (!empty($countVal[1])) {
                $emptyrowsMap[$countVal[0]] = $countVal[1];
            }
        }
    }

    $empty_rows_beforeMap = array();
    if (isset($_empty_rows_before) && $_empty_rows_before != '') {
        $empty_rows_before = explode($kDelim, $_empty_rows_before);
        foreach ( $empty_rows_before as $empty_row_before ) {
            $empty_rows_beforecount = explode("@@", $empty_row_before);
            $empty_rows_beforeMap[$empty_rows_beforecount[0]] = $empty_rows_beforecount[1];
        }
    }

    $underline_typeMap = array();
    if (isset($_underline_type) && $_underline_type != '') {
        $underline_type = explode($kDelim, $_underline_type);
        foreach ( $underline_type as $underline_typ) {
            $underline_typecount = explode("@@", $underline_typ);
            $underline_typeMap[$underline_typecount[0]] = $underline_typecount[1];
        }
    }

    $urtotalMap = array(
            '1' => $text->GT('IA.NONE'),
            '2' => '1',
            '3' => '2'
    );

    $center_group_headersMap = array();
    if (isset($_center_group_headers) && $_center_group_headers != '') {
              $center_group_headers = explode($kDelim, $_center_group_headers);
        foreach ( $center_group_headers as $center_group_header ) {
            $boolVal = explode("@@", $center_group_header);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $center_group_headersMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $show_currencyMap = array();
    if (isset($_show_currency) && $_show_currency != '') {
              $show_currency = explode($kDelim, $_show_currency);
        foreach ( $show_currency as $show_curr ) {
            $boolVal = explode("@@", $show_curr);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $show_currencyMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $ertotalMap = array(
            '1' => '1',
            '2' => '2',
            '3' => '3',
            '4' => '4',
            '5' => '5',
            '6' => '6',
            '7' => '7',
            '8' => '8',
            '9' => '9'
    );

    $skiptotalslineMap = array();
    if (isset($_skiptotalsline) && $_skiptotalsline != '') {
        $skiptotalsline = explode($kDelim, $_skiptotalsline);
        foreach ( $skiptotalsline as $skiptotalline ) {
            $boolVal = explode("@@", $skiptotalline);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $skiptotalslineMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $skiptMap = array();
    if (isset($_skipt) && $_skipt != '') {
        $skipt = explode($kDelim, $_skipt);
        foreach ( $skipt as $skpt ) {
            $boolVal = explode("@@", $skpt);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $skiptMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $attach_currencyMap = array();
    if (isset($_attach_currency) && $_attach_currency != '') {
        $attach_currency = explode($kDelim, $_attach_currency);
        foreach ( $attach_currency as $attach_curr ) {
            $boolVal = explode("@@", $attach_curr);
            if (!empty($boolVal[1]) && $boolVal[1]!='F') {
                $attach_currencyMap[$boolVal[0]] = $boolVal[1];
            }
        }
    }

    $rowasof = array();
    if (isset($_rowasof) && $_rowasof != '') {
        $rowasof = explode($kDelim, $_rowasof);
    }

    $report = array(
        'listing' => 1,
        'columns' => array(),
        'groups' => $groupnames,
        'rows' => array()
    );


   // call InitReport only if there is no error yet
    if ( HasErrors() ) {
        include 'popuperror.phtml';
        exit();
    }

    InitReport($report);
    /** @noinspection PhpUnusedLocalVariableInspection */
    $mapDimensionToDimensionClassID = getDimensionToDimensionClassIDMap();

    $lines = array();
    foreach ( $groups as $group ) {
        //$group = $groups[$i];
        if ($group !== null) {
            ReportLines('', 'html', $group, '', $lines, false);
        }
    }

    // BUILD JS ARRAY MAP FOR ACCTRGROUP NAMES
    $acctGrpUIMap = GenerateDimensionAndAccountMaps();
    // BUILD JS ARRAY MAP FOR ACCTGROUP PATHS
    $jsAGP = "";
    if ($lines && $gLineMap) {
        $jsArrStr = '';
        foreach ($lines as $i => $line) {
            $path = str_replace('/', '_', $gLineMap[$i]);
            $jsArrStr .= "[\"$path\"]," ;
        }

        $jsArrStr = isl_substr($jsArrStr, 0, isl_strlen($jsArrStr)-1);

        $jsAGP = "<SCRIPT language='JavaScript'>";
        $jsAGP .= " _acctgrppath_map = [";
        $jsAGP .= $jsArrStr."];";
        $jsAGP .= "</SCRIPT>";
    }
    echo $jsAGP;

    $plus = IALayoutManager::getIAAppImagePath('reportcenter', 'new_report.png');
?>
            <div id='hide_table_contents_empty' class='dialogBodyHeader' style='width: 100%; display: none; margin-left: 30px; text-align: center'>
                <?EditReportHeaderSeperator();?>
                <div  id='empty_row_page_missing_content' style='background: white;'>
                        <?=$text->GT('IA.THIS_PAGE_IS_A_PLACEHOLDER_PAGE_FROM_HELP_SYSTEM_IS_MISSING')?>
            	        <br>
            	        <br>
            	        <div class='finwizButton_action2' tabindex=0 style='display: inline-block; background: white;' onclick='frw_eatClick(event);OpenPageGroup();'>
            	            <img src="<?=$plus?>" style='vertical-align: baseline; margin-bottom: -1px;margin-right:3px'>
            	            <span style='color: #3f668d'><?=$text->GT('IA.CREATE_REPORT_STRUCTURE')?></span>
            	        </div>
        	        </div>
        	        <div id='empty_row_page'  style='background: white; padding-bottom: 20px;'></div>
    	    </div>
            <div id='hide_table_contents'>

            <input type='hidden' name='addChangeAccountsGroups_STRINGS' id='addChangeAccountsGroups_STRINGS' value=''> <!-- used for adding groups. -->

            <?
            $rowtableLinesCount = count($lines);
            $rowLARGERTABLECOUNT = 750;
            $isLargeTable = $rowtableLinesCount > $rowLARGERTABLECOUNT;
            //$isLargeTable = true;

            echo "<script>document.ADD_IMMEDIATE_WAIT_UI != undefined;</script>";
            /** @noinspection PhpUndefinedVariableInspection */
            $botspace_details = ( $uaInfo['browser'] != 'IE') ? '2px' : '3px';

            $showAccountCol = ($_accountOrDimensionRows === 'dimension' && $_dimensionRowsExpandByAccount ==='T');
            $showDimensionCol = ($_accountOrDimensionRows !== 'dimension' && $_dimensionRowsExpandByAccount ==='T');
            $thFirstCOlSpan = ($showAccountCol || $showDimensionCol) ? 4 : 3;

            $tableHead = '<thead>';
            if ($showRowStructure) {
                $tableHead .= <<< EOT
                    <tr style='background: white'>
                         <th colspan='1' style='background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; '><div> </div></th>
                         <th colspan='$thFirstCOlSpan' style='background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; '><div> </div></th>
                    </tr>
EOT;
            } else  {
                $rowFormatting = $text->GT('IA.ROW_FORMATTING');
                $totalFormatting = $text->GT('IA.TOTAL_FORMATTING');
                $currencyText = $text->GT('IA.CURRENCY');
                $tableHead .= <<< EOT
                    <tr style='background: white'>
                         <th colspan='1' style='background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; '><div> </div></th>
                         <th colspan='1' style='background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; '><div> </div></th>
                         <th colspan='4' style='background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; '><div style='text-align: center;' div><span style='font-weight: 700'>$rowFormatting</span>&nbsp<span id='row_formatting'></span></div></th>
                         <th colspan='4' style='background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; '><div style='text-align: center;' ><span style='font-weight: 700'>$totalFormatting</span>&nbsp<span id='total_formatting'></span></div></th>
                         <th colspan='2' style='background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; '><div style='text-align: center;' ><span style='font-weight: 700'>$currencyText&nbsp</span><span id='GL_FinancialReports_RowsTab_Currency'></span></div></th>
                    </tr>
EOT;
            };
            if ($showRowStructure) {
                $addrowcomponenttext= ($_accountOrDimensionRows == 'account') ? $text->GT('IA.SELECT_ACCOUNT_GROUPS'): $text->GT('IA.SELECT_DIMENSION_STRUCTURES');
                $rowStructureLabel = $text->GT('IA.ROW_STRUCTURE');
                $tableHead .= <<< EOT

    				<tr $coloredBG >
    					<th  class='fixedCol fillrow' style='min-width: 280px; vertical-align: top; overflow: hidden;  text-align: left;  border-top: none; border-right: 1px solid #b0c6e0; font-weight: 700;'>
    					    <div>
    					        <DIV style='padding-left: 3px;padding-top: 6px'>
        					        &nbsp;$rowStructureLabel&nbsp;<span id='RowsTab_RowStructure'></span>
            					    <div class='spacerFixedColDiv' style='padding-bottom: 0px; width: 100%; overflow: hidden'>
            					        <div class='finwizButton_action2' tabindex=0 style="margin: 10px;  margin-bottom: 6px;; display: inline-block" onclick='frw_eatClick(event);OpenPageGroup();'>
                                            <!--<div style='display: inline-block;padding-top: 6px; overflow:hidden;width: 3px'>&nbsp;</div>-->
                					        <!--<img src="$plus" style='vertical-align: baseline; margin-bottom: -1px;margin-right:3px'>-->$addrowcomponenttext
            					        </div>
            					    </div>
        					    </DIV>
    					    </div>
    					 </th>
EOT;
            } else {
                $reportRowsLabel = $text->GT('IA.REPORT_ROWS');
                $tableHead .= <<< EOT

    				<tr $coloredBG >
    					<th  class='fixedCol fillrow' style='min-width: 280px; vertical-align: top; overflow: hidden;  text-align: left;  border-top: none; border-right: 1px solid #b0c6e0; font-weight: 700;'>
    					    <div>
    					        <DIV style='text-align: center; padding-left: 3px;padding-top: 6px'>
                                    $reportRowsLabel
        					    </DIV>
    					    </div>
    					 </th>
EOT;
            }
            if ($showRowStructure && $showAccountCol) {
                $accountGroupLabel = $text->GT('IA.ACCOUNT_GROUP');
                $setAccountGroup = $text->GT('IA.SET_ACCOUNT_GROUP');

                    $tableHead = $tableHead.<<< EOT
                        <th align="center" class='stretch fillrow'>
    					    <div>
        					    <div style='padding-right: 20px;text-align: left; display: inline-block;padding-top:4px; min-width:95px'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div>$accountGroupLabel&nbsp;<span id='RowsTab_ExpandBy'></span>&nbsp</div>
	                            <div class='all_none' style='position: absolute; bottom: $botspace_details; padding-left: 3px; font-weight: 100'><span onclick="javascript:OpenExpandByAccountDialog(undefined, this, 'account')">$setAccountGroup</span></div>
    					    </div>
    					</th>
EOT;
            }
            if ($showRowStructure && $showDimensionCol) {
                $filterByDimensionStructure = $text->GT('IA.FILTER_BY_DIMENSION_STRUCTURE');
                $setDimensionStructure = $text->GT('IA.SET_DIMENSION_STRUCTURE');
                $tableHead = $tableHead.<<< EOT
                            <th align="center" class='stretch fillrow' >
                                <div>
                                    <div style='padding-right: 20px;text-align: left; display: inline-block;padding-top:4px; min-width:110px'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div>$filterByDimensionStructure&nbsp;<span id='RowsTab_ExpandBy'></span>&nbsp</div>
                                    <div class='all_none' style='position: absolute; bottom: $botspace_details; padding-left: 3px; font-weight: 100'><span onclick="javascript:OpenExpandByAccountDialog(undefined, this, 'dimension')">$setDimensionStructure</span></div>
                                </div>
                            </th>
EOT;
            }

            if ($showRowStructure) {
                $text = TextHelper::getInstance(FinancialReportWizard::class);
                $rowstabhelpdetail = 'RowsTab_DetailLevel';
                $detailLevelLabel = $text->GT('IA.DETAIL_LEVEL');
                $setDetailLevelLavel = $text->GT('IA.SET_DETAIL_LEVEL');
                $tableHead = $tableHead.<<< EOT
    					<th align="center" class='stretch fillrow'>
    					    <div>
        					    <div style='padding-right: 6px;text-align: left; display: inline-block;padding-top:4px; min-width:150px'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div>$detailLevelLabel&nbsp;<span id='$rowstabhelpdetail'></span>&nbsp</div>
	                            <div class='all_none' style='position: absolute; bottom: $botspace_details; padding-left: 3px; font-weight: 100'><span onclick="javascript:setdetailonrowandtotalleafs()">$setDetailLevelLavel</span></div>
    					    </div>
    					</th>
EOT;
                if ($_accountOrDimensionRows ==='account') {
                    $basisForAmountsLabel = $text->GT('IA.BASIS_FOR_AMOUNTS');
                    $periodCumulativeLabel = $text->GT('IA.PERIOD_CUMULATIVE_AND_PERCENT');
                    $tableHead = $tableHead.<<< EOT

                            <th class='stretch  fillrow' >
                                <div>
                                    <div>

                                        <div style='display: inline-block;padding-top:4px; min-width:180px'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div>$basisForAmountsLabel</div>
                                        <br><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span style='font-weight: 100; white-space: nowrap'>$periodCumulativeLabel</span>

                                    </div>
                                </div>
                             </th>
EOT;
                };
                $alwaysDisplaylabel = $text->GT('IA.ALWAYS_DISPLAY');
                $noneLabel = $text->GT('IA.NONE');
                $allLabel = $text->GT('IA.ALL');
                $tableHead = $tableHead.<<< EOT
    					 <th class='seperatorRight fillrow' align="center" style='font-weight: 100'>
    					    <div>
                                <div style='padding-left: 3px;padding-right: 20px;text-align: left; display: inline-block;padding-top:4px;font-weight: 700'>
                                    <div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div>&nbsp;$alwaysDisplaylabel&nbsp;<span id='RowsTab_AlwaysDisplay'></span>&nbsp<br><br></div>

                                <div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'always','all', false, 'span')">$allLabel</span> | <span  onclick="javascript:doswitch(document.mf,'always','none', false, 'span')">$noneLabel</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div>
    					        </div>
    					    </div>
    					</th>

EOT;
            } else  {
                $text = TextHelper::getInstance(FinancialReportWizard::class);
                $detailLevelLabel = $text->GT('IA.DETAIL_LEVEL');
                $all =  $text->GT('IA.ALL');
                $none = $text->GT('IA.NONE');
                $hideHeading = $text->GT('IA.HIDE_HEADING');
                $centerHeading = $text->GT('IA.CENTER_HEADING');
                $blankLineAbove = $text->GT('IA.BLANK_LINES_ABOVE');
                $pageBreakAfter = $text->GT('IA.PAGE_BREAK_AFTER');
                $hideTotals = $text->GT('IA.HIDE_TOTALS');
                $showOnTotals = $text->GT('IA.SHOW_ON_TOTALS');
                $showOnDetails = $text->GT('IA.SHOW_ON_DETAILS');
                $underLineTotals = $text->GT('IA.UNDERLINE_TOTALS');
                $hideSeparatorAboveTotals = $text->GT('IA.HIDE_SEPARATOR_ABOVE_TOTALS');
                $blankSpaceAbove = $text->GT('IA.BLANK_SPACE_ABOVE');
                $tableHead = $tableHead.<<< EOT
                        <th align="center" class='seperatorRight stretch fillrow'>
     					    <div>
                                <div style='padding-right: 6px;padding-left: 6px;text-align: center; font-weight: 100; padding-top:4px; min-width:60px;'>$detailLevelLabel</div>
    					    </div>
    					</th>

    					<th class='seperatorLeft fillrow' style='font-weight: 100'>
    					    <div style='text-align: center'>
    					    <p style='white-space: break-spaces'>$hideHeading<br><br></p>
    						<div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'skiph','all', false, 'span')">$all</span> | <span  onclick="javascript:doswitch(document.mf,'skiph','none', false,'span')">$none</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div></div>
     					    </div>
    				   </th>
    					<th  class='fillrow' style='font-weight: 100'>
    					    <div style='text-align: center'>
    					    <input class="aboveHeader" type="hidden" value='header1'>
    					    <p style='white-space: break-spaces'>$centerHeading<br><br></p>
    						<div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'center_group_headers','all', false, 'span')">$all</span> | <span  onclick="javascript:doswitch(document.mf,'center_group_headers','none', false, 'span')">$none</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div></div>
    					    </div>
    					</th>
    					<th  class='fillrow' style='font-weight: 100; padding-left: 3px; padding-right: 3px;'>
        					<div style='text-align: center'>
        					<p style='white-space: break-spaces'>$blankLineAbove<br></p>
    					    <select style='visibility: hidden'><option>XX</select>
        					</div>
        				</th>
    					<th class='seperatorRight fillrow' style='font-weight: 100'>
    					    <div style='text-align: center'>
    					    <p style='white-space: break-spaces'>$pageBreakAfter<br></p>
    						<div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'pgbreak','all', false, 'span')">$all</span> | <span  onclick="javascript:doswitch(document.mf,'pgbreak','none', false, 'span')">$none</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div></div>
    					    </div>
    					</th>
 
     					<th class='seperatorLeft fillrow'  style='font-weight: 100'>
     					    <div style='text-align: center'>
     					    <p style='white-space: break-spaces'>$hideTotals<br><br></p>
         					<div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'skipt[','all', false, 'span')">$all</span> | <span  onclick="javascript:doswitch(document.mf,'skipt[','none', false, 'span')">$none</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div></div>
    					    </div>
         				</th>
     					
    					<th class='fillrow'  style='font-weight: 100' >
     					    <div style='text-align: center'>
     					    <p style='white-space: break-spaces'>$blankSpaceAbove<br></p>
    					    <select style='visibility: hidden'><option>XX</select>
    					    </div>
    					</th>
    					
     					<th class='fillrow'  style='font-weight: 100; padding-left: 3px; padding-right: 3px;'>
     					    <div style='text-align: center'>
     					    <input class="aboveHeader" type="hidden" value='header2'>
     					    <p style='white-space: break-spaces'>$hideSeparatorAboveTotals</p>
    						<div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'skiptotalsline','all', false, 'span')">$all</span> | <span  onclick="javascript:doswitch(document.mf,'skiptotalsline','none', false, 'span')">$none</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div></div>
     					    </div>
    				    </th>
    					
    					<th class='seperatorRight fillrow' style='font-weight: 100;'>
    					    <div style='text-align: center'>
    					    <p style='white-space: break-spaces'>$underLineTotals<br></p>
    					    <select style='visibility: hidden'><option>XXXXX</select>
    					    </div>
    					</th>
    					
    					<th class='seperatorLeft fillrow' style='font-weight: 100'>
    					    <div style='text-align: center'>
    					    <p style='white-space: break-spaces'>$showOnDetails<br></p>
     						<div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'show_currency','all', false, 'span')">$all</span> | <span  onclick="javascript:doswitch(document.mf,'show_currency','none', false, 'span')">$none</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div></div>
    					    </div>
     					</th>
    					
    					<th class='fillrow'  style='font-weight: 100'>
    					    <div style='text-align: center'>
    					    <input class="aboveHeader" type="hidden" value='header3'>
    					    <p style='white-space: break-spaces'>$showOnTotals</p>
     						<div class='all_none'><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div><span onclick="javascript:doswitch(document.mf,'attach_currency','all', false, 'span')">$all</span> | <span  onclick="javascript:doswitch(document.mf,'attach_currency','none', false, 'span')">$none</span><div style='display: inline-block;overflow:hidden;width: 3px'>&nbsp;</div></div>
    					    </div>
     					</th>
EOT;
            };
                    $tableHead .= '</tr></thead>';
?>

            <table class="fwtablewrapper"  style="display: table; background: white"><tr><td>
                <div id="hide_table_contents_simple_header" style='position: relative; overflow: hidden'>
                    <table id="wizardRowTableHeader"  style='position: relative' class="fwtable wizardRowTable">
        			     <? echo $tableHead;?>
        			</table>
        	    </div>

            <div id='hide_table_contents_simple'>
    			<table id="wizardRowTable" style="margin-bottom: 8px;"  class="fwtable wizardRowTable <?echo (($isLargeTable) ? "largeTable":"smallTable"); ?> ">
    			     <? echo $tableHead;?>
    		        <tbody>
        <?

        //get lineinfo split out.... ugh why can't I have real APIs. :-(
        $linesDetails = array();
        foreach ( $lines as $line ) {
            $inner=''; $indent=''; $indentLevel = 0;
            parseLineInfoBecauseNoAPI($line, $inner, $indent, $indentLevel);
            $linesDetails[] = array($inner, $indent, $indentLevel);
        };

        $ishidden = array(); // always show the first level.

            $ROWFILEDS_moveHiddenFiledsOutOfTable = array();
            $ROWSCRIPT_moveHiddenFiledsOutOfTable = array();
            $line = array();
            $lineCount = count($lines);
        for ( $i = 0; $i < $lineCount; $i++ ) {
            if (!$showRowStructure && !isset($gLineMap[$i])) {
                $ROWFILEDS_moveHiddenFiledsOutOfTable[] = "\n<input type='hidden' name=\"summary[$i]\" value=\"\"><input type=hidden name=\"emptyrows[$i]\" value=\"\">";

                //totals
                $ROWFILEDS_moveHiddenFiledsOutOfTable[] = "\n<input type='hidden' name=\"emptyrows[$i]\" value=\"\">";
                $ROWFILEDS_moveHiddenFiledsOutOfTable[] = "\n<input type='hidden' name=\"empty_rows_before[$i]\" value=\"\">";
                $ROWFILEDS_moveHiddenFiledsOutOfTable[] = "\n<input type='hidden' name=\"underline_type[$i]\" value=\"\">";
                continue;
            }
            $path = $gLineMap[$i];
            $key = $path;
            $nodekeys = str_replace('/', '_', $key);

            //To find Membertype
            $curGroup = isl_substr($key, isl_strrpos($key, '/')+1);
            //'A' is account group with glaccounts as members and 'T' is a category
            $isAccountMember = (in_array($gAccountGroupMap[$curGroup]['MEMBERTYPE'], array('A','T')))?'true':'false';
            $isComputation = ($gAccountGroupMap[$curGroup]['MEMBERTYPE'] == 'C') ? true : false;
            $isPercentType = ($gAccountGroupMap[$curGroup]['COMPUTATIONAL_EXPR']['DISPLAYAS'] == 'P') ? true : false;

            $kShowDetailMap = $kShowDetail;

            $summaryVal         = $summaryMap[$lineNoMap[$key]]?:'F';

            $show_currencyDisabled = '';
            $attach_currencyDisabled = '';

                $alwaysChecked                   = $alwaysMap[$key] ? 'defaultchecked=T' : '';
                $skiphChecked                    = $skiphMap[$key] ? 'defaultchecked=T' : '';
                $center_group_headersChecked     = $center_group_headersMap[$key] ? 'defaultchecked=T' : '';
                $pgbreakChecked                  = $pgbreakMap[$key] ? 'defaultchecked=T' : '';
                $skiptChecked                    = $skiptMap[$key] ? 'defaultchecked=T' : '';
                $skiptotalslineChecked           = $skiptotalslineMap[$key] ? 'defaultchecked=T' : '';

                $underline_typeVal                 = $underline_typeMap[$key];

                $cumbalChecked    =($_cumbal[$nodekeys] ? 'checked' : '');
                $expMemVal = $expMembersMap[$lineNoMap[$key]] ?: 'F';
                $topVal = $topMap[$lineNoMap[$key]] ?: '';
                $botVal = $botMap[$lineNoMap[$key]] ?: '';
                $topbotcolVal = $topbotcolMap[$lineNoMap[$key]] ?: '2';

                $rowExpandByAccountGroupVal = $rowExpandByAccountGroupMap[$lineNoMap[$key]] ?: '';

                // if underline_typeVal is zero, then the underlines haven't been defaulted.
            // the way we'll set the defaults is that the top level (no | in path) will get the double underline,
            // and the second level (one | in path) will get a single underline, all others get none.
            if ($underline_typeVal=='') {
                $thislevel = isl_substr_count($path, "/") - 1;
                switch($thislevel){
                case 0:
                    $underline_typeVal = '3'; // the number 3 is the double underline
                    break;
                case 1:
                    $underline_typeVal = '2'; // the number w maps to the single underline
                    break;
                default:
                    $underline_typeVal = '1'; // the number 1 maps to None
                    break;
                }
            }


            // For a computation account group the unit is % and the currency will never be showned
            // For this reason we will disable the checkbox Show Currency
            if($isComputation && $isPercentType) {
                $attach_currencyDisabled = 'display:none;';
                $attach_currencyChecked = '';
                $show_currencyDisabled = 'display:none;';
                $show_currencyChecked = '';
                /*
                } else if ($_firsttime=='1'){ // first time thru, lets check both top levels
                if (isl_substr_count($path,'/')<=2){
                $attach_currencyChecked = 'checked';
                 $show_currencyChecked = 'checked';
                } else {
                $attach_currencyChecked = '';
                 $show_currencyChecked = '';
                }
                */
            } else {
                    $attach_currencyChecked     = $attach_currencyMap[$key] ? 'defaultchecked=T' : '';
                    $show_currencyChecked       = $show_currencyMap[$key] ? 'defaultchecked=T' : '';
            }

            $rowasofsel = '';
            if ($isAccountMember=='true') {
                /** @noinspection PhpUndefinedVariableInspection */
                $rowasofsel = $rowasof[$lineNoMap[$key]];
                if ($rowasofsel == '' || $rowasofsel == 'default') { $rowasofsel= 'D';
                }
                $rowasofselText = ($krowASOF[$rowasofsel]!='')?$krowASOF[$rowasofsel]:$rowasofsel;
            }

            $emptyrowsVal      = ($emptyrowsMap[$key])?:'0';
            $empty_rows_beforeVal    = $empty_rows_beforeMap[$key];

            /** @noinspection PhpUnusedLocalVariableInspection */
            $indentLevel = 0;
            if ($showRowStructure) {
                $collaseExpandDefault = "<div style='visibility: hidden; display: inline-block'><pre class='rowsTotals'>[ ]</pre></div>";
            } else {
                $collaseExpandDefault = "<div style='visibility: hidden; display: inline-block'><pre class=''>   </pre></div>";
            }

            /** @noinspection PhpUnusedLocalVariableInspection */
            $collaseExpand = $collaseExpandDefault;
            $isExpanded = false;
            if ($summaryVal == 'F' || $summaryVal == 'SUBGRP') {
                $collaseExpand = "<div class='expanded fwtree'><pre class='rowsTotals'>[-]</pre></div>";
                $isExpanded = true;
            } else {
                $collaseExpand = "<div class='collapsed fwtree'><pre class='rowsTotals'>[+]</pre></div>";
            }

            if (!$showRowStructure) {
                $collaseExpand = "<div class='collapsed fwtree'><pre class=''>   </pre></div>";
            }

                $lineDetail = $linesDetails[$i];
            $inner = trim($lineDetail[0]);
            if (endsWith($inner, ' </b>')) {
                   $inner = str_replace(' </b>', '</b>', $inner);
            };
            $indent = $lineDetail[1];
            $indentLevel = $lineDetail[2];
            $isExpandable = true;
            if ($i+1 < $lineCount ) { // look at the next line
                $indentLevel2 = $linesDetails[$i+1][2];
                if ($indentLevel2 <= $indentLevel) {
                    $isExpandable = false;
                    $isExpanded = false;
                    $collaseExpand = $collaseExpandDefault;
                }
            } else {
                $isExpandable = false;
                $isExpanded = false;
                $collaseExpand = $collaseExpandDefault;
            }

            if (sizeof($ishidden) == 0) {
                array_push($ishidden, array('level' => 0, 'expanded' => $isExpanded, 'show' => true));
            } else {
                $curLevel = $ishidden[sizeof($ishidden) - 1]['level'];
                if($curLevel < $indentLevel) {
                    $showit = $ishidden[sizeof($ishidden) - 1]['expanded'] && $ishidden[sizeof($ishidden) - 1]['show'];
                    array_push($ishidden, array('level' => $indentLevel, 'expanded' => $isExpanded, 'show' => $showit));
                } else if ($curLevel >= $indentLevel) {
                    for($ix_a = sizeof($ishidden); $ix_a > 0; $ix_a--) {
                        $curLevel = $ishidden[$ix_a - 1]['level'];
                        if ($curLevel <= $indentLevel) {
                            $ishidden[$ix_a - 1]['expanded'] = $isExpanded;
                            break;
                        }
                        array_pop($ishidden);
                    }
                }
            }

            $hideRowAsOF = ($rowasofsel == '') ? 'display: none;' : '';
            $hideCumulativeText = ($cumbalChecked === 'checked') ? '' : 'display: none';

            $ratiobaseGrp =(( $_ratiobaseGrp[$nodekeys] == '')  || ($cumbalChecked === 'checked')) ? 'display: none;' : '';
            /** @noinspection PhpUnusedLocalVariableInspection */
            $cumbalCheckedDynamic = ( $cumbalChecked === 'checked') ? 'true' : 'false';

            $accountGroupID = $acctGrpUIMap[substr(strrchr($key, "/"), 1)];

            $ishiddenStyle = ($ishidden[sizeof($ishidden) - 1]['show'])  ? '' : 'style="display:none"';
            $rowIsHidden = ($ishidden[sizeof($ishidden) - 1]['show'])  ? false : true;
            $line[] =  "<tr ".$ishiddenStyle." ><td class='fixedCol' style='min-width: 175px; text-align: left; vertical-align: middle' bgcolor=\"#FFFFFF\">";
                $popup = '';
                $reportidText = $inner;

                $accountGroupIDNoQuotes =  json_decode('"'.isl_substr($accountGroupID, 1, strlen($accountGroupID) -2).'"');
            /** @noinspection PhpUndefinedVariableInspection */
            if (!$largeTable) {
                $displayIdName = $gAccountGroupMap[$gAccountGroupMap[$accountGroupIDNoQuotes]]["NAME"];

                $dimensionName = '';
                $displayOnReportAs = 'Name: '.isl_htmlspecialchars($displayIdName, ENT_QUOTES);
                if ($_accountOrDimensionRows ==='dimension' && $indentLevel==0) {
                    if(isset($gAccountGroupMap[$gAccountGroupMap[$accountGroupIDNoQuotes]]['MEMBERTYPE'])){
                        $dimInfo = getDimInfoByComponentVal($gAccountGroupMap[$gAccountGroupMap[$accountGroupIDNoQuotes]]['MEMBERTYPE']);
                        $entity = $dimInfo['entity'];
                        //same hack as RenderDimPickerNewUI
                        if ( $entity == 'departmentfilter' ) {
                            $entity = 'departmentgroup';
                        } else if ( $entity == 'gllocationfilter' ) {
                            $entity = 'locationgroup';
                        }
                        $entMgr = Globals::$g->gManagerFactory->getManager($entity);
                        $pluralPrintAs = $entMgr->GetPluralPrintAs();
                        $memType = $dimInfo['standard'] ? I18N::getSingleToken($pluralPrintAs) : $pluralPrintAs;
                    } else {
                        $memType = $gAccountGroupMap[$gAccountGroupMap[$accountGroupIDNoQuotes]]["MEMBERTYPEEXTNAME"];
                        if (isl_str_startswith($memType, 'Group of ')) {
                            $memType =  isl_substr($memType, 9);
                        }
                    }

                    $dimensionName = '&nbsp;('.isl_htmlspecialchars($text->GT($memType)).')';
                };

                if ($showRowStructure) {
                    $popup = RowsAndTotalsPopup($accountGroupID, ($indentLevel === 0), $i, $_accountOrDimensionRows ==='dimension');
                } else {
                    $popup = RowsAndTotalsPopup($accountGroupID, false, $i, $_accountOrDimensionRows ==='dimension');
                }

                $reportidText='<span style="cursor: default" title="'.$displayOnReportAs.'">'.$inner.$dimensionName.'</span>';
            }

                $line[] = $indent.$collaseExpand.$reportidText.$popup.'</td>';

                $showDimensionDetailExpandByInfo = ($isExpandable == false) ? "SHOWDIMDETAIL_EXPAND='T'" : "";
                $showDimensionDetailTopBoxInfo = ($_accountOrDimensionRows === 'dimension' && $isExpandable == false) ? "SHOWDIMDETAIL_TOPBOT='T'" : "";

            $editImage = IALayoutManager::getIAAppImagePath('reportcenter', 'reports_edit.png');

                $summaryOrDetailExpansionOverride = $text->GT('IA.SUMMARY');
            if ($_accountOrDimensionRows === 'account') {
                if ($summaryVal == 'T') {
                    $summaryOrDetailExpansionOverride = $text->GT('IA.DETAIL');
                }
            } else {
                if ($summaryVal == 'T') {
                    $summaryOrDetailExpansionOverride = $text->GT('IA.DETAIL');
                } else if ($expMemVal == 'T') {
                    $summaryOrDetailExpansionOverride = $text->GT('IA.DETAIL');
                }
            }

                $topbottext = '';
            if ($topVal == '0') {
                $topVal = '';
            }
            if ($botVal == '0') {
                $botVal = '';
            }
            if ($topVal != '' || $botVal != '') {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $summaryOrDetail = ''; //dont show this in the UI!
                $text = TextHelper::getInstance(FinancialReportWizard::class);
                if ($topVal != '') {
                    $topbottext = "[!!<br>!!]".$text->GT('IA.SHOW_TOP')." ".$topVal;
                } else if ($botVal != '') {
                    $topbottext = "[!!<br>!!]".$text->GT('IA.SHOW_BOTTOM')." ".$botVal;
                }
            } else {
                $topVal = '';
                $botVal = '';
            }

            $BuildSumValOptionsResult = BuildSumValOptions($kShowDetail, $summaryVal, $isExpandable, true, $summaryOrDetailExpansionOverride);

            $detailSetting =  $summaryVal;
            //$detailSetting =  $BuildSumValOptionsResult['options'];

            $detailSettingselectedtext =  $BuildSumValOptionsResult['htmlText'];

            $isExpandableString = ($isExpandable?'true':'false');
                $rowcomponenttype = (($_accountOrDimensionRows !== 'dimension')?'A':'D'); // account of dimension


            global $USE_FIX_FIRST_ROW;
            /** @noinspection PhpUnusedLocalVariableInspection */
            $fudgeFirstCol = ( $USE_FIX_FIRST_ROW === 'true') ? 'firstDataColLeftFudge' : 'firstDataColLeftFudge2';
                $ROWFILEDS_moveHiddenFiledsOutOfTable[] =  // TD1
                    "<input  type='hidden'  name='rowcomponenttype[$i]' value='".$rowcomponenttype."'>";
            if ($showRowStructure) {
                $ROWFILEDS_moveHiddenFiledsOutOfTable[] =  // TD1
                    "<span class='input_hidden'  type='hidden'  id='expandMembers[$i]' name='expandMembers[$i]'  ".$showDimensionDetailExpandByInfo." value='".$expMemVal."'></span>".
                    "<span class='input_hidden' type='hidden'  id='top[$i]' name='top[$i]' value='".$topVal."'></span>".
                    "<span class='input_hidden' type='hidden'  id='bot[$i]' name='bot[$i]' value='".$botVal."'></span>".
                    "<span class='input_hidden' type='hidden'  id='topbotcol[$i]' name='topbotcol[$i]' ".$showDimensionDetailTopBoxInfo." value='".$topbotcolVal."'></span>";
            }

            if ($showRowStructure) {
                $ROWFILEDS_moveHiddenFiledsOutOfTable[] = // TD2
                    "<span class='input_hidden'  type='hidden'   id='lineno[$i]' name='lineno[$i]' value='".$key."'></span>".
                    "<span class='input_hidden'  type='hidden'   id='indentLevel_$i' name='indentLevel_$i' value='".$indentLevel."'></span>".
                    "<span class='input_hidden'  type='hidden'   id='rowasof[$i]' name='rowasof[$i]' value=\"".$rowasofsel."\" size='25'></span>" .
                    "<span class='input_hidden'  type='hidden'   id='cumbalchanged[$i]' name='cumbalchanged[$i]' value=''></span>".
                    "<input type='checkbox' style='display:none;' id='_cumbal[$nodekeys]' name='_cumbal[$nodekeys]' value='".($i+1)."' " . $cumbalChecked . ">";
            }

            if (!$showRowStructure) {
                //Note: we 'might consider looking at $rowIsHidden and resetting defaults like we do later with the checkboxes!

                $ROWFILEDS_moveHiddenFiledsOutOfTable[] = //from other TDs...
                    "<input type=hidden id=\"emptyrows[$i]\" name=\"emptyrows[$i]\" value=\"".$key."@@".$emptyrowsVal."\">".
                    "<input type=hidden id=\"empty_rows_before[$i]\" name=\"empty_rows_before[$i]\" value=\"".$key."@@".$empty_rows_beforeVal."\">".
                    "<input type=hidden id=\"underline_type[$i]\" name=\"underline_type[$i]\" value=\"".$key."@@".$underline_typeVal."\">";
            }


                $smallTableHiddenVisibility1 = (!$isLargeTable || ($isLargeTable && $isExpanded)) ? "visibility: hidden;": "";
                $smallTableHiddenVisibility2 = (!$isLargeTable) ? "visibility: hidden;": "";
                $DEFAULT_ACCOUNTGROUP_HERE = $rowExpandByAccountGroupVal; //to do: need to get this value from the row!!!!
                $DEFAULT_ACCOUNTGROUP_HERE = isl_htmlspecialchars(trim($DEFAULT_ACCOUNTGROUP_HERE, '"'), ENT_QUOTES);
                $DEFAULT_ACCOUNTGROUP_HERE_TEXT_required = "<div style=\"color:#bb0000; font-weight:normal; text-align:center\">** ".$text->GT('IA.REQUIRED')." **</div>";
            if ($_accountOrDimensionRows !== 'dimension') {
                $DEFAULT_ACCOUNTGROUP_HERE_TEXT_required = "";
            }
                $DEFAULT_ACCOUNTGROUP_HERE_STATIC = $DEFAULT_ACCOUNTGROUP_HERE;
                $DEFAULT_ACCOUNTGROUP_HERE_TEXT = ($DEFAULT_ACCOUNTGROUP_HERE != '') ? $DEFAULT_ACCOUNTGROUP_HERE : $DEFAULT_ACCOUNTGROUP_HERE_TEXT_required;
                $excludeDimensionTypeFromExpandBy = ($_accountOrDimensionRows === 'account') ? 'undefined': isl_htmlspecialchars($gAccountGroupMap[$gAccountGroupMap[$accountGroupIDNoQuotes]]['MEMBERTYPEEXTNAME'], ENT_QUOTES);
                $rowNameJS = isl_htmlspecialchars(trim($accountGroupID, '"'), ENT_QUOTES);

            if ($showRowStructure && ($showAccountCol || $showDimensionCol)) {
                if($indentLevel == 0) {
                    $line[] =
                        "<td class='accountgroupcol' style='text-align: left; cursor: hand; cursor: pointer;' onclick='OpenExpandByAccountDialog($i, document.getElementById(\"rowExpandByAccountGroup_".$i."_id\"))' >".
                            "<div style='position:relative'>".
                            "<div id='expandbyaccountgroup_".$i."' class='expandbyaccountgroup'>".
                                "<span class='input_hidden' type='hidden' onchange='expandbyaccountgroup_".$i."_change(document.getElementById(\"rowExpandByAccountGroup_".$i."_id\"))'  id='rowExpandByAccountGroup_".$i."_id' name='rowExpandByAccountGroup[".$i."]' value='".$DEFAULT_ACCOUNTGROUP_HERE."'></span>"."<span id='rowExpandByAccountGroup_".$i."_span'>$DEFAULT_ACCOUNTGROUP_HERE_TEXT"."</span>".
                            "</div>".

                            "<div style=\"position: absolute; top: 0px; right: 0px;\"><img hspace='3' class=\"arrowUp computation_arrow dimdetailarrow\" style=\"$smallTableHiddenVisibility1 margin-top:-4px;\" src=\"" . $editImage . "\" alt=''  border='0' ></a></div>".
                            "</div>".
                            "</td>\n";

                    $ROWSCRIPT_moveHiddenFiledsOutOfTable[] =
                        "\n\n if (document.GLOBAL_INPUT_ACCOUNT_IDS === undefined) {\ndocument.GLOBAL_INPUT_ACCOUNT_IDS = [];\n};".
                            "\n document.GLOBAL_INPUT_ACCOUNT_IDS[document.GLOBAL_INPUT_ACCOUNT_IDS.length] = 'rowExpandByAccountGroup_".$i."_id';".
                            "\n function expandbyaccountgroup_".$i."_change(elm) {".
                            "\n if (elm.getAttribute('value') == '') {".
                                "\n jq('#rowExpandByAccountGroup_".$i."_span').html('".$DEFAULT_ACCOUNTGROUP_HERE_TEXT_required."'); \n".
                            "\n } else {\n".
                                "\n jq('#rowExpandByAccountGroup_".$i."_span').text(elm.getAttribute('value')); \n".
                            "}; \n".
                        "\n};\n\n";

                } else if ($showRowStructure) {
                    $line[] =
                        "<td class='accountgroupcol' style='cursor: default; ' >".
                            "<div style='position:relative'>".
                                "<span class='input_hidden show_input_hidden_span'  type='hidden' id='rowExpandByAccountGroup_".$i."_id' name='rowExpandByAccountGroup[".$i."]' onchange='var x = jq(document.getElementById(\"rowExpandByAccountGroup_".$i."_id\")); x.text(x.attr(\"value\"))' value='$DEFAULT_ACCOUNTGROUP_HERE_STATIC'>$DEFAULT_ACCOUNTGROUP_HERE_STATIC</span>".
                            "</div>".
                            "</td>\n";
                }
            }
            if ($showRowStructure) {
                $line[] =
                    "<td class='' style='text-align: left; cursor: hand; cursor: pointer;' onclick='OpenDetailDialog($i, document.getElementById(\"summary[$i]\"), $isExpandableString, \"$excludeDimensionTypeFromExpandBy\", !(jq(\"#dimensiondetail_$i\").css(\"visibility\")!=\"hidden\"), \"$rowNameJS\" ) ' >".
                        "<div style='position:relative'>".
                            "<div id='dimensiondetail_$i' class='dimensiondetail'".(($isExpanded) ? 'style="visibility:hidden;"' : '').">".
                            "<span class='input_hidden' type='hidden' id='summary[$i]' name='summary[$i]' value=".$detailSetting."></span>".
                            "<span class='detailSettingselectedtext'>".util_encode($detailSettingselectedtext.$topbottext, true)."</span>".
                        "</div>".

                            "<div style=\"position: absolute; top: 0px; right: 0px;\"><img hspace='3' class=\"arrowUp computation_arrow dimdetailarrow\" style=\"$smallTableHiddenVisibility1 margin-top:-4px;\" src=\"" . $editImage . "\" alt=''  border='0' ></a></div>".
                        "</div>".
                    "</td>\n";

                if ($_accountOrDimensionRows ==='account') {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $line[] =
                        "<td  class='stretch computation_arrow_container  ".$USE_FIX_FIRST_ROW."'  onclick=\"CreateRatioBasisInfo(".$i.",'".$nodekeys."','".$isAccountMember."', this);return false;\" style='cursor: hand; cursor: pointer; text-align: left; vertical-align: baseline'>".
                           "<div style='text-align: left;position: relative;cursor: hand; cursor: pointer; '>".
                               "<div style='text-align: left; cursor: hand; cursor: pointer; '>".
                                   "<a  style='outline: 0;position: absolute; right:0px; top: 2px;' href='#' onclick=\"CreateRatioBasisInfo(".$i.",'".$nodekeys."','".$isAccountMember."', this);return false;\" id='divRowPopup[".$i."]'  >".
                                   "<img hspace='3' class=\"arrowUp computation_arrow \" style=\"$smallTableHiddenVisibility2\" src=\"" . $editImage . "\" alt=''  border='0' ></a>".
                               "</div>".
                                "<span type='text' style='display: block;padding-left:2px; color: black; $hideRowAsOF' id='rowasofText[$i]' name='rowasofText[$i]' size='25'>".$rowasofselText."</span>" .
                           "</div>".
                           "<div style='text-align: left; cursor: hand; cursor: pointer; '>".
                               "<input type='text' style='$ratiobaseGrp border:0;cursor: hand; cursor: pointer; background: transparent' readonly id='_ratiobaseGrp[$nodekeys]' name='_ratiobaseGrp[$nodekeys]' value=\"".util_encode($_ratiobaseGrp[$nodekeys])."\" size='25' onchange=\"javascript:populateRatiobases('_ratiobaseGrp', this, '$nodekeys');\">" .
                               "<div id='cumulativeText_$nodekeys' style='$hideCumulativeText ;cursor: hand; cursor: pointer;padding-left:2px; color: #000'>".$text->GT('IA.CUMULATIVE')."</div>".
                           "</div>".
                       "</td>\n";
                } else {
                    $ROWFILEDS_moveHiddenFiledsOutOfTable[] =
                        "<span class='input_hidden'  type='hidden' id='_ratiobaseGrp[$nodekeys]' name='_ratiobaseGrp[$nodekeys]' value=\"".$_ratiobaseGrp[$nodekeys]."\"></span>";
                }

                if ($rowIsHidden) {
                    $alwaysChecked = '';
                }
                        $line[] ="<td class='seperatorLeft' >".
                        "<span class='input_show input_CB noborder' type='checkbox'  name='always[$i]' $alwaysChecked value='T'  key=\"".$key."\" ><span style='".(($alwaysChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span>"
                        ."</td>\n";
            } else  {
                if ($rowIsHidden) {
                    //let's clearout all the hidden formatting values.
                    $skiphChecked = '';
                    $center_group_headersChecked = '';


                    $pgbreakChecked = '';
                    $skiptChecked = '';

                    $skiptotalslineChecked = '';
                    $show_currencyChecked = '';
                    $attach_currencyChecked = '';

                    //$empty_rows_beforeVal = '';
                    //$underline_typeVal = ''; OK to preserve this one?
                    //$emptyrowsVal='';

                }

                /*
                $listOfDetailStrings = explode("\n", $detailSettingselectedtext.$topbottext);
                $OfDetailStringsLengths = array_map('strlen', $listOfDetailStrings);
                $listOfStringLengths = max($OfDetailStringsLengths);
                if (max($OfDetailStringsLengths) > 10 ) {
                   $titleTextDetail = 'title="'.$detailSettingselectedtext.$topbottext.'"';
                }
                */

                /** @noinspection PhpUndefinedVariableInspection */
                $line[] =
                   "<td class='seperatorRight detailSettingselectedtext'>".
                   "<span class='detailSettingselectedtext' $titleTextDetail>". util_encode($detailSettingselectedtext.$topbottext,true)."</span>".
                   "</td>";

                $line[] =
                   "<td><span class='input_show input_CB noborder' type='checkbox' name='skiph[$i]' $skiphChecked  value='T' key=\"".$key."\"><span style='".(($skiphChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span></td>\n".
                   "<td><span class='input_show input_CB noborder' type='checkbox' name='center_group_headers[$i]' value='T' key=\"".$key."\" $center_group_headersChecked ><span style='".(($center_group_headersChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span></td>\n";
                if (!$isLargeTable) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $line[] = "<td><select name=\"erows[$i]\" $disableMyBal onchange = \"javascript:SetEmptyRows(this.form, '$i', '$key');\"><option " . (( $emptyrowsVal == '' || $emptyrowsVal == '0') ? 'selected':'') . "value=\"0\">0</option>" . ListFinOptions($emptyrowsVal, $ertotalMap) . "</select></td>\n";
                } else {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $line[] = "<td><input  name=\"erows[$i]\" $disableMyBal style='text-align: center' size='1' maxlength='1' onchange = \"javascript:SetEmptyRows(this.form, '$i', '$key', 'input');\" value='$emptyrowsVal'></td>\n";
                };
                $line[] =
                   "<td class='seperatorRight'><span class='input_show input_CB noborder' type='checkbox' class='' name='pgbreak[$i]' value='T' key=\"".$key."\" $pgbreakChecked ><span style='".(($pgbreakChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span></td>\n".
                   "<td class='seperatorLeft' align='center' ><span class='input_show input_CB noborder' type='checkbox'  name='skipt[$i]' value='T' key=\"".$key."\"  $skiptChecked     ><span style='".(($skiptChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span></td>\n";
                if (!$isLargeTable) {
                    $line[] = "<td style='text-align: center' ><select name=\"erowstotal[$i]\" onchange = \"javascript:SetEmpty_Rows_Before(this.form, '$i', '$key', 'input');\"><option ".(($emptyrowsVal == '' || $emptyrowsVal =='0') ? 'selected':'')."value=\"0\">0</option>" . ListFinOptions($empty_rows_beforeVal,  $ertotalMap). "</select></td>\n";
                } else {
                    $line[] = "<td style='text-align: center' ><input  name=\"erowstotal[$i]\" style='text-align: center' size='1' maxlength='1' onchange = \"javascript:SetEmpty_Rows_Before(this.form, '$i', '$key');\" value='$empty_rows_beforeVal'></td>\n";
                };
                $line[] =
                   "<td align='center' ><span class='input_show input_CB noborder' type='checkbox'   name='skiptotalsline[$i]'   value='T'  key=\"".$key."\"  $skiptotalslineChecked ><span style='".(($skiptotalslineChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span></td>\n";
                if (!$isLargeTable) {
                    $line[] = "<td class='seperatorRight' style='text-align: center' ><select name=\"urowstotal[$i]\" onchange = \"javascript:SetUnderline_Type(this.form, '$i', '$key', 'input');\">" . ListFinOptions($underline_typeVal,  $urtotalMap, false, true). "</select></td>\n";
                } else {
                    $displayUnderlineVal = ($underline_typeVal > 1) ? ($underline_typeVal - 1) : '';
                    $line[] = "<td class='seperatorRight' style='text-align: center' ><input name=\"urowstotal[$i]\" style='text-align: center' size='1' maxlength='1' onchange = \"javascript:SetUnderline_Type(this.form, '$i', '$key');\" value='$displayUnderlineVal'></td>\n";
                };

                $line[] =
                   "<td class='seperatorLeft'><span class='input_show input_CB noborder' type='checkbox'  name='show_currency[$i]' value='T' key=\"".$key."\" $show_currencyChecked  ><span style='$show_currencyDisabled ".(($show_currencyChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span></td>\n".
                   "<td align='center' ><span class='input_show input_CB noborder' type='checkbox'    name='attach_currency[$i]'   value='T' key=\"".$key."\"  $attach_currencyChecked ><span style='$attach_currencyDisabled ".(($attach_currencyChecked!='') ? '' : 'visibility: hidden')."'>&#x2713;</span></span></td>\n";
            }
                  $line[] = "</tr>\n";

            //echo $line;
        }

                echo join('', $line);
        ?>

    			</tbody>
    			</table>

            </div>
    		    </td></tr></table>
            </div>


            <?  //Move the hidden fields out of the table, may aid in performance when the table gets huge
                echo '<div style="display:none">';
                echo join('', $ROWFILEDS_moveHiddenFiledsOutOfTable);
                echo '</div>';
            ?>

    		<script>

            <?
                echo join('', $ROWSCRIPT_moveHiddenFiledsOutOfTable);
            ?>

            function hidedimensionDetailClick($clicked) {
    		    var $labelTR = $clicked.closest("TR");
    		    var expanded = $labelTR.find('td:first-child div.expanded').length;
        		var index = $labelTR.index();
                var trBody = jq("#wizardRowTable>tbody>tr")[index];
                var $dimDetail = jq(trBody).find("td>div>div.dimensiondetail");
                $dimDetail[0].style.visibility = expanded ? 'hidden' : '';
                var largeTable = <?echo ($isLargeTable ? "true" : "false");?>;
                if (largeTable) {
                    $img = jq(trBody).find("td img.dimdetailarrow") ;
                    $img[0].style.visibility = (expanded ? 'hidden' : 'visible');
                }
                var $inputDetail = jq(trBody).find("td>div>div.dimensiondetail>span[name^=summary]");
                var $visibleText = jq(trBody).find("td>div>div.dimensiondetail>span.detailSettingselectedtext");
                //$inputDetail[0].selectedIndex = expanded ? 0 : 1;
                if (expanded) {
                    //jq($inputDetail[0].options[0]).text("Details");
                    $inputDetail.attr('value', "F");
                    $visibleText.html("Details");

               } else {
                    //jq($inputDetail[0].options[0]).text("Summary");
                    $inputDetail.attr('value', "T");
                    $visibleText.html("Summary");
                }
                hideSubs("wizardRowTable", $labelTR, index, !expanded);

                if  (!expanded) recalculateTableColumnHeight();
            }

    		var oWizardTableController;
    		var oWizardTable;
            function assignClickEvent() {
                if (<? echo $showRowStructure ? 'true' : 'false';?>) {
                    jq(".EditReportWizard").click(function (event) {

                        var $source = jq(event.srcElement? event.srcElement : event.target);
                        if ($source.parent().hasClass('fwtree')) {
                            $source = $source.parent();
                            if ($source.hasClass('expanded')) {
                                $source.removeClass('expanded');
                                $source.addClass('collapsed');
                                $source.html("<pre  class='rowsTotals'>[+]</pre>");
                            } else {
                                $source.removeClass('collapsed');
                                $source.addClass('expanded');
                                $source.html("<pre class='rowsTotals'>[-]</pre>");
                            };
                            hidedimensionDetailClick($source);
    //                        recalculateTableColumnHeight();

                            fixHeaderWidthsSimple(undefined, undefined);
                            fixHeaderPositionSimple();
                        }
                    });
                };
            };

            var haltRecalculate = false;
            function recalculateTableColumnHeight() {
                if (!$USE_FIX_FIRST_ROW) return;


                if (undefined ||!haltRecalculate) { // why undefined???? confused
        	        var colLabelRows = jq('.EditReportWizard .DTFC_LeftBodyWrapper .fwtable>TBODY>TR');
        	        jq('#wizardRowTable>TBODY>TR').each(function(index, elm){
        	            colLabelRows[index].style.height = jq(elm).height() + 'px';
                    }); //fwtable

                    fixScrollHeight();
                    fixHeaderWidthsSimple();
                    }
            }


            <?if (count($lines) == 0) {?>
	            FORMWILLHANDLESTRETCH = false;
            <?
} else {?>
                FORMWILLHANDLESTRETCH = true;
                FieldHelpModal_IGNORE = true;
                <?
}?>


            jq(document).ready( function () {
                jq('span.input_CB').each(function() {
                    this.checked = this.getAttribute('defaultchecked') === 'T';
                    this.onclick = function () {
                        this.checked = !this.checked;
                        jq(this).find("span").css('visibility', this.checked ? 'visible' : 'hidden')
                        //debugger;
                    }
                });

                try {
        	        ROWSANDTOTALS = true;

                    if (document.GLOBAL_INIT_COMBOS != undefined) {
                        for (xx=0;xx<document.GLOBAL_INIT_COMBOS.length;xx++) {
                            document.GLOBAL_INIT_COMBOS[xx]();//call all the init functions in this array
                        }
                        document.GLOBAL_INIT_COMBOS=undefined;
                    }

                    <?if (count($lines) == 0) {?>
            	        jq('#hide_table_contents').hide();
            	        jq('#hide_table_contents_empty').css('display', 'inline-block');
                    <?
}?>

                      jq(window).bind('resize', function () {
                          fixScrollHeightSimple();
                          fixHeaderWidthsSimple();
                          fixHeaderPositionSimple();
                      } );


                      jq('#hide_table_contents_simple').scroll(function () {
                          fixHeaderWidthsSimple();
                          fixHeaderPositionSimple();
                      } );




                      initDivHeadersSimple();
                      fixHeaderPositionSimple();
                      fixHeaderWidthsSimple();
                      fixScrollHeightSimple();
                      assignClickEvent();

                      if (<?echo !$isLargeTable? "true" : "false";?>) jq('.wizardRowTable>tbody>tr').hover(
                           function() {
                               var $thisTR = jq(this);
                               $thisTR.children().addClass('rowisselected');
                               $thisTR.find('.computation_arrow').each(function() {
                                   var $dimDetail = jq(this).closest("TD").find("div.dimensiondetail");
                                   //if ($dimDetail.length && $dimDetail.css('visibility') == 'hidden' ) {
                                   //    jq(this).css('visibility', 'hidden');
                                   //} else {
                                   jq(this).css('visibility', 'visible');
                                   //}
                            	});
                        	},
                    	    function() {
                                var $thisTR = jq(this);
                                $thisTR.children().removeClass('rowisselected')
                        	    $thisTR.find('.computation_arrow').css('visibility', 'hidden');
                    	    });


            	   <?if (count($lines) == 0) {?>
            	         jq('.EditReportWizard').css('overflow', 'auto');
            	         // OpenPageGroup();
            	         if (FieldHelpModal_iconContentDefined("empty_row_page")) {
            	            jq("#empty_row_page_missing_content").hide();
            	            FieldHelpModal_showContentInline("empty_row_page");
            	            jq("#hide_table_contents_empty").css("margin","0");
                     	    jq("#hide_table_contents_empty").css("display","block");
                     	    jq("#empty_row_page").height( jq("#empty_row_page").find(".modalBodyContent").height() + 'px');
                          }
                    <?
}?>

                    //Have to rebind stuff after the copy that happened above;
                    //new IAPopupMenu(jq('.accountGroupPopup'));
         	        jq(".EditReportWizardOuterContent").css("background-image", "");

         	        if (FieldHelpModal_IGNORE) {
             	        window.setTimeout(function() {
                            try {
                                FieldHelpModalInit(jq('#wizardRowTableHeader'));
                            } catch (e) {
                                console.log(e.description);
                            }
              	        }, 500);
         	        } else {

                    }

        	    } catch (e) {
                    jq(".fwtablewrapper").css("display", "table");
                    jq(".EditReportWizardContent").css("visibility", "visible");
         	        jq(".EditReportWizardOuterContent").css("background-image", "");
                    console.log(e.description);
                }

        	 } );

        	 </script>

            <?
            $runtimeparams = isl_htmlspecialchars($_runtimeparams);
            $fs_runtimeparams = FieldSize('reportinfo.runtimeparams');?>
            <input type='hidden' name=".runtimeparams" value="<?=$runtimeparams?>" size=50 maxlength="<?=$fs_runtimeparams?>">

        <?
        //To Draw Div Section
        if (count($lines)) {
            DrawRowDivSection($krowASOF);
            /** @noinspection PhpUndefinedVariableInspection */
            DrawDetailDialog($kShowDetailMap);
        }
        if ($showRowStructure) {
                DrawPageGroupsSection($groups);
                echo '<!-- ***** -->';
                DrawAccountComponentOnAllRowsDialog('allrows', $_accountOrDimensionRows);
                echo '<!-- ***** -->';
                DrawAccountComponentOnAllRowsDialog('onerow', $_accountOrDimensionRows);
                echo '<!-- ***** -->';
        }

}

function EditReportGeneratePageComputations()
{
            global $gLineMap;
            global $groupnames;
            global $kDelim, $coloredBG, $kColumnOperation;
            global $fromCSTool;
            global $groups;

            $text = TextHelper::getInstance(FinancialReportWizard::class);

            $_applyrepfilteronly  = &Request::$r->_applyrepfilteronly;
            $_colCompValue        = &Request::$r->_colCompValue;
            $_columnOperation     = &Request::$r->_columnOperation;
            $_currentcompcolindex = &Request::$r->_currentcompcolindex;
            $_operandGrp          = &Request::$r->_operandGrp;
            $_totalcompcol        = &Request::$r->_totalcompcol;
            $_totalcompcol = util_encode($_totalcompcol);
            $_summary             = &Request::$r->_summary;

            // WELCOME TO COMPUTATION TAB

            GenerateDimensionAndAccountMaps();

            $report = array(
                'listing' => 1,
                'columns' => array(),
                'groups' => $groupnames,
                'rows' => array()
            );

            // call InitReport only if there is no error yet
            if ( HasErrors() ) {
                include 'popuperror.phtml';
                exit();
            }

            InitReport($report);

            $lines = array();


            foreach ( $groups as $group ) {
                //$group = $groups[$i];
                if ($group !== null) {
                    ReportLines('', 'html', $group, '', $lines, false);
                }
            }
            $_lineno = &Request::$r->_lineno;
            //Map will have accountgroupkey & lineno
            $lineNoMap = explode($kDelim, $_lineno);
            $lineNoMap = array_flip($lineNoMap);


            $summaryMap = array();
            if (isset($_summary) && $_summary != '') {
                $summary = explode($kDelim, $_summary);
                foreach ( $summary as $val ) {
                    $summaryMap[] = $val;
                }
            }
                        // build computation map
            $kASOF = array( 'P' => 'for period', 'B' => 'start of period', 'E' => 'end of period' );
            $columnOperationMap = array();
    /** @noinspection PhpUnusedLocalVariableInspection */
    $resetPaths = false;
            if ( isset($_columnOperation) && $_columnOperation != '' ) {
                for ($j=0;$j<$_totalcompcol;$j++) {
                    $columnOperation[$j] = explode($kDelim, $_columnOperation[$j]);
                    foreach ( $columnOperation[$j] as $opandoper) {
                        $coCount = explode("@@", $opandoper);

                        // paths have been changed.. reset all computation paths
                        if(!in_array($coCount[0], $gLineMap)) {
                            /** @noinspection PhpUnusedLocalVariableInspection */
                            $resetPaths = true;
                        }

                        $columnOperationMap[$j][$coCount[0]] = $coCount[1];
                    }
                }
            }

            if ( isset($_operandGrp) && $_operandGrp != '' ) {
                for ($j=0;$j<$_totalcompcol;$j++) {
                    $operandGrp[$j] = explode($kDelim, $_operandGrp[$j]);
                    foreach ( $operandGrp[$j] as $opandoper) {
                        $coCount = explode("@@", $opandoper);
                        $operandGrpMap[$j][$coCount[0]]['OPERAND'] = $coCount[1];
                        $operandGrpMap[$j][$coCount[0]]['OPERANDTYPE'] = $coCount[2];
                        $operandGrpMap[$j][$coCount[0]]['OPERANDASOF'] = $coCount[3];
                    }

                }
            }


            // BUILD JS ARRAY MAP FOR ACCTGROUP PATHS
            if ( $lines && $gLineMap ) {

                $jsArrStr = '';
                /** @noinspection PhpUnusedLocalVariableInspection */
                $jsAGP = '';

                foreach ( $lines as $i => $val ) {
                    $path = str_replace('/', '_', $gLineMap[$i]);
                    $jsArrStr .= "[\"$path\"]," ;
                }

                $jsArrStr = isl_substr($jsArrStr, 0, isl_strlen($jsArrStr)-1);

                $jsAGP = "<SCRIPT language='JavaScript'>";
                $jsAGP .= " _acctgrppath_map = [";
                $jsAGP .= $jsArrStr."];";
                $jsAGP .= "</SCRIPT>";

            }

    /** @noinspection PhpUndefinedVariableInspection */
    echo $jsAG;
    /** @noinspection PhpUndefinedVariableInspection */
    echo $jsAGP;

        ?>
    	<input type=hidden name=".currentcompcolindex" value="<?=($_currentcompcolindex ? util_encode($_currentcompcolindex): 0 )?>">
    	<input type=hidden name=".totalcompcol" value="<?=$_totalcompcol?>">
        <?for ($j=0;$j<$_totalcompcol;$j++) {?>
    		<input type=hidden name="_columnOperation[<?=$j?>]" value="<?=util_encode($_columnOperation[$j])?>">
    		<input type=hidden name="_operandGrp[<?=$j?>]" value="<?=util_encode($_operandGrp[$j])?>">
        <?
}?>

    	<input type=hidden name=".isadd" value="">
    	<input type=hidden name=".isabove" value="">
    	<input type=hidden name=".isremove" value="">
    	<input type=hidden name=".opComputation" value="">
    	<input type=hidden name=".colCompValue" value="<?= util_encode($_colCompValue) ?>">
    	<STYLE  type="text/css">

    		FONT.customCtrl1	{ font-size: 6pt; font-weight:bold; color:#003366; }
    		FONT.customCtrl2	{ font-size: 8pt; font-weight:bold; color:#003366; }

    		select.Menu{
    		  font-size:   8pt;
    		  font-style:  normal;
    		}
    	</STYLE>


            <div id='hide_table_contents_empty' class='dialogBodyHeader' style='width: 100%; display: none; margin-left: 30px; text-align: center'>
                <?EditReportHeaderSeperator();?>
                <div  id='empty_computations_missing_content' style='margin: 20px; background: white;'>
                        <?=$text->GT('IA.DEFINE_THE_COMPUTATIONS_YOU_WANT_TO_USE_FOR_THIS_REPORT')?>
            	        <br>
            	        <br>
            	        <div class='finwizButton_action2' tabindex=0 style='display: inline-block;' onclick='frw_eatClick(event);addCompColumns(0, false);'>
            	            <img src="<?= /** @noinspection PhpUndefinedVariableInspection */
                            $plus?>" style='vertical-align: baseline; margin-bottom: -1px;margin-right:3px'>
            	            <?=$text->GT('IA.ADD_COMPUTATIONS')?>
            	        </div>
        	        </div>
        	        <div id='empty_computations_page'  style='background: white; padding-bottom: 20px;'></div>
    	    </div>

            <div id='hide_table_contents' style='background: white; <? echo ($_totalcompcol == 0) ? 'display: none;' : '';?>'>
                <?printRowWiseCompCtrlHeaderDropdowns()?>

    <?
              $tableHead =  '<thead>';
    /** @noinspection PhpUnusedLocalVariableInspection */
    $tableHeadReal = '<thead>';
              $tableHeadReal = $tableHead.'<tr style="background: white">'.
                         '<th colspan="1" style="height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "><div> </div></th>'.
                         '<th colspan="'.$_totalcompcol.'" style="text-align: right; height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "><div><table style="border: none; width: 100%"><tr><td style="border: none; text-align: right; padding-top: 3px;padding-right: 2px;">'.
                         '<input name=".applyrepfilteronly" value="F" '.(($_applyrepfilteronly == 'F')?'checked':'').' onchange="document.mf.elements[\'.applyrepfilteronly\'].value = (this.checked) ? \'F\' : \'T\'" type="checkbox"></td><td style="width: 1px; border: none; white-space: nowrap; vertical-align: middle;color:#355E8B"> '.$text->GT('IA.APPLY_ACCOUNT_GROUP_FILTERS_TO_COMPUTATIONS').' <span class="GL_FinancialReports_ComputationsTab_ApplyAccountGroupFilters cellhelp"></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th></tr></table></div></th>';
              $tableHead = $tableHead.'<tr style="background: white">'.
                         '<th colspan="1" style="height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "><div> </div></th>'.
                         '<th colspan="'.$_totalcompcol.'" style="text-align: right; height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "><div><table style="border: none; width: 100%"><tr><td style="border: none; text-align: right; padding-top: 3px;padding-right: 2px;">'.
                         '</th></tr></table></div></th>';
              $tableHead = $tableHead.'</tr>';
              $tableHeadReal = $tableHeadReal.'</tr>';


                $tableHead = $tableHead.'<tr '.$coloredBG.'><th style="min-width: 180px; text-align: left"><div style="padding-bottom: 3px;">&nbsp;'.$text->GT('IA.COMPUTATIONS_ON_GROUPS').'<span class="compuation_on_groups"></span>&nbsp;&nbsp;</div></th>';
                $tableHeadReal = $tableHeadReal.'<tr style="border-bottom-style: solid; border-bottom-width: thin;"'.$coloredBG.'><th style="min-width: 180px; text-align: left"><div style="padding-bottom: 3px;">&nbsp;'.$text->GT('IA.NAME').'*<span class="compuation_on_groups"></span>&nbsp;&nbsp;</div></th>';

    for ($i=0;$i<$_totalcompcol;$i++) {
        /** @noinspection PhpUndefinedVariableInspection */
        $tableHead = $tableHead . printRowWiseCompCtrl($i, $lastIndex);
        $tableHeadReal = $tableHeadReal.printRowWiseCompCtrl($i, $lastIndex, GetCompInputCtrl('_compName', $i));
    }

    $tableHeadReal = $tableHeadReal.'<tr '.$coloredBG.'><th style="min-width: 180px; text-align: left"><div style="padding-bottom: 3px;">&nbsp;'.$text->GT('IA.DESCRIPTION').'&nbsp;&nbsp;</div></th>';

    for ($i=0;$i<$_totalcompcol;$i++) {
        /** @noinspection PhpUndefinedVariableInspection */
        $tableHeadReal = $tableHeadReal.printRowWiseCompCtrl($i, $lastIndex, GetCompInputCtrl('_compDescription', $i));
    }
              $tableHead = $tableHead.'</tr></thead>';
              $tableHeadReal = $tableHeadReal.'</tr></thead>';
                ?>
			<table class="fwtablewrapper"  style="background: white"><tr><td>

             <div id="hide_table_contents_simple_header" style='position: relative; overflow: hidden'>
                <table id="wizardComputationTableHeader"   style='position: relative' class="fwtable wizardComputationTable">
        			     <? echo $tableHeadReal;?>
        		</table>

        	 </div>

            <div id='hide_table_contents_simple'>
                <table id="wizardComputationTable" class="fwtable wizardComputationTable" >
    			     <? echo $tableHead;?>

    	    	<tbody>
        <?

        //get lineinfo split out.... ugh why can't I have real APIs. :-(
        $linesDetails = array();
        foreach ( $lines as $val ) {
            $inner=''; $indent=''; $indentLevel = 0;
            parseLineInfoBecauseNoAPI($val, $inner, $indent, $indentLevel);
            $linesDetails[] = array($inner, $indent, $indentLevel);
        };



        $ishidden = array(); // always show the first level.
        foreach ( $lines as $i => $val ) {
            if (!isset($gLineMap[$i])) {
                continue;
            }
            /** @noinspection PhpUnusedLocalVariableInspection */
            $line =  str_replace('<tr bgcolor="#FFFFFF">', '<tr>', $val);
            $path = $gLineMap[$i];
            $key = $path;
            $nodekeys = str_replace('/', '_', $key);

            /** @noinspection PhpUnusedLocalVariableInspection */
            $indentLevel = 0;
            $collaseExpandDefault = "<div style='visibility: hidden; display: inline-block'><pre  class='rowsTotals'>[ ]</pre></div>";
            /** @noinspection PhpUnusedLocalVariableInspection */
            $collaseExpand = $collaseExpandDefault;
            $isExpanded = false;
            $summaryVal = $summaryMap[$lineNoMap[$key]]?:'F';
            if ($summaryVal == 'F' || $summaryVal == 'SUBGRP') {
                $collaseExpand = "<div style='visibility: hidden' class='expanded'><pre  class='rowsTotals'>[-]</pre></div>";
                $isExpanded = true;
            } else  {
                $collaseExpand = "<div style='visibility: hidden' class='collapsed'><pre  class='rowsTotals'>[+]</pre></div>";
            }

            $lineDetail = $linesDetails[$i];
            $inner = $lineDetail[0];
            $indent = $lineDetail[1];
            $indentLevel = $lineDetail[2];
            /** @noinspection PhpUnusedLocalVariableInspection */
            $isExpandable = true;
            if ($i+1 < count($lines)) { // look at the next line
                $indentLevel2 = $linesDetails[$i+1][2];
                if ($indentLevel2 <= $indentLevel) {
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $isExpandable = false;
                    $isExpanded = false;
                    $collaseExpand = $collaseExpandDefault;
                }
            } else {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $isExpandable = false;
                $isExpanded = false;
                $collaseExpand = $collaseExpandDefault;
            }

            if (sizeof($ishidden) == 0) {
                array_push($ishidden, array('level' => 0, 'expanded' => $isExpanded, 'show'=> true));
            } else {
                $curLevel = $ishidden[sizeof($ishidden) - 1]['level'];
                if($curLevel < $indentLevel) {
                    $showit = $ishidden[sizeof($ishidden) - 1]['expanded'] && $ishidden[sizeof($ishidden) - 1]['show'];
                    array_push($ishidden, array('level' => $indentLevel, 'expanded' => $isExpanded, 'show' => $showit));
                } else if ($curLevel >= $indentLevel) {
                    for($ix_a = sizeof($ishidden); $ix_a > 0; $ix_a--) {
                        $curLevel = $ishidden[$ix_a - 1]['level'];
                        if ($curLevel <= $indentLevel) {
                            $ishidden[$ix_a - 1]['expanded'] = $isExpanded;
                            break;
                        }
                        array_pop($ishidden);
                    }
                }
            }

            $ishiddenStyle = ($ishidden[sizeof($ishidden) - 1]['show'])  ? '' : 'style="display:none"';
            $line =  "<tr ".$ishiddenStyle."><td  class='fixedCol' style='text-align: left; vertical-align: middle;' bgcolor=\"#FFFFFF\">";
            $line .= $indent.$collaseExpand.$inner.'</td>';

            $editImage = IALayoutManager::getIAAppImagePath('reportcenter', 'reports_edit.png');

            $opMap = array(
                        'A' => 'IA.ADD',
                        'S'=> 'IA.SUBTRACT',
                        'SF'=> 'IA.SUBTRACT_FROM',
                        'M'=> 'IA.MULTIPLY',
                        'D'=> 'IA.DIVIDE',
                        'DI'=> 'IA.DIVIDE_INTO',
                        );

            for ($j=0;$j<$_totalcompcol;$j++) {
                $columnOperationVal = isl_trim($columnOperationMap[$j][$key]);
                /** @noinspection PhpUndefinedVariableInspection */
                $_helper__operandGrp_value = $operandGrpMap[$j][$key]['OPERAND'];
                $_helper__operandGrp_style =  ($columnOperationVal=='' && $_helper__operandGrp_value != '') ? ';visibility: hidden; ' : '' ;
                $_helper__selColumnOperation_text =  ($columnOperationVal=='' && $_helper__operandGrp_value != '') ? 'IA.HIDE_REPORT_CELL': $opMap[$columnOperationVal];

                //$columnHelperOperationVal = ($columnOperationVal)?$kColumnOperation[$columnOperationVal]:'';
                    $jsonInner = isl_htmlspecialchars(trim(json_encode($inner), '"'), ENT_QUOTES);
                $line .= "<td class='forceWhite' style='text-align: left; cursor: hand; cursor: pointer;'>".
                "<input type=hidden class='hidden_Operation' name='.hidcolumnOperation[".$j."][".$nodekeys."]' value='".$key."@@".$columnOperationVal."'>".
                "<input type=hidden name='_hidoperandGrp[".$j."][".$nodekeys."]' value='".$key."@@".$operandGrpMap[$j][$key]['OPERAND']."@@".$operandGrpMap[$j][$key]['OPERANDTYPE']."@@".$operandGrpMap[$j][$key]['OPERANDASOF']."'>".
                    "<input type=hidden id='indentLevel_$i' name='indentLevel_$i' value='".$indentLevel."'>".
                "<div class='computation_arrow_container' style='position: relative; padding-right: 15px; cursor: hand; cursor: pointer;' onclick='CreateComputationInfo(".$j.",\"$nodekeys\", \"$jsonInner\", this);return false;'>".
                 "<a style='outline: 0;position: absolute; right:0px; top: 2px;' href='#' onclick='CreateComputationInfo(".$j.",\"$nodekeys\", \"$jsonInner\", this);return false;' id='divCompPopup[".$j."][".$nodekeys."]'  >".
                 "<img hspace='3'  class=\"arrowUp computation_arrow\" style=\"outline: 0;visibility: hidden\" src=\"" . $editImage ."\" alt=''  border='0' ></a>".
                 "<span class ='_helper__selColumnOperation' id='_helper__selColumnOperation[".$j."][".$nodekeys."]'>".$text->GT($_helper__selColumnOperation_text ?: "")."</span>".
                 "<input type=text size=30 style='border:0; $_helper__operandGrp_style; cursor: hand; cursor: pointer;background: transparent' name='_helper__operandGrp[".$j."][".$nodekeys."]' value='".$_helper__operandGrp_value."' readonly>".
                "</div>".
                "</td>\n";
            }
            $line .= "</tr>\n";

            echo $line;
        }
        ?>
     		    </tbody>
    			</table>
    		</td></tr></table>

    		</div>


    		<script>
            //hide subgroups

            //This might be replaced with images.
    		var opMap = {
                    'A' : 'IA.ADD',
                    'S': 'IA.SUBTRACT',
                    'SF': 'IA.SUBTRACT_FROM',
                    'M': 'IA.MULTIPLY',
                    'D': 'IA.DIVIDE',
                    'DI': 'IA.DIVIDE_INTO'
    	    		};
            function set_helper__selColumnOperation($textField) {
                var hiddenVal = $textField.parent().parent().find('.hidden_Operation').val();
                var a = hiddenVal.split(localDelim);
                var op = '';
                if (a && a.length) {
                    op = a[a.length-1];
                }

                op = opMap[op] ? GT(opMap[op]) : '';

                if (op == '' && $textField.parent().parent().find('INPUT[name^="_helper__operandGrp"]').val() != '') {
                    op = GT('IA.HIDE_REPORT_CELL');
                    $textField.parent().parent().find('INPUT[name^="_helper__operandGrp"]').css('visibility','hidden');
                } else {
                    $textField.parent().parent().find('INPUT[name^="_helper__operandGrp"]').css('visibility','visible');
                    op = op + ' ';
                }

                $textField.text(op + " ");
            }

    		jq(document).ready( function () {
        	    try {
        	        FORMWILLHANDLESTRETCH = true;

                        initDivHeadersSimple();
                        fixHeaderPositionSimple();
                        fixHeaderWidthsSimple();
                        fixScrollHeightSimple();

                        jq('#hide_table_contents_simple').scroll(function () {
                            fixHeaderWidthsSimple();
                            fixHeaderPositionSimple();
                         });

                        jq(window).bind('resize', function () {
                            fixScrollHeightSimple();
                            fixHeaderWidthsSimple();
                            fixHeaderPositionSimple();
                         } );

                        jq('.wizardComputationTable>tbody>tr').hover(
                        	function() {
                            	var $thisTR =  jq(this);
                            	$thisTR.children().addClass('rowisselected');
                        	    $thisTR.find('.computation_arrow').css('visibility', 'visible');
                    	    },
                    	    function() {
                            	var $thisTR =  jq(this);
                            	$thisTR.children().removeClass('rowisselected')
                        	    $thisTR.find('.computation_arrow').css('visibility', 'hidden');
                            });


            	    FieldHelpModal_IGNORE = false;
                    <?
                    if ($_totalcompcol == 0) {?>
                            FieldHelpModal_IGNORE = true;
                            jq('.EditReportWizard').css('overflow', 'auto');
                            // OpenPageGroup();
                            if (FieldHelpModal_iconContentDefined("empty_computations_page")) {
                               jq("#empty_computations_missing_content").hide();
                               FieldHelpModal_showContentInline("empty_computations_page");
                               jq("#hide_table_contents_empty").css("margin","0");
                                jq("#hide_table_contents_empty").css("display","block");
                                jq("#empty_computations_page").height( jq("#empty_computations_page").find(".modalBodyContent").height() + 'px');
                                } else {
                               jq("#empty_computations_missing_content").show();
                               jq("#hide_table_contents_empty").css("margin","0");
                                jq("#hide_table_contents_empty").css("display","block");
                               }
                            <?
                    }?>

             	    jq(".EditReportWizardOuterContent").css("background-image", "");

         	        if (!FieldHelpModal_IGNORE) {
             	        window.setTimeout(function() {
                            FieldHelpModalInit(jq('#wizardComputationTableHeader'));
                	        }, 500);
         	        }



                } catch (e) {
        	        console.log(e);
         	        jq(".EditReportWizardOuterContent").css("background-image", "");
         	    }

        	 } );
        	</script>

        <?
        //To Draw Div Section
        if (count($lines)) {
            DrawCompDivSection($kColumnOperation, $kASOF, $fromCSTool);
        }
}

function EditReportGeneratePageNotations()
{
    global $gLineMap;
    global $groupnames;
    global $kDelim, $coloredBG;
    global $groups;

    $text = TextHelper::getInstance(FinancialReportWizard::class);

    $_colNotationValue = &Request::$r->_colNotationValue;
    $_currentnotationcolindex = &Request::$r->_currentnotationcolindex;
    $_totalnotationcol = &Request::$r->_totalnotationcol;
    $_totalnotationcol = util_encode($_totalnotationcol);
    $_summary = &Request::$r->_summary;

    // WELCOME TO COMPUTATION TAB

    GenerateDimensionAndAccountMaps();

    $report = [
        'listing' => 1,
        'columns' => [],
        'groups'  => $groupnames,
        'rows'    => [],
    ];

    // call InitReport only if there is no error yet
    if ( HasErrors() ) {
        include 'popuperror.phtml';
        exit();
    }

    InitReport($report);

    $lines = [];

    foreach ( $groups as $group ) {
        //$group = $groups[$i];
        if ( $group !== null ) {
            ReportLines('', 'html', $group, '', $lines, false);
        }
    }
    $_lineno = &Request::$r->_lineno;
    //Map will have accountgroupkey & lineno
    $lineNoMap = explode($kDelim, $_lineno);
    $lineNoMap = array_flip($lineNoMap);

    $summaryMap = [];
    if ( isset($_summary) && $_summary != '' ) {
        $summary = explode($kDelim, $_summary);
        foreach ( $summary as $val ) {
            $summaryMap[] = $val;
        }
    }

    // BUILD JS ARRAY MAP FOR ACCTGROUP PATHS
    if ( $lines && $gLineMap ) {
        $jsArrStr = '';
        /** @noinspection PhpUnusedLocalVariableInspection */
        $jsAGP = '';

        foreach ( $lines as $i => $val ) {
            $path = str_replace('/', '_', $gLineMap[$i]);
            $jsArrStr .= "[\"$path\"],";
        }

        $jsArrStr = isl_substr($jsArrStr, 0, isl_strlen($jsArrStr) - 1);

        $jsAGP = "<SCRIPT language='JavaScript'>";
        $jsAGP .= " _acctgrppath_map = [";
        $jsAGP .= $jsArrStr . "];";
        $jsAGP .= "</SCRIPT>";
    }

    /** @noinspection PhpUndefinedVariableInspection */
    echo $jsAG;
    /** @noinspection PhpUndefinedVariableInspection */
    echo $jsAGP;

    ?>
    <input type=hidden name=".currentnotationcolindex"
           value="<?= (int) $_currentnotationcolindex ?>">
    <input type=hidden name=".totalnotationcol" value="<?= (int) $_totalnotationcol ?>">
    <input type=hidden name=".isadd" value="">
    <input type=hidden name=".isabove" value="">
    <input type=hidden name=".isremove" value="">
    <input type=hidden name=".opNotation" value="">
    <input type=hidden name=".colNotationValue" value="<?= util_encode($_colNotationValue) ?>">
    <STYLE type="text/css">

        FONT.customCtrl1 {
            font-size: 6pt;
            font-weight: bold;
            color: #003366;
        }

        FONT.customCtrl2 {
            font-size: 8pt;
            font-weight: bold;
            color: #003366;
        }

        select.Menu {
            font-size: 8pt;
            font-style: normal;
        }
    </STYLE>


    <div id='hide_table_contents_empty' class='dialogBodyHeader'
         style='width: 100%; display: none; margin-left: 30px; text-align: center'>
        <? EditReportHeaderSeperator(); ?>
        <div id='empty_computations_missing_content' style='margin: 20px; background: white;'>
            <?=$text->GT('IA.DEFINE_THE_NOTATIONS_YOU_WANT_TO_USE_FOR_THIS_REPO')?>
            <br>
            <br>
            <div class='finwizButton_action2' tabindex=0 style='display: inline-block;'
                 onclick='frw_eatClick(event);addNotationColumns(0, false);'>
                <img src="<?= /** @noinspection PhpUndefinedVariableInspection */
                $plus ?>" style='vertical-align: baseline; margin-bottom: -1px;margin-right:3px'>
                <?=$text->GT('IA.ADD_NOTATIONS')?>
            </div>
        </div>
        <div id='empty_computations_page' style='background: white; padding-bottom: 20px;'></div>
    </div>

    <div id='hide_table_contents'
         style='background: white; <? echo ( $_totalnotationcol == 0 ) ? 'display: none;' : ''; ?>'>
        <? printRowWiseNotationCtrlHeaderDropdowns() ?>

        <?
        $tableHead = '<thead>';
        /** @noinspection PhpUnusedLocalVariableInspection */
        $tableHeadReal = '<thead>';
        $tableHeadReal = $tableHead . '<tr style="background: white">' .
                         '<th colspan="1" style="height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "><div> </div></th>'
                         .
                         '<th colspan="' . $_totalnotationcol
                         . '" style="text-align: right; height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "></th>';
        $tableHead = $tableHead . '<tr style="background: white">' .
                     '<th colspan="1" style="height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "><div> </div></th>'
                     .
                     '<th colspan="' . $_totalnotationcol
                     . '" style="text-align: right; height: 0px; background: white; ;  border: none; border-bottom: 1px solid #b0c6e0; "><div><table style="border: none; width: 100%"><tr><td style="border: none; text-align: right; padding-top: 3px;padding-right: 2px;">'
                     .
                     '</th></tr></table></div></th>';
        $tableHead = $tableHead . '</tr>';
        $tableHeadReal = $tableHeadReal . '</tr>';

        $tableHead = $tableHead . '<tr ' . $coloredBG
                     . '><th style="min-width: 180px; text-align: left"><div style="padding-bottom: 3px;">&nbsp;Computations on groups<span class="compuation_on_groups"></span>&nbsp;&nbsp;</div></th>';
        $tableHeadReal =
            $tableHeadReal . '<tr style="border-bottom-style: solid; border-bottom-width: thin;"' . $coloredBG
            . '><th style="min-width: 180px; text-align: left"><div style="padding-bottom: 3px;">&nbsp;'. $text->GT('IA.NAME') . '<span class="compuation_on_groups">*</span>&nbsp;&nbsp;</div></th>';

        for ( $i = 0; $i < $_totalnotationcol; $i++ ) {
            /** @noinspection PhpUndefinedVariableInspection */
            $tableHead = $tableHead . printRowWiseCompCtrl($i, $lastIndex);
            $tableHeadReal =
                $tableHeadReal . printRowWiseCompCtrl($i, $lastIndex, GetCompInputCtrl('_notationName', $i));
        }

        $tableHeadReal = $tableHeadReal . '<tr ' . $coloredBG
                         . '><th style="min-width: 180px; text-align: left"><div style="padding-bottom: 3px;">&nbsp;'.$text->GT('IA.DESCRIPTION').'&nbsp;&nbsp;</div></th>';

        for ( $i = 0; $i < $_totalnotationcol; $i++ ) {
            /** @noinspection PhpUndefinedVariableInspection */
            $tableHeadReal =
                $tableHeadReal . printRowWiseCompCtrl($i, $lastIndex, GetCompInputCtrl('_notationDescription', $i));
        }
        $tableHead = $tableHead . '</tr></thead>';
        $tableHeadReal = $tableHeadReal . '</tr></thead>';
        ?>
        <table class="fwtablewrapper" style="background: white">
            <tr>
                <td>

                    <div id="hide_table_contents_simple_header" style='position: relative; overflow: hidden'>
                        <table id="wizardComputationTableHeader" style='position: relative'
                               class="fwtable wizardComputationTable">
                            <? echo $tableHeadReal; ?>
                        </table>

                    </div>

                    <div id='hide_table_contents_simple'>
                        <table id="wizardComputationTable" class="fwtable wizardComputationTable">
                            <? echo $tableHead; ?>

                            <tbody>
                            <?

                            //get lineinfo split out.... ugh why can't I have real APIs. :-(
                            $linesDetails = [];
                            foreach ( $lines as $val ) {
                                $inner = '';
                                $indent = '';
                                $indentLevel = 0;
                                parseLineInfoBecauseNoAPI($val, $inner, $indent, $indentLevel);
                                $linesDetails[] = [ $inner, $indent, $indentLevel ];
                            };

                            $ishidden = []; // always show the first level.
                            foreach ( $lines as $i => $val ) {
                                if ( ! isset($gLineMap[$i]) ) {
                                    continue;
                                }
                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $line = str_replace('<tr bgcolor="#FFFFFF">', '<tr>', $val);
                                $path = $gLineMap[$i];
                                $key = $path;
                                $nodekeys = str_replace('/', '_', $key);

                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $indentLevel = 0;
                                $collaseExpandDefault =
                                    "<div style='visibility: hidden; display: inline-block'><pre  class='rowsTotals'>[ ]</pre></div>";
                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $collaseExpand = $collaseExpandDefault;
                                $isExpanded = false;
                                $summaryVal = $summaryMap[$lineNoMap[$key]] ? : 'F';
                                if ( $summaryVal == 'F' || $summaryVal == 'SUBGRP' ) {
                                    $collaseExpand =
                                        "<div style='visibility: hidden' class='expanded'><pre  class='rowsTotals'>[-]</pre></div>";
                                    $isExpanded = true;
                                } else {
                                    $collaseExpand =
                                        "<div style='visibility: hidden' class='collapsed'><pre  class='rowsTotals'>[+]</pre></div>";
                                }

                                $lineDetail = $linesDetails[$i];
                                $inner = $lineDetail[0];
                                $indent = $lineDetail[1];
                                $indentLevel = $lineDetail[2];
                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $isExpandable = true;
                                if ( $i + 1 < count($lines) ) { // look at the next line
                                    $indentLevel2 = $linesDetails[$i + 1][2];
                                    if ( $indentLevel2 <= $indentLevel ) {
                                        /** @noinspection PhpUnusedLocalVariableInspection */
                                        $isExpandable = false;
                                        $isExpanded = false;
                                        $collaseExpand = $collaseExpandDefault;
                                    }
                                } else {
                                    /** @noinspection PhpUnusedLocalVariableInspection */
                                    $isExpandable = false;
                                    $isExpanded = false;
                                    $collaseExpand = $collaseExpandDefault;
                                }

                                if ( sizeof($ishidden) == 0 ) {
                                    array_push($ishidden, [ 'level' => 0, 'expanded' => $isExpanded, 'show' => true ]);
                                } else {
                                    $curLevel = $ishidden[sizeof($ishidden) - 1]['level'];
                                    if ( $curLevel < $indentLevel ) {
                                        $showit = $ishidden[sizeof($ishidden) - 1]['expanded']
                                                  && $ishidden[sizeof($ishidden) - 1]['show'];
                                        array_push($ishidden, [ 'level' => $indentLevel, 'expanded' => $isExpanded,
                                                                'show'  => $showit ]);
                                    } else if ( $curLevel >= $indentLevel ) {
                                        for ( $ix_a = sizeof($ishidden); $ix_a > 0; $ix_a-- ) {
                                            $curLevel = $ishidden[$ix_a - 1]['level'];
                                            if ( $curLevel <= $indentLevel ) {
                                                $ishidden[$ix_a - 1]['expanded'] = $isExpanded;
                                                break;
                                            }
                                            array_pop($ishidden);
                                        }
                                    }
                                }

                                $ishiddenStyle =
                                    ( $ishidden[sizeof($ishidden) - 1]['show'] ) ? '' : 'style="display:none"';
                                $line = "<tr " . $ishiddenStyle
                                        . "><td  class='fixedCol' style='text-align: left; vertical-align: middle;' bgcolor=\"#FFFFFF\">";
                                $line .= $indent . $collaseExpand . $inner . '</td>';
                                $notationAndGrp = Request::$r->_notationAndGrp ?: [];
                                for ( $j = 0; $j < $_totalnotationcol; $j++ ) {
                                    // col + node path
                                    $notationKey = "{$j}{$kDelim}{$nodekeys}";
                                    $value = $notationAndGrp[$notationKey] ?? '';
                                    $line .= "<td class='forceWhite' style='text-align: left; cursor: hand; cursor: pointer;'>"
                                             .
                                             "<input type=hidden id='indentLevel_$i' name='indentLevel_$i' value='"
                                             . $indentLevel . "'>" .
                                             "<input name=\".notationAndGrp[{$notationKey}]\" type='text' maxlength='80' 
                style='width: 100%; border: none; background: transparent; outline: none;' value=\"{$value}\" data-notationindex='{$j}'>" .
                                             "</td>\n";
                                }
                                $line .= "</tr>\n";

                                echo $line;
                            }
                            ?>
                            </tbody>
                        </table>
                </td>
            </tr>
        </table>

    </div>


    <script>
        jq(document)
            .ready(function() {
                try {
                    FORMWILLHANDLESTRETCH = true;
                    jq(".EditReportWizard").css('overflow', 'hidden');
                    initDivHeadersSimple();
                    fixHeaderPositionSimple();
                    fixHeaderWidthsSimple();
                    fixScrollHeightSimple();

                    jq('#hide_table_contents_simple')
                        .scroll(function() {
                            fixHeaderWidthsSimple();
                            fixHeaderPositionSimple();
                        });

                    jq(window)
                        .bind('resize', function() {
                            fixScrollHeightSimple();
                            fixHeaderWidthsSimple();
                            fixHeaderPositionSimple();
                        });

                    jq('.wizardComputationTable>tbody>tr')
                        .hover(
                            function() {
                                var $thisTR = jq(this);
                                $thisTR.children()
                                    .addClass('rowisselected');
                                $thisTR.find('.computation_arrow')
                                    .css('visibility', 'visible');
                            },
                            function() {
                                var $thisTR = jq(this);
                                $thisTR.children()
                                    .removeClass('rowisselected');
                                $thisTR.find('.computation_arrow')
                                    .css('visibility', 'hidden');
                            });

                    FieldHelpModal_IGNORE = false;
                    <?
                    if ($_totalnotationcol == 0) {?>
                    FieldHelpModal_IGNORE = true;
                    jq('.EditReportWizard')
                        .css('overflow', 'auto');
                    // OpenPageGroup();
                    if ( FieldHelpModal_iconContentDefined('empty_computations_page') ) {
                        jq('#empty_computations_missing_content')
                            .hide();
                        FieldHelpModal_showContentInline('empty_computations_page');
                        jq('#hide_table_contents_empty')
                            .css('margin', '0');
                        jq('#hide_table_contents_empty')
                            .css('display', 'block');
                        jq('#empty_computations_page')
                            .height(jq('#empty_computations_page')
                                        .find('.modalBodyContent')
                                        .height() + 'px');
                    } else {
                        jq('#empty_computations_missing_content')
                            .show();
                        jq('#hide_table_contents_empty')
                            .css('margin', '0');
                        jq('#hide_table_contents_empty')
                            .css('display', 'block');
                    }
                    <?
                    }?>

                    jq('.EditReportWizardOuterContent')
                        .css('background-image', '');

                    if ( !FieldHelpModal_IGNORE ) {
                        window.setTimeout(function() {
                            FieldHelpModalInit(jq('#wizardComputationTableHeader'));
                        }, 500);
                    }

                } catch ( e ) {
                    console.log(e);
                    jq('.EditReportWizardOuterContent')
                        .css('background-image', '');
                }

            });
    </script>

    <?
}

function EditReportGeneratePageColumns()
{
    global $kcolASOF;
    global $kDelim, $kMeasures, $coloredBG, $kSummaryOp, $fromCSTool;
    global $report, $companyBooks, $adjBooks, $_columnTabRows;
    global $budgetMap, $kCompMap, $kCompareBy, $kPrecisions, $kNotationMap;

    $_aboveThresholdColor    = &Request::$r->_aboveThresholdColor;
    $_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
    $_belowThresholdColor    = &Request::$r->_belowThresholdColor;
    $_colAccountGrp          = &Request::$r->_colAccountGrp;
    $_colasof                = &Request::$r->_colasof;
    $_bot                    = &Request::$r->_bot;
    $_colAttributeField  = &Request::$r->_colAttributeField;
    $_colAttributeObject = &Request::$r->_colAttributeObject;
    $_colBudValue        = &Request::$r->_colBudValue;
    $_colCompValue       = &Request::$r->_colCompValue;
    $_colNotationValue       = &Request::$r->_colNotationValue;
    $_colhdr1            = &Request::$r->_colhdr1;
    $_colhdr2            = &Request::$r->_colhdr2;
    $_colhide            = &Request::$r->_colhide;
    $_colPeriod          = &Request::$r->_colPeriod;
    $_colPeriodOffset    = &Request::$r->_colPeriodOffset;
    $_colPeriodOffsetBy  = &Request::$r->_colPeriodOffsetBy;
    $_colPrecision       = &Request::$r->_colPrecision;
    $_colreportingMethod = &Request::$r->_colreportingMethod;
    $_colShowAs          = &Request::$r->_colShowAs;
    $_colTitle           = &Request::$r->_colTitle;
    $_colValueType       = &Request::$r->_colValueType;
    $_compareby          = &Request::$r->_compareby;
    $_compOnCol          = &Request::$r->_compOnCol;
    $_conditionWithMessage = &Request::$r->_conditionWithMessage;
    $_currentcolindex      = &Request::$r->_currentcolindex;
    $_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;
    $_enableColorScale             = &Request::$r->_enableColorScale;
    $_isadd              = &Request::$r->_isadd;
    $_isremove           = &Request::$r->_isremove;
    $_isabove            = &Request::$r->_isabove;
    $_indicatorType      = &Request::$r->_indicatorType;
    $_rightlefthide      = &Request::$r->_rightlefthide;
    $_showSparkline      = &Request::$r->_showSparkline;
    $_topbotcol          = &Request::$r->_topbotcol;
    $_trendIncreaseClr   = &Request::$r->_trendIncreaseClr;
    $_trendType          = &Request::$r->_trendType;
    $_summaryStr         = &Request::$r->_summaryStr;
    $_switchColsTable    = &Request::$r->_switchColsTable;
    $_threshold          = &Request::$r->_threshold;
    $_thresholdType      = &Request::$r->_thresholdType;
    $_top                = &Request::$r->_top;
    $colasof             = &Request::$r->colasof;
    $colhdr1             = &Request::$r->colhdr1;
    $colhdr2             = &Request::$r->colhdr2;
    $colreportingMethod  = &Request::$r->colreportingMethod;

    // WELCOME TO COLUMNS TAB

    if ( isset($_compareby) && $_compareby != '' ) {
        $compareby     = explode($kDelim, $_compareby);
    }
    if ( isset($_rightlefthide) && $_rightlefthide != '' ) {
        $rightlefthide     = explode($kDelim, $_rightlefthide);
    }
    if ( isset($_summaryStr) && $_summaryStr != '' ) {
        $summaryStr = explode($kDelim, $_summaryStr);
    }
    if ( isset($_colhdr1) && $_colhdr1 != '' ) {
        $colhdr1         = explode($kDelim, $_colhdr1);
    }
    if ( isset($_colhdr2) && $_colhdr2 != '' ) {
        $colhdr2         = explode($kDelim, $_colhdr2);
    }
    if ( isset($_colasof) && $_colasof != '' ) {
        $colasof         = explode($kDelim, $_colasof);
    }
    if ( isset($_colhide) && $_colhide != '' ) {
        $colhide         = explode($kDelim, $_colhide);
    }

    if ( isset($_colPeriod) && $_colPeriod != '' ) {
        $colPeriod    = explode($kDelim, $_colPeriod);
    }
    if ( isset($_colPeriodOffset) && $_colPeriodOffset != '' ) {
        $colPeriodOffset    = explode($kDelim, $_colPeriodOffset);
    }
    if ( isset($_colPeriodOffsetBy) && $_colPeriodOffsetBy != '' ) {
        $colPeriodOffsetBy    = explode($kDelim, $_colPeriodOffsetBy);
    }
    if ( isset($_colValueType) && $_colValueType != '' ) {
        $colValueType = explode($kDelim, $_colValueType);
    }
    if ( isset($_colAttributeObject) && $_colAttributeObject != '' ) {
        $colAttributeObject = explode($kDelim, $_colAttributeObject);
    }
    if ( isset($_colAttributeField) && $_colAttributeField != '' ) {
        $colAttributeField = explode($kDelim, $_colAttributeField);
    }
    if ( isset($_colBudValue) && $_colBudValue != '' ) {
        $colBudValue    = explode($kDelim, $_colBudValue);
    }
    if ( isset($_colCompValue) && $_colCompValue != '' ) {
        $colCompValue    = explode($kDelim, $_colCompValue);
    }
    if ( isset($_colNotationValue) && $_colNotationValue != '' ) {
        $colNotationValue    = explode($kDelim, $_colNotationValue);
    }
    if ( isset($_compOnCol) && $_compOnCol != '' ) {
        $compOnCol    = explode($kDelim, $_compOnCol);
    }

    if ( isset($_colTitle) && $_colTitle != '' ) {
        $colTitle        = explode($kDelim, $_colTitle);
    }
    if ( isset($_colShowAs) && $_colShowAs != '' ) {
        $colShowAs    = explode($kDelim, $_colShowAs);
    }
    if ( isset($_colPrecision) && $_colPrecision != '' ) {
        $colPrecision = explode($kDelim, $_colPrecision);
    }
    if ( isset($_colAccountGrp) && $_colAccountGrp != '' ) {
        $colAccountGrp = explode($kDelim, $_colAccountGrp);
    }
    if ( isset($_colreportingMethod) && $_colreportingMethod != '' ) {
        $colreportingMethod = explode($kDelim, $_colreportingMethod);
    }
    if ( isset($_indicatorType) && $_indicatorType != '' ) {
        $indicatorType = explode($kDelim, $_indicatorType);
    }
    if ( isset($_enableColorScale) && $_enableColorScale != '' ) {
        $enableColorScale = explode($kDelim, $_enableColorScale);
    }
    if ( isset($_threshold) && $_threshold != '' ) {
        $threshold = explode($kDelim, $_threshold);
    }
    if ( isset($_aboveThresholdColor) && $_aboveThresholdColor != '' ) {
        $aboveThresholdColor = explode($kDelim, $_aboveThresholdColor);
    }
    if ( isset($_belowThresholdColor) && $_belowThresholdColor != '' ) {
        $belowThresholdColor = explode($kDelim, $_belowThresholdColor);
    }
    if ( isset($_thresholdType) && $_thresholdType != '' ) {
        $thresholdType = explode($kDelim, $_thresholdType);
    }
    if ( isset($_conditionWithMessage) && $_conditionWithMessage != '' ) {
        $conditionWithMessage = explode($kDelim, $_conditionWithMessage);
    }
    if ( isset($_trendType) && $_trendType != '' ) {
        $trendType = explode($kDelim, $_trendType);
    }
    if ( isset($_trendIncreaseClr) && $_trendIncreaseClr != '' ) {
        $trendIncreaseClr = explode($kDelim, $_trendIncreaseClr);
    }
    if ( isset($_showSparkline) && $_showSparkline != '' ) {
        $showSparkline = explode($kDelim, $_showSparkline);
    }

    $kPeriodMap = ($fromCSTool) ? GetIAPeriodMap(true) : GetPeriodMap(true, '', true);

    //$currentPeriod = CurrentReportingPeriod();
    $temp_periodvalues = array_flip($kPeriodMap);
    $currentMonthResolved = DBTokensHandler::getInstance()->getExternalLabel('Current Month');
    $currentPeriod = $temp_periodvalues[$currentMonthResolved]; // this represents the 'Current Month' system defined period;

    $columnCount = ( countArray($report['columns']) ?: 2 );
    /** @noinspection PhpUndefinedVariableInspection */
    $_columnTabRows = ( countArray($colPeriod) ?: $columnCount );

    for($i2 = 0; $i2<$_columnTabRows; $i2++) {
        /** @noinspection PhpUndefinedVariableInspection */
        if ( $colCompValue[$i] == 'a') {
            $colCompValue[$i] = 'x';
            /** @noinspection PhpUndefinedVariableInspection */
            if ( !isset($colTitle[$i]) || $colTitle[$i] == '') {
                $colTitle[$i] = 'Actual';
            }
        }
    }

    $curSelIndex = (isset($_currentcolindex) && $_currentcolindex != '') ? $_currentcolindex : 1;
    if ($_isadd == 1 && $_isabove != 1) {
        $curSelIndex = $curSelIndex + 1;
    } else if ($_isremove && $_currentcolindex > $_columnTabRows) {
        $curSelIndex  = $_columnTabRows;
    }

    $curSelIndex = ($curSelIndex > 1) ? $curSelIndex : 1;

    ?>
        <input type=hidden name=".currentcolindex" value="<?=$curSelIndex?>">
        <input type=hidden name=".isadd" value="">
        <input type=hidden name=".isabove" value="">
        <input type=hidden name=".isremove" value="">
        <input type=hidden name=".opComputation" value="">
        <input type=hidden name=".moveLorR" value="">

        <?php
        //topbotcol
        $topbot_singleCol = 2;
        //This is a PER ROW list! The list gets reset however when a column is selected
        //because for not we use the same column for all rows but the eventual plan is to
        //allow mapping of different rows to different columns!
        $topBotIsSet = false;
        if (isset($_top) && $_top != '') {
            $topMems = explode($kDelim, $_top);
            foreach ( $topMems as $val ) {
                if ($val !='') {
                    $topBotIsSet = true;
                    break;
                };
            }
        }
        if (!$topBotIsSet) {
            if (isset($_bot) && $_bot != '') {
                $botMems = explode($kDelim, $_bot);
                foreach ( $botMems as $val ) {
                    if ($val !='') {
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $topBotIsSet = true;
                        break;
                    };
                }
            }
        }


        $isATopBotColAlsoExpandedCol = isATopBotColAlsoExpandedCol();
        if ($isATopBotColAlsoExpandedCol) {
            $globalColForAllRows = '';
            $topbot_singleCol = 0;
        } else {
            //Then the default index!
            $isATopBotColAlsoExpandedCol = isATopBotColAlsoExpandedColFromIndex($topbot_singleCol);
            if ($isATopBotColAlsoExpandedCol) {
                $globalColForAllRows = '';
                $topbot_singleCol = 0;
            }
        }
        if (!$isATopBotColAlsoExpandedCol) {
            $topbotcolMems = explode($kDelim, $_topbotcol);
            foreach ( $topbotcolMems as $value) {
                if (!empty($value)) {
                    // setting topbot to int for PHP8.1 issue
                    $topbot_singleCol = (int)$value;
                    break;
                }
            }
        }
        /** @noinspection PhpUnusedLocalVariableInspection */
        $topbotcolMap = array();
        if (isset($_topbotcol) && $_topbotcol != '') {
            $topbotcolMems = explode($kDelim, $_topbotcol);
            foreach ( $topbotcolMems as $ij =>  & $topbotcolMem ) {
                if (!$isATopBotColAlsoExpandedCol) {
                    $trialVal = $topbotcolMem;
                    if ($trialVal != '' && !isset($globalColForAllRows)) {
                        //we want to enfore only one value, if somehow the values were mixed this resets it.
                        $globalColForAllRows = $trialVal;
                        $topbot_singleCol = (int)$trialVal;
                    }
                }
                /** @noinspection PhpUndefinedVariableInspection */
                $topbotcolMem = ( $trialVal == '') ? '' : $globalColForAllRows;
                echo "<input type='hidden' name='topbotcol[".$ij."]' value='".$topbotcolMem."'>\n";
            }
            unset($topbotcolMem);
        }


        $_switchColsTable = 'C';
        $showReportingMethod =  !((isset($fromCSTool) && $fromCSTool != '') || empty($adjBooks)) ;
        //TODO: ^^^ If somehow reporting method was set on any column (from a saved report), then that should overide the test below!! I think this is a bug
        //TODO: in the old report writer too! ALternativly, and maybe this is better? The values should really be cleared
        //TODO: in the top of the file??? THis is a bug waiting to happen if not fixed if the tests above can chnage after a company is set up!


        $showTopBottom = showTopBottomSettingsOnCol();
        $showAccountComponent = $_accountOrDimensionRows === 'dimension' && $_dimensionRowsExpandByAccount !=='T';
        $showDimensionComponent = $_accountOrDimensionRows !== 'dimension' && $_dimensionRowsExpandByAccount !=='T';

        /** @noinspection PhpUnusedLocalVariableInspection */
        $acctGrpUIMap = GenerateDimensionAndAccountMaps();

        $layout = array(
                'fields' => array(
                    'IA.COLUMN_TYPE',  'IA.CALCULATION_DETAILS', 'IA.AMOUNT_TYPE', 'IA.REPORTING_PERIOD',
                    'IA.COLUMN_HEADER', ($showAccountComponent ?
                        'IA.ACCOUNT_GROUP'
                        : ($showDimensionComponent && !$fromCSTool ?
                        'IA.DIMENSION_STRUCTURE'
                        : 'HIDEROW')),
                    'IA.EXPAND_BY',
                    'IA.TOTAL',
                    'IA.SHOW_AS', 'IA.PRECISION', ($showReportingMethod ? 'IA.REPORTING_BOOK' : 'HIDEROW'), ($showTopBottom ? 'IA.USE_AS_TOP_BOTTOM' : 'HIDEROW'),
                    'IA.VISUAL_INDICATORS','IA.HIDE_COLUMN'
                ),
        );
        $fields = $layout['fields'];

        echo "<script>var GLOBAL_SHOWTOPBOTTOM = ".($showTopBottom ? 'true;': 'false;')."</script>";
        if ($showReportingMethod) {
            $_reportingBook = &Request::$r->_reportingBook;
            echo "<script>var GLOBAL_REPORTINGBOOK = ".json_encode(isl_htmlspecialchars($_reportingBook))."</script>" ;
        }

        //To Draw Div Section
        DrawSumStrDivSection($kSummaryOp);

        ?>

        <?
        echo "<div  id='divHeader' $coloredBG>";
            echo "<div id='divHeaderScroller'>";
        for ($i=0; $i < $_columnTabRows;$i++) {
                echo "<div></div>";
        }
            echo "</div>";
        echo "</div>";

        for ($i=0, $index=1; $i < $_columnTabRows;$i++, $index++){
            $columnName = TextHelper::getInstance(FinancialReportWizard::class)->GT('IA.COLUMN_INDEX', ['INDEX' => $index]);
                echo RenderRowWiseMenu($columnName, $index, true, $_columnTabRows);
        }
        ?>

        <table class="fwtablewrapper" ><tr><td>
        <table id="wizardColumnTable" class="fwtable fwcolumntable hideNonSelected" >
         <thead>
            <tr <? echo $coloredBG; ?>>
                <th>
                    <div class='spacerFixedColDiv' style='position: absolute; bottom: 0px; padding-bottom: 4px; width: 100%'></div>
                </th>
                <?
                for ($i=0, $index=1; $i < $_columnTabRows;$i++, $index++){
                    /** @noinspection PhpUndefinedVariableInspection */
                    $compareByString =in_array($colValueType[$i], array( 'a', 'x', 'b', 'CA', 'CB', 'F', 'FF', 'RB', 'v', 'V', 'nv', 'nV', 'rV', 'nrV')) ? $kCompareBy[$compareby[$i]] : '';
                    /** @noinspection PhpUndefinedVariableInspection */
                    printRowWiseCtrl($index, $colTitle[$i], $compareByString, $kMeasures[$colValueType[$i]]);
                }
                ?>
            </tr>

        </thead>
        <tbody>
        <?
        $text = TextHelper::getInstance(FinancialReportWizard::class);
        for ($j=0; $j < count($fields);$j++) {
            if( $fields[$j] != '' ) { ?>
               <tr <?echo (($fields[$j] == 'HIDEROW')? 'style="display: none"': ''); ?> >
             <td  align="right" nowrap>
              <b><?echo $text->GT($fields[$j]);
              if($fields[$j] === 'IA.ACCOUNT_GROUP'){
                  echo '<div style="text-align:center"><A class="colComponentLink" onclick="setAccountComponentOnAllColumns(\'account\')">'.$text->GT('IA.SET_ACROSS_COLUMNS').'</A></div>';
              } else if ($fields[$j] === 'IA.DIMENSION_STRUCTURE'){
                  echo '<div style="text-align:center"><A class="colComponentLink" onclick="setAccountComponentOnAllColumns(\'dimension\')">'.$text->GT('IA.SET_ACROSS_COLUMNS').'</A></div>';
              }
              ?></b>
             </td>
                <?
                /** @noinspection PhpUndefinedVariableInspection */
                /** @noinspection PhpUnusedLocalVariableInspection */
                $func = $actions[$i];

                for ($i=0; $i < $_columnTabRows;$i++) {
                    if ( $j == 0 ) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnValue($i, $colValueType[$i], $kMeasures, $colAttributeObject[$i], $colAttributeField[$i]);
                    }
                    else if ( $j == 1 ) {
                        echo '<td>';
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnBudget($i, $colValueType[$i], $colBudValue[$i], $budgetMap);
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnCalculation($i, $colValueType[$i], $colCompValue[$i], $kCompMap, $compOnCol[$i]);
                        printColumnNotation($i, $colValueType[$i], $colNotationValue[$i], $kNotationMap);
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnSummary($i, $colValueType[$i], $summaryStr[$i]);
                        echo '</td>';
                    }
                    else if ( $j == 2 ) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        printCumulative($i, $colValueType[$i], $kcolASOF, $colasof[$i]);
                    }
                    else if ( $j == 3 ) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnPeriod($i, $colValueType[$i], $colPeriod[$i], $kPeriodMap, $currentPeriod, $colPeriodOffset[$i], $colPeriodOffsetBy[$i]);
                    }
                    else if ( $j == 4 ) {
                        echo '<td>';
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnHdr1($i, $colhdr1[$i], $colValueType[$i]);
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnHdr2($i, $colhdr2[$i], $colValueType[$i]);
                        /** @noinspection PhpUndefinedVariableInspection */
                        printColumnTitle($i, $colTitle[$i], $colValueType[$i]);
                        echo '</td>';
                    }
                        else if ( $j == 5 ) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnAccountGroup($i, $colAccountGrp[$i], $colValueType[$i], $showAccountComponent ? 'account' : 'dimension');
                        }
                        else if ( $j == 6 ) {
                            $colRolPd = isl_trim($colPeriod[$i])?$colPeriod[$i]:$currentPeriod;
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnCompareby($i, $colValueType[$i], $compareby[$i], $kCompareBy, $kPeriodMap[$colRolPd], true, $topbot_singleCol, $showTopBottom, $showSparkline[$i]);
                        }
                        else if ( $j == 7 ) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnRightLeftHide($i, $rightlefthide[$i], $compareby[$i]);
                        }
                        else if ( $j == 8 ) {
                            $colRolPd = isl_trim($colPeriod[$i])?$colPeriod[$i]:$currentPeriod;
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnShowAs($i, $colValueType[$i], $colShowAs[$i], $kPeriodMap[$colRolPd], $topbot_singleCol, $showTopBottom);
                        }
                        else if ( $j == 9 ) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnPrecision($i, $colValueType[$i], $colPrecision[$i], $kPrecisions);
                        }
                        else if ( $j == 10 ) {
                            translateReportingBooksDBValue($adjBooks);
                            translateReportingBooksDBValue($companyBooks);
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnReportingMethod($i, $colreportingMethod[$i], $companyBooks, $adjBooks, $colValueType[$i]);
                        }
                        else if ( $j == 11 ) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnTopBotCol($i, $topbot_singleCol, $colValueType[$i], $compareby[$i], $colShowAs[$i]);
                        }
                        else if ( $j == 13 ) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            printColumnHide($i, $compareby[$i], $colhide[$i], $colValueType[$i]);
                        }
                        else if ( $j == 12 ) {
                            $colRolPd = isl_trim($colPeriod[$i])?$colPeriod[$i]:$currentPeriod;
                            /** @noinspection PhpUndefinedVariableInspection */
                            printVisualControls(
                                $i, $colValueType[$i], $indicatorType[$i],
                                $conditionWithMessage[$i],
                                $enableColorScale[$i], $threshold[$i], $aboveThresholdColor[$i], $belowThresholdColor[$i], $thresholdType[$i],
                                $trendType[$i], $trendIncreaseClr[$i],
                                $showSparkline[$i], $kPeriodMap[$colRolPd], $compareby[$i]
                            );
                        }
                }
                ?>
            </tr>
            <?
            }
        } ?>
            </tbody>
        	</table>
        	</td></tr></table>

        	<script>
            jq(".EditReportWizardContent").css("visibility", "hidden");
//	        jq(document.body).addClass("busy");
	        jq(".EditReportWizardContent")[0].scrollLeft = 1;
	        jq(".EditReportWizardContent")[0].scrollLeft = 0;

    		var oWizardTable = undefined;
        	var oWizardTableController = undefined;
        	var haltRecalculate = false;

            function recalculateTableColumnHeight() {
                if (undefined ||!haltRecalculate) {
        	        var colLabelRows = jq('.EditReportWizard .DTFC_LeftBodyWrapper .fwtable>TBODY>TR');
        	        jq('#wizardColumnTable>TBODY>TR').each(function(index, elm){
        	            colLabelRows[index].style.height = jq(elm).height() + 'px';
                    }); //fwtable

                    window.setTimeout(function() {
                        fixScrollHeight();
                        fixHeaderWidths();
                        fixScrollHeight();
                        fixHeaderWidths();
                        }, 1);

                    }
            }

    	    jq(document).ready( function () {
        	    try {

        	        jq(".EditReportWizardContent").css("visibility", "hidden");
//        	        jq(document.body).addClass("busy");
        	        jq(".EditReportWizardContent")[0].scrollLeft = 1;
        	        jq(".EditReportWizardContent")[0].scrollLeft = 0;

                     FORMWILLHANDLESTRETCH = true;
                     COLUMN_TABLE_ACTIVE = true;

                	var _selectedRow = null;

                	var selCss = {"background": "#FFF"};
                	var selNotCss = {"background": "#EDF2F9"};

                    jq('#wizardColumnTable>TBODY>TR>TD:nth-child(1)').addClass('fixedCol');
                    jq('#wizardColumnTable>THEAD>TR>TH:nth-child(1)').addClass('fixedCol');


                    jq('#wizardColumnTable>TBODY>TR:last').addClass('lastRow');

                    oWizardTable =jq('#wizardColumnTable').dataTable( {
            	        "sScrollY": SetContentHeight() + "px", //this table is not that long
        	            "sScrollX": "1px",
        	            //"sScrollX": "100%",
            	        "bScrollCollapse": true,
            	        "bPaginate": false,
            	        "bSort": false,
            	        "bInfo" : false,
            	        "bFilter" : false,
            	        //"sHeightMatch": "auto"
            	        } );

        	        function setCollasedText(index, element) {
	                    var name=element.name;
	                    var inactiveValue = document.getElementById(name+'_inactiveValue'); // can't use jquery here?
	                    if (inactiveValue) {
    	                    var notColSelValue = "";
    	                    if (!element.disabled && !(element.style.visibility === 'hidden'|| jq(element).hasClass() === 'hideSection')) {
        	                    $inactiveValue= jq(inactiveValue);
                                if (element.type === 'radio') {
                                    notColSelValue = $inactiveValue.attr('chkText');
                                } else if (element.type === 'checkbox') {
        	                        notColSelValue = element.checked ? ($inactiveValue.attr('chkText') ? $inactiveValue.attr('chkText') : '') : ($inactiveValue.attr('unchkText') ? $inactiveValue.attr('unchkText') : '');
        	                    } else if (this.tagName === 'SELECT') {
        	                        notColSelValue = jq(element).find("option:selected:first").text();
        	                        notColSelValue = (notColSelValue.indexOf("--") == 0 || notColSelValue === 'None' || notColSelValue === 'Do not expand') ? '' : notColSelValue;

                                    //hack for reporting period offset
                                    if (name.indexOf('colPeriodOffset') == 0)  {
                                        if (name.indexOf('_select') != -1) {
                                            var prdOffsetObj = document.getElementsByName(name.replace('_select', '_text'));
                                        } else {
                                            var prdOffsetObj = document.getElementsByName(name.replace('By', '_text'));
                                        }

                                        if (prdOffsetObj[0]) {
                                            if (prdOffsetObj[0].value == '0') {
                                                notColSelValue = '';
                                            } else if (prdOffsetObj[0].value == '1') {
                                                notColSelValue = notColSelValue.replace('s','');
                                            }
                                        }

                                    };

            	                } else {
            	                    if (element.value == '-- Select Reporting Period --') { //hackolla
            	                    //need a better way to handle combo2boxes.
            	                        notColSelValue = '';
            	                    } else {
                                        notColSelValue = element.value ? element.value : '';
                                        if (name.indexOf('colPeriodOffset') == 0 && (element.value == '0' || element.value == ''))  {

                                            notColSelValue = '';
                                        }
            	                    }
        	                    }
        	                    $inactiveValue.text(notColSelValue);
	                        } else {
        	                    $inactiveValue= jq(inactiveValue);
        	                    $inactiveValue.text('');
        	                }

    	                    var altTextValue = document.getElementById(name+'_altText'); // can't use jquery here?
	                        if (altTextValue) {
	    	                    $inactiveValue.text(jq(altTextValue).text());
	    	                }
	                    }
	                }

            	    function resetTabIndexesAndInitialize(tableid) {
            	        haltRecalculate = true;

        	            var columnCount = 0;
        	            jq(tableid+'>TBODY tr').each(function() {
        	                columnCount = Math.max(columnCount, jq(this).children('td').length);
        	            });

        	            var cellCounter = 1;
        	            for(var columnIndex = 0; columnIndex < columnCount; ++columnIndex) {

    	                    jq(tableid+'>TBODY tr').each(function() {
        	                    var inputs = jq(this).children('td').eq(columnIndex).find('input, select');

        	                    if(inputs != null)
        	                    {
        	                        inputs.each(function() {
            	                        jq(this).attr('tabindex', cellCounter++);}
        	                        );
        	                    }

        	                    inputs.each(setCollasedText);
        	                });

    	                    var obj = document.getElementsByName('colValueType['+columnIndex+']');
        	                if (obj && obj.length && obj[0].value == 'a') {
            	                //'a' now switches to actual (as is 'x' but now it just means select.)
        	                    SetColValueSelection(obj[0], columnIndex);
        	                }
        	            }

            	        haltRecalculate = false;
            	    }

            	    function isColumnSelectedAlready(td) {
                     	var colIndex = jq(td).parent("tr").children().index(td) + 1;
                    	return (_selectedRow == colIndex);
            	    }
                	function clickColumn(td, from) {
                	    cmbCloseAll();

                    	var colIndex = (from !== "HEADER") ? jq(td).parent("tr").children().index(td) + 1 : jq('.EditReportWizard #divHeaderScroller').children().index(td) + 1;
                    	if (_selectedRow == colIndex && from !== "HEADER") {
                    	    return;
                	    }
                        document.mf.elements['.currentcolindex'].value = colIndex;

                	    if (_selectedRow) {
                	        jq('.EditReportWizard #divHeaderScroller>div:nth-child('+_selectedRow+')').removeClass('colsel');
                	        jq('#wizardColumnTable>TBODY>TR>TD:nth-child('+_selectedRow+')').each(function (inx, elm) {
                           	    $elm = jq(elm);
                           	    $elm.removeClass('colsel');
                	            var inputs = $elm.find('input, select');
                	            inputs.each(setCollasedText);
                	            });
                	    }

                	    if (_selectedRow != colIndex || from !== "HEADER") {
                	        jq('.EditReportWizard #divHeaderScroller>div:nth-child('+colIndex+')').addClass('colsel').removeClass('fwhover');

                       	    jq('#wizardColumnTable>TBODY>TR>TD:nth-child('+colIndex+')').each(function() {
                    	        var $this = jq(this);
                    	        $this.addClass('colsel');
                    	        $this.removeClass('colselTemp');
                    	        $this.removeClass('fwhover');

                    	        var visible = $this.find("INPUT:visible, SELECT:visible");
                    	        var showLabel = visible.length ? true : false;
                    	        for(var i=0; i <visible.length && !showLabel; i++) {
                        	        if (this.style.visibility !== 'hidden') showLabel = true;
                        	    }

                        	    if (!showLabel) {
                        	        $this.find(".cellLabel").addClass('hideSection');
                        	        $this.find(".cellhelp").addClass('hideSection');
                            	} else {
                                    $this.find(".cellLabel").removeClass('hideSection');
                                    $this.find(".cellhelp").removeClass('hideSection');
                                }

                        	    //hac for Calculation details
                        	    var $parent = $this;
                        	    $this.find(".fwbudget").each(function(){

                            	    var coltype = document.mf.elements['colValueType[' + (colIndex - 1) + ']'].value;
                            	    var onColLabel = document.getElementById('labcompOnCol[' + (colIndex - 1)+ ']');
                            	    var colNotationValueObj = document.getElementById('colNotationValue[' + (colIndex - 1)+ ']');
                            	    var $helpOnColObj = jq(onColLabel).parent().find('.cellhelp');

                                    var isBudget = jq.inArray(obj.value, ['b','P','V','rV','v','nV','nv','CB','pb','RB','F', 'FF', 'nrV']) !== -1;
                            	    if (coltype == 'S') {
                            	        $parent.find(".cellLabel").addClass('hideSection');
                            	        $parent.find(".cellhelp").addClass('hideSection');
                                	    $parent.find('.fwsummary').removeClass('hideSection');
                            	        jq(onColLabel).addClass('hideSection');
                            	        $helpOnColObj.addClass('hideSection');
                            	    } else if (coltype == 'CA') {
                            	        $parent.find(".cellLabel").addClass('hideSection');
                            	        $parent.find(".cellhelp").addClass('hideSection');
                                	    jq(onColLabel).removeClass('hideSection');
                            	        $helpOnColObj.removeClass('hideSection');
                                	    $parent.find('.fwcalculation').removeClass('hideSection');
                                    } else if (coltype == 'CB') {
                            	        $parent.find(".cellLabel").addClass('hideSection');
                            	        $parent.find(".cellhelp").addClass('hideSection');
                                        $parent.find('.fwbudget').removeClass('hideSection');
                                        $parent.find('.GL_FinancialReports_ColumnsTab_Budget').removeClass('hideSection');
                            	        jq(onColLabel).removeClass('hideSection');
                            	        $helpOnColObj.removeClass('hideSection');
                            	        $parent.find('.fwcalculation').removeClass('hideSection');
                                    } else if (isBudget) {  // other budgets
                                        $parent.find(".cellLabel").addClass('hideSection');
                                        $parent.find(".cellhelp").addClass('hideSection');
                                        jq(onColLabel).addClass('hideSection');
                            	        $helpOnColObj.addClass('hideSection');
                                        $parent.find('.fwbudget').removeClass('hideSection');
                                        $parent.find('.GL_FinancialReports_ColumnsTab_Budget').removeClass('hideSection');
                                    } else if (coltype == 'VDS') {
                                        $parent.children().addClass('hideSection');
                                        var hdr1Obj = document.mf.elements['colhdr1[' + colIndex - 1 + ']'];
                                        if ( hdr1Obj ) {
                                            jq(hdr1Obj)
                                                .closest('.colsel')
                                                .children()
                                                .addClass('hideSection');
                                        }
                                    }else if (coltype == 'NOT') {
                                        $parent.find(".cellLabel").addClass('hideSection');
                                        $parent.find(".cellhelp").addClass('hideSection');
                                        jq(colNotationValueObj).removeClass('hideSection');
                                        // $helpOnColObj.removeClass('hideSection');
                                        $parent.find('.fwnotation').removeClass('hideSection');
                                    };
                        	    });

                                //hack for reporting period offset
                        	    $this.find(".offsetCellLabel").each(function(){
                            	    var $that = jq(this);
                        	        var $periodOffset = $this.find('SELECT[name^="colPeriodOffset"]');
                        	        var showLabel =  ($periodOffset.length && !$periodOffset[0].disabled);

                            	    if (!showLabel) {
                            	        $that.addClass('hideSection');
                                    } else {
                                        $that.removeClass('hideSection');
                                    }
                                });

                    	    });

                	        _selectedRow = colIndex;
                    	} else {
                            _selectedRow = 0;
                	    }

                        //jq('#wizardColumnTable>THEAD>TR>TH').css('width', '0px'); //fwtable
                        jq('#wizardColumnTable').css('width', '0px');


                        recalculateTableColumnHeight();
                    };

            	    resetTabIndexesAndInitialize('#wizardColumnTable');
            	    window.setTimeout(function() {resetTabIndexesAndInitialize('#wizardColumnTable');}, 10); // some fiedls get initalize later in the ready state, OK to do again.
            	    oWizardTableController = new FixedColumns( oWizardTable );
                    //jq('#wizardColumnTable').width('100%');
                    jq('#wizardColumnTable').css('width', '0px');

                    initDivHeaders();


                    jq('#wizardColumnTable>TBODY>TR').each(function(){
                        oWizardTableController.fnRecalculateHeight(this);
                    }); //fwtable

                    if (document.GLOBAL_INIT_COMBOS != undefined) {
                        for (xx=0;xx<document.GLOBAL_INIT_COMBOS.length;xx++) {
                            document.GLOBAL_INIT_COMBOS[xx]();//call all the init functions in this array
                        }
                        document.GLOBAL_INIT_COMBOS=undefined;
                    }

                    var curCol = <?php echo (isset($curSelIndex) && $curSelIndex != '') ? $curSelIndex : 1;?> ;
                    clickColumn( jq('#wizardColumnTable>TBODY>TR>TD:nth-child('+curCol+')') ); // select the INITIAL index here;

                    if (isIE && getInternetExplorerVersion().major <= 8) {
                        //hide cell labels on load;
                         jq('#wizardColumnTable>TBODY>TR>TD').each(function() {
                             var $this = jq(this);
                             var visible = $this.find("INPUT:visible, SELECT:visible");
                             var showLabel = visible.length ? true : false;
                             for(var i=0; i <visible.length && !showLabel; i++) {
                    	         if (this.style.visibility !== 'hidden') showLabel = true;
                    	     }
                    	     if (!showLabel) {
                    	         $this.find(".cellLabel").addClass('hideSection');
                    	         $this.find(".cellhelp").addClass('hideSection');
                            }

                             //hack for reporting period offset
                             $this.find(".offsetCellLabel").each(function(){
                                 var $that = jq(this);
                                 var $periodOffset = $this.find('SELECT[name^="colPeriodOffset"]');
                                 var showLabel =  ($periodOffset.length && !$periodOffset[0].disabled);
                                 if (!showLabel) {
                                     $that.addClass('hideSection');
                                 }
                             });

                             //hack for computations
                             $this.find("span.cellLabel.fwbudget").each(function(){
                                 var $that = jq(this);
                                 var $periodOffset = $this.find('SELECT[name^="colBudValue"]');
                                 var showLabel =  ($periodOffset.length && !$periodOffset[0].disabled);
                                 if (!showLabel) {
                                     $that.addClass('hideSection');
                                 }
                             });
                             $this.find("span.cellLabel.fwcalculation").each(function(){
                                 var $that = jq(this);
                                 var $periodOffset = $this.find('SELECT[name^="colCompValue"]');
                                 var showLabel =  ($periodOffset.length && !$periodOffset[0].disabled);
                                 if (!showLabel) {
                                     $that.addClass('hideSection');
                                     $this.find(".cellhelp").addClass('hideSection');
                                 }
                             });
                             $this.find("div.cellLabel.fwsummary").each(function(){
                                 var $that = jq(this);
                                 var $periodOffset = $this.find('INPUT[name^="summaryStr"]');
                                 var showLabel =  ($periodOffset.length && !$periodOffset[0].disabled);
                                 if (!showLabel) {
                                     $that.addClass('hideSection');
                                 }
                             });


                         });

                   } else {
                        //may need to undisable some fields based on type, bug shoulds when the user navs away and the hits the back button!!!
                        var budgetColTypes = {'b': true, 'P': true, 'V': true, 'rV': true, 'v': true, 'nV': true, 'nv': true, 'CB': true, 'pb': true, 'RB': true, 'F': true, 'FF' : true, 'nrV' : true};
                        jq('#wizardColumnTable>TBODY').find('SELECT[name^="colValueType"]').each(function() {
                            var $this = jq(this);
                            var $parenttd = $this.closest('td');
                            var $parenttr = $parenttd.closest('tr');
                            var $parenttbody = $parenttr.closest('tbody');
                            var indextd = $parenttd.index() + 1;
                            var $targetcol = $parenttbody.find('TR>TD:nth-child('+indextd+')');

                            var coltype = $this.val();

                            $targetcol.find('SELECT[name^="colCompValue"], INPUT[name^="compOnCol"]').prop('disabled', true);
                            $targetcol.find('INPUT[name^="summaryStr"]').prop('disabled', true);
                            $targetcol.find('A[id^="sumBut"]').prop('disabled', true);
                            var $budgetSelect = $targetcol.find('SELECT[name^="colBudValue"]');
                            $budgetSelect.prop('disabled', (budgetColTypes[coltype] === undefined));

                            if (coltype == 'S') {
                                $targetcol.find('INPUT[name^="summaryStr"]').prop('disabled', false);
                                $targetcol.find('A[id^="sumBut"]').prop('disabled', false);
                            } else if (coltype == 'CA') {
                                $targetcol.find('SELECT[name^="colCompValue"], INPUT[name^="compOnCol"]').prop('disabled', false);
                            } else if (coltype == 'CB') {
                                $targetcol.find('SELECT[name^="colCompValue"], INPUT[name^="compOnCol"]').prop('disabled', false);
                            } else {
                            };

                        });

                     }

            	    jq('.EditReportWizard #divHeaderScroller>div').click(function(event){frw_eatClick(event); clickColumn(this, 'HEADER');});
            	    //jq('.fwcolumntable>THEAD>TR>TH').click(function(){return clickColumn(this, 'HEADER');});
                    jq('#wizardColumnTable>TBODY>TR>TD').click(function(event){if (isHackComboBox(event) || isColumnSelectedAlready(this))return; frw_eatClick(event); clickColumn(this, null) ;});

                	function keyDown (event, input) {
                    	//tab key handling
                	    var keyCode  = event.keyCode || event.which;
                	    var shiftKey = event.shiftKey;


                	    if (keyCode == 9) {
                          	var $curTR = jq(input).closest("TR");
                    	    var $tbody = jq(input).closest("TBODY");

                            if (!shiftKey) {
                              	var $curTD = jq(input).closest("TD");
                                var curIndex = $curTD.parent("tr").children().index($curTD[0]) + 1;

                            	var $trs = $tbody.children("TR");
                            	var lastVisibleTR = null;
                            	for (var i=$trs.length; i--;) {
                            	    if (jq($trs[i]).children('TD:nth-child('+(curIndex)+')').find(".cellLabel:hidden").length === 0) {
                            	        lastVisibleTR = $trs[i];
                            	        break;
                            	    };
                            	}

                                if ($curTR[0] == lastVisibleTR) {
                        	        if (curIndex < $curTR.children('td').length) {
                            	        jq('#wizardColumnTable>TBODY>TR>TD:nth-child('+(curIndex + 1)+')').addClass('colsel colselTemp');
                            	    }
                        	    }
                            } else {
                            	var $trFirst =  $tbody.children("TR:first");
                                if ($curTR[0] == $trFirst[0]) {
                                  	var $curTD = jq(input).closest("TD");
                                	var curIndex = $curTD.parent("tr").children().index($curTD[0]);
                                	if (curIndex > 0) {
                                    	jq('#wizardColumnTable>TBODY>TR>TD:nth-child('+(curIndex)+')').addClass('colsel colselTemp');
                                    }
                        	    }
                            }
                	    }
                	}
                	jq('#wizardColumnTable>TBODY>TR>TD INPUT, #wizardColumnTable>TBODY>TR>TD SELECT, #wizardColumnTable>TBODY>TR>TD TEXTAREA').keydown(function(event){return keyDown(event, this);});

                	function mouseDownInput(event, input, type) {
                	    if (isHackComboBox(event)) return;

                    	var $td = jq(input).closest("TD");
                	    if (!$td.hasClass('colsel') || $td.hasClass('colselTemp')) {
                	        clickColumn($td[0]);
                	        if (type === 'focus') {
                    	        try {
                    	            input.focus();
                    	            input.select();
                    	        } catch (e) {}
                    	    };

                    	    frw_eatClick(event);
                    	    return false;
                	    }
                	}
                	jq('#wizardColumnTable>TBODY>TR>TD INPUT, #wizardColumnTable>TBODY>TR>TD SELECT, #wizardColumnTable>TBODY>TR>TD TEXTAREA').mousedown(function(event){return mouseDownInput(event, this, 'mousedwn');});
                	jq('#wizardColumnTable>TBODY>TR>TD INPUT, #wizardColumnTable>TBODY>TR>TD SELECT, #wizardColumnTable>TBODY>TR>TD TEXTAREA').focus(function(event){return mouseDownInput(event, this, 'focus');});


                	jq('#wizardColumnTable>TBODY>TR>TD, #divHeaderScroller>div').hover(
            	        function(){
                	        $this = jq(this);
                        	var colIndex = $this.parent().children().index(this) + 1;
                        	if (_selectedRow == colIndex) return;
                        	jq('#wizardColumnTable>TBODY>TR>TD:nth-child('+colIndex+')').addClass('fwhover');
                        	jq('.EditReportWizard #divHeaderScroller>div:nth-child('+colIndex+')').addClass('fwhover');
                        },
            	        function(){
                	        $this = jq(this);
                        	var colIndex = $this.parent().children().index(this) + 1;
                        	if (_selectedRow == colIndex) return;
                        	jq('#wizardColumnTable>TBODY>TR>TD:nth-child('+colIndex+')').removeClass('fwhover');
                        	jq('.EditReportWizard #divHeaderScroller>div:nth-child('+colIndex+')').removeClass('fwhover');
                        }
                      );

                    fixHeaderWidths();
                    fixScrollHeight();
                    fixHeaderWidths();
                    jq('.EditReportWizard .dataTables_scrollBody').scroll(function () {
                        fixHeaderPosition();
                    });

                    jq(window).scroll(function () {
                        fixHeaderPosition();
                    });

                    jq(window).bind('resize', function () {
                        //jq('#wizardColumnTable').width('100%');
                        fixScrollHeight();
                        fixHeaderWidths();
                        fixScrollHeight();
                        fixHeaderWidths();
                        //recalculateTableColumnHeight();
            	    } );

                    fixHeaderWidths();

                    for (var i =0 ; i < <?=$_columnTabRows?>; i++) {
                        fillColTitleIndicators(i);
                    }

        	        jq(document.body).removeClass("busy");
        	        jq(".EditReportWizardContent").css("visibility", "visible");
        	        jq(".EditReportWizardContent")[0].scrollLeft = 1;
        	        jq(".EditReportWizardContent")[0].scrollLeft = 0;

        	    } catch (e) {
         	        jq(document.body).removeClass("busy");
        	        jq(".EditReportWizardContent").css("visibility", "visible");
        	        console.log(e.description);
        	        jq(".EditReportWizardContent")[0].scrollLeft = 1;
        	        jq(".EditReportWizardContent")[0].scrollLeft = 0;

                }
        	});


	        </script>

<?
    DrawAccountComponentOnAllColumnsDialog($showAccountComponent ? 'account' : 'dimension');

}

function EditReportGeneratePageFormat()
{
    $_r                = &Request::$r->_r;
    $_title            = &Request::$r->_title;
    $_align_currency   = &Request::$r->_align_currency;
    $_bottommargin     = &Request::$r->_bottommargin;
    $_definedby        = &Request::$r->_definedby;
    $_deptlocgrpsort   = &Request::$r->_deptlocgrpsort;
    $_displocascotitle = &Request::$r->_displocascotitle;
    $_footer_alignment = &Request::$r->_footer_alignment;
    $_pg               = &Request::$r->_pg;
    $_pgnumber         = &Request::$r->_pgnumber;
    $_reportdate       = &Request::$r->_reportdate;
    $_reporttime       = &Request::$r->_reporttime;
    $_rfcd             = &Request::$r->_rfcd;
    $_rightmargin      = &Request::$r->_rightmargin;
    $_rounding         = &Request::$r->_rounding;
    $_runtimeparams    = &Request::$r->_runtimeparams;
    $_zeroactual       = &Request::$r->_zeroactual;
    $_zeroWTrans       = &Request::$r->_zeroWTrans;
    $_footerallpages   = Request::$r->_footerallpages;
    $_header_alignment = &Request::$r->_header_alignment;
    $_showExchRate     = &Request::$r->_showExchRate;

    global $fontMap, $colorMap;
    global $finDimInfo, $kGLid, $fontArray, $sizeMap, $smallSizeMap;
    global $fromCSTool, $kRounding;
    global $kPageSizeMap;
    global $prompt_for_reportingachdr;

    $text = TextHelper::getInstance(FinancialReportWizard::class);

    $_titlecomment       = &Request::$r->_titlecomment;
    $_reportingBook      = &Request::$r->_reportingBook;
    $_industry           = &Request::$r->_industry;
    $_reportingachdrkey  = &Request::$r->_reportingachdrkey;
    $_footertext         = &Request::$r->_footertext;
    $_displayfooteras    = &Request::$r->_displayfooteras;
    $_forceDefaulting    = &Request::$r->_forceDefaulting;
    $_hidezerobalcol     = &Request::$r->_hidezerobalcol;
    $_displaydash        = &Request::$r->_displaydash;
    $_leftmargin         = &Request::$r->_leftmargin;
    $_logo               = &Request::$r->_logo;
    $_logoalign          = &Request::$r->_logoalign;
    $_negnumber          = &Request::$r->_negnumber;
    $_orientation        = &Request::$r->_orientation;
    $_pagesize           = &Request::$r->_pagesize;
    $_showDeptIDNAME     = &Request::$r->_showDeptIDNAME;
    $_showLocIDNAME      = &Request::$r->_showLocIDNAME;
    $_showDimIDNAME      = &Request::$r->_showDimIDNAME;
    $_topmargin          = &Request::$r->_topmargin;
    $_scale              = &Request::$r->_scale;
    $_title2             = &Request::$r->_title2;
    $_title3             = &Request::$r->_title3;
    $_WIZARD_TAB_SUBPAGE = &Request::$r->_WIZARD_TAB_SUBPAGE;
    $_hideCFltrTitle     = Request::$r->_hideCFltrTitle;
    $_noofpages          = Request::$r->_noofpages;

    $sub = 'general'; //or setup or columns

    if (!empty($_WIZARD_TAB_SUBPAGE)) {
        $sub = $_WIZARD_TAB_SUBPAGE;
    }


    //EditReportHeaderSeperator();

    $copyedFromReportNamed_id = '';

    $myReports = getMyReports($_industry);
    if ($_pg == '7' && Request::$r->_reportToCopyFrom_docopy === 'true' && isset(Request::$r->_reportToCopyFrom_select) && Request::$r->_reportToCopyFrom_select !== '') {
        [$pageformattingToCopy, $otherReportInfo] = getReportFormat(Request::$r->_reportToCopyFrom_select);

        $copyedFromReportNamed_id = Request::$r->_reportToCopyFrom_select;

        //resets all the global values from another report!
        setFormatGlobalValues($pageformattingToCopy, $otherReportInfo);
    }

    $_definedby = $_definedby ?: 'S';

        // if orientation was not set,
    if ($_orientation!='P' && $_orientation!='L') {
        if ($_r) {
            // our reports used to set orientation based on number of columns, we mimic that here as well.
            $allcols = 0;
            /** @noinspection PhpUndefinedVariableInspection */
            $maincolcount = countArray($report['columns']);
            $subcolcount = 0;
            for ($i=0;$i<$maincolcount;$i++) {
                $subcolcount = $subcolcount + countArray(SubCols($report['columns'][$i]));
                if ($subcolcount) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    foreach ( SubCols($gReport['columns'][$i]) as $subc) {
                        $allcols++;
                    }
                } else {
                    $allcols++;
                }
            }
            if ($allcols < 4) {
                $_orientation = 'P';
            } else {
                $_orientation = 'L';
            }
        } else {
            // new report without orientation, default it simply.
            $_orientation = 'P';
        }
    }

    // If we are in fit to page layout always set orientation to portrait
    if($_definedby == 'F') {
        $_orientation = 'P';
    }

    // defaulting goes here:
    if ($_align_currency!='L' && $_align_currency!='F') {
        $_align_currency = 'L';
    }
    if ($_footer_alignment!='L' && $_footer_alignment!='M' && $_footer_alignment!='R') {
        $_footer_alignment = 'L';
    }
    if ($_header_alignment!='L' && $_header_alignment!='M' && $_header_alignment!='R') {
        $_header_alignment = 'L';
    }


    $title = isl_htmlspecialchars($_title);
    $title2 = isl_htmlspecialchars($_title2);
    $title3 = isl_htmlspecialchars($_title3);
    $titlecomment = isl_htmlspecialchars($_titlecomment);

    $fs_title = FieldSize('reportinfo.title');
    $fs_title2 = FieldSize('reportinfo.title2');
    $fs_title3 = FieldSize('reportinfo.title3');

    if ( $_topmargin == '' ) {
        $_topmargin = ( $_orientation !='P' ? '20' : '28' );
    }

    if ( $_bottommargin == '' ) {
        $_bottommargin = ( $_orientation !='P' ? '28' : '28' );
    }

    if ( $_leftmargin == '' ) {
        /** @noinspection PhpUndefinedVariableInspection */
        $_leftmargin = ( $_orientation != 'P' ? '36' : ( 36 - $maxfitwidth) );
    }

    if ( $_rightmargin == '' ) {
        /** @noinspection PhpUndefinedVariableInspection */
        $_rightmargin = ( $_orientation != 'P' ? '36' : ( 36 - $maxfitwidth) );
    }

    $_topmargin = (isset($_topmargin) && $_topmargin) ? $_topmargin : '0';
    $_bottommargin = (isset($_bottommargin) && $_bottommargin) ? $_bottommargin : '0';
    $_leftmargin = (isset($_leftmargin) && $_leftmargin) ? $_leftmargin : '0';
    $_rightmargin = (isset($_rightmargin) && $_rightmargin) ? $_rightmargin : '0';

/** @noinspection PhpUnusedLocalVariableInspection */
$logoChecked     = ( $_logo == 'T') ? 'checked' : '';
    $pgnumberChecked     = ($_pgnumber == 'T') ? 'checked' : '';
    $reportdateChecked   = ($_reportdate == 'T') ? 'checked' : '';
    $reporttimeChecked   = ($_reporttime == 'T') ? 'checked' : '';
    $fallpagesChecked     = ($_footerallpages == 'T') ? 'checked' : '';

    if ($copyedFromReportNamed_id != '') {
        $copiedFromReportNamed = '<p>'. $text->GT('IA.FORMATTING_WAS_COPIED_FROM_REPORT_NAME',
                                                  ['REPORT_NAME' => $myReports[$copyedFromReportNamed_id] ]) . '</p>';
    }


?>

	<input type=hidden name=".colcomparison">

    <div class='reportSection formatPage'>
        <!--<div class='reportSectionLabel'  style="margin-bottom: 20px;   <?=($sub != 'general') ? 'display: none;':''; ?>">Copy formatting</div>-->
        <div style="margin-left: 0px;<?=($sub != 'general') ? 'display: none;':''; ?>">
             <div class='reportSectionLabel reportsmalllabel' style='margin-top: 0px;margin-bottom: 12px;'><?=$text->GT('IA.COPY_FORMATTING_FROM_AN_EXISTING_REPORT')?>
                    <span class='GL_FinancialReports_FormattingTab_CopyFormatting cellhelp'></span>
             </div>
             <fieldset>
                 <table>
                     <tr>
                        <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.REPORT')?></label></td>
                        <td class='reportFieldValue'>
                            <select id="reportToCopyFrom_select" name='.reportToCopyFrom_select' style='max-width: 250px;' onchange='jq("#reportToCopyFrom_button").css("visibility", (this.selectedIndex != 0 ? "visible" : "hidden"))'>
                                <option value='' selected></option>
                                <? foreach ($myReports as $key => $value){
                                        echo "<option value='".$key."'>".isl_htmlspecialchars($value)."</option>";
}
                                ?>
                            </select>
                        </td>
                        <td class='reportFieldValue' style='padding-left: 5px'>
                             <input type='hidden' id='reportToCopyFrom_docopy' name='.reportToCopyFrom_docopy' value="">
                             <div  type='button' id='reportToCopyFrom_button' class='finwizButton_action2' tabindex=0
                                     style='display: inline-block; visibility: hidden'
                                     onclick='jq("#reportToCopyFrom_docopy").val(""); if (confirm(GT("IA.COPYING_FORMAT_SETTINGS_FROM_ANOTHER_REPORT"))) {jq("#reportToCopyFrom_docopy").val("true"); SetNoCheckRequired(); DoSubmit("", ".jump", "7");} else {jq("#reportToCopyFrom_select")[0].selectedIndex = 0; jq(this).css("visibility", "hidden")};'>
                                    <?=$text->GT('IA.COPY_FORMATTING')?>
                            </div>
                         </td>
                     </tr>
                     <tr>
                         <td></td>
                         <td colspan=2>
                         <div class="reportSectionLabelText toptext" style='margin: 0px'>
                             <P style='color: #999; margin:0px; padding: 0px'> <?=$text->GT('IA.COPIES_SELECTED_FORMATTING_OPTIONS_FROM_THE_SELECT')?>
                         </div>
                         </td>
                     </tr>
                     <tr>
                       <td></td><td colspan=2> <?
                             /** @noinspection PhpUndefinedVariableInspection */
                             echo $copiedFromReportNamed?></td>
                     <tr>
                 </table>
             </fieldset>
        </div>
    </div>
    <?
    if (!$fromCSTool) {
        $runtimeparams = isl_htmlspecialchars($_runtimeparams);
        $fs_runtimeparams = FieldSize('reportinfo.runtimeparams');
    }
    ?>
    <input type=hidden name=".runtimeparams" value="<?= /** @noinspection PhpUndefinedVariableInspection */
    $runtimeparams?>" size=50 maxlength="<?= /** @noinspection PhpUndefinedVariableInspection */
    $fs_runtimeparams?>">

    <?
    $rptAcctEnabled = IsReportingAccountEnabled();
    if ($rptAcctEnabled) {
    ?>
    <div class='reportSection formatPage'>

        <!--<div class='reportSectionLabel'  style="margin-bottom: 20px;">General options for display and print</div>-->
        <div style="margin-left: 0px;  <?=($sub != 'general') ? 'display: none;':''; ?>;">

        <? // this should not execute from CS Tools
        if (!$fromCSTool) {
            $prompt_for_reportingachdr = isl_strstr($_runtimeparams, "reportingachdr") ?  "checked" : "";
        ?>
        <div class='reportSectionLabel reportsmalllabel' style='margin-top: 0px;'><?=$text->GT('IA.ACCOUNTS')?>
         <span class='GL_FinancialReports_FormattingTab_Oscar cellhelp'></span>
        </div>
         <fieldset>

             <table>
                 <tr>
                     <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.REPORTING_ACCOUNTS')?></label></td>
                 <td class='reportFieldValue'>
                         <select name=".reportingachdrkey"
                         onchange="if (document.forms['mf'].elements['.reportingachdrkeyAsOf']) {document.forms['mf'].elements['.reportingachdrkeyAsOf'].value = this.value;}" >
                         <option value=''></option>
                            <?
                                 $gManagerFactory = Globals::$g->gManagerFactory;
                                 $reportingAcHdrMrg = $gManagerFactory->getManager('reportingacheader');
                                 //$allowedreportingacsetsubquery = ReportingACHeaderManager::PermittedReportingAccountSetsSubQuery();
                                 $params = array(
                                     'selects' => array('RECORDNO', 'HEADERID'),
                                   'filters' => array(
                                             array(
                                                 array('STATUS', '=', 'active'),
                            //                                                    array('HEADERID', 'INSUBQUERY', $allowedreportingacsetsubquery),
                                             ),
                                         ),
                                     'orders' => array(array('HEADERID', 'asc')),
                                 );
                                 $reportingAcHdr = $reportingAcHdrMrg->GetList($params);
                            foreach ( $reportingAcHdr as $val) {
                                    ?>
                                     <option value='<?echo ($val['RECORDNO']);?>'
                                        <?if ($_reportingachdrkey == $val['RECORDNO']) {
                                            echo "selected";
                                        }?>>
                                        <?echo ($val['HEADERID']);?></option>
                                    <?
                                    }
                            ?>
                        </select>
                        </td>
                        <td class="reportFieldValue indent1" style='vertical-align: top'>
                            <table style=''>
                             <tr>
                                 <td>
                                     <input type="checkbox" class="noborder" name="prompt_for_reportingacheaderkey"
                                     onclick="UpdateRuntimeParams('reportingachdr', this);" <?=$prompt_for_reportingachdr?>   >
                                 </td>
                                 <td class='checkboxalign'>
                                     <label><?=$text->GT('IA.PROMPT')?></label>
                                 </td>
                             </tr>
                            </table>
                        </td>
                        <td  style='vertical-align: text-top'>
                            <span class='GL_FinancialReports_FormattingTab_ReportingAccounts cellhelp'></span>
                        </td>
                    </tr>
                    <tr><td></td><td colspan="3"><label  style='white-space: nowrap'><?=$text->GT('IA.REPORT_OUTPUT_WILL_DISPLAY_USING_THESE_ACCOUNTS')?></label></td></tr>

                </table>
            </fieldset>
            <?
        }?>
        </div>
    </div>
    <?
    }?>


    <div class='reportSection formatPage'>

        <!--<div class='reportSectionLabel'  style="margin-bottom: 20px;">General options for display and print</div>-->
        <div style="margin-left: 0px;  <?=($sub != 'general') ? 'display: none;':''; ?>;">

            <div class='reportSectionLabel reportsmalllabel' style='margin-top: 0px;'><?=$text->GT('IA.NUMBERS')?>
            		<span class='GL_FinancialReports_FormattingTab_Numbers cellhelp'></span>
            </div>
                <fieldset>
                    <table>
                         <tr>
                            <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.NEGATIVE_NUMBERS')?></label></td>
                            <td class='reportFieldValue'>
                                <select name=".negnumber">
                                    <option value='P' <?if ($_negnumber == "P") {
                                        echo "selected";
                                    }?>>(1234)</option>
                                    <option value='L' <?if ($_negnumber == "L") {
                                        echo "selected";
                                    }?>>-1234</option>
                                    <option value='T' <?if ($_negnumber == "T") {
                                        echo "selected";
                                    }?>>1234-</option>
                                </select>
                            </td>
                         </tr>
                        <tr>
                            <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.NEGATIVE_NUMBERS_COLOR_PDF_ONLY')?></label></td>
                            <td class='reportFieldValue'>
                                <?
                                    $fval = 'neg';
                                    $fontColor = "_".$fval."color";
                                    $$fontColor = &Request::$r->$fontColor;
                                ?>
                                <select name=".<?=$fval;?>color" size="1">
                                    <?ShowFinOptions($$fontColor, $colorMap);?>
                                </select>
                            </td>
                        </tr>
                         <tr>
                            <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.ROUNDING')?></label></td>
                            <td class='reportFieldValue'>
                 				<select name=".rounding">
                				<?ShowFinOptions($_rounding,  $kRounding);?>
                				</select>
                            </td>
                         </tr>
                         <tr>
                            <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.ALIGN_CURRENCY_SYMBOL')?></label></td>
                            <td class='reportFieldValue'>
                                <select name=".align_currency">
                                    <option value='L' <?if ($_align_currency == "L") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.ALIGN_LEFT')?></option>
                                    <option value='F' <?if ($_align_currency == "F") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.FLOAT_WITH_VALUE')?></option>
                                </select>
                            </td>
                         </tr>
                            <?if (IsMCMESubscribed() && GLBookManager::IsConsolidationBook($_reportingBook, false)) {
                            ?>
                         <tr>
                            <td class='reportFieldLabel' class='formatSelectSpacing'><label><?=$text->GT('IA.SHOW_EXCHANGE_RATES')?></label></td>
                            <td class='reportFieldValue'>
                                <select  name=".showExchRate">
                                <?ShowFinOptions(
    $_showExchRate, $text->getBulkTokens([
                                    'DN' => 'IA.DO_NOT_DISPLAY',
                                    'ES' => 'IA.ENDING_SPOT_RATE',
                                    'WA' => 'IA.WEIGHTED_AVERAGE_RATE',
                                    'B' => 'IA.BOTH'
                                 ])
);?>
                             </select>
                            </td>
                         </tr>
                        <?
}?>
                 		    <tr>
                    			<td class="reportFieldLabel" style='vertical-align: top;'><label><div style='margin-top: 2px; text-align: right'><?=$text->GT('IA.ZERO_NUMBERS')?></div></label></td>
                    			<td class="reportFieldValue">
                    			    <table  cellpadding=0 cellspacing=0>
                                        <tr>
                                            <td> <!-- / here to align the damm checkbox -->
                                                <input type="checkbox" class="noborder" name="zeroWTrans" value="F"
                                                <?=($_zeroWTrans == 'T' ? 'CHECKED' : ''); ?>
                        				        onchange="document.mf.elements['.zeroWTrans'].value=(document.mf.elements['zeroWTrans'].checked?'T':'F')">
                                            </td>
                                            <td class='checkboxalign'>
                    				            <label style='white-space: nowrap'><?=$text->GT('IA.DISPLAY_ZERO_BALANCE_WITH_ACTIVITY')?>&nbsp;</label>
            				                </td>
                                            <td>
                                                <span class='Zero_balance_rows cellhelp'></span>
                                            </td>
                                        </tr>
                                    </table>
                    			</td>
                    		</tr>
                 		    <tr>
                    			<td class="reportFieldLabel" ><label>&nbsp</label></td>
                    			<td class="reportFieldValue">
                    			    <table cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                        <input type="checkbox" class="noborder" id="zeroactual" name="zeroactual" value="F"
                                            <?=($_zeroactual=='T')?'checked':''?>
                                            onchange="document.mf.elements['.zeroactual'].value=jq('#zeroactual')[0].checked?'T':'F'"   >
                    			     </td><td class='checkboxalign'>
                    				<label style='white-space: nowrap'><?=$text->GT('IA.DISPLAY_ZERO_FOR_FUTURE_PERIODS')?></label>
                    				</td></tr></table>
                    			</td>
                    		</tr>
                    		<tr>
                    			<td class="reportFieldLabel" ><label>&nbsp</label></td>
                    			<td class="reportFieldValue">

                    			    <table cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                        <input type="checkbox" class="noborder" name="hidezerobalcol"  value="T"
                                            <?=(($_hidezerobalcol == 'F') ? 'CHECKED' : ''); ?>
                                            onchange="document.mf.elements['.hidezerobalcol'].value=document.mf.elements['hidezerobalcol'].checked?'F':'T'" >
                    			     </td><td class='checkboxalign'>
                    				<label style='white-space: nowrap'><?=$text->GT('IA.DISPLAY_ZERO_BALANCE_COLUMNS')?></label>
                    				</td></tr></table>
                    			</td>
                    		</tr>
                        <tr>
                            <td class="reportFieldLabel" ><label>&nbsp</label></td>
                            <td class="reportFieldValue">

                                <table cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                            <input type="checkbox" class="noborder" name="displaydash"  value="T"
                                                <?=(($_displaydash == 'T') ? 'CHECKED' : ''); ?>
                                                   onchange="document.mf.elements['.displaydash'].value = document.mf.elements['displaydash'].checked ? 'T':'F'" >
                                        </td><td class='checkboxalign'>
                                            <label style='white-space: nowrap'><?=$text->GT('IA.DISPLAY_DASH_FOR_ZERO')?></label>
                                        </td></tr></table>
                            </td>
                        </tr>

                    		</table>
                 </fieldset>
             </div>

            <div class='reportSection' style="margin-left: 0px; ;  <?=($sub != 'general') ? 'display: none;':''; ?>">
            <div class='reportSectionLabel toptext reportsmalllabel' style='margin-bottom: 12px'><?=$text->GT('IA.DIMENSIONS')?>
                <span class='GL_FinancialReports_FormattingTab_Dimensions cellhelp'></span>
            </div>
                <fieldset>
                    <table>
                         <tr>
                            <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.SHOW_DEPARTMENTS')?></label></td>
                            <td class='reportFieldValue' >
                                 <select name="showDeptIDNAME" id="showDeptIDNAME" onchange="document.mf.elements['.showDeptIDNAME'].value = jq('#showDeptIDNAME')[0].value;">
                                    <option value='N' <?if (!isset($_showDeptIDNAME) || $_showDeptIDNAME == "N" || $_showDeptIDNAME == '') {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.NAME_ONLY')?></option>
                                    <option value='I' <?if ($_showDeptIDNAME == "I") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.ID_ONLY')?></option>
                                    <option value='T' <?if ($_showDeptIDNAME == "T") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.NAME_AND_ID')?></option>
                                    </select>
                            </td>
                         </tr>
                            <tr>
                            <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.SHOW_LOCATIONS')?></label></td>
                            <td class='reportFieldValue' >
                                <select name="showLocIDNAME" id="showLocIDNAME"  onchange="document.mf.elements['.showLocIDNAME'].value = jq('#showLocIDNAME')[0].value;">
                                    <option value='N' <?if (!isset($_showLocIDNAME) || $_showLocIDNAME == "N" || $_showLocIDNAME == '') {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.NAME_ONLY')?></option>
                                    <option value='I' <?if ($_showLocIDNAME == "I") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.ID_ONLY')?></option>
                                    <option value='T' <?if ($_showLocIDNAME == "T") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.NAME_AND_ID')?></option>
                                </select>
                            </td>
                         </tr>
             		 <?
                        $showInactiveDimRow = false;
                     foreach( $finDimInfo as $key => $val) {
                    if(in_array($key, array('department','location'))) {
                        continue;
                    }
                    $showInactiveDimRow = true;
                    break;
                }
                if($showInactiveDimRow) { ?>
                         <tr>
                            <td class='reportFieldLabel formatSelectSpacing'><label><?=$text->GT('IA.SHOW_ALL_OTHERS')?></label></td>
                            <td class='reportFieldValue'>
                                <select name="showDimIDNAME"  id="showDimIDNAME"  onchange="document.mf.elements['.showDimIDNAME'].value = jq('#showDimIDNAME')[0].value;">
                                    <option value='N' <?if (!isset($_showDimIDNAME) || $_showDimIDNAME == "N" || $_showDimIDNAME == '') {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.NAME_ONLY')?></option>
                                    <option value='I' <?if ($_showDimIDNAME == "I") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.ID_ONLY')?></option>
                                    <option value='T' <?if ($_showDimIDNAME == "T") {
                                        echo "selected";
                                    }?>><?=$text->GT('IA.NAME_AND_ID')?></option>
                                </select>
                            </td>
                         </tr>
                <?
                }?>

                		    <tr>
                    			<td class="reportFieldLabel" style='vertical-align: top;' ><label><div style='margin-top: 2px;; text-align: right'><?=$text->GT('IA.SORTING')?></div></label></td>
                    			<td class="reportFieldValue">
                    			    <div style='display: inline-block'>
                        			    <table  style='display: inline-table'cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                        <input type="checkbox" class="noborder" name="deptlocgrpsort" value="<?= util_encode($_deptlocgrpsort) ?>" onclick="this.value=this.checked?'T':''; document.mf.elements['.deptlocgrpsort'].value=this.value;"  <?=($_deptlocgrpsort=='T')?'checked':''?> >
                        				</td><td class='checkboxalign'>
                        				<label  style='white-space: nowrap'><?=$text->GT('IA.ALPHABETICALLY_SORT_DEPARTMENT_SLASH_LOCATION_GROUP')?></label>
                        				</td></tr></table>
                        				<div style='display: inline-block; vertical-align: top;' id='alpha_sort_dims'></div>
                    				</div>

                    			</td>
                    		</tr>
                     </table>

                 </fieldset>
             </div>
        </div>
    <div class='reportSection  formatPage'>
        <div style="margin-left: 0px; <?=($sub != 'setup') ? 'display: none;':''; ?>">
            <div class='reportSectionLabel reportsmalllabel' style='margin-top: 0px;'><?=$text->GT('IA.PAGE_HEADER')?>
                <span class='GL_FinancialReports_FormattingTab_PageHeader cellhelp'></span>
            </div>
                <fieldset>
                    <table>
                 		    <tr>
                    			<td class="reportFieldLabel" ><label><?=$text->GT('IA.LOGO')?></label></td>
                    			<td class="reportFieldValue" style='vertical-align: top'>
                    			    <table cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                        			    <input type="hidden" name=".logo" value="<?if ($_logo=='T') {
                        			        echo 'T';
                                        } else {
                        			        echo'F';
                                        }?>">
                                        <input type="checkbox" class="noborder" name=".logo_Tmp" value="foo" onclick="if (this.checked) jq('.logoalign').show(); else jq('.logoalign').hide(); document.getElementsByName('.logo')[0].value= this.checked?'T':'F';"  <?=($_logo=='T')?'checked':''?> >
                        				</td><td class='checkboxalign' style='vertical-align: top' >
                        				<label><?=$text->GT('IA.INCLUDE_LOGO')?> &nbsp;</label>
                    				</td>

                                    <td class='reportFieldLabel logoalign'  style=' padding-left: 20px; padding-top: 3px; vertical-align: baseline; <?if ($_logo!='T') {echo ' display: none;"';
}?>'><label><?=$text->GT('IA.ALIGN')?></label></td>
                                    <td class='reportFieldValue logoalign' style='vertical-align: top <?if ($_logo!='T') {echo '; display: none;"';
}?>' >
                         			    <input type="hidden" name=".logoalign" value="<?echo $_logoalign == '' ? 'L' : $_logoalign ;?>">
                                        <select id='logoalign' name='logoalign' onchange="document.getElementsByName('.logoalign')[0].value= jq('#logoalign')[0].value;">
                                            <option value='L' <?if ($_logoalign == "L") { echo "selected";
}?>><?=$text->GT('IA.LEFT')?></option>
                                            <option value='M' <?if ($_logoalign == "M") { echo "selected";
}?>><?=$text->GT('IA.MIDDLE')?></option>
                                            <option value='R' <?if ($_logoalign == "R") { echo "selected";
}?>><?=$text->GT('IA.RIGHT')?></option>
                                        </select>
                                    </td>
                    			</tr></table>
                    			</td>


                    		</tr>

                         <tr>
                            <td class='reportFieldLabel' ><label><?=$text->GT('IA.REPORT_TITLE')?></label></td>
                            <td class='reportFieldValue formatInputSpacing' colspan='3'>
                                <input type=text name=".title" value="<?=$title?>" size=50 maxlength="<?=$fs_title?>">
                            </td>
                         </tr>
                            <tr>
                            <td class='reportFieldLabel '><label><?=$text->GT('IA.SUBTITLE_1')?></label></td>
                            <td class='reportFieldValue formatInputSpacing' colspan='3'>
    				            <input type=text name=".title2" value="<?=$title2?>" size=50 maxlength="<?=$fs_title2?>">
                            </td>
                         </tr>
                         <tr>
                            <td class='reportFieldLabel '  ><label><?=$text->GT('IA.SUBTITLE_2')?></label></td>
                            <td class='reportFieldValue formatInputSpacing' colspan='3'>
                                <input type=text name=".title3" value="<?=$title3?>" size=50 maxlength="<?=$fs_title3?>">
                            </td>
                         </tr>
                         <tr>
                            <td class='reportFieldLabel ' ><label><?=$text->GT('IA.TITLE_COMMENT')?></label></td>
                            <td class='reportFieldValue formatSelectSpacing' colspan='3'>
                                <input type="text" name=".titlecomment"  value="<?=$titlecomment?>" size="50" maxlength="50">
                               <div class="reportHelp"><?=$text->GT('IA.DISPLAYS_ALIGNED_LEFT_JUST_ABOVE_REPORT_BODY')?>.</div>
                             </td>
                         </tr>
                         </table><table>
                            <tr>
                    			<td class="reportFieldLabel" style='vertical-align: top;' ><label><div style='margin-top: 2px;; text-align: right'><?=$text->GT('IA.COMPANY_TITLE')?></div></label></td>
                    			<td class="reportFieldValue"  style='white-space: nowrap' colspan='3'>
                    			    <div style='display: inline-block; white-space: nowrap'>
                        			    <table  style='display: inline-table;'cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                        <input type="checkbox" class="noborder" name="displocascotitle" value="<?=util_encode($_displocascotitle)?>" onclick="this.value=this.checked?'T':''; document.mf.elements['.displocascotitle'].value=this.value;"  <?=isFieldChecked($_displocascotitle)?> >
                        				</td><td class='checkboxalign'>
                        				    <label  style='white-space: nowrap'><?=$text->GT('IA.DISPLAY_LOCATIONS_AS_COMPANY_TITLE_ON_INDIVIDUAL_REPORTS')?></label>
                        				</td></tr></table>
                        				<div style='display: inline-block; vertical-align: top;' id='show_as_title_on_individual_reports'></div>
                    				</div>

                    			</td>
                    		</tr>
                        <?
                        $enableCustomHdr = GetPreferenceForProperty($kGLid, 'CUSTOM_HEADER');
                        if(!empty($enableCustomHdr) && $enableCustomHdr == 'T') {
                            ?>
                                 <tr>
                                    <td class='reportFieldLabel formatInputSpacing' ><label><?=$text->GT('IA.ALIGN_DEPARTMENT_SLASH_LOCATION_TITLE')?></label></td>
                                    <td class='reportFieldValue' colspan='3'>
                                        <div style='display: block-inline'>
                                            <select name='.header_alignment'>
                                                <option value='L' <?if ($_header_alignment == "L") { echo "selected";
}?>><?=$text->GT('IA.LEFT')?></option>
                                                <option value='M' <?if ($_header_alignment == "M") { echo "selected";
}?>><?=$text->GT('IA.MIDDLE')?></option>
                                                <option value='R' <?if ($_header_alignment == "R") { echo "selected";
}?>><?=$text->GT('IA.RIGHT')?></option>
                                            </select>
                                            <div style='display: inline-block; vertical-align: top;' id='align_custom_headers'></div>
                                        </div>
                                   </td>
                                 </tr>
                            <?
                        }?>


                       </table>
                 </fieldset>
             </div>

            <div class='reportSection' style='margin-left: 0px; <?=($sub != 'setup') ? 'display: none;':''; ?>'>
                <div class='reportSectionLabel reportsmalllabel'><?=$text->GT('IA.PAGE_FOOTER')?>
                    <span class='GL_FinancialReports_FormattingTab_PageFooter cellhelp'></span>
                </div>
                <fieldset>
                      <table>
                            <tr>
                                <td class='reportFieldLabel' style='vertical-align: top'><input type="radio" id="standard_footer_type" name=".displayfooteras" value="S" <? echo $_displayfooteras != 'F' ? 'checked' : ''?>></td>
                                <td><label for="displayfooteras"><?=$text->GT('IA.SHOW_FOOTER_TEXT')?></label></td>
                            </tr>
                            <tr>
                                <td class='reportFieldLabel' style='vertical-align: top'></td>
                                <td class='reportFieldValue formatInputSpacing'>
                                <textarea name=".footertext" cols=50 rows=3 maxlength="400"><? if ($_footertext) { echo $_footertext; } ?></textarea>
                               </td>
                           </tr>
                          <tr>
                              <td class='reportFieldLabel' >
                                  <input type="radio" id="filters_footer_type" name=".displayfooteras" value="F" <? echo $_displayfooteras == 'F' ? 'checked' : ''?>>
                              </td>
                              <td><label for="displayfooteras"><?=$text->GT('IA.SHOW_FILTERS_TAB_AND_PROMPT_SELECTIONS')?></label><br></td>
                          </tr>
                           <tr>
                                <td class='reportFieldLabel' ><label><?=$text->GT('IA.ALIGN_FOOTER')?></label></td>
                                <td class='reportFieldValue formatSelectSpacing' style='padding-top: 8px;'>
                                    <select name='.footer_alignment'>
                                        <option value='L' <?if ($_footer_alignment == "L") { echo "selected";
}?>><?=$text->GT('IA.LEFT')?></option>
                                        <option value='M' <?if ($_footer_alignment == "M") { echo "selected";
}?>><?=$text->GT('IA.MIDDLE')?></option>
                                        <option value='R' <?if ($_footer_alignment == "R") { echo "selected";
}?>><?=$text->GT('IA.RIGHT')?></option>
                                    </select>
                               </td>
                            <tr>
                    			<td class="reportFieldLabel" ><label>&nbsp</label></td>
                    			<td class="reportFieldValue" colspan='3'>
                    			    <table cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                    <input type="checkbox" class="noborder" name=".footerallpages" value="T" <?=$fallpagesChecked;?> >
                    				</td><td class='checkboxalign'>
                    				<label style='white-space: nowrap'><?=$text->GT('IA.INCLUDE_FOOTER_TEXT_ON_ALL_PAGES')?></label>
                    				</td></tr></table>
                    			</td>
                    		</tr>

                    		<tr>
                    			<td class="reportFieldLabel"><label>&nbsp</label></td>
                    			<td class="reportFieldValue"   style='white-space: nowrap' colspan='3'>
                    			    <table style='display: inline-table' cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                    <input type="checkbox" class="noborder" name=".pgnumber" value="T" <?=$pgnumberChecked;?> >
                    				</td><td class='checkboxalign'>
                    				<label style='white-space: nowrap'><?=$text->GT('IA.INCLUDE_PAGE_NUMBER')?></label>
                    				</td></tr></table>

                    				<table style='margin-left: 20px; display: inline-table' cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                    <input type="checkbox" class="noborder" name=".reportdate" value="T" <?=$reportdateChecked;?>>
                    				</td><td class='checkboxalign'>
                    				<label style='white-space: nowrap'><?=$text->GT('IA.INCLUDE_REPORT_DATE')?></label>
                    				</td></tr></table>

                    				<table style='margin-left: 20px; display: inline-table' cellpadding=0 cellspacing=0><tr><td> <!-- / here to align the damm checkbox -->
                                    <input type="checkbox" class="noborder" name=".reporttime" value="T" <?=$reporttimeChecked;?>>
                    				</td><td class='checkboxalign'>
                    				<label style='white-space: nowrap'><?=$text->GT('IA.INCLUDE_REPORT_TIME')?></label>
                    				</td></tr></table>
                    			</td>
                    		</tr>
                       </table>
                   </fieldset>
             </div>

        <div class='reportSection  formatPage' style='margin-left: 0px;  <?=($sub != 'setup') ? 'display: none;':''; ?>'>
            <div class='reportSectionLabel reportsmalllabel'><?=$text->GT('IA.FONTS')?>
                <span class='GL_FinancialReports_FormattingTab_Fonts cellhelp'></span>
            </div>
            <fieldset>
                <TABLE cellpadding="3px" cellspacing="1" style="background-color: #AEC6E0; border: 1px solid #AEC6E0; border-spacing: 1px; border-collapse: separate;">
                    <thead>
                    <tr class="multiline_bg_beige">
                        <th align="right"  style='color:  #355E8B; padding-top: 8px; padding-bottom: 8px;'>&nbsp;</th>
                        <th align="center" style='color:  #355E8B'><?=$text->GT('IA.FONT')?></th>
                        <th align="center" style='color:  #355E8B'><?=$text->GT('IA.SIZE')?></th>
                        <th align="center" style='color:  #355E8B'><?=$text->GT('IA.COLOR')?><br/><?=$text->GT('IA.PDF_ONLY')?></th>
                        <th align="center" style='color:  #355E8B'><?=$text->GT('IA.BOLD')?></th>
                        <th align="center" style='color:  #355E8B'><?=$text->GT('IA.ITALIC')?></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?
                    // Show custom header fonts only if it is enabled
                    if(!empty($enableCustomHdr) && $enableCustomHdr == 'T') {
                        $fontArray['IA.CUSTOM_HEADERS'] = 'custhdr';
                    }
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $trstyle = IALayoutManager::fullCSSHover()
                        ? 'class="multiline_bg_beige_rollover"'
                        : 'class="multiline_bg_beige" onmouseover="this.style.backgroundColor=\'#ffffff\'" onmouseout="this.style.backgroundColor=\'\'"';

                    $altColor = '#FFFFFF';
                    foreach( $fontArray as $fkey => $fval ) {
                        $altColor = ($altColor === '#FFFFFF') ? '#EDF2F9' : "#FFFFFF";

                        $fontVar = "_".$fval."font";
                        $fontSize = "_".$fval."size";
                        $fontType = "_".$fval."type";
                        $fontWeight = "_".$fval."weight";
                        $fontColor = "_".$fval."color";

                        $$fontVar = &Request::$r->$fontVar;
                        $$fontSize = &Request::$r->$fontSize;
                        $$fontWeight = &Request::$r->$fontWeight;
                        $$fontType = &Request::$r->$fontType;
                        $$fontColor = &Request::$r->$fontColor;
                        $$fontSize = &Request::$r->$fontSize;
                        $$fontWeight = &Request::$r->$fontWeight;
                        $$fontType = &Request::$r->$fontType;
                        $$fontColor = &Request::$r->$fontColor;

                        if ($fval == 'rp1' || $fval == 'rp2' || $fval == 'rp3' || $fval == 'cny') {
                            $sMap = $sizeMap;
                            if (!$$fontSize) { $$fontSize = '14';
                            }
                        } else if ($fval == 'custhdr') {
                            $sMap = $smallSizeMap;
                            if (!$$fontSize) { $$fontSize = '7';
                            }
                        } else {
                            $sMap = $smallSizeMap;
                            if (!$$fontSize) { $$fontSize = '10';
                            }
                        }

                        $fontWeightChecked = ($$fontWeight == 'bold') ? 'checked' : '';
                        $fontTypeChecked = ($$fontType == 'italic') ? 'checked' : '';

                        $keyName=$fkey;
                        if ($keyName === 'Body') {
                            if ($_definedby == 'S' || $_definedby == '') {
                                $keyName = 'IA.EXPAND_BY_HEADINGS_AND_COLUMN_BODY';
                            } else {
                                $keyName = 'IA.EXPAND_BY_HEADINGS';
                            };
                        };

                        ?>
                        <tr  style='background-color: <?=$altColor?>'>
                            <td style='color: #355E8B; background-color:#e4ebf5; padding-right: 20px' align="left" nowrap><?=$text->GT($keyName)?></td>
                            <td nowrap align="center" >
                                <select name=".<?=$fval;?>font" size="1">
                                    <?ShowFinOptions($$fontVar, $fontMap);?>
                                </select>
                            </td>
                            <td  nowrap align="center">
                                <select name=".<?=$fval;?>size" size="1">
                                    <? ShowFinOptions($$fontSize, $sMap);?>
                                </select>
                            </td>

                            <td  nowrap align="center">
                                <select name=".<?=$fval;?>color" size="1">
                                    <?ShowFinOptions($$fontColor, $colorMap);?>
                                </select>
                            </td>

                            <td style='padding-right: 5px; padding-left: 5px;  ' nowrap align="center">
                                <input type="checkbox" class="noborder" name=".<?=$fval;?>weight" value="bold" <?=$fontWeightChecked;?>>
                            </td>
                            <td style='padding-right: 5px; padding-left: 5px; ' nowrap align="center">
                                <input type="checkbox" class="noborder"  name=".<?=$fval;?>type" value="italic" <?=$fontTypeChecked;?>>
                            </td>
                        </tr>
                    <?
                    } ?>
                    </tbody>
                </table>


                <p style='text-align: left; color: #355E8B'>
                    <?=$text->GT('IA.EXPAND_BY_HEADINGS_DISPLAY_WHEN_EXPANDING_ROWS_OR_')?>
            </fieldset>
        </div>


        <div class='reportSection '  style='margin-left: 0px;  <?=($sub != 'setup') ? 'display: none;':''; ?>'>
            <div id='reportSectionLabel_pageSettings' class='reportSectionLabel reportsmalllabel'><?=$text->GT('IA.PAGE_SETTINGS')?>
                <span class='GL_FinancialReports_FormattingTab_PageSettings cellhelp'></span>
            </div>
            <fieldset>
                <input type='hidden' id='navToPageSection' name='navToPageSection' value='F'>
                <?if (Request::$r->navToPageSection == 'T') {?>
                <script>
                    jq(document).ready(function () {
                        if (isIE) {
                            window.setTimeout(function() {
                                document.getElementById('reportSectionLabel_pageSettings').scrollIntoView();
                            }, 1);
                        } else {
                            document.getElementById('reportSectionLabel_pageSettings').scrollIntoView();
                        }
                    });
                    <?
}?>
                </script>
                <table>
                    <tr>
                        <td class='reportFieldLabel' style='vertical-align: top; padding-top: 5px;'><label><?=$text->GT('IA.OPTIONS')?></label></td>
                        <td class='reportFieldValue formatInputSpacing' style='white-space: nowrap' colspan='2'>
                            <table style='border-spacing:2px; border-collaspe:separate; '><tr>
                                    <?global $fromCSTool?>
                                    <td style='padding-left: 5px;' > <!-- / here to align the damm checkbox -->
                                        <input type="radio" class="noborder" name=".definedby" value="S" <?if ( !$_definedby || $_definedby == "S" || $fromCSTool ) { echo "checked";
}?>  onclick="document.getElementById('navToPageSection').value='T'; FRL_RefreshPageLayoutSettings();">
                                    </td><td style='padding-left: 5px'>
                                        <label><?=$text->GT('IA.DEFAULT_PAGE_SETTINGS')?></label>
                                    </td>
                                </tr><tr>
                                    <td style='padding-left: 5px;' >
                                        <input type="radio" class="noborder" <? if ($fromCSTool) { echo "disabled";
}  ?> name=".definedby" value="F" <?if ($_definedby == "F") { echo "checked";
}?> onclick="document.getElementById('navToPageSection').value='T'; FRL_RefreshPageLayoutSettings(this);">
                                    </td><td style='padding-left: 5px'>
                                        <label><?=$text->GT('IA.FIT_TO')?> </label>
                                        <div style='padding-left: 4px; display: inline-block; margin-top: -2px;'>
                                            <select name=".noofpages" <?=($_definedby != "F") ? 'disabled' : '';?>>
                                                <?=GenerateOptions(25, $_noofpages);?>
                                            </select><label> <?=$text->GT('IA.PAGE_S_WIDE')?></label>
                                            <input type="hidden" name=".forceDefaulting" value="0">
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style='padding-left: 5px;'>
                                        <input type="radio" class="noborder" <? if ($fromCSTool) { echo "disabled";
}  ?> name=".definedby" value="U" <?if ($_definedby == "U") { echo "checked";
}?> onclick="document.getElementById('navToPageSection').value='T'; FRL_handleCustomLayout(this);">
                                    </td><td style='padding-left: 5px'>
                                        <label><?=$text->GT('IA.CUSTOM_PAGE_SIZE_COLUMN_WIDTHS_AND_COLUMN_BREAKS')?></label>
                                    </td>
                                </tr>

                            </table>
                        </td>
                    </tr>

                    <?
                    // calculate scaling text
                    $sysDefined =  ( $_definedby == 'S' || $_definedby == '' );
                    $_scale = ( !isset($_scale) || $_scale == '' || $sysDefined || $_forceDefaulting == '1' ? 'P' : $_scale );
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $scale_text = ( $_scale == 'P' ? 'Points' : 'Inches' );
                    ?>

                    <tr><td colspan='3'>&nbsp;</td></tr>

                    <tr class='formatPage'>
                        <td class='reportFieldLabel' ><label><?=$text->GT('IA.PAGE_ORIENTATION')?></label></td>
                        <td class='reportFieldValue formatSelectSpacing'>
                            <select name='.orientation' onchange='FRL_RefreshPageLayoutSettings();' <?if($_definedby == 'F') { echo 'disabled';
}?>>
                                <option value="P" <?if ($_orientation == "P") { echo "selected";
}?>><?=$text->GT('IA.PORTRAIT')?></option>
                                <option value="L" <?if ($_orientation == "L") { echo "selected";
}?>><?=$text->GT('IA.LANDSCAPE')?></option>
                            </select>
                        </td>
                        <td rowspan="3" style='width: 70%;'>
                            <table><tr>
                                    <td class='reportFieldLabel' style='min-width: 50px'><label><?=$text->GT('IA.MARGINS')?></label></td>
                                    <td>
                                        <?$defaultState = ( $_definedby == 'S' || $_definedby == 'F' || $_definedby == '' ) ? 'disabled' : '';?>
                                        <table <?=($defaultState =='disabled') ? ' class="margintabledispable"' : ''?>>
                                            <tr>
                                                <td colspan='6' class="reportFieldLabel"  style='padding-right: 0px; text-align: center; min-width: 0px'><label><?=$text->GT('IA.TOP')?></label></td>
                                            </tr>
                                            <tr>
                                                <td colspan='6' style='text-align: center;' class="reportFieldValue"  >
                                                    <input style='text-align: center;' type="text" id="topmargin"  name=".topmargin" value="<?=$_topmargin?>" size="4" maxlength="4" <?=$defaultState;?> >
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style='width: 100%; '><div  style='text-align:center'><table style='display: inline-table;'>
                                                            <tr>
                                                                <td class="reportFieldLabel"   style='min-width: 30px' ><label><?=$text->GT('IA.LEFT')?></label></td>
                                                                <td class="reportFieldValue" >
                                                                    <input type="text" id="leftmargin" name=".leftmargin" value="<?=$_leftmargin?>" size="4" maxlength="4" <?=$defaultState;?> >
                                                                </td>
                                                                <td style='width: 60px;'></td>
                                                                <td class="reportFieldValue"   style='white-space: nowrap;' >
                                                                    <input style='text-align: right; margin-right: 5px;' type="text" id="rightmargin" name=".rightmargin" value="<?=$_rightmargin?>" size="4" maxlength="4" <?=$defaultState;?> >
                                                                </td>
                                                                <td class="reportFieldLabel"  style='min-width: 0px' ><label><?=$text->GT('IA.RIGHT')?></label></td>
                                                            </tr>
                                                        </table></div></td>
                                            </tr>
                                            <tr>
                                                <td colspan='6' class="reportFieldValue"  style='text-align: center;' >
                                                    <input style='text-align: center;' type="text" id="bottommargin" name=".bottommargin" value="<?=$_bottommargin?>" size="4" maxlength="4" <?=$defaultState;?> >
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan='6' class="reportFieldLabel"   style='padding-right: 0px; text-align: center; min-width: 0px'><label><?=$text->GT('IA.BOTTOM')?></label>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr></table>
                        </td>
                    </tr>
                    <tr class='formatPage'>
                        <td class='reportFieldLabel' ><label><?=$text->GT('IA.MEASUREMENT')?></label></td>
                        <td class='reportFieldValue formatSelectSpacing'>
                            <select name=".scale" id="scaleIP" onchange='FRL_handleScaling(jq(this).val())' <?if($_definedby == 'F' || $_definedby == 'S') { echo 'disabled';
}?>>
                                <option value="P" <?if (!$_scale || $_scale == "P") { echo "selected";
}?>><?=$text->GT('IA.POINTS')?></option>
                                <option value="I" <?if ($_scale == "I") { echo "selected";
}?>><?=$text->GT('IA.INCHES')?></option>
                            </select>
                        </td>
                    </tr>
                    <tr class='formatPage'>
                        <td class='reportFieldLabel ' ><label><?=$text->GT('IA.PAGE_SIZE')?></label></td>
                        <td class='reportFieldValue formatSelectSpacing'>
                            <select name=".pagesize" size="1" <?=($_definedby != 'F') ? $defaultState : 'disabled';?>
                                    onchange="document.mf.elements['.pagesize'].value = this[this.selectedIndex].value;FRL_RefreshPageLayoutSettings();" >
                                <?ShowFinOptions($_pagesize, $kPageSizeMap);?>
                            </select>
                            <span id="pageText"></span>
                        </td>
                    </tr>
                </table>
            </fieldset>
        </div>



         <div class='reportSection'  style='margin-left: 0px;  <?=($sub != 'setup') ? 'display: none;':''; ?>'>

            <?if ($_definedby == 'S' || $_definedby == '' || $_definedby == 'F' ) {?>
            <div class='reportSectionLabel reportsmalllabel'><?=$text->GT('IA.COLUMN_OPTIONS_AND_FONTS')?>
            <?
} else {?>
            <div class='reportSectionLabel reportsmalllabel'><?=$text->GT('IA.COLUMN_OPTIONS_BREAKS_AND_FONTS')?>
            <?
}?>
             <span class='GL_FinancialReports_FormattingTab_ColumnOptions cellhelp'></span>
         </div>


             <fieldset>
                       <table>
                           <tr>
                               <td class='' ><label></label></td>
                         <td class='reportFieldValue formatInputSpacing'>
                             <table cellpadding=0 cellspacing=0>

                                 <tr><td> <!-- / here to align the damm checkbox -->
                                         <input type="checkbox" class="noborder" name="hideCFltrTitle" value="T" <?=($_hideCFltrTitle === 'T' ? 'CHECKED' : ''); ?> onclick="this.value=(this.checked ? 'T':'F'); document.mf.elements['.hideCFltrTitle'].value=this.value;" >
                                     </td><td class='checkboxalign'>
                                         <label style='white-space: nowrap' ><?=$text->GT('IA.HIDE_FILTER_DETAILS_ON_COLUMN_TITLES')?></label>
                                         <span class='GL_FinancialReports_FormattingTab_HideFilterDetails cellhelp'></span>
                                     </td></tr>
                                    <tr><td> <!-- / here to align the damm checkbox -->
                                         <input type="checkbox" class="noborder" name="rfcd" value="T" <?=($_rfcd == 'F' ? '' : 'CHECKED'); ?> onclick="this.value=(this.checked ? 'T':'F'); document.mf.elements['.rfcd'].value=this.value;" >
                                     </td><td class='checkboxalign'>
                                            <?
                                             $repeatAccountOrDimension = $_accountOrDimensionRows === 'account' ? 'IA.REPEAT_ACCOUNT_NAME_SLASH_NUMBER_AS_1_ST_COLUMN_ON' : 'IA.REPEAT_DIMENSION_NAME_SLASH_ID_AS_1_ST_COLUMN_ON_REPORTS';
                                            ?>
                                         <label style='white-space: nowrap' ><?=$text->GT($repeatAccountOrDimension)?></label>
                                     </td></tr></table>
                         </td>
                     </tr>
                 </table>
                    <? EditReportGeneratePageLayout(); ?>
             </fieldset>
        </div>
    </div>

<?
}

function EditReportGeneratePageSliceAndDice()
{
    global $fromCSTool, $companyBooks, $structureBookArray, $strctureRecArray;
    global $finDimInfo, $prompt_for_reportbookChecked, $prompt_for_acctgrp, $prompt_for_ownerKeyChecked, $prompt_for_dateChecked, $prompt_for_showpreviewdateChecked;
    $text = TextHelper::getInstance(FinancialReportWizard::class);
    $_r                      = &Request::$r->_r;
    $_reportingBook          = &Request::$r->_reportingBook;
    $_industry               = &Request::$r->_industry;
    $_asofdate               = &Request::$r->_asofdate;
    $_name                   = &Request::$r->_name;
    $_ownerKey               = &Request::$r->_ownerKey;
    $_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
    $_exclSubs               = &Request::$r->_exclSubs;
    $_runtimeparams          = &Request::$r->_runtimeparams;
    $_showInactiveDept       = &Request::$r->_showInactiveDept;
    $_showInactiveLoc        = &Request::$r->_showInactiveLoc;
    $_ownershipStructure     = &Request::$r->_ownershipStructure;    

    GenerateDimensionAndAccountMaps();

    EditReportHeaderSeperator();
    translateReportingBooksDBValue($companyBooks);
    ?>
        <div class='reportSection'>
                <div class='reportSectionLabel toptext'><?=$text->GT('IA.SPECIFY_THE_DATA_TO_INCLUDE_IN_YOUR_REPORT')?></div>
                <div class="reportSectionLabelText toptext">
                     <P>
                         <?=$text->GT('IA.SLICE_AND_DICE_YOUR_DATA_ANY_WAY_YOU_WANT_SELECT_B')?>
                </div>
                <fieldset>
                    <table style='width: auto'>
                    <? //if (!$fromCSTool) {
                        $runtimeparams = isl_htmlspecialchars($_runtimeparams);
                        $fs_runtimeparams = FieldSize('reportinfo.runtimeparams');
                    ?>
                    <tr>
                            <td class='reportFieldLabel' style='vertical-align: middle;'><label><?=$text->GT('IA.AS_OF_DATE')?></label></td>
                            <td class='reportFieldValue' style='width: auto; white-space: nowrap'>
                                <div class="requiredOuter" style='position: static; display: inline-block;'>
                                    <?
                                    $params = array();
                                    $params["varname"] = ".asofdate";
                                    $params["value"] = ($_asofdate != '' ? FormatDateForStorage($_asofdate) : '');
                                    $params["size"] = "12";
                                    $params["showPlaceholderText"] = $text->GT('IA.TODAY');
                                    $params["maxlength"] = "200";
                                    $params['onchange'] = ";jq('#asofdate_headermodified').val('false');";
                                    $ctrl = new DateControl($params);
                                    $ctrl->Show();
                                    ?>
                                </div>
                                <div style="padding-top: 7px; display: inline-block; padding-left: 2px;"><span class='GL_FinancialReports_FiltersTab_AsOfDate cellhelp' style='width: 21px'></span></div>
                            </td>
                            <td class="reportFieldValue indent1" style='vertical-align: middle'>
                 			    <table style=''><tr><td> <!-- / here to align the damm checkbox -->
            					<input type="checkbox" class="noborder nomargin" name="prompt_for_date"
            					       onclick="UpdateRuntimeParams('asofdate', this); UpdateShowTimePeriod(this);" <?=$prompt_for_dateChecked?>   >
                				</td><td class='checkboxalign'>
            					 <label><?=$text->GT('IA.PROMPT')?></label>
                            </td>
                                        <td><span class='GL_FinancialReports_FiltersTab_Asof_Prompt cellhelp' style='width: 21px'></span></td>
                                        <td>
                                            <input type="checkbox" class="noborder nomargin" id='prompt_for_showpreviewdate' name="prompt_for_showpreviewdate" style="margin-left: 20px"
                                                   onchange="UpdateRuntimeParams('showpreviewdate', this);" <?=$prompt_for_showpreviewdateChecked?>   >
                                        </td>
                                        <td class='checkboxalign'>
                                            <label><?=$text->GT('IA.SHOW_TIME_PERIOD')?></label>
                                        </td>
                                        <td><span class='GL_FinancialReports_FiltersTab_ShowTimePeriod cellhelp' style='width: 21px'></span></td>

                                    </tr></table>
                            </td>
                            <td style='width: 100%'></td>
                         </tr>
                         <tr>
                             <td class='reportFieldLabel'><label> </label></td>
                             <td colspan='3' style='white-space: nowrap'>
                                 <div class="reportHelp"><?=$text->GT('IA.LEAVE_AS_OF_DATE_BLANK_TO_AUTOMATICALLY_USE_THE_CU')?></div>
                             </td>
                         </tr>
                            <?if (countArray($strctureRecArray) > 1) {
                            echo "<script>var jsonStructureBookMap = ".json_encode($structureBookArray)."</script>" ;
                                ?>
                                <tr>
                                    <td class="reportFieldLabel" ><label><?=$text->GT('IA.OWNERSHIP_STRUCTURE')?></label></td>
                                    <td class="reportFieldValue"  style='width: 1px;'>
                                        <select name=".ownershipStructure" onchange="populateStructureBooks(this.value, '');">
                                            <?ShowFinOptions($_ownershipStructure, $strctureRecArray, false, true);?>
                                        </select>
                                    </td>
                                    <td style='width: 100%'></td>
                                </tr>
                                <?
                            };?>
                            <?if (countArray($companyBooks) > 1) { ?>
                		    <tr>
                    			<td class="reportFieldLabel" ><label><?=$text->GT('IA.REPORTING_BOOK')?></label></td>
                    			<td class="reportFieldValue"  style='width: 1px;'>
                					<select name=".reportingBook">
                            <?ShowFinOptions($_reportingBook,  $companyBooks, false, true);?>
                                    </select>
                    			</td>
                    			<td class="reportFieldValue indent1" style='vertical-align: middle'>
                     			    <table style=''><tr><td> <!-- / here to align the damm checkbox -->
                            		<input type="checkbox" class="noborder" name="prompt_for_reportbook"
                        				   onclick="UpdateRuntimeParams('repbook', this);" <?=$prompt_for_reportbookChecked?>>
                    				</td><td class='checkboxalign'>
                					 <label><?=$text->GT('IA.PROMPT')?></label>
                    				</td></tr></table>
                				</td>
                                <td style='width: 100%'></td>
                			</tr>
                            <?
};?>

		                  <?if ( CheckAuthorization(GetOperationId('ap/lists/owner/view'), 1) || CheckAuthorization(GetOperationId('ap/lists/owner/edit'), 1)) {?>
                		    <tr>
                    			<td class="reportFieldLabel" style='vertical-align: middle; padding-top: 9px;'><label>Owner</label></td>
                    			<td class="reportFieldValue"  style='width: 1px; white-space: nowrap; ; padding-top: 12px'>
                        <?

                                $showpickcnt = GetUserPreferences($pref, 'SHOWPICKCOUNT', 1000);
                                $showpickcnt = intval($showpickcnt);
                        if ( $showpickcnt <= 0 ) {
                            $showpickcnt = 1000;
                        }
                                $buttonPath = IALayoutManager::getCSSButtonPath();
                                $textName = ".ownerKey";

                                $key = 'cm/lists/ownerpick';
                                $idpick = GetOperationId($key);

                        /** @noinspection PhpUndefinedVariableInspection */
                        $jsStr .= "<div id='placeholder_$textName'></div>";
                                $jsStr .= '<script>';

                        $params = array();
                        $params['path'] = ".ownerKey";
                                $params['uicontrol'] = '1';
                                $params['use1000PickerctIfDefault0'] = true;

                        $value = json_encode($_ownerKey);

                        // Render combo
                                $initCollection = PrintEntityComboValues('owner', $params);
                                $initCollectionJson = json_encode($initCollection);

                                $jsStr .= "jq().ready(function(){";
                                $jsStr .= <<< EOT
                                    var gCSSButtonPath = '$buttonPath';
                                    
                                    var options = new Array();
                                    options['zIndex'] = 30;
                                    options['findFunction'] = function () {
                    				    LaunchSelect('picker.phtml', 'pick', 'cm','ownerpick', '$textName', 'mf', $idpick, '', ''); 
                                    };
                    			    options['viewURL'] = null;
                                    options['isIdBased'] = false;
                                    options['defaultCollection'] = $initCollectionJson; 
                                    options['dataValue'] = $value;
                                    options['defaultlistsize'] = $showpickcnt;
                                    options['nonEditor'] = true;
                                    options['elementNamePrefix'] = ''; // THIS MUST BE HERE! 
                                    
                                    //add a function for add and view and set it in
                                    attachComboBoxToElement('$textName',options);
                                    
EOT;
                                $jsStr .= '});';


                            $jsStr .= "</script>";

                            echo $jsStr;
                                ?>
                				</td>
                    			<td class="reportFieldValue indent1" style='vertical-align: bottom; padding-top: 8px'>
                     			    <table style=''><tr><td> <!-- / here to align the damm checkbox -->
                            		<input type="checkbox" class="noborder" name="prompt_for_reportbook"
                        				   onclick="UpdateRuntimeParams('owner', this);" <?=$prompt_for_ownerKeyChecked?>>
                    				</td><td class='checkboxalign'>
                					 <label><?=$text->GT('IA.PROMPT')?></label>
                    				</td></tr></table>
                				</td>
                                <td style='width: 100%'></td>
                			</tr>
		                  <?
} ?>

                            <?
//};?>
                           <tr><td><div class='linespacer'></div></td></tr>

                       </table>
                 </fieldset>
        </div>

        <?if (false) {
            //late decision to hide this
            ?>
            <div style='<?if ($_accountOrDimensionRows !== 'dimension') { echo 'display:none';
}?>'>
                <div class='reportSection'>
                <div class='reportSectionLabel'><?
                if ($_accountOrDimensionRows === 'dimension') {
                    $helpUrlPath = GetHelpFullPath('GL_FinancialReportEditor_RowsTab_DimensionsPath', false);
                    echo "Prompt for account filter for dimensional structures";
                } else {
                    $helpUrlPath = GetHelpFullPath('GL_FinancialReportEditor_RowsTab_AddGroups', false);
                    echo "Define dimensional filter for account groups";
                }

                    $helpIcon_filter = " <div class='help_text_icon_inline' style='vertical-align: middle;' onclick='Launch(\"".$helpUrlPath."\", \"addaccoutgroupdimensionhelp\", 575, 450, false);'></div>";
                    echo $helpIcon_filter;
                    ?>
                    <!-- <span class='GL_FinancialReports_FiltersTab_DimensionFilters cellhelp'></span> -->
                </div>
                <fieldset>
                    <table>
                        <tr class="filtersSection2">
                            <td class='reportFieldLabel' <?
                            if ($_accountOrDimensionRows === 'dimension') { echo "style='vertical-align: middle'";
                            }
                                ?> ><label><?
if ($_accountOrDimensionRows === 'dimension') { echo $text->GT("IA.ACCOUNT");
}
else { echo $text->GT("IA.DIMENSION");
}

if ($_accountOrDimensionRows !== 'dimension') {
    $prompt_for_acctgrp = '';
    echo '<script>jq().ready(function(){UpdateRuntimeParams("acctgrp", jq(\'#prompt_for_acctgrp\')[0])});</script>';
}

                                    ?></label></td>
                            <td class='indent1' style='padding-left:0px; vertical-align: <?if ($_accountOrDimensionRows === 'dimension') { echo "middle'";
} else { echo 'top';
}?> '>
                                <input type="checkbox" class="noborder" id='prompt_for_acctgrp' name="prompt_for_acctgrp"
                                       onclick="UpdateRuntimeParams('acctgrp', this);" <?=$prompt_for_acctgrp?>>
                            </td>
                            <td class='reportFieldValue checkboxalign' style='vertical-align: <?if ($_accountOrDimensionRows === 'dimension') { echo "middle'";
} else { echo 'top; padding-top:4px;';
}?>'>
                                <label><?=$text->GT('IA.PROMPT')?></label>
                            </td>
                        </tr>
                    </table>
                </fieldset>
                </div>
            </div>
        <?
}?>

        <div class='reportSection filtersSection'>
        <div class='reportSectionLabel'><?=$text->GT('IA.DEFINE_DIMENSION_FILTERS')?>
            <span class='GL_FinancialReports_FiltersTab_DimensionFilters cellhelp'></span>
        </div>
        <fieldset>
        <input type=hidden name=".runtimeparams" value="<?= /** @noinspection PhpUndefinedVariableInspection */
        $runtimeparams?>" size=50 maxlength="<?= /** @noinspection PhpUndefinedVariableInspection */
        $fs_runtimeparams?>">
        <input type=hidden name=".exclSubs" value="<? echo isl_htmlspecialchars($_exclSubs); ?>">
        <table>


            <?
                 $showInactiveDimRowShown = false;
                 $showInactiveDeptLocRowShown=false;

            foreach($finDimInfo as $name => $params) {

                    $originalName = $params['fullname'];
                    $fn = $text->GT($params['label']);

                    $allDimensionToken = 'IA.ALL_';
                    $specificDimensionToken = 'IA.SPECIFIC_';
                    $specificDimensionSlashGroupToken = 'IA.SPECIFIC_';
                    $noDimensionSpecifiedToken = 'IA.NO_';
                    $allDimensionPlaceHolder = $specificDimensionPlaceHolder = [];
                    $specificDimensionSlashGroupPlaceHolder = $noDimensionSpecifiedPlaceHolder = [];
                    $considerUsingDimensionLabel = "";
                    if($params['standard']){
                        $dimensionEntity = $params['entity'];
                        if($dimensionEntity === 'departmentfilter'){
                            $dimensionEntity = 'department';
                        } else if ($dimensionEntity === 'gllocationfilter'){
                            $dimensionEntity = 'location';
                        }
                        $allDimensionToken .= strtoupper($dimensionEntity); // Eg: All Departments
                        $specificDimensionToken .= strtoupper($dimensionEntity); // Eg: Specific Department
                        $specificDimensionSlashGroupToken .= strtoupper($dimensionEntity) . '_SLASH_GROUP'; // Eg: Specific Department/Group
                        $noDimensionSpecifiedToken .= strtoupper($dimensionEntity) . '_SPECIFIED'; // Eg: No Department specified
                        $considerUsingDimensionLabel = 'IA.CONSIDER_USING_' . strtoupper($dimensionEntity) . '_DIMENSION_GROUP';
                    } else {
                        // All UDD
                        $allDimensionToken .= 'PLATFORM_OBJECTS';
                        $allDimensionPlaceHolder['OBJECT_NAME'] = $originalName;
                        // Specific UDD
                        $specificDimensionToken .= 'PLATFORM_OBJECTS';
                        $specificDimensionPlaceHolder['OBJECT_NAME'] = $originalName;
                        // Specific UDD/Group
                        $specificDimensionSlashGroupToken .= 'PLATFORM_OBJECT_SLASH_GROUP';
                        $specificDimensionSlashGroupPlaceHolder['PLATFORM_OBJECT'] = $originalName;
                        // No UDD specified
                        $noDimensionSpecifiedToken .= 'PLATFORM_OBJECT_SPECIFIED';
                        $noDimensionSpecifiedPlaceHolder['PLATFORM_OBJECT'] = $originalName;
                    }
                    $allDimensionLabel = $text->GT($allDimensionToken, $allDimensionPlaceHolder);
                    $specificDimensionLabel = $text->GT($specificDimensionToken, $specificDimensionPlaceHolder);
                    $specificDimensionSlashGroupLabel = $text->GT($specificDimensionSlashGroupToken, $specificDimensionSlashGroupPlaceHolder);
                    $noDimensionSpecifiedLabel = $text->GT($noDimensionSpecifiedToken, $noDimensionSpecifiedPlaceHolder);

                    $showInactiveDeptLocRow = in_array($name, array('department','location'));
                    $showInactiveDeptLocRowShown = $showInactiveDeptLocRowShown || $showInactiveDeptLocRow;

                if(!$showInactiveDimRowShown && !$showInactiveDeptLocRow) {
                    $showInactiveDimRowShown = true; ?>
                    <tr>
                        <td colspan='<?=($showInactiveDeptLocRowShown) ? '5' : '4'?>'>
                            <? if($showInactiveDeptLocRowShown) { EditReportSectionSeperator();
}?>
                        </td>
                    </tr>
                    <?
                }?>

                           <tr>
                           <td class="reportFieldLabel" ><label><?echo $fn?></label></td>
                           <td class="reportFieldValue">
                               <select class='dimfiltertype' id="<?=$name?>_FilterType" name=".<?=$name?>_FilterType">
                                   <option value='nofilter'><?echo $allDimensionLabel?></option>
                                   <option value='specifichierarchy'><?echo $specificDimensionSlashGroupLabel;?></option>
                                   <!-- <option value='specific'><?echo $specificDimensionLabel;?></option> -->
                                   <!-- <option value='nullvalue'><?echo $noDimensionSpecifiedLabel;?></option> NOT SUPPORTED-->
                                    <?if (countArray($params['relatedFields'])>0) {
                                        $labelSeperator = '______________________________________________';
                                        global $IE_8_HACKATHON;
                                        if ($IE_8_HACKATHON) { $labelSeperator = '_______________________________';
                                        }
                                ?>
                            <optgroup label="<?=$labelSeperator?>"></optgroup>
                            <option value='type'><?echo $text->GT(($params['entity'] == 'item') ? 'IA.PRODUCT_LINE' : 'IA.'.strtoupper($params['entity']).'_TYPE');?></option>
                            <?
}?>
                               </select>
                               <div id='<?echo $name."_dd"?>' style='display:none; position: static' >
                                   <fieldset class='dimensionDD' style="border:0px;">
                                <?
                                if ($params['isplatform']) {
                                    $params['entity'] = $params['pickentity'];
                                    $params['type']['entity'] = $params['type']['pickentity'];
                                }
                                    echo "<table style='display: inline-table'><tr>";
                                    RenderDimPickerNewUI($params, 'mf', '', true);
                                    echo"</tr></table>";
                                ?>

                                   </fieldset>
                               </div>
                               <div id='<?echo $name."_type_dd"?>' style='display:none; position: static' >
                                   <fieldset class='dimensionDD' style="border:0px;"><table><tr>
                                <?
                                foreach( $params['relatedFields'] ?? [] as $val) {
                                    RenderDimPickerNewUI($params[$val], 'mf', '', false);
}?>
                                   </tr></table></fieldset>
                               </div>

                               <script>
                                    //need to figure if we need to show a combo box
                                   jq(document).ready( function() {

                                       var timeoutCallback = function () {
                                    var typeVal = jq("#<?echo $name."_type_dd"?> INPUT[type=text]").val();
                                    var dimVal = jq("#<?echo $name."_dd"?> INPUT[type=text]").val();

                                    if (dimVal && dimVal.length) {
                                        jq("#<?=$name?>_FilterType").val('specifichierarchy');
                                    } else if (typeVal && typeVal.length) {
                                        jq("#<?=$name?>_FilterType").val('type');
                                    }

                                    var value = jq("#<?=$name?>_FilterType").val();
                                    if (value == 'specifichierarchy')  {
                                        jq("#<?echo $name."_dd"?>").show();
                                        if (jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked')) {
                                            jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'checked');
                                        } else {
                                            jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'unchecked');
                                        }
                                        jq("#<?echo $name."_sub_dd"?>").show();
                                        jq("#<?echo $name."_type_dd"?> INPUT[type=text]").val('');
                                        jq("#<?echo $name."_sub_dd_hint"?>").hide();
                                        jq("#<?echo $name."_sub_dd_hint"?>").closest('TD')[0].colSpan=1;
                                        jq("#<?echo $name."_ir_table"?>").show();
                                    }
                                    if (value == 'type')  {
                                        jq("#<?echo $name."_type_dd"?>").show();
                                        jq("#<?echo $name."_ir_table"?>").hide();
                                        jq("#<?echo $name."_dd"?> INPUT[type=text]").val('');
                                        jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked', true); //always check it when we hide this guy! Ticket 14533
                                        jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'checked');
                                        jq("#<?echo $name."_sub_dd"?>").hide();
                                        jq("#<?echo $name."_prompt_dd"?>").hide();
                                        jq("#<?echo $name."_sub_dd_hint"?>").show();
                                        jq("#<?echo $name."_sub_dd_hint"?>").closest('TD')[0].colSpan=3;
                                    } else {
                                        jq("#<?echo $name."_ir_table"?>").show();
                                        if (jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked')) {
                                            jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'checked');
                                        } else {
                                            jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'unchecked');
                                        }
                                        jq("#<?echo $name."_sub_dd"?>").show();
                                        jq("#<?echo $name."_prompt_dd"?>").show();
                                        jq("#<?echo $name."_sub_dd_hint"?>").hide();
                                        jq("#<?echo $name."_sub_dd_hint"?>").closest('TD')[0].colSpan=1;
                                    }

                                    var cb = jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]");
                                    if (cb.length)  {
                                        UpdateExclSubs("<?echo $name?>", cb[0]);
                                    }

                                    jq("#<?=$name?>_FilterType").change(function () {
                                        var value = jq("#<?=$name?>_FilterType").val();

                                        if (jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val') == undefined) {
                                            jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'checked');
                                        }

                                        if (value == 'specifichierarchy') {
                                            jq("#<?echo $name."_type_dd"?>").hide();
                                            jq("#<?echo $name."_type_dd"?> INPUT[type=text]").val('');
                                            jq("#<?echo $name."_dd"?>").show();
                                            jq("#<?echo $name."_dd"?> INPUT[type=text]").val('');
                                            if (jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val') == 'checked') {
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked', true);
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'checked');
                                            } else {
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked', false);
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'unchecked');
                                            }
                                            jq("#<?echo $name."_sub_dd"?>").show();
                                            jq("#<?echo $name."_prompt_dd"?>").show();
                                            jq("#<?echo $name."_sub_dd_hint"?>").hide();
                                            jq("#<?echo $name."_sub_dd_hint"?>").closest('TD')[0].colSpan=1;
                                            jq("#<?echo $name."_ir_table"?>").show();
                                        } else if (value == 'type') {
                                            jq("#<?echo $name."_dd"?>").hide();
                                            jq("#<?echo $name."_ir_table"?>").hide();
                                            jq("#<?echo $name."_dd"?> INPUT[type=text]").val('');
                                            jq("#<?echo $name."_type_dd"?>").show();
                                            if (jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked')) {
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'checked');
                                            } else {
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'unchecked');
                                            }
                                            //always check it when we hide this guy! Ticket 14533
                                            jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked', true);

                                            jq("#<?echo $name."_sub_dd"?>").hide();
                                            jq("#<?echo $name."_prompt_dd"?>").hide();
                                            jq("#<?echo $name."_sub_dd_hint"?>").show();
                                            jq("#<?echo $name."_sub_dd_hint"?>").closest('TD')[0].colSpan=3;

                                       } else {
                                            jq("#<?echo $name."_ir_table"?>").show();
                                            jq("#<?echo $name."_prompt_dd"?>").show();
                                            if (jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val') == 'checked') {
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked', true);
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'checked');
                                            } else {
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('checked', false);
                                                jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]").prop('old_val', 'unchecked');
                                            }
                                            jq("#<?echo $name."_sub_dd"?>").show();
                                            jq("#<?echo $name."_dd"?>").hide();
                                            jq("#<?echo $name."_dd"?> INPUT[type=text]").val('');
                                            jq("#<?echo $name."_type_dd"?>").hide();
                                            jq("#<?echo $name."_type_dd"?> INPUT[type=text]").val('');
                                            jq("#<?echo $name."_sub_dd_hint"?>").hide();
                                            jq("#<?echo $name."_sub_dd_hint"?>").closest('TD')[0].colSpan=1;
                                       }

                                       var cb = jq("#<?echo $name."_sub_dd"?> INPUT[type=checkbox]");
                                       if (cb.length)  {
                                           UpdateExclSubs("<?echo $name?>", cb[0]);
                                       }

                                    });
                                       };
                                       window.setTimeout(timeoutCallback, 1); // New, need to let all the fields initialize first!

                                   });

                                </script>

                           </td>

                           <td class="reportFieldValue indent1 subs" style='vertical-align: top'>
                               <div id='<?echo $name."_sub_dd"?>' style='display:none; position: static' >
                                <?if ( !$params['isplatform'] && $name != 'item' && $name != 'affiliateentity' ) {?>
                            <table style=''><tr><td> <!-- / here to align the damm checkbox -->
                                        <input type="checkbox" class="noborder" name="exclSubs_for_<?=$name?>"
                                         onclick="UpdateExclSubs('<?=$name?>', this);" <?=isl_strstr($_exclSubs, $name) ?  "" : "checked";?>   >
                            </td><td class='checkboxalign2'>
                            <Label style='white-space: nowrap'><?=$text->GT('IA.INCLUDE_SUBS')?></Label>
                            </td></tr></table>
                        <?
} ?>
                               </div>
                               <div style='display:none; white-space: nowrap ;color: #787878;' id='<?echo $name."_sub_dd_hint"?>'><?=$text->GT($considerUsingDimensionLabel)?> <span class='GL_FinancialReports_FiltersTab_UseDimensionGroups cellhelp'></span></div>
                           </td>

                           <td class="reportFieldValue indent1" id='' style='vertical-align: top'>
                               <div id='<?echo $name."_prompt_dd"?>' style='display:none; position: static' >
                                   <table style=''><tr><td> <!-- / here to align the damm checkbox -->
                                   <input type="checkbox" class="noborder" name="prompt_for_<?=$name?>"
                                        onclick="UpdateRuntimeParams('<?=($name=='department')?'dept':$name?>', this);" <?=isl_strstr($_runtimeparams, ($name=='department')?'dept':$name) ?  "checked" : "";?>   >
                                   </td><td class='checkboxalign2'>
                                   <label><?=$text->GT('IA.PROMPT')?></label>
                                   </td></tr></table>
                               </div>
                           </td>

                           <td class="reportFieldValue indent1" style='vertical-align: top'>
                                <? if (in_array($name, array('department','location', 'project'))) { ?>
                            <?
                            // Following logic will return as follows
                            // fldname = .ir_dept fldval = $_ir_dept
                            $field = $params['path'];
                            if($name === 'project') {
                                $field = '.project';
                            }
                            $fldName = '.ir'.str_replace(".", "_", $field);
                            $fldval = '_ir'.str_replace(".", "_", $field);
                            $fldval = Request::$r->$fldval;

                            if ($fldval === "true") { $checked = ' checked ';
                            } else { $checked = ' ';
                            } ?>
                        <table style='' id="<?=$name?>_ir_table"><tr><td> <!-- / here to align the damm checkbox -->
                        <input type="checkbox" class="noborder" name="<?=$fldName?>" value="<?=$fldval?>" <?=$checked?>
                             onclick="if(this.checked) {this.value = 'true'} else {this.value = 'false'} ">
                        </td><td class='checkboxalign2' style='white-space: nowrap'>
                        <Label><?=$text->GT('IA.RUN_AS_INDIVIDUAL_REPORTS')?>
                            <?
                            if (!isset($indreft)) {
                                $indreft = 'set';
                                echo "<span class='GL_FinancialReports_FiltersTab_IndividualReports cellhelp'></span>";
                            }
                            ?>
                        </Label>
                        </td></tr></table>

                        <?
} ?>
                           </td>

                           <td class="reportFieldValue indent1" style='vertical-align: top'>
                                <? if ($showInactiveDeptLocRow) { ?>
                            <?
                            // Following logic will return as follows
                            // fldname = .ir_dept fldval = $_ir_dept
                            /** @noinspection PhpUnusedLocalVariableInspection */
                            $fldName = '.ir' . str_replace(".", "_", $params['path']);
                            $fldval = '_ir'.str_replace(".", "_", $params['path']);
                            $fldval = Request::$r->{$fldval};

                            if($fldval === "true") {
                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $checked = ' checked ';
                            } else {
                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $checked = ' ';
                            } ?>
                        <table style=''><tr><td> <!-- / here to align the damm checkbox -->
                            <? if ($name === 'department') { ?>
                                <!--  <input type="hidden" id='showInactiveDept' name="showInactiveDept" value="<?php echo $_showInactiveDept === 'F' ? 'F' : 'T'; ?>"> -->
                                <input type="checkbox" class="noborder"  name="showInactiveDept" value="T"
                                    <?=($_showInactiveDept === 'F' ? 'CHECKED' : ''); ?>
                                onchange="var val= document.getElementsByName('showInactiveDept')[0].checked ? 'F': 'T';  document.mf.elements['.showInactiveDept'].value = val">
                            <?
} else {?>
                                <input type="checkbox" class="noborder"  name="showInactiveLoc" value="T"
                                    <?=($_showInactiveLoc === 'F' ? 'CHECKED' : ''); ?>
                                onchange="var val= document.getElementsByName('showInactiveLoc')[0].checked ? 'F': 'T';  document.mf.elements['.showInactiveLoc'].value = val">

                            <?
}?>
                        </td><td class='checkboxalign2' style='white-space:nowrap'>
                        <Label><?=$text->GT('IA.HIDE_INACTIVES')?>  <?
                        if (!isset($actuvesreft)) {
                            $actuvesreft = 'set';
                            echo "<span class='GL_FinancialReports_FiltersTab_HideInactives cellhelp'></span>";
                        }
                            ?>
                        </Label>
                        </td></tr></table>

                        <?
} ?>
                           </td>

                </tr>
            <?
            if($name == 'location' && IsMCMESubscribed() && !GetContextLocation()) {
                echo "<tr><td></td><td colspan='3' class='reportSectionLabelText' style='color: ".(locationFilterNeeded() ? '#b00' : '#999999')."; font-size: 10px'>".$text->GT('IA.A_SPECIFIC_LOCATION_IS_REQUIRED_WHEN_THE_NON_CONSOLIDATION')."</td></tr>";
            }

            } //for each

                    ?>
                    </table>
             </fieldset>
        </div>

        <script>
         jq().ready(function() {
     	    jq('img[src$="pick.gif"]').attr('title','Select from list view');
    	    jq('img[src$="new.gif"]').attr('title',GT('IA.ADD_NEW'));
    	    jq('img[src$="view.gif"]').attr('title',GT('IA.VIEW_SELECTED'));
          });

         pinLeftTabMenu();
         SetContentHeight(true);
         jq(window).bind('resize', function () {
             SetContentHeight();
         });
        </script>

<?
}

global $original_dept, $original_location;
function getLocDeptRecNo()
{
    global $LocGrpRec, $_location_grp, $_userid;
    global $DeptGrpRec, $_dept_grp;
    global $ProjectGrpRec, $_project_grp;
    global $original_location, $original_dept;

    $_location  = &Request::$r->_location;
    $_dept      = &Request::$r->_dept;
    $_ownerKey  = &Request::$r->_ownerKey;
    $_ir_dept   = Request::$r->_ir_dept;
    $_ir_location = Request::$r->_ir_location;
    $_ir_project = Request::$r->_ir_project;

    if (isset($_location) && $_location !='') {
        //To get record# of selected location/group from the picker
        if(IsGroup('location', $_location, $locgrprec)) {
            if($_ir_location != 'true') {
                $LocGrpRec = $locgrprec;
            } else {
                $_location_grp = $locgrprec;
            }
            // reset to no selection, so that UI logic works
            $original_location = $_location;
            $_location = '';
        }
        else  {
            $original_location = $_location;
            [$_locid] = explode(PICK_RECVAL_SEP, isl_trim($_location));
            GetRecordNo($_userid, 'location', $_locid, $_location);
        }
    }

    if (isset($_dept) && $_dept !='') {
        //To get record# of selected department/group from the picker
        if(IsGroup('department', $_dept, $deptgrprec)) {
            if ($_ir_dept != 'true') {
                $DeptGrpRec = $deptgrprec;
            } else {
                $_dept_grp = $deptgrprec;
            }
            // reset to no selection, so that UI logic works
            $original_dept = $_dept;
            $_dept = '';
        }
        else  {
            $original_dept = $_dept;
            [$_deptid] = explode(PICK_RECVAL_SEP, isl_trim($_dept));
            GetRecordNo($_userid, 'department', $_deptid, $_dept);
        }
    }

    if (isset($_ownerKey) && $_ownerKey !='') {
        [$_ownerId] = explode(PICK_RECVAL_SEP, isl_trim($_ownerKey));
        GetRecordNo($_userid, 'vendor', $_ownerId, $_ownerKey);
        global $_allowOwnerValid;
        $_allowOwnerValid = true;
    }

}

function resetDepartmentLocation()
{
    global $original_location, $original_dept;

    $_location  = &Request::$r->_location;
    $_dept      = &Request::$r->_dept;

    if (isset($original_dept)) {
        $_dept = $original_dept;
    }
    if (isset($original_location)) {
        $_location = $original_location;
    }
}

function EditReportGeneratePageLayout()
{

    global $kPGOffsets, $kDelim, $report;
    global $kMeasures;
    global $_columnTabRows;
    global $fontMap, $colorMap, $smallSizeMap, $LocGrpRec, $DeptGrpRec, $gAccountGroupMap;
    $text = TextHelper::getInstance(FinancialReportWizard::class);
    $_location           = &Request::$r->_location;
    $_asofdate           = &Request::$r->_asofdate;
    $_dept               = &Request::$r->_dept;
    /** @noinspection PhpUnusedLocalVariableInspection */
    $_ownerKey           = &Request::$r->_ownerKey;
    $_bottommargin       = &Request::$r->_bottommargin;
    $_colAccountGrp      = &Request::$r->_colAccountGrp;
    $_colAttributeField  = &Request::$r->_colAttributeField;
    $_colAttributeObject = &Request::$r->_colAttributeObject;
    $_colfontbold        = &Request::$r->_colfontbold;
    $_colfontcolor       = &Request::$r->_colfontcolor;
    $_colfontitalic      = &Request::$r->_colfontitalic;
    $_colfontname        = &Request::$r->_colfontname;
    $_colfontsize        = &Request::$r->_colfontsize;
    $_colhdr1            = &Request::$r->_colhdr1;
    $_colhdr2            = &Request::$r->_colhdr2;
    $_colhide            = &Request::$r->_colhide;
    $_colPeriod          = &Request::$r->_colPeriod;
    $_colPeriodOffset    = &Request::$r->_colPeriodOffset;
    $_colpgbreak         = &Request::$r->_colpgbreak;
    $_colTitle           = &Request::$r->_colTitle;
    $_colValueType       = &Request::$r->_colValueType;
    $_colwidth           = &Request::$r->_colwidth;
    $_compareby          = &Request::$r->_compareby;
    $_definedby          = &Request::$r->_definedby;
    $_exclSubs           = &Request::$r->_exclSubs;
    $_forceDefaulting    = &Request::$r->_forceDefaulting;
    $_indicatorType      = &Request::$r->_indicatorType;
    $_leftmargin         = &Request::$r->_leftmargin;
    $_orientation        = &Request::$r->_orientation;
    $_pagesize           = &Request::$r->_pagesize;
    $_rfcd               = &Request::$r->_rfcd;
    $_rightlefthide      = &Request::$r->_rightlefthide;
    $_rightmargin        = &Request::$r->_rightmargin;
    $_showInactiveDept   = &Request::$r->_showInactiveDept;
    $_showInactiveLoc    = &Request::$r->_showInactiveLoc;
    $_topmargin          = &Request::$r->_topmargin;
    $_scale              = &Request::$r->_scale;
    $_moveLorR           = Request::$r->_moveLorR;

    if ( isset($_colhdr1) && $_colhdr1 != '' ) {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $colhdr1     = explode($kDelim, $_colhdr1);
    }
    if ( isset($_colhdr2) && $_colhdr2 != '' ) {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $colhdr2     = explode($kDelim, $_colhdr2);
    }
    if ( isset($_colPeriod) && $_colPeriod != '' ) {
        $colPeriod    = explode($kDelim, $_colPeriod);
    }
    if ( isset($_colPeriodOffset) && $_colPeriodOffset != '' ) {
        $colPeriodOffset = explode($kDelim, $_colPeriodOffset);
    }
    if ( isset($_compareby) && $_compareby != '' ) {
        $compareby     = explode($kDelim, $_compareby);
    }

    if ( isset($_indicatorType) && $_indicatorType != '' ) {
        $indicatorType     = explode($kDelim, $_indicatorType);
    }
    if ( isset($_rightlefthide) && $_rightlefthide != '' ) {
        $rightlefthide     = explode($kDelim, $_rightlefthide);
    }
    if ( isset($_colValueType) && $_colValueType != '' ) {
        $colValueType = explode($kDelim, $_colValueType);
    }

    if ( isset($_colAttributeObject) && $_colAttributeObject != '' ) {
        $colAttributeObject = explode($kDelim, $_colAttributeObject);
    }

    $colAttributeField = empty($_colAttributeField) ? [] : explode($kDelim, $_colAttributeField);

    if ( isset($_colTitle) && $_colTitle != '' ) {
        $colTitle        = explode($kDelim, $_colTitle);
    }

    //if ( isset($_expof) && $_expof != '' ) 				{ $expof		= explode($kDelim, $_expof); 	}
    if ( isset($_colwidth) && $_colwidth != '' ) {
        $colwidth        = explode($kDelim, $_colwidth);
    }
    if ( isset($_colpgbreak) && $_colpgbreak != '' ) {
        $colpgbreak    = explode($kDelim, $_colpgbreak);
    }
    if ( isset($_colhide) && $_colhide != '' ) {
        $colhide         = explode($kDelim, $_colhide);
    }
    $colfontname = empty($_colfontname) ? [] : explode($kDelim, $_colfontname);
    $colfontcolor = empty($_colfontcolor) ? [] : explode($kDelim, $_colfontcolor);
    if ( isset($_colfontsize) && $_colfontsize != '' ) {
        $colfontsize = explode($kDelim, $_colfontsize);
    }
    if ( isset($_colfontitalic) && $_colfontitalic != '' ) {
        $colfontitalic = explode($kDelim, $_colfontitalic);
    }
    if ( isset($_colfontbold) && $_colfontbold != '' ) {
        $colfontbold = explode($kDelim, $_colfontbold);
    }
    if ( isset($_colAccountGrp) && $_colAccountGrp != '' ) {
        $colAccountGrp = explode($kDelim, $_colAccountGrp);
    }

    $columnCount = ( countArray($report['columns']) ?: 2 );
    $_columnTabRows = ( countArray($colPeriod) ?: $columnCount );

    for($i2 = 0; $i2<$_columnTabRows; $i2++) {
        /** @noinspection PhpUndefinedVariableInspection */
        if ( $colCompValue[$i] == 'a') {
            $colCompValue[$i] = 'x';
            /** @noinspection PhpUndefinedVariableInspection */
            if ( !isset($colTitle[$i]) || $colTitle[$i] == '') {
                $colTitle[$i] = 'Actual';
            }
        }
    }
    $simulatedColumns = array();

    // simulate gReport['columns'] to reuse the PDF paging
    for ($i=0;$i<$_columnTabRows;$i++) {
        /** @noinspection PhpUndefinedVariableInspection */
        $simulatedColumns[] = array(
                'TYPE' => $colValueType[$i] ,
                'PERIOD#' => $colPeriod[$i],
                'PERIODOFFSET' => $colPeriodOffset[$i],
                'COMPAREBY' => $compareby[$i],
                'INDICATORTYPE' => $indicatorType[$i],
                'RIGHTLEFTHIDE' => $rightlefthide[$i],
                'COLTITLE' => $colTitle[$i],
                'ORIGINALCOL' => true,
                'COLHIDE' => $colhide[$i], //required to check whether the column is hiden or not
                'ACCTGRPFILTERKEY' => $colAccountGrp[$i] ? $gAccountGroupMap[$colAccountGrp[$i]] : '',
        );
    }
    // get record no for location and department
    getLocDeptRecNo();
    /* @var array $gReport */
    global $gReport;
    $_reportparameters['EXCLUDESUBS'] = $_exclSubs;
    $gReport['PARAMETERS'] = serialize($_reportparameters);
    $gReport['LOCATION#'] = $_location;
    ReCacheDeptLocMaps($_showInactiveDept, $_showInactiveLoc, true);

    $expLoc = $LocGrpRec ? '' : $_location;
    $expDep = $DeptGrpRec ? '' : $_dept;
    ExpandColumnsForWizard($simulatedColumns, $expDep, $expLoc, $_asofdate);

    $kPeriodMap = GetPeriodMap();

    /** @noinspection PhpUndefinedVariableInspection */
    $repeatingDesc = ( $_rfcd == 'T' && in_array($colValueType[0], array( 'DSC', 'DSCN', 'AN'))) ;

    //$defaultState = ( $_definedby == 'S' || $_definedby == '' ? 'disabled' : '' );
    //$sysDefined = ( $_definedby == 'S' || $_definedby == '' ? true : false );

    if ( $_definedby == 'S' || $_definedby == '' ) {
        $defaultState = '';
        $sysDefined = true;
        $_pagesize = 'Letter';
        $_topmargin = $_bottommargin = $_leftmargin = $_rightmargin = '';
    } else {
        $defaultState = '';
        $sysDefined = false;
    }


    // calculate scaling text
    $_scale = ( !isset($_scale) || $_scale == '' || $sysDefined || $_forceDefaulting == '1' ? 'P' : $_scale );
    $scale_text = ( $_scale == 'P' ? 'IA.ENTER_COLUMN_WIDTH_IN_POINTS_1_INCH_72_POINTS' : 'IA.ENTER_COLUMN_WIDTH_IN_INCHES_1_INCH_72_POINTS' );

    // calculate page test
    // looks like $_pagesize can also be null, no initialization
    $offsets = $kPGOffsets[$_pagesize] ?? [];
    $offsets = ( $_orientation == 'P' ? $offsets : array_reverse($offsets) );
    /** @noinspection PhpUnusedLocalVariableInspection */
    $pagetext = '( ' . $offsets[0] . ' , ' . $offsets[1] . ' )';

    $nCols = $_columnTabRows = count($simulatedColumns);

    GetPageBreaks($repeatingDesc, $_orientation, 'S', $_pagesize, $simulatedColumns, $page, $breakIndexes, $no_of_textcols, $no_of_numbercols, $no_of_vdscols);

    $maxfitwidth = ( $breakIndexes[0] == 5 ?  2 : 0 );

    // CALCULATE THE MARGINS
    if ( $_topmargin == '' ) {
        $_topmargin = ( $_orientation !='P' ? '20' : '28' );
    }

    if ( $_bottommargin == '' ) {
        $_bottommargin = ( $_orientation !='P' ? '28' : '28' );
    }

    if ( $_leftmargin == '' ) {
        $_leftmargin = ( $_orientation !='P' ? '36' : (36 - $maxfitwidth) );
    }

    if ( $_rightmargin == '' ) {
        $_rightmargin = ( $_orientation !='P' ? '36' : (36 - $maxfitwidth) );
    }

    // GET PAGE OFFSETS FOR THE SELECTED PAPER TYPE
    [$pgWidth, $pgHeight] = GetPageOffsets($_pagesize, $_orientation);

    // CALCULATE THE REMAINING PAGE HEIGHT AND WIDTH
    /** @noinspection PhpUnusedLocalVariableInspection */
    $pgHeightCalc = ( $pgHeight - $_topmargin - $_bottommargin );
    $pgWidthCalc = ( $pgWidth - $_leftmargin - $_rightmargin );

    $numbercolwidth = numbercolwidth - $maxfitwidth;
    /** @noinspection PhpUnusedLocalVariableInspection */
    $shorttextcolwidth = shorttextcolwidth - $maxfitwidth;
    /** @noinspection PhpUnusedLocalVariableInspection */
    $percentcolwidth = percentcolwidth;

    //eppp(" _orientation : $_orientation _pagesize : $_pagesize($pgHeight, $pgWidth) remaining($pgHeightCalc, $pgWidthCalc) ");

    ?>
        	<script>
        		var pgHeight= <?=$pgHeight?>;
        		var pgWidth = <?=$pgWidth?>;

        		var repeatingDesc = '<?=($repeatingDesc ? 1 : 0);?>';

        		var PageStartIndexes = new Array();
        		var PageEndIndexes = new Array();
        		var TotalCols = <?=$nCols?>;
        		var TotalPages = <?=count($page)?>;
            <?
            foreach ( $page as $key => $values ) {
                echo "PageStartIndexes[" . $key ."] = " . $values['startidx'] . ";";
                echo "PageEndIndexes[" . $key ."] = " . $values['endidx'] . ";";
            }

            $i=0;
            echo "var kPGHeight = new Array( ";
            foreach ( $kPGOffsets as $values ) {
                //echo "kPGHeight['" . $i ."'] = '" . $values[0] . "';";
                echo "'" . $values[0] . "',";
                $i++;
            }
            echo " ''); ";

            $i=0;
            echo "var kPGWidth = new Array( ";
            foreach ( $kPGOffsets as $values ) {
                //echo "kPGHeight['" . $i ."'] = '" . $values[1] . "';";
                echo "'" . $values[0] . "',";
                $i++;
            }
            echo " ''); ";
            ?>

        	</script>
        	<input type="hidden" name="ncols" value="<?=$_columnTabRows;?>" >
        	<input type="hidden" name="currentcolwidth" value="0" >
            <?if ($defaultState == 'disabled') {
                echo '<div style="display: none">';
}?>

            		<TABLE cellpadding="3px" cellspacing="1" style="background-color: #AEC6E0; border: 1px solid #AEC6E0; border-spacing: 1px; border-collapse: separate;">
                        <thead>
                			<tr  class="multiline_bg_beige">
                				 <td align="center" colspan="3"  style='color:  #355E8B; vertical-align: baseline; padding-top: 8px; padding-bottom: 8px;'>
                				     <b><?=$text->GT('IA.COLUMN')?></b></td>
                				 <td align="center"  style='color:  #355E8B; vertical-align: baseline; <?=($_definedby != 'F' && !($_definedby == 'S' || $_definedby == '')) ? '' : 'display: none;';?>'>
                					<b><?=$text->GT('IA.WIDTH')?>*<br/><?=$text->GT('IA.PDF_ONLY')?></b>
                				 </td>
                				 <td align="center"  style='color:  #355E8B; vertical-align: baseline; <?=($_definedby != 'F' && !($_definedby == 'S' || $_definedby == '')) ? '' : 'display: none;';?>'><b><?=$text->GT('IA.BREAK')?><br/><?=$text->GT('IA.PDF_ONLY')?></b></td>
                				 <td align="center"  style='color:  #355E8B; vertical-align: baseline'><b><?=$text->GT('IA.FONT')?></b></td>
                				 <td align="center"  style='color:  #355E8B; vertical-align: baseline'><b><?=$text->GT('IA.SIZE')?></b></td>
                				 <td align="center"  style='color:  #355E8B; vertical-align: baseline'><b><?=$text->GT('IA.COLOR')?><br/><?=$text->GT('IA.PDF_ONLY')?></b></td>
                				 <td align="center"  style='color:  #355E8B; vertical-align: baseline'><b><?=$text->GT('IA.BOLD')?></b></td>
                				 <td align="center"  style='color:  #355E8B; vertical-align: baseline'><b><?=$text->GT('IA.ITALIC')?></b></td>
                			</tr>
                		</thead>
                		<tbody>
                            <?

                            $defaultSettings = ( $sysDefined || $_forceDefaulting == '1' ? true : false );
                            $originalCol = 0;

                            $altColor = '#FFFFFF';
                            for ($i=0, $index=1; $i < $nCols;$i++, $index++) {
                                $altColor = ($altColor === '#FFFFFF') ? '#EDF2F9' : "#FFFFFF";

                                $thiscol            = $simulatedColumns[$i];
                                $IsDescriptiveColumn = IsDescriptiveColumn($thiscol);

                                      $colValueType = $thiscol['TYPE'];
                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $attributeObject = $thiscol['ATTRIBUTEOBJECT'];
                                /** @noinspection PhpUnusedLocalVariableInspection */
                                $attributeField = $thiscol['ATTRIBUTEFIELD'];
                                $colValueType = ($colValueType == 'a') ? 'x' : $colValueType;
                                $colPeriod        = $thiscol['PERIOD#'];
                                $colTitle        = $thiscol['COLTITLE'];
                                $expof            = ( isset($thiscol['EXPOF']) && $thiscol['EXPOF'] !== '' && !$thiscol['ORIGINALCOL']? $thiscol['EXPOF'] : '' );

                                $selectedItem = $colValueType;
                                $selectedItem = $kMeasures[$selectedItem];
                                $title = ( $colTitle ?: $selectedItem );

                                $title .= ( $thiscol['HEADER0'] ? ' for ' . $thiscol['HEADER0'] : '' );
                                if ($title == '') {
                                    $title = $kMeasures[$colValueType];
                                }

                                $selectedPeriod = ( $IsDescriptiveColumn ? '&nbsp;' : util_encode($kPeriodMap[$colPeriod]) );

                                if ( $defaultSettings ) {

                                    if ( in_array($index, $breakIndexes) ) {
                                        $isChecked = 'checked';
                                        $colpgbreak[$i] = 'T';
                                    } else {
                                        $isChecked = '';
                                    }

                                    $pgIndex = GetPageIndexForColumn($page, $i);
                                    $no_textcols = $no_of_textcols[$pgIndex];
                                    $no_numbercols = $no_of_numbercols[$pgIndex];

                                    if ( $IsDescriptiveColumn && $colValueType != 'ATR' ) {
                                        $colwidth[$i] = empty($no_textcols) ? 0 : (($pgWidthCalc - ($no_numbercols * $numbercolwidth)) / $no_textcols);
                                    } else {
                                        $colwidth[$i] = $numbercolwidth;
                                    }


                                } else {
                                    /** @noinspection PhpUndefinedVariableInspection */
                                    $isChecked = ( $colpgbreak[$i] == 'T' ? 'checked' : '' );
                                }

                                // for new columns set the default values
                                if ( empty($_moveLorR) && !$thiscol['ORIGINALCOL'] && empty($thiscol['EXPOF']) ) {
                                    $colfontname[] = $colfontname[$i];
                                    /** @noinspection PhpUndefinedVariableInspection */
                                    $colfontitalic[] = $colfontitalic[$i];
                                    /** @noinspection PhpUndefinedVariableInspection */
                                    $colfontbold[] = $colfontbold[$i];
                                    /** @noinspection PhpUndefinedVariableInspection */
                                    $colfontsize[] = $colfontsize[$i];
                                    $colfontcolor[] = $colfontcolor[$i];

                                    $colfontname[$i] = 'Helvetica';
                                    $colfontitalic[$i] = '';
                                    $colfontbold[$i] = '';
                                    $colfontsize[$i] = '10';
                                    $colfontcolor[$i] = 'black';
                                }

                                // Backward compatibility: for empty COLFONT, set the value from BODYFONT
                                if(empty($colfontname[$i]) || $colfontname[$i] === '####'){
                                    global $_bodyweight, $_bodytype;
                                    $colfontname[$i] = &Request::$r->_bodyfont;
                                    $colfontitalic[$i] = $_bodytype;
                                    $colfontbold[$i] = $_bodyweight;
                                    $colfontsize[$i] = &Request::$r->_bodysize;
                                    $colfontcolor[$i] = &Request::$r->_bodycolor;
                                }

                                /** @noinspection PhpUndefinedVariableInspection */
                                $colItalicChecked = ( $colfontitalic[$i] == 'italic' ? 'checked' : '' );
                                /** @noinspection PhpUndefinedVariableInspection */
                                $colBoldChecked = ( $colfontbold[$i] == 'bold' ? 'checked' : '' );
                                /** @noinspection PhpUndefinedVariableInspection */
                                $selectedsize = ( $colfontsize[$i] ?: '10' );

                                /** @noinspection PhpUndefinedVariableInspection */
                                $colpgbreak[$i] = ( $colpgbreak[$i] == '' ? 'F' : $colpgbreak[$i] );

                                      $columntype = $colValueType;
                                /** @noinspection PhpUndefinedVariableInspection */
                                $attributeObject = $colAttributeObject;
                                $attributeField = $colAttributeField;
                                if($thiscol['TYPE'] === 'VDS'){
                                    $colwidth[$i] = $_scale == 'P' ? 25.0 : 0.35; // 1 inch = 72 points
                                }

                                ?>
                             <tr style='<?if($thiscol['COLHIDE'] == 'Y') { echo "display:none;";
}?> background-color: <?=$altColor?>'>
            				 <td  style='color:  #355E8B;  background-color: #e4ebf5;' align=center nowrap style="padding:4px"><?=$index?>
            				 <input type="hidden" name="sortord[<?=$i?>]" value="<?=$index-1?>">
            				<?if ($thiscol['ORIGINALCOL']) { ?>
            					<input type="hidden" name="originalColSortOrd[<?=$originalCol++?>]" value="<?=$index-1?>">
            				<?
} ?>
            				 </td>
            				 <td  style='color:  #355E8B;  background-color: #e4ebf5;' align=left ><?if ($title) { echo $title;
} else { echo "--{$text->GT('IA.NO_TITLE')}--";
}?></td>
            				 <td  style='color:  #355E8B;  background-color: #e4ebf5;' align=left nowrap><?=DBTokensHandler::getInstance()->getExternalLabel($selectedPeriod)?></td>
            				 <td align=center  nowrap <?=($_definedby != 'F' && !($_definedby == 'S' || $_definedby == '')) ? '' : 'style="display: none;"';?>>
            					<input type="hidden" name="expof[<?=$i?>]" value="<?=$expof?>">
                                <input type="hidden" name="columntype[<?=$i?>]" value="<?=$columntype?>">
                                <input type="hidden" name="columnattribute[<?=$i?>]" value="<?=$attributeObject?>">
                                <input type="hidden" name="columnfield[<?=$i?>]" value="<?=$attributeField?>">
            					<input type="hidden" name="columnhide[<?=$i?>]" value="<?=($thiscol['COLHIDE'] == 'Y')?'Y':'N'?>">
            					<input type="text" name="colwidth[<?=$i?>]" value="<?= /** @noinspection PhpUndefinedVariableInspection */
                                ( $thiscol['COLHIDE'] == 'Y')?0:(isset($colwidth[$i])?( $colwidth[$i] == '' ? 0 : $colwidth[$i] ):0)?>"
            					        	size="4" maxlength="4" <?=($_definedby != 'F' && $thiscol['TYPE'] !== 'VDS') ? $defaultState : 'disabled';?>
            								onfocus="document.mf.onfocusvalue.value=this.value;"
            								onblur="FRL_validateWidth(<?=$i?>, this)">
                             </td>
            				 <td align=center  nowrap <?=($_definedby != 'F' && !($_definedby == 'S' || $_definedby == '')) ? '' : 'style="display: none;"';?>>
            						<input type="checkbox" class="noborder"  name="colpgbreak[<?=$i?>]"
            							value="<?= util_encode($colpgbreak[$i]) ?>" <?=$isChecked;?> <?=($_definedby != 'F') ? $defaultState : 'disabled';?>
            							onClick="this.value = this.checked ? 'T' : 'F'; FRL_onclick_colPGBreak(this);" >
            				</td>
            				 <td align=center  nowrap>
            					<select name="colfontname[<?=$i?>]" size="1" <?=$defaultState;?> >
                    <?ShowFinOptions($colfontname[$i], $fontMap);?>
            					</select>
            				</td>
            				 <td align=center  nowrap>
            					<select name="colfontsize[<?=$i?>]" size="1" <?=$defaultState;?> >
                    <? ShowFinOptions($selectedsize, $smallSizeMap);?>
            					</select>
            				</td>
            				 <td align=center  nowrap>
            					<select name="colfontcolor[<?=$i?>]" size="1" <?=$defaultState;?> >
                    <?ShowFinOptions($colfontcolor[$i], $colorMap);?>
            					</select>
            				</td>
            				 <td align='center'  nowrap style='padding-right: 5px; padding-left: 5px;  '>
                            <input type="checkbox" class="noborder"  name="colfontbold[<?=$i?>]"
                             value="<?= util_encode($colfontbold[$i]) ?>" <?=$colBoldChecked?>
                             onclick="if(this.checked){this.value='bold';}else{this.value='normal';}" <?=$defaultState;?>>
            				</td>
            				 <td align='center'  nowrap style='padding-right: 5px; padding-left: 5px;  '>
                            <input type="checkbox" class="noborder"  name="colfontitalic[<?=$i?>]"
                             value="<?= util_encode($colfontitalic[$i]) ?>" <?=$colItalicChecked?>
                             onclick="if(this.checked){this.value='italic';}else{this.value='normal';}" <?=$defaultState;?>>
            				</td>


                             </tr>

                                <?
                            }
                ?>
            		</tbody>
            		</TABLE>
                <?if ($_definedby == 'U') {?>
            	<P style='text-align: left; color: #355E8B'>* <span id='scale_text2'><?= $text->GT($scale_text);?></span>
                <?
}
        echo "<script>pinLeftTabMenu();SetContentHeight(true);</script>";
if ($defaultState == 'disabled') {
    echo '</div>';
};
}

function EditReportGenerateSummary()
{
    global $gNoReportSave, $fromCSTool;
    $_title    = &Request::$r->_title;
    $_r        = &Request::$r->_r;
    $_status   = &Request::$r->_status;
    $_optimizeQuery   = &Request::$r->_optimizeQuery;
    $_name     = &Request::$r->_name;
    $_rptowner = &Request::$r->_rptowner;

    EditReportHeaderSeperator();
    $text = TextHelper::getInstance(FinancialReportWizard::class);

    ?>

	<input type=hidden name=".colcomparison">

    <div class='reportSection'>
        <div class='reportSectionLabel'><?=$text->GT('IA.REPORT_INFORMATION')?>
        </div>
        <fieldset>
            <table style='width: 1px'>
                <tr>
                    <td style='vertical-align: bottom;' class='reportFieldLabel'><label><?=$text->GT('IA.REPORT_NAME')?></label>&nbsp;</td>
                    <td class='reportFieldValue'>
                        <div style='white-space: nowrap;' class="requiredOuter">
                            <?=isl_htmlspecialchars($_name)?>
                        </div>
                    </td>
                 </tr>
                 <tr><td><div class='linespacer'></div></td></tr>
                    <?if (!$fromCSTool) { ?>
                        <tr>
                            <td class="reportFieldLabel" ><label>&nbsp</label></td>
                            <td class="reportFieldValue">
                                <table style='margin-top: 10px'>
                                    <tr>
                                        <td>
                                            <input name=".status" type='checkbox' value='active' <?php if ( $_status
                                                                                                            == 'active' ) {
                                                echo 'checked';
                                            } ?>>
                                        </td>
                                        <td class='checkboxalign'>
                                            <label style='white-space: nowrap'><?= $text->GT('IA.THIS_REPORT_IS_ACTIVE') ?></label>
                                        </td>

                                    </tr>
                                </table>
                            </td>
                    </tr>
                    <?
};?>
                 <tr><td><div class='linespacer'></div></td></tr>
                 <tr>
                    <td  style='vertical-align: bottom;' class='reportFieldLabel'><label><?=$text->GT('IA.REPORT_OWNER')?></label>&nbsp;</td>
                    <td class='reportFieldValue'>
                        <div  style='white-space: nowrap;' class="requiredOuter">
                            <?=isl_htmlspecialchars($_rptowner)?>
                        </div>
                    </td>
                 </tr>
             </table>
         </fieldset>
         <div class="reportSectionLabelText">
             <P>
             <?=$text->GT('IA.CLICK_THE_B_PREVIEW_B_BUTTON_TO_DISPLAY_THE_REPORT')?>
             <br>
             <?=$text->GT('IA.RETURN_TO_THE_B_FORMATTING_B_TAB_TO_CHANGE_THE_DIS')?>
             <P>
             <?=$text->GT('IA.ONCE_YOU_ARE_SATISFIED_BE_SURE_TO_B_SAVE_B_THE_REP')?>
         </div>
    </div>
        <?if( FinancialReportHelper::getInstance()->canShowBetaCheckbox() && !$fromCSTool ){?>
            <div class='reportSection'>
                <div class='reportSectionLabel'><?=$text->GT('IA.PERFORMANCE')?>
                </div>
                <fieldset>
                    <table style='width: 1px'>
                        <tr>
                            <td class="reportFieldValue">
                                <table>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="noborder" name="optimizeQuery" value="F"
                                                <?=($_optimizeQuery == 'T' ? 'CHECKED' : ''); ?>
                                                   onchange="document.mf.elements['.optimizeQuery'].value=(document.mf.elements['optimizeQuery'].checked?'T':'F')">
                                        </td>
                                        <td class='checkboxalign'>
                                            <label style='white-space: nowrap'><?=$text->GT('IA.TRY_BETA_IMPROVEMENTS')?></label>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </fieldset>
            </div>
        <?}?>
    <?if (!$fromCSTool) { EditReportSectionSeperator();
}
    /** @noinspection PhpUnusedLocalVariableInspection */
    $helpUrlPath1 = GetHelpFullPath('GL_FinancialReportEditor_NextSteps_Favorite', false);
    $helpUrlPath2 = GetHelpFullPath('GL_FinancialReportEditor_NextSteps_AddToGroup', false);
    /** @noinspection PhpUnusedLocalVariableInspection */
    $helpUrlPath3 = GetHelpFullPath('GL_FinancialReportEditor_NextSteps_Schedule', false);
    $helpUrlPath4 = GetHelpFullPath('GL_FinancialReportEditor_NextSteps_Dashboard', false);

    /** @noinspection PhpUnusedLocalVariableInspection */
    $reportGroupPermissions = 'true'; /// ugh... not bemissions but module.

    /* @var Security $gSecurity */
    global $gSecurity;
    $reportGroupPermissions = ($gSecurity->IsOperationAllowed('memrptgrp', 'list')) ? 'true' : 'false';

    ?>
    <div class='reportSection'  <? if ($fromCSTool) { echo "style='display: none'";
}  ?> >
        <div class='reportSectionLabel'><?=$text->GT('IA.WHAT_S_NEXT')?></div>
        <div class="reportSectionLabelText">
              <ul style='padding-left: 20px'>
                <li><A style='text-decoration: underline' href='javascript:createReportsMyFavorite_foo();' ><?=$text->GT('IA.ADD_THIS_REPORT_TO_YOUR_MY_FAVORITES_LIST_IN_THE_R')?></A></li>
                <li><A style='text-decoration: underline'  href='javascript:doAddToDashboard();'><?=$text->GT('IA.ADD_THIS_REPORT_TO_A_DASHBOARD')?></A></li>
                    <?
                    $allowedperms = GetPagePermissions(GetOperationId('gl/reports/financialreport'));
                    if (isset($allowedperms['edit']) && (empty($gNoReportSave) || $gNoReportSave != '1')) {
                    ?>
                        <li><A style='text-decoration: underline'  href='javascript:gotoSchedule_foo();'><?=$text->GT('IA.SCHEDULE_THIS_REPORT_TO_RUN_PERIODICALLY')?></A></li>
                        <li><A style='text-decoration: underline'  href='javascript:gotoReportGroup_foo();'><?=$text->GT('IA.INCLUDE_THIS_REPORT_IN_A_REPORT_GROUP')?></A></li>
                    <?
                    }?>
               </ul>
        </div>
    </div>
    <input name="whatNextPostSaveAction" id="whatNextPostSaveAction" value='' type="hidden">
    <script>
    function createReportsMyFavorite_foo() {
        createReportsMyFavorite();
    }
    function gotoSchedule_foo() {
        saveAndGotoSchedule();
    }
    function gotoReportGroup_foo() {
        if (<?=$reportGroupPermissions?>) {
            saveAndGotoReportGroup();
        } else {
            reportGroupPermissionsDialog('<?=$helpUrlPath2?>');
        }
    }


    </script>
    <!-- This is for Add To Dashboard functionality where a memorized report is created and added to a dashboard -->
    <input name="domemorize" id="domemorize" value='false' type="hidden">
    <input name="memorizewithdescription" id="memorizewithdescription" value='' type="hidden">
    <input name="memorizepublicflag" id="memorizepublicflag" value='' type="hidden">


    <input name="memorizewithname" id="memorizewithname" value='<?isl_htmlspecialchars($_title)?>' type="hidden">
    <input name="memorizetodashboard" id="memorizetodashboard" value='' type="hidden">
    <input name="memorizewithcolumn" id="memorizewithcolumn" value='' type="hidden">
    <input name="memorizenoofrows" id="memorizenoofrows" value='' type="hidden">
    <input name="memorizecompheight" id="memorizecompheight" value='' type="hidden">
    <input name="memorizeshowcollapsed" id="memorizeshowcollapsed" value='' type="hidden">
    <input name="memorizereportview" id="memorizereportview" value='' type="hidden">
    <input name="memorizecsrftoken" id="memorizecsrftoken" value='' type="hidden">


    <script>
    function doAddToDashboard() {

        //todo: make sure one dashboard exists. If not show help.

        <? if (!anyDashboards()) {?>
            var buttons = {
              "<?=$text->GT('IA.OK')?>" : function() {
                      jq( this ).dialog( "close" );
                    },
              "<?=$text->GT('IA.LEARN_MORE')?>" : function() {
                    Launch("<?=$helpUrlPath4?>", "nextstepshelp", 575, 450, false);
                    jq( this ).dialog( "close" );
                  }
            };

            var dialogText = "<?=$text->GT('IA.TO_ADD_THIS_REPORT_TO_A_DASHBOARD')?>";
                dialogText += "<P style='margin-top: 12px;'><?=$text->GT('IA.TRY_UPDATING_DASHBOARDS_DIRECTLY_FROM')?></P>";

            jq("<div title='<?=$text->GT('IA.ADD_TO_DASHBOARD')?>'><p style='font-size: 12px; margin: 10px; white-space: normal'>"+dialogText+"</div>").dialog(
                 {width: '450px',
                 dialogClass: 'addDimensionDialog',
                 closeText: 'X',
                 modal: true,
                 resizable: false,
                 minHeight: '0px',
                 buttons: buttons,
                 dragStop: function(event, ui){
                    if (ui.offset.top < 0) {
                        jq(this).closest(".ui-draggable").css('top','0px');
                    }
                 }
                });
             return;
        <?
} ?>

        var rid = document.getElementsByName(".r")[0].value;
        if (rid == '') {
            favoritesOKDialog("<?=$text->GT('IA.ADD_THIS_REPORT_TO_DASHBOARD')?>", "<?=$text->GT('IA.CANNOT_ADD_TO_DASHBOARD_PLEASE_SAVE')?>");
        } else {
            doAddToDashboard_DoIt();
        }
    }

    //Add To Dashboard scripts
    function doAddToDashboard_DoIt() {
        var fe_createopid = <?=GetOperationId('co/lists/memorizedreports/create')?>;

		var iHeight = 150;
		var iWidth = 450;

        if (top && top.quixote) {
            iHeight = 300;
            iWidth = 600;
        }

		jq('#domemorize').val('');

		var url = "memorizeoptions.phtml?.sess="+gSess+"&.op="+fe_createopid+"&.financialReport=true&.financialReportTitle="+encodeURIComponent('<?=util_encode($_title)?>')+"&.addToDash=true&.isgraph=false";
		baseModalDialog(url, "", window, resumeDoMemorize, iWidth, iHeight, false);
    }

 	function resumeDoMemorize() {
        var fe_editopid = <?=GetOperationId('gl/reports/financialreport/edit')?>;
 	    if(jq('#domemorize').val() == "true") {
			var url = "FinancialReportWizard.phtml?.sess="+gSess;
			url = url + "&.addToDashboard=true";
			url = url + "&.FinancialReportWizard_dash_reportname=" + encodeURIComponent(jq('#memorizewithname').val());
			url = url + "&.FinancialReportWizard_dash_reporttodashboard=" + jq('#memorizetodashboard').val();
			url = url + "&.FinancialReportWizard_dash_reportwithcolumn=" + jq('#memorizewithcolumn').val();
			url = url + "&.FinancialReportWizard_dash_reportnoofrows=" + jq('#memorizenoofrows').val();
			url = url + "&.FinancialReportWizard_dash_reportcompheight=" + jq('#memorizecompheight').val();
			url = url + "&.FinancialReportWizard_dash_reportshowcollapsed=" + jq('#memorizeshowcollapsed').val();
			url = url + "&.FinancialReportWizard_dash_reportview=" + jq('#memorizereportview').val();
			url = url + "&.op=" + fe_editopid;
			url = url + "&.FinancialReportWizard_dash_key=<?=$_r?>";
			url = url + "&.r=<?=$_r?>";

            jq.ajax(url, {
                type: "POST",
            });

            favoritesOKDialog("<?=$text->GT('IA.REPORT_ADDED_TO_DASHBOARD')?>", "<?=$text->GT('IA.THIS_REPORT_HAS_BEEN_ADDED_TO_YOUR_DASHBOARD')?>");
 	    }
		jq('domemorize').val('');

		return true;
	}


    pinLeftTabMenu();
	SetContentHeight(true);
    jq(window).bind('resize', function () {
    	SetContentHeight();
    });
    </script>

    <?
}


if (!isset($_schopKey) && isset($_POST['_schopKey'])) {
    $_schopKey = $_POST['_schopKey'];
}
?>
    <input type='hidden' id='_schopKey' name='_schopKey' value='<?= util_encode($_schopKey) ?>'>
<?



function EditReportGeneratePagePermissions()
{
        global $MODNAME;
        global $POLNAME;
        global $kGLid;
        $mylogin = GetMyLogin();
        $public = GetPreferenceForProperty($kGLid, 'DEFAULT_FINRPT_PUBLIC');

        $MODNAME = "gl";
        $POLNAME = "Financial Report";

        $_name = &Request::$r->_name;
        $_rptowner = &Request::$r->_rptowner;

        $form = 'mf';
        $path = $_name;
        $default_perm = ($public == 'T')? 'allow_0' : 'deny_0';

        $isAdmin = (GetMyAdminLevel() == '2');

        $mode = ( ($_rptowner == $mylogin) || $isAdmin ) ? 'active':'inactive';

        RenderPermissions($form, $default_perm, $mode);
}


/**
 * @return bool
 */
function locationFilterNeeded()
{
    global $LocGrpRec, $_location_grp;

    $_reportingBook = &Request::$r->_reportingBook;
    $_location      = &Request::$r->_location;
    $_runtimeparams = &Request::$r->_runtimeparams;

    $promptLocation_selected = in_array('location', explode(":", $_runtimeparams));
    if (IsMCMESubscribed() && !GLBookManager::IsConsolidationBook($_reportingBook, false)) {
        if(!$promptLocation_selected && !GetContextLocation()) {
            if(isl_trim($_location) == '' && !isset($LocGrpRec) && !isset($_location_grp)) {
                return true;
            }
        }
    }
    return false;
}

/**
 * @return bool
 */
function reportHasRows()
{
    $_lineno = &Request::$r->_lineno;
    global $kDelim;
    $lineNoMap = explode($kDelim, $_lineno ?? '');
    return ($lineNoMap !== false && count($lineNoMap) >= 1);
}

/**
 * @return bool
 */
function missingAccountGroupsRowsTab()
{
    $_lineno = &Request::$r->_lineno;
    $missingAccountGroupsRowsTab = false;
    $_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
    $_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;
    $_rowExpandByAccountGroup      = &Request::$r->_rowExpandByAccountGroup;
    global $kDelim;

    $lineNoMap = explode($kDelim, $_lineno);
    $lineNoMap = array_flip($lineNoMap);

    if ($_dimensionRowsExpandByAccount == 'T' && $_accountOrDimensionRows == 'dimension') {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $rowExpandByAccountGroupMap = array();
        if (isset($_rowExpandByAccountGroup) && $_rowExpandByAccountGroup != '') {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $rowExpandByAccountGroupMems = explode($kDelim, $_rowExpandByAccountGroup);
            $lineNoMap = explode($kDelim, $_lineno);
            $accountMap = explode($kDelim, $_rowExpandByAccountGroup);

        }

        $rowCt = sizeof($lineNoMap);
        for($i = 0; $i < $rowCt; $i++) {
            $outermost = count(explode("/", ($lineNoMap[$i][0] != "/" ? $lineNoMap[$i] : substr($lineNoMap[$i], 1) ))) <= 1;
            /** @noinspection PhpUndefinedVariableInspection */
            if ( $outermost && isl_trim($accountMap[$i]) == '') {
                $missingAccountGroupsRowsTab = true;
                break;
            }
        }
    }

    return $missingAccountGroupsRowsTab;
}

/**
 * @return bool
 */
function missingAccountGroupsColumnsTab()
{
    $missingAccountGroupsColumnsTab = false;
    $_accountOrDimensionRows = &Request::$r->_accountOrDimensionRows;
    $_colAccountGrp          = &Request::$r->_colAccountGrp;
    $_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;
    $_colValueType = Request::$r->_colValueType;

    global $kDelim;
    $colValueType = explode($kDelim, $_colValueType);

    if ( isset($_colAccountGrp) && $_colAccountGrp != '' ) {
        $colAccountGrp = explode($kDelim, $_colAccountGrp);
    }

    if (($_dimensionRowsExpandByAccount == 'F'|| $_dimensionRowsExpandByAccount == '') && $_accountOrDimensionRows == 'dimension') {
        $columnCount = sizeof($colValueType);
        for($i = 0; $i < $columnCount; $i++) {
            /** @noinspection PhpUndefinedVariableInspection */
            if ( isl_trim($colAccountGrp[$i]) == '' && !in_array($colValueType[$i], array( 'DSC', 'DSCN', 'AN', 'S', 'ATR', 'VDS', 'NOT'))) {
                $missingAccountGroupsColumnsTab = true;
                break;
            }
        }
    }

    return $missingAccountGroupsColumnsTab;
}

/**
 * @return bool
 */
function missingDimensionStructureColumnsTab()
{
    $missingDimensionStructureColumnsTab = false;
    $_accountOrDimensionRows       = &Request::$r->_accountOrDimensionRows;
    $_colAccountGrp                = &Request::$r->_colAccountGrp;
    $_compareby                    = &Request::$r->_compareby;
    $_dimensionRowsExpandByAccount = &Request::$r->_dimensionRowsExpandByAccount;
    $colValueType                  = Request::$r->colValueType ?? [];

    global $kDelim;

    if ( isset($_colAccountGrp) && $_colAccountGrp != '' ) {
        $colAccountGrp = explode($kDelim, $_colAccountGrp);
    }
    if ( isset($_compareby) && $_compareby != '' ) {
        $compareby     = explode($kDelim, $_compareby);
    }

    if (($_dimensionRowsExpandByAccount == 'F'|| $_dimensionRowsExpandByAccount == '') && $_accountOrDimensionRows == 'account') {
        $columnCount = countArray($colValueType);
        for($i = 0; $i < $columnCount; $i++) {
            /** @noinspection PhpUndefinedVariableInspection */
            if ( isl_trim($colAccountGrp[$i]) == '' && ( $compareby[$i] == 'ACCT' || $compareby[$i] == 'ACCTALL') && !in_array($colValueType[$i], array( 'DSC', 'DSCN', 'AN', 'S', 'ATR', 'VDS', 'NOT'))) {
                $missingDimensionStructureColumnsTab = true;
                break;
            }
        }
    }

    return $missingDimensionStructureColumnsTab;
}

/**
 * @return bool
 */
function showTopBottomSettingsOnCol()
{
    $_bot = &Request::$r->_bot;
    $_top = &Request::$r->_top;
    global $kDelim;

    /** @noinspection PhpUnusedLocalVariableInspection */
    $topbotcolMap = array();
    if (isset($_top) && $_top != '') {
        $topbotcolMems = explode($kDelim, $_top);
        foreach ( $topbotcolMems as $trialVal ) {
            if ($trialVal != '') {
                    return true;
            }
        }
    }

    /** @noinspection PhpUnusedLocalVariableInspection */
    $topbotcolMap = array();
    if (isset($_bot) && $_bot != '') {
        $topbotcolMems = explode($kDelim, $_bot);
        foreach ( $topbotcolMems as $trialVal ) {
            if ($trialVal != '') {
                return true;
            }
        }
    }

    return false;
}

/**
 * @return bool
 */
function missingTopBottomSettings()
{
    global $kDelim;

    $_colShowAs   = &Request::$r->_colShowAs;
    $_compareby   = &Request::$r->_compareby;
    $_topbotcol   = &Request::$r->_topbotcol;
    $_colValueType = Request::$r->_colValueType;

    if (!showTopBottomSettingsOnCol()) {
        return false;
    }

    $colValueType = explode($kDelim, $_colValueType);

    /** @noinspection PhpUnusedLocalVariableInspection */
    $topbotcolMap = array();
    if (isset($_topbotcol) && $_topbotcol != '') {
        $topbotcolMems = explode($kDelim, $_topbotcol);
        $check_count = count($colValueType);
        foreach ( $topbotcolMems as $trialVal ) {
            if ($trialVal != '') {
                if ( $trialVal > $check_count ) {
                    return true;
                }
            }
        }

        $coltypehide =  array('a', 'x', 'b');

        if ( isset($_compareby) && $_compareby != '' ) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $compareby     = explode($kDelim, $_compareby);
        }
        /** @noinspection PhpUnusedLocalVariableInspection */
        $expandyby = $_compareby[(int)$topbotcolMems[0] - 1];
        if (!isset($expandby) || $expandby == '') {
            $expandby = 'T';
        }

        if ( isset($_colShowAs) && $_colShowAs != '' ) {
            $colShowAs    = explode($kDelim, $_colShowAs);
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $showas = $colShowAs[(int)$topbotcolMems[0] - 1];
        if (!isset($expandby) || $expandby == '') {
            $expandby = '';
        }

        if (in_array($colValueType[(int)$topbotcolMems[0]-1], $coltypehide)) {
            if ($expandby == 'T' && ($showas == '' || $showas == 'NC')) {
                return false;
            }
        }
    };


    return true;
}

/**
 * @return bool
 */
function isValidAdjBookSelection()
{
    $colreportingMethod = &Request::$r->colreportingMethod;
    $ok = true;
    for ( $ij = 0; $ij < countArray($colreportingMethod); $ij++ ) {
        if ( $colreportingMethod[$ij] != '' ) {
            [$adjBookStr, $includeRepBook] = explode(';', $colreportingMethod[$ij]);
            if ( $adjBookStr == '' && $includeRepBook == 'false' ) {
                $ok = false;
                break;
            }
        }
    }
    return $ok;
}

/**
 * @return bool
 */
function canHideTotal()
{
    $_compareby     = &Request::$r->_compareby;
    $_rightlefthide = &Request::$r->_rightlefthide;
    $rightlefthide = [];
    $compareby = [];
    global $kDelim;
    if ( isset($_rightlefthide) && $_rightlefthide != '' ) {
        $rightlefthide     = explode($kDelim, $_rightlefthide);
    }


    if ( isset($_compareby) && $_compareby != '' ) {
        $compareby     = explode($kDelim, $_compareby);
    }

    $cannotHideThese = array ('ACCT', 'ACCTALL', 'ACCTLEAF');
    foreach ( $rightlefthide as $ij => $val ) {
        if ( $val == 'H' && in_array($compareby[$ij], $cannotHideThese)) {
            return false;
        }
    }

    return true;
}


// Only hide the Save, Save As... buttons when editing a new report.
/** @noinspection PhpUndefinedVariableInspection */
if ($hideSaveButtons) {
        $this->page['hasdone'] = '0';
}

$headerLabel = ($_pg == '1') ? "" :  $_name;

$canShowComputations = true; // eventually we want to hide the tab in the 'new' case and only
                             //have it show once you try to use a computation on a column.

$reportHasAcctGrps = (($_pg == '1' || $_pg == '3') && empty($_r) && countArray($groups) == 0 ) ? '1' : '0';


//Check to see if we need a required flag on rows/columns tabs:
if (!reportHasRows()) {
    $requiredRows = false;
    $requiredColumns = false;
    $requiredFilters = false;
    $selectTopBot = false;
} else {
    $requiredColumns = missingAccountGroupsColumnsTab();
    $requiredColumnStructure = missingDimensionStructureColumnsTab();
    $requiredRows = missingAccountGroupsRowsTab();
    $requiredFilters = locationFilterNeeded();
    $selectTopBot = missingTopBottomSettings();
}
global $errorPages;
if (!isset($errorPages)) {
    $errorPages = array();
}

$tabContent = new FinancialReportWizard(
    $headerLabel, $kSetupPages[$_pg], $_pg,
    'EditReportGenerateTabContent', array(), $fromCSTool,
    $gNoReportSave, count($groups) > 0, $canShowComputations,
    $requiredRows, $requiredColumns, $requiredFilters, $requiredColumnStructure, $errorPages
);
$tabContent->GenerateWizard();

/**
 * @param array  $context
 */
function EditReportGenerateTabContent(/** @noinspection PhpUnusedParameterInspection */ $context)
{
    //This is passed in as a callback to EditReportWizard write the individual tabs
    global $fromCSTool;
    $_pg = &Request::$r->_pg;

    if ($_pg != '11' && $_pg != '1' ) {
        $form = 'mf';
        upermSetFRWizardInterPageCachedPermissions($form); //this is to cache permissions from page to page.
    }

    DrawDupNameSection();
    DrawWaitScrim();

    $pageMap = array (
        '1' => 'EditReportGeneratePageTitle',
        '3' => 'EditReportGeneratePageRowsStructure',
        '4' => 'EditReportGeneratePageComputations',
        '5' => 'EditReportGeneratePageColumns',
        '10' => 'EditReportGeneratePageSliceAndDice',
        '7' =>  'EditReportGeneratePageFormat',
        '12' => 'EditReportGeneratePageRowsFormatting',
        '11' => 'EditReportGeneratePagePermissionsHack',
        '8' =>  'EditReportGenerateSummary',
        '13' =>  'EditReportGeneratePageNotations',

    );

    $callFunc = $pageMap[$_pg];
    if (( $callFunc == 'EditReportGeneratePermissions' && $fromCSTool)
    ) {
        //no-op
    } else {
        $callFunc();
    }

} // end EditReportGenerateTabContent

?>



</form>
</center>
<script>
    var listFields = "<?php echo $tabContent->getListFields(); ?>";
    jq().ready(function () {
        removeSaveFinRptDisableClass('#btnfinrptdup');
        removeSaveFinRptDisableClass('#btnfinrptsave');
        removeSaveFinRptDisableClass('#btnfinrptsavedone');

        jq('#btnfinrptsave').on('click',function() {
            SetNoCheckRequired();
            DoSubmit(listFields,'.savestay','');
        });

        jq('#btnfinrptsavedone').on('click', function() {
            SetNoCheckRequired();
            DoSubmit(listFields,'.save','');
        });

        jq('#btnfinrptdup').on('click', function() {
            SetNoCheckRequired();
            DoSubmit(listFields,'.saveas','');
        });
    });
    function removeSaveFinRptDisableClass(fieldid)
    {
        var fld = jq(fieldid);
        if(fld.length && fld.hasClass('disabledFormSubmit'))
        {
            fld.removeClass('disabledFormSubmit');
        }
    }
</script>
</body>
</html>
