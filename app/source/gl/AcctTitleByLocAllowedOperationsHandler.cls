<?php
/**
 * AcctTitleByLocAllowedOperationsHandler
 *
 * <AUTHOR> Behere
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 *
 */

class AcctTitleByLocAllowedOperationsHandler extends AllowedOperationsHandler
{

    public function __construct(EntityManager $entManager)
    {
        parent::__construct($entManager);
    }
    /**
     * Override of parent method, necessary for non-megalized entity.
     *
     * @inheritDoc
     */
    protected function isValidOwner(array $record): bool
    {
        return true;
    }
}