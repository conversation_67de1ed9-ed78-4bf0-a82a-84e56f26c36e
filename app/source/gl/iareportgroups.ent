<?php
/**
 *    FILE:            iareportgroups.ent
 *    AUTHOR:            Senthil
 *    DESCRIPTION:    entity definition for iareportgroups object
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/


$kSchemas['iareportgroups'] = array (
    'object' => array (
         'REPORTNO', 'SORTORD', 'ACCTGRPNO', 'INDUSTRYCODE',
    ),
    'schema' => array (
        'REPORTNO'        => 'report#',
        'SORTORD'        => 'sortord',
        'ACCTGRPNO'        => 'acctgrp#',
        'INDUSTRYCODE'    => 'industrycode',
    ),
    'fieldinfo' => array (
        array (
            'path' => 'REPORTNO',
            'fullname' => 'IA.REPORT_NUMBER',
            'desc' => 'IA.REPORT_NUMBER',
            'hidden' => true,
            'readonly' => true,
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'size' => 8,

            )
        ),
        array (
            'path' => 'SORTORD',
            'fullname' => 'IA.SORT_ORD',
            'desc' => 'IA.SORT_ORD',
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'size' => 4,

            )
        ),
        array (
            'path' => 'ACCTGRPNO', //'acctgrp#'
            'fullname' => 'IA.ACCT_GRP_NO',
            'desc' => 'IA.ACCT_GRP_NO',
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'size' => 8,

            )
        ),
        array (
            'path' => 'INDUSTRYCODE',
            'fullname' => 'IA.INDUSTRY_CODE',
            'desc' => 'IA.INDUSTRY_CODE',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            )
        ),
    ),
    'table' => 'iareportgroups',
    'global' => true,
    'vid' => 'REPORTNO',
    'module' => 'gl'
);
