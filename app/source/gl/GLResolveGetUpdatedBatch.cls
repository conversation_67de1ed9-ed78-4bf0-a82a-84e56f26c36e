<?php
/**
 * Created by JetBrains PhpStorm.
 * User: nshirole
 * Date: 29/11/13
 * Time: 1:33 PM
 * To change this template use File | Settings | File Templates.
 */

class GLResolveGetUpdatedBatch extends GetUpdatedBatch
{
    /**
     * Set batch boundaries
     */
    protected function setMinMaxRec() 
    {
        $minMax = $this->getGLEntryKeyMinMax();
        if ( $minMax == null ) {
            $this->setNodata();
        }
        else {
            $this->setMinRec($minMax['MINREC']);
            $this->setMaxRec($minMax['MAXREC']);
        }
    }

    /**
     * Get min max value
     * 
     * @return null|array
     */
    protected function getGLEntryKeyMinMax( )
    {
        $asofQry = " AS OF TIMESTAMP TO_TIMESTAMP('".$this->getReadTimeLocalTZ()."', 'MM/DD/YYYY HH24:MI:SS')";
        $whenCreatedQry = " AND whencreated < to_date(:2, 'MM/DD/YYYY HH24:MI:SS')";
        $whenModifiedQry = " AND whenmodified >= to_date(:2, 'MM/DD/YYYY HH24:MI:SS')";
        $whenModifiedQry .= " AND whenmodified < to_date(:3, 'MM/DD/YYYY HH24:MI:SS')";

        // Record min record#
        $minQry = "SELECT MIN(glentrykey) minrec FROM v_glresolvemst $asofQry WHERE cny# = :1 ".
            $whenCreatedQry.$whenModifiedQry;
        $minRes = QueryResult(array($minQry, GetMyCompany(), $this->getTimestampGMT(), $this->getFlashBackStartTime()));

        if ( !isset($minRes[0]['MINREC']) ) {
            return null;
        }

        $arr['MINREC'] = intval($minRes[0]['MINREC']);

        // Record max record#
        $maxQry = "SELECT MAX(glentrykey) maxrec FROM v_glresolvemst $asofQry WHERE cny# = :1 ".
            $whenCreatedQry.$whenModifiedQry;
        $maxRes = QueryResult(array($maxQry, GetMyCompany(), $this->getTimestampGMT(), $this->getFlashBackStartTime()));

        if ( !isset($maxRes[0]['MAXREC']) ) {
            return null;
        }

        $arr['MAXREC'] = intval($maxRes[0]['MAXREC']);

        return $arr;
    }

}