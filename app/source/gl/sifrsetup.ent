<?php

/**
 * sifrsetup.ent
 *
 * <AUTHOR>  <raja.m<PERSON><PERSON><PERSON><PERSON><PERSON>@sage.com>
 * @copyright 2024 Sage Intacct Inc, All Rights Reserved
 */

$kSchemas['sifrsetup'] = array(
    'fake' => true,
    'schema' => array(
        'MODULE_CONFIGURED' => 'module_configured'
    ),
    'fieldinfo' => array(
        array(
            'path' => 'MODULE_CONFIGURED',
            'fullname' => 'IA.MODULE_CONFIGURED',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'boolean',
                'validvalues' => array('T', 'F'),
                '_validivalues' => array('T', 'F'),
            ),
            'default' => 'T',
        ),
    ),
    'table' => 'modulepref',
    'vid' => 'modulekey',
    'module' => 'co',
    'api' => array(
        'PERMISSION_READ' => 'co/sifrsetup/edit',  // Used for audit trail - there is no 'view'
    ),
);