<?php

/**
 * GlAccountingBooksService
 *
 * <AUTHOR>
 * @copyright Copyright (C)2022 Sage Intacct Corporation, All Rights Reserved
 */

require_once 'util.inc';
require_once 'xml_api.inc';
require_once 'backend_error.inc';

class GlAccountingBooksService
{

    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return array
     */
    public function closeBooks(array $request, array $extraContext) : array
    {
        return $this->bookActionHandler($request,'closebooks',  $extraContext);
        
    }

    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return array
     */
    public function openBooks(array $request, array $extraContext) : array
    {
        return $this->bookActionHandler($request,'openbooks',  $extraContext);
        
    }


    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return array
     */
    public function bookActionHandler(array $request, string $entity,  array $extraContext) : array
    {
        global $gErr;
        $objMgr = Globals::$g->gManagerFactory->getManager($entity);
        $result = $objMgr->API_Add($request);
        $gErr->GetErrList($errlist);
        $gErr->getWarningsList($warninglist);
        $errlist = INTACCTarray_merge($errlist, $warninglist);
        if (is_array($errlist)) {
            foreach ($errlist as $errors) {
                $result[] = $errors['CDESCRIPTION'];
            }
        }

        return ['STATUS' => $result];
    }

    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return array
     */
    public function closedBookPeriod(array $request, array $extraContext): array
    {
        $closeBookHandler = new CloseBooksHandler();

        $result =  $closeBookHandler->getClosePeriods($request, $extraContext);

        return $result;
    }
}