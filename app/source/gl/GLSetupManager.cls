<?php

/**
 * GLSetupManager.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation, All Rights Reserved
 */

define("MAX_GL_APPROVAL_LEVELS", 30);  // Somewhat arbitrary, for audit trail diffing only.

require_once 'ApprovalManager.cls';

/**
 * Manager for General Ledger Setup
 */
class GLSetupManager extends ModuleSetupManager
{
    /**
     * @var string $dimKeyOrder
     */
    private static $dimKeyOrder = null;

    /**
     * @var array $authorizedGLBatchApprovers
     */
    private static $authorizedGLBatchApprovers = null;

    /**
     * @param array $_params parameters
     */
    public function __construct(/** @noinspection PhpUnusedParameterInspection */ $_params = array())
    {
        $params['moduleKey'] = Globals::$g->kGLid;
        parent::__construct($params);
    }

    /**
     * Override parent's method
     * Get a set of preferences
     *
     * @param string        $id
     * @param string[]|null $fields
     *
     * @return array
     */
    public function get($id, $fields = null)
    {
        return $this->getWithDimSetup( true);
    }

    /**
     * Override parent's method
     * Get a set of preferences
     *
     * @param bool $needDimSetup
     *
     * @return array
     */
    public function getWithDimSetup($needDimSetup)
    {
        $retPreferences = parent::get('');
        unset($retPreferences['DUMMY']); // remove dummy value, we use it for layout spacing in the xml
        //START Modification Multibooks
        if ($retPreferences['MULTIBOOKS'] == '' || $retPreferences['MULTIBOOKS'] == 'F') {
            $retPreferences['COMPANYBOOK'] = (IsCompanyAccrual() ? 'Accrual' : 'Cash');
        } else if ($retPreferences['MULTIBOOKS'] == 'T') {
            $retPreferences['COMPANYBOOK'] = 'Accrual and cash';
        }
        //END Modification Multibooks

        if (!util_isPlatformDisabled() && $needDimSetup) {
            $this->getDimensionSetup($retPreferences);
        }

        $this->getPostDimenstions($retPreferences);

        $this->getAcctSeqNum($retPreferences);

        //Fetch GL Approval Setup Configuration from glapproverconfig
        $this->collectAllJournalApprovers($retPreferences);

        //Fetch GLOD Configuration from glodconfig
        $this->collectAllGlodConfig($retPreferences);

        //Fetch GL Approval Email Notification Setup
        $GlApproverEmailConfigMgr = Globals::$g->gManagerFactory->getManager(
            'glapproveremailconfig'
        );
        $GlApproverEmailConfigMgr->collectEmailNotificationData($retPreferences);

        return $retPreferences;
    }

    /**
     * @param array $values getting dimensions
     */
    private function getPostDimenstions(&$values)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $postDims = Util_DataRecordFormatter::jsonToPhp($values['AUTOSTATPOST']);

        $values['POSTDIMGRID'] = array();
        $sno = 0;
        $statacctMgr = $gManagerFactory->getManager('stataccount');
        $locMgr = $gManagerFactory->getManager('location');

        foreach ($postDims as $pDim) {

            if ($pDim['acct']) {
                $values['POSTDIMGRID'][$sno]['POSTDIMENSIONNAME'] = $pDim['entity'];

                $filter = array(
                    'selects' => array('ACCOUNTNO'),
                    'filters' => array(array(array('RECORDNO', '=', $pDim['acct']))),
                );

                $res = $statacctMgr->GetList($filter);

                $pDim['acct'] = $res[0]['ACCOUNTNO'];

                if ($pDim['loc']) {
                    $filter = array(
                        'selects' => array('LOCATIONID'),
                        'filters' => array(array(array('RECORDNO', '=', $pDim['loc']), array('STATUS', '=', 'active'))),
                    );

                    $res = $locMgr->GetList($filter);
                    $pDim['loc'] = $res[0]['LOCATIONID'];
                }

                if ($pDim['grp']) {

                    if (!stristr($pDim['entity'], "custdim_")) {

                        $grpMgr = DimensionGroupManager::getDimensionGroupManager($pDim['entity']);
                        $filter = array(
                            'selects' => array('ID'),
                            'filters' => array(array(array('RECORDNO', '=', $pDim['grp']))),
                        );

                        $res = $grpMgr->GetList($filter);
                        $pDim['grp'] = $res[0]['ID'];
                    } else {

                        $listDef = Pt_ListDefManager::getById($pDim['grp']);
                        if ($listDef) {
                            $pDim['grp'] = $listDef->getViewName();
                        }

                    }
                }

                $values['POSTDIMGRID'][$sno]['POSTDIMENSIONACCT'] = $pDim['acct'];
                $values['POSTDIMGRID'][$sno]['POSTDIMENSIONGRP'] = $pDim['grp'];
                $values['POSTDIMGRID'][$sno]['POSTDIMENSIONDESC'] = $pDim['desc'];
                $values['POSTDIMGRID'][$sno]['POSTDIMENSIONLOC'] = $pDim['loc'];
                $sno++;
            }
        }
    }

    /**
     * @param array $values set the posting dimensions
     *
     * @return bool
     */
    private function setPostDimensions(&$values)
    {
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;

        $postDims = array();
        $ok = true;
        $sno = 0;
        $statacctMgr = $gManagerFactory->getManager('stataccount');
        $locMgr = $gManagerFactory->getManager('location');
        $statAccs = array();

        $dims = IADimensions::getActiveGLDimensions(!util_isPlatformDisabled());

        foreach ($values['POSTDIMGRID'] as $pDim) {

            if ($pDim['POSTDIMENSIONACCT'] && $pDim['POSTDIMENSIONNAME']) {

                list($acctNo) = explode('--', $pDim['POSTDIMENSIONACCT']);
                list($activeGrp) = explode('--', $pDim['POSTDIMENSIONGRP']);
                list($postLoc) = explode('--', $pDim['POSTDIMENSIONLOC']);
                $entity = $pDim['POSTDIMENSIONNAME'];

                // allow only dimensions which are enabled
                if (!isset($dims[$entity])) {

                    $msg = I18N::getSingleToken('IA.THE_DIMENSION_IS_NOT_ENABLED_IN_THIS_COMPANY', [
                        [ "name" => 'ENTITY', "value" => $entity ],
                    ]);

                    $corr = I18N::getSingleToken('IA.FIRST_ENABLE_IN_THE_DIMENSION_SETTINGS_SECTION', [
                        [ "name" => 'ENTITY', "value" => $entity ],
                    ]);
                    $gErr->addIAError('GL-5034', __FILE__ . ':' . __LINE__,
                        $msg, ['ENTITY' => isl_ucfirst($entity)],
                        '', [],
                        $corr, ['ENTITY' => isl_ucfirst($entity)]
                    );
                    $ok = false;
                }

                $filter = array(
                    'selects' => array('RECORDNO'),
                    'filters' => array(array(array('ACCOUNTNO', '=', $acctNo))),
                );

                $res = $statacctMgr->GetList($filter);

                if (!isset($res[0]['RECORDNO'])) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $msg = "Invalid account '" . $acctNo . "' selected for posting '" . $dim['fullname'] . "''";
                    $corr = "Pick a valid account.";
                    $gErr->addIAError('GL-5035', __FILE__ . ':' . __LINE__,
                        $msg, ['ACCTNO' =>$acctNo ,'FULLNAME' => $dim['fullname']],
                        '', [],
                        $corr, []
                    );

                    $ok = false;
                }
                $acctNo = $res[0]['RECORDNO'];
                $statAccs[] = $acctNo;

                if ($postLoc) {

                    if ($entity != 'location' && $entity != 'employee') {
                        $filter = array(
                            'selects' => array('RECORDNO'),
                            'filters' => array(
                                array(
                                    array('LOCATIONID', '=', $postLoc), array('STATUS', '=', 'active')
                                )
                            ),
                        );

                        $res = $locMgr->GetList($filter);

                        if (!isset($res[0]['RECORDNO'])) {
                            $msg = "Invalid location " . $postLoc . " selected";
                            $corr = "Pick a valid active location";
                            $gErr->addIAError('GL-5036', __FILE__ . ':' . __LINE__, $msg, ['POSTLOC' => $postLoc], '', [], $corr, []);
                            $ok = false;
                        }
                        $postLoc = $res[0]['RECORDNO'];
                    } else {
                        $msg = I18N::getSingleToken('IA.POSTING_LOCATION_IS_NOT_ALLOWED_FOR_DIMENSION', [
                           [ "name" => 'ENTITY', "value" => $entity ],
                        ]);
                        $corr = I18N::getSingleToken('IA.REMOVE_POSTING_LOCATION');
                        $gErr->addIAError('GL-5037', __FILE__ . ':' . __LINE__,
                            $msg, ['ENTITY' => I18N::getSingleToken(isl_strtoupper($entity))], '', [], $corr, []
                        );
                        $ok = false;
                    }

                } else if (IsMultiEntityCompany() && $entity != 'location' && $entity != 'employee') {
                    $msg =I18N::getSingleToken('IA.LOCATION_IS_REQUIRED_FOR_STATISTICAL_POSTING');
                    $corr = I18N::getSingleToken('IA.PICK_A_VALID_ACTIVE_LOCATION_1');
                    $gErr->addError('GL-5038', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                    $ok = false;
                }

                if (!$pDim['POSTDIMENSIONDESC']) {
                    $msg = " Posting description is required for statistical posting";
                    $corr = "Enter a descrption ";
                    $gErr->addError('GL-5001', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                    $ok = false;
                }

                if ($activeGrp) {

                    if (!stristr($entity, "custdim_")) {

                        $grpMgr = DimensionGroupManager::getDimensionGroupManager($entity);
                        $filter = array(
                            'selects' => array('RECORDNO'),
                            'filters' => array(array(array('ID', '=', $activeGrp))),
                        );

                        $res = $grpMgr->GetList($filter);

                    } else {

                        $listDef = Pt_ListDefManager::getByViewName($activeGrp);
                        if ($listDef) {
                            $res[0]['RECORDNO'] = $listDef->getId();
                        }
                    }

                    if (!isset($res[0]['RECORDNO'])) {
                        $msg = "Invalid Group '" . $activeGrp . "'' selected";
                        $corr = "Pick a valid group";
                        $gErr->addIAError('GL-5031', __FILE__ . ':' . __LINE__, $msg, ['ACTIVE_GRP' =>  $activeGrp], $corr, []);
                        $ok = false;
                    }

                    $activeGrp = $res[0]['RECORDNO'];

                } else {
                    $msg = " Group is required for statistical posting";
                    $corr = "Pick a valid group";
                    $gErr->addError('GL-5002', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                    $ok = false;
                }

                $postDims[] = array(
                    'sno' => ++$sno,
                    'entity' => $entity,
                    'acct' => $acctNo,
                    'grp' => $activeGrp,
                    'desc' => $pDim['POSTDIMENSIONDESC'],
                    'loc' => $postLoc,
                );
            }
        }

        if (count($statAccs) != count(array_unique($statAccs))) {
            $msg = "Same account number cannot be given for more than one rule";
            $corr = "Pick an unique account number for each rule.";
            $gErr->addError('GL-5003', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }

        $values['AUTOSTATPOST'] = '';
        if (count($postDims)) {

            if ($values['AUTOSTATPOSTJRNL']) {
                list($journal) = explode('--', $values['AUTOSTATPOSTJRNL']);
                $values['AUTOSTATPOSTJRNL'] = $journal;
            }

            $values['AUTOSTATPOST'] = Util_DataRecordFormatter::phpToJson($postDims);
        }

        if ($values['AUTOSTATPOST'] && !$values['AUTOSTATPOSTJRNL']) {
            $msg = "Journal for 'Dimension Posting' cannot be empty";
            $corr = "Pick a valid journal.";
            $gErr->addError('GL-5004', __FILE__ . ':' . __LINE__, $msg, '', $corr);

            $ok = false;
        }

        unset($values['POSTDIMGRID']);

        return $ok;

    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $ok = parent::regularAdd($values);
        $ok = $ok && $this->migrateApprovals(); // update basejournal

        return $ok;
    }

    /**
     * Override parent's method
     * Configure the module
     *
     * @param array &$values values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $source = "GLSetupManager::Set";

        $inEntity = getMEContextLocation() !== false;
        $journalsBeforeSet = $this->getCurrentApprovalJournals(); //remember the state of Approvals before transaction start
        $ok = SpendControlHandler::validateVisibleDimensions($values,SpendControlHandler::SC_MODULE_GL);

        if($values['ENABLE_GLMATCHING'] == 'false'
           && GLMatchingManager::isEnabled() && GLMatchingManager::hasGLMatchingEnabledAccounts()
        ){
            $msg = "Cannot disable Lettrage as there are accounts with lettrage enabled.";
            $corr = "Disable Lettrage for all accounts before disabling it.";
            Globals::$g->gErr->addError('GL-7079', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            return false;
        }

        // Validate default matching sequence
        if(!$inEntity){
            if ( $values['ENABLE_GLMATCHING'] == 'true' ) {
                if ( ! isNullOrBlank($values['DEFAULT_GLMATCHING_SEQ']) ) {
                    $seqNumMgr = Globals::$g->gManagerFactory->getManager('seqnum');
                    $seqNumRecordNo = $seqNumMgr->GetRecordNoFromVid($values['DEFAULT_GLMATCHING_SEQ']);
                    if ( ! empty($seqNumRecordNo) ) {
                        $values['DEFAULT_GLMATCHING_SEQ'] = $seqNumRecordNo;
                    } else {
                        $msg = "Invalid default matching sequence selected.";
                        $corr = "Pick a valid default matching sequence.";
                        Globals::$g->gErr->addError('GL-7060', __FILE__ . ':' . __LINE__, $msg, '', $corr);

                        return false;
                    }
                } else {
                    $msg = "Default matching sequence is not selected.";
                    $corr = "Ensure that a default matching sequence is selected.";
                    Globals::$g->gErr->addError('GL-7060', __FILE__ . ':' . __LINE__, $msg, '', $corr);

                    return false;
                }
            } else {
                $values['DEFAULT_GLMATCHING_SEQ'] = ''; // if lettrage is not enabled, then reset the default matching sequence
            }
        }

        if( $values['ENABLE_REGULARIZATION_ACCOUNT'] == 'true' && !empty($values['REGULARIZATION_ACCT_RECLASSIFICATION_JRN']) ){
            $journalMgr = Globals::$g->gManagerFactory->getManager('journal');
            $journalBook = $journalMgr->GetBaseJournalBookID($values['REGULARIZATION_ACCT_RECLASSIFICATION_JRN']);

            if ($journalBook != 'A' && $journalBook != 'G') {
                $msg = "Invalid journal selected for regularization account reclassification journal.";
                $corr = "Only Accrual or GAAP journals can be selected for regularization account reclassification journal.";
                Globals::$g->gErr->addError('GL-7094', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                return false;
            }
        }
        
        if($values['ENABLE_REGULARIZATION_ACCOUNT'] == 'false'
            && ( GetPreferenceForProperty(Globals::$g->kGLid, 'ENABLE_REGULARIZATION_ACCOUNT') == 'T' )
        ){
            if(GLAccountManager::hasGLDynamicEnabledAccounts()){
                $msg = "Cannot disable regularization account as there are accounts with regularization accounts enabled.";
                $corr = "Disable regularization account for all accounts before disabling it.";
                Globals::$g->gErr->addError('GL-7090', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                return false;
            }else{
                // Disable Regularization Account Reclassification with all its configurations
                $values['ENABLE_REGULARIZATION_ACCOUNT'] = 'false';
                $values['REGULARIZATION_ACCT_RECLASSIFICATION_JRN'] = '';
            }
           
        }
        
        if($values['COMPANYBOOK'] == 'Cash' 
            && ( GetPreferenceForProperty(Globals::$g->kGLid, 'ENABLE_REGULARIZATION_ACCOUNT') == 'T' )){
            $values['ENABLE_REGULARIZATION_ACCOUNT'] = 'false';
            $values['REGULARIZATION_ACCT_RECLASSIFICATION_JRN'] = '';
        }

        if (!$inEntity && $ok) {
            $ok = $ok && $this->validateInput($values);
        }

        $ok = $ok && $this->validateAndTranslateToRecordNo($values);
        $ok = $ok && $this->validateApprovalPreferences($values, $inEntity);
        $GlApproverEmailConfigMgr = Globals::$g->gManagerFactory->getManager(
            'glapproveremailconfig'
        );
        $ok = $ok && $GlApproverEmailConfigMgr->validateApprovalEmailNotification($values);

        if (!$ok) {
            return false;
        }

        if (!util_isPlatformDisabled()) {
            // This validation prevents 'DIIMENSIONSORDER' to be overwritten, since it also gets set 
            // from custom object definition screen when enabling custom dimensions
            if (!$this->validateCustomDimensions()) {
                return false;
            }

            // Unset 'CUSTDIM_10006DIMENSION' and 'BSBCUSTDIM_10006' values
            // 1. Values are stored on platform side 
            // 2. If stored, they showup in <get_companyprefs application="GL"/> call
            $custdims = IADimensions::getCustomDimensionObjects();
            foreach ($custdims as $custdim) {
                $componentval = $custdim['componentval'];
                unset($values['BSB' . $componentval]);
                unset($values[$componentval . 'DIMENSION']);
            }

            // Collection UDD auto-fill overridden sequence i.e. value for DIMENSION_AUTOFILL_SEQUENCESTORED
            $this->collectUDDAutofillOverrideSequence($values);

            $dimensionFiltingEnabled = $values['ENABLEDIMENSIONFILTERING'] == 'true';

            // Ensure both are in sync
            if ($dimensionFiltingEnabled !== self::isDimensionFilteringEnabled()) {

                // INVALIDATE THE COMPANY CACHE, SO THAT IT COULD BE REFRESHED
                $ok = $ok && InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());
            }
        }
        if (!$inEntity) {
            $ok = $ok && $this->setPostDimensions($values);
        }

        if (GetMyIndustryType() !== "NFP") {
            $values['ENABLERESTRICTIONRELEASE'] = 'false';
        }

        if (IsMultiEntityCompany()) {
            $ok = $ok && $this->setAcctSeqNum($values);
        } else {
            $values['ENABLEACCTSEQNUM'] = false;
            $values['ACCTSEQNUMENTITIES'] = '';
            $values['GENERATEACCTSEQNUM'] = 'Default';
        }

        // configureGLAccount issues delete at some stage(SetModulePreferencesByRef), starting transaction from here
        $this->_QM->beginTrx($source);

        if (!$inEntity) {
            $ok = $ok && $this->configureGLAccount($values);
        }

        $currentReportPeriod = GetPreferenceForProperty(Globals::$g->kGLid, 'ENABLE_STATUTORY_REPORTING_PERIOD');

        if ($ok) {
            unset($values['DUMMY']); // remove dummy value, we use it for layout spacing in the xml
            $ok = $ok && $this->deleteGLApproverConfig();
            $ok = $ok && parent::regularSet($values);

            if ($ok) {
                $glapproverconfigManager = $gManagerFactory->getManager('glapproverconfig');

                foreach ($values['JOURNAL_GRID'] as $key => $approver) {
                    $approvalValues[] = $this->prepareApproverListToInsert($approver, $key);
                }
                $ok = $glapproverconfigManager->add($approvalValues);

                  $glConfigMgr = $gManagerFactory->getManager('glodconfig');
                  $ok = $ok && $glConfigMgr->regularAdd($values['GLOD_CONFIG_GRID']);
            }

            $ok = $ok && $GlApproverEmailConfigMgr->processEmailNotificationData($values);

            if ($values['JEAPPROVAL_ENABLE'] == 'false') {
                $ok = $ok && $this->clearExistingApprovalPreferences();
                $ok = $ok && $this->deleteGLApproverConfig(true);
                $ok = $ok && $this->clearBaseJournal();
            } else {
                //see what's new in the transaction
                $journalsAfterSet = $this->getCurrentApprovalJournals();
                $enabledJournals = array_diff($journalsAfterSet, $journalsBeforeSet);
                $disabledJournals = array_diff($journalsBeforeSet, $journalsAfterSet);

                $ok = $ok && $this->updateBaseJournal($enabledJournals, 'T');
                $ok = $ok && $this->updateBaseJournal($disabledJournals, 'F');
            }

            //Reset Statutory lock period for Top and the entities to its default period when we disable it
            if($currentReportPeriod === 'T' && $values['ENABLE_STATUTORY_REPORTING_PERIOD'] == 'false') {
                $srpMgr = $gManagerFactory->getManager('statutoryreportingperiod');
                StatutoryReportingPeriodManager::resetStatutoryLockPeriod();
            }
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $this->_QM->rollbackTrx($source);
        } else {
            if( IsModuleIdInstalled(Globals::$g->kSALESFORCE2id) ) {
                $sfSetupMgr = Globals::$g->gManagerFactory->getManager('salesforcesetup');
                $sfSetupMgr->updateGLDimensions(null, $values);
            }
        }

        return $ok;
    }
    
    /**
     * @param array $values
     *
     */
    private function getAcctSeqNum(&$values)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $locMgr = $gManagerFactory->getManager('locationentity');
        $acctSeqNumEntities = array();
        
        if ($values['ENABLEACCTSEQNUM'] && isset($values['ACCTSEQNUMENTITIES'])) {
            $acctSeqNumRecNos = explode('#~#', $values['ACCTSEQNUMENTITIES']);
            $filter = array(
                'selects' => array('LOCATIONID', 'NAME'),
                'filters' => array(array(array('RECORDNO', 'IN', $acctSeqNumRecNos))),
            );

            $res = $locMgr->GetList($filter);

            foreach ($res as $r) {
                $acctSeqNumEntities[] = $r['LOCATIONID'] . '--' . $r['NAME'];
            }
            $values['ACCTSEQNUMENTITIES'] = implode('#~#', $acctSeqNumEntities);
        }
        
    }

    /**
     * validateAndTranslateToRecordNo
     *
     * @param array $values
     *
     * @return bool true if Unrealized gain & loss account and journal are valid else false
     */
    private function validateAndTranslateToRecordNo(&$values)
    {
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $gainLossAcct = &$values['UNREALIZED_GAIN_LOSS_ACCT'];
        $gainLossJrn = &$values['UNREALIZED_GAIN_LOSS_JRN'];
        if (!isset($gainLossAcct) && isset($gainLossJrn)) {
            $gErr->addError(
                "GL-5005", __FILE__ . __LINE__,
                "Unrealized gain & loss account is required when Unrealized gain & loss journal selected"
            );
            return false;
        } elseif (isset($gainLossAcct) && !isset($gainLossJrn)) {
            $gErr->addError(
                "GL-5006", __FILE__ . __LINE__,
                "Unrealized gain & loss journal is required when Unrealized gain & loss account selected"
            );
            return false;
        } elseif (!isset($gainLossAcct) && !isset($gainLossJrn)) {
            return true;
        }

        $acctMgr = $gManagerFactory->getManager('glaccount');
        $params = array(
            'selects' => array('RECORDNO'),
            'filters' => array(array(array('ACCOUNTNO', '=', $gainLossAcct))),
        );
        $acctRecs = $acctMgr->GetList($params);
        $gainLossAcct = $acctRecs[0]['RECORDNO'];

        $glJrnMgr = $gManagerFactory->getManager('gljournal');
        $filter = array(
            'selects' => array('RECORDNO'),
            'filters' => array(
                array(
                    array('SYMBOL', '=', $gainLossJrn)
                )
            )
        );
        $jrnRecNo = $glJrnMgr->GetList($filter);
        $gainLossJrn = $jrnRecNo[0]['RECORDNO'];
        return true;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    private function setAcctSeqNum(&$values)
    {
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $locMgr = $gManagerFactory->getManager('locationentity');
        $acctSeqNumRecNos = array();

        $ok = true;
        
        if ($values['ENABLEACCTSEQNUM'] == 'true') {

            // if accounting sequence is enabled for the first time then, delete the orphan batches in the company
            $kGLid = Globals::$g->kGLid;
            $enableAcctSeq = (GetPreferenceForProperty($kGLid, 'ENABLEACCTSEQNUM') == 'T') ? 'true' : 'false';
            if ($enableAcctSeq != $values['ENABLEACCTSEQNUM']) {
                $locs = [];
                $ok = $this->deleteOrphanBatches(GetMyCompany(), $locs);
                if (!$ok) {
                    $msg = "Unable to enable accounting sequence";
                    $corr = "Not able to delete the existing orphan batches. 
                        Please contact Intacct support for further details.";
                    $gErr->addError('GL-5007', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                    $ok = false;
                }
            }

            $acctSeqNumEntities = array();
            if (!empty($values['ACCTSEQNUMENTITIES'])) {
                $acctSeqNumEntities = explode('#~#', $values['ACCTSEQNUMENTITIES']);
            }
            foreach ($acctSeqNumEntities as $acctSeqNumEntity) {
                $locationID = explode('--', $acctSeqNumEntity);
                $filter = array(
                    'selects' => array('RECORDNO'),
                    'filters' => array(array(array('LOCATIONID', '=', $locationID[0]))),
                );

                $res = $locMgr->GetList($filter);
                
                if (empty($res[0]['RECORDNO'])) {
                    $msg = "Invalid entity '" . $locationID[0] . "' selected for accounting sequence numbering";
                    $corr = "Pick a valid entity.";
                    $gErr->addIAError('GL-5039', __FILE__ . ':' . __LINE__, $msg, ['LOCATIONID' => $locationID[0]], '', [], $corr, []);
                    $ok = false;
                } else {
                    $acctSeqNumRecNos[] = $res[0]['RECORDNO'];
                }
            }
            $values['ACCTSEQNUMENTITIES'] = implode('#~#', $acctSeqNumRecNos);

            // Delete orphan batches for newly added entities
            $acctSeqEntitiesMap = GetPreferenceForProperty($kGLid, 'ACCTSEQNUMENTITIES');
            $acctSeqEntities = explode('#~#', $acctSeqEntitiesMap);
            $newEntities = array_diff($acctSeqNumRecNos, $acctSeqEntities);

            if ($newEntities) {
                $ok = $ok && $this->deleteOrphanBatches(GetMyCompany(), $newEntities);
            }

        } else {
            $values['ACCTSEQNUMENTITIES'] = '';
            $values['GENERATEACCTSEQNUM'] = 'Default';
        }
        
        return $ok;
    }

    /**
     * Delete the orphan batches in the company
     *
     * @param int $cny
     * @param int[] $locs
     *
     * @return bool
     */
    private function deleteOrphanBatches($cny, $locs)
    {
        $query
            = "DELETE
               FROM
                    glbatch g
               WHERE
                    journalseqno IS NULL
               AND NOT EXISTS (
                        SELECT
                            1
                        FROM
                            glentry ge
                        WHERE
                            ge.cny# = g.cny#
                            AND ge.batch# = g.record#
                            AND ge.cny# = :1
                )
                AND NOT EXISTS (
                        SELECT
                            1
                        FROM
                            contractresolve cr
                        WHERE
                            cr.cny# = g.cny#
                            AND cr.glbatchkey = g.record#
                            AND cr.cny# = :1
                ) 
                AND g.cny# = :1";

        if ($locs) {
            $query = PrepINClauseStmt($query, $locs, " and g.locationkey ");
        }

        $ok = ExecStmt(array($query, $cny));

        $query = "UPDATE icbatch ic
                  SET
                    glbatchkey = NULL
                  WHERE
                    ic.cny# = :1
                  AND NOT EXISTS (
                        SELECT
                            1
                        FROM
                            glbatch g
                        WHERE
                            g.cny# = ic.cny#
                            AND g.cny# = :1
                            AND g.record# = ic.glbatchkey
                   )";

        $ok = $ok &&  ExecStmt(array($query, $cny));

        return $ok;

    }

    /**
     * Override parent's method
     * Set a single preference
     *
     * @param string $property  path
     * @param string $value     value
     * @param bool   $predelete delete first ?
     * @param string $mod       module
     * @param string $ins_updt  insert
     * @param bool   $forceRoot    if true will always write pref to the root rather than the current entity
     *
     * @return bool false if error
     */
    public function SetPreference($property, $value, $predelete = true, $mod = '', $ins_updt = '', $forceRoot=false)
    {
        if ($property == "COMPANYBOOK") {
            $property = "MULTIBOOKS";
            if ($value != 'Accrual and cash') {
                $value = 'F';
            } else {
                $value = 'T';
            }
        }
        return parent::SetPreference($property, $value, $predelete, $mod, $ins_updt, $forceRoot);
    }

    /**
     * Validate custom dimensions
     *
     * @return bool false if error
     */
    protected function validateCustomDimensions()
    {
        $gErr = Globals::$g->gErr;

        $glDimDefIds = Pt_DataObjectManager::getAllGLDimensionIds();
        //epp('$glDimDefIds:');eppp($glDimDefIds);

        if (count($glDimDefIds) > 0) {

            // Validate if custom dimension data is not stale
            $dimkeys = self::getCustomDimensionKeyOrder();
            //epp('$dimkeys:');eppp($dimkeys);

            $nonEmptyDimkeys = array();
            foreach ($dimkeys as $dim) {
                if ($dim != "") {
                    $nonEmptyDimkeys[] = $dim;
                }
            }
            //epp('$nonEmptyDimkeys:');eppp($nonEmptyDimkeys);

            if (count($nonEmptyDimkeys) != count($glDimDefIds)) {
                $gErr->addError(
                    "GL-5008", __FILE__ . __LINE__,
                    "GL Dimensions have changed. Please refresh the screen and try again."
                );
                return false;
            }

            // If all Ids match
            $diff1 = array_diff($nonEmptyDimkeys, $glDimDefIds);
            $diff2 = array_diff($glDimDefIds, $nonEmptyDimkeys);

            if (count($diff1) > 0 || count($diff2) > 0) {
                $gErr->addError(
                    "GL-5008", __FILE__ . __LINE__,
                    "GL Dimensions have changed. Please refresh the screen and try again."
                );
                return false;
            }
        }

        return true;
    }

    /**
     * Override parent's method
     *
     * @param bool $merged
     *
     * @return array
     */
    public static function proxy_getDimensionPreferencesInfo($merged = false)
    {
        $allDims = IADimensions::getAllDimensions(!util_isPlatformDisabled());
        $dimObjInfo = array();

        foreach ($allDims as $dimid => $dim) {
            $dimObjInfo[$dimid] = $allDims[$dimid]; // $dim;
            $dimObjInfo[$dimid]['dimfieldid'] = $dim['dimensionid'];
            $dimObjInfo[$dimid]['path'] = isl_strtoupper($dim['dimensionid']); // strtoupper($dim['dimensionid']);
            $dimObjInfo[$dimid]['type'] = array(
                'ptype' => 'boolean',
                'type' => 'boolean',
                'validvalues' => array('T', 'F'),
                '_validivalues' => array('T', 'F'),
            );
        }

        return $dimObjInfo;
    }

    /**
     * Validate the data entered
     *
     * @param array &$values the data
     *
     * @return bool false if error else true
     */
    private function validateInput(&$values)
    {
        $gErr = Globals::$g->gErr;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $err = GetModulePreferences(Globals::$g->kGLid, $preferences);
        $gManagerFactory = Globals::$g->gManagerFactory;

        $ok = true;

        if (!$values['BSBL'] && IsMultiEntityCompany()) {
            $gErr->addError(
                "GL-5009", __FILE__ . __LINE__,
                "Balance Sheet by Location is required when Multi-Entity is subscribed"
            );
            $ok = false;
        }
        //adding a validation for safety if user bypasses js validation and try to set BSBAFFILIATEENTITY to T
        if (isset($values['BSBAFFILIATEENTITY']) && $values['BSBAFFILIATEENTITY']=='T') {
            $gErr->addError(
                "GL-7056", __FILE__ . __LINE__,
                "Enforced balancing by Affiliate entity is not supported."
            );
            $ok = false;
        }

        // TODO Check if we can disable accounting numbering sequence here

        if ($values['USEALLOCATION'] != 'true') {
            // check again when saving the preferences
            $result = QueryResult(
                array(
                    "SELECT count(1) CNT FROM glentry WHERE cny# = :1 AND allocationkey IS NOT NULL AND ROWNUM=1",
                    GetMyCompany()
                )
            );
            if ($result[0]['CNT'] > 0) {
                $gErr->addError(
                    "GL-5010", GetFL(),
                    'Disabling Allocation Error',
                    "Allocated journal entries found. You cannot turn off allocation."
                );
                $ok = false;
            }

            // check if AP/AR have turned on enabling allocation option
            // we should not allow disable allocation from GL, if subledgers have this option ON

            $apalloc = GetPreferenceForProperty(Globals::$g->kAPid, 'USEALLOCATION');
            $aralloc = GetPreferenceForProperty(Globals::$g->kARid, 'USEALLOCATION');
            if ($apalloc == 'Y' || $aralloc == 'true') {
                if ($apalloc == 'Y' && $aralloc == 'true') {
                    $errMsg = 'Accounts Payable and Accounts Receivable';
                } else if ($apalloc == 'Y') {
                    $errMsg = 'Accounts Payable';
                } else {
                    $errMsg = 'Accounts Receivable';
                }
                $gErr->addIAError(
                    'GL-5011', GetFL(),
                    'You can not disable allocation in general ledger as subledgers may have allocation enabled.', [],
                    "Please disable allocation in $errMsg before disabling allocation in general ledger.", ['ERR_MSG' => $errMsg]
                );
                $ok = false;
            }
        }

        if ($values['COMPANYBOOK'] == 'Accrual and cash' && $preferences['MULTIBOOKS'] != 'T') {
            $enddate = GetCurrentDate();
            $qry = "SELECT 1 found FROM (
                        SELECT max(entry_date) trns_date FROM glentry WHERE cny# = :1
                        UNION ALL 
                        SELECT MAX(whencreated) trns_date FROM prrecord WHERE cny# = :1 
                    )
                    WHERE trns_date > :2 ";

            $resultSet = QueryResult(
                array($qry, GetMyCompany(), $enddate)
            );


            $fileLine = __FILE__ . ':' . __LINE__;
            if ($this->shouldWarn(null, $values, [$fileLine])
                && (isset($resultSet[0]['FOUND']) || $resultSet[0]['FOUND'] != '')
            ) {
                $gErr->AddWarning(
                    "The change in accounting method is effective immediately, however, you have future-dated transactions which were accounted for under your current method. "
                    ."To ensure proper treatment, either remove these entries first then re-input after the accounting method change or continue with the change and directly record any additional entries related for accurate reporting with the new method."
                );
                $ok = false;
            }
        }

        //Validate if delegation is enabled and no delegates are assigned
        if ($values['JEAPPROVAL_ENABLE'] == 'true' && $values['ENABLEDELEGATION'] == 'true') {
            $delegateMngr = Globals::$g->gManagerFactory->getManager('glapprovaldelegate');
            $delegateFilter = array(
                'filters' => array(array(
                    array('CNY#', '=', GetMyCompany()),
                ))
            );
            $gldelegates = $delegateMngr->GetList($delegateFilter);
            if (count($gldelegates) < 1 || (empty($gldelegates[0]['DEFAULT_DELEGATEKEY']) && empty($gldelegates[0]['DEFAULT_DELEGATE']))) {
                $ok = false;
                $gErr->addError(
                    'GL-5012', GetFL(),
                    'Can not enable delegation with out configuring default delegate',
                    "You must first configure default-delegate in manage delegate page"
                );
            }
        }

        // lets make sure the GL accounts are not tied to any bank accounts for autobalance plug account
        if ($values['ENABLE_AUTOBALANCE'] == 'true') {
            if ($values['AUTOBALANCE_ACCOUNT'] != '') {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $isBankAcct = false;
                list($acctNo) = explode('--', $values['AUTOBALANCE_ACCOUNT']);

                $acctMgr = $gManagerFactory->getManager('glaccount');
                $acctsMap = $acctMgr->GetBankAccountsMap(array($acctNo));

                // If the GL account is associated to a bank account let's throw an error
                if ($acctsMap[$acctNo]['ISCASHACCT'] == 'T') {
                    $ok = false;
                    $gErr->addIAError('GL-5040', __FILE__ . ':' . __LINE__,
                        "The GL Account no. '$acctNo' is associated to the Bank Account '". $acctsMap[$acctNo]['CASHACCTID'] . "'
                        and cannot be used as an Auto Balancing GL Account.", ['ACCTNO' => $acctNo, 'ACCTSMAP' => $acctsMap[$acctNo]['CASHACCTID']],
                        '', [],
                        "Please select another GL account.", []
                    );
                }
                if (!isset($acctsMap[$acctNo]['RECORD#'])) {
                    $msg = "Invalid Account " . $acctNo . " selected";
                    $corr = "Pick a valid account.";
                    $gErr->addIAError('GL-5032', __FILE__ . ':' . __LINE__, $msg, ['ACCT_NO' =>  $acctNo], $corr, []);
                    $ok = false;
                }
            } else {
                $msg = "Auto balancing GL Account cannot be empty.";
                $corr = "Pick a valid account.";
                $gErr->addError('GL-5013', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                $ok = false;
            }
        }

        if ($values['ENABLE_AUTOBALANCE'] == 'true') {

            $autobalanceShouldBeDisabled = true;
            foreach ($values as $key => $value) {
                if (strpos($key, 'BSB') !== false && $value == 'T') {
                    $autobalanceShouldBeDisabled = false;
                }
            }

            if ($autobalanceShouldBeDisabled) {
                $msg = "Cannot enable auto balance without any balance enabled.";
                $corr = "Enable at least one JE balancing.";
                $gErr->addError('GL-5014', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                $ok = false;
            }
        }

        // lets make sure the GL accounts are not tied to any bank accounts for rounding plug account
        if ($values['ENABLEMULTICURRENCY'] == 'true') {
            if ($values['ROUNDING_ACCOUNT'] != '') {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $isBankAcct = false;
                list($acctNo) = explode('--', $values['ROUNDING_ACCOUNT']);

                $acctMgr = $gManagerFactory->getManager('glaccount');
                $acctsMap = $acctMgr->GetBankAccountsMap(array($acctNo));

                // If the GL account is associated to a bank account let's throw an error
                if ($acctsMap[$acctNo]['ISCASHACCT'] == 'T') {
                    $ok = false;
                    $gErr->addIAError(
                        'GL-5041', __FILE__ . ':' . __LINE__,
                        "The GL Account no. '$acctNo' is associated to the Bank Account '"
                        . $acctsMap[$acctNo]['CASHACCTID'] . "' and cannot be used as an Auto Balancing GL Account.",
                        ['ACCTNO' => $acctNo, 'ACCTSMAP' => $acctsMap[$acctNo]['CASHACCTID']],
                        '', [],
                        "Please select another GL account.", []
                    );
                }
                if (!isset($acctsMap[$acctNo]['RECORD#'])) {
                    $msg = "Invalid Rounding account " . $acctNo . " selected";
                    $corr = "Pick a valid account.";
                    $gErr->addIAError('GL-5033', __FILE__ . ':' . __LINE__, $msg, ['ACCT_NO' => $acctNo], $corr, []);
                    $ok = false;
                }
            }
            $values['INTACCTRATETYPE'] = '';
            $values['OANDAAPIKEY'] = '';

            /*
             if ( $ok && $intacctRateType == IntacctRate::OANDA) {
                if (empty($values['OANDAAPIKEY'])) {
                    $msg = "Oanda API Key is required";
                    $corr = "Enter valid Oanda API key for unlimited access";
                    $gErr->addError('GL-5016', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                    $ok = false;
                } else if ($preferences['OANDAAPIKEY'] != $values['OANDAAPIKEY']) {

                    $oanda = Oanda::testOandaCall($values['OANDAAPIKEY']);
                    if (!$oanda) {
                        $msg = "Invalid Oanda API Key provided";
                        $corr = "Enter valid Oanda API key for unlimited access";
                        $gErr->addError('GL-5017', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                        $ok = false;
                    } else {
                        $values['OANDAAPIKEY'] = TwoWayEncrypt($values['OANDAAPIKEY']);
                    }
                }
            }*/
        } else {
            $values['INTACCTRATETYPE'] = '';
            $values['OANDAAPIKEY'] = '';
        }

        //validate against ENABLEMULTICURRENCY when we try disable it and also handle INTACCTRATEOPTION changes
        $existingValues = $this->get('');
        if ( ( $existingValues['ENABLEMULTICURRENCY'] == 'true'
               && $existingValues['ENABLEMULTICURRENCY'] != $values['ENABLEMULTICURRENCY'] )
             || ( ( ! empty($existingValues['INTACCTRATEOPTION']) )
                  && $existingValues['INTACCTRATEOPTION'] != $values['INTACCTRATEOPTION'] ) ) {
            $multiCurrencyTranscnt = QueryResult(
                [
                    "select 1 as exist from dual where 
                              exists( select 1 from glentrymst where cny# = :1 and nvl(currency, 'USD') != nvl(basecurr, 'USD') and rownum = 1) 
                           or exists ( select 1 from prentrymst where cny# = :1 and nvl(currency, 'USD') != nvl(basecurr, 'USD') and rownum = 1)",
                    GetMyCompany(),
                ]
            );
            $multiCurrencyTranscnt = ! empty($multiCurrencyTranscnt) && $multiCurrencyTranscnt[0]['EXIST'];

            if ( $multiCurrencyTranscnt ) {
                $msg = " There are multi currency transactions found in this company.";
                $corr =
                    "You can't disable multi-currency if you enter transactions after it's turned on (or) You cannot change Intacct rate option";
                $gErr->addError('GL-5015', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                $ok = false;
            }
        }

        $dimInfo = IADimensions::getAllDimensions(!util_isPlatformDisabled());
        foreach ( $dimInfo as $dim) {
            $dimID = strtoupper($dim['dimensionid']);
            if ($dim['standard'] && $dimID == 'WAREHOUSEDIMENSION' && $values[$dimID] == 'T' && !IsInstalled(Globals::$g->kINVid)) {
                $msg = "In order to enable the Warehouse dimension, you must be subscribed to Inventory Control.";
                $gErr->addError('GL-5018', __FILE__ . ':' . __LINE__, $msg, '');
                $ok = false;
            }
            if ($dim['standard'] && $dimID == 'CONTRACTDIMENSION' && $values[$dimID] == 'T' && !(IsInstalled(Globals::$g->kCNid) || ContractUtil::isInstalledBasicContract())) {
                $msg = "In order to enable the Contract dimension, you must be subscribed to Contracts.";
                $gErr->addError('GL-5019', __FILE__ . ':' . __LINE__, $msg, '');
                $ok = false;
            }
            if ($dim['standard'] && $dimID == 'AFFILIATEENTITYDIMENSION' && $values[$dimID] == 'T') {
                $ME_isAffiliateEntityDimEnabled = MESetupManager::isAffiliateEntityDimensionEnabled();
                if (!$ME_isAffiliateEntityDimEnabled) {
                    $msg = "In order to enable the Affiliate entity dimension, you must enable it from Multi-entity configuration.";
                    $gErr->addError('GL-7061', __FILE__ . ':' . __LINE__, $msg, '');
                    $ok = false;
                }
            }
        }

        $hiddenDims = array('TASKDIMENSION-NOHIDE');
        $dimInfo = IADimensions::getAllDimensions(!util_isPlatformDisabled());
        foreach ( $dimInfo as $dim) {
            $name = strtoupper($dim['label']) . 'DIMENSION';
            if ($dim['standard'] && in_array($name, $hiddenDims) && $values[$name] == 'T') {
                $msg = "Cannot enable '" . $dim['label'] . "' dimension in this company.";
                $gErr->addIAError('GL-5042', __FILE__ . ':' . __LINE__, $msg, ['LABEL' => $dim['label']], '', []);
                $ok = false;
            }
        }

        // Disallow changing accounting type to non-Accrual under certain conditions:
        // - company is subscribed to Construction
        // - AP or AR retainage is active/enabled
        if ( $values['COMPANYBOOK'] != 'Accrual' ) {
            if ( CRESetupManager::isCREInstalled() ) {
                $gErr->addError('GL-5043', __FILE__ . ':' . __LINE__,
                                'Cannot change accounting type when Construction is enabled'
                );
                $ok = false;
            } elseif ( CRESetupManager::isARRetainageEnabled() || CRESetupManager::isAPRetainageEnabled() ) {
                $gErr->addError('GL-5056', __FILE__ . ':' . __LINE__,
                                'Cannot change accounting type when Retainage is enabled'
                );
                $ok = false;
            }
        }

        // Taxes does not support Cash basis accounting yet
        if( TaxSetupManager::isTaxModuleConfigured() && $values['COMPANYBOOK'] == 'Cash' )
        {
            $gErr->addError(
                    'GL-5044', GetFL(),
                    "The accounting method cannot be changed",
                    "Cash book is not allowed since company is subscribed to Taxes."
            );
            $ok = false;
        }

        if ( IsInstalled(Globals::$g->kCNid) ) {
            // Contract module is subscribed.
            // Company should not be allowed to change its book to Cash or Cash and Accrual book.
            if ( $values['COMPANYBOOK'] == 'Cash' ) {
                // Cash book.
                $ok = false;
                $erMsg = "Cash book is not allowed as company is subscribed to Contract module.";
                $gErr->addError("GL-5045", __FILE__ . ":" . __LINE__, $erMsg);
            } else if ( $values['COMPANYBOOK'] == 'Accrual and cash' ) {
                // Cash and Accrual book.
                $ok = false;
                $erMsg = "Cash and Accrual book is not allowed as company is subscribed to Contract module.";
                $gErr->addError("GL-5046", __FILE__ . ":" . __LINE__, $erMsg);
            }
        }

        return $ok;
    }

    /**
     * Configure the data entered
     *
     * @param array $values the data
     *
     * @return bool false if error else true
     */
    private function configureGLAccount(&$values)
    {
        $gErr = Globals::$g->gErr;

        $postingPref = GetValueForIACFGProperty('ALLOW_GLPOSTINGCONFIGS');
        $postingCny = explode(',', $postingPref);
        /** @noinspection PhpUnusedLocalVariableInspection */
        $allowPostingConfigs = in_array(GetMyCompany(), $postingCny);
        GetModulePreferences(Globals::$g->kGLid, $preferences);
        $isMultibooks = $values['COMPANYBOOK'] == 'Accrual and cash' ? true : false;
        $ok = true;
        $oldValues = $this->get('');
        // if MCP is disabled in GL, disable in rest of the modules
        if ($values['ENABLEMULTICURRENCY'] == 'false'
            && $values['ENABLEMULTICURRENCY'] != $oldValues['ENABLEMULTICURRENCY']
        ) {
            $mcpModules = array(
                Globals::$g->kAPid,
                Globals::$g->kARid,
                Globals::$g->kSOid,
                Globals::$g->kSFORCEid,
                Globals::$g->kPOid,
                Globals::$g->kEEid,
                Globals::$g->kCMid
            );

            foreach ($mcpModules as $mcpMod) {
                $isInstalled = IsModuleIdInstalled($mcpMod);

                if ($isInstalled) {
                    GetModulePreferences($mcpMod, $modpreferences);
                    $property = ($mcpMod == Globals::$g->kSFORCEid) ? 'SFORCEISMCP' : 'MCPENTRY';

                    if (isset($modpreferences[$property])) {
                        unset($modpreferences[$property]);
                        $ok = $ok && SetModulePreferences($mcpMod, $modpreferences);
                    }
                }
            }
        }
        //CHECK for all modules not only Projects
        // @see Iris
        if (IsInstalled(Globals::$g->kPAid)) {
            // For PA we need customer, item and employee dimensions
            $reqdims = array('CUSTOMERDIMENSION', 'ITEMDIMENSION', 'EMPLOYEEDIMENSION');
            foreach ($reqdims as $reqdim) {
                if ($values[$reqdim] != 'T') {
                    $gErr->addError(
                        "GL-5029", __FILE__ . __LINE__,
                        "You cannot remove Customer, Item or Employee dimensions when 'Projects' application is subscribed."
                    );
                    /*TODO::i18N-review no need of placeholder*/
                    $ok = false;
                }
            }

            // If we track time against project we can not unselect the project dimension
            $showCustProj = GetPreferenceForProperty(Globals::$g->kPAid, 'TSSHOWCUSTPROJ');
            if ($showCustProj != 'CI' && $values['PROJECTDIMENSION'] != 'T') {
                $gErr->addError(
                    "GL-5030", __FILE__ . __LINE__,
                    "You can not remove the project dimension when you are tracking time by projects in the 'Projects' application."
                );
                $ok = false;
            }
        }

        if (IsInstalled(Globals::$g->kCNid)) {
            // For Contract we need contract dimension
            if ($values['CONTRACTDIMENSION'] != 'T') {
                $gErr->addError(
                    "GL-5020", __FILE__ . __LINE__,
                    "Enable the Contract dimension in General Ledger configuration."
                );
                $ok = false;
            }
        }

        $ME_isAffiliateEntityDimEnabled = MESetupManager::isAffiliateEntityDimensionEnabled();
        if ($ME_isAffiliateEntityDimEnabled) {
            // Affiliate entity dimension can be removed from GL if it enabled in ME setup
            if ($values['AFFILIATEENTITYDIMENSION'] != 'T') {
                $gErr->addError(
                    "GL-7062", __FILE__ . __LINE__,
                    "You cannot remove Affiliate entity dimension when it is enabled in Multi-Entity configuration."
                );
                $ok = false;
            }
        }

        if (IsInstalled(Globals::$g->kINVid) &&  GetPreferenceForProperty(Globals::$g->kINVid, 'ENABLESUPPLIESINVENTORY') ==='T' ) {
           // For Supplies Inventory we need employee dimension
            if ($values['EMPLOYEEDIMENSION'] != 'T') {
                $gErr->addError(
                    "GL-7068", __FILE__ . __LINE__,
                    "The Employee dimension is required for Supplies Inventory."
                );
                $ok = false;
            }
            // For Supplies Inventory we need employee dimension
            if ($values['VENDORDIMENSION'] != 'T') {
                $gErr->addError(
                    "GL-7069", __FILE__ . __LINE__,
                    "The Vendor dimension is required for Supplies Inventory."
                );
                $ok = false;
            }
        }

        // IF SUBSCRIBED TO MULTIPLE BOOKS REPORTING THEN SET THE PREFERENCES
        if ($values['COMPANYBOOK'] == 'Accrual and cash') {
            if ($preferences['MULTIBOOKS'] != 'T') {
                $ok = $ok && SetMultiBookPreferences($values, $preferences);
            }
        } else if ($preferences['MULTIBOOKS'] == 'T') {
            // IF USER HAD ALREADY SUBSCRIBED TO MULTIBOOKS AND NOW HES UNSUBSRIBING IT THEN.,
            $ok = $ok && UnSubscribeMultiBook($values, $preferences);
        }

        if (isset($values['ENABLEGAAP']) && $values['ENABLEGAAP'] == 'true') {
            $ok = $ok && AddGAAPBooks($values, $preferences, $isMultibooks);
        } else {
            $ok = $ok && UnSubscribeGAAP($values, $preferences);
        }

        if (isset($values['ENABLETAX']) && $values['ENABLETAX'] == 'true') {
            $ok = $ok && AddTAXBooks($values, $preferences, $isMultibooks);
        } else {
            $ok = $ok && UnSubscribeTAX($values, $preferences);
        }

        if ($values['ENABLEUSERBOOK'] != 'true') {
            $ok = $ok && UnSubscribeUserDefinedBooks($values, $preferences);
        }

        $ok = $ok && $this->checkTaskAndCostType($values, $oldValues, $gErr);

        if ($ok == true) {
            //START Modification Multibooks

            //check if there is Transactions in the company
            //Updated the query because the old one was taking long time to execute
            $bookCount = QueryResult(
                array(
                    "SELECT
                        COUNT(1) count
                    FROM
                        (
                            SELECT 1 cnt FROM dual WHERE EXISTS ( SELECT 1 FROM basegltotals WHERE amount != 0 AND bookid = 'ACCRUAL' AND cny# = :1)
                            UNION ALL
                            SELECT 1 cnt FROM dual WHERE EXISTS ( SELECT 1 FROM basegltotals WHERE amount != 0 AND bookid = 'CASH' AND cny# = :1 )
                        )",
                    GetMyCompany()
                )
            );

            $bookCount = $bookCount[0]['COUNT'];
            $allowUnsubscribe = ($bookCount > 1 ? false : true);
            if ($allowUnsubscribe) {
                $transactionExist = QueryResult(
                    array(
                        "select 1 as exist from dual where exists (select 1 from glentrymst where cny# = :1 and rownum = 1)",
                        GetMyCompany()
                    )
                );
                $transactionExist = !empty($transactionExist) && $transactionExist[0]['EXIST'];
                $multibookError = false;
                $curPref = (IsCompanyAccrual() ? 'Accrual' : 'Cash');

                if ($values['COMPANYBOOK'] != 'Accrual and cash') {
                    $app = explode('@', Globals::$g->_userid)[2] ?? null;
                    if ($app != 'A' && ! $transactionExist ) {
                        $co = GetCompany(Globals::$g->_userid, true);
                        $co['ACCRUAL'] = ($values['COMPANYBOOK'] == 'Accrual' ? 'T' : 'F');
                        $values['DEFAULT_BOOK'] = $co['ACCRUAL'] == 'T' ? 'A' : 'C';
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $res = SetCpaCompany(Globals::$g->_userid, $co);
                    } else if (! $transactionExist) {
                        $co = GetAcctCompany(Globals::$g->_userid);
                        $co['ACCRUAL'] = ($values['COMPANYBOOK'] == 'Accrual' ? 'T' : 'F');
                        $values['DEFAULT_BOOK'] = $co['ACCRUAL'] == 'T' ? 'A' : 'C';
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $res = SetAcctCompany(Globals::$g->_userid, $co);
                    } else if ($values['COMPANYBOOK'] != $curPref) {
                        $multibookError = true;
                    }
                } else {
                    $app = explode('@', Globals::$g->_userid)[2] ?? null;
                    if ($app != 'A' && ! $transactionExist ) {
                        $co = GetCompany(Globals::$g->_userid, true);
                        $co['ACCRUAL'] = ($values['DEFAULT_BOOK'] == 'A' ? 'T' : 'F');
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $res = SetCpaCompany(Globals::$g->_userid, $co);
                    } else if (! $transactionExist) {
                        $co = GetAcctCompany(Globals::$g->_userid);
                        $co['ACCRUAL'] = ($values['DEFAULT_BOOK'] == 'A' ? 'T' : 'F');
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $res = SetAcctCompany(Globals::$g->_userid, $co);
                    } else {
                        $defaultBook = $values['DEFAULT_BOOK'] == 'A' ? 'Accrual' : 'Cash';
                        if ($defaultBook != $curPref) {
                            $multibookError = true;
                        }
                    }
                }
            } else {
                $tmpVal = $isMultibooks ? 'T' : 'F';
                if ($tmpVal != $preferences['MULTIBOOKS']) {
                    $multibookError = true;
                }
            }

            /** @noinspection PhpUndefinedVariableInspection */
            if ($multibookError) {
                $msg = "You are not allowed to change the accounting method when there is a transaction";
                $corr = "You may need to clear all transactions before changing the accounting method.";
                $gErr->addError('GL-5021', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                $ok = false;
            }
        }
        // INVALIDATE THE COMPANY CACHE, SO THAT IT COULD BE REFRESHED
        $ok = $ok && InValidateSession(ProfileHandler::COMPANY_CACHE_FLAG, Session::getKey());

        //END Modification Multibooks
        return $ok;
    }

    /**
     * @param array          $values the values to save to the configuration
     * @param array          $oldValues values of configuration before changes
     * @param IAIssueHandler $gErr
     * @return bool
     */
    private function checkTaskAndCostType($values, $oldValues, $gErr)
    {
        $ok = true;

        if (!empty($values['TASKDIMENSION']) && !empty($oldValues['TASKDIMENSION']) &&
            $values['TASKDIMENSION'] != $oldValues['TASKDIMENSION']) {

            $disabling = $values['TASKDIMENSION'] != 'T' && $oldValues['TASKDIMENSION'] == 'T';
            if ($disabling && self::transactionsExistForTask()) {
                $msg = "You cannot disable task dimension after transactions are entered.";
                $gErr->addError('GL-5022', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
        }

        if ($ok && $values['TASKDIMENSION'] == 'F' && $values['COSTTYPEDIMENSION'] == 'T'){

            $msg = "Task dimension must be enabled when Cost type dimension is enabled.";
            $corr = "Ensure that Task dimension is enabled when Cost type dimension is enabled.";
            $gErr->addError('GL-5023', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }

        if ($ok && $values['TASKDIMENSION'] == 'T' && !IsInstalled(Globals::$g->kPAid)) {
            $msg = "Projects module is not installed.";
            $corr = "Ensure that Projects module is installed before enabling Task as a dimension.";
            $gErr->addError('GL-5024', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }

        if ($ok && $values['PROJECTDIMENSION'] == 'F' && $values['TASKDIMENSION'] == 'T') {
            $msg = "Project dimension is not enabled.";
            $corr = "Ensure that Project dimension is enabled before enabling Task as a dimension.";
            $gErr->addError('GL-5025', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }

        return $ok;
    }

    /**
     * Return DIMKEYORDER value
     *
     * @return array
     */
    public static function getCustomDimensionKeyOrder()
    {
        $dimkeyorder = self::getDIMKEYORDER();

        if ($dimkeyorder == "") {
            return array();
        }

        // Its important to leave empty values, since they represent deleted custom dimensions
        return explode("|", $dimkeyorder);
    }

    /**
     * Return indexes custom dimensions
     *
     * @return array
     */
    public static function getUsedCustDimIndexes()
    {
        $key_order = self::getCustomDimensionKeyOrder();

        $indexes = array();

        // Eliminate empty ones
        for ($i = 0; $i < count($key_order); $i++) {

            if ($key_order[$i] != "") {
                $indexes[$key_order[$i]] = ($i + 1);
            }

        }

        return $indexes;
    }

    /**
     * Return DIMKEYORDER value
     *
     * @return array
     */
    public static function getUsedCustDimColumnNamesMap()
    {
        $key_order = self::getCustomDimensionKeyOrder();

        $columns = array();

        // Eliminate empty ones
        for ($i = 0; $i < count($key_order); $i++) {

            if ($key_order[$i] != "") {
                $columns[$key_order[$i]] = "CUSTDIM" . ($i + 1);
            }

        }

        return $columns;
    }

    /**
     * Return DIMKEYORDER value
     *
     * @param array $defIds          ids
     * @param array &$dimkeyIndexMap dimension key map
     */
    public static function addCustomDimensions($defIds, &$dimkeyIndexMap)
    {
        $kGLid = Globals::$g->kGLid;
        GetModulePreferences($kGLid, $prefs);

        // Throws exception if reached capacity
        $dimkeyIndexes = self::getAvailableCustomDimensionIndex(count($defIds));

        // Take care of order in user interface
        $dimOrderStr = $prefs['DIMENSIONSORDER'];
        //eppp("$dimOrderStr:".$dimOrderStr);
        //BUG:91216 - Adding condition to check for duplicate before adding a custom dimention
        $dimorder = explode('#~#', $dimOrderStr);

        // Do not append new custom dimension if no sequence is specified
        // This will allow default sequence to kick-in
        if (isset($dimOrderStr) && $dimOrderStr != "") {
            for ($i = 0; $i < count($defIds); $i++) {

                $objDefId = $defIds[$i];
                //eppp("objDefId:".$objDefId);
                if(in_array("custdim_" . $objDefId, $dimorder)){
                    throw new Exception("Duplicate dimorder entry");
                }

                $dimOrderStr .= "#~#" . "custdim_" . $objDefId;
            }


            if (!self::setDIMENSIONSORDER($dimOrderStr)) {
                throw new Exception("Error updating GL Setup.");
            }

        }

        // Tare care of dimkey allotment in GLTOTALS table
        $dimKeyOrderStr = self::getDIMKEYORDER();

        // First ever GL Dimension
        /** @noinspection PhpUnusedLocalVariableInspection */
        $dimKeyOrderArr = null;
        if (!isset($dimKeyOrderStr) || $dimKeyOrderStr == "") {
            $dimKeyOrderArr = array_fill(0, MAX_CUSTDIMS, "");
        } else {
            $dimKeyOrderArr = explode("|", $dimKeyOrderStr);

            // Take care of increase in dimensions for old records
            if (count($dimKeyOrderArr) < MAX_CUSTDIMS) {
                $dimKeyOrderArr = array_pad($dimKeyOrderArr, MAX_CUSTDIMS, "");
            }
        }

        for ($i = 0; $i < count($defIds); $i++) {

            $objDefId = $defIds[$i];
            //eppp("objDefId:".$objDefId);

            $dimkeyIndex = $dimkeyIndexes[$i];

            $dimKeyOrderArr[$dimkeyIndex] = $objDefId;
            $dimkeyIndexMap[$objDefId] = $dimkeyIndex;
        }

        $newDimKeyOrderStr = implode("|", $dimKeyOrderArr);
        //epp('$newDimKeyOrderStr:');eppp($newDimKeyOrderStr);
        //epp('$dimkeyIndexMap:');eppp($dimkeyIndexMap);

        if (!self::setDIMKEYORDER($newDimKeyOrderStr)) {
            throw new Exception("Error updating GL Setup.");
        }
    }

    /**
     * Return available custom dimension index
     *
     * @param int $num number of custom dimensions allowed
     *
     * @return array
     */
    public static function getAvailableCustomDimensionIndex($num = 1)
    {
        $dimKeyOrderStr = self::getDIMKEYORDER();

        if (!isset($dimKeyOrderStr)) {
            $dimKeyOrderStr = "";
        }

        $dimKeyOrderArr = explode("|", $dimKeyOrderStr);

        $dimCount = count(array_filter($dimKeyOrderArr));

        // Take care of increase in dimensions for old records
        if ($dimCount < MAX_CUSTDIMS) {
            $dimKeyOrderArr = array_pad($dimKeyOrderArr, MAX_CUSTDIMS, "");
        } else {
            throw new Exception("You have reached maximum allowed number of Custom Dimensions - " . MAX_CUSTDIMS);
        }

        $indexes = array();
        for ($i = 0; $i < count($dimKeyOrderArr); $i++) {

            if ($dimKeyOrderArr[$i] == "") {
                $indexes[] = $i;
            }

            if (count($indexes) == $num) {
                break;
            }
        }

        // Reached capacity
        return $indexes;
    }

    /**
     * Remove custom dimensions
     *
     * @param array $defIds ids
     *
     * @return true
     */
    public static function removeCustomDimensions($defIds)
    {

        $kGLid = Globals::$g->kGLid;
        GetModulePreferences($kGLid, $prefs);

        $dimOrderStr = $prefs['DIMENSIONSORDER'];
        //eppp("$dimOrderStr:".$dimOrderStr);

        // Take care of dimkey allotment
        $dimKeyOrderStr = self::getDIMKEYORDER();
        //eppp("$dimKeyOrderStr:".$dimKeyOrderStr);


        if ($dimOrderStr != "") {
            $toRemove1Arr = array();
            foreach ($defIds as $defId) {
                $toRemove1Arr[] = "custdim_" . $defId;
            }
            //epp("$toRemove1Arr:");  eppp($toRemove1Arr);

            $dimOrderArr = explode("#~#", $dimOrderStr);
            $newDimOrderArr = INTACCTarray_diff($dimOrderArr, $toRemove1Arr);

            $newDimOrderStr = implode("#~#", $newDimOrderArr);
            //eppp("$newDimOrderStr:".$newDimOrderStr);

            if (!self::setDIMENSIONSORDER($newDimOrderStr)) {
                throw new Exception("Error updating GL Setup.");
            }
        }

        if ($dimKeyOrderStr != "") {
            $dimKeyOrderArr = explode("|", $dimKeyOrderStr);
            //eppp($dimKeyOrderArr);

            $toRemove2Arr = array();
            foreach ($defIds as $defId) {
                $toRemove2Arr[] = $defId;
            }

            //epp("$toRemove2Arr:");  eppp($toRemove2Arr);
            for ($i = 0; $i < count($dimKeyOrderArr); $i++) {
                $defId = $dimKeyOrderArr[$i];

                if (in_array($defId, $toRemove2Arr)) {
                    $dimKeyOrderArr[$i] = "";
                }
            }

            $newDimKeyOrderStr = implode("|", $dimKeyOrderArr);
            //eppp("$newDimKeyOrderStr:".$newDimKeyOrderStr);

            if (!self::setDIMKEYORDER($newDimKeyOrderStr)) {
                throw new Exception("Error updating GL Setup.");
            }
        }

        return true;
    }

    /**
     * Set DimensionsOrder property in the db
     *
     * @param string $newdimorder dimension order
     *
     * @return bool false if error
     */
    private static function setDIMENSIONSORDER($newdimorder)
    {
        $kGLid = Globals::$g->kGLid;
        $qry = "DELETE modulepref WHERE cny# = :1 AND modulekey = :2 AND property = :3 ";
        $ok = ExecStmt(array($qry, GetMyCompany(), $kGLid, 'DIMENSIONSORDER'));

        $qry = "INSERT INTO modulepref (cny#,modulekey,property,value,locationkey) VALUES (:1,:2,:3,:4,:5)";
        $ok = $ok && ExecStmt(array($qry, GetMyCompany(), $kGLid, 'DIMENSIONSORDER', $newdimorder, ''));

        return $ok;
    }

    /**
     * Return DimensionsOrder value
     *
     * @return string
     */
    private static function getDIMKEYORDER()
    {
        if (self::$dimKeyOrder === null) {
            $cny = GetMyCompany();
            if ($cny) {
                $selqry = "SELECT value FROM companypref WHERE cny# = :1 AND property = :2";
                $res = QueryResult(array($selqry, $cny, 'DIMKEYORDER'));
            } else {
                $res = array();
            }
            //epp('$res:');eppp($res);

            self::$dimKeyOrder = $res[0]['VALUE'] ?? "";
        }

        return self::$dimKeyOrder;
    }

    /**
     * Set DimensionsKeyOrder property in the db
     *
     * @param string $dimkeyorder dimension key order
     *
     * @return bool false if error
     */
    private static function setDIMKEYORDER($dimkeyorder)
    {
        $delqry = "DELETE companypref WHERE cny# = :1 AND property = :2";
        if (!ExecStmt(array($delqry, GetMyCompany(), 'DIMKEYORDER'))) {
            return false;
        }

        $gManagerFactory = Globals::$g->gManagerFactory;
        $MM = $gManagerFactory->getManager('cosetup');
        $recId = $MM->GetNextSequence('COMPANYPREF');

        if (!isset($recId)) {
            return false;
        }

        $insertqry = "INSERT INTO companypref (cny#,record#,property,value) VALUES (:1,:2,:3,:4)";
        if (!ExecStmt(array($insertqry, GetMyCompany(), $recId, 'DIMKEYORDER', $dimkeyorder))) {
            return false;
        }

        return true;
    }

    /**
     * Fetches the GL module configuration
     *
     * @return array the GL module configuration
     */
    private static function getConfig()
    {
        GetModulePreferences(Globals::$g->kGLid, $config);
        return $config;
    }

    /**
     * determines if any gl approvals are configured
     *
     * @param string $journalId optional journal id.  If present will further determine if there are approval
     *                          rules for ths specified journal.
     *
     * @return bool true if any gl approvals are configured
     */
    public static function isApprovalEnabled($journalId = null)
    {
        $prefs = self::getConfig();
        $enabled = crackArray($prefs, 'JEAPPROVAL_ENABLE', 'F') === 'T';
        if ($enabled && $journalId) {
            $process = self::getJEApprovalProcess($journalId);
            if (!$process) {
                $enabled = false;
            }
        }
        return $enabled;
    }

    /**
     * determines if journal entry email notification is enabled
     *
     * @return bool true if email notification is enabled for je approvals
     */
    public static function isEmailNotificationsEnabled()
    {
        $prefs = self::getConfig();
        return crackArray($prefs, 'JEAPPROVAL_NOTIFY', 'F') === 'T';
    }

    /**
     * determines if statutory reporting period is configured
     *
     * @return bool
     */
    public static function isStatutoryReportingEnable()
    {
        $prefs = self::getConfig();
        $enabled = crackArray($prefs, 'ENABLE_STATUTORY_REPORTING_PERIOD', 'F') === 'T';

        return $enabled;
    }

    /**
     * @param array $locationKeys
     *
     * @return bool|array|string
     */
    public static function getFutureLimitForEntities(array $locationKeys) : bool|array|string
    {
        $query = [];
        $query[0] = "select locationkey, value as futureopenperiod from modulepref where cny# = :1 and modulekey = :2 and property = :3 ";
        $query[1] = GetMyCompany();
        $query[2] = '2.GL';
        $query[3] = 'OPEN_PERIOD_LIMIT';


        if ( isNonEmptyArray($locationKeys) ) {
            $query = PrepINClauseStmt($query, $locationKeys, " AND locationkey ");
        }

        $result = QueryResult($query) ? : [];

        return $result;
    }

    /**
     * Returns a single preference for the given Location
     *
     * @param string $property
     * @param string $mod
     * @param array  $locationKeys
     *
     * @return string|false
     */
    public static function getPreferenceForEntities(string $property, string $mod, array $locationKeys) : bool|string
    {
        $query = [];
        $query[0] = "select distinct(value) as futureopenperiod from modulepref where cny# = :1 and modulekey = :2 and property = :3 ";
        $query[1] = GetMyCompany();
        $query[2] = $mod;
        $query[3] = $property;


        if ( isNonEmptyArray($locationKeys) ) {
            $query = PrepINClauseStmt($query, $locationKeys, " AND locationkey ");
        }

        $query[0] .= " group by locationkey, value";
        $result = QueryResult($query);

        $resCount = util::countOrZero($result);

        if ( $resCount == 0 ) {
            //If there is no preference set for entity, then get the root value
            $output = GetPreferenceForProperty($mod, 'OPEN_PERIOD_LIMIT');
        } else if ( $resCount == 1 ) {
            $output = isset($result[0]['FUTUREOPENPERIOD']) ? $result[0]['FUTUREOPENPERIOD'] : false;
        } else if ($resCount > 1) {
            //If each entity has different values then return false
            $output = false;
        }

        return $output;
    }

    /**
     * builds out the approval process for the specified journal
     *
     * @param string $journalId the journal id
     *
     * @return array|bool the approval process or false if the approval process cannot
     *                    be created
     */
    public static function getJEApprovalProcess($journalId)
    {
        $process = [];
        $prefs = self::getConfig();

        if ($journalId && $prefs['JEAPPROVAL_ENABLE'] == 'T') {
            $selfApproval = crackArray($prefs, 'JEAPPROVAL_SELFAPPROVE', 'T') == 'T';
            $currentUserLogin = Profile::getProperty('login');
            $manager = Globals::$g->gManagerFactory->getManager('userinfo');
            $manager->GetUserRecFromLogin($currentUserLogin, $userRec);
            $approvers = self::getJournalApprovers($journalId);
            if ($approvers) {
                foreach ($approvers as $value) {
                    /*
                     * Return Primary Approver if,
                     * Self approval is enabled (ie., Substitute Approver column will not be there) (OR)
                     * Logged-in user is not a Primary Approver (OR)
                     * Logged-in user is not in Primary Approver Group
                    */
                    if ($selfApproval || ($value['PRIMARYAPPROVERLOGINID'] !='' && $currentUserLogin !== $value['PRIMARYAPPROVERLOGINID'])
                        || ($value['PRIMARYAPPROVERGROUPMEMBERS'] != '' && !in_array($userRec, explode(',', $value['PRIMARYAPPROVERGROUPMEMBERS']))
                        || ($value['PRIMARYAPPROVERTYPE'] == 'EMA' && UserInfoManager::currentUserHasManager() == true))
                    ) {
                        $process[] = self::constructApproverList($value, 'PRIMARY', $value['PRIMARYAPPROVERTYPE']);
                    } else if ($value['ALTERNATEAPPROVERTYPE']) {
                        $process[] = self::constructApproverList(
                            $value, 'ALTERNATE', $value['ALTERNATEAPPROVERTYPE']
                        );
                    } else {
                        global $gErr;
                        $msg = "A Substitute approver is required for " . $journalId .
                            "because you can’t approve your own submission. Save ".
                            "your transaction in draft mode and contact your ".
                            "administrator to assign a Substitute approver.";
                        $gErr->addIAError('GL-5026', __FILE__ . ':' . __LINE__, $msg, ['JOURNALID' => $journalId]);

                        return false;
                    }
                }
            }
        }

        return $process;
    }

    /**
     * determines if the specified user (record#) is the admin approver for journal entries
     *
     * @param int $userrec a user's record#
     *
     * @return bool true if the specified user is the je admin approver
     */
    public static function isAdminApprover($userrec)
    {
        $prefs = self::getConfig();

        $uservals = explode("--", $prefs['ADMINUSER']);
        if (isset($uservals[0]) && $uservals[0]) {
            global $gManagerFactory;
            /** @var UserInfoManager $userinfoMgr */
            $userinfoMgr = $gManagerFactory->getManager('userinfo');
            $adminApprover = $userinfoMgr->Get($uservals[0]);
            if ($adminApprover['RECORD#'] == $userrec) {
                return true;
            }
        }
        return false;
    }

    /**
     * validates je approval config
     *
     * @param array &$values  the new gl module configuration
     * @param bool  $inEntity true if we're in an ME-shared entity
     *
     * @return bool true if valid
     */
    private function validateApprovalPreferences(&$values, $inEntity)
    {
        global $gErr;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $journalMgr = Globals::$g->gManagerFactory->getManager('journal');

        $ok = true;

        if ( ! $values['JEAPPROVAL_ENABLE']) {
            $values['JEAPPROVAL_ENABLE'] = 'false';
        }

        if ($values['JEAPPROVAL_ENABLE'] == 'false') {
            return $ok;
        }

        //  Get the list of authorized users
        $validApprovers = self::getAuthorizedGLBatchApprovers();

        if ( ! $inEntity) {
            // Let's make sure the admin user is valid
            if (isset($values['ADMINUSER'])) {
                $uservals = explode("--", $values['ADMINUSER']);
                if ( ! isset($validApprovers[$uservals[0]])) {
                    $msg = $values['ADMINUSER']
                        . " isn't a valid Admin approver. Make sure that "
                        . $values['ADMINUSER'] . " has permission"
                        . " to approve journal entries or select another user as the Admin approver. ";
                    $gErr->addIAError(
                        'GL-5047', __FILE__ . ':' . __LINE__, $msg, ['ADMINUSER' => $values['ADMINUSER']]
                    );
                    $ok = false;
                }

                // Append the description to the userid value.
                if (isset($validApprovers[$uservals[0]])
                    && $validApprovers[$uservals[0]] != ''
                ) {
                    $values['ADMINAPPROVER'] = $uservals[0] . "--"
                        . $validApprovers[$uservals[0]];
                } else {
                    $values['ADMINAPPROVER'] = $uservals[0];
                }
            }
        }

        // Add the system approvers to validate the approval process rows
        $systemApproversInternal = JEApproverPickManager::GetSystemApprovers();
        $systemApproversExternal = JEApproverPickManager::convertSystemApproversToExternalLabels($systemApproversInternal);
        foreach ($systemApproversExternal as $systemApprover) {
            $approver = $systemApprover['PICKID'];
            $validApprovers[$approver] = '';
        }

        //$offset = 0;
        $jeApprovers = [];

        foreach ($values['JOURNAL_GRID'] as $key => $approver) {
            $journalName = $approver['JOURNAL'];
            $user = $approver['USER'];
            $user2 = $approver['USER2'];
            $uservals = null;

            if ( ! trim($journalName, '--')) {
                unset($values['JOURNAL_GRID'][$key]);
                //$offset++;
            } else {
                if ( ! $user) {
                    $msg
                        = "There’s no Approver selected for {$journalName} on line "
                        . ($key + 1) . " ."
                        . " Designate an Approver for the journal. ";
                    $gErr->addIAError(
                        'GL-5048', __FILE__ . ':' . __LINE__, $msg, ['JOURNALNAME' => $journalName, 'KEY' => ($key + 1)]
                    );
                    $ok = false;
                } else {
                    $uservals = explode("--", $user);
                    if ( ! isset($validApprovers[$uservals[0]])) {
                        $msg
                            = "{$user} isn’t a valid Approver for {$journalName} on line "
                            . ($key + 1) . "."
                            . " Make sure that {$user} has permissions to approve journal entry transactions"
                            . " or select a different Approver. ";
                        $gErr->addIAError(
                            'GL-5049', __FILE__ . ':' . __LINE__, $msg,
                            ['USER' => $user, 'JOURNALNAME' => $journalName, 'KEY' => ($key + 1)]
                        );
                        $ok = false;
                    }
                }
                
                if ($values['JEAPPROVAL_SELFAPPROVE'] == 'true' || $values['JEAPPROVAL_SELFAPPROVE'] == 'T') {
                    $values['JOURNAL_GRID'][$key]['USER2'] = '';
                } else if ( ! $user2) {
                    $msg
                        = "There’s no Substitute approver selected for {$journalName} on line "
                        . ($key + 1) . ""
                        . " and the Approver can’t self-approve. Designate a Substitute approver for this journal. ";
                    $gErr->addIAError(
                        'GL-5050', __FILE__ . ':' . __LINE__, $msg, ['JOURNALNAME' => $journalName, 'KEY' => ($key + 1)]
                    );
                    $ok = false;
                } else {
                    $user2vals = explode("--", $user2);
                    if ( ! isset($validApprovers[$user2vals[0]])) {
                        $msg = "{$user2} assigned on line " . ($key + 1)
                            . " isn’t a valid Journal Entry approver."
                            . " Make sure that {$user2} has the correct permissions to be able to approve journal"
                            . " entries or select another user or user group for the approver.";
                        $gErr->addIAError(
                            'GL-5051', __FILE__ . ':' . __LINE__, $msg, ['USER2' => $user2, 'KEY' => ($key + 1)]
                        );
                        $ok = false;
                    }
                    /** @noinspection PhpUndefinedVariableInspection */
                    //Restrict Approver and Substitute having same user in the same level
                    if ($uservals[0] == $user2vals[0]
                        && $uservals[1] == $user2vals[1]
                    ) {
                        $msg
                            = "Select a different user or user group for the Approver and Substitute approver."
                            . " The Approver and the Substitute approver can't overlap.";
                        $gErr->addError(
                            'GL-5027', __FILE__ . ':' . __LINE__, $msg
                        );
                        $ok = false;
                    }

                    //Restrict Approver and Substitute having UserGroup in the same level
                    if ($uservals[1] == 'UserGroup'
                        && $user2vals[1] == 'UserGroup'
                    ) {
                        $msg
                            = "For the same approval level, a user group can be".
                            " the Approver or the Substitute approver, but not ".
                            "both. Make one of your selections an individual user.";
                        $gErr->addError(
                            'GL-5028', __FILE__ . ':' . __LINE__, $msg
                        );
                        $ok = false;
                    }

                    if ($uservals[1] == 'UserGroup'
                        && self::IsApproverExistInUserGroup(
                            $user2vals[0], $uservals[0]
                        ) == true
                    ) {
                        $msg = "For journal " . $journalName . ", the user ".$user2vals[0]." is ".
                            "either the Approver or a member of the Approver ".
                            "user group. Because overlapping assignments aren’t ".
                            "supported, this ".$user2vals[0]." can't be assigned as a ".
                            "Substitute approver.  Select a different Substitute ".
                            "approver or remove the member from the user group.";
                        $gErr->addIAError(
                            'GL-5052', __FILE__ . ':' . __LINE__, $msg, ['JOURNALNAME' => $journalName, 'USER2VALS' => $user2vals[0]]
                        );
                        $ok = false;
                    }

                    if ($user2vals[1] == 'UserGroup'
                        && self::IsApproverExistInUserGroup(
                            $uservals[0], $user2vals[0]
                        ) == true
                    ) {
                        $msg = "For journal ".$journalName.", the user ".$uservals[0] .
                            " is either the Substitute approver or a member of the".
                            " Substitute approver user group. Because overlapping".
                            " assignments aren’t supported, this ".$uservals[0].
                            " can't be assigned as an Approver. Select a different".
                            " Approver or remove the member from the user group.";
                        $gErr->addIAError(
                            'GL-5053', __FILE__ . ':' . __LINE__, $msg, ['JOURNALNAME' => $journalName, 'USERVALS' => $uservals[0]]
                        );
                        $ok = false;
                    }
                }

                if (isset($jeApprovers[$journalName][$uservals[0]])) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $msg
                        = "A user can be selected only once per journal as an approver. {$uservals[0]}"
                        . " is designated as the Approver for the same journal multiple times. Select a different Approver"
                        . " or remove {$uservals[0]} to resolve the duplicate selection.";
                    $gErr->addIAError(
                        'GL-5054', __FILE__ . ':' . __LINE__, $msg, ['USERVALS' => $uservals[0]]
                    );
                    $ok = false;
                }

                //Validate UserGroup Member duplication
                if ($uservals[1] == 'UserGroup') {
                    //$ugroupMembers = [];
                    $objUgroupMembersManager
                        = Globals::$g->gManagerFactory->getManager(
                        'usergroupmembers'
                    );
                    $groupMemberData
                        = $objUgroupMembersManager->getUserGroupMemberByGroupName(
                        $uservals[0]
                    );
                    if ($groupMemberData) {
                        $duplicateMembers = null;
                        foreach ($groupMemberData as $member) {
                            //$ugroupMembers[$member['NAME']] = $member['RH'];

                            if ( ! isset($jeApprovers[$journalName][$member['NAME']])) {
                                $jeApprovers[$journalName][$member['NAME']]
                                    = true;
                            } else {
                                $duplicateMembers[] = $member['NAME'];
                            }
                        }

                        if ($duplicateMembers) {
                            /** @noinspection PhpUndefinedVariableInspection */
                            $msg
                                = "{$uservals[0]} or one of its members is selected more than once as the Approver "
                                . "for {$journalName}. Select a different Approver or adjust the members of "
                                . "{$uservals[0]} to remove the duplicate selection.";
                            $gErr->addIAError(
                                'GL-5055', __FILE__ . ':' . __LINE__, $msg, ['USERVALS' => $uservals[0], 'JOURNALNAME' => $journalName]
                            );
                            $ok = false;
                        }
                    }
                }

                /** @noinspection PhpUndefinedVariableInspection */
                $jeApprovers[$journalName][$uservals[0]] = true;
            }
        }

        return $ok;
    }

    /**
     * Check if Approver exists in user group
     *
     * @param string $approver
     * @param string $usergroup
     *
     * @return bool
     */
    private static function IsApproverExistInUserGroup($approver, $usergroup) {
        $manager = Globals::$g->gManagerFactory->getManager('userinfo');
        $manager->GetUserRecFromLogin($approver, $userRec);
        $objUgroupMembersManager = Globals::$g->gManagerFactory->getManager('usergroupmembers');
        return $objUgroupMembersManager->checkUserExistInGroup($userRec, null, $usergroup);
    }
    /**
     * removes all je approval settings from module prefs table.
     *
     * @return bool false if error
     */
    private function clearExistingApprovalPreferences()
    {

        $stmt[0] = "DELETE modulepref where cny# = :1 and modulekey = :2 and "
            . "( property in ('JEAPPROVAL_NOTIFY', 'JEAPPROVAL_SELFAPPROVE', 'ADMINUSER') )";
        $stmt[1] = GetMyCompany();
        $stmt[2] = $this->_moduleKey;

        if (!ExecStmt($stmt)) {
            return false;
        }

        return true;

    }

    /**
     * Removes all gl module prefs except me-shared approval settings when we're in a ME-entity context.
     *
     * @param array &$values gl prefs about to be saved
     *
     * @see ModuelSetupManager::SetByRef
     */
    protected function sanitizeBeforeSet(&$values)
    {
        if (getMEContextLocation()) {
            $kGLid = Globals::$g->kGLid;
            $enableStatutoryReportingPeriod = GetPreferenceForProperty($kGLid, 'ENABLE_STATUTORY_REPORTING_PERIOD');

            $newValues = array();
            for ($i = 0; true; $i++) {
                $key = 'ME_JOURNAL_' . $i;
                if (!isset($values[$key])) {
                    break;
                }
                $newValues[$key] = $values[$key];
            }

            if($enableStatutoryReportingPeriod === 'T') {
                $newValues['OPEN_PERIOD_LIMIT'] = $values['OPEN_PERIOD_LIMIT'];
            }

            $values = $newValues;

        }

    }

    /**
     * get the list of users authorized to approve journal entries
     * @param string $locationKey
     *
     * @return array list of userids.  $ret[$userId] = $userDesc
     */
    public static function getAuthorizedGLBatchApprovers($locationKey = null)
    {
        if (self::$authorizedGLBatchApprovers === null) {
            global $gManagerFactory;
            $approverPickMgr = $gManagerFactory->getManager(
                'jeadminapproverpick'
            );
            $result = $approverPickMgr->getAuthorizedApprovalUsers($locationKey);
            $validApprovers = [];

            foreach ($result as $ret) {
                if ($ret['DESCRIPTION'] === null) {
                    $ret['DESCRIPTION'] = '';
                }
                $validApprovers[$ret['LOGINID']] = $ret['DESCRIPTION'];
            }

            $approverGroupPickMgr = $gManagerFactory->getManager(
                'jeapprovergrouppick'
            );
            $groupApprovers = $approverGroupPickMgr->getApproverGroup($result);

            foreach ($groupApprovers as $groupApprover) {
                $validApprovers[$groupApprover['NAME']] = "UserGroup";
            }
            self::$authorizedGLBatchApprovers = $validApprovers ?? "";
        }

        return self::$authorizedGLBatchApprovers;
    }

    /**
     * Retrieves dimension relationships to be displayed on GL Setup page
     *
     * @param array $retPreferences
     */
    private function getDimensionSetup(&$retPreferences)
    {
        $uddSetup = array();

        $uddMap = Pt_DimensionUtil::getDimFieldsMap();
        $ctxMap = Pt_DimensionUtil::getPlatformDefinedGLDimensionRestrictionsFieldNames();

        $hideFromDimensionRelationship = ['COSTTYPE'];

        foreach ($uddMap as $defId => $fieldName) {

            if (in_array($fieldName, $hideFromDimensionRelationship)) {
                continue;
            }

            $objDef = Pt_DataObjectDefManager::getById($defId);
            $setup = array();
            $setup['DIMDISPLAYNAME'] = I18N::getSingleToken($objDef->getSingularName());
            $setup['DIMOBJDEFID'] = "DEFID_" . $objDef->getId();
            $setup['DIMAUTOFILLENABLED'] = $objDef->autoFillRelatedObjects();
            $setup['DIMALLOWOVERRIDE'] = $objDef->autoFillEnableOverride();

            // Auto-fill
            $this->getAutofillDimensionSetup($retPreferences, $objDef, $setup);

            // Filtering
            if ($ctxMap[$fieldName]) {
                $this->getFilteringDimensionSetup($ctxMap[$fieldName], $setup);
            }

            $uddSetup[] = $setup;
        }

        $retPreferences['DIMSETUP'] = $uddSetup;
    }

    /**
     * Check if transactions exist which use the Task dimension.  (If any exist, then Task can not be disabled as a dimension.)
     *
     * @return bool
     */
    public static function transactionsExistForTask()
    {
        // check if there are already GL transactions with TASKDIMKEY set:
        $result = QueryResult(
            array(
                "select 1 as exist from dual where exists (select 1 from glentrymst where cny# = :1 and taskdimkey is not null and rownum = 1)",
                GetMyCompany()
            )
        );
        return !empty($result) && $result[0]['EXIST'];
    }

    /** Determine if a dimension should be hidden (not available) in GL Dimensions table
     *
     * @param string $dimKey dimension to consider for hiding
     * @return bool
     * @throws Exception
     */
    public function hideDimension($dimKey)
    {
        $hide = false;
        // consider making the $dimKey comparison case-insensitive?  (ask Nadeem)
        switch ($dimKey) {
            case 'task':
                $hide = $this->hideTask();
                break;
            case 'contract':
                $hide = !IsInstalled(Globals::$g->kCNid);
                break;
            case 'costtype':
                $hide = $this->hideCostType();
                break;
            case 'fixedasset':
                $hide = !IsInstalled(Globals::$g->kFAid);
                break;    
            case 'affiliateentity':
                $hide = !MESetupManager::isAffiliateEntityDimensionEnabled();
                break;
        }
        return $hide;
    }

    /** Determine if Task should be hidden (not available) in GL Dimensions table
     *
     * @return bool true if Task dimension should be hidden from Dimensions table, otherwise false
     *
     * @throws Exception
     */
    public function hideTask()
    {
        // Do not show the task dimension if Projects module is not installed/subscribed
        return !IsInstalled(Globals::$g->kPAid);
    }

    /** Determine if the cost type dimension should be hidden, based on feature flag settings and whether it's
     * a production company and transactions exist
     *
     * @return bool
     * @throws Exception
     */
    public function hideCostType()
    {
        // Do not show the costtype dimension if the CRE feature is not enabled
        if (!CRESetupManager::isCREInstalled()) {
            return true;
        }
        
        // Also do not show it if it is a production company and there are transactions
        $result = QueryResult(
            array(
                "select 1 as exist from dual where exists (select 1 from glentrymst where cny# = :1 and rownum = 1)",
                GetMyCompany()
            )
        );
        $transactionsExist = !empty($result) && $result[0]['EXIST'];
        if ($this->isProductionCompany() && $transactionsExist) {
            return true;
        }

        return false;
    }

    /**
     * @param array            $retPreferences
     * @param Pt_DataObjectDef $objDef
     * @param array            $setup
     */
    private function getAutofillDimensionSetup($retPreferences, Pt_DataObjectDef $objDef, &$setup)
    {
        if (isset($retPreferences['DIMENSION_AUTOFILL_SEQUENCESTORED'])
            && $retPreferences['DIMENSION_AUTOFILL_SEQUENCESTORED'] != ''
        ) {
            $dimSeq = unserialize($retPreferences['DIMENSION_AUTOFILL_SEQUENCESTORED']);
            if ($dimSeq["DEFID_" . $objDef->getId()]) {
                $setup['DIMAUTOFILL'] = implode(",", $dimSeq["DEFID_" . $objDef->getId()]);
            }
        } else {
            $objDefIds = Pt_DimensionUtil::getAutoFilledDimensions($objDef->getId(), true);
            if (count($objDefIds) > 0) {
                $autoFillDefIds = array();
                foreach ($objDefIds as $defId) {
                    $autoFillDefIds[] = "DEFID_" . $defId;
                }

                $setup['DIMAUTOFILL'] = implode(",", $autoFillDefIds);
            }
        }
    }

    /**
     * @param array $filteredbyRaw
     * @param array $setup
     */
    private function getFilteringDimensionSetup($filteredbyRaw, &$setup)
    {
        $filteredby = array();
        foreach ($filteredbyRaw as $detail) {
            $objDef = Pt_DataObjectDefManager::getByName($detail['source_dimension']);
            if ($objDef) {
                $filteredby[] = I18N::getSingleToken($objDef->getSingularName());
            }
        }

        $setup['DIMRESTRICTEDBY'] = implode(", ", $filteredby);
    }


    /**
     * @param array $values
     */
    protected function collectUDDAutofillOverrideSequence(&$values)
    {
        $seqarr = array();
        foreach ( $values['DIMSETUP'] as $value) {
            if (isset($value['DIMAUTOFILL']) && $value['DIMAUTOFILL'] != '') {
                $seqarr[$value['DIMOBJDEFID']] = explode(",", $value['DIMAUTOFILL']);
            }
        }

        $values['DIMENSION_AUTOFILL_SEQUENCESTORED'] = serialize($seqarr);
    }

    /**
     * Return DIMENSION_AUTOFILL_SEQUENCESTORED value, this time with dimension field names
     *
     * @return array
     */
    public static function getDimensionAutofillSequenceWithFieldNames()
    {
        $rawSeq = self::getDimensionAutofillSequence();
        //eppp_p($rawSeq);

        $fieldNameSeq = array();
        $dimMap = Pt_DimensionUtil::getDimFieldsMap();

        foreach ($rawSeq as $destDim => $srcDimsArr) {
            //eppp_p($srcDimsArr);

            $destDefId = (int)(substr($destDim, 6, strlen($destDim)));
            //eppp_p($destDefId);

            if ($dimMap[$destDefId]) {

                $fieldNameSeq [$dimMap[$destDefId]] = array();
                foreach ($srcDimsArr as $defIdStr) {
                    $defId = (int)(substr($defIdStr, 6, strlen($defIdStr)));

                    $fieldNameSeq[$dimMap[$destDefId]][] = $dimMap[$defId];
                }
            }
        }

        return $fieldNameSeq;
    }

    /**
     * Get saved auto-fill sequence
     *
     * @return array
     *
     * @throws Exception
     */
    public static function getDimensionAutofillSequenceSaved()
    {
        /**
         * @var ManagerFactory $gManagerFactory
         */
        $gManagerFactory = Globals::$g->gManagerFactory;
        $mgr = $gManagerFactory->getManager('glsetup');
        $prefs = $mgr->getWithDimSetup(false);

        return $prefs['DIMENSION_AUTOFILL_SEQUENCESTORED']
            ? unserialize($prefs['DIMENSION_AUTOFILL_SEQUENCESTORED']) : null;
    }

    /**
     * Return DIMENSION_AUTOFILL_SEQUENCESTORED value
     *
     * @return array
     */
    public static function getDimensionAutofillSequence()
    {
        $seq = self::getDimensionAutofillSequenceSaved();

        if (!isset($seq)) {
            $seq = self::recalcAutofillSequence();
        }

        return $seq;
    }

    /**
     * Recalculates DIMENSION_AUTOFILL_SEQUENCESTORED value
     *
     * @return array
     */
    public static function recalcAutofillSequence()
    {
        $dimAutofillSequence = array();
        $dimMap = Pt_DimensionUtil::getDimFieldsMap();

        foreach ( $dimMap as $defId => $fieldName) {
            $objDefIds = Pt_DimensionUtil::getAutoFilledDimensions($defId, true);

            if (count($objDefIds) > 0) {
                $arr = array();
                foreach ($objDefIds as $autoFilledDefId) {
                    $arr[] = 'DEFID_' . $autoFilledDefId;
                }
                $dimAutofillSequence['DEFID_' . $defId] = $arr;
            }
        }

        return $dimAutofillSequence;
    }

    /**
     * Set DIMENSION_AUTOFILL_SEQUENCESTORED property in the db
     *
     * @param mixed $dimAutofillSequence dimension key order
     *
     * @return bool false if error
     */
    public static function setDimensionAutofillSequence($dimAutofillSequence)
    {
        $delqry = "DELETE modulepref WHERE cny# = :1 AND modulekey = :2 AND property = :3";
        if (!ExecStmt(array($delqry, GetMyCompany(), '2.GL', 'DIMENSION_AUTOFILL_SEQUENCESTORED'))) {
            return false;
        }

        $insertqry = "INSERT INTO modulepref (cny#,modulekey,property,value) VALUES (:1,:2,:3,:4)";
        if (!ExecStmt(
            array($insertqry, GetMyCompany(), '2.GL', 'DIMENSION_AUTOFILL_SEQUENCESTORED'
            , serialize($dimAutofillSequence))
        )) {
            return false;
        }

        return true;
    }

    /**
     * Checks if dimension filtering is enabled
     *
     * @return bool
     */
    public static function isDimensionFilteringEnabled()
    {
        $companyCache = CompanyCacheHandler::getInstance(GetMyCompany());
        if (!$companyCache->hasProperty('ENABLEDIMENSIONFILTERING')) {
            $mgr = Globals::$g->gManagerFactory->getManager('glsetup');
            $prefs = $mgr->get('');

            $filteringEnabled = $prefs['ENABLEDIMENSIONFILTERING'] == 'true';

            $companyCache->setProperty('ENABLEDIMENSIONFILTERING', $filteringEnabled);
            $companyCache->saveData();
        }

        return $companyCache->getProperty('ENABLEDIMENSIONFILTERING');
    }

    /**
     * Contract setup to automatically enable Contract dimension
     *
     * @return bool
     */
    public function enableContractDimension()
    {
        $ok = true;

        $kGLid = Globals::$g->kGLid;
        GetModulePreferences($kGLid, $prefs);

        $dimOrder = $prefs['DIMENSIONSORDER'];

        if (strpos($dimOrder, "#~#contract") === false) {
            $dimOrder = $dimOrder . "#~#" . "contract";
        }

        $ok = $ok && $this->SetPreference('DIMENSIONSORDER', $dimOrder);
        $ok = $ok && $this->SetPreference('BSBCONTRACT', 'F');
        $ok = $ok && $this->SetPreference('CONTRACTDIMENSION', 'T');

        return $ok;
    }

    /**
     * CRE setup to automatically enable CRE dimensions - project, task and cost type
     *
     * @return bool
     */
    public function enableCREDimensions()
    {
        $kGLid = Globals::$g->kGLid;
        GetModulePreferences($kGLid, $prefs);

        $dimOrder = $prefs['DIMENSIONSORDER'];

        if (strpos($dimOrder, "#~#project") === false) {
            $dimOrder = $dimOrder . "#~#" . "project";
        }
        if (strpos($dimOrder, "#~#task") === false) {
            $dimOrder = $dimOrder . "#~#" . "task";
        }
        if (strpos($dimOrder, "#~#costtype") === false) {
            $dimOrder = $dimOrder . "#~#" . "costtype";
        }

        $ok = $this->SetPreference('DIMENSIONSORDER', $dimOrder);

        $ok = $ok && $this->SetPreference('PROJECTDIMENSION', 'T');
        $ok = $ok && $this->SetPreference('TASKDIMENSION', 'T');
        $ok = $ok && $this->SetPreference('COSTTYPEDIMENSION', 'T');

        return $ok;
    }

    /**
     * ME setup to automatically enable the Affiliate Entity dimension in GL when it is being enabled in ME setup
     *
     * @return bool
     */
    public function enableAffiliateEntityDimension() : bool
    {
        $ok = true;

        $dimOrder = $this->getSetupPreferenceForProperty('DIMENSIONSORDER');

        if (strpos($dimOrder, "#~#affiliateentity") === false) {
            $dimOrder = $dimOrder . "#~#" . "affiliateentity";
        }

        $ok = $ok && $this->SetPreference('DIMENSIONSORDER', $dimOrder);
        $ok = $ok && $this->SetPreference('BSBAFFILIATEENTITY', 'F');
        $ok = $ok && $this->SetPreference('AFFILIATEENTITYDIMENSION', 'T');

        return $ok;
    }

    /**
     *  getAuditApprovalPrefix - returns the field prefix for approvals, which is different depending
     *   on whether we are in a location context or not.  Used by Audit Trail.
     *
     * @return string Prefix for field names associated with approval settings.
     */
    private function getAuditApprovalPrefix()
    {
        //  The properties for journal approval are different depending on if we're in a non-ME or root company
        //   as opposed to an ME company entity.
        $currentLoc = GetContextLocation();
        return empty($currentLoc) ? "JOURNAL_" : "ME_JOURNAL_";
    }

    /**
     * getAuditFields - get the audit fields, add in the approval levels.
     *
     * @param array &$additionalFieldInfo Allows optionally adding fieldinfo for fields.
     *
     * @return string[]
     */
    public function getAuditFields(&$additionalFieldInfo)
    {

        $keys = parent::getAuditFields($additionalFieldInfo);

        //  Add in the approval levels.
        $jprefix = $this->getAuditApprovalPrefix();
        for ($i = 0; $i < MAX_GL_APPROVAL_LEVELS; $i++) {
            $levelName = $jprefix . $i;

            $keys[] = $levelName;
            $additionalFieldInfo[$levelName] = [
                'path' => $levelName,
                'fullname' => 'Level ' . $i . ' journal:approver',
            ];
        }

        global $gBooleanType;
        $otherFields = [
            'AUTOSTATPOST' => ['fullname' => 'Dimension counts'],
            'DIMENSIONSORDER' => ['fullname' => 'Dimension order'],
            'BSBL' => ['fullname' => 'Balance by location', 'type' => $gBooleanType],
            'BSBDEPARTMENT' => ['fullname' => 'Balance by department', 'type' => $gBooleanType],
            'BSBCLASS' => ['fullname' => 'Balance by class', 'type' => $gBooleanType],
            'BSBCUSTOMER' => ['fullname' => 'Balance by customer', 'type' => $gBooleanType],
            'BSBEMPLOYEE' => ['fullname' => 'Balance By employee', 'type' => $gBooleanType],
            'BSBITEM' => ['fullname' => 'Balance by item', 'type' => $gBooleanType],
            'BSBPROJECT' => ['fullname' => 'Balance by project', 'type' => $gBooleanType],
            'BSBVENDOR' => ['fullname' => 'Balance by vendor', 'type' => $gBooleanType],
            'BSBCONTRACT' => ['fullname' => 'Balance by contract', 'type' => $gBooleanType],
            'BSBTASK' => ['fullname' => 'Balance by task', 'type' => $gBooleanType],
            'BSBWAREHOUSE' => ['fullname' => 'Balance by warehouse', 'type' => $gBooleanType],
            'BSBCOSTTYPE' => ['fullname' => 'Balance by cost type', 'type' => $gBooleanType],
        ];
        AuditTrail::addAuditFields($otherFields, $keys, $additionalFieldInfo);

        return $keys;
    }

    /**
     * transformAuditValue - transforms audit field value from internal to external representation.
     *  For this class, transforms special approvers fields.
     *
     * @param string $path        Name of field.
     * @param array  $values      Object values.
     * @param string $fullPath    Name of field path in full values.
     * @param array  &$fullValues Object values in entire object.
     *
     * @return string Transformed value.
     */
    public function transformAuditValue($path, $values, $fullPath, &$fullValues)
    {
        $jprefix = $this->getAuditApprovalPrefix();
        if (isl_strpos($path, $jprefix) === 0 && !empty($values[$path])) {
            return implode(":", explode('#~#', isl_trim($values[$path], '#~#')));
        } else if ($path == 'AUTOSTATPOST' && !empty($values[$path])) {
            $this->getPostDimenstions($values);
            $out = [];
            foreach ( $values['POSTDIMGRID'] as $index => $entry) {
                $out[] = "Dimension: " . $values['POSTDIMGRID'][$index]['POSTDIMENSIONNAME'] .
                    ", Account: " . $values['POSTDIMGRID'][$index]['POSTDIMENSIONACCT'] .
                    ", Group: " . $values['POSTDIMGRID'][$index]['POSTDIMENSIONGRP'] .
                    ", Description: " . $values['POSTDIMGRID'][$index]['POSTDIMENSIONDESC'] .
                    ", Location: " . $values['POSTDIMGRID'][$index]['POSTDIMENSIONLOC'];

            }
            return implode("\n", $out);
        } else if ($path == 'DIMENSIONSORDER') {
            $uddMap = Pt_DimensionUtil::getDimFieldsMap();
            $orders = explode('#~#', $values[$path]);
            foreach ($orders as $dim) {
                if (strpos($dim, 'custdim_') === 0) {
                    $dimId = substr($dim, 8);
                    if (isset($uddMap[$dimId])) {
                        $outOrders[] = ucfirst(isl_strtolower(isl_substr($uddMap[$dimId], 5)));
                    } else {
                        $outOrders[] = "unknown dim:" . $dim;
                    }
                } else {
                    $outOrders[] = ucfirst($dim);
                }
            }

            /** @noinspection PhpUndefinedVariableInspection */
            return implode(',', $outOrders);
        }
        return parent::transformAuditValue($path, $values, $fullPath, $fullValues);
    }

    /**
     * isProductionCompany - checks whether the company is a production company
     * The functionality was extracted into this function. The function is protected (not private) so that
     *      it can be mocked for a unit test.
     *
     * @return bool true if the company is a production company
     */
    protected function isProductionCompany() {

        $cny = GetMyCompany();
        $tenantType = GetTenantType($cny);
        return ($tenantType == COMPANY_TYPE_PRODUCTION or $tenantType == COMPANY_TYPE_IMAGE);
    }


    /**
     * Check if any transactions exist related to this dimension.  (If so, the dimension can not be turned off.)
     * @param string $key
     * @param [] $dimInfo
     * @return bool
     */
    public static function transactionsExistForDim($key, $dimInfo = [])
    {
        // check if there are already GL transactions with the given dimkey :
        if (in_array($key, CRESetupManager::$disabledDimensions)) {
            $parentDims = [$key];
            if (!isEmptyArray($dimInfo)) {
                $node = $key;
                while (isset($dimInfo[$node]['dependent_dimension_relationship']['parent'])) {
                    array_unshift($parentDims, $dimInfo[$node]['dependent_dimension_relationship']['parent']);
                    $node = $dimInfo[$node]['dependent_dimension_relationship']['parent'];
                }
            } else {
                $parentDims = CRESetupManager::$disabledDimensions;
            }

            foreach ($parentDims as $disabledDimension) {
                $disabledDimension = $disabledDimension . 'dimkey';

                static $transactionSearchCache = [];
                if ($transactionSearchCache == []) {
                    reset($transactionSearchCache);
                }

                if (array_key_exists($disabledDimension, $transactionSearchCache)) {
                    $hasTransaction = $transactionSearchCache[$disabledDimension];
                } else {
                    $hasTransaction = QueryResult(
                        array(
                            "select 1 as exist from dual where exists (select 1 from glentrymst where cny# = :1 and {$disabledDimension} is not null)",
                            GetMyCompany()
                        )
                    );
                    $transactionSearchCache[$disabledDimension] = $hasTransaction;
                }
                if (!empty($hasTransaction) && $hasTransaction[0]['EXIST']) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Get the list of Journal Symbols for which the approvals are enabled
     *
     * @return array
     */
    protected function getCurrentApprovalJournals()
    {
        $stmt = [];
        $existClause = "SELECT 1 FROM GLAPPROVERCONFIG G WHERE G.CNY# = B.CNY# AND G.JOURNALKEY = B.RECORD# AND ROWNUM = 1";
        $stmt[0] = "SELECT SYMBOL FROM BASEJOURNAL B WHERE B.CNY# = :1 AND EXISTS({$existClause})";
        $stmt[1] = GetMyCompany();

        $result = QueryResult($stmt);

        return array_column($result, 'SYMBOL');
    }

    /**
     * Update base journal as approval enabled
     *
     * @param array  $symbols
     *
     * @param string $set
     *
     * @return bool
     */
    protected function updateBaseJournal($symbols, $set = 'T')
    {
        $ok = true;

        if(!empty($symbols)){
            $stmt[0] = "UPDATE BASEJOURNAL SET APPROVAL_ENABLED = '{$set}' WHERE CNY# = :1";
            $stmt[1] = GetMyCompany();
            $stmt = PrepINClauseStmt($stmt, $symbols, ' AND SYMBOL ');
            $ok = $ok && ExecStmt($stmt);
        }

        return $ok;
    }

    /**
     * Update basejournal with the modupref approval config
     *
     * @return bool
     */
    protected function migrateApprovals()
    {
        $existClause = "SELECT 1 FROM GLAPPROVERCONFIG G WHERE G.CNY# = B.CNY# AND G.JOURNALKEY = B.RECORD# AND ROWNUM = 1";
        $inClause = "SELECT RECORD# FROM BASEJOURNAL B WHERE B.CNY# = :1 AND EXISTS({$existClause})";
        $stmt[0] = "UPDATE BASEJOURNAL SET APPROVAL_ENABLED = 'T' WHERE CNY# = :1 AND RECORD# IN ({$inClause})";
        $stmt[1] = GetMyCompany();
        $ok = ExecStmt($stmt);

        return $ok;
    }

    /**
     * Clear basejournal
     *
     * @return bool
     */
    protected function clearBaseJournal()
    {
        $stmt[0] = "UPDATE BASEJOURNAL SET APPROVAL_ENABLED = NULL WHERE CNY# = :1 AND APPROVAL_ENABLED IN ( 'T', 'F' )";
        $stmt[1] = GetMyCompany();

        return ExecStmt($stmt);
    }

    /**
     * Get list of Journal Approvers added from GL Configuration Setup
     *
     * @param string $journalSymbol
     *
     * @return array[]|mixed|null
     */
    public static function getJournalApprovers($journalSymbol = null)
    {
        $locationKey = getMEContextLocation();

        $GlApproverMgr = Globals::$g->gManagerFactory->getManager(
            'glapproverconfig'
        );

        $selectItems = [
            'GLAPPROVERCONFIG.LINENUMBER AS LINENUMBER',
            'GLAPPROVERCONFIG.APPROVALLEVEL AS APPROVALLEVEL',
            'GLAPPROVERCONFIG.JOURNALKEY AS JOURNALKEY',
            "(JOURNAL.SYMBOL || '--' || TRIM (JOURNAL.TITLE)) AS JOURNALNAME",
            'GLAPPROVERCONFIG.RECORD# AS RECORD#',
            'GLAPPROVERCONFIG.LOCATIONKEY AS LOCATIONKEY',
            'GLAPPROVERCONFIG.PRIMARYAPPROVER AS PRIMARYAPPROVER',
            'GLAPPROVERCONFIG.PRIMARYAPPROVERGROUP AS PRIMARYAPPROVERGROUP',
            'GLAPPROVERCONFIG.PRIMARYAPPROVERTYPE AS PRIMARYAPPROVERTYPE',
            'GLAPPROVERCONFIG.ALTERNATEAPPROVER AS ALTERNATEAPPROVER',
            'GLAPPROVERCONFIG.ALTERNATEAPPROVERGROUP AS ALTERNATEAPPROVERGROUP',
            'GLAPPROVERCONFIG.ALTERNATEAPPROVERTYPE AS ALTERNATEAPPROVERTYPE',
            'GLAPPROVERCONFIG.OUTLIERASSISTANT AS OUTLIERASSISTANT',
            'PRIMARY_APPROVER.LOGINID AS PRIMARYAPPROVERLOGINID',
            'PRIMARY_APPROVER.DESCRIPTION AS PRIMARYAPPROVERDESCRIPTION',
            'ALTERNATE_APPROVER.LOGINID AS ALTERNATEAPPROVERLOGINID',
            'ALTERNATE_APPROVER.DESCRIPTION AS ALTERNATEAPPROVERDESCRIPTION',
            'PRIMARY_APPROVER_GROUP.NAME AS PRIMARYAPPROVERUGROUPNAME',
            'ALTERNATE_APPROVER_GROUP.NAME AS ALTERNATEAPPROVERUGROUPNAME',
            "(SELECT LISTAGG(PRIMMEMBERUGROUP.U_O_GKEY, ', ') WITHIN GROUP (ORDER BY PRIMMEMBERUGROUP.U_O_GKEY) "
            . "FROM MEMBERUGROUP PRIMMEMBERUGROUP WHERE CNY#=GLAPPROVERCONFIG.CNY# AND  PRIMMEMBERUGROUP.PARENTGROUP = "
            . "GLAPPROVERCONFIG.PRIMARYAPPROVERGROUP) AS PRIMARYAPPROVERGROUPMEMBERS",
            "(SELECT LISTAGG(ALTMEMBERUGROUP.U_O_GKEY, ', ') WITHIN GROUP (ORDER BY ALTMEMBERUGROUP.U_O_GKEY) "
            . "FROM MEMBERUGROUP ALTMEMBERUGROUP WHERE CNY#=GLAPPROVERCONFIG.CNY# AND "
            . "ALTMEMBERUGROUP.PARENTGROUP = GLAPPROVERCONFIG.ALTERNATEAPPROVERGROUP) AS ALTERNATEAPPROVERGROUPMEMBERS",
        ];

        $filters = [];

        if ($journalSymbol) {
            if (isl_strstr($journalSymbol, '--')) {
                [$journalSymbol] = explode('--', $journalSymbol);
            }
            $journal_id = self::getJournalKeyBySymbol($journalSymbol);

            if ( isNullOrBlank($journal_id) ) {
                global $gErr;
                $msg = "Invalid Journal " . $journalSymbol . " selected";
                $corr = "Pick a valid active Journal.";
                $gErr->addIAError('GL-0973', __FILE__ . ':' . __LINE__, $msg, ['JOURNAL' => $journalSymbol], '', [], $corr, []);
                return false;
            }

            $filters[] = [
                'GLAPPROVERCONFIG.JOURNALKEY', '=', $journal_id,
            ];
        } else {
            if ($locationKey) {
                $filters[] = [
                    'GLAPPROVERCONFIG.LOCATIONKEY', '=', $locationKey,
                ];
            } else {
                $filters[] = ['GLAPPROVERCONFIG.LOCATIONKEY', 'IS NULL'];
            }
        }

        $queryParameters = [
            'selects' => $selectItems,
            'filters' => [$filters],
            'orders'  => [['GLAPPROVERCONFIG.LINENUMBER', 'ASC'],],
        ];

        $approvalConfigData = $GlApproverMgr->GetList($queryParameters);

        // In a child entity, first look for the journal in the me-specific settings and only if
        // we don't find one continue on to the root config.
        if (count($approvalConfigData)) {
            $formattedApprovalData = null;
            foreach ($approvalConfigData as $approvalData)
            {
                $index = !empty($approvalData['LOCATIONKEY'])
                    ? $approvalData['LOCATIONKEY'] : 0;
                $formattedApprovalData[$index][] = $approvalData;
            }
            //If entity has the approval setup for the journal, then pick it up else pick up the root level setup
            if (isset($formattedApprovalData[$locationKey])) {
                $approvalConfigData = $formattedApprovalData[$locationKey];
            } else {
                $approvalConfigData = $formattedApprovalData[0] ?? null;
            }
        }

        return $approvalConfigData;
    }

    /**
     * Construct the approvers based on their type
     *
     * @param array $approverData
     * @param string $approverStage
     * @param string $approverType
     *
     * @return array
     */
    private static function constructApproverList($approverData, $approverStage, $approverType)
    {
        switch ($approverType) {
        case 'EMA':
            $approver = [
                'id'                 => null,
                'type'               => $approverData["{$approverStage}APPROVERTYPE"],
                'name'               => null,
                'approver_stage'     => $approverStage,
                'approval_type_name' => APPTYPE_EMPLMNGR_LEVEL,
            ];
            break;
        case 'U':
            $approver = [
                'id'                 => $approverData["{$approverStage}APPROVER"],
                'type'               => $approverData["{$approverStage}APPROVERTYPE"],
                'name'               => $approverData["{$approverStage}APPROVERLOGINID"],
                'approver_stage'     => $approverStage,
                'approval_type_name' => APPTYPE_USER_LEVEL,
            ];
            break;
        case 'UG':
            $approver = [
                'id'                 => $approverData["{$approverStage}APPROVERGROUP"],
                'type'               => $approverData["{$approverStage}APPROVERTYPE"],
                'name'               => $approverData["{$approverStage}APPROVERUGROUPNAME"],
                'group_members'      => $approverData["{$approverStage}APPROVERGROUPMEMBERS"],
                'approver_stage'     => $approverStage,
                'approval_type_name' => APPTYPE_USER_GROUP_LEVEL,
            ];
            break;
        default:
            $approver = [];
            break;
        }

        return $approver;
    }

    /**
     * Collect All Journal Approvers Configured for GL
     *
     * @param array $value
     */
    public function collectAllJournalApprovers(&$value){
        $approvers = self::getJournalApprovers();
        $value['Journal_Approvers'] = $approvers;
    }

    /**
     * Collect All Glod Configuration
     *
     * @param array $value
     */
    public function collectAllGlodConfig(&$value)
    {
        if (!GLODInteractions::isGlodEnabled()) {
            return;
        }
        $glodConfigMgr = Globals::$g->gManagerFactory->getManager('glodconfig');
        $value['GLOD_CONFIG_GRID'] = $glodConfigMgr->getActiveConfiguration();
    }

    /**
     * delete all configuration that match the location for GL module
     *
     * @param false $deletaAll
     *
     * @return bool
     */
    function deleteGLApproverConfig($deletaAll = false)
    {
        $source = "GLSetupManager::deleteGLApproverConfig";
        $stmt = [];

        $stmt[0] = "DELETE glapproverconfig where cny# = :1";
        $stmt[1] = GetMyCompany();

        if($deletaAll == false) {
            if (IsMultiEntityCompany() && GetContextLocation()) {
                $stmt[0] .= " and locationkey = :2 ";
                $stmt[2] = GetContextLocation();
            } else {
                $stmt[0] .= " and locationkey is null ";
            }
        }

        $ok = $this->_QM->beginTrx($source);
        if ($ok) {
            $ok = ExecStmt($stmt);
            $ok = $ok && $this->_QM->commitTrx($source);
            if (!$ok) {
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * Prepare approval data to be added
     *
     * @param array $approver
     * @param int $counter
     *
     * @return array
     */
    protected function prepareApproverListToInsert($approver, $counter)
    {
        //TODO: Refactor this method to collect and process all the data at the same time
        $journal_id = NULL;
        $locationkey = GetContextLocation();
        $line_number = $counter + 1;
        $primary_approver = NULL;
        $primary_approver_group = NULL;
        $primary_approver_type = NULL;
        $alternate_approver = NULL;
        $alternate_approver_group = NULL;
        $alternate_approver_type = NULL;

        if(isset($approver['JOURNAL'])) {
            list($journalSymbol) = explode('--', $approver['JOURNAL']);
            $journal_id = self::getJournalKeyBySymbol($journalSymbol);
        }
        if(isset($approver['USER']) && $approver['USER'] != '') {
            if((DBTokensHandler::getInstance()->getInternalKey($approver['USER'])) == APPTYPE_EMPLMNGR_LEVEL) {
                $primary_approver_type = 'EMA';
            }
            else {
                $objUserManager = Globals::$g->gManagerFactory->getManager('userinfo');
                list($login, $desc) = explode('--', $approver['USER']);
                if($login != '' && $desc == 'UserGroup') {
                    $grp_em = new UserGroupManager();
                    $r = $grp_em->DoQuery('QRY_USERGROUP_SELECT_BYNAME', array($login));
                    $primary_approver_group = $r[0]['RECORD#'];
                    $primary_approver_type = 'UG';
                } else if($login != '') {
                    $primary_approver = $objUserManager->getUserRecord($login);
                    $primary_approver_type = 'U';
                }
            }
        }

        if(isset($approver['USER2'])  && $approver['USER2'] != '') {
            if((DBTokensHandler::getInstance()->getInternalKey($approver['USER2'])) == APPTYPE_EMPLMNGR_LEVEL) {
                $alternate_approver_type = 'EMA';
            }
            else {
                $objUserManager = Globals::$g->gManagerFactory->getManager('userinfo');
                list($login, $desc) = explode('--', $approver['USER2']);
                if($login != '' && $desc == 'UserGroup') {
                    $grp_em = new UserGroupManager();
                    $r = $grp_em->DoQuery('QRY_USERGROUP_SELECT_BYNAME', array($login));
                    $alternate_approver_group = $r[0]['RECORD#'];
                    $alternate_approver_type = 'UG';
                } else {
                    $alternate_approver = $objUserManager->getUserRecord($login);
                    $alternate_approver_type = 'U';
                }
            }
        }
        $approver['OUTLIERASSISTANT'] = $approver['OUTLIERASSISTANT'] == 'T' || $approver['OUTLIERASSISTANT'] == 'true' ?
            'true' : 'false';

        $approverValues = array(
            'JOURNALKEY'=> $journal_id,
            'LINENUMBER' => $line_number,
            'LOCATIONKEY' => $locationkey,
            'APPROVALLEVEL' => $line_number,
            'PRIMARYAPPROVER' => $primary_approver,
            'PRIMARYAPPROVERGROUP' => $primary_approver_group,
            'PRIMARYAPPROVERTYPE' => $primary_approver_type,
            'ALTERNATEAPPROVER' => $alternate_approver,
            'ALTERNATEAPPROVERGROUP' => $alternate_approver_group,
            'ALTERNATEAPPROVERTYPE' =>$alternate_approver_type,
            'OUTLIERASSISTANT' => $approver['OUTLIERASSISTANT']
        );

        return $approverValues;
    }

    /**
     * Construct the approvers based on their type
     *
     * @param array $approverData
     * @param string $approverStage
     * @param string $approverType
     *
     * @return string|null
     */
    private static function getApproversToListInView($approverData, $approverStage, $approverType)
    {
        switch ($approverType) {
        case 'EMA':
            $approver = DBTokensHandler::getInstance()->getExternalLabel(APPTYPE_EMPLMNGR_LEVEL);
            break;
        case 'U':
            $approverDesc = '';
            if(!empty($approverData["{$approverStage}APPROVERDESCRIPTION"])){
                $approverDesc = '--'.$approverData["{$approverStage}APPROVERDESCRIPTION"];
            }
            $approver = $approverData["{$approverStage}APPROVERLOGINID"].$approverDesc;
            break;
        case 'UG':
            $approver = $approverData["{$approverStage}APPROVERUGROUPNAME"].'--UserGroup';
            //$approver = $approverData["{$approverStage}APPROVERUGROUPNAME"].'--'.$approverData["{$approverStage}APPROVERUGROUPDESC"];
            break;
        default:
            $approver = null;
            break;
        }

        return $approver;
    }

    /**
     * Format Approval Setup Data
     *
     * @param array $approvers
     *
     * @return array
     */
    public static function formatJournalApproverDataToList($approvers) {
        $result = [];
        foreach ($approvers as $arrApprover) {
            if ($arrApprover['LOCATIONKEY'] == GetContextLocation()) {
                $entry = array();
                $entry['JOURNAL'] = $arrApprover['JOURNALNAME'];
                $entry['OUTLIERASSISTANT'] = $arrApprover['OUTLIERASSISTANT'];
                $entry['USER'] = self::getApproversToListInView($arrApprover, 'PRIMARY', $arrApprover['PRIMARYAPPROVERTYPE']);
                $entry['USER2'] = self::getApproversToListInView($arrApprover, 'ALTERNATE', $arrApprover['ALTERNATEAPPROVERTYPE']);
                $result[] = $entry;
            }
        }
        return $result;
    }

    /**
     * Vaidate Approval Setup when user submits the JE
     *
     * @param string $journalSymbol
     *
     * @return bool
     */
    public static function validateApprovalOnJESubmit($journalSymbol) {
        $glModPrefs = GetModulePreferences(Globals::$g->kGLid, $glPrefs);
        $approvalSetup = self::getJournalApprovers($journalSymbol);
        $glModPrefs['JOURNAL_GRID'] = self::formatJournalApproverDataToList($approvalSetup);
        $inEntity = getMEContextLocation() !== false;
        $isValid = (new self())->validateApprovalPreferences($glModPrefs,$inEntity);
        return $isValid;
    }

    /**
     * Check current user is a valid approver
     *
     * @return bool
     */
    public static function isUserAValidApprover() {
        $currentUserLoginId = GetMyLogin();
        //  Get the list of authorized users
        $validApprovers = self::getAuthorizedGLBatchApprovers();

        return isset($validApprovers[$currentUserLoginId]);
    }

    /**
     * Get Journal Record# using Journal symbol
     *
     * @param string $journalSymbol
     *
     * @return mixed|null
     */
    public static function getJournalKeyBySymbol($journalSymbol) {
        $objOpallJournalPickMgr = Globals::$g->gManagerFactory->getManager('opalljournalpick');
        $filter = array(
            'selects' => array('RECORD#'),
            'filters' => array(array(array('SYMBOL', '=', $journalSymbol))),
        );
        $journalRec = $objOpallJournalPickMgr->GetList($filter);
        $journal_id = $journalRec[0]['RECORD#'] ?? null;

        return $journal_id;
    }

    /**
     * check to see if approval and delegation is enabled in GL config
     *
     * @return bool true if approvals and delegation are both enabled, false otherwise
     */
    public function isApprovalDelegationEnabled()
    {
        $prefs = [];
        if (isset($this->_moduleKey) && $this->_moduleKey != '') {
            GetModulePreferences($this->_moduleKey, $prefs);
        }
        // first check if PO approval is enabled
        $enableGLAppprovals = $prefs['JEAPPROVAL_ENABLE'];
        if (!$enableGLAppprovals || $enableGLAppprovals == 'false') {
            return false;
        } else {
            $enableAppprovalDelegation = $prefs['ENABLEDELEGATION'] ?? null;
            if (!$enableAppprovalDelegation || $enableAppprovalDelegation == 'F') {
                return false;
            } else {
                return true;
            }
        }
    }

    /**
     * qrequest to get Email notification Notify list
     *
     * @return string
     */
    public function getAllJournalApprovalNotifiers()
    {
        //Fetch GL Approval Email Notification Setup
        $GlApproverEmailConfigMgr = Globals::$g->gManagerFactory->getManager(
            'glapproveremailconfig'
        );
        $GlApproverEmailConfigMgr->collectEmailNotificationData($emailNotificationSetup);
        $journalKeys = array_column($emailNotificationSetup['EMAILNOTIFICATIONLIST'], 'JOURNALKEY');

        $emailNotificationGrid = [];
        if ($journalKeys && $journalKeys != '') {
            $locationKey = GetContextLocation();
            $locationFilter = $locationKey ? 'LOCATIONKEY ='.
                $locationKey : 'LOCATIONKEY IS NULL';

            $stmt = array();
            $stmt[0] = "SELECT cny#, journalkey, COUNT(*) as approvalLevel," .
                " LISTAGG(primaryapprovergroup, ', ') as usergroupkey" .
                " FROM" .
                " glapproverconfig" .
                " WHERE" .
                " cny# = :1";
            $stmt[1] = GetMyCompany();
            $stmt = PrepINClauseStmt($stmt, $journalKeys, " AND journalkey ");
            $stmt[0] .= " AND ".$locationFilter." GROUP BY cny#, journalkey HAVING count(*) = 1 AND LISTAGG(primaryapprovergroup, ', ') IS NOT NULL";

            $result = QueryResult($stmt);

            $notifiersList = [];
            if ($result) {
                foreach ($result as $item) {
                    $notifyPickerData = ['None','Approver'];
                    $groupMgr = Globals::$g->gManagerFactory->getManager('usergroup');
                    $groupName = $groupMgr->GetGrpName($item['USERGROUPKEY']);

                    $grpMemMgr = Globals::$g->gManagerFactory->getManager('usergroupmembers');
                    $grpMembers = $grpMemMgr->getValidApproversFromGroup($item['USERGROUPKEY']);


                    if($groupName && $grpMembers) {
                        foreach ($grpMembers as $grpMemLoginId => $grpMemberRec) {
                            $notifyPickerData[] = $groupName.'--'.$grpMemLoginId;
                        }
                    }
                    $notifiersList[$item['JOURNALKEY']] = $notifyPickerData;
                }
            }

            foreach ($emailNotificationSetup['EMAILNOTIFICATIONLIST'] as $rows) {
                if(array_key_exists($rows['JOURNALKEY'], $notifiersList) ){
                    $emailNotificationGrid[$rows['JOURNAL']]['validValues'] = $notifiersList[$rows['JOURNALKEY']];
                    $emailNotificationGrid[$rows['JOURNAL']]['selectedVal'] = $rows['NOTIFY'];
                }
            }
        }

        return json_encode($emailNotificationGrid);
    }

    /**
     * qrequest to get Email notification Notify list
     *
     * @param array $params qrequest parameters
     *
     * @return string
     */
    public function getJournalApprovalNotifiers($params)
    {
        $journalSymbol = $params['approverGroupName'];
        $notifyPickerData = ['None','Approver'];
        if ($journalSymbol && $journalSymbol != '') {
            if(!empty($params['approverGroupName'])) {
                $groupName = $params['approverGroupName'];
                $groupMemMgr = Globals::$g->gManagerFactory->getManager('usergroupmembers');
                $groupRecordId = $groupMemMgr->getUserGroupIdByName($params['approverGroupName']);

                $this->getApproverGroupMembers($notifyPickerData, $groupName, $groupRecordId);
            }
            else {
                $journalKey = self::getJournalKeyBySymbol($journalSymbol);

                $sql = "SELECT cny#, journalkey, COUNT(*)," .
                    " LISTAGG(primaryapprovergroup, ', ') as usergroupkey" .
                    " FROM" .
                    " glapproverconfig" .
                    " WHERE" .
                    " cny# = :1 AND journalkey = :2 AND locationkey IS NULL" .
                    " GROUP BY cny#, journalkey" .
                    " HAVING COUNT(*) = 1";

                $result = QueryResult(
                    [
                        $sql, GetMyCompany(), $journalKey
                    ]
                );

                if ($result) {
                    $groupMgr = Globals::$g->gManagerFactory->getManager(
                        'usergroup'
                    );
                    $groupName = $groupMgr->GetGrpName(
                        $result[0]['USERGROUPKEY']
                    );

                    $this->getApproverGroupMembers($notifyPickerData, $groupName, $result[0]['USERGROUPKEY']);
                }
            }
        }

        // prepare xml
        /*$xml = '<EntityOpen>';
        $xml .= '<Approvers>';
        foreach ($notifyPickerData as $notify) {
        $xml .= "<Approver label=\"" . htmlspecialchars($notify)
            . "\"/>";
        }
        $xml .= '</Approvers>';
        $xml .= '</EntityOpen>';

        return $xml;*/
        return json_encode($notifyPickerData);
    }

    /**
     * @param array $notifyPickerData
     * @param string $groupName
     * @param int $groupRecordId
     */
    private function getApproverGroupMembers(&$notifyPickerData, $groupName, $groupRecordId) {
        $grpMemMgr = Globals::$g->gManagerFactory->getManager('usergroupmembers');
        $grpMembers = $grpMemMgr->getValidApproversFromGroup($groupRecordId);

        if($groupName && $grpMembers) {
            foreach ($grpMembers as $grpMemLoginId => $grpMemberRec) {
                $notifyPickerData[] = $groupName.'--'.$grpMemLoginId;
            }
        }
    }

    /**
     * GET OTHER DEFAULT BOOKS PROPERTY FROM MODULEPREF
     *
     *  @return string
     */

    public function getDefaultBooksSetting(){
        $property = "OTHERDEFAULTBOOKS";
        return GetPreferenceForProperty(Globals::$g->kGLid, $property);
    }

}
