<?php

/**
 * Entity file for Close Books
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */
/**
 * Entity file
 */
$kSchemas['closebooks'] = array(
    'object' => array(
        'RECORDN<PERSON>',
        'STARTOP<PERSON>'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'STARTOPEN' => 'startopen',
    ),
    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'desc' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'format' => $gRecordNoFormat
            ),
            'id' => 1
        ),
        array(
            'path' => 'OPENDATE',
            'fullname' => 'IA.CLOSE_COMPANY_BOOKS_FROM',
            'desc' => 'IA.DATE',
            'readonly' => true,
            'type' => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat,
                'maxlength' => 10
            ),
            'id' => 2
        ),
        array(
            'path' => 'ENTITYID',
            'fullname' => 'IA.ENTITY_ENTITY_GROUP',
            'desc' => 'IA.ENTITY_ENTITY_GROUP',
            'hidden' => true,
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'pick_url' => 'picker.phtml?.showonlyentity=1',
                'entity' => 'gllocationfilterpick',
                'showonlyentity' => true,
            ),
            'showaudit' => true,
            'id' => 3
        ),
        array(
            'path' => 'PERIOD',
            'fullname' => 'IA.TO_THE_END_OF_PERIOD',
            'desc' => 'IA.TO_THE_END_OF_PERIOD',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'id' => 4
        ),
        array(
            'path' => 'OFFLINE',
            'fullname' => 'IA.PROCESS_OFFLINE',
            'desc' => 'IA.OPEN_ACCOUNTS_PAYABLE',            
            'type' => $gBooleanType,
            'id' => 5
        ),        
        array(
            'path' => 'ENTITYLIST',
            'fullname' => 'IA.ENTITIES_CLOSED',
            'desc' => 'IA.ENTITIES_CLOSED',
            'hidden' => true,
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
            ),
            'showaudit' => true,
            'id' => 6
        ),
        array(
            'path' => 'PERIODRECNO',
            'fullname' => 'IA.PERIOD_KEY',
            'desc' => 'IA.PERIOD_KEY',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'id' => 7
        ),
    ),
    'table' => 'location',
    'printas' => 'IA.CLOSE_BOOK_LOG',
    'pluralprintas' => 'IA.CLOSE_BOOK_LOGS',
    'vid' => 'RECORDNO',
    'module' => 'gl',
    'api' => array(
        'PERMISSION_MODULES' => array('gl'),
        'PERMISSION_CREATE' => 'lists/closebooks/edit',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ),
    'nameFields' => [ 'ENTITYID', 'PERIOD' ],
    'platformProperties' => [
        // PRR needs this setting. If you intend to change it, please confirm it first with someone from the Platform
        // team
        SOBJECT_CAN_HAVE_RELATIONSHIPS => false,
    ],

);

