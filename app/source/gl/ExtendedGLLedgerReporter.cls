<?php
/**
 *  FILE: ExtendedGLLedgerReporter.cls
 *
 *  @description extends the base class for the general ledger report
 *
 *  <AUTHOR> <<EMAIL>>
 *  @copyright 2012 Intacct Corporation, All Rights Reserved
 */
import('GLLedgerReporter');
/**
* ExtendedGLLedgerReporter
*/
class ExtendedGLLedgerReporter extends GLLedgerReporter
{
    /**
    *  All Possible Cash Books
    *
    *  @access Public
    */
    public $reportBooks = array('TAXCASH', 'GAAPCASH', 'GAAPTAXCASH', 'CASH');

    /**
     * @param array $_params the array to quote
     */
    function __construct($_params)
    {
        parent::__construct($_params);
    }

    /**
     *
     */
    protected function setPREntryQuery()
    {
        if (in_array($this->params['reportingBook'], $this->reportBooks) && $this->params['SHOWBILLLINES'] == 'true' && !empty($this->_gSubTranscationRecs) ) {
            $prentryargs = array();
            //Collect all values of PRENTRYRECS from the Result Set so that it will fetch those only
            $prentryrecs = array();
            foreach( $this->_gSubTranscationRecs as $entries){
                foreach($entries as $entry){
                    $prentryrecs[] = $entry['PRENTRYREC'];
                }
            }
            $prentryargs[0] = "select
                            prentrypymtrecs.payitemkey as prentryrec, prrecord.recordid 
                            from  prentrypymtrecs, prrecordmst prrecord where 
                            prentrypymtrecs.cny# = :1 and 
                            prentrypymtrecs.cny# = prrecord.cny# and 
                            prentrypymtrecs.recordkey = prrecord.record# and 
                            prrecord.recordtype IN('pi', 'pa', 'ri', 'ra', 'pd', 'rd', 'cq')";
                            
            $prentryargs[1] = GetMyCompany();
            $prentryargs[2] = 'T';

            $prentryargs = PrepINClauseStmt($prentryargs, array_unique($prentryrecs), " and prentrypymtrecs.payitemkey ");

            $queryresult = QueryResult($prentryargs);
            for ($i = 0; $i < count($queryresult); $i++) {
                $billoffsets[$queryresult[$i]["PRENTRYREC"]] = $queryresult[$i];
            }

            $this->_gTranscationOffsets =& $billoffsets;
        }
    }
    
     /**
 * Set setBillLines 
     *
     * @access Protected 
     */
    protected function setBillLines()
    {
        if (in_array($this->params['reportingBook'], $this->reportBooks) && $this->params['SHOWBILLLINES'] == 'true') {          
            $this->selforbilllines = ", decode(gl_info.cbprentrykey, NULL, gl_info.prentrykey, gl_info.cbprentrykey) 
            as prentryrec ";
            $this->grpforbilllines = ", decode(gl_info.cbprentrykey, NULL, gl_info.prentrykey, gl_info.cbprentrykey) ";
        }
    }

    /**
     * appendBillinfo
     * @param array $modRecords
     * @param int $y
     *
     * @return string
     */
    protected function appendBillinfo($modRecords, $y)
    {
        if (in_array($this->params['reportingBook'], $this->reportBooks) && $this->params['SHOWBILLLINES'] == 'true') {
            $appendbillinfo = "";

            if ($modRecords[$y]['BILLNUM']) {
                $appendbillinfo = " :: " . $modRecords[$y]['BILLNUM'];
            }
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $appendbillinfo;
    }

    /**
     * setGSubTranscationRecs
     *
     * @param array  $gTransactionOffsets
     * @param array  $entry
     */
    protected function setGSubTranscationRecs($gTransactionOffsets, $entry)
    {
        if (in_array($this->params['reportingBook'], $this->reportBooks) && $this->params['SHOWBILLLINES'] == 'true' && !empty($this->_gSubTranscationRecs[$entry['RECORD#']])) {
            foreach($this->_gSubTranscationRecs[$entry['RECORD#']] as $key => &$prentry){
                if ($prentry['PRENTRYREC'] != '' && $gTransactionOffsets[$prentry['PRENTRYREC']]) {
                    $this->_gSubTranscationRecs[$entry['RECORD#']][$key]['BILLNUM'] = $gTransactionOffsets[$prentry['PRENTRYREC']]['RECORDID'];
                }
            }            
        }
    }
    
}

