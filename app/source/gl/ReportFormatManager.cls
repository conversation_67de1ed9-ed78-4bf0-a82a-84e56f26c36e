<?
/**
 *    FILE:            ReportFormatManager.cls
 *    AUTHOR:            rs
 *    DESCRIPTION:    manager for gl reportformat object
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

class ReportFormatManager extends EntityManager
{
    /**
     * @return bool
     */
    public function IsAuditEnabled()
    {
        return false;
    }

    /**
     * @param string $verb   action on the entity (Set, Delete, Add, ...)
     * @param string $key    vid of the entity action is being perf ormed on
     * @param mixed  $param1 useless hack because people do not know how to declare a function properly
     * @param mixed  $param2 useless hack because people do not know how to declare a function properly
     * @param array  $values the object data
     *
     * @param bool   $fastUpdate
     *
     * @return bool
     */
    public function DoEvent($verb, $key, $param1 = null, $param2 = null, $values = [], $fastUpdate = false)
    {
        return true;
    }
}

