<?
/**
 *    FILE:            gltrialbalance.rpt
 *    AUTHOR:            <PERSON>
 *    DESCRIPTION:    rpt file for Trial Balance
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */


$currentPeriod = GlbudgettypeManager::getCurrentReportingReport();
$reportingPeriods = GetPeriodMap(true, '', true);

// 'Include Tax Return Code' filter
$includeTaxCode = array(
    'fullname' => '',
    'type' => array(
        'ptype' => 'empty',
        'type' => 'empty',
    ),
    'hidden' => 'true'
);
if (IsTaxCodeEnabled()) {
    $includeTaxCode = array(
        'fullname' => "IA.INCLUDE_TAX_CODES",
        'desc' => "IA.INCLUDE_TAX_CODES",
        'type' => array(
            'ptype' => 'boolean',
            'type' => 'boolean',
        ),
        'path' => 'INCLUDETAXCODE',
        'group_separator' => true,
    );
}

// 'Exclude Adjusting Entries Code' filter

$excludeAdjEntries = array(
    'fullname' => "IA.EXCLUDE_ADJUSTING_ENTRIES",
    'desc' => "IA.EXCLUDE_ADJUSTING_ENTRIES",
    'type' => array(
        'ptype' => 'boolean',
        'type' => 'boolean',
    ),
    'path' => 'excludeadjentries',
    'default' => false,
    'hidden' => !JournalManager::IsAdjJournalsEnabled()
);


$kSchemas["gltrialbalance"] = array(
    'schema' => array(
        array()
    ),
    'individualreport' => array(
        'loc' => array('default' => false),
        'dept' => array('default' => false),
    ),
    'promptonrun' => array(
        'loc' => array('default' => false),
        'dept' => array('default' => false),
        'REPORTINGTIMEPERIODFIELDS' => array('default' => false),
    ),
    'fieldgroup' => array(
        'REPORTINGTIMEPERIODFIELDS' => array(
            'layout' => 'landscape',
            'fields' => array(
                array(
                    'fullname' => 'IA.REPORTING_PERIOD',
                    'type' => array(
                        'type' => 'enum',
                        'ptype' => 'enum',
                        'validlabels' => array_values($reportingPeriods),
                        'validvalues' => array_keys($reportingPeriods),
                    ),
                    'path' => 'rp',
                    'default' => $currentPeriod,
                    'onchange' => "UpdateAsOfDate(document.main.elements['_obj__asofdate'], document.main.elements['helper__obj__rp'], document.main.elements['helper__por__REPORTINGTIMEPERIODFIELDS'], document.main.elements['_por__REPORTINGTIMEPERIODFIELDS'], 'fld_obj__asofdate', true);"
                ),
                array(
                    'fullname' => 'IA.AS_OF_DATE',
                    'type' => array(
                        'type' => 'date',
                        'ptype' => 'date',
                        'maxlength' => 10,
                        'size' => '10',
                    ),
                    'path' => 'asofdate',
                    'default' => GetCurrentDate(),
                ),
            ),
        ),
        'BALANCEDATE' => array(
            'layout' => 'landscape',
            'fields' => array(
                array(
                    'fullname' => 'IA.OPENING_BALANCE_DATE',
                    'type' => array(
                        'type' => 'date',
                        'ptype' => 'date',
                        'maxlength' => 10,
                        'size' => '10',
                    ),
                    'path' => 'startdate',
                ),
                array(
                    'fullname' => 'IA.CLOSING_BALANCE_DATE',
                    'type' => array(
                        'type' => 'date',
                        'ptype' => 'date',
                        'maxlength' => 10,
                        'size' => '10',
                    ),
                    'path' => 'enddate',
                ),
            )
        ),
        'ZEROBALSTATACCTYTD' => array(
            'fields' => array(
                array(
                    'fullname' => 'IA.SHOW_ZERO_BALANCE_ACCOUNTS',
                    'type' => array(
                        'ptype' => 'radio',
                        'type' => 'radio',
                        'validlabels' => array('IA.ALL', 'IA.ONLY_WITH_ACTIVITY', 'IA.DO_NOT_SHOW'),
                        'validvalues' => array('A', 'W', 'N'),
                    ),
                    'path' => 'zeroBalance',
                    'default' => 'N',
                    'onchange_js' => "if(this.value=='A'){this.form.elements['_obj__allAccts'].value='Y'}else{this.form.elements['_obj__allAccts'].value='N'};if(this.value=='W'){this.form.elements['_obj__zeroAcctsWTrans'].value='Y'}else{this.form.elements['_obj__zeroAcctsWTrans'].value='N'};",
                ),
                array(
                    'fullname' => 'IA.SHOW_ACCOUNTS',
                    'type' => array(
                        'ptype' => 'radio',
                        'type' => 'radio',
                        'validlabels' => array('IA.INCLUDING_STATISTICAL_ACCOUNTS','IA.ONLY_SHOWING_STATISTICAL_ACCOUNTS','IA.DO_NOT_SHOW_STATISTICAL_ACCOUNTS'),
                        'validvalues' => array('I', 'O', 'E'),
                    ),
                    'path' => 'statAccts',
                    'default' => 'E',
                ),
                array(
                    'fullname' => 'IA.RETAIN_YTD_BALANCES',
                    'type' => array(
                        'ptype' => 'boolean',
                        'type' => 'boolean',
                    ),
                    'path' => 'ytdbal',
                ),
                array(
                        'fullname' => 'IA.SHOW_OPEN_CLOSE_DEBIT_CREDIT',
                        'type' => array(
                            'ptype' => 'boolean',
                            'type' => 'boolean',
                        ),
                        'path' => 'debitcreditbalance',
                    ),
            ))
    ),
    'fieldinfo' => array(
        'userprefs' => true,
        'lines' => array(
            array(
                'title' => 'IA.TIME_PERIOD',
                'fields' => array(
                    array(
                        'fullname' => 'IA.PERIOD',
                        'type' => array('type' => 'fieldgroup'),
                        'path' => 'REPORTINGTIMEPERIODFIELDS',
                    ),
                    $gOrLabel,
                    array(
                        'fullname' => 'IA.BALANCE_DATE',
                        'type' => array('type' => 'fieldgroup'),
                        'path' => 'BALANCEDATE',
                    ),
                )
            ),
            array(
                'title' => 'IA.FILTERS',
                'fields' => array(
                    array(
                        'fullname' => '',
                        'type' => array(
                            'ptype' => 'text',
                            'type' => 'text',
                        ),
                        'path' => 'allAccts',
                        'hidden' => true,
                        'post' => true,
                    ),
                    array(
                        'fullname' => '',
                        'type' => array(
                            'ptype' => 'text',
                            'type' => 'text',
                        ),
                        'path' => 'zeroAcctsWTrans',
                        'hidden' => true,
                        'post' => true,
                    ),
                ),
            ),
            array(
                'title' => 'IA.FORMAT',
                'fields' => array(
                    $includeTaxCode,
                    $excludeAdjEntries,
                    array(
                        'fullname' => '',
                        'type' => array('type' => 'fieldgroup'),
                        'path' => 'ZEROBALSTATACCTYTD',
                        'group_separator' => true,
                    ),
                    array(
                        'fullname' => 'IA.PRINT_PAGE_OPTIONS',
                        '_func' => 'FieldSetLayout',
                        'title' => '',
                        'columns' => array(
                            array(
                                'fullname' => 'IA.PAGE_ORIENTATION',
                                'type' => array(
                                    'ptype' => 'radio',
                                    'type' => 'radio',
                                    'validlabels' => array('IA.PORTRAIT', 'IA.LANDSCAPE'),
                                    'validvalues' => array('P', 'L'),
                                ),
                                'path' => 'orientation',
                                'default' => 'P',
                            ),
                        )
                    )
                ),
            )
        )
    ),
    'controls' => array(
        kShowHTML,
        kShowPDF,
        kShowExcel,
        kShowCSV,
        kShowText,
        kShowBackground,
        kEmail,
        kAddtoDashboard,
        kMemorize,
    ),
    'addDimFilterIndex' => 1,
    'layout' => 'frame',
    'layoutproperties' => $gInvRptLayoutProperties,
    'module' => 'gl',
    'printas' => 'IA.TRIAL_BALANCE_REPORT',
    'helpfile' => 'Running_Trial_Balance_Reports',
    'onloadjs' => "initShowDimensions(); ",
    'afterfunction' => 'callThis',
    'reportingAccounts' => true,
);

