<?
//===========================================================================
//	FILE:			GLRestrictionReleaseSourceManager.cls
//	AUTHOR:			<PERSON><PERSON><PERSON><PERSON>
//	DESCRIPTION:
//
//	(C) 2000, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	Corporation and is protected by the copyright laws.  Information
//	herein may not be used, copied or disclosed in whole or in part
//	without prior written consent from Intacct Corporation.
//===========================================================================

/**
 * Class GLRestrictionReleaseRestrictedDimManager
 */
class GLRestrictionReleaseRestrictedDimManager extends OwnedObjectManager
{
    /**
     * __construct
     *
     * @param array $params entitymanager param
     */
    function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * @param array $values
     * @return bool
     */
    public function regularAdd(&$values)
    {
        return false;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function regularSet(&$values)
    {
        return false;
    }

    /**
     * @param int|string $values
     *
     * @return bool
     */
    public function Delete($values)
    {
        return false;
    }
}
