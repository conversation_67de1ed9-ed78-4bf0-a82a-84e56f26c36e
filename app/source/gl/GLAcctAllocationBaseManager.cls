<?
//===========================================================================
//	FILE:			GLAcctAllocationBaseManager.cls
//	AUTHOR:			<PERSON><PERSON><PERSON><PERSON>
//	DESCRIPTION:
//
//	(C) 2000, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	Corporation and is protected by the copyright laws.  Information
//	herein may not be used, copied or disclosed in whole or in part
//	without prior written consent from Intacct Corporation.
//===========================================================================

/**
 * Class GLAcctAllocationBaseManager
 */
class GLAcctAllocationBaseManager extends EntityManager
{

    CONST PRESERVED = 'Preserve values';
    CONST NOT_INVITED = 'Not considered';
    CONST ALONG_FOR_RIDE = 'Allocation focus';
    CONST PER_DIMENSION_VALUE = 'Per dimension value';
    CONST TRUEUP_DELTA = 'Activity delta';
    CONST TRUEUP_AUTOREVERSE = 'Auto-reverse prior post';
    CONST TRUEUP_NONE = 'None';
    CONST ALLOWALLOCATION_WITHINENTITY = 'Within one entity';
    CONST ALLOWALLOCATION_ACROSSENTITIES = 'Across entities';
    CONST ALLOWALLOCATION_WITHINENTITY_INTERNAL = 'W';
    CONST ALLOWALLOCATION_ACROSSENTITIES_INTERNAL = 'A';

    CONST TRUEUP_AUTOREVERSE_INTERNAL = 'T';
    CONST PRESERVED_INTERNAL = 'P';
    CONST NOT_INVITED_INTERNAL = 'N';
    CONST ALONG_FOR_RIDE_INTERNAL = 'A';
    CONST PER_DIMENSION_VALUE_INTERNAL = 'D';
    CONST INCEPTION_TO_DATE = 'Inception To Date';
    CONST INCEPTION_TO_CURRENT_MONTH = 'Inception To Current Month';
    CONST FOCUSPROJECT = 'FOCUSPROJECT';
    CONST BOOKTYPE_USRDEF = 'User Defined';
    CONST ALTERNATE_BOOKS_ONLY = 'Alternate books only';
    CONST MAINANDALTERNATEBOOKS = 'Main reporting book and alternate books';
    CONST SKIP = 'Skip and continue if a group in the sequence fails';
    CONST STOP = 'Stop if a group member in the sequence fails';

    CONST INTERNAL_TYPE_DYNAMIC_ALLOC = 'DA';
    CONST INTERNAL_TYPE_RESTRICTION_RELEASE = 'RR';

    CONST TYPE_DYNAMIC_ALLOC = 'Dynamic Allocation';
    CONST TYPE_RESTRICTION_RELEASE = 'Restriction Release';

    const STAT_ACCT_GRP_MEMBER_TYPES = ['Statistical Accounts', 'Statistical Category'];

    /* Currently, we are not supporting these dimensions.*/
    const NOT_SUPPORTED_DIMENSION_LIST = ['task', 'costtype', 'fixedasset', 'affiliateentity'];

    CONST OVERIDE_TYPE_TARGET = 'TARGET';
    CONST OVERIDE_TYPE_REVERSE = 'REVERSE';

    const ACCOUNT_ALLOCATION = 'Account Allocation';

    /* @var string[] $trueupNonEditableFields */
    private array $trueupNonEditableFields = [
        'FOCUSLOCATION',
        'FOCUSDEPARTMENT',
        'FOCUSPROJECT',
        'FOCUSCUSTOMER',
        'FOCUSVENDOR',
        'FOCUSEMPLOYEE',
        'FOCUSITEM',
        'FOCUSCLASS',
        'FOCUSCONTRACT',
        'FOCUSWAREHOUSE',
        'SOURCE_GLACCTGRP',
        'SOURCE_PERCENT2ALLOCATE',
        'SOURCE_CURRENCY',
        'SOURCE_REPORTINGBOOK',
        'SOURCE_ADJBOOKID',
        'SOURCE_TIMEPERIOD',
        'SOURCE_SOURCEINCLUDEREPORTINGBOOK',
        'TRUEUP',
        'TARGET_REPORTINGBOOK',
        'TARGET_ADJBOOKID',
        'TARGET_JOURNALSYMBOL',
        'TARGET_GLACCOUNTNO'
    ];

    CONST DIMENSION_GROUP_FIELDS
        = [
            'LOCNO', 'VENDORID', 'WAREHOUSEID', 'PROJECTID', 'DEPTNO',
            'CUSTOMERID',
            'CONTRACTID', 'ITEMID', 'CLASSID', 'EMPLOYEEID'
            /*"ASSETID"*/
        ];

    CONST DIMENSION_OVERRIDE_FIELDS
        = [
            'OVERRIDELOCATION', 'OVERRIDEDEPARTMENT', 'OVERRIDEPROJECT',
            'OVERRIDECUSTOMER', 'OVERRIDEVENDOR', 'OVERRIDEITEM',
            'OVERRIDEEMPLOYEE', 'OVERRIDECLASS', 'OVERRIDEWAREHOUSE',
            'OVERRIDECONTRACT'
        ];
    CONST SOURCEPOOL_STATIC_FIELDS_MAP
        = [
            'GLACCTGRP' => 'SOURCE_GLACCTGRP',
            'PERCENT2ALLOCATE' => 'SOURCE_PERCENT2ALLOCATE',
            'CURRENCY' => 'SOURCE_CURRENCY',
            'REPORTINGBOOK' => 'SOURCE_REPORTINGBOOK',
            'ADJBOOKID' => 'SOURCE_ADJBOOKID',
            'TIMEPERIOD' => 'SOURCE_TIMEPERIOD',
            'SOURCEINCLUDEREPORTINGBOOK' => 'SOURCE_SOURCEINCLUDEREPORTINGBOOK',
            'TRUEUP' => 'TRUEUP'
        ];
    CONST SOURCEPOOL_STATICROW1_FIELDSMAP
        = [
            'GLACCTGRP' => 'SOURCE_GLACCTGRP', 'PERCENT2ALLOCATE' => 'SOURCE_PERCENT2ALLOCATE',
            'CURRENCY' => 'SOURCE_CURRENCY'
        ];
    CONST SOURCEPOOL_STATICROW2_FIELDSMAP
        = [
            'TIMEPERIOD' => 'SOURCE_TIMEPERIOD', 'TRUEUP' => 'TRUEUP'
          ];
    CONST BASISPOOL_STATIC_FIELDS_MAP
        = [
            'ALLOCATIONMETHOD' => 'BASIS_ALLOCATIONMETHOD',
            'REPORTINGBOOK' => 'BASIS_REPORTINGBOOK',
            'TIMEPERIOD' => 'BASIS_TIMEPERIOD',
            'GLACCTGRP' => 'BASIS_GLACCTGRP',
            'ADJBOOKID' => 'BASIS_ADJBOOKID',
            'SKIPNEGATIVE' => 'BASIS_SKIPNEGATIVE',
            'ACCUMULATION' => 'BASIS_ACCUMULATION',
            'BASISINCLUDEREPORTINGBOOK' => 'BASIS_BASISINCLUDEREPORTINGBOOK'
        ];
    CONST BASISPOOL_STATICROW3_FIELDSMAP
        = [
            'TIMEPERIOD' => 'BASIS_TIMEPERIOD',
            'SKIPNEGATIVE' => 'BASIS_SKIPNEGATIVE'
        ];
    CONST BASISPOOL_STATIC_FIELDS_MAP_VIEW_MODE
        = [
            'ALLOCATIONMETHOD' => 'BASIS_ALLOCATIONMETHOD',
            'REPORTINGBOOK' => 'BASIS_REPORTINGBOOK',
            'TIMEPERIOD' => 'BASIS_TIMEPERIOD',
            'GLACCTGRP' => 'BASIS_GLACCTGRP',
            'ADJBOOKID' => 'BASIS_ADJBOOKID',
            'DROPNEGATIVE_VIEWMODE' => 'DROPNEGATIVE_VIEWMODE',
            'ACCUMULATION' => 'BASIS_ACCUMULATION',
            'BASISINCLUDEREPORTINGBOOK' => 'BASIS_BASISINCLUDEREPORTINGBOOK',
            'BASISDIMENSIONFILTER_VIEWMODE' => 'BASISDIMENSIONFILTER_VIEWMODE'
        ];
    CONST BASISPOOL_STATICROW3_FIELDSMAP_VIEW_MODE
        = [
            'TIMEPERIOD' => 'BASIS_TIMEPERIOD',
            'DROPNEGATIVE_VIEWMODE' => 'DROPNEGATIVE_VIEWMODE',
            'BASISDIMENSIONFILTER_VIEWMODE' => 'BASISDIMENSIONFILTER_VIEWMODE'
        ];
    CONST TARGETPOOL_STATIC_FIELDS_MAP
        = [
            'REPORTINGBOOK' => 'TARGET_REPORTINGBOOK',
            'ADJBOOKID' => 'TARGET_ADJBOOKID',
            'JOURNALSYMBOL' => 'TARGET_JOURNALSYMBOL',
            'GLACCOUNTNO' => 'TARGET_GLACCOUNTNO',
            'EXCH_RATE_TYPE_ID' => 'TARGET_EXCH_RATE_TYPE_ID'
        ];
    CONST IGC_DIMENSION_TREATMENT_VALUES
        = array(
            GLAcctAllocationManager::NOT_INVITED => GLAcctAllocationManager::NOT_INVITED_INTERNAL,
            GLAcctAllocationManager::PRESERVED => GLAcctAllocationManager::PRESERVED_INTERNAL
        );
    CONST ALLOCATION_LABEL_FIELDS
        = [
            'DUMMYSOURCE_VIEWMODE' => 'DUMMYSOURCE',
            'DUMMYBASIS_VIEWMODE' => 'DUMMYBASIS', 'DUMMYTARGET_VIEWMODE' => 'DUMMYTARGET'
        ];
    CONST ADJBOOKID_FIELD = "ADJBOOKID";

    CONST ACCRUAL_AND_CASH = "ACCRUAL AND CASH";

    /**
     * @var array $showOnlyTheseDims
     */
    private $showOnlyTheseDims = array();
    /**
     * @var array $allDimensionInfo
     */
    private $allDimensionInfo = array();

    /** @var array $reportStdDimensions */
    private $reportStdDimensions = [];

    /** @var array $allStdDimensionInfo */
    private $allStdDimensionInfo = [];

    /**
     * @var array $customDimensionInfo
     */
    private $customDimensionInfo = array();
    /**
     * @var array $customDimensionInfo
     */
    private $checkDuplicate = true;
    /**
     * @var array $accountGrpMembers
     */
    private $accountGrpMembers = array();
    /**
     * @var array $availableTimeperiods
     */
    private $availableTimeperiods = array();
    /**
     * @var string $sourceCurrency
     */
    private $sourceCurrency;

    /* @var  array $dimInfo */
    private $dimInfo;

    /* @var  array $glAcctGrpInfo */
    private $glAcctGrpInfo;

    /* @var array $customTimePeriodIds */
    private $customTimePeriodIds = array();

    /**
     * @var bool $delByAllocKey
     */
    private $delByAllocKey = false;

    /* @var bool $isSrcAcctGrpStat */
    private $isSrcAcctGrpStat = false;

    /* @var bool $isBasisAcctGrpStat */
    private $isBasisAcctGrpStat = false;


    /**
     * __construct
     *
     * @param array $params entitymanager param
     */
    function __construct($params = array())
    {
        parent::__construct($params);

        if ( ! GetMyCompany() ) {
            return;
        }

        $this->showOnlyTheseDims = array_keys(
            IADimensions::getActiveGLDimensions(false)
        );

        $this->allDimensionInfo = IADimensions::getAllDimensions(false);
        $this->allStdDimensionInfo = IADimensions::getAllDimensionObjectProperties(false);
        $this->reportStdDimensions = IADimensions::getReportDimensions(false);
        $this->customDimensionInfo = $this->getActiveGLCustomDimensionInfo();
        $this->availableTimeperiods = $this->getAllocationTimeperiods();
        $this->customTimePeriodIds = $this->getCustomTimePeriodIds();
    }
    
    /**
     * @param $ignoreDimensionList
     *
     * @return bool
     */
    protected function ignoreDimensions(&$ignoreDimensionList) : bool
    {
        return false;
    }

    /**
     * @return string
     */
    protected function getTagName(){
        return self::TYPE_RESTRICTION_RELEASE;
    }

    /**
     * New dimension must have Group as substring instead of Grp that are used by older dimension
     * Ex - Asset: we are using Group as substring in the Db column name
     * @param string $dimKey
     * @return bool
     */
    public static function dimensionWithGrpInPath(string $dimKey): bool
    {
        return in_array($dimKey, ['location', 'department', 'project', 'customer', 'vendor', 'employee',
            'item', 'class', 'contract', 'warehouse', 'costtype']);
    }

    /**
     * @return bool
     */
    protected function isSrcAcctGrpStat(): bool
    {
        return $this->isSrcAcctGrpStat;
    }

    /**
     * @return bool
     */
    private function isBasisAcctGrpStat(): bool
    {
        return $this->isBasisAcctGrpStat;
    }


    /**
     * @return array
     */
    private function getCustomTimePeriodIds()
    {
        $records = Globals::$g->gManagerFactory->getManager("glbudgettype")
            ->GetList(array('selects' => array('RECORD#')));
        $customTimePeriodIds = [];
        foreach ($records as $record) {
            $customTimePeriodIds[$record['RECORD#']] = true;
        }
        return $customTimePeriodIds;
    }

    /**
     * @return array
     */
    protected function getRegulateIds(): array
    {
        return ["ACCTALLOCATIONID"];
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    protected function regularAdd(&$values)
    {
        $source = "GLAcctAllocationManager::add";
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->transformValues($values);

        // Make sure we have a unique template ID
        if ($ok
            && $this->checkDuplicate
            && $this->isTemplateExists(
                $values['ACCTALLOCATIONID']
            )) {
            $msg =  "Allocation Template ID " . $values['ACCTALLOCATIONID']
                . " is already present.";

            $corr = "Provide a new Allocation Template ID";
            Globals::$g->gErr->addIAError(
                'GL-2363', __FILE__ . ':' . __LINE__, $msg,
                ['ACCTALLOCATIONID' => $values['ACCTALLOCATIONID']],
                $corr, []
            );
            $ok = false;
        }

        if ($values['GLACCTALLOCATIONTARGET']['BILLABLE'] === 'true') {
            $journalSymbol = explode('--', $values['GLACCTALLOCATIONTARGET']['JOURNALSYMBOL'])[0];
            $ok = $ok && self::validateJournalIsBillable($journalSymbol);
        }

        $ok = $ok && $this->validateNTranslateInput($values);

        if (!$ok) {
            $tagName = $this->getTagName();
            $msg = "Could not create {$tagName}!";
            $gErr = Globals::$g->gErr;
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION)? "GL-2379": "GL-2301";
            $gErr->addError($errorCode, __FILE__ . ':' . __LINE__, $msg);
        }

        $ok = $ok && parent::regularAdd($values);

        // Add suporting document, if any
        if($values['SUPDOCID']){
            $ok = $ok && AddSupportingDocumentMap(
                    $values['RECORDNO'],
                    $values['SUPDOCID'], '', $this->getSupportingDocumentMapType());
        }

        $ok = $ok && $this->_QM->commitTrx($source);

        if (!$ok) {
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Get the the supporting document map type
     *
     * @return string the supporting document map type
     */
    private function getSupportingDocumentMapType()
    {
        return 'GLACCTALLOCATION';
    }

    /**
     * @param int $recordNo
     *
     * @return bool
     */
    public function allocDefinitionHasRuns($recordNo)
    {
        $hasRuns = false;

        // get the list of sub/child account allocations
        $qry[] = "select record# from glacctallocationmst where cny# = :1"
            . " and (record# = :2 or latestversionkey = :2) order by record# desc";
        $qry[] = GetMyCompany();
        $qry[] = $recordNo;

        $allocDefs = QueryResult($qry);

        if (!empty($allocDefs)) {

            $allocDefRecs = [];
            foreach ($allocDefs as $allocDef) {
                $allocDefRecs[] = $allocDef['RECORD#'];
            }


            // if true, then some allocation runs already happened on this allocationid else false
            $gManagerFactory = Globals::$g->gManagerFactory;
            $manager = $gManagerFactory->getManager('glacctallocationrun');
            $filter = array(
                'selects' => array('RECORDNO'),
                'filters' => array(
                    array(
                        array('GLACCTALLOCATIONKEY', 'IN', $allocDefRecs),
                        array('STATE', '!=', GLAcctAllocationRunManager::FAIL_STATE)
                    )
                )
            );

            $result = $manager->GetList($filter);

            if (!empty($result)) {
                $hasRuns = true;
            }
        }

        return $hasRuns;
    }

    /**
     * @param array $object
     * @param array $values
     * @return array
     */
    function API_UpdateMerge(&$object, &$values)
    {
        $values["OLD_AUTOREVERSEPRIORPOSTEDJE"] = $object['AUTOREVERSEPRIORPOSTEDJE'];
        return parent::API_UpdateMerge($object, $values);
    }

    /**
     * @param $values
     */
    private function unsetRootNdOwnedObjectsRecNo(&$values)
    {
        $recNoFldName = 'RECORDNO';
        unset($values[$recNoFldName]);
        $objs = $this->GetOwnedObjects();
        foreach ($objs as $obj) {
            $path = $obj["path"];
            if (isset($values[$path][$recNoFldName])) {
                unset($values[$path][$recNoFldName]);
            }
            if (isset($values[$path][0][$recNoFldName])) {
                unset($values[$path][0][$recNoFldName]);
            }
        }
    }

    /**
     * @param array $values
     *
     * @return bool
     * @throws IAException
     */
    protected function regularSet(&$values)
    {
        $source = 'GLAcctAllocationManager::set';
        $ok = $this->_QM->beginTrx($source);

        if (!empty($values['RECORDNO'])) {

            // update supporting doc, if any or delete it
            $ok = $ok && SetSupportingDocumentMap($values['RECORDNO'], $values['SUPDOCID'] ?? "", '',
                    $this->getSupportingDocumentMapType());

            $ok = $ok
                && $this->validateAllocationID(
                    $values['STATUS'], $values['ACCTALLOCATIONID'], $values['RECORDNO']
                );

            $ok = $ok && $this->checkAllocationDefinitionRuns($values['RECORDNO']);

            if ($ok) {
                // Get the number of transactions created out of this template
                $hasRuns = $this->allocDefinitionHasRuns($values['RECORDNO']);

                // If we have transactions for this template or we are editing an older version of it we will create
                // another version of it
                if ($hasRuns) {
                    $oldRecordNo = $values['RECORDNO'];
                    $this->unsetRootNdOwnedObjectsRecNo($values);

                    $this->checkDuplicate = false;
                    if (isset($values['OLD_AUTOREVERSEPRIORPOSTEDJE']) &&
                        ($values['OLD_AUTOREVERSEPRIORPOSTEDJE'] == 'false') &&
                        ($values['AUTOREVERSEPRIORPOSTEDJE'] == 'true')) {
                        $ok = false;
                        Globals::$g->gErr->addError('GL-2364',
                            __FILE__ . ":" . __LINE__,
                        "Auto-reverse prior post can not be changed, if allocation has already run"
                        );
                    }

                    $ok = $ok && $this->validateAllocationVersion($values, $oldRecordNo);

                    $ok = $ok && $this->add($values);
                    // Update the current template version key
                    $ok = $ok
                        && $this->updateTemplateLatestVersionKey(
                            $values['RECORDNO'], $oldRecordNo
                        );
                } else {
                    $ok = $ok && $this->transformValues($values);

                    if ($values['GLACCTALLOCATIONTARGET']['BILLABLE'] === 'true') {
                        $journalSymbol = explode('--', $values['GLACCTALLOCATIONTARGET']['JOURNALSYMBOL'])[0];
                        $ok = $ok && self::validateJournalIsBillable($journalSymbol);
                    }

                    $ok = $ok && $this->validateNTranslateInputForSet($values);

                    $ok = $ok && $this->validateNTranslateInput($values);

                    // If no transaction and we are editing the lastest template we will do a normal set
                    $ok = $ok && parent::regularSet($values);
                }
            }
        } else {
            $tagName = $this->getTagName();
            $msg = "Could not create ".$tagName."!, Provide RECORDNO to update.";
            $gErr = Globals::$g->gErr;
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2380": "GL-2365";
            $gErr->addError($errorCode, __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        }


        $ok = $ok && $this->_QM->commitTrx($source);

        if (count(Globals::$g->gErr->getErrors()) > 0 && !$ok) {
            $tagName = $this->getTagName();
            $msg = "Could not update ".$tagName."!";
            $gErr = Globals::$g->gErr;
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2381": "GL-2366";
            $gErr->addError($errorCode, __FILE__ . ':' . __LINE__, $msg);
        } elseif (!$ok && !$this->_warningValidation && count(Globals::$g->gErr->getErrors()) === 0) {
            $ok = true;
        }

        if (!$ok) {
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param string $journalSymbol
     *
     * @return bool
     */
    public static function validateJournalIsBillable($journalSymbol)
    {
        $glbatchMgr = Globals::$g->gManagerFactory->getManager("glbatch");
        if (!$glbatchMgr->isBillableEnabled()) {
            Globals::$g->gErr->addError(
                'GL-2302', __FILE__ . ':' . __LINE__,
                "We cannot able to process allocation becasuse GL is not configured for project billable."
            );
            return false;
        } elseif (!$glbatchMgr->isJournalBillabled($journalSymbol)) {
            Globals::$g->gErr->addError(
                'GL-2303', __FILE__ . ':' . __LINE__,
                "We cannot able to process allocation because the selected journal is not enabled for project billable."
            );
            return false;
        }
        return true;
    }

    /**
     * @param string      $ID
     * @param string|null $fields
     *
     * @return array|bool
     * @access public
     */
    public function Get($ID, $fields = null)
    {
        if (!isset($ID) || $ID === '') {
            return false;
        }

        $retPreferences = parent::get($ID);
        if (!$retPreferences) {
            // Cannot find the allocation definition with the given ID. Try to read the
            // allocation definition by its name
            $recordno = $this->GetRecordNo('ACCTALLOCATIONID', $ID, false);
            if (isset($recordno)) {
                $retPreferences = parent::get($recordno);
            }
        }

        if (!$retPreferences) {
            $tagName = strtolower($this->getTagName());
            $errorCode = ($tagName === strtolower(self::ACCOUNT_ALLOCATION)) ? "GL-2382": "GL-2367";
            Globals::$g->gErr->addIAError(
                $errorCode, __FILE__ . ':' . __LINE__,
                "Cannot find the {$tagName} definition with the given key $ID.",
                ['ID' => $ID]
            );
            return false;
        }

        $this->setActiveGLDimensionInfo();
        $Indexes = array('SOURCE', 'BASIS');
        foreach ($Indexes as $indexVal) {
            foreach ($this->dimInfo as $dimval) {
                if ($dimval['udd'] == 1) {
                    continue;
                }

                if ($dimval['entity'] === 'location') {
                    $fieldpath = 'LOCNO';
                    $fieldGrpPath = 'LOCGRPNO';
                } elseif ($dimval['entity'] === 'department') {
                    $fieldpath = 'DEPTNO';
                    $fieldGrpPath = 'DEPTGRPNO';
                } else {
                    $fieldpath = isl_strtoupper($dimval['entity']) . "ID";
                    $fieldGrpPath = isl_strtoupper($dimval['entity']) . 'GRPID';
                }
                if (empty($retPreferences['GLACCTALLOCATION' . $indexVal][0][$fieldpath])) {
                    $retPreferences['GLACCTALLOCATION' . $indexVal][0][$fieldpath] = $retPreferences['GLACCTALLOCATION'
                    . $indexVal][0][$fieldGrpPath];
                }
            }
        }

        // convert exchangerate type if present
        if (!empty($retPreferences['GLACCTALLOCATIONTARGET'][0]['EXCH_RATE_TYPE_ID'])) {
            $exTypeMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
            $retPreferences['GLACCTALLOCATIONTARGET'][0]['EXCH_RATE_TYPE_ID'] = $exTypeMgr->GetExchangeRateTypeName($retPreferences['GLACCTALLOCATIONTARGET'][0]['EXCH_RATE_TYPE_ID']);
        }

        // get supporting doc
        $retPreferences['SUPDOCID'] = GetSupportingDocumentID($ID, $this->getSupportingDocumentMapType());

        return $retPreferences;
    }


    /**
     * @param array $values
     *
     * @return array
     */
    public function API_FormatObject($values)
    {
        $values = parent::API_FormatObject($values);

        // we need to format source object here
        $sourceObj = Globals::$g->gManagerFactory->getManager('glacctallocationsource');
        $values['GLACCTALLOCATIONSOURCE'] = $sourceObj->API_FormatObject($values['GLACCTALLOCATIONSOURCE']);

        // we need to format basis object here
        $sourceObj = Globals::$g->gManagerFactory->getManager('glacctallocationbasis');
        $values['GLACCTALLOCATIONBASIS'] = $sourceObj->API_FormatObject($values['GLACCTALLOCATIONBASIS']);

        return $values;
    }

    /**
     * This function is the opposite of API_FormatObject, to 'untransform' the
     * outbound values in a suitable PHP structure.
     *
     * @param array $values
     *
     * @return array unformatted structure
     */

    public function API_UnformatObject($values)
    {
        $values = parent::API_UnformatObject($values);
        foreach ($values as &$val) {
            if (!empty($val['GLACCTALLOCATIONSOURCE'])) {
                // we need to format source object here
                $sourceObj = Globals::$g->gManagerFactory->getManager('glacctallocationsource');
                unset($val['GLACCTALLOCATIONSOURCE']['glacctallocationsource'][0]['SOURCEADJBOOKS']['glacctallocationsourceadjbooks']);
                $val['GLACCTALLOCATIONSOURCE']['glacctallocationsource'][0]['SOURCEADJBOOKS'] = [
                    'GLACCTALLOCATIONSOURCEADJBOOK' =>
                        $val['GLACCTALLOCATIONSOURCE']['glacctallocationsource'][0]['SOURCEADJBOOKS']
                ];

                $val['GLACCTALLOCATIONSOURCE']['glacctallocationsource'] = $sourceObj->API_ProcessReadData(
                    $val['GLACCTALLOCATIONSOURCE']['glacctallocationsource']
                );
                /* We need the below line to unset 'glacctallocationsourceadjbooks' from array.
                 This is added back to our array by the API_ProcessReadData(...) */
                unset($val['GLACCTALLOCATIONSOURCE']['glacctallocationsource'][0]['SOURCEADJBOOKS']['glacctallocationsourceadjbooks']);

                $allocationValues = $val['GLACCTALLOCATIONSOURCE']['glacctallocationsource'][0];
                unset($val['GLACCTALLOCATIONSOURCE']);
                $val['GLACCTALLOCATIONSOURCE'] = $allocationValues;

                // we need to format basis object here
                $bacisObj = Globals::$g->gManagerFactory->getManager('glacctallocationbasis');
                unset($val['GLACCTALLOCATIONBASIS']['glacctallocationbasis'][0]['BASISADJBOOKS']['glacctallocationbasisadjbooks']);
                $val['GLACCTALLOCATIONBASIS']['glacctallocationbasis'][0]['BASISADJBOOKS'] = [
                    'GLACCTALLOCATIONBASISADJBOOK' =>
                        $val['GLACCTALLOCATIONBASIS']['glacctallocationbasis'][0]['BASISADJBOOKS']
                ];

                $val['GLACCTALLOCATIONBASIS']['glacctallocationbasis'] = $bacisObj->API_ProcessReadData(
                    $val['GLACCTALLOCATIONBASIS']['glacctallocationbasis']
                );
                /* We need the below line to unset 'glacctallocationbasisadjbooks' from array.
                  This is added back to our array by the API_ProcessReadData(...) */
                unset($val['GLACCTALLOCATIONBASIS']['glacctallocationbasis'][0]['BASISADJBOOKS']['glacctallocationbasisadjbooks']);

                $allocationValues = $val['GLACCTALLOCATIONBASIS']['glacctallocationbasis'][0];
                unset($val['GLACCTALLOCATIONBASIS']);
                $val['GLACCTALLOCATIONBASIS'] = $allocationValues;

                $allocationValues
                    = $val['GLACCTALLOCATIONTARGET']['glacctallocationtarget'][0];
                unset($val['GLACCTALLOCATIONTARGET']);
                $val['GLACCTALLOCATIONTARGET'] = $allocationValues;

                $allocationValues
                    = $val['GLACCTALLOCATIONREVERSE']['glacctallocationreverse'][0];
                unset($val['GLACCTALLOCATIONREVERSE']);
                $val['GLACCTALLOCATIONREVERSE'] = $allocationValues;
            }
        }
        return $values;
    }

    /**
     * @param array $values
     */
    private function sanitizeRootOnlyFields(&$values)
    {
        // do not allow users to save below values in entity level
        if (GetContextLocation()) {
            unset($values['ALLOWALLOCATION']);
        } else if (isNullOrBlank($values['ALLOWALLOCATION'])) {
            $values['ALLOWALLOCATION'] = $this->GetFieldInfo('ALLOWALLOCATION')['default'];
        }

        if(GetContextLocation() || !IsMCMESubscribed() || $values['ALLOWALLOCATION'] === self::ALLOWALLOCATION_WITHINENTITY) {
            unset($values['GLACCTALLOCATIONTARGET']['EXCH_RATE_TYPE_ID']);
        }
    }

    /**
     * @return string
     */
    public function getAllocType(): string
    {
        return self::TYPE_RESTRICTION_RELEASE;
    }

    /**
     * @param array $allPDVDims
     * @param array $source
     * @param array $basis
     *
     * @return bool
     */
    private function validatePDVSrcNdBasis($allPDVDims, $source, $basis):bool
    {
        $ok = true;
        if(isEmptyArray($allPDVDims)){
            return $ok;
        }

        $gErr = Globals::$g->gErr;
        $pdvDim = $allPDVDims[0]; /* since we support only one dim */
        $pdvDimId = isl_strtoupper($pdvDim) . "ID";
        $pdvDimGrpId = isl_strtoupper($pdvDim) . "GRPID";

        $sourcePdvDimId = $source[$pdvDimId] ?? null;
        $basisPdvDimId = $basis[$pdvDimId] ?? null;
        $sourcePdvGrpDimId = $source[$pdvDimGrpId] ?? null;
        $basisPdvGrpDimId = $basis[$pdvDimGrpId] ?? null;

        $isSourceAndBasePDVNotSame =
            (!isNullOrBlank($basisPdvDimId) && ($sourcePdvDimId !== $basisPdvDimId))
            || (!isNullOrBlank($basisPdvGrpDimId) && ($sourcePdvGrpDimId !== $basisPdvGrpDimId));

        $bothIdAndGrpExists = (!isNullOrBlank($sourcePdvDimId) || !isNullOrBlank($basisPdvDimId))
            && (!isNullOrBlank($sourcePdvGrpDimId) || !isNullOrBlank($basisPdvGrpDimId));

        //TODO: Need to fix this condition with 2024R4, this is just a workaround.
        /* We have a code in get method that sets both id and grp to same value if group is provided (IA-161969) */
        if ($bothIdAndGrpExists) {
            $bothIdAndGrpExists = !(
                $sourcePdvDimId === $sourcePdvGrpDimId
                && $basisPdvDimId === $basisPdvGrpDimId
                && ($sourcePdvDimId === $basisPdvDimId || isNullOrBlank($basisPdvDimId))
            );
        }


        if ($isSourceAndBasePDVNotSame || $bothIdAndGrpExists) {
            $ok = false;
            $gErr->addError(
                'GL-2304', __FILE__ . ':' . __LINE__,
                "Source and Basis should have same filter for per-dimension-value."
            );
        }

        return $ok;
    }

    /**
     * Validate and translate the input data
     *
     * @param array $values
     *
     * @return bool if error else true
     *
     * @throws IAException
     */
    private function validateNTranslateInput(&$values)
    {
        $ok = true;
        $restrictSourceCustTimePrd = false;
        $this->sanitizeRootOnlyFields($values);

        $values['ALLOCTYPE'] = $this->getAllocType();

        if (empty($values['ACTIVITYDELTA'])) {
            $values['ACTIVITYDELTA'] = 'false';
        }

        if (empty($values['AUTOREVERSEPRIORPOSTEDJE'])) {
            $values['AUTOREVERSEPRIORPOSTEDJE'] = 'false';
        }

        if ($values['AUTOREVERSEPRIORPOSTEDJE'] === 'true'){
            $restrictSourceCustTimePrd = true;
        }

        if ($values['AUTOREVERSEPRIORPOSTEDJE'] === 'true' && $values['ACTIVITYDELTA'] === 'true') {

            Globals::$g->gErr->addError(
                'GL-2368', __FILE__ . ':' . __LINE__,
                "Activity delta and Auto-reverse prior post options cannot be used together"
            );
          //  (code change review )
            $ok = false;
        }

        $journalsymbol = explode('--', $values['GLACCTALLOCATIONTARGET']['JOURNALSYMBOL'])[0];
        // if auto reversal is on, check if journal is set for approvals
        if ($values['AUTOREVERSEPRIORPOSTEDJE'] === 'true' && GLSetupManager::isApprovalEnabled($journalsymbol)) {
            Globals::$g->gErr->addIAError(
                'GL-2369', __FILE__ . ':' . __LINE__,
                "Target journal '$journalsymbol' is set for " . "approval and cannot be used with 'Auto-reverse prior post' option",
                ['JOURNALSYMBOL' => $journalsymbol]
            );   //(code change Review )
            $ok = false;
        }

        $allPDVDims = [];
        foreach ($this->allDimensionInfo as $dimKey => $dimval) {
            $path = 'FOCUS' . isl_strtoupper($dimKey);
            if (!in_array($dimKey, $this->showOnlyTheseDims)) {
                unset($values[$path]);
            } elseif (empty($values[$path])) {
                $values[$path] = self::NOT_INVITED;
                if (IsMCMESubscribed() && $path === 'FOCUSLOCATION') {
                    $values[$path] = self::PRESERVED;
                }
            } else if ($values[$path] === self::PER_DIMENSION_VALUE) {
                $allPDVDims[] = $dimKey;
            }
        }

        foreach ($this->customDimensionInfo as $custKey => $custVal) {
            $path = 'FOCUS' . $custKey;
            if (empty($values[$path])) {
                $values[$path] = self::NOT_INVITED;
            }
        }

        if (count($allPDVDims) > 1) {
            Globals::$g->gErr->addError(
                'GL-2305', __FILE__ . ':' . __LINE__,
                "A per-dimension value can take only one dimension. Select a single dimension and try again."
            );
            $ok = false;
        }


        $sourceEntityMaps =[];
        if (!empty($values['GLACCTALLOCATIONSOURCE'])) {

            $ok = $ok
                && $this->validateNTranslateSource(
                    $values['GLACCTALLOCATIONSOURCE'], $values[self::FOCUSPROJECT], $restrictSourceCustTimePrd, $sourceEntityMaps
                );
        } else {
            $tagName = $this->getTagName();
            $msg = sprintf("GL %s source information is required ", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2383": "GL-2306";
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            $ok = false;
        }

        $basisEntityMaps = [];
        if (!empty($values['GLACCTALLOCATIONBASIS'])) {

            $ok = $ok
                && $this->validateNTranslateBasis(
                    $values['GLACCTALLOCATIONBASIS'], $values[self::FOCUSPROJECT], $basisEntityMaps
                );
        } else {
            $tagName = $this->getTagName();
            $msg = sprintf("GL %s basis information is required ", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2384": "GL-2307";
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            $ok = false;
        }

        $ok = $ok && $this->validatePDVSrcNdBasis($allPDVDims,
                $values['GLACCTALLOCATIONSOURCE'], $values['GLACCTALLOCATIONBASIS']);

        if (!empty($values['GLACCTALLOCATIONTARGET'])) {
            $ok = $ok
                && $this->validateNTranslateTarget(
                    $values['GLACCTALLOCATIONTARGET']
                );
            $ok = $ok
                && $this->validateOverrideDimensions(
                    $values, self::OVERIDE_TYPE_TARGET
                );
        } else {
            $tagName = $this->getTagName();
            $msg = sprintf("GL %s target information is required ", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2377": "GL-2308";
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            $ok = false;
        }

        if (!empty($values['GLACCTALLOCATIONREVERSE'])) {
            $ok = $ok && $this->validateNTranslateReverse($values['GLACCTALLOCATIONREVERSE'], $values['ACTIVITYDELTA']);

            $ok = $ok && $this->validateOverrideDimensions($values, self::OVERIDE_TYPE_REVERSE);

        } else {
            $tagName = $this->getTagName();
            $msg = sprintf("GL %s reverse information is required ", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2378": "GL-2309";
            Globals::$g->gErr->addIAError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            $ok = false;
        }

        if (IsMultiEntityCompany() ) {
            $label = 'Location';

            if (IsMCMESubscribed() && empty($values["GLACCTALLOCATIONSOURCE"]["LOCNO"])) {
                $msg = "$label/$label group is required in souce filter";
                Globals::$g->gErr->addError(
                    'GL-2310', __FILE__ . ':' . __LINE__, $msg
                );
                $ok = false;
            }

            if (IsMCMESubscribed() && $values["GLACCTALLOCATIONBASIS"]["ALLOCATIONMETHOD"] !== "Dynamic-relative account statistical"
                && empty($values["GLACCTALLOCATIONBASIS"]["LOCNO"])
            ) {
                $msg = "$label/$label group is required in basis filter";
                Globals::$g->gErr->addError(
                    'GL-2311', __FILE__ . ':' . __LINE__, $msg
                );
                $ok = false;
            }

            if ($values['FOCUSLOCATION'] === self::NOT_INVITED) {
                $ok = $ok && $this->validateLocationOverrides(
                        $values['GLACCTALLOCATIONTARGET']['OVERRIDELOCATION'],
                        $values['GLACCTALLOCATIONREVERSE']['OVERRIDELOCATION'],
                        $this->sourceCurrency,
                        $values['ALLOWALLOCATION'],
                        $values['GLACCTALLOCATIONTARGET']['EXCH_RATE_TYPE_ID']
                    );
            } else if ($values['FOCUSLOCATION'] === self::ALONG_FOR_RIDE) {
                if ($values['ALLOWALLOCATION'] === self::ALLOWALLOCATION_WITHINENTITY) {
                    if (count($basisEntityMaps) > 1) {
                        $msg = "Selected " . $label . " group '" . $values["GLACCTALLOCATIONBASIS"]["LOCNO"]
                            . "' has members belonging to different entities";
                        Globals::$g->gErr->addIAError(
                            'GL-2496', __FILE__ . ':' . __LINE__,
                            $msg, ['LOCNO' => $values["GLACCTALLOCATIONBASIS"]["LOCNO"]]
                        );
                        $ok = false;
                    } else if ($basisEntityMaps[0] !== $sourceEntityMaps[0]) {
                        $msg = "Source and Basis " . $label . " filters belonging to different entities";
                        Globals::$g->gErr->addError('GL-2495', __FILE__ . ':' . __LINE__, $msg);
                        $ok = false;
                    }

                }
            } else if ($values['FOCUSLOCATION'] === self::PRESERVED
                && $values['ALLOWALLOCATION'] === self::ALLOWALLOCATION_ACROSSENTITIES
            ) {
                $locLabel = "Location";

                $msg = "Allocation of type 'Across entities' cannot be supported while preserving $locLabel dimension";
                Globals::$g->gErr->addError(
                    'GL-2312', __FILE__ . ':' . __LINE__, $msg
                );
                $ok = false;
            }

            if ($values['ALLOWALLOCATION'] === self::ALLOWALLOCATION_ACROSSENTITIES && count($sourceEntityMaps) > 1) {
                $msg = "Selected " . $label . " group '" . $values["GLACCTALLOCATIONSOURCE"]["LOCNO"]
                    . "' has members belonging to different entities";
                Globals::$g->gErr->addIAError(
                    'GL-2313', __FILE__ . ':' . __LINE__, $msg, ['LOCNO' => $values["GLACCTALLOCATIONSOURCE"]["LOCNO"]]
                );
                $ok = false;
            }

            if (IsMCMESubscribed() && $values['ALLOWALLOCATION'] === self::ALLOWALLOCATION_ACROSSENTITIES
                && empty($values['GLACCTALLOCATIONTARGET']['EXCH_RATE_TYPE_ID'])) {
                $msg = "Exchange rate is required for across entity allocation.";
                Globals::$g->gErr->addError('GL-2314', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * Validate and translate the input data
     *
     * @param array $values
     *
     * @return bool if error else true
     *
     * @throws IAException
     */
    private function validateNTranslateInputForSet(&$values)
    {
        $ok = true;

        if (empty($values['GLACCTALLOCATIONSOURCE']['RECORDNO'])) {
            $tagName = $this->getTagName();
            $msg = sprintf("RECORDNO  for GL %s source  is required to update", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2385": "GL-2315";
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            $ok = false;
        }

        if ($ok && empty($values['GLACCTALLOCATIONBASIS']['RECORDNO'])) {
            $tagName = $this->getTagName();
            $msg = sprintf("RECORDNO  for GL %s basis  is required to update", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2386": "GL-2316";
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            return false;
        }

        if ($ok && empty($values['GLACCTALLOCATIONTARGET']['RECORDNO'])) {
            $tagName = $this->getTagName();
            $msg = sprintf("RECORDNO  for GL %s target  is required to update", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2387": "GL-2317";
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            return false;
        }

        if ($ok && empty($values['GLACCTALLOCATIONREVERSE']['RECORDNO'])) {
            $tagName = $this->getTagName();
            $msg = sprintf("RECORDNO  for GL %s reverse  is required to update", $tagName);
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2388": "GL-2318";
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );
            return false;
        }

        return $ok;
    }

    /**
     * Validate and translate the input data for allocation source
     *
     * @param array  $values
     * @param string $projectDimFilter
     * @param bool $restrictCustTimePrd
     * @param string[] $entityMaps
     *
     * @return false if error else true
     *
     * @throws IAException
     */
    private function validateNTranslateSource(
        &$values, $projectDimFilter, $restrictCustTimePrd, &$entityMaps
    )
    {
        $ok = $this->validateInceptionToPeriod($projectDimFilter, $values['TIMEPERIOD']);
        $timeperiodKey = '';
        $ok = $ok && $this->validateTimePeriods($values['TIMEPERIOD'], $timeperiodKey, $restrictCustTimePrd);

        if ($ok) {
            $values['TIMEPERIODKEY'] = $timeperiodKey;
        }

        $normalBalance = 0;
        $groupkey = '';
        $ok = $ok && $this->validateAcctGrpNGetGrpKey($values['GLACCTGRP'], $groupkey,
                $normalBalance, true, '', '');

        if ($ok) {
            if ($this->isSrcAcctGrpStat() && ($values['SOURCEINCLUDEREPORTINGBOOK'] === self::ALTERNATE_BOOKS_ONLY)) {
                $msg = "You've selected a statistical account group and alternate books in source. Alternate books do not contain statistical data. Include the main reporting book and continue.";
                Globals::$g->gErr->addError('GL-2319', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
            $values['GLACCTGRPKEY'] = $groupkey;
        }
        $ok = $ok && $this->isValidGLBookid($values['REPORTINGBOOK']);

        $ok = $ok && $this->validateDimensionValues($values, $entityMaps);

        if (IsMCMESubscribed()) {
            $currency = '';
            $ok = $ok
                && $this->validateLocationBaseCurrency(
                    $values['LOCNO'], $values['CURRENCY'], 'SOURCE', $currency
                );
            if ($ok && !empty($values['LOCNO'])) {
                $values['CURRENCY'] = $currency;
            }
        } else {
            if (empty($values['CURRENCY'])) {
                $values['CURRENCY'] = GetBaseCurrency();
            } elseif ($values['CURRENCY'] != GetBaseCurrency()) {
                $msg = "Given currency " . $values['CURRENCY'] . " is invalid ";
                Globals::$g->gErr->addIAError(
                    'GL-2320', __FILE__ . ':' . __LINE__, $msg, ['CURRENCY' => $values['CURRENCY']]
                );
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * Validate and translate the input data for allocation basis
     *
     * @param array  $values
     *
     * @param string $projectDimFilter
     *
     * @param string[] $entityMaps
     *
     * @return bool if error else true
     *
     * @throws IAException
     */
    private function validateNTranslateBasis(&$values, $projectDimFilter, &$entityMaps)
    {
        $ok = $this->validateInceptionToPeriod($projectDimFilter, $values['TIMEPERIOD']);
        $timeperiodKey = '';
        $ok = $ok && $this->validateTimePeriods($values['TIMEPERIOD'], $timeperiodKey);

        if ($ok) {
            $values['TIMEPERIODKEY'] = $timeperiodKey;
        }

        if ($ok && empty($values['SKIPNEGATIVE'])) {
            $values['SKIPNEGATIVE'] = 'false';
        }
        $groupkey = '';
        $ok = $ok
            && $this->validateAcctGrpNGetGrpKey(
                $values['GLACCTGRP'], $groupkey, $normalBalance, false, $values['SKIPNEGATIVE'],
                $values['ACCUMULATION']
            );

        if ($ok) {
            if ($this->isBasisAcctGrpStat() && ($values['BASISINCLUDEREPORTINGBOOK'] === self::ALTERNATE_BOOKS_ONLY)) {
                $msg = "You've selected a statistical account group and alternate books in basis. Alternate books do not contain statistical data. Include the main reporting book and continue";
                Globals::$g->gErr->addError('GL-2321', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }

            if ($this->isBasisAcctGrpStat() ^ ($values['ALLOCATIONMETHOD'] === "Dynamic-relative account statistical")) {
                if($this->isBasisAcctGrpStat()){
                    $msg = "Provide financial account in basis, If Allocation method 'Dynamic-relative account financial' is selected";
                    $errorCode  = "GL-2497";
                }else{
                    $msg = "Provide statistical account in basis, If Allocation method 'Dynamic-relative account statistical' is selected";
                    $errorCode  = "GL-2498";
                }
                Globals::$g->gErr->addError(
                    $errorCode , __FILE__ . ':' . __LINE__, $msg
                );
                $ok = false;
            }
            $values['GLACCTGRPKEY'] = $groupkey;
        }

        $ok = $ok && $this->isValidGLBookid($values['REPORTINGBOOK']);

        $ok = $ok && $this->validateDimensionValues($values, $entityMaps);

        if (IsMCMESubscribed() && $values["ALLOCATIONMETHOD"] !== "Dynamic-relative account statistical") {
            $currency = '';
            $ok = $ok
                && $this->validateLocationBaseCurrency(
                    $values['LOCNO'], $values['CURRENCY'], 'BASIS', $currency
                );
            if ($ok && !empty($values['LOCNO'])) {
                $values['CURRENCY'] = $currency;
            }
        } else {
            if (empty($values['CURRENCY'])) {
                $values['CURRENCY'] = GetBaseCurrency();
            } elseif ($values['CURRENCY'] != GetBaseCurrency()) {
                $msg = "Given currency " . $values['CURRENCY'] . " is invalid ";
                Globals::$g->gErr->addIAError(
                    'GL-2320', __FILE__ . ':' . __LINE__, $msg, ['CURRENCY' => $values['CURRENCY']]
                );
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * * Validate and translate the input data for allocation target
     *
     * @param array $values
     *
     * @return bool
     */

    private function validateNTranslateTarget(
        &$values
    )
    {
        $ok = $this->validateGLAccountNGetAcctKey(
            $values['GLACCOUNTNO'], $glacctkey
        );
        if ($ok) {
            $values['GLACCOUNTKEY'] = $glacctkey;
        }

        $journalkey = '';
        $ok = $ok
            && $this->validateGLJournalNGetJournalKey(
                $values['JOURNALSYMBOL'], $journalkey
            );
        if ($ok) {
            $values['JOURNALKEY'] = $journalkey;
        }
        $ok = $ok
            && $this->validateDimensionValues(
                $values, $map, false
            );

        if ($ok && isset($values['EXCH_RATE_TYPE_ID']) && $values['EXCH_RATE_TYPE_ID'] != '') {

            $exTypeMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
            $values['EXCH_RATE_TYPE_ID'] = $exTypeMgr->GetExchangeRateTypeID($values['EXCH_RATE_TYPE_ID']);

            if (!isset($values['EXCH_RATE_TYPE_ID']) || $values['EXCH_RATE_TYPE_ID'] == '') {
                $msg = "Invalid Exchange Rate Type given";
                $corr = "Enter a valid Exchange Rate Type";
                Globals::$g->gErr->addError('GL-2322', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                $ok = false;
            }

        }

        return $ok;
    }

    /**
     * * Validate and translate the input data for allocation reverse
     *
     * @param array  $values
     *
     * @param string $delta
     *
     * @return bool
     */
    private function validateNTranslateReverse(
        &$values, $delta
    )
    {
        $ok = true;

        if (empty($values['USESOURCEACCOUNT'])) {
            $values['USESOURCEACCOUNT'] = 'false';
        } elseif ($values['USESOURCEACCOUNT'] === 'true' && !empty($values['GLACCOUNTNO'])) {
            $msg = "Either Use source account or GLAccount, Both is not allowed.";
            Globals::$g->gErr->addError(
                'GL-2323', __FILE__ . ':' . __LINE__, $msg
            );
            $ok = false;
        } elseif ($values['USESOURCEACCOUNT'] === 'false' && empty($values['GLACCOUNTNO'])) {
            $msg = "Either Use source account or GLAccount value is required.";
            Globals::$g->gErr->addError(
                'GL-2324', __FILE__ . ':' . __LINE__, $msg
            );
            $ok = false;
        }

        if ($ok && !empty($values['GLACCOUNTNO'])) {
            $glacctkey = '';
            $ok = $ok && $this->validateGLAccountNGetAcctKey($values['GLACCOUNTNO'], $glacctkey);

            if ($ok) {
                $values['GLACCOUNTKEY'] = $glacctkey;
            }
        } else {
            $values['GLACCOUNTKEY'] = '';
        }

        if ($ok && $delta == 'true' && $values['USESOURCEACCOUNT'] === 'false') {
            $ok = $this->deltaAllocationValidation($values['GLACCOUNTNO']);
        }

        $ok = $ok
            && $this->validateDimensionValues(
                $values, $map,false
            );
        return $ok;
    }


    /**
     * Delete
     *
     * @param string $ID record no
     *
     * @return bool
     */
    function Delete($ID)
    {
        $gErr = Globals::$g->gErr;
        $source = "GLAcctAllocationManager::Delete";

        $ok = $this->_QM->beginTrx($source);
        $ok = $ok && parent::Delete($ID);
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $tagName = $this->getTagName();
            $msg = "Could not delete {$tagName}!";
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2389": "GL-2325";
            $gErr->addError($errorCode, __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * merging objects schemas
     */
    function MergeObjectSchema()
    {
        parent::MergeObjectSchema();

        $schema = &$this->_schemas[$this->_entity]['schema'];

        $ptMap = GLEntryManager::getPTFieldsMap();
        $glDimOrder = GLSetupManager::getCustomDimensionKeyOrder();

        for ($i = 0; $i < count($glDimOrder); $i++) {
            $defId = $glDimOrder[$i];

            if ($defId != "") {
                $fieldPath = isl_strtoupper($ptMap[$defId]);

                $schema["FOCUS" . $fieldPath] = "focuscustdim" . ($i + 1);
            }
        }

        $showOnlyTheseDimensions = array_keys(
            IADimensions::getActiveGLDimensions(false)
        );

        $getAllDimensions = array_keys(
            IADimensions::getAllDimensions(false)
        );

        $inActiveDimensions = array_diff(
            $getAllDimensions, $showOnlyTheseDimensions
        );

        foreach ($inActiveDimensions as $val) {
            $dimensionName = "FOCUS" . isl_strtoupper($val);
            if (isset($schema[$dimensionName])) {
                unset($schema[$dimensionName]);
            }
        }
    }

    /**
     * merging objects fields
     */
    function MergeObjectFieldList()
    {

        parent::MergeObjectFieldList();

        $schemaobject = &$this->_schemas[$this->_entity]['object'];

        $ptMap = GLEntryManager::getPTFieldsMap();

        foreach ($ptMap as $fieldPath) {
            $schemaobject[] = "FOCUS" . isl_strtoupper($fieldPath);
        }
    }

    /**
     * merging objects fieldinfo
     */
    public function MergeFieldInfo()
    {
        parent::MergeFieldInfo();

        $fieldInfo = &$this->_schemas[$this->_entity]['fieldinfo'];
        $gle_def_id = Util_StandardObjectMap::getObjectId('glentry');
        $arrFields = Pt_DataFieldDefManager::getRawGLDimensionFields($gle_def_id);

        $custDimFieldsMap = GLEntryManager::getPTFieldsMap();
        $flippedCustDimFieldsMap = array_flip($custDimFieldsMap);

        $i = 1;
        $focusDimensions = [];
        foreach ($arrFields as $rawField) {
            $path = isl_strtoupper($rawField['FIELD_NAME']);
            $fullname = $rawField['DISPLAY_LABEL'];

            $def_id = $flippedCustDimFieldsMap[$path];
            if (!isset($def_id)) {
                continue;
            }
            $fPath = "FOCUS" . $path;
            // Add drop-down for filter type
            $filtertypenode = array(
                'fullname' => $fullname,
                'type' => array(
                    'ptype' => 'enum',
                    'type' => 'enum',
                    'validlabels' => array(
                        'IA.NOT_CONSIDERED',
                        'IA.PRESERVE_VALUES',
                        'IA.ALLOCATION_FOCUS',
                    ),
                    'validvalues' => array(
                        self::NOT_INVITED,
                        self::PRESERVED,
                        self::ALONG_FOR_RIDE,
                    ),
                    '_validivalues' => array(
                        self::NOT_INVITED_INTERNAL,
                        self::PRESERVED_INTERNAL,
                        self::ALONG_FOR_RIDE_INTERNAL,
                    ),
                ),
                'path' => $fPath,
                'id' => 50 + $i++,
                'udd' => true,
            );

            /** @noinspection PhpUndefinedVariableInspection */
            $events['change'] = "hideNotInvitedDimensions(this.meta);";
            $filtertypenode['events'] = $events;
            $fieldInfo[] = $filtertypenode;

            $focusDimensions[] = $fPath;
        }

        $this->appendTrueUpNonEditableFields($focusDimensions);
    }

    /**
     * function which checks for valid accountgroup
     *
     * @param string $acctgroupid
     * @param int    $acctgroupkey
     * @param bool   $isSource
     * @param string $skipNegative
     * @param int    &$normalBalance
     * @param string  $accumulation
     *
     * @return bool
     */
    public function validateAcctGrpNGetGrpKey(
        $acctgroupid, &$acctgroupkey, &$normalBalance, $isSource = false, $skipNegative = 'false', $accumulation = ""
    )
    {
        $path = 'Basis';
        $errorCode = 'GL-2393';
        if ($isSource) {
            $path = 'Source';
            $errorCode = 'GL-2394';
        }
        if (!empty($acctgroupid)) {
            if ((substr($acctgroupid, 0, 7) == 'System_')) {
                $msg = "Invalid account group in $path, IA account groups are not supported.";
                Globals::$g->gErr->addError(
                    $errorCode, __FILE__ . ':' . __LINE__, $msg
                );
                $ok = false;
            } else {

                if ($this->glAcctGrpInfo[$acctgroupid]) {
                    $result[0] = $this->glAcctGrpInfo[$acctgroupid];
                } else {
                    $filters = array(
                        'selects' => array('RECORDNO', 'MEMBERTYPE'),
                        'filters' => array(
                            array(
                                array(
                                    'NAME', '=', $acctgroupid
                                )
                            )
                        )
                    );
                    $gManagerFactory = Globals::$g->gManagerFactory;
                    $acctgrpMgr = $gManagerFactory->getManager('glacctgrp');
                    $result = $acctgrpMgr->GetList($filters);

                    // cache grp info now
                    $this->glAcctGrpInfo[$acctgroupid] = [
                        'RECORDNO' => $result[0]['RECORDNO'],
                        'MEMBERTYPE' => $result[0]['MEMBERTYPE']
                    ];
                }

                if (isset($result[0]['RECORDNO'])) {

                    // Validate for accounts type
                    if ($result[0]['MEMBERTYPE'] != 'Accounts'
                        && $result[0]['MEMBERTYPE'] != 'Groups'
                        && $result[0]['MEMBERTYPE'] != 'Category'
                        && $result[0]['MEMBERTYPE'] != 'Statistical Accounts'
                        && $result[0]['MEMBERTYPE'] != 'Statistical Category'
                    ){

                        $msg = sprintf("Supported account group types include Accounts, Group of accounts, Category,
                            Statistical account groups, and Statistical categories. 
                            Select a supported account group and try again in %s.", $path);

                        $errorCode = 'GL-2396';
                        if ($isSource) {
                            $errorCode = 'GL-2397';
                        }
                        Globals::$g->gErr->addError(
                            $errorCode, __FILE__ . ':' . __LINE__, $msg
                        );
                        $ok = false;
                    } else {
                        $ok = $this->validateGlAcctGrpMembers(
                            $acctgroupid, $isSource, $skipNegative, $normalBalance, $path, $accumulation
                        );

                        if ($ok) {
                            $acctgroupkey = $result[0]['RECORDNO'];
                        }
                    }
                } else {
                    $msg = "$path Account Group is invalid";
                    $errorCode = 'GL-2398';
                    if ($isSource) {
                        $errorCode = 'GL-2399';
                    }
                    Globals::$g->gErr->addError(
                        $errorCode, __FILE__ . ':' . __LINE__, $msg
                    );

                    $ok = false;
                }
            }
            if ($isSource) {
                $this->isSrcAcctGrpStat = in_array($this->glAcctGrpInfo[$acctgroupid]['MEMBERTYPE'] ?? "",
                    self::STAT_ACCT_GRP_MEMBER_TYPES);
            } else {
                $this->isBasisAcctGrpStat = in_array($this->glAcctGrpInfo[$acctgroupid]['MEMBERTYPE'] ?? "",
                    self::STAT_ACCT_GRP_MEMBER_TYPES);
            }
        } else {
            $msg = "$path Account Group is required";
            $errorCode = 'GL-2400';
            if ($isSource) {
                $errorCode = 'GL-2401';
            }
            Globals::$g->gErr->addError(
                $errorCode, __FILE__ . ':' . __LINE__, $msg
            );

            $ok = false;
        }
        return $ok;
    }

    /**
     * function which checks for valid glbookid
     *
     * @param string $glbookid
     *
     * @return bool
     */
    public function isValidGLBookid($glbookid)
    {
        $ok = true;
        if (!empty($glbookid)) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $bookMgr = $gManagerFactory->getManager('glbook');
            $getListVars = array(
                'selects' => array('BOOKID', 'TYPE'),
                'filters' => array(
                    array(
                        array('BOOKID', '=', $glbookid),
                    ),
                ),
            );
            $resultSet = $bookMgr->GetList($getListVars);

            if (empty($resultSet)) {
                $ok = false;
                $msg = "Invalid glbook " . $glbookid;
                Globals::$g->gErr->addIAError(
                    'GL-2326', __FILE__ . ':' . __LINE__, $msg, ['GLBOOKID' => $glbookid]
                );
            } else {
                $validBooks = array('Standard', 'User Defined', 'Gaap', 'Tax');
                if (!in_array($resultSet[0]['TYPE'], $validBooks)) {
                    $msg = "Invalid Book " . $glbookid
                        . " type."
                        . $glbookid;
                    Globals::$g->gErr->addIAError(
                        'GL-2327', __FILE__ . ':' . __LINE__, $msg, ['GLBOOKID' => $glbookid]
                    );
                    return false;
                }
            }
        } else {
            $ok = false;
            $msg = "glbook is required";
            Globals::$g->gErr->addError(
                'GL-2328', __FILE__ . ':' . __LINE__, $msg
            );
        }
        return $ok;
    }

    /**
     * function which checks for valid glbookid
     *
     * @param array $values
     *
     * @param string[]  $entityMaps
     *
     * @param bool  $includegrp
     *
     * @return bool
     */
    public function validateDimensionValues(
        &$values, &$entityMaps, $includegrp = true
    )
    {
        $ok = true;
        $showOnlyTheseDims = array_flip($this->showOnlyTheseDims);
        $gManagerFactory = Globals::$g->gManagerFactory;
        foreach ($this->allDimensionInfo as $dimKeyName => $dimKeyVal) {
            $isDimensionWithGrpInPath = self::dimensionWithGrpInPath($dimKeyName);
            if ($includegrp && $dimKeyName === 'location') {
                $path = 'LOCNO';
            } elseif ($includegrp && $dimKeyName === 'department') {
                $path = 'DEPTNO';
            } else {
                $partialPath = (isset($dimKeyVal['internalpath']) && !$isDimensionWithGrpInPath)
                    ? $dimKeyVal['internalpath'] : $dimKeyName;
                $partialPath = isl_strtoupper($partialPath);
                /*This is the path in the ent file*/
                $path = $includegrp ? $partialPath . 'ID' : 'OVERRIDE' . $partialPath;
            }

            if (isset($showOnlyTheseDims[$dimKeyName])
                && !isNullOrBlank($values[$path])) {
                $id = explode('--', $values[$path])[0];
                $dimMgr = $gManagerFactory->getManager($dimKeyVal['entity']);
                $dimId = $dimMgr->GetRecordNoFromVid($id);
                $dimKey = is_array($dimId) ? $dimId[0]['RECORD#'] : $dimId;
                if ($dimKey) {
                    if (!$isDimensionWithGrpInPath) {
                        $forDimKey = ':' . isl_strtolower($this->allStdDimensionInfo[$dimKeyName]['dimdbkey']);
                        $forDimGrpKey = ':' . isl_strtolower($this->reportStdDimensions[$dimKeyName]['dimgrpdbkey']);
                    } else {
                        $forDimKey = ':' . ($dimKeyName) . 'dimkey';
                        $forDimGrpKey = ':' . ($dimKeyName) . 'grpkey';
                    }
                    $values[$forDimKey] = $dimKey;
                    if ($includegrp) {
                        $values[$forDimGrpKey] = '';
                    }
                } else {
                    $dimKeyNameUC = isl_strtoupper($dimKeyName);
                    if ($includegrp) {
                        $dimGrpMgr = $gManagerFactory->getManager(
                            $dimKeyName . 'group'
                        );
                        $dimGrpId = $dimGrpMgr->GetRecordNoFromVid($id);
                        $dimGrpKey = is_array($dimGrpId)
                            ? $dimGrpId[0]['RECORD#'] : $dimGrpId;
                        if ($dimGrpKey) {
                            if (!$isDimensionWithGrpInPath) {
                                $forDimKey = ':' . isl_strtolower($this->allStdDimensionInfo[$dimKeyName]['dimdbkey']);
                                $forDimGrpKey = ':' . isl_strtolower($this->reportStdDimensions[$dimKeyName]['dimgrpdbkey']);
                            } else {
                                $forDimKey = ':' . ($dimKeyName) . 'dimkey';
                                $forDimGrpKey = ':' . ($dimKeyName) . 'grpkey';
                            }
                            $values[$forDimGrpKey] = $dimGrpKey;
                            $values[$forDimKey] = '';
                        } else {

                            $msg = "Invalid $dimKeyName/$dimKeyName Group given "
                                . $values[$path];
                            $dimKeyNameTokenVal = I18N::getSingleToken("IA." . $dimKeyNameUC);

                            Globals::$g->gErr->addIAError(
                                'GL-2447', __FILE__ . ':' . __LINE__, $msg, ['DIM_KEY_NAME_TOKEN' => $dimKeyNameTokenVal, 'VALUES_PATH' => $values[$path]]
                            );
                            $ok = false;
                        }
                    } else {
                        $dimKeyNameTokenVal = I18N::getSingleToken("IA." . $dimKeyNameUC);
                        $msg = "Invalid $dimKeyName given " . $values[$path];
                        Globals::$g->gErr->addIAError(
                            'GL-2506', __FILE__ . ':' . __LINE__, $msg, ['DIM_KEY_NAME' => $dimKeyNameTokenVal, 'VALUES_PATH' => $values[$path]]
                        );
                        $ok = false;
                    }
                }
            } else {
                unset($values[$path]);
            }
        }

        if ($ok && $includegrp && IsMultiEntityCompany()) {
            $locationid = explode('--', $values['LOCNO'])[0];

            $ok = $ok && $this->getEntityMapForGivenLocOrLocGroup($entityMaps, $locationid);
        }

        return $ok;
    }

    /**
     * @param string[] $entityMaps
     * @param string $locationid
     *
     * @return bool
     */
    public function getEntityMapForGivenLocOrLocGroup(&$entityMaps, $locationid)
    {
        $ok = true;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $locMembersTemp = [];
        $label = 'Location';
        if (!empty($locationid)) {
            $locationMgr = $gManagerFactory->getManager('location');
            $queryParams = array(
                'selects' => array('RECORDNO', 'STATUS'),
                'filters' => array(
                    array(
                        array('LOCATIONID', '=', $locationid),
                    )
                ),
            );
            $result = $locationMgr->GetList($queryParams);

            if (!empty($result)) {
                if ($result[0]['STATUS'] != 'active') {
                    $msg = "Given $label " . $locationid . " is not active.";
                    Globals::$g->gErr->addIAError(
                        'GL-2329', __FILE__ . ':' . __LINE__, $msg, ['LOCATIONID' => $locationid]
                    );
                    $ok = false;
                }

                $locMembersTemp[] = $result[0]['RECORDNO'];
            } else {

                $locationGrpMgr = $gManagerFactory->getManager('locationgroup');
                $resultsActiveLocs = $locationGrpMgr->GetLocationGrpMembers($locationid);

                if (empty($resultsActiveLocs)) {
                    $msg = "Selected $label group " . $locationid . " has no active $label";
                    Globals::$g->gErr->addIAError(
                        'GL-2330', __FILE__ . ':' . __LINE__, $msg, ['LOCATIONID' => $locationid]
                    );
                    $ok = false;
                }

                if ($ok) {
                    foreach ($resultsActiveLocs as $locval) {
                        $locMembersTemp[] = $locval;
                    }
                }
            }
        }

        $resultLocMap = self::getParentLocationMap($locMembersTemp, 'LOCATION#');
        $locMembers = array_values($resultLocMap);

        $tentityMaps = [];
        $entityMaps = [];
        foreach ($locMembers as $tempLoc) {
            if ($tempLoc != "" && !isset($tentityMaps[$tempLoc])) {
                $tentityMaps[$tempLoc] = 1;
                $entityMaps[] = $tempLoc;
            }
        }

        return $ok;
    }

    /**
     * Utility method to retrieve the parent location map for the given child
     * locations.
     *
     * @param array  $childLoations
     *
     * @param string $filter
     *
     * @return array
     */
    public static function getParentLocationMap($childLoations, $filter)
    {
        $parentlocMap = array();
        if (!empty($childLoations)) {
            $cny = GetMyCompany();
            $stmt
                = array(
                "select $filter, ENTITY# from v_locationent where cny# = :1 ",
                $cny
            );
            $stmt = PrepINClauseStmt($stmt, $childLoations, " and $filter ");
            $resultSet = QueryResult($stmt);

            // Loop over the result the form the parent and child location map detail
            foreach ($resultSet as $loc) {
                $parentlocMap[$loc[$filter]] = $loc['ENTITY#'];
            }
        }
        return $parentlocMap;
    }

    /**
     * function which checks for valid glaccount
     *
     * @param string $glacctno
     *
     * @param int    $glacctkey
     *
     * @return bool
     */
    public function validateGLAccountNGetAcctKey($glacctno, &$glacctkey)
    {
        $ok = false;
        if (!empty($glacctno)) {
            $glacctno = explode('--', $glacctno)[0];
            $gManagerFactory = Globals::$g->gManagerFactory;

            $glaccountMgr = $gManagerFactory->getManager('baseaccount');
            $queryParams = array(
                'selects' => array('RECORDNO', 'ACCOUNT_TYPE', 'STATUS', 'STATISTICAL', 'SUBLEDGERCONTROLON', 'CLOSEABLE'),
                'filters' => array(
                    array(
                        array('ACCT_NO', '=', $glacctno),
                    )
                ),
            );
            $result = $glaccountMgr->GetList($queryParams);
            if (!empty($result)) {
                if ($result[0]['STATUS'] != 'T') {
                    $msg = "Given GLAccount " . $glacctno . " is inactive";
                    Globals::$g->gErr->addIAError(
                        'GL-2331', __FILE__ . ':' . __LINE__, $msg, ['GLACCTNO' => $glacctno]
                    );
                } elseif ($result[0]['SUBLEDGERCONTROLON'] == 'true') {
                    $msg = "Direct posting to  GLAccount " . $glacctno . " is not allowed !";
                    Globals::$g->gErr->addIAError(
                        'GL-2332', __FILE__ . ':' . __LINE__, $msg, ['GLACCTNO' => $glacctno]
                    );
                } elseif ($result[0]['CLOSEABLE'] == 'R') {
                    $tagName = $this->getTagName();
                    $msg = sprintf("The account %s designated as a Closed to account.
                        Account with the period end closing type Closed to are not supported in %ss.", $glacctno, $tagName);
                    $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2390": "GL-2333";
                    Globals::$g->gErr->addIAError(
                        $errorCode, __FILE__ . ':' . __LINE__, $msg, ['GLACCTNO' => $glacctno]
                    );
                } elseif ($this->isSrcAcctGrpStat() && ($result[0]['STATISTICAL'] != 'T')) {
                    $msg = sprintf("The Source and Target  types do not match. Account %s contains a non-statistical account.
                         Because the Source account is designated as Statistical, select a Statistical account or 
                         account group for the Target and try again.", $glacctno);

                    Globals::$g->gErr->addIAError(
                        'GL-2334', __FILE__ . ':' . __LINE__, $msg, ['GLACCTNO' => $glacctno]
                    );
                } elseif (!$this->isSrcAcctGrpStat() && ($result[0]['STATISTICAL'] == 'T')) {
                    $msg = sprintf("The Source and Target selection types do not match. Account %s contains a statistical account.
                         Because the Source account is designated as Financial, select a Financial account or
                          account group as the Target and try again.", $glacctno);

                    Globals::$g->gErr->addIAError(
                        'GL-2335', __FILE__ . ':' . __LINE__, $msg, ['GLACCTNO' => $glacctno]
                    );
                } else {
                    $glacctkey
                        = $result[0]['RECORDNO'];
                    $ok = true;
                }
            } else {
                $msg = "Given GLAccount " . $glacctno . " is not present in the system.";
                Globals::$g->gErr->addIAError(
                    'GL-2336', __FILE__ . ':' . __LINE__, $msg, ['GLACCTNO' => $glacctno]
                );
            }
        }
        return $ok;
    }

    /**
     * @param string $journalType
     * @return bool
     */
    protected function isJournalTypeNotAllowed(/** @noinspection PhpUnusedParameterInspection */ $journalType){
        return false;
    }
    /**
     * function which checks for valid journal
     *
     * @param string $journalsymbol
     *
     * @param int    $journalkey
     *
     * @return bool
     */
    public function validateGLJournalNGetJournalKey(
        $journalsymbol, &$journalkey
    )
    {
        $ok = false;
        if (!empty($journalsymbol)) {
            $journalsymbol = explode('--', $journalsymbol)[0];
            $qry = "SELECT record# as RECORDNO FROM basejournal WHERE cny#=:1 and symbol = :2";
            $cny = GetMyCompany();
            $resultJrnl = QueryResult([$qry, $cny, $journalsymbol]);

            if (empty($resultJrnl)) {
                $msg = "Journal: " . $journalsymbol . " is not valid.";
                Globals::$g->gErr->addIAError(
                    'GL-2337', __FILE__ . ':' . __LINE__, $msg, ['JOURNALSYMBOL' => $journalsymbol]
                );
            } else {
                $bookjrnlStmt[0]
                    = "select a.bookid,b.useradjbookkey, b.status,glbk.record#,glbk.type from bookjournals a, basejournal b, glbook glbk where a.journalkey = b.record# and a.cny# = b.cny# and glbk.cny# = a.cny# and glbk.bookid = a.bookid and b.cny# = :1 and b.record# = :2";
                $bookjrnlStmt[1] = $cny;
                $bookjrnlStmt[2] = $resultJrnl[0]['RECORDNO'];
                $result = QueryResult($bookjrnlStmt);
                $isUserAdjBook = false;
                $isJrnlStatus = 'F';

                if (!empty($result)) {
                    foreach ($result as $jrlVal) {
                        $isJrnlStatus = $jrlVal['STATUS'];

                        if (!$this->isSrcAcctGrpStat() && !$isUserAdjBook && !empty($jrlVal['USERADJBOOKKEY'])) {
                            $isUserAdjBook = true;
                        } else if ($this->isJournalTypeNotAllowed($jrlVal['TYPE'])) {
                            $msg = "Given journal " . $journalsymbol . " is not allowed!";
                            if($this->isSrcAcctGrpStat()){
                                $msg2 =  "When Source Account group has statistical account/ category
                                                            and we select non-statistical journal in target";
                                $errorCode  = "GL-2499";
                            }else{
                                $msg2 = "Only user-defined book journal is allowed!";
                                $errorCode  = "GL-2500";
                            }
                            Globals::$g->gErr->addIAError($errorCode , __FILE__ . ':' . __LINE__, $msg, ['JOURNALSYMBOL' => $journalsymbol], $msg2, []);
                            // code change review
                            break;
                        } else {
                            $journalkey = $resultJrnl[0]['RECORDNO'];
                            $ok = true;
                        }
                    }

                    if ($isUserAdjBook) {
                        if ($isJrnlStatus != 'T') {
                            $msg = "Given journal " . $journalsymbol . " is inactive";
                            Globals::$g->gErr->addIAError('GL-2338', __FILE__ . ':' . __LINE__, $msg, ['JOURNALSYMBOL' => $journalsymbol]);
                        } else {
                            $journalkey = $resultJrnl[0]['RECORDNO'];
                            $ok = true;
                        }
                    }
                } else {
                    $msg = "journal " . $journalsymbol . " does not exists";
                    Globals::$g->gErr->addIAError('GL-2339', __FILE__ . ':' . __LINE__, $msg, ['JOURNALSYMBOL' => $journalsymbol]);
                }
            }
        } else {
            $msg = "journal is required";
            Globals::$g->gErr->addError('GL-2340', __FILE__ . ':' . __LINE__, $msg);
        }
        return $ok;
    }

    /**
     * Update the latest template version key
     *
     * @param int $oldversionkey
     * @param int $latestversion
     *
     * @return bool false if error else true
     */
    private function updateTemplateLatestVersionKey($latestversion, $oldversionkey)
    {
        // to update the latest revision key for the current records
        $qry
            = "UPDATE glacctallocation SET latestversionkey = :1 
                WHERE record# = :2 
                AND cny# = :3";
        $ok = ExecStmt(
            array($qry, $latestversion, $oldversionkey, GetMyCompany())
        );

        // to update the latest revision key for the old records
        $qry
            = "UPDATE glacctallocation SET latestversionkey = :1 
                WHERE latestversionkey = :2 
                AND cny# = :3";
        $ok = $ok
            && ExecStmt(
                array($qry, $latestversion, $oldversionkey, GetMyCompany())
            );

        //update allocation group members to latest version of the allocation definition here
        $qry
            = "UPDATE glacctallocationgrpmembers SET glacctallocationkey = :1 
                WHERE glacctallocationkey = :2 
                AND cny# = :3";
        $ok = $ok
            && ExecStmt(
                array($qry, $latestversion, $oldversionkey, GetMyCompany())
            );
        return $ok;
    }

    /**
     * Get the recordno of the latest template.
     *
     * @param string $templateId the template ID
     *
     * @return int the recordno of the lastest template
     */
    public function getLastestTemplateKey($templateId)
    {
        $filter = array(
            'selects' => array('RECORDNO'),
            'filters' => array(
                array(
                    array('ACCTALLOCATIONID', '=', $templateId),
                    array('LATESTVERSIONKEY', 'IS NULL')
                )
            )
        );
        $result = $this->GetList($filter);
        return $result[0]['RECORDNO'];
    }

    /**
     * check whether templateid with same name already exists
     *
     * @param string $templateId the template ID
     *
     * @return bool
     */
    public function isTemplateExists($templateId)
    {
        $ok = false;
        $acctAllocationIDFieldName = 'ACCTALLOCATIONID';
        $allocType = $this->getAllocType();

        $values = [$acctAllocationIDFieldName => $templateId];
        $this->trimSpacesAndRestrictHyphens($values);
        $filter = array(
            'selects' => array('RECORDNO'),
            'filters' => array(
                array(
                    array($acctAllocationIDFieldName, '=', $values[$acctAllocationIDFieldName]),
                    array('ALLOCTYPE', '=', $allocType)
                )
            )
        );
        $result = $this->GetList($filter);
        if (isset($result[0]['RECORDNO'])) {
            $ok = true;
        }
        return $ok;
    }

    /**
     * @param array $allocDefObj
     *
     * @return bool
     */
    public function validateAllocationDefForRun($allocDefObj)
    {
        $ok = $this->transformValues($allocDefObj);

        $ok = $ok && $this->validateNTranslateInput($allocDefObj);

        return $ok;
    }

    /**
     * Transform the values to required format for manager validations
     * Since we are expecting one-to-one relationship with owned object.
     * Let's keep only one format.
     *
     * @param array $values
     *
     * @return bool
     */
    private function transformValues(&$values)
    {
        $objs = $this->GetOwnedObjects();
        foreach ($objs as $obj) {
            $path = $obj["path"];
            if (isset($values[$path][0])) {
                $ownedObjData = $values[$path][0];
                unset($values[$path]);
                $values[$path] = $ownedObjData;
            }
        }
        return true;
    }

    /**
     * check for overrided dimensions values
     *
     * @param array  $values
     *
     * @param string $indexName
     *
     * @return bool
     */
    private function validateOverrideDimensions(
        &$values, $indexName
    )
    {
        $actualName = $indexName;
        $indexName = "GLACCTALLOCATION" . $indexName;
        $ok = true;
        $ptMap = GLEntryManager::getPTFieldsMap();
        $showOnlyTheseDimensions = array_flip($this->showOnlyTheseDims);
        foreach ($this->allDimensionInfo as $dimKey => $val) {
            $path = "FOCUS" . isl_strtoupper($dimKey);
            $vallabel = $val['label'];
            $overridePath = "OVERRIDE" . isl_strtoupper($dimKey);
            if (isset($showOnlyTheseDimensions[$dimKey])) {
                if (!empty($values[$path])
                    && ($actualName !== self::OVERIDE_TYPE_REVERSE && !$this->isDimTreatmentAllowedForTarget($values[$path]))
                    && !empty($values[$indexName][$overridePath])) {
                    $ok = false;
                    $msg = "Dimension " . $vallabel
                        . " is not allowed to override in the $actualName pool";
                    $vallabelTxt = I18N::getSingleToken($vallabel);
                    if($actualName === 'SOURCE'){
                        $errorCode = 'GL-2341';
                    }elseif($actualName === 'BASIS'){
                        $errorCode = 'GL-2448';
                    }else{
                        $errorCode = 'GL-2449';
                    }
                    Globals::$g->gErr->addIAError(
                        $errorCode, __FILE__ . ':' . __LINE__, $msg, ['VAL' => $vallabelTxt]
                    );
                }
            } else {
                unset($values[$path]);
            }
        }

        if ($ok) {
            foreach ($ptMap as $val) {
                $path = "FOCUS" . isl_strtoupper($val);
                $overridePath = isl_strtoupper($val);
                if (!empty($values[$path])
                    && ($actualName !== self::OVERIDE_TYPE_REVERSE && !$this->isDimTreatmentAllowedForTarget($values[$path]))
                    && !empty($values[$indexName][$overridePath])) {
                    $ok = false;
                    $msg = "Dimension " . $val
                        . " is not allowed to override in the $actualName pool";
                        $vallabelTxt = I18N::getSingleToken($val);

                    if($actualName === 'SOURCE'){
                        $errorCode = 'GL-2341';
                    }elseif($actualName === 'BASIS'){
                        $errorCode = 'GL-2448';
                    }else{
                        $errorCode = 'GL-2449';
                    }
                    Globals::$g->gErr->addIAError(
                        $errorCode, __FILE__ . ':' . __LINE__, $msg, ['VAL' => $vallabelTxt]
                    );
                }
            }
        }

        return $ok;
    }

    /**
     * Function will return account group members
     *
     * @param string $accountGrpName
     * @param bool   $isSource
     * @param string $skipNegative
     * @param int    &$normalBalance
     * @param string $path
     * @param string $accumulation
     *
     * @return bool
     */
    private function validateGlAcctGrpMembers(
        $accountGrpName, $isSource, $skipNegative, &$normalBalance, $path, $accumulation
    )
    {
        $ok = true;
        $glAcctGrpsStmt[0] = "SELECT acct_no,account_type,status, normal_balance, closeable, statistical";

        if ($path === 'Source') {
            $glAcctGrpsStmt[0] .= ', subledgercontrolon';
        }

        $glAcctGrpsStmt[0] .= " FROM baseaccount baseaccount  where baseaccount.cny# = :1" ;

        $glAcctGrpsStmt[0] .= " and exists (";
        $glAcctGrpsStmt[0] .= " select 1 " .
            " from glacctgrpranges ga " .
            " where baseaccount.cny# = ga.cny# " .
            " and baseaccount.acct_no between ga.rangefrom and ga.rangeto " .
            " and (   " .
            " ga.parentkey in " .
            " (   select child# from glacctgrpmembers  " .
            "     start with parent# in  " .
            "     ( select record# from glacctgrp " .
            "       where name = :2 and howcreated = 'U' " .
            "       and cny# = :1" .
            "     ) and cny# = :1 " .
            "     connect by prior child# = parent# and cny# = :1 " .
            " ) or ga.parentkey in " .
            " ( select record# from glacctgrp " .
            "   where name = :2 and howcreated = 'U' and cny# = :1 " .
            " ) " .
            " ) ";
        $industry = GetMyIndustryType();
        if ($industry != '') {
            // for category group
            $glAcctGrpsStmt[0] .= " union all " .
                " select 1 " .
                " from coacatmembers ga " .
                " where baseaccount.cny# = ga.cny# " .
                " and baseaccount.categorykey = ga.categorykey " .
                " and (   " .
                " ga.parent# in " .
                " (   select child# from glacctgrpmembers  " .
                "     start with parent# in  " .
                "     ( select record# from glacctgrp " .
                "       where name = :2 and howcreated = 'U' " .
                "       and cny# = :1" .
                "     ) and cny# = :1 " .
                "     connect by prior child# = parent# and cny# = :1 " .
                " ) or ga.parent# in " .
                " ( select record# from glacctgrp " .
                "   where name = :2 and howcreated = 'U' and cny# = :1 " .
                " ) " .
                " ) ";
        }
        $glAcctGrpsStmt[0] .= ")";
        $glAcctGrpsStmt[1] = GetMyCompany();
        $glAcctGrpsStmt[2] = $accountGrpName;
        $grpAcctGrpsResult = QueryResult($glAcctGrpsStmt);

        $foundActive = false;
        $prvTrType = $grpAcctGrpsResult[0]['NORMAL_BALANCE'];
        $prvAccountType = $grpAcctGrpsResult[0]['ACCOUNT_TYPE'];
        $prvIsStatistical = $grpAcctGrpsResult[0]['STATISTICAL'];
        $tagName = $this->getTagName();
        foreach ($grpAcctGrpsResult as $grpVal) {

            // reatained earing accounts are not allowed
            if ($grpVal['CLOSEABLE'] === 'R') {
                $ok = false;
                $msg = "The account group $accountGrpName has an account designated as a Closed to account. "
                    . "Accounts with the period end closing type Closed to are not supported in {$tagName}s.";
                    $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2391": "GL-2342";
                Globals::$g->gErr->addIAError(
                    $errorCode, __FILE__ . ':' . __LINE__, $msg, ['ACCOUNTGRPNAME' => $accountGrpName]
                );
                break;
            }
            // allow only same account type groups
            $cond = ($path == 'Basis') &&
                (($accumulation == 'Ending Balance') && ($prvAccountType !== $grpVal['ACCOUNT_TYPE']));

            if ($cond || $prvIsStatistical !== $grpVal['STATISTICAL']) {
                $ok = false;
                $msg = "The account group $accountGrpName has mixed account types. "
                    . "Mixed account types are not supported in {$tagName}s. You must use accounts of the same type.";
                    $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2395": "GL-2343";
                Globals::$g->gErr->addIAError(
                    $errorCode, __FILE__ . ':' . __LINE__, $msg, ['ACCOUNTGRPNAME' => $accountGrpName]
                );
                break;
            }

            if ($grpVal['STATUS'] === 'T') {
                $foundActive = true;

                if ($isSource) {
                    $this->accountGrpMembers[$grpVal['ACCT_NO']] = $grpVal['STATUS'];
                }
            }

            if (isset($grpVal['SUBLEDGERCONTROLON']) && $grpVal['SUBLEDGERCONTROLON'] == 'T') {
                $accountNo = $grpVal['ACCT_NO'];
                $msg = "In {$path} : The account group '$accountGrpName' has a account no {$accountNo} that have " .
                    "restriction for direct gl posting";
                $cor = "To correct this, select only accounts with the same normal balance type.";
                Globals::$g->gErr->addIAError(
                    'GL-2344', __FILE__ . ':' . __LINE__, $msg,['PATH'=>$path,'ACCOUNTGRPNAME'=>$accountGrpName,'ACCOUNTNO'=>$accountNo], $cor,[]
                );
                $ok = false;
            }

            if ($skipNegative === 'true') {
                if ($prvTrType !== $grpVal['NORMAL_BALANCE']) {
                    $msg = "The account group $accountGrpName has a combination of accounts that have " .
                        "normal balance types Debits and Credits. Your selection of Skip negative accounts can’t be applied at this time.";
                    $cor = "To correct this, non control over control account.";
                    Globals::$g->gErr->addIAError(
                        'GL-2345', __FILE__ . ':' . __LINE__, $msg, ['ACCOUNTGRPNAME' => $accountGrpName], $cor,[]
                    );
                    $ok = false;
                }
            }

            $normalBalance = $grpVal['NORMAL_BALANCE'];

            $prvTrType = $grpVal['NORMAL_BALANCE'];
            $prvAccountType = $grpVal['ACCOUNT_TYPE'];
            $prvIsStatistical = $grpVal['STATISTICAL'];
        }

        // return NORMAL_BALANCE
        if (!$ok) {
            unset($normalBalance);
        }

        if ($ok && !$foundActive) {
            $msg = "Given account group $accountGrpName does not have any active accounts in it.";
            Globals::$g->gErr->addIAError(
                'GL-2346', __FILE__ . ':' . __LINE__, $msg, ['ACCOUNTGRPNAME' => $accountGrpName]
            );
            $ok = false;
        }

        return $ok;
    }

    /**
     * Check whether account allocaiton module is configuered
     *
     * @param string $operation
     * @param array  $values
     *
     * @return bool
     */
    public function API_Validate($operation, &$values = null)
    {
        $ok = GLAcctAllocationRunManager::API_isAccountAlloctionSubscribed();

        $ok = $ok && parent::API_Validate($operation, $values);

        return $ok;
    }

    /**
     * get active gl custom dimensions
     *
     * @return array
     */
    private function getActiveGLCustomDimensionInfo()
    {
        $result = array();
        $objDef = Pt_StandardUtil::getObjDef('glentry');
        if ($objDef) {
            $platformRels = Pt_StandardUtil::getRelationshipFields($objDef);
            foreach ($platformRels as $ptRel) {
                if ( ( $relDef = $ptRel->getRelationshipDef() ) && $relDef->isGLDimension() ) {
                    $result[isl_strtoupper(
                        $ptRel->getFieldName()
                    )]
                        = 1;
                }
            }
        }
        return $result;
    }

    /**
     * get the base currency for given location/group
     *
     * @param string $location
     *
     * @param string $givenCurrency
     *
     * @param string $index
     *
     * @param string $currency
     *
     * @return bool
     */
    public function validateLocationBaseCurrency(
        $location, $givenCurrency, $index, &$currency
    )
    {
        $ok = true;
        $label = 'Location';

        if (!empty($location)) {

            $gManagerFactory = Globals::$g->gManagerFactory;
            $locationid = explode('--', $location)[0];
            $locationGrpMgr = $gManagerFactory->getManager(
                'locationgroup'
            );
            $recordNo = $locationGrpMgr->GetRecordNoFromVid($locationid);
            if (!empty($recordNo)) {
                $grpMembers = $locationGrpMgr->getGroupMembers(
                    $recordNo, false
                );
                if (hasSameBaseCurrency($grpMembers['MEMBERIDS'])) {
                    $grpMemLocationID = explode(
                        '--', $grpMembers['MEMBERIDS'][0]
                    )[0];
                    $currency = GetLocationBaseCurrency($grpMemLocationID);
                } else {
                    $msg = "Invalid $label group " . $locationid
                        . ", this group contains members with different currencies";
                    Globals::$g->gErr->addIAError(
                        'GL-2347', __FILE__ . ':' . __LINE__, $msg, ['LOCATIONID' => $locationid]
                    );
                    $ok = false;
                }
            } else {
                $currency = GetLocationBaseCurrency($locationid);
            }
            if ($index === 'BASIS') {
                if ($ok && !empty($givenCurrency) && ($givenCurrency != $currency)) {
                    $msg = "Invalid currency " . $givenCurrency
                        . " for the location $locationid";
                    Globals::$g->gErr->addIAError(
                        'GL-2348', __FILE__ . ':' . __LINE__, $msg, ['GIVENCURRENCY' => $givenCurrency, 'LOCATIONID' => $locationid]
                    );
                    $ok = false;
                }
            }
        } else {
            $msg = $this->getMessage("GL-2501", [$label, $index]);
            Globals::$g->gErr->addIAError(
                'GL-2501', __FILE__ . ':' . __LINE__, $msg, ['LABEL' => $label]
            );
            $ok = false;
        }
        if ($ok && $index === 'SOURCE') {
            $this->sourceCurrency = $currency;
        }
        return $ok;
    }

    /**
     * @param string $errId
     * @param string[] $params
     *
     * @return string
     */
    protected static function getMessage(string $errId, array $params = [])
    {
        static $messages = null;

        if ($messages == null) {
            $messages = [
                "GL-2501" => "Value for %s Dimension is required in funding source dimension.",
                "GL-2502" => "%s dimension value for Temp restricted and Unrestricted designation should belongs to same entity if balance by entity is enabled.",
            ];
        }

        $returnMessage = $messages[$errId] ?? "";
        if ($returnMessage !== "") {
            $returnMessage = vsprintf($returnMessage, $params);
        }

        return $returnMessage;
    }

    /**
     * get the base currency for given location/group
     *
     * @param array $values
     *
     * @param int   $oldRecordNo
     *
     * @return bool
     */
    public function validateAllocationVersion($values, $oldRecordNo)
    {
        $ok = true;
        $result = $this->Get($oldRecordNo);
        $journal = $values['GLACCTALLOCATIONTARGET'][0]['JOURNALSYMBOL']
            ?? $values['GLACCTALLOCATIONTARGET']['JOURNALSYMBOL'];

        if (!empty($result) && !empty($journal)) {
            $journal = explode('--', $journal)[0];
            $oldJournal = $result['GLACCTALLOCATIONTARGET'][0]['JOURNALSYMBOL'];
            if ($oldJournal != $journal) {
                $msg
                    = "Changing Reporting/Adjusting books or journals is not allowed, Kindly create new allocation definition";
                Globals::$g->gErr->addError(
                    'GL-2349', __FILE__ . ':' . __LINE__, $msg
                );
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * Check if the allocation definition is in running/queued state
     *
     * @param int $allocationKey
     *
     * @return bool
     */
    protected function checkAllocationDefinitionRuns($allocationKey)
    {
        $ok = true;

        // check if the allocation definition has some runs in queue/progress
        $gManagerFactory = Globals::$g->gManagerFactory;
        $glacctallocrunMgr = $gManagerFactory->getManager(
            'glacctallocationrun'
        );
        $filter = array(
            'selects' => array('RECORDNO'),
            'filters' => array(
                array(
                    array('GLACCTALLOCATIONKEY', '=', $allocationKey),
                    array(
                        'STATE', 'IN',
                        array(GLAcctAllocationRunManager::INPRROGRESS_STATE, GLAcctAllocationRunManager::QUEUE_STATE)
                    ),
                )
            )
        );
        $result = $glacctallocrunMgr->GetList($filter);

        if (!empty($result)) {
            $tagName = $this->getTagName();
            $msg = "Runs for the {$tagName} definition is either in Process/Queued!, Kindly try updating after sometime.";
            $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2392": "GL-2350";
            $gErr = Globals::$g->gErr;
            $gErr->addError($errorCode, __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        }


        if ($ok) {
            $query[0]
                = "SELECT glallocmem.glacctallocationkey
                    FROM glacctallocationrun glallocrun,
                      glacctallocationgrp glgrp,
                      glacctallocationgrpmembers glallocmem
                    WHERE glallocrun.cny# = :1
                    AND glallocrun.cny# = glgrp.cny#
                    AND glgrp.cny# = glallocmem.cny#
                    AND glallocrun.glacctallocationgrpkey = glgrp.record#
                    AND glgrp.record# = glallocmem.glacctallocationgrpkey
                    AND glallocrun.state IN ('I','Q')
                    AND glallocmem.glacctallocationkey = " . $allocationKey;

            $query[1] = GetMyCompany();
            $membersResult = QueryResult($query);
            if (!empty($membersResult)) {
                $tagName = $this->getTagName();
                $msg = "Runs for the {$tagName} definition is either in Process/Queued!, Kindly try updating after sometime.";
                $gErr = Globals::$g->gErr;
                $errorCode = ($tagName === self::ACCOUNT_ALLOCATION) ? "GL-2392": "GL-2350";
                $gErr->addError($errorCode, __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * Function to check whether given AllocationID should be same as in DB
     *
     * @param string $status
     *
     * @param string $allocationID
     *
     * @param int    $recordno
     *
     * @return bool
     */
    private function validateAllocationID($status, $allocationID, $recordno)
    {
        $ok = true;
        $filter = array(
            'selects' => array('ACCTALLOCATIONID', 'STATUS'),
            'filters' => array(
                array(
                    array('RECORDNO', '=', $recordno),
                )
            )
        );
        $result = $this->GetList($filter);
        if ($result[0]['ACCTALLOCATIONID'] != $allocationID) {
            $msg = " Changing the AllocationID is not allowed.";
            $gErr = Globals::$g->gErr;
            $gErr->addError('GL-2351', __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        } elseif ($result[0]['STATUS'] === 'active' && $status === 'inactive') {
            //Do not allow to inactive allocation, if it already involved in any group
            $gManagerFactory = Globals::$g->gManagerFactory;
            $glacctallocationgrpmemberMgr = $gManagerFactory->getManager('glacctallocationgrpmember');

            $filter = array(
                'selects' => array('GLACCTALLOCATIONGRPID'),
                'filters' => array(
                    array(
                        array('GLACCTALLOCATIONID', '=', $allocationID),
                    )
                ),
            );
            $result = $glacctallocationgrpmemberMgr->GetList($filter);
            if (isset($result[0]) && isset($result[0]['GLACCTALLOCATIONGRPID'])) {
                $allocGroupIds = array_column($result, 'GLACCTALLOCATIONGRPID');
                $allocGroupIdsStr = array_pop($allocGroupIds);
                if (isNonEmptyArray($allocGroupIds)) {
                    $allocGroupIdsStr = implode(", ", $allocGroupIds) . ", and " . $allocGroupIdsStr;
                }

                $msg = "You cannot inactivate the allocation definition because it's already associated with a allocation group.";
                $msg2 = "To proceed, first remove from the associated allocation groups.";
                $corrMsg = sprintf('Associated allocation groups: %s', $allocGroupIdsStr);
                $gErr = Globals::$g->gErr;
                $gErr->addIAError('GL-2352', __FILE__ . ':' . __LINE__, $msg, [], $msg2, [], $corrMsg, ['ALLOC_GROUP_IDS_STR' => $allocGroupIdsStr]);
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * Check if the reverse account is present in the source pool account group
     *
     * @param string $accountNo
     *
     * @return bool
     */
    private function deltaAllocationValidation($accountNo)
    {
        $ok = true;
        if (!isset($this->accountGrpMembers[$accountNo])) {
            $msg = " If activity delta is enabled, then account group used in source pool must include reverse account "
                . $accountNo;
            $gErr = Globals::$g->gErr;
            $gErr->addIAError('GL-2353', __FILE__ . ':' . __LINE__, $msg, ['ACCOUNTNO' => $accountNo]);
            $ok = false;
        }
        return $ok;
    }

    /**
     * Validate given timeperiod
     *
     * @param string $timeperiod
     * @param string $timeperiodKey
     * @param bool $restrictCustTimePrd
     *
     * @return bool
     */
    private function validateTimePeriods($timeperiod, &$timeperiodKey, $restrictCustTimePrd = false)
    {
        $ok = true;
        $restrictCustTimePrdCondFail = false;
        $timeperiodKey = $this->availableTimeperiods[$timeperiod];
        if ($restrictCustTimePrd) {
            $restrictCustTimePrdCondFail = isset($this->customTimePeriodIds[$timeperiodKey]);
        }

        if (!isset($this->availableTimeperiods[$timeperiod]) || $restrictCustTimePrdCondFail) {
            $msg = " Given time period $timeperiod is invalid.";
            $gErr = Globals::$g->gErr;
            $gErr->addIAError('GL-2354', __FILE__ . ':' . __LINE__, $msg, ['TIMEPERIOD' => $timeperiod]);
            $ok = false;
        }

        return $ok;
    }

    /**
     * get default available timeperiods
     *
     * @return array
     */
    public function getAllocationTimeperiods()
    {
        $reportingPeriods = GetPeriodMap(true, '', true);
        $reportingPeriods = array_flip($reportingPeriods);
        $timeperiodValues = array();

        foreach ($reportingPeriods as $name => $timeVal) {
                $timeperiodValues[$name] = $timeVal;
        }

        return $timeperiodValues;
    }

    /**
     * Validate given timeperiod against project dimension filters//
     *
     * @param string $projectDimFilter
     *
     * @param string $timeperiod
     *
     * @return bool
     */
    private function validateInceptionToPeriod($projectDimFilter, $timeperiod)
    {
        $ok = true;
        if (!empty($projectDimFilter) && $projectDimFilter !== self::ALONG_FOR_RIDE
            && (($timeperiod === self::INCEPTION_TO_DATE)||($timeperiod === self::INCEPTION_TO_CURRENT_MONTH))) {

            $msg = "Dimension filter Project value should be " . self::ALONG_FOR_RIDE . " to use "
                . $timeperiod . " time period.";
            $gErr = Globals::$g->gErr;
            $gErr->addIAError('GL-2355', __FILE__ . ':' . __LINE__,
                $msg, ['TIMEPERIODKEY' => $timeperiod]
            );
            $ok = false;
        }
        return $ok;
    }

    /**
     * @param string $key
     *
     * @return bool
     */
    public function deleteByAllocKey($key)
    {
        $this->delByAllocKey = true;
        $ok =  $this->Delete($key);
        $this->delByAllocKey = false;
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function beforeDelete(&$values)
    {
        $ok = true;
        $mycny = GetMyCompany();
        $resultAllocation = [];
        if (!$this->delByAllocKey) {
            $resultAllocation = QueryResult(
                [
                    "select acctallocationid from glacctallocation where cny# = :1 and record# = :2", $mycny,
                    $values[0]['RECORD#']
                ]
            );
        }

        if ($this->delByAllocKey || !empty($resultAllocation)) {

            $queryDelete[0]
                = "SELECT COUNT(1) runcount
                        FROM glacctallocationrunmst glrun,
                          glacctallocationmst glalloc
                        WHERE glrun.cny#               = :1
                        AND glrun.cny#                 = glalloc.cny#
                        AND glrun.glacctallocationkey  = glalloc.record#
                        AND glrun.state                != 'F'
                        AND glrun.glacctallocationkey IN ";

            $queryDelete[1] = $mycny;

            if ($this->delByAllocKey) {
                $glacctallockeyInClauseVal = "(:2)";
                $queryDelete[2] = $values[0]['RECORD#'];
            } else {
                $glacctallockeyInClauseVal = "(SELECT record#
                          FROM glacctallocationmst glacctalloc
                          WHERE cny#           = :1
                          AND acctallocationid = :2
                          )";
                $queryDelete[2] = $resultAllocation[0]['ACCTALLOCATIONID'];
            }

            $queryDelete[0] = $queryDelete[0] . $glacctallockeyInClauseVal;

            $result = QueryResult($queryDelete);
            if ($result[0]['RUNCOUNT'] > 0) {
                $msg = "Delete the reference child records and try again. Could not able to delete the definition.";
                $gErr = Globals::$g->gErr;
                $gErr->addError('GL-2356', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
            $ok = $ok && $this->deleteByParentAllocationId($values[0]['RECORD#']);
        }
        return $ok;
    }


    /**
     * function to delete the old versions of the allocations before trying to delete the parent
     *
     * @param  int $id
     *
     * @return bool
     */
    private function deleteByParentAllocationId($id)
    {
        $query[] = "select record# from glacctallocation where cny# = :1 and latestversionkey = :2";
        $query[] = GetMyCompany();
        $query[] = $id;
        $result = QueryResult($query);
        $ok = true;

        foreach ($result as $values) {
            $ok = $ok && $this->Delete($values['RECORD#']);
        }

        if (!$ok) {
            $msg = "Could not able to delete the definition.";
            $gErr = Globals::$g->gErr;
            $gErr->addError('GL-2357', __FILE__ . ':' . __LINE__, $msg);
        }

        if ($ok) {

            $ours = $this->GetOwnedObjects();

            foreach ($ours as $ownedObjectDesc) {
                $ownedEntity = $ownedObjectDesc['entity'];
                $ownedObjectManager = $this->GetOwnedObjectManager($ownedEntity);
                $ok = $ok && $ownedObjectManager->deleteByParent($id, DELETE_FOR_DELETE);
            }
        }
        return $ok;
    }

    /**
     * function to validate location override value should have same base currency in case of IGC company
     *
     * @param string $targetLocationOverride
     *
     * @param string $reverseLocationOverride
     *
     * @param string $sourceLocationCurrency
     *
     * @param string $allowCrossEntityAlloc
     *
     * @param string $exchRateType
     *
     * @return bool
     */
    private function validateLocationOverrides(
        $targetLocationOverride, $reverseLocationOverride, $sourceLocationCurrency, $allowCrossEntityAlloc, $exchRateType
    )
    {
        $ok = true;
        $label = 'Location';

        if (empty($targetLocationOverride)) {
            $msg
                = "$label dimension value for target entry is required if dimension treatment value for $label is Not considered";
            $gErr = Globals::$g->gErr;
            $gErr->addError('GL-2358', __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        } elseif (empty($reverseLocationOverride)) {
            $msg
                = "$label dimension value for reverse pool is required if dimension treatment value for $label is Not considered";
            $gErr = Globals::$g->gErr;
            $gErr->addError('GL-2359', __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        }
        global $kMEid;
        $balance_entry_loc_Pref = GetPreferenceForProperty($kMEid, 'BALANCE_ENTRY_LOC');
        if ($ok) {
            $targetLocationOverride = explode('--', $targetLocationOverride)[0];
            $reverseLocationOverride = explode('--', $reverseLocationOverride)[0];
            if ($balance_entry_loc_Pref === 'Location') {
                if ($targetLocationOverride !== $reverseLocationOverride) {
                    $msg = "$label dimension value for target and reverse should be same if balance by location is enabled.";
                    $gErr = Globals::$g->gErr;
                    $gErr->addError('GL-2360', __FILE__ . ':' . __LINE__, $msg);
                    $ok = false;
                }
            } else {

                // allow allocation def having set different target and reversal entities, only if ALLOWALLOCATION is set to 'Within one entity'
                if ($allowCrossEntityAlloc === self::ALLOWALLOCATION_WITHINENTITY) {

                    $locMembersTemp = array($targetLocationOverride, $reverseLocationOverride);
                    $resultLocMap = self::getParentLocationMap($locMembersTemp, 'LOCATION_NO');
                    if ($resultLocMap[$targetLocationOverride] !== $resultLocMap[$reverseLocationOverride]) {
                        $msg = $this->getMessage("GL-2502",[$label]);
                        $gErr = Globals::$g->gErr;
                        $gErr->addIAError('GL-2502', __FILE__ . ':' . __LINE__, $msg, ['LABEL' => $label]);
                        $ok = false;
                    }
                }
            }
        }
        if ($ok && IsMCMESubscribed()) {
            if ($allowCrossEntityAlloc === self::ALLOWALLOCATION_WITHINENTITY) {
                $locationid = explode('--', $targetLocationOverride)[0];
                $currency = GetLocationBaseCurrency($locationid);
                if ($sourceLocationCurrency !== $currency) {
                    $msg = "$label dimension used in source, target and reverse pool should have same base currency.";
                    Globals::$g->gErr->addError('GL-2361', __FILE__ . ':' . __LINE__, $msg);
                    $ok = false;
                }
            } else if ($allowCrossEntityAlloc === self::ALLOWALLOCATION_ACROSSENTITIES && empty($exchRateType)){
                $msg = "Exchange rate is required for across entity allocation.";
                Globals::$g->gErr->addError('GL-2362', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * set active gl dimensions
     */
    private function setActiveGLDimensionInfo()
    {
        $this->dimInfo = IADimensions::getActiveGLDimensions(
            !util_isPlatformDisabled()
        );
    }

    /**
     * Function to get the latest posting date for the given allocation
     *
     * @param string $allocationId
     *
     * @return string
     */
    public function getPostingDateForGivenAllocation($allocationId)
    {
        $query[0]
            = "SELECT MAX(glbat.batch_date) batch_date
                        FROM glbatchmst glbat,
                          glacctallocationrunmst glrun
                        WHERE glbat.cny# = :1 
                        AND glbat.cny# = glrun.cny#
                        AND glbat.glacctallocationrunkey = glrun.record#
                        AND glrun.glacctallocationkey IN 
                        (
                        select record# from glacctallocationmst where cny# = :1 and acctallocationid = :2
                        )";
        $query[1] = GetMyCompany();
        $query[2] = $allocationId;
        $result = QueryResult($query);
        return $result[0]['BATCH_DATE'];
    }

    /**
     * @return bool
     */
    public function hasNonEditableFields()
    {
        return true;
    }

    /**
     * @param array $values
     *
     * @return array|string[]
     */
    public function getNonEditableFields($values)
    {
        $nonEditableFields = [
            'ACCTALLOCATIONID'
        ];

        // if true up is selected then mark all fields except header and basis section fields
        if ($values['RECORDNO']
            && $values['TRUEUP'] === self::TRUEUP_AUTOREVERSE
            && $this->allocDefinitionHasRuns($values['RECORDNO'])
        ) {

            $ownedObjects =& $this->GetOwnedObjects();

            $flds = [];
            if (is_array($ownedObjects)) {
                foreach ($ownedObjects as $objRec) {
                    $lineManager = $this->GetOwnedObjectManager($objRec['entity']);

                    $flds = $lineManager->GetGetFields();

                }
            }

            $nonEditableFields = array_merge($nonEditableFields, $flds);
            $nonEditableFields = array_merge($nonEditableFields, $this->trueupNonEditableFields);
        }


        return $nonEditableFields;
    }

    /**
     * @param string[] $fields
     */
    private function appendTrueUpNonEditableFields($fields)
    {
        if (!empty($fields)) {
            $this->trueupNonEditableFields = array_merge($this->trueupNonEditableFields, $fields);
        }
    }
    /**
     * @param string $dimTreatment
     * @return bool
     */
    public function isDimTreatmentAllowedForTarget($dimTreatment)
    {
        return in_array($dimTreatment,
            [
                self::NOT_INVITED,
                self::PRESERVED,
                self::PER_DIMENSION_VALUE
            ]);
    }

    /**
     * @return array
     */
    public function getDBTranslatedFieldsForEditor(): array
    {
        // Translate EXCH_RATE_TYPE_ID field explicitely in formeditor
        return ['EXCH_RATE_TYPE_ID', 'TARGET_EXCH_RATE_TYPE_ID'];
    }
}
