<?php
/**
*    FILE:        GLAccountBalancesReporter
*    AUTHOR:        <PERSON>
*    DESCRIPTION:
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/

class GLAccountBalancesReporter extends GLTrialbalanceReporter
{
    
    //This Holds Mapped Native Accounts
    /* @var array $mappedNativeAccts */
    public static $mappedNativeAccts;

    /* @var array $mappedNativeAcctRecs */
    public static $mappedNativeAcctRecs;

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct($_params);
    }

    /**
     * Description: This will apply the filters on the map and do
     * the get list on nativetoreportingacmap.
     */
    public function initNativeToRptAcs()
    {       
        if(!empty($this->params['REPORTINGACCOUNTSET'])) {
             //If we come From Dashboard
            if(!empty($this->params['reportparams'])) {
                $this->params['actstrt'] = $this->params['reportparams']['actstrt'];
                $this->params['actend'] = $this->params['reportparams']['actend'];
            }
            $gManagerFactory = Globals::$g->gManagerFactory;
            $reportingAcMapMgr = $gManagerFactory->getManager('nativetoreportingacmap');
            list($_acctno) = explode(PICK_RECVAL_SEP, isl_trim($this->params['actstrt']));
            list($_accttono) = explode(PICK_RECVAL_SEP, isl_trim($this->params['actend']));            
            //Get the Results
            /** @noinspection PhpUndefinedVariableInspection */
            $results = $reportingAcMapMgr->GetNativetoRepotingACMap($this->params['REPORTINGACCOUNTSET'], $_ac, $_acctno, $_accttono);
            //This Variable will be used in the backend_tb to filter the Results
            self::$mappedNativeAccts = array();
            /** @noinspection PhpUnusedLocalVariableInspection */
            $count = count($results);
            if(!empty($results)) {
                foreach($results as $val){
                    $this->nativeToRptAcs[$val['NATIVEACNO']] = $val;
                    self::$mappedNativeAccts[$val['NATIVEACNO']] = $val['NATIVEACNO'];                
                    self::$mappedNativeAcctRecs[] = $val['NATIVEACKEY'];                
                }                
            }
        }   
    }

    /**
     * Description: Return Mapped stat T or F for Native Act.
     *
     * @param string $_acct: Native Account.
     *
     * @return bool
     */
    public static function IsMappedNativeAct($_acct)
    {
        return isset(self::$mappedNativeAccts[$_acct]);
    }

    /**
     *
     *
     * @param array $lines: Line values.
     */
    /**
     * Description: Group the Transcations into one Array.
     *
     * @param array  $lines Line values.
     * @param int    $maprptackey
     * @param array  $val
     *
     * @return bool
     */
    public function GroupLineTxns(&$lines, $maprptackey, $val)
    {
    
        if(!empty($val['ACCDBTXNS'][0]['TXN'])) {
            $this->AddSubTxns($lines['tbreport'][0]['trans'][$maprptackey]['ACCDBTXNS'][0]['TXN'], $val['ACCDBTXNS'][0]['TXN']);
        }

        if(!empty($val['ACCCRTXNS'][0]['TXN'])) {
            $this->AddSubTxns($lines['tbreport'][0]['trans'][$maprptackey]['ACCCRTXNS'][0]['TXN'], $val['ACCCRTXNS'][0]['TXN']);
        }

        if(!empty($val['ACCADJCRTXNS'][0]['TXN'])) {
            $this->AddSubTxns($lines['tbreport'][0]['trans'][$maprptackey]['ACCADJCRTXNS'][0]['TXN'], $val['ACCADJCRTXNS'][0]['TXN']);
        }

        if(!empty($val['ACCADJCRTXNS'][0]['TXN'])) {
            $this->AddSubTxns($lines['tbreport'][0]['trans'][$maprptackey]['ACCADJDBTXNS'][0]['TXN'], $val['ACCADJDBTXNS'][0]['TXN']);
        }
        //eppp_p($lines);
        return true;
    }

    /**
     * Description: Group the Transcations into one Array.
     *
     * @param array $linetrxns: Line values.
     * @param array $valtrxns:  line value Trxns.
     */
    function AddSubTxns(&$linetrxns, $valtrxns)
    {
        foreach($valtrxns as $v){
            $linetrxns[] = $v;
        }
    }

    /**
     * @return array|mixed
     */
    function DoMap() 
    {
        if ($this->params['statAccts'] == 'true') {
            $this->params['statAccts'] = 'I';
        } else {
            $this->params['statAccts'] = 'E';
        }
        return parent::DoMap();
    }

}