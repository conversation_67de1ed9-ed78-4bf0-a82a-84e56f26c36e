<?
//=============================================================================
//
//	FILE:			GAAPAdjJrnlEditor.cls
//	AUTHOR:			<PERSON><PERSON>
//	DESCRIPTION:	Editor for GAAP Adjustment Journal.
//
//
//	(C)2010, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

/**
 * Editor class for GAAPAdjJrnlEditor object
 */
class GAAPAdjJrnlEditor extends JournalEditor
{

    /**
     * @param array $_params the parameters of the class
     */
    public function __construct($_params) 
    {
        parent::__construct($_params);
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state
     * 
     * @param array &$params the data
     */
    protected function buildDynamicMetadata(&$params) 
    {
        parent::buildDynamicMetadata($params);        
    }

}


