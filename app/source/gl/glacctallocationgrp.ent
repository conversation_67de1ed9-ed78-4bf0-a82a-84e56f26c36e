<?
/**
 *    FILE:        glacctallocation.ent
 *    AUTHOR:        Harish B
 *    DESCRIPTION:    entity definition for allocation object
 *
 *    (C) 2018, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
$kSchemas['glacctallocationgrp'] = array(
    'children' => array(
        'glacctallocationgrpmember' => array(
            'fkey' => 'record#',
            'invfkey' => 'glacctallocationgrpkey',
            'table' => 'glacctallocationgrpmembers',
            'join' => 'outer',
        ),
    ),
    'nexus' => array(
        'glacctallocationgrpmember' => array(
            'object' => 'glacctallocationgrpmember',
            'relation' => MANY2ONE,
            'field' => 'RECORDNO'
        )
    ),
    // object
    'object' => array(
        'RECORDNO',
        'NAME',
        'DESCRIPTION',
        'STATUS',
        'GRPERRORPROCESSINGMETHOD',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    // schema
    'schema' => array(
        'RECORDNO' => 'record#',
        'NAME' => 'name',
        'DESCRIPTION' => 'description',
        'STATUS' => 'status',
        'GRPERRORPROCESSINGMETHOD' => 'errororskip',
        'LOCATIONKEY' => 'locationkey',
        'DEPTKEY' => 'deptkey',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'SI_UUID'    => 'si_uuid',
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'GLACCTALLOCATIONGRPKEY', 'invfkey' => 'RECORDNO', // the field with which the owned object points to the parent
            'entity' => 'glacctallocationgrpmember',
            'path' => 'ACCTALLOCATIONGRPMEMBERS'
        ),
    ),
    // fieldinfo
    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'fullname' => 'IA.GROUP_NAME',
            'desc' => 'IA.NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => $gLocationIDFormat
            ),
            'required' => true,
            'path' => 'NAME',
            'id' => 2
        ),
        array(
            'fullname' => 'IA.DESCRIPTION',
            'desc' => 'IA.DESCRIPTION',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200
            ),
            'required' => true,
            'path' => 'DESCRIPTION',
            'id' => 3
        ),
        $gStatusFieldInfo,
        array(
            'path' => 'LOCATIONKEY',
            'fullname' => 'IA.LOCATION_AT',
            'desc' => 'IA.MEGA_LOCATION_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer'
            ),
            'id' => 6
        ),
        array(
            'path' => 'DEPTKEY',
            'fullname' => 'IA.DEPT_AT',
            'desc' => 'IA.MEGA_DEPARTMENT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer'
            ),
            'id' => 7
        ),
        array(
            'fullname' => 'IA.MEMBER',
            'type' => array(
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'glacctallocation',
                'pickfield' => array('ACCTALLOCATIONID'),
                'restrict' => array(
                    array(
                        'pickField' => 'STATUS',
                        'value' => 'active',
                    )
                )
            ),
            'desc' => 'IA.MEMBER',
            'path' => 'ALLOCATIONID',
            'id' => 8
        ),
        array(
            'fullname' => 'IA.SOURCE_POOL_TIME_PERIOD',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.SOURCE_POOL_TIME_PERIOD',
            'path' => 'SOURCE_TIME_PERIOD',
            'readonly'  =>  true,
            'id' => 9
        ),
        array(
            'fullname' => 'IA.BASIS_TIME_PERIOD',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.BASIS_TIME_PERIOD',
            'readonly'  =>  true,
            'path' => 'BASIS_TIME_PERIOD',
            'id' => 10
        ),
        array(
            'fullname' => 'IA.POSTING_JOURNAL',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.POSTING_JOURNAL',
            'readonly'  =>  true,
            'path' => 'POSTING_JOURNAL',
            'id' => 11
        ),
        array(
            'fullname' => 'IA.GROUP_ERROR_PROCESSING_METHOD',
            'type' => array(
                'ptype' => 'radio',
                'type' => 'radio',
                'maxlength' => 1,
                'validlabels' => array( 'IA.STOP_IF_A_GROUP_MEMBER_IN_THE_SEQUENCE_FAILS', 'IA.SKIP_AND_CONTINUE_IF_A_MEMBER_IN_THE_SEQUENCE' ),
                'validvalues' => array(
                    GLAcctAllocationManager::STOP, GLAcctAllocationManager::SKIP
                ),
                '_validivalues' => array('E', 'S'),
            ),
            'default' => GLAcctAllocationManager::STOP,
            'required' => true,
            'path' => 'GRPERRORPROCESSINGMETHOD',
            'layout' => 'portrait',
            'id' => 12,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        $gSiUuidFieldInfo,
    ),
    'autoincrement' => 'RECORDNO',
    'table' => 'glacctallocationgrp',
    'printas' => 'IA.ACCOUNT_ALLOCATION_GROUP',
    'pluralprintas' => 'IA.ACCOUNT_ALLOCATION_GROUPS',
    'sicollaboration' => true,
    'module' => 'gl',
    'vid' => 'RECORDNO',
    'api' => array(
        'GETNAME_BY_GET' => true, // Use individual Get instead of GetList
        'GET_BY_GET' => true,
        'PERMISSION_READ' => 'lists/glacctallocationgrp/view',
        'PERMISSION_CREATE' => 'lists/glacctallocationgrp/create',
        'PERMISSION_UPDATE' => 'lists/glacctallocationgrp/edit',
        'PERMISSION_DELETE' => 'lists/glacctallocationgrp/delete',
        'ITEMS_ALIAS' => ['GLACCTALLOCATIONGRPMEMBERS'],
        'ITEM_ALIAS' => ['GLACCTALLOCATIONGRPMEMBER'],
        'ITEMS_INTERNAL' => ['ACCTALLOCATIONGRPMEMBERS'],
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED',
    ),
    'auditcolumns' => true,
    'addauditloginids' => true,
    'upsertEntries' => true,
    'description' => 'IA.LIST_OF_PROCESSING_GROUPS_FOR_DYNAMIC_ALLOCATIONS',
);
