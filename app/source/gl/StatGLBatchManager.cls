<?php

/**
 * Manager class for the Statistical GL Batch object
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for the Statistical GL Batch object
 */
class StatGLBatchManager extends GLBatchManager
{
    /* @var bool $deferPermissionCheck*/
    private static $deferPermissionCheck;

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
    
    /**
     * unset defer permission validation at Init
     */
    public static function unsetDeferPermValidation()
    {
        self::$deferPermissionCheck = false;
    }

    /**
     * Setter to defer the permission validation at Init
     *
     * @param string $noun noun of the permission
     *
     */
    public static function setDeferPermValidation($noun='')
    {
        if (empty($noun)) {
            $noun = 'statglbatch';
        }
        $addOp = GetOperationId("gl/lists/$noun/create");
        
        self::$deferPermissionCheck = CheckAuthorization($addOp, 1);;
    }
    
    /**
     * Getter to defer the permission validation at Init
     *
     * @return bool
     */
    public static function getDeferPermValidation()
    {
        return self::$deferPermissionCheck;
    }

    /**
     * Hook function to control adding support for Spend Control
     *
     * @return string
     */
    public function batchSupportSpendValidation()
    {
        return false;
    }

    /**
     * Returns object definition Id for the object.
     *
     * @return int
     */
    public function getObjectDefId()
    {
        return Util_StandardObjectMap::getObjectId('glbatch', $this->getDocType());
    }
}

