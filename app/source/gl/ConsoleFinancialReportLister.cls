<?
import('NLister');

class ConsoleFinancialReportLister extends NLister
{

    function __construct()
    {

        $this->additionalTokens = array_merge($this->additionalTokens, [
            'IA.INSTALL', 'IA.HTML_1' , 'IA.TEXT', 'IA.PDF', 'IA.EXCEL', 'IA.SCHEDULE', 'IA.PROCESS_AND_STORE', 'IA.DOWNLOAD'
        ]);
        $this->textTokens = array_merge($this->textTokens, ['IA.SAVE_FINANCIAL_REPORT_AS', 'IA.REPORT_NAME',
                                                            'IA.REPORT_NAME_CANNOT_BE_EMPTY']);


        parent::__construct(
            array(
            'entity'        => 'consolefinancialreport',
            'title'            => 'IA.CONSOLE_FINANCIAL_REPORTS',
            'entitynostatus' =>  true,
            'suppressPrivate' => true,  
                    )
        );
    }



    function BuildTable() 
    {
        global $_userid;
        list($user_rec,$cny) = explode('@', $_userid);
        $target = (Profile::getUserCacheProperty('USERPREF', 'WINOPEN') || !Profile::hasUserCacheProperty('USERPREF', 'WINOPEN')) ? $user_rec.$cny: '_new';

        $iafinopid = GetOperationId('gl/reports/financialreport/view'); 
        $schopid = GetOperationId('co/lists/finrptschop/create');

        parent::BuildTable();
        $table = &$this->table;

        for ($i=0; $i<count($table); $i++) {
            $rec = $table[$i]['RECORD#'];

            $saveAs = "reporter.phtml?.confin=1&.r=$rec&.type=saveas&.op=$iafinopid&" . OptDone(ScriptRequest());
            $table[$i]["SAVEAS"]    =    "<a href='#' onclick=\"SaveIAFin('".$table[$i]['NAME']."','saveas','".htmlentities($saveAs, ENT_COMPAT)."');\">".I18N::getSingleToken('IA.INSTALL')."</a>";

            $table[$i]["VIEWHTML"]    =    "<a href='reporter.phtml?.confin=1&.r=$rec&.type=html&.op=$iafinopid&" . OptDone(ScriptRequest()) .
                "' target='$target'>".I18N::getSingleToken('IA.HTML_1')."</a>";
            $table[$i]["VIEWTEXT"]    =    "<a href='reporter.phtml?.confin=1&.r=$rec&.type=text&.op=$iafinopid&" . OptDone(ScriptRequest()) . 
                "' target='$target'>".I18N::getSingleToken('IA.TEXT')."</a>";
            $table[$i]["VIEWPDF"]    =    "<a href='reporter.phtml?.confin=1&.r=$rec&.type=pdf&.op=$iafinopid&" . OptDone(ScriptRequest()) . 
                "' target='_new'>".I18N::getSingleToken('IA.PDF')."</a>";
            $table[$i]["VIEWEXCEL"]    =    "<a href='reporter.phtml?.confin=1&.r=$rec&.type=excel&.op=$iafinopid&" . OptDone(ScriptRequest()) . 
                "' target='_new'>".I18N::getSingleToken('IA.EXCEL')."</a>";

            $vtext = " onclick=\"Javascript:window.open('csvdownload.phtml?.r=$rec&.type=csv&.op=$iafinopid&.utarg=reporter.phtml&".
            OptDone(ScriptRequest())."', 'csv', 'width=525,height=125,scrollbars=no,dependent,left=317,top=300');\"";
            
            $table[$i]["VIEWCSV"] = "<a href='#' " . $vtext . ">".I18N::getSingleToken('IA.DOWNLOAD')."</a>";
            
            $offline = " onclick=\"Javascript:window.open('reporter.phtml?.confin=1&.r=$rec&.type=background&.op=$iafinopid&".
            OptDone(ScriptRequest())."', 'ProcessStore', 'width=400,height=100,scrollbars=yes,dependent,left=317,top=300');\"";
            $table[$i]["OFFLINE"] = "<a href='#' " . $offline . ">".I18N::getSingleToken('IA.PROCESS_AND_STORE')."</a>";

            //To show schedule link
            if (CheckAuthorization($schopid)) {
                $schedule = "reporter.phtml?.confin=1&.r=$rec&.type=schedule&.op=$iafinopid&" . OptDone(ScriptRequest());
                $table[$i]["SCHEDULE"]    =    "<a href='#' onclick=\"SaveIAFin('".$table[$i]['NAME']."','schedule','".htmlentities($schedule, ENT_COMPAT)."');\">".I18N::getSingleToken('IA.SCHEDULE')."</a>";
            }
        }

        $flds    =    array('NAME', "OFFLINE", "VIEWHTML", "VIEWTEXT", "VIEWCSV", "VIEWPDF", "VIEWEXCEL", "SCHEDULE" );
        $fldlabels    =    array('NAME', "", "", "", "", "", "", "");
        $this->SetOutputFields($flds, $fldlabels);
    }


    /**
     * @return string
     */
    function genJSIncludes() 
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $jsInc = parent::genJSIncludes();
        $jsInc = "<jslib>../resources/js/financialreportwizard.js</jslib>";
        return $jsInc;
    }


    /**
     * @param int $i
     *
     * @return string
     */
    function calcEditUrl($i) 
    {
        $finopid = GetOperationId('gl/reports/financialreport/create');
        $t = $this->table;
        if ( CheckAuthorization($finopid) ) {
            return ($t[$i]['SAVEAS']);
        }
        return ' ';
    }

    /**
     * @param array $querySpec
     * @param string $querytype
     *
     * @return mixed
     */
    function GetList($querySpec, $querytype = 'normal')
    { 
        $extCny = Profile::getCompanyCacheProperty('CPAInfo', 'EXTERNCNYKEY');

        // for filters
        $fName = isl_strtoupper(Request::$r->F_NAME);
        $filter = '';
        if (isset($fName) && $fName!='') {
            $fName = (isl_strstr('%', $fName)?$fName:$fName.'%');
            $filter    .= " AND upper(REPORTINFO.NAME) LIKE ('".$fName."')";
        }

        $qry = "SELECT 
					REPORTINFO.*, REPORTINFO.RECORD# AS RECORDNO, COUNT(1) OVER() QCNT
				FROM 
					REPORTINFOMST REPORTINFO, REPORTCLIENTS 						
				WHERE 
					REPORTINFO.CNY# = :2
					AND REPORTINFO.CNY# = REPORTCLIENTS.CNY#
					AND REPORTINFO.RECORD# = REPORTCLIENTS.REPORT#
					AND REPORTINFO.CATEGORY = 'glfinancial'
					AND REPORTCLIENTS.ENTITYCNY# = :1
					AND REPORTINFO.STATUS = 'T' $filter
				ORDER BY 
					REPORTINFO.NAME";
        $table = DBRunner::runOnCnyDB(
            'QueryResult',
            array(array($qry, GetMyCompany(),$extCny), $querySpec['start'], $querySpec['max']),
            $extCny
        );
        
        return $table;
    }

}

