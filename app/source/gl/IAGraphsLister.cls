<?

import('ReportLister');

/**
 * Class IAGraphsLister
 */
class IAGraphsLister extends ReportLister
{

    function __construct()
    {
        $this->textTokens = array_merge($this->textTokens, [ 'IA.GRAPH_NAME',
            'IA.SAVE_FINANCIAL_GRAPH_AS', 'IA.PLEASE_ENTER_A_GRAPH_NAME'
        ]);
        $this->additionalTokens = array_merge($this->additionalTokens, [
            'IA.EDIT_ASSIGNMENTS', 'IA.VIEW_GRAPH', 'IA.PRINTABLE_PDF','IA.CONSOLE_GRAPHS', 'IA.INSTALL',
        ]);
        parent::__construct(
            array(
            'entity'        => 'iagraphs',
            'title'            => 'IA.QUICK_START_LIBRARY',
            'helpfile'        => 'Viewing_a_List_of_Intacct_Standard_Financial_Graphs',
            'suppressPrivate' => true,
            'showstatus'    => false,
            'entitynostatus' => 1,
            'linkinfo'        => array(                                        
                                        'hasflash'        => 1,
                                        'haspdf'        => 1,
                                        'opID'            => 'gl/reports/graphs/view',
                                        'href'            => "reporter.phtml"
                                    )
            )
        );
    }


    /**
     * @param int $i
     * @param array $vals
     * @return array
     */
    function calcViewUrlParms($i, $vals )
    {
        $owner = $this->GetObjectOwnership($i);
        if( $owner < 0 ) { return parent::calcViewUrlParms($i, $vals); 
        }
        else { 
            return array(); 
        }
    }


    /**
     * @return array
     */
    function GetFields()
    {
        // IF YOU WANT MORE LINKS IN THE REPORT LISTERS, DESCRIBE THEM HERE!!!!
        $fields =  array(
            'hasflash' => array('action'    => 'viewf',
                                'linkname'    => GT($this->textMap, 'IA.VIEW_GRAPH'),
                                'field'        => "'VIEWFLASH'",
                                'status'    => 'Flash Format'),
            'haspdf' => array(    'action'    => 'viewp',
                                  'linkname'    => GT($this->textMap, 'IA.PRINTABLE_PDF'),
                                  'field'        => "'VIEWPDF'",
                                  'status'    => 'View Report'),
        );
        return $fields;
    }


    /**
     * @param string $action
     * @param string $href
     * @param int $opID
     * @param int $recno
     * @param string $donerequest
     * @param string $linkname
     * @param string $statusdisp
     * @param array $curField
     * @return string
     */
    function MakeLink($action, $href, $opID, $recno, $donerequest, $linkname, $statusdisp, $curField=[])
    { 
        if ($action=='viewh') {
            $urlaction = '&.type=html&.iafin=1';
        }
        else if ($action=='viewp') {
            $urlaction = '&.type=pdf&.iafin=1';
        }
        else if ($action=='viewf') {

            //To find the equivalent Graph type of Flash
            switch (isl_substr($curField['RE_TYPE'], 6, isl_strlen($curField['RE_TYPE']))) {
            case 'pie.1' : {
                $chartType = 'Pie';
              break;
                }
            case 'pie.4' : {
                $chartType = 'Pie';
              break;
                }
            case 'col.1' : {
                $chartType = 'Column';
              break;
                }
            case 'col.4' : {
                $chartType = 'Column';
              break;
                }
            case 'bar.1' : {
                $chartType = 'Bar';
              break;
                }
            case 'bar.3' : {
                $chartType = 'Stacked bar';
              break;
                }
            case 'line.1' : {
                $chartType = 'Line';
              break;
                }
            default : {
                // If no mapping possible then the value is already using new library format.
                $chartType = $curField['RE_TYPE'];
              break;
                }
            }

            $href = "flashcomphelper.phtml?.iafin=1&.popup=1&.title="
                . urlencode(URLCleanParams::insert('.title', $curField['NAME']))."&.re_type="
                . urlencode($curField['RE_TYPE'])."&.xOrient=diagonal_up&.yOrient=horizontal&.entityNo=".$recno
                . "&.type=R&.external=1&.series=".$curField['SERIES']."&.seconseries=".$curField['SECONSERIES']
                . "&.chart_type=".urlencode($chartType);

            //$opID = GetOperationID('flashcomphelper');
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return '<a  href="#Skip" onclick=\'Launch("' . $href . '&.op=' . $opID . '&.r=' . $recno . '&.' . $urlaction .
               '&' . $donerequest . '&.isgraph=1&.viewSource=1' . '","rwindow",850,800);\' ONMOUSEOVER=\'window.status="' . statusdisp($statusdisp) . '"; return true;\' ' .
               'onfocus=\'window.status="' . statusdisp($statusdisp) . '"; return true;\' ' .
               'onblur=\'window.status=""\' ' .
               'ONMOUSEOUT=\'window.status=""\'>' . $linkname . '</a>';
    }



    function BuildTable() 
    {
        $iafinopid = GetOperationId('gl/reports/iagraphs');
        parent::BuildTable();
        $table = &$this->table;

        for ($i=0; $i<count($table); $i++) {
            $rec = $table[$i]['RECORDNO'];

            $saveAs = "reporter.phtml?.iafin=1&.r=$rec&.type=saveas&.op=$iafinopid&" . OptDone(ScriptRequest());
            $table[$i]["SAVEAS"]    =    "<a href='#' onclick=\"SaveIAFin('".htmlentities($table[$i]['NAME'], ENT_COMPAT)."','saveas','".htmlentities($saveAs, ENT_COMPAT)."');\" >".GT($this->textMap, 'IA.INSTALL')."</a>";
        }

    }


    /**
     * @return array
     */
    function BuildQuerySpec()
    {
        $querySpec = parent::BuildQuerySpec();

        $industryFilter = [
            GetMyIndustryType(),
            IsModuleConfigured(Globals::$g->kDBBid) ? GetMyIndustryType() . '-DBB' : null,
            IsModuleConfigured(Globals::$g->kSIFRid) ? GetMyIndustryType() . '-SIFR' : null,
            IsModuleConfigured(Globals::$g->kSIEMRid) ? GetMyIndustryType() . '-EMR' : null,
        ];

        $querySpec['filters'][0][] = ['INDUSTRYCODE', 'IN', array_filter($industryFilter)];

        $querySpec['filters'][0][] = array('RE_TYPE', '!=', 'DB_KPI');
        $querySpec['selects'][] = 'RE_TYPE';
        $querySpec['selects'][] = 'SERIES';
        $querySpec['selects'][] = 'SECONSERIES';

        return $querySpec;
    }

    /**
     * @return string
     */
    function genJSIncludes()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $jsInc = parent::genJSIncludes();
        $jsInc = "<jslib>../resources/js/graphwizard.js</jslib>";
        return $jsInc;
    }


    /**
     * @param int $i
     * @return string
     */
    function calcEditUrl($i)
    {
        $finopid = GetOperationId('gl/reports/graphs/create');
        $t = $this->table;
        if ( CheckAuthorization($finopid) ) {
            return ($t[$i]['SAVEAS']);
        }
        return ' ';
    }

}


