<?php
/**
 * AllowedOperationsHandler
 *
 * <AUTHOR>
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 *
 */
class SODocumentParamsAllowedOperationsHandler extends DocumentParamsAllowedOperationsHandler
{
    /**
     * @inheritDoc
     */
    public function __construct(EntityManager $entManager)
    {
        parent::__construct($entManager);
    }

    /**
     * @param array $record
     * @param string|null $moduleKey
     * @return bool
     */
    protected function showRecurringTemplates(array $record, /** @noinspection PhpUnusedParameterInspection */ string $moduleKey = null): bool
    {
        return $this->calcRecurringTemplates($record, 'sodocumentparams');
    }

    /**
     *
     *
     * @return array
     */
    protected function getFieldsForPermissionChecks() : array
    {
        $fields = parent::getFieldsForPermissionChecks();
        $fields = array_merge($fields, [ 'ENABLECONTRACTBILLING']);
        return $fields;
    }
}
