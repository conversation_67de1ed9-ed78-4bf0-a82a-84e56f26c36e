<?

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_GET_KITCOMPONENT'] = array(
	'QUERY' => "select ic.componentkey ITEMCOMPONENTKEY, item.name ITEMDESC, ic.quantity ITEMQUANTITY, 
                       ic.revpercent INVOICEPRICE, ic.KCDLVRSTATUS DLVRSTATUS, 
                       TO_CHAR(CURRENT_DATE, 'MM/DD/YYYY') DLVRDATE, ic.KCREVDEFSTATUS REVDEFSTATUS 
                from icitemcomponent ic, icitem item
                where ic.itemkey =?  AND ic.cny# =? and ic.componentkey = item.itemid and ic.cny# = item.cny#
                order by ic.RECORD#",
	'ARGTYPES' => array('text' ,'integer'),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_SELECT_BY_KEY'] = array(
	'QUERY' => "select ic.componentkey ITEMCOMPONENTKEY, item.name ITEMDESC, ic.quantity ITEMQUANTITY, 
                       dd.INVOICEPRICE INVOICEPRICE, 
                       case when dd.DLVRSTATUS is null then ic.KCDLVRSTATUS else dd.DLVRSTATUS end DLVRSTATUS, 
                       case when dd.DLVRDATE is null then TO_CHAR(CURRENT_DATE, 'MM/DD/YYYY') else TO_CHAR(dd.DLVRDATE, 'MM/DD/YYYY') end DLVRDATE, 
                       case when dd.REVDEFSTATUS is null then ic.KCREVDEFSTATUS else dd.REVDEFSTATUS end REVDEFSTATUS
                from icitemcomponent ic, icitem item, docentrydetail dd
                where ic.itemkey = ?  AND ic.cny# = ?
                      and ic.componentkey = item.itemid and ic.cny# = item.cny#
                      and ic.cny# = dd.cny#
                      and dd.line_no = ?
                      and dd.dochdrkey = ?
                      and ic.itemkey = dd.kitid
                      and ic.componentkey = dd.itemcomponentkey
                order by ic.RECORD#",
	'ARGTYPES' => array('text' ,'integer', 'integer', 'text'),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_SELECT_LINE_QTY_BY_KEY'] = array(
	'QUERY' => "select docentry.UIQTY
                from dochdr dochdr, docentry docentry
                where docentry.itemkey = ?  AND docentry.cny# = ? and docentry.lineno = ?
                      and docentry.dochdrkey = dochdr.record#
                      and docentry.cny# = dochdr.cny#
                      and dochdr.docid = ?",
	'ARGTYPES' => array('text' ,'integer', 'integer', 'text'),
);


$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_SELECT_ITEM_STATUS_BY_KEY'] = array(
	'QUERY' => "select DLVRSTATUS, DLVRDATE, REVDEFSTATUS
                from docentrydetail
                where itemcomponentkey = ?  AND cny# = ? and line_no = ? and dochdrkey = ?",
	'ARGTYPES' => array('text' ,'integer', 'integer', 'text'),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_SELECT_SUMMARY_BY_DOCID'] = array(
	'QUERY' => "select unique bundle_no BUNDLEID from docentrydetail where cny# = ? and dochdrkey = ? and bundle_no is not null order by bundle_no",
	'ARGTYPES' => array('integer', 'text'),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_SELECT_DETAIL_BY_DOCID'] = array(
    'QUERY' => "select BUNDLEID, LINENO, KITID, ITEMCOMPONENTKEY, ITEMDESC, ITEMQUANTITY, VSOEUNITPRICE, VSOEEXTPRICE,
                       VSOEPERCENT, INVOICEPRICE, TRX_VSOEALLOCATION, VSOEALLOCATION, DLVRSTATUS, RECORDNO, SCHEDULEREC
                from(
                /* with vsoe price */
                select dd.bundle_no BUNDLEID, (dd.line_no + 1) LINENO, 
                       dd.kitid KITID, dd.itemcomponentkey || '--' || item.name ITEMCOMPONENTKEY, item.name ITEMDESC, 
                       case when itempl.VALUEBASE = 'P' then null else (case when ic.quantity is null then de.uiqty else ic.quantity * de.uiqty end) end ITEMQUANTITY, 
                       case itempl.VALUEBASE when 'P' then entrypl.VALUE || '%' else round(dd.VSOEEXTPRICE / NULLIF(CASE WHEN ic.quantity IS NULL THEN de.uiqty ELSE ic.quantity * de.uiqty END,0),2)  || '' end VSOEUNITPRICE,
                       dd.VSOEEXTPRICE VSOEEXTPRICE,
                       dd.VSOEPERCENT VSOEPERCENT, dd.INVOICEPRICE INVOICEPRICE,
                       dd.TRX_VSOEALLOCATION TRX_VSOEALLOCATION, dd.VSOEALLOCATION VSOEALLOCATION,
                       case dd.DLVRSTATUS when 'D' then 'Delivered' when 'U' then 'Undelivered' else '' end DLVRSTATUS, dd.record# RECORDNO, rrs.record# SCHEDULEREC
                from icitemcomponent ic, icitem item, docentrydetail dd, dochdr dochdr, docentry de,
                     vsoeitemprclst itempl, vsoeitemprclstentry entrypl,  revrecschedule rrs
                where dd.cny# = ? and dd.dochdrkey = ?
                      and dd.itemcomponentkey = item.itemid(+) and dd.cny# = item.cny#(+)
                      and ic.cny#(+)= dd.cny#
                      and ic.itemkey(+) = dd.kitid
                      and ic.componentkey(+) = dd.itemcomponentkey
                      and dd.cny# = itempl.cny#
                      and dd.vsoeplkey = itempl.record#
                      and entrypl.cny# = itempl.cny#
                      and entrypl.vsoeitemprclstkey = itempl.record#
                      and entrypl.startdate <= to_date(?, 'MM/DD/YYYY') and entrypl.enddate >= to_date(?, 'MM/DD/YYYY')
                      and dd.cny# = dochdr.cny#
                      and dd.dochdrkey = dochdr.docid
                      and dd.cny# = de.cny#
                      and dochdr.record# = de.dochdrkey
                      and dd.line_no = de.lineno
                      and dd.bundle_no is not null

                      and dd.cny# = rrs.cny#(+)
                      and dd.docentrykey = rrs.docentrykey (+)
                      and dd.itemcomponentkey = rrs.description (+)
                    union all
                /* with out vsoe price */
                select dd.bundle_no BUNDLEID, (dd.line_no + 1) LINENO,
                       dd.kitid KITID, dd.itemcomponentkey || '--' || item.name ITEMCOMPONENTKEY, item.name ITEMDESC,
                       case when ic.quantity is null then de.uiqty else ic.quantity * de.uiqty end ITEMQUANTITY,
                       '' VSOEUNITPRICE,
                       dd.VSOEEXTPRICE VSOEEXTPRICE,
                       dd.VSOEPERCENT VSOEPERCENT, dd.INVOICEPRICE INVOICEPRICE,
                       dd.TRX_VSOEALLOCATION TRX_VSOEALLOCATION, dd.VSOEALLOCATION VSOEALLOCATION,
                       case dd.DLVRSTATUS when 'D' then 'Delivered' when 'U' then 'Undelivered' else '' end DLVRSTATUS, dd.record# RECORDNO, rrs.record# SCHEDULEREC
                from icitemcomponent ic, icitem item, docentrydetail dd, dochdr dochdr, docentry de,  revrecschedule rrs
                where dd.cny# = ? and dd.dochdrkey = ?
                      and dd.itemcomponentkey = item.itemid(+) and dd.cny# = item.cny#(+)
                      and ic.cny#(+)= dd.cny#
                      and ic.itemkey(+) = dd.kitid
                      and ic.componentkey(+) = dd.itemcomponentkey
                      and dd.vsoeplkey is null
                      and dd.cny# = dochdr.cny#
                      and dd.dochdrkey = dochdr.docid
                      and dd.cny# = de.cny#
                      and dochdr.record# = de.dochdrkey
                      and dd.line_no = de.lineno
                      and dd.bundle_no is not null
                      and dd.itemcomponentkey != 'Bundle Discount'

                      and dd.cny# = rrs.cny#(+)
                      and dd.docentrykey = rrs.docentrykey (+)
                      and dd.itemcomponentkey = rrs.description (+)
                    union all
                    /* discount item */
                select dd.bundle_no BUNDLEID, (dd.line_no + 1) LINENO, dd.kitid KITID, dd.itemcomponentkey ITEMCOMPONENTKEY, '' ITEMDESC,
                       null ITEMQUANTITY,
                       '' VSOEUNITPRICE,
                       dd.VSOEEXTPRICE VSOEEXTPRICE,
                       dd.VSOEPERCENT VSOEPERCENT, dd.INVOICEPRICE INVOICEPRICE,
                       dd.TRX_VSOEALLOCATION TRX_VSOEALLOCATION, dd.VSOEALLOCATION VSOEALLOCATION,
                       case dd.DLVRSTATUS when 'D' then 'Delivered' when 'U' then 'Undelivered' else '' end DLVRSTATUS, dd.record# RECORDNO, rrs.record# SCHEDULEREC
                from docentrydetail dd,  revrecschedule rrs
                where dd.cny# = ? and dd.dochdrkey = ?
                      and dd.itemcomponentkey = 'Bundle Discount'

                      and dd.cny# = rrs.cny#(+)
                      and dd.docentrykey = rrs.docentrykey (+)
                      and dd.itemcomponentkey = rrs.description (+)
                )
                order by bundleid, lineno, KITID, ITEMCOMPONENTKEY",
    'ARGTYPES' => array('text', 'integer', 'date', 'date', 'integer', 'text', 'integer', 'text'),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_GETKITDETAIL_BY_DOCID_LINENO'] = array(
	'QUERY' => "select item.itemid ITEMID, item.name ITEMDESC, ic.quantity ITEMQUANTITY, dd.VSOEALLOCATION VSOEALLOCATION
                from icitemcomponent ic, icitem item, docentrydetail dd
                where dd.cny# = ?
                      and dd.dochdrkey = ?
                      and dd.line_no = ?
                      and dd.cny# = ic.cny#
                      and dd.kitid = ic.itemkey
                      and dd.itemcomponentkey = ic.componentkey
                      and ic.cny# = item.cny#
                      and ic.componentkey = item.itemid
                order by ic.lineno",
	'ARGTYPES' => array('integer', 'text', 'integer'),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_GETSTATUS_BY_DOCID'] = array(
	'QUERY' => "select TEMPTAB.BUNDLENO, TEMPTAB.LINENO, TEMPTAB.KITID, TEMPTAB.ITEMID || '--' || TEMPTAB.ITEMNAME ITEMID,
                       TEMPTAB.DLVRSTATUS, TEMPTAB.DOCENTRYDETAILKEY,
                       TEMPTAB.DLVRDATE, TEMPTAB.REVDEFSTATUS, TEMPTAB.REVRECSTARTDATE, rrs.record# SCHEDULEREC,
                       case rrs.status  when 'N' then 'Not Started' when 'P' then 'In Progress'
                            when 'C' then 'Completed' when 'H' then 'On Hold' when 'X' then 'Partially Terminated'
                            else 'Terminated' end SCHEDULESTATUS, template.templateid
                from (select dh.cny# CNY#, dh.record# dochdrkey, de.revrecstartdate, de.record# docentrykey, dd.record# docentrydetailkey,
                             dd.bundle_no BUNDLENO, dd.line_no LINENO, dd.kitid KITID, dd.itemcomponentkey ITEMID, item.name ITEMNAME,
                             dd.DLVRSTATUS DLVRSTATUS, dd.DLVRDATE DLVRDATE, dd.REVDEFSTATUS REVDEFSTATUS
                      from docentrydetail dd, docentry de, dochdr dh, icitem item
                      where dd.cny# = ? and dd.dochdrkey = ? and dd.cny# = dh.cny# and dd.dochdrkey = dh.docid
                            and dh.cny# = de.cny# and dh.record# = de.dochdrkey and dd.line_no = de.lineno
                            and dd.itemcomponentkey = item.itemid(+) and dd.cny# = item.cny#(+)) TEMPTAB,
                      revrecschedule rrs, revrectemplate template
                where TEMPTAB.CNY# = rrs.cny#(+) and TEMPTAB.dochdrkey = rrs.dochdrkey(+) and TEMPTAB.docentrykey = rrs.docentrykey(+)
                      and TEMPTAB.ITEMID = rrs.description(+) and rrs.REVRECTEMPLATEKEY = template.record#(+) and rrs.cny# = template.cny#(+)
                order by TEMPTAB.BUNDLENO, TEMPTAB.LINENO, TEMPTAB.KITID, ITEMID",
	'ARGTYPES' => array('integer', 'text'),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_UPDATE_DELIVERY_STATUS_BY_ITEMID'] = array(
	'QUERY' => "UPDATE docentrydetail SET dlvrstatus=?, dlvrdate=?, isrevonhold=? WHERE record# =?  AND cny# =?  ",
		'ARGTYPES' => array('text','date','integer','integer' ),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_UPDATE_ISONHOLD_STATUS_BY_ITEMID'] = array(
	'QUERY' => "UPDATE docentrydetail SET isrevonhold=? WHERE record# =?  AND cny# =?  ",
		'ARGTYPES' => array('integer','integer' ),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_GET_DELIVERY_STATUS_BY_DOCNO_LINENO'] = array(
	'QUERY' => "SELECT ITEMCOMPONENTKEY, REVDEFSTATUS, DLVRSTATUS from docentrydetail WHERE DOCHDRKEY = ? AND LINE_NO = ? AND cny# =?  ",
		'ARGTYPES' => array('text','integer','integer' ),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_GET_DELIVERY_STATUS_BY_DOCNO_BUNDLENO'] = array(
	'QUERY' => "SELECT ITEMCOMPONENTKEY, REVDEFSTATUS, DLVRSTATUS from docentrydetail WHERE DOCHDRKEY = ? AND BUNDLE_NO = ? AND cny# =?  ",
		'ARGTYPES' => array('text','integer','integer' ),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_GET_UBC_BY_DOCNO'] = array(
	'QUERY' => "SELECT COUNT(*) COUNT from docentrydetail WHERE DOCHDRKEY = ? AND cny# =? AND BUNDLE_NO IS NULL AND DLVRSTATUS = 'U' AND REVDEFSTATUS = 'B'",
		'ARGTYPES' => array('text','integer','integer' ),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_DELETE_BY_DOCID'] = array(
	'QUERY' => "DELETE from docentrydetail WHERE DOCHDRKEY = ?  AND cny# = ?",
		'ARGTYPES' => array('text', 'integer' ),
);

$kdocentrydetailQueries['QRY_DOCENTRYDETAIL_GET_VSOEPLKEY_COUNT'] = array(
	'QUERY' => "SELECT COUNT(*) COUNT from docentrydetail WHERE VSOEPLKEY = ? AND cny# =?",
		'ARGTYPES' => array('integer','integer' ),
);