<?php
/**
 * File OrderEntryRevenueTxnEntriesService.cls contains the class OrderEntryRevenueTxnEntriesService
 *
 * <AUTHOR> @copyright 2000-2024 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class OrderEntryRevenueTxnLineService
{
    const METRIC_ACTION_EDIT = 'Edit';
    const METRIC_ACTION_POST = 'Post';

    const HEADER_FIELD_MAP = [
        'customerKey' => ['CUSTREC', ':unset' => ['CUSTVENDID', 'CUSTVENDNAME']],
        'billToId' => ['BILLTO#~#CONTACTNAME', ':unset' => ['BILLTOKEY']],
        'shipToId' => ['SHIPTO#~#CONTACTNAME', ':unset' => ['SHIPTOKEY']],
        'paymentTermKey' => ['TERMKEY', ':unset' => ['TERM', 'WHENDUE']],
        'txnDate' => ['WHENCREATED', ':unset' => ['WHENDUE']],
        'dueDate' => ['WHENDUE'],
        // 'glPostingDate' => ['WHENPOSTED'],
        // 'baseCurrency' => ['BASECURR'],
        // 'txnCurrency' => ['CURRENCY'],
        // 'exchangeRateDate' => ['EXCHRATEDATE'],
        // 'exchangeRateTypeKey' => ['EXCHRATETYPE'],
    ];

    const LINE_FIELD_MAP = [
        'revenueRecognitionTemplateKey' => ['REVRECTEMPLATEKEY', ':unset' => ['REVRECTEMPLATE']],
        'revenueRecognitionStartDate' => ['REVRECSTARTDATE'],
        'revenueRecognitionEndDate' => ['REVRECENDDATE'],
        'itemDescription' => ['ITEMDESC'],
        'memo' => ['MEMO'],
        'locationKey' => ['LOCATIONKEY', ':unset' => ['LOCATIONID', 'LOCATIONNAME']],
        'departmentKey' => ['DEPTKEY', ':unset' => ['DEPARTMENTID', 'DEPARTMENTNAME']],
        'vendorKey' => ['VENDORDIMKEY', ':unset' => ['VENDORID', 'VENDORNAME']],
        'employeeKey' => ['EMPLOYEEDIMKEY', ':unset' => ['EMPLOYEEID', 'EMPLOYEENAME']],
        'classKey' => ['CLASSDIMKEY', ':unset' => ['CLASSID']],
        'contractKey' => ['CONTRACTDIMKEY', ':unset' => ['CONTRACTID', 'CONTRACTNAME']],
        'projectKey' => ['PROJECTDIMKEY', ':unset' => ['PROJECTID', 'PROJECTNAME']],
    ];

    public function editLines(array $request, array $extraContext): array
    {
        $createMetricObj = new MetricOrderEntryRevenueTxnBulkAction();
        $createMetricObj->startTime();
        $createMetricObj->setAction(self::METRIC_ACTION_EDIT);

        $this->parseRequest($request, $lineKeys, $headerFieldsToEdit, $headerFieldsToClear, $lineFieldsToEdit,
                           $lineFieldsToClear, $extraContext);
        if (empty($lineKeys) || (empty($headerFieldsToEdit) && empty($lineFieldsToEdit))) {
            return ['STATUS' => 'completed'];
        }
        $revTxnEntryMgr = Globals::$g->gManagerFactory->getManager('revenuetxnentry');

        $filter = [
            'selects' => ['DOCHDRID', 'DOCPARID', 'RECORDNO', 'LINENO'],    // RECORDNO and LINENO are needed for the implicit order by
            'filters' => [[['RECORDNO', 'IN', $lineKeys]]],
            'orders' => [['DOCHDRID']],
        ];
        $results = $revTxnEntryMgr->GetList($filter);
        if (!empty($results)) {
            $ok = true;
            XACT_BEGIN(__METHOD__);
            $errMsg = null;
            if (!empty($headerFieldsToEdit)) {
                // create an array with docId as keys and docType as values
                $soDocRowIds = array_combine(array_column($results, 'DOCHDRID'),
                                             array_column($results, 'DOCPARID'));
                $ok = $this->setFields($soDocRowIds, 'sodocument', $headerFieldsToEdit, $headerFieldsToClear, $errMsg);
                $createMetricObj->setHeaderCount(count($soDocRowIds));
            }
            if ($ok && !empty($lineFieldsToEdit)) {
                // create an array with docEntry record# as keys and docType as values
                $soDocEntryRowKeys = array_combine(array_column($results, 'RECORDNO'),
                                                   array_column($results, 'DOCPARID'));
                $ok = $this->setFields($soDocEntryRowKeys, 'sodocumententry', $lineFieldsToEdit, $lineFieldsToClear, $errMsg);
                $createMetricObj->setLineCount(count($soDocEntryRowKeys));
            }
            $ok = $ok && XACT_COMMIT(__METHOD__);

            if ($ok) {
                $createMetricObj->stopTime();
                $createMetricObj->publish();
            }

            if (!$ok) {
                XACT_ABORT(__METHOD__);
                throw new APIExecutionException($errMsg);
            }
        }
        return ['STATUS' => 'completed'];
    }

    private function parseRequest(array $request, ?array &$lineKeys, ?array &$headerFieldsToEdit, ?array &$headerFieldsToClear,
                                  ?array &$lineFieldsToEdit, ?array &$lineFieldsToClear, array $extraContext): void
    {
        $lineKeys = $request['revenueTxnLineKeys'] ?? [];
        unset($request['revenueTxnLineKeys']);

        $headerFieldsToEdit = $lineFieldsToEdit = [];
        foreach ($request as $key => $value) {
            if (array_key_exists($key, self::HEADER_FIELD_MAP)) {
                $mapEntry = self::HEADER_FIELD_MAP[$key];
                $headerFieldsToEdit[$mapEntry[0]] = $value;
                $headerFieldsToClear = array_merge($headerFieldsToClear ?? [], $mapEntry[':unset'] ?? []);
            } elseif (array_key_exists($key, self::LINE_FIELD_MAP)) {
                $mapEntry = self::LINE_FIELD_MAP[$key];
                $lineFieldsToEdit[$mapEntry[0]] = $value;
                $lineFieldsToClear = array_merge($lineFieldsToClear ?? [], $mapEntry[':unset'] ?? []);
            } else {
                throw new APIInvalidFieldException($extraContext, $key, APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0022);
            }
        }
    }

    private function setFields(array $rowKeyAndDocTypeArr, string $entity, array $fieldsToEdit, array $fieldsToClear, ?string &$error): bool
    {
        $ok = true;
        foreach ($rowKeyAndDocTypeArr as $rowKey => $docType) {
            $entityManager = Globals::$g->gManagerFactory->getManager($entity, false, ['DOCTYPE' => $docType]);
            $entityManager->disableWarningValidation();
            $values = $entityManager->Get($rowKey);
            if ($values) {
                foreach (array_unique($fieldsToClear) as $fieldToClear) {
                    unset($values[$fieldToClear]);
                }
                foreach ($fieldsToEdit as $fieldName => $value) {
                    if (str_contains($fieldName, '#~#')) {
                        $fieldNames = explode('#~#', $fieldName);
                        $depth = count($fieldNames);
                        $parent = &$values[$fieldNames[0]];
                        for ($i = 1; $i < $depth - 1; $i++) {
                            $parent = &$values[$fieldNames[$i]];
                        }
                        $parent[$fieldNames[$depth - 1]] = $value;
                    } else {
                        $values[$fieldName] = $value;
                    }
                }
                $ok = $entityManager->set($values);
                if (!$ok) {
                    $errInfo = [
                        ['name' => 'RECORDNO', 'value' => $rowKey],
                        ['name' => 'DOCNO', 'value' => $values['DOCNO']],
                        ['name' => 'DOCTYPE', 'value' => $values['DOCPARID']]
                    ];
                    if ($entity === 'sodocumententry') {
                        $errInfo[] = [ 'name' => 'LINENO', 'value' => $values['LINE_NO']];
                        $token = 'IA.BULK_EDIT_TXN_LINE_FAILED';
                    } else {
                        $token = 'IA.BULK_EDIT_TXN_FAILED';
                    }
                    $error = I18N::getSingleToken($token, $errInfo);
                    break;
                }
            }
        }
        return $ok;
    }

    public function postTransactions(array $request): array
    {
        $createMetricObj = new MetricOrderEntryRevenueTxnBulkAction();
        $createMetricObj->startTime();

        $docHdrKeyDocIdMap = $this->getTransactionsToPost($request['REVTXNLINEKEYS']);
        $ok = $this->postTransactionsOffline($docHdrKeyDocIdMap, $runObjectKey);

        if ($ok) {
            $createMetricObj->setAction(self::METRIC_ACTION_POST);
            $createMetricObj->setHeaderCount(count($docHdrKeyDocIdMap));
            $createMetricObj->setLineCount(count($request['REVTXNLINEKEYS']));
            $createMetricObj->stopTime();
            $createMetricObj->publish();
        }

        if (!$ok) {
            throw new APIExecutionException(I18N::getSingleToken('IA.ERROR_IN_POSTING_TRANSACTIONS'));
        }
        return ['RUNKEY' => $runObjectKey];
    }

    /**
     * @param array $docEntryKeys
     *
     * @return array
     * @throws APIExecutionException
     */
    private function getTransactionsToPost(array $docEntryKeys) : array
    {
        if (empty($docEntryKeys)) {
            throw new APIExecutionException(I18N::getSingleToken('IA.NO_REVENUE_TXN_ENTRIES_SELECTED_FOR_POSTING'));
        } else {
            // get all the dochdr keys for the SELECTED transaction entries to determine which
            // transactions need to be posted
            $soDocumentEntryManager = Globals::$g->gManagerFactory->getManager('sodocumententry');
            $params = [
                'selects' => ['RECORDNO', 'DOCHDRNO', 'DOCHDRID'],
                'filters' => [[[ 'RECORDNO', 'IN', $docEntryKeys]]]
            ];
            $selectedEntriesResults = $soDocumentEntryManager->GetList($params);

            if (empty($selectedEntriesResults) || count($selectedEntriesResults) != count($docEntryKeys)) {
                throw new APIExecutionException(I18N::getSingleToken('IA.UNABLE_TO_BULK_POST_TRANSACTIONS'));
            }

            // map dochdr keys to the selected transaction entries
            $docHeaderSelectedEntriesMapping = [];
            $docHeaderKeys = [];
            $docHeaderKeyDocIdMapping = [];
            foreach ( $selectedEntriesResults as $result ) {
                $docHeaderSelectedEntriesMapping[$result['DOCHDRNO']][] = $result['RECORDNO'];
                $docHeaderKeys[] = $result['DOCHDRNO'];
                $docHeaderKeyDocIdMapping[$result['DOCHDRNO']] = $result['DOCHDRID'];
            }

            // get ALL the transaction entries associated to the transactions that need to be posted
            $params = [
                'selects' => ['RECORDNO', 'DOCHDRNO', 'DOCID'],
                'filters' => [[[ 'DOCHDRNO', 'IN', $docHeaderKeys]]]
            ];
            $allEntriesResults = $soDocumentEntryManager->GetList($params);

            // map dochdr key to ALL its transaction entries
            $docHeaderAllEntriesMapping = [];
            foreach ( $allEntriesResults as $result ) {
                $docHeaderAllEntriesMapping[$result['DOCHDRNO']][] = $result['RECORDNO'];
                if (empty($result['DOCID'])) {
                    throw new APIExecutionException(I18N::getSingleToken('IA.CANNOT_POST_ENTRIES_DOCUMENT_NUMBER_MISSING'));
                }
            }

            // determine if all entries of a transaction were selected for posting.  A discrepancy would be detected
            // in the following scenarios:
            // 1) User truly didn't select all entries of the transaction to be posted
            // 2) User selected all entries for a transaction but in another session an additional transaction
            //    entry (or entries) was added / deleted by another user but unknown to the current user
            // 3) User selected entries of a transaction(s) that has now been deleted
            $isDiscrepancyDetected = false;
            $docIdsWithDiscrepancy = [];
            foreach ($docHeaderSelectedEntriesMapping as $headerKey => $selectedEntries) {
                $allEntries = $docHeaderAllEntriesMapping[$headerKey] ?? [];
                sort($selectedEntries);
                sort($allEntries);
                if ($selectedEntries != $allEntries) {
                    $docIdsWithDiscrepancy[] = $docHeaderKeyDocIdMapping[$headerKey];
                    $isDiscrepancyDetected = true;
                }
            }

            if ($isDiscrepancyDetected) {
                // showing only the first 10 transactions for now due to sizing of the error display at the top of UI
                throw new APIExecutionException(I18N::getSingleToken('IA.TO_POST_ALL_TXN_ENTRIES_MUST_BE_SELECTED',
                                                                     [['name' => 'COUNT', 'value' => count($docIdsWithDiscrepancy)],
                                                                      ['name' => 'TXNS', 'value' => implode(", ", array_slice($docIdsWithDiscrepancy, 0, 10, true))]
                                                                     ]));
            } else {
                return $docHeaderKeyDocIdMapping;
            }
        }
    }

    /**
     * @param $docHdrKeyDocIdMap
     *
     * @return bool
     */
    private function postTransactionsOffline($docHdrKeyDocIdMap, &$runObjectKey)
    {
        $runObject = RevenueTxnConsumerHelper::prepareRunObjectForBulkPost($docHdrKeyDocIdMap);
        if ($runObject != null) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $runObjectManager = $gManagerFactory->getManager('runobject');
            $ok = $runObjectManager->add($runObject);
            $runObjectKey = $runObject[':RECORDNO'];
        } else {
            $ok = false;
        }
        return $ok;
    }
}
