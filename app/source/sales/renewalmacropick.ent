<?

/**
 *    FILE: RenewalMacroManager.cls
 *    AUTHOR: Nirmal
 *    DESCRIPTION:
 *
 *    (C) 2007, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *  Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */


$kSchemas['renewalmacropick'] = array(
    'schema' => array('RECORDNO' => 'record#', 'MACROID'=> 'macroid', 'TRANSACTIONTYPE' => 'transactiontype', 'TERMLENGTH' => 'termlength', 'TERMPERIOD' => 'termperiod'),
    'object' => array( 'MACROID', 'TRANSACTIONTYPE', 'TERMLENGTH', 'TERMPERIOD' ),
    'fieldinfo' => array(
        array (
            'fullname' => 'IA.RENEWAL_TEMPLATE_PICK',
            'type' => array ( 'ptype' => 'text', 'type' => 'text', 'maxlength' => 200 ),
            'required' => true,
            'path' => 'MACROID'
        ),
        array(
            'path' => 'TRANSACTIONTYPE',
            'fullname' => 'IA.TRANSACTION_TYPE',
            'type' => array(
                'ptype' => 'enum',
                'type'     => 'enum',
                'validvalues' => array('Sales Transaction', 'Contract'),
                '_validivalues' => array('SO', 'CN'),
                'validlabels' => array('IA.SALES_TRANSACTION', 'IA.CONTRACT'),
            ),
            'default'     => 'Sales Transaction',
        ),
        array(
            'path' => 'TERMPERIOD',
            'fullname' => 'IA.DEFAULT_RENEWAL_TERM_PERIOD',
            'type' => array(
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => array('Years', 'Months', 'Days'),
                '_validivalues' => array('Y', 'M', 'D'),
                'validlabels' => array('IA.YEARS', 'IA.MONTHS', 'IA.DAYS'),
            ),
            'default' => 'Months',
        ),
    ),
    'table'     => 'renewalmacro',
    'vid'         => 'MACROID',
    'module'     => 'so',
    'dbfilters' => array (array ('renewalmacropick.latestversionkey', 'ISNULL'),array('renewalmacropick.status','=','T')),
    'printas'    => 'IA.RENEWAL_TEMPLATES',
    'pluralprintas'    => 'IA.RENEWAL_TEMPLATES'
);

