<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" 
	xmlns:xsl	="http://www.w3.org/1999/XSL/Transform" 
	xmlns:fo	="http://www.w3.org/1999/XSL/Format"
    xmlns:fox	="http://xml.apache.org/fop/extensions">  

<xsl:include href="../../private/xslinc/miscdocument_inc.xsl"/>  
<xsl:template match="/">
	<fo:root 
		xmlns:fo	="http://www.w3.org/1999/XSL/Format"  
		xmlns:svg	="http://www.w3.org/2000/svg"
		xmlns:fox	="http://xml.apache.org/fop/extensions">     
	   
		<xsl:call-template name="pagemaster"/>          	
		<xsl:apply-templates/>
	</fo:root>
</xsl:template>
<xsl:template match="DOCUMENT">
	<xsl:apply-templates/>
</xsl:template>

<xsl:template match="OLDROOT">
    <fo:page-sequence 
		master-reference="psmA"
		initial-page-number="1"
		>
 		<fo:static-content 
			flow-name="xsl-region-after"
			>
			<xsl:call-template name="footer">
				<xsl:with-param name="col1" select="'footerquotedate'"/>
				<xsl:with-param name="col2" select="'footervaliduptodate'"/>
				<xsl:with-param name="col3" select="''"/>
				<xsl:with-param name="col4" select="'footercustomerid'"/>
				<xsl:with-param name="pagenum" select="'footerpage'"/>
			</xsl:call-template>
		</fo:static-content>
        <fo:flow 
			flow-name="xsl-region-body"
			>
			<xsl:call-template name="pagetop_ARS"/>
			<xsl:call-template name="titlequotenumber"/>
			<xsl:call-template name="titlequotedate"/>
			<xsl:call-template name="titlevaliduptodate"/>
			<xsl:call-template name="salesperson_ARS"/>		
			<xsl:choose>
				<xsl:when test="(REC/_DOCPAR/SHOWTITLE1 !='' ) and (REC/_DOCPAR/SHOWTITLE2 !='' )">
					<xsl:call-template name="displaycontacts"/>
				</xsl:when>
				<xsl:otherwise>
					<xsl:call-template name="quotetoaddress"/>
				</xsl:otherwise>
			</xsl:choose>
				<xsl:call-template name="confirmto_ARS"/>
				<xsl:call-template name="customerpo_ARS"/>
			<xsl:call-template name="quotebody"/>
        </fo:flow>
    </fo:page-sequence>
</xsl:template>

	<!-- print sales person from Customer : Rep -->
	<xsl:template name="salesperson_ARS">
		<fo:block-container border-color="white" border-style="solid" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt" height="1.0cm" width="2.0in" top="2.7cm" left="4.3in" padding="2pt" position="absolute">
			<fo:table table-layout="fixed">
				<fo:table-column column-width="1.0in"/>
				<fo:table-column column-width="1.9in"/>
				<fo:table-body font-family="Helvetica" font-weight="normal" font-size="10pt">
					<fo:table-row line-height="0.5cm">
						<fo:table-cell>
							<fo:block text-align="start">Sales Person : </fo:block>
						</fo:table-cell>
						<xsl:choose>
							<xsl:when test="REC/CUSTVEND/CUSTREPNAME !=''">
								<fo:table-cell>
									<fo:block>
										<xsl:value-of select="REC/CUSTVEND/CUSTREPNAME"/>
									</fo:block>
								</fo:table-cell>
							</xsl:when>
							<xsl:otherwise>
								<fo:table-cell>
									<fo:block/>
								</fo:table-cell>
							</xsl:otherwise>
						</xsl:choose>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block-container>
	</xsl:template>
	<!-- pagetop_custom -->
	<xsl:template name="pagetop_ARS">
		<xsl:call-template name="companyaddress_ARS"/>
		<xsl:call-template name="documentlabel"/>
	</xsl:template>
	<!-- confirmto custom field for this company-->
	<xsl:template name="confirmto_ARS">
		<fo:block space-before="0.3cm" border-color="white">
			<fo:table table-layout="fixed" border-color="white" border-style="solid" border-width="0.005pt" height="0.1cm">
				<fo:table-column column-width="6.0cm"/>
				<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="10pt">
					<fo:table-row>
						<fo:table-cell padding="6pt">
							<fo:block position="absolute"><xsl:text>Confirm To : </xsl:text><xsl:value-of select="REC/CONFIRM_TO"/>
							</fo:block>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
	</xsl:template>
	<!-- companyaddress_custom -->
	<xsl:template name="companyaddress_ARS">
		<fo:block text-align="start" height="4cm" width="12cm" space-before="0.85in" white-space-collapse="false" />
	</xsl:template>
	<!--Customer PO# -->
	<xsl:template name="customerpo_ARS">
		<fo:block space-before="0.1cm" border-color="white">
			<fo:table table-layout="fixed" border-color="white" border-style="solid" border-width="0.005pt" height="0.1cm">
				<fo:table-column column-width="6.0cm"/>
				<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="10pt">
					<fo:table-row>
						<fo:table-cell padding="6pt">
							<fo:block position="absolute"><xsl:text>Customer PO # : </xsl:text><xsl:value-of select="REC/PONUMBER"/>
							</fo:block>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
	</xsl:template>
	<!-- Customer PO with Term for category not 'R' -->
	<xsl:template name="customerpo_term_ARS">
		<fo:block space-before="0.1cm" border-color="white">
			<fo:table table-layout="fixed" border-color="white" border-style="solid" border-width="0.005pt" height="0.1cm">
				<fo:table-column column-width="6.0cm"/>
				<fo:table-column column-width="6.0cm"/>
				
				<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="10pt">
					<fo:table-row>
						<fo:table-cell padding="6pt">
							<fo:block position="absolute"><xsl:text>Customer PO # : </xsl:text><xsl:value-of select="REC/PONUMBER"/>
							</fo:block>
						</fo:table-cell>
						<fo:table-cell padding="6pt">
							<fo:block position="absolute"><xsl:text>Terms : </xsl:text><xsl:value-of select="REC/TERM/NAME"/>
							</fo:block>
						</fo:table-cell>						
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
	</xsl:template>	


</xsl:stylesheet>
