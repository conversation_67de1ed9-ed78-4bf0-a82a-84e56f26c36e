<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">

<xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/> 
<xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/> 

<xsl:variable name="varMcEnabled">
    <xsl:value-of select="/reportdata/report/@ISMCENABLED"/>
</xsl:variable>

    <xsl:template match="/">
        <xsl:apply-templates/>          
    </xsl:template>
    
    <xsl:template match="reportdata">
        <xsl:apply-templates/>
    </xsl:template>

    <xsl:template match="report">

        <report
            department      = "{@department}"
            location        = "{@location}"
            orientation     = "Landscape"
            report_date     = "{@reportdate}"
            report_time     = "{@reporttime}"
            align_currency  = "left"
            page_number     = "Y"
            action          = ""
            sess            = "{@sess}"
            done            = "{@done}"
            footer_allpages = "Y"
        >

            <xsl:if test="(@orientation = 'Portrait')">
                <xsl:attribute name="maxfit">Y</xsl:attribute>
            </xsl:if>
            <company s="2"><xsl:value-of select="@co"/></company>
            <title s="3"><xsl:value-of select="@title"/></title>
            <title s="3"><xsl:value-of select="@title2"/></title>
            <footer s="footer" lines="1"><xsl:value-of select="@titlecomment"/></footer>

            <xsl:call-template name="buildheaderrows"/>
            <xsl:apply-templates/>
            <xsl:call-template name="stylegroups"/>
            <script language="javascript">
              <xsl:apply-templates select="@javascript"/>
              <xsl:call-template name="script"/>        
            </script>
</report>
</xsl:template>

<xsl:template name="buildheaderrows">
    <header>
      <hrow s="6">
                <hcol id="0" width="17" s="17">IA.DOCUMENT_DATE</hcol>
                <hcol id="1" width="17" s="17">Customer</hcol>
                <hcol id="2" width="17" s="17">IA.DOCUMENT_ID</hcol>
                <xsl:if test="$varMcEnabled='Y'">
                  <col id="3" width="17" s="17">IA.CURRENCY</col>
                </xsl:if>
                <hcol id="4" width="17" s="17">IA.PRICE_LIST_NAME</hcol>
                <hcol id="5" width="17" s="17">IA.ITEM</hcol>
                <hcol id="6" width="17" s="17">IA.STATUS</hcol>
                <hcol id="7" width="17" s="18">IA.FAIR_VALUE_PRICE</hcol>
                <hcol id="8" width="17" s="18">IA.EXTENDED_FAIR_VALUE_PRICE</hcol>
                <hcol id="9" width="17" s="18">IA.TXN_PRICE</hcol>
                <xsl:if test="$varMcEnabled='Y'">
                  <hcol id="10" width="17" s="18">IA.MEA_ALLOCATION_TXN</hcol>
                </xsl:if>
                <hcol id="11" width="18" s="18">IA.MEA_ALLOCATION<xsl:if test="($varMcEnabled='Y')">(<xsl:value-of select="/reportdata/report/@BASECURR"/>)</xsl:if>
                </hcol>
                <hcol id="12" width="17" s="18">IA.REALLOCATED</hcol>
      </hrow>
    </header>
</xsl:template>

<xsl:template match="NODATA">
    <xsl:if test="string(@NODATA)=1">
        <row s="14">
            <col id="0" s="19" colspan="8">IA.NO_DATA_FOUND</col>
        </row>
    </xsl:if>
</xsl:template>

    <xsl:template match="VSOESUMMARY">
      <body s="1">
        <xsl:apply-templates/>
      </body>
    </xsl:template>

    <xsl:template match="VSOESUBTOTAL">
      <xsl:apply-templates/>
      <row s="12">
            <col id="1" s="23"><xsl:value-of select="@DOCDATE"/></col>
            <col id="1" s="23"><xsl:value-of select="@CUSTOMER"/></col>
            <col id="1" s="23"><xsl:value-of select="@DOCID"/></col>
            <xsl:if test="$varMcEnabled='Y'">
              <col id="1" s="23"><xsl:value-of select="@CURRENCY"/></col>
            </xsl:if>
            <col id="1" s="23"><xsl:value-of select="@VSOEPLNAME"/></col>
            <col id="1" s="23"><xsl:value-of select="@ITEMNAME"/></col>
            <col id="1" s="23"><xsl:value-of select="@DLVRSTATUS"/></col>
            <col id="1" s="33"><xsl:value-of select="@VSOEUNITPRICE"/></col>
            <col id="1" s="59" colspan="2"><xsl:value-of select="@VSOEEXTPRICE"/></col>
            <xsl:if test="$varMcEnabled='Y'">
              <col id="1" s="34"><xsl:value-of select="@TRX_VSOEALLOCATION"/></col>
            </xsl:if>
            <col id="1" s="34"><xsl:value-of select="@VSOEALLOCATION"/></col>
            <col id="1" s="23"></col>
        </row>
    </xsl:template>

    <xsl:template match="VSOE">
      <row s="12">
        <xsl:variable name="varValueBase">
          <xsl:value-of select="@VALUEBASE"/>
        </xsl:variable>
            <col id="1" s="23"><xsl:value-of select="@DOCDATE"/></col>
            <col id="1" s="23"><xsl:value-of select="@CUSTOMER"/></col>
            <col id="1" s="23">
              <xsl:attribute name="href">
                <xsl:value-of select="@DOCIDHREF"/>
              </xsl:attribute>
              <xsl:value-of select="@DOCID"/>
            </col>
            <xsl:if test="$varMcEnabled='Y'">
              <col id="1" s="23"><xsl:value-of select="@CURRENCY"/></col>
            </xsl:if>
            <col id="1" s="23"><xsl:value-of select="@VSOEPLNAME"/></col>
            <col id="1" s="23"><xsl:value-of select="@ITEMNAME"/></col>
            <col id="1" s="23"><xsl:value-of select="@DLVRSTATUS"/></col>
            <xsl:choose>
              <xsl:when test="$varValueBase='A'">
                <col id="1" s="34"><xsl:value-of select="@VSOEUNITPRICE"/></col>
              </xsl:when>
              <xsl:otherwise>
                <col id="1" s="22"><xsl:value-of select="@VSOEUNITPRICE"/></col>
              </xsl:otherwise>
            </xsl:choose>
            <col id="1" s="34"><xsl:value-of select="@VSOEEXTPRICE"/></col>
            <col id="1" s="34"><xsl:value-of select="@INVOICEPRICE"/></col>
            <xsl:if test="$varMcEnabled='Y'">
              <col id="1" s="34"><xsl:value-of select="@TRX_VSOEALLOCATION"/></col>
            </xsl:if>
            <col id="1" s="34"><xsl:value-of select="@VSOEALLOCATION"/></col>
            <col id="1" s="23"><xsl:value-of select="@ISREALLOCATED"/></col>
        </row>
    </xsl:template>
</xsl:stylesheet>