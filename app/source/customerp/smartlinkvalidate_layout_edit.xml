<?xml version='1.0' encoding='UTF-8'?>
<ROOT assoc="T" >
    <entity>smartlinkvalidate</entity>
	<title>IA.SMART_RULES</title>
	<helpfile>Account_Information</helpfile>
    <pages>
        <page assoc="T" >
			<helpfile>CLC101</helpfile>
            <title>IA.SELECT_OBJECT</title>
            <path>OBJECTPAGE</path>
			<instruction>IA.CHOOSE_OBJECT_TO_EXTEND</instruction>
			<fields>
				   <field>RECORDNO</field>
				   <field>PACKAGEKEY</field>
				   <field>OWNEROBJECT</field>
				   <field>DOCTYPE</field>
			</fields>
        </page>
        <page assoc="T" >
			<helpfile>CLC102</helpfile>
            <title>IA.SELECT_PROPERTIES</title>
            <path>PROPERTYPAGE</path>
			<instruction>IA.SELECT_RULE_PROPERTIES</instruction>
			<fields>
				<field>ERRORTYPE</field>
				<field>EVENTSCONTROL</field>
				<SinglelineLayout assoc="T" key="hbox">
					<fullname>IA.CONDITION</fullname>
					<required>true</required>
					<columns>
						<column assoc="T">
							<path>CONDITION</path>
						</column>
						<column assoc="T">
							<path>LABELDATETIME</path>
						</column>
					</columns>
					<_func>SinglelineLayout</_func>
				</SinglelineLayout>
				<field>ERRORMESSAGE</field>
			</fields>
        </page>
        <page assoc="T" >
			<helpfile>CLC103</helpfile>
            <title>IA.SAVE</title>
            <path>SAVEPAGE</path>
			<instruction>IA.SELECT_DEPLOYMENT_OPTIONS</instruction>
			<fields>
				<field>SMARTLINKID</field>
				<field>DESCRIPTION</field>
				<field>STATUS</field>
			</fields>
        </page>
    </pages>
</ROOT>