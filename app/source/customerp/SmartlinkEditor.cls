<?php
import('IAWizard');
import('NexusUIInfo');
import('NexusDBInfo');
import('RenderDetailsDOMHelper');

/**
 */
class SmartlinkEditor extends CustomComponentEditor
{
    /**
     * @var RenderDetailsDOMHelper $rddomhelper
     */
    var $rddomhelper;

    /**
     * Smart link objects specific tokens
     * @var string[] ADDITIONAL_TOKENS
     */
    private const ADDITIONAL_TOKENS = [
        'IA.CONDITION',
        'IA.NOTE_USE_THE_YYYY_MM_DD_FORMAT_FOR_DATE_VALUES',
        'IA.SELECT_NO_DASHES',
        'IA.SHOW_MORE',
        'IA.ADD_OR_EDIT',
        'IA.DONE_TEXT'
    ];
    
    const EVENT_TO_TOKEN_MAP = [
        CUSTOMERP_EVENT_ADD => 'IA.ADD',
        CUSTOMERP_EVENT_SET => 'IA.SET',
        CUSTOMERP_EVENT_DELETE => 'IA.DELETE',
        CUSTOMERP_EVENT_APPROVE => 'IA.APPROVE',
        CUSTOMERP_EVENT_DECLINE => 'IA.DECLINE',
        CUSTOMERP_EVENT_PAID => 'IA.PAID'
    ];
    
    /**
     * @param array $params
     */
    function __construct($params)
    {
        parent::__construct($params);
        $this->additionalTokens = array_merge($this->additionalTokens, self::ADDITIONAL_TOKENS);
        $this->rddomhelper = new RenderDetailsDOMHelper();
    }

    /**
     * New ActionHandlers
     *
     */
    function InitConstants()
    {
        parent::InitConstants();
        $this->kActionHandlers[$this->kExportAction] = [
            'handler' => 'ProcessExportAction',
            'states'  => [ $this->kShowNewState, $this->kShowViewState, $this->kShowEditState ],
            'csrf'    => true,
        ];
    }

    /**
     * Include new buttons for display
     *
     * @param array $_params
     *
     * @return array
     */
    function Buttons_Instantiate($_params)
    {
        $_params = parent::Buttons_Instantiate($_params);
        switch ($_params['state']) {
            case Template_CreateWarningState:
            case Editor_ShowNewState:
                $exportbutton = GT($this->textMap,'IA.EXPORT_DEF');
                $exportaction = 'export';
                break;
            case Template_EditWarningState:
            case Editor_ShowEditState:
                $exportbutton = GT($this->textMap,'IA.EXPORT_DEF') ;
                $exportaction = 'export';
                break;
            case Editor_ShowViewState:
                $exportbutton = GT($this->textMap,'IA.EXPORT_DEF');
                $exportaction = 'export';
                break;
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['exportbutton'] = $exportbutton;
        /** @noinspection PhpUndefinedVariableInspection */
        $_params['exportaction'] = $exportaction;

        return $_params;
    }

    /**
     * Shows Export Def button
     *
     * @param array $_params
     */
    function ShowFormButtons($_params)
    {
        Editor::ShowFormButtons($_params);
        $_params = $_params['buttons'];

        // Mode
        $do = $this->GetWizardMode();
        //eppp($do);

        // Run
        $pgIdx = $this->GetDestinationPageIndex();
        //eppp($pgIdx);

        $lastIdx = $this->GetLastPageIndex();
        //eppp($lastIdx);

        $firstIdx = $this->GetFirstPageIndex();
        //eppp($firstIdx);

        // Export
        if ( $pgIdx == $lastIdx || $do == 'edit' ) {
            $this->ShowExportButton($_params);
        }

        // Prev
        if ( $pgIdx != $firstIdx ) {
            $this->ShowPrevButton($_params);
        }

        // Goto
        if ( ! ( $do == 'create' && $pgIdx == 0 ) ) {
            $this->ShowGotoButton($_params);
        }

        // Next
        if ( $pgIdx != $lastIdx ) {
            $this->ShowNextButton($_params);
        }
        if ($this->verb != 'view' && $pgIdx != $firstIdx && $pgIdx != $lastIdx) {
            $ownerObject = isl_strtoupper($this->getOwnerObject());
            $docType = urlencode($this->GetDocType());
            ?>
            <INPUT id="fieldlookupbtn" type="button" name="fieldlookupbtn" class="nosavehistory <?=Pt_SetupComponents::$emptyOrBtnPrimaryClass?>"
                   value="<?=GT($this->textMap, 'IA.FIELD_LOOKUP');?>"
                   onclick="displayFieldPopup(<? echo "'{$ownerObject}','{$docType}'"; ?>)" value="<?=GT($this->textMap, 'IA.FIELD_LOOKUP');?>"/>
            <?
        }
    }

    /**
     * Export Button
     *
     * @param array &$_params
     */
    function ShowExportButton(&$_params)
    {
        $exportbutton = $_params['exportbutton'];
        $exportaction = $_params['exportaction'];
        if ( $exportbutton ) { ?>
            <INPUT id="exportbuttid" type="button" name="exportbutton" class="nosavehistory <?=Pt_SetupComponents::$emptyOrBtnPrimaryClass?>"
                   value="<?= $exportbutton ?>"
                   onclick="document.forms[0].<?= Editor_Action ?>.value = '<?= $exportaction ?>';if(BeforeSubmit()){document.forms[0].submit();}">
            <?
        }
    }

    /**
     * Resolve allowed owner objects
     *
     * @param array $_params
     *
     * @return array
     */
    function Editor_Expand($_params)
    {
        $_params = parent::Editor_Expand($_params);

        return $_params;
    }

    /**
     * Converts data into Wizard display format
     *
     * @param array &$_params
     */
    function ProcessViewAction(&$_params)
    {
        //To display Valid Label in view mode
        parent::ProcessViewAction($_params);
        $this->ProcessCurrentObjectToView();
    }

    /**
     * Converts data into Wizard display format
     *
     * @param array &$_params
     */
    function ProcessEditAction(&$_params)
    {
        parent::ProcessEditAction($_params);
        $this->ProcessCurrentObjectToView();
    }

    /**
     * Converts data into storage format
     *
     * @param array &$_params
     */
    function ProcessSaveAction(&$_params)
    {
        global $gErr;
        $ok = $this->ValidateCurrentObject();
        if ( ! $ok && $gErr->hasErrors() ) {
            $this->state = $this->kErrorState;

            return;
        }
        $this->ProcessCurrentObjectToStore();
        parent::ProcessSaveAction($_params);
    }

    /**
     * Converts data into storage format
     *
     * @param array &$_params
     *
     * @return null|bool
     */
    function ProcessCreateAction(&$_params)
    {
        global $gErr;
        $ok = $this->ValidateCurrentObject();
        if (!$ok && $gErr->hasErrors()) {
            $this->state = $this->kErrorState;

            return null;
        }
        $this->ProcessCurrentObjectToStore();

        return parent::ProcessCreateAction($_params);
    }

    /**
     * Handles Next Button
     *
     * @param array &$_params
     */
    function ProcessNextAction(&$_params)
    {
        //eppp($_params);
        //eppp($GLOBALS['_GET']);
        //eppp($GLOBALS['_POST']);
        //global $gRequest;
        //$obj =& $gRequest->GetCurrentObject();
        //eppp($obj);

        parent::ProcessNextAction($_params);
    }
    
    /**
     * Handles Previous Button
     *
     * @param array &$_params
     */
    function ProcessPrevAction(&$_params)
    {
        //eppp($_params);
        //eppp($GLOBALS['_GET']);
        //eppp($GLOBALS['_POST']);
        //global $gRequest;
        //$obj =& $gRequest->GetCurrentObject();
        //eppp($obj);

        parent::ProcessPrevAction($_params);
    }

    /**
     * Package XML export
     *
     * @param array &$_params
     */
    function ProcessExportAction(&$_params)
    {
        // This will create correct structure
        $this->ProcessCurrentObjectToStore();

        $obj =& Request::$r->GetCurrentObject();
        //eppp($obj);
        //dieFL();

        $out_xml = $this->rddomhelper->GetSmartlinkXML($obj);
        //dieFL($out_xml);

        $title = $obj['SMARTLINKID'];
        if ( $title == '' ) {
            $title = 'SmartLink';
        }

        header("Content-type: text/xml");
        header("Content-Disposition:  attachment; filename=\"$title.xml\"");
        header("Content-Length: " . strlen($out_xml));
        echo $out_xml;
        exit();
    }

    /**
     * Validation logic
     *
     * @return bool
     */
    function ValidateCurrentObject()
    {
        $obj =& Request::$r->GetCurrentObject();
        //eppp($obj);

        $condition = $obj['CONDITION'];
        //eppp_p($condition);
        //dieFL();

        $ok = true;
        if ( $condition != '' ) {
            // $obj['DOCTYPE'] may have multiple values concatenated by #~#: PDLC-1612-18
            $doctypes = explode('#~#', $obj['DOCTYPE']);
            import('ConditionEvaluator');
            foreach ( $doctypes as $doctype) {
                $ok = $ok && ConditionEvaluator::validate($condition, $doctype);
            }
            //eppp($ok);
        }

        //dieFL();
        return $ok;
    }

    /**
     * Create current object field for editor to make sense of it
     *
     * @return bool
     */
    function ProcessCurrentObjectToView()
    {
        $obj =& Request::$r->GetCurrentObject();
        //eppp($obj);
        //dieFL();
        $ok = true;

        // Common fields
        $label = "";
        if ( $obj['CUSTOMCOMPONENT']['LABEL'] != '' ) {
            $label = $obj['CUSTOMCOMPONENT']['LABEL'];
        } else if ( $obj['CUSTOMCOMPONENT.LABEL'] != '' ) {
            $label = $obj['CUSTOMCOMPONENT.LABEL'];
        }

        $fieldset = "";
        if ( $obj['CUSTOMCOMPONENT']['CUSTOMLAYOUTCOMPONENT']['FIELDSET'] != '' ) {
            $fieldset = $obj['CUSTOMCOMPONENT']['CUSTOMLAYOUTCOMPONENT']['FIELDSET'];
        } else if ( $obj['CUSTOMCOMPONENT.CUSTOMLAYOUTCOMPONENT.FIELDSET'] != '' ) {
            $fieldset = $obj['CUSTOMCOMPONENT.CUSTOMLAYOUTCOMPONENT.FIELDSET'];
        }

        $obj['LABEL'] = $label;
        $obj['FIELDSET'] = $fieldset;
        $this->setObjectPage($obj);
        Request::$r->SetCurrentObject($obj);

        return $ok;
    }

    /**
     * Create current object, for manager to make sense of it
     *
     * @return bool
     */
    function ProcessCurrentObjectToStore()
    {
        $obj =& Request::$r->GetCurrentObject();
        //eppp($obj);
        //dieFL();

        // Common fields
        $page = ( $obj['EXISTINGPAGE'] != '' ) ? $obj['EXISTINGPAGE'] : $obj['NEWPAGE'];
        $obj['SMARTLINKID'] = isl_strtoupper($obj['SMARTLINKID']);
        $obj['CUSTOMCOMPONENT']['LABEL'] = $obj['LABEL'];
        $obj['CUSTOMCOMPONENT']['CUSTOMLAYOUTCOMPONENT']['PAGE'] = $page;
        $obj['CUSTOMCOMPONENT']['CUSTOMLAYOUTCOMPONENT']['FIELDSET'] = $obj['FIELDSET'];

        return true;
    }

    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss include the YUI css files
     */
    function showScripts($addYuiCss = true)
    {
        parent::showScripts($addYuiCss);
        ?>
        <script src="../resources/js/nexus.js"></script>
        <script src="../resources/js/smartlink.js"></script>
        <script language="javascript">
            function fetchDocType(object) {
                <?
                $_op = Request::$r->_op;
                $_sess = Session::getKey();
                ?>
                var url = "nexus.phtml?.sess=<?=$_sess?>&.op=<?=$_op?>&_action=getdoctype";
                url += '&object=' + object;
                xml = doAjax(url);
                return xml;
            }
        </script>
        <?
    }

    /**
     * Custom fields uploaded through package are not editable
     *
     * @return bool
     */
    function CanEdit()
    {
        $obj =& Request::$r->GetCurrentObject();
        if ( isset($obj['PACKAGEKEY']) ) {
            return false;
        }

        // PDLC-0584-10
        $appIds = Pt_SysRelsManager::getLeftIds(REL_APP_SMART_LINK, $obj['RECORDNO']);
        if ( count($appIds) > 0 ) {
            return false;
        }

        return parent::CanEdit();
    }

    /**
     * Hides doc type
     *
     * @param array &$_field
     * @param bool  $_inlineitem
     * @param bool  $encodeLabel
     */
    function ShowSimpleFieldLabel(&$_field, $_inlineitem = false, $encodeLabel = true)
    {
        $mode = $this->GetWizardMode();
        //eppp($mode);

        // New page option
        if ( $mode === 'view' && in_array($_field['path'], [ SmartLinkManager::DATETIMELABEL ]) ) {
            return;
        }
        // Document type
        if ( $_field['path'] === 'DOCTYPE' ) {
            $ownerObject = $this->getOwnerObject();
            $display = "none";
            if ( $this->nexusui->IsSCMObject($ownerObject) ) {
                $display = "block";
            }
            ?>
            <div id="DOCTYPE_LABEL" style="display:<?= $display ?>"> <?
                parent::ShowSimpleFieldLabel($_field, $_inlineitem);
                ?> </div> <?
            return;
        }
        parent::ShowSimpleFieldLabel($_field, $_inlineitem);
    }

    /**
     * @param array &$_field
     */
    function ShowSimpleFieldValue(&$_field)
    {
        $mode = $this->GetWizardMode();
        // New page option
        if ( $mode === 'view' && in_array($_field['path'], [ SmartLinkManager::DATETIMELABEL ]) ) {
            return;
        }
        if ($_field['path'] === 'OWNEROBJECT') {
            $fieldLabels = $_field['type']['validlabels'];
            I18N::addTokens(I18N::tokenArrayToObjectArray($fieldLabels));
            $this->textMap = array_merge($this->textMap, I18N::getText());
            $_field['type']['validlabels'] = [];
            if (!empty($fieldLabels)) {
                foreach ($fieldLabels as $fieldLabel) {
                    $_field['type']['validlabels'][] = GT($this->textMap, $fieldLabel);
                }
            }
        }
        
        // Document type
        if ( $_field['path'] === 'DOCTYPE' ) {
            $ownerObject = $this->getOwnerObject();
            $display = "none";
            if ( $this->nexusui->IsSCMObject($ownerObject) ) {
                $doctypes = $this->nexusdb->GetAllDocType($ownerObject);
                //eppp($doctypes);

                $_field['type']['validlabels'] = $doctypes;
                $_field['type']['validvalues'] = $doctypes;
                //eppp($doctypeField);

                $display = "block";
            }
            ?>
            <div id="DOCTYPE_FIELD" style="display:<?= $display ?>"> <?
                parent::ShowSimpleFieldValue($_field);
                ?> </div> <?
            return;
        }
        parent::ShowSimpleFieldValue($_field);
    }

    /**
     * Get root object from request
     *
     * @return string
     */
    function GetDocType()
    {
        $doctype = Request::$r->GetCurrentObjectValueByPath("DOCTYPE");

        return $doctype;
    }

    /**
     * @param array $_field
     *
     * @return bool
     */
    public function FieldNeedsStar(&$_field)
    {
        $mode = $this->GetWizardMode();
        if ( $_field['path'] === 'DOCTYPE' && $mode === 'edit' ) {
            $needsStar = true;
        } else {
            $needsStar = parent::FieldNeedsStar($_field);
        }

        return $needsStar;
    }
}


