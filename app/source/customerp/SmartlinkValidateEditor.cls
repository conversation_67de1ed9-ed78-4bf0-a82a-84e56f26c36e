<?
import('SmartlinkEditor');

/**
 * Validate Wizard
 */
class SmartlinkValidateEditor extends SmartlinkEditor
{

    /**
     * Smart rule specific tokens
     * @var string[] ADDITIONAL_TOKENS
     */
    private const ADDITIONAL_TOKENS = [
        'IA.SMART_RULES',
        'IA.TYPE',
        'IA.EVENTS',
        'IA.ERROR_MESSAGE',
        'IA.ADD',
        'IA.SET',
        'IA.DELETE',
        'IA.APPROVE',
        'IA.DECLINE',
        'IA.PAID',
        'IA.SMART_RULE_ID',
        'IA.ERROR',
        'IA.WARNING',
    ];

    /**
     * @param array $params
     */
    public function __construct($params)
    {
        parent::__construct($params);
        $this->additionalTokens = array_merge($this->additionalTokens, self::ADDITIONAL_TOKENS);
    }

    /**
     * Create current object field to editor to make sense of it
     *
     * @return bool
     */
    function ProcessCurrentObjectToView()
    {
        $ok = parent::ProcessCurrentObjectToView();
        if ( ! $ok ) {
            return false;
        }

        $obj =& Request::$r->GetCurrentObject();
        //eppp($obj);
        //dieFL();

        $events = [];
        foreach ( $obj['EVENTS'] as $details ) {
            $events[] = $details['EVENT'];
        }
        $obj['EVENTSCONTROL'] = join('#~#', $events);

        // XML fields
        if ( $obj['RENDERDETAILS'] ) {
            $this->rddomhelper->parseValidate($obj['RENDERDETAILS'], $obj);
        }
        //eppp($obj);
        //dieFL();

        Request::$r->SetCurrentObject($obj);

        return $ok;
    }

    /**
     * Get objDefId parameter and set default Object picklist field to that value
     *
     * @param array $_params
     */
    function Editor_Instantiate(&$_params)
    {
        $objDefId = Request::$r->_objDefId;
        if ( isset($objDefId) && $objDefId != '' ) {
            $this->MatchTemplates($_params, [ 'path' => 'OWNEROBJECT' ], $field);
            $field[0]['default'] = $objDefId;
        }

        parent::Editor_Instantiate($_params);
    }

    /**
     * Create current object for manager to make sense of it
     *
     * @return bool
     */
    function ProcessCurrentObjectToStore()
    {
        $ok = parent::ProcessCurrentObjectToStore();
        if ( ! $ok ) {
            return false;
        }

        $obj =& Request::$r->GetCurrentObject();
        //eppp($obj);
        //dieFL();

        // XML fields
        $obj['RENDERDETAILS'] = $this->rddomhelper->constructValidate($obj);
        //eppp($obj);
        //dieFL();

        //eppp($obj['EVENTSCONTROL']);
        //dieFL();

        $obj['TYPE'] = 'validate';
        Request::$r->SetCurrentObject($obj);

        return $ok;
    }

    /**
     * Validation logic
     *
     * @return bool
     */
    function ValidateCurrentObject()
    {
        //eppp_p("SmartlinkValidateEditor::ValidateCurrentObject");

        $obj =& Request::$r->GetCurrentObject();
        //eppp_p($obj);
        //dieFL();
        // $obj['DOCTYPE'] may have multiple values concatenated by #~#: PDLC-1612-18
        $doctypes = explode('#~#', $obj['DOCTYPE']);
        //eppp_p($doctype);

        import('Injector');

        // Condition
        $condition = $obj['CONDITION'];
        //eppp_p($condition);
        foreach ( $doctypes as $doctype ) {
            if ( $condition != ''
                 && ! Injector::validateInjectionParameters($condition, $doctype, $resultstr, $allbadpath)
            ) {
                global $gErr;
                $helpstr = $condition;
                if ( count($allbadpath) > 0 ) {
                    $helpstr = join(", ", $allbadpath);
                }
                $gErr->addIAError(
                    "CERP-0002", __FILE__ . '.' . __LINE__,
                    "Invalid injection parameter used in condition" . $helpstr, ["HELPSTR" => $helpstr]
                );

                return false;
            }

            // Error Message
            $errmsg = $obj['ERRORMESSAGE'];
            //eppp_p($errmsg);

            if ( $errmsg != ''
                 && ! Injector::validateInjectionParameters($errmsg, $doctype, $resultstr, $allbadpath)
            ) {
                global $gErr;
                $helpstr = $errmsg;
                if ( count($allbadpath) > 0 ) {
                    $helpstr = join(", ", $allbadpath);
                }
                $gErr->addIAError(
                    "CERP-0003", __FILE__ . '.' . __LINE__,
                    "Invalid injection parameter used in error message " . $helpstr, ["HELPSTR" => $helpstr]
                );

                return false;
            }
        }
        $ok = parent::ValidateCurrentObject();

        return $ok;
    }
}
