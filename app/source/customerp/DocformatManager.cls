<?php
/**
 * This is Custom Document Template Manager
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * DocformatManager
 *    manager sub class for Custom Document Template
 */
class DocformatManager extends EntityManager
{
    /**
     * Get
     *    overriding base function
     *
     * @param string $ID     id of custom document template
     * @param array  $fields select fields
     *
     * @return array  array of values
     */
    function Get($ID, $fields = null)
    {
        list($ID) = explode("--", $ID);
        $values = parent::get($ID, $fields);
        if (isset($values['XSL'])) {
            $values['XSL'] = databaseStringUncompress($values['XSL']);
        }
        return $values;
    }

    /**
     * Add
     *    overriding base function for translation
     *
     * @param array &$values array of fields
     * 
     * @return bool  indicates success or failure
     */
    protected function regularAdd(&$values)
    {
        // Check if template with the same name already existed, 
        // if yes, update the existing one instead of creating a new one.
        $existing_recordno = $this->getTemplateRecordno($values['DESCRIPTION'], $values['MODULEID']);
        if($existing_recordno) {
            $values['RECORDNO'] = $existing_recordno;
            return $this->set($values);
        }

        $ok = $this->PrepValues($values, 'add');

        $ok = $ok && parent::regularAdd($values);
        $ok = $ok && $this->SetDefault($values);

        return $ok;
    }

    /**
     * Return the next record# using the globaldocxsl view
     *
     * @param int $reserveCnt
     *
     * @return int|false value of the next record#
     */
    public function GetNextRecordKey($reserveCnt = 1)
    {
        $qry = "select GET_NEXT_RECORD_TEMPLATE as id from dual";
        $result = QueryResult([$qry]);
        return $result[0]['ID'];
    }

    /**
     * SetDefault
     *    Set the given template as the default one of the same type
     *  and set other templates of the same type to non-default.
     *  
     * @param array &$values array of fields
     * 
     * @return bool  indicates success or failure
     */
    function SetDefault(&$values)
    {
        $ok = true;

        if ( $values['ISDEFAULT'] != 'false' ) {
            $args = array($values['CNY'], $values['MODULEID'], $values['DOCTYPE'], $values['RECORDNO']);
            $ok = $this->DoQuery('QRY_DOCFORMAT_SET_DEFAULT', $args);
        }

        return $ok;
    }

    /**
     * Set
     *    overriding base function
     *  
     * @param array &$values array of fields
     * 
     * @return bool  indicates success or failure
     */
    protected function regularSet(&$values)
    {
        $ok = $this->PrepValues($values);;

        //eppp_p($values);dieFL();
        $ok = $ok && parent::regularSet($values);     
        $ok = $ok && $this->SetDefault($values);

        return $ok;
    }

    /**
     * Return a list of entities
     *   This method is overridden in order to provide the proper cny# context (lost during
     *   this call because of it's .ent status as a 'global').
     *
     * @param array $params
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array[]
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        //  Temporarily set the object schema global to false.  This allows the EntityManager's SQL generation
        //   to properly set the cny# context (otherwise, it returns ALL custom docs, not just the ones
        //   in the current company).
        $prevValue = $this->SetSchemaGlobal(false);

        $ret = parent::GetList($params, $_crosscny, $nocount);

        foreach ($ret as $key => $row) {
            if (isset($row['XSL'])) {
                $ret[$key]['XSL'] = databaseStringUncompress($row['XSL']);
            }
        }

        //  Reset the global status, and return.
        $this->SetSchemaGlobal($prevValue);

        return $ret;
    }

    /**
     * PrepValues
     *    overriding base function
     *  
     * @param array  &$values array of fields
     * @param string $mod     mode
     * 
     * @return bool  indicates success or failure
     */
    function PrepValues(&$values, $mod='edit')
    {
        global $gErr;
        $ok = true;

        $values['CNY'] = GetMyCompany();
        $values['USERKEY'] = GetMyUserid();
        $values['WHENMODIFIED'] = GetTimestampGMT();

        if ( !isset($values['ISDEFAULT']) || empty($values['ISDEFAULT']) ) {
            $values['ISDEFAULT'] = 'false';
        }

        if ( isset($values['STATUS']) ) {
            if ( $values['STATUS'] == 'inactive' ) {
                $values['ISDEFAULT'] = 'false';
            }
        }

        // Fetch the content of the word document.
        //if ( !isset($values['XSL']) && $_FILES['XSL']['tmp_name'] != '') {
        //	$fp = fopen ($_FILES['XSL']['tmp_name'], 'r');
        //	$doc = fread($fp,$_FILES['XSL']['size']);
        //	fclose($fp);
        //	$values['XSL'] = $doc;
        //}

        //  Keep the blob field's value when editing or copying.
        if ($values['RECORDNO']!='' && !isset($values['XSL'])) {
            $Query[0]=  "select XSL,TEMPLATETYPE from v_iadocxsl where cny# in (0,  :1 ) and record#=:2";
            $Query[1]=  GetMyCompany();
            $Query[2]=  $values['RECORDNO'];
            $temp   =   QueryResult($Query);
            if(empty($temp)) {
                $gErr->addError(
                    'CORE-1156', GetFL(), 'Save template failed',
                    'The original template has been deleted by other user.'
                );
                $ok = false;
            }
            $temp   =   $temp[0];
            $values['XSL'] = databaseStringUncompress($temp['XSL']);
            $values['TEMPLATETYPE'] =   $temp['TEMPLATETYPE'];
            unset($Query);
        } else {
            //$_parts = explode(".", $_FILES['XSL']['name']);
            // $_parts = explode(".", $values['XSL_FILE_INFO']['name']);
            if(isset($values['XSL_FILE_INFO']['name'])){
                $_parts = explode(".", $values['XSL_FILE_INFO']['name']);
            } else {
                $_parts = explode(".", 'file.' . isl_strtolower($values['TEMPLATETYPE']));
            }
            if (isl_strtolower($_parts[count($_parts)-1]) == 'doc') {
                $values['TEMPLATETYPE'] = 'DOC';
            } else if (isl_strtolower($_parts[count($_parts)-1]) == 'docx') {
                $values['TEMPLATETYPE'] = 'DOX';
            } else if (!empty($_parts[count($_parts)-1])) {
                $part = $_parts[count($_parts)-1];
                $gErr->addIAError(
                    'CORE-1157', GetFL(), 'Error file type: .' . $part, ['PART'=>$part],
                    'Please upload .doc or .docx template file.'
                );
                $currentObject = Request::$r->GetCurrentObject();
                $currentObject['XSL'] = '';
                Request::$r->SetCurrentObject($currentObject);
                $ok = false;
            } else {
                $gErr->addError(
                    'CORE-1158', GetFL(), 'Template file is required',
                    'Please upload .doc or .docx template file.'
                );
                $ok = false;
            }
        }

        // Show error msg if template name has special characters.
        if ( !isset($values['DESCRIPTION']) 
            || !isl_preg_match("/^[\w\s_\-\.]{0,40}$/", isl_trim($values['DESCRIPTION'])) 
        ) {
            $gErr->addIAError(
                'CORE-1159', GetFL(), 'Save template failed.',
                [],
                'Illegal format for custom document: Template Name '.$values['DESCRIPTION'],
                ['VALUES_DESCRIPTION'=>$values['DESCRIPTION']]
            );
            $ok = false;
        }

        // Show error msg if description is more than 100 chars.
        if (isl_strlen($values['DESCRIPTIONEXT']) > 100) {
            $gErr->addError(
                'CORE-1160', GetFL(), 'Save template failed.',
                'The description text should be less than 100 characters.'
            );
            $ok = false;
        }

        if ( $ok && $mod == 'add' ) {
            $values['RECORDNO'] = null;
        }

        if (isset($values['XSL'])) {
            $values['XSL'] = databaseStringCompress($values['XSL']);
        }

        return $ok;
    }

    /**
     * getTemplateRecordno
     *    query by name
     *  
     * @param array  $docname  custom docsument name
     * @param string $moduleid moduleid
     * 
     * @return bool  indicates success or failure
     */
    function getTemplateRecordno($docname, $moduleid)
    {
        $args = array($docname, GetMyCompany(), $moduleid);
        $res = $this->DoQuery('QRY_DOCFORMAT_SELECT_RAW_FROM_RECORDNO', $args);

        return $res[0]['RECORD#'];
    }

    /**
     * IsDeletable
     *    overriding base function
     *  
     * @param string  $_id   custom docsument id
     * @param bool $doget boolean to query or not
     * @param array   $raw   moduleid
     * 
     * @return bool  indicates success or failure
     */
    function IsDeletable($_id, $doget=true, $raw = null)
    {
        $ok = parent::IsDeletable($_id, $doget, $raw);

        // Check if template is owned by Transaction Definitions
        if ($ok) {
            global $gErr;
            // Check if template is owned by Order Entry Transaction Definitions
            $code = 'QRY_CHECK_DOCPAR_EXISTS_FOR_DOCXSL';
            $return =  $this->DoQuery($code, array($_id));
            if ( !is_null($return[0]['RECORD#']) ) {
                $tds = '';
                foreach($return as $value) {
                    $tds = $tds.$value['DOCID'].' ';
                }
                $gErr->addIAError(
                    'CORE-1161', __FILE__.':'.__LINE__, '', [],
                    "This template cannot be deleted because Order Entry/Purchasing Transaction Definitions: ".
                    $tds ." are referencing it through field DOCXSLKEY.", ['TRANSACTION_DEFINITION'=>$tds],
                    "Reset Order Entry/Purchasing Transaction Definitions that referenced first, ".
                    "and then delete this template."
                );
                return false;
            }

            // Check if template is owned by Accounts Receivable Transaction Definitions
            global $kARid;
            $res = GetModulePreferences($kARid, $preferences);
            $value = $res['INVOICEFORMAT'] ?? null;

            if ( !is_null($value) && $value == $_id ) {
                $gErr->addError(
                    'CORE-1162', __FILE__.':'.__LINE__, '',
                    "This template cannot be deleted because Accounts Receivable reference it.",
                    'Delete all records that reference this template first, and then delete this template.'
                );
                return false;
            }
        }

        return $ok;
    }


    /**
     * BaseGet
     *    Get Intacct's baseline document or normal document.
     *  Document owner is "AP Clerk" when click "Print to..." in View custom document
     *  
     * @param string $ID custom docsument id
     * @param string[] $fields
     * 
     * @return array  record values
     */
    function BaseGet($ID, $fields = null)
    {
        $defaultCny = '0';
        $cny = $this->DoQuery("QRY_DOCFORMAT_CHECK_NOT_CUSTOM", [ $ID ]) ? $defaultCny : Request::$r->_cny;

        if (!is_null($cny) && $cny == $defaultCny) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $source = 'DocformatManager::BaseGet';
            if(count($this->customFields)) {
                $this->MergeQueries(); 
            }
            global $gErr;

            $code = 'QRY_' .  isl_strtoupper($this->_entity) . '_SELECT_SINGLE_BASELINEDOC_VID';
            $result = $this->DoQuery($code, array($ID), false);
            // This isn't really an error.  
            if (!is_array($result) || count($result) == 0) {
                if (count($gErr->errors) > 0) {
                    $gErr->addIAError('CORE-1163', __FILE__ . '.' . __LINE__, "Failed to run query $code",
                    ['CODE'=>$code]);
                }
                return array();
            }
            $obj = $this->_ProcessResult($result[0]);
            $this->_ProcessResultForOwnership($obj);

            // Set Intacct's baseline document owner as 'Intacct'.
            $obj['USERREC'] = 'Intacct';
            return $obj;
        } else {
            return parent::BaseGet($ID);
        }
    }

}
