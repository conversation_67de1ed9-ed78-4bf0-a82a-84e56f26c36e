<?php

require_once 'CustomERP.inc';

/* Object Catalog structure:
array('entityname' => array('ID' => 'ID',
						    'Name' => 'Name',
							'FieldList' => array('FieldName1' => array('ID' => 'ID',
																	  'Label' => 'Label',
																	  'Description' => 'Description',
																	  'Datatype' => 'Datatype',
																	  'Hidden' => boolean,
																	  'Required' => booean,
																	  'IsCustom' => boolean,
																	  'PackageName' => 'PackageName')
																	  'PackageKey' => 'PackageKey',
												 'FieldName2' => ...
												 )
							'SmartLinks' => array('Smartlink1' => array('ID' => 'ID',
																		'Label' => 'Label',
																		'Type' => 'Type',
																		'Description' => 'Description',
																		'PackageName' => 'PackageName',
																		'PackageKey' => 'PackageKey',
																		'Event' => 'Event',
																		'Condition' => 'Condition',
																		'ErrorType' => 'ErrorType',
																		'Details' => 'Details'),
												  'Smartlink2' => ...
												  )
							'Nexus' => array($kSchemas['entityname']['nexus']),
							'SupportedFeatures' => $kSchemas['entityname']['customerp']
));

*/

/**
 * Class CustomERPCatalog
 */
class CustomERPCatalog
{
    /**
     * @var array $packageMap
     */
    public $packageMap; // map of CustomERP Packages
    /**
     * @var ManagerFactory $MF
     */
    public $MF;  // handle to manager factory
    /**
     * @var array $mappedEntities
     */
    public $mappedEntities;   // array of already mapped entities
    /**
     * @var array $specialEnts
     */
    public $specialEnts;
    /**
     * @var array $catalog
     */
    public $catalog; // the catalog of data.
    /**
     * @var array $catalogTOC
     */
    public $catalogTOC; // catalog table of contents

    function __construct()
    {
        $this->MF = Globals::$g->gManagerFactory;

        // process = (full, partial)
        $this->specialEnts = array(
            'companypref' => array('process' => 'partial'),
        );

        $this->_BuildPackageMap();
    }

    /**
     * Used to get a simple list of standard objects used for drill-down in customization services
     *
     * @return array
     * @throws Exception
     */
    public function GetCustomERPCatalogTOC()
    {
        $catalogTOC = array();

        global $gCustomERPEntityFeatures;
        foreach ($gCustomERPEntityFeatures as $entitykey) {
            $em = $this->MF->getManager($entitykey);
            if (array_key_exists('printas', $em->_schemas[$entitykey])) {
                $name = $em->_schemas[$entitykey]['printas'];
            } else {
                $name = isl_strtoupper($entitykey);
            }

            $catalogTOC[isl_strtoupper($entitykey)] = $name;
        }
        ksort($catalogTOC);

        return $catalogTOC;
    }

    /**
     * @return array
     */
    function GetCustomERPCatalog()
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $catalog = array();
        global $gCustomERPEntityFeatures;
        // Get Documents for SCM entities
        $SCMDocuments = self::GetSCMDocuments();
        foreach ($gCustomERPEntityFeatures as $entity) {
            if (isset($SCMDocuments[$entity])) {
                foreach ($SCMDocuments[$entity] as $doctype) {
                    if (in_array($entity . "__" . $doctype, $this->mappedEntities)) {
                        continue;
                    }
                    $this->_GetEntityData($entity, $doctype, false, true, false);
                }
            } else {
                if ( $this->mappedEntities && in_array($entity, $this->mappedEntities) ) {
                    continue;
                }
                $this->_GetEntityData($entity, '', false, true, false);
            }
        }

        // Build any non-standard objects.
        // -- company combines company and companypref
        $this->_AnnexObject('COMPANY', 'COMPANYPREF');


        // USERINFO is also published as CURRENTUSER
        $userinfo = $this->catalog['USERINFO'];
        //eppp_p($userinfo);
        //dieFL();

        $userinfo['ID'] = 'CURRENTUSER';
        $userinfo['SHOWID'] = 'CURRENTUSER';
        $userinfo['Name'] = 'Current User';

        $this->catalog['CURRENTUSER'] = $userinfo;

        // sort the catalog
        ksort($this->catalog);

        return $this->catalog;
    }

    /**
     * @return array
     */
    public static function GetSCMDocuments()
    {
        // To Get the entity level doctypes for a MEGA company set the report view context.
        if (IsMultiEntityCompany() && !GetContextLocation()) {
            SetReportViewContext();
        }

        $qry = "
			SELECT sale_pur_trans, docid
			FROM docpar WHERE status = 'T'
			and sale_pur_trans in ('I','S','P')
			and latestversionkey IS NULL
			and cny# = :1
			and docid not in ('Build Kits','Disassemble Kits')
			ORDER BY sale_pur_trans, docid asc
		";
        $docs = QueryResult(array($qry, GetMyCompany()));

        // Set the view context back to transaction after retrieving the doctypes.
        if (IsMultiEntityCompany() && !GetContextLocation()) {
            SetTransactionViewContext();
        }

        $mappedDocs = array();
        foreach ($docs as $doc) {
            $mappedDocs[$doc['SALE_PUR_TRANS']][] = $doc['DOCID'];
        }

        return array(
            'invdocument' => $mappedDocs['I'],
            'invdocumententry' => $mappedDocs['I'],
            'sodocument' => $mappedDocs['S'],
            'sodocumententry' => $mappedDocs['S'],
            'podocument' => $mappedDocs['P'],
            'podocumententry' => $mappedDocs['P'],
            'porecurdocument' => $mappedDocs['P'],
            'sorecurdocument' => $mappedDocs['S'],
        );
    }

    /**
     * like @see CustomERPCatalog::GetSCMDocuments but for a single document parameters record
     *
     * @param array $docParamValues values array from DocumentParamsManager
     *
     * @return array  [ string => array[ doctype ], ... ]
     */
    static function getSCMDocumentMap($docParamValues)
    {
        $mappedDocs = [$docParamValues['DOCID']];

        switch ($docParamValues['SALE_PUR_TRANS']) {
        case 'Internal':
            return [
                'invdocument' => $mappedDocs,
                'invdocumententry' => $mappedDocs,
            ];

        case 'Purchase':
            return [
                'podocument' => $mappedDocs,
                'podocumententry' => $mappedDocs,
                'porecurdocument' => $mappedDocs,
            ];

        case 'Sale':
            return [
                'sodocument' => $mappedDocs,
                'sodocumententry' => $mappedDocs,
                'sorecurdocument' => $mappedDocs,
            ];
        }

        return [];
    }

    /**
     * Get metadata about a standard object.
     *
     * @param string $entity        The standard object type
     * @param string $doctype       If you are asking for metadata about a po, so, or icdocument, pass the doctype
     * @param bool   $showInactive  fetch data about inactive smart events, links, ... ?
     *
     * @return array
     */
    public function GetEntityData($entity, $doctype, $showInactive = false)
    {
        $catalogID = isl_strtoupper($entity) . ($doctype == '' ? '' : ' [' . $doctype . ']');
        $this->_GetEntityData($entity, $doctype, false, false, $showInactive);

        return $this->catalog[$catalogID];
    }

    /**
     * Returns the list of the fields for the entity specified by its name and document type
     * that can be used by the external systems to query data.
     *
     * @param string $entity
     * @param string $doctype
     * @param bool   $isPlatform
     * @param bool   $getRelated
     * @param bool   $showInactive
     *
     * @return array
     * @throws EntityException
     */
    public function getEntityAvailableFields(string $entity, $doctype = '', $isPlatform = false, $getRelated = false,
                                             $showInactive = false)
    {
        static $entityMap = [];
        $key = strtoupper($entity);
        if ($doctype != null) {
            $key .= " [$doctype]";
        }

        if (!isset($entityMap[$key])) {
            // Initializes the catalog
            $this->_GetEntityData(strtolower($entity), $doctype, $isPlatform, $getRelated, $showInactive);
            if (isset($this->catalog[$key])) {
                $entityMap[$key] = INTACCTarray_project($this->catalog[$key]['FieldList'], 'ID');
            } else {
                throw new EntityException("Requested entity ($entity) is not supported.");
            }
        }

        return $entityMap[$key];
    }

    /**
     * populate $this->catalog[...] for the specified entity/doctype
     *
     * @param string      $entity
     * @param string|null $doctype
     * @param bool        $isPlatform
     * @param bool        $getRelated    fetch data about objects related through the entity's ['nexus'] entry
     * @param bool        $showInactive  fetch data about inactive smart events, links, ... ?
     *
     * @return array|bool|null
     * @throws Exception
     */
    private function _GetEntityData($entity, $doctype, $isPlatform, $getRelated,
        /** @noinspection PhpUnusedParameterInspection */ $showInactive)
    {
        // If the entity has already been catalogued, return
        $catalogID = isl_strtoupper($entity) . ($doctype == '' ? '' : ' [' . $doctype . ']');
        if ( $this->mappedEntities && is_array($this->mappedEntities) &&
             array_key_exists($catalogID, $this->mappedEntities) ) {
            return $this->catalog[$catalogID];
        }

        if ( ! FeatureConfigManagerFactory::getInstance()->isFeatureEnabled("ENABLE_FA")) {
            if ($entity == 'fixedasset') {
                return null;
            }
        }

        if ( ! FeatureConfigManagerFactory::getInstance()->isFeatureEnabled("ENABLE_WIP_REPORT")) {
            if ($entity == 'wipperiod') {
                return null;
            }
        }

        if ( ! FeatureConfigManagerFactory::getInstance()->isFeatureEnabled("ENABLE_CRE_TASKTYPE")) {
            if ($entity == 'tasktype') {
                return null;
            }
        }

        if ( !VendorComplianceSetupManager::isVendorComplianceEnabled() ) {
            $entitiesToSkip = [
                'compliancetype',
                'compliancedefinition',
                'compliancedefassociations',
                'compliancerecord',
                'compliancerecorddetail',
            ];
            if ( in_array($entity, $entitiesToSkip) ) {
                return null;
            }
        }

        $isspecial = isset($this->specialEnts[$entity]);
        //eppp_p($entity);
        //eppp_p($isspecial);

        if ($isspecial && $this->specialEnts[$entity]['process'] == 'full') {
            $func = "GetEntityData_$entity";
            $this->$func($entity, $doctype);

            return null;
        }

        if ($isspecial && $this->specialEnts[$entity]['process'] == 'partial') {
            $func = "GetEntityData_$entity";
            $em = $this->$func($entity, $doctype);
        } else {
            // Make up for the manager factory assuming that any PT_ prefix is coming from a Nexus database.
            // These aren't.
            $entityName = $isPlatform ? "PT_$entity" : $entity;
            // Instantiate the entity manager and get a handle
            $em = $this->MF->getManager($entityName, true, array( 'DOCTYPE' => $doctype));
        }

        $thisSchema = $em->_schemas[$entity];
        if ($thisSchema === null) {
            $thisSchema = INTACCTarray_get($em->_schemas, $entity);
        }

        $thisobj = array();

        // ID
        $thisobj['ID'] = isl_strtoupper($entity);

        // SHOW ID
        $thisobj['SHOWID'] = $catalogID;

        // DOCUMENT TYPE
        $thisobj['DOCTYPE'] = $doctype;

        $thisobj['Name'] = $thisSchema['printas'] ?? isl_strtoupper($entity);

        ////////////////////////////////////////////////////////////////////////////////
        // Build the Fields list
        ////////////////////////////////////////////////////////////////////////////////

        $index = ( $thisSchema && array_key_exists('publish', $thisSchema) ) ? 'publish' : 'object';
        $schema = $thisSchema[$index] ?? null;
        if ( $schema && is_array($schema) ) {
            foreach ( $schema as $object ) {
                // skip the fields with complex path
                $ct = explode(".", $object);
                if ( count($ct) > 1 ) {
                    continue;
                }

                $thisobj['FieldList'][$object] = $this->_GetObjectData($object, $em);
            }
        }

        //  Get any runtime not included in the fieldinfo list.
        $runtimeFields = $em->getRuntimeFields();
        if ( $runtimeFields && is_array($runtimeFields) ) {
            foreach ( $runtimeFields as $fieldName => &$fieldInfo ) {
                $thisobj['FieldList'][$fieldName] = $this->_ExtractFieldInfo($em, $fieldName, $fieldInfo, false);
            }
        }

        ////////////////////////////////////////////////////////////////////////////////
        // Build the Smartlinks list
        ////////////////////////////////////////////////////////////////////////////////

        if (!$em->LoadSmartlinks(
            array(
                CUSTOMERP_SMARTLINKCLICK,
                CUSTOMERP_SMARTLINKWORKFLOW,
                CUSTOMERP_SMARTLINKFETCH,
                CUSTOMERP_SMARTLINKVALIDATE,
                CUSTOMERP_SMARTLINKCALCULATE,
            ), $showInactive
        )) {
            // No smartlink. Write the object into the catalog and return
            $thisobj['Nexus'] = $thisSchema['nexus'];
            $this->catalog[$thisobj['SHOWID']] = $thisobj;
            $this->mappedEntities[] = ($doctype == '') ? $entity : $entity . "__" . $doctype;

            return true;
        }

        $smartLinks = $em->smartLinks ?? null;
        if ( $smartLinks && is_array($smartLinks) ) {
            foreach ( $smartLinks as $smartLink ) {
                $thisobj['SmartLinks'][$smartLink->_smartlinkid] = $this->_GetSmartLinkData($smartLink);
            }
        }

        ////////////////////////////////////////////////////////////////////////////////
        // Add the supported features
        ////////////////////////////////////////////////////////////////////////////////

        $thisobj['SupportedFeatures'] = $thisSchema['customerp'];

        ////////////////////////////////////////////////////////////////////////////////
        // Get the Nexus
        ////////////////////////////////////////////////////////////////////////////////

        $this->mappedEntities[] = ($doctype == '') ? $entity : $entity . "__" . $doctype;

        $thisobj['Nexus'] = $thisSchema['nexus'];
        $this->catalog[$thisobj['SHOWID']] = $thisobj;

        if ( $getRelated == true ) {
            $nexus = $thisobj['Nexus'] ?? null;
            if ( $nexus && is_array($nexus) ) {
                foreach ( $nexus as $nexusObj ) {
                    if ( in_array($nexusObj['object'], $this->mappedEntities) ) {
                        continue;
                    }

                    // The object is considered a platform if the object definition for that object can
                    // be created by the Pt_DataObjectDefManager with the platform field set to TRUE
                    $isPt = ( $objDef = Pt_DataObjectDefManager::getByName($nexusObj['object']) ) && $objDef->isPlatform();

                    $this->_GetEntityData($nexusObj['object'], '', $isPt, true, false);
                }
            }
        }

        return true;
    }

    /**
     * @obsolete ? (no uses found)
     *
     * @param string $entity
     *
     * @return EntityManager
     * @throws Exception
     */
    function GetEntityData_companypref($entity)
    {
        $em = $this->MF->getManager($entity);

        $obj = $em->get(GetMyCompany());
        //eppp_p($obj);

        $pulishfields = array_keys($obj);
        //eppp_p($pulishfields);
        //dieFL();

        $thisSchema = &$em->_schemas[$entity];
        $thisSchema['publish'] = $pulishfields;

        return $em;
    }

    /**
     * @param string        $object
     * @param EntityManager $em
     *
     * @return array
     */
    function _GetObjectData($object, &$em)
    {
        // is this a custom field?
        $isCustom = false;
        if (array_key_exists($object, $em->customFields)) {
            $isCustom = true;
        }

        // Get the fieldinfo
        $thisSchema = &$em->_schemas[$em->_entity];
        $fieldinfo = false;
        foreach ($thisSchema['fieldinfo'] as $field) {
            if ($field['path'] == $object) {
                $fieldinfo = $field;
                break;
            }
        }

        // If it's a customfield, get the fieldinfo from the manager
        if ($isCustom) {
            /** @var CustomField $obj */
            $obj = $em->customFields[$object];
            $obj->MergeFieldInfo();
            $fieldinfo = $obj->GetFieldInfo();
        }

        $objectData = $this->_ExtractFieldInfo($em, $object, $fieldinfo, $isCustom);
        if ($isCustom) {
            $objectData['RECORDNO'] = $em->customFields[$object]->recordkey;
        }

        return $objectData;
    }

    /**
     * @param EntityManager $em
     * @param string        $fieldName
     * @param array         $fieldinfo
     * @param bool          $isCustom
     *
     * @return array
     */
    private function _ExtractFieldInfo($em, $fieldName, $fieldinfo, $isCustom)
    {
        $objectData = array('ID' => $fieldName, 'IsCustom' => $isCustom);

        // Set the label
        $objectData['Label'] = $fieldinfo['fullname'];

        // Set the Description
        $objectData['Description'] = $fieldinfo['desc'];

        // Set the datatype
        if ($isCustom) {
            $objectData['Datatype'] = $em->customFields[$fieldName]->type;
        } else {
            $objectData['Datatype'] = $fieldinfo['type']['ptype'];
        }

        // Is it hidden?
        $objectData['Hidden'] = ($fieldinfo['hidden']) ? 'true' : 'false';

        // Is it required?
        $objectData['Required'] = ($fieldinfo['required']) ? 'true' : 'false';

        // if it's a custom field, add the package name and key
        if ($isCustom) {
            $objectData['PackageKey'] = $em->customFields[$fieldName]->packagekey;
            $objectData['PackageName'] = $this->packageMap[$em->customFields[$fieldName]->packagekey];
        }

        return $objectData;
    }

    /**
     * @param SmartLink $SmartLink
     *
     * @return array
     */
    function _GetSmartLinkData(&$SmartLink)
    {
        $smartLinkData = array(
            'RECORDNO' => $SmartLink->_recordkey,
            'ID' => $SmartLink->_smartlinkid,
            'Label' => $SmartLink->_label,
            'Type' => $SmartLink->_type,
            'PackageKey' => $SmartLink->_packagekey,
            'PackageName' => $this->packageMap[$SmartLink->_packagekey],
            'Status' => $SmartLink->_active,
        );
        $smartLinkEvents = $SmartLink->_events ?? null;
        if ( $smartLinkEvents && is_array($smartLinkEvents) && count($smartLinkEvents) > 0 ) {
            $smartLinkData['Events'] = implode(", ", $smartLinkEvents);
        }
        if ($SmartLink->_type == CUSTOMERP_SMARTLINKVALIDATE) {
            /** @var SmartlinkValidate $SmartLink */
            $smartLinkData['Condition'] = $SmartLink->getCondition();
            $smartLinkData['ErrorType'] = $SmartLink->getErrorType();
        }

        if ($SmartLink->_type == CUSTOMERP_SMARTLINKWORKFLOW) {
            /** @var SmartlinkWorkflow $SmartLink */
            $smartLinkData['Condition'] = $SmartLink->getCondition();
            $smartLinkData['Action'] = $SmartLink->getActionType();
        }

        return $smartLinkData;
    }

    /**
     * @throws Exception
     */
    function _BuildPackageMap()
    {
        $emPkg = $this->MF->getManager('package');
        $list = $emPkg->GetList(array('selects' => array('RECORDNO', 'NAME')));

        foreach ($list as $package) {
            $this->packageMap[$package['RECORDNO']] = $package['NAME'];
        }
    }

    /**
     * @param string $conqueror
     * @param string $conquered
     */
    function _AnnexObject($conqueror, $conquered)
    {
        if (!array_key_exists($conqueror, $this->catalog)) {
            return;
        }
        if (!array_key_exists($conquered, $this->catalog)) {
            return;
        }

        // fields
        $this->catalog[$conqueror]['FieldList'] =
            INTACCTarray_merge($this->catalog[$conqueror]['FieldList'], $this->catalog[$conquered]['FieldList']);

        // Smartlinks
        $this->catalog[$conqueror]['SmartLinks'] =
            INTACCTarray_merge($this->catalog[$conqueror]['SmartLinks'], $this->catalog[$conquered]['SmartLinks']);

        // annihilate the conquered!
        unset($this->catalog[$conquered]);
    }
}