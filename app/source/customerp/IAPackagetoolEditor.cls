<?php
//=============================================================================
//
//	FILE:			IAPackagetoolEditor.cls
//	AUTHOR:			<PERSON>
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================
class IAPackagetoolEditor extends Editor
{
    /**
     * Turn off the save and new button
     */
    function ShowNew()
    {
        $this->_params['buttons']['dobutton'] = 'Import';
        unset($this->_params['buttons']['deliverbutton']);
        unset($this->_params['buttons']['saveandnewbutton']);
        $this->ShowTop();
    }

    /**
     * The value "multipart/form-data" has to be used in combination with the INPUT element, type="file"
     */
    function ShowExtraFormProps()
    {
        echo('ENCTYPE="multipart/form-data"');
    }

    /**
     * Create the input to upload the xml file
     * The input has to be with the hidden variable else the file won't be submitted
     */
    function ShowHiddenFields()
    {
        include_once 'process_status.inc';
        Editor::ShowHiddenFields();
        PrintStatusBarHiddenVarInit();
        if ($this->state == 'shownew') {
            ?>
            <input type="hidden" name="MAX_FILE_SIZE" value="120000">
            <br/>
            <table border=0 width=100% class="field_list_data">
                <tr>
                    <td class="label_cell"><font class="form_required">* Customization Package Location </font></td>
                    <td class="value_cell"><INPUT type="file" name="MYFILEPATH" size="50" maxlength="256"></td>
                </tr>
            </table>
            <?
        }
    }

    /**
     * @param array $_field
     *
     * @throws Exception
     */
    function ShowSimpleFieldValue(&$_field)
    {
        if ($_field['path'] == 'INDUSTRYCODE') {
            $indMgr = Globals::$g->gManagerFactory->getManager('industry');
            $industryMap = $indMgr->GetIndustryMap();
            $_field['type']['validvalues'] = array_keys($industryMap);
            $_field['type']['validlabels'] = array_values($industryMap);
        }
        parent::ShowSimpleFieldValue($_field);
    }
}
