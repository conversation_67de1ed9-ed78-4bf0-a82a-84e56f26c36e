<?php
//=============================================================================
//
//	FILE:			IAPackageLister.cls
//	AUTHOR:			<PERSON>
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

class IAPackageLister extends NLister
{
    /** @var array $additionalTokens */
    public $additionalTokens = [
        'IA.INSTALL',
        'IA.PROBLEM_INSTALLING_PACKAGE',
        'IA.PACKAGE_INSTALLED',
        'IA.PACKAGE_UPDATED',
        'IA.PACKAGE_IS_BEING_INSTALLED',
    ];
    
    function __construct()
    {
        parent::__construct(
            array(
                'entity' => 'iapackage',
                'title' => 'IA.CUSTOMIZATION_PACKAGE',
                'fields' => array('NAME', 'DESCRIPTION', 'AUTHOR', 'INTACCTID', 'SIGNED'),
            )
        );
    }

    /**
     * @return array
     */
    function BuildQuerySpec()
    {
        // Filter packages by industry type
        $querySpec = parent::BuildQuerySpec();
        $industry = GetMyIndustryType();
        $industry = $industry != '' ? $industry : 'ALL';
        $querySpec['filters'][0][] = array('INDUSTRYCODE', 'IN', array($industry, 'ALL'));

        return $querySpec;
    }

    /**
     *
     */
    function BuildTable()
    {
        parent::BuildTable();
        $table = &$this->table;

        $sess = Session::getKey();

        // We will add an install button for each reports. We will install the report via Ajax call
        for ($i = 0; $i < count($table); $i++) {
            $table[$i]["INSTALL"] = "<a href='javascript:InstallIAPackage(\"" . $table[$i]['RECORDNO'] . "\", \"$sess\")'>" . GT($this->textMap, 'IA.INSTALL') . "</a>";
        }
    }

    /**
     * Edit will be replaced by Install. We can not edit a system package.
     * @param int $i
     *
     * @return string
     */
    function calcEditUrl($i)
    {
        $auth = CheckAuthorization(GetOperationId('cerp/setup/package/create'));

        return $auth ? $this->table[$i]['INSTALL'] : ' ';
    }

    /**
     * @return string
     */
    function genJSIncludes()
    {
        $jsIncStr = parent::genJSIncludes();
        ?>
        <script>
            var textMap = <?=json_encode($this->textMap)?>;
        </script>
        <?php
        $jsIncStr .= "<jslib>../resources/qx/js/i18n.js</jslib>";
        $jsIncStr .= "<jslib>../resources/js/custompackage.js</jslib>";

        return $jsIncStr;
    }
}
