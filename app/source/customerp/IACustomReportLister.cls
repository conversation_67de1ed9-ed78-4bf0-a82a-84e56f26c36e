<?php
/**
 * IACustomReportLister
 *
 * <AUTHOR> <Kiran.Sarabu<PERSON><EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Lister class for IACustomReportLister
 */
class IACustomReportLister extends NLister
{

    public function __construct()
    {
        $additionalTokens = [
            ['id' => 'IA.CUSTOM_REPORT_LIBRARY'],
            ['id' => 'IA.NAME'],
            ['id' => 'IA.DATA_TYPE'],
            ['id' => 'IA.REPORT_TYPE'],
            ['id' => 'IA.INSTALL'],
            ['id' => 'IA.RUN']
        ];
        I18N::addTokens($additionalTokens);
        I18N::getText();
        parent::__construct(
            [
                'entity'         => 'iacustomreport',
                'title'          => 'IA.CUSTOM_REPORT_LIBRARY',
                'fields'         => [ 'NAME', 'ROOT', 'TYPE' ],
                'fieldlabels'    => [ I18N::getSingleToken('IA.NAME'),
                                      I18N::getSingleToken('IA.DATA_TYPE'),
                                      I18N::getSingleToken('IA.REPORT_TYPE') ],
                'entitynostatus' => 1,
            ]
        );
    }

    /**
     * BuildTable
     * $this->table this structure is being modified
     */
    public function BuildTable()
    {
        parent::BuildTable();
        $table = &$this->table;
        $flds = $this->_params['_fields'];
        $fldnames = $this->_params['_fieldlabels'];

        $sess = Session::getKey();

        $cr_op = GetOperationId('co/reports/custom');

        // We will add an install button for each reports. We will install the report via Ajax call
        for ( $i = 0; $i < count($table); $i++ ) {
            $table[$i]["INSTALL"] =
                "<a href='#' onclick='InstallIACustomReport(\"" . $table[$i]['NAME'] . "\", \"$sess\")'>" . I18N::getSingleToken('IA.INSTALL') . "</a>";

            $runlink = $this->MakeRunLink($table[$i]['NAME'], $cr_op);
            $this->table[$i]['RUN'] = $runlink;
        }

        $flds[] = 'RUN';
        $fldnames[] = '';

        $this->SetOutputFields($flds, $fldnames);
    }

    /**
     * @return array
     */
    function BuildQuerySpec()
    {
        // Filter reports by industry type
        $querySpec = parent::BuildQuerySpec();
        $industry = GetMyIndustryType();
        $industry = $industry != '' ? $industry : 'ALL';
        $querySpec['filters'][0][] = [ 'INDUSTRYCODE', 'IN', [ $industry, 'ALL' ] ];

        return $querySpec;
    }

    /**
     * Make Run Link
     *
     * @param string $crname input values
     * @param int    $op     op value
     *
     * @return string
     */
    public function MakeRunLink($crname, $op)
    {
        $_sess = Session::getKey();
        $runfunc = "var url = \"runcustomreport.phtml?.sess=$_sess&.op=$op"
                   . "&.cr=" . urlencode(URLCleanParams::insert('.cr', $crname))
                   . "\";baseModalDialog(url, \"\", window, 0, 350, 110, false);return false;";

        return "<a href='' onclick='javascript:$runfunc'>" . I18N::getSingleToken('IA.RUN') . "</a>";
    }

    /**
     * overrding the base function from NLister.cls
     *
     * @param string $i Loop Counter
     *
     * @return string Install link
     */
    public function calcEditUrl($i)
    {
        $auth = CheckAuthorization(GetOperationId('cerp/lists/customreport/create'));

        return $auth ? $this->table[$i]['INSTALL'] : ' ';
    }

    /**
     * Get the Js Includes
     *
     * @return string Js information
     */
    public function genJSIncludes()
    {
        $jsIncStr = parent::genJSIncludes();
        $jsIncStr .= "<jslib>../resources/js/customreport.js</jslib>";

        return $jsIncStr;
    }
}
