<?php

/**
 * Field Lookup Wizard
 */
class FieldLookupEditor extends CustomReportEditor
{
    /** @var string[] $additionalTokens */
    protected $additionalTokens = [
        'IA.OK',
        'IA.CANCEL',
        'IA.GET_MERGE_FIELDS',
    ];
    /**
     * @param array $params
     */
    function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * Include new buttons for display
     *
     * @param array $_params
     *
     * @return array
     */
    function Buttons_Instantiate($_params)
    {
        $_params = parent::Buttons_Instantiate($_params);
        $_params["dobutton"] = GT($this->textMap, 'IA.OK');
        $_params["doaction"] = "updateParent";
        $_params["saveandnewbutton"] = null;
        $_params["saveandnewaction"] = null;
        $_params["saveandprintbutton"] = null;
        $_params["saveandprintaction"] = null;
        $_params["deliverbutton"] = null;
        $_params["deliveraction"] = null;
        $_params["runbutton"] = null;
        $_params["runaction"] = null;
        $_params["exportbutton"] = null;
        $_params["exportaction"] = null;
        $_params["cancelbutton"] = GT($this->textMap, 'IA.CANCEL');
        $_params["cancelaction"] = "cancel";
        return $_params;
    }

    /**
     * @param array  $_params
     * @param string $disable
     */
    function ShowDoButtons($_params, $disable = "")
    {
        $dobutton = $_params['dobutton'];
        $doaction = $_params['doaction'];
        $disabled = $disable == 1 ? "disabled=true" : "";
        if ($dobutton) { ?>
            <INPUT id="<?= Editor_SaveBtnID; ?>" type="button" name="savebutton" <?= $disabled; ?> class="nosavehistory <?=Pt_SetupComponents::$emptyOrBtnPrimaryClass?>"
                   value="<?= $dobutton; ?>" onclick="<? echo $doaction ?>()" disableonsubmit="true">
            <?
        }
    }

    /**
     * Get selected columns from request
     *
     * @param string $objectpath
     *
     * @return string[]
     */
    function GetColumns($objectpath = '')
    {
        return array();
    }

    /**
     * @access public
     */
    function ShowNavigationControlsBottom()
    {
    }

    /**
     * @access public
     */
    function ShowNavigationControlsTop()
    {
    }

    /**
     * @access public
     */
    function ShowPageInstruction()
    {
    }
}
