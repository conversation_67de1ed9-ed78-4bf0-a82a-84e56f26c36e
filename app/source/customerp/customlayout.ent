<?
    $kSchemas['customlayout'] = array (
        'object' => array (
           'RECORDN<PERSON>', 
           'CUSTOMLAYOUTID',
           'OWNEROBJECT',
        ),
        'schema' => array (
            'RECORDNO'    => 'record#',
            'CUSTOMLAYOUTID'    => 'customlayoutid',
            'OWNEROBJECT'    => 'ownerobject',
            'PACKAGEKEY'    => 'packagekey',
        ),
        'fieldinfo' => array (
            array (
                'path' => 'RECORDNO',
                'desc' => 'IA.RECORD_NUMBER',
                'fullname' => 'IA.RECORD_NUMBER',
                'hidden' => true,
                'type' => array (
                    'ptype' => 'sequence',
                    'type' => 'integer',
                    'maxlength' => 8,
                    'size' => 8,
                    'format' => $gRecordNoFormat,
                )
            ),
            array (
                'fullname' => 'IA.CUSTOM_LAYOUT_ID',
                'type' => array ( 
                    'ptype' => 'text', 
                    'type' => 'text', 
                    'maxlength' => 80, 
                    'size' => 20, 
                    'format' => '/^.{1,80}$/' 
                ),
                'required' => true,
                'desc' => 'IA.CUSTOM_LAYOUT_ID',
                'path' => 'CUSTOMLAYOUTID'
            ),
            array (
                'fullname' => 'IA.OWNER_OBJECT',
                'type' => array (
                    'ptype' => 'enum',
                    'type' => 'enum',
                    'validlabels' => array (
            //						'Company',
            //						'User',
            //						'Customer',
            //                        'Customer Type',
            //						'Vendor',
            //                        'Vendor Type',
            //						'GL Journal',
            //                        'Statistical journal',
            //						'GL Transaction',
            //						'GL Entry',
            //						'Recurring GL Transaction',
            //						'Recurring GL Entry',
            //						'Invoice',
            //						'Invoice Item',
            //						'AR Adjustment',
            //						'AR Adjustment Item',
            //						'Bill',
            //						'Bill Item',
            //                        'Employee',
            //						'Employee Expenses',
            //						'Employee Expenses Item',
            //					    'Employee Out of Office',
            //					    'Employee Positions and Skills',
            //						'Employee Rates',   
            //                        'Expense Type',
            //						'AP Adjustment',
            //						'AP Adjustment Item',
            //						'Department',
            //						'Class',
            //                        'Charge Card Transaction',
            //                        'Charge Card Transaction Detail',
            //                        'Charge Payoffs',
            //                        'Credit Card Charges and Other Fees',
            //                        'Credit Card Charges and Other Fees Entry', 
            //						'Location',
            //						'Inter Entity Relationships',
            //                        'Inter Entity Transactions',
            //						'Entity',
            //						'Territory',
            //						'Warehouse',
            //						'Item',
            //						'GL Account',
            //                        'Statistical Account',
            //                        'GL Account Group',
            //                        'SO Document',
            //                        'SO Document Entry',
            //					    'Out of Office',
            //					    'Positions and Skills',
            //						'Project',
            //						'Project Resources',
            //						'Project Status',
            //						'Project Type',
            //                        'PO Document',
            //                        'PO Document Entry',
            //						'Task',
            //                        'Task Resources',
            //                        'INV Document',
            //                        'INV Document Entry',
            //						'GL Account',
            //						'Stkit Document',
            //						'Stkit Document Entry',
            //                        'Timesheet',
            //						'Timesheet Entry',
            //						'GAAP Adjustment Journal',
            //						'Tax Adjustment Journal',
            //						'Unit Of Measure',
            //						'SO Price List',
            //						'SO Price List Entry',
            //						'PO Price List',
            //						'PO Price List Entry',
            //						'INV Price List',
            //						'INV Price List Entry',
            //                        'Other Receipts',
            //                        'Other Receipts Detail',
            //                        'Deposits',
            //                        'Bank Interest and Charges',
            //                        'Bank Interest and Charges Entry'
                    ),
                    'validvalues' => array (
                    //						'companypref',
                    //						'userinfo',
                    //						'customer',
                    //                        'custtype',
                    //						'vendor',
                    //                        'vendtype',
                    //						'journal',
                    //						'statjournal',
                    //						'glbatch',
                    //						'glentry',
                    //						'recurglbatch',
                    //						'recurglentry',
                    //						'arinvoice',
                    //						'arinvoiceitem',
                    //						'aradjustment',
                    //						'aradjustmentitem',
                    //						'apbill',
                    //						'apbillitem',
                    //                        'employee',
                    //						'eexpenses',
                    //						'eexpensesitem',
                    //					    'employeeoutofoffice',
                    //					    'employeepositionskill',
                    //					    'outofoffice',
                    //                        'employeerate',
                    //                        'eeaccountlabel',
                    //						'apadjustment',
                    //						'apadjustmentitem',
                    //						'department',
                    //						'class',
                    //                        'cctransaction',
                    //                        'cctransactionentry',
                    //                        'chargepayoff',
                    //                        'creditcardfee',
                    //                        'creditcardfeeentry',
                    //						'location',
                    //						'ierelation',
                    //                        'iettransactions',
                    //						'locationentity',
                    //						'territory',
                    //						'warehouse',
                    //						'item',
                    //						'glaccount',
                    //                        'stataccount',
                    //                        'glacctgrp',
                    //						'trecord',
                    //						'tentry',
                    //                        'sodocument',
                    //					    'outofoffice',
                    //					    'positionskill',
                    //						'project',
                    //						'projectresources',
                    //						'projectstatus',
                    //						'projecttype',
                    //                        'sodocumententry',
                    //                        'podocument',
                    //                        'podocumententry',
                    //						'task',
                    //                        'taskresources',
                    //                        'invdocument',
                    //                        'invdocumententry',
                    //						'stkitdocument',
                    //                        'stkitdocumententry',
                    //                        'timesheet',
                    //						'timesheetentry',
                    //						'gaapadjjrnl',
                    //						'taxadjjrnl',
                    //						'uom',
                    //						'sopricelist',
                    //						'sopricelistentry',
                    //						'popricelist',
                    //						'popricelistentry',
                    //						'invpricelist',
                    //						'invpricelistentry',
                    //						'vendoracctnoloc',
                    //                        'otherreceipts',
                    //                        'otherreceiptsentry',
                    //                        'deposit',
                    //                        'bankfee',
                    //                        'bankfeeentry'
                    ),
                ),
                'required' => true,
                'desc' => 'IA.OWNER_OBJECT',
                'path' => 'OWNEROBJECT'
            ),
        ),
        'table' => 'customlayout',
        'printas'    =>    'IA.LAYOUT',
        'pluralprintas' => 'IA.LAYOUTS',
        'vid' => 'CUSTOMLAYOUTID',
        'autoincrement' => 'RECORDNO',
        'module' => 'co'
    );
