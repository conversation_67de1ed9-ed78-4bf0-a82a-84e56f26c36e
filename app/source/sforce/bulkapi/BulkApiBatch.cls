<?php

/**
 * Class BulkApiBatch
 *
 * Used by the BulkApiProcessor to create batches for the salesforce bulk API calls.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2017 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class BulkApiBatch
{
    /** @var  string $intacctObjectName */
    private $intacctObjectName;

    /** @var  string $salesforceObjectName */
    private $salesforceObjectName;

    /** @var  string[][] $batchObjects */
    private $batchObjects;


    /**
     * @param string $intacctObjectName
     * @param string $salesforceObjectName
     */
    public function __construct($intacctObjectName, $salesforceObjectName)
    {
        $this->intacctObjectName = $intacctObjectName;
        $this->salesforceObjectName = $salesforceObjectName;
    }

    /**
     * @return string
     */
    public function getIntacctObjectName()
    {
        return $this->intacctObjectName;
    }

    /**
     * @param string $intacctObjectName
     */
    public function setIntacctObjectName($intacctObjectName)
    {
        $this->intacctObjectName = $intacctObjectName;
    }

    /**
     * @return string
     */
    public function getSalesforceObjectName()
    {
        return $this->salesforceObjectName;
    }

    /**
     * @param string $salesforceObjectName
     */
    public function setSalesforceObjectName($salesforceObjectName)
    {
        $this->salesforceObjectName = $salesforceObjectName;
    }

    /**
     * @return string[][]
     */
    public function getBatchObjects()
    {
        return $this->batchObjects;
    }

    /**
     * @param string[][] $batchObjects
     */
    public function setBatchObjects($batchObjects)
    {
        $this->batchObjects = $batchObjects;
    }


}