<?php
/**
 * Class IntacctManagedSalesDocument
 *
 * Implementation of the parent class for all managed transaction documents.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2018 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class IntacctManagedSalesDocument extends IntacctManagedUpdatableObject
{

    /**
     * IntacctManagedObject constructor.
     *
     * @param string          $intacctObject
     * @param SyncFilter|null $syncFilter
     * @param string|null     $documentType
     *
     * @throws BulkApiException
     */
    public function __construct($intacctObject, SyncFilter $syncFilter = null, $documentType = null)
    {
        $this->documentType = $documentType;
        parent::__construct($intacctObject, $syncFilter);
    }

    /**
     * @param string $intacctObject
     */
    protected function assignManager($intacctObject)
    {
        $this->manager =
            Globals::$g->gManagerFactory->getManager($intacctObject, false, [ 'DOCTYPE' => $this->documentType ]);
    }

    /**
     * Returns the name of the subscription object.
     *
     * @return string
     */
    public function getSubscriptionObject()
    {
        return $this->getDocumentSubscriptionObject();
    }

    /**
     * Subscription object name for the document of the specific type.
     *
     * @return string
     */
    protected function getDocumentSubscriptionObject()
    {
        // return 'SODOCUMENT_' . $this->getDocumentDiscriminator();
        return $this->checkSoDocV2Enabled() ? 'SODOCUMENT2' : 'SODOCUMENT';
    }

    /**
     * By default the key in the mapping array is the name of the intacct object. Can be owerwritten
     * by the child classes.
     *
     * @return string
     */
    public function getMappingKey()
    {
        return $this->checkSoDocV2Enabled() ? 'SODOCUMENTV2' : 'SODOCUMENT_IA_' . $this->getDocumentDiscriminator();
    }

    /**
     * By default the document class is the same as the document discriminator
     *
     * @return string
     * @throws IAException
     */
    public function getDocumentClass()
    {
        return strtoupper($this->getDocumentDiscriminator());
    }

    /**
     * Implementation of the method returning query parameters for querying sales
     * documents of the specific type from the Intacct database.
     *
     * @return array
     */
    public function getQueryParams()
    {
        $params = [
            'filters' => [ [ [ 'DOCPARCLASS', '=', strtoupper($this->getDocumentDiscriminator()) ] ] ],
            'usemst'  => true,
        ];

        return $params;
    }

    /**
     * @return CloseableIterator
     */
    public function createDatasetIterator() : CloseableIterator
    {
        $iterator = IntacctDocumentReader::getDocumentIterator($this);

        return $iterator;
    }

    /**
     * Returns the list of intacct sales documents stored in the Intacct database.
     *
     * @return array[]|false
     * @throws IAException
     */
    public function getStoredObjects()
    {
        $reader = IntacctDocumentReader::getReader($this);
        $storedDocuments = $reader::getStoredDocuments();

        return $storedDocuments;
    }

    /**
     * Returns the list of related objects that needs to be deleted for the
     * transaction document on update.
     *
     * @return string[]
     */
    public function getRelatedDeletableObjects()
    {
        return [
            IntacctManagedObject::SODOCUMENTENTRY => $this->getDocumentDiscriminator(),
            IntacctManagedObject::SODOCUMENTSUBTOTAL => $this->getDocumentDiscriminator()
        ];
    }

    /**
     * Returnes the list of batched objects corresponding to the salesforce Ids of the transaction documents.
     *
     * @param string[] $externalReferenceIds list of salesforce IDs references by the objects that
     *                                       will be deleted
     *
     * @return array
     * @throws BulkApiException
     * @throws IAException
     */
    public function getDeletableBatches($externalReferenceIds)
    {
        $ret = [];
        $relatedObjects = $this->getRelatedDeletableObjects();
        foreach ($relatedObjects as $document=>$discriminator) {

            $managedObject =
                IntacctManagedSalesDocumentFactory::getManagedObject($document, new SyncFilter(), $discriminator, null);

            $batch = [];
            $managedObject->setQueriedDatasetAdjustable(false);

            $managedObject->readSalesforceRecords();
            $salesforceRecords = $managedObject->getSalesforceRecords();
            foreach ($salesforceRecords as $record) {
                /** @noinspection PhpUndefinedMethodInspection */
                $referencedFieldId = $record->{$managedObject->getDocumentReferenceFieldName()};
                if (in_array($referencedFieldId, $externalReferenceIds)) {
                    if (count($batch) === BulkApiProcessor::BATCH_MAX_LIMIT) {
                        $ret[$managedObject->getMappingKey()][] = $batch;
                        $batch = [];
                    }
                    $batch[] = $record->Id;
                }
            }
            if (!empty($batch)) {
                $ret[$managedObject->getMappingKey()][] = $batch;
            }
        }

        return $ret;
    }

    /**
     * Returns the list of key fileds which will be used to store the queried
     * data from the salesforce in the map for easy access.
     *
     * By default, the key field is 'Name'. The specific managed object needs
     * to specify the list of key fields if the record queried from salesforce
     * is identified by field (or fields) other than Name.
     *
     * @return string[]
     */
    protected function getSforceKeyFields()
    {
        return $this->checkSoDocV2Enabled() ? [SforceNamespaceHandler::getPrefix() . 'Document_Number__c'] : parent::getSforceKeyFields();
    }

    /**
     * Two fields Id and Name are the default once queried from salesforce.
     *
     * @return array
     */
    protected function getDefaultSalesforceFields()
    {
        return $this->checkSoDocV2Enabled() ? ['Id', 'Name', SforceNamespaceHandler::getPrefix() . 'Document_Number__c'] : parent::getDefaultSalesforceFields();
    }

}