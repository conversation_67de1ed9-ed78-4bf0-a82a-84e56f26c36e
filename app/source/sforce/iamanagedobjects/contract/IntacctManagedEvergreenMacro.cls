<?php

/**
 * Class IntacctManagedEvergreenMacro
 *
 * Implementation of the IntacctManagedObject for Contract Evergreen Template
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2022 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class IntacctManagedEvergreenMacro extends IntacctManagedUpdatableObject
{
    /**
     * Returns the name of the subscription object.
     *
     * @return string
     */
    public function getSubscriptionObject()
    {
        return 'EVERGREENMACRO';
    }
}
