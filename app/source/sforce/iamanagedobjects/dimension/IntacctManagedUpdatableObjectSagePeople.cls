<?php

/**
 * Class IntacctManagedUpdatableObjectSagePeople
 *
 * Parent class for all managed objects that need to check for the matching
 * record in SagePeople before inserting a new record.
 *
 * <AUTHOR> M <<EMAIL>>
 * @copyright 2000-2020 Sage Intacct Inc
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Inc and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Sage Intacct Inc.
 */

abstract class IntacctManagedUpdatableObjectSagePeople extends IntacctManagedUpdatableObject
{
    /**
     * Uses the cached salesforce client to query the data from the SagePeople.
     *
     * @throws BulkApiException
     * @throws Exception
     * @throws IAException
     */
    public function readSalesforceRecords()
    {
        // cache the salesforce session id and the bulk api end point
        static $sforceClientCache = [];
        if (isset($sforceClientCache['sforce-session-id'])) {
            $sforceSessionId = $sforceClientCache['sforce-session-id'];
            $bulkApiEndPoint = $sforceClientCache['bulk-api-end-point'];
        } else {
            $soapClient = $this->getSforceSoapClient();

            $sforceSessionId = $soapClient->getSessionId();
            assert(!is_null($sforceSessionId));
            $sforceClientCache['sforce-session-id'] = $sforceSessionId;

            $bulkApiEndPoint = $soapClient->getLocation();
            assert(!is_null($bulkApiEndPoint));
            $sforceClientCache['bulk-api-end-point'] = $bulkApiEndPoint;
        }

        // Create an instance of the BulkApiProcessor for Query operation
        $processor = new BulkApiProcessor($bulkApiEndPoint, $sforceSessionId, BulkApiOperation::QUERY);

        /** @var string $salesforceObjectName */
        $salesforceObject = SagePeopleFieldMap::getSforceObject($this->getMappingKey());

        // Read the list of the default salesforce fields
        $sforceFields = $this->getDefaultSalesforceFields();

        // Merge the array of the default fields with the list of the optional once
        $sforceFields = array_merge($sforceFields, $this->getAdditionalSalesforceFields());

        $maxId = null;
        $allRecords = [];

        do {
            $queryFilters = $this->getSoqlQueryFilters();
            if ($maxId !== null) {
                $queryFilters[] = "Id > '$maxId'";
            }
            $queryBuilder = new BulkApiQueryBuilder(
                $salesforceObject, $sforceFields, $queryFilters, ['Id']);

            $queryBuilder->setLimit(BulkApiQueryBuilder::QUERY_RESULT_LIMIT);

            // Execute bulk api query using the query builder
            $records = $processor->query($queryBuilder);

            // Drop the field 'attributes' from the response
            foreach ($records as &$record) {
                unset($record->attributes);
            }

            // Keep collecting the returned records
            $allRecords = array_merge($allRecords, $records);

            $size = count($records);

            if (!isEmptyArray($records) && $size === $queryBuilder->getLimit()) {
                $maxId = $records[$size - 1]->Id;
            } else {
                $maxId = null;
            }

        } while ($maxId !== null);


        if ($this->queriedDatasetAdjustable) {
            // If marked as adjustable convert the data such as for each record the key
            // is a string representing record key and value is the queries object Id
            $this->salesforceRecords = $this->prepareSalesforceRecords($allRecords);
        } else {
            $this->salesforceRecords = $allRecords;
        }

    }

    /**
     * Returns lazy loaded Sage People client.
     *
     * @return null|SforceClient
     */
    protected function getSforceClient()
    {
        if (is_null($this->sforceClient)) {
            $client = SalesforceUtil::getDefaultSagePeopleClient();
            if ($client === null) {
                logStackTraceBrief();
                throw new IAException("Failed to create Sage People client.");
            }

            $this->sforceClient = $client;
        }
        return $this->sforceClient;
    }

    /**
     * Reads the mapping properties from the .INC file and assigns to the
     * proper fields of the managed object
     */
    protected function assignMappingProperties()
    {
        // Read and assign intacct Id field
        include 'SagePeopleObjectMap.inc';
        $mappingKey = $this->getMappingKey();

        /** @noinspection PhpUndefinedVariableInspection */
        if (!isArrayValueProvided($objSchemaMap, $mappingKey)) {
            throw new BulkApiException("Not Intacct managed object: " . $this->intacctObject,
                BulkApiErrorCodes::NOT_A_MANAGED_OBJECT);
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $this->intacctIdField = $objSchemaMap[$mappingKey]['INTACCTPRIMFIELD'];

        $fieldsMap = $objSchemaMap[$mappingKey]['fieldsMap'];

        $this->fieldMapping = INTACCTarray_list2map(
            $fieldsMap, 'PARTNERFIELD', 'INTACCTFIELD');

        $this->intacctFields = array_values($this->fieldMapping);

        // Read and assign the corresponding salesforce object
        $this->salesforceObject = $objSchemaMap[$mappingKey]['SFOBJECT'];

        if ($this->getParentField() !== null) {
            $this->adjustSforceParentField();
        }
    }

}