<?php

/**
 * Class IntacctManagedEmployeeTypeSagePeople
 *
 * Implementation of the IntacctManagedObject for Employee Type -- Sage People Integration.
 *
 * <AUTHOR> M <<EMAIL>>
 * @copyright 2000-2020 Sage Intacct Inc
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Inc and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Sage Intacct Inc.
 */
class IntacctManagedEmployeeTypeSagePeople extends IntacctManagedRecursiveObjectSagePeople
{
    /**
     * Returns the name of the subscription object.
     *
     * @return string
     */
    public function getSubscriptionObject()
    {
        return 'EMPLOYEETYPE';
    }

    /**
     * Field pointing to the parent reference.
     *
     * @return string
     */
    public function getIntacctParentReferenceField()
    {
        return 'NAME';
    }

    /**
     * Returns the field name referencing the parent object. Applicable to
     * recursive objects only.
     *
     * @return string
     */
    public function getParentField()
    {
        return 'PARENT.NAME';
    }

}