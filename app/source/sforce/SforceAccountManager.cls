<?
require_once 'ims_publish_1.cls';

class SforceAccountManager extends EntityManager
{
    /**
     * @param array $params
     *
     * @return int
     */
    function GetCount($params)
    {
        return 50;
    }

    /**
     * @param array $params
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return bool
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        global $gErr;
        $pub = new ims_publish_1();
        if (!$pub->PublishMsg('SforceAccountManager', 'SFORCE', 'GETLIST_CUSTOMER', '', '', [], $response)) {
            $gErr->addError('SFDC-1250', __FILE__ . '.' . __LINE__, 'Failed to publish topic GETLIST_CUSTOMER');
            epp("gerr: "); eppp($gErr->errors);
            return false;
        }
        //		epp("response:"); eppp($response);
        return $response;
    }
}
