<?php

/**
 * class IntacctSforceSyncProcessorBiDirectional to sync the Intacct object from Intacct to Salesforce
 * when bi-directional support available for an objact
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class IntacctSforceSyncProcessorSFContract extends IntacctSforceSyncProcessorBiDirectional
{

    /* @var  bool $onlyCustomFlds For bi-direction sync, standard fields not required to push to Salesforce*/
    private $postContractBalanceOnly = false;

    /**
     * @param bool $val
     */
    public function setPostContractBalanceOnly($val)
    {
        $this->postContractBalanceOnly = $val;
    }

    /**
     * @return bool
     */
    public function getPostContractBalanceOnly()
    {
        return $this->postContractBalanceOnly;
    }

    /**
     * @param string[][]  $args
     * @param string      $action
     * @param string|bool $externalId
     *
     * @return bool|string[]
     */
    protected function prepareSingleObjectForSforcePost(&$args, &$action, &$externalId)
    {
        if ( !$this->getPostContractBalanceOnly() ) {
            $postData = parent::prepareSingleObjectForSforcePost($args, $action, $externalId);
            if ( $postData === false ) {
                return false;
            }
        }
        // $postData = true when there is no bi-directional mapping set
        // $postData is not set if it comes from create contract invoice/invoice payment/addition of contract lines
        if ( !isset($postData) || $postData === true) {
            $postData = $this->reBuildParams($args, $action, $externalId);
            if ( $postData === true ) {
                return true;
            }
        }

        if ( $externalId ) {
            $postData = $this->prepareContractBalance($args, $postData);
        }

        return $postData;
    }

    /**
     * @param string[][]  $args
     * @param string      $action
     * @param string|bool $externalId
     *
     * @return bool|string[]
     */
    private function reBuildParams($args, &$action, &$externalId)
    {
        $postData = [];
        $externalId = $this->getExternalId($args);
        if ( $externalId ) {
            $action = 'update';
            $this->setSubscriptionRequired(false);
            $postData = $this->salesforceSystemFlds($postData);
        } else {
            $this->logInfo(
                'No need to sync back to '. $this->objSchemaMap['SFOBJECT'] .', as there is no subscription link.'
            );
            return true;
        }
        return $postData;
    }

    /**
     * @param string[][] $args
     * @param string[]   $postData
     *
     * @return bool|string[]
     */
    private function prepareContractBalance($args, $postData)
    {
        $billingSchEntryMgr = Globals::$g->gManagerFactory->getManager('contractbillingscheduleentry');
        $paymentSchEntryMgr = Globals::$g->gManagerFactory->getManager('contractpaymentscheduleentry');
        $sfContractArgs = [];
        //$onlyInProgress is false when Evergreen is not enabled,
        // this code has to be changed when Evergreen support is added from SFDC sync
        $onlyInProgress = false;
        $cdStateFilter = $onlyInProgress ?
            ['contractdetail.state', '=', ContractDetailState::STATE_INPROGRESS_CODE] :
            ['contractdetail.state', '!=', ContractDetailState::STATE_RENEWAL_FORECAST_CODE];

        $filters = [['CONTRACTKEY', '=', $args['RECORDNO']], ['STATE', '!=', 'Terminated'], $cdStateFilter];
        $resTotal = $billingSchEntryMgr->GetList(
            [
                'selects' => [['fields' => ['AMOUNT'], 'function' => 'sum(${1})']],
                'columnaliases' => array('AMOUNT'),
                'filters' => [$filters],
            ]
        );

        $filters = [['CONTRACTKEY', '=', $args['RECORDNO']], ['POSTED', '=', 'true'], $cdStateFilter];
        $resBilled = $billingSchEntryMgr->GetList(
            [
                'selects' => [['fields' => ['AMOUNT'], 'function' => 'sum(${1})']],
                'columnaliases' => array('AMOUNT'),
                'filters' => [$filters],
            ]
        );
        $resPaid = $paymentSchEntryMgr->GetList(
            [
                'selects' => [['fields' => ['AMOUNT'], 'function' => 'sum(${1})']],
                'columnaliases' => array('AMOUNT'),
                'filters' => [$filters],
            ]
        );

        if (isset($resTotal[0]['AMOUNT'])) {
            $sfContractArgs['SUMMARYTOTALFIELD'] = $resTotal[0]['AMOUNT'];

            if (isset($resBilled[0]['AMOUNT'])) {
                $amountBilled = $resBilled[0]['AMOUNT'];
                $sfContractArgs['SUMMARYBILLEDFIELD'] = $amountBilled;
                $amountReceived = $resPaid[0]['AMOUNT'] ?? 0.0;
                $amountOutstanding = ibcsub($amountBilled, $amountReceived);
                $sfContractArgs['SUMMARYRECEIVED'] = $amountReceived;
                $sfContractArgs['SUMMARYOUTSTANDING'] = $amountOutstanding;
            } else {
                $sfContractArgs['SUMMARYBILLEDFIELD'] = 0.0;
                $sfContractArgs['SUMMARYRECEIVED'] = 0.0;
                $sfContractArgs['SUMMARYOUTSTANDING'] = 0.0;
            }
        }
        
        if ( !$this->getPostContractBalanceOnly() ) {
            $contractExternalId = $this->subMgr->GetExternalId($args['CONTRACTID'], 'CONTRACT',
                SforceNamespaceHandler::SUBSCRIBER_NAME);
            $postData[SforceNamespaceHandler::getPrefix() . 'Intacct_Contract__c'] = $contractExternalId;
        }
        $postData[SforceNamespaceHandler::getPrefix() . 'Contract_Total__c'] = $sfContractArgs['SUMMARYTOTALFIELD'];
        $postData[SforceNamespaceHandler::getPrefix() . 'Total_Outstanding__c'] = $sfContractArgs['SUMMARYOUTSTANDING'];
        $postData[SforceNamespaceHandler::getPrefix() . 'Total_Billed__c'] = $sfContractArgs['SUMMARYBILLEDFIELD'];
        $postData[SforceNamespaceHandler::getPrefix() . 'Total_Received__c'] = $sfContractArgs['SUMMARYRECEIVED'];
        return $postData;
    }

}