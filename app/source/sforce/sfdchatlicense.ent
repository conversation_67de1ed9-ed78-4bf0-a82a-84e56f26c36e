<?php

//
// FILE:            sfdchatlicense.ent
// AUTHOR:          DWilks
// DESCRIPTION:     SFDChatter License Entity
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//

require_once 'globals.ent';
global $gRecordNoFormat, $gRecordNoNewFormat, $gDateType;

$kSchemas['sfdchatlicense'] = array (
    'object' => [
        'RECORDNO',
        'CNY',
        'SFDC_ORG_ID',
        'OWNORG',
        'ORGTYPE',
        'STATUS',
        'SA_ACCEPT_DATE',
        'INITIAL_ORDER_DATE',
        'CANCEL_ORDER_DATE',
        'NUM_USERS',
        'NUM_ADMINS',
    ],

    'schema' => [
        'RECORDNO' => 'record#',
        'CNY' => 'cny#',
        'SFDC_ORG_ID' => 'sfdc_org_id',
        'OWNORG' => 'ownorg',
        'ORGTYPE' => 'orgtype',
        'STATUS' => 'status',
        'SA_ACCEPT_DATE' => 'sa_accept_date',
        'INITIAL_ORDER_DATE' => 'initial_order_date',
        'CANCEL_ORDER_DATE' => 'cancel_order_date',
        'NUM_USERS' => 'num_users',
        'NUM_ADMINS' => 'num_admins',
    ],

    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'desc' => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden' => true,
            'type' => [
                'ptype' => 'sequence',
                'type' => 'integer',
                'maxlength' => 8,
                'size' => 8,
                'format' => $gRecordNoFormat,
            ],
            'id' => 1,
        ],
        [
            'path' => 'CNY',
            'desc' => 'IA.COMPANY_ID',
            'fullname' => 'IA.COMPANY_ID',
            'hidden' => true,
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 15,
                'size' => 15,
                'format' => $gRecordNoNewFormat,
            ],
            'id' => 2,
        ],
        [
            'path' => 'SFDC_ORG_ID',
            'fullname' => 'IA.SFDC_ORG_ID',
            'desc' => 'IA.ID_OF_SALESFORCE_ORG',
            'type' => [ 'ptype' => 'text', 'type' => 'text', 'maxlength' => 30 ],
            'id' => 3,
        ],
        [
            'path' => 'OWNORG',
            'fullname' => 'IA.OWN_ORG',
            'desc' => 'IA.ORG_IS_OWNED_BY_CUSTOMER_OR_INTACCT',
            'type' => [ 'ptype' => 'text', 'type' => 'text', 'maxlength' => 1 ],
            'id' => 4,
        ],
        [
            'path' => 'ORGTYPE',
            'fullname' => 'IA.ORG_TYPE',
            'desc' => 'IA.SALESFORCE_ORGANIZATION_TYPE',
            'type' => [ 'ptype' => 'text', 'type' => 'text', 'maxlength' => 1 ],
            'id' => 5,
        ],
        [
            'path' => 'STATUS',
            'fullname' => 'IA.STATUS',
            'desc' => 'IA.STATUS',
            'type' => [ 'ptype' => 'text', 'type' => 'text', 'maxlength' => 1 ],
            'id' => 6,
        ],
        [
            'path' => 'SA_ACCEPT_DATE',
            'fullname' => 'IA.SUBSCRIBE_DATE',
            'desc' => 'IA.DATE_SUBSCRIBED_TO_COLLABORATE',
            'type' => $gDateType,
            'id' => 7,
        ],
        [
            'path' => 'INITIAL_ORDER_DATE',
            'fullname' => 'IA.INITIAL_ORDER_ANNIVERSARY_DATE',
            'desc' => 'IA.DATE_OF_INITIAL_SFDC_ORDER',
            'type' => $gDateType,
            'id' => 8,
        ],
        [
            'path' => 'CANCEL_ORDER_DATE',
            'fullname' => 'IA.FINAL_CANCELLATION_ORDER_DATE',
            'desc' => 'IA.DATE_OF_FINAL_SFDC_CANCELATION_ORDER',
            'type' => $gDateType,
            'id' => 9,
        ],
        [
            'path' => 'NUM_USERS',
            'fullname' => 'IA.PLATFORM_USER_LICENSES_ORDERED',
            'desc' => 'IA.TOTAL_NUMBER_OF_PLATFORM_USER_LICENSES_ORDERED',
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 6,
                'size' => 6,
            ),
            'id' => 10,
        ],
        [
            'path' => 'NUM_ADMINS',
            'fullname' => 'IA.ADMINISTRATOR_LICENSES_ORDERED',
            'desc' => 'IA.TOTAL_NUMBER_OF_ADMINISTRATOR_LICENSES_ORDERED',
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 4,
                'size' => 4,
            ),
            'id' => 11,
        ],
    ],

    'table' => 'sfdchat_license',
    'printas'=>'IA.SFDC_CHATTER_LICENSES',
    'pluralprintas'=>'IA.SFDC_CHATTER_LICENSES',
    //     'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'module' => 'co',
    'nosysview' => true,
    
    'globalschema' => true,
    'global' => true,
);
