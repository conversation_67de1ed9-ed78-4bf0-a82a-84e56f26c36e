<?php

/**
 * Class SforceSyncProcessorCustomer
 * Class to sync account from Salesforce to Intacct customer
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

require_once 'SforceConstants.inc';

class SforceSyncProcessorCustomer extends SforceSyncProcessor
{

    /* @var  string $accountAtEntity */
    private $accountAtEntity;

    /**
     * @param string $accountAtEntity
     */
    public function setAccountAtEntity($accountAtEntity)
    {
        $this->accountAtEntity = $accountAtEntity;
    }

    /**
     * @return string
     */
    public function getAccountAtEntity()
    {
        return $this->accountAtEntity;
    }

    /**
     * Use this function to modify any value
     *
     * @param array    $sfdcAccount Values from Salesforce
     * @param array    $output      Intacct query output for object
     * @param string   $action      Type of action
     * @param array    $customerValues
     *
     * @return array
     */
    protected function prepareInnerValue(&$sfdcAccount, $output, $action, $customerValues)
    {
        $customerValues['REQUEST_ORIGINATOR'] = 'SFORCE';

        $key = -1;
        $contactList = array();
        if ( !empty($output) ) {
            $gManagerFactory =  Globals::$g->gManagerFactory;
            $objMgr = $gManagerFactory->getManager('customerentitycontacts');
            $gListparams = array(
                'selects' => array('CATEGORYNAME','CONTACT.NAME'),
                'filters' => array(array(array('ENTITY', '=', $output['ENTITY']))),
            );

            $contactInfoList = $objMgr->GetList($gListparams);

            foreach ($contactInfoList as $key => $contactInfo)
            {
                $contactList[$contactInfo['CATEGORYNAME']] = $key;
                $customerValues['CONTACT_LIST_INFO'][$key] = array('CATEGORYNAME' => $contactInfo['CATEGORYNAME'],
                    'CONTACT' => array('NAME' => $contactInfo['CONTACT.NAME']));
            }
        }

        //Sync BillTo Contact and assign it to customer contact list
        $updateContListBillToShipto = !isset($this->sfpreferences['CONTACTCATOGERY']) ||
            $this->sfpreferences['CONTACTCATOGERY'] == 'T' || $this->sfpreferences['CONTACTCATOGERY'] == 'A';

        if ( $this->sfpreferences['SFORCEBILLTOSHIPTO'] != 'false' ) {
            if ( $sfdcAccount[SforceNamespaceHandler::getPrefix().'Bill_to_Contact__c'] ) {
                $billToContact =
                    $this->syncContact($sfdcAccount[SforceNamespaceHandler::getPrefix().'Bill_to_Contact__c']);
                if ($updateContListBillToShipto) {
                    $customerValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'] = $billToContact;
                }
                $billToContactkey = $contactList['BILLTO ADDRESS'] ?? ++$key;
                $customerValues['CONTACT_LIST_INFO'][$billToContactkey] = array('CATEGORYNAME' => 'BILLTO ADDRESS',
                    'CONTACT' => array('NAME' => $billToContact));
            }

            //Sync ShipTo Contact and assign it to customer contact list
            if ( $sfdcAccount[SforceNamespaceHandler::getPrefix().'Ship_to_Contact__c'] ) {
                $shipToContact =
                    $this->syncContact($sfdcAccount[SforceNamespaceHandler::getPrefix().'Ship_to_Contact__c']);
                if ($updateContListBillToShipto) {
                    $customerValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D'] = $shipToContact;
                }
                $shipToContactkey = $contactList['SHIPTO ADDRESS'] ?? ++$key;
                $customerValues['CONTACT_LIST_INFO'][$shipToContactkey] = array('CATEGORYNAME' => 'SHIPTO ADDRESS',
                    'CONTACT' => array('NAME' => $shipToContact));
            }
        }

        $isBillAddFound = $this->isAddressDataFound($sfdcAccount, 'Billing');
        $isShipAddFound = $this->isAddressDataFound($sfdcAccount, 'Shipping');
        $updateContListBillingShipping = !isset($this->sfpreferences['CONTACTCATOGERY']) ||
            $this->sfpreferences['CONTACTCATOGERY'] == 'I' || $this->sfpreferences['CONTACTCATOGERY'] == 'A';

        if ( $this->sfpreferences['SFORCEBILLINGSHIPPING'] != 'false' ) {
            if ($isBillAddFound) {
                $billToAction = 'create';
                $defaultBillTo = 'B_' . $sfdcAccount[SforceNamespaceHandler::getPrefix().'IntacctID__c'];
                if ( ! isset($customerValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'])
                    && $updateContListBillingShipping )
                {
                    $customerValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'] = $defaultBillTo;
                }
                $customerValues['CUSTOMER.BILLTO.CONTACTNAME'] = $defaultBillTo;
                $customerValues['CUSTOMER.BILLTO.PRINTAS'] = $sfdcAccount['Name'];

                // Split the billing street into two parts if there are multiple lines
                $billingStreet = SforceSyncProcessorMisc::splitAddress($sfdcAccount['BillingStreet']);
                //Override the $sfdcAccount['BillingStreet'] to first line of address else it will carry all lines of
                //address and it will become duplicate address line.
                $sfdcAccount['BillingStreet'] = $billingStreet[0];
                $customerValues['CUSTOMER.BILLTO.MAILADDRESS.ADDRESS1'] = $billingStreet[0];
                $customerValues['CUSTOMER.BILLTO.MAILADDRESS.ADDRESS2'] = $billingStreet[1];
                $customerValues['CUSTOMER.BILLTO.REQUEST_ORIGINATOR'] = 'SFORCE';

                if ( isset($contactList['BILLING ADDRESS']) ) {
                    $billToAction = 'update';
                    $billTokey = $contactList['BILLING ADDRESS'];
                } else {
                    $billTokey = ++$key;
                }
                $customerValues['CONTACT_LIST_INFO'][$billTokey] = array('CATEGORYNAME' => 'BILLING ADDRESS',
                    'CONTACT' => array('NAME' => $customerValues['CUSTOMER.BILLTO.CONTACTNAME']));

                $this->setChildObjAction('BILLTO', $billToAction);
            }

            if ($isShipAddFound) {
                $shipToAction = 'create';
                $defaultShipTo = 'S_' . $sfdcAccount[SforceNamespaceHandler::getPrefix().'IntacctID__c'];
                if ( ! isset($customerValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D'])
                    && $updateContListBillingShipping )
                {
                    $customerValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D'] = $defaultShipTo;
                }
                $customerValues['CUSTOMER.SHIPTO.CONTACTNAME'] = $defaultShipTo;
                $customerValues['CUSTOMER.SHIPTO.PRINTAS'] = $sfdcAccount['Name'];

                // Split the billing street into two parts if there are multiple lines
                $shippingStreet = SforceSyncProcessorMisc::splitAddress($sfdcAccount['ShippingStreet']);
                //Override the $sfdcAccount['BillingStreet'] to first line of address else it will carry all lines of
                //address and it will become duplicate address line.
                $sfdcAccount['ShippingStreet'] = $shippingStreet[0];
                $customerValues['CUSTOMER.SHIPTO.MAILADDRESS.ADDRESS1'] = $shippingStreet[0];
                $customerValues['CUSTOMER.SHIPTO.MAILADDRESS.ADDRESS2'] = $shippingStreet[1];
                $customerValues['CUSTOMER.SHIPTO.REQUEST_ORIGINATOR'] = 'SFORCE';

                if ( isset($contactList['SHIPPING ADDRESS']) )
                {
                    $shipToAction = 'update';
                    $shipTokey = $contactList['SHIPPING ADDRESS'];
                }else {
                    $shipTokey = ++$key;
                }
                $customerValues['CONTACT_LIST_INFO'][$shipTokey] = array('CATEGORYNAME' => 'SHIPPING ADDRESS',
                    'CONTACT' => array('NAME' => $customerValues['CUSTOMER.SHIPTO.CONTACTNAME']));
                $this->setChildObjAction('SHIPTO', $shipToAction);
            }
        }

        if ( $this->sfpreferences['CONTACTCATOGERY'] != 'N'
            && ( !isset($customerValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'])
                || !isset($customerValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D']) )
        ) {
            // $foundBillTo -> Does existing billTo found in updated contact list then only it is valid
            // Case 1: BillTo contact 'A' (SFDC) was assigned earlier and now it is deleted in SFDC
            // then 'A' has to be removed from contact list
            // $billToOccurance -> Count of occurrence of same address in contact list
            // $isSfdcShipToAssigned -> is BillTo assigned (with help of $billToOccurance to deicide it come only
            // through SFDC or through Intacct also)
            // Case 2: BillTo contact 'A' (SFDC) was assigned earlier, But with same contact 'A' with different category
            // in Intacct used as Bill-To contact and now it is deleted in SFDC then 'A' should continue to assigned in Intacct
            // then 'A' has to be removed from contact list
            // Case 3: BillTo contact 'A' (SFDC) was assigned earlier and now it was modified to 'C' in SFDC
            // then 'A' has to change to 'C'
            // Case 4: Bill-To contact is assigned with Intacct address 'I' and BillTo contact 'A' assigned in SFDC
            // then sync process should maintain address 'I' only as

            $foundBillTo = false;
            $billToOccurance = 0;
            $isSfdcBillToAssigned = false;
            $isSfdcBillingAddAssigned = false;

            $foundShipTo = false;
            $shipToOccurance = 0;
            $isSfdcShipToAssigned = false;
            $isSfdcShippingAddAssigned  = false;
            foreach ( $customerValues['CONTACT_LIST_INFO'] as $contactSet ) {

                if ( !empty($output['BILLTO.CONTACTNAME'])
                    && $output['BILLTO.CONTACTNAME'] == $contactSet['CONTACT']['NAME'] )
                {
                    $foundBillTo = true;
                    $billToOccurance++;
                    if ($updateContListBillToShipto && $contactSet['CATEGORYNAME'] == 'BILLTO ADDRESS') {
                        $isSfdcBillToAssigned = true;
                    }
                    if ($updateContListBillingShipping && $contactSet['CATEGORYNAME'] == 'BILLING ADDRESS') {
                        $isSfdcBillingAddAssigned = true;
                    }
                }

                if ( !empty($output['SHIPTO.CONTACTNAME'])
                    && $output['SHIPTO.CONTACTNAME'] == $contactSet['CONTACT']['NAME'] )
                {
                    $foundShipTo = true;
                    $shipToOccurance++;
                    if ($updateContListBillToShipto && $contactSet['CATEGORYNAME'] == 'SHIPTO ADDRESS') {
                        $isSfdcShipToAssigned = true;
                    }
                    if ($updateContListBillingShipping && $contactSet['CATEGORYNAME'] == 'SHIPPING ADDRESS') {
                        $isSfdcShippingAddAssigned = true;
                    }
                }

            }
            $isSfdcBillToAssigned = $isSfdcBillToAssigned && $billToOccurance == 1;
            $isSfdcShipToAssigned = $isSfdcShipToAssigned && $shipToOccurance == 1;

            $isSfdcBillingAddAssigned = $isSfdcBillingAddAssigned && $billToOccurance == 1;
            $isSfdcShippingAddAssigned = $isSfdcShippingAddAssigned && $shipToOccurance == 1;

            // $foundBillTo : Is existing BILLTO Contact is valid in new set of contacts in diferent set of contact category ?
            // $isBillToAssigned :
            if ( $foundBillTo && !($isSfdcBillToAssigned || $isSfdcBillingAddAssigned) ) {
                $customerValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'] = $output['BILLTO.CONTACTNAME'];
            }
            if ( $foundShipTo && !($isSfdcShipToAssigned || $isSfdcShippingAddAssigned) ) {
                $customerValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D'] = $output['SHIPTO.CONTACTNAME'];
            }
        }

        //Jira: IA-152961
        //This is special case as DISPLAYCONTACT.MAILADDRESS.ADDRESS1 gets split into two lines and mapping is not exposed in field mapping directly
        //If there is custom fieldmapping for DISPLAYCONTACT.MAILADDRESS.ADDRESS1 then that need to use while creating and updating the address line
        //Unset the same custom fieldmapping so it won't repeat in further translation in reformatValues()
        $addressLineSet = false;
        if ( !empty($this->objSchemaMap['customFieldsMap']) ) {
            $addressLineKey = null;
            $custFieldsMap = $this->objSchemaMap['customFieldsMap'];
            if ( isset($custFieldsMap['BillingStreet--CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1']) )
            {
                $addressLineKey = $custFieldsMap['BillingStreet--CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1']['PARTNERFIELD'];
                unset($this->objSchemaMap['customFieldsMap']['BillingStreet--CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1']);
            }
            if ( isset($custFieldsMap['ShippingStreet--CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1']) )
            {
                $addressLineKey = $custFieldsMap['ShippingStreet--CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1']['PARTNERFIELD'];
                unset($this->objSchemaMap['customFieldsMap']['ShippingStreet--CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1']);
            }
            if (!is_null($addressLineKey)) {
                // Split the billing street into two parts if there are multiple lines
                $addressLine = SforceSyncProcessorMisc::splitAddress($sfdcAccount[$addressLineKey]);
                $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1'] = $addressLine[0];
                $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS2'] = $addressLine[1];
                $addressLineSet = true;
            }

        }

        $customerValues['CUSTOMER.DISPLAYCONTACT.PRINTAS'] = $sfdcAccount['Name'];
        if ( !isset($output['DISPLAYCONTACT.CONTACTNAME']) && ( $isBillAddFound || $isShipAddFound) ) {
            $customerValues['CUSTOMER.DISPLAYCONTACT.CONTACTNAME'] = isl_trim($sfdcAccount['Name']) .
                "(C" . isl_trim($sfdcAccount[SforceNamespaceHandler::getPrefix().'IntacctID__c']) . ")";

            if (!$addressLineSet) {
                if ( $isBillAddFound ) {
                    if ( !isset($billingStreet) ) {
                        // Split the billing street into two parts if there are multiple lines
                        $billingStreet = SforceSyncProcessorMisc::splitAddress($sfdcAccount['BillingStreet']);
                    }
                    $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1'] = $billingStreet[0];
                    $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS2'] = $billingStreet[1];

                } else if ( $isShipAddFound && !isset($shippingStreet) ) {
                    if ( !isset($shippingStreet) ) {
                        // Split the shipping street into two parts if there are multiple lines
                        $shippingStreet = SforceSyncProcessorMisc::splitAddress($sfdcAccount['ShippingStreet']);
                    }
                    $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS1'] = $shippingStreet[0];
                    $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ADDRESS2'] = $shippingStreet[1];
                }
            }

            $displayAddressSfKey = $isBillAddFound ? 'Billing' : 'Shipping';
            $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.CITY'] = $sfdcAccount[$displayAddressSfKey.'City'];
            $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.STATE'] = $sfdcAccount[$displayAddressSfKey.'State'];
            $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.ZIP']
                = $sfdcAccount[$displayAddressSfKey.'PostalCode'];
            $customerValues['CUSTOMER.DISPLAYCONTACT.MAILADDRESS.COUNTRY']
                = SforceSyncProcessorMisc::adjustCountryName($sfdcAccount[$displayAddressSfKey.'Country']);
        }
        $customerValues = parent::prepareInnerValue($customerValues, $output, $action, $customerValues);
        return $customerValues;
    }

    /**
     * @param string $contactExternalId
     *
     * @return string
     */
    private function syncContact($contactExternalId)
    {
        $gErr = Globals::$g->gErr;
        $contactSfId = $this->subMgr->GetIntacctId($contactExternalId, 'CONTACT', SforceNamespaceHandler::SUBSCRIBER_NAME);
        if ( !$contactSfId ) {
            $contactArgs['OBJECT'] = 'CONTACT';
            $contactArgs['KEY'] = $contactExternalId;
            $sforceSyncProcessorContact
                = SyncProcessorManagerFactory::getSforceSyncProcessor($contactArgs['OBJECT'], $this->sfpreferences);
            $ret = $sforceSyncProcessorContact->create($contactArgs);
            if ( !$ret->getStatus() ) {
                $gErr->addIAError(
                    'SFDC-1247', __FILE__.'.'.__LINE__, 'Unable to synchronize '
                    .'CONTACT with Salesforce Id '.$contactExternalId,
                    ['CONTACT_EXTERNAL_ID' => $contactExternalId]
                );
            } else {
                //$contactSfId = $ret->setExternalIds()[0]['intacctId'];
                $idSet = $ret->getExternalIds();
                $contactSfId = $idSet[$contactExternalId]['intacctId'];
            }
        }
        return $contactSfId;
    }

    /**
     * @return string
     */
    protected function getreadFields()
    {
        //return array('RECORDNO', 'DISPLAYCONTACT.CONTACTNAME', 'CONTACT_LIST_INFO');
        return '';
    }

    /**
     * @param string[] $sfdcAccount
     * @param string   $addressKey
     *
     * @return bool
     */
    private function isAddressDataFound($sfdcAccount, $addressKey)
    {
        $addressFields = array($addressKey.'City',
            $addressKey.'Country',
            $addressKey.'PostalCode',
            $addressKey.'State',
            $addressKey.'Street'
        );
        $foundBillingData = false;
        foreach ($addressFields as $field) {
            if (isset($sfdcAccount[$field]) && $sfdcAccount[$field] != '') {
                $foundBillingData = true;
                break;
            }
        }
        return $foundBillingData;
    }

    /**
     * @return string|null
     * @throws Exception
     */
    protected function getSequenceNumbering()
    {
        $kARid = Globals::$g->kARid;

        //AR Customer Sequence preference should overwrite SFDC Customer Seq.
        $custSeq = GetPreferenceForProperty($kARid, 'RCUSTSEQUENCE');

        // if the company is configured to use customer sequences, set the sequence.
        if ( (!isset($custSeq) || $custSeq == '')
            && $this->sfpreferences['SFORCECUSTSEQUENCE'] == 'true'
        ) {
            $custSeq = $this->sfpreferences['SFORCEDOCNUMBERING'];
        }
        return $custSeq;
    }

    /**
     * @param array &$objValues
     */
    protected function assignChildRecords(&$objValues)
    {
        if ( isset($this->sfpreferences['CONTACTCATOGERY'])) {
            $objValues['BILLTO'] = array();
            $objValues['SHIPTO'] = array();
            if ($this->sfpreferences['CONTACTCATOGERY'] != 'N') {
                // Assign Billto to customer
                if (isset($objValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'])
                    && !empty($objValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'])) {
                    $objValues['BILLTO']['CONTACTNAME'] = $objValues['OVERWRITEFIELDSVALUE']['BILLTOCONTACTNAME__D'];
                } else {
                    $objValues['BILLTO']['CONTACTNAME'] = null;
                }
                // Assign Shipto to customer
                if (isset($objValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D'])
                    && !empty($objValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D'])) {
                    $objValues['SHIPTO']['CONTACTNAME'] = $objValues['OVERWRITEFIELDSVALUE']['SHIPTOCONTACTNAME__D'];
                } else {
                    $objValues['SHIPTO']['CONTACTNAME'] = null;
                }
            } else {
                unset($objValues['SHIPTO']['CONTACTNAME']);
                unset($objValues['BILLTO']['CONTACTNAME']);
            }
        }
    }

    /**
     * This is to reset the subscription links and re-sync the result of merge accounts
     *
     * @param string[] $sfObj
     *
     * @return SyncProcessorResult
     */
    public function merge($sfObj)
    {
        $gErr = Globals::$g->gErr;
        $mergeAccIds = $this->getMergedAccounts($sfObj['KEY']);
        //if ( empty($mergeAccIds) ) {
        //    $message = 'Merged account not found in Salesforce for Account Id '.$sfObj['KEY'].'.';
        //    $this->syncResult->setSynMessage($message);
        //}
        //Include master record too for clear subscription
        array_push($mergeAccIds, $sfObj['KEY']);
        if(! $this->subMgr->DeleteSubscriptions(
            $mergeAccIds, $this->objSchemaMap['INTACCTOBJECT'], SforceNamespaceHandler::SUBSCRIBER_NAME))
        {
            $gErr->addError(
                'SFDC-1248',
                __FILE__.'.'.__LINE__,
                'Unable to delete the records for merged accounts in Salesforce'
            );
        } else{
            return $this->update($sfObj);
        }
        $this->syncResult->setStatus(false);
        return $this->syncResult;
    }

    /**
     * @param string $masterRecId
     *
     * @return array
     */
    private function getMergedAccounts($masterRecId)
    {
        $mergeAccIds = [];
        $select = "Id, masterRecordId";
        $whereClause = "isDeleted=true and masterRecordId = '".$masterRecId."'";
        $ret = $this->queryAll($select, $this->objSchemaMap['SFOBJECT'], $whereClause);
        // If return is false then ???
        if ( $ret && $ret->totalSize > 0 ) {
            foreach ( $ret->records as $rec ) {
                $mergeAccIds[] = $rec->Id;
            }
        }

        return $mergeAccIds;
    }

    /**
     * @param string $action
     *
     * @return bool
     */
    protected function isValidAction(string $action): bool
    {
        $accContactSyncOnce = $this->sfpreferences['ACCCONTACTSYNCONCE'] ?? null;
        if ( $accContactSyncOnce === 'true' && $action == 'update'){
            $msg = 'Account is already synced with Intacct. Update sync is disabled, edit Advanced CRM Integration configuration options to update.';
            Globals::$g->gErr->addError(
                'SFDC-1255',
                __FILE__.'.'.__LINE__,
                $msg
            );
            return false;
        }
        return true;
    }
}
