<?php

/**
 * class IntacctSforceSyncProcessorSODocument to sync the Intacct object 'SODOCUMENT' from Intacct to Salseforce
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

require_once 'SforceConstants.inc';

class IntacctSforceSyncProcessorSODocument extends IntacctSforceSyncProcessor
{

    /* @var bool $isTbInvoice */
    protected $isTbInvoice = false;

     /**
      * @param bool $value
      */
    public function setIsTbInvoice($value)
    {
        $this->isTbInvoice = $value;
    }

    /**
     * @param string[][] $args
     *
     * @return bool
     */
    public function delete($args)
    {
        $ok = parent::delete($args);
        $args = $this->processOpportunityId($args);
        $ok = $ok && $this->setOppAmount($args);
        return $ok;
    }

    /**
     * @param string[][] $fieldsMap
     *
     * @return string[][]
     */
    protected function updateFieldsMap($fieldsMap)
    {
        if ( $this->sfpreferences['SFORCECONTRACTSYNC'] == 'true' && $this->doctype == 'Invoice' ) {
            if (!isset($this->sfFieldMap)) {
                $this->sfFieldMap = new SforceFieldMap('SODOCUMENT');
            }
            $soDocumentContractFieldsMap = $this->sfFieldMap->getSODocumentContractFieldsMap();

            foreach ($soDocumentContractFieldsMap as $fieldinfo) {
                $fieldsMap[$fieldinfo['PARTNERFIELD']] = $fieldinfo;
            }
        }
        return $fieldsMap;
    }

    /**
     * @param string[][] $args
     */
    protected function updateObjSchemaMap($args)
    {
        if ( $this->sfpreferences['SYNCDOCUMENTTYPEDETAIL#' . $args['DOCPARID']] == 'F' ) {
            array_shift($this->objSchemaMap['OWNEDOBJS']);
        }
        parent::updateObjSchemaMap($args);
    }

    /**
     * Assigns the exchange rate before calling the default implementation.
     *
     * @param string[][] $intacctObject
     */
    protected function adjustIntacctObject(&$intacctObject)
    {
        $this->assignExchangeRate($intacctObject);

        parent::adjustIntacctObject($intacctObject);
    }

    /**
     * Updates the intacct object by assigning the exchange rate.
     *
     * @param string[][] $intacctObject
     */
    private function assignExchangeRate(&$intacctObject)
    {
        if ($this->sfismcp) {
            $sfClient = $this->getSfClient();
            $corpExchRate
                = SforceSyncProcessorMisc::getCorpExchangeRate(
                $intacctObject['CURRENCY'], $intacctObject['EXCHRATE'], $intacctObject['BASECURR'],
                $intacctObject['EXCHRATEDATE'], $sfClient);
            $intacctObject['SFEXCHRATE'] = $corpExchRate;
            $this->setCorpExchRate($corpExchRate);
        }
    }

    /**
     * @param array $args
     * @return mixed
     */
    protected function prepareValues($args)
    {
        // Get Opportunity ID in $args
        $args = $this->processOpportunityId($args);
        $args = $this->prepSODocument($args);
        // TODO : For SF MCP, get corporate exchange rate & set as $this->SFEXCHANGE for SFORCE_DERIVED_CURRENCY fields calculation
        $this->assignExchangeRate($args);

        return parent::prepareValues($args);

    }

    /**
     * @param array $args
     *
     * @return array
     */
    protected function prepSODocument($args)
    {
        $args['TOTAL'] = $this->getTotal($args);
        $args['SUBTOTAL'] = $this->getSubtotal($args['ENTRIES']);
        if ($this->sfismcp) {
            //No need as Corp_Currency_Subtotal__c & Corp_Currency_Total__c will derived from
            // SUBTOTAL & TOTAL with SFEXCHRATE
            $args['BASETOTAL'] = $this->getBaseTotal($args);
            $args['BASESUBTOTAL'] = $this->getBaseSubTotal($args['ENTRIES']);
        }

        return $args;
    }


    /**
     * Adds opportunity ID in $args
     *
     * @param string[][] $args
     *
     * @return string[][]
     */
    public function processOpportunityId($args)
    {
        // if SFORCEID is not set (The opportunity value), check to see if there is a source document
        // if so, check to see if the source document has a linked opportunity.
        if (      (!array_key_exists('SFORCEID', $args) || $args['SFORCEID'] == '')
            && $this->isTbInvoice == false
        ) {
            $oppid = $this->getOpportunityID($args);

            // If no opportunity link found within Intacct subscription,
            // It may be due to some error during _CreateSODocument,
            // SF document gets created without linked opportunity, in this case,
            // Check if we've opportunity reference stored on Intacct side (dochdr.PONUMBER),
            // and on SF side, we've this opportunity record available,
            // If so, link this document to its referenced opportunity
            if ($oppid == '' && array_key_exists('PONUMBER', $args) && $args['PONUMBER'] != '') {
                // retrive opportunity Id from SFDC
                $sfAccountExtId = $this->subMgr->GetExternalId(
                    $args['CUSTVENDID'], 'CUSTOMER', SforceNamespaceHandler::SUBSCRIBER_NAME
                );
                $oppid = $this->getSFOpportunityIdByName($sfAccountExtId, $args['PONUMBER']);
            }

            // populate Opportunity Id
            $args['SFORCEID'] = $oppid;
        }
        return $args;
    }

    /**
     * Get the related opportunity for a sales document
     *
     * @param string[][] $sodocValues
     *
     * @return false|string SFDC key for the related opportunity or false if none exists
     */
    private function getOpportunityID($sodocValues)
    {
        // Get the SODOCUMENTOPP subscription, value will be available in case of update
        $oppid = $this->subMgr->GetExternalId(
            $sodocValues[$this->objSchemaMap['INTACCTPRIMFIELD']],
            'SODOCUMENTOPP', SforceNamespaceHandler::SUBSCRIBER_NAME
        );
        $this->logInfo("Adding opportunityid of ". $oppid,__FILE__.'.'.__LINE__);
        if (!$oppid || $oppid == '') {
            if (array_key_exists('CREATEDFROM', $sodocValues) && $sodocValues['CREATEDFROM'] != '') {
                $sODocumentManager = Globals::$g->gManagerFactory->getManager('SODocument');
                $retvalue = $sODocumentManager->GetList(array(
                    'selects' => array('RECORDNO'),
                    'filters' =>     array(array(array('DOCID', '=', $sodocValues['CREATEDFROM'])))));

                // Get the SODOCUMENTOPP subscription
                if (isset($retvalue[0]) && $retvalue[0] != '') {
                    $oppid = $this->subMgr->GetExternalId(
                        $retvalue[0]['RECORDNO'], 'SODOCUMENTOPP',
                        SforceNamespaceHandler::SUBSCRIBER_NAME
                    );
                    $this->logInfo("Adding opportunityid of ". $oppid,__FILE__.'.'.__LINE__);
                }
            }

            // if we still don't have an oppid, see if we can trace back to a parent document via recurring transactions
            if ($oppid == '') {
                $recurDocumentMgr = Globals::$g->gManagerFactory->getManager('RecurDocument');
                $retvalue = $recurDocumentMgr->GetRecurParentRecordNo($sodocValues['DOCID']);

                // Get the SODOCUMENTOPP subscription
                $oppid = $this->subMgr->GetExternalId(
                    $retvalue, 'SODOCUMENTOPP',
                    SforceNamespaceHandler::SUBSCRIBER_NAME
                );
                $this->logInfo("Adding opportunityid of ". $oppid,__FILE__.'.'.__LINE__);
            }
        }

        return $oppid;
    }

    /**
     * @param string $sfAccountExtId
     * @param string $oppname
     *
     * @return string
     */
    private function getSFOpportunityIdByName($sfAccountExtId, $oppname)
    {

        // bad request
        if (!$sfAccountExtId || !isset($oppname) || $oppname == '') {
            return '';
        }

        $opp = '';
        $select = 'Id';
        $sfobjName = 'Opportunity';
        $oppname = addslashes($oppname);
        $whereClause = "AccountId = '$sfAccountExtId' and Name = '$oppname'";
        $result = $this->query($select, $sfobjName, $whereClause);
        
        if ($result != false && $result->totalSize == 1) {
            //$sfObjData->records[0]->$field
            foreach ($result->records as $oppObj) {
                $opp = $oppObj->Id;
            }
        }

        return $opp;
    }

    /**
     * @param array $args
     *
     * @return int
     */
    private function getTotal($args)
    {
        $sumOfSubtotals = 0;
        // first try the subtotals
        if (is_array($args) && array_key_exists('SUBTOTALS', $args) && count($args['SUBTOTALS']) > 0) {
            // look for the subotal with 'TOTAL'
            foreach ($args['SUBTOTALS'] as $total) {
                if ($total['DESCRIPTION'] == 'TOTAL') {

                    if ($this->sfismcp) {
                        return $total['TRX_TOTAL'];
                    } else {
                        return $total['TOTAL'];
                    }

                }
                if (!in_array($total['DESCRIPTION'], array('TOTAL', 'SUBTOTAL'))) {
                    if ($this->sfismcp) {
                        $sumOfSubtotals = bcadd($sumOfSubtotals, $total['TRX_TOTAL']);
                    } else {
                        $sumOfSubtotals = bcadd($sumOfSubtotals, $total['TOTAL']);
                    }
                }

            }
            // if we have gotten here, just fall down to the next logic
        }
        $subTotal = $this->getSubtotal($args['ENTRIES']);
        $total = bcadd($subTotal, $sumOfSubtotals);
        return $total;
    }

    /**
     * @param array $args
     *
     * @return int
     */
    private function getBaseTotal($args)
    {
        $sumOfSubtotals = 0;
        // first try the subtotals
        if (array_key_exists('SUBTOTALS', $args) && count($args['SUBTOTALS']) > 0) {
            // look for the subtotal with 'TOTAL'
            foreach ($args['SUBTOTALS'] as $total) {
                if ($total['DESCRIPTION'] == 'TOTAL') {
                    return $total['TOTAL'];
                }
                if (!in_array($total['DESCRIPTION'], array('TOTAL', 'SUBTOTAL'))) {
                    $sumOfSubtotals = bcadd($sumOfSubtotals, $total['TOTAL']);
                }
            }
            // if we have gotten here, just fall down to the next logic
        }

        $baseSubTotal = $this->getBaseSubTotal($args['ENTRIES']);
        $baseTotal = bcadd($baseSubTotal, $sumOfSubtotals);
        return $baseTotal;
    }

    /**
     * @param array $entries
     *
     * @return int
     */
    private function getSubtotal($entries)
    {
        $total = 0;
        foreach ($entries as $entry) {
            if ($this->sfismcp) {
                $total += $entry['TRX_VALUE'];
            } else {
                $total += $entry['UIVALUE'];
            }
        }
        return $total;
    }

    /**
     * @param array $entries
     *
     * @return int
     */
    private function getBaseSubTotal($entries)
    {
        $total = 0;
        foreach ($entries as $entry) {
            $total += $entry['TOTAL'];
        }
        return $total;
    }

    /**
     * @param string $oppkey
     * @return array
     */
    private function getOppAmt($oppkey)
    {
        //Make From table
        $fromTable = ( $this->sfpreferences['SFORCEREPQAOA'] == 'true' )
            ? SforceNamespaceHandler::getPrefix() . 'Sales_Order__c'
            : SforceNamespaceHandler::getPrefix() . 'Sales_Quote__c';

        if ($this->sfismcp) {
            $fields = ( $this->sfpreferences['SFORCEINCLUDETAXINQUOTE'] == 'true' )
                ? array(SforceNamespaceHandler::getPrefix() . 'Total__c','LastModifiedDate',
                    SforceNamespaceHandler::getPrefix() . 'Corp_Currency_Total__c')
                : array(SforceNamespaceHandler::getPrefix() . 'Subtotal__c','LastModifiedDate',
                    SforceNamespaceHandler::getPrefix() . 'Corp_Currency_Subtotal__c');
        } else {
            $fields = ( $this->sfpreferences['SFORCEINCLUDETAXINQUOTE'] == 'true' )
                ? array(SforceNamespaceHandler::getPrefix() . 'Total__c','LastModifiedDate')
                : array(SforceNamespaceHandler::getPrefix() . 'Subtotal__c','LastModifiedDate');
        }

        //Make Select fields
        $select = implode(",", $fields);
        //Make Where clause
        $whereClause = SforceNamespaceHandler::getPrefix() . "Opportunity__c = '$oppkey'";

        $result = $this->query($select, $fromTable, $whereClause);

        $latestAmount = 0;
        $corpCurrencyAmount = 0;
        if ($result != false && $result->totalSize >= 1) {
            // 	first Transaction only
            $maxdate = '';
            foreach ($result->records as $key => $salesDoc) {
                if ($key == 0) {
                    $latestAmount = $salesDoc->{$fields[0]};
                    $maxdate = $salesDoc->{$fields[1]};
                    if ($this->sfismcp) {
                        $corpCurrencyAmount = $salesDoc->{$fields[2]};
                    }
                } else if ($salesDoc->{$fields[1]} > $maxdate) {
                    $latestAmount = $salesDoc->{$fields[0]};
                    $maxdate = $salesDoc->{$fields[1]};
                    if ($this->sfismcp) {
                        $corpCurrencyAmount = $salesDoc->{$fields[2]};
                    }
                }
            }
        }
        return array($latestAmount, $corpCurrencyAmount);
    }

    /**
     * @param string[][] $args
     *
     * @return bool
     */
    protected function setOppAmount($args)
    {
        if ( $args['STATE'] == 'Draft'
            || !isset($args['SFORCEID'])
            || $args['SFORCEID'] == ''
        ) {
            return true;
        }

        if ( $this->sfpreferences['SFORCEUPDOPPAMT'] == 'true' ) {
            $oppkey = $args['SFORCEID'];
            $amount  = $this->getOppAmt($oppkey);

            $postData = '';
            $postData .= '"Amount":"' . $amount[0] . '"';
            if ( $this->sfismcp ) {
                $postData .= ',"' . SforceNamespaceHandler::getPrefix() . 'Corp_Currency_Amount__c":"'
                    .$amount[1]. '"';
            }
            $postData = '{' . $postData . '}';

            if ( !$this->patch('Opportunity', $oppkey, $postData) ) {
                $this->logError(
                    '**********', __FILE__.'.'.__LINE__, "Opportunity Amount failed to update."
                );
                return false;
            }
        }

        return true;
    }

    /**
     * @param array      $args
     * @param string     $externalId
     * @param null|bool  $syncStatus
     *
     * @return bool
     */
    protected function afterUpsertAction($args, $externalId, $syncStatus = null)
    {
        $ok = false;
        if ( $syncStatus ) {
            $ok = $this->setOppAmount($args);
            if ( !$ok ) {
                $this->processDelete($args, $externalId);
            }
        }

        // Update SF payment details
        // We post payment details on SFDC only during manual re-post action for a document
        // For normal edit and save for a sales document, no need to post payment details,
        // since if payment details exist for document, edit will not be allowed anyways
        if ( $ok && $args['sfrepost']
            && $this->sfpreferences['POSTPAYMENTDETAILS'] == "true"
            && ($this->doctype == 'Invoice' || $args['_DOCPAR']['DOCCLASS'] == 'Invoice')
        ) {
            // copy required params to payment args
            $payargs = $args;
            $payargs['SFOBJTYPE'] = $this->objSchemaMap['SFOBJECT'];
            $payargs['SODOCKEY'] = $externalId;
            //$payargs['CORPEXCHRATE'] = $this->corpExchRate;

            $paymentSyncProcessor
                = SyncProcessorManagerFactory::getIntacctSforceSyncProcessor(
                    'SODOCUMENTPAYMENT', $this->doctype, $this->sfpreferences, $this->syncAdapter, null
                );
            $paymentSyncProcessor->setCorpExchRate($this->corpExchRate);
            if (!$paymentSyncProcessor->set($payargs) && $this->objSchemaMap['SFOBJECT'] == 'Sales_Invoice__c') {
                //If any error while posing payment details then throw the error only for doctype -> Sales Invoice
                //$this->handleErrors($args);
                //return false;
                $ok = false;
            }
        }
        //updating contract amount in intacct contract summary in salesforce
        if ( !empty($args['CNCONTRACTINFOS']) ) {
            foreach ($args['CNCONTRACTINFOS'] as $contractInfo) {
                /* @var IntacctSforceSyncProcessorSFContract $iaSyncProcessorSFContract */
                $iaSyncProcessorSFContract = SyncProcessorManagerFactory::getIntacctSforceSyncProcessor('SFCONTRACT', '', $this->sfpreferences, $this->syncAdapter, null);
                $contractArgs['CONTRACTID'] = $contractInfo['CONTRACTID'];
                $contractArgs['RECORDNO'] = $contractInfo['CONTRACTKEY'];
                $iaSyncProcessorSFContract->setPostContractBalanceOnly(true);
                $ret = $iaSyncProcessorSFContract->set($contractArgs);
                if ( ! $ret) {
                    $this->logInfo("Contract Summary Amount has not been updated",
                        __FILE__.'.'.__LINE__);
                }
            }
        }

        if ( !$ok ) {
            $this->handleErrors($args);
        } else {
            // log success in partner sync log
            $this->logSyncStatus($this->objSchemaMap['INTACCTOBJECT'], $args['RECORDNO']);
        }
        return $ok;
    }

    /**
     * @param string[][] $args
     * @param string     $errMsg
     *
     * @return bool
     */
    protected function handleErrors($args, $errMsg='')
    {
        // send error email
        $this->syncAdapter->getObjError()->GetErrList($errs);
        $PkgId = $this->syncAdapter->getPackageId();
        if ($errs) {
            foreach ($errs as $err) {
                $errMsg  .= "\n\n"."Error: ".$err['NUMBER']."($PkgId)\n";
                $errMsg  .= "Description: ".$err['DESCRIPTION']."  ".$err['CDESCRIPTION']."\n";
                if (isset($err['CORRECTION']) && $err['CORRECTION'] != '') {
                    $errMsg  .= "Correction: ".$err['CORRECTION']."\n";
                }
            }
            $obj['DOCID'] = $args['DOCID'];
            $obj['WHENCREATED'] = $args['WHENCREATED'];
            $obj['CUSTVENDID'] = $args['CUSTVENDID'];
            $obj['CUSTVENDNAME'] = $args['CUSTVENDNAME'];
            $obj['MESSAGE'] = $errMsg;
            $this->sendEmailErrorNotification($obj, $errMsg);
        }

        // create partner sync error log
        $this->logSyncStatus($this->objSchemaMap['INTACCTOBJECT'], $args['RECORDNO'], 'F', $errMsg);

        return true;
    }

    /**
     * @param string[] $obj
     * @param string   $errMsg
     */
    private function sendEmailErrorNotification($obj, $errMsg)
    {
        $emailid = GetMyContactEmail(true);
        $email = new IAEmail($emailid);
        $email->_from = "Intacct Customer Support <<EMAIL>>";
        $email->_reply_to = "<EMAIL>";
        $email->subject = "Salesforce.com posting error notification for Transaction ID: ".$obj['DOCID'];
        $email->body = "Hi ".GetMyContactPrintas().",\n\n".
            "The following transaction encountered an error while attempting to post to Salesforce.com\n\n" .
            "Company ID: " .GetMyCompanyTitle()."\n".
            "Transaction ID: " .$obj['DOCID']."\n".
            "Transaction creation date: ".$obj['WHENCREATED']."\n".
            "Customer: ".$obj['CUSTVENDID']."--".$obj['CUSTVENDNAME']."\n\n".
            "Following error(s) detected:-\n".
            "--------------------------\n".$errMsg."\n--------------------------\n\n".
            "Once this error is resolved, you can synchronize this transaction with Salesforce by".
            " logging into Intacct and clicking the 'Synchronize Salesforce Document' link located ".
            "in the transaction's History tab. "."\n\n".
            "If you are unable to resolve this error, please contact your Salesforce or Intacct administrator.";
        $email->send();
    }

    /**
     * @param string $object
     * @param string $objectid
     * @param string $state
     * @param string $result
     *
     * @return string[]|bool
     */
    private function logSyncStatus($object, $objectid, $state='S', $result='')
    {
        // create partner sync error log
        $partnerSyncLogObj['PARTNER'] = 'SFORCE';
        $partnerSyncLogObj['OBJECT'] = $object;
        $partnerSyncLogObj['OBJECTID'] =  $objectid;
        $partnerSyncLogObj['STATE'] =  $state;
        $partnerSyncLogObj['STATUS'] =  'T';
        $partnerSyncLogObj['RESULT'] = $result;

        $partnerSyncLogMgr = Globals::$g->gManagerFactory->getManager('PartnerSyncLog');
        $ok = $partnerSyncLogMgr->add($partnerSyncLogObj);
        return $ok;
    }

    /**
     * @param array          &$args
     * @param string[][]     $fieldsMap
     * @param string[]|null &$postOnlyData
     *
     * @return string[]
     */
    protected function preparePostData(&$args, $fieldsMap, &$postOnlyData = null)
    {
        $this->setSkipDerived(false);
        return parent::preparePostData($args,$fieldsMap,$postOnlyData);
    }

    /**
     * @param string        $action
     * @param array $iaObjValues
     *
     *  @return bool
     */
    protected function isValidAction($action, $iaObjValues = null)
    {
        if(!$this->validateForSubscriptionOnly($iaObjValues, $action)) {
            return parent::isValidAction($action);
        } else {
            $this->logInfo("Sync back to Salesforce is disabled for this transaction: " . $this->docparid, __FILE__ . '.' . __LINE__);
            return false;
        }
    }

    /**
     * @param array $args
     * @param string $action
     *
     * @return bool
     */
    private function validateForSubscriptionOnly(array $args, string $action)
    {
        $ok = false;
        if(SalesforceUtil::isFromSFOne2OneDocSyncMap($this->sfpreferences, $this->docparid)) {
            $ok = true;
            if( $action === 'create' && !empty($args['SFORCEID']) ) {
                $this->createMoreSubscription($args);
            }
        }
        return $ok;
    }

    /**
     * @param string[][] $args
     * @param string $externalId
     *
     * @return bool
     */
    protected function processDelete($args, $externalId): bool
    {
        $ok = parent::processDelete($args, $externalId);
        $namespaceHandler = $this->getNamespaceHandler();
        $externalIdFlow = $this->subMgr->GetExternalId($args[$this->objSchemaMap['INTACCTPRIMFIELD']]
            , $this->getObjectName()
            , $namespaceHandler::SUBSCRIBER_NAME
            , GetMyCompany()
        );

        $ok1 = true;
        if ($externalIdFlow) {
            if ($this->objSchemaMap['DELETEFROMSFDC']) {
                //Delete from SFDC
                if (!$this->sfDelete($this->getSfObject($args), $externalIdFlow)) {
                    $this->logInfo("Unable to perform the delete action in Salesforce.",
                        __FILE__ . '.' . __LINE__);
                    $ok = false;
                }
            }

            $ok1 = $this->subMgr->DeleteSubscription(
                $args[$this->objSchemaMap['INTACCTPRIMFIELD']],
                $this->getObjectName(), $namespaceHandler::SUBSCRIBER_NAME, GetMyCompany()
            );
        }
        return $ok && $ok1;
    }

    /**
     * @return string
     */
    protected function getObjectName(): string
    {
        return 'SODOCUMENT2';
    }

    /**
     * @param array $args
     * @return string
     * @throws IAException
     */
    protected function getSfObject(array $args): string
    {
        return SyncProcessorSODocumentV2::getSoDocSFObjName();
    }

}
