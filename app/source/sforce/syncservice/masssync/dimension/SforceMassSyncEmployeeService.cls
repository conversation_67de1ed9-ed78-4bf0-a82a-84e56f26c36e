<?php

/**
 * Class SforceMassSyncItemService
 *
 * Implementation of the mass sync service for the EMPLOYEE objects
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2018 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class SforceMassSyncEmployeeService extends SforceMassSyncService
{
    /**
     * Returns the string referring to the intacct managed CUSTOMER object.
     *
     * @return string
     */
    protected function getIntacctManagedObject()
    {
        return IntacctManagedObject::EMPLOYEE;
    }

}