<?php

/**
 * Class SforceMassSyncCustomerService
 *
 * Implementation of the mass sync service for the CUSTOMER objects
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2018 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class SforceMassSyncCustomerService extends SforceMassSyncService
{

    /**
     * Returns the string referring to the intacct managed CUSTOMER object.
     *
     * @return string
     */
    protected function getIntacctManagedObject()
    {
        return IntacctManagedObject::CUSTOMER;
    }

    /**
     * Prepares the list of intacct objects in the right order specific to the api call
     *
     * @param array $syncArgs
     *
     * @return IntacctSyncObject[]
     */
    public function prepareIntacctSyncList($syncArgs)
    {
        $customerSyncObject = parent::getDefaultIntacctSyncObject($syncArgs);

        $syncList = [];
        if (isset($syncArgs['TRX-DOCS'])) {
            $syncList = $this->createTrxSyncObjectList($syncArgs);
        } else if (isset($syncArgs['DEPENDENT-OBJECTS'])) {
            $syncList = $this->createDependentObjectList($syncArgs);
        }

        if (!empty($syncList)) {
            $customerSyncObject->setPostSyncObjects($syncList);
        }

        return [$customerSyncObject];
    }

    /**
     * Creates the list of the dependent objects for syncing to salesforce
     * @param array $syncArgs
     *
     * @return IntacctSyncObject[]
     * @throws Exception
     */
    private function createDependentObjectList($syncArgs)
    {
        $list = [];

        // record# of all accounts from the xml request
        $customerRecordIds = $this->retrieveCustomerRecordIds($syncArgs);

        if (isset($syncArgs['DEPENDENT-OBJECTS'])) {
            $dependentObjectTypes = $this->adjustDependentObjects($syncArgs);
            foreach ($dependentObjectTypes as $objectType) {
                $service = SforceMassSyncServiceFactory::getSforceMassSyncService($objectType);
                $service->addToSyncArgumentSource(self::REFERENCE_RECORD_IDS, $customerRecordIds);


                $list[] = $service->prepareIntacctSyncList($syncArgs);
            }
        }

        return $list;
    }

    /**
     * Compiles the list of the sync objects based on the list of the transaction document types in the
     * passed input parameter.
     *
     * @param array $syncArgs
     *
     * @return IntacctSyncObject[]
     */
    private function createTrxSyncObjectList($syncArgs)
    {
        $list = [];

        // record# of all accounts from the xml request
        $customerRecordIds = $this->retrieveCustomerRecordIds($syncArgs);

        // List of all thansaction definisions from the xml request
        $trxDefinitions = $this->retrieveTrxDefinitions($syncArgs);

        $syncList = [];
        foreach ($trxDefinitions as $td) {
            $service = SforceMassSyncSalesDocumentServiceFactory::getDocumentSyncService($td['DOCCLASS'], $td['DOCID']);

            $serviceSyncArgs = $service->getSyncArguments($td, $customerRecordIds);

            if ($syncArgs['STARTDATE'] != null && $syncArgs['ENDDATE'] != null) {
                $serviceSyncArgs['STARTDATE'] = DateTime::createFromFormat("Y-m-d", $syncArgs['STARTDATE']);
                $serviceSyncArgs['ENDDATE'] = DateTime::createFromFormat("Y-m-d", $syncArgs['ENDDATE']);
            }
            $documentSyncList = $service->prepareIntacctSyncList($serviceSyncArgs);

            $syncList[] = $documentSyncList;
        }

        if (!empty($syncList)) {
            $list = array_merge(...$syncList);
        }

        return $list;
    }

    /**
     * Returns the list of RECORD# values for all the transaction document types from the request.
     *
     * @param array $syncArgs
     *
     * @return array
     * @throws Exception
     */
    private function retrieveTrxDefinitions($syncArgs)
    {
        $manager = Globals::$g->gManagerFactory->getManager('sodocumentparams');
        $params = ['selects' => ['RECORDNO', 'DOCCLASS', 'DOCID']];
        $params['filters'] = [
            [
                [ 'STATUS', '=', 'active' ],
                [ 'DOCID', 'IN', $syncArgs['TRX-DOCS']['TRX-DOC'] ],
            ],
        ];
        $params['usemst'] = true;
        $trxDefs = $manager->GetList($params);

        return $trxDefs;
    }

    /**
     * Returns the list of RECORD# for all customers in the XML request
     *
     * @param array $syncArgs
     *
     * @return string[]
     * @throws Exception
     */
    private function retrieveCustomerRecordIds($syncArgs)
    {
        static $cachedIds = [];
        $hashCode = parent::syncArgsHashCode($syncArgs);
        if (isset($cachedIds[$hashCode])) {
            return $cachedIds[$hashCode];
        }

        $customerIds = $this->retrieveSubscribedIntaccIds($syncArgs);
        if (!empty($customerIds)) {
            $manager = Globals::$g->gManagerFactory->getManager('customer');
            $params = [
                'selects' => ['RECORDNO'],
                'filters' => [
                    [
                        ['CUSTOMERID', 'IN', $customerIds]
                    ]
                ],
                'usemst' => true
            ];
            $recordIds = array_column($manager->GetList($params), 'RECORDNO');
        } else {
            $recordIds = [];
        }
        $cachedIds[$hashCode] = $recordIds;

        return $recordIds;
    }

    /**
     * Validates data in the xml request.
     *
     * @param array $syncArgs
     *
     * @return bool
     */
    protected function validate($syncArgs) : bool
    {
        $ok = true;

        // First validate accounts for the given collection of the salesforce internal ids for those accounts
        if (isset($syncArgs['KEYS']['KEY'])) {
            $accountIds = $syncArgs['KEYS']['KEY'];
            $ok = self::validateAccounts($accountIds);
        }

        // Validate correctnes of the transaction document types
        if (isset($syncArgs['TRX-DOCS']['TRX-DOC'])) {
            $trxDocuments = $syncArgs['TRX-DOCS']['TRX-DOC'];
            $ok = $ok && SforceMassSyncSalesDocumentService::validateTransactionDocuments($trxDocuments);
        }

        return $ok;
    }

    /**
     * Validates the following requirements:
     * 1. The list of account Ids is not empty
     * 2. All accounts in the list are valid salesforce account Ids
     * 3. All accounts in the list have been synced
     *
     * @param string[] $accountIds
     *
     * @return bool
     */
    public static function validateAccounts($accountIds) : bool
    {
        if (empty($accountIds)) {
            Globals::$g->gErr->addError('SFDC-1189', __METHOD__, "Empty list of accounts");
            return false;
        }

        if (!self::validateExternalIdLengths($accountIds)) {
            Globals::$g->gErr->addError('SFDC-1190', __METHOD__, "Invalid account Id in the list. Expected ids with 18 character length.");
            return false;
        }

        if (!self::validateAccountsInSalesforce($accountIds, $diff)) {
            Globals::$g->gErr->addIAError('SFDC-1191', __METHOD__,
                "Invalid account ID(s) in the list: " .implode(', ', $diff), ["DIFF" => implode(', ', $diff)]);
            return false;
        }

        if (!self::validateAccountLinks($accountIds)) {
            Globals::$g->gErr->addError('SFDC-1192', __METHOD__, "Not all accounts are synced");
            return false;
        }

        return true;
    }

    /**
     * Checks if all account Ids in the passed list represent salesforce accounts synced with Intacct
     *
     * @param string[] $accountIds
     *
     * @return bool
     */
    private static function validateAccountLinks($accountIds) : bool
    {
        /* @var SubscriptionManager $manager */
        $manager = Globals::$g->gManagerFactory->getManager('subscription');
        $params = [
            'selects' => ['EXTERNALID'],
            'filters' => [
                [
                    ['SUBSCRIBER', '=', 'SFORCE'],
                    ['OBJECT', '=', IntacctManagedObject::CUSTOMER],
                    ['EXTERNALID', 'IN', $accountIds]
                ]
            ]
        ];
        $externalIds = array_column($manager->GetList($params), 'EXTERNALID');

        $diff = array_diff($accountIds, $externalIds);
        logToFileInfo(__FILE__ . "  " . __FUNCTION__ . " difference ids : " . json_encode($diff));

        return empty($diff);
    }

    /**
     * Confirms that the passed list represents the valid salesforce account Ids.
     *
     * @param string[] $accountIds
     * @param array $diff
     *
     * @return bool
     * @throws BulkApiException
     */
    private static function validateAccountsInSalesforce($accountIds, &$diff) : bool
    {
        $managedCustomer =
            IntacctManagedObjectFactory::getManagedObject(new IntacctSyncObjectNode(IntacctManagedObject::CUSTOMER));

        return parent::validateObjectsInSalesforce($managedCustomer, $accountIds, $diff);
    }

    /**
     * Removes the first emement from the list (refers to the CUSTOMER).
     *
     * @param IntacctSyncObjectNode[] $syncNodeList
     *
     * @return IntacctSyncObjectNode[]
     */
    public function correctSyncNodeList($syncNodeList)
    {
        $syncNodeList = array_slice($syncNodeList, 1);

        return $syncNodeList;
    }

    /**
     * In addition to the fields corrected in the base class corrects the following fields:
     * - TRX-DOC
     * - DEPENDENT-OBJECT
     *
     * @param array $syncArgs
     *
     */
    protected function correctSyncArgs(&$syncArgs)
    {
        parent::correctSyncArgs($syncArgs);

        if ( isset($syncArgs['TRX-DOCS']['TRX-DOC']) && is_string($syncArgs['TRX-DOCS']['TRX-DOC']) ) {
            $trxDoc = $syncArgs['TRX-DOCS']['TRX-DOC'];
            $syncArgs['TRX-DOCS']['TRX-DOC'] = [ $trxDoc ];
        }

        if ( isset($syncArgs['DEPENDENT-OBJECTS']['DEPENDENT-OBJECT']) && is_string($syncArgs['DEPENDENT-OBJECTS']['DEPENDENT-OBJECT']) ) {
            $dependentObject = $syncArgs['DEPENDENT-OBJECTS']['DEPENDENT-OBJECT'];
            $syncArgs['DEPENDENT-OBJECTS']['DEPENDENT-OBJECT'] = [ $dependentObject ];
        }
    }

    /**
     * The dependent objects might need adjustment in some cases. Like if both CONTRACT
     * and CONTRACTDETAIL are in the list the latter has to be removed.
     *
     * @param array $syncArgs
     *
     * @return string[]
     */
    private function adjustDependentObjects($syncArgs)
    {
        $dependentObjects = [];
        if ( isset($syncArgs['DEPENDENT-OBJECTS']) ) {
            $dependentObjects = $syncArgs['DEPENDENT-OBJECTS']['DEPENDENT-OBJECT'];
            if ( in_array(IntacctManagedObject::CONTRACT, $dependentObjects)
                 && in_array(IntacctManagedObject::CONTRACTDETAIL, $dependentObjects) ) {
                if (($key = array_search(IntacctManagedObject::CONTRACTDETAIL, $dependentObjects)) !== false) {
                    unset($dependentObjects[$key]);
                }
            }
        }

        return $dependentObjects;
    }

}