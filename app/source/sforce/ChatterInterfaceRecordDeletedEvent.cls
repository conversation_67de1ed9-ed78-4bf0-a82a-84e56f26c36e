<?php

use source\core\events\EventsManager;

class ChatterInterfaceRecordDeletedEvent implements Event
{
    public function __construct() {}

    public function execute(string $eventType): bool
    {
        if ($eventType == EventsManager::POST_COMMIT_EVENTS) {
            ChatterDeleterPoller::publish(GetMyCompany(), \ChatterInterface::getRecordsToDelete());
        } else if ($eventType == EventsManager::POST_ROLLBACK_EVENTS) {
            \ChatterInterface::setRecordsToDelete([]);
        }
        return true;
    }

    public function save(string $eventType): ?array
    {
        if (empty(\ChatterInterface::getRecordsToDelete())) {
            return null;
        }
        return [
            "args" => json_encode([
                "recordsToDelete" => \ChatterInterface::getRecordsToDelete()
            ])
        ];
    }

    public function restore(array $args, string $eventType): void
    {
        \ChatterInterface::setRecordsToDelete($args['recordsToDelete']);
    }
}