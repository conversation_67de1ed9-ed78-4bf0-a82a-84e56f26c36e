<?php

/**
 * Class SalesforceSetupSettingsViewerPreview
 * Implements required functions for the preview deployment
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class SalesforceSetupSettingsViewerPreview extends SalesforceSetupSettingsViewer
{
    /**
     * @param array $values
     */
    protected function configureDeploymentSiteCombo(& $values = null)
    {
        // Hide the DEPLOYMENT_SITE field by calling the patent's method
        parent::hideDeploymentSiteCombo();

        // Set the value of the field to Production
        $values['DEPLOYMENT_SITE'] = SforceNamespaceHandler::PACKAGE_MODE_PREVIEW_KEY;
    }

}