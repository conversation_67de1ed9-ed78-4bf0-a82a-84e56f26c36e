<?php

//
// FILE:            Sforcev2PackageInstaller.cls
// AUTHOR:          DWilks
// DESCRIPTION:     A package installer for Salesforce V2 Integration
//
// (C)2000, Intacct Corporation, All Rights Reserved
//
// Intacct Corporation Proprietary Information.
// This document contains trade secret data that belongs to Intacct
// corporation and is protected by the copyright laws. Information herein
// may not be used, copied or disclosed in whole or part without prior
// written consent from Intacct Corporation.
//

class Sforcev2PackageInstaller extends SfdcPackageInstaller
{
    /**
     * @return string
     */
    protected function getServiceId()
    {
        return SfdcPackageInstallerFactory::SERVICE_SFORCEV2;
    }

    /**
     * fetch all the subscribers on the current database instance
     *
     * @param string|null $optCny
     *
     * @return string[]|false  admin subscribers keyed by cny#
     */
    protected function getSubscribersOnDB($optCny)
    {
        $ret = SforceInterface::getSubscribersOnDB($optCny);
        return $ret;
    }

    /**
     * Create and return a session for a subscriber returned from getSubscribersOnDB
     *
     * @param string $subscriber
     *
     * @return AbstractSalesforceSession|null
     */
    protected function getSubscriberSession($subscriber)
    {
        try {
            $ret = SforceInterface::getSubscriberAdminSession($subscriber);
            return $ret;
        } catch (IAException $ignored) {
            return null;
        }
    }
}