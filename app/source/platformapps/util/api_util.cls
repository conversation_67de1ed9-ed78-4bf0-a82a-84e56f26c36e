<?
/**
 * Copyright (c) 2013, Intacct OpenSource Initiative
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted provided that the
 * following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following
 * disclaimer in the documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
 * INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
 * IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * OVERVIEW
 * The general pattern for using this SDK is to first create an instance of api_session and call either
 * connectCredentials or connectSessionId to start an active session with the Intacct Web Services gateway.
 * You will then pass the api_session as an argument in the api_post class methods.  intacctws-php handles all
 * XML serialization and de-serialization and HTTPS transport.
 */

/**
 * class api_util
 * Utility methods for the intacctws-php classes
 */
class api_util
{

    /**
     * Convert a currency amount to a decimal amount
     * @param string $value
     * @internal param String $value amount in currency format
     *
     * @return string amount in decimal format
     */
    public static function currToDecimal($value) 
    {
        $new = "";
        for($x=0; $x < strlen($value); $x++) {
            if (substr($value, $x, 1) != '$' && substr($value, $x, 1) != ',') {
                $new .= substr($value, $x, 1);
            }
        }
        return $new;

    }

    /**
     * Convert a date to the old 2.1 date format
     * @param string $date date in format "m/d/Y"
     * @return string in the old 2.1 xml date format
     */
    public static function dateToOldDate($date) 
    {
        $xml = "";
        $xml .= "<year>" . date("Y", strtotime($date)) . "</year>";
        $xml .= "<month>" . date("m", strtotime($date)) . "</month>";
        $xml .= "<day>" . date("d", strtotime($date)) . "</day>";
        return $xml;
    }

    /**
     * Given a starting date and a number of periods, return an array of dates
     * where the first date is the last day of the month of the first period
     * and every subsequent date is the last day of the following month
     * @param string $date  starting date
     * @param int    $count number of periods to compute
     * @return array
     */
    public static function getRangeOfDates($date, $count) 
    {
        // the first date is the first of the following month                                                                        
        $month = date("m", strtotime($date)) + 1;
        $year = date("Y", strtotime($date));
        if ($month == 13) {
            $month = 1;
            $year++;
        }
        $dateTime = new DateTime($year . "-" . $month . "-01");
        $dateTime->modify("-1 day");

        $dates = array($dateTime->format("Y-m-d"));
        // now, iterate $count - 1 times adding one month to each                                                                    
        for ($x=1; $x < $count; $x++) {
            $dateTime->modify("+1 day");
            $dateTime->modify("+1 month");
            $dateTime->modify("-1 day");
            array_push($dates, $dateTime->format("Y-m-d"));
        }
        return $dates;
    }

    /**                                                                                                                              
     * Convert a php structure to an XML element
     *
     * @param string $key    element name
     * @param array  $values element values
     *
     * @return string xml                                                                                                            
     */
    public static function phpToXml($key, $values) 
    {
        $xml = "";
        if (!is_array($values)) {
            return "<$key>$values</$key>";
        }

        $arrKeys = array_keys($values);
        if (!is_numeric(array_shift($arrKeys))) {
            $xml = "<" . $key . ">";
        }
        foreach($values as $node => $value) {
            if (is_array($value)) {
                if (is_numeric($node)) {
                    $node = $key;
                }
                $xml .= self::phpToXml($node, $value);
            }
            else {
                $xml .= "<" . $node . ">" . htmlspecialchars($value, ENT_COMPAT) . "</" . $node . ">";
            }
        }
        $arrKeys = array_keys($values);
        if (!is_numeric(array_shift($arrKeys))) {
            $xml .= "</" . $key . ">";
        }
        return $xml;
    }

    /**                                                                                                                              
     * Convert a CSV string result into a php array.
     * This work for Intacct API results.  Not a generic method
     *
     * @param string $csv
     *
     * @return array[]  list of row arrays
     */
    public static function csvToPhp($csv) 
    {

        $fp = fopen('php://temp', 'r+');
        fwrite($fp, $csv);

        rewind($fp);

        $table = array();
        // get the header row                                                                                                        
        $header = fgetcsv($fp, 10000, ',', '"');

        // get the rows                                                                                                              
        while (($data = fgetcsv($fp, 10000, ',', '"')) !== false) {
            $row = array();
            foreach($header as $key => $value) {
                $row[$value] = $data[$key];
            }
            $table[] = $row;
        }

        return $table;
    }

    /**
     * Convert a error object into nice text
     *
     * @param SimpleXMLElement $error SimpleXMLElement
     *
     * @return string formatted error message
     */
    public static function xmlErrorToString($error) 
    {

        if (!is_object($error)) {
            return "Malformed error: " . var_export($error, true);
        }

        $error = $error->error[0];
        if (!is_object($error)) {
            return "Malformed error: " . var_export($error, true);
        }

        $errorno = is_object($error->errorno) ? (string)$error->errorno : ' ';
        $description = is_object($error->description) ? (string)$error->description : ' ';
        $description2 = is_object($error->description2) ? (string)$error->description2 : ' ';
        $correction = is_object($error->correction) ? (string)$error->correction : ' ';

        return "$errorno: $description: $description2: $correction";
    }


}
