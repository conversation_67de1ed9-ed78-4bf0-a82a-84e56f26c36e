<?php

//=============================================================================
//
//	FILE:			servefile.phtml
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once('util.inc');
$_file = Request::$r->_file;
$_cny = Request::$r->_cny;
$_bankid = Request::$r->_bankid;

// Request originates from local(tomcat for the pdf report)
if (!ValidateAccess()) {
    if (Request::$r->_sess) {
        Init();
    } else {
        LogToFile('Invalid access of the signature file - ' . Request::$r->_cny);
        dieFL();
    }
}
$_type = Request::$r->_type;
list($cny, $bank, $bankid) = explode('/', $_cny);
ServeImage($cny, $_file, $_type, '', $_cny);
