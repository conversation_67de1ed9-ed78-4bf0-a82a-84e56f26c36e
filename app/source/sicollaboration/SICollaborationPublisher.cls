<?php
/**
 * Class SICollaborationPublisher
 *
 * <AUTHOR> <ujjain.jagathe<PERSON><EMAIL>>
 * @copyright 2023 Intacct Corporation, All Rights Reserved
 */
class SICollaborationPublisher
{
    public const SYNC_USER_PERMISSIONS = 'SYNC_USER_PERMISSIONS';
    public const SYNC_ENTITY_RESTRICTIONS = 'SYNC_ENTITY_RESTRICTIONS';
    public const RESYNC_USER_PERMISSIONS = 'RESYNC_USER_PERMISSIONS';
    public const RESYNC_ENTITY_RESTRICTIONS = 'RESYNC_ENTITY_RESTRICTIONS';
    public const DELETE_USER_PERMISSIONS = 'DELETE_USER_PERMISSIONS';
    public const DELETE_ENTITY_RESTRICTIONS = 'DELETE_ENTITY_RESTRICTIONS';

    /**
     * Generic method for publishing the sync event.
     *
     * @param string[] $body
     * @param string $topic
     * @param int $delay
     * @param string $parentPackageUserId
     * @return bool
     */
    public static function publishCollaborationEvent(array $body = [], string $topic = '', int $delay = 0, string $parentPackageUserId = ''): bool
    {
        $publisher = new ims_publish_1(
            IMS_MODE_NONBLOCKING,
            IMS_PROCESS_REMOTE,
            IMS_MODE_QUEUED);
        if (!empty($parentPackageUserId)) {
            Backend_Init::SetEnvironment(GetMyCompany(), $parentPackageUserId);
        }
        $ok = $publisher->PublishMsg(
            'SICOLLABORATION',
            'INTACCT',
            $topic,
            IMS_PRIORITY_DEFAULT,
            $body,
            [],
            $response,
            $delay
        );

        if (!$ok) {
            logStackTraceBrief();
        }

        return $ok;
    }
}