<?php

class VendorWFPayMethodRule extends Rule
{
    public function __construct(ValidatorResources $validatorResources)
    {
        $this->validatorResources = $validatorResources;
    }
    
    /**
     * if $value === 'WF Domestic ACH' then ACHENABLED or ENABLEWFPM must be true
     * if $value === 'WF USD Wire' then WIREENABLED or ENABLEWFPM must be true
     * if $value === 'WF Check' then CHECKENABLED or ENABLEWFPM must be true
     *
     * @param mixed $value
     *
     * @return bool
     */
    public function check($value) : bool
    {
        if ( $value === '' ) {
            return true;
        }
        
        // csvimport_vendorach.cls
        if (in_array($value, [ PaymentUtils::WFCHECK_PAYMENTMETHOD,
                               PaymentUtils::WFWIRE_PAYMENTMETHOD,
                               PaymentUtils::WFACH_PAYMENTMETHOD ])) {
            return $this->validateWFPayMethod($value);
        }
        
        return true;
    }
    
    /**
     * ValidateWFPMConfig() - VendorManager.cls
     *
     * @param $value
     *
     * @return bool
     */
    public function validateWFPayMethod($value) : bool
    {
        $preferences = $this->validatorResources->GetModulePreferences(Globals::$g->kOPYMTSid);
        
        $enableWFPM = ( $preferences['ENABLEWFPM'] ?? null) ?: '';
        $achEnabled = ( $preferences['ACHENABLED'] ?? null) ?: '';
        $wireEnabled = ( $preferences['WIREENABLED'] ?? null) ?: '';
        $checkEnabled = ( $preferences['CHECKENABLED'] ?? null) ?: '';
        
        if ($enableWFPM != 'T'
            || ( !$achEnabled && $value === PaymentUtils::WFACH_PAYMENTMETHOD )
            || ( !$wireEnabled && $value === PaymentUtils::WFWIRE_PAYMENTMETHOD )
            || ( !$checkEnabled && $value === PaymentUtils::WFCHECK_PAYMENTMETHOD )
        ) {
            $this->message = new ErrorMessage(
                sprintf("The Preferred Payment method %s is not enabled in this company", $value),
                'DM-0059',
                ['PAY_METHOD' => $value]
            );
            return false;
        }
    
        return true;
    }
}
