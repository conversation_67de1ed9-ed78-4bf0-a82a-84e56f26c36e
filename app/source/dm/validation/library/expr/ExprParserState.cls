<?php
/**
 * Copyright (c) 2023 Sage Intacct
 */

class ExprParserState
{
    private string $name;            // Name of state.

    private array $transitions;      // Transitions to next state.

    /**
     * @param string $name        Name of transition.
     * @param array  $transitions Transitions for this state.
     */
    public function __construct(string $name, array $transitions)
    {
        $this->name = $name;
        $this->transitions = $transitions;
    }

    /**
     * @return string Name of transition.
     */
    public function getName() : string
    {
        return $this->name;
    }

    /**
     * Initialize a state (done only once).
     *
     * @param array $stateMap Map of state names to state objects.
     */
    public function init(array $stateMap)
    {
        foreach ($this->transitions as $nextTrans) {
            $nextTrans->init($stateMap);
        }
    }

    /**
     * @return array Defined transitions for this state.
     */
    public function getTransitions() : array
    {
        return $this->transitions;
    }

    /**
     * Perform a transition based on the input token.
     *
     * @param ExprParserToken $token           Input token.
     * @param array           &$stateStack     Stack of states by level.
     * @param int             $stateTOS        Current top of state stack.
     * @param array           &$codeStack      Code stack.
     * @param ?array          &$newCodeStack   New code stack.
     * @param int             &$recurseControl Controls levels of recursion.
     *
     * @return bool|ErrorMessage True means success, otherwise error object.
     */
    public function doTransition(ExprParserToken $token, array &$stateStack, int &$stateTOS,
     array &$codeStack, ?array &$newCodeStack, int &$recurseControl) : bool|ErrorMessage
    {
        foreach ($this->transitions as $nextTrans) {
            $toState = $nextTrans->matchesToken($token->getType());
            if ($toState !== null) {
                $toTrans = $nextTrans;
                $ret = $toTrans->executeEnter($token, $stateStack, $stateTOS, $toState, $codeStack,
                 $newCodeStack, $recurseControl);
                return $ret;
            }
        }
        $msg = "Unexpected token $token (state=$this->name), state stack:\n" . ppS($stateStack);
        return new ErrorMessage($msg, 'DM-0098');
    }

    /**
     * @return string
     */
    public function __toString()
    {
        $str = "STATE $this->name, transitions:\n";
        foreach ($this->transitions as $nextTrans) {
            $str .= "    " . $nextTrans . "\n";
        }
        return $str;
    }
}
