<?php

/**
 * NIS_EnvironmentBuilder.cls
 * File description
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 */
class NIS_SpaceBuilder
{

    protected ?string            $spaceName = null;
    private FF_MODEL_Environment $environment;
    private FlatFileRestClient   $restClient;

    public function __construct(FF_MODEL_Environment $environment, FlatFileRestClient $restClient)
    {
        if ( is_null($this->spaceName) ) {
            $this->spaceName = GetMyCompanyTitle() . ' [ ' . GetMyLogin() . ' ]';
        }

        $this->restClient = $restClient;
        $this->environment = $environment;
    }

    public function build() : FF_MODEL_Space|FF_MODEL_Error
    {
        $space = $this->restClient->getSpace($this->spaceName, $this->environment->getId());
        $localeParts = explode('_', NIS_LocaleHelper::getPrefferedLocale());

        $spaceConfig = [
            'name'             => $this->spaceName,
            'environmentId'    => $this->environment->getId(),
            'metadata'         => FF_MODEL_Theme::toArray(),
            'languageOverride' => $localeParts[0],
            'translationsPath' => BaseURL_NoPort() . '../resources/thirdparty/flat-file/translations/' . $localeParts[0]
                                  . '/translation.json',
        ];

        return is_null($space) ? $this->restClient->createSpace($spaceConfig)
            : $this->restClient->updateSpace($space->getId(), $spaceConfig);
    }
}