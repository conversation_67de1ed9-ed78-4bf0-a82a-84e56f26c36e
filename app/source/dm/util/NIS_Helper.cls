<?php



/**
 * NIS_Helper.cls
 * Colelction of static methods to help with common tasks
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 */
class NIS_Helper
{
    use NIS_ConfigTrait;

    const API_VERSION = "v1";
    const API_VERSION_NO_V = "1";
    const API_INTERNAL_VERSION = "v2i";

    public static function getAPIVersion(): string
    {
        return self::API_VERSION;
    }

    public static function getAPIVersionNoV(): string
    {
        return self::API_VERSION_NO_V;
    }

    public static function getAPIInternalVersion(): string
    {
        return self::API_INTERNAL_VERSION;
    }

    public static function getApiBaseURL() : string
    {
        return ( new self )->getConfiguration('PLATFORM_API_URL', self::NIS_SECTION) . APIConstants::URI_SEPARATOR . 'v1';
    }

    /**
     * @return string
     */
    public static function getClientId() : string
    {
        return TwoWayDecryptWithKey(( new self )->getConfiguration('PLATFORM_CLIENT_ID', self::NIS_SECTION), 'IA_INIT');
    }

    /**
     * @return string
     */
    public static function getClientSecret() : string
    {
        return TwoWayDecryptWithKey(( new self )->getConfiguration('PLATFORM_CLIENT_SECRET', self::NIS_SECTION), 'IA_INIT');
    }

    /**
     * @throws Exception
     */
    public static function getHTMLHeaderScripts() : string
    {
        $scripts = '';

        if ( ! FeatureConfigManagerFactory::getInstance()
                                          ->isFeatureEnabled("DM_IMPORT_SERVICE_DISABLE") ) {
            $csp = IACspPolicyManager::getCspPolicy();

            if ( $csp ) {
                $domains = [ 'platform.flatfile.com', 'platform.ca.flatfile.com', 'platform.uk.flatfile.com',
                             'platform.au.flatfile.com' ,'sage.us.flatfile.com', 'sage.ca.flatfile.com', 'sage.eu.flatfile.com', 'sage.au.flatfile.com' ,'sage.stage.flatfile.com' ];

                foreach ( $domains as $domain ) {
                    $csp->addAllowed(CspPolicy::POLICY_CONNECT, 'https://' . $domain);
                    $csp->addAllowed(CspPolicy::POLICY_FRAME, 'https://' . $domain);
                }

                $csp->addAllowed(CspPolicy::POLICY_CONNECT, 'https://*.pndsn.com');
                $csp->addAllowed(CspPolicy::POLICY_CONNECT, 'https://api.x.flatfile.com');
                $csp->addAllowed(CspPolicy::POLICY_FRAME, 'https://spaces.flatfile.com');
            }

            $scripts = '<script>const NIS = {}; NIS.BACKEND_URL =  "' . GoUrl('import_wizard_flat_file.phtml') . '"</script>';

            if ( self::isNISImportServiceEnabled() ) {

                $scripts .= '<script>const platformApiUrl = "'.GetValueForIACFGProperty('NIS')['PLATFORM_API_URL'].'"</script>';

                foreach ( [ "../resources/thirdparty/flat-file/nis-client-bundle.min.js",
                            "../resources/qx/js/qxdialog.js" ] as $path ) {
                    $scripts .= '<script src="' . URLReplace::replaceRelativeURL($path) . '"></script>';
                }
            }
        }

        return $scripts;
    }

    public static function isNISImportServiceEnabled() : bool
    {
        $fcm = FeatureConfigManagerFactory::getInstance();

        return $fcm->isFeatureEnabled("DM_IMPORT_SERVICE_GLOBAL")
               && ! $fcm->isFeatureEnabled("DM_IMPORT_SERVICE_DISABLE");
    }

    /**
     * return decrypted client id for import service to make next gen api calls
     *
     * @return string
     */
    public static function getImportClientId() : string
    {
        $decryptedClientId =
            TwoWayDecryptWithKey((new self)->getConfiguration('IMPORT_CLIENT_ID', self::NIS_SECTION), 'IA_INIT');

        if ( $decryptedClientId === false ) {
            logToFileError('Failed to decrypt the import service client id');
            throw new Exception('Failed to decrypt the import service client id');
        }

        return $decryptedClientId;
    }

    /**
     * return decrypted client secret for import service. This is not used for token generation and only used for
     * authenticating the webhook callback
     *
     * @return string
     */
    public static function getImportClientSecret() : string
    {
        $decryptedSecret =
            TwoWayDecryptWithKey((new self)->getConfiguration('IMPORT_CLIENT_SECRET', self::NIS_SECTION), 'IA_INIT');

        if ( $decryptedSecret === false ) {
            logToFileError('Failed to decrypt the import client secret');
            throw new Exception('Failed to decrypt the import client secret');
        }

        return $decryptedSecret;
    }

    /**
     * return a generic error message with a support id
     *
     * @return string
     */
    public static function getSupportMessage() : string
    {
        $supportId = Globals::$g->perfdata->getRequestID();

        return I18N::getSingleToken('IA.IMPORT_ERROR_SUPPORT_ID', [
            [ "name" => "SUPPORT_ID", "value" => $supportId ],
        ]);
    }

    /**
     *  custom properties for the dialog
     *
     * @return array
     */
    public static function getCustomProperties() : array
    {
        return [
            'modalsize'      => 'small',
            'noIframe'       => false,
            'noResize'       => true,
            'dialogButtons'  => [],
            'footerCloseBtn' => true,
        ];
    }
}
