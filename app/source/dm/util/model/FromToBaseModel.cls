<?php

/**
 * FromToBaseModel
 * Generic class
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
class FromToBaseModel
{

    /**
     * @var array
     */
    protected array $customSetters = [];
    /**
     * @var array
     */
    protected array $customGetters = [];

    /**
     * Define the order of class properties to be parsed for toArray()
     *
     * It may help when you want to add extra info on the object based on some computation done in the getter
     *
     * @var array
     */
    protected array $exportProperties = [];

    /**
     * @param string $json
     *
     * @return bool|FF_MODEL_Job
     * @throws JsonException
     */
    public static function fromJSON(string $json) : mixed
    {
        $jobData = json_decode($json, true, 512, JSON_THROW_ON_ERROR);

        return self::fromArray($jobData);
    }

    /**
     * @return string
     * @throws JsonException
     */
    public function toJSON() : string
    {
        return json_encode($this->toArray(), JSON_THROW_ON_ERROR);
    }

    /**
     * @param array $array
     *
     * @return mixed
     * @throws Exception
     */
    public static function fromArray(array $array = []) : mixed
    {
        $subject = self::getInstance();

        foreach ( $array as $prop => $value ) {
            try {
                if ( isset($subject->customSetters[$prop]) ) {
                    $setter = $subject->customSetters[$prop];
                    $subject->$setter($value);
                } else {
                    $setter = 'set' . ucfirst($prop);

                    if ( method_exists($subject, $setter) ) {
                        $subject->$setter($value);
                    } else {
                        addLog("Import Error: No setter found for property $prop on class " . get_class($subject),
                               LogManager::ERROR, true);
                    }
                }
            } catch (\Throwable $e) {
                addLog("Import Error: Exception calling setter for property $prop on class " . get_class($subject)
                       . ": " . $e->getMessage(),
                       LogManager::ERROR, true);
            }
        }

        return $subject;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function toArray() : array
    {
        $array = [];
        $properties = get_object_vars($this);

        if($this->exportProperties) {
            $properties = array_merge(array_combine($this->exportProperties, array_fill(0, count($this->exportProperties), null)), get_object_vars($this));
        }

        foreach ( $properties as $prop => $value ) {
            if ( !in_array($prop, [ 'customSetters', 'customGetters', 'exportProperties' ]) && ! is_null($value) ) {
                if ( isset($this->customGetters[$prop]) ) {
                    $__getter = $this->customGetters[$prop];
                    $__value = $this->$__getter();

                    if($__value !== null) {
                        $array[$prop] = $__value;
                    }
                    continue;
                }

                $getter = 'get' . ucfirst($prop);

                if ( method_exists($this, $getter) ) {
                    $value = $this->$getter();

                    if ($value !== null) {
                        $array[$prop] = $value;
                    }

                    continue;
                } else {
                    throw new Exception("No getter found for property $prop on class " . get_class($this));
                }
            }
        }

        return $array;
    }

    /**
     * @return self
     */
    public static function getInstance() : self
    {
        $lateBindingClassName = static::class;

        return new $lateBindingClassName();
    }

    /**
     * @param string $name
     * @param mixed  $value
     *
     * @return void
     * @throws Exception
     */
    public function __set(string $name, mixed $value)
    {
        $setter = 'set' . ucfirst($name);

        if ( method_exists($this, $name) ) {
            $this->$setter($value);
        } else if ( isset($this->customSetters[$name]) ) {
            $this->customSetters[$name]($value);
        } else {
            throw new Exception("No setter found for property $name on class " . get_class($this));
        }
    }

    /**
     * @param string $name
     *
     * @return mixed
     * @throws Exception
     */
    public function __get(string $name) : mixed
    {
        $getter = 'get' . ucfirst($name);
        if ( method_exists($this, $getter) ) {
            return $this->$getter();
        } else if ( isset($this->customGetters[$name]) ) {
            return $this->customGetters[$name]();
        } else {
            throw new Exception("No getter for property $name found on class " . get_class($this));
        }
    }
}
