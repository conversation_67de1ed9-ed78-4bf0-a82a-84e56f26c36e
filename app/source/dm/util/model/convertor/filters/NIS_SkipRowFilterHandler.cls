<?php

/**
 * NIS_SkipRowFilterHandler.cls
 * Filter properties having  Skip rows
 *
 * In case of derived sheets like header and details, we don't need to add skip rows
 *
 * <AUTHOR> Sherpa
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
class NIS_SkipRowFilterHandler extends NIS_AbstractModelFilterHandler
{

    /**
     * @param mixed $key
     * @param array $data
     *
     * @return bool
     */
    protected function shouldBreak(mixed $key, array $data) : bool
    {
        return $key === NIS_NgToBlueprintConvertor::$SKIP_ROW_FIELD;
    }
}