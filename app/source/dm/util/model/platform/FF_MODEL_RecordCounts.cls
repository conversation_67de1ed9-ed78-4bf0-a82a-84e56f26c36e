<?php

/**
 * FF_MODEL_CountRecords.cls
 * File description
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 */
class FF_MODEL_RecordCounts extends FromToBaseModel
{

    /**
     * @var int
     */
    protected int $total = 0;
    /**
     * @var int
     */
    protected int $valid = 0;
    /**
     * @var int
     */
    protected int $error = 0;
    /**
     * @var FF_MODEL_RecordCounts[]
     */
    protected array $byField = [];

    /**
     * @var array|null
     */
    protected ?array $errorsByField = null;

    /**
     * @var int|null
     */
    protected ?int $countsComputedAt = null;

    /**
     *
     */
    public function __construct()
    {
        $this->customGetters = [
            'byField' => '_getByField',
        ];

        $this->customSetters = [
            'byField' => '_setByField',
        ];
    }

    /**
     * @return int
     */
    public function getTotal() : int
    {
        return $this->total;
    }

    /**
     * @param int $total
     *
     * @return FF_MODEL_RecordCounts
     */
    public function setTotal(int $total) : FF_MODEL_RecordCounts
    {
        $this->total = $total;

        return $this;
    }

    /**
     * @return int
     */
    public function getValid() : int
    {
        return $this->valid;
    }

    /**
     * @param int $valid
     *
     * @return FF_MODEL_RecordCounts
     */
    public function setValid(int $valid) : FF_MODEL_RecordCounts
    {
        $this->valid = $valid;

        return $this;
    }

    /**
     * @return int
     */
    public function getError() : int
    {
        return $this->error;
    }

    /**
     * @param int $error
     *
     * @return FF_MODEL_RecordCounts
     */
    public function setError(int $error) : FF_MODEL_RecordCounts
    {
        $this->error = $error;

        return $this;
    }

    /**
     * @return FF_MODEL_RecordCounts[]
     */
    public function getByField() : array
    {
        return $this->byField;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function _getByField() : array
    {
        $byField = [];
        foreach ( $this->byField as $field => $recordCounts ) {
            $byField[$field] = $recordCounts->toArray();
        }

        return $byField;
    }

    /**
     * @param array $byField
     *
     * @return FF_MODEL_RecordCounts
     */
    public function setByField(array $byField) : FF_MODEL_RecordCounts
    {
        $this->byField = $byField;

        return $this;
    }

    /**
     * @param array $byField
     *
     * @return $this
     * @throws Exception
     */
    public function _setByField(array $byField) : FF_MODEL_RecordCounts
    {
        foreach ( $byField as $field => $recordCounts ) {
            $this->byField[$field] = self::fromArray($recordCounts);
        }

        return $this;
    }

    /**
     * @return array|null
     */
    public function getErrorsByField() : ?array
    {
        return $this->errorsByField;
    }

    /**
     * @param array|null $errorsByField
     *
     * @return FF_MODEL_RecordCounts
     */
    public function setErrorsByField(?array $errorsByField) : FF_MODEL_RecordCounts
    {
        $this->errorsByField = $errorsByField;

        return $this;
    }


    /**
     * @return int|null
     */
    public function getCountsComputedAt(): ?int
    {
        return $this->countsComputedAt;
    }

    /**
     * @param int|null $countsComputedAt
     *
     * @return FF_MODEL_RecordCounts
     */
    public function setCountsComputedAt(?int $countsComputedAt): FF_MODEL_RecordCounts
    {
        $this->countsComputedAt = $countsComputedAt;

        return $this;
    }

}