<?php

/**
 * FF_MODEL_Record.cls
 * File description
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 */
class FF_MODEL_RecordConfig extends FromToBaseModel
{

    /**
     * @var bool|null
     */
    public ?bool  $readonly = null;
    /**
     * @var array|null
     */
    public ?array $fields = null;

    /**
     * @return bool|null
     */
    public function getReadonly() : ?bool
    {
        return $this->readonly;
    }

    /**
     * @param bool|null $readonly
     *
     * @return FF_MODEL_RecordConfig
     */
    public function setReadonly(?bool $readonly) : FF_MODEL_RecordConfig
    {
        $this->readonly = $readonly;

        return $this;
    }

    /**
     * @return array|null
     */
    public function getFields() : ?array
    {
        return $this->fields;
    }

    /**
     * @return array|null
     */
    public function addReadOnlyField(string $field) : ?array
    {
        return $this->fields[$field] = ['readonly' => true];
    }


    /**
     * @param array|null $fields
     *
     * @return FF_MODEL_RecordConfig
     */
    public function setFields(?array $fields) : FF_MODEL_RecordConfig
    {
        $this->fields = $fields;

        return $this;
    }
}