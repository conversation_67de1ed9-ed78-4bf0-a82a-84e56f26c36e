<?php



/**
 * FF_MODEL_BaseField
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
class FF_MODEL_InputField extends FromToBaseModel
{

    /**
     * @var string
     */
    protected string $key;
    /**
     * @var string
     */
    protected string $label;
    /**
     * @var string
     */
    protected string $type;
    /**
     * @var string|null
     */
    protected ?string $description;
    /**
     * @var mixed
     */
    protected mixed $defaultValue;
    /**
     * @var FF_ENUM_ConstraintType[]
     */
    protected array $constraints;

    /**
     *
     */
    public function __construct()
    {
        $this->customSetters = [
            'config'      => '_setConfig',
            'constraints' => '_setConstraints',
        ];

        $this->customGetters = [
            'config'      => '_getConfig',
            'constraints' => '_getConstraints',
        ];
    }

    /**
     * @return string
     */
    public function getKey() : string
    {
        return $this->key;
    }

    /**
     * @param string $key
     *
     * @return FF_MODEL_InputField
     */
    public function setKey(string $key) : FF_MODEL_InputField
    {
        $this->key = $key;

        return $this;
    }

    /**
     * @return string
     */
    public function getLabel() : string
    {
        return $this->label;
    }

    /**
     * @param string $label
     *
     * @return FF_MODEL_InputField
     */
    public function setLabel(string $label) : FF_MODEL_InputField
    {
        $this->label = $label;

        return $this;
    }

    /**
     * @return string
     */
    public function getType() : string
    {
        return $this->type;
    }

    /**
     * @param string $type
     *
     * @return FF_MODEL_InputField
     */
    public function setType(string $type) : FF_MODEL_InputField
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getDescription() : ?string
    {
        return $this->description;
    }

    /**
     * @param string|null $description
     *
     * @return FF_MODEL_InputField
     */
    public function setDescription(?string $description) : FF_MODEL_InputField
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDefaultValue() : mixed
    {
        return $this->defaultValue;
    }

    /**
     * @param mixed $defaultValue
     *
     * @return FF_MODEL_InputField
     */
    public function setDefaultValue(mixed $defaultValue) : FF_MODEL_InputField
    {
        $this->defaultValue = $defaultValue;

        return $this;
    }

    /**
     * @return array
     */
    public function getConstraints() : array
    {
        return $this->constraints;
    }

    /**
     * @return array
     */
    public function _getConstraints() : array
    {
        $constraints = [];
        foreach ( $this->constraints as $constraint ) {
            $constraints[] = [ 'type' => $constraint->value ];
        }

        return $constraints;
    }

    /**
     * @param array $constraints
     *
     * @return FF_MODEL_InputField
     */
    public function setConstraints(array $constraints) : FF_MODEL_InputField
    {
        $this->constraints = $constraints;

        return $this;
    }

    /**
     * @param array $constraints
     *
     * @return $this
     * @throws Exception
     */
    public function _setConstraints(array $constraints) : FF_MODEL_InputField
    {
        $this->constraints = [];
        foreach ( $constraints as $constraint ) {
            if ( $constraint !== FF_ENUM_ConstraintType::REQUIRED ) {
                throw new Exception('Invalid constraint type');
            }

            $this->constraints[] = FF_ENUM_ConstraintType::from($constraint);
        }

        return $this;
    }
}