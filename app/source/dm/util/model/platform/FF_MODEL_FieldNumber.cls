<?php

/**
 *
 * FF_MODEL_FieldNumber.cls
 * Model map
 *
 * <AUTHOR> [<EMAIL>]
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
class FF_MODEL_FieldNumber extends FF_MODEL_Field
{

    /**
     * @var bool|null
     */
    public ?bool $isArray = null;
    /**
     * @var FF_MODEL_FieldNumberConfig|null
     */
    public ?FF_MODEL_FieldNumberConfig $config = null;

    /**
     *
     */
    public function __construct()
    {
        parent::__construct();
        $this->type = FF_ENUM_FieldType::NUMBER;

        $this->customSetters['config'] = '_setConfig';
        $this->customGetters['config'] = '_getConfig';
    }

    /**
     * @return bool|null
     */
    public function getIsArray() : ?bool
    {
        return $this->isArray;
    }

    /**
     * @param bool|null $isArray
     *
     * @return FF_MODEL_FieldNumber
     */
    public function setIsArray(?bool $isArray) : FF_MODEL_FieldNumber
    {
        $this->isArray = $isArray;

        return $this;
    }

    /**
     * @return FF_MODEL_FieldNumberConfig|null
     */
    public function getConfig() : ?FF_MODEL_FieldNumberConfig
    {
        return $this->config;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function _getConfig() : array
    {
        return $this->config->toArray();
    }

    /**
     * @param FF_MODEL_FieldNumberConfig|null $config
     *
     * @return FF_MODEL_FieldNumber
     */
    public function setConfig(?FF_MODEL_FieldNumberConfig $config) : FF_MODEL_FieldNumber
    {
        $this->config = $config;

        return $this;
    }

    /**
     * @param array $config
     *
     * @return void
     * @throws Exception
     */
    public function _setConfig(array $config) : void
    {
        $this->config = FF_MODEL_FieldNumberConfig::fromArray($config);
    }
}