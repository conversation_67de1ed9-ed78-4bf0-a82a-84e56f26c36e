<?php

/**
 * Manager class for BVASetupManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 Sage Intacct Corporation, All Rights Reserved
 */
class BVASetupManager extends ModuleSetupManager
{
    const BUDGET_INSIGHT_SCHEDULEDOPERATION_NAME = 'GL Budget Insight Notification';
    const DAILY = 'Daily';
    const WEEKLY = 'Weekly';
    const MONTHLY = 'End of the month';

    /**
     * @param $params
     */
    public function __construct($params = array())
    {
        $params['moduleKey'] = Globals::$g->kBVAid;
        parent::__construct($params);
    }

    /******************************************** GMS Chat Context ****************************************************/

    public function isAnalyseByLocMgrEnabled($setupValues): bool
    {
        return $setupValues['ANALYSE.BYLOCATION'] === 'true';
    }

    public function isAnalyseBYDeptMgrEnabled($setupValues): bool
    {
        return $setupValues['ANALYSE.BYDEPARTMENT'] === 'true';
    }

    /**
     * @return array
     */
    public function getBvaChatContext(): array
    {
        if(!BVASetupManager::isCopilotEnabledForBVA()) {
            return [];
        }

        $userType = null;

        $userLoginId = GetMyLogin();
        $gManagerFactory = Globals::$g->gManagerFactory;

        $bvaSetupResult = $this->GetList(array(
            'selects' => array('CONTROLLERUSERGROUP.KEY', 'CONTROLLERUSER.ID',
                'ANALYSE.BYLOCATION', 'ANALYSE.BYDEPARTMENT')
        ));

        if (isset($bvaSetupResult[0])) {
            $isControllerUser = false;

            /* Check if the user is a controller user (Individual controller user) */
            if (isset($bvaSetupResult[0]['CONTROLLERUSER.ID'])) {
                $bvaControllerUserId = $bvaSetupResult[0]['CONTROLLERUSER.ID'];
                if ($bvaControllerUserId === $userLoginId) {
                    $isControllerUser = true;
                }
            }

            /* Check if the user is a controller user (Group controller user) */
            if (!$isControllerUser && (isset($bvaSetupResult[0]['CONTROLLERUSERGROUP.KEY']))) {
                $groupUsersMgr = $gManagerFactory->getManager("groupusers");
                $groupUsersData = $groupUsersMgr->GetList(
                    array(
                        'selects' => array('USERLOGINID'),
                        'filters' => array(array(
                            array('GROUPKEY', '=', $bvaSetupResult[0]['CONTROLLERUSERGROUP.KEY']),
                            array('USERLOGINID', '=', $userLoginId)
                        )),
                    )
                );
                $isControllerUser = isset($groupUsersData[0]['USERLOGINID']);
            }

            if ($isControllerUser) {
                $userType = BVAUser::USER_TYPE_CONTROLLER;
            } else {
                $isAnalyseByLocMgrEnbld = $this->isAnalyseByLocMgrEnabled($bvaSetupResult[0]);
                $isAnalyseByDeptMgrEnbld = $this->isAnalyseBYDeptMgrEnabled($bvaSetupResult[0]);

                if ($isAnalyseByLocMgrEnbld || $isAnalyseByDeptMgrEnbld) {
                    $filter = array(
                        'selects' => array('CONTACTINFO.CONTACTNAME'),
                        'filters' => array(array(
                            array('LOGINID', '=', $userLoginId))
                        ),
                    );

                    $userInfo = Globals::$g->gManagerFactory->getManager("userinfo")->GetList($filter);

                    if (isset($userInfo[0]['CONTACTINFO.CONTACTNAME'])) {
                        $filter = array(
                            'selects' => array('COUNT(1) as CNT'),
                            'filters' => array(array(
                                array('SUPERVISORNAME', '=', $userInfo[0]['CONTACTINFO.CONTACTNAME']),
                                array('STATUS', '=', 'active'),
                            ))
                        );

                        $islocManager = false;
                        $isDeptManager = false;

                        if ($isAnalyseByLocMgrEnbld) {
                            $locResults = Globals::$g->gManagerFactory->getManager("location")->GetList($filter);
                            $islocManager = ($locResults[0]['CNT'] ?? '0') !== '0';
                        }

                        if ($isAnalyseByDeptMgrEnbld) {
                            $deptResults = Globals::$g->gManagerFactory->getManager("department")->GetList($filter);
                            $isDeptManager = ($deptResults[0]['CNT'] ?? '0') !== '0';
                        }

                        if ($islocManager && $isDeptManager) {
                            $userType = BVAUser::USER_TYPE_MANAGER;
                        } else if ($islocManager) {
                            $userType = BVAUser::USER_TYPE_MANAGER_LOCATION;
                        } else if ($isDeptManager) {
                            $userType = BVAUser::USER_TYPE_MANAGER_DEPARTMENT;
                        }

                    }
                }
            }
        }

        return ($userType === null) ? [] : [
            "kind" => "ytd_bva",
            "bva" => ["user_type" => $userType]
        ];
    }

    public static function getChatContextLocale(): string
    {
        $operatingCountry = Profile::getCompanyCacheProperty('COMPANYPREF', 'OPCOUNTRY');
        $userLocale = 'en-US';
        if (isset($operatingCountry)) {
            $userLocale = $operatingCountry === 'United Kingdom' ? 'en-GB' : 'en-US';
        }
        return $userLocale;
    }

    /********************************************* For Scheduler ******************************************************/
    private function CreateSchedule(&$values, &$schedValues)
    {
        // Prepare the schedule data from the recurring transaction data
        $ok = $this->PrepareScheduleValues($values, $schedValues);
        // Add a new schedule
        $gManagerFactory = Globals::$g->gManagerFactory;
        $schedMgr = $gManagerFactory->getManager('schedule');
        $ok = $ok && $schedMgr->add($schedValues);
        // Throw an error if the Add fails else add the schedule values to the data array
        if ($ok) {
            $schPath = 'SCHVALUES';
            $values[$schPath] = $schedValues;
        } else {
            $gErr = Globals::$g->gErr;
            $gErr->addIAError(
                'GL-0912',
                __FILE__ . ':' . __LINE__,
                'Schedule record could not be created for ' . $schedValues['DESCRIPTION'],
                ['DESCRIPTION' => $schedValues['DESCRIPTION']]
            );
        }

        return $ok;
    }

    /**
     * @return bool
     */
    public static function isBVAEnabled(): bool
    {
        static $ret = null;
        if ($ret === null) {
            try {
                $ret = FeatureConfigManagerFactory::getInstance()->isFeatureEnabled("ENABLE_COPILOT")
                    && IsModuleIdInstalled(Globals::$g->kBVAid)
                    && (GetPreferenceForProperty(Globals::$g->kAIMLid, "BUDGET_INSIGHT_ENABLED") === 'T')
                    && !IsMCMESubscribed();
            } catch (Exception $e) {
                LogToFile($e->getMessage());
                $ret = false;
            }
        }
        return $ret;
    }

    public static function isCopilotEnabledForBVA(): bool
    {
        static $ret = null;
        if ($ret === null) {
            try {
                $ret = self::isBVAEnabled()
                    && IsOperationAllowed(GetOperationId("bva/activities/budgetvariance"));
            } catch (Exception $e) {
                LogToFile($e->getMessage());
                $ret = false;
            }
        }
        return $ret;
    }


    /**
     * Get the company week start day number
     *
     * @return int the company week start day number
     */
    private function computeWeekStart()
    {
        // Get the week start day number from the preferences
        $weekStartDay = GetMyWeekStart() - 1;

        // If the week start is set to beginning of the year we reset it to Monday
        if ($weekStartDay == 8) {
            $weekStartDay = 1;
        }

        return $weekStartDay;
    }


    private function PrepareScheduleValues($values, &$schedValues)
    {
        $schedValues['CNY#'] = GetMyCompany();
        $schedValues['USERNO'] = 'system';
        $schedValues['WHENCREATED'] = GetCurrentDate();
        $schedValues['STATUS'] = 'active';
        $schedValues['EXECCOUNT'] = 0;

        // Set name and description of the schedule
        $schedValues['NAME'] = self::BUDGET_INSIGHT_SCHEDULEDOPERATION_NAME;
        $schedValues['DESCRIPTION'] = 'GL Budget Insight Notification Schedule';

        $schedValues['STARTDATE'] = AddDays(GetCurrentDate(), 0);

        $values['NOTIFICATIONFREQUENCY'] = $values['NOTIFICATIONFREQUENCY'] ?? self::DAILY;
        if ($values['NOTIFICATIONFREQUENCY'] === self::DAILY) {
            $schedValues['REPEATBY'] = 'Day';
        } else if ($values['NOTIFICATIONFREQUENCY'] === self::WEEKLY) {
            $companyStartDay = $this->computeWeekStart() + 1;
            $today = date('w', strtotime(GetCurrentDate())) + 1;
            if ($today < $companyStartDay) {
                $addDays = $companyStartDay - $today;
            } else {
                $addDays = ($companyStartDay + 7) - $today;
            }
            $schedValues['STARTDATE'] = AddDays(GetCurrentDate(), $addDays);
            $schedValues['REPEATBY'] = 'Week';
        } else if ($values['NOTIFICATIONFREQUENCY'] === self::MONTHLY) {
            $schedValues['STARTDATE'] = date('m/t/Y');
            $schedValues['REPEATBY'] = 'EndOfMonth';
        }
        $schedValues['NEXTEXECDATE'] = $schedValues['STARTDATE'];
        $schedValues['LASTEXECDATE'] = $values['LASTEXECDATE'];

        // Scheduled to run by daily script
        $schedValues['EXECTYPE'] = 'Automatic';
        // If the start date is in the past let's set up the scheduler to run in non-blocking mode immediately
        if (SysDateCompare($values['STARTDATE'], GetCurrentDate()) <= 0) {
            $schedValues['EXECTYPE'] = 'Immediate';
        }
        return true;
    }

    private function CreateOperation(&$values, $schedValues)
    {
        // Prepare the operation data from the schedule data
        $operValues = array();
        $this->PrepareOperationValues($values, $schedValues, $operValues);

        // Add the operation
        $gManagerFactory = Globals::$g->gManagerFactory;
        $operMgr = $gManagerFactory->getManager('operation');
        $ok = $operMgr->add($operValues);

        // Throw an error if the Add fails else add the operation values to the data array
        if ($ok) {
            $operPath = 'OPERVALUES';
            $values[$operPath] = $operValues;
        } else {
            $gErr = Globals::$g->gErr;
            $gErr->addIAError(
                'GL-1813',
                __FILE__ . ':' . __LINE__,
                'Operation record could not be created for ' . $operValues['DESCRIPTION'],
                ['DESCRIPTION' => $operValues['DESCRIPTION']]
            );
        }

        return $ok;
    }

    private function PrepareOperationValues($values, $schedValues, &$operValues)
    {
        // Fill up operation table values
        $operValues['CNY#'] = $schedValues['CNY#'];
        $operValues['NAME'] = $schedValues['NAME'];
        $operValues['STATUS'] = 'active';
        // Same name is used as schedule
        $operValues['DESCRIPTION'] = $schedValues['DESCRIPTION'];
        $operValues['USERNO'] = $schedValues['USERNO'];

        // This is API which will be called by scheduler adapater
        $operValues['ACTION'] = 'NOTIFY';
        $operValues['ENTITY'] = 'GLBUDGETINSIGHT';

        // Set the module key
        $operValues['MODULEKEY'] = Globals::$g->kBUDGid;
        $operValues['STATUS'] = 'active';

    }

    private function PrepareScheduledOperationValues($values, &$schedOperValues)
    {
        // Get the schedule data
        $schedPath = 'SCHVALUES';
        $schedValues = $values[$schedPath];

        // Get the operation data
        $operPath = 'OPERVALUES';
        $operValues = $values[$operPath];

        // Build the schedule operation data
        $schedOperValues['CNY#'] = $schedValues['CNY#'];
        $schedOperValues['NAME'] = $schedValues['NAME'];
        $schedOperValues['DESCRIPTION'] = $schedValues['DESCRIPTION'];
        $schedOperValues['USERNO'] = $schedValues['USERNO'];
        $schedOperValues['STATUS'] = 'active';

        // The scheduled operation manager is expecting this for the translation
        // TODO: we should modify the manager to acct the record# directly. This will avoid useless queries
        $schedOperValues['OPERATION']['NAME'] = $operValues['NAME'];
        $schedOperValues['SCHEDULE']['NAME'] = $schedValues['NAME'];

        // because if user unsubscribe for notifications,  then we need to set it as inactive
    }

    private function CreateScheduledOperation(&$values)
    {
        // Build the schedule operation data
        $schedOperValues = array();
        $this->PrepareScheduledOperationValues($values, $schedOperValues);

        // Create the scheduled operation
        $gManagerFactory = Globals::$g->gManagerFactory;
        $schedOperMgr = $gManagerFactory->getManager('scheduledoperation');
        $ok = $schedOperMgr->add($schedOperValues);

        // Throw an error if the Add fails else add the schedules operation values to the data array
        if ($ok) {
            $schopPath = 'SCHOPVALUES';
            $values[$schopPath] = $schedOperValues;
            $values['SCHOPKEY'] = $schedOperValues[':record#'];
        } else {
            $gErr = Globals::$g->gErr;
            $gErr->addIAError(
                'GL-1814',
                __FILE__ . ':' . __LINE__,
                'ScheduledOperation record could not be created for ' . $schedOperValues['DESCRIPTION'],
                ['DESCRIPTION' => $schedOperValues['DESCRIPTION']]
            );
        }

        return $ok;
    }

    /**
     * Create a recurring journal entry creation schedules
     *
     * @param array $values the recurring journal entry data
     * @param array $schedValues the schedule data
     *
     * @return bool false if error else true
     */
    private function CreateTimerRecords(&$values, &$schedValues)
    {

        // Create the schedule record
        $ok = $this->CreateSchedule($values, $schedValues);
        // Create the operation record
        $ok = $ok && $this->CreateOperation($values, $schedValues);
        // Create the scheduled operation record
        $ok = $ok && $this->CreateScheduledOperation($values);

        return $ok;
    }

    /**
     * Update a recurring journal entry schedule
     *
     * @param array $values the recurring journal entry data
     * @param array $schedValues the schedule data
     *
     * @return bool false if error else true
     */
    private function UpdateTimerRecord(&$values, &$schedValues)
    {
        $ok = true;
        if ($values['NOTIFYWHEN']['EXPENSEEXCEEDBUDGET'] === 'false') {
            unset($values['SCHOPKEY']);
        }

        // Update the schedule record
        $ok = $ok && $this->UpdateSchedule($values, $schedValues);
        // Update the operation record
        $ok = $ok && $this->UpdateOperation($values, $schedValues);

        // Update the scheduled operation record
        $ok = $ok && $this->UpdateScheduledOperation($values);

        return $ok;
    }

    private function UpdateSchedule(&$values, &$schedValues)
    {
        // Prepare the schedule data from the recurring transaction data
        $this->PrepareScheduleValues($values, $schedValues);
        // Edit the schedule
        $gManagerFactory = Globals::$g->gManagerFactory;
        $schedMgr = $gManagerFactory->getManager('schedule');
        $ok = $schedMgr->set($schedValues);

        // Throw an error if the Edit fails else add the schedule values to the data array
        if ($ok) {
            $schPath = 'SCHVALUES';
            $values[$schPath] = $schedValues;
        } else {
            $gErr = Globals::$g->gErr;
            $gErr->addIAError(
                'GL-1815',
                __FILE__ . ':' . __LINE__,
                'Schedule record could not be updated for ' . $schedValues['DESCRIPTION'],
                ['DESCRIPTION' => $schedValues['DESCRIPTION']]
            );
        }

        return $ok;
    }

    private function UpdateOperation(&$values, $schedValues)
    {
        // Prepare the operation data from the schedule data
        $operValues = array();
        $this->PrepareOperationValues($values, $schedValues, $operValues);
        // Edit the operation
        $gManagerFactory = Globals::$g->gManagerFactory;
        $operMgr = $gManagerFactory->getManager('operation');
        $ok = $operMgr->set($operValues);

        // Throw an error if the Edit fails else add the operation values to the data array
        if ($ok) {
            $operPath = 'OPERVALUES';
            $values[$operPath] = $operValues;
        } else {
            $gErr = Globals::$g->gErr;
            $gErr->addIAError(
                'GL-1816',
                __FILE__ . ':' . __LINE__,
                'Operation record could not be updated for ' . $operValues['DESCRIPTION'],
                ['DESCRIPTION' => $operValues['DESCRIPTION']]
            );
        }

        return $ok;
    }

    private function UpdateScheduledOperation(&$values)
    {
        // Build the schedule operation data
        $schedOperValues = array();
        $this->PrepareScheduledOperationValues($values, $schedOperValues);

        // Edit the scheduled operation
        $gManagerFactory = Globals::$g->gManagerFactory;
        $schedOperMgr = $gManagerFactory->getManager('scheduledoperation');
        $ok = $schedOperMgr->set($schedOperValues);

        // Throw an error if the Edit fails else add the schedules operation values to the data array
        if ($ok) {
            $schopPath = 'SCHOPVALUES';
            $values[$schopPath] = $schedOperValues;
        } else {
            $gErr = Globals::$g->gErr;
            $gErr->addIAError(
                'GL-1817',
                __FILE__ . ':' . __LINE__,
                'ScheduledOperation record could not be updated for ' . $schedOperValues['DESCRIPTION'],
                ['DESCRIPTION' => $schedOperValues['DESCRIPTION']]
            );
        }

        return $ok;
    }

    private function UpdateTimerRecords(&$values, &$schedValues)
    {
        $ok = true;
        // Update the schedule if exists else create a new one.
        if (isset($values['SCHOPKEY']) && $values['SCHOPKEY'] != '') {

            $ok = $ok && $this->UpdateTimerRecord($values, $schedValues);
        } else {
            $ok = $ok && $this->CreateTimerRecords($values, $schedValues);
        }
        return $ok;
    }

    /**
     * This function deletes scheduled operations for that module and returns
     * bool
     *
     * @return bool
     */
    public function deleteScheduledOperationRecords()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $schedopMgr = $gManagerFactory->getManager('scheduledoperation');
        $ok = $schedopMgr->DeleteScheduledTransaction(
            self::BUDGET_INSIGHT_SCHEDULEDOPERATION_NAME
        );
        return $ok;
    }



    /*******************************************************************************************************************/


    /**
     * @param array $values
     * @param string $arrIndexName
     * @param bool|null $noErrorAdded
     * @return bool
     */
    private function isIdFieldExist(array $values, string $arrIndexName, ?bool &$noErrorAdded): bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;
        $noErrorAdded = true;
        if (!isset($values[$arrIndexName]["ID"]) || isNullOrBlank($values[$arrIndexName]["ID"])) {
            $fieldInfo = $this->GetFieldInfo($arrIndexName . ".ID");
            $ok = false;
            if ($fieldInfo["required"] ?? false) {
                $fieldName = $fieldInfo["fullname"] ?? $arrIndexName;
                $noErrorAdded = false;
                $gErr->addIAError('BVA-001', __FILE__ . ':' . __LINE__,
                    "Required field is missing: " . $fieldName,
                    ["FIELD_NAME" => $fieldName]
                );
            }
        }
        return $ok;
    }

    /**
     * @param array $values
     * @param string $arrIndexName
     * @param string $entityName
     * @param string $idFieldName
     * @param array $additionalFilter
     * @param string $recordNoFieldName
     * @return bool
     */
    private function validateNdSetKeyFromFilter(array  &$values, string $arrIndexName, string $entityName,
                                                string $idFieldName, array $additionalFilter = [],
                                                string $recordNoFieldName = 'RECORDNO'): bool
    {
        if (!$this->isIdFieldExist($values, $arrIndexName, $noErrorAdded)) {
            $values[$arrIndexName]["KEY"] = null;
            return $noErrorAdded;
        }

        $mgr = Globals::$g->gManagerFactory->getManager($entityName);

        $recordNo = null;
        $additionalFilter[] = [$idFieldName, '=', $values[$arrIndexName]["ID"]];
        $params = [
            'selects' => [$recordNoFieldName],
            'filters' => [$additionalFilter]
        ];
        $result = $mgr->GetList($params);
        if (Util::countOrZero($result) === 1 && isset($result[0][$recordNoFieldName])) {
            $recordNo = $result[0][$recordNoFieldName];
        }

        return $this->internalSetKeyValue($values, $arrIndexName, $recordNo);
    }

    /**
     * @param array $values
     * @param string $arrIndexName
     * @param string $entityName
     * @return bool
     */
    private function validateNdSetKeyFromId(array &$values, string $arrIndexName, string $entityName): bool
    {
        if (!$this->isIdFieldExist($values, $arrIndexName, $noErrorAdded)) {
            $values[$arrIndexName]["KEY"] = null;
            return $noErrorAdded;
        }
        $recordNo = Globals::$g->gManagerFactory->getManager($entityName)
            ->GetRecordNoFromVid($values[$arrIndexName]["ID"]);

        return $this->internalSetKeyValue($values, $arrIndexName, $recordNo);
    }


    /**
     * @param $values
     * @param string $arrIndexName
     * @param string|null $recordNo
     * @return bool
     */
    private function internalSetKeyValue(&$values, string $arrIndexName, ?string $recordNo): bool
    {
        $ok = true;
        if (!isset($recordNo)) {
            $ok = false;
            $fieldInfo = $this->GetFieldInfo($arrIndexName . ".ID");
            $fieldName = $fieldInfo["fullname"] ?? $arrIndexName;
            Globals::$g->gErr->addIAError('BVA-0002', __FILE__ . ':' . __LINE__,
                "Given value is either invalid or inactive for :" . $fieldName,
                ["FIELD_NAME" => $fieldName]);
        } else {
            $values[$arrIndexName]["KEY"] = $recordNo;
        }
        return $ok;
    }

    /**
     * @param array $values
     * @return bool
     */
    private function validateNdSetReportingBook(array &$values): bool
    {
        $arrIndexName = "REPORTINGBOOK";
        if (!$this->isIdFieldExist($values, $arrIndexName, $noErrorAdded)) {
            return $noErrorAdded;
        }

        $ok = true;
        $glBookManager = Globals::$g->gManagerFactory->getManager("glbook");
        $params = [
            'selects' => ['RECORDNO'],
            'filters' => [[
                ['BOOKID', '=', $values[$arrIndexName]["ID"]],
                ['TYPE', '=', 'Standard'],
                ['STATUS', '=', 'active']
            ]]
        ];

        $result = $glBookManager->GetList($params);

        if (!isset($result[0])) {
            $ok = false;
            $fieldInfo = $this->GetFieldInfo($arrIndexName . ".ID");
            $fieldName = $fieldInfo["fullname"] ?? $arrIndexName;
            Globals::$g->gErr->addIAError('BVA-0002', __FILE__ . ':' . __LINE__,
                "Given value is either invalid or inactive for :" . $fieldName,
                ["FIELD_NAME" => $fieldName]);
        } else {
            $values[$arrIndexName]["KEY"] = $result[0]['RECORDNO'];
        }

        return $ok && $this->validateNdSetOtherReportingBook($values);
    }

    /**
     * @param array $values
     * @return bool
     */
    private function validateNdSetOtherReportingBook(array &$values): bool
    {
        $gErr = Globals::$g->gErr;

        if (!isset($values['OTHERBOOKENTRIES']) || Util::isEmptyCountable($values['OTHERBOOKENTRIES'])) {
            if ($values["ISBOOKCOMBINED"] === "false") {
                $gErr->addIAError('BVA-0012', __FILE__ . ':' . __LINE__,
                    "Combine reporting book can't be false if  other books are not present");
                return false;
            }
            return true;
        }


        $reportingBookId = $values['REPORTINGBOOK']['ID'];

        if (isNullOrBlank($reportingBookId)) {
            /* Error message is already added by Reporting Period*/
            return false;
        }

        if (isset($values['OTHERBOOKENTRIES'][0]['GLBOOK']) && !is_array($values['OTHERBOOKENTRIES'][0]['GLBOOK'])) {
            $gErr->addIAError('BVA-0003', __FILE__ . ':' . __LINE__,
                "Other books are not in correct format");
            return false;
        }
        $ok = true;
        $glBookIds = [];

        foreach ($values['OTHERBOOKENTRIES'] as $arrIndex => $otherBook) {
            $index = $arrIndex + 1;
            if (isNullOrBlank($otherBook['GLBOOK']['ID'])) {
                $ok = false;
                $gErr->addIAError('BVA-0004', __FILE__ . ':' . __LINE__,
                    "Invalid value for other book in at index: " . $index,
                    ["INDEX" => $index]);
            } else {
                $glBookId = $otherBook['GLBOOK']['ID'];
                $ending = $reportingBookId;
                if (substr_compare($glBookId, $ending, -strlen($ending)) !== 0) {
                    $ok = false;
                    $gErr->addIAError('BVA-0004', __FILE__ . ':' . __LINE__,
                        "Invalid value for other book in at index: " . $index,
                        ["INDEX" => $index]);
                }
                if (!isset($glBookIds[$glBookId])) {
                    $glBookIds[$glBookId] = $glBookId;
                } else {
                    $ok = false;
                    $gErr->addIAError('BVA-0005', __FILE__ . ':' . __LINE__,
                        "Duplicate value for other book in at index: " . $index,
                        ["INDEX" => $index]);
                }
            }
        }


        if ($ok && !isEmptyArray($glBookIds)) {
            $glBookManager = Globals::$g->gManagerFactory->getManager("glbook");
            $params = [
                'selects' => ['BOOKID', 'RECORDNO'],
                'filters' => [[
                    ['BOOKID', 'in', array_values($glBookIds)],
                    ['TYPE', 'in', ['User Defined', 'Gaap', 'Tax']],
                    ['STATUS', '=', 'active']
                ]]
            ];

            $result = $glBookManager->GetList($params);

            if (isset($result[0])) {
                $glBookIdKeyMap = array_column($result, 'RECORDNO', 'BOOKID');
                foreach ($values['OTHERBOOKENTRIES'] as $arrIndex => &$otherBook) {
                    $index = $arrIndex + 1;
                    $glBookId = $otherBook['GLBOOK']['ID'];
                    if (isset($glBookIdKeyMap[$glBookId])) {
                        $otherBook['GLBOOK']['KEY'] = $glBookIdKeyMap[$glBookId];
                    } else {
                        $ok = false;
                        $gErr->addIAError('BVA-0004', __FILE__ . ':' . __LINE__,
                            "Invalid value for other book in at index: " . $index,
                            ["INDEX" => $index]);
                    }
                }
            } else {
                $ok = false;
                $gErr->addIAError('BVA-0003', __FILE__ . ':' . __LINE__,
                    "Other books are not in correct format");
            }
        }

        return $ok;
    }

    public static function replaceLastWord($string, $replacement)
    {
        /* Find the position of the last occurrence of the replacement word in the string */
        $lastOccurrencePosition = strrpos($string, $replacement);

        /* Extract the string up to the last occurrence of the replacement word */
        $beginning = substr($string, 0, $lastOccurrencePosition);

        /* Trim any trailing spaces and return the result */
        return trim($beginning);
    }

    /**
     * @param array $values
     * @return bool
     */
    private function validateNdSetAcctGrpKey(array &$values): bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $glAcctGrpMgr = $gManagerFactory->getManager("glacctgrp");

        /* Make sure to keep NETINCOME at the beginning */
        $accountGrpFields = ['NETINCOME', 'REVENUE', 'COSTOFREVENUE', 'EXPENSES'];

        foreach ($accountGrpFields as $accountGrpField) {
            $isNonRequiredField = ($accountGrpField === 'COSTOFREVENUE');
            $acctGrpVal = $values[$accountGrpField]["ID"] ?? null;
            if (isNullOrBlank($acctGrpVal)) {
                if ($isNonRequiredField) {
                    $values[$accountGrpField]["KEY"] = null;
                } else {
                    $fieldInfo = $this->GetFieldInfo($accountGrpField . ".ID");
                    $fieldName = $fieldInfo["fullname"] ?? $accountGrpField;
                    $gErr->addIAError('BVA-0002', __FILE__ . ':' . __LINE__,
                        "Given value is either invalid or inactive for :" . $fieldName,
                        ["FIELD_NAME" => $fieldName]);

                    $ok = false;
                }
                continue;
            }

            $glGrpMemberResult = $glAcctGrpMgr->GetList([
                'selects' => ['RECORDNO', 'NORMAL_BALANCE'],
                'filters' => [[['NAME', '=', $acctGrpVal]]]
            ]);

            $ok = $ok && $this->internalSetKeyValue($values,
                    $accountGrpField,
                    $glGrpMemberResult[0]['RECORDNO'] ?? null
                );

            switch ($accountGrpField) {
                case 'NETINCOME':
                    if (($glGrpMemberResult[0]['NORMAL_BALANCE'] ?? null) !== 'credit') {
                        $gErr->addIAError('BVA-0018', __FILE__ . ':' . __LINE__,
                            "Net operating income account group must have a credit normal balance");
                        $ok = false;
                    }
                    break;
                case 'REVENUE':
                    if (($glGrpMemberResult[0]['NORMAL_BALANCE'] ?? null) !== 'credit') {
                        $gErr->addIAError('BVA-0015', __FILE__ . ':' . __LINE__,
                            "Revenue account group must have a credit normal balance");
                        $ok = false;
                    }
                    break;
                case 'COSTOFREVENUE':
                    if (($glGrpMemberResult[0]['NORMAL_BALANCE'] ?? null) !== 'debit') {
                        $gErr->addIAError('BVA-0016', __FILE__ . ':' . __LINE__,
                            "Cost of revenue account group must have a debit normal balance");
                        $ok = false;
                    }
                    break;
                case 'EXPENSES':
                    if (($glGrpMemberResult[0]['NORMAL_BALANCE'] ?? null) !== 'debit') {
                        $gErr->addIAError('BVA-0017', __FILE__ . ':' . __LINE__,
                            "Operating expenses account group must have a debit normal balance");
                        $ok = false;
                    }
                    break;
            }
        }
        if ($ok) {
            $netIncome = $values['NETINCOME'];
            $revenue = $values['REVENUE'];
            $costOfRevenue = $values['COSTOFREVENUE'];
            $expenses = $values['EXPENSES'];

            $bvaAccountGroupCategoryObj = new BVAAccountGroupCategory(
                $netIncome, $revenue, $costOfRevenue, $expenses
            );

            /* Validate Accounts in each Group */
            $acctGrpCtgErrorGenObj = $bvaAccountGroupCategoryObj->getErrors();
            if ($acctGrpCtgErrorGenObj->valid()) {
                $acctGrpCtgError = $acctGrpCtgErrorGenObj->current();
                $gErr->addIAError($acctGrpCtgError['messageCode'], __FILE__ . ':' . __LINE__,
                    $acctGrpCtgError['message'],
                    $acctGrpCtgError['messageParameters']);
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * @param $values
     * @return bool
     */
    private function ValidateNdTranslate(&$values)
    {

        $gErr = Globals::$g->gErr;

        if (!is_array($values)) {
            $gErr->addIAError('BVA-0010', __FILE__ . ':' . __LINE__, "Invalid input");
            return false;
        }

        if (!isRoot()) {
            $entityDesc = "";
            $objId = I18N::getSingleToken("IA.CONFIGURE_COPILOT_VARIANCE_ANALYSIS");
            Globals::$g->gErr->addIAError("CORE-1049", GetFL(),
                "Currently, we can't access $entityDesc '$objId'. The data might no longer exist, " .
                "or you don't have the proper permissions. Check your permissions and try again.",
                ['ENTITY_DESCRIPTION' => $entityDesc, 'OBJECT_ID' => $objId]
            );
            return false;
        }

        $values['MODULEKEY'] = Globals::$g->kBVAid;
        $values['NOTIFYBY.COPILOT'] = "true";
        /*TODO:: we can remove this field, discussed with PM */
        $values['ANALYSE.BYSUBLOCS'] = "true";
        $values['ANALYSE.BYSUBDEPTS'] = "true";

        $ok = $this->validateNdSetKeyFromId($values, "GLBUDGETHEADER", "glbudgetheader");
        $ok = $ok && $this->validateNdSetKeyFromFilter($values, "CONTROLLERUSER", "userinfo",
                "LOGINID",
                [
                    ["USERTYPE", "IN", BVAVarianceProcessor::ALLOWED_USER_TYPES],
                    ["STATUS", "=", "active"],
                    ["UNRESTRICTED", "=", "true"],
                    ["LOGINDISABLED", "=", "false"],
                ]
            );
        $ok = $ok && $this->validateNdSetKeyFromId($values, "CONTROLLERUSER", "userinfo");
        $ok = $ok && $this->validateNdSetKeyFromId($values, "CONTROLLERUSERGROUP", "usergroup");
        $ok = $ok && $this->validateNdSetKeyFromFilter($values, "LOCFINREPORT", "reportinfo",
                "NAME", [["STATUS", "=", "active"]]);
        $ok = $ok && $this->validateNdSetKeyFromFilter($values, "DEPTFINREPORT", "reportinfo",
                "NAME", [["STATUS", "=", "active"]]);
        $ok = $ok && $this->validateNdSetReportingBook($values);
        $ok = $ok && $this->validateNdSetAcctGrpKey($values);

        if ($ok && $values['ANALYSE']['BYLOCATION'] !== 'true') {
            if (isset($values['LOCFINREPORT']['ID']) && !isNullOrBlank($values['LOCFINREPORT']['ID'])) {
                $gErr->addIAError('BVA-0019', __FILE__ . ':' . __LINE__,
                    "Analyse by location manager is not enabled however location financial report is selected");
                $ok = false;
            }
            if ($values['NOTIFYTO']['LOCATIONMANAGER'] === 'true') {
                $gErr->addIAError('BVA-0020', __FILE__ . ':' . __LINE__,
                    "Notify by location manager can't be enabled if analyse by location is not selected");
                $ok = false;
            }
        }

        if ($ok && $values['ANALYSE']['BYDEPARTMENT'] !== 'true') {
            if (isset($values['DEPTFINREPORT']['ID']) && !isNullOrBlank($values['DEPTFINREPORT']['ID'])) {
                $gErr->addIAError('BVA-0021', __FILE__ . ':' . __LINE__,
                    "Analyse by department manager is not enabled however department financial report is selected");
                $ok = false;
            }
            if ($values['NOTIFYTO']['DEPARTMENTMANAGER'] === 'true') {
                $gErr->addIAError('BVA-0022', __FILE__ . ':' . __LINE__,
                    "Notify by department manager can't be enabled if analyse by department is not selected");
                $ok = false;
            }
        }

        if ($ok) {
            $controllerUsers = [];

            if (!isNullOrBlank($values['CONTROLLERUSERGROUP']['ID'] ?? null)) {
                $userGroupKey = $values['CONTROLLERUSERGROUP']['KEY'] ?? null;
                $controllerUsers = Globals::$g->gManagerFactory->getManager("groupusers")
                    ->getGroupUsers($userGroupKey);
                $controllerUsers = array_values($controllerUsers);
            }

            if (!isNullOrBlank($values['CONTROLLERUSER']['ID'] ?? null)) {
                $controllerUsers[] = $values['CONTROLLERUSER']['ID'];
            }

            if (!Util::isEmptyCountable($controllerUsers)) {
                $ok = $this->validateControllerUsers($controllerUsers);
            }
        }

        $ok = $ok && $this->UpdateTimerRecords($values, $schedValues);

        return $ok;
    }

    public function validateControllerUsers($controllerUsers): bool
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        $userInfoMgr = Globals::$g->gManagerFactory->getManager("userinfo");
        $userInfoResults = $userInfoMgr->GetList([
            'selects' => ['LOGINID', 'USERTYPE', 'UNRESTRICTED', "LOGINDISABLED"],
            'filters' => [[
                ['LOGINID', 'in', $controllerUsers],
                ['STATUS', '=', 'active']
            ]]
        ]);

        if (!Util::isEmptyCountable($userInfoResults)) {

            foreach ($userInfoResults as $userInfo) {

                if (!in_array($userInfo['USERTYPE'], BVAVarianceProcessor::ALLOWED_USER_TYPES)) {
                    $gErr->addIAError('BVA-0023', __FILE__ . ':' . __LINE__,
                        sprintf("Invalid user-type for controller user: %s",
                            $userInfo['LOGINID']),
                        ['USER_ID' => $userInfo['LOGINID']]);
                    $ok = false;
                }

                if ($userInfo['UNRESTRICTED'] !== 'true') {
                    $gErr->addIAError('BVA-0024', __FILE__ . ':' . __LINE__,
                        sprintf("Restricted user can't be a controller user:  %s",
                            $userInfo['LOGINID']),
                        ['USER_ID' => $userInfo['LOGINID']]);
                    $ok = false;
                }

                if ($userInfo['LOGINDISABLED'] === 'true') {
                    $gErr->addIAError('BVA-0025', __FILE__ . ':' . __LINE__,
                        sprintf("User does not meet the requirements to be a controller user:  %s",
                            $userInfo['LOGINID']),
                        ['USER_ID' => $userInfo['LOGINID']]);
                    $ok = false;
                }
            }
        }

        return $ok;
    }


    /**
     * @param $id
     * @param $fields
     * @return array
     */
    public function get($id, $fields = null)
    {
        // $id is always pointing to vid, get doesn't return if it is blank or null
        if (isNullOrBlank($id)) {
            $id = Globals::$g->kBVAid;
        }
        return parent::get($id, $fields);
    }

    /**
     * @return bool
     */
    public function isBVAConfigured(): bool
    {
        $params = [
            'selects' => [
                [
                    'fields' => ['RECORDNO'],
                    'function' => 'count(${1})'
                ]
            ]
        ];

        $result = $this->GetList($params);
        return ($result[0]['RECORDNO'] != '0');
    }

    /**
     * @param $values
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $ok = $this->validateNdTranslate($values);
        return $ok && parent::regularAdd($values);
    }

    protected function regularSet(&$values)
    {
        $source = __CLASS__ . '::' . __FUNCTION__;
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->validateNdTranslate($values);
        $ok = $ok && parent::regularSet($values);

        /* If the user has disabled the notification, then we need to delete the scheduled operation records */
        if ($values['NOTIFYWHEN']['EXPENSEEXCEEDBUDGET'] === 'false') {
            $ok = $ok && $this->deleteScheduledOperationRecords();
        }
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = "Error on updating budget copilot variance analysis setup";
            Globals::$g->gErr->addError('BVA-0014', __FILE__ . ':' . __LINE__, $msg);
            $this->_QM->rollbackTrx($source);
            $ok = false;
        }
        return $ok;
    }

    /**
     * @param $operation
     * @param $values
     * @return bool
     */
    public function API_Validate($operation, &$values = null)
    {
        $ok = self::isBVAEnabled();

        if (!$ok) {
            Globals::$g->gErr->addIAError(
                'BVA-0001', __FILE__ . ':' . __LINE__,
                "Budget Copilot Variance Analysis is not enabled or not configured"
            );
        }
        return $ok && parent::API_Validate($operation, $values);
    }
}
