<?

//	FILE:			AuditPeriodGrpLister.cls
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	Lister for Audit Reporting Periods
//
//	(C)2002, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//

import('NLister');


class AuditPeriodGrpLister extends NLister {

	function __construct()
    {
		global $_auditrepperiodkey;

		parent::__construct(
			array (
				'entity'    	=>  'AuditPeriodGrp',
				'title'			=>	'IA.LEADSHEET_TO_ACCTS_MAPPING',
				'entitynostatus' => true,
				'disableedit'	=>  1,
				'disableadd'	=>  1,
				'edit'				=> 'edit_acctleadsheetmap.phtml?.type=S&.auditrepperiodkey='.$_auditrepperiodkey,
				'fields'		=>  array('RECORDNO',
					'AUDITREPPERIODKEY','ACCTGRPKEY','glacctgrp.name',
					'glacctgrp.isleaf','glacctgrp.HOWCREATED'),
				'helpfile'	=> 'CO56',
			)
		);
	}



	function BuildTable() {
		global $_auditrepperiodkey;
		$_type = Request::$r->_type;

		Lister::BuildTable();
		$op =  $this->_params['_op']['parent'];
		foreach($this->table as $i => $rec) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $name = str_replace(' ', '_', $rec['NAME']);
			$do = CheckPermissionPriority(GetOperationId('audit/lists/audit_periods')); 
			$this->table[$i]['EDITMEMBERS'] = '<a href="'.CallUrl('edit_acctgrpmembers.phtml?type=html&.r=' . $rec['ACCTGRPKEY'] . '&.op=' . $op . '&.do=' . $do . '&.type=' . $_type . '&.auditrepperiodkey=' . $_auditrepperiodkey). '" ' . HREFUpdateStatus("Members") . ">Members</a>"; 
		}

		$this->_params['_fields'] =  array('GLACCTGRP.NAME', 'EDITMEMBERS');
		$this->_params['_fieldlabels'] =  array('Name', '');
		$this->_params['_fieldorders']= array('GLACCTGRP.NAME', 'EDITMEMBERS');
		$this->_params['_fullnames'] = array('GLACCTGRP.NAME', 'EDITMEMBERS');
	}

    /**
     * @return array|mixed
     */
	function CalcFiltersLite() {
		global $_auditrepperiodkey;
		$_type = Request::$r->_type;

		$filters = Lister::CalcFiltersLite();
		if (isset($_type) && $_type == 'S')	{
			if (isset($_auditrepperiodkey) && $_auditrepperiodkey !=''){
				$filters[] = array('glacctgrp.isleaf', '=', 'T');
				$filters[] = array('glacctgrp.howcreated', '=', 'S');
				$filters[] = array('AUDITREPPERIODKEY', '=', $_auditrepperiodkey);
			}
		} else {
			$filters[] = array('glacctgrp.howcreated', '=', 'U');
		}
		return $filters;
	}

    /**
     * @return string
     */
	function genGlobs(){
		$ret = parent::genGlobs();
		
		global $_auditrepperiodkey;
		$_type = Request::$r->_type;

		$ret .= "<g name='.auditrepperiodkey'>$_auditrepperiodkey</g>";
		$ret .= "<g name='.type'>$_type</g>";
		return $ret;
	}

    /**
     * @return string
     */
	function genAllButtons() {
		$ret = parent::genAllButtons();

		global $_auditrepperiodkey;
		$_type = Request::$r->_type;

		$_sess = Session::getKey();
		$flipUrl = "lister.phtml?.type=$_type&amp;.sess=".$_sess."&amp;.auditrepperiodkey=$_auditrepperiodkey&amp;.op=" .
                   GetOperationId('co/setup/AuditAccountList') . "&amp;.done=" . insertDone(Request::$r->_comeback);

		$ret .= "<b id='flip'>";
		$ret .= CreateHRef($flipUrl, 'Flip', 'Accts to Leadsheet Mapping');
		$ret .= "</b>";

		return $ret;	
	}

    /**
     * @return string
     */
	function genTopPanel() {
		$ret = "<b id='flip'/>";
		$ret .= parent::genTopPanel();
		return $ret;	
	}

    /**
     * @return string
     */
	function genBotPanel() {
		$ret = "<b id='flip'/>";
		$ret .= parent::genBotPanel();
		return $ret;	
	}


}


