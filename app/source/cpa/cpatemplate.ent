<?php
/**
 * Entity for the Console Template object
 *
 * <AUTHOR>
 * @copyright 2000-2015 Intacct Corporation All, Rights Reserved
 */

$kMonths = Globals::$g->kMonths ?? [];
$podgroupMap = Globals::$g->gPODManager->getPodGroupSelectData(false);
$kMonthsKeys = [];
foreach (array_keys($kMonths) as $key) {
    $kMonthsKeys[] = "" . $key;
}
$kNonStandardPeriodsMap = Globals::$g->kNonStandardPeriodsMap ?? [];

$kSchemas["cpatemplate"] = array(
//    "children" => array(
//        "v_practicetemplates" => array(
//            "fkey" => "templatecny#",
//            "invfkey" => "templatecny#",
//            "join" => "outer",
//            "table" => "v_practicetemplates",
//        ),
//    ),
    "object" => array(
        "RECORDNO",
        "GUID",
        "CONSOLE_CNY",
        "TEMPLATECNY#",
        "TITLE",
        "NAME",
        "DESCRIPTION",
        "STATE",
        "STATUS",
        "ACCRUAL",
        "NONSTANDARDPERIODS",
        "ERROR",
    ),
    "schema" => array(
        "RECORDNO" => "templatecny#",
        "GUID" => "guid",
        "CONSOLE_CNY" => "cny#",
        "TEMPLATECNY#" => "templatecny#",
        "TITLE" => "title",
        "NAME" => "name",
        "DESCRIPTION" => "description",
        "STATE" => "state",
        "STATUS" => "status",
        "ACCRUAL" => "reporting_method",
        "NONSTANDARDPERIODS" => "nonstandardperiods",
        "ERROR" => "error",
    ),
    "fieldinfo" => array(
        $gRecordNoHiddenFieldInfo,
        "GUID" => array(
            "fullname" => "IA.GUID",
            "desc" => "IA.GUID",
            "readonly" => true,
            "hidden" => true,
            "type" => array(
                "ptype" => "text",
                "type" => "text",
                "maxlength" => 32,
            ),
            "path" => "GUID",
        ),
        "CONSOLE_CNY" => array(
            "fullname" => "IA.CONSOLE_CNY",
            "desc" => "IA.CONSOLE_CNY",
            "readonly" => true,
            "hidden" => true,
            "type" => array(
                "ptype" => "integer",
                "type" => "integer",
                "size" => 15,
                "maxlength" => 15,
                "format" => $gRecordNoFormat
            ),
            "path" => "CONSOLE_CNY",
        ),
        "TEMPLATECNY#" => array(
            "fullname" => "IA.TEMPLATE_CNY_NUMBER",
            "desc" => "IA.TEMPLATE_RECORD_NUMBER",
            "required" => false,
            "readonly" => true,
            "hidden" => true,
            "type" => array(
                "ptype" => "integer",
                "type" => "integer",
                "size" => 15,
                "maxlength" => 15,
                "format" => $gRecordNoFormat
            ),
            "path" => "TEMPLATECNY#",
        ),
        "TITLE" => array(
            "fullname" => "IA.TEMPLATE_ID",
            "desc" => "IA.TEMPLATE_ID",
            "type" => array(
                "ptype" => "text",
                "type" => "text",
                "maxlength" => 40,
                "size" => 40,
            ),
            "required" => false,
            "readonly" => true,
            "path" => "TITLE"
        ),
        "NAME" => array(
            "fullname" => "IA.TEMPLATE_NAME",
            "desc" => "IA.TEMPLATE_NAME",
            "type" => array(
                "ptype" => "text",
                "type" => "text",
                "maxlength" => 40,
                "size" => 40,
                "format" => "/^[\w\s\._\-]{1,40}$/"
            ),
            "required" => true,
            "path" => "NAME"
        ),
        "DESCRIPTION" => array(
            "fullname" => "IA.DESCRIPTION",
            "desc" => "IA.DESCRIPTION",
            "type" => array(
                "ptype" => "multitext",
                "type" => "text",
                "maxlength" => 200,
                "size" => 20,
                "numofrows" => 4,
                "numofcols" => 35,
                "format" => $gDescription,
            ),
            "path" => "DESCRIPTION"
        ),
        "STATE" => array(
            "path" => "STATE",
            "fullname" => "IA.STATE",
            "desc" => "IA.STATE",
            "type" => array(
                "ptype" => "enum",
                "type" => "enum",
                "validlabels" => array("IA.NEW", "IA.IN_PROGRESS", "IA.READY", "IA.FAILED"),
                "validvalues" => array("NEW", "IN_PROGRESS", "READY", "FAILED"),
            ),
            "readonly" => true,
        ),
        $gStatusFieldInfo,
        "ACCRUAL" => array(
            "fullname" => "IA.REPORTING_METHOD",
            "type" => array(
                "ptype" => "radio",
                "type" => "radio",
                "validlabels" => array("IA.ACCRUAL_ONLY", "IA.CASH_ONLY"),
                "validvalues" => array("Accrual only", "Cash only"),
                "_validivalues" => array("T", "F")
            ),
            "default" => "Accrual only",
            "desc" => "IA.REPORTING_METHOD",
            "path" => "ACCRUAL",
        ),
        "NONSTANDARDPERIODS" => array(
            "fullname" => "IA.ACCOUNTING_PERIODS",
            "type" => array(
                "ptype" => "radio",
                "type" => "text",
                "validlabels" => array_values($kNonStandardPeriodsMap),
                "validvalues" => array_keys($kNonStandardPeriodsMap),
                "_validivalues" => array_keys($kNonStandardPeriodsMap),
            ),
            "desc" => "IA.ACCOUNTING_PERIODS",
            "path" => "NONSTANDARDPERIODS",
            "helpText" => "IA.ONCE_CREATED_THE_ACCOUNTING_PERIODS_SETTING",
            "required" => false,
            "showlabelalways" => true,
        ),
        "ERROR" => array(
            "path" => "ERROR",
            "fullname" => "IA.ERROR",
            "desc" => "IA.ERROR",
            "type" => array(
                "ptype" => "text",
                "type" => "text",
            ),
            "readonly" => true,
            "hidden" => true,
        ),

        "CREATEFROM" => array(
            "fullname" => "IA.CREATE_FROM",
            "type" => array(
                "ptype" => "radio",
                "type" => "radio",
                "validlabels" => array("IA.SCRATCH", "IA.EXISTING_TEMPLATE", "IA.EXISTING_INSTANCE"),
                "validvalues" => array("Scratch", "Existing template", "Existing instance"),
            ),
            "default" => "Scratch",
            "desc" => "IA.CREATE_FROM",
            "path" => "CREATEFROM"
        ),
        "INDUSTRY1KEY" => array(
            "path" => "INDUSTRY1KEY",
            "fullname" => "IA.TEMPLATE_KEY",
            "desc" => "IA.TEMPLATE_KEY",
            "type" => array(
                "type" => "integer",
                "ptype" => "integer",
                "size" => 15,
            ),
        ),
        "INDUSTRY1" => array(
            "fullname" => "IA.TEMPLATE",
            "desc" => "IA.EXISTING_TEMPLATE",
            "type" => array(
                "ptype" => "ptr",
                "type" => "text",
                "pickentity" => "cpatemplate",
                "entity" => "cpatemplate",
                "size" => 40,
                "maxlength" => 40,
                "restrict" => array(
                    array(
                        "pickField" => "STATE",
                        "value" => "READY"
                    ),
                ),
            ),
            "hidden" => true,
            "noedit" => true,
            "nonew" => true,
            "noview" => true,
            "path" => "INDUSTRY1",
        ),
        "INDUSTRY2KEY" => array(
            "path" => "INDUSTRY2KEY",
            "fullname" => "IA.INSTANCE_KEY",
            "desc" => "IA.INSTANCE_KEY",
            "type" => array(
                "type" => "integer",
                "ptype" => "integer",
                "size" => 15,
            ),
        ),
        "INDUSTRY2" => array(
            "fullname" => "IA.INSTANCE",
            "desc" => "IA.EXISTING_INSTANCE",
            "type" => array(
                "ptype" => "ptr",
                "type" => "text",
                "entity" => "client",
                "size" => 40,
                "maxlength" => 40,
                "restrict" => array(
                    array(
                        "pickField" => "LINKED",
                        "value" => "Enabled"
                    ),
                    array(
                        "pickField" => "STATE",
                        "value" => "READY"
                    ),
                ),
            ),
            "hidden" => true,
            "noedit" => true,
            "nonew" => true,
            "noview" => true,
            "path" => "INDUSTRY2",
            "renameable" => true,
        ),
        "CURRENCYFORMAT" => array(
            "fullname" => "IA.CURRENCY_FORMAT",
            "desc" => "IA.CURRENCY_FORMAT",
            "type" => array(
                "ptype" => "enum",
                "type" => "text",
                "maxlength" => 20,
                "size" => 20,
            ),
            //"default" => "United States",
            "required" => false,
            "path" => "CURRENCYFORMAT"
        ),
        "FIRSTMONTH" => array(
            "path" => "FIRSTMONTH",
            "fullname" => "IA.FIRST_FISCAL_MONTH",
            "type" => array(
                "ptype" => "enum",
                "type" => "text",
                "validlabels" => array_values($kMonths),
                "validvalues" => $kMonthsKeys,
            ),
            "showlabelalways" => true,
        ),
        "FIRSTMONTHTAX" => array(
            "path" => "FIRSTMONTHTAX",
            "fullname" => "IA.FIRST_TAX_MONTH",
            "type" => array(
                "ptype" => "enum",
                "type" => "text",
                "validlabels" => array_values($kMonths),
                "validvalues" => $kMonthsKeys,
            ),
            "showlabelalways" => true,
        ),
        "PRIMACCTNOLEN" => array(
            "fullname" => "IA.PRIMARY_ACCOUNT_KEY_LENGTH",
            "desc" => "IA.ACCOUNT_NUMBER_LENGTH",
            "type" => array(
                "ptype" => "text",
                "type" => "text",
                "maxlength" => 2,
                "size" => 2,
                "format" => "/^[0-9]{0,4}$/"
            ),
            "path" => "PRIMACCTNOLEN",
        ),
        "ACCTNOSEPARATOR" => array(
            "fullname" => "IA.ACCOUNT_FIELD_SEPARATOR",
            "desc" => "IA.ACCOUNT_FIELD_SEPARATOR",
            "type" => array(
                "ptype" => "text",
                "type" => "text",
                "maxlength" => 2,
                "size" => 2,
                "format" => "/^.{0,4}$/"
            ),
            "path" => "ACCTNOSEPARATOR",
        ),
        "SUBACCTNOLEN" => array(
            "fullname" => "IA.SUBACCOUNT_NUMBER_LENGTH",
            "desc" => "IA.SUBACCOUNT_NUMBER_LENGTH",
            "type" => array(
                "ptype" => "text",
                "type" => "text",
                "maxlength" => 2,
                "size" => 2,
                "format" => "/^.{0,4}$/"
            ),
            "path" => "SUBACCTNOLEN",
        ),
    ),
    "table" => "v_templates",
    "printas" => "IA.MANAGE_TEMPLATE",
    "pluralprintas" => "IA.MANAGE_TEMPLATES",
    "vid" => "TITLE",
    "module" => "mp",
    "nosysview" => true,
    "api" => array(
        "PERMISSION_READ" => "setup/cpatemplate/edit",  // View Audit Trail, but there is no view, use edit.
    ),
);
