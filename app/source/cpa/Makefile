#Makefile.in
#

ENTITY_XMLS=                           \
	$(EMPTY)

ENTITY_ENTS=                           \
	cpatemplate.ent                       \
	client.ent                         \
	externserviceauth.ent                  \
	externserviceauthuser.ent                  \
	myexternserviceauth.ent                     \
	practicebankaccount.ent                         \
	practicecreditcard.ent                         \
	practicecache.ent                         \
	mpsetup.ent                                 \
	externserviceauthtenant.ent                                 \
	$(EMPTY)

QUERY_OBJECTS=$(ENTITY_ENTS:.ent=.qry)

#
# no generated queries, but custom queries:
#   (list the custom query files for which no .qry file is generated from a .ent file)
#
CUSTOM_ONLY_QUERIES=    \
    template.cqry     \
    $(EMPTY)


LAYOUT_XMLS=                           \
	template_layout_edit.xml		   \
	$(EMPTY)

LAYOUT_PHPS=$(LAYOUT_XMLS:.xml=.php)
	
LAYOUT_XSLS=$(LAYOUT_XMLS:.xml=.xsl)

include ../Makefile.in
