<?php

/**
 * WebHooks serialization manager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation All, Rights Reserved
 */

// namespace WebHooks;

use Jose\Component\Signature\Serializer\CompactSerializer;
use Jose\Component\Signature\Serializer\JWSSerializerManager;
use Jose\Component\Signature\Serializer\JWSSerializerManagerFactory;

define("WEBHOOKS_SERIALIZATION_COMPACT", 0);
define("WEBHOOKS_SERIALIZATION_JSON_GENERAL", 1);
define("WEBHOOKS_SERIALIZATION_JSON_FLATTENED", 2);

class WebHooksSerializationManager extends WebHooks
{

    //constants
    const SERIALIZATION_COMPACT = 'jws_compact';

    /**
     * @var JWSSerializerManagerFactory $serializationManagerFactory
     */
    private $serializationManagerFactory = null;

    /**
     * @var JWSSerializerManager $compactserializationManager
     */
    private $compactserializationManager = null;

    /**
     * WebHooksSerializationManager constructor.
     *
     */
    protected function __construct()
    {
        $this->serializationManagerFactory = new JWSSerializerManagerFactory();
        //because of internal implementation of JWSSerializerManagerFactory and how it's performing DI and
        //how the factory will return the required instance this is not a memory leak(duplicate instance)
        $this->serializationManagerFactory->add(new CompactSerializer());
    }

    /**
     * Returns the serialization manager.
     *
     * @param int $serializationType
     *
     * @return JWSSerializerManager|null
     */
    public function getSerialization(int $serializationType) : ?JWSSerializerManager
    {
        $result = null;

        switch ($serializationType) {
            case WEBHOOKS_SERIALIZATION_COMPACT :
                //because of internal implementation of JWSSerializerManagerFactory and how it's performing DI and
                //how the factory will return the required instance. Only one instance is created
                if ( ! $this->compactserializationManager ) {
                    $this->compactserializationManager =
                        $this->serializationManagerFactory->create([ self::SERIALIZATION_COMPACT ]);
                }
                $result = $this->compactserializationManager;
                break;
            case WEBHOOKS_SERIALIZATION_JSON_FLATTENED:
            case WEBHOOKS_SERIALIZATION_JSON_GENERAL :
            default :
            self::getWebHooksHTTPResponse()
                ->setHTTPResponseCode(501)
                ->setHTTPResponseBody(WHServerError,
                                      'WebHooks serializer not implemented');
            self::sendResponse();
        }

        return $result;
    }

}