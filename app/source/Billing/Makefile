#Makefile.in
#

ENTITY_XMLS=                           \
	$(EMPTY)

ENTITY_ENTS=                        \
	BS_ActivityLog.ent				\
	BS_InvoiceLineItem.ent			\
	BS_Invoice.ent					\
	BS_OrderLineItem.ent			\
	BS_Subscription.ent				\
	BS_PriceList.ent				\
	BS_PriceListLine.ent			\
	billingclientcompany.ent		\
	bs_invoiceedit.ent              \
	$(EMPTY)
	
LAYOUT_XMLS=                           \
	BS_PriceList_layout_edit.xml       \
	bs_invoiceedit_layout_edit.xml       \
	$(EMPTY)

#
# no generated queries, but custom queries:
#   (list the custom query files for which no .qry file is generated from a .ent file)
#
CUSTOM_ONLY_QUERIES=    \
    bs_lib.cqry   \
    bsterritory.cqry   \
    $(EMPTY)



QUERY_OBJECTS=$(ENTITY_ENTS:.ent=.qry)
LAYOUT_PHPS=$(LAYOUT_XMLS:.xml=.php)
LAYOUT_XSLS=$(LAYOUT_XMLS:.xml=.xsl)

include ../Makefile.in
