<?

require_once 'BS_Common.inc';
global $BS_Common;

$kSchemas['BS_InvoiceLineItem'] = array(
	'table' => 'bsinvoicelineitem',
	'vid' => 'ID',
	'object' => array(
        'ID',
        'INVOICE_ID',
        'BILL_TO_CNY',
        'TIMESTAMP',
        'PRODUCT',
        'MEMO',
        'TOTAL_QUANTITY',
        'TOTAL_COMMISSION',
        'TOTAL_PRICE',
	),
	'schema' => array(
		'ID' => 'id',
        'INVOICE_ID' => 'invoiceid',
        'BILL_TO_CNY' => 'billtocny#',
        'TIMESTAMP' => 'timestamp',
        'PRODUCT' => 'productid',
        'MEMO' => 'memo',
        'TOTAL_QUANTITY' => 'totalquantity',
        'TOTAL_COMMISSION' => 'totalcommission',
        'TOTAL_PRICE' => 'totalprice',
	),
	'fieldinfo' => array(
		$BS_Common['ID'],
		array(
			'fullname' => 'IA.INVOICE_ID',
			'type' => array(
				'ptype' => 'integer',
				'type' => 'integer',
				'maxlength' => 8,
			),
			'path' => 'INVOICE_ID'
		),
		$BS_Common['LINE_ITEMS']['BILL_TO_CNY'],
		$BS_Common['TIMESTAMP'],
		$BS_Common['LINE_ITEMS']['DESCRIPTION'],
		$BS_Common['LINE_ITEMS']['SUMMARYDESCRIPTION'],
		array(
			'fullname' => 'IA.TOTAL_QUANTITY',
			'type' => array(
				'ptype' => 'integer',
				'type' => 'integer',
				'maxlength' => 8,
			),
			'path' => 'TOTAL_QUANTITY'
		),
		array(
			'fullname' => 'IA.TOTAL_COMMISSION',
			'type' => array(
				'ptype' => 'currency',
				'type' => 'currency',
			),
			'path' => 'TOTAL_COMMISSION'
		),
		array(
			'fullname' => 'IA.TOTAL_PRICE',
			'type' => array(
				'ptype' => 'currency',
				'type' => 'currency',
			),
			'path' => 'TOTAL_PRICE'
		)

	)
);


