<?php

class ExpensesEmployeeService
{
    public function allowedactions(array $request, array $extraContext) : array
    {
        $userKey = GetMyUserid();
        $userId = GetMyLogin();
        $employeeId = GetMyEmpid();
        $employeeManager = Globals::$g->gManagerFactory->getManager('employee');
        if ($employeeId  != null) {
            $employeeKey = $employeeManager->GetRecordNoFromVid($employeeId);
        }
        $isSITEmbeddingProvisioned = SITUtils::isSITEmbeddingEnabled();
        $isElectronicReceiptProvisioned = ExpenseUtils::isExpenseAutomationActive() && ExpenseUtils::useElectronicReceiptsWithEmpExpenses();
        $isMultiCurrency = IsMCPEnabled('ee');
        $expenseManager = Globals::$g->gManagerFactory->getManager('eexpenses');
        $defaultExchangeRate = $expenseManager->getExchangeRateTypeId(
            $expenseManager->getConfigExchangeRateType([])
        );
        $defaultExchangeRate = $defaultExchangeRate;
        $canApproveExpense = ($operationId = GetOperationId('ee/activities/approveexpenses')) > 0 && CheckAuthorization($operationId, 1);;

        $contextArray = [
            'userKey' => $userKey,
            'userId' => $userId,
            'employeeId' => $employeeId,
            'employeeKey' => $employeeKey,
            'isEmployeeFileReceipts' => $isElectronicReceiptProvisioned,
            'isEmployeeReceiptMultiCurrency' => $isMultiCurrency,
            'employeeExchangeRate' => $defaultExchangeRate,
            'isEmployeeAppover' => $canApproveExpense,
        ];
        return $contextArray;
    }
}