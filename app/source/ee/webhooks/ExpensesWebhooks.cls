<?php

/**
 * Expense inbound webhook
 *
 *
 * <AUTHOR>
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 */
class ExpensesWebhooks
{
    public const EXPENSEWEBHOOK = 'EXPENSE_WEBHOOK';
    public const TOPIC_SAIL_WEBHOOK = 'SAIL_WEBHOOK';

    public const DRAFT_STATUS = 'Draft';

    public const QUEUED_STATE = 'Q';
    /**
     * @param array $request
     * @param array $extraContext
     *
     * @throws APIException
     */
    public function receipts(array $request, array $extraContext)
    {
        $GUID = $request['customerId'];
        $companyInfo = self::getCompanyAndDatabaseID($GUID);
        $companyID = $companyInfo['CNY#'];
        $results = DBRunner::runOnCnyDB('ExpensesWebhooks::updateNotification',
            [$companyID, $request],
            $companyID);
        return $results;
    }


    /**
     * Get cny# based on the GUID
     *
     * @param string $GUID
     *
     * @return array
     */
    public static function getCompanyAndDatabaseID(string $GUID) : array
    {
        $stmt = [ "select CNY#,TITLE from SCHEMAMAP where GUID = :1 AND STATUS = 'T'", $GUID ];

        $qryResult = QueryResult($stmt, 0, '', GetGlobalConnection());

        $result = $qryResult[0] ? : null;

        return $result;
    }

    /**
     * @param int $companyID
     * @param array $request
     * @return array|string[]
     */
    public static function updateNotification(int $companyID, array $request): array
    {
        LogToFile('Receipt Webhook Notification LOGS : Start');

        if ($request['workflow'] === 'accounts_payable') {
            /* if workflow is account payable then use the AP metrics and set the queue type to W
                else use the default expense metrics and set the queue type to A
            */
            $metric = new MetricAccountsPayableWebhookProcess();
            $topic = self::TOPIC_SAIL_WEBHOOK;
            $queuetype = 'W';
        } else {
            $metric = new MetricExpenseWebhookProcess();
            $topic = self::EXPENSEWEBHOOK;
            $queuetype = 'A';
        }

        $metric->startTime();
        $fileId = $request['orchestrationId'];
        $request['companyid'] = $companyID;
        $jsonNotification = self::arrayChangeKeyCaseRecursive($request);
        $jsonNotification = json_encode($jsonNotification);
        LogToFile('STXWEBHOOK LOGS : FILEID - ' . $fileId);
        $documentType = self::DRAFT_STATUS;
        $type = self::DRAFT_STATUS;
        $metric->setId($fileId);
        $metric->setType(self::DRAFT_STATUS);
        $metric->setNotificationStatus($type);

        $insertQuery = "INSERT INTO FIFODISPATCHERQUEUE (cny#,record#,topic,doctype, object, objectrecid, details, state, whencreated, createdby, type)
                        VALUES (:1, get_nextrecordid(:1, 'FIFODISPATCHERQUEUE'), :2, :3, utl_raw.cast_to_raw('$jsonNotification'), :4,:5,:6,TO_DATE('" . GetCurrentUTCTimestamp() . "','MM/DD/YYYY HH24:MI:SS'),:7,:8)";
        $ok = ExecStmt([$insertQuery, $companyID, $topic, $documentType, $fileId, '{"status":"Queued"}', self::QUEUED_STATE, UserInfoManager::SYSTEMUSER, $queuetype]);

        $metric->stopTime();
        $metric->setTimeTakenToProcess($metric->getTime());
        $metric->publish();

        if ($ok) {
            $metric->setStatus('Success');
            LogToFile('Receipt Webhook Notification LOGS : FILEID - ' . $fileId . ' - Success');
            $result = ["status" => "success", "orchestrationId" => $request['orchestrationId']];
        } else {
            $metric->setStatus('Failed');
            LogToFile('Receipt Webhook Notification LOGS : FILEID - ' . $fileId . ' - DB Insert Failed');
            $result = ["status" => "failure", "orchestrationId" => "failure"];
        }
        LogToFile('Receipt Webhook Notification LOGS : End'.$fileId);
        return $result;
    }

    /**
     * @param array $arr
     * @return array
     */
    private static function arrayChangeKeyCaseRecursive(array $arr): array
    {
        return array_map(static function ($item) {
            if (is_array($item)) {
                $item = self::arrayChangeKeyCaseRecursive($item);
            }
            return $item;
        }, array_change_key_case($arr));
    }
}
