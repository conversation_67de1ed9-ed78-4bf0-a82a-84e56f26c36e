<?php

//=============================================================================
//
//	FILE:			 ExpenseWebhookQueueManager.cls
//	<AUTHOR> Adiserla <<EMAIL>>
//	DESCRIPTION:	 ExpenseWebhookQueueManager class
//
//
//=============================================================================

class ExpenseWebhookQueueManager extends FIFODispatcherQueueManager
{
    const EXPENSEWEBHOOK = 'EXPENSEWEBHOOK';

    /**
     * @param array $job
     * @param bool $includeUserRecord
     * @param bool $includeLocation
     * @param bool $includeDepartment
     * @return bool
     */
    public function addJobInQueue($job, bool $includeUserRecord = true, bool $includeLocation = true, bool $includeDepartment = true): bool
    {
        $ok = false;
        if ($this->completeAndValidateJob($job, $includeUserRecord, $includeLocation, $includeDepartment)) {
            $ok = parent::add($job);
            if (!$ok) {
                logToFileCritical(self::LOG_PREFIX . "($this->_entity) - Could not add job in queue.");
            }
        }
        return $ok;
    }
}