<?php

/**
 *    SAILCreateCompanyRequestMapper.cls
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025, Intacct Corporation, All Rights Reserved
 */
class SAILCreateCompanyRequestMapper extends AutomatedTransactionRequestMapper
{

    /**
     * SAILCreateCompanyRequestMapper constructor.
     *
     * @param AutomatedTransactionInteractions $interactions
     *
     * @throws AutomatedTransactionException
     */
    public function __construct(AutomatedTransactionInteractions $interactions)
    {
        parent::__construct(SAILInteractions::ACTION_CREATE_COMPANY, $interactions);
    }

    /**
     * @param array $payload
     *
     * @return array
     */
    protected function format(array &$payload): array
    {
        return $payload;
    }
}