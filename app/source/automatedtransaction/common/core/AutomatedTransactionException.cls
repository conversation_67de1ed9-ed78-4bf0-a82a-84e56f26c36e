<?php
/**
 * AutomatedTransactionException.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025, Intacct Corporation, All Rights Reserved
 */
class AutomatedTransactionException extends Exception
{

    const GENERAL_ERROR = 0;
    const CNY_NOT_FOUND = 1;
    const SERVER_ERROR = 2;
    const AUTH_ERROR = 3;
    const PROCESS_ERROR = 4;
    const INVALID_INPUT = 5;
    const INVALID_COMPANY = 6;
    const INVALID_ACTION = 7;
    const INVALID_PROPERTY = 8;

    const ERROR_MESSAGES = [
        self::GENERAL_ERROR => "Some Error in Automated Transaction",
        self::CNY_NOT_FOUND => "Company not found",
        self::SERVER_ERROR => "Server Error",
        self::AUTH_ERROR => "Auth Token Error",
        self::PROCESS_ERROR => "Unable to process response",
        self::INVALID_INPUT => "Invalid input",
        self::INVALID_COMPANY => "Invalid Company",
        self::INVALID_ACTION => "Unknown property",
    ];

    /**
     * @var string $calledClass
     */
    private $calledClass = '';

    /**
     * Construct the exception. Note: The message is NOT binary safe.
     *
     * @param int $code [optional] The Exception code.
     * @param string $message [optional] The Exception message to throw.
     */
    public function __construct($code = 0, $message = "")
    {
        if (empty($message) && isset(self::ERROR_MESSAGES[$code])) {
            $message = self::ERROR_MESSAGES[$code];
        }
        $this->calledClass = get_called_class();
        parent::__construct($message, $code);
    }

    /**
     * @return bool
     */
    public function isCompanyNotFound(): bool
    {
        return $this->code === self::CNY_NOT_FOUND;
    }

    /**
     * @return bool
     */
    public function isServerError(): bool
    {
        return $this->code === self::SERVER_ERROR;
    }

    /**
     * @return bool
     */
    public function isAuthError(): bool
    {
        return $this->code === self::AUTH_ERROR;
    }

    /**
     * @return bool
     */
    public function isProcessError(): bool
    {
        return $this->code === self::PROCESS_ERROR;
    }
}