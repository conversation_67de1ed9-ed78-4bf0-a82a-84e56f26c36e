<?php
/**
 *    AutomatedTransactionHttpRequest.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025, Intacct Corporation, All Rights Reserved
 */
abstract class AutomatedTransactionHttpRequest implements AutomatedTransactionHttpRequestInterface
{
    /*
    * @var AutomatedTransactionInteractions $interactions
    */
    protected $interactions;

    function __construct(AutomatedTransactionInteractions $interactions)
    {
        $this->interactions = $interactions;
    }

    /**
     * Get Token in HttpResonse Object
     *
     * @param bool $noCacheToken
     *
     * @return SimpleHttpResponse
     *
     * @throws \Exception|SimpleHttpResponseException|\InvalidArgumentException
     */
    abstract public function getTokenResponse(bool $noCacheToken): SimpleHttpResponse;

    /**
     * Get access_token from HttpRespose object
     *
     * @param bool $noCacheToken
     *
     * @return string
     *
     * @throws SimpleHttpResponseException
     */
    private function getAccessToken($noCacheToken): string
    {
        $tokenResponseObj = $this->getTokenResponse($noCacheToken);
        $tokenResponse = $tokenResponseObj->getResponse();
        $tokenResponse = json_decode($tokenResponse);
        if (!isset($tokenResponse->access_token) || !is_string($tokenResponse->access_token)) {
            throw (new SimpleHttpResponseException($tokenResponseObj, "Unable to build HTTP Request header"));
        }

        return $tokenResponse->access_token;
    }

    /**
     * Build auth header for Automated Transaction request
     *
     * @param string[] $headers
     * @param bool $noCacheToken Shouldn't be call in usual scenerio
     *
     *
     * @throws SimpleHttpResponseException
     */
    private function buildAuthHeader(&$headers = [], $noCacheToken = false)
    {
        $accessToken = $this->getAccessToken($noCacheToken);
        $headers[] = "Authorization: Bearer {$accessToken}";
        $headers[] = "Content-Type: application/json";
        $headers[] = "Accept: application/json";
    }

    /**
     * Do the request to the Automated Transaction server with Auth header
     *
     * @param string $action
     * @param string $url
     * @param array|string $body
     * @param array $headers
     * @param string $httpMethod
     *
     * @return SimpleHttpResponse
     * @throws Exception
     */
    private function doHTTPCall(string $action, string $url, $body, $headers,
                                string $httpMethod = "POST"): SimpleHttpResponse
    {
        $timeout = $this->interactions->getIAConfig("AT_TIMEOUT");
        $curlInfo = null;
        $guId = \CompanyCacheHandler::getInstance()->getProperty('COMPANYPREF', 'GUID');
        $httpCallMetric = new MetricAutomatedTransactionHttpRequestHttpCall();
        $httpCallMetric->startTime();
        $httpCallMetric->setAction($action);
        $httpCallMetric->setEndPoint($url);
        $httpCallMetric->setGuId($guId);

        $responseCode = \Util::httpCall($url, $body, $response, ($httpMethod === 'GET'), null,
            null, true, $headers, $httpMethod, null, true, $theRetHeaders,
            false, $curlInfo, $timeout);

        $httpCallMetric->setResCode($responseCode);
        $httpCallMetric->stopTime();
        $httpCallMetric->publish();

        return (new SimpleHttpResponse($responseCode, $response, $theRetHeaders));
    }

    /**
     * All Automated Transaction request must pass through this function.
     *
     * @param string $action Automated Transaction Action
     * @param string $url partial, environment-agnostic, url
     * @param array|string $body JSON request body
     * @param string $httpMethod
     * @param bool $noCacheToken
     *
     * @return SimpleHttpResponse
     * @throws SimpleHttpResponseException
     */
    public function makeHttpRequest(string $action, string $url, $body, string $httpMethod = "POST",
                                    bool   $noCacheToken = false): SimpleHttpResponse
    {
        $this->buildAuthHeader($headers, $noCacheToken);
        return $this->doHTTPCall($action, $url, $body, $headers, $httpMethod);
    }
}