<?php
/**
 * POAutomatedTransactionFileUploadManager.cls
 *
 * <AUTHOR> <reuben<PERSON><EMAIL>> *
 * @copyright 2025 Intacct Corporation -- All Rights Reserved
 */
class POAutomatedTransactionFileUploadManager extends AutomatedTransactionFileUploadManager
{

    public static array $automatedTransactionErrorMap = [
        'ERR_DUPLICATE_SHORT' => 'IA.DUPLICATE_OF_EXISTING_DOCUMENT',
        'ERR_DUPLICATE_LONG' => 'IA.THE_SUBMITTED_DOCUMENT_DUPLICATES_AN_EXISTING_DOCUMENT',
        'SOURCEDOC_CANNOT_BE_LINKED_LONG' => 'IA.SOURCEDOC_CANNOT_BE_LINKED',
    ];

    /**
     * @param array $params parameters
     */
    public function __construct($params = array())
    {
        $this->mod = 'po';
        parent::__construct($params);
    }


    /**
     * This function, is to prepare the values for creating Draft document. We relaxed validation for creating Draft doc
     *
     * 1. first we need to get target document for that location from PO configuration.
     *      a. If there is no docType for that location, we need to consider 'Top level' doctype from config.
     * 2. We need to prepare one dummy line entries to create document. Line entries validation has been relaxed fo
     *    Draft doc creation. so we need to location and qunatity as a part of ['ENTRIES'] array.
     *
     * @param string $supportingDocumentID
     * @param string $locationId
     *
     * @return array
     */
    protected function prepareValue(string $supportingDocumentID, string $locationId): array
    {
        $value = [];

        //preapare minimal information on header.
        $value['mod'] = 'po';
        $value['SUPDOCID'] = $supportingDocumentID;
        $value['DOCSOURCE'] = self::AUTOMATEDTRANSACTION_SOURCE[$this->uploadingEntity];
        $value['STATE'] = DocumentManager::ANALYZING_STATE;
        $value['DOCPARID'] = PODocumentManager::DEFAULTPOAUTOMATIONDOCTYPE;

        //prepare minimal information on line entries.
        $value['ENTRIES'][0] = [
            'LOCATION' => $locationId,
            'UIQTY' => '0'
        ];

        return $value;
    }

    /**
     * This function is to preparing values for automatedTransactionfileupload table on Purchasing module.
     *
     * 1. In parent we are preparing values for automatedTransactionfileupload for required fields.
     * 2. We are overriding it and unsetting PRPRECORDKEY and assigning DOCHDRKEY and MODULEKEY
     *
     * @param string $documentId
     * @param string $dochdrkey
     * @param string $locationkey
     * @param string $multiLineConfig
     * @param AutomatedTransactionCommon $automatedTrxn
     * @return array
     */
    protected function prepareAutomatedTransactionFileupload(string $documentId, string $dochdrkey, string $locationkey, string $multiLineConfig, AutomatedTransactionCommon $automatedTrxn): array
    {
        $automatedTransactionFileUpload = parent::prepareAutomatedTransactionFileupload($documentId, $dochdrkey,  $locationkey,  $multiLineConfig,  $automatedTrxn);
        $automatedTransactionFileUpload['DOCHDRKEY'] = $dochdrkey;
        $automatedTransactionFileUpload['MODULEKEY'] = $this->getModuleKey();
        unset($automatedTransactionFileUpload['PRRECORDKEY']);

        return $automatedTransactionFileUpload;
    }

    /**
     * @return void
     */
    public function setUploadingEntity()
    {
        $this->uploadingEntity = 'podocument';
    }

    /**
     * @return string
     */
    public function getModuleKey()
    {
        return Globals::$g->kPOid;
    }

    /**
     * @param int $ID
     * @param array $automatedTransactionFileDetails
     *
     * @return bool
     */
    public static function updatePOAutomatedTransactionBeforePODocumentDelete($ID, $automatedTransactionFileDetails): bool
    {
        $ok = true;
        $values = [];
        $poautomatedTransactionDocMgr = Globals::$g->gManagerFactory->getManager('poautomatedtransactionfileupload');
        //get the Delete PO Document Details and update accrdingly
        $docMgr = Globals::$g->gManagerFactory->getManager('podocument');
        $docParams = [
            'selects' => ['DOCID', 'STATE','VENDDOCNO', 'CUSTVENDID', 'AUWHENCREATED', 'WHENCREATED', 'CREATEDBY'],
            'filters' => [
                [
                    ['RECORDNO', '=', $ID],
                ]
            ],
            'usemst' => true
        ];
        $docResult = $docMgr->GetList($docParams);
        $values['STXFILEID'] = $automatedTransactionFileDetails[0]['STXFILEID'];
        $values['RECORDNO'] = $automatedTransactionFileDetails[0]['RECORDNO'];
        $values['GENERICPARAMS'] = $automatedTransactionFileDetails[0]['GENERICPARAMS'] . '{"DELETED_DOCHDRKEY" : ' . $ID . '}';
        $values['DELETEDRECORDID'] = $docResult[0]['VENDDOCNO'];
        $values['DELETEDRECNO'] = $ID;
        $values['DELETEDRECVENDORID'] = $docResult[0]['CUSTVENDID'];
        //$automatedTransactionFileDetails['DELETEDRECSTATE'] = $docResult[0]['STATE'];
        $values['DELETEDRECAUWHENCREATED'] = $docResult[0]['AUWHENCREATED'];
        $values['DELETEDRECWHENCREATED'] = $docResult[0]['WHENCREATED'];
        $values['DELETEDRECDATE'] = GetCurrentUTCTimestamp();
        $values['DELETEDRECCREATEDBY'] = $docResult[0]['CREATEDBY'];
        $values['DELETEDRECMODIFIEDBY'] = GetMyUserid();
        $values['DOCHDRKEY'] = null;
        $ok = $ok && $poautomatedTransactionDocMgr->set($values);

        return $ok;
    }

    /**
     * @param array $values
     * @return bool
     */
    public static function updateAutomatedTransactionPODocumentError($values)
    {
        $ok = true;
        if ($values['UPLOADSTATUS'] === self::AUTOMATEDTRANSACTION_ERROR_STATE_FOR_BILL
            && !empty($values['UPLOADFILEISSUE'])
            && in_array($values['DOCSOURCE'], self::$billSource, true)
        ) {

            //Check if the automatedTransaction filedetails has the state cancelled then dont do anything, just skip the job notification.
            $automatedTransactionFileuploadMgr = Globals::$g->gManagerFactory->getManager('poautomatedtransactionfileupload');
            $params = [
                'selects' => [ 'STXFILEID' ],
                'filters' => [ [ [ 'DOCHDRKEY', '=', $values['RECORDNO'] ],
                    [ 'STATE', '=', self::DRAFT_ERROR_STATE ] ] ],
            ];
            $automatedTransactionFileDetails = $automatedTransactionFileuploadMgr->GetList($params);

            if(empty($automatedTransactionFileDetails)){
                return $ok;
            }

            $automatedTransactionFileDetail = $automatedTransactionFileDetails[0];

            $source = 'AutomatedTransactionFILEDETAILS:set';
            $ok = $ok && $automatedTransactionFileuploadMgr->_QM->beginTrx($source);

            $automatedTransactionValue['STATE'] = self::DRAFT_COMPLETED_STATE;
            $automatedTransactionValue['STXFILEID'] = $automatedTransactionFileDetail['STXFILEID'];
            $automatedTransactionValue['ERRORMSG'] = 'The draft document was updated/posted manually when in the draft state';

            $ok = $ok && $automatedTransactionFileuploadMgr->set($automatedTransactionValue);

            $ok = $ok && $automatedTransactionFileuploadMgr->_QM->commitTrx($source);
            if(!$ok){
                $automatedTransactionFileuploadMgr->_QM->rollbackTrx($source);
                LogToFile('Something went wrong to update the AutomatedTransactionFILEDETAILS table.');
            }
        }
        return $ok;
    }
}