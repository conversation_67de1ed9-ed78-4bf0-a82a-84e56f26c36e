<?php
/**
 * Manager class for automatedtransactionsetup information
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Intacct Corporation, All Rights Reserved
 */

$kSchemas['automatedtransactionsetup'] = array(
    'object' => array(
        'RECORDNO',
        'MODULEKEY',
        'PROVIDER',
        'STATE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY'
    ),
    'schema'    => array(
        'RECORDNO' => 'record#',
        'MODULEKEY' => 'modulekey',
        'PROVIDER' => 'provider',
        'STATE' => 'state',
        'SI_UUID' => 'si_uuid',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby'
    ),
    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat,
            ),
            'hidden' => true,
            'readonly' => true,
            'id' => 1,
        ),
        array(
            'path' => 'MODULEKEY',
            'fullname' => 'IA.MODULE_KEY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 20,
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 2,
        ),
        array(
            'path' => 'PROVIDER',
            'fullname' => 'IA.PROVIDER',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 20,
            ),
            'readonly' => true,
            'id' => 3,
        ),
        array(
            'path'     => 'STATE',
            'fullname' => 'IA.STATE',
            'desc'     => 'IA.STATE',
            'type'     => [
                'ptype'         => 'enum',
                'type'          => 'text',
                'validlabels'   => [ 'IA.IN_PROGRESS', 'IA.SUBSCRIBED', 'IA.FAILED' ],
                'validvalues'   => [ AutomatedTransactionSetupManager::STATE_INPROGRESS, AutomatedTransactionSetupManager::STATE_SUBSCRIBED, AutomatedTransactionSetupManager::STATE_FAILED ],
                '_validivalues' => [ 'P', 'S', 'F' ],
            ],

            'readonly'    => true,
            'showInGroup' => true,
            'renameable' => true,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'SETUPKEY', // the field that the owned object point to
            'invfkey' => 'RECORDNO',
            'entity' => 'automatedtransactioncompany', // to the parent vid
            'path' => 'COMPANIES',
        ),
    ),
    'api' => [
        'PERMISSION_READ' => 'services/modules/view',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
        'PERMISSION_MODULES' => ['co'],
    ],
    'table' => 'automatedtransactionsetup',
    'printas' => 'IA.AUTOMATED_TRANSACTION_SETUP',
    'pluralprintas' => 'IA.AUTOMATED_TRANSACTION_SETUP',
    'vid' => 'RECORDNO',
    'auditcolumns' => true,
    'autoincrement' => 'RECORDNO',
    'primaryfield' => 'RECORDNO',
    'module' => 'co'
);

