<?php

trait LoginTokens
{
    
    /** @var array $loginTokens */
    protected array $loginTokens = [
        ['id' => 'IA.HOME'],
        ['id' => 'IA.SUBMIT'],
        ['id' => 'IA.SECURITY'],
        ['id' => 'IA.SYSTEM_STATUS'],
        ['id' => 'IA.INTACCT'],
        ['id' => 'IA.AUTHORIZE'],
        ['id' => 'IA.OAUTH_APP_ACCESS_INTACCT_ON_MY_BEHALF'],
        ['id' => 'IA.COMPANY_ID'],
        ['id' => 'IA.CLIENT_ID'],
        ['id' => 'IA.USER_ID'],
        ['id' => 'IA.PASSWORD'],
        ['id' => 'IA.SIGN_IN'],
        ['id' => 'IA.FORGOT_YOUR_PASSWORD'],
        ['id' => 'IA.REMEMBER_ME'],
        ['id' => 'IA.SIGN_IN_TO_SAGE_INTACCT'],
        ['id' => 'IA.USER_INFORMATION'],
        ['id' => 'IA.TYPE'],
        ['id' => 'IA.USER_NAME'],
        ['id' => 'IA.ADMIN_PRIVILEGE'],
        ['id' => 'IA.ACCEPT'],
        ['id' => 'IA.DECLINE'],
        ['id' => 'IA.ERROR_NO_APPLICATION_IDENTIFIED'],
        ['id' => 'IA.WAIT_FEW_MINUTES_CHECK_EMAIL_AT'],
        ['id' => 'IA.UPTIME'],
        ['id' => 'IA.ERROR_COLON'],
        ['id' => 'IA.HELP'],
        ['id' => 'IA.CLIENT'],
        ['id' => 'IA.CHECK_HERE_FOR_SIGLE_SIGN_ON'],
        ['id' => 'IA.ACTIVATE_YOUR_COMPANY'],
        ['id' => 'IA.ACTIVATE_COMPANY_AUTHORIZATION_CODE'],
        ['id' => 'IA.AUTHORIZATION_CODE'],
        ['id' => 'IA.USE_SINGLE_SIGN_ON'],
        ['id' => 'IA.USE_BASIC_SIGN_IN'],
        ['id' => 'IA.PRIVACY_POLICY'],
        ['id' => 'IA.SELECT_TENANT'],
        ['id' => 'IA.PLEASE_SELECT_TENANT_AUTHENTICATE'],
        ['id' => 'IA.TENANT'],
        ['id' => 'IA.CONSOLE'],
        ['id' => 'IA.SIGN_IN_TO_INTACCT'],
        ['id' => 'IA.LOOKING_TO_ACCESS_YOUR_SAGE_INTACCT_PRODUCT']
    ];
}
