<?php

interface SSOValidationInterface
{

    /**
     * @param string $errorMessage
     */
    public function addErrorMessage(string $errorMessage): void;

    /**
     * @return array
     */
    public function getErrorMessages(): array;

    /**
     * @param CompanyCacheHandler $cch
     * @param UserCacheHandler    $uch
     *
     * @return bool
     */
    public function validate(CompanyCacheHandler $cch, UserCacheHandler $uch): bool;
}
