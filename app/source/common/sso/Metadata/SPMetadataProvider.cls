<?php

require_once '../../private/lib/robrichards/autoload.php';

use Rob<PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityDSig as XMLSecurityDSig;

class SPMetadataProvider implements SPMetadataProviderInterface
{

    /**
     * @param string $entityID
     * @param string $certificate
     * @param string $consumerServiceLocation
     * @param string $logoutServiceLocation
     * @param string $nameIdFormat
     * @param string $contactPersonType
     * @param string $contactPersonName
     * @param string $contactPersonEmailAddress
     *
     * @return string
     */
    public function buildMetadata(
        string $entityID,
        string $certificate,
        string $consumerServiceLocation,
        string $logoutServiceLocation,
        string $nameIdFormat,
        string $contactPersonType,
        string $contactPersonName,
        string $contactPersonEmailAddress
    ): string {
        $doc  = new \DOMDocument();
        $root = $this->createRoot($doc, $entityID);
        $SPSSODescriptor = $this->createSPSSODescriptor($doc);
        $SPSSODescriptor->appendChild($this->createKeyDescriptor($doc, $certificate, 'signing'));
        $SPSSODescriptor->appendChild($this->createKeyDescriptor($doc, $certificate, 'encryption'));
        $SPSSODescriptor->appendChild($this->createSingleLogoutService($doc, $logoutServiceLocation));
        $SPSSODescriptor->appendChild($this->createAssertionConsumerService($doc, $consumerServiceLocation));
        $SPSSODescriptor->appendChild($this->createNameIdFormat($doc, $nameIdFormat));
        $root->appendChild($SPSSODescriptor);

        $contactPerson = $this->createContactPerson(
            $doc,
            $contactPersonType,
            $contactPersonName,
            $contactPersonEmailAddress
        );
        $root->appendChild($contactPerson);

        $doc->appendChild($root);
        return $doc->saveXML();
    }

    /**
     * @param \DOMDocument $document
     * @param string      $entityID
     *
     * @return \DOMElement
     */
    private function createRoot(\DOMDocument $document, string $entityID): \DOMElement
    {
        $root = $document->createElementNS(
            'urn:oasis:names:tc:SAML:2.0:metadata',
            'md:EntityDescriptor'
        );
        $root->setAttribute('entityID', $entityID);
        $root->setAttributeNS(
            AbstractIdP::NS_URI,
            'xmlns:ds',
            'http://www.w3.org/2000/09/xmldsig#'
        );
        $root->setAttributeNS(
            AbstractIdP::NS_URI,
            'xmlns:xsi',
            AbstractIdP::NS_XML_SCHEMA_INSTANCE,
        );
        $root->setAttributeNS(
            AbstractIdP::NS_URI,
            'xmlns:mdui',
            'urn:oasis:names:tc:SAML:metadata:ui'
        );
        return $root;
    }

    /**
     * @param \DOMDocument $document
     *
     * @return \DOMElement
     */
    private function createSPSSODescriptor(\DOMDocument $document): \DOMElement
    {
        $descriptor = $document->createElement('md:SPSSODescriptor');
        $descriptor->setAttribute('protocolSupportEnumeration', AbstractIdP::NS_SAML_PROTOCOL);

        return $descriptor;
    }
    
    /**
     * @param \DOMDocument $document
     * @param string      $X509Certificate
     * @param string      $use
     *
     * @return \DOMElement
     */
    private function createKeyDescriptor(
        \DOMDocument $document,
        string $X509Certificate,
        string $use
    ): \DOMElement {
        $keyDescriptor = $document->createElement('md:KeyDescriptor');
        $keyDescriptor->setAttribute('use', $use);
        
        $keyInfo = $document->createElement('ds:KeyInfo');
        $keyInfo->setAttributeNS(
            AbstractIdP::NS_URI
            ,'xmlns:ds',
            'http://www.w3.org/2000/09/xmldsig#'
        );
        
        $x509Data = $document->createElement('ds:X509Data');
        $x509CertificateNode = $document->createElement('ds:X509Certificate', $X509Certificate);
        
        $x509Data->appendChild($x509CertificateNode);
        $keyInfo->appendChild($x509Data);
        $keyDescriptor->appendChild($keyInfo);
        
        return $keyDescriptor;
    }

    /**
     * @param \DOMDocument $document
     * @param string      $logoutLocation
     *
     * @return \DOMElement
     */
    private function createSingleLogoutService(\DOMDocument $document, string $logoutLocation): \DOMElement
    {
        $singleLogoutService = $document->createElement('md:SingleLogoutService');
        $singleLogoutService->setAttribute('Binding', AbstractIdP::BINDING_HTTP_POST);
        $singleLogoutService->setAttribute('Location', $logoutLocation);

        return $singleLogoutService;
    }

    /**
     * @param \DOMDocument $document
     * @param string      $assertionLocation
     *
     * @return \DOMElement
     */
    private function createAssertionConsumerService(\DOMDocument $document, string $assertionLocation): \DOMElement
    {
        $singleLoginService = $document->createElement('md:AssertionConsumerService');
        $singleLoginService->setAttribute('Binding',AbstractIdP::BINDING_HTTP_POST);
        $singleLoginService->setAttribute('Location', $assertionLocation);

        return $singleLoginService;
    }

    /**
     * @param \DOMDocument $document
     * @param string      $nameIdFormat
     *
     * @return \DOMElement
     */
    private function createNameIdFormat(\DOMDocument $document, string $nameIdFormat): \DOMElement
    {
        return $document->createElement('md:NameIDFormat', $nameIdFormat);
    }

    /**
     * @param \DOMDocument $document
     * @param string      $contactType
     * @param string      $name
     * @param string      $emailAddress
     *
     * @return \DOMElement
     */
    private function createContactPerson(
        \DOMDocument $document,
        string $contactType,
        string $name,
        string $emailAddress
    ): \DOMElement {
        $contactPerson = $document->createElement('md:ContactPerson');
        $contactPerson->setAttribute('contactType',$contactType);
        $contactPerson->appendChild($document->createElement('md:GivenName', $name));
        $contactPerson->appendChild($document->createElement('md:EmailAddress', $emailAddress));

        return $contactPerson;
    }
}
