<?php

interface SPMetadataProviderInterface
{
    /**
     * @param string $entityID
     * @param string $certificate
     * @param string $consumerServiceLocation,
     * @param string $logoutServiceLocation,
     * @param string $nameIdFormat,
     * @param string $contactPersonType
     * @param string $contactPersonName
     * @param string $contactPersonEmailAddress
     *
     * @return string
     */
    public function buildMetadata(
        string $entityID,
        string $certificate,
        string $consumerServiceLocation,
        string $logoutServiceLocation,
        string $nameIdFormat,
        string $contactPersonType,
        string $contactPersonName,
        string $contactPersonEmailAddress
    ): string;
}
