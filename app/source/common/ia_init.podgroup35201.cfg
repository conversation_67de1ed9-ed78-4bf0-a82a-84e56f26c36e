; THIS IS THE CONFIGURATION OVERWRITE FOR pod group 35201

; Tomcat K8s POD for main Dev - dc352
IA_USE_LOCALHOST="0"
IA_TOMCAT_SERVER="https://tomcat-dc352.dev.app.intacct.com"
CHECK_TOMCAT_SERVER="https://tomcat-dc352.dev.app.intacct.com"
FINRPT_TOMCAT_SERVER="https://tomcat-dc352.dev.app.intacct.com"

; The id of the schema where the template companies are saved to
; this is used by the app to determine if it needs to/can use the DBLink to apply the template on a client
IA_TEMPLATE_SERVER_ID=35293

; PDLC-0969-11: Sample Company Trial Environment
; Set IS_SAMPLE_COMPANY_ENVIRONMENT to enable sample company functionality
IS_SAMPLE_COMPANY_ENVIRONMENT = "0"

; Zabbix proxy for ENG01 environment
ZABBIX_ENDPOINT = "https://mgt63.intacct.com/nsca/send_nsca.php"

; the base URL for Application Server
IA_APPLICATION_SERVER="pod-p352.intacct.com"

; Nagios proxy for ENG01 environment
NAGIOS_ENDPOINT = "https://mgt63.intacct.com/nsca/send_nsca.php"

; when a user add a new sandbox from the sandbox page, an email will be sent to the following email
CONTACT_FOR_SANDBOX_REQUEST = "<EMAIL>";

; Set to 0 to disable writing Content Security Policy violations to cspreport.log
IA_ENABLE_CSP_REPORTING = "0"

SFDCHATTER_SFORCE_OAUTH_KEY_FILENAME = "sfchatter.key"
SFDCHATTER_PKG_VERSION = "1.0"
SFDCHATTER_PKG_PREFIX = "intacct_social2"
SFDCHATTER_TRIALFORCE_ADMIN_USER_EMAIL = "<EMAIL>"
SFDCHATTER_TRIALFORCE_OAUTH_KEY_FILENAME = "trialforce.key"
SFDCHATTER_COA_OAUTH_KEY_FILENAME = "trialforce.key"

;Onboard Smart Transaction End Point URL
;We will be pointing the preproduction endpoint to sandbox, bcoz qa auth jwt will not work for STX pre prod environment
STX_ENDPOINT = "https://edocs.pp-fabric.sage.com/api-v2.0"
STX_NOTIFICAION_SUBSCRIPTION_ENDPOINT = "https://subscriptions.pp-fabric.sage.com/api/v1.0"
STX_WEBHOOK_ENDPOINT = 'https://sbc-2f339a22cc6de95bf5-main.intacct.com/ia/xml/webhooks.phtml'

; FilterURL whitelist
IP_WHITE_LIST_HOST_NAMES = 'www-p352.intacct.com,www-p307.intacct.com'

[REPORTING]
; the company to use when generating the RPD
;RPD_CNY=30201001138221 ; 30201001138221 => CRW_RPD
;OBIEE_ADMIN_CNY=30201001138243,30201001138244,30201001138245

; snowflake
SNOW_CONNECTION_STRING="****************************************************************************************************************************************" 
SNOW_USER=svc_main_oracle_rpt 
SNOW_PASSWORD="****"

; ------------------------------------------------------------
; SFDC Settings
; ------------------------------------------------------------
; ------------------------------------------------------------
SFDC_APP_NAMESPACE = "ia_migration"
; ------------------------------------------------------------
SAGE_PEOPLE_APP_NAMESPACE = "ia_pp_release"
; ------------------------------------------------------------

[IA_IP_ACCESS_JPP352]
network = "************"
mask = "32"

[SENDGRID_WEBHOOKS]
; Keys Name Format: since we will have multiple SubUsers, we need to follow a specific pattern
; The format is "SENDGRID_WEBHOOK_PUBLIC_KEY_" + subuser_name
;
; these are the Public Keys generated by SendGrid for validating webhook the request
SENDGRID_WEBHOOK_PUBLIC_KEY_SAGEINTACCT_PERF1 = "****"
SENDGRID_WEBHOOK_PUBLIC_KEY_SAGEINTACCT_PERF2 = "****"
SENDGRID_WEBHOOK_PUBLIC_KEY_SAGEINTACCT_PERF3 = "****"

[SENDGRID_API]
; Keys Name Format: since we will have multiple SubUsers, we need to follow a specific pattern
; The format is "SENDGRID_API_" + key_purpose ("EMAIl_SEND_" or "AUTHENTICATE_") + subuser_name
; For the API key, we may add other actions down the line
;
; These are the api keys used for:
; 1. Authenticating/Validating the Domains
; 2. Sending Emails
SENDGRID_API_EMAIL_SEND_SAGEINTACCT_PERF1   = "****"
SENDGRID_API_AUTHENTICATE_SAGEINTACCT_PERF1 = "****"
SENDGRID_API_EMAIL_SEND_SAGEINTACCT_PERF2   = "****"
SENDGRID_API_AUTHENTICATE_SAGEINTACCT_PERF2 = "****"
SENDGRID_API_EMAIL_SEND_SAGEINTACCT_PERF3   = "****"
SENDGRID_API_AUTHENTICATE_SAGEINTACCT_PERF3 = "****"

[NIS]
; The number of records which will determine if an import is done synchronously or asynchronously
PLATFORM_SYNC_THRESHOLD = 10
PLATFORM_API_URL="https://sage.stage.flatfile.com/api"
PLATFORM_SPACE_URL="https://sage.stage.flatfile.com/s"
DEFAULT_FLATFILE_HEALTHCHECK_ENDPOINT="https://sage.stage.flatfile.com/api/v1/health"

;---------------------------------------------------------------------
; COPILOT GMS Configurations - Start
;---------------------------------------------------------------------
[GMS_SERVER]
GMS_ACCESS_TOKEN_URL = "https://api.gms.pre-production.eu-west-1.sageai.sagecloudops.com/gms/token"
GMS_ACCESS_TOKEN_VALIDATION_URL = "https://api.gms.pre-production.eu-west-1.sageai.sagecloudops.com/gms/copilot/clients/sif/v1/token_info"
GMS_TIMEOUT = 30
GMS_CHAT_WINDOW_URL = "https://api.gms.pre-production.eu-west-1.sageai.sagecloudops.com/gms"
GMS_CHAT_WINDOW_CLIENT = "sif_v1"
GMS_CHAT_TENANT_ID = "IA-THREE-FIVE-TWO"

[BVA_CTRL]
BVA_BASE_URL = "https://api.gms.pre-production.eu-west-1.sageai.sagecloudops.com/gms"
BVA_DATA_UPLOAD_URL = "https://api.gms.pre-production.eu-west-1.sageai.sagecloudops.com/gms/data"
BVA_IMAGE_RETRIEVE_URL_PREFIX = "https://api.gms.pre-production.eu-west-1.sageai.sagecloudops.com/gms/data/"
BVA_IMAGE_RETRIEVE_URL_SUFFIX= "/results"
BVA_TIMEOUT = 300
;Example - BVA_IMAGE_RETRIEVE_URL_PART1/{uniqueUserId}/BVA_IMAGE_RETRIEVE_URL_PART3

[SCI_GMS_API]
SCI_API_AUDIENCE = "SAIL/gms-service-preprod"
;---------------------------------------------------------------------
; COPILOT GMS Configurations - End
;---------------------------------------------------------------------

