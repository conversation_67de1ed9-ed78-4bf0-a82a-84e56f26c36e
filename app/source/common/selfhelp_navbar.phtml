<?php

require_once 'util.inc';
require_once 'cpa_util.inc';

Request::$r->_op = MAX_DUMMY_ID;
$id = Request::$r->id;
$_hlpstr = Request::$r->_hlpstr;
$_tab = Request::$r->_tab;
$uid = Request::$r->uid;

Init();

$MenuItems = [
    "Help" => [ "title" => "Help" ],
    "LC"   => [ "title" => "Learning Center" ],
    "CP"   => [ "title" => "Customer Portal" ],
    "RN"   => [ "title" => "Release Notes" ],
    "CS"   => [ "title" => _("Checks & Supplies") ],
    "HL"   => [ "title" => "Help & Learing Center" ],
];


/**
 * @param array[][] $MenuItems
 */
function setupLearningCenter(&$MenuItems)
{
    // Get configuration, bail if not found
    $lcUrl = GetValueForIACFGProperty('LEARNING_CENTER_URL');
    if ( ! $lcUrl ) {
        return;
    }

    $MenuItems['LC']['href'] = $lcUrl;

}

/**
 * @param array[][] $MenuItems
 */
function setupChecksAndSupplies(&$MenuItems)
{
    // Get configuration, bail if not found
    $url = GetValueForIACFGProperty('CHECKS_AND_SUPPLIES_URL');
    if ( ! $url ) {
        return;
    }

    $MenuItems['CS']['href'] = $url;

}

/**
 * @param array[][] $MenuItems
 */
function setupHelpAndLearningCenter(&$MenuItems)
{
    // Get configuration, bail if not found
    $url = GetValueForIACFGProperty('HELP_AND_TRAINING_CENTER_URL');
    if ( ! $url ) {
        return;
    }
    $url = 'https://' . GetLocalizedDocsUrl() . $url;
    $url = str_replace('localhost', Globals::$g->gPODManager->getCurrentPOD()->getExternalHost(), $url);

    $MenuItems['HT']['href'] = $url;

}

/**
 * @param array[][] $MenuItems
 * @param string    $helpStr
 */
function setupMenuLinks(&$MenuItems, $helpStr)
{
    setupLearningCenter($MenuItems);
    setupChecksAndSupplies($MenuItems);
    setupHelpAndLearningCenter($MenuItems);

    $MenuItems['Help']['href'] = GetHelpFullPath($helpStr);

    if ( IsCPAUser() != 'T' ) {
        $MenuItems['CP']['href'] = GetValueForIACFGProperty("IA_CUSTOMERPORTAL");
    }

    $rnUrl = GetLocalizedDocsUrlPath() . GetValueForIACFGProperty("IA_HC_RELEASENOTES");
    Bean::NFeaturesUrlArgs($rnUrl);
    $MenuItems['RN']['href'] = $rnUrl;
}

setupMenuLinks($MenuItems, $_hlpstr);

// Compute the default menu item
if ( isset($_tab) && ( isset($MenuItems[$_tab]['href']) || isset($MenuItems[$_tab]['script']) ) ) {
    $defaultMenuItemId = $_tab;
} else {
    $defaultMenuItemId = "Help";
}

$MenuItems[$defaultMenuItemId]['selected'] = true;


/*
    Effective with the Fall 2014 release we want to just navigate directly to the page rather then adding our own frame around it;
*/
$url = $MenuItems[$defaultMenuItemId]['href'];
http_redirect($url);
exit();
