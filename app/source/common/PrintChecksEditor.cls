<?php
/**
 * An editor class for the printcheck entity
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */

/**
 * Parent Editor class 
 */

class PrintChecksEditor extends FormEditor
{

    /* @var string $url */
    var $url;
    /* @var array $checksToUpdate */
    var $checksToUpdate;
    /* @var string $multiPageStubDetails */
    var $multiPageStubDetails;

    /**
     * @param array $_params the parameters of the class
     */

    public function __construct($_params = array())
    {
        parent::__construct($_params);
        $this->addVoidAction();
        $this->addConfirmAction();
        $this->addPrintAction();
        $this->url = '';
        $this->multiPageStubDetails = '';

    }

    /**
     * getJavaScriptFileNames
     *
     * @return array
     */
    protected function getJavaScriptFileNames()
    {
        return array(
            '../resources/js/printchecks.js',
            '../resources/js/common_helper.js',
        );
    }

    /**
     * buildDynamicMetadata: This  manipulates ui components
     *
     * @param array $params metadata
     */
    protected function buildDynamicMetadata(&$params)
    {
        if ( IsMultiEntityCompany() && GetContextLocation() == '' ) {
            $found = array();
            self::findElements($params, array('path' => 'SEARCH_LOCATIONID'), EditorComponentFactory::TYPE_FIELD, $found);
            if ( $found ) {
                $found[0]['hidden'] = false;
            }
            if(!IsRestrictedUser()) {
                $found = array();
                self::findElements($params, array('path' => 'ENTITYLEVELCHECKS'), EditorComponentFactory::TYPE_FIELD, $found);
                if ( $found ) {
                    $found[0]['hidden'] = false;
                }
            }
        }
        /* // Don't attach onload search() JS while printing, just to save the checkbox state
        $gRequest = Request::getInstance();
        $print = Request::$r->print;
        if($print != 1) {
            $found = array();
            self::findElements($params, array('id' => 'mainForm'), null, $found);
            $found[0]['events']['load'] = 'search();' . $found[0]['events']['load'];
        } */
        parent::buildDynamicMetadata($params);
    }

    /**
     * mediateDataAndMetadata
     *
     * @param array $obj parameters
     *
     * @return bool|null true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        global $gCheckPeriodSelectText, $gElementMap;
        $opid = Request::$r->_op;
        $reportingPeriods = GetPeriodMap(true, $gCheckPeriodSelectText);
        $view = $this->getView();
        $gManagerFactory =  Globals::$g->gManagerFactory;
        if(!isset($obj['ASOFDATE']) || $obj['ASOFDATE'] == '') {
            $obj['ASOFDATE'] = GetCurrentDate();
        }
        $period = array();
        $view->findComponents(array('path' => 'PERIOD'), EditorComponentFactory::TYPE_FIELD, $period);
        if ( $period[0] ) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $type = $period[0]->getProperty('type');
            $period[0]->setProperty(array('type', 'validvalues'), array_values($reportingPeriods));
            $period[0]->setProperty(array('type', 'validlabels'), array_values($reportingPeriods));
        }
        if($obj['CHECKSTYLE'] == 'B') {
            $matches = array();
            $view->findComponents(array('path' => 'CHECKINGACCOUNT'), EditorComponentFactory::TYPE_FIELD, $matches);
            if ( $matches ) {
                $matches[0]->setProperty('required', false);
            }
            $matches = array();
            $view->findComponents(array('path' => 'NEXTCHECKNUMBER'), EditorComponentFactory::TYPE_FIELD, $matches);
            if ( $matches ) {
                $matches[0]->setProperty('hidden', true);
            }
        }
        if(!IsMCPSubscribed()) {
            // Get the entry grid
            $matches = array();
            $view->findComponents(array('path' => 'CHECKS'), EditorComponentFactory::TYPE_GRID, $matches);
            if(!$matches) {
                return null;
            }
            $entryGridObj = &$matches[0];
            // Remove the columns
            $matches = array();
            $view->findComponents(array('path' => 'C3'), EditorComponentFactory::TYPE_FIELD, $matches);
            foreach ($matches as $match) {
                $entryGridObj->removeChild($match->parent);
            }
        }
        if($obj['ENTITYLEVELCHECKS'] == 'true') {
            $matches = array();
            $view->findComponents(array('path' => 'C13'), EditorComponentFactory::TYPE_FIELD, $matches);
            $matches[0]->parent->setProperty('hidden', false);
        }
        // Once the check is printed, update the checknum in the grid
        $print = Request::$r->print;

	$varChecksToUpdate = is_array($this->checksToUpdate) ? count($this->checksToUpdate) : 0 ;

        if($print == 1 && $varChecksToUpdate <= 200 && (Request::$r->_kNoWarn != true)) {
            if(isset($obj['CHECKINGACCOUNT']) && $obj['CHECKINGACCOUNT'] != '') { 
                $result = $this->GetnextCheckNumber($obj['CHECKINGACCOUNT']);
                if($obj['CHECKSTYLE'] == 'P') {
                    $obj['NEXTCHECKNUMBER'] = $result[0]['CHECK_CURRNO'];
                }
            }
	    $varChecksToUpdate = is_array($this->checksToUpdate)? array_values($this->checksToUpdate): array();  
            $res = $this->GetCheckNumbers($varChecksToUpdate);
           
            //Write login to ftech checknum from DB and replace it with blah
            foreach($this->checksToUpdate as $k => $chk){
                $obj['CHECKS'][$k]['C6'] = $res[$chk];
                $obj['CHECKS'][$k]['CONFORVOID'] = "true";
            }
        }
        //Prefill the Checks per page option with last user session
        $userprefOpkey = $gElementMap[$opid]['key'];
        $memManager = $gManagerFactory->getManager('memorizedreports');
        $res = $memManager->GetUserPref($userprefOpkey);
        if(!empty($res) && !empty($res['PARAMETERS'])){
            $obj['CHECKSPERPAGE'] = $res['PARAMETERS']['CHECKSPERPAGE'];
            if($obj['CHECKSPERPAGE'] == 3){
                $matches = array();
                $view->findComponents(array('path' => 'CHECKSTUB'), EditorComponentFactory::TYPE_FIELD, $matches);
                if ( $matches ) {
                    $matches[0]->setProperty('readonly', true);
                }
                $matches = array();
                $view->findComponents(array('path' => 'VENDORSTUB'), EditorComponentFactory::TYPE_FIELD, $matches);
                if ( $matches ) {
                    $matches[0]->setProperty('readonly', true);
                }
            }
        }
        
        if (!CRESetupManager::isCREJointCheckEnabled()) {
            $view->findAndSetProperty(['path' => 'C14'], ['hidden' => true]);
        }

        return parent::mediateDataAndMetadata($obj);
    }

    /**
     * @param string $acct1
     *
     * @return bool|string|string[][]
     */
    function GetnextCheckNumber($acct1)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $bankaccount = $gManagerFactory->getManager('bankaccount');
        $acct = explode('--', $acct1);
        $result = $bankaccount->getCheckNumber($acct[0]);

        return $result;
    }

    /**
     * @param array $recordNums
     *
     * @return array
     */
    function GetCheckNumbers($recordNums)
    {
        $prrecord = Globals::$g->gManagerFactory->getManager('prrecord');
        $filter = array(
            'selects' => array('DOCNUMBER', 'RECORDNO'),
            'filters' => array(
                array(
                    array('RECORDNO', 'in', $recordNums),
                )
            )
        );

        $res = $prrecord->GetList($filter);
        $result = array();
        foreach($res as $val){
            $result[$val['RECORDNO']] = $val['DOCNUMBER'];
        }
        return $result;
    }
    /**
     * getStandardButtons
     *
     * @param array $state parameters
     *
     * @return array $buttons to display
     */
    public function getStandardButtons($state)
    {
        $buttons = array();
        //$this->setButtonDetails($buttons, 'searchButton', 'searchButton', 'View', '', false, "search()", false);
        $this->setButtonDetails($buttons, Editor_SaveBtnID, 'dobutton', 'IA.PRINT', 'print', true, "print()", true, false, array('print' => 1));
        $this->setButtonDetails($buttons, Editor_SaveBtnID, 'dobutton', 'IA.CONFIRM', 'confirm', true, "confirmOrVoid('confirm')", true, false, array('confirm' => 1));
        $this->setButtonDetails($buttons, Editor_SaveBtnID, 'dobutton', 'IA.VOID', 'void', true, "confirmOrVoid('void')", true, false, array('void' => 1));
        return $buttons;
    }

    /**
     * getCurrentPrintURL
     *
     * @return string url
     */
    protected function getCurrentPrintURL()
    {
        return '';
    }

    /**
     * addPrintAction
     */
    protected function addPrintAction()
    {
        $this->kActionHandlers['print'] = array(
            'handler' => 'ProcessPrintAction',
            'states' => array(
                $this->kShowEditState,
                $this->kShowNewState,
                $this->kShowViewState,
                $this->kEditWarningState,
                $this->kCreateWarningState,
            )
        );
    }

    /**
     * addVoidAction
     */
    protected function addVoidAction()
    {
        $this->kActionHandlers['void'] = array(
            'handler' => 'ProcessVoidAction',
            'states' => array(
                $this->kShowEditState,
                $this->kShowViewState,
                $this->kShowNewState,
            ),
            'csrf' => true,
        );
    }

    /**
     * addConfirmAction
     */
    protected function addConfirmAction()
    {
        $this->kActionHandlers['confirm'] = array(
            'handler' => 'ProcessConfirmAction',
            'states' => array(
                $this->kShowEditState,
                $this->kShowViewState,
                $this->kShowNewState,
            ),
            'csrf' => true,
        );
    }

    /**
     * ProcessVoidAction
     *
     * @param array $_params params
     *
     * @return bool
     */
    protected function ProcessVoidAction(&$_params)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = $this->retrieveObjectFromView($_params, $obj);
        return $this->processRequest($_params, $obj, $dummy);
    }

    /**
     * ProcessConfirmAction
     *
     * @param array $_params params
     *
     * @return bool
     */
    protected function ProcessConfirmAction(&$_params)
    {
        $ok = $this->retrieveObjectFromView($_params, $obj);
        $dummy = [];
        $ok = $ok && $this->processRequest($_params, $obj, $dummy);
        if($ok && $obj['CHECKSTYLE'] == 'P') {
            $obj['RECS'] = $this->multiPageRecords($obj['RECORDS'],$obj['CHECKSTUB'],$obj['VENDORSTUB']);
            // Need to assign the RECS to blank array as for every new request, the old data needs to be removed,
            // otherwise, the PDF will contain old data as well along with the new one. Ticket Ref# : 72339 for more info
            $showdetails = implode(",", $obj['CHECKSTUB']);
            $showdetailedvendstub = implode(",", $obj['VENDORSTUB']);
            if(count($obj['RECS']) > 0) {
                   $records = implode(",", $obj['RECS']);
                   $this->multiPageStubDetails = true;
                   $showEntityChecks = ($obj['ENTITYLEVELCHECKS'] == true) ? 1 : 0;
                   $this->url = $this->getCurrentPrintURL();
                   $this->url .= '&.r=' . $records;
                   $this->url .= '&.showdetails=' . $showdetails;
                   $this->url .= '&.showdetailedvendstub=' . $showdetailedvendstub;
                   $this->url .= '&.showEntityChecks=' . $showEntityChecks;
                   $this->url .= '&.pcopy= 1';
            }
        }
        return $ok;
    }

    /**
     * ProcessPrintAction
     *
     * @param array $_params params
     *
     * @return bool
     */
    protected function ProcessPrintAction(&$_params)
    {
        
        $ok = $this->retrieveObjectFromView($_params, $obj);
        
        $print = Request::$r->print;
        $ok = $ok && $this->processRequest($_params, $obj, $checksArr);
        $records = '';
        if(isset($obj['RECORDS']) && is_array($obj['RECORDS'])) {
            $records = implode(",", $obj['RECORDS']);
        }
        $showdetails = '';
        if(isset($obj['CHECKSTUB']) && is_array($obj['CHECKSTUB'])) {
            $showdetails = implode(",", $obj['CHECKSTUB']);
        }
        $showdetailedvendstub = '';
        if(isset($obj['VENDORSTUB']) && is_array($obj['VENDORSTUB'])) {
            $showdetailedvendstub = implode(",", $obj['VENDORSTUB']);
        }
        $showEntityChecks = ($obj['ENTITYLEVELCHECKS'] == true) ? 1 : 0;
        $sortOrder =  $obj['SORTORD'];
        /** @noinspection PhpUndefinedVariableInspection */
        if ( $ok && $print == 1 && count($checksArr['PRINTCHECK']) <= 200 ) {
            $this->url = $this->getCurrentPrintURL();
            $this->url .= '&.r=' . $records;
            if($obj['CHECKSPERPAGE'] == ONE_CHECK){
                $this->url .= '&.showdetails=' . $showdetails;
                $this->url .= '&.showdetailedvendstub=' . $showdetailedvendstub;
            }
            $this->url .= '&.showEntityChecks=' . $showEntityChecks;
            $this->url .= '&.ordersortby=' . urlencode($sortOrder);
            $this->url .= '&.checksperpage=' . $obj['CHECKSPERPAGE'];
            //$this->url .= '&.j= 1';
            UserReportPrefCommit($obj);
        }
        return $ok;
    }

    /**
     * processRequest
     *
     * @param array $_params   params
     * @param array $obj       the data
     * @param array $checksArr boolean
     *
     * @return bool
     */
    protected function processRequest(
        /** @noinspection PhpUnusedParameterInspection */ &$_params,
        /** @noinspection PhpUnusedParameterInspection */ &$obj,
        /** @noinspection PhpUnusedParameterInspection */ &$checksArr)
    {
        //Let the child class to implement his own stuff.
        return true;
    }

    /**
     * @return array
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        $print = Request::$r->print;
        $vars['warnmsg'] = Request::$r->_warnDuplicateMsg;
        $vars['_kNoWarn'] = Request::$r->_kNoWarn;
        $vars['_kNoDupl'] = Request::$r->_kNoDupl;
        $vars['printURL'] = $this->url;
        $vars['state'] = $this->state;
	$varChecksToUpdate = is_array($this->checksToUpdate) ? count($this->checksToUpdate) : 0 ;
        $vars['doSearch'] = ($print == 1 && $varChecksToUpdate <= 200) ? false : true;
        $vars['multiPageStub'] = $this->multiPageStubDetails;
        return $vars;
    }

    /**
     * @param array $paymentKey
     * @param array $checkStub
     * @param array $vendorStub
     *
     * @return string[][]
     */
    function multiPageRecords($paymentKey,$checkStub,$vendorStub)
    {
        // find the list of payments which needs multiple page to print the stub details
        $stmt = array();
        $stmt[0] = "select parentpymt,recordkey,paymentkey from prentrypymtrecs pymt, prrecordmst pr where pr.cny# = :1  and pymt.cny# = :1 
          and pymt.paymentkey = pr.record# and pr.recordtype in ('pp','pa', 'pi','po')";
        $stmt[1] = GetMyCompany();
        $stmt = PrepINClauseStmt($stmt, $paymentKey, " and parentpymt ");
        $resultSet = QueryResult($stmt);
        $invoices = [];
        foreach($resultSet as $record){
            if(!isset($invoices[$record['PARENTPYMT']])){
                $invoices[$record['PARENTPYMT']] = [];
            }
            if($record['PARENTPYMT'] != $record['PAYMENTKEY'] ){
                if(!in_array($record['PAYMENTKEY'],$invoices[$record['PARENTPYMT']]) ){
                    $invoices[$record['PARENTPYMT']][] = $record['PAYMENTKEY'];
                }
            }
            if(!in_array($record['RECORDKEY'],$invoices[$record['PARENTPYMT']]) ){
                $invoices[$record['PARENTPYMT']][] = $record['RECORDKEY'];
            }
        }
        $lineItemsCount = [];
        if( countArray($checkStub) > 0 || countArray($vendorStub) > 0  ){
            $allInvoiceKeys = [];
            foreach ($checkStub as $pkey){
                if(isset($invoices[$pkey])){
                    $allInvoiceKeys = array_merge($allInvoiceKeys,$invoices[$pkey]);
                }
            }
            foreach ($vendorStub as $pkey){
                if(isset($invoices[$pkey])){
                    $allInvoiceKeys = array_merge($allInvoiceKeys,$invoices[$pkey]);
                }
            }
            $stmt = array();
            $stmt[0] = "select recordkey,count(*) TOTAL from prentry where cny# = :1 and lineitem = 'T'";
            $stmt[1] = GetMyCompany();
            $stmt = PrepINClauseStmt($stmt, $allInvoiceKeys, " and recordkey ");
            $stmt[0] .= " group by recordkey ";
            $lineItemsCount = QueryResult($stmt);
            $lineItemsCount = array_column($lineItemsCount,'TOTAL','RECORDKEY');
        }

        $PaymentKeys = [] ;
        foreach ( $invoices as $paymentKey => $invoice ){
            $invoiceCount = count($invoice);
            if( in_array($paymentKey,$checkStub) || in_array($paymentKey,$vendorStub) ){
                $lineItemCount = 0 ;
                foreach ($invoice as $invKey){
                    $lineItemCount += $lineItemsCount[$invKey];
                }
                if( ($invoiceCount + $lineItemCount ) > 18 ){
                    $PaymentKeys[] = $paymentKey;
                }
            }else{
                if( $invoiceCount > 18){
                    $PaymentKeys[] = $paymentKey;
                }
            }
        }
        return $PaymentKeys;
    }

    /**
     * I18N. Collection of tokens used primarily in JavaScript.
     *
     * @return string[]
     */
    protected function getFormTokens() : array
    {
        $this->textTokens[] = 'IA.DO_YOU_STILL_WISH_TO_CONTINUE';
        $this->textTokens[] = 'IA.SELECT_CHECK_TO_PRINT';
        $this->textTokens[] = 'IA.SELECT_AMEX_CONFIRMATION_TEXT';
        $this->textTokens[] = 'IA.SELECT_CHECK_TO_CONFIRM';
        $this->textTokens[] = 'IA.SELECT_CHECK_TO_VOID';
        $this->textTokens[] = 'IA.FEWER_FILTERS';
        $this->textTokens[] = 'IA.MORE_FILTERS_DOT';
        return parent::getFormTokens();
    }

}

