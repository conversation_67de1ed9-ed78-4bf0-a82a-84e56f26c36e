<?php

class ConfigureIntegrationProcessor
{
    protected int $stepCount;

    protected int $postProcessCount;
    private array $params;
    private array $stepsResults;

    private QueryManager $qm;

    function __construct() {
        //default the no of steps if not set in subclass
        if (!isset($this->stepCount)) {
            $this->stepCount = 1;
        }

        if (!isset($this->postProcessCount)) {
            $this->postProcessCount = 1;
        }

        $this->qm = new QueryManager();
        $this->params = [];
        $this->stepsResults = [];
    }
    protected function initialize(array $parameters) : void {
        $this->params = $parameters;
    }

    public function getParamValue(string $key) : mixed {
        return $this->params[$key];
    }

    public function setParamValue(string $key, mixed $value) : void {
        $this->params[$key] = $value;
    }

    final public function doStepsInOrder() : void {
        //make sure that these function exists in this class or any subclass of this class
        //if the methods are not there the code will throw an exception
        $validationFunctionPrefix = "validate_step_";
        $actualFunctionPrefix = "process_step_";
        $stepCount = $this->getStepCount();

        $allOk = true;
        for ($i = 0; $i < $stepCount; $i++) {
            $ok = true;
            $validationFunc = $validationFunctionPrefix . $i;
            $actualFunc = $actualFunctionPrefix . $i;
            if (method_exists($this, $validationFunc) &&
                method_exists($this, $actualFunc)) {
                $stepValidationResults = call_user_func(array($this, $validationFunc));
                if ($stepValidationResults instanceof StepValidationResults) {
                    $doNext = $stepValidationResults->isDoNextStep() === false;
                } else {
                    throw new IAException("validation step result must be of type StepValidationResults");
                }
                if ($doNext) {
                    $source = $actualFunc;
                    $ok = $this->qm->beginTrx($source);
                    $ok = $ok && call_user_func(array($this, $actualFunc));
                    $ok = $ok && $this->qm->commitTrx($source);
                    if (!$ok) {
                        $this->qm->rollbackTrx($source);
                    }
                }
            } else {
                throw new IAException("Implement all the step functions. [" . $actualFunc . ", " . $validationFunc . "]");
            }
            if (!$ok) {
                $allOk = false;
                break;
            }
        }
        if ($allOk)  {
            //process this step only if all the steps in the process are success
            $postProcessPefix = "post_step_";
            for ($i = 0; $i < $this->postProcessCount; $i++) {
                $postFunc = $postProcessPefix.$i;
                if (method_exists($this, $postFunc)) {
                    call_user_func(array($this,$postFunc));
                } else {
                    throw new IAException("Implement all post process functions. [" . $postFunc . "]");
                }
            }
        }
    }

    final public function validateStepsInOrder(array &$validationResults) : bool {
        $validationFunctionPrefix = "validate_step_";
        $stepCount = $this->getStepCount();
        for ($i = 0; $i < $stepCount; $i++) {
            $validationFunc = $validationFunctionPrefix . $i;
            if (method_exists($this, $validationFunc)) {
                $step_id = 'step_'.$i;
                $identifyFunc = 'identify_step_'. $i;
                if (method_exists($this, $identifyFunc)) {
                    $step_id = call_user_func(array($this, $identifyFunc));
                }
                $stepValidationResults = call_user_func(array($this, $validationFunc));
                if ($stepValidationResults instanceof StepValidationResults) {
                    $validationResults['validate_step'][$i]['result'] = $stepValidationResults->getValidationMessage();
                    $validationResults['validate_step'][$i]['do_next'] = $stepValidationResults->isDoNextStep();
                    $validationResults['validate_step'][$i]['console_message'] = $stepValidationResults->getConsoleMessage();
                    $validationResults['validate_step'][$i]['step_id'] = $step_id;
                    $this->stepsResults[$i] =  $validationResults['validate_step'][$i];
                } else {
                    throw new IAException("validation step result must be of type StepValidationResults");
                }
            } else {
                throw new IAException("Implement all the step functions.[" . $validationFunc . "]");
            }
        }
        return true;
    }

    final public function getValidationResultForStep(int $stepNumber) : string {
        return $this?->stepsResults[$stepNumber]['result'] ?? '';
    }
    final public function isAllDoneNow() : bool{
        $allDone = true;
        foreach ($this->stepsResults as $oneStepResult) {
            if ($oneStepResult['do_next'] === false) {
                $allDone = false;
                break;
            }
        }
        return $allDone;
    }
    protected function identify_step_0() : string {
        return 'sample step id';
    }
    //these are added as sample functions
    protected function validate_step_0() : StepValidationResults {
        $validationResult = new StepValidationResults('validation result', false);
        return $validationResult;
    }

    protected function process_step_0() : bool {
        return true;
    }
    public function getStepCount(): int
    {
        return $this->stepCount;
    }
}
final class StepValidationResults {
    private string $validationMessage;
    private bool $doNextStep;

    private array $consoleMessage;

    public function __construct(string $message = '', bool $doNextStep = false, array $consoleMessage = []) {
        $this->doNextStep = $doNextStep;
        $this->validationMessage = $message;
        $this->consoleMessage = $consoleMessage;
    }

    public function getValidationMessage(): string
    {
        return $this->validationMessage;
    }

    public function isDoNextStep(): bool
    {
        return $this->doNextStep;
    }

    public function getConsoleMessage(): array
    {
        return $this->consoleMessage;
    }
}
