<?
/**
 *   FILE: MemorizedreportspickPicker.cls
 *   AUTHOR: NaveenS
 *   DESCRIPTION:
 *
 *   (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *   This document contains trade secret data that belongs to Intacct
 *   Corporation and is protected by the copyright laws.  Information
 *   herein may not be used, copied or disclosed in whole or in part
 *   without prior written consent from Intacct Corporation.
 */

import('NPicker');

class MemorizedreportspickPicker extends NPicker
{

    function __construct()
    {
        parent::__construct(
            array(
                'entity' => 'memorizedreportspick',
                'pickfield' => 'PICKID',
                'fields' => array('PICKID', 'REPORTTYPE', 'REPORTAUDIENCE', 'PUBLICFLAG'),
                'nofilteronthesefields' => array('PUBLICFLAG'),
            )
        );
    }

    function BuildTable()
    {
        parent::BuildTable();
        $table = &$this->table;
        for ($i = 0; $i < count($table); $i++) {
            $table[$i]["PUBLICFLAG"] = ($table[$i]["PUBLICFLAG"] == 'true') ? 'Yes' : 'No';
        }
    }

    /**
     * @return array
     */
    function BuildQuerySpec()
    {
        $qrySpec = parent::BuildQuerySpec();
        $myuserrec = GetMyUserid();
        $qrySpec['filters'][0][] = array(
            'operator' => 'or',
            'filters' => array(
                array('memorizedreportspick.userkey', '=', $myuserrec),
                array('memorizedreportspick.publicflag', '=', 'T'),
            )
        );
        $packMgr = Globals::$g->gManagerFactory->getManager('package');
        if (($packMgr->IsPackageInstalledAndSigned('REALPAGE_OWNERDIST')) && !CheckAuthorization(GetOperationId('ap/lists/owner/view'), 1) && !CheckAuthorization(GetOperationId('ap/lists/owner/edit'), 1)) {
            $cny = GetMyCompany();
            $subquery = array("
							SELECT 
								NAME 
							FROM 
								REPORTINFO
							WHERE 
								CNY#=$cny AND VENDORKEY IS NOT NULL
								AND USERTEMP# IS NULL 
								AND CATEGORY = 'glfinancial' 
								AND STATUS = 'T'");
            $qrySpec['filters'][0][] = array('PICKID', 'NOT IN SUBQUERY', $subquery);

        }

        return $qrySpec;
    }
}


