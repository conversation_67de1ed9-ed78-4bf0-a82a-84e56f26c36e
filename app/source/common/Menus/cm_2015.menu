<?php
/**
 * cm_2015.menu
 *
 * Cash Management
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 Intacct Corporation -- All Rights Reserved.
 */

/**
 * See MenuConstants.inc for a description of all allowed keys.
 *
 * The array key for a menu item should never be changed. It is used
 * to persist the sort order of a user's favorite menu items. It is
 * not used as the name of the menu in the UI anymore (that's what
 * the MENU_NAME key is for). E.g.:
 *
 * 'FinancialReports' => [                                 // Array key, DO NOT CHANGE
 *     'ReportsHeader' => [                                // Array key, DO NOT CHANGE
 *         MENU_TYPE => MENU_SECTION,                      // Menu section w/icon
 *         MENU_NAME => 'IA.FINANCIAL_REPORTING',          // Internationalized text
 *         MENU_SECTION_ICON => 'icon-financialreports'    // Icon glyph from iafonts.css
 *     ],
 *     'My Stored Reports' => [                            // Array key, do not change
 *         MENU_NAME => 'IA.MY_STORED_REPORTS',            // Internationalized text
 *         MENU_SCRIPT => 'lister.phtml',                  // URL
 *         MENU_KEY => 'gl/reports/reportstore'            // Key from security.inc
 *     ],*
 *
 *  [...]
 */
$cm_menu = [
    'Tasks' => [
        'Tasks' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true
        ],
        'Accounts' => [
            MENU_NAME => 'IA.ACCOUNTS',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Charge Card Accounts' => [
                    MENU_NAME => 'IA.CHARGE_CARD',
                    MENU_FAVNAME => 'IA.CHARGE_CARD_ACCOUNTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/creditcard'
                ],
                'Checking Accounts' => [
                    MENU_NAME => 'IA.CHECKING',
                    MENU_FAVNAME => 'IA.CHECKING_ACCOUNTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/checkingaccount'
                ],
                'Savings Accounts' => [
                    MENU_NAME => 'IA.SAVINGS',
                    MENU_FAVNAME => 'IA.SAVINGS_ACCOUNTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/savingsaccount'
                ],
            ]
        ],
        'Transactions' => [
            MENU_NAME => 'IA.TRANSACTIONS',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Deposits' => [
                    MENU_NAME => 'IA.DEPOSITS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/deposit',
                ],
                'Funds Transfer' => [
                    MENU_NAME => 'IA.FUNDS_TRANSFERS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/fundstransfer',
                ],
                'Manual Payment' => [
                    MENU_NAME => 'IA.MANUAL_PAYMENT',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'ap/lists/apquickpay',
                ],
                'Other Receipts' => [
                    MENU_NAME => 'IA.OTHER_RECEIPTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/otherreceipts',
                ],
                'Bank Interest and Charges' => [
                    MENU_NAME => 'IA.INTEREST_AND_CHARGES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/bankfee'
                ],
                'Bank Transactions' => [
                    MENU_NAME => 'IA.BANK_TRANSACTIONS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/bankaccttxnrecord',
                    MENU_NO_ADD_RECORD => true,
                ],
                'Bank Feeds' => [
                    MENU_NAME => 'IA.BANK_FEEDS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/bankaccttxnfeed',
                ],
            ]
        ],
        'Charge Card' => [
            MENU_NAME => 'IA.CHARGE_CARD',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Charge-Card Transaction' => [
                    MENU_NAME => 'IA.CHARGE_CARD_TRANSACTIONS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/cctransaction'
                ],
                'Charge Payoffs' => [
                    MENU_NAME => 'IA.CHARGE_PAYOFFS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/chargepayoff'
                ],
                'Credit Card Charges and Other Fees' => [
                    MENU_NAME => 'IA.CREDIT_CARD_CHARGES_AND_FEES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/creditcardfee'
                ],
            ]
        ],
    ],
    'Activities' => [
        'Activities' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true,
        ],
        'Reconciliation' => [
            MENU_NAME => 'IA.RECONCILIATION',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Reconcile Bank' => [
                    MENU_NAME => 'IA.BANK',
                    MENU_FAVNAME => 'IA.BANK',
                    MENU_SCRIPT => 'editor.phtml',
                    MENU_KEY => 'cm/lists/bankacctrecon/create'
                ],
                'Reconcile Credit Card' => [
                    MENU_NAME => 'IA.CREDIT_CARD',
                    MENU_FAVNAME => 'IA.CREDIT_CARD',
                    MENU_SCRIPT => 'editor.phtml',
                    MENU_KEY => 'cm/lists/creditacctrecon/create'
                ],
                'View Reconciliation Histories' => [
                    MENU_NAME => 'IA.VIEW_RECONCILIATION_HISTORIES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/activities/reconhistory'
                ],
            ]
        ],
        'Payment files' => [
            MENU_NAME => 'IA.PAYMENT_FILES',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'ACH Payment File' => [
                    MENU_NAME => 'IA.ACH_PAYMENT_FILE',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/achpaymentfile'
                ],
                'Generate ACH File' => [
                    MENU_NAME => 'IA.ACH_FILE_GENERATION',
                    MENU_SCRIPT => 'lister.phtml?.mod=11.CM',
                    MENU_KEY => 'cm/activities/achfilegenerator'
                ],
                'Generate bank payment files' => [
                    MENU_NAME => 'IA.BANK_FILE_GENERATION',
                    MENU_SCRIPT => 'lister.phtml?.mod=11.CM',
                    MENU_KEY => 'cm/activities/bankfilegenerator'
                ],
                'Bank payment files' => [
                    MENU_NAME => 'IA.BANK_PAYMENT_FILES',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/bankfile'
                ],

            ]
        ],
        'Manage Subledger' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.SUBLEDGER',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Open Subledger' => [
                    MENU_NAME => 'IA.OPEN',
                    MENU_FAVNAME => 'IA.OPEN_SUBLEDGER',
                    MENU_SCRIPT => 'manage_prbatch.phtml?.prtype=c&.praction=open',
                    MENU_KEY => 'cm/activities/open_batches'
                ],
                'Close Subledger' => [
                    MENU_NAME => 'IA.CLOSE',
                    MENU_FAVNAME => 'IA.CLOSE_SUBLEDGER',
                    MENU_SCRIPT => 'manage_prbatch.phtml?.prtype=c&.praction=close',
                    MENU_KEY => 'cm/activities/close_batches'
                ],
            ]
        ],
        'More' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.MORE',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Print Payment Copies' => [
                    MENU_NAME => 'IA.PRINT_PAYMENT_COPIES',
                    MENU_SCRIPT => 'reporteditor.phtml?.mod=3.AP',
                    MENU_KEY => 'cm/activities/cmcheckcopy'
                ],
            ]
        ],
    ],
    'Reports' => [
        'ReportsHeader' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true,
        ],
        'Custom Views' => [
            MENU_CATEGORY_HEADER => true,
            MENU_NAME => 'IA.CUSTOM_VIEWS',
        ],
        'Reports' => [
            MENU_NAME => 'IA.REPORTS',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_LINEBREAK => true,
            MENU_CATEGORY_ITEMS => [
                'My Stored Reports' => [
                    MENU_NAME => 'IA.MY_STORED_REPORTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/reports/reportstore'
                ],
                'Memorized Reports' => [
                    MENU_NAME => 'IA.MEMORIZED_REPORTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/memorizedreports',
                    MENU_NO_ADD_RECORD => true
                ],
                'Custom Reports' => [
                    MENU_NAME => 'IA.CUSTOM_REPORTS',
                ],
            ]
        ],
        'ReportsList' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_ITEMS => [
                'Registers' => [
                    MENU_NAME => 'IA.REGISTERS',
                    MENU_POPUPMENUS => [
                        'Bank Register' => [
                            MENU_NAME => 'IA.BANK',
                            MENU_FAVNAME => 'IA.BANK_REGISTER',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'cm/reports/bankregister'
                        ],
                        'Check Register' => [
                            MENU_NAME => 'IA.CHECK',
                            MENU_FAVNAME => 'IA.CHECK_REGISTER',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'cm/reports/checkregister'
                        ],
                        'Deposits Register' => [
                            MENU_NAME => 'IA.DEPOSITS',
                            MENU_FAVNAME => 'IA.DEPOSITS_REGISTER',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'cm/reports/depositsregister'
                        ],
                        'Receipts Register' => [
                            MENU_NAME => 'IA.RECEIPTS',
                            MENU_FAVNAME => 'IA.RECEIPTS_REGISTER',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'cm/reports/receiptsregister'
                        ],
                        'Charge-Card Register' => [
                            MENU_NAME => 'IA.CHARGE_CARD',
                            MENU_FAVNAME => 'IA.CHARGE_CARD_REGISTER',
                            MENU_SCRIPT => 'reporteditor.phtml',
                            MENU_KEY => 'cm/reports/ccregister'
                        ],
                    ]
                ],
                'Cash Balances' => [
                    MENU_NAME => 'IA.CASH_BALANCES',
                    MENU_FAVNAME => 'IA.CASH_BALANCES_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'cm/reports/cmbalance'
                ],
                'Cash Analysis' => [
                    MENU_NAME => 'IA.CASH_ANALYSIS',
                    MENU_FAVNAME => 'IA.CASH_ANALYSIS_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'cm/reports/cashanalysis'
                ],
                'CM Revaluation Report' => [
                    MENU_NAME => 'IA.REVALUATION',
                    MENU_FAVNAME => 'IA.REVALUATION_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'cm/reports/cmreval',
                    MENU_MCP_KEY => 'cm/reports/cmreval'
                ],
                'Adjusted Available Cash' => [
                    MENU_NAME => 'IA.ADJUSTED_AVAILABLE_CASH',
                    MENU_FAVNAME => 'IA.ADJUSTED_AVAILABLE_CASH_REPORT',
                    MENU_SCRIPT => 'reporteditor.phtml',
                    MENU_KEY => 'cm/reports/adjavailablecash'
                ],
            ]
        ]
    ],
    'Setup' => [
        'Setup' => [
            MENU_TYPE => MENU_SECTION,
            MENU_NAME_HIDDEN => true
        ],
        'Configuration' => [
            MENU_NAME => 'IA.CONFIGURATION',
            MENU_FAVNAME => 'IA.CASH_MANAGEMENT_CONFIGURATION',
            MENU_MODULE_CONFIG => 'cm',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_TITLE => true,
        ],
        'Financial Institution' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.FINANCIAL_INSTITUTION',
            MENU_SCRIPT => 'lister.phtml',
            MENU_KEY => 'cm/lists/financialinstitution',
            MENU_CATEGORY_TITLE => true,
        ],
        'Accounts' => [
            MENU_NAME => 'IA.ACCOUNTS',
            MENU_TYPE => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'Charge Card Accounts' => [
                    MENU_NAME => 'IA.CHARGE_CARD',
                    MENU_FAVNAME => 'IA.CHARGE_CARD_ACCOUNTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/creditcard'
                ],
                'Checking Accounts' => [
                    MENU_NAME => 'IA.CHECKING',
                    MENU_FAVNAME => 'IA.CHECKING_ACCOUNTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/checkingaccount'
                ],
                'Savings Accounts' => [
                    MENU_NAME => 'IA.SAVINGS',
                    MENU_FAVNAME => 'IA.SAVINGS_ACCOUNTS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/savingsaccount'
                ],
            ]
        ],
        'Reconciliation rules' => [
            MENU_NAME            => 'IA.RECONCILIATION_RULES',
            MENU_TYPE            => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_ITEMS_NO_AUTO_SORT => true,
            MENU_CATEGORY_ITEMS  => [
                'Rules'   => [
                    MENU_NAME           => 'IA.MATCHING_AND_CREATION_RULES',
                    MENU_FAVNAME        => 'IA.MATCHING_AND_CREATION_RULES',
                    MENU_SCRIPT         => 'lister.phtml',
                    MENU_KEY            => 'cm/lists/banktxnrule',
                ],
                'Ruleset' => [
                    MENU_NAME           => 'IA.RULE_SETS',
                    MENU_FAVNAME        => 'IA.RULE_SETS',
                    MENU_SCRIPT         => 'lister.phtml',
                    MENU_KEY            => 'cm/lists/banktxnruleset',
                ],
                'Rulerun'   => [
                    MENU_NAME           => 'IA.RULE_SET_PERFORMANCE_LOG',
                    MENU_FAVNAME        => 'IA.RULE_SET_PERFORMANCE_LOG',
                    MENU_SCRIPT         => 'lister.phtml',
                    MENU_KEY            => 'cm/lists/banktxnrulerun',
                    MENU_NO_ADD_RECORD  => true,
                ],
                'AssignRules'   => [
                    MENU_NAME           => 'IA.ASSIGNMENT_RULES',
                    MENU_FAVNAME        => 'IA.ASSIGNMENT_RULES',
                    MENU_SCRIPT         => 'lister.phtml',
                    MENU_KEY            => 'cm/lists/banktxnassignrule',
                ],
            ],
        ],
        'Reconciliation txn templates' => [
            MENU_NAME            => 'IA.RECONCILIATION_TXN_TEMPLATES',
            MENU_TYPE            => MENU_CATEGORY,
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS  => [
                'Journal entry templates' => [
                    MENU_NAME           => 'IA.JOURNAL_ENTRY',
                    MENU_FAVNAME        => 'IA.JOURNAL_ENTRY_TEMPLATE',
                    MENU_SCRIPT         => 'lister.phtml',
                    MENU_KEY            => 'cm/lists/cmruleglbatchtmpl',
                ],
                'Credit card transaction templates' => [
                    MENU_NAME           => 'IA.CREDIT_CARD_TRANSACTION',
                    MENU_FAVNAME        => 'IA.CREDIT_CARD_TRANSACTION_TEMPLATES',
                    MENU_SCRIPT         => 'lister.phtml',
                    MENU_KEY            => 'cm/lists/cmrulecctxntmpl',
                ],
                'AR advance' => [
                    MENU_NAME           => 'IA.AR_ADVANCE',
                    MENU_FAVNAME        => 'IA.AR_ADVANCE',
                    MENU_SCRIPT         => 'lister.phtml',
                    MENU_KEY            => 'cm/lists/cmrulearadvancetmpl',
                ]
            ],
        ],
        'More' => [
            MENU_TYPE => MENU_CATEGORY,
            MENU_NAME => 'IA.MORE',
            MENU_CATEGORY_HEADER => true,
            MENU_CATEGORY_ITEMS => [
                'ACH Bank Configurations' => [
                    MENU_NAME => 'IA.ACH_BANK_CONFIGURATIONS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/achbank'
                ],
                'Owner Distribution' => [
                    MENU_NAME => 'IA.OWNER_DISTRIBUTION',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/ownerdist'
                ],
                'Custom Check Formats' => [
                    MENU_NAME => 'IA.CUSTOM_CHECK_FORMATS',
                    MENU_SCRIPT => 'lister.phtml',
                    MENU_KEY => 'cm/lists/customcheckformat'
                ]
            ]
        ]
    ],
];

