<?php

/**
 * Default locale handler for IAMessage
 *
 * Mutliple IAMessage instance might have token id clision
 * while placholders might be having differfent value
 *
 * This can happen in the scenarios when ORM accumulate errors and warnings during validation process
 */
class IAMessageLocaleHandler implements IALocaleHandler
{
    /** @var string|null $token */
    private $token = null;

    /** @var array|null $placeholderObject */
    private $placeholderObject = null; // expected format:  [['name'=>'','value'=>''], ['name'=>'','value'=>'']]

    /** @var array $localeMessageMap */
    private $localeMessageMap = [];

    /** @var bool $toShowFlag */
    private $toShowFlag;

    public function _construct()
    {
        $this->toShowFlag = \IAIssueUtil::showPlaceholders();
    }

    /**
     * handls initial set or reset
     * one handler instance is expected to be bound to one IAMesssage instance only
     * given IAMessage id should never be changed, so add token should only for either
     *  1. initial add
     *  2. reset placeholders whle id should not be changed
     *
     * @param string $token
     * @param array  $placeHolderMap
     *
     * @return void
     * @throws IAMessageSetupException
     */
    public function addToken(string $token, array $placeHolderMap = [], string $localContext='')
    {
        if (null !== $this->token && $token !== $this->token) {
            throw new IAMessageSetupException(__METHOD__ . ': has been bound to token ' . $this->token . ' already. Init a new one for token ' . $token);
        } else if (empty($token)) {
            throw new IAMessageSetupException(__METHOD__ . ': empty token id');
        }
        $this->token = $token;
        $this->placeholderObject = I18N::mapToPlaceholderArray($placeHolderMap);
        $this->localeMessageMap = []; // force a text retake
    }

    /**
     * @param string      $token
     * @param string|null $locale
     * @param string      $localContext
     *
     * @return string|null
     */
    public function getText(string $token, ?string $locale=null, string $localContext='') : ?string
    {
        // will handle $localContext later
        logToFileDebug('localContext: ' . $localContext);

        $locale = $locale ? trim($locale) : I18N::getLocale();

        // get from cache
        if (isset($this->localeMessageMap[$locale])) {
            return $this->localeMessageMap[$locale];
        }

        // not found in cache, so resolve it by invoking I18N function
        $currentLocale = I18N::getLocale();
        I18N::setLocale($locale);
        $text = I18N::getSingleToken($token, $this->placeholderObject ?? []);

        // reset locale back to original
        I18N::setLocale($currentLocale);

        // save to cache
        if (null !== $text) {
            $this->localeMessageMap[$locale] = $text;
        }

        return $text;
    }

    /**
     * return placeholder seems a temp I18N workaround (maybe I am wrong)
     * thus, have a spearate method to set it
     * instead of having it as a input param of getText() (and we need to refactor later)
     *
     * given I18N is a static class, this method should be virtually
     * used with getText automically in consumer logic
     *
     * @param bool $toShow
     *
     * @return void
     */
    public function setToReturnPlaceholder(bool $toShow)
    {
        // no matter $this->toShowFlag is same as $toShow or not
        // we need to explicitly set it to I18N
        // given I18N is a static class
        I18N::setPlaceholderReturn($toShow);
        if ($this->toShowFlag !== $toShow) {
            // force flush existing cached text
            $this->toShowFlag = $toShow;
            $this->localeMessageMap = [];
        }
    }

    /**
     * @return array
     */
    public function getMessageMap() : array
    {
        return $this->localeMessageMap;
    }
}