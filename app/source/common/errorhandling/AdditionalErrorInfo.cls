<?php

/**
 * The AdditionalErrorInfo class is used for supporting I18n in the NextGen UI/UX.
 *
 * <AUTHOR>
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
class AdditionalErrorInfo implements JsonSerializable
{

    const KEY_PROPERTY_SET = 'propertySet';

    private string $messageId;
    private string $uxMessageId;
    private array  $placeholders;
    private array  $propertySet;

    public function __construct(string $messageId, string $uxMessageId, array $placeholders, array $propertySet)
    {
        $this->messageId    = $messageId;
        $this->uxMessageId  = $uxMessageId;
        $this->placeholders = $placeholders;
        $this->propertySet  = $propertySet;
    }

    public function getMessageId() : string
    {
        return $this->messageId;
    }

    // set message id
    public function setMessageId(string $messageId) : void
    {
        $this->messageId = $messageId;
    }

    public function getUxMessageId() : string
    {
        return $this->uxMessageId;
    }

    // set ux message id
    public function setUxMessageId(string $uxMessageId) : void
    {
        $this->uxMessageId = $uxMessageId;
    }

    public function getPlaceholders() : array
    {
        return $this->placeholders;
    }

    // set placeholders
    public function setPlaceholders(array $placeholders) : void
    {
        $this->placeholders = $placeholders;
    }

    public function jsonSerialize() : mixed
    {
        $result = [];
        if ( ! empty($this->messageId) ) {
            $result['messageId'] = $this->messageId;
        }
        if ( ! empty($this->uxMessageId) ) {
            $result['uxMessageId'] = $this->uxMessageId;
        }
        $result['placeholders'] = empty($this->placeholders) ? new stdClass() : $this->placeholders;
        $result[self::KEY_PROPERTY_SET] = empty($this->propertySet) ? new stdClass() : $this->propertySet;

        return $result;
    }

    /**
     * @return array
     */
    public function getPropertySet() : array
    {
        return $this->propertySet;
    }

    /**
     * @param array $propertySet
     *
     * @return AdditionalErrorInfo
     */
    public function setPropertySet(array $propertySet) : AdditionalErrorInfo
    {
        $this->propertySet = $propertySet;

        return $this;
    }
}