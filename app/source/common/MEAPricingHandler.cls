<?php
/**
 *    FILE: MEAPricingHandler.cls
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2016, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
abstract class MEAPricingHandler
{
    const MEA_PRICELIST_ENTITY_VSOE = 'vsoeitempricelistentry';
    const GENERIC_CORRECTION_MSG = 'Edit the MEA amount(s) for the applicable contract line(s)';

    /** @var bool $hasWarnedGenericCorectionMsg */
    protected $hasWarnedGenericCorectionMsg = false;
    /** @var bool $hasWarnedGenericHelp */
    protected $hasWarnedGenericHelp = false;

    /**
     * @param string   $priceListKey
     * @param string[] $itemIds
     * @param string   $currency
     * @param string   $startDate
     * @param string   $priceListName
     *
     * @return MEAItemPricingInfo[]
     * @throws IAException
     */
    final public static function getPriceListInfoForItem($priceListKey, $itemIds, $currency, $startDate, $priceListName)
    {
        $meaPricingInfos = array();

        foreach ( $itemIds as $itemId ) {
            $meaEMValues = null;
            if ($startDate) {
                $resultValues = self::queryForFVItems($startDate, $itemId, $currency, $priceListKey);
                if ( $resultValues !== false && count($resultValues) == 1 ) {
                    $meaEMValues = $resultValues[0];
                }
            }
            if ($meaEMValues === null) {
                $resultValues = self::queryForNonFVItems($itemId, $currency, $priceListKey);
                if ( $resultValues !== false && count($resultValues) == 1 ) {
                    $meaEMValues = $resultValues[0];
                } else {
                    throw IAException::newIAException('CN-1666',"There is no active MEA price list entry for item '$itemId' in '$currency' currency in the '$priceListName' MEA price list." , ['ITEM_ID' => $itemId, 'CURRENCY' => $currency, 'PRICE_LIST_NAME' => $priceListName]);
                }
            }
            if ( $meaEMValues !== null ) {
                $thisItemMeaPricingInfo = new MEAItemPricingInfo();
                $thisItemMeaPricingInfo->setEmValues($meaEMValues);
                $meaPricingInfos[] = $thisItemMeaPricingInfo;
            }
        }
        return $meaPricingInfos;
    }

    /**
     * @param bool $isPreview
     * @param string $handleOpenEntries
     */
    final public function processMEA($isPreview, $handleOpenEntries)
    {
        //add steps for the MEA process
        $this->innerProcessMEA($isPreview, $handleOpenEntries);
        //all needs to be method calls to be implmented in the derived classes
        
    }

    /**
     * @param bool $isPreview
     * @param string $handleOpenEntries
     */
    abstract protected function innerProcessMEA($isPreview, $handleOpenEntries);

    /**
     * @param MEABundlePricingParams[] $meaBundlePricingParams
     *
     * @throws IAException
     */
    final protected function doBundleMEACalculations($meaBundlePricingParams)
    {
        $doRounding = true;
        $bundleTotal = 0;
        $bundleTotalNonDebook = 0;
        $hasDebookLine = false;
        $baseBundleTotal = 0;
        $meaBundleTotal = 0;
        $overriddenMeaBundleTotal = 0;
        //$calcPrecision = $meaBundlePricingParams->getPrecisionForCalc();
        $bundleNeedsResidual = false;
        $noFVItemTotal = 0;
        $areAllItemsResidual = true;
        $precisionQty = ContractUtil::INTERMEDIATE_PRECISION;
        $precisionCalc = ContractUtil::INTERMEDIATE_PRECISION;
        foreach ( $meaBundlePricingParams as $oneEntry ) {
            $meaPricingInfo = $oneEntry->getMeaItemPricingInfo();

            if ( $oneEntry->isResidualItem() === true ) {
                $bundleNeedsResidual = true;
                $noFVItemTotal = ibcadd($noFVItemTotal, $oneEntry->getLineAmount(), ContractUtil::INTERMEDIATE_PRECISION, true);
            }

            $areAllItemsResidual = $areAllItemsResidual && $oneEntry->isResidualItem();

            $meaAmount = $meaPricingInfo->getMeaItemPrice();
            $flatAmount = $oneEntry->getLineAmount();
            $baseFlatAmount = $oneEntry->getLineBaseAmount();
            //$unitPrice = $oneEntry->getLineUnitPrice();
            $unitQuantity = $oneEntry->getLineUnitQuantity();
            $precisionCalc = $oneEntry->getPrecisionForCalc();
            $precisionQty = $oneEntry->getPrecisionForQty();
            $fvCompuationMemo = "";
            $fairValueUnitPrice = null;

            if ( $oneEntry->getLineIsDiscountType() ) {
                $meaAmount = 0;
            }
            if ( $unitQuantity === null ) {
                $unitQuantity = 1;
            }

            if ( $meaPricingInfo->getMeaUsePriceBands() === true ) {
                $fairValueUnitPrice = $oneEntry->getFVPriceForPriceBands( $fvCompuationMemo);
            } else if ( $meaPricingInfo->isPercentBased() === true ) {
                $unitQuantity = $oneEntry->getLineIsDebookType() ? -1 : 1;
                $innerPercent = ibcdiv($meaPricingInfo->getMeaItemPrice(),'100', $precisionCalc, true);
                if ( $meaPricingInfo->isPercentOnFairValue() === true ) {
                    $totalForPercent = $meaPricingInfo->getFvPriceByCategory();
                    if ( $totalForPercent === null ) {
                        throw new IAException("Fair value cannot be computed.", 'CN-1667');
                    }
                    $fairValueUnitPrice = ibcmul($totalForPercent,$innerPercent, $precisionCalc, true);
                    $fvCompuationMemo = $meaPricingInfo->getMeaItemPrice() . "% of (" . $meaPricingInfo->getFvPriceMemo() . ")";
                } else if ( $meaPricingInfo->isPercentOnSalesPrice() === true ) {
                    $totalForPercent = $meaPricingInfo->getSalesPriceByCategory();
                    if ( $totalForPercent === null ) {
                        throw new IAException("Fair value cannot be computed. The total of the extended price is not present. Most likely all the items in the bundle are percent based.",'CN-1668');
                    }

                    $fairValueUnitPrice = ibcmul($totalForPercent,$innerPercent, $precisionCalc, true);
                    $fvCompuationMemo = $meaPricingInfo->getMeaItemPrice() . "% of (" . $meaPricingInfo->getSalesPriceMemo() . ")";
                }
            } else {
                $fairValueUnitPrice = $meaAmount;
            }
            $fairValueExtPrice = ibcmul($fairValueUnitPrice,
                                        $unitQuantity,
                                        $precisionCalc,
                                        true
            );
            $meaPricingInfo->setFairValueUnitPrice($fairValueUnitPrice);
            $meaPricingInfo->setFairValueExtPrice($fairValueExtPrice);
            $meaPricingInfo->setFvComputationMemo($fvCompuationMemo);
            $meaBundleTotal = ibcadd($meaBundleTotal, $fairValueExtPrice, $precisionCalc, true);
            $meaOverriddenAmount = $oneEntry->getMeaOverriddenAmount() ?? $fairValueExtPrice;
            $overriddenMeaBundleTotal = ibcadd($overriddenMeaBundleTotal, $meaOverriddenAmount, $precisionCalc, true);

            //need to see where this can be moved to
            $bundleTotal = ibcadd($bundleTotal, $flatAmount, $precisionCalc, true);
            if (!$oneEntry->getLineIsDebookType()) {
                $bundleTotalNonDebook = ibcadd($bundleTotalNonDebook, $flatAmount, $precisionCalc, true);
            } else {
                $hasDebookLine = true;
            }
            $baseBundleTotal = ibcadd($baseBundleTotal, $baseFlatAmount, $precisionCalc, true);
        }

        if ($bundleTotal < 0) {
            throw new IAException("The total extended transaction value of the bundled contract lines is less than 0.00. Either add or remove contract lines from the bundle or edit the flat/fixed amount for the applicable contract lines to achieve a total extended transaction value that is equal to or greater than 0.00.", 'CN-1669');
        } elseif ($bundleTotalNonDebook == 0) {
            throw new IAException("The total extended transaction value of the bundled contract lines is 0.00. Either add or remove contract lines from the bundle or edit the flat/fixed amount for the applicable contract lines to achieve a total extended transaction value that is greater than 0.00.", 'CN-1670');
        }

        $residualAmountToBeDistributed = 0;
        $skipPercentCalcuation = false;

        if ( $bundleNeedsResidual === true ) {
            if ( $areAllItemsResidual === true ) {
                throw new IAException('MEA allocations cannot be calculated for a bundle with all residual items.', 'CN-1671');
            }
            if ( $bundleTotal >= $meaBundleTotal ) {
                //in this case delta needs to be allocated to the residual items
                $residualAmountToBeDistributed = ibcsub($bundleTotal, $meaBundleTotal, ContractUtil::AMOUNT_PRECISION, true);
                $skipPercentCalcuation = true;
            }
        }

        //throw error here if bundle total is zero
        $totalAllocated = 0;
        $totalBaseAllocated = 0;
        $bundleHasDifferentExchangeRate = false;
        $lastExchangeRate = null;
        foreach ( $meaBundlePricingParams as & $oneEntry ) {
            if ( $lastExchangeRate != null && $oneEntry->getExchangeRate() != $lastExchangeRate ) {
                $bundleHasDifferentExchangeRate = true;
                break;
            }
            $lastExchangeRate = $oneEntry->getExchangeRate();
        }
        //this section calculated the allocation amount and sets the return parameters
        /* @var MEABundlePricingParams $oneEntry */
        foreach ( $meaBundlePricingParams as & $oneEntry ) {
            /* @var MEAItemPricingInfo $meaPricingInfo */
            $meaPricingInfo = $oneEntry->getMeaItemPricingInfo();
            $fairValueExtPrice = $meaPricingInfo->getFairValueExtPrice();
            $meaTxnAllocation = null;
            $meaPercent = null;
            if ( $bundleNeedsResidual === true ) {
                if ( $oneEntry->isResidualItem() === true ) {
                    if ($residualAmountToBeDistributed == 0 || $overriddenMeaBundleTotal == $bundleTotal) {
                        if ($overriddenMeaBundleTotal == $bundleTotal) {
                            $doRounding = false;
                        }
                        $meaTxnAllocation = 0;
                        $fvCompuationMemoResidual = 'Residual amount for this allocation is 0';
                        $meaPricingInfo->setFvComputationMemo($fvCompuationMemoResidual);
                    } elseif ($noFVItemTotal == 0) {
                        $meaTxnAllocation = null;
                        $doRounding = false;
                        $errorMsg = "This MEA has a residual amount of $residualAmountToBeDistributed that can't be allocated.";
                        $errorInfo = new ContractI18nErrorInfo('CN-1673',
                            ['RESIDUAL_AMOUNT_TO_DISTRIBUTE' => $residualAmountToBeDistributed],
                            $errorMsg);
                        $correctionMsg = self::GENERIC_CORRECTION_MSG . " to allocate the residual amount as desired.";
                        $this->warningOrException(!$this->isPreview, $errorInfo, $correctionMsg);
                    } else {
                        $percentOfRes = ibcdiv($oneEntry->getLineAmount(), $noFVItemTotal, ContractUtil::INTERMEDIATE_PRECISION, true);
                        $perMemo = ibcmul($percentOfRes,'100', ContractUtil::AMOUNT_PRECISION, true);
                        $meaTxnAllocation = ibcmul($residualAmountToBeDistributed, $percentOfRes, ContractUtil::AMOUNT_PRECISION, true);
                        $fvCompuationMemoResidual = 'Residual of ' . $residualAmountToBeDistributed . ' is allocated with ' . $perMemo . '%';
                        $meaPricingInfo->setFvComputationMemo($fvCompuationMemoResidual);
                    }
                    $meaPercent = 0;
                } else if ( $skipPercentCalcuation === true ){
                    $meaTxnAllocation = ibcmul($fairValueExtPrice,'1', ContractUtil::AMOUNT_PRECISION, true);
                    $meaPercent = 0;
                }
            }

            $innerMEAPercent = null;
            if ( $skipPercentCalcuation === false ) {
                $innerMEAPercent = ibcdiv($fairValueExtPrice, $meaBundleTotal, $precisionCalc, true);
                $meaPercent = ibcmul($innerMEAPercent, '100', $precisionCalc, true);
                $meaTxnAllocation = ibcmul($innerMEAPercent, $bundleTotal, $precisionQty, true);
            }

            $totalAllocated = ibcadd($totalAllocated, $meaTxnAllocation, $precisionQty, true);

            $oneEntry->setMeaAllocation($meaTxnAllocation);
            $oneEntry->setMeaCalculatedAmount($meaTxnAllocation);
            $oneEntry->setMeaPercent($meaPercent);
            
            if ( $oneEntry->getExchangeRate() !== null ) {
                if ( $innerMEAPercent != null && $bundleHasDifferentExchangeRate === false ) {
                    $meaBaseAllocation = ibcmul($innerMEAPercent, $baseBundleTotal, $precisionQty, true);
                } else {
                    $exchRate = $oneEntry->getExchangeRate();
                    $meaBaseAllocation = ibcmul($meaTxnAllocation, $exchRate, $precisionQty, true);
                }

                $oneEntry->setMeaBaseAllocation($meaBaseAllocation);
                //$oneEntry->setExchangeRate(null);
                $totalBaseAllocated = ibcadd($totalBaseAllocated, $meaBaseAllocation, $precisionQty, true);
            } else {
                $oneEntry->setMeaBaseAllocation($meaTxnAllocation);
            } 
        }
        
        if ( $totalAllocated == 0 && $bundleTotal != 0 ) {
            $doRounding = false;
            if (!$hasDebookLine) {
                throw new IAException(
                    'The total MEA amount for the contract lines selected to be included in the bundle is 0.00, ' .
                    'so the allocation cannot be calculated. Select at least one contract line that has a positive fair value price'
                ,'CN-1672');
            }
        }

        if ($doRounding) {
            ContractUtil::correctObjectRoundingErrors($bundleTotal, $meaBundlePricingParams, $bundleHasDifferentExchangeRate);
            ContractUtil::correctObjectRoundingBaseAmountErrors($baseBundleTotal, $meaBundlePricingParams, $bundleHasDifferentExchangeRate);
        }
    }

    /**
     * @param bool   $throwException
     * @param string $errorMsg
     * @param string $correctionMsg
     *
     * @throws IAException
     */
    protected function warningOrException($throwException, $errorInfo, $correctionMsg=null)
    {
        if ($correctionMsg === null) {
            $correctionMsg = self::GENERIC_CORRECTION_MSG . '.';
            $this->hasWarnedGenericCorectionMsg = true;
        } elseif (strpos($correctionMsg, self::GENERIC_CORRECTION_MSG)) {
            $this->hasWarnedGenericCorectionMsg = true;
        }
        if ($throwException) {
            throw IAException::newIAException($errorInfo->getErrorId(), $errorInfo->getErrorMessage(),
                $errorInfo->getPlaceholders());
        } else {
            $errMsg = "$errorInfo->getErrorMessage() $correctionMsg";
            Globals::$g->gErr->AddWarning($errMsg);
        }
    }

    /**
     * @return MEABundlePricingParams
     */
    public static function getMEABundlePricingParamsObject()
    {
        return new MEABundlePricingParams();
    }

    /**
     * @param string $startDate
     * @param string $itemId
     * @param string $currency
     * @param string $priceListKey
     *
     * @return bool|string[][]
     */
    private static function queryForFVItems($startDate, $itemId, $currency, $priceListKey)
    {
        $meaPriceListQuery = "select * from 
                                (select 
                                    apl.name,
                                    aipl.itemkey,
                                    aipl.currency,
                                    aipl.itmprclsttyp,
                                    aipl.percentbase,
                                    aipl.percentof,
                                    aipl.usepricebands, 
                                    aipl.pricebandtype,
                                    aipl.pricebandrule,
                                    aiple.value,
                                    aiple.bandup, 
                                    aiple.banddown, 
                                    aiple.markup, 
                                    aiple.markdown
                                from allocitemprclst aipl
                                    inner join allocprclst apl on (aipl.allocprclstkey = apl.record# and aipl.cny# = apl.cny#)
                                    inner join allocitemprclstentry aiple  on (aipl.record# = aiple.allocitemprclstkey and aipl.cny# = aiple.cny#)
                                where 
                                    apl.pricelisttype = 'M'
                                    and apl.record# = :1
                                    and apl.status = 'T'
                                    and aipl.status = 'T'
                                    and aipl.version is null
                                    and aiple.startdate <= :2
                                    and aipl.itemkey = :3
                                    and aipl.currency = :4
                                    and aipl.cny# = :5
                                order by aiple.startdate desc)
                            where rownum = 1";
        $queryForItemPriceList = array(
            'QUERY' => $meaPriceListQuery,
            'ARGTYPES' => array('integer', 'date', 'text', 'text', 'integer')
        );

        $priceListManager = Globals::$g->gManagerFactory->getManager('contractmeapricelist');
        $resultValues = $priceListManager->DoCustomQuery($queryForItemPriceList, [ $priceListKey, $startDate, $itemId, $currency, GetMyCompany()]);
        return $resultValues;
    }

    /**
     * @param string $itemId
     * @param string $currency
     * @param string $priceListKey
     *
     * @return bool|string[][]
     */
    private static function queryForNonFVItems($itemId, $currency, $priceListKey)
    {
        $meaPriceListQuery = "select * from 
                                (select 
                                    apl.name,
                                    aipl.itemkey,
                                    aipl.currency,
                                    aipl.itmprclsttyp,
                                    aipl.percentbase,
                                    aipl.percentof,
                                    aipl.usepricebands, 
                                    aipl.pricebandtype,
                                    aipl.pricebandrule
                                from allocitemprclst aipl
                                    inner join allocprclst apl on (aipl.allocprclstkey = apl.record# and aipl.cny# = apl.cny#)
                                where 
                                    apl.pricelisttype = 'M'
                                    and apl.status = 'T'
                                    and aipl.status = 'T'
                                    and apl.record# = :1
                                    and aipl.version is null
                                    and aipl.itemkey = :2
                                    and aipl.currency = :3
                                    and aipl.cny# = :4)
                            where rownum = 1";
        $queryForItemPriceList = array(
            'QUERY' => $meaPriceListQuery,
            'ARGTYPES' => array('integer', 'text', 'text', 'integer')
        );

        $priceListManager = Globals::$g->gManagerFactory->getManager('contractmeapricelist');
        $resultValues = $priceListManager->DoCustomQuery($queryForItemPriceList, [ $priceListKey, $itemId, $currency, GetMyCompany()]);
        return $resultValues;
    }
}

class MEABundlePricingParams implements IRoundingErrorEntry, IRoundingBaseAmountErrorEntry 
{
    /** @var string $lineIdentifier  */
    private $lineIdentifier;
    /** @var float|string $lineFlatAmount */
    private $lineFlatAmount;
    /** @var string $multiplierDetail */
    private $multiplierDetail;
    /** @var  string|float $lineAmount */
    private $lineAmount;
    /** @var  string|float $lineBaseAmount */
    private $lineBaseAmount;
    /** @var  string|float $lineUnitPrice */
    private $lineUnitPrice;
    /** @var  string|int $lineUnitQuantity */
    private $lineUnitQuantity;
    /** @var string $lineType */
    private $lineType;
    /** @var  MEAItemPricingInfo $meaItemPricingInfo */
    private $meaItemPricingInfo;
    /** @var  string|float $meaPercent */
    private $meaPercent;
    /** @var  string|float $meaAllocation */
    private $meaAllocation;
    /** @var  string|float $meaBaseAllocation */
    private $meaBaseAllocation;
    /** @var string|float $meaCalculatedAmount */
    private $meaCalculatedAmount;
    /** @var string|float $meaOverriddenAmount */
    private $meaOverriddenAmount;
    /** @var  string|float $exchangeRate */
    private $exchangeRate;
    /* @var float $lineMultiplier */
    private $lineMultiplier;
    /* @var float $rawLineQuantity */
    private $rawLineQuantity;
    /** @var int $precisionForCalc */
    private $precisionForCalc = 10;
    /** @var int $precisionForQty */
    private $precisionForQty = 2;

    /* interface methods*/

    /**
     * @return float
     */
    public function getAmount()
    {
        return $this->getMeaAllocation();
    }

    /**
     * @return float
     */
    public function getBaseAmount()
    {
        return $this->getMeaBaseAllocation();
    }

    /**
     * @param float $amt
     */
    public function setAmount($amt)
    {
        $this->setMeaAllocation($amt);
    }

    /**
     * @param float $amt
     */
    public function setBaseAmount($amt)
    {
        $this->setMeaBaseAllocation($amt);
    }

    /**
     * @return string
     */
    public function getLineIdentifier()
    {
        return $this->lineIdentifier;
    }

    /**
     * @param string $lineIdentifier
     */
    public function setLineIdentifier($lineIdentifier)
    {
        $this->lineIdentifier = $lineIdentifier;
        
    }

    /**
     * @return string
     */
    public function getLineAmount()
    {
        return $this->lineAmount;
    }

    /**
     * @param string $lineAmount
     */
    public function setLineAmount($lineAmount)
    {
        $this->lineAmount = $lineAmount;
        
    }

    /**
     * @return MEAItemPricingInfo
     */
    public function getMeaItemPricingInfo()
    {
        return $this->meaItemPricingInfo;
    }

    /**
     * @param MEAItemPricingInfo $meaItemPricingInfo
     */
    public function setMeaItemPricingInfo($meaItemPricingInfo)
    {
        $this->meaItemPricingInfo = $meaItemPricingInfo;
        
    }

    /**
     * @return float
     */
    public function getMeaPercent()
    {
        return $this->meaPercent;
    }

    /**
     * @param float $meaPercent
     */
    public function setMeaPercent($meaPercent)
    {
        $this->meaPercent = $meaPercent;
        
    }

    /**
     * @return float
     */
    public function getMeaAllocation()
    {
        return $this->meaAllocation;
    }

    /**
     * @param float $meaAllocation
     */
    public function setMeaAllocation($meaAllocation)
    {
        $this->meaAllocation = $meaAllocation;
        
    }

    /**
     * @return float
     */
    public function getExchangeRate()
    {
        return $this->exchangeRate;
    }

    /**
     * @param float $exchangeRate
     */
    public function setExchangeRate($exchangeRate)
    {
        $this->exchangeRate = $exchangeRate;
    }

    /**
     * @return int
     */
    public function getPrecisionForCalc()
    {
        return $this->precisionForCalc;
    }

    /**
     * @param int $precisionForCalc
     */
    public function setPrecisionForCalc($precisionForCalc)
    {
        $this->precisionForCalc = $precisionForCalc;
    }

    /**
     * @return float
     */
    public function getMeaBaseAllocation()
    {
        return $this->meaBaseAllocation;
    }

    /**
     * @param float $meaBaseAllocation
     */
    public function setMeaBaseAllocation($meaBaseAllocation)
    {
        $this->meaBaseAllocation = $meaBaseAllocation;
    }

    /**
     * @return float
     */
    public function getLineUnitPrice()
    {
        return $this->lineUnitPrice;
    }

    /**
     * @param float $lineUnitPrice
     */
    public function setLineUnitPrice($lineUnitPrice)
    {
        $this->lineUnitPrice = $lineUnitPrice;
    }

    /**
     * @return int
     */
    public function getLineUnitQuantity()
    {
        return $this->lineUnitQuantity;
    }

    /**
     * @param int $lineUnitQuantity
     */
    public function setLineUnitQuantity($lineUnitQuantity)
    {
        $this->lineUnitQuantity = $lineUnitQuantity;
    }

    /**
     * @return bool
     */
    public function getLineIsDiscountType() : bool
    {
        return $this->lineType === ContractDetailManager::LINETYPE_DISCOUNT;
    }

    /**
     * @return bool
     */
    public function getLineIsDebookType() : bool
    {
        return $this->lineType === ContractDetailManager::LINETYPE_DEBOOK;
    }

    /**
     * @param string $lineType
     */
    public function setLineType(string $lineType)
    {
        $this->lineType = $lineType;
    }

    /**
     * @return int
     */
    public function getPrecisionForQty()
    {
        return $this->precisionForQty;
    }

    /**
     * @param int $precisionForQty
     */
    public function setPrecisionForQty($precisionForQty)
    {
        $this->precisionForQty = $precisionForQty;
    }

    /**
     * @return float
     */
    public function getLineBaseAmount()
    {
        return $this->lineBaseAmount;
    }

    /**
     * @param float $lineBaseAmount
     */
    public function setLineBaseAmount($lineBaseAmount)
    {
        $this->lineBaseAmount = $lineBaseAmount;
    }

    /**
     * @return float
     */
    public function getLineMultiplier()
    {
        return $this->lineMultiplier;
    }

    /**
     * @param float $lineMultiplier
     */
    public function setLineMultiplier($lineMultiplier)
    {
        $this->lineMultiplier = $lineMultiplier;
    }

    /**
     * @param float|string $meaPrice
     *
     * @return float|string
     */
    public function getExtendedMeaPrice( $meaPrice )
    {
        $lineQty = $this->lineUnitQuantity ?? 1;

        return ibcmul($meaPrice, $lineQty, $this->precisionForCalc, true);
    }

    /**
     * @return float|string
     */
    public function getLineFlatAmount()
    {
        return $this->lineFlatAmount;
    }

    /**
     * @param float|string $lineFlatAmount
     */
    public function setLineFlatAmount($lineFlatAmount)
    {
        $this->lineFlatAmount = $lineFlatAmount;
    }

    /**
     * @return string
     */
    public function getMultiplierDetail()
    {
        return $this->multiplierDetail;
    }

    /**
     * @param string $multiplierDetail
     */
    public function setMultiplierDetail($multiplierDetail)
    {
        $this->multiplierDetail = $multiplierDetail;
    }

    /**
     * @return bool
     */
    public function isResidualItem()
    {
        return $this->getMeaItemPricingInfo()->isResidualItem();
    }

    /**
     * @param string $fvCompuationMemo
     *
     * @return float
     */
    public function getFVPriceForPriceBands( &$fvCompuationMemo )
    {
        $fairValueUnitPrice = null;
        $meaPricingInfo = $this->getMeaItemPricingInfo();
        $meaAmount = $meaPricingInfo->getMeaItemPrice();
        $unitPrice = $this->getLineUnitPrice();
        $precisionCalc = $this->getPrecisionForCalc();
        $fvCompuationMemo = "";

        if ($this->getLineIsDiscountType()) {
            $meaAmount = 0;
        }

        if ( $unitPrice == null ) {
            $unitPrice = $this->getLineFlatAmount();
        }


        $fairValuePriceBandRule = $meaPricingInfo->getFairValuePriceBandRule();


        $pbRule = $fairValuePriceBandRule === 'FV' ? "Fair value" : "Nearest boundary";
        $upperBandValue = "";
        $lowerBandValue = "";
        if ( $meaPricingInfo->isPriceBandAmountBased() ) {
            $upperBandValue = $meaPricingInfo->getMeaBandUp();
            $lowerBandValue = $meaPricingInfo->getMeaBandDown();
        } else if ( $meaPricingInfo->isPriceBandPercentBased() === true  ) {
            //TODO check here to see if the values needs to be calculated here
            $fairValueMarkUp = $meaPricingInfo->getMeaMarkUp();
            $fairValueMarkDown = $meaPricingInfo->getMeaMarkDown();
            $upperBandValue = ibcadd($meaAmount,ibcmul($meaAmount,ibcdiv($fairValueMarkUp,'100',$precisionCalc,true),$precisionCalc, true),$precisionCalc,true);
            $lowerBandValue = ibcsub($meaAmount,ibcmul($meaAmount,ibcdiv($fairValueMarkDown,'100',$precisionCalc,true),$precisionCalc, true),$precisionCalc,true);
        }
        $fvCompuationMemo = "Lower limit = $lowerBandValue, Upper limit = $upperBandValue. Rule when outside range = $pbRule";
        $fairValuePriceForComparison = $unitPrice;
        $isFVPriceWithinBands = false;
        if ( $lowerBandValue <= $fairValuePriceForComparison && $fairValuePriceForComparison <= $upperBandValue ) {
            $isFVPriceWithinBands = true;
        }

        if ( $isFVPriceWithinBands === true ) {
            $fairValueUnitPrice = $unitPrice;
        } else {
            if ( $fairValuePriceBandRule == 'NB' ) {
                //code for nearest boundry, to adjust the FV price and FV Ext Price
                //and re-calculate the FV percentage
                if ( $fairValuePriceForComparison > $upperBandValue ) {
                    $fairValueUnitPrice = $upperBandValue;
                } else if ( $fairValuePriceForComparison < $lowerBandValue ) {
                    $fairValueUnitPrice = $lowerBandValue;
                }
            } else if ( $fairValuePriceBandRule == 'FV' ) {
                $fairValueUnitPrice = $meaAmount;
            }
        }
        return $fairValueUnitPrice;
    }

    /**
     * @return float|string
     */
    public function getMeaCalculatedAmount()
    {
        return $this->meaCalculatedAmount;
    }

    /**
     * @param float|string $meaCalculatedAmount
     */
    public function setMeaCalculatedAmount($meaCalculatedAmount)
    {
        $this->meaCalculatedAmount = $meaCalculatedAmount;
    }

    /**
     * @return float|string
     */
    public function getMeaOverriddenAmount()
    {
        return $this->meaOverriddenAmount;
    }

    /**
     * @param float|string $meaOverriddenAmount
     */
    public function setMeaOverriddenAmount($meaOverriddenAmount)
    {
        $this->meaOverriddenAmount = $meaOverriddenAmount;
    }

    /**
     * @return float
     */
    public function getRawLineQuantity()
    {
        return $this->rawLineQuantity;
    }

    /**
     * @param float $rawLineQuantity
     */
    public function setRawLineQuantity($rawLineQuantity)
    {
        $this->rawLineQuantity = $rawLineQuantity;
    }
}
