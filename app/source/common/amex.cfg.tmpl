; ------------------------------------------------------------
; AMEX-related configuration data
;-------------------------------------------------------------

; The configuration settings are different in development/test environment and in production
; environment. However, the underlying code will continue to use the same fields and credentials
; tags to connect to AMEX. Please update this section appropriately before proceeding.

; AMEX SFTP Settings
AMEX_SFTP_USE_SSHKEYS=1
AMEX_SFTP_UPLOAD_URL = "sftp://fsgatewaytest.aexp.com/inbox/"
AMEX_SFTP_DOWNLOAD_URL = "sftp://fsgatewaytest.aexp.com/outbox/"
AMEX_SFTP_PUBLIC_KEY = "key_amex_dev.pub"
AMEX_SFTP_PRIVATE_KEY = "key_amex_dev"
AMEX_SFTP_PASSPHRASE  = "{-{ ops/app_secrets_amex.amex_amex_sftp_passphrase }-}"
; This field specifies the user ID provided by amex to login to their SFTP site.
AMEX_SFTP_USER  = "intaccttest"

; E2 SSL details
AMEX_SSL_CERT = "amex_e2_SSL.pem"
AMEX_SSL_CERT_PASSPHRASE = "{-{ ops/app_secrets_amex.amex_amex_ssl_cert_passphrase }-}"

; Message layer certificate details
AMEX_MSG_PRIVATE_KEY = "amex_private_key_with_pass.pem"
AMEX_MSG_PRIVATE_KEY_PASSPHRASE = "{-{ ops/app_secrets_amex.amex_amex_msg_private_key_passphrase }-}"
AMEX_MSG_PUBLIC_CRT = "amex_public.crt"

; AMEX E2 DB details
AMEX_PARTNER_ENTITY_ID = "VZ130BG001"
AMEX_ACCT_LOCATION = "https://qservices.americanexpress.com:443/OrganizationAccountService"
AMEX_ORG_LOCATION = "https://qservices.americanexpress.com:443/OrganizationManagementService"

; ------------------------------------------------------------
; AMEX: REST based configuration settings section
;-------------------------------------------------------------

; The REST configuration settings are different in development/test environment and in production
; environment. However, the underlying code will continue to use the same fields and credentials
; tags to connect to AMEX. Please update this section appropriately before proceeding.

AMEX_INTACCT_PARTNER_NAME = "INTACCT"
AMEX_REST_HTTP_METHOD = "POST"
AMEX_REST_HOST = "qwww395.americanexpress.com"
AMEX_REST_PORT = "443"

; AMEX REST API V3 - this placeholder is mainly because card acceptance API is supported on a different testing environment than the other APIs
; in production this should have the same value as AMEX_REST_HOST_V3
AMEX_REST_HOST_QA_V3 = "api.qa.americanexpress.com"

; This is the placeholder for the AMEX API V3 production host
AMEX_REST_HOST_V3 = "apivir-qa.americanexpress.com"

AMEX_ENABLE_API_V3 = "1";

;AMEX_REST_CLIENT_ID = "405f1af5-ab2d-44ad-bb85-0a9ebc3bd9e3"
;AMEX_REST_SECRET_KEY = "69140294-922b-4313-97bf-38f45733bd51"

; AMEX REST based API QA endpoints
AMEX_ENROLL_LOCATION_RESTv2 = "/dpg/smartservices/payments/v2/manageEnrollment"
AMEX_INQUIRY_LOCATION_RESTv2 = "/dpg/smartservices/payments/v2/getStatus"
AMEX_PAYMENTS_LOCATION_RESTv2 = "/dpg/smartservices/payments/v2/processPayment"

; Amex ACH payment method related configuration settings section
AMEX_RETRIEVE_PAYERS_RESTv1 = "/dpg/smartservices/payments/v2/retrievePayers";
AMEX_RETRIEVE_PAYMENT_LIMIT_RESTv1 = "/dpg/smartservices/payments/v2/retrievePaymentLimit";
AMEX_RETRIEVE_EXCEPTIONS_RESTv1 = "/dpg/smartservices/payments/v2/getExceptions";

; Supplier Match related APIs
AMEX_SUPPLIER_MATCH_LOCATION_RESTv1 = "/dpg/suppliermatchservice/v1/uploadSuppliers"
AMEX_SUPPLIER_MATCH_INQUIRY_LOCATION_RESTv1 = "/dpg/suppliermatchservice/v1/getMatchStatus"

; NOTE for Production Settings:
; The following 2 fields need to be updated with the values provided by AMEX for production.
; Please set these fields with the encrypted values returned by the following function:
; TwoWayEncryptWithKey($value, 'IA_INIT');
; $value is the cleartext value for the corresponding fields below. The cleartext value
; will be provided by AMEX to Intacct Ops team before the release.
AMEX_REST_CLIENT_ID = "{-{ ops/app_secrets_amex.amex_amex_rest_client_id }-}"
AMEX_REST_SECRET_KEY = "{-{ ops/app_secrets_amex.amex_amex_rest_secret_key }-}"


; ------------------------------------------------------------
; V3 migration related configuration data
;-------------------------------------------------------------
V3MIGRATION_SFTP_URL = "sftp://fsgateway.aexp.com/"
V3MIGRATION_SFTP_USERID  = "{-{ ops/app_secrets_amex.amex_v3migration_sftp_userid }-}"
V3MIGRATION_SFTP_PASSWORD  = "{-{ ops/app_secrets_amex.amex_v3migration_sftp_password }-}"

V3MIGRATION_SFTP_TEST_URL = "sftp://fsgatewaytest.aexp.com/"
V3MIGRATION_SFTP_TEST_USERID  = "{-{ ops/app_secrets_amex.amex_v3migration_sftp_test_userid }-}"
V3MIGRATION_SFTP_TEST_PASSWORD  = "{-{ ops/app_secrets_amex.amex_v3migration_sftp_test_password }-}"
