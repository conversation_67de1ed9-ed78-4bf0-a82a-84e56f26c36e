<?
// PA Policies
$kPolicy = [
    'Categories' => [
        'values' => [
            'list' => [
                'eops'    => [ 'pa/lists/category' ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [ 'pa/lists/category/view', ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [ 'pa/lists/category/create' ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [ 'pa/lists/category/edit', ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [ 'pa/lists/category/delete' ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CATEGORIES',
    ],
    'Category Groups' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/categorygroup',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/categorygroup/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/categorygroup/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/categorygroup/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/categorygroup/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CATEGORY_GROUPS',
    ],
    'Customers' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/customer',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/customer/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/customer/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/customer/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/customer/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CUSTOMERS',
    ],
    'Customer Groups' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/customergroup',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/customergroup/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/customergroup/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/customergroup/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/customergroup/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CUSTOMER_GROUPS',
    ],
    'Customer Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/custtype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/custtype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/custtype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/custtype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/custtype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CUSTOMER_TYPES',
    ],
    'Projects' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/project',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/project/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/project/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/project/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/project/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECTS',
    ],
    'Project Groups' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projectgroup',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/projectgroup/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/projectgroup/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/projectgroup/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/projectgroup/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_GROUPS',
    ],
    'Project Contracts' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projectcontract',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/projectcontract/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/projectcontract/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/projectcontract/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/projectcontract/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_CONTRACTS',
    ],
    'Project Contract Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projectcontracttype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/projectcontracttype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/projectcontracttype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/projectcontracttype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/projectcontracttype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_CONTRACT_TYPES',
    ],
    'Project Resources' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projectresources',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/projectresources/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/projectresources/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/projectresources/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/projectresources/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_RESOURCES',
    ],
    'Project Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projecttype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/projecttype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/projecttype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/projecttype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/projecttype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_TYPES',
    ],
    'Project Status' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projectstatus',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/projectstatus/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/projectstatus/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/projectstatus/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/projectstatus/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_STATUS',
    ],
    'WIP management' => [
        'values' => [
            'list' => [
                'eops' => [
                    'pa/lists/wip',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List'
            ],
            'readonly' => [
                'eops' => [
                    'pa/lists/wip/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View'
            ],
            'add' => [
                'eops' => [
                    'pa/lists/wip/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add'
            ],
            'modify' => [
                'eops' => [
                    'pa/lists/wip/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit'
            ],
            'delete' => [
                'eops' => [
                    'pa/lists/wip/delete'
                ],
                'display' => 'IA.DELETE',
                'value'   =>  'Delete'
            ],
            'post' => [
                'eops' => [
                    'pa/lists/wipperiod/post'
                ],
                'display' => 'IA.POST',
                'value'   =>  'Post'
            ],
        ],
        'label' => 'IA.WIP_MANAGEMENT',
    ],
    'Tasks' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/task',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/task/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/task/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/task/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/task/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.TASKS',
    ],
    'Task Groups' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/taskgroup',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/taskgroup/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/taskgroup/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/taskgroup/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/taskgroup/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.TASK_GROUPS',
    ],
    'Task Resources' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/taskresources',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/taskresources/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/taskresources/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/taskresources/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/taskresources/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.TASK_RESOURCES',
    ],
    'Items' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/item',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/item/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/item/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/item/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/item/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.ITEMS',
    ],
    'Item Groups' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/itemgroup',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/itemgroup/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/itemgroup/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/itemgroup/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/itemgroup/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.ITEM_GROUPS',
    ],
    'Employees' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/employee',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/employee/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/employee/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/employee/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/employee/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
            'achsetup' => [
                'eops'    => [
                    'pa/lists/employee/achsetup',
                ],
                'display' => 'IA.BANK_DETAILS',
                'value'   => 'Bank Details',
            ],
        ],
        'label' => 'IA.EMPLOYEES',
    ],
    'Employee Groups' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/employeegroup',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/employeegroup/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/employeegroup/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/employeegroup/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/employeegroup/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.EMPLOYEE_GROUPS',
    ],
    'Employee Rates' => [
        'values' => [
            'readonly' => [
                'eops'    => [
                    'pa/lists/employeerate',
                    'pa/lists/employeerate/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
        ],
        'label' => 'IA.EMPLOYEE_RATES',
    ],
    'Generate Invoices' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/activities/expensebilling',
                ],
                'display' => 'IA.RUN',
                'value'   => 'Run',
            ],
            'add' => [
                'eops'    => [
                    'pa/activities/expensebilling/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
        ],
        'label' => 'IA.GENERATE_INVOICES',
    ],
    'Invoices' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/sodocument',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/sodocument/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/sodocument/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/sodocument/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PA.INVOICES',
    ],
    'Print/E-mail Invoices' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/activities/sobulkprint',
                ],
                'display' => 'IA.RUN',
                'value'   => 'Run',
            ],
        ],
        'label' => 'IA.PRINTEMAIL_INVOICES',
    ],
    'Customer List Report' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/reports/entitylist',
                ],
                'display' => 'IA.RUN',
                'value'   => 'Run',
            ],
        ],
        'label' => 'IA.CUSTOMER_LIST_REPORT',
    ],
    'Manage Resource Schedules' => [
        'values' => [
            'list' => [
                'eops'    => [ 'pa/lists/employeeschedule/search' ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'viewall' => [
                'eops'    => [ 'pa/lists/employeeschedule/viewall' ],
                'display' => 'IA.VIEW_ALL',
                'value'   => 'View All',
            ],
        ],
        'label' => 'IA.MANAGE_RESOURCE_SCHEDULES',
    ],
    'Transaction Rule' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/transactionrule',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/transactionrule/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/transactionrule/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/transactionrule/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/transactionrule/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.TRANSACTION_RULE',
    ],
    'Gantt Chart' => [
        'values' => [
            'view' => [
                'eops'    => [
                    'pa/activities/ganttchart/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'list' => [
                'eops'    => [
                    'pa/activities/ganttchart',
                ],
                'display' => 'IA.CUSTOMIZE',
                'value'   => 'Customize',
            ],
            'financial' => [
                'eops'    => [
                    'pa/activities/ganttchart/financial',
                ],
                'display' => 'IA.FINANCIAL_DATA',
                'value'   => 'Financial Data',
            ],
        ],
        'label' => 'IA.GANTT_CHART',
    ],
    'Standard Tasks' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/standardtask',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/standardtask/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/standardtask/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/standardtask/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/standardtask/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.STANDARD_TASKS',
    ],
    'Task Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/tasktype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/tasktype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/tasktype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/tasktype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/tasktype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.TASK_TYPES',
    ],
    'Standard Cost Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/standardcosttype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/standardcosttype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/standardcosttype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/standardcosttype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/standardcosttype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.STANDARD_COST_TYPES',
    ],
    'Accumulation Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/accumulationtype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/accumulationtype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/accumulationtype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/accumulationtype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/accumulationtype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.ACCUMULATION_TYPES',
    ],
    'Cost Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/costtype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/costtype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/costtype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/costtype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/costtype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.COST_TYPES',
    ],
    'Cost Type Groups' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/costtypegroup',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/costtypegroup/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/costtypegroup/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/costtypegroup/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/costtypegroup/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.COST_TYPE_GROUPS',
    ],
    'Project Estimates' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/pjestimate',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/pjestimate/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/pjestimate/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/pjestimate/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/pjestimate/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_ESTIMATES',
    ],
    'Estimate Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/pjestimatetype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/pjestimatetype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/pjestimatetype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/pjestimatetype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/pjestimatetype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.ESTIMATE_TYPES',
    ],
    'Project Change Orders' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projectchangeorder',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/projectchangeorder/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/projectchangeorder/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/projectchangeorder/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/projectchangeorder/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_CHANGE_ORDERS',
    ],
    'Construction reporting' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/crereporting/view',
                ],
                'display' => 'IA.ENABLE_CONSTRUCTION_REPORTS',
                'value'   => 'Enable construction reports',
            ],
        ],
        'label' => 'IA.CONSTRUCTION_REPORTING',
    ],
    'Project Detail reporting' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/projectdetail/view',
                ],
                'display' => 'IA.ENABLE_PROJECT_DETAIL_REPORTING',
                'value'   => 'Enable Project detail report',
            ],
        ],

        'label' => 'IA.PROJECT_DETAIL_REPORTING',
    ],
    'Change Request Types' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/changerequesttype',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/changerequesttype/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/changerequesttype/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/changerequesttype/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/changerequesttype/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CHANGE_REQUEST_TYPES',
    ],
    'Change Requests' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/changerequest',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/changerequest/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/changerequest/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/changerequest/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/changerequest/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CHANGE_REQUESTS',
    ],
    'Change Request Status' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/changerequeststatus',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/changerequeststatus/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/changerequeststatus/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/changerequeststatus/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/changerequeststatus/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.CHANGE_REQUEST_STATUS',
    ],
    'Project Contract Rate Tables' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/ratetable',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/ratetable/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/lists/ratetable/create',
                ],
                'display' => 'IA.ADD',
                'value'   => 'Add',
            ],
            'modify' => [
                'eops'    => [
                    'pa/lists/ratetable/edit',
                ],
                'display' => 'IA.EDIT',
                'value'   => 'Edit',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/ratetable/delete',
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.PROJECT_CONTRACT_RATE_TABLES',
    ],
    'WIP Relief' => [
        'values' => [
            'list' => [
                'eops'    => [
                    'pa/lists/wipreliefrun',
                ],
                'display' => 'IA.LIST',
                'value'   => 'List',
            ],
            'readonly' => [
                'eops'    => [
                    'pa/lists/wipreliefrun/view',
                ],
                'display' => 'IA.VIEW',
                'value'   => 'View',
            ],
            'add' => [
                'eops'    => [
                    'pa/activities/wipreliefrun',
                ],
                'display' => 'IA.GENERATE',
                'value'   => 'Generate',
            ],
            'delete' => [
                'eops'    => [
                    'pa/lists/wipreliefrun/delete'
                ],
                'display' => 'IA.DELETE',
                'value'   => 'Delete',
            ],
        ],
        'label' => 'IA.WIP_RELIEF',
    ],
];

// Presets

$_kReadonly = [
    'Customers' => ['list', 'readonly'],
    'Customer Groups' => ['list', 'readonly'],
    'Customer Types' => ['list', 'readonly'],
    'Projects' => ['list', 'readonly'],
    'Project Groups' => ['list', 'readonly'],
    'Project Contracts' => ['list', 'readonly'],
    'Project Contract Types' => ['list', 'readonly'],
    'Project Resources' => ['list', 'readonly'],
    'Project Types' => ['list', 'readonly'],
    'Project Status' => ['list', 'readonly'],
    'Project Estimates' => ['list', 'readonly'],
    'Estimate Types' => ['list', 'readonly'],
    'WIP management' => ['list', 'readonly'],
    'Tasks' => ['list', 'readonly'],
    'Task Groups' => ['list', 'readonly'],
    'Task Resources' => ['list', 'readonly'],
    'Items' => ['list', 'readonly'],
    'Item Groups' => ['list', 'readonly'],
    'Time Types' => ['list', 'readonly'],
    'Invoices' => ['list', 'readonly'],
    //'Print/E-mail Invoices' =>  array( 'list' ),
    'Gantt Chart' => ['view', 'list', 'financial'],
    'Standard Tasks' => ['list', 'readonly'],
    'Task Types' => ['list', 'readonly'],
    'Standard Cost Types' => ['list', 'readonly'],
    'Cost Types' => ['list', 'readonly'],
    'Accumulation Types' => ['list', 'readonly'],
    'Cost Type Groups' => ['list', 'readonly'],
    'Change Request Types' => ['list', 'readonly'],
    'Change Requests' => ['list', 'readonly'],
    'Change Request Status' => ['list', 'readonly'],
    'Project Change Orders' => ['list', 'readonly'],
    'Project Contract Rate Tables' => ['list', 'readonly'],
    'Construction reporting' => [ 'list', 'readonly' ],
    'Project Detail reporting' => [ 'list', 'readonly' ],
    'WIP Relief' => [ 'list', 'readonly' ],
];

$kPreset = [
    'Default' => [],
    'Readonly' => $_kReadonly,
    'Employee' => array_merge(
        $_kReadonly, [
            'Manage Resource Schedules' => ['list'],
        ]
    ),
    'CRM' => array_merge(
        $_kReadonly, [
            'Projects' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Groups' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Contracts' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Resources' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Status' => ['list', 'readonly'],
            'Project Estimates' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Estimate Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'WIP management' => ['list', 'readonly', 'add', 'modify', 'delete', 'post'],
            'Tasks' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Task Groups' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Task Resources' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Manage Resource Schedules' => ['list'],
        ]
    ),
    // Approver is the keyword used for users of type Project Manager (PM):
    'Approver' => array_merge(
        $_kReadonly, [
            'Projects' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Groups' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Contracts' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Contract Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Contract Rate Tables' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Resources' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Status' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Estimates' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Estimate Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'WIP management' => ['list', 'readonly', 'add', 'modify', 'delete', 'post'],
            'Tasks' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Task Groups' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Task Resources' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Manage Resource Schedules' => ['list', 'viewall'],
            'Generate Invoices' => ['list', 'add'],
            'Invoices' => ['list', 'readonly', 'modify', 'delete'],
            'Standard Tasks' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Task Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Standard Cost Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Cost Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Accumulation Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Cost Type Groups' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Change Request Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Change Requests' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Change Request Status' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'Project Change Orders' => ['list', 'readonly', 'add', 'modify', 'delete'],
            'WIP Relief' => ['list', 'readonly', 'add', 'delete'],
        ]
    ),

    // List of object permissions for construction objects that are enabled when the Construction subscription
    // is enabled (determined by the 'Job Management' flag in the subscription configuration screen for Construction)
    'JobManagement' => [
        'Cost Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Standard Cost Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Project Contracts' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Project Contract Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Project Change Orders' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Project Contract Rate Tables' => ['list', 'readonly'],
        'WIP management' => ['list', 'readonly', 'add', 'modify', 'delete', 'post'],
        'Accumulation Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Construction reporting' => ['list'],
        'Project Detail reporting' => ['list', 'readonly'],
        'Cost Type Groups' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Change Request Types' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Change Requests' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Change Request Status' => ['list', 'readonly', 'add', 'modify', 'delete'],
        'Employee Positions' => ['list', 'readonly', 'add', 'modify', 'delete'],
    ],

];

// UI mapping

$kUIMap = [
    'Lists' => [
        'Customers',
        'Customer Groups',
        'Customer Types',
        'Projects',
        'Project Groups',
        'Project Resources',
        'Project Types',
        'Project Status',
        'Project Contracts',
        'Project Contract Types',
        'Project Contract Rate Tables',
        'Project Estimates',
        'Estimate Types',
        'WIP management',
        'Tasks',
        'Task Groups',
        'Task Resources',
        'Items',
        'Item Groups',
        'Employees',
        'Employee Groups',
        'Employee Rates',
        'Generate Invoices',
        'Invoices',
        'Print/E-mail Invoices',
        'Manage Resource Schedules',
        'Transaction Rule',
        'Gantt Chart',
        'Standard Tasks',
        'Task Types',
        'Standard Cost Types',
        'Accumulation Types',
        'Cost Types',
        'Cost Type Groups',
        'Construction reporting',
        'Project Detail reporting',
        'Change Request Types',
        'Change Requests',
        'Change Request Status',
        'Project Change Orders',
        'Employee Positions',
        'WIP Relief',
    ],
    'Reports' => [
        'Customer List Report',
    ],
];