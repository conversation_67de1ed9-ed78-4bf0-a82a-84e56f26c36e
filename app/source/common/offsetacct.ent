<?
$kSchemas['offsetacct'] = array(

    'children' => array(
        'entitygroup' => array ('fkey' => 'entgrpkey','invfkey' => 'record#', 'table' => 'entityglgrp', 'join' => 'outer'),
        'itemglgroup' => array ('fkey' => 'itemgrpkey','invfkey' => 'record#', 'table' => 'icitemglgrp', 'join' => 'outer'),
        'warehouse' => array ('fkey' => 'whsekey','invfkey' => 'record#', 'table' => 'icwarehouse', 'join' => 'outer'),
        'glaccount' => array ('fkey' => 'glaccountrecordno','invfkey' => 'record#', 'table' => 'glaccount'),
        'dept' => array ('fkey' => 'deptkey','invfkey' => 'dept_no', 'table' => 'department', 'join' => 'outer'),
        'location' => array ('fkey' => 'locationkey','invfkey' => 'record#', 'table' => 'location', 'join' => 'outer'),
        'documentparams' => array ('fkey' => 'docparkey','invfkey' => 'record#', 'table' => 'docpar', 'join' => 'outer'),
    ),


    'object' => array(
        'RECORDNO',
        'DOCPARNO',
        'ITEM_GLGROUP',
        'ENT_GLGROUP',
        'WAREHOUSE',
        'GLACCOUNT',
        'DEPT',
        'LOCATION',
        'ISOFFSET',
        'DEBIT_CREDIT',
        'MODULE',
        'GLACCTTITLE',
        'DEPTTITLE',
        'LOCATIONNAME',
        'LINE_NO',
    ),


    'schema' => array(
        'RECORDNO'         =>    'record#',
        'DOCPARNO'        =>    'docparkey',
        'ITEM_GLGROUP'    =>    'itemglgroup.name',
        'ENT_GLGROUP'    =>    'entitygroup.name',
        'WAREHOUSE'        =>    'whsekey',
        'GLACCOUNT'        =>    'glaccount.acct_no',
        'GLACCOUNTRECORDNO'     => 'glaccountrecordno',
        'DEPT'            =>    'deptkey',
        'LOCATION'        =>    'locationkey',
        'ISOFFSET'        =>    'isoffset',
        'DEBIT_CREDIT'    =>    'dr_cr',
        'MODULE'        =>    'module',
        'GLACCTTITLE'    => 'glaccount.title',
        'DEPTTITLE'        => 'dept.title',
        'LOCATIONNAME'    => 'location.name',
        'LINE_NO'       => 'line_no',
    ),


    'fieldinfo' => array(
        array (
            'path'        => 'RECORDNO',
            'fullname'    => 'IA.RECORD_NUMBER',
            'desc'        => 'IA.RECORD_NUMBER',
            'type'         => array (
                'type'         => 'integer',
                'ptype'     => 'sequence',
                'size'         => 8,
                'maxlength' => 8
            ),
            'readonly' => true,'hidden' => true,
            'id' => 1,
        ),
        array (
            'path'        => 'DOCPARNO',
            'fullname'    => 'IA.DOCUMENT_NAME',
            'desc'        => 'IA.DOCUMENT_NAME',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'documentparams',
                'size'        =>  8,
                'maxlength'    =>    8,
                'format'    =>     $gRecordNoFormat
            ),
            'required' => true,
            'id' => 2,
        ),
        array (
            'path'        => 'ITEM_GLGROUP',
            'fullname'    => 'IA.ITEM_GLGROUP',
            'desc'        => 'IA.ITEM_GLGROUP',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'itemglgroup',
                'size'         => 20
            ),
            'renameable' => true,
            'id' => 3,
        ),
        array (
            'path'        => 'ENT_GLGROUP',
            'fullname'    => 'IA.ENTITY_GROUP',
            'desc'        => 'IA.ENTITY_GROUP',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'entityglgroup',
                'size'         => 20
            ),
            'id' => 4,
        ),
        array (
            'path'        => 'WAREHOUSE',
            'fullname'    => 'IA.WAREHOUSE',
            'desc'        => 'IA.WAREHOUSE',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'warehouse',
                'size'         => 20
            ),
            'renameable' => true,
            'id' => 5,
        ),
        array (
            'path'        => 'GLACCOUNT',
            'fullname'    => 'IA.GL_ACCOUNT',
            'desc'        => 'IA.GL_ACCOUNT',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity'    => 'glaccount',
                'size'         => 20,
                'maxlength'    => 106
            ),
            'required' => true,
            'id' => 6,
        ),
        array (
            'path'        => 'DEPT',
            'fullname'    => 'IA.DEPARTMENT',
            'desc'        => 'IA.DEPARTMENT',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'department',
                'pickentity'    => 'departmentpick',
                'size'         => 20
            ),
            'renameable' => true,
            'id' => 7,
        ),
        array (
            'path'        => 'LOCATION',
            'fullname'    => 'IA.LOCATION',
            'desc'        => 'IA.LOCATION',
            'type'         => array (
                'type'         => 'ptr',
                'ptype'     => 'ptr',
                'entity'    => 'location',
                'pickentity'    => 'locationpick',
                'size'         => 20
            ),
            'renameable' => true,
            'id' => 8,
        ),
        array (
            'path'        => 'ISOFFSET',
            'fullname'    => 'IA.IS_OFFSET',
            'desc'        => 'IA.IS_OFFSET',
            'type'         => $gBooleanType,
            'id' => 9,
        ),
        array (
            'path'        => 'DEBIT_CREDIT',
            'fullname'    => 'IA.DEBIT_CREDIT',
            'desc'        => 'IA.DEBIT_CREDIT',
            'type'         => array (
                'type'             => 'enum',
                'ptype'         => 'enum',
                'validvalues'     => array ('Debit','Credit'),
                '_validivalues' => array ('1','-1'),
                'validlabels'     => array ('IA.DEBIT','IA.CREDIT'),
            ),
            'id' => 10,
            // enum fields are indirectly enforced by the UI. Causes problem with the JS Validation
            //'required' => true
        ),
        array (
            'path'        => 'MODULE',
            'fullname'    => 'IA.MODULE',
            'desc'        => 'IA.MODULE',
            'type'         => array (
                'type'             => 'enum',
                'ptype'         => 'enum',
                'validvalues'     => array ('AP/AR','INV', 'ADDITIONAL'),
                '_validivalues' => array ('PR','INV', 'ADD'),
                'validlabels'     => array ('IA.AP_AR','IA.INV', 'IA.ADDITIONAL'),
            ),
            'required' => true,
            'id' => 11,
        ),
        array(
            'path' => 'LINE_NO',
            'fullname' => 'IA.LINE_NO',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 4,
                'format' => $gLineNoFormat
            ),
            'readonly' => true,
            'derived' => true,
            'id' => 12
        ),

        $gStatusFieldInfo
    ),

    'table'         => 'offsetacct',
    'primaryfield'    => 'GLACCOUNT',
    'vid'             => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'printas'        => 'IA.OFFSET_ACCOUNT',
    'pluralprintas'  => 'IA.OFFSET_ACCOUNTS',
    'module'         => 'co',
    'dontGenerateQueries' => true,
);
