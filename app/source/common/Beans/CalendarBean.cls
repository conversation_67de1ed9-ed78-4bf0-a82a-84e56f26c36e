<?php
require_once 'backend_dates.inc';

/**
 * Class CalendarBean
 */
class CalendarBean extends Bean
{

    function RenderBody()
    {

    ?>

		<!-- moved to the page header <link type="text/css" rel="stylesheet" href="../resources/thirdparty/yui/css/hcalendar.css">  -->
		<script language="JavaScript" src="../resources/thirdparty/yui/calendar-min.js"></script>
		<script language="JavaScript" src="../resources/thirdparty/yui/yahoo-dom-event.js"></script>

		<div class="hcalendar">
			<div id="cal1Container"></div>
		</div>

		<script>
			YAHOO.namespace("calendar");

			YAHOO.calendar.init = function() {
				YAHOO.calendar.cal1 = new YAHOO.widget.Calendar("cal1","cal1Container",{hide_blank_weeks:true});
				YAHOO.calendar.cal1.render();
			}

			YAHOO.util.Event.onDOMReady(YAHOO.calendar.init);
		</script>

    <?

    }

    /**
     * @return bool
     */
    function SetDivClassAttributes()
    {
        //echo $this->beanWidth;
        $this->className = " class=\"SCROLLCOMP\" ";
        if ($this->height) {
            $this->initialStyle .= "height:" . $this->height . "px;";
        } else {
            $this->initialStyle .= "height:auto;";
        }
        return true;
    }

}


