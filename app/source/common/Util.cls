<?php
/** 
 * Utility class for all apps
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2001 Intacct Corporation, All Rights Reserved
 */

require_once 'ims_include.inc';

/**
 * Utility class - same as util.inc but encapsulated in a class.
 */
class Util
{
    const LOG_PREFIX = LoginManager::LOG_PREFIX . '::' . __CLASS__;
    const CURL_TIMEOUT_DEFAULT = 300; // In seconds - 5m
    const CURL_TIMEOUT_MAX_EXTERNAL = 1500; // In seconds - 25m
    const CURL_TIMEOUT_MAX_INTERNAL = 14400; // In seconds - 4h
    
    const MAX_LEVEL = 10;
    const INTACCTID_SEPARATOR = '--';
    const AMOUNT_PRECISION = 2;

    /**
     * @var string[] $COLOR_MAP
     */
    private static $COLOR_MAP = array ( '#fd9829', '#81b800', '#4180c0', '#63093b', '#818283' );

    const LOG_EXTERNAL_HTTP_ALL = 'ALL';
    const LOG_EXTERNAL_HTTP_FAILURE = 'FAILURE';
    const LOG_EXTERNAL_HTTP_NONE = 'NONE';
    const MAINTENANCE_MODE = 'maintenance_mode';

    /**
     * No-op constructor. It's private since the methods in this class are all static
     */
    private function __construct()
    {
    }

    /**
     * Lightweight RPC call API for the app. This function doesn't use IMS and it won't setup the session on
     * the remote call.
     *
     * @param callback    $callback           the callback info of the function to call - since we are doing an RPC call,
     *                                        the callback cannot reference an object. It could be a top level function or
     *                                        a class static method
     * @param array       $args               the arguments for the RPC call
     * @param mixed       $results            the result returned by the remote function
     * @param array       $errors             Errors array
     * @param mixed       $filesNeeded        name(s) of files that need to be included to run the function remotely
     * @param string|null $urlOverride        a different URL to use instead of rpc.phtml
     * @param bool        $appendRemoteErrors Append remote errors to local gErr
     * @param array|null  $addlParams         More parameters to send to the call
     * @param string      $logPrefix          Used to add a prefix to the log messages
     * @param int|null    $timeout            Dynamic timeout value for curl
     *
     * @return bool success or failure
     */
    public static function rpcCall(
        $callback,
        $args,
        &$results,
        &$errors,
        $filesNeeded = null,
        $urlOverride = null,
        $appendRemoteErrors = true,
        $addlParams = null,
        string $logPrefix = self::LOG_PREFIX,
        int|null $timeout = null
    ) {
        $gErr = Globals::$g->gErr;

        // let's see if we are in the make process, then we simply call the function directly
        if ( defined('INTACCTCONTEXT') ) {
            // TODO GLM: Make sure my method version is compatible with the caller method version
            $results = call_user_func_array($callback, ensureArray($args));
            $ok = ! $gErr->hasErrors();
            return $ok;
        }

        if ( $urlOverride != null ) {
            $myURL = $urlOverride;
        } else {
            $ims_queueaddr =& Globals::$g->ims_queueaddr;
            $myURL = str_replace('/imsq.phtml', '/rpc.phtml', $ims_queueaddr);
        }

        $userAndPwd = null;
        if ( ! self::isInternalRequest($myURL) ) {
            $userAndPwd = GetValueForIACFGProperty('RPC_CREDENTIALS');
        }

        if ( $filesNeeded && ! is_array($filesNeeded) ) {
            $filesNeeded = array($filesNeeded);
        }
        $request = array(
            'func' => $callback,
            'args' => $args,
            'files' => $filesNeeded,
        );

        $body = array('request' => serialize($request) );
        if ( $addlParams ) {
            array_append($body, $addlParams);
        }
        $theRetHeaders = [];
        $ok = self::httpCall(
            $myURL,
            $body,
            $res,
            false,
            $userAndPwd,
            null,
            true,
            null,
            null,
            null,
            true,
            $theRetHeaders,
            timeout: $timeout,
            logPrefix: $logPrefix
        );

        if ( $ok ) {
            // TODO: remove array_change_key_case after the httpCall was updated to process the headers in lower case
            $response = unserialize(trim($res));
            if ( is_array($response)
                && ( array_key_exists('data', $response) || array_key_exists('errors', $response) )
            ) {
                $results = arrayExtractValue($response, 'data');
                $errors = arrayExtractValue($response, 'errors');
                
                if ($appendRemoteErrors && is_array($errors) && count($errors) > 0 ) {
                    $gErr->AppendErrList($errors);
                    $ok = false; // TODO GLM: Why exiting with failure if the initial return value was success ?
                    // TODO GLM: Remote Errors are not MY Errors.
                }
    
                // For maintenance mode, also return the error - maintenance_mode key was added to the executeRPCCall()
                // call, to flag in the response that the maintenance mode was on when the call happened
                if (array_key_exists(self::MAINTENANCE_MODE, $response) && $response[self::MAINTENANCE_MODE] == 1) {
                    $errors[DBRunner::IN_MAINTENANCE_MODE] = true;
                    $gErr->AppendErrList($errors);
                    addLog("RPC response: maintenance_mode was on when the call happened", LogManager::WARN);
                    $ok = false;
                }
            } else {
                $msg = 'There was an error executing the remote function:' .
                    ' An unexpected response was received from the remote process.';
                if ( $appendRemoteErrors ) {
                    $gErr->addError(
                        '**********',
                        __FILE__ . '.' . __LINE__,
                        $msg
                    );
                }
                logToFileError(sprintf("%s::%s:%d::RPC response: %s",$logPrefix, __METHOD__, __LINE__, $res));
                $ok = false;
            }
        }
        
        return $ok;
    }

    /**
     * This function is to sent requests in parallel
     *
     * @param array  $urls       urls
     * @param int    $batchCount number of requests per time
     * @param string $auth       authentication
     *
     * @return array
     * @throws Exception
     */
    public static function curlParallelRequests($urls, $batchCount, $auth='')
    {
        $results = [];
        $uCnt = count($urls);
        
        if (!$uCnt) {
            return $results;
        } else if ($uCnt < $batchCount) {
            $batchCount = $uCnt;
        }
        
        $urlBatches = array_chunk($urls, $batchCount);
        
        $ch = [];
        $mh = curl_multi_init();
        $init = true;
        
        foreach ($urlBatches as $batch) {
            
            //in the final batch keep only required no. of $ch
            $count = count($batch);
            if ($count < $batchCount) {
                for ($i=$count; $i<$batchCount; $i++) {
                    curl_close($ch[$i]);
                }
                
                $ch = array_slice($ch, 0, $count);
            }
            
            foreach ($batch as $key => $val) {
                if ($init) {
                    $ch[$key] = curl_init();
                }
                curl_setopt($ch[$key], CURLOPT_URL, $val);
                curl_setopt($ch[$key], CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch[$key], CURLOPT_FOLLOWLOCATION, 1);
                if (!empty($auth)) {
                    curl_setopt($ch[$key], CURLOPT_USERPWD, $auth);
                }
                curl_multi_add_handle($mh, $ch[$key]);
            }

            $stillRunning = null;
            do {
                $status = curl_multi_exec($mh, $stillRunning);
                if ($status > 0) {
                    $error = curl_multi_strerror($status);
                    throw new Exception("curlParallelRequests error: $error" . print_r($error, true));
                }
            } while ($stillRunning > 0);

            // Get content and remove handles.
            foreach ($ch as $key => $val) {
                $res = curl_multi_getcontent($val);
                if (empty($res)) {
                    LogToFile("ianotify: curlParallelRequests - No data returned for ".$batch[$key]."\n");
                }
                $results[] = $res;
                curl_multi_remove_handle($mh, $val);
            }
            
            $init = false;
        }
        
        //closing remaining connections
        foreach ( $ch as $key => $val) {
            curl_close($ch[$key]);
        }
        
        curl_multi_close($mh);
        
        return $results;
    }


    /** replace FILTER_SANITIZE_STRING which is deprecated */
    public static function filterSanitizeString(string $s) {
         $s = strip_tags($s);
         $s = str_replace(['\'', '"'], ['&#39;', '&#34;'], $s);

         return $s;
    }

    /**
     * Compute the COOKIE value for the development options such as private logging file and debug session
     *
     * @return string the cookies string
     */
    public static function computeDevCookie() 
    {
        static $DEV_COOKIE_NAMES = array (
            'IA_AUTH_USER',
            'IA_CFG_OVRWR',
            'XDEBUG_SESSION',
            'COVERAGERESULT_SETNAME',
            'IA_TESTNAME',
            'XDEBUG_PROFILE',
            'IA_USER_REQUEST_ID',
        );
        
        $cookies = [];
        foreach ($DEV_COOKIE_NAMES as $cookieName) {
            if ($_COOKIE[$cookieName] ?? null) {
                $cookies[] = $cookieName . '=' . $_COOKIE[$cookieName];
            }
        }

        return implode(';', $cookies);
    }

    /**
     * Compute the special headers for a curl request
     *
     * @return string[]  the headers added
     */
    public static function computeSpecialHeaders()
    {
        $headers = [];
        // special processing for X_FORWARDED_FOR
        // when it's not set we initialize it from SERVER[REMOTE_ADDR]
        // this header is needed for xdebug - we rely on it when debugging
        // on the VMs built by devops and installed on users' laptops.
        // we might rely on this for common image dev machines as well
        if ( ! Globals::$g->islive ) {
            $headerName = 'X-FORWARDED-FOR';
            static $allHeaders = null;
            if ( ! isset($allHeaders) ) {
                if ( function_exists('getallheaders') ) {
                    $allHeaders = array_change_key_case(getallheaders(), CASE_UPPER);
                } else {
                    $allHeaders = [];
                }
            }
            if ( isset($allHeaders[$headerName]) ) {
                $headers[] = "$headerName: " . $allHeaders[$headerName];
            } else {
                $headers[] = "$headerName: " . $_SERVER['REMOTE_ADDR'];
            }
        }

        return $headers;
    }

    /**
     * Determine if a URL is internal or external
     *
     * @param string $url
     *
     * @return bool
     */
    public static function isInternalRequest($url)
    {
        // Parse the url, looking to see if the host contains 'localhost' or '127.0.0.1'
        $components = parse_url($url);
        return (
            is_array($components)
            && $components['host']
            && ($components['host'] == '127.0.0.1' || $components['host'] == 'localhost')
        );
    }
    
    
    /**
     * Curl HTTP call wrapper.
     *
     * @param string        $url                 the URL where to place the HTTP call
     * @param array|string  $body                the params to send in the form of a map or an URL encoded string
     * @param string|false &$response            the resposne from the URL or false on curl errors
     * @param bool          $doGet               true to make a Get call, false to use Post (default)
     * @param string|null   $userAndPwd          the URL encoded user and password for HTTP authentication
     * @param array|null    $_cookies            cookies to be passed with the curl call
     * @param bool          $doBind              true to force binding(escaping) the params
     * @param string[]|null $headers             array of arbitrary http headers to send
     * @param string|null   $customRequestMethod for sending newer http methods like delete
     * @param bool|null     $acceptCookies       if true will set returned cookies
     * @param bool          $retHeaders          if true will return headers in &$theRetHeaders
     * @param string[]|null $theRetHeaders       the returned headers if $retHeaders is true
     * @param bool          $ignoreCerts         if true will ignore invalid certificates.  USE WITH CAUTION
     * @param array|null    $curlInfo            associative array output of curl_getinfo
     * @param int|string|null $timeout        Forced timeout value to use (internal/external values are used if not set) - position #15
     * @param int|null      $connectTimeout      Connect timeout value to use if not null
     * @param string        $logPrefix           Used to add a prefix to the log messages
     *
     * @return int|false HTTP status code or FALSE for failure
     */
    public static function httpCall(
        $url,
        $body,
        &$response,
        $doGet = false,
        $userAndPwd = null,
        $_cookies = null,
        $doBind = true,
        $headers = null,
        $customRequestMethod = null,
        $acceptCookies = null,
        $retHeaders = false,
        &$theRetHeaders = null,
        $ignoreCerts = false,
        &$curlInfo = null,
        $timeout = null, // curl timeout - position #15
        $connectTimeout = null,
        string $logPrefix = self::LOG_PREFIX
    ) {
        /* @var PerfData $perfdata */
        global $perfdata;
        
        if ( is_array($body) ) {
            $isFile = false;
            $haveCurlFileClass = class_exists('CURLFile');
            if ( $doBind === false ) {
                foreach ( $body as $key => $file ) {
                    if ( is_object($file) && $haveCurlFileClass && $file instanceof CURLFile ) {
                        $isFile = true;
                    } else if ( is_string($file) && substr($file, 0, 1) == '@' ) {
                        $isFile = true;
                        if ( $haveCurlFileClass ) {
                            if (preg_match('/@(.*);filename=(.*)/', $file, $matches)) {
                                $body[$key] = new CURLFile($matches[1], 'application/octet-stream', $matches[2]);
                            } else {
                                $body[$key] = new CURLFile(isl_substr($file, 1));
                            }
                        }
                    }
                }
            }
            if (!$isFile) {
                $body = http_build_query($body);
            }
        }

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Intacct Standard HTTP Agent');
        if ( defined('CURLOPT_MUTE') ) {
            // This option hasn't done anything in libcurl since 2001 and was removed from php in 2004 5.2.14
            curl_setopt($ch, CURLOPT_MUTE, 1);
        }
        if ( $ignoreCerts || !Globals::$g->islive ) {
            // Allow for snake oil certificates
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        }

        $externalHttpCall = ! self::isInternalRequest($url);

        // Determine timeout. Use passed-in value if set, otherwise base it on external/internal host
        if (isset($timeout) && ((int) $timeout) >= 0) {
            $curlTimeoutSecs = (int)$timeout  > self::CURL_TIMEOUT_MAX_EXTERNAL
                ? self::CURL_TIMEOUT_MAX_EXTERNAL
                : (int) $timeout ;
        } else {
            if ( $externalHttpCall ) {
                // Default timeout for external calls
                $curlTimeoutSecs = self::CURL_TIMEOUT_DEFAULT;
            } else {
                // For local requests, set the timeout to 4 hours (something long)
                $curlTimeoutSecs = self::CURL_TIMEOUT_MAX_INTERNAL;
            }
        }

        if ($connectTimeout != null) {
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $connectTimeout); // Seconds until connection timeout
        }

        curl_setopt($ch, CURLOPT_TIMEOUT, $curlTimeoutSecs); //Seconds until timeout
        
        if ( $userAndPwd ) {
            curl_setopt($ch, CURLOPT_USERPWD, $userAndPwd);
        }
        
        if ( ! $doGet ) {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        }

        if ( $customRequestMethod !== null ) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $customRequestMethod);
            if ( $body !== null && $body !== '' ) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
            }
        }
        
        $cookie = self::computeDevCookie();
        if ($_cookies != null && count($_cookies) > 0) {
            foreach ($_cookies as $id => $cokie) {
                if ($cookie != '') {
                    $cookie .= "; ";
                }
                $cookie .= "$id=" .rawurlencode($cokie);  // Values may contain semicolon, needs encoding.
            }
        }
        if ($cookie != '') {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }

        if ( ! $headers || ! is_array($headers) ) {
            $headers = [];
        }
        $devHeaders = self::computeSpecialHeaders();
        $headers = array_merge($headers, $devHeaders);
        if ( ! empty($headers) ) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        curl_setopt($ch, CURLOPT_HEADER,  0);
        $returnedHeaders = [];
        $clearHeaders = true;
        if ( $acceptCookies || $retHeaders ) {
            curl_setopt(
                $ch,
                CURLOPT_HEADERFUNCTION,
                function(/** @noinspection PhpUnusedParameterInspection */ $ch, $header) use (&$returnedHeaders, &$clearHeaders) {
                    $len = strlen($header);
                    if ( $clearHeaders ) {
                        // Reset the headers on a) start of the request or b) after a blank line was seen in the headers
                        // which, since we're processing another header means that the previous block was from a proxy
                        // server
                        $returnedHeaders = [];
                        $clearHeaders = false;
                    }
                    if ( $header == "\r\n" ) {
                        // Proxy servers can inject multiple header blocks.  Only preserve the last block which *should*
                        // be from the target server.
                        $clearHeaders = true;
                    } else {
                        if ( $header[$len-2] == "\r" && $header[$len-1] == "\n" ) {
                            $header = substr($header, 0, -2);
                        }
                        // I'd like to encapuslate this in a  function but can't pass the use's variable into another
                        // function by-reference
                        $colon = strpos($header, ':');
                        if ( $colon ) {
                            $name =  substr($header, 0, $colon);
                            $value = substr($header, $colon + 2);
                            if ( $name !== false && $name !== '' && $value !== false && $value !== '' ) {
                                if ( isset($returnedHeaders[$name]) ) {
                                    if ( ! is_array($returnedHeaders[$name]) ) {
                                        $returnedHeaders[$name] = [ $returnedHeaders[$name] ];
                                    }
                                    $returnedHeaders[$name][] = $value;
                                } else {
                                    $returnedHeaders[$name] = $value;
                                }
                            }
                        }

                    }
                    return $len;
                }
            );
        }
        
        if ( $externalHttpCall ) {
            $perfdata->incrementExthttpCount();
            StartTimer('exthttpCall');
        }

        $response = false;
        // check for both versions
        for ($i = 0; $i < 2; $i++) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $clearHeaders = true;
            $response = curl_exec($ch);
            $errorno = curl_errno($ch);
            $errtext = curl_error($ch);
            //Error code CURLE_OPERATION_TIMEOUTED (28) is for request timeout
            //If there is request timeout then no need to resend the request
            if ($errorno && $errorno != CURLE_OPERATION_TIMEOUTED) {
                // try with the older http version
                curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
            } else {
                // it worked get out
                break;
            }
        }

        if ( $externalHttpCall ) {
            StopTimer('exthttpCall');
        }

        $curlInfo = curl_getinfo($ch);
        $curlInfo['curl_error'] = $errtext;
        $curlInfo['curl_errno'] = $errorno;

        // TODO GLM: Remove error setting inside this method. This should be the responsability of the caller when checking the returned result.
        if ($curlInfo['http_code'] !=0 ) { //$curlInfo['http_code']==0 when host is down/invalid
            if ( $response === false || $errorno ) {
                Globals::$g->gErr->addError('**********', __FILE__.'.'.__LINE__, 'There was an error executing the call: ' . $errorno . ' - ' . $errtext);
                $ok = false;
            } else {
                $ok = $curlInfo['http_code'];
            }
        } else {
            $ok = false;
        }

        if ( $externalHttpCall ) {
            self::logExternalHTTPCall($url, $curlInfo['http_code']);
        }

        if ( $acceptCookies || $retHeaders ) {
            if ( $retHeaders ) {
                $xProcessInfoStr = arrayExtractValue(array_change_key_case($returnedHeaders, CASE_LOWER), 'x-process-info');
                $parsedUrl = parse_url($url);
                LogToFile(
                    sprintf(
                        "%s::%s:%d::X-PROCESS-INFO %s, %s\n",
                        $logPrefix, __METHOD__, __LINE__, basename($parsedUrl['path'] ?? $url), $xProcessInfoStr
                    )
                );
                $theRetHeaders = $returnedHeaders;
            }
            if ( $acceptCookies ) {
                $cookieHeaders = crackArray($returnedHeaders, 'set-cookie', [], caseSensitive: false);
                if ( ! is_array($cookieHeaders) ) {
                    $cookieHeaders = [ $cookieHeaders ];
                }
                foreach ( $cookieHeaders as $cookie ) {
                    $name = null;
                    $value = null;
                    $expires = 0;
                    $path = '/';
                    $domain = '';
                    $secure = null;
                    $httpOnly = false;
                    $cookieCrumbs = explode(';', $cookie);
                    foreach ( $cookieCrumbs as $cookieCrumb ) {
                        $cookieCrumbParts = explode('=', trim($cookieCrumb));
                        if ( $name === null ) {
                            $name = $cookieCrumbParts[0];
                            $value = $cookieCrumbParts[1];
                        } else {
                            switch (\strtolower($cookieCrumbParts[0])) {
                                case 'expires':
                                    $expires = strtotime($cookieCrumbParts[1]);
                                    break;
                                case 'max-age':
                                    if ($cookieCrumbParts[1] > 0) {
                                        // only use max-age if expires is not set or it fails to parse the datetime
                                        if (0 === $expires || false === $expires) {
                                            $expires = time() + $cookieCrumbParts[1];
                                        }
                                    }
                                    break;
                                case 'domain':
                                    $domain = $cookieCrumbParts[1];
                                    break;
                                case 'path':
                                    $path = $cookieCrumbParts[1];
                                    break;
                                case 'secure':
                                    $secure = true;
                                    break;
                                case 'httponly':
                                    $httpOnly = true;
                                    break;
                            }
                        }
                    }
                    if ( $name !== null
                        && $value !== null
                        && $secure !== null
                        && $value !== IASessionHandler::SESSION_COOKIE_DELETED_VALUE
                    ) {
                        INTACCTsetcookie(
                            (string)$name,
                            $value,
                            (int)$expires,
                            (string)$path,
                            (string)$domain,
                            (bool)$httpOnly
                        );
                    }
                }
            }
        }

        curl_close($ch);
        
        return $ok;
    }

    /**
     * Log the details of the external http call
     *
     * @param string $url      the URL where the call was made to
     * @param int    $httpCode the http response code
     */
    public static function logExternalHTTPCall($url, $httpCode)
    {
        static $logExternalHTTP = null;
        if ( $logExternalHTTP === null ) {
            $logExternalHTTP = GetValueForIACFGProperty('LOG_EXTERNAL_HTTP_CALLS');
            if ( ! $logExternalHTTP ) {
                $logExternalHTTP = self::LOG_EXTERNAL_HTTP_NONE;
            } else if ( ! in_array($logExternalHTTP, [
                self::LOG_EXTERNAL_HTTP_ALL,
                self::LOG_EXTERNAL_HTTP_FAILURE,
                self::LOG_EXTERNAL_HTTP_NONE,
            ]) ) {
                dieFL('Unexpected value (' . $logExternalHTTP . ') for LOG_EXTERNAL_HTTP_CALLS in configuration file');
            }
        }
        if ( $logExternalHTTP === self::LOG_EXTERNAL_HTTP_ALL
            || ( $logExternalHTTP === self::LOG_EXTERNAL_HTTP_FAILURE && ( $httpCode < 200 || $httpCode >= 300) )
        ) {
            $bareURL = strtok($url, '?');
            logToFileCritical("OPS ALERT HTTP Call to '$bareURL', http code '$httpCode', cny " . GetMyCompany()
                . ' at ' . date('Y-m-d H:i:s T'));
            if ( $httpCode < 200 | $httpCode >= 400 ) {
                logStackTraceBrief();
            }
        }
    }

    /**
     * Deliver the subscription packages that have been created by this unit of work
     */
    public static function deQueueSubscriptions()
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        if ( $gManagerFactory ) {
            /* @var IMSSubscriptionManager $subscription */
            $subscription = $gManagerFactory->getManager('imssubscription');
            $subscription->DeQueueSubscriptions();
        }
    }

    /**
     * Generate a lister line for the tree view
     *
     * @param int     $level       the level of the node
     * @param string  $nodeString  the node label
     * @param bool    $noParent    whether the node has a parent or not
     * @param bool    $isLastNode  true if this is the last node, false otherwise
     *
     * @return string HTML string to display the line in the lister
     */
    public static function getListTreeString($level, $nodeString, $noParent, $isLastNode)
    {
        if ($level > self::MAX_LEVEL - 1) {
            $level = self::MAX_LEVEL - 1;
        }
        $color = self::$COLOR_MAP[$level % count(self::$COLOR_MAP)];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $text = '  ' . $nodeString;

        $nodeImageString = '';
        $chainImageString = '';
        $isnode = $level > 0;
        $nodeImage = ( $isLastNode ? 'nodeEnd.gif' : 'node.gif' );
        $chainImage = 'chain.gif';
        $nodeAlt = '';

        // if not root and its parent is filtered out, show different image.
        if ($noParent) {
            $nodeImage = 'nodeOrphan.png';
            $nodeAlt = 'Parent not displayed';
            $chainImage = 'blank.gif';
        }

        if ( $level > 1 ) {
            $nodeImageString = "img2='$nodeImage' alt2='$nodeAlt'";
            $repeat = $level - 1;
            $chainImageString = "img1='$chainImage' img1-repeat='$repeat'";
        } else if ($level == 1) {
            $nodeImageString = "img1='$nodeImage' alt1='$nodeAlt'";
        }
        $tree = "<listtree color='$color' level='$level' isnode='$isnode' " .
            "$chainImageString $nodeImageString> $nodeString</listtree>";

        return $tree;
    }

    /**
     * Curl HTTP file transfer wrapper.
     *
     * @param string  $url          the URL where to place the hTTP call
     * @param string  $request      the type of request to make (upload, download, list)
     * @param array   $credentials  the credentials used to connect to the server
     * @param string  &$response    the response from the URL
     * @param string  &$error       curl error message (if any)
     * @param array   $uploadData   information used for upload (file or buffer)
     * @param bool    $ignoreCerts  ignore certificate verification (for testing)
     * @param int     $port         the port to connect to (default 443)
     *
     *
     * Example usage:
     *
     * $url = 'http://www.sftpserver.com/';
     * $request = 'upload';
     * $credentials = array('userpwd' => "$user:$pwd");
     * $uploadData = array('dst' => $filename, // This is the destination, it can be the filename or a full path
     *                     'srcFile' => "/webdirs/wfpm/$filename",);
     *
     * $ok = Util::httpFileTransferCall($url, $request, $credentials, $response, $uploadData);
     * // check if ok
     * // return from curl is in $response variable
     *
     * @return bool (false implies curl error or bad HTTP response code)
     * @throws Exception
     */
    public static function httpFileTransferCall($url, $request, $credentials, &$response, &$error, $uploadData=[], $ignoreCerts=false, $port=443)
    {
        // create a new cURL resource
        $ch = curl_init();
        $error = '';

        // credentials
        // This allows just a user name (authorized by key) or 'user:password' format
        if ( isset($credentials['userpwd']) ) {
            curl_setopt($ch, CURLOPT_USERPWD, $credentials['userpwd']);
        }
        // Private/Public key pair
        if ( isset($credentials['sslcertfile']) ) {
            curl_setopt($ch, CURLOPT_SSLCERT, $credentials['sslcertfile']);
            curl_setopt($ch, CURLOPT_SSLKEY, $credentials['sslkeyfile']);
            curl_setopt($ch, CURLOPT_SSLCERTTYPE, 'PEM');
            // private key passphrase (if any)
            if ( isset($credentials['sslkeypasswd']) ) {
                curl_setopt($ch, CURLOPT_SSLKEYPASSWD, $credentials['sslkeypasswd']);
            }
        }

        if ( $ignoreCerts || !Globals::$g->islive ) {
            // Allow for snake oil certificates
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        }

        curl_setopt($ch, CURLOPT_PROTOCOLS, (CURLPROTO_HTTP | CURLPROTO_HTTPS));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_PORT, $port);

        //this handles a download, list, or pre-connect for upload
        $response = curl_exec($ch);

        if ( curl_errno($ch) ) {
            $errno = curl_errno($ch);
            $error = curl_error($ch);
            Globals::$g->gErr->addError('**********', GetFL(), "Connect failed, URL=$url ERROR=$error ($errno)");
            return false;
        }

        $httpResponseCode = (int) curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
        // We only accept 2XX response codes. Anything else implies an error and we cannot perform the desired request
        if ( $httpResponseCode < 200 || $httpResponseCode >= 300 ) {
            $error = "Connect failed, HTTP response code: [$httpResponseCode], URL=$url";
            Globals::$g->gErr->addError('**********', GetFL(), $error);
            return false;
        }

        // Uploads are handled after an initial connect, to distinguish between a connection error vs upload error.
        if ( $request == 'upload' ) {

            if ( isset($uploadData['dst']) ) {
                // Append the destination (usually file name) to the url
                $url .= $uploadData['dst'];
            } else {
                throw new Exception("An upload destination was not specified.");
            }

            if ( isset($uploadData['srcBuffer']) ) {
                // If a buffer is provided, create a temporary file pointer using php://temp
                $src = $uploadData['srcBuffer'];
                $fp = fopen("php://temp", 'r+');
                fputs($fp, $src);
                rewind($fp);
                $size = strlen($src);
            } else if ( isset($uploadData['srcFile']) ) {
                // Use a regular file
                $src = $uploadData['srcFile'];
                $fp = fopen($src, 'r');
                $size = filesize($src);
            } else {
                throw new Exception("An upload data source was not provided.");
            }

            //Set options for upload
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_UPLOAD, 1);
            curl_setopt($ch, CURLOPT_INFILE, $fp);
            curl_setopt($ch, CURLOPT_INFILESIZE, $size);

            //upload the data
            $response = curl_exec($ch);

            if ( curl_errno($ch) ) {
                $errno = curl_errno($ch);
                $error = curl_error($ch);
                Globals::$g->gErr->addError('**********', GetFL(), "Uploading failed, URL=$url ERROR=$error ($errno)");
                return false;
            }

            $httpResponseCode = (int) curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
            // We only accept 2XX response codes. Anything else implies an error and we cannot perform the desired request
            if ( $httpResponseCode < 200 || $httpResponseCode >= 300 ) {
                $error = "Uploading failed, HTTP response code: [$httpResponseCode], URL=$url";
                Globals::$g->gErr->addError('**********', GetFL(), $error);
                return false;
            }

            fclose($fp);
        }

        // close cURL resource, and free up system resources
        curl_close($ch);

        return true;
    }


    /**
     * Curl SFTP call wrapper.
     *
     * @param string  $url          the URL where to place the SFTP call
     * @param string  $request      the type of request to make (upload, download, list)
     * @param array   $credentials  the credentials used to connect to the server
     * @param string  &$response    the response from the URL
     * @param string  &$error       curl error message (if any)
     * @param array   $uploadData   information used for upload (file or buffer)
     * @param int     $port         the port to connect to (default 22)
     *
     * Example usage:
     *
     * $url = 'sftp://www.sftpserver.com/';
     * $request = 'upload';
     * $credentials = array('userpwd' => "$user:$pwd");
     * $uploadData = array('dst' => $filename, // This is the destination, it can be the filename or a full path
     *                     'srcFile' => "/webdirs/wfpm/$filename",);
     *
     * $ok = Util::sftpCall($url, $request, $credentials, $response, $uploadData);
     * // check if ok
     * // return from curl is in $response variable
     *
     * @return bool success or failure
     * @throws Exception
     */
    public static function sftpCall($url, $request, $credentials, &$response, &$error, $uploadData=[], $port=22)
    {
        // create a new cURL resource
        $ch = curl_init();
        $error = '';

        // credentials
        // This allows just a user name (authorized by key) or 'user:password' format
        if ( isset($credentials['userpwd']) ) {
            curl_setopt($ch, CURLOPT_USERPWD, $credentials['userpwd']);
        }
        // Private/Public key pair
        if ( isset($credentials['prvkeyfile']) ) {
            curl_setopt($ch, CURLOPT_SSH_PRIVATE_KEYFILE, $credentials['prvkeyfile']);
            curl_setopt($ch, CURLOPT_SSH_PUBLIC_KEYFILE, $credentials['pubkeyfile']);
            curl_setopt($ch, CURLOPT_SSH_AUTH_TYPES, CURLSSH_AUTH_PUBLICKEY);
            // private key passphrase (if any)
            if ( isset($credentials['keypasswd']) ) {
                curl_setopt($ch, CURLOPT_KEYPASSWD, $credentials['keypasswd']);
            }
        }

        curl_setopt($ch, CURLOPT_PROTOCOLS, CURLPROTO_SFTP);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_PORT, $port);

        // Socks proxy use (if defined in ia_init.cfg);
        $proxy = GetValueForIACFGProperty('PROXY_SOCKS5');
        if ( !empty($proxy) ) {
            curl_setopt($ch, CURLOPT_PROXY, $proxy);
            curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
        }

        if ( $request == 'list' ) {
            curl_setopt($ch, CURLOPT_FTPLISTONLY, true);
        }

        //this handles a download, list, or pre-connect for upload
        $response = curl_exec($ch);

        if ( curl_errno($ch) ) {
            $errno = curl_errno($ch);
            $error = curl_error($ch);
            Globals::$g->gErr->addError('**********', GetFL(), "Connect failed, URL=$url ERROR=$error ($errno)");
            return false;
        }

        // Uploads are handled after an initial connect, to distinguish between a connection error vs upload error.
        if ( $request == 'upload' ) {

            if ( isset($uploadData['dst']) ) {
                // Append the destination (usually file name) to the url
                $url .= $uploadData['dst'];
            } else {
                throw new Exception("An upload destination was not specified.");
            }

            if ( isset($uploadData['srcBuffer']) ) {
                // If a buffer is provided, create a temporary file pointer using php://temp
                $src = $uploadData['srcBuffer'];
                $fp = fopen("php://temp", 'r+');
                fputs($fp, $src);
                rewind($fp);
                $size = strlen($src);
            } else if ( isset($uploadData['srcFile']) ) {
                // Use a regular file
                $src = $uploadData['srcFile'];
                $fp = fopen($src, 'r');
                $size = filesize($src);
            } else {
                throw new Exception("An upload data source was not provided.");
            }

            //Set options for upload
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_UPLOAD, 1);
            curl_setopt($ch, CURLOPT_INFILE, $fp);
            curl_setopt($ch, CURLOPT_INFILESIZE, $size);

            //upload the data
            $response = curl_exec($ch);

            if ( curl_errno($ch) ) {
                $errno = curl_errno($ch);
                $error = curl_error($ch);
                Globals::$g->gErr->addError('**********', GetFL(), "Uploading failed, URL=$url ERROR=$error ($errno)");
                return false;
            }

            fclose($fp);
        }

        // close cURL resource, and free up system resources
        curl_close($ch);

        return true;

    }

    /**
     * Kick off an IMS request to replicate Global DB info to a POD
     *
     * @param string $podURL  the POD URL
     * @param string $cnys    the list of companies that need to be updated
     *
     * @return bool success or failure
     */
    public static function kickOffGlobalDBReplication($podURL, $cnys)
    {
        $pub = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);
        $msgBody = array(
            'action' => 'replicateGlobalDB',
            'data' => $cnys,
            'podURL' => $podURL,
        );

        return $pub->PublishMsg(
            'REPLICATE_GLOBAL_DB',
            'INTACCT',
            'RUN_OFFLINEACTION',
            IMS_PRIORITY_DEFAULT,
            $msgBody,
            [],
            $response
        );
    }
    /**
     * Detect & replicate the global DB changes to all PODs
     *
     * @return bool success or failure
     */
    public static function replicateGlobalDB(): bool
    {
        $globalReplication = new GlobalReplication();

        //start replication
        $ok = $globalReplication->replicateGlobalDB();

        return $ok;
    }

    /**
     * Refresh the materialized view(s) in the database.
     *
     * @param string $viewName  the name of the view or views. If it's more than one view, the value is a string with
     *                           the view names separated by comma
     *
     * @return bool  success or failure
     */
    public static function refreshMaterializedView($viewName)
    {
        $stmt = array(
            "begin DBMS_MVIEW.REFRESH(:1, 'F', '', TRUE, FALSE, 0, 0, 0, FALSE, FALSE); end;",
            $viewName
        );
        return ExecStmt($stmt);
    }

    /**
     * Get the ID part of $iaIdWithName (e.g. ITEM001--Battery will return ITEM001)
     *
     * @param  string $iaIdWithName
     * @return string just the ID part
     */
    public static function getIDPart($iaIdWithName)
    {
        $pos = isl_strpos($iaIdWithName, self::INTACCTID_SEPARATOR);
        if ($pos !== false) {
            $iaId = isl_substr($iaIdWithName, 0, $pos);
        } else {
            $iaId = $iaIdWithName;
        }
        return $iaId;
    }

    /**
     * Return the number of decimals of a string
     * @param int|string $num string number
     *
     * @return int the number of decimals
     */
    public static function getNumberOfDecimals($num)
    {
        return strlen(substr(strrchr($num, "."), 1));
    }

    /**
     * Fetch the raw location data for a specific company. This is uslally invoked through DBRunner to be placed
     * on the right DB.
     *
     * @param int    $cny         the company
     * @param string $locationId  the location id
     *
     * @return array  the location raw record
     */
    public static function privateFetchRawLocation($cny, $locationId)
    {
        $saveUserId = Globals::$g->_userid;
        Globals::$g->_userid = '@' . $cny;
        $locMgr = new LocationManager();
        $rec = $locMgr->GetRaw($locationId);
        Globals::$g->_userid = $saveUserId;
        return $rec;
    }

    /**
     * Get the type of practice
     *
     * @param int|null $cny  the company record#
     *
     * @return string  the practice type, C, M, etc, or null if the company is not a practice
     */
    public static function getPracticeType($cny = null)
    {
        if ( ! $cny ) {
            $cny = GetMyCompany();
            if ( ! $cny ) {
                return null;
            }
        }
        $app = null;
        if (Profile::exists() && (Profile::getCompanyCacheProperty('company', 'RECORD#') == $cny)) {
            $app = Profile::getCompanyCacheProperty('practicecompany.practicetype');
        } else {
            $rslt = DBRunner::runOnCnyDB(
                'QueryResult',
                array(array("SELECT practicetype FROM practicecompany WHERE cny# = :1", $cny)),
                $cny
            );
            if ($rslt[0]) {
                $app = isl_trim($rslt[0]['PRACTICETYPE']);
            }
        }

        return $app;
    }

    /**
     * Retrieve all the log messages from Cache and append the to the corresponding log files
     * The messages are deleted from Cache
     *
     * @return int[]
     */
    public static function dumpAllLogMessagesFromCache()
    {
        $loggingMode = getenv('IA_LOGGING');
        if ($loggingMode == 5) {
            return self::dumpAllLogMessagesFromMemcache();
        }

        return ['pids' => 0, 'msgs' => 0];
    }

    /**
     * Process the array storing the log messages and append the messages to the corresponding log files
     *
     * @param array $logBuffer  array storing all the log messages together with the corresponding log files
     */
    public static function processBufferArrayAndDumpToFiles($logBuffer)
    {
        if (is_array($logBuffer)) {
            foreach ($logBuffer as $fname => $messages) {
                error_log(join("", $messages), 3, $fname);
            }
        }
    }

    /**
     * Retrieve all the log messages from Memcache and append the to the corresponding log files
     * The messages are deleted from Memcache
     *
     * @return int[]
     */
    public static function dumpAllLogMessagesFromMemcache()
    {
        $minPID = 1;
        $maxPID = GetValueForIACFGProperty('IA_MAX_PID') ? (int) GetValueForIACFGProperty('IA_MAX_PID')
                                                                 : 500000;

        $currentPID = $minPID;
        $batchSize = 500;

        $pidPorcessed = 0;
        $msgFetched = 0;

        $cacheClient = CacheClient::getProxyInstance();
        $cacheClient->setupConnection([['127.0.0.1', GetValueForIACFGProperty('IA_LOGMANAGER_MEMCACHE_PORT')]], false);

        while ( $currentPID <= $maxPID ) {
            $endPID = min($currentPID + $batchSize, $maxPID + 1);
            $pidKeys = [];
            for ( $ix = $currentPID; $ix < $endPID; $ix++ ) {
                $pidKeys[] = LOGFILESMAP . $ix;
            }

            $values = $cacheClient->get($pidKeys);

            if ( $values  ) {

                // in values we have a list of pairs in the form [PID_KEY => log files]
                // let's process each PID and dump their logs
                foreach ( $values as $pidKey => $pidInfo ) {

                    $pidPorcessed++;
                    // lock the PID to avoid dumping the logs at the same time with another instance of the
                    // cron or the process itself

                    $lockId = $pidKey . LogManagerMemcache::PID_LOCK_SUFFIX;
                    if ( $cacheClient->add($lockId, 1, LogManagerMemcache::PID_LOCK_EXPIRY, false) ) {
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $dummyADF = new AutoDestructFunc(
                            function() use ($lockId, $cacheClient) {
                                $cacheClient->delete($lockId);
                            }
                        );

                        // pidInfo is a list of log files
                        // let's fetch their counters first
                        $counterKeys = [];
                        foreach ( $pidInfo as $index => $file) {
                            $counterKeys[] = 'c' . $pidKey . '_' . $index;
                        }
                        $counters = $cacheClient->get($counterKeys);

                        // now let's go after the messages
                        $logBuffer = [];
                        foreach ($pidInfo as $index => $file) {
                            $counterKey = 'c' . $pidKey . '_' . $index;;
                            $cacheClient->delete($counterKey);
                            if (isset($counters[$counterKey])) {
                                $counter = $counters[$counterKey];
                                $sMsg = $counter['s'];
                                // the number of messages is not exactly what is stored in memcache since memcache is
                                // not updated for each message, it is up to the number stored in memcache +
                                // the threshold
                                $cMsg = $counter['c'] + LogManagerMemcache::FILE_INFO_CACHE_THRESHOLD;
                                for ($kx = $sMsg; $kx < $cMsg; $kx++) {
                                    $key = $pidKey . '_' . $index . '_' . $kx;
                                    $msg = $cacheClient->get($key);
                                    if ($msg) {
                                        $msgFetched++;
                                        $cacheClient->delete($key);
                                        $logBuffer[$file][] = $msg;
                                    }
                                }
                            }
                        }
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $dummyADF = null;
                        $cacheClient->delete($lockId);
                        self::processBufferArrayAndDumpToFiles($logBuffer);
                    }
                }
            }

            // we can't delete the PID keys since the PIDs could still be running
            // that means the keys of the processes that die unexpectedly will remain cached until
            // they expire

            // go to the next batch of processes
            $currentPID = $endPID;
        }

        return ['pids' => $pidPorcessed, 'msgs' => $msgFetched];
    }

    /**
     * Usage:
     *     $period_resArr = Util::getPeriodName("544");
     *     echo "\n" . " Period name for id 544 is:  " . $period_resArr[0]["NAME"];
     *
     * @param int $_period
     *
     * @return array
     */
    public static function getPeriodName($_period) 
    {

        $gManagerFactory = Globals::$g->gManagerFactory;

        // get the Start date to use to lookup the name
        [$startDate] = Period2Dates($_period);
        //echo "StartDate is: " . $startDate . "<br>";
        $rptPeriodMgr = $gManagerFactory->getManager('reportingperiod');
        $res = $rptPeriodMgr->GetList(
            array(
            'selects' => array('NAME'),
            //            'filters' => array(array(array('START_DATE', '=', $startDate), array('BUDGETING', '=', 'true')))
            'filters' => array(
                   0 => array(
                        array('START_DATE', '=', $startDate),
                        array('BUDGETING', '=', 'true')
                    ),
                )
            )
        );

            return $res;
    }

    /**
     * Return the name of the MST synonym for the given table name. If the table name is the MST synonym itself,
     * it would just return that name.
     *
     * @param string $table the table name
     *
     * @return string the MST synonym name
     */
    public static function getMstForTable($table)
    {
        if ( strtolower(substr($table, -3)) != 'mst' ) {
            $table .= 'mst';
        }
        return $table;
    }

    /**
     * Reserves (a small amount of) memory for a shutdown function so it can run to completion
     * in case the process terminated due to a memory limitation error.
     *
     * @param string $callable  the function to call (callable object)
     * @param int    $memory    amount of memory to reserve (pre-allocate) so the underlying function can execute
     */
    public static function registerShutdownFuncWithReserveMemory($callable, $memory = 1024)
    {
        if ( !is_callable($callable) ) {
            throw new Exception(__FUNCTION__ . " called without callable parameter.");
        }

        static $maxMemory = 10000000;
        if ( $memory > $maxMemory ) {
            throw new Exception(__FUNCTION__ . " called with memory parameter that exceeds maximum allowed ($maxMemory bytes).");

        }

        // This storage is freed on error (in case allowed memory is exhausted)
        $uniqueID = uniqid('reserveMemoryForShutdownFunc_');
        $GLOBALS[$uniqueID] = str_repeat('0', $memory);

        // Register the shell shutdown function (and free the memory when called)
        register_shutdown_function(function() use($callable, $uniqueID)
        {
            unset($GLOBALS[$uniqueID]);
            call_user_func($callable);
        });
    }
    /**
     * This method checks if a company name already exists ignoring case
     *
     * @param string $coId  company name that is about to be check if already exists
     *
     * @return bool
     */
    public static function companyExists($coId)
    {
        //TODO TA: Check should be done on POD GROUPS?
        $retVal = false;
        $qry = array("select TITLE from SCHEMAMAP where upper(TITLE)=:1", isl_strtoupper($coId));
        $res = QueryResult($qry, 0, '', GetGlobalConnection());
        if (isset($res[0])) {
            $retVal = true;
        }

        return $retVal;
    }

    /**
     * @param array    $fieldValues1
     * @param array    $fieldValues2
     * @param string[] $fieldNames
     *
     * @return bool
     */
    public static function anyChangeInFieldValues($fieldValues1, $fieldValues2, $fieldNames)
    {
        $anyChange = false;
        foreach ($fieldNames as $fieldName) {
            $fieldHasValue1 = isArrayValueProvided($fieldValues1, $fieldName);
            $fieldHasValue2 = isArrayValueProvided($fieldValues2, $fieldName);
            if ($fieldHasValue1 !== $fieldHasValue2 || ( $fieldHasValue1 && $fieldValues1[$fieldName] != $fieldValues2[$fieldName])) {
                $anyChange = true;
                break;
            }
        }
        return $anyChange;
    }

    /**
     * Execute aggregations and trimming on result sets returned from multiple schemas (by DBRunner::runOn*)
     *
     * @param array $multiResultSet  the multi-dB result set, aka a collection of results sets indexed by DB id
     * @param array $aggregation     the aggregations to perform
     * @param array $top             the trimming (TOP rows) to perform
     * @param bool  $injectDBId      true if this function should inject a column DBID in the result set
     * @param int   $dbColLength     the maximum length of the values in the DB column
     *
     * @return array the combined result set
     *
     * @throws Exception if incorrect aggregations or trimming info are passed in
     */
    public static function postProcessMultipleDBsResultSet(
        $multiResultSet,
        $aggregation,
        $top,
        $injectDBId,
        $dbColLength = 20
    ) {
        $result = [];

        if ( $injectDBId ) {
            foreach ( $multiResultSet as $dbId => &$dbResult ) {
                foreach ($dbResult as &$row) {
                    $row['DB'] = $dbId;
                }
                unset($row);
            }
            unset($dbResult);
        }

        $dbOverflow  = [];

        if ( $aggregation ) {
            $gb = explode(',', $aggregation['gb']);
            $totals = $aggregation['totals'];
            foreach ( $multiResultSet as $dbResult ) {
                if ( is_array($dbResult) ) {
                    foreach ( $dbResult as $row ) {
                        $keys = [];
                        foreach ( $gb as $col ) {
                            $keys[] = $row[$col];
                        }
                        $key = implode(':', $keys);
                        if ( ! isset($result[$key]) ) {
                            $result[$key] = $row;
                        } else {
                            foreach ( $totals as $total ) {
                                $col = $total[0];
                                $op = $total[1];
                                $val = $row[$col];
                                $prevVal = $result[$key][$col];
                                switch ( $op ) {
                                case 'sum':
                                    $result[$key][$col] = $prevVal + $val;
                                    break;
                                default:
                                    throw new Exception("Unexpected operation '$op'' for aggregation");
                                }
                            }
                            if ( $injectDBId && ! isset($dbOverflow[$key])  ) {
                                $result[$key]['DB'] .= ',' . $row['DB'];
                                if ( strlen($result[$key]['DB']) > $dbColLength ) {
                                    $result[$key]['DB'] = substr($result[$key]['DB'], 0, $dbColLength - 3)
                                        . '...';
                                    $dbOverflow[$key] = true;
                                }
                            }
                        }
                    }
                }
            }
            // if $top is also specified, we don't need to bother to remvoe the array keys, usort will do it
            if ( ! $top ) {
                $result = array_values($result);
            }
        } else {
            $result = DBRunner::mergeRunOnSomeResults($multiResultSet);
        }

        if ( $top ) {
            $orderCol = $top['order'][0];
            $multiplier = $top['order'][1] == 'DESC' ? -1 : 1;
            /** @noinspection PhpUnusedLocalVariableInspection */
            $ok = usort(
                $result,
                function($a, $b) use ($orderCol, $multiplier) {
                    $aa = $a[$orderCol];
                    $bb = $b[$orderCol];
                    if ( $aa == $bb ) {
                        return 0;
                    } else if ( $aa < $bb ) {
                        return -$multiplier;
                    } else {
                        return $multiplier;
                    }
                }
            );
            if ( isset($top['cnt']) ) {
                array_splice($result, $top['cnt']);
            }
        }
        return $result;
    }

    public static function executeRPCCall()
    {
        if ( ! Globals::$g->islive ) {
            LogToFile(
                "Authenticated user for RPC: " . ($_SERVER['PHP_AUTH_USER'] ?: '<none>')
                . "\n"
            );
        }
        
        if (!checkForMaintenance()) {
            // this pod is in maintenance, mark it so it can be reported
            Globals::$g->gErr->addError('**********', __FILE__ . '.' . __LINE__,"POD is in maintenance mode.");
        }

        $result = array();
        
        $isMaintenanceSet = false;
        $hasJSONinRequest = false;
        $gErr = Globals::$g->gErr;
        if ( self::allowDuringMaintenance() || checkForMaintenance(IA_MAINTENANCE_FILE, true)) {
            try {
                Backend_Init::Init();
                if (Request::$r->jsonData == '1') {
                    $hasJSONinRequest = true;
                    $requestString = Request::$r->requestString;
                    $request = json_decode($requestString, true);
                } else {
                    $request = unserialize(Request::$r->request);
                }
                //LogToFile("RPC.phtml: " . print_r($request, true) . "\n");
        
                if (is_array($request) && array_key_exists('func', $request)) {
                    $files = arrayExtractValue($request, 'files');
                    if (is_array($files)) {
                        foreach ($files as $file) {
                            include_once $file;
                        }
                    }
                    $args = arrayExtractValue($request, 'args');
                    
                    // check if the method called is static or not
                    if (is_array($request['func'])) {
                        try {
                            $class = $request['func'][0] ?? '';
                            $function = $request['func'][1] ?? '';
                            $methodIsStatic = new ReflectionMethod($class, $function);
                            if ($methodIsStatic->isStatic()) {
                                // if the method is static call call_user_func_array
                                $result['data'] = call_user_func_array($request['func'], ensureArray($args));
                            } else {
                                // if the method is not static instantiate the class and call the function on the class instance
            
                                // get the class instance
                                $classInstance = new $class();
            
                                // call the method
                                $result['data'] = $classInstance->$function(...$args);
                            }
                        } catch (Exception $e) {
                            logToFileError($e);
                            $result['data'] = call_user_func_array($request['func'], ensureArray($args));
                        }
                    } else {
                        $result['data'] = call_user_func_array($request['func'], ensureArray($args));
                    }
            
                } else {
                    $gErr->addError(
                        '**********',
                        __FILE__ . '.' . __LINE__,
                        'No function specified in the remote call.'
                    );
                }
            } catch (Exception $ex) {
                LogToFile("rpc.phtml caught an exception: " . $ex . "\n");
                $gErr->addError('GGM', __FILE__ . '.' . __LINE__, $ex->getMessage());
            }
            
        } else {
            if (Request::$r->jsonData == '1') {
                $hasJSONinRequest = true;
            }
            $isMaintenanceSet = true;
        }
    
        if ( $gErr->hasErrors() ) {
            $gErr->GetErrList($result['errors']);
            if ($isMaintenanceSet === true) {
                $result[self::MAINTENANCE_MODE] = 1;
            }
        }

        if ( $hasJSONinRequest === true ) {
            echo json_encode($result);
        } else {
            echo serialize($result);
        }
    }

    /**
     * Record CS Tools authantication
     *
     * @param string[] $cookies
     *
     * @return string|null
     *
     * @throws Exception
     */
    public static function authCSTools($cookies)
    {
        $cookieValueEncrypted = $cookies['php_mini_auth'];
        $doneValueEncrypted = $cookies['php_mini_auth_done'];

        if ($cookieValueEncrypted) {
            $cookieValue = TwoWayDecryptWithKey($cookieValueEncrypted, "IA_INIT");
            $cookieValue = explode("-", $cookieValue);

            $doneValue = TwoWayDecryptWithKey($doneValueEncrypted, "IA_INIT");

            $cookieUserID   = $cookieValue[0];
            $cookieSecret   = $cookieValue[1];

            $query = "update csaccl set string = '$cookieSecret' where record#='$cookieUserID'";
            $res = ExecSimpleStmt($query, 1, GetCSConnection());
            if ($res) {
                $memCacheClient = CacheClient::getInstance();
                if ($memCacheClient) {
                    $memCacheCookieKey = "auth_phtml_" . $cookieUserID;
                    $memCacheClient->set($memCacheCookieKey, $cookieValueEncrypted, 30); // 30 seconds expiration time

                    $memCacheDoneKey = "done_$memCacheCookieKey";
                    $memCacheClient->set($memCacheDoneKey, $doneValue, 30); // 30 seconds expiration time

                    return $memCacheCookieKey;
                } else {
                    logToFileError("CSTools authentication - Cannot instantiate cache instance");
                }
            } else {
                logToFileError("CSTools authentication - Cannot store cookie string in database");
            }
        }

        return null;
    }

    /**
     * Clean up practice tables : practiceclientusers, practiceclientuserloc, practiceclientsugroup, practiceclientugrouploc
     *
     * @return int error code; 1..4 for specific queries, 0 for no errors
     */
    public static function cleanUpPracticeTables()
    {
        $return = 0;
        // Remove from user assignments
        $sql = "DELETE FROM practiceclientusers WHERE entitycny# NOT IN (SELECT cny# FROM schemamap)";
        $ok = ExecStmtEx(array($sql));
        if ($ok === false) {
            $return = 1;
            addLog("Cannot clean up the 'practiceclientusers' table.", LogManager::ERROR);
        }

        $sql = "DELETE FROM practiceclientuserloc WHERE entitycny# NOT IN (SELECT cny# FROM schemamap)";
        $ok = ExecStmtEx(array($sql));
        if ($ok === false) {
            $return = 2;
            addLog("Cannot clean up the 'practiceclientuserloc' table.", LogManager::ERROR);
        }

        // Remove from usergroup assignments
        $sql = "DELETE FROM practiceclientsugroup WHERE entitycny# NOT IN (SELECT cny# FROM schemamap)";
        $ok = ExecStmtEx(array($sql));
        if ($ok === false) {
            $return = 3;
            addLog("Cannot clean up the 'practiceclientsugroup' table.", LogManager::ERROR);
        }

        $sql = "DELETE FROM practiceclientugrouploc WHERE entitycny# NOT IN (SELECT cny# FROM schemamap)";
        $ok = ExecStmtEx(array($sql));
        if ($ok === false) {
            $return = 4;
            addLog("Cannot clean up the 'practiceclientugrouploc' table.", LogManager::ERROR);
        }

        return $return;
    }

    /**
     * PHP count function backward compatibility wrapper for Php version before 8.0.  Starting from Php 8.0.0,
     * count() will throw TypeError on invalid countable types passed to the value parameter.  This wrapper
     * should only be used in case where calling of count() with potentially invalid value parameter type
     * cannot be avoided.
     *
     * @see https://www.php.net/manual/en/function.count#refsect1-function.count-returnvalues
     *
     * @param mixed $value
     *
     * @return int
     */
    public static function php7CompatCount($value, int $mode = COUNT_NORMAL) : int
    {
        if (is_countable($value)) {
            return count($value, $mode);
        } else {
            return is_null($value) ? 0 : 1;
        }
    }

    /**
     * This function may be used to maintain behavior between PHP 7/8 for non-strict equality to 0
     * NOTE: Recommended to use this as a guideline and reference
     *
     * - the empty part will handle: '', false, null, 0, 0.0, '0', []
     * - strings that start with numbers will be cast to float, which takes only the leading numbers
     * - WARNING: strings with a single 'e' or 'E' may be treated as scientific notation ("E notation")
     *
     * Example:
     * - OLD: if ( !isset($var) || $var == 0 ) {
     * - NEW: if ( Util::php7eq0($var ?? 0) ) {
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function php7eq0($value): bool
    {
        return !is_array($value) && (empty($value) || (is_string($value) && (0.0 === (float) $value)));
    }

    /**
     * This function may be used to maintain behavior between PHP 7/8 for non-strict equality between two values
     * NOTE: Recommended to use this as a guideline and reference
     *     *
     * @param mixed $value1
     * @param mixed $value2
     *
     * @return bool
     */
    public static function php7equal($value1, $value2): bool
    {
        if ($value1 === '' && $value2 !== '') {
            return self::php7eqEmptyStr($value2);
        }
        if ($value1 !== '' && $value2 === '') {
            return self::php7eqEmptyStr($value1);
        }
        if ((0 === $value1 || 0.0 === $value1) && 0 !== $value2 && 0.0 !== $value2) {
            return self::php7eq0($value2);
        }
        if (0 !== $value1 && 0.0 !== $value1 && (0 === $value2 || 0.0 === $value2)) {
            return self::php7eq0($value1);
        }
        
        return $value1 == $value2;
    }

    /**
     * This function may be used to maintain behavior between PHP 7/8 for comparison greater than 0
     * NOTE: Recommended to use this as a guideline and reference
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function php7gt0($value): bool
    {
        return
            (true === $value)
            || is_array($value)
            || ( (is_numeric($value) || is_string($value)) && (0.0 < (float) $value) );
    }

    /**
     * This function may be used to maintain behavior between PHP 7/8 for comparison less than 0
     * NOTE: Recommended to use this as a guideline and reference
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function php7lt0($value): bool
    {
        return ( (is_numeric($value) || is_string($value)) && (0.0 > (float) $value) );
    }

    /**
     * This function may be used to maintain behavior between PHP 7/8 for non-strict equality to empty string
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function empty(), with one exception: '0' (which is not equal to '')
     *
     * Example:
     * - OLD: if ( !isset($var) || $var == '' ) {
     * - NEW: if ( Util::php7eqEmptyStr($var ?? '') ) {
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function php7eqEmptyStr($value): bool
    {
        return !is_array($value) && empty($value) && ('0' !== $value);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for count
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function count()
     *
     * Example:
     * - OLD: if ( count($var) == 0 ) {
     * - NEW: if ( Util::php7Count($var) == 0 ) {
     *
     * @param mixed $value
     *
     * @return int
     */
    public static function php7Count($value): int
    {
        if (is_countable($value)) {
            return count($value);
        };
        if (null === $value) {
            return 0;
        }
        return 1;
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for strpos
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function strpos()
     *
     * Example:
     * - OLD: if ( strpos($haystack, $needle) === false || strpos($haystack, $needle) === null ) {
     * - NEW: if ( Util::php7Strpos($haystack, $needle) === false || Util::php7Strpos($haystack, $needle) === null ) {
     *
     * @param mixed $haystack
     * @param mixed $needle
     * @param int   $offset
     *
     * @return int|bool|null
     */
    public static function php7Strpos($haystack, $needle, int $offset = 0) : int|bool|null
    {
        if (is_object($haystack) || is_array($haystack)
        ) {
            return null;
        }
        if (null === $needle
            || (true === $haystack && true === $needle)
            || (false === $needle)
            || is_object($needle)
            || is_array($needle)
            || (is_int($haystack) && is_int($needle))
            || (is_float($haystack) && is_float($needle))
            || (strlen((string)$haystack) < $offset)
        ) {
            return false;
        }
        
        return strpos($haystack, $needle, $offset);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for mb_strpos
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function mb_strpos()
     *
     * Example:
     * - OLD: if ( mb_strpos($haystack, $needle) === false || mb_strpos($haystack, $needle) === null ) {
     * - NEW: if ( Util::php7MbStrpos($haystack, $needle) === false || Util::php7MbStrpos($haystack, $needle) === null ) {
     *
     * @param mixed $haystack
     * @param mixed $needle
     * @param int   $offset
     *
     * @return int|bool|null
     */
    public static function php7MbStrpos($haystack, $needle, int $offset = 0) : int|bool|null
    {
        if (is_object($haystack) || is_array($haystack) || is_object($needle) || is_array($needle)
        ) {
            return null;
        }
        if (null === $needle
            || (false === $needle)
            || (strlen((string)$haystack) < abs($offset))
        ) {
            return false;
        }
        
        return mb_strpos($haystack, $needle, $offset);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for mb_strstr
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function mb_strstr()
     *
     * Example:
     * - OLD: if ( mb_strstr($haystack, $needle) === false || mb_strstr($haystack, $needle, $before_needle) === false ) {
     * - NEW: if ( Util::php7MbStrstr($haystack, $needle) === false || Util::php7MbStrstr($haystack, $needle, $before_needle) === false ) {
     *
     * @param mixed $haystack
     * @param mixed $needle
     * @param bool  $before_needle
     *
     * @return string|false
     */
    public static function php7MbStrstr($haystack, $needle, $before_needle = false) : string|false
    {
        if (is_array($haystack) || is_object($haystack) || null === $haystack || '' === $needle
        ) {
            return false;
        }
        
        return mb_strstr($haystack, $needle, $before_needle);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for strstr
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function strstr()
     *
     * Example:
     * - OLD: if ( strstr($haystack, $needle) === false || strstr($haystack, $needle, $before_needle) === false ) {
     * - NEW: if ( Util::php7Strstr($haystack, $needle) === false || Util::php7Strstr($haystack, $needle, $before_needle) === false ) {
     *
     * @param mixed $haystack
     * @param mixed $needle
     * @param bool  $before_needle
     *
     * @return string|false
     */
    public static function php7Strstr($haystack, $needle, $before_needle = false) : string|false
    {
        if (is_array($haystack) || is_object($haystack) || null === $haystack || '' === $needle
        ) {
            return false;
        }
        
        return strstr($haystack, $needle, $before_needle);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8, unsupported values are forced to empty array
     * array_keys(array $array): array
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function array_keys()
     *
     * Example:
     * - OLD: $keys = array_keys($array);
     * - NEW: $keys = Util::php7ArrayKeys($array);
     *
     * @param mixed $array
     *
     * @return array
     */
    public static function php7ArrayKeys($array) : array
    {
        if (!is_array($array)) {
            return [];
        }
        
        return array_keys($array);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for round
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function round()
     *
     * Example:
     * - OLD: round($number, $precision, PHP_ROUND_HALF_UP)
     * - NEW: Util::php7Round($number, $precision, PHP_ROUND_HALF_UP)
     *
     * @param mixed $number
     * @param mixed $precision
     * @param mixed $mode
     *
     * @return float|false|null
     */
    public static function php7Round($number, $precision = 0, $mode = PHP_ROUND_HALF_UP) : float|false|null
    {
        if (is_array($number)) {
            return false;
        }
        if (null === $number || false === $number) {
            return (float) 0;
        }
        if (is_object($number) || true === $number) {
            return (float) 1;
        }
        try {
            $round = round($number, $precision, $mode);
        } catch (TypeError $e) {
            $round = (float) 0;
            if (substr_count($e->getMessage(), 'Argument #3 ($mode) must be of type int') > 0
                || substr_count($e->getMessage(), 'Argument #2 ($precision) must be of type int') > 0
            ) {
                $round = null;
            }
        }
        return $round;
    }

    /**
     * This function may be used to maintain behavior between PHP 7/8 for function mb_split
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function mb_split()
     *
     * Example:
     * - OLD: mb_split($pattern, $string, $limit)
     * - NEW: Util::php7MbSplit($pattern, $string, $limit)
     *
     * @param mixed $pattern
     * @param mixed $string
     * @param mixed $limit
     *
     * @return array|false
     */
    public static function php7MbSplit($pattern, $string, $limit): array|false
    {
        if (is_array($pattern) || is_object($pattern)
            || is_array($string) || is_object($string)
            || is_array($limit) || is_object($limit)
        ) {
            return false;
        }
        return mb_split($pattern, $string, $limit);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for function explode
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function explode()
     *
     * Example:
     * - OLD: explode($separator, $string, $limit)
     * - NEW: Util::php7Explode($separator, $string, $limit)
     *
     * @param mixed $separator
     * @param mixed $string
     * @param int $limit
     *
     * @return array|bool|null
     */
    public static function php7Explode($separator, $string, int $limit = PHP_INT_MAX) : array|bool|null
    {
        if (is_array($separator) || is_object($separator)
            || is_array($string) || is_object($string)
        ) {
            return null;
        }
        if (null === $separator || false === $separator || '' === $separator) {
            return false;
        }
        if (null === $string) {
            return [''];
        }
        return explode($separator, $string, $limit);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for function array_values
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function array_values()
     *
     * Example:
     * - OLD: array_values($array)
     * - NEW: Util::php7ArrayValues($array)
     *
     * @param mixed $array
     *
     * @return array|null
     */
    public static function php7ArrayValues($array) : array|null
    {
        if (!is_array($array)) {
            return null;
        }
        return array_values($array);
    }
    
    /**
     * This function may be used to maintain behavior between PHP 7/8 for function base64_decode
     * NOTE: Recommended to use this as a guideline and reference
     * - This is similar to the php function base64_decode()
     *
     * Example:
     * - OLD: base64_decode($string, $strict)
     * - NEW: Util::php7Base64Decode($string, $strict)
     *
     * @param mixed $string
     * @param bool  $strict
     *
     * @return string|bool|null
     */
    public static function php7Base64Decode($string, bool $strict = false) : string|bool|null
    {
        if (is_array($string) || is_object($string)) {
            return null;
        }
        return base64_decode($string, $strict);
    }

    /**
     * PHP count function backward compatibility wrapper for Php version before 8.0.  Starting from Php 8.0.0,
     * count() will throw TypeError on invalid countable types passed to the value parameter.  This wrapper
     * should only be used in case where calling of count() with potentially invalid value parameter type
     * cannot be avoided.
     *
     * This is very close to the above function but always returns 0 if not countable or the count of the items.
     * If the passed in $value has 0 items, then it will return 0. The typical use is in a loop where
     * you want to count for 0 to the number of elements in an array, so if not countable, i.e. not an array in most
     * cases, return 0.
     *
     * @param mixed $value
     *
     * @return int
     */
    public static function countOrZero($value, int $mode = COUNT_NORMAL) : int
    {
        // If the subject is an array, or simple countable, then we can just return the return of the call to the native
        // count function
        if (is_countable($value)) {
            return count($value, $mode);
        }

        // The code base often uses null as a default value for variables which are otherwise an array.  In these cases
        // we don't want to log an error.   However in the future if we should chose to abandon the "default null
        // standard" then what we should do is remove this if branch and allow the error to be logged when the parameter
        // is null.
        if ($value === null) {
            return 0;
        }

        // If here then we know there should not have been a call to the count function with the value provided
        logToFileError(
            __METHOD__
            . "; Subject provided is not an array, but rather a '"
            . gettype($value)
            . "'; exported: " . var_export($value, true)
            , true
        );
        return 0;
    }

    /**
     * This is a wrapper function for both arrays and countable objects to determine if they are empty.  This function
     * is written to be PHP 8.0 safe in that it is type checking the parameter and logging an error if the type is not
     * an array or countable.  The function assumes the object is empty if the parameter is not an array or countable.
     * .  Starting from Php 8.0.0,
     * count() will throw TypeError on invalid countable types passed to the value parameter.  This wrapper
     * should only be used in case where calling of count() with potentially invalid value parameter type
     * cannot be avoided.
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function isEmptyCountable($value): bool
    {
        // This first condition is included to avoid logging errors on nulls as null is often used as a default value
        // in our code base.
        if ($value === null) {
            return true;
        }

        // Handle arrays
        if ( is_array($value) ) {
            return empty($value);
        }

        // Handle countable objects
        if ( is_countable($value) ) {
            return (count($value) === 0);
        }

        // If here then the value provide is not countable, log an error about it
        logToFileError(
            __METHOD__
            . "; Subject provided is not countable, but rather a '"
            . gettype($value)
            . "'; exported: " . var_export($value, true)
            , true
        );
        return true;
    }

    /**
     * PHP array_values function backward compatibility wrapper for Php version before 8.0.  Starting from Php 8.0.0,
     * array_values() will throw TypeError on non-array types passed to the value parameter.  This wrapper
     * should only be used in cases where calling of array_values() with potentially invalid value parameter type
     * cannot be avoided.
     *
     * @param mixed $subject
     *
     * @return array
     */
    public static function array_values_typeCheck($subject) : array
    {
        // If the subject is an array then we can just return the return of the call to the native array_values function
        if (is_array($subject)) {
            return array_values($subject);
        }

        // The code base often uses null as a default value for variables which are otherwise an array.  In these cases
        // we don't want to log an error.   However in the future if we should chose to abandon the "default null
        // standard" then what we should do is remove this if branch and allow the error to be logged when the parameter
        // is null.
        if ($subject === null) {
            return [];
        }

        // If here then we know there should not have been a call to the count function with the value provided
        logToFileError(
            __METHOD__
            . "; Subject provided is not an array, but rather a '"
            . gettype($subject)
            . "'; exported: " . var_export($subject, true)
            , true
        );
        return [];
    }
    
    /**
     * PHP in_array function that is 8.1 compatible but do not accept object as second parameter.
     * 0 == '' will return false
     * 0 == 'fo' will return false
     *
     * @param mixed $needle   The searched value.
     * @param mixed $haystack The array.
     * @param bool  $strict   If the third parameter strict is set to true then the in_array() function will also check the types of the needle in the haystack
     *
     * @return bool
     */
    public static function php81InArray($needle, $haystack, bool $strict = false): bool
    {
        if (!is_array($haystack)) {
            return false;
        }
        if ($needle === 0) {
            return in_array($needle, $haystack, true);
        }
        if (is_string($needle) && false !== array_search(0, $haystack, true)) {
            $haystackWithoutZero = array_filter($haystack, function($value) { return $value !== 0;}, 0);
            return in_array($needle, $haystackWithoutZero, $strict);
        }
        return in_array($needle, $haystack, $strict);
    }
    
    

    /**
     * Skip maintenance checks if this method returns true
     * For now - allow Kong initial config to be built during maintenance
     *
     * @return bool
     */
    private static function allowDuringMaintenance() : bool
    {
        // strpos should not be false or 0
        return ! empty(Request::$r->requestString) && strpos(Request::$r->requestString, 'KongConfGenerator')
                 && strpos(Request::$r->requestString, 'read-config') ;
    }

    /**
     * Wrapper for strtoupper
     * @param mixed $string   The value we want to process.
     *
     * @return mixed
     */
    public static function php7strtoupper($string)
    {
        if (is_array($string) || is_object($string)){
            return null;
        }
        if ($string == null){
            return "";
        }
        return strtoupper($string);
    }
    
    /**
     * Wrapper for strtolower
     * @param mixed $string   The value we want to process.
     *
     * @return mixed
     */
    public static function php7strtolower($string)
    {
        if(is_array($string) || is_object($string)){
            return null;
        }
        if ($string == null){
            return "";
        }
        return strtolower($string);
    }

}
