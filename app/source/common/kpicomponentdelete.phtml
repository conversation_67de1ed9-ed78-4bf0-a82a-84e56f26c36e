<?php
require_once 'util.inc';
require_once 'KpiComponentManager.cls';

Init();

if ( !CsrfUtils::verifyInputToken(Request::$r->_op) ) {
    popupErrorAndShutdown(
        'BL03002133',
        __FILE__.":".__LINE__,
        "Invalid request. Please Retry.",
        "CSRF: kpicomponentdelete csrf token mismatch",
        false
    );
}

$kpi = KpiComponentEditor::getKpi($_REQUEST['_r']);
if(is_array($kpi)) {
    if ($kpi['KPITYPE'] == 2) {
        $kpiComponentManager = new ListCardComponentManager();
        $ok = $kpiComponentManager->Delete($_REQUEST['_r']);
        if (!$ok) {
            $msg = 'Cannot delete List Card Component';
            Globals::$g->gErr->addError('GL-6601', __FILE__ . ':' . __LINE__, $msg);
            Globals::$g->gUIUtils->ShowError();
        };

    } else {
        //default behaviour to performance cards of type == 1 for now
        $kpiComponentManager = new KpiComponentManager();
        $ok = $kpiComponentManager->Delete($_REQUEST['_r']);
        if (!$ok) {
            $msg = 'Cannot delete KPI Component';
            Globals::$g->gErr->addError('GL-6602', __FILE__ . ':' . __LINE__, $msg);
            Globals::$g->gUIUtils->ShowError();
        };

    }
} else {
// for IA Kpi components delete
    $iakpi = IAKpiComponentEditor::getKpi($_REQUEST['_r']);
    if (is_array($iakpi)) {
        $iakpiComponentManager = new IAKpiComponentManager();
        $ok = $iakpiComponentManager->Delete($_REQUEST['_r']);
        if (!$ok) {
            $msg = 'Cannot delete KPI Component';
            Globals::$g->gErr->addError('GL-6602', __FILE__ . ':' . __LINE__, $msg);
            Globals::$g->gUIUtils->ShowError();
        };
    }
}

?>
<!doctype=html>
<html xmlns="http://www.w3.org/1999/html">
<head>
</head>
<body>

<?php
if ($ok) {
    //todo: need to change to ajax call.
    echo '<script>window.parent.showScrimAndNav();</script>';
}
?>

</body>
</html>