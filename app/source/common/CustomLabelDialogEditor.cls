<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Sage Intacct, Inc. All rights reserved.
 */

class CustomLabelDialogEditor extends FormEditor
{
    /**
     * @return string[]
     */
    protected function getJavaScriptFileNames()
    {
        $scripts = parent::getJavaScriptFileNames();
        $scripts[] = '../resources/js/customlabeldialog.js';

        return $scripts;
    }

    /**
     * @param string $state
     *
     * @return array
     */
    public function getStandardButtons($state)
    {
        $buttons = [];
        $suffix = Request::$r->_suffix ?? '';
        $this->setButtonDetails($buttons, Editor_SaveBtnID,
                                'dobutton', 'IA.SAVE', 'create', false, "saveData('$suffix')",
                                false, false);
        $this->setButtonDetails($buttons, Editor_CancelBtnID,
                                'cancelbutton', 'IA.CANCEL','cancel', false);
        return $buttons;
    }

    /**
     * @return array
     */
    protected function getEditorGlobals() : array
    {
        $vars = [];
        $localeList = getLocaleList()['LOCALE_VALUE'];
        $locales = [];
        foreach ($localeList as $locale) {
            $locales[] = getFormattedLocale($locale, '%1$s-%2$s');
        }
        $vars['LOCALES'] = json_encode($locales);
        $vars['_LOCALES'] = json_encode($localeList);
        
        $vars['_CURRENT_USER_LOCALE'] = getUserLocale();
        $vars['_CURRENT_COMPANY_LOCALE'] = getCompanyLocale();

        $vars['LABELFIELD'] = Request::$r->LABELFIELD;
        return $vars;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        /** @var CustomLabelManager $mgr */
        $mgr = Globals::$g->gManagerFactory->getManager("customlabel");

        $customLabels = json_decode(Request::$r->_data, true);
        $customLabelID = array_unique(array_filter(array_column($customLabels, 'ID')));
        if (count($customLabelID) > 1) {
            logToFileError(__FILE__ . '.' . __LINE__ . ' - Multiple IDs detected for Custom Label: '. Request::$r->_data);
            $customLabelID = CustomComponentManager::getUniqueID();
        } elseif (count($customLabelID)) {
            $customLabelID = array_values($customLabelID)[0];
        } else {
            $customLabelID = CustomComponentManager::getUniqueID();
        }
        $dummyLine = $obj['CUSTOMLABELS'][0];
        $obj['CUSTOMLABELS'] = [];
        $obj['ID'] = $customLabelID;

        foreach($customLabels as $customLabel) {
            if (empty($customLabel['ID'])) {
                $customLabel['ID'] = $customLabelID;
            }
            if ($mgr->isValidCustomLabel($customLabel)) {
                array_push($obj['CUSTOMLABELS'], $customLabel);
            }
        }
        array_push($obj['CUSTOMLABELS'], $dummyLine);
        if ( isset(Request::$r->FIELDID) ) {
            $obj['FIELDID'] = Request::$r->FIELDID;
        } else {
            $view = $this->getView();
            $view->findComponents([ 'path' => 'FIELDID' ], EditorComponentFactory::TYPE_FIELD, $components);
            foreach ( $components as $field ) {
                $field->setProperty('hidden', true);
            }
        }

        return parent::mediateDataAndMetadata($obj);
    }
    
    /**
     * I18N. Collection of tokens used primarily in JavaScript.
     *
     * @return array $textTokens
     */
    protected function getFormTokens(): array
    {
        $this->textTokens[] = 'IA.EDIT_CUSTOMLABELS';
        $this->textTokens[] = 'IA.ADD_AN_ENTRY_FOR_YOUR_COMPANY_S_LANGUAGE';
        
        return parent::getFormTokens();
    }
}
