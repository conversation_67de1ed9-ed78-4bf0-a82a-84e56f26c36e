<? 
    require_once 'util.inc'; 
    require_once 'html_header.inc';
    require_once 'menu_lib.inc'; 
    Request::$r->_op = GetOperationId('dummyid');
    Init();

    $props = array();
    $props['bgimage'] = '../resources/images/ia-app/calc/calc_bg.gif';
    $props['bgcolor'] = '#CCCCCC';
    $props['nocheck'] = true;
    $props['nojs'] = true;
    $props['nohotkey'] = true;
    $props['nobeancss'] = true;
    $props['incJSValidationFiles'] = false;
    $props['title'] = 'Intacct Applications';

    PrintCommonHtmlHeader($props);
?>

<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td><img src="../resources/images/ia-app/calc/intacct.gif" alt="" width="76" height="25" border="0"></td>
<td align="right"><a href="javascript:window.close();"><img src="../resources/images/ia-app/calc/x.gif" alt="" width="18" height="25" border="0"></a></td>
</tr>
</table>
<br>

<b>Choose an Application:</b>
<table border="0" cellpadding="1" cellspacing="0" width="99%" bgcolor="#999999"><tr><td valign="top">
<table border="0" cellpadding="0" cellspacing="1" width="100%">
<? ShowModuleList(); ?>
</table>
</td></tr></table>
</body>
</HTML>
