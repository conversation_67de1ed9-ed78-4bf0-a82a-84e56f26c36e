<?

/** 
 *  FILE:            ItemPaymentEditor.cls
 *  AUTHOR:            <PERSON>
 *  DESCRIPTION:    Common editor functions for payments that target specific 
 *                    amounts to specific line items of the document being paid.
 *
 *  (C) 2001, Intacct Corporation, All Rights Reserved                
 *
 *  This document contains trade secret data that belongs to Intacct       
 *  Corporation and is protected by the copyright laws.  Information 
 *  herein may not be used, copied or disclosed in whole or in part        
 *  without prior written consent from Intacct Corporation.          
 */



import('Editor');
require_once "backend_prrecord.inc";



/**
*/
class ItemPaymentEditor extends Editor
{
    /**
     * @var string $_itemOwnerVar
     */
    var $_itemOwnerVar = "_inv";
    /**
     * @var null|string $_itemOwnerKey
     */
    var $_itemOwnerKey; // PRRECORD.RECORD# OF THE DOCUMENT WHOSE LINE ITEMS ARE BEING EDITED
    /**
     * @var string $_selectedPymtVar
     */
    var $_selectedPymtVar = "_pymt";
    /**
     * @var null|string $_selectedPymtKey
     */
    var $_selectedPymtKey; // PRRECORD.RECORD# OF ANY PENDING PAYMENT TO THE $_itemOwnerKey DOCUMENT
    /**
     * @var string $_paymentAmtVar
     */
    var $_paymentAmtVar = "_amt";
    /**
     * @var null|string $_paymentAmt
     */
    var $_paymentAmt;
    /**
     * @var string $_crAvailAmtVar
     */
    var $_crAvailAmtVar = "_cr";
    /**
     * @var null|string $_crAvailAmt
     */
    var $_crAvailAmt;
    /**
     * @var string $_crAppliedAmtVar
     */
    var $_crAppliedAmtVar = "_creditApplied";
    /**
     * @var null|string $_crAppliedAmt
     */
    var $_crAppliedAmt;
    /**
     * @var string $_crTargetVar
     */
    var $_crTargetVar = "_item";
    /**
     * @var null|string $_crTargetKey
     */
    var $_crTargetKey; // PRENTRY.RECORD# OF THE INVOICE THIS DOCUMENT (CREDIT) IS PAYING
    /**
     * @var string $_autoSplitVar
     */
    var $_autoSplitVar = "_autosplit";
    /**
     * @var null|string $_autoSplit
     */
    var $_autoSplit;
    /**
     * @var string[] $_itemOwnerRec
     */
    var $_itemOwnerRec;
    /**
     * @var array $_defaultAmtsMap
     */
    var $_defaultAmtsMap;
    /**
     * @var null|string $_docEntity
     */
    var $_docEntity;
    /**
     * @var array $_paymentLines
     */
    var $_paymentLines;
    /**
     * @var null|string $_discAvailAmt
     */
    var $_discAvailAmt;
    /**
     * @var null|string $_discAppliedAmt
     */
    var $_discAppliedAmt;
    /**
     * @var string $_discAvailAmtVar
     */
    var $_discAvailAmtVar = "_discAvail";
    /**
     * @var string $_discAppliedAmtVar
     */
    var $_discAppliedAmtVar = "_discApplied";


    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        parent::__construct($_params);
        $this->_itemOwnerKey    = Request::$r->{$this->_itemOwnerVar};
        $this->_selectedPymtKey = Request::$r->{$this->_selectedPymtVar};
        $this->_paymentAmt      = Request::$r->{$this->_paymentAmtVar};
        $this->_crAvailAmt      = Request::$r->{$this->_crAvailAmtVar};
        $this->_crAppliedAmt    = Request::$r->{$this->_crAppliedAmtVar};
        $this->_crTargetKey     = Request::$r->{$this->_crTargetVar};
        $this->_autoSplit       = Request::$r->{$this->_autoSplitVar};
        $this->_docEntity       = Request::$r->_entity;
        $this->_discAvailAmt    = Request::$r->{$this->_discAvailAmtVar};
        $this->_discAppliedAmt  = Request::$r->{$this->_discAppliedAmtVar};

        if ($this->_itemOwnerKey) {
            GetPRRecord($this->_itemOwnerKey, $this->_itemOwnerRec);
        }
    }


    /**
     * @param array $_params
     *
     * @return bool
     */
    function ProcessEditNewAction(&$_params)
    {
        return $this->ProcessEditAction($_params);
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    function ProcessEditAction(&$_params)
    {
        $entityMgr = Globals::$g->gManagerFactory->getManager($_params['entity']);
        
        if ($this->ProcessErrorRetrivalAction($entityMgr)) {        
            return true;
        }

        Request::$r->_changed = false;
        $objId = Request::$r->{Globals::$g->kId};

        $invId = $this->_itemOwnerKey;
        /**
         * @var ItemPaymentManager $entityMgr
         */
        $obj = $entityMgr->GetDisplayItemsForPayment($invId, $this->_paymentAmt, $this->_discAvailAmt, $this->_crAppliedAmt, $this->_selectedPymtKey, "", false, true);
        $this->_paymentLines = $obj;

        if (!$obj) {
            global $gErr;
            $entityDesc = $_params['entityDesc'];
            $gErr->addError("BL03000021", __FILE__ . ":" . __LINE__, _("Fetching $entityDesc '$objId' failed"));
            $this->state = $this->kErrorState;
        }
        else {
            $newObj = array();
            foreach($obj as $itemEntry) {
                $this->_defaultAmtsMap[$itemEntry['RECORDNO']] = $itemEntry['PAIDAMOUNT'];

                /** @noinspection PhpSillyAssignmentInspection */
                $itemEntry['AMOUNT']        = $itemEntry['AMOUNT'];
                /** @noinspection PhpSillyAssignmentInspection */
                $itemEntry['TOTALDUE']        = $itemEntry['TOTALDUE'];
                $itemEntry['PAIDAMOUNT']    = $this->_autoSplit ? $itemEntry['PAIDAMOUNT'] : 0.00;
                $itemEntry['ENTITYDISCOUNTS'] = $this->_discAvailAmt;
                $itemEntry['DISCOUNTS']        = $this->_autoSplit ? $itemEntry['DISCOUNTS'] : 0.00;
                $itemEntry['ENTITYCREDITS'] = $this->_crAvailAmt;
                $itemEntry['CREDITS']        = $this->_autoSplit ? $itemEntry['CREDITS'] : 0.00;
                $newObj['ITEM_LIST_INFO'][] = $itemEntry;                
            }
            Request::$r->SetCurrentObject($newObj);
            $this->state = $this->kShowEditState;
        }
        return true;
    }


    /**
     * @param array  $_fields
     * @param string $_mode
     * @param int    $_actualcols
     * @param int    $line
     */
    function ShowMultiLineRow($_fields, $_mode, $_actualcols, $line = 0)
    {

        $linecol = "#000000";
        $onclick = "toggleColor($line); if (parent.frames[1].SwitchItemContext) { parent.frames[1].SwitchItemContext($line); } ";
        $idTag = ($_mode == 'inputs') ? "id='rowIndex[$line]' OnClick='$onclick'" : "";

        for ($iV = 0; $iV < $this->MultilineMaxVertical($_fields); $iV++) { 
            $col = $iV % 2 ? '#CCCCCC' : '#DDDDDD';  ?>
        <TR <?=$idTag?>>
        <?  $first = true;
         $totalcols = 0;
        foreach ($_fields['cells'] as $cell) {
            $totalcols++;
            $vcell = $cell['_args'][$iV];

            //All the rest of them are treated hidden
            if ($totalcols > $_actualcols) {
                if ($vcell['hidden'] && $_mode == 'inputs') {
                    ?>
                  <TD width="0"><INPUT type="hidden" name="<?= $vcell['varname'] ?>" value="<?= $vcell['value'] ?>"></TD>
                <?
                }else {
                    ?>
                 <TD width="0"></TD>

                    <?
                }
                continue;
            }

            $colwidth = $vcell['type']['multilinecolsepr'] ?:7;
            $defaultalign = 'left';
            $colalign = $vcell['type']['colalign'] ?:$defaultalign;
            $bgcol = $first ? $linecol : $col;
            $width = $first ? 1 : $colwidth;
            $firstbgcol = $first ? "bgcolor='$bgcol'" : "";
            ?>
			
         <TD width="1" <?=$firstbgcol?>><? echo Transparent($width, 1); ?></TD>
         <TD valign="middle" align="<? echo $colalign; ?>" nowrap>

        <?    if ($vcell['_func'] == 'LineNo' && $_mode == 'inputs') { ?>
				<FONT size=1>&nbsp;<?  echo ($line+1);  ?></FONT>
				<?
}
elseif ($vcell && $_mode == 'labels') { 
            $this->ShowSimpleFieldLabel($vcell, true); 
}
elseif ($vcell) {
    $this->ShowSimpleFieldValue($vcell); 
}
else { 
            echo '&nbsp;'; 
}
                ?>
         </TD>
        <? 
        $first = false;
        
        ?>
        <?    
        } ?>
     <TD width="0" bgcolor="<? echo $linecol; ?>"><? echo Transparent(1, 1); ?></TD>
		

     </TR>
        <? 
        } ?>
     <TR>
      <TD height="1" bgcolor="<? echo $linecol; ?>" colspan="<? echo
        count($_fields['cells']) * 2 + 1; ?>" ><? echo Transparent(1, 1); ?></TD>
     </TR>
        <?
    }


    /**
     * @param array $_field
     */
    function ShowSimpleFieldValue(&$_field)
    {
        global $gWarnOnSaveJS;
        if ($_field['path'] == 'PAYFULL') {
            $name = $_field['varname'];
            $value = $_field['value'];
            $rownum = $_field['rownum'];

            $totaldue = $this->_paymentLines[$rownum]['TOTALDUE'];
            $paidamount = $this->_paymentLines[$rownum]['PAIDAMOUNT'];
            $credits = $this->_paymentLines[$rownum]['CREDITS'];

            $amountTaken = bcadd($credits, $paidamount);
            ?>
         <input type=hidden name="<?=$name?>" value="<?=$value?>" size=20>
         <input onchange="<?=$gWarnOnSaveJS?>" class="noborder" type=checkbox name="<? echo "helper_$name"; ?>" value="" 
            <? echo ($amountTaken == $totaldue) ? 'checked' : ''; ?>
         onClick="PayFullLine(this, <?=$rownum?>, <?=$totaldue?>);" size=20 >
            <?
        } else {
            parent::ShowSimpleFieldValue($_field);
        }
    }


    /**
     * @param array $_params
     *
     * @return array
     */
    function MultilineLayout_Expand($_params)
    {
        $_params = Editor::MultilineLayout_Expand($_params);
        $recordtype = $this->_itemOwnerRec['RECORDTYPE'];
        $recordID = $this->_itemOwnerRec['RECORDID'];
        $entity = $this->_itemOwnerRec['ENTITY'];
        $date = FormatDateForDisplay($this->_itemOwnerRec['WHENCREATED']);

        $prrecMgr = Globals::$g->gManagerFactory->getManager('prrecord');
        $docName = $prrecMgr->DocNameFromRecordtype($recordtype);

        $_params['title'] = "$entity &nbsp;&nbsp;&nbsp;  $docName" . (isl_trim($recordID) ? ": " . isl_trim($recordID) : "") . " &nbsp;&nbsp;&nbsp; Date: $date";

        return $_params;
    }


    /**
     * @param array $_params
     *
     * @return array
     */
    function MultilineLayout_Instantiate($_params)
    {
        $ret = Editor::MultilineLayout_Instantiate($_params);
        if ($ret['path'] == 'ITEM_LIST_INFO') { 
            for($i=0; $i < count($ret['rows']); $i++) {

                // ADD THE URL INFO AND FORMAT THE NUMBERS (NEGATIVES COULD NOT BE TYPED AS CURRENCY IN THE xml FILE)
                for($j=0; $j < count($ret['rows'][$i]['cells']); $j++){
                    $acell =& $ret['rows'][$i]['cells'][$j];

                    for($k=0; $k < count($acell['_args']); $k++){
                        $aarg =& $acell['_args'][$k];

                        $totaldue = $this->_paymentLines[$i]['TOTALDUE'];

                        if ($aarg['path'] == 'CREDITS') {
                            $aarg['onfocus'] = "previousCreditValue = this.value;";
                            $aarg['onchange'] = "this.value = Math.abs(this.value); CheckLineAmount(this, $i, $totaldue); ProcessCreditsDistrubution($i, this);";
                            $aarg['readonly'] = ($totaldue < 0 && Request::$r->_noCredits == 1);
                        }
                        if ($aarg['path'] == 'CREDITLINK') {
                            $aarg['onclick'] = "if (parent.frames[1].SwitchItemContext) { parent.frames[1].SwitchItemContext($i); } ";
                            $aarg['statusmsg'] = "Apply Credit Line Items";
                            if ($totaldue < 0) {
                                $aarg['hreftxt'] = "";
                            }
                        }

                        if ($aarg['path'] == 'PAIDAMOUNT') {
                            $aarg['onchange'] = "this.value = Math.abs(this.value); CheckLineAmount(this, $i, $totaldue); RefreshMultilineTotal(this,'Layer0','Layer0_form','_obj__ITEM_LIST_INFO','PAIDAMOUNT');";
                        }

                        if ($aarg['path'] == 'DISCOUNTS') {
                            $aarg['hidden'] = (Request::$r->_noDiscounts == 1);
                        }
                        unset($aarg);
                    }
                    unset($acell);
                }
            }
        }

        return $ret;
    }

    /**
     * @param array $_params
     *
     * @return int
     */
    function FigureOutNumOfRows($_params)
    {
        return count(Request::$r->cache['currentobject']['ITEM_LIST_INFO']);
    }

    // FOR SOME REASON, OVERRIDING THIS FUNCTION AS BELOW LOSES THE TOTALING JS ON THE SELECTED AMOUNTS

    /**
     * @param array $_fields
     * @param int   $_pos
     */
    function ShowMultilineRefresh(&$_fields, $_pos)
    {
        if (!$_fields['readonly']) {
            $fldname =   Request::PathToFieldName($_fields['varpath'] .  '_numofrows') . "_$_pos"; 
            $onClickCode = "ResetDefaults();";
        ?>
        <TD align="right" valign="left" colspan="4" nowrap>
          <TABLE cellpadding="0" cellspacing="0" border="0" >
        <TR>
            <?
            if ($_fields['norefreshlink']) { ?>
             <TD> &nbsp </TD>
            <? 
            } else { ?>
                 <TD valign="top" align="left" height=20 nowrap><A
                 href="#Skip" onclick="javascript:<? echo $onClickCode; ?>" ><FONT size=2>Reset Default Amounts</A></FONT>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
                 <TD valign="middle" align="right" nowrap>
                  <input type=hidden name=<? echo $fldname ?> value=<? echo $_fields['actualnumofrows'] ?>>&nbsp;</TD>
                 <TD valign="middle" align="right" >&nbsp;<TD>
                <? 
            } ?>
      </TR>
       </TABLE>
      </TD>
        <?
        } else {
        ?>
          <TD><FONT>&nbsp;</FONT></TD>
        <?
        }
    }


    /**
     * @param array $_params
     */
    function ShowDeliverButton($_params)
    { 
    }


    /**
     * @param array $_params
     */
    function ShowCopyButtons($_params)
    {
    }

    

    function PrintOnLoad() 
    {
        Editor::PrintOnLoad();

        echo "parentElementsHdl = parent.window.opener.document.forms[0].elements; ";

        if (!$this->_autoSplit) {
            echo "GetSplitSelections(); ";
        }
    }


    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     * @param bool $addYuiCss  include the YUI css files
     */
    function showScripts($addYuiCss = true)
    {
        parent::showScripts($addYuiCss);
        $itemInfoArr = Request::$r->cache['currentobject']['ITEM_LIST_INFO'];
        $i = 0;
        ?>
    <script language=javascript>
     var currentRowIndex;
     var previousCreditValue = 0.00;
     var parentElementsHdl;
     var rowKeyMap = new Array();
     var nonCreditableMap = new Array();
     var docEntity = '<?=$this->_docEntity?>';
        <? 
        foreach ($itemInfoArr as $itemInfo) { ?>
         rowKeyMap[<?= $i ?>] = <?= $itemInfo['RECORDNO'] ?>;
            <? if ($itemInfo['AMOUNT'] < 0) { ?>
				nonCreditableMap[<?= $i ?>] = true;
				<? 
}
                $i++; ?>
        <?    
        } ?>

     function PayFullLine(element, index, totaldue) {

      var amountFld = element.form.elements['_obj__ITEM_LIST_INFO('+index+')__PAIDAMOUNT'];
      if (element.checked) {
       amountFld.value = GetFullLineAmountToPay(element, index, totaldue);
      } else {
       amountFld.value = '0.00';
      }
      RefreshMultilineTotal(element,'Layer0','Layer0_form','_obj__ITEM_LIST_INFO','PAIDAMOUNT');
     }

     function CheckLineAmount(element, index, totaldue) {
      var frm = element.form;
      var paidamount = parseFloat(frm.elements['_obj__ITEM_LIST_INFO('+index+')__PAIDAMOUNT'].value.replace(/,/g,""));
      var creditTaken = parseFloat(frm.elements['_obj__ITEM_LIST_INFO('+index+')__CREDITS'].value.replace(/,/g,""));
      var payCheckBox = frm.elements['helper__obj__ITEM_LIST_INFO('+index+')__PAYFULL'];

      var amountUsed = paidamount + creditTaken;

      if (amountUsed > totaldue) {
       alert('Amount due on line is '+totaldue);
       element.value = parseFloat((element.value).toString().replace(/,/g,"")) - (amountUsed - totaldue);
       payCheckBox.checked = true;
      } else {
       if (amountUsed == totaldue) {
        payCheckBox.checked = true;
       } else {
        payCheckBox.checked = false;
       }
      }
      return true;
     }

     function GetFullLineAmountToPay(element, index, totaldue) {

      var creditFld = element.form.elements['_obj__ITEM_LIST_INFO('+index+')__CREDITS'];
      if (creditFld.value)	{
       appliedcredit = parseFloat((creditFld.value).replace(/,/g,""));
      } else {
       appliedcredit = 0;
      }

      selectedamount = totaldue - appliedcredit;
      return RoundCurrency(selectedamount);
     }
		
     function ProcessCreditsDistrubution(index, creditField) {
		
      if (parent.frames[1]) {
       var creditFrame = parent.frames[1];
       var availCredit = parseFloat((baseGetText(creditFrame.document.getElementById('.creditAvail'))).replace(/,/g,""));
       if (isNaN(previousCreditValue)) previousCreditValue = 0.00;

       if (creditField.value > availCredit) {
        alert('available credit to apply is only '+availCredit);
        creditField.value = availCredit + parseFloat(previousCreditValue);
       }

       creditFrame.DistributeCredits(index, creditField.value); 
      }
      return true;
     }

     function toggleColor(row) {

        if (currentRowIndex != null) {
         if (document.getElementById('rowIndex['+currentRowIndex+']')) {
          document.getElementById('rowIndex['+currentRowIndex+']').bgColor = "#DDDDDD";
         }
        }

      if (document.getElementById('rowIndex['+row+']')) {
       document.getElementById('rowIndex['+row+']').bgColor = "#ffffff";
      }
			   
      currentRowIndex = row;
     }

     function SetParentSplitSelections() {

      var myform = document.forms[1];
      var currentKey = (<?= $this->_itemOwnerKey ?>).toString();
      var splitSelectionMap = new Array();
      var openerHdl = parent.window.opener;

      splitSelectionMap[currentKey] = new Array();
      for (i = 0; i < rowKeyMap.length; i++) {
       var selectedAmt = parseFloat((myform.elements['_obj__ITEM_LIST_INFO('+i+')__PAIDAMOUNT'].value).replace(/,/g,""));
       if (isNaN(selectedAmt)) { selectedAmt = 0.00; }

       var currItemKey = rowKeyMap[i];
       splitSelectionMap[currentKey][currItemKey] = selectedAmt;
      }

      //Transferring values to parent
      if (parent.frames[1] && parent.frames[1].GetLineCreditKeys) {
       var lineCrKeyMap = parent.frames[1].GetLineCreditKeys();
       for (i = 0; i < lineCrKeyMap.length; i++) {
        var crItemKey = lineCrKeyMap[i].toString();

        openerHdl.document.forms[0].elements['.linecredits['+docEntity+']['+crItemKey+']'].value =
         baseGetText(parent.frames[1].document.getElementById('.linecredit['+crItemKey+']'));
       }
      }

      var creditInfoVar = null;

      if (parent.frames[1].GetCreditInfoVar) {
       creditInfoVar = parent.frames[1].GetCreditInfoVar();
      }

      openerHdl.SetSplitSelections(splitSelectionMap, creditInfoVar);

      return true;
     }

     function GetSplitSelections() {
      var myform = document.forms[1];
      var creditArr = parent.window.opener.GetCreditSplits(<?= $this->_itemOwnerKey ?>);

      total = 0;
      for (i = 0; i < rowKeyMap.length; i++) {
       var currItemKey = rowKeyMap[i];

       var selectedAmt = parseFloat((parentElementsHdl['.splits[<?= $this->_itemOwnerKey ?>]['+currItemKey+']'].value).replace(/,/g,""));
       if (isNaN(selectedAmt)) { selectedAmt = 0.00; }

       myform.elements['_obj__ITEM_LIST_INFO('+i+')__PAIDAMOUNT'].value = RoundCurrency(selectedAmt);

       if ( creditArr['<?= $this->_itemOwnerKey ?>'] ) {
        var rowCredits = creditArr['<?= $this->_itemOwnerKey ?>'][currItemKey];
        selCreditAmt = 0.00;
					
        for (var ir in rowCredits) {
         row = rowCredits[ir];
         for (var cr in row) {
          val = parseFloat(row[cr].replace(/,/g,""));
          selCreditAmt = selCreditAmt + val;
         }
        }

        myform.elements['_obj__ITEM_LIST_INFO('+i+')__CREDITS'].value = RoundCurrency(selCreditAmt);
       }
      }
      UpdateSelectionTotal();

      return true;
     }
		
     function FindItemRow(itemKey) {
      var row = -1;
      var rIndex = 0;
      while (row < 0 && rIndex < rowKeyMap.length) {
       if (rowKeyMap[rIndex] == itemKey) {
        row = rIndex;
       }
       rIndex++;
      }
      return row;
     }

     function UpdateCreditsApplied(invItemKey, prevAmt, currAmt) {
      var row = FindItemRow(invItemKey);
      if (row < 0) { return false; }

      if (document.forms[1]) {
       if (document.forms[1].elements['_obj__ITEM_LIST_INFO('+ row +')__CREDITS']) {
        var currTotalFld = document.forms[1].elements['_obj__ITEM_LIST_INFO('+ row +')__CREDITS'];
        var currTotaldueFld = document.forms[1].elements['_obj__ITEM_LIST_INFO('+ row +')__TOTALDUE'];
        var currTotalVal = (currTotalFld.value - prevAmt + currAmt);

        if ( currTotalVal > currTotaldueFld.value ) {
         alert('Line Item : ' + (row+1) + ' Credit amount applied is greater than amount due ' + currTotaldueFld.value);
         return false;
        }

        currTotalFld.value = Math.round(currTotalVal * 100) / 100;

        return true;
       }
      }

      return false;
     }

     function GetCreditsApplied(invItemKey) {
      var row = FindItemRow(invItemKey);
      if (row < 0) { return false; }

      if (document.forms[1]) {
       if (document.forms[1].elements['_obj__ITEM_LIST_INFO('+ row +')__CREDITS']) {
        var currTotalFld = document.forms[1].elements['_obj__ITEM_LIST_INFO('+ row +')__CREDITS'];
        return parseFloat((currTotalFld.value).replace(/,/g,""));
       }
      }

      return false;
     }

     function ResetDefaults() {
      var myform = document.forms[1]; <?
        $i = 0;
         foreach($this->_defaultAmtsMap as $defAmt) { ?>
         var obj = myform.elements['_obj__ITEM_LIST_INFO(<?= $i++ ?>)__PAIDAMOUNT'];
         obj.value = '<?= $defAmt ?>'; 
         RefreshMultilineTotal(obj,'Layer0','Layer0_form','_obj__ITEM_LIST_INFO','PAIDAMOUNT');<?
        } ?>
      UpdateSelectionTotal();

      return true;
     }

     function UpdateSelectionTotal() {
      var myform = document.forms[1];
      for (i = 0; i < rowKeyMap.length; i++) {
       var obj = myform.elements['_obj__ITEM_LIST_INFO('+i+')__PAIDAMOUNT'];
       RefreshMultilineTotal(obj,'Layer0','Layer0_form','_obj__ITEM_LIST_INFO','PAIDAMOUNT');
      }
     }

    </script>
    <?
    }


    /**
     * @param array  $_params
     * @param string $disable
     */
    function ShowDoButtons($_params, $disable = "")
    {
        if ( $_params['dobutton'] ) {
            ?><INPUT class="nosavehistory" type="button" name="savebutton" value="Done"
          onclick="javascript: if(BeforeSubmit()){ SetParentSplitSelections(); return parent.close(); }"  ><?
        }
    }


    /**
     * @param array $_params
     */
    function ShowCancelButtons($_params)
    {
        if ( $_params['cancelbutton'] ) {
            ?><INPUT class="nosavehistory" type="button" name=".cancel" value="<?=$_params['cancelbutton']?>"
        onclick="javascript: return parent.close()"  ><?
        }
    }


}




