<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
<TITLE> New Document </TITLE>
<META NAME="Generator" CONTENT="EditPlus">
<META NAME="Author" CONTENT="">
<META NAME="Keywords" CONTENT="">
<META NAME="Description" CONTENT="">
<script>
	function setSize() {
		var coOrdinates = new Object();
		coOrdinates.x = document.getElementById("x").value;
		coOrdinates.y = document.getElementById("y").value;
		window.returnValue = coOrdinates;
		window.close();
	}
</script>
</HEAD>

<BODY>
<label>X</label>
<input type=text value="" id="x" size="3"></input>
<label>&nbsp;Y</label>
<input type=text value="" id="y" size="3"></input>
<input type="button" value="set size" onclick="javascript:setSize()"></input>
<script>
	var coOrdinates = window.dialogArguments;
	document.getElementById("x").value = coOrdinates.x;
	document.getElementById("y").value = coOrdinates.y;
</script>
</BODY>
</HTML>
