<?

$kSchemas['clientpick'] = array (

    'schema' => array(
        'TITLE'        => 'title',
        'NAME'         => 'name',
        'DESCRIPTION'    => 'description',
        'PATH'    => 'path',
        'CLIENTLEVEL' => 'clientlevel',
        'ENTITYCNY#'    => 'entitycny#',
        'USERID'    => 'userid',
        'CLIENTENTITYKEY' => 'cliententitykey', 
        'CLIENTENTITYID' => 'cliententityid',
        'CLIENTENTITYNAME' => 'cliententityname',
    ),

    'object' => array(
        'TITLE',
        'NAME',
        'DESCRIPTION',
        'PATH',
        'CLIENTLEVEL',
        'ENTITYCNY#',
        'CLIENTENTITYKEY',
        'CLIENTENTITYID',
        'CLIENTENTITYNAME',
        'USERID',
    ),

    'fieldinfo' => array(

        array (
            'path'        => 'TITLE',
            'desc'        => 'IA.TITLE',
            'fullname'    => 'IA.COMPANY_ID',
            'required'    => true,
            'type' => array (
                'type'        => 'text',
                'ptype'        => 'text',
                'size'        => 20,
                'maxlength'    => 20,
            ),

        ),

        array (
            'path'        => 'NAME',
            'desc'        => 'IA.NAME',
            'fullname'    => 'IA.COMPANY_NAME',
            'required'    => true,
            'type' => array (
                'type'        => 'text',
                'ptype'        => 'text',
                'size'        => 20,
                'maxlength'    => 20,
            ),

        ),

        array (
            'path'        => 'DESCRIPTION',
            'desc'        => 'IA.DESCRIPTION',
            'fullname'    => 'IA.DESCRIPTION',
            'required'    => true,
            'type' => array (
                'type'        => 'text',
                'ptype'        => 'text',
                'size'        => 100,
                'maxlength'    => 100,
            )
        ),


        array (
            'path'        => 'ENTITYCNY#',
            'desc'        => 'IA.ENTITYCNY',
            'fullname'    => 'IA.ENTITYCNY',
            'required'    => true,
            'type' => array (
                'type'        => 'integer',
                'ptype'        => 'integer',
            ),

        ),

        array (
            'path'        => 'CLIENTENTITYID',
            'desc'        => 'IA.CLIENT_ENTITY_ID',
            'fullname'    => 'IA.CLIENT_ENTITY_ID',
            'required'    => false,
            'type' => array (
                'type'        => 'text',
                'ptype'        => 'text',
                'size'        => 20,
                'maxlength'    => 20,
            ),

        ),

        array (
            'path'        => 'CLIENTENTITYNAME',
            'desc'        => 'IA.NAME',
            'fullname'    => 'IA.CLIENT_ENTITY_NAME',
            'required'    => true,
            'type' => array (
                'type'        => 'text',
                'ptype'        => 'text',
                'size'        => 20,
                'maxlength'    => 20,
            ),

        ),

        array (
            'path'        => 'USERID',
            'desc'        => 'IA.USERID',
            'fullname'    => 'IA.USERID',
            'required'    => true,
            'type' => array (
                'type'        => 'integer',
                'ptype'        => 'integer',
            ),

        ),


    ),

    'table'      => 'v_clientpick_new',
    'module'     => 'mp',
    'vid'        => 'TITLE',
    'printas'    => 'IA.INSTANCE',
    'pluralprintas' => 'IA.INSTANCES',
    'renameable' => true,
);


