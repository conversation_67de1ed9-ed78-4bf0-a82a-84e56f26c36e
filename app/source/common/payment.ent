<?

/**
 *    FILE: payment.ent
 *    AUTHOR: <PERSON> & Paul <PERSON>
 *    DESCRIPTION: centralized payment meta
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */



// IF NO OTHER FILE HAS SUPPLIED THIS VALUE YET, WE WILL GIVE IT A DEFAULT
global $gPymtEntName;
if (empty($gPymtEntName)) { 
    $gPymtEntName = "Payment"; // the name of the business object we're describing
}


require 'prrecordhistory.ent';
$kSchemas['payment'] = array(
    'children' => array(
        'paymethod' => array(
            'fkey'    => 'paymethodkey',
            'invfkey' => 'record#',
            'table'   => 'paymethod',
            'join' => 'outer',
            'filter'  => " (providerpaymethodkey is null or paymethod.providerpaymentmethodkey = providerpaymethodkey) ",
            'rpdFilter'  => " (exists (select 1 from paymethod, prrecord
                                         where prrecord.paymethodkey = paymethod.record# 
                                               and (prrecord.providerpaymethodkey IS NULL 
                                                   or paymethod.providerpaymentmethodkey = prrecord.providerpaymethodkey)
                                      )
                               ) ",
            'global'  => true,
        ),
        'prbatch' => array(
                    'table' => 'prbatch', 
                    'fkey' => 'prbatchkey', 
                     'invkey' => 'record#', 
                    'join' => 'outer'
        ),
        'financialaccount' => array(
                    'table' => 'financialaccount',
                    'fkey' => 'financialentity',
                    'invkey' => 'entity',
                    'join' => 'outer'
        ),
        'providerpaymentmethod' => array(
            'fkey'     => 'providerpaymethodkey',
            'invfkey'  => 'record#',
            'join'     => 'outer',
            'table'    => 'providerpaymethod',
            'children' => array(
                'paymentprovider' => array(
                    'fkey'    => 'providerkey',
                    'invfkey' => 'record#',
                    'join'     => 'outer',
                    'table'   => 'paymentprovider',
                ),
            ),
        ),
        'providerpayment' => array(
            'fkey'    => 'record#',
            'invfkey' => 'paymentkey',
            'join'    => 'outer',
            'table'   => 'providerpayment',
        ),
    ),


    'dbfilters' => array (
        array ('payment.recordtype', 'IN', array(PRRECORD_TYPE_PAYMENT, PRRECORD_TYPE_REIMBURSEMENT))
    ),


    'object' => array (
        'RECORDNO',
        'ENTITY',
        'PAYMENTTYPE',
        'PAYMENTDATE',
        'RECEIPTDATE',
        'PAYMENTAMOUNT',
        'DOCUMENTNUMBER',
        'MEMO',
        'STATE',
        'STATUS',
        'FINANCIALACCOUNT',
        'RECORDTYPE',
        'CURRENCY',
        'PAYMENTTRXAMOUNT',
        'BASECURR',
        'PROVIDERPAYMENTMETHOD.PAYMENTPROVIDER.NAME'
    ),

    'schema' => array(
        'RECORDNO'         => 'record#',
        'ENTITY'           => 'entity',
        'PAYMENTTYPE'      => 'paymethod.name',
        'PAYMENTDATE'      => 'whenpaid',
        'RECEIPTDATE'      => 'whencreated',
        'PAYMENTAMOUNT'    => 'totalentered',
        'DOCUMENTNUMBER'   => 'docnumber',
        'MEMO'             => 'description',
        'STATE'            => 'state',
        'STATUS'           => 'status',
        'FINANCIALACCOUNT' => 'financialentity',
        'RECORDTYPE'       => 'recordtype',
        'CURRENCY'         => 'currency',
        'PAYMENTTRXAMOUNT' => 'trx_totalentered',
        'BATCHTITLE'       => 'prbatch.title',
        'POSTINGDATE'      => 'prbatch.created',
        'BASECURR'         => 'basecurr',
        'PROVIDERPAYMENTMETHOD' => array(
            'providerpaymentmethod.*' => 'providerpaymentmethod.*',
            'PAYMENTPROVIDER'     => [
                'paymentprovider.*' => 'paymentprovider.*',
            ],
        ),
        'PROVIDERPAYMENT' => array(
            'providerpayment.*' => 'providerpayment.*'
        ),
    ),


	'fieldinfo' => array(

		array (
			'path' => 'RECORDNO',
			'desc' => 'IA.RECORD_NUMBER',
			'fullname' => 'IA.RECORD_NUMBER',
			'hidden' => true,
			'type' => array (
				'type' => 'integer',
				'ptype' => 'sequence',
				'maxlength' => 8,
				'size' => 8,
				'format' => $gRecordNoFormat,
				),
			'id' => 201
		),
		$gStatusFieldInfo,
		array(
			'fullname' => 'IA.PAYMENT_STATUS',
			'type' => array( 
				'ptype' => 'enum', 
				'type' => 'text',
//  			'validlabels' => array('Submitted', 'Approved', 'Voided', 'Confirmed', 'Printed'),
//  			'validvalues' => array ('Submitted', 'Approved', 'Voided', 'Confirmed', 'Printed'), 
//  			'_validivalues' => array ('S', 'A', 'V', 'C', 'P'),
				'format' => '/.{0,100}/' 
			),
			'desc' => "IA.STATE_OF_PAYMENT",
			'path' => 'STATE',
			'id' => 202
		),
		array (
			'fullname' => 'IA.BATCH_TITLE',
			'required' => false,
			'type' => array ( 
				'ptype' => 'enum', 
				'type' => 'text',
			),
			'path' => 'BATCHTITLE',
			'id' => 204
		),
		array(
			'fullname' => 'IA.POSTING_DATE',
			'type' => $gDateType,
			'desc' => "",
			'path' => 'POSTINGDATE',
			'id' => 205
		),
		array(
			'fullname' => "",
			'type' => $gDateType,
			'desc' => "",
			'path' => 'PAYMENTDATE',
			'id' => 206
		),
		array(
			'fullname' => 'IA.RECEIPT_DATE',
			'type' => $gDateType,
			'desc' => "IA.RECEIPT_DATE",
			'path' => 'RECEIPTDATE',
			'id' => 207
		),
 		array(
			'fullname' => 'IA.PAYMENT_AMOUNT',
			'type' 		=> array (
				'ptype' => 'currency',
				'type' => 'decimal',
				'maxlength' => 8,
				'format' => $gCurrencyFormat
			),
			'desc' => "IA.PAYMENT_AMOUNT",
			'path' => 'PAYMENTAMOUNT',
			'id' => 208
		),
 		array(
			'fullname' => 'IA.TOTAL_APPLIED',
			'type' 		=> array (
				'ptype' => 'currency',
				'type' => 'decimal',
				'maxlength' => 21,
				'format' => $gDecimalFormat, 
			),
			'path' => 'TOTALPAID',
			'readonly' => true,
			'id' => 209
		),
		array(
			'fullname' => 'IA.DOCUMENT_CHECK_NO',
			'type' 		=> array (
				'ptype' => 'text',
				'type' => 'text',
				'maxlength' => 21,
				'format' => '/.{0,21}/' 
			),
			'desc' => 'IA.DOCUMENT_NUMBER',
			'path' => 'DOCUMENTNUMBER',
			'id' => 210
		),
 		array(
			'fullname' => 'IA.RECORD_TYPE',
			'type' 		=> array (
				'ptype' => 'text',
				'type' => 'text',
				'maxlength' => 21,
				'format' => '/.{0,21}/' 
			),
			'desc' => 'IA.DOCUMENT_NUMBER',
			'path' => 'RECORDTYPE',
			'id' => 211
		),
        array(
            'fullname' => 'IA.PAYMENT_STATUS',
            'type' => array( 
                'ptype' => 'enum', 
                'type' => 'text',
        //  			'validlabels' => array('Submitted', 'Approved', 'Voided', 'Confirmed', 'Printed'),
        //  			'validvalues' => array ('Submitted', 'Approved', 'Voided', 'Confirmed', 'Printed'), 
        //  			'_validivalues' => array ('S', 'A', 'V', 'C', 'P'),
                'format' => '/.{0,100}/' 
            ),
            'desc' => "IA.STATE_OF_PAYMENT",
            'path' => 'STATE',
            'id' => 202
        ),
        array (
            'fullname' => 'IA.PAYMENT_METHOD',
            'type' => array(
                'ptype'  => 'ptr',
                'type'   => 'ptr',
                'entity' => 'paymethod',
                'pickentity' => 'paymethod',
                'maxlength'     => 21,
            ),
            'transformFunction' => "translatePaymethodName", // this is used for custom report
            'required' => false,
            'default' => 'Printed Check',
            'desc' => "IA.PAYMENT_METHOD",
            'onchange' => 'SetNoCheckRequired();ChangePayType(this); ',
            'path' => 'PAYMENTTYPE',
            'id' => 203
        ),
        array (
            'fullname' => 'IA.BATCH_TITLE',
            'required' => false,
            'type' => array ( 
                'ptype' => 'enum', 
                'type' => 'text',
            ),
            'path' => 'BATCHTITLE',
            'id' => 204
        ),
        array(
            'fullname' => 'IA.POSTING_DATE',
            'type' => $gDateType,
            'desc' => "",
            'path' => 'POSTINGDATE',
            'id' => 205
        ),
        array(
            'fullname' => "",
            'type' => $gDateType,
            'desc' => "",
            'path' => 'PAYMENTDATE',
            'id' => 206
        ),
        array(
            'fullname' => 'IA.RECEIPT_DATE',
            'type' => $gDateType,
            'desc' => "IA.RECEIPT_DATE",
            'path' => 'RECEIPTDATE',
            'id' => 207
        ),
         array(
            'fullname' => 'IA.PAYMENT_AMOUNT',
            'type'         => array (
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 8,
                'format' => $gCurrencyFormat
            ),
            'desc' => "IA.PAYMENT_AMOUNT",
            'path' => 'PAYMENTAMOUNT',
            'id' => 208
         ),
         array(
            'fullname' => 'IA.TOTAL_APPLIED',
            'type'         => array (
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 21,
                'format' => $gDecimalFormat,
            ),
            'path' => 'TOTALPAID',
            'readonly' => true,
            'id' => 209
         ),
         array(
            'fullname' => 'IA.DOCUMENT_CHECK_NO',
            'type'         => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 21,
                'format' => '/.{0,21}/' 
            ),
            'desc' => 'IA.DOCUMENT_NUMBER',
            'path' => 'DOCUMENTNUMBER',
            'id' => 210
         ),
         array(
            'fullname' => 'IA.RECORD_TYPE',
            'type'         => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 21,
                'format' => '/.{0,21}/' 
            ),
            'desc' => 'IA.DOCUMENT_NUMBER',
            'path' => 'RECORDTYPE',
            'id' => 211
         ),
         array(
            'fullname' => 'IA.MEMO',
            'type'         => array (
                'ptype' => 'multitext',
                'type' => 'text',
                'maxlength' => 1000,
                'format' => $gDescription,
            ),
            'desc' => 'IA.MEMO',
            'path' => 'MEMO',
            'id' => 212
         ),
         array(
         'fullname' => 'IA.ACCOUNT',
         'type' => array( 
         'ptype' => 'enum',
         'type' => 'text',
         'validvalues' => array(),
         'validlabels' => array(),
         ),
         'desc' => 'IA.ACCOUNT',
         'path' => 'FINANCIALACCOUNT',
         'id' => 213
         ),
         array(
         'fullname' => 'IA.ATTACHMENT',
         'type'         => array (
         'ptype' => 'supdocptr',
         'type' => 'supdocptr',
                'width' => 695,
                'height' => 440,
         'maxlength' => 21,
         'entity' => 'supportingdocumentdata'
         ),
         'desc' => 'IA.ATTACHMENT',
         'path' => 'SUPDOCID',
         'entity' => 'supportingdocumentdata',
         'id' => 214
         ),
         array(
         'fullname' => 'IA.BASE_CURRENCY',
         'type' => array (
         'ptype' => 'text',
         'type' => 'text',
         'maxlength' => 3,
         ),
         'desc' => 'IA.BASE_CURRENCY',
         'path' => 'BASECURR',
         'id' => 215
         ),
         array(
         'fullname' => 'IA.PAYMENT_CURRENCY',
         'type' => array (
         'ptype' => 'text',
         'type' => 'text',
         'maxlength' => 3,
         ),
         'desc' => 'IA.CURRENCY',
         'path' => 'CURRENCY',
         'id' => 216
         ),
         array(
         'fullname' => 'IA.PAYMENT_TRANSACTION_AMOUNT',
         'type'         => array (
         'ptype' => 'currency',
         'type' => 'decimal',
         'maxlength' => 8,
         'format' => $gCurrencyFormat
         ),
         'desc' => "IA.PAYMENT_TRANSACTION_AMOUNT",
         'path' => 'PAYMENTTRXAMOUNT',
         'id' => 217,
         'platform' => false, // this is overwritten in sub entities
         ),
    ),

    'table' => 'prrecord',
    'lite_table' => 'prrecordlite',
    'vid' => 'RECORDNO',
    'nosysview' => true,
    
    'workflows' => array(
        'Printed Check' => 'printedcheck',
        'Credit Card' => 'appayment',
        'codeEFT' => 'appayment',
        'Online' => 'onlinepayment',
        'Cash' => 'appayment',
        'Manual Check' => 'quickpay',
        'EFT' => 'appayment',
        'ACH' => 'appayment',
        'Check Delivery' => 'appayment',
        'Amex ACH' => 'appayment',
        'Amex Charge Card' => 'appayment',
        'WF Check' => 'appayment',
        'WF Domestic ACH' => 'appayment',
        'WF USD Wire' => 'appayment',
        '' => 'appayment'        // DEFAULT
    )
);
require 'prrecord.ent';
$kSchemas['payment'] = EntityManager::inheritEnts($kSchemas['prrecord'], $kSchemas['payment']);
$kSchemas['payment']['atlasentityonly'] = true;
$kSchemas['payment']['nexus']['financialaccount'] = array('object' => 'financialaccount','relation' => ONE2MANY,'field' => 'financialentity');

