<?php
/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

$kSchemas['fifodispatcherhistory'] = [
    'object' => [
        'RECORDNO',
        FIFODispatcherHistoryManager::DISPATCHERQUEUEKEY,
        FIFODispatcherHistoryManager::CATEGORY,
        FIFODispatcherHistoryManager::DESCRIPTION,
        FIFODispatcherHistoryManager::DETAILS,
    ],
    'schema' => [
        'RECORDNO' => 'record#',
        FIFODispatcherHistoryManager::DISPATCHERQUEUEKEY => 'dispatcherqueuekey#',
        FIFODispatcherHistoryManager::CATEGORY => 'category',
        FIFODispatcherHistoryManager::DESCRIPTION => 'description',
        FIFODispatcherHistoryManager::DETAILS => 'details',
    ],
    'fieldinfo' => [
        [
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ],
            'hidden' => true,
            'readonly' => true,
        ],
        [
            'path' => FIFODispatcherHistoryManager::DISPATCHERQUEUEKEY ,
            'fullname' => 'IA.JOB_RECORD_NO',
            'readonly' => true,
            'hidden' => false,
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
                'format' => $gRecordNoFormat,
            ],
        ],
        [
            'path' => FIFODispatcherHistoryManager::CATEGORY,
            'fullname' => 'IA.JOB_CATEGORY',
            'readonly' => true,
            'hidden' => true,
            'type' => [
                'type' => 'char',
                'ptype' => 'char',
                'maxlength' => 1,
            ],
        ],
        [
            'path' => FIFODispatcherHistoryManager::DESCRIPTION,
            'fullname' => 'IA.JOB_DESCRIPTION',
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200,
            ],
        ],
        [
            'path' => FIFODispatcherHistoryManager::DETAILS,
            'fullname' => 'IA.JOB_DETAILS',
            'type' => [
                'ptype' => 'text',
                'type' => 'blob',
            ],
        ],
    ],
    'parententity' => 'fifodispatcherqueue', // overwrite this field in your entity
    'table' => 'fifodispatcherhistory',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'module' => 'co'
];
