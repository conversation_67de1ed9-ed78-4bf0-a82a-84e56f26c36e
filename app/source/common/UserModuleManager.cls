<?php

class UserModuleManager extends EntityManager
{
    /**
     * Disable auditing
     *
     * @return bool
     */
    public function IsAuditEnabled()
    {
        return false;
    }

    /**
     * DoEvent hook for usermodule
     *
     * @param string $verb   action on the entity (Set, Delete, Add, ...)
     * @param string $key    vid of the entity action is being perf ormed on
     * @param mixed  $param1 Parameter 1
     * @param mixed  $param2 Parameter 2
     * @param array  $values the object data
     *
     * @param bool   $fastUpdate
     *
     * @return true
     */
    public function DoEvent($verb, $key, $param1 = null, $param2 = null, $values = [], $fastUpdate = false)
    {
        // Disable user events
        return true;
    }
}
