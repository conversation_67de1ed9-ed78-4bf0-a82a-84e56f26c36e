<?php
/**
 * Email delivery configuration
 *
 * AUTHOR:            <PERSON>
 *
 * @copyright 2020, Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 *
 */

define("EMAIL_DELIVERY_CFG", "emaildeliveryconfiguration.cfg");

require_once 'util.inc';
require_once 'TimelapseLimiter.cls';
require_once 'LogManager.cls';

class EmailDeliveryConfiguration
{
    /** @var EmailDeliveryConfiguration $instance */
    private static $instance = null;

    /** @var array $timelapseLimiters of type TimelapseLimiter */
    private $timelapseLimiters = [];

    /** @var LogManager $logManager */
    private $logManager = null;

    /** @var int $currentCNY current company id */
    private $currentCNY = null;

    /**
     * @return EmailDeliveryConfiguration
     */
    public static function get() {
        if (self::$instance == null) {
            self::$instance = new self();
            self::$instance->initialize();
        }
        return self::$instance;
    }

    /**
     * @return array|null of TimelapseLimiter when configured
     */
    public function getTimelapseLimiters() {
        return $this->timelapseLimiters;
    }

    /**
     * Initialize this instance
     */
    private function initialize() {
        if (is_null($this->logManager)) {
            $this->logManager = LogManager::getInstance();
        }

        if (is_null($this->currentCNY)) {
            $this->currentCNY = GetMyCompany();
        }

        $filename = ia_cfg::getCfgDir() . EMAIL_DELIVERY_CFG;
        $configurationArray = file_exists($filename) ? parse_ini_file($filename, true) : false;
        if ($configurationArray) {
            $this->parseConfiguration($configurationArray);
        } else {
            $this->logManager->logErrorMessage(EMAIL_DELIVERY_CFG . " is missing or invalid.\n");
        }
    }

    /**
     * @param array $configData
     */
    private function parseConfiguration($configData)
    {
        if (isArrayValueProvided($configData, 'EMAIL_DELIVERY_TIMELAPSE_LIMITERS')) {
            $this->parseGeneralTimelapseLimiters($configData);
            $this->parseCurrentCompanyTimelapseLimiters($configData);
        }
    }

    /**
     * Retrieve general limiters from the configuration
     * @param array $configData
     */
    private function parseGeneralTimelapseLimiters($configData)
    {
        if (isArrayValueProvided($configData['EMAIL_DELIVERY_TIMELAPSE_LIMITERS'], 'GENERAL')) {
            $generalLimiters = json_decode($configData['EMAIL_DELIVERY_TIMELAPSE_LIMITERS']['GENERAL'], false);
            if (isNonEmptyArray($generalLimiters)) {
                $this->parseTimelapseLimiters($generalLimiters);
            } else {
                $this->logManager->logErrorMessage(
                    EMAIL_DELIVERY_CFG . " cannot decode GENERAL timelapse limiters.\n");
            }
        }
    }

    /**
     * Retrieve current company limiters from the configuration
     * @param array $configData
     */
    private function parseCurrentCompanyTimelapseLimiters($configData) {
        $currentCompanyKey = 'CNY'. $this->currentCNY;
        if (isArrayValueProvided($configData['EMAIL_DELIVERY_TIMELAPSE_LIMITERS'], $currentCompanyKey)) {
            $cnyLimiters = json_decode($configData['EMAIL_DELIVERY_TIMELAPSE_LIMITERS'][$currentCompanyKey], false);
            if (isNonEmptyArray($cnyLimiters)) {
                $this->parseTimelapseLimiters($cnyLimiters);
            } else {
                $this->logManager->logErrorMessage(
                    EMAIL_DELIVERY_CFG . " timelapse limiters for $currentCompanyKey cannot be decoded.\n");
            }
        }
    }

    /**
     * @param array $limitersArrayCfg
     */
    private function parseTimelapseLimiters($limitersArrayCfg)
    {
        foreach ($limitersArrayCfg as $limiterCfg) {
            if ($limiterCfg->trigger && $limiterCfg->triggerAction) {
                $limiter = new TimelapseLimiter($limiterCfg->trigger, $limiterCfg->triggerAction);
                if ($limiterCfg->timeIntervalSeconds) {
                    $limiter->setTimeIntervalSeconds((int)$limiterCfg->timeIntervalSeconds);
                }
                if ($limiterCfg->thresholdValue) {
                    $limiter->setThreshold((int)$limiterCfg->thresholdValue);
                }
                if ($limiterCfg->systemReaction) {
                    $limiter->setSystemReaction((string)$limiterCfg->systemReaction);
                }
                // check and ignore duplicates
                if (!array_key_exists((string)$limiter, $this->timelapseLimiters)) {
                    $this->timelapseLimiters[(string)$limiter] = $limiter;

                    if (!Globals::$g->islive && !strstr($_SERVER['SCRIPT_NAME'], 'phpunit')) {
                        $this->logManager->logGenMessage(
                            "EmailDeliveryConfiguration: using configured limiter $limiter\n",
                            LOG_FILE);
                    }
                } else {
                    $this->logManager->logErrorMessage(
                        EMAIL_DELIVERY_CFG . " duplicate timelapse limiter $limiter found.\n");
                }
            } else {
                $this->logManager->logErrorMessage(
                    EMAIL_DELIVERY_CFG . " timelapse limiter is missing trigger or triggerAction.\n");
            }
        }
    }
}
