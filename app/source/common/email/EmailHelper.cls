<?php

/**
 * EmailHelper
 *
 * The purpose of this class is to contain static functions which are used to contruct/gather data to send email via
 * the Email or LocalizedEmail classes.
 *
 * In the short term, this class will contain methods to construct email data and send them via one of the classes.
 * In the long term, usage of this class should be refactored, with the goal that this class is no longer needed.
 */

class EmailHelper
{
    /**
     * Construction not allowed
     */
    private function __construct()
    {
        throw new Error(__CLASS__ . " construction not allowed");
    }

    /**
     * Intended to be sent to internal recipients (Operations, Support, Engineers)
     *
     * Known recipients:
     * - <EMAIL>
     * - <EMAIL>
     * - Config: IA_ALERT_EMAIL
     * - <EMAIL>
     * - sandbox-eng-user-git-email
     *
     * NOTE:
     * - Currently only used by backend_util.inc and util.inc to send notifications for internal consumption
     * - Could be a candidate for removal after refactor
     *
     * @param string $from
     * @param array|string $to
     * @param string $subject
     * @param string $body
     *
     * @return bool
     */
    public static function sendInternal($from, $to, $subject, $body): bool // TODO: I18N_EMAIL: Check if localization need for internal use
    {
        if ($to == '' || $from == '' || $subject == '') {
            return false;
        }

        $email = new IAEmail($to);
        $email->_from = $from;
        $email->subject = $subject;
        $email->body = $body;
        return $email->send();
    }

    /**
     * Original global scope "ia_mail" function in IAEmail.cls (now static)
     *
     * NOTE:
     * - This function is used for internal communication and business communication to customers
     * - Old Comment: "HH - Ask John to fix this and check support for extra_headers!"
     *
     * @param array|string $to
     * @param string       $subject
     * @param string       $message
     * @param string       $headers
     * @param bool         $allowDeliveryLog
     *
     * @return bool
     */
    public static function ia_mail($to, $subject, $message, $headers = '', $allowDeliveryLog = true): bool
    {
        if (isl_strpos($to, ',')) {
            $to = explode(',', $to);
        }
        $email = new IAEmail($to);

        $ok = true;

        $matches = [];
        isl_preg_match('/From: +([^\r\n]+)/', $headers, $matches);
        if (is_array($matches) && $matches[1] != '') {
            $email->_from = $matches[1];
        } else {
            $ok = false;
        }

        $matches = [];
        isl_preg_match('/Reply-To: +([^\r\n]+)/', $headers, $matches);
        if (is_array($matches) && $matches[1] != '') {
            $email->_reply_to = $matches[1];
        } else {
            $ok = false;
        }

        if (!$ok) {
            logToFileError("Email From/To not found in headers for message to '$to' regarding subject '$subject' and message '$message'.\n\n");
            Globals::$g->gErr->addError("PL03000108", __FILE__ . ":" . __LINE__, "Internal error - missing From/Reply-To headers.", '', "Please contact Customer Support for assistance.");
            return false;
        }

        $email->subject = $subject;
        $email->body = $message;
        $email->setFlagEmailDeliveryLog($allowDeliveryLog);
        $email->send();

        return true;
    }
}