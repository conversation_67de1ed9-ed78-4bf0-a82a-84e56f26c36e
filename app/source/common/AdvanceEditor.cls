<?

/** 
 *  FILE:         AdvanceEditor.cls
 *  AUTHOR:       <PERSON>
 *  (C) 2001, Intacct Corporation, All Rights Reserved                
 *
 *  This document contains trade secret data that belongs to Intacct       
 *  Corporation and is protected by the copyright laws.  Information 
 *  herein may not be used, copied or disclosed in whole or in part        
 *  without prior written consent from Intacct Corporation.          
 */



import('PaymentEditor');
import('PREntryEditor');



class AdvanceEditor extends PaymentEditor
{
    /**
     * @var int $numofrows
     */
    var $numofrows;

    /**
     * @param array $_params
     */
    function __construct($_params)
    {
        $this->additionalTokens[] = 'IA.EXPENSE_TYPE';
        $this->additionalTokens[] = 'IA.ACCOUNT_LABEL';
        $this->additionalTokens[] = 'IA.EMPLOYEE_ID';
        $this->additionalTokens[] = 'IA.ADVANCE_PAYMENT';
        parent::__construct($_params);
        $this->mcpEnabled = IsMCPEnabled();
    }


    function ShowTop() 
    { 
        // Advances for Cash Basis companies not supported yet.
        CompanyType($accrual);
        if ( !IsOperatingMultipleBooks() && !$accrual ) {
            import('UIUtils');

            $title = " Advance Payments ";

            Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000');
            echo('<CENTER><FORM name="main" method="post">');
            $this->ShowHiddenFields();
            echo('</FORM>');
            htmlHeaderBar(" $title");

            Globals::$g->gUIUtils->ShowFeatureComingSoon($title, "for Cash Basis companies");
            
            htmlButtonFooter($title);
            htmlFooter($title);
            exit(1);
        }
        else {
            parent::ShowTop();
        }
    }

    /**
     * @return string
     */
    function GetDefaultPayType()
    {
        $default = "";

        foreach($this->_params['layout']['pages'][0]['fields'] as $field) {
            if ($field['path'] == 'PAYMENTTYPE') {
                $default = $this->_defaultPayMethod ?: $field['default'];
            }
        }
        return $default;
    }

    /**
     * @return AdvanceManager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof AdvanceManager);
        return $this->entityMgr;
    }

    /**
     * @param array $_params
     */
    function SetDefaultOffsetForDisplay(&$_params)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $defOffsetDisplay = "";
        $defAcct = $this->getEntityMgr()->GetDefaultOffset();

        if (!empty($defAcct)) {
            $mod = Request::$r->_mod;
            $isLabel = GetLabelStatus($mod);
            if ($isLabel) {
                global $kAPid, $kARid, $kEEid;
                $myModuleKey = $this->getEntityMgr()->_moduleKey;
                $modPrefixMap = array($kAPid => 'ap', $kARid => 'ar', $kEEid => 'ee');

                $labelMgrName = $modPrefixMap[$myModuleKey] . 'accountlabel';
                $acLabelMgr = Globals::$g->gManagerFactory->getManager($labelMgrName);
                $acLabels = $acLabelMgr->GetList();

                foreach ($acLabels as $label) {
                    if ($label['GLACCOUNTNO'] == $defAcct) {
                        $_params['defaultdisplaylabel'] = $label['ACCOUNTLABEL'];
                    }
                }
            } else {
                $_params['defaultdisplayacct'] = $defAcct;
                $glAcctMap = GetGLAccountMap("status='T'");
                if ($glAcctMap[$defAcct]) {
                    $_params['defaultdisplayacct'] .= (PICK_RECVAL_SEP . $glAcctMap[$defAcct]);
                }
            }
        }
    }

    /**
     * @param array $_params
     *
     * @return bool
     */

    function ProcessCreateAction(&$_params)
    {
        $_params['entityDesc'] = GT($this->textMap, 'IA.ADVANCE_PAYMENT');
        parent::ProcessCreateAction($_params);
        if ($this->state != 'error' && $this->state != 'createwarning' ) {
            $obj =& Request::$r->GetCurrentObject();
            if ($this->mcpEnabled) {
                $exchMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
                $rtype = $exchMgr->GetDefaultExchangeRateType();
                $nobj['EXCHRATEDATE'] = GetCurrentDate();
                $nobj['EXCHRATETYPE'] = $rtype[0]['NAME'];
            }
            $nobj['PAYMENTDATE'] = $obj['PAYMENTDATE'];
            $nobj['PAYMENTTYPE'] = $obj['PAYMENTTYPE'];
            $nobj['FINANCIALACCOUNT'] = $obj['FINANCIALACCOUNT'];

            Request::$r->SetCurrentObject($nobj);
        }

        return false;
    }



    function ShowHiddenFields() 
    { 
        $lastPayMethod = Request::$r->_lastpaymethod;

        if (empty($lastPayMethod)) { 
            $lastPayMethod = $this->GetDefaultPayType(); 
        }

        ?><INPUT type="hidden" name='.lastpaymethod' value="<?= util_encode($lastPayMethod); ?>"><?

        Editor::ShowHiddenFields();
    }

    /**
     * @param array $_params
     *
     * @return array
     */
    function MultilineLayout_Instantiate($_params)
    {
        $ret = PaymentEditor::MultilineLayout_Instantiate($_params);

        if ($this->state == Editor_InitState || $this->state == Editor_ShowNewState) {
            $this->SetDefaultOffsetForDisplay($ret);
        }
        $this->_prentryEditor->MultilineLayout_InstantiateByProxy($ret);

        return $ret;
    }

    /**
     * @param array $_params
     */
    function Editor_Instantiate(&$_params)
    {
        parent::Editor_Instantiate($_params);
        // Commenting code Bug#12460
        /*
        $paymentType = $this->GetPaymentType();
        if ($paymentType == 'Online') {
        global $gErr;
        if (!IsModuleConfigured('26.OBP')) {
        $gErr->AddError("BL03000145",__FILE__ . __LINE__,
        "Please enroll for Intacct's Online Bill Payment service to proceed.");	
        require_once('popuperror.phtml');
        exit();
        }
        }
        */
        $itemHiddenFields = $this->_prentryEditor->GetFieldsToRemove();
        if (count($itemHiddenFields)) {
            $this->HideFieldsByPath($_params, $itemHiddenFields);
        }

        $_mod = Request::$r->_mod;
        $isLabel = GetLabelStatus($_mod);
        $findfields = array(
        // array('path' => 'PAYMENTTYPE'),
        array('path' => 'ENTRYDESCRIPTION'),
        );
        if ($isLabel) {
            $findfields[] = array('path' => 'ACCOUNTLABELKEY');
        } else {
            $findfields[] = array('path' => 'GLACCOUNT');
        }
        foreach ($findfields as $ff) {
            $_found = array();
            $this->MatchTemplates($_params, $ff, $_found);
            if ($_found) {
                /*
                if ($ff['path'] == 'PAYMENTTYPE') {
                foreach ($_found as $key => $found) {
                $_found[$key]['type']['validlabels'] = array('Check', 'Charge Card', 'Record Transfer', 'Cash');
                $_found[$key]['type']['validvalues'] = array('Printed Check', 'Credit Card', 'EFT', 'Cash');
                $_found[$key]['type']['_validvalues'] = array('Printed Check', 'Credit Card', 'EFT', 'Cash');
                }
                continue;
                }
                */
                if ($ff['path'] == 'GLACCOUNT' || $ff['path'] == 'ACCOUNTLABELKEY') {
                    foreach ( $_found as $key => $found) {
                        $_found[$key]['type']['size'] = 30;
                    }
                    continue;
                }
                if ($ff['path'] == 'ENTRYDESCRIPTION') {
                    foreach ( $_found as $key => $found) {
                        $_found[$key]['type']['size'] = 30;
                        $_found[$key]['numofcols'] = 30;
                    }
                    continue;
                }
            }
        }
    }

    /**
     * @param array $_params
     *
     * @return array
     */
    function MergeLayout($_params)
    {

        $_mod = Request::$r->_mod;
        $isLabel = GetLabelStatus($_mod);
        $editing = ($this->state == Editor_ShowEditState);

        $findfields = array();
        if ($isLabel) {
            $findfields[] = array('path' => 'GLACCOUNT');
        }

        if ($editing) {
            $findfields[] = array('path' => 'VENDORID');
            $findfields[] = array('path' => 'PAYMENTTYPE');
        }

        foreach ($findfields as $ff) {
            $_found = array();
            $this->MatchTemplates($_params, $ff, $_found);
            if ($_found) {
                if ($ff['path'] == 'GLACCOUNT') {
                    foreach ( $_found as $key => $found) {
                        $_found[$key]['path'] = 'ACCOUNTLABELKEY';
                        $_found[$key]['fullname'] = ($_mod == 'ee' ? GT($this->textMap, 'IA.EXPENSE_TYPE') : GT($this->textMap, 'IA.ACCOUNT_LABEL'));
                    }
                    continue;
                }
                if ($ff['path'] == 'VENDORID' || $ff['path'] == 'PAYMENTTYPE') {
                    $_found[0]['readonly'] = true;
                    $_found[0]['required'] = false;
                    continue;
                }
            }
        }

        // For now these are the same. Only subclasses should be calling this one.
        PREntryEditor::Editor_ExpandByProxy($_params);

        return parent::MergeLayout($_params);
    }

    /**
     * @param array $_params
     */
    function MergeOwnerDimensionFields(&$_params)
    {
        $this->calcOwnerDimensionFields($_params);
    }


    /**
     * @param array $_params
     *
     * @return array
     */
    function Buttons_Instantiate($_params)
    {
        $_params = Editor::Buttons_Instantiate($_params);

        unset($_params['deliverbutton']);
        unset($_params['deliveraction']);

        if(    isset($_params['saveandnewbutton']) &&     $_params['saveandnewbutton'] != '') {
            $_params['saveandnewbutton'] = GT($this->textMap, 'IA.SAVE');;
            unset($_params['dobutton']);
            unset($_params['doaction']);
        }
        return $_params;
    }


    /**
     * @return array
     */
    function getHotKeyButtonIDs()
    {
        $cmdButtonIDMap = parent::getHotKeyButtonIDs();
        if (count($cmdButtonIDMap)>0) {            
            if ( $this->_params['buttons']['saveandnewbutton'] ) {
                // Using 'Save And New' button ID for 'Save'
                if ( $cmdButtonIDMap['SAVE'] ) {
                    $cmdButtonIDMap['SAVE'] = Editor_SaveAndNewBtnID;
                }
            }
        }

        return $cmdButtonIDMap;
    }


    /*
    function ShowSimpleFieldLabel(&$_field) {
    $editing = ($this->state == Editor_ShowEditState);

    $viewOnlyOnEditFlds = array('VENDORID', 'PAYMENTTYPE');
    if ($editing && (in_array($_field['path'], $viewOnlyOnEditFlds))) {
    $_field['readonly'] = true;
    $_field['required'] = false;
    }
    PaymentEditor::ShowSimpleFieldLabel($_field);
    }
    */


    /**
     * @param array $_layout
     *
     * @return int
     */
    function FigureOutNumOfRows($_layout)
    {
        /** @noinspection PhpUndefinedVariableInspection */
        $_layout['numofrows'] = $this->_prentryEditor->GetNumOfLineitemRows($default);
        $this->numofrows = Editor::FigureOutNumOfRows($_layout);
        return $this->numofrows;
    }


    /**
     * Add Javascript and CSS to the page.  Subclasses really should call back to their parent.
     *
     * @param bool $addYuiCss
     * @bool $addYuiCss  include the YUI css files
     */
    function showScripts($addYuiCss = true)
    {
        parent::showScripts($addYuiCss);
        //$this->_prentryEditor->ShowScriptsByProxy();


        if ( !util_isPlatformDisabled() ) {
            ?>
<script>
    function pt_editor_getUIFieldId( serverFieldId )
    {
        if ( serverFieldId == 'ACCOUNTNO' ) {
            return 'GLACCOUNT';
        }

        return serverFieldId;
    }


    function pt_editor_getServerFieldName( srcFieldName )
    {
        if ( srcFieldName == 'GLACCOUNT' ) {
            return 'ACCOUNTNO';
        }

        return srcFieldName;
    }
</script>
            <?
        }
    }


    /**
     * @param array $_params
     * @param array $values
     *
     * @return bool
     */
    function PrepareInputValues(&$_params, &$values)
    {

        $ok = parent::PrepareInputValues($_params, $values);

        $entityIdKey = isl_strtoupper($this->getEntityMgr()->_pymt_entity) . 'ID';

        list($values[$entityIdKey]) = explode(PICK_RECVAL_SEP, $values[$entityIdKey]);
        $values['DOCUMENTNUMBER'] = isl_substr($values['DOCUMENTNUMBER'], 0, 80);
        $_pymtAmtSplits    = &$values['PRENTRY'];
        foreach($_pymtAmtSplits as $pymtIndex => $payItem) {
            $_pymtAmtSplits[$pymtIndex]['AMOUNT'] = str_replace(",", "", isl_trim($payItem['AMOUNT']));
            if ($_pymtAmtSplits[$pymtIndex]['AMOUNT'] == 0) {
                unset($_pymtAmtSplits[$pymtIndex]);
                continue;
            }
            list($_pymtAmtSplits[$pymtIndex]['GLACCOUNT'])  = explode(PICK_RECVAL_SEP, isl_trim($payItem['GLACCOUNT']));
            list($_pymtAmtSplits[$pymtIndex]['DEPARTMENT']) = explode(PICK_RECVAL_SEP, isl_trim($payItem['DEPARTMENT']));
            list($_pymtAmtSplits[$pymtIndex]['LOCATION'])   = explode(PICK_RECVAL_SEP, isl_trim($payItem['LOCATION']));
        }

        return $ok;
    }


    /**
     * @param array $_params
     * @param string $entobjname
     */
    function SetMCPUpdates(&$_params, $entobjname)
    {
        
        if ($this->mcpEnabled) {
            $_sess = Session::getKey();

            if (!$this->atlas || ($this->atlas && GetContextLocation())) {
                $basecurr = GetBaseCurrency();
            }
            
            $qop = GetOperationId('co/lists/trxcurrencies/view');
            $_found0 = array();
            // add QRequest for defaulting vendor currency
            $this->MatchTemplates($_params, array('path' => $entobjname), $_found0);
            foreach ( $_found0 as $v => $acct_fld) {
                /** @noinspection PhpUndefinedVariableInspection */
                $_found0[$v]['onchange'] .= "UpdateDefaultCurrency('$_sess', $qop, '$basecurr');";
            }

            // add QRequest for getting exchange rate
            $find = array (
                array('path' => 'CURRENCY'),
                array('path' => 'EXCHRATEDATE'),
                array('path' => 'EXCHRATETYPE'),
            );
            // At Atlas root, append the Base currency
            if(IsMCMESubscribed() && !GetContextLocation()) {
                $find = INTACCTarray_merge($find, array(array('path' =>'BASECURR')));
            }


            foreach ($find as $f) {
                $_found = array();
                $this->MatchTemplates($_params, $f, $_found);
                if($_found) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $_found[0]['onchange'] .= "UpdateExchangeRate('$_sess', $qop, '$basecurr', '');";
                }
            }
        }
    }


    /**
     * @param array $_params
     */
    function ProcessEditNewAction(&$_params)
    {
        parent::ProcessEditNewAction($_params);

        //To store default value only at new mode not at the time of error retrival
        $errorRecoveryTime = Request::$r->{'_errorTimeStamp'};

        if (!isset($errorRecoveryTime) || $errorRecoveryTime=='') {
            $obj =& Request::$r->GetCurrentObject();
            if ($this->mcpEnabled) {
                $exchMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
                $rtype = $exchMgr->GetDefaultExchangeRateType();
                $obj['EXCHRATEDATE'] = GetCurrentDate();
                $obj['EXCHRATETYPE'] = $rtype[0]['NAME'];
            }
            Request::$r->SetCurrentObject($obj);
        }
    }



    /**
     * Sets platform based auto-fill for glaccount
     *
     * @param string $entity
     * @param array $multilineColumn
     */
    function MergeOwnedObjectDimensions($entity, &$multilineColumn)
    {
        parent::MergeOwnedObjectDimensions($entity, $multilineColumn);

        if ( !util_isPlatformDisabled() ) {

            if ( $multilineColumn[0]['path'] == 'GLACCOUNT' ) {

                if ( Pt_StandardUtil::autoFillRelated('glaccount') ) {
                    $multilineColumn[0]['onchange'] .= 'pt_editor_defaultRelated(this, null);';
                }
            }
        }
    }
}
