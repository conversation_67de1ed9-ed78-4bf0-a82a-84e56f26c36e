<?php
/**
 *    FILE:
 *    AUTHOR:            <PERSON>
 *    DESCRIPTION:
 *
 *    (C)2000, Intacct Corporation, All Rights Reserved
 *
 *    Intacct Corporation Proprietary Information.
 *    This document contains trade secret data that belongs to Intacct
 *    corporation and is protected by the copyright laws. Information herein
 *    may not be used, copied or disclosed in whole or part without prior
 *    written consent from Intacct Corporation.
 *
 */

class MailaddressManager extends EntityManager
{
    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "MailAddressManager::Add";
        global $gErr;

        $ok = $this->_QM->beginTrx($source);
        $ok = $ok && $this->HandleCountryCode($values);

        $nextId = $this->GetNextRecordKey();
        $ok = $ok && isset($nextId);
        $values[':record#'] = $nextId;

        $ok = $ok && parent::regularAdd($values);

        $values['RECORDKEY'] = $nextId;

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = "Could not create MailAddress record!";
            $gErr->addIAError('CO-1094', GetFL(), $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $ok = $this->HandleCountryCode($values);
        $ok = $ok && parent::regularSet($values);
        return $ok;
    }


    /**
     * @param array $values
     *
     * @return bool
     */
    function HandleCountryCode(&$values)
    {
        global $gErr;

        // this is coming from gateway
        if ($values['ISOCOUNTRYCODE'] != '') {
            $values['COUNTRYCODE'] = $values['ISOCOUNTRYCODE'];
        }

        // Rule
        if ($values['COUNTRY'] && isl_strlen($values['COUNTRY']) > 60) {
            $gErr->addError('**********', GetFL(), "Invalid length for country string " . $values['COUNTRY']);
            return false;
        }

        // Rule - COUNTRYCODE field has valid values defined so this validation is useless
        if ($values['COUNTRYCODE'] && isl_strlen($values['COUNTRYCODE']) != 2) {
            $gErr->addIAFieldError('MAILADDRESS.COUNTRYCODE', 'CONTACT', 'CO-1093', GetFL(),
                                   "Invalid country code format " . $values['COUNTRYCODE'],
                                   [ 'COUNTRY_CODE' => $values['COUNTRYCODE'] ]);
            return false;
        }
        
        $isoused = IsISOCountryEnabled();
        $isImporting = false; // use countryGetImportingCSVFile() if we always want to not require a CC;
        $cname = '';
        $ccode = '';
        $ctempcode = '';
        $ctempname = '';
        
        if ($values['COUNTRY']) {
            $ctempname = $values['COUNTRY'];
            $ccode = GetCountryCode($values['COUNTRY']);
            if ($ccode === '') {
                $countryNameVariant = searchCountryNameVariant($values['COUNTRY']);
                $ccode = GetCountryCode($countryNameVariant);
            }
        }

        if ($isoused) {
            if ($values['COUNTRY'] && $values['COUNTRY'] !== '') {
                $values['COUNTRYCODE'] = GetCountryCode($values['COUNTRY']);
                if ($values['COUNTRYCODE']  === '') {
                    $countryNameVariant = searchCountryNameVariant($values['COUNTRY']);
                    $values['COUNTRYCODE'] = GetCountryCode($countryNameVariant);
                }
            } elseif ($values['COUNTRYCODE'] === null || empty($values['COUNTRYCODE'])) {
                $values['COUNTRYCODE'] = getAddressCountryDefault(GetMyCompany())['countryCode'];
            }

            if ($values['COUNTRYCODE']) {
                $ctempcode = $values['COUNTRYCODE'];
                $cname = GetCountryName($ctempcode);
                $ctempcode = $values['COUNTRYCODE']; //restore the value
            } elseif ($isImporting) {
                $cname = '';
                $ccode = '';
                $ctempcode = '';
            }
        }

        $hasCityStateOrZip = $values['CITY'] || $values['STATE'] || $values['ZIP'];

        if ($isoused) {
            // we allow blank country when importing.
            /** @noinspection PhpUndefinedVariableInspection */
            if ( !$isImporting && $values['COUNTRYCODE'] && ! $values['COUNTRY'] && $cname == '') {
                $gErr->addError('**********', GetFL(), "Invalid country code " . $values['COUNTRYCODE']);
                return false;
            } /** @noinspection PhpUndefinedVariableInspection */ elseif ( ! $values['COUNTRYCODE'] && $values['COUNTRY'] && $ccode == '') {
                $gErr->addError('**********', GetFL(), "Cannot figure out country code from country " . $values['COUNTRY']);
                return false;
            } /** @noinspection PhpUndefinedVariableInspection */ elseif ( $ccode != '' && $ctempcode != '' && $ccode != $ctempcode) {
                $gErr->addError('**********', GetFL(), "Country and country code mismatch");
                return false;
            } /** @noinspection PhpUndefinedVariableInspection */ elseif ( $hasCityStateOrZip && $ccode == '' && $cname == '') {
                $gErr->addError('**********', GetFL(), "Invalid Country and country code");
                return false;
            }
            /** @noinspection PhpUndefinedVariableInspection */
            $values['COUNTRYCODE'] = ($ctempcode ?: $ccode);
            /** @noinspection PhpUndefinedVariableInspection */
            $values['COUNTRY'] = ($cname ?: $ctempname);
    
            if (isset($values['STATE']) && $values['STATE'] !== '') {
                if (isset(RegionEditor::COUNTRIES_WITH_REGIONS[$values['COUNTRYCODE']])) {
                    $regions = RegionEditor::getRegionsOfCountries()[$values['COUNTRYCODE']];
                    // If the state does not come as an iso code, get the iso code
                    if (!isset($regions[$values['STATE']])) {
                        $localizedRegionsNames = getLocalizedText(I18N::tokenArrayToObjectArray(array_values($regions)), $errMsg);
                        if ($errMsg !== '') {
                            logToFileError("Tokens could not be loaded from text service. Error message: " . $errMsg);
                        } else {
                            $stateToken = array_search($values['STATE'], $localizedRegionsNames);
                            if ($stateToken !== false) {
                                $stateCode = array_search($stateToken, $regions);
                                $values['STATE'] = $stateCode;
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * @param string $addr
     *
     * @return string
     */
    public static function GetTrimAddress($addr)
    {

        if (isl_strlen($addr) > 80) {
            $addr = isl_substr($addr, 0, 80) . '...';
        }

        return $addr;
    }

    /**
     * @param string      $verb
     * @param string      $key
     * @param string|null $param1
     * @param bool|null   $param2
     * @param array       $values
     *
     * @param bool        $fastUpdate
     *
     * @return bool
     */
    function DoEvent($verb, $key, $param1 = null, $param2 = null, $values = [], $fastUpdate = false)
    {
        return true;
    }

}
