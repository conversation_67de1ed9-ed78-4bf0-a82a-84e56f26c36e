<xsl:stylesheet	version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
<xsl:import	href="../../private/xslinc/to_html.xsl"/>
<!--overridden from	Base template. Implimentation provided-->
<xsl:template name="reportFooter">
	<tr>
		<td>
		 <xsl:call-template	name="break">	 <xsl:with-param name="text" select="/report/footer" />	 </xsl:call-template>
		</td>
	</tr>		
		<xsl:if	test="(/report/@report_date!='')">
			<tr>
				<td>Created	on:	<xsl:value-of select="/report/@report_date"/>
				<xsl:text disable-output-escaping="yes">&amp;nbsp;</xsl:text>
				<xsl:value-of select="/report/@report_time"/>
				</td>
			</tr>
		</xsl:if>
    <xsl:if test="(/report/@rptAcctSet!='')">
        <table>
        <tr><td></td></tr>
        <tr><td><xsl:value-of select="/report/@rptAcctFooterText"/></td></tr>
        </table>
    </xsl:if>
</xsl:template>
<!--overridden from	Base template removed images JPC - added border for XSL test-->
<xsl:template name="underline">
  <xsl:param name="size"/>
  <xsl:choose>
    <xsl:when test="($use_spacer = 1)"></xsl:when>
	<xsl:otherwise>
      <xsl:choose>
		<xsl:when test="($size &gt; 1)">
            <xsl:attribute name="style">border-bottom-style:double</xsl:attribute>
		</xsl:when>
	    <xsl:otherwise>
          <xsl:attribute name="style">border-bottom-style:solid</xsl:attribute>
	    </xsl:otherwise>
      </xsl:choose>
    </xsl:otherwise>
  </xsl:choose>
</xsl:template>

<!--overridden from	Base template removed images -->
<xsl:template name="levelImage">
   <xsl:param name="lvl" select="0"/>
  <xsl:text	disable-output-escaping="yes">&amp;nbsp;</xsl:text>
</xsl:template>

<!--overridden from	Base template removed images -->
<xsl:template name="memoImage">
	  <xsl:text	disable-output-escaping="yes">&amp;nbsp;</xsl:text>
</xsl:template>
<!-- This Template is overridden from the base just	removed	image-->
<xsl:template name="spacer">		
	<xsl:param name="class"/>

	<td>
		<xsl:if	test="($class != '')">
			<xsl:attribute name="class"><xsl:value-of select="$class"/></xsl:attribute>
		</xsl:if>				
		
	</td>	
</xsl:template>

<!-- replace all \n	with <br />'s  -->
<xsl:template name="break">
   <xsl:param name="text" select="."/>
   <xsl:choose>
   <xsl:when test="contains($text, '&#xa;')">
	  <xsl:value-of	select="substring-before($text,	'&#xa;')"/>
	  <br/>
	  <xsl:call-template name="break">
		  <xsl:with-param name="text" select="substring-after($text,'&#xa;')"/>
	  </xsl:call-template>
   </xsl:when>
   <xsl:otherwise>
	<xsl:value-of select="$text"/>
   </xsl:otherwise>
   </xsl:choose>
</xsl:template>

<!-- String	left Pad with n(count) spaces  -->
<xsl:template name="padSpaces">
  <xsl:param name="count" select="1"/>
  <xsl:if test="$count > 0">
	<xsl:text disable-output-escaping="yes">&amp;nbsp;</xsl:text>
	<xsl:call-template name="padSpaces">
		<xsl:with-param	name="count" select="$count	- 1"/>
	</xsl:call-template>
  </xsl:if>
</xsl:template>

<xsl:template name="reporttitlesection">
    <xsl:if test="(/report/company != '')">
        <tr>
            <td class="DGB">
                <xsl:value-of select="/report/company"/>
            </td>
        </tr>
    </xsl:if>
    <xsl:if test="(/report/@reportname != '')">
        <tr>
            <td class="DGB">
                <xsl:value-of select="/report/@reportname"/>
            </td>
        </tr>
    </xsl:if>
    <xsl:for-each select="title">
        <xsl:variable name="rtitle">
            <xsl:value-of select="."/>
        </xsl:variable>
        <xsl:if test="number(position()) &gt; 1 and number(position()) &lt; 4 and $rtitle != ''">
            <tr>
                <td class="DGB">
                    <xsl:value-of select="$rtitle"/>
                </td>
            </tr>
        </xsl:if>
    </xsl:for-each>
    <xsl:if test="(/report/@reportingbookforexcel != '')">
        <tr>
            <td class="DGB">
                Reporting Book: 
            </td>
            <td class="DGB">
                <xsl:value-of select="/report/@reportingbookforexcel"/>
            </td>
        </tr>
    </xsl:if>
    <xsl:if test="(/report/@asofforexcel != '')">
        <tr>
            <td class="DGB">
                As of Date: 
            </td>
            <td class="DGB" style="mso-number-format:'\@'">
                <xsl:value-of select="/report/@asofforexcel"/>
            </td>
        </tr>
    </xsl:if>
    <xsl:if test="(/report/@rptAccountSetID != '')">
        <tr>
            <td class="DGB">
                Reporting account set: 
            </td>
            <td class="DGB" >
                <xsl:value-of select="/report/@rptAccountSetID"/>
            </td>
        </tr>
    </xsl:if>    
    <xsl:if test="(/report/@deptfull != '') and ($individualreport = 'N' and count(//custtitle) = 0)">
        <tr>
            <td class="DGB">
                <xsl:value-of select="/report/@deptlabel"/>: 
            </td>
            <td class="DGB">
                <xsl:value-of select="/report/@deptfull"/>
            </td>
        </tr>
    </xsl:if>
    <xsl:if test="(/report/@locationfull != '') and ($individualreport = 'N' and count(//custtitle) = 0)">
        <tr>
            <td class="DGB">
                <xsl:value-of select="/report/@loclabel"/>: 
            </td>
            <td class="DGB">
                <xsl:value-of select="/report/@locationfull"/>
            </td>
        </tr>
    </xsl:if>
    <xsl:call-template name="dimensions"/>
    <tr><td/><td/></tr>
</xsl:template>

<xsl:template name="dimensions">
    <xsl:for-each select="/report/rtdim">
            <xsl:if test="(./*/@value) != '' or (./*/@rfvalue) != ''">
            <tr>
                <xsl:if test="(./*/@value) != ''">
                    <td class="DGB">
                        <xsl:value-of select="(./*/@renameddimension)"/>: 
                    </td>
                    <td class="DGB">
                        <b><xsl:value-of select="(./*/@value)"/></b>
                    </td>
                </xsl:if>
                <xsl:if test="(./*/@rfvalue != '')">
                    <td class="DGB">
                        <xsl:value-of select="(./*/@rflabel)"/>: 
                    </td>
                    <td class="DGB">
                        <xsl:value-of select="(./*/@rfvalue)"/>
                    </td>
                </xsl:if>
            </tr>
            </xsl:if>
    </xsl:for-each>
</xsl:template>

</xsl:stylesheet>
