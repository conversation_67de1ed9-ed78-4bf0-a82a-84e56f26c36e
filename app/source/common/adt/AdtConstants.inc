<?php
/**
 * Contains constants and enumerations supporting custom abstract data types
 * salesforce modules.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Class AbstractDataType
 *
 * Simulates enumeration with the supported abstract data types
 */
abstract class AbstractDataType
{
    const LARGE_LIST = 'large_list';
    const VOLATILE_DATA = 'volatile_data';
    const TELE_VENDOR = 'tele_vendor';
}

/**
 * Class StorageType
 *
 * Simulates enumeration of storage types supporting ADTs
 */
abstract class StorageType
{
    const MONGO_DB = 'mongo-db';
    const ORACLE = 'oracle';
}
