<?
    //require_once("isl.inc");
if (!defined("CLASS_EXTENSION")) {
    define("CLASS_EXTENSION", "cls");
}

spl_autoload_register('iaautoloader');
/**
 * This is a global auto load function to load any class as needed.
 * If a class needs to be loaded by PHP code but the class definition is not known,
 * the iaautoloader function is invoked to load the class definition.
 *
 * The iaautoloader function is invoked once per class, so it won't matter if the same class
 * is used multiple times in the code.
 *
 * @param string $className  the name of the class to load
 */
function iaautoloader($className)
{
    //if( isl_strpos($className, 'Logger') === false ) error_log($_SERVER['SCRIPT_FILENAME'] . " - Loading class $className");
    /** @noinspection PhpUnusedLocalVariableInspection */
    $ret = import($className);
}

/**
 * @param string $file
 *
 * @return bool
 */
function file_exists_in_path($file)
{
    foreach(explode(':', ini_get('include_path')) as $dir) {
        $l = strlen($dir);
        if ($l > 0 && $dir[$l-1] !== '/') {
            $dir .= '/';
        }
        if (file_exists($dir.$file)) {
            return true;
        }
    }
    return false;
}

/**
 * @param string|null $identifier
 *
 * @return bool
 */
function import($identifier)
{
    global $class_registry;
    if (!$identifier) {
        epp('Warning: import called with null argument');
        return false;
    }

    if ( class_exists($identifier, false)
        || interface_exists($identifier, false)
    ) {
        return true;
    }

    // this should optimize the majority of calls and avoid the call to class_lookup()
    $class_name = $class_registry['lower_to_upper_map'][strtolower($identifier)] ?? null;
    ($class_name != '') && include_once $class_name . '.' . CLASS_EXTENSION;
    if ( class_exists($identifier, false)
        || interface_exists($identifier, false)
    ) {
        return true;
    }

    $identifier = class_lookup($identifier);
    if (!$identifier) {
        return false;
    }
    $class_file = $identifier.'.'.CLASS_EXTENSION;


    if (file_exists_in_path($class_file) && include_once $class_file) {
        if ( class_exists($identifier, false)
            || interface_exists($identifier, false)
            || trait_exists($identifier, false)
        ) {
                return true ;
        }
        else {
            epp("Warning: file not in path or error including $class_file");
            return false;
        }
    }
    else {
        epp("did not find $class_file");
    }

    return false;
}

/** @noinspection GenericObjectTypeUsageInspection - yes, we accept any object
 *
 * @param string|object $id
 *
 * @return string|null
 */
function class_lookup($id)
{
    global $class_registry;

    if ( is_object($id) ) {
        return $class_registry['lower_to_upper_map'][strtolower(get_class($id))];
    }

    if ( false !== strpos($id, '.') ) {
        $file_to_class = array_flip($class_registry['class_to_file_map']);

        return $file_to_class[$id] ?? null;
    } else if ( preg_match('/[A-Z]/', $id) ) {
        if ( $class_registry['lower_to_upper_map'][strtolower($id)] ?? null ) {
            return $class_registry['lower_to_upper_map'][strtolower($id)];
        } else {
            //trigger_error("class_lookup(): {$id} not found in class registry (have you run make?)", E_USER_WARNING);
            return null;
        }
    } else {
        return $class_registry['lower_to_upper_map'][$id] ?? null;
    }
}
    require_once 'class_registry.inc';

