<?php

/**
 * Class ProxyDatabaseConnection
 *
 * Proxy connection to Java DB connector if the connection is application/schemalet
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2020 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class ProxyDatabaseConnection extends DatabaseConnection
{
    private const METRICS_SUMMARY_LOG = '/tmp/perf_db.log';
    public const DB_SOCKET_ENABLE  = 'ENABLE';
    public const DB_SOCKET_ADDRESS = 'ADDRESS';
    public const METRICS_LEVEL = 'METRICS_LEVEL';
    public const DB_SOCKET_PORT = 'PORT';
    public const CONFIG_LEVEL  = 'CONFIG_LEVEL';
    public const CFG_PROTCOL_VERSION = 'PROTCOL_VERSION';
    public const CONFIG_OPT_SCHEMA = 'schema';
    public const CONFIG_OPT_CENTRAL = 'central';
    public const USE_REST_API_CONNECT = 'USE_REST_API_CONNECT';
    public const REST_API_PORT = 'REST_API_PORT';
    public const ENABLE_IMS = 'ENABLE_IMS';

    protected const DIALECT_ORACLE = 'Oracle';
    protected const DIALECT_SNOWFLAKE = 'Snowflake';

    /** @var int $metricsLevel */
    public static $metricsLevel = 0; // 0 == no metrics, 1 == summary metrics, 2 == detailed metrics
    /** @var array $config  */
    private static $config;

    /** @var DBSocketConnector $connector */
    private $connector;

    /** @var array $metricsSummary */
    private static $metricsSummary = null;
    /** @var bool $execCommit set to true by executeStatement() if the call auto-commits */
    private $execCommit = false;
    /** @var string|null $proxyAddr */
    private $proxyAddr = null;
    /** @var array|null @metrics */
    private $metrics = null;

    public static function init()
    {
        $dbSocketConfig = ia_cfg::getDatabaseSocketConfig();
        self::$config = [
            'enabled'         => ($dbSocketConfig[self::DB_SOCKET_ENABLE] ?? 'false') === 'true',
            'address'         => $dbSocketConfig[self::DB_SOCKET_ADDRESS],
            'port'            => $dbSocketConfig[self::DB_SOCKET_PORT],
            'level'           => $dbSocketConfig[self::CONFIG_LEVEL] ?? self::CONFIG_OPT_SCHEMA,
            'restApiConnect'  => $dbSocketConfig[self::USE_REST_API_CONNECT],
            'restApiPort'     => $dbSocketConfig[self::REST_API_PORT],
            'protocolVersion' => $dbSocketConfig[self::CFG_PROTCOL_VERSION],
            'enableIMS'       => ($dbSocketConfig[self::ENABLE_IMS] ?? 'false') === 'true',
        ];

        if ( self::$config['enabled'] ) {
            if ( isset($_COOKIE['IA_DB_PROXY']) && $_COOKIE['IA_DB_PROXY'] === 'disable' ) {
                self::$config['enabled'] = false;
            }
        }

        self::$metricsLevel = $dbSocketConfig[self::METRICS_LEVEL];
    }

    /**
     * @param array $loginInfo
     * @param bool  $isApplicationConnection
     *
     * @return bool
     */
    public static function isProxyEnabledFor(array $loginInfo, bool $isApplicationConnection): bool
    {
        return self::$config['enabled']
            && ( self::$config['level'] === self::CONFIG_OPT_CENTRAL || $loginInfo[DBSchemaInfo::USE_PROXY] === 'T' ) && ($isApplicationConnection || self::$config['enableIMS']);

    }

    protected function disableProxyForRequest(): void
    {
        self::$config['enabled'] = false;
    }

    /**
     * ProxyDatabaseConnection constructor. This constructor is public because it is invoked from unit tests.
     *
     * @param array  $loginInfo
     * @param string $id
     *
     */
    public function __construct($loginInfo, $id)
    {
        parent::__construct($loginInfo, $id);
    }

    /**
     * @return int the proxy connection id
     */
    public function getProxyConnectionInfo()
    {
        if ( $this->conn != null ) {
            return $this->conn->getData()->getConnectionId();
        }

        return -1;
    }

    /**
     * @return bool
     * @throws DBSocketConnectorException
     */
    public function commit(): bool
    {
        return $this->simpleMessage(DBSocketConnector::MESSAGE_TYPE_COMMIT);
    }

    /**
     * @return bool
     * @throws DBSocketConnectorException
     */
    public function rollback(): bool
    {
        return $this->simpleMessage(DBSocketConnector::MESSAGE_TYPE_ROLLBACK);
    }

    /**
     * @param array $metrics
     * @param bool  $force
     *
     * @return array
     */
    public function getMetrics(array $metrics, bool $force = false)
    {
        if ( $this->conn ) {
            return $force ? parent::getMetrics($metrics, $force) : $this->conn->getData()->getDbMetrics();
        }

        return $metrics;
    }

    public function terminate() {
        // We won't terminate the Global DB connection since its reused in multiple places, especially in CS Tools
        if ( $this->id == self::GLOBAL_SERVER_ID ) {
            return;
        }

        if ( $this->conn != null ) {
            if ( ! $this->isJoinedConnection ) {
                $this->simpleMessage(DBSocketConnector::MESSAGE_TYPE_DISCONNECT);
            }
            $this->conn = null;
        }
        if ( $this->connector != null ) {
            $this->connector->close();
            $this->connector = null;
        }
        parent::terminate();
    }

    /**
     * @param string $messageType
     *
     * @return bool
     * @throws DBSocketConnectorException
     */
    private function simpleMessage(string $messageType): bool
    {
        $info = new DbInfo($this->connector->roundTrip(
            ['connID' => $this->conn->getData()->getConnectionId()],
            $messageType,
            $this
        ));

        if ( $info->getMessageType() == DBSocketConnector::MESSAGE_TYPE_OK ) {
            return true;
        } else {
            // this is an error message
            $this->getErrors($info);
            return false;
        }
    }

    /**
     * {@inheritDoc}
     */
    protected function joinDatabaseConnection(int $connectionID, string $dbu, string $db, $loginInfo, bool $isApplicationConnection)
    {
        try {
            $this->connector = $this->setupSocket($db, $loginInfo, $isApplicationConnection);
            $dbInfo = [
                'version'     => self::$config['protocolVersion'],
                'messageType' => DBSocketConnector::MESSAGE_TYPE_CONNECT,
                'data'        => [
                    'connID' => $connectionID,
                ]
            ];
            $dbConn = new DbInfo($dbInfo);

            $this->conn = $dbConn;
            $msg = ", joining existing connection for '$dbu@$db'\n";
            logToFileInfo(__FUNCTION__ . ": proxy conn id " . $connectionID . $msg);
            $dbMetrics = parent::getMetrics([]);
            $dbSessionInfo = parent::getDBSessionInformation(null);
            $dbConn->setAddlConnectInfo($dbMetrics, $dbSessionInfo);
            $this->getErrors($dbConn);
            return $dbConn;
        } catch (DBSocketConnectorException $ex) {
            $msg = ", FAILED to join existing connection for '$dbu@$db'\n";
            logToFileError(__FUNCTION__ . ": proxy conn id " . $connectionID . $msg);
            return null;
        }
    }

    protected function getDbId(): string {
        return Globals::$g->gDBServerId;
    }

    protected function getSchemaId(): string {
        return Globals::$g->gDBSchemaId;
    }

    protected function getDialect(): string {
        return self::DIALECT_ORACLE;
    }

    /**
     * {@inheritDoc}
     */
    protected function createDatabaseConnection($dbu, $dbp, $db, $dbCharset, $ownerId, $loginInfo, string $method, bool $isApplicationConnection)
    {
        $params = [
            'dbID' => $this->getDbId(),
            'schemaID' => $this->getSchemaId(),
            'host' => $db,
            'user' => $dbu,
            'pwd' => $dbp,
            'ownerID' => strtolower($ownerId),
            'meglID' => arrayExtractValue($loginInfo, 'meglid'),
            'charset' => $dbCharset,
            'caseInsensitiveSort' => DoCaseInsensitiveSort(),
            'sessionParams' => $loginInfo['session'],
            'readTime' => ServeCurrentTimestamp(),
            'readTimeGMT' => ServeCurrentTimestampGMT(),
            'requestID' => GenerateRequestId(),
            'webHost' => getenv("IA_SERVER_NAME"),
            'webPID' => getmypid(),
            'webUniqID' => Globals::$g->perfdata->getSerialnbr(),
            'isIMSConnection' => !$isApplicationConnection,
            'maxThreadsInPool' => arrayExtractValue($loginInfo, 'maxThreadsInPool'),
            'dialect' => $this->getDialect(),
        ];

        if ( self::$config['restApiConnect'] === 'true' ) {
            $headers = array('Content-Type: application/json');
            $url = "http://" . self::$config['address'] . ":" . self::$config['restApiPort'] . "/api/v1/db/connect";
            $ret = Util::httpCall($url, json_encode($params), $dbInfo, false, null, null, null, $headers,);
            if ( $ret !== 200 ) {
                logToFileError("Something went wrong while requesting connection through REST API");
                return null;
            } else {
                $data = json_decode($dbInfo, true, JSON_THROW_ON_ERROR);
                try {
                    $this->connector = self::getDbSocketConnectorMock() ??
                        new DBSocketConnector($data['hostName'], $data['port'], self::$config['protocolVersion']);
                    $dbInfo = [
                        'version'     => 1,
                        'messageType' => 'C',
                        'data'        => $data,
                    ];
                } catch (DBSocketConnectorException $ex) {
                    $dbInfo = [
                        'version'     => '1',
                        'messageType' => 'X',
                        'data'        => ['code' => 0, 'state' => 'PHP', 'message' => $ex->getMessage()],
                    ];
                }
            }
        } else {
            try {
                $this->connector = $this->setupSocket($db, $loginInfo, $isApplicationConnection);
                $dbInfo = $this->connector->roundTrip($params, DBSocketConnector::MESSAGE_TYPE_CONNECT, $this);
            } catch (DBSocketConnectorException $ex) {
                $dbInfo = [
                    'version'     => '1',
                    'messageType' => 'X',
                    'data'        => ['code' => 0, 'state' => 'PHP', 'message' => $ex->getMessage()],
                ];
            }
        }

        $dbConn = new DbInfo($dbInfo);

        $lastError = $this->getErrors($dbConn);
        if ( $lastError != false ) {
            if ( ($lastError['code'] == -1 || $lastError['code'] == -2) && $dbConn->getData()->getState() == 'APP' ) {
                // these are the maintenance/stopped error codes from DB proxy
                activateMaintenance();
            } else {
                LogToFile("$method: Connection error: " . print_r($lastError, true) . "\n");

                ManageORAErrors([
                    'sql' => 'connect',
                    'code' => $lastError['code'],
                    'message' => $lastError['message'],
                ]);
            }

            return null;
        }
        $msg = ", logging in as '$dbu@$db', charset:'$dbCharset'\n";
        logToFileInfo(__FUNCTION__ . ": proxy conn id " . $dbConn->getData()->getConnectionId() . $msg);
        return $dbConn;
    }

    /**
     * @param string $db
     * @param array  $loginInfo
     * @param bool   $isApplicationConnection
     *
     * @return DBSocketConnector
     */
    private function setupSocket(string $db, array $loginInfo, bool $isApplicationConnection)
    {
        $proxyPort = self::$config['port'];
        if ( self::$config['level'] == self::CONFIG_OPT_CENTRAL ) {
            $proxyHost = self::$config['address'];
        } else {
            $proxyHost = arrayExtractValue($loginInfo, DBSchemaInfo::PROXY_HOST);
            if (!$proxyHost) {
                if (!$isApplicationConnection && isl_str_endswith($db, 'ims')) {
                    $db = str_replace('ims', '', $db);
                }
                $proxyHost = DBSchemaInfo::computeHostForServer($db);
            }
            $proxyPort = arrayExtractValue($loginInfo, 'proxyPort') ?: $proxyPort;
        }
        $this->proxyAddr = "$proxyHost:$proxyPort";
        logToFileInfo("Initiating db proxy socket to " . $this->proxyAddr);
        return self::getDbSocketConnectorMock() ??
            new DBSocketConnector($proxyHost, $proxyPort, self::$config['protocolVersion']);
    }
    /**
     * @param array           $loginInfo
     * @param DbInfo|resource $dbConn
     * @param bool            $isApplicationConnection
     * @param string          $source
     * @param string          $dbOwner
     * @param string          $dbService
     *
     * @return bool
     */
    protected function executeAdditionalQueries($loginInfo, $dbConn, $isApplicationConnection, $source, $dbOwner, $dbService) : bool
    {
        global $gDBSchemaId;

        $meglId = arrayExtractValue($loginInfo, 'meglid');
        // change the oracle connection from 'USER to 'OWNER_XX' where XX is SCHEMALETID
        // this basically enables the access of all DB schema objects and still goes by privileges of the USER schema.
        $stmtstr = "
        BEGIN
          execute immediate 'ALTER SESSION SET CURRENT_SCHEMA = $dbOwner'; ";
        if ( DoCaseInsensitiveSort() ) {
            $stmtstr .= "execute immediate 'alter session set NLS_SORT=" . LinguisticSort::getNlsSort() . "_CI'; ";
        }

        if ( $loginInfo['session'] ) {
            foreach ( $loginInfo['session'] as $pair ) {
                [ $var, $value ] = explode(':', $pair);
                $stmtstr .= "execute immediate 'alter session set \"$var\"= $value'; ";
            }
        }

        // This generates or propagates the readtime timestamp, and a random number for the requestid, and stores it into the database context.
        $readtime = ServeCurrentTimestamp();
        $readtimegmt = ServeCurrentTimestampGMT();
        $requestid = GenerateRequestId();

        $stmtstr .= "\n " .
            "	TMCONTEXT.CLEAR();  " .
            "   MC.reset(); " .
            "	acct_Utils.batchOrMigrate:='F'; " .
            "	TMproc('readtime', :1);  " .
            "	TMproc('readtimegmt', :2);  " .
            "	TMproc('requestid', :3);  " .
            "	TMProc('SESSIONKEY',null);  " .
            "	TMProc('LOCATIONKEY',null);  " .
            "	TMProc('VIEWTYPE', null);  " .
            "	TMProc('CONTEXT_BOOK', null); " .
            "	TMProc('CURRENCY', 'ZZZ');  " .
            "	TMProc('DBID', :4);  " .
            "	TMProc('SCHEMAID', :5); " .
            "	TMProc('OWNERID', :6); " .
            "	TMProc('SKIP_POPULATE_ENTITY_TOTALS', 'F'); " .
            "	TMProc('MEGLID', :7); ";

        // reset ia_monitor context only if the dbu is not global
        if ( isl_stristr($dbService, 'global') === false ) {
            $stmtstr .= ' ia_monitor.SetClientIdentifier(null); ia_monitor.SetModule('
                . ( $isApplicationConnection ? 'null' : '\'imsq.phtml\'' )
                . ', null); ia_monitor.SetAction(null); ';
        }

        $stmtstr .= "END; ";

        return $this->execStmtEx(
            [
                $stmtstr,
                $readtime,
                $readtimegmt,
                $requestid,
                Globals::$g->gDBServerId,
                $gDBSchemaId,
                $dbOwner,
                $meglId
            ],
            0,
            [],
            $rowCount,
            false
        );
    }

    /**
     * {@inheritDoc}
     */
    protected function getDBSessionInformation($dbConn) : ?array
    {
        /** @var DbInfo $dbConn */
        $sid = $dbConn->getData()->getSid();
        $serial = $dbConn->getData()->getSerial();

        if ( null === $sid && null === $serial ) {
            logToFileError('Error getting DB session information.');

            return null;
        }

        logToFileInfo(
            'DB session information: SID: ' . $sid . ', ' . 'SERIAL#: ' . $serial
        );

        return [
            'SID'     => $sid,
            'SERIAL#' => $serial,
        ];
    }

    /**
     * {@inheritDoc}
     */
    protected function executeStatement($type, $stmt_data, $headers, $count, $start, $trxLevel)
    {
        Globals::$g->perfdata->incrementCount();
        $this->connector = self::getDbSocketConnectorMock() ?? $this->connector;
        $msgParams = [
            'connID'  => $this->conn->getData()->getConnectionId(),
            'headers' => $headers,
            'stmt'    => $stmt_data['stmt_desc'],
            'args'    => $stmt_data['bound_vars'] ?? null,
            'start'   => $start,
            'count'   => $count,
            'blobs'   => isset($stmt_data['blobs']) ? $this->getBlobs($stmt_data['blobs']) : null,
            'types'   => $stmt_data['types'] ?? null
        ];
        $this->execCommit = $msgParams['commit']  = $trxLevel === 1 && ! Globals::$g->gTx->hasPreCommitEvents($this);
        return new DbInfo($this->connector->roundTrip(
            $msgParams,
            $type,
            $this
        ));
    }

    /**
     * {@inheritDoc}
     */
    protected function fetchResults($stmt, $stmt_data, $source, $stmt_to_log, $connName, $start, $count, $headers, &$firstRow)
    {
        // at this point th query was executed and the caller confirmed that there is no error
        // then $stmt cotains the first batch of the result set
        // the way we pick the data out of $stmt it depends on the $headers flag
        // Also, note that the DB proxy code takes care of start & count, so we don't have to worry about it
        // during fetchResults

        $firstBatch = true;

        $entireResults = null;
        $entireCount = 0;

        do {
            /** @var DbInfo $stmt */
            $batchResults = $this->getResults($stmt, $headers);
            $batchCount = count($batchResults);

            if ( $batchCount === 0 ) {
                $entireResults = [];
                break;
            }

            if ( $firstBatch ) {
                if ( $batchCount > 0 ) {
                    $firstRow = $batchResults[0];
                }
                $entireResults = $batchResults;
                $firstBatch = false;
            } else {
                foreach ( $batchResults as $row ) {
                    $entireResults[] = $row;
                }
            }
            $entireCount += $batchCount;

            if ( $stmt->getData()->hasMore() ) {
                $dbInfo = new DbInfo($this->connector->roundTrip(
                    [
                        'connID'  => $this->conn->getData()->getConnectionId(),
                        'queryID' => $stmt->getData()->getID(),
                    ],
                    DBSocketConnector::MESSAGE_TYPE_QUERY_MORE,
                    $this
                ));
                if ( $dbInfo->getMessageType() == DBSocketConnector::MESSAGE_TYPE_ERROR ) {
                    $error = $this->getErrors($dbInfo);
                    logToFileWarning(
                        $source . ": during getting more data after $entireCount: " . $error['code']
                        . ' (' . $error['message'] . ') sql: ' . $stmt_to_log . "\n"
                    );
                    break;
                } else {
                    $stmt = $dbInfo;
                }
            } else {
                break;
            }
        } while ( true ); // the code breaks out of the loop when there are no more batchResults to fetch

        return $entireResults;
    }

    /**
     * @param array $results the results in which to decode the binary data;
     *                       it is passed by reference to avoid creating large copies
     * @param array $blobs
     *
     * @return array
     */
    private function decodeResults(array &$results, array $blobs) : array
    {
        if ( ! $blobs ) {
            return $results;
        }

        foreach ($results as &$row) {
            foreach ($blobs as $key) {
                if (!empty($row[$key])) {
                    $row[$key] = base64_decode($row[$key]);
                }
            }
        }
        unset($row); // unset since we used it as reference above

        return $results;
    }

    /**
     * {@inheritDoc}
     */
    protected function getErrors($stmt)
    {
        /** @var DbInfo $stmt */
        if ( $stmt->getMessageType() !== DBSocketConnector::MESSAGE_TYPE_ERROR ) {
            $this->lastError = false;
            return false;
        }

        if ( null === $stmt->getData()->getCode() && null === $stmt->getData()->getMessage() ) {
            $this->lastError = false;
            return false;
        }

        $this->lastError = [
            'code' => $stmt->getData()->getCode(),
            'message' => $stmt->getData()->getMessage(),
        ];

        return $this->lastError;
    }

    /**
     * {@inheritDoc}
     */
    protected function parseStatement(
        array $stmt_desc,
        array &$ret_stmt_data = null,
        int $noofblobcols = 0,
        $blobvars = null
    ) : bool
    {
        $ret_stmt_data = $ret_stmt_data ?? [];
        $ret_stmt_data['stmt_desc'] = $stmt_desc[0];

        for ( $i = 1; $i < count($stmt_desc); $i++ ) {
            $ret_stmt_data['bound_vars'][$i-1] = ($stmt_desc[$i] !== false && $stmt_desc[$i] !== "")
                ? (string) $stmt_desc[$i] : null;
        }

        return true;
    }

    /**
     * {@inheritDoc}
     */
    protected function getRowAffected($stmt)
    {
        /** @var DbInfo $stmt */
        return $stmt->getData()->getCount();
    }

    /**
     * {@inheritDoc}
     */
    protected function saveBlobls($noofblobcols, $blobvars, $stmt_data, $source, $stmt_to_log)
    {
        return true;
    }

    /**
     * @param array $blobs
     *
     * @return array
     */
    private function getBlobs(array $blobs) : array
    {
        $newBlobs = [];

        foreach ( $blobs as $blobDetail ) {
            if (strlen($blobDetail['DATA'] ?? '') > 0) {
                $newBlobs[] = base64_encode($blobDetail['DATA']);
            }
        }

        return $newBlobs;
    }

    /**
     * @param resource $stmt
     */
    protected function freeStatement($stmt)
    {
    }

    /**
     * {@inheritDoc}
     */
    protected function buildOciCollection(array $argTypes, string $connName, $conn) : array
    {
        return [];
    }

    /**
     * {@inheritDoc}
     */
    protected function appendToCollection(array $argTypes, array $stmtdata, array $collectionarray) : array
    {
        // for Java code we need to create a numerically indexed array
        // the argTypes here could be either numerically indexed or associative array
        $collection = [];
        $ix = 0;
        foreach ($argTypes as $key => $type ) {
            foreach ( $stmtdata as $row ) {
                if ( $row[$key] === "" || $row[$key] === null || $row[$key] === false ) {
                    $val = null;
                } else {
                    $val = $type == "date" ? trim($row[$key]) : $row[$key];
                }
                $collection[$ix][] = $val;

            }
            $ix++;
        }
        return $collection;
    }

    /**
     * {@inheritDoc}
     */
    protected function bindQueryParams(
        $stmtargs,
        $connName,
        $collectionarray,
        $stmt,
        $source,
        $stmtstr,
        $commitLines,
        $stmt_desc,
        $stmt_to_log) : bool
    {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    protected function parseExecStatement(string $stmtstr)
    {
        return true;
    }

    /**
     * @param DbInfo $stmt
     * @param string $source
     * @param string $stmt_to_log
     * @param int    $index
     * @param bool   $headers
     *
     * @return array
     * @throws DBSocketConnectorException
     */
    public function getBulkResults(&$stmt, string $source, string $stmt_to_log, int $index, bool $headers) : array
    {
        if ( ! $stmt->getData()->hasMore() ) {
            return [];
        }

        $stmt = new DbInfo($this->connector->roundTrip(
            [
                'connID'  => $this->conn->getData()->getConnectionId(),
                'queryID' => $stmt->getData()->getID(),
            ],
            DBSocketConnector::MESSAGE_TYPE_QUERY_MORE,
            $this
        ));

        if ( $stmt->getMessageType() !== DBSocketConnector::MESSAGE_TYPE_ERROR ) {
            return $this->getResults($stmt, $headers);
        }

        $error = $this->getErrors($stmt);

        logToFileWarning(
            $source . ": during getting more data after $index: " . $error['code']
            . ' (' . $error['message'] . ') sql: ' . $stmt_to_log . "\n"
        );

        return [];
    }

    /**
     * @param DbInfo $stmt
     * @param bool   $headers
     *
     * @return array
     */
    public function getResults(DbInfo $stmt, bool $headers) : array
    {
        /** @var DbInfo $stmt */
        $results =  $headers ? $stmt->getData()->getResultCols() : $stmt->getData()->getResultNums();

        if ( count($results) === 0 ) {
            return $results;
        }

        $blobs = $stmt->getData()->getBlobs();

        if ( count($blobs) > 0 ) {
            $results = $this->decodeResults($results, $blobs);
        }

        return $results;
    }

    /**
     * @return bool true if the executeStatement has auto-commited, false otherwise
     */
    protected function execStmtAutoCommit()
    {
        return $this->execCommit;
    }

    /**
     * @param array $metrics
     */
    public static function addMetrics(array $metrics)
    {
        if ( self::$metricsSummary === null ) {
            self::$metricsSummary = $metrics;
        } else {
            foreach( $metrics as $idx => $val ) {
                self::$metricsSummary[$idx] += $val;
            }
        }
    }

    public static function logMetricsSummary()
    {
        if ( self::$metricsLevel == 1 && self::$metricsSummary !== null ) {
            error_log('F2:'
                . Globals::$g->perfdata->getSerialnbr() . ':'
                . implode(":", self::$metricsSummary) . "\n", 3, self::METRICS_SUMMARY_LOG);
        }
    }

    /**
     * @param bool $isJoinedConnection
     */
    public function setIsJoinedConnection(bool $isJoinedConnection): void
    {
        if ( $isJoinedConnection != $this->isJoinedConnection ) {
            $cmd = $isJoinedConnection ? DBSocketConnector::MESSAGE_TYPE_RELEASE_OWNERSHIP : DBSocketConnector::MESSAGE_TYPE_TAKE_OWNERSHIP;

            $this->simpleMessage($cmd);

            parent::setIsJoinedConnection($isJoinedConnection);
        }

    }
    /**
     * @return string|null
     */
    public function getProxyAddr(): ?string
    {
        return $this->proxyAddr;
    }

    /**
     * @param int $timeoutMS
     */
    public function setCallTimeout(int $timeoutMS) : void
    {
        // Do nothing
    }

    /**
     * @param array $metrics
     */
    public function setMetrics(array $metrics): void
    {
        $this->metrics = $metrics;
    }

    /**
     * @return float
     */
    protected function getQueuedTime(): float
    {
        if ( $this->metrics != null ) {
            return round($this->metrics[4] / 1000, 3);
        }
        return 0;
    }
}

ProxyDatabaseConnection::init();