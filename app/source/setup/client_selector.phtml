<?php

header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

require_once "util.inc";
require_once 'login_util.inc';

Init();

if (Request::$r->_entRecordNo !== null) {
    require_once 'slide.inc';

    $entRecordNo = (int)Request::$r->_entRecordNo;
    if ($entRecordNo > 0) {
        // The customer has selected a client (an intem in the drop down list box other than console)
        InitSlide();

        $sourceInfo = FetchSourceInfo();

        $_destTitle = $_destUser = $_destPassword = '';
        $_attr = [];

        $clientLinkResult = FetchClientLink(
            $entRecordNo, $_destlocation, $_destTitle, $_destUser, $_destPassword,
            $_attr, $_srcop, $sourceInfo
        );

        if ($clientLinkResult) {
            $session = SwitchCompanies(
                $_destTitle, $_destUser, $_destPassword, '', $_destlocation, $_destdepartment, $_destlocgrp, $_destdeptgrp, $sourceInfo,
                $_destURL, $_uid, Request::$r->_invokedby ?? null
            );
        }

        if ($session) {
            // Successful slide in. Forward to the permission page providing the new session
            $params = [
                'state'         => Request::$r->state,
                'client_id'     => Request::$r->client_id,
                'redirect_uri'  => Request::$r->redirect_uri,
                'response_type' => Request::$r->response_type,
                'scope'         => Request::$r->scope,
                'username'      => Request::$r->username,
                '.access_token' => Request::$r->_access_token,
                '.op'           => Request::$r->_op,
                '.report'       => Request::$r->_report,
                '.mode'         => Request::$r->_mode,
                '.type'         => Request::$r->_type,
                '.sess'         => $session,
                '.p'            => '1',
                '.pickent'      => 'true',
                'cnyid'         => $_destTitle ?? '',
                'userid'        => $_destUser ?? ''
            ];
            $params_str = buildQueryString($params);
            Fwd("oauth_token.phtml?$params_str");
        }
    }
    Shutdown();
}

include_once 'cpaclient_util.inc';
$userid = Globals::$g->_userid;
$clientList = GetUserClientCompanies($userid);
ShowOAuthConsoleCompany(new LoginUtil(), $clientList);

