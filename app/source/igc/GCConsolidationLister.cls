<?php

/**
 *    FILE:            GCConsolidationLister.cls
 *    AUTHOR:          Girish D <<EMAIL>>
 *    DESCRIPTION:     File GCConsolidationLister.cls contains implementation of Global Consolidation Book lister
 *
 *    (C) 2008, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class GCConsolidationLister extends NLister
{

    function __construct()
    {
        if (IsMCMESubscribed()) {
            $fields = [
                'BOOKKEY', 'LOCATIONNAME', 'PERIODID', 'BSRATE', 'WARATE', 'ACTIONTIME', 'STARTEDBY',
                'CSNSTATUS', 'MESSAGE'
            ];
        } else {
            $fields = [
                'BOOKKEY', 'LOCATIONNAME', 'PERIODID', 'ACTIONTIME', 'STARTEDBY', 'CSNSTATUS',
                'MESSAGE'
            ];
        }

        $sortOrder = 'BOOKKEY:a,LOCATIONNAME:a';
        // If tier consolidation is enabled then add structure name and comment in the first column of lister
        if (IsOperationAllowed(GetOperationId('atlas/lists/gcownershipstructure/view'))) {
            array_unshift($fields, 'STRUCTURENAME');
            $sortOrder = 'STRUCTUREKEY:a,BOOKKEY:a,LOCATIONNAME:a';
        }

        $_params = array(
            'entity' => 'gcconsolidation',
            'title' => 'IA.CONSOLIDATION_HISTORY',
            'fields' => $fields,
            'sortcolumn' => $sortOrder
        );

        $bookName = Request::$r->F_BOOKKEY;
        $structureName = Request::$r->F_STRUCTURENAME;

        $_stdConsPeriod = Request::$r->F_PERIODID;
        if (!isNullOrBlank($_stdConsPeriod)) {
            $title = $_params['title'];
            if (!isNullOrBlank($bookName)) {
                $title = [
                    'id' => 'IA.CONSOLIDATION_TITLE_FOR_BOOK_PERIOD',
                    'placeHolders' => [
                        ['name' => 'PERIOD', 'value' => $_stdConsPeriod],
                        ['name' => 'BOOKNAME', 'value' => $bookName]
                    ]
                ];
            } elseif (!isNullOrBlank($structureName)) {
                $title = [
                    'id' => 'IA.CONSOLIDATION_HISTORY_STRUCTURENAME_PERIOD',
                    'placeHolders' => [
                        ['name' => 'PERIOD', 'value' => $_stdConsPeriod],
                        ['name' => 'STRUCTURENAME', 'value' => $structureName]
                    ]
                ];
            }
            $_params['title'] = $title;
        }
        parent::__construct($_params);

        $this->addLabelMapping('BOOKKEY', 'IA.BOOK', true);
        $this->addLabelMapping('ACTIONTIME', 'IA.RUN_DATE_AND_TIME', true);
        $this->addLabelMapping('STARTEDBY', 'IA.RUN_BY', true);
        $this->addLabelMapping('CSNSTATUS', 'IA.CONSOLIDATION_STATE', true);
        $this->addLabelMapping('MESSAGE', 'IA.DETAILS', true);
    }
}
