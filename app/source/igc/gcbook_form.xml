<?xml version="1.0" encoding='utf-8'?>
<ROOT>
    <entity>gcbook</entity>
    <view system="true">
        <events>
            <load>initialize();</load>
        </events>
        <title>IA.BOOK_SETUP</title>
        <pages>
            <page id="bookInformationPage" >
                <title>IA.BOOK_INFORMATION</title>
                <section isCollapsible="true" id="book-information-section" className="transaction-section" customFields="no">
                    <title>IA.GENERAL_INFORMATION</title>
                    <row noLabel="true">
                        <field fullname="IA.BOOK_NAME">
                            <path>BOOKID</path>
                            <events>
                                <change>bookNameChange(this, event)</change>
                            </events>
                        </field>
                        <field>
                            <path>DESCRIPTION</path>
                        </field>
                        <field fullname="IA.BOOK_STATUS">
                            <path>STATUS</path>
                        </field>
                    </row>

                    <row noLabel="true">
                        <field fullname="IA.STATISTICAL_JOURNAL_SYMBOL">
                            <path>BOOKSTATJOURNALSYMBOL</path>
                            <required>true</required>
                        </field>
                        <field fullname="IA.STATISTICAL_JOURNAL_NAME">
                            <path>BOOKSTATJOURNALTITLE</path>
                            <required>true</required>
                        </field>
                        <field fullname="IA.BUDGET">
                            <path>BUDGETID</path>
                        </field>
                    </row>

                    <row noLabel="true">
                        <field fullname="IA.ACCOUNTING_METHOD">
                            <path>SOURCEBOOKID</path>
                        </field>
                        <field fullname="IA.DEFAULT_DEPARTMENT">
                            <path>DEPARTMENTID</path>
                        </field>
                        <field fullname="IA.LEGACY_BOOK" hidden="true">
                            <path>ISLEGACYSTORAGE</path>
                            <readonly>true</readonly>
                        </field>
                    </row>
                </section>

                <section isCollapsible="true" id="currency-section" className="transaction-section" customFields="no">
                    <title>IA.MULTI_CURRENCY_TOKEN</title>
                    <row noLabel="true">
                        <field fullname="IA.CURRENCY">
                            <path>CURRENCY</path>
                        </field>
                        <field>
                            <path>EXCHRATETYPE</path>
                        </field>
                    </row>

                    <row label="IA.TRANSLATION_METHOD_FOR">
                        <field>
                            <path>BSTRANMETHOD</path>
                        </field>
                        <field>
                            <path>ISTRANMETHOD</path>
                        </field>
                    </row>

                    <row label="IA.CURRENCY_EXCHANGE_GAIN_LOSS_ACCOUNT">
                        <field>
                            <path>CTANETINCOMEACCOUNTNO</path>
                            <required>true</required>
                        </field>
                        <field>
                            <path>CTANETASSETACCOUNTNO</path>
                            <required>true</required>
                        </field>
                    </row>
                </section>

                <section id="kpisSection" className="transaction-section" customFields="no">
                    <title>IA.KPIS_TO_DISPLAY</title>
                    <grid>
                        <path>GCBOOKACCTGROUPS</path>
                        <column hidden="true">
                            <field fullname="IA.RECNO" hidden1="true">
                                <path>RECORDNO</path>
                                <required>false</required>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.GL_ACCOUNT_GROUP">
                                <path>ACCTGROUPKEY</path>
                                <required>false</required>
                                <events>
                                    <change>checkDuplicateElement('GCBOOKACCTGROUPS', this.meta);</change>
                                </events>
                            </field>
                        </column>
                    </grid>
                </section>
            </page>

            <page id="entitiesPage">
                <title>IA.ENTITIES_TO_CONSOLIDATE</title>
                <section isCollapsible="true" id="entities-to-consolidate-section" className="transaction-section" customFields="no">
                    <title>IA.CONSOLIDATION_ENTITIES</title>
                    <row noLabel="true">
                        <field required="true">
                            <path>ENTITIESTOCONSOLIDATE</path>
                        </field>
                        <field hidden="true">
                            <path>OWNERSHIPSTRUCTUREURL</path>
                        </field>
                        <field hidden="true">
                            <path>OWNERSHIPSTRUCTURELINK</path>
                            <type type='text' ptype='href'></type>
                            <fullname>IA.CONSOLIDATED_ENTITIES</fullname>
                            <events>
                                <click>redirectToOwnershipStructure();</click>
                            </events>
                            <hreftxt>IA.VIEW_OWNERSHIP_STRUCTURE</hreftxt>
                        </field>
                        <field fullname="IA.ELIMINATION_ENTITY">
                            <path>EENAME</path>
                            <events>
                                <change>addEliminationJournal()</change>
                            </events>
                        </field>
                    </row>
                    <row noLabel="true">
                        <field fullname="IA.ELIMINATION_ENTITY_HIDDEN" hidden="true">
                            <path>ELIMENTITYLOCNAME</path>
                        </field>
                        <field fullname="IA.ENABLE_INTER_ENTITY_AUTO_ELIMINATION">
                            <path>AUTOELIMINATION</path>
                            <events>
                                <change>addEliminationAccounts(this.checked);</change>
                            </events>
                        </field>
                        <field>
                            <path>ELIMINATIONADJACCT</path>
                            <required>true</required>
                        </field>
                        <field hidden="true">
                            <path>ELIMINATEBYAFFILIATEENTITY</path>
                            <events>
                                <change>includeAffiliateEntityDimension(this.checked);</change>
                            </events>
                        </field>
                    </row>
                </section>

                <section isCollapsible="true" id="dimensions_setup" className="transaction-section" customFields="no">
                    <title>IA.CONSOLIDATED_DIMENSIONS</title>
                    <subsection id="dimensionsetup" columnCount="3"></subsection>
                </section>
            </page>

            <page id="journalsPage">
                <title>IA.JOURNALS</title>
                <section>
                <row path="adjBookSelectBtnRow" hidden="true">
                    <caption className="float_right" id="adjBookSelect"
                             isCollapsible="false">
                        <button id="adjBookSelectBtn">
                            <name>IA.ADD_JOURNALS_FROM_ANOTHER_BOOK</name>
                            <events>
                                <click>showAdjBookSelect(this);</click>
                            </events>
                        </button>
                    </caption>
                </row>
                <row>
                    <grid noNewRows="true" hasFixedNumOfRows="true" showDelete="false" noDragDrop="true">
                        <path>JOURNALS</path>
                        <column hidden="true">
                            <field fullname="IA.BOOK" hidden="true">
                                <path>RECORDNO</path>
                                <required>false</required>
                            </field>
                            <field fullname="IA.BOOK" hidden="true">
                                <path>PRECORDNO</path>
                                <required>false</required>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BOOK" hidden="true">
                                <path>BOOKID</path>
                                <readonly>true</readonly>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BOOK">
                                <path>TRANSLATEDBOOKID</path>
                                <required>false</required>
                                <readonly>true</readonly>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.BOOK_TYPE" hidden="true">
                                <path>BOOKTYPE</path>
                                <readonly>true</readonly>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.ENTITY">
                                <path>LOCATIONNAME</path>
                                <required>false</required>
                                <readonly>true</readonly>
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.JOURNAL_SYMBOL">
                                <path>SYMBOL</path>
                                <type type="text" ptype="text" size="16" maxlength="16" />
                            </field>
                        </column>
                        <column>
                            <field fullname="IA.JOURNAL_TITLE">
                                <path>TITLE</path>
                                <type type="text" ptype="text" size="40" maxlength="40" />
                            </field>
                        </column>
                    </grid>
                </row>
                </section>
            </page>

            <page id="accountsToOverridePage">
                <title>IA.ACCOUNTS_TO_OVERRIDE</title>
                <section id="accounts-to-override-section">
                    <row>
                        <field>
                            <path>HISTORICALRATEDATETYPE</path>
                        </field>
                    </row>
                    <row>
                        <caption className="float_right" id="bulkUpdate"
                                 isCollapsible="false">
                            <button id="bulkUpdateRate">
                                <name>IA.CHANGE_RATE_TABLE_FOR_SELECTED_ACCOUN</name>
                                <events>
                                    <click>showBulkUpdatePopup(this);</click>
                                </events>
                            </button>
                        </caption>
                    </row>
                    <row>
                        <grid clazz="accountOverrideGrid" noDragDrop="true">
                            <path>GCBOOKACCTRATETYPES</path>
                            <column hidden="true">
                                <field fullname="IA.RECNO" hidden1="true">
                                    <path>RECORDNO</path>
                                    <required>false</required>
                                </field>
                            </column>
                            <column className="center">
                                <field fullname="IA.SELECT">
                                    <path>SELECT</path>
                                    <events>
                                        <change>enablebulkratetablebutton();</change>
                                    </events>
                                    <type type="boolean" ptype="boolean"></type>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.GL_ACCOUNT">
                                    <path>GLACCOUNTNO</path>
                                    <required>false</required>
                                    <events>
                                        <change>checkDuplicateElement('GCBOOKACCTRATETYPES', this.meta);</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.RATE_TYPE">
                                    <path>GLACCTRATETYPES</path>
                                    <required>false</required>
                                    <events>
                                        <change>validateRateTypes(this.meta.getLineNo());</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.RATE_TABLE">
                                    <path>OVERRIDERATE</path>
                                    <required>false</required>
                                    <events>
                                        <change>validateRateTypes(this.meta.getLineNo());</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.USE_RATE_TABLE_UNTIL">
                                    <path>OVERRIDEEXPIRYDATE</path>
                                    <required>false</required>
                                    <events>
                                        <change>displayWarningMessageOnModifyAccts();</change>
                                    </events>
                                </field>
                            </column>
                        </grid>
                    </row>
                </section>
            </page>

            <page id="eliminationAcctPage">
                <title>IA.ELIMINATION_ACCOUNTS</title>
                <section id="elimination-acct-section">
                    <row label="IA.GL_ACCOUNTS_SELECTED_FROM_ENTITY_SETU">
                        <field fullname="IA.ENTITY_ELIMINATION_ACCOUNT" hidden="true">
                            <path>ENTITYELIMACCTMAP</path>
                            <required>false</required>
                        </field>
                        <grid noNewRows="true" hasFixedNumOfRows="true" showDelete="false" noDragDrop="true">
                            <path>LOCATIONELIM</path>
                            <column hidden="true">
                                <field fullname="IA.RECNO" hidden="true">
                                    <path>RECORDNO</path>
                                    <required>false</required>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.GL_ACCOUNT">
                                    <path>LOCELIMGLACCOUNTNO</path>
                                    <readonly>true</readonly>
                                </field>
                            </column>
                        </grid>
                    </row>

                    <row label="IA.ADDITIONAL_GL_ACCOUNTS_FOR_ELIMINATIO">
                        <grid noDragDrop="true">
                            <path>GCBOOKELIMACCOUNTS</path>
                            <column>
                                <field fullname="IA.GL_ACCOUNT">
                                    <path>GLACCOUNTNO</path>
                                </field>
                            </column>
                        </grid>
                    </row>
                </section>

            </page>
        </pages>

        <!-- Bulk set rate table popup -->
        <floatingPage modalsize="small">
            <id>bulkUpdateRateTablePage</id>
            <pages>
                <page>
                    <section className="transaction-section" customFields="no">
                        <title>IA.SET_SELECTED_ACCOUNTS_TO</title>
                        <row>
                            <field fullname="IA.RATE_TYPE">
                                <path>RATETABLE</path>
                                <required>false</required>
                                <events>
                                    <change>validateBulkRateTypes();</change>
                                </events>
                            </field>
                        </row>
                        <row>
                            <field fullname="IA.RATE_TABLE">
                                <path>RATETYPES</path>
                                <required>false</required>
                                <events>
                                    <change>validateBulkRateTypes();</change>
                                </events>
                            </field>
                        </row>
                        <row>
                            <field fullname="IA.USE_RATE_TABLE_UNTIL">
                                <path>RATETABLEUNTIL</path>
                            </field>
                        </row>
                    </section>
                </page>
            </pages>
            <footer>
                <button id="okButton">
                    <name>IA.OK</name>
                    <events>
                        <click>updateBulkRates()</click>
                    </events>
                </button>
                <button id="cancelButton">
                    <name>IA.CANCEL</name>
                    <events>
                        <click>window.editor.hidePage('bulkUpdateRateTablePage',false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>

        <floatingPage modalsize="small">
            <id>adjBookPage</id>
            <pages>
                <page>
                    <section className="transaction-section" id="booksToAdd" customFields="no">
                        <title>IA.SELECT_BOOKS_TO_ADD</title>
                        <grid hasFixedNumOfRows="true" title="" noDragDrop="true"
                              noPagination="true">
                            <path>JOURNALBOOKTYPES</path>
                            <column className="center">
                                <field fullname="IA.SELECT">
                                    <path>JBTSELECT</path>
                                    <type type="boolean" ptype="boolean"></type>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.BOOK_TYPE" hidden="true">
                                    <path>BOOKTYPE</path>
                                    <readonly>true</readonly>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.BOOK" hidden="true">
                                    <path>JOURNALBOOKID</path>
                                    <readonly>true</readonly>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.BOOK">
                                    <path>TRANSLATEDBOOKID</path>
                                    <readonly>true</readonly>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.FIRST_PERIOD_CONSOLIDATED_WITH_DATA" path='PERIODCONSOLIDATED' isHTML="1">
                                    <type type='href' ptype='href' />
                                    <events>
                                        <click>fetchFirstPeriodWithData(this.meta);</click>
                                    </events>
                                </field>
                            </column>
                        </grid>
                    </section>
                </page>
            </pages>
            <footer>
                <button id="selectedBooksButton">
                    <name>IA.ADD_SELECTED_BOOKS</name>
                    <events>
                        <click>updateAdjJournals()</click>
                    </events>
                </button>
                <button id="closebutton">
                    <name>IA.CANCEL</name>
                    <events>
                        <click>window.editor.hidePage('adjBookPage',false);</click>
                    </events>
                </button>
            </footer>
        </floatingPage>
    </view>
    <helpfile></helpfile>
</ROOT>
