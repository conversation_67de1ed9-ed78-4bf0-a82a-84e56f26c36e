<?php

/**
 *    FILE:            gcownershipstructurechart.ent
 *    AUTHOR:          Nithin MG <<EMAIL>>
 *    DESCRIPTION:     ent for ownership structure detail
 *
 *    (C) 2008, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

$kSchemas['gcownershipstructurechart'] = array(
    'object' => array(
        'RECORDNO',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'GRAPHPREVIEW' => ''
    ),
    'fieldinfo' => [
        [
            'path'     => 'OWNERSHIPSTRUCTURE',
            'fullname' => 'IA.OWNERSHIP_STRUCTURE',
            'type'       => [
                'ptype'      => 'ptr',
                'type'       => 'text',
                'entity'     => 'gcownershipstructure',
                'pickentity' => 'gcownershipstructure',
                'pickfield' => array('STRUCTURENAME'),
            ],
            'required' => true,
        ],
        [
            'fullname' => 'IA.REPORTING_PERIOD_EFFECTIVE_START_DATE',
            'desc' => 'IA.REPORTING_PERIOD_EFFECTIVE_START_DATE',
            'type' => array(
                'ptype' => 'webcombo',
                'type' => 'webcombo',
                'validvalues' => array(),
                '_validivalues' => array(),
                'validlabels' => array(),
            ),
            'path' => 'FROMPERIOD',
            'id' => 7,
            'required' => true,
        ],
        [
            'path' => 'EMAILADD',
            'fullname' => 'IA.NOTIFY_THE_FOLLOWING_EMAIL_ADDRESS',
            'required' => false,
            'type' => [
                'ptype' => 'text',
                'type' => 'text',
                'format' => $gEmailFormat
            ],
            'id' => 8
        ],
        [
            'fullname' => 'Zoom In',
            'path' => 'MAP_ZOOM_IN',
            'hrefimage' => '<i class="fa fa-fw fa-search-plus"/>',
            'type' => [
                'ptype' => 'href',
                'type' => 'href'
            ],
            'id' => 9
        ],
        [
            'fullname' => 'Zoom Out',
            'path' => 'MAP_ZOOM_OUT',
            'hrefimage' => '<i class="fa fa-fw fa-search-minus"/>',
            'type' => [
                'ptype' => 'href',
                'type' => 'href'
            ],
            'id' => 10
        ],
    ],
    'table' => 'dummy',
    'vid'     => 'RECORDNO',
    'printas' => 'IA.OWNERSHIP_STRUCTURE_CHART',
    'pluralprintas' => 'IA.OWNERSHIP_STRUCTURE_CHARTS',
    'module' => 'atlas',
    'dontGenerateQueries' => true,
);

