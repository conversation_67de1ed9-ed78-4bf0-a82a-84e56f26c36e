<?php

/**
 *    FILE:            GCBookPicker.cls
 *    AUTHOR:          Nithin MG <<EMAIL>>
 *    DESCRIPTION:     File GCBookPicker.cls contains implementation of global consolidation editor
 *
 *    (C) 2008, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

/**
 * Class GCBookPicker
 * Picker class for the gcbook entitiy
 */
class GCBookPicker extends NPicker
{

    public function __construct()
    {
        parent::__construct(
            [
                'entity'        =>  'gcbook',
                'fields'        =>  array('BOOKID', 'DESCRIPTION', 'STATUS'),
                'pickfield'        =>  'BOOKID',
            ]
        );
    }
}
