<?
//=============================================================================
//
//	FILE:					csv_metadata_loanschedule.inc
//	AUTHOR:					Nirmal Shukla
//	DESCRIPTION:			Loan Schedule Import Metadata
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

    $imeta = array(
            'LOANID'            =>    'LOAN_ID',
            'PAYMENT_NO'        =>    'PAYMENT_NO',    
            'BILL_NO'            =>    'BILL_NO',
            'CREATED_DATE'        =>    'CREATED_DATE',
            'GL_POSTING_DATE'    =>    'GL_DATE',
            'DUE_DATE'            =>    'DUE_DATE',
            'PRINCIPAL_AMOUNT'    =>    'PRINCIPAL',
            'INTEREST_AMOUNT'    =>    'INTEREST',
            'ESCROW_AMOUNT'        =>    'ESCROW',
            'OTHER_1_AMOUNT'    =>    'OTHER_1',
            'OTHER_2_AMOUNT'    =>    'OTHER_2',
            'OTHER_3_AMOUNT'    =>    'OTHER_3',
            'OTHER_4_AMOUNT'    =>    'OTHER_4',
            'OTHER_5_AMOUNT'    =>    'OTHER_5',
            'OTHER_6_AMOUNT'    =>    'OTHER_6',
            'OTHER_7_AMOUNT'    =>    'OTHER_7',
            'SCHEDULE_STATUS'    =>    'STATUS',
            );

    $imetagroom = array(
        'STATUS' => 'SetScheduleStatus'
    );

