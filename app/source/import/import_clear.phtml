<?  
//=============================================================================
//
//	FILE:					import_clear.phtml
//	AUTHOR:				<PERSON>
//	DESCRIPTION:		Company Migration Utility - data clearing page
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

    require_once 'html_header.inc';
    require_once 'util.inc';
    require_once 'cpa_util.inc';
    require_once 'browser.inc';    
    require_once 'imp_delete.inc';
    require_once 'backend_import.inc';

	$_op 	 = Request::$r->_op;
	$_sess   = Request::$r->_sess;
	$hlpfile = &Request::$r->hlpfile;
    $_all_budgets = Request::$r->_all_budgets;
	$_all_company = &Request::$r->_all_company;
    $_all_cust    = &Request::$r->_all_cust;
    $_all_emp     = &Request::$r->_all_emp;
    $_all_trans   = &Request::$r->_all_trans;
    $_all_vend    = &Request::$r->_all_vend;
    $_continue    = Request::$r->_continue;
    $inFrame      = Request::$r->_popup == '0';

    $localTokens = [
            'IA.CLEAR_DATA', 'IA.COMPANY_MIGRATION_UTILITY_CLEAR_COMPANY_DATA', 'IA.CANCEL', 'IA.ALL_TRANSACTIONS', 'IA.ALL_BUDGETS',
            'IA.ALL_CUSTOMERS', 'IA.ALL_VENDORS', 'IA.ALL_EMPLOYEES', 'IA.CLEAR_ALL_DATA_CLEAN_COMPANY', 'IA.INTACCT_COMPANY_MIGRATION_UTILITY_STATUS',
            'IA.DELETE_IN_PROGRESS', 'IA.PROCESSING_PLEASE_WAIT_ECLIPSE', 'IA.CLEAR_DATA_SUCCESS', 'IA.CLEAR_DATA_FAIL', 'IA.EMPLOYEES',
            'IA.CUSTOMERS', 'IA.VENDORS', 'IA.TRANSACTIONS', 'IA.BUDGETS'
    ];
    $jsTokens = ['IA.PLEASE_SELECT_A_MODULE_TO_CLEAR', 'IA.WARNING_CLEAR_COMPANY', 'IA.WARNINGS'];
    $textJSON = json_encode(I18N::getTokensForArray(I18N::tokenArrayToObjectArray($jsTokens)));
    $textMap = I18N::getTokensForArray(I18N::tokenArrayToObjectArray($localTokens));

    $source  = "import_clear.phtml";
    LinguisticSortUtil::skipLinguisticSortSetup();
    Init();

    global $_userid;
    $templateUser = IsTemplateUser();
    $CPAUser = IsCPAUser();
    $removeCompanyClear = GetValueForIACFGProperty("IA_REMOVE_COMPANY_CLEAR");

// 	global $gErr;
    list($user_rec,$cny) = explode('@', $_userid);
    $delstatus = '';
    $delmessage = '';

if (isset($_continue) && $_continue != '') {
    if ( ! CsrfUtils::verifyInputToken('import_clear') ) {
        LogToFile("\n Failed to delete all the records selected. Invalid request. \n");
        epp("\n Failed to delete all the records selected. Invalid request. \n");
        // 			$gErr->AddError('BL01000041',__FILE__.":".__LINE__,"Failed to delete all the records selected.");
        $delstatus = 'F';
        $hlpfile = 'Clearing_Data_Failed';
    }elseif (!$_all_trans == 'T' && !$_all_budgets == 'B' && !$_all_emp == 'E' && !$_all_cust == 'C' && !$_all_vend == 'V' && !$_all_company == 'A') {
        //add an error
        epp("\n nothing was selected -- try again \n");
    } else {
        $del_list = '';
        if ($_all_company == 'A') {
            $del_list = 'AECVTB';
            $delmessage = "Company";
        } else {
            if ($_all_emp) {
                $del_list .= $_all_emp; 
                $delmessage = (($delmessage <> '') ? ($delmessage . ", ") : "") . GT($textMap, 'IA.EMPLOYEES');
            }
            if ($_all_cust) {
                $del_list .= $_all_cust; 
                $delmessage = (($delmessage <> '') ? ($delmessage . ", ") : "") . GT($textMap,"IA.CUSTOMERS");
            }
            if ($_all_vend) {
                $del_list .= $_all_vend; 
                $delmessage = (($delmessage <> '') ? ($delmessage . ", ") : "") . GT($textMap,"IA.VENDORS");
            }
            if ($_all_trans) {
                $del_list .= $_all_trans; 
                $delmessage = (($delmessage <> '') ? ($delmessage . ", ") : "") . GT($textMap,'IA.TRANSACTIONS');
            }
            if ($_all_budgets) {
                $del_list .= $_all_budgets; 
                $delmessage = (($delmessage <> '') ? ($delmessage . ", ") : "") . GT($textMap,'IA.BUDGETS');
            }

        }

        if (FeatureConfigManagerFactory::getInstance()->isFeatureEnabled("ENABLE_CLEAR_COMPANY_DATA_SCRIPT_IMS")) {
            global $_userid;
            $_sess = Session::getKey();
            $notificationEmail = GetEmailForNotification();
            $incorrectEmails = ['', '<EMAIL>', '<EMAIL>'];

            // Validate the email
            $isEmailValid = filter_var($notificationEmail, FILTER_VALIDATE_EMAIL) && !in_array($notificationEmail, $incorrectEmails);

            // Prepare the invalid email message only if the email is invalid
            if (!$isEmailValid) {
                $invalidEmailToken = I18N::getTokensForArray([
                    [
                        'id' => 'IA.AFTER_THE_CLEAR_COMPANY_DATA_PROCESS_COMPLETES_INVALID_EMAIL',
                        'placeHolders' => [
                            ['name' => 'EMAIL', 'value' => util_encode($notificationEmail)],
                        ]
                    ]
                ]);

                // Set the error message if the email is invalid
                $isValidEmail = GT($invalidEmailToken, 'IA.AFTER_THE_CLEAR_COMPANY_DATA_PROCESS_COMPLETES_INVALID_EMAIL');
            } else {
                $isValidEmail = '';
            }

            $userName = GetCompany(Globals::$g->_userid)['CONTACTNAME'];
            $imsObj = [
                'importParams' => ['emailaddress' => $notificationEmail, 'userName' => $userName],
                'arguments'    => ['del_list' => $del_list, 'all_company' => $_all_company, 'cny' => $cny, 'delmessage' => $delmessage, 'inFrame' => $inFrame],
                'userid'       => $_userid,
                'session'      => $_sess,
            ];

            $publish = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);
            if (!$publish->PublishMsg('CLEAR_COMPANY_DATA_SCRIPT', 'CLEAR_COMPANY_DATA', 'CLEAR_COMPANY_DATA_OBJECT', '', $imsObj, [], $response, 0, IMS_TYPE_LARGE)) {
                $gErr->addIAError('CO-0378', __FILE__ . ":" . __LINE__, "PublishMsg failed");
                $ok = false;
            }

            ForwardtoSuccessURL(urlencode($delmessage), '', 'clear_company_data', false, $notificationEmail, 0, 0, $isValidEmail, $inFrame ? '0' : '');
        } else {
            $result = cleanDB4Import($del_list, Session::getKey(), GetCompanyMinMaxDate(), GetPaymentInfo(), util_isPlatformDisabled());
            if (!$result) {
                $delstatus = 'F';
                $hlpfile = 'Clearing_Data_Failed';
            } else {
                if ($_all_company === 'A') {
                    CompanyCacheHandler::getInstance($cny, true)->destroyCache();
                }
                $delstatus = 'T';
                $hlpfile = 'Clearing_Data_Succeeded';
                ForwardtoSuccessURL(urlencode($delmessage), '', 'delete', false, '', 0, 0, '', $inFrame ? '0' : '');
            }
        }
    }
}
    
$_all_trans = '';
$_all_emp = '';
$_all_cust = '';
$_all_vend = '';
$_all_company = '';

$isQuixote = QXCommon::isQuixote();

if ($templateUser == 'T' || $CPAUser == 'T') {
    $title = ' ' . GT($textMap, 'IA.CLEAR_DATA');
    $hlpfile = 'Clear_Template_Data';
} else {
    $title = ' ' . GT($textMap, 'IA.COMPANY_MIGRATION_UTILITY_CLEAR_COMPANY_DATA');
    $hlpfile = 'Clear_Company';
}
    $jsbuttons[] = array('clear', GT($textMap, 'IA.CLEAR_DATA'), "return launchStatus();");
    if ($inFrame) {
        $cancelBtnEvt = "window.parent.openInIFrame('iamain', 'dash.phtml?.op=" . GetOperationId("dash") . "&.nomenu=1&.sess=" .
        Session::getKey() . "');return false;";
    } elseif ($isQuixote) {
        $cancelBtnEvt = "window.parent.closeQxDialog(window.frameElement);";
    } else {
        $cancelBtnEvt = "if (parent.YAHOO && parent.YAHOO.picker && parent.YAHOO.picker.dialog){parent.YAHOO.picker.dialog.hide();}";
    }

    $jsbuttons[] = array('impback', GT($textMap, 'IA.CANCEL'), $cancelBtnEvt);
    $props = array();
    $props['nocheck'] = false;
    $props['nohotkey'] = false;
    $props['incJSValidationFiles'] = true;
    $props['customCSS'] = '<link href="../resources/thirdparty/yui/css/container.css" rel="stylesheet" type="text/css">';
    PrintCommonHtmlHeader($props);?>

<SCRIPT>
    var textMap = <?= $textJSON ?>;

function launchStatus() {
	if (document.forms.f.elements['.comp'].checked == true ||
		document.forms.f.elements['.trans'].checked == true ||
		document.forms.f.elements['.budgets'].checked == true ||
		document.forms.f.elements['.emp'].checked == true ||
		document.forms.f.elements['.cust'].checked == true ||
		document.forms.f.elements['.vend'].checked == true) {
		if (confirm(GT('IA.WARNING_CLEAR_COMPANY'))) {

            if (typeof PAGE_LAYOUT_TYPE !== 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
                var ctx = window.top || window;
                if (ctx.QXUtil) { ctx.QXUtil.showLoading(1, '<?= GT($textMap,'IA.PROCESSING_PLEASE_WAIT_ECLIPSE'); ?>'); }
            } else {
                // if yahoo dialog is opened then just show progress bar
                if (parent.YAHOO.picker.dialog) {
                    DisplayLoadingPanel(GT($textMap,'IA.PROCESSING_PLEASE_WAIT_ECLIPSE'));
                }
                else {

                    var StatusTitle = "<? echo urlencode(GT($textMap,'IA.INTACCT_COMPANY_MIGRATION_UTILITY_STATUS'));?>";
                    var StatusVar = '.processed';
                    var StatusVal = '1';
                    var ProgressTitle = "<? echo urlencode(GT($textMap,'IA.DELETE_IN_PROGRESS'));?>";
                    var StatusType = 'cmu_delete';
                    var url = 'process_status.phtml?.sess=<?echo $_sess?>&.op=<? echo $_op;?>&.status_title=' + StatusTitle +
                        '&.status_var=' + StatusVar + '&.status_val=' + StatusVal + '&.progress_title=' + ProgressTitle + '&.status_type=' + StatusType;
                    var params = 'width=740,height=150,status=yes,scrollbars=no,dependent,left=100,top=200';
                    var hWnd = window.open(url, "status", params);
                    hWnd.focus();
                }
            }
			document.forms.f.elements['.continue'].value = 'Clear';
			//following line creates a duplicate request, so commented out for now.
			//document.forms.f.submit();
			return true;
		}else{
			document.forms.f.elements['.continue'].value = '';
			return false;
		}
	} else {
		alert(GT('IA.PLEASE_SELECT_A_MODULE_TO_CLEAR'));
		document.forms.f.elements['.continue'].value = '';
		return false;
	}
}

function setProcessed() {
	document.forms[0].elements['.processed'].value = 1;
}

function Do(deltype) {
	if (deltype == 'E') {
		if (document.forms.f.elements['.emp'].checked == true) {
			document.forms.f.elements['.all_emp'].value = deltype;
			document.forms.f.elements['.trans'].checked = true;
			document.forms.f.elements['.all_trans'].value = 'T';
		} else {
			document.forms.f.elements['.all_emp'].value = '';
		}
	} else if (deltype == 'C'){
		if (document.forms.f.elements['.cust'].checked == true) {
			document.forms.f.elements['.all_cust'].value = deltype;
			document.forms.f.elements['.trans'].checked = true;
			document.forms.f.elements['.all_trans'].value = 'T';
		} else {
			document.forms.f.elements['.all_cust'].value = '';
		}
	} else if (deltype == 'V') {
		if (document.forms.f.elements['.vend'].checked == true) {
			document.forms.f.elements['.all_vend'].value = deltype;
			document.forms.f.elements['.trans'].checked = true;
			document.forms.f.elements['.all_trans'].value = 'T';
		} else {
			document.forms.f.elements['.all_vend'].value = '';
		}
	} else if (deltype == 'T') {
		if (document.forms.f.elements['.trans'].checked == true) {
			document.forms.f.elements['.all_trans'].value = deltype;
		} else {
			document.forms.f.elements['.all_trans'].value = '';
			document.forms.f.elements['.all_emp'].value = '';
			document.forms.f.elements['.all_cust'].value = '';
			document.forms.f.elements['.all_vend'].value = '';
			document.forms.f.elements['.all_company'].value = '';
			document.forms.f.elements['.emp'].checked = false;
			document.forms.f.elements['.cust'].checked = false;
			document.forms.f.elements['.vend'].checked = false;
			document.forms.f.elements['.comp'].checked = false;
		}
	} else if (deltype == 'B') {
		if (document.forms.f.elements['.budgets'].checked == true) {
			document.forms.f.elements['.all_budgets'].value = deltype;
		} else {
			document.forms.f.elements['.all_budgets'].value = '';
		}
	}
	else if (deltype == 'A') {
		if (document.forms.f.elements['.comp'].checked == true) {
			document.forms.f.elements['.trans'].checked = true;
			document.forms.f.elements['.budgets'].checked = true;
			document.forms.f.elements['.emp'].checked = true;
			document.forms.f.elements['.cust'].checked = true;
			document.forms.f.elements['.vend'].checked = true;
			document.forms.f.elements['.all_company'].value = deltype;
			document.forms.f.elements['.all_trans'].value = 'T';
			document.forms.f.elements['.all_vend'].value = 'V';
			document.forms.f.elements['.all_cust'].value = 'C';
			document.forms.f.elements['.all_emp'].value = 'E';
		} else {
			document.forms.f.elements['.all_company'].value = '';
		}
	}
}

</SCRIPT>
<script type="text/javascript" language="javascript" src="../resources/js/base_lib.js"></script>
<form name="f" action="<? echo GoUrl("import_clear.phtml"); ?>" method=post >
<input type=hidden name='.sess' value="<? echo $_sess; ?>">
<input type="hidden" name=".op" value="<? echo $_op ?>">
<input type="hidden" name=".continue" value="">
<input type="hidden" name=".all_trans" value="<? echo $_all_trans ?>">
<input type="hidden" name=".all_budgets" value="<? echo util_encode($_all_budgets); ?>">
<input type="hidden" name=".all_emp" value="<? echo $_all_emp ?>">
<input type="hidden" name=".all_cust" value="<? echo $_all_cust ?>">
<input type="hidden" name=".all_vend" value="<? echo $_all_vend ?>">
<input type="hidden" name=".all_company" value="<? echo $_all_company ?>">
<input type=hidden name=".processed" value="0">
<input type="hidden" name=".popup" value="<? if ($inFrame) { ?>0<? } else { ?>1<? } ?>">
<?= CsrfUtils::generateCsrfTokenInput('import_clear') ?>
<? htmlHeaderBar($title, '', [], $jsbuttons, true); ?>
<? if ($isQuixote) { ?>
<div class="qx-page-container">
<? } ?>
    <? if ($delstatus == 'T') { ?>
        <script language="javascript">setProcessed();</script>
    <? if ($isQuixote) { ?>
        <div class="qx-message-section">
            <div class="qx-message-content">
                <div class="qx-message qx_message_type0">
                    <? echo GT($textMap, 'IA.CLEAR_DATA_SUCCESS'); ?>
                </div>
            </div>
        </div>
    <? } else { ?>
        <table border="0" cellpadding="5" cellspacing="0">
            <tr>
                <td align=left><font color="#FFOOOO" size=4 face="verdana"><b><?= GT($textMap, 'IA.CLEAR_DATA_SUCCESS'); ?></b></font></td>
            </tr>
        </table>
    <? }
    } else if ($delstatus == 'F') { ?>
        <script language="javascript">setProcessed();</script>
    <? if ($isQuixote) { ?>
        <div class="qx-message-section">
            <div class="qx-message-content">
                <div class="qx-message qx_message_type3">
                    <? echo GT($textMap, 'IA.CLEAR_DATA_FAIL'); ?>
                </div>
            </div>
        </div>
    <? } else { ?>
        <table border="0" cellpadding="5" cellspacing="0">
            <tr>
                <td align=left><font color="#FFOOOO" size=4 face="verdana"><b><?= GT($textMap, 'IA.CLEAR_DATA_FAIL'); ?></b></font></td>
            </tr>
        </table>
    <? }
    } ?>
<? if (!$isQuixote) { ?>
    <br><br>
<? } else { ?>
    <div class="qx-page-section">
        <div class="qx-page-element">
    <? } ?>
<table class="field_list_data qx-co-import-data" width="100%">
<tr>
<td>
<table border="0" cellpadding="5" cellspacing="1">
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
    <tr>
        <td align=left width="40%" class="label_cell">
            <?= GT($textMap, 'IA.ALL_TRANSACTIONS'); ?>
        </td>
        <td>
            <div class="checkbox">
                <input type="checkbox" class="noborder" name=".trans" onclick="javascript:Do('T');"
                       onmouseover='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onfocus='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onblur='window.status="";return true;'
                       onmousemove='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onmouseout="window.status=''; return true;">
                <label></label>
            </div>
        </td>
    </tr>
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
    <tr>
        <td align=left width="40%" class="label_cell">
            <?= GT($textMap, 'IA.ALL_BUDGETS'); ?>
        </td>
        <td>
            <div class="checkbox">
                <input type="checkbox" class="noborder" name=".budgets" onclick="javascript:Do('B');"
                       onmouseover='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onfocus='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onblur='window.status="";return true;'
                       onmousemove='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onmouseout="window.status=''; return true;">
                <label></label>
            </div>
        </td>
    </tr>
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
    <tr>
        <td align=left width="40%" class="label_cell">
            <? echo GT($textMap,'IA.ALL_CUSTOMERS'); ?>
        </td>
        <td>
            <div class="checkbox">
                <input type="checkbox" class="noborder" name=".cust" onclick="javascript:Do('C');"
                       onmouseover='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onfocus='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onblur='window.status="";return true;'
                       onmousemove='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onmouseout="window.status=''; return true;">
                <label></label>
            </div>
        </td>
    </tr>
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
    <tr>
        <td align=left width="40%" class="label_cell">
            <? echo GT($textMap,'IA.ALL_VENDORS'); ?>
        </td>
        <td>
            <div class="checkbox">
                <input type="checkbox" class="noborder" name=".vend" onclick="javascript:Do('V');"
                       onmouseover='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onfocus='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onblur='window.status="";return true;'
                       onmousemove='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onmouseout="window.status=''; return true;">
                <label></label>
            </div>
        </td>
    </tr>
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
    <tr>
        <td align=left width="40%" class="label_cell">
            <?= GT($textMap, 'IA.ALL_EMPLOYEES'); ?>
        </td>
        <td>
            <div class="checkbox">
                <input type="checkbox" class="noborder" name=".emp" onclick="javascript:Do('E');"
                       onmouseover='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onfocus='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onblur='window.status="";return true;'
                       onmousemove='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onmouseout="window.status=''; return true;">
                <label></label>
            </div>
        </td>
    </tr>
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
<? if ($removeCompanyClear != true) { ?>
    <tr>
        <td align=left width="40%" class="label_cell">
            <?= GT($textMap, 'IA.CLEAR_ALL_DATA_CLEAN_COMPANY'); ?>
        </td>
        <td>
            <div class="checkbox">
                <input type="checkbox" class="noborder" name=".comp" onclick="javascript:Do('A');"
                       onmouseover='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onfocus='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onblur='window.status="";return true;'
                       onmousemove='window.status="<? echo statusdisp('delete'); ?>"; return true;'
                       onmouseout="window.status=''; return true;">
                <label></label>
            </div>
        </td>
    </tr>
<? 
} ?>
</tr>
</table>
</td>
</tr>
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
<!--<tr><td colspan=3>&nbsp;</td></tr>-->
</table>
<? if ($isQuixote) {?>
            </div>
        </div>
    </div>
<? } ?>
<? htmlButtonFooter($gray_msg_text, [], $jsbuttons) ?>
<? htmlFooter($title); ?>
</form>
