<?php
/**
 * FILE: csvimport_sotaxschedule.cls
 * AUTHOR: <PERSON><PERSON><PERSON>
 * DESCRIPTION:
 *
 *    (C) 2022, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
import('csvimport_taxschedule');

class csvimport_sotaxschedule extends csvimport_taxschedule
{
    /**
     * csvimport_sotaxschedule constructor
     *
     * @param array $args
     */
    function __construct($args = [])
    {
        parent::__construct($args);
    }
}