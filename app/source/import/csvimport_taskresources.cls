<?php
/**
* class for managing Task Resources CSV Import
* 
* <AUTHOR> <<EMAIL>>
* @copyright 2000 Intacct Corporation All, Rights Reserved
* 
*/
require_once 'csvimport_base.cls';

/**
* Csvimport_Taskresources
* 	class for managing Task Resources CSV Import
*
*/
class Csvimport_Taskresources extends csvimport_base
{
    /**
     * csvimport_taskresources
     * 	defines merge data
     */
    function __construct()
    {
        include( 'csv_metadata_taskresources.inc' );

        parent::__construct('taskresources');

        /** @noinspection PhpUndefinedVariableInspection */
        $this->imeta = $imeta;
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imetagroom = $imetagroom;

        // current entity manager
        $gManagerFactory = Globals::$g->gManagerFactory;
        $this->entMgr = $gManagerFactory->getManager('taskresources');

        // merge imeta and imetagroom structures
        $this->MergeMeta();
    }

    /**
     * import
     * 	import every row and process
     *
     * @param array $rowarray row
     * 
     * @return bool indicates success or failure
     */
    function import($rowarray)
    {
        global $gErr;
        $values = $this->entMgr->FlatToStructured($rowarray);
        if(isset($values['TASKID']) && $values['TASKID'] != '' && isset($values['TASKNAME']) && $values['TASKNAME'] != ''  ){
            $gErr->addIAError(
                'PA-0207', __FILE__ . ':' . __LINE__,
                'Specify either a Task name or Task ID, not both.', [],
                $values['TASKID'] ."/". $values['TASKNAME'] . "' cannot give both '", ['TASKID' => $values['TASKID'], 'TASKNAME' => $values['TASKNAME']]
            );
            return false;
        }
        if(isset($values['ISFULLTIME'])){
            if(strtoupper($values['ISFULLTIME']) == 'T'){
                $values['ISFULLTIME'] = 'true';
            }elseif (strtoupper($values['ISFULLTIME']) == 'F'){
                $values['ISFULLTIME'] = 'false';
            }
        }
        if(isset($values['SOFTBOOK'])){
            if(strtoupper($values['SOFTBOOK']) == 'T'){
                $values['SOFTBOOK'] = 'true';
            }elseif (strtoupper($values['SOFTBOOK']) == 'F'){
                $values['SOFTBOOK'] = 'false';
            }
        }
        return $this->entMgr->add($values);
    }
}
