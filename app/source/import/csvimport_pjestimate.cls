<?php

/**
 * Class for the CSV Import of Project Estimate Transaction object
 *
 * <AUTHOR>
 * @copyright 2019 Intacct Corporation All, Rights Reserved
 */

/**
 * Class for the CSV Import of Project Estimate object
 */
class csvimport_pjestimate extends csvimport_base
{
    /**
     * @param array $args
     */
    public function __construct($args = [])
    {
        $entity = $args['entity'] ?? 'pjestimate';

        parent::__construct($entity);

        include_once 'csv_metadata_pjestimate.inc';

        // Initialize the class variables
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imeta = $imeta;
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imetagroom = $imetagroom;

        /** @noinspection PhpUndefinedVariableInspection */
        $this->initializeFieldPrefixMap($imetaFieldPrefixMap);
        /** @noinspection PhpUndefinedVariableInspection */
        $this->objkey = $imetakeys['objkey'];
        $this->csvkey = $imetakeys['csvkey'];

        // current entity manager
        $gManagerFactory = Globals::$g->gManagerFactory;
        $this->entMgr = $gManagerFactory->getManager($entity, true);

        // Merge the meta
        $this->MergeMeta();
    }

    /**
     * Override function for writing the error file
     *
     * @param array     $obj       the data
     * @param resource  $errfp     the file pointer
     * @param int       $noheaders true if no header else false
     */
    public function _WriteToImportErrorFile($obj, $errfp, $noheaders = 1)
    {
        $this->_writeUTF8Bom($errfp);
        foreach ( $obj as $values ) {
            $csvstring = ArrayToCSVString($values, [], $noheaders);
            fwrite($errfp, $csvstring);
        }
    }

    /**
     * Check to see if we should stop reading rows (true) or not (false)
     *
     * @param array $row
     *
     * @return bool
     */
    protected function checkStopReadCSVTransaction($row)
    {
        $sequenceid = GetPreferenceForProperty(Globals::$g->kPAid, 'PJESTIMATE_SEQUENCEID');
        if (!empty($sequenceid)) {
            if ($row['PROJECTID']) {
                return true;
            }
            else {
                return false;
            }
        }
        else {
            // Treat same project ID as part of previous
            return $this->checkIDChangeReadCSVTransaction($row, 'PJESTIMATEID');
        }
    }

    /**
     * Override function of the base to get file instead of row contents
     * @param array $obj
     *
     * @return bool
     */
    function _GetNextImportObject(&$obj)
    {
        return $this->_GetImportCSVTransaction($this->impFP, $this->objMap, $obj);
    }

    /**
     * @param array $rowarray
     *
     * @return bool
     *
     * @throws IAException
     */
    function Import($rowarray)
    {
        //logToFileDebug(__FILE__.':'.__LINE__);
        //dieFL();
        $ok = true;
        $gErr = Globals::$g->gErr;

        // Start a transaction. Everything has to succeed or we will rollback all of it.
        $source = 'csvimport_pjestimate::Import';
        XACT_BEGIN($source);

        // Build the data array into the format entity manager expect
        $pjestimate = array();
        $ok = $ok && $this->ConstructRecord($rowarray, $pjestimate);

        // upsert constructed pjestimate records..
        $pjestimate['PJESTIMATEENTRIES'] = $pjestimate['ENTRIES'];
        unset($pjestimate['ENTRIES']);

        list($pjestimateid) = explode('--', $pjestimate['PJESTIMATEID']);
        $existingRecordNo = null;

        if ( $pjestimateid != '' ) {
            $params = array(
                'selects' => array('RECORDNO', 'PJESTIMATEID'),
                'filters' => array(
                    array(
                        array('PJESTIMATEID', '=', $pjestimateid),
                    )
                )
            );
            $mylist = $this->entMgr->GetList($params);
            $existingRecordNo = $mylist[0]['RECORDNO'];
        }

        if ($existingRecordNo) {
            // a project estimate with this id already exists, so it's an error
            $gErr->addIAError(
                number: 'CRE-0900',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PJESTIMATEID' => $pjestimateid]
            );
            $ok = false;
        }

        $ok = $ok && $this->entMgr->add($pjestimate);

        if ( !$ok ) {

            $gErr->addIAError(
                number: 'CRE-0901',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['PJESTIMATE' => $pjestimate['PJESTIMATEID']]
            );
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;
    }

    /**
     * @param array $rowarray
     * @param array $pjestimate
     *
     * @return bool
     */
    function ConstructRecord(&$rowarray, &$pjestimate)
    {
        $ok = true;
        $pjestimate = array();

        foreach ($rowarray as $row) {
            $tmppjestimate = array();

            foreach ($this->imeta as $key => $value) {
                if ($this->imetagroom[$value]) {
                    $function = $this->imetagroom[$value];
                    $row[$value] = $this->$function($row[$value]);
                }
                $tmppjestimate[$key] = $row[$value];
            }

            $structured = $this->entMgr->FlatToStructured($tmppjestimate);

            //if ($this->entMgr->hasSequence()) {
            //    unset($tmppjestimate['PJESTIMATEID']);
            //}

            $items = INTACCTarray_merge($structured['PJESTIMATEENTRY'], $structured['PJESTIMATEENTRIES']);
            $structured['ENTRIES'] = array();

            // Get the header
            if ( $row['PJESTIMATEENTRY_LINENO'] == '1' ) {
                $pjestimate = $structured;
            }

            // Get the line item
            if ( !empty($row['PJESTIMATEENTRY_LINENO']) ) {
                $pjestimate['ENTRIES'][] = $items;
            }
        }

        return $ok;
    }

}
