<?
//=============================================================================
//
//	FILE:					cvs_metadata_initopenitems.inc
//	AUTHOR:					<PERSON><PERSON>
//	DESCRIPTION:			Meta Data for Init Open Items import
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================
$imeta = array(
        'HEADER.DATE'            => 'DATE',
        'HEADER.DOCNO'            => 'DOC_NO',
        'HEADER.TYPE'            => 'TYPE',
        'HEADER.AMOUNT'            => 'AMOUNT',
        'HEADER.PAYEE'            => 'PAYEE',
        'HEADER.DESCRIPTION'    => 'DESCRIPTION'
    );

    $imetagroom = array(
    );
    
    $imetakeys = array(
        'objkey'    => 'ACCTID',
        );

    $imetaFieldPrefixMap = array(
            'HEADER' => 'initopenitems',
    );
    