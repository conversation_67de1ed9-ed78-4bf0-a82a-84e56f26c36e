<?php
//=============================================================================
//
//  FILE:     ImportResponseBase.cls
//  AUTHOR:   <PERSON>andru <PERSON>
//  DESCRIPTION: Class to create import response (e.g. Excel error file)
//
//  (C)2000,2021 Intacct Corporation, All Rights Reserved
//
//  Intacct Corporation Proprietary Information.
//  This document contains trade secret data that belongs to Intacct
//  corporation and is protected by the copyright laws. Information herein
//  may not be used, copied or disclosed in whole or part without prior
//  written consent from Intacct Corporation.
//
//=============================================================================

/**
 * Class ImportResponseBase.cls
 *
 */
abstract class ImportResponseBase
{
    const EXTENSION_CSV = '.csv';

    public array $columns;
    public array $specs;
    
    /**
     * @var array $entityInfoMap list of fields with their structure
     */
    protected array $entityInfoMap;
    
    /**
     * @var array[][] $rows Rows of data.
     */
    protected array $rows;

    /**
     * @var array[] $columnErrors errors
     */
    protected array $columnErrors;

    /**
     * @var int $curRow Index to current row.
     */
    protected int $curRow = 0;

    /**
     * @var string[] $fieldsMap Map of field names to import column names.
     */
    protected array $fieldsMap;
    
    /**
     * @param array $fieldsMap
     */
    public function __construct(array $fieldsMap)
    {
        $this->fieldsMap = $fieldsMap;
        $this->rows = [];
        $this->columnErrors = [];
        $this->curRow = 0;
    }

    /**
     * Add header to response
     *
     * @param array $columns
     * @param array $specs
     */
    public function addHeader(array $columns, array $specs = [])
    {
        $this->columns = $columns;
        $this->specs = $specs;
    }
    
    /**
     * @param string $fieldName
     * @param string $err
     */
    public function addFieldError(string $fieldName, string $err)
    {
        if (isset($this->fieldsMap[$fieldName])) {
            $fieldName = $this->fieldsMap[$fieldName];
        }

        if ( ! isset($this->columnErrors[$this->curRow])) {
            $this->columnErrors[$this->curRow] = [];
        }

        if ( ! isset($this->columnErrors[$this->curRow][$fieldName])) {
            $this->columnErrors[$this->curRow][$fieldName] = [];
        }

        $this->columnErrors[$this->curRow][$fieldName][] = $err;
    }

    /**
     * @param array $row
     */
    public function addRow(array $row)
    {
        $this->curRow++;
        $this->rows[] = $row;
    }

    /**
     * @return string
     */
    public static function getFileName() : string
    {
        return self::getName() . self::getFileExt();
    }

    /**
     * the name used to store and retrieve the object from memory
     *
     * @return string
     */
    public static function getName() : string
    {
        return "err_" . GetMyCompany() . "_" . GetMyUserid();
    }

    /**
     * @return string
     */
    public static function getFileContentType() : string
    {
        return 'application/csv';
    }

    /**
     * @return string
     */
    public static function getFileExt() : string
    {
        return self::EXTENSION_CSV;
    }

}
