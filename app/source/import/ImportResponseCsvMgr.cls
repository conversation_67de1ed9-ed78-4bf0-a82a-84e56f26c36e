<?php
//=============================================================================
//
//  FILE:     ImportResponseCsvMgr.cls
//  AUTHOR:   Alexandru <PERSON>
//  DESCRIPTION: Class to create import response (e.g. Excel error file)
//
//  (C)2000,2021 Intacct Corporation, All Rights Reserved
//
//  Intacct Corporation Proprietary Information.
//  This document contains trade secret data that belongs to Intacct
//  corporation and is protected by the copyright laws. Information herein
//  may not be used, copied or disclosed in whole or part without prior
//  written consent from Intacct Corporation.
//
//=============================================================================

/**
 * Class ImportResponseCsvMgr.cls
 */
class ImportResponseCsvMgr extends ImportResponseBase implements ImportResponseMgr
{

    private CsvWriter $writeMgr;

    
    public function __construct(array $fieldsMap)
    {
        parent::__construct($fieldsMap);

        $this->writeMgr = new CsvWriter();
    }
    
    public function write() : string
    {
        return $this->writeMgr->write($this->rows, $this->columnErrors, $this->columns, $this->specs);
    }
    
    public function download(string $filename)
    {
        $filename .= self::getFileExt();
        $contentType = self::getFileContentType();
        
        header('Content-type: ' . $contentType);
        header('Content-Disposition: attachment; filename="' . $filename . '"');
    
        echo $this->write();
    }

    /**
     * @return string
     */
    public static function getFileContentType() : string
    {
        return 'application/csv';
    }

    /**
     * @return string
     */
    public static function getFileExt() : string
    {
        return self::EXTENSION_CSV;
    }
}
