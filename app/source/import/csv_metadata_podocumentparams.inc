<?
$imeta = array(
    // Properties
    'DOCID'                     => 'TEMPLATE_NAME',
    'DESCRIPTION'               => 'TEMPLATE_DESC',
    'DOCCLASS'                  => 'TEMPLATE_CLASS',
    'CATEGORY'                  => 'CATEGORY',
    'REPORTINGCATEGORY'         => 'REPORTINGCATEGORY',
    'STATUS'                    => 'STATUS',
    'SEQUENCE'                  => 'NUMBERING_SEQUENCE',
    'PRESERVE_SEQNUM'           => 'PRESERVE_SEQUENCE_NO',
    'INHERIT_SOURCE_DOCNO'      => 'INHERIT_SOURCE_DOC_NO',
    // Inventory Control
    'WARNONLOWQTY'              => 'WARN_ON_LOW_QUANTITY',
    'IN_OUT'                    => 'IN_OUT_INVENTORY',
    'WAREHOUSESELMETHOD'        => 'WAREHOUSE_SEL_METHOD',
    'DEFAULT_WAREHOUSE'         => 'DEFAULT_WAREHOUSE',
    // Accounting
    'UPDATES_GL'                => 'TRANSACTION_POSTING',
    'POSTTOGL'                  => 'ADDITIONAL_GL_ENTRY',
    // Accounting -> Transaction Subtotals
    'SHOW_TOTALS'               => 'SHOW_SUBTOTALS',
    'LINELEVELSIMPLETAX'        => 'ENABLELINELEVELSIMPLETAX',
    'SHOWEXPANDEDTOTALS'        => 'SHOW_EXPANDED_SUBTOTALS',
    'SUBTOTALTEMPLATE'          => 'SUBTOTAL_TEMPLATE_NAME',
    // Accounting -> User Overrides
    'ALLOWEDITCUSTVEND'         => 'ALLOW_EDIT_CUST_VEND',
    'DEPTOVERRIDE'              => 'DEPT_OVERRIDE',
    'LOCOVERRIDE'               => 'LOCATION_OVERRIDE',
    'ENABLEOVERRIDETAX'         => 'OVERRIDE_TAX',
    'OVERRIDE_PRICE'            => 'OVERRIDE_PRICE',
    'OVERRIDE_EXCH_RATE_TYPE'   => 'OVERRIDE_EXCHANGE_RATE',
    // Accounting -> Multi-currency
    'EXCHRATETYPES.NAME'        => 'EXCHANGE_RATE_TYPE',
    'DISPLAY_BASECURRENCY'      => 'DISPLAY_BASE_CURRENCY',
    // Workflow -> Transaction conversion
    'CONVTYPE'                  => 'PARTIAL_CONVERSION',
    // Workflow -> Pricing and terms
    'PRICELISTID'               => 'INITIAL_PRICELIST',
    'SPECIAL_PRICELISTID'       => 'SPECIAL_PRICELIST',
    'UPDATES_PRICELISTID'       => 'PRICELIST_TO_UPDATE',
    'CREDITLIMITCHECK'          => 'CREDIT_LIMIT_CHECK',
    'TERM_NAME'                 => 'DEFAULT_AR_TERM',
    // Print
    'XSLTEMPLATE'               => 'PRINTED_FORMAT',
    'FIXED_MESG'                => 'FIXED_MESSAGE',
    'CONTACTTITLE1'             => 'TITLE_ONE',
    'SHOWTITLE1'                => 'SHOW_TITLE_ONE',
    'ALLOWEDITBILLTO'           => 'ALLOW_EDIT_TITLE_ONE',
    'CONTACTTITLE2'             => 'TITLE_TWO',
    'SHOWTITLE2'                => 'SHOW_TITLE_TWO',
    'ALLOWEDITSHIPTO'           => 'ALLOW_EDIT_TITLE_TWO',
    'CONTACTTITLE3'             => 'TITLE_THREE',
    'SHOWTITLE3'                => 'SHOW_TITLE_THREE',
    'ALLOWEDITDELIVERTO'        => 'ALLOW_EDIT_TITLE_THREE',
    'ALLOW_ALLOCATIONS'         => 'ALLOW_ALLOCATIONS',

    // Inventory Control -> Totals
    'TOTALS.LINE_NO'            => 'TOTALS_LINE_NO',
    'TOTALS.TOTALID'            => 'TOTALS_INVENTORY_TOTAL',
    'TOTALS.Q_QV'               => 'TOTALS_MAINTAIN',
    'TOTALS.SIGN'               => 'TOTALS_ADD_SUBTRACT',

    // Accounting -> Transaction Subtotals
    'SUBTOTALS.LINE_NO'         => 'SUBTOTALS_LINE_NO',
    'SUBTOTALS.DESCRIPTION'     => 'SUBTOTALS_DESCRIPTION',
    'SUBTOTALS.DISC_CHARGE'     => 'SUBTOTALS_TYPE',
    'SUBTOTALS.BASELINE'        => 'SUBTOTALS_APPLIED_TO_LINE',
    'SUBTOTALS.AMT_PERC'        => 'SUBTOTALS_VALUE_TYPE',
    'SUBTOTALS.VALUE'           => 'SUBTOTALS_VALUE',
    'SUBTOTALS.DEBIT_CREDIT'    => 'SUBTOTALS_DEBIT_CREDIT',
    'SUBTOTALS.GLACCOUNT'       => 'SUBTOTALS_GL_ACCOUNT',
    'SUBTOTALS.GLOFFSETACCOUNT' => 'SUBTOTALS_GL_OFFSET_ACCOUNT',
    'SUBTOTALS.DEPARTMENT'      => 'SUBTOTALS_DEPARTMENT',
    'SUBTOTALS.LOCATION'        => 'SUBTOTALS_LOCATION',
    'SUBTOTALS.APPORTIONED'     => 'SUBTOTALS_IS_APPORTIONED',
    'SUBTOTALS.ISTAX'           => 'SUBTOTALS_IS_TAX',
    'SUBTOTALS.ISAVATAX'        => 'SUBTOTALS_IS_AVATAX',

    // Workflow -> Transaction conversion
    'RECALLS.LINE_NO'           => 'RECALLS_LINE_NO',
    'RECALLS.RECDOCPAR'         => 'RECALLS_CAN_BE_CREATED_FROM',

    // Posting Configuration -> COGS Account Mapping
    'INVGL_ACCOUNTS.LINE_NO'    => 'INVGL_ACCOUNTS_LINE_NO',
    'INVGL_ACCOUNTS.ITEM_GLGROUP' => 'INVGL_ITEM_GLGROUP',
    'INVGL_ACCOUNTS.WAREHOUSE'  => 'INVGL_WAREHOUSE',
    'INVGL_ACCOUNTS.ENT_GLGROUP' => 'INVGL_ENT_GLGROUP',
    'INVGL_ACCOUNTS.DEBIT_CREDIT'=> 'INVGL_DEBIT_CREDIT',
    'INVGL_ACCOUNTS.GLACCOUNT'  => 'INVGL_GLACCOUNT',
    'INVGL_ACCOUNTS.ISOFFSET'   => 'INVGL_IS_OFFSET',
    'INVGL_ACCOUNTS.DEPT'       => 'INVGL_DEPARTMENT',
    'INVGL_ACCOUNTS.LOCATION'   => 'INVGL_LOCATION',

    // Posting Configuration -> COGS Account Mapping
    'AR_ACCOUNTS.LINE_NO'       => 'AR_ACCOUNTS_LINE_NO',
    'AR_ACCOUNTS.ITEM_GLGROUP'  => 'AR_ACCOUNTS_ITEM_GLGROUP',
    'AR_ACCOUNTS.WAREHOUSE'     => 'AR_ACCOUNTS_WAREHOUSE',
    'AR_ACCOUNTS.ENT_GLGROUP'   => 'AR_ACCOUNTS_ENT_GLGROUP',
    'AR_ACCOUNTS.DEBIT_CREDIT'  => 'AR_ACCOUNTS_DEBIT_CREDIT',
    'AR_ACCOUNTS.GLACCOUNT'     => 'AR_ACCOUNTS_GLACCOUNT',
    'AR_ACCOUNTS.ISOFFSET'      => 'AR_ACCOUNTS_IS_OFFSET',
    'AR_ACCOUNTS.DEPT'          => 'AR_ACCOUNTS_DEPARTMENT',
    'AR_ACCOUNTS.LOCATION'      => 'AR_ACCOUNTS_LOCATION',

    // Entity Settings Tab
    'ENTITY_PROPS.LINE_NO'              => 'ENTITY_PROP_LINE_NO',
    'ENTITY_PROPS.ENTITY_NAME'          => 'ENTITY_PROP_ENTITY_NAME',
    'ENTITY_PROPS.SEQUENCE'             => 'ENTITY_PROP_DOC_SEQUENCE',
    'ENTITY_PROPS.PRESERVE_SEQNUM'      => 'ENTITY_PROP_PRESERVE_SEQNUM',
    'ENTITY_PROPS.INHERIT_SOURCE_DOCNO' => 'ENTITY_PROP_INHERIT_SOURCE_DOCNO',
    'ENTITY_PROPS.XSLTEMPLATE'          => 'ENTITY_PROP_XSLTEMPLATE',
    'ENTITY_PROPS.SUBTOTALTEMPLATE'     => 'ENTITY_PROP_SUBTOTAL_TEMPLATE_NAME',
    'ENTITY_PROPS.SHOWEXPANDEDTOTALS'   => 'ENTITY_PROPS_SHOW_EXPANDED_SUBTOTALS',
    'ENTITY_PROPS.ENABLEOVERRIDETAX'    => 'ENTITY_PROPS_OVERRIDE_TAX',
    'ENTITY_PROPS.LINELEVELSIMPLETAX'   => 'ENTITY_PROPS_ENABLE_LINELEVEL_SIMPLETAX',

    // Security Configuration
    'USERPERM'                  => 'USER_BASED_PERMISSIONS',
    'EDITTYPE'                  => 'EDIT_POLICY',
    'DELTYPE'                   => 'DELETE_POLICY',
    'CREATETYPE'                => 'CREATE_POLICY',
    'TD_CREATION_RULE'          => 'TD_CREATION_RULE',

    'UG_PERM.LINE_NO'           => 'UG_PERM_LINE_NO',
    'UG_PERM.ALLOWACCESS2'      => 'UG_PERM_ACCESS_RIGHT',
    'UG_PERM.PERMLISTMEMBER'    => 'UG_PERM_USER_GROUP_NAME',

    // Additional information
    'ENABLEADDINFOSCOPE'      =>  'ENABLEADDINFOSCOPE',
    'ENABLEADDINFOSCHEDULE'      =>  'ENABLEADDINFOSCHEDULE',
    'ENABLEADDINFOINTERNALREF'      =>  'ENABLEADDINFOINTERNALREF',
    'ENABLEADDINFOEXTERNALREF'      =>  'ENABLEADDINFOEXTERNALREF',
    'ENABLEADDINFOBOND'      =>  'ENABLEADDINFOBOND',
    'PRIMARYDOC'            =>  'PRIMARYDOC',
);

if (CRESetupManager::isAPRetainageEnabled()) {
    $imeta['ENABLE_RETAINAGE'] = 'ENABLE_RETAINAGE';
}

if ( TaxSetupManager::isVATEnabled() ) {
    $imeta['DISABLEVAT'] = 'DISABLEVAT';
    $imetaExtraColumns = array(
        array(  'OVERWRITE_IMETA'   =>    'T',
                'TITLE'             =>    'DISABLEVAT',
                'DESC'              =>    "Field Name: DISABLEVAT\n"
                                        . "UI Field Name: Don't capture tax\n"
                                        . "Type: Boolean\n"
                                        . "Valid Values: true, false\n"
                                        . "Dependencies: Available when transaction posting is posted to GL\n"
                                        . "Required: No\n"
                                        . "Editable: Yes"),
    );
}
