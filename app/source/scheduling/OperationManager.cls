<?
class OperationManager extends EntityManager
{
    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "Operation::Add";
        global $gErr;

        $ok = $this->_QM->beginTrx($source);

        // Set record#
        $nextId = $this->GetNextRecordKey();
        $ok = $ok && isset($nextId);
        $values['RECORDNO'] = $values[':record#'] = $nextId;

        // Set default fields
        $values[':user#']        = GetMyUserid();
        $values['WHENCREATED'] = GetCurrentDate();

        $ok = $ok &&$this->_Translate($values);

        if ( isset($values['BODYDATA']) ) {
            $values['BODYDATA'] = databaseStringCompress($values['BODYDATA']);
        }

        $ok = $ok && parent::regularAdd($values);
        
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = _("Could not create Operation record!");
            $gErr->addError('SCHED-0001', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $source = "Operation::Set";
        global $gErr;

        $ok = $this->_QM->beginTrx($source);
        $ok = $ok && $this->_Translate($values);

        if ( isset($values['BODYDATA']) ) {
            $values['BODYDATA'] = databaseStringCompress($values['BODYDATA']);
        }

        $ok = $ok && parent::regularSet($values);
        
        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            $msg = _("Could not set Operation record!");
            $gErr->addError('SCHED-0001', __FILE__ . ':' . __LINE__, $msg);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * @param string        $ID
     * @param string[]|null $fields
     *
     * @return array|false
     */
    function get($ID, $fields=null)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $source = "Operation::Get";

        $operation = parent::get($ID);
        $operation['BODYLINK'] = $ID;

        if ( isset($operation['BODYDATA']) ) {
            $operation['BODYDATA'] = databaseStringUncompress($operation['BODYDATA']);
        }
        $operation['BODYDATA'] = DoUnserialize($operation['BODYDATA']);

        return $operation;
    }

    /**
     * @param string $recordID
     *
     * @return array|null
     */
    function GetByRecordID($recordID)
    {
        // Instead of get by name, which is non-indexed, lets get by record#
        $queryParams = array(
            'filters'    => array( array (
                                    array ('RECORDNO', '=', $recordID),
                            )),
        );
                            
        // Get the operation body
        $operation = parent::GetList($queryParams);    
        if ( count($operation) > 0 ) {
            $operation = $operation[0];        
    
            $operation['BODYLINK'] = $operation['NAME'];

            if ( isset($operation['BODYDATA']) ) {
                $operation['BODYDATA'] = databaseStringUncompress($operation['BODYDATA']);
            }
            $operation['BODYDATA'] = DoUnserialize($operation['BODYDATA']);
    
            return $operation;
        }
        else {
            return null; 
        }
    }

    
    /**
     * Translates VIDs to foreign keys
     *
     * @param array $values the object
     *
     * @return bool
     */
    function _Translate(&$values) 
    {
        global $gManagerFactory;

        if (!empty($values['USERNO'])) {
            $opMgr = $gManagerFactory->getManager('userinfo');
            $op = $opMgr->GetRaw($values['USERNO']);
            $values[':user#'] = ($op[0]['RECORD#'])?: GetMyUserid();
        } else {
            $values[':user#'] = GetMyUserid();
        }

        if (is_array($values['BODYDATA'])) {
            $values['BODYDATA'] = serialize($values['BODYDATA']);
        } elseif (!$values['BODYDATA']) {
            $values['BODYDATA'] = 'No values were stored.';
        } 
        
        return true;
    }

    /**
     * @param string        $operation  api operation to be validated
     * @param array|string  $values     object data
     * @param int           $checkOpId  the successful checked op id
     *
     * @return bool
     */
    public function API_ValidateWithoutError($operation, &$values = null, &$checkOpId=null)
    {
        // only unrestricted admin users may view these records
        return IsUserAdmin() && !IsRestrictedUser();

    }

    /**
     * Disable auditing for Operation
     *
     * @return bool
     */
    public function IsAuditEnabled()
    {
        return false;
    }

    /**
     * @param int|string|array $_id
     *
     * @return string[][]|bool
     */
    function GetRaw($_id)
    {
        $result = parent::GetRaw($_id);

        if ( isset($result[0]['BODYDATA']) ) {
            $result[0]['BODYDATA'] = databaseStringUncompress($result[0]['BODYDATA']);
        }

        return $result;
    }

    /**
     * Return a list of entities
     *
     * @param array $params    a structure used to build the custom query
     * @param bool  $_crosscny if true do not add the var.cny# = ... code
     * @param bool  $nocount   don't generate a count column
     *
     * @return array[] $newResult  result of query
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        $results = parent::GetList($params, $_crosscny, $nocount);

        foreach ( $results as $key => $res ) {
            if ( isset($res['BODYDATA']) ) {
                $results[$key]['BODYDATA'] = databaseStringUncompress($res['BODYDATA']);
            }
        }

        return $results;
    }

}

