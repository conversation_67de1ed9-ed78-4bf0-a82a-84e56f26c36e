<?php

class LoanStatementRunSummaryEditor extends RunObjectSummaryEditor
{

    public function __construct($params = [])
    {
        parent::__construct($params);
        $this->additionalTokens[] = 'IA.LOCATION_NAME';
        $this->additionalTokens[] = 'IA.START_DATE';
        $this->additionalTokens[] = 'IA.END_DATE';
        $this->additionalTokens[] = 'IA.LOAN_ACCOUNT_NAME';

    }

    /**
     * Sets derived field values
     *
     * @param array $obj Array of attributes
     *
     * @return bool
     */

    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);

        $this->setTitle(I18N::getSingleToken('IA.BULK_ACTION_RUNS', [
            [ "name" => 'RUN_ID', "value" => $obj['RECORDNO'] ],
        ]));

        return true;
    }

    protected function getFormTokens() : array
    {
        $this->textTokens[] = 'IA.LOCATION_NAME';
        $this->textTokens[] = 'IA.START_DATE';
        $this->textTokens[] = 'IA.END_DATE';
        $this->textTokens[] = 'IA.LOAN_ACCOUNT_NAME';
        return parent::getFormTokens();
    }
}