<?php

class LoanStatementProcessingConsumerHelper extends RunConsumerHelper
{

    /**
     * @inheritDoc
     */
    public function execute()
    {
        $loanStatementRunKey = $this->getRunEntryData('HEADERKEY');
        $loanAccountKey = $this->getRunEntryData('DETAILKEY');
        SNLIntegrationUtils::prepareStatementWithInterest($loanAccountKey, $loanStatementRunKey);
        return true;
    }

    public function getErrorMessageForUser(array $runEntry, $errors)
    {
        if (is_array($errors)) {
            $separator = '<br/>';
            $retVal = '';
            foreach ($errors as $error) {
                if (!preg_match('/^[A-Z]+-[0-9]+$/', $error)) {
                    if ($retVal) {
                        $retVal .= $separator;
                    }
                    $retVal .= $error;
                }
            }
        } else {
            $retVal = parent::getErrorMessageForUser($runEntry, $errors);
        }
        return $retVal;
    }

    /**
     * @return string
     */
    public function getRunEntriesUISection()
    {
        return 'loanstatementrundetailentries';
    }

    public function getDrillDownURLForRunEntry($runEntry, $module, $isAPI = false)
    {
        return $runEntry['ACTIONSUMMARY'] ?? '';
    }
    public function onRunCompletion()
    {
        // call finalize statement api to ds side
        $loanStatementRunKey = $this->getParameterData("LOAN_STATEMENT_RUN_KEY");
        SNLIntegrationUtils::finalizeStatementRun($loanStatementRunKey);
        return parent::onRunCompletion();
    }

    /**
     * @override
     *
     * @param int  $runKey
     *
     * @return array
     */
    public function getMoreData($runKey): array
    {
        $querySpec = "SELECT cr.cny#, cr.record#, cr.type, cr.executionmode, cr.state, cr.createdby,cre.record#,
                                cre.headerkey, loc.NAME LOCATION, lnr.STARTDATE STARTDATE, lnr.ENDDATE ENDDATE, lacct.NAME LOAN_ACCOUNT
                        FROM contractrun cr
                            INNER JOIN contractrungroup crg ON (crg.runkey = cr.record# AND crg.cny# = cr.cny#)
                            INNER JOIN contractrunentry cre ON (cre.rungroupkey = crg.record# AND cre.cny# = crg.cny#)
                            inner join SNLLOANSTATEMENTRUN lnr on (cre.HEADERKEY = lnr.RECORD# and cre.cny# = lnr.cny#)
                            inner join SNLLOANACCOUNT lacct on (lacct.RECORD# = cre.DETAILKEY and lacct.cny# = cre.cny#)
                            inner join LOCATION loc on (lacct.LOCATIONKEY = loc.RECORD# and lacct.CNY# = loc.CNY#)
                        WHERE cr.record# = :1 AND cr.cny# = :2 ORDER BY cr.cny#, cr.whenmodified";
        $queryWithParams = [$querySpec, $runKey, GetMyCompany()];
        $result = QueryResult($queryWithParams);
        $runDetailMap = resultSetToMapByColumn($result, 'RECORD#');

        $this->setMoreData($runDetailMap);
        $result = $runDetailMap;

        return $result;
    }

    /**
     * @override
     * @param array     $runEntry
     * @param string    $module
     * @param int       $runKey
     * @param bool      $isAPI
     * @return void
     */
    public function setMoreDetailDataPerEntry(&$runEntry, $module, $runKey, $isAPI = false)
    {
        parent::setMoreDetailDataPerEntry($runEntry, $module, $runKey, $isAPI);
        $runDetailMap = $this->getMoreData($runKey);

        $rec = $runDetailMap[$runEntry['RECORDNO']];
        $runEntry['LOCATION'] = $rec['LOCATION'];
        $runEntry['STARTDATE'] = $rec['STARTDATE'];
        $runEntry['ENDDATE'] = $rec['ENDDATE'];
        $runEntry['LOAN_ACCOUNT'] = $rec['LOAN_ACCOUNT'];
    }
}