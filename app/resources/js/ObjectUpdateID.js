/**
 * Created by simam on 8/9/14.
 */
/*
 FILE:		ObjectUpdateID.js
 DESCRIPTION:	Functionality of Handling the screen in Object Update ID Tool

 @copyright 1999-2014 Intacct Corporation

 This document contains trade secret data that belongs to Intacct
 corporation and is protected by the copyright laws. Information
 herein may not be used, copied or disclosed in whole or part
 without prior written consent from Intacct Corporation.
 */

function HomeScreen(actionName) {
    var action = document.getElementById('action').value = actionName;
    var index = document.getElementById('index').value = actionName;
    var commit = document.getElementById('commit').value = actionName;
    return true;
}

function setAction(actionName, cny) {
    var action = document.getElementById('actionname').value = actionName;
    var r = document.getElementById('cny').value = cny;
    return true;
}

function getRadioAction(radioaction) {
    var matchrecord = document.getElementById('mid').value = radioaction;
    var matchid = document.getElementById('mrecord').value = radioaction;
    jq('input').click(function (e) {
        if (e.key) {
            jq(this).prop('checked', false);
        }
    });
    return true;
}
function checkObject(objectAction) {
    if (objectAction == 'BaseAccount') {
        window.document.getElementById('.BaseAccount').style.visibility = 'visible'
    } else {
        window.document.getElementById('.BaseAccount').style.visibility = 'hidden'
    }
    return true;
}

function removeSpace(value) {
    var checked = document.getElementById('space').checked;
    if(checked == true) {
        alert("All double space will get remove for selected object.");
        document.getElementById('mrecord').disabled = true;
        document.getElementById('mid').disabled = true;
        document.getElementById('csv').disabled = true;
    } else {
        document.getElementById('mrecord').disabled = false;
        document.getElementById('mid').disabled = false;
        document.getElementById('csv').disabled = false;
    }
}
function showEmailBox(value) {
    var checked = document.getElementById('.emailNotify').checked;
    if(checked == true) {
        window.document.getElementById('.showEmail').style.visibility = 'visible';
    } else {
        window.document.getElementById('.showEmail').style.visibility = 'hidden'
    }
}

function validateEmail() {
    var email = document.getElementById('.emailid').value;
    var emailFormat = /^\w+@\w+(\.\w{2,3})+$/;
    if(emailFormat.test(email)) {
        return true;
    } else {
        alert("Email id format is not correct, Format <NAME_EMAIL>");
        document.getElementById('.emailid').value = '';
        return false;
    }
}