function showHideMembers(grpType) 
{
    window.setTimeout(function() {
    var needfocus = 'select_list';
    if (grpType == null) {
        needfocus = 'id_name';
    }
    groupField = window.editor.findComponents('GROUPTYPE', 'Field')[0];
    groupFieldValue = groupField.getValue();

    var grid = window.editor.findComponents('MEMBERS', 'Grid'); 
    var topObj = window.view.findComponentsById('TopMatch');
    if (groupFieldValue == 'SPECIFIC') {
        grid[0].show();
        topObj[0].showHide(false);
    } else if (groupFieldValue == 'TOP' || groupFieldValue == 'BOTTOM') {
        var labelObj = window.editor.findComponents('PREFIX_TOPMATCH');
        var label = 'Show bottom';
        if (groupFieldValue == 'TOP') {
            label = 'Show top';
        }
        labelObj[0].setValue(label);
        
        grid[0].hide();
        topObj[0].showHide(true);
        if (!needfocus !== 'id_name') {
            topObj[0].setFocus();
            needfocus = false;
        }
    } else {
        grid[0].hide(true);
        topObj[0].showHide(true);
    }
    
    if (needfocus === 'id_name') {
        var idFld = window.editor.findComponents('ID', 'Field');
        if (!idFld[0].readonly) {
            idFld[0].setFocus();            
        } else {
            var nameFld = window.editor.findComponents('NAME', 'Field');    
            nameFld[0].setFocus();
        }
    } else if (needfocus === 'select_list') {
        groupField.setFocus();
    }
    }, 0);
    return true;
}

function changeCondition(obj) 
{
    conditionTypeField = window.editor.findComponents('CONDITIONTYPE', 'Field')[0];

    condObj = window.editor.findComponents('CONDITION', 'Field');    
    if (conditionTypeField.getValue() == 'EXPRESSION') {
        condObj[0].updateProperty('disabled', false, true);
        if (condObj[0].getValue() == '') {
            condObj[0].setFocus();
        }
        
    } else {
        condObj[0].setValue('');
        condObj[0].updateProperty('disabled', true, true);
        conditionTypeField.setFocus();
    }
    
    return true;
}

function changeGridFldTypes(_entity) 
{    
    grid = window.editor.findComponents('MEMBERFILTERS', 'Grid'); 
    for (i = 0; i < grid[0].currentChildren.length; i++) {
        var fieldFld = grid[0].findLineComponent('FIELD', i, 'Field');
        var valueFld = grid[0].findLineComponent('VALUE', i, 'Field');        
        var val = valueFld.getValue();
        if ( fieldFld.getValue()) {
            changeFldType(fieldFld, _entity, val);
        }        
    }
    
    return true;
}

function changeFldType(obj, _entity, objval)
{
    var grid = obj.getGrid();
    var lineNo = obj.getLineNo();
    
    var callParams =  {
        'function': 'q_getFieldType',
        'entity': _entity, 
        'field': obj.getValue()
    };
    
    var updateArgs = {grid: grid, lineNo: lineNo, fldobj: obj, objval: objval} ;
    var qrequestBatch = window.view.getQRequestBatch();    
    qrequestBatch.addRequest('QRequest', callParams, respProcesser_changeField, updateArgs, true);
    qrequestBatch = null;    
    
}

function respProcesser_changeField( params, m_response ) 
{
    const EQUALS= GT( 'IA.EQUALS');
    const NOT_EQUAL_TO = GT( 'IA.NOT_EQUAL_TO');
    const GREATER_THAN = GT( 'IA.GREATER_THAN');
    const LESS_THAN =GT('IA.LESS_THAN');
    const IS_ONE_OF =GT( 'IA.IS_ONE_OF');
    const IS_NOT_ONE_OF =GT('IA.IS_NOT_ONE_OF');
    const IS_EMPTY = GT('IA.IS_EMPTY');
    const IS_NOT_EMPTY= GT( 'IA.IS_NOT_EMPTY');
    const CONTAINS = GT( 'IA.CONTAINS');
    const IS_CURRENT_USER= GT('IA.IS_CURRENT_USER');

    // If there is no result, no need to continue
    if ( !m_response ) {
        return;
    }   
    
    // refresh value field
    var contentRes = m_response.getElementsByTagName('Content');    
    var fldRes = m_response.getElementsByTagName('Field');    
    var valueFld = params.grid.findLineComponent('VALUE', params.lineNo, 'Field');
    var operFld = params.grid.findLineComponent('OPERATOR', params.lineNo, 'Field');
    
    // default values    
    if (view.state != 'showview') {
        operFld.updateProperty('disabled', false, true);
        valueFld.updateProperty('readonly', false, true); 
        valueFld.updateProperty('disabled', false, true);    
    }
    
    var opers;
    
    if ( contentRes.length ) {
        if (fldRes.item(i).getAttribute("supportCU")) {
            opers = [EQUALS, NOT_EQUAL_TO, GREATER_THAN, LESS_THAN,
                              IS_CURRENT_USER, IS_EMPTY, IS_NOT_EMPTY, CONTAINS];
        } else {
            opers = [EQUALS, NOT_EQUAL_TO, GREATER_THAN, LESS_THAN,
                              IS_EMPTY, IS_NOT_EMPTY, CONTAINS];
        }
        var entity = contentRes.item(i).getAttribute("entity");        
        var dateCol = contentRes.item(i).getAttribute("date");
        
        if (entity) {
            var pickentity = contentRes.item(i).getAttribute("pickentity");
            var pickurl = contentRes.item(i).getAttribute("pickurl");
            
            valueFld.updateProperty(['type', 'type'], 'ptr');           
            valueFld.updateProperty(['type', 'entity'], entity);
            if (!pickentity) {
                pickentity = entity;
            }
            valueFld.updateProperty(['type', 'pickentity'], pickentity); 
            valueFld.updateProperty(['type', 'pick_url'], pickurl); 
            
            var restrictRes = m_response.getElementsByTagName('Restrict');  
            valueFld.type.restrict = new Array();
            for (var i = 0; i < restrictRes.length; i++) {
                valueFld.type.restrict[i] = new Object();
                valueFld.type.restrict[i].pickField = restrictRes.item(i).getAttribute("pickField");
                valueFld.type.restrict[i].field = restrictRes.item(i).getAttribute("field");
                valueFld.type.restrict[i].value = restrictRes.item(i).getAttribute("value");
            }
            
            valueFld.listAllowed = true;
            valueFld.setValue(params.objval);
            valueFld.uiControl = 'ControlPtr';                 
        } else if (dateCol) {
            valueFld.updateProperty(['type', 'type'], 'date');
            valueFld.setValue(params.objval);
            valueFld.uiControl = 'ControlDate';                           
        } else {
            var validlabels = new Array();
            var validvalues = new Array();

            opers = new Array(EQUALS, NOT_EQUAL_TO, GREATER_THAN, LESS_THAN,
                              IS_EMPTY, IS_NOT_EMPTY);
            
            for (var i = 0; i < contentRes.length; i++) {                        
                validvalues[i] = contentRes.item(i).getAttribute("value");
                validlabels[i] = contentRes.item(i).getAttribute("label");
            }
            
            // for check box show only equals
            if ( fldRes.item(0).getAttribute("type") == 'boolean' ) {
                opers = new Array(EQUALS);
                operFld.setValue(EQUALS);
                operFld.updateProperty('disabled', true, true);               
            }
            valueFld.updateProperty(['type', 'ptype'], "enum");
            valueFld.updateProperty(['type', 'type'], "text");
            valueFld.updateProperty(['type', 'validlabels'], validlabels);
            valueFld.updateProperty(['type', 'validvalues'], validvalues);
            var objval = (params.objval) ? params.objval : validvalues[0];
            valueFld.setValue(objval);
            valueFld.uiControl = "ControlEnum";
        }
    } else if ( fldRes.item(0).getAttribute("type") == 'percent' ) {        

        opers = [EQUALS, NOT_EQUAL_TO, GREATER_THAN, LESS_THAN,
                          IS_ONE_OF, IS_NOT_ONE_OF, IS_EMPTY, IS_NOT_EMPTY];
        
        valueFld.uiControl = 'ControlPercent';        
        valueFld.updateProperty(['type', 'type'], 'percent');
        valueFld.updateProperty(['type', 'ptype'], 'percent');        
        valueFld.setValue(params.objval);
    } else {
        valueFld.uiControl = 'ControlText';        
        valueFld.setValue(params.objval);         
        
        opers = [EQUALS, NOT_EQUAL_TO, GREATER_THAN, LESS_THAN,
                          IS_ONE_OF, IS_NOT_ONE_OF, IS_EMPTY, IS_NOT_EMPTY];
            
        // for non numeric fields add 'contains'
        if ( fldRes.item(0).getAttribute("type") != 'currency' && fldRes.item(0).getAttribute("type") != 'decimal') {
            opers[opers.length] = CONTAINS;
        }
    }
    
    operFld.updateProperty(['type', 'validvalues'], opers, true);
    valueFld.renderer = null;
    valueFld.redraw();
    
    // check the operator
    changeOperator(operFld);
    
    
}

function changeOperator(obj)
{
    const IS_EMPTY = GT('IA.IS_EMPTY');
    const IS_NOT_EMPTY= GT( 'IA.IS_NOT_EMPTY');
    const IS_CURRENT_USER= GT('IA.IS_CURRENT_USER');

    var grid = obj.getGrid();
    var lineNo = obj.getLineNo();
    var valueFld = grid.findLineComponent('VALUE', lineNo, 'Field');
    
    if (obj.getValue() == IS_EMPTY || obj.getValue() == IS_NOT_EMPTY || obj.getValue() == IS_CURRENT_USER) {
        valueFld.setValue(null);
        valueFld.updateProperty('readonly', true, true); 
        valueFld.updateProperty('disabled', true, true);
    } else {
        valueFld.updateProperty('readonly', false, true); 
        valueFld.updateProperty('disabled', false, true);
    }
}

function autoPopulateName(txt) 
{    
    var nameFld = window.view.findComponents('NAME');
    if ( nameFld && nameFld[0].getValue() == '' ) {
        nameFld[0].setValue(txt);
    }    
}