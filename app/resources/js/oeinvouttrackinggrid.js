/***********************************************************************************************************************
 * Tracking attributes utility methods
 **********************************************************************************************************************/

    // Constants and bitmasks for tracking attribute
const NONEMASK = 0x0000;
const SERIALMASK = 0x0001;
const LOTMASK = 0x0010;
const BINMASK = 0x0100;
const EXPIRATIONMASK = 0x1000;

function TrackingConfig(tracking) {
    // Display labels associated with tracking attributes
    this.labelMap = { Serial: SERIALMASK, Lot: LOTMASK, Bin: BINMASK, Exp: EXPIRATIONMASK };
    this.masks = {
        SERIAL_MASK: { mask: 0x0001, label: 'Serial' }, LOT_MASK: { mask: 0x0010, label: 'Lot' },
        BIN_MASK: { mask: 0x0100, label: 'Bin' }, EXPIRATION_MASK: { mask: 0x1000, label: 'Exp' }
    };
    this.tracking = tracking;
}

TrackingConfig.prototype = {
    constructor: TrackingConfig
};

// Additional tracking bitmasks may be added to current
TrackingConfig.prototype.set = function(tracking) {
    this.tracking |= tracking;
};

// Additional tracking bitmasks may be added to current
TrackingConfig.prototype.setTrackingData = function(trackingData) {
    this.tracking |= (trackingData.enablesno === 'T' ? SERIALMASK : NONEMASK);
    this.tracking |= (trackingData.enablelot === 'T' ? LOTMASK : NONEMASK);
    this.tracking |= (trackingData.enablebin === 'T' ? BINMASK : NONEMASK);
    this.tracking |= (trackingData.enableexpiration === 'T' ? EXPIRATIONMASK : NONEMASK);
};

// Get current tracking combination
TrackingConfig.prototype.get = function() {
    return this.tracking;
};

// Check if all masks have been set
TrackingConfig.prototype.areAllSet = function() {
    return (this.tracking === (SERIALMASK | LOTMASK | BINMASK | EXPIRATIONMASK));
};

// Get combination display label for hyperlink column; only legal combinations listed--others are unused.
TrackingConfig.prototype.getLabel = function() {
    var labelStr = '';
    switch (this.tracking) {
        case SERIALMASK:
            labelStr = GT('IA.SERIAL');
            break;
        case SERIALMASK|EXPIRATIONMASK:
            labelStr = GT('IA.SERIAL_EXPIRATION');
            break;
        case LOTMASK:
            labelStr = GT('IA.LOT');
            break;
        case LOTMASK|EXPIRATIONMASK:
            labelStr = GT('IA.LOT_EXPIRATION');
            break;
        case BINMASK:
            labelStr = GT('IA.BIN');
            break;
        case SERIALMASK|LOTMASK:
            labelStr = GT('IA.SERIAL_LOT');
            break;
        case SERIALMASK|LOTMASK|EXPIRATIONMASK:
            labelStr = GT('IA.SERIAL_LOT_EXPIRATION');
            break;
        case SERIALMASK|BINMASK:
            labelStr = GT('IA.SERIAL_BIN');
            break;
        case SERIALMASK|BINMASK|EXPIRATIONMASK:
            labelStr = GT('IA.SERIAL_BIN_EXPIRATION');
            break;
        case LOTMASK|BINMASK:
            labelStr = GT('IA.LOT_BIN');
            break;
        case LOTMASK|BINMASK|EXPIRATIONMASK:
            labelStr = GT('IA.LOT_BIN_EXPIRATION');
            break;
        case SERIALMASK|LOTMASK|BINMASK:
            labelStr = GT('IA.SERIAL_LOT_BIN');
            break;
        case SERIALMASK|LOTMASK|BINMASK|EXPIRATIONMASK:
            labelStr = GT('IA.SERIAL_LOT_BIN_EXPIRATION');
            break;
    }
    return labelStr;
};

/** End of Tracking attributes utility methods **/

/***********************************************************************************************************************
 * OE inventory out tracking grid object
 *
 * This object supports the tracking grid on an OE transaction with inventory going out of company.
 * Some requirements are needed in order to use this gric
 * 1. A parent grid would instantantiate this grid and the parent grid contains an ITEMID path.
 * 2. This grid contains SERIALNO, LOTNO, BINID, EXPIRATION, and QUANTITYAVAIL columns
 *    (look at SKCOMPONENTTRACKINGGRID in stkitdocument_form.xml or TRACKING in iworder_pi_floatpage.xml).
 * 3. At least a qty field with the path of your choice but your need to return it in getQtyFieldPaths().
 * 4. All methods with label "//Needs to be overridden" need to be overridden in your subclass grid object.
 * 5. This oeinvouttrackinggrid.js needs to be included in your editor getJavaScriptFileNames() method.
 * 6. Handles the change events for SERIALNO, LOTNO, BINID, EXPIRATION, and any qty paths, then call
 *    serialNoChanged, lotNoChanged, binChanged, expirationChanged, and qtyFldChanged methods respectively.
 *
 *
 **********************************************************************************************************************/

function OEInvOutTrackingGrid() {
}

OEInvOutTrackingGrid.inheritsFrom(Grid);

OEInvOutTrackingGrid.prototype.getTotalQtyNeededFromParent = function()
{
    return undefined;
}

OEInvOutTrackingGrid.prototype.getTrackingRows = function()
{
    //Needs to be overridden
}

OEInvOutTrackingGrid.prototype.getItemPath = function()
{
    //Needs to be overridden
}

OEInvOutTrackingGrid.prototype.getQtyFieldPaths = function()
{
    //Needs to be overridden
}

OEInvOutTrackingGrid.prototype.getWarehouseKey = function()
{
    //Needs to be overridden
}

OEInvOutTrackingGrid.prototype.getTotalQuantityGreaterThanEntryQuantityMessage = function()
{
    //Should be overridden to specialize the error message
    return GT('IA.OE_TOTAL_QTY_GREATER_THAN_ENTRY_QTY');
}

OEInvOutTrackingGrid.prototype.getTrackQuantityGreaterThanEntryQuantityMessage = function()
{
    //Should be overridden to specialize the error message
    return GT('IA.OE_TRACK_QTY_GREATER_THAN_ENTRY_QTY');
}

OEInvOutTrackingGrid.prototype.getTrackQuantityGreaterThanAvailableQuantityMessage = function()
{
    //Should be overridden to specialize the error message
    return GT('IA.OE_TRACK_QTY_GREATER_THAN_AVAILABLE_QTY');
}

OEInvOutTrackingGrid.prototype.getItemId = function()
{
    var itemid = this.parentValue[this.getItemPath()].split('--')[0];
    return itemid;
}

function getMatchingTrackingRow(trackingRows, rowDataToMatch)
{
    if (trackingRows) {
        for ( var i = 0; i < trackingRows.length; i ++ ) {
            if ( (rowDataToMatch.SERIALNO == undefined || rowDataToMatch.SERIALNO == '' || rowDataToMatch.SERIALNO == trackingRows[i].serialno)
                 && (rowDataToMatch.LOTNO == undefined || rowDataToMatch.LOTNO == '' || rowDataToMatch.LOTNO == trackingRows[i].lotno)
                 && (rowDataToMatch.BINID == undefined || rowDataToMatch.BINID == '' || rowDataToMatch.BINID == trackingRows[i].binid)
                 && (rowDataToMatch.EXPIRATION == undefined || rowDataToMatch.EXPIRATION == '' || rowDataToMatch.EXPIRATION == trackingRows[i].expiration) ) {
                return trackingRows[i];
            }
        }
    }
    return undefined;
}

OEInvOutTrackingGrid.prototype.initialize = function(parentContext, parentValue)
{
    Grid.prototype.initialize.call(this, parentContext, parentValue);

    if (this.value) {
        var trackingRows = this.getTrackingRows();
        if (trackingRows) {
            this.value.forEach(function(gridRow) {
                if (gridRow.TRACK_QUANTITY) {
                    var trackingRow = window.getMatchingTrackingRow(trackingRows, gridRow);
                    if (trackingRow) {
                        gridRow.QUANTITYAVAIL = window.getQtyAvail(trackingRow);
                    }
                }
            });
        }
    }
}

//Override to detect that the user click on a row of the tracking grid
OEInvOutTrackingGrid.prototype.showLineDetailsByElement = function(element)
{
    //Detect that the user just selected a row on tracking grid
    var needToExeAfter = false;
    var line;
    if (!this.lineDetails || !this.lineDetails.isVisible()) {
        line = this.findBodyLine(element);
        if (line && line.gridRow !== undefined && this.selectedLine != line) {
            this.beforeLineSelection(line.gridRow); //User just click on a row on the tracking grid
            needToExeAfter = true;
        }
    }
    var retVal = Grid.prototype.showLineDetailsByElement.call(this, element);

    if (needToExeAfter) {
        this.afterLineSelection(line.gridRow);
    }

    return retVal;
}

OEInvOutTrackingGrid.prototype.beforeLineSelection = function(lineNo)
{
    //Set previous values on line
    this.preservePrevValueOnLineFields(lineNo);

    //Restrict the line pickers to available rows in tracking data
    this.restrictLinePickers(lineNo);
}

OEInvOutTrackingGrid.prototype.afterLineSelection = function(lineNo)
{
    //Enable/disable the line base on availability
    this.enableDisableLineQtyFields(lineNo);
}

OEInvOutTrackingGrid.prototype.deleteRow = function (rowIndex)
{
    var itemTrackingRows = this.getTrackingRows();
    var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, this.value[rowIndex], false);
    if (matchedTrackingRows.length == 1) {
        var qtyonrow = this.getQtyOnLine(rowIndex);
        matchedTrackingRows[0].qtyused -= qtyonrow;

        //Need to update qty on all rows
        this.updateLinesAvailQtyField(matchedTrackingRows[0]);
    }

    Grid.prototype.deleteRow.call(this, rowIndex);

    //Update the parent grid linedetails qty field as we just deleted a row
    if (matchedTrackingRows.length == 1) {
        this.updateParentGridLineDetailsQty();
    }
}

OEInvOutTrackingGrid.prototype.preservePrevValueOnLineFields = function(lineNo)
{
    this.preservePrevValueOnLineField(lineNo, 'SERIALNO');
    this.preservePrevValueOnLineField(lineNo, 'LOTNO');
    this.preservePrevValueOnLineField(lineNo, 'BINID');
    this.preservePrevValueOnLineField(lineNo, 'EXPIRATION');
    var qtyFieldPaths = this.getQtyFieldPaths();
    for (var i = 0; i < qtyFieldPaths.length; i++) {
        this.preservePrevValueOnLineField(lineNo, qtyFieldPaths[i]);
    }
}

OEInvOutTrackingGrid.prototype.preservePrevValueOnLineField = function(lineNo, pickerPath)
{
    //Get the picker and make sure it's not hidden
    var fld = this.findLineComponent(pickerPath, lineNo, 'Field');
    if (!fld || fld.hidden) {
        return; //bail out if this picker is not there or hidden
    }
    fld.valuePrev = fld.value;
}

OEInvOutTrackingGrid.prototype.setValueOnLinePickers = function(lineNo, pickerValues)
{
    this.setValueOnLinePicker(lineNo, 'SERIALNO', pickerValues.serialno);
    this.setValueOnLinePicker(lineNo, 'LOTNO', pickerValues.lotno);
    this.setValueOnLinePicker(lineNo, 'BINID', pickerValues.binid);
    this.setValueOnLinePicker(lineNo, 'EXPIRATION', pickerValues.expiration);
}

OEInvOutTrackingGrid.prototype.setValueOnLinePicker = function(lineNo, pickerPath, value)
{
    //Get the picker and make sure it's not hidden
    var webcomboPicker = this.findLineComponent(pickerPath, lineNo, 'Field');
    if ( webcomboPicker.hidden ) {
        return; //bail out if this picker is hidden
    }
    webcomboPicker.setValue(value);
}

OEInvOutTrackingGrid.prototype.restrictLinePickers = function(lineNo)
{
    var itemTrackingRows = this.getTrackingRows();

    //Filter the serial no picker to what we have available for tracking only and not show all
    var serialNos = this.getAvailTrackingDataFieldForItem(itemTrackingRows, 'serialno');
    this.arrayPush(serialNos, this.value[lineNo].SERIALNO);
    this.restrictLinePicker(lineNo, serialNos, 'SERIALNO');

    //Filter the lot no picker to what we have available for tracking only and not show all
    var lotnos = this.getAvailTrackingDataFieldForItem(itemTrackingRows, 'lotno');
    this.arrayPush(lotnos, this.value[lineNo].LOTNO);
    this.restrictLinePicker(lineNo, lotnos, 'LOTNO');

    //Filter the bin id picker to what we have available for tracking only and not show all
    var binids = this.getAvailTrackingDataFieldForItem(itemTrackingRows, 'binid');
    this.arrayPush(binids, this.value[lineNo].BINID);
    this.restrictLinePicker(lineNo, binids, 'BINID');

    //Filter the expiration date picker to what we have available for tracking only and not show all
    var expirations = this.getAvailTrackingDataFieldForItem(itemTrackingRows, 'expiration');
    this.arrayPush(expirations, this.value[lineNo].EXPIRATION);
    this.restrictLinePicker(lineNo, expirations, 'EXPIRATION');
}

//Returns an array of tracking data of the fieldPath if we have qtyleft to use
OEInvOutTrackingGrid.prototype.getAvailTrackingDataFieldForItem = function(itemTrackingRows, fieldPath)
{
    var fields = [];
    if (itemTrackingRows) {
        itemTrackingRows.forEach(function(aRow) {
            if (fields.contains(aRow[fieldPath]) == -1
                && (aRow.qtyleft - ((!aRow.qtyused)? 0 : aRow.qtyused)) > 0) {
                fields.push(aRow[fieldPath]);
            }
        });
    }
    return fields;
}

//On the passed in lineNo, restrict the pickers in pickersToRestrict to tracking data matching rowDataToMatch with available qty
OEInvOutTrackingGrid.prototype.restrictLinePickersToMatchingData = function(lineNo, pickersToRestrict, rowDataToMatch)
{
    //Make sure we want to restrict something
    if (!pickersToRestrict.SERIALNO && !pickersToRestrict.LOTNO && !pickersToRestrict.BINID && !pickersToRestrict.EXPIRATION) {
        return;
    }

    //Get all the available tracking data rows base on the passed in rowDataToMatch
    var serialNos = [];
    var lotNos = [];
    var binIds = [];
    var expirations = [];
    var itemTrackingRows = this.getTrackingRows();
    var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, rowDataToMatch, true);
    for (var i = 0; i < matchedTrackingRows.length; i++) {
        this.arrayPush(serialNos, matchedTrackingRows[i].serialno);
        this.arrayPush(lotNos, matchedTrackingRows[i].lotno);
        this.arrayPush(binIds, matchedTrackingRows[i].binid);
        this.arrayPush(expirations, matchedTrackingRows[i].expiration);
    }

    if (pickersToRestrict.SERIALNO) {
        this.restrictLinePicker(lineNo, serialNos, 'SERIALNO');
    }
    if (pickersToRestrict.LOTNO) {
        this.restrictLinePicker(lineNo, lotNos, 'LOTNO');
    }
    if (pickersToRestrict.BINID) {
        this.restrictLinePicker(lineNo, binIds, 'BINID');
    }
    if (pickersToRestrict.EXPIRATION) {
        this.restrictLinePicker(lineNo, expirations, 'EXPIRATION');
    }
}

//Function to compare numbers and strings to support sorting in restrictLinePicker to sort numbers ahead of strings.
function compare(aVal, bVal)
{
    var aNum = !isNaN(aVal);
    var bNum = !isNaN(bVal);

    //Compare by numbers
    if (aNum && bNum) {
        return aVal-bVal;
    } else if (aNum) {
        return -1;
    } else if (bNum) {
        return 1;
    }

    //Compare by string
    if (aVal < bVal) {
        return -1;
    } else if (aVal > bVal) {
        return 1;
    }
    return 0;
}

//Restrict the webcombo picker of the passed in pickerPath to the passed in pickerValues
OEInvOutTrackingGrid.prototype.restrictLinePicker = function(lineNo, pickerValues, pickerPath)
{
    //Get the picker and make sure it's not hidden
    var webcomboPicker = this.findLineComponent(pickerPath, lineNo, 'Field');
    if (webcomboPicker.hidden) {
        return; //bail out if this picker is hidden
    }

    //The UI infrastructure wrap the picker around the combobox which maintains the servercount to display the "-- Show more --".
    //Since we don't want it, we need to reset it to the same size as the length of the passed in pickerValues.
    var comboName = '_c' + webcomboPicker.renderer.getHTMLName();
    var comboBox = window[comboName];
    comboBox.serverCount = pickerValues.length; //So we don't get the "-- Show more --" entry in picker

    //Sets the new pickerValues
    pickerValues.sort(compare);
    webcomboPicker.type.validlabels = pickerValues;
    webcomboPicker.type.validvalues = pickerValues;
    webcomboPicker.updateMetadata();
}

OEInvOutTrackingGrid.prototype.updateLineQtyFields = function(lineNo)
{
    var itemTrackingRows = this.getTrackingRows();
    var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, this.value[lineNo], false);

    //Set qty avail to zero as we use up the serial no
    var qtyAvail = this.findLineComponent('QUANTITYAVAIL', lineNo);
    var qtyAvailVal = (matchedTrackingRows.length == 1)? this.getQtyAvail(matchedTrackingRows[0]) : '';
    qtyAvail.setValue(qtyAvailVal);

    //Set this serial no row qty picked/packed to 1
    var qtyFieldPaths = this.getQtyFieldPaths();
    for (var i = 0; i < qtyFieldPaths.length; i++) {
        var qtyFld = this.findLineComponent(qtyFieldPaths[i], lineNo);
        if (qtyFld) {
            var qtyFldVal = (matchedTrackingRows.length == 1 && matchedTrackingRows[0].serialno && matchedTrackingRows[0].qtyused)?
                            matchedTrackingRows[0].qtyused.toString() : '';
            qtyFld.setValue(qtyFldVal);
        }
    }

    //Update totals and redraw to show the columns being shown/hidden
    this.computeTotals();
    this.refreshTotals();

    //Update the linedetails qty picked/packed base on tracking qty total
    this.updateParentGridLineDetailsQty();
}

OEInvOutTrackingGrid.prototype.updateLinesAvailQtyField = function(rowDataToMatch)
{
    //Update the qty available on the grid
    var qtyAvail = this.getQtyAvail(rowDataToMatch);
    for (var i = 0; i < this.value.length; i++) {
        var gridDataRow = this.value[i];
        if ((gridDataRow.SERIALNO != undefined || gridDataRow.LOTNO != undefined || gridDataRow.BINID != undefined || gridDataRow.EXPIRATION != undefined)
            && (gridDataRow.SERIALNO == undefined || gridDataRow.SERIALNO == '' || gridDataRow.SERIALNO == rowDataToMatch.serialno)
            && (gridDataRow.LOTNO == undefined || gridDataRow.LOTNO == '' || gridDataRow.LOTNO == rowDataToMatch.lotno)
            && (gridDataRow.BINID == undefined || gridDataRow.BINID == '' || gridDataRow.BINID == rowDataToMatch.binid)
            && (gridDataRow.EXPIRATION == undefined || gridDataRow.EXPIRATION == '' || gridDataRow.EXPIRATION == rowDataToMatch.expiration))
        {
            var qtyAvailFld = this.findLineComponent('QUANTITYAVAIL', i, 'Field');
            qtyAvailFld.setValue(qtyAvail.toString());
        }
    }
}

OEInvOutTrackingGrid.prototype.updateParentGridLineDetailsQty = function()
{
    //Set qty picked/packed on the parent grid linedetails base on our qty picked/packed tracking column total
    var parentGrid = this.parentComponent.parentComponent.parentComponent.parentComponent;
    var qtyFieldPaths = this.getQtyFieldPaths();
    for (var i = 0; i < qtyFieldPaths.length; i++) {
        var qtyFld = parentGrid.findLineComponent(qtyFieldPaths[i], parentGrid.lineDetails.lineNo, 'Field');
        if (qtyFld) {
            var qtytotal = this.getColumnTotal(qtyFieldPaths[i]);
            qtyFld.setValue(qtytotal.toString());
        }
    }
}

//Enable/disable the picked/packed qty field base on existing serial, lot, bin, expiration fields
OEInvOutTrackingGrid.prototype.enableDisableLineQtyFields = function(lineNo)
{
    var serialNoFld = this.findLineComponent('SERIALNO', lineNo, 'Field');
    var enableQtyFld;
    var hasSerialNo = false;
    if (serialNoFld && !serialNoFld.hidden) {
        enableQtyFld = false; //If we have serialno field, then disable qty field
        hasSerialNo = true;
    } else {
        var lotNoFld = this.findLineComponent('LOTNO', lineNo, 'Field');
        var binIdFld = this.findLineComponent('BINID', lineNo, 'Field');
        var expirationFld = this.findLineComponent('EXPIRATION', lineNo, 'Field');
        enableQtyFld = (lotNoFld && (lotNoFld.hidden || lotNoFld.value)
                        && binIdFld && (binIdFld.hidden || binIdFld.value)
                        && expirationFld && (expirationFld.hidden || expirationFld.value))? true : false;
    }
    var qtyFieldPaths = this.getQtyFieldPaths();
    for (var i = 0; i < qtyFieldPaths.length; i++) {
        var qtyFld = this.findLineComponent(qtyFieldPaths[i], lineNo, 'Field');
        if (qtyFld) {
            if (hasSerialNo && (!serialNoFld.parentValue[qtyFieldPaths[i]] || serialNoFld.parentValue[qtyFieldPaths[i]] == '0')) {
                enableQtyFld = true; //Qty is blanked and we have serial no, so enable qty fld
            }
            qtyFld.updateProperty('readonly', !enableQtyFld);
            qtyFld.redraw();
        }
    }
}

OEInvOutTrackingGrid.prototype.canAddLine = function()
{
    //we need to check the total qty exceeding the line ordered qty
    var qtyNeededFromParentGridLine = this.getTotalQtyNeededFromParent();
    if (qtyNeededFromParentGridLine) {
        var trackingGrid = this;
        var qtyFieldPaths = this.getQtyFieldPaths();
        for (var i = 0; i < qtyFieldPaths.length; i++) {
            if (Number(trackingGrid.getColumnTotal(qtyFieldPaths[i])) >= Number(qtyNeededFromParentGridLine)) {
                return false;
            }
        }
    }
    return true;
}

//Will push value into values, if value has something and is not already in the values
OEInvOutTrackingGrid.prototype.arrayPush = function(values, value)
{
    if (value && values.contains(value) == -1) {
        values.push(value);
    }
}

OEInvOutTrackingGrid.prototype.getQtyAvail = function(dataRow)
{
    return window.getQtyAvail(dataRow);
}

function getQtyAvail(dataRow)
{
    var qtyused = (dataRow.qtyused)? dataRow.qtyused : 0;
    var qtyavail = Number(dataRow.qtyleft) - Number(qtyused);
    return qtyavail.toString();
}

OEInvOutTrackingGrid.prototype.getQtyOnLine = function(lineNo)
{
    var trackingGrid = this;
    var qtyFieldPaths = this.getQtyFieldPaths();
    for (var i = 0; i < qtyFieldPaths.length; i++) {
        var qtyFld = trackingGrid.findLineComponent(qtyFieldPaths[i], lineNo, 'Field');
        if (qtyFld && !qtyFld.hidden) {
            return qtyFld.value;
        }
    }
    return '0';
}

//Returns an array of tracking data of the fieldPath if we have qtyleft to use
OEInvOutTrackingGrid.prototype.getMatchedTrackingDataRowForItem = function(itemTrackingRows, rowDataToMatch, availOnly)
{
    return window.getMatchedTrackingDataRowForItem(itemTrackingRows, rowDataToMatch, availOnly);
}

function getMatchedTrackingDataRowForItem(itemTrackingRows, rowDataToMatch, availOnly)
{
    var rows = [];
    if (itemTrackingRows) {
        itemTrackingRows.forEach(function(aRow) {
            if ( (!availOnly || (aRow.qtyleft - ((!aRow.qtyused) ? 0 : aRow.qtyused)) > 0)
                 && (rowDataToMatch.SERIALNO == undefined || rowDataToMatch.SERIALNO == '' || rowDataToMatch.SERIALNO ==
                     aRow.serialno)
                 &&
                 (rowDataToMatch.LOTNO == undefined || rowDataToMatch.LOTNO == '' || rowDataToMatch.LOTNO == aRow.lotno)
                 &&
                 (rowDataToMatch.BINID == undefined || rowDataToMatch.BINID == '' || rowDataToMatch.BINID == aRow.binid)
                 && (rowDataToMatch.EXPIRATION == undefined || rowDataToMatch.EXPIRATION == '' ||
                     rowDataToMatch.EXPIRATION == aRow.expiration) ) {
                rows.push(aRow);
            }
        });
    }
    return rows;
}

//Combine rows into one row with common data, otherwise set that field to blank
OEInvOutTrackingGrid.prototype.combineDataRows = function(rows)
{
    var serialno;
    var lotno;
    var binid;
    var expiration;

    rows.forEach(function(aRow) {
        if (aRow.serialno && aRow.serialno != serialno) {
            serialno = (serialno == undefined)? aRow.serialno : '';
        }
        if (aRow.lotno && aRow.lotno != lotno) {
            lotno = (lotno == undefined)? aRow.lotno : '';
        }
        if (aRow.binid && aRow.binid != binid) {
            binid = (binid == undefined)? aRow.binid : '';
        }
        if (aRow.expiration && aRow.expiration != expiration) {
            expiration = (expiration == undefined)? aRow.expiration : '';
        }
    });

    return {'serialno':serialno, 'lotno':lotno, 'binid':binid, 'expiration':expiration};
}

OEInvOutTrackingGrid.prototype.serialNoChanged = function(obj)
{
    var itemTrackingRows = this.getTrackingRows();
    var lineNo = obj.meta.getLineNo();

    //If we have prev value, find matching tracking data row, undo qtyused
    if (obj.meta.valuePrev) {
        var prevRowDataToMatch = Object.assign({}, this.value[lineNo]);
        prevRowDataToMatch.SERIALNO = obj.meta.valuePrev;
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, prevRowDataToMatch, false);
        if (matchedTrackingRows.length > 0) {
            matchedTrackingRows[0].qtyused = undefined;
        }
        obj.meta.valuePrev = obj.meta.value;
    } else {
        if (!this.canAddLine()) {
            alert(this.getTotalQuantityGreaterThanEntryQuantityMessage());
            obj.meta.setValue('');
            return;
        }
    }

    //New serialno, find matching tracking data row, update qtyused
    if (obj.meta.value) {
        //Get all the available rows for itemid with matching serialno, there should only be one
        var rowDataToMatch = {'SERIALNO': obj.meta.value};
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, rowDataToMatch, true);
        matchedTrackingRows[0].qtyused = 1;
        var dataRow = this.combineDataRows(matchedTrackingRows);
        this.setValueOnLinePickers(lineNo, dataRow);
        this.preservePrevValueOnLineFields(lineNo);
    }

    //Restrict the current row pickers and update the qty
    this.restrictLinePickers(lineNo);
    this.updateLineQtyFields(lineNo);
    this.enableDisableLineQtyFields(lineNo);
}

OEInvOutTrackingGrid.prototype.lotNoChanged = function(obj)
{
    var itemTrackingRows = this.getTrackingRows();
    var lineNo = obj.meta.getLineNo();
    var hasPrevValue = false;

    //If we have prev value, find matching tracking data row, undo qtyused
    if (obj.meta.valuePrev) {
        var prevRowDataToMatch = Object.assign({}, this.value[lineNo]);
        prevRowDataToMatch.LOTNO = obj.meta.valuePrev;
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, prevRowDataToMatch, false);
        if (matchedTrackingRows.length > 0) {
            matchedTrackingRows[0].qtyused = undefined;
        }
        obj.meta.valuePrev = obj.meta.value;
        hasPrevValue = true;
    } else {
        if (!this.canAddLine()) {
            alert(this.getTotalQuantityGreaterThanEntryQuantityMessage());
            obj.meta.setValue('');
            return;
        }
    }

    //New lotno, find matching tracking data row, update qtyused
    var pickersToRestrictFurther = {};
    var rowDataToMatch = (hasPrevValue)?
        {'LOTNO': obj.meta.value} :
        {'SERIALNO': obj.meta.parentValue.SERIALNO, 'LOTNO': obj.meta.value, 'BINID': obj.meta.parentValue.BINID, 'EXPIRATION': obj.meta.parentValue.EXPIRATION};
    if (obj.meta.value) {
        //Get all the available rows for itemid with matching lotno
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, rowDataToMatch, true);
        if (matchedTrackingRows.length == 1 && matchedTrackingRows[0].serialno) {
            matchedTrackingRows[0].qtyused = 1;
        }
        var dataRow = this.combineDataRows(matchedTrackingRows);
        this.setValueOnLinePickers(lineNo, dataRow);
        this.preservePrevValueOnLineFields(lineNo);
        pickersToRestrictFurther = {'SERIALNO': (dataRow.serialno == ''),
            'LOTNO': (dataRow.lotno == ''),
            'BINID': (dataRow.binid == ''),
            'EXPIRATION': (dataRow.expiration == '')};
    }

    //Restrict the current row pickers and update the qty
    this.restrictLinePickers(lineNo);
    this.restrictLinePickersToMatchingData(lineNo, pickersToRestrictFurther, rowDataToMatch);
    this.updateLineQtyFields(lineNo);
    this.enableDisableLineQtyFields(lineNo);
}

OEInvOutTrackingGrid.prototype.binChanged = function(obj)
{
    var itemTrackingRows = this.getTrackingRows();
    var lineNo = obj.meta.getLineNo();
    var hasPrevValue = false;

    //If we have prev value, find matching tracking data row, undo qtyused
    if (obj.meta.valuePrev) {
        var prevRowDataToMatch = Object.assign({}, this.value[lineNo]);
        prevRowDataToMatch.BINID = obj.meta.valuePrev;
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, prevRowDataToMatch, false);
        if (matchedTrackingRows.length > 0) {
            matchedTrackingRows[0].qtyused = undefined;
        }
        obj.meta.valuePrev = obj.meta.value;
        hasPrevValue = true;
    } else {
        if (!this.canAddLine()) {
            alert(this.getTotalQuantityGreaterThanEntryQuantityMessage());
            obj.meta.setValue('');
            return;
        }
    }

    //New binid, find matching tracking data row, update qtyused
    var pickersToRestrictFurther = {};
    var rowDataToMatch = (hasPrevValue)?
        {'BINID': obj.meta.value} :
        {'SERIALNO': obj.meta.parentValue.SERIALNO, 'LOTNO': obj.meta.parentValue.LOTNO, 'BINID': obj.meta.value, 'EXPIRATION': obj.meta.parentValue.EXPIRATION};
    if (obj.meta.value) {
        //Get all the available rows for itemid with matching lotno
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, rowDataToMatch, true);
        if (matchedTrackingRows.length == 1 && matchedTrackingRows[0].serialno) {
            matchedTrackingRows[0].qtyused = 1;
        }
        var dataRow = this.combineDataRows(matchedTrackingRows);
        this.setValueOnLinePickers(lineNo, dataRow);
        this.preservePrevValueOnLineFields(lineNo);
        pickersToRestrictFurther = {'SERIALNO': (dataRow.serialno == ''),
            'LOTNO': (dataRow.lotno == ''),
            'BINID': (dataRow.binid == ''),
            'EXPIRATION': (dataRow.expiration == '')};
    }

    //Restrict the current row pickers and update the qty
    this.restrictLinePickers(lineNo);
    this.restrictLinePickersToMatchingData(lineNo, pickersToRestrictFurther, rowDataToMatch);
    this.updateLineQtyFields(lineNo);
    this.enableDisableLineQtyFields(lineNo);
}

OEInvOutTrackingGrid.prototype.expirationChanged = function(obj)
{
    var itemTrackingRows = this.getTrackingRows();
    var lineNo = obj.meta.getLineNo();
    var hasPrevValue = false;

    //If we have prev value, find matching tracking data row, undo qtyused
    if (obj.meta.valuePrev) {
        var prevRowDataToMatch = Object.assign({}, this.value[lineNo]);
        prevRowDataToMatch.EXPIRATION = obj.meta.valuePrev;
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, prevRowDataToMatch, false);
        if (matchedTrackingRows.length > 0) {
            matchedTrackingRows[0].qtyused = undefined;
        }
        obj.meta.valuePrev = obj.meta.value;
        hasPrevValue = true;
    } else {
        if (!this.canAddLine()) {
            alert(this.getTotalQuantityGreaterThanEntryQuantityMessage());
            obj.meta.setValue('');
            return;
        }
    }

    //New expiration, find matching tracking data row, update qtyused
    var pickersToRestrictFurther = {};
    var rowDataToMatch =  (hasPrevValue)?
        {'EXPIRATION': obj.meta.value} :
        {'SERIALNO': obj.meta.parentValue.SERIALNO, 'LOTNO': obj.meta.parentValue.LOTNO, 'BINID': obj.meta.parentValue.BINID, 'EXPIRATION': obj.meta.value};
    if (obj.meta.value) {
        //Get all the available rows for itemid with matching lotno
        var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, rowDataToMatch, true);
        if (matchedTrackingRows.length == 1 && matchedTrackingRows[0].serialno) {
            matchedTrackingRows[0].qtyused = 1;
        }
        var dataRow = this.combineDataRows(matchedTrackingRows);
        this.setValueOnLinePickers(lineNo, dataRow);
        this.preservePrevValueOnLineFields(lineNo);
        pickersToRestrictFurther = {'SERIALNO': (dataRow.serialno == ''),
            'LOTNO': (dataRow.lotno == ''),
            'BINID': (dataRow.binid == ''),
            'EXPIRATION': (dataRow.expiration == '')};
    }

    //Restrict the current row pickers and update the qty
    this.restrictLinePickers(lineNo);
    this.restrictLinePickersToMatchingData(lineNo, pickersToRestrictFurther, rowDataToMatch);
    this.updateLineQtyFields(lineNo);
    this.enableDisableLineQtyFields(lineNo);
}

OEInvOutTrackingGrid.prototype.qtyFldChanged = function(obj)
{
    var itemTrackingRows = this.getTrackingRows();
    var lineNo = obj.meta.getLineNo()

    //Update the qtyused in the tracking data
    var matchedTrackingRows = this.getMatchedTrackingDataRowForItem(itemTrackingRows, this.value[lineNo], false);
    if (matchedTrackingRows.length == 1) {
        //We are editing the qty of a row, so we should have just one tracking row
        var qtyused;
        if (matchedTrackingRows[0].qtyused) {
            //Tracking row already has been used, so put back prev value and add the new value
            var prevVal = (obj.meta.valuePrev)? obj.meta.valuePrev : 0;
            qtyused = Number(matchedTrackingRows[0].qtyused) - Number(prevVal) + Number(obj.meta.value);
        } else {
            //Tracking data has not been used, so just take the new value
            qtyused = obj.meta.value;
        }

        //Validate the entered qty to make sure it is within the boundary
        var qtytotal = this.getColumnTotal(obj.meta.path);
        var errorMsg;
        var totalQtyNeeded = this.getTotalQtyNeededFromParent();
        if (Number(qtytotal) > Number(totalQtyNeeded)) {
            //Make sure the qty entered is not more than the order entry line ordered quantity
            errorMsg = this.getTrackQuantityGreaterThanEntryQuantityMessage();
        } else if (Number(qtyused) > Number(matchedTrackingRows[0].qtyleft)) {
            //Make sure the qty being added is not more than what is available
            errorMsg = this.getTrackQuantityGreaterThanAvailableQuantityMessage();
        }
        if (errorMsg) {
            alert(errorMsg);
            obj.meta.setValue(obj.meta.valuePrev);
            this.computeTotals();
            this.refreshTotals();
            return;
        }

        //Update the qtyused on tracking row and the available qty on the grid
        matchedTrackingRows[0].qtyused = qtyused;
        this.updateLinesAvailQtyField(matchedTrackingRows[0]);
        this.updateParentGridLineDetailsQty();
    }

    //Preserves the prev value
    obj.meta.valuePrev = obj.meta.value;
}