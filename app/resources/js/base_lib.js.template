/*
 * -File		base_lib.js.template
 * -License		LGPL (http://www.gnu.org/copyleft/lesser.html)
 * -Copyright	2001, The Turing Studio, Inc.
 * -Author		ale<PERSON> black, <EMAIL>
 * -Author		<PERSON> (2008) common code additions/platform independence
 */

/** 
 * this function opens a popup window. use:
 * baseOpenWindow('http://www.yahoo.com','yahooWin','300','500');
 * 
 * 
 * <AUTHOR>
 * @param	windowURL			The url to open in the new window.
 * @param	windowName			The name of the new window.
 * @param	windowWidth			The width in pixels (or w/trailing '%') of the new window.
 * @param	windowHeight		The height in pixels (or w/trailing '%') of the new window.
 * @param	windowScrollbars	Optional. Can be set to yes|no|auto. controls the scrollbars.
 * @param	centerIt			Optional. If set, centers window on screen.
 */

function baseOpenWindow(windowURL, windowName, windowWidth, windowHeight, windowScrollbars, centerIt) {
	//  See if the width and/or height are percents, if so, convert them.
	var wPercent = parsePercent(windowWidth);
	if (wPercent != -1) {
		windowWidth = screen.width * wPercent;
	}
	var hPercent = parsePercent(windowHeight);
	if (hPercent != -1) {
		windowHeight = screen.height * hPercent;
	}

	//  If requested, center the window on the screen.
	var posStr = '';
	if (centerIt) {
		var top = (screen.height - windowHeight)/2;
		var left = (screen.width - windowWidth)/2;
		posStr = ",top="+top+",left="+left;	
	}

	//  Post it!
	windowName = window.open(windowURL, windowName,
	 "width=" + windowWidth + ",height=" + windowHeight +
	  ",toolbar=0,location=0,directories=0,status=0,menuBar=0,scrollbars=" +
	  windowScrollbars + ",resizable=1,dependant"+posStr);

	//  Focus it!
	windowName.focus();
}
/** 
 * Same as baseOpenWindow(...), except opens new window with all browser controls 
 * (Tool bar, Status bar etc.)
 * 
 * 
 * <AUTHOR>
 * @param	windowURL			The url to open in the new window.
 * @param	windowName			The name of the new window.
 * @param	windowWidth			The width in pixels (or w/trailing '%') of the new window.
 * @param	windowHeight		The height in pixels (or w/trailing '%') of the new window.
 * @param	centerIt			Optional. If set, centers window on screen.
 */

function baseOpenWindowFullyFunctional(windowURL, windowName, windowWidth, windowHeight, centerIt) {
	//  See if the width and/or height are percents, if so, convert them.
	var wPercent = parsePercent(windowWidth);
	if (wPercent != -1) {
		windowWidth = screen.width * wPercent;
	}
	var hPercent = parsePercent(windowHeight);
	if (hPercent != -1) {
		windowHeight = screen.height * hPercent;
	}

	//  If requested, center the window on the screen.
	var posStr = '';
	if (centerIt) {
		var top = (screen.height - windowHeight)/2;
		var left = (screen.width - windowWidth)/2;
		posStr = ",top="+top+",left="+left;	
	}

	//  Post it!
	windowName = window.open(windowURL, windowName,
	 "width=" + windowWidth + ",height=" + windowHeight +
	  ",toolbar=1,location=1,directories=1,status=1,menuBar=1,scrollbars=1,resizable=1,dependant"+posStr);

	//  Focus it!
	windowName.focus();
}

function parsePercent(val, lowerBound, upperBound, outOfBoundRet) {
	if (!lowerBound) lowerBound = 0;
	if (!upperBound) upperBound = 100;
	if (!outOfBoundRet) outOfBoundRet = -1;

	if (typeof val != 'string' || val.indexOf('%') == -1) {
		return outOfBoundRet;
	}

	var percent = parseInt(val)/100.0;
	if (percent < lowerBound || percent > upperBound) {
		return outOfBoundRet;
	}
	return percent;
}



/** 
 * this function sets the window's opener href to the url specified. if closeMe
 * is set to 1, the window closes itself after setting the location.
 * 
 * <AUTHOR> Black
 * @param	url	the url to set the parent's location to
 * @param	closeMe	value can be 0 or 1
 */
function set_parent_location(url,closeMe) {
	parent.opener.location.href = url;
	parent.opener.focus();
	
	if (closeMe == 1) {
		window.close();
	}
}


/**
 * this is a generalized function for setting form focus.
 * it takes the integer form index, and the element index to focus.
 * 
 * <AUTHOR> Black
 * @param	form_index	the document.forms index (integer)
 * @param	element_index	the form.elements index (integer)
 */
function setFocus(form_index, element_index) {
	if (document.forms[form_index].elements[element_index]) {
		document.forms[form_index].elements[element_index].focus();
	}
}


/**
 * this is part of a workaround to using images as submit buttons, but still
 * having netscape submit the form when the user hits 'enter'.
 * it scans all keys pressed - if the key is 'enter' is submits the form.
 * right now, it can only be used on a form with an index of 0.
 * 
 * the other bit of the code is:
 * 
 * if (navigator.appName == "Netscape"){
 * document.captureEvents(Event.KEYPRESS);
 * document.onkeypress = checkInputKey;
 * }
 * 
 * <AUTHOR> Black
 * @param	k	the kay passed in to check
 */
function checkInputKey(k) {
  	var key = escape(k.which);
  	if ( key == '3'){
	document.forms[0].submit();
  	}
}



/**
 * these three functions are exactly the same, except for the string that is
 * appended to the name.
 * 
 * they accept a 'name' which is:
 * 	-the "name" attribute in the image tag"
 * 	-the base name of the image
 * 
 * they also accept a base path, which is where the image variants are stored.
 * these functions assume a naming convention:
 * 
 * %name%_on.gif
 * %name%_click.gif
 * %name%_off.gif
 * 
 * because of that, the function calls are simple, the the code is clean.
 * 
 * wrote these because I dislike the dreamweaver functions. egh.
 * 
 * img_on('reply_via_email','images/');
 * 
 * <AUTHOR> Black
 * @param	name	the name of the image (see above)
 * @param	basepath	the location in htdocs of the image
 */
function img_on(name,basepath) {
	path=basepath+name+"_on.gif";
	document.images[name].src= path;
}

function img_click(name, basepath) {
	path=basepath+name+"_click.gif";
	document.images[name].src= path;
}

function img_off(name, basepath) {
	path=basepath+name+"_off.gif";
	document.images[name].src= path;
}

function prependImageNameIfIPad(imageName) {
	return (globalIs.touch) ? ('mobile_' + imageName) : imageName;
}


/**
 *  baseAddEventHandler
 *    This function adds an event handler to an object in a browser-independent fashion.
 *
 *	@param	obj			Object to attach event handler to.
 *	@param	evType		Name of event for handler (without 'on' prefix).
 *	@param	fn			Event handler function.
 */

function baseAddEventHandler(obj, evType, fn) {
	if (obj.addEventListener){
		obj.addEventListener(evType, fn, false);
		return true;
	} else if (obj.attachEvent){
		var r = obj.attachEvent("on"+evType, fn);
		return r;
	} else {
		return false;
	}
}

/**
 *  baseRemoveEventHandler
 *    This function removes an event handler from an object in a browser-independent fashion.
 *
 *	@param	obj			Object to remove event handler from.
 *	@param	evType		Name of event for handler (without 'on' prefix).
 *	@param	fn			Event handler function to remove.
 */

function baseRemoveEventHandler(obj, evType, fn) {
	if (obj.removeEventListener){
		obj.removeEventListener(evType, fn, false);
		return true;
	} else if (obj.detachEvent){
		obj.detachEvent("on"+evType, fn);
		return true;
	} else {
		return false;
	}
}

/**
 *  baseGetNamedObjs / baseGetDynamicNamedObjs
 *   These functions will return a list of 'named' objects, but only in special circumstances.
 *  IE has a bug where it will not properly returned named elements if those elements have been
 *  added dynamically.  So, this function will sniff the browser and return the named objects for
 *  compliant browsers using getElementsByName, and use a MUCH slower variation for IE.
 */

function baseGetNamedObjs(theName) {
	if (globalIs.ie) {
		var allDivs = document.getElementsByTagName('DIV');
		var outDivs = new Array();
		var numOut = 0;
		for (var i=0, cnt=allDivs.length; i<cnt; i++) {
			if (allDivs[i].getAttribute('name') == theName) {
				outDivs[numOut++] = allDivs[i];
			}
		}
		return outDivs;
	} else {
		return document.getElementsByName(theName);
	}
}

function baseGetDynamicNamedObjs(tagName, theName) {
	if (globalIs.ie) {
		var allObjs = document.getElementsByTagName(tagName);
		var outObjs = new Array();
		var numOut = 0;
		for (var i=0, cnt=allObjs.length; i<cnt; i++) {
			if (allObjs[i].getAttribute('name') == theName) {
				outObjs[numOut++] = allObjs[i];
			}
		}
		return outObjs;
	} else {
		return document.getElementsByName(theName);
	}
}

/**
 *  baseSizeBlock
 *    This function sizes the borders of a html 'Block'.
 */

var blockHeightAdjust = 10;	//  Reduces block height by this amount (twice if top and bottom borders).
var fallbackdivWidth = 0;
var fallbackulcoffsetWidth = 0;
var fallbackurcoffsetWidth = 0;

function baseSizeBlock(preStr) {
	//var borderDivs = document.getElementsByName(preStr+"BlockBorderTop");
	var borderDivs = baseGetNamedObjs(preStr+"BlockBorderTop");
	if (borderDivs.length <= 0) {
		return;			// No results.
	}
	var divWidth = borderDivs[0].offsetWidth == 0 ? fallbackdivWidth : borderDivs[0].offsetWidth;

	var ulc = baseGetNamedObjs(preStr+"BlockBorderTopULC")[0];
	var llc = baseGetNamedObjs(preStr+"BlockBorderBottomLLC")[0];
	var urc = baseGetNamedObjs(preStr+"BlockBorderTopURC")[0];
	var lrc = baseGetNamedObjs(preStr+"BlockBorderBottomLRC")[0];
	var ulcoffsetWidth = ulc.offsetWidth == 0 ? fallbackulcoffsetWidth : ulc.offsetWidth;
	var urcoffsetWidth = urc.offsetWidth == 0 ? fallbackurcoffsetWidth : urc.offsetWidth;

	//  Calc width first.  Only set if > 0.  Because of refresh event timing, parent div width
	//   may not yet be properly set.
	var newWidth = divWidth - ulcoffsetWidth - urcoffsetWidth - 1;

	if (newWidth > 0) {
		var topPieces = baseGetNamedObjs(preStr+"BlockBorderTopMiddle");
		for (var i=0; i<topPieces.length; i++) {
			topPieces[i].style.width = newWidth + "px";
		}
		var bottomPieces = baseGetNamedObjs(preStr+"BlockBorderBottomMiddle");
		for (var i=0; i<bottomPieces.length; i++) {
			bottomPieces[i].style.width = newWidth + "px";
		}
	}

	for (var i=0; i<borderDivs.length; i++) {
		var bn = i + "";
		var left_border = document.getElementById(preStr+"BlockBorderLeft"+bn);
		var right_border = document.getElementById(preStr+"BlockBorderRight"+bn);
		var thisResultDiv = document.getElementById(preStr+"BlockContents"+bn);

		//  If is important to extract the height BEFORE setting the width.  Setting the
		//   width changes the layout, and causes spurious issues with the 'correct' height.
		var blockHeight = thisResultDiv.offsetHeight;

		var newBorderWidth = divWidth - left_border.offsetWidth - right_border.offsetWidth - 1;
		var fblock_div = document.getElementById(preStr+"BlockFooter"+bn);
		if (newBorderWidth > 0) {
			thisResultDiv.style.width = newBorderWidth + "px";

			//  Adjust the height smaller because we 'squish' the top and bottom together to make
			//   the rounded headers match the surrounding rounded border.  This adjustment depends
			//   on whether the block has just a header or a header and a footer.
			//var heightAdjust = (fblock_div) ? (2*blockHeightAdjust) + 1 : blockHeightAdjust;
			var heightAdjust = (2*blockHeightAdjust);
			var heightval = blockHeight - heightAdjust + 1;
			// Script dies in IE for -ve value
			if(heightval < 0) {
				heightval = 0;
		}
			left_border.style.height = heightval + "px";
			right_border.style.height = heightval + "px";
		}

		//  Do the block header.
		var block_div = document.getElementById(preStr+"BlockHeader"+bn);
		if (block_div) {
			var left_bcorn = document.getElementById(preStr+"BlockHeaderULC"+bn);
			var right_bcorn = document.getElementById(preStr+"BlockHeaderURC"+bn);
			var block_border = document.getElementById(preStr+"BlockHeaderUB"+bn);
			newBorderWidth = block_div.offsetWidth - left_bcorn.offsetWidth - right_bcorn.offsetWidth;
			if (newBorderWidth > 0) {
				block_border.style.width = newBorderWidth + "px";
			}
		}

		//  If there is one, do the block footer.
		if (fblock_div) {
			var fleft_bcorn = document.getElementById(preStr+"BlockFooterULC"+bn);
			var fright_bcorn = document.getElementById(preStr+"BlockFooterURC"+bn);
			var fblock_border = document.getElementById(preStr+"BlockFooterUB"+bn);
			newBorderWidth = fblock_div.offsetWidth - fleft_bcorn.offsetWidth - fright_bcorn.offsetWidth;
			if (newBorderWidth > 0) {
				fblock_border.style.width = newBorderWidth + "px";
			}
		}
	}
}

/**
 * This function will check all the checkboxes in a particular form. It has been
 * abstracted to be used with any form, which means that you have to pass in
 * everything:
 * 	
 * -ckval is the value to set the checkbox to, can be "true" or "false"
 * -form_index is the integer index of the form. typically, this is 0, but if you
 * have some other forms on your page before the checkbox form, you'll need this.
 * 
 * example:
 * checkTheBoxes('true', '25', '0');
 * 
 * <AUTHOR> Black
 * @param	ckval	the value to set the element.checked attribute to
 * @param	form_index	the integer index of the form to check all the boxes in
 */
function checkTheBoxes(ckval, form_index) {
	var form_length = document.forms[form_index].elements.length;
	if (form_length != 0) {
		for (var i = 0; i <= form_length-1; ++i) { 
			if (document.forms[form_index].elements[i].type == "checkbox") {
				document.forms[form_index].elements[i].checked = ckval;
			}
		}
	}
}		



/**
 * This is a simple function that takes a set of field names and a matching set of 
 * field values that belong to one form and creates hidden inputs within the given
 * form object.
 *
 * <AUTHOR>
 * @param	names	- the set of field names for the new hidden inputs
 * @param	values	- the set of field values for each field name in 'names'
 * @param	formObj	- the form object that is to receive the new hidden inputs
 */
function addHiddenInputsToForm(names, values, formObj) {
	if ( ( !names || names.length == 0 || !values || values.length == 0) ) {
		return false;
	}
	if ( typeof(formObj) != 'object' ) {
		return false;
	}
	if ( names.length != values.length ) {
		return false;
	}

	for (var i = 0; i < names.length; i++) {
		var inputNode = baseCreateNamedElement('input', fields[i], formObj.ownerDocument);
		inputNode.setAttribute('type', 'hidden');
		inputNode.setAttribute('value', values[i]);
		formObj.appendChild(inputNode);
	}

	return true;
}


/**
 *  baseStrHtmlDecode
 *    This function decodes a string that was encoded on the server side by the standard 'htmlspecialchars'
 *   function.
 *
 */

function baseStrHtmlDecode(str) {
	str = str.toString();
	str = str.replace(/&lt;/g, '<');
}


function baseStrHtmlEncode(str) {
    str = str.toString();
    str = str.replace('<', '&lt;');
    str = str.replace('>', '&gt;');
    
    return str;
}

/**
 * baseEscapeQueryString
 *  This function escapes the values in a query string (of the format '&.=value1&.=value2...')
 * @param string str - the query string which has to be escaped
 * @return string str - the query string with values escaped
  */

function baseEscapeQueryString(str) {
	var arr = str.split('&');
	for (var i=0; i<arr.length; i++) {
		var tempArr = arr[i].split('=');
		if (tempArr.length > 1) {
			tempArr[1] = escape(tempArr[1]);
			arr[i] = tempArr.join('=');
		}
	}
	str = arr.join('&');
	return str;
}


/**
 *  basePrepareREFormat
 *    This function prepares a regular expression format for use on user-input characters.
 */

function basePrepareREFormat(format) {
	//  For UTF-8, replace any occurances of the word character with an equivalent
	//   that matches the individual character ranges.  This could be improved to
	//   explicitly disallow Unicode symbol character ranges, but at a higher performance cost.
	format = format.replace(/\\w/g, 'a-zA-Z0-9_\\u00A1-\\uFFFF');
	return format;
}


/**
 *   baseStringSubsArgs
 *    This function will substitute arguments into a given string.  It assumes that the first argument
 *   is a string containing ':1', ':2', etc, which are substituted with the values given after the first
 *   argument.  That is, the second argument is substituted into ':1', the third into ':2', etc.  This
 *   function is useful for formatting a message with a variable number of substituted runtime values.
 */

function baseStringSubsArgs() {
	if (arguments.length <= 0) {
		return "";
	}
	var str = arguments[0];
	for (var i=1; i<arguments.length; i++) {
		str = str.replace(":"+i, arguments[i]);
	}
	return str;
}


/**
 *  baseValidateDate
 *     This function validates a given date and returns true if the date is valid, and false otherwise.
 *    It assumes that the input date is in the given format 'dateFormat'; if 'dateFormat' is empty,
 *    '/mdY' is assumed.
 */

function baseValidateDate(val, dateFormat) {
	if (dateFormat) {
		var indate =  ReformatDate(val, dateFormat, '/mdY');
	} else {
		var indate = val;
	}

	legend =
	 new RegExp('^((0[1-9]/?)|([1-9]/)|(1[0-2]/?))((0[1-9]/?)|([1-9]/)|([1-2][0-9]/?)|(3[0-1]/?))([0-9]{4})$');
	return legend.test(indate);
}


/**
 *  baseCreateNamedElement
 *    This function creates a new element of the given type and name in the given document or
 *   form context.  Note that this function does NOT add the newly created element as a child.
 *   IE has yet another bug that requires attributes to be added to the new element before it
 *   is added to the document/form context (eg via appendChild).  So, this function simply
 *   creates the element; the caller is responsible for setting any attributes and THEN adding
 *   the object to its context.
 */

function baseCreateNamedElement(type, name, createContext) {
	if (createContext == null || typeof createContext == 'undefined')
		createContext = document;

	// Horrible IE bug prevents using normal DOM createElement/setAttribute(name.  If
	//  done that way, new element won't be found by name.  Use hack below to get around this bug.
	var node;
	if (globalIs.ie) {
		node = createContext.createElement("<"+type+" name='"+name+"'//>");
	} else {
		node = createContext.createElement(type);
		node.setAttribute('name', name);
	}
	return node;
}


/**
 *  pseudoActiveElementOnFocus
 *    This event callback sets the activeElement for browsers that don't support activeElement.
 */
function pseudoActiveElementOnFocus(evt) {
	if (evt && evt.explicitOriginalTarget) {
		document.activeElement = evt.explicitOriginalTarget;
	} else if (evt && evt.target) {
		document.activeElement = evt.target;
	} else {
		document.activeElement = document;
	}
}

function pseudoDeactivateOnBlur(evt) {
	document.activeElement = null;
}

function baseOnLoad() {
	// For browsers that don't support activeElement, add focus handler to fake it.
	if (typeof document.activeElement == "undefined") {
		if (document.addEventListener)		// Should always be true.  No activeElement => addEventListener. 
		{
			document.addEventListener("focus", pseudoActiveElementOnFocus, true);
			document.addEventListener("blur", pseudoDeactivateOnBlur, true);
		}
	}

	// Initialize any base icons.
	baseIconInit();

	// Initialize any hidden fields.
	baseHiddenInit();
	
	// Try to initialize the clickTracker from attachEvents
	if (typeof clickTracker != 'undefined') {
	    clickTracker.init();
	}

//jq(window).ready(function (){
//    alert("Number nodes:"+jq('*').length);
//});

}

function staticCopyFromHidden(evt) {
	evt = (evt || window.event);
	var hidden = (evt.srcElement || evt.target);
	CopyFromHidden(hidden.form, hidden.name, hidden.value);
}

function baseHiddenInit() {
    var elems = document.getElementsByTagName('*');
    var len = elems.length;
    var observer =  null;
    var optionsObserver = {
        subtree: false,
        attributes: true
    };

    for (var i=0; i<len; i++) {
        var nextElem = elems[i];
        if (nextElem.className == 'baseHidden') {
            var nonHiddenName = nextElem.getAttribute('nonhiddenname');
            // IE doesn't allow standard access, so do it their way.
            
            if (typeof nonHiddenName == 'undefined' || nonHiddenName == null) {
                nonHiddenName = nextElem.nonhiddenname;
            }
            
            if (nonHiddenName != null) {
                var MutationObserver = window.WebKitMutationObserver || window.MozMutationObserver || window.MutationObserver;
                //As of 9/12 this is defined in chrome and not safari.
                if (MutationObserver) {
                    
                    if (observer == null) {
                        observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(e) {
                                if (e.attributeName == 'value'); {
                                    CopyFromHidden(e.target.form, e.target.name, e.target.value);
                                }
                            });
                        });
                    }
                    
                    observer.observe(nextElem, optionsObserver);
                    
                }  else if (nextElem.attachEvent) {
                    nextElem.attachEvent("onpropertychange", staticCopyFromHidden);
                } else if (nextElem.addEventListener) {
                    nextElem.addEventListener("DOMAttrModified", staticCopyFromHidden, false);
                }; 
            }
        }
    }
}

/**
 *  baseIcon functionality
 *     These functions provide a means of creating/setting an icon on an element in
 *    a browser-independent fashion.
 *
 *  baseIconAdd			Add icon to a given element.
 *  baseIconSet			Set icon on a given element.
 *  baseIconToggle		Toggles between two given icons.
 *  baseIconInit		Initialize all icons in a DOM.  Called automatically by baseOnLoad.
 */

if (typeof baseIconArrowLeft == 'undefined') {
var baseIconArrowLeft = 0;
var baseIconArrowRight = 1;
var baseIconArrowUp = 2;
var baseIconArrowDown = 3;
var baseIconX = 4;
var baseIconCrossCircle = 5;
var baseIconArrowLeftToEnd = 6;
var baseIconArrowRightToEnd = 7;
var baseIconLast = 7;		// Make sure this one is the same value as the last icon above!!!

var baseIconClasses = ['baseIconArrowLeft', 'baseIconArrowRight', 'baseIconArrowUp', 'baseIconArrowDown',
 'baseIconX', 'baseIconCC', 'baseIconArrowLeftToEnd', 'baseIconArrowRightToEnd'];
var baseIconUnicodes = ['\u25c0', '\u25b6', '\u25b2', '\u25bc', 'x', 'o', '\u25c0'+'\u25c0', '\u25b6'+'\u25b6'];
var baseIconWebdings = [ '3', '4', '5', '6', '\162', '\170', '7', '8'];
}

//  Used for browser-independent nodeType ids.
var OUR_ELEMENT_NODE = 1;
var OUR_TEXT_NODE    = 3;

function baseIconFind(elem) {
	var len = elem.childNodes.length;
	for (var i=0; i<len; i++) {
		var nextElem = elem.childNodes[i];
		if (nextElem.nodeType == OUR_ELEMENT_NODE && nextElem.getAttribute('iconValue') != null)
			return elem.childNodes[i];
	}
	return null;
}

function baseIconAdd(elem, icon) {
	if (icon == null || icon == "" || icon == "NaN" || icon < 0 || icon > baseIconLast) return null;

	//  If there is already an icon on this element, simply reset its value.
	var ourSpan = baseIconFind(elem);
	if (ourSpan != null)
		return baseIconSet(elem, icon);

	var fontSize;
	if (!globalIs.canDisplayUnicode)
		fontSize = '1em';				// Default for webdings
	var fontSizeAttr = elem.getAttribute('baseFontSize');
	if (fontSizeAttr)
		fontSize = fontSizeAttr;

	//  Create the new icon as a SPAN with a text node.
	var newSpan = document.createElement('span');
	newSpan.setAttribute('class', baseIconClasses[icon]);
	newSpan.style.fontSize = fontSize;
	elem.appendChild(newSpan);

	if (globalIs.canDisplayUnicode)
		newSpan.appendChild(document.createTextNode(baseIconUnicodes[icon]));
	else {
		newSpan.style.fontFamily = 'webdings';
    	newSpan.appendChild(document.createTextNode(baseIconWebdings[icon]));	// Rely on webdings
	}
	newSpan.setAttribute('iconValue', icon);
	return newSpan;
}

function baseIconSet(elem, icon) {
	if (icon == null || icon == "" || icon == "NaN" || icon < 0 || icon > baseIconLast) return null;
	var ourSpan = baseIconFind(elem);
	if (ourSpan == null)
		return baseIconAdd(elem, icon);

	// If unicode allowed, use it, else rely on webdings
	if (globalIs.canDisplayUnicode)
		ourSpan.replaceChild(document.createTextNode(baseIconUnicodes[icon]), ourSpan.firstChild);
	else
		ourSpan.replaceChild(document.createTextNode(baseIconWebdings[icon]), ourSpan.firstChild);
	ourSpan.setAttribute('iconValue', icon);
	return ourSpan;
}

function baseIconSetValue(elem, icon) {
	if (icon == null || icon == "" || icon == "NaN" || icon < 0 || icon > baseIconLast) return;

	// If unicode allowed, use it, else rely on webdings
	if (globalIs.canDisplayUnicode) {
		elem.value = baseIconUnicodes[icon];
	} else {
		elem.style.fontFamily = 'webdings';
		elem.style.fontSize = '1em';
		elem.value = baseIconWebdings[icon];
	}
	return;
}

function baseIconToggle(elem, icon1, icon2) {
	var ourSpan = baseIconFind(elem);
	if (ourSpan == null)
		return baseIconAdd(elem, icon1);	// If none, add first icon as default.

	var newVal = (ourSpan.getAttribute('iconValue')==icon1)?icon2:icon1;
	baseIconSet(elem, newVal);
}

/**
 *  baseIconInit
 *    Initializes all anchors of class 'baseIcon' and input buttons of 'baseIconButton'.
 *   What this does for anchors is to create a visible icon as a child of this element.
 *   For buttons, it sets the button value (i.e. its label).  The initial value of the label
 *   is given by the element attribute 'iconState', and must be one of the base
 *   icon values given above.
 */

function baseIconInit() {
	//  First, process any anchors.
    if (window.jq) {
       jq('A.baseIcon').each(function() {
           var curState = this.getAttribute('iconState');
           if (curState != null) {
               baseIconAdd(this, curState);
           }
       });
    } else {
        var elems = document.getElementsByTagName('A');
        var len = elems.length;
        for (var i=0; i<len; i++) {
            var nextElem = elems[i];
            if (nextElem.className == 'baseIcon') { // can't handle multiple class names like jq above
                var curState = nextElem.getAttribute('iconState');
                if (curState != null)
                    baseIconAdd(nextElem, curState);
            }
        }
    }

	//  Now, do the input buttons.
	var elems = document.getElementsByTagName('INPUT');
	var len = elems.length;
	for (var i=0; i<len; i++) {
		var nextElem = elems[i];
		if (nextElem.className == 'baseIconButton') {
			var curState = nextElem.getAttribute('iconState');

			// IE doesn't allow standard access, so do it their way.
			if (typeof curState  == 'undefined' || curState == null)
				curState = nextElem.iconState;
			if (curState != null)
				baseIconSetValue(nextElem, curState);
		}
	}	
}


/**
 * baseModalDialog
 *
 *  Post a modal dialog.
 *    Note: for non-IE browsers, modal dialogs do not operate as expected.  That is, the
 *   user can still interact with the parent window (and other windows).  This may be fixed
 *   when other browsers (Firefox 3.0?) support the idea of modal windows.
 *
 * @param     
 * @url			URL of the document to load/display.
 * @title		Title of new dialog.
 * @varg		Variant to make available to the new window dialog as context information.
 * @width		Width of new dialog (currently only supports pixels).
 * @height		Height of new dialog (currently only supports pixels).
 * @addHelp		1=add help menu, 0=no help.
 * @return
 * @throws    
 */

var modalChildWindow = null;
var modalResumeFunc = null;

function putModalOnTop(evt) {
	if (modalChildWindow && !modalChildWindow.closed) {
		modalChildWindow.focus();
	}
}

function nullResume(retValue) {
	modalChildWindow = null;
}

function modalResume() {
	// Reset the modalChildWindow BEFORE the resume function.  This allows the resume
	//   function to call baseModalDialog, if necessary, to repost the modal dialog.
	//   This is not really a case of recursive modal dialogs, merely ships passing in the night...
	if (modalChildWindow != null) {
		var retVal = modalChildWindow.returnValue;
		modalChildWindow = null;
		modalResumeFunc(retVal);
	}
}

function baseModalDialog(url, title, darg, resumeFunc, width, height, addHelp) {
	
	if (resumeFunc == 0 || resumeFunc == "")
		modalResumeFunc = nullResume;
	else
		modalResumeFunc = resumeFunc;

	if (window.showModalDialog) {
		var features = "dialogWidth: " + width + " px;";
		features += "dialogHeight: " + height + " px;";
		features += "help: " + (addHelp)?"yes;":"no;";
		// window.showModalDialog is not supported in Mozilla versions older than 3
		// also its not possible to create a modal window as in IE but at least we can keep
		// the child window in front of the screen  
		var retVal = window.showModalDialog(url, darg, features);		
	
		modalResumeFunc(retVal);
	} else {
		if (modalChildWindow && !modalChildWindow.closed) { // Don't allow nested modal windows.
			if (console) console.log("Cannot post recursive modal dialogs");
			return false;
		}
		var centerx = (window.screen.width / 2) + window.screen.left;
		var centery = (window.screen.height / 2) + window.screen.top;
		var features = "width=" + width;
		features += ",height=" + height;
		features += ",left=" + (centerx - (width/2));
		features += ",top=" + (centery - (height/2));
		try {
			modalChildWindow = window.open(url, title, features);
		} catch(e) {
			if (console.log) console.log("Modal dialog error: " + e.description);
			return false;
		}

        var ua = navigator.userAgent;
        /* MSIE used to detect old browsers and Trident used to newer ones*/
        var isIE = ua.indexOf("MSIE ") > -1 || ua.indexOf("Trident/") > -1;
        var isEdge = ua.indexOf("Edge") > -1

        // IE does not like to ovewrite the close function
        if (isEdge || isIE) {
            jq(modalChildWindow).unload(function() {
                modalResume();
            });
        } else {
            modalChildWindow.realClose = modalChildWindow.close;
            modalChildWindow.close = function() {
                modalResume();
                this.realClose(); //calling modalChildWindow.realClose() here raises an exception so I use this.realClose which seems to oddly work.
            };
        }

        modalChildWindow.dialogArguments = darg;
		window.onclick = putModalOnTop;
		window.onfocus = putModalOnTop;      
	}
	return true;
}


////////////////////////
// baseGetText/baseSetText
//   Browser independent text get/set methods.
//
function baseGetText(elem) {
	if ('string' == typeof elem.innerText) return elem.innerText;
	if ('string' == typeof elem.textContent) return elem.textContent;
	return elem.innerHTML.replace(/<[^>]*>/g,'');	// Return HTML with tags removed.
}

function baseSetText(elem, txt) {
	// Check for button and text input types first up to avoid them getting into innerText check
	// It gets into problems with Webkit based browsers (Chrome and Safari)
	if (elem.tagName == 'INPUT' && (elem.type == 'button' || elem.type == 'text')) {
		elem.value = txt;
		return;
	}

	// Do the IE way instead of DOM, because DOM way has a bug where it seems to expand
	//  whitespace.
	if ('string' == typeof elem.innerText) {
		elem.innerText = txt;
		return;
	}


	// Others that support 'textContext', like FF.
	if ('string' == typeof elem.textContent) {
		elem.textContent = txt;
		return;
	}

	//  DOM way below doesn't seem to work with IE - expands whitespace.
	//if (existing == "")
	//	elem.appendChild(document.createTextNode(txt));
	//else
	//	elem.replaceChild(document.createTextNode(txt), elem.firstChild);
}


////////////////////////
// baseIsVisible
//   Tests to see if an element is visible.  Includes testing all parents up the DOM hierarchy.
function baseIsVisible(elem) {
    if (jq) {
        //use jquery if available. Faster than other  methods across different browsers.
        var $elem = jq(elem);
        return !($elem.is(':hidden') || $elem.parents(':hidden').length);
    };
    while (elem && elem.tagName != 'BODY') {
        try {
            //SOME REALLY OLD NON-IE BROWSERs MAY FAIL THAT IS WHY I HAVE THE TRY CATCH....
            //OF COURSE WE PROBALLY DON'T SUPPRORT THOSE BROWSERS.
            var computedStyle;
            if (window.getComputedStyle) {
                computedStyle = window.getComputedStyle(elem, null);
            } else {
                computedStyle = elem.currentStyle;
            }        
            if (computedStyle.display == 'none' || computedStyle.visibility == 'hidden')
                return false;
        } catch (e) {
        }
        
        //THIS OLD CODE IS WRONG! DOES NOT LOOK AT STYLES THAT COME FROM CLASS DEFINITIONS.
        //JUST LEAVING HERE IF FOR SOME RAESON THE NEWER METHODS ARE FAILING AS IT IS A 
        //BETTER GUESS THAN NO/RANDOM GUESS.
        if (elem.style.display == 'none' || elem.style.display.visibility == 'hidden')
            return false;
        elem = elem.parentNode || elem.parentElement;
    }
    return true;
}


////////////////////////
// baseFindIndexInArr
//   This function finds an element in a given element array, and returns its index.  It
//  attempts to match the object by id, name, and the isEqualNode method (which may not
//  be implemented for all browsers).  If the element array 'array' is not given, then
//  all elements in the DOM are searched.  If the element is not found, -1 is returned.

function baseFindIndexInArr(elem, arr, excludeHidden) {
	if (arr == null)
		arr = document.getElementsByTagName('*');
	var ind;
	var len = arr.length;
	
	// Find the elements position in the list.
//	if (elem.id) {
//		for (ind=0; ind<len; ind++) {
//			if (arr[ind].id == elem.id && !(excludeHidden && arr[ind].type == 'hidden')) {
//				return ind;
//			}
//		}
//	}
//	if (elem.name) {
//		for (ind=0; ind<len; ind++) {
//			if (arr[ind].name == elem.name && !(excludeHidden && arr[ind].type == 'hidden')) {
//				return ind;
//			}
//		}
//	}
	for (ind=0; ind<len; ind++) {
		if (arr[ind] == elem && !(excludeHidden && arr[ind].type == 'hidden')) {
			return ind;
		}
	}
	//  The following is not implemented even for FF.  If someday it is, it is a better test.
	//if (elem.isEqualNode) {
	//	for (ind=0; ind<len; ind++) {
	//		if (elem.isEqualNode(arr[ind])) {
	//			return ind;
	//		}
	//	}
	//}
	return -1;
}


////////////////////////
// baseNextInTabOrder
//   Gets the next element in the user tab order.  Only considers visible elements of the following
//  type: a, button, input, textarea, select.  Wraps around to start of tab order if necessary.
//
//  @param
//  @elem		Element to start traversal to next element from.
//

function baseNextInTabOrder(elem, reverse) {
	var ind, seenIndex = -1;
	arr = document.getElementsByTagName('*');
	var len = arr.length;

	//  Find the index of the given element.  If none, we'll start at the beginning.
	if (elem != null)
		seenIndex = baseFindIndexInArr(elem, arr, true);

	//  Look through elements, attempting to match visible elements of the right type.
	//  Fix for DE15757: We need to also ignore elements with tabIndex = -98 this is 
	//  used by bootstrap-select for the hidden select in selectpickers 
	if ((elem == null || seenIndex != -1) && seenIndex != -98) {
		var ind = (reverse) ? seenIndex-1 : seenIndex+1;
		if (ind >= len)
			ind = 0;
		else if (ind < 0)
			ind = len-1;

		while (ind != seenIndex) {
			if (arr[ind].tabIndex != -1 && arr[ind].tabIndex != -98) {
				// Only consider if visible and in the tab order.
				var ne = arr[ind];
				if (ne.type != 'hidden' && !ne.disabled &&  //note ne.disabled could be undefined of false!
				(ne.tagName=='A'||ne.tagName=='BUTTON'||ne.tagName=='INPUT'||
				 ne.tagName=='TEXTAREA'||ne.tagName=='SELECT') && baseIsVisible(arr[ind])) {
					return arr[ind];
				}
			}
			if (reverse) {
				if (--ind < 0)			// Check for wraparound
					ind = len-1;
			} else {
				if (++ind >= len)		// Check for wraparound.
					ind = 0;
			}
		}
	}
	//  Not found - just look for one with a tab index.
	for (ind=0; ind<len; ind++) {
		if (arr[ind].tabIndex != -1 && arr[ind].tabIndex != -98) {
			return arr[ind];
		}
	}
	return null;		// Can't find any.  Could be no elements in form have a tab order != -1?
}

// baseDisableAnchor
function baseDisableAnchor(anch, disable) {
	if (typeof anch.disabled != "undefined") {	// IE allows direct, easy disabling of anchors
		anch.disabled = disable;
		return;
	}

	if (disable){
		var href = anch.getAttribute("href");
		if (href && href != "" && href != null){
			anch.setAttribute('href_bak', href);
		}
		anch.removeAttribute('href');

		anch.setAttribute('color_bak', anch.style.color);
		anch.style.color="gray";

		var func = anch.getAttribute('onclick');
		anch.setAttribute('onclick_bak', func);
		anch.setAttribute('onclick', 'void(0);');
	} else {
		if (anch.attributes['href_bak'])
			anch.setAttribute('href', anch.attributes['href_bak'].nodeValue);
		if (anch.style.color == 'gray')
			anch.style.color = anch.getAttribute('color');
		if (anch.attributes['onclick_bak'])
			anch.setAttribute('onclick', anch.getAttribute('onclick_bak'));
	}
}

////////////////////////
//   baseGetParent
//     This function returns the parent of an element in the DOM in a browser-independent fashion.

function baseGetParent(elem) {
	if (elem) {
		return elem.parentNode || elem.parentElement;
	}
	return null;
}


////////////////////////
//   baseIsParent
//     This function determines if the given IDed or named object is in the parent path of the given
//   element - that is, is the parent an ancestor of the given element.  If the named/IDed ancestor
//   is found, it is returned, else null is returned.

function baseFindParent(elem, parentId, parentName) {
	var parent = elem.parentNode || elem.parentElement;
	while (parent && parent.tagName != 'BODY') {
		if ((parentId && parent.id == parentId) || (parentName && parent.name == parentName)) {
			return parent;
		}
		parent = parent.parentNode || parent.parentElement;
	}
	return null;
}

////////////////////////
//  baseRemoveAll
//    This function removes any elements from a parent element.  It also optionally removes the parent.

function baseRemoveAll(ourElem, removeMeToo) {
	while (ourElem.firstChild) {
		ourElem.removeChild(ourElem.firstChild);
	}
	if (removeMeToo) {
		var parent = baseGetParent(ourElem);
		parent.removeChild(ourElem);
	}
	return;
}

////////////////////////
//  baseSelectAddOption
//    Generic function to add an option to a select object in a browser-independent fashion.
//
//		@param	selectElem		Select element to add option to.
//		@param	text			Text label of new option.
//		@param	value			Value of new option.
//		@param	keepSorted		Optional parameter to keep select items sorted by text.

function baseSelectAddOption(selectElem, text, value, keepSorted) {
	var newOpt = document.createElement('option');
	newOpt.text = text;
	newOpt.value = value;

	return baseSelectAddOptionObj(selectElem, newOpt, keepSorted);
}


////////////////////////
//  baseSelectAddOptionObj
//    Generic function to add an option object to a select object in a browser-independent fashion.
//
//		@param	selectElem		Select element to add option to.
//		@param	newOpt			New option to add.
//		@param	keepSorted		Optional parameter to keep select items sorted by text.

function baseSelectAddOptionObj(selectElem, newOpt, keepSorted) {

	//  If sorted, find the right spot.
	var optAfter = null;
	var optIndAfter = null;
	if (keepSorted) {
		for (var i=0,cnt=selectElem.length; i<cnt; i++) {
			if (newOpt.text < selectElem.options[i].text) {
				optAfter = selectElem.options[i];
				optIndAfter = i;
				break;
			}
		}
	}

	//  IE and W3C do adds differently.  For speed, do browser sniffining instead of
	//   the standard try/catch.

	if (globalIs.ie) {
		if (optIndAfter == null) {
			selectElem.add(newOpt);
		} else {
			selectElem.add(newOpt, optIndAfter);	// IE only.
		}
	} else {
		selectElem.add(newOpt, optAfter);			// Doesn't work on IE.
	}
//	try {
//		selectElem.add(newOpt, optAfter);			// Doesn't work on IE.
//	} catch(e) {
//		if (optIndAfter == null) {
//			selectElem.add(newOpt);
//		} else {
//			selectElem.add(newOpt, optIndAfter);	// IE only.
//		}
//	}
	return newOpt;
}


////////////////////////
//  baseSelectAddOptionAt
//    Function to create and add an option to a select at a specific location in the option list.
//
//		@param		selectElem	Select element to remove option from.
//		@param		text		Text of new option.
//		@param		value		Value of new option.
//		@param		atIndex		0-based index indicating where to add option.  Out of range adds to end.

function baseSelectAddOptionAt(selectElem, text, value, atIndex) {
	var newOpt = document.createElement('option');
	newOpt.text = text;
	newOpt.value = value;
	if (atIndex && atIndex >= 0 && atIndex < selectElem.length) {

		//  Adding the option at a certain spot is different between IE and standards, at least for IE7.
		if (globalIs.ie) {
			selectElem.add(newOpt, atIndex);
		} else {
			selectElem.insertBefore(newOpt, selectElem.options[atIndex]);
		}
	} else {
		baseSelectAddOptionObj(selectElem, newOpt);
	}
}


////////////////////////
//  baseSelectAddOptionList
//    This function adds a list of options to a select, in a manner most efficient for the browser.

function baseSelectAddOptionList(selectElem, labels, values) {
	if (globalIs.ie) {
		var arr = new Array(labels.length);
		for (var i=0; i<labels.length; i++) {
			if (i==0 || labels[i] != labels[i-1]) {
				//arr[i] = "<OPTION value='" + values[i] + "'>" + labels[i] + "</OPTION>";
				arr[i] = "<OPTION>" + labels[i] + "</OPTION>";
			}
		}
		selectElem.outerHTML = "<SELECT id='" + selectElem.id + "'>" + arr.join('') + "</SELECT>";
	} else {
	}
}


////////////////////////
//  baseSelectRemoveOptionObj
//    Generic function to remove a given option object from a select object in a browser-independent fashion.
//
//		@param		selectElem	Select element to remove option from.
//		@param		optObj		Option object to remove.

function baseSelectRemoveOptionObj(selectElem, optObj) {
	selectElem.removeChild(optObj);
}


////////////////////////
//  baseSelectRemoveAllOptions
//    Generic function to remove all existing options from a select object in a browser-independent fashion.
//
//		@param		selectElem	Select element to remove all options from.

function baseSelectRemoveAllOptions(selectElem) {
	var optCnt = selectElem.length;
	for (var optNum=optCnt-1; optNum>=0; optNum--) {
		selectElem.remove(optNum);
	}
}


////////////////////////
//  baseSelectMoveOption
//    This function moves an existing option from a given spot to another given spot in the order
//   of the options in the given select object.
//
//		@param		selectElem	Select element to move option of.
//		@param		oldIndex	Index of option to move.
//		@param		newIndex	New spot for index.
//		@retvalue				True if the option was successfully moved (i.e. inputs were valid indices)

function baseSelectMoveOption(selectElem, oldIndex, newIndex) {
	if ((oldIndex < 0 || oldIndex >= selectElem.length) ||
	 (newIndex < 0 || newIndex >= selectElem.length)) {
		return false;
	}
	if (oldIndex == newIndex) {
		return true;
	}
	var opt = selectElem.options[oldIndex];
	selectElem.removeChild(opt);

	var newSpot = selectElem.options[newIndex];
	selectElem.insertBefore(opt, newSpot);

	return true;
}


////////////////////////
//  baseSelectGetVal
//    This function will return the selected value of a given select element.  It detects if the select
//   object can hold single or multiple values, and returns either a value (for single select) or an
//   array (for multiple selects).
//
//		@param		elem		Select element to get value of.
//		@retvalue				Array of values for the select (only one entry if single select)

function baseSelectGetVal(elem) {
	var len = elem.options.length;
	if (elem.multiple) {
		var outArray = new Array();
		for (var i=0, count=0; i<len; i++) {
			if (elem.options[i].selected) {
				outArray[count++] = elem.options[i].value;
			}
		}
		return outArray;
	} else {
		if (elem.selectedIndex >= 0 && elem.selectedIndex < len) {
			return elem.options[elem.selectedIndex].value;
		}
		return '';
	}
}


////////////////////////
//  baseSelectGetOpts
//    This function will return the selected options of a given select element.
//
//		@param		elem		Select element to get value of.
//		@retvalue				Array of options for the select (only one entry if single select)

function baseSelectGetOpts(elem) {
	var outArray = new Array();
	var len = elem.options.length;
	if (elem.multiple) {
		for (var i=0, count=0; i<len; i++) {
			if (elem.options[i].selected) {
				outArray[count++] = elem.options[i];
			}
		}
	} else {
		if (elem.selectedIndex >= 0 && elem.selectedIndex < len) {
			outArray[0] = elem.options[elem.selectedIndex];
		}
	}
	return outArray;
}


////////////////////////
//  baseSelectGetOptIndexes
//    This function will return the indexes of the selected options of a given select element.
//
//		@param		elem		Select element to get value of.
//		@retvalue				Array of option indexes for the select (only one entry if single select)

function baseSelectGetOptIndexes(elem) {
	var outArray = new Array();
	var len = elem.options.length;
	if (elem.multiple) {
		for (var i=0, count=0; i<len; i++) {
			if (elem.options[i].selected) {
				outArray[count++] = i;
			}
		}
	} else {
		if (elem.selectedIndex >= 0 && elem.selectedIndex < len) {
			outArray[0] = elem.selectedIndex;
		}
	}
	return outArray;
}


////////////////////////
//  baseRadioGetVal
//    This function will find and return the value of a checked radio button.  This routine
//   accepts either a radio button or an array of radio buttons.  If it is passed a null,
//   or for some reason none of the radio buttons are checked, it returns the empty string.
//

function baseRadioGetVal(elem) {
	if (!elem) {
		return "";
	}

	var len = elem.length;
	if (typeof len == 'undefined') {
		if (elem.checked)
			return elem.value;
	} else {
		for (var i=0; i<len; i++) {
			if (elem[i].checked) {
				return elem[i].value;
			}
		}
	}
	return "";
}


////////////////////////
//  baseRadioSetVal
//    This function will check the correct radio button, given a value.  This function accepts
//   either a single radio button, or an array.  If the given value matches the value of the
//   given single radio button, it is checked.  If the given value matches one of the values of
//   a given array of radio buttons, that one is checked and the others are unchecked.  This function
//   returns true if a radio button was checked, else returns false.

function baseRadioSetVal(elem, newValue) {
	if (!elem) {
		return false;
	}

	var retVal = false;
	var len = elem.length;
	if (typeof len == 'undefined') {
		elem.checked = (elem.value == newValue.toString());
		retVal = elem.checked;
	} else {
		for (var i=0; i<len; i++) {
			if (elem.value == newValue.toString()) {
				elem[i].checked = true;
				retVal = true;
			} else {
				elem[i].checked = false;
			}
		}
	}
	return retVal;
}


/**
 *  baseForceClick
 *    This function force a click on the given element in a browser-independent fashion.
 */

function baseForceClick(elem) {
	if (elem.onclick) {
		elem.onclick();
	} else if (elem.tagName == 'A') {
		window.location.href = elem.href;
	}
}


/**
 *  baseLoadXMLString - given server request, returns XML response string.
 *  baseParseXMLString - given XML string, parses and returns XML doc.
 *  baseLoadXMLHTTP - given XML request to server, returns XML doc.  Currently only synchronous.
 *  baseLoadXML - given XML result, loads locally into XML doc.
 */

var reStripSpaces = /^\s+(.*)\s*$/m;

function baseLoadXMLString(sXML, async) {
	var qr = new QRequest;
	var oXmlDoc = qr.getHTTPObject();

	sXML = sXML.replace(reStripSpaces, "$1");   // strip spaces
	if (sXML!="") {
		oXmlDoc.open("GET", sXML, false);
		oXmlDoc.send(null);
		return oXmlDoc.responseText;
	}
	return "";
}

function baseParseXMLString(xmlString) {
	var retDoc = baseLoadXML(xmlString);
	return (retDoc === undefined) ? null : retDoc;
}

function baseLoadXMLHTTP(sXML, async) {
	var xmlstr = baseLoadXMLString(sXML, async);
	return baseParseXMLString(xmlstr);
}

function baseLoadXML(xmlString) {
    //BUG 6152: The IE Parser orders attributes in a diffenent order then its DOMParser!!!!
    //          So we have code (lists in some places, that badly assumes that the list will be ordered
    //          in the order that was declared.) So for now we will prefer using the activeX version if possible.
    var retDoc = null;
    if (window.ActiveXObject || "ActiveXObject" in window) {
        try {
            retDoc = new ActiveXObject("Microsoft.XMLDOM");
            retDoc.async = false;
            if (!retDoc.loadXML(xmlString)) {
                if (retDoc.parseError.errorCode != 0) {
                    var myErr = retDoc.parseError;
                    //alert("You have error " + myErr.reason);
                   retDoc = null;
                }
            }
        } catch (e) {
            retDoc = null;
            //here if the activeX version of the dom parser ever goes away!
        }
    }
    
    if (retDoc === null && window.DOMParser) {
        var p = new DOMParser();
        retDoc = p.parseFromString(xmlString, "text/xml");
    } else if (retDoc === null) {
    	return undefined;
    }
    
	return retDoc;
}

function baseLoadXMLDoc(filename) {
	var xmlDoc;
	if (window.ActiveXObject) {
		xmlDoc = new ActiveXObject("Microsoft.XMLDOM");
	} else if (document.implementation && document.implementation.createDocument) {
		xmlDoc = document.implementation.createDocument("", "", null);
	}
	xmlDoc.async = false;
	xmlDoc.load(filename);
	return xmlDoc;
}


////////////////////////
//  baseSetXmlValue
//    This function will set a named value node in an XML doc to the given value, under the element
//   with the given tag name.  If a named value already exists in the element, it's value is replaced
//   with the new given value, else the value is added.

function baseSetXmlValue(xmlDocOrNode, nodeName, attrName, attrVal, valueName, value) {
	var elems = xmlDocOrNode.getElementsByTagName(nodeName);
	for (var i=0,cnt=elems.length; i<cnt; i++) {

		//  If an attribute is supplied, make sure this node has the attribute and its value matches
		//   the given attribute value.
		if (attrName) {
			var attr = elems[i].attributes.getNamedItem(attrName);
			if (!attr || attr.nodeValue != attrVal) {
				continue;
			}
		}

		// Find the node.  If it's there, set it.  If not, add it.
		var valNode = elems[i].getElementsByTagName(valueName)[0];
		if (valNode) {
			valNode.firstChild.nodeValue = value;
		} else {
			var xmlDoc;
			if (xmlDocOrNode.ownerDocument) {
				xmlDoc = xmlDocOrNode.ownerDocument;
			} else {
				xmlDoc = xmlDocOrNode;
			}
			valNode = xmlDoc.createElement(valueName);
			valTxtNode = xmlDoc.createTextNode(value);
			valNode.appendChild(valTxtNode);
			elems[i].appendChild(valNode);
		}
		break;
	}
}

////////////////////////
//  baseSetXmlElemValue
//    This function will set a named text value on the given XML node.  If a value of the given name already
//   exists on the given element, its value is set to the given value.  If not, a new value with the given
//   value name and value are created and attached to the element.  This function returns the value node.

function baseSetXmlElemValue(xmlDoc, nodeElem, valueName, value) {
	var valNode = nodeElem.getElementsByTagName(valueName)[0];
	if (valNode) {
		valNode.firstChild.nodeValue = value;
	} else {
		valNode = xmlDoc.createElement(valueName);
		valTxtNode = xmlDoc.createTextNode(value);
		valNode.appendChild(valTxtNode);
		nodeElem.appendChild(valNode);
	}
	return valNode;
}

////////////////////////
//  baseSetXmlElemTextValue
//    This function sets the text node value of a given XML node element.  If the node does not
//   have a text value, one is added, else the existing text value is reset to the given value.
//   This function returns the new or reset text node.

function baseSetXmlElemTextValue(xmlDoc, nodeElem, value) {
	if (!nodeElem.firstChild) {
		newTextNode = xmlDoc.createTextNode(value);
		nodeElem.appendChild(newTextNode);
		return newTextNode;
	} else {
		nodeElem.firstChild.nodeValue = value;
		return nodeElem.firstChild;
	}
}


////////////////////////
//  baseSetOrCreateXmlElemTextValue
//    This function will set a text element on a node.  It will also check to see if the given
//   node exists, and if not, create it in the given parent.

function baseSetOrCreateXmlElemTextValue(xmlDoc, nodeElem, parentNodeName, nodeName, value) {
	if (!nodeElem) {
		//  Create the node.
		nodeElem = xmlDoc.createElement(nodeName);

		//  If no parent node name given, find the top node, then add the new node to it.
		if (parentNodeName == '') {
			var parentNode = xmlDoc.firstChild;
			while (parentNode && parentNode.nodeType != OUR_ELEMENT_NODE) {
				parentNode = topNode.nextSibling;
			}
		} else {
			var parentNode = xmlDoc.getElementsByTagName(parentNodeName)[0];
		}
		parentNode.appendChild(nodeElem);
	}
	return baseSetXmlElemTextValue(xmlDoc, nodeElem, value);
}


////////////////////////
//  baseGetXmlValue
//    This function retrieves the value of the given node in an XML doc.  It returns the first
//   value of this named node found.  Optionally, a childNodeName can be supplied to return the
//   value of that named child in the first found named node.
//
//    Optionally, a attribute name/value can be supplied, and the
//   named node is first filtered by this attribute name/value.  If supplied, the node must have
//   this attribute name/value to be considered.

function baseGetXmlValue(xmlDocOrNode, nodeName, childNodeName, attrName, attrVal) {
	var node = baseGetXmlNode(xmlDocOrNode, nodeName, childNodeName, attrName, attrVal);
	if (node) {

		//  If the node supports textContent, return it.  This code gets around a 4K size limitation
		//   on Firefox for nodeValues.  But, using textContent, you get the whole thing even if > 4K long.
		if (typeof node.textContent != "undefined") {
			return node.textContent;
		} else if (node.firstChild) {
			return node.firstChild.nodeValue;
		}
	}
	return null;
}


////////////////////////
//  baseGetXmlAttr/baseGetXmlNodeAttr
//    These functions return the value of a named attribute on a given XML node.  If the given
//   node is null, or the named attribute does not exist, then null is returned, else the
//   value of the attribute is returned.

function baseGetXmlNodeAttr(xmlNode, attrName) {
	if (!xmlNode || !xmlNode.attributes) {
		return null;
	}
	var attr = xmlNode.attributes.getNamedItem(attrName);
	if (attr == null) { return null; }
	return attr.nodeValue;
}

function baseGetXmlAttr(xmlDoc, nodeName, attrName) {
	var node = baseGetXmlNode(xmlDoc, nodeName);
	if (node == null) { return null; }
	return baseGetXmlNodeAttr(node, attrName);
}


////////////////////////
//  baseGetXmlNode
//    This function retrieves a given named node in an XML doc.  It returns the first
//   node that matches the given name.  Optionally, a childNodeName can be supplied to return the
//   named child in the first found named node.
//
//    Optionally, a attribute name/value can be supplied, and the
//   named node is first filtered by this attribute name/value.  If supplied, the node must have
//   this attribute name/value to be considered.

function baseGetXmlNode(xmlDocOrNode, nodeName, childNodeName, attrName, attrVal) {
	var elems = xmlDocOrNode.getElementsByTagName(nodeName);
	for (var i=0,cnt=elems.length; i<cnt; i++) {

		//  If an attribute is supplied, make sure this node has the attribute and its value matches
		//   the given attribute value.
		if (attrName) {
			var attr = elems[i].attributes.getNamedItem(attrName);
			if (!attr || attr.nodeValue != attrVal) {
				continue;
			}
		}

		//  If the child node is requested, see if it exists.  If so, return its value, else keep looking.
		if (childNodeName) {
			// Find the node.  If it's there, return its value.  If not, keep going.
			var valNode = elems[i].getElementsByTagName(childNodeName)[0];
			if (valNode && valNode.firstChild) {
				return valNode;
			}

		//  If no child node is supplied, simply return the named nodes value.
		} else {
			return elems[i];
		}
	}
	return null;
}


////////////////////////
//  baseGetXmlValues
//    This function returns the attribute values of the given named node(s).  It returns an array, one
//   entry per node.  The entry is itself an associative array of the attribute names and values on
//   each node.

function baseGetXmlValues(xmlDocOrNode, nodeName) {
	var out = new Array();
	var elems = xmlDocOrNode.getElementsByTagName(nodeName);
	for (var i=0,cnt=elems.length; i<cnt; i++) {
		out[i] = new Object;
		for (var i1=0,cnt1=elems[i].attributes.length; i1<cnt1; i1++) {
			out[i][elems[i].attributes[i1].nodeName] = elems[i].attributes[i1].nodeValue;
		}
	}
	return out;
}


////////////////////////
// baseTransformXML 
//   This function uses a given XSL doc to transform a given XML doc.  It places
//  the resulting node as the child of the given resultsElement.  If topLevelParams
//  is given, then node values can be preset into the top level XML node.
//
//  @param
//  @xmlDoc				XML doc to transform.
//	@xslDoc				XSL doc to use to transform the XML.
//	@resultsElement		Where to place results, as the child of this element.
//	@topLevelParams		Optional array of (name, value) pairs that represent top level XML node/values.  If
//						 the XML nodes exist, their values are set to the given value, else they are added.
//
function baseTransformXML(xmlDoc, xslDoc, resultsElement, topLevelParams) {

	//  If given top level params, set them in the XML doc.
	if (topLevelParams) {

		//  Find the top node.
		var topNode = xmlDoc.firstChild;
		while (topNode && topNode.nodeType != OUR_ELEMENT_NODE) {
			topNode = topNode.nextSibling;
		}

		var cnt = topLevelParams.length;
		for (var i=0; i<cnt; i+=2) {
			var nextName = topLevelParams[i];
			var nextValue = topLevelParams[i+1];

			// Find the node.  If it's there, set it.  If not, add it.
			var valNode = topNode.getElementsByTagName(nextName)[0];
			if (valNode) {
				valNode.firstChild.nodeValue = nextValue;
			} else {
				valNode = xmlDoc.createElement(nextName);
				valTxtNode = xmlDoc.createTextNode(nextValue);
				valNode.appendChild(valTxtNode);
				topNode.appendChild(valNode);
			}	
		}
	}

	//  IE doesn't allow object detection on XML doc methods, apparently, so do browser sniffing.
	//if (xmlDoc.transformNode) {
	if (globalIs.ie) {
		resStr = xmlDoc.transformNode(xslDoc);
		if (resultsElement) {
			resultsElement.innerHTML = resStr;
		}
	} else {
		var xsltProc = new XSLTProcessor();
		xsltProc.importStylesheet(xslDoc);
		if (resultsElement) {
			var frag = xsltProc.transformToFragment(xmlDoc, document);

			while (resultsElement.firstChild) {
				resultsElement.removeChild(resultsElement.firstChild);
			}

			resultsElement.appendChild(frag);
		}
	}
}
		

/**
 *  baseTransformXML2XML
 *    This function transforms a given XML doc using a given XSL doc into a new XML doc, and returns that
 *   new XML document object.
 */

function baseTransformXML2XML(xmlDoc, xslDoc) {

	//  IE doesn't allow object detection on XML doc methods, apparently, so do browser sniffing.
	//if (xmlDoc.transformNode) {
	if (globalIs.ie) {
		resStr = xmlDoc.transformNode(xslDoc);
		return baseLoadXML(resStr);
	} else {
		var xsltProc = new XSLTProcessor();
		xsltProc.importStylesheet(xslDoc);
		return xsltProc.transformToDocument(xmlDoc);
	}
}

/**
 *  baseSendUIEvent
 *     This function allows the firing of a UI event on the given element.
 *
 * @param     
 * @elem		Element to fire on.
 * @eventName	Event to fire WITHOUT the 'on' prefix.
 */

function baseSendUIEvent(elem, eventName)
{
	if (elem.dispatchEvent && document.createEvent) {
        var evt = document.createEvent("Events");
        evt.initEvent(eventName, true, true);
        elem.dispatchEvent(evt);
    } else if (elem.fireEvent) {
    	elem.fireEvent('on' + eventName);
	} else {
	    //crap!
	}
}
		

/* +BI+ element naming
 * GetLayerDoc
 *    This function does a base lookup of a layer, and returns the associated document.
 *   It allows the input parameter _layer to be empty, in which case it returns the current
 *   document.
 *
 * @param
 * @_layer	Name of the layer whose document should be retrieved (can be '').
 */

function GetLayerDoc(_layer) { 
	var layerDoc = document;
	if (_layer) {
		layerDoc = layerDoc.getElementById(_layer).ownerDocument;
	}
	return layerDoc;
}


function baseTransferAction(evt) {
	evt = (evt || window.event);

	//  Get active element.  Should now always be set (thanks to baseOnLoad).
	var theElement;
	if (typeof document.activeElement != "undefined") {
		theElement = document.activeElement;
	}
	else if (evt && evt.explicitOriginalTarget)		// Should not be needed, but...
		theElement = evt.explicitOriginalTarget;
	else
		if (console) console.log("baseTransferAction can't find active element...");

	if (theElement && theElement.name && (theElement.type == "button" || theElement.type == "submit" || theElement.tagName == 'A'))  {
		SetNoCheckRequired();
		//ResetFormChangedFlag();

		// Get the list of all links and buttons of the page
		var buttonsElements = document.getElementsByTagName('input');
		var linksElements = document.getElementsByTagName('A');

		// Disable all action buttons/links we want to see disabled once the page has been already submitted
		// It makes sure we dont create object/transaction multiple times
		for (var i = 0; i < buttonsElements.length; i++) { // Buttons
			var button = buttonsElements[i];
			if (button.name != 'exportbutton' && button.getAttribute('disableonsubmit') == "true") {
				button.disabled = true;
			}
		}
		for (var i = 0; i < linksElements.length; i++) { // Links
			var link = linksElements[i];
			if (link.name != 'exportbutton' && link.getAttribute('disableonsubmit') == "true") {
				link.disabled = true;
				link.onclick = function(){return false}; // Added for anchor as it is not possible to disable it
			}
		}
		
		var buttonName = theElement.name;
		var buttonValue = theElement.value;	

		// Disable the origin button
		if(buttonName != 'exportbutton') {
			theElement.disabled = true;
			theElement.onclick = function(){return false}; // Added for anchor as it is not possible to disable it
		}

		// Pass the origin button value to know the action later
		var xfrButtonName = buttonName.replace(".", "_");
		var xfrButtonVar = document.createElement('input');
		xfrButtonVar.setAttribute('type', 'hidden');
		xfrButtonVar.setAttribute('name', xfrButtonName);
		xfrButtonVar.setAttribute('value', buttonValue);

		var formObj = document.forms[0];
		formObj.appendChild(xfrButtonVar);		
	}
	return true;
}

function baseGetClientWidth() {
   if( typeof( window.innerWidth ) == 'number' ) {
       //Non-IE
        return window.innerWidth;
     } else if( document.documentElement && document.documentElement.clientWidth ) {
       //IE 6+ in 'standards compliant mode'
         return document.documentElement.clientWidth;
     } else  {
       //IE 4 compatible
         return document.body.clientWidth;
     }

}

function baseGetClientHeight() {
    if( typeof( window.innerWidth ) == 'number' ) {
        //Non-IE
        return window.innerHeight;
      } else if( document.documentElement && document.documentElement.clientHeight ) {
        //IE 6+ in 'standards compliant mode'
         return document.documentElement.clientHeight;
      } else  {
        //IE 4 compatible
          return document.body.clientHeight;
      }
}


/**
 *  baseGetObjectLeft/Top
 *    These functions return the left/top of the given element, and can be used to determine
 *   the absolute position of an element.  They allow an optional parameter to remember the
 *   overall body padding, which can be returned by a subsequent call to baseGetLastLeft/TopPad.
 */

var lastLeftPad = 0;
var lastTopPad = 0;
function baseGetLastLeftPad() { return lastLeftPad; }
function baseGetLastTopPad() { return lastTopPad; }

function baseGetObjectLeft(elem, optionalRememberPadding) { 
	var left = 0; 
	while (elem && elem.tagName != "DIV" && elem.tagName != "BODY") { 
		left += elem.offsetLeft; 
		elem = elem.offsetParent; 
	} 

	if (typeof optionalRememberPadding != 'undefined') {
		while (elem && elem.tagName != "BODY") {
			elem = elem.offsetParent;
		}
		var padding = elem.style.paddingLeft;
		if (padding != "") {
			lastLeftPad = Number(padding.substring(0,(padding).indexOf('px')));
		} else {
			lastLeftPad = 0;
		}
	}
	return left;
} 

function baseGetObjectTop(elem, optionalRememberPadding) { 
	var top = 0; 
	while (elem && elem.tagName != "DIV" && elem.tagName != "BODY") { 
		top += elem.offsetTop; 
		elem = elem.offsetParent; 
	} 
	if (typeof optionalRememberPadding != 'undefined') {
		while (elem && elem.tagName != "BODY") {
			elem = elem.offsetParent;
		}
		var padding = elem.style.paddingTop;
		if (padding != "") {
			lastTopPad = Number(padding.substring(0,(padding).indexOf('px')));
		} else {
			lastTopPad = 0;
		}
	}
	return top;
} 

/**
 *  baseHtmlToUnicode
 *    This function translates HTML entities (&uxxxx; and &name;) into their true unicode characters that
 *   can be displayed by the browser.
 *
 *  baseHtmlToUnicode2 is an alternative version that decodes without use of an intermediate textarea,
 *   if for any reason such a text area cannot be created and used.  However, it does not translate
 *   non-numeric html entities (e.g. '&nbsp').
 */

function baseHtmlToUnicode(str) {

	//  If there are no ampersands, there are no html entities, so just return the string.
	if (str.indexOf("&") == -1) {
		return str;
	}

	//  Our method involves using a textarea to do the HTML translation, so create a temp one.
	var ta = document.createElement("textarea");

	//  IE mangles non-entity uses of ampersand, so we need a more explicit approach.
	if (globalIs.ie) {
		//  First do any numeric HTML entities
		str = baseHtmlToUnicode2(str, false);

		//  Now, find and replace all the named html entities individually.  This avoids
		//   replacing any non-html entity usage of ampersands (e.g. 'H&K').
		arr = str.match(/&[a-zA-Z]{1,10};/g);
		if (arr != null) {
			for (var x=0; x<arr.length; x++){
				ta.innerHTML = arr[x];
				str = str.replace(arr[x], ta.value);
			}
		}
		return str;

	//  Non-IE browsers handle the conversion properly using a textarea.
	} else {
		ta.innerHTML = str.replace(/</g, "&lt;").replace(/>/g,"&gt;");
		return ta.value;
	}
}

function baseHtmlToUnicode2(str, decodeFirst) {
	if (decodeFirst) {
		str = decodeURIComponent(str);
	}
	arr = str.match(/&#[0-9]{1,5};/g);
		
	// if no matches found in string then skip
	if (arr!=null) {
		for(var x=0;x<arr.length;x++){
			m = arr[x];
			c = m.substring(2,m.length-1); //get numeric part which is refernce to unicode character
			// if its a valid number we can decode
			if(c >= -32768 && c <= 65535){
				// decode every single match within string
				str = str.replace(m, String.fromCharCode(c));
			}else{
				str = str.replace(m, "");
			}
		}			
	}
	return str;
}

/**
 *  baseEnterBtnClick - simple onkeypress callback to send enter to associated button.
 */
function baseEnterBtnClick(e, buttonId) {
    e = e || window.event;
    if (e.keyCode == 13) {
        document.getElementById(buttonId).click();
    }
    return false;
}

// +BI+ Browser capability sniffing
/**
 * newIs
 *
 *    This function returns a browser-capability object.  It encapsulates any necessary
 *   browser sniffing - for example, Unicode character support.  Although browser sniffing
 *   is considered bad practice, it is necessary in some isolated cases, so at least we've
 *   centralized it in one spot.  Note that this object exposes capabilities, such as
 *   'canDisplayUnicode', that are externally not version specific, hopefully making it easier
 *   to adjust to new browser capabilities.
 *
 */

function getInternetExplorerVersion()
// Returns the version of Internet Explorer or a -1
// (indicating the use of another browser).
{
	var rv = -1; // Return value assumes failure.
	var mv = -1; // Return value assumes failure.

	var ua = navigator.userAgent;
	var re  = new RegExp("MSIE ([0-9]{1,}[\.0-9]{0,})|Trident/.*rv:([0-9]{1,}[\.0-9]{0,})");
	if (re.exec(ua) != null) {
		rv = parseFloat( RegExp.$1 || RegExp.$2 );
		mv = RegExp.$1;
	}
	return {major: rv, minor: mv};
}

function getNTVersion()
{
    var ua = navigator.userAgent;
    var re  = new RegExp("Windows NT ([0-9]{1,}\.[0-9]{0,})");
    if (re.exec(ua) != null) {
        return parseFloat( RegExp.$1 );
    }
    return -1;
}

function newIs() {
	var agt=navigator.userAgent.toLowerCase();
	this.major = parseInt(navigator.appVersion);
	this.minor = parseFloat(navigator.appVersion);

    var ver = getInternetExplorerVersion();
	this.major = (ver.major > 0) ? ver.major : this.major;
	this.minor = (ver.minor > 0) ? ver.minor : this.minor;

	// Turn off old 'nav' code.
	//this.nav = ((agt.indexOf('mozilla')!=-1) && ((agt.indexOf('spoofer')==-1) && (agt.indexOf('compatible') == -1)));
	this.nav = false;
	this.nav2 = (this.nav && (this.major == 2));
	this.nav3 = (this.nav && (this.major == 3));
	this.nav4 = (this.nav && (this.major == 4));
	this.nav4up = this.nav && (this.major >= 4);

	this.ie   = ( agt.indexOf('msie') != -1 || agt.match(/trident\//) );
	this.ie3  = (this.ie && (this.major == 2));
	this.ie4  = (this.ie && (this.major == 4));
	this.ie4up  = this.ie  && (this.major >= 4);

	this.ie5  =   this.ie && (this.major == 5); 
	this.ie5up  = this.ie  && (this.major >= 5);
	
    this.ie6up  = this.ie  && (this.major >= 6);
    this.ie7up  = this.ie  && (this.major >= 7);
    this.ie7dn  = this.ie  && (this.major <= 7);
    this.ie8up  = this.ie  && (this.major >= 8);
    this.ie9dn  = this.ie  && (this.major <= 9);
    this.ie10up  = this.ie  && (this.major >= 10);
    this.ie10dn  = this.ie  && (this.major <= 10);
    this.ie11up  = this.ie  && (this.major >= 11);

	this.opera = (agt.indexOf('opera') != -1);
	this.konqueror = (agt.indexOf('konqueror') !=-1);
	this.w3c = (agt.indexOf('gecko')!=-1);
	this.gecko = (agt.indexOf('gecko')!=-1);
    this.applewebkit = (agt.indexOf('applewebkit')!=-1);
    this.chrome = (agt.indexOf('chrome')!=-1);
    this.safari = !this.chrome && (agt.indexOf('safari')!=-1);
	this.ff = agt.indexOf(' firefox/') != -1;

    this.maybemetro  = false;
    if ( this.ie ) {
        if ( this.major >= 10 ) {
            var ntVer = getNTVersion();
            if ( ntVer >= 6.2 ) {
                if ( agt.indexOf('arm') != -1 ) {
                    this.winrt = true;
                    this.maybemetro  = true;
                }
            }
            if ( ! this.maybemetro ) {
                // Fires in full-screen (F11) desktop mode.  Oh well.
                this.maybemetro  = window.innerHeight == screen.height;
            }
        }
    } else if ( this.ff ) {
        // Figure out someday, but who is going to do this?
    } else if ( this.chrome ) {
        // Figure out someday, but who is going to do this?
    }

	// Current Internet Explorer does not support unicode display - check for IE8?
	this.canDisplayUnicode = (this.ie) ? false : true;
	
	// Touch devices
	this.touch = (agt.indexOf('iphone')!=-1) || (agt.indexOf('ipad')!=-1) || (agt.indexOf('android')!=-1) || (agt.indexOf('windows phone')!=-1) || (agt.indexOf('webos')!=-1);
    this.mobileSafari = this.safari && this.touch;
}

//  Create global for accessing browser capabilities.  At least we will have browser sniffing isolated.
var globalIs = new newIs();


/*
* Display a loading panel in the page
*/
function DisplayLoadingPanel(ptitle){

	// Set the default properties
	ptitle = !ptitle ? "Loading, please wait..." : ptitle;
	
	// Create the YAHOO namespace
	YAHOO.namespace("loadingPanel");

	// Create the panel only the first time
	if(!YAHOO.loadingPanel.panel) {
		YAHOO.loadingPanel.panel = new YAHOO.widget.Panel("wait",  
						{ width: '250px', 
						  fixedcenter: true, 
						  close: false, 
						  draggable: false, 
						  zindex:1000,
						  modal: true,
						  visible: false
						} 
					);

		YAHOO.loadingPanel.panel.setHeader(ptitle);
		YAHOO.loadingPanel.panel.setBody("<center><img src='../resources/thirdparty/yui/images/rel_interstitial_loading.gif'></center>");
		YAHOO.loadingPanel.panel.render(document.body);
	} else { 
		// If the panel already exists we only change specific properties for performences reasons
		YAHOO.loadingPanel.panel.setHeader(ptitle);
	}

	// Show the Panel
	YAHOO.loadingPanel.panel.show();
}

function StripXMLData(response) {
	if (response.length) {		
		var nodevalue = new Array();
		for (var i = 0; i < response.length; i++) {
			if (response[i].firstChild && response[i].firstChild.data)
			{
				nodevalue[i] = new Array(response[i].firstChild.data);
			} else {
				nodevalue[i] = '';
			}			
		}	
	}
	return nodevalue;
}

/*
* Display a message box in the page
*/
function DisplayMsgBox(ptitle,msg){

	// Set the default properties
	ptitle = !ptitle ? "Intacct" : ptitle;
	
	// Create the YAHOO namespace
	YAHOO.namespace("msgBox");

	// Create the panel only the first time
	if(!YAHOO.msgBox.panel) {
		YAHOO.msgBox.panel = new YAHOO.widget.Panel("wait",  
						{ width: '500px', 
						  fixedcenter: true, 
						  close: false, 
						  draggable: false, 
						  zindex:1000,
						  modal: true,
						  visible: false
						} 
					);

		YAHOO.msgBox.panel.setHeader(ptitle);
		YAHOO.msgBox.panel.setBody("<center>"+msg+"</center><br><center><input type=button value=Ok onclick='YAHOO.msgBox.panel.hide()'> </center>");
		YAHOO.msgBox.panel.render(document.body);
	} else { 
		// If the panel already exists we only change specific properties for performences reasons
		YAHOO.msgBox.panel.setHeader(ptitle);
	}

	// Show the Panel
	YAHOO.msgBox.panel.show();
}

function replaceXMLEscapeCharacters(inputString){

    if (typeof inputString == 'string') {
        var resultString = inputString;
        if ((typeof inputString == 'undefined') || (!inputString)) {
            return "";
        }
        if(inputString.indexOf("&") != -1){
            resultString = replaceAllChars(inputString,"&","&amp;");
            inputString = resultString;
        }
        if(inputString.indexOf("'")!= -1){
            resultString = replaceAllChars(inputString, "'","&apos;");
            inputString = resultString;
        }
        if(inputString.indexOf('"')!= -1){
            resultString = replaceAllChars(inputString, '"',"&quot;");
            inputString = resultString;
        }
        if(inputString.indexOf('>')!= -1){
            resultString = replaceAllChars(inputString, '>',"&gt;");
            inputString = resultString;
        }
        if(inputString.indexOf('<')!= -1){
            resultString = replaceAllChars(inputString, '<',"&lt;");
            inputString = resultString;
        }
        return resultString;
    } else {
        return inputString;
    }
}

/*
 * unreplaceXMLEscapeCharacters
 *  This function does a 'decoding' of special characters back to their original (an inverse of util_encode).
 */

/*  Replaced chars.  Not sure if a single complex RegEx is better than separate ones for performance or not */
var unencodeChars = [
 new RegExp("&apos;",'g'), "'", new RegExp("&#039;",'g'), "'",
 new RegExp("&quot;",'g'), '"', new RegExp("&#034;",'g'), '"',
 new RegExp("&gt;",'g'), ">", new RegExp("&#062",'g'), ">",
 new RegExp("&lt;",'g'), "<", new RegExp("&#060",'g'), "<",
 new RegExp("&amp;",'g'), "&", new RegExp("&#038",'g'), "&"];  // Do the amp last!  Prevents double unencoding.

function unreplaceXMLEscapeCharacters(inputString){

    if (typeof inputString == 'string') {
        var resultString = inputString;
        if ((typeof inputString == 'undefined') || (!inputString)) {
            return "";
        }

        //  Replace any html entities - first do a quick test for ampersand.
        if (inputString.indexOf("&") != -1){
            for (var i=0; i<unencodeChars.length; i+=2) {
                inputString = inputString.replace(unencodeChars[i], unencodeChars[i+1]);
            }
        }

        return inputString;
    } else {
        return inputString;
    }
}

function replaceAllChars(txt, replace, with_this){
    return txt.replace(new RegExp(replace,'g'),with_this);
}

Array_contains = function(arr, value)
{
	if (arr) {
		for( var ix = 0; ix < arr.length; ix++ )
		{
		    if( arr[ix] === value ) return ix;
		}
	}
 	return -1;
};


function addClassName(element, className)
{
    if ( ! element ) return;
    var classes = element.className.split(' ');
    if( Array_contains(classes, className) < 0 )
    {
        if( element.className.length > 0 ) element.className += ' ';
        element.className += className;
    }
}

function removeClassName(element, className)
{
    if ( ! element ) return;
    var classes = element.className.split(' ');
    var idx = Array_contains(classes, className);
    if( idx >= 0 )
    {
        classes.splice(idx, 1);
        element.className = classes.join(' ');
    }
}

function hasClassName(element, className)
{
    if ( ! element ) return;
    var classes = element.className.split(' ');
    return Array_contains(classes, className) >= 0;
}

if ( window.jq ) {
    jq(function() {
        $fancyBoxes = jq('a.fancybox');
        if ( $fancyBoxes.length > 0) {
            $fancyBoxes.fancybox();
        }
    });
}

function baselib_stopPropagation (event) {
    //needed for IE8 in quirks mode pages. Normally it looks like YUI events were not always being killed.
    try {
        //sometimes YUI blows becuase we are using and old version!
        YAHOO.util.Event.stopEvent(event); //yahoo events must be explictly stopped because in IE9 our version of YUI fails!
    } catch (e) {}

    if (event.stopPropagation) event.stopPropagation();
    if (event.stopImmediatePropagation) event.stopImmediatePropagation();

    //this crap is for IE...
    if (event.cancelBubble) event.cancelBubble = true;
    if (event.preventDefault) event.preventDefault();
    event.returnValue = false;
    return false;
}

function findEffectiveZIndex(elem, minZIndex) {
    var t = jq(elem);
    // $.zIndex() was deprecated in JQ-UI 1.11, so we need to include it as our own custom function, 'getEffectiveZIndex'
    var getEffectiveZIndex = function ($elem) {
        if ($elem && $elem.length ) {
            var elem = jq( $elem[ 0 ] ), position, value;
            while ( elem.length && elem[ 0 ] !== document ) {
                // Ignore z-index if position is set to a value where z-index is ignored by the browser
                // This makes behavior of this function consistent across browsers
                // WebKit always returns auto if the element is positioned
                position = elem.css( "position" );
                if ( position === "absolute" || position === "relative" || position === "fixed" ) {
                    // IE returns 0 when zIndex is not specified
                    // other browsers return a string
                    // we ignore the case of nested elements with an explicit value of 0
                    // <div style="z-index: -10;"><div style="z-index: 0;"></div></div>
                    value = parseInt( elem.css( "zIndex" ), 10 );
                    if ( !isNaN( value ) && value !== 0 ) {
                        return value;
                    }
                }
                elem = elem.parent();
            }
        }
        return 0;
    }
    var zidx = getEffectiveZIndex(t);
    if (minZIndex != undefined) {
        return (zidx < minZIndex) ? minZIndex : zidx;
    }
    return zidx;
}

function baseSetURLParams(url, params) {

    url = url || window.location.href || '';
    url = url.split("#")[0];
    url =  url.match(/\?/) ? url : url + '?';

    for ( var key in params ) {
        var re = RegExp( '(&|(\\?))' + key + '=?[^&]*', 'g' );
        // ?.param=1
        // ? ... &.param=1
        // ? ...   - add new param .param
        url = url.replace( re, '\$2');
        url += '&' + key + '=' + params[key];
    }
    // cleanup url
    url = url.replace(/\?&/, '?');

    return url;
}

function setCookieValue(cookie_name, cookie_value, cookie_expire) {
    var cookie = cookie_name + "=" + escape(cookie_value);
    if(cookie_expire){
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + cookie_expire);
        cookie += ";expires=" + exdate.toGMTString();
    }
    cookie += "; secure";
    document.cookie = cookie;
    return true;
}

/**
 * Calculate term due date
 */
function CalculateDueDate(term_mnth, term_day, from_date)
{
    if (from_date == '') {
        return false;
    }
    var theDate = new Date(from_date);
    if (term_mnth == '') {
        return AddDays(from_date, term_day, null);
    }
    return AddMonths(from_date, term_mnth, null, term_day);
}

var getParamFromUrlMap = null;
function getParamFromUrl(paramName) {
    if ( getParamFromUrlMap === null ) {

        getParamFromUrlMap = {};

        var params = location.href.substr(location.href.indexOf("?") + 1);
        params = params.split("&");

        // split param and value into individual pieces
        for (var i = 0; i < params.length; i++) {
            parts = params[i].split("=");
            // Purposefully not decoding.  If you want it decoded, do it yourself.  Decoding here leads to
            // ambiguities like, should a + become a space?  This is only known within the context of the
            // page and parameter.
            getParamFromUrlMap[parts[0]] = parts[1];
        }

    }

    return getParamFromUrlMap[paramName];
}

function addEscapeForCharacter( input_string, charctr ) {
	var output_string = "";
    var specialCharacter = charctr;
    for(var i=0;i<input_string.length;i++) {
        var character = input_string.charAt(i);
        if(specialCharacter.indexOf(character) != -1) {
            output_string = output_string + "\\" + character;
        } else {
            output_string = output_string + character;
        }
    }
    return output_string;
}


function indirectPostLaunch(name,helpwinparam,url,prefix,postArgs)
{
	var indirectURL = prefix + "postindirect.html";
	window.indirectPostArgs = postArgs;
	window.indirectPostURL = url;
	WinPop(indirectURL, name, helpwinparam);
}

function indirectPost()
{
	// Sanity checking to see if the opener global variables are set
	var winHandle = window.opener;
	if (winHandle && winHandle.indirectPostArgs && winHandle.indirectPostURL) {

		var postArgs = winHandle.indirectPostArgs;
		var postURL = winHandle.indirectPostURL;

		// Make sure the variables are of the correct type and non empty url
		if ( ( typeof postArgs === 'array' || typeof postArgs === 'object' )
			&& typeof postURL === 'string' && postURL.length > 0 ) {

			doPost(postURL, postArgs);
			return;
		}
		//alert("postArgs or postURL types are not correct");
	}

	//alert("postArgs or postURL not set");
	window.close();
}

function showAuditTrailWindow(operation, entity, entityKey, sess)
{
    if (sess == null) {
        sess = gSess;
    }
    var url = 'editor.phtml?.popup=3&.ydialog=1&.sess=' + sess + '&.op=' + operation + '&.showgrid=1'
        + '&.auditEntity=' + entity + '&.auditEntityKey=' + encodeURIComponent(entityKey) + "&.dcl=1";
    Launch(url, 'Audit Trail', 900, 600);
    return true;
}

function doPost(dst, postArgs, theWindow)
{
    theWindow = theWindow || window;
    var theDocument = theWindow.document;
	var form = theDocument.createElement("form");
	form.setAttribute("method", 'post');
	form.setAttribute("action", dst);

	for(var key in postArgs) {
		if ( postArgs.hasOwnProperty(key) ) {
			var hiddenField = theDocument.createElement("input");
			hiddenField.setAttribute("type", "hidden");
			hiddenField.setAttribute("name", key);
			hiddenField.setAttribute("value", postArgs[key]);
			form.appendChild(hiddenField);
		}
	}

    theDocument.body.appendChild(form);
	form.submit();

	return false;
}

/**
 * Code needed for the administrator password page (e.g. user info, my preferences screens).
 * On page load, append the html which is necessary for showing admin password dialog window
 * while editing/adding new users. This is the logic borrowed from platform.
 * Please see more info on pt_dialog.inc and FormEditor.cls
 *
 * @return void
 */
function appendAdminPasswordDialogWindow()
{
    var dialogWindow      = document.getElementById('rbe_yuiDialog');

    // If adminpassword dialog window not available, append it
    if ( !dialogWindow ) {
        var divOuter          = document.createElement('div');
        var divInner          = document.createElement('div');
        var hdDiv             = document.createElement('div');
        var bdDiv             = document.createElement('div');
        var ftDiv             = document.createElement('div');
        divOuter.className    = 'yui-skin-sam';
        divInner.className    = 'yuiDialog';
        divInner.id           = 'rbe_yuiDialog';
        divInner.style.zIndex = 10;
        hdDiv.className       = 'hd';
        bdDiv.className       = 'bd';
        ftDiv.className       = 'ft';
        divInner.appendChild(hdDiv);
        divInner.appendChild(bdDiv);
        divInner.appendChild(ftDiv);
        divOuter.appendChild(divInner);
        document.body.appendChild(divOuter);
        dialogWindow = document.getElementById('rbe_yuiDialog');
    }
    // somehow the dialogbox height and width are not properly set by function rbf_dialogAutoSize
    // we do it manually here since we can not change anythng platform specific code
    dialogWindow.style.width  = "400px";
    dialogWindow.style.height = "200px";
    dialogWindow.style.height = "200px";
}

/**
 * Binds the 'ENTER' key to the 'done' field on admin password popup window
 *
 * @return Bool True or False
 */
function focusAdminPasswordWindow()
{
    var adminPassField = jq("#rbe_dialogIframe").contents().find("#adminpwd");
    var doneField      = jq("#rbe_dialogIframe").contents().find("#adminpassDone");
    adminPassField.bind(
        "keydown",
        function (e) {
            if ( e.which == 13 ) {
                doneField.trigger('click');
            }
        }
    );
    return true;
}

function isValueInteger(value) {
	return typeof value === 'number' && Number.isFinite(value) && Math.floor(value) === value;
}

//DO NOT REMOVE THE FOLLOWING COMMENTS!!
/*URLCleanParams.js*/
/*end URLCleanParams.js*/

function generateDynamicUrl(url) {
  var regex = /\{(.*?)\}/g;
  return url.replace(regex, function(e) {
    var field = e.replace(/[{}]/g, '');
    return field && window[field] ? window[field] : '';
  });
}

/* this function will give EOM for passed date */
function getEOM(inputDate)
{
    var dt = new Date().parseFromSystemFormat(inputDate);
    // GET THE MONTH AND YEAR OF THE SELECTED DATE.
    var month = dt.getMonth(),
        year = dt.getFullYear();
    var LastDay = new Date(year, month + 1, 0).toString("MM/dd/yyyy");
    return LastDay;
}

/* This function will calculate due date including new EOM terms */
// termDueDate is an array 
function CalculateDueDateEOM(termDueDate, from_date, format) {
	format = format || null;
    if (from_date == '') {
        return;
    }
    theDate = new Date(from_date);
    if (termDueDate[1]) {
        if (termDueDate[0] == 'AE') {
            var endOFMonth = getEOM(from_date);
            return AddDays(endOFMonth, termDueDate[1], format);
        } else if (termDueDate[0] == 'EE') {
            var newDate = AddDays(from_date, termDueDate[1], format);
            return getEOM(newDate);
        } else if(termDueDate[0] == '') {
            return AddDays(from_date, termDueDate[1], format);
        } else {
             return AddMonths(from_date, termDueDate[0], format, termDueDate[1]);
         }
    } else {
        return AddDays(from_date, termDueDate[0], format);
    }
}