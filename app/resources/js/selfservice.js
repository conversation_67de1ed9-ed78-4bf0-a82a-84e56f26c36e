	var textonload = true;
	var casefldval = '';
	var appareafldval='';
	var subjectfldval= '';

	function HideShowHelpDiv(vis) {
		divObj = document.getElementById("helpdiv");
		if (divObj)
		{
			divObj.style.visibility = vis;
		}
	}
	
	function ValidateForm(){
		
		casefldval=document.getElementById("caseId").value;
		appareafldval=document.getElementById("appareaId").value;
		subjectfldval=document.getElementById("subjectId").value;
        if(casefldval == '' || appareafldval == '' || subjectfldval == '' ){
			alert("Please Enter all the required fields");
			return false;
		}
		else {		
			document . forms [0]._action.value = 'create';document.forms[0].submit()
		}

		var saveButts = document.getElementsByName("save");
		for (var i =0; i < saveButts.length;i++ ) {
			saveButts[i].disabled=true;
		}
		
		return true;
	}
	function toggle(obj) {
		var currTabObj;

		var topWinHeight = 773;
		var topWinWidth = 900;

		for (var i = 0; i <= 6; i++)
		{
			currTabObj = document.getElementById('Tab_'+i);

			if (currTabObj) {
				if (currTabObj.id != obj.id) {
					currTabObj.className = "layoutTabOff";
				} else {
					currTabObj.className = "layoutTabOn";
				}
			}			
		}		
		
		relObj = document.getElementById('release');
		regObj = document.getElementById('regtab');
		caseObj = document.getElementById('casetab');		
		wattabObj=document.getElementById('wattab');

		brdrObj=document.getElementById('border');		
		if (obj.id == "Tab_0") {			
			DisplayObj(relObj);
			HideObj(caseObj);
			HideObj(regObj);
			HideObj(wattabObj);
			HideObj(brdrObj);

		} else if (obj.id == "Tab_1") {
			HideObj(caseObj);
			HideObj(relObj);
			HideObj(regObj);
			DisplayObj(wattabObj);
			HideObj(brdrObj);

		} else if(obj.id == "Tab_2"){
			
			HideObj(caseObj);
			HideObj(relObj);
			DisplayObj(regObj);
			HideObj(wattabObj);
			HideObj(brdrObj);
			topWinWidth = 920;

		} else if (obj.id == "Tab_3"){
			
			DisplayObj(caseObj);
			HideObj(relObj);
			HideObj(regObj);
			HideObj(wattabObj);
			HideObj(brdrObj);

		} else {	        
			DisplayObj(brdrObj);
			HideObj(regObj);
			HideObj(relObj);
			HideObj(caseObj);			
			HideObj(wattabObj);
		} 
		
		top.resizeTo(topWinWidth, topWinHeight);

		if (document.getElementById(obj.id+'_ifr_src'))	{
			selectedSrc = document.getElementById(obj.id+'_ifr_src').value;
			SetLowerFrame(selectedSrc);			
		}

		return true;
	}

	function DisplayObj(obj) {
		if (obj) {
			obj.style.visibility = 'visible';
			obj.style.display = 'block';
		}
		return true;
	}

	function HideObj(obj) {
		if (obj) {
			obj.style.visibility = 'hidden';
			obj.style.display = 'none';
		}		
		return true;
	}

	function SetLowerFrame(src) {
		newsrc = src;
		newsrcVals = newsrc.split('.ts=');
		if (newsrcVals.length > 1)
		{
			var mydate= new Date()
			newsrcVals[1] = mydate.getTime();
			newsrc = newsrcVals[0]+".ts="+newsrcVals[1];
		} 		
		currIfr = parent.document.getElementById('ssmain');
		currIfr.style.width = '100%';		
		currIfr.src = newsrc;
		return true;
	}

	function OnPageLoad() {	

		/*winOpener = parent.window.opener;
		hlpObj = winOpener.document.forms[0].elements['hlp'];
		
		if (hlpObj) {
			hlp_str = hlpObj.value;
		} else {		
			if (winOpener.top.iamain.document.frames[0] && winOpener.top.iamain.document.frames[0].document.forms[0]) {
				hlp_str = winOpener.top.iamain.document.frames[0].document.forms[0].elements['hlp'].value;
			} else {
				hlp_str = winOpener.top.iamain.document.forms[0].elements['hlp'].value;
			}
		}*/
		if (hlp_str) {
			SetLowerFrame(hlp_str);
			helpPageObj = document.getElementById('Tab_1')
			toggle(helpPageObj)
		}
		return true;
	}