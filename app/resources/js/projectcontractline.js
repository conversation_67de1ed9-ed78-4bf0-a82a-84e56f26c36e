var CONSTANTS = (function () {
    var arrConstants = {
        // Billing type enum in validvalues
        'BILLING_TYPE_PB_INDEX': 0,
        
        // Max billing enum in validvalues
        'MAX_BILLING_TP_INDEX' : 0,
        'MAX_BILLING_SA_INDEX' : 1,
    };

    return {
        get: function (name) { return arrConstants[name]; }
    };
})();

var projectcontractline_opid = 3846;

function onContractLineLoad()
{
    updateBillType();
    calcTotals(); // do this to update the header total fields on load
    var url = window.location.href;
    if (url.indexOf('tasktab=1') >= 0) {
        jq('a[href="#task_mapping_form"]').tab('show');
    }
    window.editor.dataChanged = false;

    projectContractLineSummaryBillingFormOnLoad();
}

/*
 * on form load, removes 'More actions' button from form, if launched from Summary billing details tab/grid
 * on OE invoice
 */
function projectContractLineSummaryBillingFormOnLoad()
{
    if (window['fromSummBillDetGrid']) {
        SummaryBilledDetailUtilFunctions.removeMoreActionsButton();
    }
}

var projectArr = [];
var taskArr = [];

/**
 * Bulk action to remove selected rows (Grids -> First grid -> Remove rows bulk actions)
 * @param gridId
 */
function removeSelectedRowsFromGrid(gridId) {
    var grid = window.editor.findComponents(gridId, 'Grid');
    if ( grid.length ) {
        var values = grid[0].value;
        for ( var index = values.length - 1; index > - 1; index -- ) {
            if ( values[index].SELECTED === 'true' ) {
                grid[0].deleteRow(index, true);
            }
        }
        grid[0].redraw();
    }
    calcTotals();
}

function ProjectContractLineEntryGrid()
{
}

ProjectContractLineEntryGrid.inheritsFrom( Grid );

/**
 * Implements custom sort for 'decimal' columns in ProjectContractLineEntryGrid
 */
ProjectContractLineEntryGrid.prototype.sortCompare = function(params) {
    switch (params.path) {
        case 'QTY':
        case 'UNITPRICE':
        case 'PRICE':
        case 'PRICEMARKUPPERCENT':
        case 'PRICEMARKUPAMOUNT':
        case 'LINEPRICE':
            params.left = parseFloat(params.left);
            params.right = parseFloat(params.right);
    }

    return Grid.prototype.sortCompare(params);
}

/*
 * Determines if a given row should be visible based on a flag
 */
ProjectContractLineEntryGrid.prototype.isRowVisible = function(rowIndex, isTotal) {
    var show = Grid.prototype.isRowVisible.call(rowIndex, isTotal);
    if ( show ) {
        show = EntrySortFilterGrid.isRowVisible(this, rowIndex);
    }
    return show;
}

/*
 * Overrides the default grid filter behavior
 */
ProjectContractLineEntryGrid.prototype.filterGrid = function() {
    // see gridFilterChanged() below
}

/*
 * Overrides the default grid sort behavior
 */
ProjectContractLineEntryGrid.prototype.sortGrid = function() {
    // see gridSortChanged() below
}

/*
 * function to manage onChange event of projectcontractlineentry.wftype
 * update price totals on PCL
 */
function pclentry_onChangeWFType(elem)
{
    calcTotals();
}

function pclentry_prefillPriceEffectiveDate(elem) {
    elem.meta.getGrid().gatherData();
    var rowNo = elem.meta.getLineNo();
    var rowValue = elem.meta.getParentValue();
    var effectiveDate = rowValue.PRICEEFFECTIVEDATE ? rowValue.PRICEEFFECTIVEDATE : '';
    var contractDate = window.editor.view.getField('CONTRACTLINEDATE').getValue();
    if (!effectiveDate) {
        if (!contractDate) {
            var today = getTodaysDate();
            rowValue.PRICEEFFECTIVEDATE = today;
        }
        else {
            rowValue.PRICEEFFECTIVEDATE = contractDate;
        }
        var grid = window.editor.view.findComponents('ITEMS', 'Grid');
        grid = grid[0];
        grid.refreshRow(rowNo);
    }
}

function getTodaysDate() {
    let today = new Date();
    let dd = String(today.getDate()).padStart(2, '0');
    let mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
    let yyyy = today.getFullYear();

    return mm + '/' + dd + '/' + yyyy;
}

/*
 * function to manage onChange event of projectcontractlineentry.projectid
 * clear task and cost type id's
 */
function pclentry_onChangeProject(elem)
{
    elem.meta.getGrid().gatherData();
    var rowNo = elem.meta.getLineNo();
    var rowValue = elem.meta.getParentValue();
    var projectid = ( rowValue.PROJECTID ? rowValue.PROJECTID : '' );
    if (projectid != projectArr[rowNo]) {
        rowValue.TASKID = '';
        rowValue.COSTTYPEID = '';
        var grid = window.editor.view.findComponents('ITEMS', 'Grid');
        grid = grid[0];

        // no prefilling Location and Customer

        grid.refreshRow(rowNo);
        projectArr[rowNo] = projectid;
    }
}

/*
 * Whenever an entry changes the value of the task, update the production unit description associated with it
 */
function pclentry_onChangeTask(elem) {
    elem.meta.getGrid().gatherData();
    var rowNo = elem.meta.getLineNo();
    var rowValue = elem.meta.getParentValue();
    var taskid = ( rowValue.TASKID ? rowValue.TASKID : '' );
    var taskSplit = taskid.split('--')[0];

    if (taskid != taskArr[rowNo]) {
        rowValue.COSTTYPEID = '';
        var grid = window.editor.view.findComponents('ITEMS', 'Grid');
        grid = grid[0];
        grid.refreshRow(rowNo);
        taskArr[rowNo] = taskid;
    }
}

function calcPrice(obj)
{
    obj.meta.getGrid().gatherData();

    var rowNo = obj.meta.getLineNo();
    var rowValue = obj.meta.getParentValue();
    var qty = ( rowValue.QTY ? rowValue.QTY : 0 );
    var unitprice = ( rowValue.UNITPRICE ? rowValue.UNITPRICE : 0 );

    rowValue['PRICE'] = RoundCurrency(qty * unitprice);

    var grid = window.editor.view.findComponents('ITEMS', 'Grid');
    grid = grid[0];
    grid.computeTotals();
    grid.refreshTotals();
    grid.refreshRow(rowNo);
    calcMarkup(obj);
}

function calcMarkup(obj)
{
    obj.meta.getGrid().gatherData();

    var rowNo = obj.meta.getLineNo();
    var rowValue = obj.meta.getParentValue();
    var price = ( rowValue.PRICE ? rowValue.PRICE : 0 );
    var markupPercent = (rowValue.PRICEMARKUPPERCENT ? rowValue.PRICEMARKUPPERCENT : 0 );

    rowValue.PRICEMARKUPAMOUNT = RoundCurrency(price * markupPercent / 100);

    var grid = window.editor.view.findComponents('ITEMS', 'Grid');
    grid = grid[0];
    grid.computeTotals();
    grid.refreshTotals();
    grid.refreshRow(rowNo);

    calcLinePrice(obj);
}

function calcLinePrice(obj)
{
    obj.meta.getGrid().gatherData();

    var rowNo = obj.meta.getLineNo();
    var rowValue = obj.meta.getParentValue();
    var price = ( rowValue.PRICE ? rowValue.PRICE : 0 );
    var markup = ( rowValue.PRICEMARKUPAMOUNT ? rowValue.PRICEMARKUPAMOUNT : 0 );

    rowValue.LINEPRICE = RoundCurrency(+price + +markup);

    var grid = window.editor.view.findComponents('ITEMS', 'Grid');
    grid = grid[0];
    grid.computeTotals();
    grid.refreshTotals();
    grid.refreshRow(rowNo);
    calcTotals();
}

// Calculate price totals on PCL based on PCL entries
function calcTotals()
{
    var totalRevisedPrice = 0;
    var originalPrice = 0;
    var revisionPrice = 0;
    var forecastPrice = 0;
    var approvedChangePrice = 0;
    var pendingChangePrice = 0;
    var otherPrice = 0;

    var data = window.editor.view.gatherData();

    var pclGrid = data.ITEMS;
    var crGrid = data.CRENTRIES;

    var wftype;
    var lineprice;

    for (var i = 0; i<pclGrid.length; i++) {
        wftype = pclGrid[i].WFTYPE;
        lineprice = pclGrid[i].LINEPRICE;
        if (wftype && lineprice) {
            switch (wftype) {
                case 'original':
                    totalRevisedPrice += parseFloat(lineprice);
                    originalPrice += parseFloat(lineprice);
                    break;
                case 'revision':
                    totalRevisedPrice += parseFloat(lineprice);
                    revisionPrice += parseFloat(lineprice);
                    break;
                case 'forecast':
                    forecastPrice += parseFloat(lineprice);
                    break;
                case 'approved change':
                    totalRevisedPrice += parseFloat(lineprice);
                    approvedChangePrice += parseFloat(lineprice);
                    break;
                case 'pending change':
                    pendingChangePrice += parseFloat(lineprice);
                    break;
                case 'other':
                    otherPrice += parseFloat(lineprice);
                    break;
            }
        }
    }

    if (crGrid) {
        for (var i = 0; i<crGrid.length; i++) {
            wftype = crGrid[i].WFTYPE_VALUE;
            lineprice = crGrid[i].LINEPRICE;
            if (wftype && lineprice) {
                switch (wftype) {
                    case 'original':
                        totalRevisedPrice += parseFloat(lineprice);
                        originalPrice += parseFloat(lineprice);
                        break;
                    case 'revision':
                        totalRevisedPrice += parseFloat(lineprice);
                        revisionPrice += parseFloat(lineprice);
                        break;
                    case 'forecast':
                        forecastPrice += parseFloat(lineprice);
                        break;
                    case 'approved change':
                        totalRevisedPrice += parseFloat(lineprice);
                        approvedChangePrice += parseFloat(lineprice);
                        break;
                    case 'pending change':
                        pendingChangePrice += parseFloat(lineprice);
                        break;
                    case 'other':
                        otherPrice += parseFloat(lineprice);
                        break;
                }
            }
        }
    }

    // if price value is 0, convert variable to string to prevent UI from having null returned
    if (totalRevisedPrice == 0) {
        totalRevisedPrice = "0";
    }
    if (originalPrice == 0) {
        originalPrice = "0";
    }
    if (revisionPrice == 0) {
        revisionPrice = "0";
    }
    if (forecastPrice == 0) {
        forecastPrice = "0";
    }
    if (approvedChangePrice == 0) {
        approvedChangePrice = "0";
    }
    if (pendingChangePrice == 0) {
        pendingChangePrice = "0";
    }
    if (otherPrice == 0) {
        otherPrice = "0";
    }

    var total = window.editor.view.getField('TOTALREVISEDPRICE');
    total.setValue(totalRevisedPrice);
    total = window.editor.view.getField('HDR_TOTALREVISEDPRICE');
    total.setValue(totalRevisedPrice);
    total = window.editor.view.getField('ORIGINALPRICE');
    total.setValue(originalPrice);
    total = window.editor.view.getField('HDR_ORIGINALPRICE');
    total.setValue(originalPrice);
    total = window.editor.view.getField('REVISIONPRICE');
    total.setValue(revisionPrice);
    total = window.editor.view.getField('FORECASTPRICE');
    total.setValue(forecastPrice);
    total = window.editor.view.getField('APPROVEDCHANGEPRICE');
    total.setValue(approvedChangePrice);
    total = window.editor.view.getField('HDR_APPROVEDCHANGEPRICE');
    total.setValue(approvedChangePrice);
    total = window.editor.view.getField('PENDINGCHANGEPRICE');
    total.setValue(pendingChangePrice);
    total = window.editor.view.getField('OTHERPRICE');
    total.setValue(otherPrice);
}

function onChangeBillType(obj)
{
    // Reset maximum billing amount to zero, only if the bill type changes
    var maximumBillingAmountObj = window.editor.view.getField('MAXIMUMBILLINGAMOUNT');
    if ( !maximumBillingAmountObj.getValue() ) {
        maximumBillingAmountObj.setValue(0);
    }

    updateBillType();
}

function onChangeMaxBilling(obj)
{
    updateBillType();
}

/**
 * Handle the hiding and showing based on the bill type
 */
function updateBillType()
{
    var billingTypeObj = window.editor.view.getField('BILLINGTYPE');
    var maximumBillingObj = window.editor.view.getField('MAXIMUMBILLING');
    var maximumBillingAmountObj = window.editor.view.getField('MAXIMUMBILLINGAMOUNT');
    var totalPriceObj = window.editor.view.getField('TOTALREVISEDPRICE');
    var summarizeObj = window.editor.view.getField('SUMMARIZEBILL');
    var rateTableSection = window.editor.findComponentsById('ratetable_section', 'Section')[0];

    var billingType = billingTypeObj.getValue();

    maximumBillingAmountObj.required = false;

    if ( billingType == billingTypeObj.validvalues[CONSTANTS.get('BILLING_TYPE_PB_INDEX')] ) {
        maximumBillingObj.setValue(maximumBillingObj.validvalues[CONSTANTS.get('MAX_BILLING_TP_INDEX')]);
        maximumBillingObj.readonly = true;
        maximumBillingAmountObj.showHide(false);
        summarizeObj.showHide(false);
        rateTableSection.updateProperty('hidden', true);
        rateTableSection.parentComponent.redraw();
    } else {
        if (window.editor.view.state != 'showview') {
            maximumBillingObj.readonly = false;
        }

        var maximumBilling = maximumBillingObj.getValue();

        if (maximumBilling == maximumBillingObj.validvalues[CONSTANTS.get('MAX_BILLING_SA_INDEX')]) {
            maximumBillingAmountObj.showHide(true);
            maximumBillingAmountObj.required = true;
        } else {
            maximumBillingAmountObj.showHide(false);
        }
        if (summarybillingfeatureenabled) { // only show if feature flag enabled
            summarizeObj.showHide(true);
        }

        rateTableSection.updateProperty('hidden', false);
        rateTableSection.parentComponent.redraw();
    }
}

function onChangeProject(obj) {
    var projectId = obj.getValue();

    // Retrieve the project location
    getObjectProperties(
        'project',
        projectId,
        ['LOCATIONID', 'LOCATIONNAME'],
        'PROJECTLOCATIONID'
    );
    // Retrieve the project department
    getObjectProperties(
        'project',
        projectId,
        ['DEPARTMENTID', 'DEPARTMENTNAME'],
        'DEPARTMENTID'
    );
    // Retrieve the project class
    getObjectProperties(
        'project',
        projectId,
        ['CLASSID', 'CLASSNAME'],
        'CLASSID'
    );

    var taskObj = window.editor.view.getField('TASKID');
    var costTypeObj = window.editor.view.getField('COSTTYPEID');
    taskObj.setValue('');
    costTypeObj.setValue('');

    onChangeDimensions();
}

function onChangeTask(obj) {
    var costTypeObj = window.editor.view.getField('COSTTYPEID');
    costTypeObj.setValue('');
    onChangeDimensions();
}

/*
 *  function to set dimension fields in PCLE to have same value as the dimension fields in PCL
 */
function onChangeDimensions() {
    var data = window.editor.view.gatherData();
    var pcleGrid = data.ITEMS;
    var val = '';

    // Get the grid
    var grid = window.editor.view.findComponents('ITEMS', 'Grid');
    grid = grid[0];
    var defaultLine = grid.getDefaultLine();

    var dimensionFields = ['LOCATIONID', 'DEPARTMENTID', 'PROJECTID', 'CUSTOMERID', 'VENDORID', 'EMPLOYEEID',
                                   'ITEMID', 'CLASSID', 'TASKID', 'COSTTYPEID'];

    // Set PCLE field values and default values from the PCL
    for (var i=0; i<pcleGrid.length; i++) {
        for (var x=0; x<dimensionFields.length; x++) {
            // LOCATIONID value in PCLE is derived from the PROJECTLOCATIONID value in PCL
            if (dimensionFields[x] == 'LOCATIONID') {
                val = data['PROJECTLOCATIONID'];
            } else {
                val = data[dimensionFields[x]];
            }
            pcleGrid[i][dimensionFields[x]] = val;
            defaultLine[dimensionFields[x]] = val;
        }
    }
}

function onChangeProjectContract(obj) {
    var projectContractId = obj.getValue();

    // Retrieve the customer
    getObjectProperties(
        'projectcontract',
        projectContractId,
        ['CUSTOMERID', 'CUSTOMERNAME'],
        'CUSTOMERID'
    );

    // Retrieve list of valid projects for project picker
    getObjectProperties(
        'projectcontractproject',
        projectContractId,
        ['VALIDPROJECTKEYS'],
        'VALIDPROJECTKEYS',
        function(fieldObj, data) {
            var projectObj = window.editor.view.getField('PROJECTID');
            projectObj.reset();
            projectObj.retrieveValues();
        }
    );
}

/**
 * Method to retrieve property value(s) from the server using Ajax
 */
function getObjectProperties(objectType, objectId, objectProperties, fieldPath, callback) {
    var fieldObj = window.editor.view.getField(fieldPath);
    fieldObj.setValue(null);
    if ( objectId ) {
        params = {
            '_objectType': objectType,
            '_objectId': objectId,
            '_objectProperties': objectProperties
        };
        // Find and set field values
        window.editor.ajax(false, 'getObjectProperties', params, function(data) {
            if ( data._errors != undefined ) {
                alert(data._errors);
            } else if ( objectProperties.length == 2 ) {
                // If field values are null, set final value as blank
                var value = null;
                if ( data[objectProperties[0]] !== null || data[objectProperties[1] !== null]) {
                    value = data[objectProperties[0]] + '--' + data[objectProperties[1]];
                }
                fieldObj.setValue(value);
            } else {
                fieldObj.setValue(data[objectProperties[0]]);
            }
            // Set project derived dimension fields in PCLE here to avoid async data issues
            if (objectType == 'project') {
                onChangeDimensions();
            }
            if ( callback !== undefined ) {
                callback(fieldObj, data);
            }
        });
    }
}

var taskSelectorHandler = {
    "messageIdCache": [],
    "showSelectTasksPage": function(opId) {
        var $editor = this.getEditor();
        var $url = "lister.phtml?.op="+opId+"&_selector=T&_pclId="+$editor['data']['view']['PROJECTCONTRACTLINEID']+"&_pjcnId="+ encodeURIComponent($editor['data']['view']['PROJECTCONTRACTID']) +"&refreshTarget="+window.name;
        return Launch($url);
    },

    "closeSelectTasksPage": function() {
        // Called when the 'Done' button is clicked
        var $this = this;

        // Show the spinning bar to 'lock' the screen
        $this.showLoadingBar();

        // Get the info from the URL of the current frame
        const urlSearchParams = new URLSearchParams(window.location.search);
        const params = Object.fromEntries(urlSearchParams.entries());

        // If the target is iamain, we're on the PCL workflow and need to refresh the main page
        if (params.refreshTarget == "iamain") {
            window.parent.location.replace(window.parent.location.href + "&tasktab=1");

        // If the target is anything else, then we're on the PCN workflow and need to refresh a specific popup window
        } else {
            window.parent.frames[params.refreshTarget].location.replace(window.parent.frames[params.refreshTarget].location.href + "&tasktab=1")
        }

        return window.parent.closeQxDialog(window.frameElement);
    },

    "showLoadingBar": function() {
        var $editor = this.getEditor();
        $editor.showLoadingBar();
    },

    "hideLoadingBar": function() {
        var $editor = this.getEditor();
        $editor.hideLoadingBar();
    },

    "getEditor": function() {
        var $editor;
        if(typeof window.editor === 'undefined' || window.editor === null) {
            $editor = window.parent.editor;
        } else {
            $editor = window.editor;
        }
        return $editor;
    },

    "gatherData": function() {
        var $editor = this.getEditor();
        $editor.gatherData();
    },

    "getGridObj": function(gridName) {
        var $editor = this.getEditor();
        var $grid = $editor.findComponents(gridName);
        $grid = $grid[0];
        return $grid;
    },

    "removeSelectedRowsFromGrid": function(grid) {
        var $rowCount = grid.value.length;
        for(var i=$rowCount-1; i>=0; i--) {
            if(this.isSelectedRow(grid, i)) {
                grid.deleteRowData(i);
            }
        }
        grid.redraw();
    },

    "getSelectedInfo": function(grid) {
        var $uniquePropertyName = grid._getUniquePropOnRow();
        var $rowCount = grid.value.length;

        var $selectedInfo = {};
        for(var i=0, j=0; i<$rowCount; i++) {
            if(this.isSelectedRow(grid, i)) {
                $selectedInfo[j++] = {
                    "taskid": grid.value[i]['TASKID'],
                    "projectid": grid.value[i]['PROJECTID']
                };
            }
        }
        return $selectedInfo;
    },

    "showErrorDiv": function(id, message, errorType) {
        var $this = this;
        if(id === null || typeof id === "undefined" || message === null || typeof message === "undefined") {
            return false;
        }
        if(typeof $this.messageIdCache[id] === "undefined" || $this.messageIdCache[id] < 0) {
            $this.messageIdCache[id] = window.view.addMessage(errorType, message);
        }
        return true;
    },

    "hideErrorDiv": function(id, errorType) {
        var $this = this;
        if(id === null || typeof id === "undefined") {
            return false;
        }

        //hide the warning message
        if(typeof $this.messageIdCache[id] !== "undefined" && $this.messageIdCache[id] > 0) {
            window.view.removeMessage(errorType, $this.messageIdCache[id]);
            $this.messageIdCache[id] = -1;
        }
        return true;
    },

    "deleteSelectedEntries": function() {
        var $this=this;
        var $editor = this.getEditor();

        var entryGrid = $this.getGridObj("PCLTASKS");
        var $selectedCount = entryGrid.selectColumnProps.numSelectedItems;

        if($selectedCount === 0) {
            $this.showErrorDiv("noItemSelected", GT('IA.SELECT_AT_LEAST_ONE_RECORD'), MESSAGE_ERROR);
            return false;
        } else {
            $this.hideErrorDiv("noItemSelected", MESSAGE_ERROR);
        }

        if(!confirm(GT({"id": "IA.YOU_ARE_ABOUT_TO_DELETE_ITEMS", "placeHolders": [{"name": "SELECTED_COUNT", "value": $selectedCount}]}))) {
            return false;
        }

        $this.showLoadingBar();
        var $selectedInfo = $this.getSelectedInfo(entryGrid);

        var pclId = $editor['data']['view']['PROJECTCONTRACTLINEID'];
        var pcnId = $editor['data']['view']['PROJECTCONTRACTID'];

        $editor.ajax(false, "removeMappedTasksPJContract", {"_data": $selectedInfo, "_pclId": pclId, "_pcnId": pcnId}, function(data) {
            if(data === true) {
                $this.removeSelectedRowsFromGrid(entryGrid);
                entryGrid.unselectAllLines();
                $this.hideErrorDiv("errorInRemovingEntry", MESSAGE_ERROR);
            } else if(typeof data.message != 'undefined' && data.message !== '') {
                $this.showErrorDiv("errorInRemovingEntry", data.message, MESSAGE_ERROR);
            } else {
                $this.showErrorDiv("errorInRemovingEntry", GT('IA.SOMETHING_WENT_WRONG_PLEASE_TRY_AGAIN_IN_SOME_TIME'), MESSAGE_ERROR);
            }

            $this.hideLoadingBar();
        }, function(error) {
            $this.hideLoadingBar();
        });
    },

    "isSelectedRow": function(grid, rowNumber) {
        // if select all is checked, we return true
        if(this.allRowsSelected(grid)) {
            return true;
        }

        if(this.someRowsSelected(grid) && !grid.selectColumnProps.isRowAnException(grid.getValue()[rowNumber], rowNumber)) {
            return true;
        }

        // check if the row exists in the dom
        // user could have navigated to a currently non active page and made a selection
        var selectedRow = grid.findLineComponent("SELECTED", rowNumber, "Field");
        if(typeof selectedRow === "undefined" || selectedRow === null) {
            return false;
        }
        return (selectedRow.getValue() === "true");
    },

    "allRowsSelected": function(grid) {
        return (grid.selectColumnProps.selectionState === Grid.SelectionsState.ALL);
    },

    "someRowsSelected": function(grid) {
        return (grid.selectColumnProps.selectionState === Grid.SelectionsState.ALL_EXCEPT_SOME);
    },

    "addPCLTasks": function() {
        var $this = this;
        var $selectedInfo = $this.getSelectedItems();
        if ($selectedInfo.length === 0) {
            alert(GT('IA.SELECT_AT_LEAST_ONE_RECORD'));
            return false;
        }
        $this.showLoadingBar();

        var $editor = $this.getEditor();

        const urlSearchParams = new URLSearchParams(window.location.search);
        const params = Object.fromEntries(urlSearchParams.entries());

        var pclId = params._pclId;
        var pcnId = params._pjcnId;

        $editor.ajax(false, "addMappedTasksPJContract", { "_data": $selectedInfo, "_pclId": pclId, "_pcnId": pcnId}, function(data) {

            if (data.hasOwnProperty('_errors')) {
                //close the loading bar but keep the floating div open until the user says Done
                $this.hideLoadingBar();
                alert(data._errors[0]);
            } else {
                window.location.reload()
            }
        }, function(error) {
            alert(GT('IA.SOMETHING_WENT_WRONG_PLEASE_TRY_AGAIN_IN_SOME_TIME'));
            $this.hideLoadingBar();
        });
    },

    "openViewModal": function(values) {
        var errorMsg = values.ERROR;
        if (errorMsg != undefined) {
            alert(errorMsg);
            return;
        }
        Launch(values.URL, GT('IA.VIEW_DETAILS'), 1200, 800);
    },

    "openTaskView": function(row) {
        if (row != null) {
            var grid = window.editor.view.findComponents('PCLTASKS', 'Grid');
            var $this = this;
            grid = grid[0];
            var gridData = grid.gatherData();
            var args = {
                '_taskId' : gridData.PCLTASKS[row].PROJECTID +"--"+ gridData.PCLTASKS[row].TASKID
            };
            // let ajax do its thing
            window.editor.ajax(false, 'viewTaskUrl', args, $this.openViewModal);
        }
    },

    "openProjectView": function(row) {
        if (row != null) {
            var grid = window.editor.view.findComponents('PCLTASKS', 'Grid');
            var $this = this;
            grid = grid[0];
            var gridData = grid.gatherData();
            var args = {
                '_projectId' : gridData.PCLTASKS[row].PROJECTID
            };
            // let ajax do its thing
            window.editor.ajax(false, 'viewProjectUrl', args, $this.openViewModal);
        }
    },

    "getSelectedItems": function() {
        var selected = [];
        var checkboxes = jq('[name=".checks[]"]');
        jq(checkboxes).each(function(i, checkbox) {
            if(jq(checkbox).is(":checked")) {
                selected.push(jq(checkbox).val());
            }
        });
        return selected;
    }
};

function priceEffectiveDateForm(meta) {
    if (meta) {
        this.meta = meta;
    }
}

priceEffectiveDateForm.inheritsFrom(Field);

priceEffectiveDateForm.prototype.validateData = function()
{
    // skip validation if value is null
    if (!this.value) {
        return true;
    }

    return Field.prototype.validateData.call(this);
}


function taskFloatingLink(meta) {

    if (meta) {
        this.meta = meta;
    }
}

taskFloatingLink.inheritsFrom(Field);

taskFloatingLink.prototype.draw = function() {

    if ((this.value && this.value != '')) {
        this.htmlPrefix = '<a href=#skip onclick=javascript:viewTask('+this.context[2]+')>';
        this.htmlSuffix = '</a>';
        this.isHTML = true;
    } else {
        this.htmlSuffix = null;
        this.htmlPrefix = null;
        this.isHTML = false;
        this.value = '';
    }

    return Field.prototype.draw.call(this);
};

function projectFloatingLink(meta) {

    if (meta) {
        this.meta = meta;
    }
}

projectFloatingLink.inheritsFrom(Field);

projectFloatingLink.prototype.draw = function() {

    if ((this.value && this.value != '')) {
        this.htmlPrefix = '<a href=#skip onclick=javascript:viewProject('+this.context[2]+')>';
        this.htmlSuffix = '</a>';
        this.isHTML = true;
    } else {
        this.htmlSuffix = null;
        this.htmlPrefix = null;
        this.isHTML = false;
        this.value = '';
    }

    return Field.prototype.draw.call(this);
};

function viewTask(row) {
    if (row != null) {
        var grid = window.editor.view.findComponents('PCLTASKS', 'Grid');
        grid = grid[0];
        var gridData = grid.gatherData();
        var args = {
            '_taskId' : gridData.PCLTASKS[row].TASKID,
            '_projectId': gridData.PCLTASKS[row].PROJECTID
        };
        // let ajax do its thing
        window.editor.ajax(false, 'viewTaskUrl', args, c_finishTask);
    }
}

function viewProject(row) {
    if (row != null) {
        var grid = window.editor.view.findComponents('PCLTASKS', 'Grid');
        grid = grid[0];
        var gridData = grid.gatherData();
        var args = {
            '_taskId' : gridData.PCLTASKS[row].TASKID,
            '_projectId': gridData.PCLTASKS[row].PROJECTID
        };
        // let ajax do its thing
        window.editor.ajax(false, 'viewProjectUrl', args, c_finishProject);
    }
}
function c_finishTask(values) {

    var errorMsg = values.ERROR;
    if (errorMsg != undefined) {
        alert(errorMsg);
        return;
    }
    Launch(values.URL, GT('IA.VIEW_TASK_DETAILS'), 1200, 800);
}
function c_finishProject(values) {

    var errorMsg = values.ERROR;
    if (errorMsg != undefined) {
        alert(errorMsg);
        return;
    }
    Launch(values.URL, GT('IA.VIEW_PROJECT_DETAILS'), 1200, 800);
}

//on quixote we have bootstrap 'shown.bs.tab' event fired when tab is active
//need to be wrapped on a dom ready function
jq(function() {
    jq('.qx-nav-tabs').on('shown.bs.tab', function (e) {
        var activeTab = jq(e.target).attr('href');
        if (activeTab == '#pcl_billing_form') {
            loadProjectContractLineBilling360ViewSections(null);
        }
    })
});

/*
 * Load project contract billing and invoices data
 */
function loadProjectContractLineBilling360ViewSections(event) {
    // Get the PC and PCL key
    var projectContractLineKey = window.editor.view.value.RECORDNO;
    var projectContractKey = window.editor.view.value.PROJECTCONTRACTKEY;

    if (!projectContractKey || !projectContractLineKey)  {
        return;
    }

    QXUtil.showLoading(1, GT('IA.LOADING_PROJECT_CONTRACT_LINE_BILLING_INFORMATION'));

    // Make the ajax call
    var args = {
        projectContractKey : projectContractKey,
        projectContractLineKey : projectContractLineKey
    };
    window.editor.ajax(false, 'getPCLBilling360View', args, CRE360View.c_getBilling360View);
}

jq(document).ready(function() {
    // Check

    if(typeof jq.Topic !== 'undefined') {
        jq.Topic('gridMultipleRowSelectionChanged').subscribe(function(context, data) {
            taskSelectorHandler.getGridObj("PCLTASKS").redraw();
        });
    }

    /*
     * Subscribe to the gridSortChanged and gridFilterChanged topics
     */
    allowCancelWithChanges = false;

    jq.Topic('gridSortChanged').subscribe(function(context, path, direction) {
        EntrySortFilterGrid.gridSortChanged(context, path, direction);
    });
    jq.Topic('gridFilterChanged').subscribe(function(context, path, oldValue, newValue) {
        EntrySortFilterGrid.gridFilterChanged(context, path, oldValue, newValue);
    });
});
