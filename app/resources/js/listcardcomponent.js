function ChangeKpiGroup(meta) {
    var groupName = window.editor.view.getField('DASHBOARDGROUPNAME');
    if (meta.value == '-- Use a new group --') {
        groupName.updateProperty('hidden', false);

    } else {
        groupName.updateProperty('hidden', true);
        groupName.setValue(meta.getValue())
    }
    groupName.redraw();

}

function showHidePickerFromForm(filterFor, enableptr, enableptr2) {
    if (enableptr2 == undefined) {
        showHidePicker(filterFor, enableptr, 'specific');
    } else {
        showHidePicker(filterFor, enableptr2, 'group');
        showHidePicker(filterFor, enableptr, 'specific');
    }
}

function showHidePicker(filterFor, enableptr, filterType){
    var ptr = window.view.findComponents(enableptr);
    if ( !ptr || ptr.length == 0 ) {
        return false;
    }
    ptr = ptr[0];
    if (this.view.readonly) {
        if(filterFor.value && filterFor.value.search('nofilter') < 0) {
            filterFor.showHide(true);
            if (filterFor.value.search('nullvalue') == 0) {
                ptr.showHide(false);
            } else {
                ptr.showHide(true);
            }
            ptr.parentComponent.redraw();
            return true;
        } else {
            ptr.showHide(false);
            filterFor.showHide(false);
            ptr.parentComponent.redraw();
            return false;
        }
    }

    if(filterFor.value && (filterFor.value.search(filterType) == 0)) {
        ptr.label = '';
        ptr.showHide(true);
        ptr.setFocus(); // so tabbing works after making a selection.
        ptr.parentComponent.redraw();
        return true;
    } else {
        ptr.showHide(false);
        ptr.parentComponent.redraw();
        filterFor.focus();
        return false;
    }
}

function setShowAsDependencies(redraw) {
    var showAsValue = window.editor.view.getField('SHOW_AS_VALUE').getValue();
    var roundingField = window.editor.view.getField('ROUNDING');

    var validlabels = ['No rounding', 'Whole number', 'Thousands', 'Millions']; // use to say whole dollar/amount....
    var validvalues = ['none', 'whole', 'thousands', 'millions'];

    if (showAsValue == 'number') {
        var validlabels = ['No rounding', 'Whole number', 'Thousands', 'Millions'];
    } else if (showAsValue == 'percent') {
        validlabels = ['No rounding', 'Whole number'];
        validvalues = ['none', 'whole'];
    }

    var curRounding = roundingField.getValue();
    if (jq.inArray(curRounding, validvalues) < 0) {
        if (curRounding == 'dollars') {
            roundingField.setValue('whole');
        } else if (curRounding == 'whole') {
            roundingField.setValue('dollars');
        } else {
            roundingField.setValue('none');
        }

    }

    roundingField.updateProperty(['type', 'validlabels'], validlabels);
    roundingField.updateProperty(['type', 'validvalues'], validvalues);

    if (redraw !== false) {
        roundingField.parentComponent.parentComponent.redraw();
    }

}


var KPI_INITIALIZING = true;
function setFormInitialState() {
    if (window.editor.view.state == 'shownew') {
        window.editor.setDoNotShowLoadingBar(true);
    }

    initComponents();
    beanIDChange2();
    calctypeChanged();
    setShowAsDependencies(true);

    //window.editor.view.getField('TITLE').setFocus();
    KPI_INITIALIZING = false;
}

function kpiSaveAction() {

    window.parent.editor.showLoadingBar();

    var valid = window.editor.validateData(true);
    if (!valid) {
        window.parent.editor.hideLoadingBar();
        return false;
    }


    return true;
}


function kpiSaveAction2() {

    window.editor.showLoadingBar();

    var valid = window.editor.validateData(true);
    if (!valid) {
        window.editor.hideLoadingBar();
        return false;
    }


    return true;
}

/**
 * Function called to set the focus on a given field. Used after redrawing components.
 */
function setFocusOnField(fieldname)
{
    window.editor.view.getField(fieldname).setFocus();
}

/**
 * Initialize the Componenent selection drop down
 */
function initComponents() {
    var beanIDField = window.editor.view.getField('BEANID');

    // We need to have BEANID as a text control in order to get the value but we must display it as an enum control
    beanIDField.uiControl = 'ControlEnum';
    beanIDField.renderer = null;
    beanIDField.updateProperty(['type', 'validlabels'], COMPONENTINFO['I']['LISTAS']);
    beanIDField.updateProperty(['type', 'validvalues'], COMPONENTINFO['I']['ENTITIES']);

    beanIDField.updateProperty('hidden', false);
    beanIDField.updateProperty('required', true);
    beanIDField.parentComponent.redraw();
    if (beanIDField.type['validvalues'].indexOf(beanIDField.getValue()) == -1) {
        beanIDField.setValue(COMPONENTINFO['I']['ENTITIES'][0]);
    }
}

/**
 * Populate the RECORDUSERVIEW field
 * @param beanIdChanged
 */
function beanIDChange2() {
    var beanIDField = window.editor.view.getField('BEANID');

    var recordUserViewField = window.editor.view.getField('RECORDUSERVIEW');

    if (recordUserViewField) {
        recordUserViewField.updateProperty('hidden', false);
        recordUserViewField.updateProperty(['type', 'validlabels'], UVARR[beanIDField.getValue()]['LISTAS']);
        recordUserViewField.updateProperty(['type', 'validvalues'], UVARR[beanIDField.getValue()]['ENTITIES']);

        recordUserViewField.redraw();
        if (recordUserViewField.type['validvalues'].indexOf(recordUserViewField.getValue()) == -1) {
            recordUserViewField.setValue(recordUserViewField.type['validvalues'][0]);
            if (recordUserViewField.getValue == 'NULL') {
                recordUserViewField.setValue('');
            }
        }
    }

    var sumcolumnField = window.editor.view.getField('SUMCOLUMN');
    if (sumcolumnField) {
        sumcolumnField.updateProperty(['type', 'validlabels'], FIELDINFO[beanIDField.getValue()]['LISTAS']);
        sumcolumnField.updateProperty(['type', 'validvalues'], FIELDINFO[beanIDField.getValue()]['ENTITIES']);

        if (sumcolumnField.type['validvalues'].indexOf(sumcolumnField.getValue()) == -1) {
            sumcolumnField.setValue(sumcolumnField.type['validvalues'][0]);
            if (sumcolumnField.getValue == 'NULL') {
                sumcolumnField.setValue('');
            }
        }
    }

    return true;
}

function calctypeChanged() {
    var calcTypeField = window.editor.view.getField('CALCTYPE');
    var sumcolumnField = window.editor.view.getField('SUMCOLUMN');

    if(calcTypeField.getValue() === 'S')  {
        sumcolumnField.updateProperty('hidden', false);
    }
    else {
        sumcolumnField.updateProperty('hidden', true);
        //window.editor.view.getField('SHOW_AS_VALUE').setValue('number'); // trying to be helpful
    }
    sumcolumnField.redraw();

}
/**
 * On the component selection, automatically populate the title field to default value.
 */
function setTitle2() {
    var beanIDField = window.editor.view.getField('BEANID');
    var index = beanIDField.type['validvalues'].indexOf(beanIDField.getValue());
    var beanIDLabel = beanIDField.type['validlabels'][index];

    if (beanIDLabel) {
        var titleField = window.editor.view.getField('TITLE');
        if (beanIDLabel.indexOf(' -- ') >= 0) {
            beanIDLabel = beanIDLabel.substring(beanIDLabel.indexOf(' -- ') + 4);
        }
        titleField.setValue(beanIDLabel);
        // toggleShowCollapsed();
    }
}