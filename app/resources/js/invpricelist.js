/* 
* To change this template, choose Tools | Templates
* and open the template in the editor.
*/


function RoundedValue(valobj){
        val = valobj.value;
        if (val == null || val.toString() == '' || isNaN(val)) {
                valobj.value ='';
        }
        else{
        decplace = val.indexOf(".");
        decvalue = val.substr(decplace);
                if (decvalue.length >= 11 )
                {
                        valobj.value = RoundCurrency(val,10);
                }
        }
}

//Function RoundCurrency
function RoundCurrency(val,degree) {
        if (val == null || val == '') return '';
        if(arguments.length > 1 ) {
                var n = Math.pow(10,degree);
                if (val != '') {
                        val = Math.round(val * n)/n;
                        var suffixStr = (val.toString().indexOf('.') == -1) ? '.' : '';
                        suffixLen = degree - ((val.toString().indexOf('.') > 0) ? (val.toString().length - val.toString().indexOf('.')-1) : 0 );

                        for(i=0; i < suffixLen; i++) {
                                suffixStr +='0';
                        }
                        if (suffixLen > 0 || (val.toString().indexOf('.') == -1 && degree > 0)) {
                                val = val + suffixStr;
                        }
                        if (val.toString().charAt(val.toString().length-degree) == '.') {
                                val = val + '0';
                        }
                }
        }else
        {
                //	Default Round Currency Method
                if (val != '') {
                        val = Math.round(val * 100)/100;
                        if (val.toString().indexOf('.') == -1) {
                                val = val + '.00';
                        }
                        if (val.toString().charAt(val.toString().length-2) == '.') {
                                val = val + '0';
                        }
                }
        }
        return val.toString();
}