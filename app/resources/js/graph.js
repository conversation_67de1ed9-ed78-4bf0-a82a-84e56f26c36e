var allowedPeriodComparisions = [ "IA.CURRENT_MONTH", "IA.CURRENT_MONTH_TO_DATE", "IA.CURRENT_QUARTER", "IA.CURRENT_QUARTER_TO_DATE", "IA.CURRENT_YEAR", "IA.CURRENT_YEAR_TO_DATE", "IA.THIS_WEEK", "IA.TODAY", "IA.FISCAL_CURRENT_MONTH", "IA.FISCAL_CURRENT_QUARTER", "IA.FISCAL_CURRENT_QUARTER_TO_DATE", "IA.FISCAL_CURRENT_YEAR", "IA.FISCAL_CURRENT_YEAR_TO_DATE", "IA.TWELVE_MONTHS_TO_CURRENT_MONTH", "IA.TWELVE_MONTHS_TO_CURRENT_DATE"];
allowedPeriodComparisions = allowedPeriodComparisions.map(GT);
var allowedWFCPeriodComparisions = [ "IA.CURRENT_MONTH", "IA.CURRENT_QUARTER", "IA.CURRENT_YEAR", "IA.THIS_WEEK", "IA.FISCAL_CURRENT_MONTH", "IA.FISCAL_CURRENT_QUARTER", "IA.FISCAL_CURRENT_YEAR", "IA.TWELVE_MONTHS_TO_CURRENT_MONTH"];
allowedWFCPeriodComparisions = allowedWFCPeriodComparisions.map(GT);
var periodExpansionFields = ['EXPANDBY', 'TRENDING', 'SIMILARPRDCMP', 'PERIODOFFSETBY'];
var wfExpansionFields = ['WF_SECONSERIES', 'WF_DIMENSION', 'WF_DIMSTRUC', 'WF_EXPANDBY'];

var FIXED_SECTION_DEFAULT_TOP = 60;

function setFormInitialState() {
    var graphContainerSpan = document.getElementById('graphContainerSpan');
    if(graphContainerSpan){
        graphContainerSpan.innerText = GT('IA.THE_GRAPH_PREVIEW_WILL_APPEAR_HERE');
    }
    if (window.editor.view.state == 'shownew') {
//        window.editor.setDoNotShowLoadingBar(true);
        data = view.gatherData();
        periods = window.editor.view.getField('PERIODS');
        setFldDefaultVal(periods, "2");
        budgets = window.editor.view.getField('BUDGETS');
        setFldDefaultVal(budgets, "a");

        window.editor.findComponentsById('secondaryDS')[0].updateProperty('hidden', false);


    }
    if (globTable === 'T') {
        fetchIAAcctGrpOptions();
    }
    if (globTable !== 'T') {
        setTrendingDD();
        initMultipleBooks();
        setReportingBookType(true);
    }
    setSecondSeries();
    setGraphTypeDependencies();
    setPeriodDependencies();
    drawGraphTypeImages();

    if (globTable !== 'T') {
        if (window.editor.view.state == 'showedit' ||
            window.editor.view.state == 'showview') {
            saveAndRefreshGraph();
        }

        if (window.editor.view.state !== 'showview') {
            jq('.dimFilters').change();
        }
    }
}

//checking other default books
function getOtherDefaultBooks(){
    return window.editor.view.getField('DEFAULTOTHERBOOKS').type.validvalues;
}

/**
 * When budget comparison is used, then hide zero bal values by default
 */
function enableDisableHideZeroCheckbox() {
    data = view.gatherData();

    var fldObj = window.editor.findComponents('HIDEZEROBALCOL', 'Field');
    if (fldObj) {
        fldObj = fldObj[0];
        if (data['PRIMARYSERIES'] === 'B' || data['SECONDARYSERIES'] === 'B') {
            fldObj.updateProperty('disabled', true);
            fldObj.setValue('true');
        } else {
            fldObj.updateProperty('disabled', false);
        }

        fldObj.parentComponent.redraw();
    }
}

/**
 * Move filed from one section to other with position
 *
 * @param fieldPath
 * @param section
 * @param position
 */
function moveFieldToDifSection(fieldPath, section, position) {
    fld = window.editor.view.getField(fieldPath);
    fromSection = fld.parentComponent;
    if (fromSection != null) {
        toSection = window.editor.findComponentsById(section, 'Section')[0];
        //toSection.addChildAt(fld, position); // add to new section
        toSection.children.splice(position, 0, fld);
        //toSection.fitChildElement(fld, this.children[ix], this.value, index, elements);
        fromSection.removeChild(fld); // remove from old section
        fld.parentComponent = toSection;
        toSection.redraw();
        fromSection.redraw();
    }
}

/**
 * Move fileds to the end of diff section
 *
 * @param fieldPath
 * @param section
 */
function pushFieldToDifSection(fieldPath, section) {
    fld = window.editor.view.getField(fieldPath);
    fromSection = fld.parentComponent;
    if (fromSection != null) {
        toSection = window.editor.findComponentsById(section, 'Section')[0];
        toSection.addChild(fld); // add to new section
        //toSection.fitChildElement(fld, this.children[ix], this.value, index, elements);
        fromSection.removeChild(fld); // remove from old section
        fld.parentComponent = toSection;
        toSection.redraw();
        fromSection.redraw();
    }
}


/**
 * Hide or unhide list of fields
 *
 * @param filedList  List of fileds to be hidden or shown
 * @param hideUnhide Flag to hide or show
 */
function hideUnhideFields(filedList, hideUnhide) {
    for (var i = 0; i < filedList.length; i++) {
        window.editor.view.getField(filedList[i]).updateProperty('hidden', hideUnhide);

    }
    window.editor.view.getField(filedList[0]).parentComponent.redraw();
}


/**
 * Auto-populate title value
 */
function setTitle() {
    data = view.gatherData();
    if (jq.trim(data['TITLE']) === '') {
        colTrendingObj = window.editor.view.getField('TITLE');
        colTrendingObj.setValue(window.data['NAME']);
    }
}

function reloadIAAcctGrpOptions() {
    fetchIAAcctGrpOptions();
    if (GroupCtrl){
        GroupCtrl.getObject().setValue('');
    }

}

function fetchIAAcctGrpOptions() {
    data = view.gatherData();
    if ( data['INDUSTRY'] === '' ) {
        return false;
    }

    var callParams = {
        'function': 'getIAAcctGrpsByIndustry',
        'entity': 'iaacctgrppick',
        'industrycode': data['INDUSTRY']
    };
    var updateArgs = {};
    var qrequestBatch = window.view.getQRequestBatch();
    qrequestBatch.addRequest('QRequest', callParams, RespProcesser_populateAcctGrpOptions, updateArgs, false);
    qrequestBatch = null;
}

function RespProcesser_populateAcctGrpOptions(params, m_response) {
    var data = JSON.parse(m_response);

    GroupCtrl.getObject().updateProperty(['type', 'validvalues'], data.validvalues);
    GroupCtrl.getObject().updateProperty(['type', 'validlabels'], data.validlabels);

    GroupCtrl.setPickerValuesFromMultiSelectValues(data.validlabels,data.validvalues); // updating picker values

}

/**
 * This is a controller which contains the logic for converting the Picker to multiselect and vise-versa.
 * It will only convert multiselect to Picker if that is originally defined as Picker.
 * Also we need to extends InputControlPtr to perform this operation which is performend in init().
 *
 * @param params
 * @constructor
 */
Object.defineProperty(this, 'PickerMultiSelectCtrl', {
    value: function (params) {
        /**
         * Behaves as constant.
         *
         * @returns {{FIELD: *|string, PICKER_VAL_FLD: *|string, USER_UI_CONTROL: *|string}}
         * @constructor
         */
        function CONST() {
            return {
                FIELD: params.FIELD, /* Componenet name as mentioned in field-info */
                PICKER_VAL_FLD: params.PICKER_VAL_FLD, /* Additional params passed from the backend to support multiselect */
                USER_UI_CONTROL: params.USER_UI_CONTROL, /* Function name which is extending InputControlPtr
                                                  and implements updateValues */
                UPDATE_SERVER_DATA_RECEIVED_FLG: params.UPDATE_SERVER_DATA_RECEIVED_FLG /* true|false*/
            };
        }

        /*creating single UI component Object*/
        var uiFieldComponent = null;
        var serverDataReceivedFlg = false;

        /**
         * It simply set top level object attribute to specified value.
         * If objKey is null or undefined then obj should be of @type - <InputControl> and obj will call
         * setValue function to set it objKey.
         *
         * @param obj
         * @param value
         * @param objKey
         */
        var setObject = function (obj, value, objKey) {
            if (objKey === undefined || objKey == null) {
                obj.setValue(value);
            }
            else {
                obj[objKey] = value;
            }
        };
        /**
         * This will take <refVal> from <refObj> using <refKey> and set <refVal> to <obj> for <objKey> attribute.
         * If <objKey> is null see setObject() - doc
         *
         * @param refObj
         * @param refKey
         * @param refVal
         * @param obj
         * @param objKey | optional default is "value"
         */
        var setObjByRefObjMapForValue = function (refObj, refKey, refVal, curVal, obj, objKey) {
            var xArr = refObj.type[refKey];
            var index = -1;
            /* Don't use findindex() as its not supported in IE-10 */
            for (var i = 0; i < xArr.length; i++) {
                if (xArr[i] == curVal) {
                    index = i;
                    break;
                }
            }
            if (index != -1) {
                setObject(obj, (refObj.type[refVal][index]).toString(), objKey);
            }
        };

        /**
         *
         * If objKey is null see setObject() - doc
         *
         * @param pickerObj
         * @param pickObjKey
         * @param pickObjValue
         * @param curVal
         * @param obj
         * @param objKey
         */
        var setObjByPickerObjMapForValue = function (pickerObj, pickObjKey, pickObjValue, curVal, obj, objKey) {
            var index = -1;
            for (i = 0; i < pickerObj.currentPickValues.length; i++) {
                if (pickerObj.currentPickValues[i][pickObjKey] == curVal) {
                    index = i;
                    break;
                }
            }
            if (index != -1) {
                setObject(obj, pickerObj.currentPickValues[index][pickObjValue], objKey);
            }
        };


        /**
         *  Set the Flag as true when server data received for Picker Component which sets currentPickValues
         */
        var setServerDataReceived = function () {
            if (CONST().UPDATE_SERVER_DATA_RECEIVED_FLG) {
                serverDataReceivedFlg = true;
            }
        };
        /**
         * Return the object of this controller's field component.
         *
         * @returns {*}
         */
        this.getObject = function () {
            if (uiFieldComponent == null) {
                var obj = window.editor.findComponents(CONST().FIELD, 'Field');
                if (obj[0]) {
                    uiFieldComponent = obj[0];
                }
            }
            return uiFieldComponent;
        };

        /**
         * Redraw the controller's field component.
         */
        this.redrawComponent = function () {
            return this.getObject().redraw();
        };

        /**
         * Return true if server call ends for data received (when field type is 'PTR').
         *
         * @returns {boolean}
         */
        this.isServerDataReceived = function () {
            return serverDataReceivedFlg;
        };

        this.setPickerValuesFromMultiSelectValues = function (validLables, validValues) {
            var obj = this.getObject();
            var totalValues = validLables.length;
            var hashValueFieldName = CONST().PICKER_VAL_FLD;
            for (indx = 0; indx < totalValues; indx++) {
                obj.currentPickValues[indx] = {0: validLables[indx]};
                obj.currentPickValues[indx][hashValueFieldName] = ""+validValues[indx];
            }
            this.redrawComponent();
        };

        /**
         * convert to Multiselect Type Component
         */
        this.toMultiSelect = function () {
            var pickerObj = this.getObject();
            if (pickerObj.type.type != "ptr") {
                return false;
            }

            var curVal = pickerObj.getValue();
            if (this.isServerDataReceived()) {
                pickerObj.type.validlabels = [];
                pickerObj.type.validvalues = [];
                var index = -1;
                var pickerValue = "";
                var hashValueFieldName = CONST().PICKER_VAL_FLD;
                for (i = 0; i < pickerObj.currentPickValues.length; i++) {
                    pickerObj.type.validlabels.push(pickerObj.currentPickValues[i][0]);
                    pickerObj.type.validvalues.push(pickerObj.currentPickValues[i][hashValueFieldName]);
                    if (index == -1 && pickerObj.currentPickValues[i][0] == curVal) {
                        index = i;
                    }
                }
                if (index != -1) {
                    pickerValue = pickerObj.type.validvalues[index];
                }
                pickerObj.setValue(pickerValue);
            } else {
                setObjByRefObjMapForValue(pickerObj, "validlabels", "validvalues", curVal, pickerObj, null);
            }

            pickerObj.type.type = "multipick";
            pickerObj.type.ptype = "multipick";
            pickerObj.type.delimiter = '#~#';
            pickerObj.renderer = null;
            pickerObj.uiControl = 'ControlMultiPick';
            pickerObj.userUIControl = CONST().USER_UI_CONTROL + "MultiPick";
        };

        /**
         * convert to Picker Type Component
         */
        this.toPicker = function () {
            var pickerObj = this.getObject();
            var data = view.gatherData();
            var culVal = data[pickerObj.path];

            if (culVal) {
                curVal = culVal.split('#~#');
                if (curVal.length == 1) {
                    curVal = curVal[0].toString();
                    if (this.isServerDataReceived()) {
                        setObjByPickerObjMapForValue(pickerObj, CONST().PICKER_VAL_FLD, "0", curVal, pickerObj, null);
                    } else {
                        setObjByRefObjMapForValue(pickerObj, "validvalues", "validlabels", curVal, pickerObj, null);
                    }
                } else {
                    pickerObj.setValue('');
                }
            }

            pickerObj.updateProperty(['type', 'type'], 'ptr');
            pickerObj.updateProperty(['type', 'delimiter'], '');
            pickerObj.userUIControl = CONST().USER_UI_CONTROL;
            pickerObj.renderer = null;
            pickerObj.uiControl = 'ControlPtr';
        };

        /**
         * It will gather all the view data and modify <PICKER_VAL_FLD> field data to its corresponsing hash value.
         *
         * @param sourceModify
         * @returns {{}}
         */
        this.getViewDataWithCtrlFieldHashVal = function (sourceModifyFlg) {
            var data = {};
            if (sourceModifyFlg === true) {
                data = view.gatherData();
            } else {
                /* Used for IE-10 - older Browser*/
                if (typeof Object.assign != 'function') {
                    data = jq.extend({}, view.gatherData());
                } else {
                    data = Object.assign({}, view.gatherData());
                }
            }

            var currObj = this.getObject();
            var field = CONST().FIELD;
            if (data[field] && currObj.type.type == "ptr") {
                var curVal = data[field];
                if (this.isServerDataReceived()) {
                    setObjByPickerObjMapForValue(currObj, "0", CONST().PICKER_VAL_FLD, curVal, data, field);
                } else {
                    setObjByRefObjMapForValue(currObj, "validlabels", "validvalues", curVal, data, field);
                }
            }
            return data;
        };

        this.init = function () {
            var functionBody = "this.meta = meta; return this;";
            var functionEle = new Function('meta', functionBody);
            var functionEleMultiSelect = new Function('meta', functionBody);
            var thisCtrl = this;
            /*
              Inheriting InputControlPtr -  userUIControlName. Only used when Field is of Picker type ('ptr')
              @type {string}
             */
            functionEle.inheritsFrom(InputControlPtr);
            functionEleMultiSelect.inheritsFrom(InputControlMultiPick);
            /**
             * <USER_UI_CONTROL>InputControlPtr is inherited from InputControlPtr. Also Ctrl field has some other
             * field types such as multiselect and Ptr type. Hence this function will be executed only when
             * '<FIELD>' element is of type "ptr" as well as it should have userUIControl set as "<USER_UI_CONTROL>".
             *
             * @param values
             * @param serverCount
             */
            functionEle.prototype.updateValues = function (values, serverCount) {
                InputControlPtr.prototype.updateValues.call(this, values, serverCount);
                /* serverCount will be undefied if we have not received data from the server  */
                if (serverCount !== undefined && (!thisCtrl.isServerDataReceived())) {
                    setServerDataReceived();
                }
            };

            window[CONST().USER_UI_CONTROL] = functionEle;
            window[CONST().USER_UI_CONTROL + "MultiPick"] = functionEleMultiSelect;
            return thisCtrl;
        };
    },
    enumerable: false,
    writable: false,
    configurable: false
});

/**
 * Group Component Controller of @type - PickerMultiSelectCtrl
 */
Object.defineProperty(this, 'GroupCtrl', {
    value: (function () {
        return new PickerMultiSelectCtrl({
            FIELD: "GROUPS",
            PICKER_VAL_FLD: "RECORDNO",
            USER_UI_CONTROL: "GroupInputControl",
            UPDATE_SERVER_DATA_RECEIVED_FLG: (globTable === 'F')
        }).init();
    })(),
    enumerable: true,
    writable: false,
    configurable: false
});

/**
 * Attached additional functionality for Standard Save/SaveAndNew Button.
 *
 * @returns {boolean}
 */
function stdSaveButton() {
    GroupCtrl.getViewDataWithCtrlFieldHashVal(true);
    updateFixedSectionPosition();
    return true;
}

function updateFixedSectionPosition() {
  var shiftedTop = !window.editor.validateData() ? jq('#ErrorMsg').height() - 5 : 0;
  var fixedSection = jq('.qx-fixed-section');
  fixedSection.css('top', (FIXED_SECTION_DEFAULT_TOP + shiftedTop) + 'px');
}

/**
 * Creates a temporary $report object and retrieves fusion chart dataXML
 */
function saveAndRefreshGraph() {
    updateFixedSectionPosition();
    // Check for all required fields
    if ( !window.editor.validateData() ) {
        return false;
    }

    jq('#graphContainer').html("Please wait ...");

    var data = GroupCtrl.getViewDataWithCtrlFieldHashVal(false);
    var callParams = {
        'function': 'createTempReportAndGetFusionChartData',
        'entity': 'graphsmanager',
        'data': data
    };
    var updateArgs = {};
    var qrequestBatch = window.view.getQRequestBatch();
    qrequestBatch.addRequest('QRequest', callParams, RespProcesser_refreshChart, updateArgs, false);
    qrequestBatch = null;
}

function RespProcesser_refreshChart(params, m_response) {
    var data = JSON.parse(m_response);

    if (data["status"] === "pass") {

        var type = data["type"];
        var tempid = 'GLGraph' + Date.now();

        FusionCharts.addEventListener("rendered", function (eventObject) {
          if (eventObject.sender.args.renderAt) {
            var graphEl = document.getElementById(eventObject.sender.args.renderAt);
            var fixedSections = document.getElementsByClassName('qx-visibility-section-check');
            if (graphEl && (fixedSections.length > 0)) {
              var fixedSectionEl = fixedSections[0];
              if ((typeof elementIsInViewport === 'function') && !elementIsInViewport(fixedSectionEl)) {
                fixedSectionEl.classList.add('qx-absolute-section');
              } else {
                fixedSectionEl.classList.remove('qx-absolute-section');
              }
            }
          }
        });

        chart = FusionCharts.render({
            type: type,
            id: tempid,
            renderAt: "graphContainer",
            exportShowMenuItem: "0",
            width: "100%",
            height: "100%",
            dataFormat: "xml",
            dataSource: data["data"]
        });
    } else {
        jq('#graphContainer').html(data["message"]);
    }
}


function setTrendingDD() {
    data = view.gatherData();
    // formeditor dont like 0 value
    colTrendingObj = window.editor.view.getField('TRENDING');
    colTrendingObj.setValue(data['TRENDING']);
}

/**
 * Override the report type field
 */
function ReTypeField() {
}

ReTypeField.inheritsFrom(Field);

/**
 * Re-draw graph type control
 */
ReTypeField.prototype.draw = function () {
    var result = Field.prototype.draw.call(this);
    drawGraphTypeImages();
    return result;
}

function drawGraphTypeImages() {
    data = view.gatherData();
    var parent = jq('#graphTypeContainer');

    jq(parent).empty(); // clear its content

    chartTypes = [
        {'uikey': 'GRAPH.pie.1', 'key': 'pie', 'label': 'IA.PIE'},
        {'uikey': 'Doughnut', 'key': 'doughnut', 'label': 'IA.DOUGHNUT_HTML_ONLY'},
        {'uikey': 'GRAPH.line.1', 'key': 'line', 'label': 'IA.LINE'},
        {'uikey': 'GRAPH.col.1', 'key': 'column', 'label': 'IA.COLUMN'},
        {'uikey': 'Stacked column', 'key': 'column_stacked', 'label': 'IA.STACKED_COLUMN_HTML_ONLY'},
        {'uikey': 'Parallel column', 'key': 'column_parallel', 'label': 'IA.PARALLEL_COLUMN_HTML_ONLY'},
        {'uikey': 'GRAPH.bar.1', 'key': 'bar', 'label': 'IA.BAR'},
        {'uikey': 'Stacked bar', 'key': 'bar_stacked', 'label': 'IA.STACKED_BAR_HTML_ONLY'},
        {'uikey': 'Area', 'key': 'area', 'label': 'IA.AREA_HTML_ONLY'},
        {'uikey': 'Stacked area', 'key': 'area_stacked', 'label': 'IA.STACKED_AREA_HTML_ONLY'},
        {'uikey': 'Waterfall', 'key': 'waterfall', 'label': 'IA.WATERFALL_HTML_ONLY'}
    ];

    chartTypes = chartTypes.map(function(obj) {
        return {
            'uikey': obj.uikey,
            'key': obj.key,
            'label': GT(obj.label)
        };
    });

    var hideWaterfall = false;
    var hideOtherCharts = false;
    if (window.editor.view.state === "showedit") {
        if (data['RE_TYPE'] === 'Waterfall') {
            hideOtherCharts = true;
        } else {
            hideWaterfall = true;
        }
    } else if (window.editor.view.state === "showview") {
        hideOtherCharts = true;
    }

    for (var i = 0; i < chartTypes.length; i++) {

        if (hideOtherCharts && data['RE_TYPE'] !== chartTypes[i]['uikey']) {
            continue;
        }

        if (hideWaterfall && chartTypes[i]['uikey'] === 'Waterfall') {
            continue;
        }

        var selected = ''
        if (data['RE_TYPE'] === chartTypes[i]['uikey']) {
            selected = '_selected';
        }
        aTag = "<a href='#' "
            + " id='ct_" + chartTypes[i]['key'] + "'"
            + " title='" + chartTypes[i]['label'] + "'"
            + " type='" + chartTypes[i]['key'] + "'";

        if (window.editor.view.state !== "showview") {
            aTag += " onclick='setGraphType(\"" + chartTypes[i]['uikey'] + "\");saveAndRefreshGraph();'";
        }

        aTag += " class='" + chartTypes[i]['key'] + selected + "'"
            + ">"
            + "</a>";

        jq(parent).append(aTag);
    }

    jq("#graphTypeContainer a").bind({
        "mouseenter mouseleave": function () {
            jq(this).toggleClass(this.type + "_hover");
        },
    });

}

function setGraphType(type) {

    jq("#_obj__RE_TYPE").val(type);
    jq("#_obj__RE_TYPE").change();
    drawGraphTypeImages();
}

function setGraphTypeDependencies() {
    seconSeriesObj = window.editor.findComponents('SECONDARYSERIES', 'Field')[0];
    data = view.gatherData();

    // if it is waterfall then call the below function and return
    if (setWaterfallDependencies()) {
        return true;
    }
    if (data['RE_TYPE'].indexOf('pie') != -1 || data['RE_TYPE'].indexOf('Doughnut') != -1) {
        seconSeriesObj.updateProperty('disabled', true);
        seconSeriesObj.parentComponent.redraw();
        seconSeriesObj.setValue("N");
        setSeries(true);
    }
    else {
        seconSeriesObj.updateProperty('disabled', false);
        seconSeriesObj.parentComponent.redraw();
        setSeries(false);
    }
    setSeriesDependencies();
    showHideIncludePeriodHeaderOption();
}

function setDDList(objName, newLabels, newValues) {
    fldObj = window.editor.findComponents(objName, 'Field')[0];
    curSelectedVal = fldObj.getValue();

    fldObj.updateProperty(['type', 'validvalues'], newValues);
    fldObj.updateProperty(['type', 'validlabels'], newLabels);

    if (jq.inArray(curSelectedVal, newValues) > -1) { //if found
        fldObj.setValue(curSelectedVal);
    } else {
        fldObj.setValue(newValues[0]);
    }

    fldObj.parentComponent.redraw();
}

function setWaterfallDependencies() {
    data = view.gatherData();

    setWaterFallExpansions();

    seriesobj = window.editor.findComponents('PRIMARYSERIES', 'Field')[0];
    seconSeriesObj = window.editor.findComponents('SECONDARYSERIES', 'Field')[0];
    seconSeriesSec = window.editor.findComponentsById('secondaryDS')[0];

    if (data['RE_TYPE'] !== 'Waterfall') {
        //seconSeriesSec.updateProperty('hidden', false);
        //seconSeriesSec.parentComponent.redraw();
        //window.editor.view.getField('ASOF').updateProperty('hidden', true);

        jq('#primaryDS_li_0').parent().parent().addClass('alignLeft2Ctrl');

        seriesobj.updateProperty('hidden', false);
        seconSeriesObj.updateProperty('disabled', false);
        seriesobj.parentComponent.redraw();
        return false;
    }

    //seconSeriesSec.updateProperty('hidden', true);
    //seconSeriesSec.parentComponent.redraw();

    //window.editor.view.getField('ASOF').updateProperty('hidden', false);

    jq('#primaryDS_li_0').parent().parent().removeClass('alignLeft2Ctrl');

    // reset primary and secondary series settigns and disable them
    setSeries(false);
    seriesobj.updateProperty('hidden', true);
    seriesobj.setValue('A');
    seconSeriesObj.updateProperty('disabled', true);
    setSecondSeries();
    seconSeriesObj.setValue('N');

    //hide Expansion section
    hideUnhideFields(periodExpansionFields, true);

    seriesobj.parentComponent.redraw();

    // Reset period comparison checkbox
    showHidePeriodComparison();

    return true;
}

function setWaterFallExpansions() {
    data = view.gatherData();

    if (data['RE_TYPE'] !== 'Waterfall') {
        hideUnhideFields(wfExpansionFields, true);
        hideUnhideFields(periodExpansionFields, false);
        return false;
    }

    if (globTable !== 'T') {
        setWFSeconSeries();
        setWFDimensions();
        setWFDimStruc();
    }

    hideUnhideFields(wfExpansionFields, false);
    hideUnhideFields(periodExpansionFields, true);

    setWFSeconSeriesDependencies();

    return true;
}

function setWFSeconSeries(periodValues, periodLabels) {
    if (typeof periodValues !== 'undefined') {
        periodValues = periodValues;
    } else {
        expby = window.editor.view.getField('EXPANDBY');
        periodValues = expby.type.validvalues;
    }

    if (typeof periodLabels !== 'undefined') {
        periodLabels = periodLabels;
    } else {
        expby = window.editor.view.getField('EXPANDBY');
        periodLabels = expby.type.validlabels;
    }


    newLabels = [GT('IA.ACCOUNT_GROUPS_OR_ACCOUNTS'), GT('IA.REPORTING_PERIOD')];
    newValues = ['AGORA', 'P'];

    //newLabels[newLabels.length] = 'Reporting period';
    //newValues[newValues.length] = 'P';
    //
    if (globTable !== 'T' && SHOWDIMSTRUC === 'T') {
        newLabels[newLabels.length] = GT('IA.DIMENSION_STRUCTURE');
        newValues[newValues.length] = 'ACCT';
    }

    newLabels = newLabels.concat(aDimLab.slice());
    newValues = newValues.concat(aDimVal.slice());


    //for (var i = 0; i < periodValues.length; i++) {
    //    if (periodLabels[i] !== 'None') {
    //        newLabels[newLabels.length] = periodLabels[i];
    //        newValues[newValues.length] = periodValues[i];
    //    }
    //}

    setDDList('WF_SECONSERIES', newLabels, newValues);
}

function setWFSeconSeriesDependencies() {
    var hideDimFld = true;
    var hidePeriodFld = true;
    data = view.gatherData();

    if (data['WF_SECONSERIES'] === 'ACCT') {
        hideDimFld = false;
    } else if (data['WF_SECONSERIES'] === 'P') {
        hidePeriodFld = false;
    }

    window.editor.view.getField('WF_DIMENSION').hidden = hideDimFld;
    window.editor.view.getField('WF_DIMSTRUC').hidden = hideDimFld;
    window.editor.view.getField('WF_EXPANDBY').hidden = hidePeriodFld;

    window.editor.view.getField('WF_DIMSTRUC').parentComponent.redraw();

    showHideIncludePeriodHeaderOption();
}

function setWFDimensions() {
    var newLabels = [];
    var newValues = [];

    // you can choose dimension only if that has structures defined
    for (var i = 0; i < aDimVal.length; i++) {
        if (DIMSTRUC[aDimVal[i]] && DIMSTRUC[aDimVal[i]]['val'].length > 0) {
            newLabels[newLabels.length] = aDimLab[i];
            newValues[newValues.length] = aDimVal[i];
        }
    }

    setDDList('WF_DIMENSION', newLabels, newValues);
}

function setWFDimStruc() {
    data = view.gatherData();
    selectedDim = data['WF_DIMENSION'];
    if (SHOWDIMSTRUC === 'T' && (selectedDim !== undefined || selectedDim !== null)) {
        setDDList('WF_DIMSTRUC', DIMSTRUC[selectedDim]['lab'], DIMSTRUC[selectedDim]['lab']);
    }
}

function initMultipleBooks() {
    var rb = window.editor.view.getField('REPORTING_BOOK_ADJUSTMENTBOOKS');
    var adjBookVals = rb.type.validvalues;
    var reportingBookType = window.editor.view.getField('REPORTING_BOOK_LISTTYPE');
    if (adjBookVals == null || adjBookVals.length == 0) {
        // if no additional books hide the single multiple select list
        jq("body").addClass('hidereportingbooklisttype');
        window.editor.view.getField('REPORTING_BOOK_LISTTYPE').updateProperty('disabled', true);
        reportingBookType.setValue('single');
    }

    var otherDefaultBooks = getOtherDefaultBooks();
    var getState = window.editor.getGlobalFieldValue('_state');
    if( getState != "showedit" && (otherDefaultBooks.length > 0)) {
        reportingBookType.setValue('multiple');
    }
}

function translateReportingBookHelp(adjBookTxts, adjBookVal, reportingBooks) {
    var reportingBookValue = [];
    if(reportingBooks[0] != ""){
        for(var i = 0; i < reportingBooks.length; i++){
            reportingBooks[i] = reportingBooks[i] == "GAAP" ? "GAAPADJ" : reportingBooks[i];
            reportingBooks[i] = reportingBooks[i] == "TAX" ? "TAXADJ" :  reportingBooks[i];
            reportingBookValue.push(adjBookTxts[adjBookVal.indexOf(reportingBooks[i])]);
        }
    }
    return reportingBookValue;
}

function setReportingBookType(redraw) {
    var reportingBookType = window.editor.view.getField('REPORTING_BOOK_LISTTYPE');
    var reportingBookTypeValue = reportingBookType.getValue();
    var rb = window.editor.view.getField('REPORTING_BOOK_ADJUSTMENTBOOKS');
    var adjBookTxts = rb.type.validlabels;
    var adjBookVal = rb.type.validvalues;
    var filter = window.editor.view.getField('REPORTING_BOOK_ONLYFILTERADDBOOKS').getValue();
    var reportingBookMultiple =  window.editor.view.getField('REPORTING_BOOK_MULTIPLE').getValue();
    var defBook =""
    var reportingBooks = "";
    var reportingBookHlp = [];

    if (!IS_MULTIBOOK || window.editor.view.state === "showview") {
        //Need to check for REPORTINGBOOKHELP
        if((reportingBookMultiple !== undefined) && (reportingBookMultiple.length != 0)) {
            reportingBooks = JSON.parse(reportingBookMultiple);
            reportingBookHlp = translateReportingBookHelp(adjBookTxts,adjBookVal,reportingBooks);
        }

        if (filter === 'nofilter' || filter == "") {
            defBook = window.editor.view.getField('REPORTING_BOOK2').getValue();
            reportingBookHlp.push(defBook);
        }

        if(reportingBookHlp.length > 1){
            reportingBookHlp = reportingBookHlp.sort().join(",");
        } else {
            reportingBookHlp.join("");
        }
        window.editor.view.getField('REPORTINGBOOKHELP').setValue(reportingBookHlp);
        window.editor.view.getField('REPORTING_BOOK_LISTTYPE').updateProperty('hidden', true);
        window.editor.view.getField('REPORTING_BOOK').updateProperty('hidden', true);
        window.editor.view.findComponents(undefined, 'Buttons')[0].updateProperty('hidden', true);
        window.editor.view.findComponents(undefined, 'Buttons')[0].redraw();
    } else {
        if (reportingBookTypeValue == 'single') {
            window.editor.view.getField('REPORTING_BOOK').updateProperty('hidden', false);
            window.editor.view.getField('REPORTING_BOOK2').setValue('hidden', window.editor.view.getField('REPORTING_BOOK').getValue());
            //setting Value of REPORTINGBOOKHELP to Null if reportingBokkTypeValue =  single
            window.editor.view.getField('REPORTING_BOOK_MULTIPLE').setValue('[]');
            window.editor.view.getField('REPORTINGBOOKHELP').setValue("");
            window.editor.view.findComponents(undefined, 'Buttons')[0].updateProperty('hidden', true);
        } else {
            window.editor.view.getField('REPORTING_BOOK').updateProperty('hidden', true);
            //if selected for MultiBook, Default Values are Set Here
            var otherDefaultBooks = getOtherDefaultBooks();
            var preSelectedValues = [];
            var reportingBookValue = [];
            //setting DefaultBook As Accural or Cash
            if (filter === 'nofilter' || filter == "") {
                defBook = window.editor.view.getField('REPORTING_BOOK').getValue();
                window.editor.view.getField('REPORTING_BOOK2').setValue(defBook);
            }

            if ( window.editor.getGlobalFieldValue('_state') !== "showedit" ) {
                    var splitOtherDefaultBooks = otherDefaultBooks.split("#~#").sort();
                    for(var i = 0; i < splitOtherDefaultBooks.length; i++){
                        preSelectedValues.push(adjBookTxts[adjBookVal.indexOf(splitOtherDefaultBooks[i])]);
                    }
                    window.editor.view.getField('REPORTING_BOOK_MULTIPLE').setValue(JSON.stringify(splitOtherDefaultBooks));
                    defBook != "" ? preSelectedValues.push(defBook) : preSelectedValues;
                    window.editor.view.getField('REPORTINGBOOKHELP').setValue(preSelectedValues.sort().join(","))
            } else {
                var reportingMultipleBooksStr = window.editor.view.getField('REPORTING_BOOK_MULTIPLE').getValue();
                reportingBooks = reportingMultipleBooksStr != '[""]' ? JSON.parse(reportingMultipleBooksStr) : otherDefaultBooks.split("#~#");
                reportingBookValue = translateReportingBookHelp(adjBookTxts,adjBookVal,reportingBooks);
                defBook != "" ? reportingBookValue.push(defBook) : reportingBookValue;
                window.editor.view.getField('REPORTINGBOOKHELP').setValue(reportingBookValue.sort().join(","));
            }

       //     window.editor.view.getField('REPORTING_BOOK').updateProperty('hidden', true);
            window.editor.view.findComponents(undefined, 'Buttons')[0].updateProperty('hidden', false);
        }
        window.editor.view.findComponents(undefined, 'Buttons')[0].redraw();
        if (redraw !== false) {
            reportingBookType.parentComponent.parentComponent.redraw();
        }
    }

    setReportingBookDependenies(redraw);

}

function setReportingBookDependenies(redraw) {
    //debugger;
    var rb = window.editor.view.getField('REPORTING_BOOK').getValue();

    if (locationRequiredIfActualBook) {
        var rb2 = window.editor.view.getField('REPORTING_BOOK2').getValue();
        var rblt = window.editor.view.getField('REPORTING_BOOK_LISTTYPE').getValue();
        var selectedBooks = window.editor.view.getField('REPORTING_BOOK_MULTIPLE').getValue();
        if (selectedBooks == null || selectedBooks == undefined || selectedBooks == '') {
            selectedBooks = [];
        } else {
            selectedBooks = JSON.parse(selectedBooks);
            if (selectedBooks.length == 0) {
                selectedBooks = [];
            }
        }
        if ((rblt == 'single' && rb == 'ACCRUAL') || (rblt == 'multiple' && rb2 == 'ACCRUAL')) {
//            window.editor.view.getField('FILTERLOC').updateProperty('required', true);
            window.editor.view.getField('LOCATION').updateProperty('required', true);
            window.editor.view.getField('FILTERLOCATION').setValue('specifichierarchy');
        } else if (rblt == 'multiple' && (rb2 == '' || rb2 == null) && selectedBooks.length > 1) {
//            window.editor.view.getField('FILTERLOC').updateProperty('required', true);
            window.editor.view.getField('LOCATION').updateProperty('required', true);
            window.editor.view.getField('FILTERLOCATION').setValue('specifichierarchy');
        } else {
//            window.editor.view.getField('FILTERLOC').updateProperty('required', false);
            window.editor.view.getField('LOCATION').updateProperty('required', false);
        }
        if (redraw !== false) {
//            window.editor.view.getField('FILTERLOC').parentComponent.redraw();
            window.editor.view.getField('LOCATION').parentComponent.redraw();
        }
    }
}

function openAdjBookDialog() {
    var rb = window.editor.view.getField('REPORTING_BOOK2');
    var companyBookVals = rb.type.validvalues;
    var companyBookTxts = rb.type.validlabels;
    var rb = window.editor.view.getField('REPORTING_BOOK_ADJUSTMENTBOOKS');
    var adjBookVals = rb.type.validvalues;
    var adjBookTxts = rb.type.validlabels;
    //Getting Other Default Books setting
    var otherDefaultBooks = getOtherDefaultBooks();

    openAdjBookDialogInternal("IA.SELECT_REPORTING_BOOKS", companyBookTxts, companyBookVals, adjBookTxts, adjBookVals,otherDefaultBooks);
}

function openAdjBookDialogInternal(dialogTitle, companyBookTxts, companyBookVals, adjBookTxts, adjBookVals, otherDefaultBooks) {
    jq('#adjbookSelection').remove(); //it will no-op if it is not there.
    var $text = jq(document.createElement("div"));
    $text.css("font-size", "12px");
    $text.css("margin", "0px");
    $text.css("white-space", "normal");
    $text.html(GT('IA.SELECT_ONE_OR_MORE_REPORTING_BOOKS'));

    var $p = jq(document.createElement("P"));
    $p.css("font-size", "12px");
    $p.css("margin", "10px");
    $p.css("margin-bottom", "0px");
    $p.css("padding-left", "10px");
    $p.css("white-space", "normal");

    //todo: get the current values
    var selectedBooks = window.editor.view.getField('REPORTING_BOOK_MULTIPLE').getValue();
    if (selectedBooks == null || selectedBooks == undefined || selectedBooks == '') {
        selectedBooks = [];
    } else if(selectedBooks == '[""]') {
        selectedBooks = getOtherDefaultBooks().split("#~#");
    } else {
        selectedBooks = JSON.parse(selectedBooks);
        if (selectedBooks.length == 0) {
            selectedBooks = [];
        }
    }

    var companyBookSelected = window.editor.view.getField('REPORTING_BOOK2').getValue();
    if (companyBookSelected == null && selectedBooks.length > 0) {
        window.editor.view.getField('REPORTING_BOOK2').setValue('');
        companyBookSelected = '';
    }
    if (companyBookSelected == '' && selectedBooks.length == 0) {
        companyBookSelected = companyBookVals[0];
    }

    $p.append(openAdjBookDialog_buildControls(companyBookTxts, companyBookVals, companyBookSelected, adjBookTxts, adjBookVals, selectedBooks, otherDefaultBooks))

    var dialogElms = jq("<DIV><\/DIV>");
    dialogElms.addClass('openAdjBookDialog_buildControls');
    dialogElms.append($text[0]);

    var dialogParams = {
        title: GT(dialogTitle),
        "open": function () {
            $div = jq(this).find(".dialogContent");
            $div.append($p);
        },
        contentType: 'html',
        content: dialogElms.innerHTML,
        width: '550px',
        dialogClass: 'addDimensionDialog',
        closeText: 'X',
        modal: true,
        resizable: false,
        minHeight: '0px',
        position: [225, 5],
        buttons: {},
        close: function () {
            jq(this).dialog("destroy");
        },
        dragStop: function (event, ui) {
            if (ui.offset.top < 0) {
                jq(this).closest(".ui-draggable").css('top', '0px');
            }
        }

    };

    dialogParams.buttons[GT('IA.SET')] = function () {
        var selections = [];
        var selectedAdjBooks = '';
        var selectedIdx = 0;
        jq('#adjbookSelection option').each(function () {
            var $this = jq(this);
            if ($this.prop('selected')) {
                selections[selections.length] = $this.val();
                if (selectedIdx > 0) {
                    selectedAdjBooks += ', ';
                }
                selectedAdjBooks += $this.text();
                selectedIdx++;
            }
        });

        var json = JSON.stringify(selections);
        var reportingBook2Value = jq('#reportingBook2')[0].options[jq('#reportingBook2')[0].selectedIndex].value;
        var reportingBook2Text = jq('#reportingBook2')[0].options[jq('#reportingBook2')[0].selectedIndex].text;

        if (selections.length == 0 && reportingBook2Value == '') {
            alert(GT('IA.YOU_MUST_CHOOSE_AT_LEAST_ONE_REPORTING_BOOK'));
        } else {
            window.editor.view.getField('REPORTING_BOOK').setValue(reportingBook2Value);
            window.editor.view.getField('REPORTING_BOOK2').setValue(reportingBook2Value);
            window.editor.view.getField('REPORTING_BOOK_MULTIPLE').setValue(json);

            var rptBookHlp = '';
            if (jq('#POPUP_REPORTING_BOOK_ONLYFILTERADDBOOKS').val() === 'nofilter') {
                rptBookHlp += reportingBook2Text + ", ";
            }

            if (selectedAdjBooks !== '') {
                rptBookHlp += selectedAdjBooks;
            }
        }

        window.editor.view.getField('REPORTINGBOOKHELP').setValue(rptBookHlp);
        window.editor.view.getField('REPORTING_BOOK_ONLYFILTERADDBOOKS').setValue(jq('#POPUP_REPORTING_BOOK_ONLYFILTERADDBOOKS').val());

        setReportingBookDependenies();
        jq(this).dialog("close");
    };
    dialogParams.buttons[GT('IA.CANCEL')] = function () {
        jq(this).dialog("close");
    };

    showJQDialog(dialogParams)

    return;
}

function openAdjBookDialog_buildControls(companyBookTxts, companyBookVals, companyBookSelected, adjustBookTxts, adjustBookVals, selectList, filterSelect, otherDefaultBooks) {

    //Couldn't access otherDefaultBooks from function arguments directly. So, I've used args array to access last param
    otherDefaultBooks = arguments[arguments.length - 1];
    var $sel1 = jq(document.createElement('SELECT'));
    $sel1.prop('id', 'adjbookSelection');
    $sel1.prop('multiple', 'multiple');
    $sel1.prop('size', 'expandBy8_section');
    $sel1.prop('selected', true);
    $sel1.addClass('multiselect')
    //$sel1.addClass('add-all')
    //$sel1.addClass('remove-all')

    var allOptions = [];
    var isAdjBookSelected = false;

    //array that saves keys for default values
    var preSelectedKeys = [];

    var splitOtherDefaultBooks = otherDefaultBooks.split("#~#");
    if(splitOtherDefaultBooks.length != 0 && (selectList.length == splitOtherDefaultBooks.length)){
        for(var i = 0; i < splitOtherDefaultBooks.length; i++){
            var optGroup = document.createElement('option');
            var $optGroup = jq(optGroup);
            var isOtherDefaultBook = jq.inArray(splitOtherDefaultBooks[i], adjustBookVals);
            preSelectedKeys.push(isOtherDefaultBook);
            if(isOtherDefaultBook >= 0){
                $optGroup.text(adjustBookTxts[isOtherDefaultBook]);
                $optGroup.val(adjustBookVals[isOtherDefaultBook]);
                $optGroup.attr("selected","selected");
                allOptions.push($optGroup);
            }
        }
    }

    jq(adjustBookTxts).each(function (k) {
        if(jq.inArray(k, preSelectedKeys) < 0){
            var optGroup = document.createElement('option')
            var $optGroup = jq(optGroup);
            $optGroup.text(adjustBookTxts[k]);
            $optGroup.val(adjustBookVals[k]);
            if (jq.inArray(adjustBookVals[k], selectList) >= 0) {
                $optGroup.attr('selected', 'selected');
                isAdjBookSelected = true;
            }
            allOptions[allOptions.length] = $optGroup[0];
        }
    });

    $sel1.append(allOptions);
    $sel1Span = jq(document.createElement('SPAN'));
    $sel1Span.append($sel1)

    var $multiselect = $sel1.multiselect({
        doubleClickable: true,
        swapTables: true,
        sortable: false
    });

    $multiselect.multiselect('add', function () {
        //debugger;
        jq(this).add();
    });

    $multiselect.multiselect('remove', function () {
        //debugger;
        jq(this).remove();
    });

    $sel1Span.find('div.ui-multiselect').css({
        'white-space': 'nowrap',
        'width': '500px',
        'height': '100px',
        'overflow': 'visible'
    });

    $sel1Span.find('div.ui-multiselect div.available, div.ui-multiselect div.selected').css({
        'display': 'inline',
        'width': '48%',
        'height': '127px',
        'border': 'none'
    });

    $sel1Span.find('div.ui-multiselect ul.available, div.ui-multiselect ul.selected').css({
        'border': '#aebed0 solid 1px',
        'height': '100px'
    });

    $sel1Span.find('div.ui-multiselect div.available').css('margin-right', '5px');

    $sel1Span.find('div.ui-multiselect div.ui-widget-header').css({
        'display': 'block',
        'background-color': 'white',
        'color': '#3f5d86',
        'font-weight': 'normal',
        'font-size': '12px',
        'border': 'none',
        'border-radius': '0',
        '-moz-border-radius': '0',
        '-webkit-border-radius': '0'
    })

    $sel1Span.find('div.ui-multiselect div.available .ui-widget-header').html('&nbsp;' + GT('IA.OTHER_AVAILABLE_BOOKS'));
    $sel1Span.find('div.ui-multiselect div.selected  .ui-widget-header').html('&nbsp;' + GT('IA.SELECTED_BOOKS'));


    var allOptions = [];
    var isCompanySelected = false;
    var $reportingBook2 = jq(document.createElement('SELECT'));
    $reportingBook2.attr('id', 'reportingBook2');
    jq(companyBookTxts).each(function (k) {
        var optGroup = document.createElement('option')
        var $optGroup = jq(optGroup);
        $optGroup.text(companyBookTxts[k]);
        $optGroup.val(companyBookVals[k]);
        if (companyBookVals[k] === companyBookSelected) {
            $optGroup.attr('selected', 'selected');
            isCompanySelected = true;
        }
        allOptions[allOptions.length] = $optGroup[0];
    });
    if (!isCompanySelected) {
        $reportingBook2[0].selectedIndex = 0;
    }
    $reportingBook2.append(allOptions);

    var table = document.createElement('table');
    table.style.marginTop = '20px';
    table.cellspacing = '4px';
    var row = table.insertRow(-1);
    var cell1_1 = row.insertCell(-1);
    var cell1_2 = row.insertCell(-1);
    cell1_1.style.textAlign = 'right';
    jq(cell1_2).text(GT('IA.MAIN_REPORTING_BOOK'));
    cell1_2.style.color = '#3f5d86';
    cell1_2.style.fontSize = '12px';
    jq(cell1_1).css('vertical-align', 'top');
    jq(cell1_1).css('padding-top', '16px');
    jq(cell1_2).append($reportingBook2);
    jq(cell1_2).append("<div style='color: grey;'>"+GT('IA.TO_COMBINE_AMOUNTS_FROM_MULTIPLE_BOOKS')+"</div>");
    jq(cell1_2).css('padding-bottom', '10px');

    var row = table.insertRow(-1);
    var cell1_1 = row.insertCell(-1);
    var cell1_2 = row.insertCell(-1);
    cell1_1.style.textAlign = 'right';
    jq(cell1_1).css('vertical-align', 'top');
    jq(cell1_1).css('padding-top', '16px');
    jq(cell1_2).append($sel1Span)

    var row1x1 = table.insertRow(-1);
    var cell1x1_1 = row1x1.insertCell(-1);
    var cell1x1_2 = row1x1.insertCell(-1);
    jq(cell1x1_2).append("<span style='color: grey;line-height: 1.25em'>"+GT('IA.TO_ADD_AN_AVAILABLE_BOOK_TO_THE_SELECTED_BOOKS')+"</span>");
    jq(cell1x1_2).css('vertical-align', 'top');


    var filterValue = window.editor.view.getField('REPORTING_BOOK_ONLYFILTERADDBOOKS').getValue();
    var filter = filterValue == 'filter' ? ' selected ' : '';
    var nofilter = filterValue == 'nofilter' ? ' selected ' : '';
    if (filterValue = null || filterValue == '') {
        var nofilter = filterValue == 'nofilter' ? ' selected ' : '';
    }

    var row = table.insertRow(-1);
    var cell1_1 = row.insertCell(-1);
    var cell1_2 = row.insertCell(-1);
    cell1_1.style.textAlign = 'right';
    jq(cell1_1).css('vertical-align', 'top');
    jq(cell1_1).css('padding-top', '16px');
    jq(cell1_2).css('padding-top', '8px')
    jq(cell1_2).append("<span style='color: rgb(63, 93, 134);font-size: 12px;'>"+GT('IA.USE_ENTRIES_FROM')+" </span>");
    jq(cell1_2).append('<select id="POPUP_REPORTING_BOOK_ONLYFILTERADDBOOKS"><option value="nofilter" ' + nofilter + ' >'+GT('IA.MAIN_REPORTING_BOOK_AND_SELECTED_BOOKS')+'</option><option value="filter" ' + filter + '>'+GT('IA.SELECTED_BOOKS_ONLY')+'</option></select>');
    var row1x1 = table.insertRow(-1);
    var cell1x1_1 = row1x1.insertCell(-1);
    var cell1x1_2 = row1x1.insertCell(-1);


    return table;
}

/**
 * Set primary series values
 *
 * @param noBudget
 * @returns {boolean}
 */
function setSeries(noBudget) {
    seriesobj = window.editor.findComponents('PRIMARYSERIES', 'Field')[0];
    if (!seriesobj) {
        return false;
    }

    var newLabels = [];
    var newValues = [];
    for (var i = 0; i < aSeriesLab.length; i++) {
        // if the selected value is Department/Location then don't show them in the secondary series
        if (noBudget && aSeriesVal[i] == 'B') {
            continue;
        }
        newLabels[newLabels.length] = aSeriesLab[i];
        newValues[newValues.length] = aSeriesVal[i];
    }

    setDDList('PRIMARYSERIES', newLabels, newValues);
}

/**
 * Set secondary series value based on primary series data
 *
 * @returns {boolean}
 */
function setSecondSeries() {
    data = view.gatherData();
    seriesobj = window.editor.findComponents('PRIMARYSERIES', 'Field')[0];
    seconSeriesObj = window.editor.findComponents('SECONDARYSERIES', 'Field')[0];

    if (!seconSeriesObj || seconSeriesObj.disabled) {
        setSeriesDependencies();
        return true;
    }

    curSerVal = seriesobj.getValue();
    curSeconSerVal = seconSeriesObj.getValue();

    var newLabels = [];
    var newValues = [];

    newLabels[newLabels.length] = GT('IA.NONE');
    newValues[newValues.length] = 'N';
    for (var i = 0; i < aSeriesLab.length; i++) {
        if (aSeriesVal[i] != curSerVal) {
            // if the selected value is Department/Location then don't show them in the secondary series
            if ((curSerVal != 'A' && curSerVal != 'P' && curSerVal != 'B') && (aSeriesVal[i] != 'A' && aSeriesVal[i] != 'P' && aSeriesVal[i] != 'B')) {
                continue;
            }

            newLabels[newLabels.length] = aSeriesLab[i];
            newValues[newValues.length] = aSeriesVal[i];
//            select = (curSeconSerVal == aSeriesVal[i]) ? 'select' : '';
//            seconSeriesObj.append('<option ' + select + ' value="' + aSeriesVal[i] + '">' + aSeriesLab[i] + '</option>');
        }
    }

    setDDList('SECONDARYSERIES', newLabels, newValues);

    setSeriesDependencies();
}

/**
 * Sets all the series dependencies
 */
function setSeriesDependencies() {
    data = view.getValue();
    hideExpandSection = false;

    var isWaterfall = false;
    if (data['RE_TYPE'] == 'Waterfall') {
        isWaterfall = true;
    }

    primarySeries = data['PRIMARYSERIES'];
    secondSeries = data['SECONDARYSERIES'];
    if (secondSeries !== 'N' && (
            primarySeries == 'DEP' ||
            primarySeries == 'LOC' ||
            secondSeries == 'DEP' ||
            secondSeries == 'LOC' || !(primarySeries == 'P' || secondSeries == 'P')
        )
    ) {
        hideExpandSection = true;
    }

    hideUnhideFields(periodExpansionFields, hideExpandSection);

    periods = window.editor.findComponents('PERIODS', 'Field')[0];
    pdummy = window.editor.findComponents('PERIODS_DUMMY', 'Field')[0];
    if (isWaterfall) {
        showOnlyWFCPeriodComparisonPeriods();
        setAsEnum(periods);
        if (data['WF_SECONSERIES'] === 'P') {
            moveFieldToDifSection('PERIODS', 'primaryDS', 1);
            pdummy.updateProperty('hidden', false);
        } else {
            moveFieldToDifSection('PERIODS', 'additionalParams', pdummy.parentComponent.children.indexOf(pdummy));
            pdummy.updateProperty('hidden', true);
        }
    }
    else if ((primarySeries === 'P' && data['psPeriodExpnasion'] === "true")
        || (secondSeries === 'P' && data['ssPeriodExpnasion'] === "true")
    ) {
        showOnlyPeriodComparisonPeriods();
        setAsEnum(periods);
        destsec = (primarySeries === 'P') ? 'primaryDS' : 'secondaryDS';
        moveFieldToDifSection('PERIODS', destsec, 1);
        pdummy.updateProperty('hidden', false);
    }
    else if (!(primarySeries === 'P' || secondSeries === 'P')
    ) {
        showAllPeriods();
        setAsEnum(periods);
        moveFieldToDifSection('PERIODS', 'additionalParams', pdummy.parentComponent.children.indexOf(pdummy));
        pdummy.updateProperty('hidden', true);
    } else {
        showAllPeriods();
        setAsMultiPick(periods);
        destsec = (primarySeries === 'P') ? 'primaryDS' : 'secondaryDS';
        moveFieldToDifSection('PERIODS', destsec, 1);
        pdummy.updateProperty('hidden', false);
    }

    gdummy = window.editor.findComponents('GROUPS_DUMMY', 'Field')[0];
    if (isWaterfall) {
        GroupCtrl.toPicker();
        if (data['WF_SECONSERIES'] === 'AGORA') {
            moveFieldToDifSection('GROUPS', 'primaryDS', 1);
            gdummy.updateProperty('hidden', false);
        } else {
            moveFieldToDifSection('GROUPS', 'additionalParams', gdummy.parentComponent.children.indexOf(gdummy));
            gdummy.updateProperty('hidden', true);
        }
    }
    else if (!(primarySeries == 'A' || secondSeries == 'A')) {
        GroupCtrl.toPicker();
        moveFieldToDifSection('GROUPS', 'additionalParams', gdummy.parentComponent.children.indexOf(gdummy));
        gdummy.updateProperty('hidden', true);
    } else {
        GroupCtrl.toMultiSelect();
        destsec = (primarySeries === 'A') ? 'primaryDS' : 'secondaryDS';
        moveFieldToDifSection('GROUPS', destsec, 1);
        gdummy.updateProperty('hidden', false);
    }

    // Reset period comparison checkbox
    showHidePeriodComparison();

    budgets = window.editor.findComponents('BUDGETS', 'Field')[0];
    bdummy = window.editor.findComponents('BUDGETS_DUMMY', 'Field')[0];
    if (isWaterfall || !(primarySeries == 'B' || secondSeries == 'B')) {
        setAsEnum(budgets);
        moveFieldToDifSection('BUDGETS', 'additionalParams', bdummy.parentComponent.children.indexOf(bdummy));
        bdummy.updateProperty('hidden', true);
    } else {
        setAsMultiPick(budgets);
        destsec = (primarySeries === 'B') ? 'primaryDS' : 'secondaryDS';
        moveFieldToDifSection('BUDGETS', destsec, 1);
        bdummy.updateProperty('hidden', false);
    }

    periods.renderer = null;
    budgets.renderer = null;
    periods.redraw();
    budgets.redraw();
    pdummy.redraw();
    gdummy.redraw();
    GroupCtrl.redrawComponent();
    //window.editor.findComponents('PRIMARYSERIES', 'Field')[0].redraw();


    // set change events
    if (!(primarySeries == 'P' || secondSeries == 'P')) {
        jq('#_obj__PERIODS').on('change', setPeriodDependencies);
    } else {
        jq('#available__obj__PERIODS').closest('table').on('click', setPeriodDependencies);
        jq('#available__obj__PERIODS').closest('table').on('dblclick', setPeriodDependencies);
    }

    enableDisableHideZeroCheckbox();
}

/**
 * Set control as normal select box
 *
 * @param obj
 */
function setAsEnum(obj) {
    obj.uiControl = 'ControlEnum';
    obj.updateProperty(['type', 'type'], 'enum');
    obj.updateProperty(['type', 'ptype'], 'enum');
    obj.updateProperty(['type', 'delimiter'], '');

    data = view.gatherData();
    var curVal = data[obj.path].split('#~#');
    if (curVal.length == 1) {
        obj.setValue(curVal[0]);
    } else {
        obj.setValue('');
    }
}

/**
 * Set colntrol as multi pick select box
 *
 * @param obj
 */
function setAsMultiPick(obj) {
    obj.uiControl = 'ControlMultiPick';
    obj.updateProperty('datasort', false);
    obj.updateProperty(['type', 'type'], 'multipick');
    obj.updateProperty(['type', 'ptype'], 'multipick');
    obj.updateProperty(['type', 'delimiter'], '#~#');
}

/**
 * Set control as text box
 *
 * @param obj
 */
function setAsText(obj) {
    obj.uiControl = 'ControlText';
    obj.updateProperty(['type', 'type'], 'text');
    obj.updateProperty(['type', 'ptype'], 'text');
}

/**
 * Set control as checkbox
 *
 * @param obj
 */
function setAsCheckbox(obj) {
    obj.uiControl = 'ControlBoolean';
}

/**
 * Show hide period comparison checkbox
 *
 * @param path                 period comparison checkbox path
 * @param associatedSeriesPath Associated series drop down
 */
function showHidePeriodComparison() {
    data = view.getValue();

    var sections = [
        {'associatedSeriesPath': 'PRIMARYSERIES', 'otherSeries': 'SECONDARYSERIES', 'path': 'psPeriodExpnasion'},
        {'associatedSeriesPath': 'SECONDARYSERIES', 'otherSeries': 'PRIMARYSERIES', 'path': 'ssPeriodExpnasion'}
    ];

    for (var i = 0; i < sections.length; i++) {
        path = sections[i]['path'];
        periodCompFld = window.editor.findComponents(path, 'Field')[0];

        if ((data[sections[i]['otherSeries']] === 'DEP' || data[sections[i]['otherSeries']] === 'LOC')
            || data[sections[i]['associatedSeriesPath']] !== 'P') {
            periodCompFld.setValue('N/A');
            setAsText(periodCompFld);
            periodCompFld.readonly = true;
            periodCompFld.hidden = true;
        } else {
            val = periodCompFld.getValue();
            val = (val === 'N/A') ? '' : val;
            periodCompFld.setValue(val);
            setAsCheckbox(periodCompFld);
            if (window.editor.view.state !== "showview") {
                periodCompFld.readonly = false;
                periodCompFld.hidden = false;
            }
        }

        periodCompFld.renderer = null;
        periodCompFld.parentComponent.redraw();
    }

    showHideExpansionFields();
}

function showHideIncludePeriodHeaderOption() {
    data = window.view.gatherData();

    var hide = false;
    if (data['RE_TYPE'] === 'Waterfall' && data['WF_SECONSERIES'] !== 'P') {
        hide = true;
    }

    var includePrHdr = window.editor.findComponents('NOPERIODHEADER', 'Field');
    if (includePrHdr) {
        includePrHdr = includePrHdr[0];
        includePrHdr.hidden = hide;
        includePrHdr.parentComponent.redraw();
    }
}

function showAllPeriods() {
    var periods = window.editor.findComponents('PERIODS', 'Field')[0];
    var newValues = [];
    var newLabels = [];

    for (var kp in kPeriodMap) {
        newValues[newValues.length] = kp;
        newLabels[newLabels.length] = kPeriodMap[kp];
    }
    periods.updateProperty(['type', 'validvalues'], newValues);
    periods.updateProperty(['type', 'validlabels'], newLabels);
}

function showOnlyWFCPeriodComparisonPeriods() {
    data = window.view.gatherData();

    if (data['WF_SECONSERIES'] !== 'P') {
        showAllPeriods();
        return true;
    }

    var periods = window.editor.findComponents('PERIODS', 'Field')[0];
    var newValues = [];
    var newLabels = [];

    for (var kp in kPeriodMap) {
        if (jq.inArray(kPeriodMap[kp], allowedWFCPeriodComparisions) > -1) { //if found
            newValues[newValues.length] = kp;
            newLabels[newLabels.length] = kPeriodMap[kp];
        }
    }
    periods.updateProperty(['type', 'validvalues'], newValues);
    periods.updateProperty(['type', 'validlabels'], newLabels);
}

function showOnlyPeriodComparisonPeriods() {
    var periods = window.editor.findComponents('PERIODS', 'Field')[0];
    var newValues = [];
    var newLabels = [];

    for (var kp in kPeriodMap) {
        if (jq.inArray(kPeriodMap[kp], allowedPeriodComparisions) > -1) { //if found
            newValues[newValues.length] = kp;
            newLabels[newLabels.length] = kPeriodMap[kp];
        }
    }
    periods.updateProperty(['type', 'validvalues'], newValues);
    periods.updateProperty(['type', 'validlabels'], newLabels);
}

/**
 * if EXPANDBY is 'T' which means 'None' is selected then hide SIMILARPRDCMP <field>
 */
function showHideSortByTimelineField() {
    var colCompareBySelectorObj = window.editor.view.getField('EXPANDBY');
    var colTrendingObj = window.editor.view.getField('TRENDING');
    var colSimilarPeriodCmpObj = window.editor.view.getField('SIMILARPRDCMP');
    if (colSimilarPeriodCmpObj && colSimilarPeriodCmpObj) {
        /* if EXPANDBY is 'T' which means 'None' is selected then hide SIMILARPRDCMP <field> */
        if (colCompareBySelectorObj.getValue() == "T" || colTrendingObj.getValue() == "0") {
            colSimilarPeriodCmpObj.setValue(false);
            colSimilarPeriodCmpObj.showHide(false);
        } else if (colSimilarPeriodCmpObj.hidden == true) {
            /* To retail last value selected for colSimilarPeriodCmpObj else by changing EXPANDBY it will get overide */
            colSimilarPeriodCmpObj.setValue(true);
            colSimilarPeriodCmpObj.showHide(true);
        }
    }
}

function showHideExpansionFields() {
    data = view.getValue();

    var sections = [
        {'sectionPath': 'primaryDS', 'dsPath': 'PRIMARYSERIES', 'path': 'psPeriodExpnasion'},
        {'sectionPath': 'secondaryDS', 'dsPath': 'SECONDARYSERIES', 'path': 'ssPeriodExpnasion'}
    ];

    for (var i = 0; i < sections.length; i++) {
        psExppath = sections[i]['path'];

        if (data[sections[i]['dsPath']] === 'P') {
            periods = window.editor.findComponents('PERIODS', 'Field')[0];

            if (data[psExppath] === 'true') {
                showOnlyPeriodComparisonPeriods();
                setAsEnum(periods);
                for (var j = 0; j < periodExpansionFields.length; j++) {
                    pushFieldToDifSection(periodExpansionFields[j], sections[i]['sectionPath']);
                    //window.editor.view.getField(filedList[i]).updateProperty('hidden', hideUnhide);
                    //window.editor.view.getField(filedList[i]).redraw();
                }
            } else {
                showAllPeriods();
                setAsMultiPick(periods);
            }
            periods.renderer = null;
            periods.redraw();
        }
    }

    setPeriodDependencies();
}

/**
 * Set default value for the given fields
 *
 * @param fld
 * @param defaultVal
 */
function setFldDefaultVal(fld, defaultVal) {
    if (fld.getValue() === '') {
        fld.setValue(defaultVal);
    }
}

/**
 * Set reporting period depencies
 *
 * @returns {boolean}
 */
function setPeriodDependencies() {

    data = view.getValue();
    var periodsSelected = data['PERIODS'].split('#~#');
    var obj = document.getElementsByName('selected__obj__PERIODS')[0];

    var isPeriodExpansionSelected = false;
    var sections = [
        {'sectionPath': 'primaryDS', 'dsPath': 'PRIMARYSERIES', 'path': 'psPeriodExpnasion'},
        {'sectionPath': 'secondaryDS', 'dsPath': 'SECONDARYSERIES', 'path': 'ssPeriodExpnasion'}
    ];

    for (var i = 0; i < sections.length; i++) {
        if (data[sections[i]['dsPath']] === 'P'
            && data[sections[i]['path']] === 'true'
        ) {
            isPeriodExpansionSelected = true;
            break;
        }
    }

    var colCompareBySelectorObj = window.editor.view.getField('EXPANDBY');
    var WFCompareBySelectorObj = window.editor.view.getField('WF_EXPANDBY');
    var colTrendingObj = window.editor.view.getField('TRENDING');
    var colPeriodOffsetByObj = window.editor.view.getField('PERIODOFFSETBY');

    colCompareBySelectorObj.updateProperty('hidden', false);
    colTrendingObj.updateProperty('hidden', false);
    setFldDefaultVal(colTrendingObj, '0');
    setFldDefaultVal(colPeriodOffsetByObj, 'D');
    colPeriodOffsetByObj.updateProperty('hidden', true);
    var selPeriod = kPeriodMap[periodsSelected[0]];

    var newLabels = [];
    var newValues = [];
    //Adding rolling period values into compare by option
    switch (selPeriod) {
        case GT("IA.TODAY"):
            setFldDefaultVal(colPeriodOffsetByObj, 'Y');
            colPeriodOffsetByObj.updateProperty('hidden', false);
            colCompareBySelectorObj.updateProperty('hidden', true);
            break;
        case GT('IA.TWELVE_MONTHS_TO_CURRENT_DATE'):
            newLabels[newLabels.length] = aRollingCompareByLab[0];
            newValues[newValues.length] = aRollingCompareByVal[0];
            setFldDefaultVal(colCompareBySelectorObj, 'T');
            colCompareBySelectorObj.updateProperty('hidden', true);
            break;
        case GT('IA.FISCAL_CURRENT_YEAR_TO_DATE'):
        case GT('IA.CURRENT_YEAR_TO_DATE'):
        case GT('IA.FISCAL_CURRENT_YEAR'):
        case GT('IA.CURRENT_YEAR'):
            for (var i = 0; i < yearRolOpts.length; i++) {
                newLabels[newLabels.length] = aRollingCompareByLab[yearRolOpts[i]];
                newValues[newValues.length] = aRollingCompareByVal[yearRolOpts[i]];
            }
            break;
        case GT('IA.TWELVE_MONTHS_TO_CURRENT_MONTH'):
            for (var i = 0; i < tMonthRolOpts.length; i++) {
                newLabels[newLabels.length] = aRollingCompareByLab[tMonthRolOpts[i]];
                newValues[newValues.length] = aRollingCompareByVal[tMonthRolOpts[i]];
            }
            break;
        case GT('IA.FISCAL_CURRENT_QUARTER_TO_DATE'):
        case GT('IA.CURRENT_QUARTER'):
        case GT('IA.CURRENT_QUARTER_TO_DATE'):
        case GT('IA.FISCAL_CURRENT_QUARTER'):
            for (var i = 0; i < quarterRolOpts.length; i++) {
                newLabels[newLabels.length] = aRollingCompareByLab[quarterRolOpts[i]];
                newValues[newValues.length] = aRollingCompareByVal[quarterRolOpts[i]];
            }
            break;
        case GT('IA.CURRENT_MONTH_TO_DATE'):
        case GT('IA.CURRENT_MONTH'):
            for (var i = 0; i < monthRolOpts.length; i++) {
                newLabels[newLabels.length] = aRollingCompareByLab[monthRolOpts[i]];
                newValues[newValues.length] = aRollingCompareByVal[monthRolOpts[i]];
            }
            break;
        case GT('IA.FISCAL_CURRENT_MONTH'):
            for (var i = 0; i < tMonthRolOpts.length; i++) {
                newLabels[newLabels.length] = aRollingCompareByLab[tMonthRolOpts[i]];
                newValues[newValues.length] = aRollingCompareByVal[tMonthRolOpts[i]];
            }
            break;
        case GT('IA.THIS_WEEK'):
            for (var i = 0; i < weekRolOpts.length; i++) {
                newLabels[newLabels.length] = aRollingCompareByLab[weekRolOpts[i]];
                newValues[newValues.length] = aRollingCompareByVal[weekRolOpts[i]];
            }
            break;
        default:
            for (var i = 0; i < 1; i++) {
                newLabels[newLabels.length] = aRollingCompareByLab[weekRolOpts[i]];
                newValues[newValues.length] = aRollingCompareByVal[weekRolOpts[i]];
            }
            colCompareBySelectorObj.updateProperty('hidden', true);
            colTrendingObj.updateProperty('hidden', true);
            break;
    }

    /* Setting Drop-down value as 'T' since the dropdown list does not contain selected value */
    if (newValues.indexOf(colCompareBySelectorObj.getValue()) == -1) {
        colCompareBySelectorObj.setValue("T");
    }
    colCompareBySelectorObj.updateProperty(['type', 'validvalues'], newValues);
    colCompareBySelectorObj.updateProperty(['type', 'validlabels'], newLabels);
    colCompareBySelectorObj.parentComponent.redraw();

    // remove None option
    newValues = newValues.slice(1);
    newLabels = newLabels.slice(1);
    setDDList('WF_EXPANDBY', newLabels, newValues);


    setWFSeconSeries(newValues, newLabels);

    if ((periodsSelected.length > 1 || isPeriodExpansionSelected === false)
    ) {
        colCompareBySelectorObj.setValue('T');
        colCompareBySelectorObj.updateProperty('hidden', true);
        colTrendingObj.setValue('');
        colTrendingObj.updateProperty('hidden', true);
        colPeriodOffsetByObj.setValue('D');
        colPeriodOffsetByObj.updateProperty('hidden', true);
        colCompareBySelectorObj.parentComponent.redraw();
    }

    showHideSortByTimelineField();
    return true;
}

function showHidePickerFromForm(filterFor, enableptr, enableptr2) {
    if (enableptr2 == undefined) {
        showHidePicker(filterFor, enableptr, 'specifichierarchy');
    } else {
        showHidePicker(filterFor, enableptr2, 'type');
        showHidePicker(filterFor, enableptr, 'specifichierarchy');
        showHideDisplayAndPrompt(enableptr, filterFor.value);
    }
}

function showHideDisplayAndPrompt(enableptr, filterType) {

    // Hide or show type options
    var dimpath = enableptr.substring(0, enableptr.length - 2);
    var display = window.view.findComponents('PROMPT' + dimpath, 'Field');
    var incsub = window.view.findComponents('EXCLSUBS_FOR_' + dimpath, 'Field');

    if (display !== null) {
        display = display[0];
        if (filterType === 'type') {
            display.updateProperty('hidden', true);
        } else {
            display.updateProperty('hidden', false);
        }
        display.parentComponent.redraw();
    }

    if (incsub !== null) {
        incsub = incsub[0];
        if (filterType === 'type') {
            incsub.updateProperty('hidden', true);
        } else {
            incsub.updateProperty('hidden', false);
        }
        incsub.parentComponent.redraw();
    }
    window.view.findComponents(enableptr)[0].parentComponent.redraw();
}

function showHidePicker(filterFor, enableptr, filterType) {
    var ptr = window.view.findComponents(enableptr);
    if (!ptr || ptr.length == 0) {
        return false;
    }
    ptr = ptr[0];

    if (this.view.readonly) {
        if (filterFor.value && filterFor.value.search('nofilter') < 0) {
            filterFor.showHide(true);
            if (filterFor.value.search('nullvalue') == 0) {
                ptr.showHide(false);
                ptr.setValue("");
            } else {
                ptr.showHide(true);
            }
            ptr.parentComponent.redraw();
            return true;
        } else {
            ptr.showHide(false);
            filterFor.showHide(false);
            ptr.setValue("");
            ptr.parentComponent.redraw();
            return false;
        }
    }

    if (filterFor.value && (filterFor.value.search(filterType) == 0)) {
        // UDD's are suppose to have platform PTR control
        if ( filterType == 'specifichierarchy' && ptr.iscustom && ptr.isDimension ) {
            ptr.renderer = null;
            ptr.uiControl = 'ControlPlatformPTR';
            ptr.type.format = null;
            ptr.pt_ListAllowed = true;
        }
        ptr.label = '';
        ptr.showHide(true);
        ptr.setFocus(); // so tabbing works after making a selection.
        ptr.parentComponent.redraw();

        //showHideDisplayAndPrompt(filterFor.value, filterType);
        return true;
    } else {
        ptr.showHide(false);
        ptr.setValue("");
        ptr.parentComponent.redraw();
        filterFor.focus();
        return false;
    }
}
