var INVENTORY = 'Inventory';
var NONINV = 'Non-Inventory';
var NONINV_PO = 'Non-Inventory (Purchase only)';
var NONINV_SO = 'Non-Inventory (Sales only)';
var KIT = 'Kit';
var STOCKABLE_KIT = 'Stockable Kit';
var LANDEDCOST_DEFAULT_VALUE = '1';

//Values taken from $gSalesForecastMethodValues and $gSalesForecastMethodIValues in globals.ent
var SINGLE_VALUE = 'Demand forecast by single value';
var FORECAST_TABLE = 'Demand forecast by fluctuating values';
var REORDER_POINT = 'Reorder point';
var SINGLE_VALUE_LABEL = GT('IA.DEMAND_FORECAST_BY_SINGLE_VALUE');
var FORECAST_TABLE_LABEL = GT ('IA.DEMAND_FORECAST_BY_FLUCTUATING_VALUES');
var REORDER_POINT_LABEL = GT('IA.REORDER_POINT');

//adding the labels and trying to maintain the same order as in server to avoid any confusion
var INV_REPLENISHMENT_METHOD_VALUES_F   = [SINGLE_VALUE, REORDER_POINT,FORECAST_TABLE];
var INV_REPLENISHMENT_METHOD_IVALUES_F  = ['FORECAST_DEMAND', 'REORDER_POINT', 'FORECAST_TABLE'];
var INV_REPLENISHMENT_METHOD_LABELS_F   = [SINGLE_VALUE_LABEL, REORDER_POINT_LABEL,FORECAST_TABLE_LABEL];

var SKIT_REPLENISHMENT_METHOD_VALUES_F  = [ FORECAST_TABLE, REORDER_POINT];
var SKIT_REPLENISHMENT_METHOD_IVALUES_F = ['FORECAST_TABLE', 'REORDER_POINT'];
var SKIT_REPLENISHMENT_METHOD_LABELS_F   = [FORECAST_TABLE_LABEL, REORDER_POINT_LABEL];

var INV_REPLENISHMENT_METHOD_VALUES     = [ SINGLE_VALUE, REORDER_POINT];
var INV_REPLENISHMENT_METHOD_IVALUES    = ['FORECAST_DEMAND', 'REORDER_POINT'];
var INV_REPLENISHMENT_METHOD_LABELS     = [SINGLE_VALUE_LABEL, REORDER_POINT_LABEL];

var SKIT_REPLENISHMENT_METHOD_VALUES    = [ REORDER_POINT ];
var SKIT_REPLENISHMENT_METHOD_IVALUES   = [ 'REORDER_POINT'];
var SKIT_REPLENISHMENT_METHOD_LABELS   = [ REORDER_POINT_LABEL ];

var compStandardCost = new Array();
var enableForContractFeatureEnabled = false;

function StatusField(meta)
{
    this.meta = meta;
}

StatusField.inheritsFrom( InputControlBoolean );

StatusField.prototype.convertValueToBoolean = function(data, noDefault)
{
    var	boolData =  (data =='inactive' ) ? true : false;
    var strData = boolData ? 'true' : 'false';
    if( strData != data )
    {
        this.setValue(strData);
    }

    return boolData;
};

StatusField.prototype.extractValueFromUI = function(element)
{
    if( this.meta.readonly ) return InputControl.prototype.extractValueFromUI.call(this, element);
    return element.checked ? 'inactive' : 'active';
};


function BinTrackSection()
{
}

BinTrackSection.inheritsFrom( Section );

BinTrackSection.prototype.draw = function()
{
    var enableBin = window.editor.view.findComponents('ENABLE_BINS', 'Field');
    if(enableBin && enableBin[0]){
        if(enableBin[0].getValue() == 'true'){
            this.hidden = false;
            var grid = this.getGrid();
            var rowNo = this.getLineNo();
            var warehouseid = grid.value[rowNo].WAREHOUSEID;
            // if(warehouseid && warehouseid != ''){
                setBinTrackOptions(warehouseid, rowNo, true);   // true == first time
            // }
        }else{
            this.hidden = true;
        }
    }
    return Section.prototype.draw.call(this);
};


function LcostField()
{
}

LcostField.inheritsFrom(Field);

LcostField.prototype.draw = function ()
{
    if (this.path == 'VALUE') {
        if (this.parentValue.ACTIVE == 'true') {
            this.disabled = false;
        } else {
            this.disabled = true;
        }
    }

    return Field.prototype.draw.call(this);
};

function SummaryField()
{
}

SummaryField.inheritsFrom(Field);

SummaryField.prototype.draw = function()
{
    var elements = Field.prototype.draw.call(this);
    if((action == 'new' || action == 'create' || action == 'copy') && this.path=='ITEMTYPE') {
        return elements;
    }
    if ( elements && elements.length  == 2 ) {
        elements.splice(1, 0, document.createElement('br'));
    }
    return elements;
};

function VenderGrid()
{
}

VenderGrid.inheritsFrom( Grid );

VenderGrid.prototype.canDeleteRow = function(rowIndex, isTotal)
{
    return true;
};

//Base class will only hide a column if atleast one row is in the grid. So we override to hide the column title even if there are no rows.
VenderGrid.prototype.hideColumn = function(columnOrIndex)
{
    var retVal = false;
    if (action == 'view' && this.getRowCount() == 0) {
        retVal = this.showHideColumnTitle(columnOrIndex, true);
    } else {
        //Calls base class logic
        retVal = Grid.prototype.hideColumn.call(this, columnOrIndex);
    }

    return retVal;
};

//Base class will only show a column if atleast one row is in the grid. So we override to show the column title even if there are no rows.
VenderGrid.prototype.showColumn = function(columnOrIndex, toIndex)
{
    var retVal = false;
    if (action == 'view' && this.getRowCount() == 0) {
        retVal = this.showHideColumnTitle(columnOrIndex, false);
    } else {
        //Calls base class logic
        retVal = Grid.prototype.showColumn.call(this, columnOrIndex, toIndex);
    }

    return retVal;
};

VenderGrid.prototype.showHideColumnTitle = function(column, hide)
{
    var retVal = false;
    for (var i = 0; i < this.children.length; i++) {
        if (this.children[i].field[0] && this.children[i].field[0].path && this.children[i].field[0].path == column) {
            this.children[i].setProperty('hidden', hide);
            retVal = true;
            break;
        }
    }
    return retVal;
};

function ItemOnLoad()
{
    // If CONTRACTENABLED is visible on page load then that means the feature is enabled
    var contractEnabledField = window.editor.view.findComponents('CONTRACTENABLED', 'Field');
    if ( contractEnabledField && contractEnabledField[0] ) {
        contractEnabledField = contractEnabledField[0];
        enableForContractFeatureEnabled = contractEnabledField.isVisible();
    }

    VendorHistoryGrid_disableEnablePreferredVendorCheckboxes();
    VendorHistoryGrid_toggleFirstPreferredVendorCheckboxes(0);

    //We need to set fields for replenishment for both view and edit
    setFieldsForReplenishment();

    //show & hide grid grid columns for tracking & inquiry tab
    showHideFulfillmentColumnsTrackingInquiry();

    if (window.view.readonly) {
        return true;
    }

    var iType = window.editor.view.getFieldValue('ITEMTYPE');

    setFieldForLandedCost(iType);

    SetCostMethodValue(iType);
    SetRevenuePostingValue(iType);

    toggleKitControls();
    populateUOMPicklists(window.view.value.UOMGRP, true);

    showHideMultiTaxGroups();

    hideShowItemIsSuppliesInv();
}

function ItemOnChange(obj)
{
    window.setTimeout(function() {
        window.editor.gatherData();
        var iType = window.editor.view.getFieldValue('ITEMTYPE');

        if(iType == STOCKABLE_KIT && defaultCost != 'FIFO'){
            alert(GT("IA.SK_COST_METHOD_SHOULD_BE_FIFO"));
        }

        if (iType == KIT) {
            var isSupplies = window.editor.view.getFieldValue('ISSUPPLYITEM');
            if (isSupplies == 'true') {
                alert(GT("IA.SUPPLIES_CANNOT_BE_A_KIT"));
                window.editor.view.setFieldValue('ITEMTYPE', INVENTORY);
                return;
            }
        }

        ShowHideItemFields(iType);
        showHideMultiTaxGroups();
        SetCostMethodValue(iType);
        SetRevenuePostingValue(iType);

        setFieldForLandedCost(iType);

        toggleKitControls();
        setFieldsForReplenishment();
        hideShowItemIsSuppliesInv();
        SetDefaultConversionTypeValue(iType);
    }, 0);
}


function SetDefaultConversionTypeValue(iType) {
    if (action == 'new' || action == 'create' || action == 'copy') {
        var converType = window.editor.view.findComponents('DEFAULT_CONVERSIONTYPE', 'Field');
        if (iType == STOCKABLE_KIT || iType == KIT || iType == INVENTORY) {
            converType[0].setValue('Quantity');
            converType[0].readonly = true;
        } else {
            converType[0].readonly = false;
        }
        converType[0].redraw();
    }
}

function UOMGRPField()
{
}

UOMGRPField.inheritsFrom(Field);

UOMGRPField.prototype.handleChange = function(event, element)
{
    this.oldValue = this.value;
    return Field.prototype.handleChange.call(this, event, element);
};

function OnChangeCostMethod(){
    window.setTimeout(function() {
    window.editor.gatherData();
    SetTrackingControls();
    toggleTrackingControls();
    }, 0);
}

function OnChangeBinControls(){
    window.editor.gatherData();
    toggleTrackingControls();
}

function OnChangeContractEnabled(){
    window.editor.gatherData();
    toggleKitControls();
}

function OnChangeRevPosting(){
    window.editor.gatherData();
    toggleKitControls();
}

function OnchangeUOM() {
    var msg = GT("IA.DIFFERENT_UOM_THAN_CURRENT_UOM_GROUP");
    if (confirm(msg)) {
        populateUOMPicklists(this.value, false);
        return true;
    }
    var ug = window.editor.view.findComponents('UOMGRP', 'Field');
    ug[0].setValue(ug[0].oldValue);

    return false;
}

function ShowHideItemFields(iType){

    var iFields = new Array();

    if(iType == INVENTORY){
        iFields = initInventoryFields(iFields, true);
        iFields = initTrackingFields(iFields, true, iType);
        iFields = initSalesFields(iFields, true);
        iFields = initPurchaseFields(iFields, true, iType);
        iFields.push({
            'type':'Field',
            'path':'EXPENSEACCTKEY',
            'show':false
        });
        iFields.push({
            'type':'Field',
            'path':'STANDARD_COST',
            'show':!ismcmesubscribed
        });
        iFields.push({
            'type':'Field',
            'path':'UPC',
            'show':true
        });

        iFields.push({
            'type':'Tab',
            'path':'term',
            'show':itemhasTerm
        });
        iFields.push({
            'type':'Tab',
            'path':'vendor',
            'show':true
        });
        iFields.push({
            'type':'Section',
            'path':'kitComponent',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'warehouse',
            'show':true
        });
        iFields.push({
            'type':'Section',
            'path':'VendorHistorySectionId',
            'show':true
        });
    }else if(iType == NONINV){
        iFields = initInventoryFields(iFields, false);
        iFields = initTrackingFields(iFields, false, iType);
        iFields = initSalesFields(iFields, true);
        iFields = initPurchaseFields(iFields, true, iType);

        iFields.push({
            'type':'Field',
            'path':'EXPENSEACCTKEY',
            'show':true
        });
        iFields.push({
            'type':'Field',
            'path':'STANDARD_COST',
            'show':!ismcmesubscribed
        });
        iFields.push({
            'type':'Field',
            'path':'UPC',
            'show':true
        });

        iFields.push({
            'type':'Tab',
            'path':'term',
            'show':itemhasTerm
        });
        iFields.push({
            'type':'Tab',
            'path':'vendor',
            'show':true
        });
        iFields.push({
            'type':'Section',
            'path':'kitComponent',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'warehouse',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'VendorHistorySectionId',
            'show':true
        });
    }else if(iType == NONINV_PO){
        iFields = initInventoryFields(iFields, false);
        iFields = initTrackingFields(iFields, false, iType);
        iFields = initSalesFields(iFields, false);
        iFields = initPurchaseFields(iFields, true, iType);

        iFields.push({
            'type':'Field',
            'path':'EXPENSEACCTKEY',
            'show':true
        });
        iFields.push({
            'type':'Field',
            'path':'STANDARD_COST',
            'show':!ismcmesubscribed
        });
        iFields.push({
            'type':'Field',
            'path':'UPC',
            'show':true
        });


        iFields.push({
            'type':'Tab',
            'path':'term',
            'show':false
        });
        iFields.push({
            'type':'Tab',
            'path':'vendor',
            'show':true
        });
        iFields.push({
            'type':'Section',
            'path':'kitComponent',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'warehouse',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'VendorHistorySectionId',
            'show':true
        });
    }else if(iType == NONINV_SO){
        iFields = initInventoryFields(iFields, false);
        iFields = initTrackingFields(iFields, false, iType);
        iFields = initSalesFields(iFields, true);
        iFields = initPurchaseFields(iFields, false, iType);

        iFields.push({
            'type':'Field',
            'path':'EXPENSEACCTKEY',
            'show':false
        });
        iFields.push({
            'type':'Field',
            'path':'STANDARD_COST',
            'show':!ismcmesubscribed
        });
        iFields.push({
            'type':'Field',
            'path':'UPC',
            'show':true
        });

        iFields.push({
            'type':'Tab',
            'path':'term',
            'show':itemhasTerm
        });
        iFields.push({
            'type':'Tab',
            'path':'vendor',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'kitComponent',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'warehouse',
            'show':false
        });
    }else if(iType == KIT){
        iFields = initTrackingFields(iFields, false, iType);
        iFields = initSalesFields(iFields, true);
        iFields = initPurchaseFields(iFields, true, iType);

        iFields.push({
            'type':'Field',
            'path':'EXPENSEACCTKEY',
            'show':false
        });
        iFields.push({
            'type':'Field',
            'path':'STANDARD_COST',
            'show':false
        });

  	    if(isadvsetup){
		    iFields.push({
				'type':'Field',
				'path':'INCOMEACCTKEY',
				'show':false
		    });
			iFields.push({
				'type':'Field',
				'path':'INVACCTKEY',
				'show':false
			});
		}

        iFields.push({
            'type':'Field',
            'path':'COGSACCTKEY',
            'show':false
        });
        iFields.push({
            'type':'Field',
            'path':'UPC',
            'show':false
        });

        iFields.push({
            'type':'Tab',
            'path':'term',
            'show':itemhasTerm
        });
        iFields.push({
            'type':'Tab',
            'path':'vendor',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'kitComponent',
            'show':true
        });
        iFields.push({
            'type':'Section',
            'path':'warehouse',
            'show':false
        });

    }else if(iType == STOCKABLE_KIT){
        iFields = initTrackingFields(iFields, true, iType);
        iFields = initSalesFields(iFields, true);
        iFields = initPurchaseFields(iFields, true, iType);

        iFields.push({
            'type':'Field',
            'path':'EXPENSEACCTKEY',
            'show':false
        });
        iFields.push({
            'type':'Field',
            'path':'STANDARD_COST',
            'show':false
        });
        if(isadvsetup){
			iFields.push({
				'type':'Field',
				'path':'INCOMEACCTKEY',
				'show':false
			});
			iFields.push({
				'type':'Field',
				'path':'INVACCTKEY',
				'show':false
			});
            iFields.push({
                 'type':'Field',
                 'path':'COGSACCTKEY',
                 'show':false
             });
		}

        iFields.push({
            'type':'Field',
            'path':'UPC',
            'show':false
        });

        iFields.push({
            'type':'Tab',
            'path':'term',
            'show':itemhasTerm
        });
        iFields.push({
            'type':'Tab',
            'path':'vendor',
            'show':false
        });
        iFields.push({
            'type':'Section',
            'path':'kitComponent',
            'show':true
        });
        iFields.push({
            'type':'Section',
            'path':'warehouse',
            'show':true
        });
    }

    var suppliesIsOn      = (window.editor.view.getFieldValue('ISSUPPLYITEM') == 'true');

    if(isadvsetup){
        // supplies types cannot be sold and are part of PO (only)
        var invShow = (invInstalled == false || iType == KIT || iType == NONINV_SO || iType == NONINV_PO || iType == NONINV) ? false : true;
        var soShow = (soInstalled == false || iType == KIT || iType == NONINV_PO || suppliesIsOn) ? false : true;
        var poShow = (poInstalled == false || iType == KIT || iType == NONINV_SO ) ? false : true;

        iFields.push({
            'type':'Field',
            'path':'INV_PRECISION',
            'show':invShow
        });
        iFields.push({
            'type':'Field',
            'path':'SO_PRECISION',
            'show':soShow
        });
        iFields.push({
            'type':'Field',
            'path':'PO_PRECISION',
            'show':poShow
        });

        if(ismulticost == true){
            var coShow = (iType != STOCKABLE_KIT && iType != INVENTORY) ? false : true;
            iFields.push({
                'type':'Field',
                'path':'COST_METHOD',
                'show':coShow
            });
        }
    }

    if (enabledropship == true) {
        var dropshipShow = (iType != INVENTORY && iType != NONINV && iType != STOCKABLE_KIT) ? false : true;
        iFields.push({
            'type': 'Field',
            'path': 'DROPSHIP',
            'show': dropshipShow    // supplies can't drop ship but they CAN 'direct deliver' which is similar
        });
    }

    if (enableBTO == true) {
        var btoShow = (iType != INVENTORY && iType != NONINV && iType != STOCKABLE_KIT) ? false : true;
        iFields.push({
            'type': 'Field',
            'path': 'BUYTOORDER',
            'show': btoShow  && (suppliesIsOn == false) // no orders to buy TO if supplies
        });
    }

    if (enablelandedcost == true) {
        var landedCostShow =
            (iType != NONINV && iType != NONINV_PO && iType != INVENTORY && iType != STOCKABLE_KIT) ? false : true;
        iFields.push({
            'type': 'Section',
            'path': 'landedcost',
            'show': landedCostShow  && (suppliesIsOn == false) // can't do landed costs if supplies are on
        });
    }

    if(showAutoPrintLabelfields){
        var typeArray = new Array(INVENTORY, STOCKABLE_KIT);
        var autoPrintLabelShow = ( typeArray.indexOf(iType) < 0 ) ? false : true;
        iFields.push({
            'type': 'Field',
            'path': 'AUTOPRINTLABEL',
            'show': autoPrintLabelShow
        });
    }

    renderFields(iFields);

    if(isadvsetup == true){
        var sections = window.editor.view.findComponentsById('tracking', 'Section');
        var section = sections[0];

        if(section.visibleChildCount > 0){
            section.showHide(true);
        }else{
            section.showHide(false);
        }
    }

    var fulfillmentSections = window.editor.view.findComponentsById('Enablefulfillment', 'Section');
    var fulfillmentSection = fulfillmentSections[0];

    if ( isFulfillmentPrefEnabled && (iType == NONINV || iType == NONINV_SO)) {
        fulfillmentSection.showHide(true);
    } else {
        //uncheck if checked
        var fulfillmentchecked = window.editor.view.findComponents('ENABLEFULFILLMENT', 'Field')[0];
        fulfillmentchecked.setValue('false');
        fulfillmentchecked.redraw();
        fulfillmentSection.showHide(false);
    }

    return true;
}

/**
 * Method to Show and Hide Fulfillment Columns in grid on
 * Tracking and Inquiry tab. This method will show
 * columns if fulfillment is enabled.
 */
function showHideFulfillmentColumnsTrackingInquiry(){
    if ( gridColumnsWithFulfillmentReq !== null && gridColumnsWithFulfillmentReq.length !== 0 ) {
        for ( var gridPath in gridColumnsWithFulfillmentReq ){
            var grid = window.editor.view.findComponents(gridPath, 'Grid');
            if ( grid && grid[0] ) {
                for ( var i = 0; i < gridColumnsWithFulfillmentReq[gridPath].length; i ++ ) {
                    (isFulfillmentPrefEnabled) ? grid[0].showColumn(gridColumnsWithFulfillmentReq[gridPath][i]) : grid[0].hideColumn(gridColumnsWithFulfillmentReq[gridPath][i]);
                }
                grid[0].redraw();
            }
        }
    }
}

function  EnableWarehouseGrid()
{
    var fulfillmentchecked = window.editor.view.getFieldValue('ENABLEFULFILLMENT');
    var warehouseSection = window.editor.view.findComponentsById('warehouse', 'Section')[0];
    if ( fulfillmentchecked == 'true' ) {
        warehouseSection.showHide(true);

        var fields = ['CYCLE', 'ECONOMIC_ORDER_QTY', 'MIN_ORDER_QTY', 'MAX_ORDER_QTY', 'MIN_STOCK',
                      'MAX_STOCK'];

        var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
        if ( whGrid && whGrid[0] && whGrid[0].lineDetails ) {
            for ( var i = 0; i < fields.length; i ++ ) {
                var field = whGrid[0].lineDetails.findComponents(fields[i], 'Field')[0];
                if ( field ) {
                    field.updateProperty('disabled', true, true);
                }
            }
            var reorderfield = whGrid[0].lineDetails.findComponents('REORDER_POINT', 'Field');
            for ( var j = 0; j < reorderfield.length; j ++ ){
                if ( reorderfield && reorderfield[j] ) {
                    reorderfield[j].updateProperty('disabled', true, true);
                }
            }
        }
    } else {
        warehouseSection.redraw();
        warehouseSection.showHide(false);
    }
}

function renderFields(iFields)
{
    for(var i = 0; i < iFields.length; i++){
        if(iFields[i].type == 'Field'){
            var f = window.editor.view.findComponents(iFields[i].path, iFields[i].type);
            if(f && f[0]){
                f[0].showHide(iFields[i].show);
            }
        }else if(iFields[i].type == 'Grid'){
            var grid = window.editor.view.findComponents(iFields[i].path, iFields[i].type);
            if(grid){
                if(iFields[i].show == true){
                    grid[0].show();
                }else{
                    grid[0].hide();
                }
            }
        }else if (iFields[i].type == 'Tab'){
            var tabs = window.editor.view.findComponents(null, iFields[i].type);
            var tab = null;
            var isTabVisible;
            var tabIndex = null;
            for ( var ix = 0; ix < tabs.length; ix++ ) {
                if ( tabs[ix].id == iFields[i].path ) {
                    tabIndex = ix;
                    break;
                }
            }

            if(tabIndex){
                tab = window.editor.view.findComponentsById(iFields[i].path, iFields[i].type);
                isTabVisible = tab && tab[0] && tab[0].isVisible();

                if(iFields[i].show && !isTabVisible){
                    window.editor.view.showTab(tab);
                }else if (!iFields[i].show && isTabVisible){
                    window.editor.view.hideTab(tab);
                }
            }

        }else if(iFields[i].type == 'Section'){
            var sections = window.editor.view.findComponentsById(iFields[i].path, iFields[i].type);
            if( sections && sections[0] ){
                var section = sections[0];
                section.showHide(iFields[i].show);
            }
        }
    }
}

function SetCostMethodValue(iType){
    if(action == 'new' || action == 'create' || action == 'copy'){
        if( isadvsetup == true && ismulticost == true){
            var costM = window.editor.view.findComponents('COST_METHOD', 'Field');
            if(iType == KIT){
                costM[0].setValue('Standard');
            }
            if(iType == STOCKABLE_KIT){
                costM[0].setValue('FIFO');
                costM[0].readonly = true;
            }else{
                costM[0].readonly = false;
            }
            costM[0].redraw();
        }
    }

    SetTrackingControls();
    toggleTrackingControls();
}

function SetRevenuePostingValue(iType){
    if(iType == KIT || iType == STOCKABLE_KIT){
        var po = window.editor.view.findComponents('REVPOSTING', 'Field');
        var pr = window.editor.view.findComponents('REVPRINTING', 'Field');

        if(po && po[0]){
            if(iType == STOCKABLE_KIT || isadvsetup == false){
                po[0].readonly = true;
                pr[0].readonly = true;
                po[0].setValue('Kit Level');
                pr[0].setValue('Kit');
            }else{
                po[0].readonly = false;
                pr[0].readonly = false;
            }
            po[0].redraw();
            pr[0].redraw();
        }
    }
}

function initInventoryFields(allFields, show){
    var invFields = new Array('SHIP_WEIGHT', 'INVACCTKEY', 'NETWEIGHT', 'WEIGHTUOM');

    for(k = 0; k < invFields.length; k++){
        allFields.push({
            'type':'Field',
            'path':invFields[k],
            'show':show
        });
    }
    return allFields;
}

function initTrackingFields(allFields, show, iType){
    var trackingFields = new Array(	'ENABLE_SERIALNO', 'SERIAL_MASKKEY','ENABLE_LOT_CATEGORY','LOT_CATEGORYKEY','ENABLE_BINS','ENABLE_EXPIRATION');
    for(k = 0; k < trackingFields.length; k++){
        var tShow = show;
        if (iType == INVENTORY || (ENABLESTOCKABLEKITTRACKING == 'T' && iType == STOCKABLE_KIT)) {
            if(enableserial == false &&  (trackingFields[k] == 'ENABLE_SERIALNO' || trackingFields[k] == 'SERIAL_MASKKEY')){
                tShow = false;
            }
            if(enablelot == false &&  (trackingFields[k] == 'ENABLE_LOT_CATEGORY' || trackingFields[k] == 'LOT_CATEGORYKEY')){
                tShow = false;
            }
            if(enablebin == false &&  trackingFields[k] == 'ENABLE_BINS'){
                tShow = false;
            }
            if(enableexpir == false &&  trackingFields[k] == 'ENABLE_EXPIRATION'){
                tShow = false;
            }
        } else if (ENABLESTOCKABLEKITTRACKING != 'T' && iType == STOCKABLE_KIT){
            if(trackingFields[k] == 'ENABLE_BINS'){
                if(enablebin == false){
                    tShow = false;
                }

            }else{
                tShow = false;
            }
        }
        allFields.push({
            'type':'Field',
            'path':trackingFields[k],
            'show':tShow
        });
    }
    return allFields;
}


function initSalesFields(allFields, show){
    var salesFields = new Array(	'SALES_UNIT', 'BASEPRICE', 'INCOMEACCTKEY','INVACCTKEY', 'COGSACCTKEY');

    if(isadvsetup == false){
        salesFields[salesFields.length] = 'OFFSETOEGLACCOUNTKEY';
        if(enablerevrec == true){
            salesFields[salesFields.length] = 'DEFERREDREVACCTKEY';
            salesFields[salesFields.length] = 'DEFAULTREVRECTEMPLKEY';
        }
    }
    if(action != 'new' && action != 'create' && action != 'copy'){
        salesFields[salesFields.length] = 'WHENLASTSOLD';
    }

    for(k = 0; k < salesFields.length; k++){
        allFields.push({
            'type':'Field',
            'path':salesFields[k],
            'show':show
        });
    }
    return allFields;
}

function initPurchaseFields(allFields, show, iType){
    var pFields = new Array( 'PURCHASE_UNIT', 'EXPENSEACCTKEY', 'INVACCTKEY', 'COGSACCTKEY');
    if(isadvsetup == false){

        pFields.push('OFFSETPOGLACCOUNTKEY');
    }
    for(k = 0; k < pFields.length; k++){
        var poShow = show;
        if(pFields[k] == 'OFFSETPOGLACCOUNTKEY' && iType == KIT){
            poShow = false;
        }
        allFields.push({
            'type':'Field',
            'path':pFields[k],
            'show':poShow
        });
    }
    return allFields;
}

function SetTrackingControls(){

    var costM = window.editor.view.findComponents('COST_METHOD', 'Field');
    var itemType = window.editor.view.findComponents('ITEMTYPE', 'Field');

    if(costM && costM[0]){
        var costmethod = costM[0].getValue();
        var disableTrackingCtrls = costmethod == 'LIFO' ? true : false;
        var elcat = window.editor.view.findComponents('ENABLE_LOT_CATEGORY', 'Field');
        if(elcat && elcat[0]){
            if(disableTrackingCtrls){
                elcat[0].setValue(false);
            }
            elcat[0].updateProperty('disabled',disableTrackingCtrls, true);
        }

        var lcat = window.editor.view.findComponents('LOT_CATEGORYKEY', 'Field');
        if(lcat && lcat[0]){
            if(disableTrackingCtrls){
                lcat[0].setValue('');
            }
            lcat[0].updateProperty('disabled',disableTrackingCtrls, true);
        }

        var enableSN = window.editor.view.findComponents('ENABLE_SERIALNO', 'Field');
        if(enableSN && enableSN[0]){
            if(disableTrackingCtrls){
                enableSN[0].setValue(false);
            }
            enableSN[0].updateProperty('disabled',disableTrackingCtrls, true);
        }

        var sMask = window.editor.view.findComponents('SERIAL_MASKKEY', 'Field');
        if(sMask && sMask[0]){
            if(disableTrackingCtrls){
                sMask[0].setValue('');
            }
            sMask[0].updateProperty('disabled',disableTrackingCtrls, true);
        }

        var enableBin = window.editor.view.findComponents('ENABLE_BINS', 'Field');
        if(enableBin && enableBin[0] &&
           (ENABLESTOCKABLEKITTRACKING == 'T' || !(itemType[0].getValue()==STOCKABLE_KIT && costmethod=='FIFO'))){
            if(disableTrackingCtrls){
                enableBin[0].setValue(false);
            }
            enableBin[0].updateProperty('disabled',disableTrackingCtrls, true);
        }

        var enableExp = window.editor.view.findComponents('ENABLE_EXPIRATION', 'Field');
        if(enableExp && enableExp[0]){
            if(disableTrackingCtrls){
                enableExp[0].setValue(false);
            }
            enableExp[0].updateProperty('disabled',disableTrackingCtrls, true);
        }
    }
}

function toggleTrackingControls(){

    var enableSN = window.editor.view.findComponents('ENABLE_SERIALNO', 'Field');
    var enableLot = window.editor.view.findComponents('ENABLE_LOT_CATEGORY', 'Field');
    var lotC = window.editor.view.findComponents('LOT_CATEGORYKEY', 'Field');
    var sMask = window.editor.view.findComponents('SERIAL_MASKKEY', 'Field');
    var costM = window.editor.view.getFieldValue('COST_METHOD');
    var disableTracking = false;
    if (costM=='LIFO') {
        disableTracking = true;
    }

    var lotEnabled = false;
    var snEnabled = false;
    var expEnabled = false;
    var binEnabled = false;

    if(enableSN && enableSN[0] && enableSN[0].getValue() == 'true'){
        snEnabled = true;
    }else if(snEnabled == false){
        if(enableLot && enableLot[0]){
            enableLot[0].updateProperty('disabled',disableTracking, true);
        }
        if(lotC && lotC[0]){
            lotC[0].updateProperty('disabled',disableTracking, true);
        }
    }

    if(enableLot && enableLot[0] && enableLot[0].getValue() == 'true'){
        lotEnabled = true;
    }else if(lotEnabled == false){
        if(enableSN && enableSN[0]){
            enableSN[0].updateProperty('disabled',disableTracking, true);
        }
        if(sMask && sMask[0]){
            sMask[0].updateProperty('disabled',disableTracking, true);
        }
    }


    var enableExp = window.editor.view.findComponents('ENABLE_EXPIRATION', 'Field');
    if(enableExp && enableExp[0]){
        if(enableExp[0].getValue() == 'true'){
            expEnabled = true;
        }

        if(snEnabled == true || lotEnabled == true){
            enableExp[0].updateProperty('disabled',false, true);
        }else{
            enableExp[0].setValue(false);
            enableExp[0].updateProperty('disabled',true, true);
        }
    }

    var enableBin = window.editor.view.findComponents('ENABLE_BINS', 'Field');
    if(enableBin && enableBin[0]){
        if(enableBin[0].getValue() == 'true'){
            binEnabled = true;
        }
    }

    /*
     * This code needs to be executed for all the actions but not just for creation
     * as the Existing items that are being edited need to have the cost fields with
     * the respective values that are valid for the ITEM_TYPE
     * or bin tracking field to be enabled or disabled respectively
     */
    costM = window.editor.view.findComponents('COST_METHOD', 'Field');
    var iType = window.editor.view.getFieldValue('ITEMTYPE');
    if(costM && costM[0]){
        var validLabels = new Array(GT("IA.COST_METHOD_STANDARD"),GT("IA.COST_METHOD_AVERAGE"),GT("IA.FIFO"),GT("IA.LIFO"));
        var validValues = new Array('Standard','Average','FIFO','LIFO');
        var selected    = costM[0].getValue();
        if(ismulticost != true) {
            selected = defaultCost;
        }
        if ( iType == STOCKABLE_KIT ) {
            validLabels= new Array(GT("IA.FIFO"));
            validValues = new Array('FIFO');
            if (selected!='FIFO') {
                selected = 'FIFO';
            }
            if (ENABLESTOCKABLEKITTRACKING != 'T' && enableBin && enableBin[0]) {
                enableBin[0].updateProperty('disabled',false, true);
            }
        } else if ( iType != INVENTORY ) {
            /*
             * For Non-Inventory, Non-Inventory(Sales Only), Non-Inventory(Pruchase Only) and Kit
             */
            validLabels=new Array(GT("IA.COST_METHOD_STANDARD"));
            validValues = new Array('Standard');
            if (selected!='Standard') {
                selected = 'Standard';
            }
        } else if (iType == INVENTORY && (lotEnabled || snEnabled || expEnabled || binEnabled)) {
            validLabels=new Array(GT("IA.COST_METHOD_STANDARD"),GT("IA.COST_METHOD_AVERAGE"),GT("IA.FIFO"));
            validValues = new Array('Standard','Average', 'FIFO');
            if (validValues.indexOf(selected) < 0) {
                selected = validValues[0];
            }
        }

        if(action == 'new' || action == 'create' || action == 'copy'){
            costM[0].updateProperty(['type','validlabels'], validLabels, true);
            costM[0].updateProperty(['type','validvalues'], validValues, true);
            costM[0].setValue(selected);
            costM[0].redraw();
        }
    }

    return true;
}


// when customer toggles 'Is supplies item' field....
// Note that they are allowed to toggle it because this is a NEW item and supplies is enabled in config.....
function handleItemIsSuppliesInv()
{
    window.setTimeout(function() {
        window.editor.gatherData();

        var isSupplies = window.editor.view.getFieldValue('ISSUPPLYITEM');
        var iType      = window.editor.view.getFieldValue('ITEMTYPE');
        if (iType     == KIT) {
            if (isSupplies == 'true') {
                alert(GT("IA.SUPPLIES_CANNOT_BE_A_KIT"));
                window.editor.view.setFieldValue('ISSUPPLYITEM', 'false');
                return;
            }
        }

        ShowHideItemFields(iType);
        hideShowItemIsSuppliesInv();
    }, 0);
}


function hideShowItemIsSuppliesInv()
{
    var itemIsSuppliesInv = window.editor.view.findComponents('ISSUPPLYITEM');
    var itemType = window.editor.view.getFieldValue('ITEMTYPE');
    var editable = (action == 'new' || action == 'copy' || (action == 'edit' && !supplyiteminuse));
    var enable   = (invInstalled && suppliesInventoryConfigEnabled && (itemType !== KIT && itemType !== NONINV_SO));
    if (itemIsSuppliesInv) {
        itemIsSuppliesInv[0].showHide(enable);
        if (enable) {
            itemIsSuppliesInv[0].updateProperty('disabled', !editable, true);
        } else {
            window.editor.view.setFieldValue('ISSUPPLYITEM', 'false');
        }
    }

    // set/protect stuff
    isSupplies = window.editor.view.getFieldValue('ISSUPPLYITEM') === 'true';
    if (isSupplies) {
        // if sneaky QA try to set things they shouldn't, and THEN change to supply...
        var setThese =  {
            'ENABLEFULFILLMENT' : 'false',
            'BUYTOORDER'        : 'false',
            'INV_PRECISION'     : 10,
            'SO_PRECISION'      : 10,
            'PO_PRECISION'      : 10,
            'ENABLELANDEDCOST'  : 'false',
            'UPC'               : '' };
        for (var i in setThese) {
            window.editor.view.setFieldValue(i, setThese[i]);
        }
    }
    var protectThese =  ['ENABLEFULFILLMENT', 'BUYTOORDER', 'SO_PRECISION', 'ENABLELANDEDCOST'];
    for (var j = 0; j < protectThese.length; j++) {
        var field = window.editor.view.findComponents(protectThese[j]);
        if (field && field[0]) {
            field[0].updateProperty('readonly', isSupplies, true);
        } else {
            console.log ("FAILED");
        }
    }
}

function toggleKitControls(){

    var iType = window.editor.view.getFieldValue('ITEMTYPE');
    if(iType != KIT  && iType != STOCKABLE_KIT) return true;

    var contractEnabled = false;
    var contractEnabledField = window.editor.view.findComponents('CONTRACTENABLED', 'Field');

    // Don't do any hiding/showing if the feature is disabled or we can't find the enable for contract checkbox
    if ( enableForContractFeatureEnabled && contractEnabledField && contractEnabledField[0] ) {
        contractEnabledField = contractEnabledField[0];
        contractEnabled = (contractEnabledField.getValue() == 'true');

        // Only show enable for contracts checkbox for kits
        contractEnabledField.showHide(iType == KIT);

        var revPostField = window.editor.view.findComponents('REVPOSTING', 'Field');
        if ( revPostField && revPostField[0] ) {
            revPostField = revPostField[0];

            if ( contractEnabled ) {
                // If enable for contract is checked, set revenue posting field to Component level and disable it
                revPostField.setValue('Component Level');
                revPostField.updateProperty('disabled', true, true);
            } else {
                // If enable for contracts is unchecked, enable revenue posting field
                revPostField.updateProperty('disabled', false, true);
            }
        }
    }

    var revPost = window.editor.view.getFieldValue('REVPOSTING');

    if(usefulfillment == true){
        var show = (revPost == 'Component Level') ? false : true;
        var sections = window.editor.view.findComponentsById('vsoe', 'Section');
        var section = sections[0];
        if(section){
            section.showHide(show);
        }
    }

    var compGrid = window.editor.view.findComponents('COMPONENT_INFO', 'Grid');
    if(compGrid && compGrid.length > 0)
    {
        compGrid = compGrid[0];
        var revP = compGrid.findComponents('REVPERCENT', 'Field');
        if ( revP && revP.length > 0 ) {
            if(revPost == 'Kit Level'){
                for ( var ix = 0; ix < revP.length; ix++ ) {
                    revP[ix].updateProperty('disabled', true, true);
                    revP[ix].setValue('');
                }
            }else{
                for ( var ix = 0; ix < revP.length; ix++ ) {
                    revP[ix].updateProperty('disabled', false, true);
                }
            }
            compGrid.redraw();
        }
    }

    var revPrint = window.editor.view.findComponents('REVPRINTING', 'Field');
    if ( revPrint && revPrint[0] ) {
        revPrint = revPrint[0];
        if ( revPost == 'Kit Level' || contractEnabled ) {
            // If revenue posting is set to Kit Level or contracts is enabled, set print level to kit and disable it
            revPrint.setValue('Kit');
            revPrint.updateProperty('disabled', true, true);
        } else {
            // If revenue posting is set to Component Level and contracts is disabled, enable print level
            revPrint.updateProperty('disabled', false, true);
        }
    }
}

function ShowHideLandedCost(obj)
{
    var landedcostgrid = window.editor.view.findComponents('LANDEDCOSTINFO', 'Grid');
    var iType = window.editor.view.getFieldValue('ITEMTYPE');

    if (obj.checked && iType != NONINV && iType != NONINV_PO) {
        landedcostgrid[0].updateProperty('hidden', false, false);
    } else {
        landedcostgrid[0].updateProperty('hidden', true, false);
    }

    if (typeof PAGE_LAYOUT_TYPE !== 'undefined' && PAGE_LAYOUT_TYPE === 'Q' && !landedcostgrid[0].table) {
        landedcostgrid[0].draw();
        landedcostgrid[0].parentComponent.redraw();
        //if we redraw parent we need to check checkbox
        if (obj.checked == true) {
            jq('#_obj__ENABLELANDEDCOST').prop('checked', true);
        }
    } else {
        landedcostgrid[0].redraw();

        //We need to also redraw the LC section holding the  Lc grid otherwise it will not show
        var section = window.editor.view.findComponentsById('landedcost', 'Section');
        if (section && section[0]) {
            section[0].redraw();
        }
    }
}

/**
 * Function to Show and Hide Landed Cost Value and
 * depending on Object Checked
 * Default the Value (i.e. LANDEDCOST_DEFAULT_VALUE)
 * referred in item_advanced_form.xml
 * @param {object} obj The Object Invoked
 * @param {object} landedCostGrid The Entire Grid
 * @param {integer} lineNo The Line No of grid
 */
function EnableLandedCostValue(obj, landedCostGrid, lineNo) {
    ShowHideLandedCostValue(obj, landedCostGrid, lineNo);
    var landedCostValue = landedCostGrid.findComponents('VALUE', 'Field');
    SetLandedCostValue(obj, landedCostValue, lineNo);
}

/**
 * Function to Show and Hide Landed Cost Value and
 * depending on Object Checked
 *
 * @param {object} obj The Object Invoked
 * @param {object} landedCostGrid The Entire Grid
 * @param {integer} lineNo The Line No of grid
 */
function ShowHideLandedCostValue(obj, landedCostGrid, lineNo) {
    var landedCostValue = landedCostGrid.findComponents('VALUE', 'Field');
    landedCostValue[lineNo].updateProperty('disabled', !obj.checked, false);
    if (typeof PAGE_LAYOUT_TYPE !== 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
        landedCostValue[lineNo].draw();
        landedCostValue[lineNo].parentComponent.redraw();
    } else {
        landedCostValue[lineNo].redraw();
    }
}


/**
 * Function to set the Landed Cost Value to
 * Default Value (i.e. LANDEDCOST_DEFAULT_VALUE)
 *
 * @param {object} obj The Object Invoked
 * @param {object} landedCostValue The Entire Grid
 * @param {integer} lineNo The Line No of grid
 */
function SetLandedCostValue(obj, landedCostValue, lineNo) {
    landedCostValue[lineNo].setValue(obj.checked ?
        (typeof valueOfLandedCost !== 'undefined' && valueOfLandedCost !== '')
            ? valueOfLandedCost :LANDEDCOST_DEFAULT_VALUE :'');
}


/**
 * Function to verify Landed Cost Value is empty
 * and show alert message.
 * referred in item_advanced_form.xml
 * @param {object} obj The Object Invoked
 * @param {object} landedCostGrid The Entire Grid
 * @param {integer} lineNo The Line No of grid
*/
function CheckLandedCostValueIsEmpty(obj,landedCostGrid,lineNo) {

    if (obj.meta.value == '' || isNaN(obj.meta.value) ||  Number(obj.meta.value)<0) {
      //  $quantityName = Number(obj.meta.value);
      // if(($quantityName < 0))
        alert(GT("IA.ENTER_POSITIVE_NUMBER"));
        var landedCostValue = landedCostGrid.findComponents('VALUE', 'Field');
        landedCostValue[lineNo].setValue(LANDEDCOST_DEFAULT_VALUE);
        if (typeof PAGE_LAYOUT_TYPE !== 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
            landedCostValue[lineNo].draw();
            landedCostValue[lineNo].parentComponent.redraw();
        } else {
            landedCostValue[lineNo].redraw();
        }
    }
}

function AutoFillWareHouse(obj) {
    var warehouseid = obj.value;
    var rowNo = obj.meta.getLineNo();
    setBinTrackOptions(warehouseid, rowNo, true);
    populateWarehouseCurrencyAndReplenishment(warehouseid, rowNo);
}


function fetchBinTrackOptions(warehouseid, rowNo) {
    var qrequest = new QRequest;
    var url = 'qrequest.phtml?.function=fetchBinTrackOptions&.handler=QRequest&.entity=item&.sess='+sess
        +'&.otherparams=warehouseid'
        +'&.warehouseid='+encodeURIComponent(warehouseid);
    var updateFunc = "RespProcesser_fetchBinTrackOptions";
    var updateArgs = "'"+rowNo+"--"+warehouseid+"'";
    qrequest.quickRequest(url, updateFunc, updateArgs, false);
}


function RespProcesser_fetchBinTrackOptions(rowAndWarehouse, m_response){

    var t_arr       = rowAndWarehouse.split("--");
    var rowNo       = t_arr[0];
    var warehouseid = t_arr[1];

    var nodes = m_response.getElementsByTagName("bin");
    // we ONLY get bins and ONLY in advanced mode
    for (var i = 0; i < nodes.length; i++) {
        var binId = nodes.item(i).getAttribute("id");
        wBins.push( [binId, warehouseid ] );
    }
    setBinTrackOptions(warehouseid, rowNo, false);  // false so we don't recurse forever
}


function setBinTrackOptions(warehouseid, rowNo, firstTime) {
    var grid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    grid = grid[0];

    var noWarehouseFound = true;
    var validValues      = new Array();

    if (binsAdvanced == 'T') {
        var obj = grid.findLineComponent('DEFAULT_AISLE', rowNo, 'Field');
        if (obj) {
            obj.updateProperty('hidden', true, true);
        }
        obj = grid.findLineComponent('DEFAULT_ROW', rowNo, 'Field');
        if (obj) {
            obj.updateProperty('hidden', true, true);
        }
    } else {
        for (var m = 0; m < wAisles.length; m++) {
            if ((wAisles[m][1] === warehouseid) && (wAisles[m][0] != null)) {
                noWarehouseFound = false;
                validValues.push(wAisles[m][0]);
            }
        }
        // if no aisles or rows, then maybe we're in advanced mode.
        // in any case, don't show the dropdowns if empty
        var aObj = grid.findLineComponent('DEFAULT_AISLE', rowNo, 'Field');
        if (aObj) {
            aObj.updateProperty(['type', 'validlabels'], validValues, true);
            aObj.updateProperty(['type', 'validvalues'], validValues, true);
            aObj.updateProperty(['type', '_validvalues'], validValues, true);
        }

        validValues = new Array();
        for (var m = 0; m < wRows.length; m++) {
            if ((wRows[m][1] === warehouseid) && (wRows[m][0] != null)) {
                noWarehouseFound = false;
                validValues.push(wRows[m][0]);
            }
        }
        var rObj = grid.findLineComponent('DEFAULT_ROW', rowNo, 'Field');
        if (rObj) {
            rObj.updateProperty(['type', 'validlabels'], validValues, true);
            rObj.updateProperty(['type', 'validvalues'], validValues, true);
            rObj.updateProperty(['type', '_validvalues'], validValues, true);
        }
    }

    validValues = new Array();
    for (var m=0; m < wBins.length; m++) {
        if ((wBins[m][1] === warehouseid) && (wBins[m][0] != null)) {
            noWarehouseFound = false;
            validValues.push(wBins[m][0]);
        }
    }

    // show this dropdown unconditionally
    var wObj = grid.findLineComponent('DEFAULT_BIN', rowNo, 'Field');
    if(wObj){
        var hideThis = (binsAdvanced == 'T') && (validValues.length == 0);
        wObj.updateProperty('hidden', hideThis, true);
        wObj.updateProperty(['type','validlabels'], validValues, true);
        wObj.updateProperty(['type','validvalues'], validValues, true);
        wObj.updateProperty(['type','_validvalues'], validValues, true);
    }

    // if we did not find the warehouse, ask the server if there is a new warehouse
    // or new bins on the warehouse.  But do this only once, as it recurses back into here.
    if (firstTime && warehouseid && (warehouseid != '') && (noWarehouseFound == true)) {
        fetchBinTrackOptions(warehouseid, rowNo);   // do this again, but with more warehouses
    }
}


function AutoFillComponent(obj){

    window.editor.gatherData();
    var kitID = window.editor.view.getFieldValue('ITEMID');
    var itemID = obj.value;
    var rowNo = obj.meta.getLineNo();
    var cObj = null;
    if (itemID != '' && itemID == kitID){
        alert(GT("IA.KIT_ID_COMPONENT_ID_CANNOT_BE_SAME"));
        cObj = grid.findLineComponent('COMPONENTKEY', rowNo, 'Field');
        cObj.setValue('');
        return true;
    }

    var grid = window.editor.view.findComponents('COMPONENT_INFO', 'Grid');
    grid = grid[0];

    for( var i = 0 ; i < grid.getRowCount() ; i++ )
    {
        if(i == rowNo) continue;
        var iComp = grid.value[i]['COMPONENTKEY'];
        if(iComp != '' && iComp == itemID)
        {
            cObj = grid.findLineComponent('COMPONENTKEY', rowNo, 'Field');
            cObj.setValue('');
            grid.setFocus(i);
            return true;
        }
    }
    var INDXITEMID = 0;
    var INDXDESC = 1;
    var INDXSTDUNIT = 2;
    var INDXCOSTMETHOD = 3;
    var INDXSTANDARDCOST = 4;
    var INDXITEMTYPE = 5;
    var INDXKCDLVRSTATUS = 6;
    var INDXKCREVDEFSTATUS = 7;

    var rec = ItemDetailFetch(itemID, kitID);
    if (rec==null){
        alert(GT("IA.NO_ITEM_MATCH_FOUND"));
    } else if (rec[INDXCOSTMETHOD] == 'X') {
        alert(GT("IA.ITEM_IN_PARENT_HIERARCHY"));
        return true;
    }


    var itemDesc = grid.findLineComponent('ITEMDESC', rowNo, 'Field');
    if(itemDesc){
        itemDesc.setValue(rec[INDXDESC]);
    }

    if(usefulfillment)
    {
        var kcdl = grid.findLineComponent('KCDLVRSTATUS', rowNo, 'Field');
        kcdl.setValue(rec[INDXKCDLVRSTATUS]);

        var kcre = grid.findLineComponent('KCREVDEFSTATUS', rowNo, 'Field');
        kcre.setValue(rec[INDXKCREVDEFSTATUS]);
    }

    var ucost = grid.findLineComponent('UNIT', rowNo, 'Field');
    ucost.setValue(rec[INDXSTDUNIT]);


    if (rec[INDXCOSTMETHOD] != null) {
        var cmethod = grid.findLineComponent('COSTMETHOD', rowNo, 'Field');
        if(cmethod){
            cmethod.setValue(rec[INDXCOSTMETHOD]);
        }
    }

    if (rec[INDXSTANDARDCOST] != null) {
        var scost = grid.findLineComponent('COST', rowNo, 'Field');
        if(scost){
            scost.setValue(rec[INDXSTANDARDCOST]);
        }
        compStandardCost[itemID] = rec[INDXSTANDARDCOST];
    }

    if (rec[INDXITEMTYPE] != null) {
        var ittype = grid.findLineComponent('ITEMTYPE1', rowNo, 'Field');
        if(ittype){
            ittype.setValue(rec[INDXITEMTYPE]);
        }
    }

    CalculateStandardCost(rowNo);
}

function CalculateStandardCost(rowNo){
    var grid = window.editor.view.findComponents('COMPONENT_INFO', 'Grid');
    grid = grid[0];

    var qtyF = grid.findLineComponent('QUANTITY', rowNo, 'Field');
    var qty = qtyF.getValue();
    if(qty == null || qty == '' || qty == '0'){
        qtyF.setValue('1');
    }else{
        var compFld = grid.findLineComponent('COMPONENTKEY', rowNo, 'Field');
        var compID = compFld.getValue();
        var scost = grid.findLineComponent('COST', rowNo, 'Field');
        if(scost){
            var sVal = compStandardCost[compID] * qty;
            scost.setValue(sVal);
        }
    }

    var costM = window.editor.view.findComponents('COST_METHOD', 'Field');
    var costmethod = costM[0].getValue();
    var stdCost = window.editor.view.findComponents('STANDARD_COST', 'Field');

    if (costmethod == 'Standard' || costmethod == 'Average'){
        var totalItemCost = 0;
        for( var i = 0 ; i < grid.getRowCount() ; i++ )
        {
            var compID = grid.value[i]['COMPONENTKEY'];
            if(compID == null || compID == '') continue;
            var iCost = grid.value[i]['COST'];
            var iCostM = grid.value[i]['COSTMETHOD'];
            if (iCostM == null || iCostM == 'F' || iCostM == 'L') {
                totalItemCost = 0;
                break;
            }

            if (iCost != null && iCost != 0) {
                totalItemCost = parseFloat(totalItemCost) + parseFloat(iCost);
            }
        }

        stdCost[0].setValue(RoundCurrency(totalItemCost , 2));
        stdCost[0].updateProperty('disabled',true, true);

    }
}

function AutoFillQuantity(obj){
    window.editor.gatherData();
    var rowNo = obj.meta.getLineNo();
    var grid = window.editor.view.findComponents('COMPONENT_INFO', 'Grid');
    grid = grid[0];
    var compID = grid.findLineComponent('COMPONENTKEY', rowNo, 'Field');
    if (compID.getValue() != ''){
        CalculateStandardCost(rowNo);
    }
}


function ItemDetailFetch(itemid,kitid) {

    var strLoc = "itemDetailfetch.phtml?.sess="+sess
                 + "&.itemid="+encodeURIComponent(itemid)
                 + "&.kitid="+encodeURIComponent(kitid)
                 + "&.tags=1&.mod="+mod;

    output = baseLoadXMLHTTP(strLoc, true);
    nodes = output.getElementsByTagName("item");
    if (nodes.length > 0) {
        itemid = nodes.item(0).getAttribute("itemid");
        if (itemid) {
            desc = nodes.item(0).getAttribute("desc");
            stdunit = nodes.item(0).getAttribute("stdunit");
            costmethod = nodes.item(0).getAttribute("costmethod");
            standardcost = nodes.item(0).getAttribute("standardcost");
            itemtype = nodes.item(0).getAttribute("itemtype");
            vsoedlvrstatus = nodes.item(0).getAttribute("vsoedlvrstatus");
            vsoerevdefstatus = nodes.item(0).getAttribute("vsoerevdefstatus");

            var newItem = Array(itemid, desc, stdunit, costmethod , standardcost , itemtype, vsoedlvrstatus, vsoerevdefstatus);
            return newItem;
        }
    }
}



/* ----- effective date warehouse standard cost realted  ---*/

function StdCostEntriesSection()
{
   // this.hasForm = false;
}

StdCostEntriesSection.inheritsFrom( Section );

StdCostEntriesSection.prototype.draw = function()
{
    return Section.prototype.draw.call(this);
};


function StdCostEntriesGrid()
{
}

StdCostEntriesGrid.inheritsFrom( Grid );

StdCostEntriesGrid.prototype.deleteRow = function(rowIndex)
{
    Grid.prototype.deleteRow.call(this, rowIndex);
};

StdCostEntriesGrid.prototype.initialize = function(parentContext, parentValue) {
    Grid.prototype.initialize.call(this, parentContext, parentValue);
}

StdCostEntriesGrid.prototype.draw = function(drawDoneCallback)
{
    return Grid.prototype.draw.call(this, drawDoneCallback);
};



function ItemWhseDetails()
{
}

ItemWhseDetails.inheritsFrom( LineDetails );

ItemWhseDetails.prototype.initialize = function (parentContext, parentValue)
{
    LineDetails.prototype.initialize.call( this, parentContext, parentValue );
};

ItemWhseDetails.prototype.expand = function()
{
    LineDetails.prototype.expand.call(this);
    HideStdCostEntries(this);
    showHideWarehouseFieldsForReplishment(this);
    populateWarehouseUOMPicklists(this);
    populateWarehouseSalesForecastMethodPicklist(this);

    WarehouseVendorGrid_disableEnablePreferredVendorCheckboxes(this);
    WarehouseVendorGrid_toggleFirstPreferredVendorCheckboxes(this, 0);
}

function showHideWarehouseFieldsForReplishment(itemWhseLineDetail)
{
    //Warehouse replenishment flag
    var whReplenishOn      = (itemWhseLineDetail.value.W_ENABLE_REPLENISHMENT === 'true'); // warehouse is enabled for replenishment?
    var iw_replenishmentOn = (itemWhseLineDetail.value.ENABLE_REPLENISHMENT === 'true'); // the item/warehouse

    //Item replenishment flag
    var field = window.editor.view.findComponents('ENABLE_REPLENISHMENT', 'Field'); // item is enabled?
    var itemReplenishOn = false;
    if (field && field[0]) {
        itemReplenishOn = (field[0].getValue() === 'true');
    }

    //Inv config page flag
    field = window.editor.view.findComponents('SHOW_REPLENISHMENT', 'Field'); // are we configured for replenishment?
    var invReplenishOn = false;
    if (field && field[0]) {
        invReplenishOn = (field[0].getValue() === 'true');
    }

    var replenishmentOn = invReplenishOn && itemReplenishOn && whReplenishOn;

    var forecastValue = null;
    if (replenishmentOn) {
        var forecastMethodField = itemWhseLineDetail.findComponentsById('SalesForecastMethodFieldId', 'Field');
        if (forecastMethodField && forecastMethodField[0]) {
            forecastValue = forecastMethodField[0].getValue();
        }
        var grid = itemWhseLineDetail.findComponents('ITEMWAREHOUSEVENDORENTRIES', 'Grid');
        if (grid && grid[0]) {
            (forecastValue === SINGLE_VALUE) ?
                grid[0].showColumn('FORECAST_DEMAND_IN_LEAD_TIME') :
                grid[0].hideColumn('FORECAST_DEMAND_IN_LEAD_TIME');
        }
    }

    // remove the 'enable replenishment' checkbox if the item, warehouse, or config don't have it on.
    // Also, remove the cycle count and Storage Area if replenishment is off.  Even though those two fields
    // ARE NOT PART OF REPLENISHMENT, they are enabled BELOW in the CycleSectionId for the old-style
    // replenishment report.  Ick.
    var section = itemWhseLineDetail.findComponentsById('WarehouseReplenishmentId', 'Section');
    if (section && section[0]) {
        section[0].showHide(replenishmentOn);
        section[0].redraw();
    }

    var section = itemWhseLineDetail.findComponentsById('WhseEnableReplenishmentId', 'Field');
    if (section && section[0]) {
        section[0].showHide(replenishmentOn);
        section[0].redraw();
    }

    //Replenishment UI sections
    section = itemWhseLineDetail.findComponentsById('SafetyStockSectionId', 'Field');
    if (section && section[0]) {
        section[0].showHide(replenishmentOn && iw_replenishmentOn);
        section[0].redraw();
    }

    section = itemWhseLineDetail.findComponentsById('MaxOrderSectionId', 'Field');
    if (section && section[0]) {
        section[0].showHide(replenishmentOn && iw_replenishmentOn);
        section[0].redraw();
    }

    //Replenishment UI section
    field = itemWhseLineDetail.findComponentsById('SalesForecastMethodFieldId', 'Field');
    if (field && field[0]) {
        field[0].showHide(replenishmentOn && iw_replenishmentOn);
        field[0].redraw();
    }

    //Replenishment UI section
    section = itemWhseLineDetail.findComponentsById('WhseReorderSectionId', 'Section');
    if (section && section[0])
    {
        section[0].showHide(replenishmentOn && iw_replenishmentOn && forecastValue === 'Reorder point');
        section[0].redraw();
    }
    //Replenishment UI section
    field = itemWhseLineDetail.findComponentsById('addforecastwarehouse', 'Button');
    if (field && field[0])
    {
        var create = field[0].create
        var isfluctuatingforecast = forecastValue === 'Demand forecast by fluctuating values';
        showbutton = (create == false) && (isfluctuatingforecast == true) && replenishmentOn;
        field[0].showHide(showbutton);
        field[0].redraw();
    }

    //Replenishment UI section
    section = itemWhseLineDetail.findComponentsById('ReorderSectionId', 'Section');
    if (section && section[0]) {
        section[0].showHide(replenishmentOn && iw_replenishmentOn && forecastValue === 'Reorder point');
        section[0].redraw();
    }

    //Replenishment UI section
    section = itemWhseLineDetail.findComponentsById('ItemWarehouseVendorGridSectionId', 'Section');
    if (section && section[0]) {
        var isStockableKit = window.editor.view.getFieldValue('ITEMTYPE') == STOCKABLE_KIT;
        section[0].showHide(replenishmentOn && iw_replenishmentOn && !isStockableKit);
        section[0].redraw();
    }

    //Non-replenishment UI section
    section = itemWhseLineDetail.findComponentsById('CycleSectionId', 'Section');
    if (section && section[0]) {
        section[0].showHide(!replenishmentOn);  // so enable it if replenishment is NOT on
        section[0].redraw();
    }

    //Non-replenishment UI section
    section = itemWhseLineDetail.findComponentsById('OrderQtyStockAmtSectionId', 'Section');
    if (section && section[0]) {
        section[0].showHide(!replenishmentOn);  // so enable it if replenishment is NOT on
        section[0].redraw();
    }
}

function HideStdCostEntries(objtemp){
    var costMethodFld = window.editor.view.findComponents('COST_METHOD', 'Field')[0];
    var itemTypeFld = window.editor.view.findComponents('ITEMTYPE', 'Field')[0];
    var enableFulfillment = window.editor.view.getFieldValue('ENABLEFULFILLMENT');

    //add fulfillment condition
     if(costMethodFld && itemTypeFld) {
        var costmethod = costMethodFld.getValue();
        var itemType = itemTypeFld.getValue();

        var hideStdCostEntries = ((costmethod == 'Standard' && itemType=='Inventory') || enableFulfillment == 'true') ? true : false;
        var section = objtemp.findComponentsById('StdCostEntries', 'Section')[0];
        if (section) {
            section.showHide(hideStdCostEntries);
            section.redraw();
        }
    }
}

function handleSalesForecastMethodPicklistChange()
{
    //When forecast method changes, we need to get the line details of
    //the warehouse grid and show/hide the fields in it.
    var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    if (whGrid && whGrid[0] && whGrid[0].lineDetails) {
        showHideWarehouseFieldsForReplishment(whGrid[0].lineDetails);
    }
}

function handleIWReplenishmentOnChange()
{
    //When forecast method changes, we need to get the line details of
    //the warehouse grid and show/hide the fields in it.
    var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    if (whGrid && whGrid[0] && whGrid[0].lineDetails) {
        showHideWarehouseFieldsForReplishment(whGrid[0].lineDetails);
    }
}

//This function updates the sales forecast method picklist since inventory and
//stockable kit have different pick values
function populateWarehouseSalesForecastMethodPicklist(itemWhseLineDetail)
{
    var replenishmentOn = (itemWhseLineDetail.value.W_ENABLE_REPLENISHMENT === 'true');
    var field = window.editor.view.findComponents('ENABLE_REPLENISHMENT', 'Field');
    if (field && field[0] && replenishmentOn) {
        replenishmentOn = (field[0].getValue() === 'true');
    }

    //Below logic is only for replenishment turned on so bail if not
    if (!replenishmentOn) {
        return;
    }

    field = itemWhseLineDetail.findComponentsById('SalesForecastMethodFieldId', 'Field');
    if (field && field[0]) {
        populateSalesForecastMethodPicklist(field[0]);
    }
}

function populateSalesForecastMethodPicklist(picklist)
{
    if (picklist) {
        var field       = window.editor.view.findComponents('FLUCTUATING_FORECAST', 'Field');
        var value       = field[0].getValue();
        var fluctuate   = (value === 'true');

        var itemType = window.editor.view.getFieldValue('ITEMTYPE');
        if (itemType == INVENTORY) {
            if (fluctuate) {
                picklist.type.validvalues   = INV_REPLENISHMENT_METHOD_VALUES_F;
                picklist.type._validivalues = INV_REPLENISHMENT_METHOD_IVALUES_F;
                picklist.type.validlabels  = INV_REPLENISHMENT_METHOD_LABELS_F;
            } else {
                picklist.type.validvalues   = INV_REPLENISHMENT_METHOD_VALUES;
                picklist.type._validivalues = INV_REPLENISHMENT_METHOD_IVALUES;
                picklist.type.validlabels  = INV_REPLENISHMENT_METHOD_LABELS;
            }
            picklist.redraw();
        } else if (itemType == STOCKABLE_KIT) {
            if (fluctuate) {
                picklist.type.validvalues   = SKIT_REPLENISHMENT_METHOD_VALUES_F;
                picklist.type._validivalues = SKIT_REPLENISHMENT_METHOD_IVALUES_F;
                picklist.type.validlabels  = SKIT_REPLENISHMENT_METHOD_LABELS_F;
            } else {
                picklist.type.validvalues   = SKIT_REPLENISHMENT_METHOD_VALUES;
                picklist.type._validivalues = SKIT_REPLENISHMENT_METHOD_IVALUES;
                picklist.type.validlabels  = SKIT_REPLENISHMENT_METHOD_LABELS;
            }
            //Set the current value to the first item in picklist if we don't have the current value in our picklist for s-kit type
            if (picklist.type.validvalues.indexOf(picklist.getValue()) < 0) {
                picklist.setValue(picklist.type.validvalues[0]);
            }
            picklist.redraw();
        }
    }
}

function populateWarehouseCurrencyAndReplenishment(warehouseid, rowNo) {
    var qrequest = new QRequest;
    var url = 'qrequest.phtml?.function=FetchWarehouseCurrencyAndReplenishment&.handler=QRequest&.entity=item&.sess='+sess
        +'&.otherparams=warehouseid'
        +'&.warehouseid='+encodeURIComponent(warehouseid);
    var updateFunc = "RespProcesser_WarehouseCurrencyAndReplenishment";
    var updateArgs = "'"+rowNo+"'";
    qrequest.quickRequest(url, updateFunc, updateArgs, false);
}

function RespProcesser_WarehouseCurrencyAndReplenishment(rowNo, m_response){
    nodes = m_response.getElementsByTagName("FetchWarehouseCurrencyAndReplenishment");

    var grid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    if (grid && grid[0]) {
        var whseCurrency = grid[0].findLineComponent('WHSE_CURRENCY', rowNo, 'Field');
        if (whseCurrency) {
            whseCurrency.setValue(nodes.item(0).getAttribute("currency"));
            whseCurrency.redraw();
        }

        //Init the warehouse line details with the returned replenishment flag. Also, if the warehouse grid
        //is expanded, we need to collapse and expanded back to make the redraw of popup window shade show correctly.
        var itemType = window.editor.view.getFieldValue('ITEMTYPE');
        var showReplenishmentField = window.editor.view.findComponents('SHOW_REPLENISHMENT', 'Field');
        var enableReplenishmentField = window.editor.view.findComponents('ENABLE_REPLENISHMENT', 'Field');
        grid[0].lineDetails.value.W_ENABLE_REPLENISHMENT = (
            (showReplenishmentField && showReplenishmentField[0]) &&
            (showReplenishmentField[0].getValue() === 'true') &&
            (enableReplenishmentField && enableReplenishmentField[0]) &&
            (enableReplenishmentField[0].getValue() === 'T' || enableReplenishmentField[0].getValue() === 'true') &&
            (itemType == INVENTORY || itemType == STOCKABLE_KIT)) &&
            (nodes.item(0).getAttribute("w_enable_replenishment"));
        if (grid[0].lineDetails.isExpanded()) {
            grid[0].lineDetails.collapse();
            grid[0].lineDetails.expand();
        }
    }
}


function setFieldForLandedCost(iType)
{
    window.setTimeout(function() {
        if (enablelandedcost == true) {
            var enableLandedCostObj = window.editor.view.findComponents('ENABLELANDEDCOST', 'Field');
            if (iType == NONINV || iType == NONINV_PO) {
                enableLandedCostObj[0].fullname = GT("IA.ENABLE_AS_LANDED_COST");
            } else {
                enableLandedCostObj[0].fullname = GT("IA.ENABLE_DISTRIBUTION_OF_LANDED_COSTS_TO_THIS_ITEM");
            }

            enableLandedCostObj[0].redraw();
        }
    }, 0);
}

function setFieldsForReplenishment()
{
    window.setTimeout(function() {
        var itemType    = window.editor.view.getFieldValue('ITEMTYPE');
        var field       = window.editor.view.findComponents('SHOW_REPLENISHMENT', 'Field');
        var value       = field[0].getValue();
        var show        = (value === 'true' && (itemType == INVENTORY || itemType == STOCKABLE_KIT));

        field           = window.editor.view.findComponents('ENABLE_REPLENISHMENT', 'Field'); // item level
        value           = field[0].getValue();
        var replenish   = ((value === 'T' || value === 'true') && show);
        field[0].updateProperty('disabled', !show, true);    // disable the checkbox

        //Show the vendor tab for s-kit
        var vendorTab = window.editor.view.findComponentsById('vendor', 'Tab');
        if (vendorTab && vendorTab[0]) {
            if (itemType == STOCKABLE_KIT) {
                (show) ? window.editor.view.showTab(vendorTab[0]) : window.editor.view.hideTab(vendorTab[0]);
            }
        }

        //Hide replenishment section if main replenishment
        var replenishementSection = window.editor.view.findComponentsById('ReplenishmentSectionId', 'Section');
        if (replenishementSection && replenishementSection[0]) {
            replenishementSection[0].showHide(show);
            replenishementSection[0].redraw();
        }

        var allfields  = [ "REPLENISHMENT_METHOD", "DEFAULT_REPLENISHMENT_UOM", "REORDER_POINT", "REORDER_QTY", "MAX_ORDER_QTY", "MAX_ORDER_QTY_REP", "SAFETY_STOCK"];
        for (var i = 0; i < allfields.length; i++) {
            field = window.editor.view.findComponents(allfields[i], 'Field');
            if (field && field[0]) {
                field[0].showHide(replenish);
                field[0].redraw();
            }
        }

        var forecastMethodvalue = null;
        if (replenish) {
            var field = window.editor.view.findComponents('REPLENISHMENT_METHOD', 'Field');
            forecastMethodvalue = field[0].getValue();

            var addforecastbutton = window.editor.view.findComponentsById('addforecastitem', 'Button');
            if (addforecastbutton && addforecastbutton[0])
            {
                create = addforecastbutton[0].create;
                forecasttype = forecastMethodvalue === 'Demand forecast by fluctuating values';
                showbutton = create == false && forecasttype == true;
                addforecastbutton[0].showHide(showbutton);
                addforecastbutton[0].redraw();
            }

            var reorderSection = window.editor.view.findComponentsById('ReorderSectionId', 'Section');
            if (reorderSection && reorderSection[0])
            {
                reorderSection[0].showHide(forecastMethodvalue === 'Reorder point');
                reorderSection[0].redraw();
            }


            var vendorSection = window.editor.view.findComponentsById('VendorHistorySectionId', 'Section');
            if (vendorSection && vendorSection[0]) {
                if (itemType == STOCKABLE_KIT) {
                    vendorSection[0].showHide(false);
                    vendorSection[0].redraw();
                }
            }

            var picklist = window.editor.view.findComponents('REPLENISHMENT_METHOD', 'Field');
            if (picklist && picklist[0]) {
                populateSalesForecastMethodPicklist(picklist[0]);
            }
        }

        var grid = window.editor.view.findComponents('VENDOR_INFO', 'Grid');
        if (grid && grid[0]) {
            (replenish) ? grid[0].showColumn('MIN_ORDER_QTY') : grid[0].hideColumn('MIN_ORDER_QTY');
            (replenish) ? grid[0].showColumn('UOM') : grid[0].hideColumn('UOM');
            (replenish) ? grid[0].showColumn('PREFERRED_VENDOR') : grid[0].hideColumn('PREFERRED_VENDOR');
            (forecastMethodvalue === SINGLE_VALUE) ? grid[0].showColumn('FORECAST_DEMAND_IN_LEAD_TIME') : grid[0].hideColumn('FORECAST_DEMAND_IN_LEAD_TIME');
            grid[0].redraw();
        }

    }, 0);
}

function populateUOMPicklists(uomgrp, loadingItem)
{
    //Bail if we have nothing to populate
    if (!uomgrp || uomgrp === '') {
        return;
    }

    var qrequest = new QRequest;
    var url = 'qrequest.phtml?.function=FetchUOMGrpInfo&.handler=QRequest&.entity=item&.sess='+sess
        +'&.otherparams=uomgrp'
        +'&.uomgrp='+encodeURIComponent(uomgrp);
    var updateFunc = "RespProcesser_UOMPicklists";
    var updateArgs = "'"+loadingItem+"'";
    qrequest.quickRequest(url, updateFunc, updateArgs, false);
}

function fillPicklist(picklist, dataArray, defaultVal)
{
    if (picklist && dataArray) {
        picklist.updateProperty(['type', 'validvalues'], dataArray);
        if (defaultVal) {
            picklist.setValue(defaultVal);
        }
        picklist.updateMetadata();
    }
}

function populateDefaultReplenishnentUOMPicklist(uoms, purchaseDef)
{
    //Fill the "Default unit of measure" picklist in the advanced tab that depend on the UOM group
    var defaultUOMPicklist = window.editor.view.findComponents('DEFAULT_REPLENISHMENT_UOM', "Field");
    if (defaultUOMPicklist && defaultUOMPicklist[0]) {
        fillPicklist(defaultUOMPicklist[0], uoms, purchaseDef);
    }
}

function populateVendorHistoryUOMPicklists(uoms, purchaseDef)
{
    //Fill the vendor grid's unit of measure picklists in the vendor history tab that depend on the UOM group
    var vendorHistUOMPicklist = window.editor.view.findComponents('UOM', "Field");
    if (vendorHistUOMPicklist) {
        for (var i = 0; i < vendorHistUOMPicklist.length; i++) {
            if (vendorHistUOMPicklist[i].parentValue.VENDORID && vendorHistUOMPicklist[i].parentValue.VENDORID.length > 0) {
                fillPicklist(vendorHistUOMPicklist[i], uoms, purchaseDef);
            }
        }
    }
}

function populateWarehouseUOMPicklists(itemWhseLineDetail)
{
    //If replenishment is not on for warehouse, we don't need to populate the UOMs
    var replenishmentOn = (itemWhseLineDetail.value.W_ENABLE_REPLENISHMENT === 'true') && (itemWhseLineDetail.value.ENABLE_REPLENISHMENT === 'true');
    if (!replenishmentOn) {
        return;
    }

    //This is the actual populating of the UOMs to the picklist
    var warehouseUOMPicklist = itemWhseLineDetail.findComponents('UOM', "Field");
    if (warehouseUOMPicklist) {
        var uomgrpField = window.editor.view.findComponents('UOMGRP', 'Field');
        if (uomgrpField && uomgrpField[0] && uomgrpField[0].uoms) {
            for (var i = 0; i < warehouseUOMPicklist.length; i++) {
                if (warehouseUOMPicklist[i].parentValue.VENDORID && warehouseUOMPicklist[i].parentValue.VENDORID.length > 0) {
                    fillPicklist(warehouseUOMPicklist[i], uomgrpField[0].uoms, uomgrpField[0].purchaseDefaultUOM);
                }
            }
        }
    }
}

function populateItemCrossRefUOMPicklists(uoms)
{
    //Fill the vendor grid's unit of measure picklists in the item cross reference tab that depend on the UOM group
    var itemCrossRefUOMPicklist = window.editor.view.findComponents('UNIT', "Field");
    if (itemCrossRefUOMPicklist) {
        for (var i = 0; i < itemCrossRefUOMPicklist.length; i++) {
            var refType = itemCrossRefUOMPicklist[0].parentValue["REFTYPE"];
            if (refType == 'Customer' || refType == 'Vendor') {
                fillPicklist(itemCrossRefUOMPicklist[i], uoms);
            }
        }
    }
}

function RespProcesser_UOMPicklists(loadingItem, m_response)
{
    //Process the uoms of the uom group
    var responseUOMs = m_response.getElementsByTagName('uom');
    var uoms = '';
    if (responseUOMs.length > 0) {

        //Get the uoms from the response
        var uoms = new Array();
        var uomFactors = new Array();
        for (var i = 0; i < responseUOMs.length; i++) {
            uoms[i] = responseUOMs.item(i).getAttribute("unit");
            uomFactors[uoms[i]] = responseUOMs.item(i).getAttribute("factor");
        }

        //Preserves the uoms in the uom group incase we need it such as in handleVendorChange
        var uomgrpField = window.editor.view.findComponents('UOMGRP', 'Field');
        if (uomgrpField && uomgrpField[0]) {
            uomgrpField[0].uoms = uoms;
            uomgrpField[0].uomFactors = uomFactors;
        }
    }

    //Process the uoms defaults of the uom group
    var purchaseDef = '';
    if (loadingItem && loadingItem === "false") {
        var responsePurchaseDef = m_response.getElementsByTagName('purchase_default');
        if (responsePurchaseDef.length > 0) {
            purchaseDef = responsePurchaseDef.item(0).getAttribute("unit");
            var uomgrpField = window.editor.view.findComponents('UOMGRP', 'Field');
            if (uomgrpField && uomgrpField[0]) {
                uomgrpField[0].purchaseDefaultUOM = purchaseDef;
            }
        }
    }

    populateDefaultReplenishnentUOMPicklist(uoms, purchaseDef);
    populateVendorHistoryUOMPicklists(uoms, purchaseDef);
    populateItemCrossRefUOMPicklists(uoms);
}

function VendorHistoryGrid_handleVendorChange(vendorDropDownObj)
{
    var rowNo = vendorDropDownObj.meta.getLineNo();

    //Populate the UOM picklist in the vendor grid
    if (vendorDropDownObj.meta.parentValue['UOM'] === undefined) {
        var uomgrpField = window.editor.view.findComponents('UOMGRP', 'Field');
        if (uomgrpField && uomgrpField[0] && uomgrpField[0].uoms) {
            var field = window.editor.view.findComponents('UOM', "Field");
            if (field && field[rowNo]) {
                fillPicklist(field[rowNo], uomgrpField[0].uoms);
            }
        }
    }

    //Default the econ order qty to 1 when vendors change
    var field = window.editor.view.findComponents('ECONOMIC_ORDER_QTY', "Field");
    if (field && field[rowNo]) {
        field[rowNo].setValue(1);
    }

    //Default the econ order qty to 1 when vendors change
    field = window.editor.view.findComponents('MIN_ORDER_QTY', "Field");
    if (field && field[rowNo]) {
        field[rowNo].setValue(1);
    }

    //Populate the vendor history grid lead time field base on the vendor's default
    VendorGrid_PopulateLeadTimeField("RespProcesser_VendorHistoryGridLeadTime", vendorDropDownObj.meta.value, rowNo);

    VendorHistoryGrid_disableEnablePreferredVendorCheckboxes();
    VendorHistoryGrid_toggleFirstPreferredVendorCheckboxes(rowNo);
}

function WarehouseVendorGrid_handleVendorChange(vendorDropDownObj)
{
    var rowNo = vendorDropDownObj.meta.getLineNo();
    var whGrid = null;

    //Populate the UOM picklist in the warehouse vendor grid
    if (vendorDropDownObj.meta.parentValue['UOM'] === undefined) {
        var uomgrpField = window.editor.view.findComponents('UOMGRP', 'Field');
        if (uomgrpField && uomgrpField[0] && uomgrpField[0].uoms) {
            var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
            if (whGrid && whGrid[0]) {
                var vendorGrid = whGrid[0].lineDetails.findComponents('ITEMWAREHOUSEVENDORENTRIES', 'Grid');
                if (vendorGrid && vendorGrid[0]) {
                    var field = vendorGrid[0].findComponents('UOM', "Field");
                    if (field && field[rowNo]) {
                        fillPicklist(field[rowNo], uomgrpField[0].uoms);
                    }

                    //Default the econ order qty to 1 when vendors change
                    var field = vendorGrid[0].findComponents('ECONOMIC_ORDER_QTY', "Field");
                    if (field && field[rowNo]) {
                        field[rowNo].setValue(1);
                    }

                    //Default the econ order qty to 1 when vendors change
                    field = vendorGrid[0].findComponents('MIN_ORDER_QTY', "Field");
                    if (field && field[rowNo]) {
                        field[rowNo].setValue(1);
                    }
                }
            }
        }
    }

    //Populate the warehouse vendor grid lead time field base on the vendor's default
    VendorGrid_PopulateLeadTimeField("RespProcesser_WarehouseVendorGridLeadTime", vendorDropDownObj.meta.value, rowNo);

    if (whGrid == null) {
        whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    }
    if (whGrid && whGrid[0] && whGrid[0].lineDetails) {
        WarehouseVendorGrid_disableEnablePreferredVendorCheckboxes(whGrid[0].lineDetails);
        WarehouseVendorGrid_toggleFirstPreferredVendorCheckboxes(whGrid[0].lineDetails, rowNo);
    }
}

function Grid_handleUOMChange(uomDropDownObj)
{
    var uomgrpField = window.editor.view.findComponents('UOMGRP', 'Field');
    if (uomgrpField && uomgrpField[0] && uomgrpField[0].uomFactors) {
        uomDropDownObj.meta.parentValue['CONVFACTOR'] = uomgrpField[0].uomFactors[uomDropDownObj.meta.value];
    }
}

function VendorGrid_PopulateLeadTimeField(respHandler, vendorId, rowNo)
{
    //Bail if we have nothing to populate
    if (!vendorId || vendorId === '') {
        return;
    }

    var qrequest = new QRequest;
    var url = 'qrequest.phtml?.function=FetchVendorInfo&.handler=QRequest&.entity=item&.sess='+sess
        +'&.otherparams=vendorId'
        +'&.vendorId='+encodeURIComponent(vendorId);
    var updateFunc = respHandler;
    var updateArgs = "'"+rowNo+"'";
    qrequest.quickRequest(url, updateFunc, updateArgs, false);
}


function RespProcesser_VendorHistoryGridLeadTime(rowNo, m_response)
{
    nodes = m_response.getElementsByTagName("FetchVendorInfo");
    if (nodes && nodes.item(0)) {
        var field = window.editor.view.findComponents('LEAD_TIME', "Field");
        if (field && field[rowNo]) {
            field[rowNo].setValue(nodes.item(0).getAttribute("lead_time_default"));
        }
    }
}

function RespProcesser_WarehouseVendorGridLeadTime(rowNo, m_response)
{
    nodes = m_response.getElementsByTagName("FetchVendorInfo");
    if (nodes && nodes.item(0)) {
        var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
        if (whGrid && whGrid[0]) {
            var vendorGrid = whGrid[0].lineDetails.findComponents('ITEMWAREHOUSEVENDORENTRIES', 'Grid');
            if (vendorGrid && vendorGrid[0]) {
                var field = vendorGrid[0].findComponents('LEAD_TIME', "Field");
                if (field && field[rowNo]) {
                    field[rowNo].setValue(nodes.item(0).getAttribute("lead_time_default"));
                }
            }
        }
    }
}

function VendorHistoryGrid_handlePreferredVendorToggle(vendorCheckBoxObj)
{
    if (vendorCheckBoxObj.meta.getValue() === 'false') {
        vendorCheckBoxObj.meta.setValue('true'); //We cannot uncheck a preferred vendor but can select another one
    } else {
        VendorHistoryGrid_uncheckPreferredVendorCheckboxes(vendorCheckBoxObj.meta.getLineNo());
    }
}

function WarehouseVendorGrid_handlePreferredVendorToggle(vendorCheckBoxObj)
{
    if (vendorCheckBoxObj.meta.getValue() === 'false') {
        vendorCheckBoxObj.meta.setValue('true'); //We cannot uncheck a preferred vendor but can select another one
    } else {
        var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
        if (whGrid && whGrid[0] && whGrid[0].lineDetails) {
            WarehouseVendorGrid_uncheckPreferredVendorCheckboxes(whGrid[0].lineDetails, vendorCheckBoxObj.meta.getLineNo());
        }
    }
}

function VendorHistoryGrid_toggleFirstPreferredVendorCheckboxes(rowNo)
{
    var vendInfoGrid = window.editor.view.findComponents('VENDOR_INFO', 'Grid');
    if (vendInfoGrid) {
        var preferredFld = vendInfoGrid[0].findComponents('PREFERRED_VENDOR', 'Field');

        if (preferredFld) {
            //See if we already have a preferred vendor checked
            for (var i = 0; i < preferredFld.length; i++) {
                if (preferredFld[i].getValue() === 'true') {
                    return;
                }
            }

            var vendorFld = vendInfoGrid[0].findComponents('VENDORID', 'Field');
            if (vendorFld[rowNo].getValue() !== undefined) {
                preferredFld[rowNo].setValue('true');
                preferredFld[rowNo].redraw(); //Need to do this otherwise in view mode, we will not see the check mark
            }
        }
    }
}

function VendorHistoryGrid_disableEnablePreferredVendorCheckboxes()
{
    var vendInfoGrid = window.editor.view.findComponents('VENDOR_INFO', 'Grid');
    if (vendInfoGrid) {
        var preferredFld = vendInfoGrid[0].findComponents('PREFERRED_VENDOR', 'Field');
        var vendorFld = vendInfoGrid[0].findComponents('VENDORID', 'Field');

        if (vendorFld && preferredFld) {
            for (var i = 0; i < preferredFld.length; i++) {
                preferredFld[i].updateProperty('disabled', (vendorFld[i].getValue() === undefined), true);
            }
        }
    }
}

function VendorHistoryGrid_uncheckPreferredVendorCheckboxes(skipRowNo)
{
    var vendInfoGrid = window.editor.view.findComponents('VENDOR_INFO', 'Grid');
    if (vendInfoGrid) {
        var preferredFld = vendInfoGrid[0].findComponents('PREFERRED_VENDOR', 'Field');

        if (preferredFld) {
            for (var i = 0; i < preferredFld.length; i++) {
                if (i != skipRowNo) {
                    preferredFld[i].setValue('false');
                }
            }
        }
    }
}

function WarehouseVendorGrid_toggleFirstPreferredVendorCheckboxes(itemWhseLineDetail, rowNo)
{
    //var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    if (itemWhseLineDetail) {
        var preferredFld = itemWhseLineDetail.findComponents('PREFERRED_VENDOR', 'Field');

        if (preferredFld) {
            //See if we already have a preferred vendor checked
            for (var i = 0; i < preferredFld.length; i++) {
                if (preferredFld[i].getValue() === 'true') {
                    return;
                }
            }

            var vendorFld = itemWhseLineDetail.findComponents('VENDORID', 'Field');
            if (vendorFld[rowNo].getValue() !== undefined) {
                preferredFld[rowNo].setValue('true');
                preferredFld[rowNo].redraw();
            }
        }
    }
}

function WarehouseVendorGrid_disableEnablePreferredVendorCheckboxes(itemWhseLineDetail)
{
    //var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    if (itemWhseLineDetail) {
        var preferredFld = itemWhseLineDetail.findComponents('PREFERRED_VENDOR', 'Field');
        var vendorFld = itemWhseLineDetail.findComponents('VENDORID', 'Field');

        if (vendorFld && preferredFld) {
            for (var i = 0; i < preferredFld.length; i++) {
                preferredFld[i].updateProperty('disabled', (vendorFld[i].getValue() === undefined), true);
            }
        }
    }
}

function WarehouseVendorGrid_uncheckPreferredVendorCheckboxes(itemWhseLineDetail, skipRowNo)
{
    //var whGrid = window.editor.view.findComponents('WAREHOUSE_INFO', 'Grid');
    if (itemWhseLineDetail) {
        var preferredFld = itemWhseLineDetail.findComponents('PREFERRED_VENDOR', 'Field');

        if (preferredFld) {
            for (var i = 0; i < preferredFld.length; i++) {
                if (i != skipRowNo) {
                    preferredFld[i].setValue('false');
                }
            }
        }
    }
}




function handleaAddForecast(obj, opId,a,b)
{
    fieldMeta =  obj.meta;
    clickdata = fieldMeta.clickdata;
    url = clickdata.url;
        Launch(url, 'addforecast', 900, 500);
}

function showHideMultiTaxGroups() {
    var multiTaxGroupPage = window.view.findComponentsById("sys_itg");
    if (multiTaxGroupPage && (multiTaxGroupPage[0].hidden === 'false' || multiTaxGroupPage[0].hidden === false)) {
        var allowMultipleTaxGroups = window.editor.findComponents('ALLOWMULTIPLETAXGRPS', 'Field');
        allowMultipleTaxGroups = allowMultipleTaxGroups[0];
        onChangeAllowMultipleTaxGroup(allowMultipleTaxGroups.value, false);
    } else {
        //Forcefully redrawing the TAXGROUP.NAME picker
        var taxGroupName = window.editor.findComponents('TAXGROUP.NAME', 'Field');
        if (taxGroupName && taxGroupName[0]) {
            taxGroupName[0].redraw();
        }
    }
}

function onChangeAllowMultipleTaxGroup(fieldValue, fromHandleEvent) {
    fieldValue = fieldValue == 'true' || fieldValue == true ? true : false;
    var grid = window.editor.findComponents('MULTIPLEITEMTAXGROUPS', 'Grid');
    grid = grid ? grid[0] : null;
    if (grid) {
        grid.showHide(fieldValue);
        grid.parentComponent.showHide(fieldValue);
    }
    var taxGroupNameHeader = window.editor.findComponents('TAXGROUP.NAME', 'Field');
    taxGroupNameHeader = taxGroupNameHeader[1];
    var taxSolutionIdHeader = window.editor.findComponents('TAXGROUP.TAXSOLUTION.SOLUTIONID', 'Field');
    taxSolutionIdHeader = taxSolutionIdHeader[0];
    var taxSolutionKeyHeader = window.editor.findComponents('TAXGROUP.TAXSOLUTIONKEY', 'Field');
    taxSolutionKeyHeader = taxSolutionKeyHeader[0];
    taxGroupNameHeader.updateProperty('readonly', fieldValue);
    taxSolutionIdHeader.updateProperty('readonly', fieldValue);
    if (fieldValue === true) {
        for (var i = 0; i < grid.value.length; i++) {
            var taxSolutionIdGrid = grid.findLineComponent('TAXGROUP.TAXSOLUTION.SOLUTIONID', i, 'Field');
            var taxgroupGrid = grid.findLineComponent('TAXGROUP.NAME', i, 'Field');
            var taxSolutionKeyGrid = grid.findLineComponent('TAXSOLUTIONKEY', i, 'Field');
            if (fromHandleEvent === true) {
                if (i == 0 && (taxSolutionIdGrid.value === undefined || taxSolutionIdGrid.value == '') && (taxgroupGrid.value === undefined || taxgroupGrid.value == '')) {
                    if (taxGroupNameHeader && taxGroupNameHeader.value && taxGroupNameHeader.value != '') {
                        taxgroupGrid.setValue(taxGroupNameHeader.value);
                    }
                    if (taxSolutionIdHeader && taxSolutionIdHeader.value && taxSolutionIdHeader.value != '') {
                        taxSolutionIdGrid.setValue(taxSolutionIdHeader.value);
                        var entityId = taxSolutionIdHeader.getValue();
                        var pickerValues = [];
                        if (entityId) {
                            pickerValues = taxSolutionIdHeader.findPickerObject(entityId);
                            if (pickerValues == null) {
                                pickerValues = taxSolutionIdHeader.findMatchingPickerObject(entityId);
                            }
                        }
                        updateItemTaxGroupPicker(taxgroupGrid, pickerValues);
                    }
                }
            } else {
                if (taxSolutionIdGrid && taxSolutionIdGrid.value && taxSolutionKeyGrid && taxSolutionKeyGrid.value) {
                    var taxSolutionRecordNo = [];
                    taxSolutionRecordNo.RECORDNO = taxSolutionKeyGrid.value;
                    updateItemTaxGroupPicker(taxgroupGrid, taxSolutionRecordNo);
                }
            }
        }
        taxGroupNameHeader.setValue("");
        taxSolutionIdHeader.setValue("");
        taxSolutionKeyHeader.setValue("");
    } else {
        if (fromHandleEvent === true) {
            var taxSolutionIdGrid = grid.findLineComponent('TAXGROUP.TAXSOLUTION.SOLUTIONID', 0, 'Field');
            var taxgroupGrid = grid.findLineComponent('TAXGROUP.NAME', 0, 'Field');
            if (taxSolutionIdGrid && taxSolutionIdGrid.value && taxSolutionIdGrid.value != '') {
                taxSolutionIdHeader.setValue(taxSolutionIdGrid.value);
                var entityId = taxSolutionIdGrid.getValue();
                var pickerValues = [];
                if (entityId) {
                    pickerValues = taxSolutionIdGrid.findPickerObject(entityId);
                    if (pickerValues == null) {
                        pickerValues = taxSolutionIdGrid.findMatchingPickerObject(entityId);
                    }
                }
                updateItemTaxGroupPicker(taxGroupNameHeader, pickerValues);
            }

            if (taxgroupGrid && taxgroupGrid.value && taxgroupGrid.value != '') {
                taxGroupNameHeader.setValue(taxgroupGrid.value);
            }
        } else {
            if (taxSolutionIdHeader && taxSolutionIdHeader.value && taxSolutionKeyHeader && taxSolutionKeyHeader.value) {
                var taxSolutionRecordNo = [];
                taxSolutionRecordNo.RECORDNO = taxSolutionKeyHeader.value;
                updateItemTaxGroupPicker(taxGroupNameHeader, taxSolutionRecordNo);
            }
        }
    }
    taxGroupNameHeader.redraw();
    taxSolutionIdHeader.redraw();
}

function onChangeTaxSolution(fieldMeta) {
    var entityId = fieldMeta.getValue();
    var pickerValues = [];
    if (entityId) {
        pickerValues = fieldMeta.findPickerObject(entityId);
        if (pickerValues == null) {
            pickerValues = fieldMeta.findMatchingPickerObject(entityId);
        }
    }
    var taxGroupName = window.editor.findComponents('TAXGROUP.NAME', 'Field');
    taxGroupName = taxGroupName[1];

    updateItemTaxGroupPicker(taxGroupName, pickerValues);
}

function onChangeTaxSolutionGrid(fieldMeta) {
    var entityId = fieldMeta.getValue();
    var lineNo = fieldMeta.getLineNo();
    var grid = fieldMeta.getGrid();
    for (var i = 0; i < grid.value.length; i++) {
        if (lineNo != i) {
            var taxSolnID = grid.findLineComponent('TAXGROUP.TAXSOLUTION.SOLUTIONID', i, 'Field');
            if (fieldMeta.value == taxSolnID.value) {
                taxSolnID = grid.findLineComponent('TAXGROUP.TAXSOLUTION.SOLUTIONID', lineNo, 'Field');
                taxSolnID.setValue("");
                alert(GT("IA.TAX_SOL_TAX_GROUP_MAPPING"));
                return;
            }
        }
    }
    // Get the picker object
    var pickerValues = [];
    if (entityId) {
        pickerValues = fieldMeta.findPickerObject(entityId);
        if (pickerValues == null) {
            pickerValues = fieldMeta.findMatchingPickerObject(entityId);
        }
    }
    var taxGroupName = grid.findLineComponent('TAXGROUP.NAME', lineNo, 'Field');
    updateItemTaxGroupPicker(taxGroupName, pickerValues);
}

function updateItemTaxGroupPicker(taxGroup, params) {
    if (taxGroup) {
        taxGroup.type.restrict = new Array();
        var pickerFilter=[];
        if (!params) {
            pickerFilter = ['0'];
        }
        else
            pickerFilter = [params.RECORDNO];
        taxGroup.type.restrict.push({'pickField': 'TAXSOLUTIONKEY', 'value': Object.values(pickerFilter)});
        taxGroup.redraw();
    }
}

function TaxGroupGrid() {
}

TaxGroupGrid.inheritsFrom(Grid);

TaxGroupGrid.prototype.deleteRow = function (atIndex) {
    Grid.prototype.deleteRow.call(this, atIndex);
    redrawTaxGridAfterDeleteRow();
};

function redrawTaxGridAfterDeleteRow() {
    var grid = window.editor.findComponents('MULTIPLEITEMTAXGROUPS', 'Grid')[0];
    for (var i = 0; i < grid.value.length; i++) {
        var taxSolutionIdGrid = grid.findLineComponent('TAXGROUP.TAXSOLUTION.SOLUTIONID', i, 'Field');
        var taxgroupGrid = grid.findLineComponent('TAXGROUP.NAME', i, 'Field');
        var taxSolutionKeyGrid = grid.findLineComponent('TAXSOLUTIONKEY', i, 'Field');
        var taxSolutionRecordNo = [];
        if (taxSolutionIdGrid && taxSolutionIdGrid.value && taxSolutionKeyGrid && taxSolutionKeyGrid.value) {
            taxSolutionRecordNo.RECORDNO = taxSolutionKeyGrid.value;
        }
        updateItemTaxGroupPicker(taxgroupGrid, taxSolutionRecordNo);
    }
}


/* ----------- Item Cross reference related ------------*/

function ItemCrossRefencesSection()
{
    // this.hasForm = false;
}

ItemCrossRefencesSection.inheritsFrom( Section );

ItemCrossRefencesSection.prototype.draw = function()
{
    return Section.prototype.draw.call(this);
};

function disableItemCrossrefGridFields(obj) {
    var refType = obj.meta.getValue();
    var rowNo = obj.meta.getLineNo();
    var grid = window.editor.view.findComponents('ITEMCROSSREFERENCES', 'Grid');
    grid = grid[0];

    grid.gatherData();

    var refTypeContext = (refType == 'Customer' || refType == 'Vendor') ? 'External' : 'Internal';
    var crossRefFields = {
        'VENDORID': {'disabled': true, 'required': false},
        'CUSTOMERID': {'disabled': true, 'required': false},
        'ITEMALIASID': {'disabled': true, 'required': false},
        'ITEMALIASDESC': {'disabled': true, 'required': false},
        'UNIT': {'disabled': false, 'required': false},
        'ALTERNATEITEMID': {'disabled': true, 'required': false}
    };

    var fldObj;
    var fldID;
    for (fldID in crossRefFields) {
        fldObj = grid.findLineComponent(fldID, rowNo, 'Field');
        //var fldObj = grid.findLineComponent(fldID, rowNo, 'Field');
        if (fldObj) {
            fldObj.setValue(null);
            fldObj.updateProperty('disabled', crossRefFields[fldID].disabled, true);
            fldObj.updateMetadata();
        }
    }

    switch (refType) {
        case 'Customer':
            crossRefFields.CUSTOMERID.disabled = false;
            break;

        case 'Vendor':
            crossRefFields.VENDORID.disabled = false;
            break;

        case 'Substitute':
        case 'Upgrade':
        case 'Downgrade':
        case 'Complement':
            crossRefFields.ALTERNATEITEMID.disabled = false;
            break;
    }

    crossRefFields.ITEMALIASID.disabled = (refTypeContext == 'Internal');
    crossRefFields.ITEMALIASDESC.disabled = (refTypeContext == 'Internal');

    for (fldID in crossRefFields) {
        fldObj = grid.findLineComponent(fldID, rowNo, 'Field');
        if (fldObj) {
            fldObj.setValue(null);
            fldObj.updateProperty('disabled', crossRefFields[fldID].disabled, true);
            fldObj.redraw();
        }
    }
}

function ItemCrossRefGridField() {
}

ItemCrossRefGridField.inheritsFrom(Field);

ItemCrossRefGridField.prototype.draw = function () {
    if (!window.view.readonly) {

        var editMode = false;
        if (this.parentValue.RECORDNO != undefined) {
            editMode = true;
        }

        var refTypeContest = (this.parentValue.REFTYPE == 'Customer' || this.parentValue.REFTYPE == 'Vendor')
            ? 'External' : 'Internal';
        var disableField = false;

        switch (this.path) {
            case 'REFTYPE':
                if(this.value == undefined) {
                    var defaultValue = mod == 'po' ? 'Vendor' : 'Customer';
                    // the following line is to fix the case: 4294199: Smart Rule Warning not working
                    // defect ticket: 126386: "Smart Rule Warning not working
                    this.parentValue.REFTYPE = defaultValue;
                    this.setValue(defaultValue);
                } else {
                    disableField = (editMode || disableField);
                }
                break;

            case 'VENDORID':
                if (this.parentValue.REFTYPE != 'Vendor') {
                    disableField = true;
                }
                disableField = (editMode || disableField);
                break;

            case 'CUSTOMERID':
                if (this.parentValue.REFTYPE != 'Customer') {
                    disableField = true;
                }
                disableField = (editMode || disableField);
                break;

            case 'ALTERNATEITEMID':
                if (refTypeContest == 'External') {
                    disableField = true;
                }
                break;

            case 'ITEMALIASID':
            case 'ITEMALIASDESC':
                if (refTypeContest == 'Internal') {
                    disableField = true;
                }
                break;

            case 'UNIT':
                if (uomCache) {
                    var uoms = [];
                    if (this.parentValue.ALTERNATEITEMID && this.parentValue.ALTERNATEITEMID != '') {
                        var alternateItemID = this.parentValue.ALTERNATEITEMID.split("--")[0];
                        if (uomCache[alternateItemID]) {
                            uoms = uomCache[alternateItemID];
                        }
                    } else {
                        var itemid = window.editor.view.getFieldValue('ITEMID');
                        if (uomCache[itemid]) {
                            uoms = uomCache[itemid];
                        }
                    }
                    this.type.validlabels = uoms;
                    this.type.validvalues = uoms;
                    this.updateMetadata();
                }
                break;
        }
        this.disabled = disableField;
    }

    return Field.prototype.draw.call(this);
};


function populateAlternateItemUOM(obj)
{
    var itemID = obj.value;

    //return if we have nothing to populate
    if (!itemID || itemID === '') {
        return true;
    }

    itemID = itemID.split('--')[0];
    var rowNo = obj.meta.getLineNo();

    // first check in the UOM cache
    var uoms = uomCache[itemID];
    if (uoms && uoms.length > 0) {
        var grid = window.editor.view.findComponents('ITEMCROSSREFERENCES', 'Grid');
        grid = grid[0];
        if (grid) {
            grid.gatherData();
            var unitField = grid.findLineComponent('UNIT', rowNo, 'Field');
            if (unitField) {
                unitField.setValue('');
                unitField.type.validlabels = uoms;
                unitField.type.validvalues = uoms;
                unitField.updateMetadata();
                return;
            }
        }
    }

    // if the UOM values doesnt exist in UOM cache then fetch the UOM details
    var qrequest = new QRequest;
    var url = 'qrequest.phtml?.function=FetchItemUOMInfo&.handler=QRequest&.entity=itemcrossref&.sess='+sess
        +'&.otherparams=itemid'
        +'&.itemid='+encodeURIComponent(itemID);
    var updateFunc = "RespProcesser_AlternateItemUOM";
    var updateArgs = "'" + rowNo + "'";
    qrequest.quickRequest(url, updateFunc, updateArgs, false);
    return true;
}

function RespProcesser_AlternateItemUOM(rowNo, m_response)
{
    // Perform the validation upfront.
    if (!m_response) {
        window.editor.hideLoadingBar();
        return false;
    }

    var uomdetails = m_response.getElementsByTagName("uomdetail");
    if (uomdetails) {
        var uomlist = uomdetails.item(0).getElementsByTagName("uom");

        if (uomlist) {
            var newValues = new Array();
            for (var i = 0; i < uomlist.length; i++) {
                newValues[i] = uomlist.item(i).getAttribute("unit");
            }

            var grid = window.editor.view.findComponents('ITEMCROSSREFERENCES', 'Grid');
            grid = grid[0];
            if (grid) {
                grid.gatherData();
                var unitField = grid.findLineComponent('UNIT', rowNo, 'Field');
                var inetrnalItemIdField = grid.findLineComponent('ALTERNATEITEMID', rowNo, 'Field');

                if (unitField) {
                    unitField.setValue('');
                    unitField.type.validlabels = newValues;
                    unitField.type.validvalues = newValues;
                    unitField.updateMetadata();
                }

                if (inetrnalItemIdField && inetrnalItemIdField.value != '') {
                    var alternateItemID = inetrnalItemIdField.value.split("--")[0];
                    uomCache[alternateItemID] = newValues;
                }
            }
        }
    }

    return true;
}
/* ----------- Item Cross reference related ------------*/

function ItemReportLink(obj, reporttype) {

    var itemId = obj.meta.parentValue['ITEMID'];
    itemId = itemId.split('--')[0];

    if (itemId == null || itemId == '') {
        alert(GT("IA.SELECT_AN_ITEM"));
        return false;
    }

    var title = '';
    var url = '';
    switch (reporttype) {
        case 'physicalinventory':
            title = 'Physical inventory';
            url = 'reporteditor.phtml?.sess=' + sess + '&.op=' + itemsphysicalinventoryop + '&.type=_html&.drillfilter=1&.popup=1'
                + '&FROMITEMID=' + encodeURIComponent(itemId)
                + '&TOITEMID=' + encodeURIComponent(itemId)
                + '&APPLYLOCATIONRESTRICTION=false'
                + '&_obj__REPORTGROUPINGS=Warehouse&_obj__REPORTON=ITEMID';
            break;
        case 'invstatus':
            url = 'reporteditor.phtml?.sess=' + sess + '&.op=' + itemstatusop + '&.type=_html&.drillfilter=1&.popup=1'
                + '&APPLYLOCATIONRESTRICTION=false'
                + '&FROMITEMID=' + encodeURIComponent(itemId)
                + '&TOITEMID=' + encodeURIComponent(itemId);
            title = 'Inventory status';
            break;
        case 'itemactivity':
            url = 'reporteditor.phtml?.type=_html&.sess=' + sess + '&.op=' + itemactivityop + '&.type=_html&.popup=1'
                + '&_obj__REPORTON=ITEMID'
                + '&APPLYLOCATIONRESTRICTION=false'
                + '&FROMITEMID=' + encodeURIComponent(itemId)
                + '&TOITEMID=' + encodeURIComponent(itemId);
            title = 'Item activity';
            break;
    }

    Launch(url, title, '800', '200');
    return false;
}

function WarnOnDisablingPreferences(Obj) {
    var fieldValue = Obj.checked;
    if (fieldValue == false) {
        return true;
    }

    var prefPath = Obj.meta.path;
    var descArr = [];

    descArr['ENABLE_BINS'] = GT("IA.CANNOT_DISABLE_TRACKING_FEATURE_BIN");
    descArr['ENABLE_LOT_CATEGORY'] =  GT("IA.CANNOT_DISABLE_TRACKING_FEATURE_LOT");
    descArr['ENABLE_SERIALNO'] = GT("IA.CANNOT_DISABLE_TRACKING_FEATURE_SERIALNO");
    descArr['ENABLE_EXPIRATION'] =GT("IA.CANNOT_DISABLE_TRACKING_FEATURE_EXPIRATION");
    descArr['ENABLELANDEDCOST'] = GT("IA.CANNOT_DISABLE_LANDING_COST_FEATURE");

    var userConformation = confirm(descArr[prefPath]);
    var ObjField = window.editor.view.findComponents(prefPath)[0];
    if (userConformation == false) {
        ObjField.setValue(userConformation);
    }
    ObjField.redraw();

    return userConformation;
}

function processLandedCost(Obj) {
    if (WarnOnDisablingPreferences(Obj)) {
        ShowHideLandedCost(Obj);
    }
}



/* ----------- Transactions tab related ------------*/

/**
 * To Open Transaction Tab it check for transaction type
 * @param fieldMeta
 * @param drillKey
 * @param drillType
 * @returns {boolean}
 */
function drilldownAndShowForDocumentID(fieldMeta, drillKey, drillType){
    if (!fieldMeta) {
        return false;
    }
    var rowData = fieldMeta.parentValue;
    var transactionType = rowData['TRANSACTIONTYPE'];

    switch (transactionType){
        case 'I':
            drillType = 'invdoc';
            break;
        case 'S':
            drillType = 'sodoc';
            break;
        case 'P':
            drillType = 'podoc';
            break;
        default:
            drillType = drillType;
    }
    drilldownAndShow(fieldMeta, drillKey, drillType);
}
/**
 * go to a related object using the key and type
 * @param fieldMeta
 * @param drillKey
 * @param drillType
 * @returns {boolean}
 */
function drilldownAndShow(fieldMeta, drillKey, drillType){
    if (!fieldMeta) {
        return false;
    }
    var rowData = fieldMeta.parentValue;
    var recordhash = rowData[drillKey];
    // if record hash is null just dont process the request
    if(recordhash == null) {
        return false;
    }
        var $drillTypeMap = {
            "customer": view_customer,
            "vendor": view_vendor,
            "contact": view_contact,
            "warehouse": view_warehouse,
            "sodoc": view_sodocument,
            "podoc": view_podocument,
            "invdoc": view_invdocument,
        };

        var opid = (typeof $drillTypeMap[drillType] !== "undefined") ? $drillTypeMap[drillType] : -1;

        if (opid != -1) {
            var scripturl = "editor.phtml?.do=view&.r=" + escape(recordhash) + "&.op=" + opid + "&.popup=1&.viewsonly=1&.sess=" + sess;
            ShowDialog(scripturl, 800, 600);
        }
    return true;
}

function beforeApplyFilters(params){
    var itemid = window.editor.view.getFieldValue('ITEMID');
    if (itemid.length != 0) {

        var keyIndex = 0;
        if (typeof params.queryParams.filters === "undefined") {
            params.queryParams.filters = [];
        }

        keyIndex = params.queryParams.filters.length;
        params.queryParams.filters.push({
            key: ++keyIndex,
            operation: 'equals',
            path: 'ITEMID',
            value: itemid
        });

        if (params.queryParams.filters.length > 1 && params.queryParams.expression !== 'and') {
            params.queryParams.expression = 'and';
        }
    }

    // delete expression if no filter set is there
    if (typeof params.queryParams.filters !== "undefined" && params.queryParams.filters.length < 2) {
        delete params.queryParams.expression;
    }
}
/**
 * Method to define pre filter set on filter component for Inquiry - Sales Tab
 * @param params
 */
function beforeApplyFiltersInquirySales(params) {
    var keyIndex = 0;
    if (typeof params.queryParams.filters === "undefined") {
        params.queryParams.filters = [];
    }

    keyIndex = params.queryParams.filters.length;
    params.queryParams.filters.push({
        key: ++keyIndex,
        operation: 'not equal to',
        path: 'SOTRANSONHOLD',
        value: 0
    });

    if (params.queryParams.filters.length > 1 && params.queryParams.expression !== 'and') {
        params.queryParams.expression = 'and';
    }

    // filtering for specific item
    beforeApplyFilters(params);
}

function OnSelectionOfhold(){

}

/**
 * Method to define pre filter set on filter component for Inquiry - Purchase Tab
 * @param params
 */
function beforeApplyFiltersInquiryPurchase(params) {
    var keyIndex = 0;
    if (typeof params.queryParams.filters === "undefined") {
        params.queryParams.filters = [];
    }

    keyIndex = params.queryParams.filters.length;
    params.queryParams.filters.push({
        key: ++keyIndex,
        operation: 'not equal to',
        path: 'POTRANSONORDER',
        value: 0
    });

    if (params.queryParams.filters.length > 1 && params.queryParams.expression !== 'and') {
        params.queryParams.expression = 'and';
    }

    // filtering for specific item
    beforeApplyFilters(params);
}

/**
 * Method to define pre filter set on filter component for Inquiry - In Transit Tab
 * @param params
 */
function beforeApplyFiltersInquiryInTransit(params) {
    // filtering for specific item
    beforeApplyFilters(params);
}
