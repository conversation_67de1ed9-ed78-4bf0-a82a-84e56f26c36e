/**
 * qxcustomrw.styl -- File to restyle Custom Report Wizard for quixote
 *
 * THIS FILE OVERWRITE STANDARD CSS RULES, NO REUSE OF THIS FILE
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation -- All Rights Reserved.
 */

.quixote {
  &.qx-custom-rpw {
    regular_font(14px)
    color col-crep-font-color

    & a {
      margin 0 2px
      & label {
        text-decoration none
        color col-intacct-link-color
        cursor pointer
      }
      &.btn {
        text-decoration none
      }
    }

    & input[type="radio"] {
      font-size
      & + label {
        margin-left 5px
      }
    }

    & .leaf_column_header {
      height 30px
      & th:not(:first-of-type) {
        text-align center
      }
    }
    & .qx-crep-button-container {
      width auto
      padding 5px
      & input[type="button"] {
        display inline-block
        float left
        border 0
        padding 5px
        width auto !important
        color col-link-text-color

        &:hover,
        &:focus {
          color col-link-text-highlight-color
          outline 0
        }
      }
    }
    & .qx-crep-btn-up,
    & .qx-crep-btn-down,
    & .qx-crep-btn-top,
    & .qx-crep-btn-bottom {
      border-radius(radius-all-elements)
      font-size 12px
      padding 0px 4px
      margin 1px 4px 0 2px
      display block
      color col-primary-button-color
      background-color col-primary-button-background
      border 1px solid col-primary-button-border-color

      &:hover,
      &:focus,
      &.focus,
      &:active,
      &.active {
        color col-primary-button-hover-color
        background-color col-primary-button-hover-background
        border-color col-primary-button-hover-border-color
      }

      & img {
        display none
      }
    }

    & .qx-crep-btn-up {
      &:after {
        content "\f0d8"
        font-family 'Font Awesome 5 Pro'
      }
    }

    & .qx-crep-btn-down {
      &:after {
        content "\f0d7"
        font-family 'Font Awesome 5 Pro'
      }
    }

    & .qx-crep-btn-top {
      &:after {
        content "\f102"
        font-family 'Font Awesome 5 Pro'
      }
    }

    & .qx-crep-btn-bottom {
      &:after {
        content "\f103"
        font-family 'Font Awesome 5 Pro'
      }
    }

    & .trigger {
      padding-left 20px
      position relative
      display block

      &::before {
        font-family 'Font Awesome 5 Pro'
        font-size 18px
        font-weight bold
        display block
        position absolute
        top -2px
        left 10px
      }

      & img {
        display none
      }

      &.qx-crep-trigger-down {
        &::before {
          content "\f0d7"
          top -3px
          left 6px
        }
      }

      &.qx-crep-trigger-right {
        &::before {
          content "\f0da"
        }
      }
    }

    & .draglist {
      border 0
      display block
      padding 5px
      & .list {
        margin 2px 0
        & table {
          & tr {
            border 0 !important
            background col-crep-group-background
            display block
            padding 5px
            & .listlab {
              display block
              width 350px
            }
          }
        }
      }
    }

    & .form_required {
      margin-left 15px
    }
    & .node_name {
      regular_font(12px)
    }

    & .form-group {
      margin-left 15px

      & .checkbox {
        padding-left 20px
      }
    }
    & .checkbox {
      padding 0
      & input[type="checkbox"].standAloneCheckBox {
        opacity 1
        transform none
        position initial
        margin 0 5px
      }
    }
    // DE8857 - Align properly the inline radio buttons to avoid overlapping
    & .qx-radio {
      padding-left 0px
      & .radio-inline {
        padding-left 20px
        margin-top 6px
      }
    }
    & a#prevbuttid,
    & a#nextbuttid {
      regular_font(12px)
    }

    & a#prevbuttid {
      & img {
        display none
      }
      &:before {
        content "\f100"
        font-family 'Font Awesome 5 Pro'
      }
    }

    & a#nextbuttid {
      & img {
        display none
      }
      &:after {
        content "\f101"
        font-family 'Font Awesome 5 Pro'
      }
    }

    & #runbuttid,
    & #exportbuttid {
      line-height 1.33333
      padding 2px 15px 4px 15px
      margin 0
    }

    .wizard_page {
      background col-co-container-background
      border 1px solid col-co-container-border
      width 100%
      display block
      border-radius(radius-section)
      padding 0 0 20px 0
      margin-bottom 20px

      & fieldset {
        border 1px solid col-crep-border-color
        position relative
        padding 15px 0 0 0

        & legend {
          position absolute
          background none
          border 0
          & img {
            position absolute
            top -20px
            right 0px
            background #fff
            padding 0 5px
          }
        }
      }

      & br {
        display none
      }

      & .wizard_title {
        height 52px
        border-bottom 1px solid col-frw-header-border
        position fixed
        z-index 2
        top 0
        padding 5px 20px
        margin-left -15px

        & td {
          background-color col-frw-header-background
          & .ia-list-breadcrumb {
            display none
          }

          &:first-child {
            padding-left 15px
          }

          &:last-child {
            padding-right 15px
          }
        }
        & .qx-gotolist-container {
          display inline-block
          transform translateY(-3px)
        }
        & .wizard_header_title {
          regular_font(26px)
          color col-header-title-color
          line-height 1.15
          white-space nowrap
        }
      }

      & .wizard_controls_table {
        & .wizard_field_list_data {
          width 98%
          margin 15px 1%

          & tr.qx-crep-tr-spacing {
            display none
          }

          & .qx-crep-selection-group {
            display block
            float right
            margin-right 15px
          }

          & .node_name {
            height 30px
            background col-crep-group-background
            margin-top 5px
            & .trigger {
              cursor pointer
              & label {
                cursor pointer
              }
            }
          }
          & .branch {
            padding 5px
            width auto !important
            & table {
              margin 2px
            }

            & input[type="text"] {
              padding 4px
              margin 0 2px
              border-radius radius-input !important
            }
          }

          & td.label_cell {
            width 270px
            padding 5px
            text-align right
            white-space nowrap
            font-size 11px
            font-family Arial, sans-serif
            font-weight bold
            margin 4px
          }
          & .dropdown-toggle {
            border-radius radius-input !important
          }
        }
        regular_font(12px)
        & .wizard_instruction {
          regular_font(18px)
          display block
        }
        & .btn-group.bootstrap-select {
          max-width 350px
        }
        & select {
          padding 3px
          background col-co-select-background
          margin 2px
          border-radius radius-input
          &:focus-visible {
            outline-color none
          }

          &:not([size]) {
            height 24px
          }
        }
      }
    }

    & .yui-panel {
      background #FFF
      border-radius 4px
      border 2px solid #3F5986
    }

    & .yui-overlay {
      & .hd, & .hdl, & .hdr {
        height 32px
      }
      & .hd {
        & h1 {
          font-size 14px
          font-weight 700
          font-family "Lucida Grande", "Lucida Sans Unicode", Verdana, Arial, Helvetica, sans-serif
          margin 0px
          padding 8px 15px
          color #FFF
        }
      }

      & .bd,
      & .bdl,
      & .ft,
      & .ft div,
      & .hdl {
        background none
      }

      & .qx-operator {
        margin 2px
        padding 2px 0
      }

      & .container-close {
        &:before {
          font-family 'Font Awesome 5 Pro'
          font-size inherit
          content "\f00d"
          position absolute
          top 0
          left 10000em
          color #FFF
        }
      }

      & .hd {
        margin 3px
      }

      & .hdr {
        background #2e4a69
        border-radius 4px
      }

      & .btn-primary {
        width auto !important
      }
    }
  }
  //override drop-down size for this custom implementation
  & .wizard_fieldsonly_list_data {
    & .input-group {
      width 158px !important
    }
  }
}
