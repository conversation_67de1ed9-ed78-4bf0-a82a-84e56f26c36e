/**
 * qxstate.js
 *
 * Saves UI state for Quixote.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation -- All Rights Reserved
 */

var QXState = window.QXState ||
{
    _worker: null,

    /**
     * Start qxqueue web worker
     *
     * @param csrfToken token for web worker to use.
     */
    initialize: function(csrfToken, session, path)
    {
        this._worker = new Worker('../resources/qx/js/qxqueue.js');
        if (this._worker) {
            this._worker.onmessage = this.onMessage;
            var msg = {cmd: 'start', token: csrfToken, session: session, path: path};
            this._worker.postMessage(msg);
        }
    },
    /**
     * Save key/value pair. If a web worker is available, post it to the
     * worker. Otherwise, post to server immediately.
     *
     * @param key   key for value
     * @param value value to save
     */
    save: function(key, value)
    {
        if (this._worker) {
            this._worker.postMessage({cmd: 'save', key: key, value: value});
        } else {
            QXUtil.postKV(key, value, null, null);
        }
    },
    /**
     * Instruct worker to flush any pending UI state to server.
     *
     */
    flush: function()
    {
        if (this._worker) {
            this._worker.postMessage({cmd: 'flush'});
        }
    },

    /**
     * Handle errors posted by worker.
     *
     * TODO This needs love from doc team including instructions.
     *
     * TODO how can user recover?
     *
     * @param e event data
     */
    onMessage: function(e)
    {
        try {
            var text = '';

            switch (e.data.errorType) {
                case 'apperror':
                    text = e.data.errors[0]['message'];
                    break;
                case 'error':
                    switch(e.data.status) {
                        case 0:
                            text = GT('IA.OFFLINE_PLEASE_CHECK_YOUR_CONNECTION');
                            break;
                        case 404:
                            text = GT('IA.REQUESTED_URL_NOT_FOUND');
                            break;
                        case 500:
                            text = GT('IA.INTERNAL_SERVER_ERROR');
                            break;
                        default:
                            text = e.data.responseText;
                    }
                    break;
                case 'timeout':
                    text = GT("IA.REQUEST_TIMED_OUT");
                    break;
                default:
                    text = GT("IA.ERROR_OCCURED_WHILE_SAVING_UI_STATE");
            }
            // TODO switch QXDialog.
            alert(text);
        } catch (ignored) {
            // Nothing user can do something about.
        }
    }
};
