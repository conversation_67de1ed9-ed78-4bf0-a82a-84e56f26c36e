/*
 * QUIXOTE DIALOG
 * replacement for <PERSON><PERSON> and jQ<PERSON>y dialogs
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation -- All Rights Reserved.
 */

;(function ($, window, document, undefined) {
    "use strict";

    var qxDialog = 'qxdialog',
        textValues = {
            labelBtnMaximize: 'IA.MAXIMIZE',
            labelBtnMinimize: 'IA.MINIMIZE',
            labelBtnClose: 'IA.CLOSE',
            labelBtnOpenWindow: 'IA.OPEN_IN_A_BROWSER_WINDOW',
            btnTextSave: 'IA.SAVE',
            btnTextCancel: 'IA.CANCEL',
            confirmText: 'IA.CHANGES_YOU_MADE_MAY_NOT_BE_SAVED'
        },
        defaults = {
            dialogTitle: 'Title',
            dialogContent: 'Content',
            dialogButtons: ['all', 'save', 'cancel', 'close'],
            configType: ['help', 'form', 'custom', 'other'],
            customSizes: ['small', 'medium', 'extralarge'],
            buildMode: null,
            type: ['picker','other','html'],
            parentDiv: null,
            buttons: '',
            footerCloseBtn: false,
            showFooter: false,
            maximized: false,
            topButtons: false,
            modalsize: '',
            preventAutoHideLineDetails: false,
            limitHelpDialogs: 5,
            typeOfModalsLimited: ['help'],
            popupWidth: '1080',
            popupHeight: '800',
            promptDirty: true,
            layout: {
                modContainer: '<div class="quixote-modal modal qxdialog fade" id="qxmodalid" tabindex="-1" role="dialog" aria-labelledby="qxmodaltitleid"></div>',
                modDialog: '<div class="modal-dialog center" role="document"></div>',
                mCnt: '<div class="modal-content"></div>',
                mCntHeader: '<div class="modal-header"></div>',
                mCntHeaderTitle: '<div class="modal-title" id="qxmodaltitleid"></div>',
                mCntHeaderSize: '<button type="button" class="close qx-mod-size" aria-label="'+ GT(textValues.labelBtnMaximize) +'" title="'+ GT(textValues.labelBtnMaximize) +'"><span aria-hidden="true"><i class="qx-icon fal fa-expand-alt" title="'+ GT(textValues.labelBtnMaximize) +'" data-iatoken="'+ textValues.labelBtnMaximize +'"></i></span></button>',
                mCntHeaderPopOut: '<button type="button" class="close qx-mod-pop-open" aria-label="'+ GT(textValues.labelBtnOpenWindow) +'" title="'+ GT(textValues.labelBtnOpenWindow) +'"><span aria-hidden="true"><i class="qx-icon fal fa-external-link-alt" title="'+ GT(textValues.labelBtnOpenWindow) +'" data-iatoken="'+ textValues.labelBtnOpenWindow +'"></i></span></button>',
                mCntHeaderClose: '<button type="button" class="close qx-mod-close" data-dismiss="modal" aria-label="'+ GT(textValues.labelBtnClose) +'" title="'+ GT(textValues.labelBtnClose) +'"><span aria-hidden="true"><i class="qx-icon fal fa-times" title="'+ GT(textValues.labelBtnClose) +'" data-iatoken="'+ textValues.labelBtnClose +'"></i></span></button>',
                mCntHeaderLoading:'<div class="qx-modal-loading"></div>',
                mCntHeaderButtons:'<div class="qx-modal-header-btns"></div>',
                mCntBody: '<div class="modal-body"></div>',
                mCntFooter: '<div class="modal-footer"></div>',
                footerButtons: {
                    save: '<button id="qxmodbtnsave" class="btn btn-sm btn-primary" data-iatoken="'+ textValues.btnTextSave +'">'+ GT(textValues.btnTextSave) +'</button>',
                    cancel: '<button id="qxmodbtncancel" class="btn btn-sm btn-secondary" data-iatoken="'+ textValues.btnTextCancel +'">'+ GT(textValues.btnTextCancel) +'</button>',
                    close: '<button id="qxmodbtnclose" class="btn btn-sm btn-secondary" data-dismiss="modal" aria-label="'+ textValues.labelBtnClose +'" data-iatoken="'+ textValues.labelBtnClose +'">'+ GT(textValues.labelBtnClose) +'</button>'
                }
            },

        };

    function QXDialogConstructor(element, options) {
        this.element = element;

        this.settings = $.extend({}, defaults, options);
        this._defaults = defaults;
        this._textValues = textValues;
        this._name = qxDialog;
        this.init();
    }

    $.extend(QXDialogConstructor.prototype, {
        init: function() {
            this.qxmodal();
        },

        qxmodal: function(dataValues) {
            var rnd5Digits = Math.floor(Math.random()*9000) + 1000;
            var showLoading = false;
            this.$idUnique = 'qx-diag-'+rnd5Digits;
            var $parent = $(parent.document.body);
            //use same 'page' to add modal, so it cover the other one, we add 'qx-has-qxmodal' to can detect it
            //$tmpEl is the actual page from where is opened the modal, where we add a empty div to can detect that we opened a modal from there
            if ($parent.hasClass('qx-has-qxmodal')) {
                this.$initEl = $parent;
                this.$tmpEl = $(this.element);
            } else {
                this.$initEl = $(this.element);
                this.$tmpEl = null;
            }

            this.removeClosedModals();
            this.$buildMode = null;
            this.$dataConfig = this.getValueOfConfig('configType');
            this.$dataContentType = this.getValueOfConfig('type');
            this.$buildMode = this.getValueOfConfig('buildMode');
            this.$diagButtons = this.getDialogData('buttons');
            this.$customProp = this.getDialogData('customProp');
            this.$showFooter = this.settings.showFooter;
            this.$showfooterCloseBtn = this.settings.footerCloseBtn;
            this.$maximized = this.settings.maximized;
            this.$topButtons = this.settings.topButtons;
            this.$modalsize = '';
            this.isFloatingPage = false;
            this.isSecondaryFP = false;

            if (typeof dataValues === 'object') {
                this.$dataConfig = dataValues.configType;
                this.$dataContentType = dataValues.type;
                this.$buildMode = dataValues.buildMode;
                this.$diagButtons = dataValues.buttons;
                if (dataValues.customProp) this.$customProp = dataValues.customProp;
            }

            if (this.$customProp && typeof this.$customProp === 'object' && this.$customProp.modalsize) {
                this.$modalsize = this.$customProp.modalsize;
            }

            if (this.$customProp && typeof this.$customProp === 'object' && this.$customProp.preventAutoHideLineDetails) {
                this.$preventAutoHideLineDetails = this.$customProp.preventAutoHideLineDetails;
            }

            if (this.$customProp && typeof this.$customProp === 'object') {
                this.$popoutWidth = this.$customProp.popoutWidth || defaults.popupWidth;
                this.$popoutHeight = this.$customProp.popoutHeight || defaults.popupHeight;
                this.$promptDirty = this.$customProp.promptDirty || defaults.promptDirty;
            }

            if (this.$dataConfig === 'help' || this.$dataConfig === 'custom' || this.$dataContentType === 'html') {
                this.$showFooter = true;
            }
            if (this.$dataConfig === 'help' || this.$dataConfig === 'custom') {
                this.$showfooterCloseBtn = true;
            }
            this.hasOpenModal = this.checkForOpenedModals();
            this.isOpenedInIE = this.checkIfModalOpenedInIE();

            if (this.$buildMode === 'wrap' || this.$buildMode === 'copy') {
                this.$showFooter = true;
                this.isFloatingPage = true;
                this.buildDialogWrapper(dataValues);
            } else {
                this.buildDialog(dataValues);
            }

            //floating page should be opened always in same context, not outside like form modals
            if (this.isFloatingPage) {
                this.$initEl = $(this.element);
                this.$tmpEl = null;
            }

            var totalModalsOpen = $('.qx-modal-form.in',this.$initEl).length;
            if (this.$dataConfig !== 'help' && this.$dataConfig !== 'custom') {
                this.$container.addClass('qx-mod-nr-'+totalModalsOpen);
                //add last active class, to know which one is last/active
                if (!this.isFloatingPage) {
                    $('.qxdialog.in:not(.qx-modal-help)',this.$initEl).removeClass('qx-modal-last');
                    this.$container.addClass('qx-modal-last');
                }
            }

            this.$initEl.addClass('qx-has-qxmodal');
            if (this.$buildMode !== 'wrap') {
                this.$initEl.append(this.$container);
            }
            if (this.$tmpEl) {
                this.$tmpEl.append('<div class="qxdialog"></div>')
            }

            if (this.$dataConfig === 'form') showLoading = true;


            var $qxmodal = $('#'+this.$idUnique, this.$initEl);

            //DE571 - fix the focus on modal for firefox so the dropdowns inside to work
            var enforceModalFocusFn = $.fn.modal.Constructor.prototype.enforceFocus;
            $.fn.modal.Constructor.prototype.enforceFocus = function() {};
            $qxmodal.on('hidden', function() {
                $.fn.modal.Constructor.prototype.enforceFocus = enforceModalFocusFn;
            });
            this.adjustElementszIndex($qxmodal);

            if (!this.limitModalsOpened(this.$dataConfig)) {
                $qxmodal.modal('show');
            } else {
                //todo: alert user
            }
            //handle backdrops when there are more modals opened and after modal is shown
            if (this.$tmpEl) {
                this.handleBackdropForMultipleModalsOpened($qxmodal);
            }
            if (!$qxmodal.length) {
                //for cases when floating page modal is generated inside an iframe
                $qxmodal = $('#'+this.$idUnique);
                $qxmodal.modal('show');
                $('.qx-mod-size').on('click',$.proxy(function() {
                    $('#'+this.$idUnique).find('.modal-content').toggleClass('qx-mod-fullscreen');
                },this));
                showLoading = false;
            }

            //if it's floating page, we look for error in parent (not only maximized floating pages)
            if (this.isFloatingPage && !this.isSecondaryFP) {
                var $fsModal = $qxmodal.find('.modal-content');
                if ($fsModal.length) {
                    this.checkForErrorMessageInParent($fsModal);
                }
            }

            //help dialog should be draggable and not modals, so we have to remove the backdrop
            if (this.$dataConfig === 'help') {
                this.changeHelpModal($qxmodal);
            }

            $qxmodal.on('hidden.bs.modal', $.proxy(function() {
                //fix for IE dirty problem, remove iframe from a closed modal,
                // so IE doesnt detect that form activity
                $('.qxdialog:not(.in)',this.$initEl).find('iframe').remove();
                var spto = 0;
                //if it's floating page, we have to trigger closing events
                if (this.isFloatingPage) {
                    this.hideFPGridLineDetails(); //hide lineDetails and switch on close, because it remains visible
                    spto = 300; //0.3s delay for spinner on floating pages, to actually lock user before closing
                    var fpdiv = $qxmodal.find('.qx-floating-page>div:first');
                    if (fpdiv.length) {
                        var fpID = fpdiv.attr('id');
                        if (fpID) {
                            try {
                                window.editor.pageCloseEvents(fpID);
                                if (window.editor.view && window.editor.view.floatingPages) {
                                    var floatingPage = window.editor.view.floatingPages[fpID] || {};
                                    if (floatingPage.page && floatingPage.page.destroyOnClose) {
                                        floatingPage.page.destroy(false);
                                        window.editor.view.value[floatingPage.page.path] = jq.extend(true, {}, window.editor.view.initialData[floatingPage.page.path]);
                                        var grids = typeof floatingPage.page.findComponents === 'function' ? floatingPage.page.findComponents('', 'Grid') : [];
                                        if (grids.length) {
                                            var index = 0;
                                            for (index; index < grids.length; index += 1) {
                                                //set linde details
                                                grids[index].clear({
                                                    data: true,
                                                    noRedraw: true,
                                                    filters: true,
                                                    select: true,
                                                    sorting: true,
                                                    lineDetails: true
                                                });
                                            }
                                        }
                                        delete window.editor.view.initialData[floatingPage.page.path];
                                    }
                                }
                            } catch(err) {
                                console.log('QXDIAG Err: Cannot close editor FP: '+err.message);
                            }
                        }
                    }
                    this.clearMutationObserver();
                }
                handlePaceActive(spto);
                $('body').removeClass('qx-modal-help-open');
                this.removeBackdropByClass(this.$idUnique);
            },this));

            $qxmodal.on('shown.bs.modal', $.proxy(function() {
                $qxmodal.find('.form-control:enabled:visible:first').focus();
            },this));

            $qxmodal.on('hide.bs.modal', $.proxy(function(ev) {
                if (ev) {
                    var $evTarget = $(ev.target);
                    // DE17952: $evTarget.find('i') returned an array on which find does not work, therefore
                    // search for the exact i element using the class
                    var $evTargetButtons = $evTarget.find('i.fa-external-link-alt');
                    var evTgHasClass = $evTargetButtons.hasClass('fa-external-link-alt-clicked');

                    // if one grid contains the class qx-grid-editMode then we should exit the edit mode
                    var $gridElement = $evTarget.find('.qx-floating-page .qx-grid-editMode');
                    if (this.$dataContentType === 'floating-page' && $gridElement.length) {
                        window.editor.exitEditPageMode(false);
                    }

                    if (this.$promptDirty && !this.isFloatingPage && !this.isSecondaryFP && evTgHasClass) {
                        var $currentDialogIframe = $(this.$mCntBody[0]).find('iframe')[0];
                        if ($currentDialogIframe
                            && $currentDialogIframe.contentWindow.editor
                            && $currentDialogIframe.contentWindow.editor.dataChanged
                            && window.warnOnChange === "1") {
                            if (!window.confirm(GT(this._textValues.confirmText))) {
                                $evTargetButtons.removeClass('fa-external-link-alt-clicked');
                                ev.preventDefault();
                                ev.stopImmediatePropagation();
                                return false;
                            }
                        }
                        setModalPopUpOrigin();
                        LaunchInWindow(this.$contentLocation, this.$contentName, this.$popoutWidth, this.$popoutHeight);
                    }
                }
            },this));

            $('.qx-mod-size', this.$initEl).on('click',$.proxy(function(ev) {
                $qxmodal.find('.modal-content').eq(0).toggleClass('qx-mod-fullscreen');
                if ($qxmodal.hasClass('qx-modal-attachments')) $qxmodal.toggleClass('qx-mod-fixed-fullscreen');
                var $evTg = $(ev.target);
                if (!$evTg.is('i')) {
                    $evTg.find('i').toggleClass('fa-expand-alt fa-compress-alt');
                    $evTg.attr('title', $evTg.find('i').hasClass('fa-compress-alt') ? GT(this._textValues.labelBtnMinimize) : GT(this._textValues.labelBtnMaximize));
                } else {
                    $evTg.toggleClass('fa-expand-alt fa-compress-alt');
                    $evTg.attr('title', $evTg.hasClass('fa-compress-alt') ? GT(this._textValues.labelBtnMinimize) : GT(this._textValues.labelBtnMaximize));
                }
            },this));

            if (this.$customProp && typeof this.$customProp === 'object' && this.$customProp.noIframe) {
                showLoading = false;
            }

            if (showLoading) {
                var $qxmod = this;
                $('iframe', this.$initEl).load(function () {
                    $('.qx-modal-loading', $qxmodal).hide();
                    if (this.src) {
                        $('.qx-mod-pop-open', $qxmodal).show();
                    }
                    //add coming soon to page loaded in modal if it's loaded in old UI for modals with iframe
                    var $ifrCnts = $(this).contents();
                    if (top && top.QX && typeof top.QX.addComingSoonToPage === 'function' ) {
                        top.QX.addComingSoonToPage($ifrCnts);
                    }
                    $qxmod.detectDownloadableContent($ifrCnts, $qxmodal);
                    qxDialogHideLoading();
                    //stop spinner when we have frameset inside iframe
                    if ($ifrCnts.length) {
                        $ifrCnts.find('frameset frame:eq(1)').load(function(){
                            qxDialogHideLoading();
                        });
                    }
                });
                if (this.$buildMode === 'wrap' || this.$buildMode === 'copy') {
                    $('.qx-modal-loading', $qxmodal).hide();
                }
            } else {
                $('.qx-modal-loading', $qxmodal).hide();
            }
            if (top && top.quixote && top.quixote.isMobile) {
                $qxmodal.find('.qx-mod-close').on('touchstart', function() {
                    $(this).click();
                });
            }
        },

        buildDialog: function(dataValues) {
            this.getDialogLayout();

            var modTitle = this.getDialogData('title');
            var mCnt = this.getDialogData('content');
            var diagCls = 'qx-modal-custom';
            var resize = 1;

            switch (this.$dataConfig) {
                case 'form':
                    diagCls = 'qx-modal-form';
                    resize = 1;
                    break;
                case 'help':
                    diagCls = 'qx-modal-help';
                    resize = 0;
                    break;
                case 'custom':
                    diagCls = 'qx-modal-custom';
                    resize = 0;
                    break;
                default:
                    diagCls = 'qx-modal-other';
                    resize = 1;
            }

            if (typeof dataValues === 'object') {
                modTitle = dataValues.title;
                mCnt = dataValues.content;
                diagCls = 'qx-modal-'+dataValues.configType;
                this.$diagButtons = dataValues.content;
            }

            this.$container.addClass(diagCls);
            if (this.$dataConfig === 'custom') {
                this.$container.addClass('qx-modal-help');
            }

            if (this.$dataConfig === 'form' && this.$customProp && typeof this.$customProp === 'object') {
                if (this.$customProp.noResize) resize = !this.$customProp.noResize;
                if (this.$customProp.customClass) this.$container.addClass(this.$customProp.customClass);
            }

            this.$container.attr('id',this.$idUnique);

            this.buildModalHeader(modTitle, resize);

            this.$mCnt.append(this.$mCntHeader);

            if (mCnt && mCnt.src && mCnt.src.indexOf('supportingdocument') > 0) {
                this.$container.addClass('qx-modal-attachments');
            }
            if (this.$dataContentType === 'html') {
                this.$container.addClass('qxdialog-html');
                if (this.settings.customSizes.indexOf(this.$modalsize) > -1) {
                    this.$container.addClass('qx-modsize-' + this.$modalsize);
                }
            }

            this.$mCntBody.html(mCnt);
            this.$mCnt.append(this.$mCntBody);
            if (this.$customProp && typeof this.$customProp === 'object' && this.$customProp.maximized) {
                this.$mCnt.addClass('qx-mod-fullscreen');
                this.$mResizeButton = this.$mCntHeader.find('.qx-mod-size');
                this.$mResizeIcon = this.$mResizeButton.find('i.qx-icon');
                this.$mResizeIcon.toggleClass('fa-expand-alt fa-compress-alt');
                this.$mResizeIcon.attr('title', this.$mResizeIcon.hasClass('fa-compress-alt') ? GT(this._textValues.labelBtnMinimize) : GT(this._textValues.labelBtnMaximize));
                this.$mResizeButton.attr('title', this.$mResizeIcon.hasClass('fa-compress-alt') ? GT(this._textValues.labelBtnMinimize) : GT(this._textValues.labelBtnMaximize));
            }
            if (this.$showfooterCloseBtn) this.$mCntFooter.append(this.$mCntFooterBtnClose);
            if (!this.$topButtons) this.$mCnt.append(this.$mCntFooter);
            this.$modDialog.append(this.$mCnt);
            this.$container.append(this.$modDialog);
        },

        limitModalsOpened: function (tp) {
            if ($.inArray(tp, this.settings.typeOfModalsLimited) > -1) {
                var hlpMod = this.$initEl.children('.qx-modal-help.in').length;
                if (hlpMod >= this.settings.limitHelpDialogs) {
                    return true;
                }
            }
            return false;
        },

        clearYuiElements: function(yuiElem) {
            var $yuiel = yuiElem;
            if (!(yuiElem instanceof jQuery)) {
                $yuiel = $('#'+yuiElem);
            }
            if ($yuiel.length) {
                $yuiel.removeClass('yui-panel-container');
                $yuiel.removeClass('shadow');
                $yuiel.removeClass('yui-force-redraw');
                $yuiel.removeClass('floating_page');
            }
            var $yuiDiv = $yuiel.children('.yui-module');
            $yuiDiv.removeClass('yui-module');
            $yuiDiv.removeClass('yui-overlay');
            $yuiDiv.removeClass('yui-panel');
            $yuiel.find('.hd').hide();
            $yuiel.find('.container-close').hide();
            $yuiel.find('.ft').hide();
        },
        //this method is only for images, need to enhance to support other formats
        detectDownloadableContent: function($ifrCnt, $qxmodal) {
            var $imgBody = $ifrCnt.find('body > img');
            if ($imgBody.length) {
                var imgSrc = $imgBody.attr('src');
                if (imgSrc) {
                    var ifrurl = $qxmodal.find('.qx-modal-iframe').attr('src');
                    window.open(ifrurl, "qxdialog", "width=790,height=550");
                    $qxmodal.find('.qx-mod-close').click();
                }
            }
        },
        getPropertiesFromParentDiv: function(el) {
            var $yel = el;
            if (!(el instanceof jQuery)) {
                $yel = $('#'+el);
            }
            if ($yel.hasClass('qx-fp-maximized')) {
                this.$maximized = true;
                if ($yel.hasClass('qx-fp-topbuttons')) {
                    this.$topButtons = true;
                }
            }
            var modsize = $yel.attr('data-modal-size');
            if (modsize) {
                this.$modalsize = modsize;
            }
        },

        checkForActiveFloatingPages: function() {
            if ($('.qxdialog-floating-page.in').length) {
                this.$container.addClass('qx-fp-sec');
                this.isSecondaryFP = true;
            }
        },

        checkForOpenedModals: function() {
            if (parent && parent.document) {
                if ($('.quixote-modal.qx-modal-form.in', parent.document).length) {
                    return true;
                }
            } else if ($('.quixote-modal.qx-modal-form.in').length) {
                return true;
            }
            return false;
        },

        checkIfModalOpenedInIE: function() {
            if (typeof QXUtil !== 'undefined' && QXUtil.isIE()) {
                return true;
            } else {
                return this.$initEl.hasClass('qx-b_IE');
            }
        },

        changeHelpModal: function($mod) {
            var $modbody = $mod.parent('body');
            if ($modbody.hasClass('modal-open')) $modbody.addClass('qx-modal-help-open');
        },
        //adjust elements z-index which have higher z-index that modal
        //if we don't find modal context or linedetails elements, do nothing
        adjustElementszIndex: function($mod) {
            //if we dont have $mod, that means we are in another context
            //and we have to look for modal in different context
            var $ifrContext = null;
            if (!$mod.length) {
                if (parent && parent.document) {
                    $ifrContext = $('.quixote-modal.qx-modal-form.qx-modal-last.in', parent.document)
                        .find('iframe.qx-modal-iframe');
                    $mod = $ifrContext.contents().find('#'+this.$idUnique);
                }
            }
            var $els = null;
            if ($ifrContext && $ifrContext.length) {
                $els = $ifrContext.contents().find('.lineDetails, .line_details_switch');
            } else {
                $els = $('.lineDetails, .line_details_switch');
            }
            if ($els.length && $mod.length) {
                var elsZIndex = $els.css('z-index');
                var modalZIndex = $mod.css('z-index');
                if (elsZIndex && modalZIndex && elsZIndex > modalZIndex) {
                    var newZIndex = modalZIndex - 11;
                    if ($els.length) {
                        $els.css('z-index',newZIndex);
                    }
                }
            }
        },

        attachMutationObserver: function(cntSel, callback) {
            //using Mutation Observer to detect when a div is added in DOM
            var onMutationsObserved = function(mutations) {
                mutations.forEach(function(mutation) {
                    var $elem = null;
                    if (mutation.addedNodes.length) {
                        $elem = $(mutation.addedNodes);
                    } else if (mutation.removedNodes.length) {
                        $elem = $(mutation.removedNodes);
                    }
                    if ($elem && $elem.length) callback($elem);
                });
            };

            var target = $(cntSel)[0];
            var mocfg = { childList: true, subtree: true };
            var MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;
            this.observer = new MutationObserver(onMutationsObserved);
            this.observer.observe(target, mocfg);
        },

        checkForErrorMessageInParent: function($fsm) {
            //attach an observer on element with id: #ErrorMsg looking for an element with class .editor_message
            this.attachMutationObserver('#ErrorMsg', function(el){
                var $elMsg = $(el);
                if ($elMsg.length) {
                    var $modbody = $fsm.find('.modal-body');
                    var $modbodyFP = $modbody.find('.qx-floating-page');
                    $elMsg.addClass('qx-modal-error');
                    //first we remove any .editor_message in modal then add
                    $modbody.find('.qx-modal-error').remove();
                    if ($modbodyFP.length) {
                        $elMsg.prependTo($modbodyFP);
                    } else if ($modbody.length && $modbody.children().length > 0) {
                        // prepend error only if it has active content
                        $elMsg.prependTo($modbody);
                    }

                }
            });
        },

        clearMutationObserver: function() {
            if (this.observer) {
                //using try catch in case the observer was not correctly initialized
                try {
                    this.observer.disconnect();
                } catch(err) {
                    console.log('QXDIAG Err: Cannot disconnect observer: '+err.message);
                }
            }
        },

        hideFPGridLineDetails: function($mod) {
            //we have to hide the line_details_switch attached to a grid inside modal
            if (this.$preventAutoHideLineDetails) return;
            this.$initEl.find('.line_details_switch, .lineDetails').hide();
        },

        buildDialogWrapper: function(dataValues) {
            this.getDialogLayout();
            var modTitle = this.getDialogData('title');
            var mCnt = this.getDialogData('content');
            var parentDiv = this.getDialogData('parentDiv');
            this.$diagButtons = this.getDialogData('buttons');
            var diagCls = 'qx-modal-form';
            if (typeof dataValues === 'object') {
                modTitle = dataValues.title;
                mCnt = dataValues.content;
                diagCls = 'qx-modal-'+dataValues.configType;
                parentDiv  = dataValues.parentDiv;
                this.$diagButtons = dataValues.buttons;
            }

            this.clearYuiElements(parentDiv);
            this.getPropertiesFromParentDiv(parentDiv);

            this.$container.addClass(diagCls);
            this.$container.addClass('qxdialog-floating-page');
            if (this.settings.customSizes.indexOf(this.$modalsize) > -1) {
                this.$container.addClass('qx-modsize-'+this.$modalsize);
            }
            this.$container.attr('id',this.$idUnique);
            this.checkForActiveFloatingPages();

            if (this.$buildMode === 'copy') {
                diagCls = 'qx-modal-fp';
                this.$container.addClass(diagCls);
                this.$container.attr('id',this.$idUnique);
                this.buildModalHeader(modTitle,0);
                this.$mCnt.append(this.$mCntHeader);
                this.$mCntBody.append($('#'+parentDiv).clone());
                this.$mCnt.append(this.$mCntBody);
                if (!this.$topButtons) this.$mCnt.append(this.$mCntFooter);
                this.$modDialog.append(this.$mCnt);
                this.$container.append(this.$modDialog);
            } else {
                parentDiv.wrap(this.$container);
                var $mod = $('#'+this.$idUnique);
                $mod.find('.qx-floating-page').wrap(this.$mCntBody);
                $mod.find('.modal-body').wrap(this.$mCnt);
                var $mContent = $mod.find('.modal-content');
                var fpResize = this.checkForFullScreen($mContent);
                this.buildModalHeader(modTitle,fpResize);
                $mContent.prepend(this.$mCntHeader);
                if (!this.$topButtons) $mContent.append(this.$mCntFooter);
                $mContent.wrap(this.$modDialog);
            }
            this.moveOldFooterButtons();
        },

        buildModalHeader: function(title,rsz) {
            var $mainView = window.editor && window.editor.findComponents('', 'View');
            var popOutDialogEnabling = $mainView && $mainView[0].popOutDialog;
            var hideSecondLevelPopoutIE = this.isOpenedInIE && this.hasOpenModal;
            this.$mCntHeaderTitle.html(title);
            if (title) this.$mCntHeaderTitle.prop("title", title);
            this.$mCntHeader.append(this.$mCntHeaderLoading);
            this.$mCntHeader.append(this.$mCntHeaderTitle);
            // add popout button and open dialog if it's 'form' and allowed to be opened in new window
            if (this.$dataConfig === "form"
                && (popOutDialogEnabling || typeof popOutDialogEnabling === "undefined")
                && (top && top.quixote && !top.quixote.isMobile)
                && !this.isFloatingPage && !this.isSecondaryFP && !hideSecondLevelPopoutIE) {
                this.$mCntHeader.append(this.$mCntHeaderPopOut);
                this.$mCntHeaderPopOut.hide();
                this.$mCntHeaderPopOut.on('click', $.proxy(function() {
                    this.$mCntHeaderPopOut.find('i').addClass('fa-external-link-alt-clicked');
                    this.$contentLocation = $(this.$mCntBody[0]).find('iframe')[0].src;
                    this.$contentName = 'qxdialog';
                    this.$mCntHeaderClose.click();
                }, this));
            }
            if (rsz) this.$mCntHeader.append(this.$mCntHeaderSize);
            if (this.$topButtons) {
                this.$mCntHeaderButtons.append(this.$mCntFooter);
                this.$mCntHeader.append(this.$mCntHeaderButtons);
                this.$mCntHeaderClose.addClass('qx-mod-close-hidden');
            }
            this.$mCntHeader.append(this.$mCntHeaderClose);
        },

        getDialogLayout: function() {
            this.$container = $(this.settings.layout.modContainer);
            this.$modDialog = $(this.settings.layout.modDialog);
            this.$mCnt = $(this.settings.layout.mCnt);
            this.$mCntHeader = $(this.settings.layout.mCntHeader);
            this.$mCntHeaderTitle = $(this.settings.layout.mCntHeaderTitle);
            this.$mCntHeaderPopOut = $(this.settings.layout.mCntHeaderPopOut);
            this.$mCntHeaderSize = $(this.settings.layout.mCntHeaderSize);
            this.$mCntHeaderClose = $(this.settings.layout.mCntHeaderClose);
            this.$mCntHeaderLoading = $(this.settings.layout.mCntHeaderLoading);
            this.$mCntHeaderButtons = $(this.settings.layout.mCntHeaderButtons);
            this.$mCntBody = $(this.settings.layout.mCntBody);
            this.$mCntFooter = this.getFooterButtons();
        },

        getFooterButtons: function() {
            var $footerBtns = $(this.settings.layout.mCntFooter);
            if (this.$showFooter) {
                if (this.$diagButtons && this.$diagButtons.buttons) {
                    var vBtns = this.$diagButtons.buttons;
                    for (var key in vBtns)
                    {
                        if (vBtns.hasOwnProperty(key)) {
                            var btnObj = vBtns[key];
                            var newBtn = $('<button/>');
                            newBtn.addClass('btn btn-secondary');
                            newBtn.text(GT(btnObj.name));
                            if ( btnObj.type && btnObj.type === 'close' ) {
                                newBtn.attr('data-dismiss', 'modal');
                            }
                            newBtn.click(btnObj.callback);
                            $footerBtns.append(newBtn);
                        }
                    }
                }
                if (this.$showfooterCloseBtn) {
                    $footerBtns.append('<button class="btn btn-secondary" data-dismiss="modal" aria-label="'+ GT('IA.CLOSE') +'">'+ GT('IA.CLOSE') +'</button>');
                }

            }
            return $footerBtns;
        },

        moveOldFooterButtons: function() {
            var $mod = $('#'+this.$idUnique);
            var $modFooter = $mod.find('.modal-footer');
            var $oldBtns = $mod.find('ul.footer');
            $oldBtns.find('button').each(function(){
                $modFooter.append(this);
            });
            $oldBtns.remove();
        },

        checkForFullScreen: function($vmod) {
            if ($vmod && this.$maximized) {
                $vmod.addClass('qx-mod-maximized');
                if (this.$topButtons) $vmod.addClass('qx-mod-topbtns');
                return false;
            } else {
                return true;
            }
        },

        getDialogData: function(dataType) {
            var dCnt = this.getValueOfConfig(dataType);
            if (!dCnt) {
                dCnt = null;
            }
            return dCnt;
        },

        hideDialog: function() {
            $('.qx-modal-form.in.qx-modal-last').modal('hide');
        },

        removeClosedModals: function() {
            $('.qxdialog:not(.in)',this.$initEl).remove();
            handlePaceActive();
        },

        handleBackdropForMultipleModalsOpened: function($mod) {
            if (this.$tmpEl) {
                this.$tmpEl.find('.modal-backdrop.in').hide();
            }
            $('<div class="modal-backdrop fade in '+this.$idUnique+'"></div>').hide().fadeIn(200).insertAfter($mod);
        },

        removeBackdropByClass: function(cls) {
            this.$initEl.find('.modal-backdrop.'+cls).fadeOut(200, function() {
                $(this).remove();
            });
        },

        getValueOfConfig: function(cfg) {
            var valCfg = this.$initEl.data(cfg);
            if (!valCfg) valCfg = this.settings[this.camelCase(cfg)];
            return valCfg;
        },

        camelCase: function (string) {
            return string.replace( /-([a-z])/ig, function( all, letter ) {
                return letter.toUpperCase();
            });
        },
    });

    $.fn[qxDialog] = function (options, dataKey) {
        return this.each(function () {
            if (!$.data(this, dataKey)) {
                $.data(this, dataKey, new QXDialogConstructor(this, options));
            }
        });
    };

})(jQuery, window, document);

function sanitizeUrl(url) {
    try {
        var allowedProtocols = ['http:', 'https:'];
        var baseUrl = window.location.href; // Full current URL including path
        var a = document.createElement('a'); // Create a temporary anchor element

        a.href = url; // Set the relative or absolute URL

        // If the URL is relative, the browser resolves it against the current path
        if (a.protocol && allowedProtocols.indexOf(a.protocol) !== -1) {
            // If relative, make sure it's resolved properly by constructing a full URL
            if (!a.origin || a.origin === 'null') {
                var resolvedUrl = new URL(a.href, baseUrl);
                return resolvedUrl.href;
            }
            return a.href;
        }
    } catch (e) {
        console.error('Invalid URL:', e);
    }
    return 'about:blank';
}
/**
 *
 * @param string title
 * @param string content
 * @param string buttons
 * @param string modaltype
 * @param string cnttype
 * @param obj customProp
 */
function showQxDialog(title, content, buttons, modaltype, cnttype, customProp) {
    if (!cnttype) cnttype = 'iframe';
    if (!content) content = 'NA';
    if (!modaltype) modaltype = 'form';
    if (!buttons) buttons = 'none';
    if (typeof customProp !== 'object') customProp = {};

    if (modaltype === 'form' || modaltype === 'picker') {
        if (cnttype === 'url' || cnttype === 'iframe' || cnttype === 'html') {
            showQxDialogForm(title, content, buttons, modaltype, cnttype, customProp);
        } else {
            console.log('QXDIAG Err: cannot build dialog, unaccepted cnttype: '+cnttype);
        }
    } else if (modaltype === 'help') {
        showQxDialogHelp(content, title, buttons, customProp);
    } else if (modaltype === 'custom') {
        showQxDialogCustom(content, title, buttons, customProp);
    } else if (modaltype === 'floatingpage') {
        var fpmode = 'wrap';
        if (cnttype === 'idcopy') {
            fpmode = 'copy';
        }
        var fpInArray = window.editor ? window.editor.findComponents(content) : null;
        var fp = fpInArray && fpInArray.length ? fpInArray[0] : null;
        //default value true, and the only time we set it to false when autoHideLoading is explicitly false (null, undefined = true)
        customProp.autoHideLoading = !fp || fp.autoHideLoading !== false;
        showQxDialogFloatingPage(content, fpmode, buttons, customProp);
    } else {
        console.log('QXDIAG Err: Unaccepted modal type: '+modaltype);
    }
}

//form dialog instance - DONT use this function directly, use showQxDialog with params
function showQxDialogForm(title, content, buttons, type, cnttype, customProp) {
    if (typeof customProp !== 'object') customProp = {};

    switch(cnttype) {
        case 'url':
            var iframe = getIFrameForQXDialog();
            iframe.src = sanitizeUrl(content);
            iframe.dataOrigSrc = sanitizeUrl(content);
            content = iframe;
            break;
        case 'html':
            type = 'html';
            break;
        default:
        //default is iframe
    }
    var modalOptions = {
        title: title,
        content: content,
        configType: 'form',
        type: type,
        buildMode: 'normal',
        buttons: buttons,
        customProp: customProp
    }
    getQxDialogInstance(modalOptions);

    //attach draggable for attachements modals
    var context = null;
    if (window.parent && window.parent.document) {
        context = window.parent.document;
    }
    //in default context
    var attchMods = jq('.qxdialog.qx-modal-form.qx-modal-attachments');
    if (attchMods.length) {
        attchMods.draggable({
            handle: '.modal-header'
        });
        //in parent context where all modals are built
    } else if(context) {
        //we need to use parent jquery for draggable to work
        var jqParent = window.parent.jq;
        if (jqParent && jqParent().draggable) {
            jqParent('.qxdialog.qx-modal-form.qx-modal-attachments', context).draggable({
                handle: '.modal-header'
            });
        }
    }
}

/**
 * Create an iframe suitable for cnttype 'url' and 'iframe'.
 *
 * WW: This is exposed so that we can post a form with the iframe as the form target.
 *
 * @returns {HTMLIFrameElement}
 */
function getIFrameForQXDialog()
{
    //build the iframe from content which should be url
    var nriFrames = jq('.qxdialog.in .qx-modal-iframe',parent.document).length;
    var idiFrame = 'picker_iframe-' + nriFrames;
    var classiFrame = 'qx-modal-iframe';

    var iframe = document.createElement('iframe');
    iframe.name = idiFrame; //for quixote we use picker_iframe-X, where X is the number of iframe
    iframe.id = idiFrame;
    iframe.className = classiFrame;
    iframe.frameborder = 0;

    return iframe;
}

//help dialog instance - DONT use this function directly, use showQxDialog with params
function showQxDialogHelp($jqDialog, vtitle, buttons) {
    var title = null;
    var content = null;
    if ($jqDialog instanceof jQuery) {
        title = $jqDialog.find('.modalHeading').html();
        content = $jqDialog.find('.modalBodyContent').html();
    } else {
        title = vtitle ? vtitle : '';
        content = $jqDialog;
    }

    var modalOptions = {
        title: title,
        content: content,
        configType: 'help',
        type: 'help',
        buildMode: 'normal',
        buttons: buttons
    }
    getQxDialogInstance(modalOptions);
    jq('.qxdialog.qx-modal-help').draggable({
        handle: '.modal-header'
    });
}

function showQxDialogCustom($jqDialog, vtitle, buttons, draggable) {
    var title = null;
    var content = null;
    if ($jqDialog instanceof jQuery) {
        title = $jqDialog.find('.modalHeading').html();
        content = $jqDialog.find('.modalBodyContent').html();
    } else {
        title = vtitle ? vtitle : '';
        content = $jqDialog;
    }

    var modalOptions = {
        title: title,
        content: content,
        configType: 'custom',
        type: 'custom',
        buildMode: 'normal',
        buttons: buttons
    };
    getQxDialogInstance(modalOptions);
}

//dialog for floating-page - - DONT use this function directly, use showQxDialog with params
function showQxDialogFloatingPage(id, bmd, buttons, customProp) {
    var jqFP = jq('#'+id);
    var title = jqFP.find('.hdr h1').html();
    var content = ''; //not used in this case
    var parent = jqFP.parents('.yui-panel-container');
    var bmode = 'wrap';
    if (bmd && bmd === 'copy') {
        bmode = 'copy';
        parent = id;
    }
    //scroll parent page to top if floating page has datepicker
    if (jqFP.find('.hasDatepicker').length) {
        window.scrollTo(0,0);
    }
    //remove mask from old modal
    jq('#'+id+'_mask').remove();
    var modalOptions = {
        title: title,
        content: content,
        configType: 'form',
        type: 'floating-page',
        buildMode: bmode,
        parentDiv: parent,
        buttons: 'close',
        customProp: customProp
    }
    getQxDialogInstance(modalOptions);
}

function getQxDialogInstance(diagOps) {
    var dataKey = 'qx_qxdialog' + (diagOps.buildMode === 'normal' ? '_bmNormal' : '');
    if (typeof jq('body').data()[dataKey] === 'undefined') {
        //create a new instance
        jq('body').qxdialog(diagOps, dataKey).data(dataKey);
    } else {
        //use the instance already created
        jq('body').qxdialog(dataKey).data(dataKey).qxmodal(diagOps);
    }
}

function closeQxFloatingPage() {
    jq('.qxdialog-floating-page.in:last').find('.qx-mod-close').click();
    handlePaceActive(300);
}

window.closeQxDialog = function(frameElement){
    if (frameElement) {
        jq(frameElement).closest(".quixote-modal").find('.qx-mod-close').click();
    } else {
        //fix for cases where we load Qx content in old dialog
        //and we try to close with this method
        var yparent = parent.window;
        var ydialog = yparent && yparent.editor && yparent.view ? 1 : 0;
        var winHandle = ydialog ? yparent : window.opener;
        if (winHandle && winHandle.YAHOO && winHandle.YAHOO.picker) {
            winHandle.YAHOO.picker.dialog.hide();
        } else {
            // If we call this method and we don't have action ui modal and we don't have the old ui dialog
            // then we run out of options, we close the browser popup.
            // This was introduced after we implemented exceptions to open in browser popups instead of modals
            window.close();
        }
    }

    handlePaceActive();
};

window.closeActiveQxDialog = function() {
    jq(".quixote-modal.qxdialog.in:last").find('.qx-mod-close').click();
    jq(".modal-backdrop.fade.in:last").remove();
    handlePaceActive();
};

window.getActiveQXDialogIframe = function() {
    return jq(".quixote-modal.qxdialog.in:last").find('.qx-modal-iframe');
};

window.getActiveFloatingPage = function() {
    return jq(".quixote-modal.qxdialog.qxdialog-floating-page.in:last").find('.qx-floating-page');
}
// need this to return the footer and add the shade to restrict the user from accidentally clicking on other buttons
window.getActiveFloatingPageFooter = function() {
    return jq(".quixote-modal.qxdialog.qxdialog-floating-page.in:last").find('.modal-footer');
}

function handlePaceActive(spto) {
    if (window && window.Pace) {
        jq('.pace-active').addClass('pace-inactive').removeClass('pace-active');
    } else {
        if (window && window.parent) {
            var paceobj = jq('.pace-active',window.parent.document);
            if (paceobj.length) {
                paceobj.addClass('pace-inactive').removeClass('pace-active');
            } else {
                if (window.parent.parent) {
                    jq('.pace-active',window.parent.parent.document).addClass('pace-inactive').removeClass('pace-active');
                }
            }
        }
    }

    if (this.autoHideLoading) {
        if (isNaN(spto)) spto = 0;
        window.setTimeout(function() {
            qxDialogHideLoading();
        }, spto);
    }
}

//this should be used to check for form modals
window.isQxDialog = function() {
    var isQxD = jq('.qxdialog.qx-modal-form.in', parent.document).length;
    return isQxD;
};

//this should be used to check for floating pages,
//this is separate than isQxDialog to not impact the functionality and because it has limited use
window.isFloatingPage = function () {
    var isQxFP = jq('.qxdialog.qxdialog-floating-page.in').length;
    return isQxFP;
};

function getWinHandleContext(winH) {
    //check if we are in browser popup with action ui,
    //for cases when we have combination of old ui with new ui
    var isPopup = false;
    if ( window.opener && window.opener !== window) isPopup = true;
    if ( !isPopup ) {
        var nriFrames = parent.document.getElementsByClassName("qx-modal-iframe").length;
        if (nriFrames > 1) {
            var ciFNr = window.name.split('-');
            var nrFToPop = ciFNr[1]-1;
            var idiFToPop = "picker_iframe-"+nrFToPop;
            if (nrFToPop >= 0) {
                winH = parent.document.getElementById(idiFToPop).contentWindow;
            }
        }
    } else {
        winH = window.opener;
    }

    return winH;
}

// Fix for DE15220: Modals can be opened in pages with or without qxutil, so we need to check
function qxDialogHideLoading () {
    if (top && top.QXUtil) {
        top.QXUtil.hideLoading();
    } else if (typeof QXUtil !== 'undefined') {
        QXUtil.hideLoading();
    }
};

/**
 * Override bootstraps hideModal function because we don't want to remove the modal-open class from the body
 * when there are other floating pages opened.
 */
if (jq && jq.fn && jq.fn.modal && jq.fn.modal.Constructor) {
    jq.fn.modal.Constructor.prototype.hideModal = function() {
        var that = this;
        this.$element.hide();
        this.backdrop(function() {
            //isFloatingPage returns the number of open floating pages after the current one was closed
            if (typeof isFloatingPage === 'function' && !isFloatingPage()) {
                that.$body.removeClass('modal-open');
            }
            that.resetAdjustments();
            that.resetScrollbar();
            that.$element.trigger('hidden.bs.modal');
        });
    };
}
