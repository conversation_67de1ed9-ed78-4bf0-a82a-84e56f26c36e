export const ImportDataActionListener = (listener, dependencies) => {
    const { api, OPERATION, axios, BACKEND_URL } = dependencies;
    // Import data -> Trigger defined in NIS_ActionsHelper::getImportDataButton()
    listener.on(
        "job:ready", { job: "workbook:import-data" },
        async({ context }) => {
            await axios.get(BACKEND_URL, {
                params: {
                    '.action': 'platform-import',
                    '.jID': context.jobId,
                    '.op': OPERATION
                }
            }).then((response) => {
                if ( response.data.success ) {
                    api.jobs.complete(context.jobId, {
                        outcome: {
                            heading: GT('IA.FF_IMPORT_RESULTS'),
                            message: `${response.data.message}`
                        }
                    });
                } else {
                    api.jobs.fail(context.jobId, {
                        outcome: {
                            heading: GT('IA.FF_IMPORT_RESULTS'),
                            message: `${response.data.message}`
                        }
                   })
                }
            });
        }
    );
}