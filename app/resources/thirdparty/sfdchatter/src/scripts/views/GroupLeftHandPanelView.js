define(function (require, exports, module) {

    require('backbone.salesforce');


    var BaseView = require('views/BaseView');
    var PanelTemplate = _.template(require('text!templates/GroupLeftHandPanel.html'));
    var GroupInfoDialogView = require('views/GroupInformationDialogView');
    var GroupDescriptionDialogView = require('views/GroupDescriptionDialogView');
    var GroupEditDialogView = require('views/GroupEditDialogView');
    var GroupModel = require('models/GroupModel');
    var GroupProfilePicUploadDialogView = require('views/GroupProfilePicUploadDialogView');

    return BaseView.extend({

        template: PanelTemplate,

        events: {

            'click .group_profile_upload':   'doUploadGroupProfilePic',
            'click .edit_group_information': 'doEditGroupInformation',
            'click .edit_group_description': 'doEditGroupDescription',
            'click .edit_group':             'doEditGroup'
        },

        initialize: function () {

            this.render();

            _.bindAll(this, 'doEditGroupInformation', 'doEditGroupDescription', 'doUploadGroupProfilePic');



        },

        render: function() {
            this.el.innerHTML = this.template(this.model);
        },

        doEditGroup: function() {

            var visibility;
            if ( this.model.groupInfo.CollaborationType == "Private" ) {
                visibility = "PrivateAccess";
            } else if ( this.model.groupInfo.CollaborationType == "Unlisted" ) {
                visibility = "Unlisted";
            } else {
                visibility = "PublicAccess";
            }

            var groupModel = new GroupModel({
                    name:                   this.model.groupInfo.Name || "",
                    owner:                  this.model.groupInfo.OwnerId,
                    description:            this.model.groupInfo.Description || "",
                    isAutoArchiveDisabled:  this.model.groupInfo.IsAutoArchiveDisabled || false,
                    visibility:             visibility,
                    canHaveChatterGuests:   this.model.groupInfo.CanHaveGuests || false
                });
            groupModel.id = this.model.groupInfo.Id;
            var editView = new GroupEditDialogView({model: groupModel, ownerId: this.model.groupInfo.OwnerId, ownerName: this.model.groupInfo.Owner.Name, groupId: this.model.groupInfo.Id, isArchived: this.model.groupInfo.IsArchived });
            editView.on({ 'done': function() {
                                    window.location.href = window.location.href;
                                }.bind(this),
                          'deleted': function() {
                                    navigate("<<CHATTERFEED>>", "chatterfeed", "<<GROUPS>>");
                                }.bind(this)});
        },

        doEditGroupInformation: function() {
            var groupInfoModel = new Backbone.Salesforce.Model({
                    information: {
                        title: this.model.groupInfo.InformationTitle || "",
                        text: this.model.groupInfo.InformationBody || ""
                    }
                },
                { url: '/chatter/groups/' + this.model.groupInfo.Id });
            groupInfoModel.id = this.model.groupInfo.Id;
            var infoView = new GroupInfoDialogView({model: groupInfoModel });
            infoView.on('done', function() {
                window.location.href = window.location.href;
            }.bind(this));
        },

        doEditGroupDescription: function() {
            var groupDescriptionModel = new Backbone.Salesforce.Model({
                    description: this.model.groupInfo.Description || ""
                },
                { url: '/chatter/groups/' + this.model.groupInfo.Id });
            groupDescriptionModel.id = this.model.groupInfo.Id;
            var descView = new GroupDescriptionDialogView({model: groupDescriptionModel });
            descView.on('done', function() {
                window.location.href = window.location.href;
            }.bind(this));
        },

        doUploadGroupProfilePic: function() {
            var uploadView = new GroupProfilePicUploadDialogView({ model: this.model.groupInfo });
            uploadView.on('new_image', function(newUrl) {
                if ( newUrl ) {
                    this.$('#group_profile_photo').attr('src', newUrl);
                } else {
                    // Big hammer.
                    window.location.href = window.location.href;
                }
            }.bind(this));
        }

    });

});