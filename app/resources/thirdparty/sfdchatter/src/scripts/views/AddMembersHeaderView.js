define(function (require, exports, module) {

    require('backbone.salesforce');


    var BaseView = require('views/BaseView');
    var PanelTemplate = _.template(require('text!templates/AddMembersHeaderView.html'));

    return BaseView.extend({

        template: PanelTemplate,

        events: {
                'click a#addmembersfilter_requests': 'handleFilterRequests',
                'click a#addmembersfilter_members':  'handleFilterMembers',
                'click a#addmembersfilter_everyone': 'handleFilterEveryone',
                'keyup #addmembersearchbox':         'handleSearchKeyUp',
                'blur #addmembersearchbox':          'handleSearchBlur',
                'focus #addmembersearchbox':         'handleSearchFocus',
                'click a.searchtextclear':           'handleSearchClear'
        },

        initialize: function () {
            _.bindAll(this, 'render', 'handleFilterEveryone', 'handleFilterMembers', 'handleFilterRequests',
                            'handleSearchKeyUp', 'handleSearchBlur', 'handleSearchClear',
                            'handleFilterWhoChange', 'handleFilterTextChange',
                            'handleRequestsModified', 'handleMembersModified');

            this.model.filter.on({ 'change:who': this.handleFilterWhoChange,
                                   'change:searchText': this.handleFilterTextChange });
            this.model.pendingRequests.on('modified', this.handleRequestsModified);
            this.model.members.on('modified', this.handleMembersModified);

        },

        handleRequestsModified: function() {
            this.updateNumRequests();
        },

        handleMembersModified: function() {
            this.updateNumMembers();
        },


        handleFilterWhoChange: function(model, who) {
            this.$('.addmembers_who').removeClass('everyone').removeClass('requests').removeClass('members').addClass(who);
        },

        handleFilterTextChange: function(model, text) {
            this.$('#addmembersearchbox').val(text);
        },

        updateNumRequests: function() {
            var newLength = this.model.pendingRequests.length;
            this.$('.addmembers_who').attr('requests', newLength);
            this.$('.addmembers_who .requestcount').text(newLength);
        },

        updateNumMembers: function() {
            var newLength = this.model.members.length;
            this.$('.addmembers_who').attr('members', newLength);
            this.$('.addmembers_who .membercount').text(newLength);
        },

        render: function () {
            this.el.innerHTML = this.template({ filter: this.model.filter});
            this.updateNumMembers();
            this.updateNumRequests();
            this.manageSearchClear(this.model.filter.get('searchText'));
            return this;
        },

        handleFilterEveryone: function() {
            this.trigger('filter-everyone', '');
        },

        handleFilterMembers: function() {
            this.trigger('filter-members', '');
        },

        handleFilterRequests: function() {
            this.trigger('filter-requests', '');
        },

        handleSearchKeyUp: function() {
            var newValue = this.$('#addmembersearchbox').val();
            this.manageSearchClear(newValue);
            if ( this.keydelaytimer ) {
                window.clearTimeout(this.keydelaytimer);
            }
            this.keydelaytimer = window.setTimeout(function() {
                this.keydelaytimer = undefined;
                this.trigger('filter-searchtext', newValue);
            }.bind(this), 250);
        },

        handleSearchClear: function() {
            var $searchBox = this.$('#addmembersearchbox');
            $searchBox.val('');
            this.manageSearchClear('');
            this.trigger('filter-searchtext', '');
            $searchBox.focus();
        },

        manageSearchClear: function(searchText) {
            if ( searchText != '' ) {
                this.$('a.searchtextclear').css('display', 'inline');
            } else {
                this.$('a.searchtextclear').css('display', 'none');
            }
        },

        handleSearchBlur: function() {
            this.trigger('filter-searchblur', '');
        },

        handleSearchFocus: function() {
            this.trigger('filter-searchfocus', '');
        }

    });

});