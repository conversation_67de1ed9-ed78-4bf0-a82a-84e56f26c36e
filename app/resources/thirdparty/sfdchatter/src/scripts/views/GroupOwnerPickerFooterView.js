define(function (require, exports, module) {

    require('backbone.salesforce');


    var BaseView = require('views/BaseView');
    var PanelTemplate = _.template(require('text!templates/ManageRolesFooterView.html'));

    return BaseView.extend({

        template: PanelTemplate,

        events: {
            'click #managerolesprevbutton':  'handlePrev',
            'click #managerolesnextbutton':  'handleNext',
            'click #managerolesdonebutton':  'handleDone'
        },

        initialize: function() {
            _.bindAll(this, 'render', 'handlePrev', 'handleNext', 'handleDone', 'updateButtonStatus');

            this.on("updatebuttonstatus", this.updateButtonStatus);
        },

        updateButtonStatus: function() {
            if ( this.model.pageNum == 0 ) {
                this.$('.prevpage_wrapper').removeClass('notfirstpage').addClass('firstpage');
            } else {
                this.$('.prevpage_wrapper').removeClass('firstpage').addClass('notfirstpage');
            }

            if ( this.model.members.length != this.model.pageSize ) {
                this.$('.nextpage_wrapper').removeClass('notlastpage').addClass('lastpage');
            } else {
                this.$('.nextpage_wrapper').removeClass('lastpage').addClass('notlastpage');
            }

        },

        render: function() {
            this.el.innerHTML = this.template(this.model);
            return this;
        },

        handlePrev: function() {
            this.trigger('prev', '');
        },

        handleNext: function() {
            this.trigger('next', '');
        },

        handleDone: function() {
            this.trigger('done', '');
        }

    });

});