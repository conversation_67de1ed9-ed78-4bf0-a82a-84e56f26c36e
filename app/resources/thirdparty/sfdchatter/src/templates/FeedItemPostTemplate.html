<span class="feeditemtext cxfeeditemtext">
<% _.each(body.messageSegments, function(seg) { %>  
<% if(seg.type === "Mention") {  %>
<%    if ( seg.record && ( seg.record.type == 'User' || seg.record.type == 'CollaborationGroup' ) ) { %>
    <a href="javascript:;" onclick="<%= "navigate('"+seg.type+"', '"+seg.text+"', '"+seg.record.id+"');"  %>"><%= seg.text %></a>
<%    } else { %>
    <%= seg.text %>
<%    } %>
<% } else { %>
    <%= seg.text %>
<% } %>
<% }); %>
</span>
<% if(type == "LinkPost") {  %>
<div class="linkPostLinkContainer">
  <span class="linkPostLinkTitle"><a href="<%- capabilities.link.url %>" class="linkPostLink" target="_blank"><img src="../resources/thirdparty/sfdchatter/images/s.gif" alt="<%- capabilities.link.urlName %>" title="<%- capabilities.link.urlName %>"><span title="<%- capabilities.link.url %>"><%= capabilities.link.urlName %></span></a><div class="feeditemlinkpost"><%= capabilities.link.url %></div></span></div>
<% } else if(type== "ContentPost" || type == 'ContentComment') { %>
<% if(capabilities.content !=null && capabilities.content.id != null) { %>
<span>
	<table class="contentPost">
		<tbody>
			<tr>
				<td class="thumbnailCell" id="left_745918848">
                        <% if ( envCanPreview ) { %>
                            <img src="<%= renditionUrl %>" alt="Click to preview"
                            class="contentThumbnail contentThumbnailPreviewable"
                            title="Click to preview"
                            id="ext-gen3">
					    <% } else{ %>
                            <img src="<% renditionUrl %>"
                             class="contentThumbnail contentThumbnailPreviewable"
                             id="ext-gen3">
					    <% } %>
				</td>
				<td class="rightSideCell">
					<table class="contentdetails">
						<tbody>
							<tr>
								<td>
									<div class="contentFileTitle">
										<a href="<%= hostURL%><%= Backbone.Salesforce.scrubbedPath(capabilities.content.downloadUrl) %>&.format=binary" class="contentActionLink">
											<span class="contentTitleLink" id="title_745918848" title="<%= capabilities.content.title %>"><%= capabilities.content.title %></span>
										</a>
									</div>
								</td>
							</tr>
							<tr>
								<td class="moreFileActions-td">
									<a href="<%= hostURL%><%= Backbone.Salesforce.scrubbedPath(capabilities.content.downloadUrl) %>&.format=binary"
									class="contentActionLink" shouldstayinownframe="true">
										<img src="../resources/thirdparty/sfdchatter/images/s.gif" alt="" width="1" height="1" class="chatterFileIcon downloadItemIcon">
										<span class="contentActionLabel">Download <%- capabilities.content.fileExtension %></span>
									</a>
									<span class="contentActionLabel fileSize">(<%= formatUtil.parseSize(capabilities.content.fileSize) %>)</span>
                                    <span class="contentFileMoreActionsSep">&#183;</span>
                                    <div class="contentFileMoreActionsCont" id="contentFileMoreActions<%= commentId%>Div">
                                        <a class="contentFileMoreActionsLink" href="javascript:;"
                                         id="contentFileMoreActions<%= commentId%>">
                                            <span class="contentFileMoreActionsLinkLabel">More Actions</span>
                                            <span id="contentFileMoreActionsIcon" class="icon fa fa-caret-down"></span>
                                        </a>
                                    </div>
								</td>
							</tr>
						</tbody>
					</table>
				</td>
			</tr>
		</tbody>
	</table>
</span>

<% } } %>
