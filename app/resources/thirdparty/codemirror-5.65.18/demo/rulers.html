<!doctype html>

<title>CodeMirror: Ruler Demo</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<link rel="stylesheet" href="../lib/codemirror.css">
<script src="../lib/codemirror.js"></script>
<script src="../addon/display/rulers.js"></script>
<style>
  .CodeMirror {border-top: 1px solid #888; border-bottom: 1px solid #888;}
</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Ruler demo</a>
  </ul>
</div>

<article>
<h2>Ruler Demo</h2>

<script>
  var nums = "0123456789", space = "          ";
  var colors = ["#fcc", "#f5f577", "#cfc", "#aff", "#ccf", "#fcf"];
  var rulers = [], value = "";
  for (var i = 1; i <= 6; i++) {
    rulers.push({color: colors[i], column: i * 10, lineStyle: "dashed"});
    for (var j = 1; j < i; j++) value += space;
    value += nums + "\n";
  }
  var editor = CodeMirror(document.body.lastChild, {
    rulers: rulers,
    value: value + value + value,
    lineNumbers: true
});
</script>

<p>Demonstration of
the <a href="../doc/manual.html#addon_rulers">rulers</a> addon, which
displays vertical lines at given column offsets.</p>

</article>
