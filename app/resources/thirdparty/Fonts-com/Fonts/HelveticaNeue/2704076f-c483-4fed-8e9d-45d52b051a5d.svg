<?xml  version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" >
<metadata>
<version>1.0</version>
<id><![CDATA[Neue Helvetica W01 89 Cm Heavy]]></id>
<vendor>Monotype Imaging Inc.</vendor>
<credits>
<name>Fonts.com WebFonts</name>
<URL>http://webfonts.fonts.com</URL>
<role>Home of the Web fonts</role>
</credits>
<license>
<URL>http://webfonts.fonts.com/Legal</URL>
</license>
<copyright><![CDATA[Copyright © 2013 Monotype Imaging Inc. All right reserved.]]></copyright>
<trademark><![CDATA[Helvetica is a trademark of Monotype Imaging Inc. registered in the U.S. Patent and Trademark Office and may be registered in certain other jurisdictions.]]></trademark>
<licensee>
<name></name>
</licensee>
</metadata>
<defs >
<font horiz-adv-x="353" id="2704076f-c483-4fed-8e9d-45d52b051a5d">
<font-face font-family="Neue Helvetica W01 89 Cm Heavy" panose-1="2 11 9 8 2 2 2 2 2 4" ascent="983" descent="-248" units-per-em="1000" alphabetic="0">
</font-face>
<missing-glyph horiz-adv-x="0" />

<glyph unicode="&#xA;" />
<glyph unicode="&#xD;" horiz-adv-x="180" />
<glyph unicode=" " horiz-adv-x="180" />
<glyph unicode="!" horiz-adv-x="199" d="M163 509L143 227H51L32 509L24 714H171L163 509ZM36 0V162H158V0H36Z"  />
<glyph unicode="&quot;" horiz-adv-x="341" d="M36 714H144V372H36V714ZM197 714H306V372H197V714Z"  />
<glyph unicode="#" horiz-adv-x="401" d="M60 0L73 188H19V283H80L89 408H37V503H96L110 694H200L186 503H243L257 694H347L333 503H386V408H326L317 283H369V188H310L297 0H207L220 188H163L150 0H60ZM170 283H227L236 408H179L170 283Z"  />
<glyph unicode="$" horiz-adv-x="401" d="M382 200Q382 103 346 54T238 -6V-73H167V-6Q130 -3 104 9T60 47T33 114T22 216H158Q159 186 162 166T169 135T181 119T201 114Q238 114 238 170Q238 207 222 231T182 276T131 315T79 360T39 424T23 520Q23 606 59 650T167 703V766H238V704Q277 699 302 684T344 643T366 581T373 499H253Q252 543 244 565T210 588Q187 588 180 572T173 536Q173 502 188 480T227 439T277 402T328 357T366 294T382 200Z"  />
<glyph unicode="%" horiz-adv-x="504" d="M227 539Q227 448 204 410T123 372Q67 372 45 410T23 539Q23 624 44 663T123 702Q182 702 204 663T227 539ZM140 539Q140 569 140 587T137 615T132 628T123 632Q114 632 111 613T108 539Q108 509 108 491T110 462T115 448T123 444Q128 444 131 448T137 462T139 491T140 539ZM326 708H414L176 -8H88L326 708ZM482 163Q482 72 459 34T378 -4Q322 -4 300 34T278 163Q278 205 282 235T298 286T329 316T378 326Q437 326 459 287T482 163ZM395 163Q395 193 395 211T392 239T387 252T378 256Q369 256 366 237T363 163Q363 133 363 115T365 86T370 72T378 68Q383 68 386 72T392 86T394 115T395 163Z"  />
<glyph unicode="&amp;" horiz-adv-x="495" d="M386 567Q386 538 380 515T360 471T325 431T275 389L351 250Q355 267 357 287T361 330H479Q475 277 460 227T420 133L496 0H348L324 44Q296 18 264 5T190 -8Q145 -8 112 6T57 47T24 111T13 192Q13 228 20 256T41 308T77 353T126 398L120 409Q101 443 89 477T76 552Q76 627 113 670T232 714Q312 714 349 672T386 567ZM220 109Q252 109 272 134L180 305Q166 286 159 266T152 217Q152 169 166 139T220 109ZM199 558Q199 524 218 488L226 474Q246 494 255 514T265 557Q265 583 256 593T232 603Q199 603 199 558Z"  />
<glyph unicode="&apos;" horiz-adv-x="180" d="M36 714H144V372H36V714Z"  />
<glyph unicode="(" horiz-adv-x="262" d="M251 721Q227 671 210 616T183 504T167 390T162 277Q162 219 167 159T184 41T211 -73T251 -177H164Q135 -131 110 -81T67 26T37 144T26 277Q26 411 64 518T164 721H251Z"  />
<glyph unicode=")" horiz-adv-x="262" d="M98 721Q129 674 154 624T198 519T226 404T236 277Q236 207 225 145T196 26T152 -80T98 -177H12Q34 -129 51 -74T79 40T95 159T101 277Q101 332 96 389T81 504T53 616T12 721H98Z"  />
<glyph unicode="*" horiz-adv-x="322" d="M107 414L37 466L91 542L4 568L31 651L118 620V714H204V620L291 651L318 568L231 542L285 466L215 414L161 494L107 414Z"  />
<glyph unicode="+" horiz-adv-x="401" d="M257 198V66H144V198H18V309H144V441H257V309H383V198H257Z"  />
<glyph unicode="&#x2c;" horiz-adv-x="180" d="M150 38Q150 -2 144 -36T124 -96T86 -138T25 -157V-78Q42 -74 56 -58T70 -5V5H25V189H150V38Z"  />
<glyph unicode="-" horiz-adv-x="255" d="M15 234V370H241V234H15Z"  />
<glyph unicode="." horiz-adv-x="180" d="M27 0V189H152V0H27Z"  />
<glyph unicode="/" horiz-adv-x="255" d="M-11 -19L155 731H265L99 -19H-11Z"  />
<glyph unicode="0" horiz-adv-x="401" d="M385 352Q385 252 378 183T349 72T292 11T199 -8Q143 -8 107 10T51 71T24 183T16 352Q16 446 23 513T50 623T106 686T200 706Q259 706 295 687T351 625T378 515T385 352ZM237 352Q237 428 236 474T231 545T220 578T200 586Q188 586 181 576T171 539T166 468T164 352Q164 277 165 231T170 158T180 122T199 113Q211 113 218 124T230 163T235 237T237 352Z"  />
<glyph unicode="1" horiz-adv-x="401" d="M148 0V511H45V611Q107 614 139 635T185 705H290V0H148Z"  />
<glyph unicode="2" horiz-adv-x="401" d="M26 0V29Q26 71 35 105T60 172T101 238T155 310Q176 337 191 360T216 407T230 456T235 514Q235 554 227 572T199 590Q176 590 169 562T160 480H25Q25 533 33 575T61 646T117 690T205 706Q296 706 339 659T382 510Q382 468 374 433T351 367T319 310T281 260Q250 223 227 189T195 117H376V0H26Z"  />
<glyph unicode="3" horiz-adv-x="401" d="M384 204Q384 139 367 98T323 34T261 1T193 -8Q143 -8 109 5T53 46T21 116T11 215H152Q153 155 162 127T194 99Q205 99 213 102T228 117T237 149T240 202Q240 238 237 260T224 294T196 310T148 314V410Q174 410 191 413T217 428T231 459T235 512Q235 559 227 577T195 596Q169 596 162 568T155 493H18Q21 546 31 586T64 652T119 692T203 706Q239 706 270 697T325 666T362 612T375 531Q375 459 354 422T287 369V367Q342 353 363 315T384 204Z"  />
<glyph unicode="4" horiz-adv-x="401" d="M218 0V127H9V238L183 698H346V239H393V127H346V0H218ZM218 485H216L129 239H218V485Z"  />
<glyph unicode="5" horiz-adv-x="401" d="M379 254Q379 182 370 132T338 51T282 6T196 -8Q155 -8 123 1T70 35T37 100T24 204H167Q168 153 172 126T197 99Q207 99 213 105T224 128T229 170T231 234Q231 306 225 334T197 362Q178 362 172 345T163 307H34L44 695H354V579H165L157 427L159 429Q174 455 194 469T253 484Q292 484 316 469T355 424T374 352T379 254Z"  />
<glyph unicode="6" horiz-adv-x="401" d="M385 251Q385 174 372 124T334 45T275 4T197 -8Q139 -8 104 10T49 67T22 169T15 320Q15 409 20 480T45 602T104 679T209 706Q256 706 286 693T334 657T359 601T367 529H239Q238 566 231 580T206 595Q194 595 186 588T172 560T164 503T161 411H163Q176 435 199 452T260 469Q286 469 309 460T349 427T375 361T385 251ZM232 223Q232 259 231 283T226 321T216 340T198 346Q175 346 169 315T163 222Q163 187 164 164T169 128T179 109T197 103Q206 103 213 107T224 124T230 160T232 223Z"  />
<glyph unicode="7" horiz-adv-x="401" d="M78 0Q95 165 136 306T232 562H15V695H380V574Q350 507 327 442T286 308T256 164T235 0H78Z"  />
<glyph unicode="8" horiz-adv-x="401" d="M388 201Q388 88 343 40T201 -8Q103 -8 58 40T13 201Q13 234 17 262T31 313T58 350T101 375V377Q63 393 46 429T28 523Q28 615 70 660T201 706Q289 706 331 662T374 523Q374 463 356 428T300 377V375Q351 359 369 317T388 201ZM240 208Q240 261 234 290T200 319Q174 319 168 289T161 210Q161 183 162 163T167 130T179 110T201 103Q227 103 233 130T240 208ZM236 513Q236 557 230 576T200 596Q180 596 173 577T165 512Q165 461 171 440T201 419Q222 419 229 440T236 513Z"  />
<glyph unicode="9" horiz-adv-x="401" d="M381 378Q381 289 376 218T351 96T292 19T187 -8Q140 -8 110 5T62 41T37 97T30 169H157Q159 132 166 118T191 103Q203 103 211 110T225 138T233 195T236 288H234Q220 263 197 246T137 229Q110 229 87 237T47 270T21 336T11 447Q11 523 24 573T62 653T122 694T200 706Q258 706 293 688T348 631T374 529T381 378ZM233 476Q233 511 232 534T227 570T217 589T200 595Q191 595 184 591T173 574T167 538T165 475Q165 439 166 415T170 377T180 357T198 351Q221 351 227 383T233 476Z"  />
<glyph unicode=":" horiz-adv-x="180" d="M27 0V189H152V0H27ZM27 355V544H152V355H27Z"  />
<glyph unicode=";" horiz-adv-x="180" d="M27 355V544H152V355H27ZM152 38Q152 -10 146 -45T126 -104T87 -141T26 -157V-78Q35 -76 43 -73T57 -61T67 -40T71 -5V5H27V189H152V38Z"  />
<glyph unicode="&lt;" horiz-adv-x="401" d="M15 174V334L380 474V352L120 253L380 154V33L15 174Z"  />
<glyph unicode="=" horiz-adv-x="401" d="M18 284V396H383V284H18ZM18 112V223H383V112H18Z"  />
<glyph unicode="&gt;" horiz-adv-x="401" d="M21 33V154L281 253L21 352V474L386 334V174L21 33Z"  />
<glyph unicode="?" horiz-adv-x="356" d="M116 227V237Q116 271 120 296T131 343T151 383T180 426Q196 449 208 473T220 533Q220 565 211 583T178 602Q151 602 141 578T131 496H10Q10 547 18 588T45 659T97 705T179 722Q264 722 304 675T344 541Q344 512 339 489T324 447T304 412T280 380Q265 360 255 343T238 309T229 272T226 227H116ZM110 0V162H231V0H110Z"  />
<glyph unicode="@" horiz-adv-x="567" d="M548 432Q548 349 531 295T488 208T432 162T377 149Q354 149 339 160T324 195V203H322Q306 177 285 163T234 149Q206 149 187 162T155 198T138 251T132 317Q132 365 143 407T174 482T221 532T281 551Q310 551 331 537T366 492H367L375 533H438L396 285Q392 268 392 249Q392 236 395 230T407 224Q422 224 435 237T459 276T475 336T481 415Q481 665 293 665Q237 665 198 640T133 573T97 474T86 356Q86 291 97 235T131 137T191 71T280 47Q335 47 373 68T432 127H515Q476 49 415 16T277 -17Q209 -17 160 10T80 86T34 204T19 356Q19 445 38 515T93 633T180 706T295 731Q359 731 407 709T486 646T532 551T548 432ZM215 318Q215 269 226 249T261 228Q279 228 294 243T319 282T336 335T342 391Q342 434 331 456T293 478Q275 478 261 465T236 430T221 379T215 318Z"  />
<glyph unicode="A" horiz-adv-x="401" d="M255 0L240 135H160L147 0H-3L100 714H301L405 0H255ZM197 566H195L167 257H230L197 566Z"  />
<glyph unicode="B" horiz-adv-x="409" d="M398 206Q398 96 346 48T204 0H25V714H200Q242 714 276 706T334 677T371 625T384 543Q384 478 366 439T303 382V379Q359 361 378 320T398 206ZM239 517Q239 564 227 582T188 600H173V418H192Q218 418 228 445T239 517ZM247 216Q247 276 234 297T194 318H173V114H190Q220 114 233 139T247 216Z"  />
<glyph unicode="C" horiz-adv-x="398" d="M388 275Q387 197 377 143T345 56T288 7T201 -8Q145 -8 109 12T52 77T23 190T15 357Q15 454 24 523T55 637T116 701T212 722Q263 722 296 707T349 661T377 583T385 471H254Q253 545 244 573T213 602Q200 602 191 594T177 559T169 485T166 357Q166 279 168 231T174 157T187 122T208 113Q219 113 227 119T240 144T248 193T252 275H388H388Z"  />
<glyph unicode="D" horiz-adv-x="419" d="M404 362Q404 281 396 215T365 101T304 27T205 0H25V714H203Q266 714 305 692T366 626T396 515T404 362ZM253 359Q253 435 249 479T236 545T217 574T194 580H173V134H195Q209 134 220 142T238 174T249 243T253 359Z"  />
<glyph unicode="E" horiz-adv-x="342" d="M25 0V714H325V580H173V429H304V304H173V134H329V0H25Z"  />
<glyph unicode="F" horiz-adv-x="328" d="M25 0V714H320V580H173V429H302V304H173V0H25Z"  />
<glyph unicode="G" horiz-adv-x="425" d="M295 0L288 57H285Q264 22 238 7T169 -8Q125 -8 96 13T48 80T23 195T15 361Q15 466 27 536T64 647T128 705T220 722Q272 722 306 707T361 663T390 590T399 489H270Q269 549 259 575T221 602Q204 602 194 594T177 561T169 488T166 362Q166 282 168 233T177 158T192 122T217 113Q230 113 239 119T255 143T265 190T268 267V274H208V386H403V0H295Z"  />
<glyph unicode="H" horiz-adv-x="416" d="M243 0V304H173V0H25V714H173V442H243V714H391V0H243Z"  />
<glyph unicode="I" horiz-adv-x="198" d="M25 0V714H173V0H25Z"  />
<glyph unicode="J" horiz-adv-x="352" d="M327 172Q327 122 316 88T284 32T232 2T159 -8Q122 -8 93 0T44 28T14 85T3 179V195H131V175Q131 143 134 129T154 114Q170 114 174 129T179 172V714H327V172Z"  />
<glyph unicode="K" horiz-adv-x="407" d="M259 0L197 256L173 196V0H25V714H173V436H175L253 714H412L302 417L419 0H259Z"  />
<glyph unicode="L" horiz-adv-x="328" d="M28 0V714H176V134H322V0H28Z"  />
<glyph unicode="M" horiz-adv-x="537" d="M383 0V469H381L329 0H209L157 469H155V0H21V714H221L268 247H270L326 714H516V0H383Z"  />
<glyph unicode="N" horiz-adv-x="430" d="M254 0L161 395H159V0H25V714H179L269 324H271V714H405V0H254Z"  />
<glyph unicode="O" horiz-adv-x="412" d="M398 357Q398 258 390 189T361 76T302 12T205 -8Q146 -8 109 12T51 75T23 188T15 357Q15 451 22 520T50 634T108 700T205 722Q266 722 304 701T363 635T391 521T398 357ZM247 357Q247 435 245 481T239 551T226 583T205 590Q193 590 186 581T174 545T168 474T166 357Q166 280 167 235T173 165T185 133T205 125Q218 125 226 135T238 170T245 241T247 357Z"  />
<glyph unicode="P" horiz-adv-x="397" d="M389 489Q389 372 343 312T201 251H173V0H25V714H191Q236 714 272 706T335 673T375 604T389 489ZM236 484Q236 538 227 561T189 584H173V380H189Q215 380 225 404T236 484Z"  />
<glyph unicode="Q" horiz-adv-x="412" d="M398 357Q398 247 388 175T352 59L412 -30L308 -108L239 -6Q231 -7 223 -7T205 -8Q146 -8 109 12T51 75T23 188T15 357Q15 451 22 520T50 634T108 700T205 722Q266 722 304 701T363 635T391 521T398 357ZM247 357Q247 435 245 481T239 552T226 583T205 591Q193 591 186 581T174 545T168 474T166 357Q166 280 167 235T173 165T185 133T205 125Q218 125 226 135T238 170T245 241T247 357Z"  />
<glyph unicode="R" horiz-adv-x="414" d="M392 534Q392 459 373 418T305 356V353Q329 346 344 336T369 306T382 256T387 178Q388 98 391 58T404 8V0H255Q252 7 251 22T248 57T245 101T244 150Q244 196 241 222T233 262T218 279T194 282H173V0H25V714H216Q309 714 350 667T392 534ZM247 502Q247 549 236 570T195 592H176V396H198Q226 396 236 423T247 502Z"  />
<glyph unicode="S" horiz-adv-x="383" d="M372 205Q372 96 326 44T189 -8Q143 -8 111 3T57 40T25 110T12 222H147Q150 160 158 137T190 114Q227 114 227 171Q227 210 211 235T171 281T120 321T68 368T28 434T12 531Q12 629 58 675T191 722Q240 722 273 708T326 667T354 601T363 509H243Q241 554 234 579T200 604Q176 604 170 587T163 550Q163 514 178 491T217 448T267 411T318 366T356 301T372 205Z"  />
<glyph unicode="T" horiz-adv-x="364" d="M107 0V580H6V714H358V580H256V0H107Z"  />
<glyph unicode="U" horiz-adv-x="402" d="M381 181Q381 81 338 36T201 -9Q150 -9 116 2T60 36T30 94T21 180V714H170V164Q170 142 177 131T201 120Q233 120 233 164V714H381V181H381Z"  />
<glyph unicode="V" horiz-adv-x="396" d="M110 0L-5 714H156L202 231H204L253 714H402L292 0H110Z"  />
<glyph unicode="W" horiz-adv-x="541" d="M315 0L278 417H276L235 0H78L-4 714H139L171 264H173L208 714H347L378 264H380L414 714H546L466 0H315Z"  />
<glyph unicode="X" horiz-adv-x="403" d="M246 0L200 223H198L157 0H-9L97 358L-4 714H157L202 501H203L249 714H410L304 360L413 0H246Z"  />
<glyph unicode="Y" horiz-adv-x="399" d="M128 0V235L-11 714H153L200 431H202L255 714H410L276 235V0H128Z"  />
<glyph unicode="Z" horiz-adv-x="374" d="M11 0V107L185 580H23V714H359V592L190 134H360V0H11Z"  />
<glyph unicode="[" horiz-adv-x="262" d="M51 -177V721H243V621H181V-76H243V-177H51Z"  />
<glyph unicode="\" horiz-adv-x="255" d="M155 -19L-11 731H99L265 -19H155Z"  />
<glyph unicode="]" horiz-adv-x="262" d="M19 -177V-76H82V621H19V721H212V-177H19Z"  />
<glyph unicode="^" horiz-adv-x="409" d="M289 376L205 599L120 376H-1L125 705H283L409 376H289Z"  />
<glyph unicode="_" horiz-adv-x="500" d="M0 -132V-78H500V-132H0Z"  />
<glyph unicode="`" horiz-adv-x="271" d="M122 596L60 742H193L211 596H122Z"  />
<glyph unicode="a" horiz-adv-x="343" d="M201 0Q199 14 198 26T196 53H193Q182 19 160 6T109 -8Q56 -8 34 31T11 152Q11 219 33 256T113 314L142 323Q161 329 171 335T187 351T193 371T195 400Q195 432 192 447T171 462Q156 462 149 447T142 383H24Q23 426 30 459T55 514T101 547T172 558Q204 558 231 553T277 532T308 490T319 420V45Q319 35 320 22T325 0H201ZM195 268H193Q188 261 183 258T167 248Q155 240 149 226T143 175Q143 134 148 120T167 106Q195 106 195 163V268Z"  />
<glyph unicode="b" horiz-adv-x="359" d="M346 283Q346 205 342 150T325 59T290 8T234 -8Q207 -8 187 6T154 56H152V0H22V717H156V499H158Q170 535 190 546T239 558Q269 558 289 544T322 498T340 413T346 283ZM212 274Q212 329 211 361T206 411T198 433T185 438Q178 438 173 433T164 410T158 361T156 276Q156 187 161 150T183 113Q191 113 196 117T205 136T210 184T212 274Z"  />
<glyph unicode="c" horiz-adv-x="330" d="M321 222Q321 153 313 109T287 38T240 2T167 -8Q120 -8 90 6T43 54T20 141T13 275Q13 349 20 402T45 490T94 541T175 558Q254 558 287 510T321 350H203Q203 381 201 401T196 433T188 449T176 454Q168 454 163 450T154 427T149 373T147 272Q147 211 148 176T153 123T162 101T175 96Q183 96 188 101T197 121T202 159T204 222H321H321Z"  />
<glyph unicode="d" horiz-adv-x="359" d="M13 287Q13 363 19 415T37 499T71 544T122 558Q150 558 170 547T201 500H203V717H337V0H207V58H205Q192 23 170 8T119 -8Q84 -8 64 8T32 60T17 152T13 287ZM147 279Q147 220 148 187T153 138T161 117T175 113Q183 113 188 121T197 149T201 200T203 277Q203 329 202 360T197 409T188 432T176 438Q168 438 163 434T154 413T149 365T147 279Z"  />
<glyph unicode="e" horiz-adv-x="342" d="M322 192Q322 162 318 127T298 62T253 12T171 -8Q123 -8 92 8T44 60T20 150T13 283Q13 427 50 492T175 558Q226 558 256 538T302 479T324 381T329 244H141V239Q141 189 142 160T148 116T158 97T173 92Q193 92 198 121T204 192H322ZM208 333Q208 370 207 393T201 431T191 450T175 456Q166 456 160 452T149 434T143 396T141 333H208Z"  />
<glyph unicode="f" horiz-adv-x="227" d="M44 0V443H6V550H44V573Q44 609 49 636T68 683T106 713T168 723Q185 723 197 723T228 721V619Q222 620 220 620T215 620Q195 620 189 610T182 573V550H224V443H178V0H44Z"  />
<glyph unicode="g" horiz-adv-x="361" d="M339 -13Q339 -108 299 -145T173 -183Q126 -183 99 -172T56 -141T36 -93T29 -30H146Q147 -62 153 -75T174 -88Q188 -88 196 -77T205 -26V68H203Q191 33 170 21T121 9Q90 9 70 21T37 64T19 145T13 273Q13 351 18 405T36 493T70 542T125 558Q152 558 173 543T207 494H209V550H339V-13ZM205 279Q205 368 199 402T177 437Q169 437 164 433T154 414T149 367T147 281Q147 226 148 195T153 148T162 128T175 124Q182 124 188 128T197 148T203 195T205 279Z"  />
<glyph unicode="h" horiz-adv-x="365" d="M209 0V407Q209 425 204 434T184 443Q171 443 164 432T156 402V0H22V717H156V501H158Q175 530 196 544T251 558Q303 558 323 528T343 448V0H209Z"  />
<glyph unicode="i" horiz-adv-x="177" d="M22 598V723H155V598H22ZM22 0V550H155V0H22Z"  />
<glyph unicode="j" horiz-adv-x="181" d="M157 -66Q157 -129 127 -156T27 -183Q16 -183 4 -183T-22 -181V-80Q-14 -81 -13 -81T-6 -81Q24 -81 24 -50V550H157V-66ZM24 598V723H157V598H24Z"  />
<glyph unicode="k" horiz-adv-x="367" d="M229 0L176 221L156 166V0H22V717H156V350H159L225 550H364L284 328L371 0H229Z"  />
<glyph unicode="l" horiz-adv-x="182" d="M24 0V717H158V0H24Z"  />
<glyph unicode="m" horiz-adv-x="548" d="M392 0V407Q392 425 387 434T368 443Q356 443 349 433T341 405V0H207V407Q207 425 202 434T183 443Q170 443 163 432T156 402V0H22V550H152V494H154Q170 528 193 543T250 558Q291 558 311 539T338 487Q353 520 377 539T437 558Q489 558 507 529T526 448V0H392Z"  />
<glyph unicode="n" horiz-adv-x="365" d="M209 0V407Q209 425 204 434T184 443Q171 443 164 432T156 402V0H22V550H152V494H154Q171 528 194 543T251 558Q303 558 323 528T343 448V0H209Z"  />
<glyph unicode="o" horiz-adv-x="351" d="M338 275Q338 198 331 145T306 57T256 8T175 -8Q126 -8 95 7T45 57T20 145T13 275Q13 346 19 399T43 487T92 540T175 558Q227 558 259 541T308 488T332 399T338 275ZM204 275Q204 335 203 370T199 424T190 448T175 454Q167 454 162 447T153 420T149 365T147 275Q147 215 148 180T153 126T161 102T175 97Q184 97 189 104T198 130T203 184T204 275Z"  />
<glyph unicode="p" horiz-adv-x="359" d="M346 263Q346 186 341 135T322 51T289 6T238 -8Q210 -8 190 3T158 50H156V-180H22V550H152V492H154Q167 527 189 542T240 558Q275 558 296 542T328 490T342 398T346 263ZM212 271Q212 329 211 362T206 412T198 433T185 437Q177 437 172 429T163 401T158 350T156 273Q156 221 157 190T162 141T171 118T184 112Q191 112 196 116T205 137T210 185T212 271Z"  />
<glyph unicode="q" horiz-adv-x="359" d="M13 268Q13 346 18 401T35 491T69 542T123 558Q151 558 171 543T205 494H207V550H337V-180H203V51H201Q189 16 168 4T119 -8Q89 -8 69 6T36 52T18 137T13 268ZM147 275Q147 221 148 189T153 139T162 117T175 112Q182 112 187 117T196 140T201 189T203 274Q203 363 198 400T176 437Q168 437 163 433T154 413T149 365T147 275Z"  />
<glyph unicode="r" horiz-adv-x="247" d="M22 0V550H153V476H155Q163 517 180 537T229 558Q231 558 234 558T243 557V410Q233 411 227 411T212 412Q180 412 168 394T156 350V0H22Z"  />
<glyph unicode="s" horiz-adv-x="318" d="M308 163Q308 125 301 94T276 40T229 5T154 -8Q78 -8 47 34T12 169H132Q134 131 138 108T160 85Q177 85 180 101T184 134Q184 163 172 181T140 215T98 244T57 278T25 327T12 402Q12 480 48 519T161 558Q206 558 233 546T276 511T296 459T301 392H193Q192 421 188 441T164 461Q148 461 144 447T140 417Q140 392 152 377T183 348T224 321T264 287T295 238T308 163Z"  />
<glyph unicode="t" horiz-adv-x="236" d="M224 3Q207 0 188 -2T147 -4Q115 -4 95 3T64 24T49 58T45 103V443H6V550H45V696H179V550H224V443H179V128Q179 114 186 111T202 107Q212 107 224 110V3H224Z"  />
<glyph unicode="u" horiz-adv-x="362" d="M210 0V54H208Q194 19 171 6T115 -8Q65 -8 43 21T21 102V550H154V143Q154 107 178 107Q191 107 198 118T206 148V550H340V0H210Z"  />
<glyph unicode="v" horiz-adv-x="338" d="M84 0L-2 550H137L168 150H171L207 550H342L250 0H84Z"  />
<glyph unicode="w" horiz-adv-x="495" d="M279 0L255 364H253L223 0H77L2 550H140L157 196H159L193 550H324L348 196H350L374 550H495L426 0H279Z"  />
<glyph unicode="x" horiz-adv-x="350" d="M209 0L169 175L131 0H-8L83 286L-3 550H145L183 380L223 550H359L270 275L357 0H209Z"  />
<glyph unicode="y" horiz-adv-x="342" d="M244 -23Q236 -68 227 -99T200 -148T156 -175T85 -183Q67 -183 54 -183T25 -180V-79Q44 -82 58 -82Q77 -82 85 -75T96 -51Q97 -42 97 -33T95 -13L-4 550H137L172 170H173L212 550H348L244 -23H244Z"  />
<glyph unicode="z" horiz-adv-x="301" d="M10 0V95L133 443H12V550H288V461L156 108H290V0H10Z"  />
<glyph unicode="{" horiz-adv-x="292" d="M272 625H255Q230 625 217 614T204 562V417Q204 376 200 351T187 309T160 286T118 274V272Q145 267 162 258T188 232T200 192T204 135V-15Q204 -56 215 -68T246 -80H272V-177H200Q160 -177 136 -166T97 -136T78 -93T73 -44V96Q73 133 72 156T64 192T47 211T15 216V331Q35 331 46 336T64 355T71 393T73 456V591Q73 616 78 639T97 681T135 710T200 721H272V625Z"  />
<glyph unicode="|" horiz-adv-x="184" d="M38 -19V731H146V-19H38Z"  />
<glyph unicode="}" horiz-adv-x="292" d="M20 721H92Q132 721 156 710T195 681T214 640T219 591V456Q219 418 220 394T228 355T245 336T277 331V216Q257 216 246 211T228 193T221 156T219 96V-44Q219 -69 214 -93T195 -135T157 -165T92 -177H20V-80H46Q66 -80 77 -68T88 -15V135Q88 168 91 191T104 231T130 257T174 272V274Q148 278 132 285T106 309T92 350T88 417V562Q88 603 75 614T37 625H20V721Z"  />
<glyph unicode="~" horiz-adv-x="436" d="M432 252Q412 217 381 194T307 171Q277 171 256 180T217 201T181 221T137 231Q112 231 91 217T48 170L3 286Q23 321 54 344T128 368Q161 368 183 359T224 338T259 317T298 307Q323 307 345 321T387 369L432 252H432Z"  />
<glyph unicode="&#xA0;" horiz-adv-x="180" />
<glyph unicode="&#xA1;" horiz-adv-x="199" d="M28 -164L37 41L56 323H149L167 41L175 -164H28ZM163 388H41V550H163V388Z"  />
<glyph unicode="&#xA2;" horiz-adv-x="392" d="M351 222Q351 162 345 121T326 53T291 12T238 -6V-74H167V-7Q129 -3 105 14T67 65T48 150T43 275Q43 341 48 391T67 475T105 529T167 555V618H238V555Q299 546 325 498T351 350H233Q233 381 232 401T227 433T219 449T206 454Q198 454 193 450T184 427T180 373T178 272Q178 211 179 176T184 123T193 101T205 96Q213 96 218 101T227 121T232 159T234 222H351H351Z"  />
<glyph unicode="&#xA3;" horiz-adv-x="401" d="M252 284Q253 276 253 269T254 254Q254 208 234 174T179 118L181 116Q191 121 199 122T213 123Q227 123 238 120T259 114T281 108T308 105Q348 105 383 135L419 25Q385 3 356 -4T295 -11Q271 -11 251 -7T212 4T175 14T138 19Q115 19 97 11T60 -8L17 80Q48 99 68 115T99 150T114 189T118 239Q118 251 116 262T111 284H17V368H73Q58 400 47 439T35 535Q35 615 81 660T210 706Q252 706 285 696T341 661T376 595T388 494H260Q259 548 250 569T219 590Q181 590 181 523Q181 504 186 486T198 449T215 410T232 368H340V284H252Z"  />
<glyph unicode="&#xA4;" horiz-adv-x="401" d="M263 346Q263 373 246 391T201 409Q175 409 157 393T138 346Q138 318 156 301T201 284Q227 284 245 301T263 346ZM362 346Q362 308 348 277L386 240L307 159L270 196Q238 181 201 181Q162 181 132 196L94 158L15 238L52 276Q38 309 38 346Q38 383 52 414L16 451L95 532L131 495Q147 502 164 506T201 510Q219 510 236 506T270 495L307 533L386 453L348 414Q354 400 358 383T362 346Z"  />
<glyph unicode="&#xA5;" horiz-adv-x="401" d="M128 0V116H29V202H128V258H29V344H102L-10 695H153L200 427H202L254 695H411L302 344H371V258H277V202H371V116H277V0H128Z"  />
<glyph unicode="&#xA6;" horiz-adv-x="184" d="M38 442V731H146V442H38ZM38 -19V279H146V-19H38Z"  />
<glyph unicode="&#xA7;" horiz-adv-x="420" d="M388 31Q388 -57 339 -102T209 -148Q123 -148 81 -106T33 38H161Q163 -5 177 -21T215 -38Q232 -38 246 -25T260 15Q260 39 242 57T195 91T135 125T75 166T29 220T10 296Q10 341 28 374T87 437Q67 460 55 491T42 565Q42 642 89 683T217 724Q265 724 296 710T345 674T371 621T379 560H259Q258 585 248 600T215 615Q195 615 185 603T175 573Q175 550 192 533T236 499T293 466T350 425T394 371T412 296Q412 254 394 221T337 159Q360 136 374 105T388 31ZM290 266Q290 286 279 300T248 328T206 353T158 380Q131 358 131 329Q131 309 142 294T172 266T214 241T262 214Q276 225 283 237T290 266Z"  />
<glyph unicode="&#xA8;" horiz-adv-x="271" d="M23 729H116V601H23V729ZM155 729H247V601H155V729Z"  />
<glyph unicode="&#xA9;" horiz-adv-x="530" d="M513 357Q513 283 499 220T455 110T378 38T265 12Q199 12 152 38T76 110T32 220T18 357Q18 431 32 494T75 604T152 676T265 702Q331 702 378 676T455 604T499 495T513 357ZM459 357Q459 422 449 476T416 570T356 631T265 653Q212 653 176 632T116 571T83 478T72 357Q72 292 82 238T115 144T175 83T265 61Q318 61 355 82T415 143T448 236T459 357ZM386 306Q385 211 356 174T262 137Q228 137 205 149T166 187T145 254T138 355Q138 474 167 524T268 574Q333 574 358 539T384 423H290Q290 471 286 485T270 500Q262 500 258 496T250 476T246 432T245 355Q245 308 246 280T249 237T255 217T266 212Q272 212 276 215T283 228T287 256T289 306H386Z"  />
<glyph unicode="&#xAA;" horiz-adv-x="232" d="M135 361Q131 376 131 397H129Q116 356 73 356Q8 356 8 454Q8 496 22 520T72 555L95 561Q118 569 123 577T129 603Q129 620 127 628T115 637Q106 637 103 628T100 596H15Q15 648 38 675T119 702Q140 702 158 699T190 685T210 658T218 613V393Q218 387 219 377T222 361H135ZM129 522H128Q125 518 115 513Q107 508 104 500T101 472Q101 451 104 442T115 433Q128 433 129 451V522Z"  />
<glyph unicode="&#xAB;" horiz-adv-x="352" d="M327 346L257 290L327 233V84L183 203V373L327 495V346ZM166 346L95 290L166 233V84L21 203V373L166 495V346Z"  />
<glyph unicode="&#xAC;" horiz-adv-x="401" d="M383 375V130H270V262H18V375H383Z"  />
<glyph unicode="&#xAD;" horiz-adv-x="255" d="M15 234V370H241V234H15Z"  />
<glyph unicode="&#xAE;" horiz-adv-x="530" d="M513 357Q513 283 499 220T455 110T378 38T265 12Q199 12 152 38T76 110T32 220T18 357Q18 431 32 494T75 604T152 676T265 702Q331 702 378 676T455 604T499 495T513 357ZM459 357Q459 422 449 476T416 570T356 631T265 653Q212 653 176 632T116 571T83 478T72 357Q72 292 82 238T115 144T175 83T265 61Q318 61 355 82T415 143T448 236T459 357ZM387 457Q387 411 374 387T333 353V351Q348 346 357 340T371 323T379 294T383 246Q385 198 387 176T396 147V143H299Q296 152 294 175T292 228Q292 258 291 275T286 300T278 311T265 313H253V143H157V571H272Q334 571 360 546T387 457ZM293 440Q293 472 287 485T266 498H255V378H266Q282 378 287 392T293 440Z"  />
<glyph unicode="&#xAF;" horiz-adv-x="271" d="M7 621V705H263V621H7Z"  />
<glyph unicode="&#xB0;" horiz-adv-x="277" d="M275 569Q275 538 265 513T236 469T193 441T139 431Q110 431 86 441T42 469T13 512T2 569Q2 600 12 625T41 669T85 698T139 708Q166 708 191 699T234 671T264 627T275 569ZM201 569Q201 597 184 615T139 633Q113 633 95 616T77 569Q77 542 94 525T139 507Q165 507 183 524T201 569Z"  />
<glyph unicode="&#xB1;" horiz-adv-x="401" d="M18 26V137H383V26H18ZM257 249V161H144V249H18V360H144V448H257V360H383V249H257Z"  />
<glyph unicode="&#xB2;" horiz-adv-x="273" d="M19 403V423Q19 466 41 504T103 593Q129 626 143 651T158 709Q158 730 153 738T137 747Q126 747 121 732T115 688H18Q18 721 24 747T44 792T82 819T140 829Q199 829 229 800T260 708Q260 682 253 660T235 620T211 585T186 556Q169 537 156 520T136 486H257V403H19Z"  />
<glyph unicode="&#xB3;" horiz-adv-x="273" d="M259 528Q259 487 247 462T217 422T176 403T131 398Q63 398 36 433T9 538H108Q110 500 115 487T132 473Q144 473 150 484T157 527Q157 546 155 557T148 575T133 584T106 587V654Q135 654 144 663T154 707Q154 731 149 740T132 749Q118 749 114 735T110 696H13Q17 764 45 796T138 828Q161 828 182 823T220 805T245 772T255 722Q255 679 239 657T195 626V624Q230 615 244 593T259 528Z"  />
<glyph unicode="&#xB4;" horiz-adv-x="271" d="M149 596H60L78 742H211L149 596Z"  />
<glyph unicode="&#xB5;" horiz-adv-x="362" d="M210 0V56H208Q204 36 197 25T181 7Q167 -2 154 3V-183H21V550H154V139Q155 107 178 107Q191 107 198 118T206 148V550H340V0H210Z"  />
<glyph unicode="&#xB6;" horiz-adv-x="451" d="M409 714V-140H319V616H263V-140H173V327Q142 328 114 341T63 380T28 440T15 519Q15 611 64 662T200 714H409Z"  />
<glyph unicode="&#xB7;" horiz-adv-x="180" d="M29 203V400H151V203H29Z"  />
<glyph unicode="&#xB8;" horiz-adv-x="271" d="M233 -130Q233 -176 203 -198T125 -220Q99 -220 78 -215T43 -202L60 -155Q74 -162 87 -164T113 -166Q133 -166 145 -158T158 -130Q158 -99 124 -99Q114 -99 108 -101T94 -106L71 -96L101 0H150L131 -57L133 -58Q140 -56 146 -56T158 -55Q233 -55 233 -130Z"  />
<glyph unicode="&#xB9;" horiz-adv-x="273" d="M104 403V698H36V769Q80 773 100 786T132 829H205V403H104Z"  />
<glyph unicode="&#xBA;" horiz-adv-x="238" d="M230 529Q230 439 207 398T118 356Q86 356 65 366T31 397T13 451T8 529Q8 571 13 603T30 657T64 690T119 702Q153 702 175 691T209 658T225 604T230 529ZM134 529Q134 559 134 578T132 608T127 623T119 627Q114 627 112 624T107 610T105 580T104 529Q104 494 104 475T107 446T111 434T119 431Q124 431 127 434T131 448T133 478T134 529Z"  />
<glyph unicode="&#xBB;" horiz-adv-x="352" d="M187 84V233L257 290L187 346V495L332 373V203L187 84ZM25 84V233L95 290L25 346V495L170 373V203L25 84Z"  />
<glyph unicode="&#xBC;" horiz-adv-x="607" d="M104 277V572H36V643Q80 647 100 660T132 703H205V277H104ZM383 708H471L233 -8H145L383 708ZM477 1V74H341V154L453 422H568V153H602V74H568V1H477ZM477 295H475L422 153H477V295Z"  />
<glyph unicode="&#xBD;" horiz-adv-x="635" d="M381 1V21Q381 64 403 102T465 191Q491 224 505 249T520 307Q520 328 515 336T499 345Q488 345 483 330T477 286H380Q380 319 386 345T406 390T444 417T502 427Q561 427 591 398T622 306Q622 280 615 258T597 218T573 183T548 154Q531 135 518 118T498 84H619V1H381ZM104 277V572H36V643Q80 647 100 660T132 703H205V277H104ZM383 708H471L233 -8H145L383 708Z"  />
<glyph unicode="&#xBE;" horiz-adv-x="629" d="M259 402Q259 361 247 336T217 296T176 277T131 272Q63 272 36 307T9 412H108Q110 374 115 361T132 347Q144 347 150 358T157 401Q157 420 155 431T148 449T133 458T106 461V528Q135 528 144 537T154 581Q154 605 149 614T132 623Q118 623 114 609T110 570H13Q17 638 45 670T138 702Q161 702 182 697T220 679T245 646T255 596Q255 553 239 531T195 500V498Q230 489 244 467T259 402ZM405 708H493L255 -8H167L405 708ZM499 1V74H363V154L475 422H590V153H624V74H590V1H499ZM499 295H497L444 153H499V295Z"  />
<glyph unicode="&#xBF;" horiz-adv-x="356" d="M178 -172Q93 -172 53 -125T13 9Q13 37 18 60T33 103T53 138T76 170Q92 190 102 207T119 241T128 278T131 323H240V313Q240 279 236 254T225 207T205 166T176 123Q160 101 148 77T136 17Q136 -15 145 -33T178 -52Q206 -52 216 -28T226 54H347Q347 3 339 -38T312 -109T260 -155T178 -172ZM247 388H125V550H247V388Z"  />
<glyph unicode="&#xC0;" horiz-adv-x="404" d="M256 0L241 135H161L148 0H-2L101 714H302L406 0H256ZM198 566H196L168 257H231L198 566ZM98 906H231L249 760H160L98 906Z"  />
<glyph unicode="&#xC1;" horiz-adv-x="404" d="M256 0L241 135H161L148 0H-2L101 714H302L406 0H256ZM198 566H196L168 257H231L198 566ZM246 760H157L175 906H308L246 760Z"  />
<glyph unicode="&#xC2;" horiz-adv-x="404" d="M256 0L241 135H161L148 0H-2L101 714H302L406 0H256ZM198 566H196L168 257H231L198 566ZM248 760L204 838L158 760H70L125 906H282L337 760H248Z"  />
<glyph unicode="&#xC3;" horiz-adv-x="404" d="M256 0L241 135H161L148 0H-2L101 714H302L406 0H256ZM198 566H196L168 257H231L198 566ZM344 893Q342 831 323 796T260 760Q245 760 233 765T211 777T189 789T166 795Q155 795 148 786T140 760H75Q76 822 95 858T158 894Q173 894 185 889T207 876T229 864T252 858Q263 858 270 868T279 893H344H344Z"  />
<glyph unicode="&#xC4;" horiz-adv-x="404" d="M256 0L241 135H161L148 0H-2L101 714H302L406 0H256ZM198 566H196L168 257H231L198 566ZM91 764V893H184V764H91ZM223 764V893H315V764H223Z"  />
<glyph unicode="&#xC5;" horiz-adv-x="404" d="M256 0L241 135H161L148 0H-2L101 714H302L406 0H256ZM198 566H196L168 257H231L198 566ZM318 854Q318 802 286 771T205 739Q182 739 162 747T126 771T101 807T92 854Q92 879 101 900T125 937T160 960T205 969Q227 969 247 961T284 939T309 903T318 854ZM257 854Q257 877 243 892T205 908Q184 908 169 894T153 854Q153 831 167 816T205 801Q227 801 242 816T257 854Z"  />
<glyph unicode="&#xC6;" horiz-adv-x="554" d="M254 0V129H177L155 0H-9L159 714H537V580H401V429H516V304H401V134H541V0H254ZM250 560L195 250H254V560H250Z"  />
<glyph unicode="&#xC7;" horiz-adv-x="398" d="M306 -130Q306 -176 276 -198T198 -220Q172 -220 151 -215T116 -202L133 -155Q147 -162 160 -164T186 -166Q206 -166 218 -158T231 -130Q231 -99 197 -99Q187 -99 181 -101T167 -106L144 -96L172 -6Q125 -1 95 22T47 89T22 199T15 357Q15 454 24 523T55 637T116 701T212 722Q263 722 296 707T349 661T377 583T385 471H254Q253 545 244 573T213 602Q200 602 191 594T177 559T169 485T166 357Q166 279 168 231T174 157T187 122T208 113Q219 113 227 119T240 144T248 193T252 275H388Q387 201 378 149T349 63T298 13T220 -7L204 -57L206 -58Q213 -56 219 -56T231 -55Q306 -55 306 -130Z"  />
<glyph unicode="&#xC8;" horiz-adv-x="342" d="M25 0V714H325V580H173V429H304V304H173V134H329V0H25ZM72 906H205L223 760H134L72 906Z"  />
<glyph unicode="&#xC9;" horiz-adv-x="342" d="M25 0V714H325V580H173V429H304V304H173V134H329V0H25ZM218 760H129L147 906H280L218 760Z"  />
<glyph unicode="&#xCA;" horiz-adv-x="342" d="M25 0V714H325V580H173V429H304V304H173V134H329V0H25ZM221 760L177 838L131 760H43L98 906H255L310 760H221Z"  />
<glyph unicode="&#xCB;" horiz-adv-x="342" d="M25 0V714H325V580H173V429H304V304H173V134H329V0H25ZM66 764V893H159V764H66ZM198 764V893H290V764H198Z"  />
<glyph unicode="&#xCC;" horiz-adv-x="198" d="M25 0V714H173V0H25ZM-2 906H131L149 760H60L-2 906Z"  />
<glyph unicode="&#xCD;" horiz-adv-x="198" d="M25 0V714H173V0H25ZM146 760H57L75 906H208L146 760Z"  />
<glyph unicode="&#xCE;" horiz-adv-x="198" d="M25 0V714H173V0H25ZM148 760L104 838L58 760H-30L25 906H182L237 760H148Z"  />
<glyph unicode="&#xCF;" horiz-adv-x="198" d="M25 0V714H173V0H25ZM-13 764V893H80V764H-13ZM119 764V893H211V764H119Z"  />
<glyph unicode="&#xD0;" horiz-adv-x="427" d="M6 412H34V714H211Q274 714 313 692T374 626T404 515T412 362Q412 281 404 215T373 101T312 27T213 0H34V328H6V412ZM261 359Q261 435 257 479T244 545T226 574T204 580H183V412H225V328H183V134H205Q219 134 229 142T247 174T257 243T261 359Z"  />
<glyph unicode="&#xD1;" horiz-adv-x="430" d="M254 0L161 395H159V0H25V714H179L269 324H271V714H405V0H254ZM361 893Q359 831 340 796T277 760Q262 760 250 765T228 777T206 789T183 795Q172 795 165 786T157 760H92Q93 822 112 858T175 894Q190 894 202 889T224 876T246 864T269 858Q280 858 287 868T296 893H361H361Z"  />
<glyph unicode="&#xD2;" horiz-adv-x="412" d="M398 357Q398 258 390 189T361 76T302 12T205 -8Q146 -8 109 12T51 75T23 188T15 357Q15 451 22 520T50 634T108 700T205 722Q266 722 304 701T363 635T391 521T398 357ZM247 357Q247 435 245 481T239 551T226 583T205 590Q193 590 186 581T174 545T168 474T166 357Q166 280 167 235T173 165T185 133T205 125Q218 125 226 135T238 170T245 241T247 357ZM99 906H232L250 760H161L99 906Z"  />
<glyph unicode="&#xD3;" horiz-adv-x="412" d="M398 357Q398 258 390 189T361 76T302 12T205 -8Q146 -8 109 12T51 75T23 188T15 357Q15 451 22 520T50 634T108 700T205 722Q266 722 304 701T363 635T391 521T398 357ZM247 357Q247 435 245 481T239 551T226 583T205 590Q193 590 186 581T174 545T168 474T166 357Q166 280 167 235T173 165T185 133T205 125Q218 125 226 135T238 170T245 241T247 357ZM248 760H159L177 906H310L248 760Z"  />
<glyph unicode="&#xD4;" horiz-adv-x="412" d="M398 357Q398 258 390 189T361 76T302 12T205 -8Q146 -8 109 12T51 75T23 188T15 357Q15 451 22 520T50 634T108 700T205 722Q266 722 304 701T363 635T391 521T398 357ZM247 357Q247 435 245 481T239 551T226 583T205 590Q193 590 186 581T174 545T168 474T166 357Q166 280 167 235T173 165T185 133T205 125Q218 125 226 135T238 170T245 241T247 357ZM252 760L208 838L162 760H74L129 906H286L341 760H252Z"  />
<glyph unicode="&#xD5;" horiz-adv-x="412" d="M398 357Q398 258 390 189T361 76T302 12T205 -8Q146 -8 109 12T51 75T23 188T15 357Q15 451 22 520T50 634T108 700T205 722Q266 722 304 701T363 635T391 521T398 357ZM247 357Q247 435 245 481T239 551T226 583T205 590Q193 590 186 581T174 545T168 474T166 357Q166 280 167 235T173 165T185 133T205 125Q218 125 226 135T238 170T245 241T247 357ZM346 893Q344 831 325 796T262 760Q247 760 235 765T213 777T191 789T168 795Q157 795 150 786T142 760H77Q78 822 97 858T160 894Q175 894 187 889T209 876T231 864T254 858Q265 858 272 868T281 893H346H346Z"  />
<glyph unicode="&#xD6;" horiz-adv-x="412" d="M398 357Q398 258 390 189T361 76T302 12T205 -8Q146 -8 109 12T51 75T23 188T15 357Q15 451 22 520T50 634T108 700T205 722Q266 722 304 701T363 635T391 521T398 357ZM247 357Q247 435 245 481T239 551T226 583T205 590Q193 590 186 581T174 545T168 474T166 357Q166 280 167 235T173 165T185 133T205 125Q218 125 226 135T238 170T245 241T247 357ZM94 764V893H187V764H94ZM226 764V893H318V764H226Z"  />
<glyph unicode="&#xD7;" horiz-adv-x="401" d="M307 67L201 173L94 66L15 146L122 253L16 359L95 440L201 334L307 441L386 361L280 254L386 148L307 67Z"  />
<glyph unicode="&#xD8;" horiz-adv-x="412" d="M39 -36L67 48Q36 90 26 164T15 357Q15 451 22 520T50 634T108 700T205 722Q254 722 290 707L310 767L373 745L345 663Q377 620 387 545T398 357Q398 258 390 189T361 76T302 12T205 -8Q180 -8 160 -5T122 5L101 -58L39 -36ZM162 328L240 559Q235 583 227 590T205 597Q193 597 185 587T171 551T164 478T162 357V328ZM205 118Q218 118 226 128T240 164T248 237T250 357V381L172 152Q177 131 185 125T205 118Z"  />
<glyph unicode="&#xD9;" horiz-adv-x="402" d="M381 181Q381 81 338 36T201 -9Q150 -9 116 2T60 36T30 94T21 180V714H170V164Q170 142 177 131T201 120Q233 120 233 164V714H381V181H381ZM95 906H228L246 760H157L95 906Z"  />
<glyph unicode="&#xDA;" horiz-adv-x="402" d="M381 181Q381 81 338 36T201 -9Q150 -9 116 2T60 36T30 94T21 180V714H170V164Q170 142 177 131T201 120Q233 120 233 164V714H381V181H381ZM246 760H157L175 906H308L246 760Z"  />
<glyph unicode="&#xDB;" horiz-adv-x="402" d="M381 181Q381 81 338 36T201 -9Q150 -9 116 2T60 36T30 94T21 180V714H170V164Q170 142 177 131T201 120Q233 120 233 164V714H381V181H381ZM247 760L203 838L157 760H69L124 906H281L336 760H247Z"  />
<glyph unicode="&#xDC;" horiz-adv-x="402" d="M381 181Q381 81 338 36T201 -9Q150 -9 116 2T60 36T30 94T21 180V714H170V164Q170 142 177 131T201 120Q233 120 233 164V714H381V181H381ZM90 764V893H183V764H90ZM222 764V893H314V764H222Z"  />
<glyph unicode="&#xDD;" horiz-adv-x="399" d="M128 0V235L-11 714H153L200 431H202L255 714H410L276 235V0H128ZM250 760H161L179 906H312L250 760Z"  />
<glyph unicode="&#xDE;" horiz-adv-x="397" d="M389 379Q389 263 343 202T200 140H173V0H25V714H173V603H192Q236 603 272 595T334 562T374 493T389 379ZM236 373Q236 427 226 450T190 474H173V269H189Q216 269 226 294T236 373Z"  />
<glyph unicode="&#xDF;" horiz-adv-x="396" d="M382 216Q382 112 348 53T233 -7Q222 -7 210 -7T184 -3V99Q190 98 193 98T200 98Q226 98 237 125T248 221Q248 290 237 318T189 347H185V448H188Q204 448 213 455T228 475T235 502T237 535Q237 587 227 603T196 620Q173 620 165 601T156 536V0H22V540Q22 634 63 678T198 722Q276 722 321 683T367 556Q367 489 347 454T291 407V405Q343 389 362 345T382 216Z"  />
<glyph unicode="&#xE0;" horiz-adv-x="344" d="M202 0Q200 14 199 26T197 53H194Q183 19 161 6T110 -8Q57 -8 35 31T12 152Q12 219 34 256T114 314L143 323Q162 329 172 335T188 351T194 371T196 400Q196 432 193 447T172 462Q157 462 150 447T143 383H25Q24 426 31 459T56 514T102 547T173 558Q205 558 232 553T278 532T309 490T320 420V45Q320 35 321 22T326 0H202ZM196 268H194Q189 261 184 258T168 248Q156 240 150 226T144 175Q144 134 149 120T168 106Q196 106 196 163V268ZM137 596L75 742H208L226 596H137Z"  />
<glyph unicode="&#xE1;" horiz-adv-x="344" d="M202 0Q200 14 199 26T197 53H194Q183 19 161 6T110 -8Q57 -8 35 31T12 152Q12 219 34 256T114 314L143 323Q162 329 172 335T188 351T194 371T196 400Q196 432 193 447T172 462Q157 462 150 447T143 383H25Q24 426 31 459T56 514T102 547T173 558Q205 558 232 553T278 532T309 490T320 420V45Q320 35 321 22T326 0H202ZM196 268H194Q189 261 184 258T168 248Q156 240 150 226T144 175Q144 134 149 120T168 106Q196 106 196 163V268ZM224 596H135L153 742H286L224 596Z"  />
<glyph unicode="&#xE2;" horiz-adv-x="344" d="M202 0Q200 14 199 26T197 53H194Q183 19 161 6T110 -8Q57 -8 35 31T12 152Q12 219 34 256T114 314L143 323Q162 329 172 335T188 351T194 371T196 400Q196 432 193 447T172 462Q157 462 150 447T143 383H25Q24 426 31 459T56 514T102 547T173 558Q205 558 232 553T278 532T309 490T320 420V45Q320 35 321 22T326 0H202ZM196 268H194Q189 261 184 258T168 248Q156 240 150 226T144 175Q144 134 149 120T168 106Q196 106 196 163V268ZM220 596L176 675L130 596H42L97 742H254L309 596H220Z"  />
<glyph unicode="&#xE3;" horiz-adv-x="344" d="M202 0Q200 14 199 26T197 53H194Q183 19 161 6T110 -8Q57 -8 35 31T12 152Q12 219 34 256T114 314L143 323Q162 329 172 335T188 351T194 371T196 400Q196 432 193 447T172 462Q157 462 150 447T143 383H25Q24 426 31 459T56 514T102 547T173 558Q205 558 232 553T278 532T309 490T320 420V45Q320 35 321 22T326 0H202ZM196 268H194Q189 261 184 258T168 248Q156 240 150 226T144 175Q144 134 149 120T168 106Q196 106 196 163V268ZM315 730Q313 667 294 632T231 596Q216 596 204 601T182 614T160 626T137 632Q126 632 119 622T111 597H46Q47 659 66 695T129 731Q144 731 156 726T178 713T200 701T223 695Q234 695 241 704T250 730H315H315Z"  />
<glyph unicode="&#xE4;" horiz-adv-x="344" d="M202 0Q200 14 199 26T197 53H194Q183 19 161 6T110 -8Q57 -8 35 31T12 152Q12 219 34 256T114 314L143 323Q162 329 172 335T188 351T194 371T196 400Q196 432 193 447T172 462Q157 462 150 447T143 383H25Q24 426 31 459T56 514T102 547T173 558Q205 558 232 553T278 532T309 490T320 420V45Q320 35 321 22T326 0H202ZM196 268H194Q189 261 184 258T168 248Q156 240 150 226T144 175Q144 134 149 120T168 106Q196 106 196 163V268ZM66 729H159V601H66V729ZM198 729H290V601H198V729Z"  />
<glyph unicode="&#xE5;" horiz-adv-x="344" d="M202 0Q200 14 199 26T197 53H194Q183 19 161 6T110 -8Q57 -8 35 31T12 152Q12 219 34 256T114 314L143 323Q162 329 172 335T188 351T194 371T196 400Q196 432 193 447T172 462Q157 462 150 447T143 383H25Q24 426 31 459T56 514T102 547T173 558Q205 558 232 553T278 532T309 490T320 420V45Q320 35 321 22T326 0H202ZM196 268H194Q189 261 184 258T168 248Q156 240 150 226T144 175Q144 134 149 120T168 106Q196 106 196 163V268ZM294 691Q294 638 262 607T181 575Q134 575 101 606T68 691Q68 716 77 737T101 773T136 796T181 805Q203 805 223 797T260 775T285 739T294 691ZM233 691Q233 714 219 729T181 745Q160 745 145 731T129 691Q129 668 143 653T181 638Q203 638 218 653T233 691Z"  />
<glyph unicode="&#xE6;" horiz-adv-x="527" d="M326 244V239Q326 189 328 160T333 116T343 97T358 92Q379 92 384 121T390 192H507Q507 162 503 127T484 62T439 12T356 -8Q338 -8 320 -6T286 6T255 31T228 74H227Q207 25 181 9T117 -8Q63 -8 38 31T13 152Q13 219 35 257T115 314L144 323Q163 329 173 335T189 350T195 371T196 400Q196 432 193 447T173 462Q157 462 151 447T144 383H25Q24 422 31 454T55 509T99 545T165 558Q198 558 222 550T262 520H264Q286 544 312 551T362 558Q412 558 442 538T488 479T509 381T514 244H326ZM394 333Q394 370 393 393T388 431T378 450T362 456Q352 456 346 452T335 434T328 396T326 333H394ZM196 268H194Q190 261 185 258T168 247Q162 243 158 239T151 227T147 207T145 177Q145 135 150 121T169 106Q196 106 196 163V268Z"  />
<glyph unicode="&#xE7;" horiz-adv-x="330" d="M264 -130Q264 -176 234 -198T156 -220Q130 -220 109 -215T74 -202L91 -155Q105 -162 118 -164T144 -166Q164 -166 176 -158T189 -130Q189 -99 155 -99Q145 -99 139 -101T125 -106L102 -96L130 -5Q94 0 72 17T36 68T18 153T13 275Q13 349 20 402T45 490T94 541T175 558Q254 558 287 510T321 350H203Q203 381 201 401T196 433T188 449T176 454Q168 454 163 450T154 427T149 373T147 272Q147 211 148 176T153 123T162 101T175 96Q183 96 188 101T197 121T202 159T204 222H321Q321 156 314 112T289 42T245 4T178 -8L162 -57L164 -58Q171 -56 177 -56T189 -55Q264 -55 264 -130Z"  />
<glyph unicode="&#xE8;" horiz-adv-x="342" d="M322 192Q322 162 318 127T298 62T253 12T171 -8Q123 -8 92 8T44 60T20 150T13 283Q13 427 50 492T175 558Q226 558 256 538T302 479T324 381T329 244H141V239Q141 189 142 160T148 116T158 97T173 92Q193 92 198 121T204 192H322ZM208 333Q208 370 207 393T201 431T191 450T175 456Q166 456 160 452T149 434T143 396T141 333H208ZM135 596L73 742H206L224 596H135Z"  />
<glyph unicode="&#xE9;" horiz-adv-x="342" d="M322 192Q322 162 318 127T298 62T253 12T171 -8Q123 -8 92 8T44 60T20 150T13 283Q13 427 50 492T175 558Q226 558 256 538T302 479T324 381T329 244H141V239Q141 189 142 160T148 116T158 97T173 92Q193 92 198 121T204 192H322ZM208 333Q208 370 207 393T201 431T191 450T175 456Q166 456 160 452T149 434T143 396T141 333H208ZM220 596H131L149 742H282L220 596Z"  />
<glyph unicode="&#xEA;" horiz-adv-x="342" d="M322 192Q322 162 318 127T298 62T253 12T171 -8Q123 -8 92 8T44 60T20 150T13 283Q13 427 50 492T175 558Q226 558 256 538T302 479T324 381T329 244H141V239Q141 189 142 160T148 116T158 97T173 92Q193 92 198 121T204 192H322ZM208 333Q208 370 207 393T201 431T191 450T175 456Q166 456 160 452T149 434T143 396T141 333H208ZM216 596L172 675L126 596H38L93 742H250L305 596H216Z"  />
<glyph unicode="&#xEB;" horiz-adv-x="342" d="M322 192Q322 162 318 127T298 62T253 12T171 -8Q123 -8 92 8T44 60T20 150T13 283Q13 427 50 492T175 558Q226 558 256 538T302 479T324 381T329 244H141V239Q141 189 142 160T148 116T158 97T173 92Q193 92 198 121T204 192H322ZM208 333Q208 370 207 393T201 431T191 450T175 456Q166 456 160 452T149 434T143 396T141 333H208ZM65 729H158V601H65V729ZM197 729H289V601H197V729Z"  />
<glyph unicode="&#xEC;" horiz-adv-x="177" d="M22 0V550H155V0H22ZM49 596L-13 742H120L138 596H49Z"  />
<glyph unicode="&#xED;" horiz-adv-x="177" d="M22 0V550H155V0H22ZM136 596H47L65 742H198L136 596Z"  />
<glyph unicode="&#xEE;" horiz-adv-x="177" d="M22 0V550H155V0H22ZM134 596L90 675L44 596H-44L11 742H168L223 596H134Z"  />
<glyph unicode="&#xEF;" horiz-adv-x="177" d="M22 0V550H155V0H22ZM-20 729H73V601H-20V729ZM112 729H204V601H112V729Z"  />
<glyph unicode="&#xF0;" horiz-adv-x="361" d="M78 615L132 644Q112 661 86 679L196 738Q207 727 216 718T234 698L288 727L320 678L271 652Q295 617 310 580T333 500T345 406T348 293Q348 209 339 151T310 58T256 8T173 -8Q126 -8 96 9T47 59T21 144T13 263Q13 325 18 375T38 461T77 516T139 535Q164 535 181 526T209 503L212 505Q203 555 172 599L114 569L78 615ZM213 255Q213 309 211 341T205 391T195 413T181 418Q172 418 166 412T155 388T149 338T147 255Q147 203 149 172T155 125T165 103T180 97Q189 97 195 102T205 123T211 171T213 255Z"  />
<glyph unicode="&#xF1;" horiz-adv-x="365" d="M209 0V407Q209 425 204 434T184 443Q171 443 164 432T156 402V0H22V550H152V494H154Q171 528 194 543T251 558Q303 558 323 528T343 448V0H209ZM320 730Q318 667 299 632T236 596Q221 596 209 601T187 614T165 626T142 632Q131 632 124 622T116 597H51Q52 659 71 695T134 731Q149 731 161 726T183 713T205 701T228 695Q239 695 246 704T255 730H320H320Z"  />
<glyph unicode="&#xF2;" horiz-adv-x="351" d="M338 275Q338 198 331 145T306 57T256 8T175 -8Q126 -8 95 7T45 57T20 145T13 275Q13 346 19 399T43 487T92 540T175 558Q227 558 259 541T308 488T332 399T338 275ZM204 275Q204 335 203 370T199 424T190 448T175 454Q167 454 162 447T153 420T149 365T147 275Q147 215 148 180T153 126T161 102T175 97Q184 97 189 104T198 130T203 184T204 275ZM131 596L69 742H202L220 596H131Z"  />
<glyph unicode="&#xF3;" horiz-adv-x="351" d="M338 275Q338 198 331 145T306 57T256 8T175 -8Q126 -8 95 7T45 57T20 145T13 275Q13 346 19 399T43 487T92 540T175 558Q227 558 259 541T308 488T332 399T338 275ZM204 275Q204 335 203 370T199 424T190 448T175 454Q167 454 162 447T153 420T149 365T147 275Q147 215 148 180T153 126T161 102T175 97Q184 97 189 104T198 130T203 184T204 275ZM219 596H130L148 742H281L219 596Z"  />
<glyph unicode="&#xF4;" horiz-adv-x="351" d="M338 275Q338 198 331 145T306 57T256 8T175 -8Q126 -8 95 7T45 57T20 145T13 275Q13 346 19 399T43 487T92 540T175 558Q227 558 259 541T308 488T332 399T338 275ZM204 275Q204 335 203 370T199 424T190 448T175 454Q167 454 162 447T153 420T149 365T147 275Q147 215 148 180T153 126T161 102T175 97Q184 97 189 104T198 130T203 184T204 275ZM219 596L175 675L129 596H41L96 742H253L308 596H219Z"  />
<glyph unicode="&#xF5;" horiz-adv-x="351" d="M338 275Q338 198 331 145T306 57T256 8T175 -8Q126 -8 95 7T45 57T20 145T13 275Q13 346 19 399T43 487T92 540T175 558Q227 558 259 541T308 488T332 399T338 275ZM204 275Q204 335 203 370T199 424T190 448T175 454Q167 454 162 447T153 420T149 365T147 275Q147 215 148 180T153 126T161 102T175 97Q184 97 189 104T198 130T203 184T204 275ZM312 730Q310 667 291 632T228 596Q213 596 201 601T179 614T157 626T134 632Q123 632 116 622T108 597H43Q44 659 63 695T126 731Q141 731 153 726T175 713T197 701T220 695Q231 695 238 704T247 730H312H312Z"  />
<glyph unicode="&#xF6;" horiz-adv-x="351" d="M338 275Q338 198 331 145T306 57T256 8T175 -8Q126 -8 95 7T45 57T20 145T13 275Q13 346 19 399T43 487T92 540T175 558Q227 558 259 541T308 488T332 399T338 275ZM204 275Q204 335 203 370T199 424T190 448T175 454Q167 454 162 447T153 420T149 365T147 275Q147 215 148 180T153 126T161 102T175 97Q184 97 189 104T198 130T203 184T204 275ZM64 729H157V601H64V729ZM196 729H288V601H196V729Z"  />
<glyph unicode="&#xF7;" horiz-adv-x="401" d="M274 90Q274 57 252 37T202 17Q173 17 152 37T130 90Q130 123 152 142T202 162Q231 162 252 142T274 90ZM18 198V309H383V198H18ZM274 418Q274 384 252 365T202 345Q173 345 152 364T130 418Q130 451 152 470T202 489Q231 489 252 470T274 418Z"  />
<glyph unicode="&#xF8;" horiz-adv-x="351" d="M300 501Q323 466 330 410T338 275Q338 198 331 145T306 57T256 8T175 -8Q125 -8 93 8L71 -45L20 -23L50 48Q28 82 21 137T13 275Q13 346 19 399T43 487T92 540T175 558Q225 558 257 541L279 592L330 571L300 501ZM175 464Q166 464 160 457T151 429T146 371T144 275V271L203 412Q200 446 194 455T175 464ZM175 87Q184 87 190 94T200 121T205 179T207 275V278L148 138Q151 104 157 96T175 87Z"  />
<glyph unicode="&#xF9;" horiz-adv-x="362" d="M210 0V54H208Q194 19 171 6T115 -8Q65 -8 43 21T21 102V550H154V143Q154 107 178 107Q191 107 198 118T206 148V550H340V0H210ZM157 596L95 742H228L246 596H157Z"  />
<glyph unicode="&#xFA;" horiz-adv-x="362" d="M210 0V54H208Q194 19 171 6T115 -8Q65 -8 43 21T21 102V550H154V143Q154 107 178 107Q191 107 198 118T206 148V550H340V0H210ZM241 596H152L170 742H303L241 596Z"  />
<glyph unicode="&#xFB;" horiz-adv-x="362" d="M210 0V54H208Q194 19 171 6T115 -8Q65 -8 43 21T21 102V550H154V143Q154 107 178 107Q191 107 198 118T206 148V550H340V0H210ZM244 596L200 675L154 596H66L121 742H278L333 596H244Z"  />
<glyph unicode="&#xFC;" horiz-adv-x="362" d="M210 0V54H208Q194 19 171 6T115 -8Q65 -8 43 21T21 102V550H154V143Q154 107 178 107Q191 107 198 118T206 148V550H340V0H210ZM89 729H182V601H89V729ZM221 729H313V601H221V729Z"  />
<glyph unicode="&#xFD;" horiz-adv-x="342" d="M244 -23Q236 -68 227 -99T200 -148T156 -175T85 -183Q67 -183 54 -183T25 -180V-79Q44 -82 58 -82Q77 -82 85 -75T96 -51Q97 -42 97 -33T95 -13L-4 550H137L172 170H173L212 550H348L244 -23H244ZM220 596H131L149 742H282L220 596Z"  />
<glyph unicode="&#xFE;" horiz-adv-x="359" d="M346 263Q346 186 341 135T322 51T289 6T238 -8Q210 -8 190 4T158 52H156V-180H22V717H156V496H158Q171 529 192 543T242 558Q276 558 296 542T328 490T342 398T346 263ZM212 271Q212 329 211 362T206 412T198 433T185 437Q177 437 172 429T163 401T158 350T156 273Q156 221 157 190T162 141T171 118T184 112Q191 112 196 116T205 137T210 185T212 271Z"  />
<glyph unicode="&#xFF;" horiz-adv-x="342" d="M244 -23Q236 -68 227 -99T200 -148T156 -175T85 -183Q67 -183 54 -183T25 -180V-79Q44 -82 58 -82Q77 -82 85 -75T96 -51Q97 -42 97 -33T95 -13L-4 550H137L172 170H173L212 550H348L244 -23H244ZM64 729H157V601H64V729ZM196 729H288V601H196V729Z"  />
<glyph unicode="&#x131;" horiz-adv-x="177" d="M22 0V550H155V0H22Z"  />
<glyph unicode="&#x152;" horiz-adv-x="558" d="M256 0V37H254Q237 13 215 3T163 -8Q115 -8 86 14T42 82T21 196T15 355Q15 442 20 510T43 626T90 697T170 722Q200 722 220 712T254 683H256V714H541V580H405V429H520V304H405V134H546V0H256ZM252 389Q252 453 250 492T242 554T228 583T209 590Q197 590 189 581T176 545T169 474T166 356Q166 279 168 234T176 163T189 131T208 123Q221 123 229 133T243 169T250 240T252 356V389Z"  />
<glyph unicode="&#x153;" horiz-adv-x="545" d="M525 192Q525 162 521 127T502 62T457 12T374 -8Q344 -8 319 3T274 42H272Q255 15 230 4T170 -8Q123 -8 93 7T45 57T20 145T13 275Q13 346 19 399T42 487T90 540T170 558Q208 558 232 546T274 507H275Q295 532 320 545T378 558Q428 558 458 538T505 479T527 381T532 244H344V239Q344 189 346 160T351 116T361 97T376 92Q397 92 402 121T408 192H525ZM412 333Q412 370 411 393T406 431T396 450T380 456Q370 456 364 452T353 434T346 396T344 333H412ZM204 275Q204 335 203 370T199 424T190 448T175 454Q167 454 162 447T153 420T149 365T147 275Q147 215 148 180T153 126T161 102T175 97Q184 97 189 104T198 130T203 184T204 275Z"  />
<glyph unicode="&#x160;" horiz-adv-x="383" d="M372 205Q372 96 326 44T189 -8Q143 -8 111 3T57 40T25 110T12 222H147Q150 160 158 137T190 114Q227 114 227 171Q227 210 211 235T171 281T120 321T68 368T28 434T12 531Q12 629 58 675T191 722Q240 722 273 708T326 667T354 601T363 509H243Q241 554 234 579T200 604Q176 604 170 587T163 550Q163 514 178 491T217 448T267 411T318 366T356 301T372 205ZM274 760H117L62 906H150L196 827L240 906H329L274 760Z"  />
<glyph unicode="&#x161;" horiz-adv-x="318" d="M308 163Q308 125 301 94T276 40T229 5T154 -8Q78 -8 47 34T12 169H132Q134 131 138 108T160 85Q177 85 180 101T184 134Q184 163 172 181T140 215T98 244T57 278T25 327T12 402Q12 480 48 519T161 558Q206 558 233 546T276 511T296 459T301 392H193Q192 421 188 441T164 461Q148 461 144 447T140 417Q140 392 152 377T183 348T224 321T264 287T295 238T308 163ZM241 596H84L29 742H117L163 664L207 742H296L241 596Z"  />
<glyph unicode="&#x178;" horiz-adv-x="399" d="M128 0V235L-11 714H153L200 431H202L255 714H410L276 235V0H128ZM94 764V893H187V764H94ZM226 764V893H318V764H226Z"  />
<glyph unicode="&#x17D;" horiz-adv-x="374" d="M11 0V107L185 580H23V714H359V592L190 134H360V0H11ZM275 760H118L63 906H151L197 827L241 906H330L275 760Z"  />
<glyph unicode="&#x17E;" horiz-adv-x="301" d="M10 0V95L133 443H12V550H288V461L156 108H290V0H10ZM237 596H80L25 742H113L159 664L203 742H292L237 596Z"  />
<glyph unicode="&#x192;" horiz-adv-x="401" d="M273 414H359L348 328H262L224 8Q213 -89 173 -132T55 -176Q33 -176 16 -174T-22 -167L-8 -60Q5 -64 19 -64Q51 -64 64 -47T83 14L122 328H40L49 414H133L146 518Q153 571 165 607T199 665T253 696T330 706Q371 706 394 700L380 591Q374 593 366 593T350 594Q334 594 324 590T306 575T293 545T283 498L273 414Z"  />
<glyph unicode="&#x237;" horiz-adv-x="181" d="M157 -66Q157 -129 127 -156T27 -183Q16 -183 4 -183T-22 -181V-80Q-14 -81 -13 -81T-6 -81Q24 -81 24 -50V550H157V-66H157Z"  />
<glyph unicode="&#x2C6;" horiz-adv-x="271" d="M180 596L136 675L90 596H2L57 742H214L269 596H180Z"  />
<glyph unicode="&#x2C7;" horiz-adv-x="271" d="M214 596H57L2 742H90L136 664L180 742H269L214 596Z"  />
<glyph unicode="&#x2DA;" horiz-adv-x="271" d="M249 691Q249 638 217 607T136 575Q89 575 56 606T23 691Q23 716 32 737T56 773T91 796T136 805Q158 805 178 797T215 775T240 739T249 691ZM188 691Q188 714 174 729T136 745Q115 745 100 731T84 691Q84 668 98 653T136 638Q158 638 173 653T188 691Z"  />
<glyph unicode="&#x2DC;" horiz-adv-x="271" d="M270 730Q268 667 249 632T186 596Q171 596 159 601T137 614T115 626T92 632Q81 632 74 622T66 597H1Q2 659 21 695T84 731Q99 731 111 726T133 713T155 701T178 695Q189 695 196 704T205 730H270H270Z"  />
<glyph unicode="&#x2013;" horiz-adv-x="500" d="M0 240V364H500V240H0Z"  />
<glyph unicode="&#x2014;" horiz-adv-x="1000" d="M29 240V364H971V240H29Z"  />
<glyph unicode="&#x2018;" horiz-adv-x="180" d="M29 519Q29 567 34 602T54 661T93 698T154 714V635Q145 633 137 630T123 618T113 597T109 562V552H154V368H29V519H29Z"  />
<glyph unicode="&#x2019;" horiz-adv-x="180" d="M152 563Q152 515 146 480T126 421T87 384T26 368V447Q35 449 43 452T57 464T67 485T71 520V530H27V714H152V563H152Z"  />
<glyph unicode="&#x201A;" horiz-adv-x="180" d="M152 37Q152 -11 146 -46T126 -105T87 -142T26 -158V-79Q35 -77 43 -74T57 -62T67 -41T71 -6V4H27V188H152V37Z"  />
<glyph unicode="&#x201C;" horiz-adv-x="341" d="M29 519Q29 567 34 602T54 661T93 698T154 714V635Q145 633 137 630T123 618T113 597T109 562V552H154V368H29V519ZM190 519Q190 567 196 602T216 661T254 698T315 714V635Q306 633 298 630T284 618T274 597T270 562V552H315V368H190V519Z"  />
<glyph unicode="&#x201D;" horiz-adv-x="341" d="M313 563Q313 515 307 480T287 421T249 384T188 368V447Q197 449 205 452T219 464T228 485T232 520V530H188V714H313V563ZM152 563Q152 515 146 480T126 421T87 384T26 368V447Q35 449 43 452T57 464T67 485T71 520V530H27V714H152V563Z"  />
<glyph unicode="&#x201E;" horiz-adv-x="341" d="M313 37Q313 -11 307 -46T287 -105T249 -142T188 -158V-79Q197 -77 205 -74T219 -62T228 -41T232 -6V4H188V188H313V37ZM152 37Q152 -11 146 -46T126 -105T87 -142T26 -158V-79Q35 -77 43 -74T57 -62T67 -41T71 -6V4H27V188H152V37Z"  />
<glyph unicode="&#x2020;" horiz-adv-x="405" d="M145 -139V447H11V550H145V714H260V550H395V447H260V-139H145Z"  />
<glyph unicode="&#x2021;" horiz-adv-x="405" d="M145 -139V60H11V164H145V423H11V526H145V714H260V526H395V423H260V164H395V60H260V-139H145Z"  />
<glyph unicode="&#x2022;" horiz-adv-x="500" d="M250 535Q286 535 318 521T375 483T413 427T428 357Q428 321 414 289T376 232T320 193T250 179Q212 179 180 193T123 231T86 288T72 357Q72 394 86 426T124 483T180 521T250 535Z"  />
<glyph unicode="&#x2026;" horiz-adv-x="1000" d="M106 196H228V0H106V196ZM439 196H561V0H439V196ZM772 196H894V0H772V196Z"  />
<glyph unicode="&#x2030;" horiz-adv-x="724" d="M227 539Q227 448 204 410T123 372Q67 372 45 410T23 539Q23 624 44 663T123 702Q182 702 204 663T227 539ZM140 539Q140 569 140 587T137 615T132 628T123 632Q114 632 111 613T108 539Q108 509 108 491T110 462T115 448T123 444Q128 444 131 448T137 462T139 491T140 539ZM326 708H414L176 -8H88L326 708ZM482 163Q482 72 459 34T378 -4Q322 -4 300 34T278 163Q278 205 282 235T298 286T329 316T378 326Q437 326 459 287T482 163ZM395 163Q395 193 395 211T392 239T387 252T378 256Q369 256 366 237T363 163Q363 133 363 115T365 86T370 72T378 68Q383 68 386 72T392 86T394 115T395 163ZM701 163Q701 72 678 34T597 -4Q541 -4 519 34T497 163Q497 248 518 287T598 326Q657 326 679 288T701 163ZM615 163Q615 193 614 211T611 239T606 252T598 256Q589 256 586 237T582 163Q582 133 582 115T584 86T589 72T597 68Q602 68 605 72T611 86T614 115T615 163Z"  />
<glyph unicode="&#x2039;" horiz-adv-x="190" d="M21 203V373L166 495V346L95 290L166 233V84L21 203Z"  />
<glyph unicode="&#x203A;" horiz-adv-x="190" d="M25 84V233L95 290L25 346V495L170 373V203L25 84Z"  />
<glyph unicode="&#x2044;" horiz-adv-x="101" d="M126 708H214L-24 -8H-112L126 708Z"  />
<glyph unicode="&#x2074;" horiz-adv-x="273" d="M143 403V476H7V556L119 824H234V555H268V476H234V403H143ZM143 697H141L88 555H143V697Z"  />
<glyph unicode="&#x20AC;" horiz-adv-x="401" d="M221 236Q228 174 247 146T306 118Q333 118 352 125T394 146V18Q366 4 338 -1T271 -7Q224 -7 190 6T131 49T92 125T71 236H9L27 322H66V366V378H10L27 464H71Q78 534 97 581T144 655T210 694T292 706Q324 706 353 700T407 679L382 558Q365 572 348 577T315 583Q275 583 253 557T221 464H366L349 378H216V364V322H340L322 236H221Z"  />
<glyph unicode="&#x2122;" horiz-adv-x="506" d="M399 357V587H398L374 357H316L292 587H290V357H214V714H317L344 486H345L367 714H476V357H399ZM62 357V641H11V714H199V641H148V357H62Z"  />

<hkern u1="(" u2="j" k="-28" />
<hkern u1="&#x2c;" u2="T" k="54" />
<hkern u1="&#x2c;" u2="V" k="36" />
<hkern u1="&#x2c;" u2="W" k="24" />
<hkern u1="&#x2c;" u2="Y" k="68" />
<hkern u1="&#x2c;" u2="v" k="31" />
<hkern u1="&#x2c;" u2="w" k="21" />
<hkern u1="&#x2c;" u2="y" k="31" />
<hkern u1="&#x2c;" u2="&#xdd;" k="68" />
<hkern u1="&#x2c;" u2="&#xfd;" k="31" />
<hkern u1="&#x2c;" u2="&#x178;" k="68" />
<hkern u1="&#x2c;" u2="&#x2018;" k="66" />
<hkern u1="&#x2c;" u2="&#x2019;" k="62" />
<hkern u1="&#x2c;" u2="&#x201c;" k="66" />
<hkern u1="&#x2c;" u2="&#x201d;" k="62" />
<hkern u1="-" u2="T" k="50" />
<hkern u1="-" u2="V" k="14" />
<hkern u1="-" u2="W" k="5" />
<hkern u1="-" u2="X" k="18" />
<hkern u1="-" u2="Y" k="52" />
<hkern u1="-" u2="&#xdd;" k="52" />
<hkern u1="-" u2="&#x178;" k="52" />
<hkern u1="." u2="T" k="54" />
<hkern u1="." u2="V" k="36" />
<hkern u1="." u2="W" k="24" />
<hkern u1="." u2="Y" k="68" />
<hkern u1="." u2="v" k="31" />
<hkern u1="." u2="w" k="21" />
<hkern u1="." u2="y" k="31" />
<hkern u1="." u2="&#xdd;" k="68" />
<hkern u1="." u2="&#xfd;" k="31" />
<hkern u1="." u2="&#x178;" k="68" />
<hkern u1="." u2="&#x2018;" k="68" />
<hkern u1="." u2="&#x2019;" k="64" />
<hkern u1="." u2="&#x201c;" k="68" />
<hkern u1="." u2="&#x201d;" k="64" />
<hkern u1="/" u2="J" k="60" />
<hkern u1="A" u2="A" k="-10" />
<hkern u1="A" u2="T" k="35" />
<hkern u1="A" u2="U" k="-5" />
<hkern u1="A" u2="V" k="28" />
<hkern u1="A" u2="W" k="19" />
<hkern u1="A" u2="X" k="-15" />
<hkern u1="A" u2="Y" k="37" />
<hkern u1="A" u2="Z" k="-10" />
<hkern u1="A" u2="w" k="5" />
<hkern u1="A" u2="x" k="-9" />
<hkern u1="A" u2="z" k="-5" />
<hkern u1="A" u2="&#xc0;" k="-10" />
<hkern u1="A" u2="&#xc1;" k="-10" />
<hkern u1="A" u2="&#xc2;" k="-10" />
<hkern u1="A" u2="&#xc3;" k="-10" />
<hkern u1="A" u2="&#xc4;" k="-10" />
<hkern u1="A" u2="&#xc5;" k="-10" />
<hkern u1="A" u2="&#xd9;" k="-5" />
<hkern u1="A" u2="&#xda;" k="-5" />
<hkern u1="A" u2="&#xdb;" k="-5" />
<hkern u1="A" u2="&#xdc;" k="-5" />
<hkern u1="A" u2="&#xdd;" k="37" />
<hkern u1="A" u2="&#x178;" k="37" />
<hkern u1="A" u2="&#x17d;" k="-10" />
<hkern u1="A" u2="&#x2018;" k="22" />
<hkern u1="A" u2="&#x2019;" k="22" />
<hkern u1="A" u2="&#x201c;" k="22" />
<hkern u1="A" u2="&#x201d;" k="22" />
<hkern u1="A" u2="&#x2122;" k="43" />
<hkern u1="C" u2="T" k="3" />
<hkern u1="C" u2="X" k="10" />
<hkern u1="C" u2="Y" k="14" />
<hkern u1="C" u2="&#xc6;" k="3" />
<hkern u1="C" u2="&#xdd;" k="14" />
<hkern u1="C" u2="&#x178;" k="14" />
<hkern u1="F" u2="&#x2c;" k="54" />
<hkern u1="F" u2="." k="53" />
<hkern u1="F" u2="A" k="21" />
<hkern u1="F" u2="a" k="11" />
<hkern u1="F" u2="&#xc0;" k="21" />
<hkern u1="F" u2="&#xc1;" k="21" />
<hkern u1="F" u2="&#xc2;" k="21" />
<hkern u1="F" u2="&#xc3;" k="21" />
<hkern u1="F" u2="&#xc4;" k="21" />
<hkern u1="F" u2="&#xc5;" k="21" />
<hkern u1="F" u2="&#xc6;" k="42" />
<hkern u1="F" u2="&#xe0;" k="11" />
<hkern u1="F" u2="&#xe1;" k="11" />
<hkern u1="F" u2="&#xe2;" k="11" />
<hkern u1="F" u2="&#xe6;" k="11" />
<hkern u1="F" u2="&#x2026;" k="53" />
<hkern u1="G" u2="T" k="3" />
<hkern u1="G" u2="Y" k="7" />
<hkern u1="G" u2="&#xdd;" k="7" />
<hkern u1="G" u2="&#x178;" k="7" />
<hkern u1="K" u2="-" k="15" />
<hkern u1="K" u2="w" k="18" />
<hkern u1="K" u2="y" k="10" />
<hkern u1="K" u2="&#xad;" k="15" />
<hkern u1="K" u2="&#xfd;" k="10" />
<hkern u1="K" u2="&#x2013;" k="15" />
<hkern u1="K" u2="&#x2014;" k="15" />
<hkern u1="L" u2="-" k="33" />
<hkern u1="L" u2="C" k="3" />
<hkern u1="L" u2="G" k="3" />
<hkern u1="L" u2="O" k="3" />
<hkern u1="L" u2="Q" k="3" />
<hkern u1="L" u2="T" k="47" />
<hkern u1="L" u2="V" k="38" />
<hkern u1="L" u2="W" k="19" />
<hkern u1="L" u2="Y" k="56" />
<hkern u1="L" u2="c" k="3" />
<hkern u1="L" u2="e" k="3" />
<hkern u1="L" u2="o" k="3" />
<hkern u1="L" u2="y" k="25" />
<hkern u1="L" u2="&#xad;" k="33" />
<hkern u1="L" u2="&#xc7;" k="3" />
<hkern u1="L" u2="&#xd2;" k="3" />
<hkern u1="L" u2="&#xd3;" k="3" />
<hkern u1="L" u2="&#xd4;" k="3" />
<hkern u1="L" u2="&#xd5;" k="3" />
<hkern u1="L" u2="&#xd6;" k="3" />
<hkern u1="L" u2="&#xd8;" k="3" />
<hkern u1="L" u2="&#xdd;" k="56" />
<hkern u1="L" u2="&#xe7;" k="3" />
<hkern u1="L" u2="&#xe8;" k="3" />
<hkern u1="L" u2="&#xe9;" k="3" />
<hkern u1="L" u2="&#xea;" k="3" />
<hkern u1="L" u2="&#xf2;" k="3" />
<hkern u1="L" u2="&#xf3;" k="3" />
<hkern u1="L" u2="&#xf4;" k="3" />
<hkern u1="L" u2="&#xf8;" k="3" />
<hkern u1="L" u2="&#xfd;" k="25" />
<hkern u1="L" u2="&#x152;" k="3" />
<hkern u1="L" u2="&#x153;" k="3" />
<hkern u1="L" u2="&#x178;" k="56" />
<hkern u1="L" u2="&#x2013;" k="33" />
<hkern u1="L" u2="&#x2014;" k="33" />
<hkern u1="L" u2="&#x2018;" k="72" />
<hkern u1="L" u2="&#x2019;" k="65" />
<hkern u1="L" u2="&#x201c;" k="72" />
<hkern u1="L" u2="&#x201d;" k="65" />
<hkern u1="L" u2="&#x2122;" k="68" />
<hkern u1="O" u2="T" k="3" />
<hkern u1="O" u2="X" k="10" />
<hkern u1="O" u2="Y" k="14" />
<hkern u1="O" u2="&#xc6;" k="3" />
<hkern u1="O" u2="&#xdd;" k="14" />
<hkern u1="O" u2="&#x178;" k="14" />
<hkern u1="P" u2="&#x2c;" k="84" />
<hkern u1="P" u2="-" k="6" />
<hkern u1="P" u2="." k="82" />
<hkern u1="P" u2="/" k="34" />
<hkern u1="P" u2="A" k="24" />
<hkern u1="P" u2="a" k="3" />
<hkern u1="P" u2="c" k="6" />
<hkern u1="P" u2="e" k="6" />
<hkern u1="P" u2="o" k="6" />
<hkern u1="P" u2="s" k="3" />
<hkern u1="P" u2="&#xad;" k="6" />
<hkern u1="P" u2="&#xc0;" k="24" />
<hkern u1="P" u2="&#xc1;" k="24" />
<hkern u1="P" u2="&#xc2;" k="24" />
<hkern u1="P" u2="&#xc3;" k="24" />
<hkern u1="P" u2="&#xc4;" k="24" />
<hkern u1="P" u2="&#xc5;" k="24" />
<hkern u1="P" u2="&#xc6;" k="50" />
<hkern u1="P" u2="&#xe0;" k="3" />
<hkern u1="P" u2="&#xe1;" k="3" />
<hkern u1="P" u2="&#xe2;" k="3" />
<hkern u1="P" u2="&#xe6;" k="3" />
<hkern u1="P" u2="&#xe7;" k="6" />
<hkern u1="P" u2="&#xe8;" k="6" />
<hkern u1="P" u2="&#xe9;" k="6" />
<hkern u1="P" u2="&#xea;" k="6" />
<hkern u1="P" u2="&#xf2;" k="6" />
<hkern u1="P" u2="&#xf3;" k="6" />
<hkern u1="P" u2="&#xf4;" k="6" />
<hkern u1="P" u2="&#xf8;" k="6" />
<hkern u1="P" u2="&#x153;" k="6" />
<hkern u1="P" u2="&#x2013;" k="6" />
<hkern u1="P" u2="&#x2014;" k="6" />
<hkern u1="P" u2="&#x2026;" k="82" />
<hkern u1="Q" u2="T" k="3" />
<hkern u1="Q" u2="X" k="10" />
<hkern u1="Q" u2="Y" k="14" />
<hkern u1="Q" u2="&#xc6;" k="3" />
<hkern u1="Q" u2="&#xdd;" k="14" />
<hkern u1="Q" u2="&#x178;" k="14" />
<hkern u1="R" u2="U" k="-1" />
<hkern u1="R" u2="&#xd9;" k="-1" />
<hkern u1="R" u2="&#xda;" k="-1" />
<hkern u1="R" u2="&#xdb;" k="-1" />
<hkern u1="R" u2="&#xdc;" k="-1" />
<hkern u1="S" u2="T" k="3" />
<hkern u1="T" u2="&#x2c;" k="54" />
<hkern u1="T" u2="-" k="50" />
<hkern u1="T" u2="." k="54" />
<hkern u1="T" u2="/" k="50" />
<hkern u1="T" u2=":" k="28" />
<hkern u1="T" u2=";" k="28" />
<hkern u1="T" u2="A" k="35" />
<hkern u1="T" u2="C" k="5" />
<hkern u1="T" u2="G" k="5" />
<hkern u1="T" u2="J" k="66" />
<hkern u1="T" u2="O" k="5" />
<hkern u1="T" u2="Q" k="5" />
<hkern u1="T" u2="a" k="50" />
<hkern u1="T" u2="c" k="45" />
<hkern u1="T" u2="e" k="45" />
<hkern u1="T" u2="m" k="30" />
<hkern u1="T" u2="n" k="30" />
<hkern u1="T" u2="o" k="45" />
<hkern u1="T" u2="r" k="30" />
<hkern u1="T" u2="s" k="43" />
<hkern u1="T" u2="u" k="33" />
<hkern u1="T" u2="v" k="26" />
<hkern u1="T" u2="w" k="32" />
<hkern u1="T" u2="x" k="30" />
<hkern u1="T" u2="y" k="27" />
<hkern u1="T" u2="z" k="30" />
<hkern u1="T" u2="&#xad;" k="50" />
<hkern u1="T" u2="&#xc0;" k="35" />
<hkern u1="T" u2="&#xc1;" k="35" />
<hkern u1="T" u2="&#xc2;" k="35" />
<hkern u1="T" u2="&#xc3;" k="35" />
<hkern u1="T" u2="&#xc4;" k="35" />
<hkern u1="T" u2="&#xc5;" k="35" />
<hkern u1="T" u2="&#xc6;" k="50" />
<hkern u1="T" u2="&#xc7;" k="5" />
<hkern u1="T" u2="&#xd2;" k="5" />
<hkern u1="T" u2="&#xd3;" k="5" />
<hkern u1="T" u2="&#xd4;" k="5" />
<hkern u1="T" u2="&#xd5;" k="5" />
<hkern u1="T" u2="&#xd6;" k="5" />
<hkern u1="T" u2="&#xd8;" k="5" />
<hkern u1="T" u2="&#xe0;" k="50" />
<hkern u1="T" u2="&#xe1;" k="50" />
<hkern u1="T" u2="&#xe2;" k="50" />
<hkern u1="T" u2="&#xe6;" k="50" />
<hkern u1="T" u2="&#xe7;" k="45" />
<hkern u1="T" u2="&#xe8;" k="45" />
<hkern u1="T" u2="&#xe9;" k="45" />
<hkern u1="T" u2="&#xea;" k="45" />
<hkern u1="T" u2="&#xf1;" k="30" />
<hkern u1="T" u2="&#xf2;" k="45" />
<hkern u1="T" u2="&#xf3;" k="45" />
<hkern u1="T" u2="&#xf4;" k="45" />
<hkern u1="T" u2="&#xf8;" k="45" />
<hkern u1="T" u2="&#xf9;" k="33" />
<hkern u1="T" u2="&#xfa;" k="33" />
<hkern u1="T" u2="&#xfb;" k="33" />
<hkern u1="T" u2="&#xfd;" k="27" />
<hkern u1="T" u2="&#x131;" k="30" />
<hkern u1="T" u2="&#x152;" k="5" />
<hkern u1="T" u2="&#x153;" k="45" />
<hkern u1="T" u2="&#x2013;" k="50" />
<hkern u1="T" u2="&#x2014;" k="50" />
<hkern u1="T" u2="&#x2026;" k="54" />
<hkern u1="U" u2="A" k="-5" />
<hkern u1="U" u2="&#xc0;" k="-5" />
<hkern u1="U" u2="&#xc1;" k="-5" />
<hkern u1="U" u2="&#xc2;" k="-5" />
<hkern u1="U" u2="&#xc3;" k="-5" />
<hkern u1="U" u2="&#xc4;" k="-5" />
<hkern u1="U" u2="&#xc5;" k="-5" />
<hkern u1="V" u2="&#x2c;" k="36" />
<hkern u1="V" u2="-" k="14" />
<hkern u1="V" u2="." k="36" />
<hkern u1="V" u2=":" k="5" />
<hkern u1="V" u2=";" k="5" />
<hkern u1="V" u2="A" k="28" />
<hkern u1="V" u2="J" k="32" />
<hkern u1="V" u2="a" k="21" />
<hkern u1="V" u2="c" k="17" />
<hkern u1="V" u2="e" k="17" />
<hkern u1="V" u2="m" k="6" />
<hkern u1="V" u2="n" k="6" />
<hkern u1="V" u2="o" k="17" />
<hkern u1="V" u2="r" k="6" />
<hkern u1="V" u2="u" k="7" />
<hkern u1="V" u2="&#xad;" k="14" />
<hkern u1="V" u2="&#xc0;" k="28" />
<hkern u1="V" u2="&#xc1;" k="28" />
<hkern u1="V" u2="&#xc2;" k="28" />
<hkern u1="V" u2="&#xc3;" k="28" />
<hkern u1="V" u2="&#xc4;" k="28" />
<hkern u1="V" u2="&#xc5;" k="28" />
<hkern u1="V" u2="&#xc6;" k="42" />
<hkern u1="V" u2="&#xe0;" k="21" />
<hkern u1="V" u2="&#xe1;" k="21" />
<hkern u1="V" u2="&#xe2;" k="21" />
<hkern u1="V" u2="&#xe6;" k="21" />
<hkern u1="V" u2="&#xe7;" k="17" />
<hkern u1="V" u2="&#xe8;" k="17" />
<hkern u1="V" u2="&#xe9;" k="17" />
<hkern u1="V" u2="&#xea;" k="17" />
<hkern u1="V" u2="&#xf1;" k="6" />
<hkern u1="V" u2="&#xf2;" k="17" />
<hkern u1="V" u2="&#xf3;" k="17" />
<hkern u1="V" u2="&#xf4;" k="17" />
<hkern u1="V" u2="&#xf8;" k="17" />
<hkern u1="V" u2="&#xf9;" k="7" />
<hkern u1="V" u2="&#xfa;" k="7" />
<hkern u1="V" u2="&#xfb;" k="7" />
<hkern u1="V" u2="&#x131;" k="6" />
<hkern u1="V" u2="&#x153;" k="17" />
<hkern u1="V" u2="&#x2013;" k="14" />
<hkern u1="V" u2="&#x2014;" k="14" />
<hkern u1="V" u2="&#x2026;" k="36" />
<hkern u1="W" u2="&#x2c;" k="24" />
<hkern u1="W" u2="-" k="5" />
<hkern u1="W" u2="." k="24" />
<hkern u1="W" u2="A" k="19" />
<hkern u1="W" u2="J" k="18" />
<hkern u1="W" u2="a" k="11" />
<hkern u1="W" u2="&#xad;" k="5" />
<hkern u1="W" u2="&#xc0;" k="19" />
<hkern u1="W" u2="&#xc1;" k="19" />
<hkern u1="W" u2="&#xc2;" k="19" />
<hkern u1="W" u2="&#xc3;" k="19" />
<hkern u1="W" u2="&#xc4;" k="19" />
<hkern u1="W" u2="&#xc5;" k="19" />
<hkern u1="W" u2="&#xc6;" k="23" />
<hkern u1="W" u2="&#xe0;" k="11" />
<hkern u1="W" u2="&#xe1;" k="11" />
<hkern u1="W" u2="&#xe2;" k="11" />
<hkern u1="W" u2="&#xe6;" k="11" />
<hkern u1="W" u2="&#x2013;" k="5" />
<hkern u1="W" u2="&#x2014;" k="5" />
<hkern u1="W" u2="&#x2026;" k="24" />
<hkern u1="X" u2="-" k="18" />
<hkern u1="X" u2="A" k="-15" />
<hkern u1="X" u2="C" k="10" />
<hkern u1="X" u2="G" k="10" />
<hkern u1="X" u2="O" k="10" />
<hkern u1="X" u2="Q" k="10" />
<hkern u1="X" u2="c" k="10" />
<hkern u1="X" u2="e" k="10" />
<hkern u1="X" u2="o" k="10" />
<hkern u1="X" u2="y" k="10" />
<hkern u1="X" u2="&#xad;" k="18" />
<hkern u1="X" u2="&#xc0;" k="-15" />
<hkern u1="X" u2="&#xc1;" k="-15" />
<hkern u1="X" u2="&#xc2;" k="-15" />
<hkern u1="X" u2="&#xc3;" k="-15" />
<hkern u1="X" u2="&#xc4;" k="-15" />
<hkern u1="X" u2="&#xc5;" k="-15" />
<hkern u1="X" u2="&#xc7;" k="10" />
<hkern u1="X" u2="&#xd2;" k="10" />
<hkern u1="X" u2="&#xd3;" k="10" />
<hkern u1="X" u2="&#xd4;" k="10" />
<hkern u1="X" u2="&#xd5;" k="10" />
<hkern u1="X" u2="&#xd6;" k="10" />
<hkern u1="X" u2="&#xd8;" k="10" />
<hkern u1="X" u2="&#xe7;" k="10" />
<hkern u1="X" u2="&#xe8;" k="10" />
<hkern u1="X" u2="&#xe9;" k="10" />
<hkern u1="X" u2="&#xea;" k="10" />
<hkern u1="X" u2="&#xf2;" k="10" />
<hkern u1="X" u2="&#xf3;" k="10" />
<hkern u1="X" u2="&#xf4;" k="10" />
<hkern u1="X" u2="&#xf8;" k="10" />
<hkern u1="X" u2="&#xfd;" k="10" />
<hkern u1="X" u2="&#x152;" k="10" />
<hkern u1="X" u2="&#x153;" k="10" />
<hkern u1="X" u2="&#x2013;" k="18" />
<hkern u1="X" u2="&#x2014;" k="18" />
<hkern u1="Y" u2="&#x2c;" k="68" />
<hkern u1="Y" u2="-" k="52" />
<hkern u1="Y" u2="." k="68" />
<hkern u1="Y" u2="/" k="30" />
<hkern u1="Y" u2=":" k="29" />
<hkern u1="Y" u2=";" k="29" />
<hkern u1="Y" u2="A" k="41" />
<hkern u1="Y" u2="C" k="14" />
<hkern u1="Y" u2="G" k="14" />
<hkern u1="Y" u2="J" k="73" />
<hkern u1="Y" u2="O" k="14" />
<hkern u1="Y" u2="Q" k="14" />
<hkern u1="Y" u2="a" k="50" />
<hkern u1="Y" u2="c" k="50" />
<hkern u1="Y" u2="e" k="50" />
<hkern u1="Y" u2="m" k="30" />
<hkern u1="Y" u2="n" k="30" />
<hkern u1="Y" u2="o" k="50" />
<hkern u1="Y" u2="r" k="30" />
<hkern u1="Y" u2="s" k="45" />
<hkern u1="Y" u2="u" k="25" />
<hkern u1="Y" u2="v" k="20" />
<hkern u1="Y" u2="&#xad;" k="52" />
<hkern u1="Y" u2="&#xc0;" k="41" />
<hkern u1="Y" u2="&#xc1;" k="41" />
<hkern u1="Y" u2="&#xc2;" k="41" />
<hkern u1="Y" u2="&#xc3;" k="41" />
<hkern u1="Y" u2="&#xc4;" k="41" />
<hkern u1="Y" u2="&#xc5;" k="41" />
<hkern u1="Y" u2="&#xc6;" k="60" />
<hkern u1="Y" u2="&#xc7;" k="14" />
<hkern u1="Y" u2="&#xd2;" k="14" />
<hkern u1="Y" u2="&#xd3;" k="14" />
<hkern u1="Y" u2="&#xd4;" k="14" />
<hkern u1="Y" u2="&#xd5;" k="14" />
<hkern u1="Y" u2="&#xd6;" k="14" />
<hkern u1="Y" u2="&#xd8;" k="14" />
<hkern u1="Y" u2="&#xe0;" k="50" />
<hkern u1="Y" u2="&#xe1;" k="50" />
<hkern u1="Y" u2="&#xe2;" k="50" />
<hkern u1="Y" u2="&#xe6;" k="50" />
<hkern u1="Y" u2="&#xe7;" k="50" />
<hkern u1="Y" u2="&#xe8;" k="50" />
<hkern u1="Y" u2="&#xe9;" k="50" />
<hkern u1="Y" u2="&#xea;" k="50" />
<hkern u1="Y" u2="&#xf1;" k="30" />
<hkern u1="Y" u2="&#xf2;" k="50" />
<hkern u1="Y" u2="&#xf3;" k="50" />
<hkern u1="Y" u2="&#xf4;" k="50" />
<hkern u1="Y" u2="&#xf8;" k="50" />
<hkern u1="Y" u2="&#xf9;" k="25" />
<hkern u1="Y" u2="&#xfa;" k="25" />
<hkern u1="Y" u2="&#xfb;" k="25" />
<hkern u1="Y" u2="&#x131;" k="30" />
<hkern u1="Y" u2="&#x152;" k="14" />
<hkern u1="Y" u2="&#x153;" k="50" />
<hkern u1="Y" u2="&#x2013;" k="52" />
<hkern u1="Y" u2="&#x2014;" k="52" />
<hkern u1="Y" u2="&#x2026;" k="68" />
<hkern u1="Z" u2="w" k="5" />
<hkern u1="Z" u2="y" k="3" />
<hkern u1="Z" u2="&#xfd;" k="3" />
<hkern u1="f" u2="*" k="-13" />
<hkern u1="f" u2="?" k="-22" />
<hkern u1="f" u2="&#x2122;" k="-17" />
<hkern u1="k" u2="-" k="26" />
<hkern u1="k" u2="&#xad;" k="26" />
<hkern u1="k" u2="&#x2013;" k="26" />
<hkern u1="k" u2="&#x2014;" k="26" />
<hkern u1="r" u2="&#x2c;" k="40" />
<hkern u1="r" u2="-" k="5" />
<hkern u1="r" u2="." k="40" />
<hkern u1="r" u2="/" k="20" />
<hkern u1="r" u2="c" k="2" />
<hkern u1="r" u2="e" k="2" />
<hkern u1="r" u2="o" k="2" />
<hkern u1="r" u2="w" k="-8" />
<hkern u1="r" u2="z" k="-10" />
<hkern u1="r" u2="&#xad;" k="5" />
<hkern u1="r" u2="&#xe7;" k="2" />
<hkern u1="r" u2="&#xe8;" k="2" />
<hkern u1="r" u2="&#xe9;" k="2" />
<hkern u1="r" u2="&#xea;" k="2" />
<hkern u1="r" u2="&#xf2;" k="2" />
<hkern u1="r" u2="&#xf3;" k="2" />
<hkern u1="r" u2="&#xf4;" k="2" />
<hkern u1="r" u2="&#xf8;" k="2" />
<hkern u1="r" u2="&#x153;" k="2" />
<hkern u1="r" u2="&#x2013;" k="5" />
<hkern u1="r" u2="&#x2014;" k="5" />
<hkern u1="r" u2="&#x2026;" k="40" />
<hkern u1="v" u2="&#x2c;" k="31" />
<hkern u1="v" u2="." k="31" />
<hkern u1="v" u2="&#x2026;" k="31" />
<hkern u1="w" u2="&#x2c;" k="21" />
<hkern u1="w" u2="." k="21" />
<hkern u1="w" u2="&#x2026;" k="21" />
<hkern u1="y" u2="&#x2c;" k="36" />
<hkern u1="y" u2="." k="36" />
<hkern u1="y" u2="&#x2026;" k="36" />
<hkern u1="{" u2="j" k="-28" />
<hkern u1="&#xad;" u2="T" k="50" />
<hkern u1="&#xad;" u2="V" k="14" />
<hkern u1="&#xad;" u2="W" k="5" />
<hkern u1="&#xad;" u2="X" k="18" />
<hkern u1="&#xad;" u2="Y" k="52" />
<hkern u1="&#xad;" u2="&#xdd;" k="52" />
<hkern u1="&#xad;" u2="&#x178;" k="52" />
<hkern u1="&#xc0;" u2="A" k="-10" />
<hkern u1="&#xc0;" u2="T" k="35" />
<hkern u1="&#xc0;" u2="U" k="-5" />
<hkern u1="&#xc0;" u2="V" k="28" />
<hkern u1="&#xc0;" u2="W" k="19" />
<hkern u1="&#xc0;" u2="X" k="-15" />
<hkern u1="&#xc0;" u2="Y" k="37" />
<hkern u1="&#xc0;" u2="Z" k="-10" />
<hkern u1="&#xc0;" u2="w" k="5" />
<hkern u1="&#xc0;" u2="x" k="-9" />
<hkern u1="&#xc0;" u2="z" k="-5" />
<hkern u1="&#xc0;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc0;" u2="&#xd9;" k="-5" />
<hkern u1="&#xc0;" u2="&#xda;" k="-5" />
<hkern u1="&#xc0;" u2="&#xdb;" k="-5" />
<hkern u1="&#xc0;" u2="&#xdc;" k="-5" />
<hkern u1="&#xc0;" u2="&#xdd;" k="37" />
<hkern u1="&#xc0;" u2="&#x178;" k="37" />
<hkern u1="&#xc0;" u2="&#x17d;" k="-10" />
<hkern u1="&#xc0;" u2="&#x2018;" k="22" />
<hkern u1="&#xc0;" u2="&#x2019;" k="22" />
<hkern u1="&#xc0;" u2="&#x201c;" k="22" />
<hkern u1="&#xc0;" u2="&#x201d;" k="22" />
<hkern u1="&#xc0;" u2="&#x2122;" k="43" />
<hkern u1="&#xc1;" u2="A" k="-10" />
<hkern u1="&#xc1;" u2="T" k="35" />
<hkern u1="&#xc1;" u2="U" k="-5" />
<hkern u1="&#xc1;" u2="V" k="28" />
<hkern u1="&#xc1;" u2="W" k="19" />
<hkern u1="&#xc1;" u2="X" k="-15" />
<hkern u1="&#xc1;" u2="Y" k="37" />
<hkern u1="&#xc1;" u2="Z" k="-10" />
<hkern u1="&#xc1;" u2="w" k="5" />
<hkern u1="&#xc1;" u2="x" k="-9" />
<hkern u1="&#xc1;" u2="z" k="-5" />
<hkern u1="&#xc1;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc1;" u2="&#xd9;" k="-5" />
<hkern u1="&#xc1;" u2="&#xda;" k="-5" />
<hkern u1="&#xc1;" u2="&#xdb;" k="-5" />
<hkern u1="&#xc1;" u2="&#xdc;" k="-5" />
<hkern u1="&#xc1;" u2="&#xdd;" k="37" />
<hkern u1="&#xc1;" u2="&#x178;" k="37" />
<hkern u1="&#xc1;" u2="&#x17d;" k="-10" />
<hkern u1="&#xc1;" u2="&#x2018;" k="22" />
<hkern u1="&#xc1;" u2="&#x2019;" k="22" />
<hkern u1="&#xc1;" u2="&#x201c;" k="22" />
<hkern u1="&#xc1;" u2="&#x201d;" k="22" />
<hkern u1="&#xc1;" u2="&#x2122;" k="43" />
<hkern u1="&#xc2;" u2="A" k="-10" />
<hkern u1="&#xc2;" u2="T" k="35" />
<hkern u1="&#xc2;" u2="U" k="-5" />
<hkern u1="&#xc2;" u2="V" k="28" />
<hkern u1="&#xc2;" u2="W" k="19" />
<hkern u1="&#xc2;" u2="X" k="-15" />
<hkern u1="&#xc2;" u2="Y" k="37" />
<hkern u1="&#xc2;" u2="Z" k="-10" />
<hkern u1="&#xc2;" u2="w" k="5" />
<hkern u1="&#xc2;" u2="x" k="-9" />
<hkern u1="&#xc2;" u2="z" k="-5" />
<hkern u1="&#xc2;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc2;" u2="&#xd9;" k="-5" />
<hkern u1="&#xc2;" u2="&#xda;" k="-5" />
<hkern u1="&#xc2;" u2="&#xdb;" k="-5" />
<hkern u1="&#xc2;" u2="&#xdc;" k="-5" />
<hkern u1="&#xc2;" u2="&#xdd;" k="37" />
<hkern u1="&#xc2;" u2="&#x178;" k="37" />
<hkern u1="&#xc2;" u2="&#x17d;" k="-10" />
<hkern u1="&#xc2;" u2="&#x2018;" k="22" />
<hkern u1="&#xc2;" u2="&#x2019;" k="22" />
<hkern u1="&#xc2;" u2="&#x201c;" k="22" />
<hkern u1="&#xc2;" u2="&#x201d;" k="22" />
<hkern u1="&#xc2;" u2="&#x2122;" k="43" />
<hkern u1="&#xc3;" u2="A" k="-10" />
<hkern u1="&#xc3;" u2="T" k="35" />
<hkern u1="&#xc3;" u2="U" k="-5" />
<hkern u1="&#xc3;" u2="V" k="28" />
<hkern u1="&#xc3;" u2="W" k="19" />
<hkern u1="&#xc3;" u2="X" k="-15" />
<hkern u1="&#xc3;" u2="Y" k="37" />
<hkern u1="&#xc3;" u2="Z" k="-10" />
<hkern u1="&#xc3;" u2="w" k="5" />
<hkern u1="&#xc3;" u2="x" k="-9" />
<hkern u1="&#xc3;" u2="z" k="-5" />
<hkern u1="&#xc3;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc3;" u2="&#xd9;" k="-5" />
<hkern u1="&#xc3;" u2="&#xda;" k="-5" />
<hkern u1="&#xc3;" u2="&#xdb;" k="-5" />
<hkern u1="&#xc3;" u2="&#xdc;" k="-5" />
<hkern u1="&#xc3;" u2="&#xdd;" k="37" />
<hkern u1="&#xc3;" u2="&#x178;" k="37" />
<hkern u1="&#xc3;" u2="&#x17d;" k="-10" />
<hkern u1="&#xc3;" u2="&#x2018;" k="22" />
<hkern u1="&#xc3;" u2="&#x2019;" k="22" />
<hkern u1="&#xc3;" u2="&#x201c;" k="22" />
<hkern u1="&#xc3;" u2="&#x201d;" k="22" />
<hkern u1="&#xc3;" u2="&#x2122;" k="43" />
<hkern u1="&#xc4;" u2="A" k="-10" />
<hkern u1="&#xc4;" u2="T" k="35" />
<hkern u1="&#xc4;" u2="U" k="-5" />
<hkern u1="&#xc4;" u2="V" k="28" />
<hkern u1="&#xc4;" u2="W" k="19" />
<hkern u1="&#xc4;" u2="X" k="-15" />
<hkern u1="&#xc4;" u2="Y" k="37" />
<hkern u1="&#xc4;" u2="Z" k="-10" />
<hkern u1="&#xc4;" u2="w" k="5" />
<hkern u1="&#xc4;" u2="x" k="-9" />
<hkern u1="&#xc4;" u2="z" k="-5" />
<hkern u1="&#xc4;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc4;" u2="&#xd9;" k="-5" />
<hkern u1="&#xc4;" u2="&#xda;" k="-5" />
<hkern u1="&#xc4;" u2="&#xdb;" k="-5" />
<hkern u1="&#xc4;" u2="&#xdc;" k="-5" />
<hkern u1="&#xc4;" u2="&#xdd;" k="37" />
<hkern u1="&#xc4;" u2="&#x178;" k="37" />
<hkern u1="&#xc4;" u2="&#x17d;" k="-10" />
<hkern u1="&#xc4;" u2="&#x2018;" k="22" />
<hkern u1="&#xc4;" u2="&#x2019;" k="22" />
<hkern u1="&#xc4;" u2="&#x201c;" k="22" />
<hkern u1="&#xc4;" u2="&#x201d;" k="22" />
<hkern u1="&#xc4;" u2="&#x2122;" k="43" />
<hkern u1="&#xc5;" u2="A" k="-10" />
<hkern u1="&#xc5;" u2="T" k="35" />
<hkern u1="&#xc5;" u2="U" k="-5" />
<hkern u1="&#xc5;" u2="V" k="28" />
<hkern u1="&#xc5;" u2="W" k="19" />
<hkern u1="&#xc5;" u2="X" k="-15" />
<hkern u1="&#xc5;" u2="Y" k="37" />
<hkern u1="&#xc5;" u2="Z" k="-10" />
<hkern u1="&#xc5;" u2="w" k="5" />
<hkern u1="&#xc5;" u2="x" k="-9" />
<hkern u1="&#xc5;" u2="z" k="-5" />
<hkern u1="&#xc5;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc5;" u2="&#xd9;" k="-5" />
<hkern u1="&#xc5;" u2="&#xda;" k="-5" />
<hkern u1="&#xc5;" u2="&#xdb;" k="-5" />
<hkern u1="&#xc5;" u2="&#xdc;" k="-5" />
<hkern u1="&#xc5;" u2="&#xdd;" k="37" />
<hkern u1="&#xc5;" u2="&#x178;" k="37" />
<hkern u1="&#xc5;" u2="&#x17d;" k="-10" />
<hkern u1="&#xc5;" u2="&#x2018;" k="22" />
<hkern u1="&#xc5;" u2="&#x2019;" k="22" />
<hkern u1="&#xc5;" u2="&#x201c;" k="22" />
<hkern u1="&#xc5;" u2="&#x201d;" k="22" />
<hkern u1="&#xc5;" u2="&#x2122;" k="43" />
<hkern u1="&#xc7;" u2="T" k="3" />
<hkern u1="&#xc7;" u2="X" k="10" />
<hkern u1="&#xc7;" u2="Y" k="14" />
<hkern u1="&#xc7;" u2="&#xc6;" k="3" />
<hkern u1="&#xc7;" u2="&#xdd;" k="14" />
<hkern u1="&#xc7;" u2="&#x178;" k="14" />
<hkern u1="&#xd2;" u2="T" k="3" />
<hkern u1="&#xd2;" u2="X" k="10" />
<hkern u1="&#xd2;" u2="Y" k="14" />
<hkern u1="&#xd2;" u2="&#xc6;" k="3" />
<hkern u1="&#xd2;" u2="&#xdd;" k="14" />
<hkern u1="&#xd2;" u2="&#x178;" k="14" />
<hkern u1="&#xd3;" u2="T" k="3" />
<hkern u1="&#xd3;" u2="X" k="10" />
<hkern u1="&#xd3;" u2="Y" k="14" />
<hkern u1="&#xd3;" u2="&#xc6;" k="3" />
<hkern u1="&#xd3;" u2="&#xdd;" k="14" />
<hkern u1="&#xd3;" u2="&#x178;" k="14" />
<hkern u1="&#xd4;" u2="T" k="3" />
<hkern u1="&#xd4;" u2="X" k="10" />
<hkern u1="&#xd4;" u2="Y" k="14" />
<hkern u1="&#xd4;" u2="&#xc6;" k="3" />
<hkern u1="&#xd4;" u2="&#xdd;" k="14" />
<hkern u1="&#xd4;" u2="&#x178;" k="14" />
<hkern u1="&#xd5;" u2="T" k="3" />
<hkern u1="&#xd5;" u2="X" k="10" />
<hkern u1="&#xd5;" u2="Y" k="14" />
<hkern u1="&#xd5;" u2="&#xc6;" k="3" />
<hkern u1="&#xd5;" u2="&#xdd;" k="14" />
<hkern u1="&#xd5;" u2="&#x178;" k="14" />
<hkern u1="&#xd6;" u2="T" k="3" />
<hkern u1="&#xd6;" u2="X" k="10" />
<hkern u1="&#xd6;" u2="Y" k="14" />
<hkern u1="&#xd6;" u2="&#xc6;" k="3" />
<hkern u1="&#xd6;" u2="&#xdd;" k="14" />
<hkern u1="&#xd6;" u2="&#x178;" k="14" />
<hkern u1="&#xd8;" u2="T" k="3" />
<hkern u1="&#xd8;" u2="X" k="10" />
<hkern u1="&#xd8;" u2="Y" k="14" />
<hkern u1="&#xd8;" u2="&#xc6;" k="3" />
<hkern u1="&#xd8;" u2="&#xdd;" k="14" />
<hkern u1="&#xd8;" u2="&#x178;" k="14" />
<hkern u1="&#xd9;" u2="A" k="-5" />
<hkern u1="&#xd9;" u2="&#xc0;" k="-5" />
<hkern u1="&#xd9;" u2="&#xc1;" k="-5" />
<hkern u1="&#xd9;" u2="&#xc2;" k="-5" />
<hkern u1="&#xd9;" u2="&#xc3;" k="-5" />
<hkern u1="&#xd9;" u2="&#xc4;" k="-5" />
<hkern u1="&#xd9;" u2="&#xc5;" k="-5" />
<hkern u1="&#xda;" u2="A" k="-5" />
<hkern u1="&#xda;" u2="&#xc0;" k="-5" />
<hkern u1="&#xda;" u2="&#xc1;" k="-5" />
<hkern u1="&#xda;" u2="&#xc2;" k="-5" />
<hkern u1="&#xda;" u2="&#xc3;" k="-5" />
<hkern u1="&#xda;" u2="&#xc4;" k="-5" />
<hkern u1="&#xda;" u2="&#xc5;" k="-5" />
<hkern u1="&#xdb;" u2="A" k="-5" />
<hkern u1="&#xdb;" u2="&#xc0;" k="-5" />
<hkern u1="&#xdb;" u2="&#xc1;" k="-5" />
<hkern u1="&#xdb;" u2="&#xc2;" k="-5" />
<hkern u1="&#xdb;" u2="&#xc3;" k="-5" />
<hkern u1="&#xdb;" u2="&#xc4;" k="-5" />
<hkern u1="&#xdb;" u2="&#xc5;" k="-5" />
<hkern u1="&#xdc;" u2="A" k="-5" />
<hkern u1="&#xdc;" u2="&#xc0;" k="-5" />
<hkern u1="&#xdc;" u2="&#xc1;" k="-5" />
<hkern u1="&#xdc;" u2="&#xc2;" k="-5" />
<hkern u1="&#xdc;" u2="&#xc3;" k="-5" />
<hkern u1="&#xdc;" u2="&#xc4;" k="-5" />
<hkern u1="&#xdc;" u2="&#xc5;" k="-5" />
<hkern u1="&#xdd;" u2="&#x2c;" k="68" />
<hkern u1="&#xdd;" u2="-" k="52" />
<hkern u1="&#xdd;" u2="." k="68" />
<hkern u1="&#xdd;" u2="/" k="30" />
<hkern u1="&#xdd;" u2=":" k="29" />
<hkern u1="&#xdd;" u2=";" k="29" />
<hkern u1="&#xdd;" u2="A" k="41" />
<hkern u1="&#xdd;" u2="C" k="14" />
<hkern u1="&#xdd;" u2="G" k="14" />
<hkern u1="&#xdd;" u2="J" k="73" />
<hkern u1="&#xdd;" u2="O" k="14" />
<hkern u1="&#xdd;" u2="Q" k="14" />
<hkern u1="&#xdd;" u2="a" k="50" />
<hkern u1="&#xdd;" u2="c" k="50" />
<hkern u1="&#xdd;" u2="e" k="50" />
<hkern u1="&#xdd;" u2="m" k="30" />
<hkern u1="&#xdd;" u2="n" k="30" />
<hkern u1="&#xdd;" u2="o" k="50" />
<hkern u1="&#xdd;" u2="r" k="30" />
<hkern u1="&#xdd;" u2="s" k="45" />
<hkern u1="&#xdd;" u2="u" k="25" />
<hkern u1="&#xdd;" u2="v" k="20" />
<hkern u1="&#xdd;" u2="&#xad;" k="52" />
<hkern u1="&#xdd;" u2="&#xc0;" k="41" />
<hkern u1="&#xdd;" u2="&#xc1;" k="41" />
<hkern u1="&#xdd;" u2="&#xc2;" k="41" />
<hkern u1="&#xdd;" u2="&#xc3;" k="41" />
<hkern u1="&#xdd;" u2="&#xc4;" k="41" />
<hkern u1="&#xdd;" u2="&#xc5;" k="41" />
<hkern u1="&#xdd;" u2="&#xc6;" k="60" />
<hkern u1="&#xdd;" u2="&#xc7;" k="14" />
<hkern u1="&#xdd;" u2="&#xd2;" k="14" />
<hkern u1="&#xdd;" u2="&#xd3;" k="14" />
<hkern u1="&#xdd;" u2="&#xd4;" k="14" />
<hkern u1="&#xdd;" u2="&#xd5;" k="14" />
<hkern u1="&#xdd;" u2="&#xd6;" k="14" />
<hkern u1="&#xdd;" u2="&#xd8;" k="14" />
<hkern u1="&#xdd;" u2="&#xe0;" k="50" />
<hkern u1="&#xdd;" u2="&#xe1;" k="50" />
<hkern u1="&#xdd;" u2="&#xe2;" k="50" />
<hkern u1="&#xdd;" u2="&#xe6;" k="50" />
<hkern u1="&#xdd;" u2="&#xe7;" k="50" />
<hkern u1="&#xdd;" u2="&#xe8;" k="50" />
<hkern u1="&#xdd;" u2="&#xe9;" k="50" />
<hkern u1="&#xdd;" u2="&#xea;" k="50" />
<hkern u1="&#xdd;" u2="&#xf1;" k="30" />
<hkern u1="&#xdd;" u2="&#xf2;" k="50" />
<hkern u1="&#xdd;" u2="&#xf3;" k="50" />
<hkern u1="&#xdd;" u2="&#xf4;" k="50" />
<hkern u1="&#xdd;" u2="&#xf8;" k="50" />
<hkern u1="&#xdd;" u2="&#xf9;" k="25" />
<hkern u1="&#xdd;" u2="&#xfa;" k="25" />
<hkern u1="&#xdd;" u2="&#xfb;" k="25" />
<hkern u1="&#xdd;" u2="&#x131;" k="30" />
<hkern u1="&#xdd;" u2="&#x152;" k="14" />
<hkern u1="&#xdd;" u2="&#x153;" k="50" />
<hkern u1="&#xdd;" u2="&#x2013;" k="52" />
<hkern u1="&#xdd;" u2="&#x2014;" k="52" />
<hkern u1="&#xdd;" u2="&#x2026;" k="68" />
<hkern u1="&#xde;" u2="J" k="26" />
<hkern u1="&#xfd;" u2="&#x2c;" k="36" />
<hkern u1="&#xfd;" u2="." k="36" />
<hkern u1="&#xfd;" u2="&#x2026;" k="36" />
<hkern u1="&#xff;" u2="&#x2c;" k="36" />
<hkern u1="&#xff;" u2="." k="36" />
<hkern u1="&#xff;" u2="&#x2026;" k="36" />
<hkern u1="&#x160;" u2="T" k="3" />
<hkern u1="&#x178;" u2="&#x2c;" k="68" />
<hkern u1="&#x178;" u2="-" k="52" />
<hkern u1="&#x178;" u2="." k="68" />
<hkern u1="&#x178;" u2="/" k="30" />
<hkern u1="&#x178;" u2=":" k="29" />
<hkern u1="&#x178;" u2=";" k="29" />
<hkern u1="&#x178;" u2="A" k="41" />
<hkern u1="&#x178;" u2="C" k="14" />
<hkern u1="&#x178;" u2="G" k="14" />
<hkern u1="&#x178;" u2="J" k="73" />
<hkern u1="&#x178;" u2="O" k="14" />
<hkern u1="&#x178;" u2="Q" k="14" />
<hkern u1="&#x178;" u2="a" k="50" />
<hkern u1="&#x178;" u2="c" k="50" />
<hkern u1="&#x178;" u2="e" k="50" />
<hkern u1="&#x178;" u2="m" k="30" />
<hkern u1="&#x178;" u2="n" k="30" />
<hkern u1="&#x178;" u2="o" k="50" />
<hkern u1="&#x178;" u2="r" k="30" />
<hkern u1="&#x178;" u2="s" k="45" />
<hkern u1="&#x178;" u2="u" k="25" />
<hkern u1="&#x178;" u2="v" k="20" />
<hkern u1="&#x178;" u2="&#xad;" k="52" />
<hkern u1="&#x178;" u2="&#xc0;" k="41" />
<hkern u1="&#x178;" u2="&#xc1;" k="41" />
<hkern u1="&#x178;" u2="&#xc2;" k="41" />
<hkern u1="&#x178;" u2="&#xc3;" k="41" />
<hkern u1="&#x178;" u2="&#xc4;" k="41" />
<hkern u1="&#x178;" u2="&#xc5;" k="41" />
<hkern u1="&#x178;" u2="&#xc6;" k="60" />
<hkern u1="&#x178;" u2="&#xc7;" k="14" />
<hkern u1="&#x178;" u2="&#xd2;" k="14" />
<hkern u1="&#x178;" u2="&#xd3;" k="14" />
<hkern u1="&#x178;" u2="&#xd4;" k="14" />
<hkern u1="&#x178;" u2="&#xd5;" k="14" />
<hkern u1="&#x178;" u2="&#xd6;" k="14" />
<hkern u1="&#x178;" u2="&#xd8;" k="14" />
<hkern u1="&#x178;" u2="&#xe0;" k="50" />
<hkern u1="&#x178;" u2="&#xe1;" k="50" />
<hkern u1="&#x178;" u2="&#xe2;" k="50" />
<hkern u1="&#x178;" u2="&#xe6;" k="50" />
<hkern u1="&#x178;" u2="&#xe7;" k="50" />
<hkern u1="&#x178;" u2="&#xe8;" k="50" />
<hkern u1="&#x178;" u2="&#xe9;" k="50" />
<hkern u1="&#x178;" u2="&#xea;" k="50" />
<hkern u1="&#x178;" u2="&#xf1;" k="30" />
<hkern u1="&#x178;" u2="&#xf2;" k="50" />
<hkern u1="&#x178;" u2="&#xf3;" k="50" />
<hkern u1="&#x178;" u2="&#xf4;" k="50" />
<hkern u1="&#x178;" u2="&#xf8;" k="50" />
<hkern u1="&#x178;" u2="&#xf9;" k="25" />
<hkern u1="&#x178;" u2="&#xfa;" k="25" />
<hkern u1="&#x178;" u2="&#xfb;" k="25" />
<hkern u1="&#x178;" u2="&#x131;" k="30" />
<hkern u1="&#x178;" u2="&#x152;" k="14" />
<hkern u1="&#x178;" u2="&#x153;" k="50" />
<hkern u1="&#x178;" u2="&#x2013;" k="52" />
<hkern u1="&#x178;" u2="&#x2014;" k="52" />
<hkern u1="&#x178;" u2="&#x2026;" k="68" />
<hkern u1="&#x17d;" u2="w" k="5" />
<hkern u1="&#x17d;" u2="y" k="3" />
<hkern u1="&#x17d;" u2="&#xfd;" k="3" />
<hkern u1="&#x2013;" u2="T" k="50" />
<hkern u1="&#x2013;" u2="V" k="14" />
<hkern u1="&#x2013;" u2="W" k="5" />
<hkern u1="&#x2013;" u2="X" k="18" />
<hkern u1="&#x2013;" u2="Y" k="52" />
<hkern u1="&#x2013;" u2="&#xdd;" k="52" />
<hkern u1="&#x2013;" u2="&#x178;" k="52" />
<hkern u1="&#x2014;" u2="T" k="50" />
<hkern u1="&#x2014;" u2="V" k="14" />
<hkern u1="&#x2014;" u2="W" k="5" />
<hkern u1="&#x2014;" u2="X" k="18" />
<hkern u1="&#x2014;" u2="Y" k="52" />
<hkern u1="&#x2014;" u2="&#xdd;" k="52" />
<hkern u1="&#x2014;" u2="&#x178;" k="52" />
<hkern u1="&#x2018;" u2="&#x2c;" k="56" />
<hkern u1="&#x2018;" u2="." k="56" />
<hkern u1="&#x2018;" u2="A" k="22" />
<hkern u1="&#x2018;" u2="J" k="95" />
<hkern u1="&#x2018;" u2="a" k="6" />
<hkern u1="&#x2018;" u2="c" k="5" />
<hkern u1="&#x2018;" u2="e" k="5" />
<hkern u1="&#x2018;" u2="o" k="5" />
<hkern u1="&#x2018;" u2="&#xc0;" k="22" />
<hkern u1="&#x2018;" u2="&#xc1;" k="22" />
<hkern u1="&#x2018;" u2="&#xc2;" k="22" />
<hkern u1="&#x2018;" u2="&#xc3;" k="22" />
<hkern u1="&#x2018;" u2="&#xc4;" k="22" />
<hkern u1="&#x2018;" u2="&#xc5;" k="22" />
<hkern u1="&#x2018;" u2="&#xc6;" k="48" />
<hkern u1="&#x2018;" u2="&#xe0;" k="6" />
<hkern u1="&#x2018;" u2="&#xe1;" k="6" />
<hkern u1="&#x2018;" u2="&#xe2;" k="6" />
<hkern u1="&#x2018;" u2="&#xe6;" k="6" />
<hkern u1="&#x2018;" u2="&#xe7;" k="5" />
<hkern u1="&#x2018;" u2="&#xe8;" k="5" />
<hkern u1="&#x2018;" u2="&#xe9;" k="5" />
<hkern u1="&#x2018;" u2="&#xea;" k="5" />
<hkern u1="&#x2018;" u2="&#xf2;" k="5" />
<hkern u1="&#x2018;" u2="&#xf3;" k="5" />
<hkern u1="&#x2018;" u2="&#xf4;" k="5" />
<hkern u1="&#x2018;" u2="&#xf8;" k="5" />
<hkern u1="&#x2018;" u2="&#x153;" k="5" />
<hkern u1="&#x2018;" u2="&#x2026;" k="56" />
<hkern u1="&#x2019;" u2="&#x2c;" k="120" />
<hkern u1="&#x2019;" u2="." k="120" />
<hkern u1="&#x2019;" u2="A" k="39" />
<hkern u1="&#x2019;" u2="a" k="23" />
<hkern u1="&#x2019;" u2="c" k="13" />
<hkern u1="&#x2019;" u2="e" k="13" />
<hkern u1="&#x2019;" u2="o" k="13" />
<hkern u1="&#x2019;" u2="s" k="14" />
<hkern u1="&#x2019;" u2="&#xc0;" k="39" />
<hkern u1="&#x2019;" u2="&#xc1;" k="39" />
<hkern u1="&#x2019;" u2="&#xc2;" k="39" />
<hkern u1="&#x2019;" u2="&#xc3;" k="39" />
<hkern u1="&#x2019;" u2="&#xc4;" k="39" />
<hkern u1="&#x2019;" u2="&#xc5;" k="39" />
<hkern u1="&#x2019;" u2="&#xe0;" k="23" />
<hkern u1="&#x2019;" u2="&#xe1;" k="23" />
<hkern u1="&#x2019;" u2="&#xe2;" k="23" />
<hkern u1="&#x2019;" u2="&#xe6;" k="23" />
<hkern u1="&#x2019;" u2="&#xe7;" k="13" />
<hkern u1="&#x2019;" u2="&#xe8;" k="13" />
<hkern u1="&#x2019;" u2="&#xe9;" k="13" />
<hkern u1="&#x2019;" u2="&#xea;" k="13" />
<hkern u1="&#x2019;" u2="&#xf2;" k="13" />
<hkern u1="&#x2019;" u2="&#xf3;" k="13" />
<hkern u1="&#x2019;" u2="&#xf4;" k="13" />
<hkern u1="&#x2019;" u2="&#xf8;" k="13" />
<hkern u1="&#x2019;" u2="&#x153;" k="13" />
<hkern u1="&#x2019;" u2="&#x2026;" k="120" />
<hkern u1="&#x201c;" u2="&#x2c;" k="56" />
<hkern u1="&#x201c;" u2="." k="56" />
<hkern u1="&#x201c;" u2="A" k="22" />
<hkern u1="&#x201c;" u2="J" k="95" />
<hkern u1="&#x201c;" u2="a" k="6" />
<hkern u1="&#x201c;" u2="c" k="5" />
<hkern u1="&#x201c;" u2="e" k="5" />
<hkern u1="&#x201c;" u2="o" k="5" />
<hkern u1="&#x201c;" u2="&#xc0;" k="22" />
<hkern u1="&#x201c;" u2="&#xc1;" k="22" />
<hkern u1="&#x201c;" u2="&#xc2;" k="22" />
<hkern u1="&#x201c;" u2="&#xc3;" k="22" />
<hkern u1="&#x201c;" u2="&#xc4;" k="22" />
<hkern u1="&#x201c;" u2="&#xc5;" k="22" />
<hkern u1="&#x201c;" u2="&#xc6;" k="48" />
<hkern u1="&#x201c;" u2="&#xe0;" k="6" />
<hkern u1="&#x201c;" u2="&#xe1;" k="6" />
<hkern u1="&#x201c;" u2="&#xe2;" k="6" />
<hkern u1="&#x201c;" u2="&#xe6;" k="6" />
<hkern u1="&#x201c;" u2="&#xe7;" k="5" />
<hkern u1="&#x201c;" u2="&#xe8;" k="5" />
<hkern u1="&#x201c;" u2="&#xe9;" k="5" />
<hkern u1="&#x201c;" u2="&#xea;" k="5" />
<hkern u1="&#x201c;" u2="&#xf2;" k="5" />
<hkern u1="&#x201c;" u2="&#xf3;" k="5" />
<hkern u1="&#x201c;" u2="&#xf4;" k="5" />
<hkern u1="&#x201c;" u2="&#xf8;" k="5" />
<hkern u1="&#x201c;" u2="&#x153;" k="5" />
<hkern u1="&#x201c;" u2="&#x2026;" k="56" />
<hkern u1="&#x201d;" u2="&#x2c;" k="120" />
<hkern u1="&#x201d;" u2="." k="120" />
<hkern u1="&#x201d;" u2="A" k="39" />
<hkern u1="&#x201d;" u2="a" k="23" />
<hkern u1="&#x201d;" u2="c" k="13" />
<hkern u1="&#x201d;" u2="e" k="13" />
<hkern u1="&#x201d;" u2="o" k="13" />
<hkern u1="&#x201d;" u2="s" k="14" />
<hkern u1="&#x201d;" u2="&#xc0;" k="39" />
<hkern u1="&#x201d;" u2="&#xc1;" k="39" />
<hkern u1="&#x201d;" u2="&#xc2;" k="39" />
<hkern u1="&#x201d;" u2="&#xc3;" k="39" />
<hkern u1="&#x201d;" u2="&#xc4;" k="39" />
<hkern u1="&#x201d;" u2="&#xc5;" k="39" />
<hkern u1="&#x201d;" u2="&#xe0;" k="23" />
<hkern u1="&#x201d;" u2="&#xe1;" k="23" />
<hkern u1="&#x201d;" u2="&#xe2;" k="23" />
<hkern u1="&#x201d;" u2="&#xe6;" k="23" />
<hkern u1="&#x201d;" u2="&#xe7;" k="13" />
<hkern u1="&#x201d;" u2="&#xe8;" k="13" />
<hkern u1="&#x201d;" u2="&#xe9;" k="13" />
<hkern u1="&#x201d;" u2="&#xea;" k="13" />
<hkern u1="&#x201d;" u2="&#xf2;" k="13" />
<hkern u1="&#x201d;" u2="&#xf3;" k="13" />
<hkern u1="&#x201d;" u2="&#xf4;" k="13" />
<hkern u1="&#x201d;" u2="&#xf8;" k="13" />
<hkern u1="&#x201d;" u2="&#x153;" k="13" />
<hkern u1="&#x201d;" u2="&#x2026;" k="120" />
<hkern u1="&#x2026;" u2="T" k="54" />
<hkern u1="&#x2026;" u2="V" k="36" />
<hkern u1="&#x2026;" u2="W" k="24" />
<hkern u1="&#x2026;" u2="Y" k="68" />
<hkern u1="&#x2026;" u2="v" k="31" />
<hkern u1="&#x2026;" u2="w" k="21" />
<hkern u1="&#x2026;" u2="y" k="31" />
<hkern u1="&#x2026;" u2="&#xdd;" k="68" />
<hkern u1="&#x2026;" u2="&#xfd;" k="31" />
<hkern u1="&#x2026;" u2="&#x178;" k="68" />
<hkern u1="&#x2026;" u2="&#x2018;" k="68" />
<hkern u1="&#x2026;" u2="&#x2019;" k="64" />
<hkern u1="&#x2026;" u2="&#x201c;" k="68" />
<hkern u1="&#x2026;" u2="&#x201d;" k="64" />

</font>
</defs>
<text x="40" y="40" font-family="Neue Helvetica W01 89 Cm Heavy" font-size="30" fill="#933" >ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz AÁÀÂÄÅÃÆ CÇ DÐ EÉÈÊË I Í Ì Î Ï NÑ</text>
<text x="40" y="80" font-family="Neue Helvetica W01 89 Cm Heavy" font-size="30" fill="#933" >OÓÒÔÖÕØŒ SŠ UÚÙÛÜ YÝŸ ZŽ Þ aáàâäåãæ cç dð eéèêë i ı í ì î ï nñ oóòôöõøœ sšß uúùûü yýÿ zž</text>
<text x="40" y="120" font-family="Neue Helvetica W01 89 Cm Heavy" font-size="30" fill="#933" >þ 1234567890 ½ ¼ ¾ % ‰ $¢£¥ƒ€¤ † ‡ § ¶ # ^~µ +×± &lt; = &gt; ÷¬ !¡?¿ &quot; &amp; &apos; * ° . , : ; () [ \ ] {} / |</text>
<text x="40" y="160" font-family="Neue Helvetica W01 89 Cm Heavy" font-size="30" fill="#933" >¦ _ ‚ „ … ‹› «» ‘ ’ “ ” • ­ - – — @ © ® ™ ªº ¹²³ ´ ` ˆ ˜ ¨ ¯ · ¸</text>
</svg>
