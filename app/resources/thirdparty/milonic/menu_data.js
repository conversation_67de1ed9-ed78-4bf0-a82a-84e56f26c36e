fixMozillaZIndex=true; //Fixes Z-Index problem  with Mozilla browsers but causes odd scrolling problem, toggle to see if it helps
_menuCloseDelay=500;
_menuOpenDelay=150;
retainClickValue=1;
_subOffsetTop=2;
_subOffsetLeft=-2;




with(menuStyle_top=new mm_style()){
bordercolor="#ffffff";
borderstyle="solid";
borderwidth=1;
fontfamily="arial, tahoma";
fontsize="70%";
fontstyle="normal";
headerbgcolor="#AFD1B5";
headercolor="#000099";
offbgcolor="#FFFFFF";
offcolor="#000000";
offborder="1px solid #DDDDDD";
onbgcolor="#FEFAD2";
onborder="1px solid #999999";
oncolor="#000000";
onsubimage="../resources/thirdparty/milonic/images/blue_7x4.gif";
padding=2;
pagebgcolor="#CFE2D1";
pagecolor="#000066";
separatoralign="right";
separatorwidth="85%";
subimage="../resources/thirdparty/milonic/images/black_7x4.gif";
}

with(menuStyle_top_2011_lister=new mm_style()){
    bordercolor="#ffffff";
    borderstyle="solid";
    borderwidth=1;
    fontstyle="normal";
    headerbgcolor="#AFD1B5";
    headercolor="#000099";
    menuZIndex=0;
    offbgcolor="#FFFFFF";
    onbgcolor="#F6E0bc";
    onsubimage="../resources/thirdparty/milonic/images/black_7x4.gif";
    padding=2;
    pagebgcolor="#CFE2D1";
    pagecolor="#000066";
    separatoralign="right";
    separatorwidth="85%";
    subimage="../resources/thirdparty/milonic/images/black_7x4.gif";
}

with(menuStyle=new mm_style()){
bordercolor="#CCCCCC";
borderstyle="solid";
borderwidth=1;
onbgcolor="#FFFFFF";
oncolor="#000000";
offbgcolor="#efefef";
offcolor="#000000";
fontfamily="arial, tahoma";
fontsize="100%";
fontstyle="normal";
headerbgcolor="#ffffff";
headercolor="#000000";
}

with(menuStyle_2011=new mm_style()){
bordercolor="#c4e4f1";
borderstyle="solid";
borderwidth=1;
onbgcolor="#edf1fa";
oncolor="#4D729B";
offbgcolor="#FBFCFE";
offcolor="#4D729B";
fontfamily="arial, tahoma";
fontsize="100%";
fontstyle="normal";
headerbgcolor="#ffffff";
headercolor="#000000";
}

with(exportButton_2011=new mm_style()){
    bordercolor="#c4e4f1";
    borderstyle="solid";
    borderwidth=1;
    onbgcolor="#edf1fa";
    oncolor="#4D729B";
    offbgcolor="#FBFCFE";
    offcolor="#4D729B";
    fontfamily="arial, tahoma";
    fontsize="100%";
    fontstyle="normal";
    headerbgcolor="#ffffff";
    headercolor="#000000";
}

function createListControlsMenu(menuName, menuStyle) {
    
    with(milonic=new menuname(menuName)){
        overflow="scroll";
        style=menuStyle;
        if (sysViewStr && sysViewStr != "") {
            var controls = sysViewStr.split("~~");

            for (var i = 0; i < controls.length; i++ )
            {
                aI("text="+controls[i]+";url=Javascript:SelectListControl('Go','"+controls[i]+"');offclass=menu_center;onclass=menu_center;");
            }       
        }

        if (uvStr && uvStr != "") {
            var userviews = uvStr.split("~~");
            aI("text=<hr/>;url=#;offclass=menu_center;onclass=menu_center;");
            for (var i = 0; i < userviews.length; i++ ) {
                aI("text="+userviews[i]+";url=Javascript:SelectListControl('Go','"+userviews[i]+"');offclass=menu_center;onclass=menu_center;");
            }
        }

        if (gvStr && gvStr != "") {
            var globalviews = gvStr.split("~~");
            aI("text=<hr/>;url=#;offclass=menu_center;onclass=menu_center;");
            for (var i = 0; i < globalviews.length; i++ ) {
                aI("text="+globalviews[i]+";url=Javascript:SelectListControl('Go','"+globalviews[i]+"');offclass=menu_center;onclass=menu_center;");
            }
        }

    }
    
}

createListControlsMenu("ListControls", menuStyle);
createListControlsMenu("ListControls_2011", menuStyle_2011);

function createSubExportMenu(menuName, menuStyle) {

    with(milonic=new menuname(menuName)){
    	overflow="scroll";
    	style=menuStyle;
    	aI("text=CSV;url=#Skip;clickfunction=Javascript:ExportData('_csv');offclass=menu_center;onclass=menu_center;");
    	aI("text=Excel;url=#Skip;clickfunction=Javascript:ExportData('_excel');offclass=menu_center;onclass=menu_center;");
    	aI("text=Word;url=#Skip;clickfunction=Javascript:ExportData('_word');offclass=menu_center;onclass=menu_center;");
    	aI("text=PDF;url=#Skip;clickfunction=Javascript:ExportData('_pdf');offclass=menu_center;onclass=menu_center;");
		if(csvImportStr == 1){
			aI("text=CSV for Import;url=#Skip;clickfunction=Javascript:ExportData('_csvimport');offclass=menu_center;onclass=menu_center;");
		}
    }

}

createSubExportMenu("sub_Export", menuStyle);
createSubExportMenu("sub_Export_2011", menuStyle_2011);

function createGroupsMenu(menuName, menuStyle) {
    
    with(milonic=new menuname(menuName)){
        overflow="scroll";
        style=menuStyle;
        if (groupsMenuStr && groupsMenuStr != "") {
            var controls = groupsMenuStr.split("~~");

            for (var i = 0; i < controls.length; i++ )
            {
                aI("text="+controls[i]+";url=Javascript:SelectGroups('"+controls[i]+"');offclass=menu_center;onclass=menu_center;");
            }       
        }

    }
    
}

createGroupsMenu("Groups", menuStyle);
createGroupsMenu("Groups_2011", menuStyle_2011);

drawMenus();

