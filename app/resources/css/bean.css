/* http://stackoverflow.com/questions/9129182/iframe-100-height-causes-vertical-scrollbar */


body.DASHBOARD iframe {
    display: block;
}

.dashsnap_scrim_parent {
    position: absolute;
    top: 0;
    left: 0;
}

.dashsnap_header {
    background-color: black;
    height: 64px;
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    z-index: 10;
}

.dashsnap_header_text {
    color: white;
    font-family: 'HelveticaNeueW01-Thin', 'Helvetica Light', 'Ariel Light', 'sans-serif';
    font-weight: bold;
    font-size: 21px;
    padding: 12px;
    cursor: default;
    top: 5px;
}

.dashsnap_header_close {
    position: fixed;
    right: 10px;
    cursor: pointer;
}

.dashsnap_header_hint {
    position: fixed;
    left: 10px;
}


.dashsnap_scrim_dark {
    background-color: black;
    opacity: .5;
    position: absolute;
}

.dashsnap_scrim_target {
    background-color: transparent;
    position: absolute;
    cursor: pointer;
}

.dashsnap_scrim_target_border {
    background-color: black;
    position: absolute;
    bottom: 0px;
    height: 40px;
    width: 100%;
}

.dashsnap_scrim_target_title {
    color: white;
    position: absolute;
    bottom: 0px;
    left: 0px;
    font-size: 12px;
    line-height: 12px;
    padding: 8px;
    margin: 6px;
    display: inline-block;
}

.dashsnap_scrim_target_button {
    position: absolute;
    bottom: 0px;
    right: 5px;
    color: white;
    background-color: #06cbf9;
    padding: 8px;
    margin: 6px;
    font-size: 12px;
    line-height: 12px;
    display: inline-block;
}

/* Calendar Day Header - the cells showing Sunday, Monday .... */
.CalHead {cursor:default; text-align:center;}
/* Day of Week - the Anchor inside CalHead showing the day of week */
.DoWAnchor {text-decoration:none; font:bold 7.5pt Tahoma, Verdana, sans-serif; color:white;}
/* Day Column Cell - the TD cell of each day */
.CalCol {}
/* Day Cell - the DIV cell inside TD */
.CalCell {cursor:pointer; text-align:center;}
/* Day Number - the Anchor inside DIV showing the day number  */
.CellAnchor {text-decoration:none; font:9pt verdana;}

/* WeekNo Header - the top header cell of the week number column */
.WeekHead {cursor:default; text-align:center; font:bold 10pt arial; color:white;}
/* WeekNo Column Cell - the cells of the week number column */
.WeekCol {cursor:default; text-align:center; background:#b0c4de;}
/* WeekNo Anchor style */
.WeekAnchor {text-decoration:none; font:bold 8pt arial; color:white;}

/* Month navigators - the < or > used to move to previous or next month */
.MonthNav {cursor:pointer; background-color:transparent; vertical-align:middle; font:bold 10pt "arial bold"; color:black;}
A.MonthNav:hover {color:yellow}
A.MonthNav:active {color:red}

/* styles for the 3 calendar sections (actually 3 table TDs) */
.CalTop {text-align:center;}
.CalMiddle {}
.CalBottom {text-align:center;}

/* Calendar title - showing year and month. when giDCStyle=0, it's the style of the year/month dropdowns; giDCStyle>0, it's the style to show gsCalTitle.  */
.CalTitle {vertical-align:middle; font:10pt arial; color:black;}

/* The style of internal floating div/layer tags, which are usually used to create the artificial dropdown selectors. */
.FreeDiv {}

/* The style of the outer TABLE tag which is the outer calendar panel. */
#outerTable {border:2px ridge #ffffff;}
/* The style of the inner DIV tag that holds the inner panel of all calendar cells. */
#innerDiv {border:1px solid #dcdcdc;}
/*===== Above CSS styles are commonly used in the script engine =====*/

/*====== Following are additional per-theme styles, e.g. the inner dropdown selectors and today etc. You may have your own defined. ======*/
.BottomAnchor {text-decoration:none; font:bold 9pt Tahoma, Verdana; color:white;}
A.BottomAnchor:hover {color:yellow;}
A.BottomAnchor:active {color:orange;}



/** Dashboard specific css **/

/** DB Common section **/

TD {
    font-family: Helvetica;
    font-size:11px;
    color: #070707;
}

TABLE.DASHHEADER {WIDTH:100%;border-width:0px;padding-bottom:0px;padding-right:0px;padding-top:0px;valign:bottom}

BODY {overflow:auto;padding:0px;background:#fff;}

body.DASHBOARD td.dashcolumn,
body.DASHBOARD td.dashcell,
body.DASHBOARD td.dashcell>table>tbody>tr>td {
    padding: 0px;
}

SELECT.navmenus,SELECT.DASHBOARD {background-color: #fff; color: #000; font-family: Verdana, Arial, sans-serif; font-size: 10px; font-style: normal; font-variant: normal; font-weight: normal; text-decoration: none; text-transform: none}

INPUT.DASHBOARD {height:18px;font-family: Helvetica;font-size:10px; color:#000000;border:1px solid rgb(100,100,100);vertical-align:baseline;cursor:pointer;}

TABLE.DASHBOARD_MIDDLE {width:100%;}

TR.DASHBOARD {height:10px; vertical-align:middle;}

FONT.DASHHEAD {font-family: Helvetica, sans-serif; font-size: 11px; font-weight:bold; text-decoration:none; }

FONT.DASHBOARD {font-family: Helvetica, sans-serif; font-size: 11px; font-weight:bold; text-decoration:none; color:#333;}

FONT.DASHDEFAULT {font-family: Helvetica, sans-serif; font-size: 11px; text-decoration:none; color:#333;}

A.dashlink {cursor:pointer; font:bold 9pt Tahoma, Verdana;}

a.DashPanelLeft:link,
a.DashPanelLeft:visited,
a.DashPanelLeft:active 	{
    display:block;
    padding:2px 2px 2px 15px;
    font-family: Helvetica;
    font-size:11px;
    border-bottom:1px solid #fff;
    color:black;
    font-weight:normal;
    text-decoration:none;
    height:12px;
    background:white;
}
a.DashPanelLeft:hover 	{
    color:lightgrey;
    border-bottom: 1px solid #999;
    background:#fff;
}

A.DashHelp:link 		{font-family:  Helvetica, Geneva; font-size:12px; color:#FFFFFF; font-weight:bold; text-decoration:none; vertical-align:middle;}
A.DashHelp:visited 		{font-family:  Helvetica, Geneva; font-size:12px; color:#FFFFFF; font-weight:bold; text-decoration:none; vertical-align:middle;}
A.DashHelp:active 		{font-family:  Helvetica, Geneva; font-size:12px; color:#FFFFFF; font-weight:bold; text-decoration:none; vertical-align:middle;}
A.DashHelp:hover 		{font-family:  Helvetica, Geneva; font-size:12px; color:#DEE7E6; font-weight:bold; text-decoration:none; vertical-align:middle;}

.dashEditColCtrls {
    text-align: center;
}

/** DB Common section ends **/

/** DB Components section **/
td.comp_holder {
    padding:0;
    margin:0;
    background:#FFFFFF;
}
TABLE.COMP
{table-layout:fixed;width:100%;padding:1px;background: #FFFFFF;border-top:0px;}

TR.COMP {vertical-align:middle;color:#000000;	}

TR.COMP_SUB_HEADER {height:10px; vertical-align:middle;color:#FFFFFF;}

TD.COMP_SUB_HEADER {
    color:#000;
    font-weight: bold;
    /*letter-spacing:1.5px;*/
    padding:2px 2px 2px 4px;
}

TD.COMP_SUB_HEADER2 {
    color:#000;
    background:#CCCBC4;
    font-weight: bold;
    border-left:2px solid #CCCBC4;
    border-right:2px solid #CCCBC4;
    border-bottom:1px solid #666;
}

FONT.Dash {font-family: Webdings; font-size: 10px; text-decoration:none;letter-spacing:1.5px; line-height:1;color:#FFFFFF;}

DIV.SCROLLCOMP {
    height:100%;
    padding: 0px;
    border: none;
    background: white;
}
DIV.SCROLLCOMP::-webkit-scrollbar-track-piece {
    background: transparent;
}
/** DB Components section ends **/

/** DB Lister additional css**/

FONT.ClientParent {font-family: Webdings; font-size: 13px; text-decoration:none;letter-spacing:1.5px; line-height:1;}
FONT.ClientShared {font-family: Webdings; font-size: 13px; text-decoration:none;letter-spacing:1.5px; line-height:1;}
FONT.Client {font-family: Webdings; font-size: 13px; text-decoration:none;letter-spacing:1.5px; line-height:1;}
FONT.ClientLine {font-family: Webdings; font-size: 18px; text-decoration:none;letter-spacing:1.5px; line-height:1;}
FONT.ClientSpaceLine {font-family: Webdings; font-size: 0.5px; text-decoration:none;letter-spacing:1.5px;}
FONT.Priority{
    color:#000;
    text-align:center;
    padding:1px;

}
TH.REPL {
    top:0px;
    background:#CCCBC4;
    font-weight: normal;
    padding: 2px 2px;
    text-align: left;

    position:relative;
}

TH.REPH {
    top:0px;
    padding: 2px 2px;
    text-align: left;
    position:relative;
    color: white;
    font: normal bold 12px Helvetica, sans-serif;
}

/*td.messageBoardLister,
td.messageBoardListerHeader{

	font: normal normal 10px Verdana, Geneva, Arial, Helvetica, sans-serif;
	border-bottom: 1px solid #fff;
	border-left: none;
	vertical-align:top;
padding:0px 0px 0px 0px;
}*/
tr.messageBoardLister{
    background: #f5f5f5;
}
tr.messageBoardListerHighlight{
    background:#fff;
}
td.messageBoardListerHeader,
td.messageBoardListerHeader.messageBoardListerHeader_bean {
    background:#f0f0f0;
    color: #070707;
    font-weight: normal;
    border-bottom:1px solid #fff;
    padding:1px 2px 1px 1px;
    vertical-align:middle;
}

table.messageBoardListerTable_bean td.pt_pageablelist TD {
    background: #f9f9f9;
}

table.messageBoardListerTable_bean td.pt_pageablelist TD.messageBoardListerHeader {
    background: #f0f0f0;
}
table.messageBoardListerTable_bean td.pt_pageablelist TD.normalRow {
    background: #f9f9f9;
}

td.messageBoardListerHeader.messageBoardListerHeader_bean FONT.DASHBOARD,
td.messageBoardListerHeader font.DASHBOARD,
TD.pt_pageablelist td.messageBoardListerHeader font.DASHBOARD {
    color: #070707;
    font-weight: 500;
    background: #f0f0f0;
}

table.messageBoardListerTable_bean td.pt_pageablelist TD.normalRow {
    color: #070707;
    background: #f9f9f9;
    font-weight: 100;
    line-height: 18px;
    padding-top: 0;
    padding-bottom: 0;
}


table.messageBoardListerTable_bean,
.DASHBACKGROUND_BEAN, .SCROLLCOMP.DASHBACKGROUND_BEAN {
    background: #F9F9F9;
}

table.messageBoardListerTable_bean TD,
table.messageBoardListerTable_bean TD.messageBoardListerHeader_bean {
    PADDING-RIGHT: 5PX;
}

table.messageBoardListerTable_bean TD.pt_pageablelist {
    PADDING-RIGHT: 0px;
}


table.messageBoardListerTable_bean TD.result1,
table.messageBoardListerTable_bean TD.result2 {
    font-weight: 100;
    line-height: 18px;
}

/*tbody.scrollContent td,
tbody.scrollContent tr.normalRow td {
	background: #E9E8DF;
	font: normal normal 11px Verdana, Geneva, Arial, Helvetica, sans-serif;
	border-bottom: 1px solid #fff;
	border-left: none;
	padding: 4px 2px 4px 2px;
}*/

tr.normalRow {
    background: #E9E8DF;
    font-family: Helvetica, sans-serif;
}

td.normalRow {
    font: normal normal 11px Helvetica, sans-serif;
    border-bottom: 0px solid #fff;
    border-left: none;
    padding: 4px 2px 4px 2px;
}

div.comp_timestamp {
    font: normal 8px Helvetica, sans-serif;
    border-left: none;
}
TD.COMP_SUB_HEADER .comp_timestamp {
    letter-spacing: 1.5;
}

/*tbody.scrollContent td, tbody.scrollContent tr.alternateRow td {
	font: normal normal 11px Verdana, Geneva, Arial, Helvetica, sans-serif;
	border-bottom: 1px solid #fff;
	border-left: none;
	background: #E9E8DF;
	padding: 4px 2px 4px 2px;
}

tbody.scrollContent td, tbody.scrollContent tr.highlightRow td {
	font: normal normal 11px Verdana, Geneva, Arial, Helvetica, sans-serif;
	border-bottom: 1px solid #999;
	border-left: none;
	padding: 4px 2px 4px 2px;
		background:#fff;
}*/




/** DB Lister additional ends here **/

/** Dashboard Reports css **/

TR.RGDB TD, TH.DGB1, TH.DGB, TH.GB, TH.RG {
    font-family: Verdana, Arial, sans-serif;
    font-size: 10px;
    font-style: normal;
    font-variant: normal;
    font-weight: bold;
    text-decoration: none;
    text-transform: none;
}

TR.RDGB TD, TH.DGB2, TH.DGB, TH.GB {
    font-family: Verdana, Arial, sans-serif;
    font-size: 10px;
    font-style: normal;
    font-variant: normal;
    font-weight: bold;
    text-decoration: none;
    text-transform: none;
}

TR.GB TD {
    font-family: Verdana, Arial, sans-serif;
    font-size: 10px;
    font-style: normal;
    font-variant: normal;
    font-weight: bold;
    text-decoration: none;
    text-transform: none;
}

/** Dashboard Reports css ends here**/
/* dashboard */
table.dashboard_container {
    border-bottom:1px solid #CCC;
}


td.dashcell td.COMP_SUB_HEADER td.tdAssistLinks a.baseIcon {
    margin-left: 3px;
    margin-right: 0px;
}
td.dashcell td.COMP_SUB_HEADER td.tdAssistLinks a.baseIcon > .baseIconX {
    display:inline-block;
    padding-left: 2px;
    padding-right: 6px;
}

a.baseIcon.dashboard_expandcollaspe * {
    font-size: 12px;
    color: #949895;
}


font.dashboard_title{
    color:#333;
    font-size:9px;
    text-transform:uppercase;

    font-weight:bold;
}

SELECT.menu{
    color:#333;
}
INPUT.Task{
    color:#000;

    border: 1px solid #000;
    vertical-align: middle;
    font-size:7pt;
    behavior: none;
    background-color: #E5C646;
    height:18px;
    text-decoration:none;
}

td.layoutTabOn {
    background:#E9E8DF;
    color:#000;
    /* url(../../resources/images/ia-app/backgrounds/tab_on_bg_default.gif) top left repeat-x; */
}
td.layoutTabOff a:visited {
    background:#fff;
    color:#666;
    /* url(../../resources/images/ia-app/backgrounds/tab_on_bg_default.gif) top left repeat-x; */
}

/* Lister Reports thingies */
TR.RGDB TH.DGB1, TH.DGB, TH.GB, TH.RG {
    background-color: #F3F2EB;
    color: #333;
}

TR.RDGB TH.DGB2, TH.DGB, TH.GB {
    background-color: #F3F2EB;
    color: #333;
}

TR.GB {
    background-color: #F3F2EB;
    color: #333;
}

/** Dashboard Styles **/

TABLE.DASHHEADER_CONTAINER {
    background: #e1e1e1;
}

table.DASHBOARD_MIDDLE {
    background:#E6E6E6;
    text-align:center;
    width: 100%;
    border-spacing: 5px 0px;
    border-collapse: separate;}

table.dashboard_container td.COMP_SUB_HEADER{
    background:#fff url(../../resources/images/ia-app/backgrounds/tab_off_bg_default.gif) bottom left repeat-x;
    color: #070707;
    font-weight: 100;
    font-family: helvetica;
    font-size: 16px;
    vertical-align: top;
}
table.dashboard_container_bottom  {
    border: none;
}

div.dashboard_container_bottom_div {
    border: 1px solid transparent;
    margin-top:0px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 4px;
    overflow: hidden;
}

table.dashboard_container_bottom.dashboard_theme {
    background:transparent;border: none;
}
table.dashboard_container.dashboard_theme {
    background:transparent;border-bottom: 1px solid #E1E1E1;
}

/*
table.DASHBOARD_MIDDLE.dashboard_theme_T,  {
    background: #E6E6E6;
}
*/

table.DASHBOARD_MIDDLE.dashboard_theme_NB {
    background: #dae2eb;
}
table.DASHBOARD_MIDDLE.dashboard_theme_NG {
    background: #e4f2bc;
}

table.DASHBOARD_MIDDLE.dashboard_theme_NP {
    background: #c7c7e5;
}

table.DASHBOARD_MIDDLE.dashboard_theme_ND {
    background: #E6E6E6;
}

table.DASHBOARD_MIDDLE.dashboard_theme_NO {
    background: #f4eccb;
}


table.dashboard_container_bottom.dashboard_corner { /*is a corner!*/
    -moz-border-radius: 9px/*{cornerRadius}*/;
    -webkit-border-radius: 9px/*{cornerRadius}*/;
    -khtml-border-radius: 9px/*{cornerRadius}*/;
    border-radius: 9px/*{cornerRadius}*/;

    border-width: 2px;
    overflow: hidden; /*so corners clip the content! */
}


table.dashboard_container_bottom>TBODY>TR>TD {
    padding: 0px;
}

table.dashboard_container_bottom TR:first-child TD.COMP_SUB_HEADER {
    border-bottom: 1px solid #E1E1E1;
    padding-top: 3px;
    padding-bottom: 5px;

    background: #F9F9F9;
}
table.dashboard_container_bottom TR:first-child TD.COMP_SUB_HEADER TD.tdAssistLinks {
    padding: 0px;
    padding-top: 2px;
}
table.dashboard_container_bottom TR:first-child TD.COMP_SUB_HEADER TD.tdAssistLinks.EmbeddedContentBean {
    padding-top: 4px;
}
table.dashboard_container_bottom TD IMG {margin-right: 3px;}


table.dashboard_container_CB td.COMP_SUB_HEADER{
    padding:0px 0 3px 2px;
}

table.dashboard_container_CG td.COMP_SUB_HEADER{
    padding:0px 0 3px 2px;
}

table.dashboard_container_CP td.COMP_SUB_HEADER{
    padding:0px 0 3px 2px;
}

table.dashboard_container_CO td.COMP_SUB_HEADER{
    padding:0px 0 3px 2px;
}

table.dashboard_container_CD td.COMP_SUB_HEADER{
    padding:0px 0 3px 2px;
}

/*
COPY PARTS FROM:

	echo "table.dashboard_container_bottom  {border: 1px solid #CCCCCC;margin-top:5px;}";
    echo "table.dashboard_container_X {background:#FFFFFF;border-bottom:1px solid #".$navbgcolor.";border-top:0px solid #".$navbgcolor.";}";
	echo "table.dashboard_container_X_bottom {border:1px solid #".$navbgcolor.";margin-top:5px;}";
	echo "table.dashboard_container_X_bottom TD {padding: 0px;}";
    echo "table.dashboard_container_X TD {background:transparent;padding-top: 2px;padding-bottom: 5px;}";
	echo "table.dashboard_container_X_bottom TD IMG {margin-right: 3px;}";
	echo "table.DASHBOARD_MIDDLE_X {background:#FFF}";

*/


/** Dashboard for touch device styles **/

.ia-touch td.dashcell td.COMP_SUB_HEADER {
    height: 50px;
}

.ia-touch td.dashcell td.COMP_SUB_HEADER > font.d_c_title {
    font-family: Calibri, Arial, Helvetica, Geneva;
    font-size: 16px;
}

.ia-touch td.dashcell td.COMP_SUB_HEADER > div.comp_timestamp {
    font-family: Calibri, Arial, Helvetica, Geneva;
    font-size: 12px;
}

.ia-touch td.dashcell td.COMP_SUB_HEADER td.tdAssistLinks a > img {
    height: 25px;
    width: 25px;
}

.ia-touch td.dashcell td.COMP_SUB_HEADER td.tdAssistLinks a.baseIcon > .baseIconX {
    font-size: 20px;
}
.ia-touch td.dashcell td.COMP_SUB_HEADER td.tdAssistLinks a > img[data-type=change_graph],
.ia-touch td.dashcell td.COMP_SUB_HEADER > a.baseIcon > .baseIconArrowDown {
    display: none;
}

/** Dashboard Styles **/

/** Dashboard specific css ends**/

/** Lister skinning **/
/*TABLE.LISTHEADER {WIDTH:100%;PADDING:4px 0px 4px 0px;border-style:Solid;border-width:0px;border-bottom-width:0px;border-color:333333;height:26px;valign:bottom;}
TABLE.LISTFOOTER {WIDTH:100%;PADDING:4px 0px 4px 0px;border-style:Solid;border-width:0px;border-top-width:0px;border-color:333333;height:26px;valign:bottom;}
TABLE.LISTCONTENT {WIDTH:100%;border-style:Solid;border-width:0px;border-top-width:2px;border-bottom-width:2px;border-color:333333;}
FONT.LISTTITLE {font-size:14px;font-weight:bold}*/
/** Lister skinning ends here**/

/* HOMEPAGE */

body.HOMEPAGE {
    background-color: #ffffff;
}

body.HOMEPAGE table.dashboard_container_bottom {
    border: 0px none;
}

body.HOMEPAGE table.dashboard_container {
    border-bottom:1px solid #dfdfe0;
}

body.HOMEPAGE font.d_c_title {
    color: #000;
    font-family: Helvetica, Geneva;
    font-size: 14px;
    font-weight: bold;
    text-transform: none;
    letter-spacing: normal;
}

body.HOMEPAGE table.dashboard_container td.COMP_SUB_HEADER{
    background:#ffffff url(../../resources/images/ia-app/backgrounds/tab_off_bg_default.gif) bottom left repeat-x;
    color:#000;
}

body.HOMEPAGE table.dashboard_container td.COMP_SUB_HEADER .baseIcon,
body.HOMEPAGE table.dashboard_container td.COMP_SUB_HEADER a.baseIcon {
    color:#000;
}

body.HOMEPAGE a.DashPanelLeft:link,
body.HOMEPAGE a.DashPanelLeft:visited,
body.HOMEPAGE a.DashPanelLeft:active  {border-bottom:1px solid #ffffff;color:#000;background:#ffffff;}
body.HOMEPAGE a.DashPanelLeft:hover   {color:#000;border-bottom: 1px solid #ffffff;background:#f6e0bc;}

body.HOMEPAGE .hcalendar .yui-calcontainer {
    background-color: #fff;
}

body.HOMEPAGE  TD.COMP_SUB_HEADER2 {
    color:#000;
    background:#ffffff;
    border-bottom:1px solid #dfdfe0;
    font-weight: bold;
}

body.HOMEPAGE TABLE.COMP {
    background: #ffffff;
}

body.HOMEPAGE .nfeatures td.comp_holder {
    background: #ffffff;
}

body.HOMEPAGE tr.messageBoardLister{
    background: #ffffff;
}
body.HOMEPAGE tr.messageBoardListerHighlight{
    background:#f6e0bc;
}

body.HOMEPAGE td.messageBoardListerHeader{
    background:#ffffff;
    border-bottom:1px solid #dfdfe0;
}

body.HOMEPAGE td.comp_holder {
    background: #ffffff;
}

body.DASHBOARD A.Task {
    border:             1px solid transparent;
    cursor:             pointer;
    padding:            2px 5px 2px 5px;
    margin:             2px 2px;
    display:            inline-block;
    font-size:          12px;
}
body.DASHBOARD A.Task:hover {
    border:             1px solid #4180c0;
    padding:            2px 5px 2px 5px;
    margin:             2px 2px;
    display:            inline-block;
    font-size:          12px;
}


TABLE.dashboardstaticlastrow {
    width: 100%;
    height:200px;
    display: none;
}

DIV.kpistrip {
    white-space: normal;
    line-height: 22px;
    background: #F9F9F9;
    margin-bottom: -4px;
    overflow-y: hidden;
}

DIV.kpibean {
    display: inline-block;

    width:  286px;
    height: 116px;

    overflow: hidden;

    margin-top: 2px;
    margin-left: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    text-align: center;
    -webkit-border-radius: 1%;
    -moz-border-radius: 1%;
    border-radius: 1%;
}

DIV.kpibean_empty {
    text-align: center;
    font-size: 18px;
    padding: 20px;
}

IFRAME.kpibean_frame {
    width:  286px;
    height: 116px;

    margin:     0px;
    border:     none;
    overflow:   hidden;
}


DIV.kpititle {
    display: inline-block;
    font-size: 16px;
    font-family: Helvetica;
    font-weight: bold;
}

DIV.kpipercentagegrphic {
    font-size: 32px;
    font-family: Helvetica;
    display: inline-block;
    white-space: nowrap;
}


DIV.kpipercentagegrphicunchanged {
    font-size: 18px;
    white-space: normal;
}
DIV.kpidirectiongraphic {
    font-size: 36px;
    padding-top: 10px;
}
DIV.kpidirectiongraphicup {
    color: green;
}
DIV.kpidirectiongraphicdown {
    color: red;
}

.invertColor {
    -webkit-filter: invert(100%);
    -moz-filter: invert(100%);
    -ms-filter: invert(100%);
    filter: invert(100%);
}

.dashboard_button_fa {
    /*color: white;*/
    cursor: pointer;
    margin-right: 5px;
    font-size: 2.5em;
    opacity: .7;
}

.dashboard_button_fa_toolarge{
    font-size: 2.2em;
}

.dashboard_button_fa:hover {
    /*color: #a1a1a1;*/
    opacity: 1;
}

.dashboard_component_button_fa {
    color: #a1a1a1;
    cursor: pointer;
    margin-right: 5px;
    font-size: 1.7em;
}
.dashboard_component_button_fa:hover {
    color: #454545;
    opacity: .7;
}
.dashboard_hideframe .dashboard_component_button_fa {
    font-size: 1.25em;
    opacity: .7;
}

.dashboard_hideframe .dashboard_component_button_fa:hover {
    opacity: 1;
}


.dashboard_hideframe,

.dashboard_hideframe,
.dashboard_hideframe .dashboard_container,
.dashboard_hideframe .dashboard_container TR:first-child TD.COMP_SUB_HEADER
{
background: transparent;
border-bottom: none;
}
.dashboard_hideframe .dashboard_container TR:first-child TD.COMP_SUB_HEADER {
display: none;
margin: 0px;
padding: 0px;
}
.dashboard_hideframe .dashboard_container TR:first-child TD.COMP_SUB_HEADER:last-child {
    display: block;
}

.dashboardexcludehandle:hover {
    cursor: default;
}

DIV .dragdrophandle {
    position: absolute;
    top: 4px;
    left: 0px;
    font-size: 14px;
    color: #a1a1a1;
    width: 286px; /*same as kpibean_frame*/
    cursor: move;
}
DIV .dragdrophandle:hover {
    color: #696969;
}

DIV.wizardglass {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}
DIV.wizardglassbackground {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 26;
    background: black; /*IE 8 neede this here?*/
    opacity:0.20;
    filter:alpha(opacity=20);
}
DIV.wizardglassbackgroundimg {
    z-index: 27;
    position: absolute;
    margin-left: 45%;
    margin-right: 45%;
    text-align: center;
    padding-top: 200px
}

div.bodydiv_rounded {
    border: 1px solid transparent;
    margin-left:2px;
    margin-right:2px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 4px;
    overflow: hidden;
}

div .SCROLLCOMP_kpistrip {
    margin-bottom: -3px;
    overflow-y: hidden;
}

.tdDashboardFilters UL.assistedField {
  padding-left: 0px;

}
.tdDashboardFilters INPUT.combo-input {
    width: 175px;
    background: transparent;
}

.tdDashboardFilters INPUT.combo-input,
.tdDashboardFilters .combo-box.combo-box-border {
    background: white;

    padding-left: 2px;
    padding-right: 2px;

    color: #5a5a5a;

    border: 1px solid #c1c1c1;

    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 4px;
    text-overflow: ellipsis ;
}

.tdDashboardFilters .buttons.dropdownCB {
    opacity: .8;
}

.tdDashboardFilters .filterbutton {
    border: 1px solid #c1c1c1;
    background: transparent;
    opacity: .7;
    padding: 4px;
    padding-bottom: 2px;
    padding-top: 2px;
    padding-right: 6px;

    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    -khtml-border-radius: 5px;
    border-radius: 5px;
    cursor: pointer;

    visibility: hidden;

}
.tdDashboardFilters .filterbutton:hover {
    opacity: .9;
}
.tdDashboardFilters .filterbutton I {
    margin-right: 2px;
    margin-left: 2px;
    opacity: 1;
}
.tdDashboardFilters .filterbutton I:hover {
    opacity: 1;
}

.tdDashboardFilters .filterbutton2 {
    border: 1px solid transparent;
    background: transparent;
    padding: 4px;
    padding-bottom: 2px;
    padding-top: 2px;

    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    -khtml-border-radius: 5px;
    border-radius: 5px;

    opacity: .5;
}
.tdDashboardFilters .filterbutton2:hover {
    opacity: .5;
}
.tdDashboardFilters .filterbutton2 I {
    cursor: default;
    margin-right: 2px;
    margin-left: 2px;
    opacity: .5;
}
.tdDashboardFilters .filterbutton2 #applybuttontext2 {
    color: transparent;
}

.dashboard_component_button_fa.translucentFilterIcon {
    opacity: .4;
    cursor: default;
}
.dashboard_component_button_fa.translucentFilterIcon:hover {
    opacity: .2; /*?? not sure why .4 was not working??*/
    cursor: default;
}

.tdDashboardFilters .as_of_date_header_table INPUT[type="text"] {
    border: 1px solid #c1c1c1;
    padding: 4px;
    padding-bottom: 2px;
    padding-top: 2px;

    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    -khtml-border-radius: 5px;
    border-radius: 5px;

    color: #5a5a5a;
    height: 18px;
    font-size: 11px;
}

.tdDashboardFilters a.Pick img {
    margin-top: 4px
}











/* END HOME PAGE */

