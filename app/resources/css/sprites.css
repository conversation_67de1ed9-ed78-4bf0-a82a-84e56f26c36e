
.menuicons,
.buttons {
    cursor: pointer;
	background-image: url(../images/ia-app/intacctSprite.png);
	display: inline-block;
	vertical-align: middle;
}

.view {
	width: 18px;
	height: 19px;
	background-position: -409px -9px;
}
.view:hover {	background-position: -409px -28px; }
.view:active {	background-position: -409px -47px; }

.edit {
	width: 18px;
	height: 19px;
	background-position: -386px -9px;
}
.edit:hover {	background-position: -386px -28px; }
.edit:active {	background-position: -386px -47px; }

.view_attachment {
	width: 19px;
	height: 19px;
	background-position: -584px -9px;
}
.view_attachment:hover {	background-position: -584px -28px; }
.view_attachment:active {	background-position: -584px -47px; }

.refresh {
	width: 18px;
	height: 19px;
	background-position: -786px -9px;
}
.refresh:hover {	background-position: -786px -28px; }
.refresh:active {	background-position: -786px -47px; }

.arrowRight {
	width: 18px;
	height: 19px;
	background-position: -608px -7px;
}
.arrowRight:hover {	background-position: -608px -26px; }
.arrowRight:active {	background-position: -608px -45px; }

.arrowDown {
	width: 18px;
	height: 19px;
	background-position: -632px -7px;
}
.arrowDown:hover {	background-position: -632px -26px; }
.arrowDown:active {	background-position: -632px -45px; }

.expand {
	width: 18px;
	height: 19px;
	background-position: -563px -9px;
}
.expand:hover {	background-position: -563px -28px; }
.expand:active {	background-position: -563px -47px; }

.calendar {
	width: 18px;
	height: 19px;
	background-position: -497px -9px;
}
.calendar:hover {	background-position: -497px -28px; }
.calendar:active {	background-position: -497px -47px; }

.calculator {
	width: 18px;
	height: 19px;
	background-position: -541px -9px;
}
.calculator:hover {	background-position: -541px -28px; }
.calculator:active {	background-position: -541px -47px; }

.moveright {
	width: 18px;
	height: 19px;
	background-position: -475px -9px;
}
.moveright:hover {	background-position: -475px -28px; }
.moveright:active {	background-position: -475px -47px; }

.moveleft {
	width: 18px;
	height: 19px;
	background-position: -453px -9px;
}
.moveleft:hover {	background-position: -453px -28px; }
.moveleft:active {	background-position: -453px -47px; }

.dropdownCB {
	width: 14px;
	height: 14px;
	background-position: -673px -9px;
}
.dropdownCB:hover {	background-position: -673px -28px; }
.dropdownCB:active {	background-position: -673px -47px; }

.dragdrop {
	width: 7px;
	height: 11px;
	background-position: -723px -9px;
}
.dragdrop:hover {	background-position: -723px -28px; }
.dragdrop:active {	background-position: -723px -47px; }

.plus {
	width: 12px;
	height: 12px;
	background-position: -691px -9px;
}
.plus:hover {	background-position: -691px -28px; }
.plus:active {	background-position: -691px -47px; }

.minus {
	width: 12px;
	height: 12px;
	background-position: -771px -9px;
}
.main:hover {	background-position: -771px -28px; }
.main:active {	background-position: -771px -47px; }

.add {
	width: 18px;
	height: 19px;
	background-position: -753px -9px;
}
.add:hover {	background-position: -753px -28px; }
.add:active {	background-position: -753px -47px; }

.trash {
	width: 12px;
	height: 15px;
	background-position: -707px -9px;
}
.trash:hover {	background-position: -707px -28px; }
.trash:active {	background-position: -707px -47px; }

.checkmark {
	width: 13px;
	height: 13px;
	background-position: -360px -123px;
}

@media print {
    .checkmark:before {
        content: "T";
    }
}
.checkmark_empty {
    width: 13px;
    height: 13px;
    background-position: -375px -123px;
}

.menulist {
    width: 11px;
    height: 14px;
    background-position: -387px -85px;
}
.menulist:hover {    background-position: -387px -101px; }

.menuadd {
    width: 14px;
    height: 14px;
    background-position: -409px -85px;
}
.menuadd:hover {    background-position: -409px -101px; }

.menuleftarrow {
    width: 18px;
    height: 14px;
    background-position: -431px -85px;
}
.menuleftarrow:hover {  background-position: -431px -101px; }

.menurightarrow {
    width: 18px;
    height: 14px;
    background-position: -453px -85px;
}
.menurightarrow:hover { background-position: -453px -101px; }

.blank:hover,
.blank {
    background-position: 99px 99px;
}

a.button, a.button_disabled {
	background-color:transparent;
	background-image:url("../images/ia-app/intacctSprite.png");
	background-position:-8px -6px;
	display:inline-block;
	height:28px;
	line-height:26px;
	text-decoration:none;
	margin-right: 8px;
}

a.button_disabled {
    background-position:-8px -90px;
}

a.button:active, a.button:focus {	background-position: -8px -62px; }
a.button:hover { background-position: -8px -33px; }

a.button > span, a.button_disabled > span {
	padding-left: 15px;
	padding-right: 15px;
	margin-right: -10px;
	white-space: nowrap;
}

a.button > span.rb, a.button_disabled > span.rb {
	background-image: url("../images/ia-app/intacctSprite.png");
	background-color: transparent;
	display: inline-block;
	background-position: -367px -6px;
	width: 10px;
	height: 28px;
	padding: 0;
}

a.button_disabled > span.rb {
	background-position: -367px -90px;
}

a.button:active > span.rb, a.button:focus > span.rb { background-position: -367px -62px; }
a.button:hover > span.rb { background-position: -367px -33px; }

a.button_disabled span {
     color: #A0A0A0;
}
/******************************************************************************/
label.button, label.button_disabled {
    background-color:transparent;
    background-image:url("../images/ia-app/intacctSprite.png");
    background-position:-8px -6px;
    position: relative;
    display: inline-block;
    height:28px;
    line-height:26px;
    text-decoration:none;
    margin-right: 8px;
}

label.button_disabled {
    background-position:-8px -90px;
}

label.button:active, label.button:focus {	background-position: -8px -62px; }
label.button:hover { background-position: -8px -33px; }

label.button > span.split_label, label.button_disabled > span.split_label,
label.button > a > span.split_label, label.button_disabled > a > span.split_label {
    padding-left: 15px;
    padding-right: 15px;
    margin-right: -10px;
    white-space: nowrap;
    display: inline-block;
}

label.splitcontainer {
    position: relative;
}
label.button > span.split_divider {
    color: #7E8994 !important;
    padding-left: 3px;
    padding-right: 3px;
}
label.button > span.rb, label.button_disabled > span.rb {
    background-image: url("../images/ia-app/intacctSprite.png");
    background-color: transparent;
    display: inline-block;
    background-position: -372px -6px;
    width: 10px;
    height: 28px;
    padding: 0;
    position: absolute;
}

label.button_disabled > span.rb {
    background-position: -372px -90px;
}

label.button:active > span.rb, label.button:focus > span.rb { background-position: -372px -62px; }
label.button:hover > span.rb { background-position: -372px -33px; }

label.button_disabled span {
    color: #A0A0A0;
}
label.button > a.splitbutton {
    background: transparent;
    top: 0;
    left: 0;
    width: 75%;
    height: 28px;
}
label.button > a.splitmenu {
    background: transparent;
    display: inline-block;
    height: 28px;
}

/***************************************************************************************/
div.line_details_switch, div.line_details_switch > div {
	background-color:transparent;
	background-image:url("../images/ia-app/intacctSprite.png");
	height:20px;
	line-height:15px;
	text-decoration:none;
	/* margin-top: -15px; */
	white-space: nowrap;
	vertical-align: middle;
	float: left;	
}

div.line_details_switch {
	background-position:-384px -179px;
	padding-left: 20px;
	margin-right: 20px;
	color : #e6e6e6;
}

div.line_details_switch .lineDetailsHelp {
    color : #F6E0BC;
    font-size: 10px;
    text-decoration: none;
}

div.line_details_switch:hover { background-position: -384px -205px; color : black; }
div.line_details_switch:hover .lineDetailsHelp {color : #666666;}

div.line_details_switch > .rb {
	background-position: -549px -179px;
	width: 20px;
	padding: 0;
	position: absolute;
	top: 0;
	right: -20px;
}
div.line_details_switch:hover > .rb { background-position: -549px -205px; }

.schedule {
    width: 18px;
    height: 19px;
    background-position: -808px -9px;
}
.schedule:hover {	background-position: -808px -28px; }
