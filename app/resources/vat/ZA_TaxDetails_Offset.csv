DONOTIMPORT,DE<PERSON><PERSON>ID,DESCRIPTION,TA<PERSON>YP<PERSON>,<PERSON><PERSON>UE,MINTAX<PERSON>LE,MAXT<PERSON><PERSON><PERSON><PERSON>,INCLUDE,MINTAX,MAXTAX,GLACCOUNT,TAXAUTHORITY,STATUS,TAXUID,REVERSECHARGE,TAXRATE,TAXSOLUTIONID,USEEXPENSEACCT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
"#
Any row which starts with a # will be ignored during import","Field Name: DETAILID
UI Field Name: Detail ID
Type: Character
Length: 100
Default Value: None
Valid Values: Alphanumeric and underscore
Dependencies: None
Required: Yes
Editable: No","Field Name: DESCRIPTION
UI Field Name: Description
Type: Character
Length: 400
Default Value: None
Valid Values: Alphanumeric and underscore
Dependencies: None
Required: No
Editable: Yes","Field Name: Tax type
UI Field Name: Hidden in UI
Type: Character
Length: 8
Default Value: Sale
Valid Values: Sale, Purchase
Dependencies: None
Required: Yes
Editable: No
Notes: Identifies which application uses the tax detail. If your company isn't subscribed to the Taxes application, Accounts Receivable and Order Entry share the same set of tax details (Sale), but Purchasing does not (Purchase).
If your company is subscribed to the Taxes application, Accounts Receivable and Order Entry share the same set of tax schedules (Sale), and Accounts Payable and Purchasing share the same set of tax schedules (Purchase).","Field Name: Percent
UI Field Name: Percent
Type: Number
Length: 12
Default Value: None
Valid Values:  Digits 0-9
Dependencies: None
Required: No
Editable: Yes
","Field Name: MINTAXABLE
UI Field Name: Minimum taxable amount
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes
","Field Name: MAXTAXABLE
UI Field Name: Maximum taxable amount
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes","Field Name: INCLUDE
UI Field Name: Inclue in taxable amount
Type: Character
Length: N/A
Default Value: None
Valid Values: Full Amount  or Amount Within Range
Dependencies: None
Required: Yes
Editable: Yes
","Field Name: MINTAX
UI Field Name: Minimum tax
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes
","Field Name: MAXTAX
UI Field Name: Maximum tax
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes
","Field Name: GLACCOUNT
UI Field Name: GL Account
Type: Character
Length: 24
Default Value: None
Valid Values: Must conform to primary/sub account specification in General Ledger > Setup > Accounts > Accounts 
Dependencies: Refers to a valid account number
Required: Yes
Editable: Yes                   Notes: The value in ACCOUNTLABEL overrides the value in GLACCOUNT.                            
","Field Name: TAXAUTHORITY
UI Field Name: Tax authority
Type: Character
Length: 100
Default Value: None
Valid Values: Alphanumeric and underscore
Dependencies: None
Required: No
Editable: No
","Field Name: STATUS
UI Field Name: Status
Type: Character
Default Value: active
Valid Values: active, inactive
Dependencies: None
Required: No
Editable: Yes
Notes: active = visible in lists; inactive = not visible in lists, but available for later use.","Field Name: TAXUID
UI Field Name: Tax UID
Type: Character
Length: 100
Default Value: none
Valid Values: Alphanumeric and underscore
Dependencies: Must refer to a valid tax UID for the tax solution
Required: Yes, only for the standard out-of-box tax solutions (Australia, Canada, South Africa, or United Kingdom)
Editable: No
Notes: Has no effect on any other tax solutions besides the out-of-box VAT tax solutions","Field Name: REVERSECHARGE
UI Field Name: Is reverse charge
Type: Character
Length: 1
Default Value: F
Valid Values: T = true; F = false
Dependencies: None
Required: No
Editable: Yes","Field Name: TAXRATE
UI Field Name:  Tax rate
Type: Character
Length: 8
Default Value: Standard
Valid Values:  Standard, Reduced, Exempt, Zero
Dependencies: None
Required: No
Editable: No
Notes:   Identifies the category this tax detail falls under: Exempt, Standard, Reduced, Zero. Use for United Kingdom VAT.","Field Name: TAXSOLUTIONID
UI Field Name: Tax solution
Type: Character
Length: 100
Default Value: None
Valid Values: Alphanumeric and underscore
Dependencies: Must refer to a valid tax solution
Required:  Yes, if your organization is configured for multiple tax jurisdictions.
Editable: No","Field Name: USEEXPENSEACCT
UI Field Name: Use expense account
Type: Character
Length: 1
Default Value: F
Valid Values: T = true; F = false
Dependencies: None
Required: No
Editable: Yes
Notes: Only support purchase tax type for a VAT tax solution.",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Standard Rate Output,Standard Rate Output,Sale,15,,,,,,,,,ZA.SROutputOffset_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Standard Rate (Capital Goods) Output,Standard Rate (Capital Goods) Output,Sale,15,,,,,,,,,ZA.SRCOutput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Zero Rate (Excluding Goods Exported),Zero Rate (Excluding Goods Exported),Sale,0,,,,,,,,,ZA.ZOutput_ZAR.ZeroZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Zero Rate (Only Goods Exported),Zero Rate (Only Goods Exported),Sale,0,,,,,,,,,ZA.ZExOutput_ZAR.ZeroZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Exempt and Non-Supplies,Exempt and Non-Supplies,Sale,0,,,,,,,,,ZA.ExNsOutput_ZAR.ExemptZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Change in Use Output,Change in Use Output,Sale,15,,,,,,,,,ZA.CIUOutput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Export of Second Hand Goods,Export of Second Hand Goods,Sale,15,,,,,,,,,ZA.ESHGOutput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Bad Debts,Bad Debts,Sale,15,,,,,,,,,ZA.BDInput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Standard Rate Input,Standard Rate Input,Purchase,15,,,,,,,,,ZA.OGInputOffset_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Standard Rate (Capital Goods) Input,Standard Rate (Capital Goods) Input,Purchase,15,,,,,,,,,ZA.CGInput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Change in Use Input,Change in Use Input,Purchase,15,,,,,,,,,ZA.CIUInput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Capital Goods Imported,Capital Goods Imported,Purchase,0,,,,,,,,,ZA.CGImInput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Other Goods Imported (Not Capital Goods),Other Goods Imported (Not Capital Goods),Purchase,0,,,,,,,,,ZA.OGImInput_ZAR.Standard2018ZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Other Input Tax Adjustments,Other Input Tax Adjustments,Sale,100,,,,,,,,,ZA.OTAInput_ZAR.AdjustmentZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,Other Output Tax Adjustments,Other Output Tax Adjustments,Purchase,100,,,,,,,,,ZA.OTAOutput_ZAR.AdjustmentZAR.VAT,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,No Input VAT,No Input VAT,Purchase,0,,,,,,,,,ZA.NOVInput_ZAR.ZeroZAR,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,No Output VAT,No Output VAT,Sale,0,,,,,,,,,ZA.NOVOutput_ZAR.ZeroZAR,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
