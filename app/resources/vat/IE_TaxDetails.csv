"DONOTIMPORT","<PERSON><PERSON><PERSON><PERSON>","DESCRIPTI<PERSON>","TAXTYP<PERSON>","VALUE","MINTAXAB<PERSON>","MAXTAXABLE","INCLUDE","MINTAX","MAXTAX","G<PERSON>CCOUNT","<PERSON><PERSON>UN<PERSON><PERSON><PERSON>","TAXAUTHORITY","STATUS","TAXUID","REVERSECHAR<PERSON>","TAXRA<PERSON>","USEEXPENSEACCT","TAXFILING"
"#
Any row which starts with a # will be ignored during import","Field Name: DETAILID
UI Field Name: Detail ID
Type: Character
Length: 100
Default Value: None
Valid Values: Alphanumeric and underscore
Dependencies: None
Required: Yes
Editable: No","Field Name: DESCRIPTION
UI Field Name: Description
Type: Character
Length: 400
Default Value: None
Valid Values: Alphanumeric and underscore
Dependencies: None
Required: No
Editable: Yes","Field Name: Tax type
UI Field Name: Hidden in UI
Type: Character
Length: 8
Default Value: Sale
Valid Values: Sale, Purchase
Dependencies: None
Required: Yes
Editable: No
Notes: Identifies which application uses the tax detail. If your company isn't subscribed to the Taxes application, Accounts Receivable and Order Entry share the same set of tax details (Sale), but Purchasing does not (Purchase).
If your company is subscribed to the Taxes application, Accounts Receivable and Order Entry share the same set of tax schedules (Sale), and Accounts Payable and Purchasing share the same set of tax schedules (Purchase).","Field Name: Percent
UI Field Name: Percent
Type: Number
Length: 12
Default Value: None
Valid Values:  Digits 0-9
Dependencies: None
Required: No
Editable: Yes","Field Name: MINTAXABLE
UI Field Name: Minimum taxable amount
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes","Field Name: MAXTAXABLE
UI Field Name: Maximum taxable amount
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes","Field Name: INCLUDE
UI Field Name: Inclue in taxable amount
Type: Character
Length: N/A
Default Value: None
Valid Values: Full Amount  or Amount Within Range
Dependencies: None
Required: Yes
Editable: Yes
","Field Name: MINTAX
UI Field Name: Minimum tax
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes","Field Name: MAXTAX
UI Field Name: Maximum tax
Type: Number
Length: 8
Default Value: None
Valid Values: Digits 0-9
Dependencies: None
Required: No
Editable: Yes","""Field Name: GLACCOUNT
UI Field Name: GL Account
Type: Character
Length: 24
Default Value: None
Valid Values: Must conform to primary/sub account specification in General Ledger > Setup > Accounts > Accounts
Dependencies: Refers to a valid account number
Required: Yes
Editable: Yes
Notes: If TAXTYPE is Sale and Advanced Tax is enabled in Accounts Receivable, a value is not required. The value in ACCOUNTLABEL overrides the value in GLACCOUNT.""","""Field Name: ACCOUNTLABEL
UI Field Name: Account label
Type: Character
Length: 80
Default Value: None
Valid Values: Any
Dependencies: None
Required: Yes
Editable: Yes
Notes:  If TAXTYPE is Sale and Advanced Tax is enabled in Accounts Receivable, a value is required. The value entered in this field overrides the value in GLACCOUNT.""","Field Name: TAXAUTHORITY
UI Field Name: Tax authority
Type: Character
Length: 100
Default Value: None
Valid Values: Alphanumeric and underscore
Dependencies: None
Required: No
Editable: No","Field Name: STATUS
UI Field Name: Status
Type: Character
Default Value: active
Valid Values: active, inactive
Dependencies: None
Required: No
Editable: Yes
Notes: active = visible in lists; inactive = not visible in lists, but available for later use.","Field Name: TAXUID
UI Field Name: Tax UID
Type: Character
Length: 100
Default Value: none
Valid Values: Alphanumeric and underscore
Dependencies: Must refer to a valid tax UID for the tax solution
Required: Yes, only for the standard out-of-box tax solutions (Australia, Canada, South Africa, or United Kingdom)
Editable: No
Notes: Has no effect on any other tax solutions besides the out-of-box VAT tax solutions","Field Name: REVERSECHARGE
UI Field Name: Is reverse charge
Type: Character
Length: 1
Default Value: F
Valid Values: T = true; F = false
Dependencies: None
Required: No
Editable: Yes","Field Name: TAXRATE
UI Field Name:  Tax rate
Type: Character
Length: 8
Default Value: Standard
Valid Values:  Standard, Reduced, Exempt, Zero
Dependencies: None
Required: No
Editable: No
Notes:   Identifies the category this tax detail falls under: Exempt, Standard, Reduced, Zero. Use for United Kingdom VAT.","Field Name: USEEXPENSEACCT
UI Field Name: Use expense account
Type: Character
Length: 1
Default Value: F
Valid Values: T = true; F = false
Dependencies: None
Required: No
Editable: Yes
Notes: Only support purchase tax type for a VAT tax solution.","Field Name: TAXFILING
UI Field Name: Non-reporting
Type: Character
Length: 13
Default Value: reporting
Valid Values: reporting; non-reporting 
Dependencies: None
Required: No
Editable: Yes"
"","Zero rate for resale - IE","Zero rated purchase of goods/services for resale","Purchase","0","","","","","","","","","","IE110001","false","Zero","","Reporting"
"","Zero rate for non-resale - IE","Zero rated purchase of goods/services for non-resale","Purchase","0","","","","","","","","","","IE110002","false","Zero","","Reporting"
"","Standard rate non-resale - IE","Standard rated purchase of goods/services for non-resale","Purchase","23","","","","","","","","","","IE110003","false","Standard","","Reporting"
"","Exempt goods for non-resale - IE","Exempt purchase of goods for non-resale","Purchase","0","","","","","","","","","","IE110004","false","Exempt","","Reporting"
"","Reduced rate non-resale - IE","Reduced rate purchase of goods/services for non-resale","Purchase","13.5","","","","","","","","","","IE110006","false","Reduced","","Reporting"
"","EC exempt import of goods for non-resale - IE","Exempt import of goods from suppliers in EC","Purchase","0","","","","","","","","","","IE110007","false","Exempt","","Reporting"
"","Exempt goods non-EC - IE","Exempt import of goods from suppliers outside EC","Purchase","0","","","","","","","","","","IE110009","false","Exempt","","Reporting"
"","EC zero rate goods for non-resale - IE","Zero rated purchases of goods from suppliers in EC","Purchase","0","","","","","","","","","","IE110010","false","Zero","","Reporting"
"","EC standard rate goods non-resale input - IE","Standard rated purchases of goods from suppliers in EC Input","Purchase","23","","","","","","","","","","IE115001","false","Standard","","Reporting"
"","EC standard rate goods non-resale output - IE","Standard rated purchases of goods from suppliers in EC Output","Purchase","-23","","","","","","","","","","IE115008","true","Standard","","Reporting"
"","Zero rate goods from outside EC for resale - IE","Zero rated purchases of goods from suppliers outside EC","Purchase","0","","","","","","","","","","IE115015","false","Zero","","Reporting"
"","Standard rate for resale - IE","Standard rated purchase of goods/services for resale","Purchase","23","","","","","","","","","","IE115022","false","Standard","","Reporting"
"","Reverse charge including Relevant Contracts Tax input - IE","Reverse charge including Relevant Contracts Tax (RCT), domestic reverse charge","Purchase","23","","","","","","","","","","IE120001","false","Standard","","Reporting"
"","Reverse charge including Relevant Contracts Tax output - IE","Reverse charge including Relevant Contracts Tax (RCT), domestic reverse charge","Purchase","-23","","","","","","","","","","IE120501","true","Standard","","Reporting"
"","Zero rate of goods from outside EC non-resale - IE","Zero rated purchases of goods from suppliers outside EC","Purchase","0","","","","","","","","","","IE125001","false","Zero","","Reporting"
"","EC zero rate services non-resale - IE","Zero rated purchases of services from suppliers in EC","Purchase","0","","","","","","","","","","IE125501","false","Zero","","Reporting"
"","EC standard rate RC service output - IE","Standard-rate purchases of services from suppliers in EC - Reverse charge","Purchase","-23","","","","","","","","","","IE130001","true","Standard","","Reporting"
"","EC standard rate service input - IE","Standard-rate purchases of services from suppliers in EC","Purchase","23","","","","","","","","","","IE130003","false","Standard","","Reporting"
"","EC reduced rate goods for resale input - IE","Reduced rated purchases of goods from suppliers in EC for resale Input","Purchase","13.5","","","","","","","","","","IE130007","false","Reduced","","Reporting"
"","EC reduced rate goods for resale output - IE","Reduced rated purchases of goods from suppliers in EC for resale Output","Purchase","-13.5","","","","","","","","","","IE130008","true","Reduced","","Reporting"
"","EC reduced rate goods for non-resale input - IE","Reduced rated purchases of goods from suppliers in EC for non resale Input","Purchase","13.5","","","","","","","","","","IE130009","false","Reduced","","Reporting"
"","EC reduced rate goods for non-resale output - IE","Reduced rated purchases of goods from suppliers in EC for non resale Output","Purchase","-13.5","","","","","","","","","","IE130013","true","Reduced","","Reporting"
"","Second reduced rate goods for resale - IE","Second reduced rate sales/purchase of goods for resale","Purchase","9","","","","","","","","","","IE130015","false","Reduced","","Reporting"
"","Second reduced rate goods for non-resale - IE","Second reduced rate sales/purchase of goods for non-resale","Purchase","9","","","","","","","","","","IE130018","false","Reduced","","Reporting"
"","EC second reduced rate goods for resale input - IE","Second reduced rated purchases of goods from EC suppliers for resale Input","Purchase","9","","","","","","","","","","IE130021","false","Reduced","","Reporting"
"","EC second reduced rate goods for resale output - IE","Second reduced rated purchases of goods from EC suppliers for resale Output","Purchase","-9","","","","","","","","","","IE130025","true","Reduced","","Reporting"
"","Zero rate service UK or non-EC - IE","Purchase of Services from UK or outside EU","Purchase","0","","","","","","","","","","IE130027","false","Zero","","Reporting"
"","Exempt for resale - IE","Exempt purchase of goods/services for resale","Purchase","0","","","","","","","","","","IE130031","false","Exempt","","Reporting"
"","EC second reduced rate goods non-resale input - IE","Second reduced rated purchases of goods from EC suppliers for non resale Input","Purchase","9","","","","","","","","","","IE130033","false","Reduced","","Reporting"
"","EC second reduced rate goods non-resale output - IE","Second reduced rated purchases of goods from EC suppliers for non resale Output","Purchase","-9","","","","","","","","","","IE130049","true","Reduced","","Reporting"
"","PVA RC standard rate ROW import resale output - IE","Standard rate ROW import (Resale) Postponed VAT Output","Purchase","-23","","","","","","","","","","IE130051","true","Standard","","Reporting"
"","PVA standard rate ROW import resale input - IE","Standard rate ROW import (Resale) Postponed VAT Input","Purchase","23","","","","","","","","","","IE130055","false","Standard","","Reporting"
"","PVA RC standard rate ROW import non-resale output - IE","Standard rate ROW import (Non resale) Postponed VAT Output","Purchase","-23","","","","","","","","","","IE130057","true","Standard","","Reporting"
"","PVA standard rate ROW import non-resale input - IE","Standard rate ROW import (Non resale) Postponed VAT Input","Purchase","23","","","","","","","","","","IE130073","false","Standard","","Reporting"
"","PVA RC zero rate ROW import resale output - IE","RC Zero rate ROW import (Resale) Postponed VAT Output","Purchase","0","","","","","","","","","","IE130079","true","Zero","","Reporting"
"","PVA zero rate ROW import resale input - IE","Zero rate ROW import (Resale) Postponed VAT Input","Purchase","0","","","","","","","","","","IE130315","false","Zero","","Reporting"
"","PVA RC zero rate ROW import non-resale output - IE","RC Zero rate ROW import (Non resale) Postponed VAT Output","Purchase","0","","","","","","","","","","IE130317","true","Zero","","Reporting"
"","PVA zero rate ROW import non-resale input - IE","Zero rate ROW import (Non resale) Postponed VAT Input","Purchase","0","","","","","","","","","","IE130321","false","Zero","","Reporting"
"","PVA RC reduced rate ROW import resale output - IE","RC Reduced rate ROW import (Resale) Postponed VAT Output","Purchase","-13.5","","","","","","","","","","IE130323","true","Reduced","","Reporting"
"","PVA reduced rate ROW import resale input - IE","Reduced rate ROW import (Resale) Postponed VAT Input","Purchase","13.5","","","","","","","","","","IE130501","false","Reduced","","Reporting"
"","PVA RC reduced rate ROW import non-resale output - IE","RC Reduced rate ROW import (Non resale) Postponed VAT Output","Purchase","-13.5","","","","","","","","","","IE130503","true","Reduced","","Reporting"
"","PVA reduced rate ROW import non-resale input - IE","Reduced rate ROW import (Non resale) Postponed VAT Input","Purchase","13.5","","","","","","","","","","IE130507","false","Reduced","","Reporting"
"","PVA RC second reduced rate ROW import resale output - IE","RC Second reduced rate ROW import (Resale) Postponed VAT Output","Purchase","-9","","","","","","","","","","IE130508","true","Reduced","","Reporting"
"","PVA second reduced rate ROW import resale input - IE","Second reduced rate ROW import (Resale) Postponed VAT Input","Purchase","9","","","","","","","","","","IE130509","false","Reduced","","Reporting"
"","PVA RC second reduced rate ROW import non-resale output - IE","RC Second reduced rate ROW import (Non resale) Postponed VAT Output","Purchase","-9","","","","","","","","","","IE130513","true","Reduced","","Reporting"
"","PVA second reduced rate ROW import non-resale input - IE","Second reduced rate ROW import (Non resale) Postponed VAT Input","Purchase","9","","","","","","","","","","IE130515","false","Reduced","","Reporting"
"","EC exempt import of goods for resale - IE","Exempt import of goods from suppliers in EC","Purchase","0","","","","","","","","","","IE130518","false","Exempt","","Reporting"
"","ROW exempt import of goods - IE","Exempt import of goods from suppliers outside EC","Purchase","0","","","","","","","","","","IE130521","false","Exempt","","Reporting"
"","EC zero rate goods for resale - IE","Zero rated purchases of goods from suppliers in EC","Purchase","0","","","","","","","","","","IE130525","false","Zero","","Reporting"
"","EC goods standard rate resale input - IE","Standard rated purchases of goods from suppliers in EC for resale","Purchase","23","","","","","","","","","","IE130527","false","Standard","","Reporting"
"","EC RC goods standard rate resale output - IE","RC Standard rated purchases of goods from suppliers in EC for resale- Reverse Charge","Purchase","-23","","","","","","","","","","IE130531","true","Standard","","Reporting"
"","Purchase of services from UK or outside EU - IE","Purchase of Services from UK or outside EU","Purchase","0","","","","","","","","","","IE130533","false","Standard","","Reporting"
"","No purchase VAT - IE","Non-Vatable Tax Code, Not on Vat return","Purchase","0","","","","","","","","","","IE130549","false","Exempt","","Not reporting"
"","Zero rate - IE","Zero rated sales of goods/services for resale","Sale","0","","","","","","","","","","110001IE","false","Zero","","Reporting"
"","Export of goods ROW - IE","Export of goods ROW","Sale","0","","","","","","","","","","130001IE","false","Zero","","Reporting"
"","Standard rate - IE","Standard rated sales of goods/services for resale","Sale","23","","","","","","","","","","510001IE","false","Standard","","Reporting"
"","EC Services VAT Reg - IE","Sales of services to VAT registered customers in EC","Sale","0","","","","","","","","","","115001IE","false","Zero","","Reporting"
"","Export of Services ROW - IE","Export of services ROW","Sale","0","","","","","","","","","","135001IE","false","Zero","","Reporting"
"","Second reduced rate of goods for non-resale - IE","Second reduced rate sales/purchase of goods for non-resale","Sale","0","","","","","","","","","","110004IE","false","Reduced","","Reporting"
"","Exempt - IE","Exempt sales of goods/services for resale","Sale","0","","","","","","","","","","910001IE","false","Exempt","","Reporting"
"","Reduced rate goods for resale - IE","Reduced rate sales and purchase of goods for resale","Sale","13.5","","","","","","","","","","910004IE","false","Reduced","","Reporting"
"","EC Goods VAT Reg - IE","Sales of goods to VAT registered customers in EC","Sale","0","","","","","","","","","","110101IE","false","Zero","","Reporting"
"","No sales VAT - IE","Non-Vatable Tax Code, Not on Vat return","Sale","0","","","","","","","","","","190001IE","false","Zero","","Not reporting"