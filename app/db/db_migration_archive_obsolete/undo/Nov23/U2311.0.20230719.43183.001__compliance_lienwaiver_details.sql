-- <PERSON><PERSON><PERSON>:  add columns for lien waiver support to Compliance.
-- <PERSON><PERSON><PERSON>:  add final, voided, docparid, primarydockey, apbillkey, appaymentkey, sendtocontactkey to compliancerecord
-- <PERSON><PERSON><PERSON>:  add prrecordtype to compliancerecorddetail
ALTER TABLE COMPLIANC<PERSON><PERSON>ORDDE<PERSON>IL DROP COLUMN PRRECORDTYPE
/

DROP INDEX IX_COMPREC_SENDTOCONTACTKEY
/
DROP INDEX IX_COMPREC_APPAYMENTKEY 
/
DROP INDEX IX_COMPREC_APBILLKEY
/
DROP INDEX IX_COMPREC_PRIMARYDOCKEY 
/
DROP INDEX IX_COMPREC_DOCPARID
/
ALTER TABLE COMPLIANCERECORD DROP CONSTRAINT FK_COMPTYPE_SENDTOCONTACTKEY
/
ALTER TABLE COMPLIANCERECORD DROP CONSTRAINT FK_COMPREC_APPAYMENTKEY
/
ALTER TABLE COMPLIANCERECORD DROP CONSTRAINT FK_COMPREC_APBILLKEY
/
ALTER TABLE COMPLIANCERECORD DROP CONSTRAINT FK_COMPREC_PRIMARYDOCKEY
/
ALTER TABLE COMPLIANCERECORD DROP COLUMN SENDTOCONTACTKEY
/
ALTER TABLE COMPLIANCERECORD DROP COLUMN FINAL
/
ALTER TABLE COMPLIANCERECORD DROP COLUMN VOIDED
/
ALTER TABLE COMPLIANCERECORD DROP COLUMN DOCPARID           
/
ALTER TABLE COMPLIANCERECORD DROP COLUMN PRIMARYDOCKEY   
/
ALTER TABLE COMPLIANCERECORD DROP    COLUMN APBILLKEY             
/
ALTER TABLE COMPLIANCERECORD DROP    COLUMN APPAYMENTKEY       
/
