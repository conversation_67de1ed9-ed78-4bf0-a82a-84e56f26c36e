DROP INDEX UQ_RECONLOG_RECORDNO
/
ALTER TABLE RECONLOG DROP COLUMN RECORD#
/
-- Revert the reconhistory proc to original
CREATE OR REPLACE FORCE VIEW reconhistory (cny#,finacctid,finacctname,finacctno,finaccttype,recdate,recbal,created,loginid,filetype,whenmodified,status,"STATE",locationkey,deptkey,supdockey) AS
(
 select r.cny#, c.cardid as finacctid, c.description as finacctname, c.cardnum as finacctno, 'Credit Card' as finaccttype,
        r.recdate, r.recbal, r.created, u.loginid, r.filetype, r.whenmodified, r.status, r.state, c.locationkey, c.deptkey, r.supdockey  from reconlog r, creditcard c, userinfo u  where r.financialentity = c.cardid
       and c.cny# = r.cny#
       and r.userkey = u.record# (+)
       and r.cny# = u.cny# (+)
 union all
 select r.cny#, b.accountid as finacctid, b.name as finacctname, b.accountno as finacctno, b.type as finaccttype,
        r.recdate, r.recbal, r.created, u.loginid, r.filetype, r.whenmodified, r.status, r.state, b.locationkey, b.deptkey, r.supdockey  from reconlog r, bankaccount b, userinfo u  where r.financialentity = b.accountid
       and r.cny# = b.cny#
       and r.userkey = u.record# (+)
       and r.cny# = u.cny# (+)
);
/