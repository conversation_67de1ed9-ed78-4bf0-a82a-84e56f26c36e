------------------------------------------------------------------------------------------------------------------------
-- V2411.0.20240828.172377.001__add_transaction_type_for_adjustment.sql
-- JIRA : IA-172377: Add transaction type for adjustment
------------------------------------------------------------------------------------------------------------------------
/*

== DELTA ONLY FOR DEV SCHEMAS ==

Description: We have decided not to proceed with using ADJTYPE since no new adjustment type is being added for the refund.
The corrective action is to drop the column.
The repair action script can be found in
A2411.0.20240828.172377.001__add_transaction_type_for_adjustment.sql

Repair action: EXECUTE

*/

-- Add new columns INTO prrrecord
-- ALTER TABLE PRRECORD ADD (
--     ADJTYPE CHAR(1 CHAR)
-- )
-- /