-- skipped the create table for AFFILIA<PERSON>ENTITY as that will be a view v_affiliateentity written in another file
-- also we have updated AFFILIATEENTITY with LOCATION in below queries IN REFERENCES(as AFFILIATEENTITY table does not exists 
-- and V_AFFILIATEENTITY is a view cannot be used)

--The original script is modified and split into 4 scripts, 
-- 1. add column in Aug24 OCR,-- V2408.0.20241004.178637.001__affiliateentity_dimension_add_columns.sql
-- 2. add index in Post script Aug24 OCR,  --V2408.0.20241004.178637.002__affiliateentity_dimension_add_index_post.sql
-- 3. add foreign key with novalidate in this script file in Nov24, 
-- 4. enable validate in Post script in Nov24 --V2411.0.20240828.132125.002__affiliateentity_dimension_post.sql

/*
== DELTA ONLY FOR DEV SCHEMAS ==

Description:  modified script and split. If this migration is already run, no further action is necessary

Repair action: IGNORE
*/

CREATE TABLE AFFILIATEENTITYGROUP (
                                      CNY# NUMBER(15),
                                      RECORD# NUMBER(15),
                                      ID VARCHAR2(100 CHAR),
                                      NAME VARCHAR2(500 CHAR),
                                      DESCRIPTION VARCHAR2(500 CHAR),
                                      GROUPTYPE VARCHAR2(1 CHAR),
                                      MEMBERFILTERS VARCHAR2(4000 CHAR),
                                      LOCATIONKEY NUMBER(15) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),
                                      DEPTKEY NUMBER(15) DEFAULT sys_context('TMCtx', 'DEPTKEY'),
                                      WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
                                      WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
                                      CREATEDBY NUMBER(15),
                                      MODIFIEDBY  NUMBER(15),
                                      SI_UUID VARCHAR2(36 CHAR),
                                      CONSTRAINT PK_AFFILIATEENTITYGROUP PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,
                                      CONSTRAINT UQ_AFFILIATEENTITYGROUP_ID UNIQUE (CNY#, ID) USING INDEX TABLESPACE ACCTINDX  ENABLE,
                                      CONSTRAINT FK_AFFILIATEENTITYGROUP_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
                                      CONSTRAINT FK_AFFILIATEENTITYGROUP_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
                                      CONSTRAINT FK_AFFILIATEENTITYGROUP_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
                                      CONSTRAINT FK_AFFILIATEENTITYGROUP_LOCKEY FOREIGN KEY (CNY#, LOCATIONKEY) REFERENCES LOCATION (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_AFFILIATEENTITYGROUP_CREATEDBY ON AFFILIATEENTITYGROUP (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_AFFILIATEENTITYGROUP_MODIFIEDBY ON AFFILIATEENTITYGROUP (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_AFFILIATEENTITYGROUP_LOCKEY ON AFFILIATEENTITYGROUP (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX
/

CREATE TABLE AFFILIATEENTITYGRPMEMBERS (
                                           CNY# NUMBER(15),
                                           RECORD# NUMBER(15),
                                           GROUPKEY NUMBER(15) CONSTRAINT NN_AFFILIATEENTITYGRPMEMBERS_GROUPKEY NOT NULL ENABLE,
                                           AFFILIATEENTITYKEY NUMBER(15) CONSTRAINT NN_AFFILIATEENTITYGRPMEMBERS_AFFILIATEENTITY NOT NULL ENABLE,
                                           SORTORD NUMBER(8) CONSTRAINT NN_AFFILIATEENTITYGRPMEMBERS_SORTORD NOT NULL ENABLE,
                                           WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
                                           WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
                                           CREATEDBY NUMBER(15),
                                           MODIFIEDBY  NUMBER(15),
                                           SI_UUID VARCHAR2(36 CHAR),
                                           CONSTRAINT PK_AFFILIATEENTITYGRPMEMBERS PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,
                                           CONSTRAINT FK_AFFILIATEENTITYGRPMEMBERS_GROUP FOREIGN KEY (CNY#, GROUPKEY) REFERENCES AFFILIATEENTITYGROUP(CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
                                           CONSTRAINT FK_AFFILIATEENTITYGRPMEMBERS_AFFILIATEENTITY FOREIGN KEY (CNY#, AFFILIATEENTITYKEY) REFERENCES LOCATION(CNY#, RECORD#) DEFERRABLE ENABLE,
                                           CONSTRAINT FK_AFFILIATEENTITYGRPMEMBERS_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
                                           CONSTRAINT FK_AFFILIATEENTITYGRPMEMBERS_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
                                           CONSTRAINT FK_AFFILIATEENTITYGRPMEMBERS_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_AFFILIATEENTITYGRPMEMBERS_CREATEDBY ON AFFILIATEENTITYGRPMEMBERS (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_AFFILIATEENTITYGRPMEMBERS_MODIFIEDBY ON AFFILIATEENTITYGRPMEMBERS (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_AFFILIATEENTITYGRPMEMBERS_AFFILIATEENTITY ON AFFILIATEENTITYGRPMEMBERS (CNY#, AFFILIATEENTITYKEY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_AFFILIATEENTITYGRPMEMBERS_GROUP ON AFFILIATEENTITYGRPMEMBERS (CNY#, GROUPKEY) TABLESPACE ACCTINDX INVISIBLE
/



---- ***** Adding AFFILIATEENTITYDIMKEY in the following tables

ALTER TABLE BASEGLTOTALS ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15) DEFAULT 0 CONSTRAINT NN_BASEGLTOTALS_AFFILIATEENTITYDIMKEY NOT NULL ENABLE
)
/

ALTER TABLE HLTH_BASEGLTOTALS ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15) DEFAULT 0 CONSTRAINT NN_HLTH_BASEGLTOTALS_AFFILIATEENTITYDIMKEY NOT NULL ENABLE
)
/

ALTER TABLE UPDPRBCACHE ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15)
)
/

ALTER TABLE PROJECTTOTALS ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15) DEFAULT 0 CONSTRAINT NN_PROJECTTOTALS_AFFILIATEENTITYDIMKEY NOT NULL ENABLE
)
/

ALTER TABLE DETOTALS ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15) DEFAULT 0 CONSTRAINT NN_DETOTALS_AFFILIATEENTITYDIMKEY NOT NULL ENABLE
)
/

ALTER TABLE GLOB_TEMP_GLENTRY_TOTALS ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15)
)
/

ALTER TABLE GLOB_TEMP_PROJECTTOTALS ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15)
)
/

ALTER TABLE CONSGLENTRY ADD (
  AFFILIATEENTITYDIMKEY NUMBER(15) DEFAULT 0 CONSTRAINT NN_CONSGLENTRY_AFFILIATEENTITYDIMKEY NOT NULL ENABLE
)
/    
   
ALTER TABLE GLODCONFIG ADD (AFFILIATEENTITYDIMKEY CHAR(1 CHAR) DEFAULT 'F')
/


---- ***** Adding REQUIREAFFILIATEENTITY in the following tables

ALTER TABLE BASEACCOUNT ADD (REQUIREAFFILIATEENTITY CHAR(1 CHAR) DEFAULT 'F' CONSTRAINT NN_BASEACCOUNT_REQAFFILIATEENTITY NOT NULL ENABLE)
/


---- ***** Adding FILTERAFFILIATEENTITY in the following tables

ALTER TABLE GLACCTGRP ADD (FILTERAFFILIATEENTITY NUMBER(2) )
/

---- ***** Adding AFFILIATEENTITYDIMENSION in the following tables

ALTER TABLE ARSETUP ADD (AFFILIATEENTITYDIMENSION CHAR(1 CHAR) DEFAULT 'F')
/


-- Adding foreign key for column AFFILIATEENTITYDIMKEY with NOVALIDATE for the following tables
-- if the foreign key does not exists.
DECLARE
fk_count INTEGER;
    loop_table_name
VARCHAR2(30);
    add_fk_stmt
VARCHAR2(300);
    TYPE
table_names_t IS TABLE OF VARCHAR2(30);
    table_names
table_names_t := table_names_t(
        'GLACCTGRP', 'REPORTINFO', 'GLENTRY', 'GLBUDGET', 'REVRECSCHEDULEENTRY',
        'PRENTRY', 'RECURPRENTRY', 'DOCENTRY', 'RECURDOCENTRY', 'TRANSTMPLENTRY',
        'RECURSUBTOTALS', 'DOCHDRSUBTOTALS', 'PRTAXENTRY', 'TIMESHEETENTRY',
        'ALLOCATIONENTRY', 'RECURGLENTRY', 'CONTRACT', 'CONTRACTDETAIL',
        'CONTRACTEXPENSEDETAIL', 'GENINVOICELINE', 'GLACCTALLOCATIONBASIS',
        'GLACCTALLOCATIONREVERSE', 'GLACCTALLOCATIONSOURCE', 'GLACCTALLOCATIONTARGET',
        'PJESTIMATEENTRY', 'PROJECTCONTRACTLINE', 'RATETABLEENTRY_AP',
        'RATETABLEENTRY_CC', 'RATETABLEENTRY_EE', 'RATETABLEENTRY_GL',
        'RATETABLEENTRY_PO', 'RATETABLEENTRY_TS', 'CRDETAIL'
    );
BEGIN
FOR i IN 1..table_names.COUNT LOOP
        loop_table_name := table_names(i);
SELECT COUNT(*)
INTO fk_count
FROM all_constraints
WHERE owner = sys_context('userenv', 'current_schema')
  AND table_name = loop_table_name
  AND constraint_name = 'FK_' || loop_table_name || '_AFFILIATEENTITYDIMKEY';

IF
fk_count = 0 THEN
            add_fk_stmt := 'ALTER TABLE ' || loop_table_name || ' ADD CONSTRAINT FK_' || loop_table_name || '_AFFILIATEENTITYDIMKEY FOREIGN KEY (CNY#, AFFILIATEENTITYDIMKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE NOVALIDATE';
EXECUTE IMMEDIATE add_fk_stmt;
END IF;
END LOOP;
END;
/    

-- Adding foreign key for column AFFILIATEENTITYGROUPKEY with NOVALIDATE
-- if the foreign key does not exists.
DECLARE
fk_count INTEGER;
    loop_table_name
VARCHAR2(30);
    add_fk_stmt
VARCHAR2(300);
    TYPE
table_names_t IS TABLE OF VARCHAR2(30);
    table_names
table_names_t := table_names_t(
        'REPORTINFO', 'GLACCTALLOCATIONBASIS', 'GLACCTALLOCATIONSOURCE', 'GLDIMGRPMEMBERS'
    );
BEGIN
FOR i IN 1..table_names.COUNT LOOP
        loop_table_name := table_names(i);
SELECT COUNT(*)
INTO fk_count
FROM all_constraints
WHERE owner = sys_context('userenv', 'current_schema')
  AND table_name = loop_table_name
  AND constraint_name = 'FK_' || loop_table_name || '_AFFILIATEENTITYGROUPKEY';
IF
fk_count = 0 THEN
            add_fk_stmt := 'ALTER TABLE ' || loop_table_name || ' ADD CONSTRAINT FK_' || loop_table_name || '_AFFILIATEENTITYGROUPKEY FOREIGN KEY (CNY#, AFFILIATEENTITYGROUPKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE NOVALIDATE';
EXECUTE IMMEDIATE add_fk_stmt;
END IF;
END LOOP;
END;
/


---- Updating AFFILIATEENTITYDIMKEY in the following views at /users/shashikant.kuswaha/projects/feature/app/db/db_migration/objects/Views/

-- R__VIEW_BASEGLTOTALS_OWNER

-- R__VIEW_DISPGLENTRY

-- R__VIEW_DISPPRENTRY

-- R__VIEW_DISPRECURGLENTRY

-- R__VIEW_DISPRECURPRENTRY

-- R__VIEW_GLACCOUNTBALANCE

-- R__VIEW_GLBUDGET_OWNER

-- R__VIEW_GLENTRY_OWNER

-- R__VIEW_V_BASEGLTOT_DDS10P1

-- R__VIEW_V_BASEGLTOT_DDS10P2

-- R__VIEW_V_BASEGLTOT_DDS10P3

-- R__VIEW_V_BILLABLEEXPENSE

-- R__VIEW_V_DOCHDRSUBTOTALS

-- R__VIEW_V_GENINVOICEPREBILLLINE

-- R__VIEW_V_GLACCOUNTBAL_DDS10

-- R__VIEW_V_GLACCOUNTBAL_DDS10L2

-- R__VIEW_V_GLACCOUNTBAL_DDS10L3

-- R__VIEW_V_GLBUDGETGLENTRY

-- R__VIEW_V_GLDETAIL

-- R__VIEW_V_GLDOCDETAIL

-- R__VIEW_V_IETRANSACTIONS

-- R__VIEW_V_KITCOSTING

-- R__VIEW_V_RRSCHDLENTRY_DETAIL

-- R__VIEW_V_RRSCHEDULEENTRY

-- R__VIEW_V_CREREPORTING


---- Updating REQUIREAFFILIATEENTITY in the following views at /users/shashikant.kuswaha/projects/feature/app/db/db_migration/objects/Views/

-- R__VIEW_GLACCOUNT

-- R__VIEW_STATACCOUNT


---- Updating following packages at /users/shashikant.kuswaha/projects/feature/app/db/db_migration/objects/Packages/

-- R__PKG_ACCT_UTILS

-- R__PKG_PA_UTILS


---- Updating following package bodies at /users/shashikant.kuswaha/projects/feature/app/db/db_migration/objects/PackageBodies/

-- R__PKGBODY_ACCT_UTILS

-- R__PKGBODY_HLTH_UTILS

-- R__PKGBODY_INV_UTILS

-- R__PKGBODY_PA_UTILS


---- Updating following procedures at /users/shashikant.kuswaha/projects/feature/app/db/db_migration/objects/Procedures/

-- R__PROC_COMPUTEDEFERREDGLTOTALS


---- Updating following triggers at /users/shashikant.kuswaha/projects/feature/app/db/db_migration/objects/Triggers/

-- R__TRIG_ARIU_DOCENTRY

-- R__TRIG_ARIU_TIMESHEETENTRY

-- R__TRIG_ARIUD_PRENTRY

-- R__TRIG_BRDU_PRENTRY

-- R__TRIG_BRUD_DOCENTRY

-- R__TRIG_BRUD_TIMESHEETENTRY

-- R__TRIG_GLENTRY_DO_GLTOTALS


---- ***** MANUALLY update following triggers

    -- R__TRIG_BRIU_BASEGLTOTALS

    -- R__TRIG_BRIU_GLBUDGET


---- ***** Manually update AFFILIATEENTITYDIMKEY/FILTERAFFILIATEENTITY/REQUIREAFFILIATEENTITY in the following MEGA views

    -- R__VIEW_V_MEBASEACCOUNT 
    -- R__VIEW_V_MEGLACCOUNT
    -- R__VIEW_V_MESTATACCOUNT
    -- R__VIEW_V_MEBASEGLTOTALS
    -- R__VIEW_V_MEDISPGLENTRY
    -- R__VIEW_V_MEDISPPRENTRY
    -- R__VIEW_V_MEDISPRECURGLENTRY
    -- R__VIEW_V_MEDISPRECURPRENTRY
    -- R__VIEW_V_MEDOCENTRY
    -- R__VIEW_V_MEGLACCOUNTBALANCE
    -- R__VIEW_V_MEGLENTRY
    -- R__VIEW_V_MEPRENTRY
    -- R__VIEW_V_MEPRENTRYDUE
    -- R__VIEW_V_MERECURGLENTRY
    -- R__VIEW_V_MERECURPRENTRY
    -- R__VIEW_V_MERECURSUBTOTALS
    -- R__VIEW_V_VMEDOCHDRSUBTOTALS
    -- R__VIEW_V_MEGLBUDGET
    -- R__VIEW_V_VMEGLDETAIL
    -- R__VIEW_V_VMEGLDOCDETAIL
    -- R__VIEW_V_MEGLENTRY
    -- R__VIEW_V_VMEIETRANSACTIONS
    -- R__VIEW_V_VMEKITCOSTING
    -- R__VIEW_V_MEREVRECSCHEDULEENTRY
    -- R__VIEW_V_VMERRSCHDLENTRY_DETAIL
    -- R__VIEW_V_VMERRSCHEDULEENTRY
    -- R__VIEW_V_MEGLACCTGRP
    -- R__VIEW_V_MERECURDOCENTRY
    -- R__VIEW_V_METRANSTMPLENTRY
    -- R__VIEW_V_MEPRTAXENTRY
    -- R__VIEW_V_METIMESHEETENTRY
    -- R__VIEW_V_MEALLOCATIONENTRY
    -- R__VIEW_V_MECONTRACT
    -- R__VIEW_V_MECONTRACTDETAIL
    -- R__VIEW_V_MECONTRACTEXPENSEDETAIL
    -- R__VIEW_V_MEPJESTIMATEENTRY