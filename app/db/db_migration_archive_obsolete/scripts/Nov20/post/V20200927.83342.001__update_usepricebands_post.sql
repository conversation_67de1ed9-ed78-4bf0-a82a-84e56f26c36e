--liquibase formatted sql
--changeset vwong:20200927.83342.001 runOnChange:false logicalFilePath:V20200927.83342.001__update_usepricebands_post.sql
BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

update (select usepricebands from allocitemprclst ai
        inner join allocprclst al on 
            al.record# = ai.allocprclstkey and 
            al.cny# = ai.cny# 
        where
            al.pricelisttype = 'M' and
            ai.usepricebands in ('true')
        ) item     
set 
    item.usepricebands = 'T'
/

-- update all non true and not null to 'F'
update (select usepricebands from allocitemprclst ai
        inner join allocprclst al on 
            al.record# = ai.allocprclstkey and 
            al.cny# = ai.cny# 
        where
            al.pricelisttype = 'M' and
            ai.usepricebands not in ('T', 'true') and 
            ai.usepricebands is not null
        ) item     
set 
    item.usepricebands = 'F'
/

BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/