--liquibase formatted sql
--changeset abhijit.kumar:V20200915.118801.001 runOnChange:false logicalFilePath:V20200915.118801.001__F2965_budget_credit_type_account.sql

------------------------------------------------------------------------------------------------------------------------
----------------  START F2965 - Budget credit type account migration --------------
------------------------------------------------------------------------------------------------------------------------
DECLARE
    c_limit   PLS_INTEGER;
    TYPE glb_ba_bm_type IS RECORD (
        glb_cny      glbudget.cny#%TYPE,
        glb_record   glbudget.record#%TYPE
    );
    TYPE glb_ba_bm_typetable IS
        TABLE OF glb_ba_bm_type INDEX BY BINARY_INTEGER;
    glbdata   glb_ba_bm_typetable;
    CURSOR glb_ba_bm_cur IS
    SELECT
        glb.cny#,
        glb.record#
    FROM
        glbudget      glb,
        baseaccount   ba,
        (
            SELECT
                cny#,
                'F' state
            FROM
                budgetmigration
            WHERE
                nvl(state,'F') <> 'T'
        ) bm
    WHERE
        glb.cny# = ba.cny#
        AND glb.account# = ba.record#
        AND ba.normal_balance = -1
        AND glb.cny# = bm.cny# (+)
        AND nvl(bm.state,'T') <> 'F';

BEGIN
    OPEN glb_ba_bm_cur;
    c_limit := 25000;
    LOOP
        FETCH glb_ba_bm_cur BULK COLLECT INTO glbdata LIMIT c_limit;
        EXIT WHEN glbdata.count = 0;
        FORALL indx IN 1..glbdata.count
            UPDATE glbudget
            SET
                amount = amount * -1
            WHERE
                cny# = glbdata(indx).glb_cny
                AND record# = glbdata(indx).glb_record;

    END LOOP;

    CLOSE glb_ba_bm_cur;
END;
/

----------------------------------------------------------------------------------------------------------------------
----------------  END F2965 - Budget credit type account migration --------------
------------------------------------------------------------------------------------------------------------------------
