--liquibase formatted sql
--changeset jim.reed:20211020.3712.007 runOnChange:false logicalFilePath:V20211020.3712.007__RateTableEntries_Deferrable_FK.sql


ALTER TABLE RATETABLE DROP CONSTRAINT FK_RATETABLE_LOCATIONKEY
/

ALTER TABLE RATETABLE ADD
    CONSTRAINT FK_RATETABLE_LOCATIONKEY FOREIGN KEY (CNY#, LOCATIONKEY)
        REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE
/

ALTER TABLE RATETABLEENTRY_AP DROP CONSTRAINT FK_RATETABLEENTRY_AP_ST
/
ALTER TABLE RATETABLEENTRY_AP DROP CONSTRAINT FK_RATETABLEENTRY_AP_SCT
/
ALTER TABLE RATETABLEENTRY_AP DROP CONSTRAINT FK_RATETABLEENTRY_AP_AT
/

ALTER TABLE RATETABLEENTRY_AP ADD (
    CONSTRAINT FK_RATETABLEENTRY_AP_ST FOREIGN KEY (CNY#, <PERSON><PERSON><PERSON><PERSON><PERSON>SKKEY)
        REFERENCES STANDARDTASK (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_AP_SCT FOREIGN KEY (CNY#, STANDARDCOSTTYPEKEY)
        REFERENCES STANDARDCOSTTYPE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_AP_AT FOREIGN KEY (CNY#, ACCUMULATIONTYPEKEY)
        REFERENCES ACCUMULATIONTYPE (CNY#, RECORD#) DEFERRABLE ENABLE
)
/

ALTER TABLE RATETABLEENTRY_GL DROP CONSTRAINT FK_RATETABLEENTRY_GL_ST
/
ALTER TABLE RATETABLEENTRY_GL DROP CONSTRAINT FK_RATETABLEENTRY_GL_SCT
/
ALTER TABLE RATETABLEENTRY_GL DROP CONSTRAINT FK_RATETABLEENTRY_GL_AT
/

ALTER TABLE RATETABLEENTRY_GL ADD (
    CONSTRAINT FK_RATETABLEENTRY_GL_ST FOREIGN KEY (CNY#, STANDARDTASKKEY)
        REFERENCES STANDARDTASK (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_GL_SCT FOREIGN KEY (CNY#, STANDARDCOSTTYPEKEY)
        REFERENCES STANDARDCOSTTYPE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_GL_AT FOREIGN KEY (CNY#, ACCUMULATIONTYPEKEY)
        REFERENCES ACCUMULATIONTYPE (CNY#, RECORD#) DEFERRABLE ENABLE
)
/

ALTER TABLE RATETABLEENTRY_EE DROP CONSTRAINT FK_RATETABLEENTRY_EE_ST
/
ALTER TABLE RATETABLEENTRY_EE DROP CONSTRAINT FK_RATETABLEENTRY_EE_SCT
/
ALTER TABLE RATETABLEENTRY_EE DROP CONSTRAINT FK_RATETABLEENTRY_EE_AT
/

ALTER TABLE RATETABLEENTRY_EE ADD (
    CONSTRAINT FK_RATETABLEENTRY_EE_ST FOREIGN KEY (CNY#, STANDARDTASKKEY)
        REFERENCES STANDARDTASK (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_EE_SCT FOREIGN KEY (CNY#, STANDARDCOSTTYPEKEY)
        REFERENCES STANDARDCOSTTYPE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_EE_AT FOREIGN KEY (CNY#, ACCUMULATIONTYPEKEY)
        REFERENCES ACCUMULATIONTYPE (CNY#, RECORD#) DEFERRABLE ENABLE
)
/

ALTER TABLE RATETABLEENTRY_CC DROP CONSTRAINT FK_RATETABLEENTRY_CC_ST
/
ALTER TABLE RATETABLEENTRY_CC DROP CONSTRAINT FK_RATETABLEENTRY_CC_SCT
/
ALTER TABLE RATETABLEENTRY_CC DROP CONSTRAINT FK_RATETABLEENTRY_CC_AT
/

ALTER TABLE RATETABLEENTRY_CC ADD (
    CONSTRAINT FK_RATETABLEENTRY_CC_ST FOREIGN KEY (CNY#, STANDARDTASKKEY)
        REFERENCES STANDARDTASK (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_CC_SCT FOREIGN KEY (CNY#, STANDARDCOSTTYPEKEY)
        REFERENCES STANDARDCOSTTYPE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_CC_AT FOREIGN KEY (CNY#, ACCUMULATIONTYPEKEY)
        REFERENCES ACCUMULATIONTYPE (CNY#, RECORD#) DEFERRABLE ENABLE
)
/

ALTER TABLE RATETABLEENTRY_PO DROP CONSTRAINT FK_RATETABLEENTRY_PO_ST
/
ALTER TABLE RATETABLEENTRY_PO DROP CONSTRAINT FK_RATETABLEENTRY_PO_SCT
/
ALTER TABLE RATETABLEENTRY_PO DROP CONSTRAINT FK_RATETABLEENTRY_PO_AT
/

ALTER TABLE RATETABLEENTRY_PO ADD (
    CONSTRAINT FK_RATETABLEENTRY_PO_ST FOREIGN KEY (CNY#, STANDARDTASKKEY)
        REFERENCES STANDARDTASK (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_PO_SCT FOREIGN KEY (CNY#, STANDARDCOSTTYPEKEY)
        REFERENCES STANDARDCOSTTYPE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_RATETABLEENTRY_PO_AT FOREIGN KEY (CNY#, ACCUMULATIONTYPEKEY)
        REFERENCES ACCUMULATIONTYPE (CNY#, RECORD#) DEFERRABLE ENABLE
)
/
