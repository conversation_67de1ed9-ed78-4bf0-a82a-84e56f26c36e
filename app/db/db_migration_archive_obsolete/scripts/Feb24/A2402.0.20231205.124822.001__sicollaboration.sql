-- Updating the modulekey to 84.SICOLLAB
update iamodule set iamoduleid='84.SICOLLAB' where iamoduleid='83.SICOLLAB'
/
update SIC<PERSON>LABORATIONSETUP set modulekey='84.SICOLLAB' where modulekey='83.SICOLLAB'
/
alter table SICOLLABORATIONSETUP modify modulekey default '84.SIC<PERSON><PERSON><PERSON>'
/
update MODULEPREF set modulekey='84.SICOLLAB' where modulekey='83.SICOLLAB'
/
declare
    column_exists exception;
    pragma exception_init (column_exists , -01430);
begin
    execute immediate 'ALTER TABLE SICOLLABORATIONSETUP ADD SI_UUID VARCHAR2(36 CHAR)';
    exception when column_exists then null;
end;
/
ALTER TABLE SICOLLABORATIONSETUP MODIFY CREATEDBY NUMBER(15, 0)
/
ALTER TABLE SICOLLABORATIONSETUP MODIFY MODIFIEDBY NUMBER(15, 0)
/
