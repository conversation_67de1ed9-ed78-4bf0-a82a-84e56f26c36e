
DECLARE
  CLOBSTRING CLOB := '';
BEGIN
  CLOBSTRING := '<customErpPackage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><packageDescription><name>Fixed Asset Management Report</name><description>This package installs custom reports for Fixed Assets Management</description><author>Sage Intacct</author></packageDescription><customReports>';
  CLOBSTRING := CLOBSTRING || '<customReport><name>Depreciation Forecast Report</name><description></description><type>summary</type><module>fa</module><root>DEPRECIATIONSCHEDULEENTRY</root><documentType></documentType><columns><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.ASSETID</path><label>Asset ID</label></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.NAME</path><label>Asset Name</label></field><field><path>DEPRECIATIONSCHEDULEENTRY.POSTINGDATE</path><label>Posting date</label></field><field><path>DEPRECIATIONSCHEDULEENTRY.AMOUNT</path><label>Remaining Depreciation</label></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.CLASSIFICATION.MELOCATION.LOCATIONID</path><label>Location ID</label></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.CLASSIFICATION.CLASSIFICATIONID</path><label>Classification ID</label></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.CLASSIFICATION.NAME</path><label>Classification Name</label></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.DEPRRULE.POSTINGRULE.JOURNAL.TITLE</path><label>Journal</label></field></columns><parameters><field><path>DEPRECIATIONSCHEDULEENTRY.POSTINGDATE</path><label>Posting date</label><promptOnRun>Y</promptOnRun><fieldSet></fieldSet></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.CLASSIFICATION.NAME</path><label>Asset Classification Name</label><promptOnRun>N</promptOnRun><fieldSet></fieldSet></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.DEPRRULE.POSTINGRULE.JOURNAL.BOOKID</path><label>Book</label><promptOnRun>N</promptOnRun><fieldSet></fieldSet></field>';
  CLOBSTRING := CLOBSTRING || '<field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.DEPRRULE.POSTINGRULE.JOURNAL.TITLE</path><label>Journal</label><promptOnRun>N</promptOnRun><fieldSet></fieldSet></field></parameters><filter><logical logical_operator=''and''><expression><path>DEPRECIATIONSCHEDULEENTRY.STATE</path><operator>eq</operator><value>Not posted</value></expression><expression><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.STATUS</path><operator>eq</operator><value>Active</value></expression></logical></filter><sortby><field><path>DEPRECIATIONSCHEDULEENTRY.AMOUNT</path><direction>asc</direction></field></sortby><groupby><level><group><field><path>DEPRECIATIONSCHEDULEENTRY.POSTINGDATE</path><direction>asc</direction></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.CLASSIFICATION.CLASSIFICATIONID</path></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.CLASSIFICATION.NAME</path></field></group><summary><field><path>DEPRECIATIONSCHEDULEENTRY.AMOUNT</path><label>Sum of Amount</label><function>sum</function></field></summary></level><level><group><field><path>DEPRECIATIONSCHEDULEENTRY.POSTINGDATE</path><direction>asc</direction></field><field><path>DEPRECIATIONSCHEDULEENTRY.DEPRSCHEDULE.ASSET.CLASSIFICATION.CLASSIFICATIONID</path></field></group><summary><field><path>DEPRECIATIONSCHEDULEENTRY.AMOUNT</path><label>Sum of Amount</label><function>sum</function></field></summary></level><level><group><field><path>DEPRECIATIONSCHEDULEENTRY.POSTINGDATE</path><direction>asc</direction></field></group><summary><field><path>DEPRECIATIONSCHEDULEENTRY.AMOUNT</path><label>Sum of Amount</label><function>sum</function></field></summary></level></groupby>';
  CLOBSTRING := CLOBSTRING || '<summary><field><path>DEPRECIATIONSCHEDULEENTRY.AMOUNT</path><label>Sum of Amount</label><function>sum</function></field></summary><graph><graphtype>Column</graphtype><yaxis>sum__DEPRECIATIONSCHEDULEENTRY.AMOUNT</yaxis><xaxis>DEPRECIATIONSCHEDULEENTRY.POSTINGDATE</xaxis><graphgrouping></graphgrouping><xorient>W</xorient><legend>B</legend><fontsize>10</fontsize><graphsize>M</graphsize><width></width><height></height></graph></customReport>';
  CLOBSTRING := CLOBSTRING || '</customReports></customErpPackage>';
  INSERT INTO IAPACKAGE
    (
      RECORD#,
      NAME,
      DESCRIPTION,
      AUTHOR,
      INTACCTID,
      SIGNED,
      INDUSTRYCODE,
      XML
    )
    VALUES
    (
      (select max(record#)+1 from iapackage),
      'Fixed Assets Management Reports',
      'This package installs custom reports for Fixed Assets Management',
      'Intacct',
      NULL,
      NULL,
      'ALL',
      CLOBSTRING
    );

END;
/
