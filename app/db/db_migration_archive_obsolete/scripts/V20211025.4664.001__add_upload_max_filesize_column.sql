--liquibase formatted sql
--changeset teodor.mincu:20211025.4664.001 runOnChange:false logicalFilePath:V20211025.4664.001__add_upload_max_filesize_column.sql

insert into companypref (cny#, record#, STATUS, PROPERTY, VALUE)
select
    distinct(record#),
    (coalesce((SELECT max(record#) from companypref where cny# = company.record#),0) + 1),
    'T',
    'ATTACHMENT_MAX_UPLOAD_SIZE',
    -1
from company
/

update recordseq set recordseq.value = (select(max(record#)) from companypref where cny# = recordseq.cny#)
where recordseq.property='COMPANYPREF.RECORD#' and recordseq.cny# in (select record# from company)
/