alter session set constraints=deferred;
DELETE FROM IACOACATMEMBERS WHERE PARENT# = '-1282' AND CATEGORYKEY = '941';
DELETE FROM IACOACATMEMBERS WHERE PARENT# = '-1389' AND CATEGORYKEY = '963';
DELETE FROM IACOACATMEMBERS WHERE PARENT# = '-1300' AND CATEGORYKEY = '905';
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2384,'System_AP - Beginning of Period',1,'T','B','AP - Beginning of Period','Total AP - Beginning of Period','T','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2386,'System_AP - End of Period',1,'T','E','AP - End of Period','Total AP - End of Period','T','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2388,'System_AP - Average Numerator',1,'F','P','AP - Average Numerator','Total AP - Average Numerator','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2390,'System_AP - Average Balance',1,null,'P','AP - Average Balance','Total AP - Average Balance','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2393,'System_AP Turnover Ratio',1,null,'P','AP Turnover Ratio','Total AP Turnover Ratio','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2401,'System_AR - Beginning of Period',-1,'T','B','AR - Beginning of Period','Total AR - Beginning of Period','T','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2403,'System_AR - End of Period',-1,'T','E','AR - End of Period','Total AR - End of Period','T','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2411,'System_Active Vendors',1,'T',null,'Active Vendors','Total Active Vendors','VEN',null,'NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2412,'System_AP Turnover Ratio Inverse for DPO monthly',1,null,'P','AP Turnover Ratio Inverse for DPO monthly','Total AP Turnover Ratio Inverse for DPO monthly','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2413,'System_DPO monthly',1,null,'P','DPO monthly','Total DPO monthly','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2414,'System_Bad Debt Ratio',1,null,'P','Bad Debt Ratio','Total Bad Debt Ratio','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2415,'System_AR - Average Numerator',-1,'F','P','AR - Average Numerator','Total AR - Average Numerator','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2416,'System_AR - Average Balance',-1,null,'P','AR - Average Balance','Total AR - Average Balance','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2417,'System_DSO numerator',1,null,'P','DSO numerator','Total DSO numerator','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2418,'System_DSO',1,null,'P','DSO','Total DSO','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2419,'System_Cash Ratio',1,null,'P','Cash Ratio','Total Cash Ratio','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2420,'System_Active Customers',1,'T',null,'Active Customers','Total Active Customers','CUS',null,'NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2421,'System_Gross land, buildings, and equipment',1,'T','E','Gross land, buildings, and equipment','Total Gross land, buildings, and equipment','T','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2422,'System_Non-Liquid NA',1,'F','P','Non-Liquid NA','Total Non-Liquid NA','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2423,'System_Unrestricted Net Assets',-1,'F','P','Unrestricted Net Assets','Total Unrestricted Net Assets','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2424,'System_Unrestricted Liquid Net Assets',1,null,'P','Unrestricted Liquid Net Assets','Total Unrestricted Liquid Net Assets','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2425,'System_Non-cash Expenses',1,'F','P','Non-cash Expenses','Total Non-cash Expenses','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2426,'System_Functional Expenses, Net',1,null,'P','Functional Expenses, Net','Total Functional Expenses, Net','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2427,'System_Liquidity denominator monthly',1,null,'P','Liquidity denominator monthly','Total Liquidity denominator monthly','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2428,'System_Grants and Contributions',-1,'F','P','Grants and Contributions','Total Grants and Contributions','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2429,'System_Earned Revenue',-1,'F','P','Earned Revenue','Total Earned Revenue','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2430,'System_Net Surplus (Deficit)',-1,'F','P','Net Surplus (Deficit)','Total Net Surplus (Deficit)','G','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2432,'System_Interest Expense CR',-1,'T','P','Interest Expenses','Total Interest Expenses','T','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2440,'System_AP - Average Balance2',1,null,'P','AP - Average Balance2','Total AP - Average Balance2','C','B','NFP');
insert into IAGLACCTGRP (RECORD#, NAME, NORMAL_BALANCE, ISLEAF, ASOF, TITLE, TOTALTITLE, MEMBERTYPE, DBCR, INDUSTRYCODE) values (-2449,'System_Total Current Receivables, Net',1,'F','P','Total Current Receivables, Net','Total Current Receivables, Net','G','B','NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4938,-2449,-1750,3,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4937,-2449,-1419,2,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4936,-2449,-1230,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4935,-2449,-1236,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4882,-2388,-2386,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4881,-2388,-2384,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4864,-2430,-1438,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4863,-2430,-2206,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4862,-2429,-1367,7,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4861,-2429,-1281,6,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4860,-2429,-2189,5,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4859,-2429,-1364,4,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4858,-2429,-1332,3,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4857,-2429,-1288,2,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4856,-2429,-1756,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4855,-2429,-1425,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4854,-2428,-2186,5,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4853,-2428,-1734,4,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4852,-2428,-1733,3,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4851,-2428,-1426,2,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4850,-2428,-1424,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4849,-2428,-1738,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4848,-2425,-1730,2,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4847,-2425,-1261,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4846,-2425,-1377,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4845,-2423,-1311,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4844,-2422,-1305,4,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4843,-2422,-1349,3,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4842,-2422,-1304,2,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4841,-2422,-1338,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4840,-2422,-2421,0,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4839,-2415,-2403,1,'NFP');
insert into IAGLACCTGRPMEMBERS (RECORD#, PARENT#, CHILD#, SORTORD, INDUSTRYCODE) values (4838,-2415,-2401,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3048,-1282,941,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3038,-1389,963,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3030,-1300,905,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3017,-2432,941,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3014,-2421,933,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3013,-2403,967,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3011,-2401,967,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3008,-2386,968,0,'NFP');
insert into IACOACATMEMBERS (RECORD#, PARENT#, CATEGORYKEY, SORTORD, INDUSTRYCODE) values (3006,-2384,968,0,'NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (328,-2424,-2423,null,'S',-2422,null,0,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (327,-2427,-2426,null,'D',null,'12',0,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (326,-2426,-1438,null,'S',-2425,null,2,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (325,-2419,-1333,null,'D',-1253,null,2,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (324,-2418,-2417,null,'M',null,'30.416666666',2,'V','Days','1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (323,-2417,-2416,null,'D',-2206,null,2,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (322,-2413,-2412,null,'M',null,'30.416666666',2,'V','Days','1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (321,-2416,-2415,null,'D',null,'2',2,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (320,-2414,-1371,null,'D',-2206,null,2,'P',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (319,-2412,-2390,null,'D',-1438,null,2,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (318,-2393,-1438,null,'D',-2390,null,2,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (317,-2390,-2388,null,'D',null,'-2',2,'V',null,'1','NFP');
insert into IACOMPGRPMEMBERS (RECORD#, PARENTKEY, LHSACCTGRPKEY, LHSCONST, OPERATOR, RHSACCTGRPKEY, RHSCONST, PRECISION, DISPLAYAS, UOM, UOMALIGNMENT, INDUSTRYCODE) values (316,-2440,-2387,null,'D',null,'-2',2,'V',null,'1','NFP');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (677,'Net Asset Composition YOY','Net Asset Composition YOY',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','Stacked column','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (676,'DB_KPI.676','Pledges Receivable.676',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (675,'DB_KPI.675','Grants Receivable.675',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (674,'AR Composition Trend','AR Composition Trend',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','Stacked column','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (671,'Revenues and Expenditures - Budget vs Actual YTD','Revenues and Expenditures - Budget vs Actual YTD',null,'D','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','T');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (670,'Expenditure vs Budget','Expenditure Composition Trend',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','Stacked column','B','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (669,'Expenditure Composition YOY','Expenditure Composition YOY',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','Stacked column','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (668,'Revenue Composition YOY','Revenue Composition YOY',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','Stacked column','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (667,'DB_KPI.667','Net Surplus (Deficit).667',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (666,'DB_KPI.666','Cash and Equivalents.666',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (665,'DB_KPI.665','Unrestricted Liquid Net Assets.665',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (664,'DB_KPI.664','Expenditures.664',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (663,'DB_KPI.663','Revenue.663',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (662,'DB_KPI.662','Unrestricted Net Assets.662',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (657,'DB_KPI.657','Accounts Payable Balance.657',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (656,'DB_KPI.656','Accounts Receivable.656',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (655,'DB_KPI.655','Cash & Equivalents.655',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (654,'DB_KPI.654','Unrestricted Net Assets.654',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (653,'DB_KPI.653','Expenditures.653',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (652,'DB_KPI.652','Revenue.652',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (651,'Revenues and Expenditures - Budget vs Actual QTD','Revenues and Expenditures - Budget vs Actual QTD',null,'D','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','T');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (650,'Expenditure Composition Trend','Expenditure Composition Trend',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','Stacked column','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (649,'DB_KPI.649','General & Admin YTD.649',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (648,'DB_KPI.648','Personnel Expenses YTD.648',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (647,'DB_KPI.647','Expenditures YTD.647',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (646,'DB_KPI.646','General & Admin QTD.646',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (645,'DB_KPI.645','Personnel Expenses QTD.645',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (644,'DB_KPI.644','Expenditures QTD.644',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (643,'Revenue Composition Trend','Revenue Composition Trend',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','Stacked column','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (642,'Revenue Analysis','Revenue Analysis',null,'D','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','T');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (641,'Expenditure Analysis','Expenditure Analysis',null,'D','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','T');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (640,'DB_KPI.640','Net Income YTD.640',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (639,'DB_KPI.639','Direct Expenses YTD.639',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (638,'DB_KPI.638','Revenue YTD.638',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (637,'DB_KPI.637','Net Income QTD.637',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (636,'DB_KPI.636','Direct Expenses QTD.636',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (635,'DB_KPI.635','Revenue QTD.635',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (634,'Revenue and Net Income Trend - monthly','Revenue and Net Income Trend',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','GRAPH.line.1','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (633,'Top 10 Vendors','Top 10 Vendors',null,'C','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','F');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (632,'Top 10 Customers by Revenue','Top 10 Customers by Revenue',null,'C','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','F');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (631,'Top 10 AR Balances','Top 10 AR Balances',null,'C','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','F');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (629,'AR and Revenue Trend - Monthly','AR and Revenue Trend - Monthly',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','GRAPH.line.1','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (627,'DB_KPI.627','Bad Debt Ratio.627',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (625,'DB_KPI.625','Accounts Receivable, Net.625',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (624,'DB_KPI.624','Bad Debt Expense.624',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (623,'DB_KPI.623','Revenue.623',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (622,'Top 10 AP Balances','Top 10 AP Balances',null,'C','As of [As_of_Date_in_Word]',null,'a:16:{s:15:"SWITCHCOLSTABLE";s:1:"C";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"T";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";s:0:"";s:14:"SHOWDEPTIDNAME";s:0:"";s:15:"SHOWINACTIVELOC";s:0:"";s:13:"SHOWLOCIDNAME";s:0:"";s:13:"SHOWDIMIDNAME";s:0:"";s:12:"SHOWEXCHRATE";s:0:"";s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";}',null,'REPORT','P','P','glfinancial','NFP','F');
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (621,'DPO Trend - Monthly','DPO Trend - Monthly',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','GRAPH.line.1','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (620,'AP Trend - Monthly','AP Trend - Monthly',null,'C',null,null,'a:22:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:13:"SIMILARPRDCMP";s:1:"F";s:14:"NOPERIODHEADER";s:1:"F";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','GRAPH.line.1','P','N','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (619,'DB_KPI.619','Days Payables Outstanding.619',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (618,'DB_KPI.618','AP Turnover Ratio.618',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (617,'DB_KPI.617','Expenditures.617',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (616,'DB_KPI.616','Average AP Balance.616',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (615,'DB_KPI.615','Accrued Liabilities Balance.615',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTINFO (RECORD#, NAME, TITLE, TITLECOMMENT, ROUNDING, TITLE2, TITLE3, PARAMETERS, RUNTIMEPARAMS, RE_TYPE, SERIES, SECONSERIES, CATEGORY, INDUSTRYCODE, ACCOUNTATROW) values (614,'DB_KPI.614','Accounts Payable Balance.614',null,'C',null,null,'a:21:{s:15:"SWITCHCOLSTABLE";s:1:"R";s:4:"RFCD";s:1:"T";s:14:"HIDECFLTRTITLE";s:1:"F";s:10:"ZEROWTRANS";s:1:"F";s:14:"HIDEZEROBALCOL";s:1:"F";s:11:"DISPLAYDASH";s:1:"F";s:15:"DISPLAYFOOTERAS";s:1:"S";s:14:"DEPTLOCGRPSORT";s:1:"F";s:16:"SHOWINACTIVEDEPT";N;s:14:"SHOWDEPTIDNAME";N;s:15:"SHOWINACTIVELOC";N;s:13:"SHOWLOCIDNAME";N;s:13:"SHOWDIMIDNAME";N;s:12:"SHOWEXCHRATE";N;s:16:"DISPLOCASCOTITLE";s:1:"F";s:11:"EXCLUDESUBS";s:0:"";s:14:"NOPERIODHEADER";s:1:"T";s:13:"WFSECONSERIES";N;s:10:"WFEXPANDBY";N;s:11:"WFDIMENSION";N;s:10:"WFDIMSTRUC";N;}','dept:location:repbook:asofdate','DB_KPI','P','A','glgraph','NFP',null);
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (677,0,-2169,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (677,1,-2170,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (677,4,-2168,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (677,3,-2167,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (677,2,-2171,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (676,0,-1750,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (675,0,-1419,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (674,0,-1236,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (674,3,-1750,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (674,2,-1419,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (674,1,-1230,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (671,0,-2430,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,0,-1435,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,1,-1759,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,2,-1433,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,3,-1381,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,9,-1752,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,5,-1740,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,6,-1201,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,7,-1747,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,8,-1749,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (670,4,-1736,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,0,-1435,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,1,-1759,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,2,-1433,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,3,-1381,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,9,-1752,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,5,-1740,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,6,-1201,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,7,-1747,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,8,-1749,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (669,4,-1736,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (668,1,-1739,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (668,2,-2194,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (668,0,-2191,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (668,4,-2193,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (668,5,-2192,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (668,3,-2189,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (667,0,-2430,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (666,0,-1333,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (665,0,-2424,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (664,0,-1438,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (663,0,-2206,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (662,0,-2423,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (657,0,-1237,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (656,0,-1236,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (655,0,-1333,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (654,0,-1311,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (653,0,-1438,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (652,0,-2206,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (651,0,-2430,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,0,-1435,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,1,-1759,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,2,-1433,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,3,-1381,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,9,-1752,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,5,-1740,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,6,-1201,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,7,-1747,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,8,-1749,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (650,4,-1736,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (649,0,-1201,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (648,0,-1759,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (647,0,-1438,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (646,0,-1201,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (645,0,-1759,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (644,0,-1438,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (643,0,-2191,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (643,1,-1739,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (643,5,-2192,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (643,3,-2189,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (643,4,-2193,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (643,2,-2194,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (642,0,-2206,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (641,0,-1438,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (640,0,-2207,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (639,0,-1435,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (638,0,-2206,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (637,0,-2207,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (636,0,-1435,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (635,0,-2206,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (634,1,-2207,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (634,0,-2206,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (633,0,-2411,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (632,0,-2420,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (631,0,-2420,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (629,1,-1249,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (629,0,-1236,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (627,0,-2414,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (625,0,-1293,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (624,0,-1371,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (623,0,-2206,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (622,0,-2411,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (621,0,-2413,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (620,0,-1237,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (619,0,-2413,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (618,0,-2393,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (617,0,-1438,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (616,0,-2390,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (615,0,-1344,'NFP');
insert into IAREPORTGROUPS (REPORT#, SORTORD, ACCTGRP#, INDUSTRYCODE) values (614,0,-1237,'NFP');
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (677,'/-2169',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (677,'/-2170',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (677,'/-2168',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (677,'/-2167',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (677,'/-2171',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (676,'/-1750',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (675,'/-1419',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (674,'/-1236',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (674,'/-1750',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (674,'/-1419',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (674,'/-1230',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2189',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2193',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2192',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'F',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1435',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1759',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1759/-1383',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1759/-1283',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1759/-1436',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1759/-1437',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1433',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1381',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1736',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1740',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1369',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1261',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1371',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1372',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1373',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1735',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1431',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1263',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1376',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1217',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1377',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1264',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1378',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1273',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1247',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-2174',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1279',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1356',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1266',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1282',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1744',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1379',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1269',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1270',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1389',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1441',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1272',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1274',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1248',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1386',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1387',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1430',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1242',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1755',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1388',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1196',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1730',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1201/-1743',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1747',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1749',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-1438/-1752',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'F',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'F',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2191',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2191/-1426',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2191/-1733',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2191/-2186',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2191/-1734',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-1739',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-1739/-1738',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-1739/-1424',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2194',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2194/-1425',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2194/-1756',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2194/-1288',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2194/-1332',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (671,'/-2430/-2206/-2194/-1364',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1435',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1759',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1433',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1381',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1752',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1740',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1201',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1747',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1749',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (670,'/-1736',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1435',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1759',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1433',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1381',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1752',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1740',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1201',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1747',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1749',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (669,'/-1736',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (668,'/-2191',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (668,'/-1739',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (668,'/-2192',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (668,'/-2189',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (668,'/-2193',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (668,'/-2194',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (667,'/-2430',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (666,'/-1333',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (665,'/-2424',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (664,'/-1438',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (663,'/-2206',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (662,'/-2423',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (657,'/-1237',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (656,'/-1236',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (655,'/-1333',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (654,'/-1311',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (653,'/-1438',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (652,'/-2206',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-1739/-1738',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-1739/-1424',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2194',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2194/-1425',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2194/-1756',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2194/-1288',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2194/-1332',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2194/-1364',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2189',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2193',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2192',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438',null,null,null,null,null,null,null,'2',null,null,null,null,null,null,null,null,null,null,'NFP',null,'F',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1435',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1759',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1759/-1383',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1759/-1283',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1759/-1436',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1759/-1437',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1433',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1381',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1736',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1740',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1369',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1261',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1371',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1372',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1373',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1735',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1431',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1263',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1376',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1217',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1377',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1264',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1378',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1273',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1247',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-2174',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1279',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1356',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1266',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1282',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1744',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1379',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1269',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1270',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1389',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1441',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1272',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1274',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1248',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1386',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1387',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1430',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1242',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1755',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1388',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1196',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1730',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1201/-1743',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1747',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1749',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-1438/-1752',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430',null,null,null,null,null,null,null,'3',null,null,null,null,null,null,null,null,null,null,'NFP',null,'F',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206',null,null,null,null,null,null,null,'2',null,null,null,null,null,null,null,null,null,null,'NFP',null,'F',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2191',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2191/-1426',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2191/-1733',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2191/-2186',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-2191/-1734',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (651,'/-2430/-2206/-1739',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1435',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1759',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1433',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1381',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1752',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1740',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1201',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1747',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1749',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (650,'/-1736',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (649,'/-1201',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (648,'/-1759',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (647,'/-1438',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (646,'/-1201',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (645,'/-1759',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (644,'/-1438',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (643,'/-2191',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (643,'/-1739',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (643,'/-2192',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (643,'/-2189',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (643,'/-2193',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (643,'/-2194',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206',null,null,null,null,null,null,null,'3',null,null,null,null,null,null,null,null,-2206,null,'NFP',null,'F',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2192',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2191/-1426',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2191/-1733',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2191/-2186',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2191/-1734',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-1739',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-1739/-1738',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-1739/-1424',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2194',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2194/-1425',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2194/-1756',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2194/-1288',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2194/-1332',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2194/-1364',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2189',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2193',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,'D','NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (642,'/-2206/-2191',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-2206,null,'NFP',null,'T',null,'F',null,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1736',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1435',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1747',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1749',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1752',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1759',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,null,'NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1759/-1283',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1759/-1383',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1759/-1436',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1759/-1437',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438',null,null,null,null,null,null,null,'3',null,null,null,null,null,null,null,null,null,'D','NFP','F','F',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,null,'NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1196',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1217',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1242',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1247',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1248',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1261',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1263',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1264',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1266',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1269',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1270',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1272',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1273',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1274',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1279',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1282',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1356',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1369',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1371',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1372',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1373',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1376',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1377',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1378',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1379',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1386',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1387',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1388',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1389',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1430',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1431',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1441',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1730',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1735',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1743',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1744',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-1755',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1201/-2174',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1381',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1433',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (641,'/-1438/-1740',null,null,null,null,null,null,null,'1',null,null,null,null,null,null,null,null,-1438,'D','NFP','F','T',null,'F',null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (640,'/-2207',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (639,'/-1435',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (638,'/-2206',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (637,'/-2207',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (636,'/-1435',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (635,'/-2206',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (634,'/-2207',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (634,'/-2206',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (633,'/-2411',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP','F','F',null,'F',10,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (632,'/-2420',null,null,null,'T',null,null,null,'3',null,null,null,null,null,null,null,null,null,null,'NFP','F','F',null,'F',10,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (631,'/-2420',null,'T',null,'T',null,null,null,'1',null,null,null,null,null,null,null,null,null,null,'NFP','F','F',null,'F',10,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (629,'/-1249',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (629,'/-1236',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (627,'/-2414',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (625,'/-1293',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (624,'/-1371',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (623,'/-2206',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (622,'/-2411',null,'T',null,'T',null,null,null,'1',null,null,null,null,null,null,null,null,null,null,'NFP',null,'F',null,'F',10,null,2);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (621,'/-2413',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (620,'/-1237',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (619,'/-2413',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (618,'/-2393',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (617,'/-1438',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (616,'/-2390',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (615,'/-1344',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTROWS (REPORT#, PATH, SUMMARY, SKIPT, ALWAYS, SKIPH, RATIOBASE, PGBREAK, EMPTYROWS, UNDERLINE_TYPE, TOTALS_BOLD, EMPTY_ROWS_BEFORE, SHOW_CURRENCY, ATTACH_CURRENCY, CENTER_GROUP_HEADERS, SKIPTOTALSLINE, OPERANDGRPKEY, OPERATION, RATIOBASEGRPKEY, ROWAMOUNTTYPE, INDUSTRYCODE, CUMULATIVEBALANCE, EXPANDBY, ROWACCTGRPKEY, EXPANDMEMBERS, TOP, BOTTOM, TOPBOTCOL) values (614,'/-1237',null,null,'T',null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'NFP',null,'T',null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (677,0,null,14,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (676,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (675,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (674,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-11,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (671,2,null,15,null,null,'T','Budget','No_Header',null,'b',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (671,4,null,15,null,null,'T','Budget % Var','No_Header',null,'nv',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (671,3,null,15,null,null,'T','Budget Diff','No_Header',null,'nV',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,'TI',null,0,'green','C','NC',null,'arrow','green','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (671,1,null,15,null,null,'T','Actual','No_Header',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (671,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (670,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (669,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (668,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (667,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (666,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (665,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (664,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (663,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (662,0,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (657,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (656,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (655,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (654,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (653,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (652,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (651,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (651,1,null,9,null,null,'T','Actual','No_Header',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (651,4,null,9,null,null,'T','Budget % Var','No_Header',null,'nv',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (651,3,null,9,null,null,'T','Budget Diff','No_Header',null,'nV',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,'CS',null,0,'green','C','red',null,'arrow','green','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (651,2,null,9,null,null,'T','Budget','No_Header',null,'b',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (650,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-11,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (649,1,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (649,0,null,15,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (648,1,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (648,0,null,15,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (647,1,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (647,0,null,15,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (646,1,null,9,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (646,0,null,9,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (645,1,null,9,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (645,0,null,9,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (644,1,null,9,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (644,0,null,9,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (643,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-11,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (642,1,null,15,null,null,'T','YTD Actual','No_Header',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (642,2,null,15,null,null,'T','% Revenue','No_Header',null,'p',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,'CS',null,0,'green','C','NC',null,'arrow','green','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (642,3,null,15,null,null,'T','YTD Budget','No_Header',null,'b',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (642,6,null,15,null,null,'T','PYTD % Var','No_Header',null,'d',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,'TI',null,0,'green','C','NC',null,'arrow','green','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (642,5,null,17,null,null,'T','PYTD Actual','No_Header',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (642,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (642,4,null,15,null,null,'T','YTD Budget % Var','No_Header',null,'v',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,'TI',null,0,'green','C','NC',null,'arrow','green','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (641,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (641,1,null,15,null,null,'T','YTD Actual','No_Header',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (641,2,null,15,null,null,'T','% Expenditures','No_Header',null,'p',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,'CS',null,0,'yellow','C','NC',null,'arrow','green','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (641,6,null,15,null,null,'T','PYTD % Var','No_Header',null,'d',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,'TI',null,0,'green','C','NC',null,'arrow','red','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (641,4,null,15,null,null,'T','YTD Budget % Var','No_Header',null,'v',null,'A  ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,'TI',null,0,'green','C','NC',null,'arrow','red','N');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (641,5,null,17,null,null,'T','PYTD Actual','No_Header',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (641,3,null,15,null,null,'T','YTD Budget','No_Header',null,'b',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'P','NFP','R','T','D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (640,1,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (640,0,null,15,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (639,0,null,15,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (639,1,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (638,1,null,15,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (638,0,null,15,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (637,1,null,9,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (637,0,null,9,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (636,1,null,9,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (636,0,null,9,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (635,1,null,9,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (635,0,null,9,null,null,'T','Period_Header_1',null,null,'b',null,null,2,null,0,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (634,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-11,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (633,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (633,2,null,45,null,null,'T','12-Month Trend','No_Header',null,'x',null,'NC ',2,null,0,'####','F',0,null,null,null,null,null,null,null,'D','NFP','R','PRDM','D',-1438,null,'SP',null,0,'green','C','NC',null,'arrow','green','Y');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (633,1,null,15.51,null,null,'T','YTD','Period_Header_2',null,'x',null,'NC ',2,null,0,'####','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',-1438,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (632,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (632,2,null,45,null,null,'T','No_Header','12-Month Trend',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','PRDM','D',-2206,null,'SP',null,0,'green','C','NC',null,'arrow','green','Y');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (632,1,null,15.51,null,null,'T','Date_Header_1','Date_Header_2',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',-2206,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (631,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (631,2,null,45,null,null,'T','No_Header','12-Month Trend',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','PRDM','D',-1236,null,'SP',null,0,'green','C','NC',null,'arrow','green','Y');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (631,1,null,2,null,null,'T','Balance','Period_Header_2',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',-1236,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (629,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-11,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'D',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (627,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (625,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (624,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (623,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (622,0,null,2,null,null,'T','Period_Header_1','Period_Header_2',null,'DSC',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R',null,'D',null,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (622,2,null,45,null,null,'T','No_Header','12-Month Trend',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','PRDM','D',-1237,null,'SP',null,0,'green','C','NC',null,'arrow','green','Y');
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (622,1,null,2,null,null,'T','Balance','Period_Header_2',null,'x',null,'NC ',2,null,0,'Helvetica#10###black','F',0,null,null,null,null,null,null,null,'D','NFP','R','T','D',-1237,null,null,null,0,'green','C','NC',null,'arrow','green',null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (621,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-11,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (620,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-11,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'Y',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (619,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (618,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (617,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (616,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (615,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTCOLS (REPORT#, SORTORD, PERIOD#, DATETYPE, HEADERCOMMENT, SUBCOLUMNS, COMPAREBY, COLHDR1, COLHDR2, BUDGETKEYS, TYPE, BUDGETKEY, SHOWAS, PRECISION, TITLE, PERIODOFFSET, COLFONT, COLPGBREAK, COLWIDTH, EXPOF, SUMMARYOP, SUMMARYCOLS, COLCOMPVALUE, SUMMARYSTR, COLHIDE, COMPONCOL, COLAMOUNTTYPE, INDUSTRYCODE, RIGHTLEFTHIDE, EXPANDBY, PERIODOFFSETBY, COLACCTGRPKEY, COLNOTATIONVALUE, INDICATORTYPE, ENABLECOLORSCALE, THRESHOLD, ABOVETHRESHOLDCOLOR, THRESHOLDTYPE, BELOWTHRESHOLDCOLOR, CONDITIONWITHMESSAGE, TRENDTYPE, TRENDINCREASECLR, SHOWSPARKLINE) values (614,0,null,2,null,null,'T','Period_Header_1',null,null,'a',null,null,2,null,-1,'####','F',0,null,null,null,null,null,null,null,null,'NFP','R',null,'M',null,null,null,null,null,null,null,null,null,null,null,null);
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (677,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (676,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (675,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (674,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (671,'F','L','Helvetica#14###black','Helvetica#14###black','Helvetica#14###black','Helvetica#10###black',null,null,'Helvetica#10###black','F','F','F','P','####black','Helvetica#10###black','P','L','L','Helvetica#14###black',null,'T',null,'S',null,null,null,null,null,null,'NFP','Helvetica#10###black',null,'Helvetica#10###black',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (670,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (669,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (668,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (667,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (666,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (665,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (664,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (663,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (662,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (657,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (656,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (655,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (654,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (653,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (652,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (651,'F','L','Helvetica#14###black','Helvetica#14###black','Helvetica#14###black','Helvetica#10###black',null,null,'Helvetica#10###black','F','F','F','P','####black','Helvetica#10###black','P','L','L','Helvetica#14###black',null,'T',null,'S',null,null,null,null,null,null,'NFP','Helvetica#10###black',null,'Helvetica#10###black',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (650,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (649,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (648,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (647,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (646,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (645,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (644,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (643,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (642,'F','L','Helvetica#14###black','Helvetica#14###black','Helvetica#14###black','Helvetica#10###black',null,null,'Helvetica#10###black','F','F','F','P','####black','Helvetica#10###black','P','L','L','Helvetica#14###black',null,'T',null,'S',null,null,null,null,null,null,'NFP','Helvetica#10###black',null,'Helvetica#10###black',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (641,'F','L','Helvetica#14###black','Helvetica#14###black','Helvetica#14###black','Helvetica#10###black',null,null,'Helvetica#10###black','F','F','F','P','####black','Helvetica#10###black','P','L','L','Helvetica#14###black',null,'T',null,'S',null,null,null,null,null,null,'NFP','Helvetica#10###black',null,'Helvetica#10###black',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (640,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (639,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (638,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (637,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (636,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (635,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (634,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (633,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,'S',null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (632,'F','L','Helvetica#14###black','Helvetica#14###black','Helvetica#14###black','Helvetica#10###black',null,null,'Helvetica#10###black','F','F','F','P','####black','Helvetica#10###black','P','L','L','Helvetica#14###black',null,'T',null,'S',null,null,null,null,null,null,'NFP','Helvetica#10###black',null,'Helvetica#10###black',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (631,'F','L','Helvetica#14###black','Helvetica#14###black','Helvetica#14###black','Helvetica#10###black',null,null,'Helvetica#10###black','F','F','F','P','####black','Helvetica#10###black','P','L','L','Helvetica#14###black',null,'T',null,'S',null,null,null,null,null,null,'NFP','Helvetica#10###black',null,'Helvetica#10###black',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (629,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (627,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (625,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (624,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (623,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (622,'F','L','Helvetica#14###black','Helvetica#14###black','Helvetica#14###black','Helvetica#10###black',null,null,'Helvetica#10###black','F','F','F','P','####black','Helvetica#10###black','P','L','L','Helvetica#14###black',null,'T',null,'S',null,null,null,null,null,null,'NFP','Helvetica#10###black',null,'Helvetica#10###black',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (621,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (620,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (619,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (618,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (617,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (616,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (615,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IAREPORTFORMAT (REPORT#, PRINTLOGO, LOGOALIGN, CNYNAMEFONT, TITLE1FONT, TITLE2FONT, BODYFONT, FOOTERTEXT, FOOTERALLPAGES, FOOTERFONT, PRINTPGNUMBER, PRINTREPORTDATE, PRINTREPORTTIME, NEGNUMBERSTYLE, NEGNUMBERFONT, HDRTOTALFONT, ORIENTATION, FOOTER_ALIGNMENT, ALIGN_CURRENCY, TITLE3FONT, SHOWGLACCOUNT, RFCD, PAGESIZE, DEFINEDBY, TOPMARGIN, LEFTMARGIN, RIGHTMARGIN, BOTTOMMARGIN, SCALE, ZEROACTUAL, INDUSTRYCODE, SUBGRPHDRFONT, APPLYREPFILTERONLY, COLHDRFONT, HEADER_ALIGNMENT, FITTOPAGE, CUSTHDRFONT) values (614,null,null,'####','####','####','####',null,null,'####','F','F','F',null,'####','####',null,null,null,'####',null,'T',null,null,null,null,null,null,null,null,'NFP','####',null,'####',null,null,'####');
insert into IADASHBOARD (RECORD#, TITLE, DESCRIPTION, COL1TYPE, COL2TYPE, COL3TYPE, DEFAULTDASHBOARD, APP, THEME, TYPE, INDUSTRYCODE, USERTYPE, CUSTOMBACKGROUNDCOLOR, CUSTOMFONTCOLOR, FILTERTYPES, SHOWASOFDATE) values (-29,'System_AP Manager','AP Manager Dashboard','N','W','N','F','A','ND','S','NFP','A','E5E5E5','696969','a:0:{}','F');
insert into IADASHBOARD (RECORD#, TITLE, DESCRIPTION, COL1TYPE, COL2TYPE, COL3TYPE, DEFAULTDASHBOARD, APP, THEME, TYPE, INDUSTRYCODE, USERTYPE, CUSTOMBACKGROUNDCOLOR, CUSTOMFONTCOLOR, FILTERTYPES, SHOWASOFDATE) values (-35,'System_AR Manager','AR Manager Dashboard','N','W','N','F','A','ND','S','NFP','A','E5E5E5','696969','a:0:{}','F');
insert into IADASHBOARD (RECORD#, TITLE, DESCRIPTION, COL1TYPE, COL2TYPE, COL3TYPE, DEFAULTDASHBOARD, APP, THEME, TYPE, INDUSTRYCODE, USERTYPE, CUSTOMBACKGROUNDCOLOR, CUSTOMFONTCOLOR, FILTERTYPES, SHOWASOFDATE) values (-36,'System_Dept Mgr - Revenue','Dept Mgr Dashboard - Revenue Generating','N','W','N','F','A','ND','S','NFP','A','E5E5E5','696969','a:0:{}','F');
insert into IADASHBOARD (RECORD#, TITLE, DESCRIPTION, COL1TYPE, COL2TYPE, COL3TYPE, DEFAULTDASHBOARD, APP, THEME, TYPE, INDUSTRYCODE, USERTYPE, CUSTOMBACKGROUNDCOLOR, CUSTOMFONTCOLOR, FILTERTYPES, SHOWASOFDATE) values (-37,'System_Dept Mgr - Support','Dept Mgr Dashboard - Support Function','N','W','N','F','A','ND','S','NFP','A','E5E5E5','696969','a:0:{}','F');
insert into IADASHBOARD (RECORD#, TITLE, DESCRIPTION, COL1TYPE, COL2TYPE, COL3TYPE, DEFAULTDASHBOARD, APP, THEME, TYPE, INDUSTRYCODE, USERTYPE, CUSTOMBACKGROUNDCOLOR, CUSTOMFONTCOLOR, FILTERTYPES, SHOWASOFDATE) values (-38,'System_Controller','Controller Dashboard','N','W','N','F','A','ND','S','NFP','A','E5E5E5','696969','a:0:{}','F');
insert into IADASHBOARD (RECORD#, TITLE, DESCRIPTION, COL1TYPE, COL2TYPE, COL3TYPE, DEFAULTDASHBOARD, APP, THEME, TYPE, INDUSTRYCODE, USERTYPE, CUSTOMBACKGROUNDCOLOR, CUSTOMFONTCOLOR, FILTERTYPES, SHOWASOFDATE) values (-39,'System_CFO','CFO Dashboard','N','W','N','F','A','ND','S','NFP','A','E5E5E5','696969','a:0:{}','F');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3084,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',1,3,1,1,'Net Asset Composition','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"677";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-39,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3083,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',2,2,1,1,'AR Composition Trend','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"674";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-35,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3082,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',2,3,1,1,'Revenue Composition Trend','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"643";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-35,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3079,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',2,3,1,1,'Expenditure Composition Trend','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"650";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-37,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3076,'Reporter Bean','DashboardReporter.cls',3,1,1,1,'Top 10 AP Balances','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"622";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-29,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3074,'Reporter Bean','DashboardReporter.cls',2,2,1,1,'Revenues and Expenditures - Budget vs Actual YTD','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"671";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-39,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3073,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',3,2,1,1,'Expenditure vs Budget','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"670";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"300";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-37,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3071,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',3,2,1,1,'Expenditure Composition','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"669";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-39,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3070,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',3,1,1,1,'Revenue Composition','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"668";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-39,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3057,'Reporter Bean','DashboardReporter.cls',2,3,1,1,'Expenditure Analysis','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"641";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3056,'Reporter Bean','DashboardReporter.cls',2,2,1,1,'Revenue Analysis','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"642";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3054,'Kpi Strip Bean','KpiBean.cls',2,1,1,1,'Performance cards','KpiBean',null,'a:4:{s:6:"entity";s:7:"KpiBean";s:9:"alignment";s:6:"center";i:0;s:9:"showframe";i:1;s:5:"false";}','Y',null,'T','F',-39,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3053,'CustomNavBean','CustomNavBean.cls',1,2,1,1,'Reports','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"150";s:7:"ENTRIES";a:4:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Reports#~#page.phtml?.op=1499&.ifmod=reportcenter";s:8:"MENUNAME";s:14:"Reports Center";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:46:"General Ledger#~#lister.phtml?.op=29&.ifmod=gl";s:8:"MENUNAME";s:17:"Financial Reports";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:46:"General Ledger#~#lister.phtml?.op=30&.ifmod=gl";s:8:"MENUNAME";s:16:"Financial Graphs";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Budgets#~#reporteditor.phtml?.op=1488&.ifmod=budg";s:8:"MENUNAME";s:13:"Budget Report";}}}','Y',null,'F','F',-39,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3052,'CustomNavBean','CustomNavBean.cls',1,1,1,1,'My Tasks','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"200";s:7:"ENTRIES";a:7:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:41:"Company#~#lister.phtml?.op=1576&.ifmod=co";s:8:"MENUNAME";s:16:"Open Assignments";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"General Ledger#~#lister.phtml?.op=1162&.ifmod=gl";s:8:"MENUNAME";s:11:"Approve JEs";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:44:"Purchasing#~#lister.phtml?.op=4470&.ifmod=po";s:8:"MENUNAME";s:11:"Approve POs";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:50:"Accounts Payable#~#lister.phtml?.op=4568&.ifmod=ap";s:8:"MENUNAME";s:13:"Approve Bills";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Accounts Payable#~#lister.phtml?.op=122&.ifmod=ap";s:8:"MENUNAME";s:16:"Approve Payments";}i:5;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=357&.ifmod=ee";s:8:"MENUNAME";s:16:"Approve Expenses";}i:6;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=409&.ifmod=ee";s:8:"MENUNAME";s:22:"Approve Reimbursements";}}}','Y',null,'F','F',-39,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3048,'Kpi Strip Bean','KpiBean.cls',2,1,1,1,'Performance cards','KpiBean',null,'a:4:{s:6:"entity";s:7:"KpiBean";s:9:"alignment";s:6:"center";i:0;s:9:"showframe";i:1;s:5:"false";}','Y',null,'T','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3047,'Closed Thru Summary Bean','ClosedThruSummaryBean.cls',1,3,1,1,'Closed Thru Summary','ClosedThruSummaryBean',10,'a:2:{s:10:"compheight";s:3:"200";s:8:"viewType";s:1:"S";}','Y',null,'F','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3046,'Reporter Bean','DashboardReporter.cls',3,2,1,1,'Top 10 AP Balances','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"622";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3045,'Reporter Bean','DashboardReporter.cls',3,1,1,1,'Top 10 AR Balances','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"631";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3044,'CustomNavBean','CustomNavBean.cls',1,2,1,1,'Reports','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"200";s:7:"ENTRIES";a:8:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:46:"General Ledger#~#lister.phtml?.op=29&.ifmod=gl";s:8:"MENUNAME";s:17:"Financial Reports";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:54:"General Ledger#~#reporteditor.phtml?.op=1488&.ifmod=gl";s:8:"MENUNAME";s:13:"Budget Report";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:53:"General Ledger#~#reporteditor.phtml?.op=123&.ifmod=gl";s:8:"MENUNAME";s:25:"Comparative Trial Balance";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:54:"General Ledger#~#reporteditor.phtml?.op=3266&.ifmod=gl";s:8:"MENUNAME";s:25:"Dimension Balances Report";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:52:"General Ledger#~#reporteditor.phtml?.op=38&.ifmod=gl";s:8:"MENUNAME";s:15:"Journals Report";}i:5;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:52:"General Ledger#~#reporteditor.phtml?.op=32&.ifmod=gl";s:8:"MENUNAME";s:21:"General Ledger Detail";}i:6;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:54:"Accounts Payable#~#reporteditor.phtml?.op=44&.ifmod=ap";s:8:"MENUNAME";s:9:"AP Ledger";}i:7;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:57:"Accounts Receivable#~#reporteditor.phtml?.op=63&.ifmod=ar";s:8:"MENUNAME";s:9:"AR Ledger";}}}','Y',null,'F','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3043,'CustomNavBean','CustomNavBean.cls',1,1,1,1,'My Tasks','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"200";s:7:"ENTRIES";a:8:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:16:"Open Assignments";s:6:"MENUOP";s:41:"Company#~#lister.phtml?.op=1576&.ifmod=co";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:17:"Manage Checklists";s:6:"MENUOP";s:41:"Company#~#lister.phtml?.op=1545&.ifmod=co";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:11:"Approve JEs";s:6:"MENUOP";s:48:"General Ledger#~#lister.phtml?.op=1162&.ifmod=gl";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:11:"Approve POs";s:6:"MENUOP";s:44:"Purchasing#~#lister.phtml?.op=4470&.ifmod=po";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:13:"Approve Bills";s:6:"MENUOP";s:50:"Accounts Payable#~#lister.phtml?.op=4568&.ifmod=ap";}i:5;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Accounts Payable#~#lister.phtml?.op=122&.ifmod=ap";s:8:"MENUNAME";s:16:"Approve Payments";}i:6;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=357&.ifmod=ee";s:8:"MENUNAME";s:16:"Approve Expenses";}i:7;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=409&.ifmod=ee";s:8:"MENUNAME";s:22:"Approve Reimbursements";}}}','Y',null,'F','F',-38,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3041,'Reporter Bean','DashboardReporter.cls',2,2,1,1,'Expenditure Analysis','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"641";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-37,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3040,'Reporter Bean','DashboardReporter.cls',3,1,1,1,'Top 10 Vendors','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"633";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-37,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3039,'Kpi Strip Bean','KpiBean.cls',2,1,1,1,'Performance cards','KpiBean',null,'a:4:{s:6:"entity";s:7:"KpiBean";s:9:"alignment";s:6:"center";i:0;s:9:"showframe";i:1;s:5:"false";}','Y',null,'T','F',-37,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3038,'CustomNavBean','CustomNavBean.cls',1,2,1,1,'Reports','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"175";s:7:"ENTRIES";a:6:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Time & Expenses#~#lister.phtml?.op=4888&.ifmod=ee";s:8:"MENUNAME";s:15:"My Timeshsheets";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=372&.ifmod=ee";s:8:"MENUNAME";s:11:"My Expenses";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Time & Expenses#~#lister.phtml?.op=4860&.ifmod=ee";s:8:"MENUNAME";s:16:"Staff Timesheets";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=322&.ifmod=ee";s:8:"MENUNAME";s:14:"Staff Expenses";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:54:"Time & Expenses#~#reporteditor.phtml?.op=364&.ifmod=ee";s:8:"MENUNAME";s:13:"Employee List";}i:5;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Budgets#~#reporteditor.phtml?.op=1488&.ifmod=budg";s:8:"MENUNAME";s:13:"Budget Report";}}}','Y',null,'F','F',-37,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3037,'CustomNavBean','CustomNavBean.cls',1,1,1,1,'My Tasks','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"175";s:7:"ENTRIES";a:5:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:41:"Company#~#lister.phtml?.op=1576&.ifmod=co";s:8:"MENUNAME";s:16:"Open Assignments";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:44:"Purchasing#~#lister.phtml?.op=4470&.ifmod=po";s:8:"MENUNAME";s:11:"Approve POs";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:16:"Approve Expenses";s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=357&.ifmod=ee";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:22:"Approve Reimbursements";s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=409&.ifmod=ee";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:18:"Approve Timesheets";s:6:"MENUOP";s:49:"Time & Expenses#~#lister.phtml?.op=4851&.ifmod=ee";}}}','Y',null,'F','F',-37,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3036,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',2,3,1,1,'Revenue Composition Trend','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"643";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:14:"Stacked column";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"B";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-36,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3035,'Reporter Bean','DashboardReporter.cls',3,2,1,1,'Top 10 AR Balances','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"631";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-36,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3034,'Reporter Bean','DashboardReporter.cls',2,2,1,1,'Revenue Analysis','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"642";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-36,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3032,'Reporter Bean','DashboardReporter.cls',3,1,1,1,'Top 10 Customers by Revenue','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"632";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-36,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3031,'Kpi Strip Bean','KpiBean.cls',2,1,1,1,'Performance cards','KpiBean',null,'a:4:{s:6:"entity";s:7:"KpiBean";s:9:"alignment";s:6:"center";i:0;s:9:"showframe";i:1;s:5:"false";}','Y',null,'T','F',-36,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3030,'CustomNavBean','CustomNavBean.cls',1,2,1,1,'Reports','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"150";s:7:"ENTRIES";a:5:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Time & Expenses#~#lister.phtml?.op=4888&.ifmod=ee";s:8:"MENUNAME";s:13:"My Timesheets";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=372&.ifmod=ee";s:8:"MENUNAME";s:11:"My Expenses";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:49:"Time & Expenses#~#lister.phtml?.op=4860&.ifmod=ee";s:8:"MENUNAME";s:16:"Staff Timesheets";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:58:"Accounts Receivable#~#reporteditor.phtml?.op=292&.ifmod=ar";s:8:"MENUNAME";s:13:"Customer List";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:44:"Order Entry#~#lister.phtml?.op=953&.ifmod=so";s:8:"MENUNAME";s:10:"Price List";}}}','Y',null,'F','F',-36,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3029,'CustomNavBean','CustomNavBean.cls',1,1,1,1,'My Tasks','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"175";s:7:"ENTRIES";a:5:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:16:"Open Assignments";s:6:"MENUOP";s:41:"Company#~#lister.phtml?.op=1576&.ifmod=co";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:11:"Approve POs";s:6:"MENUOP";s:44:"Purchasing#~#lister.phtml?.op=4470&.ifmod=po";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:16:"Approve Expenses";s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=357&.ifmod=ee";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:22:"Approve Reimbursements";s:6:"MENUOP";s:48:"Time & Expenses#~#lister.phtml?.op=409&.ifmod=ee";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:38:"Time & Expenses#~#lister.phtml?.op=4851&.ifmod=ee";s:8:"MENUNAME";s:18:"Approve Timesheets";}}}','Y',null,'F','F',-36,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3028,'Reporter Bean','DashboardReporter.cls',3,2,1,1,'Top 10 Customers by Revenue','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"632";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-35,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3027,'Reporter Bean','DashboardReporter.cls',3,1,1,1,'Top 10 AR Balances','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"631";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-35,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3024,'Kpi Strip Bean','KpiBean.cls',2,1,1,1,'Performance cards','KpiBean',null,'a:4:{s:6:"entity";s:7:"KpiBean";s:9:"alignment";s:6:"center";i:0;s:9:"showframe";i:1;s:5:"false";}','Y',null,'T','F',-35,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3023,'CustomNavBean','CustomNavBean.cls',1,2,1,1,'Reports','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"150";s:7:"ENTRIES";a:5:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:14:"Customer Aging";s:6:"MENUOP";s:57:"Accounts Receivable#~#reporteditor.phtml?.op=66&.ifmod=ar";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:9:"AR Ledger";s:6:"MENUOP";s:57:"Accounts Receivable#~#reporteditor.phtml?.op=63&.ifmod=ar";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:59:"Accounts Receivable#~#reporteditor.phtml?.op=1011&.ifmod=ar";s:8:"MENUNAME";s:17:"Invoices Analysis";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:14:"Sales Analysis";s:6:"MENUOP";s:50:"Order Entry#~#reporteditor.phtml?.op=750&.ifmod=so";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:58:"Accounts Receivable#~#reporteditor.phtml?.op=292&.ifmod=ar";s:8:"MENUNAME";s:13:"Customer List";}}}','Y',null,'F','F',-35,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3022,'CustomNavBean','CustomNavBean.cls',1,1,1,1,'My Tasks','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"175";s:7:"ENTRIES";a:5:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:16:"Open Assignments";s:6:"MENUOP";s:41:"Company#~#lister.phtml?.op=1576&.ifmod=co";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:6:"MENUOP";s:81:"Accounts Receivable#~#lister.phtml?.recordtype=ri&.it=arinvoice&.op=340&.ifmod=ar";s:8:"MENUNAME";s:11:"AR Invoices";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:15:"Receive Payment";s:6:"MENUOP";s:53:"Accounts Receivable#~#lister.phtml?.op=3606&.ifmod=ar";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:12:"Make Deposit";s:6:"MENUOP";s:49:"Accounts Receivable#~#lister.phtml?.op=1240&.ifmod=ar";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:16:"Manage Customers";s:6:"MENUOP";s:51:"Accounts Receivable#~#lister.phtml?.op=68&.ifmod=ar";}}}','Y',null,'F','F',-35,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3020,'Reporter Bean','DashboardReporter.cls',3,2,1,1,'Cash Balances','DashboardReporter',-1,'a:6:{s:6:"entity";b:1;s:8:"noOfRows";i:-1;s:4:"type";s:1:"I";s:15:"finreportentity";s:3:"319";s:10:"compheight";s:1:"0";s:6:"mgname";s:0:"";}','Y',null,'F','F',-29,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3019,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',2,3,1,1,'DPO Trend - Monthly','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"621";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:4:"Line";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"N";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-29,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3018,'DBFlashCompRenderer Bean','DBFlashCompRenderer.cls',2,2,1,1,'AP Trend - Monthly','DBFlashCompRenderer',10,'a:8:{s:6:"entity";s:3:"620";s:8:"noOfRows";s:2:"10";s:4:"type";s:1:"I";s:9:"graphType";s:4:"Line";s:10:"compheight";s:3:"200";s:7:"xOrient";s:1:"W";s:6:"legend";s:1:"N";s:8:"fontsize";s:2:"10";}','Y',null,'F','F',-29,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3017,'Kpi Strip Bean','KpiBean.cls',2,1,1,1,'Performance cards','KpiBean',null,'a:4:{s:6:"entity";s:7:"KpiBean";s:9:"alignment";s:6:"center";i:0;s:9:"showframe";i:1;s:5:"false";}','Y',null,'T','F',-29,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3016,'CustomNavBean','CustomNavBean.cls',1,2,1,1,'Reports','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"150";s:7:"ENTRIES";a:5:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:12:"Vendor Aging";s:6:"MENUOP";s:54:"Accounts Payable#~#reporteditor.phtml?.op=46&.ifmod=ap";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:9:"AP Ledger";s:6:"MENUOP";s:54:"Accounts Payable#~#reporteditor.phtml?.op=44&.ifmod=ap";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:14:"Bills Analysis";s:6:"MENUOP";s:56:"Accounts Payable#~#reporteditor.phtml?.op=1010&.ifmod=ap";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:15:"Posted Payments";s:6:"MENUOP";s:50:"Accounts Payable#~#lister.phtml?.op=1054&.ifmod=ap";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:12:"1099 Reports";s:6:"MENUOP";s:56:"Accounts Payable#~#reporteditor.phtml?.op=2209&.ifmod=ap";}}}','Y',null,'F','F',-29,'NFP','T');
insert into IABEANS (RECORD#, DESCRIPTION, BEANFILE, BEANLOCATION, DISPLAYORDER, SIZEX, SIZEY, TITLE, BEANID, NUMBEROFROWS, PARAMS, REMOVABLE, BEANTYPE, SNAPSHOT, COLLAPSED, DASHBOARDKEY, INDUSTRYCODE, DASHBOARDFILTER) values (3015,'CustomNavBean','CustomNavBean.cls',1,1,1,1,'My Tasks','CustomNavBean',10,'a:2:{s:10:"compheight";s:3:"175";s:7:"ENTRIES";a:6:{i:0;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:16:"Open Assignments";s:6:"MENUOP";s:41:"Company#~#lister.phtml?.op=1576&.ifmod=co";}i:1;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:11:"Enter Bills";s:6:"MENUOP";s:74:"Accounts Payable#~#lister.phtml?.recordtype=pi&.it=apbill&.op=51&.ifmod=ap";}i:2;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:9:"Pay Bills";s:6:"MENUOP";s:50:"Accounts Payable#~#editor.phtml?.op=1552&.ifmod=ap";}i:3;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:19:"Bank Reconciliation";s:6:"MENUOP";s:49:"Cash Management#~#editor.phtml?.op=1324&.ifmod=cm";}i:4;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:14:"Manage Vendors";s:6:"MENUOP";s:48:"Accounts Payable#~#lister.phtml?.op=48&.ifmod=ap";}i:5;a:4:{s:7:"__dummy";s:0:"";s:10:"_isNewLine";b:1;s:8:"MENUNAME";s:9:"1099 Form";s:6:"MENUOP";s:70:"Accounts Payable#~#reporteditor.phtml?.object=vendor&.op=155&.ifmod=ap";}}}','Y',null,'F','F',-29,'NFP','T');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (205,3024,3,'1','Pledges Receivable','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1750;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',676,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (204,3024,2,'1','Grants Receivable','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1419;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',675,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (203,3054,6,'1','Net Surplus (Deficit)','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2430;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',667,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (202,3054,1,'1','Cash and Equivalents','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1333;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',666,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (201,3054,3,'1','Unrestricted Liquid Net Assets','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2424;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',665,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (200,3054,5,'1','Expenditures','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1438;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',664,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (199,3054,4,'1','Revenue','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2206;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',663,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (198,3054,2,'1','Unrestricted Net Assets','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2423;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',662,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (197,3048,6,'1','Accounts Payable Balance','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1237;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',657,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (196,3048,5,'1','Accounts Receivable','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1236;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',656,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (195,3048,4,'1','Cash & Equivalents','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1333;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',655,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (194,3048,3,'1','Unrestricted Net Assets','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1311;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',654,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (193,3048,2,'1','Expenditures','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1438;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',653,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (192,3048,1,'1','Revenue','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2206;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',652,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (191,3039,6,'1','General & Admin YTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1201;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',649,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (190,3039,5,'1','Personnel Expenses YTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1759;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',648,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (189,3039,4,'1','Expenditures YTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1438;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',647,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (188,3039,3,'1','General & Admin QTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1201;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:9;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',646,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (187,3039,2,'1','Personnel Expenses QTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1759;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:9;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',645,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (186,3039,1,'1','Expenditures QTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1438;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:9;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',644,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (185,3031,6,'1','Net Income YTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2207;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',640,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (184,3031,5,'1','Direct Expenses YTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1435;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',639,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (183,3031,4,'1','Revenue YTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2206;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:15;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',638,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (182,3031,3,'1','Net Income QTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2207;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:9;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',637,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (181,3031,2,'1','Direct Expenses QTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1435;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:9;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',636,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (180,3031,1,'1','Revenue QTD','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2206;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:9;s:13:"reportingbook";N;s:9:"compareto";s:6:"budget";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',635,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (178,3024,6,'1','Bad Debt Ratio','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"number";s:12:"accountgroup";i:-2414;s:8:"rounding";s:4:"none";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',627,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (176,3024,4,'1','Accounts Receivable, Net','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1293;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',625,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (175,3024,5,'1','Bad Debt Expense','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1371;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"Y";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:6:"period";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',624,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (174,3024,1,'1','Revenue','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2206;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',623,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (173,3017,6,'1','Days Payables Outstanding','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"number";s:12:"accountgroup";i:-2413;s:8:"rounding";s:4:"none";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',619,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (172,3017,5,'1','AP Turnover Ratio','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"number";s:12:"accountgroup";i:-2393;s:8:"rounding";s:4:"none";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:5:"green";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',618,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (171,3017,4,'1','Expenditures','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1438;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',617,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (170,3017,3,'1','Average AP Balance','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-2390;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',616,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (169,3017,2,'1','Accrued Liabilities Balance','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1344;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',615,'NFP');
insert into IAKPICOMPONENT (RECORD#, BEANKEY, KPIORDER, KPITYPE, TITLE, PARAMS, REPORTKEY, INDUSTRYCODE) values (168,3017,1,'1','Accounts Payable Balance','a:21:{s:7:"version";i:1;s:11:"showasvalue";s:6:"amount";s:12:"accountgroup";i:-1237;s:8:"rounding";s:5:"whole";s:15:"reportingperiod";i:2;s:13:"reportingbook";N;s:9:"compareto";s:6:"period";s:11:"comparetoby";s:1:"M";s:12:"comparetonum";i:-1;s:14:"comparisondata";s:10:"percentage";s:15:"visualindicator";s:5:"arrow";s:20:"visualindicatorcolor";s:3:"red";s:6:"budget";N;s:21:"reportingbooklisttype";s:6:"single";s:14:"reportingbook2";N;s:21:"reportingbookmultiple";N;s:20:"onlyfilteronaddbooks";N;s:8:"asofdate";s:0:"";s:17:"reportresultcache";N;s:21:"entitylocationcontext";N;s:15:"dashboardfilter";N;}',614,'NFP');
insert into IACUSTOMERGROUP (RECORD#, ID, NAME, DESCRIPTION, GROUPTYPE, MEMBERFILTERS, INDUSTRYCODE) values (-2,'System_Active','Active Customers',null,'A','{"FILTERS":{"SORTORDER":"ASC","RESTRICTTO":"","SORTFIELD":"CUSTOMERID","MEMBERS":[{"__dummy":"","_isNewLine":true,"FIELD":"STATUS","VALUE":"inactive","OPERATOR":"!="}],"CONDITIONTYPE":"AND"}}','NFP');
insert into IAVENDORGROUP (RECORD#, ID, NAME, DESCRIPTION, GROUPTYPE, MEMBERFILTERS, INDUSTRYCODE) values (-2,'System_Active','Active Vendors',null,'A','{"FILTERS":{"SORTORDER":"ASC","RESTRICTTO":"","SORTFIELD":"VENDORID","MEMBERS":[{"__dummy":"","_isNewLine":true,"FIELD":"STATUS","VALUE":"inactive","OPERATOR":"!="}],"CONDITIONTYPE":"AND"}}','NFP');
insert into IAGLDIMGRPMEMBERS (RECORD#, PARENT#, LOCATIONGROUPKEY, DEPARTMENTGROUPKEY, VENDORGROUPKEY, CUSTOMERGROUPKEY, PROJECTGROUPKEY, EMPLOYEEGROUPKEY, ITEMGROUPKEY, CLASSGROUPKEY, SORTORD, INDUSTRYCODE) values (10,-2420,null,null,null,-2,null,null,null,null,0,'NFP');
insert into IAGLDIMGRPMEMBERS (RECORD#, PARENT#, LOCATIONGROUPKEY, DEPARTMENTGROUPKEY, VENDORGROUPKEY, CUSTOMERGROUPKEY, PROJECTGROUPKEY, EMPLOYEEGROUPKEY, ITEMGROUPKEY, CLASSGROUPKEY, SORTORD, INDUSTRYCODE) values (9,-2411,null,null,-2,null,null,null,null,null,0,'NFP');
UPDATE IAGLACCTGRP SET NORMAL_BALANCE = '1' WHERE RECORD# = '-1282';
UPDATE IAGLACCTGRP SET TITLE = 'Sales & Use Tax Payable', TOTALTITLE = 'Total Sales & Use Tax Payable' WHERE RECORD# = '-1300';
UPDATE IAGLACCTGRP SET NORMAL_BALANCE = '1' WHERE RECORD# = '-1955';