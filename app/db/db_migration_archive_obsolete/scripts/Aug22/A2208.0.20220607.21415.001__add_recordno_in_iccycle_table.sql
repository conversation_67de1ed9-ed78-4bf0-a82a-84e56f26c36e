-- drop the initial index if it was already created on the schema (otherwise it will give an error when flyway will apply again the V file and the index is already created)
DECLARE
index_exists number := 0;
BEGIN
Select count(*) into index_exists
from user_indexes
where index_name = 'UQ_ICCYCLE_RECORDNO' and table_name = 'ICCYCLE';
if (index_exists > 0) then
      execute immediate 'drop index UQ_ICCYCLE_RECORDNO';
end if;
end;
/

CREATE UNIQUE INDEX UQ_ICCYCLE_RECORDNO ON ICCYCLE ("CNY#", "RECORD#") TABLESPACE ACCTINDX
/