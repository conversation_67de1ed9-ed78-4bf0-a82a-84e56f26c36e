--liquibase formatted sql
--changeset kghosh:20200615.25867.001 runOnChange:false logicalFilePath:*********.25867.001__SouthAfrica_TaxSubmission.sql

------------------------------------------------------------------------------------------------------------------------
----------------  DDL -- ticket 1xxxxx : South Africa Tax submission migration
------------------------------------------------------------------------------------------------------------------------

-- Add alternative setup column to tax solution table
ALTER TABLE TAXSOLUTION ADD ALTSETUP CHAR(1 CHAR) DEFAULT 'F'
/

-- Add email op-in column to customer table
ALTER TABLE CUSTOMER ADD EMAILOPTIN CHAR(1 CHAR) DEFAULT 'F'
/

-- Add is credit transaction column to taxledger table
ALTER TABLE TAXLEDGER ADD ISCREDIT CHAR (1 CHAR) DEFAULT 'F'
/


------------- DML - Insert South Africa tax solution for multitax customers ---------------------------------------------

INSERT INTO TAXSOLUTION (CNY#, RECORD#, SOLUTIONID, STATUS, TAXMETHOD, SHOWMULTILINETAX, GLACCOUNTPURCHASEKEY, GLACCOUNTSALEKEY, STARTDATE, DESCRIPTION, TYPE, ALTSETUP)
SELECT
    M.CNY# CNY#,
    GET_NEXTRECORDID(M.CNY#, 'TAXSOLUTION') RECORD#,
    'South Africa - VAT' SOLUTIONID,
    'N' STATUS,
    'VAT' TAXMETHOD,
    NULL SHOWMULTILINETAX,
    NULL GLACCOUNTPURCHASEKEY,
    NULL GLACCOUNTSALEKEY,
    NULL STARTDATE,
    '[Not configured] Preinstalled tax solution for South Africa - VAT' DESCRIPTION,
    'S' TYPE,
    'F' ALTSETUP
FROM MODULEPREF M
WHERE M.MODULEKEY = '64.TAX' AND M.PROPERTY = 'SINGLEORMULTITAX' AND M.VALUE = 'Multiple'
AND NOT EXISTS ( SELECT 1 FROM TAXSOLUTION T WHERE T.SOLUTIONID = 'South Africa - VAT' AND T.CNY# = M.CNY#)
/
