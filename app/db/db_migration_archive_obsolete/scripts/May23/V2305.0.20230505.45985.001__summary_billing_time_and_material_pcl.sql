DECLARE
n pls_integer :=0;
BEGIN
select count(*) into n from all_tab_cols where owner = sys_context('userenv', 'current_schema') and table_name = 'PROJECTCONTRACTLINE'
                                           and column_name ='SUMMARIZEBILL';
if n<=0 then
     EXECUTE IMMEDIATE 'ALTER TABLE PROJECTCONTRACTLINE ADD (
    SUMMARIZEBILL CHAR(1 CHAR) DEFAULT ''F''
)';
end if;
END;
/

DECLARE
n pls_integer :=0;
BEGIN
select count(*) into n from all_tab_cols where owner = sys_context('userenv', 'current_schema') and table_name = 'DOCENTRY'
                                           and column_name ='ISSUMMARIZED';
if n<=0 then
     EXECUTE IMMEDIATE 'ALTER TABLE DOCENTRY ADD (
    ISSUMMARIZED CHAR(1 CHAR) DEFAULT ''F''
)';
end if;
END;
/

DECLARE
n pls_integer :=0;
BEGIN
select count(*) into n from all_tab_cols where owner = sys_context('userenv', 'current_schema') and table_name = 'PRENTRY'
                                           and column_name ='ISSUMMARIZED';
if n<=0 then
     EXECUTE IMMEDIATE 'ALTER TABLE PRENTRY ADD (
    ISSUMMARIZED CHAR(1 CHAR) DEFAULT ''F''
)';
end if;
END;
/


DECLARE
n pls_integer :=0;
BEGIN
SELECT count(*) into n FROM all_tables where owner = sys_context('userenv', 'current_schema') and table_name = 'SUMMARYBILLEDDETAIL';
if n<=0 then
     EXECUTE IMMEDIATE
'CREATE TABLE SUMMARYBILLEDDETAIL
(
    CNY#                    NUMBER(15, 0) CONSTRAINT NN_SUMBILLDET_CNY NOT NULL ENABLE,
    RECORD#                 NUMBER(15, 0) CONSTRAINT NN_SUMBILLDET_RECORD NOT NULL ENABLE,
    TRANSACTIONTYPE         CHAR(2 CHAR),
    PCNKEY                  NUMBER(15, 0),
    PCLKEY                  NUMBER(15, 0),
    GLENTRYKEY              NUMBER(15, 0),
    TSENTRYKEY              NUMBER(15, 0),
    PRENTRYKEY              NUMBER(15, 0),
    DOCENTRYKEY             NUMBER(15, 0),
    QUANTITYBILLED          NUMBER(30,10),
    TXNPRICEBILLED          NUMBER(30,10),
    TXNAMOUNTBILLED         NUMBER(30,10),
    SUMDOCHDRKEY            NUMBER(15, 0),
    SUMDOCENTRYKEY          NUMBER(15, 0),
    WHENCREATED             DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE ''GMT'',
    WHENMODIFIED            DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE ''GMT'',
    CREATEDBY               NUMBER(15, 0),
    MODIFIEDBY              NUMBER(15, 0),
    CONSTRAINT FK_SUMBILLDET_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_PCN_KEY FOREIGN KEY (CNY#, PCNKEY) REFERENCES PROJECTCONTRACT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_PCL_KEY FOREIGN KEY (CNY#, PCLKEY) REFERENCES PROJECTCONTRACTLINE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_GLENTRY_KEY FOREIGN KEY (CNY#, GLENTRYKEY) REFERENCES GLENTRY (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_TSENTRY_KEY FOREIGN KEY (CNY#, TSENTRYKEY) REFERENCES TIMESHEETENTRY (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_PRENTRY_KEY FOREIGN KEY (CNY#, PRENTRYKEY) REFERENCES PRENTRY (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_DOCENTRY_KEY FOREIGN KEY (CNY#, DOCENTRYKEY) REFERENCES DOCENTRY (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_SUM_DOCHDR_KEY FOREIGN KEY (CNY#, SUMDOCHDRKEY) REFERENCES DOCHDR (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_SUM_DOCENTRY_KEY FOREIGN KEY (CNY#, SUMDOCENTRYKEY) REFERENCES DOCENTRY (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_SUMBILLDET_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE
)
    TABLESPACE ACCTDATA'
;
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_CNY# ON SUMMARYBILLEDDETAIL (CNY#) TABLESPACE ACCTINDX';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_PCN_KEY ON SUMMARYBILLEDDETAIL (CNY#, PCNKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_PCL_KEY ON SUMMARYBILLEDDETAIL (CNY#, PCLKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_GLENTRY_KEY ON SUMMARYBILLEDDETAIL (CNY#, GLENTRYKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_TSENTRY_KEY ON SUMMARYBILLEDDETAIL (CNY#, TSENTRYKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_PRENTRY_KEY ON SUMMARYBILLEDDETAIL (CNY#, PRENTRYKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_DOCENTRY_KEY ON SUMMARYBILLEDDETAIL (CNY#, DOCENTRYKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_SUM_DOCHDR_KEY ON SUMMARYBILLEDDETAIL (CNY#, SUMDOCHDRKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_SUM_DOCENTRY_KEY ON SUMMARYBILLEDDETAIL (CNY#, SUMDOCENTRYKEY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_CREATEDBY ON SUMMARYBILLEDDETAIL (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE';
EXECUTE IMMEDIATE 'CREATE INDEX IX_SUMBILLDET_MODIFIEDBY ON SUMMARYBILLEDDETAIL (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE';

end if;
END;
/