/*

== DELTA ONLY FOR DEV SCHEMAS ==

Description: check if the procedure exist using all_objects instead of all_procedures because it not contains the invalid procedures

Repair action: EXECUTE

*/
DECLARE
 n NUMBER;
BEGIN

  SELECT COUNT(*) AS COUNT INTO n FROM all_objects
  WHERE UPPER(object_name) = UPPER('compute_company_size')
    AND UPPER(object_type) = 'PROCEDURE'
    AND UPPER(owner) = UPPER(SYS_CONTEXT('userenv', 'current_schema'));

  IF (n > 0) THEN
    EXECUTE IMMEDIATE 'DROP PROCEDURE compute_company_size';
  END IF;

END;
/
