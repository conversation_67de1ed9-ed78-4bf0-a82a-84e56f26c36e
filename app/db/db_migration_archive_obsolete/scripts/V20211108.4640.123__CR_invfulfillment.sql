--liquibase formatted sql
--changeset rcollins:20211108.4640.123 runOnChange:false logicalFilePath:V20211108.4640.123__CR_invfulfillment.sql

-- these are additional 2022 R1 changes for inventory fulfillment....


-- to enable docentry alloc/reserve:
ALTER TABLE DOCENTRY add (
    ALLOCRESERVEISENABLED       CHAR(1 CHAR),
    QUANTITYRESERVED            NUMBER(30,10),
    QUANTITYALLOCATED           NUMBER(30,10),
    QUANTITYRESERVEDWHENOPEN    NUMBER(30,10), -- i.e. before moved in fulfillment
    QUANTITYALLOCATEDWHENOPEN   NUMBER(30,10)
    )
/

ALTER TABLE DOCENTRYTRACKDETAIL add (
    QUANTITYRESERVED            NUMBER(30,10),
    QUANTITYALLOCATED           NUMBER(30,10), -- also called quantity PACKED
    QUANTITYRESERVED<PERSON>H<PERSON><PERSON><PERSON>    NUMBER(30,10),
    QUANTI<PERSON><PERSON>LOC<PERSON><PERSON>WHENOPEN   NUMBER(30,10),
    QUANTITYPACKED              NUMBER(30,10),
    WOR<PERSON><PERSON>UEUEKEY                NUMBER(15,0)    -- which bundle does this belong to?  (only when in fulfillment)
    )
/

ALTER TABLE ICWORKQUEUE add (
    DOCUMENTTYPE                CHAR(1 CHAR),
    ORIGINALDOCID               VARCHAR2(60 CHAR)
    )
/

