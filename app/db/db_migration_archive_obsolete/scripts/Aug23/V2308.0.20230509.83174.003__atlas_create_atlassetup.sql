-- Consolidation configuration feature, <PERSON><PERSON> story: IA-83174, RM-3099 
-- added by: <PERSON><PERSON><PERSON>

-- Disable trigger
BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

-- insert MODU<PERSON>_CONFIGURED as T, to config atlassetup module
INSERT INTO MODULEPREF (CNY#, MOD<PERSON>LEKEY, PROPERTY, VALUE)
SELECT A.CNY#, <PERSON><PERSON>OD<PERSON>LEKEY, 'MODULE_CONFIGURED', 'T' FROM ATLASSETUP A WHERE NOT EXISTS ( SELECT 1 FROM MODULEPREF M WHERE M.CNY# = A.CNY# AND M.MODULEKEY = '45.ATLAS' AND M.PROPERTY = 'MODULE_CONFIGURED' )
/

-- Enable trigger
BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/