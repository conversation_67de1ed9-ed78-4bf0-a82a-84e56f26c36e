--liquibase formatted sql
--changeset trondonuwu:V20211029.43251.001 runOnChange:false logicalFilePath:V20211029.43251.001__fixedassetpoc.sql

------------------------------------------------------------------------------------------------------------------------
-- Start Story S43251: Fixed Asset POC initial tables
------------------------------------------------------------------------------------------------------------------------

CREATE TABLE FACLASSIFICATION (
    CNY#  NUMBER(15,0) CONSTRAINT NN_FACLASSIFICATION_CNY# NOT NULL ENABLE,
    RECORD# NUMBER(15,0) CONSTRAINT NN_FACLASSIFICATION_RECORD# NOT NULL ENABLE,
    CLASSIFICATIONID VARCHAR2(60 CHAR),
    NAME VARCHAR2(200 CHAR),
    STATUS CHAR(1 CHAR),
    DEPRACCTKEY  NUMBER(15,0),
    DEPREXPACCTKEY  NUMBER(15,0),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    CREATEDBY NUMBER(15,0),
    MODIFIEDBY  NUMBER(15,0),
    CONSTRAINT PK_FACLASSIFICATION PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,
    CONSTRAINT UQ_FACLASSIFICATION_CLASSIFICATIONID UNIQUE (CNY#, CLASSIFICATIONID) USING INDEX TABLESPACE ACCTINDX  ENABLE,
    CONSTRAINT FK_FACLASSIFICATION_DEPRACCTKEY FOREIGN KEY (CNY#, DEPRACCTKEY) REFERENCES BASEACCOUNT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FACLASSIFICATION_DEPREXPACCTKEY FOREIGN KEY (CNY#, DEPREXPACCTKEY) REFERENCES BASEACCOUNT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FACLASSIFICATION_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FACLASSIFICATION_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FACLASSIFICATION_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_FACLASSIFICATION_DEPRACCTKEY ON FACLASSIFICATION (CNY#, DEPRACCTKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FACLASSIFICATION_DEPREXPACCTKEY ON FACLASSIFICATION (CNY#, DEPREXPACCTKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FACLASSIFICATION_CREATEDBY ON FACLASSIFICATION (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FACLASSIFICATION_MODIFIEDBY ON FACLASSIFICATION (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/


CREATE TABLE FAASSET (
    CNY#  NUMBER(15,0) CONSTRAINT NN_FAASSET_CNY# NOT NULL ENABLE,
    RECORD# NUMBER(15,0) CONSTRAINT NN_FAASSET_RECORD# NOT NULL ENABLE,
    CLASSIFICATIONKEY  NUMBER(15,0),
    ASSETID VARCHAR2(60 CHAR),
    STATE CHAR(1 CHAR),
    STATUS CHAR(1 CHAR),
    NAME VARCHAR2(200 CHAR),
    SERIALNUMBER VARCHAR2(200 CHAR),
    ASSETCOST  NUMBER(14,2),
    SALVAGEVALUE  NUMBER(14,2),
    INSERVICEDATE DATE,
    DEPRACCTKEY  NUMBER(15,0),
    DEPREXPACCTKEY  NUMBER(15,0),

    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    CREATEDBY NUMBER(15,0),
    MODIFIEDBY  NUMBER(15,0),

    MELOCATIONKEY NUMBER(15, 0) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),
    LOCATIONKEY NUMBER(15, 0),
    DEPTKEY NUMBER(15, 0),

    CUSTOMERDIMKEY      NUMBER(15, 0),
    VENDORDIMKEY        NUMBER(15, 0),
    EMPLOYEEDIMKEY      NUMBER(15, 0),
    CLASSDIMKEY         NUMBER(15, 0),
    ITEMDIMKEY          NUMBER(15, 0),
    CONTRACTDIMKEY      NUMBER(15, 0),
    PROJECTDIMKEY       NUMBER(15, 0),
    TASKDIMKEY          NUMBER(15, 0),
    WAREHOUSEDIMKEY     NUMBER(15, 0),
    COSTTYPEDIMKEY      NUMBER(15, 0),
    CLASS2DIMKEY        NUMBER(15, 0),

    CUSTFIELD1          VARCHAR2(4000 CHAR),
    CUSTFIELD2          VARCHAR2(4000 CHAR),
    CUSTFIELD3          VARCHAR2(4000 CHAR),
    CUSTFIELD4          VARCHAR2(4000 CHAR),
    CUSTFIELD5          VARCHAR2(4000 CHAR),
    CUSTFIELD6          VARCHAR2(4000 CHAR),
    CUSTFIELD7          VARCHAR2(4000 CHAR),
    CUSTFIELD8          VARCHAR2(4000 CHAR),
    CUSTFIELD9          VARCHAR2(4000 CHAR),
    CUSTFIELD10         VARCHAR2(4000 CHAR),
    CUSTFIELD11         VARCHAR2(4000 CHAR),
    CUSTFIELD12         VARCHAR2(4000 CHAR),
    CUSTFIELD13         VARCHAR2(4000 CHAR),
    CUSTFIELD14         VARCHAR2(4000 CHAR),
    CUSTFIELD15         VARCHAR2(4000 CHAR),
    CUSTFIELD16         VARCHAR2(4000 CHAR),
    CUSTFIELD17         VARCHAR2(4000 CHAR),
    CUSTFIELD18         VARCHAR2(4000 CHAR),
    CUSTFIELD19         VARCHAR2(4000 CHAR),
    CUSTFIELD20         VARCHAR2(4000 CHAR),
    CUSTFIELD21         VARCHAR2(4000 CHAR),
    CUSTFIELD22         VARCHAR2(4000 CHAR),
    CUSTFIELD23         VARCHAR2(4000 CHAR),
    CUSTFIELD24         VARCHAR2(4000 CHAR),
    CUSTFIELD25         VARCHAR2(4000 CHAR),
    CUSTFIELD26         VARCHAR2(4000 CHAR),
    CUSTFIELD27         VARCHAR2(4000 CHAR),
    CUSTFIELD28         VARCHAR2(4000 CHAR),
    CUSTFIELD29         VARCHAR2(4000 CHAR),
    CUSTFIELD30         VARCHAR2(4000 CHAR),
    CUSTFIELD31         VARCHAR2(4000 CHAR),
    CUSTFIELD32         VARCHAR2(4000 CHAR),
    CUSTFIELD33         VARCHAR2(4000 CHAR),
    CUSTFIELD34         VARCHAR2(4000 CHAR),
    CUSTFIELD35         VARCHAR2(4000 CHAR),
    CUSTFIELD36         VARCHAR2(4000 CHAR),
    CUSTFIELD37         VARCHAR2(4000 CHAR),
    CUSTFIELD38         VARCHAR2(4000 CHAR),
    CUSTFIELD39         VARCHAR2(4000 CHAR),
    CUSTFIELD40         VARCHAR2(4000 CHAR),
    CUSTFIELD41         VARCHAR2(4000 CHAR),
    CUSTFIELD42         VARCHAR2(4000 CHAR),
    CUSTFIELD43         VARCHAR2(4000 CHAR),
    CUSTFIELD44         VARCHAR2(4000 CHAR),
    CUSTFIELD45         VARCHAR2(4000 CHAR),
    CUSTFIELD46         VARCHAR2(4000 CHAR),
    CUSTFIELD47         VARCHAR2(4000 CHAR),
    CUSTFIELD48         VARCHAR2(4000 CHAR),
    CUSTFIELD49         VARCHAR2(4000 CHAR),
    CUSTFIELD50         VARCHAR2(4000 CHAR),
    CUSTFIELD51         VARCHAR2(4000 CHAR),
    CUSTFIELD52         VARCHAR2(4000 CHAR),
    CUSTFIELD53         VARCHAR2(4000 CHAR),
    CUSTFIELD54         VARCHAR2(4000 CHAR),
    CUSTFIELD55         VARCHAR2(4000 CHAR),
    CUSTFIELD56         VARCHAR2(4000 CHAR),
    CUSTFIELD57         VARCHAR2(4000 CHAR),
    CUSTFIELD58         VARCHAR2(4000 CHAR),
    CUSTFIELD59         VARCHAR2(4000 CHAR),
    CUSTFIELD60         VARCHAR2(4000 CHAR),
    CUSTFIELD61         VARCHAR2(4000 CHAR),
    CUSTFIELD62         VARCHAR2(4000 CHAR),
    CUSTFIELD63         VARCHAR2(4000 CHAR),
    CUSTFIELD64         VARCHAR2(4000 CHAR),
    CUSTFIELD65         VARCHAR2(4000 CHAR),
    CUSTFIELD66         VARCHAR2(4000 CHAR),
    CUSTFIELD67         VARCHAR2(4000 CHAR),
    CUSTFIELD68         VARCHAR2(4000 CHAR),
    CUSTFIELD69         VARCHAR2(4000 CHAR),
    CUSTFIELD70         VARCHAR2(4000 CHAR),
    CUSTFIELD71         VARCHAR2(4000 CHAR),
    CUSTFIELD72         VARCHAR2(4000 CHAR),
    CUSTFIELD73         VARCHAR2(4000 CHAR),
    CUSTFIELD74         VARCHAR2(4000 CHAR),
    CUSTFIELD75         VARCHAR2(4000 CHAR),
    CUSTFIELD76         VARCHAR2(4000 CHAR),
    CUSTFIELD77         VARCHAR2(4000 CHAR),
    CUSTFIELD78         VARCHAR2(4000 CHAR),
    CUSTFIELD79         VARCHAR2(4000 CHAR),
    CUSTFIELD80         VARCHAR2(4000 CHAR),
    CUSTFIELD81         VARCHAR2(4000 CHAR),
    CUSTFIELD82         VARCHAR2(4000 CHAR),
    CUSTFIELD83         VARCHAR2(4000 CHAR),
    CUSTFIELD84         VARCHAR2(4000 CHAR),
    CUSTFIELD85         VARCHAR2(4000 CHAR),
    CUSTFIELD86         VARCHAR2(4000 CHAR),
    CUSTFIELD87         VARCHAR2(4000 CHAR),
    CUSTFIELD88         VARCHAR2(4000 CHAR),
    CUSTFIELD89         VARCHAR2(4000 CHAR),
    CUSTFIELD90         VARCHAR2(4000 CHAR),
    CUSTFIELD91         VARCHAR2(4000 CHAR),
    CUSTFIELD92         VARCHAR2(4000 CHAR),
    CUSTFIELD93         VARCHAR2(4000 CHAR),
    CUSTFIELD94         VARCHAR2(4000 CHAR),
    CUSTFIELD95         VARCHAR2(4000 CHAR),
    CUSTFIELD96         VARCHAR2(4000 CHAR),
    CUSTFIELD97         VARCHAR2(4000 CHAR),
    CUSTFIELD98         VARCHAR2(4000 CHAR),
    CUSTFIELD99         VARCHAR2(4000 CHAR),
    CUSTFIELD100        VARCHAR2(4000 CHAR),

    REL1 NUMBER(15, 0),
    REL2 NUMBER(15, 0),
    REL3 NUMBER(15, 0),
    REL4 NUMBER(15, 0),
    REL5 NUMBER(15, 0),
    REL6 NUMBER(15, 0),
    REL7 NUMBER(15, 0),
    REL8 NUMBER(15, 0),
    REL9 NUMBER(15, 0),
    REL10 NUMBER(15, 0),
    REL11 NUMBER(15, 0),
    REL12 NUMBER(15, 0),
    REL13 NUMBER(15, 0),
    REL14 NUMBER(15, 0),
    REL15 NUMBER(15, 0),
    REL16 NUMBER(15, 0),
    REL17 NUMBER(15, 0),
    REL18 NUMBER(15, 0),
    REL19 NUMBER(15, 0),
    REL20 NUMBER(15, 0),
    REL21 NUMBER(15, 0),
    REL22 NUMBER(15, 0),
    REL23 NUMBER(15, 0),
    REL24 NUMBER(15, 0),
    REL25 NUMBER(15, 0),
    REL26 NUMBER(15, 0),
    REL27 NUMBER(15, 0),
    REL28 NUMBER(15, 0),
    REL29 NUMBER(15, 0),
    REL30 NUMBER(15, 0),
    REL31 NUMBER(15, 0),
    REL32 NUMBER(15, 0),
    REL33 NUMBER(15, 0),
    REL34 NUMBER(15, 0),
    REL35 NUMBER(15, 0),
    REL36 NUMBER(15, 0),
    REL37 NUMBER(15, 0),
    REL38 NUMBER(15, 0),
    REL39 NUMBER(15, 0),
    REL40 NUMBER(15, 0),
    REL41 NUMBER(15, 0),
    REL42 NUMBER(15, 0),
    REL43 NUMBER(15, 0),
    REL44 NUMBER(15, 0),
    REL45 NUMBER(15, 0),
    REL46 NUMBER(15, 0),
    REL47 NUMBER(15, 0),
    REL48 NUMBER(15, 0),
    REL49 NUMBER(15, 0),
    REL50 NUMBER(15, 0),
    REL51 NUMBER(15, 0),
    REL52 NUMBER(15, 0),
    REL53 NUMBER(15, 0),
    REL54 NUMBER(15, 0),
    REL55 NUMBER(15, 0),
    REL56 NUMBER(15, 0),
    REL57 NUMBER(15, 0),
    REL58 NUMBER(15, 0),
    REL59 NUMBER(15, 0),
    REL60 NUMBER(15, 0),
    REL61 NUMBER(15, 0),
    REL62 NUMBER(15, 0),
    REL63 NUMBER(15, 0),
    REL64 NUMBER(15, 0),
    REL65 NUMBER(15, 0),
    REL66 NUMBER(15, 0),
    REL67 NUMBER(15, 0),
    REL68 NUMBER(15, 0),
    REL69 NUMBER(15, 0),
    REL70 NUMBER(15, 0),
    REL71 NUMBER(15, 0),
    REL72 NUMBER(15, 0),
    REL73 NUMBER(15, 0),
    REL74 NUMBER(15, 0),
    REL75 NUMBER(15, 0),
    REL76 NUMBER(15, 0),
    REL77 NUMBER(15, 0),
    REL78 NUMBER(15, 0),
    REL79 NUMBER(15, 0),
    REL80 NUMBER(15, 0),
    REL81 NUMBER(15, 0),
    REL82 NUMBER(15, 0),
    REL83 NUMBER(15, 0),
    REL84 NUMBER(15, 0),
    REL85 NUMBER(15, 0),
    REL86 NUMBER(15, 0),
    REL87 NUMBER(15, 0),
    REL88 NUMBER(15, 0),
    REL89 NUMBER(15, 0),
    REL90 NUMBER(15, 0),
    REL91 NUMBER(15, 0),
    REL92 NUMBER(15, 0),
    REL93 NUMBER(15, 0),
    REL94 NUMBER(15, 0),
    REL95 NUMBER(15, 0),
    REL96 NUMBER(15, 0),
    REL97 NUMBER(15, 0),
    REL98 NUMBER(15, 0),
    REL99 NUMBER(15, 0),
    REL100 NUMBER(15, 0),

    UDDREL1 NUMBER(15,0),
    UDDREL2 NUMBER(15,0),
    UDDREL3 NUMBER(15,0),
    UDDREL4 NUMBER(15,0),
    UDDREL5 NUMBER(15,0),
    UDDREL6 NUMBER(15,0),
    UDDREL7 NUMBER(15,0),
    UDDREL8 NUMBER(15,0),
    UDDREL9 NUMBER(15,0),
    UDDREL10 NUMBER(15,0),
    UDDREL11 NUMBER(15,0),
    UDDREL12 NUMBER(15,0),
    UDDREL13 NUMBER(15,0),
    UDDREL14 NUMBER(15,0),
    UDDREL15 NUMBER(15,0),
    UDDREL16 NUMBER(15,0),
    UDDREL17 NUMBER(15,0),
    UDDREL18 NUMBER(15,0),
    UDDREL19 NUMBER(15,0),
    UDDREL20 NUMBER(15,0),

    CONSTRAINT PK_FAASSET PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,
    CONSTRAINT UQ_FAASSET_ASSETID UNIQUE (CNY#, ASSETID) USING INDEX TABLESPACE ACCTINDX  ENABLE,
    CONSTRAINT FK_FAASSET_CLASSIFICATIONKEY FOREIGN KEY (CNY#, CLASSIFICATIONKEY) REFERENCES FACLASSIFICATION (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_DEPRACCTKEY FOREIGN KEY (CNY#, DEPRACCTKEY) REFERENCES BASEACCOUNT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_DEPREXPACCTKEY FOREIGN KEY (CNY#, DEPREXPACCTKEY) REFERENCES BASEACCOUNT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_MELOCATIONKEY FOREIGN KEY(CNY#, MELOCATIONKEY) REFERENCES LOCATION(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_LOCATIONKEY FOREIGN KEY(CNY#, LOCATIONKEY) REFERENCES LOCATION(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_DEPTKEY FOREIGN KEY (CNY#, DEPTKEY) REFERENCES DEPARTMENT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_PROJECTDIMKEY FOREIGN KEY (CNY#, PROJECTDIMKEY) REFERENCES PROJECT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_TASKDIMKEY FOREIGN KEY (CNY#, TASKDIMKEY) REFERENCES TASK (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_COSTTYPEDIMKEY FOREIGN KEY (CNY#, COSTTYPEDIMKEY) REFERENCES COSTTYPE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_CUSTOMERDIMKEY FOREIGN KEY (CNY#, CUSTOMERDIMKEY) REFERENCES CUSTOMER (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_VENDORDIMKEY FOREIGN KEY (CNY#, VENDORDIMKEY) REFERENCES VENDOR (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_EMPLOYEEDIMKEY FOREIGN KEY (CNY#, EMPLOYEEDIMKEY) REFERENCES EMPLOYEE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_ITEMDIMKEY FOREIGN KEY (CNY#, ITEMDIMKEY) REFERENCES ICITEM (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_CLASSDIMKEY FOREIGN KEY (CNY#, CLASSDIMKEY) REFERENCES CLASS (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_CONTRACTDIMKEY FOREIGN KEY (CNY#, CONTRACTDIMKEY) REFERENCES CONTRACT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_WAREHOUSEDIMKEY FOREIGN KEY (CNY#, WAREHOUSEDIMKEY) REFERENCES ICWAREHOUSE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_CLASS2DIMKEY FOREIGN KEY (CNY#, CLASS2DIMKEY) REFERENCES CLASS2 (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FAASSET_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_FAASSET_CLASSIFICATIONKEY ON FAASSET (CNY#, CLASSIFICATIONKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_DEPRACCTKEY ON FAASSET (CNY#, DEPRACCTKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_DEPREXPACCTKEY ON FAASSET (CNY#, DEPREXPACCTKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_CREATEDBY ON FAASSET (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_MODIFIEDBY ON FAASSET (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_MELOCATIONKEY ON FAASSET (CNY#, MELOCATIONKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_LOCATIONKEY ON FAASSET (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_DEPTKEY ON FAASSET (CNY#, DEPTKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_PROJECTDIMKEY ON FAASSET (CNY#, PROJECTDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_TASKDIMKEY ON FAASSET (CNY#, TASKDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_COSTTYPEDIMKEY ON FAASSET (CNY#, COSTTYPEDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_CUSTOMERDIMKEY ON FAASSET (CNY#, CUSTOMERDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_VENDORDIMKEY ON FAASSET (CNY#, VENDORDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_EMPLOYEEDIMKEY ON FAASSET (CNY#, EMPLOYEEDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_ITEMDIMKEY ON FAASSET (CNY#, ITEMDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_CLASSDIMKEY ON FAASSET (CNY#, CLASSDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_CONTRACTDIMKEY ON FAASSET (CNY#, CONTRACTDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_WAREHOUSEDIMKEY ON FAASSET (CNY#, WAREHOUSEDIMKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FAASSET_CLASS2DIMKEY ON FAASSET (CNY#, CLASS2DIMKEY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE TABLE FADEPRRULE (
    CNY#  NUMBER(15,0) CONSTRAINT NN_FADEPRRULE_CNY# NOT NULL ENABLE,
    RECORD# NUMBER(15,0) CONSTRAINT NN_FADEPRRULE_RECORD# NOT NULL ENABLE,
    TYPE CHAR(1 CHAR),
    CLASSIFICATIONKEY  NUMBER(15,0),
    ASSETKEY NUMBER(15,0),
    METHODTYPE CHAR(1 CHAR),
    USEFULLIFE NUMBER(5,0),
    JOURNALKEY  NUMBER(15,0),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    CREATEDBY NUMBER(15,0),
    MODIFIEDBY  NUMBER(15,0),
    CONSTRAINT PK_FADEPRRULE PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,
    CONSTRAINT FK_FADEPRRULE_CLASSIFICATIONKEY FOREIGN KEY (CNY#, CLASSIFICATIONKEY) REFERENCES FACLASSIFICATION (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRRULE_ASSETKEY FOREIGN KEY (CNY#, ASSETKEY) REFERENCES FAASSET (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRRULE_JOURNALKEY FOREIGN KEY (CNY#, JOURNALKEY) REFERENCES BASEJOURNAL (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRRULE_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRRULE_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRRULE_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_FADEPRRULE_CLASSIFICATIONKEY ON FADEPRRULE (CNY#, CLASSIFICATIONKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRRULE_ASSETKEY ON FADEPRRULE (CNY#, ASSETKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRRULE_JOURNALKEY ON FADEPRRULE (CNY#, JOURNALKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRRULE_CREATEDBY ON FADEPRRULE (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRRULE_MODIFIEDBY ON FADEPRRULE (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/


CREATE TABLE FADEPRSCHEDULE (
    CNY#  NUMBER(15,0) CONSTRAINT NN_FADEPRSCHEDULE_CNY# NOT NULL ENABLE,
    RECORD# NUMBER(15,0) CONSTRAINT NN_FADEPRSCHEDULE_RECORD# NOT NULL ENABLE,
    ASSETKEY NUMBER(15,0),
    DEPRRULEKEY  NUMBER(15,0),
    STATE CHAR(1 CHAR),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    CREATEDBY NUMBER(15,0),
    MODIFIEDBY  NUMBER(15,0),
    CONSTRAINT PK_FADEPRSCHEDULE PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,
    CONSTRAINT FK_FADEPRSCHEDULE_ASSETKEY FOREIGN KEY (CNY#, ASSETKEY) REFERENCES FAASSET (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHEDULE_DEPRRULEKEY FOREIGN KEY (CNY#, DEPRRULEKEY) REFERENCES FADEPRRULE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHEDULE_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHEDULE_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHEDULE_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_FADEPRSCHEDULE_ASSETKEY ON FADEPRSCHEDULE (CNY#, ASSETKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRSCHEDULE_DEPRRULEKEY ON FADEPRSCHEDULE (CNY#, DEPRRULEKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRSCHEDULE_CREATEDBY ON FADEPRSCHEDULE (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRSCHEDULE_MODIFIEDBY ON FADEPRSCHEDULE (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/


CREATE TABLE FADEPRSCHENTRY (
    CNY#  NUMBER(15,0) CONSTRAINT NN_FADEPRSCHENTRY_CNY# NOT NULL ENABLE,
    RECORD# NUMBER(15,0) CONSTRAINT NN_FADEPRSCHENTRY_RECORD# NOT NULL ENABLE,
    SCHEDULEKEY NUMBER(15,0),
    GLBATCHKEY  NUMBER(15,0),
    SCHOPKEY  NUMBER(15,0),
    POSTINGDATE DATE,
    PERIOD NUMBER(4,0),
    STATE CHAR(1 CHAR),
    AMOUNT NUMBER(14,2),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    CREATEDBY NUMBER(15,0),
    MODIFIEDBY  NUMBER(15,0),
    CONSTRAINT PK_FADEPRSCHENTRY PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX  ENABLE,
    CONSTRAINT FK_FADEPRSCHENTRY_SCHEDULEKEY FOREIGN KEY (CNY#, SCHEDULEKEY) REFERENCES FADEPRSCHEDULE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHENTRY_GLBATCHKEY FOREIGN KEY (CNY#, GLBATCHKEY) REFERENCES GLBATCH (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHENTRY_SCHOPKEY FOREIGN KEY (CNY#, SCHOPKEY) REFERENCES SCHEDULEDOPERATION (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHENTRY_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHENTRY_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_FADEPRSCHENTRY_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_FADEPRSCHENTRY_SCHEDULEKEY ON FADEPRSCHENTRY (CNY#, SCHEDULEKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRSCHENTRY_GLBATCHKEY ON FADEPRSCHENTRY (CNY#, GLBATCHKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRSCHENTRY_SCHOPKEY ON FADEPRSCHENTRY (CNY#, SCHOPKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRSCHENTRY_CREATEDBY ON FADEPRSCHENTRY (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_FADEPRSCHENTRY_MODIFIEDBY ON FADEPRSCHENTRY (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/

------------------------------------------------------------------------------------------------------------------------
-- End Story S43251: Fixed Asset POC initial tables
------------------------------------------------------------------------------------------------------------------------