DECLARE
    v_column_exists number := 0;
BEGIN
    select count(1) into v_column_exists
    FROM ALL_TAB_COLS
    where owner = sys_context('userenv', 'current_schema')
      and upper(TABLE_NAME) = 'GLBOOKACCTRATETYPES' and upper(COLUMN_NAME) = 'G<PERSON>CCOUNTKEY';

    if (v_column_exists = 0) then
        execute immediate 'ALTER TABLE GLBOOKACCTRATETYPES ADD  (
            GLACCOUNTKEY NUMBER(15,0),
            CONSTRAINT FK_GLBOOKACCTRATETYPES_GLACCOUNTKEY FOREIGN KEY (CNY#, <PERSON><PERSON><PERSON>OUNTKEY)
                REFERENCES BASEACCOUNT (CNY#, RECORD#) DEFERRABLE ENABLE
            )';
    end if;
 end;
/
-- New column added

DECLARE
        v_column_exists number := 0;
    BEGIN
        select count(1) into v_column_exists
        FROM ALL_TAB_COLS
        where owner = sys_context('userenv', 'current_schema')
          and upper(TABLE_NAME) = 'GLBOOKACCTRATETYPES' and upper(COLUMN_NAME) = 'G<PERSON><PERSON>OUNTNO';

        if (v_column_exists <> 0) then
            execute immediate 'update (select  glbr.GLACCOUNTKEY, ba.RECORD# ba_recordno
                    from glbookacctratetypes glbr
                             join baseaccount ba
                                  on glbr.cny# = ba.cny# and glbr.GLACCOUNTNO = ba.ACCT_NO
            ) tb
            set tb.GLACCOUNTKEY = ba_recordno';

            execute immediate 'ALTER TABLE GLBOOKACCTRATETYPES DROP  CONSTRAINT NN_GLBOOKARTS_GLACCOUNTNO';

            execute immediate 'ALTER TABLE GLBOOKACCTRATETYPES DROP  CONSTRAINT UQ_GLBOOKACCTRATETYPES DROP INDEX';

            execute immediate 'ALTER TABLE GLBOOKACCTRATETYPES ADD constraint NN_GLBOOKARTS_GLACCOUNTKEY check (GLACCOUNTKEY IS NOT NULL)';

            execute immediate 'ALTER TABLE GLBOOKACCTRATETYPES ADD constraint UQ_GLBOOKACCTRATETYPES unique (CNY#, BOOKKEY, GLACCOUNTKEY) USING INDEX TABLESPACE ACCTINDX ENABLE';

            execute immediate 'ALTER TABLE GLBOOKACCTRATETYPES DROP COLUMN GLACCOUNTNO';

            execute immediate 'DROP INDEX IX_ACCOUNTLABEL_GLACCOUNTRECORDNO';

            execute immediate 'DROP INDEX IX_OFFSETACCT_GLACCOUNTRECORDNO';

            execute immediate 'create index IX_ACCOUNTLABEL_GLACCOUNTRECORDNO on ACCOUNTLABEL (CNY#, GLACCOUNTRECORDNO) TABLESPACE ACCTINDX INVISIBLE';
            execute immediate 'create index IX_OFFSETACCT_GLACCOUNTRECORDNO on OFFSETACCT (CNY#, GLACCOUNTRECORDNO) TABLESPACE ACCTINDX INVISIBLE';
            execute immediate 'create index IX_GLBOOKACCTRATETYPES_GLACCOUNTKEY on GLBOOKACCTRATETYPES (CNY#, GLACCOUNTKEY) TABLESPACE ACCTINDX INVISIBLE';

            update modulepref
            set value =
                    (
                        select t.value
                        from tmp_modulepref_ia83163_deleteby0524 t
                        where
                                t.cny# = modulepref.cny#
                          and t.modulekey = modulepref.modulekey
                          and t.property = modulepref.property
                          AND ROWNUM = 1
                    )
            where (modulepref.MODULEKEY, modulepref.PROPERTY) in (
                                                                  ('61.SFDC2', 'GLACCOUNTDETAILFIELDS'),
                                                                  ('61.SFDC2', 'STATACCOUNTDETAILFIELDS'));

            update modulepref
            set value = (
                select
                    LISTAGG(ba_recordno, '#~#') WITHIN GROUP (ORDER BY my_level asc) final_value
                from (
                         select ba.record# ba_recordno, my_level
                         from (select
                                   modulepref.cny# as cny,
                                   level my_level,
                                   regexp_substr(
                                           modulepref.value,
                                           '[^#~#]+',
                                           1,
                                           level
                                       ) my_acct_no
                               from dual
                               connect by level <= REGEXP_COUNT(modulepref.value, '#~#') + 1
                              ) mytable
                                  inner join baseaccount ba
                                             on
                                                         ba.cny# = mytable.cny
                                                     and ba.acct_no = mytable.my_acct_no
                     )
            )
            where
                    (modulepref.MODULEKEY, modulepref.PROPERTY) in (
                                                                    ('61.SFDC2', 'GLACCOUNTDETAILFIELDS'),
                                                                    ('61.SFDC2', 'STATACCOUNTDETAILFIELDS')
                    );

            update modulepref
            set value = (
                select value
                from tmp_modulepref_ia83163_deleteby0524 t
                where
                        t.cny#=  modulepref.cny#
                  and t.modulekey = modulepref.modulekey
                  and t.property = modulepref.property
                  and rownum = 1
            )
            where modulekey = '2.GL'
              and property = 'UNREALIZED_GAIN_LOSS_ACCT'
              and value is null
              and locationkey is null;
        else
            EXECUTE IMMEDIATE 'ALTER TABLE GLBOOKACCTRATETYPES DROP  CONSTRAINT UQ_GLBOOKACCTRATETYPES DROP INDEX';
            EXECUTE IMMEDIATE 'ALTER TABLE GLBOOKACCTRATETYPES ADD constraint UQ_GLBOOKACCTRATETYPES unique (CNY#, BOOKKEY, GLACCOUNTKEY) USING INDEX TABLESPACE ACCTINDX ENABLE';
        end if;
    end;
/

-- To fix qadb and nov23 schema (failure due to data corruption issue)
DECLARE
    v_column_exists number := 0;
    v_constraint_exists number := 0;
    lv_stmt  varchar2(32767);
BEGIN
    select count(1) into v_column_exists
    FROM ALL_TAB_COLS
    where owner = sys_context('userenv', 'current_schema')
      and upper(TABLE_NAME) = 'TMP_GLBOOKACCTRATETYPES_DEL_IA83163_DELETEBY0524';

    if (v_column_exists = 0) then
        EXECUTE IMMEDIATE 'create table tmp_glbookacctratetypes_del_ia83163_deleteby0524 TABLESPACE ACCTDATA as
            (select * from glbookacctratetypes glbr where glaccountkey is null)';
    end if;

    select count(1) into v_constraint_exists
    FROM ALL_CONSTRAINTS
    where owner = sys_context('userenv', 'current_schema')
      and CONSTRAINT_NAME = 'NN_GLBOOKARTS_GLACCOUNTKEY'
      and upper(TABLE_NAME) = 'GLBOOKACCTRATETYPES';

    if (v_column_exists = 0) then
        EXECUTE IMMEDIATE 'DELETE from glbookacctratetypes glbr where glaccountkey is null';

        EXECUTE IMMEDIATE 'ALTER TABLE GLBOOKACCTRATETYPES ADD constraint NN_GLBOOKARTS_GLACCOUNTKEY check (GLACCOUNTKEY IS NOT NULL)';
        EXECUTE IMMEDIATE 'ALTER TABLE GLBOOKACCTRATETYPES ADD constraint UQ_GLBOOKACCTRATETYPES unique (CNY#, BOOKKEY, GLACCOUNTKEY) USING INDEX TABLESPACE ACCTINDX ENABLE';
        EXECUTE IMMEDIATE 'ALTER TABLE GLBOOKACCTRATETYPES DROP COLUMN GLACCOUNTNO';



        lv_stmt := q'[
DECLARE
    v_cny company.record#%type;
    PropertyKey  varchar2(30) := 'ACCTNO_TO_REC_MIG_IA83163';
    CURSOR allcompanies IS
        SELECT record# cny
        FROM company
        where not exists (
                select 1 from modulepref mpp
                where mpp.cny#= company.record#
                  and mpp.MODULEKEY = '2.GL' AND mpp.PROPERTY = PropertyKey
                  and mpp.VALUE = 'true'
    );
BEGIN

    -- Initialise v_cny and since we are using rollback, so save the changes then proceed
    v_cny := 0;

    BEGIN
        FOR mycompany IN allcompanies
            LOOP
                v_cny := mycompany.cny;
                ----------------------- Migration Start (per cny) -----------------------
                    INSERT INTO MODULEPREF (CNY#, MODULEKEY, PROPERTY, VALUE, LOCATIONKEY, WHENMODIFIED)
                    VALUES (v_cny, '2.GL', PropertyKey, null, null, (CURRENT_TIMESTAMP AT TIME ZONE 'GMT'));
                    COMMIT;

                    -- modulepref data clean up
                    update MODULEPREF set value = trim(SUBSTR(value, 0, INSTR(value, '--')-1))
                    where
                    CNY# = v_cny
                    and value like '%--%' and value not like '#~#'
                    and (MODULEKEY, PROPERTY) in (
                                                                ('11.CM', 'UNDEPACCTNO'),
                                                                ('2.GL', 'AUTOBALANCE_ACCOUNT'),
                                                                ('2.GL', 'ROUNDING_ACCOUNT'),
                                                                ('3.AP', 'AP_RETAINAGE_ACCOUNT'),
                                                                ('3.AP', 'PD_ACCOUNT'),
                                                                ('3.AP', 'PI_ACCOUNT'),
                                                                ('3.AP', 'PM_ACCOUNT'),
                                                                ('3.AP', 'PP_ACCOUNT'),
                                                                ('3.AP', 'PR_ACCOUNT'),
                                                                ('4.AR', 'AR_RETAINAGE_ACCOUNT'),
                                                                ('4.AR', 'AR_RETAINAGE_RELEASE_ACCOUNT'),
                                                                ('4.AR', 'RD_ACCOUNT'),
                                                                ('4.AR', 'RI_ACCOUNT'),
                                                                ('4.AR', 'RM_ACCOUNT'),
                                                                ('4.AR', 'RO_ACCOUNT'),
                                                                ('4.AR', 'RP_ACCOUNT'),
                                                                ('4.AR', 'RRG_ACCOUNT'),
                                                                ('4.AR', 'RR_ACCOUNT'),
                                                                ('40.CCP', 'ACH_GLACCOUNTKEY'),
                                                                ('40.CCP', 'MERCHANT_GLACCOUNTKEY'),
                                                                ('55.CONTRACT', 'ACPENT_CASHACCOUNTNO'),
                                                                ('6.EE', 'EI_ACCOUNT'),
                                                                ('6.EE', 'EM_ACCOUNT'),
                                                                ('6.EE', 'ER_ACCOUNT'),
                                                                ('64.TAX', 'UNRECOVERABLETAXACCTNO'),
                                                                ('7.INV', 'DAM_GLACCOUNT'),
                                                                ('7.INV', 'INV_GLACCOUNT'),
                                                                ('7.INV', 'LANDEDCOSTACCOUNT'),
                                                                ('7.INV', 'LANDEDCOSTACCOUNTESTIMATE'),
                                                                ('7.INV', 'OFF_GLACCOUNT'),
                                                                ('7.INV', 'SCRAP_GLACCOUNT'),
                                                                ('8.SO', 'DIRECT_GLACCOUNT'),
                                                                ('8.SO', 'OFF_GLACCOUNT'),
                                                                ('9.PO', 'DIRECT_GLACCOUNT'),
                                                                ('9.PO', 'MAT_IV_GLACCT'),
                                                                ('9.PO', 'MAT_PV_GLACCT'),
                                                                ('9.PO', 'OFF_GLACCOUNT'),
                                                                ('9.PO', 'THREEWAYGLACCT')
                        );

                    -- We need above script because it will throw this error if we combine both
                    -- ORA-01779: cannot modify a column which maps to a non key-preserved table
                    update (
                        select mp.CNY#, mp.MODULEKEY, mp.PROPERTY, mp.value myvalue, ba.RECORD# myrecordno
                        from (
                                 select mptbl.CNY#, mptbl.MODULEKEY, mptbl.PROPERTY, mptbl.VALUE
                                 from MODULEPREF mptbl
                                 where mptbl.CNY# = v_cny
                                   and (mptbl.MODULEKEY, mptbl.PROPERTY) in (
                                                                             ('11.CM', 'UNDEPACCTNO'),
                                                                             ('2.GL', 'AUTOBALANCE_ACCOUNT'),
                                                                             ('2.GL', 'ROUNDING_ACCOUNT'),
                                                                             ('3.AP', 'AP_RETAINAGE_ACCOUNT'),
                                                                             ('3.AP', 'PD_ACCOUNT'),
                                                                             ('3.AP', 'PI_ACCOUNT'),
                                                                             ('3.AP', 'PM_ACCOUNT'),
                                                                             ('3.AP', 'PP_ACCOUNT'),
                                                                             ('3.AP', 'PR_ACCOUNT'),
                                                                             ('4.AR', 'AR_RETAINAGE_ACCOUNT'),
                                                                             ('4.AR', 'AR_RETAINAGE_RELEASE_ACCOUNT'),
                                                                             ('4.AR', 'RD_ACCOUNT'),
                                                                             ('4.AR', 'RI_ACCOUNT'),
                                                                             ('4.AR', 'RM_ACCOUNT'),
                                                                             ('4.AR', 'RO_ACCOUNT'),
                                                                             ('4.AR', 'RP_ACCOUNT'),
                                                                             ('4.AR', 'RRG_ACCOUNT'),
                                                                             ('4.AR', 'RR_ACCOUNT'),
                                                                             ('40.CCP', 'ACH_GLACCOUNTKEY'),
                                                                             ('40.CCP', 'MERCHANT_GLACCOUNTKEY'),
                                                                             ('55.CONTRACT', 'ACPENT_CASHACCOUNTNO'),
                                                                             ('6.EE', 'EI_ACCOUNT'),
                                                                             ('6.EE', 'EM_ACCOUNT'),
                                                                             ('6.EE', 'ER_ACCOUNT'),
                                                                             ('64.TAX', 'UNRECOVERABLETAXACCTNO'),
                                                                             ('7.INV', 'DAM_GLACCOUNT'),
                                                                             ('7.INV', 'INV_GLACCOUNT'),
                                                                             ('7.INV', 'LANDEDCOSTACCOUNT'),
                                                                             ('7.INV', 'LANDEDCOSTACCOUNTESTIMATE'),
                                                                             ('7.INV', 'OFF_GLACCOUNT'),
                                                                             ('7.INV', 'SCRAP_GLACCOUNT'),
                                                                             ('8.SO', 'DIRECT_GLACCOUNT'),
                                                                             ('8.SO', 'OFF_GLACCOUNT'),
                                                                             ('9.PO', 'DIRECT_GLACCOUNT'),
                                                                             ('9.PO', 'MAT_IV_GLACCT'),
                                                                             ('9.PO', 'MAT_PV_GLACCT'),
                                                                             ('9.PO', 'OFF_GLACCOUNT'),
                                                                             ('9.PO', 'THREEWAYGLACCT')
                                     )
                                   and mptbl.value is not null
                             ) mp
                                 left join baseaccount ba
                                           on mp.cny# = ba.cny# and mp.value = ba.acct_no
                    )
                        mymodulepref
                    set myvalue = myrecordno;

                    update modulepref
                            set value = (
                                            select
                                                   LISTAGG(ba_recordno, '#~#') WITHIN GROUP (ORDER BY my_level asc) final_value
                                            from (
                                                     select ba.record# ba_recordno, my_level
                                                     from (select
                                                                  modulepref.cny# as cny,
                                                                  level my_level,
                                                                  regexp_substr(
                                                                          modulepref.value,
                                                                          '[^#~#]+',
                                                                          1,
                                                                          level
                                                                      ) my_acct_no
                                                           from dual
                                                           connect by level <= REGEXP_COUNT(modulepref.value, '#~#') + 1
                                                          ) mytable
                                                              inner join baseaccount ba
                                                                         on
                                                                                     ba.cny# = mytable.cny
                                                                                 and ba.acct_no = mytable.my_acct_no
                                                 )
                                        )
                    where  modulepref.cny# = v_cny
                    and (modulepref.MODULEKEY, modulepref.PROPERTY) in (
                                                                            ('61.SFDC2', 'GLACCOUNTDETAILFIELDS'),
                                                                            ('61.SFDC2', 'STATACCOUNTDETAILFIELDS')
                                                                        );

                    UPDATE MODULEPREF set VALUE = 'true'
                    WHERE CNY# = v_cny and MODULEKEY = '2.GL' AND PROPERTY = PropertyKey;

                    ----------------------- Migration End (per cny) --------Fix-A2---------------
                    COMMIT;

            END LOOP;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            raise_application_error(-20001,'GL Account ID change cny# '||v_cny|| '-CODE-'|| SQLCODE||' -ERROR- '||SQLERRM);
    END;
END;]';

        execute immediate lv_stmt;

    end if;

END;
/