-- liquibase formatted sql
-- changeset anoop.singh:20230824.101244.001 runOnChange:false logicalFilePath:V2308.0.20230824.101244.001__pcb_inv_details_schema.sql
-- <PERSON>ra story IA-101244
-- Added by: <EMAIL> An<PERSON> Singh
-- To create PCB_INVOICE_DETAILS, which will store PCB invoice calculated details

CREATE TABLE PCB_INVOICE_DETAILS
(
    CNY#                NUMBER(15, 0) CONSTRAINT NN_PCB_INV_DETAILS_CNY NOT NULL ENABLE,
    DOCHDRKEY           NUMBER(15, 0) CONSTRAINT NN_PCB_INV_DETAILS_DOCHDRKEY NOT NULL ENABLE,
    DOCNO               VARCHAR2(30 CHAR),
    PRJCONTRACTKEY      NUMBER(15, 0) CONSTRAINT NN_PCB_INV_DETAILS_PCKEY NOT NULL ENABLE,
    PRJCTRCTLINEKEY     NUMBER(15, 0) CONSTRAINT NN_PCB_INV_DETAILS_PCLKEY NOT NULL ENABLE,
    PRJCONTRACTID       VARCHAR2(20 CHAR),
    PRJCONTRACTLINEID   VARCHAR2(20 CHAR),
    EXTERNALREF         VARCHAR2(100 CHAR),
    INTERNALREF         VARCHAR2(100 CHAR),
    PROJECTKEY          NUMBER(15, 0),
    PROJECTID           VARCHAR2(20 CHAR),
    TASKKEY             NUMBER(15, 0),
    TASKID              VARCHAR2(20 CHAR),
    BILLABLE            CHAR(1 CHAR) DEFAULT ('F'),
    ORGCONTRACTAMT      NUMBER(14, 2),
    CHGAPPRPMONTADD     NUMBER(14, 2),
    CHGAPPRPMONTDED     NUMBER(14, 2),
    CHGAPPRCMONTADD     NUMBER(14, 2),
    CHGAPPRCMONTDED     NUMBER(14, 2),
    TOTNETAPPRCHNGS     NUMBER(14, 2),
    TOTREVSDCONAMT      NUMBER(14, 2),
    COMPTHISPERIOD      NUMBER(14, 2),
    STREDMATERIALS      NUMBER(14, 2),
    TOTCOMPTODATE       NUMBER(14, 2),
    COMPPRIORAPPLNS     NUMBER(14, 2),
    PERCNTAGECOMPTD     NUMBER(14, 2),
    RETPERCENTAGE       NUMBER(14, 2),
    INVAMTRETAINED      NUMBER(14, 2),
    INVRETAINGEBILLD    NUMBER(14, 2),
    NETCHNGERETHELD     NUMBER(14, 2),
    RETHELDTODATE       NUMBER(14, 2),
    RETBILLEDTODATE     NUMBER(14, 2),
    RETBALANCETODATE    NUMBER(14, 2),
    PREVRETBALANCE      NUMBER(14, 2),
    TOTALBILLED         NUMBER(14, 2),
    TAXES               NUMBER(14, 2),
    CHARGES             NUMBER(14, 2),
    DISCOUNTS           NUMBER(14, 2),
    LOCATIONKEY         NUMBER(15, 0) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),
    WHENCREATED         DATE   DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT' CONSTRAINT NN_PCB_INV_DETAILS_WHENCREATED NOT NULL ENABLE,
    WHENMODIFIED        DATE   DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT' CONSTRAINT NN_PCB_INV_DETAILS_WHENMODIFIED NOT NULL ENABLE,
    CREATEDBY           NUMBER(15, 0),
    MODIFIEDBY          NUMBER(15, 0),
    SI_UUID             VARCHAR2(36 CHAR),
    CONSTRAINT PK_PCB_INVOICE_DETAILS PRIMARY KEY (CNY#, DOCHDRKEY, PRJCONTRACTKEY, PRJCTRCTLINEKEY) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_PCB_INV_DETAILS_CNY FOREIGN KEY(CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PCB_INV_DETAILS_DOCHDRKEY FOREIGN KEY (CNY#, DOCHDRKEY) REFERENCES DOCHDR (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PCB_INV_DETAILS_PCKEY FOREIGN KEY (CNY#, PRJCONTRACTKEY) REFERENCES PROJECTCONTRACT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PCB_INV_DETAILS_PCLKEY FOREIGN KEY (CNY#, PRJCTRCTLINEKEY) REFERENCES PROJECTCONTRACTLINE (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PCB_INV_DETAILS_PROJECTKEY FOREIGN KEY (CNY#, PROJECTKEY) REFERENCES PROJECT (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PCB_INV_DETAILS_TASKKEY FOREIGN KEY (CNY#, TASKKEY) REFERENCES TASK (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT CK_PCB_INV_DETAILS_BILLABLE CHECK (BILLABLE IN ('T','F')) ENABLE VALIDATE,
    CONSTRAINT FK_PCB_INV_DETAILS_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PCB_INV_DETAILS_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/

CREATE INDEX IX_PCB_INV_DETAILS_CNY ON PCB_INVOICE_DETAILS (CNY#) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PCB_INV_DETAILS_DOCHDRKEY ON PCB_INVOICE_DETAILS (CNY#, DOCHDRKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PCB_INV_DETAILS_PCKEY ON PCB_INVOICE_DETAILS (CNY#, PRJCONTRACTKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PCB_INV_DETAILS_PCLKEY ON PCB_INVOICE_DETAILS (CNY#, PRJCTRCTLINEKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PCB_INV_DETAILS_PROJECTKEY ON PCB_INVOICE_DETAILS (CNY#, PROJECTKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PCB_INV_DETAILS_TASKKEY ON PCB_INVOICE_DETAILS (CNY#, TASKKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_PCB_INV_DETAILS_CREATEDBY ON PCB_INVOICE_DETAILS ( CNY#, CREATEDBY ) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_PCB_INV_DETAILS_MODIFIEDBY ON PCB_INVOICE_DETAILS ( CNY#, MODIFIEDBY ) TABLESPACE ACCTINDX INVISIBLE
/