--liquibase formatted sql
--changeset mjagadish:20210930.42904.001 runOnChange:false logicalFilePath:V20210930.42904.002__item_cross_reference_enhancement_post.sql

BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

-- update vendorkey with vendor.record#
UPDATE (
    SELECT
        v.record#   v_record#,
        i.vendorkey
    FROM
        icitemcrossref i
        INNER JOIN vendor v ON v.cny# = i.cny#
                               AND v.vendorid = i.vendorid
    WHERE
        i.vendorid IS NOT NULL
) itemcross
SET
    itemcross.vendorkey = itemcross.v_record#
/


-- update customerkey with customer.record#
UPDATE (
    SELECT
        c.record#   c_record#,
        i.customerkey
    FROM
        icitemcrossref i
        INNER JOIN customer c ON c.cny# = i.cny#
                               AND c.customerid = i.customerid
    WHERE
        i.customerid IS NOT NULL
) itemcross
SET
    itemcross.customerkey = itemcross.c_record#
/

BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/