--liquibase formatted sql
--changeset <PERSON><PERSON>:20241120.200277.001 runOnChange:false
--logicalFilePath:V2502.0.20241120.200277.001__add_customer_refund_fields_in_pymtdetail
--OWNER_CONTACT: raj<PERSON>.<EMAIL>

-- add new column in pymtdetail
DECLARE
n pls_integer :=0;
BEGIN
    SELECT count(*) into n FROM all_tab_cols WHERE owner = sys_context('userenv', 'current_schema')
    AND table_name = 'PYMTDETAIL' AND column_name IN ('REFUNDKEY');
    IF n=0 then
         EXECUTE IMMEDIATE 'ALTER TABLE PYMTDETAIL ADD (
            REFUNDKEY NUMBER(15, 0),
            REFUNDENTRYKEY NUMBER(15, 0),
            REFUNDAMOUNT NUMBER,
            TRX_REFUNDAMOUNT NUMBER
         )';
    END IF;
END;
/
----------------------<PERSON>ND OF MIGRATION----------------------------------------