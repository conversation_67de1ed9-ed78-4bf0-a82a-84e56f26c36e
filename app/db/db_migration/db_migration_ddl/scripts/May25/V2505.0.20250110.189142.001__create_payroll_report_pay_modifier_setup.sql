--OWNER_CONTACT:<EMAIL>

/*
== DELTA ONLY FOR DEV SCHEMAS ==

Description: add on cascade delete to the foreign key constraints, and add missing FK relationships

Repair action: EXECUTE
Repair action: EXECUTE

*/

CREATE TABLE PAYROLLREPORTPAYMODIFIERSETUP(
    CNY# NUMBER(15) CONSTRAINT NN_PYLRTPMSETUP_CNY NOT NULL ENABLE,
    RECORD# NUMBER(15) CONSTRAINT NN_PYLRTPMSETUP_RECORD# NOT NULL ENABLE,
    PAYMODIFIERID VARCHAR2(100 CHAR) CONSTRAINT NN_PYLRTPMSETUP_PAYMODIFIERID NOT NULL ENABLE,
    PAYCATEGORY VARCHAR2(100 CHAR),
    PAYMODIFIERTYPE VARCHAR2(100 CHAR),
    RECORDTYPE VARCHAR2(100 CHAR),
    SYSTEMPAYMODIFIERKEY NUMBER(15, 0) DEFAULT NULL,
    PAYROLLREPORTEMPLOYEEKEY NUMBER(15, 0) DEFAULT NULL,
    COMPENSATIONTRADEKEY NUMBER(15, 0) DEFAULT NULL,
    COMPENSATIONTRADELEVELKEY NUMBER(15, 0) DEFAULT NULL,
    EFFECTIVEDATE DATE,
    EXPIRATIONDATE DATE,
    REVISIONNUMBER NUMBER(30, 10),
    AFFECTSTAXABLEEARNINGSFOR VARCHAR2(200 CHAR),
    AFFECTSNET CHAR(1 CHAR) DEFAULT NULL,
    MAXAMOUNTPERPERIOD NUMBER(30, 10),
    MAXPERCENTOFBASIS NUMBER(30, 10),
    MAXPERCENTOFAVAILABLEFUNDS NUMBER(30, 10),
    LIFETIMELIMITAMOUNT NUMBER(30, 10),
    AVAILABLEFUNDSINEXCESSOF NUMBER(30, 10),
    MAXPERCENTOFAVAILABLEFUNDSINEXCESSOF NUMBER(30, 10),
    CONTRIBUTIONLIMIT NUMBER(30, 10),
    CATCHUPCONTRIBUTIONLIMIT NUMBER(30, 10),
    CONTRIBUTIONPERCENTOFEARNINGSLIMIT NUMBER(30, 10),
    ALLSOURCESLIMIT NUMBER(30, 10),
    MATCHINGAPPLIESTOCATCHUPCONTRIBUTION NUMBER(30, 10),
    MAXIMUMEARNINGSSUBJECTTOMATCHING NUMBER(30, 10),
    COMPLIANCEREPORT CHAR(1 CHAR) DEFAULT NULL,
    ISUNIONDUES CHAR(1 CHAR) DEFAULT NULL,
    TAXTYPENUMBER VARCHAR2(100 CHAR),
    PRETAXTAXTYPENO VARCHAR2(100 CHAR),
    PRETAXMATCHTAXTYPENO VARCHAR2(100 CHAR),
    AFTERTAXTAXTYPENO VARCHAR2(100 CHAR),
    AFTERTAXMATCHTAXTYPENO VARCHAR2(100 CHAR),
    LOCATIONKEY NUMBER(15, 0) DEFAULT sys_context('TMCtx', 'LOCATIONKEY'),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    CREATEDBY NUMBER(15, 0),
    MODIFIEDBY NUMBER(15, 0),
    SI_UUID VARCHAR2(36 CHAR),
    CONSTRAINT PK_PYLRTPMSETUP PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_PYLRTPMSETUP_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPMSETUP_EMPLOYEEKEY FOREIGN KEY (CNY#, PAYROLLREPORTEMPLOYEEKEY) REFERENCES PAYROLLREPORTEMPLOYEE (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPMSETUP_COMPTRDKEY FOREIGN KEY (CNY#, COMPENSATIONTRADEKEY) REFERENCES PAYROLLREPORTCOMPENSATIONTRADE (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPMSETUP_COMPTRDLVLKEY FOREIGN KEY (CNY#, COMPENSATIONTRADELEVELKEY) REFERENCES PAYROLLREPORTCOMPENSATIONTRADELEVEL (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPMSETUP_SYSPAYMODKEY FOREIGN KEY (CNY#, SYSTEMPAYMODIFIERKEY) REFERENCES PAYROLLREPORTPAYMODIFIERSETUP (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPMSETUP_LOCATIONKEY FOREIGN KEY (CNY#, LOCATIONKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPMSETUP_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_PYLRTPMSETUP_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT UQ_PYLRTPMSETUP_PAYMFID_EFDATE UNIQUE (CNY#, PAYMODIFIERID, RECORDTYPE, EFFECTIVEDATE) USING INDEX TABLESPACE ACCTINDX ENABLE
)TABLESPACE ACCTDATA
/
CREATE INDEX IX_PYLRTPMSETUP_CNY ON PAYROLLREPORTPAYMODIFIERSETUP (CNY#) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMSETUP_LOCATIONKEY ON PAYROLLREPORTPAYMODIFIERSETUP (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMSETUP_EMPLOYEEKEY ON PAYROLLREPORTPAYMODIFIERSETUP (CNY#, PAYROLLREPORTEMPLOYEEKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMSETUP_COMPTRDKEY ON PAYROLLREPORTPAYMODIFIERSETUP (CNY#, COMPENSATIONTRADEKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMSETUP_COMPTRDLVLKEY ON PAYROLLREPORTPAYMODIFIERSETUP (CNY#, COMPENSATIONTRADELEVELKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMSETUP_SYSPAYMODKEY ON PAYROLLREPORTPAYMODIFIERSETUP (CNY#, SYSTEMPAYMODIFIERKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMSETUP_CREATEDBY ON PAYROLLREPORTPAYMODIFIERSETUP ( CNY#, CREATEDBY ) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMSETUP_MODIFIEDBY ON PAYROLLREPORTPAYMODIFIERSETUP ( CNY#, MODIFIEDBY ) TABLESPACE ACCTINDX INVISIBLE
/


CREATE TABLE PAYROLLREPORTPAYMODIFIERBASIS(
      CNY# NUMBER(15) CONSTRAINT NN_PYLRTPMBASIS_CNY NOT NULL ENABLE,
      RECORD# NUMBER(15) CONSTRAINT NN_PYLRTPMBASIS_RECORD# NOT NULL ENABLE,
      PAYMODIFIERKEY NUMBER(15, 0) CONSTRAINT NN_PYLRTPMBASIS_PAYMODIFIERKEY NOT NULL ENABLE,
      DEFINITIONTYPE VARCHAR2(100 CHAR),
      BASISCONTRIBUTORTYPE VARCHAR2(100 CHAR),
      CONTRIBUTORLIST VARCHAR2(200 CHAR),
      INVERT CHAR(1 CHAR) DEFAULT NULL,
      WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
      WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
      CREATEDBY NUMBER(15, 0),
      MODIFIEDBY NUMBER(15, 0),
      SI_UUID VARCHAR2(36 CHAR),
      CONSTRAINT PK_PYLRTPMBASIS PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
      CONSTRAINT FK_PYLRTPMBASIS_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
      CONSTRAINT FK_PYLRTPMBASIS_PAYMODIFIERKEY FOREIGN KEY (CNY#, PAYMODIFIERKEY) REFERENCES PAYROLLREPORTPAYMODIFIERSETUP (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
      CONSTRAINT FK_PYLRTPMBASIS_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
      CONSTRAINT FK_PYLRTPMBASIS_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
      CONSTRAINT UQ_PYLRTPMBASIS_PAYMFID_DEF UNIQUE (CNY#, PAYMODIFIERKEY, DEFINITIONTYPE, BASISCONTRIBUTORTYPE, CONTRIBUTORLIST, INVERT) USING INDEX TABLESPACE ACCTINDX ENABLE
)TABLESPACE ACCTDATA
/
CREATE INDEX IX_PYLRTPMBASIS_CNY ON PAYROLLREPORTPAYMODIFIERBASIS (CNY#) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMBASIS_PAYMODIFIERKEY ON PAYROLLREPORTPAYMODIFIERBASIS (CNY#, PAYMODIFIERKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMBASIS_CREATEDBY ON PAYROLLREPORTPAYMODIFIERBASIS (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMBASIS_MODIFIEDBY ON PAYROLLREPORTPAYMODIFIERBASIS (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/


CREATE TABLE PAYROLLREPORTPAYMODIFIERRATES(
      CNY# NUMBER(15) CONSTRAINT NN_PYLRTPMRATES_CNY NOT NULL ENABLE,
      RECORD# NUMBER(15) CONSTRAINT NN_PYLRTPMRATES_RECORD# NOT NULL ENABLE,
      PAYMODIFIERKEY NUMBER(15, 0) CONSTRAINT NN_PYLRTPMRATES_PAYMODIFIERKEY NOT NULL ENABLE,
      WORKEDHOURSID VARCHAR2(100 CHAR),
      AMOUNTPERHOUR NUMBER(30, 10),
      MAXHOURSPERPERIOD NUMBER(30, 10),
      MAXHOURSPERYEAR NUMBER(30, 10),
      MATCHINGRATE NUMBER(30, 10),
      MATCHINGMAXDEFERMENTRATE NUMBER(30, 10),
      WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
      WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
      CREATEDBY NUMBER(15, 0),
      MODIFIEDBY NUMBER(15, 0),
      SI_UUID VARCHAR2(36 CHAR),
      CONSTRAINT PK_PYLRTPMRATES PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
      CONSTRAINT FK_PYLRTPMRATES_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
      CONSTRAINT FK_PYLRTPMRATES_PAYMODIFIERKEY FOREIGN KEY (CNY#, PAYMODIFIERKEY) REFERENCES PAYROLLREPORTPAYMODIFIERSETUP (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
      CONSTRAINT FK_PYLRTPMRATES_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
      CONSTRAINT FK_PYLRTPMRATES_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO(CNY#, RECORD#) DEFERRABLE ENABLE,
      CONSTRAINT UQ_PYLRTPMRATES_PAYMFID_WRKHID UNIQUE (CNY#, PAYMODIFIERKEY, WORKEDHOURSID, MATCHINGRATE) USING INDEX TABLESPACE ACCTINDX ENABLE
)TABLESPACE ACCTDATA
/
CREATE INDEX IX_PYLRTPMRATES_CNY ON PAYROLLREPORTPAYMODIFIERRATES (CNY#) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMRATES_PAYMODIFIERKEY ON PAYROLLREPORTPAYMODIFIERRATES (CNY#, PAYMODIFIERKEY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMRATES_CREATEDBY ON PAYROLLREPORTPAYMODIFIERRATES (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/
CREATE INDEX IX_PYLRTPMRATES_MODIFIEDBY ON PAYROLLREPORTPAYMODIFIERRATES (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/