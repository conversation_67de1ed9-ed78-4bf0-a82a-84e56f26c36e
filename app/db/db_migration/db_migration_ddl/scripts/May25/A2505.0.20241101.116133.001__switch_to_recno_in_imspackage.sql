--OWNER_CONTACT: <EMAIL>

-- IMSDELIVERY CHANGES
-- 1.1 DROP CONSTRAINT "PK_IMSDELIVERY" PRIMARY KEY (IMSPACKAGEKEY, TIMESTAMP) to set the new one based on (cny#, packageid, timestamp)
DECLARE
nCount pls_integer := 0;
BEGIN
SELECT count(*) into nCount from all_constraints where owner = sys_context('userenv', 'current_schema') and table_name = 'IMSDELIVERY' and constraint_name= 'PK_IMSDELIVERY';
IF nCount = 1 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE IMSDELIVERY DROP CONSTRAINT PK_IMSDELIVERY DROP INDEX';
END IF;
END;
/

-- IMSDELIVERY changes
-- 1.2 Drop the FK_IMSDELIVER_IMSPACKAGEKEY constraint if it exists
DECLARE
nCount pls_integer := 0;
BEGIN
SELECT count(*) into nCount from all_constraints where owner = sys_context('userenv', 'current_schema') and table_name = 'IMSDELIVERY' and constraint_name= 'FK_IMSDELIVER_IMSPACKAGEKEY';
IF nCount = 1 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE IMSDELIVERY DROP CONSTRAINT FK_IMSDELIVER_IMSPACKAGEKEY';
END IF;
END;
/

-- IMSDELIVERY changes
-- 1.3 Drop the index for imsdelivery imspackagekey and change to include CNY# column
DECLARE
index_exists number := 0;
BEGIN
SELECT count(*) INTO index_exists
FROM all_indexes WHERE owner = sys_context('userenv', 'current_schema')
                   AND index_name = 'IX_IMSDELIVER_IMSPACKAGEKEY' AND table_name = 'IMSDELIVERY';

IF (index_exists = 1) THEN
      EXECUTE IMMEDIATE 'DROP INDEX IX_IMSDELIVER_IMSPACKAGEKEY';
END IF;
END;
/

-- IMSPACKAGE changes
-- 3.1 DROP the PK_IMSPACKAGE constraint if it exists on the schemas
DECLARE
nCount pls_integer := 0;
BEGIN
SELECT count(*) into nCount from all_constraints where owner = sys_context('userenv', 'current_schema') and table_name = 'IMSPACKAGE' and constraint_name= 'PK_IMSPACKAGE';
IF nCount = 1 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE IMSPACKAGE DROP CONSTRAINT PK_IMSPACKAGE DROP INDEX';
END IF;
END;
/

-- 2.2 Add the PK_IMSDELIVERY constraint based on (cny#, packageid, timestamp)
ALTER TABLE IMSDELIVERY
    ADD CONSTRAINT PK_IMSDELIVERY PRIMARY KEY (CNY#, IMSPACKAGEKEY, TIMESTAMP) USING INDEX TABLESPACE ACCTIMS
/

-- 3.2 ADD the PK_IMSPACKAGE constraint based on (cny#, packageid)
ALTER TABLE IMSPACKAGE
    ADD CONSTRAINT PK_IMSPACKAGE PRIMARY KEY (CNY#, PACKAGEID) USING INDEX TABLESPACE ACCTIMS
/

-- 2.3 Add the FK_IMSDELIVER_IMSPACKAGEKEY constraint based on (cny#, packageid)
ALTER TABLE IMSDELIVERY
    ADD CONSTRAINT FK_IMSDELIVER_IMSPACKAGEKEY FOREIGN KEY (CNY#, IMSPACKAGEKEY) REFERENCES IMSPACKAGE (CNY#, PACKAGEID) ON DELETE CASCADE DEFERRABLE ENABLE VALIDATE
/

CREATE INDEX IX_IMSDELIVER_IMSPACKAGEKEY ON IMSDELIVERY (CNY#, IMSPACKAGEKEY) TABLESPACE ACCTIMS ONLINE
/