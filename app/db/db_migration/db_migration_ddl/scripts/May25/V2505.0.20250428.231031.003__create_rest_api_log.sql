--OWNER_CONTACT: <EMAIL>

-- drop table REST_API_LOG if exists
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE REST_API_LOG CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -942 THEN
            RAISE;
        END IF;
END;
/

-- Create REST_API_LOG table to track API usage with named constraints
CREATE TABLE REST_API_LOG (
    RECORD# NUMBER(15,0) CONSTRAINT NN_REST_API_LOG_RECORD NOT NULL ENABLE,
    LOG_TYPE VARCHAR2(20 CHAR) CONSTRAINT NN_REST_API_LOG_LOG_TYPE NOT NULL ENABLE,
    API_NAME VARCHAR2(100 CHAR) CONSTRAINT NN_REST_API_LOG_API_NAME NOT NULL ENABLE,
    HTTP_METHOD VARCHAR2(20 CHAR) CONSTRAINT NN_REST_API_LOG_HTTP_METHOD NOT NULL ENABLE,
    REQUEST_TYPE VARCHAR2(20 CHAR) CONSTRAINT NN_REST_API_LOG_REQUEST_TYPE NOT NULL ENABLE,
    START_TIME DATE CONSTRAINT NN_REST_API_LOG_START_TIME NOT NULL ENABLE,
    END_TIME DATE CONSTRAINT NN_REST_API_LOG_END_TIME NOT NULL ENABLE,
    NO_OF_REQUESTS NUMBER(10) CONSTRAINT NN_REST_API_LOG_NO_OF_REQUESTS NOT NULL ENABLE,
    HTTP_STATUS VARCHAR2(20 CHAR) CONSTRAINT NN_REST_API_LOG_HTTP_STATUS NOT NULL ENABLE,
    CNY# NUMBER(15,0) CONSTRAINT NN_REST_API_LOG_CNY NOT NULL ENABLE,
    LOGINID VARCHAR2(200 CHAR) CONSTRAINT NN_REST_API_LOG_LOGINID NOT NULL ENABLE,
    CLIENT_ID VARCHAR2(80 CHAR) CONSTRAINT NN_REST_API_LOG_CLIENT_ID NOT NULL ENABLE,
    API_VERSION VARCHAR2(20 CHAR) CONSTRAINT NN_REST_API_LOG_API_VERSION NOT NULL ENABLE,
    ADDITIONAL_INFO VARCHAR2(2000 CHAR),
    SI_UUID VARCHAR2(36 CHAR),
    CONSTRAINT PK_REST_API_LOG PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_REST_API_LOG_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
) TABLESPACE ACCTDATA
/