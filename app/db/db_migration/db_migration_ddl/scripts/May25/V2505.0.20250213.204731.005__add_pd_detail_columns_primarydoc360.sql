--OWNER_CONTACT: <EMAIL>
/*
== DELTA ONLY FOR DEV SCHEMAS ==
Description:
Repair action: IGNORE
*/

ALTER TABLE PRIMARY_DOC_SUMMARY DROP CONSTRAINT FK_PRIMARY_DOC_SUMMARY_RECORD
/

ALTER TABLE PRIMARY_DOC_SUMMARY ADD CONSTRAINT FK_PRIMARY_DOC_SUMMARY_DOCKEY FOREIGN KEY (CNY#, DOCKEY) REFERENCES DOCHDR (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
/

ALTER TABLE PRIMARY_DOC_DETAILS ADD (
    DOCEN<PERSON>YKEY NUMBER(15, 0),
    DOCNAME VARCHAR2(30 CHAR),
    PROJECTID VARCHAR2(20 CHAR),
    PROJECTNAME VARCHAR2(200 CHAR),
    TASKID VARCHAR2(20 CHAR),
    TASKNAME VARCHAR2(200 CHAR),
    COSTTYPEID VARCHAR2(20 CHAR),
    COSTTYPENAME VARCHAR2(200 CHAR),
    ITEMID VARCHAR2(20 CHAR),
    ITEMNAME VARCHAR2(200 CHAR),
    LOCATIONNAME VARCHAR2(100 CHAR),
    DEPARTMENTKEY NUMBER(15, 0),
    DEPARTMENTNAME VARCHAR2(40 CHAR)
)
/

ALTER TABLE PRIMARY_DOC_DETAILS DROP CONSTRAINT FK_PRIMARY_DOC_DETAILS_RECORD
/

ALTER TABLE PRIMARY_DOC_DETAILS ADD CONSTRAINT FK_PRIMARY_DOC_DETAILS_DOCKEY FOREIGN KEY (CNY#, DOCKEY) REFERENCES DOCHDR (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
/

ALTER TABLE PRIMARY_DOC_DETAILS ADD CONSTRAINT FK_PRIMARY_DOC_DETAILS_DOCENTRYKEY FOREIGN KEY (CNY#, DOCENTRYKEY) REFERENCES DOCENTRY (CNY#, RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE
/
