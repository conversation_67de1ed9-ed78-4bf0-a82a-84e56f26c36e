--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PROC_POPULATE_CCPAYMENTRECORDS.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE PROCEDURE "POPULATE_CCPAYMENTRECORDS" 
	(
		C<PERSON><PERSON><PERSON>		IN CCPAYMENTRECORDS.CNY#%TYPE,
		CardID		IN CCPAYMENTRECORDS.CARDID%TYPE,
		PaymentKey	IN CCPAYMENTRECORDS.PAYMENTKEY%TYPE,
		ChargeKey	IN CCPAYMENTRECORDS.CHARGEKEY%TYPE
	) IS

	INS_STATUS CHAR(1);

BEGIN
	INSERT INTO CCPAYMENTRECORDS (CNY#, CARD<PERSON>, PAYMENTKEY, PAYITEMKEY, CHARGEKEY, CHARGEITEMKEY, AMOUNT )
	SELECT	distinct
		CnyKey, CardID, payline.recordkey, payline.record#, chargeline.recordkey, chargeline.record#, chargeline.amount
	FROM
		(
			select rownum seq, record#, recordkey, lineitem, accountkey, location#, dept#, amount
			from (
				select	record#, recordkey, lineitem, accountkey, location#, dept#, amount
				from	prentry
				where	cny# = CnyKey and recordkey = PaymentKey
				order by record#, recordkey, lineitem, accountkey, location#, dept#, amount
			)
		) payline,
		(
			select rownum seq, record#, recordkey, lineitem, accountkey, location#, dept#, amount
			from (
				select	record#, recordkey, lineitem, accountkey, location#, dept#, amount
				from	prentry
				where	cny# = CnyKey and recordkey = ChargeKey
				order by record#, recordkey, lineitem, accountkey, location#, dept#, amount
			)
		) chargeline
	WHERE
		payline.lineitem = chargeline.lineitem
		and payline.accountkey = chargeline.accountkey
		and nvl(payline.location#, 0) = nvl(chargeline.location#, 0)
		and nvl(payline.dept#, 0) = nvl(chargeline.dept#, 0)
		and payline.amount = chargeline.amount
		and payline.seq = chargeline.seq;

	SELECT	(CASE WHEN COUNT(CCPAYMENTRECORDS.PAYITEMKEY) !=  COUNT(PRENTRY.RECORD#) THEN 1 ELSE 0 END) INTO INS_STATUS
	FROM	CCPAYMENTRECORDS, PRENTRY
	WHERE	CCPAYMENTRECORDS.CNY# = CnyKey
		AND CCPAYMENTRECORDS.CARDID = CardID
		AND CCPAYMENTRECORDS.PAYMENTKEY = PaymentKey
		AND PRENTRY.CNY# = CnyKey AND PRENTRY.RECORDKEY = ChargeKey;

	IF ( INS_STATUS = '1' ) THEN
		RAISE_APPLICATION_ERROR(-20001, 'Internal Error: CCPAYMENTRECORDS Link Table Population is incorrect.');
	END IF;

END POPULATE_CCPAYMENTRECORDS;





 
 
/
