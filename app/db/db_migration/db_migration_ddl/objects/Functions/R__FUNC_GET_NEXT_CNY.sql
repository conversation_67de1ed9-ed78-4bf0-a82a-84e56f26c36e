--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__FUNC_GET_NEXT_CNY.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE function GET_NEXT_CNY return number as
  tmp_number number ;
  begin
    SELECT seq_companyid.nextval@DBLINK_GLOB into tmp_number
    FROM    DUAL ;
    SELECT nvl(min(podid), 0) * 1000000000 + tmp_number
    INTO tmp_number FROM pod where is_current = 'Y';
    return tmp_number ;
  end;
/
