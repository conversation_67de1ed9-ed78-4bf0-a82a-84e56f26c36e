--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__FUNC_CHECK_VALID_APPROVERS.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE FUNCTION check_valid_approvers (cny IN NUMBER) RETURN NUMBER
IS
  isvalid           NUMBER;
  v_property        VARCHAR2(40);
  v_actual_rulename VARCHAR2(200);

BEGIN
    isvalid := 1;
    FOR ROW IN (
      SELECT property, value
      FROM modulepref
      WHERE cny# = cny AND modulekey = '9.PO' AND locationkey IS NULL AND property LIKE 'APPROVAL%'
    )
    LOOP
      IF (row.value IS NULL)
      THEN
        dbms_output.put_line(row.property || ' contains NULL approval value !');
        isvalid := 0;
        EXIT;
      END IF;
    END LOOP;
    RETURN isvalid;
END;
/
