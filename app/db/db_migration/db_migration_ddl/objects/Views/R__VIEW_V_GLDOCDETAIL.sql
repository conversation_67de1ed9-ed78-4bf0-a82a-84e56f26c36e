--OWNER_CONTACT:<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>


--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_GLDOCDETAIL.sql
--

CREATE OR REPLACE FORCE EDITIONABLE VIEW V_GLDOCDETAIL (
    cny#,
    recordno,
    batch_date,
    batch_no,
    batch_title,
    journal#,
    modulekey,
    childentity,
    userkey,
    modified,
    referenceno,
    prbatchkey,
    ownershipkey,
    periodkey,
    bookid,
    batch#,
    adj,
    record#,
    line_no,
    entry_date,
    tr_type,
    document,
    account#,
    cleared,
    clrdate,
    timeperiod,
    location#,
    dept#,
    customerdimkey,
    projectdimkey,
    vendordimkey,
    employeedimkey,
    itemdimkey,
    classdimkey,
    contractdimkey,
    taskdimkey,
    warehousedimkey,
    costtypedimkey,
    class2dimkey,
    assetdimkey,
    affiliateentitydimkey,
    workorderdimkey,
--NEWDIMTOOL-    newstddimkey,
--*** DO NOT alter/remove the above line.
    basecurr,
    currency,
    description,
    debitamount,
    creditamount,
    amount,
    trx_debitamount,
    trx_creditamount,
    trx_amount,
    debitaccount#,
    creditaccount#,
    warehousekey,
    itemkey,
    subledgeritemkey,
    quantity,
    value,
    unitcost,
    cost,
    in_out,
    entrydescription,
    docid,
    whencreated,
    whenmodified,
    rel1, rel2, rel3, rel4, rel5, rel6, rel7, rel8, rel9, rel10, rel11, rel12, rel13, rel14, rel15, rel16, rel17, rel18,
    rel19, rel20, rel21, rel22, rel23, rel24, rel25, rel26, rel27, rel28, rel29, rel30, rel31, rel32, rel33, rel34,
    rel35, rel36, rel37, rel38, rel39, rel40, rel41, rel42, rel43, rel44, rel45, rel46, rel47, rel48, rel49, rel50,
    rel51, rel52, rel53, rel54, rel55, rel56, rel57, rel58, rel59, rel60, rel61, rel62, rel63, rel64, rel65, rel66,
    rel67, rel68, rel69, rel70, rel71, rel72, rel73, rel74, rel75, rel76, rel77, rel78, rel79, rel80, rel81, rel82,
    rel83, rel84, rel85, rel86, rel87, rel88, rel89, rel90, rel91, rel92, rel93, rel94, rel95, rel96, rel97, rel98,
    rel99, rel100,
    uddrel1, uddrel2, uddrel3, uddrel4, uddrel5, uddrel6, uddrel7, uddrel8, uddrel9, uddrel10, uddrel11, uddrel12,
    uddrel13, uddrel14, uddrel15, uddrel16, uddrel17, uddrel18, uddrel19, uddrel20
) AS
    SELECT
        glb.cny#,
        gle.batch#
        || '-'
        || gle.record#
        || '-'
        || r.docentrykey
        || '-'
        || r.cogsposting
        || '-'
        || lower(bj.bookid) AS recordno,
        glb.batch_date,
        glb.batch_no,
        glb.batch_title,
        glb.journal#,
        glb.modulekey,
        glb.childentity,
        glb.userkey,
        glb.modified,
        glb.referenceno,
        glb.prbatchkey,
        glb.locationkey ownershipkey,
        (
            SELECT
                MAX(p.record#)
            FROM
                glbudgettype p
            WHERE
                p.budgeting = 'T'
                AND   p.cny# = gle.cny#
                AND   gle.entry_date BETWEEN p.start_date AND p.end_date
        ) periodkey,
        bj.bookid,
        gle.batch#,
        gle.adj,
        gle.record#,
        gle.line_no,
        gle.entry_date,
        gle.tr_type,
        gle.document,
        gle.account#,
        gle.cleared,
        gle.clrdate,
        gle.timeperiod,
        gle.location#,
        gle.dept#,
        gle.customerdimkey,
        gle.projectdimkey,
        gle.vendordimkey,
        gle.employeedimkey,
        gle.itemdimkey,
        gle.classdimkey,
        gle.contractdimkey,
        gle.taskdimkey,
        gle.warehousedimkey,
        gle.costtypedimkey,
        gle.class2dimkey,
        gle.assetdimkey,
        gle.affiliateentitydimkey,
        gle.workorderdimkey,
--NEWDIMTOOL-        gle.newstddimkey,
--*** DO NOT alter/remove the above line.            
        gle.basecurr,
        nvl(r.currency,gle.currency) AS currency,
        nvl(r.description,gle.description) AS description,
        DECODE(gle.tr_type,1,nvl(r.amount,gle.amount),0) AS debitamount,
        DECODE(gle.tr_type,-1,nvl(r.amount,gle.amount),0) AS creditamount,
        nvl(r.amount,gle.amount) * gle.tr_type AS amount,
        DECODE(gle.tr_type,1,nvl(r.trx_amount,gle.trx_amount),0) AS trx_debitamount,
        DECODE(gle.tr_type,-1,nvl(r.trx_amount,gle.trx_amount),0) AS trx_creditamount,
        nvl(r.trx_amount,gle.trx_amount) * gle.tr_type AS trx_amount,
        DECODE(gle.tr_type,1,gle.account#,NULL) AS debitaccount#,
        DECODE(gle.tr_type,-1,gle.account#,NULL) AS creditaccount#,
        DECODE(dec.record#,NULL,r.warehousekey,dec.whsekey) AS warehousekey,
        DECODE(dec.record#,NULL,r.itemkey,dec.itemkey) AS itemkey,
        r.subledgeritemkey,
        DECODE(dec.record#,NULL,r.quantity,dec.quantity) AS quantity,
        dec.cost AS value,
        dec.unitcost AS unitcost,
        dec.cost AS cost,
        dec.in_out,
        r.memo AS entrydescription,
        r.docid,
        r.whencreated,
        r.whenmodified,
        gle.rel1,
        gle.rel2,
        gle.rel3,
        gle.rel4,
        gle.rel5,
        gle.rel6,
        gle.rel7,
        gle.rel8,
        gle.rel9,
        gle.rel10,
        gle.rel11,
        gle.rel12,
        gle.rel13,
        gle.rel14,
        gle.rel15,
        gle.rel16,
        gle.rel17,
        gle.rel18,
        gle.rel19,
        gle.rel20,
        gle.rel21,
        gle.rel22,
        gle.rel23,
        gle.rel24,
        gle.rel25,
        gle.rel26,
        gle.rel27,
        gle.rel28,
        gle.rel29,
        gle.rel30,
        gle.rel31,
        gle.rel32,
        gle.rel33,
        gle.rel34,
        gle.rel35,
        gle.rel36,
        gle.rel37,
        gle.rel38,
        gle.rel39,
        gle.rel40,
        gle.rel41,
        gle.rel42,
        gle.rel43,
        gle.rel44,
        gle.rel45,
        gle.rel46,
        gle.rel47,
        gle.rel48,
        gle.rel49,
        gle.rel50,
        gle.rel51,
        gle.rel52,
        gle.rel53,
        gle.rel54,
        gle.rel55,
        gle.rel56,
        gle.rel57,
        gle.rel58,
        gle.rel59,
        gle.rel60,
        gle.rel61,
        gle.rel62,
        gle.rel63,
        gle.rel64,
        gle.rel65,
        gle.rel66,
        gle.rel67,
        gle.rel68,
        gle.rel69,
        gle.rel70,
        gle.rel71,
        gle.rel72,
        gle.rel73,
        gle.rel74,
        gle.rel75,
        gle.rel76,
        gle.rel77,
        gle.rel78,
        gle.rel79,
        gle.rel80,
        gle.rel81,
        gle.rel82,
        gle.rel83,
        gle.rel84,
        gle.rel85,
        gle.rel86,
        gle.rel87,
        gle.rel88,
        gle.rel89,
        gle.rel90,
        gle.rel91,
        gle.rel92,
        gle.rel93,
        gle.rel94,
        gle.rel95,
        gle.rel96,
        gle.rel97,
        gle.rel98,
        gle.rel99,
        gle.rel100,
        gle.uddrel1,
        gle.uddrel2,
        gle.uddrel3,
        gle.uddrel4,
        gle.uddrel5,
        gle.uddrel6,
        gle.uddrel7,
        gle.uddrel8,
        gle.uddrel9,
        gle.uddrel10,
        gle.uddrel11,
        gle.uddrel12,
        gle.uddrel13,
        gle.uddrel14,
        gle.uddrel15,
        gle.uddrel16,
        gle.uddrel17,
        gle.uddrel18,
        gle.uddrel19,
        gle.uddrel20
    FROM
        glentry gle,
        glbatch glb,
        bookjournals bj,
        (
            SELECT
                a.cny#,
                a.record#,
                a.docentrykey,
                a.cogsposting,
                NULL AS subledgeritemkey,
                a.currency,
                a.amount,
                a.trx_amount,
                NULL AS description,
                de.quantity,
                de.warehousekey,
                de.itemkey,
                de.memo,
                de.dochdrkey,
                dh.docid,
                dh.whencreated,
                dh.whenmodified
            FROM
                (
                    SELECT
                        degl.cny#,
                        degl.glentrykey record#,
                        degl.docentrykey,
                        degl.cogsposting,
                        degl.currency,
                        SUM(degl.amount) AS amount,
                        SUM(degl.trx_amount) AS trx_amount
                    FROM
                        deglresolve degl
                    GROUP BY
                        degl.cny#,
                        degl.glentrykey,
                        degl.docentrykey,
                        degl.cogsposting,
                        degl.currency
                ) a,
                docentry de,
                dochdr dh
            WHERE
                a.cny# = de.cny#
                AND   a.docentrykey = de.record#
                AND   de.cny# = dh.cny#
                AND   de.dochdrkey = dh.record#
            UNION ALL
            SELECT
                prgl.cny#,
                prgl.glentrykey record#,
                depr.docentrykey,
                'F' AS cogsposting,
                depr.itemid AS subledgeritemkey,
                prgl.currency,
                prgl.amount,
                prgl.trx_amount,
                prgl.description,
                de.quantity,
                de.warehousekey,
                de.itemkey,
                de.memo,
                de.dochdrkey,
                dh.docid,
                dh.whencreated,
                dh.whenmodified
            FROM
                prglentryresolve prgl,
                deprentryresolve depr,
                docentry de,
                dochdr dh
            WHERE
                prgl.cny# = depr.cny#
                AND   prgl.prentrykey = depr.prentrykey
                AND   depr.cny# = de.cny#
                AND   depr.docentrykey = de.record#
                AND   de.cny# = dh.cny#
                AND   de.dochdrkey = dh.record#
        ) r,
        docentrycost dec
    WHERE
        gle.cny# = glb.cny#
        AND   gle.batch# = glb.record#
        AND   bj.cny# = glb.cny#
        AND   bj.journalkey = glb.journal#
        AND   gle.cny# = r.cny# (+)
        AND   gle.record# = r.record# (+)
        AND   r.cny# = dec.cny# (+)
        AND   r.docentrykey = dec.docentrykey (+);
