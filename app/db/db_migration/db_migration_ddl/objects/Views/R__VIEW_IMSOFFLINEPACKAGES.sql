--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_IMSOFFLINEPACKAGES.sql

CREATE OR REPLACE FORCE VIEW imsofflinepackages (dbid,cny#,packageid,queuetype,queuepriority,highresourceusage,qguid,sandbox,timestamp_date,exec_req_time,"CONTEXT",loginid,topic,recipient,"TYPE",package_details,package_action) AS
SELECT
  s.databaseid as dbid,
  p.cny#,
  p.packageid,
  q.queuetype,
  q.priority as queuepriority,
  q.highresourceusage,
  q.permanentqueueguid as qguid,
  p.sandbox,
  p.timestamp_date,
  p.exec_req_time,
  s.title as context,
  p.loginid,
  p.topic,
  p.recipient,
  p.type,
  p.package_details,
  p.package_action
FROM imspackage p,
  imsqueuetype q,
  schemamap s
WHERE p.cny#   = s.cny#
      AND p.imsqueuekey = q.permanentqueueguid
      AND p.state       = 'Q'
      AND p.sandbox IS NOT NULL;