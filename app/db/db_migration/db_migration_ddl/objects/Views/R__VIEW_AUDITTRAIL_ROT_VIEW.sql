--OWNER_CONTACT:<EMAIL>

-- Create a view for the audit trail rotating tables
CREATE OR REPLACE FORCE VIEW AUDITTRAIL_ROT_VIEW (CNY#,RECORDID,OBJECTTYP<PERSON>,<PERSON>BJECTKEY,OBJECT<PERSON>SC,USERID,<PERSON><PERSON><PERSON><PERSON><PERSON>,ACC<PERSON><PERSON>ODE,IPA<PERSON>DRESS,SOURCE,<PERSON><PERSON>J_BLOB,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,VERSION,AT_TABLE) AS
SELECT CNY#,RECORDID,OBJECTTYPE,OBJECTKEY,OBJECTDESC,USERID,
ACCESSTIME,ACCESSMODE,IPADDRESS,SOURCE,OB<PERSON>_BLOB,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>AT<PERSON>,VERSION,'AT_ROT_01' FROM AT_ROT_01
UNION ALL
SELECT CNY#,RECORDID,OBJECTTYP<PERSON>,OBJECTKEY,OBJECTDESC,USERID,
<PERSON><PERSON>ST<PERSON><PERSON>,<PERSON><PERSON><PERSON>OD<PERSON>,<PERSON><PERSON>DR<PERSON>S,SOURCE,<PERSON><PERSON><PERSON>_<PERSON><PERSON>O<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,'AT_ROT_02' FROM AT_ROT_02;
