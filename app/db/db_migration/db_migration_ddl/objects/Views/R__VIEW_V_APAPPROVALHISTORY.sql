--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_APAPPROVALHISTORY.sql

CREATE OR REPLACE FORCE VIEW v_apapprovalhistory (cny#,record#,prrecordkey,approval_stage,approval_type,approval_level,approver_number,dimension_used,approverkey,approver_title,approvedbykey,eventdate,comments,"STATE",createdbykey,creationdate,valueapproval_currency,valueapproval_exchrate_date,valueapproval_exchrate_type_id,valueapproval_exchange_rate,valueapproval_amount,createdbyemployeeid,approverusergroupkey,deptkey) AS
(SELECT apphist.cny#,
    apphist.record#,
    apphist.prrecordkey,
    apphist.approval_stage,
    apphist.approval_type ,
    apphist.approval_level,
    apphist.approver_number,
    apphist.dimension_used,
    apphist.approverkey ,
    apphist.approver_title ,
    apphist.approvedbykey ,
    apphist.eventdate ,
    apphist.comments ,
    apphist.state ,
    apphist.createdbykey ,
    apphist.creationdate ,
    apphist.valueapproval_currency ,
    apphist.valueapproval_exchrate_date ,
    apphist.valueapproval_exchrate_type_id ,
    apphist.valueapproval_exchange_rate ,
    apphist.valueapproval_amount ,
    userempinfo.employeeid AS createdbyemployeeid,
    apphist.approverusergroupkey,
    apphist.deptkey
  FROM approvalhistory apphist,
    (SELECT userinfo.cny# ,
      userinfo.record# ,
      employee.employeeid
    FROM userinfo userinfo,
      employee employee
    WHERE employee.cny#(+)     = userinfo.cny#
    AND employee.contactkey(+) = userinfo.contactkey
    ) userempinfo
  WHERE apphist.cny#       = userempinfo.cny#
  AND apphist.createdbykey = userempinfo.record#
  AND apphist.prrecordkey IS NOT NULL
  );