--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_CNSACCTBAL.sql

CREATE OR REPLACE FORCE VIEW cnsacctbal (cny#,record#,accountkey,subsidiarykey,subaccountkey,subaccountno,subtitle,cnsperiodkey,consbsrrate,conswarrate,subaccttype,journal#,symbol,batch_title,batch_date,account#,acct_no,entry_date,timeperiod,debit,credit,period#,pname,subsidiaryid,formula_debit,formula_credit) AS
(
SELECT  a.CNY#,a.RECORD#,a.ACCOUNTKEY,a.SUBSIDIARYKEY,a.SUBACCOUNTKEY,a.SUBACCOUNTNO,a.SUBTITLE,
        a.CNSPERIODKEY,
    nvl(a.acctbsrrate, nvl(cp.perbsrrate, 1)) as CONSBSRRATE,
    nvl(a.acctwarrate, nvl(cp.perwarrate, 1)) as CONSWARRATE,
        decode(a.SUBACCTTYPE,'N', 'Balance Sheet', 'Income Statement') subaccttype,
        b.journal#, bj.symbol, b.batch_title, b.batch_date,
        c.account#, ba.acct_no, c.entry_date, c.timeperiod,
        decode(c.tr_type, '1', c.amount, 0) as debit,
        decode(c.tr_type, '-1', c.amount, 0) as credit,
        d.record# period#, d.name pname, e.subsidiaryid,
    decode(c.tr_type, '1', round(decode(ba.account_type, 'N', ((c.amount)/nvl(a.acctbsrrate, nvl(cp.perbsrrate, 1))), ((c.amount)/nvl(a.acctwarrate, nvl(cp.perwarrate, 1))) ), 2), 0) formula_debit,
    decode(c.tr_type, '-1', round(decode(ba.account_type, 'N', ((c.amount)/nvl(a.acctbsrrate, nvl(cp.perbsrrate, 1))), ((c.amount)/nvl(a.acctwarrate, nvl(cp.perwarrate, 1))) ), 2), 0) formula_credit
FROM cnsaccount A, glbatch B, glentry C, glbudgettype d, subsidiary e, baseaccount ba, cnsperiod cp, basejournal bj
WHERE
            a.cny# = b.cny#
            and a.subsidiarykey = b.childentity
            --
            and b.cny# = c.cny#
            and b.record# = c.batch#
            --
            and bj.cny# = b.cny#
            and bj.record# = b.journal#
            --
            and cp.cny# = a.cny#
            and cp.record# = a.cnsperiodkey
            and cp.subsidiarykey = a.subsidiarykey
            --
            and d.cny# = cp.cny#
            and d.record# = cp.periodkey
            --
            and c.cny# = a.cny#
            and c.account# = a.accountkey
            and b.batch_date = d.end_date
            --
            and ba.cny# = c.cny#
            and ba.record# = c.account#
            --
            and e.cny# = a.cny#
            and e.record# = a.subsidiarykey
UNION
SELECT  e.CNY#,ba.record# as record#,ba.record# as accountkey, e.record# as SUBSIDIARYKEY,0,ba.acct_no as SUBACCOUNTNO,ba.title as SUBTITLE,
        cp.record# as CNSPERIODKEY,
        1 as CONSBSRRATE,
        1 as CONSWARRATE,
        decode(ba.account_type,'N', 'Balance Sheet', 'Income Statement') subaccttype,
        b.journal#, bj.symbol, b.batch_title, b.batch_date,
        c.account#, ba.acct_no, c.entry_date, c.timeperiod,
        decode(c.tr_type, '1', c.amount, 0) as debit,
        decode(c.tr_type, '-1', c.amount, 0) as credit,
        d.record# period#, d.name pname, e.subsidiaryid,
        decode(c.tr_type, '1', c.amount,0) formula_debit,
        decode(c.tr_type, '-1', c.amount,0) formula_credit
FROM  glbatch B, glentry C, glbudgettype d, subsidiary e, baseaccount ba, cnsperiod cp, basejournal bj
WHERE
            b.cny# = c.cny#
            and b.record# = c.batch#
            --
            and bj.cny# = b.cny#
            and bj.record# = b.journal#
            --
            and cp.cny# = e.cny#
            and cp.subsidiarykey = e.record#
            --
            and d.cny# = cp.cny#
            and d.record# = cp.periodkey
            --
            and c.cny# = e.cny#
            and (c.account# = e.ctaacctkey or c.account# = e.ctaincacctkey)
            and b.batch_date = d.end_date
            --
            and ba.cny# = c.cny#
            and ba.record# = c.account#
            --
            and e.cny# = B.cny#
            and e.record# = B.childentity
)
order by SUBACCOUNTKEY asc
 
 ;