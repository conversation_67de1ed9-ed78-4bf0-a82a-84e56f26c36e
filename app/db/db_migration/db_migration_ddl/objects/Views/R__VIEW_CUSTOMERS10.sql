-- OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE FORCE VIEW customers10 (cny#, "NAME", title, contactemail, created) AS
SELECT a.cny#, a.name, a.title, a.contactemail, a.created
FROM v_customer a, company2 b
WHERE a.cny# = b.cny#
  AND TRUNC(b.duedate) >= TRUNC(sysdate) - 10
  AND TRUNC(b.duedate) < TRUNC(sysdate) + 90
UNION
SELECT record# cny#, name, title, contactemail, created
FROM company
WHERE type IN ('production', 'image')
/
