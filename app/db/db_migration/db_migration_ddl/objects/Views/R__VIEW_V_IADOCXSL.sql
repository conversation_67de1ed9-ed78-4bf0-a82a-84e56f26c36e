--OWNER_CONTACT:<EMAIL>,<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_IADOCXSL.sql

CREATE OR <PERSON><PERSON>LACE FORCE VIEW v_iadocxsl (cny#,record#,moduleid,doctype,description,xsl,groupby,userkey,templatetype,descriptionext,isdefault,params,locale,whenmodified,status,encryptpdf,hascustomfields) AS
(
SELECT
	CNY#,
	RECORD#,
	MODULEID,
	DOCTYPE,
	DESCRIPTION,
	XSL,
	GROUPBY,
	USERKEY,
	TEMPLATETYPE,
	DESCRIPTIONEXT,
	ISDEFAULT,
	PARAMS,
	LOCAL<PERSON>,
	WHENMODIFIED,
    STATUS,
    ENCRYP<PERSON>DF,
    HASCUSTOMFIELDS
FROM DOCXSL
UNION ALL
SELECT
	0 AS CNY#,
	RECORD#,
	<PERSON>OD<PERSON><PERSON><PERSON>,
	DOCTY<PERSON><PERSON>,
	DESCRIPTION,
	X<PERSON>,
	'' AS GROUPBY,
	0  AS USERKEY,
	TEM<PERSON>AT<PERSON>YP<PERSON> ,
	<PERSON><PERSON><PERSON>PTIONEXT,
	'F' AS ISDEFAULT,
	PARAMS,
	LOCAL<PERSON>,
	WHENMODIFIED,
    STATUS,
    'F' AS ENCRYPTPDF,
    'F' AS HASCUSTOMFIELDS
FROM IADOCXSL
);