--OWNER_CONTACT:<EMAIL>,<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_FINANCIALENTITY.sql

CREATE OR REPLACE FORCE VIEW v_financialentity (cny#,pickid,"ID",acctno,"NAME","TYPE",glaccountkey,glaccountno,disableiet,reconmode,reconparams,location#,locationno,vendorid,vendorkey,currency,exp_year,exp_month,status, FINANCIALACCTKEY) AS
select
        f.cny# as CNY#,
        f.entity || '--' || b.name as PICKID,
        f.entity as ID,
        b.accountno as ACCTNO,
        b.name as NAME,
        lower(b.type) as TYPE,
        g.record# as GLACCOUNTKEY,
		    g.acct_no as GLACCOUNTNO,
        f.disableiet,
        f.reconmode,
        f.reconparams,
        b.location#,
        l.location_no,
        NULL,
        NULL,
        b.currency,
        NULL,
        NULL,
        b.status,
        f.record#
    from financialaccount f, bankaccount b, glaccount g, location l
    where f.type = 'ba'
    and f.entity = b.accountid
    and f.cny# = b.cny#
    and b.cny# = g.cny# (+)
    and b.cny# = l.cny# (+)
    and b.glaccountkey = g.record# (+)
    and b.location# = l.record# (+)
    union
    select
        f.cny# as CNY#,
        f.entity as PICKID,
        f.entity as ID,
        c.cardnum as ACCTNO,
        NULL as NAME,
        lower(c.liabilitytype) as TYPE,
        g.record# as GLACCOUNTKEY,
		    g.acct_no as GLACCOUNTNO,
        f.disableiet,
        f.reconmode,
        f.reconparams,
        c.location#,
        l.location_no,
        c.vendorid,
        v.record#,
        NULL as CURRENCY,
        c.exp_year,
        c.exp_month,
        c.status,
        f.record#
    from financialaccount f, creditcard c, glaccount g, location l, vendor v
    where f.type = 'cc'
    and f.entity = c.cardid
    and f.cny# = c.cny#
    and c.cny# = g.cny# (+)
    and c.cny# = l.cny# (+)
    and c.cny# = v.cny# (+)
    and c.liabacctkey = g.record# (+)
    and c.location# = l.record# (+)
    and c.vendorid = v.vendorid (+);