--OWNER_CONTACT:jana<PERSON>ana<PERSON>.r<PERSON><PERSON><PERSON>@sage.com

CREATE OR REPLACE VIEW V_REPORTINGACHEADER_RESTRICTED AS
WITH CTE_REPORTINGACHEADER AS (SELECT *
                       FROM (SELECT CNY#, R<PERSON>ORTINGACHEADERKEY, ISALLOWED, <PERSON><PERSON><PERSON><PERSON><PERSON>EY, USERTYPE
                             FROM (SELECT CNY#,
                                          REPORTINGACHEADERKEY,
                                          ISALLOWED,
                                          CASE
                                              WHEN USERTYPE = 'U' THEN USERINFOKEY
                                              ELSE CAST(SYS_CONTEXT('TMCtx', 'USERKEY') as NUMBER) END AS USERINFOKEY,
                                          USERTYPE
                                   FROM REPORTINGACHEADERPERMISSIONS
                                   WHERE ((USERTYPE = 'U' AND USERINFOKEY = SYS_CONTEXT('TMCtx', 'USERKEY'))
                                       OR (USERTYPE = 'E'))
                                     AND CNY# = SYS_CONTEXT('TMCtx', 'CNYN<PERSON>')
                                   UNION
                                   SELECT F.CNY#, <PERSON><PERSON>REPORTINGACHEADERKEY, F.ISALLOWED, G.RH AS USERINFOKEY, F.USERTYPE
                                   FROM REPORTINGACHEADERPERMISSIONS F,
                                        V_UGROUPMEMBER G
                                   WHERE G.CNY# = F.CNY#
                                     AND F.UGROUPKEY = G.PARENTGROUP
                                     AND G.TYPE = 'U'
                                     AND F.USERTYPE = 'G'
                                     AND F.CNY# = SYS_CONTEXT('TMCtx', 'CNYNO')
                                     AND G.RH = SYS_CONTEXT('TMCtx', 'USERKEY'))
                             GROUP BY CNY#, REPORTINGACHEADERKEY, ISALLOWED, USERINFOKEY, USERTYPE)
                       -- THIS ORDER BY IS NECESSARY TO MAINTAIN THE OLD BEHAVIOUR
                       ORDER BY REPORTINGACHEADERKEY, USERTYPE DESC, ISALLOWED DESC),
     CTE_ISADMIN AS (SELECT 1
                     FROM USERINFO U
                     WHERE U.CNY# = SYS_CONTEXT('TMCtx', 'CNYNO')
                       AND (U.ADMIN = '2')
                       AND U.RECORD# = SYS_CONTEXT('TMCtx', 'USERKEY'))
SELECT *
FROM (SELECT R.*,
             (CASE
                 -- REPORT OWNER AND FULL ADMIN IS ALLOWED BY DEFAULT
                  WHEN R.USERKEY = SYS_CONTEXT('TMCtx', 'USERKEY') OR EXISTS(SELECT 1 FROM CTE_ISADMIN) THEN 'T'
                  ELSE
                      NVL((SELECT C.ISALLOWED
                           FROM CTE_REPORTINGACHEADER C
                           WHERE C.REPORTINGACHEADERKEY = R.RECORD#
                             AND C.CNY# = R.CNY#
                             AND R.CNY# = SYS_CONTEXT('TMCtx', 'CNYNO')
                               FETCH FIRST ROW ONLY), 'F') END) AS ISALLOWED
      FROM REPORTINGACHEADER R
      WHERE CNY# = SYS_CONTEXT('TMCtx', 'CNYNO'))
WHERE ISALLOWED = 'T';