--OWNER_CONTACT:<EMAIL>

-- comment used to reapply view
CREATE OR REPLACE FORCE EDITIONABLE VIEW V_SENDGRIDMODULEEMAILDELIVERYLOG (CNY#, RECOR<PERSON>#, SEND<PERSON><PERSON>#, <PERSON>AT<PERSON>, SE<PERSON>ER, EMAILTO, S<PERSON><PERSON>JEC<PERSON>, WH<PERSON>SE<PERSON>, WH<PERSON>UPDATED, <PERSON><PERSON><PERSON><PERSON><PERSON>, EMAIL<PERSON><PERSON>LATEKEY, ENTITYNAME, CA<PERSON><PERSON>ORYTYP<PERSON>, CATEGORY, EMAILTEMPLATE, REFERENCENO, DRILLDOWNKEY, ENTITYID, DELIVERYLOGKEY, C<PERSON>TOMERKEY, VENDORKEY, LOCATIONKEY, DOCUMENTDATE, RESULT, RESU<PERSON><PERSON><PERSON><PERSON>, CUSTOMERID, VENDORID) AS
  (
    SELECT
        l.CNY#,
        l.RECORD#,
        l.SENDGRID#,
        l.STATUS,
        l.SENDER,
        l.<PERSON>,
        l.<PERSON>,
        l.WHENSENT,
        l.W<PERSON>DATED,
        l.<PERSON>LE<PERSON>,
        l.<PERSON>MAILTEMPLATEKEY,
        CASE WHEN l.CUSTOMERKEY IS NOT NULL THEN c.NAME WHEN l.VENDORKEY IS NOT NULL THEN v.NAME ELSE NULL END,
        CASE WHEN l.PRRECORDKEY IS NOT NULL THEN p.RECORDTYPE WHEN l.DOCHDRKEY IS NOT NULL
         THEN dp.SALE_PUR_TRANS ELSE NULL END,
        CASE WHEN l.PRRECORDKEY IS NOT NULL THEN DECODE(p.RECORDTYPE, 'ri', 'Invoice', 'ra', 'Adjustment',
         'rd', 'Discount', 'rp', 'Receipt', 'rr', 'Advance', NULL)
            WHEN l.DOCHDRKEY IS NOT NULL THEN dp.DOCID ELSE NULL END,
        et.EMAILTEMPLATENAME,
        CASE WHEN l.PRRECORDKEY IS NOT NULL THEN p.RECORDID WHEN l.DOCHDRKEY IS NOT NULL THEN d.DOCNO ELSE NULL END,
        CASE WHEN l.PRRECORDKEY IS NOT NULL THEN TO_CHAR(p.RECORD#) WHEN l.DOCHDRKEY IS NOT NULL
         THEN d.DOCID ELSE NULL END,
        CASE WHEN l.CUSTOMERKEY IS NOT NULL THEN c.CUSTOMERID WHEN l.VENDORKEY IS NOT NULL THEN v.VENDORID ELSE NULL END,
        l.DELIVERYLOGKEY,
        l.CUSTOMERKEY,
        l.VENDORKEY,
        l.LOCATIONKEY,
        d.WHENCREATED,
        l.RESULT,
        l.RESULTDETAIL,
        c.CUSTOMERID,
        v.VENDORID
    FROM
        SENDGRIDEMAILDELIVERYLOG l, PRRECORD p, DOCHDR d, EMAILTEMPLATE et, CUSTOMER c, VENDOR v, DOCPAR dp
    WHERE
        l.CNY# = p.CNY#(+) AND l.CNY# = d.CNY#(+) AND l.CNY# = et.CNY#(+) AND l.CNY# = c.CNY#(+) AND
         l.CNY# = v.CNY#(+) AND d.CNY# = dp.CNY#(+) AND
        l.PRRECORDKEY = p.RECORD#(+) AND l.DOCHDRKEY = d.RECORD#(+) AND l.EMAILTEMPLATEKEY = et.RECORD#(+) AND
         l.CUSTOMERKEY = c.RECORD#(+) AND l.VENDORKEY = v.RECORD#(+) AND
        d.DOCPARKEY = dp.RECORD#(+)
);
-- comment used to reapply view
