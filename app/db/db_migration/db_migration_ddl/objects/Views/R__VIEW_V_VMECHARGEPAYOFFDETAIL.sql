--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_VMECHARGEPAYOFFDETAIL.sql

CREATE OR REPLACE FORCE VIEW V_VMECHARGEPAYOFFDETAIL AS
SELECT A.*
FROM V_CHARGEPAYOFFDETAIL A
WHERE A.CNY# = sys_context('TMCtx', 'CNY#')
  AND
    (
        --restrict user to view record only from the respective context
        CASE
            WHEN sys_context('TMCtx', 'LOCATIONKEY') IS NULL
                AND SUBSTR(sys_context('TMCtx', 'LOCMAPABOVE', 4000), NVL(A.LOCATIONKEY, 0)+1, 1) = 'T'
                THEN 'T'
            WHEN sys_context('TMCtx', 'LOCATIONKEY') IS NOT NULL
                AND SUBSTR(sys_context('TMCtx', 'LOCMAP<PERSON>LOW', 4000), NVL(<PERSON><PERSON>L<PERSON>KEY, 0)+1, 1) = 'T'
                THEN 'T'
            ELSE 'F'
            END) = 'T';
