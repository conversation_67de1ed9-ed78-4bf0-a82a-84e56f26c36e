--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_CONTRACTBILLINGSCHEDULE.sql

CREATE OR REPLACE FORCE VIEW contractbillingschedule (cny#,record#,"TYPE",contractkey,contractdetailkey,contractexpensedetailkey,"STATE",whencreated,whenmodified,createdby,modifiedby) AS
select CNY#,RECORD#,TYPE,CONTRACTKEY,CONTRACTDETAILKEY,CONTRACTEXPENSEDETAILKEY,STATE,WHENCREATED, WHENMODIFIED, CREATEDBY, MODIFIEDBY from CONTRACTSCHEDULE where type = 'B';