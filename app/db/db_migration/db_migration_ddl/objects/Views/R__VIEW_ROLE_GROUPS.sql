--OWNER_CONTACT:<EMAIL>,<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_ROLE_GROUPS.sql

CREATE OR REPLACE FORCE VIEW role_groups (cny#,record#,groupkey,rolekey,whencreated,whenmodified,createdby,modifiedby,rel1,rel2,rel3,rel4,rel5,rel6,rel7,rel8,rel9,rel10,rel11,rel12,rel13,rel14,rel15,rel16,rel17,rel18,rel19,rel20,rel21,rel22,rel23,rel24,rel25,rel26,rel27,rel28,rel29,rel30,rel31,rel32,rel33,rel34,rel35,rel36,rel37,rel38,rel39,rel40,rel41,rel42,rel43,rel44,rel45,rel46,rel47,rel48,rel49,rel50,rel51,rel52,rel53,rel54,rel55,rel56,rel57,rel58,rel59,rel60,rel61,rel62,rel63,rel64,rel65,rel66,rel67,rel68,rel69,rel70,rel71,rel72,rel73,rel74,rel75,rel76,rel77,rel78,rel79,rel80,rel81,rel82,rel83,rel84,rel85,rel86,rel87,rel88,rel89,rel90,rel91,rel92,rel93,rel94,rel95,rel96,rel97,rel98,rel99,rel100) AS
select distinct r.cny#, r.record#, gh.descendant, r.rolekey,
      coalesce(LEAST(r.WHENCREATED,gh.WHENCREATED), coalesce(r.WHENCREATED,gh.WHENCREATED)),
      coalesce(GREATEST(r.WHENMODIFIED,gh.WHENMODIFIED), coalesce(r.WHENMODIFIED,gh.WHENMODIFIED)),
      coalesce(LEAST(r.CREATEDBY,gh.CREATEDBY), coalesce(r.CREATEDBY,gh.CREATEDBY)),
      coalesce(GREATEST(r.MODIFIEDBY,gh.MODIFIEDBY), coalesce(r.MODIFIEDBY,gh.MODIFIEDBY)),
      r.rel1,
      r.rel2,
      r.rel3,
      r.rel4,
      r.rel5,
      r.rel6,
      r.rel7,
      r.rel8,
      r.rel9,
      r.rel10,
      r.rel11,
      r.rel12,
      r.rel13,
      r.rel14,
      r.rel15,
      r.rel16,
      r.rel17,
      r.rel18,
      r.rel19,
      r.rel20,
      r.rel21,
      r.rel22,
      r.rel23,
      r.rel24,
      r.rel25,
      r.rel26,
      r.rel27,
      r.rel28,
      r.rel29,
      r.rel30,
      r.rel31,
      r.rel32,
      r.rel33,
      r.rel34,
      r.rel35,
      r.rel36,
      r.rel37,
      r.rel38,
      r.rel39,
      r.rel40,
      r.rel41,
      r.rel42,
      r.rel43,
      r.rel44,
      r.rel45,
      r.rel46,
      r.rel47,
      r.rel48,
      r.rel49,
      r.rel50,
      r.rel51,
      r.rel52,
      r.rel53,
      r.rel54,
      r.rel55,
      r.rel56,
      r.rel57,
      r.rel58,
      r.rel59,
      r.rel60,
      r.rel61,
      r.rel62,
      r.rel63,
      r.rel64,
      r.rel65,
      r.rel66,
      r.rel67,
      r.rel68,
      r.rel69,
      r.rel70,
      r.rel71,
      r.rel72,
      r.rel73,
      r.rel74,
      r.rel75,
      r.rel76,
      r.rel77,
      r.rel78,
      r.rel79,
      r.rel80,
      r.rel81,
      r.rel82,
      r.rel83,
      r.rel84,
      r.rel85,
      r.rel86,
      r.rel87,
      r.rel88,
      r.rel89,
      r.rel90,
      r.rel91,
      r.rel92,
      r.rel93,
      r.rel94,
      r.rel95,
      r.rel96,
      r.rel97,
      r.rel98,
      r.rel99,
      r.rel100
  from ugroup_hierarchy gh, ugroles r
  where gh.cny# = r.cny# and r.u_o_gkey = gh.groupkey and r.type = 'G';