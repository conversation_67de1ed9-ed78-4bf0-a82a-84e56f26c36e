--OWNER_CONTACT:<EMAIL>

CREATE OR REPLACE FORCE VIEW COMPLETE_AUDITSTORAGE (CNY#, USERID, ACCESSTIME, OBJECTTYPE, OBJECTKEY,
 ACCESSMODE, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPADDRES<PERSON>, "<PERSON>OUR<PERSON>", BLO<PERSON>, OPERATION, ID, NOTES, RECORD_URL,
 FIELDNAME, <PERSON>IELDTYP<PERSON>, <PERSON><PERSON>STRVA<PERSON>, NE<PERSON><PERSON>VAL, OLDINTVAL, NEWINTVAL, OLDNUMVAL, NEWNUMVAL,
 OLDDATEVAL, NEWDATEVAL, OLDVAL, NEWVAL,ACTION_DETAILS, RECORDNO) AS

SELECT CNY#, <PERSON>ER<PERSON>, ACCESSTIME, OBJECTTYPE, OBJECTKEY,
       ACCESSMOD<PERSON>, RA<PERSON>MO<PERSON>, WOR<PERSON><PERSON>OWACTION, IPADDRESS, "SOURCE", <PERSON><PERSON><PERSON><PERSON>, O<PERSON>ERAT<PERSON>, ID, NOTES, RECOR<PERSON>_URL,
       <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>LDSTRVAL, NEWSTR<PERSON><PERSON>, <PERSON><PERSON>INTVAL, NEWINTVAL, OLDNUMVAL, NEW<PERSON><PERSON><PERSON><PERSON>,
       OLDDATEVAL, NEWDATEVAL, OLDVAL, NEWVAL,ACTION_DETAILS, RECORDNO
FROM AUDITSTORAGE_FIELDS_AUDITKEY
UNION ALL
SELECT CNY#, USERID, ACCESSTIME, OBJECTTYPE, OBJECTKEY,
       ACCESSMODE, null, WORKFLOWACTION, IPADDRESS, "SOURCE", null, null,
       "ID", NOTES, null, null, null, null, null, null, null, null, null, null, null, null, null,
       ACTION_DETAILS, RECORDNO
FROM auditstorage_history_auditkey;