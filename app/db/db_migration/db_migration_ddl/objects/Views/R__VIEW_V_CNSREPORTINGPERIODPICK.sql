--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_CNSREPORTINGPERIODPICK.sql

CREATE OR REPLACE FORCE VIEW v_cnsreportingperiodpick (record#,cny#,"NAME",start_date,end_date) AS
SELECT DISTINCT (C.PERIODKEY) AS RECORD#, G.CNY#, G.NAME, G.START_DATE, G.END_DATE
FROM GLBUDGETTYPE G, CNSPERIOD C 
WHERE G.CNY# = C.CNY# AND G.RECORD# = C.PERIODKEY AND G.DATETYPE = 99
ORDER BY G.END_DATE ASC

-- ************************************************************************************************
-- NEW TABLES STC
-- ************************************************************************************************

 
 
 
 
 ;