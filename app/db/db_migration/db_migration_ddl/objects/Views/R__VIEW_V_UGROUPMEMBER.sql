--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_UGROUPMEMBER.sql

CREATE OR REPLACE FORCE VIEW v_ugroupmember (cny#,"NAME",descr,"TYPE",parentgroup,rh) AS
SELECT m.CNY#,
    ui.LOGINID,
    ui.description DESCR,
    m.TYPE,
    m.PARENTGROUP,
    m.U_O_GKEY
  FROM memberugroup m,
    userinfo ui
  WHERE m.TYPE   = 'U'
  AND m.CNY#     = ui.CNY#
  AND m.U_O_GKEY = ui.RECORD#
  UNION ALL
  SELECT m.CNY#,
    ug.NAME,
    ug.DESCR,
    m.TYPE,
    m.PARENTGROUP,
    m.U_O_GKEY
  FROM memberugroup m,
    ugroup ug
  WHERE m.TYPE   = 'G'
  AND m.CNY#     = ug.CNY#
  AND m.U_O_GKEY = ug.RECORD#
  UNION ALL
  SELECT CNY#,
    NAME,
    DESCR,
    'G',
    0,
    RECORD#
  FROM ugroup ug
  WHERE NOT EXISTS
    (SELECT 1
    FROM memberugroup m
    WHERE m.TYPE   = 'G'
    AND m.CNY#     = ug.CNY#
    AND m.U_O_GKEY = ug.RECORD#
    );