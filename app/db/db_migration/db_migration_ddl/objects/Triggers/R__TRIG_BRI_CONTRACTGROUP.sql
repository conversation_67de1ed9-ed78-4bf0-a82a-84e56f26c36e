--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_BRI_CONTRACTGROUP.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER BRI_CONTRACTGROUP BEFORE INSERT ON CONTRACTGROUP FOR EACH ROW
  BEGIN
    DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);
    IF acct_utils.trigger_enabled <> 'F' THEN
      :NEW.whencreated := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
      :NEW.whenmodified := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
      :NEW.createdby := SYS_CONTEXT('TMCtx', 'USERKEY');
      :NEW.modifiedby := SYS_CONTEXT('TMCtx', 'USERKEY');
    END IF;
  END BRI_CONTRACTGROUP;
/
