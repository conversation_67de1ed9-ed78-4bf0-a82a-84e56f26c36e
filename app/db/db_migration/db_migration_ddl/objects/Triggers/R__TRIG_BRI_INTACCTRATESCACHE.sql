--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_BRI_INTACCTRATESCACHE.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER BRI_INTACCTRATESCACHE BEFORE
  INSERT ON intacctratescache FOR EACH ROW BEGIN DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);
  IF acct_utils.trigger_enabled <> 'F' THEN
    IF TO_CHAR(:NEW.EXCHDATE, 'YYYY-MM-DD') > TO_CHAR(CURRENT_TIMESTAMP at TIME zone 'GMT' + INTERVAL '1' DAY, 'YYYY-MM-DD') THEN
      raise_application_error(-20001, 'Date '||TO_CHAR(:NEW.EXCHDATE, 'YYYY-MM-DD')|| ' should be less than today '|| TO_CHAR(CURRENT_TIMESTAMP at TIME zone 'GMT', 'YYYY-MM-DD'));
    END IF;
  END IF;
END BRI_INTACCTRATESCACHE;
/
