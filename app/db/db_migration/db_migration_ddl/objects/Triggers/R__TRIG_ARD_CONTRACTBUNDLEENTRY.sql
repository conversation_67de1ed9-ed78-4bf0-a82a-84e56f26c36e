--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARD_CONTRACTBUNDLEENTRY.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER ARD_CONTRACTBUNDLEENTRY AFTER DELETE ON CONTRACT<PERSON><PERSON><PERSON><PERSON>ENTRY FOR EACH ROW
  DECLARE
     var_bundleType VARCHAR2(30);
     var_entityName VARCHAR2(30);
  BEGIN
    DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);
    IF acct_utils.trigger_enabled <> 'F' THEN
      select bundle.TYPE into var_bundleType from contractbundle bundle where bundle.cny#=:old.cny# and bundle.record#=:old.BUNDLEKEY;
      var_entityName :=  GET_CONTRACTBUNDLEENTRY_TYPE(var_bundleType);
      INSERT INTO DELETE_LOG (CNY#, RECORDKEY, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, OB<PERSON>_DE<PERSON>_ID, WH<PERSON><PERSON>EATED, WHEN<PERSON>LETED, DELETEDBY) VALUES(
        :old.cny#,
        :old.record#,
        var_entityName,
        null,
        null,
        null,
        :old.whencreated,
        (CURRENT_TIMESTAMP AT TIME ZONE 'GMT'),
        SYS_CONTEXT('TMCtx', 'USERKEY')
      );
    END IF;
  END ARD_CONTRACTBUNDLEENTRY;
/
