--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_BRI_USERPROF.sql
--OWNER_CONTACT: <EMAIL>,<EMAIL>,<EMAIL>


CREATE OR REPLACE TRIGGER "BRI_USERPROF" before
  INSERT ON userprof FOR EACH row DECLARE ld_birth DATE;
  BEGIN
    dbms_application_info.read_client_info(acct_Utils.Trigger_Enabled);
    IF acct_Utils.Trigger_Enabled <> 'F' THEN
      -- Create log entry for non CS users
      IF (NVL(:new.parent,'N') != 'intacct' AND NVL(:new.oid,'N') != 'IMSOID') THEN
        BEGIN
          INSERT
          INTO stat_loginlog
            (
              cny#,
              user#,
              ip,
              sessionkey,
              locationkey
            )
            VALUES
            (
              :new.cny#,
              :new.userrec,
              :new.ip,
              :new.session#,
              :new.locationkey
            );
          EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN NULL;
          END;
            -- update company last_used to extend it's lifetime
           UPDATE company2
           SET last_used = CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
            used_for = 'user login',
            unused_date = NULL,
            blocked_date = NULL
           WHERE cny# = :new.cny#;
      END IF;
    END IF;
  END bri_userprof;
/
