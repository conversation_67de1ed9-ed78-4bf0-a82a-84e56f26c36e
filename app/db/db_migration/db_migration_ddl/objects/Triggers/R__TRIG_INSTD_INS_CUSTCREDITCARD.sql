--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_INSTD_INS_CUSTCREDITCARD.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER instd_ins_custcreditcard
INSTEAD OF INSERT ON custcreditcard
FOR EACH ROW
BEGIN

INSERT INTO
	custccdata(
		cny#         ,
		record#      ,
		description  ,
		status       ,
		cardtype     ,
		exp_month    ,
		exp_year     ,
		mailaddrkey  ,
		cardid       ,
		customerid   ,
		firstname    ,
		lastname     ,
		companyname  ,
		default_card ,
		usebilltoaddr
	)
VALUES(
		:new.cny#         ,
		:new.record#      ,
		:new.description  ,
		:new.status       ,
		:new.cardtype     ,
		:new.exp_month    ,
		:new.exp_year     ,
		:new.mailaddrkey  ,
		:new.cardid       ,
		:new.customerid   ,
		:new.firstname    ,
		:new.lastname     ,
		:new.companyname  ,
		:new.default_card ,
		:new.usebilltoaddr
);

INSERT INTO custccnum ( cny#, cckey, cardnum ) VALUES ( :new.cny#, :new.record#, :new.cardnum );

END;
/
