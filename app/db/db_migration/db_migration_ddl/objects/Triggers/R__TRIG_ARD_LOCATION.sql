--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARD_LOCATION.sql
--OWNER_CONTACT: <EMAIL>,<EMAIL>

CREATE OR REPLACE TRIGGER ard_location AFTER DELETE ON location FOR EACH ROW
BEGIN
  dbms_application_info.read_client_info(acct_utils.trigger_enabled);
  IF acct_utils.trigger_enabled <> 'F' THEN
    INSERT INTO delete_log (cny#, recordkey, object, doctype, obj_def_id, whencreated, whendeleted, deletedby)
    VALUES(:old.cny#, :old.record#, 'location', NULL, NULL, :old.whencreated, (CURRENT_TIMESTAMP AT TIME ZONE 'GMT'), sys_context('TMCtx', 'USERKEY'));

    IF :old.locationtype IN ('N', 'E') THEN
      INSERT INTO delete_log (cny#, recordkey, object, doctype, obj_def_id, whencreated, whendeleted, deletedby)
      VALUES(:old.cny#, :old.record#, 'locationentity', NULL, NULL, :old.whencreated, (CURRENT_TIMESTAMP AT TIME ZONE 'GMT'), sys_context('TMCtx', 'USERKEY'));

      -- add the billing event
      INSERT INTO ia_message_queue (message_queue_id, cny#, type, action, payload)
      SELECT message_queue_seq.nextval, :old.cny#, 'ENTITY', 'DELETE',
        json_object(
          'record' VALUE :old.record#,
          'company_record' VALUE :old.cny#
        )
      FROM company c
      WHERE c.record# = :old.cny#
        AND c.ia_billing > 0
        AND c.moved = 'F';
    END IF;
  END IF;
END ard_location;
/
