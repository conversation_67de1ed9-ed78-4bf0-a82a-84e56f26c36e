--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARU_PRBATCH.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER "ARU_PRBATCH" AFTER UPDATE OF "TITLE", "CREATED" ON "PRBATCH" FOR EACH ROW
DECLARE
       a_timeperiod      NUMBER;
BEGIN
   DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);

   IF acct_utils.trigger_enabled <> 'F'
   THEN

      IF (    NVL (:new.nogl, 'F') != 'T'
          AND (:old.title != :new.title OR :old.created != :new.created)
         )
      THEN

         -- compute timeperiod for non-standard companies
        SELECT CASE WHEN nonstandardperiods = 'T'
            THEN (SELECT ac.period FROM glaccperiod ac
                    WHERE ac.cny# = :new.cny#
                    AND :new.created >= startdate AND :new.created <= enddate
            )
            ELSE null END
          INTO a_timeperiod
        FROM acctcompany a
        WHERE a.cny# = :new.cny#;

         -- update glentry table entry_date
         UPDATE glentry
            SET entry_date = :new.created,
        timeperiod = a_timeperiod
          WHERE cny# = :new.cny#
            AND EXISTS (
                   SELECT 1
                     FROM glbatchmst
                    WHERE glbatchmst.cny# = glentry.cny#
                      AND glbatchmst.record# = glentry.batch#
                      AND glbatchmst.cny# = :new.cny#
                      AND glbatchmst.prbatchkey = :new.record#);

         -- update glbatch table batch_date and batch_title
         UPDATE glbatchmst
            SET batch_title = :new.title,
                batch_date = :new.created
          WHERE cny# = :new.cny# AND prbatchkey = :new.record#;
      END IF;
   END IF;
END aru_prbatch;
/
