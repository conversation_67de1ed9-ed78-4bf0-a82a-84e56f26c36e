--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_BRUD_CONTRACTSCHEDULE.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER BRUD_CONTRACTSCHEDULE BEFORE UPDATE OR DELETE ON CONTRACTSCHEDULE
FOR EACH ROW
DECLARE
    readtimegmt     VARCHAR2(30 CHAR);
    requestid       VARCHAR2(32 CHAR);
BEGIN
    DBMS_APPLICATION_INFO.READ_CLIENT_INFO(ACCT_UTILS.Trigger_Enabled);
    IF ACCT_UTILS.Trigger_Enabled <> 'F' THEN

        -- Get the readtime and request ID
        readtimegmt := sys_context('TMCtx', 'readtimegmt');
        requestid := sys_context('TMCtx', 'requestid');

        -- If the readtimegmt is before the LASTUPDATED it means the record was modified and we are not up to date anymore
        IF readtimegmt IS NOT NULL THEN
            IF ( (to_char(:old.LASTUPDATED,'yyyy/mm/dd HH24:MI:SSxFF') > readtimegmt) AND (:old.requestid <> requestid) ) THEN
                raise_application_error(-20001, 'Record modified by someone else.');
            END IF;
        END IF;

        -- For each update we will update the version and request ID
        IF ( UPDATING ) THEN
            :new.LASTUPDATED := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
            :new.requestid := requestid;
        END IF;

    END IF;
END BRUD_CONTRACTSCHEDULE;
/
