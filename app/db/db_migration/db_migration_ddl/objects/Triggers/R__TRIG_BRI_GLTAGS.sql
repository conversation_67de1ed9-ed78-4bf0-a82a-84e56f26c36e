--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_BRI_GLTAGS.sql
--OWNER_CONTACT: <EMAIL>,<EMAIL>

CREATE OR REPLACE TRIGGER BRI_GLTAGS BEFORE INSERT ON GLTAGS FOR EACH ROW
BEGIN
DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);
IF acct_utils.trigger_enabled <> 'F' THEN
  :NEW.createdby := SYS_CONTEXT('TMCtx', 'USERKEY');
  :NEW.modifiedby := SYS_CONTEXT('TMCtx', 'USERKEY');
END IF;
END BRI_GLTAGS;
/
