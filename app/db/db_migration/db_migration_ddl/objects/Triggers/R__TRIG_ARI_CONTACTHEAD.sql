--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARI_CONTACTHEAD.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER ari_contacthead AFTER INSERT ON contacthead FOR EACH ROW
BEGIN
  dbms_application_info.read_client_info(acct_utils.trigger_enabled);
  IF acct_utils.trigger_enabled <> 'F' THEN
    -- TODO send only needed contacts (used by users/companies)
    -- add the billing event
    INSERT INTO ia_message_queue (message_queue_id, cny#, type, action, payload)
    SELECT message_queue_seq.nextval, :new.cny#, 'CONTACT', 'INSERT',
      json_object(
        'record' VALUE :new.record#,
        'company_record' VALUE :new.cny#,
        'first_name' VALUE cv.firstname,
        'middle_name' VALUE cv.mi,
        'last_name' VALUE cv.lastname,
        'email' VALUE cv.email1
      )
    FROM contactversion cv, company c
    WHERE c.record# = :new.cny#
      AND c.ia_billing > 0
      AND c.moved = 'F'
      AND cv.cny# = :new.cny#
      AND cv.record# = :new.vrec#;
  END IF;
END ari_contacthead;
/
