--OWNER_CONTACT:<EMAIL>

-- Adding Time Module
INSERT INTO IAMODULE (IAM<PERSON><PERSON><PERSON><PERSON>, NAME, SYMB<PERSON>, COMPANY, PREINSTALLED, CONFIGURABLE, REMOVABLE, PERMKEY, APP, <PERSON>R<PERSON><PERSON><PERSON><PERSON>) VALUES ('91.TT', 'Time', 'tt', 'Intacct', 'F', 'T', 'T', 1629, 'A', 'L')
/

-- Adding Time Module policies
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Employee Groups', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Employees', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECOR<PERSON>#, MODULE, NAME, TYPE, VER<PERSON>CAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Employee Rates', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Employee Types', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Employee Aging Reports/Graphs', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'My Timesheets', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Staff Timesheets', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Time sources', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Time preferences', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Manage Timesheets', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Labor Classes', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Labor Shifts', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Labor Unions', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Time Types', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Approve Timesheets', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Timesheet Rules', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Positions and Skills', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Employee Out of Office', 'U', 'F', null)
/
INSERT INTO IAPOLICY (RECORD#, MODULE, NAME, TYPE, VERTICAL, SI_UUID) VALUES ((SELECT MAX(RECORD#) + 1 FROM IAPOLICY), 'tt', 'Employee Positions', 'U', 'F', null)
/

-- Adding Module Preferences 9.TT wherever we find 48.PROJACCT
INSERT INTO MODULEPREF (CNY#, MODULEKEY, PROPERTY, VALUE, LOCATIONKEY, WHENMODIFIED)
SELECT CNY#, '91.TT', PROPERTY, VALUE, LOCATIONKEY, WHENMODIFIED
FROM MODULEPREF WHERE MODULEKEY = '48.PROJACCT'
/

-- Adding Module 9.TT wherever we find 48.PROJACCT
INSERT INTO MODULE (cny#, MODULEID)
SELECT cny#, '91.TT'
FROM MODULE WHERE moduleid = '6.EE'
/
