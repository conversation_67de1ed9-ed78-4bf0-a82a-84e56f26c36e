-- IA-57308 Task Integration - SQL Migration Script for Production Companies

UPDATE PARTNERFI<PERSON>DMAP SET MASTER='B' WHERE PARTNER='SFORCE2' AND INTACCTOBJECT='TASK' AND  ISCUSTOMFIELDMAP='F' AND INTACCTFIELD NOT IN ('<PERSON><PERSON><PERSON>DA<PERSON>', 'ACTUALQTY','AEND<PERSON><PERSON>','APPROVEDQTY', 'PERCENTCOMPLE<PERSON>','REMAININGQTY','PROJECTKEY');
/
UPDATE PARTNERFIELDMAP SET MASTER='T' WHERE PARTNER='SFORCE2' AND INTACCTOBJECT='PROJECT' AND  ISCUSTOMFIELDMAP='F' AND INTACCTFIELD IN ('ACTUALQTY', 'APPROVEDQTY', 'PERCENTCOMPLETE', 'REMAININGQTY', 'ESTQTY');
/
UPDATE PARTNERFIELDMAP SET DERIVED='T' WHERE PARTNER='SFORCE2' AND INTACCTOBJECT='TASK' AND  ISCUSTOMFIELDMAP='F' AND INTACCTFIELD ='PROJECTKEY';
/