--liquibase formatted sql
--changeset ravi.shankarchaudhary:V2411.0.20240904.173097.001 runOnChange:false logicalFilePath:V2411.0.20240904.173097.001__customer_contract_allocation_audit_date_default.sql

-- customer_contract_allocation whencreated/whenodified update

CREATE INDEX IX_CUSTOMER_WHENCREATED ON CUSTOMER (CNY#, WHENCREATED) TABLESPACE ACCTINDX ONLINE
/

CREATE INDEX IX_CUSTOMER_WHENMODIFIED ON CUSTOMER (CNY#, WHENMODIFIED) TABLESPACE ACCTINDX ONLINE
/

CREATE INDEX IX_ALLOCATION_WHENCREATED ON ALLOCATION (CNY#, WHENCREATED) TABLESPACE ACCTINDX ONLINE
/

CREATE INDEX IX_ALLOCATION_WHENMODIFIED ON ALLOCATION (CNY#, WHENMODIFIED) TABLESPACE ACCTINDX ONLINE
/

CREATE INDEX IX_CONTRACT_WHENCREATED ON CONTRACT (CNY#, WHENCREATED) TABLESPACE ACCTINDX ONLINE
/

CREATE INDEX IX_CONTRACT_WHENMODIFIED ON CONTRACT (CNY#, WHENMODIFIED) TABLESPACE ACCTINDX ONLINE
/

BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

DECLARE
CURSOR cur IS

SELECT company.record#, company.created FROM company where exists (select 1 from customer customer where company.record# = customer.cny# and (customer.whencreated is null OR customer.whenmodified is null));

v_cny number;
v_created date;
BEGIN

open cur;
loop

fetch cur into v_cny, v_created;
   exit when cur%notfound;

    update customer set whencreated = v_created where cny# = v_cny and whencreated is null;

    update customer set whenmodified = v_created where cny# = v_cny and whenmodified is null;

    commit;

end loop;
close cur;
END;
/

DECLARE
    CURSOR cur IS

        SELECT company.record#, company.created FROM company where exists (select 1 from allocation allocation where company.record# = allocation.cny# and (allocation.whencreated is null OR allocation.whenmodified is null));

    v_cny number;
    v_created date;
BEGIN

    open cur;
    loop

        fetch cur into v_cny, v_created;
        exit when cur%notfound;

        update allocation set whencreated = v_created where cny# = v_cny and whencreated is null;

        update allocation set whenmodified = v_created where cny# = v_cny and whenmodified is null;

        commit;

    end loop;
    close cur;
END;
/

DECLARE
    CURSOR cur IS

        SELECT company.record#, company.created FROM company where exists (select 1 from contract contract where company.record# = contract.cny# and (contract.whencreated is null OR contract.whenmodified is null));

    v_cny number;
    v_created date;
BEGIN

    open cur;
    loop

        fetch cur into v_cny, v_created;
        exit when cur%notfound;

        update contract set whencreated = v_created where cny# = v_cny and whencreated is null;

        update contract set whenmodified = v_created where cny# = v_cny and whenmodified is null;

        commit;

    end loop;
    close cur;
END;
/

BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/