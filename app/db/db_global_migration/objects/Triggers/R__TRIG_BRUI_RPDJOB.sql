CREATE OR REPLACE EDITIONABLE TRIGGER BRUI_RPDJOB
before insert or update on RPDJOB for each row
  declare
    enabled char(1);
  begin
    DBMS_APPLICATION_INFO.read_client_info (enabled);
    if enabled <> 'F' then
      case
        when inserting or updating then
        :new.whenmodified := current_timestamp at time zone 'GMT';
        if :new.record# is null then
          select rpdjob_seq.NEXTVAL into :new.record# from dual;
        end if;
      end case;
    end if;
  end BRUI_RPDJOB;
/

