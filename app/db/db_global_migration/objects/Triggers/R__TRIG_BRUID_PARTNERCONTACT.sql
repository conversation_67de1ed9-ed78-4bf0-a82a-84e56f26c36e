CREATE OR REPLACE EDITIONABLE TRIGGER BRUID_PARTNERCONTACT
before insert or update or delete on IMSPARTNERCONTACT for each row
declare enabled char(1);
        rec IMSPARTNERCONTACT.RECORD#%TYPE;
begin
  DBMS_APPLICATION_INFO.read_client_info (enabled);
  if enabled <> 'F' then
    case
      when inserting or updating then
        :new.whenmodified := current_timestamp at time zone 'GMT';
        if :new.record# is null then
            select imspartnercontact_seq.NEXTVAL into rec from dual;
            SELECT nvl(min(podid), 0) * 1000000000 + rec
                INTO :new.record# FROM pod where is_current = 'Y';
        end if;
      when deleting then
        insert into IMSPARTNERCONTACTLOG(whencreated, record#)
        values(current_timestamp at time zone 'GMT', :old.record#);
    end case;
  end if;
end BRUID_PARTNERCONTACT;
/

