CREATE OR <PERSON><PERSON><PERSON><PERSON> EDITIONABLE TRIGGER BRIU_SFDC_INSTALL_LOG
BEFORE INSERT OR UPDATE ON SFDC_INSTALL_LOG
REFERENCING NEW AS new
FOR EACH ROW
  BEGIN
    IF :new.WHENCREATED IS NULL THEN
      :new.WHENCREATED := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
    END IF;
    :new.WHENMODIFIED := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
    IF :new.RECORD# IS NULL THEN
      SELECT SEQ_SFDC_INSTALL_LOG_RECNO.nextval into :new.RECORD# from dual;
    END IF;
  END BRIU_SFDC_INSTALL_LOG;
/

