-- TRIG<PERSON>R FOR SEND<PERSON>ID<PERSON>UTHENTICATION<PERSON>Y<PERSON>
CREATE OR <PERSON><PERSON><PERSON>CE EDITIONABLE TRIGGER BRUID_SENDGRIDAUTHENTICATIONSY<PERSON>
before insert or update on SEND<PERSON><PERSON><PERSON><PERSON><PERSON>ENTICATIONSYNC for each row
declare enabled char(1);
        rec SE<PERSON><PERSON><PERSON><PERSON><PERSON>HENTICATIONSYNC.RECORD#%TYPE;
begin
  DBMS_APPLICATION_INFO.read_client_info(enabled);
  if enabled <> 'F' then
    case
    when inserting then
        :new.timestamp := current_timestamp at time zone 'GMT';
        if :new.record# is null then
select get_nextrecordid('', 'SEND<PERSON><PERSON>AUTHENTICATIONSYNC') INTO :new.record# from dual;
end if;
when updating then
        if :new.record# is null then
select get_nextrecordid('', 'SENDGRIDAUTHENTICATIONSYNC') INTO :new.record# from dual;
end if;
end case;
end if;
end BRUID_SEND<PERSON>IDAUTHENTICATIONSYNC;
/

ALTER TRIGGER BRUID_SENDGRID<PERSON>UTH<PERSON><PERSON>CA<PERSON>ONSYNC ENABLE;
/