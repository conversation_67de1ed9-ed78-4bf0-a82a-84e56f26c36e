  CREATE OR REPLACE EDITIONABLE TRIGGER BRIUD_PRACTICECOMPANY
  before insert or update or delete on practicecompany for each row
  declare enabled char(1);
  begin
    DBMS_APPLICATION_INFO.read_client_info (enabled);
    if enabled <> 'F' then
      case
        when inserting or updating then begin
          :new.whenmodified := current_timestamp at time zone 'GMT';
        end;
        when deleting then
          BEGIN
            INSERT INTO practicecompanylog (whencreated, cny#) VALUES (current_timestamp at time zone 'GMT', :old.cny#);
            EXCEPTION
              WHEN DUP_VAL_ON_INDEX THEN
                UPDATE practicecompanylog SET whencreated = current_timestamp at time zone 'GMT'
                WHERE cny# = :old.cny#;
          END;
      end case;
    end if;
  end briud_practicecompany;
/

