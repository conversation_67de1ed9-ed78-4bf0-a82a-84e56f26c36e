-- delete nn_csroletool_tool_id constraint
ALTER TABLE csroletool
DROP CONSTRAINT nn_csroletool_tool_id
/

-- add foreign key on toolkey
ALTER TABLE csroletool
ADD CONSTRAINT fk_csroletool_toolkey FOREIGN KEY (toolkey) REFERENCES cstoolnames (record#) DEFERRABLE ENABLE
/

-- add not null constraint on toolkey
ALTER TABLE csroletool
ADD CONSTRAINT nn_csroletool_toolkey CHECK (toolkey IS NOT NULL) ENABLE
/

-- create index for tool_id
CREATE INDEX ix_csroletool_toolkey ON csroletool (toolkey) TABLESPACE acctglobindx
/

-- delete uq_csroletool_tool_idrolekey constraint
ALTER TABLE csroletool
DROP CONSTRAINT uq_csroletool_tool_idrolekey
/

-- add unique constraint on toolkey and rolekey
ALTER TABLE csroletool
ADD CONSTRAINT uq_csroletool_toolrolekey UNIQUE (toolkey, rolekey) USING INDEX TABLESPACE acctglobindx ENABLE
/
