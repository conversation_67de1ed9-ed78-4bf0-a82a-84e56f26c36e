-- undo add package_push_queue table to replication

DROP TABLE package_push_queue_log
/

DELETE FROM app_replicated_tables
WHERE replicated_table = 'package_push_queue'
/

merge into APP_REPLICATED_TABLES
    using
        (select null from dual)
on
    (APP_REPLICATED_TABLES.replicated_table = 'package_push_queue')
    when not matched then
insert (replicated_table, table_type, log_table)
values ('package_push_queue', 'functional', 'package_push_queue_log')
/

-- create the log table for package_push_queue
CREATE TABLE package_push_queue_log
(
    RECORD# NUMBER(15,0),
    WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    CONSTRAINT PK_PACKAGE_PUSH_QUEUE_LOG PRIMARY KEY (RECORD#) USING INDEX TABLESPACE acctglobindx ENABLE
) TABLESPACE ACCTGLOBDATA
/
