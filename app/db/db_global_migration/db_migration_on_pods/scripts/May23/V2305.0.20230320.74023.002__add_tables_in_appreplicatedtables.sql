merge into APP_REPLICATED_TABLES
    using
        (select null from dual)
    on
        (APP_REPLICATED_TABLES.replicated_table = 'dbserverthrottle')
    when not matched then
        insert (replicated_table, table_type)
        values ('dbschemathrottle', 'functional')
/

merge into APP_REPLICATED_TABLES
    using
(select null from dual)
on
    (APP_REPLICATED_TABLES.replicated_table = 'dbserverthrottle')
    when not matched then
insert (replicated_table, table_type)
values ('dbserverthrottle', 'functional')
/
