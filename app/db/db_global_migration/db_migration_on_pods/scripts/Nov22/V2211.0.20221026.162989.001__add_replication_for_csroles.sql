-- delete trigger bri_csroles
DROP TRIGGER bri_csroles
/

-- add whencreated, whenmodified, createdbyuser and modifiedbyuser in csroles, we want to replicate this table and whenmodified is required
ALTER TABLE csroles ADD (
    whencreated DATE DEFAULT CURRENT_TIMES<PERSON>MP AT TIME ZONE 'GMT',
    whenmodified DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    createdby NUMBER(15,0),
    modifiedby NUMBER(15,0),
    CONSTRAINT fk_csroles_createdby FOREIGN KEY (createdby) REFERENCES csaccl (RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT fk_csroles_modifiedby FOREIGN KEY (modifiedby) REFERENCES csaccl (RECORD#) DEFERRABLE ENABLE
)
/

-- create csroles_log table
-- this table is used for csroles table replication
CREATE TABLE csroles_log (
    record# NUMBER(15),
    whencreated DATE DEFAULT CURRENT_TIMESTAMP AT TIME ZONE 'GMT',
    CONSTRAINT pk_csroles_log PRIMARY KEY (record#) USING INDEX TABLESPACE acctglobindx ENABLE
) TABLESPACE acctglobdata
/
